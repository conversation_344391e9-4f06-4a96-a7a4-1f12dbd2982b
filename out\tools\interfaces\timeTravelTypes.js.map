{"version": 3, "file": "timeTravelTypes.js", "sourceRoot": "", "sources": ["../../../src/tools/interfaces/timeTravelTypes.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface GitCommit {\r\n  hash: string;\r\n  author: string;\r\n  date: string;\r\n  message: string;\r\n  changes: Array<{\r\n    file: string;\r\n    additions: number;\r\n    deletions: number;\r\n    type: 'added' | 'modified' | 'deleted';\r\n  }>;\r\n}\r\n\r\nexport interface CodeChange {\r\n  type: string;\r\n  description: string;\r\n  impactLevel: number;\r\n  metadata: {\r\n    commit?: string;\r\n    author?: string;\r\n    date?: string;\r\n    files?: string[];\r\n    additions?: number;\r\n    deletions?: number;\r\n    [key: string]: unknown;\r\n  };\r\n}\r\n\r\nexport interface TimelineEvent {\r\n  id: string;\r\n  type: string;\r\n  description: string;\r\n  timestamp: number;\r\n  commit?: string;\r\n  author?: string;\r\n  context?: string[];\r\n  changes?: CodeChange[];\r\n  state: Record<string, unknown>;\r\n  complexity?: CodeAnalysis;\r\n}\r\n\r\nexport interface Timeline {\r\n  events: TimelineEvent[];\r\n  metadata: {\r\n    startTime: number;\r\n    endTime: number;\r\n    totalEvents: number;\r\n  };\r\n  branches?: Array<{\r\n    name: string;\r\n    events: TimelineEvent[];\r\n  }>;\r\n}\r\n\r\nexport interface ChangePattern {\r\n  type: string;\r\n  frequency: number;\r\n  severity?: number;\r\n  confidence?: number;\r\n  magnitude?: number;\r\n  timeframe?: number;\r\n}\r\n\r\nexport interface DependencyImpact {\r\n  impactedFiles: string[];\r\n  riskLevel: 'low' | 'medium' | 'high';\r\n  metrics?: {\r\n    complexity?: number;\r\n    coverage?: number;\r\n    dependencies?: number;\r\n    [key: string]: number | undefined;\r\n  };\r\n  directDependencies: string[];\r\n  indirectDependencies: string[];\r\n  impactedAreas: string[];\r\n}\r\n\r\nexport interface CodeAnalysis {\r\n  complexity?: string;\r\n  issues: Array<{\r\n    type: string;\r\n    severity: string;\r\n    location?: string;\r\n  }>;\r\n  metrics: {\r\n    cyclomaticComplexity?: number;\r\n    cognitiveComplexity?: number;\r\n    [key: string]: number | undefined;\r\n  };\r\n  relatedChanges?: CodeChange[];\r\n  impactedAreas?: string[];\r\n  originCommit?: GitCommit;\r\n  riskAssessment?: {\r\n    severity: 'low' | 'medium' | 'high';\r\n    confidence: number;\r\n    recommendations: string[];\r\n  };\r\n}\r\n"]}