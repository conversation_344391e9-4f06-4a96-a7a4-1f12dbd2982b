"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VSCodeClipboardService = exports.PtyTerminalService = exports.VSCodeWorkspaceKnowledge = exports.VSCodeFileSystemManager = exports.VSCodeOutputChannelLogger = exports.VSCodeUIProvider = exports.LanguageModelProvider = exports.LMStudioModel = exports.OllamaModel = exports.OpenAIModel = exports.BaseLanguageModel = exports.StaticToolRegistry = exports.VSCodePermissionManager = exports.Orchestrator = exports.ToolWorkflowFactory = exports.ToolWorkflowStep = exports.BaseTool = exports.TOOL_PERMISSIONS = void 0;
exports.bootstrap = bootstrap;
const zod_1 = require("zod");
const crypto_1 = require("crypto");
const vscode = __importStar(require("vscode"));
const os = __importStar(require("os"));
const path = __importStar(require("path"));
const fs_1 = require("fs");
const node_fetch_1 = __importDefault(require("node-fetch"));
const events_1 = require("events");
// Import existing advanced functionality instead of reimplementing
const memoryManager_1 = require("../memory/memoryManager");
const workflowEngine_1 = require("../agents/workflows/workflowEngine");
const workflowManager_1 = require("../agents/workflows/workflowManager");
const workflowRegistry_1 = require("../agents/workflows/workflowRegistry");
const pseudoTerminal_1 = require("./pseudoTerminal");
const config_1 = require("../config");
/** An identifier for a resource or capability that a tool may need to access. */
exports.TOOL_PERMISSIONS = [
    'workspace:read',
    'workspace:write',
    'shell:execute', // Governs execute() and stream()
    'terminal:interactive_session', // Governs the powerful createSession()
    'network:unrestricted',
    'clipboard:read',
];
// Custom Error classes
class OperationCancelledError extends Error {
    constructor(m = 'Operation cancelled.') { super(m); this.name = 'OperationCancelledError'; }
}
class PermissionDeniedError extends Error {
    constructor(m) { super(m); this.name = 'PermissionDeniedError'; }
}
class ToolDefinitionError extends Error {
    constructor(m) { super(m); this.name = 'ToolDefinitionError'; }
}
class TerminalError extends Error {
    constructor(m) { super(m); this.name = 'TerminalError'; }
}
class WorkflowError extends Error {
    constructor(m) { super(m); this.name = 'WorkflowError'; }
}
class NotImplementedError extends Error {
    constructor(m) { super(m); this.name = 'NotImplementedError'; }
}
// #endregion
// #region PART 2: Abstract Base Class for Tools
class BaseTool {
    version = '1.0.0';
    async invoke(actionName, input, options) {
        const executionId = (0, crypto_1.randomUUID)();
        const { services, cancellationToken } = options;
        const actionDef = this.actions[actionName];
        try {
            if (!actionDef)
                throw new ToolDefinitionError(`Action '${actionName}' not found in tool '${this.name}'.`);
            const requiredPermissions = this.getPermissions(actionName);
            if (requiredPermissions.length > 0) {
                const rationale = `Tool '${this.name}' is requesting permissions to perform action '${actionName}'.`;
                const hasPermission = await services.permissionManager.requestPermissions(requiredPermissions, rationale);
                if (!hasPermission)
                    throw new PermissionDeniedError(`User denied required permissions: ${requiredPermissions.join(', ')}`);
            }
            if (cancellationToken.isCancellationRequested)
                throw new OperationCancelledError();
            const validatedInput = actionDef.inputSchema.parse(input);
            const { output, memoriesCreated } = await this._execute(actionName, validatedInput, options);
            const validatedOutput = actionDef.outputSchema.parse(output);
            return { executionId, toolName: this.name, actionName, status: 'success', output: validatedOutput, memoriesCreated };
        }
        catch (error) {
            let structuredError;
            if (error instanceof zod_1.ZodError)
                structuredError = { message: 'Data validation failed', code: 'E_VALIDATION', details: error.flatten() };
            else if (error instanceof PermissionDeniedError)
                structuredError = { message: error.message, code: 'E_PERMISSION_DENIED' };
            else if (error instanceof OperationCancelledError)
                return { executionId, toolName: this.name, actionName, status: 'cancelled', error: { message: error.message, code: 'E_CANCELLED' } };
            else if (error instanceof Error)
                structuredError = { message: error.message, stack: error.stack, code: error.code || 'E_EXECUTION_ERROR' };
            else
                structuredError = { message: 'An unknown error occurred', code: 'E_UNKNOWN' };
            services.logger.error(structuredError.message, { tool: this.name, action: actionName, error: structuredError });
            return { executionId, toolName: this.name, actionName, status: 'failure', error: structuredError };
        }
    }
    getPermissions(actionName) {
        // Log the action name for debugging and audit purposes
        console.log(`Getting permissions for action: ${actionName}`);
        return [];
    }
}
exports.BaseTool = BaseTool;
// #endregion
// #region PART 3: Advanced Workflow Integration for Tools
/**
 * Tool Workflow Step - Integrates tools into the advanced workflow system
 */
class ToolWorkflowStep {
    id;
    name;
    description;
    execute;
    nextSteps;
    isConditional;
    toolName;
    actionName;
    input;
    services;
    token;
    constructor(id, toolName, actionName, input, services, token) {
        this.id = id;
        this.name = `Tool: ${toolName}.${actionName}`;
        this.description = `Execute ${toolName}.${actionName}`;
        this.nextSteps = [];
        this.toolName = toolName;
        this.actionName = actionName;
        this.input = input;
        this.services = services;
        this.token = token;
        this.execute = async (context) => {
            const tool = this.services.toolRegistry.getTool(this.toolName);
            if (!tool) {
                const errorMsg = `Tool '${this.toolName}' not found.`;
                return { success: false, error: errorMsg };
            }
            try {
                // Hydrate input using workflow context
                const hydratedInput = this.hydrateInput(this.input, context);
                // Execute the tool
                const result = await tool.invoke(this.actionName, hydratedInput, {
                    workflowContext: context,
                    services: this.services,
                    cancellationToken: this.token
                });
                return {
                    success: result.status === 'success',
                    output: result.output,
                    error: result.error ? result.error.message : undefined
                };
            }
            catch (error) {
                const errorMsg = `Error executing tool '${this.toolName}.${this.actionName}': ${error instanceof Error ? error.message : String(error)}`;
                return { success: false, error: errorMsg };
            }
        };
    }
    hydrateInput(input, context) {
        const hydrated = {};
        for (const [key, value] of Object.entries(input)) {
            if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
                // Handle template strings like {{variableName}} or {{outputs.stepId}}
                const path = value.slice(2, -2).trim().split('.');
                const [source, ...propertyPath] = path;
                let resolvedValue;
                if (source === 'inputs') {
                    resolvedValue = context.inputs;
                }
                else if (source === 'outputs') {
                    resolvedValue = context.outputs;
                }
                else if (source === 'variables') {
                    resolvedValue = context.variables;
                }
                else {
                    // Try to find in context properties
                    resolvedValue = context[source];
                }
                if (resolvedValue === undefined) {
                    throw new WorkflowError(`Hydration failed: Source '${source}' not found in context.`);
                }
                for (const prop of propertyPath) {
                    resolvedValue = resolvedValue?.[prop];
                }
                if (resolvedValue === undefined) {
                    throw new WorkflowError(`Hydration failed: Path '${value}' could not be resolved.`);
                }
                hydrated[key] = resolvedValue;
            }
            else {
                hydrated[key] = value;
            }
        }
        return hydrated;
    }
}
exports.ToolWorkflowStep = ToolWorkflowStep;
/**
 * Tool Workflow Factory - Creates workflows that leverage tools
 */
class ToolWorkflowFactory {
    services;
    constructor(services) {
        this.services = services;
    }
    /**
     * Create a workflow from tool execution plan
     */
    createWorkflowFromPlan(plan) {
        const steps = plan.steps.map(step => new ToolWorkflowStep(step.id, step.tool_name, step.action_name, step.input, this.services, new vscode.CancellationTokenSource().token));
        const workflowDefinition = {
            id: plan.id,
            name: plan.name,
            description: `Tool-based workflow: ${plan.name}`,
            version: '1.0.0',
            steps,
            inputs: plan.inputs || [],
            outputs: [],
            startStepId: plan.steps[0]?.id || ''
        };
        return new workflowEngine_1.Workflow(workflowDefinition);
    }
    /**
     * Create a simple single-tool workflow
     */
    createSimpleToolWorkflow(toolName, actionName, input, workflowId = `tool-${toolName}-${Date.now()}`) {
        const stepId = 'execute-tool';
        const step = new ToolWorkflowStep(stepId, toolName, actionName, input, this.services, new vscode.CancellationTokenSource().token);
        const workflowDefinition = {
            id: workflowId,
            name: `Execute ${toolName}.${actionName}`,
            description: `Simple workflow to execute ${toolName}.${actionName}`,
            version: '1.0.0',
            steps: [step],
            inputs: [],
            outputs: [],
            startStepId: stepId
        };
        return new workflowEngine_1.Workflow(workflowDefinition);
    }
}
exports.ToolWorkflowFactory = ToolWorkflowFactory;
const WorkflowPlanSchema = zod_1.z.object({
    thought: zod_1.z.string().describe('The reasoning and strategy for the generated plan.'),
    steps: zod_1.z.array(zod_1.z.object({
        id: zod_1.z.string().describe('A unique camelCase identifier for this step, e.g., \'readFileStep\'.'),
        tool_name: zod_1.z.string().describe('The exact name of the tool to use.'),
        action_name: zod_1.z.string().describe('The exact name of the action to execute.'),
        input: zod_1.z.record(zod_1.z.any()).describe('Input parameters. Use \'$stepId.output\' to reference outputs from previous steps.'),
    })).describe('The sequence of tool calls to achieve the goal.'),
});
class Orchestrator {
    services;
    llm;
    workflowFactory;
    _emitter = new events_1.EventEmitter();
    constructor(services) {
        this.services = services;
        this.llm = services.llmProvider.getActiveModel();
        this.workflowFactory = new ToolWorkflowFactory(services);
    }
    on(event, listener) {
        this._emitter.on(event, listener);
    }
    async run(goal, options) {
        const { visualContext, token } = options;
        const workflowId = (0, crypto_1.randomUUID)();
        return this.services.ui.showProgress(`AI Agent: ${goal}`, async (progress) => {
            this._emitter.emit('workflowStart', { workflowId, goal });
            progress.report({ message: 'Analyzing goal and memories...' });
            // Utilize memory manager for context retrieval
            const relevantMemories = await this.services.memoryManager.searchMemories({
                query: goal,
                limit: 10
            });
            // Store search results in memory for future reference
            const memoryEntry = {
                id: (0, crypto_1.randomUUID)(),
                content: `Goal: ${goal} | Memories found: ${relevantMemories.length}`,
                timestamp: Date.now(),
                metadata: {
                    source: 'orchestrator', // Type assertion for compatibility
                    type: 'goal_analysis',
                    tags: ['goal_analysis', 'workflow_execution'],
                    goal,
                    memoryCount: relevantMemories.length,
                    workflowId
                }
            };
            // Use memoryManager instance with full MemoryManager functionality
            try {
                await memoryManager_1.memoryManager.storeMemory?.(memoryEntry);
            }
            catch (memoryError) {
                // Silently handle memory storage errors
                console.warn('Failed to store memory:', memoryError);
            }
            // Get configuration for enhanced workflow features using getConfig directly
            const config = (0, config_1.getConfig)('aiAgent', {});
            const enableAdvancedFeatures = config.enableAdvancedFeatures ?? true;
            const plan = await this.createWorkflowPlan(goal, visualContext, token);
            this.services.logger.info('Generated workflow plan', { workflowId, plan });
            // Create workflow using the factory with enhanced features
            const workflow = this.workflowFactory.createWorkflowFromPlan({
                id: workflowId,
                name: `Workflow for: ${goal}`,
                steps: plan.steps
            });
            // Set up agent for the workflow
            const agent = workflow.context?.agent;
            if (agent) {
                workflow.setAgent(agent);
            }
            // Enable advanced features if configured
            if (enableAdvancedFeatures) {
                workflow.enableAdvancedFeatures({
                    goddessMode: true,
                    quantumAnalysis: true,
                    neuralSynthesis: false,
                    timeTravelDebugging: false,
                    adaptivePersonality: true
                });
            }
            progress.report({ message: 'Executing workflow...' });
            // Execute the workflow - simplified implementation
            const finalResult = await this.executeWorkflowSimple(workflow);
            // Register workflow with registry for future reference
            workflowRegistry_1.workflowRegistry.registerWorkflow?.(workflow.getDefinition());
            this._emitter.emit('workflowEnd', {
                workflowId,
                finalContext: {
                    status: finalResult.success ? 'completed' : 'failed',
                    endTime: new Date()
                }
            });
            this.services.logger.info('Workflow finished with result:', { workflowId, finalResult });
            return finalResult;
        });
    }
    async runSimpleTool(toolName, actionName, input, token) {
        const workflow = this.workflowFactory.createSimpleToolWorkflow(toolName, actionName, input);
        return await workflow.execute?.(workflow.getContext()) || { success: false, error: 'Workflow execution not supported' };
    }
    async createWorkflowPlan(goal, visualContext, token) {
        const relevantMemories = await this.services.memoryManager.searchMemories({ query: goal, limit: 5 });
        const toolDocs = this.services.toolRegistry.getAllTools().map(t => t.getDocumentation()).join('\n---\n');
        const promptParts = [];
        const textualPrompt = `You are a world-class AI orchestrator. Your task is to create a JSON workflow plan to achieve the user's GOAL.\n\nGOAL: "${goal}"\n\nRELEVANT MEMORIES (for context):\n${relevantMemories.length > 0 ? relevantMemories.map(m => `- ${m.content}`).join('\n') : 'None'}\n\nAVAILABLE TOOLS:\n${toolDocs}\n\nINSTRUCTIONS:\n1. Think step-by-step to decompose the goal into a sequence of tool calls.\n2. Define the workflow as a JSON object strictly matching this schema: { "thought": "string", "steps": [{ "id": "string", "tool_name": "string", "action_name": "string", "input": "object" }] }\n3. To use an output from a previous step, use the syntax "$stepId.output".\n4. If images are provided, incorporate your analysis of them into the plan.\n5. Ensure the plan is logical and directly works towards solving the user's GOAL.\n\nRESPONSE (JSON object only):`;
        if (visualContext?.length) {
            promptParts.push({ type: 'text', text: 'Analyze image(s)...' });
            visualContext.forEach(image => { promptParts.push({ type: 'image', source: { type: 'base64', media_type: image.mimeType, data: image.base64Data } }); });
        }
        promptParts.push({ type: 'text', text: textualPrompt });
        const responseJson = await this.llm.generateResponse(promptParts, { cancellationToken: token, jsonOutput: true });
        return WorkflowPlanSchema.parse(JSON.parse(responseJson));
    }
    /**
     * Simplified workflow execution for when the full workflow engine is not available
     */
    async executeWorkflowSimple(workflow) {
        const definition = workflow.getDefinition();
        const context = workflow.getContext();
        // Use workflowManager for advanced workflow coordination if available
        const useAdvancedWorkflow = workflowManager_1.workflowManager?.coordinateWorkflowExecution;
        if (useAdvancedWorkflow) {
            try {
                // Leverage workflowManager for enhanced execution
                const enhancedResult = await workflowManager_1.workflowManager.coordinateWorkflowExecution(definition, context, {
                    enableParallelExecution: false,
                    enableErrorRecovery: true,
                    enableProgressTracking: true
                });
                if (enhancedResult) {
                    return {
                        success: enhancedResult.success,
                        output: enhancedResult.output,
                        error: enhancedResult.error
                    };
                }
            }
            catch (error) {
                // Fall back to simple execution if advanced features fail
                this.services.logger.warn('Advanced workflow execution failed, falling back to simple execution', { error: error instanceof Error ? error.message : String(error) });
            }
        }
        // Execute steps sequentially
        for (let i = 0; i < definition.steps.length; i++) {
            const step = definition.steps[i];
            const startTime = Date.now();
            // Record step start in history
            context.history.push({
                stepId: step.id,
                startTime: new Date(startTime),
                result: undefined
            });
            try {
                // Execute the step
                const result = await step.execute(context);
                // Record step completion
                const historyEntry = context.history[context.history.length - 1];
                historyEntry.endTime = new Date();
                historyEntry.result = result;
                // Store output in context if successful
                if (result.success && result.output !== undefined) {
                    context.outputs[step.id] = result.output;
                }
                // If step failed, return failure result
                if (!result.success) {
                    return {
                        success: false,
                        error: result.error || `Step '${step.name}' failed`
                    };
                }
                // If step specifies next step, jump to it
                if (result.nextStepId) {
                    const nextIndex = definition.steps.findIndex(s => s.id === result.nextStepId);
                    if (nextIndex !== -1) {
                        i = nextIndex - 1; // Will be incremented by loop
                    }
                }
            }
            catch (error) {
                // Record error in history
                const historyEntry = context.history[context.history.length - 1];
                historyEntry.endTime = new Date();
                historyEntry.result = {
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                };
                return {
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                };
            }
        }
        // All steps completed successfully
        return {
            success: true,
            output: context.outputs
        };
    }
}
exports.Orchestrator = Orchestrator;
// #endregion
// #region PART 4: Production Service Implementations
class VSCodePermissionManager {
    context;
    GRANTED_KEY = 'ai-agent.grantedPermissions';
    initialize(context) { this.context = context; }
    async requestPermissions(permissions, rationale) {
        if (!permissions.length)
            return true;
        const granted = new Set(this.context.globalState.get(this.GRANTED_KEY, []));
        const needed = permissions.filter(p => !granted.has(p));
        if (!needed.length)
            return true;
        const decision = await vscode.window.showWarningMessage(`${rationale} Requires: ${needed.join(', ')}. Grant?`, { modal: true }, 'Grant');
        if (decision === 'Grant') {
            needed.forEach(p => granted.add(p));
            await this.context.globalState.update(this.GRANTED_KEY, Array.from(granted));
            return true;
        }
        return false;
    }
}
exports.VSCodePermissionManager = VSCodePermissionManager;
class StaticToolRegistry {
    tools = new Map();
    async initialize(services) { }
    getTool(toolName) { return this.tools.get(toolName); }
    getAllTools() { return Array.from(this.tools.values()); }
}
exports.StaticToolRegistry = StaticToolRegistry;
class BaseLanguageModel {
    logger;
    constructor(services) { this.logger = services.logger; }
}
exports.BaseLanguageModel = BaseLanguageModel;
class OpenAIModel extends BaseLanguageModel {
    async generateResponse(prompt, options) {
        const config = vscode.workspace.getConfiguration('aiAgent.openAI');
        const apiKey = config.get('apiKey');
        const baseUrl = config.get('baseUrl', 'https://api.openai.com/v1');
        if (!apiKey)
            throw new Error('OpenAI API key is not configured.');
        const controller = new AbortController();
        const registration = options.cancellationToken.onCancellationRequested(() => controller.abort());
        // Log the request for debugging using the inherited logger
        this.logger.info('Generating response with OpenAI', { jsonOutput: options.jsonOutput, promptLength: prompt.length });
        try {
            const body = { model: config.get('model', 'gpt-4-turbo'), messages: [{ role: 'user', content: prompt }], temperature: 0.1, };
            if (options.jsonOutput)
                body.response_format = { 'type': 'json_object' };
            const response = await (0, node_fetch_1.default)(`${baseUrl}/chat/completions`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${apiKey}` }, body: JSON.stringify(body), signal: controller.signal });
            if (!response.ok) {
                const errorBody = await response.text();
                throw new Error(`API Error ${response.status}: ${errorBody}`);
            }
            const data = await response.json();
            // Log successful response using inherited logger
            this.logger.debug('OpenAI response received', { contentLength: data.choices[0]?.message?.content?.length });
            return data.choices[0]?.message?.content ?? '';
        }
        catch (error) {
            if (error instanceof Error && error.name === 'AbortError')
                throw new OperationCancelledError();
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(errorMessage, { message: 'OpenAI request failed.' });
            throw error;
        }
        finally {
            registration.dispose();
        }
    }
}
exports.OpenAIModel = OpenAIModel;
class OllamaModel extends BaseLanguageModel {
    async generateResponse(prompt, options) {
        const config = vscode.workspace.getConfiguration('aiAgent.ollama');
        const model = config.get('model');
        if (!model)
            throw new Error('Ollama model not configured.');
        this.logger.info('Using Ollama.', { baseUrl: config.get('baseUrl', 'http://localhost:11434'), model });
        throw new NotImplementedError('Ollama client not implemented.');
    }
}
exports.OllamaModel = OllamaModel;
class LMStudioModel extends BaseLanguageModel {
    async generateResponse(prompt, options) {
        const config = vscode.workspace.getConfiguration('aiAgent.lmstudio');
        this.logger.info('Using LM Studio.', { baseUrl: config.get('baseUrl', 'http://localhost:1234/v1') });
        throw new NotImplementedError('LM Studio client not implemented.');
    }
}
exports.LMStudioModel = LMStudioModel;
class LanguageModelProvider {
    activeModel;
    initialize(services) {
        const providerType = vscode.workspace.getConfiguration('aiAgent').get('provider', 'openai');
        switch (providerType.toLowerCase()) {
            case 'openai':
                this.activeModel = new OpenAIModel(services);
                break;
            case 'ollama':
                this.activeModel = new OllamaModel(services);
                break;
            case 'lmstudio':
                this.activeModel = new LMStudioModel(services);
                break;
            default: throw new Error(`Unsupported provider: '${providerType}'.`);
        }
        services.logger.info(`Active AI provider: ${providerType}`);
    }
    getActiveModel() { if (!this.activeModel)
        throw new Error('LLM Provider not initialized.'); return this.activeModel; }
}
exports.LanguageModelProvider = LanguageModelProvider;
class VSCodeUIProvider {
    async showProgress(title, work) {
        return vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title,
            cancellable: true
        }, (progress, token) => work(progress, token));
    }
    showInformationMessage(message) {
        vscode.window.showInformationMessage(message);
    }
    showWarningMessage(message) {
        vscode.window.showWarningMessage(message);
    }
    showErrorMessage(message) {
        vscode.window.showErrorMessage(message instanceof Error ? message.message : message);
    }
}
exports.VSCodeUIProvider = VSCodeUIProvider;
class VSCodeOutputChannelLogger {
    channel;
    constructor(name) { this.channel = vscode.window.createOutputChannel(name); }
    log(l, m, meta) { this.channel.appendLine(`[${l}] ${m}${meta ? ` | ${JSON.stringify(meta)}` : ''}`); }
    debug(m, meta) { this.log('DEBUG', m, meta); }
    info(m, meta) { this.log('INFO', m, meta); }
    warn(m, meta) { this.log('WARN', m, meta); }
    error(m, meta) { this.log('ERROR', m instanceof Error ? m.message : m, m instanceof Error ? { ...meta, stack: m.stack } : meta); }
}
exports.VSCodeOutputChannelLogger = VSCodeOutputChannelLogger;
class VSCodeFileSystemManager {
    workspaceRoot;
    constructor() { const root = vscode.workspace.workspaceFolders?.[0]; if (!root)
        throw new Error('No workspace folder open.'); this.workspaceRoot = root.uri.fsPath; }
    _resolveAndValidatePath(p) { const abs = path.resolve(this.workspaceRoot, p); if (!abs.startsWith(this.workspaceRoot))
        throw new Error('Path traversal denied.'); return abs; }
    async readFile(p) { return fs_1.promises.readFile(this._resolveAndValidatePath(p), 'utf-8'); }
    async writeFile(p, c) { const full = this._resolveAndValidatePath(p); await fs_1.promises.mkdir(path.dirname(full), { recursive: true }); await fs_1.promises.writeFile(full, c); }
    async deleteFile(p) { await fs_1.promises.unlink(this._resolveAndValidatePath(p)); }
    async createDirectory(p) { await fs_1.promises.mkdir(this._resolveAndValidatePath(p), { recursive: true }); }
}
exports.VSCodeFileSystemManager = VSCodeFileSystemManager;
class VSCodeWorkspaceKnowledge {
    defaultIgnore = new Set(['.git', 'node_modules', 'dist', 'build', '.vscode', '__pycache__']);
    async getDirectoryTree(options) {
        const rootFolder = vscode.workspace.workspaceFolders?.[0];
        if (!rootFolder)
            return null;
        const ignoreSet = new Set([...this.defaultIgnore, ...(options?.ignorePatterns ?? [])]);
        const buildTree = async (dirPath) => {
            const entries = await fs_1.promises.readdir(dirPath, { withFileTypes: true });
            const children = [];
            for (const entry of entries) {
                if (ignoreSet.has(entry.name))
                    continue;
                const fullPath = path.join(dirPath, entry.name);
                const relPath = path.relative(rootFolder.uri.fsPath, fullPath);
                if (entry.isDirectory()) {
                    children.push(await buildTree(fullPath));
                }
                else {
                    children.push({ path: relPath, name: entry.name, type: 'file' });
                }
            }
            return { path: path.relative(rootFolder.uri.fsPath, dirPath), name: path.basename(dirPath), children, type: 'directory' };
        };
        return buildTree(rootFolder.uri.fsPath);
    }
    async findFilesByPattern(p) { return vscode.workspace.findFiles(p); }
    async getFileContent(p) { return new VSCodeFileSystemManager().readFile(p); }
}
exports.VSCodeWorkspaceKnowledge = VSCodeWorkspaceKnowledge;
class PtyTerminalService {
    logger;
    shell;
    terminalStats;
    terminalConfig;
    currentProcess = null;
    terminalActions = null;
    currentState;
    constructor(logger) {
        this.logger = logger;
        this.shell = os.platform() === 'win32' ? 'powershell.exe' : (process.env.SHELL || 'bash');
        // Initialize currentState after shell is set
        this.currentState = {
            isConnected: false,
            isReady: false,
            currentDirectory: process.cwd(),
            shellType: this.shell,
            dimensions: { columns: 80, rows: 24 },
            lastCommand: '',
            commandHistory: [],
            isBusy: false
        };
        // Initialize terminal configuration and stats for enhanced terminal management
        this.terminalConfig = {
            shell: this.shell,
            cwd: process.cwd(),
            env: process.env,
            dimensions: { columns: 80, rows: 24 }
        };
        this.terminalStats = {
            totalCommands: 0,
            totalOutputBytes: 0,
            averageResponseTime: 0,
            uptimeSeconds: 0,
            errorCount: 0,
            resizeCount: 0
        };
        // Terminal actions will be initialized when a session is created
    }
    getWorkspaceCwd() {
        return vscode.workspace.workspaceFolders?.[0]?.uri.fsPath ?? os.homedir();
    }
    resolveCwd(cwd) {
        const workspaceRoot = this.getWorkspaceCwd();
        const resolved = cwd ? path.resolve(workspaceRoot, cwd) : workspaceRoot;
        if (!resolved.startsWith(workspaceRoot)) {
            throw new TerminalError('Terminal CWD cannot be outside the workspace.');
        }
        return resolved;
    }
    async execute(command, args, options) {
        return new Promise((resolve) => {
            let stdout = '';
            const stderr = '';
            let wasCancelled = false;
            const startTime = Date.now();
            const cwd = this.resolveCwd(options.cwd);
            const fullCommand = [command, ...args].join(' ');
            const shellCmd = os.platform() === 'win32'
                ? `cmd /c "${fullCommand}"`
                : `sh -c "${fullCommand.replace(/"/g, '\\"')}"`;
            const terminalProcess = new pseudoTerminal_1.InteractiveSession({
                name: 'Terminal Command',
                shell: this.shell,
                cwd
            });
            // Handle output
            const dataDisposable = terminalProcess.onData(data => {
                stdout += data;
            });
            // Handle cancellation
            const cancelDisposable = options.cancellationToken.onCancellationRequested(() => {
                wasCancelled = true;
                terminalProcess.dispose();
            });
            // Handle process exit
            const exitDisposable = terminalProcess.onExit(exitCode => {
                dataDisposable.dispose();
                cancelDisposable.dispose();
                exitDisposable.dispose();
                resolve({
                    command: shellCmd,
                    output: stdout,
                    exitCode: exitCode,
                    duration: Date.now() - startTime,
                    success: exitCode === 0,
                    error: wasCancelled ? 'Command was cancelled' : undefined,
                    background: false,
                    completedAt: new Date()
                });
            });
            // Start the process
            terminalProcess.initialize().then(() => {
                return terminalProcess.writeLn(shellCmd);
            }).catch(error => {
                this.logger.error('Failed to initialize or write to terminal:', error);
                resolve({
                    command: shellCmd,
                    exitCode: -1,
                    output: stdout,
                    duration: 0,
                    success: false,
                    error: 'Failed to initialize terminal',
                    background: false,
                    completedAt: new Date()
                });
            });
        });
    }
    async stream(command, args, options) {
        return new Promise((resolve) => {
            const cwd = this.resolveCwd(options.cwd);
            const fullCommand = [command, ...args].join(' ');
            const shellCmd = os.platform() === 'win32'
                ? `cmd /c "${fullCommand}"`
                : `sh -c "${fullCommand.replace(/"/g, '\\"')}"`;
            const terminalProcess = new pseudoTerminal_1.InteractiveSession({
                name: 'Terminal Stream',
                shell: this.shell,
                cwd
            });
            // Forward output to the provided callback
            const dataDisposable = terminalProcess.onData(data => {
                options.onData(data);
            });
            // Handle cancellation
            const cancelDisposable = options.cancellationToken.onCancellationRequested(() => {
                terminalProcess.dispose();
                resolve({ exitCode: -1 });
            });
            // Handle process exit
            const exitDisposable = terminalProcess.onExit(exitCode => {
                dataDisposable.dispose();
                cancelDisposable.dispose();
                exitDisposable.dispose();
                resolve({ exitCode: exitCode });
            });
            // Start the process
            terminalProcess.initialize().then(() => {
                return terminalProcess.writeLn(shellCmd);
            }).catch(error => {
                this.logger.error('Failed to initialize or write to terminal stream:', error);
                resolve({ exitCode: -1 });
            });
        });
    }
    async createSession(options) {
        try {
            const cwd = this.resolveCwd(options?.cwd);
            const session = new pseudoTerminal_1.InteractiveSession({
                name: 'Interactive Terminal Session',
                shell: this.shell,
                cwd
            });
            this.logger.info(`Created interactive terminal session ${session.id}.`);
            return session;
        }
        catch (error) {
            this.logger.error(`Failed to create interactive session: ${error instanceof Error ? error.message : String(error)}`);
            throw new TerminalError(`Failed to create interactive session: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
exports.PtyTerminalService = PtyTerminalService;
// #endregion
// #region PART 5: Main Extension Entrypoint & Service Wiring
class VSCodeClipboardService {
    async readText() {
        try {
            return await vscode.env.clipboard.readText();
        }
        catch (error) {
            throw new Error(`Failed to read clipboard text: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async readImage() {
        // VS Code doesn't provide direct clipboard image access
        // This is a placeholder for potential future implementation
        throw new NotImplementedError('Image clipboard reading not implemented for VS Code');
    }
}
exports.VSCodeClipboardService = VSCodeClipboardService;
// These classes are placeholders representing the existing services in your codebase.
// This bootstrap function correctly integrates with them as per your report.
class SettingsManager {
    async initialize(context) { }
}
class PromptManager {
    async initialize() { }
}
class KnowledgebaseManager {
    async initialize() { }
}
async function bootstrap(context) {
    // 1. Core Initialization Flow (as per your report)
    // Logger is the first and most critical service.
    const logger = new VSCodeOutputChannelLogger('AI Agent Framework');
    logger.info('Activating AI Agent...');
    try {
        // Initialize your existing services. The `try...catch` pattern with graceful
        // degradation is applied as you specified.
        const settingsManager = new SettingsManager();
        try {
            await settingsManager.initialize(context);
            logger.info('SettingsManager initialized');
        }
        catch (e) {
            const error = e instanceof Error ? e : new Error(String(e));
            logger.error(`SettingsManager Initialization failed: ${error.message}`, error);
        }
        // --- This is where the framework's services are bootstrapped ---
        // These framework services depend only on `logger`.
        const ui = new VSCodeUIProvider();
        const permissionManager = new VSCodePermissionManager();
        permissionManager.initialize(context);
        // Create memory manager with proper type casting
        const memoryManager = new memoryManager_1.MemoryManager();
        const fileSystem = new VSCodeFileSystemManager();
        const workspace = new VSCodeWorkspaceKnowledge();
        const terminalService = new PtyTerminalService(logger);
        const clipboard = new VSCodeClipboardService();
        const toolRegistry = new StaticToolRegistry();
        const llmProvider = new LanguageModelProvider();
        // Create service container with all properties at once
        const services = {
            logger,
            ui,
            permissionManager,
            toolRegistry,
            memoryManager,
            workspace,
            fileSystem,
            terminalService,
            clipboard,
            llmProvider
        };
        try {
            await memoryManager.initialize(context);
            logger.info('MemoryManager initialized');
        }
        catch (e) {
            const error = e instanceof Error ? e : new Error(String(e));
            logger.error(`MemoryManager Initialization failed: ${error.message}`, error);
            throw new Error(`Failed to initialize MemoryManager: ${error.message}`);
        }
        // Initialize services that require the full service container
        try {
            llmProvider.initialize(services);
            logger.info('LanguageModelProvider initialized');
        }
        catch (e) {
            const error = e instanceof Error ? e : new Error(String(e));
            logger.error(`LanguageModelProvider Initialization failed: ${error.message}`, error);
        }
        try {
            await toolRegistry.initialize(services);
            logger.info('ToolRegistry initialized');
            // Your concrete tools would be registered here.
        }
        catch (e) {
            const error = e instanceof Error ? e : new Error(String(e));
            logger.error(`ToolRegistry Initialization failed: ${error.message}`, error);
        }
        // Check if all critical services initialized successfully before creating the orchestrator
        if (!services.llmProvider || !services.toolRegistry || !services.memoryManager) {
            throw new Error('Critical services failed to initialize. Agent cannot start.');
        }
        const orchestrator = new Orchestrator(services);
        // Wire up orchestrator events to the logger.
        orchestrator.on('workflowStart', (args) => logger.info('Workflow started.', args));
        orchestrator.on('stepComplete', (args) => logger.info(`Workflow step '${args.stepId}' completed.`, { result: args.result }));
        orchestrator.on('workflowEnd', (args) => logger.info(`Workflow finished with status: ${args.finalContext.status}.`, { id: args.workflowId }));
        // Register the main command that triggers the agent.
        const commandDisposable = vscode.commands.registerCommand('ai-agent.run', async () => {
            const tokenSource = new vscode.CancellationTokenSource();
            try {
                const goal = await vscode.window.showInputBox({ title: 'AI Agent', prompt: 'What is your goal?' });
                if (!goal)
                    return;
                const finalResult = await orchestrator.run(goal, { token: tokenSource.token });
                if (finalResult.success) {
                    ui.showInformationMessage(`Agent task completed successfully. Final output: ${JSON.stringify(finalResult.output)}`);
                }
                else {
                    throw new WorkflowError(`Task failed. Reason: ${finalResult.error ?? 'Unknown error'}`);
                }
            }
            catch (error) {
                if (!(error instanceof OperationCancelledError)) {
                    logger.error(error, { message: 'Orchestrator failed to run goal.' });
                    ui.showErrorMessage(error);
                }
                else {
                    logger.info('Agent task was cancelled by user.');
                }
            }
        });
        logger.info('AI Agent Framework bootstrapped successfully and command is registered.');
        return commandDisposable;
    }
    catch (error) {
        const safeError = error instanceof Error ? error : new Error(String(error));
        logger.error(safeError, { message: 'Fatal error during AI Agent Framework bootstrap.' });
        // Per your report, show error after a delay.
        setTimeout(() => vscode.window.showErrorMessage(`AI Agent Framework failed to initialize: ${safeError.message}`), 1000);
        return { dispose: () => { } };
    }
}
// #endregion
//# sourceMappingURL=toolFrame.js.map