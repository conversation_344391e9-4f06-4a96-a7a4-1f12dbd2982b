import { ITool, ToolInput, ToolResult, ToolActionDefinition } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import axios from 'axios';
import { z } from 'zod';

export class WebSearchTool implements ITool {
  readonly id = 'webSearch';
  readonly name = 'Web Search (Advanced)';
  readonly description = 'Performs web search using multiple providers and advanced options.';
  readonly type = 'multi-action'; // Required by ITool
  readonly actions: Record<string, ToolActionDefinition> = {
    'duckduckgo': {
      description: 'Web search using DuckDuckGo Instant Answer API.',
      inputSchema: z.object({
        query: z.string().describe('The search query.')
      })
    },
    'multi': {
      description: 'Search multiple web engines simultaneously.',
      inputSchema: z.object({
        query: z.string().describe('The search query.'),
        engines: z.array(z.string()).optional().describe('Optional list of search engines to use.')
      })
    }
  };

  // Store the implementation of each action separately
  private async executeDuckDuckGo(input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      const query = input.query as string;
      if (!query) {
        return {
          success: false,
          error: '\'query\' is required.',
          toolId: this.id,
          actionName: 'duckduckgo'
        };
      }

      // Validate query length
      if (query.length > 1000) {
        return {
          success: false,
          error: 'Query is too long. Maximum length is 1000 characters.',
          toolId: this.id,
          actionName: 'duckduckgo'
        };
      }

      try {
        const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1`;
        const response = await axios({
          method: 'get',
          url,
          timeout: 10000, // 10 second timeout
          headers: {
            'User-Agent': 'Codessa-Extension/1.0'
          }
        });

        const data = response.data;

        // Format the results in a more structured way
        const formattedResults = {
          query,
          abstractText: data.AbstractText || '',
          abstractSource: data.AbstractSource || '',
          abstractURL: data.AbstractURL || '',
          answer: data.Answer || '',
          definition: data.Definition || '',
          definitionSource: data.DefinitionSource || '',
          heading: data.Heading || '',
          image: data.Image || '',
          relatedTopics: (data.RelatedTopics || []).slice(0, 5).map((topic: { Text?: string; FirstURL?: string }) => ({
            text: topic.Text || '',
            url: topic.FirstURL || ''
          }))
        };

        // Create a human-readable summary
        let summary = '';

        if (formattedResults.abstractText) {
          summary += `${formattedResults.abstractText}\n`;
          if (formattedResults.abstractSource) {
            summary += `Source: ${formattedResults.abstractSource}\n`;
          }
        } else if (formattedResults.answer) {
          summary += `${formattedResults.answer}\n`;
        } else if (formattedResults.definition) {
          summary += `Definition: ${formattedResults.definition}\n`;
          if (formattedResults.definitionSource) {
            summary += `Source: ${formattedResults.definitionSource}\n`;
          }
        } else if (formattedResults.relatedTopics.length > 0) {
          summary += 'Related Topics:\n';
          formattedResults.relatedTopics.forEach((topic: any, index: number) => {
            summary += `${index + 1}. ${topic.text}\n`;
          });
        } else {
          summary = 'No instant answer found.';
        }

        return {
          success: true,
          output: {
            summary,
            data: formattedResults
          },
          toolId: this.id,
          actionName: 'duckduckgo',
          metadata: {
            hasAbstract: !!formattedResults.abstractText,
            hasAnswer: !!formattedResults.answer,
            hasDefinition: !!formattedResults.definition,
            topicCount: formattedResults.relatedTopics.length
          }
        };
      } catch (error: any) {
        // Handle specific error types
        if (error.code === 'ECONNABORTED') {
          return {
            success: false,
            error: 'Search request timed out. Please try again later.',
            toolId: this.id,
            actionName: 'duckduckgo'
          };
        } else if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          return {
            success: false,
            error: `Search failed with status ${error.response.status}: ${error.message}`,
            toolId: this.id,
            actionName: 'duckduckgo'
          };
        } else if (error.request) {
          // The request was made but no response was received
          return {
            success: false,
            error: 'No response received from search provider. Please check your internet connection.',
            toolId: this.id,
            actionName: 'duckduckgo'
          };
        } else {
          // Something happened in setting up the request that triggered an Error
          return {
            success: false,
            error: `Web search failed: ${error.message || error}`,
            toolId: this.id,
            actionName: 'duckduckgo'
          };
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Web search failed: ${error.message || error}`,
        toolId: this.id,
        actionName: 'duckduckgo'
      };
    }
  }

  private async executeMultiSearch(input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      const query = input.query as string;
      if (!query) {
        return {
          success: false,
          error: '\'query\' is required.',
          toolId: this.id,
          actionName: 'multi'
        };
      }

      // Get the list of engines to search, or use defaults
      const engines = (input.engines as string[]) || ['duckduckgo', 'bing', 'google'];

      // Validate engines
      const validEngines = ['duckduckgo', 'bing', 'google', 'yahoo', 'yandex'];
      const invalidEngines = engines.filter(e => !validEngines.includes(e.toLowerCase()));

      if (invalidEngines.length > 0) {
        return {
          success: false,
          error: `Invalid search engines: ${invalidEngines.join(', ')}. Valid options are: ${validEngines.join(', ')}.`,
          toolId: this.id,
          actionName: 'multi'
        };
      }

      // Initialize results object
      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};

      // Search with DuckDuckGo if requested
      if (engines.some(e => e.toLowerCase() === 'duckduckgo')) {
        try {
          const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1`;
          const response = await axios({ method: 'get', url });
          const data = response.data;

          results.duckduckgo = {
            abstractText: data.AbstractText || '',
            abstractSource: data.AbstractSource || '',
            abstractURL: data.AbstractURL || '',
            answer: data.Answer || '',
            relatedTopics: (data.RelatedTopics || []).slice(0, 5).map((topic: { Text?: string; FirstURL?: string }) => ({
              text: topic.Text || '',
              url: topic.FirstURL || ''
            }))
          };
        } catch (error: any) {
          errors.duckduckgo = `DuckDuckGo search failed: ${error.message || 'Unknown error'}`;
        }
      }

      // Search with Bing if requested (simulated)
      if (engines.some(e => e.toLowerCase() === 'bing')) {
        try {
          // In a real implementation, this would use the Bing Search API
          // For now, we'll simulate results
          results.bing = {
            searchUrl: `https://www.bing.com/search?q=${encodeURIComponent(query)}`,
            message: 'Bing search results would appear here in a real implementation.'
          };
        } catch (error: any) {
          errors.bing = `Bing search failed: ${error.message || 'Unknown error'}`;
        }
      }

      // Search with Google if requested (simulated)
      if (engines.some(e => e.toLowerCase() === 'google')) {
        try {
          // In a real implementation, this would use the Google Custom Search API
          // For now, we'll simulate results
          results.google = {
            searchUrl: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
            message: 'Google search results would appear here in a real implementation.'
          };
        } catch (error: any) {
          errors.google = `Google search failed: ${error.message || 'Unknown error'}`;
        }
      }

      // Search with Yahoo if requested (simulated)
      if (engines.some(e => e.toLowerCase() === 'yahoo')) {
        try {
          results.yahoo = {
            searchUrl: `https://search.yahoo.com/search?p=${encodeURIComponent(query)}`,
            message: 'Yahoo search results would appear here in a real implementation.'
          };
        } catch (error: any) {
          errors.yahoo = `Yahoo search failed: ${error.message || 'Unknown error'}`;
        }
      }

      // Search with Yandex if requested (simulated)
      if (engines.some(e => e.toLowerCase() === 'yandex')) {
        try {
          results.yandex = {
            searchUrl: `https://yandex.com/search/?text=${encodeURIComponent(query)}`,
            message: 'Yandex search results would appear here in a real implementation.'
          };
        } catch (error: any) {
          errors.yandex = `Yandex search failed: ${error.message || 'Unknown error'}`;
        }
      }

      // Check if we have any successful results
      const successfulEngines = Object.keys(results);
      const failedEngines = Object.keys(errors);

      if (successfulEngines.length === 0) {
        return {
          success: false,
          error: `All search engines failed: ${Object.values(errors).join('; ')}`,
          toolId: this.id,
          actionName: 'multi'
        };
      }

      // Format the output
      return {
        success: true,
        output: {
          query,
          engines: successfulEngines,
          results,
          errors: failedEngines.length > 0 ? errors : undefined
        },
        toolId: this.id,
        actionName: 'multi',
        metadata: {
          successfulEngines,
          failedEngines
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Multi-search failed: ${error.message || error}`,
        toolId: this.id,
        actionName: 'multi'
      };
    }
  }

  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    try {
      // For backward compatibility, check if actionName is undefined and try to get it from input
      const actionId = actionName || input.action as string;
      const query = input.query as string;

      // Validate query
      if (!query) {
        return {
          success: false,
          error: '\'query\' is required.',
          toolId: this.id,
          actionName
        };
      }

      // Create a clean copy of the input without the action property
      const actionInput = { ...input };
      if ('action' in actionInput) {
        delete actionInput.action;
      }

      // If no specific action is provided, default to duckduckgo
      if (!actionId || !this.actions[actionId]) {
        return this.executeDuckDuckGo(actionInput, context);
      }

      // Route to the appropriate action implementation
      switch (actionId) {
        case 'duckduckgo':
          return this.executeDuckDuckGo(actionInput, context);
        case 'multi':
          return this.executeMultiSearch(actionInput, context);
        default:
          return {
            success: false,
            error: `Unknown action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`,
            toolId: this.id,
            actionName,
            metadata: {
              availableActions: Object.keys(this.actions)
            }
          };
      }
    } catch (error: any) {
      // Catch any unexpected errors in the execution flow
      return {
        success: false,
        error: `Unexpected error in Web Search tool: ${error.message}`,
        toolId: this.id,
        actionName,
        metadata: {
          errorStack: error.stack
        }
      };
    }
  }
}

export const webSearchTool = new WebSearchTool();
