import * as vscode from 'vscode';
import { ITool } from './tool.ts.backup';
import { DocumentationTool } from './docsTool';
import { DirectoryListTool } from './directoryListTool';
import { CodeSearchTool } from './codeSearchTool';
import { TerminalCommandTool } from './terminalCommandTool';
import { WebSearchTool } from './webSearchTool';
import { WebReadTool } from './webReadTool';
import { createMemoryTool } from './memoryTool';
import { memoryManager } from '../memory/memoryManager';
import { BrowserPreviewTool } from './browserPreviewTool';
import { DeployWebAppTool } from './deployWebAppTool';
import { GitTool } from './gitTool';
import { EditorActionsTool } from './editorActionsTool';
import { CodeIntelligenceTool } from './codeIntelligenceTool';
import { CodeGenerationTool } from './codeGenerationTool';
import { LintDiagnosticsTool } from './lintDiagnosticsTool';
import { Logger } from '../logger';
import { EnhancedFileSystemTool } from './enhancedFileTools';

// Import advanced tools
import { ExplainCodeTool, DocumentCodeTool, GenerateTestsTool, MultiFileCodeGenTool } from './advancedCodeGenerationTool';
import { FuzzySearchTool, SemanticSearchTool, SearchPreviewTool } from './advancedCodeSearchTool';
import { DocumentationGenTool, DocumentationSearchTool, DocumentationSummaryTool, DocumentationVisualizationTool } from './advancedDocsTool';
import { MultiCursorTool, ClipboardTool, BatchEditTool, FindReplaceTool } from './advancedEditorActionsTool';
import { GitStashTool, GitRevertTool, GitCherryPickTool, GitRebaseTool, GitTagTool, GitBranchGraphTool } from './advancedGitTool';
import { BatchLintTool, AutoFixAllTool, LintSummaryTool, DiagnosticsSearchTool } from './advancedLintDiagnosticsTool';
import { MemorySearchTool, MemoryUpdateTool, MemoryTagTool, MemoryVisualizationTool } from './advancedMemoryTool';
import { WebMultiSearchTool, WebContentExtractTool, WebSnapshotTool, WebDeployStatusTool } from './advancedWebTools';

// Import new advanced analysis tools
import { CodeComplexityTool } from './codeAnalysisTool';
import { SecurityVulnerabilityTool } from './codeAnalysisTool';
import { DependencyAnalysisTool } from './dependencyAnalysisTool';
import { CodeSmellDetectorTool, RefactoringAdvisorTool } from './codeRefactoringTool';

// Import enhanced file system tool
// Import diagnostics tool
import { DiagnosticsTool } from './diagnosticsTool';

// Import advanced editing tools
import { PrecisionEditTool } from './advancedCodeEditingTool';
import { DiffTool } from './advancedDiffTool';
import { LargeFileEditTool } from './largeFileEditTool';

// Phase 2 tools
import { TimeTravelDebuggingTool } from './timeTravelDebuggingTool';
import { CascadeEditingTool } from './cascadeEditingTool';

/**
 * Registry for all available tools in the extension.
 * Acts as a central access point for tool instances.
 */
// Singleton export removed; use ToolRegistry.instance instead.
export class ToolRegistry {
  private static _instance: ToolRegistry | undefined;
  private tools = new Map<string, ITool>();
  private initialized = false;

  private constructor() {
    // Private constructor to prevent direct instantiation
  }

  /**
     * Get the singleton instance of ToolRegistry
     */
  public static get instance(): ToolRegistry {
    if (!ToolRegistry._instance) {
      // Create a new instance if it doesn't exist yet
      ToolRegistry._instance = new ToolRegistry();
      // Note: The context will be set when initialize() is called
    }
    return ToolRegistry._instance;
  }

  /**
     * Initialize the ToolRegistry
     */
  public static async initialize(context: vscode.ExtensionContext): Promise<ToolRegistry> {
    const instance = ToolRegistry.instance; // This will create the instance if it doesn't exist
    if (instance.initialized) {
      Logger.instance.warn('ToolRegistry.initialize() called multiple times. This is allowed but may indicate an issue.');
      return instance;
    }
    await instance.initialize(context);
    return instance;
  }

  /**
     * Get a tool by ID
     */
  public static getTool(toolId: string): ITool | undefined {
    if (!ToolRegistry.instance) {
      throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');
    }
    return ToolRegistry.instance.tools.get(toolId);
  }

  /**
     * Get all registered tools
     */
  public static getAllTools(): ITool[] {
    if (!ToolRegistry.instance) {
      throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');
    }
    return Array.from(ToolRegistry.instance.tools.values());
  }

  /**
     * Register a tool
     */
  public static registerTool(tool: ITool): void {
    if (!ToolRegistry.instance) {
      throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');
    }
    if (ToolRegistry.instance.tools.has(tool.id)) {
      Logger.instance.warn(`Tool with ID '${tool.id}' is already registered. Overwriting.`);
    }
    ToolRegistry.instance.tools.set(tool.id, tool);
    Logger.instance.info(`Registered tool: ${tool.id} (${tool.name}) - ${tool.description}`);
  }

  /**
     * Initialize the registry
     */
  private async initialize(context: vscode.ExtensionContext): Promise<void> {
    if (this.initialized) {
      Logger.instance.warn('ToolRegistry.initialize() called when already initialized');
      return;
    }

    Logger.instance.info('Starting tool registry initialization...');

    // Initialize memory manager first
    await memoryManager.initialize(context);

    // Register built-in tools
    this.registerBuiltInTools();

    // Wait a bit to ensure all tools are registered
    await new Promise(resolve => setTimeout(resolve, 1000));

    Logger.instance.info(`Tool registry initialized with ${this.tools.size} tools`);
    this.initialized = true;
  }

  /**
     * Register built-in tools with the registry
     */
  private registerBuiltInTools() {
    try {
      Logger.instance.info('Starting tool registration...');

      // Register basic tools with standardized IDs
      this.registerToolWithId(new DirectoryListTool(), 'listDir');
      this.registerToolWithId(new CodeSearchTool(), 'codeSearch');
      this.registerToolWithId(new TerminalCommandTool(), 'terminal_command');
      this.registerToolWithId(new WebSearchTool(), 'webSearch');
      this.registerToolWithId(new WebReadTool(), 'webRead');
      this.registerToolWithId(createMemoryTool(memoryManager), 'memory_management');
      this.registerToolWithId(new BrowserPreviewTool(), 'browserPreview');
      this.registerToolWithId(new DeployWebAppTool(), 'deployWebApp');
      this.registerToolWithId(new GitTool(), 'git');
      this.registerToolWithId(new EditorActionsTool(), 'editor');
      this.registerToolWithId(new CodeIntelligenceTool(), 'codeIntel');
      this.registerToolWithId(new CodeGenerationTool(), 'codeGen');
      this.registerToolWithId(new LintDiagnosticsTool(), 'lint_diagnostics');
      this.registerToolWithId(new DiagnosticsTool(), 'diagnostics');
      this.registerToolWithId(new EnhancedFileSystemTool(), 'enhancedFile');
      this.registerToolWithId(new DocumentationTool(), 'docs');

      // Register advanced code generation tools
      this.registerToolWithId(new ExplainCodeTool(), 'explain');
      this.registerToolWithId(new DocumentCodeTool(), 'document');
      this.registerToolWithId(new GenerateTestsTool(), 'generateTests');
      this.registerToolWithId(new MultiFileCodeGenTool(), 'multiFileGen');
      this.registerToolWithId(new DocumentationGenTool(), 'docGen');
      this.registerToolWithId(new DocumentationSearchTool(), 'docSearch');
      this.registerToolWithId(new DocumentationSummaryTool(), 'docSummary');
      this.registerToolWithId(new DocumentationVisualizationTool(), 'docViz');
      this.registerToolWithId(new FuzzySearchTool(), 'fuzzySearch');
      this.registerToolWithId(new SemanticSearchTool(), 'semanticSearch');
      this.registerToolWithId(new SearchPreviewTool(), 'searchPreview');
      this.registerToolWithId(new MultiCursorTool(), 'multiCursor');
      this.registerToolWithId(new ClipboardTool(), 'clipboard');
      this.registerToolWithId(new BatchEditTool(), 'batchEdit');
      this.registerToolWithId(new FindReplaceTool(), 'findReplace');
      this.registerToolWithId(new GitStashTool(), 'stash');
      this.registerToolWithId(new GitRevertTool(), 'revert');
      this.registerToolWithId(new GitCherryPickTool(), 'cherryPick');
      this.registerToolWithId(new GitRebaseTool(), 'rebase');
      this.registerToolWithId(new GitTagTool(), 'tag');
      this.registerToolWithId(new GitBranchGraphTool(), 'branchGraph');
      this.registerToolWithId(new BatchLintTool(), 'batchLint');
      this.registerToolWithId(new AutoFixAllTool(), 'autoFixAll');
      this.registerToolWithId(new LintSummaryTool(), 'lintSummary');
      this.registerToolWithId(new DiagnosticsSearchTool(), 'diagnosticsSearch');
      this.registerToolWithId(new MemorySearchTool(memoryManager), 'memorySearch');
      this.registerToolWithId(new MemoryUpdateTool(memoryManager), 'memoryUpdate');
      this.registerToolWithId(new MemoryTagTool(memoryManager), 'memoryTag');
      this.registerToolWithId(new MemoryVisualizationTool(memoryManager), 'memoryViz');
      this.registerToolWithId(new WebMultiSearchTool(), 'webMultiSearch');
      this.registerToolWithId(new WebContentExtractTool(), 'webExtract');
      this.registerToolWithId(new WebSnapshotTool(), 'webSnapshot');
      this.registerToolWithId(new WebDeployStatusTool(), 'deployStatus');
      this.registerToolWithId(new CodeComplexityTool(), 'codeComplexity');
      this.registerToolWithId(new SecurityVulnerabilityTool(), 'securityVulnerability');
      this.registerToolWithId(new DependencyAnalysisTool(), 'dependencyAnalysis');
      this.registerToolWithId(new CodeSmellDetectorTool(), 'codeSmellDetector');
      this.registerToolWithId(new RefactoringAdvisorTool(), 'refactoringAdvisor');
      this.registerToolWithId(new PrecisionEditTool(), 'precisionEdit');
      this.registerToolWithId(new DiffTool(), 'diffTool');
      this.registerToolWithId(new LargeFileEditTool(), 'largeFileEdit');

      // Register advanced code search tools
      this.registerToolWithId(new FuzzySearchTool(), 'fuzzySearch');
      this.registerToolWithId(new SemanticSearchTool(), 'semanticSearch');
      this.registerToolWithId(new SearchPreviewTool(), 'searchPreview');

      // Register advanced documentation tools
      this.registerToolWithId(new DocumentationGenTool(), 'docGen');
      this.registerToolWithId(new DocumentationSearchTool(), 'docSearch');
      this.registerToolWithId(new DocumentationSummaryTool(), 'docSummary');
      this.registerToolWithId(new DocumentationVisualizationTool(), 'docViz');

      // Register advanced editor action tools
      this.registerToolWithId(new MultiCursorTool(), 'multiCursor');
      this.registerToolWithId(new ClipboardTool(), 'clipboard');
      this.registerToolWithId(new BatchEditTool(), 'batchEdit');
      this.registerToolWithId(new FindReplaceTool(), 'findReplace');

      // Register advanced git tools
      this.registerToolWithId(new GitStashTool(), 'stash');
      this.registerToolWithId(new GitRevertTool(), 'revert');
      this.registerToolWithId(new GitCherryPickTool(), 'cherryPick');
      this.registerToolWithId(new GitRebaseTool(), 'rebase');
      this.registerToolWithId(new GitTagTool(), 'tag');
      this.registerToolWithId(new GitBranchGraphTool(), 'branchGraph');

      // Register advanced lint diagnostic tools
      this.registerToolWithId(new BatchLintTool(), 'batchLint');
      this.registerToolWithId(new AutoFixAllTool(), 'autoFixAll');
      this.registerToolWithId(new LintSummaryTool(), 'lintSummary');
      this.registerToolWithId(new DiagnosticsSearchTool(), 'diagnosticsSearch');

      // Register advanced memory tools
      this.registerToolWithId(new MemorySearchTool(memoryManager), 'memorySearch');
      this.registerToolWithId(new MemoryUpdateTool(memoryManager), 'memoryUpdate');
      this.registerToolWithId(new MemoryTagTool(memoryManager), 'memoryTag');
      this.registerToolWithId(new MemoryVisualizationTool(memoryManager), 'memoryViz');

      // Register advanced web tools
      this.registerToolWithId(new WebMultiSearchTool(), 'webMultiSearch');
      this.registerToolWithId(new WebContentExtractTool(), 'webExtract');
      this.registerToolWithId(new WebSnapshotTool(), 'webSnapshot');
      this.registerToolWithId(new WebDeployStatusTool(), 'deployStatus');

      // Register advanced code analysis tools
      this.registerToolWithId(new CodeComplexityTool(), 'codeComplexity');
      this.registerToolWithId(new SecurityVulnerabilityTool(), 'securityVulnerability');
      this.registerToolWithId(new DependencyAnalysisTool(), 'dependencyAnalysis');

      // Register advanced code refactoring tools
      this.registerToolWithId(new CodeSmellDetectorTool(), 'codeSmellDetector');
      this.registerToolWithId(new RefactoringAdvisorTool(), 'refactoringAdvisor');

      // Register advanced code editing tools
      this.registerToolWithId(new PrecisionEditTool(), 'precisionEdit');
      this.registerToolWithId(new DiffTool(), 'diffTool');
      this.registerToolWithId(new LargeFileEditTool(), 'largeFileEdit');

      // Phase 2: Register revolutionary tools
      this.registerToolWithId(new TimeTravelDebuggingTool(), 'timeTravelDebug');
      this.registerToolWithId(new CascadeEditingTool(), 'cascadeEdit');

      // Phase 5: Register revolutionary tools
      this.registerRevolutionaryPhase5Tools();

      Logger.instance.info(`Initialized ${this.tools.size} tools in registry (including Phase 5 revolutionary tools).`);
    } catch (error) {
      Logger.instance.error('Error registering built-in tools:', error);
    }
  }

  /**
     * Register Phase 5 Revolutionary Tools
     */
  private registerRevolutionaryPhase5Tools(): void {
    try {
      // Import and register revolutionary tools
      const { QuantumAnalysisTool } = require('./quantumAnalysisTool');
      const { NeuralCodeSynthesisTool } = require('./neuralCodeSynthesisTool');

      // Register Quantum Analysis Tool
      this.registerToolWithId(new QuantumAnalysisTool(), 'quantum_analysis');

      // Register Neural Code Synthesis Tool
      this.registerToolWithId(new NeuralCodeSynthesisTool(), 'neural_code_synthesis');

      // Update essential tools to include revolutionary tools
      this.updateEssentialToolsWithRevolutionary();

      Logger.instance.info('Phase 5 revolutionary tools registered successfully');
    } catch (error) {
      Logger.instance.error('Error registering Phase 5 revolutionary tools:', error);
    }
  }

  /**
     * Update essential tools to include revolutionary capabilities
     */
  private updateEssentialToolsWithRevolutionary(): void {
    // Add revolutionary tools to essential tools for advanced agents
    const revolutionaryToolIds = [
      'quantum_analysis',
      'neural_code_synthesis',
      'timeTravelDebug'
    ];

    // These will be available to agents that request revolutionary capabilities
    Logger.instance.debug(`Revolutionary tools available: ${revolutionaryToolIds.join(', ')}`);
  }

  /**
     * Registers a tool with the registry
     */
  public registerToolWithId(tool: ITool, id: string): void {
    // Override the tool's ID with our standardized ID
    Object.defineProperty(tool, 'id', {
      value: id,
      writable: false,
      configurable: false
    });

    this.registerTool(tool);
  }

  registerTool(tool: ITool): void {
    if (this.tools.has(tool.id)) {
      Logger.instance.warn(`Tool with ID '${tool.id}' is already registered. Overwriting.`);
    }
    this.tools.set(tool.id, tool);
    Logger.instance.info(`Registered tool: ${tool.id} (${tool.name}) - ${tool.description}`);

    // Log the tool's actions if it's a multi-action tool
    if (tool.type === 'multi-action' && tool.actions) {
      Object.entries(tool.actions).forEach(([actionName, action]) => {
        Logger.instance.debug(`  Action: ${actionName} - ${action.description || 'No description'}`);
      });
    }
  }

  /**
     * Gets a tool by ID
     */
  getTool(toolId: string): ITool | undefined {
    const tool = this.tools.get(toolId);
    if (!tool) {
      const availableTools = Array.from(this.tools.keys()).join(', ');
      Logger.instance.warn(`Tool with ID '${toolId}' not found. Available tools: ${availableTools}`);
    }
    return tool;
  }

  /**
     * Gets all registered tools
     */
  getAllTools(): ITool[] {
    return Array.from(this.tools.values());
  }

  /**
     * Converts array of tool IDs to a map of tool instances
     */
  getToolsByIds(toolIds: string[]): Map<string, ITool> {
    const result = new Map<string, ITool>();

    for (const id of toolIds) {
      const tool = this.getTool(id);
      if (tool) {
        result.set(id, tool);
      } else {
        Logger.instance.warn(`Tool with ID '${id}' not found.`);
      }
    }

    return result;
  }

  /**
     * Get all essential tools for agents
     */
  getEssentialTools(): Map<string, ITool> {
    const essentialToolIds = [
      'listDir',
      'codeSearch',
      'memory_management',
      'editor',
      'terminal_command',
      'webSearch',
      'git',
      'codeIntel',
      'memorySearch',
      'memoryUpdate'
    ];

    return this.getToolsByIds(essentialToolIds);
  }

  /**
     * Get tools by category
     */
  getToolsByCategory(category: string): ITool[] {
    return Array.from(this.tools.values()).filter(tool =>
      tool.category === category
    );
  }

  /**
     * Get all available tool categories
     */
  getToolCategories(): string[] {
    const categories = new Set<string>();
    for (const tool of this.tools.values()) {
      if (tool.category) {
        categories.add(tool.category);
      }
    }
    return Array.from(categories);
  }
}
