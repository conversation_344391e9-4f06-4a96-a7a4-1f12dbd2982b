/**
 * Codessa Agent Configuration UI Styles
 * 
 * A clean, form-focused interface for configuring agents
 */

:root {
    --codessa-primary: #4D7AFF;
    --codessa-primary-light: #7A9BFF;
    --codessa-primary-dark: #2B5CE0;
    --codessa-secondary: #34D399;
    --codessa-accent: #EC4899;
    --codessa-background: #FFFFFF;
    --codessa-foreground: #1F2937;
    --codessa-gray-100: #F3F4F6;
    --codessa-gray-200: #E5E7EB;
    --codessa-gray-300: #D1D5DB;
    --codessa-gray-400: #9CA3AF;
    --codessa-gray-500: #6B7280;
    --codessa-gray-600: #4B5563;
    --codessa-gray-700: #374151;
    --codessa-gray-800: #1F2937;
    --codessa-gray-900: #111827;
    --codessa-error: #F87171;
    --codessa-warning: #FBBF24;
    --codessa-success: #34D399;
    --codessa-info: #60A5FA;
}

body.vscode-dark {
    --codessa-background: #111827;
    --codessa-foreground: #F9FAFB;
    --codessa-gray-100: #1F2937;
    --codessa-gray-200: #374151;
    --codessa-gray-300: #4B5563;
    --codessa-gray-400: #6B7280;
    --codessa-gray-500: #9CA3AF;
    --codessa-gray-600: #D1D5DB;
    --codessa-gray-700: #E5E7EB;
    --codessa-gray-800: #F3F4F6;
    --codessa-gray-900: #F9FAFB;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--codessa-background);
    color: var(--codessa-foreground);
    font-size: 14px;
    line-height: 1.5;
}

* {
    box-sizing: border-box;
}

.config-container {
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
    height: 100vh;
    padding: 20px;
}

.config-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
}

.config-header .logo {
    width: 32px;
    height: 32px;
    margin-right: 12px;
}

.config-header h1 {
    margin: 0;
    font-size: 24px;
    color: var(--vscode-editor-foreground);
}

.config-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.form-section {
    margin-bottom: 28px;
    background: var(--vscode-editor-background);
    border: 1px solid var(--vscode-editor-lineHighlightBorder);
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}

.form-section h2 {
    margin: 0 0 16px 0;
    font-size: 18px;
    color: var(--vscode-editor-foreground);
}

.form-group {
    margin-bottom: 16px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--vscode-editor-foreground);
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--vscode-input-border);
    background: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 4px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
}

.form-control:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

.description {
    margin-top: 4px;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
}

/* Checkbox container styles */
.checkbox-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.checkbox-item {
    display: flex;
    align-items: center;
    background-color: var(--codessa-gray-100);
    border: 1px solid var(--codessa-gray-200);
    border-radius: 4px;
    padding: 8px 12px;
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.checkbox-item:hover {
    background-color: var(--codessa-gray-200);
}

.checkbox-item.selected {
    background-color: rgba(77, 122, 255, 0.1);
    border-color: var(--codessa-primary);
}

.checkbox-item input[type="checkbox"] {
    margin-right: 8px;
}

.checkbox-item .item-label {
    font-weight: 500;
}

.checkbox-item .item-description {
    font-size: 12px;
    color: var(--codessa-gray-500);
    margin-top: 4px;
}

.tool-item, .agent-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tool-item input[type="checkbox"], 
.agent-item input[type="checkbox"] {
    margin: 0;
}

.tool-item label, 
.agent-item label {
    margin: 0;
    cursor: pointer;
}

/* Form actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding: 16px;
    background-color: var(--codessa-gray-100);
    border-top: 1px solid var(--codessa-gray-200);
    position: sticky;
    bottom: 0;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn.primary {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.btn.primary:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
}

.btn.secondary {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.btn.secondary:hover:not(:disabled) {
    background: var(--vscode-button-secondaryHoverBackground);
}

.btn:active {
    transform: translateY(1px);
}

/* Connection status */
#connection-status {
    margin-left: 12px;
    font-size: 13px;
}

#connection-status.success {
    color: var(--vscode-testing-iconPassed);
}

#connection-status.error {
    color: var(--vscode-testing-iconFailed);
}

.status-success {
    color: var(--codessa-success);
}

.status-error {
    color: var(--codessa-error);
}

.status-warning {
    color: var(--codessa-warning);
}

.status-info {
    color: var(--codessa-info);
}

/* Spinner animation */
@keyframes spin {
    to { transform: rotate(360deg); }
}

.spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(77, 122, 255, 0.2);
    border-radius: 50%;
    border-top-color: var(--codessa-primary);
    animation: spin 0.8s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .config-form {
        padding: 16px;
    }
    
    .form-section {
        padding: 16px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}