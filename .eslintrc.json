{"root": true, "env": {"browser": true, "es2022": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended-type-checked", "plugin:@typescript-eslint/stylistic-type-checked"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/consistent-type-imports": "warn", "indent": ["error", 2, {"SwitchCase": 1}], "quotes": ["error", "single"], "semi": ["error", "always"], "no-console": "warn"}, "overrides": [{"files": ["*.js"], "env": {"node": true}, "rules": {"@typescript-eslint/no-var-requires": "off"}}]}