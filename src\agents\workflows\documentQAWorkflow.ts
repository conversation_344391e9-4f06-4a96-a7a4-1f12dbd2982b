/**
 * Document QA Workflow
 *
 * This module provides a workflow for document question-answering:
 * - Loading and processing documents
 * - Analyzing document content
 * - Answering questions based on document content
 */

import { ITool } from '../../tools/tool.ts.backup';
import { Agent } from '../agentUtilities/agent';
import { <PERSON>ssa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';
import { workflowRegistry } from './workflowRegistry';
import { logger } from '../../logger';
import { StructuredTool } from './corePolyfill';
import { codessaMemoryProvider as codessaMemoryManager } from '../../memory/codessa/codessaMemory';
import { MemorySource, MemoryType } from '../../memory/types';

/**
 * Create a Document QA workflow for answering questions about documents
 */
export function createDocumentQAWorkflow(
  id: string,
  name: string,
  description: string,
  qaAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  logger.info(`Creating Document QA workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const documentLoadingNode = Codessa.createAgentNode('document-loading', 'Document Loading', qaAgent);
  const documentProcessingNode = Codessa.createAgentNode('document-processing', 'Document Processing', qaAgent);
  const questionAnalysisNode = Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);
  const contextRetrievalNode = Codessa.createAgentNode('context-retrieval', 'Context Retrieval', qaAgent);

  const documentQAMemoryManager = {
    async storeDocumentContent(content: string, metadata: Record<string, any> = {}): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content,
          metadata: {
            source: 'file' as MemorySource,
            type: 'document' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['document', 'content'],
            ...metadata
          }
        });
        logger.info('Saved document content to memory');
      } catch (error) {
        logger.error('Failed to save document content to memory:', error);
      }
    },

    async retrieveRelevantContent(query: string): Promise<any[]> {
      try {
        const memories = await codessaMemoryManager.searchMemories({
          query,
          limit: 5
        });

        logger.info(`Retrieved ${memories.length} relevant document content items from memory`);
        return memories;
      } catch (error) {
        logger.error('Failed to retrieve relevant document content from memory:', error);
        return [];
      }
    },

    async storeQuestion(question: string, answer: string): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content: JSON.stringify({ question, answer }),
          metadata: {
            source: 'conversation' as MemorySource,
            type: 'conversation' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['document', 'qa', 'question', 'answer']
          }
        });
        logger.info('Saved question-answer pair to memory');
      } catch (error) {
        logger.error('Failed to save question-answer pair to memory:', error);
      }
    }
  };

  const answerGenerationNode = Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);
  const answerVerificationNode = Codessa.createAgentNode('answer-verification', 'Answer Verification', qaAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-loading', source: 'input', target: 'document-loading', type: 'default' },
    { name: 'loading-to-processing', source: 'document-loading', target: 'document-processing', type: 'default' },
    { name: 'processing-to-question', source: 'document-processing', target: 'question-analysis', type: 'default' },
    { name: 'question-to-retrieval', source: 'question-analysis', target: 'context-retrieval', type: 'default' },
    { name: 'retrieval-to-generation', source: 'context-retrieval', target: 'answer-generation', type: 'default' },
    { name: 'generation-to-verification', source: 'answer-generation', target: 'answer-verification', type: 'default' },
    { name: 'verification-to-output', source: 'answer-verification', target: 'output', type: 'default' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect context retrieval to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `retrieval-to-tool-${index}`,
        source: 'context-retrieval',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to answer generation
      edges.push({
        name: `tool-${index}-to-generation`,
        source: `tool-${index}`,
        target: 'answer-generation',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      documentLoadingNode,
      documentProcessingNode,
      questionAnalysisNode,
      contextRetrievalNode,
      answerGenerationNode,
      answerVerificationNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'document-qa' as OperationMode,
    tags: ['document-qa', 'question-answering', 'document-analysis'],
    metadata: {
      memoryManager: documentQAMemoryManager
    }
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a specialized PDF QA workflow
 */
export function createPDFQAWorkflow(
  id: string,
  name: string,
  description: string,
  qaAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  logger.info(`Creating PDF QA workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const pdfLoadingNode = Codessa.createAgentNode('pdf-loading', 'PDF Loading', qaAgent);
  const pdfParsingNode = Codessa.createAgentNode('pdf-parsing', 'PDF Parsing', qaAgent);
  const textExtractionNode = Codessa.createAgentNode('text-extraction', 'Text Extraction', qaAgent);
  const imageExtractionNode = Codessa.createAgentNode('image-extraction', 'Image Extraction', qaAgent);
  const tableExtractionNode = Codessa.createAgentNode('table-extraction', 'Table Extraction', qaAgent);
  const contentAnalysisNode = Codessa.createAgentNode('content-analysis', 'Content Analysis', qaAgent);

  const pdfQAMemoryManager = {
    async storePDFContent(content: string, contentType: string, metadata: Record<string, any> = {}): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content,
          metadata: {
            source: 'file' as MemorySource,
            type: 'document' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['pdf', 'document', contentType],
            ...metadata
          }
        });
        logger.info(`Saved PDF ${contentType} content to memory`);
      } catch (error) {
        logger.error(`Failed to save PDF ${contentType} content to memory:`, error);
      }
    },

    async retrieveRelevantPDFContent(query: string): Promise<any[]> {
      try {
        const memories = await codessaMemoryManager.searchMemories({
          query,
          limit: 5,
          filter: {
            tags: ['pdf']
          }
        });

        logger.info(`Retrieved ${memories.length} relevant PDF content items from memory`);
        return memories;
      } catch (error) {
        logger.error('Failed to retrieve relevant PDF content from memory:', error);
        return [];
      }
    },

    async storePDFQuestion(question: string, answer: string): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content: JSON.stringify({ question, answer }),
          metadata: {
            source: 'conversation' as MemorySource,
            type: 'conversation' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['pdf', 'document', 'qa', 'question', 'answer']
          }
        });
        logger.info('Saved PDF question-answer pair to memory');
      } catch (error) {
        logger.error('Failed to save PDF question-answer pair to memory:', error);
      }
    }
  };

  const questionAnalysisNode = Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);
  const answerGenerationNode = Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-loading', source: 'input', target: 'pdf-loading', type: 'default' },
    { name: 'loading-to-parsing', source: 'pdf-loading', target: 'pdf-parsing', type: 'default' },
    { name: 'parsing-to-text', source: 'pdf-parsing', target: 'text-extraction', type: 'default' },
    { name: 'parsing-to-image', source: 'pdf-parsing', target: 'image-extraction', type: 'default' },
    { name: 'parsing-to-table', source: 'pdf-parsing', target: 'table-extraction', type: 'default' },
    { name: 'text-to-analysis', source: 'text-extraction', target: 'content-analysis', type: 'default' },
    { name: 'image-to-analysis', source: 'image-extraction', target: 'content-analysis', type: 'default' },
    { name: 'table-to-analysis', source: 'table-extraction', target: 'content-analysis', type: 'default' },
    { name: 'analysis-to-question', source: 'content-analysis', target: 'question-analysis', type: 'default' },
    { name: 'question-to-answer', source: 'question-analysis', target: 'answer-generation', type: 'default' },
    { name: 'answer-to-output', source: 'answer-generation', target: 'output', type: 'default' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect content analysis to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `analysis-to-tool-${index}`,
        source: 'content-analysis',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to question analysis
      edges.push({
        name: `tool-${index}-to-question`,
        source: `tool-${index}`,
        target: 'question-analysis',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      pdfLoadingNode,
      pdfParsingNode,
      textExtractionNode,
      imageExtractionNode,
      tableExtractionNode,
      contentAnalysisNode,
      questionAnalysisNode,
      answerGenerationNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'document-qa' as OperationMode,
    tags: ['pdf', 'document-qa', 'question-answering'],
    metadata: {
      memoryManager: pdfQAMemoryManager
    }
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}
