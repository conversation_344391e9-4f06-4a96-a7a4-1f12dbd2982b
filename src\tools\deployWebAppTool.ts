import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import * as vscode from 'vscode';
import * as cp from 'child_process';
import { WebDeployStatusTool } from './advancedWebTools';
import { z } from 'zod';

export class DeployWebAppTool implements ITool {
  readonly id = 'deployWebApp';
  readonly name = 'Deploy Web App (Advanced)';
  readonly description = 'Deploys web apps and manages deployment status with advanced options.';
  readonly type = 'multi-action'; // Required by ITool
  readonly actions: Record<string, any> = {
    'deploy': {
      id: 'deploy',
      name: 'Deploy Web App',
      description: 'Deploy a JavaScript web application using a deployment provider.',
      type: 'single-action', // Required by ITool
      actions: {}, // Required by ITool
      singleActionSchema: z.object({
        projectPath: z.string().describe('Path to the project to deploy'),
        provider: z.string().describe('Deployment provider (netlify, vercel)'),
        args: z.string().optional().describe('Additional arguments for the deployment command')
      }),
      inputSchema: {
        type: 'object',
        properties: {
          projectPath: { type: 'string', description: 'Path to the project to deploy' },
          provider: { type: 'string', description: 'Deployment provider (netlify, vercel)' },
          args: { type: 'string', description: 'Additional arguments for the deployment command' }
        },
        required: ['projectPath', 'provider']
      },
      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
        const projectPath = input.projectPath as string;
        const provider = input.provider as string;
        const args = input.args as string || '';
        if (!projectPath || !provider) {
          return { success: false, error: '\'projectPath\' and \'provider\' are required.', toolId: 'deploy', actionName };
        }
        let cwd = projectPath;
        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0 && !projectPath.match(/^.:\\/) && !projectPath.startsWith('/')) {
          cwd = vscode.workspace.workspaceFolders[0].uri.fsPath + '/' + projectPath;
        }
        let command = '';
        if (provider === 'netlify') {
          command = `npx netlify deploy --dir . ${args}`;
        } else if (provider === 'vercel') {
          command = `npx vercel --prod ${args}`;
        } else {
          return { success: false, error: `Unsupported provider: ${provider}`, toolId: 'deploy', actionName };
        }
        try {
          const result = await new Promise<string>((resolve, reject) => {
            cp.exec(command, { cwd }, (err, stdout, stderr) => {
              if (err && !stdout) return reject(stderr || err.message);
              resolve(stdout || stderr);
            });
          });
          return { success: true, output: result.trim(), toolId: 'deploy', actionName };
        } catch (error: any) {
          return { success: false, error: `Deployment failed: ${error.message || error}`, toolId: 'deploy', actionName };
        }
      }
    },
    'status': {
      ...new WebDeployStatusTool(),
      type: 'single-action',
      actions: {},
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const projectPath = input.projectPath as string;
        const provider = input.provider as string;
        if (!projectPath || !provider) {
          return { success: false, error: '\'projectPath\' and \'provider\' are required.', toolId: 'status', actionName };
        }
        // Placeholder implementation
        return { success: true, output: `Deployment status for ${projectPath} using ${provider}: Active`, toolId: 'status', actionName };
      }
    },
  };
  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    // For backward compatibility, check if actionName is undefined and try to get it from input
    const actionId = actionName || input.action as string || 'deploy';
    const actionTool = this.actions[actionId];
    if (!actionTool) {
      return { success: false, error: `Unknown deploy action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };
    }
    const actionInput = { ...input };
    if ('action' in actionInput) {
      delete actionInput.action;
    }

    // Check if the action tool has the new execute method signature
    if (actionTool.execute.length >= 2) {
      // Pass the actionId as the actionName parameter to the nested tool
      return actionTool.execute(actionId, actionInput, context);
    } else {
      // Fallback for older tools that don't have the new signature
      const result = await (actionTool as any).execute(actionInput, context);
      // Add the actionName to the result if it's not already there
      if (result && !result.actionName) {
        result.actionName = actionId;
      }
      return result;
    }
  }
}

export const deployWebAppTool = new DeployWebAppTool();
