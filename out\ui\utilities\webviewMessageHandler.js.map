{"version": 3, "file": "webviewMessageHandler.js", "sourceRoot": "", "sources": ["../../../src/ui/utilities/webviewMessageHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,yCAAsC;AAItC,MAAa,qBAAqB;IACxB,OAAO,CAA0B;IAEzC,YAAY,OAAgC;QAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAEO,UAAU,CAAC,OAAoB;QACrC,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,OAAO,CAAC;QACzB,CAAC;QACD,OAAQ,OAAe,CAAC,OAAO,IAAI,OAAO,CAAC;IAC7C,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,OAAY,EAAE,OAAoB;QAC3D,IAAI,CAAC;YACH,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxB,KAAK,mBAAmB;oBACtB,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBAE3C,KAAK,cAAc;oBACjB,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAEnC,KAAK,wBAAwB;oBAC3B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACtD,OAAO,IAAI,CAAC;gBAEd,KAAK,kBAAkB;oBACrB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAChD,OAAO,IAAI,CAAC;gBAEd,KAAK,cAAc;oBACjB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;oBAChE,OAAO,IAAI,CAAC;gBAEd;oBACE,eAAM,CAAC,IAAI,CAAC,oBAAoB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;oBACnD,OAAO,IAAI,CAAC;YAChB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,eAAM,CAAC,KAAK,CAAC,2BAA2B,YAAY,EAAE,CAAC,CAAC;YACxD,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAY;QAC5C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACvD,OAAO,CAAC,OAAO,EACf,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,KAAK,EAAE,IAAI,CACZ,CAAC;QACF,OAAO,EAAE,SAAS,EAAE,MAAM,KAAK,KAAK,EAAE,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,qBAAqB;YACrB,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAE5D,wBAAwB;YACxB,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAE/D,oBAAoB;YACpB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAClE,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACvC,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;oBAC7B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,EAAE,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAjFD,sDAiFC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { logger } from '../../logger';\n\ntype WebviewLike = vscode.WebviewPanel | vscode.WebviewView | { webview: vscode.Webview };\n\nexport class WebviewMessageHandler {\n  private context: vscode.ExtensionContext;\n\n  constructor(context: vscode.ExtensionContext) {\n    this.context = context;\n  }\n\n  private getWebview(webview: WebviewLike): vscode.Webview {\n    if ('webview' in webview) {\n      return webview.webview;\n    }\n    return (webview as any).webview || webview;\n  }\n\n  public async handleMessage(message: any, webview: WebviewLike): Promise<Record<string, any> | boolean | null> {\n    try {\n      switch (message.command) {\n        case 'showConfirmDialog':\n          return this.handleConfirmDialog(message);\n\n        case 'clearStorage':\n          return this.handleClearStorage();\n\n        case 'showInformationMessage':\n          vscode.window.showInformationMessage(message.message);\n          return true;\n\n        case 'showErrorMessage':\n          vscode.window.showErrorMessage(message.message);\n          return true;\n\n        case 'reloadWindow':\n          vscode.commands.executeCommand('workbench.action.reloadWindow');\n          return true;\n\n        default:\n          logger.warn(`Unknown command: ${message.command}`);\n          return null;\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      logger.error(`Error handling message: ${errorMessage}`);\n      return { error: errorMessage };\n    }\n  }\n\n  private async handleConfirmDialog(message: any): Promise<{ confirmed: boolean }> {\n    const result = await vscode.window.showInformationMessage(\n      message.message,\n      { modal: true },\n      'Yes', 'No'\n    );\n    return { confirmed: result === 'Yes' };\n  }\n\n  private async handleClearStorage(): Promise<{ success: boolean; error?: string }> {\n    try {\n      // Clear global state\n      await this.context.globalState.update('codessa', undefined);\n\n      // Clear workspace state\n      await this.context.workspaceState.update('codessa', undefined);\n\n      // Clear all secrets\n      const secrets = await this.context.secrets.get('codessa.secrets');\n      if (secrets) {\n        const secretsObj = JSON.parse(secrets);\n        for (const key in secretsObj) {\n          await this.context.secrets.delete(`codessa.secrets.${key}`);\n        }\n      }\n\n      return { success: true };\n    } catch (error) {\n      logger.error('Failed to clear storage:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : String(error)\n      };\n    }\n  }\n}\n"]}