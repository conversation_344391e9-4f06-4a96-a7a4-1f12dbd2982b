"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.openModeSelector = openModeSelector;
const vscode = __importStar(require("vscode"));
const modeSelectorView_1 = require("../ui/chat/modeSelectorView");
const chatView_1 = require("../ui/chat/chatView");
const agentManager_1 = require("../agents/agentUtilities/agentManager");
const logger_1 = require("../logger");
const operationMode_1 = require("../agents/agentModes/operationMode");
const receiverAgent_1 = __importDefault(require("../agents/agentTypes/receiverAgent"));
const supervisorAgent_1 = require("../agents/agentTypes/supervisorAgent");
const agentFactory_1 = require("../agents/agentUtilities/agentFactory");
/**
 * Initialize the multi-agent system with specialized agents for each mode
 */
async function initializeAgentSystem(context) {
    try {
        logger_1.logger.info('Initializing multi-agent system...');
        // Create receiver agent for preprocessing user input
        const receiverAgentInstance = new receiverAgent_1.default({
            id: 'receiver-agent',
            name: 'Receiver Agent',
            description: 'Preprocesses user input to enhance clarity and detail',
            systemPromptName: 'receiverAgent',
            tools: ['file', 'search', 'memory']
        });
        // Create supervisor agent for orchestrating everything
        const supervisorAgent = new supervisorAgent_1.SupervisorAgent({
            id: 'supervisor-agent',
            name: 'Supervisor Agent',
            description: 'Orchestrates specialized agents and workflows',
            systemPromptName: 'supervisorAgent',
            tools: ['file', 'search', 'memory', 'workflow'],
            isSupervisor: true
        }, receiverAgentInstance);
        // Register the core agents
        agentManager_1.agentManager.addAgent(receiverAgentInstance);
        agentManager_1.agentManager.addAgent(supervisorAgent);
        // Create specialized agents for each mode
        const modes = operationMode_1.operationModeRegistry.getAllModes();
        for (const mode of modes) {
            const agentConfig = {
                id: `${mode.id}-agent`,
                name: `${mode.displayName} Agent`,
                description: `Specialized agent for ${mode.displayName} mode`,
                systemPromptName: `${mode.id}Agent`,
                tools: ['file', 'search', 'memory', 'workflow']
            };
            // Create a specialized agent for this mode
            const modeAgent = (0, agentFactory_1.createModeAgent)(mode.id, agentConfig);
            agentManager_1.agentManager.addAgent(modeAgent);
            // Register with supervisor
            supervisorAgent.registerModeAgent(mode.id, modeAgent);
            logger_1.logger.info(`Created and registered specialized agent for ${mode.displayName} mode`);
        }
        // Store in global state for access from other parts of the extension
        context.globalState.update('receiverAgent', receiverAgentInstance);
        context.globalState.update('supervisorAgent', supervisorAgent);
        logger_1.logger.info('Multi-agent system initialized successfully');
        return supervisorAgent;
    }
    catch (error) {
        logger_1.logger.error('Error initializing multi-agent system:', error);
        throw error;
    }
}
/**
 * Get the supervisor agent or initialize the agent system if needed
 */
async function getSupervisorAgent(context) {
    // Check if supervisor agent exists in global state
    let supervisorAgent = context.globalState.get('supervisorAgent');
    if (!supervisorAgent) {
        try {
            // Initialize the agent system
            supervisorAgent = await initializeAgentSystem(context);
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize multi-agent system:', error);
            vscode.window.showErrorMessage('Failed to initialize multi-agent system');
            return null;
        }
    }
    return supervisorAgent;
}
/**
 * Open the mode selector
 */
async function openModeSelector(context) {
    try {
        logger_1.logger.info('Opening mode selector...');
        // Create the mode selector view
        const modeSelectorView = modeSelectorView_1.ModeSelectorView.createOrShow(context.extensionUri, context);
        // Handle mode selection
        modeSelectorView.onModeSelected(async (mode) => {
            logger_1.logger.info(`Mode selected: ${mode.displayName} (${mode.id})`);
            // Get the supervisor agent
            const supervisorAgent = await getSupervisorAgent(context);
            if (!supervisorAgent) {
                return;
            }
            // Open chat panel with the selected mode and supervisor agent
            chatView_1.ChatPanel.createOrShow(context.extensionUri, supervisorAgent, context, mode);
        });
        logger_1.logger.info('Mode selector opened successfully');
    }
    catch (error) {
        logger_1.logger.error('Error opening mode selector:', error);
        vscode.window.showErrorMessage(`Failed to open mode selector: ${error instanceof Error ? error.message : String(error)}`);
    }
}
//# sourceMappingURL=openModeSelector.js.map