import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import { llmService } from '../llm/llmService';
import { LLMConfig } from '../config';
import { ExplainCodeTool, DocumentCodeTool, GenerateTestsTool, MultiFileCodeGenTool } from './advancedCodeGenerationTool';
import { z } from 'zod';

export class CodeGenerationTool implements ITool {
  readonly id = 'codeGen';
  readonly name = 'Code Generation & Refactor (Advanced)';
  readonly description = 'Generate, refactor, insert, explain, document, create tests, and multi-file code generation using AI.';
  readonly type = 'multi-action'; // Required by ITool
  readonly actions: Record<string, any> = {
    'generate': {
      id: 'generate',
      name: 'Generate Code',
      description: 'Generate code from prompt using AI.',
      type: 'single-action', // Required by ITool
      actions: {}, // Required by ITool
      singleActionSchema: z.object({
        prompt: z.string().describe('The prompt describing what code to generate')
      }),
      inputSchema: {
        type: 'object',
        properties: {
          prompt: { type: 'string', description: 'The prompt describing what code to generate' }
        },
        required: ['prompt']
      },
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const provider = await llmService.getProviderForConfig(this.llmConfig);
        if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: 'generate', actionName };
        const result = await provider.generate({ prompt: input.prompt, systemPrompt: 'You are an expert code generator.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });
        if (result.error) return { success: false, error: result.error, toolId: 'generate', actionName };
        return { success: true, output: result.content, toolId: 'generate', actionName };
      }
    },
    'refactor': {
      id: 'refactor',
      name: 'Refactor Code',
      description: 'Refactor code using AI.',
      type: 'single-action', // Required by ITool
      actions: {}, // Required by ITool
      singleActionSchema: z.object({
        prompt: z.string().describe('The prompt describing how to refactor the code'),
        code: z.string().describe('The code to refactor')
      }),
      inputSchema: {
        type: 'object',
        properties: {
          prompt: { type: 'string', description: 'The prompt describing how to refactor the code' },
          code: { type: 'string', description: 'The code to refactor' }
        },
        required: ['prompt', 'code']
      },
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const provider = await llmService.getProviderForConfig(this.llmConfig);
        if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: 'refactor', actionName };
        const userPrompt = `Refactor the following code as requested.\nRequest: ${input.prompt}\nCode:\n${input.code}`;
        const result = await provider.generate({ prompt: userPrompt, systemPrompt: 'You are an expert code refactoring assistant.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });
        if (result.error) return { success: false, error: result.error, toolId: 'refactor', actionName };
        return { success: true, output: result.content, toolId: 'refactor', actionName };
      }
    },
    'insert': {
      id: 'insert',
      name: 'Insert Code',
      description: 'Insert code at a position in a file.',
      type: 'single-action', // Required by ITool
      actions: {}, // Required by ITool
      singleActionSchema: z.object({
        prompt: z.string().describe('The prompt describing what code to insert'),
        filePath: z.string().describe('The path to the file where code should be inserted'),
        position: z.object({}).describe('The position in the file where code should be inserted')
      }),
      inputSchema: {
        type: 'object',
        properties: {
          prompt: { type: 'string', description: 'The prompt describing what code to insert' },
          filePath: { type: 'string', description: 'The path to the file where code should be inserted' },
          position: { type: 'object', description: 'The position in the file where code should be inserted' }
        },
        required: ['prompt', 'filePath', 'position']
      },
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const provider = await llmService.getProviderForConfig(this.llmConfig);
        if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: 'insert', actionName };
        const result = await provider.generate({ prompt: input.prompt, systemPrompt: 'You are an expert code generator.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });
        if (result.error) return { success: false, error: result.error, toolId: 'insert', actionName };
        return { success: true, output: result.content, toolId: 'insert', actionName };
      }
    },
    'explain': new ExplainCodeTool(),
    'document': new DocumentCodeTool(),
    'generateTests': new GenerateTestsTool(),
    'multiFileGen': new MultiFileCodeGenTool(),
  };
  private llmConfig: LLMConfig = {
    provider: 'openai',
    modelId: 'gpt-3.5-turbo',
    options: { temperature: 0.2 }
  };
  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    // For backward compatibility, check if actionName is undefined and try to get it from input
    const action = actionName || input.action as string;
    if (!action) {
      return { success: false, error: `Action parameter is required. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };
    }
    const actionTool = this.actions[action];
    if (!actionTool) {
      return { success: false, error: `Unknown code generation action: ${action}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };
    }
    const actionInput = { ...input };
    if ('action' in actionInput) {
      delete actionInput.action;
    }

    // Check if the action tool has the new execute method signature
    if (actionTool.execute.length >= 2) {
      // Pass the action as the actionName parameter to the nested tool
      return actionTool.execute(action, actionInput, context);
    } else {
      // Fallback for older tools that don't have the new signature
      const result = await (actionTool as any).execute(actionInput, context);
      // Add the actionName to the result if it's not already there
      if (result && !result.actionName) {
        result.actionName = action;
      }
      return result;
    }
  }
}

export const codeGenerationTool = new CodeGenerationTool();
