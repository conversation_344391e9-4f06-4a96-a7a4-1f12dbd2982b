import * as vscode from 'vscode';
import { Agent } from './agent';
import { logger } from '../../logger';
import { AgentRole } from '../../types/agent';
import { LLMConfig } from '../../llm/types';


/**
 * Event emitted when agents are changed
 */
export interface AgentChangeEvent {
  type: 'add' | 'update' | 'delete';
  agentId: string;
}

/**
 * Manages agents in the system
 */

export interface AgentConfig {
    id: string;
    name: string;
    description?: string;
    role: AgentRole;
    capabilities: string[];
    llmProvider: string;
    llmModel: string;
    temperature?: number;
    maxTokens?: number;
    systemPromptName: string;
    llm?: LLMConfig;
    tools?: string[];
    isSupervisor?: boolean;
}

export class AgentManager {
  private static _instance: AgentManager;
  private _agents: Map<string, Agent> = new Map();
  private _onAgentsChanged = new vscode.EventEmitter<AgentChangeEvent>();
  private _receiverAgent?: Agent;
  private _supervisorAgent?: Agent;

  /**
     * Event that fires when agents are changed
     */
  public readonly onAgentsChanged = this._onAgentsChanged.event;

  private constructor() {
    // Initialize with default agent
    this.addAgent(new Agent({
      id: 'default',
      name: 'Default Agent',
      systemPromptName: 'default',
      role: 'assistant',
      capabilities: ['chat'],
      llmProvider: 'openai',
      llmModel: 'gpt-4'
    }));
  }

  /**
     * Get the singleton instance of AgentManager
     */
  public static getInstance(): AgentManager {
    if (!AgentManager._instance) {
      AgentManager._instance = new AgentManager();
    }
    return AgentManager._instance;
  }

  /**
     * Get all agents
     */
  public getAllAgents(): Agent[] {
    return Array.from(this._agents.values());
  }

  /**
     * Get all agent IDs
     */
  public getAgentIds(): string[] {
    return Array.from(this._agents.keys());
  }

  /**
     * Get an agent by ID
     */
  public getAgent(id: string): Agent | undefined {
    return this._agents.get(id);
  }

  /**
     * Get the default agent
     */
  public getDefaultAgent(): Agent | undefined {
    // First try to find an agent with ID 'default'
    let defaultAgent = this._agents.get('default');

    // If not found, try to find an agent with name 'default' (case insensitive)
    if (!defaultAgent) {
      defaultAgent = Array.from(this._agents.values()).find(
        agent => agent.name.toLowerCase() === 'default'
      );
    }

    // If still not found, return the first agent
    if (!defaultAgent && this._agents.size > 0) {
      defaultAgent = Array.from(this._agents.values())[0];
    }

    return defaultAgent;
  }

  /**
     * Add a new agent
     */
  public addAgent(agent: Agent): void {
    this._agents.set(agent.id, agent);
    logger.info(`Added agent ${agent.id}`);
    this._onAgentsChanged.fire({ type: 'add', agentId: agent.id });

    // Set receiver and supervisor agents if they match the roles
    if (agent.role === 'receiver') {
      this._receiverAgent = agent;
    } else if (agent.role === 'supervisor') {
      this._supervisorAgent = agent;
    }
  }

  /**
     * Create a new agent from config
     */
  public createAgent(_config: AgentConfig): Agent {
    const agent = new Agent({
      ..._config,
      role: 'assistant',
      capabilities: ['chat'],
      llmProvider: 'openai',
      llmModel: 'gpt-4'
    });
    this.addAgent(agent);
    return agent;
  }

  /**
     * Update an existing agent
     */
  public updateAgent(id: string, updatedAgent: Agent): boolean {
    if (this._agents.has(id)) {
      this._agents.set(id, updatedAgent);
      logger.info(`Updated agent ${id}`);
      this._onAgentsChanged.fire({ type: 'update', agentId: id });
      return true;
    }
    return false;
  }

  /**
     * Delete an agent by ID
     */
  public deleteAgent(id: string): boolean {
    const result = this._agents.delete(id);
    if (result) {
      logger.info(`Deleted agent ${id}`);
      this._onAgentsChanged.fire({ type: 'delete', agentId: id });

      // Clear receiver or supervisor if this agent was one
      if (this._receiverAgent === this._agents.get(id)) {
        this._receiverAgent = undefined;
      }
      if (this._supervisorAgent === this._agents.get(id)) {
        this._supervisorAgent = undefined;
      }
    }
    return result;
  }

  public getReceiverAgent(): Agent | undefined {
    return this._receiverAgent;
  }

  public setReceiverAgent(agent: Agent): void {
    this._receiverAgent = agent;
    this.addAgent(agent);
    logger.info(`Receiver agent set: ${agent.name}`);
  }

  public getSupervisorAgent(): Agent | undefined {
    return this._supervisorAgent;
  }

  public setSupervisorAgent(agent: Agent): void {
    this._supervisorAgent = agent;
    this.addAgent(agent);
    logger.info(`Supervisor agent set: ${agent.name}`);
  }

  public getAdditionalAgents(): Agent[] {
    return this.getAllAgents().filter(agent =>
      agent !== this._receiverAgent &&
      agent !== this._supervisorAgent
    );
  }

  public dispose(): void {
    this._agents.clear();
    this._receiverAgent = undefined;
    this._supervisorAgent = undefined;
  }
}

// Export a singleton instance
export const agentManager = AgentManager.getInstance();
