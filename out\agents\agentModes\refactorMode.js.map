{"version": 3, "file": "refactorMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/refactorMode.ts"], "names": [], "mappings": ";;;AACA,mDAA4E;AAG5E,yCAAsC;AACtC,qDAAkD;AAElD;;GAEG;AACH,MAAa,YAAa,SAAQ,6BAAa;IACpC,EAAE,GAAG,UAAU,CAAC;IAChB,WAAW,GAAG,UAAU,CAAC;IACzB,WAAW,GAAG,sCAAsC,CAAC;IACrD,IAAI,GAAG,UAAU,CAAC;IAClB,kBAAkB,GAAG,2BAAW,CAAC,cAAc,CAAC;IAChD,yBAAyB,GAAG,IAAI,CAAC,CAAC,iCAAiC;IACnE,sBAAsB,GAAG,KAAK,CAAC;IAExC;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY,EACZ,aAA4B,EAC5B,gBAAsC;QAEtC,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,OAAO,EAAE,CAAC,CAAC;YAExE,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE7E,kCAAkC;YAClC,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;wBACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,wCAAwC,CAAC,CAAC;oBAClG,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,CAAC;gBACrF,kCAAkC;YACpC,CAAC;YAED,iCAAiC;YACjC,MAAM,MAAM,GAAG;;;uBAGE,OAAO;;;EAG5B,cAAc;;EAEd,aAAa;;;;;;;;;;;;;;;;;;CAkBd,CAAC;YAEI,oCAAoC;YACpC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAEnE,0CAA0C;YAC1C,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,qBAAqB,OAAO,EAAE,CAAC,CAAC;oBACrE,MAAM,WAAW,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,EAAE,WAAW,CAAC,CAAC;gBACjF,qCAAqC;YACvC,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YAC3E,OAAO,8CAA8C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAChH,CAAC;IACH,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG,EAAE,kDAAkD;YACpE,SAAS,EAAE,IAAI,EAAG,4CAA4C;YAC9D,aAAa,EAAE,EAAE;YACjB,IAAI,EAAE,MAAM;SACb,CAAC;IACJ,CAAC;CACF;AAxGD,oCAwGC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { contextManager } from './contextManager';\n\n/**\n * Refactor Mode - Code refactoring and optimization\n */\nexport class RefactorMode extends OperationMode {\n  readonly id = 'refactor';\n  readonly displayName = 'Refactor';\n  readonly description = 'Refactor and optimize code structure';\n  readonly icon = '$(tools)';\n  readonly defaultContextType = ContextType.SELECTED_FILES;\n  readonly requiresHumanVerification = true; // Refactoring should be verified\n  readonly supportsMultipleAgents = false;\n\n  /**\n     * Process a user message in Refactor mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    contextSource: ContextSource,\n    additionalParams?: Record<string, any>\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Refactor mode: ${message}`);\n\n      // Get context content\n      const contextContent = await contextManager.getContextContent(contextSource);\n\n      // Add memory context if available\n      let memoryContext = '';\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          const relevantMemories = await agentMemory.getRelevantMemories(message);\n          if (relevantMemories && relevantMemories.length > 0) {\n            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to refactor context`);\n          }\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to retrieve memory context for refactor:', memoryError);\n        // Continue without memory context\n      }\n\n      // Prepare the refactoring prompt\n      const prompt = `\nYou are a code refactoring expert. Analyze and refactor the following code based on the request.\n\nRefactoring Request: ${message}\n\nCode to Refactor:\n${contextContent}\n\n${memoryContext}\n\nPlease provide:\n1. Analysis of current code structure and issues\n2. Refactored code with improvements\n3. Explanation of changes made\n4. Benefits of the refactoring\n5. Potential risks or considerations\n6. Testing recommendations\n\nFocus on:\n- Code readability and maintainability\n- Performance optimization\n- Design patterns and best practices\n- Reducing complexity and duplication\n- Improving error handling\n\nProvide the refactored code in proper code blocks with clear explanations.\n`;\n\n      // Generate response using the agent\n      const response = await agent.generate(prompt, this.getLLMParams());\n\n      // Store the refactoring session in memory\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          await agentMemory.addMessage('user', `Refactor request: ${message}`);\n          await agentMemory.addMessage('assistant', response);\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to store refactor session in memory:', memoryError);\n        // Continue without storing in memory\n      }\n\n      return response;\n    } catch (error) {\n      Logger.instance.error('Error processing message in Refactor mode:', error);\n      return `Error processing your refactoring request: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Get LLM parameters specific to Refactor mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.1, // Very low temperature for consistent refactoring\n      maxTokens: 3000,  // High token limit for detailed refactoring\n      stopSequences: [],\n      mode: 'edit'\n    };\n  }\n}\n"]}