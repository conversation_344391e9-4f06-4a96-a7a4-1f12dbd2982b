{"version": 3, "file": "modelConfig.js", "sourceRoot": "", "sources": ["../../src/config/modelConfig.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,sDAYC;AAED,oDAEC;AAlBD,+CAAiC;AAEjC,SAAgB,qBAAqB;IACnC,0DAA0D;IAC1D,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAS,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IAC1E,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAS,eAAe,EAAE,QAAQ,CAAC,CAAC;IAEpE,OAAO;QACL,QAAQ,EAAE,gBAAgB;QAC1B,OAAO,EAAE,aAAa;QACtB,WAAW,EAAE,GAAG;QAChB,SAAS,EAAE,IAAI;KAChB,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB;IAClC,OAAO,EAAE,CAAC;AACZ,CAAC", "sourcesContent": ["import { LLMConfig } from '../llm/types';\nimport * as vscode from 'vscode';\n\nexport function getDefaultModelConfig(): LLMConfig {\n  // Get selected provider and model from chat view settings\n  const config = vscode.workspace.getConfiguration('codessa');\n  const selectedProvider = config.get<string>('selectedProvider', 'ollama');\n  const selectedModel = config.get<string>('selectedModel', 'llama3');\n\n  return {\n    provider: selectedProvider,\n    modelId: selectedModel,\n    temperature: 0.7,\n    maxTokens: 2000\n  };\n}\n\nexport function getMaxToolIterations(): number {\n  return 10;\n} "]}