{"version": 3, "file": "prWorkflows.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/prWorkflows.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;AAaH,4DAgMC;AASD,wDAsMC;AA1ZD,iDAAwE;AAExE,yCAAsC;AAEtC;;;;;;GAMG;AACH,SAAgB,wBAAwB,CAAC,OAIxC;IACC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,sBAAsB,CAAC;IACpD,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,yEAAyE,CAAC;IAErH,2CAA2C;IAC3C,MAAM,QAAQ,GAAoB;QAChC,EAAE,EAAE,wBAAwB,IAAI,CAAC,GAAG,EAAE,EAAE;QACxC,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,aAAa;QAC5B,IAAI,EAAE,aAAa;QACnB,KAAK,EAAE;YACL;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,OAAO;aACf;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,QAAQ;aAChB;SACF;QACD,KAAK,EAAE;YACL;gBACE,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,aAAyB;gBAC/B,IAAI,EAAE,cAAc;aACrB;SACF;QACD,WAAW,EAAE,OAAO;KACrB,CAAC;IAEF,wCAAwC;IACxC,MAAM,kBAAkB,GAAc;QACpC,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,iBAAiB;QACvB,KAAK,EAAE,iBAAiB;QACxB,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAE1D,4BAA4B;YAC5B,OAAO;gBACL,OAAO,EAAE;oBACP,eAAe,EAAE;wBACf,YAAY,EAAE,CAAC;wBACf,UAAU,EAAE,GAAG;wBACf,YAAY,EAAE,EAAE;wBAChB,OAAO,EAAE,gDAAgD;qBAC1D;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,0BAAW,CACb,iBAAiB,EACjB,EAAE,EACF,IAAI,CAAC,SAAS,CAAC;wBACb,YAAY,EAAE,CAAC;wBACf,UAAU,EAAE,GAAG;wBACf,YAAY,EAAE,EAAE;wBAChB,OAAO,EAAE,gDAAgD;qBAC1D,CAAC,CACH;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,yBAAyB,GAAc;QAC3C,EAAE,EAAE,yBAAyB;QAC7B,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,yBAAyB;QAC/B,KAAK,EAAE,yBAAyB;QAChC,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAElD,kDAAkD;YAClD,kDAAkD;YAElD,qDAAqD;YACrD,MAAM,OAAO,GAAG,wCAAwC,CAAC;YACzD,MAAM,aAAa,GAAG;;;;;;;;;;;;;CAa3B,CAAC;YAEI,OAAO;gBACL,OAAO,EAAE;oBACP,uBAAuB,EAAE;wBACvB,KAAK,EAAE,OAAO;wBACd,WAAW,EAAE,aAAa;qBAC3B;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,wBAAS,CAAC;;SAEf,OAAO;;;EAGd,aAAa,EAAE,CAAC;iBACT;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,YAAY,GAAc;QAC9B,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,WAAW;QAClB,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpC,kDAAkD;YAClD,yDAAyD;YAEzD,uBAAuB;YACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,KAAK,GAAG,oCAAoC,QAAQ,EAAE,CAAC;YAE7D,OAAO;gBACL,OAAO,EAAE;oBACP,SAAS,EAAE;wBACT,MAAM,EAAE,QAAQ;wBAChB,GAAG,EAAE,KAAK;wBACV,MAAM,EAAE,MAAM;qBACf;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,0BAAW,CACb,WAAW,EACX,EAAE,EACF,OAAO,QAAQ,0BAA0B,KAAK,EAAE,CACjD;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,wBAAwB;IACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACxC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IAC/C,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAElC,iCAAiC;IACjC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,iBAAiB;QACzB,IAAI,EAAE,aAAyB;QAC/B,IAAI,EAAE,aAAa;KACpB,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,yBAAyB;QACjC,IAAI,EAAE,aAAyB;QAC/B,IAAI,EAAE,2BAA2B;KAClC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,aAAyB;QAC/B,IAAI,EAAE,gBAAgB;KACvB,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,aAAyB;QAC/B,IAAI,EAAE,WAAW;KAClB,CAAC,CAAC;IAEH,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,sBAAsB,CAAC,OAItC;IACC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,oBAAoB,CAAC;IAClD,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,6DAA6D,CAAC;IAEzG,2CAA2C;IAC3C,MAAM,QAAQ,GAAoB;QAChC,EAAE,EAAE,sBAAsB,IAAI,CAAC,GAAG,EAAE,EAAE;QACtC,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,WAAW;QAC1B,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE;YACL;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,OAAO;aACf;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,QAAQ;aAChB;SACF;QACD,KAAK,EAAE;YACL;gBACE,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,WAAuB;gBAC7B,IAAI,EAAE,cAAc;aACrB;SACF;QACD,WAAW,EAAE,OAAO;KACrB,CAAC;IAEF,sCAAsC;IACtC,MAAM,eAAe,GAAc;QACjC,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,sBAAsB;QAC5B,KAAK,EAAE,sBAAsB;QAC7B,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAE7D,iCAAiC;YACjC,OAAO;gBACL,OAAO,EAAE;oBACP,YAAY,EAAE;wBACZ,YAAY,EAAE,CAAC;wBACf,UAAU,EAAE,EAAE;wBACd,YAAY,EAAE,EAAE;wBAChB,UAAU,EAAE,QAAQ;wBACpB,eAAe,EAAE;4BACf,qCAAqC;4BACrC,gCAAgC;4BAChC,uCAAuC;yBACxC;qBACF;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,0BAAW,CACb,cAAc,EACd,EAAE,EACF,IAAI,CAAC,SAAS,CAAC;wBACb,YAAY,EAAE,CAAC;wBACf,UAAU,EAAE,EAAE;wBACd,YAAY,EAAE,EAAE;wBAChB,UAAU,EAAE,QAAQ;wBACpB,eAAe,EAAE;4BACf,qCAAqC;4BACrC,gCAAgC;4BAChC,uCAAuC;yBACxC;qBACF,CAAC,CACH;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,kBAAkB,GAAc;QACpC,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,0BAA0B;QAChC,KAAK,EAAE,0BAA0B;QACjC,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAEtD,kDAAkD;YAClD,MAAM,cAAc,GAAG;gBACrB;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,EAAE;oBACR,OAAO,EAAE,qEAAqE;iBAC/E;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,EAAE;oBACR,OAAO,EAAE,iEAAiE;iBAC3E;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,EAAE;oBACR,OAAO,EAAE,yEAAyE;iBACnF;aACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE;oBACP,eAAe,EAAE;wBACf,QAAQ,EAAE,cAAc;wBACxB,OAAO,EAAE,uEAAuE;qBACjF;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,wBAAS,CAAC;;;;;;+EAMuD,CAAC;iBACvE;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,gBAAgB,GAAc;QAClC,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,eAAe;QACtB,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAE7C,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YAEtD,OAAO;gBACL,OAAO,EAAE;oBACP,aAAa,EAAE;wBACb,EAAE,EAAE,QAAQ;wBACZ,MAAM,EAAE,WAAW;wBACnB,MAAM,EAAE,mBAAmB;qBAC5B;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,0BAAW,CACb,eAAe,EACf,EAAE,EACF,cAAc,QAAQ,wDAAwD,CAC/E;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,wBAAwB;IACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACrC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACxC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAEtC,iCAAiC;IACjC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,cAAc;QACtB,IAAI,EAAE,WAAuB;QAC7B,IAAI,EAAE,kBAAkB;KACzB,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE,iBAAiB;QACzB,IAAI,EAAE,WAAuB;QAC7B,IAAI,EAAE,sBAAsB;KAC7B,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,eAAe;QACvB,IAAI,EAAE,WAAuB;QAC7B,IAAI,EAAE,sBAAsB;KAC7B,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,WAAuB;QAC7B,IAAI,EAAE,WAAW;KAClB,CAAC,CAAC;IAEH,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,uDAAuD", "sourcesContent": ["/**\n * PR Workflow Templates\n *\n * This module provides specialized workflow templates for PR-related tasks:\n * - PR creation\n * - PR review\n * - PR feedback\n * - PR merge\n */\n\nimport { AIMessage, ToolMessage, StructuredTool } from './corePolyfill';\nimport { GraphDefinition, GraphState, GraphNode, EdgeType } from './types';\nimport { Logger } from '../../logger';\n\n/**\n * Creates a PR creation workflow\n * This workflow helps with creating a PR by:\n * 1. Analyzing the changes\n * 2. Generating a PR title and description\n * 3. Creating the PR\n */\nexport function createPRCreationWorkflow(options: {\n  name?: string;\n  description?: string;\n  tools?: StructuredTool[];\n}): GraphDefinition {\n  const name = options.name || 'PR Creation Workflow';\n  const description = options.description || 'Workflow for creating pull requests with proper descriptions and titles';\n\n  // Create base workflow using ReAct pattern\n  const workflow: GraphDefinition = {\n    id: `pr-creation-workflow-${Date.now()}`,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'pr-creation',\n    type: 'pr-creation',\n    nodes: [\n      {\n        id: 'input',\n        type: 'input',\n        name: 'Input',\n        label: 'Input'\n      },\n      {\n        id: 'output',\n        type: 'output',\n        name: 'Output',\n        label: 'Output'\n      }\n    ],\n    edges: [\n      {\n        source: 'input',\n        target: 'output',\n        type: 'pr-creation' as EdgeType,\n        name: 'Default Flow'\n      }\n    ],\n    startNodeId: 'input'\n  };\n\n  // Add specialized nodes for PR creation\n  const analyzeChangesNode: GraphNode = {\n    id: 'analyze_changes',\n    type: 'tool',\n    name: 'Analyze Changes',\n    label: 'Analyze Changes',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Analyzing changes for PR creation');\n\n      // Simulated analysis result\n      return {\n        outputs: {\n          analyze_changes: {\n            filesChanged: 5,\n            linesAdded: 120,\n            linesRemoved: 45,\n            summary: 'Added new feature X and refactored component Y'\n          }\n        },\n        messages: [\n          new ToolMessage(\n            'analyze_changes',\n            {},\n            JSON.stringify({\n              filesChanged: 5,\n              linesAdded: 120,\n              linesRemoved: 45,\n              summary: 'Added new feature X and refactored component Y'\n            })\n          )\n        ]\n      };\n    }\n  };\n\n  const generatePRDescriptionNode: GraphNode = {\n    id: 'generate_pr_description',\n    type: 'agent',\n    name: 'Generate PR Description',\n    label: 'Generate PR Description',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Generating PR description');\n\n      // We could use the changes from the previous step\n      // const changes = state.outputs?.analyze_changes;\n\n      // Generate PR title and description based on changes\n      const prTitle = 'Add feature X and refactor component Y';\n      const prDescription = `\n## Changes\n- Added new feature X\n- Refactored component Y for better performance\n- Updated tests\n\n## Impact\n- Improves user experience\n- Reduces load time by 15%\n\n## Testing\n- All tests passing\n- Manual testing completed\n`;\n\n      return {\n        outputs: {\n          generate_pr_description: {\n            title: prTitle,\n            description: prDescription\n          }\n        },\n        messages: [\n          new AIMessage(`I've generated a PR title and description based on your changes:\n\nTitle: ${prTitle}\n\nDescription:\n${prDescription}`)\n        ]\n      };\n    }\n  };\n\n  const createPRNode: GraphNode = {\n    id: 'create_pr',\n    type: 'tool',\n    name: 'Create PR',\n    label: 'Create PR',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Creating PR');\n\n      // We could use the PR info from the previous step\n      // const prInfo = state.outputs?.generate_pr_description;\n\n      // Simulate PR creation\n      const prNumber = Math.floor(Math.random() * 1000) + 1;\n      const prUrl = `https://github.com/org/repo/pull/${prNumber}`;\n\n      return {\n        outputs: {\n          create_pr: {\n            number: prNumber,\n            url: prUrl,\n            status: 'open'\n          }\n        },\n        messages: [\n          new ToolMessage(\n            'create_pr',\n            {},\n            `PR #${prNumber} created successfully: ${prUrl}`\n          )\n        ]\n      };\n    }\n  };\n\n  // Add nodes to workflow\n  workflow.nodes.push(analyzeChangesNode);\n  workflow.nodes.push(generatePRDescriptionNode);\n  workflow.nodes.push(createPRNode);\n\n  // Add edges to connect the nodes\n  workflow.edges.push({\n    source: 'input',\n    target: 'analyze_changes',\n    type: 'pr-creation' as EdgeType,\n    name: 'To Analysis'\n  });\n\n  workflow.edges.push({\n    source: 'analyze_changes',\n    target: 'generate_pr_description',\n    type: 'pr-creation' as EdgeType,\n    name: 'To Description Generation'\n  });\n\n  workflow.edges.push({\n    source: 'generate_pr_description',\n    target: 'create_pr',\n    type: 'pr-creation' as EdgeType,\n    name: 'To PR Creation'\n  });\n\n  workflow.edges.push({\n    source: 'create_pr',\n    target: 'output',\n    type: 'pr-creation' as EdgeType,\n    name: 'To Output'\n  });\n\n  return workflow;\n}\n\n/**\n * Creates a PR review workflow\n * This workflow helps with reviewing a PR by:\n * 1. Analyzing the PR changes\n * 2. Checking for issues\n * 3. Generating review comments\n */\nexport function createPRReviewWorkflow(options: {\n  name?: string;\n  description?: string;\n  tools?: StructuredTool[];\n}): GraphDefinition {\n  const name = options.name || 'PR Review Workflow';\n  const description = options.description || 'Workflow for reviewing pull requests and providing feedback';\n\n  // Create base workflow using ReAct pattern\n  const workflow: GraphDefinition = {\n    id: `pr-review-workflow-${Date.now()}`,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'pr-review',\n    type: 'pr-review',\n    nodes: [\n      {\n        id: 'input',\n        type: 'input',\n        name: 'Input',\n        label: 'Input'\n      },\n      {\n        id: 'output',\n        type: 'output',\n        name: 'Output',\n        label: 'Output'\n      }\n    ],\n    edges: [\n      {\n        source: 'input',\n        target: 'output',\n        type: 'pr-review' as EdgeType,\n        name: 'Default Flow'\n      }\n    ],\n    startNodeId: 'input'\n  };\n\n  // Add specialized nodes for PR review\n  const analyzeCodeNode: GraphNode = {\n    id: 'analyze_code',\n    type: 'tool',\n    name: 'Analyze Code Changes',\n    label: 'Analyze Code Changes',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Analyzing code changes for PR review');\n\n      // Simulated code analysis result\n      return {\n        outputs: {\n          analyze_code: {\n            filesChanged: 3,\n            linesAdded: 85,\n            linesRemoved: 32,\n            complexity: 'medium',\n            potentialIssues: [\n              'Possible memory leak in file1.js:45',\n              'Unused variable in file2.js:23',\n              'Missing error handling in file3.js:78'\n            ]\n          }\n        },\n        messages: [\n          new ToolMessage(\n            'analyze_code',\n            {},\n            JSON.stringify({\n              filesChanged: 3,\n              linesAdded: 85,\n              linesRemoved: 32,\n              complexity: 'medium',\n              potentialIssues: [\n                'Possible memory leak in file1.js:45',\n                'Unused variable in file2.js:23',\n                'Missing error handling in file3.js:78'\n              ]\n            })\n          )\n        ]\n      };\n    }\n  };\n\n  const generateReviewNode: GraphNode = {\n    id: 'generate_review',\n    type: 'agent',\n    name: 'Generate Review Comments',\n    label: 'Generate Review Comments',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Generating PR review comments');\n\n      // Generate review comments based on code analysis\n      const reviewComments = [\n        {\n          file: 'file1.js',\n          line: 45,\n          comment: 'This could lead to a memory leak. Consider using a WeakMap instead.'\n        },\n        {\n          file: 'file2.js',\n          line: 23,\n          comment: 'This variable is declared but never used. Consider removing it.'\n        },\n        {\n          file: 'file3.js',\n          line: 78,\n          comment: 'Add try/catch block to handle potential errors in this async operation.'\n        }\n      ];\n\n      return {\n        outputs: {\n          generate_review: {\n            comments: reviewComments,\n            summary: 'The PR has some minor issues that should be addressed before merging.'\n          }\n        },\n        messages: [\n          new AIMessage(`I've reviewed the PR and found the following issues:\n\n1. **file1.js:45** - This could lead to a memory leak. Consider using a WeakMap instead.\n2. **file2.js:23** - This variable is declared but never used. Consider removing it.\n3. **file3.js:78** - Add try/catch block to handle potential errors in this async operation.\n\nOverall, the PR has some minor issues that should be addressed before merging.`)\n        ]\n      };\n    }\n  };\n\n  const submitReviewNode: GraphNode = {\n    id: 'submit_review',\n    type: 'tool',\n    name: 'Submit Review',\n    label: 'Submit Review',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Submitting PR review');\n\n      // Simulate review submission\n      const reviewId = Math.floor(Math.random() * 1000) + 1;\n\n      return {\n        outputs: {\n          submit_review: {\n            id: reviewId,\n            status: 'submitted',\n            result: 'changes_requested'\n          }\n        },\n        messages: [\n          new ToolMessage(\n            'submit_review',\n            {},\n            `PR review #${reviewId} submitted successfully with status: changes_requested`\n          )\n        ]\n      };\n    }\n  };\n\n  // Add nodes to workflow\n  workflow.nodes.push(analyzeCodeNode);\n  workflow.nodes.push(generateReviewNode);\n  workflow.nodes.push(submitReviewNode);\n\n  // Add edges to connect the nodes\n  workflow.edges.push({\n    source: 'input',\n    target: 'analyze_code',\n    type: 'pr-review' as EdgeType,\n    name: 'To Code Analysis'\n  });\n\n  workflow.edges.push({\n    source: 'analyze_code',\n    target: 'generate_review',\n    type: 'pr-review' as EdgeType,\n    name: 'To Review Generation'\n  });\n\n  workflow.edges.push({\n    source: 'generate_review',\n    target: 'submit_review',\n    type: 'pr-review' as EdgeType,\n    name: 'To Review Submission'\n  });\n\n  workflow.edges.push({\n    source: 'submit_review',\n    target: 'output',\n    type: 'pr-review' as EdgeType,\n    name: 'To Output'\n  });\n\n  return workflow;\n}\n\n// No need to re-export, they're already exported above\n"]}