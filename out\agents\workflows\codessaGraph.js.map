{"version": 3, "file": "codessaGraph.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/codessaGraph.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;AAEH,0CAAwB", "sourcesContent": ["/**\n * Codessa Graph Implementation\n * This file re-exports the graph implementation from the graph.ts file\n * to provide a consistent interface for the codessa module\n */\n\nexport * from './graph';\n\n// Additional types needed for the codessa module\nexport interface CompiledGraph {\n    invoke: (state: any, options?: any) => Promise<any>;\n}\n"]}