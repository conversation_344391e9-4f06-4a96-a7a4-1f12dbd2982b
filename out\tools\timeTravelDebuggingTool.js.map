{"version": 3, "file": "timeTravelDebuggingTool.js", "sourceRoot": "", "sources": ["../../src/tools/timeTravelDebuggingTool.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,uCAAoC;AACpC,yDAAwD;AACxD,qEAAkE;AAClE,sCAAmC;AACnC,2CAA6B;AAE7B,YAAY;AACZ,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAC9B,MAAM,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,aAAa;AAqHrD;;GAEG;AACH,MAAa,uBAAuB;IAClB,EAAE,GAAG,qBAAqB,CAAC;IAC3B,IAAI,GAAG,4BAA4B,CAAC;IACpC,WAAW,GAAG,wDAAwD,CAAC;IACvE,IAAI,GAAoB,eAAe,CAAC;IAEhD,OAAO,CAAU;IACjB,cAAc,CAAqB;IACnC,cAAc,CAAyB;IACvC,KAAK,GAAiC,IAAI,GAAG,EAAE,CAAC;IAChD,YAAY,CAAS;IAE7B;QACE,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,qCAAkB,EAAE,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,IAAI,+CAAsB,EAAE,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC;IACzC,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QAC3F,MAAM,eAAe,GAAG,KAAwB,CAAC;QAEjD,IAAI,CAAC;YACH,QAAQ,eAAe,CAAC,OAAO,EAAE,CAAC;gBAChC,KAAK,gBAAgB;oBACnB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,IAAK,EAAmB,EAAE,eAAe,CAAC,CAAC;gBACrF,KAAK,UAAU;oBACb,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAK,EAAmB,EAAE,eAAe,CAAC,CAAC;gBAC/E,KAAK,qBAAqB;oBACxB,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,IAAK,EAAmB,EAAE,eAAe,CAAC,CAAC;gBAC1F;oBACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,WAAoB,EAAE,SAAkB;QACzF,MAAM,QAAQ,GAAG,WAAW,QAAQ,IAAI,WAAW,IAAI,EAAE,IAAI,SAAS,IAAI,EAAE,EAAE,CAAC;QAC/E,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAW,QAAQ,CAAC,CAAC;QACrD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC5D,CAAC;QAED,MAAM,QAAQ,GAAa,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3G,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAEtF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACrE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEpF,IAAI,UAAoC,CAAC;YACzC,IAAI,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;gBACxD,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAa,CAAC;gBAC9C,UAAU,GAAG;oBACX,OAAO,EAAE;wBACP,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,KAAK;wBACpC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK;wBAClC,eAAe,EAAE,MAAM,CAAC,eAAe,EAAE,KAAK;wBAC9C,QAAQ,EAAE,MAAM,CAAC,eAAe,EAAE,OAAO;qBAC1C;oBACD,MAAM,EAAE,MAAM,CAAC,eAAe,IAAI,EAAE;iBACrC,CAAC;YACJ,CAAC;YAED,MAAM,KAAK,GAAkB;gBAC3B,EAAE,EAAE,MAAM,CAAC,IAAI;gBACf,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,MAAM,CAAC,OAAO;gBAC3B,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE;gBAC1C,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM,CAAC,IAAI;gBACnB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,UAAU;aACvB,CAAC;YAEF,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,QAAQ,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvC,QAAQ,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;QAEvD,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEpC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,QAAgB,EAAE,MAAc;QACxE,MAAM,QAAQ,GAAG,gBAAgB,QAAQ,IAAI,MAAM,EAAE,CAAC;QACtD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAmB,QAAQ,CAAC,CAAC;QAC7D,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC5D,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;QAC9F,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YACrF,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,gCAAgC,CAAC,CAAC;QAChE,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,CAAC,MAA0B,CAAC;QACjD,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAElC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;IAC5D,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,WAAmB;QAChE,MAAM,QAAQ,GAAG,OAAO,QAAQ,IAAI,WAAW,EAAE,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAe,QAAQ,CAAC,CAAC;QACzD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC5E,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;gBACtD,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC;gBAC7G,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,gCAAgC,CAAC,CAAC;YAChE,CAAC;YACD,MAAM,YAAY,GAAG,cAAc,CAAC,MAAkB,CAAC;YACvD,MAAM,cAAc,GAAG,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAmB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvG,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc;YAEpG,MAAM,QAAQ,GAAiB;gBAC7B,cAAc;gBACd,aAAa;gBACb,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAkB,CAAC;gBAC3G,MAAM,EAAE,EAAE,EAAE,uBAAuB;gBACnC,OAAO,EAAE,EAAE,EAAG,uBAAuB;aACtC,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEpC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACvE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAqB,EAAE,MAAc;QACrE,MAAM,MAAM,GAAqB;YAC/B,aAAa,EAAE,EAAE;YACjB,kBAAkB,EAAE,EAAE;YACtB,oBAAoB,EAAE,EAAE;YACxB,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,KAAK;SACjB,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ;gBAAE,SAAS;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;YACxH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,MAAM,SAAS,GAAG,MAAM,CAAC,MAA0B,CAAC;gBACpD,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,kBAAkB,CAAC,CAAC;gBAChE,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,oBAAoB,CAAC,CAAC;gBACpE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,MAAM,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC;YACtD,MAAM,EAAE,MAAM,CAAC,kBAAkB;YACjC,QAAQ,EAAE,MAAM,CAAC,oBAAoB;SACtC,CAAC,CAAC;QACH,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEnD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,sBAAsB,CAAC,MAAuB;QACpD,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAyB,CAAC;QAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,OAAO;gBAAE,SAAS;YAEvB,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEnC,IAAI,QAAQ,EAAE,CAAC;gBACb,QAAQ,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IAEO,aAAa,CAAC,MAAqB,EAAE,MAAqB;QAChE,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACrD,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;QAC/D,MAAM,cAAc,GAAG,WAAW,GAAG,WAAW,CAAC;QAEjD,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,mCAAmC;YACrE,OAAO;gBACL,IAAI,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB;gBACxE,SAAS,EAAE,QAAQ;gBACnB,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;gBACnC,SAAS,EAAE,CAAC;aACb,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,YAAsD;QACxF,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QAExC,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEjF,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAC,GAAG,EAAC,EAAE;YACxC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;YAC/E,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAa,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC;oBAChF,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC,CAAC;QAEJ,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACnC,CAAC;IAEO,kBAAkB,CAAC,MAAwB;QACjD,MAAM,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;QACxF,MAAM,kBAAkB,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;QAEvD,IAAI,SAAS,GAAG,EAAE,IAAI,kBAAkB,GAAG,CAAC,EAAE,CAAC;YAC7C,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,SAAS,GAAG,EAAE,IAAI,kBAAkB,GAAG,CAAC,EAAE,CAAC;YACpD,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,mBAAmB,CAAC,MAAwB;QAClD,OAAO,MAAM,CAAC,SAAS,KAAK,MAAM;YAChC,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAChF,CAAC;IAEO,UAAU,CAAC,QAAsB;QAKvC,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,EAAE,MAAM,IAAI,CAAC,CAAC;QAEzD,MAAM,IAAI,GAAG;YACX,QAAQ,EAAE,KAAkC;YAC5C,UAAU,EAAE,GAAG;YACf,eAAe,EAAE,EAAc;SAChC,CAAC;QAEF,IAAI,aAAa,GAAG,CAAC,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;YAC1C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;YACvB,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CACvB,yDAAyD,EACzD,yDAAyD,CAC1D,CAAC;QACJ,CAAC;aAAM,IAAI,aAAa,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,CAAC,IAAI,CACvB,uCAAuC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAChF,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAqB,EAAE,KAAsB;QACxE,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;IACrF,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,OAAqB,EAAE,KAAsB;QAClE,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,IAAI,MAAM,CAAC,CAAC;IAC1E,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAqB,EAAE,KAAsB;QAC7E,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oDAAoD,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC1G,CAAC;QACD,OAAO,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;IAC7E,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAuB;QACzD,MAAM,QAAQ,GAAoB,EAAE,CAAC;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,IAAI,OAAO,EAAE,CAAC;gBACZ,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,iCAAiC,CAAC,QAAyB;QACjE,kFAAkF;QAClF,MAAM,SAAS,GAAoB,EAAE,CAAC;QACtC,wEAAwE;QACxE,sDAAsD;QACtD,OAAO,CAAC,GAAG,CAAC,wDAAwD,EAAE,QAAQ,CAAC,CAAC;QAChF,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,YAAY,CAAI,GAAW;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAkB,CAAC;QAEnD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC;IACpB,CAAC;IAEO,UAAU,CAAI,GAAW,EAAE,IAAO;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YACjD,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACvD,CAAC;CACF;AA9VD,0DA8VC", "sourcesContent": ["/**\n * Time-Travel Debugging Tool - Revolutionary debugging across temporal dimensions\n * \n * This tool provides advanced debugging capabilities by analyzing code history,\n * tracking bug origins, and exploring alternative timelines.\n */\n\nimport { ITool, ToolInput, ToolResult } from './tool';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { GitTool } from './gitTool';\nimport { CodeComplexityTool } from './codeAnalysisTool';\nimport { DependencyAnalysisTool } from './dependencyAnalysisTool';\nimport { logger } from '../logger';\nimport * as path from 'path';\n\n// Constants\nconst DEFAULT_CACHE_SIZE = 50;\nconst CACHE_EXPIRY_MS = 30 * 60 * 1000; // 30 minutes\n\n// Types and interfaces for time travel debugging\nexport interface GitCommit {\n  hash: string;\n  author: string;\n  date: string;\n  message: string;\n  changes: Array<{\n    file: string;\n    additions: number;\n    deletions: number;\n    type: 'added' | 'modified' | 'deleted';\n  }>;\n}\n\nexport interface CodeChange {\n  type: string;\n  description: string;\n  impactLevel: number;\n  metadata: {\n    commit?: string;\n    author?: string;\n    date?: string;\n    files?: string[];\n    additions?: number;\n    deletions?: number;\n    [key: string]: unknown;\n  };\n}\n\nexport interface TimelineEvent {\n  id: string;\n  type: string;\n  description: string;\n  timestamp: number;\n  commit?: string;\n  author?: string;\n  context?: string[];\n  changes?: CodeChange[];\n  state: Record<string, unknown>;\n  complexity?: CodeAnalysis;\n}\n\nexport interface Timeline {\n  events: TimelineEvent[];\n  metadata: {\n    startTime: number;\n    endTime: number;\n    totalEvents: number;\n  };\n  branches?: Array<{\n    name: string;\n    events: TimelineEvent[];\n  }>;\n}\n\nexport interface ChangePattern {\n  type: string;\n  frequency: number;\n  severity?: number;\n  confidence?: number;\n  magnitude?: number;\n  timeframe?: number;\n}\n\nexport interface DependencyImpact {\n  impactedFiles: string[];\n  riskLevel: 'low' | 'medium' | 'high';\n  metrics?: {\n    complexity?: number;\n    coverage?: number;\n    dependencies?: number;\n    [key: string]: number | undefined;\n  };\n  directDependencies: string[];\n  indirectDependencies: string[];\n  impactedAreas: string[];\n}\n\nexport interface CodeAnalysis {\n  complexity?: string;\n  issues: Array<{\n    type: string;\n    severity: string;\n    location?: string;\n  }>;\n  metrics: {\n    cyclomaticComplexity?: number;\n    cognitiveComplexity?: number;\n    [key: string]: number | undefined;\n  };\n  relatedChanges?: CodeChange[];\n  impactedAreas?: string[];\n  originCommit?: GitCommit;\n  riskAssessment?: {\n    severity: 'low' | 'medium' | 'high';\n    confidence: number;\n    recommendations: string[];\n  };\n}\n\n// Class implementation\ninterface CacheEntry<T> {\n  data: T;\n  timestamp: number;\n}\n\ninterface TimeTravelInput extends ToolInput {\n  command: 'analyzeHistory' | 'trackBug' | 'analyzeDependencies';\n  filePath: string;\n  startCommit?: string;\n  endCommit?: string;\n}\n\ntype CacheMap = Map<string, CacheEntry<unknown>>;\n\n/**\n * Time Travel Debugging Tool implementation\n */\nexport class TimeTravelDebuggingTool implements ITool {\n  public readonly id = 'timeTravelDebugging';\n  public readonly name = 'Time-Travel Debugging Tool';\n  public readonly description = 'Analyze code history and trace bug origins across time';\n  public readonly type: 'single-action' = 'single-action';\n\n  private gitTool: GitTool;\n  private complexityTool: CodeComplexityTool;\n  private dependencyTool: DependencyAnalysisTool;\n  private cache: Map<string, CacheEntry<any>> = new Map();\n  private maxCacheSize: number;\n\n  constructor() {\n    this.gitTool = new GitTool();\n    this.complexityTool = new CodeComplexityTool();\n    this.dependencyTool = new DependencyAnalysisTool();\n    this.maxCacheSize = DEFAULT_CACHE_SIZE;\n  }\n\n  public async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    const timeTravelInput = input as TimeTravelInput;\n\n    try {\n      switch (timeTravelInput.command) {\n        case 'analyzeHistory':\n          return await this.analyzeHistory(context ?? ({} as AgentContext), timeTravelInput);\n        case 'trackBug':\n          return await this.trackBug(context ?? ({} as AgentContext), timeTravelInput);\n        case 'analyzeDependencies':\n          return await this.analyzeDependencies(context ?? ({} as AgentContext), timeTravelInput);\n        default:\n          throw new Error(`Unknown command: ${timeTravelInput.command}`);\n      }\n    } catch (error) {\n      logger.error('Error in TimeTravelDebuggingTool:', error);\n      return {\n        success: false,\n        toolId: this.id,\n        error: error instanceof Error ? error.message : String(error)\n      };\n    }\n  }\n\n  private async analyzeCodeHistory(filePath: string, startCommit?: string, endCommit?: string): Promise<ToolResult> {\n    const cacheKey = `history:${filePath}:${startCommit ?? ''}:${endCommit ?? ''}`;\n    const cached = this.getFromCache<Timeline>(cacheKey);\n    if (cached) {\n      return { success: true, output: cached, toolId: this.id };\n    }\n\n    const timeline: Timeline = { events: [], metadata: { startTime: Date.now(), endTime: 0, totalEvents: 0 } };\n    const commits = await this.gitTool.getCommitHistory(filePath, startCommit, endCommit);\n\n    for (const commit of commits) {\n      const changes = await this.gitTool.getChanges(commit.hash, filePath);\n      const complexityResult = await this.complexityTool.execute(undefined, { filePath });\n\n      let complexity: CodeAnalysis | undefined;\n      if (complexityResult.success && complexityResult.output) {\n        const output = complexityResult.output as any;\n        complexity = {\n          metrics: {\n            cyclomatic: output.cyclomatic?.total,\n            cognitive: output.cognitive?.score,\n            maintainability: output.maintainability?.score,\n            halstead: output.maintainability?.details,\n          },\n          issues: output.recommendations ?? [],\n        };\n      }\n\n      const event: TimelineEvent = {\n        id: commit.hash,\n        type: 'commit',\n        description: commit.message,\n        timestamp: new Date(commit.date).getTime(),\n        state: {},\n        commit: commit.hash,\n        author: commit.author,\n        changes: changes,\n        complexity: complexity,\n      };\n\n      timeline.events.push(event);\n    }\n\n    timeline.metadata.endTime = Date.now();\n    timeline.metadata.totalEvents = timeline.events.length;\n\n    this.addToCache(cacheKey, timeline);\n\n    return { success: true, output: timeline, toolId: this.id };\n  }\n\n  private async analyzeTimelineDependencies(filePath: string, commit: string): Promise<ToolResult> {\n    const cacheKey = `dependencies:${filePath}:${commit}`;\n    const cached = this.getFromCache<DependencyImpact>(cacheKey);\n    if (cached) {\n      return { success: true, output: cached, toolId: this.id };\n    }\n\n    const result = await this.dependencyTool.execute(undefined, { filePath, commitHash: commit });\n    if (!result.success || !result.output) {\n      const errorMsg = result.error instanceof Error ? result.error.message : result.error;\n      throw new Error(errorMsg || 'Failed to analyze dependencies');\n    }\n    const impact = result.output as DependencyImpact;\n    impact.riskLevel = this.calculateRiskLevel(impact);\n\n    this.addToCache(cacheKey, impact);\n\n    return { success: true, output: impact, toolId: this.id };\n  }\n\n  private async trackBugOrigin(filePath: string, startCommit: string): Promise<ToolResult> {\n    const cacheKey = `bug:${filePath}:${startCommit}`;\n    const cached = this.getFromCache<CodeAnalysis>(cacheKey);\n    if (cached) {\n      return { success: true, output: cached, toolId: this.id };\n    }\n\n    try {\n      const timelineResult = await this.analyzeCodeHistory(filePath, startCommit);\n      if (!timelineResult.success || !timelineResult.output) {\n        const errorMsg = timelineResult.error instanceof Error ? timelineResult.error.message : timelineResult.error;\n        throw new Error(errorMsg || 'Failed to analyze code history');\n      }\n      const timelineData = timelineResult.output as Timeline;\n      const relatedChanges = timelineData.events.flatMap(e => e.changes).filter((c): c is CodeChange => !!c);\n      const impactedAreas = await this.identifyImpactedAreas({ direct: [], indirect: [] }); // Placeholder\n\n      const analysis: CodeAnalysis = {\n        relatedChanges,\n        impactedAreas,\n        riskAssessment: this.assessRisk({ relatedChanges, impactedAreas, issues: [], metrics: {} } as CodeAnalysis),\n        issues: [], // Add missing property\n        metrics: {},  // Add missing property\n      };\n\n      this.addToCache(cacheKey, analysis);\n\n      return { success: true, output: analysis, toolId: this.id };\n    } catch (error) {\n      const message = error instanceof Error ? error.message : String(error);\n      return { success: false, toolId: this.id, error: message };\n    }\n  }\n\n  private async analyzeChangeImpact(changes: CodeChange[], commit: string): Promise<DependencyImpact> {\n    const impact: DependencyImpact = {\n      impactedFiles: [],\n      directDependencies: [],\n      indirectDependencies: [],\n      impactedAreas: [],\n      riskLevel: 'low'\n    };\n\n    for (const change of changes) {\n      if (!change.metadata?.filePath) continue;\n      const result = await this.dependencyTool.execute(undefined, { filePath: change.metadata.filePath, commitHash: commit });\n      if (result.success && result.output) {\n        const depImpact = result.output as DependencyImpact;\n        impact.directDependencies.push(...depImpact.directDependencies);\n        impact.indirectDependencies.push(...depImpact.indirectDependencies);\n        impact.impactedFiles.push(...depImpact.impactedFiles);\n      }\n    }\n\n    impact.impactedAreas = await this.identifyImpactedAreas({\n      direct: impact.directDependencies,\n      indirect: impact.indirectDependencies\n    });\n    impact.riskLevel = this.calculateRiskLevel(impact);\n\n    return impact;\n  }\n\n  private identifyChangePatterns(events: TimelineEvent[]): ChangePattern[] {\n    const patterns = new Map<string, ChangePattern>();\n\n    for (let i = 0; i < events.length - 1; i++) {\n      const current = events[i];\n      const next = events[i + 1];\n\n      const pattern = this.compareEvents(current, next);\n      if (!pattern) continue;\n\n      const key = `${pattern.type}-${pattern.magnitude}`;\n      const existing = patterns.get(key);\n\n      if (existing) {\n        existing.frequency = (existing.frequency || 1) + 1;\n      } else {\n        patterns.set(key, { ...pattern, frequency: 1 });\n      }\n    }\n\n    return Array.from(patterns.values()).filter(p => (p.frequency || 0) >= 2);\n  }\n\n  private compareEvents(event1: TimelineEvent, event2: TimelineEvent): ChangePattern | null {\n    const timeDiff = event2.timestamp - event1.timestamp;\n    const complexity1 = event1.complexity?.metrics.cyclomatic ?? 0;\n    const complexity2 = event2.complexity?.metrics.cyclomatic ?? 0;\n    const complexityDiff = complexity2 - complexity1;\n\n    if (Math.abs(complexityDiff) > 5) { // Lower threshold for significance\n      return {\n        type: complexityDiff > 0 ? 'complexity-increase' : 'complexity-decrease',\n        timeframe: timeDiff,\n        magnitude: Math.abs(complexityDiff),\n        frequency: 1,\n      };\n    }\n\n    return null;\n  }\n\n  private async identifyImpactedAreas(dependencies: { direct: string[], indirect: string[] }): Promise<string[]> {\n    const impactedAreas = new Set<string>();\n\n    const allDeps = [...new Set([...dependencies.direct, ...dependencies.indirect])];\n\n    await Promise.all(allDeps.map(async dep => {\n      const result = await this.complexityTool.execute(undefined, { filePath: dep });\n      if (result.success && result.output) {\n        const output = result.output as any;\n        if ((output.cyclomatic?.total ?? 0) > 20 || (output.cognitive?.score ?? 0) > 15) {\n          impactedAreas.add(path.dirname(dep));\n        }\n      }\n    }));\n\n    return Array.from(impactedAreas);\n  }\n\n  private calculateRiskLevel(impact: DependencyImpact): 'low' | 'medium' | 'high' {\n    const totalDeps = impact.directDependencies.length + impact.indirectDependencies.length;\n    const impactedAreasCount = impact.impactedAreas.length;\n\n    if (totalDeps > 20 || impactedAreasCount > 5) {\n      return 'high';\n    } else if (totalDeps > 10 || impactedAreasCount > 2) {\n      return 'medium';\n    }\n    return 'low';\n  }\n\n  private isSignificantChange(impact: DependencyImpact): boolean {\n    return impact.riskLevel === 'high' ||\n      (impact.directDependencies.length + impact.indirectDependencies.length > 5);\n  }\n\n  private assessRisk(analysis: CodeAnalysis): {\n    severity: 'low' | 'medium' | 'high';\n    confidence: number;\n    recommendations: string[];\n  } {\n    const recommendations: string[] = [];\n    const impactedCount = analysis.impactedAreas?.length ?? 0;\n    const changeCount = analysis.relatedChanges?.length ?? 0;\n\n    const risk = {\n      severity: 'low' as 'low' | 'medium' | 'high',\n      confidence: 0.5,\n      recommendations: [] as string[]\n    };\n\n    if (impactedCount > 5 || changeCount > 10) {\n      risk.severity = 'high';\n      risk.confidence = 0.9;\n      risk.recommendations.push(\n        'Consider breaking changes into smaller, focused updates',\n        'Implement additional automated tests for impacted areas'\n      );\n    } else if (impactedCount > 2 || changeCount > 5) {\n      risk.severity = 'medium';\n      risk.confidence = 0.7;\n      risk.recommendations.push('Review changes in impacted areas for potential side effects');\n    }\n\n    if (analysis.originCommit) {\n      risk.recommendations.push(\n        `Focus review on changes from commit ${analysis.originCommit.hash.slice(0, 7)}`\n      );\n    }\n\n    return risk;\n  }\n\n  private async analyzeHistory(context: AgentContext, input: TimeTravelInput): Promise<ToolResult> {\n    return this.analyzeCodeHistory(input.filePath, input.startCommit, input.endCommit);\n  }\n\n  private async trackBug(context: AgentContext, input: TimeTravelInput): Promise<ToolResult> {\n    return this.trackBugOrigin(input.filePath, input.startCommit ?? 'HEAD');\n  }\n\n  private async analyzeDependencies(context: AgentContext, input: TimeTravelInput): Promise<ToolResult> {\n    if (!input.startCommit) {\n      return { success: false, error: 'A commit hash is required to analyze dependencies.', toolId: this.id };\n    }\n    return this.analyzeTimelineDependencies(input.filePath, input.startCommit);\n  }\n\n  private async analyzeChangePatterns(events: TimelineEvent[]): Promise<ChangePattern[]> {\n    const patterns: ChangePattern[] = [];\n    for (let i = 1; i < events.length; i++) {\n      const pattern = this.compareEvents(events[i - 1], events[i]);\n      if (pattern) {\n        patterns.push(pattern);\n      }\n    }\n    return patterns;\n  }\n\n  private identifyPotentialBugIntroductions(patterns: ChangePattern[]): TimelineEvent[] {\n    // Basic implementation: find events linked to high-magnitude complexity increases\n    const bugEvents: TimelineEvent[] = [];\n    // This logic requires access to the events that generated the patterns.\n    // For now, returning an empty array as a placeholder.\n    console.log('Identifying potential bug introductions from patterns:', patterns);\n    return bugEvents;\n  }\n\n  private getFromCache<T>(key: string): T | null {\n    const entry = this.cache.get(key) as CacheEntry<T>;\n\n    if (!entry) {\n      return null;\n    }\n\n    const now = Date.now();\n    if (now - entry.timestamp > CACHE_EXPIRY_MS) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return entry.data;\n  }\n\n  private addToCache<T>(key: string, data: T): void {\n    if (this.cache.size >= this.maxCacheSize) {\n      const oldestKey = this.cache.keys().next().value;\n      if (oldestKey) {\n        this.cache.delete(oldestKey);\n      }\n    }\n    this.cache.set(key, { data, timestamp: Date.now() });\n  }\n}"]}