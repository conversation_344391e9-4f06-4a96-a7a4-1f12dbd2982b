/**
 * Codessa Ask Mode Workflow
 *
 * This module provides workflow templates for Ask Mode:
 * - Question analysis
 * - Context retrieval
 * - Answer generation
 */

import { ITool } from '../../tools/tool.ts.backup';
import { Agent } from '../agentUtilities/agent';
import { <PERSON>ssa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';
import { workflowRegistry } from './workflowRegistry';
import { Logger } from '../../logger';
import { StructuredTool } from 'src/managers';

/**
 * Create an Ask Mode workflow for answering questions with context
 */
export function createAskModeWorkflow(
  id: string,
  name: string,
  description: string,
  qaAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  Logger.instance.info(`Creating Ask Mode workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const questionAnalysisNode = Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);
  const contextRetrievalNode = Codessa.createAgentNode('context-retrieval', 'Context Retrieval', qaAgent);
  const answerGenerationNode = Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);
  const answerRefinementNode = Codessa.createAgentNode('answer-refinement', 'Answer Refinement', qaAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-question', source: 'input', target: 'question-analysis', type: 'default' },
    { name: 'question-to-context', source: 'question-analysis', target: 'context-retrieval', type: 'default' },
    { name: 'context-to-answer', source: 'context-retrieval', target: 'answer-generation', type: 'default' },
    { name: 'answer-to-refinement', source: 'answer-generation', target: 'answer-refinement', type: 'default' },
    { name: 'refinement-to-output', source: 'answer-refinement', target: 'output', type: 'default' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect context retrieval to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `context-to-tool-${index}`,
        source: 'context-retrieval',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to answer generation
      edges.push({
        name: `tool-${index}-to-answer`,
        source: `tool-${index}`,
        target: 'answer-generation',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      questionAnalysisNode,
      contextRetrievalNode,
      answerGenerationNode,
      answerRefinementNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'ask' as OperationMode,
    tags: ['ask', 'question-answering', 'context']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a specialized Code Question workflow for answering code-related questions
 */
export function createCodeQuestionWorkflow(
  id: string,
  name: string,
  description: string,
  qaAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  Logger.instance.info(`Creating Code Question workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const questionAnalysisNode = Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);
  const codebaseSearchNode = Codessa.createAgentNode('codebase-search', 'Codebase Search', qaAgent);
  const codeAnalysisNode = Codessa.createAgentNode('code-analysis', 'Code Analysis', qaAgent);
  const documentationSearchNode = Codessa.createAgentNode('documentation-search', 'Documentation Search', qaAgent);
  const answerGenerationNode = Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);
  const codeExampleGenerationNode = Codessa.createAgentNode('code-example-generation', 'Code Example Generation', qaAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-question', source: 'input', target: 'question-analysis', type: 'default' },
    { name: 'question-to-codebase', source: 'question-analysis', target: 'codebase-search', type: 'default' },
    { name: 'question-to-documentation', source: 'question-analysis', target: 'documentation-search', type: 'default' },
    { name: 'codebase-to-analysis', source: 'codebase-search', target: 'code-analysis', type: 'default' },
    { name: 'analysis-to-answer', source: 'code-analysis', target: 'answer-generation', type: 'default' },
    { name: 'documentation-to-answer', source: 'documentation-search', target: 'answer-generation', type: 'default' },
    { name: 'answer-to-example', source: 'answer-generation', target: 'code-example-generation', type: 'default' },
    { name: 'example-to-output', source: 'code-example-generation', target: 'output', type: 'default' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect code analysis to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `analysis-to-tool-${index}`,
        source: 'code-analysis',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to answer generation
      edges.push({
        name: `tool-${index}-to-answer`,
        source: `tool-${index}`,
        target: 'answer-generation',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      questionAnalysisNode,
      codebaseSearchNode,
      codeAnalysisNode,
      documentationSearchNode,
      answerGenerationNode,
      codeExampleGenerationNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'ask' as OperationMode,
    tags: ['ask', 'code', 'question-answering']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}
