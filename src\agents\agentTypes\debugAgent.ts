import { Agent, AgentContext, AgentRunInput, AgentRunResult } from '../agentUtilities/agent';
import { ITool } from '../../tools/types';
import { logger } from '../../logger';
import { TimeTravelDebuggingTool } from '../../tools/timeTravelDebuggingTool';

export class DebugAgent extends Agent {
  private timeTravelTool: TimeTravelDebuggingTool;

  constructor(_config: any) {
    super({
      ..._config,
      role: 'assistant',
      capabilities: ['debug'],
      llmProvider: 'openai',
      llmModel: 'gpt-4'
    });
    this.timeTravelTool = new TimeTravelDebuggingTool();
  }

  override async run(input: AgentRunInput, context: AgentContext = {}): Promise<AgentRunResult> {
    logger.info(`DebugAgent processing: "${input.prompt.substring(0, 50)}..."`);

    // --- Gather relevant workflow for debug mode ---
    let workflow: any = undefined;
    if (context.variables?.workflowManager) {
      workflow = await context.variables.workflowManager.getWorkflowForMode('debug');
    }

    // --- Gather all relevant tools ---
    let tools = this.tools;
    if (context.tools) {
      tools = context.tools as Map<string, ITool>;
    }

    // --- Gather memory and knowledgebase context ---
    let memoryContext = '';
    if (this.getMemory) {
      const memories = await this.getMemory().getRelevantMemories(input.prompt);
      if (memories?.length) {
        memoryContext = this.getMemory().formatMemoriesForPrompt(memories);
      }
    }
    let kbContext = '';
    if (context.variables?.knowledgebaseManager) {
      kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input.prompt);
    }

    // --- Gather MCP context ---
    let mcpContext = '';
    if (context.variables?.mcpManager) {
      mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext() || {});
    }

    // --- Use external prompt if available ---
    let externalPrompt = '';
    if (context.variables?.promptManager) {
      externalPrompt = context.variables.promptManager.getSystemPrompt('debugAgent', context.variables) || '';
    }

    // Phase 2: Enhanced debugging with time-travel capabilities (Optimized)
    const shouldUseTimeTravel = this.shouldUseTimeTravelDebugging(input.prompt);
    let timeTravelContext = '';

    if (shouldUseTimeTravel && context.workspace?.currentFile) {
      try {
        const filePath = typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile;

        // Validate file path before proceeding
        if (!filePath || filePath.trim().length === 0) {
          logger.warn('Invalid file path for time-travel debugging');
          timeTravelContext = '\n## Time-Travel Analysis: Invalid file path';
        } else {
          // Create timeline for the current file with timeout
          const timelinePromise = this.timeTravelTool.execute('createTimeline', {
            filePath,
            maxEvents: 10
          });

          // Add timeout to prevent hanging
          const timelineResult = await Promise.race([
            timelinePromise,
            new Promise<never>((_, reject) =>
              setTimeout(() => reject(new Error('Timeline creation timeout')), 30000)
            )
          ]);

          if (timelineResult.success && timelineResult.output) {
            // Limit the size of timeline data to prevent overwhelming the prompt
            const timeline = timelineResult.output;
            const limitedTimeline = {
              ...timeline,
              events: timeline.events?.slice(0, 5) || [] // Limit to 5 most recent events
            };
            timeTravelContext = `\n## Time-Travel Analysis:\n${JSON.stringify(limitedTimeline, null, 2)}`;

            // Try to identify bug origin if this looks like a bug report
            const isBugReport = input.prompt.toLowerCase().includes('bug') ||
              input.prompt.toLowerCase().includes('error') ||
              input.prompt.toLowerCase().includes('issue');

            if (isBugReport) {
              try {
                const bugOriginPromise = this.timeTravelTool.execute('identifyBugOrigin', {
                  filePath,
                  bugDescription: input.prompt.substring(0, 500), // Limit description length
                  maxCommits: 5 // Reduced for performance
                });

                const bugOriginResult = await Promise.race([
                  bugOriginPromise,
                  new Promise<never>((_, reject) =>
                    setTimeout(() => reject(new Error('Bug origin analysis timeout')), 20000)
                  )
                ]);

                if (bugOriginResult.success && bugOriginResult.output) {
                  const bugData = bugOriginResult.output;
                  // Store bug analysis in context for reuse
                  context.debugData = {
                    ...context.debugData,
                    bugOrigin: bugData,
                    analysisTimestamp: Date.now()
                  };
                  timeTravelContext += `\n## Bug Origin Analysis:\n${JSON.stringify(bugData, null, 2)}`;
                }
              } catch (bugError) {
                const errorDetails = bugError instanceof Error ? bugError.stack : String(bugError);
                logger.warn(`Bug origin analysis failed: ${errorDetails}`);
                context.debugData = {
                  ...context.debugData,
                  lastError: {
                    message: String(bugError),
                    timestamp: Date.now()
                  }
                };
                timeTravelContext += '\n## Bug Origin Analysis: Analysis failed or timed out';
              }
            }
          } else {
            timeTravelContext = '\n## Time-Travel Analysis: Timeline creation failed';
          }
        }
      } catch (error) {
        logger.warn(`Time-travel debugging failed: ${error}`);
        timeTravelContext = '\n## Time-Travel Analysis: Not available due to error';
      }
    }

    // --- Compose final prompt ---
    const debugPrompt = `
# Enhanced Debug Mode Processing with Time-Travel Capabilities

${externalPrompt}

## User Problem:
${input.prompt}

## Memory Context:
${memoryContext}

## Knowledgebase Context:
${kbContext}

## MCP Context:
${mcpContext}

## Workflow:
${workflow ? JSON.stringify(workflow) : 'None'}

## Code Context:
${context.workspace?.currentFile ? (typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile) : 'No code context available'}

${timeTravelContext}

## Your Enhanced Debugging Task:
1. Analyze the problem using all available context including time-travel data
2. Identify the root cause of the issue (use timeline analysis if available)
3. Explain the problem clearly with historical context
4. Provide a specific solution with confidence level
5. Suggest preventive measures based on historical patterns

## Available Advanced Tools:
- Time-travel debugging for historical analysis
- Code timeline analysis
- Bug origin identification
- Dependency impact analysis

Use all available workflows, tools, memory, knowledgebase, and time-travel context. Stream output if possible.
`;
    input.prompt = debugPrompt;
    // Pass all gathered context and tools to super.run
    return super.run(input, { ...context, tools });
  }

  /**
     * Determine if time-travel debugging should be used
     */
  private shouldUseTimeTravelDebugging(prompt: string): boolean {
    const timeTravelIndicators = [
      'when did this break',
      'what changed',
      'regression',
      'worked before',
      'used to work',
      'history',
      'timeline',
      'previous version',
      'git',
      'commit',
      'blame'
    ];

    const promptLower = prompt.toLowerCase();
    return timeTravelIndicators.some(indicator => promptLower.includes(indicator));
  }
}