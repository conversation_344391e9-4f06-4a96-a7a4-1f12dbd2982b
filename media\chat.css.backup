/* Header and Agent Selector */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: var(--vscode-panel-background);
    border-bottom: 1px solid var(--vscode-panel-border);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-header h1 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--vscode-foreground);
}

.agent-selector select {
    background-color: var(--vscode-dropdown-background);
    color: var(--vscode-dropdown-foreground);
    border: 1px solid var(--vscode-dropdown-border);
    border-radius: 2px;
    padding: 2px 20px 2px 6px;
    font-size: 12px;
    line-height: 16px;
    height: 24px;
    min-width: 120px;
    max-width: 200px;
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M4.5%207L8%2010.5L11.5%207H4.5Z%22%20fill%3D%22%23CCCCCC%22%2F%3E%3C%2Fsvg%3E");
    background-repeat: no-repeat;
    background-position: right 4px center;
    background-size: 16px;
}

.agent-selector select:focus {
    outline: 1px solid var(--vscode-focusBorder);
    outline-offset: -1px;
}

.agent-selector select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Thinking Message Styles */
.message.thinking {
    opacity: 0.8;
}

.message.thinking .content {
    min-height: 24px;
    display: flex;
    align-items: center;
}

.thinking-dots {
    display: flex;
    align-items: center;
    gap: 4px;
}

.thinking-dots span {
    width: 4px;
    height: 4px;
    background-color: currentColor;
    border-radius: 50%;
    animation: thinkingDot 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) {
    animation-delay: 0s;
}

.thinking-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes thinkingDot {
    0%, 80%, 100% {
        transform: scale(0.6);
        opacity: 0.6;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Agent-specific message styling */
.message[data-agent]::before {
    content: attr(data-agent);
    display: block;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--vscode-descriptionForeground);
    margin-bottom: 2px;
}