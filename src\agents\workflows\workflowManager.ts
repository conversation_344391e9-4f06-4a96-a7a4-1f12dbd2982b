import { GraphDefinition } from './graphTypes';
import { workflowRegistry } from './workflowRegistry';
import { IWorkflowManager } from '../../managers';
import { logger } from '../../logger';

/**
 * Workflow Manager for handling workflow operations
 */
export class WorkflowManager implements IWorkflowManager {
  public get logger() {
    return logger;
  }
  private static instance: WorkflowManager;

  private constructor() { }

  /**
   * Get the singleton instance
   */
  public static getInstance(): WorkflowManager {
    if (!WorkflowManager.instance) {
      WorkflowManager.instance = new WorkflowManager();
    }
    return WorkflowManager.instance;
  }

  /**
   * Get a workflow for a specific mode
   */
  public async getWorkflowForMode(mode: string): Promise<GraphDefinition | undefined> {
    logger.info(`Getting workflow for mode: ${mode}`);

    // Get workflows with the specified mode tag
    const workflows = workflowRegistry.getWorkflowsByTag(mode);

    if (workflows.length > 0) {
      // Return the first matching workflow
      return workflows[0];
    }

    // If no specific workflow found, try to get a default workflow
    const defaultWorkflows = workflowRegistry.getWorkflowsByTag('default');

    if (defaultWorkflows.length > 0) {
      return defaultWorkflows[0];
    }

    return undefined;
  }

  /**
   * Get all available workflows
   */
  public async getAllWorkflows(): Promise<GraphDefinition[]> {
    return workflowRegistry.getAllWorkflows();
  }

  /**
   * Get workflows by tag
   */
  public async getWorkflowsByTag(tag: string): Promise<GraphDefinition[]> {
    return workflowRegistry.getWorkflowsByTag(tag);
  }
}

// Export singleton instance
export const workflowManager = WorkflowManager.getInstance();