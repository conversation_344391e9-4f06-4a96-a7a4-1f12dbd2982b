{"version": 3, "file": "researchMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/researchMode.ts"], "names": [], "mappings": ";;;AACA,mDAA4E;AAG5E,yCAAsC;AACtC,qDAAkD;AAElD;;GAEG;AACH,MAAa,YAAa,SAAQ,6BAAa;IACpC,EAAE,GAAG,UAAU,CAAC;IAChB,WAAW,GAAG,UAAU,CAAC;IACzB,WAAW,GAAG,+CAA+C,CAAC;IAC9D,IAAI,GAAG,WAAW,CAAC;IACnB,kBAAkB,GAAG,2BAAW,CAAC,eAAe,CAAC;IACjD,yBAAyB,GAAG,KAAK,CAAC;IAClC,sBAAsB,GAAG,IAAI,CAAC;IAEvC;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY,EACZ,aAA4B,EAC5B,gBAAsC;QAEtC,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,OAAO,EAAE,CAAC,CAAC;YAExE,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE7E,kCAAkC;YAClC,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;wBACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,wCAAwC,CAAC,CAAC;oBAClG,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,CAAC;gBACrF,kCAAkC;YACpC,CAAC;YAED,8BAA8B;YAC9B,MAAM,MAAM,GAAG;;;kBAGH,OAAO;;;EAGvB,cAAc;;EAEd,aAAa;;;;;;;;;;CAUd,CAAC;YAEI,oCAAoC;YACpC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAEnE,uCAAuC;YACvC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,mBAAmB,OAAO,EAAE,CAAC,CAAC;oBACnE,MAAM,WAAW,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,EAAE,WAAW,CAAC,CAAC;gBACjF,qCAAqC;YACvC,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YAC3E,OAAO,2CAA2C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7G,CAAC;IACH,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG,EAAE,8CAA8C;YAChE,SAAS,EAAE,IAAI,EAAG,iDAAiD;YACnE,aAAa,EAAE,EAAE;YACjB,IAAI,EAAE,UAAU;SACjB,CAAC;IACJ,CAAC;CACF;AAhGD,oCAgGC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { contextManager } from './contextManager';\n\n/**\n * Research Mode - Comprehensive research and analysis\n */\nexport class ResearchMode extends OperationMode {\n  readonly id = 'research';\n  readonly displayName = 'Research';\n  readonly description = 'Comprehensive research and analysis of topics';\n  readonly icon = '$(search)';\n  readonly defaultContextType = ContextType.ENTIRE_CODEBASE;\n  readonly requiresHumanVerification = false;\n  readonly supportsMultipleAgents = true;\n\n  /**\n     * Process a user message in Research mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    contextSource: ContextSource,\n    additionalParams?: Record<string, any>\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Research mode: ${message}`);\n\n      // Get context content\n      const contextContent = await contextManager.getContextContent(contextSource);\n\n      // Add memory context if available\n      let memoryContext = '';\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          const relevantMemories = await agentMemory.getRelevantMemories(message);\n          if (relevantMemories && relevantMemories.length > 0) {\n            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to research context`);\n          }\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to retrieve memory context for research:', memoryError);\n        // Continue without memory context\n      }\n\n      // Prepare the research prompt\n      const prompt = `\nYou are a research assistant conducting comprehensive analysis.\n\nResearch Topic: ${message}\n\nAvailable Context:\n${contextContent}\n\n${memoryContext}\n\nPlease provide:\n1. Comprehensive analysis of the topic\n2. Key findings and insights\n3. Relevant code patterns or implementations\n4. Best practices and recommendations\n5. Additional resources or references\n\nFocus on providing thorough, well-researched information with practical insights.\n`;\n\n      // Generate response using the agent\n      const response = await agent.generate(prompt, this.getLLMParams());\n\n      // Store the research session in memory\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          await agentMemory.addMessage('user', `Research topic: ${message}`);\n          await agentMemory.addMessage('assistant', response);\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to store research session in memory:', memoryError);\n        // Continue without storing in memory\n      }\n\n      return response;\n    } catch (error) {\n      Logger.instance.error('Error processing message in Research mode:', error);\n      return `Error processing your research request: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Get LLM parameters specific to Research mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.3, // Lower temperature for more focused research\n      maxTokens: 2000,  // Higher token limit for comprehensive responses\n      stopSequences: [],\n      mode: 'generate'\n    };\n  }\n}\n"]}