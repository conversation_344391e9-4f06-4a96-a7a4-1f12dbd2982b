#!/usr/bin/env node

/**
 * Post-compilation script to handle ES module vs CommonJS compatibility
 * This script renames extension.js to extension.cjs so it's treated as CommonJS
 * while keeping other files as ES modules when needed
 */

const fs = require('fs');
const path = require('path');

const outDir = path.join(__dirname, '..', 'out');
const extensionJs = path.join(outDir, 'extension.js');
const extensionCjs = path.join(outDir, 'extension.cjs');
const extensionJsMap = path.join(outDir, 'extension.js.map');
const extensionCjsMap = path.join(outDir, 'extension.cjs.map');

console.log('Running post-compile script...');

try {
  // Check if extension.js exists
  if (fs.existsSync(extensionJs)) {
    console.log('Renaming extension.js to extension.cjs...');
    
    // Rename extension.js to extension.cjs
    fs.renameSync(extensionJs, extensionCjs);
    
    // Rename source map if it exists
    if (fs.existsSync(extensionJsMap)) {
      fs.renameSync(extensionJsMap, extensionCjsMap);
      
      // Update source map reference in the .cjs file
      const cjsContent = fs.readFileSync(extensionCjs, 'utf8');
      const updatedContent = cjsContent.replace(
        '//# sourceMappingURL=extension.js.map',
        '//# sourceMappingURL=extension.cjs.map'
      );
      fs.writeFileSync(extensionCjs, updatedContent);
      
      console.log('Updated source map reference in extension.cjs');
    }
    
    console.log('✅ Successfully renamed extension files for CommonJS compatibility');
  } else {
    console.log('⚠️  extension.js not found, skipping rename');
  }
} catch (error) {
  console.error('❌ Error in post-compile script:', error);
  process.exit(1);
}

console.log('Post-compile script completed successfully');
