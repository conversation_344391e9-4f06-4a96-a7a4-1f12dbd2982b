"use strict";
/**
 * AI Agent Tool for comprehensive Linting and Diagnostics management in VS Code.
 * Provides actions to list, filter, fix, run lint tasks, batch process,
 * summarize, and search through diagnostics across the workspace.
 * Implemented as a multi-action tool coordinating VS Code APIs.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.lintDiagnosticsTool = exports.LintDiagnosticsTool = void 0;
exports.createLintDiagnosticsTool = createLintDiagnosticsTool;
const vscode = __importStar(require("vscode"));
const terminalCommandTool_1 = require("./terminalCommandTool"); // Assuming AgentContext is defined here
const zod_1 = require("zod"); // Zod for schema validation
// Assume these are available paths in the project
const logger_1 = require("../logger"); // Assuming logging utility is available
const config_1 = require("../config"); // Assuming configuration utility is available
const nodePath = __importStar(require("path")); // Node.js path module
// Use the imported schemas
const ListDiagnosticsInputSchema = zod_1.z.object({
    /** Optional file path or glob pattern to filter diagnostics by file. */
    filePath: zod_1.z.string().optional().describe('Optional file path or glob pattern to filter diagnostics by file.'),
    /** Optional filter by diagnostic severity. Can be a single severity or array of severities. */
    severity: zod_1.z.union([zod_1.z.enum(['error', 'warning', 'info', 'hint']), zod_1.z.enum(['error', 'warning', 'info', 'hint']).array()]).optional().describe('Optional filter by severity.'),
    /** Optional filter by diagnostic code. */
    code: zod_1.z.string().optional().describe('Optional filter by diagnostic code.'),
    /** Optional filter by source. */
    source: zod_1.z.string().optional().describe('Optional filter by diagnostic source.'),
    /** Optional maximum number of diagnostics to return per file. */
    maxPerFile: zod_1.z.number().int().positive().optional().describe('Optional maximum number of diagnostics per file.'),
    /** Optional maximum total number of diagnostics to return across all files. */
    limit: zod_1.z.number().int().positive().optional().describe('Optional maximum total number of diagnostics to return across all files.'),
}).describe('Input for listing diagnostics.');
const FixDiagnosticInputSchema = zod_1.z.object({
    /** Required file path (absolute or relative to workspace) of the diagnostic. */
    filePath: zod_1.z.string().min(1).describe('Required file path (absolute or relative to workspace root) of the diagnostic.'),
    /** Required zero-based index of the diagnostic within the list for the specified file (obtained from "list" action). */
    diagnosticIndex: zod_1.z.number().int().nonnegative().describe('Required zero-based index of the diagnostic to fix.'),
    /** Optional index of the code action to apply if multiple quick fixes are available for the diagnostic. Defaults to 0 (first available). */
    codeActionIndex: zod_1.z.number().int().nonnegative().optional().default(0).describe('Optional index of the quick fix action to apply.'),
}).describe('Input for applying a quick fix to a specific diagnostic.');
const RunLintTaskInputSchema = zod_1.z.object({
    /** Optional name of the specific lint task to run. Defaults to the workspace's default build task if omitted, or attempts a common "lint" task. */
    taskName: zod_1.z.string().optional().describe('Optional name of the specific lint task to run. Defaults to the workspace\'s default build task or attempts a "lint" task.'),
    /** Optional wait for the task to complete before returning. Defaults to false. */
    wait: zod_1.z.boolean().optional().default(false).describe('If true, wait for the task to complete before returning.'),
}).describe('Input for running a configured lint task.');
const ApplyCodeActionsInputSchema = zod_1.z.object({
    /** Required file path (absolute or relative to workspace) where code actions should be applied. */
    filePath: zod_1.z.string().min(1).describe('Required file path (absolute or relative to workspace root).'),
    /** Optional filter by diagnostic severity for actions. */
    severity: zod_1.z.union([zod_1.z.enum(['error', 'warning', 'info', 'hint']), zod_1.z.enum(['error', 'warning', 'info', 'hint']).array()]).optional().describe('Optional filter by severity for actions.'),
    /** Optional filter by diagnostic code for actions. */
    code: zod_1.z.union([zod_1.z.string(), zod_1.z.string().array()]).optional().describe('Optional filter by diagnostic code for actions.'),
    /** Optional filter by source for actions. */
    source: zod_1.z.union([zod_1.z.string(), zod_1.z.string().array()]).optional().describe('Optional filter by source for actions.'),
    /** Optional flag to apply only "fix all" actions if available and matching filters. */
    applyFixAll: zod_1.z.boolean().optional().default(false).describe('If true, attempt to apply "fix all" actions if available for the file and filters.'),
    /** Optional range to limit where code actions are sought (e.g., for fixing a specific section). */
    range: zod_1.z.object({
        start: zod_1.z.object({ line: zod_1.z.number().int().nonnegative(), character: zod_1.z.number().int().nonnegative() }),
        end: zod_1.z.object({ line: zod_1.z.number().int().nonnegative(), character: zod_1.z.number().int().nonnegative() }),
    }).optional().describe('Optional range within the file to apply code actions (0-based).'),
}).describe('Input for applying available code actions (fixes) to a file.');
const SummarizeDiagnosticsInputSchema = zod_1.z.object({
    /** Optional file path to summarize diagnostics for. If omitted, summarizes for the entire workspace (all open documents). */
    filePath: zod_1.z.string().optional().describe('Optional file path to summarize diagnostics for. If omitted, summarizes for all open documents.'),
    /** Optional filter by diagnostic severity for the summary. */
    severity: zod_1.z.union([zod_1.z.enum(['error', 'warning', 'info', 'hint']), zod_1.z.enum(['error', 'warning', 'info', 'hint']).array()]).optional().describe('Optional filter by severity for the summary.'),
    /** Optional filter by source for the summary. */
    source: zod_1.z.union([zod_1.z.string(), zod_1.z.string().array()]).optional().describe('Optional filter by source for the summary.'),
    /** Optional limit on the number of top issues to highlight in the summary. */
    topIssuesLimit: zod_1.z.number().int().positive().optional().describe('Optional limit on the number of top diagnostic messages/codes to highlight.'),
}).describe('Input for summarizing diagnostics.');
const SearchDiagnosticsInputSchema = zod_1.z.object({
    /** The search query string to find matching diagnostics. Searches within message, code, and source. */
    query: zod_1.z.string().min(1).describe('The search query string to find matching diagnostics.'),
    /** Optional file path to limit the search to. If omitted, searches all open documents. */
    filePath: zod_1.z.string().optional().describe('Optional file path to limit the search to.'),
    /** Optional filter by diagnostic severity for the search. */
    severity: zod_1.z.union([zod_1.z.enum(['error', 'warning', 'info', 'hint']), zod_1.z.enum(['error', 'warning', 'info', 'hint']).array()]).optional().describe('Optional filter by severity.'),
    /** Optional filter by source for the search. */
    source: zod_1.z.union([zod_1.z.string(), zod_1.z.string().array()]).optional().describe('Optional filter by source.'),
    /** Optional limit on the number of search results. */
    limit: zod_1.z.number().int().positive().optional().describe('Optional maximum number of search results.'),
}).describe('Input for searching diagnostics.');
// Schema for tool-specific configuration updates (example)
const UpdateLintToolSettingsInputSchema = zod_1.z.object({
    // Define properties matching the config structure this tool manages
    maxOutputBufferBytes: zod_1.z.number().int().positive().optional(),
    defaultTimeoutMs: zod_1.z.number().int().positive().optional(),
    defaultShell: zod_1.z.string().optional(),
    allowedCommands: zod_1.z.string().array().optional().nullable(), // Allow setting to null to clear
    forbiddenCommands: zod_1.z.string().array().optional().nullable(), // Allow setting to null to clear
    useExecForSimpleCommands: zod_1.z.boolean().optional(),
    // Add other relevant settings here
}).partial().describe('Partial settings for the LintDiagnosticsTool.');
/**
 * Helper function to convert Zod enum or string union input to a string array.
 * @param input The input from the Zod schema.
 * @returns A string array or undefined.
 */
function normalizeStringOrStringArrayInput(input) {
    if (input === undefined)
        return undefined;
    if (Array.isArray(input))
        return input;
    return [input];
}
/**
 * Implements the ITool interface for comprehensive Linting and Diagnostics management.
 * This is a multi-action tool coordinating operations via VS Code APIs.
 */
class LintDiagnosticsTool {
    id = 'lint_diagnostics'; // Unique ID
    name = 'lint_diagnostics'; // Machine-friendly name
    description = 'Provides actions to manage code diagnostics (linting/compiler issues): list, filter, apply fixes, run tasks, summarize, search, and update tool settings.';
    category = 'Development'; // Tool category
    type = 'multi-action'; // Multi-action tool
    actions;
    /**
       * Constructs a new LintDiagnosticsTool instance.
       * @param context Optional AgentContext (not strictly needed for this tool's current implementation).
       */
    constructor(context) {
        // Define the available actions using Zod schemas
        this.actions = {
            'list': {
                description: 'Lists diagnostics (errors, warnings, infos, hints) for a specific file or all currently open files, optionally filtered by severity, code, source, message substring, and limited.',
                inputSchema: ListDiagnosticsInputSchema,
            },
            'fix': {
                description: 'Applies the default quick fix to a specific diagnostic identified by file path and its index from a previous list action.',
                inputSchema: FixDiagnosticInputSchema,
            },
            'apply_code_actions': {
                description: 'Applies available quick fixes or other code actions to diagnostics in a file or a specific range, optionally filtered by severity, code, and source. Can apply "fix all" actions if requested.',
                inputSchema: ApplyCodeActionsInputSchema,
            },
            'run_task': {
                description: 'Runs a configured VS Code task, typically used for triggering linters or build processes.',
                inputSchema: RunLintTaskInputSchema,
            },
            'summarize': {
                description: 'Generates a summary report of diagnostics for a file or the entire workspace (all open documents), optionally filtered by severity and source, and highlighting top issues.',
                inputSchema: SummarizeDiagnosticsInputSchema,
            },
            'search': {
                description: 'Searches diagnostics across open documents based on a query string within the message, code, or source, optionally filtered by severity, source, and limited.',
                inputSchema: SearchDiagnosticsInputSchema,
            },
            'get_settings': {
                description: 'Retrieves the current configuration settings for the LintDiagnosticsTool.',
                inputSchema: zod_1.z.object({}).describe('No parameters required.'), // Empty schema for get_settings
            },
            'update_settings': {
                description: 'Updates configuration settings for the LintDiagnosticsTool. Requires specific input parameters corresponding to setting keys.',
                inputSchema: UpdateLintToolSettingsInputSchema,
            },
        };
        logger_1.Logger.instance.info('LintDiagnosticsTool initialized.');
    }
    /**
       * Provides the structured definition for the LLM.
       * Generates JSON schema definitions for each action.
       * Conforms to ITool.getDefinitionForModel.
       * @returns An array of JSON schema definitions suitable for LLM tool calling.
       */
    getDefinitionForModel() {
        // For a multi-action tool, the LLM typically sees each action as a distinct callable function.
        // The names should be unique across all tools/actions exposed to the LLM.
        // A common pattern is "tool_name__action_name".
        return Object.entries(this.actions).map(([actionName, actionDef]) => {
            // Generate JSON schema for the action's input schema using Zod's toJSON()
            // Use casting for robustness against potential Zod variations
            const schemaObject = actionDef.inputSchema.toJSON ? actionDef.inputSchema.toJSON() : undefined;
            if (!schemaObject) {
                logger_1.Logger.instance.error(`LintDiagnosticsTool getDefinitionForModel: Failed to generate JSON schema for action '${actionName}'.`);
                // Return a minimal definition if schema generation fails
                return {
                    name: `${this.name}__${actionName}`,
                    description: actionDef.description || `Performs the ${actionName} action.`,
                    parameters: { type: 'object', properties: {} }, // Empty params if schema fails
                };
            }
            // Apply specific schema adjustments for better LLM compatibility if needed
            // Example: For 'severity' or 'code' which can be string or string[], clarify to LLM
            if (schemaObject.properties?.severity) {
                schemaObject.properties.severity = {
                    type: 'array', // Represent as array for simplicity for LLM
                    items: { type: 'string', enum: ['error', 'warning', 'info', 'hint'] },
                    description: schemaObject.properties.severity.description,
                    uniqueItems: true,
                };
            }
            if (schemaObject.properties?.code) {
                schemaObject.properties.code = {
                    type: 'array', // Represent as array for simplicity for LLM
                    items: { type: 'string' },
                    description: schemaObject.properties.code.description,
                    uniqueItems: true,
                };
            }
            if (schemaObject.properties?.source) {
                schemaObject.properties.source = {
                    type: 'array', // Represent as array for simplicity for LLM
                    items: { type: 'string' },
                    description: schemaObject.properties.source.description,
                    uniqueItems: true,
                };
            }
            if (schemaObject.properties?.range) {
                // Adjust the range representation for LLM clarity
                schemaObject.properties.range = {
                    type: 'object',
                    description: schemaObject.properties.range.description || 'A range with start and end positions (0-based line and character).',
                    properties: {
                        start: { type: 'object', properties: { line: { type: 'integer' }, character: { type: 'integer' } }, required: ['line', 'character'], description: 'Start position.' },
                        end: { type: 'object', properties: { line: { type: 'integer' }, character: { type: 'integer' } }, required: ['line', 'character'], description: 'End position.' },
                    },
                    required: ['start', 'end'],
                };
            }
            return {
                // Action name format for the LLM
                name: `${this.name}__${actionName}`,
                description: actionDef.description,
                parameters: schemaObject, // The generated JSON schema
            };
        });
    }
    /**
       * Executes the specified linting or diagnostics action.
       * The calling framework is responsible for parsing the LLM's tool call,
       * extracting the action name and parameters, and validating parameters
       * against the specific action's schema before calling this method.
       *
       * @param actionName - The name of the action to execute (e.g., 'list', 'fix').
       * @param input - The validated input parameters for the action.
       * @param context - Optional agent context.
       * @returns A promise resolving to the tool's result.
       */
    async execute(actionName, input, context) {
        if (!actionName) {
            const errorMsg = 'LintDiagnosticsTool requires an actionName for multi-action execution.';
            logger_1.Logger.instance.error(errorMsg, { input });
            return { success: false, error: errorMsg, toolId: this.id, actionName };
        }
        const actionDefinition = this.actions[actionName];
        if (!actionDefinition) {
            const errorMsg = `LintDiagnosticsTool received unknown actionName: ${actionName}. Available actions: ${Object.keys(this.actions).join(', ')}.`;
            logger_1.Logger.instance.error(errorMsg, { input });
            return { success: false, error: errorMsg, toolId: this.id, actionName };
        }
        // Re-validate input here for robustness, although framework should do it.
        const parsedInput = actionDefinition.inputSchema.safeParse(input);
        if (!parsedInput.success) {
            const formattedError = parsedInput.error.format();
            const errorMsg = `Invalid input for action '${actionName}': ${Object.entries(formattedError)
                .filter(([key]) => key !== '_errors')
                .map(([key, value]) => `${key}: ${value._errors.join(', ')}`)
                .join('; ')}`;
            logger_1.Logger.instance.error(errorMsg, { input, validationErrors: formattedError });
            return { success: false, error: errorMsg, toolId: this.id, actionName };
        }
        const validatedInput = parsedInput.data;
        logger_1.Logger.instance.info(`Executing LintDiagnosticsTool action: "${actionName}"`);
        logger_1.Logger.instance.debug(`Input for action "${actionName}":`, validatedInput); // Log validated input
        try {
            let resultOutput; // Output of the specific action
            let successStatus = true; // Default success
            // --- Execute the specific action logic ---
            switch (actionName) {
                case 'list':
                    const listInput = validatedInput;
                    const diagnostics = await this.listDiagnostics(listInput);
                    resultOutput = diagnostics; // Return the list of diagnostics
                    logger_1.Logger.instance.info(`Action 'list' completed. Found ${diagnostics.length} diagnostics.`);
                    break;
                case 'fix':
                    const fixInput = validatedInput;
                    // Call helper to apply a single quick fix by index
                    const fixResult = await this.applySpecificDiagnosticFix(fixInput);
                    resultOutput = fixResult.output;
                    successStatus = fixResult.success;
                    if (fixResult.error)
                        logger_1.Logger.instance.error(`Action 'fix' failed: ${fixResult.error}`);
                    break;
                case 'apply_code_actions':
                    const applyInput = validatedInput;
                    // Call helper to apply multiple code actions based on filters/range
                    const applyResult = await this.applyCodeActions(applyInput);
                    resultOutput = applyResult.output;
                    successStatus = applyResult.success;
                    if (applyResult.error)
                        logger_1.Logger.instance.error(`Action 'apply_code_actions' failed: ${applyResult.error}`);
                    break;
                case 'run_task':
                    const runTaskInput = validatedInput;
                    // Execute VS Code command to run a task
                    const taskName = runTaskInput.taskName;
                    const taskCommand = taskName ? 'workbench.action.tasks.runTask' : 'workbench.action.tasks.runDefaultBuildTask'; // Often lint is the default build task
                    const taskArgs = taskName ? [taskName] : [];
                    const wait = runTaskInput.wait ?? false;
                    try {
                        // Find the task first if a name is given
                        let taskToRun;
                        if (taskName) {
                            const tasks = await vscode.tasks.fetchTasks();
                            taskToRun = tasks.find(t => t.name === taskName && t.source !== 'Workspace'); // Find non-workspace tasks first
                            if (!taskToRun && vscode.workspace.name) {
                                // Also look for tasks defined in workspace (tasks.json)
                                taskToRun = tasks.find(t => t.name === taskName && t.source === `Workspace (${vscode.workspace.name})`);
                            }
                            if (!taskToRun) {
                                throw new Error(`Task '${taskName}' not found.`);
                            }
                        }
                        else {
                            // Attempt to get default build task if no name given
                            taskToRun = await vscode.tasks.fetchTasks({ type: 'shell' }).then(tasks => tasks.find(t => t.group === vscode.TaskGroup.Build));
                            if (!taskToRun) {
                                // Fallback to attempt running a task named 'lint' or 'build'
                                taskToRun = (await vscode.tasks.fetchTasks()).find(t => t.name === 'lint' || t.name === 'build');
                            }
                            if (!taskToRun) {
                                throw new Error('No specific task name provided and couldn\'t find default build task or task named \'lint\'/\'build\'.');
                            }
                            logger_1.Logger.instance.info(`No task name specified. Attempting to run default task: ${taskToRun.name}`);
                        }
                        const execution = await vscode.tasks.executeTask(taskToRun);
                        if (wait) {
                            logger_1.Logger.instance.info(`Waiting for task '${taskToRun.name}' to complete...`);
                            // Wait for the task to terminate
                            await new Promise((resolve) => {
                                const disposable = vscode.tasks.onDidEndTaskProcess(e => {
                                    if (e.execution === execution) {
                                        logger_1.Logger.instance.info(`Task '${taskToRun.name}' finished with exit code ${e.exitCode}.`);
                                        disposable.dispose();
                                        resolve();
                                    }
                                });
                                // Add a timeout in case the task never ends or onDidEndTaskProcess doesn't fire as expected
                                const waitTimeout = setTimeout(() => {
                                    logger_1.Logger.instance.warn(`Wait for task '${taskToRun.name}' timed out after ${(0, config_1.getConfig)('tools.taskWaitTimeout', 30000)}ms.`); // Configurable timeout
                                    disposable.dispose();
                                    resolve(); // Resolve even on timeout
                                }, (0, config_1.getConfig)('tools.taskWaitTimeout', 30000));
                            });
                        }
                        resultOutput = `VS Code task '${taskToRun.name}' triggered${wait ? ' and waited for completion' : ''}. Check VS Code terminal for output.`;
                        logger_1.Logger.instance.info(resultOutput);
                    }
                    catch (error) {
                        successStatus = false;
                        resultOutput = `Failed to trigger VS Code task '${taskName || 'default'}': ${error.message || error}`;
                        logger_1.Logger.instance.error(resultOutput, error);
                    }
                    break;
                case 'summarize':
                    const summarizeInput = validatedInput;
                    resultOutput = await this.summarizeDiagnostics(summarizeInput);
                    successStatus = true; // Summary generation is generally successful
                    logger_1.Logger.instance.info('Action \'summarize\' completed.');
                    break;
                case 'search':
                    const searchInput = validatedInput;
                    resultOutput = await this.searchDiagnostics(searchInput);
                    logger_1.Logger.instance.info(`Action 'search' completed. Found ${resultOutput.length} matches.`);
                    break;
                case 'get_settings':
                    const toolSettings = this.getLintToolSettings();
                    resultOutput = toolSettings;
                    successStatus = true;
                    logger_1.Logger.instance.info('Action \'get_settings\' completed.');
                    break;
                case 'update_settings':
                    const updateSettingsInput = validatedInput;
                    // Call helper to update settings
                    // This helper should handle persistence (e.g., writing to VS Code settings)
                    await this.updateLintToolSettings(updateSettingsInput);
                    resultOutput = { status: 'settings updated', updated: Object.keys(updateSettingsInput) };
                    successStatus = true;
                    logger_1.Logger.instance.info('Action \'update_settings\' completed.');
                    break;
                default:
                    // This case should ideally not be reached due to Zod validation of actionName prior to switch
                    const defensiveUnknownActionError = `Defensive check failed: Unknown action ${actionName}`;
                    logger_1.Logger.instance.error(defensiveUnknownActionError);
                    return { success: false, error: defensiveUnknownActionError, toolId: this.id, actionName };
            }
            // Return final result
            return {
                success: successStatus,
                output: resultOutput,
                toolId: this.id,
                actionName: actionName,
                // Add usage/metadata if available
            };
        }
        catch (error) {
            // Catch any unhandled errors during action execution
            const errorMessage = `Error during LintDiagnosticsTool action "${actionName}": ${error.message || error}`;
            logger_1.Logger.instance.error(errorMessage, error);
            return { success: false, error: errorMessage, toolId: this.id, actionName };
        }
    }
    // --- Helper methods for action logic ---
    /**
       * Helper to resolve a file path string to a VS Code Uri.
       * Handles absolute paths and paths relative to the first workspace folder.
       * @param filePath The input file path string.
       * @returns The corresponding VS Code Uri.
       * @throws Error if no workspace folder is open and the path is relative, or if path is invalid.
       */
    resolveFilePathToUri(filePath) {
        if (nodePath.isAbsolute(filePath)) {
            return vscode.Uri.file(filePath);
        }
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            // Resolve relative to the first workspace folder
            const absolutePath = nodePath.resolve(workspaceFolders[0].uri.fsPath, filePath);
            return vscode.Uri.file(absolutePath);
        }
        else {
            // If no workspace folder, and path is relative, this is ambiguous.
            // Could throw or resolve relative to process.cwd(), safer to throw.
            throw new Error(`Cannot resolve relative path "${filePath}": No workspace folder is open.`);
        }
    }
    /**
       * Helper to list diagnostics for specified URIs, with filtering and limiting.
       * Internal helper used by 'list', 'summarize', 'search'.
       * @param urisToProcess Array of VS Code Uris to process.
       * @param options Filtering and limiting options.
       * @returns An array of serializable diagnostic objects.
       */
    async listDiagnosticsForUris(urisToProcess, options) {
        const allDiagnostics = [];
        for (const uri of urisToProcess) {
            // Use ensureDocumentOpen to make sure diagnostics are current
            try {
                // await vscode.workspace.openTextDocument(uri); // Opening doc ensures diagnostics are pushed by extensions
                // A lighter approach is to just get diagnostics, assuming they are updated asyncly
                // But opening is safer for getting latest state before querying.
                const doc = await vscode.workspace.openTextDocument(uri); // Open and implicitly wait for diagnostics update?
                const fileDiagnostics = vscode.languages.getDiagnostics(uri);
                // Apply filters
                const normalizedSeverities = normalizeStringOrStringArrayInput(options.severity);
                const normalizedCodes = normalizeStringOrStringArrayInput(options.code);
                const normalizedSources = normalizeStringOrStringArrayInput(options.source);
                // Message filter removed as it's not part of the schema
                const filteredDiagnostics = fileDiagnostics.filter(diag => {
                    if (normalizedSeverities && !normalizedSeverities.includes(vscode.DiagnosticSeverity[diag.severity].toLowerCase()))
                        return false;
                    if (normalizedCodes && !(diag.code === undefined || diag.code === null) && !normalizedCodes.includes(diag.code.toString()))
                        return false;
                    if (normalizedSources && !(diag.source === undefined || diag.source === null) && !normalizedSources.includes(diag.source.toLowerCase()))
                        return false;
                    return true;
                });
                // Add uri and range info for serialization
                const serializableDiagnostics = filteredDiagnostics.map(diag => ({
                    // Copy relevant diagnostic properties
                    severity: diag.severity,
                    code: diag.code?.toString(), // Ensure code is string
                    source: diag.source,
                    message: diag.message,
                    // Include location info for the diagnostic
                    resource: uri.toString(), // File URI string
                    range: new vscode.Range(diag.range.start.line, diag.range.start.character, diag.range.end.line, diag.range.end.character),
                    // Include related information if any (also needs serialization)
                    relatedInformation: diag.relatedInformation?.map(ri => new vscode.DiagnosticRelatedInformation(new vscode.Location(ri.location.uri, ri.location.range), ri.message)),
                    // Include tags if any
                    tags: diag.tags,
                }));
                allDiagnostics.push(...serializableDiagnostics);
            }
            catch (error) {
                logger_1.Logger.instance.error(`Error processing diagnostics for URI ${uri.toString()}:`, error);
                // Continue with next URI
            }
        }
        // Apply overall limit if specified
        const finalLimitedDiagnostics = options.limit ? allDiagnostics.slice(0, options.limit) : allDiagnostics;
        // Note: Currently returns a flat array. Could group by file path if needed in output.
        return finalLimitedDiagnostics;
    }
    /**
       * Helper for the 'list' action. Gets diagnostics for a file or all open files.
       * @param options List filtering options.
       * @returns An array of serializable diagnostic objects.
       */
    async listDiagnostics(options) {
        let urisToProcess = [];
        if (options.filePath) {
            try {
                // Resolve and validate the file path
                const fileUri = this.resolveFilePathToUri(options.filePath);
                // Check if the file exists before getting diagnostics
                await vscode.workspace.fs.stat(fileUri);
                urisToProcess.push(fileUri);
            }
            catch (error) {
                logger_1.Logger.instance.warn(`File not found for diagnostics listing: ${options.filePath}`, error);
                // Return empty array if file not found or path resolution fails
                return [];
            }
        }
        else {
            // Get diagnostics for all currently open text documents
            urisToProcess = vscode.workspace.textDocuments.map(doc => doc.uri);
        }
        return this.listDiagnosticsForUris(urisToProcess, options);
    }
    /**
       * Helper for the 'fix' action. Applies a specific quick fix (code action) to a diagnostic by index.
       * @param options File path, diagnostic index, and code action index.
       * @returns Result indicating success or failure.
       */
    async applySpecificDiagnosticFix(options) {
        try {
            const fileUri = this.resolveFilePathToUri(options.filePath);
            // Ensure the document is open and active for quick fixes to be readily available
            const document = await vscode.workspace.openTextDocument(fileUri);
            await vscode.window.showTextDocument(document); // Make it visible/active
            // Get current diagnostics for the document
            const diags = vscode.languages.getDiagnostics(fileUri);
            // Find the target diagnostic by index
            const targetDiag = diags[options.diagnosticIndex];
            if (!targetDiag) {
                return { success: false, error: `No diagnostic found at index ${options.diagnosticIndex} for file ${options.filePath}. Diagnostic indices are volatile and can change.` };
            }
            // Use VS Code's executeCodeActionProvider command to find applicable actions for the diagnostic's range
            // Pass 'quickfix' kind to get only quick fixes
            const codeActions = await vscode.commands.executeCommand('vscode.executeCodeActionProvider', fileUri, targetDiag.range, { only: vscode.CodeActionKind.QuickFix.value } // Request only quick fixes
            );
            if (!codeActions || codeActions.length === 0) {
                logger_1.Logger.instance.info(`No quick fixes found for diagnostic at index ${options.diagnosticIndex}.`);
                return { success: false, error: `No quick fixes found for diagnostic at index ${options.diagnosticIndex}.` };
            }
            // Find the target code action by index
            const targetAction = codeActions[options.codeActionIndex];
            if (!targetAction) {
                return { success: false, error: `No quick fix action found at index ${options.codeActionIndex} for the diagnostic. Found ${codeActions.length} fixes.` };
            }
            // Execute the command associated with the code action or apply the edit
            if (targetAction.command) {
                logger_1.Logger.instance.debug(`Applying quick fix command: ${targetAction.title}`);
                await vscode.commands.executeCommand(targetAction.command.command, ...(targetAction.command.arguments || []));
            }
            else if (targetAction.edit) {
                logger_1.Logger.instance.debug(`Applying quick fix edit: ${targetAction.title}`);
                const success = await vscode.workspace.applyEdit(targetAction.edit);
                if (!success) {
                    throw new Error(`Failed to apply workspace edit for quick fix "${targetAction.title}".`);
                }
            }
            else {
                throw new Error(`Quick fix action "${targetAction.title}" has neither a command nor an edit. Cannot apply programmatically.`);
            }
            // Save the document after applying changes
            await document.save();
            // Give VS Code a moment to update diagnostics after the fix
            await new Promise(resolve => setTimeout(resolve, 100));
            return { success: true, output: `Quick fix "${targetAction.title}" applied for diagnostic at index ${options.diagnosticIndex}.` };
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to apply quick fix for diagnostic index ${options.diagnosticIndex} in ${options.filePath}:`, error);
            return { success: false, error: `Failed to apply fix: ${error.message || error}` };
        }
    }
    /**
       * Helper for the 'apply_code_actions' action. Applies multiple code actions (fixes) to a file based on filters.
       * @param options File path, optional range, and filters.
       * @returns Result indicating success and count of applied actions.
       */
    async applyCodeActions(options) {
        try {
            const fileUri = this.resolveFilePathToUri(options.filePath);
            // Ensure the document is open and active
            const document = await vscode.workspace.openTextDocument(fileUri);
            await vscode.window.showTextDocument(document); // Makes it visible/active
            // Determine the range to request code actions for
            const range = options.range ? new vscode.Range(options.range.start.line, options.range.start.character, options.range.end.line, options.range.end.character) : new vscode.Range(0, 0, document.lineCount, 0);
            // Define the kinds of code actions to request. Prioritize 'fix all' if requested.
            const kindsToRequest = [];
            if (options.applyFixAll) {
                // Request source.fixAll actions and common specific ones like 'eslint.fixAll'
                kindsToRequest.push(vscode.CodeActionKind.SourceFixAll.value);
                kindsToRequest.push('eslint.fixAll'); // Common specific source action
                // Add other known fix-all kinds if necessary
            }
            // Always request QuickFixes unless only fix-all is explicitly meant
            if (!options.applyFixAll) { // Only include if not *only* fix-all
                kindsToRequest.push(vscode.CodeActionKind.QuickFix.value);
            }
            else {
                // If applyFixAll is true, still allow individual quick fixes if needed?
                // For now, let's stick to explicitly requested kinds.
            }
            // Use VS Code's executeCodeActionProvider command to find applicable actions
            const codeActions = await vscode.commands.executeCommand('vscode.executeCodeActionProvider', fileUri, range, { only: kindsToRequest.join(',') } // Request specific kinds
            );
            if (!codeActions || codeActions.length === 0) {
                logger_1.Logger.instance.info(`No code actions found for file ${options.filePath} within the specified range/kinds.`);
                return { success: true, output: 'No code actions found matching criteria.', appliedCount: 0 };
            }
            // Filter actions based on associated diagnostics' severity, code, source if provided
            const normalizedSeverities = normalizeStringOrStringArrayInput(options.severity);
            const normalizedCodes = normalizeStringOrStringArrayInput(options.code);
            const normalizedSources = normalizeStringOrStringArrayInput(options.source);
            const filteredActions = codeActions.filter(action => {
                // Filter based on diagnostic properties if filters are applied
                if (normalizedSeverities || normalizedCodes || normalizedSources) {
                    // Action must have diagnostics, and at least one diagnostic must match *all* filters
                    if (!action.diagnostics || action.diagnostics.length === 0)
                        return false; // Action has no diagnostics to filter by
                    return action.diagnostics.some(diag => {
                        const severityMatch = normalizedSeverities ? normalizedSeverities.includes(vscode.DiagnosticSeverity[diag.severity].toLowerCase()) : true;
                        const codeMatch = normalizedCodes ? (diag.code !== undefined && diag.code !== null && normalizedCodes.includes(diag.code.toString())) : true;
                        const sourceMatch = normalizedSources ? (diag.source !== undefined && diag.source !== null && normalizedSources.includes(diag.source.toLowerCase())) : true;
                        return severityMatch && codeMatch && sourceMatch; // All filters must match at least one diagnostic
                    });
                }
                else {
                    // No filters applied, include all actions found by kind
                    return true;
                }
            });
            if (filteredActions.length === 0) {
                logger_1.Logger.instance.info(`No code actions found after applying filters for file ${options.filePath}.`);
                return { success: true, output: 'No code actions found matching filters.', appliedCount: 0 };
            }
            logger_1.Logger.instance.debug(`Found ${filteredActions.length} code actions after filtering. Applying them...`);
            let appliedCount = 0;
            // Execute each applicable action. Prioritize 'fix all' actions if they exist in the filtered list.
            // Note: Applying multiple actions simultaneously might have side effects or overlaps.
            // A robust approach might involve applying them sequentially with delays.
            // For polyfill, apply sequentially.
            const fixAllActions = filteredActions.filter(a => a.kind?.value.includes(vscode.CodeActionKind.SourceFixAll.value) || a.command?.command.includes('fixAll')); // Heuristic for command name
            const otherActions = filteredActions.filter(a => !fixAllActions.includes(a));
            // Apply fix-all actions first if requested and available
            if (options.applyFixAll && fixAllActions.length > 0) {
                logger_1.Logger.instance.debug(`Applying ${fixAllActions.length} Fix All actions...`);
                for (const action of fixAllActions) {
                    if (action.command) {
                        try {
                            await vscode.commands.executeCommand(action.command.command, ...(action.command.arguments || []));
                            appliedCount++;
                            logger_1.Logger.instance.debug(`Applied Fix All command: ${action.title}`);
                            await new Promise(resolve => setTimeout(resolve, 100)); // Short delay
                        }
                        catch (actionError) {
                            logger_1.Logger.instance.error(`Failed to apply Fix All action "${action.title}" in ${options.filePath}:`, actionError);
                        }
                    }
                    else if (action.edit) {
                        try {
                            const success = await vscode.workspace.applyEdit(action.edit);
                            if (success) {
                                appliedCount++;
                                logger_1.Logger.instance.debug(`Applied Fix All edit: ${action.title}`);
                                await new Promise(resolve => setTimeout(resolve, 100)); // Short delay
                            }
                            else {
                                logger_1.Logger.instance.warn(`Failed to apply Fix All edit for code action "${action.title}" in ${options.filePath}.`);
                            }
                        }
                        catch (editError) {
                            logger_1.Logger.instance.error(`Error applying Fix All edit for code action "${action.title}":`, editError);
                        }
                    }
                }
            }
            else {
                // If applyFixAll wasn't requested or no fix-all actions found, apply other actions
                logger_1.Logger.instance.debug(`Applying ${otherActions.length} other actions...`);
                for (const action of otherActions) {
                    if (action.command) {
                        try {
                            await vscode.commands.executeCommand(action.command.command, ...(action.command.arguments || []));
                            appliedCount++;
                            logger_1.Logger.instance.debug(`Applied command action: ${action.title}`);
                            await new Promise(resolve => setTimeout(resolve, 50)); // Short delay
                        }
                        catch (actionError) {
                            logger_1.Logger.instance.error(`Failed to apply command action "${action.title}" in ${options.filePath}:`, actionError);
                        }
                    }
                    else if (action.edit) {
                        try {
                            const success = await vscode.workspace.applyEdit(action.edit);
                            if (success) {
                                appliedCount++;
                                logger_1.Logger.instance.debug(`Applied edit action: ${action.title}`);
                                await new Promise(resolve => setTimeout(resolve, 50)); // Short delay
                            }
                            else {
                                logger_1.Logger.instance.warn(`Failed to apply edit for code action "${action.title}" in ${options.filePath}.`);
                            }
                        }
                        catch (editError) {
                            logger_1.Logger.instance.error(`Error applying edit for code action "${action.title}":`, editError);
                        }
                    }
                    else {
                        logger_1.Logger.instance.warn(`Code action "${action.title}" in ${options.filePath} has neither a command nor an edit. Cannot apply programmatically.`);
                    }
                }
            }
            // Save the document after applying changes
            await document.save();
            // Give VS Code a moment to update diagnostics
            await new Promise(resolve => setTimeout(resolve, 100));
            return { success: true, output: `Applied ${appliedCount} code actions to ${options.filePath}.`, appliedCount };
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed during applyCodeActions for ${options.filePath}:`, error);
            return { success: false, error: `Failed to apply code actions: ${error.message || error}`, appliedCount: 0 };
        }
    }
    /**
       * Helper for the 'summarize' action. Summarizes diagnostics for a file or workspace.
       * @param options Summary scope and filters.
       * @returns A structured summary object.
       */
    async summarizeDiagnostics(options) {
        let urisToProcess = [];
        let scopeDescription = 'workspace';
        if (options.filePath) {
            try {
                const fileUri = this.resolveFilePathToUri(options.filePath);
                await vscode.workspace.fs.stat(fileUri); // Check file exists
                urisToProcess.push(fileUri);
                scopeDescription = options.filePath;
            }
            catch (error) {
                logger_1.Logger.instance.warn(`File not found for diagnostics summary: ${options.filePath}`, error);
                // Return summary indicating file not found
                return {
                    scope: options.filePath,
                    error: `File not found: ${options.filePath}`,
                    totalDiagnostics: 0,
                    countsBySeverity: { error: 0, warning: 0, info: 0, hint: 0 },
                    countsBySourceAndSeverity: {},
                    topIssues: [],
                    filtersApplied: options,
                };
            }
        }
        else {
            // Summarize for the entire workspace (all open documents)
            urisToProcess = vscode.workspace.textDocuments.map(doc => doc.uri);
            if (urisToProcess.length === 0) {
                logger_1.Logger.instance.info('No open documents to summarize diagnostics for.');
                return {
                    scope: 'workspace (no open documents)',
                    totalDiagnostics: 0,
                    countsBySeverity: { error: 0, warning: 0, info: 0, hint: 0 },
                    countsBySourceAndSeverity: {},
                    topIssues: [],
                    filtersApplied: options,
                };
            }
        }
        // Get all relevant diagnostics using the list helper, but apply filters here before counting
        const allCandidateDiagnostics = await this.listDiagnosticsForUris(urisToProcess, {
            // Pass through filters from summarize input
            severity: options.severity,
            source: Array.isArray(options.source) ? options.source[0] : options.source,
            // No limit needed here as we need all for counting
        });
        // Count diagnostics by severity and source
        const counts = {
            'error': { total: 0, sources: {} },
            'warning': { total: 0, sources: {} },
            'info': { total: 0, sources: {} },
            'hint': { total: 0, sources: {} },
        };
        for (const diag of allCandidateDiagnostics) {
            // Use the already normalized severity string from the list helper's output
            const severityName = diag.severity;
            const sourceName = diag.source || 'unknown';
            if (counts[severityName]) { // Ensure severity is one of the known types
                counts[severityName].total++;
                counts[severityName].sources[sourceName] = (counts[severityName].sources[sourceName] || 0) + 1;
            }
        }
        // Get top issues (most frequent messages or codes)
        const issueCounts = {};
        for (const diag of allCandidateDiagnostics) {
            // Create a key combining severity, source, code, and message for unique issue types
            const severity = diag.severity;
            const source = diag.source || 'unknown';
            const code = diag.code || 'unknown';
            const message = diag.message; // Use full message for grouping
            const key = `${severity}:${source}:${code}:${message}`;
            if (!issueCounts[key]) {
                issueCounts[key] = { count: 0, severity, source, code, message, exampleLocation: diag.resource && diag.range ? { resource: diag.resource, range: diag.range } : undefined };
            }
            issueCounts[key].count++;
        }
        // Convert to array and sort by count descending
        const sortedIssues = Object.values(issueCounts).sort((a, b) => b.count - a.count);
        // Take top issues up to limit
        const topIssuesLimit = options.topIssuesLimit ?? 10; // Default to top 10
        const limitedTopIssues = sortedIssues.slice(0, topIssuesLimit);
        const summary = {
            scope: scopeDescription,
            totalDiagnostics: allCandidateDiagnostics.length,
            countsBySeverity: {
                error: counts.error.total,
                warning: counts.warning.total,
                info: counts.info.total,
                hint: counts.hint.total,
            },
            countsBySourceAndSeverity: counts, // Detailed breakdown
            topIssues: limitedTopIssues.map(issue => ({
                message: issue.message,
                count: issue.count,
                severity: issue.severity,
                source: issue.source,
                code: issue.code,
                exampleLocation: issue.exampleLocation,
            })),
            filtersApplied: options,
        };
        return summary;
    }
    /**
       * Helper for the 'search' action. Searches for diagnostics based on a query string within messages, codes, or sources.
       * @param options Search query and filters.
       * @returns An array of serializable matching diagnostic objects.
       */
    async searchDiagnostics(options) {
        if (!options.query) {
            logger_1.Logger.instance.warn('Diagnostics search query is empty. Returning empty results.');
            return []; // Query is required by schema, but defensive check
        }
        // Get diagnostics based on filePath filter (or all open) using the list helper
        // We apply the severity/source filters from the search options here during listing
        const candidateDiagnostics = await this.listDiagnostics({
            filePath: options.filePath,
            severity: options.severity,
            source: Array.isArray(options.source) ? options.source[0] : options.source,
            // No overall limit yet, apply search query first
        });
        const queryLower = options.query.toLowerCase();
        // Filter candidate diagnostics by the query string across message, code, or source
        const matchingDiagnostics = candidateDiagnostics.filter(diag => {
            // diag objects from listDiagnosticsForUris already have severity, code, source, message as strings/lowercase
            const messageMatch = diag.message.toLowerCase().includes(queryLower);
            const codeMatch = diag.code?.toLowerCase().includes(queryLower) ?? false; // Use ?. for potential undefined code
            const sourceMatch = diag.source?.toLowerCase().includes(queryLower) ?? false; // Use ?. for potential undefined source
            return messageMatch || codeMatch || sourceMatch;
        });
        // Apply final limit
        const limitedMatches = options.limit ? matchingDiagnostics.slice(0, options.limit) : matchingDiagnostics;
        // The diagnostics returned by listDiagnosticsForUris are already serialized/enriched
        return limitedMatches;
    }
    /**
       * Helper to get configuration settings specific to this tool.
       * @returns An object with tool-specific settings.
       */
    getLintToolSettings() {
        // Retrieve configuration values specific to this tool
        // Assuming config keys follow a pattern like 'tools.lintDiagnostics.*'
        return {
            // Example setting: maxOutputBufferBytes is from the terminal tool config,
            // illustrating that settings might be relevant across tools.
            // Replace with actual settings for this tool if they exist.
            // E.g., including/excluding diagnostic sources, auto-fix preferences etc.
            // Example:
            // includeDiagnosticSources: getConfig<string[] | undefined>('tools.lintDiagnostics.includeSources', undefined),
            // excludeDiagnosticSources: getConfig<string[] | undefined>('tools.lintDiagnostics.excludeSources', undefined),
            // Example: settings related to task running or summarization defaults
            // defaultLintTaskName: getConfig<string | undefined>('tools.lintDiagnostics.defaultTaskName', undefined),
            // defaultSummaryTopIssuesLimit: getConfig<number | undefined>('tools.lintDiagnostics.defaultSummaryTopIssuesLimit', undefined),
            // For now, return relevant terminal tool settings as a placeholder if this tool uses them
            terminalCommandConfig: {
                maxOutputBufferBytes: (0, config_1.getConfig)('tools.terminalCommand.maxOutputBufferBytes', 10 * 1024 * 1024),
                defaultTimeoutMs: (0, config_1.getConfig)('tools.terminalCommand.defaultTimeoutMs', 60000),
                defaultShell: (0, config_1.getConfig)('tools.terminalCommand.defaultShell', (0, terminalCommandTool_1.getDefaultShell)()),
                allowedCommands: (0, config_1.getConfig)('tools.terminalCommand.allowedCommands', undefined),
                forbiddenCommands: (0, config_1.getConfig)('tools.terminalCommand.forbiddenCommands', undefined),
                useExecForSimpleCommands: (0, config_1.getConfig)('tools.terminalCommand.useExecForSimpleCommands', false),
            }
        };
    }
    /**
       * Helper to update configuration settings specific to this tool.
       * @param settings A partial object with settings to update, conforming to UpdateLintToolSettingsInputSchema.
       * @returns A promise indicating success.
       */
    async updateLintToolSettings(settings) {
        // Use setConfig utility to update settings. This utility should handle persistence.
        // Map the input object keys to actual config keys.
        // Example:
        // if (settings.includeDiagnosticSources !== undefined) {
        //     await setConfig('tools.lintDiagnostics.includeSources', settings.includeDiagnosticSources);
        // }
        // if (settings.excludeDiagnosticSources !== undefined) {
        //     await setConfig('tools.lintDiagnostics.excludeSources', settings.excludeDiagnosticSources);
        // }
        // Example: Update settings from terminalCommandTool config if this tool manages them
        if (settings.maxOutputBufferBytes !== undefined)
            await (0, config_1.setConfig)('tools.terminalCommand.maxOutputBufferBytes', settings.maxOutputBufferBytes);
        if (settings.defaultTimeoutMs !== undefined)
            await (0, config_1.setConfig)('tools.terminalCommand.defaultTimeoutMs', settings.defaultTimeoutMs);
        if (settings.defaultShell !== undefined)
            await (0, config_1.setConfig)('tools.terminalCommand.defaultShell', settings.defaultShell);
        if (settings.allowedCommands !== undefined)
            await (0, config_1.setConfig)('tools.terminalCommand.allowedCommands', settings.allowedCommands); // setConfig should handle null/undefined to clear
        if (settings.forbiddenCommands !== undefined)
            await (0, config_1.setConfig)('tools.terminalCommand.forbiddenCommands', settings.forbiddenCommands); // setConfig should handle null/undefined to clear
        if (settings.useExecForSimpleCommands !== undefined)
            await (0, config_1.setConfig)('tools.terminalCommand.useExecForSimpleCommands', settings.useExecForSimpleCommands);
        logger_1.Logger.instance.info('Lint/diagnostics settings update attempted.', settings);
        // The setConfig utility should handle firing events or re-initializing components if needed.
    }
}
exports.LintDiagnosticsTool = LintDiagnosticsTool;
// Export a factory function to create the tool instance
// If the tool needs dependencies (like MemoryManager for advanced search/summary),
// those should be passed to the factory.
function createLintDiagnosticsTool(context) {
    // Pass dependencies from context if needed by the tool constructor
    return new LintDiagnosticsTool(context);
}
// Export a singleton instance created via the factory
exports.lintDiagnosticsTool = createLintDiagnosticsTool();
//# sourceMappingURL=lintDiagnosticsTool.js.map