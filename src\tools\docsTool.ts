import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { Logger } from '../logger';
import { AgentContext } from '../agents/agentUtilities/agent';
import { llmService } from '../llm/llmService';
import { LLMConfig } from '../config';
import { DocumentationGenTool, DocumentationSearchTool, DocumentationSummaryTool, DocumentationVisualizationTool } from './advancedDocsTool';
import { z } from 'zod';
import { LLMGenerateParams, LLMGenerateResult } from '../llm/types';

/**
 * A tool for searching documentation or asking general knowledge questions.
 * This is currently implemented as a pass-through to the LLM, but could be extended
 * to use web search or other documentation sources.
 */
export class DocumentationTool implements ITool {
  // Schema definition for the tool
  readonly schema = z.object({
    action: z.string().optional().describe('The action to perform (search, generate, summarize, visualize, ask)'),
    query: z.string().optional().describe('The search query or question'),
    topic: z.string().optional().describe('Topic for documentation generation'),
    content: z.string().optional().describe('Content to process (e.g., for summarization)'),
    data: z.any().optional().describe('Data for visualization')
  });
  
  // Alias for backward compatibility
  get inputSchema() { return this.schema; }
  
  // Output schema for the tool
  readonly outputSchema = z.object({
    success: z.boolean(),
    output: z.any(),
    error: z.string().optional(),
    toolId: z.string(),
    actionName: z.string().optional()
  });
  
  private llmConfig: LLMConfig = {
    provider: 'openai',
    modelId: 'gpt-3.5-turbo',
    options: { temperature: 0.3 }
  };
  private llmService: any; // Define llmService property
  readonly id = 'docs';
  readonly name = 'Documentation (Advanced)';
  readonly description = 'Search, generate, summarize, and visualize technical documentation or knowledge.';
  readonly type = 'multi-action'; // Required by ITool
  readonly actions: Record<string, any> = {
    'search': {
      ...new DocumentationSearchTool(),
      type: 'single-action',
      actions: {},
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const query = input.query as string;
        if (!query) {
          return { success: false, error: '\'query\' parameter is required.', toolId: 'search', actionName };
        }
        // Placeholder implementation
        return { success: true, output: `Documentation search results for: ${query}`, toolId: 'search', actionName };
      }
    },
    'generate': {
      ...new DocumentationGenTool(),
      type: 'single-action',
      actions: {},
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const topic = input.topic as string;
        if (!topic) {
          return { success: false, error: '\'topic\' parameter is required.', toolId: 'generate', actionName };
        }
        // Placeholder implementation
        return { success: true, output: `Generated documentation for: ${topic}`, toolId: 'generate', actionName };
      }
    },
    'summarize': {
      ...new DocumentationSummaryTool(),
      type: 'single-action',
      actions: {},
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const content = input.content as string;
        if (!content) {
          return { success: false, error: '\'content\' parameter is required.', toolId: 'summarize', actionName };
        }
        // Placeholder implementation
        return { success: true, output: `Summarized documentation: ${content.substring(0, 50)}...`, toolId: 'summarize', actionName };
      }
    },
    'visualize': {
      ...new DocumentationVisualizationTool(),
      type: 'single-action',
      actions: {},
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const data = input.data as any;
        if (!data) {
          return { success: false, error: '\'data\' parameter is required.', toolId: 'visualize', actionName };
        }
        // Placeholder implementation
        return { success: true, output: 'Visualization created for the provided data', toolId: 'visualize', actionName };
      }
    },
    'ask': {
      id: 'ask',
      name: 'Ask Documentation',
      description: 'Ask a documentation or technical question.',
      type: 'single-action', // Required by ITool
      actions: {}, // Required by ITool
      singleActionSchema: z.object({
        query: z.string().describe('The documentation or technical question to ask.')
      }),
      inputSchema: {
        type: 'object',
        properties: {
          query: { type: 'string', description: 'The documentation or technical question to ask.' }
        },
        required: ['query']
      },
      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
        const query = input.query as string;
        if (!query) {
          return { success: false, error: '\'query\' parameter is required.', toolId: 'ask', actionName };
        }
        Logger.instance.info(`Documentation search requested for: "${query}"`);
        Logger.instance.warn('Using LLM for documentation search. This may not be accurate for recent information.');
        const researchLLMConfig: LLMConfig = {
          provider: 'openai',
          modelId: 'gpt-3.5-turbo',
          options: { temperature: 0.3 }
        };
        const provider = await llmService.getProviderForConfig(researchLLMConfig);
        if (!provider) {
          return { success: false, error: 'LLM provider for documentation search not found or configured.', toolId: 'ask', actionName };
        }
        try {
          const systemPrompt = 'You are a documentation researcher. Your task is to answer the following query with accurate, technical information.\nBe concise but thorough. Include code examples where appropriate. If you don\'t know the answer, say so instead of making things up.\nOnly answer what is asked - do not try to provide additional information beyond the scope of the query.';
          const result = await provider.generate({
            prompt: query,
            systemPrompt: systemPrompt,
            modelId: researchLLMConfig.modelId,
            options: researchLLMConfig.options,
            mode: 'generate'
          });
          if (result.error) {
            return { success: false, error: result.error, toolId: 'ask', actionName };
          }
          return { success: true, output: result.content, toolId: 'ask', actionName };
        } catch (error: any) {
          return { success: false, error: `Documentation search failed: ${error.message || error}`, toolId: 'ask', actionName };
        }
      }
    }
  };

  async execute(input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    // For multi-action tools, extract action from input if not provided
    const actionName = input.action as string | undefined;
    
    // If an action is specified, delegate to the appropriate action tool
    if (actionName && this.actions[actionName]) {
      const actionTool = this.actions[actionName];
      
      // Handle both new and old style action tools
      if (actionTool.execute) {
        const result = await actionTool.execute(input, context);
        // Ensure the action name is included in the result
        if (result && !result.actionName) {
          result.actionName = actionName;
        }
        return result;
      }
    }

    // Default behavior - treat as a documentation search if no action specified
    const query = input.query as string;

    if (!query) {
      return { success: false, error: '\'query\' parameter is required.', toolId: this.id, actionName };
    }

    Logger.instance.info(`Documentation search requested for: "${query}"`);

    // In a real implementation, we might call a search API or a dedicated service
    // For now, let's use the LLM as a fallback
    Logger.instance.warn('Using LLM for documentation search. This may not be accurate for recent information.');

    const provider = await llmService.getProviderForConfig(this.llmConfig);
    if (!provider) {
      return { success: false, error: 'LLM provider for documentation search not found or configured.', toolId: this.id, actionName };
    }

    try {
      const systemPrompt = `You are a documentation researcher. Your task is to answer the following query with accurate, technical information.
Be concise but thorough. Include code examples where appropriate. If you don't know the answer, say so instead of making things up.
Only answer what is asked - do not try to provide additional information beyond the scope of the query.`;

      const result = await provider.generate({
        prompt: query,
        systemPrompt: systemPrompt,
        modelId: this.llmConfig.modelId,
        options: this.llmConfig.options,
        mode: 'generate'
      });

      if (result.error) {
        return { success: false, error: `Documentation search failed: ${result.error}`, toolId: this.id, actionName };
      }

      return { success: true, output: result.content, toolId: this.id, actionName };
    } catch (error: any) {
      Logger.instance.error(`Error during documentation search for query "${query}":`, error);
      return { success: false, error: `Documentation search failed: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  async generate(params: LLMGenerateParams): Promise<LLMGenerateResult> {
    return await this.llmService.generate(params);
  }
}

export const documentationTool = new DocumentationTool();
