"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileChangeTracker = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
const statusBar_1 = require("../feedback/statusBar");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const logger = logger_1.Logger.instance;
/**
 * Advanced File Change Tracker - Professional change tracking with embedded diffs
 * Features comprehensive diff analysis and intelligent change management
 */
class FileChangeTracker {
    static instance = null;
    changeBar = null;
    fileWatcher = null;
    trackedChanges = new Map();
    isVisible = false;
    context;
    diffCache = new Map();
    aiAnalysisCache = new Map();
    changeHistory = new Map();
    smartFilters = new Set();
    autoReviewEnabled = true;
    collaborativeMode = false;
    realTimeSync = true;
    constructor(context) {
        this.context = context;
        this.setupFileWatcher();
        this.setupChangeBar();
    }
    /**
     * Get singleton instance
     */
    static getInstance(context) {
        if (!FileChangeTracker.instance && context) {
            FileChangeTracker.instance = new FileChangeTracker(context);
        }
        if (!FileChangeTracker.instance) {
            throw new Error('FileChangeTracker not initialized. Call getInstance with context first.');
        }
        return FileChangeTracker.instance;
    }
    /**
     * Setup file system watcher
     */
    setupFileWatcher() {
        if (!vscode.workspace.workspaceFolders) {
            return;
        }
        // Watch for file changes in the workspace
        this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/*', false, false, false);
        // Handle file modifications
        this.fileWatcher.onDidChange((uri) => {
            this.handleFileChange(uri, 'modified');
        });
        // Handle file creation
        this.fileWatcher.onDidCreate((uri) => {
            this.handleFileChange(uri, 'created');
        });
        // Handle file deletion
        this.fileWatcher.onDidDelete((uri) => {
            this.handleFileChange(uri, 'deleted');
        });
        logger.info('File change watcher initialized');
    }
    /**
     * Setup the change tracking status bar
     */
    setupChangeBar() {
        this.changeBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 1000 // High priority to appear on the left
        );
        this.changeBar.command = 'codessa.showFileChanges';
        this.updateChangeBar();
    }
    /**
     * Handle file change events with revolutionary analysis
     */
    async handleFileChange(uri, type) {
        try {
            // Skip if not in workspace
            if (!vscode.workspace.getWorkspaceFolder(uri)) {
                return;
            }
            // Skip certain file types and directories
            const relativePath = vscode.workspace.asRelativePath(uri);
            if (this.shouldIgnoreFile(relativePath)) {
                return;
            }
            // Advanced git status detection
            const gitStatus = await this.getAdvancedGitStatus(uri);
            if (!gitStatus.isModified && type === 'modified') {
                this.trackedChanges.delete(uri.fsPath);
                this.updateChangeBar();
                return;
            }
            // Generate comprehensive diff analysis
            const diffStats = await this.generateDiffStats(uri, type);
            // AI-powered change analysis
            const aiSuggestion = await this.generateAISuggestion(uri, diffStats);
            // Detect conflict markers
            const conflictMarkers = await this.detectConflictMarkers(uri);
            // Determine priority based on change impact
            const priority = this.calculateChangePriority(diffStats, relativePath);
            // Auto-generate tags based on file content and changes
            const tags = await this.generateSmartTags(uri, diffStats);
            // Create enhanced change object
            const change = {
                uri,
                type: gitStatus.changeType || type,
                relativePath,
                timestamp: Date.now(),
                diffStats,
                gitStatus: gitStatus.status,
                conflictMarkers,
                aiSuggestion,
                reviewStatus: 'pending',
                tags,
                priority
            };
            // Store change history for analytics
            this.addToChangeHistory(uri.fsPath, change);
            this.trackedChanges.set(uri.fsPath, change);
            this.updateChangeBar();
            // Trigger real-time notifications for critical changes
            if (priority === 'critical') {
                this.showCriticalChangeNotification(change);
            }
            logger.debug(`Advanced file change tracked: ${relativePath} (${type}) - Priority: ${priority}`);
        }
        catch (error) {
            logger.error('Error handling file change:', error);
        }
    }
    /**
     * Check if file should be ignored
     */
    shouldIgnoreFile(relativePath) {
        const ignorePatterns = [
            'node_modules/',
            '.git/',
            'out/',
            'dist/',
            'build/',
            '.vscode/',
            '*.log',
            '*.tmp',
            '.DS_Store',
            'Thumbs.db'
        ];
        return ignorePatterns.some(pattern => {
            if (pattern.endsWith('/')) {
                return relativePath.startsWith(pattern);
            }
            if (pattern.includes('*')) {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                return regex.test(relativePath);
            }
            return relativePath.includes(pattern);
        });
    }
    /**
     * Advanced git status detection with comprehensive analysis
     */
    async getAdvancedGitStatus(uri) {
        try {
            const gitExtension = vscode.extensions.getExtension('vscode.git');
            if (gitExtension && gitExtension.isActive) {
                const git = gitExtension.exports.getAPI(1);
                const repo = git.getRepository(uri);
                if (repo) {
                    const status = await repo.getStatus();
                    const fileStatus = status.find((item) => item.uri.fsPath === uri.fsPath);
                    if (fileStatus) {
                        let changeType = 'modified';
                        // Determine change type from git status
                        if (fileStatus.status === 1)
                            changeType = 'created'; // INDEX_ADDED
                        else if (fileStatus.status === 6)
                            changeType = 'deleted'; // INDEX_DELETED
                        else if (fileStatus.status === 3)
                            changeType = 'renamed'; // INDEX_RENAMED
                        else if (fileStatus.status === 128)
                            changeType = 'conflicted'; // CONFLICTED
                        return {
                            isModified: true,
                            status: this.convertGitStatusToString(fileStatus.status),
                            changeType,
                            branch: repo.state.HEAD?.name || 'unknown',
                            ahead: repo.state.HEAD?.ahead || 0,
                            behind: repo.state.HEAD?.behind || 0
                        };
                    }
                }
            }
            // Fallback analysis
            const stats = await fs.promises.stat(uri.fsPath);
            const now = Date.now();
            const modifiedTime = stats.mtime.getTime();
            return {
                isModified: (now - modifiedTime) < 300000, // 5 minutes
                status: 'modified',
                changeType: 'modified',
                branch: 'unknown',
                ahead: 0,
                behind: 0
            };
        }
        catch (error) {
            return {
                isModified: true,
                status: 'unknown',
                changeType: 'modified',
                branch: 'unknown',
                ahead: 0,
                behind: 0
            };
        }
    }
    /**
     * Generate comprehensive diff statistics with line-by-line analysis
     */
    async generateDiffStats(uri, type) {
        try {
            if (type === 'created') {
                const content = await vscode.workspace.fs.readFile(uri);
                const lines = new TextDecoder().decode(content).split('\n');
                return {
                    additions: lines.length,
                    deletions: 0,
                    changes: lines.length,
                    hunks: [{
                            oldStart: 0,
                            oldLines: 0,
                            newStart: 1,
                            newLines: lines.length,
                            header: `@@ -0,0 +1,${lines.length} @@`,
                            lines: lines.map((line, index) => ({
                                type: 'addition',
                                newLineNumber: index + 1,
                                content: line
                            }))
                        }]
                };
            }
            if (type === 'deleted') {
                // Try to get the last known content from git
                const gitContent = await this.getFileContentFromGit(uri);
                const lines = gitContent.split('\n');
                return {
                    additions: 0,
                    deletions: lines.length,
                    changes: lines.length,
                    hunks: [{
                            oldStart: 1,
                            oldLines: lines.length,
                            newStart: 0,
                            newLines: 0,
                            header: `@@ -1,${lines.length} +0,0 @@`,
                            lines: lines.map((line, index) => ({
                                type: 'deletion',
                                oldLineNumber: index + 1,
                                content: line
                            }))
                        }]
                };
            }
            // For modified files, generate detailed diff
            return await this.createDetailedDiff(uri);
        }
        catch (error) {
            logger.error('Error generating diff stats:', error);
            return { additions: 0, deletions: 0, changes: 0, hunks: [] };
        }
    }
    /**
     * Generate AI-powered suggestions for changes
     */
    async generateAISuggestion(uri, diffStats) {
        try {
            // Cache check
            const cacheKey = `${uri.fsPath}-${diffStats.changes}`;
            if (this.aiAnalysisCache.has(cacheKey)) {
                return this.aiAnalysisCache.get(cacheKey) ?? '📋 Change detected - Review before committing';
            }
            // Analyze change patterns
            const fileExtension = path.extname(uri.fsPath);
            const changeSize = diffStats.additions + diffStats.deletions;
            let suggestion = '';
            if (changeSize > 100) {
                suggestion = '🔍 Large change detected - Consider breaking into smaller commits';
            }
            else if (diffStats.deletions > diffStats.additions * 2) {
                suggestion = '🗑️ Major refactoring - Ensure tests are updated';
            }
            else if (fileExtension === '.ts' || fileExtension === '.js') {
                suggestion = '⚡ Code change - Run tests and check for breaking changes';
            }
            else if (fileExtension === '.json') {
                suggestion = '⚙️ Configuration change - Verify settings are correct';
            }
            else if (fileExtension === '.md') {
                suggestion = '📝 Documentation update - Check for spelling and formatting';
            }
            else {
                suggestion = '✨ File modified - Review changes before committing';
            }
            // Cache the result
            this.aiAnalysisCache.set(cacheKey, suggestion);
            return suggestion;
        }
        catch (error) {
            return '📋 Change detected - Review before committing';
        }
    }
    /**
     * Detect conflict markers in files
     */
    async detectConflictMarkers(uri) {
        try {
            const content = await vscode.workspace.fs.readFile(uri);
            const text = new TextDecoder().decode(content);
            const conflictMarkers = [
                '<<<<<<< HEAD',
                '=======',
                '>>>>>>> ',
                '|||||||| merged common ancestors'
            ];
            return conflictMarkers.some(marker => text.includes(marker));
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Calculate change priority based on impact analysis
     */
    calculateChangePriority(diffStats, relativePath) {
        const changeSize = diffStats.additions + diffStats.deletions;
        const fileExtension = path.extname(relativePath);
        // Critical files
        if (relativePath.includes('package.json') ||
            relativePath.includes('tsconfig.json') ||
            relativePath.includes('.env') ||
            relativePath.includes('Dockerfile')) {
            return 'critical';
        }
        // High priority
        if (changeSize > 200 ||
            fileExtension === '.ts' && changeSize > 50 ||
            relativePath.includes('src/') && changeSize > 100) {
            return 'high';
        }
        // Medium priority
        if (changeSize > 20 || fileExtension === '.ts' || fileExtension === '.js') {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Generate smart tags based on file analysis
     */
    async generateSmartTags(uri, diffStats) {
        const tags = [];
        const relativePath = vscode.workspace.asRelativePath(uri);
        const fileExtension = path.extname(relativePath);
        // File type tags
        if (fileExtension === '.ts' || fileExtension === '.js') {
            tags.push('code');
        }
        else if (fileExtension === '.css' || fileExtension === '.scss') {
            tags.push('styles');
        }
        else if (fileExtension === '.md') {
            tags.push('docs');
        }
        else if (fileExtension === '.json') {
            tags.push('config');
        }
        // Size-based tags
        const changeSize = diffStats.additions + diffStats.deletions;
        if (changeSize > 100) {
            tags.push('large-change');
        }
        else if (changeSize < 10) {
            tags.push('small-change');
        }
        // Pattern-based tags
        if (diffStats.deletions > diffStats.additions) {
            tags.push('refactor');
        }
        else if (diffStats.additions > diffStats.deletions * 2) {
            tags.push('feature');
        }
        // Directory-based tags
        if (relativePath.includes('test/') || relativePath.includes('spec/')) {
            tags.push('tests');
        }
        else if (relativePath.includes('src/')) {
            tags.push('source');
        }
        return tags;
    }
    /**
     * Update the change bar display with advanced analytics
     */
    updateChangeBar() {
        if (!this.changeBar) {
            return;
        }
        const changeCount = this.trackedChanges.size;
        if (changeCount === 0) {
            this.hideChangeBar();
            return;
        }
        // Calculate advanced metrics
        const changes = Array.from(this.trackedChanges.values());
        const criticalCount = changes.filter(c => c.priority === 'critical').length;
        const conflictCount = changes.filter(c => c.conflictMarkers).length;
        const totalAdditions = changes.reduce((sum, c) => sum + (c.diffStats?.additions || 0), 0);
        const totalDeletions = changes.reduce((sum, c) => sum + (c.diffStats?.deletions || 0), 0);
        // Enhanced status bar text with metrics
        let statusText = `$(source-control) ${changeCount} file${changeCount === 1 ? '' : 's'}`;
        if (criticalCount > 0) {
            statusText += ` $(warning) ${criticalCount} critical`;
        }
        if (conflictCount > 0) {
            statusText += ` $(error) ${conflictCount} conflicts`;
        }
        statusText += ` (+${totalAdditions}/-${totalDeletions})`;
        this.changeBar.text = statusText;
        this.changeBar.tooltip = this.createAdvancedTooltip(changes);
        // Dynamic color based on priority
        if (criticalCount > 0 || conflictCount > 0) {
            this.changeBar.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
            this.changeBar.color = new vscode.ThemeColor('statusBarItem.errorForeground');
        }
        else if (changes.some(c => c.priority === 'high')) {
            this.changeBar.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
            this.changeBar.color = new vscode.ThemeColor('statusBarItem.warningForeground');
        }
        else {
            this.changeBar.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
            this.changeBar.color = new vscode.ThemeColor('statusBarItem.prominentForeground');
        }
        this.showChangeBar();
    }
    /**
     * Show the change bar
     */
    showChangeBar() {
        if (this.changeBar && !this.isVisible) {
            this.changeBar.show();
            this.isVisible = true;
        }
    }
    /**
     * Hide the change bar
     */
    hideChangeBar() {
        if (this.changeBar && this.isVisible) {
            this.changeBar.hide();
            this.isVisible = false;
        }
    }
    /**
     * Show file changes quick pick
     */
    async showFileChanges() {
        if (this.trackedChanges.size === 0) {
            vscode.window.showInformationMessage('No file changes to show');
            return;
        }
        const items = [];
        // Add action items
        items.push({
            label: '$(check-all) Keep All Changes',
            description: `Keep all ${this.trackedChanges.size} changed files`,
            detail: 'Accept all current changes'
        }, {
            label: '$(discard) Discard All Changes',
            description: `Discard all ${this.trackedChanges.size} changed files`,
            detail: 'Revert all files to their last committed state'
        }, { label: '', kind: vscode.QuickPickItemKind.Separator });
        // Add individual files
        for (const change of this.trackedChanges.values()) {
            const icon = this.getFileIcon(change.type);
            items.push({
                label: `${icon} ${change.relativePath}`,
                description: change.type,
                detail: `Modified ${this.formatTimestamp(change.timestamp)}`
            });
        }
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select an action or file to manage changes',
            matchOnDescription: true,
            matchOnDetail: true
        });
        if (!selected) {
            return;
        }
        // Handle actions
        if (selected.label.includes('Keep All')) {
            await this.keepAllChanges();
        }
        else if (selected.label.includes('Discard All')) {
            await this.discardAllChanges();
        }
        else {
            // Handle individual file selection
            const fileName = selected.label.replace(/^\$\([^)]+\)\s+/, '');
            const change = Array.from(this.trackedChanges.values()).find(c => c.relativePath === fileName);
            if (change) {
                await this.showFileActions(change);
            }
        }
    }
    /**
     * Get icon for file change type
     */
    getFileIcon(type) {
        switch (type) {
            case 'modified': return '$(edit)';
            case 'created': return '$(add)';
            case 'deleted': return '$(remove)';
            case 'renamed': return '$(arrow-right)';
            case 'conflicted': return '$(warning)';
            default: return '$(file)';
        }
    }
    /**
     * Format timestamp for display
     */
    formatTimestamp(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        if (diff < 60000) {
            return 'just now';
        }
        else if (diff < 3600000) {
            const minutes = Math.floor(diff / 60000);
            return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
        }
        else {
            const hours = Math.floor(diff / 3600000);
            return `${hours} hour${hours === 1 ? '' : 's'} ago`;
        }
    }
    /**
     * Show actions for individual file
     */
    async showFileActions(change) {
        const actions = [
            {
                label: '$(eye) View Changes',
                description: 'Open diff view to see changes',
                detail: 'Compare current version with last committed version'
            },
            {
                label: '$(check) Keep Changes',
                description: 'Keep changes in this file',
                detail: 'Accept the current modifications'
            },
            {
                label: '$(discard) Discard Changes',
                description: 'Discard changes in this file',
                detail: 'Revert file to last committed state'
            },
            {
                label: '$(go-to-file) Open File',
                description: 'Open file in editor',
                detail: 'View and edit the file'
            }
        ];
        const selected = await vscode.window.showQuickPick(actions, {
            placeHolder: `Actions for ${change.relativePath}`
        });
        if (!selected) {
            return;
        }
        switch (true) {
            case selected.label.includes('View Changes'):
                await this.viewFileChanges(change);
                break;
            case selected.label.includes('Keep Changes'):
                await this.keepFileChanges(change);
                break;
            case selected.label.includes('Discard Changes'):
                await this.discardFileChanges(change);
                break;
            case selected.label.includes('Open File'):
                await vscode.window.showTextDocument(change.uri);
                break;
        }
    }
    /**
     * View changes for a specific file
     */
    async viewFileChanges(change) {
        try {
            // Use VS Code's built-in diff command
            await vscode.commands.executeCommand('vscode.diff', vscode.Uri.parse(`git:${change.uri.path}?HEAD`), change.uri, `${change.relativePath} (Working Tree)`);
        }
        catch (error) {
            logger.error('Error viewing file changes:', error);
            vscode.window.showErrorMessage(`Failed to view changes for ${change.relativePath}`);
        }
    }
    /**
     * Keep changes for a specific file
     */
    async keepFileChanges(change) {
        try {
            // For now, just remove from tracking (changes are already kept)
            this.trackedChanges.delete(change.uri.fsPath);
            this.updateChangeBar();
            vscode.window.showInformationMessage(`Changes kept for ${change.relativePath}`);
        }
        catch (error) {
            logger.error('Error keeping file changes:', error);
            vscode.window.showErrorMessage(`Failed to keep changes for ${change.relativePath}`);
        }
    }
    /**
     * Discard changes for a specific file
     */
    async discardFileChanges(change) {
        try {
            const confirm = await vscode.window.showWarningMessage(`Are you sure you want to discard changes in ${change.relativePath}?`, { modal: true }, 'Discard Changes');
            if (confirm === 'Discard Changes') {
                // Use git checkout to discard changes
                await vscode.commands.executeCommand('git.checkout', change.uri);
                // Remove from tracking
                this.trackedChanges.delete(change.uri.fsPath);
                this.updateChangeBar();
                vscode.window.showInformationMessage(`Changes discarded for ${change.relativePath}`);
            }
        }
        catch (error) {
            logger.error('Error discarding file changes:', error);
            vscode.window.showErrorMessage(`Failed to discard changes for ${change.relativePath}`);
        }
    }
    /**
     * Keep all changes
     */
    async keepAllChanges() {
        try {
            statusBar_1.statusBarManager.setActivityIndicator(true);
            const confirm = await vscode.window.showInformationMessage(`Keep all ${this.trackedChanges.size} changed files?`, 'Keep All Changes');
            if (confirm === 'Keep All Changes') {
                // Clear all tracked changes (they're already kept)
                this.trackedChanges.clear();
                this.updateChangeBar();
                vscode.window.showInformationMessage('All changes have been kept');
            }
        }
        catch (error) {
            logger.error('Error keeping all changes:', error);
            vscode.window.showErrorMessage('Failed to keep all changes');
        }
        finally {
            statusBar_1.statusBarManager.setActivityIndicator(false);
        }
    }
    /**
     * Discard all changes
     */
    async discardAllChanges() {
        try {
            statusBar_1.statusBarManager.setActivityIndicator(true);
            const confirm = await vscode.window.showWarningMessage(`Are you sure you want to discard all ${this.trackedChanges.size} changed files?`, { modal: true }, 'Discard All Changes');
            if (confirm === 'Discard All Changes') {
                // Show progress for multiple files
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: 'Discarding changes',
                    cancellable: false
                }, async (progress) => {
                    const changes = Array.from(this.trackedChanges.values());
                    const total = changes.length;
                    for (let i = 0; i < changes.length; i++) {
                        const change = changes[i];
                        progress.report({
                            increment: (100 / total),
                            message: `Discarding ${change.relativePath}...`
                        });
                        try {
                            await vscode.commands.executeCommand('git.checkout', change.uri);
                        }
                        catch (error) {
                            logger.error(`Error discarding ${change.relativePath}:`, error);
                        }
                    }
                    // Clear all tracked changes
                    this.trackedChanges.clear();
                    this.updateChangeBar();
                });
                vscode.window.showInformationMessage('All changes have been discarded');
            }
        }
        catch (error) {
            logger.error('Error discarding all changes:', error);
            vscode.window.showErrorMessage('Failed to discard all changes');
        }
        finally {
            statusBar_1.statusBarManager.setActivityIndicator(false);
        }
    }
    /**
     * Get current tracked changes
     */
    getTrackedChanges() {
        return Array.from(this.trackedChanges.values());
    }
    /**
     * Clear all tracked changes
     */
    clearTrackedChanges() {
        this.trackedChanges.clear();
        this.updateChangeBar();
    }
    /**
     * Refresh change tracking
     */
    async refreshChanges() {
        try {
            statusBar_1.statusBarManager.setActivityIndicator(true);
            // Clear current changes
            this.trackedChanges.clear();
            // Re-scan for git changes
            const gitExtension = vscode.extensions.getExtension('vscode.git');
            if (gitExtension && gitExtension.isActive) {
                const git = gitExtension.exports.getAPI(1);
                for (const repo of git.repositories) {
                    const status = await repo.getStatus();
                    for (const item of status) {
                        const change = {
                            uri: item.uri,
                            type: 'modified', // Git status provides more detailed info
                            relativePath: vscode.workspace.asRelativePath(item.uri),
                            timestamp: Date.now()
                        };
                        this.trackedChanges.set(item.uri.fsPath, change);
                    }
                }
            }
            this.updateChangeBar();
            logger.info('File changes refreshed');
        }
        catch (error) {
            logger.error('Error refreshing changes:', error);
        }
        finally {
            statusBar_1.statusBarManager.setActivityIndicator(false);
        }
    }
    /**
     * Add change to history for analytics
     */
    addToChangeHistory(filePath, change) {
        const history = this.changeHistory.get(filePath) ?? [];
        history.push(change);
        this.changeHistory.set(filePath, history);
        // Keep only last 10 changes per file
        if (history.length > 10) {
            history.shift();
        }
    }
    /**
     * Show critical change notification
     */
    showCriticalChangeNotification(change) {
        vscode.window.showWarningMessage(`Critical change detected in ${change.relativePath}`, 'View Changes', 'Dismiss').then(selection => {
            if (selection === 'View Changes') {
                this.showFileChanges();
            }
        });
    }
    /**
     * Convert git status to string
     */
    convertGitStatusToString(status) {
        switch (status) {
            case 1: return 'added';
            case 2: return 'modified';
            case 3: return 'renamed';
            case 4: return 'copied';
            case 5: return 'updated';
            case 6: return 'deleted';
            case 128: return 'conflicted';
            default: return 'unknown';
        }
    }
    /**
     * Get file content from git
     */
    async getFileContentFromGit(uri) {
        try {
            const gitExtension = vscode.extensions.getExtension('vscode.git');
            if (gitExtension && gitExtension.isActive) {
                const git = gitExtension.exports.getAPI(1);
                const repo = git.getRepository(uri);
                if (repo) {
                    const relativePath = path.relative(repo.rootUri.fsPath, uri.fsPath);
                    const content = await repo.show('HEAD', relativePath);
                    return content || '';
                }
            }
            return '';
        }
        catch (error) {
            return '';
        }
    }
    /**
     * Create detailed diff for modified files
     */
    async createDetailedDiff(uri) {
        try {
            const currentContent = await vscode.workspace.fs.readFile(uri);
            const currentText = new TextDecoder().decode(currentContent);
            const gitContent = await this.getFileContentFromGit(uri);
            // Simple diff implementation (in production, use a proper diff library)
            const currentLines = currentText.split('\n');
            const gitLines = gitContent.split('\n');
            let additions = 0;
            let deletions = 0;
            const diffLines = [];
            // Basic line-by-line comparison
            const maxLines = Math.max(currentLines.length, gitLines.length);
            for (let i = 0; i < maxLines; i++) {
                const currentLine = currentLines[i];
                const gitLine = gitLines[i];
                if (currentLine !== undefined && gitLine !== undefined) {
                    if (currentLine !== gitLine) {
                        // Modified line
                        diffLines.push({
                            type: 'deletion',
                            oldLineNumber: i + 1,
                            content: gitLine
                        });
                        diffLines.push({
                            type: 'addition',
                            newLineNumber: i + 1,
                            content: currentLine
                        });
                        additions++;
                        deletions++;
                    }
                    else {
                        // Context line
                        diffLines.push({
                            type: 'context',
                            oldLineNumber: i + 1,
                            newLineNumber: i + 1,
                            content: currentLine
                        });
                    }
                }
                else if (currentLine !== undefined) {
                    // Added line
                    diffLines.push({
                        type: 'addition',
                        newLineNumber: i + 1,
                        content: currentLine
                    });
                    additions++;
                }
                else if (gitLine !== undefined) {
                    // Deleted line
                    diffLines.push({
                        type: 'deletion',
                        oldLineNumber: i + 1,
                        content: gitLine
                    });
                    deletions++;
                }
            }
            return {
                additions,
                deletions,
                changes: additions + deletions,
                hunks: [{
                        oldStart: 1,
                        oldLines: gitLines.length,
                        newStart: 1,
                        newLines: currentLines.length,
                        header: `@@ -1,${gitLines.length} +1,${currentLines.length} @@`,
                        lines: diffLines
                    }]
            };
        }
        catch (error) {
            logger.error('Error creating detailed diff:', error);
            return { additions: 0, deletions: 0, changes: 0, hunks: [] };
        }
    }
    /**
     * Create advanced tooltip with metrics
     */
    createAdvancedTooltip(changes) {
        const criticalCount = changes.filter(c => c.priority === 'critical').length;
        const conflictCount = changes.filter(c => c.conflictMarkers).length;
        const totalAdditions = changes.reduce((sum, c) => sum + (c.diffStats?.additions || 0), 0);
        const totalDeletions = changes.reduce((sum, c) => sum + (c.diffStats?.deletions || 0), 0);
        let tooltip = `${changes.length} file${changes.length === 1 ? '' : 's'} changed\n`;
        tooltip += `+${totalAdditions} additions, -${totalDeletions} deletions\n`;
        if (criticalCount > 0) {
            tooltip += `⚠️ ${criticalCount} critical change${criticalCount === 1 ? '' : 's'}\n`;
        }
        if (conflictCount > 0) {
            tooltip += `🔥 ${conflictCount} conflict${conflictCount === 1 ? '' : 's'}\n`;
        }
        tooltip += '\nClick to manage changes';
        return tooltip;
    }
    /**
     * Dispose resources
     */
    dispose() {
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
        }
        if (this.changeBar) {
            this.changeBar.dispose();
        }
        FileChangeTracker.instance = null;
    }
}
exports.FileChangeTracker = FileChangeTracker;
//# sourceMappingURL=fileChangeTracker.js.map