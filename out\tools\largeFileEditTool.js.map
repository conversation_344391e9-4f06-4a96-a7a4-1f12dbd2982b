{"version": 3, "file": "largeFileEditTool.js", "sourceRoot": "", "sources": ["../../src/tools/largeFileEditTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,6BAAwB;AACxB,2CAA6B;AAC7B,uCAAyB;AAEzB,uCAAyB;AACzB,mDAAqC;AAErC,+CAAiC;AACjC,+BAAiC;AAEjC,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAE5C;;GAEG;AACH,MAAa,iBAAiB;IACnB,EAAE,GAAG,eAAe,CAAC;IACrB,IAAI,GAAG,iBAAiB,CAAC;IACzB,WAAW,GAAG,6EAA6E,CAAC;IAC5F,IAAI,GAAG,cAAc,CAAC;IAC/B,0EAA0E;IACjE,OAAO,GAA2B;QACzC,YAAY,EAAE;YACZ,WAAW,EAAE,gFAAgF;YAC7F,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;gBAC1D,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;oBAC3B,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC;oBAC5E,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;oBAC9D,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;oBACnG,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;iBACtF,CAAC,CAAC,CAAC,QAAQ,CAAC,4CAA4C,CAAC;gBAC1D,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oFAAoF,CAAC;gBAChI,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6DAA6D,CAAC;aAC7G,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;gBAC1D,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;oBAC3B,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC;oBAC5E,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;oBAC9D,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;oBACnG,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;iBACtF,CAAC,CAAC,CAAC,QAAQ,CAAC,4CAA4C,CAAC;gBAC1D,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oFAAoF,CAAC;gBAChI,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6DAA6D,CAAC;aAC7G,CAAC;SACH;QACD,eAAe,EAAE;YACf,WAAW,EAAE,sEAAsE;YACnF,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;gBAC1D,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sCAAsC,CAAC;gBACpE,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBACrD,MAAM,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sDAAsD,CAAC;gBAC/F,aAAa,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yDAAyD,CAAC;gBACzG,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oFAAoF,CAAC;gBAChI,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6DAA6D,CAAC;aAC7G,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;gBAC1D,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sCAAsC,CAAC;gBACpE,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBACrD,MAAM,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sDAAsD,CAAC;gBAC/F,aAAa,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yDAAyD,CAAC;gBACzG,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oFAAoF,CAAC;gBAChI,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6DAA6D,CAAC;aAC7G,CAAC;SACH;QACD,cAAc,EAAE;YACd,WAAW,EAAE,gCAAgC;YAC7C,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC7D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+CAA+C,CAAC;gBAC1F,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,qCAAqC,CAAC;gBAClG,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;gBACtF,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;aACvG,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC7D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+CAA+C,CAAC;gBAC1F,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,qCAAqC,CAAC;gBAClG,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;gBACtF,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;aACvG,CAAC;SACH;QACD,aAAa,EAAE;YACb,WAAW,EAAE,gEAAgE;YAC7E,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC7D,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8EAA8E,CAAC;aAC5H,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC7D,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8EAA8E,CAAC;aAC5H,CAAC;SACH;QACD,WAAW,EAAE;YACX,WAAW,EAAE,wCAAwC;YACrD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;gBAC3D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;gBACpE,OAAO,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,yBAAyB,CAAC;gBACtE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;gBACzE,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+DAA+D,CAAC;aACxG,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;gBAC3D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;gBACpE,OAAO,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,yBAAyB,CAAC;gBACtE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;gBACzE,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+DAA+D,CAAC;aACxG,CAAC;SACH;KACF,CAAC;IAEF,oBAAoB;IACZ,MAAM,CAAC,OAAO,GAAG,IAAI,GAAG,EAA+C,CAAC;IACxE,MAAM,CAAU,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;IACnE,MAAM,CAAU,kBAAkB,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO;IAEtE,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,UAAU,IAAI,WAAW,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACjH,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,YAAY;oBACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACnD,KAAK,eAAe;oBAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACtD,KAAK,cAAc;oBACjB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACrD,KAAK,aAAa;oBAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACpD,KAAK,WAAW;oBACd,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAClD;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,UAAU,EAAE;wBACtC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC1D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,uBAAuB;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAE5D,iCAAiC;YACjC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAExF,qDAAqD;YACrD,MAAM,QAAQ,CACZ,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAC7B,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CACjC,CAAC;YAEF,wBAAwB;YACxB,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACtC,IAAI,EAAE,UAAU;gBAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;YAED,8DAA8D;YAC9D,MAAM,QAAQ,CACZ,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAChC,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAC/B,CAAC;YAEF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,iBAAiB,KAAK,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iBAAiB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,uDAAuD;QACvD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;YACzC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;gBAChE,IAAI,CAAC;oBACH,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC/B,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBAC7B,CAAC;oBACD,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC7C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;gBAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBACpC,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;gBACjB,SAAS,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAClB,OAAO,CAAC,SAAS,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,KAAgB,EAAE,UAAkB;QAClE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,UAKvB,CAAC;YACH,MAAM,UAAU,GAAG,KAAK,CAAC,UAAoB,CAAC;YAC9C,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB;YAErE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2DAA2D;oBAClE,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,sBAAsB;YACtB,KAAK,MAAM,EAAE,IAAI,UAAU,EAAE,CAAC;gBAC5B,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;oBACb,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sCAAsC;wBAC7C,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAED,IAAI,EAAE,CAAC,SAAS,KAAK,SAAS,IAAI,EAAE,CAAC,SAAS,KAAK,IAAI,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;oBAC5E,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,wDAAwD;wBAC/D,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAED,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,CAAC;oBACjD,CAAC,EAAE,CAAC,OAAO,KAAK,SAAS,IAAI,EAAE,CAAC,OAAO,KAAK,IAAI,IAAI,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;oBACjF,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sBAAsB,EAAE,CAAC,IAAI,+CAA+C;wBACnF,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAED,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;oBAChF,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sBAAsB,EAAE,CAAC,IAAI,wBAAwB;wBAC5D,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,wCAAwC;YACxC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEpD,KAAK,MAAM,EAAE,IAAI,UAAU,EAAE,CAAC;gBAC5B,IAAI,EAAE,CAAC,SAAS,GAAG,SAAS,EAAE,CAAC;oBAC7B,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sBAAsB,EAAE,CAAC,SAAS,cAAc,SAAS,UAAU;wBAC1E,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAED,IAAI,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,OAAO,GAAG,SAAS,EAAE,CAAC;oBACzC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,oBAAoB,EAAE,CAAC,OAAO,cAAc,SAAS,UAAU;wBACtE,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,kFAAkF;YAClF,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;YAErD,6BAA6B;YAC7B,IAAI,UAAU,GAAG,EAAE,CAAC;YACpB,IAAI,YAAY,EAAE,CAAC;gBACjB,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACjD,CAAC;YAED,+BAA+B;YAC/B,MAAM,cAAc,GAAG,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhH,IAAI,CAAC;gBACH,gCAAgC;gBAChC,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;gBAE3E,6DAA6D;gBAC7D,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE;wBACN,OAAO,EAAE,uBAAuB,QAAQ,EAAE;wBAC1C,iBAAiB,EAAE,UAAU,CAAC,MAAM;wBACpC,UAAU,EAAE,UAAU,IAAI,QAAQ;wBAClC,aAAa,EAAE,YAAY;wBAC3B,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;qBAClD;oBACD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;oBACV,QAAQ,EAAE;wBACR,QAAQ;wBACR,UAAU,EAAE,UAAU,IAAI,QAAQ;wBAClC,eAAe,EAAE,UAAU,CAAC,MAAM;qBACnC;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,0BAA0B;gBAC1B,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;oBACjD,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBAChC,CAAC;gBAED,8CAA8C;gBAC9C,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBACzC,CAAC;gBAED,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uBAAuB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACtD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,yBAAyB,CACrC,SAAiB,EACjB,UAAkB,EAClB,UAKE;QAEF,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;gBAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC;gBACrC,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACtD,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;YAEvB,8DAA8D;YAC9D,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAgC,CAAC;YACzD,KAAK,MAAM,EAAE,IAAI,UAAU,EAAE,CAAC;gBAC5B,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACjC,CAAC;YAED,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACrB,WAAW,EAAE,CAAC;gBAEd,4DAA4D;gBAC5D,IAAI,WAAW,GAAG,aAAa,EAAE,CAAC;oBAChC,OAAO;gBACT,CAAC;gBAED,kBAAkB;gBAClB,IAAI,WAAW,KAAK,aAAa,EAAE,CAAC;oBAClC,aAAa,GAAG,CAAC,CAAC,CAAC;oBACnB,OAAO;gBACT,CAAC;gBAED,8CAA8C;gBAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAE5C,IAAI,SAAS,EAAE,CAAC;oBACd,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;wBACvB,KAAK,SAAS;4BACZ,gCAAgC;4BAChC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;4BAC7C,0BAA0B;4BAC1B,aAAa,GAAG,CAAC,SAAS,CAAC,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;4BACvD,MAAM;wBAER,KAAK,QAAQ;4BACX,0BAA0B;4BAC1B,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;4BAChC,6BAA6B;4BAC7B,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;4BAC7C,MAAM;wBAER,KAAK,QAAQ;4BACX,2BAA2B;4BAC3B,aAAa,GAAG,CAAC,SAAS,CAAC,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;4BACvD,MAAM;oBACV,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,0BAA0B;oBAC1B,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAClB,YAAY,CAAC,GAAG,EAAE,CAAC;gBACnB,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACrB,YAAY,CAAC,GAAG,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBAC/B,EAAE,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,KAAgB,EAAE,UAAkB;QACrE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;YACxC,MAAM,WAAW,GAAG,KAAK,CAAC,WAAqB,CAAC;YAChD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,kBAAkB;YACzD,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa,KAAK,KAAK,CAAC,CAAC,kBAAkB;YACvE,MAAM,UAAU,GAAG,KAAK,CAAC,UAAoB,CAAC;YAC9C,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB;YAErE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0BAA0B;oBACjC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,8BAA8B;oBACrC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,6BAA6B;YAC7B,IAAI,UAAU,GAAG,EAAE,CAAC;YACpB,IAAI,YAAY,EAAE,CAAC;gBACjB,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACjD,CAAC;YAED,+BAA+B;YAC/B,MAAM,cAAc,GAAG,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhH,IAAI,CAAC;gBACH,4BAA4B;gBAC5B,MAAM,KAAK,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAChE,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAEzC,gCAAgC;gBAChC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;gBAE5F,6DAA6D;gBAC7D,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE;wBACN,OAAO,EAAE,iCAAiC,QAAQ,EAAE;wBACpD,YAAY,EAAE,KAAK,CAAC,YAAY;wBAChC,aAAa,EAAE,KAAK,CAAC,aAAa;wBAClC,UAAU,EAAE,UAAU,IAAI,QAAQ;wBAClC,aAAa,EAAE,YAAY;wBAC3B,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;qBAClD;oBACD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;oBACV,QAAQ,EAAE;wBACR,QAAQ;wBACR,UAAU,EAAE,UAAU,IAAI,QAAQ;wBAClC,OAAO;wBACP,YAAY,EAAE,KAAK,CAAC,YAAY;qBACjC;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,0BAA0B;gBAC1B,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;oBACjD,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBAChC,CAAC;gBAED,8CAA8C;gBAC9C,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBACzC,CAAC;gBAED,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,0BAA0B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACzD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAChC,SAAiB,EACjB,UAAkB,EAClB,KAAa,EACb,WAAmB;QAEnB,OAAO,IAAI,OAAO,CAAkD,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtF,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;gBAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC;gBACrC,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACtD,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACrB,yCAAyC;gBACzC,MAAM,YAAY,GAAG,IAAI,CAAC;gBAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC5C,YAAY,EAAE,CAAC;oBACf,OAAO,WAAW,CAAC;gBACrB,CAAC,CAAC,CAAC;gBAEH,oCAAoC;gBACpC,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;gBAEnC,uBAAuB;gBACvB,IAAI,YAAY,KAAK,OAAO,EAAE,CAAC;oBAC7B,aAAa,EAAE,CAAC;gBAClB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAClB,YAAY,CAAC,GAAG,EAAE,CAAC;gBACnB,OAAO,CAAC,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACrB,YAAY,CAAC,GAAG,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBAC/B,EAAE,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,KAAgB,EAAE,UAAkB;QACpE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,SAAS,GAAI,KAAK,CAAC,SAAoB,IAAI,iBAAiB,CAAC,kBAAkB,CAAC;YACtF,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;YACxC,MAAM,UAAU,GAAG,KAAK,CAAC,UAAoB,CAAC;YAE9C,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,yCAAyC;YACzC,IAAI,SAAS,KAAK,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;gBACxC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,oDAAoD;oBAC3D,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,SAAS,KAAK,SAAS,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC3C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uDAAuD;oBAC9D,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,iBAAiB;YACjB,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE/C,6BAA6B;YAC7B,IAAI,MAAW,CAAC;YAEhB,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,OAAO;oBACV,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;oBAC5D,MAAM;gBAER,KAAK,SAAS;oBACZ,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;oBAC7D,MAAM;gBAER,KAAK,SAAS;oBACZ,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;oBACtF,MAAM;gBAER;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,0BAA0B,SAAS,EAAE;wBAC5C,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;YACN,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,SAAS;oBACT,MAAM;oBACN,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;qBAC1C;iBACF;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,SAAS;oBACT,SAAS;iBACV;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACxD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,SAAiB;QAClE,OAAO,IAAI,OAAO,CAA4C,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YACtF,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,IAAI,SAAS,GAAG,CAAC,CAAC;gBAElB,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;oBAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;oBAClE,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACjB,SAAS,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBAClB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAC3B,OAAO,CAAC;wBACN,KAAK,EAAE,SAAS;wBAChB,cAAc,EAAE,OAAO,GAAG,SAAS;qBACpC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,QAAgB,EAAE,SAAiB;QACnE,OAAO,IAAI,OAAO,CAAM,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAI,UAAU,GAAG,CAAC,CAAC;gBACnB,IAAI,aAAa,GAAG,CAAC,CAAC;gBACtB,IAAI,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;gBAC5C,IAAI,eAAe,GAAG,CAAC,CAAC;gBAExB,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;oBAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;oBAClE,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACrB,SAAS,EAAE,CAAC;oBACZ,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC;oBAEzB,cAAc;oBACd,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACvE,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC;oBAE1B,oBAAoB;oBACpB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC7B,UAAU,EAAE,CAAC;oBACf,CAAC;oBAED,0BAA0B;oBAC1B,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;oBACrD,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;oBACrD,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC;gBACjC,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBAClB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAE3B,gCAAgC;oBAChC,MAAM,aAAa,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEtE,qDAAqD;oBACrD,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;wBACpB,aAAa,GAAG,CAAC,CAAC;oBACpB,CAAC;oBAED,OAAO,CAAC;wBACN,KAAK,EAAE,SAAS;wBAChB,UAAU,EAAE,SAAS;wBACrB,KAAK,EAAE,SAAS;wBAChB,UAAU;wBACV,SAAS,EAAE;4BACT,SAAS,EAAE,aAAa;4BACxB,SAAS,EAAE,aAAa;4BACxB,SAAS,EAAE,aAAa;yBACzB;wBACD,cAAc,EAAE,OAAO,GAAG,SAAS;qBACpC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB,CAAC,QAAgB,EAAE,OAAe,EAAE,UAAkB,EAAE,SAAiB;QAC5G,OAAO,IAAI,OAAO,CAAM,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,IAAI,UAAU,GAAG,CAAC,CAAC;gBACnB,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAI,YAAY,GAAG,CAAC,CAAC;gBAErB,4BAA4B;gBAC5B,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;gBAEvC,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;oBAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;oBAClE,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;gBAEH,MAAM,YAAY,GAAG,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBAEtD,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACrB,SAAS,EAAE,CAAC;oBAEZ,yCAAyC;oBACzC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAElC,IAAI,OAAO,EAAE,CAAC;wBACZ,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC;wBAC7B,YAAY,EAAE,CAAC;wBAEf,4CAA4C;wBAC5C,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBAClB,YAAY,CAAC,GAAG,EAAE,CAAC;oBACnB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAE3B,OAAO,CAAC;wBACN,UAAU;wBACV,YAAY;wBACZ,UAAU,EAAE,SAAS;wBACrB,eAAe,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;wBACrE,UAAU;wBACV,cAAc,EAAE,OAAO,GAAG,SAAS;qBACpC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACrB,YAAY,CAAC,GAAG,EAAE,CAAC;oBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC;gBAEH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBAC/B,EAAE,CAAC,KAAK,EAAE,CAAC;oBACX,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,KAAgB,EAAE,UAAkB;QACnE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,WAAW,GAAI,KAAK,CAAC,WAAsB,IAAI,CAAC,CAAC;YAEvD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,iBAAiB;YACjB,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE/C,iBAAiB;YACjB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEpD,mDAAmD;YACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAEjE,mBAAmB;YACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAE7E,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC;wBAC9C,KAAK,EAAE,SAAS;wBAChB,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,KAAK,CAAC,SAAS;wBACxB,QAAQ,EAAE,KAAK,CAAC,KAAK;wBACrB,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC;qBAChD;oBACD,OAAO;iBACR;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,KAAK,EAAE,SAAS;iBACjB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACxD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,KAAa;QAC1D,OAAO,IAAI,OAAO,CAA2D,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YACrG,IAAI,CAAC;gBACH,uBAAuB;gBACvB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAEpD,uCAAuC;gBACvC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;gBAE/D,8BAA8B;gBAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;gBAE5E,MAAM,SAAS,GAAa,EAAE,CAAC;gBAC/B,MAAM,MAAM,GAAa,EAAE,CAAC;gBAC5B,MAAM,GAAG,GAAa,EAAE,CAAC;gBAEzB,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;oBAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC;oBACpC,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;gBAEH,IAAI,WAAW,GAAG,CAAC,CAAC;gBAEpB,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACrB,WAAW,EAAE,CAAC;oBAEd,0BAA0B;oBAC1B,IAAI,WAAW,IAAI,WAAW,EAAE,CAAC;wBAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvB,CAAC;oBAED,uBAAuB;oBACvB,IAAI,WAAW,IAAI,WAAW,IAAI,WAAW,GAAG,WAAW,GAAG,WAAW,EAAE,CAAC;wBAC1E,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACpB,CAAC;oBAED,oBAAoB;oBACpB,IAAI,SAAS,GAAG,WAAW,GAAG,WAAW,EAAE,CAAC;wBAC1C,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBAClB,OAAO,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,cAAc,CAAC,KAAa;QAClC,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,IAAI,IAAI,GAAG,KAAK,CAAC;QACjB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,OAAO,IAAI,IAAI,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,IAAI,IAAI,IAAI,CAAC;YACb,SAAS,EAAE,CAAC;QACd,CAAC;QAED,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;IAClD,CAAC;IAED;;SAEK;IACG,iBAAiB,CAAC,IAAY;QACpC,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEzC,wBAAwB;QACxB,MAAM,WAAW,GAAG;YAClB,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YAC1B,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YAC1B,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YAC1B,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YAC1B,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YAC1B,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YAC1B,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YAC1B,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;YAC1B,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;SAC3B,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEX,OAAO,GAAG,KAAK,KAAK,WAAW,GAAG,CAAC;IACrC,CAAC;IAED;;SAEK;IACG,cAAc,CAAC,QAAgB,EAAE,aAAqB;QAC5D,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAEjD,2BAA2B;QAC3B,MAAM,YAAY,GAA2B;YAC3C,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,oBAAoB;YAC5B,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,oBAAoB;YAC5B,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,GAAG;YACT,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,OAAO;YACjB,KAAK,EAAE,MAAM;YACb,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,UAAU;YACjB,KAAK,EAAE,cAAc;YACrB,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;QAC3B,CAAC;QAED,6DAA6D;QAC7D,IAAI,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAClF,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC1B,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,WAAW;QACb,CAAC;QAED,8BAA8B;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7D,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;gBACzE,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,KAAgB,EAAE,UAAkB;QACjE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;YACxC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;YACpC,MAAM,MAAM,GAAI,KAAK,CAAC,MAAiB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEnE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0BAA0B;oBACjC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACxD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mDAAmD;oBAC1D,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,8CAA8C;YAC9C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,iBAAiB;YACjB,IAAI,MAAW,CAAC;YAEhB,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;gBACxB,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAC3E,CAAC;iBAAM,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;gBAC9B,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAC1E,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,6BAA6B,OAAO,EAAE;oBAC7C,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,OAAO;oBACP,KAAK;oBACL,MAAM;iBACP;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,SAAS;oBACT,OAAO;oBACP,KAAK;iBACN;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,sBAAsB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACrD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,SAAiB,EAAE,YAAoB,EAAE,MAAc;QACtG,OAAO,IAAI,OAAO,CAAM,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;gBAEjC,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;oBAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC;oBACpC,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;gBAEH,IAAI,gBAAgB,GAAG,CAAC,CAAC;gBACzB,IAAI,gBAAgB,GAAG,CAAC,CAAC;gBACzB,IAAI,cAAc,GAAG,CAAC,CAAC;gBACvB,IAAI,YAAY,GAA0B,IAAI,CAAC;gBAC/C,IAAI,iBAAiB,GAAG,EAAE,CAAC;gBAE3B,MAAM,mBAAmB,GAAG,GAAG,EAAE;oBAC/B,qCAAqC;oBACrC,IAAI,YAAY,EAAE,CAAC;wBACjB,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,CAAC;oBAED,8BAA8B;oBAC9B,MAAM,WAAW,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBACjE,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,MAAM,IAAI,WAAW,EAAE,CAAC,CAAC;oBACrE,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAEpC,2BAA2B;oBAC3B,YAAY,GAAG,EAAE,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;oBACvD,gBAAgB,EAAE,CAAC;oBACnB,gBAAgB,GAAG,CAAC,CAAC;gBACvB,CAAC,CAAC;gBAEF,+BAA+B;gBAC/B,mBAAmB,EAAE,CAAC;gBAEtB,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACrB,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,CAAC;oBAEnB,4CAA4C;oBAC5C,YAAa,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;oBAEjC,+CAA+C;oBAC/C,IAAI,gBAAgB,IAAI,YAAY,EAAE,CAAC;wBACrC,mBAAmB,EAAE,CAAC;oBACxB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBAClB,+BAA+B;oBAC/B,IAAI,YAAY,EAAE,CAAC;wBACjB,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,CAAC;oBAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAE3B,OAAO,CAAC;wBACN,UAAU,EAAE,cAAc;wBAC1B,UAAU,EAAE,WAAW,CAAC,MAAM;wBAC9B,WAAW;wBACX,YAAY;wBACZ,cAAc,EAAE,OAAO,GAAG,SAAS;qBACpC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBACrB,0BAA0B;oBAC1B,IAAI,YAAY,EAAE,CAAC;wBACjB,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,CAAC;oBAED,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,SAAiB,EAAE,YAAoB,EAAE,MAAc;QACrG,OAAO,IAAI,OAAO,CAAM,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;gBAEjC,MAAM,UAAU,GAAG,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE;oBAC/C,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,wCAAwC;iBAC5F,CAAC,CAAC;gBAEH,IAAI,gBAAgB,GAAG,CAAC,CAAC;gBACzB,IAAI,eAAe,GAAG,CAAC,CAAC;gBACxB,IAAI,mBAAmB,GAAG,CAAC,CAAC;gBAC5B,IAAI,YAAY,GAA0B,IAAI,CAAC;gBAC/C,IAAI,iBAAiB,GAAG,EAAE,CAAC;gBAE3B,MAAM,mBAAmB,GAAG,GAAG,EAAE;oBAC/B,qCAAqC;oBACrC,IAAI,YAAY,EAAE,CAAC;wBACjB,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,CAAC;oBAED,8BAA8B;oBAC9B,MAAM,WAAW,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBACjE,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,MAAM,IAAI,WAAW,EAAE,CAAC,CAAC;oBACrE,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAEpC,2BAA2B;oBAC3B,YAAY,GAAG,EAAE,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;oBACvD,gBAAgB,EAAE,CAAC;oBACnB,eAAe,GAAG,CAAC,CAAC;gBACtB,CAAC,CAAC;gBAEF,+BAA+B;gBAC/B,mBAAmB,EAAE,CAAC;gBAEtB,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC9B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;oBAC/B,mBAAmB,IAAI,SAAS,CAAC;oBAEjC,6DAA6D;oBAC7D,IAAI,eAAe,GAAG,SAAS,GAAG,YAAY,EAAE,CAAC;wBAC/C,8DAA8D;wBAC9D,MAAM,cAAc,GAAG,YAAY,GAAG,eAAe,CAAC;wBAEtD,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;4BACvB,8BAA8B;4BAC9B,YAAa,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;4BACpD,eAAe,IAAI,cAAc,CAAC;wBACpC,CAAC;wBAED,2BAA2B;wBAC3B,mBAAmB,EAAE,CAAC;wBAEtB,8CAA8C;wBAC9C,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;wBACnD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAC9B,YAAa,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;4BACpC,eAAe,IAAI,cAAc,CAAC,MAAM,CAAC;wBAC3C,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,yBAAyB;wBACzB,YAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBAC3B,eAAe,IAAI,SAAS,CAAC;oBAC/B,CAAC;oBAED,+CAA+C;oBAC/C,IAAI,eAAe,IAAI,YAAY,EAAE,CAAC;wBACpC,mBAAmB,EAAE,CAAC;oBACxB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACxB,+BAA+B;oBAC/B,IAAI,YAAY,EAAE,CAAC;wBACjB,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,CAAC;oBAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAE3B,OAAO,CAAC;wBACN,UAAU,EAAE,mBAAmB;wBAC/B,UAAU,EAAE,WAAW,CAAC,MAAM;wBAC9B,WAAW;wBACX,YAAY;wBACZ,cAAc,EAAE,OAAO,GAAG,SAAS;qBACpC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBAC7B,0BAA0B;oBAC1B,IAAI,YAAY,EAAE,CAAC;wBACjB,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,CAAC;oBAED,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;;AA/8CH,8CAg9CC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { z } from 'zod';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport * as crypto from 'crypto';\nimport * as os from 'os';\nimport * as readline from 'readline';\nimport { Readable, Writable } from 'stream';\nimport * as stream from 'stream';\nimport { promisify } from 'util';\n\nconst pipeline = promisify(stream.pipeline);\n\n/**\n * Tool for editing extremely large files with efficient streaming operations\n */\nexport class LargeFileEditTool implements ITool {\n  readonly id = 'largeFileEdit';\n  readonly name = 'Large File Edit';\n  readonly description = 'Tool for editing extremely large files with efficient streaming operations.';\n  readonly type = 'multi-action';\n  // Add index signature to fix \"Element implicitly has an 'any' type\" error\n  readonly actions: { [key: string]: any } = {\n    'streamEdit': {\n      description: 'Edit a large file using streaming to avoid loading the entire file into memory',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to edit.'),\n        operations: z.array(z.object({\n          type: z.enum(['replace', 'insert', 'delete']).describe('Type of operation.'),\n          lineStart: z.number().describe('Start line number (1-based).'),\n          lineEnd: z.number().optional().describe('End line number for replace/delete operations (1-based).'),\n          content: z.string().optional().describe('New content for replace/insert operations.')\n        })).describe('List of operations to perform on the file.'),\n        outputPath: z.string().optional().describe('Path to save the edited file. If not provided, the original file will be modified.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before editing. Default is true.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to edit.'),\n        operations: z.array(z.object({\n          type: z.enum(['replace', 'insert', 'delete']).describe('Type of operation.'),\n          lineStart: z.number().describe('Start line number (1-based).'),\n          lineEnd: z.number().optional().describe('End line number for replace/delete operations (1-based).'),\n          content: z.string().optional().describe('New content for replace/insert operations.')\n        })).describe('List of operations to perform on the file.'),\n        outputPath: z.string().optional().describe('Path to save the edited file. If not provided, the original file will be modified.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before editing. Default is true.')\n      })\n    },\n    'streamReplace': {\n      description: 'Replace text in a large file using streaming and regular expressions',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to edit.'),\n        pattern: z.string().describe('Regular expression pattern to match.'),\n        replacement: z.string().describe('Replacement text.'),\n        global: z.boolean().optional().describe('Whether to replace all occurrences. Default is true.'),\n        caseSensitive: z.boolean().optional().describe('Whether the pattern is case-sensitive. Default is true.'),\n        outputPath: z.string().optional().describe('Path to save the edited file. If not provided, the original file will be modified.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before editing. Default is true.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to edit.'),\n        pattern: z.string().describe('Regular expression pattern to match.'),\n        replacement: z.string().describe('Replacement text.'),\n        global: z.boolean().optional().describe('Whether to replace all occurrences. Default is true.'),\n        caseSensitive: z.boolean().optional().describe('Whether the pattern is case-sensitive. Default is true.'),\n        outputPath: z.string().optional().describe('Path to save the edited file. If not provided, the original file will be modified.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before editing. Default is true.')\n      })\n    },\n    'chunkProcess': {\n      description: 'Process a large file in chunks',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to process.'),\n        chunkSize: z.number().optional().describe('Size of each chunk in bytes. Default is 10MB.'),\n        operation: z.enum(['count', 'analyze', 'extract']).describe('Operation to perform on each chunk.'),\n        pattern: z.string().optional().describe('Pattern to search for in extract operation.'),\n        outputPath: z.string().optional().describe('Path to save the output. Required for extract operation.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to process.'),\n        chunkSize: z.number().optional().describe('Size of each chunk in bytes. Default is 10MB.'),\n        operation: z.enum(['count', 'analyze', 'extract']).describe('Operation to perform on each chunk.'),\n        pattern: z.string().optional().describe('Pattern to search for in extract operation.'),\n        outputPath: z.string().optional().describe('Path to save the output. Required for extract operation.')\n      })\n    },\n    'getFileInfo': {\n      description: 'Get information about a large file without loading it entirely',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to analyze.'),\n        sampleLines: z.number().optional().describe('Number of lines to sample from the beginning, middle, and end. Default is 5.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to analyze.'),\n        sampleLines: z.number().optional().describe('Number of lines to sample from the beginning, middle, and end. Default is 5.')\n      })\n    },\n    'splitFile': {\n      description: 'Split a large file into smaller chunks',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to split.'),\n        outputDir: z.string().describe('Directory to save the split files.'),\n        splitBy: z.enum(['lines', 'size']).describe('Split by lines or size.'),\n        value: z.number().describe('Number of lines or size in bytes per chunk.'),\n        prefix: z.string().optional().describe('Prefix for the split files. Default is the original filename.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to split.'),\n        outputDir: z.string().describe('Directory to save the split files.'),\n        splitBy: z.enum(['lines', 'size']).describe('Split by lines or size.'),\n        value: z.number().describe('Number of lines or size in bytes per chunk.'),\n        prefix: z.string().optional().describe('Prefix for the split files. Default is the original filename.')\n      })\n    }\n  };\n\n  // Backup management\n  private static backups = new Map<string, { path: string, timestamp: number }>();\n  private static readonly BACKUP_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours\n  private static readonly DEFAULT_CHUNK_SIZE = 10 * 1024 * 1024; // 10MB\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      if (!actionName || !this.actions[actionName]) {\n        return {\n          success: false,\n          error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      switch (actionName) {\n        case 'streamEdit':\n          return this.executeStreamEdit(input, actionName);\n        case 'streamReplace':\n          return this.executeStreamReplace(input, actionName);\n        case 'chunkProcess':\n          return this.executeChunkProcess(input, actionName);\n        case 'getFileInfo':\n          return this.executeGetFileInfo(input, actionName);\n        case 'splitFile':\n          return this.executeSplitFile(input, actionName);\n        default:\n          return {\n            success: false,\n            error: `Unknown action: ${actionName}`,\n            toolId: this.id,\n            actionName\n          };\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Large file edit failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Create a backup of a file before editing\n     */\n  private async createBackup(filePath: string): Promise<string> {\n    try {\n      // Clean up old backups\n      this.cleanupOldBackups();\n\n      // Create a backup file\n      const backupDir = path.join(os.tmpdir(), 'codessa-backups');\n\n      // Ensure backup directory exists\n      if (!fs.existsSync(backupDir)) {\n        fs.mkdirSync(backupDir, { recursive: true });\n      }\n\n      const backupPath = path.join(backupDir, `${path.basename(filePath)}.${Date.now()}.bak`);\n\n      // Copy the file to the backup location using streams\n      await pipeline(\n        fs.createReadStream(filePath),\n        fs.createWriteStream(backupPath)\n      );\n\n      // Store the backup info\n      LargeFileEditTool.backups.set(filePath, {\n        path: backupPath,\n        timestamp: Date.now()\n      });\n\n      return backupPath;\n    } catch (error) {\n      throw new Error(`Failed to create backup of ${filePath}: ${error}`);\n    }\n  }\n\n  /**\n     * Restore a file from backup\n     */\n  private async restoreFromBackup(filePath: string): Promise<boolean> {\n    try {\n      const backup = LargeFileEditTool.backups.get(filePath);\n      if (!backup) {\n        return false;\n      }\n\n      // Copy the backup back to the original location using streams\n      await pipeline(\n        fs.createReadStream(backup.path),\n        fs.createWriteStream(filePath)\n      );\n\n      return true;\n    } catch (error) {\n      throw new Error(`Failed to restore ${filePath} from backup: ${error}`);\n    }\n  }\n\n  /**\n     * Clean up old backups\n     */\n  private cleanupOldBackups(): void {\n    const now = Date.now();\n    // Convert entries() iterator to array before iterating\n    const entries = Array.from(LargeFileEditTool.backups.entries());\n    for (const [filePath, backup] of entries) {\n      if (now - backup.timestamp > LargeFileEditTool.BACKUP_EXPIRY_MS) {\n        try {\n          if (fs.existsSync(backup.path)) {\n            fs.unlinkSync(backup.path);\n          }\n          LargeFileEditTool.backups.delete(filePath);\n        } catch (error) {\n          console.error(`Failed to delete backup ${backup.path}: ${error}`);\n        }\n      }\n    }\n  }\n\n  /**\n     * Get the number of lines in a file without loading it entirely\n     */\n  private async getLineCount(filePath: string): Promise<number> {\n    return new Promise<number>((resolve, reject) => {\n      let lineCount = 0;\n\n      const rl = readline.createInterface({\n        input: fs.createReadStream(filePath),\n        crlfDelay: Infinity\n      });\n\n      rl.on('line', () => {\n        lineCount++;\n      });\n\n      rl.on('close', () => {\n        resolve(lineCount);\n      });\n\n      rl.on('error', (err) => {\n        reject(err);\n      });\n    });\n  }\n\n  /**\n     * Execute stream edit action\n     */\n  private async executeStreamEdit(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const operations = input.operations as Array<{\n        type: string;\n        lineStart: number;\n        lineEnd?: number;\n        content?: string;\n      }>;\n      const outputPath = input.outputPath as string;\n      const createBackup = input.createBackup !== false; // Default to true\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!operations || !Array.isArray(operations) || operations.length === 0) {\n        return {\n          success: false,\n          error: '\\'operations\\' is required and must be a non-empty array.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate file exists\n      if (!fs.existsSync(filePath)) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate operations\n      for (const op of operations) {\n        if (!op.type) {\n          return {\n            success: false,\n            error: 'Each operation must have a \\'type\\'.',\n            toolId: this.id,\n            actionName\n          };\n        }\n\n        if (op.lineStart === undefined || op.lineStart === null || op.lineStart < 1) {\n          return {\n            success: false,\n            error: 'Each operation must have a valid \\'lineStart\\' (>= 1).',\n            toolId: this.id,\n            actionName\n          };\n        }\n\n        if ((op.type === 'replace' || op.type === 'delete') &&\n          (op.lineEnd === undefined || op.lineEnd === null || op.lineEnd < op.lineStart)) {\n          return {\n            success: false,\n            error: `Operation of type '${op.type}' must have a valid 'lineEnd' (>= lineStart).`,\n            toolId: this.id,\n            actionName\n          };\n        }\n\n        if ((op.type === 'replace' || op.type === 'insert') && op.content === undefined) {\n          return {\n            success: false,\n            error: `Operation of type '${op.type}' must have 'content'.`,\n            toolId: this.id,\n            actionName\n          };\n        }\n      }\n\n      // Get line count to validate operations\n      const lineCount = await this.getLineCount(filePath);\n\n      for (const op of operations) {\n        if (op.lineStart > lineCount) {\n          return {\n            success: false,\n            error: `Invalid lineStart: ${op.lineStart} (file has ${lineCount} lines).`,\n            toolId: this.id,\n            actionName\n          };\n        }\n\n        if (op.lineEnd && op.lineEnd > lineCount) {\n          return {\n            success: false,\n            error: `Invalid lineEnd: ${op.lineEnd} (file has ${lineCount} lines).`,\n            toolId: this.id,\n            actionName\n          };\n        }\n      }\n\n      // Sort operations by line number in descending order to avoid line number changes\n      operations.sort((a, b) => b.lineStart - a.lineStart);\n\n      // Create backup if requested\n      let backupPath = '';\n      if (createBackup) {\n        backupPath = await this.createBackup(filePath);\n      }\n\n      // Create temporary output file\n      const tempOutputPath = outputPath || path.join(os.tmpdir(), `codessa-${Date.now()}-${path.basename(filePath)}`);\n\n      try {\n        // Process the file line by line\n        await this.processFileWithOperations(filePath, tempOutputPath, operations);\n\n        // If no output path was specified, replace the original file\n        if (!outputPath) {\n          await fs.promises.rename(tempOutputPath, filePath);\n        }\n\n        return {\n          success: true,\n          output: {\n            message: `Successfully edited ${filePath}`,\n            operationsApplied: operations.length,\n            outputPath: outputPath || filePath,\n            backupCreated: createBackup,\n            backupPath: createBackup ? backupPath : undefined\n          },\n          toolId: this.id,\n          actionName,\n          metadata: {\n            filePath,\n            outputPath: outputPath || filePath,\n            operationsCount: operations.length\n          }\n        };\n      } catch (error) {\n        // Clean up temporary file\n        if (!outputPath && fs.existsSync(tempOutputPath)) {\n          fs.unlinkSync(tempOutputPath);\n        }\n\n        // Restore from backup if something went wrong\n        if (createBackup) {\n          await this.restoreFromBackup(filePath);\n        }\n\n        throw error;\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Stream edit failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Process a file with the specified operations\n     */\n  private async processFileWithOperations(\n    inputPath: string,\n    outputPath: string,\n    operations: Array<{\n      type: string;\n      lineStart: number;\n      lineEnd?: number;\n      content?: string;\n    }>\n  ): Promise<void> {\n    return new Promise<void>((resolve, reject) => {\n      const rl = readline.createInterface({\n        input: fs.createReadStream(inputPath),\n        crlfDelay: Infinity\n      });\n\n      const outputStream = fs.createWriteStream(outputPath);\n      let currentLine = 0;\n      let skipUntilLine = -1;\n\n      // Create a map of line numbers to operations for quick lookup\n      const lineToOp = new Map<number, typeof operations[0]>();\n      for (const op of operations) {\n        lineToOp.set(op.lineStart, op);\n      }\n\n      rl.on('line', (line) => {\n        currentLine++;\n\n        // Skip lines that are part of a replace or delete operation\n        if (currentLine < skipUntilLine) {\n          return;\n        }\n\n        // Reset skip flag\n        if (currentLine === skipUntilLine) {\n          skipUntilLine = -1;\n          return;\n        }\n\n        // Check if there's an operation for this line\n        const operation = lineToOp.get(currentLine);\n\n        if (operation) {\n          switch (operation.type) {\n            case 'replace':\n              // Write the replacement content\n              outputStream.write(operation.content + '\\n');\n              // Skip the original lines\n              skipUntilLine = (operation.lineEnd || currentLine) + 1;\n              break;\n\n            case 'insert':\n              // Write the original line\n              outputStream.write(line + '\\n');\n              // Write the inserted content\n              outputStream.write(operation.content + '\\n');\n              break;\n\n            case 'delete':\n              // Skip the lines to delete\n              skipUntilLine = (operation.lineEnd || currentLine) + 1;\n              break;\n          }\n        } else {\n          // Write the original line\n          outputStream.write(line + '\\n');\n        }\n      });\n\n      rl.on('close', () => {\n        outputStream.end();\n        resolve();\n      });\n\n      rl.on('error', (err) => {\n        outputStream.end();\n        reject(err);\n      });\n\n      outputStream.on('error', (err) => {\n        rl.close();\n        reject(err);\n      });\n    });\n  }\n\n  /**\n     * Execute stream replace action\n     */\n  private async executeStreamReplace(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const pattern = input.pattern as string;\n      const replacement = input.replacement as string;\n      const global = input.global !== false; // Default to true\n      const caseSensitive = input.caseSensitive !== false; // Default to true\n      const outputPath = input.outputPath as string;\n      const createBackup = input.createBackup !== false; // Default to true\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (pattern === undefined) {\n        return {\n          success: false,\n          error: '\\'pattern\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (replacement === undefined) {\n        return {\n          success: false,\n          error: '\\'replacement\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate file exists\n      if (!fs.existsSync(filePath)) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Create backup if requested\n      let backupPath = '';\n      if (createBackup) {\n        backupPath = await this.createBackup(filePath);\n      }\n\n      // Create temporary output file\n      const tempOutputPath = outputPath || path.join(os.tmpdir(), `codessa-${Date.now()}-${path.basename(filePath)}`);\n\n      try {\n        // Create regex from pattern\n        const flags = `${global ? 'g' : ''}${caseSensitive ? '' : 'i'}`;\n        const regex = new RegExp(pattern, flags);\n\n        // Process the file line by line\n        const stats = await this.processFileWithRegex(filePath, tempOutputPath, regex, replacement);\n\n        // If no output path was specified, replace the original file\n        if (!outputPath) {\n          await fs.promises.rename(tempOutputPath, filePath);\n        }\n\n        return {\n          success: true,\n          output: {\n            message: `Successfully replaced text in ${filePath}`,\n            replacements: stats.replacements,\n            linesAffected: stats.linesAffected,\n            outputPath: outputPath || filePath,\n            backupCreated: createBackup,\n            backupPath: createBackup ? backupPath : undefined\n          },\n          toolId: this.id,\n          actionName,\n          metadata: {\n            filePath,\n            outputPath: outputPath || filePath,\n            pattern,\n            replacements: stats.replacements\n          }\n        };\n      } catch (error) {\n        // Clean up temporary file\n        if (!outputPath && fs.existsSync(tempOutputPath)) {\n          fs.unlinkSync(tempOutputPath);\n        }\n\n        // Restore from backup if something went wrong\n        if (createBackup) {\n          await this.restoreFromBackup(filePath);\n        }\n\n        throw error;\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Stream replace failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Process a file with regex replacement\n     */\n  private async processFileWithRegex(\n    inputPath: string,\n    outputPath: string,\n    regex: RegExp,\n    replacement: string\n  ): Promise<{ replacements: number, linesAffected: number }> {\n    return new Promise<{ replacements: number, linesAffected: number }>((resolve, reject) => {\n      const rl = readline.createInterface({\n        input: fs.createReadStream(inputPath),\n        crlfDelay: Infinity\n      });\n\n      const outputStream = fs.createWriteStream(outputPath);\n      let replacements = 0;\n      let linesAffected = 0;\n\n      rl.on('line', (line) => {\n        // Check if the line contains the pattern\n        const originalLine = line;\n        const newLine = line.replace(regex, (match) => {\n          replacements++;\n          return replacement;\n        });\n\n        // Write the line to the output file\n        outputStream.write(newLine + '\\n');\n\n        // Count affected lines\n        if (originalLine !== newLine) {\n          linesAffected++;\n        }\n      });\n\n      rl.on('close', () => {\n        outputStream.end();\n        resolve({ replacements, linesAffected });\n      });\n\n      rl.on('error', (err) => {\n        outputStream.end();\n        reject(err);\n      });\n\n      outputStream.on('error', (err) => {\n        rl.close();\n        reject(err);\n      });\n    });\n  }\n\n  /**\n     * Execute chunk process action\n     */\n  private async executeChunkProcess(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const chunkSize = (input.chunkSize as number) || LargeFileEditTool.DEFAULT_CHUNK_SIZE;\n      const operation = input.operation as string;\n      const pattern = input.pattern as string;\n      const outputPath = input.outputPath as string;\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!operation) {\n        return {\n          success: false,\n          error: '\\'operation\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate file exists\n      if (!fs.existsSync(filePath)) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate operation-specific parameters\n      if (operation === 'extract' && !pattern) {\n        return {\n          success: false,\n          error: '\\'pattern\\' is required for \\'extract\\' operation.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (operation === 'extract' && !outputPath) {\n        return {\n          success: false,\n          error: '\\'outputPath\\' is required for \\'extract\\' operation.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Get file stats\n      const stats = await fs.promises.stat(filePath);\n\n      // Process the file in chunks\n      let result: any;\n\n      switch (operation) {\n        case 'count':\n          result = await this.countLinesInChunks(filePath, chunkSize);\n          break;\n\n        case 'analyze':\n          result = await this.analyzeFileInChunks(filePath, chunkSize);\n          break;\n\n        case 'extract':\n          result = await this.extractPatternsInChunks(filePath, pattern, outputPath, chunkSize);\n          break;\n\n        default:\n          return {\n            success: false,\n            error: `Unsupported operation: ${operation}`,\n            toolId: this.id,\n            actionName\n          };\n      }\n\n      return {\n        success: true,\n        output: {\n          operation,\n          result,\n          fileInfo: {\n            path: filePath,\n            size: stats.size,\n            chunks: Math.ceil(stats.size / chunkSize)\n          }\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          operation,\n          chunkSize\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Chunk process failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Count lines in a file using chunks\n     */\n  private async countLinesInChunks(filePath: string, chunkSize: number): Promise<{ lines: number, processingTime: number }> {\n    return new Promise<{ lines: number, processingTime: number }>(async (resolve, reject) => {\n      try {\n        const startTime = Date.now();\n        let lineCount = 0;\n\n        const rl = readline.createInterface({\n          input: fs.createReadStream(filePath, { highWaterMark: chunkSize }),\n          crlfDelay: Infinity\n        });\n\n        rl.on('line', () => {\n          lineCount++;\n        });\n\n        rl.on('close', () => {\n          const endTime = Date.now();\n          resolve({\n            lines: lineCount,\n            processingTime: endTime - startTime\n          });\n        });\n\n        rl.on('error', (err) => {\n          reject(err);\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  /**\n     * Analyze a file in chunks\n     */\n  private async analyzeFileInChunks(filePath: string, chunkSize: number): Promise<any> {\n    return new Promise<any>(async (resolve, reject) => {\n      try {\n        const startTime = Date.now();\n        let lineCount = 0;\n        let charCount = 0;\n        let wordCount = 0;\n        let emptyLines = 0;\n        let maxLineLength = 0;\n        let minLineLength = Number.MAX_SAFE_INTEGER;\n        let totalLineLength = 0;\n\n        const rl = readline.createInterface({\n          input: fs.createReadStream(filePath, { highWaterMark: chunkSize }),\n          crlfDelay: Infinity\n        });\n\n        rl.on('line', (line) => {\n          lineCount++;\n          charCount += line.length;\n\n          // Count words\n          const words = line.trim().split(/\\s+/).filter(word => word.length > 0);\n          wordCount += words.length;\n\n          // Count empty lines\n          if (line.trim().length === 0) {\n            emptyLines++;\n          }\n\n          // Track line length stats\n          maxLineLength = Math.max(maxLineLength, line.length);\n          minLineLength = Math.min(minLineLength, line.length);\n          totalLineLength += line.length;\n        });\n\n        rl.on('close', () => {\n          const endTime = Date.now();\n\n          // Calculate average line length\n          const avgLineLength = lineCount > 0 ? totalLineLength / lineCount : 0;\n\n          // If no lines were processed, set minLineLength to 0\n          if (lineCount === 0) {\n            minLineLength = 0;\n          }\n\n          resolve({\n            lines: lineCount,\n            characters: charCount,\n            words: wordCount,\n            emptyLines,\n            lineStats: {\n              maxLength: maxLineLength,\n              minLength: minLineLength,\n              avgLength: avgLineLength\n            },\n            processingTime: endTime - startTime\n          });\n        });\n\n        rl.on('error', (err) => {\n          reject(err);\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  /**\n     * Extract patterns from a file in chunks\n     */\n  private async extractPatternsInChunks(filePath: string, pattern: string, outputPath: string, chunkSize: number): Promise<any> {\n    return new Promise<any>(async (resolve, reject) => {\n      try {\n        const startTime = Date.now();\n        let matchCount = 0;\n        let lineCount = 0;\n        let matchedLines = 0;\n\n        // Create regex from pattern\n        const regex = new RegExp(pattern, 'g');\n\n        const rl = readline.createInterface({\n          input: fs.createReadStream(filePath, { highWaterMark: chunkSize }),\n          crlfDelay: Infinity\n        });\n\n        const outputStream = fs.createWriteStream(outputPath);\n\n        rl.on('line', (line) => {\n          lineCount++;\n\n          // Check if the line contains the pattern\n          const matches = line.match(regex);\n\n          if (matches) {\n            matchCount += matches.length;\n            matchedLines++;\n\n            // Write the matched line to the output file\n            outputStream.write(`${line}\\n`);\n          }\n        });\n\n        rl.on('close', () => {\n          outputStream.end();\n          const endTime = Date.now();\n\n          resolve({\n            matchCount,\n            matchedLines,\n            totalLines: lineCount,\n            matchPercentage: lineCount > 0 ? (matchedLines / lineCount) * 100 : 0,\n            outputPath,\n            processingTime: endTime - startTime\n          });\n        });\n\n        rl.on('error', (err) => {\n          outputStream.end();\n          reject(err);\n        });\n\n        outputStream.on('error', (err) => {\n          rl.close();\n          reject(err);\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  /**\n     * Execute get file info action\n     */\n  private async executeGetFileInfo(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const sampleLines = (input.sampleLines as number) || 5;\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate file exists\n      if (!fs.existsSync(filePath)) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Get file stats\n      const stats = await fs.promises.stat(filePath);\n\n      // Get line count\n      const lineCount = await this.getLineCount(filePath);\n\n      // Get sample lines from beginning, middle, and end\n      const samples = await this.getSampleLines(filePath, sampleLines);\n\n      // Detect file type\n      const fileType = this.detectFileType(filePath, samples.beginning.join('\\n'));\n\n      return {\n        success: true,\n        output: {\n          fileInfo: {\n            path: filePath,\n            size: stats.size,\n            sizeFormatted: this.formatFileSize(stats.size),\n            lines: lineCount,\n            type: fileType,\n            created: stats.birthtime,\n            modified: stats.mtime,\n            permissions: this.formatPermissions(stats.mode)\n          },\n          samples\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          size: stats.size,\n          lines: lineCount\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Get file info failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Get sample lines from beginning, middle, and end of a file\n     */\n  private async getSampleLines(filePath: string, count: number): Promise<{ beginning: string[], middle: string[], end: string[] }> {\n    return new Promise<{ beginning: string[], middle: string[], end: string[] }>(async (resolve, reject) => {\n      try {\n        // Get line count first\n        const lineCount = await this.getLineCount(filePath);\n\n        // Adjust count if file has fewer lines\n        const actualCount = Math.min(count, Math.floor(lineCount / 3));\n\n        // Calculate middle start line\n        const middleStart = Math.floor(lineCount / 2) - Math.floor(actualCount / 2);\n\n        const beginning: string[] = [];\n        const middle: string[] = [];\n        const end: string[] = [];\n\n        const rl = readline.createInterface({\n          input: fs.createReadStream(filePath),\n          crlfDelay: Infinity\n        });\n\n        let currentLine = 0;\n\n        rl.on('line', (line) => {\n          currentLine++;\n\n          // Collect beginning lines\n          if (currentLine <= actualCount) {\n            beginning.push(line);\n          }\n\n          // Collect middle lines\n          if (currentLine >= middleStart && currentLine < middleStart + actualCount) {\n            middle.push(line);\n          }\n\n          // Collect end lines\n          if (lineCount - currentLine < actualCount) {\n            end.push(line);\n          }\n        });\n\n        rl.on('close', () => {\n          resolve({ beginning, middle, end });\n        });\n\n        rl.on('error', (err) => {\n          reject(err);\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  /**\n     * Format file size in human-readable format\n     */\n  private formatFileSize(bytes: number): string {\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n    let size = bytes;\n    let unitIndex = 0;\n\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n\n    return `${size.toFixed(2)} ${units[unitIndex]}`;\n  }\n\n  /**\n     * Format file permissions\n     */\n  private formatPermissions(mode: number): string {\n    const octal = (mode & 0o777).toString(8);\n\n    // Convert to rwx format\n    const permissions = [\n      (mode & 0o400) ? 'r' : '-',\n      (mode & 0o200) ? 'w' : '-',\n      (mode & 0o100) ? 'x' : '-',\n      (mode & 0o040) ? 'r' : '-',\n      (mode & 0o020) ? 'w' : '-',\n      (mode & 0o010) ? 'x' : '-',\n      (mode & 0o004) ? 'r' : '-',\n      (mode & 0o002) ? 'w' : '-',\n      (mode & 0o001) ? 'x' : '-'\n    ].join('');\n\n    return `${octal} (${permissions})`;\n  }\n\n  /**\n     * Detect file type based on extension and content\n     */\n  private detectFileType(filePath: string, sampleContent: string): string {\n    const ext = path.extname(filePath).toLowerCase();\n\n    // Check by extension first\n    const extensionMap: Record<string, string> = {\n      '.js': 'JavaScript',\n      '.jsx': 'JavaScript (React)',\n      '.ts': 'TypeScript',\n      '.tsx': 'TypeScript (React)',\n      '.py': 'Python',\n      '.java': 'Java',\n      '.c': 'C',\n      '.cpp': 'C++',\n      '.cs': 'C#',\n      '.go': 'Go',\n      '.rb': 'Ruby',\n      '.php': 'PHP',\n      '.swift': 'Swift',\n      '.rs': 'Rust',\n      '.html': 'HTML',\n      '.css': 'CSS',\n      '.json': 'JSON',\n      '.xml': 'XML',\n      '.md': 'Markdown',\n      '.sh': 'Shell Script',\n      '.txt': 'Text',\n      '.csv': 'CSV',\n      '.sql': 'SQL'\n    };\n\n    if (extensionMap[ext]) {\n      return extensionMap[ext];\n    }\n\n    // If extension doesn't give us a clear answer, check content\n    if (sampleContent.startsWith('<?xml')) {\n      return 'XML';\n    }\n\n    if (sampleContent.startsWith('<!DOCTYPE html') || sampleContent.includes('<html')) {\n      return 'HTML';\n    }\n\n    try {\n      JSON.parse(sampleContent);\n      return 'JSON';\n    } catch (e) {\n      // Not JSON\n    }\n\n    // Check if it's a binary file\n    for (let i = 0; i < Math.min(sampleContent.length, 100); i++) {\n      const charCode = sampleContent.charCodeAt(i);\n      if (charCode === 0 || (charCode < 32 && ![9, 10, 13].includes(charCode))) {\n        return 'Binary';\n      }\n    }\n\n    // Default to text\n    return 'Text';\n  }\n\n  /**\n     * Execute split file action\n     */\n  private async executeSplitFile(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const outputDir = input.outputDir as string;\n      const splitBy = input.splitBy as string;\n      const value = input.value as number;\n      const prefix = (input.prefix as string) || path.basename(filePath);\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!outputDir) {\n        return {\n          success: false,\n          error: '\\'outputDir\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!splitBy) {\n        return {\n          success: false,\n          error: '\\'splitBy\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (value === undefined || value === null || value <= 0) {\n        return {\n          success: false,\n          error: '\\'value\\' is required and must be greater than 0.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate file exists\n      if (!fs.existsSync(filePath)) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Create output directory if it doesn't exist\n      if (!fs.existsSync(outputDir)) {\n        await fs.promises.mkdir(outputDir, { recursive: true });\n      }\n\n      // Split the file\n      let result: any;\n\n      if (splitBy === 'lines') {\n        result = await this.splitFileByLines(filePath, outputDir, value, prefix);\n      } else if (splitBy === 'size') {\n        result = await this.splitFileBySize(filePath, outputDir, value, prefix);\n      } else {\n        return {\n          success: false,\n          error: `Unsupported split method: ${splitBy}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      return {\n        success: true,\n        output: {\n          splitBy,\n          value,\n          result\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          outputDir,\n          splitBy,\n          value\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Split file failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Split a file by lines\n     */\n  private async splitFileByLines(filePath: string, outputDir: string, linesPerFile: number, prefix: string): Promise<any> {\n    return new Promise<any>(async (resolve, reject) => {\n      try {\n        const startTime = Date.now();\n        const outputFiles: string[] = [];\n\n        const rl = readline.createInterface({\n          input: fs.createReadStream(filePath),\n          crlfDelay: Infinity\n        });\n\n        let currentFileIndex = 1;\n        let currentLineCount = 0;\n        let totalLineCount = 0;\n        let outputStream: fs.WriteStream | null = null;\n        let currentOutputPath = '';\n\n        const createNewOutputFile = () => {\n          // Close previous stream if it exists\n          if (outputStream) {\n            outputStream.end();\n          }\n\n          // Create new output file path\n          const paddedIndex = currentFileIndex.toString().padStart(5, '0');\n          currentOutputPath = path.join(outputDir, `${prefix}.${paddedIndex}`);\n          outputFiles.push(currentOutputPath);\n\n          // Create new output stream\n          outputStream = fs.createWriteStream(currentOutputPath);\n          currentFileIndex++;\n          currentLineCount = 0;\n        };\n\n        // Create the first output file\n        createNewOutputFile();\n\n        rl.on('line', (line) => {\n          totalLineCount++;\n          currentLineCount++;\n\n          // Write the line to the current output file\n          outputStream!.write(line + '\\n');\n\n          // Check if we need to create a new output file\n          if (currentLineCount >= linesPerFile) {\n            createNewOutputFile();\n          }\n        });\n\n        rl.on('close', () => {\n          // Close the last output stream\n          if (outputStream) {\n            outputStream.end();\n          }\n\n          const endTime = Date.now();\n\n          resolve({\n            totalLines: totalLineCount,\n            totalFiles: outputFiles.length,\n            outputFiles,\n            linesPerFile,\n            processingTime: endTime - startTime\n          });\n        });\n\n        rl.on('error', (err) => {\n          // Close the output stream\n          if (outputStream) {\n            outputStream.end();\n          }\n\n          reject(err);\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  /**\n     * Split a file by size\n     */\n  private async splitFileBySize(filePath: string, outputDir: string, bytesPerFile: number, prefix: string): Promise<any> {\n    return new Promise<any>(async (resolve, reject) => {\n      try {\n        const startTime = Date.now();\n        const outputFiles: string[] = [];\n\n        const fileStream = fs.createReadStream(filePath, {\n          highWaterMark: Math.min(bytesPerFile, 1024 * 1024) // Use smaller chunks for better control\n        });\n\n        let currentFileIndex = 1;\n        let currentFileSize = 0;\n        let totalBytesProcessed = 0;\n        let outputStream: fs.WriteStream | null = null;\n        let currentOutputPath = '';\n\n        const createNewOutputFile = () => {\n          // Close previous stream if it exists\n          if (outputStream) {\n            outputStream.end();\n          }\n\n          // Create new output file path\n          const paddedIndex = currentFileIndex.toString().padStart(5, '0');\n          currentOutputPath = path.join(outputDir, `${prefix}.${paddedIndex}`);\n          outputFiles.push(currentOutputPath);\n\n          // Create new output stream\n          outputStream = fs.createWriteStream(currentOutputPath);\n          currentFileIndex++;\n          currentFileSize = 0;\n        };\n\n        // Create the first output file\n        createNewOutputFile();\n\n        fileStream.on('data', (chunk) => {\n          const chunkSize = chunk.length;\n          totalBytesProcessed += chunkSize;\n\n          // If adding this chunk would exceed the size limit, split it\n          if (currentFileSize + chunkSize > bytesPerFile) {\n            // Calculate how much of the chunk can fit in the current file\n            const bytesRemaining = bytesPerFile - currentFileSize;\n\n            if (bytesRemaining > 0) {\n              // Write the portion that fits\n              outputStream!.write(chunk.slice(0, bytesRemaining));\n              currentFileSize += bytesRemaining;\n            }\n\n            // Create a new output file\n            createNewOutputFile();\n\n            // Write the rest of the chunk to the new file\n            const remainingChunk = chunk.slice(bytesRemaining);\n            if (remainingChunk.length > 0) {\n              outputStream!.write(remainingChunk);\n              currentFileSize += remainingChunk.length;\n            }\n          } else {\n            // Write the entire chunk\n            outputStream!.write(chunk);\n            currentFileSize += chunkSize;\n          }\n\n          // Check if we need to create a new output file\n          if (currentFileSize >= bytesPerFile) {\n            createNewOutputFile();\n          }\n        });\n\n        fileStream.on('end', () => {\n          // Close the last output stream\n          if (outputStream) {\n            outputStream.end();\n          }\n\n          const endTime = Date.now();\n\n          resolve({\n            totalBytes: totalBytesProcessed,\n            totalFiles: outputFiles.length,\n            outputFiles,\n            bytesPerFile,\n            processingTime: endTime - startTime\n          });\n        });\n\n        fileStream.on('error', (err) => {\n          // Close the output stream\n          if (outputStream) {\n            outputStream.end();\n          }\n\n          reject(err);\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n}\n"]}