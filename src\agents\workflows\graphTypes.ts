import { Agent } from '../agentUtilities/agent';
import { ITool, StructuredTool } from '../../tools/tool.ts.backup';
import type { OperationMode, Methodology } from './types';

export type NodeType = 'input' | 'output' | 'agent' | 'tool' | 'decision' | 'process';
export type EdgeType =
    // Core flow types
    | 'success'        // Successful execution path
    | 'failure'        // Failure/error path
    | 'default'        // Default pathway

    // Structural relationships
    | 'composition'    // Whole-part relationship
    | 'dependency'     // Dependency relationship
    | 'association'    // General association

    // Special workflow types
    | 'feedback'       // Feedback loop
    | 'validation'     // Validation pathway
    | 'optimization'   // Optimization path
    | 'integration'    // Integration point

    // Control flow
    | 'conditional'    // Conditional branching
    | 'parallel'       // Parallel execution
    | 'synchronization' // Synchronization point

    // Operation modes
    | 'memory'         // Memory-enhanced operations
    | 'document-qa'    // Document Q&A
    | 'refactor'       // Code refactoring
    | 'debug'          // Code debugging
    | 'chat'           // Conversational chat
    | 'ask'            // Question answering with context
    | 'edit'           // Code editing
    | 'codegen'        // Code generation
    | 'multi-agent'    // Multi-agent collaboration

    // Methodologies
    | 'agile'          // Agile methodology
    | 'xp'             // Extreme Programming
    | 'scrum'          // Scrum framework

    // New workflow types
    | 'pr-creation'        // PR creation workflow
    | 'pr-review'          // PR review workflow
    | 'checkpoint'         // Checkpoint system
    | 'mcp'                // Model Context Protocol
    | 'pattern-refactoring' // Pattern-based refactoring
    | 'technical-debt';    // Technical debt reduction


export interface GraphNode {
    id: string;
    label: string;
    type: NodeType;
    description?: string;
    agent?: Agent;
    tool?: ITool;
    data?: any;
}

export interface GraphEdge {
    name: string;
    source: string;
    target: string;
    type: EdgeType;
    condition?: (_state: any) => boolean | Promise<boolean>;
    data?: any;
}

export interface GraphDefinition {
    id: string;
    name: string;
    description: string;
    version: string;
    nodes: GraphNode[];
    edges: GraphEdge[];
    startNodeId: string;
    operationMode: OperationMode;
    methodology?: Methodology;
    tags?: string[];
    agentNodes?: GraphNode[];
    toolNodes?: GraphNode[];
}