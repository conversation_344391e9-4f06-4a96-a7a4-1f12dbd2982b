"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.knowledgebaseManager = exports.KnowledgebaseManager = void 0;
const logger_1 = require("../logger");
/**
 * Knowledgebase Manager for handling knowledge operations
 */
class KnowledgebaseManager {
    static instance;
    knowledgebase = [];
    constructor() { }
    /**
     * Get the singleton instance
     */
    static getInstance() {
        if (!KnowledgebaseManager.instance) {
            KnowledgebaseManager.instance = new KnowledgebaseManager();
        }
        return KnowledgebaseManager.instance;
    }
    /**
     * Get relevant knowledge for a query
     */
    async getRelevantKnowledge(query) {
        logger_1.logger.info(`Getting relevant knowledge for query: ${query.substring(0, 50)}...`);
        if (this.knowledgebase.length === 0) {
            return '';
        }
        // Simple implementation - in a real system, this would use embeddings and similarity search
        const relevantItems = this.knowledgebase
            .filter(item => this.isRelevant(item.content, query))
            .slice(0, 3); // Limit to top 3 results
        if (relevantItems.length === 0) {
            return '';
        }
        // Format the knowledge for inclusion in prompts
        return relevantItems
            .map(item => `--- Knowledge Item ---\n${item.content}\n-------------------`)
            .join('\n\n');
    }
    /**
     * Add knowledge to the knowledgebase
     */
    async addKnowledge(content, metadata) {
        this.knowledgebase.push({
            content,
            metadata,
            timestamp: Date.now()
        });
        logger_1.logger.info(`Added knowledge to knowledgebase: ${content.substring(0, 50)}...`);
    }
    /**
     * Search the knowledgebase
     */
    async searchKnowledge(query) {
        return this.knowledgebase
            .filter(item => this.isRelevant(item.content, query))
            .map(item => ({
            content: item.content,
            metadata: item.metadata,
            timestamp: item.timestamp,
            relevance: this.calculateRelevance(item.content, query)
        }))
            .sort((a, b) => b.relevance - a.relevance);
    }
    /**
     * Simple relevance check - in a real system, this would use embeddings and similarity search
     */
    isRelevant(content, query) {
        const normalizedContent = content.toLowerCase();
        const normalizedQuery = query.toLowerCase();
        // Check if any words from the query appear in the content
        const queryWords = normalizedQuery.split(/\s+/);
        return queryWords.some(word => normalizedContent.includes(word));
    }
    /**
     * Calculate relevance score - in a real system, this would use embeddings and similarity search
     */
    calculateRelevance(content, query) {
        const normalizedContent = content.toLowerCase();
        const normalizedQuery = query.toLowerCase();
        // Count how many query words appear in the content
        const queryWords = normalizedQuery.split(/\s+/);
        const matchingWords = queryWords.filter(word => normalizedContent.includes(word));
        return matchingWords.length / queryWords.length;
    }
}
exports.KnowledgebaseManager = KnowledgebaseManager;
// Export singleton instance
exports.knowledgebaseManager = KnowledgebaseManager.getInstance();
//# sourceMappingURL=knowledgebaseManager.js.map