{"version": 3, "file": "llmService.js", "sourceRoot": "", "sources": ["../../src/llm/llmService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,sCAAmC;AACnC,sCAA6D;AAC7D,uDAAoD;AACpD,gEAAmE;AAEnE,uBAAuB;AACvB,yBAAyB;AACzB,+DAA4D;AAC5D,qEAAkE;AAClE,mEAAgE;AAChE,qEAAkE;AAClE,+DAA4D;AAC5D,mEAAgE;AAEhE,kCAAkC;AAClC,+DAA4D;AAC5D,mEAAgE;AAEhE,uBAAuB;AACvB,uEAAoE;AACpE,yEAAsE;AAEtE,gCAAgC;AAChC,qEAAkE;AAClE,qEAAkE;AAClE,+DAA4D;AAC5D,yEAAsE;AACtE,qEAAkE;AAClE,yDAAsD;AACtD,+DAA4D;AAC5D,qEAAkE;AAClE,uEAAoE;AACpE,uEAAoE;AAEpE,2BAA2B;AAC3B,uEAAsE;AAEtE;;GAEG;AACH,MAAM,UAAU;IACN,SAAS,GAAG,IAAI,GAAG,EAAwB,CAAC;IAC5C,mBAAmB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;IACrD,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;IACrD,OAAO,CAAsC;IAErD,wDAAwD;IAChD,qBAAqB,CAAC,WAAmB;QAC/C,IAAI,CAAC;YACH,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;QACE,+DAA+D;QAC/D,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC1C,eAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBACrE,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;SAGK;IACE,UAAU,CAAC,OAAgC;QAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAE3C,kCAAkC;QAClC,iCAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAErC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,OAAO;QACT,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAEvB,sCAAsC;QACtC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,IAAI,gBAAgB,GAAa,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtE,eAAM,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;YACzF,gBAAgB,GAAG,EAAE,CAAC;QACxB,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,kDAAkD,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QAEpI,4BAA4B;QAC5B,MAAM,iBAAiB,GAAG;YACxB,yBAAyB;YACzB,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,+BAAc,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAClE,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,qCAAiB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YACxE,EAAE,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,mCAAgB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YACtE,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,qCAAiB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YACxE,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,+BAAc,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAClE,EAAE,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,mCAAgB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAEtE,kCAAkC;YAClC,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,+BAAc,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAClE,EAAE,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,mCAAgB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAEtE,uBAAuB;YACvB,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,uCAAkB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAC1E,EAAE,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,yCAAmB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAE5E,gCAAgC;YAChC,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,qCAAiB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YACxE,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,qCAAiB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YACxE,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,+BAAc,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAClE,EAAE,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,yCAAmB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAC5E,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,qCAAiB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YACxE,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,yBAAW,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAC5D,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,+BAAc,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAClE,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,qCAAiB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YACxE,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,uCAAkB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAC1E,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,uCAAkB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;YAE1E,2BAA2B;YAC3B,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,yCAAoB,CAAC,IAAI,CAAC,OAAQ,CAAC,EAAE;SAC7E,CAAC;QAEF,8BAA8B;QAC9B,MAAM,oBAAoB,GAAa,EAAE,CAAC;QAC1C,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,kCAAkC;QAClC,KAAK,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,iBAAiB,EAAE,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnC,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,4BAA4B,CAAC,CAAC;gBAClE,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC1B,SAAS;YACX,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;YAC5C,IAAI,CAAC;gBACH,qEAAqE;gBACrE,kFAAkF;gBAClF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBACvE,MAAM,uBAAuB,GAAG,aAAa,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE9F,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC1C,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,+CAA+C,CAAC,CAAC;oBAC5E,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC1B,SAAS;gBACX,CAAC;gBAED,kCAAkC;gBAClC,IAAI,iBAAiB,GAAG,KAAK,CAAC;gBAC9B,IAAI,EAAE,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBAC3E,eAAM,CAAC,IAAI,CAAC,YAAY,EAAE,+DAA+D,CAAC,CAAC;oBAC3F,iBAAiB,GAAG,IAAI,CAAC;gBAC3B,CAAC;qBAAM,IAAI,EAAE,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACpE,eAAM,CAAC,IAAI,CAAC,YAAY,EAAE,oDAAoD,CAAC,CAAC;oBAChF,iBAAiB,GAAG,IAAI,CAAC;gBAC3B,CAAC;gBAED,IAAI,iBAAiB,EAAE,CAAC;oBACtB,oBAAoB,CAAC,IAAI,CAAC,YAAY,EAAE,qCAAqC,CAAC,CAAC;oBAC/E,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,OAAO,EAAE,CAAC;gBAC3B,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,aAAa,GAAG,QAAwB,CAAC;oBAC/C,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;oBACrC,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;oBAEvD,iDAAiD;oBACjD,IAAI,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;wBAC5B,IAAI,CAAC;4BACH,wEAAwE;4BACxE,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,KAAK,QAAQ,EAAE,CAAC;gCACzC,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;gCAClE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oCACnB,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,4BAA4B,CAAC,CAAC;oCAC9D,sDAAsD;oCACtD,IAAI,CAAC;wCACH,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,UAAU,EAAE,CAAC;wCAChD,eAAM,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;oCACtE,CAAC;oCAAC,OAAO,UAAU,EAAE,CAAC;wCACpB,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,UAAU,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;oCAC5H,CAAC;gCACH,CAAC;qCAAM,CAAC;oCACN,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,4BAA4B,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;gCACjF,CAAC;4BACH,CAAC;iCAAM,CAAC;gCACN,iDAAiD;gCACjD,IAAI,CAAC;oCACH,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,UAAU,EAAE,CAAC;oCAChD,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;oCAC5D,IAAI,CAAC,OAAO;wCAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;oCAEvD,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oCAC3D,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wCACnB,eAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,uBAAuB,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC;oCAC/E,CAAC;yCAAM,CAAC;wCACN,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oCAClC,CAAC;gCACH,CAAC;gCAAC,OAAO,UAAU,EAAE,CAAC;oCACpB,oDAAoD;oCACpD,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,UAAU,CAAC,CAAC;oCACnE,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,IAAI,SAAS,CAAC;oCACxD,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oCAC3D,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wCACnB,eAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;oCAClD,CAAC;yCAAM,CAAC;wCACN,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oCAClC,CAAC;gCACH,CAAC;4BACH,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAc,EAAE,CAAC;4BACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BAC5E,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,YAAY,EAAE,CAAC,CAAC;4BACpE,oBAAoB,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,YAAY,EAAE,CAAC,CAAC;wBAC/D,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,IAAI,CAAC,YAAY,EAAE,gCAAgC,CAAC,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5E,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,YAAY,EAAE,CAAC,CAAC;gBACnE,oBAAoB,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,YAAY,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC;QAC7D,eAAM,CAAC,IAAI,CAAC,eAAe,gBAAgB,mBAAmB,eAAe,cAAc,CAAC,CAAC;QAE7F,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,eAAM,CAAC,IAAI,CAAC,WAAW,gBAAgB,CAAC,MAAM,wBAAwB,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvG,CAAC;QAED,oDAAoD;QACpD,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACpD,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,eAAM,CAAC,IAAI,CAAC,OAAO,KAAK,EAAE,CAAC,CAAC,CAAC;QACrE,CAAC;QAED,+CAA+C;QAC/C,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;QAEhC,wFAAwF;QACxF,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC3B,eAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;YAC1F,qFAAqF;QACvF,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACnC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,EAAU,EAAE,eAAmC;QAC/E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,eAAe,EAAE,CAAC;YAEnC,yDAAyD;YACzD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAChC,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;gBAEvD,qDAAqD;gBACrD,IAAI,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;oBAC5B,IAAI,CAAC;wBACH,oEAAoE;wBACpE,yDAAyD;wBACzD,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,KAAK,QAAQ,EAAE,CAAC;4BACzC,qCAAqC;4BACrC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;4BACjE,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gCACvB,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;gCAC3C,eAAM,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;4BACtE,CAAC;iCAAM,CAAC;gCACN,mDAAmD;gCACnD,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,sBAAsB,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;4BACzE,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,mDAAmD;4BACnD,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;4BAC3C,eAAM,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;wBACtE,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,4EAA4E;wBAC5E,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;wBAC9E,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,YAAY,EAAE,CAAC,CAAC;wBAEzE,iDAAiD;wBACjD,MAAM,cAAc,GAAG,wBAAwB,EAAE,gGAAgG,CAAC;wBAElJ,wEAAwE;wBACxE,oFAAoF;wBACpF,IAAI,CAAC,IAAA,mCAAmB,EAAC,cAAc,CAAC;4BACxB,EAAE,KAAK,QAAQ;4BACf,EAAE,KAAK,UAAU;4BACjB,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;4BACxC,kEAAkE;4BAClE,2EAA2E;4BAC3E,qDAAqD;4BACrD,MAAM,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;4BAC9D,kBAAkB,CAAC,cAAc,CAAC,CAAC;wBACrC,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,cAAc,YAAY,EAAE,CAAC,CAAC;YAEpE,4DAA4D;YAC5D,oDAAoD;YACpD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,eAAe,EAAE,CAAC;gBACnC,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;oBAChC,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,iCAAiC,CAAC,CAAC;gBAC1E,CAAC;YACH,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,8DAA8D;gBAC9D,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACjG,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACL,gBAAgB,CAAC,QAAsB;QACrC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,eAAM,CAAC,IAAI,CAAC,qBAAqB,QAAQ,CAAC,UAAU,uCAAuC,CAAC,CAAC;QAC/F,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAElD,0CAA0C;QAC1C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,iCAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;SAEK;IACL,WAAW,CAAC,UAAkB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,oBAAoB,CAAC,OAAY;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEtD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,eAAM,CAAC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC;YACpE,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,iEAAiE;QACjE,MAAM,OAAO,GAAG,iCAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,eAAM,CAAC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,QAAQ,GAAG,4DAA4D,CAAC,CAAC;YACjH,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,wCAAwC;QACxC,MAAM,iBAAiB,GAAG,iCAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,oBAAoB,EAAE,CAAC;QAC3F,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,iCAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1D,yEAAyE;QACzE,IAAI,QAAQ,IAAI,MAAM,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,wEAAwE;QACxE,eAAM,CAAC,IAAI,CAAC,qBAAqB,iBAAiB,4EAA4E,CAAC,CAAC;QAEhI,kCAAkC;QAClC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1D,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,oEAAoE;YACpE,MAAM,aAAa,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAC9C,CAAC,CAAC,UAAU,KAAK,QAAQ,IAAI,CAAC,CAAC,UAAU,KAAK,UAAU,CACzD,CAAC;YAEF,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,aAAa,CAAC;YACvB,CAAC;YAED,8CAA8C;YAC9C,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QAED,uDAAuD;QACvD,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC1D,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,yDAAyD;QACzD,eAAM,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QACvF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,kBAAkB,CAAC,UAAkB;QACzC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,eAAM,CAAC,IAAI,CAAC,0CAA0C,UAAU,cAAc,CAAC,CAAC;YAChF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,iCAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YACjG,IAAI,OAAO,EAAE,CAAC;gBACZ,eAAM,CAAC,IAAI,CAAC,2BAA2B,UAAU,EAAE,CAAC,CAAC;gBACrD,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,EAAE,CAAC,CAAC;gBAChE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACL,eAAe;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;SAEK;IACL,sBAAsB;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;aACvC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,gBAAgB,GAAmB,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAG,iCAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1D,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,IAAI,MAAM,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzD,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;SAEK;IACL,eAAe;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,qBAAqB;QACzB,OAAO,IAAA,8BAAqB,GAAE,CAAC;IACjC,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,oBAAoB,CAAC,UAAkB,EAAE,OAAY;QACzD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,eAAM,CAAC,IAAI,CAAC,0CAA0C,GAAG,UAAU,GAAG,aAAa,CAAC,CAAC;YACrF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACrC,eAAM,CAAC,IAAI,CAAC,qCAAqC,GAAG,UAAU,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8CAA8C,GAAG,UAAU,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YACvF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACL,iBAAiB,CAAC,UAAkB;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,QAAQ,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;CACF;AAEY,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ILLMProvider, LLMProviderConfig } from './llmProvider';\nimport { logger } from '../logger';\nimport { LLMConfig, getDefaultModelConfig } from '../config';\nimport { providerManager } from './providerManager';\nimport { shouldSuppressError } from '../ui/feedback/notifications';\n\n// Import all providers\n// Standard API providers\nimport { OpenAIProvider } from './providers/openaiProvider';\nimport { AnthropicProvider } from './providers/anthropicProvider';\nimport { GoogleAIProvider } from './providers/googleAIProvider';\nimport { MistralAIProvider } from './providers/mistralAIProvider';\nimport { CohereProvider } from './providers/cohereProvider';\nimport { DeepSeekProvider } from './providers/deepseekProvider';\n\n// Local and self-hosted providers\nimport { OllamaProvider } from './providers/ollamaProvider';\nimport { LMStudioProvider } from './providers/lmstudioProvider';\n\n// Aggregator providers\nimport { OpenRouterProvider } from './providers/openrouterProvider';\nimport { HuggingFaceProvider } from './providers/huggingfaceProvider';\n\n// Code-specific model providers\nimport { StarCoderProvider } from './providers/starcoderProvider';\nimport { CodeLlamaProvider } from './providers/codeLlamaProvider';\nimport { ReplitProvider } from './providers/replitProvider';\nimport { WizardCoderProvider } from './providers/wizardCoderProvider';\nimport { XwinCoderProvider } from './providers/xwinCoderProvider';\nimport { PhiProvider } from './providers/phiProvider';\nimport { YiCodeProvider } from './providers/yiCodeProvider';\nimport { CodeGemmaProvider } from './providers/codeGemmaProvider';\nimport { SantaCoderProvider } from './providers/santaCoderProvider';\nimport { StableCodeProvider } from './providers/stableCodeProvider';\n\n// Additional API providers\nimport { PerplexityAIProvider } from './providers/perplexityProvider';\n\n/**\n * Service that manages LLM providers and model selection\n */\nclass LLMService {\n  private providers = new Map<string, ILLMProvider>();\n  private _onProvidersChanged = new vscode.EventEmitter<void>();\n  readonly onProvidersChanged = this._onProvidersChanged.event;\n  private context: vscode.ExtensionContext | undefined;\n  \n  // Helper function to check if a dependency is available\n  private isDependencyAvailable(packageName: string): boolean {\n    try {\n      require.resolve(packageName);\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  constructor() {\n    // Listen for configuration changes that might affect providers\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm')) {\n        logger.info('LLM configuration changed, re-initializing providers.');\n        this.reinitializeProviders();\n      }\n    });\n  }\n\n  /**\n     * Initialize the service with the extension context\n     * This must be called before using any provider functionality\n     */\n  public initialize(context: vscode.ExtensionContext): void {\n    this.context = context;\n    logger.info('Initializing LLM service...');\n\n    // Initialize the provider manager\n    providerManager.getInstance(context);\n\n    this.initializeProviders();\n  }\n\n  private async initializeProviders() {\n    if (!this.context) {\n      logger.error('LLMService not initialized with context');\n      return;\n    }\n\n    // Clear existing providers\n    this.providers.clear();\n\n    // Get enabled providers from settings\n    const config = vscode.workspace.getConfiguration('codessa.llm');\n    let enabledProviders: string[] = config.get('enabledProviders') || ['ollama'];\n    if (!Array.isArray(enabledProviders) || enabledProviders.length === 0) {\n      logger.warn('No providers enabled in settings. All LLM functionality will be disabled.');\n      enabledProviders = [];\n    }\n\n    logger.info(`Initializing LLM providers. Enabled providers: ${enabledProviders.length > 0 ? enabledProviders.join(', ') : 'none'}`);\n\n    // Define provider factories\n    const providerFactories = [\n      // Standard API providers\n      { id: 'openai', factory: () => new OpenAIProvider(this.context!) },\n      { id: 'anthropic', factory: () => new AnthropicProvider(this.context!) },\n      { id: 'googleai', factory: () => new GoogleAIProvider(this.context!) },\n      { id: 'mistralai', factory: () => new MistralAIProvider(this.context!) },\n      { id: 'cohere', factory: () => new CohereProvider(this.context!) },\n      { id: 'deepseek', factory: () => new DeepSeekProvider(this.context!) },\n\n      // Local and self-hosted providers\n      { id: 'ollama', factory: () => new OllamaProvider(this.context!) },\n      { id: 'lmstudio', factory: () => new LMStudioProvider(this.context!) },\n\n      // Aggregator providers\n      { id: 'openrouter', factory: () => new OpenRouterProvider(this.context!) },\n      { id: 'huggingface', factory: () => new HuggingFaceProvider(this.context!) },\n\n      // Code-specific model providers\n      { id: 'starcoder', factory: () => new StarCoderProvider(this.context!) },\n      { id: 'codellama', factory: () => new CodeLlamaProvider(this.context!) },\n      { id: 'replit', factory: () => new ReplitProvider(this.context!) },\n      { id: 'wizardcoder', factory: () => new WizardCoderProvider(this.context!) },\n      { id: 'xwincoder', factory: () => new XwinCoderProvider(this.context!) },\n      { id: 'phi', factory: () => new PhiProvider(this.context!) },\n      { id: 'yicode', factory: () => new YiCodeProvider(this.context!) },\n      { id: 'codegemma', factory: () => new CodeGemmaProvider(this.context!) },\n      { id: 'santacoder', factory: () => new SantaCoderProvider(this.context!) },\n      { id: 'stablecode', factory: () => new StableCodeProvider(this.context!) },\n\n      // Additional API providers\n      { id: 'perplexity', factory: () => new PerplexityAIProvider(this.context!) }\n    ];\n\n    // Track initialization errors\n    const initializationErrors: string[] = [];\n    const skippedProviders: string[] = [];\n\n    // Register only enabled providers\n    for (const { id, factory } of providerFactories) {\n      if (!enabledProviders.includes(id)) {\n        logger.debug(`Skipping provider ${id} (not enabled in settings)`);\n        skippedProviders.push(id);\n        continue;\n      }\n\n      logger.info(`Initializing provider: ${id}`);\n      try {\n        // Double-check that the provider is still enabled before creating it\n        // This prevents race conditions where settings might change during initialization\n        const currentConfig = vscode.workspace.getConfiguration('codessa.llm');\n        const currentEnabledProviders = currentConfig.get<string[]>('enabledProviders') || ['ollama'];\n\n        if (!currentEnabledProviders.includes(id)) {\n          logger.debug(`Provider ${id} was disabled during initialization, skipping`);\n          skippedProviders.push(id);\n          continue;\n        }\n        \n        // Check for required dependencies\n        let missingDependency = false;\n        if (id === 'anthropic' && !this.isDependencyAvailable('@anthropic-ai/sdk')) {\n          logger.warn(`Provider ${id} requires @anthropic-ai/sdk which is not installed. Skipping.`);\n          missingDependency = true;\n        } else if (id === 'openai' && !this.isDependencyAvailable('openai')) {\n          logger.warn(`Provider ${id} requires openai which is not installed. Skipping.`);\n          missingDependency = true;\n        }\n        \n        if (missingDependency) {\n          initializationErrors.push(`Provider ${id}: Required dependency not installed`);\n          continue;\n        }\n\n        const provider = factory();\n        if (provider) {\n          const typedProvider = provider as ILLMProvider;\n          this.registerProvider(typedProvider);\n          logger.info(`Successfully registered provider: ${id}`);\n\n          // Only test connection if provider is configured\n          if (provider.isConfigured()) {\n            try {\n              // For local providers, use silent connection test to avoid error popups\n              if (id === 'lmstudio' || id === 'ollama') {\n                const result = await typedProvider.testConnection('silent-check');\n                if (result.success) {\n                  logger.info(`Local provider ${id} is running and configured`);\n                  // Only try to list models if connection is successful\n                  try {\n                    const models = await typedProvider.listModels();\n                    logger.info(`Provider ${id} has ${models.length} models available`);\n                  } catch (modelError) {\n                    logger.debug(`Failed to list models for ${id}: ${modelError instanceof Error ? modelError.message : String(modelError)}`);\n                  }\n                } else {\n                  logger.debug(`Local provider ${id} connection test failed: ${result.message}`);\n                }\n              } else {\n                // For API providers, try to get a model ID first\n                try {\n                  const models = await typedProvider.listModels();\n                  const modelId = typedProvider.defaultModel || models[0]?.id;\n                  if (!modelId) throw new Error('No model ID available');\n\n                  const result = await typedProvider.testConnection(modelId);\n                  if (result.success) {\n                    logger.info(`API provider ${id} is configured with ${models.length} models`);\n                  } else {\n                    throw new Error(result.message);\n                  }\n                } catch (modelError) {\n                  // If we can't list models, try with a default model\n                  logger.debug('Failed to list models, trying default:', modelError);\n                  const modelId = typedProvider.defaultModel || 'default';\n                  const result = await typedProvider.testConnection(modelId);\n                  if (result.success) {\n                    logger.info(`API provider ${id} is configured`);\n                  } else {\n                    throw new Error(result.message);\n                  }\n                }\n              }\n            } catch (error: unknown) {\n              const errorMessage = error instanceof Error ? error.message : String(error);\n              logger.warn(`Failed to initialize provider ${id}: ${errorMessage}`);\n              initializationErrors.push(`Provider ${id}: ${errorMessage}`);\n            }\n          } else {\n            logger.info(`Provider ${id} registered but not configured`);\n          }\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        logger.error(`Failed to register provider ${id}: ${errorMessage}`);\n        initializationErrors.push(`Provider ${id}: ${errorMessage}`);\n      }\n    }\n\n    // Log summary of initialization\n    const initializedCount = this.providers.size;\n    const configuredCount = this.getConfiguredProviders().length;\n    logger.info(`Initialized ${initializedCount} LLM providers (${configuredCount} configured)`);\n    \n    if (skippedProviders.length > 0) {\n      logger.info(`Skipped ${skippedProviders.length} disabled providers: ${skippedProviders.join(', ')}`);\n    }\n\n    // If there were any initialization errors, log them\n    if (initializationErrors.length > 0) {\n      logger.warn('Some providers failed to initialize:');\n      initializationErrors.forEach(error => logger.warn(`  - ${error}`));\n    }\n\n    // Notify listeners that providers have changed\n    this._onProvidersChanged.fire();\n\n    // If no providers were successfully initialized, log a warning but don't throw an error\n    if (initializedCount === 0) {\n      logger.warn('No LLM providers were initialized. Users can enable providers in settings.');\n      // Don't throw an error - this allows the extension to work without any LLM providers\n    }\n  }\n\n  private async reinitializeProviders() {\n    await this.initializeProviders();\n  }\n\n  /**\n     * Attempt to register a provider with error handling\n     */\n  private async tryRegisterProvider(id: string, providerFactory: () => ILLMProvider): Promise<void> {\n    try {\n      const provider = providerFactory();\n\n      // Only register if the provider initializes successfully\n      if (provider) {\n        this.registerProvider(provider);\n        logger.info(`Successfully registered provider: ${id}`);\n\n        // Try to initialize models if provider is configured\n        if (provider.isConfigured()) {\n          try {\n            // For local providers like LM Studio and Ollama, use a silent check\n            // to avoid showing error messages if they're not running\n            if (id === 'lmstudio' || id === 'ollama') {\n              // First try a silent connection test\n              const testResult = await provider.testConnection('silent-check');\n              if (testResult.success) {\n                const models = await provider.listModels();\n                logger.info(`Provider ${id} has ${models.length} models available`);\n              } else {\n                // Just log the error without showing notifications\n                logger.debug(`Provider ${id} is not available: ${testResult.message}`);\n              }\n            } else {\n              // For cloud providers, try to list models normally\n              const models = await provider.listModels();\n              logger.info(`Provider ${id} has ${models.length} models available`);\n            }\n          } catch (error) {\n            // Log the error but don't let it prevent the provider from being registered\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            logger.warn(`Failed to list models for provider ${id}: ${errorMessage}`);\n\n            // Check if we should suppress this error message\n            const warningMessage = `Failed to connect to ${id} provider. The provider is registered but may not work until the connection issue is resolved.`;\n                        \n            // Only show warnings for cloud providers that are explicitly configured\n            // Local providers (ollama, lmstudio) should never show errors during initialization\n            if (!shouldSuppressError(warningMessage) &&\n                            id !== 'ollama' &&\n                            id !== 'lmstudio' &&\n                            provider.isConfigured()) {\n              // Only show warnings for cloud providers that should be available\n              // and only if error suppression is disabled and the provider is configured\n              // Import showWarningMessage to use error suppression\n              const { showWarningMessage } = require('../ui/notifications');\n              showWarningMessage(warningMessage);\n            }\n          }\n        }\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      logger.warn(`Failed to initialize ${id} provider: ${errorMessage}`);\n\n      // Always register the provider even if initialization fails\n      // This ensures the UI shows all available providers\n      try {\n        const provider = providerFactory();\n        if (provider) {\n          this.registerProvider(provider);\n          logger.info(`Registered provider ${id} despite initialization failure`);\n        }\n      } catch (e) {\n        // If we can't even create the provider, just log and continue\n        logger.error(`Could not create provider ${id}: ${e instanceof Error ? e.message : String(e)}`);\n      }\n    }\n  }\n\n  /**\n     * Registers a new LLM provider\n     */\n  registerProvider(provider: ILLMProvider): void {\n    if (this.providers.has(provider.providerId)) {\n      logger.warn(`Provider with ID '${provider.providerId}' is already registered. Overwriting.`);\n    }\n    this.providers.set(provider.providerId, provider);\n\n    // Also register with the provider manager\n    if (this.context) {\n      providerManager.getInstance(this.context).registerProvider(provider);\n    }\n  }\n\n  /**\n     * Gets a provider by ID\n     */\n  getProvider(providerId: string): ILLMProvider | undefined {\n    return this.providers.get(providerId);\n  }\n\n  /**\n     * Gets the appropriate LLM provider based on the config\n     */\n  async getProviderForConfig(_config: any): Promise<ILLMProvider | undefined> {\n    const provider = this.providers.get(_config.provider);\n\n    if (!provider) {\n      logger.warn('LLM provider \\'' + _config.provider + '\\' not found.');\n      return undefined;\n    }\n\n    if (!this.context) {\n      logger.error('LLMService not initialized with context');\n      return undefined;\n    }\n\n    // Check if the provider is enabled (configured and not disabled)\n    const manager = providerManager.getInstance(this.context);\n    if (!await manager.isProviderEnabled(provider.providerId)) {\n      logger.warn('LLM provider \\'' + _config.provider + '\\' is not enabled (not configured or explicitly disabled).');\n      return undefined;\n    }\n\n    return provider;\n  }\n\n  /**\n     * Gets the default provider based on settings\n     */\n  async getDefaultProvider(): Promise<ILLMProvider | undefined> {\n    if (!this.context) {\n      logger.error('LLMService not initialized with context');\n      return undefined;\n    }\n\n    // Get default provider ID from settings\n    const defaultProviderId = providerManager.getInstance(this.context).getDefaultProviderId();\n    const provider = this.providers.get(defaultProviderId);\n    const manager = providerManager.getInstance(this.context);\n\n    // Check if the default provider is enabled (configured and not disabled)\n    if (provider && await manager.isProviderEnabled(provider.providerId)) {\n      return provider;\n    }\n        \n    // If the default provider is not available, not configured, or disabled\n    logger.warn(`Default provider '${defaultProviderId}' not found, not configured, or disabled. Trying to find another provider.`);\n        \n    // Try to find an enabled provider\n    const enabledProviders = await this.getEnabledProviders();\n    if (enabledProviders.length > 0) {\n      // Prioritize local providers (Ollama, LM Studio) if they're enabled\n      const localProvider = enabledProviders.find(p => \n        p.providerId === 'ollama' || p.providerId === 'lmstudio'\n      );\n            \n      if (localProvider) {\n        return localProvider;\n      }\n            \n      // Otherwise return the first enabled provider\n      return enabledProviders[0];\n    }\n        \n    // If no enabled providers, try any configured provider\n    const configuredProviders = this.getConfiguredProviders();\n    if (configuredProviders.length > 0) {\n      return configuredProviders[0];\n    }\n        \n    // If no providers are available at all, return undefined\n    logger.warn('No LLM providers are available. Users can enable providers in settings.');\n    return undefined;\n  }\n\n  /**\n     * Sets the default provider\n     */\n  async setDefaultProvider(providerId: string): Promise<boolean> {\n    if (!this.context) {\n      logger.error('LLMService not initialized with context');\n      return false;\n    }\n\n    const provider = this.providers.get(providerId);\n    if (!provider) {\n      logger.warn(`Cannot set default provider: Provider '${providerId}' not found.`);\n      return false;\n    }\n\n    try {\n      const success = await providerManager.getInstance(this.context).setDefaultProviderId(providerId);\n      if (success) {\n        logger.info(`Set default provider to ${providerId}`);\n        return true;\n      } else {\n        logger.error(`Failed to set default provider to ${providerId}`);\n        return false;\n      }\n    } catch (error) {\n      logger.error(`Failed to set default provider to ${providerId}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Gets all available LLM providers\n     */\n  getAllProviders(): ILLMProvider[] {\n    return Array.from(this.providers.values());\n  }\n\n  /**\n     * Gets all available configured providers\n     */\n  getConfiguredProviders(): ILLMProvider[] {\n    return Array.from(this.providers.values())\n      .filter(provider => provider.isConfigured());\n  }\n     \n  /**\n     * Gets all available enabled providers (configured and not disabled)\n     */\n  async getEnabledProviders(): Promise<ILLMProvider[]> {\n    if (!this.context) {\n      logger.error('LLMService not initialized with context');\n      return [];\n    }\n        \n    const enabledProviders: ILLMProvider[] = [];\n    const manager = providerManager.getInstance(this.context);\n        \n    for (const provider of this.providers.values()) {\n      if (await manager.isProviderEnabled(provider.providerId)) {\n        enabledProviders.push(provider);\n      }\n    }\n        \n    return enabledProviders;\n  }\n\n  /**\n     * Lists all provider IDs\n     */\n  listProviderIds(): string[] {\n    return Array.from(this.providers.keys());\n  }\n\n  /**\n     * Gets the default model configuration from settings\n     */\n  async getDefaultModelConfig(): Promise<LLMConfig> {\n    return getDefaultModelConfig();\n  }\n\n  /**\n     * Updates the configuration for a provider\n     */\n  async updateProviderConfig(providerId: string, _config: any): Promise<boolean> {\n    if (!this.context) {\n      logger.error('LLMService not initialized with context');\n      return false;\n    }\n\n    const provider = this.providers.get(providerId);\n    if (!provider) {\n      logger.warn('Cannot update provider config: Provider ' + providerId + ' not found.');\n      return false;\n    }\n\n    try {\n      await provider.updateConfig(_config);\n      logger.info('Updated configuration for provider ' + providerId);\n      return true;\n    } catch (error) {\n      logger.error('Failed to update configuration for provider ' + providerId + ':', error);\n      return false;\n    }\n  }\n\n  /**\n     * Gets the configuration for a provider\n     */\n  getProviderConfig(providerId: string): LLMProviderConfig | undefined {\n    const provider = this.providers.get(providerId);\n    if (!provider) {\n      return undefined;\n    }\n\n    return provider.getConfig();\n  }\n}\n\nexport const llmService = new LLMService();"]}