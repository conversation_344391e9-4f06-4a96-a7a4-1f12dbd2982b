import path from 'path';

/**
 * Benchmark configuration
 */
export const benchmarkConfig = {
  // Number of iterations to run each benchmark
  iterations: 10,
  
  // Workflow configurations for different test scenarios
  workflows: {
    // Simple workflow with minimal steps
    simple: {
      steps: 5,  // Number of steps in the workflow
      stepDelay: 50, // ms
      memoryOperations: {
        read: 2,
        write: 1,
        search: 0,
        update: 0
      },
      revolutionaryFeatures: {
        quantumAnalysis: false,
        neuralSynthesis: false
      }
    },
    
    // Medium complexity workflow
    medium: {
      steps: 15,  // Number of steps in the workflow
      stepDelay: 100, // ms
      memoryOperations: {
        read: 5,
        write: 3,
        search: 2,
        update: 1
      },
      revolutionaryFeatures: {
        quantumAnalysis: true,
        neuralSynthesis: false
      }
    },
    
    // Complex workflow with many steps and operations
    complex: {
      steps: 30,  // Number of steps in the workflow
      stepDelay: 150, // ms
      memoryOperations: {
        read: 10,
        write: 5,
        search: 5
      }
    }
  },
  
  // Memory configuration
  memory: {
    // Size of the test memory store
    storeSize: 1000,
    
    // Types of memory to test with
    backends: ['in-memory', 'vector'],
    
    // Vector store configuration
    vectorStore: {
      dimensions: 1536,
      similarityThreshold: 0.7
    }
  },
  
  // Output configuration
  output: {
    // Directory to store benchmark results
    directory: path.join(__dirname, 'results'),
    
    // Whether to save detailed logs
    saveLogs: true,
    
    // Whether to generate charts
    generateCharts: true
  },
  
  // Performance monitoring
  monitoring: {
    // Track memory usage
    trackMemory: true,
    
    // Track CPU usage
    trackCPU: true,
    
    // Track event loop lag
    trackEventLoop: true
  }
};

export type BenchmarkConfig = typeof benchmarkConfig;
