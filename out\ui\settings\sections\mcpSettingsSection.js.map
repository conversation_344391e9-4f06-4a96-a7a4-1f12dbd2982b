{"version": 3, "file": "mcpSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/mcpSettingsSection.ts"], "names": [], "mappings": ";;AAGA,4DAiCC;AApCD,uBAAuB;AACvB,+CAAgD;AAEhD,SAAgB,wBAAwB,CAAC,SAAsB,EAAE,QAAa;IAC5E,SAAS,CAAC,SAAS,GAAG;;;;;8DAKsC,QAAQ,CAAC,WAAW,IAAI,EAAE;;;;iEAIvB,QAAQ,CAAC,SAAS,IAAI,EAAE;;;;iFAIR,QAAQ,CAAC,UAAU,IAAI,EAAE;;;;KAIrG,CAAC;IAEJ,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;QACvE,MAAM,QAAQ,GAAI,SAAS,CAAC,aAAa,CAAC,eAAe,CAAsB,CAAC,KAAK,CAAC;QACtF,MAAM,MAAM,GAAI,SAAS,CAAC,aAAa,CAAC,cAAc,CAAsB,CAAC,KAAK,CAAC;QACnF,MAAM,OAAO,GAAG,QAAQ,CAAE,SAAS,CAAC,aAAa,CAAC,cAAc,CAAsB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAClG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;QAChC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC;QAC5B,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC;QAC9B,IAAA,iBAAS,EAAC;YACR,KAAK,EAAE,oBAAoB;YAC3B,OAAO,EAAE,sCAAsC;YAC/C,SAAS,EAAE,GAAG,EAAE,GAAE,CAAC;SACpB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// MCP Settings Section\nimport { showModal } from '../components/modal';\n\nexport function renderMCPSettingsSection(container: HTMLElement, settings: any) {\n  container.innerHTML = `\n        <div class=\"settings-section\">\n            <h2 class=\"settings-section-title\">MCP Settings</h2>\n            <div class=\"settings-group\">\n                <label for=\"mcp-endpoint\">MCP Endpoint</label>\n                <input id=\"mcp-endpoint\" type=\"text\" value=\"${settings.mcpEndpoint || ''}\" placeholder=\"https://mcp.example.com/api\" />\n            </div>\n            <div class=\"settings-group\">\n                <label for=\"mcp-api-key\">API Key</label>\n                <input id=\"mcp-api-key\" type=\"password\" value=\"${settings.mcpApiKey || ''}\" placeholder=\"Enter your API key\" />\n            </div>\n            <div class=\"settings-group\">\n                <label for=\"mcp-timeout\">Timeout (seconds)</label>\n                <input id=\"mcp-timeout\" type=\"number\" min=\"1\" max=\"120\" value=\"${settings.mcpTimeout || 30}\" />\n            </div>\n            <button id=\"mcp-save-btn\" class=\"settings-save-btn\">Save MCP Settings</button>\n        </div>\n    `;\n\n  container.querySelector('#mcp-save-btn')?.addEventListener('click', () => {\n    const endpoint = (container.querySelector('#mcp-endpoint') as HTMLInputElement).value;\n    const apiKey = (container.querySelector('#mcp-api-key') as HTMLInputElement).value;\n    const timeout = parseInt((container.querySelector('#mcp-timeout') as HTMLInputElement).value, 10);\n    settings.mcpEndpoint = endpoint;\n    settings.mcpApiKey = apiKey;\n    settings.mcpTimeout = timeout;\n    showModal({\n      title: 'MCP Settings Saved',\n      content: 'Your MCP settings have been updated.',\n      onConfirm: () => {}\n    });\n  });\n}\n"]}