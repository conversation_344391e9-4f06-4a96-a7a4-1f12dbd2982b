import * as vscode from 'vscode';
import { Logger } from '../logger';

let memoryEnabled = false;

export function getMemoryEnabled(): boolean {
  return memoryEnabled;
}

export function setMemoryEnabled(enabled: boolean): void {
  const oldState = memoryEnabled;
  memoryEnabled = enabled;
  
  // Only log if state actually changed
  if (oldState !== enabled) {
    Logger.instance.info(`Memory ${enabled ? 'enabled' : 'disabled'}`);
    // Emit event for state change
    void vscode.commands.executeCommand('codessa.memoryStateChanged', enabled);
    // Update configuration
    void vscode.workspace.getConfiguration('codessa').update('memory.enabled', enabled, true);
  }
} 