import * as vscode from 'vscode';
import { logger } from './logger';

/**
 * Get a configuration value from VS Code settings
 */
export function getConfig<T>(key: string, defaultValue: T): T {
  try {
    const config = vscode.workspace.getConfiguration('codessa');
    const value = config.get<T>(key);
    return value !== undefined ? value : defaultValue;
  } catch (error) {
    logger.error(`Error reading configuration key 'codessa.${key}':`, error);
    return defaultValue;
  }
}

/**
 * Set a configuration value in VS Code settings
 */
export async function setConfig<T>(
  key: string,
  value: T,
  target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Global
): Promise<boolean> {
  try {
    const config = vscode.workspace.getConfiguration('codessa');

    // Validate the key
    if (!key || typeof key !== 'string') {
      throw new Error('Invalid configuration key');
    }

    // Try with the specified target first
    try {
      await config.update(key, value, target);
      logger.info(`Updated configuration key 'codessa.${key}' at target level ${target}`);
      return true;
    } catch (targetError) {
      logger.warn(`Failed to update at target level ${target}: ${targetError}. Trying alternative targets...`);
    }

    // If the specified target fails, try Global
    if (target !== vscode.ConfigurationTarget.Global) {
      try {
        await config.update(key, value, vscode.ConfigurationTarget.Global);
        logger.info(`Updated configuration key 'codessa.${key}' at Global level`);
        return true;
      } catch (globalError) {
        logger.warn(`Failed to update at Global level: ${globalError}. Trying Workspace level...`);
      }
    }

    // If Global fails, try Workspace if available
    if (target !== vscode.ConfigurationTarget.Workspace &&
            vscode.workspace.workspaceFolders &&
            vscode.workspace.workspaceFolders.length > 0) {
      try {
        await config.update(key, value, vscode.ConfigurationTarget.Workspace);
        logger.info(`Updated configuration key 'codessa.${key}' at Workspace level`);
        return true;
      } catch (workspaceError) {
        logger.warn(`Failed to update at Workspace level: ${workspaceError}`);
      }
    }

    // If we get here, all attempts failed
    throw new Error(`Failed to update setting 'codessa.${key}' at any target level`);
  } catch (error) {
    logger.error(`Error writing configuration key 'codessa.${key}':`, error);
    return false;
  }
}

/**
 * Delete a configuration value
 */
export async function deleteConfig(key: string): Promise<boolean> {
  try {
    const config = vscode.workspace.getConfiguration('codessa');
    await config.update(key, undefined, vscode.ConfigurationTarget.Global);
    logger.info(`Deleted configuration key 'codessa.${key}'`);
    return true;
  } catch (error) {
    logger.error(`Error deleting configuration key 'codessa.${key}':`, error);
    return false;
  }
}

/**
 * Get all configuration keys
 */
export function getAllConfigKeys(): string[] {
  try {
    const config = vscode.workspace.getConfiguration('codessa');
    return config.keys();
  } catch (error) {
    logger.error('Error getting all configuration keys:', error);
    return [];
  }
}

/**
 * Get configuration inspection (includes default, global, workspace values)
 */
export function getConfigInspection(key: string): any {
  try {
    const config = vscode.workspace.getConfiguration('codessa');
    return config.inspect(key);
  } catch (error) {
    logger.error(`Error inspecting configuration key 'codessa.${key}':`, error);
    return undefined;
  }
}

// Core settings
export function getLogLevel(): string {
  return getConfig<string>('logLevel', 'info');
}

export function getMaxToolIterations(): number {
  return getConfig<number>('maxToolIterations', 5);
}

export function getDefaultModelConfig(): LLMConfig {
  // Get selected provider and model from chat view settings
  const selectedProvider = getConfig<string>('selectedProvider', 'ollama');
  const selectedModel = getConfig<string>('selectedModel', 'llama3');

  const defaultConfig: LLMConfig = {
    provider: selectedProvider,
    modelId: selectedModel,
    options: {
      temperature: 0.7,
      maxTokens: 2000
    }
  };
  return defaultConfig;
}

// Memory settings
export function getMemoryEnabled(): boolean {
  return getConfig<boolean>('memory.enabled', true);
}

export function getMaxMemories(): number {
  return getConfig<number>('memory.maxMemories', 1000);
}

export function getMemoryRelevanceThreshold(): number {
  return getConfig<number>('memory.relevanceThreshold', 0.7);
}

export function getMemoryContextWindowSize(): number {
  return getConfig<number>('memory.contextWindowSize', 5);
}

export function getConversationHistorySize(): number {
  return getConfig<number>('memory.conversationHistorySize', 100);
}

// Provider configurations
export function getOpenAIApiKey(): string {
  return getConfig<string>('providers.openai.apiKey', '');
}

export function getOpenAIBaseUrl(): string {
  const defaultUrl = 'https://api.openai.com/v1';
  return getConfig<string>('providers.openai.baseUrl', defaultUrl);
}

export function getOpenAIOrganization(): string {
  return getConfig<string>('providers.openai.organization', '');
}

export function getGoogleAIApiKey(): string {
  return getConfig<string>('providers.googleai.apiKey', '');
}

export function getMistralAIApiKey(): string {
  return getConfig<string>('providers.mistralai.apiKey', '');
}

export function getAnthropicApiKey(): string {
  return getConfig<string>('providers.anthropic.apiKey', '');
}

export function getOllamaBaseUrl(): string {
  return getConfig<string>('providers.ollama.baseUrl', 'http://localhost:11434');
}

export function getLMStudioBaseUrl(): string {
  return getConfig<string>('providers.lmstudio.baseUrl', 'http://localhost:1234/v1');
}

// Agent configuration
export function getAgents(): AgentConfig[] {
  return getConfig<AgentConfig[]>('agents', []);
}

export async function saveAgents(agents: AgentConfig[]): Promise<boolean> {
  return await setConfig('agents', agents);
}

// Prompt configuration
export function getSystemPrompts(): Record<string, string> {
  return getConfig<Record<string, string>>('systemPrompts', {});
}

export function getPromptVariables(): Record<string, string> {
  return getConfig<Record<string, string>>('promptVariables', {});
}

// Advanced Features Settings
export function getGoddessMode(): GoddessSettings {
  const defaultSettings: GoddessSettings = {
    enabled: true,
    adaptiveLevel: 85,
    emotionalIntelligence: 90,
    motivationalStyle: 'adaptive',
    wisdomLevel: 95,
    creativityLevel: 80,
    empathyLevel: 85,
    personalityAdaptation: true,
    moodAnalysis: true,
    contextualResponses: true,
    divineGuidance: true
  };
  return getConfig<GoddessSettings>('advancedFeatures.goddessMode', defaultSettings);
}

export function getQuantumAnalysis(): QuantumSettings {
  const defaultSettings: QuantumSettings = {
    enabled: true,
    patternRecognition: true,
    superpositionAnalysis: true,
    entanglementDetection: true,
    parallelUniverseTesting: true,
    quantumInterference: true,
    confidenceThreshold: 0.7,
    maxParallelUniverses: 5,
    quantumComplexityLevel: 'advanced'
  };
  return getConfig<QuantumSettings>('advancedFeatures.quantumAnalysis', defaultSettings);
}

export function getNeuralSynthesis(): NeuralSettings {
  const defaultSettings: NeuralSettings = {
    enabled: true,
    brainInspiredGeneration: true,
    synapticConnections: true,
    consciousnessAnalysis: true,
    creativityBoost: true,
    neuralLearning: true,
    networkComplexity: 'medium',
    learningRate: 0.01,
    creativityThreshold: 0.7,
    consciousnessLevel: 75
  };
  return getConfig<NeuralSettings>('advancedFeatures.neuralSynthesis', defaultSettings);
}

export function getTimeTravelDebugging(): TimeTravelSettings {
  const defaultSettings: TimeTravelSettings = {
    enabled: true,
    futureIssuePrediction: true,
    alternativeTimelines: true,
    mitigationStrategies: true,
    temporalAnalysis: true,
    predictionAccuracy: 'high',
    timeHorizon: '3-months',
    riskThreshold: 0.6,
    maxPredictions: 10
  };
  return getConfig<TimeTravelSettings>('advancedFeatures.timeTravelDebugging', defaultSettings);
}

export function getWorkflowEnhancements(): WorkflowEnhancementSettings {
  const defaultSettings: WorkflowEnhancementSettings = {
    advancedFeaturesEnabled: true,
    autoEnhancement: true,
    goddessGuidance: true,
    quantumOptimization: true,
    neuralInsights: true,
    timeTravelPredictions: true,
    adaptiveExecution: true,
    performanceMetrics: true,
    userSatisfactionTracking: true,
    goddessRatingSystem: true
  };
  return getConfig<WorkflowEnhancementSettings>('advancedFeatures.workflowEnhancements', defaultSettings);
}

export function getUIEnhancements(): UIEnhancementSettings {
  const defaultSettings: UIEnhancementSettings = {
    advancedTheme: true,
    goddessAnimations: true,
    quantumEffects: true,
    neuralVisualizations: true,
    timeTravelIndicators: true,
    adaptiveInterface: true,
    emotionalFeedback: true,
    motivationalElements: true,
    wisdomTooltips: true,
    creativityIndicators: true
  };
  return getConfig<UIEnhancementSettings>('advancedFeatures.uiEnhancements', defaultSettings);
}

export function getAdvancedAI(): AdvancedAISettings {
  const defaultSettings: AdvancedAISettings = {
    multiModalProcessing: true,
    contextualUnderstanding: true,
    emotionalIntelligence: true,
    creativeThinking: true,
    intuitiveProblemSolving: true,
    adaptiveLearning: true,
    consciousnessSimulation: true,
    quantumInspiredAlgorithms: true,
    neuralNetworkIntegration: true,
    temporalReasoningCapabilities: true
  };
  return getConfig<AdvancedAISettings>('advancedFeatures.advancedAI', defaultSettings);
}

// Setter functions for advanced features
export async function setGoddessMode(settings: Partial<GoddessSettings>): Promise<boolean> {
  return await setConfig('advancedFeatures.goddessMode', settings);
}

export async function setQuantumAnalysis(settings: Partial<QuantumSettings>): Promise<boolean> {
  return await setConfig('advancedFeatures.quantumAnalysis', settings);
}

export async function setNeuralSynthesis(settings: Partial<NeuralSettings>): Promise<boolean> {
  return await setConfig('advancedFeatures.neuralSynthesis', settings);
}

export async function setTimeTravelDebugging(settings: Partial<TimeTravelSettings>): Promise<boolean> {
  return await setConfig('advancedFeatures.timeTravelDebugging', settings);
}

export async function setWorkflowEnhancements(settings: Partial<WorkflowEnhancementSettings>): Promise<boolean> {
  return await setConfig('advancedFeatures.workflowEnhancements', settings);
}

export async function setUIEnhancements(settings: Partial<UIEnhancementSettings>): Promise<boolean> {
  return await setConfig('advancedFeatures.uiEnhancements', settings);
}

export async function setAdvancedAI(settings: Partial<AdvancedAISettings>): Promise<boolean> {
  return await setConfig('advancedFeatures.advancedAI', settings);
}

// Type definitions
export interface LLMConfig {
    provider: string;
    modelId: string;
    options?: {
        temperature?: number;
        maxTokens?: number;
        topP?: number;
        frequencyPenalty?: number;
        presencePenalty?: number;
        stopSequences?: string[];
        [key: string]: any;
    };
}

export interface AgentConfig {
    id: string;
    name: string;
    description?: string;
    systemPromptName: string;
    llm?: LLMConfig;
    tools?: string[];
    isSupervisor?: boolean;
    chainedAgentIds?: string[];
    advancedFeatures?: {
        goddessMode?: boolean;
        quantumAnalysis?: boolean;
        neuralSynthesis?: boolean;
        timeTravelDebugging?: boolean;
        adaptivePersonality?: boolean;
    };
}

export interface GoddessSettings {
    enabled: boolean;
    adaptiveLevel: number; // 0-100
    emotionalIntelligence: number; // 0-100
    motivationalStyle: 'encouraging' | 'wise' | 'playful' | 'supportive' | 'challenging' | 'adaptive';
    wisdomLevel: number; // 0-100
    creativityLevel: number; // 0-100
    empathyLevel: number; // 0-100
    personalityAdaptation: boolean;
    moodAnalysis: boolean;
    contextualResponses: boolean;
    divineGuidance: boolean;
}

export interface QuantumSettings {
    enabled: boolean;
    patternRecognition: boolean;
    superpositionAnalysis: boolean;
    entanglementDetection: boolean;
    parallelUniverseTesting: boolean;
    quantumInterference: boolean;
    confidenceThreshold: number; // 0-1
    maxParallelUniverses: number;
    quantumComplexityLevel: 'basic' | 'intermediate' | 'advanced' | 'expert';
}

export interface NeuralSettings {
    enabled: boolean;
    brainInspiredGeneration: boolean;
    synapticConnections: boolean;
    consciousnessAnalysis: boolean;
    creativityBoost: boolean;
    neuralLearning: boolean;
    networkComplexity: 'low' | 'medium' | 'high' | 'ultra';
    learningRate: number; // 0-1
    creativityThreshold: number; // 0-1
    consciousnessLevel: number; // 0-100
}

export interface TimeTravelSettings {
    enabled: boolean;
    futureIssuePrediction: boolean;
    alternativeTimelines: boolean;
    mitigationStrategies: boolean;
    temporalAnalysis: boolean;
    predictionAccuracy: 'low' | 'medium' | 'high' | 'ultra';
    timeHorizon: '1-week' | '1-month' | '3-months' | '6-months' | '1-year';
    riskThreshold: number; // 0-1
    maxPredictions: number;
}

export interface WorkflowEnhancementSettings {
    advancedFeaturesEnabled: boolean;
    autoEnhancement: boolean;
    goddessGuidance: boolean;
    quantumOptimization: boolean;
    neuralInsights: boolean;
    timeTravelPredictions: boolean;
    adaptiveExecution: boolean;
    performanceMetrics: boolean;
    userSatisfactionTracking: boolean;
    goddessRatingSystem: boolean;
}

export interface UIEnhancementSettings {
    advancedTheme: boolean;
    goddessAnimations: boolean;
    quantumEffects: boolean;
    neuralVisualizations: boolean;
    timeTravelIndicators: boolean;
    adaptiveInterface: boolean;
    emotionalFeedback: boolean;
    motivationalElements: boolean;
    wisdomTooltips: boolean;
    creativityIndicators: boolean;
}

export interface AdvancedAISettings {
    multiModalProcessing: boolean;
    contextualUnderstanding: boolean;
    emotionalIntelligence: boolean;
    creativeThinking: boolean;
    intuitiveProblemSolving: boolean;
    adaptiveLearning: boolean;
    consciousnessSimulation: boolean;
    quantumInspiredAlgorithms: boolean;
    neuralNetworkIntegration: boolean;
    temporalReasoningCapabilities: boolean;
}

