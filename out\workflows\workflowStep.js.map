{"version": 3, "file": "workflowStep.js", "sourceRoot": "", "sources": ["../../src/workflows/workflowStep.ts"], "names": [], "mappings": ";;;AAEA,MAAa,YAAY;IACP,EAAE,CAAS;IACX,IAAI,CAAS;IACb,WAAW,CAAS;IACnB,QAAQ,CAA4D;IAC7E,WAAW,CAAS;IACpB,WAAW,CAAS;IACpB,QAAQ,CAAS;IACjB,SAAS,CAAU;IACnB,UAAU,CAA4D;IACtE,UAAU,CAAkF;IAC5F,QAAQ,CAAoE;IAC5E,WAAW,CAAkF;IAErG,YAAY,OAA4B;QACtC,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,yBAAyB;QACxE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,6BAA6B;QACvE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,OAAwB;QAC3C,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,SAA4B,CAAC;QACjC,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,4BAA4B;QAEtE,sDAAsD;QACtD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;gBAClE,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,+BAA+B;wBACvC,UAAU,EAAE,SAAS;wBACrB,QAAQ,EAAE;4BACR,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,mBAAmB;yBAC5B;qBACF,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,OAAO,QAAQ,GAAG,WAAW,EAAE,CAAC;YAC9B,QAAQ,EAAE,CAAC;YAEX,IAAI,CAAC;gBACH,iBAAiB;gBACjB,MAAM,cAAc,GAAG,IAAI,OAAO,CAAqB,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;oBACnE,UAAU,CAAC,GAAG,EAAE;wBACd,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,qBAAqB,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;oBAC9E,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC;gBAEH,gCAAgC;gBAChC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;oBAChC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;oBACtB,cAAc;iBACf,CAAC,CAAC;gBAEH,iBAAiB;gBACjB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACpB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;oBAC1D,CAAC;oBAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;wBACrB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;oBAC3D,CAAC;oBAED,OAAO,MAAM,CAAC;gBAChB,CAAC;qBAAM,CAAC;oBACN,sEAAsE;oBACtE,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,uBAAuB,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAC;gBAE3B,gDAAgD;gBAChD,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;oBAC3B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,IAAI,IAAI,KAAK,CAAC,eAAe,CAAC,EAAE,SAAS,CAAC,CAAC;IACvF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,KAAY,EAAE,SAAe;QAC/E,MAAM,WAAW,GAAuB;YACtC,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,MAAM,EAAE,SAAS;YACjB,UAAU,EAAE,SAAS;YACrB,QAAQ,EAAE;gBACR,KAAK,EAAE;oBACL,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF;SACF,CAAC;QAEF,iCAAiC;QACjC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC;gBACH,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YACvD,CAAC;YAAC,OAAO,YAAY,EAAE,CAAC;gBACtB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,YAAY,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC;gBACH,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;YAChE,CAAC;YAAC,OAAO,YAAY,EAAE,CAAC;gBACtB,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,YAAY,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAuC;QAClD,OAAO,IAAI,YAAY,CAAC;YACtB,EAAE,EAAE,SAAS,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE;YAC3B,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;YACjC,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW;YACtD,OAAO,EAAE,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ;YAC3C,UAAU,EAAE,SAAS,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW;YACpD,UAAU,EAAE,SAAS,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW;YACpD,OAAO,EAAE,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ;YAC3C,QAAQ,EAAE,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS;YAC9C,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU;YACjD,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU;YACjD,OAAO,EAAE,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ;YAC3C,UAAU,EAAE,SAAS,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW;SACrD,CAAC,CAAC;IACL,CAAC;CACF;AA3KD,oCA2KC", "sourcesContent": ["import { WorkflowContext, WorkflowStepResult, WorkflowStepOptions } from './workflowContext';\n\nexport class WorkflowStep {\n  public readonly id: string;\n  public readonly name: string;\n  public readonly description: string;\n  private readonly _execute: (context: WorkflowContext) => Promise<WorkflowStepResult>;\n  private _retryCount: number;\n  private _retryDelay: number;\n  private _timeout: number;\n  private _parallel: boolean;\n  private _condition?: (context: WorkflowContext) => boolean | Promise<boolean>;\n  private _onSuccess?: (context: WorkflowContext, result: WorkflowStepResult) => void | Promise<void>;\n  private _onError?: (context: WorkflowContext, error: Error) => void | Promise<void>;\n  private _onComplete?: (context: WorkflowContext, result: WorkflowStepResult) => void | Promise<void>;\n\n  constructor(options: WorkflowStepOptions) {\n    this.id = options.id;\n    this.name = options.name;\n    this.description = options.description || '';\n    this._execute = options.execute;\n    this._retryCount = options.retryCount ?? 0;\n    this._retryDelay = options.retryDelay ?? 1000; // 1 second default delay\n    this._timeout = options.timeout ?? 30000; // 30 seconds default timeout\n    this._parallel = options.parallel ?? false;\n    this._condition = options.condition;\n    this._onSuccess = options.onSuccess;\n    this._onError = options.onError;\n    this._onComplete = options.onComplete;\n  }\n\n  /**\n   * Execute the workflow step with the given context\n   */\n  public async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n    const startTime = new Date();\n    let lastError: Error | undefined;\n    let attempts = 0;\n    const maxAttempts = 1 + this._retryCount; // Initial attempt + retries\n\n    // Check if the step should run based on the condition\n    if (this._condition) {\n      try {\n        const shouldRun = await Promise.resolve(this._condition(context));\n        if (!shouldRun) {\n          return {\n            success: true,\n            output: 'Step skipped due to condition',\n            nextStepId: undefined,\n            metadata: {\n              skipped: true,\n              reason: 'Condition not met'\n            }\n          };\n        }\n      } catch (error) {\n        return this.handleError(context, error as Error, startTime);\n      }\n    }\n\n    // Execute the step with retries\n    while (attempts < maxAttempts) {\n      attempts++;\n      \n      try {\n        // Set up timeout\n        const timeoutPromise = new Promise<WorkflowStepResult>((_, reject) => {\n          setTimeout(() => {\n            reject(new Error(`Step '${this.name}' timed out after ${this._timeout}ms`));\n          }, this._timeout);\n        });\n\n        // Execute the step with timeout\n        const result = await Promise.race([\n          this._execute(context),\n          timeoutPromise\n        ]);\n\n        // Handle success\n        if (result.success) {\n          if (this._onSuccess) {\n            await Promise.resolve(this._onSuccess(context, result));\n          }\n          \n          if (this._onComplete) {\n            await Promise.resolve(this._onComplete(context, result));\n          }\n          \n          return result;\n        } else {\n          // If execution was not successful but didn't throw, treat as an error\n          throw new Error(result.error?.toString() || 'Step execution failed');\n        }\n      } catch (error) {\n        lastError = error as Error;\n        \n        // If we have retries left, wait before retrying\n        if (attempts < maxAttempts) {\n          await new Promise(resolve => setTimeout(resolve, this._retryDelay));\n        }\n      }\n    }\n\n    // If we get here, all attempts failed\n    return this.handleError(context, lastError || new Error('Unknown error'), startTime);\n  }\n\n  /**\n   * Handle errors during step execution\n   */\n  private async handleError(context: WorkflowContext, error: Error, startTime: Date): Promise<WorkflowStepResult> {\n    const errorResult: WorkflowStepResult = {\n      success: false,\n      error: error.message,\n      output: undefined,\n      nextStepId: undefined,\n      metadata: {\n        error: {\n          name: error.name,\n          message: error.message,\n          stack: error.stack,\n          timestamp: new Date().toISOString()\n        }\n      }\n    };\n\n    // Call error handler if provided\n    if (this._onError) {\n      try {\n        await Promise.resolve(this._onError(context, error));\n      } catch (handlerError) {\n        console.error('Error in onError handler:', handlerError);\n      }\n    }\n\n    // Call complete handler if provided\n    if (this._onComplete) {\n      try {\n        await Promise.resolve(this._onComplete(context, errorResult));\n      } catch (handlerError) {\n        console.error('Error in onComplete handler:', handlerError);\n      }\n    }\n\n    return errorResult;\n  }\n\n  /**\n   * Check if this step can run in parallel\n   */\n  public get isParallel(): boolean {\n    return this._parallel;\n  }\n\n  /**\n   * Create a shallow copy of the step with overridden options\n   */\n  public clone(overrides: Partial<WorkflowStepOptions>): WorkflowStep {\n    return new WorkflowStep({\n      id: overrides.id ?? this.id,\n      name: overrides.name ?? this.name,\n      description: overrides.description ?? this.description,\n      execute: overrides.execute ?? this._execute,\n      retryCount: overrides.retryCount ?? this._retryCount,\n      retryDelay: overrides.retryDelay ?? this._retryDelay,\n      timeout: overrides.timeout ?? this._timeout,\n      parallel: overrides.parallel ?? this._parallel,\n      condition: overrides.condition ?? this._condition,\n      onSuccess: overrides.onSuccess ?? this._onSuccess,\n      onError: overrides.onError ?? this._onError,\n      onComplete: overrides.onComplete ?? this._onComplete\n    });\n  }\n}\n"]}