{"version": 3, "file": "memoryConfig.js", "sourceRoot": "", "sources": ["../../src/memory/memoryConfig.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,4CAEC;AAED,4CAYC;AArBD,+CAAiC;AACjC,sCAAmC;AAEnC,IAAI,aAAa,GAAG,KAAK,CAAC;AAE1B,SAAgB,gBAAgB;IAC9B,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAgB,gBAAgB,CAAC,OAAgB;IAC/C,MAAM,QAAQ,GAAG,aAAa,CAAC;IAC/B,aAAa,GAAG,OAAO,CAAC;IAExB,qCAAqC;IACrC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;QACzB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;QACnE,8BAA8B;QAC9B,KAAK,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QAC3E,uBAAuB;QACvB,KAAK,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC5F,CAAC;AACH,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from '../logger';\n\nlet memoryEnabled = false;\n\nexport function getMemoryEnabled(): boolean {\n  return memoryEnabled;\n}\n\nexport function setMemoryEnabled(enabled: boolean): void {\n  const oldState = memoryEnabled;\n  memoryEnabled = enabled;\n  \n  // Only log if state actually changed\n  if (oldState !== enabled) {\n    Logger.instance.info(`Memory ${enabled ? 'enabled' : 'disabled'}`);\n    // Emit event for state change\n    void vscode.commands.executeCommand('codessa.memoryStateChanged', enabled);\n    // Update configuration\n    void vscode.workspace.getConfiguration('codessa').update('memory.enabled', enabled, true);\n  }\n} "]}