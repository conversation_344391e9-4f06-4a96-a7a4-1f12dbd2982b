{"version": 3, "file": "checkpointManager.js", "sourceRoot": "", "sources": ["../../src/checkpoint/checkpointManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,+CAAiC;AACjC,2CAA6B;AAC7B,sCAAmC;AAiBnC;;GAEG;AACH,MAAa,iBAAiB;IACpB,WAAW,GAAiB,EAAE,CAAC;IAC/B,sBAAsB,GAAG,CAAC,CAAC,CAAC;IAC5B,UAAU,GAAG,qBAAqB,CAAC;IAE3C;QACE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,CAAC,WAAW;gBAAE,OAAO;YAEzB,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAe,IAAI,CAAC,UAAU,CAAC,CAAC;YACxE,IAAI,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACxD,4CAA4C;gBAC5C,IAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAC7C,GAAG,EAAE;oBACL,SAAS,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC;iBAClC,CAAC,CAAC,CAAC;gBACJ,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC1D,eAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,2BAA2B,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACvG,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,CAAC,WAAW;gBAAE,OAAO;YAEzB,MAAM,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,eAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,MAAM,yBAAyB,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACtG,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,cAAc;QAC1B,mCAAmC;QACnC,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACpE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,oCAAoC;QACpC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxB,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC;QAED,sCAAsC;QACtC,OAAO,SAAS,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IAC5C,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,gBAAgB,CAAC,WAAmB,EAAE,KAAe,EAAE,WAAoC,EAAE;QACjG,MAAM,UAAU,GAAe;YAC7B,EAAE,EAAE,cAAc,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;YAClE,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,WAAW;YACX,KAAK,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;YACrC,QAAQ;SACT,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QAE1D,8BAA8B;QAC9B,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAE7B,eAAM,CAAC,IAAI,CAAC,uBAAuB,UAAU,CAAC,EAAE,MAAM,WAAW,EAAE,CAAC,CAAC;QACrE,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,YAAY,CAAC,SAAmB;QAC5C,MAAM,aAAa,GAAwB,EAAE,CAAC;QAE9C,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,oBAAoB;gBACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAE5C,wBAAwB;gBACxB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACpF,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAEnC,iBAAiB;gBACjB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAEzC,aAAa,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,QAAQ;oBACd,OAAO;oBACP,IAAI;iBACL,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,eAAM,CAAC,KAAK,CAAC,wBAAwB,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC5G,sCAAsC;gBACtC,aAAa,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBAC3E,IAAI,EAAE,OAAO;iBACd,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;SAEK;IACG,aAAa,CAAC,OAAe;QACnC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnE,CAAC;IAED;;SAEK;IACG,WAAW,CAAC,QAAgB;QAClC,6CAA6C;QAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,2BAA2B;QAC3B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,oDAAoD;QACpD,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,oBAAoB,CAAC,YAAoB;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC;QAEjF,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3B,eAAM,CAAC,KAAK,CAAC,yBAAyB,YAAY,EAAE,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAErD,wBAAwB;QACxB,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,oBAAoB;gBACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE7C,kDAAkD;gBAClD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACpF,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBAExC,6BAA6B;gBAC7B,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAChC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EACtB,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAC/C,CAAC;gBACF,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAEpD,iBAAiB;gBACjB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACvD,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,eAAM,CAAC,KAAK,CAAC,2BAA2B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvD,CAAC;gBAED,oBAAoB;gBACpB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxB,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,eAAM,CAAC,KAAK,CAAC,wBAAwB,IAAI,CAAC,IAAI,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC/G,CAAC;QACH,CAAC;QAED,IAAI,CAAC,sBAAsB,GAAG,eAAe,CAAC;QAE9C,oCAAoC;QACpC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAE7B,eAAM,CAAC,IAAI,CAAC,8BAA8B,YAAY,EAAE,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;SAEK;IACL,cAAc;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IAC/B,CAAC;IAED;;SAEK;IACL,oBAAoB;QAClB,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACvD,CAAC;IAED;;SAEK;IACL,kBAAkB,CAAC,aAAqB,EAAE,aAAqB;QAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,aAAa,CAAC,CAAC;QACzE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,aAAa,CAAC,CAAC;QAEzE,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,gBAAgB;QAChB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,SAAS,GAA4B,EAAE,CAAC;QAE9C,kDAAkD;QAClD,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC;YACvB,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACrC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;SACtC,CAAC,CAAC;QAEH,oBAAoB;QACpB,KAAK,MAAM,QAAQ,IAAI,QAAQ,EAAE,CAAC;YAChC,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAC/D,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAE/D,8DAA8D;YAC9D,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;gBAChD,YAAY,EAAE,CAAC;gBAEf,6BAA6B;gBAC7B,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAEzC,0BAA0B;gBAC1B,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClC,UAAU,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC9C,CAAC;qBAAM,CAAC;oBACN,YAAY,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;gBAChD,CAAC;gBAED,qBAAqB;gBACrB,SAAS,CAAC,QAAQ,CAAC,GAAG;oBACpB,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;oBACtD,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;iBACzD,CAAC;YACJ,CAAC;YACD,4BAA4B;iBACvB,IAAI,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC;gBACzB,YAAY,EAAE,CAAC;gBACf,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;gBACnD,UAAU,IAAI,SAAS,CAAC;gBAExB,SAAS,CAAC,QAAQ,CAAC,GAAG;oBACpB,KAAK,EAAE,IAAI;oBACX,UAAU,EAAE,SAAS;iBACtB,CAAC;YACJ,CAAC;YACD,8BAA8B;iBACzB,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;gBACzB,YAAY,EAAE,CAAC;gBACf,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;gBACnD,YAAY,IAAI,SAAS,CAAC;gBAE1B,SAAS,CAAC,QAAQ,CAAC,GAAG;oBACpB,OAAO,EAAE,IAAI;oBACb,YAAY,EAAE,SAAS;iBACxB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO;YACL,YAAY;YACZ,UAAU;YACV,YAAY;YACZ,SAAS;SACV,CAAC;IACJ,CAAC;CACF;AAhTD,8CAgTC;AAED,8BAA8B;AACjB,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC", "sourcesContent": ["/**\n * Checkpoint Manager for Code Changes\n * \n * This module provides a checkpoint system for tracking and managing code changes\n * during AI-assisted development. It allows:\n * - Creating checkpoints before making changes\n * - Rolling back to previous checkpoints\n * - Comparing changes between checkpoints\n * - Merging changes from different branches\n */\n\nimport * as vscode from 'vscode';\nimport * as crypto from 'crypto';\nimport * as path from 'path';\nimport { logger } from '../logger';\n\n/**\n * Checkpoint data structure\n */\nexport interface Checkpoint {\n    id: string;\n    timestamp: Date;\n    description: string;\n    files: Array<{\n        path: string;\n        content: string;\n        hash: string;\n    }>;\n    metadata: Record<string, unknown>;\n}\n\n/**\n * Checkpoint manager for tracking code changes\n */\nexport class CheckpointManager {\n  private checkpoints: Checkpoint[] = [];\n  private currentCheckpointIndex = -1;\n  private storageKey = 'codessa.checkpoints';\n    \n  constructor() {\n    this.loadCheckpoints();\n  }\n    \n  /**\n     * Loads checkpoints from storage\n     */\n  private async loadCheckpoints(): Promise<void> {\n    try {\n      const globalState = await this.getGlobalState();\n      if (!globalState) return;\n            \n      const savedCheckpoints = globalState.get<Checkpoint[]>(this.storageKey);\n      if (savedCheckpoints && Array.isArray(savedCheckpoints)) {\n        // Convert string dates back to Date objects\n        this.checkpoints = savedCheckpoints.map(cp => ({\n          ...cp,\n          timestamp: new Date(cp.timestamp)\n        }));\n        this.currentCheckpointIndex = this.checkpoints.length - 1;\n        logger.info(`Loaded ${this.checkpoints.length} checkpoints from storage`);\n      }\n    } catch (error: unknown) {\n      logger.error(`Error loading checkpoints: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n    \n  /**\n     * Saves checkpoints to storage\n     */\n  private async saveCheckpoints(): Promise<void> {\n    try {\n      const globalState = await this.getGlobalState();\n      if (!globalState) return;\n            \n      await globalState.update(this.storageKey, this.checkpoints);\n      logger.info(`Saved ${this.checkpoints.length} checkpoints to storage`);\n    } catch (error: unknown) {\n      logger.error(`Error saving checkpoints: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n    \n  /**\n     * Gets the VS Code extension context global state\n     */\n  private async getGlobalState(): Promise<vscode.Memento | null> {\n    // Get the active VS Code extension\n    const extension = vscode.extensions.getExtension('codessa.codessa');\n    if (!extension) {\n      logger.error('Codessa extension not found');\n      return null;\n    }\n        \n    // Ensure the extension is activated\n    if (!extension.isActive) {\n      await extension.activate();\n    }\n        \n    // Access the extension's global state\n    return extension.exports.getGlobalState();\n  }\n    \n  /**\n     * Creates a new checkpoint\n     */\n  async createCheckpoint(description: string, files: string[], metadata: Record<string, unknown> = {}): Promise<Checkpoint> {\n    const checkpoint: Checkpoint = {\n      id: `checkpoint-${Date.now()}-${Math.floor(Math.random() * 1000)}`,\n      timestamp: new Date(),\n      description,\n      files: await this.captureFiles(files),\n      metadata\n    };\n        \n    this.checkpoints.push(checkpoint);\n    this.currentCheckpointIndex = this.checkpoints.length - 1;\n        \n    // Save checkpoints to storage\n    await this.saveCheckpoints();\n        \n    logger.info(`Created checkpoint: ${checkpoint.id} - ${description}`);\n    return checkpoint;\n  }\n    \n  /**\n     * Captures the content of files for a checkpoint\n     */\n  private async captureFiles(filePaths: string[]): Promise<Checkpoint['files']> {\n    const capturedFiles: Checkpoint['files'] = [];\n        \n    for (const filePath of filePaths) {\n      try {\n        // Get the full path\n        const fullPath = this.getFullPath(filePath);\n                \n        // Read the file content\n        const document = await vscode.workspace.openTextDocument(vscode.Uri.file(fullPath));\n        const content = document.getText();\n                \n        // Calculate hash\n        const hash = this.calculateHash(content);\n                \n        capturedFiles.push({\n          path: filePath,\n          content,\n          hash\n        });\n      } catch (error: unknown) {\n        logger.error(`Error capturing file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);\n        // Add the file with error information\n        capturedFiles.push({\n          path: filePath,\n          content: `ERROR: ${error instanceof Error ? error.message : String(error)}`,\n          hash: 'error'\n        });\n      }\n    }\n        \n    return capturedFiles;\n  }\n    \n  /**\n     * Calculates a hash for file content\n     */\n  private calculateHash(content: string): string {\n    return crypto.createHash('sha256').update(content).digest('hex');\n  }\n    \n  /**\n     * Gets the full path for a file\n     */\n  private getFullPath(filePath: string): string {\n    // If the path is already absolute, return it\n    if (path.isAbsolute(filePath)) {\n      return filePath;\n    }\n        \n    // Get the workspace folder\n    const workspaceFolders = vscode.workspace.workspaceFolders;\n    if (!workspaceFolders || workspaceFolders.length === 0) {\n      throw new Error('No workspace folder open');\n    }\n        \n    // Resolve the path relative to the workspace folder\n    return path.join(workspaceFolders[0].uri.fsPath, filePath);\n  }\n    \n  /**\n     * Rolls back to a specific checkpoint\n     */\n  async rollbackToCheckpoint(checkpointId: string): Promise<boolean> {\n    const checkpointIndex = this.checkpoints.findIndex(cp => cp.id === checkpointId);\n        \n    if (checkpointIndex === -1) {\n      logger.error(`Checkpoint not found: ${checkpointId}`);\n      return false;\n    }\n        \n    const checkpoint = this.checkpoints[checkpointIndex];\n        \n    // Restore file contents\n    for (const file of checkpoint.files) {\n      try {\n        // Get the full path\n        const fullPath = this.getFullPath(file.path);\n                \n        // Create a TextEdit to replace the entire content\n        const document = await vscode.workspace.openTextDocument(vscode.Uri.file(fullPath));\n        const edit = new vscode.WorkspaceEdit();\n                \n        // Replace the entire content\n        const fullRange = new vscode.Range(\n          document.positionAt(0),\n          document.positionAt(document.getText().length)\n        );\n        edit.replace(document.uri, fullRange, file.content);\n                \n        // Apply the edit\n        const success = await vscode.workspace.applyEdit(edit);\n        if (!success) {\n          logger.error(`Failed to restore file: ${file.path}`);\n        }\n                \n        // Save the document\n        await document.save();\n      } catch (error: unknown) {\n        logger.error(`Error restoring file ${file.path}: ${error instanceof Error ? error.message : String(error)}`);\n      }\n    }\n        \n    this.currentCheckpointIndex = checkpointIndex;\n        \n    // Save the current checkpoint index\n    await this.saveCheckpoints();\n        \n    logger.info(`Rolled back to checkpoint: ${checkpointId}`);\n        \n    return true;\n  }\n    \n  /**\n     * Gets all checkpoints\n     */\n  getCheckpoints(): Checkpoint[] {\n    return [...this.checkpoints];\n  }\n    \n  /**\n     * Gets the current checkpoint\n     */\n  getCurrentCheckpoint(): Checkpoint | null {\n    if (this.currentCheckpointIndex === -1) {\n      return null;\n    }\n        \n    return this.checkpoints[this.currentCheckpointIndex];\n  }\n    \n  /**\n     * Compares two checkpoints and returns the differences\n     */\n  compareCheckpoints(checkpointId1: string, checkpointId2: string): Record<string, unknown> {\n    const checkpoint1 = this.checkpoints.find(cp => cp.id === checkpointId1);\n    const checkpoint2 = this.checkpoints.find(cp => cp.id === checkpointId2);\n        \n    if (!checkpoint1 || !checkpoint2) {\n      throw new Error('One or both checkpoints not found');\n    }\n        \n    // Track changes\n    let filesChanged = 0;\n    let linesAdded = 0;\n    let linesRemoved = 0;\n    const fileDiffs: Record<string, unknown> = {};\n        \n    // Get all unique file paths from both checkpoints\n    const allPaths = new Set([\n      ...checkpoint1.files.map(f => f.path),\n      ...checkpoint2.files.map(f => f.path)\n    ]);\n        \n    // Compare each file\n    for (const filePath of allPaths) {\n      const file1 = checkpoint1.files.find(f => f.path === filePath);\n      const file2 = checkpoint2.files.find(f => f.path === filePath);\n            \n      // If file exists in both checkpoints and content is different\n      if (file1 && file2 && file1.hash !== file2.hash) {\n        filesChanged++;\n                \n        // Calculate line differences\n        const lines1 = file1.content.split('\\n');\n        const lines2 = file2.content.split('\\n');\n                \n        // Simple diff calculation\n        if (lines2.length > lines1.length) {\n          linesAdded += lines2.length - lines1.length;\n        } else {\n          linesRemoved += lines1.length - lines2.length;\n        }\n                \n        // Store diff details\n        fileDiffs[filePath] = {\n          changed: true,\n          linesAdded: Math.max(0, lines2.length - lines1.length),\n          linesRemoved: Math.max(0, lines1.length - lines2.length)\n        };\n      }\n      // File added in checkpoint2\n      else if (!file1 && file2) {\n        filesChanged++;\n        const lineCount = file2.content.split('\\n').length;\n        linesAdded += lineCount;\n                \n        fileDiffs[filePath] = {\n          added: true,\n          linesAdded: lineCount\n        };\n      }\n      // File removed in checkpoint2\n      else if (file1 && !file2) {\n        filesChanged++;\n        const lineCount = file1.content.split('\\n').length;\n        linesRemoved += lineCount;\n                \n        fileDiffs[filePath] = {\n          removed: true,\n          linesRemoved: lineCount\n        };\n      }\n    }\n        \n    return {\n      filesChanged,\n      linesAdded,\n      linesRemoved,\n      fileDiffs\n    };\n  }\n}\n\n// Create a singleton instance\nexport const checkpointManager = new CheckpointManager();\n"]}