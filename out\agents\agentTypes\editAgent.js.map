{"version": 3, "file": "editAgent.js", "sourceRoot": "", "sources": ["../../../src/agents/agentTypes/editAgent.ts"], "names": [], "mappings": ";;;AAAA,mDAA6F;AAE7F,yCAAsC;AACtC,uEAAoE;AAEpE,MAAa,SAAU,SAAQ,aAAK;IAC1B,eAAe,CAAqB;IAE5C,YAAY,OAAY;QACtB,KAAK,CAAC;YACJ,GAAG,OAAO;YACV,IAAI,EAAE,WAAW;YACjB,YAAY,EAAE,CAAC,MAAM,CAAC;YACtB,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,GAAG,IAAI,uCAAkB,EAAE,CAAC;IAClD,CAAC;IAEQ,KAAK,CAAC,GAAG,CAAC,KAAoB,EAAE,UAAwB,EAAE;QACjE,eAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;QAE3E,iDAAiD;QACjD,IAAI,QAAQ,GAAQ,SAAS,CAAC;QAC9B,IAAI,OAAO,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC;YACvC,QAAQ,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAChF,CAAC;QAED,oCAAoC;QACpC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,GAAG,OAAO,CAAC,KAA2B,CAAC;QAC9C,CAAC;QAED,kDAAkD;QAClD,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;gBACrB,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QACD,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,OAAO,CAAC,SAAS,EAAE,oBAAoB,EAAE,CAAC;YAC5C,SAAS,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC9F,CAAC;QAED,6BAA6B;QAC7B,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;YAClC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,2CAA2C;QAC3C,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,IAAI,OAAO,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;YACrC,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACzG,CAAC;QAED,kEAAkE;QAClE,MAAM,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC3E,IAAI,cAAc,GAAG,EAAE,CAAC;QAExB,IAAI,uBAAuB,IAAI,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC;YAC9D,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC;gBAEnI,qBAAqB;gBACrB,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC9C,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;oBACrD,cAAc,GAAG,+CAA+C,CAAC;gBACnE,CAAC;qBAAM,CAAC;oBACN,gDAAgD;oBAChD,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,sBAAsB,EAAE;wBACzE,QAAQ;wBACR,OAAO,EAAE,EAAE,CAAC,kDAAkD;qBAC/D,CAAC,CAAC;oBAEH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;wBACtC,aAAa;wBACb,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC/B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,EAAE,KAAK,CAAC,CACvE;qBACF,CAAC,CAAC;oBAEH,IAAI,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;wBAChD,2DAA2D;wBAC3D,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC;wBACrC,MAAM,eAAe,GAAG;4BACtB,GAAG,QAAQ;4BACX,cAAc,EAAE,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,mBAAmB;4BAC/E,cAAc,EAAE;gCACd,GAAG,QAAQ,CAAC,cAAc;gCAC1B,iBAAiB,EAAE,QAAQ,CAAC,cAAc,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE;6BACjF;yBACF,CAAC;wBACF,cAAc,GAAG,gCAAgC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;oBAC9F,CAAC;yBAAM,CAAC;wBACN,cAAc,GAAG,6CAA6C,CAAC;oBACjE,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;gBACzD,cAAc,GAAG,wDAAwD,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,UAAU,GAAG;;;EAGrB,cAAc;;;EAGd,KAAK,CAAC,MAAM;;;EAGZ,aAAa;;;EAGb,SAAS;;;EAGT,UAAU;;;EAGV,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM;;;EAG5C,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,2BAA2B;;EAElL,cAAc;;;;;;;;;;;;;;;;CAgBf,CAAC;QACE,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC;QAC1B,mDAAmD;QACnD,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,MAAc;QAC5C,MAAM,iBAAiB,GAAG;YACxB,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,kBAAkB;YAClB,YAAY;YACZ,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;YACd,WAAW;YACX,YAAY;YACZ,eAAe;SAChB,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACzC,OAAO,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;IAC9E,CAAC;CACF;AAzKD,8BAyKC", "sourcesContent": ["import { Agent, Agent<PERSON>ontext, AgentRunInput, AgentRunResult } from '../agentUtilities/agent';\nimport { ITool } from '../../tools/types';\nimport { logger } from '../../logger';\nimport { CascadeEditingTool } from '../../tools/cascadeEditingTool';\n\nexport class EditAgent extends Agent {\n  private cascadeEditTool: CascadeEditingTool;\n\n  constructor(_config: any) {\n    super({\n      ..._config,\n      role: 'assistant',\n      capabilities: ['edit'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4'\n    });\n    this.cascadeEditTool = new CascadeEditingTool();\n  }\n\n  override async run(input: AgentRunInput, context: AgentContext = {}): Promise<AgentRunResult> {\n    logger.info(`EditAgent processing: \"${input.prompt.substring(0, 50)}...\"`);\n\n    // --- Gather relevant workflow for edit mode ---\n    let workflow: any = undefined;\n    if (context.variables?.workflowManager) {\n      workflow = await context.variables.workflowManager.getWorkflowForMode('edit');\n    }\n\n    // --- Gather all relevant tools ---\n    let tools = this.tools;\n    if (context.tools) {\n      tools = context.tools as Map<string, ITool>;\n    }\n\n    // --- Gather memory and knowledgebase context ---\n    let memoryContext = '';\n    if (this.getMemory) {\n      const memories = await this.getMemory().getRelevantMemories(input.prompt);\n      if (memories?.length) {\n        memoryContext = this.getMemory().formatMemoriesForPrompt(memories);\n      }\n    }\n    let kbContext = '';\n    if (context.variables?.knowledgebaseManager) {\n      kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input.prompt);\n    }\n\n    // --- Gather MCP context ---\n    let mcpContext = '';\n    if (context.variables?.mcpManager) {\n      mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext() || {});\n    }\n\n    // --- Use external prompt if available ---\n    let externalPrompt = '';\n    if (context.variables?.promptManager) {\n      externalPrompt = context.variables.promptManager.getSystemPrompt('editAgent', context.variables) || '';\n    }\n\n    // Phase 2: Enhanced editing with cascade capabilities (Optimized)\n    const shouldUseCascadeEditing = this.shouldUseCascadeEditing(input.prompt);\n    let cascadeContext = '';\n\n    if (shouldUseCascadeEditing && context.workspace?.currentFile) {\n      try {\n        const filePath = typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile;\n\n        // Validate file path\n        if (!filePath || filePath.trim().length === 0) {\n          logger.warn('Invalid file path for cascade editing');\n          cascadeContext = '\\n## Cascade Edit Analysis: Invalid file path';\n        } else {\n          // Analyze potential cascade impact with timeout\n          const impactPromise = this.cascadeEditTool.execute('analyzeCascadeImpact', {\n            filePath,\n            changes: [] // Would be populated with actual proposed changes\n          });\n\n          const impactResult = await Promise.race([\n            impactPromise,\n            new Promise<never>((_, reject) =>\n              setTimeout(() => reject(new Error('Cascade analysis timeout')), 15000)\n            )\n          ]);\n\n          if (impactResult.success && impactResult.output) {\n            // Limit the output size to prevent overwhelming the prompt\n            const analysis = impactResult.output;\n            const limitedAnalysis = {\n              ...analysis,\n              dependentEdits: analysis.dependentEdits?.slice(0, 5) || [], // Limit to 5 files\n              impactAnalysis: {\n                ...analysis.impactAnalysis,\n                testFilesAffected: analysis.impactAnalysis?.testFilesAffected?.slice(0, 3) || []\n              }\n            };\n            cascadeContext = `\\n## Cascade Edit Analysis:\\n${JSON.stringify(limitedAnalysis, null, 2)}`;\n          } else {\n            cascadeContext = '\\n## Cascade Edit Analysis: Analysis failed';\n          }\n        }\n      } catch (error) {\n        logger.warn(`Cascade editing analysis failed: ${error}`);\n        cascadeContext = '\\n## Cascade Edit Analysis: Not available due to error';\n      }\n    }\n\n    // --- Compose final prompt ---\n    const editPrompt = `\n# Enhanced Edit Mode Processing with Cascade Editing\n\n${externalPrompt}\n\n## User Request:\n${input.prompt}\n\n## Memory Context:\n${memoryContext}\n\n## Knowledgebase Context:\n${kbContext}\n\n## MCP Context:\n${mcpContext}\n\n## Workflow:\n${workflow ? JSON.stringify(workflow) : 'None'}\n\n## Current File Context:\n${context.workspace?.currentFile ? (typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile) : 'No file context available'}\n\n${cascadeContext}\n\n## Your Enhanced Editing Task:\n1. Analyze the requested changes and their potential impact\n2. Consider dependency relationships and cascade effects\n3. Provide specific code edits that address the user's request\n4. If cascade editing is needed, identify all affected files\n5. Suggest validation steps to ensure changes don't break dependencies\n\n## Available Advanced Tools:\n- Cascade editing for dependency-aware changes\n- Impact analysis for multi-file edits\n- Dependency validation\n- Automated propagation of changes\n\nUse all available workflows, tools, memory, knowledgebase, and cascade context. Stream output if possible.\n`;\n    input.prompt = editPrompt;\n    // Pass all gathered context and tools to super.run\n    return super.run(input, { ...context, tools });\n  }\n\n  /**\n     * Determine if cascade editing should be used\n     */\n  private shouldUseCascadeEditing(prompt: string): boolean {\n    const cascadeIndicators = [\n      'refactor',\n      'rename',\n      'change interface',\n      'update signature',\n      'modify api',\n      'breaking change',\n      'multiple files',\n      'dependencies',\n      'propagate',\n      'update all',\n      'global change'\n    ];\n\n    const promptLower = prompt.toLowerCase();\n    return cascadeIndicators.some(indicator => promptLower.includes(indicator));\n  }\n}"]}