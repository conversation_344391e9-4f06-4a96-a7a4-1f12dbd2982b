{"version": 3, "file": "patternRefactoringWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/patternRefactoringWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAaH,4EA2FC;AAKD,8FA8FC;AAvMD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,gCAAgC,CAC9C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,gBAAuB,EACvB,YAAmB,EACnB,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,IAAI,EAAE,CAAC,CAAC;IAEvE,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAClG,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,aAAa,CAAC,CAAC;IAC7H,MAAM,0BAA0B,GAAG,eAAO,CAAC,eAAe,CAAC,0BAA0B,EAAE,0BAA0B,EAAE,aAAa,CAAC,CAAC;IAClI,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;IAC9G,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;IACvH,MAAM,WAAW,GAAG,eAAO,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAChF,MAAM,uBAAuB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC,CAAC;IAC1H,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACxF,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,0BAA0B,EAAE,IAAI,EAAE,SAAS,EAAE;QACpH,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,0BAA0B,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9G,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QACxG,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QACxG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEtG,iBAAiB;QACjB,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,UAAU,EAAE;KAC1G,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,uCAAuC;QACvC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,0BAA0B,KAAK,EAAE;gBACvC,MAAM,EAAE,qBAAqB;gBAC7B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,gCAAgC;YAChC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,aAAa;gBAChC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,gBAAgB;YAChB,yBAAyB;YACzB,0BAA0B;YAC1B,mBAAmB;YACnB,sBAAsB;YACtB,WAAW;YACX,uBAAuB;YACvB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,qBAAsC;QACrD,IAAI,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,cAAc,CAAC;KACzD,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,yCAAyC,CACvD,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,gBAAuB,EACvB,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oDAAoD,IAAI,EAAE,CAAC,CAAC;IAEjF,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,aAAa,CAAC,CAAC;IAC1H,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC9G,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;IACjH,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,cAAc,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;IAC/F,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;IAC3G,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;IACzG,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC,CAAC;IACzH,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC;IACtG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAClH,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9G,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAE/F,iBAAiB;QACjB,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,UAAU,EAAE;KAC/G,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,kCAAkC;QAClC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,0BAA0B,KAAK,EAAE;gBACvC,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,6CAA6C;YAC7C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,UAAU;gBAC7B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,sBAAsB;gBAC9B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,wBAAwB;YACxB,oBAAoB;YACpB,qBAAqB;YACrB,eAAe;YACf,mBAAmB;YACnB,kBAAkB;YAClB,sBAAsB;YACtB,iBAAiB;YACjB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,qBAAsC;QACrD,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,cAAc,CAAC;KAC5D,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Pattern Refactoring Workflow\n *\n * This module provides a workflow for pattern-based code refactoring:\n * - Identifying code patterns\n * - Applying design patterns\n * - Refactoring code to follow best practices\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { Codessa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { Logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n\n/**\n * Create a Pattern Refactoring workflow\n */\nexport function createPatternRefactoringWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  analyzerAgent: Agent,\n  refactoringAgent: Agent,\n  testingAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating Pattern Refactoring workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const codeAnalysisNode = Codessa.createAgentNode('code-analysis', 'Code Analysis', analyzerAgent);\n  const patternIdentificationNode = Codessa.createAgentNode('pattern-identification', 'Pattern Identification', analyzerAgent);\n  const designPatternSelectionNode = Codessa.createAgentNode('design-pattern-selection', 'Design Pattern Selection', analyzerAgent);\n  const refactoringPlanNode = Codessa.createAgentNode('refactoring-plan', 'Refactoring Plan', refactoringAgent);\n  const codeTransformationNode = Codessa.createAgentNode('code-transformation', 'Code Transformation', refactoringAgent);\n  const testingNode = Codessa.createAgentNode('testing', 'Testing', testingAgent);\n  const documentationUpdateNode = Codessa.createAgentNode('documentation-update', 'Documentation Update', refactoringAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'default' },\n    { name: 'analysis-to-pattern', source: 'code-analysis', target: 'pattern-identification', type: 'default' },\n    { name: 'pattern-to-design', source: 'pattern-identification', target: 'design-pattern-selection', type: 'default' },\n    { name: 'design-to-plan', source: 'design-pattern-selection', target: 'refactoring-plan', type: 'default' },\n    { name: 'plan-to-transformation', source: 'refactoring-plan', target: 'code-transformation', type: 'default' },\n    { name: 'transformation-to-testing', source: 'code-transformation', target: 'testing', type: 'default' },\n    { name: 'testing-to-documentation', source: 'testing', target: 'documentation-update', type: 'default' },\n    { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'testing-to-transformation', source: 'testing', target: 'code-transformation', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect code transformation to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `transformation-to-tool-${index}`,\n        source: 'code-transformation',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to testing\n      edges.push({\n        name: `tool-${index}-to-testing`,\n        source: `tool-${index}`,\n        target: 'testing',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      codeAnalysisNode,\n      patternIdentificationNode,\n      designPatternSelectionNode,\n      refactoringPlanNode,\n      codeTransformationNode,\n      testingNode,\n      documentationUpdateNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'pattern-refactoring' as OperationMode,\n    tags: ['refactoring', 'design-patterns', 'code-quality']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized Design Pattern Implementation workflow\n */\nexport function createDesignPatternImplementationWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  designerAgent: Agent,\n  implementerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating Design Pattern Implementation workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const requirementsAnalysisNode = Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', designerAgent);\n  const patternSelectionNode = Codessa.createAgentNode('pattern-selection', 'Pattern Selection', designerAgent);\n  const patternAdaptationNode = Codessa.createAgentNode('pattern-adaptation', 'Pattern Adaptation', designerAgent);\n  const classDesignNode = Codessa.createAgentNode('class-design', 'Class Design', designerAgent);\n  const interfaceDesignNode = Codessa.createAgentNode('interface-design', 'Interface Design', designerAgent);\n  const implementationNode = Codessa.createAgentNode('implementation', 'Implementation', implementerAgent);\n  const testCaseGenerationNode = Codessa.createAgentNode('test-case-generation', 'Test Case Generation', implementerAgent);\n  const documentationNode = Codessa.createAgentNode('documentation', 'Documentation', implementerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },\n    { name: 'requirements-to-pattern', source: 'requirements-analysis', target: 'pattern-selection', type: 'default' },\n    { name: 'pattern-to-adaptation', source: 'pattern-selection', target: 'pattern-adaptation', type: 'default' },\n    { name: 'adaptation-to-class', source: 'pattern-adaptation', target: 'class-design', type: 'default' },\n    { name: 'adaptation-to-interface', source: 'pattern-adaptation', target: 'interface-design', type: 'default' },\n    { name: 'class-to-implementation', source: 'class-design', target: 'implementation', type: 'default' },\n    { name: 'interface-to-implementation', source: 'interface-design', target: 'implementation', type: 'default' },\n    { name: 'implementation-to-test', source: 'implementation', target: 'test-case-generation', type: 'default' },\n    { name: 'test-to-documentation', source: 'test-case-generation', target: 'documentation', type: 'default' },\n    { name: 'documentation-to-output', source: 'documentation', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'test-to-implementation', source: 'test-case-generation', target: 'implementation', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect implementation to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `implementation-to-tool-${index}`,\n        source: 'implementation',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to test case generation\n      edges.push({\n        name: `tool-${index}-to-test`,\n        source: `tool-${index}`,\n        target: 'test-case-generation',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      requirementsAnalysisNode,\n      patternSelectionNode,\n      patternAdaptationNode,\n      classDesignNode,\n      interfaceDesignNode,\n      implementationNode,\n      testCaseGenerationNode,\n      documentationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'pattern-refactoring' as OperationMode,\n    tags: ['design-patterns', 'implementation', 'architecture']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}