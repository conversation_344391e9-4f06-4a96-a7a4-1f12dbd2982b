{"_type": "UMLClass", "_id": "AAAAAAGH1FileChunkingService=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "FileChunkingService", "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAGH1FileChunkingServiceAttr1=", "_parent": {"$ref": "AAAAAAGH1FileChunkingService="}, "name": "instance", "visibility": "private", "isStatic": true, "type": "UniversalChunkingService | null", "defaultValue": "null"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1FileChunkingServiceAttr2=", "_parent": {"$ref": "AAAAAAGH1FileChunkingService="}, "name": "memoryStorer", "visibility": "private", "isStatic": true, "type": "IMemoryStorer | null", "defaultValue": "null"}], "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1FileChunkingServiceOp1=", "_parent": {"$ref": "AAAAAAGH1FileChunkingService="}, "name": "initialize", "visibility": "public", "isStatic": true, "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1FileChunkingServiceOp1P1=", "_parent": {"$ref": "AAAAAAGH1FileChunkingServiceOp1="}, "name": "memoryStorer", "type": "IMemoryStorer"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1FileChunkingServiceOp1P2=", "_parent": {"$ref": "AAAAAAGH1FileChunkingServiceOp1="}, "type": "void", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1FileChunkingServiceOp2=", "_parent": {"$ref": "AAAAAAGH1FileChunkingService="}, "name": "chunkFile", "visibility": "public", "isStatic": true, "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1FileChunkingServiceOp2P1=", "_parent": {"$ref": "AAAAAAGH1FileChunkingServiceOp2="}, "name": "filePath", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1FileChunkingServiceOp2P2=", "_parent": {"$ref": "AAAAAAGH1FileChunkingServiceOp2="}, "type": "Promise<MemoryEntry[]>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1FileChunkingServiceOp3=", "_parent": {"$ref": "AAAAAAGH1FileChunkingService="}, "name": "chunkWorkspace", "visibility": "public", "isStatic": true, "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1FileChunkingServiceOp3P1=", "_parent": {"$ref": "AAAAAAGH1FileChunkingServiceOp3="}, "name": "folderPath", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1FileChunkingServiceOp3P2=", "_parent": {"$ref": "AAAAAAGH1FileChunkingServiceOp3="}, "name": "includePatterns", "type": "string[]", "defaultValue": "['**/*.{js,ts,jsx,tsx,py,java,c,cpp,cs,go,rb,php,html,css,md,json}']"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1FileChunkingServiceOp3P3=", "_parent": {"$ref": "AAAAAAGH1FileChunkingServiceOp3="}, "name": "excludePatterns", "type": "string[]", "defaultValue": "['**/node_modules/**', '**/dist/**', '**/build/**', '**/.git/**']"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1FileChunkingServiceOp3P4=", "_parent": {"$ref": "AAAAAAGH1FileChunkingServiceOp3="}, "type": "Promise<MemoryEntry[]>", "direction": "return"}]}], "isAbstract": false}