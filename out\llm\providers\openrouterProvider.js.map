{"version": 3, "file": "openrouterProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/openrouterProvider.ts"], "names": [], "mappings": ";;;AACA,uDAAoD;AAIpD,yCAAsC;AAEtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;GAEG;AACH,MAAa,kBAAmB,SAAQ,iCAAe;IAC5C,UAAU,GAAG,YAAY,CAAC;IAC1B,WAAW,GAAG,YAAY,CAAC;IAC3B,WAAW,GAAG,iDAAiD,CAAC;IAChE,OAAO,GAAG,uBAAuB,CAAC;IAClC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,KAAK,CAAC;IACtC,eAAe,GAAG,8BAA8B,CAAC;IACjD,YAAY,GAAG,sBAAsB,CAAC;IAEvC,MAAM,GAAQ,IAAI,CAAC;IACnB,OAAO,CAAS;IAExB,YAAY,OAAgC;QAC1C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACG,gBAAgB;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBACjD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAC/C,cAAc,EAAE,uCAAuC,EAAE,yBAAyB;oBAClF,SAAS,EAAE,2BAA2B,CAAC,yBAAyB;iBACjE;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,YAAY;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC/C,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,mCAAmC,EAAE,CAAC;QACrE,CAAC;QAED,yCAAyC;QACzC,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;YAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,mBAAmB,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC;QAChF,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,4CAA4C;YAC5C,MAAM,QAAQ,GAAmF,EAAE,CAAC;YAEpG,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACxB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,MAAM,CAAC,YAAY;qBACpB,CAAC,CAAC;gBACd,CAAC;gBACD,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;iBACd,CAAC,CAAC;YACd,CAAC;YAED,2BAA2B;YAC3B,MAAM,SAAS,GAAG,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAClF,IAAI,EAAE,IAAI,CAAC,EAAE;gBACb,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAG,IAAY,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE;aAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAEhB,4CAA4C;YAC5C,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;YAEnD,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,4EAA4E;gBAC5E,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;wBACpD,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBACvG,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAC9D,KAAK,EAAE,OAAO;gBACd,QAAQ;gBACR,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS;gBAC5B,SAAS;gBACT,WAAW;aACZ,EAAE;gBACD,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,yDAAyD;YACzD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,mBAAmB,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC;YAChF,CAAC;YAED,+BAA+B;YAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;YAEjE,oBAAoB;YACpB,IAAI,eAAe,GAAkC,SAAS,CAAC;YAC/D,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;gBACrD,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;gBACpE,eAAe,GAAG;oBAChB,MAAM,EAAE,YAAY,CAAC,IAAI;oBACzB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC;iBACzC,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,MAAM;gBAC/D,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI;oBAC5B,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,CAAC;oBACnB,WAAW,EAAE,CAAC;iBACf;gBACD,QAAQ,EAAE,eAAe;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAClG,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,CAAC,cAAc,CAC1B,MAAyB,EACzB,iBAA4C;QAE5C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,kCAAkC;YAClC,MAAM,QAAQ,GAAmF,EAAE,CAAC;YAEpG,iCAAiC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,MAAM,CAAC,YAAY;iBACpB,CAAC,CAAC;YACd,CAAC;YAED,mCAAmC;YACnC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;YAED,yBAAyB;YACzB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;aACd,CAAC,CAAC;YAEZ,iCAAiC;YACjC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACpC,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,EAAE;gBAChC,MAAM,EAAE,IAAI;aACb,EAAE;gBACD,YAAY,EAAE,QAAQ;aACvB,CAAC,CAAC;YAEH,iCAAiC;YACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;oBAC/C,MAAM;gBACR,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAE3D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,+BAA+B;wBAC/B,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;4BAC5B,SAAS;wBACX,CAAC;wBAED,sBAAsB;wBACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;wBAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;wBAElC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;4BACpD,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;wBACtC,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC7G,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,eAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YACtE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAElD,6BAA6B;YAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC;YACzC,eAAM,CAAC,IAAI,CAAC,2BAA2B,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;YAEzE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;gBAC7B,EAAE,EAAE,CAAC,CAAC,EAAE;gBACR,IAAI,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;gBACpB,WAAW,EAAE,CAAC,CAAC,WAAW,IAAI,EAAE;gBAChC,aAAa,EAAE,CAAC,CAAC,cAAc,IAAI,IAAI;gBACvC,eAAe,EAAE,CAAC,CAAC,iBAAiB;gBACpC,iBAAiB,EAAE,CAAC,CAAC,kBAAkB,IAAI,KAAK;gBAChD,cAAc,EAAE,CAAC,CAAC,eAAe,IAAI,KAAK;aAC3C,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAe,EAAE,CAAe,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAE1D,6BAA6B;YAC7B,OAAO;gBACL;oBACE,EAAE,EAAE,sBAAsB;oBAC1B,IAAI,EAAE,eAAe;oBACrB,WAAW,EAAE,qCAAqC;oBAClD,aAAa,EAAE,IAAI;iBACpB;gBACD;oBACE,EAAE,EAAE,cAAc;oBAClB,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,6BAA6B;oBAC1C,aAAa,EAAE,IAAI;iBACpB;gBACD;oBACE,EAAE,EAAE,oBAAoB;oBACxB,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,mCAAmC;oBAChD,aAAa,EAAE,MAAM;iBACtB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,+DAA+D;aACzE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAEjC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,2CAA2C;aACrD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,wCAAwC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC5G,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,OAAY;QACpC,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,yBAAyB;gBACtC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,uDAAuD;gBACpE,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AAvWD,gDAuWC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { BaseLL<PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { logger } from '../../logger';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for OpenRouter API\n */\nexport class OpenRouterProvider extends BaseLLMProvider {\n  readonly providerId = 'openrouter';\n  readonly displayName = 'OpenRouter';\n  readonly description = 'Access multiple AI models through a unified API';\n  readonly website = 'https://openrouter.ai';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = false;\n  readonly defaultEndpoint = 'https://openrouter.ai/api/v1';\n  readonly defaultModel = 'openai/gpt-3.5-turbo';\n\n  private client: any = null;\n  private baseUrl: string;\n\n  constructor(context: vscode.ExtensionContext) {\n    super(context);\n    this.baseUrl = this.defaultEndpoint;\n    this.initializeClient();\n  }\n\n  /**\n     * Initialize the Axios client for API requests\n     */\n  private initializeClient(): void {\n    try {\n      if (!this.config.apiKey) {\n        logger.warn('OpenRouter API key not configured');\n        this.client = null;\n        return;\n      }\n\n      this.client = axios.create({\n        baseURL: this.baseUrl,\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'HTTP-Referer': 'https://github.com/djlaserman/Codessa', // Required by OpenRouter\n          'X-Title': 'Codessa VS Code Extension' // Required by OpenRouter\n        }\n      });\n\n      logger.info('OpenRouter client initialized');\n    } catch (error) {\n      logger.error('Failed to initialize OpenRouter client:', error);\n      this.client = null;\n    }\n  }\n\n  /**\n     * Check if the provider is configured\n     */\n  public isConfigured(): boolean {\n    return !!this.client && !!this.config.apiKey;\n  }\n\n  /**\n     * Generate text using OpenRouter\n     */\n  public async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'OpenRouter client not initialized' };\n    }\n\n    // Check for cancellation before starting\n    if (cancellationToken?.isCancellationRequested) {\n      return { content: '', error: 'Request cancelled', finishReason: 'cancelled' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare messages array with proper typing\n      const messages: Array<{ role: string; content: string; name?: string; tool_call_id?: string }> = [];\n\n      if (params.history && params.history.length > 0) {\n        messages.push(...params.history);\n      } else {\n        if (params.systemPrompt) {\n          messages.push({\n            role: 'system',\n            content: params.systemPrompt\n          } as const);\n        }\n        messages.push({\n          role: 'user',\n          content: params.prompt\n        } as const);\n      }\n\n      // Format tools if provided\n      const functions = tools && tools.size > 0 ? Array.from(tools.values()).map(tool => ({\n        name: tool.id,\n        description: tool.description,\n        parameters: (tool as any).singleActionSchema?.shape || { type: 'object', properties: {} }\n      })) : undefined;\n\n      // Only set tool_choice if we have functions\n      const tool_choice = functions ? 'auto' : undefined;\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        // Use the global AbortController if available, or provide a simple fallback\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            logger.info('OpenRouter request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          logger.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request\n      const response = await this.client.post('/v1/chat/completions', {\n        model: modelId,\n        messages,\n        temperature: params.temperature ?? 0.7,\n        max_tokens: params.maxTokens,\n        functions,\n        tool_choice\n      }, {\n        signal: abortController?.signal\n      });\n\n      // Check if the request was cancelled during the API call\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled', finishReason: 'cancelled' };\n      }\n\n      // Extract the response content\n      const content = response.data.choices[0]?.message?.content || '';\n\n      // Handle tool calls\n      let toolCallRequest: LLMGenerateResult['toolCall'] = undefined;\n      if (response.data.choices[0]?.message?.function_call) {\n        const functionCall = response.data.choices[0].message.function_call;\n        toolCallRequest = {\n          toolId: functionCall.name,\n          args: JSON.parse(functionCall.arguments)\n        };\n      }\n\n      return {\n        content,\n        finishReason: response.data.choices[0]?.finish_reason || 'stop',\n        usage: response.data.usage || {\n          promptTokens: 0,\n          completionTokens: 0,\n          totalTokens: 0\n        },\n        toolCall: toolCallRequest\n      };\n    } catch (error) {\n      logger.error('Error generating text with OpenRouter:', error);\n      return {\n        content: '',\n        error: `OpenRouter generation error: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * Stream generate text using OpenRouter\n     */\n  public async *streamGenerate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken\n  ): AsyncGenerator<string, void, unknown> {\n    if (!this.client) {\n      throw new Error('OpenRouter client not initialized');\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare messages for OpenRouter\n      const messages: Array<{ role: string; content: string; name?: string; tool_call_id?: string }> = [];\n\n      // Add system message if provided\n      if (params.systemPrompt) {\n        messages.push({\n          role: 'system',\n          content: params.systemPrompt\n        } as const);\n      }\n\n      // Add history messages if provided\n      if (params.history && params.history.length > 0) {\n        messages.push(...params.history);\n      }\n\n      // Add the current prompt\n      messages.push({\n        role: 'user',\n        content: params.prompt\n      } as const);\n\n      // Make the streaming API request\n      const response = await this.client.post('/chat/completions', {\n        model: modelId,\n        messages: messages,\n        temperature: params.temperature || 0.7,\n        max_tokens: params.maxTokens || 1024,\n        stop: params.stopSequences || [],\n        stream: true\n      }, {\n        responseType: 'stream'\n      });\n\n      // Process the streaming response\n      const stream = response.data;\n\n      for await (const chunk of stream) {\n        if (cancellationToken?.isCancellationRequested) {\n          break;\n        }\n\n        try {\n          const lines = chunk.toString().split('\\n').filter(Boolean);\n\n          for (const line of lines) {\n            // Skip \"data: [DONE]\" messages\n            if (line === 'data: [DONE]') {\n              continue;\n            }\n\n            // Parse the JSON data\n            const jsonData = line.replace(/^data: /, '');\n            const data = JSON.parse(jsonData);\n\n            if (data.choices && data.choices[0]?.delta?.content) {\n              yield data.choices[0].delta.content;\n            }\n          }\n        } catch (error) {\n          logger.error('Error parsing OpenRouter stream chunk:', error);\n        }\n      }\n    } catch (error) {\n      logger.error('Error streaming text with OpenRouter:', error);\n      throw new Error(`OpenRouter streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n     * List available models from OpenRouter\n     */\n  public async listModels(): Promise<LLMModelInfo[]> {\n    if (!this.client) {\n      logger.warn('Cannot fetch OpenRouter models, client not configured.');\n      return [];\n    }\n\n    try {\n      logger.debug('Fetching OpenRouter models list');\n      const response = await this.client.get('/models');\n\n      // OpenRouter response format\n      const models = response.data?.data || [];\n      logger.info(`Provider openrouter has ${models.length} models available`);\n\n      return models.map((m: any) => ({\n        id: m.id,\n        name: m.name || m.id,\n        description: m.description || '',\n        contextWindow: m.context_length || 4096,\n        maxOutputTokens: m.max_output_tokens,\n        supportsFunctions: m.supports_functions || false,\n        supportsVision: m.supports_vision || false\n      })).sort((a: LLMModelInfo, b: LLMModelInfo) => a.id.localeCompare(b.id));\n    } catch (error) {\n      logger.error('Failed to fetch OpenRouter models:', error);\n\n      // Return some default models\n      return [\n        {\n          id: 'openai/gpt-3.5-turbo',\n          name: 'GPT-3.5 Turbo',\n          description: 'OpenAI GPT-3.5 Turbo via OpenRouter',\n          contextWindow: 4096\n        },\n        {\n          id: 'openai/gpt-4',\n          name: 'GPT-4',\n          description: 'OpenAI GPT-4 via OpenRouter',\n          contextWindow: 8192\n        },\n        {\n          id: 'anthropic/claude-2',\n          name: 'Claude 2',\n          description: 'Anthropic Claude 2 via OpenRouter',\n          contextWindow: 100000\n        }\n      ];\n    }\n  }\n\n  /**\n     * Test connection to OpenRouter\n     */\n  public async testConnection(): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'OpenRouter client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Try a simple models request\n      await this.client.get('/models');\n\n      return {\n        success: true,\n        message: 'Successfully connected to OpenRouter API.'\n      };\n    } catch (error) {\n      logger.error('OpenRouter connection test failed:', error);\n      return {\n        success: false,\n        message: `Failed to connect to OpenRouter API: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * Update the provider configuration\n     */\n  public async updateConfig(_config: any): Promise<void> {\n    await super.updateConfig(_config);\n    this.initializeClient();\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your OpenRouter API key',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use (e.g., openai/gpt-3.5-turbo)',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n\n\n"]}