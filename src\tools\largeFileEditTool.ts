import * as vscode from 'vscode';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import { z } from 'zod';
import * as path from 'path';
import * as fs from 'fs';
import * as crypto from 'crypto';
import * as os from 'os';
import * as readline from 'readline';
import { Readable, Writable } from 'stream';
import * as stream from 'stream';
import { promisify } from 'util';

const pipeline = promisify(stream.pipeline);

/**
 * Tool for editing extremely large files with efficient streaming operations
 */
export class LargeFileEditTool implements ITool {
  readonly id = 'largeFileEdit';
  readonly name = 'Large File Edit';
  readonly description = 'Tool for editing extremely large files with efficient streaming operations.';
  readonly type = 'multi-action';
  // Add index signature to fix "Element implicitly has an 'any' type" error
  readonly actions: { [key: string]: any } = {
    'streamEdit': {
      description: 'Edit a large file using streaming to avoid loading the entire file into memory',
      schema: z.object({
        filePath: z.string().describe('Path to the file to edit.'),
        operations: z.array(z.object({
          type: z.enum(['replace', 'insert', 'delete']).describe('Type of operation.'),
          lineStart: z.number().describe('Start line number (1-based).'),
          lineEnd: z.number().optional().describe('End line number for replace/delete operations (1-based).'),
          content: z.string().optional().describe('New content for replace/insert operations.')
        })).describe('List of operations to perform on the file.'),
        outputPath: z.string().optional().describe('Path to save the edited file. If not provided, the original file will be modified.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before editing. Default is true.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to edit.'),
        operations: z.array(z.object({
          type: z.enum(['replace', 'insert', 'delete']).describe('Type of operation.'),
          lineStart: z.number().describe('Start line number (1-based).'),
          lineEnd: z.number().optional().describe('End line number for replace/delete operations (1-based).'),
          content: z.string().optional().describe('New content for replace/insert operations.')
        })).describe('List of operations to perform on the file.'),
        outputPath: z.string().optional().describe('Path to save the edited file. If not provided, the original file will be modified.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before editing. Default is true.')
      })
    },
    'streamReplace': {
      description: 'Replace text in a large file using streaming and regular expressions',
      schema: z.object({
        filePath: z.string().describe('Path to the file to edit.'),
        pattern: z.string().describe('Regular expression pattern to match.'),
        replacement: z.string().describe('Replacement text.'),
        global: z.boolean().optional().describe('Whether to replace all occurrences. Default is true.'),
        caseSensitive: z.boolean().optional().describe('Whether the pattern is case-sensitive. Default is true.'),
        outputPath: z.string().optional().describe('Path to save the edited file. If not provided, the original file will be modified.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before editing. Default is true.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to edit.'),
        pattern: z.string().describe('Regular expression pattern to match.'),
        replacement: z.string().describe('Replacement text.'),
        global: z.boolean().optional().describe('Whether to replace all occurrences. Default is true.'),
        caseSensitive: z.boolean().optional().describe('Whether the pattern is case-sensitive. Default is true.'),
        outputPath: z.string().optional().describe('Path to save the edited file. If not provided, the original file will be modified.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before editing. Default is true.')
      })
    },
    'chunkProcess': {
      description: 'Process a large file in chunks',
      schema: z.object({
        filePath: z.string().describe('Path to the file to process.'),
        chunkSize: z.number().optional().describe('Size of each chunk in bytes. Default is 10MB.'),
        operation: z.enum(['count', 'analyze', 'extract']).describe('Operation to perform on each chunk.'),
        pattern: z.string().optional().describe('Pattern to search for in extract operation.'),
        outputPath: z.string().optional().describe('Path to save the output. Required for extract operation.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to process.'),
        chunkSize: z.number().optional().describe('Size of each chunk in bytes. Default is 10MB.'),
        operation: z.enum(['count', 'analyze', 'extract']).describe('Operation to perform on each chunk.'),
        pattern: z.string().optional().describe('Pattern to search for in extract operation.'),
        outputPath: z.string().optional().describe('Path to save the output. Required for extract operation.')
      })
    },
    'getFileInfo': {
      description: 'Get information about a large file without loading it entirely',
      schema: z.object({
        filePath: z.string().describe('Path to the file to analyze.'),
        sampleLines: z.number().optional().describe('Number of lines to sample from the beginning, middle, and end. Default is 5.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to analyze.'),
        sampleLines: z.number().optional().describe('Number of lines to sample from the beginning, middle, and end. Default is 5.')
      })
    },
    'splitFile': {
      description: 'Split a large file into smaller chunks',
      schema: z.object({
        filePath: z.string().describe('Path to the file to split.'),
        outputDir: z.string().describe('Directory to save the split files.'),
        splitBy: z.enum(['lines', 'size']).describe('Split by lines or size.'),
        value: z.number().describe('Number of lines or size in bytes per chunk.'),
        prefix: z.string().optional().describe('Prefix for the split files. Default is the original filename.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to split.'),
        outputDir: z.string().describe('Directory to save the split files.'),
        splitBy: z.enum(['lines', 'size']).describe('Split by lines or size.'),
        value: z.number().describe('Number of lines or size in bytes per chunk.'),
        prefix: z.string().optional().describe('Prefix for the split files. Default is the original filename.')
      })
    }
  };

  // Backup management
  private static backups = new Map<string, { path: string, timestamp: number }>();
  private static readonly BACKUP_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours
  private static readonly DEFAULT_CHUNK_SIZE = 10 * 1024 * 1024; // 10MB

  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      if (!actionName || !this.actions[actionName]) {
        return {
          success: false,
          error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,
          toolId: this.id,
          actionName
        };
      }

      switch (actionName) {
        case 'streamEdit':
          return this.executeStreamEdit(input, actionName);
        case 'streamReplace':
          return this.executeStreamReplace(input, actionName);
        case 'chunkProcess':
          return this.executeChunkProcess(input, actionName);
        case 'getFileInfo':
          return this.executeGetFileInfo(input, actionName);
        case 'splitFile':
          return this.executeSplitFile(input, actionName);
        default:
          return {
            success: false,
            error: `Unknown action: ${actionName}`,
            toolId: this.id,
            actionName
          };
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Large file edit failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Create a backup of a file before editing
     */
  private async createBackup(filePath: string): Promise<string> {
    try {
      // Clean up old backups
      this.cleanupOldBackups();

      // Create a backup file
      const backupDir = path.join(os.tmpdir(), 'codessa-backups');

      // Ensure backup directory exists
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      const backupPath = path.join(backupDir, `${path.basename(filePath)}.${Date.now()}.bak`);

      // Copy the file to the backup location using streams
      await pipeline(
        fs.createReadStream(filePath),
        fs.createWriteStream(backupPath)
      );

      // Store the backup info
      LargeFileEditTool.backups.set(filePath, {
        path: backupPath,
        timestamp: Date.now()
      });

      return backupPath;
    } catch (error) {
      throw new Error(`Failed to create backup of ${filePath}: ${error}`);
    }
  }

  /**
     * Restore a file from backup
     */
  private async restoreFromBackup(filePath: string): Promise<boolean> {
    try {
      const backup = LargeFileEditTool.backups.get(filePath);
      if (!backup) {
        return false;
      }

      // Copy the backup back to the original location using streams
      await pipeline(
        fs.createReadStream(backup.path),
        fs.createWriteStream(filePath)
      );

      return true;
    } catch (error) {
      throw new Error(`Failed to restore ${filePath} from backup: ${error}`);
    }
  }

  /**
     * Clean up old backups
     */
  private cleanupOldBackups(): void {
    const now = Date.now();
    // Convert entries() iterator to array before iterating
    const entries = Array.from(LargeFileEditTool.backups.entries());
    for (const [filePath, backup] of entries) {
      if (now - backup.timestamp > LargeFileEditTool.BACKUP_EXPIRY_MS) {
        try {
          if (fs.existsSync(backup.path)) {
            fs.unlinkSync(backup.path);
          }
          LargeFileEditTool.backups.delete(filePath);
        } catch (error) {
          console.error(`Failed to delete backup ${backup.path}: ${error}`);
        }
      }
    }
  }

  /**
     * Get the number of lines in a file without loading it entirely
     */
  private async getLineCount(filePath: string): Promise<number> {
    return new Promise<number>((resolve, reject) => {
      let lineCount = 0;

      const rl = readline.createInterface({
        input: fs.createReadStream(filePath),
        crlfDelay: Infinity
      });

      rl.on('line', () => {
        lineCount++;
      });

      rl.on('close', () => {
        resolve(lineCount);
      });

      rl.on('error', (err) => {
        reject(err);
      });
    });
  }

  /**
     * Execute stream edit action
     */
  private async executeStreamEdit(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const filePath = input.filePath as string;
      const operations = input.operations as Array<{
        type: string;
        lineStart: number;
        lineEnd?: number;
        content?: string;
      }>;
      const outputPath = input.outputPath as string;
      const createBackup = input.createBackup !== false; // Default to true

      if (!filePath) {
        return {
          success: false,
          error: '\'filePath\' is required.',
          toolId: this.id,
          actionName
        };
      }

      if (!operations || !Array.isArray(operations) || operations.length === 0) {
        return {
          success: false,
          error: '\'operations\' is required and must be a non-empty array.',
          toolId: this.id,
          actionName
        };
      }

      // Validate file exists
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      // Validate operations
      for (const op of operations) {
        if (!op.type) {
          return {
            success: false,
            error: 'Each operation must have a \'type\'.',
            toolId: this.id,
            actionName
          };
        }

        if (op.lineStart === undefined || op.lineStart === null || op.lineStart < 1) {
          return {
            success: false,
            error: 'Each operation must have a valid \'lineStart\' (>= 1).',
            toolId: this.id,
            actionName
          };
        }

        if ((op.type === 'replace' || op.type === 'delete') &&
          (op.lineEnd === undefined || op.lineEnd === null || op.lineEnd < op.lineStart)) {
          return {
            success: false,
            error: `Operation of type '${op.type}' must have a valid 'lineEnd' (>= lineStart).`,
            toolId: this.id,
            actionName
          };
        }

        if ((op.type === 'replace' || op.type === 'insert') && op.content === undefined) {
          return {
            success: false,
            error: `Operation of type '${op.type}' must have 'content'.`,
            toolId: this.id,
            actionName
          };
        }
      }

      // Get line count to validate operations
      const lineCount = await this.getLineCount(filePath);

      for (const op of operations) {
        if (op.lineStart > lineCount) {
          return {
            success: false,
            error: `Invalid lineStart: ${op.lineStart} (file has ${lineCount} lines).`,
            toolId: this.id,
            actionName
          };
        }

        if (op.lineEnd && op.lineEnd > lineCount) {
          return {
            success: false,
            error: `Invalid lineEnd: ${op.lineEnd} (file has ${lineCount} lines).`,
            toolId: this.id,
            actionName
          };
        }
      }

      // Sort operations by line number in descending order to avoid line number changes
      operations.sort((a, b) => b.lineStart - a.lineStart);

      // Create backup if requested
      let backupPath = '';
      if (createBackup) {
        backupPath = await this.createBackup(filePath);
      }

      // Create temporary output file
      const tempOutputPath = outputPath || path.join(os.tmpdir(), `codessa-${Date.now()}-${path.basename(filePath)}`);

      try {
        // Process the file line by line
        await this.processFileWithOperations(filePath, tempOutputPath, operations);

        // If no output path was specified, replace the original file
        if (!outputPath) {
          await fs.promises.rename(tempOutputPath, filePath);
        }

        return {
          success: true,
          output: {
            message: `Successfully edited ${filePath}`,
            operationsApplied: operations.length,
            outputPath: outputPath || filePath,
            backupCreated: createBackup,
            backupPath: createBackup ? backupPath : undefined
          },
          toolId: this.id,
          actionName,
          metadata: {
            filePath,
            outputPath: outputPath || filePath,
            operationsCount: operations.length
          }
        };
      } catch (error) {
        // Clean up temporary file
        if (!outputPath && fs.existsSync(tempOutputPath)) {
          fs.unlinkSync(tempOutputPath);
        }

        // Restore from backup if something went wrong
        if (createBackup) {
          await this.restoreFromBackup(filePath);
        }

        throw error;
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Stream edit failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Process a file with the specified operations
     */
  private async processFileWithOperations(
    inputPath: string,
    outputPath: string,
    operations: Array<{
      type: string;
      lineStart: number;
      lineEnd?: number;
      content?: string;
    }>
  ): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const rl = readline.createInterface({
        input: fs.createReadStream(inputPath),
        crlfDelay: Infinity
      });

      const outputStream = fs.createWriteStream(outputPath);
      let currentLine = 0;
      let skipUntilLine = -1;

      // Create a map of line numbers to operations for quick lookup
      const lineToOp = new Map<number, typeof operations[0]>();
      for (const op of operations) {
        lineToOp.set(op.lineStart, op);
      }

      rl.on('line', (line) => {
        currentLine++;

        // Skip lines that are part of a replace or delete operation
        if (currentLine < skipUntilLine) {
          return;
        }

        // Reset skip flag
        if (currentLine === skipUntilLine) {
          skipUntilLine = -1;
          return;
        }

        // Check if there's an operation for this line
        const operation = lineToOp.get(currentLine);

        if (operation) {
          switch (operation.type) {
            case 'replace':
              // Write the replacement content
              outputStream.write(operation.content + '\n');
              // Skip the original lines
              skipUntilLine = (operation.lineEnd || currentLine) + 1;
              break;

            case 'insert':
              // Write the original line
              outputStream.write(line + '\n');
              // Write the inserted content
              outputStream.write(operation.content + '\n');
              break;

            case 'delete':
              // Skip the lines to delete
              skipUntilLine = (operation.lineEnd || currentLine) + 1;
              break;
          }
        } else {
          // Write the original line
          outputStream.write(line + '\n');
        }
      });

      rl.on('close', () => {
        outputStream.end();
        resolve();
      });

      rl.on('error', (err) => {
        outputStream.end();
        reject(err);
      });

      outputStream.on('error', (err) => {
        rl.close();
        reject(err);
      });
    });
  }

  /**
     * Execute stream replace action
     */
  private async executeStreamReplace(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const filePath = input.filePath as string;
      const pattern = input.pattern as string;
      const replacement = input.replacement as string;
      const global = input.global !== false; // Default to true
      const caseSensitive = input.caseSensitive !== false; // Default to true
      const outputPath = input.outputPath as string;
      const createBackup = input.createBackup !== false; // Default to true

      if (!filePath) {
        return {
          success: false,
          error: '\'filePath\' is required.',
          toolId: this.id,
          actionName
        };
      }

      if (pattern === undefined) {
        return {
          success: false,
          error: '\'pattern\' is required.',
          toolId: this.id,
          actionName
        };
      }

      if (replacement === undefined) {
        return {
          success: false,
          error: '\'replacement\' is required.',
          toolId: this.id,
          actionName
        };
      }

      // Validate file exists
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      // Create backup if requested
      let backupPath = '';
      if (createBackup) {
        backupPath = await this.createBackup(filePath);
      }

      // Create temporary output file
      const tempOutputPath = outputPath || path.join(os.tmpdir(), `codessa-${Date.now()}-${path.basename(filePath)}`);

      try {
        // Create regex from pattern
        const flags = `${global ? 'g' : ''}${caseSensitive ? '' : 'i'}`;
        const regex = new RegExp(pattern, flags);

        // Process the file line by line
        const stats = await this.processFileWithRegex(filePath, tempOutputPath, regex, replacement);

        // If no output path was specified, replace the original file
        if (!outputPath) {
          await fs.promises.rename(tempOutputPath, filePath);
        }

        return {
          success: true,
          output: {
            message: `Successfully replaced text in ${filePath}`,
            replacements: stats.replacements,
            linesAffected: stats.linesAffected,
            outputPath: outputPath || filePath,
            backupCreated: createBackup,
            backupPath: createBackup ? backupPath : undefined
          },
          toolId: this.id,
          actionName,
          metadata: {
            filePath,
            outputPath: outputPath || filePath,
            pattern,
            replacements: stats.replacements
          }
        };
      } catch (error) {
        // Clean up temporary file
        if (!outputPath && fs.existsSync(tempOutputPath)) {
          fs.unlinkSync(tempOutputPath);
        }

        // Restore from backup if something went wrong
        if (createBackup) {
          await this.restoreFromBackup(filePath);
        }

        throw error;
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Stream replace failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Process a file with regex replacement
     */
  private async processFileWithRegex(
    inputPath: string,
    outputPath: string,
    regex: RegExp,
    replacement: string
  ): Promise<{ replacements: number, linesAffected: number }> {
    return new Promise<{ replacements: number, linesAffected: number }>((resolve, reject) => {
      const rl = readline.createInterface({
        input: fs.createReadStream(inputPath),
        crlfDelay: Infinity
      });

      const outputStream = fs.createWriteStream(outputPath);
      let replacements = 0;
      let linesAffected = 0;

      rl.on('line', (line) => {
        // Check if the line contains the pattern
        const originalLine = line;
        const newLine = line.replace(regex, (match) => {
          replacements++;
          return replacement;
        });

        // Write the line to the output file
        outputStream.write(newLine + '\n');

        // Count affected lines
        if (originalLine !== newLine) {
          linesAffected++;
        }
      });

      rl.on('close', () => {
        outputStream.end();
        resolve({ replacements, linesAffected });
      });

      rl.on('error', (err) => {
        outputStream.end();
        reject(err);
      });

      outputStream.on('error', (err) => {
        rl.close();
        reject(err);
      });
    });
  }

  /**
     * Execute chunk process action
     */
  private async executeChunkProcess(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const filePath = input.filePath as string;
      const chunkSize = (input.chunkSize as number) || LargeFileEditTool.DEFAULT_CHUNK_SIZE;
      const operation = input.operation as string;
      const pattern = input.pattern as string;
      const outputPath = input.outputPath as string;

      if (!filePath) {
        return {
          success: false,
          error: '\'filePath\' is required.',
          toolId: this.id,
          actionName
        };
      }

      if (!operation) {
        return {
          success: false,
          error: '\'operation\' is required.',
          toolId: this.id,
          actionName
        };
      }

      // Validate file exists
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      // Validate operation-specific parameters
      if (operation === 'extract' && !pattern) {
        return {
          success: false,
          error: '\'pattern\' is required for \'extract\' operation.',
          toolId: this.id,
          actionName
        };
      }

      if (operation === 'extract' && !outputPath) {
        return {
          success: false,
          error: '\'outputPath\' is required for \'extract\' operation.',
          toolId: this.id,
          actionName
        };
      }

      // Get file stats
      const stats = await fs.promises.stat(filePath);

      // Process the file in chunks
      let result: any;

      switch (operation) {
        case 'count':
          result = await this.countLinesInChunks(filePath, chunkSize);
          break;

        case 'analyze':
          result = await this.analyzeFileInChunks(filePath, chunkSize);
          break;

        case 'extract':
          result = await this.extractPatternsInChunks(filePath, pattern, outputPath, chunkSize);
          break;

        default:
          return {
            success: false,
            error: `Unsupported operation: ${operation}`,
            toolId: this.id,
            actionName
          };
      }

      return {
        success: true,
        output: {
          operation,
          result,
          fileInfo: {
            path: filePath,
            size: stats.size,
            chunks: Math.ceil(stats.size / chunkSize)
          }
        },
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          operation,
          chunkSize
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Chunk process failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Count lines in a file using chunks
     */
  private async countLinesInChunks(filePath: string, chunkSize: number): Promise<{ lines: number, processingTime: number }> {
    return new Promise<{ lines: number, processingTime: number }>(async (resolve, reject) => {
      try {
        const startTime = Date.now();
        let lineCount = 0;

        const rl = readline.createInterface({
          input: fs.createReadStream(filePath, { highWaterMark: chunkSize }),
          crlfDelay: Infinity
        });

        rl.on('line', () => {
          lineCount++;
        });

        rl.on('close', () => {
          const endTime = Date.now();
          resolve({
            lines: lineCount,
            processingTime: endTime - startTime
          });
        });

        rl.on('error', (err) => {
          reject(err);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
     * Analyze a file in chunks
     */
  private async analyzeFileInChunks(filePath: string, chunkSize: number): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      try {
        const startTime = Date.now();
        let lineCount = 0;
        let charCount = 0;
        let wordCount = 0;
        let emptyLines = 0;
        let maxLineLength = 0;
        let minLineLength = Number.MAX_SAFE_INTEGER;
        let totalLineLength = 0;

        const rl = readline.createInterface({
          input: fs.createReadStream(filePath, { highWaterMark: chunkSize }),
          crlfDelay: Infinity
        });

        rl.on('line', (line) => {
          lineCount++;
          charCount += line.length;

          // Count words
          const words = line.trim().split(/\s+/).filter(word => word.length > 0);
          wordCount += words.length;

          // Count empty lines
          if (line.trim().length === 0) {
            emptyLines++;
          }

          // Track line length stats
          maxLineLength = Math.max(maxLineLength, line.length);
          minLineLength = Math.min(minLineLength, line.length);
          totalLineLength += line.length;
        });

        rl.on('close', () => {
          const endTime = Date.now();

          // Calculate average line length
          const avgLineLength = lineCount > 0 ? totalLineLength / lineCount : 0;

          // If no lines were processed, set minLineLength to 0
          if (lineCount === 0) {
            minLineLength = 0;
          }

          resolve({
            lines: lineCount,
            characters: charCount,
            words: wordCount,
            emptyLines,
            lineStats: {
              maxLength: maxLineLength,
              minLength: minLineLength,
              avgLength: avgLineLength
            },
            processingTime: endTime - startTime
          });
        });

        rl.on('error', (err) => {
          reject(err);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
     * Extract patterns from a file in chunks
     */
  private async extractPatternsInChunks(filePath: string, pattern: string, outputPath: string, chunkSize: number): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      try {
        const startTime = Date.now();
        let matchCount = 0;
        let lineCount = 0;
        let matchedLines = 0;

        // Create regex from pattern
        const regex = new RegExp(pattern, 'g');

        const rl = readline.createInterface({
          input: fs.createReadStream(filePath, { highWaterMark: chunkSize }),
          crlfDelay: Infinity
        });

        const outputStream = fs.createWriteStream(outputPath);

        rl.on('line', (line) => {
          lineCount++;

          // Check if the line contains the pattern
          const matches = line.match(regex);

          if (matches) {
            matchCount += matches.length;
            matchedLines++;

            // Write the matched line to the output file
            outputStream.write(`${line}\n`);
          }
        });

        rl.on('close', () => {
          outputStream.end();
          const endTime = Date.now();

          resolve({
            matchCount,
            matchedLines,
            totalLines: lineCount,
            matchPercentage: lineCount > 0 ? (matchedLines / lineCount) * 100 : 0,
            outputPath,
            processingTime: endTime - startTime
          });
        });

        rl.on('error', (err) => {
          outputStream.end();
          reject(err);
        });

        outputStream.on('error', (err) => {
          rl.close();
          reject(err);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
     * Execute get file info action
     */
  private async executeGetFileInfo(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const filePath = input.filePath as string;
      const sampleLines = (input.sampleLines as number) || 5;

      if (!filePath) {
        return {
          success: false,
          error: '\'filePath\' is required.',
          toolId: this.id,
          actionName
        };
      }

      // Validate file exists
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      // Get file stats
      const stats = await fs.promises.stat(filePath);

      // Get line count
      const lineCount = await this.getLineCount(filePath);

      // Get sample lines from beginning, middle, and end
      const samples = await this.getSampleLines(filePath, sampleLines);

      // Detect file type
      const fileType = this.detectFileType(filePath, samples.beginning.join('\n'));

      return {
        success: true,
        output: {
          fileInfo: {
            path: filePath,
            size: stats.size,
            sizeFormatted: this.formatFileSize(stats.size),
            lines: lineCount,
            type: fileType,
            created: stats.birthtime,
            modified: stats.mtime,
            permissions: this.formatPermissions(stats.mode)
          },
          samples
        },
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          size: stats.size,
          lines: lineCount
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Get file info failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Get sample lines from beginning, middle, and end of a file
     */
  private async getSampleLines(filePath: string, count: number): Promise<{ beginning: string[], middle: string[], end: string[] }> {
    return new Promise<{ beginning: string[], middle: string[], end: string[] }>(async (resolve, reject) => {
      try {
        // Get line count first
        const lineCount = await this.getLineCount(filePath);

        // Adjust count if file has fewer lines
        const actualCount = Math.min(count, Math.floor(lineCount / 3));

        // Calculate middle start line
        const middleStart = Math.floor(lineCount / 2) - Math.floor(actualCount / 2);

        const beginning: string[] = [];
        const middle: string[] = [];
        const end: string[] = [];

        const rl = readline.createInterface({
          input: fs.createReadStream(filePath),
          crlfDelay: Infinity
        });

        let currentLine = 0;

        rl.on('line', (line) => {
          currentLine++;

          // Collect beginning lines
          if (currentLine <= actualCount) {
            beginning.push(line);
          }

          // Collect middle lines
          if (currentLine >= middleStart && currentLine < middleStart + actualCount) {
            middle.push(line);
          }

          // Collect end lines
          if (lineCount - currentLine < actualCount) {
            end.push(line);
          }
        });

        rl.on('close', () => {
          resolve({ beginning, middle, end });
        });

        rl.on('error', (err) => {
          reject(err);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
     * Format file size in human-readable format
     */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
     * Format file permissions
     */
  private formatPermissions(mode: number): string {
    const octal = (mode & 0o777).toString(8);

    // Convert to rwx format
    const permissions = [
      (mode & 0o400) ? 'r' : '-',
      (mode & 0o200) ? 'w' : '-',
      (mode & 0o100) ? 'x' : '-',
      (mode & 0o040) ? 'r' : '-',
      (mode & 0o020) ? 'w' : '-',
      (mode & 0o010) ? 'x' : '-',
      (mode & 0o004) ? 'r' : '-',
      (mode & 0o002) ? 'w' : '-',
      (mode & 0o001) ? 'x' : '-'
    ].join('');

    return `${octal} (${permissions})`;
  }

  /**
     * Detect file type based on extension and content
     */
  private detectFileType(filePath: string, sampleContent: string): string {
    const ext = path.extname(filePath).toLowerCase();

    // Check by extension first
    const extensionMap: Record<string, string> = {
      '.js': 'JavaScript',
      '.jsx': 'JavaScript (React)',
      '.ts': 'TypeScript',
      '.tsx': 'TypeScript (React)',
      '.py': 'Python',
      '.java': 'Java',
      '.c': 'C',
      '.cpp': 'C++',
      '.cs': 'C#',
      '.go': 'Go',
      '.rb': 'Ruby',
      '.php': 'PHP',
      '.swift': 'Swift',
      '.rs': 'Rust',
      '.html': 'HTML',
      '.css': 'CSS',
      '.json': 'JSON',
      '.xml': 'XML',
      '.md': 'Markdown',
      '.sh': 'Shell Script',
      '.txt': 'Text',
      '.csv': 'CSV',
      '.sql': 'SQL'
    };

    if (extensionMap[ext]) {
      return extensionMap[ext];
    }

    // If extension doesn't give us a clear answer, check content
    if (sampleContent.startsWith('<?xml')) {
      return 'XML';
    }

    if (sampleContent.startsWith('<!DOCTYPE html') || sampleContent.includes('<html')) {
      return 'HTML';
    }

    try {
      JSON.parse(sampleContent);
      return 'JSON';
    } catch (e) {
      // Not JSON
    }

    // Check if it's a binary file
    for (let i = 0; i < Math.min(sampleContent.length, 100); i++) {
      const charCode = sampleContent.charCodeAt(i);
      if (charCode === 0 || (charCode < 32 && ![9, 10, 13].includes(charCode))) {
        return 'Binary';
      }
    }

    // Default to text
    return 'Text';
  }

  /**
     * Execute split file action
     */
  private async executeSplitFile(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const filePath = input.filePath as string;
      const outputDir = input.outputDir as string;
      const splitBy = input.splitBy as string;
      const value = input.value as number;
      const prefix = (input.prefix as string) || path.basename(filePath);

      if (!filePath) {
        return {
          success: false,
          error: '\'filePath\' is required.',
          toolId: this.id,
          actionName
        };
      }

      if (!outputDir) {
        return {
          success: false,
          error: '\'outputDir\' is required.',
          toolId: this.id,
          actionName
        };
      }

      if (!splitBy) {
        return {
          success: false,
          error: '\'splitBy\' is required.',
          toolId: this.id,
          actionName
        };
      }

      if (value === undefined || value === null || value <= 0) {
        return {
          success: false,
          error: '\'value\' is required and must be greater than 0.',
          toolId: this.id,
          actionName
        };
      }

      // Validate file exists
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        await fs.promises.mkdir(outputDir, { recursive: true });
      }

      // Split the file
      let result: any;

      if (splitBy === 'lines') {
        result = await this.splitFileByLines(filePath, outputDir, value, prefix);
      } else if (splitBy === 'size') {
        result = await this.splitFileBySize(filePath, outputDir, value, prefix);
      } else {
        return {
          success: false,
          error: `Unsupported split method: ${splitBy}`,
          toolId: this.id,
          actionName
        };
      }

      return {
        success: true,
        output: {
          splitBy,
          value,
          result
        },
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          outputDir,
          splitBy,
          value
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Split file failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Split a file by lines
     */
  private async splitFileByLines(filePath: string, outputDir: string, linesPerFile: number, prefix: string): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      try {
        const startTime = Date.now();
        const outputFiles: string[] = [];

        const rl = readline.createInterface({
          input: fs.createReadStream(filePath),
          crlfDelay: Infinity
        });

        let currentFileIndex = 1;
        let currentLineCount = 0;
        let totalLineCount = 0;
        let outputStream: fs.WriteStream | null = null;
        let currentOutputPath = '';

        const createNewOutputFile = () => {
          // Close previous stream if it exists
          if (outputStream) {
            outputStream.end();
          }

          // Create new output file path
          const paddedIndex = currentFileIndex.toString().padStart(5, '0');
          currentOutputPath = path.join(outputDir, `${prefix}.${paddedIndex}`);
          outputFiles.push(currentOutputPath);

          // Create new output stream
          outputStream = fs.createWriteStream(currentOutputPath);
          currentFileIndex++;
          currentLineCount = 0;
        };

        // Create the first output file
        createNewOutputFile();

        rl.on('line', (line) => {
          totalLineCount++;
          currentLineCount++;

          // Write the line to the current output file
          outputStream!.write(line + '\n');

          // Check if we need to create a new output file
          if (currentLineCount >= linesPerFile) {
            createNewOutputFile();
          }
        });

        rl.on('close', () => {
          // Close the last output stream
          if (outputStream) {
            outputStream.end();
          }

          const endTime = Date.now();

          resolve({
            totalLines: totalLineCount,
            totalFiles: outputFiles.length,
            outputFiles,
            linesPerFile,
            processingTime: endTime - startTime
          });
        });

        rl.on('error', (err) => {
          // Close the output stream
          if (outputStream) {
            outputStream.end();
          }

          reject(err);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
     * Split a file by size
     */
  private async splitFileBySize(filePath: string, outputDir: string, bytesPerFile: number, prefix: string): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      try {
        const startTime = Date.now();
        const outputFiles: string[] = [];

        const fileStream = fs.createReadStream(filePath, {
          highWaterMark: Math.min(bytesPerFile, 1024 * 1024) // Use smaller chunks for better control
        });

        let currentFileIndex = 1;
        let currentFileSize = 0;
        let totalBytesProcessed = 0;
        let outputStream: fs.WriteStream | null = null;
        let currentOutputPath = '';

        const createNewOutputFile = () => {
          // Close previous stream if it exists
          if (outputStream) {
            outputStream.end();
          }

          // Create new output file path
          const paddedIndex = currentFileIndex.toString().padStart(5, '0');
          currentOutputPath = path.join(outputDir, `${prefix}.${paddedIndex}`);
          outputFiles.push(currentOutputPath);

          // Create new output stream
          outputStream = fs.createWriteStream(currentOutputPath);
          currentFileIndex++;
          currentFileSize = 0;
        };

        // Create the first output file
        createNewOutputFile();

        fileStream.on('data', (chunk) => {
          const chunkSize = chunk.length;
          totalBytesProcessed += chunkSize;

          // If adding this chunk would exceed the size limit, split it
          if (currentFileSize + chunkSize > bytesPerFile) {
            // Calculate how much of the chunk can fit in the current file
            const bytesRemaining = bytesPerFile - currentFileSize;

            if (bytesRemaining > 0) {
              // Write the portion that fits
              outputStream!.write(chunk.slice(0, bytesRemaining));
              currentFileSize += bytesRemaining;
            }

            // Create a new output file
            createNewOutputFile();

            // Write the rest of the chunk to the new file
            const remainingChunk = chunk.slice(bytesRemaining);
            if (remainingChunk.length > 0) {
              outputStream!.write(remainingChunk);
              currentFileSize += remainingChunk.length;
            }
          } else {
            // Write the entire chunk
            outputStream!.write(chunk);
            currentFileSize += chunkSize;
          }

          // Check if we need to create a new output file
          if (currentFileSize >= bytesPerFile) {
            createNewOutputFile();
          }
        });

        fileStream.on('end', () => {
          // Close the last output stream
          if (outputStream) {
            outputStream.end();
          }

          const endTime = Date.now();

          resolve({
            totalBytes: totalBytesProcessed,
            totalFiles: outputFiles.length,
            outputFiles,
            bytesPerFile,
            processingTime: endTime - startTime
          });
        });

        fileStream.on('error', (err) => {
          // Close the output stream
          if (outputStream) {
            outputStream.end();
          }

          reject(err);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}
