"use strict";
/**
 * Codessa UI-UX Workflow
 *
 * This module provides workflow templates for UI/UX design tasks:
 * - User research
 * - Wireframing
 * - Prototyping
 * - Usability testing
 * - Implementation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUIUXWorkflow = createUIUXWorkflow;
exports.createUIComponentWorkflow = createUIComponentWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
const codessaMemory_1 = require("../../memory/codessa/codessaMemory");
/**
 * Create a UI-UX workflow for design and user experience tasks
 */
function createUIUXWorkflow(id, name, description, designerAgent, developerAgent, userResearchAgent, tools = []) {
    logger_1.Logger.instance.info(`Creating UI-UX workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const requirementsAnalysisNode = graph_1.Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', userResearchAgent);
    const userResearchNode = graph_1.Codessa.createAgentNode('user-research', 'User Research', userResearchAgent);
    const wireframingNode = graph_1.Codessa.createAgentNode('wireframing', 'Wireframing', designerAgent);
    const designSystemNode = graph_1.Codessa.createAgentNode('design-system', 'Design System', designerAgent);
    const prototypeNode = graph_1.Codessa.createAgentNode('prototype', 'Prototype', designerAgent);
    const usabilityTestingNode = graph_1.Codessa.createAgentNode('usability-testing', 'Usability Testing', userResearchAgent);
    const implementationNode = graph_1.Codessa.createAgentNode('implementation', 'Implementation', developerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create a memory manager for the UI-UX workflow
    const uiUxMemoryManager = {
        async storeDesignArtifact(artifactData, artifactType) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(artifactData),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['ui-ux', 'design', artifactType]
                    }
                });
                logger_1.Logger.instance.info(`Saved ${artifactType} to memory`);
            }
            catch (error) {
                logger_1.Logger.instance.error(`Failed to save ${artifactType} to memory:`, error);
            }
        },
        async retrieveDesignArtifacts(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5,
                    filter: {
                        tags: ['ui-ux']
                    }
                });
                logger_1.Logger.instance.info(`Retrieved ${memories.length} design artifacts from memory`);
                return memories;
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to retrieve design artifacts from memory:', error);
                return [];
            }
        },
        async storeUserResearch(researchData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(researchData),
                    metadata: {
                        source: 'user',
                        type: 'insight',
                        timestamp: new Date().toISOString(),
                        tags: ['ui-ux', 'research', 'user']
                    }
                });
                logger_1.Logger.instance.info('Saved user research to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save user research to memory:', error);
            }
        }
    };
    // Create edges
    const edges = [
        { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },
        { name: 'requirements-to-research', source: 'requirements-analysis', target: 'user-research', type: 'default' },
        { name: 'research-to-wireframing', source: 'user-research', target: 'wireframing', type: 'default' },
        { name: 'wireframing-to-design-system', source: 'wireframing', target: 'design-system', type: 'default' },
        { name: 'design-system-to-prototype', source: 'design-system', target: 'prototype', type: 'default' },
        { name: 'prototype-to-testing', source: 'prototype', target: 'usability-testing', type: 'default' },
        { name: 'testing-to-implementation', source: 'usability-testing', target: 'implementation', type: 'default' },
        { name: 'implementation-to-output', source: 'implementation', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'testing-to-wireframing', source: 'usability-testing', target: 'wireframing', type: 'feedback' },
        { name: 'implementation-to-testing', source: 'implementation', target: 'usability-testing', type: 'feedback' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect wireframing to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `wireframing-to-tool-${index}`,
                source: 'wireframing',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to design system
            edges.push({
                name: `tool-${index}-to-design-system`,
                source: `tool-${index}`,
                target: 'design-system',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            requirementsAnalysisNode,
            userResearchNode,
            wireframingNode,
            designSystemNode,
            prototypeNode,
            usabilityTestingNode,
            implementationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'ui-ux',
        tags: ['ui', 'ux', 'design', 'usability'],
        metadata: {
            memoryManager: uiUxMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a specialized UI component design workflow
 */
function createUIComponentWorkflow(id, name, description, designerAgent, developerAgent, tools = []) {
    logger_1.Logger.instance.info(`Creating UI Component workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const componentSpecificationNode = graph_1.Codessa.createAgentNode('component-specification', 'Component Specification', designerAgent);
    const designExplorationNode = graph_1.Codessa.createAgentNode('design-exploration', 'Design Exploration', designerAgent);
    const accessibilityReviewNode = graph_1.Codessa.createAgentNode('accessibility-review', 'Accessibility Review', designerAgent);
    const componentImplementationNode = graph_1.Codessa.createAgentNode('component-implementation', 'Component Implementation', developerAgent);
    const unitTestingNode = graph_1.Codessa.createAgentNode('unit-testing', 'Unit Testing', developerAgent);
    const documentationNode = graph_1.Codessa.createAgentNode('documentation', 'Documentation', developerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create a memory manager for the UI Component workflow
    const uiComponentMemoryManager = {
        async storeComponentDesign(designData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(designData),
                    metadata: {
                        source: 'system',
                        type: 'code',
                        timestamp: new Date().toISOString(),
                        tags: ['ui', 'component', 'design']
                    }
                });
                logger_1.Logger.instance.info('Saved component design to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save component design to memory:', error);
            }
        },
        async retrieveComponentDesigns(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5,
                    filter: {
                        tags: ['component']
                    }
                });
                logger_1.Logger.instance.info(`Retrieved ${memories.length} component designs from memory`);
                return memories;
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to retrieve component designs from memory:', error);
                return [];
            }
        },
        async storeAccessibilityGuidelines(guidelinesData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(guidelinesData),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['ui', 'component', 'accessibility']
                    }
                });
                logger_1.Logger.instance.info('Saved accessibility guidelines to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save accessibility guidelines to memory:', error);
            }
        }
    };
    // Create edges
    const edges = [
        { name: 'input-to-specification', source: 'input', target: 'component-specification', type: 'default' },
        { name: 'specification-to-exploration', source: 'component-specification', target: 'design-exploration', type: 'default' },
        { name: 'exploration-to-accessibility', source: 'design-exploration', target: 'accessibility-review', type: 'default' },
        { name: 'accessibility-to-implementation', source: 'accessibility-review', target: 'component-implementation', type: 'default' },
        { name: 'implementation-to-testing', source: 'component-implementation', target: 'unit-testing', type: 'default' },
        { name: 'testing-to-documentation', source: 'unit-testing', target: 'documentation', type: 'default' },
        { name: 'documentation-to-output', source: 'documentation', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'testing-to-implementation', source: 'unit-testing', target: 'component-implementation', type: 'feedback' },
        { name: 'implementation-to-accessibility', source: 'component-implementation', target: 'accessibility-review', type: 'feedback' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect design exploration to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `exploration-to-tool-${index}`,
                source: 'design-exploration',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to accessibility review
            edges.push({
                name: `tool-${index}-to-accessibility`,
                source: `tool-${index}`,
                target: 'accessibility-review',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            componentSpecificationNode,
            designExplorationNode,
            accessibilityReviewNode,
            componentImplementationNode,
            unitTestingNode,
            documentationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'ui-ux',
        tags: ['ui', 'component', 'design', 'accessibility'],
        metadata: {
            memoryManager: uiComponentMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=uiUxWorkflow.js.map