{"version": 3, "file": "generalSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/generalSettingsSection.ts"], "names": [], "mappings": ";AAAA,sCAAsC;;AAkBtC,oEA8QC;AAnRD;;;;GAIG;AACH,SAAgB,4BAA4B,CAAC,SAAsB,EAAE,WAA4B,EAAE;IACjG,iCAAiC;IACjC,MAAM,EACJ,KAAK,GAAG,QAAQ,EAChB,QAAQ,GAAG,IAAI,EACf,QAAQ,GAAG,IAAI,EACf,kBAAkB,GAAG,KAAK,EAC1B,QAAQ,GAAG,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE;QACzC,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,cAAc,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC,EACD,MAAM,GAAG,GAAS,EAAE;QAClB,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAChC,CAAC,EACD,OAAO,GAAG,GAAS,EAAE;QACnB,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAChC,CAAC,EACF,GAAG,QAAQ,CAAC;IAEb,SAAS,CAAC,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6DnB,CAAC;IAEJ,iDAAiD;IACjD,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAoB,uBAAuB,CAAC,CAAC;IACxF,IAAI,WAAW,EAAE,CAAC;QAChB,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,MAAM,cAAc,GAAG,SAAS,CAAC,aAAa,CAAoB,0BAA0B,CAAC,CAAC;IAC9F,IAAI,cAAc,EAAE,CAAC;QACnB,cAAc,CAAC,KAAK,GAAG,QAAQ,CAAC;IAClC,CAAC;IAED,MAAM,gBAAgB,GAAG,SAAS,CAAC,aAAa,CAAmB,4BAA4B,CAAC,CAAC;IACjG,IAAI,gBAAgB,EAAE,CAAC;QACrB,gBAAgB,CAAC,OAAO,GAAG,QAAQ,CAAC;IACtC,CAAC;IAED,MAAM,mBAAmB,GAAG,SAAS,CAAC,aAAa,CAAmB,+BAA+B,CAAC,CAAC;IACvG,IAAI,mBAAmB,EAAE,CAAC;QACxB,mBAAmB,CAAC,OAAO,GAAG,kBAAkB,CAAC;IACnD,CAAC;IAED,qCAAqC;IACrC,IAAI,WAAW,EAAE,CAAC;QAChB,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAQ,EAAE,EAAE;YAClD,MAAM,MAAM,GAAG,CAAC,CAAC,MAA2B,CAAC;YAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAsD,CAAC;YAE/E,kDAAkD;YAClD,MAAM,MAAM,GAAI,MAAmF,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACzH,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,WAAW,CAAC;oBACjB,OAAO,EAAE,eAAe;oBACxB,OAAO,EAAE,SAAS;oBAClB,GAAG,EAAE,OAAO;oBACZ,KAAK,EAAE,QAAQ;iBAChB,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC5B,UAAU,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,cAAc,EAAE,CAAC;QACnB,cAAc,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAQ,EAAE,EAAE;YACrD,MAAM,MAAM,GAAG,CAAC,CAAC,MAA2B,CAAC;YAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;YAE9B,kDAAkD;YAClD,MAAM,MAAM,GAAI,MAAmF,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACzH,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,WAAW,CAAC;oBACjB,OAAO,EAAE,eAAe;oBACxB,OAAO,EAAE,SAAS;oBAClB,GAAG,EAAE,UAAU;oBACf,KAAK,EAAE,QAAQ;iBAChB,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC/B,UAAU,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,gBAAgB,EAAE,CAAC;QACrB,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAQ,EAAE,EAAE;YACvD,MAAM,MAAM,GAAG,CAAC,CAAC,MAA0B,CAAC;YAC5C,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC;YAEjC,kDAAkD;YAClD,MAAM,MAAM,GAAI,MAAmF,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACzH,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,WAAW,CAAC;oBACjB,OAAO,EAAE,eAAe;oBACxB,OAAO,EAAE,SAAS;oBAClB,GAAG,EAAE,UAAU;oBACf,KAAK,EAAE,SAAS;iBACjB,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAChC,UAAU,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,mBAAmB,EAAE,CAAC;QACxB,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAQ,EAAE,EAAE;YAC1D,MAAM,MAAM,GAAG,CAAC,CAAC,MAA0B,CAAC;YAC5C,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC;YAEjC,kDAAkD;YAClD,MAAM,MAAM,GAAI,MAAmF,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACzH,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,WAAW,CAAC;oBACjB,OAAO,EAAE,eAAe;oBACxB,OAAO,EAAE,QAAQ;oBACjB,GAAG,EAAE,oBAAoB;oBACzB,KAAK,EAAE,SAAS;iBACjB,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAC;YACjD,UAAU,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iCAAiC;IACjC,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAoB,mBAAmB,CAAC,CAAC;IAChF,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACrC,MAAM,EAAE,CAAC;YACT,UAAU,CAAC,8BAA8B,EAAE,SAAS,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kCAAkC;IAClC,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,CAAoB,oBAAoB,CAAC,CAAC;IAClF,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACtC,IAAI,OAAO,CAAC,sEAAsE,CAAC,EAAE,CAAC;gBACpF,OAAO,EAAE,CAAC;gBACV,UAAU,CAAC,sCAAsC,EAAE,MAAM,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,SAAS,UAAU,CAAC,OAAe,EAAE,OAAqC,MAAM;QAC9E,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAc,iBAAiB,CAAC,CAAC;QAC9E,IAAI,CAAC,aAAa;YAAE,OAAO;QAE3B,sBAAsB;QACtB,aAAa,CAAC,WAAW,GAAG,OAAO,CAAC;QAEpC,qCAAqC;QACrC,MAAM,MAAM,GAAiC;YAC3C,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,QAAQ;YACjB,YAAY,EAAE,KAAK;YACnB,SAAS,EAAE,KAAK;YAChB,eAAe,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBAChC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;YACzD,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBAC/B,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;YAChD,UAAU,EAAE,0BAA0B;SACvC,CAAC;QAEF,eAAe;QACf,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAE3C,4BAA4B;QAC5B,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE;YAClC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;YAClC,UAAU,CAAC,GAAG,EAAE;gBACd,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBACrC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;YACpC,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,0CAA0C;QAC1C,aAAa,CAAC,gBAAgB,CAAC,YAAY,EAAE,GAAG,EAAE;YAChD,YAAY,CAAC,WAAW,CAAC,CAAC;YAC1B,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,gBAAgB,CAAC,YAAY,EAAE,GAAG,EAAE;YAChD,UAAU,CAAC,GAAG,EAAE;gBACd,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBAClC,UAAU,CAAC,GAAG,EAAE;oBACd,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;oBACrC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBACpC,CAAC,EAAE,GAAG,CAAC,CAAC;YACV,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8BAA8B;IAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9C,KAAK,CAAC,WAAW,GAAG;;;;;KAKjB,CAAC;IACJ,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["// General section logic and rendering\n\ninterface GeneralSettings {\n    theme?: 'system' | 'light' | 'dark' | 'high-contrast';\n    language?: string;\n    autoSave?: boolean;\n    perAgentLLMEnabled?: boolean;\n    onChange?: (key: string, value: unknown) => void;\n    onSave?: () => void;\n    onReset?: () => void;\n    [key: string]: unknown; // Allow additional properties\n}\n\n/**\n * Renders the General settings section in the settings UI.\n * @param container - The HTMLElement where the section should be rendered\n * @param settings - An object containing the current settings values and callbacks\n */\nexport function renderGeneralSettingsSection(container: HTMLElement, settings: GeneralSettings = {}): void {\n  // Default values if not provided\n  const {\n    theme = 'system',\n    language = 'en',\n    autoSave = true,\n    perAgentLLMEnabled = false,\n    onChange = (key: string, value: unknown) => { \n      console.log(`Setting ${key} changed to:`, value); \n    },\n    onSave = (): void => { \n      console.log('Settings saved'); \n    },\n    onReset = (): void => { \n      console.log('Settings reset'); \n    }\n  } = settings;\n\n  container.innerHTML = `\n        <form class=\"general-settings-form\" autocomplete=\"off\" style=\"max-width: 420px; margin: 0 auto;\">\n            <h2 style=\"margin-bottom: 1em;\">General Settings</h2>\n            \n            <div class=\"form-group\" style=\"margin-bottom: 1em;\">\n                <label style=\"font-weight:500;\">\n                    <input type=\"checkbox\" id=\"general-peragentllm-checkbox\" style=\"margin-right:0.5em;\">\n                    Allow each agent to use its own LLM provider/model\n                </label>\n                <div class=\"setting-description\" style=\"font-size: 0.9em; color: #666; margin-top: 0.25em;\">\n                    When enabled, each agent can be configured with its own AI provider and model\n                </div>\n            </div>\n\n            <div class=\"form-group\" style=\"margin-bottom: 1em;\">\n                <label for=\"general-theme-select\" style=\"display:block; font-weight:500;\">Theme</label>\n                <select id=\"general-theme-select\" class=\"input\" style=\"width:100%; padding:0.5em;\">\n                    <option value=\"system\">System Default</option>\n                    <option value=\"light\">Light</option>\n                    <option value=\"dark\">Dark</option>\n                </select>\n                <div class=\"setting-description\" style=\"font-size: 0.9em; color: #666; margin-top: 0.25em;\">\n                    Choose the visual theme for the extension interface\n                </div>\n            </div>\n\n            <div class=\"form-group\" style=\"margin-bottom: 1em;\">\n                <label for=\"general-language-select\" style=\"display:block; font-weight:500;\">Language</label>\n                <select id=\"general-language-select\" class=\"input\" style=\"width:100%; padding:0.5em;\">\n                    <option value=\"en\">English</option>\n                    <option value=\"de\">German</option>\n                    <option value=\"fr\">French</option>\n                    <option value=\"es\">Spanish</option>\n                    <option value=\"zh\">Chinese</option>\n                </select>\n                <div class=\"setting-description\" style=\"font-size: 0.9em; color: #666; margin-top: 0.25em;\">\n                    Select the language for the extension interface\n                </div>\n            </div>\n\n            <div class=\"form-group\" style=\"margin-bottom: 1em;\">\n                <label style=\"font-weight:500;\">\n                    <input type=\"checkbox\" id=\"general-autosave-checkbox\" style=\"margin-right:0.5em;\">\n                    Enable Auto-Save\n                </label>\n                <div class=\"setting-description\" style=\"font-size: 0.9em; color: #666; margin-top: 0.25em;\">\n                    Automatically save settings changes without requiring manual save\n                </div>\n            </div>\n\n            <div class=\"form-actions\" style=\"margin-top: 2em; display: flex; gap: 1em; justify-content: flex-end;\">\n                <button type=\"button\" id=\"general-reset-btn\" class=\"btn btn-secondary\" style=\"padding: 0.5em 1em;\">\n                    Reset to Defaults\n                </button>\n                <button type=\"button\" id=\"general-save-btn\" class=\"btn btn-primary\" style=\"padding: 0.5em 1em;\">\n                    Save Changes\n                </button>\n            </div>\n\n            <div id=\"general-status\" style=\"margin-top: 1em; padding: 0.5em; border-radius: 4px; display: none;\"></div>\n        </form>\n    `;\n\n  // Set initial values with proper type assertions\n  const themeSelect = container.querySelector<HTMLSelectElement>('#general-theme-select');\n  if (themeSelect) {\n    themeSelect.value = theme;\n  }\n\n  const languageSelect = container.querySelector<HTMLSelectElement>('#general-language-select');\n  if (languageSelect) {\n    languageSelect.value = language;\n  }\n\n  const autoSaveCheckbox = container.querySelector<HTMLInputElement>('#general-autosave-checkbox');\n  if (autoSaveCheckbox) {\n    autoSaveCheckbox.checked = autoSave;\n  }\n\n  const perAgentLLMCheckbox = container.querySelector<HTMLInputElement>('#general-peragentllm-checkbox');\n  if (perAgentLLMCheckbox) {\n    perAgentLLMCheckbox.checked = perAgentLLMEnabled;\n  }\n\n  // Event listeners with proper typing\n  if (themeSelect) {\n    themeSelect.addEventListener('change', (e: Event) => {\n      const target = e.target as HTMLSelectElement;\n      const newValue = target.value as 'system' | 'light' | 'dark' | 'high-contrast';\n      \n      // Send updateSetting message to VS Code extension\n      const vscode = (window as { acquireVsCodeApi?: () => { postMessage: (message: unknown) => void } }).acquireVsCodeApi?.();\n      if (vscode) {\n        vscode.postMessage({\n          command: 'updateSetting',\n          section: 'general',\n          key: 'theme',\n          value: newValue\n        });\n      }\n      \n      onChange('theme', newValue);\n      showStatus('Theme updated', 'success');\n    });\n  }\n\n  if (languageSelect) {\n    languageSelect.addEventListener('change', (e: Event) => {\n      const target = e.target as HTMLSelectElement;\n      const newValue = target.value;\n      \n      // Send updateSetting message to VS Code extension\n      const vscode = (window as { acquireVsCodeApi?: () => { postMessage: (message: unknown) => void } }).acquireVsCodeApi?.();\n      if (vscode) {\n        vscode.postMessage({\n          command: 'updateSetting',\n          section: 'general',\n          key: 'language',\n          value: newValue\n        });\n      }\n      \n      onChange('language', newValue);\n      showStatus('Language updated', 'success');\n    });\n  }\n\n  if (autoSaveCheckbox) {\n    autoSaveCheckbox.addEventListener('change', (e: Event) => {\n      const target = e.target as HTMLInputElement;\n      const isChecked = target.checked;\n      \n      // Send updateSetting message to VS Code extension\n      const vscode = (window as { acquireVsCodeApi?: () => { postMessage: (message: unknown) => void } }).acquireVsCodeApi?.();\n      if (vscode) {\n        vscode.postMessage({\n          command: 'updateSetting',\n          section: 'general',\n          key: 'autoSave',\n          value: isChecked\n        });\n      }\n      \n      onChange('autoSave', isChecked);\n      showStatus('Auto-save setting updated', 'success');\n    });\n  }\n\n  if (perAgentLLMCheckbox) {\n    perAgentLLMCheckbox.addEventListener('change', (e: Event) => {\n      const target = e.target as HTMLInputElement;\n      const isChecked = target.checked;\n      \n      // Send updateSetting message to VS Code extension\n      const vscode = (window as { acquireVsCodeApi?: () => { postMessage: (message: unknown) => void } }).acquireVsCodeApi?.();\n      if (vscode) {\n        vscode.postMessage({\n          command: 'updateSetting',\n          section: 'agents',\n          key: 'perAgentLLMEnabled',\n          value: isChecked\n        });\n      }\n      \n      onChange('agents.perAgentLLMEnabled', isChecked);\n      showStatus('Per-agent LLM setting updated', 'success');\n    });\n  }\n\n  // Save button with proper typing\n  const saveBtn = container.querySelector<HTMLButtonElement>('#general-save-btn');\n  if (saveBtn) {\n    saveBtn.addEventListener('click', () => {\n      onSave();\n      showStatus('Settings saved successfully!', 'success');\n    });\n  }\n\n  // Reset button with proper typing\n  const resetBtn = container.querySelector<HTMLButtonElement>('#general-reset-btn');\n  if (resetBtn) {\n    resetBtn.addEventListener('click', () => {\n      if (confirm('Are you sure you want to reset all settings to their default values?')) {\n        onReset();\n        showStatus('Settings have been reset to defaults', 'info');\n      }\n    });\n  }\n\n  /**\n   * Shows a status message in the UI\n   * @param message - The message to display\n   * @param type - The type of message (success, error, or info)\n   */\n  function showStatus(message: string, type: 'success' | 'error' | 'info' = 'info'): void {\n    const statusElement = container.querySelector<HTMLElement>('#general-status');\n    if (!statusElement) return;\n    \n    // Set message content\n    statusElement.textContent = message;\n    \n    // Apply styles based on message type\n    const styles: Partial<CSSStyleDeclaration> = {\n      display: 'block',\n      padding: '0.75em',\n      borderRadius: '4px',\n      marginTop: '1em',\n      backgroundColor: type === 'error' ? '#ffebee' : \n                     type === 'success' ? '#e8f5e9' : '#e3f2fd',\n      color: type === 'error' ? '#c62828' : \n            type === 'success' ? '#2e7d32' : '#1565c0',\n      transition: 'opacity 0.3s ease-in-out'\n    };\n    \n    // Apply styles\n    Object.assign(statusElement.style, styles);\n    \n    // Auto-hide after 5 seconds\n    const hideTimeout = setTimeout(() => {\n      statusElement.style.opacity = '0';\n      setTimeout(() => {\n        statusElement.style.display = 'none';\n        statusElement.style.opacity = '1';\n      }, 300);\n    }, 5000);\n    \n    // Clear timeout if the element is hovered\n    statusElement.addEventListener('mouseenter', () => {\n      clearTimeout(hideTimeout);\n      statusElement.style.opacity = '1';\n    });\n    \n    statusElement.addEventListener('mouseleave', () => {\n      setTimeout(() => {\n        statusElement.style.opacity = '0';\n        setTimeout(() => {\n          statusElement.style.display = 'none';\n          statusElement.style.opacity = '1';\n        }, 300);\n      }, 1000);\n    });\n  }\n\n  // Add CSS for status messages\n  const style = document.createElement('style');\n  style.textContent = `\n        .status-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }\n        .status-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }\n        .status-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }\n        .status-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }\n    `;\n  container.appendChild(style);\n}\n"]}