{"version": 3, "file": "registerModes.js", "sourceRoot": "", "sources": ["../../src/commands/registerModes.ts"], "names": [], "mappings": ";;AAgBA,sCAgFC;AA/FD,sEAAwF;AACxF,0DAAuD;AACvD,4DAAyD;AACzD,8DAA2D;AAC3D,4DAAyD;AACzD,8DAA2D;AAC3D,wEAAqE;AACrE,oEAAiE;AACjE,8EAA2E;AAC3E,oEAAiE;AACjE,sCAAmC;AAEnC;;GAEG;AACI,KAAK,UAAU,aAAa,CAAC,OAAgC;IAClE,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAE9C,iBAAiB;QACjB,qCAAqB,CAAC,YAAY,CAAC,IAAI,iBAAO,CAC5C,KAAK,EACL,KAAK,EACL,mCAAmC,EACnC,aAAa,EACb,2BAAW,CAAC,eAAe,CAC5B,CAAC,CAAC;QACH,qCAAqB,CAAC,YAAY,CAAC,IAAI,mBAAQ,CAC7C,MAAM,EACN,MAAM,EACN,oCAAoC,EACpC,YAAY,EACZ,2BAAW,CAAC,IAAI,CACjB,CAAC,CAAC;QACH,qCAAqB,CAAC,YAAY,CAAC,IAAI,qBAAS,CAC9C,OAAO,EACP,OAAO,EACP,6BAA6B,EAC7B,QAAQ,EACR,2BAAW,CAAC,cAAc,CAC3B,CAAC,CAAC;QACH,qCAAqB,CAAC,YAAY,CAAC,IAAI,mBAAQ,CAC7C,MAAM,EACN,MAAM,EACN,kDAAkD,EAClD,SAAS,EACT,2BAAW,CAAC,cAAc,CAC3B,CAAC,CAAC;QACH,qCAAqB,CAAC,YAAY,CAAC,IAAI,qBAAS,CAC9C,OAAO,EACP,OAAO,EACP,wEAAwE,EACxE,UAAU,EACV,2BAAW,CAAC,eAAe,CAC5B,CAAC,CAAC;QACH,qCAAqB,CAAC,YAAY,CAAC,IAAI,+BAAc,CACnD,aAAa,EACb,aAAa,EACb,qDAAqD,EACrD,iBAAiB,EACjB,2BAAW,CAAC,eAAe,CAC5B,CAAC,CAAC;QACH,qCAAqB,CAAC,YAAY,CAAC,IAAI,2BAAY,CACjD,UAAU,EACV,UAAU,EACV,+CAA+C,EAC/C,WAAW,EACX,2BAAW,CAAC,eAAe,CAC5B,CAAC,CAAC;QACH,qCAAqB,CAAC,YAAY,CAAC,IAAI,qCAAiB,CACtD,eAAe,EACf,eAAe,EACf,+CAA+C,EAC/C,SAAS,EACT,2BAAW,CAAC,cAAc,CAC3B,CAAC,CAAC;QACH,qCAAqB,CAAC,YAAY,CAAC,IAAI,2BAAY,CACjD,UAAU,EACV,UAAU,EACV,sCAAsC,EACtC,UAAU,EACV,2BAAW,CAAC,cAAc,CAC3B,CAAC,CAAC;QAEH,mBAAmB;QACnB,qCAAqB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE7C,mBAAmB;QACnB,MAAM,qCAAqB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAErD,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;IACzD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC1D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { operationModeRegistry, ContextType } from '../agents/agentModes/operationMode';\nimport { AskMode } from '../agents/agentModes/askMode';\nimport { ChatMode } from '../agents/agentModes/chatMode';\nimport { DebugMode } from '../agents/agentModes/debugMode';\nimport { EditMode } from '../agents/agentModes/editMode';\nimport { AgentMode } from '../agents/agentModes/agentMode';\nimport { MultiAgentMode } from '../agents/agentModes/multiAgentMode';\nimport { ResearchMode } from '../agents/agentModes/researchMode';\nimport { DocumentationMode } from '../agents/agentModes/documentationMode';\nimport { RefactorMode } from '../agents/agentModes/refactorMode';\nimport { logger } from '../logger';\n\n/**\n * Register all operation modes\n */\nexport async function registerModes(context: vscode.ExtensionContext): Promise<void> {\n  try {\n    logger.info('Registering operation modes...');\n\n    // Register modes\n    operationModeRegistry.registerMode(new AskMode(\n      'ask',\n      'Ask',\n      'Ask questions about your codebase',\n      '$(question)',\n      ContextType.ENTIRE_CODEBASE\n    ));\n    operationModeRegistry.registerMode(new ChatMode(\n      'chat',\n      'Chat',\n      'General chat with the AI assistant',\n      '$(comment)',\n      ContextType.NONE\n    ));\n    operationModeRegistry.registerMode(new DebugMode(\n      'debug',\n      'Debug',\n      'Debug issues with your code',\n      '$(bug)',\n      ContextType.SELECTED_FILES\n    ));\n    operationModeRegistry.registerMode(new EditMode(\n      'edit',\n      'Edit',\n      'AI-assisted code editing with human verification',\n      '$(edit)',\n      ContextType.SELECTED_FILES\n    ));\n    operationModeRegistry.registerMode(new AgentMode(\n      'agent',\n      'Agent',\n      'Autonomous AI agent that completes tasks with minimal user interaction',\n      '$(robot)',\n      ContextType.ENTIRE_CODEBASE\n    ));\n    operationModeRegistry.registerMode(new MultiAgentMode(\n      'multi-agent',\n      'Multi-Agent',\n      'Team of AI agents working together on complex tasks',\n      '$(organization)',\n      ContextType.ENTIRE_CODEBASE\n    ));\n    operationModeRegistry.registerMode(new ResearchMode(\n      'research',\n      'Research',\n      'Comprehensive research and analysis of topics',\n      '$(search)',\n      ContextType.ENTIRE_CODEBASE\n    ));\n    operationModeRegistry.registerMode(new DocumentationMode(\n      'documentation',\n      'Documentation',\n      'Generate comprehensive documentation for code',\n      '$(book)',\n      ContextType.SELECTED_FILES\n    ));\n    operationModeRegistry.registerMode(new RefactorMode(\n      'refactor',\n      'Refactor',\n      'Refactor and optimize code structure',\n      '$(tools)',\n      ContextType.SELECTED_FILES\n    ));\n\n    // Set default mode\n    operationModeRegistry.setDefaultMode('chat');\n\n    // Initialize modes\n    await operationModeRegistry.initializeModes(context);\n\n    logger.info('Operation modes registered successfully');\n  } catch (error) {\n    logger.error('Error registering operation modes:', error);\n    throw error;\n  }\n}\n"]}