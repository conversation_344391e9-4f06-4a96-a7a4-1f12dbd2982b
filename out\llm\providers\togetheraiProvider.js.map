{"version": 3, "file": "togetheraiProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/togetheraiProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAGpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;GAEG;AACH,MAAa,kBAAmB,SAAQ,iCAAe;IAC5C,UAAU,GAAG,YAAY,CAAC;IAC1B,WAAW,GAAG,aAAa,CAAC;IAC5B,WAAW,GAAG,yDAAyD,CAAC;IACxE,OAAO,GAAG,0BAA0B,CAAC;IACrC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,6BAA6B,CAAC;IAChD,YAAY,GAAG,sCAAsC,CAAC;IAEvD,MAAM,GAAQ,IAAI,CAAC;IACnB,eAAe,GAAmB,EAAE,CAAC;IAE7C,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;gBACnF,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAClE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,MAAM,EAAE,oBAAoB;gBACrC,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,MAAM,EAAE;oBACnC,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACzE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,wDAAwD,EAAE,CAAC;QAC1F,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,uCAAuC;YACvC,MAAM,QAAQ,GAA8E,EAAE,CAAC;YAE/F,iCAAiC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,MAAM,CAAC,YAAY;iBACpB,CAAC,CAAC;YACd,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;iBACd,CAAC,CAAC;YACd,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;wBAC9D,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBAChH,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACpC,IAAI,EAAE,MAAM,CAAC,aAAa;aAC3B,EAAE;gBACD,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,qBAAqB;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;gBAChD,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,IAAI,MAAM;gBACvD,KAAK,EAAE,MAAM,CAAC,KAAK;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAC5D,IAAI,YAAY,GAAG,iCAAiC,CAAC;YAErD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,0BAA0B,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACpH,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,gDAAgD;YAChD,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,CAAC;YAED,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAElD,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;YAED,kDAAkD;YAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI;iBAC9B,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBAC3D,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;gBACpB,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,EAAE;gBACpC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;gBACpC,aAAa,EAAE,KAAK,CAAC,cAAc,IAAI,IAAI;gBAC3C,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,KAAK,CAAC,OAAO,CAAC,KAAK,wBAAwB,KAAK,CAAC,OAAO,CAAC,MAAM,YAAY,CAAC,CAAC,CAAC,MAAM;aAC7H,CAAC,CAAC,CAAC;YAEN,mBAAmB;YACnB,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;YAE9B,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAEnE,0CAA0C;YAC1C,OAAO;gBACL;oBACE,EAAE,EAAE,sCAAsC;oBAC1C,IAAI,EAAE,uBAAuB;oBAC7B,WAAW,EAAE,2CAA2C;oBACxD,aAAa,EAAE,KAAK;oBACpB,WAAW,EAAE,MAAM;iBACpB;gBACD;oBACE,EAAE,EAAE,6BAA6B;oBACjC,IAAI,EAAE,kBAAkB;oBACxB,WAAW,EAAE,qCAAqC;oBAClD,aAAa,EAAE,IAAI;oBACnB,WAAW,EAAE,MAAM;iBACpB;gBACD;oBACE,EAAE,EAAE,4BAA4B;oBAChC,IAAI,EAAE,iBAAiB;oBACvB,WAAW,EAAE,+BAA+B;oBAC5C,aAAa,EAAE,IAAI;oBACnB,WAAW,EAAE,MAAM;iBACpB;gBACD;oBACE,EAAE,EAAE,oCAAoC;oBACxC,IAAI,EAAE,qBAAqB;oBAC3B,WAAW,EAAE,mCAAmC;oBAChD,aAAa,EAAE,IAAI;oBACnB,WAAW,EAAE,MAAM;iBACpB;gBACD;oBACE,EAAE,EAAE,kCAAkC;oBACtC,IAAI,EAAE,wBAAwB;oBAC9B,WAAW,EAAE,iCAAiC;oBAC9C,aAAa,EAAE,KAAK;oBACpB,WAAW,EAAE,MAAM;iBACpB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gEAAgE;aAC1E,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBAC9C,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,+DAA+D,OAAO,IAAI;iBACpF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uDAAuD;iBACjE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YACpE,IAAI,YAAY,GAAG,sCAAsC,CAAC;YAE1D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,4EAA4E;gBACzF,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,qEAAqE;gBAClF,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,sCAAsC;gBACnD,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AApTD,gDAoTC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { BaseLLMProvider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { Logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for Together AI models\n */\nexport class TogetherAIProvider extends BaseLLMProvider {\n  readonly providerId = 'togetherai';\n  readonly displayName = 'Together AI';\n  readonly description = 'Access to a wide range of open and closed source models';\n  readonly website = 'https://www.together.ai/';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api.together.xyz/v1';\n  readonly defaultModel = 'mistralai/Mixtral-8x7B-Instruct-v0.1';\n\n  private client: any = null;\n  private availableModels: LLMModelInfo[] = [];\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        Logger.instance.info('Together AI configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (!apiKey) {\n      Logger.instance.warn('API key not set for Together AI provider.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 120000, // 2 minutes timeout\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      Logger.instance.info('Together AI client initialized successfully.');\n    } catch (error) {\n      Logger.instance.error('Failed to initialize Together AI client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using Together AI models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'Together AI provider not configured (API key missing?)' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n            \n      // Prepare messages for chat completion\n      const messages: { role: string; content: string; name?: string; tool_call_id?: string }[] = [];\n            \n      // Add system message if provided\n      if (params.systemPrompt) {\n        messages.push({\n          role: 'system',\n          content: params.systemPrompt\n        } as const);\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        messages.push(...params.history);\n      } else {\n        // Just add the user prompt\n        messages.push({\n          role: 'user',\n          content: params.prompt\n        } as const);\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            Logger.instance.info('Together AI request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          Logger.instance.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request\n      const response = await this.client.post('/chat/completions', {\n        model: modelId,\n        messages: messages,\n        temperature: params.temperature ?? 0.7,\n        max_tokens: params.maxTokens ?? 1024,\n        stop: params.stopSequences\n      }, {\n        signal: abortController?.signal\n      });\n\n      // Parse the response\n      const result = response.data;\n            \n      return {\n        content: result.choices[0].message.content || '',\n        finishReason: result.choices[0].finish_reason || 'stop',\n        usage: result.usage\n      };\n    } catch (error: any) {\n      Logger.instance.error('Together AI generate error:', error);\n      let errorMessage = 'Failed to call Together AI API.';\n\n      if (error.response) {\n        errorMessage = `Together AI API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available Together AI models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    if (!this.client) {\n      return [];\n    }\n\n    try {\n      // If we already have models cached, return them\n      if (this.availableModels.length > 0) {\n        return this.availableModels;\n      }\n\n      // Fetch models from the API\n      const response = await this.client.get('/models');\n            \n      if (!response.data || !response.data.data) {\n        throw new Error('Invalid response from Together AI API');\n      }\n            \n      // Filter for models that support chat completions\n      const models = response.data.data\n        .filter((model: any) => model.capabilities.includes('chat'))\n        .map((model: any) => ({\n          id: model.id,\n          name: model.display_name || model.id,\n          description: model.description || '',\n          contextWindow: model.context_length || 4096,\n          pricingInfo: model.pricing ? `Input: $${model.pricing.input}/1M tokens, Output: $${model.pricing.output}/1M tokens` : 'Paid'\n        }));\n            \n      // Cache the models\n      this.availableModels = models;\n            \n      return models;\n    } catch (error) {\n      Logger.instance.error('Failed to list Together AI models:', error);\n            \n      // Return a default list of popular models\n      return [\n        {\n          id: 'mistralai/Mixtral-8x7B-Instruct-v0.1',\n          name: 'Mixtral 8x7B Instruct',\n          description: 'Mixtral MoE model with strong performance',\n          contextWindow: 32768,\n          pricingInfo: 'Paid'\n        },\n        {\n          id: 'meta-llama/Llama-3-70b-chat',\n          name: 'Llama 3 70B Chat',\n          description: 'Meta\\'s latest large language model',\n          contextWindow: 8192,\n          pricingInfo: 'Paid'\n        },\n        {\n          id: 'meta-llama/Llama-3-8b-chat',\n          name: 'Llama 3 8B Chat',\n          description: 'Smaller, faster Llama 3 model',\n          contextWindow: 8192,\n          pricingInfo: 'Paid'\n        },\n        {\n          id: 'mistralai/Mistral-7B-Instruct-v0.2',\n          name: 'Mistral 7B Instruct',\n          description: 'Efficient instruction-tuned model',\n          contextWindow: 8192,\n          pricingInfo: 'Paid'\n        },\n        {\n          id: 'codellama/CodeLlama-34b-Instruct',\n          name: 'CodeLlama 34B Instruct',\n          description: 'Specialized for code generation',\n          contextWindow: 16384,\n          pricingInfo: 'Paid'\n        }\n      ];\n    }\n  }\n\n  /**\n     * Test connection to Together AI\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'Together AI client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Simple test request to check if the API is working\n      const response = await this.client.post('/chat/completions', {\n        model: modelId,\n        messages: [{ role: 'user', content: 'Hello' }],\n        max_tokens: 10\n      });\n\n      if (response.data && response.data.choices) {\n        return {\n          success: true,\n          message: `Successfully connected to Together AI API and tested model '${modelId}'.`\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Connected to API but received an unexpected response.'\n        };\n      }\n    } catch (error: any) {\n      Logger.instance.error('Together AI connection test failed:', error);\n      let errorMessage = 'Failed to connect to Together AI API';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your Together AI API key (from https://api.together.xyz/settings/api-keys)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The Together AI API endpoint (default: https://api.together.xyz/v1)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default Together AI model to use',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n"]}