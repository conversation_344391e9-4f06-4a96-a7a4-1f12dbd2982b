{"_type": "UMLClass", "_id": "AAAAAAGH1CodessaGraphMemory=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "CodessaGraphMemory", "stereotype": "", "documentation": "Implements memory workflows using Codessa", "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaGraphMemoryAttr1=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemory="}, "name": "graph", "visibility": "private", "type": "StateGraph | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaGraphMemoryAttr2=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemory="}, "name": "compiledGraph", "visibility": "private", "type": "import('codessa').CompiledGraph | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaGraphMemoryAttr3=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemory="}, "name": "model", "visibility": "private", "type": "BaseChatModel | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaGraphMemoryAttr4=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemory="}, "name": "initialized", "visibility": "private", "type": "boolean", "defaultValue": "false"}], "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaGraphMemoryOp1=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemory="}, "name": "initialize", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaGraphMemoryOp1P1=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemoryOp1="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaGraphMemoryOp2=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemory="}, "name": "createMemoryGraph", "visibility": "private", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaGraphMemoryOp2P1=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemoryOp2="}, "type": "StateGraph", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaGraphMemoryOp3=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemory="}, "name": "compile", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaGraphMemoryOp3P1=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemoryOp3="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaGraphMemoryOp4=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemory="}, "name": "processMessage", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaGraphMemoryOp4P1=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemoryOp4="}, "name": "message", "type": "string | HumanMessage | AIMessage | SystemMessage"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaGraphMemoryOp4P2=", "_parent": {"$ref": "AAAAAAGH1CodessaGraphMemoryOp4="}, "type": "Promise<string>", "direction": "return"}]}], "isAbstract": false}