"use strict";
/**
 * Memory Enhanced Workflow
 * This file provides a workflow that enhances agents with memory capabilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMemoryEnhancedWorkflow = createMemoryEnhancedWorkflow;
const uuid_1 = require("uuid");
const logger_1 = require("../../logger");
const corePolyfill_1 = require("./corePolyfill");
/**
 * Creates a memory-enhanced workflow
 * @param id The workflow ID
 * @param name The workflow name
 * @param description The workflow description
 * @param agent The agent to use
 * @param tools The tools to use
 * @returns A workflow instance
 */
function createMemoryEnhancedWorkflow(id, name, description, agent, tools = []) {
    return {
        id: id || `memory-enhanced-workflow-${(0, uuid_1.v4)()}`,
        name: name || 'Memory Enhanced Workflow',
        description: description || 'A workflow that enhances agents with memory capabilities',
        version: '1.0.0',
        operationMode: 'memory',
        // Define nodes
        nodes: [
            {
                id: 'start',
                type: 'input',
                name: 'Start',
                label: 'Start',
                description: 'Starting point of the workflow'
            },
            {
                id: 'agent',
                type: 'agent',
                name: 'Memory Agent',
                label: 'Memory Agent',
                description: 'Agent with memory capabilities',
                agent: agent,
                execute: async (state) => {
                    try {
                        logger_1.logger.info(`Executing memory-enhanced workflow: ${name}`);
                        // Execute the agent with the input
                        const result = await agent.run({
                            prompt: state.inputs.query,
                            mode: 'chat'
                        }, {
                            tools: new Map(tools.map(tool => [tool.id, tool]))
                        });
                        return {
                            messages: result.output ? [new corePolyfill_1.AIMessage(result.output)] : [],
                            outputs: {
                                result: result,
                                metadata: {
                                    workflowId: id,
                                    workflowName: name,
                                    toolResults: result.toolResults
                                }
                            }
                        };
                    }
                    catch (error) {
                        logger_1.logger.error(`Error executing memory-enhanced workflow: ${error.message}`);
                        throw error;
                    }
                }
            },
            {
                id: 'end',
                type: 'output',
                name: 'End',
                label: 'End',
                description: 'End point of the workflow'
            }
        ],
        // Define edges
        edges: [
            {
                source: 'start',
                target: 'agent',
                type: 'default',
                name: 'Start to Agent'
            },
            {
                source: 'agent',
                target: 'end',
                type: 'success',
                name: 'Agent to End'
            }
        ],
        // Define start node
        startNodeId: 'start'
    };
}
//# sourceMappingURL=memoryEnhancedWorkflow.js.map