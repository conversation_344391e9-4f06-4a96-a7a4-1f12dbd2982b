{"version": 3, "file": "editorActionsTool.js", "sourceRoot": "", "sources": ["../../src/tools/editorActionsTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,2EAA6G;AAC7G,6BAAwB;AAExB,MAAa,iBAAiB;IACnB,EAAE,GAAG,QAAQ,CAAC;IACd,IAAI,GAAG,gBAAgB,CAAC;IACxB,WAAW,GAAG,0HAA0H,CAAC;IACzI,IAAI,GAAG,cAAc,CAAC,CAAC,oBAAoB;IAC3C,OAAO,GAAwB;QACtC,MAAM,EAAE;YACN,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,4BAA4B;YACzC,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;aAC1D,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;iBACtE;gBACD,QAAQ,EAAE,CAAC,UAAU,CAAC;aACvB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;gBAC1C,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC9D,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC1C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;YACrF,CAAC;SACF;QACD,OAAO,EAAE;YACP,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE,0BAA0B;YACvC,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC;YAChC,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,EAAE;aACb;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC9C,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;oBAC3E,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;gBACxF,CAAC;qBAAM,CAAC;oBACN,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,4BAA4B,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;gBAC9F,CAAC;YACH,CAAC;SACF;QACD,MAAM,EAAE;YACN,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,qCAAqC;YAClD,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,iCAAiC,CAAC;gBAChE,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sCAAsC,CAAC;gBACjE,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;aACjF,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,iCAAiC,EAAE;oBAC5E,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sCAAsC,EAAE;oBAC7E,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wCAAwC,EAAE;iBAClF;gBACD,QAAQ,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC;aAC/B;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;gBAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;gBAClC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,IAAI,CAAC,CAAC;gBAC3C,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC9D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAClD,MAAM,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,mBAAmB,QAAQ,IAAI,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;YACxH,CAAC;SACF;QACD,MAAM,EAAE;YACN,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,iCAAiC;YAC9C,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;gBACzD,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gCAAgC,CAAC;gBAC3D,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;oBACd,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;wBACd,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;wBAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACnB,CAAC;oBACF,GAAG,EAAE,OAAC,CAAC,MAAM,CAAC;wBACZ,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;wBAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACnB,CAAC;iBACH,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;aACtD,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;oBACrE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gCAAgC,EAAE;oBACvE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,6BAA6B,EAAE;iBACtE;gBACD,QAAQ,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC;aAC/B;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;gBAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;gBAClC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC1B,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC9D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;oBAC9B,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC;wBACtC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBACxE,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;wBAClE,WAAW,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;oBAC1D,CAAC;yBAAM,CAAC;wBACN,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBACpD,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;YACrF,CAAC;SACF;QACD,QAAQ,EAAE;YACR,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE,wBAAwB;YACrC,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;gBACnE,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;oBACd,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;wBACd,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;wBAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACnB,CAAC;oBACF,GAAG,EAAE,OAAC,CAAC,MAAM,CAAC;wBACZ,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;wBAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACnB,CAAC;iBACH,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC;aAC/B,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oCAAoC,EAAE;oBAC/E,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE;iBAC1D;gBACD,QAAQ,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;aAChC;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;gBAC1C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC1B,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC9D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACxE,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAClE,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBACpD,MAAM,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;gBACjD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,oBAAoB,QAAQ,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjG,CAAC;SACF;QACD,aAAa,EAAE;YACb,GAAG,IAAI,2CAAe,EAAE;YACxB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,kCAAkC,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;YAC1G,CAAC;SACF;QACD,WAAW,EAAE;YACX,GAAG,IAAI,yCAAa,EAAE;YACtB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,+BAA+B,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;YACrG,CAAC;SACF;QACD,WAAW,EAAE;YACX,GAAG,IAAI,yCAAa,EAAE;YACtB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,gCAAgC,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;YACtG,CAAC;SACF;QACD,aAAa,EAAE;YACb,GAAG,IAAI,2CAAe,EAAE;YACxB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,sCAAsC,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;YAC9G,CAAC;SACF;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,4FAA4F;QAC5F,MAAM,QAAQ,GAAG,UAAU,IAAI,KAAK,CAAC,MAAgB,CAAC;QACtD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oDAAoD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC5J,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,QAAQ,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAClK,CAAC;QACD,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;QACjC,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC5B,OAAO,WAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAED,gEAAgE;QAChE,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnC,mEAAmE;YACnE,OAAO,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,6DAA6D;YAC7D,MAAM,MAAM,GAAG,MAAO,UAAkB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACvE,6DAA6D;YAC7D,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACjC,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;YAC/B,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;CACF;AA5OD,8CA4OC;AAEY,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { MultiCursorTool, ClipboardTool, BatchEditTool, FindReplaceTool } from './advancedEditorActionsTool';\nimport { z } from 'zod';\n\nexport class EditorActionsTool implements ITool {\n  readonly id = 'editor';\n  readonly name = 'Editor Actions';\n  readonly description = 'Open, close, navigate, and edit files/tabs; move cursor; select text; multi-cursor; clipboard; batch edit; find/replace.';\n  readonly type = 'multi-action'; // Required by ITool\n  readonly actions: Record<string, any> = {\n    'open': {\n      id: 'open',\n      name: 'Open File',\n      description: 'Open a file in the editor.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        filePath: z.string().describe('Path to the file to open')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          filePath: { type: 'string', description: 'Path to the file to open' }\n        },\n        required: ['filePath']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const filePath = input.filePath as string;\n        const doc = await vscode.workspace.openTextDocument(filePath);\n        await vscode.window.showTextDocument(doc);\n        return { success: true, output: `Opened ${filePath}`, toolId: 'open', actionName };\n      }\n    },\n    'close': {\n      id: 'close',\n      name: 'Close Editor',\n      description: 'Close the active editor.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({}),\n      inputSchema: {\n        type: 'object',\n        properties: {},\n        required: []\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const editor = vscode.window.activeTextEditor;\n        if (editor) {\n          await vscode.commands.executeCommand('workbench.action.closeActiveEditor');\n          return { success: true, output: 'Closed active editor', toolId: 'close', actionName };\n        } else {\n          return { success: false, error: 'No active editor to close.', toolId: 'close', actionName };\n        }\n      }\n    },\n    'goto': {\n      id: 'goto',\n      name: 'Go To Position',\n      description: 'Move cursor to a specific position.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        filePath: z.string().describe('Path to the file to navigate to'),\n        line: z.number().describe('Line number to navigate to (0-based)'),\n        column: z.number().optional().describe('Column number to navigate to (0-based)')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          filePath: { type: 'string', description: 'Path to the file to navigate to' },\n          line: { type: 'number', description: 'Line number to navigate to (0-based)' },\n          column: { type: 'number', description: 'Column number to navigate to (0-based)' }\n        },\n        required: ['filePath', 'line']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const filePath = input.filePath as string;\n        const line = input.line as number;\n        const column = input.column as number || 0;\n        const doc = await vscode.workspace.openTextDocument(filePath);\n        const editor = await vscode.window.showTextDocument(doc);\n        const pos = new vscode.Position(line, column);\n        editor.selection = new vscode.Selection(pos, pos);\n        editor.revealRange(new vscode.Range(pos, pos));\n        return { success: true, output: `Moved cursor to ${filePath}:${line + 1}:${column + 1}`, toolId: 'goto', actionName };\n      }\n    },\n    'edit': {\n      id: 'edit',\n      name: 'Edit File',\n      description: 'Edit file at a range or cursor.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        filePath: z.string().describe('Path to the file to edit'),\n        text: z.string().describe('Text to insert or replace with'),\n        range: z.object({\n          start: z.object({\n            line: z.number(),\n            column: z.number()\n          }),\n          end: z.object({\n            line: z.number(),\n            column: z.number()\n          })\n        }).optional().describe('Range to replace (optional)')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          filePath: { type: 'string', description: 'Path to the file to edit' },\n          text: { type: 'string', description: 'Text to insert or replace with' },\n          range: { type: 'object', description: 'Range to replace (optional)' }\n        },\n        required: ['filePath', 'text']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const filePath = input.filePath as string;\n        const text = input.text as string;\n        const range = input.range;\n        const doc = await vscode.workspace.openTextDocument(filePath);\n        const editor = await vscode.window.showTextDocument(doc);\n        await editor.edit(editBuilder => {\n          if (range && range.start && range.end) {\n            const start = new vscode.Position(range.start.line, range.start.column);\n            const end = new vscode.Position(range.end.line, range.end.column);\n            editBuilder.replace(new vscode.Range(start, end), text);\n          } else {\n            editBuilder.insert(editor.selection.active, text);\n          }\n        });\n        return { success: true, output: `Edited ${filePath}`, toolId: 'edit', actionName };\n      }\n    },\n    'select': {\n      id: 'select',\n      name: 'Select Text',\n      description: 'Select text in a file.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        filePath: z.string().describe('Path to the file to select text in'),\n        range: z.object({\n          start: z.object({\n            line: z.number(),\n            column: z.number()\n          }),\n          end: z.object({\n            line: z.number(),\n            column: z.number()\n          })\n        }).describe('Range to select')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          filePath: { type: 'string', description: 'Path to the file to select text in' },\n          range: { type: 'object', description: 'Range to select' }\n        },\n        required: ['filePath', 'range']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const filePath = input.filePath as string;\n        const range = input.range;\n        const doc = await vscode.workspace.openTextDocument(filePath);\n        const editor = await vscode.window.showTextDocument(doc);\n        const start = new vscode.Position(range.start.line, range.start.column);\n        const end = new vscode.Position(range.end.line, range.end.column);\n        editor.selection = new vscode.Selection(start, end);\n        editor.revealRange(new vscode.Range(start, end));\n        return { success: true, output: `Selected text in ${filePath}`, toolId: 'select', actionName };\n      }\n    },\n    'multiCursor': {\n      ...new MultiCursorTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        return { success: true, output: 'Multi-cursor operation performed', toolId: 'multiCursor', actionName };\n      }\n    },\n    'clipboard': {\n      ...new ClipboardTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        return { success: true, output: 'Clipboard operation performed', toolId: 'clipboard', actionName };\n      }\n    },\n    'batchEdit': {\n      ...new BatchEditTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        return { success: true, output: 'Batch edit operation performed', toolId: 'batchEdit', actionName };\n      }\n    },\n    'findReplace': {\n      ...new FindReplaceTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        return { success: true, output: 'Find and replace operation performed', toolId: 'findReplace', actionName };\n      }\n    },\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // For backward compatibility, check if actionName is undefined and try to get it from input\n    const actionId = actionName || input.action as string;\n    if (!actionId) {\n      return { success: false, error: `Action parameter is required. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };\n    }\n    const actionTool = this.actions[actionId];\n    if (!actionTool) {\n      return { success: false, error: `Unknown editor action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };\n    }\n    const actionInput = { ...input };\n    if ('action' in actionInput) {\n      delete actionInput.action;\n    }\n\n    // Check if the action tool has the new execute method signature\n    if (actionTool.execute.length >= 2) {\n      // Pass the actionId as the actionName parameter to the nested tool\n      return actionTool.execute(actionId, actionInput, context);\n    } else {\n      // Fallback for older tools that don't have the new signature\n      const result = await (actionTool as any).execute(actionInput, context);\n      // Add the actionName to the result if it's not already there\n      if (result && !result.actionName) {\n        result.actionName = actionId;\n      }\n      return result;\n    }\n  }\n}\n\nexport const editorActionsTool = new EditorActionsTool();\n"]}