{"version": 3, "file": "workflowContext.js", "sourceRoot": "", "sources": ["../../src/workflows/workflowContext.ts"], "names": [], "mappings": "", "sourcesContent": ["import { EventEmitter } from 'events';\n\nexport interface WorkflowStepResult {\n  success: boolean;\n  output?: any;\n  error?: Error | string;\n  nextStepId?: string | null;\n  metadata?: Record<string, any>;\n}\n\nexport interface WorkflowHistoryEntry {\n  stepId: string;\n  startTime: Date;\n  endTime: Date;\n  result: WorkflowStepResult;\n  quantumAnalysis?: any;\n  neuralInsights?: any;\n  goddessGuidance?: any;\n}\n\nexport interface WorkflowContext {\n  history: WorkflowHistoryEntry[];\n  data: Record<string, any>;\n  startTime: Date;\n  endTime: Date | null;\n  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';\n  metadata: Record<string, any>;\n  emitter?: EventEmitter;\n  \n  // Add any additional context properties needed by your workflow steps\n  [key: string]: any;\n}\n\nexport interface WorkflowStepOptions {\n  id: string;\n  name: string;\n  description?: string;\n  execute: (context: WorkflowContext) => Promise<WorkflowStepResult>;\n  retryCount?: number;\n  retryDelay?: number;\n  timeout?: number;\n  parallel?: boolean;\n  condition?: (context: WorkflowContext) => boolean | Promise<boolean>;\n  onSuccess?: (context: WorkflowContext, result: WorkflowStepResult) => void | Promise<void>;\n  onError?: (context: WorkflowContext, error: Error) => void | Promise<void>;\n  onComplete?: (context: WorkflowContext, result: WorkflowStepResult) => void | Promise<void>;\n}\n"]}