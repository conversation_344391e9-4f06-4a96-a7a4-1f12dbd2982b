"use strict";
/**
 * Export all manager interfaces, workflows, and shared utilities for easy access
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCollaborativeCodingWorkflow = exports.createCrossRepoDependencyWorkflow = exports.createMultiRepoWorkflow = exports.createKnowledgeRetrievalWorkflow = exports.createKnowledgeBaseWorkflow = exports.createDesignPatternImplementationWorkflow = exports.createPatternBasedRefactoringWorkflow = exports.createMemoryEnhancedWorkflow = exports.createPDFQAWorkflow = exports.createDocumentQAWorkflow = exports.createLegacyCodeModernizationWorkflow = exports.createTechnicalDebtReductionWorkflow = exports.createAPIDocumentationWorkflow = exports.createDocumentationWorkflow = exports.createUIComponentWorkflow = exports.createUIUXWorkflow = exports.createAcademicResearchWorkflow = exports.createResearchWorkflow = exports.createDevOpsMethodologyWorkflow = exports.createWaterfallWorkflow = exports.createXPWorkflow = exports.createScrumWorkflow = exports.createAgileWorkflow = exports.createMaintenanceWorkflow = exports.createDeploymentWorkflow = exports.createTestingWorkflow = exports.createImplementationWorkflow = exports.createDesignWorkflow = exports.createRequirementsWorkflow = exports.createPlanningWorkflow = exports.createTechnicalDebtWorkflow = exports.createPatternRefactoringWorkflow = exports.mcpManager = exports.createMCPWorkflow = exports.checkpointManager = exports.createCheckpointWorkflow = exports.createPRReviewWorkflow = exports.createPRCreationWorkflow = exports.WorkflowFactory = exports.createScrumTemplateWorkflow = exports.createXPTemplateWorkflow = exports.createAgileTemplateWorkflow = exports.createDevOpsWorkflow = exports.createUXDesignWorkflow = exports.createAgenticWorkflow = exports.createCodeGenWorkflow = exports.createEditWorkflow = exports.createAskWorkflow = exports.createDebuggingWorkflow = exports.createCodeRefactoringWorkflow = void 0;
exports.createPairProgrammingWorkflow = void 0;
exports.getAllWorkflows = getAllWorkflows;
exports.getWorkflowById = getWorkflowById;
exports.createWorkflowInstance = createWorkflowInstance;
// ===== Workflow Types and Core =====
// Export types and core workflow components
__exportStar(require("../agents/workflows/types"), exports);
__exportStar(require("../agents/workflows/graph"), exports);
__exportStar(require("../agents/workflows/workflowRegistry"), exports);
__exportStar(require("../agents/workflows/memory"), exports);
// ===== Workflow Templates =====
// Basic and advanced templates
__exportStar(require("../agents/workflows/templates"), exports);
__exportStar(require("../agents/workflows/advancedTemplates"), exports);
// Specialized templates with explicit exports to avoid naming conflicts
var specializedTemplates_1 = require("../agents/workflows/specializedTemplates");
Object.defineProperty(exports, "createCodeRefactoringWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createCodeRefactoringWorkflow; } });
Object.defineProperty(exports, "createDebuggingWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createDebuggingWorkflow; } });
Object.defineProperty(exports, "createAskWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createAskWorkflow; } });
Object.defineProperty(exports, "createEditWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createEditWorkflow; } });
Object.defineProperty(exports, "createCodeGenWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createCodeGenWorkflow; } });
Object.defineProperty(exports, "createAgenticWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createAgenticWorkflow; } });
Object.defineProperty(exports, "createUXDesignWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createUXDesignWorkflow; } });
Object.defineProperty(exports, "createDevOpsWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createDevOpsWorkflow; } });
Object.defineProperty(exports, "createAgileTemplateWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createAgileWorkflow; } });
Object.defineProperty(exports, "createXPTemplateWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createXPWorkflow; } });
Object.defineProperty(exports, "createScrumTemplateWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createScrumWorkflow; } });
Object.defineProperty(exports, "WorkflowFactory", { enumerable: true, get: function () { return specializedTemplates_1.WorkflowFactory; } });
// ===== Workflow Categories =====
// PR Workflows
var prWorkflows_1 = require("../agents/workflows/prWorkflows");
Object.defineProperty(exports, "createPRCreationWorkflow", { enumerable: true, get: function () { return prWorkflows_1.createPRCreationWorkflow; } });
Object.defineProperty(exports, "createPRReviewWorkflow", { enumerable: true, get: function () { return prWorkflows_1.createPRReviewWorkflow; } });
// Checkpoint Workflow
var checkpointWorkflow_1 = require("../agents/workflows/checkpointWorkflow");
Object.defineProperty(exports, "createCheckpointWorkflow", { enumerable: true, get: function () { return checkpointWorkflow_1.createCheckpointWorkflow; } });
var checkpoint_1 = require("../checkpoint");
Object.defineProperty(exports, "checkpointManager", { enumerable: true, get: function () { return checkpoint_1.checkpointManager; } });
// MCP Workflow
var mcpWorkflow_1 = require("../agents/workflows/mcpWorkflow");
Object.defineProperty(exports, "createMCPWorkflow", { enumerable: true, get: function () { return mcpWorkflow_1.createMCPWorkflow; } });
var mcp_1 = require("../mcp");
Object.defineProperty(exports, "mcpManager", { enumerable: true, get: function () { return mcp_1.mcpManager; } });
// Advanced Refactoring Workflows
var advancedRefactoring_1 = require("../agents/workflows/advancedRefactoring");
Object.defineProperty(exports, "createPatternRefactoringWorkflow", { enumerable: true, get: function () { return advancedRefactoring_1.createPatternRefactoringWorkflow; } });
Object.defineProperty(exports, "createTechnicalDebtWorkflow", { enumerable: true, get: function () { return advancedRefactoring_1.createTechnicalDebtWorkflow; } });
// SDLC Workflows
var sdlcWorkflows_1 = require("../agents/workflows/sdlcWorkflows");
Object.defineProperty(exports, "createPlanningWorkflow", { enumerable: true, get: function () { return sdlcWorkflows_1.createPlanningWorkflow; } });
Object.defineProperty(exports, "createRequirementsWorkflow", { enumerable: true, get: function () { return sdlcWorkflows_1.createRequirementsWorkflow; } });
Object.defineProperty(exports, "createDesignWorkflow", { enumerable: true, get: function () { return sdlcWorkflows_1.createDesignWorkflow; } });
Object.defineProperty(exports, "createImplementationWorkflow", { enumerable: true, get: function () { return sdlcWorkflows_1.createImplementationWorkflow; } });
Object.defineProperty(exports, "createTestingWorkflow", { enumerable: true, get: function () { return sdlcWorkflows_1.createTestingWorkflow; } });
Object.defineProperty(exports, "createDeploymentWorkflow", { enumerable: true, get: function () { return sdlcWorkflows_1.createDeploymentWorkflow; } });
Object.defineProperty(exports, "createMaintenanceWorkflow", { enumerable: true, get: function () { return sdlcWorkflows_1.createMaintenanceWorkflow; } });
// Methodology Workflows
var methodologyWorkflows_1 = require("../agents/workflows/methodologyWorkflows");
Object.defineProperty(exports, "createAgileWorkflow", { enumerable: true, get: function () { return methodologyWorkflows_1.createAgileWorkflow; } });
Object.defineProperty(exports, "createScrumWorkflow", { enumerable: true, get: function () { return methodologyWorkflows_1.createScrumWorkflow; } });
Object.defineProperty(exports, "createXPWorkflow", { enumerable: true, get: function () { return methodologyWorkflows_1.createXPWorkflow; } });
Object.defineProperty(exports, "createWaterfallWorkflow", { enumerable: true, get: function () { return methodologyWorkflows_1.createWaterfallWorkflow; } });
Object.defineProperty(exports, "createDevOpsMethodologyWorkflow", { enumerable: true, get: function () { return methodologyWorkflows_1.createDevOpsWorkflow; } });
// Research Workflows
var researchWorkflow_1 = require("../agents/workflows/researchWorkflow");
Object.defineProperty(exports, "createResearchWorkflow", { enumerable: true, get: function () { return researchWorkflow_1.createResearchWorkflow; } });
Object.defineProperty(exports, "createAcademicResearchWorkflow", { enumerable: true, get: function () { return researchWorkflow_1.createAcademicResearchWorkflow; } });
// UI/UX Workflows
var uiUxWorkflow_1 = require("../agents/workflows/uiUxWorkflow");
Object.defineProperty(exports, "createUIUXWorkflow", { enumerable: true, get: function () { return uiUxWorkflow_1.createUIUXWorkflow; } });
Object.defineProperty(exports, "createUIComponentWorkflow", { enumerable: true, get: function () { return uiUxWorkflow_1.createUIComponentWorkflow; } });
// Documentation Workflows
var documentationWorkflow_1 = require("../agents/workflows/documentationWorkflow");
Object.defineProperty(exports, "createDocumentationWorkflow", { enumerable: true, get: function () { return documentationWorkflow_1.createDocumentationWorkflow; } });
Object.defineProperty(exports, "createAPIDocumentationWorkflow", { enumerable: true, get: function () { return documentationWorkflow_1.createAPIDocumentationWorkflow; } });
// Technical Debt Workflows
var technicalDebtWorkflow_1 = require("../agents/workflows/technicalDebtWorkflow");
Object.defineProperty(exports, "createTechnicalDebtReductionWorkflow", { enumerable: true, get: function () { return technicalDebtWorkflow_1.createTechnicalDebtWorkflow; } });
Object.defineProperty(exports, "createLegacyCodeModernizationWorkflow", { enumerable: true, get: function () { return technicalDebtWorkflow_1.createLegacyCodeModernizationWorkflow; } });
// Vector Stores
__exportStar(require("../agents/workflows/vectorStores"), exports);
// Document QA Workflows
var documentQAWorkflow_1 = require("../agents/workflows/documentQAWorkflow");
Object.defineProperty(exports, "createDocumentQAWorkflow", { enumerable: true, get: function () { return documentQAWorkflow_1.createDocumentQAWorkflow; } });
Object.defineProperty(exports, "createPDFQAWorkflow", { enumerable: true, get: function () { return documentQAWorkflow_1.createPDFQAWorkflow; } });
// Memory-Enhanced Workflows
var memoryEnhancedWorkflow_1 = require("../agents/workflows/memoryEnhancedWorkflow");
Object.defineProperty(exports, "createMemoryEnhancedWorkflow", { enumerable: true, get: function () { return memoryEnhancedWorkflow_1.createMemoryEnhancedWorkflow; } });
// Pattern-Refactoring Workflows
var patternRefactoringWorkflow_1 = require("../agents/workflows/patternRefactoringWorkflow");
Object.defineProperty(exports, "createPatternBasedRefactoringWorkflow", { enumerable: true, get: function () { return patternRefactoringWorkflow_1.createPatternRefactoringWorkflow; } });
Object.defineProperty(exports, "createDesignPatternImplementationWorkflow", { enumerable: true, get: function () { return patternRefactoringWorkflow_1.createDesignPatternImplementationWorkflow; } });
// Knowledge Base Workflows
var knowledgeBaseWorkflow_1 = require("../agents/workflows/knowledgeBaseWorkflow");
Object.defineProperty(exports, "createKnowledgeBaseWorkflow", { enumerable: true, get: function () { return knowledgeBaseWorkflow_1.createKnowledgeBaseWorkflow; } });
Object.defineProperty(exports, "createKnowledgeRetrievalWorkflow", { enumerable: true, get: function () { return knowledgeBaseWorkflow_1.createKnowledgeRetrievalWorkflow; } });
// Multi-Repository Workflows
var multiRepoWorkflow_1 = require("../agents/workflows/multiRepoWorkflow");
Object.defineProperty(exports, "createMultiRepoWorkflow", { enumerable: true, get: function () { return multiRepoWorkflow_1.createMultiRepoWorkflow; } });
Object.defineProperty(exports, "createCrossRepoDependencyWorkflow", { enumerable: true, get: function () { return multiRepoWorkflow_1.createCrossRepoDependencyWorkflow; } });
// Collaborative Coding Workflows
var collaborativeCodingWorkflow_1 = require("../agents/workflows/collaborativeCodingWorkflow");
Object.defineProperty(exports, "createCollaborativeCodingWorkflow", { enumerable: true, get: function () { return collaborativeCodingWorkflow_1.createCollaborativeCodingWorkflow; } });
Object.defineProperty(exports, "createPairProgrammingWorkflow", { enumerable: true, get: function () { return collaborativeCodingWorkflow_1.createPairProgrammingWorkflow; } });
// ===== Core Polyfills and Utilities =====
// Re-export core polyfills for langchain
// This provides compatibility shims for various langchain components
__exportStar(require("../agents/workflows/corePolyfill"), exports);
// Re-export codessa graph implementation
__exportStar(require("../agents/workflows/codessaGraph"), exports);
// ===== Helper Functions =====
const workflowRegistry_1 = require("../agents/workflows/workflowRegistry");
/**
 * Get all registered workflows
 */
function getAllWorkflows() {
    return workflowRegistry_1.workflowRegistry.getAllWorkflows();
}
/**
 * Get a workflow by ID
 */
function getWorkflowById(id) {
    return workflowRegistry_1.workflowRegistry.getWorkflow(id);
}
/**
 * Create a workflow instance
 */
function createWorkflowInstance(id) {
    return workflowRegistry_1.workflowRegistry.createWorkflowInstance(id);
}
//# sourceMappingURL=index.js.map