import * as vscode from 'vscode';
import { Logger } from '../logger';
import { getConfig } from '../config';
import type { IMemoryProvider, MemoryEntry, MemorySearchOptions, MemorySettings, MemorySource, MemoryType, SharedMemoryItem, MemoryAccessLog as TypesMemoryAccessLog, MemoryMetadata } from './types';
import { codessaMemoryProvider } from './codessa/codessaMemory';
import type { CodessaMemoryProvider } from './codessa/codessaMemory';
import { CodessaGraphMemory } from './codessa/codessaGraphMemory';
import { FileChunkingService } from './codessa/fileChunking';
import { v4 as uuidv4 } from 'uuid';
import type { IMemoryManagerAdapter, TemporalMemoryEntry } from './quantum/quantumMemorySystem';
import { QuantumMemorySystem } from './quantum/quantumMemorySystem';
import type { SupervisorAgent } from '../agents/agentTypes/supervisorAgent';

/**
 * Vector Memory Manager interface for type safety
 */
interface IVectorMemoryManager {
  setMemoryOperations(ops: unknown): void;
  initialize(context?: vscode.ExtensionContext): Promise<void>;
  addMemory(entry: MemoryEntry): Promise<MemoryEntry>;
  getMemories(): Promise<MemoryEntry[]>;
  getMemory(id: string): Promise<MemoryEntry | undefined>;
  updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined>;
  deleteMemory(id: string): Promise<boolean>;
  clearMemories(): Promise<void>;
  searchMemories(query: string, limit?: number): Promise<MemoryEntry[]>;
  searchSimilarMemories(query: string, limit?: number): Promise<MemoryEntry[]>;
  onMemoriesChanged(callback: () => void): void;
}

/**
 * Dynamic import helper to break circular dependency
 */
async function getVectorMemoryManager(): Promise<IVectorMemoryManager> {
  const { VectorMemoryManager } = await import('./vectorMemory');
  return VectorMemoryManager.getInstance() as IVectorMemoryManager;
}

/**
 * Represents the memory state for an agent, including shared memories and access logs.
 * This interface is used to manage and track memory sharing between different agents.
 * 
 * @interface CrossAgentMemory
 * @property {string} agentId - The unique identifier of the agent
 * @property {Map<string, SharedMemoryItem>} sharedMemories - Map of memory IDs to shared memory items
 * @property {MemoryAccessLog[]} accessLog - Array of access log entries for auditing
 */
export interface CrossAgentMemory {
  agentId: string;
  sharedMemories: Map<string, SharedMemoryItem>;
  accessLog: MemoryAccessLog[];
}

// Use MemoryAccessLog from types.ts
export type MemoryAccessLog = TypesMemoryAccessLog;

export interface MemoryAnalytics {
  totalMemories: number;
  temporalMemories: number;
  collaborativeShares: number;
  predictiveInsights: number;
  memoryGrowthRate: number;
  averageRetrievalTime: number;
  mostAccessedMemories: string[];
  memoryPatterns: {
    commonTags: string[];
    frequentContexts: string[];
    peakUsageTimes: number[];
  };
  userBehaviorInsights: {
    preferredMemoryTypes: string[];
    searchPatterns: string[];
    collaborationFrequency: number;
  };
}

/**
 * Memory Manager - Core class for managing agent memories and cross-agent memory sharing.
 * 
 * @class MemoryManager
 * @implements {IMemoryManagerAdapter}
 * @description Centralized memory management system that handles storage, retrieval, and sharing of memories
 * across different agents. Supports multiple memory backends and provides advanced features like
 * cross-agent memory sharing, access control, and memory analytics.
 * 
 * @example
 * ```typescript
 * const memoryManager = new MemoryManager();
 * await memoryManager.initialize(context);
 * 
 * // Add a new memory
 * const memory = await memoryManager.addMemory('Important information');
 * 
 * // Search memories
 * const results = await memoryManager.searchMemories({ query: 'important' });
 * ```
 * Central manager for all memory operations
 */
export class MemoryManager implements IMemoryManagerAdapter {
  /** Extension context for VS Code integration */
  private context: vscode.ExtensionContext | undefined;
  
  /** Active memory provider instance for storage and retrieval */
  private memoryProvider: IMemoryProvider | undefined;
  
  /** Vector memory manager for semantic search capabilities */
  private vectorMemoryManager: IVectorMemoryManager | undefined;
  
  /** Graph-based memory system for complex relationships */
  private codessaGraphMemory: CodessaGraphMemory | undefined;
  
  /** Quantum memory system for advanced memory operations */
  private quantumMemorySystem: QuantumMemorySystem | undefined;
  
  /** Tracks whether the memory manager has been initialized */
  private initialized = false;
  
  /** Event emitter for memory change notifications */
  private _onMemoriesChanged = new vscode.EventEmitter<void>();
  
  /**
   * Event that fires when memories are added, updated, or removed
   * @event
   */
  readonly onMemoriesChanged = this._onMemoriesChanged.event;

  /**
   * Register a listener for memory changes (for IMemoryOperations interface)
   * This method is required by the IMemoryOperations interface
   */
  public registerMemoryChangeListener(listener: () => void): void {
    this._onMemoriesChanged.event(listener);
  }

  /**
   * Map of agent IDs to their cross-agent memory states
   * @private
   */
  private crossAgentMemories = new Map<string, CrossAgentMemory>();
  
  /**
   * Global access log for auditing all memory operations
   * @private
   */
  private globalAccessLog: TypesMemoryAccessLog[] = [];
  
  /**
   * Maximum number of entries to keep in the access log
   * @private
   */
  private maxAccessLogSize = 1000;

  /**
   * Cache for search results to improve performance
   * @private
   */
  private memorySearchCache = new Map<string, { results: SharedMemoryItem[]; timestamp: number }>();
  
  /**
   * Time in milliseconds after which search cache entries expire
   * @private
   * @readonly
   */
  private readonly searchCacheExpiry = 2 * 60 * 1000; // 2 minutes
  
  /**
   * Maximum number of search results to cache
   * @private
   * @readonly
   */
  private readonly maxSearchCacheSize = 100;

  /**
   * Global flag indicating if memory features are enabled
   * @static
   * @private
   */
  private static memoryEnabled = false;

  /**
   * Checks if the memory system is globally enabled
   * @returns {boolean} True if memory features are enabled, false otherwise
   */
  public static getMemoryEnabled(): boolean {
    return MemoryManager.memoryEnabled;
  }

  /**
   * Enables or disables the memory system globally
   * @param {boolean} enabled - Whether to enable or disable the memory system
   */
  public static setMemoryEnabled(enabled: boolean): void {
    MemoryManager.memoryEnabled = enabled;
    Logger.instance.info(`Memory ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Initializes the memory manager with the provided VS Code extension context.
   * This method sets up the memory provider, vector memory manager, and other components.
   * 
   * @param {vscode.ExtensionContext} context - The VS Code extension context
   * @returns {Promise<void>} A promise that resolves when initialization is complete
   * @throws {Error} If initialization fails
   * 
   * @example
   * ```typescript
   * await memoryManager.initialize(vscodeContext);
   * ```
   */
  public async initialize(context: vscode.ExtensionContext): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      this.context = context;

      // Check if memory is enabled
      const memoryEnabled = getConfig<boolean>('memory.enabled', true);

      if (!memoryEnabled) {
        Logger.instance.info('Memory system is disabled');
        return;
      }

      // Initialize memory provider based on configuration
      const memorySystem = getConfig<string>('memory.system', 'codessa');

      if (memorySystem === 'codessa') {
        try {
          // Initialize Codessa memory provider
          this.memoryProvider = codessaMemoryProvider;
          await this.memoryProvider.initialize(context);

          // Try to initialize Codessa Graph memory
          try {
            this.codessaGraphMemory = new CodessaGraphMemory(this.memoryProvider as CodessaMemoryProvider);
            await this.codessaGraphMemory.initialize();
            Logger.instance.info('Codessa Graph memory initialized');
          } catch (graphError) {
            // Log but don't fail if graph memory can't initialize
            Logger.instance.warn('Failed to initialize Codessa Graph memory, continuing without it:', graphError);
          }

          Logger.instance.info('Codessa memory provider initialized');
        } catch (error) {
          // If Codessa memory provider fails, fall back to basic vector memory
          Logger.instance.warn('Failed to initialize Codessa memory provider, falling back to basic vector memory:', error);

          // Initialize basic vector memory manager as fallback
          this.memoryProvider = undefined;
          this.vectorMemoryManager = await getVectorMemoryManager();
          // Set up the memory operations for vector memory manager to break circular dependency
          this.vectorMemoryManager.setMemoryOperations(this);
          await this.vectorMemoryManager.initialize(context);

          Logger.instance.info('Basic vector memory manager initialized as fallback');
        }
      } else {
        // Initialize basic vector memory manager
        this.vectorMemoryManager = await getVectorMemoryManager();
        // Set up the memory operations for vector memory manager to break circular dependency
        this.vectorMemoryManager.setMemoryOperations(this);
        await this.vectorMemoryManager.initialize(context);

        Logger.instance.info('Basic vector memory manager initialized');
      }

      // Initialize Quantum Memory System
      try {
        // We need SupervisorAgent for quantum memory, but it might not be available yet
        // So we'll initialize it later when needed
        Logger.instance.info('Quantum Memory System will be initialized when SupervisorAgent is available');
      } catch (error) {
        Logger.instance.warn('Failed to initialize Quantum Memory System:', error);
      }

      // Register event handlers
      if (this.memoryProvider) {
        if ('onMemoriesChanged' in this.memoryProvider) {
          (this.memoryProvider as unknown as { onMemoriesChanged: (listener: () => void) => void }).onMemoriesChanged(() => {
            this._onMemoriesChanged.fire();
          });
        }
      }

      if (this.vectorMemoryManager) {
        this.vectorMemoryManager.onMemoriesChanged(() => {
          this._onMemoriesChanged.fire();
        });
      }

      this.initialized = true;
      Logger.instance.info('Memory manager initialized successfully');
    } catch (error) {
      Logger.instance.error('Failed to initialize memory manager:', error);
      throw error;
    }
  }

  /**
   * Adds a new memory or updates an existing one if an ID is provided.
   * 
   * @param {string | Omit<MemoryEntry, 'id' | 'timestamp'>} contentOrEntry - The content as a string or a partial memory entry
   * @returns {Promise<MemoryEntry>} The created or updated memory entry
   * @throws {Error} If the memory provider is not initialized or the operation fails
   * 
   * @example
   * ```typescript
   * // Add a simple text memory
   * const memory = await memoryManager.addMemory('Important note');
   * 
   * // Add a memory with metadata
   * const memoryWithMeta = await memoryManager.addMemory({
   *   content: 'Project requirements',
   *   metadata: {
   *     type: 'requirements',
   *     priority: 'high'
   *   }
   * });
   * ```
   */
  public async addMemory(contentOrEntry: string | Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      // Create memory entry with proper type handling
      const baseMemory = typeof contentOrEntry === 'string'
        ? {
          content: contentOrEntry,
          metadata: {
            source: 'user' as MemorySource,
            type: 'semantic' as MemoryType
          }
        }
        : contentOrEntry;
      
      // Create final memory entry with ID and timestamp
      const memoryEntry: MemoryEntry = {
        ...baseMemory,
        id: uuidv4(), // Always generate a new ID for memoryEntry
        timestamp: Date.now()
      };

      // Add to memory provider
      if (this.memoryProvider) {
        return await this.memoryProvider.addMemory(memoryEntry);
      } else if (this.vectorMemoryManager) {
        return await this.vectorMemoryManager.addMemory(memoryEntry);
      } else {
        throw new Error('No memory provider initialized');
      }
    } catch (error) {
      Logger.instance.error('Failed to add memory:', error);
      throw error;
    }
  }

  /**
   * Retrieves all stored memories.
   * 
   * @returns {Promise<MemoryEntry[]>} An array of all memory entries
   * 
   * @example
   * ```typescript
   * const allMemories = await memoryManager.getMemories();
   * console.log(`Total memories: ${allMemories.length}`);
   * ```
   */
  public async getMemories(): Promise<MemoryEntry[]> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      if (this.memoryProvider) {
        return await this.memoryProvider.getMemories();
      } else if (this.vectorMemoryManager) {
        return await this.vectorMemoryManager.getMemories();
      } else {
        return [];
      }
    } catch (error) {
      Logger.instance.error('Failed to get memories:', error);
      return [];
    }
  }

  /**
   * Retrieves a memory entry by its unique identifier.
   * 
   * @param {string} id - The unique identifier of the memory to retrieve
   * @returns {Promise<MemoryEntry | undefined>} The memory entry if found, undefined otherwise
   * 
   * @example
   * ```typescript
   * const memory = await memoryManager.getMemory('memory-123');
   * if (memory) {
   *   console.log('Found memory:', memory.content);
   * }
   * ```
   */
  public async getMemory(id: string): Promise<MemoryEntry | undefined> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      if (this.memoryProvider) {
        return await this.memoryProvider.getMemory(id);
      } else if (this.vectorMemoryManager) {
        return await this.vectorMemoryManager.getMemory(id);
      } else {
        return undefined;
      }
    } catch (error) {
      Logger.instance.error(`Failed to get memory ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Updates an existing memory with new content or metadata.
   * 
   * @param {string} id - The ID of the memory to update
   * @param {Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>} updates - The fields to update
   * @returns {Promise<MemoryEntry | undefined>} The updated memory entry, or undefined if not found
   * @throws {Error} If the update operation fails
   * 
   * @example
   * ```typescript
   * // Update memory content
   * const updated = await memoryManager.updateMemory('memory-123', {
   *   content: 'Updated content',
   *   metadata: { updatedAt: Date.now() }
   * });
   * ```
   */
  public async updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      if (this.memoryProvider) {
        return await this.memoryProvider.updateMemory(id, updates);
      } else if (this.vectorMemoryManager) {
        return await this.vectorMemoryManager.updateMemory(id, updates);
      } else {
        return undefined;
      }
    } catch (error) {
      Logger.instance.error(`Failed to update memory ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Deletes a memory by its ID.
   * 
   * @param {string} id - The ID of the memory to delete
   * @returns {Promise<boolean>} True if the memory was deleted, false if not found
   * 
   * @example
   * ```typescript
   * const wasDeleted = await memoryManager.deleteMemory('memory-123');
   * console.log(wasDeleted ? 'Memory deleted' : 'Memory not found');
   * ```
   */
  public async deleteMemory(id: string): Promise<boolean> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      if (this.memoryProvider) {
        return await this.memoryProvider.deleteMemory(id);
      } else if (this.vectorMemoryManager) {
        return await this.vectorMemoryManager.deleteMemory(id);
      } else {
        return false;
      }
    } catch (error) {
      Logger.instance.error(`Failed to delete memory ${id}:`, error);
      return false;
    }
  }

  /**
   * Removes all stored memories.
   * 
   * @returns {Promise<void>} A promise that resolves when all memories are cleared
   * 
   * @example
   * ```typescript
   * // Clear all memories (use with caution)
   * await memoryManager.clearMemories();
   * ```
   */
  public async clearMemories(): Promise<void> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      if (this.memoryProvider) {
        await this.memoryProvider.clearMemories();
      } else if (this.vectorMemoryManager) {
        await this.vectorMemoryManager.clearMemories();
      }

      Logger.instance.info('All memories cleared');
    } catch (error) {
      Logger.instance.error('Failed to clear memories:', error);
      throw error;
    }
  }

  /**
   * Searches through stored memories using the provided query and options.
   * Supports contextual ranking and filtering of results.
   * 
   * @param {MemorySearchOptions} options - Search configuration and query parameters
   * @returns {Promise<MemoryEntry[]>} An array of matching memory entries, sorted by relevance
   * 
   * @example
   * ```typescript
   * // Basic search
   * const results = await memoryManager.searchMemories({
   *   query: 'important note',
   *   limit: 10,
   *   minRelevance: 0.5
   * });
   * 
   * // Search with filters
   * const filteredResults = await memoryManager.searchMemories({
   *   query: 'error',
   *   filters: {
   *     type: 'error',
   *     timestamp: { $gt: Date.now() - 86400000 } // Last 24 hours
   *   }
   * });
   * ```
   */
  public async searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      let results: MemoryEntry[] = [];

      if (this.memoryProvider) {
        results = await this.memoryProvider.searchMemories(options);
      } else if (this.vectorMemoryManager) {
        // Convert options to vector memory manager format
        results = await this.vectorMemoryManager.searchMemories(options.query, options.limit);
      } else {
        return [];
      }

      // Apply contextual ranking if enabled
      if (getConfig<boolean>('phase4.enableContextualRanking', true)) {
        results = this.applyContextualRanking(results, options.query);
      }

      return results;
    } catch (error) {
      Logger.instance.error('Failed to search memories:', error);
      return [];
    }
  }

  /**
     * Apply contextual ranking to search results
     */
  private applyContextualRanking(results: MemoryEntry[], query: string): MemoryEntry[] {
    try {
      // Get current workspace context
      const workspaceContext = this.getCurrentWorkspaceContext();

      // Score results based on relevance to current context
      const scoredResults = results.map(result => ({
        entry: result,
        score: this.calculateContextualScore(result, query, workspaceContext)
      }));

      // Sort by score and return entries
      return scoredResults
        .sort((a, b) => b.score - a.score)
        .map(item => item.entry);
    } catch (error) {
      Logger.instance.warn('Failed to apply contextual ranking:', error);
      return results;
    }
  }

  /**
     * Calculate contextual relevance score for search results
     */
  private calculateContextualScore(entry: MemoryEntry, query: string, context: {currentFile?: string; language?: string; project?: string}): number {
    let score = 0;

    // Base relevance score (simple text matching)
    const queryLower = query.toLowerCase();
    const contentLower = entry.content.toLowerCase();

    if (contentLower.includes(queryLower)) score += 10;

    // Context-based scoring
    if (context.currentFile && entry.metadata.tags?.includes('file:' + context.currentFile)) {
      score += 15; // High relevance for current file
    }

    if (context.language && entry.metadata.tags?.includes(context.language)) {
      score += 8; // Medium relevance for same language
    }

    if (context.project && entry.metadata.tags?.includes('project:' + context.project)) {
      score += 5; // Low relevance for same project
    }

    // Recency bonus (newer memories get slight boost)
    const daysSinceCreation = (Date.now() - new Date(entry.timestamp).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 5 - daysSinceCreation); // Up to 5 points for recent memories

    // Type-based scoring
    if (entry.metadata.type === 'insight') score += 3;
    if (entry.metadata.type === 'code') score += 2;

    return score;
  }

  /**
     * Get current workspace context for memory operations
     */
  private getCurrentWorkspaceContext(): {currentFile?: string; language?: string; project?: string} {
    try {
      const context: {currentFile?: string; language?: string; project?: string} = {};

      // Get active editor information from VS Code API
      try {
        // Use VS Code API directly instead of window object
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
          context.currentFile = activeEditor.document.fileName;
          context.language = activeEditor.document.languageId;

          // Get workspace folder
          const workspaceFolder = vscode.workspace.getWorkspaceFolder(activeEditor.document.uri);
          if (workspaceFolder) {
            context.project = workspaceFolder.name;
          }
        }
      } catch (vscodeError) {
        // VS Code API not available, return empty context
        Logger.instance.debug('VS Code API not available for workspace context');
      }

      return context;
    } catch (error) {
      Logger.instance.warn('Failed to get workspace context:', error);
      return {};
    }
  }

  /**
     * Search memories by semantic similarity
     */
  public async searchSimilarMemories(query: string, options: Partial<MemorySearchOptions> = {}): Promise<MemoryEntry[]> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      if (this.memoryProvider) {
        return await this.memoryProvider.searchSimilarMemories(query, options);
      } else if (this.vectorMemoryManager) {
        // Convert options to vector memory manager format
        return await this.vectorMemoryManager.searchSimilarMemories(query, options.limit);
      } else {
        return [];
      }
    } catch (error) {
      Logger.instance.error('Failed to search similar memories:', error);
      return [];
    }
  }

  /**
     * Process a message with Codessa Graph memory
     */
  public async processMessage(message: string): Promise<string> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      // Check if Codessa Graph memory is available
      if (getConfig<string>('memory.system', 'codessa') === 'codessa' && this.codessaGraphMemory) {
        return await this.codessaGraphMemory.processMessage(message);
      } else {
        throw new Error('Codessa Graph memory not available');
      }
    } catch (error) {
      Logger.instance.error('Failed to process message with Codessa Graph memory:', error);
      throw error;
    }
  }

  /**
     * Chunk a file and store in memory
     */
  public async chunkFile(filePath: string): Promise<MemoryEntry[]> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      // Check if Codessa memory is available
      if (getConfig<string>('memory.system', 'codessa') === 'codessa') {
        return await FileChunkingService.chunkFile(filePath);
      } else {
        throw new Error('Codessa memory not available for file chunking');
      }
    } catch (error) {
      Logger.instance.error(`Failed to chunk file ${filePath}:`, error);
      throw error;
    }
  }

  /**
     * Chunk a workspace folder and store in memory
     */
  public async chunkWorkspace(
    folderPath: string,
    includePatterns: string[] = ['**/*.{js,ts,jsx,tsx,py,java,c,cpp,cs,go,rb,php,html,css,md,json}'],
    excludePatterns: string[] = ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.git/**']
  ): Promise<MemoryEntry[]> {
    if (!this.initialized && this.context) {
      await this.initialize(this.context);
    }

    try {
      // Check if Codessa memory is available
      if (getConfig<string>('memory.system', 'codessa') === 'codessa') {
        return await FileChunkingService.chunkWorkspace(folderPath, includePatterns, excludePatterns);
      } else {
        throw new Error('Codessa memory not available for workspace chunking');
      }
    } catch (error) {
      Logger.instance.error(`Failed to chunk workspace ${folderPath}:`, error);
      throw error;
    }
  }

  /**
     * Get comprehensive memory settings - OPTIMIZED
     */
  public getMemorySettings(): MemorySettings {
    if (this.memoryProvider && 'getMemorySettings' in this.memoryProvider) {
      return (this.memoryProvider as unknown as { getMemorySettings: () => MemorySettings }).getMemorySettings();
    } else {
      // Return comprehensive default settings
      return {
        // Core settings
        enabled: getConfig<boolean>('memory.enabled', true),
        system: getConfig<'basic' | 'codessa' | 'quantum'>('memory.system', 'codessa'),
        maxMemories: getConfig<number>('memory.maxMemories', 1000),
        maxMemoriesPerFile: getConfig<number>('phase4.maxMemoriesPerFile', 100),
        memoryRetentionDays: getConfig<number>('phase4.memoryRetentionDays', 30),
        relevanceThreshold: getConfig<number>('memory.relevanceThreshold', 0.7),
        contextWindowSize: getConfig<number>('memory.contextWindowSize', 5),
        conversationHistorySize: getConfig<number>('memory.conversationHistorySize', 100),

        // Advanced memory system settings
        enableQuantumMemory: getConfig<boolean>('phase4.enableQuantumMemory', true),
        enablePredictiveInsights: getConfig<boolean>('phase4.enablePredictiveInsights', true),
        enableMemoryVisualization: getConfig<boolean>('phase4.enableMemoryVisualization', true),
        enableCollaborativeMemory: getConfig<boolean>('phase4.enableCollaborativeMemory', true),

        // Vector store settings
        vectorStore: getConfig<'memory' | 'chroma' | 'pinecone' | 'weaviate' | 'hnswlib'>('memory.vectorStore', 'chroma'),
        vectorStoreSettings: {
          chroma: {
            directory: getConfig<string>('memory.vectorStore.chroma.directory', './.codessa/chroma'),
            collectionName: getConfig<string>('memory.vectorStore.chroma.collectionName', 'codessa_memories')
          },
          pinecone: {
            apiKey: getConfig<string>('memory.vectorStore.pinecone.apiKey', ''),
            environment: getConfig<string>('memory.vectorStore.pinecone.environment', ''),
            indexName: getConfig<string>('memory.vectorStore.pinecone.indexName', 'codessa-memories')
          }
        },

        // Database settings
        database: getConfig<'sqlite' | 'mysql' | 'postgres' | 'mongodb' | 'redis'>('memory.database', 'sqlite'),
        databaseSettings: {
          sqlite: {
            filename: getConfig<string>('memory.database.sqlite.filename', './.codessa/memory.db')
          },
          mysql: {
            host: getConfig<string>('memory.database.mysql.host', 'localhost'),
            port: getConfig<number>('memory.database.mysql.port', 3306),
            user: getConfig<string>('memory.database.mysql.user', 'root'),
            password: getConfig<string>('memory.database.mysql.password', ''),
            database: getConfig<string>('memory.database.mysql.database', 'codessa'),
            table: getConfig<string>('memory.database.mysql.table', 'memories')
          },
          postgres: {
            connectionString: getConfig<string>('memory.database.postgres.connectionString', ''),
            schema: getConfig<string>('memory.database.postgres.schema', 'codessa')
          },
          mongodb: {
            connectionString: getConfig<string>('memory.database.mongodb.connectionString', ''),
            database: getConfig<string>('memory.database.mongodb.database', 'codessa'),
            collection: getConfig<string>('memory.database.mongodb.collection', 'memories')
          },
          redis: {
            url: getConfig<string>('memory.database.redis.url', ''),
            keyPrefix: getConfig<string>('memory.database.redis.keyPrefix', 'codessa:')
          }
        },

        // File processing settings
        fileChunking: {
          chunkSize: getConfig<number>('memory.fileChunking.chunkSize', 1000),
          chunkOverlap: getConfig<number>('memory.fileChunking.chunkOverlap', 200),
          maxChunksPerFile: getConfig<number>('memory.fileChunking.maxChunksPerFile', 100)
        },

        // Performance settings
        cacheSize: getConfig<number>('memory.cacheSize', 1000),
        cacheExpiry: getConfig<number>('memory.cacheExpiry', 300000), // 5 minutes
        batchSize: getConfig<number>('memory.batchSize', 50),
        maxConcurrentOperations: getConfig<number>('memory.maxConcurrentOperations', 10)
      };
    }
  }

  /**
     * Update memory settings
     */
  public async updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean> {
    try {
      if (this.memoryProvider && 'updateMemorySettings' in this.memoryProvider) {
        const updateMethod = (this.memoryProvider as unknown as { updateMemorySettings: (settings: Partial<MemorySettings>) => boolean | void | Promise<boolean | void> }).updateMemorySettings;
        const result = await Promise.resolve(updateMethod(settings));
        return result === true || result === undefined; // Handle both boolean returns and void returns
      } else {
        Logger.instance.error('Memory provider does not support updating settings');
        return false;
      }
    } catch (error) {
      Logger.instance.error('Failed to update memory settings:', error);
      return false;
    }
  }

  /**
     * Initialize Quantum Memory System with SupervisorAgent
     */
  public async initializeQuantumMemory(supervisorAgent: SupervisorAgent): Promise<void> {
    try {
      if (!this.quantumMemorySystem) {
        // Create an adapter that delegates all IMemoryManagerAdapter methods to this instance
        const memoryManagerAdapter: IMemoryManagerAdapter = {
          // Core memory operations
          addMemory: (contentOrEntry) => this.addMemory(contentOrEntry),
          getMemory: (id) => this.getMemory(id),
          getMemories: () => this.getMemories(),
          deleteMemory: (id) => this.deleteMemory(id),
          clearMemories: () => this.clearMemories(),
          searchMemories: (options) => this.searchMemories(options),
          searchSimilarMemories: (query, options) => this.searchSimilarMemories(query, options),
          
          // Memory settings and configuration
          getMemorySettings: () => this.getMemorySettings(),
          updateMemorySettings: (settings) => this.updateMemorySettings(settings),
          
          // Cross-agent memory sharing
          shareMemoryWithAgent: (memoryId, fromAgentId, toAgentId, accessLevel, expiresIn) => 
            this.shareMemoryWithAgent(memoryId, fromAgentId, toAgentId, accessLevel, expiresIn),
          getSharedMemoriesForAgent: (agentId) => this.getSharedMemoriesForAgent(agentId),
          revokeSharedMemory: (memoryId, fromAgentId, toAgentId) => 
            this.revokeSharedMemory(memoryId, fromAgentId, toAgentId),
          searchSharedMemories: (agentId, query, limit) => 
            this.searchSharedMemories(agentId, query, limit),
            
          // Access logging and stats
          getMemoryAccessLog: (agentId, limit) => this.getMemoryAccessLog(agentId, limit),
          getMemoryAccessStats: () => this.getMemoryAccessStats(),
          
          // Advanced operations
          processMessage: (message) => this.processMessage(message),
          chunkFile: (filePath) => this.chunkFile(filePath),
          chunkWorkspace: (folderPath, includePatterns, excludePatterns) => 
            this.chunkWorkspace(folderPath, includePatterns, excludePatterns),
            
          // Event handling
          onMemoriesChanged: this.onMemoriesChanged
        };

        this.quantumMemorySystem = new QuantumMemorySystem(
          memoryManagerAdapter,
          this.vectorMemoryManager ?? await getVectorMemoryManager(),
          supervisorAgent
        );

        await this.quantumMemorySystem.initialize();
        Logger.instance.info('Quantum Memory System initialized successfully');
      }
    } catch (error) {
      Logger.instance.error('Failed to initialize Quantum Memory System:', error);
    }
  }

  /**
     * Get Quantum Memory System instance
     */
  public getQuantumMemorySystem(): QuantumMemorySystem | undefined {
    return this.quantumMemorySystem;
  }

  /**
     * Store temporal memory with versioning
     */
  public async storeTemporalMemory(
    content: string,
    context: { filePath: string; lineNumber?: number; functionName?: string; className?: string; projectContext: string }, // Typed context
    changeType: 'create' | 'update' | 'delete' | 'refactor' = 'create',
    metadata: Partial<MemoryMetadata> = {} // Use MemoryMetadata type from types.ts
  ): Promise<string | undefined> {
    if (this.quantumMemorySystem) {
      return await this.quantumMemorySystem.storeTemporalMemory(content, context, changeType, metadata);
    } else {
      Logger.instance.warn('Quantum Memory System not initialized, falling back to regular memory');
      // Fallback to regular memory
      const memoryEntry = await this.addMemory({
        content,
        metadata: {
          source: 'temporal' as MemorySource, // Use imported type
          type: 'semantic' as MemoryType, // Use imported type
          tags: ['temporal', changeType, ...(metadata.tags ?? [])],
          ...metadata
        }
      });
      return memoryEntry.id;
    }
  }

  /**
     * Retrieve contextual memories based on query
     */
  public async retrieveContextualMemories(query: string): Promise<(MemoryEntry | TemporalMemoryEntry)[]> { // Allow both MemoryEntry and TemporalMemoryEntry
    if (this.quantumMemorySystem) {
      // Convert string query to ContextualMemoryQuery
      const contextualQuery = {
        query: query,
        context: {} as { currentFile?: string; language?: string; project?: string }, // Typed context
        maxResults: 10
      };
      return await this.quantumMemorySystem.retrieveContextualMemories(contextualQuery);
    } else {
      Logger.instance.warn('Quantum Memory System not initialized, falling back to regular search');
      // Fallback to regular search
      return await this.searchMemories({ query: query, limit: 10 });
    }
  }

  /**
     * Generate predictive insights from memory
     */
  public async generatePredictiveInsights(context: string): Promise<unknown[]> { // Keeping 'any[]' as return type might be dynamic from QuantumMemorySystem
    if (this.quantumMemorySystem) {
      return await this.quantumMemorySystem.generatePredictiveInsights(context);
    } else {
      Logger.instance.warn('Quantum Memory System not initialized, cannot generate predictive insights');
      return [];
    }
  }

  /**
   * Check if the memory manager is initialized
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
     * Get memory usage analytics and statistics
     */
  public getMemoryAnalytics(): MemoryAnalytics { // Keeping 'any' here as the return type is complex and might be dynamic from QuantumMemorySystem
    if (this.quantumMemorySystem) {
      return this.quantumMemorySystem.getAnalytics();
    } else {
      Logger.instance.warn('Quantum Memory System not initialized, returning basic analytics');
      return {
        totalMemories: 0,
        temporalMemories: 0,
        collaborativeShares: 0,
        predictiveInsights: 0,
        memoryGrowthRate: 0,
        averageRetrievalTime: 0,
        mostAccessedMemories: [],
        memoryPatterns: {
          commonTags: [],
          frequentContexts: [],
          peakUsageTimes: []
        },
        userBehaviorInsights: {
          preferredMemoryTypes: [],
          searchPatterns: [],
          collaborationFrequency: 0
        }
      };
    }
  }

  /**
   * Shares a memory with another agent, granting specified access permissions.
   * 
   * @param {string} memoryId - The ID of the memory to share
   * @param {string} fromAgentId - The ID of the agent sharing the memory
   * @param {string} toAgentId - The ID of the agent to share with
   * @param {'read' | 'write' | 'admin'} [accessLevel='read'] - The level of access to grant
   * @param {number} [expiresIn] - Optional expiration time in milliseconds
   * @returns {Promise<boolean>} True if sharing was successful, false otherwise
   * 
   * @example
   * ```typescript
   * // Share a memory with read access
   * await memoryManager.shareMemoryWithAgent(
   *   'memory-123',
   *   'agent-1',
   *   'agent-2',
   *   'read'
   * );
   * 
   * // Share with write access that expires in 1 hour
   * await memoryManager.shareMemoryWithAgent(
   *   'memory-123',
   *   'agent-1',
   *   'agent-2',
   *   'write',
   *   60 * 60 * 1000 // 1 hour in milliseconds
   * );
   * ```
   */
  public async shareMemoryWithAgent(
    memoryId: string,
    fromAgentId: string,
    toAgentId: string,
    accessLevel: 'read' | 'write' | 'admin' = 'read',
    expiresIn?: number
  ): Promise<boolean> {
    try {
      // Get the memory to share
      const memory = await this.getMemory(memoryId);
      if (!memory) {
        this.logMemoryAccess(fromAgentId, memoryId, 'share', false);
        return false;
      }

      // Create shared memory item
      const sharedMemory: SharedMemoryItem = {
        id: `shared_${memoryId}_${uuidv4()}`,
        memoryId: memoryId,
        sharedBy: fromAgentId,
        sharedWith: [toAgentId],
        permissions: accessLevel,
        sharedAt: Date.now(),
        expiresAt: expiresIn ? Date.now() + expiresIn : undefined,
        context: `Shared from ${fromAgentId} to ${toAgentId}`,
        memory: memory
      };

      // Ensure cross-agent memory exists for target agent
      if (!this.crossAgentMemories.has(toAgentId)) {
        this.crossAgentMemories.set(toAgentId, {
          agentId: toAgentId,
          sharedMemories: new Map(),
          accessLog: []
        });
      }

      // Add shared memory to target agent
      const targetAgentMemory = this.crossAgentMemories.get(toAgentId);
      if (!targetAgentMemory) {
        this.logMemoryAccess(fromAgentId, memoryId, 'share', false, 'Failed to get or create agent memory');
        return false;
      }
      targetAgentMemory.sharedMemories.set(memoryId, sharedMemory);

      this.logMemoryAccess(fromAgentId, memoryId, 'share', true);
      Logger.instance.info(`Memory ${memoryId} shared from ${fromAgentId} to ${toAgentId} with ${accessLevel} access`);

      return true;
    } catch (error) {
      Logger.instance.error(`Failed to share memory ${memoryId}:`, error);
      this.logMemoryAccess(fromAgentId, memoryId, 'share', false);
      return false;
    }
  }

  /**
   * Retrieves all memories that have been shared with a specific agent.
   * 
   * @param {string} agentId - The ID of the agent
   * @returns {SharedMemoryItem[]} An array of shared memory items
   * 
   * @example
   * ```typescript
   * const sharedMemories = memoryManager.getSharedMemoriesForAgent('agent-2');
   * console.log(`Agent has ${sharedMemories.length} shared memories`);
   * ```
   */
  public getSharedMemoriesForAgent(agentId: string): SharedMemoryItem[] {
    const agentMemory = this.crossAgentMemories.get(agentId);
    if (!agentMemory) {
      return [];
    }

    // Filter out expired memories
    const now = Date.now();
    const validMemories: SharedMemoryItem[] = [];

    for (const [memoryId, sharedMemory] of agentMemory.sharedMemories) {
      if (!sharedMemory.expiresAt || sharedMemory.expiresAt > now) {
        validMemories.push(sharedMemory);
      } else {
        // Remove expired memory
        agentMemory.sharedMemories.delete(memoryId);
      }
    }

    return validMemories;
  }

  /**
     * Revoke shared memory access between agents
     */
  public async revokeSharedMemory(memoryId: string, fromAgentId: string, toAgentId: string): Promise<boolean> {
    try {
      const agentMemory = this.crossAgentMemories.get(toAgentId);
      if (!agentMemory) {
        return false;
      }

      const removed = agentMemory.sharedMemories.delete(memoryId);
      if (removed) {
        this.logMemoryAccess(fromAgentId, memoryId, 'revoke', true);
        Logger.instance.info(`Revoked shared memory ${memoryId} from agent ${toAgentId}`);
      }

      // Add a minimal async operation to justify the async
      await Promise.resolve();
      return removed;
    } catch (error) {
      Logger.instance.error(`Failed to revoke shared memory ${memoryId}:`, error);
      this.logMemoryAccess(fromAgentId, memoryId, 'revoke', false);
      return false;
    }
  }

  /**
   * Searches through memories shared with a specific agent.
   * 
   * @param {string} agentId - The ID of the agent to search shared memories for
   * @param {string} query - The search query
   * @param {number} [limit=10] - Maximum number of results to return
   * @returns {Promise<SharedMemoryItem[]>} An array of matching shared memory items
   * 
   * @example
   * ```typescript
   * // Search through shared memories
   * const results = await memoryManager.searchSharedMemories(
   *   'agent-2',
   *   'important project',
   *   5 // Limit to 5 results
   * );
   * ```
   */
  public async searchSharedMemories(agentId: string, query: string, limit = 10): Promise<SharedMemoryItem[]> {
    // Cleanup expired caches periodically
    if (Math.random() < 0.1) { // 10% chance to cleanup on each search
      this.cleanupExpiredCaches();
    }

    // Add minimal async operation to justify async
    await Promise.resolve();

    // Check cache first
    const cacheKey = `search_${agentId}_${query}_${limit}`;
    const cached = this.getSearchCache(cacheKey);
    if (cached) {
      return cached;
    }

    const sharedMemories = this.getSharedMemoriesForAgent(agentId);

    if (sharedMemories.length === 0) {
      return [];
    }

    // Optimized text-based search with early termination
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/).filter(w => w.length > 2);

    const matches: { memory: SharedMemoryItem; score: number }[] = [];

    for (const memory of sharedMemories) {
      const score = this.calculateOptimizedRelevanceScore(memory, queryWords, queryLower);
      if (score > 0) {
        matches.push({ memory, score });

        // Early termination for performance if we have enough high-scoring matches
        if (matches.length >= limit * 3 && score < 5) {
          break;
        }
      }
    }

    // Sort by relevance score (descending)
    matches.sort((a, b) => b.score - a.score);

    const results = matches.slice(0, limit).map(m => m.memory);

    // Cache the results
    this.setSearchCache(cacheKey, results);

    return results;
  }

  /**
   * Retrieves the access log for a specific agent, showing all memory operations.
   * 
   * @param {string} agentId - The ID of the agent
   * @param {number} [limit=100] - Maximum number of log entries to return
   * @returns {MemoryAccessLog[]} An array of access log entries
   * 
   * @example
   * ```typescript
   * // Get the 50 most recent access log entries
   * const accessLog = memoryManager.getMemoryAccessLog('agent-1', 50);
   * accessLog.forEach(entry => {
   *   console.log(`${entry.action} ${entry.memoryId} - ${entry.success ? 'success' : 'failed'}`);
   * });
   * ```
   */
  public getMemoryAccessLog(agentId: string, limit = 100): MemoryAccessLog[] {
    return this.globalAccessLog
      .filter(log => log.agentId === agentId)
      .slice(-limit)
      .reverse();
  }

  /**
     * Get global memory access statistics and metrics
     */
  public getMemoryAccessStats(): {
    totalAccesses: number;
    successfulAccesses: number;
    failedAccesses: number;
    agentStats: Map<string, { reads: number; writes: number; shares: number; revokes: number }>;
    } {
    const agentStats = new Map<string, { reads: number, writes: number, shares: number, revokes: number }>();
    let totalAccesses = 0;
    let successfulAccesses = 0;
    let failedAccesses = 0;

    for (const log of this.globalAccessLog) {
      totalAccesses++;
      if (log.success) {
        successfulAccesses++;
      } else {
        failedAccesses++;
      }

      if (!agentStats.has(log.agentId)) {
        agentStats.set(log.agentId, { reads: 0, writes: 0, shares: 0, revokes: 0 });
      }

      const stats = agentStats.get(log.agentId);
      if (stats) {
        switch (log.action) {
          case 'read': stats.reads++; break;
          case 'write': stats.writes++; break;
          case 'share': stats.shares++; break;
          case 'revoke': stats.revokes++; break;
        }
      }
    }

    return {
      totalAccesses,
      successfulAccesses,
      failedAccesses,
      agentStats
    };
  }

  /**
     * Log memory access for security and auditing
     */
  private logMemoryAccess(agentId: string, memoryId: string, action: 'read' | 'write' | 'share' | 'revoke', success: boolean, error?: string): void {
    const logEntry: MemoryAccessLog = {
      id: `log_${uuidv4()}`,
      agentId,
      memoryId,
      action,
      timestamp: Date.now(),
      success,
      error
    };

    this.globalAccessLog.push(logEntry);

    // Maintain log size limit
    if (this.globalAccessLog.length > this.maxAccessLogSize) {
      this.globalAccessLog = this.globalAccessLog.slice(-this.maxAccessLogSize);
    }

    // Also add to agent-specific log
    const agentMemory = this.crossAgentMemories.get(agentId);
    if (agentMemory) {
      agentMemory.accessLog.push(logEntry);
      if (agentMemory.accessLog.length > 100) {
        agentMemory.accessLog = agentMemory.accessLog.slice(-100);
      }
    }
  }

  /**
     * Calculate relevance score for shared memory search results
     */
  private calculateOptimizedRelevanceScore(memory: SharedMemoryItem, queryWords: string[], queryLower: string): number {
    let score = 0;
    const contentLower = memory.memory.content.toLowerCase();
    const contextLower = memory.context.toLowerCase();

    // Word-based matching in memory content (more accurate than regex)
    for (const word of queryWords) {
      if (contentLower.includes(word)) {
        score += 2;
        // Bonus for exact word boundaries
        if (contentLower.match(new RegExp(`\\b${word}\\b`))) {
          score += 1;
        }
      }

      // Also search in context
      if (contextLower.includes(word)) {
        score += 1;
      }
    }

    // Tag matches (high priority) - use memory.metadata.tags
    const tags = memory.memory.metadata.tags ?? [];
    for (const tag of tags) {
      const tagLower = tag.toLowerCase();
      for (const word of queryWords) {
        if (tagLower.includes(word)) {
          score += 5; // Tags are more important
        }
      }
    }

    // Exact phrase match bonus
    if (contentLower.includes(queryLower)) {
      score += 10;
    }

    // Recency bonus (optimized calculation) - use sharedAt timestamp
    const ageInHours = (Date.now() - memory.sharedAt) / (1000 * 60 * 60);
    if (ageInHours < 24) {
      score += 5; // Recent memories are more relevant
    } else if (ageInHours < 168) { // 1 week
      score += 2;
    }

    // Permission level bonus (higher access = more important)
    if (memory.permissions === 'admin') {
      score += 3;
    } else if (memory.permissions === 'write') {
      score += 1;
    }

    return score;
  }

  /**
     * Performance optimization: Search cache management
     */
  private getSearchCache(key: string): SharedMemoryItem[] | null {
    const cached = this.memorySearchCache.get(key);
    if (!cached) return null;

    // Check if cache entry has expired
    if (Date.now() - cached.timestamp > this.searchCacheExpiry) {
      this.memorySearchCache.delete(key);
      return null;
    }

    return cached.results;
  }

  private setSearchCache(key: string, results: SharedMemoryItem[]): void {
    // Implement LRU cache behavior
    if (this.memorySearchCache.size >= this.maxSearchCacheSize) {
      // Remove oldest entry
      const firstKey = this.memorySearchCache.keys().next().value;
      if (firstKey) {
        this.memorySearchCache.delete(firstKey);
      }
    }

    this.memorySearchCache.set(key, {
      results,
      timestamp: Date.now()
    });
  }

  /**
     * Performance optimization: Cleanup expired caches
     */
  private cleanupExpiredCaches(): void {
    const now = Date.now();

    // Clean search cache
    for (const [key, cached] of this.memorySearchCache.entries()) {
      if (now - cached.timestamp > this.searchCacheExpiry) {
        this.memorySearchCache.delete(key);
      }
    }
  }
}

// Export singleton instance
export const memoryManager = new MemoryManager();
