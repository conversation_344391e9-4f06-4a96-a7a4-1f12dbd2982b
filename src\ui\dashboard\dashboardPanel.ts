// src/ui/dashboardPanel.ts
import * as vscode from 'vscode';

export class DashboardPanel {
  public static currentPanel: DashboardPanel | undefined;
  private readonly _panel: vscode.WebviewPanel;
  private readonly _extensionUri: vscode.Uri;

  public static readonly viewType = 'codessa.dashboard';

  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
    this._panel = panel;
    this._extensionUri = extensionUri;
    this._panel.webview.html = this._getHtmlForWebview();
  }

  public static createOrShow(extensionUri: vscode.Uri) {
    const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined;
    if (DashboardPanel.currentPanel) {
      DashboardPanel.currentPanel._panel.reveal(column);
      return;
    }
    const panel = vscode.window.createWebviewPanel(
      DashboardPanel.viewType,
      'Codessa Dashboard',
      column || vscode.ViewColumn.One,
      { enableScripts: true }
    );
    DashboardPanel.currentPanel = new DashboardPanel(panel, extensionUri);
  }

  private _getHtmlForWebview(): string {
    // Basic HTML content; replace with your dashboard UI
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Codessa Dashboard</title>
</head>
<body>
  <h1>Codessa Dashboard</h1>
  <p>Welcome to the Codessa extension!</p>
</body>
</html>`;
  }
}
