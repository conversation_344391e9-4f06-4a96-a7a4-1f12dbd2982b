#!/usr/bin/env node

/**
 * Workflow Benchmark Runner
 * 
 * This script runs the workflow benchmark with a mocked VS Code environment.
 */

import * as path from 'path';
import * as fs from 'fs';
import { WorkflowBenchmark } from './workflowBenchmark';
// Import workflow-related classes with correct relative paths
import { Workflow } from '../../src/workflows/workflow';
import { WorkflowStep } from '../../src/workflows/workflowStep';
import { WorkflowContext } from '../../src/workflows/workflowContext';
import { benchmarkConfig } from './benchmark.config';

// Define any missing types
interface BenchmarkResult {
  iterations: number;
  duration: number;
  successRate: number;
  steps: Array<{
    id: string;
    name: string;
    duration: number;
    success: boolean;
  }>;
}

// Mock the VS Code API
const vscode = require('../../mocks/vscode.mock');

// Mock the global process object if needed
(global as any).process = {
  ...process,
  env: {
    ...process.env,
    NODE_ENV: 'test',
    VSCODE_DEBUG_MODE: 'false'
  }
};

// Mock the global console for better test output
const originalConsole = { ...console };
(global as any).console = {
  ...console,
  log: (...args: any[]) => originalConsole.log('[LOG]', ...args),
  error: (...args: any[]) => originalConsole.error('[ERROR]', ...args),
  warn: (...args: any[]) => originalConsole.warn('[WARN]', ...args),
  debug: (...args: any[]) => originalConsole.debug('[DEBUG]', ...args),
  info: (...args: any[]) => originalConsole.info('[INFO]', ...args)
};

// Mock the global require function to handle vscode module
const originalRequire = (global as any).require;
(global as any).require = function(modulePath: string) {
  if (modulePath === 'vscode') {
    return vscode;
  }
  return originalRequire(modulePath);
};

// Simple workflow step implementation
class TestStep extends WorkflowStep {
  constructor(id: string, name: string) {
    super({
      id,
      name,
      description: `Test step: ${name}`,
      execute: async (context: any) => {
        // Simulate work
        await new Promise(resolve => setTimeout(resolve, 10));
        return { 
          success: true, 
          output: `Completed ${name}`,
          nextStepId: undefined,
          metadata: {}
        };
      }
    });
  }
}

// Create a simple test workflow
function createTestWorkflow() {
  const workflow = new Workflow('test-workflow', 'Test Workflow');
  
  // Define workflow steps
  // Create steps
  const step1 = new TestStep('step1', 'First Step');
  const step2 = new TestStep('step2', 'Second Step');
  const step3 = new TestStep('step3', 'Final Step');
  
  // Add steps to workflow
  workflow.addStep(step1);
  workflow.addStep(step2);
  workflow.addStep(step3);
  
  // Set up transitions
  workflow.addTransition('step1', 'step2');
  workflow.addTransition('step2', 'step3');
  workflow.setInitialStep('step1');
  
  return workflow;
}

// Main function to run the benchmark
async function runBenchmark() {
  console.log('Starting workflow benchmark...');
  
  try {
    // Create benchmark instance
    const benchmark = new WorkflowBenchmark();
    
    // Run the benchmark
    await benchmark.run();
    
    console.log('\nBenchmark completed!');
  } catch (error) {
    console.error('Error running benchmark:', error);
    process.exit(1);
  }
}

// Run the benchmark
runBenchmark().catch(error => {
  console.error('Unhandled error in benchmark:', error);
  process.exit(1);
});
