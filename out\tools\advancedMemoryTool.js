"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryVisualizationTool = exports.MemoryTagTool = exports.MemoryUpdateTool = exports.MemorySearchTool = void 0;
const zod_1 = require("zod");
class MemorySearchTool {
    id = 'memorySearch';
    name = 'Memory Search';
    description = 'Search memories by keyword or tag.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        query: zod_1.z.string().describe('Keyword or tag to search.')
    });
    inputSchema = {
        type: 'object',
        properties: {
            query: { type: 'string', description: 'Keyword or tag to search.' }
        },
        required: ['query']
    };
    memoryManager;
    constructor(memoryManager) { this.memoryManager = memoryManager; }
    async execute(actionName, input, _context) {
        const query = input.query;
        if (!query) {
            return { success: false, error: '\'query\' is required.', toolId: this.id, actionName };
        }
        const entries = await this.memoryManager.getMemories();
        const results = entries.filter(e => (e.content && e.content.includes(query)) ||
            (e.metadata?.tags && e.metadata.tags.includes(query)));
        return { success: true, output: results, toolId: this.id, actionName };
    }
}
exports.MemorySearchTool = MemorySearchTool;
class MemoryUpdateTool {
    id = 'memoryUpdate';
    name = 'Memory Update';
    description = 'Update a memory entry by ID.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        id: zod_1.z.string().describe('Memory ID to update.'),
        content: zod_1.z.string().describe('New content.')
    });
    inputSchema = {
        type: 'object',
        properties: {
            id: { type: 'string', description: 'Memory ID to update.' },
            content: { type: 'string', description: 'New content.' }
        },
        required: ['id', 'content']
    };
    memoryManager;
    constructor(memoryManager) { this.memoryManager = memoryManager; }
    async execute(actionName, input, _context) {
        const id = input.id;
        const content = input.content;
        if (!id || !content) {
            return { success: false, error: '\'id\' and \'content\' are required.', toolId: this.id, actionName };
        }
        const entries = await this.memoryManager.getMemories();
        const entry = entries.find(e => e.id === id);
        if (!entry) {
            return { success: false, output: 'Memory entry not found', toolId: this.id, actionName };
        }
        entry.content = content;
        if (this.memoryManager.memoryProvider && this.memoryManager.memoryProvider.updateMemory) {
            const ok = await this.memoryManager.memoryProvider.updateMemory(id, entry);
            return {
                success: ok,
                output: ok ? 'Updated' : 'Not found',
                toolId: this.id,
                actionName
            };
        }
        return {
            success: true,
            output: 'Updated (in-memory only)',
            toolId: this.id,
            actionName
        };
    }
}
exports.MemoryUpdateTool = MemoryUpdateTool;
class MemoryTagTool {
    id = 'memoryTag';
    name = 'Memory Tag';
    description = 'Add or remove tags from a memory entry.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        id: zod_1.z.string().describe('Memory ID.'),
        tags: zod_1.z.array(zod_1.z.string()).describe('Tags to add/remove.'),
        mode: zod_1.z.enum(['add', 'remove']).describe('Add or remove tags.')
    });
    inputSchema = {
        type: 'object',
        properties: {
            id: { type: 'string', description: 'Memory ID.' },
            tags: { type: 'array', items: { type: 'string' }, description: 'Tags to add/remove.' },
            mode: { type: 'string', enum: ['add', 'remove'], description: 'Add or remove tags.' }
        },
        required: ['id', 'tags', 'mode']
    };
    memoryManager;
    constructor(memoryManager) { this.memoryManager = memoryManager; }
    async execute(actionName, input, _context) {
        const id = input.id;
        const tags = input.tags;
        const mode = input.mode;
        if (!id || !tags || !mode) {
            return { success: false, error: '\'id\', \'tags\', and \'mode\' are required.', toolId: this.id, actionName };
        }
        const entries = await this.memoryManager.getMemories();
        const entry = entries.find(e => e.id === id);
        if (!entry) {
            return { success: false, output: 'Memory entry not found', toolId: this.id, actionName };
        }
        if (!entry.metadata.tags) {
            entry.metadata.tags = [];
        }
        if (mode === 'add') {
            for (const tag of tags) {
                if (!entry.metadata.tags.includes(tag)) {
                    entry.metadata.tags.push(tag);
                }
            }
        }
        else if (mode === 'remove') {
            entry.metadata.tags = entry.metadata.tags.filter((t) => !tags.includes(t));
        }
        else {
            return { success: false, error: 'Invalid mode. Must be "add" or "remove".', toolId: this.id, actionName };
        }
        if (this.memoryManager.memoryProvider && this.memoryManager.memoryProvider.updateMemory) {
            const ok = await this.memoryManager.memoryProvider.updateMemory(id, entry);
            return {
                success: ok,
                output: ok ? `Tags ${mode}ed` : 'Not found',
                toolId: this.id,
                actionName
            };
        }
        return {
            success: true,
            output: `Tags ${mode}ed (in-memory only)`,
            toolId: this.id,
            actionName
        };
    }
}
exports.MemoryTagTool = MemoryTagTool;
class MemoryVisualizationTool {
    id = 'memoryViz';
    name = 'Memory Visualization';
    description = 'Visualize memory graph or clusters.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({});
    inputSchema = {
        type: 'object',
        properties: {},
        required: []
    };
    memoryManager;
    constructor(memoryManager) { this.memoryManager = memoryManager; }
    async execute(actionName, _input, _context) {
        try {
            const entries = await this.memoryManager.getMemories();
            if (!entries || entries.length === 0) {
                return {
                    success: true,
                    output: { message: 'No memory entries found to visualize', clusters: {}, connections: [] },
                    toolId: this.id,
                    actionName
                };
            }
            // Cluster memories by tags
            const clusters = {};
            const untagged = [];
            // Track connections between memories (memories that share tags)
            const connections = [];
            // First pass: organize by tags
            for (const entry of entries) {
                if (entry.metadata?.tags && entry.metadata.tags.length > 0) {
                    for (const tag of entry.metadata.tags) {
                        if (!clusters[tag]) {
                            clusters[tag] = [];
                        }
                        clusters[tag].push({
                            id: entry.id,
                            content: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),
                            tags: entry.metadata.tags,
                            created: entry.metadata.timestamp || 'unknown'
                        });
                    }
                }
                else {
                    untagged.push({
                        id: entry.id,
                        content: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),
                        created: entry.metadata.timestamp || 'unknown'
                    });
                }
            }
            // Second pass: find connections between memories
            const processedPairs = new Set();
            for (const entry of entries) {
                if (!entry.metadata?.tags || entry.metadata.tags.length === 0)
                    continue;
                for (const otherEntry of entries) {
                    if (entry.id === otherEntry.id)
                        continue;
                    if (!otherEntry.metadata?.tags || otherEntry.metadata.tags.length === 0)
                        continue;
                    // Create a unique key for this pair to avoid duplicates
                    const pairKey = [entry.id, otherEntry.id].sort().join('-');
                    if (processedPairs.has(pairKey))
                        continue;
                    processedPairs.add(pairKey);
                    // Calculate connection strength based on shared tags
                    const entryTags = new Set(entry.metadata.tags);
                    const otherTags = new Set(otherEntry.metadata.tags);
                    // Convert Set to Array before using filter to avoid downlevelIteration issues
                    const entryTagsArray = Array.from(entryTags);
                    const sharedTags = entryTagsArray.filter(tag => otherTags.has(tag));
                    if (sharedTags.length > 0) {
                        connections.push({
                            source: entry.id,
                            target: otherEntry.id,
                            strength: sharedTags.length
                        });
                    }
                }
            }
            // Add untagged memories to the result if there are any
            if (untagged.length > 0) {
                clusters['untagged'] = untagged;
            }
            return {
                success: true,
                output: {
                    totalMemories: entries.length,
                    clusters,
                    connections,
                    clusterCount: Object.keys(clusters).length,
                    connectionCount: connections.length
                },
                toolId: this.id,
                actionName
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Memory visualization failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
}
exports.MemoryVisualizationTool = MemoryVisualizationTool;
//# sourceMappingURL=advancedMemoryTool.js.map