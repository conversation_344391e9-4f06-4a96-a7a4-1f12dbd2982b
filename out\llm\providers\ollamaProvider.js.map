{"version": 3, "file": "ollamaProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/ollamaProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B,uDAAoD;AAIpD,yCAAsC;AAQtC,4EAA4E;AAC5E,mEAAsE;AAEtE,MAAa,cAAe,SAAQ,iCAAe;IACxC,UAAU,GAAG,QAAQ,CAAC;IACtB,WAAW,GAAG,QAAQ,CAAC;IACvB,WAAW,GAAG,mCAAmC,CAAC;IAClD,OAAO,GAAG,mBAAmB,CAAC;IAC9B,cAAc,GAAG,KAAK,CAAC;IACvB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,wBAAwB,CAAC;IAC3C,YAAY,GAAG,QAAQ,CAAC;IAEzB,MAAM,GAAyB,IAAI,CAAC;IACpC,OAAO,GAAG,EAAE,CAAC;IAErB,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBACrE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAE/D,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC;gBACH,2DAA2D;gBAC3D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;oBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,OAAO,EAAE,KAAK,EAAE,qBAAqB;oBACrC,OAAO,EAAE;wBACP,cAAc,EAAE,kBAAkB;qBACnC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,2CAA2C,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;gBACvE,0CAA0C;gBAC1C,0BAA0B;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBAC3D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACrB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAEhD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,wCAAwC;YAC/E,eAAM,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,+BAA+B;aACzC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,+BAA+B;iBACzC,CAAC;YACJ,CAAC;YACD,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAEtC,iDAAiD;YACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACvC,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;YAEvD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,UAAU,OAAO,gEAAgE,OAAO,IAAI;iBACtG,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,8CAA8C,IAAI,CAAC,OAAO,wBAAwB,OAAO,IAAI;aACvG,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,8CAA8C;YAC9C,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAEtD,iFAAiF;YACjF,IAAI,OAAO,KAAK,cAAc,EAAE,CAAC;gBAC/B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,yCAAyC,IAAI,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;iBAC9H,CAAC;YACJ,CAAC;YAED,4DAA4D;YAC5D,MAAM,QAAQ,GAAG,yCAAyC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEzE,IAAI,CAAC,IAAA,mCAAmB,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACnC,6CAA6C;gBAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC5B,GAAG,QAAQ,sBAAsB,EACjC,cAAc,EAAE,eAAe,CAChC,CAAC,IAAI,CAAC,CAAC,SAA6B,EAAE,EAAE;oBACvC,IAAI,SAAS,KAAK,cAAc,EAAE,CAAC;wBACjC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;oBAClE,CAAC;yBAAM,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;wBACzC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;oBACjE,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACvE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yCAAyC,IAAI,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC9H,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;SAIK;IACG,2BAA2B,CAAC,MAAc,EAAE,aAAiC,EAAE,KAA0B;QAC/G,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,2BAA2B;QAC3B,IAAI,gBAAgB,GAAG,oBAAoB,CAAC;QAC5C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,mCAAmC;YACnC,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,OAAQ,IAAY,CAAC,aAAa,KAAK,UAAU,EAAE,CAAC;gBAC5E,MAAM,UAAU,GAAI,IAAY,CAAC,aAAa,EAAE,CAAC;gBACjD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;oBACnC,gBAAgB,IAAI,KAAK,IAAI,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,KAAK,SAAS,CAAC,WAAW,IAAI,CAAC;oBAC/E,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;wBAC1B,gBAAgB,IAAI,gBAAgB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC;oBAChF,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,eAAe;gBACf,gBAAgB,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC;gBAC1D,IAAK,IAAY,CAAC,kBAAkB,EAAE,KAAK,EAAE,CAAC;oBAC5C,gBAAgB,IAAI,gBAAgB,IAAI,CAAC,SAAS,CAAE,IAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC;gBACjG,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,MAAM,gBAAgB,GAAG;EAC3B,gBAAgB;;;;;;;;;;;;;;;;;;;;;wKAqBsJ,CAAC;QAErK,8EAA8E;QAC9E,OAAO,GAAG,MAAM,OAAO,gBAAgB,EAAE,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,qDAAqD,EAAE,CAAC;QACvF,CAAC;QAED,kDAAkD;QAClD,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,qCAAqC;QAEnE,wBAAwB;QACxB,IAAI,QAAQ,GAAU,EAAE,CAAC;QAEzB,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,mCAAmC;YACnC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBAClC,8DAA8D;gBAC9D,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1D,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,6DAA6D;YAC7D,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;YAC/B,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC5B,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAC3F,CAAC;YAED,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,mDAAmD;QACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;YAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;QACpE,CAAC;QAED,uBAAuB;QACvB,MAAM,WAAW,GAAG;YAClB,KAAK,EAAE,MAAM,CAAC,OAAO;YACrB,QAAQ;YACR,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBACP,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,WAAW,EAAE,MAAM,CAAC,SAAS,EAAE,oCAAoC;gBACnE,IAAI,EAAE,MAAM,CAAC,aAAa;gBAC1B,GAAG,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;aAC1B;SACF,CAAC;QAEF,IAAI,YAA2C,CAAC;QAChD,IAAI,iBAAiB,EAAE,CAAC;YACtB,YAAY,GAAI,KAAa,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACnD,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;gBAC7C,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBACjD,YAAY,EAAE,MAAM,CAAC,4BAA4B,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC,CAAC;YAEhG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;YACjE,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE;gBAC7D,WAAW,EAAE,YAAY,EAAE,KAAK;aACjC,CAAC,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,6BAA6B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;YAEhG,sDAAsD;YACtD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;YACvC,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;YACvC,IAAI,eAA4C,CAAC;YAEjD,qCAAqC;YACrC,IAAI,CAAC;gBACH,qCAAqC;gBACrC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACnE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBAExC,4BAA4B;oBAC5B,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;wBAC1B,eAAe,GAAG;4BAChB,MAAM,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI;4BAClC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,SAAS;yBACtC,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,MAAM,EAAE,CAAC;gBAChB,mDAAmD;gBACnD,eAAM,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;YAC9E,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,MAAM,EAAE,yCAAyC;gBAC/D,KAAK,EAAE;oBACL,oCAAoC;oBACpC,YAAY,EAAE,QAAQ,CAAC,IAAI,EAAE,iBAAiB;oBAC9C,gBAAgB,EAAE,QAAQ,CAAC,IAAI,EAAE,UAAU;iBAC5C;gBACD,QAAQ,EAAE,eAAe;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAK,KAAa,CAAC,QAAQ,IAAK,KAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9D,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,mBAAmB,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC;YAC7E,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,YAAY,GAAG,4BAA4B,CAAC;YAEhD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,qBAAqB,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,aAAa,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;YACvH,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzB,YAAY,GAAG,4CAA4C,IAAI,CAAC,OAAO,sBAAsB,CAAC;YAChG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,kBAAkB;QACtB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAClE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC,CAAC;YAC5E,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAEjD,wEAAwE;YACxE,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,EAAE,CAAC;YAC3C,eAAM,CAAC,IAAI,CAAC,uBAAuB,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;YAErE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;gBAC7B,EAAE,EAAE,CAAC,CAAC,IAAI;gBACV,IAAI,EAAE,CAAC,CAAC,IAAI;gBACZ,WAAW,EAAE,SAAS,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;gBACpD,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,cAAc,IAAI,IAAI;aACjD,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAe,EAAE,CAAe,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,UAAU,CAAC,KAAa;QAC9B,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;YACjB,OAAO,GAAG,KAAK,IAAI,CAAC;QACtB,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YAC/B,OAAO,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3C,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YACtC,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,OAAY;QACpC,IAAI,CAAC;YACH,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAElC,qDAAqD;YACrD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;YAE/D,+CAA+C;YAC/C,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,8BAA8B;YAC9B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC/C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,kCAAkC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,2DAA2D,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACzF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC,CAAC,gDAAgD;QAC/D,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,iEAAiE;gBAC9E,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,wDAAwD;gBACrE,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AAvbD,wCAubC", "sourcesContent": ["import * as vscode from 'vscode';\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\nimport type { AxiosInstance } from 'axios';\nimport { BaseLLMProvider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo, ToolCallRequest } from '../llmProvider';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { logger } from '../../logger';\n\n// Define missing types\ninterface CancelTokenSource {\n    token: any;\n    cancel(_message?: string): void;\n}\n\n// Import the shouldSuppressError function directly to avoid require() calls\nimport { shouldSuppressError } from '../../ui/feedback/notifications';\n\nexport class OllamaProvider extends BaseLLMProvider {\n  readonly providerId = 'ollama';\n  readonly displayName = 'Ollama';\n  readonly description = 'Run large language models locally';\n  readonly website = 'https://ollama.ai';\n  readonly requiresApiKey = false;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'http://localhost:11434';\n  readonly defaultModel = 'llama3';\n\n  private client: AxiosInstance | null = null;\n  private baseUrl = '';\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.baseUrl = this.defaultEndpoint;\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        logger.info('Ollama configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (this.baseUrl) {\n      try {\n        // Just store the base URL and use it directly in API calls\n        this.client = axios.create({\n          baseURL: this.baseUrl,\n          timeout: 60000, // 60 seconds timeout\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        logger.info(`Ollama client initialized for base URL: ${this.baseUrl}`);\n        // Optionally check if Ollama is reachable\n        // this.checkConnection();\n      } catch (error) {\n        logger.error('Failed to initialize Ollama client:', error);\n        this.client = null;\n      }\n    } else {\n      logger.warn('Ollama base URL not configured.');\n      this.client = null;\n    }\n  }\n\n  async checkConnection(): Promise<boolean> {\n    if (!this.baseUrl || !this.client) return false;\n\n    try {\n      await this.client.get('/api/version'); // Check if the Ollama API is responding\n      logger.info(`Ollama connection successful at ${this.baseUrl}`);\n      return true;\n    } catch (error) {\n      logger.error(`Failed to connect to Ollama at ${this.baseUrl}:`, error);\n      return false;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.baseUrl;\n  }\n\n  /**\n     * Test connection to Ollama\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.baseUrl) {\n      return {\n        success: false,\n        message: 'Ollama client not initialized'\n      };\n    }\n\n    try {\n      // First check if we can connect to the Ollama server\n      if (!this.client) {\n        return {\n          success: false,\n          message: 'Ollama client not initialized'\n        };\n      }\n      await this.client.get('/api/version');\n\n      // Then check if the specified model is available\n      const models = await this.listModels();\n      const modelExists = models.some(m => m.id === modelId);\n\n      if (!modelExists) {\n        return {\n          success: false,\n          message: `Model '${modelId}' not found. You may need to pull it first with 'ollama pull ${modelId}'.`\n        };\n      }\n\n      return {\n        success: true,\n        message: `Successfully connected to Ollama server at ${this.baseUrl} and verified model '${modelId}'.`\n      };\n    } catch (error) {\n      // Log the error but don't show it to the user\n      logger.debug('Ollama connection test failed:', error);\n            \n      // If this is a silent check, just return the error without showing notifications\n      if (modelId === 'silent-check') {\n        return {\n          success: false,\n          message: `Failed to connect to Ollama server at ${this.baseUrl}: ${error instanceof Error ? error.message : 'Unknown error'}`\n        };\n      }\n            \n      // For explicit checks, use the shouldSuppressError function\n      const errorMsg = `Failed to connect to Ollama server at ${this.baseUrl}`;\n            \n      if (!shouldSuppressError(errorMsg)) {\n        // Only show the error if it's not suppressed\n        vscode.window.showErrorMessage(\n          `${errorMsg}. Is Ollama running?`,\n          'Check Ollama', 'Open Settings'\n        ).then((selection: string | undefined) => {\n          if (selection === 'Check Ollama') {\n            vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/'));\n          } else if (selection === 'Open Settings') {\n            vscode.commands.executeCommand('codessa.openProviderSettings');\n          }\n        });\n      } else {\n        logger.debug('Suppressed Ollama connection test error notification');\n      }\n            \n      return {\n        success: false,\n        message: `Failed to connect to Ollama server at ${this.baseUrl}: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * Add tool instructions to the prompt for Ollama\n     * Since Ollama doesn't have a native function calling mechanism,\n     * we'll instruct it to output tool calls in a specific JSON format.\n     */\n  private addToolInstructionsToPrompt(prompt: string, _systemPrompt: string | undefined, tools?: Map<string, ITool>): string {\n    if (!tools || tools.size === 0) {\n      return prompt;\n    }\n\n    // Format tool descriptions\n    let toolDescriptions = 'Available tools:\\n';\n    tools.forEach(tool => {\n      // Handle file tool with subactions\n      if (tool.id === 'file' && typeof (tool as any).getSubActions === 'function') {\n        const subActions = (tool as any).getSubActions();\n        for (const subAction of subActions) {\n          toolDescriptions += `- ${tool.id}.${subAction.id}: ${subAction.description}\\n`;\n          if (subAction.inputSchema) {\n            toolDescriptions += `  Arguments: ${JSON.stringify(subAction.inputSchema)}\\n`;\n          }\n        }\n      } else {\n        // Regular tool\n        toolDescriptions += `- ${tool.id}: ${tool.description}\\n`;\n        if ((tool as any).singleActionSchema?.shape) {\n          toolDescriptions += `  Arguments: ${JSON.stringify((tool as any).singleActionSchema.shape)}\\n`;\n        }\n      }\n    });\n\n    // Tool usage instructions\n    const toolInstructions = `\n${toolDescriptions}\n\nTo use a tool, output a JSON object EXACTLY in this format (no other text before or after):\n{\n  \"tool_call\": {\n    \"name\": \"tool_id.action_name\", // e.g., \"file.readFile\", \"docs.search\"\n    \"arguments\": { // Arguments specific to the tool action\n      \"arg1\": \"value1\",\n      \"arg2\": \"value2\"\n      // ...\n    }\n  }\n}\n\nAfter the tool executes, I will provide you with the result, and you can continue your task or call another tool.\n\nWhen you have the final answer and don't need to use any more tools, output a JSON object EXACTLY in this format:\n{\n  \"final_answer\": \"Your complete final response here.\"\n}\n\nThink step-by-step. Analyze the request, decide if a tool is needed, call the tool if necessary, analyze the result, and repeat until you can provide the final answer.`;\n\n    // For Ollama, typically append to the prompt rather than to the system prompt\n    return `${prompt}\\n\\n${toolInstructions}`;\n  }\n\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.baseUrl) {\n      return { content: '', error: 'Ollama provider not configured (Base URL missing?).' };\n    }\n\n    // Determine whether to use chat or completion API\n    const endpoint = '/api/chat'; // Use chat API for better formatting\n\n    // Prepare chat messages\n    let messages: any[] = [];\n\n    if (params.history && params.history.length > 0) {\n      // Convert history to Ollama format\n      messages = params.history.map(msg => {\n        // Ollama doesn't support 'tool' role, so convert to assistant\n        const role = msg.role === 'tool' ? 'assistant' : msg.role;\n        return { role, content: msg.content };\n      });\n    } else {\n      if (params.systemPrompt) {\n        messages.push({ role: 'system', content: params.systemPrompt });\n      }\n\n      // Modify prompt with tool instructions if tools are provided\n      let userPrompt = params.prompt;\n      if (tools && tools.size > 0) {\n        userPrompt = this.addToolInstructionsToPrompt(params.prompt, params.systemPrompt, tools);\n      }\n\n      messages.push({ role: 'user', content: userPrompt });\n    }\n\n    // Check for cancellation before making the request\n    if (cancellationToken?.isCancellationRequested) {\n      return { content: '', error: 'Request cancelled before sending' };\n    }\n\n    // Prepare request data\n    const requestData = {\n      model: params.modelId,\n      messages,\n      stream: false,\n      options: {\n        temperature: params.temperature ?? 0.7,\n        num_predict: params.maxTokens, // Ollama's equivalent to max_tokens\n        stop: params.stopSequences,\n        ...(params.options ?? {})\n      }\n    };\n\n    let cancelSource: CancelTokenSource | undefined;\n    if (cancellationToken) {\n      cancelSource = (axios as any).CancelToken.source();\n      cancellationToken.onCancellationRequested(() => {\n        logger.warn('Ollama request cancelled by user.');\n        cancelSource?.cancel('Request cancelled by user.');\n      });\n    }\n\n    try {\n      logger.debug(`Sending request to Ollama model ${params.modelId} at ${this.baseUrl}${endpoint}`);\n\n      if (!this.client) {\n        return { content: '', error: 'Ollama client not initialized' };\n      }\n\n      const response = await this.client.post(endpoint, requestData, {\n        cancelToken: cancelSource?.token,\n      });\n\n      logger.debug(`Ollama response received: ${JSON.stringify(response.data).substring(0, 100)}...`);\n\n      // Parse the content to see if it contains a tool call\n      const message = response.data?.message;\n      const content = message?.content ?? '';\n      let toolCallRequest: ToolCallRequest | undefined;\n\n      // Try to parse JSON from the content\n      try {\n        // Check if content is JSON formatted\n        if (content.trim().startsWith('{') && content.trim().endsWith('}')) {\n          const jsonContent = JSON.parse(content);\n\n          // Check if it's a tool call\n          if (jsonContent.tool_call) {\n            toolCallRequest = {\n              toolId: jsonContent.tool_call.name,\n              args: jsonContent.tool_call.arguments\n            };\n          }\n        }\n      } catch (_error) {\n        // Not JSON or invalid, just use as regular content\n        logger.debug('Ollama response is not a valid JSON, treating as plain text');\n      }\n\n      return {\n        content,\n        finishReason: 'stop', // Ollama doesn't provide a finish reason\n        usage: {\n          // Ollama might provide token counts\n          promptTokens: response.data?.prompt_eval_count,\n          completionTokens: response.data?.eval_count,\n        },\n        toolCall: toolCallRequest\n      };\n    } catch (error: any) {\n      if ((axios as any).isCancel && (axios as any).isCancel(error)) {\n        return { content: '', error: 'Request cancelled', finishReason: 'cancel' };\n      }\n\n      logger.error('Error calling Ollama API:', error);\n      let errorMessage = 'Failed to call Ollama API.';\n\n      if (error.response) {\n        errorMessage = `Ollama API Error: ${error.response.data?.error || error.message} (Status: ${error.response.status})`;\n      } else if (error.request) {\n        errorMessage = `Ollama connection error: Could not reach ${this.baseUrl}. Is Ollama running?`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return { content: '', error: errorMessage, finishReason: 'error' };\n    }\n  }\n\n  /**\n     * Backward compatibility method for getAvailableModels\n     * @deprecated Use listModels instead\n     */\n  async getAvailableModels(): Promise<string[]> {\n    const models = await this.listModels();\n    return models.map(model => model.id);\n  }\n\n  /**\n     * List available models from Ollama\n     */\n  public async listModels(): Promise<LLMModelInfo[]> {\n    if (!this.baseUrl) {\n      logger.warn('Cannot fetch Ollama models, client not configured.');\n      return [];\n    }\n\n    const endpoint = '/api/tags';\n\n    try {\n      logger.debug(`Fetching Ollama models list from ${this.baseUrl}${endpoint}`);\n      if (!this.client) {\n        return [];\n      }\n      const response = await this.client.get(endpoint);\n\n      // Ollama response format: { models: [{ name: \"model:tag\", ... }, ...] }\n      const models = response.data?.models || [];\n      logger.info(`Provider ollama has ${models.length} models available`);\n\n      return models.map((m: any) => ({\n        id: m.name,\n        name: m.name,\n        description: `Size: ${this.formatSize(m.size || 0)}`,\n        contextWindow: m.details?.context_length || 4096\n      })).sort((a: LLMModelInfo, b: LLMModelInfo) => a.id.localeCompare(b.id));\n    } catch (error) {\n      logger.error('Failed to fetch Ollama models:', error);\n      return [];\n    }\n  }\n\n  /**\n     * Format file size in bytes to a human-readable string\n     */\n  private formatSize(bytes: number): string {\n    if (bytes < 1024) {\n      return `${bytes} B`;\n    } else if (bytes < 1024 * 1024) {\n      return `${(bytes / 1024).toFixed(2)} KB`;\n    } else if (bytes < 1024 * 1024 * 1024) {\n      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;\n    } else {\n      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;\n    }\n  }\n\n  /**\n     * Update the provider configuration\n     */\n  public async updateConfig(_config: any): Promise<void> {\n    try {\n      await super.updateConfig(_config);\n\n      // Update baseUrl from config or fall back to default\n      this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n      // Initialize the client with new configuration\n      this.initializeClient();\n\n      // Verify the connection works\n      const connected = await this.checkConnection();\n      if (!connected) {\n        throw new Error(`Failed to connect to Ollama at ${this.baseUrl}`);\n      }\n\n      logger.info(`Successfully updated Ollama configuration with endpoint ${this.baseUrl}`);\n    } catch (error) {\n      logger.error('Failed to update Ollama configuration:', error);\n      throw error; // Re-throw to let calling code handle the error\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The URL of your Ollama server (default: http://localhost:11434)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use (e.g., llama3, mistral, etc.)',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n\n\n"]}