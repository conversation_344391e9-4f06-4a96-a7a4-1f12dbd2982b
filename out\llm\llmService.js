"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.llmService = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../logger");
const config_1 = require("../config");
const providerManager_1 = require("./providerManager");
const notifications_1 = require("../ui/feedback/notifications");
// Import all providers
// Standard API providers
const openaiProvider_1 = require("./providers/openaiProvider");
const anthropicProvider_1 = require("./providers/anthropicProvider");
const googleAIProvider_1 = require("./providers/googleAIProvider");
const mistralAIProvider_1 = require("./providers/mistralAIProvider");
const cohereProvider_1 = require("./providers/cohereProvider");
const deepseekProvider_1 = require("./providers/deepseekProvider");
// Local and self-hosted providers
const ollamaProvider_1 = require("./providers/ollamaProvider");
const lmstudioProvider_1 = require("./providers/lmstudioProvider");
// Aggregator providers
const openrouterProvider_1 = require("./providers/openrouterProvider");
const huggingfaceProvider_1 = require("./providers/huggingfaceProvider");
// Code-specific model providers
const starcoderProvider_1 = require("./providers/starcoderProvider");
const codeLlamaProvider_1 = require("./providers/codeLlamaProvider");
const replitProvider_1 = require("./providers/replitProvider");
const wizardCoderProvider_1 = require("./providers/wizardCoderProvider");
const xwinCoderProvider_1 = require("./providers/xwinCoderProvider");
const phiProvider_1 = require("./providers/phiProvider");
const yiCodeProvider_1 = require("./providers/yiCodeProvider");
const codeGemmaProvider_1 = require("./providers/codeGemmaProvider");
const santaCoderProvider_1 = require("./providers/santaCoderProvider");
const stableCodeProvider_1 = require("./providers/stableCodeProvider");
// Additional API providers
const perplexityProvider_1 = require("./providers/perplexityProvider");
/**
 * Service that manages LLM providers and model selection
 */
class LLMService {
    providers = new Map();
    _onProvidersChanged = new vscode.EventEmitter();
    onProvidersChanged = this._onProvidersChanged.event;
    context;
    // Helper function to check if a dependency is available
    isDependencyAvailable(packageName) {
        try {
            require.resolve(packageName);
            return true;
        }
        catch (error) {
            return false;
        }
    }
    constructor() {
        // Listen for configuration changes that might affect providers
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('codessa.llm')) {
                logger_1.logger.info('LLM configuration changed, re-initializing providers.');
                this.reinitializeProviders();
            }
        });
    }
    /**
       * Initialize the service with the extension context
       * This must be called before using any provider functionality
       */
    initialize(context) {
        this.context = context;
        logger_1.logger.info('Initializing LLM service...');
        // Initialize the provider manager
        providerManager_1.providerManager.getInstance(context);
        this.initializeProviders();
    }
    async initializeProviders() {
        if (!this.context) {
            logger_1.logger.error('LLMService not initialized with context');
            return;
        }
        // Clear existing providers
        this.providers.clear();
        // Get enabled providers from settings
        const config = vscode.workspace.getConfiguration('codessa.llm');
        let enabledProviders = config.get('enabledProviders') || ['ollama'];
        if (!Array.isArray(enabledProviders) || enabledProviders.length === 0) {
            logger_1.logger.warn('No providers enabled in settings. All LLM functionality will be disabled.');
            enabledProviders = [];
        }
        logger_1.logger.info(`Initializing LLM providers. Enabled providers: ${enabledProviders.length > 0 ? enabledProviders.join(', ') : 'none'}`);
        // Define provider factories
        const providerFactories = [
            // Standard API providers
            { id: 'openai', factory: () => new openaiProvider_1.OpenAIProvider(this.context) },
            { id: 'anthropic', factory: () => new anthropicProvider_1.AnthropicProvider(this.context) },
            { id: 'googleai', factory: () => new googleAIProvider_1.GoogleAIProvider(this.context) },
            { id: 'mistralai', factory: () => new mistralAIProvider_1.MistralAIProvider(this.context) },
            { id: 'cohere', factory: () => new cohereProvider_1.CohereProvider(this.context) },
            { id: 'deepseek', factory: () => new deepseekProvider_1.DeepSeekProvider(this.context) },
            // Local and self-hosted providers
            { id: 'ollama', factory: () => new ollamaProvider_1.OllamaProvider(this.context) },
            { id: 'lmstudio', factory: () => new lmstudioProvider_1.LMStudioProvider(this.context) },
            // Aggregator providers
            { id: 'openrouter', factory: () => new openrouterProvider_1.OpenRouterProvider(this.context) },
            { id: 'huggingface', factory: () => new huggingfaceProvider_1.HuggingFaceProvider(this.context) },
            // Code-specific model providers
            { id: 'starcoder', factory: () => new starcoderProvider_1.StarCoderProvider(this.context) },
            { id: 'codellama', factory: () => new codeLlamaProvider_1.CodeLlamaProvider(this.context) },
            { id: 'replit', factory: () => new replitProvider_1.ReplitProvider(this.context) },
            { id: 'wizardcoder', factory: () => new wizardCoderProvider_1.WizardCoderProvider(this.context) },
            { id: 'xwincoder', factory: () => new xwinCoderProvider_1.XwinCoderProvider(this.context) },
            { id: 'phi', factory: () => new phiProvider_1.PhiProvider(this.context) },
            { id: 'yicode', factory: () => new yiCodeProvider_1.YiCodeProvider(this.context) },
            { id: 'codegemma', factory: () => new codeGemmaProvider_1.CodeGemmaProvider(this.context) },
            { id: 'santacoder', factory: () => new santaCoderProvider_1.SantaCoderProvider(this.context) },
            { id: 'stablecode', factory: () => new stableCodeProvider_1.StableCodeProvider(this.context) },
            // Additional API providers
            { id: 'perplexity', factory: () => new perplexityProvider_1.PerplexityAIProvider(this.context) }
        ];
        // Track initialization errors
        const initializationErrors = [];
        const skippedProviders = [];
        // Register only enabled providers
        for (const { id, factory } of providerFactories) {
            if (!enabledProviders.includes(id)) {
                logger_1.logger.debug(`Skipping provider ${id} (not enabled in settings)`);
                skippedProviders.push(id);
                continue;
            }
            logger_1.logger.info(`Initializing provider: ${id}`);
            try {
                // Double-check that the provider is still enabled before creating it
                // This prevents race conditions where settings might change during initialization
                const currentConfig = vscode.workspace.getConfiguration('codessa.llm');
                const currentEnabledProviders = currentConfig.get('enabledProviders') || ['ollama'];
                if (!currentEnabledProviders.includes(id)) {
                    logger_1.logger.debug(`Provider ${id} was disabled during initialization, skipping`);
                    skippedProviders.push(id);
                    continue;
                }
                // Check for required dependencies
                let missingDependency = false;
                if (id === 'anthropic' && !this.isDependencyAvailable('@anthropic-ai/sdk')) {
                    logger_1.logger.warn(`Provider ${id} requires @anthropic-ai/sdk which is not installed. Skipping.`);
                    missingDependency = true;
                }
                else if (id === 'openai' && !this.isDependencyAvailable('openai')) {
                    logger_1.logger.warn(`Provider ${id} requires openai which is not installed. Skipping.`);
                    missingDependency = true;
                }
                if (missingDependency) {
                    initializationErrors.push(`Provider ${id}: Required dependency not installed`);
                    continue;
                }
                const provider = factory();
                if (provider) {
                    const typedProvider = provider;
                    this.registerProvider(typedProvider);
                    logger_1.logger.info(`Successfully registered provider: ${id}`);
                    // Only test connection if provider is configured
                    if (provider.isConfigured()) {
                        try {
                            // For local providers, use silent connection test to avoid error popups
                            if (id === 'lmstudio' || id === 'ollama') {
                                const result = await typedProvider.testConnection('silent-check');
                                if (result.success) {
                                    logger_1.logger.info(`Local provider ${id} is running and configured`);
                                    // Only try to list models if connection is successful
                                    try {
                                        const models = await typedProvider.listModels();
                                        logger_1.logger.info(`Provider ${id} has ${models.length} models available`);
                                    }
                                    catch (modelError) {
                                        logger_1.logger.debug(`Failed to list models for ${id}: ${modelError instanceof Error ? modelError.message : String(modelError)}`);
                                    }
                                }
                                else {
                                    logger_1.logger.debug(`Local provider ${id} connection test failed: ${result.message}`);
                                }
                            }
                            else {
                                // For API providers, try to get a model ID first
                                try {
                                    const models = await typedProvider.listModels();
                                    const modelId = typedProvider.defaultModel || models[0]?.id;
                                    if (!modelId)
                                        throw new Error('No model ID available');
                                    const result = await typedProvider.testConnection(modelId);
                                    if (result.success) {
                                        logger_1.logger.info(`API provider ${id} is configured with ${models.length} models`);
                                    }
                                    else {
                                        throw new Error(result.message);
                                    }
                                }
                                catch (modelError) {
                                    // If we can't list models, try with a default model
                                    logger_1.logger.debug('Failed to list models, trying default:', modelError);
                                    const modelId = typedProvider.defaultModel || 'default';
                                    const result = await typedProvider.testConnection(modelId);
                                    if (result.success) {
                                        logger_1.logger.info(`API provider ${id} is configured`);
                                    }
                                    else {
                                        throw new Error(result.message);
                                    }
                                }
                            }
                        }
                        catch (error) {
                            const errorMessage = error instanceof Error ? error.message : String(error);
                            logger_1.logger.warn(`Failed to initialize provider ${id}: ${errorMessage}`);
                            initializationErrors.push(`Provider ${id}: ${errorMessage}`);
                        }
                    }
                    else {
                        logger_1.logger.info(`Provider ${id} registered but not configured`);
                    }
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                logger_1.logger.error(`Failed to register provider ${id}: ${errorMessage}`);
                initializationErrors.push(`Provider ${id}: ${errorMessage}`);
            }
        }
        // Log summary of initialization
        const initializedCount = this.providers.size;
        const configuredCount = this.getConfiguredProviders().length;
        logger_1.logger.info(`Initialized ${initializedCount} LLM providers (${configuredCount} configured)`);
        if (skippedProviders.length > 0) {
            logger_1.logger.info(`Skipped ${skippedProviders.length} disabled providers: ${skippedProviders.join(', ')}`);
        }
        // If there were any initialization errors, log them
        if (initializationErrors.length > 0) {
            logger_1.logger.warn('Some providers failed to initialize:');
            initializationErrors.forEach(error => logger_1.logger.warn(`  - ${error}`));
        }
        // Notify listeners that providers have changed
        this._onProvidersChanged.fire();
        // If no providers were successfully initialized, log a warning but don't throw an error
        if (initializedCount === 0) {
            logger_1.logger.warn('No LLM providers were initialized. Users can enable providers in settings.');
            // Don't throw an error - this allows the extension to work without any LLM providers
        }
    }
    async reinitializeProviders() {
        await this.initializeProviders();
    }
    /**
       * Attempt to register a provider with error handling
       */
    async tryRegisterProvider(id, providerFactory) {
        try {
            const provider = providerFactory();
            // Only register if the provider initializes successfully
            if (provider) {
                this.registerProvider(provider);
                logger_1.logger.info(`Successfully registered provider: ${id}`);
                // Try to initialize models if provider is configured
                if (provider.isConfigured()) {
                    try {
                        // For local providers like LM Studio and Ollama, use a silent check
                        // to avoid showing error messages if they're not running
                        if (id === 'lmstudio' || id === 'ollama') {
                            // First try a silent connection test
                            const testResult = await provider.testConnection('silent-check');
                            if (testResult.success) {
                                const models = await provider.listModels();
                                logger_1.logger.info(`Provider ${id} has ${models.length} models available`);
                            }
                            else {
                                // Just log the error without showing notifications
                                logger_1.logger.debug(`Provider ${id} is not available: ${testResult.message}`);
                            }
                        }
                        else {
                            // For cloud providers, try to list models normally
                            const models = await provider.listModels();
                            logger_1.logger.info(`Provider ${id} has ${models.length} models available`);
                        }
                    }
                    catch (error) {
                        // Log the error but don't let it prevent the provider from being registered
                        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                        logger_1.logger.warn(`Failed to list models for provider ${id}: ${errorMessage}`);
                        // Check if we should suppress this error message
                        const warningMessage = `Failed to connect to ${id} provider. The provider is registered but may not work until the connection issue is resolved.`;
                        // Only show warnings for cloud providers that are explicitly configured
                        // Local providers (ollama, lmstudio) should never show errors during initialization
                        if (!(0, notifications_1.shouldSuppressError)(warningMessage) &&
                            id !== 'ollama' &&
                            id !== 'lmstudio' &&
                            provider.isConfigured()) {
                            // Only show warnings for cloud providers that should be available
                            // and only if error suppression is disabled and the provider is configured
                            // Import showWarningMessage to use error suppression
                            const { showWarningMessage } = require('../ui/notifications');
                            showWarningMessage(warningMessage);
                        }
                    }
                }
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            logger_1.logger.warn(`Failed to initialize ${id} provider: ${errorMessage}`);
            // Always register the provider even if initialization fails
            // This ensures the UI shows all available providers
            try {
                const provider = providerFactory();
                if (provider) {
                    this.registerProvider(provider);
                    logger_1.logger.info(`Registered provider ${id} despite initialization failure`);
                }
            }
            catch (e) {
                // If we can't even create the provider, just log and continue
                logger_1.logger.error(`Could not create provider ${id}: ${e instanceof Error ? e.message : String(e)}`);
            }
        }
    }
    /**
       * Registers a new LLM provider
       */
    registerProvider(provider) {
        if (this.providers.has(provider.providerId)) {
            logger_1.logger.warn(`Provider with ID '${provider.providerId}' is already registered. Overwriting.`);
        }
        this.providers.set(provider.providerId, provider);
        // Also register with the provider manager
        if (this.context) {
            providerManager_1.providerManager.getInstance(this.context).registerProvider(provider);
        }
    }
    /**
       * Gets a provider by ID
       */
    getProvider(providerId) {
        return this.providers.get(providerId);
    }
    /**
       * Gets the appropriate LLM provider based on the config
       */
    async getProviderForConfig(_config) {
        const provider = this.providers.get(_config.provider);
        if (!provider) {
            logger_1.logger.warn('LLM provider \'' + _config.provider + '\' not found.');
            return undefined;
        }
        if (!this.context) {
            logger_1.logger.error('LLMService not initialized with context');
            return undefined;
        }
        // Check if the provider is enabled (configured and not disabled)
        const manager = providerManager_1.providerManager.getInstance(this.context);
        if (!await manager.isProviderEnabled(provider.providerId)) {
            logger_1.logger.warn('LLM provider \'' + _config.provider + '\' is not enabled (not configured or explicitly disabled).');
            return undefined;
        }
        return provider;
    }
    /**
       * Gets the default provider based on settings
       */
    async getDefaultProvider() {
        if (!this.context) {
            logger_1.logger.error('LLMService not initialized with context');
            return undefined;
        }
        // Get default provider ID from settings
        const defaultProviderId = providerManager_1.providerManager.getInstance(this.context).getDefaultProviderId();
        const provider = this.providers.get(defaultProviderId);
        const manager = providerManager_1.providerManager.getInstance(this.context);
        // Check if the default provider is enabled (configured and not disabled)
        if (provider && await manager.isProviderEnabled(provider.providerId)) {
            return provider;
        }
        // If the default provider is not available, not configured, or disabled
        logger_1.logger.warn(`Default provider '${defaultProviderId}' not found, not configured, or disabled. Trying to find another provider.`);
        // Try to find an enabled provider
        const enabledProviders = await this.getEnabledProviders();
        if (enabledProviders.length > 0) {
            // Prioritize local providers (Ollama, LM Studio) if they're enabled
            const localProvider = enabledProviders.find(p => p.providerId === 'ollama' || p.providerId === 'lmstudio');
            if (localProvider) {
                return localProvider;
            }
            // Otherwise return the first enabled provider
            return enabledProviders[0];
        }
        // If no enabled providers, try any configured provider
        const configuredProviders = this.getConfiguredProviders();
        if (configuredProviders.length > 0) {
            return configuredProviders[0];
        }
        // If no providers are available at all, return undefined
        logger_1.logger.warn('No LLM providers are available. Users can enable providers in settings.');
        return undefined;
    }
    /**
       * Sets the default provider
       */
    async setDefaultProvider(providerId) {
        if (!this.context) {
            logger_1.logger.error('LLMService not initialized with context');
            return false;
        }
        const provider = this.providers.get(providerId);
        if (!provider) {
            logger_1.logger.warn(`Cannot set default provider: Provider '${providerId}' not found.`);
            return false;
        }
        try {
            const success = await providerManager_1.providerManager.getInstance(this.context).setDefaultProviderId(providerId);
            if (success) {
                logger_1.logger.info(`Set default provider to ${providerId}`);
                return true;
            }
            else {
                logger_1.logger.error(`Failed to set default provider to ${providerId}`);
                return false;
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to set default provider to ${providerId}:`, error);
            return false;
        }
    }
    /**
       * Gets all available LLM providers
       */
    getAllProviders() {
        return Array.from(this.providers.values());
    }
    /**
       * Gets all available configured providers
       */
    getConfiguredProviders() {
        return Array.from(this.providers.values())
            .filter(provider => provider.isConfigured());
    }
    /**
       * Gets all available enabled providers (configured and not disabled)
       */
    async getEnabledProviders() {
        if (!this.context) {
            logger_1.logger.error('LLMService not initialized with context');
            return [];
        }
        const enabledProviders = [];
        const manager = providerManager_1.providerManager.getInstance(this.context);
        for (const provider of this.providers.values()) {
            if (await manager.isProviderEnabled(provider.providerId)) {
                enabledProviders.push(provider);
            }
        }
        return enabledProviders;
    }
    /**
       * Lists all provider IDs
       */
    listProviderIds() {
        return Array.from(this.providers.keys());
    }
    /**
       * Gets the default model configuration from settings
       */
    async getDefaultModelConfig() {
        return (0, config_1.getDefaultModelConfig)();
    }
    /**
       * Updates the configuration for a provider
       */
    async updateProviderConfig(providerId, _config) {
        if (!this.context) {
            logger_1.logger.error('LLMService not initialized with context');
            return false;
        }
        const provider = this.providers.get(providerId);
        if (!provider) {
            logger_1.logger.warn('Cannot update provider config: Provider ' + providerId + ' not found.');
            return false;
        }
        try {
            await provider.updateConfig(_config);
            logger_1.logger.info('Updated configuration for provider ' + providerId);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Failed to update configuration for provider ' + providerId + ':', error);
            return false;
        }
    }
    /**
       * Gets the configuration for a provider
       */
    getProviderConfig(providerId) {
        const provider = this.providers.get(providerId);
        if (!provider) {
            return undefined;
        }
        return provider.getConfig();
    }
}
exports.llmService = new LLMService();
//# sourceMappingURL=llmService.js.map