{"version": 3, "file": "databaseHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/databaseHandler.ts"], "names": [], "mappings": ";;AACA,sDA8DC;AA/DD,mDAAmD;AACnD,SAAgB,qBAAqB,CAAC,OAAY,EAAE,KAAU;IAC5D,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/G,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtJ,IAAI,CAAC;QACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,mBAAmB,CAAC;gBAC7C,MAAM;YACR,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;oBACxD,QAAQ,CAAC,KAAK,GAAG,mCAAmC,CAAC;oBACrD,MAAM;gBACR,CAAC;gBACD,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;gBACxG,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACtC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;gBACnB,MAAM;YACR,CAAC;YACD,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;oBAClB,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;oBACzC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC/E,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;gBACpF,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBAClD,MAAM;YACR,CAAC;YACD,KAAK,gBAAgB,CAAC,CAAC,CAAC;gBACtB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;oBACzC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC5E,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC5C,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC3C,CAAC;QACD,CAAC;IACH,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for Database settings messages and logic\nexport function handleDatabaseMessage(message: any, panel: any) {\n  const settings = (window as any).settings || {};\n  settings.databaseConnections = Array.isArray(settings.databaseConnections) ? settings.databaseConnections : [];\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: message.type, success: false, data: null, error: null };\n  try {\n    switch (message.type) {\n    case 'getDatabases': {\n      response.success = true;\n      response.data = settings.databaseConnections;\n      break;\n    }\n    case 'addDatabase': {\n      const db = message.db;\n      if (!db || !db.name || !db.type || !db.host || !db.port) {\n        response.error = 'Missing required database fields.';\n        break;\n      }\n      db.id = db.id || (db.name + '-' + db.host + '-' + db.port).replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();\n      settings.databaseConnections.push(db);\n      response.success = true;\n      response.data = db;\n      break;\n    }\n    case 'editDatabase': {\n      const db = message.db;\n      if (!db || !db.id) {\n        response.error = 'Database id required.';\n        break;\n      }\n      const idx = settings.databaseConnections.findIndex((d: any) => d.id === db.id);\n      if (idx === -1) {\n        response.error = 'Database not found.';\n        break;\n      }\n      settings.databaseConnections[idx] = { ...settings.databaseConnections[idx], ...db };\n      response.success = true;\n      response.data = settings.databaseConnections[idx];\n      break;\n    }\n    case 'deleteDatabase': {\n      const id = message.id;\n      if (!id) {\n        response.error = 'Database id required.';\n        break;\n      }\n      const idx = settings.databaseConnections.findIndex((d: any) => d.id === id);\n      if (idx === -1) {\n        response.error = 'Database not found.';\n        break;\n      }\n      settings.databaseConnections.splice(idx, 1);\n      response.success = true;\n      break;\n    }\n    default: {\n      response.error = 'Unknown message type.';\n    }\n    }\n  } catch (err: any) {\n    response.error = err?.message || String(err);\n  }\n  panel.postMessage(response);\n}\n\n"]}