{"version": 3, "file": "knowledgebaseManager.js", "sourceRoot": "", "sources": ["../../src/knowledge/knowledgebaseManager.ts"], "names": [], "mappings": ";;;AACA,sCAAmC;AAEnC;;GAEG;AACH,MAAa,oBAAoB;IACvB,MAAM,CAAC,QAAQ,CAAuB;IACtC,aAAa,GAIhB,EAAE,CAAC;IAER,gBAAuB,CAAC;IAExB;;OAEG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;YACnC,oBAAoB,CAAC,QAAQ,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC7D,CAAC;QACD,OAAO,oBAAoB,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAAC,KAAa;QAC7C,eAAM,CAAC,IAAI,CAAC,yCAAyC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;QAElF,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,4FAA4F;QAC5F,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa;aACrC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACpD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yBAAyB;QAEzC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,gDAAgD;QAChD,OAAO,aAAa;aACjB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,2BAA2B,IAAI,CAAC,OAAO,uBAAuB,CAAC;aAC3E,IAAI,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,QAA8B;QACvE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACtB,OAAO;YACP,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,qCAAqC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAAC,KAAa;QACxC,OAAO,IAAI,CAAC,aAAa;aACtB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACpD,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;SACxD,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,OAAe,EAAE,KAAa;QAC/C,MAAM,iBAAiB,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAE5C,0DAA0D;QAC1D,MAAM,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAChD,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAAe,EAAE,KAAa;QACvD,MAAM,iBAAiB,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAE5C,mDAAmD;QACnD,MAAM,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAElF,OAAO,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAClD,CAAC;CACF;AAlGD,oDAkGC;AAED,4BAA4B;AACf,QAAA,oBAAoB,GAAG,oBAAoB,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import { IKnowledgebaseManager } from '../managers';\nimport { logger } from '../logger';\n\n/**\n * Knowledgebase Manager for handling knowledge operations\n */\nexport class KnowledgebaseManager implements IKnowledgebaseManager {\n  private static instance: KnowledgebaseManager;\n  private knowledgebase: Array<{\n    content: string;\n    metadata?: Record<string, any>;\n    timestamp: number;\n  }> = [];\n  \n  private constructor() {}\n  \n  /**\n   * Get the singleton instance\n   */\n  public static getInstance(): KnowledgebaseManager {\n    if (!KnowledgebaseManager.instance) {\n      KnowledgebaseManager.instance = new KnowledgebaseManager();\n    }\n    return KnowledgebaseManager.instance;\n  }\n  \n  /**\n   * Get relevant knowledge for a query\n   */\n  public async getRelevantKnowledge(query: string): Promise<string> {\n    logger.info(`Getting relevant knowledge for query: ${query.substring(0, 50)}...`);\n    \n    if (this.knowledgebase.length === 0) {\n      return '';\n    }\n    \n    // Simple implementation - in a real system, this would use embeddings and similarity search\n    const relevantItems = this.knowledgebase\n      .filter(item => this.isRelevant(item.content, query))\n      .slice(0, 3); // Limit to top 3 results\n    \n    if (relevantItems.length === 0) {\n      return '';\n    }\n    \n    // Format the knowledge for inclusion in prompts\n    return relevantItems\n      .map(item => `--- Knowledge Item ---\\n${item.content}\\n-------------------`)\n      .join('\\n\\n');\n  }\n  \n  /**\n   * Add knowledge to the knowledgebase\n   */\n  public async addKnowledge(content: string, metadata?: Record<string, any>): Promise<void> {\n    this.knowledgebase.push({\n      content,\n      metadata,\n      timestamp: Date.now()\n    });\n    \n    logger.info(`Added knowledge to knowledgebase: ${content.substring(0, 50)}...`);\n  }\n  \n  /**\n   * Search the knowledgebase\n   */\n  public async searchKnowledge(query: string): Promise<any[]> {\n    return this.knowledgebase\n      .filter(item => this.isRelevant(item.content, query))\n      .map(item => ({\n        content: item.content,\n        metadata: item.metadata,\n        timestamp: item.timestamp,\n        relevance: this.calculateRelevance(item.content, query)\n      }))\n      .sort((a, b) => b.relevance - a.relevance);\n  }\n  \n  /**\n   * Simple relevance check - in a real system, this would use embeddings and similarity search\n   */\n  private isRelevant(content: string, query: string): boolean {\n    const normalizedContent = content.toLowerCase();\n    const normalizedQuery = query.toLowerCase();\n    \n    // Check if any words from the query appear in the content\n    const queryWords = normalizedQuery.split(/\\s+/);\n    return queryWords.some(word => normalizedContent.includes(word));\n  }\n  \n  /**\n   * Calculate relevance score - in a real system, this would use embeddings and similarity search\n   */\n  private calculateRelevance(content: string, query: string): number {\n    const normalizedContent = content.toLowerCase();\n    const normalizedQuery = query.toLowerCase();\n    \n    // Count how many query words appear in the content\n    const queryWords = normalizedQuery.split(/\\s+/);\n    const matchingWords = queryWords.filter(word => normalizedContent.includes(word));\n    \n    return matchingWords.length / queryWords.length;\n  }\n}\n\n// Export singleton instance\nexport const knowledgebaseManager = KnowledgebaseManager.getInstance();"]}