import * as vscode from 'vscode';
import { getLogLevel } from './config';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

const LOG_LEVEL_PRIORITY: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3
};

export class Logger {
  private static _instance: Logger | null = null;
  private outputChannel: vscode.OutputChannel;
  private currentLogLevel: LogLevel;

  private configChangeDisposable: vscode.Disposable | undefined;

  public static initialize(context: vscode.ExtensionContext): void {
    const instance = Logger.instance;
    // Store the config change subscription in the extension context's subscriptions
    if (instance.configChangeDisposable) {
      instance.configChangeDisposable.dispose();
    }
    instance.configChangeDisposable = vscode.workspace.onDidChangeConfiguration(e => {
      if (e.affectsConfiguration('codessa.logLevel')) {
        instance.updateLogLevel();
      }
    });
    context.subscriptions.push(instance.configChangeDisposable);
  }

  private constructor() {
    this.outputChannel = vscode.window.createOutputChannel('Codessa');
    this.currentLogLevel = getLogLevel() as LogLevel;
  }

  public static get instance(): Logger {
    if (!Logger._instance) {
      Logger._instance = new Logger();
    }
    return Logger._instance;
  }

  private updateLogLevel(): void {
    this.currentLogLevel = getLogLevel() as LogLevel;
    this.debug(`Log level set to: ${this.currentLogLevel}`);
  }

  private shouldLog(level: LogLevel): boolean {
    return LOG_LEVEL_PRIORITY[level] >= LOG_LEVEL_PRIORITY[this.currentLogLevel];
  }

  private formatMessage(level: LogLevel, message: string, details?: unknown): string {
    const timestamp = new Date().toISOString();
    const levelPadded = level.toUpperCase().padEnd(5);
    let formattedMessage = `[${timestamp}] ${levelPadded} ${message}`;

    if (details) {
      if (details instanceof Error) {
        formattedMessage += `\n  Stack: ${details.stack || 'No stack trace available'}`;
      } else if (typeof details === 'object') {
        try {
          formattedMessage += `\n  Details: ${JSON.stringify(details, null, 2)}`;
        } catch (e) {
          formattedMessage += '\n  Details: [Object cannot be stringified]';
        }
      } else {
        formattedMessage += `\n  Details: ${details}`;
      }
    }

    return formattedMessage;
  }

  private log(level: LogLevel, message: string, details?: unknown): void {
    if (this.shouldLog(level)) {
      const formattedMessage = this.formatMessage(level, message, details);
      this.outputChannel?.appendLine(formattedMessage);

      // For errors, also show notification
      if (level === 'error') {
        vscode.window.showErrorMessage(message);
      }
    }
  }

  debug(message: string, details?: unknown): void {
    this.log('debug', message, details);
  }

  info(message: string, details?: unknown): void {
    this.log('info', message, details);
  }

  warn(message: string, details?: unknown): void {
    this.log('warn', message, details);
  }

  error(message: string, details?: unknown): void {
    this.log('error', message, details);
  }

  show(): void {
    this.outputChannel?.show();
  }

  clear(): void {
    this.outputChannel?.clear();
  }

  dispose(): void {
    this.outputChannel?.dispose();
  }
}

// Export logger instance after Logger class is defined
export const logger = Logger.instance;
