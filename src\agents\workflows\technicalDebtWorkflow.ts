/**
 * Codessa Technical Debt Workflow
 *
 * This module provides workflow templates for technical debt reduction:
 * - Code analysis
 * - Refactoring
 * - Testing
 * - Documentation
 */

import { ITool } from '../../tools/tool.ts.backup';
import { Agent } from '../agentUtilities/agent';
import { <PERSON>ssa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';
import { workflowRegistry } from './workflowRegistry';
import { Logger } from '../../logger';
import { StructuredTool } from './corePolyfill';

/**
 * Create a Technical Debt workflow for identifying and reducing technical debt
 */
export function createTechnicalDebtWorkflow(
  id: string,
  name: string,
  description: string,
  analyzerAgent: Agent,
  refactoringAgent: Agent,
  testingAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  Logger.instance.info(`Creating Technical Debt workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const codeAnalysisNode = Codessa.createAgentNode('code-analysis', 'Code Analysis', analyzerAgent);
  const debtIdentificationNode = Codessa.createAgentNode('debt-identification', 'Debt Identification', analyzerAgent);
  const prioritizationNode = Codessa.createAgentNode('prioritization', 'Prioritization', analyzerAgent);
  const refactoringPlanNode = Codessa.createAgentNode('refactoring-plan', 'Refactoring Plan', refactoringAgent);
  const refactoringImplementationNode = Codessa.createAgentNode('refactoring-implementation', 'Refactoring Implementation', refactoringAgent);
  const testingNode = Codessa.createAgentNode('testing', 'Testing', testingAgent);
  const documentationUpdateNode = Codessa.createAgentNode('documentation-update', 'Documentation Update', refactoringAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'default' },
    { name: 'analysis-to-identification', source: 'code-analysis', target: 'debt-identification', type: 'default' },
    { name: 'identification-to-prioritization', source: 'debt-identification', target: 'prioritization', type: 'default' },
    { name: 'prioritization-to-plan', source: 'prioritization', target: 'refactoring-plan', type: 'default' },
    { name: 'plan-to-implementation', source: 'refactoring-plan', target: 'refactoring-implementation', type: 'default' },
    { name: 'implementation-to-testing', source: 'refactoring-implementation', target: 'testing', type: 'default' },
    { name: 'testing-to-documentation', source: 'testing', target: 'documentation-update', type: 'default' },
    { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },

    // Feedback loops
    { name: 'testing-to-implementation', source: 'testing', target: 'refactoring-implementation', type: 'feedback' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect refactoring implementation to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `implementation-to-tool-${index}`,
        source: 'refactoring-implementation',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to testing
      edges.push({
        name: `tool-${index}-to-testing`,
        source: `tool-${index}`,
        target: 'testing',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      codeAnalysisNode,
      debtIdentificationNode,
      prioritizationNode,
      refactoringPlanNode,
      refactoringImplementationNode,
      testingNode,
      documentationUpdateNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'technical-debt' as OperationMode,
    tags: ['technical-debt', 'refactoring', 'code-quality']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a specialized Legacy Code Modernization workflow
 */
export function createLegacyCodeModernizationWorkflow(
  id: string,
  name: string,
  description: string,
  analyzerAgent: Agent,
  refactoringAgent: Agent,
  testingAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  Logger.instance.info(`Creating Legacy Code Modernization workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const codebaseAnalysisNode = Codessa.createAgentNode('codebase-analysis', 'Codebase Analysis', analyzerAgent);
  const dependencyAnalysisNode = Codessa.createAgentNode('dependency-analysis', 'Dependency Analysis', analyzerAgent);
  const architectureAssessmentNode = Codessa.createAgentNode('architecture-assessment', 'Architecture Assessment', analyzerAgent);
  const testCoverageAnalysisNode = Codessa.createAgentNode('test-coverage-analysis', 'Test Coverage Analysis', testingAgent);
  const testCreationNode = Codessa.createAgentNode('test-creation', 'Test Creation', testingAgent);
  const incrementalRefactoringNode = Codessa.createAgentNode('incremental-refactoring', 'Incremental Refactoring', refactoringAgent);
  const dependencyUpdateNode = Codessa.createAgentNode('dependency-update', 'Dependency Update', refactoringAgent);
  const architectureModernizationNode = Codessa.createAgentNode('architecture-modernization', 'Architecture Modernization', refactoringAgent);
  const regressionTestingNode = Codessa.createAgentNode('regression-testing', 'Regression Testing', testingAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-codebase-analysis', source: 'input', target: 'codebase-analysis', type: 'default' },
    { name: 'codebase-to-dependency', source: 'codebase-analysis', target: 'dependency-analysis', type: 'default' },
    { name: 'codebase-to-architecture', source: 'codebase-analysis', target: 'architecture-assessment', type: 'default' },
    { name: 'codebase-to-test-coverage', source: 'codebase-analysis', target: 'test-coverage-analysis', type: 'default' },
    { name: 'test-coverage-to-test-creation', source: 'test-coverage-analysis', target: 'test-creation', type: 'default' },
    { name: 'test-creation-to-refactoring', source: 'test-creation', target: 'incremental-refactoring', type: 'default' },
    { name: 'dependency-to-update', source: 'dependency-analysis', target: 'dependency-update', type: 'default' },
    { name: 'architecture-to-modernization', source: 'architecture-assessment', target: 'architecture-modernization', type: 'default' },
    { name: 'refactoring-to-testing', source: 'incremental-refactoring', target: 'regression-testing', type: 'default' },
    { name: 'dependency-update-to-testing', source: 'dependency-update', target: 'regression-testing', type: 'default' },
    { name: 'architecture-modernization-to-testing', source: 'architecture-modernization', target: 'regression-testing', type: 'default' },
    { name: 'testing-to-output', source: 'regression-testing', target: 'output', type: 'default' },

    // Feedback loops
    { name: 'testing-to-refactoring', source: 'regression-testing', target: 'incremental-refactoring', type: 'feedback' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect incremental refactoring to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `refactoring-to-tool-${index}`,
        source: 'incremental-refactoring',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to regression testing
      edges.push({
        name: `tool-${index}-to-testing`,
        source: `tool-${index}`,
        target: 'regression-testing',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      codebaseAnalysisNode,
      dependencyAnalysisNode,
      architectureAssessmentNode,
      testCoverageAnalysisNode,
      testCreationNode,
      incrementalRefactoringNode,
      dependencyUpdateNode,
      architectureModernizationNode,
      regressionTestingNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'technical-debt' as OperationMode,
    tags: ['legacy-code', 'modernization', 'refactoring']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}
