{"version": 3, "file": "editMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/editMode.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,mDAA4E;AAG5E,yCAAsC;AACtC,qDAAkD;AAClD,+DAA4D;AAE5D;;GAEG;AACH,MAAa,QAAS,SAAQ,6BAAa;IAChC,EAAE,GAAG,MAAM,CAAC;IACZ,WAAW,GAAG,MAAM,CAAC;IACrB,WAAW,GAAG,kDAAkD,CAAC;IACjE,IAAI,GAAG,SAAS,CAAC;IACjB,kBAAkB,GAAG,2BAAW,CAAC,cAAc,CAAC;IAChD,yBAAyB,GAAG,IAAI,CAAC;IACjC,sBAAsB,GAAG,KAAK,CAAC;IAEhC,YAAY,GAA0D,IAAI,GAAG,EAAE,CAAC;IAExF;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY,EACZ,aAA4B,EAC5B,iBAA2C;QAE3C,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC;YAEpE,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE7E,kCAAkC;YAClC,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;wBACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,oCAAoC,CAAC,CAAC;oBAC9F,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,EAAE,WAAW,CAAC,CAAC;gBACjF,kCAAkC;YACpC,CAAC;YAED,yCAAyC;YACzC,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,WAAW,EAAE;gBACrD,cAAc;gBACd,aAAa;gBACb,OAAO;aACR,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAEnE,6CAA6C;YAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAEpD,4CAA4C;YAC5C,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC7D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC1C,CAAC;YAED,mCAAmC;YACnC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,iBAAiB,OAAO,EAAE,CAAC,CAAC;oBACjE,MAAM,WAAW,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,EAAE,WAAW,CAAC,CAAC;gBAC7E,qCAAqC;YACvC,CAAC;YAED,sCAAsC;YACtC,MAAM,wBAAwB,GAAG;EACrC,QAAQ;;;;;;;CAOT,CAAC;YAEI,OAAO,wBAAwB,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,uCAAuC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QACzG,CAAC;IACH,CAAC;IAED;;SAEK;IACG,gBAAgB,CAAC,QAAgB;QACvC,MAAM,WAAW,GAA6D,EAAE,CAAC;QAEjF,2EAA2E;QAC3E,MAAM,SAAS,GAAG,yEAAyE,CAAC;QAC5F,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE7C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,yBAAyB,CAAC;YAElE,WAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;QACnD,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,CAAC;gBACH,4BAA4B;gBAC5B,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEtC,gBAAgB;gBAChB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAE9D,yBAAyB;gBACzB,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBAExC,yCAAyC;gBACzC,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAChC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EACtB,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAC/C,CAAC;gBAEF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;gBAE7C,iBAAiB;gBACjB,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAEvC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBACpE,MAAM,IAAI,KAAK,CAAC,2BAA2B,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACpH,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG,EAAE,6CAA6C;YAC/D,SAAS,EAAE,IAAI,EAAI,sCAAsC;YACzD,IAAI,EAAE,MAAM;SACb,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,eAAe,CACnB,MAAa,EACb,cAA6B;QAE7B,OAAO;;;;;;;;;;;;;;;;CAgBV,CAAC;IACA,CAAC;IAED;;SAEK;IACL,eAAe;QAKb,OAAO;YACL,YAAY,EAAE;;;;;;;;;;;;;;;;;;;CAmBnB;YACK,YAAY,EAAE;;;;;CAKnB;SACI,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,aAAa,CAAC,OAAe,EACjC,KAAgB;QAEhB,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBACxB,MAAM;YAER,KAAK,aAAa;gBAChB,yCAAyC;gBACzC,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;oBAC7D,IAAI,CAAC;wBACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACtC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;wBAC9D,gDAAgD;wBAChD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;wBAE5C,uCAAuC;wBACvC,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC;wBAC1E,0CAA0C;wBAC1C,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;wBACjD,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;wBACxC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;wBAChE,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBAEvC,YAAY;wBACZ,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,QAAQ,YAAY,CAAC,CAAC;oBAC7F,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;oBACtE,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,aAAa;gBAChB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;gBAC1B,MAAM;QACV,CAAC;IACH,CAAC;CACF;AA1QD,4BA0QC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { contextManager } from './contextManager';\nimport { promptManager } from '../../prompts/promptManager';\n\n/**\n * Edit Mode - Autonomous code editing with human verification\n */\nexport class EditMode extends OperationMode {\n  readonly id = 'edit';\n  readonly displayName = 'Edit';\n  readonly description = 'AI-assisted code editing with human verification';\n  readonly icon = '$(edit)';\n  readonly defaultContextType = ContextType.SELECTED_FILES;\n  readonly requiresHumanVerification = true;\n  readonly supportsMultipleAgents = false;\n\n  private pendingEdits: Map<string, { content: string, description: string }> = new Map();\n\n  /**\n     * Process a user message in Edit mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    contextSource: ContextSource,\n    _additionalParams?: Record<string, unknown>\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Edit mode: ${message}`);\n\n      // Get context content\n      const contextContent = await contextManager.getContextContent(contextSource);\n\n      // Add memory context if available\n      let memoryContext = '';\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          const relevantMemories = await agentMemory.getRelevantMemories(message);\n          if (relevantMemories && relevantMemories.length > 0) {\n            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to edit context`);\n          }\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to retrieve memory context for edit:', memoryError);\n        // Continue without memory context\n      }\n\n      // Prepare the prompt using promptManager\n      const prompt = promptManager.renderPrompt('mode.edit', {\n        contextContent,\n        memoryContext,\n        message\n      });\n\n      // Generate response using the agent\n      const response = await agent.generate(prompt, this.getLLMParams());\n\n      // Parse the response to extract file changes\n      const fileChanges = this.parseFileChanges(response);\n\n      // Store pending edits for user verification\n      for (const [filePath, change] of Object.entries(fileChanges)) {\n        this.pendingEdits.set(filePath, change);\n      }\n\n      // Store the edit session in memory\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          await agentMemory.addMessage('user', `Edit request: ${message}`);\n          await agentMemory.addMessage('assistant', response);\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to store edit session in memory:', memoryError);\n        // Continue without storing in memory\n      }\n\n      // Add verification UI to the response\n      const responseWithVerification = `\n${response}\n\n---\n\nI've analyzed the code and proposed the changes above. Would you like me to apply these changes?\n\n[Apply Changes] [Review Changes] [Cancel]\n`;\n\n      return responseWithVerification;\n    } catch (error) {\n      Logger.instance.error('Error processing message in Edit mode:', error);\n      return `Error processing your edit request: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Parse file changes from the agent's response\n     */\n  private parseFileChanges(response: string): Record<string, { content: string, description: string }> {\n    const fileChanges: Record<string, { content: string, description: string }> = {};\n\n    // Simple regex-based parsing (could be improved with a more robust parser)\n    const fileRegex = /### \\[(.*?)\\][\\s\\S]*?\\`\\`\\`after([\\s\\S]*?)\\`\\`\\`[\\s\\S]*?((?=### \\[)|$)/g;\n    const matches = response.matchAll(fileRegex);\n\n    for (const match of matches) {\n      const filePath = match[1].trim();\n      const content = match[2].trim();\n      const description = match[3]?.trim() || 'No description provided';\n\n      fileChanges[filePath] = { content, description };\n    }\n\n    return fileChanges;\n  }\n\n  /**\n     * Apply pending edits to files\n     */\n  async applyEdits(): Promise<void> {\n    for (const [filePath, change] of this.pendingEdits.entries()) {\n      try {\n        // Create a URI for the file\n        const uri = vscode.Uri.file(filePath);\n\n        // Read the file\n        const document = await vscode.workspace.openTextDocument(uri);\n\n        // Create a WorkspaceEdit\n        const edit = new vscode.WorkspaceEdit();\n\n        // Replace the entire content of the file\n        const fullRange = new vscode.Range(\n          document.positionAt(0),\n          document.positionAt(document.getText().length)\n        );\n\n        edit.replace(uri, fullRange, change.content);\n\n        // Apply the edit\n        await vscode.workspace.applyEdit(edit);\n\n        Logger.instance.info(`Applied edit to ${filePath}`);\n      } catch (error) {\n        Logger.instance.error(`Error applying edit to ${filePath}:`, error);\n        throw new Error(`Failed to apply edit to ${filePath}: ${error instanceof Error ? error.message : String(error)}`);\n      }\n    }\n\n    // Clear pending edits\n    this.pendingEdits.clear();\n  }\n\n  /**\n     * Get LLM parameters specific to Edit mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.3, // Lower temperature for more precise editing\n      maxTokens: 2000,   // Longer responses for detailed edits\n      mode: 'edit'\n    };\n  }\n\n  /**\n     * Get the system prompt for Edit mode\n     */\n  async getSystemPrompt(\n    _agent: Agent,\n    _contextSource: ContextSource\n  ): Promise<string> {\n    return `\nYou are an AI assistant specialized in editing code.\nYour task is to help the user make changes to their codebase.\nAnalyze the code carefully and propose specific, well-thought-out changes.\n\nWhen suggesting changes:\n1. Provide a detailed plan of the changes to be made\n2. For each file that needs to be modified, provide:\n   a. The file path\n   b. The exact code to be changed (before)\n   c. The new code (after)\n   d. A brief explanation of the change\n\nBe precise and thorough in your analysis and suggestions.\nEnsure that your proposed changes maintain the integrity and functionality of the codebase.\nConsider potential side effects of your changes and address them in your plan.\n`;\n  }\n\n  /**\n     * Get UI components specific to Edit mode\n     */\n  getUIComponents(): {\n    controlPanel?: string;\n    contextPanel?: string;\n    messageInput?: string;\n  } {\n    return {\n      contextPanel: `\n<div class=\"context-panel\">\n    <div class=\"context-header\">\n        <h3>Edit Context</h3>\n        <div class=\"context-controls\">\n            <button id=\"btn-refresh-context\" title=\"Refresh Context\"><i class=\"codicon codicon-refresh\"></i></button>\n            <button id=\"btn-select-files\" title=\"Select Files\"><i class=\"codicon codicon-file-code\"></i></button>\n            <button id=\"btn-select-folders\" title=\"Select Folders\"><i class=\"codicon codicon-folder\"></i></button>\n        </div>\n    </div>\n    <div class=\"context-type\">\n        <select id=\"context-type-selector\">\n            <option value=\"selected_files\">Selected Files</option>\n            <option value=\"current_file\">Current File</option>\n            <option value=\"custom\">Custom</option>\n        </select>\n    </div>\n    <div id=\"context-files-list\" class=\"context-files-list\"></div>\n</div>\n`,\n      messageInput: `\n<div class=\"message-input-container\">\n    <textarea id=\"message-input\" placeholder=\"Describe the changes you want to make...\"></textarea>\n    <button id=\"btn-send\" title=\"Send\"><i class=\"codicon codicon-send\"></i></button>\n</div>\n`\n    };\n  }\n\n  /**\n     * Handle mode-specific commands\n     */\n  async handleCommand(command: string,\n    _args: unknown[]\n  ): Promise<void> {\n    switch (command) {\n      case 'applyEdits':\n        await this.applyEdits();\n        break;\n\n      case 'reviewEdits':\n        // Open a diff view for each pending edit\n        for (const [filePath, change] of this.pendingEdits.entries()) {\n          try {\n            const uri = vscode.Uri.file(filePath);\n            const document = await vscode.workspace.openTextDocument(uri);\n            // Store original content for potential rollback\n            const _originalContent = document.getText();\n\n            // Create a temporary file for the diff\n            const tempUri = uri.with({ scheme: 'untitled', path: `${uri.path}.new` });\n            // Create temporary document for diff view\n            await vscode.workspace.openTextDocument(tempUri);\n            const edit = new vscode.WorkspaceEdit();\n            edit.insert(tempUri, new vscode.Position(0, 0), change.content);\n            await vscode.workspace.applyEdit(edit);\n\n            // Show diff\n            await vscode.commands.executeCommand('vscode.diff', uri, tempUri, `${filePath} (Changes)`);\n          } catch (error) {\n            Logger.instance.error(`Error showing diff for ${filePath}:`, error);\n          }\n        }\n        break;\n\n      case 'cancelEdits':\n        this.pendingEdits.clear();\n        break;\n    }\n  }\n}\n"]}