{"version": 3, "file": "codeGenerationTool.js", "sourceRoot": "", "sources": ["../../src/tools/codeGenerationTool.ts"], "names": [], "mappings": ";;;AAEA,kDAA+C;AAE/C,6EAA0H;AAC1H,6BAAwB;AAExB,MAAa,kBAAkB;IACpB,EAAE,GAAG,SAAS,CAAC;IACf,IAAI,GAAG,uCAAuC,CAAC;IAC/C,WAAW,GAAG,uGAAuG,CAAC;IACtH,IAAI,GAAG,cAAc,CAAC,CAAC,oBAAoB;IAC3C,OAAO,GAAwB;QACtC,UAAU,EAAE;YACV,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,qCAAqC;YAClD,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;aAC3E,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,6CAA6C,EAAE;iBACvF;gBACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACvE,IAAI,CAAC,QAAQ;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;gBAC/G,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,YAAY,EAAE,mCAAmC,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;gBACxM,IAAI,MAAM,CAAC,KAAK;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;gBACjG,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;YACnF,CAAC;SACF;QACD,UAAU,EAAE;YACV,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,yBAAyB;YACtC,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gDAAgD,CAAC;gBAC7E,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC;aAClD,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gDAAgD,EAAE;oBACzF,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAE;iBAC9D;gBACD,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;aAC7B;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACvE,IAAI,CAAC,QAAQ;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;gBAC/G,MAAM,UAAU,GAAG,uDAAuD,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,IAAI,EAAE,CAAC;gBAC/G,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,+CAA+C,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;gBAClN,IAAI,MAAM,CAAC,KAAK;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;gBACjG,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;YACnF,CAAC;SACF;QACD,QAAQ,EAAE;YACR,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE,sCAAsC;YACnD,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2CAA2C,CAAC;gBACxE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;gBACnF,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,wDAAwD,CAAC;aAC1F,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,2CAA2C,EAAE;oBACpF,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oDAAoD,EAAE;oBAC/F,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wDAAwD,EAAE;iBACpG;gBACD,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC;aAC7C;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACvE,IAAI,CAAC,QAAQ;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;gBAC7G,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,YAAY,EAAE,mCAAmC,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;gBACxM,IAAI,MAAM,CAAC,KAAK;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;gBAC/F,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjF,CAAC;SACF;QACD,SAAS,EAAE,IAAI,4CAAe,EAAE;QAChC,UAAU,EAAE,IAAI,6CAAgB,EAAE;QAClC,eAAe,EAAE,IAAI,8CAAiB,EAAE;QACxC,cAAc,EAAE,IAAI,iDAAoB,EAAE;KAC3C,CAAC;IACM,SAAS,GAAc;QAC7B,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,eAAe;QACxB,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;KAC9B,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,4FAA4F;QAC5F,MAAM,MAAM,GAAG,UAAU,IAAI,KAAK,CAAC,MAAgB,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oDAAoD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC5J,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mCAAmC,MAAM,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACzK,CAAC;QACD,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;QACjC,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC5B,OAAO,WAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAED,gEAAgE;QAChE,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnC,iEAAiE;YACjE,OAAO,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,6DAA6D;YAC7D,MAAM,MAAM,GAAG,MAAO,UAAkB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACvE,6DAA6D;YAC7D,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACjC,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC;YAC7B,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;CACF;AA5HD,gDA4HC;AAEY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["import { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { llmService } from '../llm/llmService';\nimport { LLMConfig } from '../config';\nimport { ExplainCodeTool, DocumentCodeTool, GenerateTestsTool, MultiFileCodeGenTool } from './advancedCodeGenerationTool';\nimport { z } from 'zod';\n\nexport class CodeGenerationTool implements ITool {\n  readonly id = 'codeGen';\n  readonly name = 'Code Generation & Refactor (Advanced)';\n  readonly description = 'Generate, refactor, insert, explain, document, create tests, and multi-file code generation using AI.';\n  readonly type = 'multi-action'; // Required by ITool\n  readonly actions: Record<string, any> = {\n    'generate': {\n      id: 'generate',\n      name: 'Generate Code',\n      description: 'Generate code from prompt using AI.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        prompt: z.string().describe('The prompt describing what code to generate')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          prompt: { type: 'string', description: 'The prompt describing what code to generate' }\n        },\n        required: ['prompt']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const provider = await llmService.getProviderForConfig(this.llmConfig);\n        if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: 'generate', actionName };\n        const result = await provider.generate({ prompt: input.prompt, systemPrompt: 'You are an expert code generator.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });\n        if (result.error) return { success: false, error: result.error, toolId: 'generate', actionName };\n        return { success: true, output: result.content, toolId: 'generate', actionName };\n      }\n    },\n    'refactor': {\n      id: 'refactor',\n      name: 'Refactor Code',\n      description: 'Refactor code using AI.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        prompt: z.string().describe('The prompt describing how to refactor the code'),\n        code: z.string().describe('The code to refactor')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          prompt: { type: 'string', description: 'The prompt describing how to refactor the code' },\n          code: { type: 'string', description: 'The code to refactor' }\n        },\n        required: ['prompt', 'code']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const provider = await llmService.getProviderForConfig(this.llmConfig);\n        if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: 'refactor', actionName };\n        const userPrompt = `Refactor the following code as requested.\\nRequest: ${input.prompt}\\nCode:\\n${input.code}`;\n        const result = await provider.generate({ prompt: userPrompt, systemPrompt: 'You are an expert code refactoring assistant.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });\n        if (result.error) return { success: false, error: result.error, toolId: 'refactor', actionName };\n        return { success: true, output: result.content, toolId: 'refactor', actionName };\n      }\n    },\n    'insert': {\n      id: 'insert',\n      name: 'Insert Code',\n      description: 'Insert code at a position in a file.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        prompt: z.string().describe('The prompt describing what code to insert'),\n        filePath: z.string().describe('The path to the file where code should be inserted'),\n        position: z.object({}).describe('The position in the file where code should be inserted')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          prompt: { type: 'string', description: 'The prompt describing what code to insert' },\n          filePath: { type: 'string', description: 'The path to the file where code should be inserted' },\n          position: { type: 'object', description: 'The position in the file where code should be inserted' }\n        },\n        required: ['prompt', 'filePath', 'position']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const provider = await llmService.getProviderForConfig(this.llmConfig);\n        if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: 'insert', actionName };\n        const result = await provider.generate({ prompt: input.prompt, systemPrompt: 'You are an expert code generator.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });\n        if (result.error) return { success: false, error: result.error, toolId: 'insert', actionName };\n        return { success: true, output: result.content, toolId: 'insert', actionName };\n      }\n    },\n    'explain': new ExplainCodeTool(),\n    'document': new DocumentCodeTool(),\n    'generateTests': new GenerateTestsTool(),\n    'multiFileGen': new MultiFileCodeGenTool(),\n  };\n  private llmConfig: LLMConfig = {\n    provider: 'openai',\n    modelId: 'gpt-3.5-turbo',\n    options: { temperature: 0.2 }\n  };\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // For backward compatibility, check if actionName is undefined and try to get it from input\n    const action = actionName || input.action as string;\n    if (!action) {\n      return { success: false, error: `Action parameter is required. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };\n    }\n    const actionTool = this.actions[action];\n    if (!actionTool) {\n      return { success: false, error: `Unknown code generation action: ${action}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };\n    }\n    const actionInput = { ...input };\n    if ('action' in actionInput) {\n      delete actionInput.action;\n    }\n\n    // Check if the action tool has the new execute method signature\n    if (actionTool.execute.length >= 2) {\n      // Pass the action as the actionName parameter to the nested tool\n      return actionTool.execute(action, actionInput, context);\n    } else {\n      // Fallback for older tools that don't have the new signature\n      const result = await (actionTool as any).execute(actionInput, context);\n      // Add the actionName to the result if it's not already there\n      if (result && !result.actionName) {\n        result.actionName = action;\n      }\n      return result;\n    }\n  }\n}\n\nexport const codeGenerationTool = new CodeGenerationTool();\n"]}