import * as vscode from 'vscode';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import { z } from 'zod';
import * as path from 'path';
import * as fs from 'fs';
import { Logger } from '../logger';

/**
 * Diagnostic severity levels
 */
export enum DiagnosticSeverity {
  Error = 'error',
  Warning = 'warning',
  Information = 'information',
  Hint = 'hint'
}

/**
 * Diagnostic source types
 */
export enum DiagnosticSource {
  TypeScript = 'typescript',
  ESLint = 'eslint',
  Stylelint = 'stylelint',
  Compiler = 'compiler',
  Linter = 'linter',
  Extension = 'extension',
  VSCode = 'vscode',
  Other = 'other'
}

/**
 * Interface for fix results
 */
interface FixResult {
  diagnostic: DiagnosticInfo;
  fixed: boolean;
  reason: string;
  action?: string;
}

/**
 * Interface for diagnostic information
 */
export interface DiagnosticInfo {
  message: string;
  severity: DiagnosticSeverity;
  source?: DiagnosticSource | string;
  code?: string | number;
  startLine: number;
  startCharacter: number;
  endLine: number;
  endCharacter: number;
  relatedInformation?: {
    message: string;
    file?: string;
    startLine?: number;
    startCharacter?: number;
  }[];
  tags?: string[];
  filePath: string;
}

/**
 * Tool for retrieving and analyzing diagnostics from the IDE
 */
export class DiagnosticsTool implements ITool {
  readonly id = 'diagnostics';
  readonly name = 'Diagnostics Tool';
  readonly description = 'Retrieves and analyzes diagnostics (errors, warnings, etc.) from the IDE.';
  readonly type = 'multi-action';
  // Add index signature to fix "Element implicitly has an 'any' type" error
  readonly actions: { [key: string]: any } = {
    'get': {
      description: 'Get diagnostics for specified files or all files in the workspace',
      schema: z.object({
        paths: z.array(z.string()).optional().describe('Optional list of file paths to get diagnostics for. If not provided, returns diagnostics for all files.'),
        severity: z.enum(['error', 'warning', 'information', 'hint', 'all']).optional().describe('Filter diagnostics by severity. Default is all.'),
        source: z.string().optional().describe('Filter diagnostics by source (e.g., "typescript", "eslint").'),
        limit: z.number().optional().describe('Maximum number of diagnostics to return. Default is no limit.')
      }),
      inputSchema: z.object({
        paths: z.array(z.string()).optional().describe('Optional list of file paths to get diagnostics for. If not provided, returns diagnostics for all files.'),
        severity: z.enum(['error', 'warning', 'information', 'hint', 'all']).optional().describe('Filter diagnostics by severity. Default is all.'),
        source: z.string().optional().describe('Filter diagnostics by source (e.g., "typescript", "eslint").'),
        limit: z.number().optional().describe('Maximum number of diagnostics to return. Default is no limit.')
      })
    },
    'analyze': {
      description: 'Analyze diagnostics and provide insights',
      schema: z.object({
        paths: z.array(z.string()).optional().describe('Optional list of file paths to analyze diagnostics for. If not provided, analyzes all files.'),
        groupBy: z.enum(['file', 'severity', 'source', 'code']).optional().describe('Group diagnostics by the specified property. Default is file.')
      }),
      inputSchema: z.object({
        paths: z.array(z.string()).optional().describe('Optional list of file paths to analyze diagnostics for. If not provided, analyzes all files.'),
        groupBy: z.enum(['file', 'severity', 'source', 'code']).optional().describe('Group diagnostics by the specified property. Default is file.')
      })
    },
    'fix': {
      description: 'Attempt to fix diagnostics automatically',
      schema: z.object({
        paths: z.array(z.string()).optional().describe('Optional list of file paths to fix diagnostics for. If not provided, attempts to fix all files.'),
        severity: z.enum(['error', 'warning', 'information', 'hint', 'all']).optional().describe('Filter diagnostics by severity to fix. Default is all.'),
        source: z.string().optional().describe('Filter diagnostics by source to fix (e.g., "typescript", "eslint").'),
        dryRun: z.boolean().optional().describe('If true, only reports what would be fixed without making changes. Default is false.')
      }),
      inputSchema: z.object({
        paths: z.array(z.string()).optional().describe('Optional list of file paths to fix diagnostics for. If not provided, attempts to fix all files.'),
        severity: z.enum(['error', 'warning', 'information', 'hint', 'all']).optional().describe('Filter diagnostics by severity to fix. Default is all.'),
        source: z.string().optional().describe('Filter diagnostics by source to fix (e.g., "typescript", "eslint").'),
        dryRun: z.boolean().optional().describe('If true, only reports what would be fixed without making changes. Default is false.')
      })
    },
    'summary': {
      description: 'Get a summary of diagnostics across the workspace',
      schema: z.object({
        detailed: z.boolean().optional().describe('If true, includes detailed breakdown by file, severity, and source. Default is false.')
      }),
      inputSchema: z.object({
        detailed: z.boolean().optional().describe('If true, includes detailed breakdown by file, severity, and source. Default is false.')
      })
    }
  };

  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      if (!actionName || !this.actions[actionName]) {
        return {
          success: false,
          error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,
          toolId: this.id,
          actionName
        };
      }

      switch (actionName) {
        case 'get':
          return this.executeGet(input, actionName);
        case 'analyze':
          return this.executeAnalyze(input, actionName);
        case 'fix':
          return this.executeFix(input, actionName);
        case 'summary':
          return this.executeSummary(input, actionName);
        default:
          return {
            success: false,
            error: `Unknown action: ${actionName}`,
            toolId: this.id,
            actionName
          };
      }
    } catch (error: any) {
      Logger.instance.error(`Error executing diagnostics action ${actionName}:`, error);
      return {
        success: false,
        error: `Diagnostics tool failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Get diagnostics for specified files or all files
     */
  private async executeGet(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const paths = input.paths as string[] | undefined;
      const severity = input.severity as DiagnosticSeverity | 'all' | undefined;
      const source = input.source as string | undefined;
      const limit = input.limit as number | undefined;

      // Get diagnostics
      const diagnostics = await this.getDiagnostics(paths);

      // Apply filters
      let filteredDiagnostics = diagnostics;

      if (severity && severity !== 'all') {
        filteredDiagnostics = filteredDiagnostics.filter(d => d.severity === severity);
      }

      if (source) {
        filteredDiagnostics = filteredDiagnostics.filter(d => d.source === source);
      }

      // Apply limit
      if (limit !== undefined && limit > 0) {
        filteredDiagnostics = filteredDiagnostics.slice(0, limit);
      }

      return {
        success: true,
        output: {
          diagnostics: filteredDiagnostics,
          count: filteredDiagnostics.length,
          totalCount: diagnostics.length
        },
        toolId: this.id,
        actionName,
        metadata: {
          paths,
          severity,
          source,
          limit
        }
      };
    } catch (error: any) {
      Logger.instance.error('Error getting diagnostics:', error);
      return {
        success: false,
        error: `Failed to get diagnostics: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Analyze diagnostics and provide insights
     */
  private async executeAnalyze(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const paths = input.paths as string[] | undefined;
      const groupBy = (input.groupBy as string) || 'file';

      // Get diagnostics
      const diagnostics = await this.getDiagnostics(paths);

      // Group diagnostics
      const groupedDiagnostics = this.groupDiagnostics(diagnostics, groupBy);

      // Generate insights
      const insights = this.generateInsights(diagnostics, groupedDiagnostics, groupBy);

      return {
        success: true,
        output: {
          groupedDiagnostics,
          insights,
          totalCount: diagnostics.length
        },
        toolId: this.id,
        actionName,
        metadata: {
          paths,
          groupBy
        }
      };
    } catch (error: any) {
      Logger.instance.error('Error analyzing diagnostics:', error);
      return {
        success: false,
        error: `Failed to analyze diagnostics: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Attempt to fix diagnostics automatically
     */
  private async executeFix(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const paths = input.paths as string[] | undefined;
      const severity = input.severity as DiagnosticSeverity | 'all' | undefined;
      const source = input.source as string | undefined;
      const dryRun = input.dryRun === true;

      // Get diagnostics
      const diagnostics = await this.getDiagnostics(paths);

      // Apply filters
      let filteredDiagnostics = diagnostics;

      if (severity && severity !== 'all') {
        filteredDiagnostics = filteredDiagnostics.filter(d => d.severity === severity);
      }

      if (source) {
        filteredDiagnostics = filteredDiagnostics.filter(d => d.source === source);
      }

      // Attempt to fix diagnostics
      const fixResults = await this.fixDiagnostics(filteredDiagnostics, dryRun);

      return {
        success: true,
        output: {
          fixResults,
          fixedCount: fixResults.filter(r => r.fixed).length,
          totalCount: filteredDiagnostics.length,
          dryRun
        },
        toolId: this.id,
        actionName,
        metadata: {
          paths,
          severity,
          source,
          dryRun
        }
      };
    } catch (error: any) {
      Logger.instance.error('Error fixing diagnostics:', error);
      return {
        success: false,
        error: `Failed to fix diagnostics: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Get a summary of diagnostics across the workspace
     */
  private async executeSummary(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const detailed = input.detailed === true;

      // Get all diagnostics
      const diagnostics = await this.getDiagnostics();

      // Generate summary
      const summary = this.generateSummary(diagnostics, detailed);

      return {
        success: true,
        output: summary,
        toolId: this.id,
        actionName,
        metadata: {
          detailed,
          totalCount: diagnostics.length
        }
      };
    } catch (error: any) {
      Logger.instance.error('Error generating diagnostics summary:', error);
      return {
        success: false,
        error: `Failed to generate diagnostics summary: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Get diagnostics from VS Code for specified files or all files
     */
  private async getDiagnostics(paths?: string[]): Promise<DiagnosticInfo[]> {
    // Get all diagnostics from VS Code
    const allDiagnostics: DiagnosticInfo[] = [];

    // If specific paths are provided, get diagnostics only for those files
    if (paths && paths.length > 0) {
      for (const filePath of paths) {
        try {
          const uri = this.resolveWorkspacePath(filePath);
          if (!uri) {
            Logger.instance.warn(`Could not resolve file path: ${filePath}`);
            continue;
          }

          const diagnostics = vscode.languages.getDiagnostics(uri);
          allDiagnostics.push(...this.convertDiagnostics(diagnostics, filePath));
        } catch (error) {
          Logger.instance.error(`Error getting diagnostics for ${filePath}:`, error);
        }
      }
    } else {
      // Get diagnostics for all files
      for (const [uri, diagnostics] of vscode.languages.getDiagnostics()) {
        const filePath = uri.fsPath;
        allDiagnostics.push(...this.convertDiagnostics(diagnostics, filePath));
      }
    }

    return allDiagnostics;
  }

  /**
     * Convert VS Code diagnostics to our DiagnosticInfo format
     */
  private convertDiagnostics(diagnostics: vscode.Diagnostic[], filePath: string): DiagnosticInfo[] {
    return diagnostics.map(d => {
      // Map VS Code severity to our severity enum
      let severity: DiagnosticSeverity;
      switch (d.severity) {
        case vscode.DiagnosticSeverity.Error:
          severity = DiagnosticSeverity.Error;
          break;
        case vscode.DiagnosticSeverity.Warning:
          severity = DiagnosticSeverity.Warning;
          break;
        case vscode.DiagnosticSeverity.Information:
          severity = DiagnosticSeverity.Information;
          break;
        case vscode.DiagnosticSeverity.Hint:
          severity = DiagnosticSeverity.Hint;
          break;
        default:
          severity = DiagnosticSeverity.Information;
      }

      // Extract source from diagnostic
      let source: string | undefined;
      if (d.source) {
        source = d.source.toLowerCase();
        // Map common sources to our enum
        if (source.includes('typescript') || source.includes('ts')) {
          source = DiagnosticSource.TypeScript;
        } else if (source.includes('eslint')) {
          source = DiagnosticSource.ESLint;
        } else if (source.includes('stylelint')) {
          source = DiagnosticSource.Stylelint;
        } else if (source.includes('compiler')) {
          source = DiagnosticSource.Compiler;
        } else if (source.includes('linter')) {
          source = DiagnosticSource.Linter;
        } else if (source.includes('extension')) {
          source = DiagnosticSource.Extension;
        } else if (source.includes('vscode')) {
          source = DiagnosticSource.VSCode;
        } else {
          source = DiagnosticSource.Other;
        }
      }

      // Convert related information
      const relatedInformation = d.relatedInformation?.map(ri => ({
        message: ri.message,
        file: ri.location.uri.fsPath,
        startLine: ri.location.range.start.line + 1, // Convert to 1-based
        startCharacter: ri.location.range.start.character + 1 // Convert to 1-based
      }));

      // Convert tags
      const tags: string[] = [];
      if (d.tags) {
        for (const tag of d.tags) {
          if (tag === vscode.DiagnosticTag.Unnecessary) {
            tags.push('unnecessary');
          } else if (tag === vscode.DiagnosticTag.Deprecated) {
            tags.push('deprecated');
          }
        }
      }

      return {
        message: d.message,
        severity,
        source,
        code: d.code ? String(d.code) : undefined,
        startLine: d.range.start.line + 1, // Convert to 1-based
        startCharacter: d.range.start.character + 1, // Convert to 1-based
        endLine: d.range.end.line + 1, // Convert to 1-based
        endCharacter: d.range.end.character + 1, // Convert to 1-based
        relatedInformation,
        tags,
        filePath
      };
    });
  }

  /**
     * Group diagnostics by the specified property
     */
  private groupDiagnostics(diagnostics: DiagnosticInfo[], groupBy: string): Record<string, DiagnosticInfo[]> {
    const grouped: Record<string, DiagnosticInfo[]> = {};

    for (const diagnostic of diagnostics) {
      let key: string;

      switch (groupBy) {
        case 'file':
          key = diagnostic.filePath;
          break;
        case 'severity':
          key = diagnostic.severity;
          break;
        case 'source':
          key = diagnostic.source || 'unknown';
          break;
        case 'code':
          key = diagnostic.code ? String(diagnostic.code) : 'unknown';
          break;
        default:
          key = diagnostic.filePath;
      }

      if (!grouped[key]) {
        grouped[key] = [];
      }

      grouped[key].push(diagnostic);
    }

    return grouped;
  }

  /**
     * Generate insights from diagnostics
     */
  private generateInsights(
    diagnostics: DiagnosticInfo[],
    groupedDiagnostics: Record<string, DiagnosticInfo[]>,
    groupBy: string
  ): any {
    const insights: any = {
      totalCount: diagnostics.length,
      severityCounts: {
        [DiagnosticSeverity.Error]: 0,
        [DiagnosticSeverity.Warning]: 0,
        [DiagnosticSeverity.Information]: 0,
        [DiagnosticSeverity.Hint]: 0
      },
      sourceCounts: {},
      mostCommonIssues: [],
      recommendations: []
    };

    // Count by severity
    for (const diagnostic of diagnostics) {
      insights.severityCounts[diagnostic.severity]++;

      // Count by source
      const source = diagnostic.source || 'unknown';
      insights.sourceCounts[source] = (insights.sourceCounts[source] || 0) + 1;
    }

    // Find most common issues
    const issueMap = new Map<string, { count: number, examples: DiagnosticInfo[] }>();

    for (const diagnostic of diagnostics) {
      const key = `${diagnostic.source || 'unknown'}-${diagnostic.code || 'unknown'}`;

      if (!issueMap.has(key)) {
        issueMap.set(key, { count: 0, examples: [] });
      }

      const issue = issueMap.get(key)!;
      issue.count++;

      if (issue.examples.length < 3) {
        issue.examples.push(diagnostic);
      }
    }

    // Sort issues by count
    insights.mostCommonIssues = Array.from(issueMap.entries())
      .map(([key, { count, examples }]) => ({
        key,
        count,
        examples
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Generate recommendations
    if (insights.severityCounts[DiagnosticSeverity.Error] > 0) {
      insights.recommendations.push('Fix all errors before proceeding with other changes.');
    }

    if (insights.mostCommonIssues.length > 0) {
      insights.recommendations.push(`Focus on fixing the most common issue first: ${insights.mostCommonIssues[0].examples[0].message}`);
    }

    // Add group-specific insights
    switch (groupBy) {
      case 'file':
        // Find files with most issues
        insights.filesWithMostIssues = Object.entries(groupedDiagnostics)
          .map(([file, diags]) => ({
            file,
            count: diags.length,
            errorCount: diags.filter(d => d.severity === DiagnosticSeverity.Error).length
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);

        if (insights.filesWithMostIssues.length > 0) {
          insights.recommendations.push(`Prioritize fixing issues in ${insights.filesWithMostIssues[0].file} which has ${insights.filesWithMostIssues[0].count} issues.`);
        }
        break;

      case 'severity':
        // Add severity-specific recommendations
        if (insights.severityCounts[DiagnosticSeverity.Warning] > 10) {
          insights.recommendations.push('Consider addressing warnings to improve code quality.');
        }
        break;

      case 'source':
        // Find most problematic sources
        insights.mostProblematicSources = Object.entries(groupedDiagnostics)
          .map(([source, diags]) => ({
            source,
            count: diags.length,
            errorCount: diags.filter(d => d.severity === DiagnosticSeverity.Error).length
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 3);

        if (insights.mostProblematicSources.length > 0) {
          insights.recommendations.push(`Consider running a linter or formatter to address ${insights.mostProblematicSources[0].source} issues.`);
        }
        break;
    }

    return insights;
  }

  /**
     * Map our diagnostic severity to VS Code's DiagnosticSeverity
     */
  private mapSeverityToVSCode(severity: DiagnosticSeverity): vscode.DiagnosticSeverity {
    switch (severity) {
      case DiagnosticSeverity.Error:
        return vscode.DiagnosticSeverity.Error;
      case DiagnosticSeverity.Warning:
        return vscode.DiagnosticSeverity.Warning;
      case DiagnosticSeverity.Information:
        return vscode.DiagnosticSeverity.Information;
      case DiagnosticSeverity.Hint:
        return vscode.DiagnosticSeverity.Hint;
      default:
        return vscode.DiagnosticSeverity.Information;
    }
  }

  /**
     * Resolve a workspace path to a vscode.Uri
     */
  private resolveWorkspacePath(filePath: string): vscode.Uri | undefined {
    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
      const workspaceRoot = vscode.workspace.workspaceFolders[0].uri;

      // Check if it's already absolute
      try {
        const uri = vscode.Uri.parse(filePath);
        if (uri.scheme) return uri; // Already absolute
      } catch (e) {
        // Ignore parsing errors, treat as relative
      }

      // If relative, join with workspace root
      return vscode.Uri.joinPath(workspaceRoot, filePath);
    } else if (vscode.Uri.parse(filePath).scheme) {
      // Absolute path outside workspace
      return vscode.Uri.parse(filePath);
    }

    // Relative path but no workspace open
    Logger.instance.warn(`Cannot resolve relative path "${filePath}" without an open workspace folder.`);
    return undefined;
  }

  /**
     * Attempt to fix diagnostics automatically
     */
  private async fixDiagnostics(diagnostics: DiagnosticInfo[], dryRun: boolean): Promise<FixResult[]> {
    const results: FixResult[] = [];

    for (const diagnostic of diagnostics) {
      try {
        // Create a VS Code range from the diagnostic
        const range = new vscode.Range(
          diagnostic.startLine - 1, // Convert to 0-based
          diagnostic.startCharacter - 1, // Convert to 0-based
          diagnostic.endLine - 1, // Convert to 0-based
          diagnostic.endCharacter - 1 // Convert to 0-based
        );

        // Create a VS Code diagnostic
        const vscodeDiagnostic = new vscode.Diagnostic(
          range,
          diagnostic.message,
          this.mapSeverityToVSCode(diagnostic.severity)
        );

        if (diagnostic.code) {
          vscodeDiagnostic.code = diagnostic.code;
        }

        if (diagnostic.source) {
          vscodeDiagnostic.source = diagnostic.source;
        }

        // Try to get code actions for the diagnostic
        const uri = this.resolveWorkspacePath(diagnostic.filePath);
        if (!uri) {
          results.push({
            diagnostic,
            fixed: false,
            reason: 'Could not resolve file path'
          });
          continue;
        }

        const document = await vscode.workspace.openTextDocument(uri);
        const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>(
          'vscode.executeCodeActionProvider',
          uri,
          range,
          vscode.CodeActionKind.QuickFix.value
        );

        if (!codeActions || codeActions.length === 0) {
          results.push({
            diagnostic,
            fixed: false,
            reason: 'No code actions available'
          });
          continue;
        }

        // Find a code action that can fix the diagnostic
        const fixAction = codeActions.find(action =>
          action.diagnostics?.some(d =>
            d.message === diagnostic.message &&
            d.range.isEqual(range)
          )
        );

        if (!fixAction) {
          results.push({
            diagnostic,
            fixed: false,
            reason: 'No matching code action found'
          });
          continue;
        }

        // Apply the fix if not in dry run mode
        if (!dryRun && fixAction.edit) {
          const success = await vscode.workspace.applyEdit(fixAction.edit);

          results.push({
            diagnostic,
            fixed: success,
            action: fixAction.title,
            reason: success ? 'Fixed' : 'Failed to apply edit'
          });
        } else {
          results.push({
            diagnostic,
            fixed: false,
            action: fixAction.title,
            reason: dryRun ? 'Dry run' : 'No edit available'
          });
        }
      } catch (error: any) {
        Logger.instance.error('Error fixing diagnostic:', error);
        results.push({
          diagnostic,
          fixed: false,
          reason: `Error: ${error.message || error}`
        });
      }
    }

    return results;
  }

  /**
     * Generate a summary of diagnostics
     */
  private generateSummary(diagnostics: DiagnosticInfo[], detailed: boolean): any {
    // Basic summary
    const summary: any = {
      totalCount: diagnostics.length,
      severityCounts: {
        [DiagnosticSeverity.Error]: 0,
        [DiagnosticSeverity.Warning]: 0,
        [DiagnosticSeverity.Information]: 0,
        [DiagnosticSeverity.Hint]: 0
      },
      fileCount: new Set(diagnostics.map(d => d.filePath)).size,
      sourceBreakdown: {}
    };

    // Count by severity
    for (const diagnostic of diagnostics) {
      summary.severityCounts[diagnostic.severity]++;

      // Count by source
      const source = diagnostic.source || 'unknown';
      summary.sourceBreakdown[source] = (summary.sourceBreakdown[source] || 0) + 1;
    }

    // Add detailed information if requested
    if (detailed) {
      // Group by file
      const byFile = this.groupDiagnostics(diagnostics, 'file');

      summary.fileBreakdown = Object.entries(byFile).map(([file, diags]) => ({
        file,
        count: diags.length,
        errorCount: diags.filter(d => d.severity === DiagnosticSeverity.Error).length,
        warningCount: diags.filter(d => d.severity === DiagnosticSeverity.Warning).length,
        infoCount: diags.filter(d => d.severity === DiagnosticSeverity.Information).length,
        hintCount: diags.filter(d => d.severity === DiagnosticSeverity.Hint).length
      })).sort((a, b) => b.count - a.count);

      // Find most common issues
      const issueMap = new Map<string, { count: number, message: string }>();

      for (const diagnostic of diagnostics) {
        const key = `${diagnostic.source || 'unknown'}-${diagnostic.code || 'unknown'}`;

        if (!issueMap.has(key)) {
          issueMap.set(key, { count: 0, message: diagnostic.message });
        }

        issueMap.get(key)!.count++;
      }

      summary.commonIssues = Array.from(issueMap.entries())
        .map(([key, { count, message }]) => ({
          key,
          count,
          message
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);
    }

    return summary;
  }


}

// Export a singleton instance of the diagnostics tool
export const diagnosticsTool = new DiagnosticsTool();