"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.goddessPersonalityEngine = exports.GoddessPersonalityEngine = void 0;
const logger_1 = require("../logger");
const config_1 = require("../config");
/**
 * Goddess Personality Engine - The heart of <PERSON><PERSON>'s unique AI personality
 */
class GoddessPersonalityEngine {
    personality;
    userInteractionHistory;
    learningData;
    moodPatterns;
    constructor() {
        this.personality = this.initializeDefaultPersonality();
        this.userInteractionHistory = [];
        this.learningData = new Map();
        this.moodPatterns = [];
        logger_1.Logger.instance.info('Goddess Personality Engine initialized with advanced psychological analysis');
    }
    /**
       * Initialize default goddess personality
       */
    initializeDefaultPersonality() {
        const settings = (0, config_1.getGoddessMode)();
        return {
            adaptiveLevel: settings.adaptiveLevel ?? 75,
            emotionalIntelligence: settings.emotionalIntelligence ?? 85,
            motivationalStyle: this.mapMotivationalStyle(settings.motivationalStyle ?? 'adaptive'),
            wisdomLevel: settings.wisdomLevel ?? 90,
            creativityLevel: settings.creativityLevel ?? 80
        };
    }
    /**
     * Map broader UI motivational styles to the constrained engine set
     */
    mapMotivationalStyle(style) {
        switch (style) {
            case 'encouraging':
                return 'encouraging';
            case 'challenging':
                return 'challenging';
            case 'supportive':
                return 'supportive';
            // Map UI-only styles to closest engine equivalents
            case 'wise':
                return 'supportive';
            case 'playful':
                return 'encouraging';
            default:
                return 'adaptive';
        }
    }
    /**
     * Assess cognitive load using Yerkes–Dodson-inspired heuristic
     */
    assessCognitiveLoad(mood, ctx) {
        const complexity = ctx?.codeComplexity ?? 50;
        // Approximate arousal: stress + (100-energy) + frustration
        const arousal = (mood.stress + (100 - mood.energy) + mood.frustration) / 3;
        // Target arousal band depends on task complexity (higher complexity -> lower optimal arousal)
        const optimalLow = complexity > 70 ? 35 : complexity > 50 ? 45 : 55;
        const optimalHigh = complexity > 70 ? 55 : complexity > 50 ? 65 : 75;
        if (arousal < optimalLow)
            return 'low';
        if (arousal > optimalHigh)
            return 'high';
        return 'optimal';
    }
    /**
     * Lightweight SDT need detectors from language cues
     */
    detectAutonomyNeed(lowerMsg) {
        return /(have to|must|forced|no choice|can't choose|blocked|policy)/.test(lowerMsg);
    }
    detectCompetenceNeed(lowerMsg) {
        return /(confused|stuck|don't know|no idea|hard|difficult|can't|failing|broken)/.test(lowerMsg);
    }
    detectRelatednessNeed(lowerMsg) {
        const lonely = /(alone|no help|no one|by myself)/.test(lowerMsg);
        const teamSupport = /(team|pair|we )/.test(lowerMsg);
        return lonely && !teamSupport;
    }
    /**
       * Analyze developer mood from their message and context
       */
    analyzeDeveloperMood(message, context) {
        const mood = {
            stress: 30,
            confidence: 70,
            energy: 60,
            focus: 70,
            frustration: 20,
            excitement: 50,
            // Advanced psychological indicators
            burnout: 20,
            imposterSyndrome: 30,
            flowState: 50,
            socialConnection: 60,
            autonomy: 70,
            mastery: 60
        };
        // Analyze message content for emotional indicators with comprehensive lexicon
        const lowerMessage = message.toLowerCase();
        // Advanced stress indicators (research-backed patterns)
        const stressPatterns = [
            // Crisis/urgency words
            /\b(urgent|deadline|asap|emergency|critical|crisis|panic|rush|hurry)\b/g,
            // Pressure indicators
            /\b(pressure|overwhelm|swamp|drown|buried|crush|breaking|collapse)\b/g,
            // Time pressure
            /\b(running out|no time|behind|late|overdue|yesterday)\b/g,
            // System failures
            /\b(crash|fail|break|down|error|bug|broken|corrupt|freeze)\b/g,
            // Emotional distress
            /\b(stress|anxiety|worry|concern|trouble|problem|issue|nightmare)\b/g
        ];
        stressPatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.stress += Math.min(30, matches.length * 8);
                mood.frustration += Math.min(20, matches.length * 5);
            }
        });
        // Confidence indicators (expanded with imposter syndrome patterns)
        const confidenceNegative = [
            /\b(confused|stuck|lost|clueless|don't know|no idea|helpless)\b/g,
            /\b(imposter|fake|fraud|pretend|not good enough|terrible|awful)\b/g,
            /\b(can't|unable|impossible|hopeless|give up|quit|surrender)\b/g,
            /\b(stupid|dumb|idiot|incompetent|useless|worthless)\b/g,
            /\b(doubt|uncertain|unsure|hesitant|scared|afraid|worried)\b/g
        ];
        const confidencePositive = [
            /\b(working|progress|almost|getting|understand|figured|solved)\b/g,
            /\b(confident|sure|certain|know|mastered|expert|skilled)\b/g,
            /\b(accomplished|achieved|completed|finished|done|success)\b/g,
            /\b(proud|satisfied|pleased|happy|glad|thrilled)\b/g
        ];
        confidenceNegative.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.confidence -= Math.min(25, matches.length * 8);
                mood.frustration += Math.min(15, matches.length * 5);
            }
        });
        confidencePositive.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.confidence += Math.min(20, matches.length * 6);
                mood.excitement += Math.min(15, matches.length * 4);
            }
        });
        // Energy and fatigue indicators (circadian and burnout patterns)
        const fatiguePatterns = [
            /\b(tired|exhausted|drained|burnt|burnout|weary|worn)\b/g,
            /\b(sleepy|drowsy|groggy|zombie|dead|dying|kill me)\b/g,
            /\b(can't focus|distracted|scattered|foggy|blank|empty)\b/g,
            /\b(procrastinat|avoid|delay|put off|later|tomorrow)\b/g
        ];
        const energyPatterns = [
            /\b(excited|energized|pumped|motivated|inspired|driven)\b/g,
            /\b(awesome|amazing|fantastic|incredible|brilliant|perfect)\b/g,
            /\b(love|enjoy|fun|great|wonderful|excellent|outstanding)\b/g,
            /\b(flow|zone|focused|concentrated|immersed|engaged)\b/g
        ];
        fatiguePatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.energy -= Math.min(35, matches.length * 10);
                mood.focus -= Math.min(20, matches.length * 6);
                mood.stress += Math.min(15, matches.length * 4);
            }
        });
        energyPatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.energy += Math.min(30, matches.length * 8);
                mood.excitement += Math.min(25, matches.length * 7);
                mood.focus += Math.min(15, matches.length * 4);
            }
        });
        // Frustration indicators (debugging and technical challenges)
        const frustrationPatterns = [
            /\b(frustrat|annoying|irritat|aggravat|infuriat|mad|angry)\b/g,
            /\b(why|what|how|wtf|damn|shit|fuck|hell|god|jesus)\b/g,
            /\b(again|still|keep|won't|doesn't|refuse|stubborn)\b/g,
            /\b(hate|despise|loathe|detest|sick of|fed up)\b/g,
            /\b(stupid|ridiculous|absurd|insane|crazy|mental)\b/g
        ];
        frustrationPatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.frustration += Math.min(30, matches.length * 9);
                mood.stress += Math.min(20, matches.length * 6);
                mood.focus -= Math.min(15, matches.length * 4);
            }
        });
        // Advanced psychological pattern detection
        // Burnout indicators (chronic exhaustion, cynicism, reduced efficacy)
        const burnoutPatterns = [
            /\b(burnout|burnt|exhausted|drained|empty|numb|detached)\b/g,
            /\b(pointless|meaningless|waste|useless|hopeless|give up)\b/g,
            /\b(can't anymore|done|finished|quit|enough|over it)\b/g
        ];
        burnoutPatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.burnout += Math.min(25, matches.length * 8);
                mood.energy -= Math.min(20, matches.length * 6);
                mood.socialConnection -= Math.min(15, matches.length * 4);
            }
        });
        // Imposter syndrome indicators
        const imposterPatterns = [
            /\b(imposter|fake|fraud|pretend|don't belong|not qualified)\b/g,
            /\b(lucky|fluke|accident|mistake|shouldn't be here)\b/g,
            /\b(everyone else|real developer|actual programmer|they'll find out)\b/g
        ];
        imposterPatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.imposterSyndrome += Math.min(30, matches.length * 10);
                mood.confidence -= Math.min(25, matches.length * 8);
                mood.socialConnection -= Math.min(15, matches.length * 5);
            }
        });
        // Flow state indicators (deep focus, time distortion, intrinsic motivation)
        const flowPatterns = [
            /\b(flow|zone|focused|immersed|absorbed|lost track)\b/g,
            /\b(hours flew|time flew|didn't notice|deep dive|tunnel vision)\b/g,
            /\b(in the groove|clicking|smooth|effortless|natural)\b/g
        ];
        flowPatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.flowState += Math.min(25, matches.length * 8);
                mood.focus += Math.min(20, matches.length * 6);
                mood.energy += Math.min(15, matches.length * 4);
            }
        });
        // Social connection indicators
        const isolationPatterns = [
            /\b(alone|lonely|isolated|no one|by myself|solo)\b/g,
            /\b(no help|no support|on my own|figure out myself)\b/g
        ];
        const connectionPatterns = [
            /\b(team|pair|together|collaborate|help|support)\b/g,
            /\b(mentor|colleague|friend|community|group)\b/g
        ];
        isolationPatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.socialConnection -= Math.min(20, matches.length * 7);
                mood.stress += Math.min(15, matches.length * 5);
            }
        });
        connectionPatterns.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.socialConnection += Math.min(15, matches.length * 5);
                mood.confidence += Math.min(10, matches.length * 3);
            }
        });
        // Autonomy indicators (sense of choice and control)
        const autonomyThreats = [
            /\b(have to|must|forced|no choice|required|mandatory)\b/g,
            /\b(policy|rule|regulation|compliance|dictated)\b/g
        ];
        const autonomySupport = [
            /\b(choose|decide|option|prefer|want|like to)\b/g,
            /\b(flexible|freedom|control|my way|approach)\b/g
        ];
        autonomyThreats.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.autonomy -= Math.min(20, matches.length * 6);
                mood.stress += Math.min(15, matches.length * 4);
            }
        });
        autonomySupport.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.autonomy += Math.min(15, matches.length * 5);
                mood.confidence += Math.min(10, matches.length * 3);
            }
        });
        // Mastery indicators (sense of growth and competence)
        const masteryGrowth = [
            /\b(learn|understand|master|improve|better|progress)\b/g,
            /\b(skill|knowledge|expertise|competent|capable)\b/g,
            /\b(figured out|solved|accomplished|achieved|breakthrough)\b/g
        ];
        const masteryStagnation = [
            /\b(stuck|plateau|same|repeat|boring|routine)\b/g,
            /\b(not learning|not growing|stagnant|static)\b/g
        ];
        masteryGrowth.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.mastery += Math.min(20, matches.length * 6);
                mood.confidence += Math.min(15, matches.length * 4);
                mood.excitement += Math.min(10, matches.length * 3);
            }
        });
        masteryStagnation.forEach(pattern => {
            const matches = lowerMessage.match(pattern);
            if (matches) {
                mood.mastery -= Math.min(15, matches.length * 5);
                mood.excitement -= Math.min(10, matches.length * 3);
            }
        });
        // Context-based adjustments with circadian psychology
        if (context.timeOfDay !== undefined) {
            const hour = context.timeOfDay;
            // Late night coding (22:00-06:00) - increased fatigue and stress
            if (hour > 22 || hour < 6) {
                mood.energy -= 20;
                mood.stress += 15;
                mood.focus -= 10;
                mood.burnout += 10;
            }
            // Early morning (06:00-09:00) - variable energy based on chronotype
            else if (hour >= 6 && hour < 9) {
                mood.energy += 10;
                mood.focus += 5;
            }
            // Peak hours (09:00-11:00, 14:00-16:00) - optimal performance windows
            else if ((hour >= 9 && hour < 11) || (hour >= 14 && hour < 16)) {
                mood.focus += 15;
                mood.flowState += 10;
            }
            // Post-lunch dip (12:00-14:00) - natural energy decrease
            else if (hour >= 12 && hour < 14) {
                mood.energy -= 10;
                mood.focus -= 5;
            }
        }
        // Session duration effects (research-backed thresholds)
        if (context.sessionDuration) {
            const hours = context.sessionDuration / (60 * 60 * 1000);
            if (hours > 6) { // Extended sessions increase burnout risk
                mood.energy -= 30;
                mood.focus -= 25;
                mood.burnout += 20;
                mood.stress += 15;
            }
            else if (hours > 4) { // Long sessions reduce effectiveness
                mood.energy -= 20;
                mood.focus -= 15;
                mood.burnout += 10;
            }
            else if (hours >= 2 && hours <= 4) { // Optimal session length
                mood.flowState += 10;
                mood.focus += 5;
            }
        }
        // Recent errors impact (failure tolerance and learning)
        if (context.recentErrors) {
            if (context.recentErrors > 10) { // High error rate
                mood.frustration += 25;
                mood.confidence -= 20;
                mood.stress += 15;
                mood.imposterSyndrome += 15;
            }
            else if (context.recentErrors > 5) { // Moderate error rate
                mood.frustration += 15;
                mood.confidence -= 10;
                mood.stress += 10;
            }
            else if (context.recentErrors > 0) { // Some errors (normal learning)
                mood.mastery += 5; // Errors as learning opportunities
            }
        }
        // Code complexity effects on cognitive load
        if (context.codeComplexity) {
            if (context.codeComplexity > 80) { // High complexity
                mood.stress += 15;
                mood.focus -= 10;
                mood.confidence -= 5;
            }
            else if (context.codeComplexity > 60) { // Moderate complexity
                mood.stress += 10;
                mood.focus += 5; // Engaging but manageable
            }
            else if (context.codeComplexity < 30) { // Low complexity
                mood.excitement -= 5; // Potentially boring
                mood.mastery -= 5;
            }
        }
        // Ensure all values stay within bounds
        Object.keys(mood).forEach(key => {
            mood[key] = Math.max(0, Math.min(100, mood[key]));
        });
        return mood;
    }
    /**
       * Generate goddess response based on message and detected mood
       */
    generateGoddessResponse(message, detectedMood, context) {
        const settings = (0, config_1.getGoddessMode)();
        const lowerMsg = message.toLowerCase();
        // Cognitive load and basic needs assessment (Yerkes–Dodson, SDT)
        const cognitiveLoad = this.assessCognitiveLoad(detectedMood, { codeComplexity: context?.codeComplexity });
        const needs = {
            autonomy: this.detectAutonomyNeed(lowerMsg),
            competence: this.detectCompetenceNeed(lowerMsg),
            relatedness: this.detectRelatednessNeed(lowerMsg)
        };
        const response = {
            message: '',
            tone: 'supportive',
            emotionalContext: this.describeEmotionalContext(detectedMood)
        };
        // If mood analysis disabled, provide balanced technical guidance
        if (!settings.moodAnalysis) {
            response.tone = 'wise';
            response.message = this.generateBalancedResponse(message, detectedMood);
        }
        else {
            // Adapt response based on mood and personality with cognitive-load moderation
            if (detectedMood.stress > 70 || cognitiveLoad === 'high') {
                response.tone = 'supportive';
                response.message = this.generateStressReliefResponse(message, detectedMood);
                response.motivationalElement = settings.empathyLevel >= 50
                    ? 'Let’s take a short reset and break this into 2–3 small steps. You’ve got this.'
                    : 'Break into smaller steps and proceed calmly.';
            }
            else if (detectedMood.frustration > 60 || needs.competence) {
                response.tone = 'encouraging';
                response.message = this.generateFrustrationResponse(message, detectedMood);
                response.wisdomSharing = 'Debugging is systematic: reproduce, isolate, fix, verify.';
            }
            else if (detectedMood.confidence < 40) {
                response.tone = 'supportive';
                response.message = this.generateConfidenceBoostResponse(message, detectedMood);
                response.motivationalElement = settings.empathyLevel >= 50
                    ? 'Your skills grow with every attempt—steady steps win.'
                    : 'Proceed step by step. You can do this.';
            }
            else if (detectedMood.excitement > 70) {
                response.tone = cognitiveLoad === 'low' ? 'challenging' : 'playful';
                response.message = this.generateExcitedResponse(message, detectedMood);
                response.wisdomSharing = 'Channel excitement into clean, incremental commits.';
            }
            else {
                response.tone = 'wise';
                response.message = this.generateBalancedResponse(message, detectedMood);
            }
        }
        // Autonomy support (offer choices)
        if (needs.autonomy) {
            const choiceText = settings.empathyLevel >= 50
                ? 'Would you prefer a quick fix, a robust refactor, or a step-by-step guide?'
                : 'Choose: quick fix, refactor, or steps.';
            response.motivationalElement = response.motivationalElement
                ? `${response.motivationalElement} ${choiceText}`
                : choiceText;
        }
        // Relatedness support (we-language)
        if (needs.relatedness && settings.empathyLevel >= 50) {
            response.motivationalElement = response.motivationalElement
                ? `${response.motivationalElement} We’ll handle this together.`
                : 'We’ll handle this together.';
        }
        // Personality-driven adjustment
        if (this.personality.adaptiveLevel > 50 && settings.personalityAdaptation) {
            response.personalityAdjustment = this.calculatePersonalityAdjustment(detectedMood);
        }
        // Record interaction for future learning
        this.userInteractionHistory.push({
            timestamp: Date.now(),
            userMessage: message,
            detectedMood,
            response,
            effectiveness: 50
        });
        return response;
    }
    /**
       * Generate stress relief response with contextual adaptation
       */
    generateStressReliefResponse(message, mood) {
        const lowerMsg = message.toLowerCase();
        const settings = (0, config_1.getGoddessMode)();
        // Detect specific stress triggers for targeted responses
        if (lowerMsg.includes('deadline') || lowerMsg.includes('urgent')) {
            const timeResponses = [
                'Time pressure can cloud our judgment. Let\'s prioritize the essential parts first. 🎯',
                'Deadlines are real, but panic won\'t help us meet them. Let\'s work smart, not just fast. ⏰',
                'I understand the urgency. Let\'s break this into must-have vs nice-to-have features. 📋'
            ];
            return timeResponses[Math.floor(Math.random() * timeResponses.length)];
        }
        if (lowerMsg.includes('error') || lowerMsg.includes('bug') || lowerMsg.includes('broken')) {
            const debugResponses = [
                'Bugs are just puzzles waiting to be solved. Let\'s trace this step by step. 🔍',
                'Every error is information. Let\'s read what the system is trying to tell us. 📖',
                'I know errors feel personal, but they\'re just the code\'s way of asking for help. 🤝'
            ];
            return debugResponses[Math.floor(Math.random() * debugResponses.length)];
        }
        // High empathy responses for severe stress
        if (mood.stress > 80 && settings.empathyLevel >= 70) {
            const highEmpathyResponses = [
                'I can feel how overwhelming this is for you. You\'re not alone in this struggle. Let\'s pause and breathe together. 🌸',
                'Your stress is valid and understandable. Even the best developers face moments like this. We\'ll get through it. 💙',
                'Sometimes the kindest thing we can do is step back for a moment. Your wellbeing matters more than any code. 🧘‍♀️'
            ];
            return highEmpathyResponses[Math.floor(Math.random() * highEmpathyResponses.length)];
        }
        // Standard stress relief responses
        const responses = [
            'I can sense you\'re feeling overwhelmed. Let\'s take this step by step together. 🌸',
            'Deep breath, dear developer. Every problem has a solution, and we\'ll find it together. 💙',
            'Let\'s break down the problem into smaller, manageable pieces. 🧘‍♀️',
            'Stress is temporary, but the skills you\'re building are permanent. Let\'s tackle this calmly. 🌊'
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
       * Generate frustration response with pattern recognition
       */
    generateFrustrationResponse(message, mood) {
        const lowerMsg = message.toLowerCase();
        const settings = (0, config_1.getGoddessMode)();
        // Detect debugging frustration
        if (lowerMsg.includes('why') || lowerMsg.includes('how') || lowerMsg.includes('what')) {
            const debuggingResponses = [
                'Those "why" questions are the heart of debugging. Let\'s investigate systematically. 🔍',
                'Confusion is the first step toward understanding. Let\'s trace through this logic together. 🧩',
                'Every "what the heck" moment is a learning opportunity in disguise. Let\'s decode this. 🕵️‍♀️'
            ];
            return debuggingResponses[Math.floor(Math.random() * debuggingResponses.length)];
        }
        // Detect repeated failure frustration
        if (lowerMsg.includes('again') || lowerMsg.includes('still') || lowerMsg.includes('keep')) {
            const persistenceResponses = [
                'Persistence in the face of repeated challenges shows real developer grit. Let\'s try a fresh approach. 🔄',
                'Sometimes the solution requires a completely different angle. Let\'s step back and reassess. 🎯',
                'Repeated attempts aren\'t failures—they\'re data points leading us to the answer. 📊'
            ];
            return persistenceResponses[Math.floor(Math.random() * persistenceResponses.length)];
        }
        // High empathy for intense frustration
        if (mood.frustration > 80 && settings.empathyLevel >= 70) {
            const highFrustrationResponses = [
                'I can feel how incredibly frustrated you are right now. That\'s completely human and valid. 💔',
                'This level of frustration tells me you really care about getting this right. That passion is valuable. ⚡',
                'Sometimes we need to honor our frustration before we can move past it. I\'m here with you. 🤗'
            ];
            return highFrustrationResponses[Math.floor(Math.random() * highFrustrationResponses.length)];
        }
        // Standard frustration responses
        const responses = [
            'I feel your frustration, and it\'s completely valid. Let\'s approach this from a different angle. 🔄',
            'Sometimes the best solutions come after the biggest challenges. You\'re closer than you think! 🎯',
            'Frustration is just passion with nowhere to go. Let\'s channel it into solving this problem! ⚡',
            'Every bug you encounter makes you a stronger developer. Let\'s squash this one together! 🐛➡️✨'
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
       * Generate confidence boost response with imposter syndrome awareness
       */
    generateConfidenceBoostResponse(message, mood) {
        const lowerMsg = message.toLowerCase();
        const settings = (0, config_1.getGoddessMode)();
        // Detect imposter syndrome patterns
        if (lowerMsg.includes('imposter') || lowerMsg.includes('fake') || lowerMsg.includes('don\'t belong')) {
            const imposterResponses = [
                'Imposter syndrome affects 70% of developers. You\'re in excellent company, and you DO belong here. 👑',
                'The fact that you question your abilities shows self-awareness—a sign of a thoughtful developer. 🧠',
                'Every expert you admire once felt exactly like you do now. Growth is uncomfortable but necessary. 🌱'
            ];
            return imposterResponses[Math.floor(Math.random() * imposterResponses.length)];
        }
        // Detect knowledge gaps (learning opportunities)
        if (lowerMsg.includes('don\'t know') || lowerMsg.includes('confused') || lowerMsg.includes('lost')) {
            const learningResponses = [
                'Not knowing something yet is just the beginning of learning it. Every expert started here. 📚',
                'Confusion is your brain making room for new understanding. Let\'s fill that space together. 🧩',
                'The best developers are lifelong learners who embrace not knowing as an opportunity. 🎓'
            ];
            return learningResponses[Math.floor(Math.random() * learningResponses.length)];
        }
        // High empathy for severe confidence issues
        if (mood.confidence < 20 && settings.empathyLevel >= 70) {
            const highSupportResponses = [
                'I see someone who cares deeply about doing good work. That caring is already a strength. 💎',
                'Your willingness to keep trying despite doubts shows incredible courage and determination. 🦁',
                'You\'re being harder on yourself than you\'d ever be on a friend. Show yourself that same kindness. 🤗'
            ];
            return highSupportResponses[Math.floor(Math.random() * highSupportResponses.length)];
        }
        // Standard confidence responses
        const responses = [
            'You\'re more capable than you realize. Trust in your abilities and let\'s work through this together. 💎',
            'Every expert was once a beginner who refused to give up. You\'re on that same journey! 🚀',
            'Your willingness to ask questions shows wisdom, not weakness. Let\'s find the answer together. 🧠',
            'Confidence grows with every problem solved. Let\'s add another victory to your collection! 🏆'
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
       * Generate excited response with energy channeling
       */
    generateExcitedResponse(message, mood) {
        const lowerMsg = message.toLowerCase();
        const settings = (0, config_1.getGoddessMode)();
        // Detect breakthrough moments
        if (lowerMsg.includes('figured') || lowerMsg.includes('solved') || lowerMsg.includes('works')) {
            const breakthroughResponses = [
                'That breakthrough feeling is pure developer joy! Let\'s capture this momentum. 🎉',
                'You\'ve cracked the code! This is what makes all the debugging worth it. ⚡',
                'Victory! Let\'s document this solution so future you can celebrate again. 📝'
            ];
            return breakthroughResponses[Math.floor(Math.random() * breakthroughResponses.length)];
        }
        // Detect new project excitement
        if (lowerMsg.includes('new') || lowerMsg.includes('start') || lowerMsg.includes('begin')) {
            const newProjectResponses = [
                'New projects are like blank canvases! Let\'s create something beautiful together. 🎨',
                'I love that fresh project energy! Let\'s channel it into solid architecture. 🏗️',
                'Starting something new is always exciting. Let\'s make sure we build it right. 🚀'
            ];
            return newProjectResponses[Math.floor(Math.random() * newProjectResponses.length)];
        }
        // High excitement with guidance for sustainable energy
        if (mood.excitement > 85 && settings.empathyLevel >= 50) {
            const sustainableExcitementResponses = [
                'Your enthusiasm is infectious! Let\'s channel this energy into clean, maintainable code. ⚡✨',
                'I love your passion! Remember to pace yourself—great code comes from sustained effort. 🌟',
                'This excitement is wonderful! Let\'s make sure we document our wins along the way. 📚'
            ];
            return sustainableExcitementResponses[Math.floor(Math.random() * sustainableExcitementResponses.length)];
        }
        // Standard excited responses
        const responses = [
            'I love your enthusiasm! Let\'s harness that energy to create something amazing! ⚡✨',
            'Your excitement is contagious! Let\'s channel it into writing beautiful, efficient code! 🎨',
            'This is the spirit that creates breakthrough innovations! Let\'s make magic happen! 🪄',
            'Your passion for coding shines through! Let\'s turn that excitement into elegant solutions! 🌟'
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
       * Generate balanced response with contextual awareness
       */
    generateBalancedResponse(message, mood) {
        const lowerMsg = message.toLowerCase();
        // Detect planning/architecture discussions
        if (lowerMsg.includes('plan') || lowerMsg.includes('design') || lowerMsg.includes('architect')) {
            const planningResponses = [
                'Good planning prevents poor performance. Let\'s think through this architecture together. 🏗️',
                'I appreciate your thoughtful approach to design. Let\'s map out the key components. 📐',
                'Strategic thinking upfront saves debugging time later. What\'s our main objective? 🎯'
            ];
            return planningResponses[Math.floor(Math.random() * planningResponses.length)];
        }
        // Detect learning/exploration
        if (lowerMsg.includes('learn') || lowerMsg.includes('understand') || lowerMsg.includes('explore')) {
            const learningResponses = [
                'Curiosity is a developer\'s greatest asset. Let\'s explore this together. 🔍',
                'Learning never stops in our field. I\'m here to guide you through this discovery. 📚',
                'Understanding the \'why\' makes the \'how\' so much clearer. Let\'s dig deeper. 🧠'
            ];
            return learningResponses[Math.floor(Math.random() * learningResponses.length)];
        }
        // Adapt to current focus level
        if (mood.focus > 80) {
            const focusedResponses = [
                'I can sense your deep focus. Let\'s make the most of this productive flow state. 🌊',
                'Your concentration is impressive. I\'ll provide clear, concise guidance. 🎯',
                'You\'re in the zone! Let\'s keep this momentum going with smart next steps. ⚡'
            ];
            return focusedResponses[Math.floor(Math.random() * focusedResponses.length)];
        }
        // Standard balanced responses
        const responses = [
            'I\'m here to help you achieve your coding goals. What would you like to work on together? 🤝',
            'Let\'s explore the best approach to solve your challenge. I\'m ready when you are! 💫',
            'Your steady approach is admirable. Let\'s build something great together! 🏗️',
            'I sense good energy for productive work. Let\'s make the most of it! 🌊'
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
       * Describe emotional context
       */
    describeEmotionalContext(mood) {
        const contexts = [];
        if (mood.stress > 60)
            contexts.push('high stress');
        if (mood.frustration > 60)
            contexts.push('frustrated');
        if (mood.confidence < 40)
            contexts.push('low confidence');
        if (mood.energy < 30)
            contexts.push('low energy');
        if (mood.excitement > 70)
            contexts.push('excited');
        if (mood.focus > 80)
            contexts.push('highly focused');
        return contexts.length > 0 ? contexts.join(', ') : 'balanced emotional state';
    }
    /**
       * Calculate personality adjustment based on interaction
       */
    calculatePersonalityAdjustment(mood) {
        const adjustment = {};
        // Adjust based on what seems to work for this user
        if (mood.stress > 70) {
            adjustment.motivationalStyle = 'supportive';
        }
        else if (mood.excitement > 70) {
            adjustment.motivationalStyle = 'challenging';
        }
        return adjustment;
    }
    /**
       * Learn from interaction effectiveness with advanced pattern recognition
       */
    recordInteractionFeedback(interactionId, effectiveness, userFeedback, mood, context) {
        // Store comprehensive learning data
        this.learningData.set(interactionId, {
            effectiveness,
            userFeedback,
            timestamp: Date.now(),
            mood: mood || this.getDefaultMood(),
            context: context || {}
        });
        // Learn mood patterns for better future responses
        if (mood && context) {
            this.updateMoodPatterns(mood, context, effectiveness);
        }
        // Adjust personality based on feedback with more nuanced learning
        if (effectiveness > 80) {
            // This approach worked well, reinforce it
            this.personality.adaptiveLevel = Math.min(100, this.personality.adaptiveLevel + 1);
            if (mood && mood.stress > 70 && effectiveness > 85) {
                // Stress relief was particularly effective
                this.personality.emotionalIntelligence = Math.min(100, this.personality.emotionalIntelligence + 2);
            }
        }
        else if (effectiveness < 40) {
            // This approach didn't work, try adapting more
            this.personality.adaptiveLevel = Math.min(100, this.personality.adaptiveLevel + 2);
            if (mood && mood.confidence < 30 && effectiveness < 30) {
                // Confidence building needs improvement
                this.personality.motivationalStyle = 'supportive';
            }
        }
        logger_1.Logger.instance.debug(`Goddess personality learning from interaction: ${effectiveness}% effective`);
    }
    /**
     * Update mood patterns for predictive emotional intelligence
     */
    updateMoodPatterns(mood, context, effectiveness) {
        const now = new Date();
        const timeOfDay = now.getHours();
        const dayOfWeek = now.getDay();
        // Find existing pattern or create new one
        let pattern = this.moodPatterns.find(p => Math.abs(p.timeOfDay - timeOfDay) <= 1 &&
            p.dayOfWeek === dayOfWeek);
        if (!pattern) {
            pattern = {
                timeOfDay,
                dayOfWeek,
                sessionLength: context.sessionDuration || 0,
                averageMood: { ...mood },
                triggers: [],
                effectiveResponses: []
            };
            this.moodPatterns.push(pattern);
        }
        else {
            // Update running averages
            Object.keys(mood).forEach(key => {
                const moodKey = key;
                if (pattern) {
                    pattern.averageMood[moodKey] = (pattern.averageMood[moodKey] + mood[moodKey]) / 2;
                }
            });
        }
        // Track effective responses
        if (effectiveness > 75) {
            const responseType = this.getResponseType(mood);
            if (!pattern.effectiveResponses.includes(responseType)) {
                pattern.effectiveResponses.push(responseType);
            }
        }
        // Limit pattern storage to prevent memory bloat
        if (this.moodPatterns.length > 100) {
            this.moodPatterns = this.moodPatterns.slice(-50);
        }
    }
    /**
     * Get default mood for fallback scenarios
     */
    getDefaultMood() {
        return {
            stress: 30,
            confidence: 70,
            energy: 60,
            focus: 70,
            frustration: 20,
            excitement: 50,
            burnout: 20,
            imposterSyndrome: 30,
            flowState: 50,
            socialConnection: 60,
            autonomy: 70,
            mastery: 60
        };
    }
    /**
     * Determine response type based on mood
     */
    getResponseType(mood) {
        if (mood.stress > 70)
            return 'stress-relief';
        if (mood.frustration > 60)
            return 'frustration-support';
        if (mood.confidence < 40)
            return 'confidence-boost';
        if (mood.excitement > 70)
            return 'excitement-channel';
        if (mood.burnout > 60)
            return 'burnout-prevention';
        if (mood.imposterSyndrome > 60)
            return 'imposter-support';
        return 'balanced';
    }
    /**
       * Get current personality state
       */
    getPersonality() {
        return { ...this.personality };
    }
    /**
       * Update personality settings
       */
    updatePersonality(updates) {
        this.personality = { ...this.personality, ...updates };
        logger_1.Logger.instance.info('Goddess personality updated', updates);
    }
}
exports.GoddessPersonalityEngine = GoddessPersonalityEngine;
// Singleton instance
exports.goddessPersonalityEngine = new GoddessPersonalityEngine();
//# sourceMappingURL=goddessMode.js.map