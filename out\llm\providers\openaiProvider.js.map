{"version": 3, "file": "openaiProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/openaiProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,uDAAoD;AACpD,4DAA4D;AAC5D,yCAAsC;AACtC,+CAAiC;AAEjC,mCAAgC;AAGhC,MAAa,cAAe,SAAQ,iCAAe;IACxC,UAAU,GAAG,QAAQ,CAAC;IACtB,WAAW,GAAG,QAAQ,CAAC;IACvB,WAAW,GAAG,2BAA2B,CAAC;IAC1C,OAAO,GAAG,oBAAoB,CAAC;IAC/B,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,2BAA2B,CAAC;IAC9C,YAAY,GAAG,QAAQ,CAAC;IACxB,kBAAkB,GAAG,IAAI,CAAC;IAC1B,qBAAqB,GAAG,wBAAwB,CAAC;IAElD,MAAM,GAAkB,IAAI,CAAC;IAErC,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBAC9E,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,sCAAsC;QACtC,IAAI,CAAC,eAAM,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;YAC/E,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kEAAkE,CAAC,CAAC;YAC1F,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,yDAAyD;YACzD,MAAM,MAAM,GAAkB;gBAC5B,MAAM;gBACN,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,EAAE,qBAAqB;gBACrC,UAAU,EAAE,CAAC,EAAG,sCAAsC;aACvD,CAAC;YAEF,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,CAAC,MAAM,CAAC,CAAC;YACjC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,4EAA4E;YAC5E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8EAA8E,CAAC,CAAC;YACtG,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,KAA0B;QACrD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,cAAc,GAAU,EAAE,CAAC;QAEjC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,mCAAmC;YACnC,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,OAAQ,IAAY,CAAC,aAAa,KAAK,UAAU,EAAE,CAAC;gBAC5E,MAAM,UAAU,GAAI,IAAY,CAAC,aAAa,EAAE,CAAC;gBAEjD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;oBACnC,cAAc,CAAC,IAAI,CAAC;wBAClB,IAAI,EAAE,UAAU;wBAChB,QAAQ,EAAE;4BACR,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,EAAE;4BAClC,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,GAAG,IAAI,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,YAAY;4BAC5E,UAAU,EAAE,SAAS,CAAC,WAAW,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE;yBACxE;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,eAAe;gBACf,cAAc,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,IAAI,CAAC,EAAE;wBACb,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,GAAG,IAAI,CAAC,EAAE,YAAY;wBACvD,UAAU,EAAG,IAAY,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE;qBAC1F;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,mDAAmD,EAAE,CAAC;QACrF,CAAC;QAED,IAAI,CAAC;YACH,mBAAmB;YACnB,IAAI,QAAe,CAAC;YAEpB,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,GAAG,MAAM,CAAC,OAAgB,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG;oBACT,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,SAAS;oBAClF,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE;iBACzC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC;YAED,2BAA2B;YAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAExD,4CAA4C;YAC5C,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,qBAAqB;YACrB,MAAM,OAAO,GAAQ;gBACnB,KAAK,EAAE,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,IAAI,eAAe;gBAC7D,QAAQ;gBACR,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS;gBAC5B,MAAM,EAAE,KAAK;gBACb,GAAG,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;aAC9E,CAAC;YAEF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAE3E,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,4EAA4E;gBAC5E,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;wBACzD,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBAChH,CAAC;YACH,CAAC;YAED,6CAA6C;YAC7C,kEAAkE;YAClE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAEpE,+FAA+F;YAC/F,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;YACvE,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4CAA4C,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;YAE1F,8BAA8B;YAC9B,IAAI,eAA4C,CAAC;YACjD,IAAI,MAAM,CAAC,OAAO,EAAE,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B;gBAE5E,IAAI,CAAC;oBACH,eAAe,GAAG;wBAChB,EAAE,EAAE,QAAQ,CAAC,EAAE;wBACf,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;wBAC9B,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC;qBACtD,CAAC;gBACJ,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,CAAC,EAAE,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;YAED,sBAAsB;YACtB,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;gBAC9C,YAAY,EAAE,MAAM,CAAC,aAAa,IAAI,SAAS;gBAC/C,KAAK,EAAE;oBACL,YAAY,EAAE,QAAQ,CAAC,KAAK,EAAE,aAAa;oBAC3C,gBAAgB,EAAE,QAAQ,CAAC,KAAK,EAAE,iBAAiB;oBACnD,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY;iBAC1C;gBACD,QAAQ,EAAE,eAAe;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,4EAA4E;YAC5E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YACvD,IAAI,YAAY,GAAG,4BAA4B,CAAC;YAEhD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClC,0BAA0B;gBAC1B,YAAY,GAAG,qBAAqB,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;YACxE,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAC3E,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACjD,0DAA0D;YAC1D,OAAO,QAAQ,CAAC,IAAI;iBACjB,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;iBACzE,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;iBACrB,IAAI,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAC3E,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACjD,0DAA0D;YAC1D,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI;iBACzB,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;YAC7E,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;YAE9E,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;gBAC7B,EAAE,EAAE,CAAC,CAAC,EAAE;gBACR,IAAI,EAAE,CAAC,CAAC,EAAE;gBACV,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE,QAAQ;gBACnC,aAAa,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClD,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;aAC/C,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAe,EAAE,CAAe,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,OAAe;QAC9C,qDAAqD;QACrD,MAAM,cAAc,GAA2B;YAC7C,QAAQ,EAAE,MAAM;YAChB,aAAa,EAAE,MAAM;YACrB,aAAa,EAAE,MAAM;YACrB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,KAAK;YACtB,mBAAmB,EAAE,KAAK;YAC1B,6BAA6B;YAC7B,SAAS,EAAE,IAAI;SAChB,CAAC;QAEF,0BAA0B;QAC1B,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,4BAA4B;QAC5B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1D,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,OAAe;QAC5C,qGAAqG;QACrG,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,OAAO,sBAAsB,CAAC;QAChC,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,OAAO,uBAAuB,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,OAAO,yBAAyB,CAAC;QACnC,CAAC;IACH,CAAC;IACD;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2DAA2D;aACrE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,kDAAkD;YAClD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAEvD,iDAAiD;YACjD,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;YAE3E,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,UAAU,OAAO,uCAAuC;iBAClE,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,4DAA4D,OAAO,IAAI;aACjF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,YAAY,GAAG,iCAAiC,CAAC;YAErD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClC,YAAY,GAAG,qBAAqB,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;YACxE,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,qEAAqE;IAErE;;SAEK;IACL,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,OAAgB;QACpD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,OAAO,IAAI,IAAI,CAAC,qBAAqB,CAAC;YAC7D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,cAAc,EAAE,CAAC,CAAC;YAE3E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;gBACnD,KAAK,EAAE,cAAc;gBACrB,KAAK,EAAE,IAAI;gBACX,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACpC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,YAAY,GAAG,+BAA+B,CAAC;YAEnD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClC,YAAY,GAAG,qBAAqB,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;YACxE,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;YACrF,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACjD,8BAA8B;YAC9B,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI;iBACzB,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;YAElD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,MAAM,CAAC,MAAM,6BAA6B,CAAC,CAAC;YAExF,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;gBAC7B,EAAE,EAAE,CAAC,CAAC,EAAE;gBACR,IAAI,EAAE,CAAC,CAAC,EAAE;gBACV,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE,kBAAkB;gBAC7C,aAAa,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,EAAE,CAAC;aAC5D,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAe,EAAE,CAAe,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACzE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iCAAiC,CAAC,OAAe;QACvD,qDAAqD;QACrD,MAAM,cAAc,GAA2B;YAC7C,wBAAwB,EAAE,IAAI;YAC9B,wBAAwB,EAAE,IAAI;YAC9B,wBAAwB,EAAE,IAAI;YAC9B,6BAA6B;YAC7B,SAAS,EAAE,IAAI;SAChB,CAAC;QAEF,0BAA0B;QAC1B,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,4BAA4B;QAC5B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1D,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC,OAAO,CAAC;IAChC,CAAC;IAEM,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,qBAAqB;gBAClC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,8DAA8D;gBAC3E,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,wDAAwD;gBACrE,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,wEAAwE;gBACrF,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AAreD,wCAqeC", "sourcesContent": ["import { LLMGenerate<PERSON><PERSON><PERSON>, LLMGenerateResult } from '../types';\nimport { LLMModelInfo, ToolCallRequest } from '../llmProvider';\nimport { BaseLLMProvider } from './baseLLMProvider';\n// Unused imports removed: getOpenAIApi<PERSON>ey, getOpenAIBaseUrl\nimport { Logger } from '../../logger';\nimport * as vscode from 'vscode';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { OpenAI } from 'openai';\nimport type { ClientOptions } from 'openai';\n\nexport class OpenAIProvider extends BaseLLMProvider {\n  readonly providerId = 'openai';\n  readonly displayName = 'OpenAI';\n  readonly description = 'OpenAI API for GPT models';\n  readonly website = 'https://openai.com';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api.openai.com/v1';\n  readonly defaultModel = 'gpt-4o';\n  readonly supportsEmbeddings = true;\n  readonly defaultEmbeddingModel = 'text-embedding-3-small';\n\n  private client: OpenAI | null = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        Logger.instance.info('OpenAI configuration changed, re-initializing client.');\n        this.initializeClient();\n      }\n    });\n  }\n\n  private initializeClient() {\n    // Check if OpenAI module is available\n    if (!OpenAI) {\n      Logger.instance.debug('OpenAI module not available - skipping initialization');\n      this.client = null;\n      return;\n    }\n\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (!apiKey) {\n      Logger.instance.debug('OpenAI API key not set - this is normal if you don\\'t use OpenAI');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize the OpenAI client with proper configuration\n      const config: ClientOptions = {\n        apiKey,\n        baseURL: baseUrl,\n        timeout: 60000, // 60 seconds timeout\n        maxRetries: 3,  // Retry failed requests up to 3 times\n      };\n\n      this.client = new OpenAI(config);\n      Logger.instance.info('OpenAI client initialized successfully.');\n    } catch (error) {\n      // Log at debug level to avoid showing errors to users who don't need OpenAI\n      Logger.instance.debug('Failed to initialize OpenAI client - this is normal if you don\\'t use OpenAI');\n      Logger.instance.debug('Error details:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  /**\n     * Formats tools for OpenAI function calling API\n     */\n  private formatToolsForOpenAI(tools?: Map<string, ITool>): any[] | undefined {\n    if (!tools || tools.size === 0) {\n      return undefined;\n    }\n\n    const formattedTools: any[] = [];\n\n    tools.forEach(tool => {\n      // Handle file tool with subactions\n      if (tool.id === 'file' && typeof (tool as any).getSubActions === 'function') {\n        const subActions = (tool as any).getSubActions();\n\n        for (const subAction of subActions) {\n          formattedTools.push({\n            type: 'function',\n            function: {\n              name: `${tool.id}.${subAction.id}`,\n              description: subAction.description || `${tool.id}.${subAction.id} operation`,\n              parameters: subAction.inputSchema || { type: 'object', properties: {} }\n            }\n          });\n        }\n      } else {\n        // Regular tool\n        formattedTools.push({\n          type: 'function',\n          function: {\n            name: tool.id,\n            description: tool.description || `${tool.id} operation`,\n            parameters: (tool as any).singleActionSchema?.shape || { type: 'object', properties: {} }\n          }\n        });\n      }\n    });\n\n    return formattedTools.length > 0 ? formattedTools : undefined;\n  }\n\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'OpenAI provider not configured (API key missing?)' };\n    }\n\n    try {\n      // Prepare messages\n      let messages: any[];\n\n      if (params.history && params.history.length > 0) {\n        messages = params.history as any[];\n      } else {\n        messages = [\n          params.systemPrompt ? { role: 'system', content: params.systemPrompt } : undefined,\n          { role: 'user', content: params.prompt }\n        ].filter(Boolean);\n      }\n\n      // Format tools if provided\n      const formattedTools = this.formatToolsForOpenAI(tools);\n\n      // Check for cancellation before calling API\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Create API request\n      const request: any = {\n        model: params.modelId || this.defaultModel || 'gpt-3.5-turbo',\n        messages,\n        temperature: params.temperature ?? 0.7,\n        max_tokens: params.maxTokens,\n        stream: false,\n        ...(formattedTools && formattedTools.length > 0 && { tools: formattedTools })\n      };\n\n      Logger.instance.debug(`Sending request to OpenAI model ${params.modelId}`);\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        // Use the global AbortController if available, or provide a simple fallback\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            Logger.instance.info('OpenAI request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          Logger.instance.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Run API request with possible cancellation\n      // Use any type to bypass type checking issues with the OpenAI SDK\n      const response = await this.client.chat.completions.create(request);\n\n      // Check for cancellation again after API call (in case it was cancelled but too late to abort)\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled during processing' };\n      }\n\n      const choice = response.choices[0];\n      Logger.instance.debug(`OpenAI response received. Finish reason: ${choice.finish_reason}`);\n\n      // Check if we got a tool call\n      let toolCallRequest: ToolCallRequest | undefined;\n      if (choice.message?.tool_calls && choice.message.tool_calls.length > 0) {\n        const toolCall = choice.message.tool_calls[0]; // Take the first one for now\n\n        try {\n          toolCallRequest = {\n            id: toolCall.id,\n            toolId: toolCall.function.name,\n            args: JSON.parse(toolCall.function.arguments || '{}')\n          };\n        } catch (e) {\n          Logger.instance.error(`Failed to parse tool call arguments: ${e}`);\n        }\n      }\n\n      // Return the response\n      return {\n        content: choice.message?.content?.trim() ?? '',\n        finishReason: choice.finish_reason ?? undefined,\n        usage: {\n          promptTokens: response.usage?.prompt_tokens,\n          completionTokens: response.usage?.completion_tokens,\n          totalTokens: response.usage?.total_tokens,\n        },\n        toolCall: toolCallRequest\n      };\n    } catch (error: any) {\n      // Handle errors, differentiating between OpenAI API errors and other errors\n      Logger.instance.error('OpenAI generate error:', error);\n      let errorMessage = 'Failed to call OpenAI API.';\n\n      if (error.status && error.message) {\n        // OpenAI API error format\n        errorMessage = `OpenAI API Error (${error.status}): ${error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  async getAvailableModels(): Promise<string[]> {\n    if (!this.client) {\n      Logger.instance.warn('Cannot fetch OpenAI models, client not configured.');\n      return [];\n    }\n    try {\n      const response = await this.client.models.list();\n      // Filter for models that support the chat completions API\n      return response.data\n        .filter((m: any) => m.id.includes('gpt') || m.id.includes('text-davinci'))\n        .map((m: any) => m.id)\n        .sort();\n    } catch (error: any) {\n      Logger.instance.error('Failed to fetch OpenAI models:', error);\n      return [];\n    }\n  }\n\n  async listModels(): Promise<LLMModelInfo[]> {\n    if (!this.client) {\n      Logger.instance.warn('Cannot fetch OpenAI models, client not configured.');\n      return [];\n    }\n    try {\n      const response = await this.client.models.list();\n      // Filter for models that support the chat completions API\n      const models = response.data\n        .filter((m: any) => m.id.includes('gpt') || m.id.includes('text-davinci'));\n      Logger.instance.info(`Provider openai has ${models.length} models available`);\n\n      return models.map((m: any) => ({\n        id: m.id,\n        name: m.id,\n        description: `OpenAI ${m.id} model`,\n        contextWindow: this.getContextWindowForModel(m.id),\n        pricingInfo: this.getPricingInfoForModel(m.id)\n      })).sort((a: LLMModelInfo, b: LLMModelInfo) => a.id.localeCompare(b.id));\n    } catch (error: any) {\n      Logger.instance.error('Failed to fetch OpenAI models:', error);\n      return [];\n    }\n  }\n\n  /**\n     * Get the context window size for a specific model\n     */\n  private getContextWindowForModel(modelId: string): number {\n    // Context window sizes based on OpenAI documentation\n    const contextWindows: Record<string, number> = {\n      'gpt-4o': 128000,\n      'gpt-4o-mini': 128000,\n      'gpt-4-turbo': 128000,\n      'gpt-4': 8192,\n      'gpt-4-32k': 32768,\n      'gpt-3.5-turbo': 16385,\n      'gpt-3.5-turbo-16k': 16385,\n      // Default for unknown models\n      'default': 4096\n    };\n\n    // Check for exact matches\n    if (contextWindows[modelId]) {\n      return contextWindows[modelId];\n    }\n\n    // Check for partial matches\n    for (const [key, value] of Object.entries(contextWindows)) {\n      if (modelId.includes(key)) {\n        return value;\n      }\n    }\n\n    return contextWindows.default;\n  }\n\n  /**\n     * Get pricing information for a specific model\n     */\n  private getPricingInfoForModel(modelId: string): string {\n    // This is simplified pricing info - in a real implementation, you might want to provide more details\n    if (modelId.includes('gpt-4')) {\n      return 'Premium tier pricing';\n    } else if (modelId.includes('gpt-3.5')) {\n      return 'Standard tier pricing';\n    } else {\n      return 'See OpenAI pricing page';\n    }\n  }\n  /**\n     * Test connection to OpenAI\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'OpenAI client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // First check if we can connect to the OpenAI API\n      const modelsResponse = await this.client.models.list();\n\n      // Then check if the specified model is available\n      const modelExists = modelsResponse.data.some((m: any) => m.id === modelId);\n\n      if (!modelExists) {\n        return {\n          success: false,\n          message: `Model '${modelId}' not found in your available models.`\n        };\n      }\n\n      return {\n        success: true,\n        message: `Successfully connected to OpenAI API and verified model '${modelId}'.`\n      };\n    } catch (error: any) {\n      Logger.instance.error('OpenAI connection test failed:', error);\n      let errorMessage = 'Failed to connect to OpenAI API';\n\n      if (error.status && error.message) {\n        errorMessage = `OpenAI API Error (${error.status}): ${error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  // Use the parent class implementation for getConfig and updateConfig\n\n  /**\n     * Generate an embedding vector for the given text\n     */\n  async generateEmbedding(text: string, modelId?: string): Promise<number[]> {\n    if (!this.client) {\n      throw new Error('OpenAI provider not configured (API key missing?)');\n    }\n\n    try {\n      const embeddingModel = modelId || this.defaultEmbeddingModel;\n      Logger.instance.debug(`Generating embedding with model ${embeddingModel}`);\n\n      const response = await this.client.embeddings.create({\n        model: embeddingModel,\n        input: text,\n        encoding_format: 'float'\n      });\n\n      return response.data[0].embedding;\n    } catch (error: any) {\n      Logger.instance.error('OpenAI embedding error:', error);\n      let errorMessage = 'Failed to generate embedding.';\n\n      if (error.status && error.message) {\n        errorMessage = `OpenAI API Error (${error.status}): ${error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      throw new Error(errorMessage);\n    }\n  }\n\n  /**\n     * List available embedding models\n     */\n  async listEmbeddingModels(): Promise<LLMModelInfo[]> {\n    if (!this.client) {\n      Logger.instance.warn('Cannot fetch OpenAI embedding models, client not configured.');\n      return [];\n    }\n\n    try {\n      const response = await this.client.models.list();\n      // Filter for embedding models\n      const models = response.data\n        .filter((m: any) => m.id.includes('embedding'));\n\n      Logger.instance.info(`Provider openai has ${models.length} embedding models available`);\n\n      return models.map((m: any) => ({\n        id: m.id,\n        name: m.id,\n        description: `OpenAI ${m.id} embedding model`,\n        contextWindow: this.getContextWindowForEmbeddingModel(m.id)\n      })).sort((a: LLMModelInfo, b: LLMModelInfo) => a.id.localeCompare(b.id));\n    } catch (error: any) {\n      Logger.instance.error('Failed to fetch OpenAI embedding models:', error);\n      return [];\n    }\n  }\n\n  /**\n     * Get the context window size for a specific embedding model\n     */\n  private getContextWindowForEmbeddingModel(modelId: string): number {\n    // Context window sizes based on OpenAI documentation\n    const contextWindows: Record<string, number> = {\n      'text-embedding-3-small': 8191,\n      'text-embedding-3-large': 8191,\n      'text-embedding-ada-002': 8191,\n      // Default for unknown models\n      'default': 2048\n    };\n\n    // Check for exact matches\n    if (contextWindows[modelId]) {\n      return contextWindows[modelId];\n    }\n\n    // Check for partial matches\n    for (const [key, value] of Object.entries(contextWindows)) {\n      if (modelId.includes(key)) {\n        return value;\n      }\n    }\n\n    return contextWindows.default;\n  }\n\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your OpenAI API key',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The OpenAI API endpoint (default: https://api.openai.com/v1)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use (e.g., gpt-4o, gpt-3.5-turbo)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultEmbeddingModel',\n        name: 'Default Embedding Model',\n        description: 'The default model to use for embeddings (e.g., text-embedding-3-small)',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n"]}