/**
 * Enhanced document retrieval tool with context support and schema validation.
 * Wraps a base retrieval tool to provide consistent behavior and type safety.
 */

import { z } from 'zod';
import { BaseTool, ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';

/**
 * Local logger implementation for internal tool logging
 */
class Logger {
  private static instance: Logger;
  
  /**
   * Get the singleton instance of the logger
   */
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Log an error message
   * @param message The error message
   * @param args Additional error data
   */
  public error(message: string, ...args: unknown[]): void {
    console.error(`[ERROR] ${message}`, ...args);
  }

  /**
   * Log a debug message
   * @param message The debug message
   * @param args Additional debug data
   */
  public debug(message: string, ...args: unknown[]): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  }
}

/**
 * Options for document retrieval
 */
export interface DocumentRetrievalOptions {
  /** Maximum number of documents to return */
  limit?: number;
  /** Minimum relevance score (0-1) */
  minScore?: number;
  /** Whether to include document metadata */
  includeMetadata?: boolean;
  /** Additional filters for document retrieval */
  filters?: Record<string, unknown>;
  /** Whether to use semantic search */
  semanticSearch?: boolean;
}

/**
 * Enhanced wrapper for document retrieval tools with context awareness
 */
export class DocumentRetrievalTool extends BaseTool {
  private readonly _baseTool: ITool;
  private readonly _defaultOptions: DocumentRetrievalOptions;
  
  public readonly type = 'single-action' as const;
  public readonly category = 'retrieval';
  public readonly config: Record<string, unknown> = {};
  public readonly metadata: Record<string, unknown> = {};
  public readonly version = '1.0.0';
  
  public readonly schema = z.union([
    // Simple string query
    z.string()
      .min(1, 'Query cannot be empty')
      .describe('The query string to search for documents.'),
    
    // Or an object with query and options
    z.object({
      query: z.string()
        .min(1, 'Query cannot be empty')
        .describe('The query string to search for documents.'),
      options: z.object({
        limit: z.number()
          .int()
          .positive()
          .max(100, 'Maximum limit is 100')
          .optional()
          .describe('Maximum number of documents to return'),
        minScore: z.number()
          .min(0)
          .max(1)
          .optional()
          .describe('Minimum relevance score (0-1)'),
        includeMetadata: z.boolean()
          .optional()
          .describe('Whether to include document metadata'),
        filters: z.record(z.unknown())
          .optional()
          .describe('Additional filters for document retrieval'),
        semanticSearch: z.boolean()
          .optional()
          .describe('Whether to use semantic search (true) or keyword search (false)')
      }).strict().optional()
    }).strict()
  ]).transform((input): { query: string; options: DocumentRetrievalOptions } => {
    // Normalize input to always return a query string and options
    if (typeof input === 'string') {
      return { query: input, options: {} };
    }
    return { query: input.query, options: input.options || {} };
  });
  
  public readonly inputSchema = this.schema;
  public readonly outputSchema = z.object({
    success: z.boolean(),
    output: z.any(),
    error: z.string().optional(),
    toolId: z.string(),
    actionName: z.string().optional()
  });

  // Class property for description to satisfy BaseTool's abstract requirement
  public readonly description: string;

  /**
   * Creates a new DocumentRetrievalTool instance
   * @param baseTool - The underlying tool to wrap (must implement ITool)
   * @param options - Default options for document retrieval
   * @param name - Optional custom name for the tool (default: 'document-retrieval-tool')
   * @param description - Optional custom description
   * @throws {Error} If baseTool is not provided or invalid
   */
  constructor(
    baseTool: ITool,
    options: DocumentRetrievalOptions = {},
    name = 'document-retrieval-tool',
    description = 'Retrieves relevant documents based on a query with contextual enhancement.'
  ) {
    super(name);

    if (!baseTool || typeof baseTool.execute !== 'function') {
      throw new Error('A valid base tool implementing ITool is required');
    }

    this._baseTool = baseTool;
    this._defaultOptions = { ...options };
    this.description = description;
  }

  /**
   * Executes the document retrieval with the given input
   * @param actionName Optional action name (for multi-action tools)
   * @param input The input parameters for the tool
   * @param context The agent context (optional)
   * @returns A promise resolving to the tool result
   */
  public async execute(input: ToolInput, context?: AgentContext): Promise<ToolResult>;
  public async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult>;
  public async execute(
    actionNameOrInput: string | undefined | ToolInput,
    inputOrContext?: ToolInput | AgentContext,
    maybeContext?: AgentContext
  ): Promise<ToolResult> {
    // Handle both method signatures
    let actionName: string | undefined;
    let input: ToolInput;
    let context: AgentContext | undefined;

    if (typeof actionNameOrInput === 'string' || actionNameOrInput === undefined) {
      actionName = actionNameOrInput;
      input = inputOrContext as ToolInput;
      context = maybeContext;
    } else {
      input = actionNameOrInput as ToolInput;
      context = inputOrContext as AgentContext | undefined;
    }

    try {
      // Update context if provided
      if (context) {
        this.updateContext(context);
      }

      // Set default action name if not provided
      const resolvedActionName = actionName || 'retrieve';
      
      // Validate and normalize input
      const normalizedInput = await this.normalizeInput(input);

      // Execute the base tool with the validated input
      const result = await this.executeBaseTool(normalizedInput, input);

      // Create and return a success result
      return this.createSuccessResult(result, normalizedInput, resolvedActionName);
    } catch (error) {
      // Create and return an error result
      const resolvedActionName = actionName || (input as any)?.action || 'retrieve';
      return this.createErrorResult(
        this.getErrorMessage(error),
        input,
        resolvedActionName
      );
    }
  }

  /**
   * Validates the input against the schema
   * @param input - The input to validate
   * @returns Validation result with validity and optional error message
   */
  public validateInput(input: unknown): { valid: boolean; error?: string } {
    const validation = this.schema.safeParse(input);
    if (validation.success) {
      return { valid: true };
    } else {
      const errorMessage = validation.error.errors
        .map(e => `${e.path.join('.')}: ${e.message}`)
        .join('; ');
      Logger.getInstance().error('Input validation failed:', errorMessage);
      return { valid: false, error: errorMessage };
    }
  }

  /**
   * Validates and normalizes the input against the schema
   * @param input - The input to validate and normalize
   * @returns Normalized input with query and options
   * @throws {Error} If validation fails
   */
  private async normalizeInput(input: unknown): Promise<{ query: string; options: DocumentRetrievalOptions }> {
    try {
      return await this.schema.parseAsync(input);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Invalid input format';
      throw new Error(`Invalid input: ${errorMessage}`);
    }
  }

  /**
   * Executes the base tool with the validated input
   * @param validated - The validated input
   * @param originalInput - The original input for context
   * @returns The result from the base tool
   */
  private async executeBaseTool(
    validated: { query: string; options: DocumentRetrievalOptions },
    originalInput: ToolInput
  ): Promise<unknown> {
    try {
      // Merge with default options
      const options = { ...this._defaultOptions, ...validated.options };

      // Prepare the input for the base tool, ensuring it matches ToolInput interface
      const baseInput: ToolInput = {
        ...originalInput,
        query: validated.query,
        options,
        // Ensure required fields are present
        action: 'retrieve',
        toolId: this._baseTool.id
      };

      // Execute the base tool with the proper input format
      if (this._baseTool.type === 'multi-action') {
        return await this._baseTool.execute('retrieve', baseInput);
      } else {
        return await this._baseTool.execute(baseInput);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error executing base tool';
      Logger.getInstance().error('Error in executeBaseTool:', errorMessage);
      throw error;
    }
  }

  /**
   * Creates a success result object
   */
  private createSuccessResult(
    result: unknown,
    validated: { query: string; options: DocumentRetrievalOptions },
    actionName: string
  ): ToolResult {
    return {
      success: true,
      output: result,
      toolId: this.id,
      actionName,
      metadata: {
        query: validated.query,
        options: validated.options
      }
    };
  }

  /**
   * Creates an error result object
   */
  private createErrorResult(
    errorMessage: string,
    input: ToolInput,
    actionName: string
  ): ToolResult {
    return {
      success: false,
      error: errorMessage,
      toolId: this.id,
      actionName,
      metadata: {
        error: errorMessage,
        input
      }
    };
  }

  /**
   * Extracts error message from unknown error type
   */
  private getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'An unknown error occurred';
  }

  /**
   * Gets the tool definition in a format suitable for consumption by LLMs
   * @returns The tool definition in a standardized format
   */
  public getDefinitionForModel(): Record<string, unknown> {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          query: { 
            type: 'string', 
            description: 'The search query' 
          },
          options: {
            type: 'object',
            properties: {
              limit: { 
                type: 'number', 
                description: 'Maximum number of results to return (max: 100)' 
              },
              minScore: { 
                type: 'number', 
                minimum: 0,
                maximum: 1,
                description: 'Minimum relevance score (0-1)' 
              },
              includeMetadata: { 
                type: 'boolean', 
                description: 'Whether to include document metadata' 
              },
              filters: { 
                type: 'object', 
                additionalProperties: true,
                description: 'Additional filters for document retrieval' 
              },
              semanticSearch: { 
                type: 'boolean', 
                description: 'Whether to use semantic search (true) or keyword search (false)' 
              }
            }
          }
        },
        required: ['query']
      }
    };
  }

  /**
   * Updates the context for the tool
   * @param context - The new agent context (optional)
   */
  public updateContext(context?: AgentContext): void {
    if (context) {
      super.setContext(context);
    } else {
      // If context is undefined, clear the current context
      super.setContext(undefined as unknown as AgentContext);
    }
  }

  /**
   * Gets the current context
   * @returns The current agent context or undefined if not set
   */
  public getContext(): AgentContext | undefined {
    return super.getContext();
  }

  /**
   * Cleans up any resources used by the tool
   * @returns A promise that resolves when cleanup is complete
   */
  public async dispose(): Promise<void> {
    try {
      if (typeof this._baseTool.dispose === 'function') {
        await this._baseTool.dispose();
      }
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      Logger.getInstance().error('Error during tool disposal:', errorMessage);
      // Don't throw from dispose to prevent masking other errors
    }
  }
}
