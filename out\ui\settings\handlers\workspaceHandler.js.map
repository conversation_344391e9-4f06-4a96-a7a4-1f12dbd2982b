{"version": 3, "file": "workspaceHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/workspaceHandler.ts"], "names": [], "mappings": ";;AAKA,wDAoDC;AAkSD,0EAqBC;AAED,gFAmBC;AACD,kFAcC;AAGD,0EAcC;AAjaD,oDAAoD;AACpD,SAAgB,sBAAsB,CAAC,OAAY,EAAE,KAAU;IAC7D,qDAAqD;IACrD,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,KAAK,cAAc;YACjB,8BAA8B;YAC9B,OAAO,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9C,KAAK,eAAe;YAClB,uDAAuD;YACvD,OAAO,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC/C,KAAK,iBAAiB;YACpB,0BAA0B;YAC1B,OAAO,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACjD,KAAK,SAAS;YACZ,wDAAwD;YACxD,OAAO,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpD,KAAK,YAAY;YACf,qDAAqD;YACrD,OAAO,uBAAuB,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACzD,KAAK,SAAS;YACZ,qEAAqE;YACrE,OAAO,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpD,KAAK,cAAc;YACjB,mDAAmD;YACnD,OAAO,qBAAqB,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACvD,KAAK,kBAAkB;YACrB,yFAAyF;YACzF,OAAO,yBAAyB,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3D,KAAK,qBAAqB;YACxB,qDAAqD;YACrD,OAAO,4BAA4B,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9D,KAAK,eAAe;YAClB,gEAAgE;YAChE,OAAO,sBAAsB,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxD,KAAK,kBAAkB;YACrB,qDAAqD;YACrD,OAAO,yBAAyB,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3D,KAAK,wBAAwB;YAC3B,gEAAgE;YAChE,OAAO,+BAA+B,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACjE,KAAK,2BAA2B;YAC9B,wDAAwD;YACxD,OAAO,kCAAkC,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,wCAAwC;QAC7G,KAAK,4BAA4B;YAC/B,oDAAoD;YACpD,OAAO,mCAAmC,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrE,KAAK,wBAAwB;YAC3B,yEAAyE;YACzE,OAAO,+BAA+B,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACjE;YACE,kBAAkB;YAClB,OAAO;IACX,CAAC;AACH,CAAC;AAED,yBAAyB;AACzB,SAAS,YAAY,CAAC,OAA2B,EAAE,KAAU;IAC3D,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACxJ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9D,QAAQ,CAAC,KAAK,GAAG,oCAAoC,CAAC;QACtD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;QACpE,QAAQ,CAAC,KAAK,GAAG,wCAAwC,CAAC;QAC1D,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,MAAM,EAAE,GAAc;QACpB,EAAE,EAAE,OAAO,CAAC,EAAE;QACd,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;QACtC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;QACxB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;QAC1B,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;QACxB,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;QAC5B,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,EAAE;QAC1C,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI;YACtC,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,OAAO,EAAE,yBAAyB;YAChD,UAAU,EAAE,MAAM;YAClB,SAAS,EAAE,IAAI;YACf,YAAY,EAAE,GAAG;YACjB,eAAe,EAAE,CAAC,MAAM,CAAC;YACzB,eAAe,EAAE,CAAC,oBAAoB,EAAE,YAAY,CAAC;SACtD;QACD,SAAS,EAAE,GAAG;QACd,SAAS,EAAE,GAAG;QACd,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE;YACR,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,oBAAoB,EAAE,KAAK;SAC5B;KACF,CAAC;IACF,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;IACnB,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,SAAS,aAAa,CAAC,OAAoD,EAAE,KAAU;IACrF,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACzJ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;QAC5B,QAAQ,CAAC,KAAK,GAAG,wBAAwB,CAAC;QAC1C,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC;IACjF,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;QACf,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAC/E,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACzC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,SAAS,eAAe,CAAC,OAAuB,EAAE,KAAU;IAC1D,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC3J,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;QAC5B,QAAQ,CAAC,KAAK,GAAG,wBAAwB,CAAC;QAC1C,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC;IACjF,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;QACf,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACnC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,0BAA0B;AAC1B,SAAS,kBAAkB,CAAC,OAAqD,EAAE,KAAU;IAC3F,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACnJ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC5E,QAAQ,CAAC,KAAK,GAAG,4CAA4C,CAAC;QAC9D,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC;IAC1B,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAgB,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACtE,QAAQ,CAAC,KAAK,GAAG,mCAAmC,CAAC;QACrD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC;IACzB,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,SAAS,uBAAuB,CAAC,OAAkD,EAAE,KAAU;IAC7F,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtJ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC1D,QAAQ,CAAC,KAAK,GAAG,4CAA4C,CAAC;QAC9D,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QACrB,QAAQ,CAAC,KAAK,GAAG,8BAA8B,CAAC;QAChD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAgB,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;IAClF,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;QACf,QAAQ,CAAC,KAAK,GAAG,8BAA8B,CAAC;QAChD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACxB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC;IACzB,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,SAAS,kBAAkB,CAAC,OAAkE,EAAE,KAAU;IACxG,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACnJ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1F,QAAQ,CAAC,KAAK,GAAG,0BAA0B,CAAC;QAC5C,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QACrB,QAAQ,CAAC,KAAK,GAAG,8BAA8B,CAAC;QAChD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAgB,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC9E,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,QAAQ,CAAC,KAAK,GAAG,8BAA8B,CAAC;QAChD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IACzB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,2BAA2B;AAC3B,SAAS,qBAAqB,CAAC,OAAgD,EAAE,KAAU;IACzF,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACxJ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACrC,QAAQ,CAAC,KAAK,GAAG,gCAAgC,CAAC;QAClD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC3B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC;IAC1B,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,wBAAwB;AACxB,SAAS,yBAAyB,CAAC,OAAsF,EAAE,KAAU;IACnI,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC5J,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QAChG,QAAQ,CAAC,KAAK,GAAG,wCAAwC,CAAC;QAC1D,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,aAAa,GAAG,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC;IAC1C,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACnC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,aAAa,CAAC;IACjC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,SAAS,4BAA4B,CAAC,OAAkD,EAAE,KAAU;IAClG,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC/J,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC7E,QAAQ,CAAC,KAAK,GAAG,wCAAwC,CAAC;QAC1D,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QACpG,QAAQ,CAAC,KAAK,GAAG,uCAAuC,CAAC;QACzD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC7C,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,aAAa,CAAC;IACjC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,uBAAuB;AACvB,SAAS,sBAAsB,CAAC,OAA6D,EAAE,KAAU;IACvG,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACzJ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC9E,QAAQ,CAAC,KAAK,GAAG,sCAAsC,CAAC;QACxD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAsB,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;QACzE,QAAQ,CAAC,KAAK,GAAG,6BAA6B,CAAC;QAC/C,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC7B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;IACxB,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,SAAS,yBAAyB,CAAC,OAAkD,EAAE,KAAU;IAC/F,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC5J,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC1D,QAAQ,CAAC,KAAK,GAAG,sCAAsC,CAAC;QACxD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QACpB,QAAQ,CAAC,KAAK,GAAG,8BAA8B,CAAC;QAChD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAsB,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;IACrF,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;QACf,QAAQ,CAAC,KAAK,GAAG,wBAAwB,CAAC;QAC1C,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACvB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;IACxB,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,wBAAwB;AACxB,SAAgB,+BAA+B,CAAC,OAA6D,EAAE,KAAU;IACvH,kDAAkD;IAClD,wGAAwG;IACxG,sDAAsD;IACtD,0HAA0H;IAC1H,kDAAkD;IAClD,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,wBAAwB,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAClK,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,aAAa,GAAG,EAAE,CAAC,aAAa,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;IACtE,EAAE,CAAC,aAAa,CAAC,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,IAAI,EAAE,CAAC;IAC1D,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC9C,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;IACzC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AAED,SAAgB,kCAAkC,CAAC,OAAqD,EAAE,KAAU;IAClH,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACrK,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;QACzE,QAAQ,CAAC,KAAK,GAAG,uCAAuC,CAAC;QACzD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,QAAQ,IAAI,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACjI,QAAQ,CAAC,KAAK,GAAG,qCAAqC,CAAC;QACvD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IACxD,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;IACzC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AACD,SAAgB,mCAAmC,CAAC,OAAiD,EAAE,KAAU;IAC/G,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtK,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;QAC7B,QAAQ,CAAC,KAAK,GAAG,uCAAuC,CAAC;QACzD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAC3C,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;IACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC;AAED,mCAAmC;AACnC,SAAgB,+BAA+B,CAAC,OAAsE,EAAE,KAAU;IAChI,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,wBAAwB,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAClK,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,EAAE,CAAC,aAAa,GAAG,EAAE,GAAG,EAAE,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAC9D,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,aAAa,CAAC;IACjC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for Workspace settings messages and logic\r\nimport type { Workspace, WorkspaceFile, WorkspaceTeamMember, WorkspaceKnowledgebaseConfig } from '../types';\r\nimport type { KnowledgebaseSource } from '../sections/knowledgebaseSettingsSection';\r\n\r\n// Handler for Workspace settings messages and logic\r\nexport function handleWorkspaceMessage(message: any, panel: any) {\r\n  // Message pattern: { command: string, payload: any }\r\n  switch (message.command) {\r\n    case 'addWorkspace':\r\n      // payload: Partial<Workspace>\r\n      return addWorkspace(message.payload, panel);\r\n    case 'editWorkspace':\r\n      // payload: { id: string, updates: Partial<Workspace> }\r\n      return editWorkspace(message.payload, panel);\r\n    case 'deleteWorkspace':\r\n      // payload: { id: string }\r\n      return deleteWorkspace(message.payload, panel);\r\n    case 'addFile':\r\n      // payload: { workspaceId: string, file: WorkspaceFile }\r\n      return addFileToWorkspace(message.payload, panel);\r\n    case 'removeFile':\r\n      // payload: { workspaceId: string, filePath: string }\r\n      return removeFileFromWorkspace(message.payload, panel);\r\n    case 'tagFile':\r\n      // payload: { workspaceId: string, filePath: string, tags: string[] }\r\n      return tagFileInWorkspace(message.payload, panel);\r\n    case 'updateMemory':\r\n      // payload: { workspaceId: string, memory: string }\r\n      return updateWorkspaceMemory(message.payload, panel);\r\n    case 'addDocumentation':\r\n      // payload: { workspaceId: string, doc: { type: string, value: string, label?: string } }\r\n      return addWorkspaceDocumentation(message.payload, panel);\r\n    case 'removeDocumentation':\r\n      // payload: { workspaceId: string, docIndex: number }\r\n      return removeWorkspaceDocumentation(message.payload, panel);\r\n    case 'addTeamMember':\r\n      // payload: { workspaceId: string, member: WorkspaceTeamMember }\r\n      return addWorkspaceTeamMember(message.payload, panel);\r\n    case 'removeTeamMember':\r\n      // payload: { workspaceId: string, memberId: string }\r\n      return removeWorkspaceTeamMember(message.payload, panel);\r\n    case 'addKnowledgebaseSource':\r\n      // payload: { workspaceId: string, source: KnowledgebaseSource }\r\n      return addWorkspaceKnowledgebaseSource(message.payload, panel);\r\n    case 'removeKnowledgebaseSource':\r\n      // payload: { workspaceId: string, sourceIndex: number }\r\n      return removeWorkspaceKnowledgebaseSource(message.payload, panel); // Ensure this function is defined below\r\n    case 'toggleKnowledgebaseSharing':\r\n      // payload: { workspaceId: string, shared: boolean }\r\n      return toggleWorkspaceKnowledgebaseSharing(message.payload, panel);\r\n    case 'configureKnowledgebase':\r\n      // payload: { workspaceId: string, config: WorkspaceKnowledgebaseConfig }\r\n      return configureWorkspaceKnowledgebase(message.payload, panel);\r\n    default:\r\n      // Unknown command\r\n      return;\r\n  }\r\n}\r\n\r\n// --- Workspace CRUD ---\r\nfunction addWorkspace(payload: Partial<Workspace>, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'addWorkspace', success: false, data: null, error: null };\r\n  if (!payload || !payload.id || !payload.name || !payload.path) {\r\n    response.error = 'Missing required workspace fields.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  if (settings.workspaces.find((w: Workspace) => w.id === payload.id)) {\r\n    response.error = 'Workspace with this id already exists.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const now = Date.now();\r\n  const ws: Workspace = {\r\n    id: payload.id,\r\n    name: payload.name,\r\n    path: payload.path,\r\n    description: payload.description || '',\r\n    tags: payload.tags || [],\r\n    files: payload.files || [],\r\n    team: payload.team || [],\r\n    memory: payload.memory || '',\r\n    documentation: payload.documentation || [],\r\n    knowledgebase: payload.knowledgebase || { \r\n      sources: [], \r\n      shared: false, \r\n      autoSync: false, \r\n      syncInterval: 3600000, // 1 hour in milliseconds\r\n      syncStatus: 'idle',\r\n      chunkSize: 1000,\r\n      chunkOverlap: 200,\r\n      includePatterns: ['**/*'],\r\n      excludePatterns: ['**/node_modules/**', '**/.git/**']\r\n    },\r\n    createdAt: now,\r\n    updatedAt: now,\r\n    isActive: true,\r\n    settings: {\r\n      autoSave: true,\r\n      autoFormat: true,\r\n      lintOnSave: true,\r\n      formatOnSave: true,\r\n      experimentalFeatures: false\r\n    }\r\n  };\r\n  settings.workspaces.push(ws);\r\n  response.success = true;\r\n  response.data = ws;\r\n  panel.postMessage(response);\r\n}\r\nfunction editWorkspace(payload: { id: string, updates: Partial<Workspace> }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'editWorkspace', success: false, data: null, error: null };\r\n  if (!payload || !payload.id) {\r\n    response.error = 'Workspace id required.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const idx = settings.workspaces.findIndex((w: Workspace) => w.id === payload.id);\r\n  if (idx === -1) {\r\n    response.error = 'Workspace not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  settings.workspaces[idx] = { ...settings.workspaces[idx], ...payload.updates };\r\n  response.success = true;\r\n  response.data = settings.workspaces[idx];\r\n  panel.postMessage(response);\r\n}\r\nfunction deleteWorkspace(payload: { id: string }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'deleteWorkspace', success: false, data: null, error: null };\r\n  if (!payload || !payload.id) {\r\n    response.error = 'Workspace id required.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const idx = settings.workspaces.findIndex((w: Workspace) => w.id === payload.id);\r\n  if (idx === -1) {\r\n    response.error = 'Workspace not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  settings.workspaces.splice(idx, 1);\r\n  response.success = true;\r\n  panel.postMessage(response);\r\n}\r\n// --- File Management ---\r\nfunction addFileToWorkspace(payload: { workspaceId: string, file: WorkspaceFile }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'addFile', success: false, data: null, error: null };\r\n  if (!payload || !payload.workspaceId || !payload.file || !payload.file.path) {\r\n    response.error = 'Missing required file or workspace fields.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws) {\r\n    response.error = 'Workspace not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.files = ws.files || [];\r\n  if (ws.files.find((f: WorkspaceFile) => f.path === payload.file.path)) {\r\n    response.error = 'File already exists in workspace.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.files.push(payload.file);\r\n  response.success = true;\r\n  response.data = ws.files;\r\n  panel.postMessage(response);\r\n}\r\nfunction removeFileFromWorkspace(payload: { workspaceId: string, filePath: string }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'removeFile', success: false, data: null, error: null };\r\n  if (!payload || !payload.workspaceId || !payload.filePath) {\r\n    response.error = 'Missing required file or workspace fields.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws || !ws.files) {\r\n    response.error = 'Workspace or file not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const idx = ws.files.findIndex((f: WorkspaceFile) => f.path === payload.filePath);\r\n  if (idx === -1) {\r\n    response.error = 'File not found in workspace.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.files.splice(idx, 1);\r\n  response.success = true;\r\n  response.data = ws.files;\r\n  panel.postMessage(response);\r\n}\r\nfunction tagFileInWorkspace(payload: { workspaceId: string, filePath: string, tags: string[] }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'tagFile', success: false, data: null, error: null };\r\n  if (!payload || !payload.workspaceId || !payload.filePath || !Array.isArray(payload.tags)) {\r\n    response.error = 'Missing required fields.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws || !ws.files) {\r\n    response.error = 'Workspace or file not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const file = ws.files.find((f: WorkspaceFile) => f.path === payload.filePath);\r\n  if (!file) {\r\n    response.error = 'File not found in workspace.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  file.tags = payload.tags;\r\n  response.success = true;\r\n  response.data = file;\r\n  panel.postMessage(response);\r\n}\r\n// --- Workspace Memory ---\r\nfunction updateWorkspaceMemory(payload: { workspaceId: string, memory: string }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'updateMemory', success: false, data: null, error: null };\r\n  if (!payload || !payload.workspaceId) {\r\n    response.error = 'Missing required workspace id.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws) {\r\n    response.error = 'Workspace not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.memory = payload.memory;\r\n  response.success = true;\r\n  response.data = ws.memory;\r\n  panel.postMessage(response);\r\n}\r\n// --- Documentation ---\r\nfunction addWorkspaceDocumentation(payload: { workspaceId: string, doc: { type: string, value: string, label?: string } }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'addDocumentation', success: false, data: null, error: null };\r\n  if (!payload || !payload.workspaceId || !payload.doc || !payload.doc.type || !payload.doc.value) {\r\n    response.error = 'Missing required documentation fields.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws) {\r\n    response.error = 'Workspace not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.documentation = ws.documentation || [];\r\n  ws.documentation.push(payload.doc);\r\n  response.success = true;\r\n  response.data = ws.documentation;\r\n  panel.postMessage(response);\r\n}\r\nfunction removeWorkspaceDocumentation(payload: { workspaceId: string, docIndex: number }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'removeDocumentation', success: false, data: null, error: null };\r\n  if (!payload || !payload.workspaceId || typeof payload.docIndex !== 'number') {\r\n    response.error = 'Missing required documentation fields.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws || !ws.documentation || payload.docIndex < 0 || payload.docIndex >= ws.documentation.length) {\r\n    response.error = 'Workspace or documentation not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.documentation.splice(payload.docIndex, 1);\r\n  response.success = true;\r\n  response.data = ws.documentation;\r\n  panel.postMessage(response);\r\n}\r\n// --- Team Members ---\r\nfunction addWorkspaceTeamMember(payload: { workspaceId: string, member: WorkspaceTeamMember }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'addTeamMember', success: false, data: null, error: null };\r\n  if (!payload || !payload.workspaceId || !payload.member || !payload.member.id) {\r\n    response.error = 'Missing required team member fields.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws) {\r\n    response.error = 'Workspace not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.team = ws.team || [];\r\n  if (ws.team.find((m: WorkspaceTeamMember) => m.id === payload.member.id)) {\r\n    response.error = 'Team member already exists.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.team.push(payload.member);\r\n  response.success = true;\r\n  response.data = ws.team;\r\n  panel.postMessage(response);\r\n}\r\nfunction removeWorkspaceTeamMember(payload: { workspaceId: string, memberId: string }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'removeTeamMember', success: false, data: null, error: null };\r\n  if (!payload || !payload.workspaceId || !payload.memberId) {\r\n    response.error = 'Missing required team member fields.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws || !ws.team) {\r\n    response.error = 'Workspace or team not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  const idx = ws.team.findIndex((m: WorkspaceTeamMember) => m.id === payload.memberId);\r\n  if (idx === -1) {\r\n    response.error = 'Team member not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.team.splice(idx, 1);\r\n  response.success = true;\r\n  response.data = ws.team;\r\n  panel.postMessage(response);\r\n}\r\n// --- Knowledgebase ---\r\nexport function addWorkspaceKnowledgebaseSource(payload: { workspaceId: string, source: KnowledgebaseSource }, panel: any) {\r\n  // Modular: delegate to knowledgebaseSection logic\r\n  // Example: knowledgebaseSection.addKnowledgebaseSourceToWorkspace(payload.workspaceId, payload.source);\r\n  // If not implemented, add in knowledgebaseSection.ts:\r\n  // export function addKnowledgebaseSourceToWorkspace(workspaceId: string, source: KnowledgebaseSource, panel: any) { ... }\r\n  // Directly update workspace knowledgebase sources\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'addKnowledgebaseSource', success: false, data: null, error: null };\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws) {\r\n    response.error = 'Workspace not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.knowledgebase = ws.knowledgebase || { sources: [], shared: false };\r\n  ws.knowledgebase.sources = ws.knowledgebase.sources || [];\r\n  ws.knowledgebase.sources.push(payload.source);\r\n  response.success = true;\r\n  response.data = ws.knowledgebase.sources;\r\n  panel.postMessage(response);\r\n}\r\n\r\nexport function removeWorkspaceKnowledgebaseSource(payload: { workspaceId: string, sourceIndex: number }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'removeKnowledgebaseSource', success: false, data: null, error: null };\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws || !ws.knowledgebase || !Array.isArray(ws.knowledgebase.sources)) {\r\n    response.error = 'Workspace or knowledgebase not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  if (typeof payload.sourceIndex !== 'number' || payload.sourceIndex < 0 || payload.sourceIndex >= ws.knowledgebase.sources.length) {\r\n    response.error = 'Invalid knowledgebase source index.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.knowledgebase.sources.splice(payload.sourceIndex, 1);\r\n  response.success = true;\r\n  response.data = ws.knowledgebase.sources;\r\n  panel.postMessage(response);\r\n}\r\nexport function toggleWorkspaceKnowledgebaseSharing(payload: { workspaceId: string, shared: boolean }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'toggleKnowledgebaseSharing', success: false, data: null, error: null };\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws || !ws.knowledgebase) {\r\n    response.error = 'Workspace or knowledgebase not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.knowledgebase.shared = !!payload.shared;\r\n  response.success = true;\r\n  response.data = ws.knowledgebase.shared;\r\n  panel.postMessage(response);\r\n}\r\n\r\n// Configure knowledgebase settings\r\nexport function configureWorkspaceKnowledgebase(payload: { workspaceId: string, config: WorkspaceKnowledgebaseConfig }, panel: any) {\r\n  const settings = (window as any).settings || {};\r\n  settings.workspaces = Array.isArray(settings.workspaces) ? settings.workspaces : [];\r\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: 'configureKnowledgebase', success: false, data: null, error: null };\r\n  const ws = settings.workspaces.find((w: Workspace) => w.id === payload.workspaceId);\r\n  if (!ws) {\r\n    response.error = 'Workspace not found.';\r\n    panel.postMessage(response);\r\n    return;\r\n  }\r\n  ws.knowledgebase = { ...ws.knowledgebase, ...payload.config };\r\n  response.success = true;\r\n  response.data = ws.knowledgebase;\r\n  panel.postMessage(response);\r\n}\r\n"]}