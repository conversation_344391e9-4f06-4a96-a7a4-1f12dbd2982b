"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PineconeVectorStore = void 0;
const logger_1 = require("../../../logger"); // Assuming path relative to the final file location
const config_1 = require("../../../config"); // Assuming path relative to the final file location
const pinecone_1 = __importDefault(require("@pinecone-database/pinecone")); // Import the default export
/**
 * Pinecone vector store implementation using the official Pinecone client and LangChain integration.
 * Handles initialization, adding, retrieving, deleting, clearing, and searching vectors in a Pinecone index.
 */
class PineconeVectorStore {
    embeddings;
    config;
    pineconeClient; // Pinecone client instance
    pineconeIndex;
    // PineconeStore from LangChain - used primarily for search abstraction if needed,
    // but direct client usage is often preferred for full feature access.
    // We will prioritize direct client usage for clarity and control,
    // but keep the instance if needed for LangChain compatibility methods.
    langchainPineconeStore;
    initialized = false;
    isInitializing = false;
    /**
       * Creates an instance of PineconeVectorStore.
       * @param embeddings - The embeddings model instance to use (primarily for LangChain compatibility if used).
       */
    constructor(embeddings) {
        this.embeddings = embeddings; // Store embeddings, mainly for potential LangChain Store usage
        const apiKey = (0, config_1.getConfig)('memory.vectorStore.pinecone.apiKey', '');
        const environment = (0, config_1.getConfig)('memory.vectorStore.pinecone.environment', '');
        const indexName = (0, config_1.getConfig)('memory.vectorStore.pinecone.indexName', 'codessa-memories');
        const namespace = (0, config_1.getConfig)('memory.vectorStore.pinecone.namespace', undefined); // Allow undefined namespace
        if (!apiKey || !environment || !indexName) {
            throw new Error('Pinecone configuration (apiKey, environment, indexName) is missing.');
        }
        this.config = {
            apiKey,
            environment,
            indexName,
            namespace,
        };
        logger_1.logger.info(`PineconeVectorStore configured: Index='${this.config.indexName}', Environment='${this.config.environment}', Namespace='${this.config.namespace ?? '(default)'}'`);
    }
    /**
       * Initializes the connection to the Pinecone index.
       * Ensures the Pinecone client is ready and the target index is accessible.
       * This method is idempotent.
       * @throws {Error} If initialization fails (e.g., invalid credentials, index not found).
       */
    async initialize() {
        if (this.initialized || this.isInitializing) {
            if (this.isInitializing) {
                // Wait for the ongoing initialization to complete
                await new Promise(resolve => {
                    const checkInterval = setInterval(() => {
                        if (!this.isInitializing) {
                            clearInterval(checkInterval);
                            resolve();
                        }
                    }, 100);
                });
            }
            if (this.initialized) {
                logger_1.logger.debug('PineconeVectorStore already initialized.');
            }
            return;
        }
        this.isInitializing = true;
        logger_1.logger.info(`Initializing Pinecone vector store connection to index '${this.config.indexName}'...`);
        try {
            // 1. Initialize Pinecone Client
            this.pineconeClient = new pinecone_1.default(); // Cast to bypass type error
            if (!this.pineconeClient) {
                throw new Error('Failed to create Pinecone client');
            }
            await this.pineconeClient.init({
                apiKey: this.config.apiKey,
                environment: this.config.environment,
            });
            logger_1.logger.debug('Pinecone client initialized.');
            // 2. Get Index Handle
            // Note: This does NOT create the index. It must exist beforehand.
            this.pineconeIndex = this.pineconeClient.Index(this.config.indexName);
            logger_1.logger.debug(`Handle obtained for Pinecone index '${this.config.indexName}'.`);
            // 3. Optional: Verify Index Connection (e.g., by describing stats)
            try {
                const stats = await this.pineconeIndex.describeIndexStats();
                logger_1.logger.info(`Successfully connected to Pinecone index '${this.config.indexName}'. Stats:`, stats);
                // Store stats if needed, e.g., dimension: stats.dimension
            }
            catch (statsError) {
                const errorMessage = statsError instanceof Error ? statsError.message : String(statsError);
                logger_1.logger.error(`Failed to verify connection or get stats for index '${this.config.indexName}'. Please ensure the index exists and credentials are correct.`, { message: errorMessage });
                throw new Error(`Failed to connect to Pinecone index '${this.config.indexName}': ${errorMessage}`);
            }
            // 4. Optional: Initialize LangChain PineconeStore if specific LangChain features are needed
            // This might be useful if integrating with chains that expect a LangChain VectorStore instance.
            // Otherwise, direct client usage (this.pineconeIndex) is sufficient.
            // this.langchainPineconeStore = await PineconeStore.fromExistingIndex(this.embeddings, {
            //     pineconeIndex: this.pineconeIndex,
            //     namespace: this.config.namespace,
            // });
            // logger.debug('LangChain PineconeStore wrapper initialized (optional).');
            this.initialized = true;
            logger_1.logger.info(`Pinecone vector store initialized successfully for index '${this.config.indexName}'.`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.logger.error('Failed to initialize Pinecone vector store:', { message: errorMessage, stack: errorStack, environment: this.config.environment, namespace: this.config.namespace });
            this.initialized = false;
            this.pineconeClient = undefined;
            this.pineconeIndex = undefined;
            this.langchainPineconeStore = undefined;
            throw new Error(`Pinecone initialization failed: ${errorMessage}`);
        }
        finally {
            this.isInitializing = false;
        }
    }
    /**
       * Throws an error if the vector store is not initialized.
       */
    assertInitialized() {
        if (!this.initialized || !this.pineconeClient || !this.pineconeIndex) {
            logger_1.logger.error('PineconeVectorStore accessed before successful initialization.', {
                initialized: this.initialized,
                hasClient: !!this.pineconeClient,
                hasIndex: !!this.pineconeIndex,
            });
            throw new Error('PineconeVectorStore is not initialized. Call initialize() first.');
        }
    }
    /**
       * Prepares metadata for Pinecone, ensuring compatibility.
       * Converts values to supported types (string, number, boolean, string[]).
       * Removes nested objects and unsupported types. Includes the original text content if provided.
       * @param metadata Raw metadata object.
       * @param textContent Optional text content associated with the vector.
       * @returns Sanitized metadata object suitable for Pinecone.
       */
    sanitizeMetadata(metadata, textContent) {
        const sanitized = {};
        const MAX_METADATA_VALUE_LENGTH = 1024; // Example limit, adjust as needed
        const MAX_TAG_LENGTH = 100;
        const MAX_TAGS = 50;
        for (const key in metadata) {
            if (!Object.prototype.hasOwnProperty.call(metadata, key)) {
                continue;
            }
            const value = metadata[key];
            if (typeof value === 'string') {
                sanitized[key] = value.substring(0, MAX_METADATA_VALUE_LENGTH);
            }
            else if (typeof value === 'number' && Number.isFinite(value)) {
                sanitized[key] = value;
            }
            else if (typeof value === 'boolean') {
                sanitized[key] = value;
            }
            else if (Array.isArray(value) && value.every(item => typeof item === 'string')) {
                // Ensure all items are strings and sanitize/limit them
                sanitized[key] = value
                    .map(tag => tag.substring(0, MAX_TAG_LENGTH))
                    .slice(0, MAX_TAGS);
            }
            else {
                // Log unsupported types/values
                logger_1.logger.warn(`[${this.config.indexName}] Metadata key '${key}' has unsupported type '${typeof value}' or value. Skipping.`);
            }
        }
        // Optionally include the original text content in metadata if provided and not too long
        if (textContent && !sanitized.text) { // Avoid overwriting if 'text' already exists
            sanitized.text = textContent.substring(0, MAX_METADATA_VALUE_LENGTH * 5); // Allow longer text field
        }
        return sanitized;
    }
    /**
       * Adds a vector with associated metadata to the Pinecone index.
       * Uses the direct Pinecone client for upserting.
       * @param id - A unique identifier for the vector.
       * @param vector - The vector embedding.
       * @param metadata - Metadata associated with the vector. Should contain simple key-value pairs.
       * @throws {Error} If the store is not initialized or if the upsert operation fails.
       */
    async addVector(id, vector, metadata = {}) {
        this.assertInitialized();
        // Extract potential text content for metadata inclusion
        const textContent = metadata?.content ?? metadata?.text ?? ''; // Look for common text fields
        const sanitizedMeta = this.sanitizeMetadata(metadata, typeof textContent === 'string' ? textContent : undefined);
        const vectorToUpsert = {
            id,
            values: vector,
            metadata: sanitizedMeta,
        };
        try {
            logger_1.logger.debug(`[${this.config.indexName}] Upserting vector with ID ${id} (Namespace: ${this.config.namespace ?? 'none'})...`);
            const upsertRequest = {
                vectors: [vectorToUpsert],
                namespace: this.config.namespace,
            };
            if (!this.pineconeIndex) {
                throw new Error('Pinecone index not initialized');
            }
            await this.pineconeIndex.upsert(upsertRequest);
            logger_1.logger.info(`[${this.config.indexName}] Successfully upserted vector with ID ${id}.`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.logger.error(`[${this.config.indexName}] Failed to upsert vector with ID ${id}:`, { message: errorMessage, stack: errorStack, id, namespace: this.config.namespace });
            // Provide more context from Pinecone errors if possible
            if (error && typeof error === 'object' && 'response' in error) {
                const errorObj = error;
                if (errorObj.response?.body) {
                    logger_1.logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);
                }
            }
            throw new Error(`Failed to add vector ${id} to Pinecone: ${errorMessage}`);
        }
    }
    /**
       * Retrieves a vector embedding by its ID directly from Pinecone.
       * Note: This uses the direct Pinecone client, as it's not a standard part of the basic LangChain VectorStore interface.
       * @param id - The unique identifier of the vector to retrieve.
       * @returns The vector embedding as an array of numbers, or undefined if not found or on error.
       * @throws {Error} If the store is not initialized.
       */
    async getVector(id) {
        this.assertInitialized();
        try {
            logger_1.logger.debug(`[${this.config.indexName}] Fetching vector with ID ${id} (Namespace: ${this.config.namespace ?? 'none'})...`);
            if (!this.pineconeIndex) {
                throw new Error('Pinecone index not initialized');
            }
            const fetchResponse = await this.pineconeIndex.fetch({ ids: [id], namespace: this.config.namespace });
            const record = fetchResponse?.vectors?.[id];
            if (record?.values) {
                logger_1.logger.debug(`[${this.config.indexName}] Successfully fetched vector for ID ${id}.`);
                return record.values;
            }
            else {
                logger_1.logger.debug(`[${this.config.indexName}] Vector with ID ${id} not found.`);
                return undefined;
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.logger.error(`[${this.config.indexName}] Failed to fetch vector with ID ${id}:`, { message: errorMessage, stack: errorStack, id, namespace: this.config.namespace });
            if (error && typeof error === 'object' && 'response' in error) {
                const errorObj = error;
                if (errorObj.response?.body) {
                    logger_1.logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);
                }
            }
            // Don't throw, return undefined as per method signature on error
            return undefined;
        }
    }
    /**
       * Deletes a vector by its ID from the Pinecone index.
       * @param id - The unique identifier of the vector to delete.
       * @returns `true` if deletion was successful or the vector didn't exist, `false` on error.
       * @throws {Error} If the store is not initialized.
       */
    async deleteVector(id) {
        this.assertInitialized();
        try {
            logger_1.logger.debug(`[${this.config.indexName}] Deleting vector with ID ${id} (Namespace: ${this.config.namespace ?? 'none'})...`);
            if (!this.pineconeIndex) {
                throw new Error('Pinecone index not initialized');
            }
            await this.pineconeIndex.delete1({ id, namespace: this.config.namespace }); // Use delete1 for single ID
            logger_1.logger.info(`[${this.config.indexName}] Delete request successful for vector ID ${id} (vector may or may not have existed).`);
            return true; // Pinecone delete is idempotent, succeeds even if ID doesn't exist
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.logger.error(`[${this.config.indexName}] Failed to delete vector with ID ${id}:`, { message: errorMessage, stack: errorStack, id, namespace: this.config.namespace });
            if (error && typeof error === 'object' && 'response' in error) {
                const errorObj = error;
                if (errorObj.response?.body) {
                    logger_1.logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);
                }
            }
            return false;
        }
    }
    /**
       * Clears vectors from the Pinecone index.
       * If a namespace is configured, only that namespace is cleared.
       * If no namespace is configured, this will clear **ALL** vectors in the index.
       * **Warning:** This is a destructive operation.
       * @throws {Error} If the store is not initialized or if clearing fails.
       */
    async clearVectors() {
        this.assertInitialized();
        const target = this.config.namespace ? `namespace '${this.config.namespace}'` : 'the entire index';
        logger_1.logger.warn(`[${this.config.indexName}] Attempting to clear all vectors from ${target}...`);
        try {
            if (this.config.namespace) {
                if (!this.pineconeIndex) {
                    throw new Error('Pinecone index not initialized');
                }
                await this.pineconeIndex.deleteAll({ namespace: this.config.namespace });
            }
            else {
                // Double-check or add safety config before allowing full index delete?
                // For now, proceed as configured.
                if (!this.pineconeIndex) {
                    throw new Error('Pinecone index not initialized');
                }
                await this.pineconeIndex.deleteAll({});
            }
            logger_1.logger.info(`[${this.config.indexName}] Successfully cleared vectors from ${target}.`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.logger.error(`[${this.config.indexName}] Failed to clear vectors from ${target}:`, { message: errorMessage, stack: errorStack, namespace: this.config.namespace });
            if (error && typeof error === 'object' && 'response' in error) {
                const errorObj = error;
                if (errorObj.response?.body) {
                    logger_1.logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);
                }
            }
            throw new Error(`Failed to clear Pinecone vectors: ${errorMessage}`);
        }
    }
    /**
       * Searches for vectors similar to the provided query vector within the Pinecone index.
       * Supports metadata filtering using Pinecone's filter syntax.
       * @param vector - The query vector embedding.
       * @param limit - The maximum number of similar vectors to return.
       * @param filter - Optional metadata filter object conforming to Pinecone's filter syntax.
       *                 Example: `{ "genre": { "$eq": "fiction" }, "year": { "$gte": 2020 } }`
       * @returns A promise resolving to an array of objects, each containing the ID and similarity score.
       * @throws {Error} If the store is not initialized or if the search fails.
       */
    async searchSimilarVectors(vector, limit = 5, filter) {
        this.assertInitialized();
        if (!Array.isArray(vector) || vector.length === 0) {
            logger_1.logger.error('Invalid query vector provided for similarity search.');
            throw new Error('Query vector must be a non-empty array of numbers.');
        }
        try {
            logger_1.logger.debug(`[${this.config.indexName}] Searching for ${limit} similar vectors (Namespace: ${this.config.namespace ?? 'none'})...`, { filter: filter ? 'present' : 'absent' });
            const queryRequest = {
                vector,
                topK: limit,
                namespace: this.config.namespace,
                filter: filter, // Pass filter directly, assuming it's Pinecone compatible
                includeMetadata: false, // Don't need metadata here, just ID and score
                includeValues: false, // Don't need vector values
            };
            if (!this.pineconeIndex) {
                throw new Error('Pinecone index not initialized');
            }
            const queryResponse = await this.pineconeIndex.query(queryRequest);
            const matches = queryResponse.matches ?? [];
            logger_1.logger.debug(`[${this.config.indexName}] Pinecone query returned ${matches.length} matches.`);
            // Map results to the required format { id: string, score: number }
            const results = matches.map((match) => ({
                id: match.id,
                // Ensure score is a number, default to 0 if missing (shouldn't happen with Pinecone)
                score: typeof match.score === 'number' ? match.score : 0,
            }));
            return results;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.logger.error(`[${this.config.indexName}] Failed to search similar vectors:`, { message: errorMessage, stack: errorStack, limit, filter, namespace: this.config.namespace });
            if (error && typeof error === 'object' && 'response' in error) {
                const errorObj = error;
                if (errorObj.response?.body) {
                    logger_1.logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);
                }
            }
            // Return empty array on error as per original signature
            return [];
        }
    }
    /**
       * Provides direct access to the initialized Pinecone Index object for advanced operations.
       * Use with caution, as it bypasses the IVectorStore abstraction.
       * @returns The Pinecone Index instance.
       * @throws {Error} If the store is not initialized.
       */
    getPineconeIndex() {
        this.assertInitialized();
        if (!this.pineconeIndex) {
            throw new Error('Pinecone index not initialized');
        }
        return this.pineconeIndex;
    }
}
exports.PineconeVectorStore = PineconeVectorStore;
//# sourceMappingURL=pineconeVectorStore.js.map