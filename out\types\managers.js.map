{"version": 3, "file": "managers.js", "sourceRoot": "", "sources": ["../../src/types/managers.ts"], "names": [], "mappings": ";AAAA;;GAEG", "sourcesContent": ["/**\n * Type definitions for various manager classes used in the Codessa application\n */\n\nimport { MCPContext } from '../mcp/mcpManager';\nimport { GraphDefinition } from '../agents/workflows/graphTypes';\n\nimport { Logger } from '../utils/logger';\n\n/**\n * Interface for the workflow manager\n */\nexport interface IWorkflowManager {\n  /**\n   * Get a workflow for a specific mode\n   */\n  getWorkflowForMode(mode: string): Promise<GraphDefinition | undefined>;\n\n  /**\n   * Get all available workflows\n   */\n  getAllWorkflows(): Promise<GraphDefinition[]>;\n\n  /**\n   * Get workflows by tag\n   */\n  getWorkflowsByTag(tag: string): Promise<GraphDefinition[]>;\n\n  /**\n   * Logger instance\n   */\n  readonly logger: Logger;\n}\n\n/**\n * Interface for the MCP manager\n */\nexport interface IMCPManager {\n  /**\n   * Get the current context\n   */\n  getCurrentContext(): MCPContext;\n\n  /**\n   * Update the context with new content\n   */\n  updateContext(content: any): void;\n\n  /**\n   * Add code to the context\n   */\n  addCode(language: string, content: string, path?: string): void;\n\n  /**\n   * Add a file to the context\n   */\n  addFile(path: string, content: string, language?: string): void;\n}\n\n/**\n * Interface for the prompt manager\n */\nexport interface IPromptManager {\n  /**\n   * Get a system prompt by name\n   */\n  getSystemPrompt(name: string, variables?: Record<string, unknown>): string;\n\n  /**\n   * Get a prompt by ID\n   */\n  getPrompt(id: string): any;\n\n  /**\n   * Render a prompt with variables\n   */\n  renderPrompt(promptId: string, variables?: Record<string, string>): string;\n}\n\n/**\n * Interface for the knowledgebase manager\n */\nexport interface IKnowledgebaseManager {\n  /**\n   * Get relevant knowledge for a query\n   */\n  getRelevantKnowledge(query: string): Promise<string>;\n\n  /**\n   * Add knowledge to the knowledgebase\n   */\n  addKnowledge(content: string, metadata?: Record<string, any>): Promise<void>;\n\n  /**\n   * Search the knowledgebase\n   */\n  searchKnowledge(query: string): Promise<any[]>;\n}"]}