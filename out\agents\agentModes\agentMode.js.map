{"version": 3, "file": "agentMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/agentMode.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,mDAA4E;AAE5E,+DAA4D;AAE5D,yCAAsC;AACtC,qDAAkD;AAGlD;;GAEG;AACH,MAAa,SAAU,SAAQ,6BAAa;IACjC,EAAE,GAAG,OAAO,CAAC;IACb,WAAW,GAAG,OAAO,CAAC;IACtB,WAAW,GAAG,wEAAwE,CAAC;IACvF,IAAI,GAAG,UAAU,CAAC;IAClB,kBAAkB,GAAG,2BAAW,CAAC,eAAe,CAAC;IACjD,yBAAyB,GAAG,KAAK,CAAC;IAClC,sBAAsB,GAAG,KAAK,CAAC;IAEhC,SAAS,GAAG,KAAK,CAAC;IAClB,iBAAiB,CAA6C;IAC9D,iBAAiB,CAAwE;IAC1F,aAAa,GAAqC,SAAS,CAAC;IAEnE;;SAEK;IACL,KAAK,CAAC,UAAU,CAAC,OAAgC;QAC/C,MAAM,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEhC,yBAAyB;QACzB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC5F,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,sBAAsB,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,yBAAyB,CAAC;QACvD,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY,EACZ,aAA4B,EAC5B,iBAA2C;QAE3C,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,OAAO,EAAE,CAAC,CAAC;YAErE,kBAAkB;YAClB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;YAErD,OAAO,qFAAqF,CAAC;QAC/F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3F,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,UAAU,CACtB,IAAY,EACZ,KAAY,EACZ,aAA4B;QAE5B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;QAC5F,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QAE9D,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,yBAAyB,CAAC;YACpD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC5B,CAAC;QAED,2BAA2B;QAC3B,MAAM,CAAC,MAAM,CAAC,YAAY,CACxB;YACE,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;YAC9C,KAAK,EAAE,eAAe;YACtB,WAAW,EAAE,IAAI;SAClB,EACD,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;YACxB,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;YAElC,sBAAsB;YACtB,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE;gBACjC,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;gBACjC,IAAI,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;YAElD,IAAI,CAAC;gBACH,sBAAsB;gBACtB,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBAE7E,kCAAkC;gBAClC,IAAI,aAAa,GAAG,EAAE,CAAC;gBACvB,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;oBACtC,IAAI,WAAW,EAAE,CAAC;wBAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;wBACrE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;4BACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,qCAAqC,CAAC,CAAC;wBAC/F,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8CAA8C,EAAE,WAAW,CAAC,CAAC;oBAClF,kCAAkC;gBACpC,CAAC;gBAED,gDAAgD;gBAChD,MAAM,YAAY,GAAG,6BAAa,CAAC,eAAe,CAAC,WAAW,EAAE;oBAC9D,IAAI;oBACJ,OAAO,EAAE,cAAc;oBACvB,aAAa;iBACd,CAAC,IAAI,yCAAyC,IAAI,iBAAiB,cAAc,OAAO,aAAa,EAAE,CAAC;gBAEzG,yBAAyB;gBACzB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC3B,MAAM,KAAK,GAAiB,EAAE,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;oBAChF,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACpE,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;gBAC5D,CAAC;gBAED,oBAAoB;gBACpB,IAAI,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;gBAE9C,+BAA+B;gBAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4CAA4C,CAAC,CAAC;YACrF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAC1D,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAEnF,0BAA0B;gBAC1B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC1H,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAC5B,YAAoB,EACpB,KAAY,EACZ,QAAmE,EACnE,KAAmB;QAEnB,oDAAoD;QACpD,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,mBAAmB;QACnB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC,CAAC;QAExE,wBAAwB;QACxB,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,QAAQ,CACvC,GAAG,YAAY,gGAAgG,EAC/G,IAAI,CAAC,YAAY,EAAE,EACnB,KAAK,CACN,CAAC;QAEF,cAAc;QACd,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC,CAAC;QAEpE,gBAAgB;QAChB,IAAI,cAAc,GAAG,GAAG,YAAY,iBAAiB,YAAY,8CAA8C,CAAC;QAEhH,OAAO,WAAW,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;YACpF,WAAW,EAAE,CAAC;YAEd,kBAAkB;YAClB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,kBAAkB,WAAW,KAAK,EAAE,SAAS,EAAE,GAAG,GAAG,SAAS,EAAE,CAAC,CAAC;YAE7F,qBAAqB;YACrB,MAAM,UAAU,GAAG,GAAG,cAAc,YAAY,WAAW,IAAI,CAAC;YAChE,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,CAAC;YAElF,sBAAsB;YACtB,cAAc,IAAI,YAAY,WAAW,MAAM,YAAY,EAAE,CAAC;YAE9D,4BAA4B;YAC5B,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;gBACtD,YAAY,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;gBAC7D,MAAM;YACR,CAAC;YAED,uCAAuC;YACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,eAAe;QACf,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;QAE3D,mBAAmB;QACnB,MAAM,aAAa,GAAG,GAAG,cAAc,uGAAuG,CAAC;QAC/I,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,CAAC;QAExF,yBAAyB;QACzB,cAAc,IAAI,iBAAiB,eAAe,EAAE,CAAC;QAErD,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACvD,OAAO,EAAE,cAAc;YACvB,QAAQ,EAAE,UAAU;SACrB,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACG,SAAS,CAAC,MAAc;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QAEnC,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,sBAAsB,CAAC;QACnD,CAAC;QAED,aAAa;QACb,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,MAAM,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG,EAAE,oDAAoD;YACtE,SAAS,EAAE,IAAI,EAAG,0CAA0C;YAC5D,aAAa,EAAE,EAAE;YACjB,IAAI,EAAE,MAAM;SACb,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,eAAe;QAKb,OAAO;YACL,YAAY,EAAE;;;;;;;;;;;CAWnB;YACK,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;CAoBnB;YACK,YAAY,EAAE;;;;;CAKnB;SACI,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,IAAW;QAC9C,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,YAAY;gBACf,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACxC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,aAAa,CAAC,GAAG,IAAI,CAAC;oBAC1C,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;gBACpD,CAAC;gBACD,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACnB,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;oBACjC,IAAI,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;gBACpD,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;CACF;AAxTD,8BAwTC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { promptManager } from '../../prompts/promptManager';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { contextManager } from './contextManager';\nimport { AgentContext } from '../../types/agent';\n\n/**\n * Agent Mode - Autonomous operation with minimal user interaction\n */\nexport class AgentMode extends OperationMode {\n  readonly id = 'agent';\n  readonly displayName = 'Agent';\n  readonly description = 'Autonomous AI agent that completes tasks with minimal user interaction';\n  readonly icon = '$(robot)';\n  readonly defaultContextType = ContextType.ENTIRE_CODEBASE;\n  readonly requiresHumanVerification = false;\n  readonly supportsMultipleAgents = false;\n\n  private isRunning = false;\n  private cancelTokenSource: vscode.CancellationTokenSource | undefined;\n  private _progressReporter: vscode.Progress<{ message?: string; increment?: number }> | undefined;\n  public statusBarItem: vscode.StatusBarItem | undefined = undefined;\n\n  /**\n     * Initialize the Agent mode\n     */\n  async initialize(context: vscode.ExtensionContext): Promise<void> {\n    await super.initialize(context);\n\n    // Create status bar item\n    this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);\n    this.statusBarItem.text = '$(robot) Agent: Idle';\n    this.statusBarItem.tooltip = 'Codessa Agent Mode';\n    this.statusBarItem.command = 'codessa.toggleAgentMode';\n    context.subscriptions.push(this.statusBarItem);\n  }\n\n  /**\n     * Process a user message in Agent mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    contextSource: ContextSource,\n    _additionalParams?: Record<string, unknown>\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Agent mode: ${message}`);\n\n      // Start the agent\n      await this.startAgent(message, agent, contextSource);\n\n      return 'Agent started. I will work on this task autonomously and report back when finished.';\n    } catch (error) {\n      Logger.instance.error('Error processing message in Agent mode:', error);\n      return `Error starting agent: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Start the agent\n     */\n  private async startAgent(\n    task: string,\n    agent: Agent,\n    contextSource: ContextSource\n  ): Promise<void> {\n    if (this.isRunning) {\n      throw new Error('Agent is already running. Please wait for it to complete or cancel it.');\n    }\n\n    this.isRunning = true;\n    this.cancelTokenSource = new vscode.CancellationTokenSource();\n\n    // Update status bar\n    if (this.statusBarItem) {\n      this.statusBarItem.text = '$(robot) Agent: Running';\n      this.statusBarItem.show();\n    }\n\n    // Start progress indicator\n    vscode.window.withProgress(\n      {\n        location: vscode.ProgressLocation.Notification,\n        title: 'Codessa Agent',\n        cancellable: true\n      },\n      async (progress, token) => {\n        this._progressReporter = progress;\n\n        // Handle cancellation\n        token.onCancellationRequested(() => {\n          this.cancelTokenSource?.cancel();\n          this.stopAgent('User cancelled the operation');\n        });\n\n        progress.report({ message: 'Starting agent...' });\n\n        try {\n          // Get context content\n          const contextContent = await contextManager.getContextContent(contextSource);\n\n          // Add memory context if available\n          let memoryContext = '';\n          try {\n            const agentMemory = agent.getMemory();\n            if (agentMemory) {\n              const relevantMemories = await agentMemory.getRelevantMemories(task);\n              if (relevantMemories && relevantMemories.length > 0) {\n                memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n                Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to agent context`);\n              }\n            }\n          } catch (memoryError) {\n            Logger.instance.warn('Failed to retrieve memory context for agent:', memoryError);\n            // Continue without memory context\n          }\n\n          // Prepare the system prompt using promptManager\n          const systemPrompt = promptManager.getSystemPrompt('agentMode', {\n            task,\n            context: contextContent,\n            memoryContext\n          }) || `You are an autonomous AI agent. Task: ${task}\\n\\nContext:\\n${contextContent}\\n\\n${memoryContext}`;\n\n          // Execute the agent task\n          if (this.cancelTokenSource) {\n            const token: AgentContext = { cancellationToken: this.cancelTokenSource.token };\n            await this.executeAgentTask(systemPrompt, agent, progress, token);\n          } else {\n            throw new Error('Cancellation token source is undefined');\n          }\n\n          // Complete the task\n          this.stopAgent('Task completed successfully');\n\n          // Show completion notification\n          vscode.window.showInformationMessage('Agent has completed the task successfully.');\n        } catch (error) {\n          Logger.instance.error('Error in agent execution:', error);\n          this.stopAgent(`Error: ${error instanceof Error ? error.message : String(error)}`);\n\n          // Show error notification\n          vscode.window.showErrorMessage(`Agent encountered an error: ${error instanceof Error ? error.message : String(error)}`);\n        }\n      }\n    );\n  }\n\n  /**\n     * Execute the agent task\n     */\n  private async executeAgentTask(\n    systemPrompt: string,\n    agent: Agent,\n    progress: vscode.Progress<{ message?: string; increment?: number }>,\n    token: AgentContext\n  ): Promise<void> {\n    // Maximum number of steps to prevent infinite loops\n    const MAX_STEPS = 20;\n    let currentStep = 0;\n\n    // Initial thinking\n    progress.report({ message: 'Analyzing task and planning approach...' });\n\n    // Generate initial plan\n    const planResponse = await agent.generate(\n      `${systemPrompt}\\n\\nFirst, create a detailed plan for completing this task. Break it down into specific steps.`,\n      this.getLLMParams(),\n      token\n    );\n\n    // Report plan\n    progress.report({ message: 'Plan created, starting execution...' });\n\n    // Execute steps\n    let currentContext = `${systemPrompt}\\n\\nMy plan:\\n${planResponse}\\n\\nNow I'll execute this plan step by step.`;\n\n    while (currentStep < MAX_STEPS && !token.cancellationToken?.isCancellationRequested) {\n      currentStep++;\n\n      // Report progress\n      progress.report({ message: `Executing step ${currentStep}...`, increment: 100 / MAX_STEPS });\n\n      // Generate next step\n      const stepPrompt = `${currentContext}\\n\\nStep ${currentStep}: `;\n      const stepResponse = await agent.generate(stepPrompt, this.getLLMParams(), token);\n\n      // Add step to context\n      currentContext += `\\n\\nStep ${currentStep}:\\n${stepResponse}`;\n\n      // Check if task is complete\n      if (stepResponse.toLowerCase().includes('task complete') ||\n        stepResponse.toLowerCase().includes('all steps completed')) {\n        break;\n      }\n\n      // Small delay to prevent rate limiting\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    }\n\n    // Final report\n    progress.report({ message: 'Generating final report...' });\n\n    // Generate summary\n    const summaryPrompt = `${currentContext}\\n\\nNow, provide a concise summary of what you've accomplished and any next steps or recommendations:`;\n    const summaryResponse = await agent.generate(summaryPrompt, this.getLLMParams(), token);\n\n    // Add summary to context\n    currentContext += `\\n\\nSummary:\\n${summaryResponse}`;\n\n    // Create a report document\n    const document = await vscode.workspace.openTextDocument({\n      content: currentContext,\n      language: 'markdown'\n    });\n\n    await vscode.window.showTextDocument(document);\n  }\n\n  /**\n     * Stop the agent\n     */\n  private stopAgent(reason: string): void {\n    this.isRunning = false;\n    this.cancelTokenSource?.dispose();\n    this.cancelTokenSource = undefined;\n\n    // Update status bar\n    if (this.statusBarItem) {\n      this.statusBarItem.text = '$(robot) Agent: Idle';\n    }\n\n    // Log reason\n    Logger.instance.info(`Agent stopped: ${reason}`);\n  }\n\n  /**\n     * Get LLM parameters specific to Agent mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.4, // Balanced temperature for creativity and precision\n      maxTokens: 2000,  // Longer responses for detailed reasoning\n      stopSequences: [],\n      mode: 'task'\n    };\n  }\n\n  /**\n     * Get UI components specific to Agent mode\n     */\n  getUIComponents(): {\n    controlPanel?: string;\n    contextPanel?: string;\n    messageInput?: string;\n  } {\n    return {\n      controlPanel: `\n<div class=\"agent-control-panel\">\n    <div class=\"agent-status\">\n        <span id=\"agent-status-indicator\" class=\"status-indicator\"></span>\n        <span id=\"agent-status-text\">Idle</span>\n    </div>\n    <div class=\"agent-controls\">\n        <button id=\"btn-start-agent\" title=\"Start Agent\"><i class=\"codicon codicon-play\"></i> Start</button>\n        <button id=\"btn-stop-agent\" title=\"Stop Agent\" disabled><i class=\"codicon codicon-stop\"></i> Stop</button>\n    </div>\n</div>\n`,\n      contextPanel: `\n<div class=\"context-panel\">\n    <div class=\"context-header\">\n        <h3>Agent Context</h3>\n        <div class=\"context-controls\">\n            <button id=\"btn-refresh-context\" title=\"Refresh Context\"><i class=\"codicon codicon-refresh\"></i></button>\n            <button id=\"btn-select-files\" title=\"Select Files\"><i class=\"codicon codicon-file-code\"></i></button>\n            <button id=\"btn-select-folders\" title=\"Select Folders\"><i class=\"codicon codicon-folder\"></i></button>\n        </div>\n    </div>\n    <div class=\"context-type\">\n        <select id=\"context-type-selector\">\n            <option value=\"entire_codebase\">Entire Codebase</option>\n            <option value=\"selected_files\">Selected Files</option>\n            <option value=\"current_file\">Current File</option>\n            <option value=\"custom\">Custom</option>\n        </select>\n    </div>\n    <div id=\"context-files-list\" class=\"context-files-list\"></div>\n</div>\n`,\n      messageInput: `\n<div class=\"message-input-container\">\n    <textarea id=\"message-input\" placeholder=\"Describe the task for the agent to complete autonomously...\"></textarea>\n    <button id=\"btn-send\" title=\"Send\"><i class=\"codicon codicon-send\"></i></button>\n</div>\n`\n    };\n  }\n\n  /**\n     * Handle mode-specific commands\n     */\n  async handleCommand(command: string, args: any[]): Promise<void> {\n    switch (command) {\n      case 'startAgent':\n        if (!this.isRunning && args.length >= 3) {\n          const [task, agent, contextSource] = args;\n          await this.startAgent(task, agent, contextSource);\n        }\n        break;\n      case 'stopAgent':\n        if (this.isRunning) {\n          this.cancelTokenSource?.cancel();\n          this.stopAgent('User manually stopped the agent');\n        }\n        break;\n    }\n  }\n}\n\n"]}