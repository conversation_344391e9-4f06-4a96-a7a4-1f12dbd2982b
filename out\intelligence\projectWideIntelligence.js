"use strict";
/**
 * Project-Wide Intelligence System - Complete project understanding
 *
 * Provides codebase mapping, dependency intelligence, architecture analysis,
 * performance insights, security analysis, and documentation intelligence
 * while integrating with existing Codessa infrastructure.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectWideIntelligence = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const logger_1 = require("../logger");
const promptManager_1 = require("../prompts/promptManager");
class ProjectWideIntelligence {
    supervisorAgent;
    toolRegistry;
    memoryManager;
    quantumMemory;
    // Analysis cache
    analysisCache = new Map();
    cacheExpiry = 3600000; // 1 hour
    // Real-time monitoring
    fileWatcher = null;
    analysisInProgress = false;
    constructor(supervisorAgent, toolRegistry, memoryManager, quantumMemory) {
        this.supervisorAgent = supervisorAgent;
        this.toolRegistry = toolRegistry;
        this.memoryManager = memoryManager;
        this.quantumMemory = quantumMemory;
        this.setupFileWatcher();
    }
    /**
       * Generate comprehensive project intelligence report
       */
    async generateProjectReport(projectPath) {
        const workspaceRoot = projectPath || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            throw new Error('No workspace folder found');
        }
        // Check cache first
        const cached = this.getCachedReport(workspaceRoot);
        if (cached) {
            logger_1.logger.info('Returning cached project intelligence report');
            return cached;
        }
        if (this.analysisInProgress) {
            throw new Error('Project analysis already in progress');
        }
        this.analysisInProgress = true;
        try {
            logger_1.logger.info('Generating comprehensive project intelligence report...');
            // 1. Build codebase map
            const codebaseMap = await this.buildCodebaseMap(workspaceRoot);
            // 2. Analyze security
            const securityAnalysis = await this.analyzeProjectSecurity(workspaceRoot, codebaseMap);
            // 3. Analyze performance
            const performanceAnalysis = await this.analyzeProjectPerformance(workspaceRoot, codebaseMap);
            // 4. Analyze documentation
            const documentationAnalysis = await this.analyzeProjectDocumentation(workspaceRoot, codebaseMap);
            // 5. Analyze dependencies
            const dependencyAnalysis = await this.analyzeProjectDependencies(workspaceRoot, codebaseMap);
            // 6. Analyze architecture
            const architectureAnalysis = await this.analyzeProjectArchitecture(workspaceRoot, codebaseMap);
            // 7. Generate recommendations
            const recommendations = await this.generateRecommendations({
                codebaseMap,
                securityAnalysis,
                performanceAnalysis,
                documentationAnalysis,
                dependencyAnalysis,
                architectureAnalysis
            });
            const report = {
                codebaseMap,
                securityAnalysis,
                performanceAnalysis,
                documentationAnalysis,
                dependencyAnalysis,
                architectureAnalysis,
                recommendations,
                generatedAt: Date.now()
            };
            // Cache the report
            this.setCachedReport(workspaceRoot, report);
            // Store in quantum memory
            await this.storeReportInMemory(report);
            logger_1.logger.info('Project intelligence report generated successfully');
            return report;
        }
        finally {
            this.analysisInProgress = false;
        }
    }
    /**
       * Build comprehensive codebase map
       */
    async buildCodebaseMap(projectRoot) {
        logger_1.logger.info('Building codebase map...');
        const map = {
            projectRoot,
            totalFiles: 0,
            totalLines: 0,
            languages: {},
            directories: [],
            dependencies: [],
            architecture: [],
            entryPoints: [],
            configFiles: [],
            testFiles: [],
            documentationFiles: []
        };
        // Use existing directory listing tool
        const dirTool = this.toolRegistry.getTool('listDir');
        if (dirTool) {
            const result = await dirTool.execute('listDirectory', {
                path: projectRoot,
                recursive: true,
                includeHidden: false
            });
            if (result.success && result.output) {
                map.directories = await this.processDirectoryStructure(result.output, projectRoot);
                this.calculateCodebaseStats(map);
            }
        }
        // Identify special files
        await this.identifySpecialFiles(map, projectRoot);
        return map;
    }
    /**
       * Analyze project security
       */
    async analyzeProjectSecurity(projectRoot, codebaseMap) {
        logger_1.logger.info('Analyzing project security...');
        const issues = [];
        // Use existing security scanning tool
        const securityTool = this.toolRegistry.getTool('securityScan');
        if (securityTool) {
            for (const dir of codebaseMap.directories) {
                if (dir.type === 'file' && this.isCodeFile(dir.path)) {
                    const result = await securityTool.execute('scanFile', {
                        filePath: dir.path
                    });
                    if (result.success && result.output?.issues) {
                        issues.push(...result.output.issues.map((issue) => ({
                            severity: issue.severity || 'medium',
                            type: issue.type || 'unknown',
                            description: issue.description || 'Security issue detected',
                            file: dir.path,
                            line: issue.line,
                            recommendation: issue.recommendation || 'Review and fix this issue',
                            cveId: issue.cveId
                        })));
                    }
                }
            }
        }
        // Analyze dependencies for vulnerabilities
        for (const dep of codebaseMap.dependencies) {
            issues.push(...dep.securityIssues);
        }
        const criticalIssues = issues.filter(issue => issue.severity === 'critical').length;
        const securityScore = Math.max(0, 100 - (issues.length * 5) - (criticalIssues * 20));
        return {
            totalIssues: issues.length,
            criticalIssues,
            issues,
            securityScore
        };
    }
    /**
       * Analyze project performance
       */
    async analyzeProjectPerformance(projectRoot, codebaseMap) {
        logger_1.logger.info('Analyzing project performance...');
        const insights = [];
        const bottlenecks = [];
        // Use AI to analyze performance patterns
        const codeFiles = codebaseMap.directories.filter(d => d.type === 'file' && this.isCodeFile(d.path));
        for (const file of codeFiles.slice(0, 20)) { // Limit for performance
            try {
                const content = await fs.promises.readFile(file.path, 'utf8');
                const performanceInsight = await this.analyzeFilePerformance(file.path, content);
                insights.push(...performanceInsight);
            }
            catch (error) {
                logger_1.logger.warn(`Failed to analyze performance for ${file.path}: ${error}`);
            }
        }
        // Calculate overall score
        const highSeverityCount = insights.filter(i => i.severity === 'high').length;
        const overallScore = Math.max(0, 100 - (insights.length * 3) - (highSeverityCount * 15));
        return {
            insights,
            overallScore,
            bottlenecks
        };
    }
    /**
       * Analyze project documentation
       */
    async analyzeProjectDocumentation(projectRoot, codebaseMap) {
        logger_1.logger.info('Analyzing project documentation...');
        const gaps = [];
        let documentedFunctions = 0;
        let totalFunctions = 0;
        // Use existing documentation tool
        const docTool = this.toolRegistry.getTool('docs');
        if (docTool) {
            const codeFiles = codebaseMap.directories.filter(d => d.type === 'file' && this.isCodeFile(d.path));
            for (const file of codeFiles.slice(0, 10)) { // Limit for performance
                const result = await docTool.execute('analyzeDocumentation', {
                    filePath: file.path
                });
                if (result.success && result.output) {
                    const analysis = result.output;
                    totalFunctions += analysis.totalFunctions || 0;
                    documentedFunctions += analysis.documentedFunctions || 0;
                    if (analysis.gaps) {
                        gaps.push(...analysis.gaps);
                    }
                }
            }
        }
        const coveragePercentage = totalFunctions > 0 ? (documentedFunctions / totalFunctions) * 100 : 0;
        const qualityScore = Math.max(0, coveragePercentage - (gaps.length * 5));
        return {
            coveragePercentage,
            gaps,
            qualityScore
        };
    }
    /**
       * Analyze project dependencies
       */
    async analyzeProjectDependencies(projectRoot, codebaseMap) {
        logger_1.logger.info('Analyzing project dependencies...');
        // Use existing dependency analysis tool
        const depTool = this.toolRegistry.getTool('dependencyAnalysis');
        let dependencies = [];
        // Analyze codebase structure for dependency patterns
        const fileCount = Object.keys(codebaseMap).length;
        logger_1.logger.debug(`Analyzing dependencies across ${fileCount} files`);
        if (depTool) {
            const result = await depTool.execute('analyzeDependencies', {
                projectPath: projectRoot
            });
            if (result.success && result.output) {
                dependencies = result.output.dependencies || [];
            }
        }
        const outdatedDependencies = dependencies.filter(dep => dep.updateAvailable).length;
        const vulnerableDependencies = dependencies.filter(dep => dep.securityIssues.length > 0).length;
        const healthScore = Math.max(0, 100 - (outdatedDependencies * 2) - (vulnerableDependencies * 10));
        return {
            totalDependencies: dependencies.length,
            outdatedDependencies,
            vulnerableDependencies,
            healthScore
        };
    }
    /**
       * Analyze project architecture
       */
    async analyzeProjectArchitecture(projectRoot, codebaseMap) {
        logger_1.logger.info('Analyzing project architecture...');
        const patterns = [];
        // Use AI to identify architecture patterns
        const prompt = this.buildArchitectureAnalysisPrompt(codebaseMap);
        const result = await this.supervisorAgent.run({
            prompt,
            mode: 'ask'
        });
        if (result.success && result.output) {
            const parsedPatterns = this.parseArchitecturePatterns(result.output);
            patterns.push(...parsedPatterns);
        }
        // Calculate complexity and maintainability
        const complexity = this.calculateArchitectureComplexity(codebaseMap);
        const maintainabilityScore = Math.max(0, 100 - complexity);
        return {
            patterns,
            complexity,
            maintainabilityScore
        };
    }
    // Helper methods
    async processDirectoryStructure(dirOutput, projectRoot) {
        // Process directory structure from tool output
        const nodes = [];
        logger_1.logger.debug(`Processing directory structure for project: ${projectRoot}`);
        if (Array.isArray(dirOutput)) {
            for (const item of dirOutput) {
                const node = {
                    name: path.basename(item.path),
                    path: item.path,
                    type: item.type,
                    size: item.size || 0,
                    importance: this.calculateFileImportance(item.path),
                    complexity: 0,
                    lastModified: item.lastModified || Date.now()
                };
                if (item.type === 'file') {
                    node.language = this.detectLanguage(item.path);
                    node.complexity = await this.calculateFileComplexity(item.path);
                }
                nodes.push(node);
            }
        }
        return nodes;
    }
    calculateCodebaseStats(map) {
        map.totalFiles = map.directories.filter(d => d.type === 'file').length;
        // Count lines and languages
        for (const dir of map.directories) {
            if (dir.type === 'file' && dir.language) {
                map.languages[dir.language] = (map.languages[dir.language] || 0) + 1;
            }
        }
    }
    async identifySpecialFiles(map, projectRoot) {
        const specialPatterns = {
            config: [/package\.json$/, /\.config\.(js|ts|json)$/, /\.env/, /Dockerfile/, /docker-compose/],
            test: [/\.test\.(js|ts|py)$/, /\.spec\.(js|ts|py)$/, /test_.*\.py$/, /__tests__/],
            docs: [/README/, /\.md$/, /docs\//, /documentation\//],
            entry: [/index\.(js|ts|py)$/, /main\.(js|ts|py)$/, /app\.(js|ts|py)$/]
        };
        for (const dir of map.directories) {
            if (dir.type === 'file') {
                const relativePath = path.relative(projectRoot, dir.path);
                if (specialPatterns.config.some(pattern => pattern.test(relativePath))) {
                    map.configFiles.push(dir.path);
                }
                if (specialPatterns.test.some(pattern => pattern.test(relativePath))) {
                    map.testFiles.push(dir.path);
                }
                if (specialPatterns.docs.some(pattern => pattern.test(relativePath))) {
                    map.documentationFiles.push(dir.path);
                }
                if (specialPatterns.entry.some(pattern => pattern.test(relativePath))) {
                    map.entryPoints.push(dir.path);
                }
            }
        }
    }
    isCodeFile(filePath) {
        const codeExtensions = ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.cs', '.go', '.rs', '.php'];
        return codeExtensions.some(ext => filePath.endsWith(ext));
    }
    detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php'
        };
        return languageMap[ext] || 'unknown';
    }
    calculateFileImportance(filePath) {
        // Calculate file importance based on various factors
        let importance = 0.5; // Base importance
        if (filePath.includes('index.') || filePath.includes('main.'))
            importance += 0.3;
        if (filePath.includes('config') || filePath.includes('package.json'))
            importance += 0.2;
        if (filePath.includes('test') || filePath.includes('spec'))
            importance -= 0.1;
        if (filePath.includes('node_modules') || filePath.includes('.git'))
            importance = 0.1;
        return Math.max(0, Math.min(1, importance));
    }
    async calculateFileComplexity(filePath) {
        try {
            const complexityTool = this.toolRegistry.getTool('codeComplexity');
            if (complexityTool) {
                const result = await complexityTool.execute('analyzeFile', { filePath });
                if (result.success && result.output) {
                    return result.output.cyclomaticComplexity || 0;
                }
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to calculate complexity for ${filePath}: ${error}`);
        }
        return 0;
    }
    async analyzeFilePerformance(filePath, content) {
        const insights = [];
        // Simple heuristic analysis (in real implementation, use more sophisticated analysis)
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            // Check for potential performance issues
            if (line.includes('for') && line.includes('for')) {
                insights.push({
                    type: 'bottleneck',
                    severity: 'medium',
                    file: filePath,
                    line: i + 1,
                    description: 'Nested loops detected',
                    impact: 'May cause performance issues with large datasets',
                    suggestion: 'Consider optimizing nested loops or using more efficient algorithms',
                    estimatedImprovement: '20-50% performance improvement'
                });
            }
            if (line.includes('setTimeout') || line.includes('setInterval')) {
                insights.push({
                    type: 'memory',
                    severity: 'low',
                    file: filePath,
                    line: i + 1,
                    description: 'Timer usage detected',
                    impact: 'Potential memory leaks if not properly cleared',
                    suggestion: 'Ensure timers are cleared when no longer needed',
                    estimatedImprovement: 'Prevents memory leaks'
                });
            }
        }
        return insights;
    }
    buildArchitectureAnalysisPrompt(codebaseMap) {
        const fileStructure = codebaseMap.directories
            .filter(d => d.type === 'file' && this.isCodeFile(d.path))
            .slice(0, 20)
            .map(d => path.relative(codebaseMap.projectRoot, d.path))
            .join('\n');
        const languages = Object.entries(codebaseMap.languages)
            .map(([lang, count]) => `${lang}: ${count} files`)
            .join('\n');
        const entryPoints = codebaseMap.entryPoints
            .map(ep => path.relative(codebaseMap.projectRoot, ep))
            .join('\n');
        return promptManager_1.promptManager.renderPrompt('analysis.architecture', {
            fileStructure,
            languages,
            entryPoints
        });
    }
    parseArchitecturePatterns(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed.patterns || [];
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to parse architecture patterns: ${error}`);
        }
        return [];
    }
    calculateArchitectureComplexity(codebaseMap) {
        // Calculate complexity based on various factors
        let complexity = 0;
        complexity += Math.min(50, codebaseMap.totalFiles * 0.1); // File count
        complexity += Object.keys(codebaseMap.languages).length * 5; // Language diversity
        complexity += codebaseMap.dependencies.length * 0.2; // Dependency count
        return Math.min(100, complexity);
    }
    async generateRecommendations(analysis) {
        const recommendations = [];
        // Security recommendations
        if (analysis.securityAnalysis && analysis.securityAnalysis.criticalIssues > 0) {
            recommendations.push(`Address ${analysis.securityAnalysis.criticalIssues} critical security issues immediately`);
        }
        // Performance recommendations
        if (analysis.performanceAnalysis && analysis.performanceAnalysis.overallScore < 70) {
            recommendations.push('Consider performance optimizations to improve overall score');
        }
        // Documentation recommendations
        if (analysis.documentationAnalysis && analysis.documentationAnalysis.coveragePercentage < 50) {
            recommendations.push('Improve documentation coverage - currently below 50%');
        }
        // Dependency recommendations
        if (analysis.dependencyAnalysis && analysis.dependencyAnalysis.outdatedDependencies > 5) {
            recommendations.push(`Update ${analysis.dependencyAnalysis.outdatedDependencies} outdated dependencies`);
        }
        return recommendations;
    }
    setupFileWatcher() {
        if (vscode.workspace.workspaceFolders) {
            this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/*');
            this.fileWatcher.onDidChange(() => {
                this.invalidateCache();
            });
            this.fileWatcher.onDidCreate(() => {
                this.invalidateCache();
            });
            this.fileWatcher.onDidDelete(() => {
                this.invalidateCache();
            });
        }
    }
    getCachedReport(projectPath) {
        const cached = this.analysisCache.get(projectPath);
        if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
            return cached.report;
        }
        if (cached) {
            this.analysisCache.delete(projectPath);
        }
        return null;
    }
    setCachedReport(projectPath, report) {
        this.analysisCache.set(projectPath, { report, timestamp: Date.now() });
    }
    invalidateCache() {
        this.analysisCache.clear();
    }
    async storeReportInMemory(report) {
        try {
            await this.quantumMemory.storeTemporalMemory('Project intelligence report generated', {
                filePath: report.codebaseMap.projectRoot,
                projectContext: path.basename(report.codebaseMap.projectRoot)
            }, 'create', { tags: ['project-intelligence', 'analysis', 'report'] });
        }
        catch (error) {
            logger_1.logger.warn(`Failed to store report in memory: ${error}`);
        }
    }
    /**
       * Dispose project intelligence system
       */
    dispose() {
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
        }
        this.analysisCache.clear();
        logger_1.logger.info('Project-Wide Intelligence disposed');
    }
}
exports.ProjectWideIntelligence = ProjectWideIntelligence;
//# sourceMappingURL=projectWideIntelligence.js.map