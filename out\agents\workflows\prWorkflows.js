"use strict";
/**
 * PR Workflow Templates
 *
 * This module provides specialized workflow templates for PR-related tasks:
 * - PR creation
 * - PR review
 * - PR feedback
 * - PR merge
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPRCreationWorkflow = createPRCreationWorkflow;
exports.createPRReviewWorkflow = createPRReviewWorkflow;
const corePolyfill_1 = require("./corePolyfill");
const logger_1 = require("../../logger");
/**
 * Creates a PR creation workflow
 * This workflow helps with creating a PR by:
 * 1. Analyzing the changes
 * 2. Generating a PR title and description
 * 3. Creating the PR
 */
function createPRCreationWorkflow(options) {
    const name = options.name || 'PR Creation Workflow';
    const description = options.description || 'Workflow for creating pull requests with proper descriptions and titles';
    // Create base workflow using ReAct pattern
    const workflow = {
        id: `pr-creation-workflow-${Date.now()}`,
        name,
        description,
        version: '1.0.0',
        operationMode: 'pr-creation',
        type: 'pr-creation',
        nodes: [
            {
                id: 'input',
                type: 'input',
                name: 'Input',
                label: 'Input'
            },
            {
                id: 'output',
                type: 'output',
                name: 'Output',
                label: 'Output'
            }
        ],
        edges: [
            {
                source: 'input',
                target: 'output',
                type: 'pr-creation',
                name: 'Default Flow'
            }
        ],
        startNodeId: 'input'
    };
    // Add specialized nodes for PR creation
    const analyzeChangesNode = {
        id: 'analyze_changes',
        type: 'tool',
        name: 'Analyze Changes',
        label: 'Analyze Changes',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Analyzing changes for PR creation');
            // Simulated analysis result
            return {
                outputs: {
                    analyze_changes: {
                        filesChanged: 5,
                        linesAdded: 120,
                        linesRemoved: 45,
                        summary: 'Added new feature X and refactored component Y'
                    }
                },
                messages: [
                    new corePolyfill_1.ToolMessage('analyze_changes', {}, JSON.stringify({
                        filesChanged: 5,
                        linesAdded: 120,
                        linesRemoved: 45,
                        summary: 'Added new feature X and refactored component Y'
                    }))
                ]
            };
        }
    };
    const generatePRDescriptionNode = {
        id: 'generate_pr_description',
        type: 'agent',
        name: 'Generate PR Description',
        label: 'Generate PR Description',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Generating PR description');
            // We could use the changes from the previous step
            // const changes = state.outputs?.analyze_changes;
            // Generate PR title and description based on changes
            const prTitle = 'Add feature X and refactor component Y';
            const prDescription = `
## Changes
- Added new feature X
- Refactored component Y for better performance
- Updated tests

## Impact
- Improves user experience
- Reduces load time by 15%

## Testing
- All tests passing
- Manual testing completed
`;
            return {
                outputs: {
                    generate_pr_description: {
                        title: prTitle,
                        description: prDescription
                    }
                },
                messages: [
                    new corePolyfill_1.AIMessage(`I've generated a PR title and description based on your changes:

Title: ${prTitle}

Description:
${prDescription}`)
                ]
            };
        }
    };
    const createPRNode = {
        id: 'create_pr',
        type: 'tool',
        name: 'Create PR',
        label: 'Create PR',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Creating PR');
            // We could use the PR info from the previous step
            // const prInfo = state.outputs?.generate_pr_description;
            // Simulate PR creation
            const prNumber = Math.floor(Math.random() * 1000) + 1;
            const prUrl = `https://github.com/org/repo/pull/${prNumber}`;
            return {
                outputs: {
                    create_pr: {
                        number: prNumber,
                        url: prUrl,
                        status: 'open'
                    }
                },
                messages: [
                    new corePolyfill_1.ToolMessage('create_pr', {}, `PR #${prNumber} created successfully: ${prUrl}`)
                ]
            };
        }
    };
    // Add nodes to workflow
    workflow.nodes.push(analyzeChangesNode);
    workflow.nodes.push(generatePRDescriptionNode);
    workflow.nodes.push(createPRNode);
    // Add edges to connect the nodes
    workflow.edges.push({
        source: 'input',
        target: 'analyze_changes',
        type: 'pr-creation',
        name: 'To Analysis'
    });
    workflow.edges.push({
        source: 'analyze_changes',
        target: 'generate_pr_description',
        type: 'pr-creation',
        name: 'To Description Generation'
    });
    workflow.edges.push({
        source: 'generate_pr_description',
        target: 'create_pr',
        type: 'pr-creation',
        name: 'To PR Creation'
    });
    workflow.edges.push({
        source: 'create_pr',
        target: 'output',
        type: 'pr-creation',
        name: 'To Output'
    });
    return workflow;
}
/**
 * Creates a PR review workflow
 * This workflow helps with reviewing a PR by:
 * 1. Analyzing the PR changes
 * 2. Checking for issues
 * 3. Generating review comments
 */
function createPRReviewWorkflow(options) {
    const name = options.name || 'PR Review Workflow';
    const description = options.description || 'Workflow for reviewing pull requests and providing feedback';
    // Create base workflow using ReAct pattern
    const workflow = {
        id: `pr-review-workflow-${Date.now()}`,
        name,
        description,
        version: '1.0.0',
        operationMode: 'pr-review',
        type: 'pr-review',
        nodes: [
            {
                id: 'input',
                type: 'input',
                name: 'Input',
                label: 'Input'
            },
            {
                id: 'output',
                type: 'output',
                name: 'Output',
                label: 'Output'
            }
        ],
        edges: [
            {
                source: 'input',
                target: 'output',
                type: 'pr-review',
                name: 'Default Flow'
            }
        ],
        startNodeId: 'input'
    };
    // Add specialized nodes for PR review
    const analyzeCodeNode = {
        id: 'analyze_code',
        type: 'tool',
        name: 'Analyze Code Changes',
        label: 'Analyze Code Changes',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Analyzing code changes for PR review');
            // Simulated code analysis result
            return {
                outputs: {
                    analyze_code: {
                        filesChanged: 3,
                        linesAdded: 85,
                        linesRemoved: 32,
                        complexity: 'medium',
                        potentialIssues: [
                            'Possible memory leak in file1.js:45',
                            'Unused variable in file2.js:23',
                            'Missing error handling in file3.js:78'
                        ]
                    }
                },
                messages: [
                    new corePolyfill_1.ToolMessage('analyze_code', {}, JSON.stringify({
                        filesChanged: 3,
                        linesAdded: 85,
                        linesRemoved: 32,
                        complexity: 'medium',
                        potentialIssues: [
                            'Possible memory leak in file1.js:45',
                            'Unused variable in file2.js:23',
                            'Missing error handling in file3.js:78'
                        ]
                    }))
                ]
            };
        }
    };
    const generateReviewNode = {
        id: 'generate_review',
        type: 'agent',
        name: 'Generate Review Comments',
        label: 'Generate Review Comments',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Generating PR review comments');
            // Generate review comments based on code analysis
            const reviewComments = [
                {
                    file: 'file1.js',
                    line: 45,
                    comment: 'This could lead to a memory leak. Consider using a WeakMap instead.'
                },
                {
                    file: 'file2.js',
                    line: 23,
                    comment: 'This variable is declared but never used. Consider removing it.'
                },
                {
                    file: 'file3.js',
                    line: 78,
                    comment: 'Add try/catch block to handle potential errors in this async operation.'
                }
            ];
            return {
                outputs: {
                    generate_review: {
                        comments: reviewComments,
                        summary: 'The PR has some minor issues that should be addressed before merging.'
                    }
                },
                messages: [
                    new corePolyfill_1.AIMessage(`I've reviewed the PR and found the following issues:

1. **file1.js:45** - This could lead to a memory leak. Consider using a WeakMap instead.
2. **file2.js:23** - This variable is declared but never used. Consider removing it.
3. **file3.js:78** - Add try/catch block to handle potential errors in this async operation.

Overall, the PR has some minor issues that should be addressed before merging.`)
                ]
            };
        }
    };
    const submitReviewNode = {
        id: 'submit_review',
        type: 'tool',
        name: 'Submit Review',
        label: 'Submit Review',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Submitting PR review');
            // Simulate review submission
            const reviewId = Math.floor(Math.random() * 1000) + 1;
            return {
                outputs: {
                    submit_review: {
                        id: reviewId,
                        status: 'submitted',
                        result: 'changes_requested'
                    }
                },
                messages: [
                    new corePolyfill_1.ToolMessage('submit_review', {}, `PR review #${reviewId} submitted successfully with status: changes_requested`)
                ]
            };
        }
    };
    // Add nodes to workflow
    workflow.nodes.push(analyzeCodeNode);
    workflow.nodes.push(generateReviewNode);
    workflow.nodes.push(submitReviewNode);
    // Add edges to connect the nodes
    workflow.edges.push({
        source: 'input',
        target: 'analyze_code',
        type: 'pr-review',
        name: 'To Code Analysis'
    });
    workflow.edges.push({
        source: 'analyze_code',
        target: 'generate_review',
        type: 'pr-review',
        name: 'To Review Generation'
    });
    workflow.edges.push({
        source: 'generate_review',
        target: 'submit_review',
        type: 'pr-review',
        name: 'To Review Submission'
    });
    workflow.edges.push({
        source: 'submit_review',
        target: 'output',
        type: 'pr-review',
        name: 'To Output'
    });
    return workflow;
}
// No need to re-export, they're already exported above
//# sourceMappingURL=prWorkflows.js.map