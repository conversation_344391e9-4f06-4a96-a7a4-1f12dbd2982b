{"version": 3, "file": "mcp.js", "sourceRoot": "", "sources": ["../../src/mcp/mcp.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,sCAAmC;AACnC,wDAAqD;AAKrD,MAAM,MAAM,GAAG,eAAM,CAAC,QAAQ,CAAC;AAE/B;;GAEG;AACU,QAAA,WAAW,GAAG,QAAQ,CAAC;AAEpC;;GAEG;AACH,IAAY,cAKX;AALD,WAAY,cAAc;IACtB,qCAAmB,CAAA;IACnB,uCAAqB,CAAA;IACrB,+CAA6B,CAAA;IAC7B,iCAAe,CAAA;AACnB,CAAC,EALW,cAAc,8BAAd,cAAc,QAKzB;AAED;;GAEG;AACH,IAAY,eAQX;AARD,WAAY,eAAe;IACvB,gCAAa,CAAA;IACb,0CAAuB,CAAA;IACvB,8BAAW,CAAA;IACX,oCAAiB,CAAA;IACjB,gCAAa,CAAA;IACb,kCAAe,CAAA;IACf,wCAAqB,CAAA;AACzB,CAAC,EARW,eAAe,+BAAf,eAAe,QAQ1B;AAyND;;GAEG;AACH,MAAa,UAAU;IACb,MAAM,CAAC,QAAQ,CAAa;IAC5B,OAAO,GAA2B,IAAI,GAAG,EAAE,CAAC;IAC5C,eAAe,GAA2E,IAAI,GAAG,EAAE,CAAC;IACpG,qBAAqB,GAA6B,IAAI,GAAG,EAAE,CAAC;IAC5D,YAAY,CAAe;IAE3B,KAAK,GAAY,EAAE,CAAC;IACpB,OAAO,CAAa;IAE5B;QACE,IAAI,CAAC,YAAY,GAAG,2BAAY,CAAC,QAAQ,CAAC;QAC1C,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,6DAA6D;QAC7D,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,yCAAyC;QACzC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAE7C,2CAA2C;QAC3C,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,mBAAW;YACpB,QAAQ,EAAE;gBACR,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;aACjC;YACD,OAAO,EAAE,EAAE;SACZ,CAAC;QAEF,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;IAEO,yBAAyB;QAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,oCAAoC;QACpC,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAS,UAAU,EAAE,MAAM,CAAC,CAAC;QACxD,MAAM,CAAC,IAAI,CAAC,0CAA0C,QAAQ,EAAE,CAAC,CAAC;QAElE,gDAAgD;QAChD,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAS,mBAAmB,EAAE,OAAO,CAAC,CAAC;QAC3E,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,iBAAiB,EAAE,CAAC;gBAC9C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACxC,MAAM,CAAC,IAAI,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,gEAAgE;QAChE,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,mBAAW;YACpB,QAAQ,EAAE;gBACR,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;gBAChC,QAAQ,EAAE,QAAQ;aACnB;YACD,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;SAC9B,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACzB,UAAU,CAAC,QAAQ,GAAG,IAAI,UAAU,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,UAAU,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;SAEK;IACG,oBAAoB;QAC1B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7F,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEpF,wBAAwB;QACxB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5E,qDAAqD;QACrD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACpF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1F,CAAC;IAED;;SAEK;IACE,cAAc,CAAC,MAAiB;QACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACtC,MAAM,CAAC,IAAI,CAAC,0BAA0B,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAmB;QAC7C,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAE1D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,wCAAwC;YACxC,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAChE,IAAI,gBAAgB,EAAE,CAAC;gBACrB,gBAAgB,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3C,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACvD,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC;YAEtC,iDAAiD;YACjD,IAAI,CAAC,aAAa,CAAC;gBACjB,oBAAoB,EAAE;oBACpB,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,IAAI,EAAE,cAAc,CAAC,QAAQ;gBAC7B,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,OAAO,EAAE,mBAAW;aACrB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAErD,OAAO;gBACL,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,IAAI,EAAE,cAAc,CAAC,KAAK;gBAC1B,KAAK,EAAE;oBACL,IAAI,EAAE,CAAC,CAAC;oBACR,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,OAAO,EAAE,mBAAW;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iBAAiB,GAAG,IAAI,GAAG,EAAkC,CAAC;IAE9D,KAAK,CAAC,gBAAgB,CAAC,OAAmB;QAKhD,MAAM,CAAC,KAAK,CAAC,0CAA0C,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACrE,kDAAkD;QAClD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACnE,OAAO;YACL,eAAe,EAAE,mBAAW;YAC5B,YAAY,EAAE;gBACZ,KAAK,EAAE;oBACL,WAAW,EAAE,IAAI;oBACjB,IAAI,EAAE,IAAI;iBACX;gBACD,SAAS,EAAE;oBACT,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,IAAI;iBAClB;gBACD,OAAO,EAAE;oBACP,WAAW,EAAE,IAAI;iBAClB;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE,MAAM;iBACd;gBACD,YAAY,EAAE;oBACZ,MAAM,EAAE,IAAI;oBACZ,SAAS,EAAE,IAAI;oBACf,MAAM,EAAE,IAAI;oBACZ,YAAY,EAAE,IAAI;iBACnB;aACF;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,oBAAoB;gBAC1B,OAAO,EAAE,OAAO;aACjB;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,YAAY,CAAC,MAAwB;QAC3C,OAAO,MAAM,IAAI,MAAM,CAAC;IAC1B,CAAC;IAEO,gBAAgB,CAAC,MAAwB;QAC/C,OAAO,KAAK,IAAI,MAAM,CAAC;IACzB,CAAC;IAEO,aAAa,CAAC,MAAwB;QAC5C,OAAO,SAAS,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC;IACjD,CAAC;IAEO,gBAAgB,CAAC,MAAwB;QAC/C,OAAO,YAAY,IAAI,MAAM,CAAC;IAChC,CAAC;IAEO,cAAc,CAAC,MAAwB;QAC7C,OAAO,OAAO,IAAI,MAAM,CAAC;IAC3B,CAAC;IAEO,YAAY,CAAC,MAAwB;QAC3C,OAAO,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC;IAChD,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAAC,OAAmB;QAO/C,MAAM,CAAC,KAAK,CAAC,gCAAgC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAE9C,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACtB,iDAAiD;gBACjD,MAAM,SAAS,GAAc;oBAC3B,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,EAAE;oBACd,QAAQ,EAAE,EAAE;iBACb,CAAC;gBAEF,yDAAyD;gBACzD,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;oBACnD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAwD,CAAC;oBAChF,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;oBAE/C,sCAAsC;oBACtC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;wBACtD,MAAM,IAAI,GAAG,KAAqF,CAAC;wBAEnG,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC;4BACf,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;gCAC1B,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC;4BAC5B,CAAC;4BAED,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG;gCAC1B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,SAAS;gCACrC,WAAW,EAAE,IAAI,CAAC,WAAW;6BAC9B,CAAC;4BAEF,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gCAC1B,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;oCACxB,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;gCAC1B,CAAC;gCACD,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,OAAO;oBACL,IAAI,EAAE,IAAI,CAAC,EAAE;oBACb,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACnD,WAAW,EAAE,SAAS;iBACvB,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,cAAc,CAAC,OAAmB;QAC9C,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;QAE7E,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;iBAC5E;aACF;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,OAAmB;QACnD,MAAM,CAAC,KAAK,CAAC,oCAAoC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;QAChE,MAAM,SAAS,GAAkB;YAC/B;gBACE,GAAG,EAAE,kBAAkB;gBACvB,IAAI,EAAE,eAAe,CAAC,SAAS;gBAC/B,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,2BAA2B;aACzC;YACD;gBACE,GAAG,EAAE,kBAAkB;gBACvB,IAAI,EAAE,eAAe,CAAC,MAAM;gBAC5B,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,uBAAuB;aACrC;SACF,CAAC;QAEF,OAAO,EAAE,SAAS,EAAE,CAAC;IACvB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,OAAmB;QAGlD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,mDAAmD;QACnD,OAAO;YACL,QAAQ,EAAE;gBACR;oBACE,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG;oBACvB,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,wBAAwB,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE;iBACnD;aACF;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB,CAAC,OAAmB;QACvD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzE,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,yBAAyB,CAAC,OAAmB;QACzD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzE,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,OAAmB;QAWjD,MAAM,CAAC,KAAK,CAAC,kCAAkC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9D,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,eAAe;oBACrB,WAAW,EAAE,0CAA0C;oBACvD,SAAS,EAAE;wBACT;4BACE,IAAI,EAAE,MAAM;4BACZ,WAAW,EAAE,iBAAiB;4BAC9B,QAAQ,EAAE,IAAI;yBACf;qBACF;iBACF;gBACD;oBACE,IAAI,EAAE,cAAc;oBACpB,WAAW,EAAE,mDAAmD;oBAChE,SAAS,EAAE;wBACT;4BACE,IAAI,EAAE,aAAa;4BACnB,WAAW,EAAE,8BAA8B;4BAC3C,QAAQ,EAAE,KAAK;yBAChB;qBACF;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAAC,OAAmB;QAC/C,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,OAAO;YACL,WAAW,EAAE,WAAW,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;YAC7C,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE;wBACP,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,WAAW,OAAO,CAAC,MAAM,CAAC,IAAI,oBAAoB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC,EAAE;qBACzG;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CAAC,OAAmB;QACrD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,yBAAyB,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,OAAmB;QAChD,MAAM,CAAC,KAAK,CAAC,iCAAiC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7D,6CAA6C;QAC7C,OAAO;YACL,MAAM,EAAE;gBACN;oBACE,EAAE,EAAE,YAAY;oBAChB,IAAI,EAAE,kBAAkB;oBACxB,WAAW,EAAE,6BAA6B;oBAC1C,YAAY,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,qBAAqB,CAAC;iBACpE;aACF;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,OAAmB;QACjD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,kDAAkD;QAClD,OAAO;YACL,MAAM,EAAE,SAAS,OAAO,CAAC,MAAM,CAAC,OAAO,mBAAmB,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;YAC/E,MAAM,EAAE,WAAW;SACpB,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,OAAmB;QAGnD,MAAM,CAAC,KAAK,CAAC,oCAAoC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;QAChE,OAAO;YACL,SAAS,EAAE;gBACT;oBACE,EAAE,EAAE,eAAe;oBACnB,IAAI,EAAE,wBAAwB;oBAC9B,WAAW,EAAE,yDAAyD;iBACvE;gBACD;oBACE,EAAE,EAAE,aAAa;oBACjB,IAAI,EAAE,sBAAsB;oBAC5B,WAAW,EAAE,6CAA6C;iBAC3D;aACF;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CAAC,OAAmB;QAKrD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,sDAAsD;QACtD,OAAO;YACL,MAAM,EAAE,YAAY,OAAO,CAAC,MAAM,CAAC,UAAU,wBAAwB;YACrE,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE;YACpC,MAAM,EAAE,WAAW;SACpB,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,OAAmB;QAIlD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QAEzC,4CAA4C;QAC5C,OAAO;YACL,OAAO,EAAE;gBACP;oBACE,EAAE,EAAE,GAAG;oBACP,OAAO,EAAE,4BAA4B,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;oBAC3D,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB;aACF,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;YACjB,KAAK,EAAE,CAAC;SACT,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,OAAmB;QAKjD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,sDAAsD;QACtD,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;YACzB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CAAC,OAAmB;QAQrD,MAAM,CAAC,KAAK,CAAC,uCAAuC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;QACnE,IAAI,CAAC;YACH,yCAAyC;YACzC,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,EAA0C;gBACnD,IAAI,EAAE,SAAS;gBACf,YAAY,EAAE,KAAK;aACpB,CAAC;YAEF,8CAA8C;YAC9C,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YAClD,IAAI,OAAO,EAAE,CAAC;gBACZ,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBAC7C,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE;iBAC3B,CAAC,CAAC,CAAC;gBACJ,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC;gBAClC,aAAa,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,WAAW,CAAC;YAC5D,CAAC;YAED,OAAO;gBACL,SAAS,EAAE,aAAa;gBACxB,YAAY,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,aAAa,CAAC;aACnE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO;gBACL,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE;gBAChE,YAAY,EAAE,EAAE;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,OAAmB;QAgBlD,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,8BAA8B,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7E,MAAM,UAAU,GAAG;gBACjB,YAAY,EAAE,IAIb;gBACD,cAAc,EAAE,EAAE;gBAClB,SAAS,EAAE,IAIV;aACF,CAAC;YAEF,wCAAwC;YACxC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YACpD,IAAI,YAAY,EAAE,CAAC;gBACjB,UAAU,CAAC,YAAY,GAAG;oBACxB,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,QAAQ;oBACxC,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU;oBAC5C,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC,SAAS;iBAC3C,CAAC;gBAEF,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBACpC,UAAU,CAAC,SAAS,GAAG;wBACrB,KAAK,EAAE;4BACL,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI;4BACvC,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS;yBAClD;wBACD,GAAG,EAAE;4BACH,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;4BACrC,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS;yBAChD;wBACD,IAAI,EAAE,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC;qBAC5D,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,UAAU;gBAClB,YAAY,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;aAC1E,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO;gBACL,MAAM,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;gBACnE,YAAY,EAAE,EAAE;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,OAAmB;QAShD,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;YAC7D,CAAC;YAED,QAAQ,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChC,KAAK,cAAc,CAAC,CAAC,CAAC;oBACpB,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;oBACxC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;wBAC5B,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;oBAC9C,CAAC;oBACD,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;wBAChC,QAAQ,EAAE,2BAA2B,GAAG,EAAE;qBAC3C,CAAC;gBACJ,CAAC;gBAED,KAAK,aAAa;oBAChB,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,QAAQ,EAAE,EAAE;wBACZ,KAAK,EAAE,CAAC;qBACT,CAAC;gBAEJ,KAAK,eAAe;oBAClB,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,CAAC;qBACX,CAAC;gBAEJ;oBACE,MAAM,IAAI,KAAK,CAAC,wBAAwB,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB,CAAC,OAAmB;QAQvD,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,WAAW,GAAG;gBAClB,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG;gBACvB,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE;oBACP,MAAM,EAAE,CAAC;oBACT,QAAQ,EAAE,CAAC;oBACX,IAAI,EAAE,CAAC;iBACR;aACF,CAAC;YAEF,OAAO;gBACL,WAAW;gBACX,YAAY,EAAE,CAAC,mBAAmB,EAAE,eAAe,EAAE,iBAAiB,CAAC;aACxE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO;gBACL,WAAW,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3F,YAAY,EAAE,EAAE;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,aAAa,CAClB,IAAoB,EACpB,MAAe,EACf,MAAyB,EACzB,MAAgC,EAChC,KAAgB;QAEhB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;YACzB,IAAI;YACJ,MAAM;YACN,MAAM;YACN,MAAM;YACN,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,mBAAW;SACrB,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO;YACL,OAAO,EAAE,mBAAW;YACpB,QAAQ,EAAE;gBACR,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;aACjC;YACD,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAgC;QACnD,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACjE,0CAA0C;QAC1C,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG;YACrB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;YACvB,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,QAAgB,EAAE,OAAe,EAAE,IAAa;QAC7D,MAAM,CAAC,IAAI,CAAC,sCAAsC,IAAI,IAAI,SAAS,EAAE,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,IAAY,EAAE,OAAe,EAAE,QAAiB;QAC7D,MAAM,CAAC,IAAI,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;QAE1D,kCAAkC;QAClC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;YAC3B,OAAO;YACP,QAAQ,EAAE,QAAQ,IAAI,MAAM;YAC5B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;CACF;AA53BD,gCA43BC", "sourcesContent": ["/**\n * Enhanced Model Context Protocol (MCP) 2025 Implementation\n * \n * This module implements the latest MCP 2025 specification with enhanced\n * capabilities for tool integration, resource management, and protocol extensions.\n */\n\nimport * as vscode from 'vscode';\nimport { Logger } from '../logger';\nimport { ToolRegistry } from '../tools/toolRegistry';\nimport { ITool } from '../tools/tool.ts.backup';\nimport { IMCPManager } from '../managers';\nimport { MCPContext } from './mcpManager';\n\nconst logger = Logger.instance;\n\n/**\n * MCP 2025 Protocol Version\n */\nexport const MCP_VERSION = '2025.1';\n\n/**\n * MCP Message Types\n */\nexport enum MCPMessageType {\n    REQUEST = 'request',\n    RESPONSE = 'response',\n    NOTIFICATION = 'notification',\n    ERROR = 'error'\n}\n\n/**\n * MCP Resource Types\n */\nexport enum MCPResourceType {\n    FILE = 'file',\n    DIRECTORY = 'directory',\n    URL = 'url',\n    MEMORY = 'memory',\n    TOOL = 'tool',\n    AGENT = 'agent',\n    WORKFLOW = 'workflow'\n}\n\n/**\n * Base Message Parameters Interface\n */\nexport interface MCPBaseParams {\n    metadata?: Record<string, unknown>;\n}\n\n/**\n * Resource Message Parameters\n */\nexport interface MCPResourceParams extends MCPBaseParams {\n    uri: string;\n}\n\n/**\n * Tool Message Parameters\n */\nexport interface MCPToolParams extends MCPBaseParams {\n    name: string;\n    arguments?: Record<string, unknown>;\n}\n\n/**\n * Agent Message Parameters\n */\nexport interface MCPAgentParams extends MCPBaseParams {\n    agentId: string;\n    task: string;\n    parameters?: Record<string, unknown>;\n}\n\n/**\n * Workflow Message Parameters\n */\nexport interface MCPWorkflowParams extends MCPBaseParams {\n    workflowId: string;\n    inputs?: Record<string, unknown>;\n}\n\n/**\n * Memory Message Parameters\n */\nexport interface MCPMemoryParams extends MCPBaseParams {\n    query: string;\n    limit?: number;\n}\n\n/**\n * Chat Message Parameters\n */\nexport interface MCPChatParams extends MCPBaseParams {\n    action: string;\n    data: Record<string, unknown>;\n}\n\n/**\n * MCP Message Parameters\n */\nexport type MCPMessageParams = \n    | MCPBaseParams \n    | MCPResourceParams\n    | MCPToolParams\n    | MCPAgentParams \n    | MCPWorkflowParams\n    | MCPMemoryParams\n    | MCPChatParams;\n\nexport interface MCPMessage {\n    id: string;\n    type: MCPMessageType;\n    method?: string;\n    params?: MCPMessageParams;\n    result?: Record<string, unknown>;\n    error?: MCPError;\n    timestamp: number;\n    version: string;\n}\n\n/**\n * MCP Error Data Type\n */\nexport type MCPErrorData = \n    | string \n    | number \n    | boolean \n    | null \n    | { [key: string]: MCPErrorData }\n    | MCPErrorData[];\n\n/**\n * MCP Error Interface\n */\nexport interface MCPError {\n    code: number;\n    message: string;\n    data?: MCPErrorData;\n}\n\n/**\n * MCP Resource Content Types\n */\nexport type MCPResourceContent =\n    | string\n    | Buffer\n    | Uint8Array\n    | Record<string, unknown>\n    | Array<unknown>;\n\n/**\n * MCP Resource Metadata\n */\nexport interface MCPResourceMetadata {\n    creator?: string;\n    createdAt?: number;\n    modifiedAt?: number;\n    tags?: string[];\n    version?: string;\n    encoding?: string;\n    [key: string]: unknown;\n}\n\n/**\n * MCP Resource Interface\n */\nexport interface MCPResource {\n    uri: string;\n    type: MCPResourceType;\n    name: string;\n    description?: string;\n    metadata?: MCPResourceMetadata;\n    content?: MCPResourceContent;\n    mimeType?: string;\n    size?: number;\n    lastModified?: number;\n}\n\n/**\n * MCP Schema Type\n */\nexport interface MCPSchema {\n    type: string;\n    properties?: Record<string, {\n        type: string;\n        description?: string;\n        required?: boolean;\n        items?: MCPSchema;\n        enum?: Array<string | number | boolean>;\n        [key: string]: unknown;\n    }>;\n    required?: string[];\n    additionalProperties?: boolean;\n    [key: string]: unknown;\n}\n\n/**\n * MCP Tool Metadata\n */\nexport interface MCPToolMetadata {\n    version?: string;\n    author?: string;\n    category?: string;\n    tags?: string[];\n    documentation?: string;\n    [key: string]: unknown;\n}\n\n/**\n * MCP Tool Interface\n */\nexport interface MCPTool {\n    name: string;\n    description: string;\n    inputSchema: MCPSchema;\n    outputSchema?: MCPSchema;\n    metadata?: MCPToolMetadata;\n}\n\n/**\n * MCP Server Interface\n */\nexport interface MCPServer {\n    name: string;\n    version: string;\n    capabilities: MCPCapabilities;\n    tools: MCPTool[];\n    resources: MCPResource[];\n}\n\n/**\n * MCP Capabilities Interface\n */\nexport interface MCPCapabilities {\n    tools?: {\n        listChanged?: boolean;\n        call?: boolean;\n    };\n    resources?: {\n        subscribe?: boolean;\n        listChanged?: boolean;\n    };\n    prompts?: {\n        listChanged?: boolean;\n    };\n    logging?: {\n        level?: string;\n    };\n    experimental?: {\n        agents?: boolean;\n        workflows?: boolean;\n        memory?: boolean;\n        goddess_mode?: boolean;\n        [key: string]: boolean | undefined;\n    };\n}\n\n/**\n * Enhanced MCP 2025 Manager\n */\nexport class MCPManager implements IMCPManager {\n  private static instance: MCPManager;\n  private servers: Map<string, MCPServer> = new Map();\n  private messageHandlers: Map<string, (message: MCPMessage) => Promise<Record<string, unknown>>> = new Map();\n  private resourceSubscriptions: Map<string, Set<string>> = new Map();\n  private toolRegistry: ToolRegistry;\n\n  private tools: ITool[] = [];\n  private context: MCPContext;\n\n  private constructor() {\n    this.toolRegistry = ToolRegistry.instance;\n    this.setupMessageHandlers();\n    \n    // Use vscode import to register configuration change handler\n    vscode.workspace.onDidChangeConfiguration(() => {\n      this.handleConfigurationChange();\n    });\n\n    // Initialize tools using ITool interface\n    this.tools = this.toolRegistry.getAllTools();\n\n    // Initialize context using MCPContext type\n    this.context = {\n      version: MCP_VERSION,\n      metadata: {\n        source: 'codessa-mcp2025',\n        timestamp: new Date().toISOString(),\n        sessionId: Date.now().toString()\n      },\n      content: {}\n    };\n    \n    logger.info('MCP 2025 Manager initialized');\n  }\n\n  private handleConfigurationChange(): void {\n    const config = vscode.workspace.getConfiguration('codessa.mcp');\n    // Update MCP-specific configuration\n    const logLevel = config.get<string>('logLevel', 'info');\n    logger.info(`MCP configuration updated - Log Level: ${logLevel}`);\n    \n    // Update active connections timeout and cleanup\n    const connectionTimeout = config.get<number>('connectionTimeout', 3600000);\n    const now = Date.now();\n    for (const [clientId, data] of this.activeConnections.entries()) {\n      if (now - data.lastActive > connectionTimeout) {\n        this.activeConnections.delete(clientId);\n        logger.info(`Cleaned up inactive connection: ${clientId}`);\n      }\n    }\n    \n    // Apply configuration to the MCP context with preserved content\n    this.context = {\n      version: MCP_VERSION,\n      metadata: {\n        source: 'codessa-mcp2025',\n        timestamp: new Date().toISOString(),\n        sessionId: Date.now().toString(),\n        logLevel: logLevel\n      },\n      content: this.context.content\n    };\n  }\n\n  public static getInstance(): MCPManager {\n    if (!MCPManager.instance) {\n      MCPManager.instance = new MCPManager();\n    }\n    return MCPManager.instance;\n  }\n\n  /**\n     * Setup message handlers\n     */\n  private setupMessageHandlers(): void {\n    this.messageHandlers.set('initialize', this.handleInitialize.bind(this));\n    this.messageHandlers.set('tools/list', this.handleToolsList.bind(this));\n    this.messageHandlers.set('tools/call', this.handleToolCall.bind(this));\n    this.messageHandlers.set('resources/list', this.handleResourcesList.bind(this));\n    this.messageHandlers.set('resources/read', this.handleResourceRead.bind(this));\n    this.messageHandlers.set('resources/subscribe', this.handleResourceSubscribe.bind(this));\n    this.messageHandlers.set('resources/unsubscribe', this.handleResourceUnsubscribe.bind(this));\n    this.messageHandlers.set('prompts/list', this.handlePromptsList.bind(this));\n    this.messageHandlers.set('prompts/get', this.handlePromptGet.bind(this));\n    this.messageHandlers.set('logging/setLevel', this.handleLoggingSetLevel.bind(this));\n        \n    // Enhanced 2025 methods\n    this.messageHandlers.set('agents/list', this.handleAgentsList.bind(this));\n    this.messageHandlers.set('agents/invoke', this.handleAgentInvoke.bind(this));\n    this.messageHandlers.set('workflows/list', this.handleWorkflowsList.bind(this));\n    this.messageHandlers.set('workflows/execute', this.handleWorkflowExecute.bind(this));\n    this.messageHandlers.set('memory/search', this.handleMemorySearch.bind(this));\n    this.messageHandlers.set('memory/store', this.handleMemoryStore.bind(this));\n\n    // VS Code specific handlers for enhanced integration\n    this.messageHandlers.set('vscode/workspace', this.handleVSCodeWorkspace.bind(this));\n    this.messageHandlers.set('vscode/editor', this.handleVSCodeEditor.bind(this));\n    this.messageHandlers.set('vscode/chat', this.handleVSCodeChat.bind(this));\n    this.messageHandlers.set('vscode/diagnostics', this.handleVSCodeDiagnostics.bind(this));\n  }\n\n  /**\n     * Register an MCP server\n     */\n  public registerServer(server: MCPServer): void {\n    this.servers.set(server.name, server);\n    logger.info(`MCP server registered: ${server.name} v${server.version}`);\n  }\n\n  /**\n     * Process an MCP message\n     */\n  public async processMessage(message: MCPMessage): Promise<MCPMessage> {\n    try {\n      logger.debug(`Processing MCP message: ${message.method}`);\n\n      if (!message.method) {\n        throw new Error('Message method is required');\n      }\n\n      // Update client's last active timestamp\n      const clientConnection = this.activeConnections.get(message.id);\n      if (clientConnection) {\n        clientConnection.lastActive = Date.now();\n      }\n\n      const handler = this.messageHandlers.get(message.method);\n      if (!handler) {\n        throw new Error(`Unknown method: ${message.method}`);\n      }\n\n      const result = await handler(message);\n\n      // Track successful message processing in context\n      this.updateContext({\n        lastProcessedMessage: {\n          id: message.id,\n          method: message.method,\n          timestamp: Date.now()\n        }\n      });\n\n      return {\n        id: message.id,\n        type: MCPMessageType.RESPONSE,\n        result: result,\n        timestamp: Date.now(),\n        version: MCP_VERSION\n      };\n\n    } catch (error) {\n      logger.error('Error processing MCP message:', error);\n            \n      return {\n        id: message.id,\n        type: MCPMessageType.ERROR,\n        error: {\n          code: -1,\n          message: error instanceof Error ? error.message : String(error)\n        },\n        timestamp: Date.now(),\n        version: MCP_VERSION\n      };\n    }\n  }\n\n  /**\n     * Handle initialize request\n     */\n  private activeConnections = new Map<string, { lastActive: number }>();\n\n  private async handleInitialize(message: MCPMessage): Promise<{\n    protocolVersion: string;\n    capabilities: MCPCapabilities;\n    serverInfo: { name: string; version: string; };\n  }> {\n    logger.debug(`Initializing MCP connection for client ${message.id}`);\n    // Track active connections for session management\n    this.activeConnections.set(message.id, { lastActive: Date.now() });\n    return {\n      protocolVersion: MCP_VERSION,\n      capabilities: {\n        tools: {\n          listChanged: true,\n          call: true\n        },\n        resources: {\n          subscribe: true,\n          listChanged: true\n        },\n        prompts: {\n          listChanged: true\n        },\n        logging: {\n          level: 'info'\n        },\n        experimental: {\n          agents: true,\n          workflows: true,\n          memory: true,\n          goddess_mode: true\n        }\n      },\n      serverInfo: {\n        name: 'Codessa MCP Server',\n        version: '1.0.0'\n      }\n    };\n  }\n\n  /**\n     * Type guards for parameter types\n     */\n  private isToolParams(params: MCPMessageParams): params is MCPToolParams {\n    return 'name' in params;\n  }\n\n  private isResourceParams(params: MCPMessageParams): params is MCPResourceParams {\n    return 'uri' in params;\n  }\n\n  private isAgentParams(params: MCPMessageParams): params is MCPAgentParams {\n    return 'agentId' in params && 'task' in params;\n  }\n\n  private isWorkflowParams(params: MCPMessageParams): params is MCPWorkflowParams {\n    return 'workflowId' in params;\n  }\n\n  private isMemoryParams(params: MCPMessageParams): params is MCPMemoryParams {\n    return 'query' in params;\n  }\n\n  private isChatParams(params: MCPMessageParams): params is MCPChatParams {\n    return 'action' in params && 'data' in params;\n  }\n\n  /**\n     * Handle tools list request\n     */\n  private async handleToolsList(message: MCPMessage): Promise<{\n    tools: Array<{\n      name: string;\n      description: string;\n      inputSchema: MCPSchema;\n    }>;\n  }> {\n    logger.debug(`Handling tools list request [${message.id}]`);\n    const tools = this.toolRegistry.getAllTools();\n        \n    return {\n      tools: tools.map(tool => {\n        // Create basic schema that matches the interface\n        const mcpSchema: MCPSchema = {\n          type: 'object',\n          properties: {},\n          required: []\n        };\n\n        // If the tool has a schema, try to extract its structure\n        if (tool.schema && typeof tool.schema === 'object') {\n          const rawSchema = tool.schema as { _def?: { shape?: Record<string, unknown> } };\n          const properties = rawSchema._def?.shape || {};\n          \n          // Process each property in the schema\n          for (const [key, value] of Object.entries(properties)) {\n            const prop = value as { _def?: { typeName?: string; isOptional?: boolean }; description?: string };\n            \n            if (prop?._def) {\n              if (!mcpSchema.properties) {\n                mcpSchema.properties = {};\n              }\n              \n              mcpSchema.properties[key] = {\n                type: prop._def.typeName || 'unknown',\n                description: prop.description\n              };\n              \n              if (!prop._def.isOptional) {\n                if (!mcpSchema.required) {\n                  mcpSchema.required = [];\n                }\n                mcpSchema.required.push(key);\n              }\n            }\n          }\n        }\n\n        return {\n          name: tool.id,\n          description: tool.description || `Tool: ${tool.id}`,\n          inputSchema: mcpSchema\n        };\n      })\n    };\n  }\n\n  /**\n     * Handle tool call request\n     */\n  private async handleToolCall(message: MCPMessage): Promise<{ content: Array<{ type: string; text: string; }> }> {\n    if (!message.params || !this.isToolParams(message.params)) {\n      throw new Error('Tool call requires name parameter');\n    }\n        \n    const tool = this.toolRegistry.getTool(message.params.name);\n    if (!tool) {\n      throw new Error(`Tool not found: ${message.params.name}`);\n    }\n\n    const result = await tool.execute(undefined, message.params.arguments || {});\n        \n    return {\n      content: [\n        {\n          type: 'text',\n          text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)\n        }\n      ]\n    };\n  }\n\n  /**\n     * Handle resources list request\n     */\n  private async handleResourcesList(message: MCPMessage): Promise<{ resources: MCPResource[] }> {\n    logger.debug(`Handling resources list request [${message.id}]`);\n    const resources: MCPResource[] = [\n      {\n        uri: 'file://workspace',\n        type: MCPResourceType.DIRECTORY,\n        name: 'Workspace',\n        description: 'Current VS Code workspace'\n      },\n      {\n        uri: 'memory://codessa',\n        type: MCPResourceType.MEMORY,\n        name: 'Codessa Memory',\n        description: 'Codessa memory system'\n      }\n    ];\n\n    return { resources };\n  }\n\n  /**\n     * Handle resource read request\n     */\n  private async handleResourceRead(message: MCPMessage): Promise<{\n    contents: Array<{ uri: string; mimeType: string; text: string }>\n  }> {\n    if (!message.params || !this.isResourceParams(message.params)) {\n      throw new Error('URI parameter is required');\n    }\n        \n    // Implementation would depend on the resource type\n    return {\n      contents: [\n        {\n          uri: message.params.uri,\n          mimeType: 'text/plain',\n          text: `Content of resource: ${message.params.uri}`\n        }\n      ]\n    };\n  }\n\n  /**\n     * Handle resource subscribe request\n     */\n  private async handleResourceSubscribe(message: MCPMessage): Promise<{ success: boolean }> {\n    if (!message.params || !this.isResourceParams(message.params)) {\n      throw new Error('URI parameter is required');\n    }\n        \n    if (!this.resourceSubscriptions.has(message.params.uri)) {\n      this.resourceSubscriptions.set(message.params.uri, new Set());\n    }\n        \n    const subscriptions = this.resourceSubscriptions.get(message.params.uri);\n    if (subscriptions) {\n      subscriptions.add(message.id);\n    }\n        \n    return { success: true };\n  }\n\n  /**\n     * Handle resource unsubscribe request\n     */\n  private async handleResourceUnsubscribe(message: MCPMessage): Promise<{ success: boolean }> {\n    if (!message.params || !this.isResourceParams(message.params)) {\n      throw new Error('URI parameter is required');\n    }\n        \n    const subscriptions = this.resourceSubscriptions.get(message.params.uri);\n    if (subscriptions) {\n      subscriptions.delete(message.id);\n    }\n        \n    return { success: true };\n  }\n\n  /**\n     * Handle prompts list request\n     */\n  private async handlePromptsList(message: MCPMessage): Promise<{\n    prompts: Array<{\n      name: string;\n      description: string;\n      arguments: Array<{\n        name: string;\n        description: string;\n        required: boolean;\n      }>;\n    }>;\n  }> {\n    logger.debug(`Handling prompts list request [${message.id}]`);\n    return {\n      prompts: [\n        {\n          name: 'code_analysis',\n          description: 'Analyze code for issues and improvements',\n          arguments: [\n            {\n              name: 'code',\n              description: 'Code to analyze',\n              required: true\n            }\n          ]\n        },\n        {\n          name: 'goddess_mode',\n          description: 'Activate Goddess Mode for enhanced AI interaction',\n          arguments: [\n            {\n              name: 'personality',\n              description: 'Personality type to activate',\n              required: false\n            }\n          ]\n        }\n      ]\n    };\n  }\n\n  /**\n     * Handle prompt get request\n     */\n  private async handlePromptGet(message: MCPMessage): Promise<{ description: string; messages: Array<{ role: string; content: { type: string; text: string } }> }> {\n    if (!message.params || !this.isToolParams(message.params)) {\n      throw new Error('Name parameter is required');\n    }\n\n    return {\n      description: `Prompt: ${message.params.name}`,\n      messages: [\n        {\n          role: 'user',\n          content: {\n            type: 'text',\n            text: `Execute ${message.params.name} with arguments: ${JSON.stringify(message.params.arguments || {})}`\n          }\n        }\n      ]\n    };\n  }\n\n  /**\n     * Handle logging set level request\n     */\n  private async handleLoggingSetLevel(message: MCPMessage): Promise<{ success: boolean }> {\n    if (!message.params || !('level' in message.params)) {\n      throw new Error('Level parameter is required');\n    }\n    logger.info(`Setting log level to: ${message.params.level}`);\n    return { success: true };\n  }\n\n  /**\n     * Handle agents list request (Enhanced 2025)\n     */\n  private async handleAgentsList(message: MCPMessage): Promise<{ agents: Array<{ id: string; name: string; description: string; capabilities: string[] }> }> {\n    logger.debug(`Handling agents list request [${message.id}]`);\n    // Implementation would list available agents\n    return {\n      agents: [\n        {\n          id: 'supervisor',\n          name: 'Supervisor Agent',\n          description: 'Coordinates multiple agents',\n          capabilities: ['delegation', 'coordination', 'workflow_management']\n        }\n      ]\n    };\n  }\n\n  /**\n     * Handle agent invoke request (Enhanced 2025)\n     */\n  private async handleAgentInvoke(message: MCPMessage): Promise<{ result: string; status: string }> {\n    if (!message.params || !this.isAgentParams(message.params)) {\n      throw new Error('AgentId and task parameters are required');\n    }\n        \n    // Implementation would invoke the specified agent\n    return {\n      result: `Agent ${message.params.agentId} executed task: ${message.params.task}`,\n      status: 'completed'\n    };\n  }\n\n  /**\n     * Handle workflows list request (Enhanced 2025)\n     */\n  private async handleWorkflowsList(message: MCPMessage): Promise<{ \n    workflows: Array<{ id: string; name: string; description: string }> \n  }> {\n    logger.debug(`Handling workflows list request [${message.id}]`);\n    return {\n      workflows: [\n        {\n          id: 'code_analysis',\n          name: 'Code Analysis Workflow',\n          description: 'Comprehensive code analysis and improvement suggestions'\n        },\n        {\n          id: 'refactoring',\n          name: 'Refactoring Workflow',\n          description: 'Automated code refactoring and optimization'\n        }\n      ]\n    };\n  }\n\n  /**\n     * Handle workflow execute request (Enhanced 2025)\n     */\n  private async handleWorkflowExecute(message: MCPMessage): Promise<{\n    result: string;\n    outputs: Record<string, unknown>;\n    status: string;\n  }> {\n    if (!message.params || !this.isWorkflowParams(message.params)) {\n      throw new Error('WorkflowId parameter is required');\n    }\n        \n    // Implementation would execute the specified workflow\n    return {\n      result: `Workflow ${message.params.workflowId} executed successfully`,\n      outputs: message.params.inputs || {},\n      status: 'completed'\n    };\n  }\n\n  /**\n     * Handle memory search request (Enhanced 2025)\n     */\n  private async handleMemorySearch(message: MCPMessage): Promise<{\n    results: Array<{ id: string; content: string; relevance: number; timestamp: number }>;\n    total: number;\n  }> {\n    if (!message.params || !this.isMemoryParams(message.params)) {\n      throw new Error('Query parameter is required');\n    }\n\n    const limit = message.params.limit ?? 10;\n        \n    // Implementation would search memory system\n    return {\n      results: [\n        {\n          id: '1',\n          content: `Memory result for query: ${message.params.query}`,\n          relevance: 0.95,\n          timestamp: Date.now()\n        }\n      ].slice(0, limit),\n      total: 1\n    };\n  }\n\n  /**\n     * Handle memory store request (Enhanced 2025)\n     */\n  private async handleMemoryStore(message: MCPMessage): Promise<{\n    id: string;\n    stored: boolean;\n    timestamp: number;\n  }> {\n    if (!message.params || !('content' in message.params)) {\n      throw new Error('Content parameter is required');\n    }\n\n    // Implementation would store content in memory system\n    return {\n      id: Date.now().toString(),\n      stored: true,\n      timestamp: Date.now()\n    };\n  }\n\n  /**\n     * Handle VS Code workspace request\n     */\n  private async handleVSCodeWorkspace(message: MCPMessage): Promise<{\n    workspace: {\n      folders: Array<{ name: string; uri: string }>;\n      name: string;\n      hasWorkspace: boolean;\n    };\n    capabilities: string[];\n  }> {\n    logger.debug(`Handling VS Code workspace request [${message.id}]`);\n    try {\n      // Get workspace information if available\n      const workspaceInfo = {\n        folders: [] as Array<{ name: string; uri: string }>,\n        name: 'Unknown',\n        hasWorkspace: false\n      };\n\n      // Get workspace folders from real VS Code API\n      const folders = vscode.workspace.workspaceFolders;\n      if (folders) {\n        workspaceInfo.folders = folders.map(folder => ({\n          name: folder.name,\n          uri: folder.uri.toString()\n        }));\n        workspaceInfo.hasWorkspace = true;\n        workspaceInfo.name = vscode.workspace.name || 'Workspace';\n      }\n\n      return {\n        workspace: workspaceInfo,\n        capabilities: ['file_access', 'editor_integration', 'diagnostics']\n      };\n    } catch (error) {\n      logger.error('Error handling VS Code workspace request:', error);\n      return {\n        workspace: { folders: [], name: 'Unknown', hasWorkspace: false },\n        capabilities: []\n      };\n    }\n  }\n\n  /**\n     * Handle VS Code editor request\n     */\n  private async handleVSCodeEditor(message: MCPMessage): Promise<{\n    editor: {\n      activeEditor: null | {\n        fileName: string;\n        languageId: string;\n        lineCount: number;\n      };\n      visibleEditors: unknown[];\n      selection: null | {\n        start: { line: number; character: number };\n        end: { line: number; character: number };\n        text: string;\n      };\n    };\n    capabilities: string[];\n  }> {\n    try {\n      logger.debug(`Processing editor request: ${message.id} (${message.method})`);\n      const editorInfo = {\n        activeEditor: null as null | {\n          fileName: string;\n          languageId: string;\n          lineCount: number;\n        },\n        visibleEditors: [],\n        selection: null as null | {\n          start: { line: number; character: number };\n          end: { line: number; character: number };\n          text: string;\n        }\n      };\n\n      // Get editor info from real VS Code API\n      const activeEditor = vscode.window.activeTextEditor;\n      if (activeEditor) {\n        editorInfo.activeEditor = {\n          fileName: activeEditor.document.fileName,\n          languageId: activeEditor.document.languageId,\n          lineCount: activeEditor.document.lineCount\n        };\n\n        if (!activeEditor.selection.isEmpty) {\n          editorInfo.selection = {\n            start: { \n              line: activeEditor.selection.start.line, \n              character: activeEditor.selection.start.character \n            },\n            end: { \n              line: activeEditor.selection.end.line, \n              character: activeEditor.selection.end.character \n            },\n            text: activeEditor.document.getText(activeEditor.selection)\n          };\n        }\n      }\n\n      return {\n        editor: editorInfo,\n        capabilities: ['text_manipulation', 'selection_access', 'cursor_control']\n      };\n    } catch (error) {\n      logger.error('Error handling VS Code editor request:', error);\n      return {\n        editor: { activeEditor: null, visibleEditors: [], selection: null },\n        capabilities: []\n      };\n    }\n  }\n\n  /**\n     * Handle VS Code chat request\n     */\n  private async handleVSCodeChat(message: MCPMessage): Promise<{\n    success: boolean;\n    messageId?: string;\n    response?: string;\n    messages?: unknown[];\n    total?: number;\n    cleared?: number;\n    error?: string;\n  }> {\n    try {\n      if (!message.params || !this.isChatParams(message.params)) {\n        throw new Error('Action and data parameters are required');\n      }\n\n      switch (message.params.action) {\n      case 'send_message': {\n        const msg = message.params.data.message;\n        if (typeof msg !== 'string') {\n          throw new Error('Message must be a string');\n        }\n        return {\n          success: true,\n          messageId: Date.now().toString(),\n          response: `Processed chat message: ${msg}`\n        };\n      }\n\n      case 'get_history':\n        return {\n          success: true,\n          messages: [],\n          total: 0\n        };\n\n      case 'clear_history':\n        return {\n          success: true,\n          cleared: 0\n        };\n\n      default:\n        throw new Error(`Unknown chat action: ${message.params.action}`);\n      }\n    } catch (error) {\n      logger.error('Error handling VS Code chat request:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : String(error)\n      };\n    }\n  }\n\n  /**\n     * Handle VS Code diagnostics request\n     */\n  private async handleVSCodeDiagnostics(message: MCPMessage): Promise<{\n    diagnostics: {\n      uri: string;\n      problems: unknown[];\n      summary: { errors: number; warnings: number; info: number };\n    };\n    capabilities: string[];\n  }> {\n    try {\n      if (!message.params || !this.isResourceParams(message.params)) {\n        throw new Error('URI parameter is required');\n      }\n\n      const diagnostics = {\n        uri: message.params.uri,\n        problems: [],\n        summary: {\n          errors: 0,\n          warnings: 0,\n          info: 0\n        }\n      };\n\n      return {\n        diagnostics,\n        capabilities: ['problem_detection', 'code_analysis', 'fix_suggestions']\n      };\n    } catch (error) {\n      logger.error('Error handling VS Code diagnostics request:', error);\n      return {\n        diagnostics: { uri: 'unknown', problems: [], summary: { errors: 0, warnings: 0, info: 0 } },\n        capabilities: []\n      };\n    }\n  }\n\n  /**\n     * Create an MCP message\n     */\n  public createMessage(\n    type: MCPMessageType,\n    method?: string,\n    params?: MCPMessageParams,\n    result?: Record<string, unknown>,\n    error?: MCPError\n  ): MCPMessage {\n    return {\n      id: Date.now().toString(),\n      type,\n      method,\n      params,\n      result,\n      error,\n      timestamp: Date.now(),\n      version: MCP_VERSION\n    };\n  }\n\n  /**\n     * Dispose of the MCP manager\n     */\n  public dispose(): void {\n    this.servers.clear();\n    this.messageHandlers.clear();\n    this.resourceSubscriptions.clear();\n    logger.info('MCP 2025 Manager disposed');\n  }\n  \n  /**\n   * Get the current context (IMCPManager interface implementation)\n   */\n  public getCurrentContext(): MCPContext {\n    return {\n      version: MCP_VERSION,\n      metadata: {\n        source: 'codessa-mcp2025',\n        timestamp: new Date().toISOString(),\n        sessionId: Date.now().toString()\n      },\n      content: {}\n    };\n  }\n  \n  /**\n   * Update the context with new content (IMCPManager interface implementation)\n   */\n  public updateContext(content: Record<string, unknown>): void {\n    logger.info('MCP2025Manager: updateContext called with content');\n    // Update the context with the new content\n    this.context.content = {\n      ...this.context.content,\n      ...content\n    };\n  }\n  \n  /**\n   * Add code to the context (IMCPManager interface implementation)\n   */\n  public addCode(language: string, content: string, path?: string): void {\n    logger.info(`MCP2025Manager: addCode called for ${path || 'unnamed'}`);\n  }\n  \n  /**\n   * Add a file to the context (IMCPManager interface implementation)\n   */\n  public addFile(path: string, content: string, language?: string): void {\n    logger.info(`MCP2025Manager: addFile called for ${path}`);\n    \n    // Add file content to the context\n    this.context.content[path] = {\n      content,\n      language: language || 'text',\n      timestamp: new Date().toISOString()\n    };\n  }\n}\n"]}