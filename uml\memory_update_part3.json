{"_type": "UMLClass", "_id": "AAAAAAGH1IVectorStore=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "IVectorStore", "stereotype": "interface", "visibility": "public", "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1IVectorStoreOp1=", "_parent": {"$ref": "AAAAAAGH1IVectorStore="}, "name": "initialize", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp1P1=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp1="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IVectorStoreOp2=", "_parent": {"$ref": "AAAAAAGH1IVectorStore="}, "name": "addVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp2P1=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp2="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp2P2=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp2="}, "name": "vector", "type": "number[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp2P3=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp2="}, "name": "metadata", "type": "Record<string, any>", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp2P4=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp2="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IVectorStoreOp3=", "_parent": {"$ref": "AAAAAAGH1IVectorStore="}, "name": "getVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp3P1=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp3="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp3P2=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp3="}, "type": "Promise<number[] | undefined>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IVectorStoreOp4=", "_parent": {"$ref": "AAAAAAGH1IVectorStore="}, "name": "deleteVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp4P1=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp4="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp4P2=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp4="}, "type": "Promise<boolean>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IVectorStoreOp5=", "_parent": {"$ref": "AAAAAAGH1IVectorStore="}, "name": "clearVectors", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp5P1=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp5="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IVectorStoreOp6=", "_parent": {"$ref": "AAAAAAGH1IVectorStore="}, "name": "searchSimilarVectors", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp6P1=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp6="}, "name": "vector", "type": "number[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp6P2=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp6="}, "name": "limit", "type": "number", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp6P3=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp6="}, "name": "filter", "type": "Record<string, any>", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IVectorStoreOp6P4=", "_parent": {"$ref": "AAAAAAGH1IVectorStoreOp6="}, "type": "Promise<Array<{id: string, score: number}>>", "direction": "return"}]}], "isAbstract": true}