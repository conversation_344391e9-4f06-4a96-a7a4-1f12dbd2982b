{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../src/utils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,kCAyCC;AAKD,4BAOC;AAKD,8CAmBC;AAKD,gDAEC;AAKD,gCAIC;AAKD,4CAQC;AAKD,sCAKC;AAKD,8CAuBC;AAKD,oCAeC;AAKD,4BAgBC;AAKD,8BAsCC;AAED,oCAMC;AAED,sCAEC;AAED,oCAEC;AAGD,sBAEC;AAOD,oDAmBC;AAxSD,+CAAiC;AACjC,qCAAkC;AAElC;;GAEG;AACH,MAAa,aAAc,SAAQ,KAAK;IAGhB;IACA;IAHtB,YACE,OAAe,EACK,IAAY,EACZ,OAAa;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAC;QAHK,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAM;QAGjC,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;IACnE,CAAC;CACF;AAVD,sCAUC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,KAAc,EAAE,OAAe;IACzD,6CAA6C;IAC7C,MAAM,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAE9D,IAAI,KAAK,YAAY,aAAa,EAAE,CAAC;QACnC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAErF,iDAAiD;QACjD,MAAM,YAAY,GAAG,GAAG,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC;QACxD,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,kEAAkE;YAClE,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAC3D,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,YAAY,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;QAClC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QAE7D,iDAAiD;QACjD,MAAM,YAAY,GAAG,GAAG,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;QACpD,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,kEAAkE;YAClE,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAC3D,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,YAAY,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;SAAM,CAAC;QACN,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAE1D,iDAAiD;QACjD,MAAM,YAAY,GAAG,GAAG,OAAO,gCAAgC,CAAC;QAChE,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,kEAAkE;YAClE,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAC3D,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,YAAY,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,QAAQ;IACtB,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,MAAM,QAAQ,GAAG,gEAAgE,CAAC;IAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IACvE,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,iBAAiB,CACrC,SAA2B,EAC3B,OAAe,EACf,UAGM,EAAE;IAER,IAAI,CAAC;QACH,OAAO,MAAM,SAAS,EAAE,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAChC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC9B,CAAC;QACD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,KAAK,CAAC;QACd,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,OAAe;IAChD,OAAO,CAAC,KAAc,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACzD,CAAC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,MAAe;IACxC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3D,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,MAAM,EAAE,CAAC;AAChF,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,MAA2B,EAAE,QAAkB;IAC9E,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;IACtE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,MAAM,IAAI,aAAa,CACrB,gCAAgC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EACpD,gBAAgB,CACjB,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,aAAa,CAAC,KAAc;IAC1C,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;QAC3B,OAAO,KAAK,CAAC,OAAO,CAAC;IACvB,CAAC;IACD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,OAAgB,EAChB,UAAoB,EAAE;IAEtB,MAAM,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;IAC3B,IAAI,OAAO,EAAE,CAAC;QACZ,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC7B,CAAC;IAED,kEAAkE;IAClE,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAC3D,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;IAE5D,IAAI,SAAS,KAAK,cAAc,IAAI,OAAO,EAAE,CAAC;QAC5C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACzD,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;QACH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,YAAY,CAChC,KAAa,EACb,IAAyF;IAEzF,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAC/B;QACE,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;QAC9C,KAAK;QACL,WAAW,EAAE,KAAK;KACnB,EACD,CAAC,QAAQ,EAAE,EAAE;QACX,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxB,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,QAAQ,CACtB,IAAO,EACP,IAAY;IAEZ,IAAI,OAAuB,CAAC;IAE5B,OAAO,SAAS,gBAAgB,CAAC,GAAG,IAAmB;QACrD,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACjE,MAAM,KAAK,GAAG,GAAG,EAAE;YACjB,YAAY,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,CAAC;QACtB,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,SAAS,CAC7B,SAA2B,EAC3B,UAKM,EAAE;IAER,MAAM,EACJ,WAAW,GAAG,CAAC,EACf,YAAY,GAAG,IAAI,EACnB,QAAQ,GAAG,KAAK,EAChB,aAAa,GAAG,CAAC,EAClB,GAAG,OAAO,CAAC;IAEZ,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,KAAK,GAAG,YAAY,CAAC;IAEzB,IAAI,SAAkB,CAAC;IACvB,OAAO,OAAO,IAAI,WAAW,EAAE,CAAC;QAC9B,IAAI,CAAC;YACH,OAAO,MAAM,SAAS,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,GAAG,KAAK,CAAC;YAClB,IAAI,OAAO,IAAI,WAAW,EAAE,CAAC;gBAC3B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,OAAO,wBAAwB,KAAK,OAAO,EAAE,KAAK,CAAC,CAAC;YACpF,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAEzD,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,aAAa,EAAE,QAAQ,CAAC,CAAC;YAClD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IACD,+EAA+E;IAC/E,MAAM,SAAS,CAAC;AAClB,CAAC;AAED,SAAgB,YAAY;IAC1B,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAS,CAAC;QACvE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QAC1C,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;AACL,CAAC;AAEM,KAAK,UAAU,aAAa,CAAiC,KAAU,EAAE,WAAmB;IACjG,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;AACnE,CAAC;AAEM,KAAK,UAAU,YAAY,CAAC,MAAc,EAAE,WAAoB,EAAE,KAAc;IACrF,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;AAC1E,CAAC;AAED,wBAAwB;AACxB,SAAgB,KAAK,CAAC,EAAU;IAC9B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD,CAAC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB,CAAC,QAAgB;IACnD,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC/D,2DAA2D;QAC3D,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACvC,IAAI,GAAG,CAAC,MAAM;gBAAE,OAAO,GAAG,CAAC,CAAC,mBAAmB;QACjD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,CAAC,CAAC,CAAC;QAC/D,CAAC;QACD,wCAAwC;QACxC,OAAO,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;SAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;QAC7C,+CAA+C;QAC/C,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IACD,sCAAsC;IACtC,wGAAwG;IACxG,OAAO,SAAS,CAAC;AACnB,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from './logger';\n\n/**\n * Custom error class for Codessa-specific errors\n */\nexport class CodesssaError extends Error {\n  constructor(\n    message: string,\n        public readonly code: string,\n        public readonly details?: any\n  ) {\n    super(message);\n    this.name = 'CodesssaError';\n    console.log(`CodesssaError created with code: ${code}`, details);\n  }\n}\n\n/**\n * Handle errors consistently throughout the extension\n */\nexport function handleError(error: unknown, context: string): void {\n  // Import here to avoid circular dependencies\n  const { shouldSuppressError } = require('./ui/notifications');\n\n  if (error instanceof CodesssaError) {\n    Logger.instance.error(`${context}: [${error.code}] ${error.message}`, error.details);\n\n    // Check if we should suppress this error message\n    const errorMessage = `${error.message} (${error.code})`;\n    if (!shouldSuppressError(errorMessage)) {\n      // Use our showErrorMessage wrapper instead of direct VS Code call\n      const { showErrorMessage } = require('./ui/notifications');\n      showErrorMessage(errorMessage);\n    } else {\n      Logger.instance.debug(`Suppressed error notification: ${errorMessage}`);\n    }\n  } else if (error instanceof Error) {\n    Logger.instance.error(`${context}: ${error.message}`, error);\n\n    // Check if we should suppress this error message\n    const errorMessage = `${context}: ${error.message}`;\n    if (!shouldSuppressError(errorMessage)) {\n      // Use our showErrorMessage wrapper instead of direct VS Code call\n      const { showErrorMessage } = require('./ui/notifications');\n      showErrorMessage(errorMessage);\n    } else {\n      Logger.instance.debug(`Suppressed error notification: ${errorMessage}`);\n    }\n  } else {\n    Logger.instance.error(`${context}: Unknown error`, error);\n\n    // Check if we should suppress this error message\n    const errorMessage = `${context}: An unexpected error occurred`;\n    if (!shouldSuppressError(errorMessage)) {\n      // Use our showErrorMessage wrapper instead of direct VS Code call\n      const { showErrorMessage } = require('./ui/notifications');\n      showErrorMessage(errorMessage);\n    } else {\n      Logger.instance.debug(`Suppressed error notification: ${errorMessage}`);\n    }\n  }\n}\n\n/**\n * Generate a nonce string for Content Security Policy\n */\nexport function getNonce(): string {\n  let text = '';\n  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  for (let i = 0; i < 32; i++) {\n    text += possible.charAt(Math.floor(Math.random() * possible.length));\n  }\n  return text;\n}\n\n/**\n * Run an async operation with proper error handling\n */\nexport async function withErrorHandling<T>(\n  operation: () => Promise<T>,\n  context: string,\n  options: {\n        showError?: boolean;\n        rethrow?: boolean;\n    } = {}\n): Promise<T | undefined> {\n  try {\n    return await operation();\n  } catch (error) {\n    if (options.showError !== false) {\n      handleError(error, context);\n    }\n    if (options.rethrow) {\n      throw error;\n    }\n    return undefined;\n  }\n}\n\n/**\n * Create an error handler function for a specific context\n */\nexport function createErrorHandler(context: string) {\n  return (error: unknown) => handleError(error, context);\n}\n\n/**\n * Generate a unique ID with an optional prefix\n */\nexport function generateId(prefix?: string): string {\n  const timestamp = Date.now();\n  const random = Math.random().toString(36).substring(2, 15);\n  return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`;\n}\n\n/**\n * Validate that required parameters are present\n */\nexport function validateRequired(params: Record<string, any>, required: string[]): void {\n  const missing = required.filter(param => params[param] === undefined);\n  if (missing.length > 0) {\n    throw new CodesssaError(\n      `Missing required parameters: ${missing.join(', ')}`,\n      'MISSING_PARAMS'\n    );\n  }\n}\n\n/**\n * Convert an error to a string representation\n */\nexport function errorToString(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return String(error);\n}\n\n/**\n * Show an error message with optional \"Show Details\" button\n */\nexport async function showDetailedError(\n  message: string,\n  details?: string,\n  buttons: string[] = []\n): Promise<string | undefined> {\n  const items = [...buttons];\n  if (details) {\n    items.push('Show Details');\n  }\n\n  // Use our showErrorMessage wrapper instead of direct VS Code call\n  const { showErrorMessage } = require('./ui/notifications');\n  const selection = await showErrorMessage(message, ...items);\n\n  if (selection === 'Show Details' && details) {\n    const detailsDoc = await vscode.workspace.openTextDocument({\n      content: details,\n      language: 'text'\n    });\n    await vscode.window.showTextDocument(detailsDoc);\n  }\n\n  return selection;\n}\n\n/**\n * Execute a task with a progress indicator\n */\nexport async function withProgress<T>(\n  title: string,\n  task: (progress: vscode.Progress<{ message?: string; increment?: number }>) => Promise<T>\n): Promise<T> {\n  return vscode.window.withProgress(\n    {\n      location: vscode.ProgressLocation.Notification,\n      title,\n      cancellable: false\n    },\n    (progress) => {\n      console.log('Progress started for:', title);\n      return task(progress);\n    }\n  );\n}\n\n/**\n * Debounce a function\n */\nexport function debounce<T extends (...args: any[]) => void>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n\n  return function executedFunction(...args: Parameters<T>) {\n    console.log('Debounced function called with args:', args.length);\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Retry an operation with exponential backoff\n */\nexport async function withRetry<T>(\n  operation: () => Promise<T>,\n  options: {\n        maxAttempts?: number;\n        initialDelay?: number;\n        maxDelay?: number;\n        backoffFactor?: number;\n    } = {}\n): Promise<T> {\n  const {\n    maxAttempts = 3,\n    initialDelay = 1000,\n    maxDelay = 10000,\n    backoffFactor = 2\n  } = options;\n\n  let attempt = 1;\n  let delay = initialDelay;\n\n  let lastError: unknown;\n  while (attempt <= maxAttempts) {\n    try {\n      return await operation();\n    } catch (error) {\n      lastError = error;\n      if (attempt >= maxAttempts) {\n        throw error;\n      }\n\n      Logger.instance.warn(`Attempt ${attempt} failed, retrying in ${delay}ms...`, error);\n      await new Promise(resolve => setTimeout(resolve, delay));\n\n      delay = Math.min(delay * backoffFactor, maxDelay);\n      attempt++;\n    }\n  }\n  // This should never be reached due to the throw above, but TypeScript needs it\n  throw lastError;\n}\n\nexport function generateUUID(): string {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n}\n\nexport async function showQuickPick<T extends vscode.QuickPickItem>(items: T[], placeHolder: string): Promise<T | undefined> {\n  return await vscode.window.showQuickPick(items, { placeHolder });\n}\n\nexport async function showInputBox(prompt: string, placeHolder?: string, value?: string): Promise<string | undefined> {\n  return await vscode.window.showInputBox({ prompt, placeHolder, value });\n}\n\n// Simple delay function\nexport function delay(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n/**\n * Ensures a file path is absolute within the workspace.\n * If relative, resolves it against the first workspace folder.\n * Returns undefined if no workspace is open and path is relative.\n */\nexport function resolveWorkspacePath(filePath: string): vscode.Uri | undefined {\n  if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n    const workspaceRoot = vscode.workspace.workspaceFolders[0].uri;\n    // Check if it's already absolute (has scheme like file://)\n    try {\n      const uri = vscode.Uri.parse(filePath);\n      if (uri.scheme) return uri; // Already absolute\n    } catch (e) {\n      console.log('Path parsing error (treating as relative):', e);\n    }\n    // If relative, join with workspace root\n    return vscode.Uri.joinPath(workspaceRoot, filePath);\n  } else if (vscode.Uri.parse(filePath).scheme) {\n    // Absolute path outside workspace (allow this)\n    return vscode.Uri.parse(filePath);\n  }\n  // Relative path but no workspace open\n  // Logger.instance.warn(`Cannot resolve relative path \"${filePath}\" without an open workspace folder.`);\n  return undefined;\n}\n\n"]}