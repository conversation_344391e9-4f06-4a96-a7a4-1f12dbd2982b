{"version": 3, "file": "promptsSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/promptsSettingsSection.ts"], "names": [], "mappings": ";AAAA,sCAAsC;;AAqBtC,oEAYC;AA/BD,kDAAkD;AAClD,SAAS,aAAa,CAAC,GAA8B;IACnD,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,CAAC;AAUD,gDAAqE;AAErE,IAAI,OAAO,GAAa,EAAE,CAAC;AAC3B,IAAI,gBAAgB,GAAkB,IAAI,CAAC;AAC3C,MAAM,YAAY,GAA6B,kCAAoB,CAAC,OAAO,CAAC;AAE5E,SAAgB,4BAA4B,CAAC,SAAsB,EAAE,QAAa;IAChF,qBAAqB;IACrB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAClE,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAC9B,uBAAuB;IACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;IACvD,IAAI,MAAM;QAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IACxE,gBAAgB;IAChB,MAAM,eAAe,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;IACnE,IAAI,eAAe;QAAE,eAAe,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAChF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;IAC/D,IAAI,aAAa;QAAE,aAAa,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACnF,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAsB;IAChD,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,iBAAiB,CAAgB,CAAC;IAC1E,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrC,OAAO,CAAC,SAAS,GAAG,oDAAoD,CAAC;QACzE,OAAO;IACT,CAAC;IACD,IAAI,IAAI,GAAG;;;0BAGa,YAAY,CAAC,QAAQ;qBAC1B,YAAY,CAAC,WAAW;;;;;;yBAMpB,YAAY,CAAC,MAAM,CAAC,UAAU;oBACnC,YAAY,CAAC,MAAM,CAAC,KAAK;qBACxB,YAAY,CAAC,MAAM,CAAC,MAAM;4BACnB,YAAY,CAAC,MAAM,CAAC,YAAY;;;;aAI/C,CAAC;IACZ,IAAI,IAAI,uCAAuC;QACzC,4FAA4F;QAC5F,sBAAsB,CAAC;IAC7B,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;QACzB,IAAI,IAAI;kBACM,CAAC,CAAC,IAAI,IAAI,EAAE;kBACZ,CAAC,CAAC,IAAI,IAAI,EAAE;kBACZ,CAAC,CAAC,CAAC,IAAI,IAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAE,EAAE,CAAC,CAAC,MAAM,GAAC,EAAE,CAAA,CAAC,CAAA,KAAK,CAAA,CAAC,CAAA,EAAE;kBAC1D,CAAC,CAAC,CAAC,SAAS,IAAE,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;kBAC3B,CAAC,CAAC,QAAQ,IAAE,EAAE;;mDAEmB,GAAG;qDACD,GAAG;;cAE1C,CAAC;IACb,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,kBAAkB,CAAC;IAC3B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IACzB,qCAAqC;IACrC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACtE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe,CAAC,SAAsB,EAAE,MAAuB,EAAE,GAAkB;IAC1F,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;IACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;IAC1D,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACnF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA6B,CAAC;IACpF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA+B,CAAC;IACtF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACnF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAA4B,CAAC;IAC3F,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,IAAI,KAAK;QAAE,KAAK,CAAC,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC;IACxE,IAAI,SAAS;QAAE,SAAS,CAAC,KAAK,GAAG,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;IACpD,IAAI,SAAS;QAAE,SAAS,CAAC,KAAK,GAAG,MAAM,EAAE,IAAI,IAAI,QAAQ,CAAC;IAC1D,IAAI,SAAS;QAAE,SAAS,CAAC,KAAK,GAAG,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;IACpD,IAAI,SAAS;QAAE,SAAS,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE,SAAS,IAAE,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnE,IAAI,aAAa;QAAE,aAAa,CAAC,KAAK,GAAG,MAAM,EAAE,QAAQ,IAAI,EAAE,CAAC;IAChE,gBAAgB,GAAG,GAAG,CAAC;AACzB,CAAC;AAED,SAAS,eAAe,CAAC,SAAsB;IAC7C,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;IACrD,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,uCAAuC;IACvC,MAAM,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;IAC/D,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACxC,gBAAgB,GAAG,IAAI,CAAC;AAC1B,CAAC;AAED,SAAS,UAAU,CAAC,SAAsB,EAAE,QAAa;IACvD,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACnF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA6B,CAAC;IACpF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA+B,CAAC;IACtF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACnF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAA4B,CAAC;IAC3F,MAAM,MAAM,GAAW;QACrB,IAAI,EAAE,SAAS,EAAE,KAAK,IAAI,EAAE;QAC5B,IAAI,EAAE,SAAS,EAAE,KAAK,IAAI,QAAQ;QAClC,IAAI,EAAE,SAAS,EAAE,KAAK,IAAI,EAAE;QAC5B,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QAChG,QAAQ,EAAE,aAAa,EAAE,KAAK,IAAI,EAAE;KACrC,CAAC;IACF,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACvB,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC;IACrC,CAAC;IACD,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,eAAe,CAAC,SAAS,CAAC,CAAC;IAC3B,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAChC,CAAC;AAED,+CAAgD;AAEhD,SAAS,YAAY,CAAC,SAAsB,EAAE,GAAW;IACvD,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,aAAa,CAAC;IACvD,IAAA,iBAAS,EAAC;QACR,KAAK,EAAE,eAAe;QACtB,OAAO,EAAE,+CAA+C,UAAU,kCAAkC;QACpG,SAAS,EAAE,GAAG,EAAE;YACd,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACvB,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;YAC3B,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAChC,CAAC;KACF,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Prompts section logic and rendering\n\n// Utility to ensure parseInt always gets a string\nfunction safeGetString(val: string | null | undefined): string {\n  return typeof val === 'string' ? val : '';\n}\n\nexport type Prompt = {\n    name: string;\n    type: string;\n    text: string;\n    variables: string[];\n    template: string;\n};\n\nimport { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';\n\nlet prompts: Prompt[] = [];\nlet editingPromptIdx: number | null = null;\nconst sectionTheme: UIThemeConfig['section'] = defaultUIThemeConfig.section;\n\nexport function renderPromptsSettingsSection(container: HTMLElement, settings: any) {\n  // Sync from settings\n  prompts = Array.isArray(settings.prompts) ? settings.prompts : [];\n  renderPromptsTable(container);\n  // Add button listeners\n  const addBtn = document.getElementById('addPromptBtn');\n  if (addBtn) addBtn.onclick = () => showPromptModal(container, {}, null);\n  // Modal buttons\n  const cancelPromptBtn = document.getElementById('cancelPromptBtn');\n  if (cancelPromptBtn) cancelPromptBtn.onclick = () => hidePromptModal(container);\n  const savePromptBtn = document.getElementById('savePromptBtn');\n  if (savePromptBtn) savePromptBtn.onclick = () => savePrompt(container, settings);\n}\n\nfunction renderPromptsTable(container: HTMLElement) {\n  const section = container.querySelector('#promptsSection') as HTMLElement;\n  if (!prompts || prompts.length === 0) {\n    section.innerHTML = '<div style=\"color:#aaa;\">No prompts defined.</div>';\n    return;\n  }\n  let html = `<style>\n        .crud-table th, .crud-table td { padding: 6px 10px; }\n        .crud-table th {\n            background: ${sectionTheme.headerBg};\n            color: ${sectionTheme.headerColor};\n            font-weight: 600;\n        }\n        .crud-table tbody tr:nth-child(even) { background: #fafbfc; }\n        .crud-table tbody tr:hover { background: #e8f0fe; }\n        .btn-prompt {\n            background:${sectionTheme.button.background};\n            color:${sectionTheme.button.color};\n            border:${sectionTheme.button.border};\n            border-radius:${sectionTheme.button.borderRadius};\n            padding:3px 10px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s;\n        }\n        .btn-prompt:hover { background:#1d4ed8; }\n    </style>`;\n  html += '<table class=\"crud-table\"><thead><tr>' +\n        '<th>Name</th><th>Type</th><th>Text</th><th>Variables</th><th>Template</th><th>Actions</th>' +\n        '</tr></thead><tbody>';\n  prompts.forEach((p, idx) => {\n    html += `<tr>\n            <td>${p.name || ''}</td>\n            <td>${p.type || ''}</td>\n            <td>${(p.text||'').slice(0,40)}${(p.text||'').length>40?'...':''}</td>\n            <td>${(p.variables||[]).join(',')}</td>\n            <td>${p.template||''}</td>\n            <td>\n                <button type=\"button\" data-edit=\"${idx}\">Edit</button>\n                <button type=\"button\" data-delete=\"${idx}\">Delete</button>\n            </td>\n        </tr>`;\n  });\n  html += '</tbody></table>';\n  section.innerHTML = html;\n  // Attach edit/delete event listeners\n  section.querySelectorAll('button[data-edit]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-edit');\n      const idx = parseInt(safeGetString(idxAttr));\n      showPromptModal(container, prompts[idx], idx);\n    });\n  });\n  section.querySelectorAll('button[data-delete]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-delete');\n      const idx = parseInt(safeGetString(idxAttr));\n      deletePrompt(container, idx);\n    });\n  });\n}\n\nfunction showPromptModal(container: HTMLElement, prompt: Partial<Prompt>, idx: number | null) {\n  const modal = document.getElementById('promptModal');\n  const title = document.getElementById('promptModalTitle');\n  const nameInput = document.getElementById('promptName') as HTMLInputElement | null;\n  const typeInput = document.getElementById('promptType') as HTMLSelectElement | null;\n  const textInput = document.getElementById('promptText') as HTMLTextAreaElement | null;\n  const varsInput = document.getElementById('promptVars') as HTMLInputElement | null;\n  const templateInput = document.getElementById('promptTemplate') as HTMLInputElement | null;\n  if (modal) modal.style.display = 'flex';\n  if (title) title.innerText = idx == null ? 'Add Prompt' : 'Edit Prompt';\n  if (nameInput) nameInput.value = prompt?.name || '';\n  if (typeInput) typeInput.value = prompt?.type || 'system';\n  if (textInput) textInput.value = prompt?.text || '';\n  if (varsInput) varsInput.value = (prompt?.variables||[]).join(',');\n  if (templateInput) templateInput.value = prompt?.template || '';\n  editingPromptIdx = idx;\n}\n\nfunction hidePromptModal(container: HTMLElement) {\n  const modal = document.getElementById('promptModal');\n  if (modal) modal.style.display = 'none';\n  // Clear validation errors in container\n  const errors = container.querySelectorAll('.validation-error');\n  errors.forEach(error => error.remove());\n  editingPromptIdx = null;\n}\n\nfunction savePrompt(container: HTMLElement, settings: any) {\n  const nameInput = document.getElementById('promptName') as HTMLInputElement | null;\n  const typeInput = document.getElementById('promptType') as HTMLSelectElement | null;\n  const textInput = document.getElementById('promptText') as HTMLTextAreaElement | null;\n  const varsInput = document.getElementById('promptVars') as HTMLInputElement | null;\n  const templateInput = document.getElementById('promptTemplate') as HTMLInputElement | null;\n  const prompt: Prompt = {\n    name: nameInput?.value || '',\n    type: typeInput?.value || 'system',\n    text: textInput?.value || '',\n    variables: varsInput?.value ? varsInput.value.split(',').map(v => v.trim()).filter(Boolean) : [],\n    template: templateInput?.value || ''\n  };\n  if (editingPromptIdx == null) {\n    prompts.push(prompt);\n  } else {\n    prompts[editingPromptIdx] = prompt;\n  }\n  settings.prompts = prompts;\n  hidePromptModal(container);\n  renderPromptsTable(container);\n}\n\nimport { showModal } from '../components/modal';\n\nfunction deletePrompt(container: HTMLElement, idx: number) {\n  const promptName = prompts[idx]?.name || 'this prompt';\n  showModal({\n    title: 'Delete Prompt',\n    content: `Are you sure you want to delete the prompt \"${promptName}\"? This action cannot be undone.`,\n    onConfirm: () => {\n      prompts.splice(idx, 1);\n      const settings = (window as any).settings || {};\n      settings.prompts = prompts;\n      renderPromptsTable(container);\n    }\n  });\n}\n\n"]}