"use strict";
/**
 * Checkpoint System for Code Changes
 *
 * This module provides a checkpoint system for tracking and managing code changes
 * during AI-assisted development.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkpointManager = exports.CheckpointManager = void 0;
var checkpointManager_1 = require("./checkpointManager");
Object.defineProperty(exports, "CheckpointManager", { enumerable: true, get: function () { return checkpointManager_1.CheckpointManager; } });
Object.defineProperty(exports, "checkpointManager", { enumerable: true, get: function () { return checkpointManager_1.checkpointManager; } });
//# sourceMappingURL=index.js.map