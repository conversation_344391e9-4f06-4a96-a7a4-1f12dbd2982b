{"version": 3, "file": "baseTreeItem.js", "sourceRoot": "", "sources": ["../../../src/ui/treeItems/baseTreeItem.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAE7B,MAAa,YAAa,SAAQ,MAAM,CAAC,QAAQ;IAC7C,YACI,KAAa,EACb,gBAAiD;QAEjD,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACnC,CAAC;IAEM,IAAI,CAAC,OAMX;QACG,IAAI,OAAO,CAAC,EAAE;YAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;QACrC,IAAI,OAAO,CAAC,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QACpD,IAAI,OAAO,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAChE,IAAI,OAAO,CAAC,YAAY;YAAE,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACnE,IAAI,OAAO,CAAC,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IACxD,CAAC;IAES,OAAO,CAAC,QAAgB,EAAE,OAAe;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QAC5D,IAAI,CAAC,QAAQ,GAAG;YACZ,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YACnE,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;SACpE,CAAC;IACN,CAAC;CACJ;AA7BD,oCA6BC", "sourcesContent": ["import * as vscode from 'vscode';\r\nimport * as path from 'path';\r\n\r\nexport class BaseTreeItem extends vscode.TreeItem {\r\n    constructor(\r\n        label: string,\r\n        collapsibleState: vscode.TreeItemCollapsibleState\r\n    ) {\r\n        super(label, collapsibleState);\r\n    }\r\n\r\n    public init(options: {\r\n        id?: string;\r\n        tooltip?: string;\r\n        description?: string;\r\n        contextValue?: string;\r\n        command?: vscode.Command;\r\n    }): void {\r\n        if (options.id) this.id = options.id;\r\n        if (options.tooltip) this.tooltip = options.tooltip;\r\n        if (options.description) this.description = options.description;\r\n        if (options.contextValue) this.contextValue = options.contextValue;\r\n        if (options.command) this.command = options.command;\r\n    }\r\n\r\n    protected setIcon(iconName: string, context: string): void {\r\n        const resourcesPath = path.join(context, '..', 'resources');\r\n        this.iconPath = {\r\n            light: vscode.Uri.file(path.join(resourcesPath, 'light', iconName)),\r\n            dark: vscode.Uri.file(path.join(resourcesPath, 'dark', iconName))\r\n        };\r\n    }\r\n}\r\n"]}