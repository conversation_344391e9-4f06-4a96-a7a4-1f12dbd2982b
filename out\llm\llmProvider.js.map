{"version": 3, "file": "llmProvider.js", "sourceRoot": "", "sources": ["../../src/llm/llmProvider.ts"], "names": [], "mappings": ";;;AA+HA,MAAsB,WAAW;IACrB,MAAM,CAAY;IAE5B,YAAY,OAAY;QACtB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;IACxB,CAAC;IAMW,UAAU,CAAC,MAAyB;QAC5C,OAAO,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC/C,CAAC;IAES,cAAc,CAAC,MAAyB;QAChD,OAAO,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,CAAC;IAC9D,CAAC;IAES,YAAY,CAAC,MAAyB;QAC9C,OAAO,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC;IAC3D,CAAC;CACJ;AAtBD,kCAsBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool } from '../tools/tool.ts.backup';\nimport { LLMConfig, LLMGenerateParams, LLMGenerateResult } from './types';\n\nexport interface ToolCallRequest {\n    id?: string;\n    name?: string;\n    toolId?: string;\n    arguments?: Record<string, any>;\n    args?: Record<string, any>;\n}\n\nexport interface LLMModelInfo {\n    id: string;\n    name?: string;\n    description?: string;\n    contextWindow?: number;\n    maxOutputTokens?: number;\n    supportsFunctions?: boolean;\n    supportsVision?: boolean;\n    deprecated?: boolean;\n    pricingInfo?: string;\n}\n\nexport interface LLMProviderConfig {\n    apiKey?: string;\n    apiEndpoint?: string;\n    organizationId?: string;\n    defaultModel?: string;\n    additionalParams?: Record<string, any>;\n    llamaCppPath?: string;\n    additionalModelDirectories?: string[];\n}\n\nexport interface ILLMProvider {\n    readonly providerId: string;\n    readonly displayName: string;\n    readonly description: string;\n    readonly website: string;\n    readonly defaultEndpoint?: string;\n    readonly requiresApiKey: boolean;\n    readonly supportsEndpointConfiguration: boolean;\n    readonly defaultModel?: string;\n    readonly supportsEmbeddings?: boolean;\n\n    /**\n     * Generates text based on the provided parameters.\n     */\n    generate(\n        _params: LLMGenerateParams,\n        _cancellationToken?: vscode.CancellationToken,\n        _tools?: Map<string, ITool>\n    ): Promise<LLMGenerateResult>;\n\n    /**\n     * Generates text streamingly, yielding chunks as they arrive.\n     * Note: Not all providers support streaming.\n     */\n    streamGenerate?(\n        _params: LLMGenerateParams,\n        _cancellationToken?: vscode.CancellationToken\n    ): AsyncGenerator<string, void, unknown>;\n\n    /**\n     * Generates an embedding vector for the given text.\n     * Note: Not all providers support embeddings.\n     */\n    generateEmbedding?(\n        _text: string,\n        _modelId?: string\n    ): Promise<number[]>;\n\n    /**\n     * Gets an embeddings interface for this provider.\n     * Note: Not all providers support embeddings.\n     */\n    getEmbeddings?(): Promise<any>;\n\n    /**\n     * Fetches the list of available models for this provider.\n     * @deprecated Use listModels() instead\n     */\n    getAvailableModels?(): Promise<string[]>;\n\n    /**\n     * Lists available models with their details.\n     * This is the preferred method for getting models.\n     */\n    listModels(): Promise<LLMModelInfo[]>;\n\n    /**\n     * Lists available embedding models with their details.\n     * Only relevant for providers that support embeddings.\n     */\n    listEmbeddingModels?(): Promise<LLMModelInfo[]>;\n\n    /**\n     * Tests the connection to the provider with the specified model.\n     */\n    testConnection(_modelId: string): Promise<{success: boolean, message: string}>;\n\n    /**\n     * Checks if the provider is configured and ready (e.g., API key set).\n     */\n    isConfigured(): boolean;\n\n    /**\n     * Gets the current configuration for this provider.\n     */\n    getConfig(): LLMProviderConfig;\n\n    /**\n     * Updates the configuration for this provider.\n     */\n    updateConfig(_config: any): Promise<void>;\n\n    /**\n     * Loads the configuration for this provider from settings.\n     */\n    loadConfig(): Promise<void>;\n\n    /**\n     * Gets the required configuration fields for this provider.\n     */\n    getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select' | 'file' | 'directory', options?: string[]}>;\n}\n\nexport abstract class LLMProvider {\n  protected config: LLMConfig;\n\n  constructor(_config: any) {\n    this.config = _config;\n  }\n\n    abstract generate(_params: LLMGenerateParams): Promise<LLMGenerateResult>;\n    abstract getModels(): Promise<string[]>;\n    abstract validateConfig(_config: any): boolean;\n\n    protected getModelId(params: LLMGenerateParams): string {\n      return params.modelId || this.config.modelId;\n    }\n\n    protected getTemperature(params: LLMGenerateParams): number {\n      return params.temperature ?? this.config.temperature ?? 0.7;\n    }\n\n    protected getMaxTokens(params: LLMGenerateParams): number {\n      return params.maxTokens ?? this.config.maxTokens ?? 2000;\n    }\n}\n\nexport { LLMGenerateParams, LLMGenerateResult };\n"]}