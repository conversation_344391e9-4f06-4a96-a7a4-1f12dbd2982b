{"version": 3, "file": "corePolyfill.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/corePolyfill.ts"], "names": [], "mappings": ";AAAA,iEAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DjE,gCAQC;AAlEY,QAAA,KAAK,GAAG,WAAW,CAAC;AACpB,QAAA,GAAG,GAAG,SAAS,CAAC;AAQ7B,oFAAoF;AACpF,8DAA8D;AAC9D,MAAa,cAAc;IAKN;IAJZ,KAAK,GAAwB,EAAE,CAAC;IAChC,QAAQ,GAAQ,SAAS,CAAC;IACzB,UAAU,CAAU;IACpB,QAAQ,GAAG,KAAK,CAAC;IACzB,YAAmB,IAAuB;QAAvB,SAAI,GAAJ,IAAI,CAAmB;QACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAChC,CAAC;IACD,OAAO,CAAC,IAAY,EAAE,OAAY,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IAChF,OAAO,CAAC,IAAY,EAAE,EAAU,IAAI,UAAU,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IAC7D,mBAAmB,CAAC,GAAG,IAAW,IAAI,UAAU,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IAC/D,QAAQ,CAAC,KAAa,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IACjE,MAAM,CAAC,GAAW,IAAI,UAAU,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IAC/C,aAAa,CAAC,KAAa,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IACtE,OAAO,KAAK,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IAChD,KAAK,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC;IACxB,KAAK,CAAC,GAAG,CAAC,KAAQ,IAAgB,OAAO,KAAK,CAAC,CAAC,CAAC;IACjD,KAAK,CAAC,MAAM,CAAC,KAAQ,EAAE,MAAY,IAAgB,OAAO,KAAK,CAAC,CAAC,CAAC;CACnE;AAlBD,wCAkBC;AAwBD;;;;GAIG;AACH,SAAgB,UAAU,CAAI,OAA8B;IAC1D,0CAA0C;IAC1C,MAAM,IAAI,GAA0B;QAClC,cAAc,EAAE,IAAI;QACpB,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC;QAClB,IAAI,EAAE,EAAE,CAAC,sBAAsB;KAChC,CAAC;IACF,OAAO,IAAI,CAAC;AACd,CAAC;AAeD,WAAiB,UAAU;IACzB,SAAgB,IAAI,CAAmC,MAAe;QACpE,8CAA8C;QAC9C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YACxC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ;gBAAG,CAAS,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,EAAE,MAAM,EAAS,CAAC;QAKjC,oBAAoB;QACnB,MAAc,CAAC,IAAI,GAAG,MAAM,CAAC;QAC9B,6BAA6B;QAC5B,MAAc,CAAC,KAAK,GAAG,IAA4B,CAAC;QACrD,OAAO,MAA8D,CAAC;IACxE,CAAC;IAfe,eAAI,OAenB,CAAA;AACH,CAAC,EAjBgB,UAAU,0BAAV,UAAU,QAiB1B;AAQD,IAAI,GAAG,GAAQ,SAAS,CAAC;AACzB,IAAI,CAAC;IAAC,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAAC,CAAC;AAAC,MAAM,CAAC,CAAC,2CAA2C,CAAC,CAAC;AAEnF;;;GAGG;AACH,IAAI,GAAG,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;IACzB,2CAA2C;IAC3C,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE;YACtD,KAAK,EAAE;gBACL,OAAO,CAAC,EAAY,EAAE,WAAwB;oBAC5C,6BAA6B;oBAC5B,IAAY,CAAC,eAAe,GAAG,EAAE,CAAC;oBACnC,IAAI,WAAW;wBAAG,IAAY,CAAC,mBAAmB,GAAG,WAAW,CAAC;oBACjE,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;YACD,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;IACL,CAAC;IACD,gCAAgC;IAChC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACnC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,OAAiB;YACxD,IAAY,CAAC,eAAe,GAAG,OAAO,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,+CAA+C;AAC/C,SAAS,oBAAoB,CAAC,GAAQ;IACpC,OAAO,CAAC,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,cAAc,KAAK,IAAI,CAAC;AACzE,CAAC;AACD,SAAS,WAAW,CAAC,GAAQ;IAC3B,OAAO,GAAG,IAAI,GAAG,YAAY,GAAG,CAAC,SAAS,CAAC;AAC7C,CAAC;AAaD,MAAa,UAAU;IACd,KAAK,GAAG,EAAqE,CAAC;IAC9E,KAAK,GAAuC,EAAE,CAAC;IACtD,qFAAqF;IAC9E,UAAU,CAA8B;IACxC,WAAW,CAA8B;IACzC,QAAQ,GAAG,KAAK,CAAC;IACjB,MAAM,CAAsD;IAC5D,WAAW,CAAO;IAClB,YAAY,CAAO;IACnB,SAAS,CAAkB;IAElC,YAAY,oBAAyB;QACnC,mEAAmE;QACnE,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,KAAK,IAAI,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACtF,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC,KAAK,CAAC;YAC9C,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,MAAM,CAAC;YAChD,IAAI,CAAC,MAAM,GAAG;gBACZ,GAAG,oBAAoB,CAAC,KAAK,CAAC,MAAM;gBACpC,GAAG,oBAAoB,CAAC,MAAM,CAAC,MAAM;aACtC,CAAC;QACJ,CAAC;aAAM,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,EAAE,CAAC;YAC/D,IAAI,CAAC,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC;QAC5C,CAAC;aAAM,IAAI,GAAG,IAAI,oBAAoB,YAAY,GAAG,CAAC,SAAS,EAAE,CAAC;YAChE,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC;YACtC,IAAI,CAAC,MAAM,GAAG,oBAAoB,CAAC,KAAK,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,GAAG,oBAAoB,CAAC;QACrC,CAAC;IACH,CAAC;IAED,OAAO,CAAC,GAA+B,EAAE,EAAuB,EAAE,OAA0C;QAC1G,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,IAAgC,EAAE,EAA8B;QACtE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,CAAC,IAAgC,EAAE,UAA0F;QAC9I,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACpE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,aAAa,CAAC,GAA+B;QAC3C,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,GAA+B;QAC5C,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACnF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,4CAA4C;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,CAAC,MAAM,CAAC,KAA2B;QACvC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC/E,IAAI,KAAK,GAAgB,EAAE,GAAI,KAAqB,EAAE,CAAC;QACvD,IAAI,OAAO,GAA+B,IAAI,CAAC,UAAU,CAAC;QAC1D,MAAM,OAAO,GAAG,IAAI,GAAG,EAA8B,CAAC;QACtD,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACrB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,IAAI;gBAAE,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,cAAc,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YACpF,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,SAAS,CAAyB,CAAC;YAC9D,oDAAoD;YACpD,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC1B,IAAI,CAAC;oBAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAyB,CAAC;gBAAC,CAAC;gBACtE,OAAO,CAAC,EAAE,CAAC;oBAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;gBAAC,CAAC;YAC3E,CAAC;YACD,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,EAAiB,CAAC,CAAC,kDAAkD;YAClG,MAAM,KAAK,CAAC;YACZ,iBAAiB;YACjB,IAAI,IAAI,GAA2C,SAAS,CAAC;YAC7D,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC9B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC1B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC7C,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;wBACf,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,GAAG,IAAK,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,MAAM,CAAC,KAA2B;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC/E,IAAI,KAAK,GAAgB,EAAE,GAAI,KAAqB,EAAE,CAAC;QACvD,IAAI,OAAO,GAA+B,IAAI,CAAC,UAAU,CAAC;QAC1D,MAAM,OAAO,GAAG,IAAI,GAAG,EAA8B,CAAC;QACtD,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACrB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,IAAI;gBAAE,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,cAAc,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YACpF,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,SAAS,CAAyB,CAAC;YAC9D,oDAAoD;YACpD,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC1B,IAAI,CAAC;oBAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAyB,CAAC;gBAAC,CAAC;gBACtE,OAAO,CAAC,EAAE,CAAC;oBAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;gBAAC,CAAC;YAC3E,CAAC;YACD,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,EAAiB,CAAC,CAAC,kDAAkD;YAClG,yDAAyD;YACzD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBACzD,IAAI,oBAAoB,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE,CAAC;wBACzF,KAAa,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC1C,CAAC;gBACH,CAAC;YACH,CAAC;YACD,iBAAiB;YACjB,IAAI,IAAI,GAA2C,SAAS,CAAC;YAC7D,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC9B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC1B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC7C,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;wBACf,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,GAAG,IAAK,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAlJD,gCAkJC;AAED,4BAA4B;AAC5B,gCAAgC;AAChC,+DAA+D;AAC/D,MAAM;AACN,oCAAoC;AACpC,wBAAwB;AACxB,uBAAuB;AACvB,gBAAgB;AAChB,6BAA6B;AAC7B,yDAAyD;AAEzD,kCAAkC;AAGlC,+BAAoC,CAAC,0BAA0B;AAC/D,+CAAiC,CAAC,+CAA+C;AAEjF,yCAAsC;AAMtC,gBAAgB;AAChB,6CAA6C;AAC7C,MAAa,IAAI;IACC,EAAE,CAAS;IACX,IAAI,CAAS;IACb,WAAW,CAAS;IACpB,IAAI,CAAmC;IACvC,QAAQ,CAAU;IAClB,kBAAkB,CAAO,CAAC,+BAA+B;IACzD,OAAO,CAAuB;IAE9C,YACE,IAAY,EACZ,WAAmB,EACnB,OAMC;QAED,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC7F,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAClH,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,eAAe,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,QAAQ,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,OAAO,EAAE,kBAAkB,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC;IAClC,CAAC;IAED;;;;;;SAMK;IACL,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAU,EAAE,OAAa;QACrE,qCAAqC;QACrC,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE,CAAC;YAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACxC,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBACnD,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,UAAU,EAAE,EAAE,CAAC;YACpE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,2DAA2D;YAC3D,MAAM,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,+BAA+B,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;SAEK;IACL,qBAAqB;QACnB,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,kBAAkB;YAC/B,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;IACJ,CAAC;CACF;AAjED,oBAiEC;AAGgB,wBAAQ;AAEzB;;GAEG;AACH,MAAa,cAAe,SAAQ,IAAI;IAC/B,MAAM,CAAM;IACnB,YACE,IAAI,GAAG,iBAAiB,EACxB,WAAW,GAAG,+CAA+C,EAC7D,MAAY,EACZ,OAKC;QAED,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,EAAE,GAAG,OAAO,EAAE,kBAAkB,EAAE,MAAM,EAAE,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IAC7B,CAAC;IACQ,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAU,EAAE,OAAa;QAC9E,kCAAkC;QAClC,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,CAAC,IAAI,gDAAgD,CAAC,CAAC;IAChG,CAAC;IACD;;;;SAIK;IACL,KAAK,CAAC,MAAM,CAAC,KAAU,EAAE,OAAa;QACpC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;CACF;AA5BD,wCA4BC;AAED,mBAAmB;AAEnB;;GAEG;AACH,MAAa,WAAW;IACb,OAAO,CAAS;IAChB,IAAI,CAAS,CAAC,kCAAkC;IAChD,iBAAiB,CAAsB,CAAC,iBAAiB;IAElE,YAAY,OAAe,EAAE,oBAAyC,EAAE;QACtE,IAAI,OAAO,OAAO,KAAK,QAAQ;YAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACtF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,yCAAyC;QAC7D,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;CACF;AAXD,kCAWC;AAED;;GAEG;AACH,MAAa,YAAa,SAAQ,WAAW;IACzB,IAAI,GAAW,OAAO,CAAC;IACzC,YAAY,OAAe,EAAE,oBAAyC,EAAE;QACtE,KAAK,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IACpC,CAAC;CACF;AALD,oCAKC;AAED;;GAEG;AACH,MAAa,SAAU,SAAQ,WAAW;IACtB,IAAI,GAAW,IAAI,CAAC;IACtC,YAAY,OAAe,EAAE,oBAAyC,EAAE;QACtE,KAAK,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IACpC,CAAC;CACF;AALD,8BAKC;AAED;;GAEG;AACH,MAAa,WAAY,SAAQ,WAAW;IACxB,IAAI,GAAW,MAAM,CAAC;IACxC,QAAQ,CAAS;IACjB,SAAS,CAAM;IACf,YAAY,QAAgB,EAAE,SAAc,EAAE,OAAO,GAAG,EAAE,EAAE,oBAAyC,EAAE;QACrG,KAAK,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;CACF;AATD,kCASC;AAED;;GAEG;AACH,MAAa,aAAc,SAAQ,WAAW;IAC1B,IAAI,GAAW,QAAQ,CAAC;IAC1C,YAAY,OAAe,EAAE,oBAAyC,EAAE;QACtE,KAAK,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IACpC,CAAC;CACF;AALD,sCAKC;AAED;;;;GAIG;AACH,MAAa,UAAU;IACF,GAAG,CAAS;IACZ,IAAI,GAAW,eAAe,CAAC,CAAC,kCAAkC;IAErF,YAAY,GAAG,GAAG,GAAG;QACnB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED;;;;SAIK;IACK,KAAK,CAAC,cAAc,CAAC,IAAY;QACzC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,kDAAkD;YAClD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;QAED,4CAA4C;QAC5C,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;QAE3E,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,gFAAgF;QAChF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,iDAAiD;YACjD,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;YACjH,MAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE,CAAC;YAEvF,8EAA8E;YAC9E,MAAM,WAAW,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;YAE/D,mBAAmB;YACnB,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACnC,CAAC;QAED,6DAA6D;QAC7D,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACxE,IAAI,IAAI,KAAK,CAAC;YAAE,OAAO,MAAM,CAAC,CAAC,yBAAyB;QACxD,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,cAAc,CAAC,KAAe;QAClC,mEAAmE;QACnE,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC;CACF;AA/DD,gCA+DC;AAED;;;GAGG;AACH,MAAa,gBAAiB,SAAQ,UAAU;IACtC,kBAAkB,CAAS;IAC3B,SAAS,CAAS,CAAC,0BAA0B;IAErD,wGAAwG;IACxG,MAAM,CAAe;IAErB,YAAY,UAQR,EAAE;QACJ,gEAAgE;QAChE,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,IAAK,OAAe,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,mCAAmC;QACnG,KAAK,CAAC,GAAG,CAAC,CAAC;QAEX,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,uBAAuB;QACnF,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,qBAAqB;QAC/D,4FAA4F;QAC5F,wDAAwD;QACxD,8DAA8D;QAC9D,qCAAqC;IACvC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe;QAC3B,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED;;SAEK;IACI,KAAK,CAAC,UAAU,CAAC,IAAY;QACpC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC;IACrE,CAAC;IAED;;;;;SAKK;IACI,KAAK,CAAC,cAAc,CAAC,KAAe;QAC3C,MAAM,OAAO,GAAe,EAAE,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACtD,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YACtD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,6BAA6B;YAC3D,0DAA0D;YAC1D,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9F,OAAO,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;QACnC,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA9DD,4CA8DC;AAYD,MAAa,0BAA0B;IACrC,EAAE,CAAS;IACX,KAAK,CAAS,CAAC,gCAAgC;IAC/C,QAAQ,CAAW,CAAC,qCAAqC;IACzD,WAAW,CAAS;IAEpB,YAAY,EAAU,EAAE,KAAa,EAAE,QAAkB,EAAE,WAAmB;QAC5E,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;CACF;AAZD,gEAYC;AAED;;;GAGG;AACH,MAAe,qBAAqB;IACxB,KAAK,GAAiC,IAAI,GAAG,EAAE,CAAC;IAChD,SAAS,GAAkB,IAAI,CAAC,CAAC,2BAA2B;IAE5D,cAAc,CAAC,MAAgB,EAAE,WAA2B;QACpE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB;YACzF,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QACD,MAAM,GAAG,GAAG,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC;QAC1C,IAAI,GAAG,KAAK,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,uCAAuC,GAAG,SAAS,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QACvF,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,4CAA4C;QAC9E,CAAC;IACH,CAAC;IAES,gBAAgB,CAAC,IAAc,EAAE,IAAc;QACvD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,+DAA+D;YAC/D,OAAO,CAAC,IAAI,CAAC,yCAAyC,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACvF,OAAO,CAAC,CAAC,CAAC,8CAA8C;QAC1D,CAAC;QACD,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEpC,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,CAAC,CAAC,4DAA4D;QACxE,CAAC;QACD,OAAO,UAAU,GAAG,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC;IAChD,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,UAAU,CAAC,KAAwB;QACvC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,qCAAqC;YACvE,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ;gBAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YAC9G,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,kDAAkD;QAC1F,CAAC;IACH,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,eAAe,CAAC,GAAa;QACjC,MAAM,OAAO,GAAG,IAAI,GAAG,EAA2B,CAAC;QACnD,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC;YACrB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAChC,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,aAAa,CAAC,GAAa;QAC/B,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC1B,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QACD,yFAAyF;QACzF,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,yCAAyC;QAClE,CAAC;QACD,OAAO,UAAU,CAAC,CAAC,8BAA8B;IACnD,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,kBAAkB;QACzC,2DAA2D;IAC7D,CAAC;IAED;;;;;;;;SAQK;IACL,KAAK,CAAC,gBAAgB,CACpB,WAAqB,EACrB,CAAS,EACT,MAAmE;QAEnE,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,kCAAkC;QAEpF,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAEjD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE,CAAC;gBACjC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YACzD,CAAC;iBAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACtC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACpC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAC5C,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,KAAK,CAC9C,CACF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5C,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC;YACtD,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC,CAAC,CAAC;QAEJ,0DAA0D;QAC1D,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAEhD,uBAAuB;QACvB,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;CACF;AAGD;;GAEG;AACH,MAAa,aAAc,SAAQ,qBAAqB;IAC9C,UAAU,GAAsB,IAAI,CAAC,CAAC,uDAAuD;IAC7F,SAAS,CAAqB,CAAC,qBAAqB;IAE5D,uDAAuD;IACvD,iFAAiF;IACjF,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAsB,EAAE,OAA+B;QACpF,qEAAqE;QACrE,0EAA0E;QAC1E,MAAM,KAAK,GAAG,IAAI,aAAa,EAAE,CAAC;QAClC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC,yBAAyB;QACxD,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,kBAAkB;QACvD,6EAA6E;QAC7E,uJAAuJ;QACvJ,OAAO,KAAK,CAAC;IACf,CAAC;IAED,uFAAuF;IAEvF;;;SAGK;IACL,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,SAAS,EAA2F;QAC1H,4EAA4E;QAC5E,0GAA0G;QAC1G,mFAAmF;QACnF,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACjE,OAAO,CAAC,IAAI,CAAC,yDAAyD,SAAS,0BAA0B,IAAI,CAAC,SAAS,gCAAgC,CAAC,CAAC;YACzJ,iDAAiD;QACnD,CAAC;QAED,MAAM,KAAK,GAAsB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACjD,EAAE,EAAE,CAAC,CAAC,EAAE;YACR,MAAM,EAAE,CAAC,CAAC,MAAM;YAChB,QAAQ,EAAE,CAAC,CAAC,QAAQ;YACpB,WAAW,EAAE,CAAC,CAAC,QAAQ,EAAE,WAAW,IAAI,CAAC,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE,uCAAuC;SACxG,CAAC,CAAC,CAAC;QACJ,MAAM,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC9B,wEAAwE;QACxE,OAAO,EAAE,aAAa,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,oBAAoB;IAC9D,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,SAAS,EAAyC;QACnE,oDAAoD;QACpD,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACjE,OAAO,CAAC,IAAI,CAAC,wDAAwD,SAAS,0BAA0B,IAAI,CAAC,SAAS,gCAAgC,CAAC,CAAC;QAC1J,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAClD,MAAM,aAAa,GAA2E,EAAE,CAAC;QACjG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACtB,2GAA2G;YAC3G,MAAM,gBAAgB,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC9C,OAAO,gBAAgB,CAAC,WAAW,CAAC,CAAC,yBAAyB;YAC9D,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC;QAC5F,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,CAAC,CAAC,oBAAoB;IAC1F,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAA+D;QACrG,2BAA2B;QAC3B,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACjE,OAAO,CAAC,IAAI,CAAC,yDAAyD,SAAS,0BAA0B,IAAI,CAAC,SAAS,gCAAgC,CAAC,CAAC;QAC3J,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;aAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,2DAA2D;YAC3D,OAAO,CAAC,IAAI,CAAC,6FAA6F,CAAC,CAAC;QAC9G,CAAC;QACD,mCAAmC;IACrC,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,EAO5E;QACC,2BAA2B;QAC3B,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACjE,OAAO,CAAC,IAAI,CAAC,wDAAwD,SAAS,0BAA0B,IAAI,CAAC,SAAS,gCAAgC,CAAC,CAAC;QAC1J,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAEnE,6CAA6C;QAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACjC,MAAM,KAAK,GAAuD;gBAChE,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,2CAA2C;aAC3E,CAAC;YACF,IAAI,aAAa,EAAE,CAAC;gBAClB,sDAAsD;gBACtD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC3C,IAAI,UAAU,EAAE,CAAC;oBACf,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;gBACnC,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,CAAC;IACrB,CAAC;IAED,sDAAsD;IAEtD;;;;SAIK;IACL,KAAK,CAAC,YAAY,CAAC,SAAqB;QACtC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,+EAA+E,CAAC,CAAC;QACnG,CAAC;QACD,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,+DAA+D;QAC/D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE5D,MAAM,KAAK,GAAsB,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1D,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,IAAA,SAAM,GAAE,EAAE,uCAAuC;YAC3G,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;YAClB,QAAQ,EAAE,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,EAAE,iBAAiB;YAChD,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,yBAAyB;SACxD,CAAC,CAAC,CAAC;QACJ,MAAM,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,uDAAuD;QACtF,4CAA4C;IAC9C,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,yBAAyB,CAAC,KAAa,EAAE,CAAS,EAAE,MAA4B;QACpF,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC,CAAC;QAChH,CAAC;QACD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAE/D,uEAAuE;QACvE,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAExE,qDAAqD;QACrD,MAAM,YAAY,GAAyB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5D,4CAA4C;YAC5C,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9H,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,mEAAmE;IACnE,WAAW,CAAC,CAAU;QACpB,OAAO;YACL,oBAAoB,EAAE,KAAK,EAAE,KAAa,EAAuB,EAAE;gBACjE,kEAAkE;gBAClE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,6BAA6B;gBAClG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;YAC5C,CAAC;SACF,CAAC;IACJ,CAAC;CACF;AAnLD,sCAmLC;AAGD;;;GAGG;AACH,MAAa,MAAO,SAAQ,qBAAqB;IACvC,cAAc,GAAG,oBAAoB,CAAC;IACtC,gBAAgB,GAAkB,IAAI,CAAC;IACvC,kBAAkB,GAAwB,EAAE,CAAC;IAC7C,UAAU,CAAa,CAAC,kCAAkC;IAElE,wEAAwE;IACxE,YAAoB,UAAsB,EAAE,MAAyF;QACnI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACrF,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC,0BAA0B;QAExD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC;YACnE,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC;YACzE,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC;QACjF,CAAC;QACD,kFAAkF;QAClF,6FAA6F;IAC/F,CAAC;IAED;;;;;;;;SAQK;IACL,MAAM,CAAC,KAAK,CAAC,aAAa,CACxB,SAAqB,EAAE,qCAAqC;IAC5D,UAAsB,EACtB,MAAwF;QAExF,gDAAgD;QAChD,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAEhD,gEAAgE;QAChE,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,wFAAwF;YACxF,iEAAiE;YACjE,MAAM,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,iGAAiG;YACjG,gFAAgF;QAClF,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,iDAAiD;IAEjD;;;;;;SAMK;IACL,KAAK,CAAC,YAAY,CAAC,SAAqB,EAAE,OAA4B;QACpE,sCAAsC;QACtC,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAChC,IAAI,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/E,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACzF,CAAC;YACD,OAAO,GAAG,CAAC,WAAW,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,sDAAsD;QACtD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE5D,MAAM,KAAK,GAAsB,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAE1B,kEAAkE;YAClE,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,IAAA,SAAM,GAAE,CAAC;YAE/F,KAAK,CAAC,IAAI,CAAC;gBACT,EAAE,EAAE,EAAE;gBACN,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE;oBACR,GAAG,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC;oBACvB,MAAM,EAAE,EAAE,EAAE,uCAAuC;iBACpD;gBACD,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,yBAAyB;aACxD,CAAC,CAAC;YACH,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC;QACD,MAAM,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,uDAAuD;QACtF,2DAA2D;QAC3D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,GAAG,CAAC,OAOT;QACC,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,mBAAmB;QAClG,IAAI,OAAO,GAAsB,EAAE,CAAC;QAEpC,IAAI,OAAO,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC1D,8FAA8F;YAC9F,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAA2B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACtG,CAAC;aAAM,CAAC;YACN,uEAAuE;YACvE,MAAM,QAAQ,GAAG,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAqB,EAAE,EAAE,CAC1D,MAAM,CAAC,OAAO,CAAC,OAAQ,CAAC,KAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CACrD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,KAAK,CAC9C,CAAC,CAAC,CAAC,SAAS,CAAC;YAEhB,oDAAoD;YACpD,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7F,CAAC;QAED,yBAAyB;QACzB,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,OAAO,EAAE,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;QAC/C,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;QAE/D,+CAA+C;QAC/C,MAAM,QAAQ,GAAsB;YAClC,GAAG,EAAE,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,UAAU,EAAE,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;YAC9F,SAAS,EAAE,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;YAC9F,2DAA2D;YAC3D,SAAS,EAAE,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;SAC1G,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,MAAM,CAAC,OAIZ;QACC,IAAI,WAAW,GAAa,EAAE,CAAC;QAE/B,IAAI,OAAO,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3C,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC;QAC5B,CAAC;aAAM,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;YAC1B,uCAAuC;YACvC,MAAM,QAAQ,GAAG,CAAC,IAAqB,EAAE,EAAE,CACzC,MAAM,CAAC,OAAO,CAAC,OAAQ,CAAC,KAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CACrD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,KAAK,CAC9C,CAAC;YACJ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtF,CAAC;aAAM,IAAI,OAAO,EAAE,aAAa,EAAE,CAAC;YAClC,iFAAiF;YACjF,MAAM,QAAQ,GAAG,CAAC,IAAqB,EAAE,EAAE,CACzC,MAAM,CAAC,OAAO,CAAC,OAAQ,CAAC,aAAc,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAC7D,GAAG,KAAK,WAAW,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAC7H,CAAC;YACJ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtF,CAAC;aAAM,CAAC;YACN,kFAAkF;YAClF,eAAM,CAAC,IAAI,CAAC,2GAA2G,CAAC,CAAC;YACzH,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,sBAAsB;QACrE,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,mBAAmB,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,wBAAwB;YAC5F,mCAAmC;YACnC,0EAA0E;YAC1E,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QACD,wGAAwG;QACxG,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,yBAAyB,CAC7B,KAAa,EACb,CAAS,EACT,MAAwC;QAExC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAE/D,uEAAuE;QACvE,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAExE,qDAAqD;QACrD,MAAM,YAAY,GAAyB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5D,4CAA4C;YAC5C,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,sCAAsC;YACzI,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,iCAAiC,CACrC,SAAmB,EACnB,CAAS,EACT,MAAwC;QAExC,uEAAuE;QACvE,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAEnE,qDAAqD;QACrD,MAAM,YAAY,GAAyB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5D,4CAA4C;YAC5C,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,sCAAsC;YACzI,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,2GAA2G;IAC3G,WAAW,CAAC,CAAU;QACpB,OAAO;YACL,oBAAoB,EAAE,KAAK,EAAE,KAAa,EAAuB,EAAE;gBACjE,kEAAkE;gBAClE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,6BAA6B;gBAClG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;SACF,CAAC;IACJ,CAAC;CACF;AA7PD,wBA6PC;AAGD,wBAAwB;AAExB;;;;GAIG;AACH,MAAa,8BAA8B;IACxB,SAAS,CAAS;IAClB,YAAY,CAAS;IACrB,UAAU,CAAW;IACrB,aAAa,CAAU,CAAC,mBAAmB;IAC5D,0EAA0E;IAC1E,mEAAmE;IACnE,6DAA6D;IAE7D,YAAY,OAMX;QACC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,GAAG,CAAC;QAChD,uDAAuD;QACvD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAChE,8DAA8D;QAC9D,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;QAC/E,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC,CAAC,gBAAgB;QAErE,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QACvE,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAChF,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QAExG,8FAA8F;QAC9F,2EAA2E;IAC7E,CAAC;IAED;;;;;;;SAOK;IACG,mBAAmB,CAAC,IAAY,EAAE,UAAoB;QAC5D,4BAA4B;QAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAEjC,iFAAiF;QACjF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YACjF,8DAA8D;YAC9D,2DAA2D;YAC3D,2EAA2E;YAC3E,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,MAAM,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,mBAAmB,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEhD,2CAA2C;QAC3C,IAAI,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACxD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAC3C,MAAM,QAAQ,GAAa,EAAE,CAAC;YAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,8BAA8B;gBAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;gBACxE,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;gBAE9B,0EAA0E;gBAC1E,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,qEAAqE;YACrE,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACG,YAAY,CAAC,QAAkB;QACrC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,YAAY,GAAa,EAAE,CAAC,CAAC,2CAA2C;QAC5E,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,kDAAkD;QAEzE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;YACrC,MAAM,mBAAmB,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC;YAEjH,yDAAyD;YACzD,IAAI,mBAAmB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACzC,yDAAyD;gBACzD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBAE1C,yEAAyE;oBACzE,IAAI,iBAAiB,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;oBAChD,IAAI,WAAW,GAAG,EAAE,CAAC;oBACrB,MAAM,eAAe,GAAa,EAAE,CAAC;oBAErC,wEAAwE;oBACxE,OAAO,iBAAiB,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBACxE,MAAM,WAAW,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;wBACpD,kEAAkE;wBAClE,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3F,IAAI,WAAW,CAAC,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC,YAAY,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACrF,4EAA4E;4BAC5E,MAAM;wBACR,CAAC;wBACD,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,2CAA2C;wBACjF,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC9C,iBAAiB,EAAE,CAAC;oBACtB,CAAC;oBAED,mFAAmF;oBACnF,YAAY,GAAG,CAAC,GAAG,eAAe,EAAE,OAAO,CAAC,CAAC;oBAC7C,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;gBAEtD,CAAC;qBAAM,CAAC;oBACN,kEAAkE;oBAClE,qEAAqE;oBACrE,wEAAwE;oBACxE,uEAAuE;oBACvE,OAAO,CAAC,IAAI,CAAC,8EAA8E,aAAa,MAAM,IAAI,CAAC,SAAS,6BAA6B,CAAC,CAAC;oBAC3J,MAAM,gBAAgB,GAAa,EAAE,CAAC;oBACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACxD,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBAClE,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;oBACjC,YAAY,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,iDAAiD;oBAC3E,aAAa,GAAG,aAAa,CAAC,CAAC,eAAe;oBAC9C,yEAAyE;oBACzE,4EAA4E;oBAC5E,gEAAgE;oBAChE,YAAY,GAAG,EAAE,CAAC;oBAClB,aAAa,GAAG,CAAC,CAAC;gBACpB,CAAC;YAEH,CAAC;iBAAM,CAAC;gBACN,mCAAmC;gBACnC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3B,aAAa,GAAG,mBAAmB,CAAC;YACtC,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC5C,CAAC;QAED,+EAA+E;QAC/E,iFAAiF;QACjF,2EAA2E;QAC3E,oGAAoG;QACpG,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAClC,OAAO,CAAC,IAAI,CAAC,oFAAoF,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,SAAS,6BAA6B,CAAC,CAAC;gBAChK,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACtD,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;iBAAM,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,8BAA8B;gBAC3D,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAGD,OAAO,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,qCAAqC;IAC3E,CAAC;IAGD;;;;;SAKK;IACL,KAAK,CAAC,SAAS,CAAC,IAAY;QAC1B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,6EAA6E;QAC7E,4EAA4E;QAC5E,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAExE,oGAAoG;QACpG,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAEvD,OAAO,WAAW,CAAC;IACrB,CAAC;CACF;AAzMD,wEAyMC;AAGD,oBAAoB;AAEpB;;;GAGG;AACH,MAAa,QAAQ;IACnB,EAAE,CAAU,CAAC,2BAA2B;IACxC,WAAW,CAAS,CAAC,oBAAoB;IACzC,QAAQ,CAAW,CAAC,sBAAsB;IAE1C,YAAY,MAAiE;QAC3E,IAAI,OAAO,MAAM,CAAC,WAAW,KAAK,QAAQ;YAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACrG,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACtC,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;IACtB,CAAC;CACF;AAXD,4BAWC;AAED,kBAAkB;AAElB;;GAEG;AACH,MAAa,kBAAkB;IAC7B,2DAA2D;IACnD,QAAQ,CAAQ,CAAC,2BAA2B;IAEpD,YAAY,QAAe;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,0BAA0B;IACtD,CAAC;IAED;;;SAGK;IACL,MAAM,CAAC,YAAY,CAAC,QAAe;QACjC,sEAAsE;QACtE,kFAAkF;QAClF,OAAO,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,cAAc,CAAC,MAA2B;QAC9C,gFAAgF;QAChF,kFAAkF;QAClF,MAAM,iBAAiB,GAAkB,EAAE,CAAC;QAC5C,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzC,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACrC,6DAA6D;gBAC7D,IAAI,OAAO,GAAG,YAAY,CAAC;gBAC3B,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;oBACzB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC7D,CAAC;gBACD,iBAAiB,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,mCAAmC;YACzF,CAAC;iBAAM,IAAI,YAAY,YAAY,mBAAmB,EAAE,CAAC;gBACvD,mDAAmD;gBACnD,MAAM,gBAAgB,GAAG,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC5D,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBACpC,iBAAiB,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,WAAW,CAAC,CAAC,CAAC,CAAC,iCAAiC;gBACtH,CAAC;qBAAM,IAAI,gBAAgB,EAAE,CAAC;oBAC5B,OAAO,CAAC,IAAI,CAAC,iCAAiC,YAAY,CAAC,aAAa,yCAAyC,CAAC,CAAC;gBACrH,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;gBAC9C,6DAA6D;gBAC7D,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC3D,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;wBAClC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACjC,CAAC;yBAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;wBACtC,iBAAiB,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;oBACpD,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;oBAC5F,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,2DAA2D,EAAE,KAAK,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrE,kEAAkE;YAClE,iBAAiB,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,+BAA+B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7G,CAAC;aAAM,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1C,wCAAwC;YACxC,iBAAiB,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;SAEK;IACL,IAAI,CAAC,OAAY;QACf,mEAAmE;QACnE,uEAAuE;QACvE,sEAAsE;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;SAIK;IACL,IAAI,CAAC,MAAW;QACd,oFAAoF;QACpF,OAAO;YACL,MAAM,EAAE,KAAK,EAAE,KAA0B,EAAE,OAAa,EAAE,EAAE;gBAC1D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAClD,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC1C,CAAC;SACF,CAAC;IACJ,CAAC;CACF;AAhGD,gDAgGC;AAED;;;GAGG;AACH,MAAa,mBAAmB;IAC9B,aAAa,CAAS,CAAC,gDAAgD;IAEvE,YAAY,aAAqB;QAC/B,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACtI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,+EAA+E;IACjF,CAAC;CACF;AARD,kDAQC;AAED,oBAAoB;AAEpB;;;GAGG;AACH,MAAa,cAAc;IACzB,gEAAgE;IAChE,SAAS,CAAO,CAAC,kFAAkF;IACnG,IAAI,CAAY;IAChB,QAAQ,CAAY;IACpB,YAAY,CAAuB;IACnC,cAAc,CAAU;IACxB,cAAc,CAAU;IACxB,OAAO,CAAU;IAEjB,YAAY,OAAY;QACtB,6CAA6C;QAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/B,CAAC;CACF;AAdD,wCAcC;AAED,0BAA0B;AAE1B;;;GAGG;AACH,MAAsB,iBAAiB;CAQtC;AARD,8CAQC;AAED;;;GAGG;AACH,MAAa,aAAc,SAAQ,iBAAiB;IAClD,6FAA6F;IAC7F,KAAK,CAAC,MAAM,CAAC,KAA6B,EAAE,OAA4D;QACtG,+EAA+E;QAC/E,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YACtC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAChH,CAAC,CAAC,KAAK,CAAC;QAEV,kDAAkD;QAClD,MAAM,iBAAiB,GAAG,6BAA6B,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;QAE5H,wEAAwE;QACxE,IAAI,OAAO,EAAE,MAAM,EAAE,SAAS,YAAY,wBAAwB,EAAE,CAAC;YACnE,MAAM,KAAK,GAAG,IAAA,SAAM,GAAE,CAAC,CAAC,oBAAoB;YAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,2BAA2B;YACpI,MAAM,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAC3C,EAAE,IAAI,EAAE,uBAAuB,EAAE,EACjC,OAAO,EACP,EAAE,KAAK,EAAE,CACV,CAAC;YACF,sBAAsB;YACtB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YACtD,MAAM,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CACzC,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC,EAAE,EAChD,EAAE,KAAK,EAAE,CACV,CAAC,CAAC,mCAAmC;QACxC,CAAC;QAGD,OAAO,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAC;IAC1C,CAAC;CACF;AA/BD,sCA+BC;AAED,uBAAuB;AAEvB;;;;GAIG;AACH,MAAa,WAAW;IACtB,4DAA4D;IAC3C,cAAc,CAAS;IACvB,UAAU,CAAS;IAEpC,YAAY,UAA6D,EAAE;QACzE,wEAAwE;QACxE,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,QAAQ,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;QAC1C,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;YAC/C,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC,CAAC;IACL,CAAC;IAED;;;;;SAKK;IACL,KAAK,CAAC,IAAI,CACR,IAAiC,EACjC,GAAG,IAAO;QAEV,wEAAwE;QACxE,+CAA+C;QAC/C,kDAAkD;QAClD,qFAAqF;QACrF,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACvB,CAAC;CACF;AA/BD,kCA+BC;AAGD,4BAA4B;AAE5B;;;GAGG;AACH,MAAa,kBAAkB;IACrB,QAAQ,GAAkB,EAAE,CAAC;IAErC;;SAEK;IACL,KAAK,CAAC,UAAU,CAAC,OAAoB;QACnC,IAAI,CAAC,CAAC,OAAO,YAAY,WAAW,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;QAC5H,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,WAAW;QACf,uEAAuE;QACvE,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,cAAc,CAAC,OAAe;QAClC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,gBAAgB,CAAC,OAAe;QACpC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IAChD,CAAC;CACF;AA1CD,gDA0CC;AAED;;;GAGG;AACH,MAAsB,cAAc;IAElC,SAAS,CAAS,CAAC,6DAA6D;IAChF,QAAQ,CAAU,CAAC,yBAAyB;IAC5C,SAAS,CAAU,CAAC,0BAA0B;IAC9C,cAAc,CAAU,CAAC,oEAAoE;IAE7F,YAAY,OAAY;QACtB,+DAA+D;QAC/D,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,qBAAqB;QACtE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC,CAAC,8BAA8B;QAErF,qDAAqD;QACrD,8FAA8F;QAE9F,qIAAqI;IACvI,CAAC;CAoBF;AAtCD,wCAsCC;AAED;;;GAGG;AACH,MAAsB,UAAU;CAe/B;AAfD,gCAeC;AAED;;;GAGG;AACH,MAAa,YAAa,SAAQ,cAAc;IAC9C,WAAW,CAAqB;IAChC,CAAC,CAAS,CAAC,uDAAuD;IAElE,YAAY,UAA0G,EAAE;QACtH,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,qDAAqD;QACrD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,kBAAkB,EAAE,CAAC;QACnE,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,kCAAkC;QAClE,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC5E,4EAA4E;IAC9E,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,mBAAmB,CAAC,CAAuB;QAC/C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;QAE9D,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,8BAA8B;QACrE,CAAC;aAAM,CAAC;YACN,oEAAoE;YACpE,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,WAAW,CAAC,MAA2B,EAAE,OAA4B;QACzE,gEAAgE;QAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5D,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,gBAAgB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5C,mCAAmC;QACnC,IAAI,mBAAmB,EAAE,CAAC;YACxB,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;YAC7D,CAAC;iBAAM,IAAI,mBAAmB,YAAY,WAAW,EAAE,CAAC;gBACtD,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,gCAAgC,QAAQ,mCAAmC,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC5D,CAAC;iBAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE,CAAC;gBACnD,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,iCAAiC,SAAS,mCAAmC,CAAC,CAAC;YAC9F,CAAC;QACH,CAAC;QAED,kDAAkD;IACpD,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACjC,CAAC;CACF;AAvED,oCAuEC;AAED;;;;GAIG;AACH,MAAa,yBAA0B,SAAQ,cAAc;IAC3D,WAAW,CAAqB;IAChC,GAAG,CAAoB,CAAC,0DAA0D;IAClF,OAAO,GAAG,EAAE,CAAC,CAAC,6BAA6B;IAE3C,YAAY,OAAmH;QAC7H,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,CAAC,GAAG;YAAE,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAC7F,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACvB,qDAAqD;QACrD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,kBAAkB,EAAE,CAAC;QACnE,2EAA2E;IAC7E,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,mBAAmB,CAAC,CAAuB;QAC/C,yCAAyC;QACzC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;IAClD,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,WAAW,CAAC,MAA2B,EAAE,OAA4B;QACzE,iCAAiC;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5D,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,UAAU,IAAI,UAAU,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;YACvH,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,QAAQ;gBAAE,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAC7F,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,WAAW;gBAAE,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxG,CAAC;QACD,IAAI,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,UAAU,IAAI,OAAO,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;YAC1H,IAAI,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;gBAAE,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;iBACnG,IAAI,OAAO,CAAC,SAAS,CAAC,YAAY,WAAW;gBAAE,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;QAC5G,CAAC;QAED,qEAAqE;QACrE,kEAAkE;QAClE,6EAA6E;QAC7E,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,0CAA0C,UAAU,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;YAC9I,2EAA2E;QAC7E,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,CAAC;CACF;AA7DD,8DA6DC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,cAAc;IAC5D,WAAW,CAAqB;IAChC,kFAAkF;IAClF,oBAAoB,CAAoE;IACxF,SAAS,CAAS,CAAC,qDAAqD;IAExE,YAAY,MAIX;QACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,8DAA8D;QAC7E,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,OAAO,MAAM,CAAC,oBAAoB,CAAC,oBAAoB,KAAK,UAAU,EAAE,CAAC;YAC3G,MAAM,IAAI,KAAK,CAAC,8GAA8G,CAAC,CAAC;QAClI,CAAC;QACD,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,iCAAiC;QAEjF,kFAAkF;QAClF,4EAA4E;QAC5E,gFAAgF;QAChF,2FAA2F;QAC3F,4EAA4E;QAC5E,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,IAAI,kBAAkB,EAAE,CAAC;QAClE,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,iHAAiH,CAAC,CAAC;QAClI,CAAC;QACD,4EAA4E;IAC9E,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,mBAAmB,CAAC,MAA2B;QACnD,yCAAyC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAC9F,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE/B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,sFAAsF,QAAQ,6BAA6B,CAAC,CAAC;YAC1I,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC;QAClC,CAAC;QAED,+CAA+C;QAC/C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAEjF,gDAAgD;QAChD,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEtE,oDAAoD;QACpD,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC;IACvC,CAAC;IAED;;;;;;SAMK;IACL,KAAK,CAAC,WAAW,CAAC,CAAsB,EAAE,EAAuB;QAC/D,2FAA2F;QAC3F,mDAAmD;QACnD,qGAAqG;QACrG,qDAAqD;QACrD,eAAM,CAAC,KAAK,CAAC,iFAAiF,CAAC,CAAC;IAClG,CAAC;IAED;;;;;SAKK;IACL,KAAK,CAAC,KAAK;QACT,oDAAoD;QACpD,0FAA0F;IAC5F,CAAC;CACF;AA9ED,gEA8EC;AAGD;;;;GAIG;AACH,MAAa,iBAAkB,SAAQ,qBAAqB;IAClD,UAAU,GAAsB,IAAI,CAAC,CAAC,yCAAyC;IAEvF,YAAY,UAAuB;QACjC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC;QACrC,mEAAmE;IACrE,CAAC;IAED;;;SAGK;IACL,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAe,EAAE,SAAqB,EAAE,UAAsB;QACnF,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACnF,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAEhH,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,QAAQ,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjG,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;SAGK;IACL,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,IAAgB,EAAE,UAAsB;QACjE,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACvF,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAGD;;;;SAIK;IACL,KAAK,CAAC,YAAY,CAAC,SAAqB;QACtC,MAAM,KAAK,GAAsB,EAAE,CAAC;QACpC,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,kBAAkB,GAAa,EAAE,CAAC,CAAC,+CAA+C;QAExF,4DAA4D;QAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,IAAA,SAAM,GAAE,CAAC;YAC1E,IAAI,MAAM,GAAG,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,4CAA4C;YAElF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClD,2BAA2B;gBAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;oBACrB,MAAM,IAAI,KAAK,CAAC,qBAAqB,EAAE,mFAAmF,CAAC,CAAC;gBAC9H,CAAC;gBACD,IAAI,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC/E,MAAM,IAAI,KAAK,CAAC,qBAAqB,EAAE,6DAA6D,CAAC,CAAC;gBACxG,CAAC;gBACD,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACnC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACpD,MAAM,GAAG,EAAE,CAAC,CAAC,8CAA8C;YAC7D,CAAC;YAED,uCAAuC;YACvC,KAAK,CAAC,IAAI,CAAC;gBACT,EAAE,EAAE,EAAE;gBACN,MAAM,EAAE,MAAM,EAAE,2CAA2C;gBAC3D,QAAQ,EAAE,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,EAAE,2BAA2B;gBAC1D,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,8BAA8B;aAC7D,CAAC,CAAC;QACL,CAAC;QAED,+BAA+B;QAC/B,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/C,4DAA4D;YAC5D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACnE,yDAAyD;YACzD,kBAAkB,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,UAAU,EAAE,EAAE;gBACvD,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,KAAK,CAAC,+DAA+D,aAAa,sBAAsB,CAAC,CAAC;oBAClH,OAAO,CAAC,+BAA+B;gBACzC,CAAC;gBACD,KAAK,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;gBAClD,iGAAiG;gBACjG,iEAAiE;YACnE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,wDAAwD;QACxD,MAAM,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;;SAOK;IACL,KAAK,CAAC,gBAAgB,CAAC,KAAwB,EAAE,CAAS,EAAE,MAA4B;QACtF,IAAI,cAAwB,CAAC;QAC7B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;YAC3F,CAAC;YACD,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,cAAc,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,+FAA+F;QAC/F,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAExE,uDAAuD;QACvD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;SAOK;IACL,KAAK,CAAC,yBAAyB,CAAC,KAAwB,EAAE,CAAS,EAAE,MAA4B;QAC/F,IAAI,cAAwB,CAAC;QAC7B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;YAC3F,CAAC;YACD,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,cAAc,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,+FAA+F;QAC/F,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAExE,yEAAyE;QACzE,MAAM,YAAY,GAAyB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5D,uEAAuE;YACvE,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,EAAE,EAAE,IAAI,CAAC,EAAE;aACZ,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;;SAKK;IACL,WAAW,CAAC,CAAU;QACpB,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC;QAElE,mEAAmE;QACnE,OAAO;YACL,oBAAoB,EAAE,KAAK,EAAE,KAAa,EAAuB,EAAE;gBACjE,kDAAkD;gBAClD,2DAA2D;gBAC3D,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAClD,CAAC;SACF,CAAC;IACJ,CAAC;CACF;AAjLD,8CAiLC;AAUD;;GAEG;AACH,IAAY,iBAoBX;AApBD,WAAY,iBAAiB;IAC3B,4CAAuB,CAAA;IACvB,oDAA+B,CAAA;IAC/B,wCAAmB,CAAA;IACnB,4CAAuB,CAAA;IACvB,gDAA2B,CAAA;IAC3B,4CAAuB,CAAA;IACvB,gDAA2B,CAAA;IAC3B,8CAAyB,CAAA;IACzB,0CAAqB,CAAA;IACrB,8CAAyB,CAAA;IACzB,kDAA6B,CAAA;IAC7B,kDAA6B,CAAA;IAC7B,wDAAmC,CAAA;IACnC,oDAA+B,CAAA;IAC/B,wDAAmC,CAAA;IACnC,kDAA6B,CAAA;IAC7B,8CAAyB,CAAA;IACzB,kDAA6B,CAAA;IAC7B,sCAAiB,CAAA;AACnB,CAAC,EApBW,iBAAiB,iCAAjB,iBAAiB,QAoB5B;AAED,8DAA8D;AAC9D,MAAM,2BAA2B,GAAG,GAAwB,EAAE;IAC5D,OAAO;QACL,iBAAiB,CAAC,SAAS;QAC3B,iBAAiB,CAAC,aAAa;QAC/B,iBAAiB,CAAC,OAAO;QACzB,iBAAiB,CAAC,SAAS;QAC3B,iBAAiB,CAAC,WAAW;QAC7B,iBAAiB,CAAC,SAAS;QAC3B,iBAAiB,CAAC,WAAW;QAC7B,iBAAiB,CAAC,UAAU;QAC5B,iBAAiB,CAAC,QAAQ;QAC1B,iBAAiB,CAAC,UAAU;QAC5B,iBAAiB,CAAC,YAAY;QAC9B,iBAAiB,CAAC,YAAY;QAC9B,iBAAiB,CAAC,eAAe;QACjC,iBAAiB,CAAC,aAAa;QAC/B,iBAAiB,CAAC,eAAe;QACjC,iBAAiB,CAAC,YAAY;QAC9B,iBAAiB,CAAC,UAAU;QAC5B,iBAAiB,CAAC,YAAY;QAC9B,iBAAiB,CAAC,MAAM;KACzB,CAAC;AACJ,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAa,wBAAwB;IAC3B,QAAQ,GAAmC,IAAI,GAAG,EAAE,CAAC,CAAC,+BAA+B;IAC5E,KAAK,CAAS;IACd,WAAW,CAAU;IACrB,QAAQ,CAAsB;IACvC,SAAS,GAAG,KAAK,CAAC;IAE1B;;;;;;SAMK;IACL,YAAY,KAAa,EAAE,WAAoB,EAAE,WAAgC,EAAE,EAAE,OAAO,GAAG,KAAK;QAClG,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QACtE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QAEzB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,eAAM,CAAC,KAAK,CAAC,2CAA2C,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,kBAAkB,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxH,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACL,UAAU,CAAC,SAAiB,EAAE,OAAwB;QACpD,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5D,eAAM,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;YAClC,eAAM,CAAC,IAAI,CAAC,qEAAqE,SAAS,IAAI,CAAC,CAAC;YAChG,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE5C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,eAAM,CAAC,KAAK,CAAC,6CAA6C,SAAS,IAAI,CAAC,CAAC;QAC3E,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;SAKK;IACL,aAAa,CAAC,SAAiB,EAAE,OAAwB;QACvD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC;YAAE,OAAO,IAAI,CAAC;QAE/C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAE3B,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC1B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,eAAM,CAAC,KAAK,CAAC,+CAA+C,SAAS,IAAI,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;SAIK;IACL,aAAa,CAAC,SAAiB;QAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,eAAM,CAAC,KAAK,CAAC,oDAAoD,SAAS,IAAI,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;SAGK;IACL,gBAAgB;QACd,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,eAAM,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;QACxE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;SAKK;IACL,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,GAAG,IAAW;QACjD,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,eAAM,CAAC,KAAK,CAAC,4BAA4B,aAAa,CAAC,MAAM,wBAAwB,SAAS,GAAG,CAAC,CAAC;YACrG,CAAC;YAED,oCAAoC;YACpC,MAAM,gBAAgB,GAAG,CAAC,GAAG,IAAI,EAAE;oBACjC,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC;YAEH,gFAAgF;YAChF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;gBAC3E,IAAI,CAAC;oBACH,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;gBAC7D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,SAAS,mBAAmB,EAAE,KAAK,CAAC,CAAC;oBACjF,MAAM,KAAK,CAAC,CAAC,sCAAsC;gBACrD,CAAC;YACH,CAAC,CAAC,CAAC,CAAC;YAEJ,4BAA4B;YAC5B,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;YACxE,IAAI,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACtC,eAAM,CAAC,IAAI,CAAC,oBAAoB,WAAW,OAAO,OAAO,CAAC,MAAM,+BAA+B,SAAS,GAAG,CAAC,CAAC;YAC/G,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1B,eAAM,CAAC,KAAK,CAAC,sDAAsD,SAAS,IAAI,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACL,WAAW,CAAC,UAAkB,EAAE,WAAgC,EAAE;QAChE,OAAO,IAAI,wBAAwB,CACjC,UAAU,EACV,IAAI,CAAC,KAAK,EAAE,mCAAmC;QAC/C,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,QAAQ,EAAE,EAAE,iBAAiB;QACpD,IAAI,CAAC,SAAS,CACf,CAAC;IACJ,CAAC;IAED;;;;SAIK;IACL,UAAU,CAAC,OAAgB;QACzB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,+CAA+C;IAE/C;;;;;SAKK;IACL,KAAK,CAAC,cAAc,CAClB,GAA2E,EAC3E,OAAiB,EACjB,UAA+B,EAAE;QAEjC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7E,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,UAA+B,EAAE;QACtE,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC1E,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,YAAY,CAAC,MAAW,EAAE,UAA+B,EAAE;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACrE,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,cAAc,CAAC,KAAsB,EAAE,UAA+B,EAAE;QAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;;;;SAKK;IACL,KAAK,CAAC,gBAAgB,CACpB,KAAyD,EACzD,MAA2B,EAC3B,UAA+B,EAAE;QAEjC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAChF,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,cAAc,CAAC,OAA4B,EAAE,UAA+B,EAAE;QAClF,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,gBAAgB,CAAC,KAAsB,EAAE,UAA+B,EAAE;QAC9E,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;;;;SAKK;IACL,KAAK,CAAC,eAAe,CACnB,IAA+D,EAC/D,KAAU,EACV,UAA+B,EAAE;QAEjC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7E,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,aAAa,CAAC,MAAW,EAAE,UAA+B,EAAE;QAChE,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,eAAe,CAAC,KAAsB,EAAE,UAA+B,EAAE;QAC7E,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACvE,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,iBAAiB,CACrB,MAAyE,EACzE,UAA+B,EAAE;QAEjC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC1E,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,iBAAiB,CACrB,MAA8D,EAC9D,UAA+B,EAAE;QAEjC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;SAKK;IACL,KAAK,CAAC,oBAAoB,CACxB,SAA6D,EAC7D,KAAa,EACb,UAA+B,EAAE;QAEjC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACvF,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,kBAAkB,CAAC,SAAgB,EAAE,UAA+B,EAAE;QAC1E,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC9E,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,oBAAoB,CAAC,KAAsB,EAAE,UAA+B,EAAE;QAClF,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;SAKK;IACL,KAAK,CAAC,iBAAiB,CACrB,MAA0D,EAC1D,SAAiB,EACjB,UAA+B,EAAE;QAEjC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACrF,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,eAAe,CAAC,MAAW,EAAE,UAA+B,EAAE;QAClE,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,iBAAiB,CAAC,KAAsB,EAAE,UAA+B,EAAE;QAC/E,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED;;;;SAIK;IACL,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,IAAS;QAClD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IACjE,CAAC;CACF;AApXD,4DAoXC", "sourcesContent": ["// Polyfill for @codessa/core functionality used in this project.\n\nexport const START = '__start__';\nexport const END = '__end__';\n\nexport interface StateGraphArgs<T> {\n  initialState?: T;\n  states?: string[];\n  channels?: any;\n}\n\n// Minimal stub for compatibility with code that expects a StateGraph with this API.\n// Use the advanced implementation below for actual workflows.\nexport class StubStateGraph<T> {\n  public nodes: Record<string, any> = {};\n  public channels: any = undefined;\n  private entryPoint?: string;\n  private compiled = false;\n  constructor(public args: StateGraphArgs<T>) {\n    this.channels = args.channels;\n  }\n  addNode(name: string, handler: any) { this.nodes[name] = handler; return this; }\n  addEdge(from: string, to: string) { /* stub */ return this; }\n  addConditionalEdges(...args: any[]) { /* stub */ return this; }\n  setStart(start: string) { this.entryPoint = start; return this; }\n  setEnd(end: string) { /* stub */ return this; }\n  setEntryPoint(entry: string) { this.entryPoint = entry; return this; }\n  compile() { this.compiled = true; return this; }\n  build() { return this; }\n  async run(input: T): Promise<T> { return input; }\n  async invoke(input: T, config?: any): Promise<T> { return input; }\n}\n\n/**\n * Defines a reducer function for a channel.\n */\ntype ReducerFn<T> = (currentState: T, updateValue: T) => T;\n/**\n * Defines a default factory for a channel.\n */\ntype DefaultFn<T> = () => T;\n\nexport interface AnnotationOptions<T> {\n  reducer?: ReducerFn<T>;\n  default?: DefaultFn<T>;\n}\n\nexport interface AnnotationInstance<T> {\n  __isAnnotation: true;\n  reducer?: ReducerFn<T>;\n  default?: DefaultFn<T>;\n  type?: any; // For TS type extraction\n  spec: Record<string, AnnotationInstance<any>>; // for merging\n}\n\n/**\n * Defines a single channel annotation.\n * @example\n *   messages: Annotation<BaseMessage[]>({ reducer, default })\n */\nexport function Annotation<T>(options?: AnnotationOptions<T>): AnnotationInstance<T> {\n  // .spec is itself for merging convenience\n  const inst: AnnotationInstance<T> = {\n    __isAnnotation: true,\n    ...(options || {}),\n    spec: {} // will be set by Root\n  };\n  return inst;\n}\n\n\n/**\n * Root annotation for defining state schemas.\n * Supports merging (via .spec) and exposes .State for type extraction.\n * @example\n *   const Ann = Annotation.Root({\n *     messages: Annotation<BaseMessage[]>({ reducer, default }),\n *     ...OtherAnn.spec // for merging\n *   })\n *   type State = typeof Ann.State;\n */\ntype AnnotationSchema = Record<string, AnnotationInstance<any>>;\n\nexport namespace Annotation {\n  export function Root<TSchema extends AnnotationSchema>(schema: TSchema) {\n    // Attach .spec to each annotation for merging\n    Object.entries(schema).forEach(([k, v]) => {\n      if (v && typeof v === 'object') (v as any).spec = { [k]: v };\n    });\n    const result = { schema } as any;\n    // TypeScript: Expose the type as State\n    type StateType = {\n      [K in keyof TSchema]: TSchema[K] extends AnnotationInstance<infer U> ? U : never;\n    };\n    // .spec for merging\n    (result as any).spec = schema;\n    // .State for type extraction\n    (result as any).State = null as unknown as StateType;\n    return result as { schema: TSchema; spec: TSchema; State: StateType };\n  }\n}\n\n// === End Codessa Annotation Polyfill (Upgraded) ===\n\n// --- Zod Integration Polyfill ---\n// Supports using Zod schemas for runtime validation and channel definition.\n// Real Codessa extends Zod with .codessa.reducer and .default; this polyfill simulates that.\nimport type { ZodType, ZodSchema, ZodObject, ZodTypeAny } from 'zod';\nlet zod: any = undefined;\ntry { zod = require('zod'); } catch { /* fallback for environments without zod */ }\n\n/**\n * Polyfill for Zod integration with Codessa channel/reducer API.\n * Usage: z.object({ ... }).codessa.reducer(fn, inputSchema).default(fn)\n */\nif (zod && zod.ZodObject) {\n  // Patch Zod prototype for .codessa.reducer\n  if (!zod.ZodType.prototype.codessa) {\n    Object.defineProperty(zod.ZodType.prototype, 'codessa', {\n      value: {\n        reducer(fn: Function, inputSchema?: ZodTypeAny) {\n          // Attach reducer as metadata\n          (this as any)._codessaReducer = fn;\n          if (inputSchema) (this as any)._codessaInputSchema = inputSchema;\n          return this;\n        }\n      },\n      writable: true,\n      configurable: true\n    });\n  }\n  // Patch .default if not present\n  if (!zod.ZodType.prototype.default) {\n    zod.ZodType.prototype.default = function (factory: Function) {\n      (this as any)._codessaDefault = factory;\n      return this;\n    };\n  }\n}\n\n/**\n * Minimal StateGraph polyfill for Codessa workflows.\n * Supports addNode, addEdge, addConditionalEdges, setEntryPoint, setFinishPoint, compile, invoke, input/output schemas, Zod validation, and streaming.\n * TypeScript-compatible and ergonomic.\n *\n * @example\n *   const ann = Annotation.Root({ ... });\n *   const graph = new StateGraph(ann)\n *     .addNode('foo', fn)\n *     .addEdge('a', 'b')\n *     .compile();\n *   await graph.invoke(input);\n */\n// Utility type guards for stricter type safety\nfunction isAnnotationInstance(obj: any): obj is AnnotationInstance<any> {\n  return !!obj && typeof obj === 'object' && obj.__isAnnotation === true;\n}\nfunction isZodObject(obj: any): obj is ZodObject<any> {\n  return zod && obj instanceof zod.ZodObject;\n}\n\ntype NodeFn<State> = (_state: any) => Promise<Partial<State>> | Partial<State>;\ninterface StateGraphNode<State> {\n  fn: NodeFn<State>;\n  options?: { input?: Partial<State> };\n}\ninterface StateGraphEdge<State> {\n  from: keyof State & string;\n  to: keyof State & string;\n  condition?: (_state: any) => boolean;\n}\n\nexport class StateGraph<StateSchema extends Record<string, any> = any> {\n  public nodes = {} as Record<keyof StateSchema & string, StateGraphNode<StateSchema>>;\n  public edges: Array<StateGraphEdge<StateSchema>> = [];\n  // entryPoint and finishPoint are always string (never null): enforce at compile time\n  public entryPoint!: keyof StateSchema & string;\n  public finishPoint!: keyof StateSchema & string;\n  public compiled = false;\n  public schema: Record<string, AnnotationInstance<any>> | undefined;\n  public inputSchema?: any;\n  public outputSchema?: any;\n  public zodSchema?: ZodObject<any>;\n\n  constructor(annotationOrChannels: any) {\n    // Accepts Annotation.Root, {input, output}, Zod, or plain channels\n    if (annotationOrChannels && annotationOrChannels.input && annotationOrChannels.output) {\n      this.inputSchema = annotationOrChannels.input;\n      this.outputSchema = annotationOrChannels.output;\n      this.schema = {\n        ...annotationOrChannels.input.schema,\n        ...annotationOrChannels.output.schema,\n      };\n    } else if (annotationOrChannels && annotationOrChannels.schema) {\n      this.schema = annotationOrChannels.schema;\n    } else if (zod && annotationOrChannels instanceof zod.ZodObject) {\n      this.zodSchema = annotationOrChannels;\n      this.schema = annotationOrChannels.shape;\n    } else {\n      this.schema = annotationOrChannels;\n    }\n  }\n\n  addNode(key: keyof StateSchema & string, fn: NodeFn<StateSchema>, options?: { input?: Partial<StateSchema> }) {\n    this.nodes[key] = { fn, options };\n    return this;\n  }\n\n  addEdge(from: keyof StateSchema & string, to: keyof StateSchema & string) {\n    this.edges.push({ from, to });\n    return this;\n  }\n\n  addConditionalEdges(from: keyof StateSchema & string, conditions: Array<{ condition: (_state: any) => boolean, to: keyof StateSchema & string }>) {\n    for (const cond of conditions) {\n      this.edges.push({ from, to: cond.to, condition: cond.condition });\n    }\n    return this;\n  }\n\n  setEntryPoint(key: keyof StateSchema & string) {\n    this.entryPoint = key;\n    return this;\n  }\n\n  setFinishPoint(key: keyof StateSchema & string) {\n    this.finishPoint = key;\n    return this;\n  }\n\n  compile() {\n    if (!this.entryPoint) throw new Error('Entry point must be set before compiling.');\n    this.compiled = true;\n    // Optionally, validate graph structure here\n    return this;\n  }\n\n  /**\n     * Streaming invoke: yields state after each node (simulated async streaming).\n     * Use for token-by-token or step-by-step streaming.\n     */\n  async *stream(input: Partial<StateSchema>): AsyncGenerator<StateSchema, StateSchema, unknown> {\n    if (!this.compiled) throw new Error('Graph must be compiled before invoking.');\n    let state: StateSchema = { ...(input as StateSchema) };\n    let current: keyof StateSchema & string = this.entryPoint;\n    const visited = new Set<keyof StateSchema & string>();\n    while (current && !visited.has(current)) {\n      visited.add(current);\n      const node = this.nodes[current];\n      if (!node) throw new Error(`Node '${current}' not found.`);\n      const nodeInput = node.options?.input ? { ...state, ...node.options.input } : state;\n      let output = await node.fn(nodeInput) as Partial<StateSchema>;\n      // Zod runtime validation of node output if possible\n      if (zod && this.zodSchema) {\n        try { output = this.zodSchema.parse(output) as Partial<StateSchema>; }\n        catch (e) { console.warn('Zod validation failed for node', current, e); }\n      }\n      state = { ...state, ...output } as StateSchema; // Type assertion fixes Partial<StateSchema> error\n      yield state;\n      // Find next node\n      let next: keyof StateSchema & string | undefined = undefined;\n      for (const edge of this.edges) {\n        if (edge.from === current) {\n          if (!edge.condition || edge.condition(state)) {\n            next = edge.to;\n            break;\n          }\n        }\n      }\n      current = next!;\n    }\n    return state;\n  }\n\n  /**\n     * Minimal synchronous invoke for demonstration. Real Codessa supports async, streaming, etc.\n     * This will just execute nodes in a simple topological order, passing state.\n     * Performs runtime validation using Zod or annotation defaults if available.\n     */\n  async invoke(input: Partial<StateSchema>) {\n    if (!this.compiled) throw new Error('Graph must be compiled before invoking.');\n    let state: StateSchema = { ...(input as StateSchema) };\n    let current: keyof StateSchema & string = this.entryPoint;\n    const visited = new Set<keyof StateSchema & string>();\n    while (current && !visited.has(current)) {\n      visited.add(current);\n      const node = this.nodes[current];\n      if (!node) throw new Error(`Node '${current}' not found.`);\n      const nodeInput = node.options?.input ? { ...state, ...node.options.input } : state;\n      let output = await node.fn(nodeInput) as Partial<StateSchema>;\n      // Zod runtime validation of node output if possible\n      if (zod && this.zodSchema) {\n        try { output = this.zodSchema.parse(output) as Partial<StateSchema>; }\n        catch (e) { console.warn('Zod validation failed for node', current, e); }\n      }\n      state = { ...state, ...output } as StateSchema; // Type assertion fixes Partial<StateSchema> error\n      // Optionally, runtime validation for annotation defaults\n      if (this.schema) {\n        for (const [key, channel] of Object.entries(this.schema)) {\n          if (isAnnotationInstance(channel) && channel.default && typeof state[key] === 'undefined') {\n            (state as any)[key] = channel.default();\n          }\n        }\n      }\n      // Find next node\n      let next: keyof StateSchema & string | undefined = undefined;\n      for (const edge of this.edges) {\n        if (edge.from === current) {\n          if (!edge.condition || edge.condition(state)) {\n            next = edge.to;\n            break;\n          }\n        }\n      }\n      current = next!;\n    }\n    return state;\n  }\n}\n\n// Usage example (see docs):\n// const ann = Annotation.Root({\n//   messages: Annotation<BaseMessage[]>({ reducer, default }),\n// });\n// const graph = new StateGraph(ann)\n//   .addNode('foo', fn)\n//   .addEdge('a', 'b')\n//   .compile();\n// await graph.invoke(input);\n// for await (const state of graph.stream(input)) { ... }\n\n// === End StateGraph Polyfill ===\n\n\nimport { v4 as uuidv4 } from 'uuid'; // Used for generating IDs\nimport * as crypto from 'crypto'; // Used for a more robust hashing in Embeddings\nimport { IChromaPolyfillInstance, ChromaGetResponse } from '../../memory/codessa/vectorStores/chromaVectorStore';\nimport { logger } from '../../logger';\n\n// === Basic Types ===\n\ntype Metadata = Record<string, any>;\n\n// === Tools ===\n// Polyfill BaseTool to match ITool interface\nexport class Tool {\n  public readonly id: string;\n  public readonly name: string;\n  public readonly description: string;\n  public readonly type: 'single-action' | 'multi-action';\n  public readonly category?: string;\n  public readonly singleActionSchema?: any; // For compatibility with ITool\n  public readonly actions?: Record<string, any>;\n\n  constructor(\n    name: string,\n    description: string,\n    options?: {\n      id?: string;\n      type?: 'single-action' | 'multi-action';\n      category?: string;\n      singleActionSchema?: any;\n      actions?: Record<string, any>;\n    }\n  ) {\n    if (typeof name !== 'string' || name.length === 0) throw new Error('Tool name is required.');\n    if (typeof description !== 'string' || description.length === 0) throw new Error('Tool description is required.');\n    this.id = options?.id ?? name.toLowerCase().replace(/\\s+/g, '_');\n    this.name = name;\n    this.description = description;\n    this.type = options?.type ?? 'single-action';\n    this.category = options?.category;\n    this.singleActionSchema = options?.singleActionSchema;\n    this.actions = options?.actions;\n  }\n\n  /**\n     * Executes the tool's logic. Must be implemented by subclasses.\n     * @param actionName - Name of the action (for multi-action tools)\n     * @param input - The input for the tool, type depends on the specific tool.\n     * @param context - AgentContext (optional)\n     * @returns The result of the tool's execution.\n     */\n  async execute(actionName: string | undefined, input: any, context?: any): Promise<any> {\n    // By default, call the single action\n    if (this.type === 'multi-action' && this.actions && actionName) {\n      const action = this.actions[actionName];\n      if (action && typeof action.execute === 'function') {\n        return action.execute(input, context);\n      } else {\n        return { success: false, error: `Unknown action: ${actionName}` };\n      }\n    } else {\n      // For single-action tools, subclasses should override this\n      throw new Error(`Tool '${this.name}' does not implement execute.`);\n    }\n  }\n\n  /**\n     * Optional: Provide a definition for LLMs (polyfill returns basic info)\n     */\n  getDefinitionForModel() {\n    return {\n      name: this.name,\n      description: this.description,\n      type: this.type,\n      schema: this.singleActionSchema,\n      actions: this.actions\n    };\n  }\n}\n\n// Alias for compatibility with code importing BaseTool\nexport { Tool as BaseTool };\n\n/**\n * Polyfill for structured tools, matching ITool and LangChain interfaces\n */\nexport class StructuredTool extends Tool {\n  public schema: any;\n  constructor(\n    name = 'structured_tool',\n    description = 'A tool with a structured input/output schema.',\n    schema?: any,\n    options?: {\n      id?: string;\n      type?: 'single-action' | 'multi-action';\n      category?: string;\n      actions?: Record<string, any>;\n    }\n  ) {\n    super(name, description, { ...options, singleActionSchema: schema });\n    this.schema = schema ?? {};\n  }\n  override async execute(actionName: string | undefined, input: any, context?: any): Promise<any> {\n    // Subclasses should override this\n    throw new Error(`StructuredTool '${this.name}' does not have an implemented execute method.`);\n  }\n  /**\n     * Default invoke method for StructuredTool.\n     * Allows workflows and wrappers to call invoke on any StructuredTool instance.\n     * Delegates to execute with undefined actionName.\n     */\n  async invoke(input: any, context?: any): Promise<any> {\n    return this.execute(undefined, input, context);\n  }\n}\n\n// === Messages ===\n\n/**\n * Base class for chat messages.\n */\nexport class BaseMessage {\n  readonly content: string;\n  readonly type: string; // Helps distinguish message types\n  readonly additional_kwargs: Record<string, any>; // For extra data\n\n  constructor(content: string, additional_kwargs: Record<string, any> = {}) {\n    if (typeof content !== 'string') throw new Error('Message content must be a string.');\n    this.content = content;\n    this.type = 'base'; // Default type, overridden by subclasses\n    this.additional_kwargs = additional_kwargs;\n  }\n}\n\n/**\n * Represents a message from the human user.\n */\nexport class HumanMessage extends BaseMessage {\n  override readonly type: string = 'human';\n  constructor(content: string, additional_kwargs: Record<string, any> = {}) {\n    super(content, additional_kwargs);\n  }\n}\n\n/**\n * Represents a message from the AI assistant.\n */\nexport class AIMessage extends BaseMessage {\n  override readonly type: string = 'ai';\n  constructor(content: string, additional_kwargs: Record<string, any> = {}) {\n    super(content, additional_kwargs);\n  }\n}\n\n/**\n * Represents a system message (e.g., instructions, context).\n */\nexport class ToolMessage extends BaseMessage {\n  override readonly type: string = 'tool';\n  toolName: string;\n  toolInput: any;\n  constructor(toolName: string, toolInput: any, content = '', additional_kwargs: Record<string, any> = {}) {\n    super(content, additional_kwargs);\n    this.toolName = toolName;\n    this.toolInput = toolInput;\n  }\n}\n\n/**\n * Represents a system message (e.g., instructions, context).\n */\nexport class SystemMessage extends BaseMessage {\n  override readonly type: string = 'system';\n  constructor(content: string, additional_kwargs: Record<string, any> = {}) {\n    super(content, additional_kwargs);\n  }\n}\n\n/**\n * Base Embeddings class polyfill.\n * Generates deterministic, fixed-dimensional vectors based on text content using hashing.\n * Note: These embeddings are NOT semantically meaningful like real model embeddings.\n */\nexport class Embeddings {\n  protected readonly dim: number;\n  protected readonly seed: string = 'polyfill_seed'; // Consistent seed for determinism\n\n  constructor(dim = 384) {\n    if (dim <= 0 || !Number.isInteger(dim)) {\n      throw new Error('Embedding dimension must be a positive integer.');\n    }\n    this.dim = dim;\n  }\n\n  /**\n     * Generates a deterministic vector from text using SHA256 hashing and projection.\n     * @param text The input text.\n     * @returns A promise resolving to a vector of numbers between -1 and 1.\n     */\n  protected async generateVector(text: string): Promise<number[]> {\n    if (typeof text !== 'string' || text.length === 0) {\n      // Return a zero vector for empty or invalid input\n      return Array(this.dim).fill(0);\n    }\n\n    // Use SHA256 for a better hash distribution\n    const hash = crypto.createHash('sha256').update(text + this.seed).digest();\n\n    const vector: number[] = [];\n    // Use the hash bytes to seed pseudo-random number generation for each dimension\n    for (let i = 0; i < this.dim; i++) {\n      // Create a dimension-specific seed from the hash\n      const dimensionSeedBuffer = Buffer.concat([hash, Buffer.from([i % hash.length])]); // Use modulo for smaller hash\n      const dimensionHash = crypto.createHash('sha256').update(dimensionSeedBuffer).digest();\n\n      // Convert the first 4 bytes of the dimension hash to a number between 0 and 1\n      const randomValue = dimensionHash.readUInt32BE(0) / 0xFFFFFFFF;\n\n      // Scale to [-1, 1]\n      vector.push(randomValue * 2 - 1);\n    }\n\n    // Normalize the vector (optional, but common for embeddings)\n    const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));\n    if (norm === 0) return vector; // Avoid division by zero\n    return vector.map(val => val / norm);\n  }\n\n  /**\n     * Embeds a single query text. Conforms to LangChain `embedQuery` signature.\n     * @param text The text to embed.\n     * @returns A promise resolving to the embedding vector.\n     */\n  async embedQuery(text: string): Promise<number[]> {\n    return this.generateVector(text);\n  }\n\n  /**\n     * Embeds multiple documents (strings). Conforms to LangChain `embedDocuments` signature.\n     * @param texts An array of text documents (strings).\n     * @returns A promise resolving to an array of embedding vectors.\n     */\n  async embedDocuments(texts: string[]): Promise<number[][]> {\n    // Basic implementation: embed one by one. Subclasses can optimize.\n    return Promise.all(texts.map(text => this.generateVector(text)));\n  }\n}\n\n/**\n * Polyfill simulating OpenAIEmbeddings.\n * Extends the base Embeddings, adding simulated batching and latency.\n */\nexport class OpenAIEmbeddings extends Embeddings {\n  private simulatedLatencyMs: number;\n  private batchSize: number; // Store batch size config\n\n  // AsyncCaller might be used internally in a real impl, but not strictly needed in this polyfill's logic\n  caller?: AsyncCaller;\n\n  constructor(options: {\n    openAIApiKey?: string;\n    modelName?: string;\n    batchSize?: number;\n    simulatedLatencyMs?: number;\n    dimensions?: number; // Use 'dimensions' for compatibility with some interfaces\n    configuration?: any; // Placeholder for other config\n    client?: any; // Placeholder for custom client\n  } = {}) {\n    // Use 'dimensions' if provided, fallback to 'dim', then default\n    const dim = options.dimensions ?? (options as any).dim ?? 1536; // Default OpenAI dimension is 1536\n    super(dim);\n\n    this.simulatedLatencyMs = options.simulatedLatencyMs ?? 50; // Default 50ms latency\n    this.batchSize = options.batchSize ?? 32; // Default batch size\n    // Note: openAIApiKey, modelName, configuration, client are ignored in this polyfill's logic\n    // but included for constructor signature compatibility.\n    // If we were using AsyncCaller, we might instantiate it here:\n    // this.caller = new AsyncCaller({});\n  }\n\n  /**\n     * Simulates waiting for a specified duration.\n     */\n  private async simulateLatency(): Promise<void> {\n    if (this.simulatedLatencyMs > 0) {\n      await new Promise(resolve => setTimeout(resolve, this.simulatedLatencyMs));\n    }\n  }\n\n  /**\n     * Embeds a single query, simulating latency. Conforms to LangChain `embedQuery` signature.\n     */\n  override async embedQuery(text: string): Promise<number[]> {\n    await this.simulateLatency();\n    return super.generateVector(text); // Use parent's generation logic\n  }\n\n  /**\n     * Embeds documents (strings) in batches, simulating latency for each batch.\n     * Conforms to LangChain `embedDocuments` signature (takes string[]).\n     * @param texts An array of text documents (strings).\n     * @returns A promise resolving to an array of embedding vectors.\n     */\n  override async embedDocuments(texts: string[]): Promise<number[][]> {\n    const results: number[][] = [];\n    for (let i = 0; i < texts.length; i += this.batchSize) {\n      const batchTexts = texts.slice(i, i + this.batchSize);\n      await this.simulateLatency(); // Simulate latency per batch\n      // Embed batch items concurrently using the parent's logic\n      const batchEmbeddings = await Promise.all(batchTexts.map(text => super.generateVector(text)));\n      results.push(...batchEmbeddings);\n    }\n    return results;\n  }\n}\n\n// === Vector Stores ===\n\n// --- Interface Helper ---\ninterface VectorStoreItem {\n  id: string;\n  vector: number[];\n  metadata: Metadata;\n  pageContent: string; // Store original text for similarity search results\n}\n\nexport class SimilaritySearchResultItem {\n  id: string;\n  score: number; // Higher is better (similarity)\n  metadata: Metadata; // Include metadata in search results\n  pageContent: string;\n\n  constructor(id: string, score: number, metadata: Metadata, pageContent: string) {\n    this.id = id;\n    this.score = score;\n    this.metadata = metadata;\n    this.pageContent = pageContent;\n  }\n}\n\n/**\n * Base class for in-memory vector store polyfills.\n * Provides common functionality like cosine similarity and basic storage.\n */\nabstract class BaseMemoryVectorStore {\n  protected store: Map<string, VectorStoreItem> = new Map();\n  protected dimension: number | null = null; // Track expected dimension\n\n  protected validateVector(vector: number[], expectedDim?: number | null): void {\n    if (!Array.isArray(vector) || vector.length === 0 || vector.some(isNaN)) { // Check for NaN\n      throw new Error('Vector must be a non-empty array of finite numbers.');\n    }\n    const dim = expectedDim ?? this.dimension;\n    if (dim !== null && vector.length !== dim) {\n      throw new Error(`Vector dimension mismatch. Expected ${dim}, got ${vector.length}.`);\n    }\n    if (this.dimension === null) {\n      this.dimension = vector.length; // Set dimension based on first vector added\n    }\n  }\n\n  protected cosineSimilarity(vecA: number[], vecB: number[]): number {\n    if (vecA.length !== vecB.length) {\n      // Should ideally be caught by validateVector, but double-check\n      console.warn(`Cosine similarity dimension mismatch: ${vecA.length} vs ${vecB.length}`);\n      return 0; // Or handle as appropriate (e.g., NaN, error)\n    }\n    let dotProduct = 0;\n    let normA = 0;\n    let normB = 0;\n    for (let i = 0; i < vecA.length; i++) {\n      dotProduct += vecA[i] * vecB[i];\n      normA += vecA[i] * vecA[i];\n      normB += vecB[i] * vecB[i];\n    }\n    const magnitudeA = Math.sqrt(normA);\n    const magnitudeB = Math.sqrt(normB);\n\n    if (magnitudeA === 0 || magnitudeB === 0) {\n      return 0; // Cosine similarity of a zero vector is undefined, return 0\n    }\n    return dotProduct / (magnitudeA * magnitudeB);\n  }\n\n  /**\n     * Add or update vectors with associated text content and metadata.\n     * @param items Array of items containing id, vector, pageContent, and metadata.\n     */\n  async addVectors(items: VectorStoreItem[]): Promise<void> {\n    for (const item of items) {\n      this.validateVector(item.vector); // Validate against store's dimension\n      if (typeof item.pageContent !== 'string') throw new Error('VectorStoreItem must include string pageContent.');\n      this.store.set(item.id, { ...item }); // Store a copy including pageContent and metadata\n    }\n  }\n\n  /**\n     * Retrieve vector items by IDs.\n     * @param ids Array of vector IDs.\n     * @returns A Map of ID to VectorStoreItem for found items.\n     */\n  async getVectorsByIds(ids: string[]): Promise<Map<string, VectorStoreItem>> {\n    const results = new Map<string, VectorStoreItem>();\n    for (const id of ids) {\n      const item = this.store.get(id);\n      if (item) {\n        results.set(id, item);\n      }\n    }\n    return results;\n  }\n\n  /**\n     * Delete vectors by IDs.\n     * @param ids Array of vector IDs.\n     * @returns An array of IDs that were successfully deleted.\n     */\n  async deleteVectors(ids: string[]): Promise<string[]> {\n    const deletedIds: string[] = [];\n    for (const id of ids) {\n      if (this.store.delete(id)) {\n        deletedIds.push(id);\n      }\n    }\n    // console.debug(`Deleted ${deletedIds.length} vectors out of ${ids.length} requested.`);\n    if (this.store.size === 0) {\n      this.dimension = null; // Reset dimension if store becomes empty\n    }\n    return deletedIds; // Return IDs actually deleted\n  }\n\n  /**\n     * Remove all vectors from the store.\n     */\n  async clear(): Promise<void> {\n    this.store.clear();\n    this.dimension = null; // Reset dimension\n    // console.debug(\"Cleared all vectors from memory store.\");\n  }\n\n  /**\n     * Find the top-K most similar vectors to a query vector using cosine similarity.\n     * @param queryVector The vector to search against.\n     * @param k The number of results to return.\n     * @param filter Optional function or object to filter items before scoring.\n     *               If a function, it receives VectorStoreItem and returns boolean.\n     *               If an object, it's treated as a metadata exact-match filter.\n     * @returns An array of search results with IDs, scores, and metadata/content.\n     */\n  async similaritySearch(\n    queryVector: number[],\n    k: number,\n    filter?: ((item: VectorStoreItem) => boolean) | Record<string, any>\n  ): Promise<SimilaritySearchResultItem[]> {\n    this.validateVector(queryVector, this.dimension); // Validate query vector dimension\n\n    let candidates = Array.from(this.store.values());\n\n    if (filter) {\n      if (typeof filter === 'function') {\n        candidates = candidates.filter((item) => filter(item));\n      } else if (typeof filter === 'object') {\n        candidates = candidates.filter(item =>\n          Object.entries(filter).every(([key, value]) =>\n            item.metadata && item.metadata[key] === value\n          )\n        );\n      }\n    }\n\n    if (candidates.length === 0) {\n      return [];\n    }\n\n    const scoredResults = candidates.map(item => ({\n      id: item.id,\n      score: this.cosineSimilarity(queryVector, item.vector),\n      metadata: item.metadata,\n      pageContent: item.pageContent,\n    }));\n\n    // Sort by score descending (higher score is more similar)\n    scoredResults.sort((a, b) => b.score - a.score);\n\n    // Return top k results\n    return scoredResults.slice(0, k);\n  }\n}\n\n\n/**\n * PineconeStore Polyfill - In-memory simulation using BaseMemoryVectorStore.\n */\nexport class PineconeStore extends BaseMemoryVectorStore {\n  private embeddings: Embeddings | null = null; // Store embeddings if used for LangChain compatibility\n  private namespace: string | undefined; // Simulate namespace\n\n  // Simulate static factory method (returns an instance)\n  // Conforms to LangChain's PineconeStore.fromExistingIndex signature (or similar)\n  static async fromExistingIndex(embeddings: Embeddings, _config: { namespace?: string }): Promise<PineconeStore> {\n    // In a real scenario, config.pineconeIndex would be used to connect.\n    // Here, we just create a new instance and store embeddings and namespace.\n    const store = new PineconeStore();\n    store.embeddings = embeddings; // Store for addDocuments\n    store.namespace = _config.namespace; // Store namespace\n    // Persistence/loading logic based on namespace would go here if implemented.\n    // console.debug(`PineconeStore polyfill created (index: ${config.pineconeIndex?.indexName || 'dummy'}, namespace: ${config.namespace || 'default'})`);\n    return store;\n  }\n\n  // --- Methods mirroring Pinecone Client behavior (adjusted for in-memory polyfill) ---\n\n  /**\n     * Add or update vectors. Simulates Pinecone's upsert.\n     * @param vectors Array of vector items. Each must have id, values, and metadata.\n     */\n  async upsert({ vectors, namespace }: { vectors: { id: string, values: number[], metadata: Metadata }[], namespace?: string }): Promise<{ upsertedCount: number }> {\n    // This polyfill does not currently handle namespaces within the storage map\n    // To implement namespaces, 'store' would need to be Map<string | undefined, Map<string, VectorStoreItem>>\n    // For simplicity, we ignore the namespace parameter here, operating on a flat map.\n    if (namespace !== this.namespace && this.namespace !== undefined) {\n      console.warn(`PineconeStore polyfill received upsert for namespace '${namespace}' but configured with '${this.namespace}'. Using configured namespace.`);\n      // Or throw error depending on desired strictness\n    }\n\n    const items: VectorStoreItem[] = vectors.map(v => ({\n      id: v.id,\n      vector: v.values,\n      metadata: v.metadata,\n      pageContent: v.metadata?.pageContent || v.metadata?.text || '', // Attempt to get content from metadata\n    }));\n    await super.addVectors(items);\n    // Persistence logic (saving to disk/mock) would go here if implemented.\n    return { upsertedCount: items.length }; // Simulate response\n  }\n\n  /**\n     * Fetch vectors by IDs. Simulates Pinecone's fetch.\n     */\n  async fetch({ ids, namespace }: { ids: string[], namespace?: string }): Promise<{ vectors: { [id: string]: { id: string, values: number[], metadata: Metadata } }, namespace: string }> {\n    // Ignore namespace for now as per upsert limitation\n    if (namespace !== this.namespace && this.namespace !== undefined) {\n      console.warn(`PineconeStore polyfill received fetch for namespace '${namespace}' but configured with '${this.namespace}'. Using configured namespace.`);\n    }\n\n    const itemsMap = await super.getVectorsByIds(ids);\n    const resultVectors: { [id: string]: { id: string, values: number[], metadata: Metadata } } = {};\n    itemsMap.forEach(item => {\n      // Remove pageContent from metadata when returning in fetch results, as Pinecone doesn't store it like this\n      const metadataToReturn = { ...item.metadata };\n      delete metadataToReturn.pageContent; // Or handle specifically\n      resultVectors[item.id] = { id: item.id, values: item.vector, metadata: metadataToReturn };\n    });\n    return { vectors: resultVectors, namespace: this.namespace || '' }; // Simulate response\n  }\n\n  /**\n     * Delete vectors by IDs or query. Simulates Pinecone's delete.\n     */\n  async delete({ ids, deleteAll, namespace }: { ids?: string[], deleteAll?: boolean, namespace?: string }): Promise<void> {\n    // Ignore namespace for now\n    if (namespace !== this.namespace && this.namespace !== undefined) {\n      console.warn(`PineconeStore polyfill received delete for namespace '${namespace}' but configured with '${this.namespace}'. Using configured namespace.`);\n    }\n\n    if (deleteAll) {\n      await super.clear();\n    } else if (ids && ids.length > 0) {\n      await super.deleteVectors(ids);\n    } else {\n      // Pinecone delete usually requires either ids or deleteAll\n      console.warn('PineconeStore polyfill \\'delete\\' called without \\'ids\\' or \\'deleteAll\\'. No action taken.');\n    }\n    // Persistence logic would go here.\n  }\n\n  /**\n     * Query for similar vectors. Simulates Pinecone's query.\n     */\n  async query({ vector, topK, filter, includeMetadata, includeValues, namespace }: {\n    vector: number[],\n    topK: number,\n    filter?: Record<string, any>, // Pinecone filter syntax is different, treat as object filter\n    includeMetadata?: boolean,\n    includeValues?: boolean,\n    namespace?: string, // Ignored\n  }): Promise<{ matches: (SimilaritySearchResultItem & { values?: number[] })[] }> {\n    // Ignore namespace for now\n    if (namespace !== this.namespace && this.namespace !== undefined) {\n      console.warn(`PineconeStore polyfill received query for namespace '${namespace}' but configured with '${this.namespace}'. Using configured namespace.`);\n    }\n\n    const results = await super.similaritySearch(vector, topK, filter);\n\n    // Format results for Pinecone query response\n    const matches = results.map(item => {\n      const match: SimilaritySearchResultItem & { values?: number[] } = {\n        id: item.id,\n        score: item.score,\n        metadata: item.metadata,\n        pageContent: item.pageContent, // Include original text from polyfill item\n      };\n      if (includeValues) {\n        // Need to fetch the actual vector values if requested\n        const vectorItem = this.store.get(item.id);\n        if (vectorItem) {\n          match.values = vectorItem.vector;\n        }\n      }\n      return match;\n    });\n\n    return { matches };\n  }\n\n  // --- LangChain VectorStore Compatibility Methods ---\n\n  /**\n     * Adds documents using the stored embeddings instance to generate vectors.\n     * Conforms to LangChain's `addDocuments` signature.\n     * @param documents Array of Documents.\n     */\n  async addDocuments(documents: Document[]): Promise<void> {\n    if (!this.embeddings) {\n      throw new Error('Embeddings instance not available in PineconeStore polyfill for addDocuments.');\n    }\n    const texts = documents.map(doc => doc.pageContent);\n    // Use the correct Embeddings method signature (takes string[])\n    const vectors = await this.embeddings.embedDocuments(texts);\n\n    const items: VectorStoreItem[] = documents.map((doc, i) => ({\n      id: doc.id ?? doc.metadata?.id ?? doc.metadata?.doc_id ?? uuidv4(), // Use doc ID, metadata ID, or generate\n      vector: vectors[i],\n      metadata: { ...doc.metadata }, // Store metadata\n      pageContent: doc.pageContent, // Store original content\n    }));\n    await super.addVectors(items); // Use base addVectors which stores all item properties\n    // Persistence logic (saving) would go here.\n  }\n\n  /**\n     * Performs similarity search using a query string and the stored embeddings.\n     * Conforms to LangChain's `similaritySearchWithScore` signature.\n     */\n  async similaritySearchWithScore(query: string, k: number, filter?: Record<string, any>): Promise<[Document, number][]> {\n    if (!this.embeddings) {\n      throw new Error('Embeddings instance not available in PineconeStore polyfill for similaritySearchWithScore.');\n    }\n    const queryEmbedding = await this.embeddings.embedQuery(query);\n\n    // Reuse the BaseMemoryVectorStore similarity search with object filter\n    const results = await super.similaritySearch(queryEmbedding, k, filter);\n\n    // Map results back to [Document, score] tuple format\n    const finalResults: [Document, number][] = results.map(item => {\n      // Reconstruct Document from the stored item\n      const doc = new Document({ pageContent: item.pageContent, metadata: item.metadata, id: item.id }); return [doc, item.score];\n    });\n\n    return finalResults;\n  }\n\n  // Optional: Provide a dummy `asRetriever` method for compatibility\n  asRetriever(k?: number): { getRelevantDocuments: (_query: string) => Promise<Document[]> } {\n    return {\n      getRelevantDocuments: async (query: string): Promise<Document[]> => {\n        // Use similarity search but only return the Documents, not scores\n        const results = await this.similaritySearchWithScore(query, k ?? 4); // Default k=4 for retrievers\n        return results.map(([doc, score]) => doc);\n      }\n    };\n  }\n}\n\n\n/**\n * Chroma Polyfill - In-memory simulation using BaseMemoryVectorStore.\n * Implements the IChromaPolyfillInstance interface.\n */\nexport class Chroma extends BaseMemoryVectorStore implements IChromaPolyfillInstance {\n  private collectionName = 'default_collection';\n  private persistDirectory: string | null = null;\n  private collectionMetadata: Record<string, any> = {};\n  private embeddings: Embeddings; // Embeddings instance is required\n\n  // Private constructor, use fromDocuments or potentially another factory\n  private constructor(embeddings: Embeddings, config?: { collectionName?: string, persistDirectory?: string, collectionMetadata?: any }) {\n    super();\n    if (!embeddings) throw new Error('Chroma polyfill requires an Embeddings instance.');\n    this.embeddings = embeddings; // Use embeddings instance\n\n    if (config) {\n      this.collectionName = config.collectionName ?? this.collectionName;\n      this.persistDirectory = config.persistDirectory ?? this.persistDirectory;\n      this.collectionMetadata = config.collectionMetadata ?? this.collectionMetadata;\n    }\n    // Persistence logic (loading from persistDirectory) would go here if implemented.\n    // console.debug(`Chroma polyfill instance created for collection '${this.collectionName}'`);\n  }\n\n  /**\n     * Static factory method to create/load a Chroma instance.\n     * Simulates connecting to a persistent collection and adding initial documents.\n     * Conforms to LangChain's `Chroma.fromDocuments` signature.\n     * @param documents Initial documents to add (can be empty).\n     * @param embeddings Embeddings instance.\n     * @param config Configuration options.\n     * @returns A Promise resolving to a Chroma instance.\n     */\n  static async fromDocuments(\n    documents: Document[], // Can be empty array to just connect\n    embeddings: Embeddings,\n    config: { collectionName: string; persistDirectory?: string; collectionMetadata?: any; }\n  ): Promise<Chroma> {\n    // Create instance using the private constructor\n    const instance = new Chroma(embeddings, config);\n\n    // If documents are provided, add them using the instance method\n    if (documents && documents.length > 0) {\n      // console.debug(`Chroma.fromDocuments: Adding ${documents.length} initial documents.`);\n      // Use the instance's addDocuments method which handles embedding\n      await instance.addDocuments(documents);\n    } else {\n      // console.debug(`Chroma.fromDocuments: Connecting to collection '${instance.collectionName}'.`);\n      // Load from persistence logic based on instance.persistDirectory would go here.\n    }\n    return instance;\n  }\n\n  // --- IChromaPolyfillInstance Implementation ---\n\n  /**\n     * Adds documents to the collection, generating embeddings using the instance's Embeddings.\n     * Conforms to `IChromaPolyfillInstance.addDocuments`.\n     * @param documents Array of Documents.\n     * @param options Optional options including ids.\n     * @returns A Promise resolving to an array of added document IDs.\n     */\n  async addDocuments(documents: Document[], options?: { ids?: string[] }): Promise<string[]> {\n    // Validate documents and extract text\n    const texts = documents.map(doc => {\n      if (typeof doc.pageContent !== 'string' || doc.pageContent.trim().length === 0) {\n        throw new Error('All documents must have non-empty string pageContent for embedding.');\n      }\n      return doc.pageContent;\n    });\n\n    // Generate embeddings using the instance's embeddings\n    const vectors = await this.embeddings.embedDocuments(texts);\n\n    const items: VectorStoreItem[] = [];\n    const addedIds: string[] = [];\n\n    for (let i = 0; i < documents.length; i++) {\n      const doc = documents[i];\n      const vector = vectors[i];\n\n      // Determine ID: use provided option, metadata ID, or generate new\n      const id = options?.ids?.[i] ?? doc.id ?? doc.metadata?.id ?? doc.metadata?.doc_id ?? uuidv4();\n\n      items.push({\n        id: id,\n        vector: vector,\n        metadata: {\n          ...(doc.metadata || {}),\n          doc_id: id, // Ensure doc_id is present in metadata\n        },\n        pageContent: doc.pageContent, // Store original content\n      });\n      addedIds.push(id);\n    }\n    await super.addVectors(items); // Use base addVectors which stores all item properties\n    // Persistence logic (saving) would go here if implemented.\n    return addedIds;\n  }\n\n  /**\n     * Retrieves data based on IDs or filters.\n     * Conforms to `IChromaPolyfillInstance.get`.\n     */\n  async get(options?: {\n    ids?: string[];\n    where?: Record<string, any>;\n    limit?: number;\n    offset?: number;\n    include?: ('documents' | 'metadatas' | 'embeddings')[];\n    whereDocument?: Record<string, any>; // Basic polyfill won't support whereDocument easily\n  }): Promise<ChromaGetResponse> {\n    const includeFields = new Set(options?.include ?? ['metadatas', 'documents']); // Default includes\n    let results: VectorStoreItem[] = [];\n\n    if (options?.ids && options.ids.length > 0) {\n      const foundMap = await super.getVectorsByIds(options.ids);\n      // Ensure results are in the order of requested IDs if possible (getVectorsByIds provides map)\n      results = options.ids.map(id => foundMap.get(id)).filter((item): item is VectorStoreItem => !!item);\n    } else {\n      // Implement basic 'where' filter (exact match on metadata keys/values)\n      const filterFn = options?.where ? (item: VectorStoreItem) =>\n        Object.entries(options!.where!).every(([key, value]) =>\n          item.metadata && item.metadata[key] === value\n        ) : undefined;\n\n      // Iterate through all stored items and apply filter\n      results = Array.from(this.store.values()).filter(item => filterFn ? filterFn(item) : true);\n    }\n\n    // Apply limit and offset\n    const offset = options?.offset ?? 0;\n    const limit = options?.limit ?? results.length;\n    const paginatedResults = results.slice(offset, offset + limit);\n\n    // Construct response based on 'include' fields\n    const response: ChromaGetResponse = {\n      ids: paginatedResults.map(item => item.id),\n      embeddings: includeFields.has('embeddings') ? paginatedResults.map(item => item.vector) : null,\n      metadatas: includeFields.has('metadatas') ? paginatedResults.map(item => item.metadata) : null,\n      // Retrieve 'pageContent' from the stored item if requested\n      documents: includeFields.has('documents') ? paginatedResults.map(item => item.pageContent ?? null) : null,\n    };\n\n    return response;\n  }\n\n  /**\n     * Deletes data based on IDs or filters.\n     * Conforms to `IChromaPolyfillInstance.delete`.\n     * @returns A Promise resolving to an array of IDs that were targeted for deletion.\n     */\n  async delete(options?: {\n    ids?: string[];\n    where?: Record<string, any>;\n    whereDocument?: Record<string, any>; // Not supported in this basic polyfill\n  }): Promise<string[]> {\n    let idsToDelete: string[] = [];\n\n    if (options?.ids && options.ids.length > 0) {\n      idsToDelete = options.ids;\n    } else if (options?.where) {\n      // Find IDs matching the 'where' filter\n      const filterFn = (item: VectorStoreItem) =>\n        Object.entries(options!.where!).every(([key, value]) =>\n          item.metadata && item.metadata[key] === value\n        );\n      idsToDelete = Array.from(this.store.values()).filter(filterFn).map(item => item.id);\n    } else if (options?.whereDocument) {\n      // Basic implementation of whereDocument - checks if content contains a substring\n      const filterFn = (item: VectorStoreItem) =>\n        Object.entries(options!.whereDocument!).every(([key, value]) =>\n          key === '$contains' && typeof value === 'string' && typeof item.pageContent === 'string' && item.pageContent.includes(value)\n        );\n      idsToDelete = Array.from(this.store.values()).filter(filterFn).map(item => item.id);\n    } else {\n      // If no criteria provided, assume delete all, consistent with some Chroma clients\n      logger.warn('Chroma polyfill \\'delete\\' called without \\'ids\\' or \\'where\\' or \\'whereDocument\\'. Assuming delete all.');\n      idsToDelete = Array.from(this.store.keys()); // Get all current IDs\n    }\n\n    if (idsToDelete.length > 0) {\n      const successfullyDeleted = await super.deleteVectors(idsToDelete); // Use base delete logic\n      // Persistence logic would go here.\n      // Return the list of IDs that were successfully deleted by the base store\n      return successfullyDeleted;\n    }\n    // Return the list of IDs that were *intended* for deletion if none were found or if no action was taken\n    return idsToDelete;\n  }\n\n  /**\n     * Performs similarity search using a query string.\n     * Conforms to `IChromaPolyfillInstance.similaritySearchWithScore`.\n     * @returns A Promise resolving to an array of [Document, score] tuples.\n     */\n  async similaritySearchWithScore(\n    query: string,\n    k: number,\n    filter?: Record<string, any> | undefined\n  ): Promise<[Document, number][]> {\n    const queryEmbedding = await this.embeddings.embedQuery(query);\n\n    // Reuse the BaseMemoryVectorStore similarity search with object filter\n    const results = await super.similaritySearch(queryEmbedding, k, filter);\n\n    // Map results back to [Document, score] tuple format\n    const finalResults: [Document, number][] = results.map(item => {\n      // Reconstruct Document from the stored item\n      const doc = new Document({ pageContent: item.pageContent, metadata: item.metadata, id: item.id }); // Use stored pageContent and metadata\n      return [doc, item.score];\n    });\n\n    return finalResults;\n  }\n\n  /**\n     * Optional: Performs similarity search using a query vector.\n     * Conforms to `IChromaPolyfillInstance.similaritySearchByVectorWithScore?`.\n     */\n  async similaritySearchByVectorWithScore(\n    embedding: number[],\n    k: number,\n    filter?: Record<string, any> | undefined\n  ): Promise<[Document, number][]> {\n    // Reuse the BaseMemoryVectorStore similarity search with object filter\n    const results = await super.similaritySearch(embedding, k, filter);\n\n    // Map results back to [Document, score] tuple format\n    const finalResults: [Document, number][] = results.map(item => {\n      // Reconstruct Document from the stored item\n      const doc = new Document({ pageContent: item.pageContent, metadata: item.metadata, id: item.id }); // Use stored pageContent and metadata\n      return [doc, item.score];\n    });\n\n    return finalResults;\n  }\n\n  /**\n     * Optional: Counts items in the collection.\n     * Conforms to `IChromaPolyfillInstance.count?`.\n     */\n  async count(): Promise<number> {\n    return this.store.size;\n  }\n\n  // Optional: Provide a dummy `asRetriever` method for compatibility if Chroma is used as a retriever source\n  asRetriever(k?: number): { getRelevantDocuments: (_query: string) => Promise<Document[]> } {\n    return {\n      getRelevantDocuments: async (query: string): Promise<Document[]> => {\n        // Use similarity search but only return the Documents, not scores\n        const results = await this.similaritySearchWithScore(query, k ?? 4); // Default k=4 for retrievers\n        return results.map(([doc]) => doc);\n      }\n    };\n  }\n}\n\n\n// === Text Splitter ===\n\n/**\n * RecursiveCharacterTextSplitter Polyfill\n * Splits text based on separators, aiming for chunk size with overlap.\n * Improved merging logic for better adherence to chunk size.\n */\nexport class RecursiveCharacterTextSplitter {\n  private readonly chunkSize: number;\n  private readonly chunkOverlap: number;\n  private readonly separators: string[];\n  private readonly keepSeparator: boolean; // LangChain option\n  // lengthFunction is part of LangChain interface, but implementing generic\n  // length based on character count is sufficient for this polyfill.\n  // private readonly lengthFunction: (text: string) => number;\n\n  constructor(options: {\n    chunkSize?: number;\n    chunkOverlap?: number;\n    separators?: string[];\n    keepSeparator?: boolean;\n    lengthFunction?: (text: string) => number; // Included for compatibility, logic uses string length\n  }) {\n    this.chunkSize = options.chunkSize ?? 1000;\n    this.chunkOverlap = options.chunkOverlap ?? 200;\n    // Default separators mimic LangChain's common defaults\n    this.separators = options.separators ?? ['\\n\\n', '\\n', ' ', ''];\n    // Ensure empty string is always the last separator if present\n    if (this.separators.includes('')) {\n      this.separators = this.separators.filter(s => s !== '').concat('');\n    } else {\n      this.separators = this.separators.concat(''); // Ensure empty string fallback\n    }\n\n    this.keepSeparator = options.keepSeparator ?? false; // Default false\n\n    if (this.chunkSize <= 0) throw new Error('chunkSize must be positive');\n    if (this.chunkOverlap < 0) throw new Error('chunkOverlap must be non-negative');\n    if (this.chunkOverlap >= this.chunkSize) throw new Error('chunkOverlap must be smaller than chunkSize');\n\n    // lengthFunction is not used in the polyfill's logic, but stored if needed for other purposes\n    // this.lengthFunction = options.lengthFunction ?? ((text) => text.length);\n  }\n\n  /**\n     * Splits text recursively based on the provided separators.\n     * This function aims to break text into segments smaller than chunkSize\n     * using the most specific available separator first.\n     * @param text The text to split.\n     * @param separators The prioritized list of separators.\n     * @returns An array of text segments.\n     */\n  private _splitTextRecursive(text: string, separators: string[]): string[] {\n    // Base case 1: No text left\n    if (text.length === 0) return [];\n\n    // Base case 2: No separators left or empty string separator is the only one left\n    if (separators.length === 0 || (separators.length === 1 && separators[0] === '')) {\n      // If the text is still too big and only \"\" separator is left,\n      // or no separators at all, return the text as one segment.\n      // The merging logic will handle breaking this down by character if needed.\n      return [text];\n    }\n\n    const currentSeparator = separators[0];\n    const remainingSeparators = separators.slice(1);\n\n    // If the current separator splits the text\n    if (currentSeparator && text.includes(currentSeparator)) {\n      const parts = text.split(currentSeparator);\n      const segments: string[] = [];\n      for (let i = 0; i < parts.length; i++) {\n        const part = parts[i];\n        // Recursively split each part\n        const subSegments = this._splitTextRecursive(part, remainingSeparators);\n        segments.push(...subSegments);\n\n        // Add the separator back if keepSeparator is true, but only between parts\n        if (this.keepSeparator && i < parts.length - 1) {\n          segments.push(currentSeparator);\n        }\n      }\n      return segments;\n    } else {\n      // If the current separator doesn't split the text, try the next ones\n      return this._splitTextRecursive(text, remainingSeparators);\n    }\n  }\n\n  /**\n     * Joins small text segments into chunks respecting chunkSize and overlap.\n     * This logic is crucial to ensure chunks are close to chunkSize and handle overlap correctly.\n     * @param segments Array of text segments (produced by recursive splitting).\n     * @returns Array of merged text chunks.\n     */\n  private _mergeChunks(segments: string[]): string[] {\n    const chunks: string[] = [];\n    let currentChunk: string[] = []; // Stores the segments of the current chunk\n    let currentLength = 0;\n    const separator = ' '; // Use a space for joining segments within a chunk\n\n    for (let i = 0; i < segments.length; i++) {\n      const segment = segments[i];\n      const segmentLength = segment.length;\n      const lengthWithSeparator = currentLength > 0 ? currentLength + separator.length + segmentLength : segmentLength;\n\n      // If adding the next segment would exceed the chunk size\n      if (lengthWithSeparator > this.chunkSize) {\n        // If we have something in the current chunk, finalize it\n        if (currentChunk.length > 0) {\n          chunks.push(currentChunk.join(separator));\n\n          // Prepare for overlap: Find segments for the beginning of the next chunk\n          let overlapStartIndex = currentChunk.length - 1;\n          let overlapText = '';\n          const overlapSegments: string[] = [];\n\n          // Iterate backward through the current chunk's segments to find overlap\n          while (overlapStartIndex >= 0 && overlapText.length < this.chunkOverlap) {\n            const prevSegment = currentChunk[overlapStartIndex];\n            // Calculate length if this segment were added to the overlap text\n            const lengthIfAdded = prevSegment.length + (overlapText.length > 0 ? separator.length : 0);\n            if (overlapText.length + lengthIfAdded > this.chunkOverlap && overlapText.length > 0) {\n              // If adding the next segment makes overlap too big, stop *before* adding it\n              break;\n            }\n            overlapSegments.unshift(prevSegment); // Add segment to the front of overlap list\n            overlapText = overlapSegments.join(separator);\n            overlapStartIndex--;\n          }\n\n          // Start the new chunk with the calculated overlap segments and the current segment\n          currentChunk = [...overlapSegments, segment];\n          currentLength = currentChunk.join(separator).length;\n\n        } else {\n          // This case is tricky: a single segment is larger than chunkSize.\n          // Recursive splitting should ideally prevent this, but if it happens\n          // (e.g., single very long line and \"\\n\" is the only separator), we must\n          // split it here. A basic character-by-character split is the fallback.\n          console.warn(`RecursiveCharacterTextSplitter: Encountered segment larger than chunkSize (${segmentLength} > ${this.chunkSize}). Forcing character split.`);\n          const forceSplitChunks: string[] = [];\n          for (let j = 0; j < segment.length; j += this.chunkSize) {\n            forceSplitChunks.push(segment.substring(j, j + this.chunkSize));\n          }\n          chunks.push(...forceSplitChunks);\n          currentChunk = [segment]; // Start new chunk attempt with the large segment\n          currentLength = segmentLength; // Reset length\n          // After force splitting, the current segment needs to be processed again\n          // or this loop index needs adjustment. Let's just process the large segment\n          // as a single chunk and then clear currentChunk to start fresh.\n          currentChunk = [];\n          currentLength = 0;\n        }\n\n      } else {\n        // Add segment to the current chunk\n        currentChunk.push(segment);\n        currentLength = lengthWithSeparator;\n      }\n    }\n\n    // Add the last remaining chunk if any\n    if (currentChunk.length > 0) {\n      chunks.push(currentChunk.join(separator));\n    }\n\n    // Handle the edge case where the *very first* segment is larger than chunkSize\n    // and the _mergeChunks logic above just added it. This requires an extra pass or\n    // more complex logic within the loop. A simpler approach for the polyfill:\n    // after merging, check any resulting chunks that are still > chunkSize and split them by character.\n    const finalChunks: string[] = [];\n    for (const chunk of chunks) {\n      if (chunk.length > this.chunkSize) {\n        console.warn(`RecursiveCharacterTextSplitter: Final chunk larger than chunkSize after merging (${chunk.length} > ${this.chunkSize}). Forcing character split.`);\n        for (let j = 0; j < chunk.length; j += this.chunkSize) {\n          finalChunks.push(chunk.substring(j, j + this.chunkSize));\n        }\n      } else if (chunk.length > 0) { // Also ensure no empty chunks\n        finalChunks.push(chunk);\n      }\n    }\n\n\n    return finalChunks.filter(Boolean); // Remove any potential empty strings\n  }\n\n\n  /**\n     * Splits the input text into chunks based on configured separators, size, and overlap.\n     * This is the primary method to call.\n     * @param text The text to split.\n     * @returns A promise resolving to an array of text chunks.\n     */\n  async splitText(text: string): Promise<string[]> {\n    if (typeof text !== 'string' || text.length === 0) {\n      return [];\n    }\n\n    // Step 1: Recursively split text into segments using prioritized separators.\n    // This aims to break the text at logical points (paragraphs, lines, words).\n    const initialSegments = this._splitTextRecursive(text, this.separators);\n\n    // Step 2: Merge these potentially small segments into chunks of the desired size, handling overlap.\n    const finalChunks = this._mergeChunks(initialSegments);\n\n    return finalChunks;\n  }\n}\n\n\n// === Documents ===\n\n/**\n * Represents a piece of text, often with associated metadata.\n * Conforms to LangChain Document structure.\n */\nexport class Document {\n  id?: string; // Optional ID for tracking\n  pageContent: string; // Main text content\n  metadata: Metadata; // Associated metadata\n\n  constructor(fields: { pageContent: string; metadata?: Metadata; id?: string }) {\n    if (typeof fields.pageContent !== 'string') throw new Error('Document requires string pageContent.');\n    this.pageContent = fields.pageContent;\n    this.metadata = fields.metadata ?? {};\n    this.id = fields.id;\n  }\n}\n\n// === Prompts ===\n\n/**\n * Placeholder for chat prompt templating functionality.\n */\nexport class ChatPromptTemplate {\n  // Minimal implementation for type checking and basic usage\n  private messages: any[]; // Stores prompt components\n\n  constructor(messages: any[]) {\n    this.messages = messages; // Store prompt components\n  }\n\n  /**\n     * Static factory method to create a template from message types or strings.\n     * Conforms to LangChain `ChatPromptTemplate.fromMessages`.\n     */\n  static fromMessages(messages: any[]): ChatPromptTemplate {\n    // console.debug(\"ChatPromptTemplate.fromMessages called (polyfill)\");\n    // In a real implementation, this would parse templates and validate message types\n    return new ChatPromptTemplate(messages);\n  }\n\n  /**\n     * Formats the prompt into a sequence of chat messages given input values.\n     * Conforms to LangChain `formatMessages`.\n     */\n  async formatMessages(values: Record<string, any>): Promise<BaseMessage[]> {\n    // console.debug(\"ChatPromptTemplate.formatMessages called (polyfill)\", values);\n    // Basic polyfill logic: create dummy messages based on keys and stored components\n    const formattedMessages: BaseMessage[] = [];\n    for (const msgComponent of this.messages) {\n      if (typeof msgComponent === 'string') {\n        // Assume it's a simple string template, replace placeholders\n        let content = msgComponent;\n        for (const key in values) {\n          content = content.replace(`{${key}}`, String(values[key]));\n        }\n        formattedMessages.push(new SystemMessage(content)); // Use SystemMessage for simplicity\n      } else if (msgComponent instanceof MessagesPlaceholder) {\n        // Insert messages from the specified variable name\n        const messagesToInsert = values[msgComponent.variable_name];\n        if (Array.isArray(messagesToInsert)) {\n          formattedMessages.push(...messagesToInsert.filter(m => m instanceof BaseMessage)); // Only add BaseMessage instances\n        } else if (messagesToInsert) {\n          console.warn(`MessagesPlaceholder variable '${msgComponent.variable_name}' did not contain an array of messages.`);\n        }\n      } else if (typeof msgComponent === 'function') {\n        // Assume it's a function returning a message or message part\n        try {\n          const result = await Promise.resolve(msgComponent(values));\n          if (result instanceof BaseMessage) {\n            formattedMessages.push(result);\n          } else if (typeof result === 'string') {\n            formattedMessages.push(new SystemMessage(result));\n          } else {\n            console.warn('ChatPromptTemplate polyfill: Function component returned unexpected type.');\n          }\n        } catch (error) {\n          console.error('ChatPromptTemplate polyfill: Error in function component:', error);\n        }\n      } else {\n        console.warn('ChatPromptTemplate polyfill: Unknown message component type.');\n      }\n    }\n\n    if (formattedMessages.length === 0 && Object.keys(values).length > 0) {\n      // Fallback if no components added messages, but there were inputs\n      formattedMessages.push(new SystemMessage(`Formatted prompt with keys: ${Object.keys(values).join(', ')}`));\n    } else if (formattedMessages.length === 0) {\n      // Fallback for empty prompt / no inputs\n      formattedMessages.push(new SystemMessage('Empty prompt'));\n    }\n\n    return formattedMessages;\n  }\n\n  /**\n     * Binds configuration to the prompt template (placeholder).\n     */\n  bind(_config: any): this {\n    // In a real implementation, this would create a new bound instance\n    // For polyfill, just return `this` as no binding logic is implemented.\n    // console.debug(\"ChatPromptTemplate.bind called (polyfill)\", config);\n    return this;\n  }\n\n  /**\n     * Pipes the output of this prompt template to a model or other runnable.\n     * @param target The model or runnable to pipe to\n     * @returns A runnable chain that will execute this prompt and then the target\n     */\n  pipe(target: any): any {\n    // Create a simple chain object that will format messages and then invoke the target\n    return {\n      invoke: async (input: Record<string, any>, options?: any) => {\n        const messages = await this.formatMessages(input);\n        return target.invoke(messages, options);\n      }\n    };\n  }\n}\n\n/**\n * Placeholder for inserting message history or other message sequences into prompts.\n * Conforms to LangChain `MessagesPlaceholder`.\n */\nexport class MessagesPlaceholder {\n  variable_name: string; // The name of the variable in the prompt values\n\n  constructor(variable_name: string) {\n    if (typeof variable_name !== 'string' || variable_name.length === 0) throw new Error('MessagesPlaceholder requires a variable_name.');\n    this.variable_name = variable_name;\n    // console.debug(`MessagesPlaceholder created for variable: ${variable_name}`);\n  }\n}\n\n// === Runnables ===\n\n/**\n * Placeholder for Runnable configuration options.\n * Conforms to LangChain `RunnableConfig`.\n */\nexport class RunnableConfig {\n  // Add common config properties if needed for type compatibility\n  callbacks?: any; // Placeholder for callback managers (CallbackManager or CallbackManagerForLLMRun)\n  tags?: string[];\n  metadata?: Metadata;\n  configurable?: Record<string, any>;\n  maxConcurrency?: number;\n  recursionLimit?: number;\n  runName?: string;\n\n  constructor(_config: any) {\n    // Assign provided properties to the instance\n    Object.assign(this, _config);\n  }\n}\n\n// === Language Models ===\n\n/**\n * Placeholder for the base language model interface.\n * Conforms to LangChain `BaseLanguageModel`.\n */\nexport abstract class BaseLanguageModel {\n  // The primary method to invoke the model\n  abstract invoke(_input: any, _options?: { signal?: AbortSignal; config?: RunnableConfig; }): Promise<any>;\n\n  // Add other common methods like 'batch', 'stream' if needed by consuming code\n  async batch?(_inputs: any[], _options?: { signal?: AbortSignal; config?: RunnableConfig | RunnableConfig[]; }): Promise<any[]>;\n  stream?(_input: any, _options?: { signal?: AbortSignal; config?: RunnableConfig; }): AsyncGenerator<any>;\n\n}\n\n/**\n * Placeholder for the base chat model interface.\n * Extends BaseLanguageModel. Conforms to LangChain `BaseChatModel`.\n */\nexport class BaseChatModel extends BaseLanguageModel {\n  // Minimal implementation for invoke, takes array of BaseMessage or string, returns AIMessage\n  async invoke(input: BaseMessage[] | string, options?: { signal?: AbortSignal; config?: RunnableConfig; }): Promise<AIMessage> {\n    // console.debug(\"BaseChatModel.invoke called (polyfill)\", { input, options });\n    const inputText = Array.isArray(input) ?\n      input.map(m => `${m.type}: ${typeof m.content === 'string' ? m.content : JSON.stringify(m.content)}`).join('\\n')\n      : input;\n\n    // Simulate a basic AI response based on the input\n    const simulatedResponse = `Polyfill AI response to: \"${inputText.substring(0, 100)}${inputText.length > 100 ? '...' : ''}\"`;\n\n    // If a callback manager is provided in config, simulate callback events\n    if (options?.config?.callbacks instanceof CallbackManagerForLLMRun) {\n      const runId = uuidv4(); // Simulate a run ID\n      const prompts = Array.isArray(input) ? [input.map(m => `${m.type}: ${m.content}`).join('\\n')] : [input]; // Simplified prompts array\n      await options.config.callbacks.handleLLMStart(\n        { name: 'BaseChatModelPolyfill' },\n        prompts,\n        { runId }\n      );\n      // Simulate async work\n      await new Promise(resolve => setTimeout(resolve, 10));\n      await options.config.callbacks.handleLLMEnd(\n        { generations: [[{ text: simulatedResponse }]] },\n        { runId }\n      ); // Simulate LangChain output format\n    }\n\n\n    return new AIMessage(simulatedResponse);\n  }\n}\n\n// === Async Caller ===\n\n/**\n * Placeholder for managing asynchronous calls with concurrency, retries, etc.\n * Basic polyfill just executes the function directly.\n * Conforms to LangChain `AsyncCaller`.\n */\nexport class AsyncCaller {\n  // Store config options if needed, but logic is minimal here\n  private readonly maxConcurrency: number;\n  private readonly maxRetries: number;\n\n  constructor(options: { maxConcurrency?: number; maxRetries?: number; } = {}) {\n    // Store options, although not fully implemented in the polyfill's logic\n    this.maxConcurrency = options.maxConcurrency ?? Infinity;\n    this.maxRetries = options.maxRetries ?? 0;\n    logger.debug('AsyncCaller created with config:', {\n      maxConcurrency: this.maxConcurrency,\n      maxRetries: this.maxRetries\n    });\n  }\n\n  /**\n     * Executes the provided asynchronous function.\n     * @param func The async function to call.\n     * @param args Arguments to pass to the function.\n     * @returns The result of the function call.\n     */\n  async call<A extends any[], T>(\n    func: (..._args: A) => Promise<T>,\n    ...args: A\n  ): Promise<T> {\n    // A real implementation would use promises and queueing for concurrency\n    // and try-catch loops with delays for retries.\n    // This polyfill just directly calls the function.\n    // console.debug(\"AsyncCaller.call executed (polyfill) - calling wrapped function.\");\n    return func(...args);\n  }\n}\n\n\n// === Memory Components ===\n\n/**\n * Simple in-memory store for chat messages.\n * Conforms to LangChain `ChatMessageHistory`.\n */\nexport class ChatMessageHistory {\n  private messages: BaseMessage[] = [];\n\n  /**\n     * Adds a single message to the history.\n     */\n  async addMessage(message: BaseMessage): Promise<void> {\n    if (!(message instanceof BaseMessage)) throw new Error('Only instances of BaseMessage can be added to ChatMessageHistory.');\n    this.messages.push(message);\n  }\n\n  /**\n     * Retrieves all messages in the history.\n     * @returns A promise resolving to an array of BaseMessage objects.\n     */\n  async getMessages(): Promise<BaseMessage[]> {\n    // Return a copy to prevent external modification of the internal array\n    return [...this.messages];\n  }\n\n  /**\n     * Clears all messages from the history.\n     */\n  async clear(): Promise<void> {\n    this.messages = [];\n  }\n\n  /**\n     * Convenience method to add a HumanMessage.\n     * @param message The human message content.\n     */\n  async addUserMessage(message: string): Promise<void> {\n    await this.addMessage(new HumanMessage(message));\n  }\n\n  /**\n     * Convenience method to add an AIMessage.\n     * @param message The AI message content.\n     */\n  async addAIChatMessage(message: string): Promise<void> {\n    await this.addMessage(new AIMessage(message));\n  }\n}\n\n/**\n * Base class for chat memory systems. Defines common properties and abstract methods.\n * Conforms to LangChain `BaseChatMemory`.\n */\nexport abstract class BaseChatMemory {\n  abstract chatHistory: ChatMessageHistory; // Requires subclasses to provide a ChatMessageHistory instance\n  memoryKey: string; // Key for memory variables (e.g., \"history\", \"chat_history\")\n  inputKey?: string; // Optional key for input\n  outputKey?: string; // Optional key for output\n  returnMessages: boolean; // Whether loadMemoryVariables should return BaseMessage[] or string\n\n  constructor(_config: any) {\n    // Assign provided properties, using defaults where appropriate\n    this.memoryKey = _config.memoryKey ?? 'history'; // Default memory key\n    this.inputKey = _config.inputKey;\n    this.outputKey = _config.outputKey;\n    this.returnMessages = _config.returnMessages ?? false; // Default to returning string\n\n    // chatHistory must be provided by subclass or config\n    // this.chatHistory = config.chatHistory; // This needs to be assigned by the subclass/factory\n\n    // console.debug(\"BaseChatMemory constructor called (polyfill)\", { memoryKey: this.memoryKey, returnMessages: this.returnMessages });\n  }\n\n  /**\n   * Loads memory variables from the memory system.\n   * @param inputs Optional input values provided to the chain/agent.\n   * @returns A promise resolving to a record containing the memory variables.\n   */\n  abstract loadMemoryVariables(_inputs?: Record<string, any>): Promise<Record<string, any>>;\n\n  /**\n   * Saves the current context (input and output) to the memory system.\n   * @param inputs The input values provided to the chain/agent.\n   * @param outputs The output values from the chain/agent.\n   */\n  abstract saveContext(_inputs: Record<string, any>, _outputs: Record<string, any>): Promise<void>;\n\n  /**\n   * Clears the memory.\n   */\n  abstract clear(): Promise<void>;\n}\n\n/**\n * Base Memory class for all memory implementations.\n * Provides common interface for memory operations.\n */\nexport abstract class BaseMemory {\n  /**\n   * Load memory variables based on the input key values.\n   */\n  abstract loadMemoryVariables(_values: Record<string, any>): Promise<Record<string, any>>;\n\n  /**\n   * Save context from this conversation to memory.\n   */\n  abstract saveContext(_inputValues: Record<string, any>, _outputValues: Record<string, any>): Promise<void>;\n\n  /**\n   * Clear memory contents.\n   */\n  clear?(): Promise<void>;\n}\n\n/**\n * BufferMemory Polyfill: Stores recent messages in a buffer in memory.\n * Conforms to LangChain `BufferMemory`.\n */\nexport class BufferMemory extends BaseChatMemory {\n  chatHistory: ChatMessageHistory;\n  k: number; // Number of messages to keep (from the end of history)\n\n  constructor(_config: { k?: number; chatHistory?: ChatMessageHistory; memoryKey?: string; returnMessages?: boolean } = {}) {\n    super(_config);\n    // Ensure chatHistory is provided or create a new one\n    this.chatHistory = _config.chatHistory ?? new ChatMessageHistory();\n    this.k = _config.k ?? Infinity; // Default to keeping all messages\n    if (this.k < 0) throw new Error('BufferMemory \\'k\\' must be non-negative.');\n    // console.debug(\"BufferMemory instance created (polyfill)\", { k: this.k });\n  }\n\n  /**\n     * Loads the last `k` messages from the chat history.\n     * @param _ Unused inputs parameter (kept for interface compatibility)\n     * @returns A record containing the memory variables\n     */\n  async loadMemoryVariables(_?: Record<string, any>): Promise<Record<string, any>> {\n    const messages = await this.chatHistory.getMessages();\n    const buffer = messages.slice(-this.k); // Get last k messages\n\n    if (this.returnMessages) {\n      return { [this.memoryKey]: buffer }; // Return array of BaseMessage\n    } else {\n      // Format as string if returnMessages is false (basic concatenation)\n      return { [this.memoryKey]: buffer.map(m => `${m.type}: ${m.content}`).join('\\n') };\n    }\n  }\n\n  /**\n     * Saves the input and output messages to the chat history.\n     */\n  async saveContext(inputs: Record<string, any>, outputs: Record<string, any>): Promise<void> {\n    // Determine input and output messages based on keys or guessing\n    const inputKey = this.inputKey ?? Object.keys(inputs)[0];\n    const outputKey = this.outputKey ?? Object.keys(outputs)[0];\n\n    const humanMessageContent = inputs[inputKey];\n    const aiMessageContent = outputs[outputKey];\n\n    // Add messages to the chat history\n    if (humanMessageContent) {\n      if (typeof humanMessageContent === 'string') {\n        await this.chatHistory.addUserMessage(humanMessageContent);\n      } else if (humanMessageContent instanceof BaseMessage) {\n        await this.chatHistory.addMessage(humanMessageContent);\n      } else {\n        console.warn(`BufferMemory: Input for key '${inputKey}' is not a string or BaseMessage.`);\n      }\n    }\n\n    if (aiMessageContent) {\n      if (typeof aiMessageContent === 'string') {\n        await this.chatHistory.addAIChatMessage(aiMessageContent);\n      } else if (aiMessageContent instanceof BaseMessage) {\n        await this.chatHistory.addMessage(aiMessageContent);\n      } else {\n        console.warn(`BufferMemory: Output for key '${outputKey}' is not a string or BaseMessage.`);\n      }\n    }\n\n    // Note: K limit is applied during load, not save.\n  }\n\n  /**\n     * Clears the chat history.\n     */\n  async clear(): Promise<void> {\n    await this.chatHistory.clear();\n  }\n}\n\n/**\n * ConversationSummaryMemory Polyfill: Placeholder for summarizing conversation.\n * Requires an LLM, which is not fully polyfilled here.\n * Conforms to LangChain `ConversationSummaryMemory`.\n */\nexport class ConversationSummaryMemory extends BaseChatMemory {\n  chatHistory: ChatMessageHistory;\n  llm: BaseLanguageModel; // Requires a BaseLanguageModel instance for summarization\n  summary = ''; // Stores the current summary\n\n  constructor(_config: { llm: BaseLanguageModel; chatHistory?: ChatMessageHistory; memoryKey?: string; returnMessages?: boolean }) {\n    super(_config);\n    if (!_config.llm) throw new Error('ConversationSummaryMemory requires an \\'llm\\' instance.');\n    this.llm = _config.llm;\n    // Ensure chatHistory is provided or create a new one\n    this.chatHistory = _config.chatHistory ?? new ChatMessageHistory();\n    // console.debug(\"ConversationSummaryMemory instance created (polyfill).\");\n  }\n\n  /**\n     * Loads the current summary.\n     * @param _ Unused inputs parameter (kept for interface compatibility)\n     * @returns A record containing the memory variables\n     */\n  async loadMemoryVariables(_?: Record<string, any>): Promise<Record<string, any>> {\n    // Return the current summary as a string\n    return { [this.memoryKey]: this.summary || '' };\n  }\n\n  /**\n     * Saves context and updates the summary.\n     * Simulates summarizing by simply appending new content for the polyfill.\n     */\n  async saveContext(inputs: Record<string, any>, outputs: Record<string, any>): Promise<void> {\n    // Save messages to history first\n    const inputKey = this.inputKey ?? Object.keys(inputs)[0];\n    const outputKey = this.outputKey ?? Object.keys(outputs)[0];\n\n    let newContent = '';\n    if (inputKey && inputs[inputKey]) {\n      newContent += `Human: ${typeof inputs[inputKey] === 'string' ? inputs[inputKey] : JSON.stringify(inputs[inputKey])}\\n`;\n      if (typeof inputs[inputKey] === 'string') await this.chatHistory.addUserMessage(inputs[inputKey]);\n      else if (inputs[inputKey] instanceof BaseMessage) await this.chatHistory.addMessage(inputs[inputKey]);\n    }\n    if (outputKey && outputs[outputKey]) {\n      newContent += `AI: ${typeof outputs[outputKey] === 'string' ? outputs[outputKey] : JSON.stringify(outputs[outputKey])}\\n`;\n      if (typeof outputs[outputKey] === 'string') await this.chatHistory.addAIChatMessage(outputs[outputKey]);\n      else if (outputs[outputKey] instanceof BaseMessage) await this.chatHistory.addMessage(outputs[outputKey]);\n    }\n\n    // Placeholder: Simulate summarization by just appending new content.\n    // A real implementation would use `this.llm.invoke` with a prompt\n    // combining the old summary and new messages to generate an updated summary.\n    if (newContent.length > 0) {\n      this.summary = (this.summary ? this.summary + '\\n' : '') + `... (Summarized context incorporating: ${newContent.trim().substring(0, 50)}...)`;\n      // console.debug(\"ConversationSummaryMemory: Updated summary (polyfill).\");\n    }\n  }\n\n  /**\n     * Clears the chat history and the summary.\n     */\n  async clear(): Promise<void> {\n    await this.chatHistory.clear();\n    this.summary = '';\n  }\n}\n\n/**\n * VectorStoreRetrieverMemory Polyfill: Placeholder for retrieving relevant docs from a vector store.\n * Requires a VectorStoreRetriever instance (or an object with getRelevantDocuments).\n * Conforms to LangChain `VectorStoreRetrieverMemory`.\n */\nexport class VectorStoreRetrieverMemory extends BaseChatMemory {\n  chatHistory: ChatMessageHistory;\n  // Placeholder for a retriever instance (e.g., from MemoryVectorStore.asRetriever)\n  vectorStoreRetriever: { getRelevantDocuments: (_query: string) => Promise<Document[]> };\n  memoryKey: string; // Often used for retrieved context (e.g., \"context\")\n\n  constructor(config: Partial<VectorStoreRetrieverMemory> & {\n    vectorStoreRetriever: { getRelevantDocuments: (_query: string) => Promise<Document[]> },\n    memoryKey?: string,\n    inputKey?: string\n  }) {\n    super(config); // Pass config to BaseChatMemory (handles memoryKey, inputKey)\n    if (!config.vectorStoreRetriever || typeof config.vectorStoreRetriever.getRelevantDocuments !== 'function') {\n      throw new Error('VectorStoreRetrieverMemory requires a valid \\'vectorStoreRetriever\\' with a \\'getRelevantDocuments\\' method.');\n    }\n    this.vectorStoreRetriever = config.vectorStoreRetriever;\n    this.memoryKey = config.memoryKey ?? 'context'; // Default memory key for context\n\n    // This memory type typically doesn't manage a chat history directly for *itself*,\n    // it uses the retriever. However, the BaseChatMemory interface requires it.\n    // We can assign a dummy history or null, or redefine the interface if possible.\n    // For this polyfill, let's assign a dummy history to satisfy the BaseChatMemory interface,\n    // although it won't be used for loading/saving context in the standard way.\n    this.chatHistory = config.chatHistory ?? new ChatMessageHistory();\n    if (config.chatHistory) {\n      console.warn('VectorStoreRetrieverMemory polyfill received chatHistory, but typically doesn\\'t use it directly for retrieval.');\n    }\n    // console.debug(\"VectorStoreRetrieverMemory instance created (polyfill).\");\n  }\n\n  /**\n     * Loads memory variables by retrieving relevant documents based on the input query.\n     */\n  async loadMemoryVariables(inputs: Record<string, any>): Promise<Record<string, any>> {\n    // Determine the query string from inputs\n    const inputKey = this.inputKey ?? Object.keys(inputs)[0]; // Use configured input key or guess\n    const query = inputs[inputKey];\n\n    if (typeof query !== 'string' || query.length === 0) {\n      console.warn(`VectorStoreRetrieverMemory: Cannot determine input string for retrieval using key '${inputKey}'. Returning empty context.`);\n      return { [this.memoryKey]: '' };\n    }\n\n    // Use the retriever to find relevant documents\n    const relevantDocs = await this.vectorStoreRetriever.getRelevantDocuments(query);\n\n    // Format documents into a single string context\n    const context = relevantDocs.map(doc => doc.pageContent).join('\\n\\n');\n\n    // Return the formatted context under the memory key\n    return { [this.memoryKey]: context };\n  }\n\n  /**\n     * Saves the current context.\n     * Note: This memory type typically doesn't save conversational context back to the vector store itself.\n     * This method might be used in combined memory systems, but for this polyfill, it does nothing.\n     * @param _ Unused inputs parameter (kept for interface compatibility)\n     * @param __ Unused outputs parameter (kept for interface compatibility)\n     */\n  async saveContext(_: Record<string, any>, __: Record<string, any>): Promise<void> {\n    // VectorStoreRetrieverMemory typically does not implement saveContext to the vector store.\n    // Its purpose is retrieval based on current input.\n    // If used in a larger system, a different memory component (like BufferMemory or ChatMessageHistory)\n    // would handle saving the actual conversation turns.\n    logger.debug('VectorStoreRetrieverMemory.saveContext called (no action taken on vector store)');\n  }\n\n  /**\n     * Clears the memory.\n     * Note: Clearing this memory component typically does *not* clear the underlying vector store.\n     * It would only clear internal state if any existed, or potentially the chat history\n     * if a separate one was managed (which is not the case in this basic polyfill).\n     */\n  async clear(): Promise<void> {\n    // No internal state to clear in this basic polyfill\n    // console.debug(\"VectorStoreRetrieverMemory.clear called (polyfill - no action taken).\");\n  }\n}\n\n\n/**\n * MemoryVectorStore Polyfill: Simple in-memory store for Documents with embeddings.\n * Stores Documents (including pageContent and metadata) and their embeddings.\n * Conforms to LangChain `MemoryVectorStore`.\n */\nexport class MemoryVectorStore extends BaseMemoryVectorStore {\n  private embeddings: Embeddings | null = null; // Optional embeddings for string queries\n\n  constructor(embeddings?: Embeddings) {\n    super();\n    this.embeddings = embeddings ?? null;\n    // console.debug(\"MemoryVectorStore instance created (polyfill).\");\n  }\n\n  /**\n     * Static factory method (placeholder for compatibility).\n     * In a real scenario, might load from a path etc.\n     */\n  static async fromTexts(texts: string[], metadatas: Metadata[], embeddings: Embeddings): Promise<MemoryVectorStore> {\n    if (!embeddings) throw new Error('Embeddings instance is required for fromTexts.');\n    if (texts.length !== metadatas.length) throw new Error('Texts and metadatas arrays must have the same length.');\n\n    const docs = texts.map((text, i) => new Document({ pageContent: text, metadata: metadatas[i] }));\n    const instance = new MemoryVectorStore(embeddings);\n    await instance.addDocuments(docs);\n    return instance;\n  }\n\n  /**\n     * Static factory method (placeholder for compatibility).\n     * In a real scenario, might load from a path etc.\n     */\n  static async fromDocuments(docs: Document[], embeddings: Embeddings): Promise<MemoryVectorStore> {\n    if (!embeddings) throw new Error('Embeddings instance is required for fromDocuments.');\n    const instance = new MemoryVectorStore(embeddings);\n    await instance.addDocuments(docs);\n    return instance;\n  }\n\n\n  /**\n     * Adds documents. Assumes embeddings are pre-computed in metadata or uses instance embeddings.\n     * Conforms to LangChain `addDocuments`.\n     * @param documents Array of Documents.\n     */\n  async addDocuments(documents: Document[]): Promise<void> {\n    const items: VectorStoreItem[] = [];\n    const textsToEmbed: string[] = [];\n    const originalDocIndices: number[] = []; // Map indices back to original documents array\n\n    // Prepare items and identify which documents need embedding\n    for (let i = 0; i < documents.length; i++) {\n      const doc = documents[i];\n      const id = doc.id ?? doc.metadata?.id ?? doc.metadata?.doc_id ?? uuidv4();\n      let vector = doc.metadata?.embedding; // Check if embedding is already in metadata\n\n      if (!Array.isArray(vector) || vector.length === 0) {\n        // Document needs embedding\n        if (!this.embeddings) {\n          throw new Error(`Document with id '${id}' is missing embedding, and no Embeddings instance provided to MemoryVectorStore.`);\n        }\n        if (typeof doc.pageContent !== 'string' || doc.pageContent.trim().length === 0) {\n          throw new Error(`Document with id '${id}' needs embedding but pageContent is empty or not a string.`);\n        }\n        textsToEmbed.push(doc.pageContent);\n        originalDocIndices.push(i); // Record original index\n        vector = []; // Placeholder, will be filled after embedding\n      }\n\n      // Create the VectorStoreItem structure\n      items.push({\n        id: id,\n        vector: vector, // May be placeholder or existing embedding\n        metadata: { ...doc.metadata }, // Store a copy of metadata\n        pageContent: doc.pageContent, // Store original page content\n      });\n    }\n\n    // Embed documents that need it\n    if (textsToEmbed.length > 0 && this.embeddings) {\n      // Use the instance's embeddings to embed the batch of texts\n      const vectors = await this.embeddings.embedDocuments(textsToEmbed);\n      // Assign the generated vectors back to the correct items\n      originalDocIndices.forEach((originalIndex, batchIndex) => {\n        if (!items[originalIndex]) {\n          console.error(`MemoryVectorStore polyfill: Internal error - original index ${originalIndex} not found in items.`);\n          return; // Skip if something went wrong\n        }\n        items[originalIndex].vector = vectors[batchIndex];\n        // Optionally add embedding to metadata here if desired, but storing in 'vector' field is primary\n        // items[originalIndex].metadata.embedding = vectors[batchIndex];\n      });\n    }\n\n    // Add all prepared items (with embeddings) to the store\n    await super.addVectors(items);\n  }\n\n  /**\n     * Perform similarity search using a query string or vector.\n     * Conforms to LangChain `similaritySearch`.\n     * @param query Query string or embedding vector (number[]).\n     * @param k Number of top results to return.\n     * @param filter Optional metadata filter.\n     * @returns A Promise resolving to an array of matching Documents.\n     */\n  async similaritySearch(query: string | number[], k: number, filter?: Record<string, any>): Promise<SimilaritySearchResultItem[]> {\n    let queryEmbedding: number[];\n    if (typeof query === 'string') {\n      if (!this.embeddings) {\n        throw new Error('MemoryVectorStore requires an Embeddings instance for string queries.');\n      }\n      queryEmbedding = await this.embeddings.embedQuery(query);\n    } else if (Array.isArray(query)) {\n      queryEmbedding = query;\n    } else {\n      throw new Error('Query must be a string or a number array.');\n    }\n\n    // Reuse the BaseMemoryVectorStore similarity search which returns SimilaritySearchResultItem[]\n    const results = await super.similaritySearch(queryEmbedding, k, filter);\n\n    // Return the SimilaritySearchResultItem array directly\n    return results;\n  }\n\n  /**\n     * Perform similarity search and return scores.\n     * Conforms to LangChain `similaritySearchWithScore`.\n     * @param query Query string or embedding vector (number[]).\n     * @param k Number of top results to return.\n     * @param filter Optional metadata filter.\n     * @returns A Promise resolving to an array of [Document, score] tuples.\n     */\n  async similaritySearchWithScore(query: string | number[], k: number, filter?: Record<string, any>): Promise<[Document, number][]> {\n    let queryEmbedding: number[];\n    if (typeof query === 'string') {\n      if (!this.embeddings) {\n        throw new Error('MemoryVectorStore requires an Embeddings instance for string queries.');\n      }\n      queryEmbedding = await this.embeddings.embedQuery(query);\n    } else if (Array.isArray(query)) {\n      queryEmbedding = query;\n    } else {\n      throw new Error('Query must be a string or a number array.');\n    }\n\n    // Reuse the BaseMemoryVectorStore similarity search which returns SimilaritySearchResultItem[]\n    const results = await super.similaritySearch(queryEmbedding, k, filter);\n\n    // Map the SimilaritySearchResultItem objects to [Document, score] tuples\n    const finalResults: [Document, number][] = results.map(item => {\n      // Reconstruct Document from the stored item's pageContent and metadata\n      const doc = new Document({\n        pageContent: item.pageContent,\n        metadata: item.metadata,\n        id: item.id\n      });\n      return [doc, item.score];\n    });\n\n    return finalResults;\n  }\n\n  /**\n     * Create a retriever instance for use with LangChain components.\n     * Conforms to LangChain `asRetriever`.\n     * @param k The number of documents the retriever should return (defaults to 4).\n     * @returns An object conforming to the LangChain Retriever interface (specifically `getRelevantDocuments`).\n     */\n  asRetriever(k?: number): { getRelevantDocuments: (_query: string) => Promise<Document[]> } {\n    const retrieverK = k ?? 4; // Default to 4 documents for retriever\n\n    // Return an object that implements the getRelevantDocuments method\n    return {\n      getRelevantDocuments: async (query: string): Promise<Document[]> => {\n        // Use this vector store's similaritySearch method\n        // Filters can be added here if the retriever supports them\n        return this.similaritySearch(query, retrieverK);\n      }\n    };\n  }\n}\n\n\n// === CallbackManager Polyfill ===\n\n/**\n * Defines the structure of a callback handler function\n */\ntype CallbackHandler = (..._args: any[]) => void | Promise<void>;\n\n/**\n * Defines the standard event types for the callback system\n */\nexport enum CallbackEventType {\n  LLM_START = 'llm_start',\n  LLM_NEW_TOKEN = 'llm_new_token',\n  LLM_END = 'llm_end',\n  LLM_ERROR = 'llm_error',\n  CHAIN_START = 'chain_start',\n  CHAIN_END = 'chain_end',\n  CHAIN_ERROR = 'chain_error',\n  TOOL_START = 'tool_start',\n  TOOL_END = 'tool_end',\n  TOOL_ERROR = 'tool_error',\n  AGENT_ACTION = 'agent_action',\n  AGENT_FINISH = 'agent_finish',\n  RETRIEVER_START = 'retriever_start',\n  RETRIEVER_END = 'retriever_end',\n  RETRIEVER_ERROR = 'retriever_error',\n  MEMORY_START = 'memory_start',\n  MEMORY_END = 'memory_end',\n  MEMORY_ERROR = 'memory_error',\n  CUSTOM = 'custom'\n}\n\n// Ensure all enum values are recognized as used by the linter\nconst _validateCallbackEventTypes = (): CallbackEventType[] => {\n  return [\n    CallbackEventType.LLM_START,\n    CallbackEventType.LLM_NEW_TOKEN,\n    CallbackEventType.LLM_END,\n    CallbackEventType.LLM_ERROR,\n    CallbackEventType.CHAIN_START,\n    CallbackEventType.CHAIN_END,\n    CallbackEventType.CHAIN_ERROR,\n    CallbackEventType.TOOL_START,\n    CallbackEventType.TOOL_END,\n    CallbackEventType.TOOL_ERROR,\n    CallbackEventType.AGENT_ACTION,\n    CallbackEventType.AGENT_FINISH,\n    CallbackEventType.RETRIEVER_START,\n    CallbackEventType.RETRIEVER_END,\n    CallbackEventType.RETRIEVER_ERROR,\n    CallbackEventType.MEMORY_START,\n    CallbackEventType.MEMORY_END,\n    CallbackEventType.MEMORY_ERROR,\n    CallbackEventType.CUSTOM\n  ];\n};\n\n/**\n * Comprehensive polyfill for LangChain's CallbackManager concept.\n * Allows registering and running callbacks for various events in the LLM pipeline.\n * Conforms to LangChain `CallbackManagerForLLMRun` and extends functionality.\n */\nexport class CallbackManagerForLLMRun {\n  private handlers: Map<string, CallbackHandler[]> = new Map(); // Store handlers by event name\n  private readonly runId: string;\n  private readonly parentRunId?: string;\n  private readonly metadata: Record<string, any>;\n  private isVerbose = false;\n\n  /**\n     * Creates a new CallbackManagerForLLMRun instance\n     * @param runId Unique identifier for this run\n     * @param parentRunId Optional parent run identifier for nested runs\n     * @param metadata Additional metadata for this run\n     * @param verbose Whether to log detailed information about callback execution\n     */\n  constructor(runId: string, parentRunId?: string, metadata: Record<string, any> = {}, verbose = false) {\n    if (!runId) throw new Error('CallbackManager requires a valid runId');\n    this.runId = runId;\n    this.parentRunId = parentRunId;\n    this.metadata = metadata;\n    this.isVerbose = verbose;\n\n    if (this.isVerbose) {\n      logger.debug(`CallbackManager initialized with runId: ${runId}${parentRunId ? `, parentRunId: ${parentRunId}` : ''}`);\n    }\n  }\n\n  /**\n     * Adds a callback handler for a specific event.\n     * @param eventName The name of the event (e.g., 'llm_start', 'llm_end').\n     * @param handler The function to call when the event occurs.\n     * @returns This instance for method chaining\n     */\n  addHandler(eventName: string, handler: CallbackHandler): this {\n    if (typeof eventName !== 'string' || eventName.length === 0) {\n      logger.warn('CallbackManager: Attempted to add handler for invalid event name.');\n      return this;\n    }\n    if (typeof handler !== 'function') {\n      logger.warn(`CallbackManager: Attempted to add non-function handler for event '${eventName}'.`);\n      return this;\n    }\n\n    if (!this.handlers.has(eventName)) {\n      this.handlers.set(eventName, []);\n    }\n    this.handlers.get(eventName)?.push(handler);\n\n    if (this.isVerbose) {\n      logger.debug(`CallbackManager: Added handler for event '${eventName}'.`);\n    }\n    return this;\n  }\n\n  /**\n     * Removes a specific handler for an event\n     * @param eventName The name of the event\n     * @param handler The handler function to remove\n     * @returns This instance for method chaining\n     */\n  removeHandler(eventName: string, handler: CallbackHandler): this {\n    if (!this.handlers.has(eventName)) return this;\n\n    const handlers = this.handlers.get(eventName);\n    if (!handlers) return this;\n\n    const index = handlers.indexOf(handler);\n    if (index !== -1) {\n      handlers.splice(index, 1);\n      if (this.isVerbose) {\n        logger.debug(`CallbackManager: Removed handler for event '${eventName}'.`);\n      }\n    }\n\n    return this;\n  }\n\n  /**\n     * Removes all handlers for a specific event\n     * @param eventName The name of the event to clear handlers for\n     * @returns This instance for method chaining\n     */\n  clearHandlers(eventName: string): this {\n    if (this.handlers.has(eventName)) {\n      this.handlers.delete(eventName);\n      if (this.isVerbose) {\n        logger.debug(`CallbackManager: Cleared all handlers for event '${eventName}'.`);\n      }\n    }\n    return this;\n  }\n\n  /**\n     * Removes all handlers for all events\n     * @returns This instance for method chaining\n     */\n  clearAllHandlers(): this {\n    this.handlers.clear();\n    if (this.isVerbose) {\n      logger.debug('CallbackManager: Cleared all handlers for all events.');\n    }\n    return this;\n  }\n\n  /**\n     * Runs all registered handlers for a given event.\n     * @param eventName The name of the event to trigger.\n     * @param args Arguments to pass to the handlers.\n     * @returns A promise that resolves when all handlers have completed\n     */\n  async runHandlers(eventName: string, ...args: any[]): Promise<void> {\n    const eventHandlers = this.handlers.get(eventName);\n    if (eventHandlers && eventHandlers.length > 0) {\n      if (this.isVerbose) {\n        logger.debug(`CallbackManager: Running ${eventHandlers.length} handlers for event '${eventName}'`);\n      }\n\n      // Add run metadata to the arguments\n      const argsWithMetadata = [...args, {\n        runId: this.runId,\n        parentRunId: this.parentRunId,\n        metadata: this.metadata,\n        timestamp: Date.now()\n      }];\n\n      // Use Promise.allSettled to ensure all handlers are attempted even if some fail\n      const results = await Promise.allSettled(eventHandlers.map(async (handler) => {\n        try {\n          return await Promise.resolve(handler(...argsWithMetadata));\n        } catch (error) {\n          logger.error(`Callback handler for event '${eventName}' threw an error:`, error);\n          throw error; // Re-throw to be caught by allSettled\n        }\n      }));\n\n      // Log any rejected promises\n      const failedCount = results.filter(r => r.status === 'rejected').length;\n      if (failedCount > 0 && this.isVerbose) {\n        logger.warn(`CallbackManager: ${failedCount} of ${results.length} handlers failed for event '${eventName}'`);\n      }\n    } else if (this.isVerbose) {\n      logger.debug(`CallbackManager: No handlers registered for event '${eventName}'.`);\n    }\n  }\n\n  /**\n     * Creates a child callback manager for nested runs\n     * @param childRunId The run ID for the child\n     * @param metadata Additional metadata for the child run\n     * @returns A new CallbackManagerForLLMRun instance\n     */\n  createChild(childRunId: string, metadata: Record<string, any> = {}): CallbackManagerForLLMRun {\n    return new CallbackManagerForLLMRun(\n      childRunId,\n      this.runId, // Current runId becomes the parent\n      { ...this.metadata, ...metadata }, // Merge metadata\n      this.isVerbose\n    );\n  }\n\n  /**\n     * Sets the verbosity level of the callback manager\n     * @param verbose Whether to enable verbose logging\n     * @returns This instance for method chaining\n     */\n  setVerbose(verbose: boolean): this {\n    this.isVerbose = verbose;\n    return this;\n  }\n\n  // --- Comprehensive event handling methods ---\n\n  /**\n     * Triggers the 'llm_start' event when an LLM begins processing.\n     * @param llm Information about the LLM being used\n     * @param prompts The input prompts being sent to the LLM\n     * @param options Additional options for the LLM call\n     */\n  async handleLLMStart(\n    llm: { name: string; model?: string; provider?: string;[key: string]: any },\n    prompts: string[],\n    options: Record<string, any> = {}\n  ): Promise<void> {\n    await this.runHandlers(CallbackEventType.LLM_START, llm, prompts, options);\n  }\n\n  /**\n     * Triggers the 'llm_new_token' event when a new token is generated in streaming mode.\n     * @param token The new token generated\n     * @param options Additional context about the token generation\n     */\n  async handleLLMNewToken(token: string, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.LLM_NEW_TOKEN, token, options);\n  }\n\n  /**\n     * Triggers the 'llm_end' event when an LLM completes processing.\n     * @param output The output from the LLM\n     * @param options Additional context about the completion\n     */\n  async handleLLMEnd(output: any, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.LLM_END, output, options);\n  }\n\n  /**\n     * Triggers the 'llm_error' event when an LLM encounters an error.\n     * @param error The error that occurred\n     * @param options Additional context about the error\n     */\n  async handleLLMError(error: Error | unknown, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.LLM_ERROR, error, options);\n  }\n\n  /**\n     * Triggers the 'chain_start' event when a chain begins processing.\n     * @param chain Information about the chain\n     * @param inputs The inputs to the chain\n     * @param options Additional context about the chain execution\n     */\n  async handleChainStart(\n    chain: { name: string; type?: string;[key: string]: any },\n    inputs: Record<string, any>,\n    options: Record<string, any> = {}\n  ): Promise<void> {\n    await this.runHandlers(CallbackEventType.CHAIN_START, chain, inputs, options);\n  }\n\n  /**\n     * Triggers the 'chain_end' event when a chain completes processing.\n     * @param outputs The outputs from the chain\n     * @param options Additional context about the chain completion\n     */\n  async handleChainEnd(outputs: Record<string, any>, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.CHAIN_END, outputs, options);\n  }\n\n  /**\n     * Triggers the 'chain_error' event when a chain encounters an error.\n     * @param error The error that occurred\n     * @param options Additional context about the error\n     */\n  async handleChainError(error: Error | unknown, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.CHAIN_ERROR, error, options);\n  }\n\n  /**\n     * Triggers the 'tool_start' event when a tool begins execution.\n     * @param tool Information about the tool\n     * @param input The input to the tool\n     * @param options Additional context about the tool execution\n     */\n  async handleToolStart(\n    tool: { name: string; description?: string;[key: string]: any },\n    input: any,\n    options: Record<string, any> = {}\n  ): Promise<void> {\n    await this.runHandlers(CallbackEventType.TOOL_START, tool, input, options);\n  }\n\n  /**\n     * Triggers the 'tool_end' event when a tool completes execution.\n     * @param output The output from the tool\n     * @param options Additional context about the tool completion\n     */\n  async handleToolEnd(output: any, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.TOOL_END, output, options);\n  }\n\n  /**\n     * Triggers the 'tool_error' event when a tool encounters an error.\n     * @param error The error that occurred\n     * @param options Additional context about the error\n     */\n  async handleToolError(error: Error | unknown, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.TOOL_ERROR, error, options);\n  }\n\n  /**\n     * Triggers the 'agent_action' event when an agent decides to take an action.\n     * @param action Information about the action being taken\n     * @param options Additional context about the action\n     */\n  async handleAgentAction(\n    action: { tool: string; toolInput: any; log?: string;[key: string]: any },\n    options: Record<string, any> = {}\n  ): Promise<void> {\n    await this.runHandlers(CallbackEventType.AGENT_ACTION, action, options);\n  }\n\n  /**\n     * Triggers the 'agent_finish' event when an agent completes its task.\n     * @param finish Information about the agent's final state\n     * @param options Additional context about the completion\n     */\n  async handleAgentFinish(\n    finish: { returnValues: any; log?: string;[key: string]: any },\n    options: Record<string, any> = {}\n  ): Promise<void> {\n    await this.runHandlers(CallbackEventType.AGENT_FINISH, finish, options);\n  }\n\n  /**\n     * Triggers the 'retriever_start' event when a retriever begins processing.\n     * @param retriever Information about the retriever\n     * @param query The query being processed\n     * @param options Additional context about the retrieval\n     */\n  async handleRetrieverStart(\n    retriever: { name: string; type?: string;[key: string]: any },\n    query: string,\n    options: Record<string, any> = {}\n  ): Promise<void> {\n    await this.runHandlers(CallbackEventType.RETRIEVER_START, retriever, query, options);\n  }\n\n  /**\n     * Triggers the 'retriever_end' event when a retriever completes processing.\n     * @param documents The documents retrieved\n     * @param options Additional context about the retrieval\n     */\n  async handleRetrieverEnd(documents: any[], options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.RETRIEVER_END, documents, options);\n  }\n\n  /**\n     * Triggers the 'retriever_error' event when a retriever encounters an error.\n     * @param error The error that occurred\n     * @param options Additional context about the error\n     */\n  async handleRetrieverError(error: Error | unknown, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.RETRIEVER_ERROR, error, options);\n  }\n\n  /**\n     * Triggers the 'memory_start' event when a memory operation begins.\n     * @param memory Information about the memory component\n     * @param operation The operation being performed (e.g., 'load', 'save')\n     * @param options Additional context about the memory operation\n     */\n  async handleMemoryStart(\n    memory: { name: string; type?: string;[key: string]: any },\n    operation: string,\n    options: Record<string, any> = {}\n  ): Promise<void> {\n    await this.runHandlers(CallbackEventType.MEMORY_START, memory, operation, options);\n  }\n\n  /**\n     * Triggers the 'memory_end' event when a memory operation completes.\n     * @param result The result of the memory operation\n     * @param options Additional context about the memory operation\n     */\n  async handleMemoryEnd(result: any, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.MEMORY_END, result, options);\n  }\n\n  /**\n     * Triggers the 'memory_error' event when a memory operation encounters an error.\n     * @param error The error that occurred\n     * @param options Additional context about the error\n     */\n  async handleMemoryError(error: Error | unknown, options: Record<string, any> = {}): Promise<void> {\n    await this.runHandlers(CallbackEventType.MEMORY_ERROR, error, options);\n  }\n\n  /**\n     * Triggers a custom event with the provided name and data.\n     * @param eventName The name of the custom event\n     * @param data The data to pass to the event handlers\n     */\n  async handleCustomEvent(eventName: string, data: any): Promise<void> {\n    await this.runHandlers(eventName, data, { customEvent: true });\n  }\n}"]}