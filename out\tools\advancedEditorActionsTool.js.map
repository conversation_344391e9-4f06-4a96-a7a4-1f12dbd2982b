{"version": 3, "file": "advancedEditorActionsTool.js", "sourceRoot": "", "sources": ["../../src/tools/advancedEditorActionsTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,6BAAwB;AAExB,MAAa,eAAe;IACjB,EAAE,GAAG,aAAa,CAAC;IACnB,IAAI,GAAG,cAAc,CAAC;IACtB,WAAW,GAAG,wCAAwC,CAAC;IACvD,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,SAAS,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;YAC1B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;YAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;SACnB,CAAC,CAAC,CAAC,QAAQ,CAAC,iCAAiC,CAAC;KAChD,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,iCAAiC,EAAE;SAC9K;QACD,QAAQ,EAAE,CAAC,WAAW,CAAC;KACxB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,SAAS,GAAG,KAAK,CAAC,SAA+C,CAAC;QACxE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAChG,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAClI,MAAM,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3G,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,SAAS,CAAC,MAAM,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IACpG,CAAC;CACF;AA3BD,0CA2BC;AAED,MAAa,aAAa;IACf,EAAE,GAAG,WAAW,CAAC;IACjB,IAAI,GAAG,WAAW,CAAC;IACnB,WAAW,GAAG,2BAA2B,CAAC;IAC1C,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC;QACtE,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC;KAC/D,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE;YAC5F,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wBAAwB,EAAE;SAChE;QACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;KACrB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;QACtC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;QAClC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAChG,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3D,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC/C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACxF,CAAC;aAAM,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3D,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;YACvE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACrF,CAAC;aAAM,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;YAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAChE,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACzF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1F,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACvG,CAAC;IACH,CAAC;CACF;AAxCD,sCAwCC;AAED,MAAa,aAAa;IACf,EAAE,GAAG,WAAW,CAAC;IACjB,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,gDAAgD,CAAC;IAC/D,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;YACtB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;YACpB,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;gBACd,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;oBACd,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;oBAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;iBACnB,CAAC;gBACF,GAAG,EAAE,OAAC,CAAC,MAAM,CAAC;oBACZ,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;oBAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;iBACnB,CAAC;aACH,CAAC;YACF,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC;KAChC,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,KAAK,EAAE;gBACL,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,iBAAiB,EAAE;aAC5X;SACF;QACD,QAAQ,EAAE,CAAC,OAAO,CAAC;KACpB,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,KAAK,GAAG,KAAK,CAAC,KAAwI,CAAC;QAC7J,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAoB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACtH,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YACzD,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC9B,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAClF,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC5E,WAAW,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,KAAK,CAAC,MAAM,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAClG,CAAC;CACF;AA9CD,sCA8CC;AAED,MAAa,eAAe;IACjB,EAAE,GAAG,aAAa,CAAC;IACnB,IAAI,GAAG,kBAAkB,CAAC;IAC1B,WAAW,GAAG,kDAAkD,CAAC;IACjE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;QACxD,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC;QACnD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;QACjD,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;KACvD,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE;YACpE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wBAAwB,EAAE;YAC/D,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mBAAmB,EAAE;YAC7D,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE;SACxD;QACD,QAAQ,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC;KAC1C,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;QACnD,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,OAAO,KAAK,SAAS;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC3I,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC9D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACzD,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;QAC3B,IAAI,OAAe,CAAC;QACpB,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9F,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC9B,WAAW,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,IAAI,WAAW,OAAO,QAAQ,QAAQ,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IACxH,CAAC;CACF;AAzCD,0CAyCC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../types/agent';\nimport { z } from 'zod';\n\nexport class MultiCursorTool implements ITool {\n  readonly id = 'multiCursor';\n  readonly name = 'Multi-Cursor';\n  readonly description = 'Add, remove, or move multiple cursors.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    positions: z.array(z.object({\n      line: z.number(),\n      column: z.number()\n    })).describe('Array of positions for cursors.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      positions: { type: 'array', items: { type: 'object', properties: { line: { type: 'number' }, column: { type: 'number' } } }, description: 'Array of positions for cursors.' }\n    },\n    required: ['positions']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const positions = input.positions as { line: number; column: number }[];\n    const editor = vscode.window.activeTextEditor;\n    if (!editor) return { success: false, error: 'No active editor.', toolId: this.id, actionName };\n    if (!positions || positions.length === 0) return { success: false, error: 'No positions provided.', toolId: this.id, actionName };\n    editor.selections = positions.map(pos => new vscode.Selection(pos.line, pos.column, pos.line, pos.column));\n    return { success: true, output: `Set ${positions.length} cursors.`, toolId: this.id, actionName };\n  }\n}\n\nexport class ClipboardTool implements ITool {\n  readonly id = 'clipboard';\n  readonly name = 'Clipboard';\n  readonly description = 'Cut, copy, or paste text.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    action: z.enum(['cut', 'copy', 'paste']).describe('Clipboard action.'),\n    text: z.string().optional().describe('Text to copy or paste.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      action: { type: 'string', enum: ['cut', 'copy', 'paste'], description: 'Clipboard action.' },\n      text: { type: 'string', description: 'Text to copy or paste.' }\n    },\n    required: ['action']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const action = input.action as string;\n    const text = input.text as string;\n    const editor = vscode.window.activeTextEditor;\n    if (!editor) return { success: false, error: 'No active editor.', toolId: this.id, actionName };\n    if (action === 'copy') {\n      const selected = editor.document.getText(editor.selection);\n      await vscode.env.clipboard.writeText(selected);\n      return { success: true, output: 'Copied to clipboard.', toolId: this.id, actionName };\n    } else if (action === 'cut') {\n      const selected = editor.document.getText(editor.selection);\n      await vscode.env.clipboard.writeText(selected);\n      await editor.edit(editBuilder => editBuilder.delete(editor.selection));\n      return { success: true, output: 'Cut to clipboard.', toolId: this.id, actionName };\n    } else if (action === 'paste') {\n      const pasteText = text || await vscode.env.clipboard.readText();\n      await editor.edit(editBuilder => editBuilder.insert(editor.selection.active, pasteText));\n      return { success: true, output: 'Pasted from clipboard.', toolId: this.id, actionName };\n    } else {\n      return { success: false, error: `Unknown clipboard action: ${action}`, toolId: this.id, actionName };\n    }\n  }\n}\n\nexport class BatchEditTool implements ITool {\n  readonly id = 'batchEdit';\n  readonly name = 'Batch Edit';\n  readonly description = 'Perform multiple edits across files/locations.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    edits: z.array(z.object({\n      filePath: z.string(),\n      range: z.object({\n        start: z.object({\n          line: z.number(),\n          column: z.number()\n        }),\n        end: z.object({\n          line: z.number(),\n          column: z.number()\n        })\n      }),\n      text: z.string()\n    })).describe('Array of edits.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      edits: {\n        type: 'array', items: { type: 'object', properties: { filePath: { type: 'string' }, range: { type: 'object', properties: { start: { type: 'object', properties: { line: { type: 'number' }, column: { type: 'number' } } }, end: { type: 'object', properties: { line: { type: 'number' }, column: { type: 'number' } } } }, text: { type: 'string' } } }, description: 'Array of edits.' }\n      },\n    },\n    required: ['edits']\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const edits = input.edits as { filePath: string; range: { start: { line: number; column: number }, end: { line: number; column: number } }, text: string }[];\n    if (!edits || edits.length === 0) return { success: false, error: 'No edits provided.', toolId: this.id, actionName };\n    for (const edit of edits) {\n      const doc = await vscode.workspace.openTextDocument(edit.filePath);\n      const editor = await vscode.window.showTextDocument(doc);\n      await editor.edit(editBuilder => {\n        const start = new vscode.Position(edit.range.start.line, edit.range.start.column);\n        const end = new vscode.Position(edit.range.end.line, edit.range.end.column);\n        editBuilder.replace(new vscode.Range(start, end), edit.text);\n      });\n    }\n    return { success: true, output: `Applied ${edits.length} edits.`, toolId: this.id, actionName };\n  }\n}\n\nexport class FindReplaceTool implements ITool {\n  readonly id = 'findReplace';\n  readonly name = 'Find and Replace';\n  readonly description = 'Find and replace text in a file or across files.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    filePath: z.string().describe('File to search/replace.'),\n    find: z.string().describe('Text or regex to find.'),\n    replace: z.string().describe('Replacement text.'),\n    isRegex: z.boolean().optional().describe('Use regex?')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      filePath: { type: 'string', description: 'File to search/replace.' },\n      find: { type: 'string', description: 'Text or regex to find.' },\n      replace: { type: 'string', description: 'Replacement text.' },\n      isRegex: { type: 'boolean', description: 'Use regex?' }\n    },\n    required: ['filePath', 'find', 'replace']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const { filePath, find, replace, isRegex } = input;\n    if (!filePath || !find || replace === undefined) return { success: false, error: 'Missing required fields.', toolId: this.id, actionName };\n    const doc = await vscode.workspace.openTextDocument(filePath);\n    const editor = await vscode.window.showTextDocument(doc);\n    const text = doc.getText();\n    let newText: string;\n    if (isRegex) {\n      newText = text.replace(new RegExp(find, 'g'), replace);\n    } else {\n      newText = text.split(find).join(replace);\n    }\n    const start = new vscode.Position(0, 0);\n    const end = new vscode.Position(doc.lineCount - 1, doc.lineAt(doc.lineCount - 1).text.length);\n    await editor.edit(editBuilder => {\n      editBuilder.replace(new vscode.Range(start, end), newText);\n    });\n    return { success: true, output: `Replaced '${find}' with '${replace}' in ${filePath}.`, toolId: this.id, actionName };\n  }\n}\n"]}