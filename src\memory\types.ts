import * as vscode from 'vscode';

/**
 * Memory System - Types, Interfaces, and Main Export Point
 *
 * This file serves as both the type definitions and the centralized export point
 * for all memory system components. Import from this file to access any memory
 * system functionality throughout the Codessa extension.
 *
 * Usage:
 * ```typescript
 * import { memoryManager, MemoryEntry, MemorySource } from '../memory/types';
 * import { initializeMemorySystem, isMemorySystemReady } from '../memory/types';
 * ```
 */

// Core memory entry interface - UNIFIED VERSION
export interface MemoryEntry {
    id: string;
    content: string;
    timestamp: number;
    embedding?: number[];
    metadata: MemoryMetadata;
}

// Comprehensive metadata interface
export interface MemoryMetadata {
    source: MemorySource;
    type: MemoryType;
    tags?: string[];
    agentId?: string;
    agentName?: string;
    namespace?: string;
    filePath?: string;
    lineNumber?: number;
    functionName?: string;
    className?: string;
    projectContext?: string;
    version?: number;
    changeType?: 'create' | 'update' | 'delete' | 'refactor';
    confidence?: number;
    importance?: number;
    [key: string]: string | number | boolean | string[] | undefined;
}

// Memory source types - UNIFIED
export type MemorySource =
    | 'conversation'
    | 'temporal'
    | 'user'
    | 'system'
    | 'agent'
    | 'file'
    | 'code'
    | 'documentation'
    | 'collaboration'
    | 'prediction'
    | 'workspace'
    | 'database';

// Memory content types - UNIFIED
export type MemoryType =
    | 'text'
    | 'semantic'
    | 'episodic'
    | 'procedural'
    | 'human'
    | 'ai'
    | 'system'
    | 'code'
    | 'insight'
    | 'pattern'
    | 'conversation'
    | 'project'
    | 'user_preference'
    | 'document'
    | 'image'
    | 'audio'
    | 'video'
    | 'binary';

// Unified search options interface - ENHANCED
export interface MemorySearchOptions {
    query: string;
    limit?: number;
    offset?: number;
    threshold?: number;
    relevanceThreshold?: number;
    filter?: MemoryFilter;
    sortBy?: 'timestamp' | 'relevance' | 'importance';
    sortOrder?: 'asc' | 'desc';
    includeMetadata?: boolean;
    contextual?: boolean;
}

// Comprehensive filter interface - UNIFIED
export interface MemoryFilter {
    source?: MemorySource | MemorySource[];
    type?: MemoryType | MemoryType[];
    tags?: string[];
    agentId?: string;
    namespace?: string;
    filePath?: string;
    fromTimestamp?: number;
    toTimestamp?: number;
    minConfidence?: number;
    minImportance?: number;
    changeType?: 'create' | 'update' | 'delete' | 'refactor';
    [key: string]: string | number | boolean | string[] | Date | undefined;
}

/**
 * Comprehensive Memory Settings Interface - UNIFIED
 */
export interface MemorySettings {
    // Core settings
    enabled: boolean;
    system: 'basic' | 'codessa' | 'quantum';
    maxMemories: number;
    maxMemoriesPerFile: number;
    memoryRetentionDays: number;
    relevanceThreshold: number;
    contextWindowSize: number;
    conversationHistorySize: number;

    // Phase 4 settings
    enableQuantumMemory: boolean;
    enablePredictiveInsights: boolean;
    enableMemoryVisualization: boolean;
    enableCollaborativeMemory: boolean;

    // Vector store settings
    vectorStore: 'memory' | 'chroma' | 'pinecone' | 'weaviate' | 'hnswlib';
    vectorStoreSettings: {
        chroma: {
            directory: string;
            collectionName: string;
        };
        pinecone: {
            apiKey: string;
            environment: string;
            indexName: string;
        };
    };

    // Database settings
    database: 'sqlite' | 'mysql' | 'postgres' | 'mongodb' | 'redis';
    databaseSettings: {
        sqlite: {
            filename: string;
        };
        mysql: {
            host: string;
            port: number;
            user: string;
            password: string;
            database: string;
            table: string;
        };
        postgres: {
            connectionString: string;
            schema: string;
        };
        mongodb: {
            connectionString: string;
            database: string;
            collection: string;
        };
        redis: {
            url: string;
            keyPrefix: string;
        };
    };

    // File processing settings
    fileChunking: {
        chunkSize: number;
        chunkOverlap: number;
        maxChunksPerFile: number;
    };

    // Performance settings
    cacheSize: number;
    cacheExpiry: number;
    batchSize: number;
    maxConcurrentOperations: number;
}

/**
 * Interface for memory operations needed by tools
 * This breaks circular dependencies between MemoryManager and tools
 */
export interface IMemoryOperations {
  addMemory(memory: Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry>;
  getMemory(id: string): Promise<MemoryEntry | undefined>;
  getMemories(limit?: number): Promise<MemoryEntry[]>;
  deleteMemory(id: string): Promise<boolean>;
  clearMemories(): Promise<void>;
  searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]>;
  searchSimilarMemories(query: string, options?: Partial<MemorySearchOptions>): Promise<MemoryEntry[]>;
  updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined>;
  getMemorySettings(): MemorySettings;
  updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean>;
  registerMemoryChangeListener: (listener: () => void) => void;
}

/**
 * Base memory provider interface
 */
export interface IMemoryProvider {
    /**
     * Initialize the memory provider
     */
    initialize(context: vscode.ExtensionContext): Promise<void>;

    /**
     * Add a memory
     */
    addMemory(memory: Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry>;

    /**
     * Get all memories
     */
    getMemories(): Promise<MemoryEntry[]>;

    /**
     * Get a memory by ID
     */
    getMemory(id: string): Promise<MemoryEntry | undefined>;

    /**
     * Delete a memory by ID
     */
    deleteMemory(id: string): Promise<boolean>;

    /**
     * Clear all memories
     */
    clearMemories(): Promise<void>;

    /**
     * Search memories
     */
    searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]>;

    /**
     * Search memories by semantic similarity
     */
    searchSimilarMemories(query: string, options?: Partial<MemorySearchOptions>): Promise<MemoryEntry[]>;

    /**
     * Get memory settings
     */
    getMemorySettings(): MemorySettings;

    /**
     * Update memory settings
     */
    updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean>;

    /**
     * Update an existing memory
     * @param id The ID of the memory to update
     * @param updates The fields to update
     * @returns The updated memory entry, or undefined if not found
     */
    updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined>;
}

/**
 * Vector store interface
 */
export interface IVectorStore {
    /**
     * Initialize the vector store
     */
    initialize(): Promise<void>;

    /**
     * Add a vector
     */
    addVector(id: string, vector: number[], metadata?: Record<string, string | number | boolean | undefined>): Promise<void>;

    /**
     * Get a vector by ID
     */
    getVector(id: string): Promise<number[] | undefined>;

    /**
     * Delete a vector by ID
     */
    deleteVector(id: string): Promise<boolean>;

    /**
     * Clear all vectors
     */
    clearVectors(): Promise<void>;

    /**
     * Search for similar vectors
     */
    searchSimilarVectors(vector: number[], limit?: number, filter?: Record<string, string | number | boolean | undefined>): Promise<Array<{id: string, score: number}>>;

    /**
     * Get a retriever interface for this vector store
     */
    asRetriever?(k?: number): { getRelevantDocuments: (query: string) => Promise<Array<{ pageContent: string; metadata: Record<string, unknown> }>> };
}

/**
 * Database interface
 */
export interface IDatabase {
    /**
     * Initialize the database
     */
    initialize(): Promise<void>;

    /**
     * Add a record
     */
    addRecord(collection: string, record: Record<string, unknown>): Promise<string>;

    /**
     * Get a record by ID
     */
    getRecord(collection: string, id: string): Promise<Record<string, unknown> | undefined>;

    /**
     * Get multiple records by IDs
     */
    getRecordsByIds?(collection: string, ids: string[]): Promise<Record<string, unknown>[]>;

    /**
     * Update a record
     */
    updateRecord(collection: string, id: string, record: Record<string, unknown>): Promise<boolean>;

    /**
     * Delete a record
     */
    deleteRecord(collection: string, id: string): Promise<boolean>;

    /**
     * Delete multiple records
     */
    deleteRecords?(collection: string, query: Record<string, unknown>): Promise<number>;

    /**
     * Query records
     */
    queryRecords(collection: string, query: Record<string, unknown>, limit?: number, sort?: Record<string, 1 | -1>): Promise<Record<string, unknown>[]>;

    /**
     * Clear all records in a collection
     */
    clearCollection(collection: string): Promise<void>;

    /**
     * Ensure a collection exists
     */
    ensureCollection?(collection: string): Promise<void>;

    /**
     * Check if the database supports text search
     */
    readonly supportsTextSearch?: boolean;
}

// Additional interfaces for Phase 4 integration
export interface SharedMemoryItem {
    id: string;
    memoryId: string;
    sharedBy: string;
    sharedWith: string[];
    permissions: 'read' | 'write' | 'admin';
    sharedAt: number;
    expiresAt?: number;
    context: string;
    memory: MemoryEntry;
}

export interface MemoryAnalytics {
    totalMemories: number;
    temporalMemories: number;
    collaborativeShares: number;
    predictiveInsights: number;
    memoryGrowthRate: number;
    averageRetrievalTime: number;
    mostAccessedMemories: string[];
    memoryPatterns: {
        commonTags: string[];
        frequentContexts: string[];
        peakUsageTimes: number[];
    };
    userBehaviorInsights: {
        preferredMemoryTypes: string[];
        searchPatterns: string[];
        collaborationFrequency: number;
    };
}

export interface MemoryVisualization {
    nodes: MemoryNode[];
    edges: MemoryEdge[];
    clusters: MemoryCluster[];
    temporalFlow: TemporalFlow[];
    metadata: VisualizationMetadata;
}

export interface MemoryNode {
    id: string;
    label: string;
    type: string;
    timestamp: number;
    context: string;
    size: number;
    color: string;
    importance: number;
}

export interface MemoryEdge {
    from: string;
    to: string;
    type: 'version' | 'context' | 'temporal' | 'collaboration';
    label: string;
    weight?: number;
    strength?: number;
}

export interface MemoryCluster {
    id: string;
    name: string;
    members: string[];
    type: 'file-based' | 'temporal' | 'semantic' | 'collaborative';
    strength: number;
}

export interface TemporalFlow {
    from: string;
    to: string;
    timeDelta: number;
    type: 'temporal';
    strength: number;
}

export interface VisualizationMetadata {
    totalNodes: number;
    totalEdges: number;
    centerNode?: string;
    generatedAt: number;
    timespan?: {
        start: number;
        end: number;
        duration: number;
    };
    complexity: number;
}

/**
 * Chat message interface
 */
/**
 * Memory access log entry
 */
export interface MemoryAccessLog {
    id: string;
    memoryId: string;
    agentId: string;
    action: 'read' | 'write' | 'share' | 'revoke';
    timestamp: number;
    success: boolean;
    error?: string;
    metadata?: Record<string, unknown>;
}

/**
 * Chat message interface
 */
export interface ChatMessage {
    id: string;
    sessionId: string;
    type: 'human' | 'ai' | 'system' | 'unknown';
    content: string;
    timestamp: number;
    metadata?: Record<string, unknown>;
}

// ============================================================================
// MEMORY SYSTEM EXPORTS - Centralized Export Point
// ============================================================================

// Main Memory Manager (Primary Integration Point)
export { memoryManager, MemoryManager } from './memoryManager';

// Vector Memory System
export { vectorMemoryManager } from './vectorMemory';
export type { VectorMemoryManager } from './vectorMemory';

// Agent Memory Integration
export { AgentMemory, getAgentMemory } from './agentMemory';

// Memory Configuration
export { getMemoryEnabled, setMemoryEnabled } from './memoryConfig';

// Codessa Memory Provider (Advanced Memory System)
export { codessaMemoryProvider, CodessaMemoryProvider } from './codessa/codessaMemory';

// Codessa Graph Memory
export { CodessaGraphMemory } from './codessa/codessaGraphMemory';

// File Chunking Service
export { FileChunkingService } from './codessa/fileChunking';

// Database Implementations
export { DatabaseFactory } from './codessa/databases/databaseFactory';
export { SQLiteDatabase } from './codessa/databases/sqliteDatabase';
export { PostgresDatabase } from './codessa/databases/postgresDatabase';
export { MySQLDatabase } from './codessa/databases/mysqlDatabase';
export { MongoDBDatabase } from './codessa/databases/mongodbDatabase';
export { RedisDatabase } from './codessa/databases/redisDatabase';

// Vector Store Implementations
export { VectorStoreFactory } from './codessa/vectorStores/vectorStoreFactory';
export { MemoryVectorStore } from './codessa/vectorStores/memoryVectorStore';
export { ChromaVectorStore } from './codessa/vectorStores/chromaVectorStore';
export { PineconeVectorStore } from './codessa/vectorStores/pineconeVectorStore';

// Quantum Memory System (Advanced)
export { QuantumMemorySystem } from './quantum/quantumMemorySystem';

// ============================================================================
// MEMORY SYSTEM HELPER FUNCTIONS
// ============================================================================

/**
 * Memory System Initialization Helper
 *
 * Provides a convenient way to initialize the entire memory system
 * with proper error handling and fallbacks.
 */
export async function initializeMemorySystem(context: vscode.ExtensionContext): Promise<boolean> {
  try {
    const { memoryManager } = await import('./memoryManager');
    await memoryManager.initialize(context);
    return true;
  } catch (error) {
    console.error('Failed to initialize memory system:', error);
    return false;
  }
}

/**
 * Memory System Status Check
 *
 * Provides a way to check if the memory system is properly initialized
 * and ready for use.
 */
export async function isMemorySystemReady(): Promise<boolean> {
  try {
    const { memoryManager } = await import('./memoryManager');
    return memoryManager.isInitialized();
  } catch (error) {
    console.error('Failed to check memory system status:', error);
    return false;
  }
}

/**
 * Get Memory System Analytics
 *
 * Provides access to memory system analytics and statistics.
 */
export async function getMemorySystemAnalytics(): Promise<MemoryAnalytics | null> {
  try {
    const { memoryManager } = await import('./memoryManager');
    return memoryManager.getMemoryAnalytics();
  } catch (error) {
    console.error('Failed to get memory system analytics:', error);
    return null;
  }
}
