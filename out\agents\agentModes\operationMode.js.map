{"version": 3, "file": "operationMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/operationMode.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC;;GAEG;AACH,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,4BAAa,CAAA;IACb,kDAAmC,CAAA;IACnC,gDAAiC,CAAA;IACjC,4CAA6B,CAAA;IAC7B,gCAAiB,CAAA;AACnB,CAAC,EANW,WAAW,2BAAX,WAAW,QAMtB;AAqDD;;GAEG;AACH,MAAsB,aAAa;IACjC,qCAAqC;IACrB,EAAE,CAAS;IAC3B,gCAAgC;IAChB,WAAW,CAAS;IACpC,8BAA8B;IACd,WAAW,CAAS;IACpC,wBAAwB;IACR,IAAI,CAAS;IAC7B,yCAAyC;IACzB,kBAAkB,CAAc;IAChD,yEAAyE;IAC/D,aAAa,CAAwB;IAC/C,qCAAqC;IAC3B,MAAM,GAAG,CAAC,OAAQ,UAAkB,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAE,UAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;IAEpK,YACE,EAAU,EACV,WAAmB,EACnB,WAAmB,EACnB,IAAY,EACZ,qBAAkC,WAAW,CAAC,YAAY;QAE1D,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC/C,CAAC;IAED;;;SAGK;IACL,KAAK,CAAC,UAAU,CAAC,OAAgC;QAC/C,2DAA2D;QAC3D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC5F,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,QAAQ,CAAC;YACnE,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,WAAW,IAAI,CAAC,WAAW,OAAO,CAAC;YAChE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QACjC,CAAC;IACH,CAAC;IAOD;;OAEG;IACH,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;YACf,aAAa,EAAE,EAAE;YACjB,IAAI,EAAE,MAAM;SACb,CAAC;IACJ,CAAC;CACF;AAxED,sCAwEC;AAGD;;GAEG;AACH,MAAM,qBAAqB;IACjB,KAAK,GAAgC,IAAI,GAAG,EAAE,CAAC;IAC/C,aAAa,CAAqB;IAE1C;;SAEK;IACL,YAAY,CAAC,IAAoB;QAC/B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;SAEK;IACL,OAAO,CAAC,EAAU;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;SAEK;IACL,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;SAEK;IACL,cAAc,CAAC,EAAU;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;SAEK;IACL,cAAc;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC7E,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,eAAe,CAAC,OAAgC;QACpD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,IAAI,OAAQ,IAAY,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;gBACnD,MAAO,IAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAED,8BAA8B;AACjB,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Agent } from '../agentUtilities/agent';\n\n/**\n * Context type for operation modes\n */\nexport enum ContextType {\n  NONE = 'none',\n  ENTIRE_CODEBASE = 'entire_codebase',\n  SELECTED_FILES = 'selected_files',\n  CURRENT_FILE = 'current_file',\n  CUSTOM = 'custom'\n}\n\n/**\n * Context source for operation modes\n */\nexport interface ContextSource {\n  type: ContextType;\n  files?: string[];\n  folders?: string[];\n  externalResources?: string[];\n  customContent?: string;\n}\n\n/**\n * Operation mode interface\n */\nexport interface IOperationMode {\n  /**\n   * Unique identifier for the mode\n   */\n  readonly id: string;\n\n  /**\n   * Display name for the mode\n   */\n  readonly displayName: string;\n\n  /**\n   * Description of the mode\n   */\n  readonly description: string;\n\n  /**\n   * Icon for the mode\n   */\n  readonly icon: string;\n\n  /**\n   * Default context type for this mode\n   */\n  readonly defaultContextType: ContextType;\n\n  /**\n   * Process a message in this mode\n   */\n  processMessage(message: string, agent: Agent, context: ContextSource): Promise<string>;\n\n  /**\n   * Get LLM parameters for this mode\n   */\n  getLLMParams(): LLMGenerateParams;\n}\n\n/**\n * Base class for operation modes\n */\nexport abstract class OperationMode implements IOperationMode {\n  /** Unique identifier for the mode */\n  public readonly id: string;\n  /** Display name for the mode */\n  public readonly displayName: string;\n  /** Description of the mode */\n  public readonly description: string;\n  /** Icon for the mode */\n  public readonly icon: string;\n  /** Default context type for this mode */\n  public readonly defaultContextType: ContextType;\n  /** Status bar item for the mode (optional, can be used by subclasses) */\n  protected statusBarItem?: vscode.StatusBarItem;\n  /** Logger instance for subclasses */\n  protected logger = (typeof (globalThis as any).Logger !== 'undefined' ? (globalThis as any).Logger.instance : undefined) || require('../../logger').Logger.instance;\n\n  constructor(\n    id: string,\n    displayName: string,\n    description: string,\n    icon: string,\n    defaultContextType: ContextType = ContextType.CURRENT_FILE\n  ) {\n    this.id = id;\n    this.displayName = displayName;\n    this.description = description;\n    this.icon = icon;\n    this.defaultContextType = defaultContextType;\n  }\n\n  /**\n     * Initialize the mode. Subclasses can override to provide custom setup.\n     * @param context Extension context\n     */\n  async initialize(context: vscode.ExtensionContext): Promise<void> {\n    // Default: set up a status bar item if not already present\n    if (!this.statusBarItem) {\n      this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);\n      this.statusBarItem.text = `${this.icon} ${this.displayName}: Idle`;\n      this.statusBarItem.tooltip = `Codessa ${this.displayName} Mode`;\n      context.subscriptions.push(this.statusBarItem);\n    }\n  }\n\n  /**\n     * Dispose of any resources held by this mode. Subclasses can override.\n     */\n  async dispose(): Promise<void> {\n    if (this.statusBarItem) {\n      this.statusBarItem.dispose();\n      this.statusBarItem = undefined;\n    }\n  }\n\n  /**\n   * Process a message in this mode (must be implemented by subclass)\n   */\n  abstract processMessage(message: string, agent: Agent, context: ContextSource): Promise<string>;\n\n  /**\n   * Get LLM parameters for this mode\n   */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.7,\n      maxTokens: 2000,\n      stopSequences: [],\n      mode: 'task'\n    };\n  }\n}\n\n\n/**\n * Registry for operation modes\n */\nclass OperationModeRegistry {\n  private modes: Map<string, IOperationMode> = new Map();\n  private defaultModeId: string | undefined;\n\n  /**\n     * Register a mode\n     */\n  registerMode(mode: IOperationMode): void {\n    this.modes.set(mode.id, mode);\n  }\n\n  /**\n     * Get a mode by ID\n     */\n  getMode(id: string): IOperationMode | undefined {\n    return this.modes.get(id);\n  }\n\n  /**\n     * Get all registered modes\n     */\n  getAllModes(): IOperationMode[] {\n    return Array.from(this.modes.values());\n  }\n\n  /**\n     * Set the default mode by ID\n     */\n  setDefaultMode(id: string): void {\n    if (this.modes.has(id)) {\n      this.defaultModeId = id;\n    } else {\n      throw new Error(`Mode with id '${id}' not registered.`);\n    }\n  }\n\n  /**\n     * Get the default mode (if any)\n     */\n  getDefaultMode(): IOperationMode | undefined {\n    return this.defaultModeId ? this.modes.get(this.defaultModeId) : undefined;\n  }\n\n  /**\n     * Initialize all registered modes\n     */\n  async initializeModes(context: vscode.ExtensionContext): Promise<void> {\n    for (const mode of this.modes.values()) {\n      if (typeof (mode as any).initialize === 'function') {\n        await (mode as any).initialize(context);\n      }\n    }\n  }\n}\n\n// Export a singleton instance\nexport const operationModeRegistry = new OperationModeRegistry();"]}