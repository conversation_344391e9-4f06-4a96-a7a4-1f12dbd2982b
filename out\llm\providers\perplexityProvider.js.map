{"version": 3, "file": "perplexityProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/perplexityProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAEpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;GAEG;AACH,MAAa,oBAAqB,SAAQ,iCAAe;IAC9C,UAAU,GAAG,YAAY,CAAC;IAC1B,WAAW,GAAG,eAAe,CAAC;IAC9B,WAAW,GAAG,6DAA6D,CAAC;IAC5E,OAAO,GAAG,4BAA4B,CAAC;IACvC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,2BAA2B,CAAC;IAC9C,YAAY,GAAG,qBAAqB,CAAC;IAEtC,MAAM,GAAQ,IAAI,CAAC;IAE3B,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBAClF,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,MAAM,EAAE,oBAAoB;gBACrC,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,MAAM,EAAE;oBACnC,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACxE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,MAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,uDAAuD,EAAE,CAAC;QACzF,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,uCAAuC;YACvC,MAAM,QAAQ,GAA8E,EAAE,CAAC;YAE/F,iCAAiC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,MAAM,CAAC,YAAY;iBACpB,CAAC,CAAC;YACd,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;iBACd,CAAC,CAAC;YACd,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;wBAC7D,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBAChH,CAAC;YACH,CAAC;YAED,oCAAoC;YACpC,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAE7C,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACpC,IAAI,EAAE,MAAM,CAAC,aAAa;gBAC1B,MAAM,EAAE,SAAS;aAClB,EAAE;gBACD,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,qBAAqB;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;gBAChD,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,IAAI,MAAM;gBACvD,KAAK,EAAE,MAAM,CAAC,KAAK;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,YAAY,GAAG,gCAAgC,CAAC;YAEpD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,yBAAyB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACnH,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,4EAA4E;QAC5E,OAAO;YACL;gBACE,EAAE,EAAE,qBAAqB;gBACzB,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,oDAAoD;gBACjE,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,mCAAmC;gBAChD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,oBAAoB;gBACxB,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,6CAA6C;gBAC1D,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,kBAAkB;gBACtB,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,4BAA4B;gBACzC,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,wBAAwB;gBAC5B,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,6CAA6C;gBAC1D,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,2CAA2C;gBACxD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,+DAA+D;aACzE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBAC9C,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,8DAA8D,OAAO,IAAI;iBACnF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uDAAuD;iBACjE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YACnE,IAAI,YAAY,GAAG,qCAAqC,CAAC;YAEzD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,uEAAuE;gBACpF,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,kEAAkE;gBAC/E,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,qCAAqC;gBAClD,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,qBAAqB;oBACrB,mBAAmB;oBACnB,oBAAoB;oBACpB,kBAAkB;oBAClB,wBAAwB;oBACxB,uBAAuB;iBACxB;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAlSD,oDAkSC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { <PERSON><PERSON><PERSON><PERSON>ider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult, LLMModelInfo } from '../llmProvider';\nimport { Logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for Perplexity AI models\n */\nexport class PerplexityAIProvider extends BaseLLMProvider {\n  readonly providerId = 'perplexity';\n  readonly displayName = 'Perplexity AI';\n  readonly description = 'Online models with strong search and reasoning capabilities';\n  readonly website = 'https://www.perplexity.ai/';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api.perplexity.ai';\n  readonly defaultModel = 'sonar-medium-online';\n\n  private client: any = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        Logger.instance.info('Perplexity configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (!apiKey) {\n      Logger.instance.warn('API key not set for Perplexity provider.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 120000, // 2 minutes timeout\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      Logger.instance.info('Perplexity client initialized successfully.');\n    } catch (error) {\n      Logger.instance.error('Failed to initialize Perplexity client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using Perplexity models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    _tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'Perplexity provider not configured (API key missing?)' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n            \n      // Prepare messages for chat completion\n      const messages: { role: string; content: string; name?: string; tool_call_id?: string }[] = [];\n            \n      // Add system message if provided\n      if (params.systemPrompt) {\n        messages.push({\n          role: 'system',\n          content: params.systemPrompt\n        } as const);\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        messages.push(...params.history);\n      } else {\n        // Just add the user prompt\n        messages.push({\n          role: 'user',\n          content: params.prompt\n        } as const);\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            Logger.instance.info('Perplexity request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          Logger.instance.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Determine if we should use search\n      const useSearch = modelId.includes('online');\n\n      // Make the API request\n      const response = await this.client.post('/chat/completions', {\n        model: modelId,\n        messages: messages,\n        temperature: params.temperature ?? 0.7,\n        max_tokens: params.maxTokens ?? 1024,\n        stop: params.stopSequences,\n        search: useSearch\n      }, {\n        signal: abortController?.signal\n      });\n\n      // Parse the response\n      const result = response.data;\n            \n      return {\n        content: result.choices[0].message.content || '',\n        finishReason: result.choices[0].finish_reason || 'stop',\n        usage: result.usage\n      };\n    } catch (error: any) {\n      Logger.instance.error('Perplexity generate error:', error);\n      let errorMessage = 'Failed to call Perplexity API.';\n\n      if (error.response) {\n        errorMessage = `Perplexity API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available Perplexity models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    // Perplexity doesn't have a models endpoint, so we return a predefined list\n    return [\n      {\n        id: 'sonar-medium-online',\n        name: 'Sonar Medium Online',\n        description: 'Medium-sized model with online search capabilities',\n        contextWindow: 4096,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'sonar-medium-chat',\n        name: 'Sonar Medium Chat',\n        description: 'Medium-sized model without search',\n        contextWindow: 4096,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'sonar-small-online',\n        name: 'Sonar Small Online',\n        description: 'Small model with online search capabilities',\n        contextWindow: 4096,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'sonar-small-chat',\n        name: 'Sonar Small Chat',\n        description: 'Small model without search',\n        contextWindow: 4096,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'codellama-70b-instruct',\n        name: 'CodeLlama 70B Instruct',\n        description: 'Large model specialized for code generation',\n        contextWindow: 4096,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'mixtral-8x7b-instruct',\n        name: 'Mixtral 8x7B Instruct',\n        description: 'Mixtral MoE model with strong performance',\n        contextWindow: 4096,\n        pricingInfo: 'Paid'\n      }\n    ];\n  }\n\n  /**\n     * Test connection to Perplexity\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'Perplexity client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Simple test request to check if the API is working\n      const response = await this.client.post('/chat/completions', {\n        model: modelId,\n        messages: [{ role: 'user', content: 'Hello' }],\n        max_tokens: 10\n      });\n\n      if (response.data && response.data.choices) {\n        return {\n          success: true,\n          message: `Successfully connected to Perplexity API and tested model '${modelId}'.`\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Connected to API but received an unexpected response.'\n        };\n      }\n    } catch (error: any) {\n      Logger.instance.error('Perplexity connection test failed:', error);\n      let errorMessage = 'Failed to connect to Perplexity API';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your Perplexity API key (from https://www.perplexity.ai/settings/api)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The Perplexity API endpoint (default: https://api.perplexity.ai)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default Perplexity model to use',\n        required: false,\n        type: 'select',\n        options: [\n          'sonar-medium-online',\n          'sonar-medium-chat',\n          'sonar-small-online',\n          'sonar-small-chat',\n          'codellama-70b-instruct',\n          'mixtral-8x7b-instruct'\n        ]\n      }\n    ];\n  }\n}\n"]}