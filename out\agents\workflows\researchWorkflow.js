"use strict";
/**
 * Codessa Research Workflow
 *
 * This module provides workflow templates for research-focused tasks:
 * - Information gathering
 * - Source evaluation
 * - Data analysis
 * - Synthesis and reporting
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createResearchWorkflow = createResearchWorkflow;
exports.createAcademicResearchWorkflow = createAcademicResearchWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
const codessaMemory_1 = require("../../memory/codessa/codessaMemory");
/**
 * Create a Research workflow for in-depth research tasks
 */
function createResearchWorkflow(id, name, description, researchAgent, analysisAgent, summaryAgent, tools = []) {
    logger_1.logger.info(`Creating Research workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const queryAnalysisNode = graph_1.Codessa.createAgentNode('query-analysis', 'Query Analysis', researchAgent);
    const informationRetrievalNode = graph_1.Codessa.createAgentNode('information-retrieval', 'Information Retrieval', researchAgent);
    // Create a memory manager for the research workflow
    const researchMemoryManager = {
        async storeResearchQuery(query) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(query),
                    metadata: {
                        source: 'user',
                        type: 'text',
                        timestamp: new Date().toISOString(),
                        tags: ['research', 'query']
                    }
                });
                logger_1.logger.info('Saved research query to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save research query to memory:', error);
            }
        },
        async retrieveRelevantInformation(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5
                });
                const information = memories.map(m => {
                    try {
                        return JSON.parse(m.content);
                    }
                    catch (parseError) {
                        logger_1.logger.debug('Failed to parse memory content as JSON:', parseError);
                        return m.content;
                    }
                });
                logger_1.logger.info(`Retrieved ${information.length} relevant information items from memory`);
                return information;
            }
            catch (error) {
                logger_1.logger.error('Failed to retrieve relevant information from memory:', error);
                return [];
            }
        },
        async storeResearchFindings(findings) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(findings),
                    metadata: {
                        source: 'system',
                        type: 'insight',
                        timestamp: new Date().toISOString(),
                        tags: ['research', 'findings']
                    }
                });
                logger_1.logger.info('Saved research findings to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save research findings to memory:', error);
            }
        }
    };
    const sourceEvaluationNode = graph_1.Codessa.createAgentNode('source-evaluation', 'Source Evaluation', analysisAgent);
    const dataAnalysisNode = graph_1.Codessa.createAgentNode('data-analysis', 'Data Analysis', analysisAgent);
    const synthesisNode = graph_1.Codessa.createAgentNode('synthesis', 'Synthesis', summaryAgent);
    const conclusionNode = graph_1.Codessa.createAgentNode('conclusion', 'Conclusion', summaryAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },
        { name: 'query-to-retrieval', source: 'query-analysis', target: 'information-retrieval', type: 'default' },
        { name: 'retrieval-to-evaluation', source: 'information-retrieval', target: 'source-evaluation', type: 'default' },
        { name: 'evaluation-to-analysis', source: 'source-evaluation', target: 'data-analysis', type: 'default' },
        { name: 'analysis-to-synthesis', source: 'data-analysis', target: 'synthesis', type: 'default' },
        { name: 'synthesis-to-conclusion', source: 'synthesis', target: 'conclusion', type: 'default' },
        { name: 'conclusion-to-output', source: 'conclusion', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect information retrieval to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `retrieval-to-tool-${index}`,
                source: 'information-retrieval',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to data analysis
            edges.push({
                name: `tool-${index}-to-analysis`,
                source: `tool-${index}`,
                target: 'data-analysis',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            queryAnalysisNode,
            informationRetrievalNode,
            sourceEvaluationNode,
            dataAnalysisNode,
            synthesisNode,
            conclusionNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'research',
        tags: ['research', 'analysis', 'information-retrieval'],
        metadata: {
            memoryManager: researchMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a specialized academic research workflow
 */
function createAcademicResearchWorkflow(id, name, description, researchAgent, analysisAgent, tools = []) {
    logger_1.logger.info(`Creating Academic Research workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const literatureReviewNode = graph_1.Codessa.createAgentNode('literature-review', 'Literature Review', researchAgent);
    const hypothesisFormulationNode = graph_1.Codessa.createAgentNode('hypothesis-formulation', 'Hypothesis Formulation', researchAgent);
    const methodologyDesignNode = graph_1.Codessa.createAgentNode('methodology-design', 'Methodology Design', researchAgent);
    const dataCollectionNode = graph_1.Codessa.createAgentNode('data-collection', 'Data Collection', researchAgent);
    const dataAnalysisNode = graph_1.Codessa.createAgentNode('data-analysis', 'Data Analysis', analysisAgent);
    const resultsInterpretationNode = graph_1.Codessa.createAgentNode('results-interpretation', 'Results Interpretation', analysisAgent);
    const conclusionNode = graph_1.Codessa.createAgentNode('conclusion', 'Conclusion', analysisAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    const academicResearchMemoryManager = {
        async storeLiteratureReview(literature) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(literature),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['academic', 'research', 'literature']
                    }
                });
                logger_1.logger.info('Saved literature review to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save literature review to memory:', error);
            }
        },
        async storeHypothesis(hypothesis) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(hypothesis),
                    metadata: {
                        source: 'system',
                        type: 'insight',
                        timestamp: new Date().toISOString(),
                        tags: ['academic', 'research', 'hypothesis']
                    }
                });
                logger_1.logger.info('Saved hypothesis to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save hypothesis to memory:', error);
            }
        },
        async storeMethodology(methodology) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(methodology),
                    metadata: {
                        source: 'system',
                        type: 'procedural',
                        timestamp: new Date().toISOString(),
                        tags: ['academic', 'research', 'methodology']
                    }
                });
                logger_1.logger.info('Saved methodology to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save methodology to memory:', error);
            }
        },
        async storeResults(results) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(results),
                    metadata: {
                        source: 'system',
                        type: 'insight',
                        timestamp: new Date().toISOString(),
                        tags: ['academic', 'research', 'results']
                    }
                });
                logger_1.logger.info('Saved research results to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save research results to memory:', error);
            }
        },
        async retrieveRelatedResearch(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5
                });
                const research = memories.map(m => {
                    try {
                        return JSON.parse(m.content);
                    }
                    catch (parseError) {
                        logger_1.logger.debug('Failed to parse research memory as JSON:', parseError);
                        return m.content;
                    }
                });
                logger_1.logger.info(`Retrieved ${research.length} related research items from memory`);
                return research;
            }
            catch (error) {
                logger_1.logger.error('Failed to retrieve related research from memory:', error);
                return [];
            }
        }
    };
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-literature', source: 'input', target: 'literature-review', type: 'default' },
        { name: 'literature-to-hypothesis', source: 'literature-review', target: 'hypothesis-formulation', type: 'default' },
        { name: 'hypothesis-to-methodology', source: 'hypothesis-formulation', target: 'methodology-design', type: 'default' },
        { name: 'methodology-to-data-collection', source: 'methodology-design', target: 'data-collection', type: 'default' },
        { name: 'data-collection-to-analysis', source: 'data-collection', target: 'data-analysis', type: 'default' },
        { name: 'analysis-to-interpretation', source: 'data-analysis', target: 'results-interpretation', type: 'default' },
        { name: 'interpretation-to-conclusion', source: 'results-interpretation', target: 'conclusion', type: 'default' },
        { name: 'conclusion-to-output', source: 'conclusion', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect data collection to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `data-collection-to-tool-${index}`,
                source: 'data-collection',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to data analysis
            edges.push({
                name: `tool-${index}-to-analysis`,
                source: `tool-${index}`,
                target: 'data-analysis',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            literatureReviewNode,
            hypothesisFormulationNode,
            methodologyDesignNode,
            dataCollectionNode,
            dataAnalysisNode,
            resultsInterpretationNode,
            conclusionNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'research',
        tags: ['research', 'academic', 'analysis'],
        metadata: {
            memoryManager: academicResearchMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=researchWorkflow.js.map