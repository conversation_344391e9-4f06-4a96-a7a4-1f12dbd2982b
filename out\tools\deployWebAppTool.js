"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.deployWebAppTool = exports.DeployWebAppTool = void 0;
const vscode = __importStar(require("vscode"));
const cp = __importStar(require("child_process"));
const advancedWebTools_1 = require("./advancedWebTools");
const zod_1 = require("zod");
class DeployWebAppTool {
    id = 'deployWebApp';
    name = 'Deploy Web App (Advanced)';
    description = 'Deploys web apps and manages deployment status with advanced options.';
    type = 'multi-action'; // Required by ITool
    actions = {
        'deploy': {
            id: 'deploy',
            name: 'Deploy Web App',
            description: 'Deploy a JavaScript web application using a deployment provider.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                projectPath: zod_1.z.string().describe('Path to the project to deploy'),
                provider: zod_1.z.string().describe('Deployment provider (netlify, vercel)'),
                args: zod_1.z.string().optional().describe('Additional arguments for the deployment command')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    projectPath: { type: 'string', description: 'Path to the project to deploy' },
                    provider: { type: 'string', description: 'Deployment provider (netlify, vercel)' },
                    args: { type: 'string', description: 'Additional arguments for the deployment command' }
                },
                required: ['projectPath', 'provider']
            },
            async execute(actionName, input, _context) {
                const projectPath = input.projectPath;
                const provider = input.provider;
                const args = input.args || '';
                if (!projectPath || !provider) {
                    return { success: false, error: '\'projectPath\' and \'provider\' are required.', toolId: 'deploy', actionName };
                }
                let cwd = projectPath;
                if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0 && !projectPath.match(/^.:\\/) && !projectPath.startsWith('/')) {
                    cwd = vscode.workspace.workspaceFolders[0].uri.fsPath + '/' + projectPath;
                }
                let command = '';
                if (provider === 'netlify') {
                    command = `npx netlify deploy --dir . ${args}`;
                }
                else if (provider === 'vercel') {
                    command = `npx vercel --prod ${args}`;
                }
                else {
                    return { success: false, error: `Unsupported provider: ${provider}`, toolId: 'deploy', actionName };
                }
                try {
                    const result = await new Promise((resolve, reject) => {
                        cp.exec(command, { cwd }, (err, stdout, stderr) => {
                            if (err && !stdout)
                                return reject(stderr || err.message);
                            resolve(stdout || stderr);
                        });
                    });
                    return { success: true, output: result.trim(), toolId: 'deploy', actionName };
                }
                catch (error) {
                    return { success: false, error: `Deployment failed: ${error.message || error}`, toolId: 'deploy', actionName };
                }
            }
        },
        'status': {
            ...new advancedWebTools_1.WebDeployStatusTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                const projectPath = input.projectPath;
                const provider = input.provider;
                if (!projectPath || !provider) {
                    return { success: false, error: '\'projectPath\' and \'provider\' are required.', toolId: 'status', actionName };
                }
                // Placeholder implementation
                return { success: true, output: `Deployment status for ${projectPath} using ${provider}: Active`, toolId: 'status', actionName };
            }
        },
    };
    async execute(actionName, input, context) {
        // For backward compatibility, check if actionName is undefined and try to get it from input
        const actionId = actionName || input.action || 'deploy';
        const actionTool = this.actions[actionId];
        if (!actionTool) {
            return { success: false, error: `Unknown deploy action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };
        }
        const actionInput = { ...input };
        if ('action' in actionInput) {
            delete actionInput.action;
        }
        // Check if the action tool has the new execute method signature
        if (actionTool.execute.length >= 2) {
            // Pass the actionId as the actionName parameter to the nested tool
            return actionTool.execute(actionId, actionInput, context);
        }
        else {
            // Fallback for older tools that don't have the new signature
            const result = await actionTool.execute(actionInput, context);
            // Add the actionName to the result if it's not already there
            if (result && !result.actionName) {
                result.actionName = actionId;
            }
            return result;
        }
    }
}
exports.DeployWebAppTool = DeployWebAppTool;
exports.deployWebAppTool = new DeployWebAppTool();
//# sourceMappingURL=deployWebAppTool.js.map