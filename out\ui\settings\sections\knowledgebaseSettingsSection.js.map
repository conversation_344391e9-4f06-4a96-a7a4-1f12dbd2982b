{"version": 3, "file": "knowledgebaseSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/knowledgebaseSettingsSection.ts"], "names": [], "mappings": ";;AAeA,gFA+CC;AAID,4DA8CC;AAED,wDA2CC;AAkCD,kDAYC;AAGD,8EASC;AAED,wFAMC;AAED,kFAOC;AA/ND,gDAAqE;AAErE,IAAI,oBAAoB,GAA0B,EAAE,CAAC;AACrD,IAAI,YAAY,GAAkB,IAAI,CAAC;AACvC,MAAM,YAAY,GAA6B,kCAAoB,CAAC,OAAO,CAAC;AAE5E,SAAgB,kCAAkC,CAAC,SAAsB,EAAE,QAAa;IACtF,0BAA0B;IAC1B,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9C,KAAK,CAAC,WAAW,GAAG;;;0BAGI,YAAY,CAAC,QAAQ;qBAC1B,YAAY,CAAC,WAAW;;;;;;8IAMiG,YAAY,CAAC,MAAM;;;;yBAIxI,YAAY,CAAC,MAAM,CAAC,UAAU;oBACnC,YAAY,CAAC,MAAM,CAAC,KAAK;qBACxB,YAAY,CAAC,MAAM,CAAC,MAAM;4BACnB,YAAY,CAAC,MAAM,CAAC,YAAY;;;;;;;wFAO4B,YAAY,CAAC,WAAW;;KAE3G,CAAC;IACJ,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAE7B,SAAS,CAAC,SAAS,IAAI,4DAA4D,CAAC;IACpF,SAAS,CAAC,SAAS,IAAI,qHAAqH,CAAC;IAC7I,SAAS,CAAC,SAAS,IAAI,iCAAiC,CAAC;IAEzD,qBAAqB;IACrB,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC;IACzG,wBAAwB,CAAC,SAAS,CAAC,CAAC;IACpC,uBAAuB;IACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;IAC9D,IAAI,MAAM;QAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAChE,gBAAgB;IAChB,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;IACpE,IAAI,SAAS;QAAE,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAC3E,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;IAChE,IAAI,OAAO;QAAE,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC9E,CAAC;AAID,SAAgB,wBAAwB,CAAC,SAAsB;IAC7D,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,uBAAuB,CAAgB,CAAC;IAChF,IAAI,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC/D,OAAO,CAAC,SAAS,GAAG,mIAAmI,CAAC;QACxJ,OAAO;IACT,CAAC;IACD,IAAI,IAAI,GAAG,uCAAuC;QAC5C,0FAA0F;QAC1F,sBAAsB,CAAC;IAC7B,oBAAoB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACxC,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9G,IAAI,IAAI;sCAC0B,QAAQ,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE;gCAChC,GAAG,CAAC,KAAK,IAAI,EAAE;gCACf,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;kBACjG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,6CAA6C,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,+CAA+C,CAAC,CAAC,CAAC,EAAE;;yEAE7E,GAAG;kFACM,GAAG;;cAEvE,CAAC;IACb,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,kBAAkB,CAAC;IAC3B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IACzB,mCAAmC;IACnC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACnE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;oBAAE,sBAAsB,CAAC,GAAG,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,mCAAmC,CAAC,EAAE,CAAC;oBAChE,oBAAoB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;oBACpC,wBAAwB,CAAC,SAAS,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,sBAAsB,CAAC,GAAkB;IACvD,0CAA0C;IAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAgB,CAAC;IAC3E,MAAM,GAAG,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC5H,KAAK,CAAC,SAAS,GAAG;;;sBAGE,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa;;;6CAGpB,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;6CACrC,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;iDACjC,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;+CAC3C,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;;;yEAGb,GAAG,CAAC,KAAK,IAAI,EAAE;yEACf,GAAG,CAAC,KAAK,IAAI,EAAE;;wEAEhB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;mGACA,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;;eAOhH,CAAC;IACd,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IAC3B,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAChG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE;QACzD,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAuB,CAAC,KAAK,CAAC;QAClF,MAAM,KAAK,GAAI,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAC1F,MAAM,KAAK,GAAI,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAC1F,MAAM,MAAM,GAAI,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAsB,CAAC,OAAO,CAAC;QACvF,MAAM,OAAO,GAAI,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAsB,CAAC,OAAO,CAAC;QACzF,IAAI,CAAC,KAAK,EAAE,CAAC;YAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAAC,OAAO;QAAC,CAAC;QACpD,MAAM,MAAM,GAAwB,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;QACvF,IAAI,GAAG,KAAK,IAAI;YAAE,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;YAC/C,oBAAoB,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;QACxC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QAC7B,wBAAwB,CAAC,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAE,CAAC,CAAC;IACtE,CAAC,CAAC;AACN,CAAC;AAED,SAAS,sBAAsB,CAAC,SAAsB;IACpD,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;IAC5D,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,+CAA+C;IAC/C,MAAM,aAAa,GAAG,SAAS,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;IACtE,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,YAAY,GAAG,IAAI,CAAC;AACtB,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAsB,EAAE,QAAa;IAC9D,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAA4B,CAAC;IAC3F,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAA4B,CAAC;IAC1F,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAA4B,CAAC;IAC1F,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAA4B,CAAC;IAC/F,MAAM,EAAE,GAAwB;QAC9B,KAAK,EAAE,UAAU,EAAE,KAAK,IAAI,EAAE;QAC9B,IAAI,EAAE,SAAS,EAAE,KAAK,IAAI,EAAE;QAC5B,KAAK,EAAE,UAAU,EAAE,KAAK,IAAI,EAAE;QAC9B,MAAM,EAAE,WAAW,EAAE,OAAO,IAAI,KAAK;KACtC,CAAC;IACF,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QACzB,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,oBAAoB,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;IAC1C,CAAC;IACD,QAAQ,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;IACrD,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAClC,wBAAwB,CAAC,SAAS,CAAC,CAAC;AACtC,CAAC;AAED,+CAAgD;AAEhD,SAAgB,mBAAmB,CAAC,SAAsB,EAAE,GAAW;IACrE,MAAM,WAAW,GAAG,oBAAoB,CAAC,GAAG,CAAC,EAAE,KAAK,IAAI,aAAa,CAAC;IACtE,IAAA,iBAAS,EAAC;QACR,KAAK,EAAE,6BAA6B;QACpC,OAAO,EAAE,6DAA6D,WAAW,2BAA2B;QAC5G,SAAS,EAAE,GAAG,EAAE;YACd,oBAAoB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;YACrD,wBAAwB,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED,sDAAsD;AACtD,SAAgB,iCAAiC,CAAC,WAAmB,EAAE,MAA2B;IAChG,6BAA6B;IAC7B,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAClH,IAAI,CAAC,EAAE;QAAE,OAAO;IAChB,IAAI,CAAC,EAAE,CAAC,aAAa;QAAE,EAAE,CAAC,aAAa,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;IACzE,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtC,uCAAuC;IACvC,IAAI,OAAQ,MAAc,CAAC,KAAK,EAAE,6BAA6B,KAAK,UAAU;QAAG,MAAc,CAAC,KAAK,CAAC,6BAA6B,CAAC,WAAW,CAAC,CAAC;AACnJ,CAAC;AAED,SAAgB,sCAAsC,CAAC,WAAmB,EAAE,WAAmB;IAC7F,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAClH,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;QAAE,OAAO;IACjF,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAChD,IAAI,OAAQ,MAAc,CAAC,KAAK,EAAE,6BAA6B,KAAK,UAAU;QAAG,MAAc,CAAC,KAAK,CAAC,6BAA6B,CAAC,WAAW,CAAC,CAAC;AACnJ,CAAC;AAED,SAAgB,mCAAmC,CAAC,WAAmB,EAAE,MAAe;IACtF,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAClH,IAAI,CAAC,EAAE;QAAE,OAAO;IAChB,IAAI,CAAC,EAAE,CAAC,aAAa;QAAE,EAAE,CAAC,aAAa,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;IACzE,EAAE,CAAC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;IACjC,IAAI,OAAQ,MAAc,CAAC,KAAK,EAAE,6BAA6B,KAAK,UAAU;QAAG,MAAc,CAAC,KAAK,CAAC,6BAA6B,CAAC,WAAW,CAAC,CAAC;AACnJ,CAAC", "sourcesContent": ["// Knowledgebase section logic and rendering\r\nexport type KnowledgebaseSource = {\r\n    label: string;\r\n    type: string;\r\n    value: string;\r\n    shared: boolean;\r\n    context?: boolean;\r\n};\r\n\r\nimport { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';\r\n\r\nlet knowledgebaseSources: KnowledgebaseSource[] = [];\r\nlet editingKbIdx: number | null = null;\r\nconst sectionTheme: UIThemeConfig['section'] = defaultUIThemeConfig.section;\r\n\r\nexport function renderKnowledgebaseSettingsSection(container: HTMLElement, settings: any) {\r\n  // Add styles to container\r\n  const style = document.createElement('style');\r\n  style.textContent = `\r\n        .crud-table th, .crud-table td { padding: 7px 12px; }\r\n        .crud-table th {\r\n            background: ${sectionTheme.headerBg};\r\n            color: ${sectionTheme.headerColor};\r\n            font-weight: 600;\r\n            font-size:1.05em;\r\n        }\r\n        .crud-table tbody tr:nth-child(even) { background: #f8fafc; }\r\n        .crud-table tbody tr:hover { background: #e0e7ef; }\r\n        .kb-badge { display:inline-block; padding:2px 8px; border-radius:8px; font-size:0.88em; margin-right:2px; background:#f3f4f6; color:${sectionTheme.accent}; }\r\n        .kb-badge.shared { background:#fef3c7; color:#b45309; }\r\n        .kb-badge.context { background:#d1fae5; color:#059669; }\r\n        .kb-action-btn {\r\n            background:${sectionTheme.button.background};\r\n            color:${sectionTheme.button.color};\r\n            border:${sectionTheme.button.border};\r\n            border-radius:${sectionTheme.button.borderRadius};\r\n            padding:3px 10px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s;\r\n        }\r\n        .kb-action-btn:hover { background:#1d4ed8; }\r\n        .kb-action-btn.delete { background:#fee2e2; color:#b91c1c; }\r\n        .kb-action-btn.delete:hover { background:#fecaca; color:#dc2626; }\r\n        .kb-action-btn[disabled] { background:#e5e7eb; color:#888; cursor:not-allowed; }\r\n        .kb-table-title { font-size:1.25em; font-weight:600; margin-bottom:8px; color:${sectionTheme.headerColor}; letter-spacing:0.01em; }\r\n        .kb-empty { color:#aaa; font-size:1.1em; padding:24px 0; text-align:center; }\r\n    `;\r\n  container.appendChild(style);\r\n  \r\n  container.innerHTML += '<div class=\"kb-table-title\">📚 Knowledgebase Sources</div>';\r\n  container.innerHTML += '<div style=\"margin-bottom:12px;\"><button id=\"addKnowledgebaseBtn\" class=\"kb-action-btn\">➕ Add Source</button></div>';\r\n  container.innerHTML += '<div id=\"knowledgebaseSection\">';\r\n\r\n  // Sync from settings\r\n  knowledgebaseSources = Array.isArray(settings.knowledgebaseSources) ? settings.knowledgebaseSources : [];\r\n  renderKnowledgebaseTable(container);\r\n  // Add button listeners\r\n  const addBtn = document.getElementById('addKnowledgebaseBtn');\r\n  if (addBtn) addBtn.onclick = () => showKnowledgebaseModal(null);\r\n  // Modal buttons\r\n  const cancelBtn = document.getElementById('cancelKnowledgebaseBtn');\r\n  if (cancelBtn) cancelBtn.onclick = () => hideKnowledgebaseModal(container);\r\n  const saveBtn = document.getElementById('saveKnowledgebaseBtn');\r\n  if (saveBtn) saveBtn.onclick = () => saveKnowledgebase(container, settings);\r\n}\r\n\r\n\r\n\r\nexport function renderKnowledgebaseTable(container: HTMLElement) {\r\n  const section = container.querySelector('#knowledgebaseSection') as HTMLElement;\r\n  if (!knowledgebaseSources || knowledgebaseSources.length === 0) {\r\n    section.innerHTML = '<div class=\"kb-empty\">No sources defined.<br><span style=\"font-size:0.95em;\">Click <b>Add Source</b> to get started!</span></div>';\r\n    return;\r\n  }\r\n  let html = '<table class=\"crud-table\"><thead><tr>' +\r\n        '<th>🔖 Type</th><th>📝 Label</th><th>🔗 Value</th><th>🏷️ Badges</th><th>⚙️ Actions</th>' +\r\n        '</tr></thead><tbody>';\r\n  knowledgebaseSources.forEach((src, idx) => {\r\n    const typeIcon = src.type === 'api' ? '🔌' : src.type === 'library' ? '📦' : src.type === 'doc' ? '📄' : '📚';\r\n    html += `<tr>\r\n            <td title=\"Source Type\">${typeIcon} ${src.type || ''}</td>\r\n            <td title=\"Label\">${src.label || ''}</td>\r\n            <td title=\"Value\">${src.value && src.value.length > 40 ? src.value.slice(0, 40) + '…' : src.value || ''}</td>\r\n            <td>${src.shared ? '<span class=\"kb-badge shared\">Shared</span>' : ''}${src.context ? '<span class=\"kb-badge context\">Context</span>' : ''}</td>\r\n            <td>\r\n                <button type=\"button\" class=\"kb-action-btn\" data-edit=\"${idx}\" title=\"Edit source\">Edit</button>\r\n                <button type=\"button\" class=\"kb-action-btn delete\" data-delete=\"${idx}\" title=\"Delete source\">Delete</button>\r\n            </td>\r\n        </tr>`;\r\n  });\r\n  html += '</tbody></table>';\r\n  section.innerHTML = html;\r\n  // Attach listeners for edit/delete\r\n  section.querySelectorAll('button[data-edit]').forEach(btn => {\r\n    btn.addEventListener('click', (e) => {\r\n      const idxStr = (e.target as HTMLElement).getAttribute('data-edit');\r\n      if (idxStr !== null) {\r\n        const idx = parseInt(idxStr);\r\n        if (!isNaN(idx)) showKnowledgebaseModal(idx);\r\n      }\r\n    });\r\n  });\r\n  section.querySelectorAll('button[data-delete]').forEach(btn => {\r\n    btn.addEventListener('click', (e) => {\r\n      const idxStr = (e.target as HTMLElement).getAttribute('data-delete');\r\n      if (idxStr !== null) {\r\n        const idx = parseInt(idxStr);\r\n        if (!isNaN(idx) && confirm('Delete this knowledgebase source?')) {\r\n          knowledgebaseSources.splice(idx, 1);\r\n          renderKnowledgebaseTable(container);\r\n        }\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport function showKnowledgebaseModal(idx: number | null) {\r\n  // Modal for add/edit knowledgebase source\r\n  const modal = document.getElementById('knowledgebaseModal') as HTMLElement;\r\n  const src = idx !== null ? knowledgebaseSources[idx] : { type: 'doc', value: '', label: '', shared: false, context: false };\r\n  modal.innerHTML = `\r\n        <div class=\"modal-overlay\" style=\"display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1000;\">\r\n            <div class=\"modal\" style=\"min-width:340px;\">\r\n                <h4>${idx === null ? 'Add Source' : 'Edit Source'}</h4>\r\n                <div><label>Type:<br>\r\n                    <select id=\"kbSourceType\">\r\n                        <option value=\"doc\"${src.type === 'doc' ? ' selected' : ''}>📄 Doc</option>\r\n                        <option value=\"api\"${src.type === 'api' ? ' selected' : ''}>🔌 API</option>\r\n                        <option value=\"library\"${src.type === 'library' ? ' selected' : ''}>📦 Library</option>\r\n                        <option value=\"other\"${src.type === 'other' ? ' selected' : ''}>📚 Other</option>\r\n                    </select>\r\n                </label></div>\r\n                <div><label>Label:<br><input id=\"kbSourceLabel\" value=\"${src.label || ''}\" /></label></div>\r\n                <div><label>Value:<br><input id=\"kbSourceValue\" value=\"${src.value || ''}\" /></label></div>\r\n                <div style=\"margin:8px 0;\">\r\n                    <label><input type=\"checkbox\" id=\"kbSourceShared\" ${src.shared ? 'checked' : ''}/> Shared</label>\r\n                    <label style=\"margin-left:16px;\"><input type=\"checkbox\" id=\"kbSourceContext\" ${src.context ? 'checked' : ''}/> Context</label>\r\n                </div>\r\n                <div style=\"margin-top:10px;\">\r\n                    <button id=\"saveKbSourceBtn\">Save</button>\r\n                    <button id=\"cancelKbSourceBtn\">Cancel</button>\r\n                </div>\r\n            </div>\r\n        </div>`;\r\n  modal.style.display = 'flex';\r\n    document.getElementById('cancelKbSourceBtn')!.onclick = () => { modal.style.display = 'none'; };\r\n    document.getElementById('saveKbSourceBtn')!.onclick = () => {\r\n      const type = (document.getElementById('kbSourceType') as HTMLSelectElement).value;\r\n      const label = (document.getElementById('kbSourceLabel') as HTMLInputElement).value.trim();\r\n      const value = (document.getElementById('kbSourceValue') as HTMLInputElement).value.trim();\r\n      const shared = (document.getElementById('kbSourceShared') as HTMLInputElement).checked;\r\n      const context = (document.getElementById('kbSourceContext') as HTMLInputElement).checked;\r\n      if (!value) { alert('Value is required.'); return; }\r\n      const newSrc: KnowledgebaseSource = { type, label, value, shared, context: !!context };\r\n      if (idx === null) knowledgebaseSources.push(newSrc);\r\n      else knowledgebaseSources[idx] = newSrc;\r\n      modal.style.display = 'none';\r\n      renderKnowledgebaseTable(document.getElementById('settingsPanel')!);\r\n    };\r\n}\r\n\r\nfunction hideKnowledgebaseModal(container: HTMLElement) {\r\n  const modal = document.getElementById('knowledgebaseModal');\r\n  if (modal) modal.style.display = 'none';\r\n  // Clear any validation errors in the container\r\n  const errorElements = container.querySelectorAll('.validation-error');\r\n  errorElements.forEach(el => el.remove());\r\n  editingKbIdx = null;\r\n}\r\n\r\nfunction saveKnowledgebase(container: HTMLElement, settings: any) {\r\n  const labelInput = document.getElementById('knowledgebaseName') as HTMLInputElement | null;\r\n  const typeInput = document.getElementById('knowledgebaseType') as HTMLInputElement | null;\r\n  const valueInput = document.getElementById('knowledgebaseUrl') as HTMLInputElement | null;\r\n  const sharedInput = document.getElementById('knowledgebaseEnabled') as HTMLInputElement | null;\r\n  const kb: KnowledgebaseSource = {\r\n    label: labelInput?.value || '',\r\n    type: typeInput?.value || '',\r\n    value: valueInput?.value || '',\r\n    shared: sharedInput?.checked || false,\r\n  };\r\n  if (editingKbIdx == null) {\r\n    knowledgebaseSources.push(kb);\r\n  } else {\r\n    knowledgebaseSources[editingKbIdx] = kb;\r\n  }\r\n  settings.knowledgebaseSources = knowledgebaseSources;\r\n  hideKnowledgebaseModal(container);\r\n  renderKnowledgebaseTable(container);\r\n}\r\n\r\nimport { showModal } from '../components/modal';\r\n\r\nexport function deleteKnowledgebase(container: HTMLElement, idx: number) {\r\n  const sourceLabel = knowledgebaseSources[idx]?.label || 'this source';\r\n  showModal({\r\n    title: 'Delete Knowledgebase Source',\r\n    content: `Are you sure you want to remove the knowledgebase source \"${sourceLabel}\"? This cannot be undone.`,\r\n    onConfirm: () => {\r\n      knowledgebaseSources.splice(idx, 1);\r\n      const settings = (window as any).settings || {};\r\n      settings.knowledgebaseSources = knowledgebaseSources;\r\n      renderKnowledgebaseTable(container);\r\n    }\r\n  });\r\n}\r\n\r\n// --- Workspace-specific knowledgebase management ---\r\nexport function addKnowledgebaseSourceToWorkspace(workspaceId: string, source: KnowledgebaseSource) {\r\n  // Find workspace in settings\r\n  const settings = (window as any).settings || {};\r\n  const ws = Array.isArray(settings.workspaces) ? settings.workspaces.find((w: any) => w.id === workspaceId) : null;\r\n  if (!ws) return;\r\n  if (!ws.knowledgebase) ws.knowledgebase = { sources: [], shared: false };\r\n  ws.knowledgebase.sources.push(source);\r\n  // Optionally trigger UI update or save\r\n  if (typeof (window as any).panel?.refreshWorkspaceKnowledgebase === 'function') (window as any).panel.refreshWorkspaceKnowledgebase(workspaceId);\r\n}\r\n\r\nexport function removeKnowledgebaseSourceFromWorkspace(workspaceId: string, sourceIndex: number) {\r\n  const settings = (window as any).settings || {};\r\n  const ws = Array.isArray(settings.workspaces) ? settings.workspaces.find((w: any) => w.id === workspaceId) : null;\r\n  if (!ws || !ws.knowledgebase || !Array.isArray(ws.knowledgebase.sources)) return;\r\n  ws.knowledgebase.sources.splice(sourceIndex, 1);\r\n  if (typeof (window as any).panel?.refreshWorkspaceKnowledgebase === 'function') (window as any).panel.refreshWorkspaceKnowledgebase(workspaceId);\r\n}\r\n\r\nexport function toggleWorkspaceKnowledgebaseSharing(workspaceId: string, shared: boolean) {\r\n  const settings = (window as any).settings || {};\r\n  const ws = Array.isArray(settings.workspaces) ? settings.workspaces.find((w: any) => w.id === workspaceId) : null;\r\n  if (!ws) return;\r\n  if (!ws.knowledgebase) ws.knowledgebase = { sources: [], shared: false };\r\n  ws.knowledgebase.shared = shared;\r\n  if (typeof (window as any).panel?.refreshWorkspaceKnowledgebase === 'function') (window as any).panel.refreshWorkspaceKnowledgebase(workspaceId);\r\n}\r\n"]}