{"_type": "UMLClass", "_id": "AAAAAAGH1PineconeVectorStore=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "PineconeVectorStore", "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAGH1PineconeVectorStoreAttr1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "embeddings", "visibility": "private", "type": "Embeddings"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1PineconeVectorStoreAttr2=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "config", "visibility": "private", "type": "PineconeConfig"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1PineconeVectorStoreAttr3=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "pineconeClient", "visibility": "private", "type": "any | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1PineconeVectorStoreAttr4=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "pineconeIndex", "visibility": "private", "type": "any | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1PineconeVectorStoreAttr5=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "langchainPineconeStore", "visibility": "private", "type": "PineconeStore | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1PineconeVectorStoreAttr6=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "initialized", "visibility": "private", "type": "boolean", "defaultValue": "false"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1PineconeVectorStoreAttr7=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "isInitializing", "visibility": "private", "type": "boolean", "defaultValue": "false"}], "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "constructor", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp1P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp1="}, "name": "embeddings", "type": "Embeddings"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp2=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "initialize", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp2P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp2="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp3=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "assertInitialized", "visibility": "private", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp3P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp3="}, "type": "void", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp4=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "sanitizeMetadata", "visibility": "private", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp4P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp4="}, "name": "metadata", "type": "Record<string, any>"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp4P2=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp4="}, "name": "textContent", "type": "string", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp4P3=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp4="}, "type": "PineconeMetadata", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp5=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "addVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp5P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp5="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp5P2=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp5="}, "name": "vector", "type": "number[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp5P3=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp5="}, "name": "metadata", "type": "Record<string, any>", "defaultValue": "{}"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp5P4=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp5="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp6=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "getVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp6P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp6="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp6P2=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp6="}, "type": "Promise<number[] | undefined>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp7=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "deleteVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp7P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp7="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp7P2=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp7="}, "type": "Promise<boolean>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp8=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "clearVectors", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp8P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp8="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp9=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "searchSimilarVectors", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp9P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp9="}, "name": "vector", "type": "number[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp9P2=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp9="}, "name": "limit", "type": "number", "defaultValue": "5"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp9P3=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp9="}, "name": "filter", "type": "Record<string, any>", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp9P4=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp9="}, "type": "Promise<Array<{id: string, score: number}>>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1PineconeVectorStoreOp10=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStore="}, "name": "getPineconeIndex", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1PineconeVectorStoreOp10P1=", "_parent": {"$ref": "AAAAAAGH1PineconeVectorStoreOp10="}, "type": "any", "direction": "return"}]}], "isAbstract": false}