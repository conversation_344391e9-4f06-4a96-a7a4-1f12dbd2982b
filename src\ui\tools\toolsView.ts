import * as vscode from 'vscode';
import { ToolRegistry } from '../../tools/toolRegistry';
import { Logger } from '../../logger';
import * as path from 'path';
import { BaseTreeItem } from '../treeItems/baseTreeItem';

/**
 * TreeItem representing a Tool in the sidebar tree view
 */
class ToolTreeItem extends BaseTreeItem {
  constructor(
        public readonly toolId: string,
        public readonly name: string,
        public readonly toolDescription: string,
        public readonly collapsible = false
  ) {
    super(name, collapsible ? vscode.TreeItemCollapsibleState.Collapsed : vscode.TreeItemCollapsibleState.None);

    this.init({
        id: toolId,
        tooltip: toolDescription,
        description: '',
        contextValue: 'tool'
    });

    const resourcesPath = path.join(__dirname, '..', '..', 'resources');
    this.iconPath = {
        light: vscode.Uri.file(path.join(resourcesPath, 'light', 'tool.svg')),
        dark: vscode.Uri.file(path.join(resourcesPath, 'dark', 'tool.svg'))
    };
  }
}

/**
 * Tree data provider for the tools sidebar
 */
export class ToolsTreeDataProvider implements vscode.TreeDataProvider<vscode.TreeItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<vscode.TreeItem | null> = new vscode.EventEmitter<vscode.TreeItem | null>();
  readonly onDidChangeTreeData: vscode.Event<vscode.TreeItem | null> = this._onDidChangeTreeData.event;

  /**
     * Refresh the entire tree
     */
  refresh(): void {
    this._onDidChangeTreeData.fire(null);
  }

  /**
     * Get tree item representation for an element
     */
  getTreeItem(element: vscode.TreeItem): vscode.TreeItem {
    return element;
  }

  /**
     * Get children of the provided element, or root elements if no element provided
     */
  async getChildren(element?: vscode.TreeItem): Promise<vscode.TreeItem[]> {
    if (!element) {
      // Root level - show all tools
      return await this.getTools();
    }
    // If the element is a parent tool with sub-actions, show its sub-actions
    const id = (element as ToolTreeItem).id;
    if (!id) return [];
    const tool = await ToolRegistry.instance.getTool(id);
    if (tool && tool.actions) {
      return Object.entries(tool.actions).map(([subId, subTool]) => {
        const name = (subTool as any).name || subId;
        const description = (subTool as any).description || '';
        const hasActions = !!(subTool as any).actions;
        return new ToolTreeItem(
          `${tool.id}.${subId}`,
          name,
          description,
          hasActions
        );
      });
    }
    return [];
  }

  /**
     * Get all tools as tree items
     */
  private async getTools(): Promise<vscode.TreeItem[]> {
    try {
      const tools = await ToolRegistry.instance.getAllTools();
      if (tools.length === 0) {
        const noToolsItem = new vscode.TreeItem(
          'No tools available',
          vscode.TreeItemCollapsibleState.None
        );
        noToolsItem.tooltip = 'Tools will appear here once a provider is configured';
        noToolsItem.contextValue = 'noTools';
        return [noToolsItem];
      }
      return tools.map(tool => {
        const name = (tool as any).name || tool.id;
        const description = (tool as any).description || '';
        const hasActions = !!(tool as any).actions;
        return new ToolTreeItem(
          tool.id,
          name,
          description,
          hasActions
        );
      });
    } catch (error) {
      Logger.instance.error('Error getting tools for tree view:', error);
      const errorItem = new vscode.TreeItem(
        'Click to configure settings',
        vscode.TreeItemCollapsibleState.None
      );
      errorItem.command = {
        command: 'codessa.openSettings',
        title: 'Open Settings',
        arguments: []
      };
      errorItem.tooltip = `Error loading tools: ${error instanceof Error ? error.message : String(error)}`;
      return [errorItem];
    }
  }
}

/**
 * Setup and register the tools tree view
 */
export function registerToolsTreeView(context: vscode.ExtensionContext): vscode.TreeView<vscode.TreeItem> {
  const treeDataProvider = new ToolsTreeDataProvider();
  const treeView = vscode.window.createTreeView('codessaToolsView', {
    treeDataProvider,
    showCollapseAll: false,
    canSelectMany: false
  });

  context.subscriptions.push(
    treeView,
    vscode.commands.registerCommand('codessa.refreshToolsView', () => treeDataProvider.refresh())
  );

  return treeView;
}
