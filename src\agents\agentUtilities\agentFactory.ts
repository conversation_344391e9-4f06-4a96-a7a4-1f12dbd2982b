import { Agent } from './agent';

import { logger } from '../../logger';
import { ChatAgent } from '../agentTypes/chatAgent';
import { EditAgent } from '../agentTypes/editAgent';
import { DebugAgent } from '../agentTypes/debugAgent';
import { RefactorAgent } from '../agentTypes/refactorAgent';
import { DocumentAgent } from '../agentTypes/documentAgent';

/**
 * Factory function to create the appropriate specialized agent for a mode
 */
export function createModeAgent(mode: string, _config: Record<string, unknown>): Agent {
  switch (mode) {
    case 'chat':
      return new ChatAgent(_config);
    case 'edit':
      return new EditAgent(_config);
    case 'debug':
      return new DebugAgent(_config);
    case 'refactor':
      return new RefactorAgent(_config);
    case 'document':
      return new DocumentAgent(_config);
    default:
      // For other modes, create a generic agent
      logger.info(`No specialized agent class found for mode: ${mode}. Using generic Agent.`);
      return new Agent({
        id: (_config as Record<string, unknown>).id as string || `agent-${Date.now()}`,
        name: (_config as Record<string, unknown>).name as string || `Agent-${mode}`,
        systemPromptName: (_config as Record<string, unknown>).systemPromptName as string || 'default',
        ..._config,
        role: 'assistant',
        capabilities: ['chat'],
        llmProvider: 'openai',
        llmModel: 'gpt-4'
      } as import('./agent').AgentConfig);
  }
}