"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = void 0;
const docsTool_1 = require("./docsTool");
const directoryListTool_1 = require("./directoryListTool");
const codeSearchTool_1 = require("./codeSearchTool");
const terminalCommandTool_1 = require("./terminalCommandTool");
const webSearchTool_1 = require("./webSearchTool");
const webReadTool_1 = require("./webReadTool");
const memoryTool_1 = require("./memoryTool");
const memoryManager_1 = require("../memory/memoryManager");
const browserPreviewTool_1 = require("./browserPreviewTool");
const deployWebAppTool_1 = require("./deployWebAppTool");
const gitTool_1 = require("./gitTool");
const editorActionsTool_1 = require("./editorActionsTool");
const codeIntelligenceTool_1 = require("./codeIntelligenceTool");
const codeGenerationTool_1 = require("./codeGenerationTool");
const lintDiagnosticsTool_1 = require("./lintDiagnosticsTool");
const logger_1 = require("../logger");
const enhancedFileTools_1 = require("./enhancedFileTools");
// Import advanced tools
const advancedCodeGenerationTool_1 = require("./advancedCodeGenerationTool");
const advancedCodeSearchTool_1 = require("./advancedCodeSearchTool");
const advancedDocsTool_1 = require("./advancedDocsTool");
const advancedEditorActionsTool_1 = require("./advancedEditorActionsTool");
const advancedGitTool_1 = require("./advancedGitTool");
const advancedLintDiagnosticsTool_1 = require("./advancedLintDiagnosticsTool");
const advancedMemoryTool_1 = require("./advancedMemoryTool");
const advancedWebTools_1 = require("./advancedWebTools");
// Import new advanced analysis tools
const codeAnalysisTool_1 = require("./codeAnalysisTool");
const codeAnalysisTool_2 = require("./codeAnalysisTool");
const dependencyAnalysisTool_1 = require("./dependencyAnalysisTool");
const codeRefactoringTool_1 = require("./codeRefactoringTool");
// Import enhanced file system tool
// Import diagnostics tool
const diagnosticsTool_1 = require("./diagnosticsTool");
// Import advanced editing tools
const advancedCodeEditingTool_1 = require("./advancedCodeEditingTool");
const advancedDiffTool_1 = require("./advancedDiffTool");
const largeFileEditTool_1 = require("./largeFileEditTool");
// Phase 2 tools
const timeTravelDebuggingTool_1 = require("./timeTravelDebuggingTool");
const cascadeEditingTool_1 = require("./cascadeEditingTool");
/**
 * Registry for all available tools in the extension.
 * Acts as a central access point for tool instances.
 */
// Singleton export removed; use ToolRegistry.instance instead.
class ToolRegistry {
    static _instance;
    tools = new Map();
    initialized = false;
    constructor() {
        // Private constructor to prevent direct instantiation
    }
    /**
       * Get the singleton instance of ToolRegistry
       */
    static get instance() {
        if (!ToolRegistry._instance) {
            // Create a new instance if it doesn't exist yet
            ToolRegistry._instance = new ToolRegistry();
            // Note: The context will be set when initialize() is called
        }
        return ToolRegistry._instance;
    }
    /**
       * Initialize the ToolRegistry
       */
    static async initialize(context) {
        const instance = ToolRegistry.instance; // This will create the instance if it doesn't exist
        if (instance.initialized) {
            logger_1.Logger.instance.warn('ToolRegistry.initialize() called multiple times. This is allowed but may indicate an issue.');
            return instance;
        }
        await instance.initialize(context);
        return instance;
    }
    /**
       * Get a tool by ID
       */
    static getTool(toolId) {
        if (!ToolRegistry.instance) {
            throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');
        }
        return ToolRegistry.instance.tools.get(toolId);
    }
    /**
       * Get all registered tools
       */
    static getAllTools() {
        if (!ToolRegistry.instance) {
            throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');
        }
        return Array.from(ToolRegistry.instance.tools.values());
    }
    /**
       * Register a tool
       */
    static registerTool(tool) {
        if (!ToolRegistry.instance) {
            throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');
        }
        if (ToolRegistry.instance.tools.has(tool.id)) {
            logger_1.Logger.instance.warn(`Tool with ID '${tool.id}' is already registered. Overwriting.`);
        }
        ToolRegistry.instance.tools.set(tool.id, tool);
        logger_1.Logger.instance.info(`Registered tool: ${tool.id} (${tool.name}) - ${tool.description}`);
    }
    /**
       * Initialize the registry
       */
    async initialize(context) {
        if (this.initialized) {
            logger_1.Logger.instance.warn('ToolRegistry.initialize() called when already initialized');
            return;
        }
        logger_1.Logger.instance.info('Starting tool registry initialization...');
        // Initialize memory manager first
        await memoryManager_1.memoryManager.initialize(context);
        // Register built-in tools
        this.registerBuiltInTools();
        // Wait a bit to ensure all tools are registered
        await new Promise(resolve => setTimeout(resolve, 1000));
        logger_1.Logger.instance.info(`Tool registry initialized with ${this.tools.size} tools`);
        this.initialized = true;
    }
    /**
       * Register built-in tools with the registry
       */
    registerBuiltInTools() {
        try {
            logger_1.Logger.instance.info('Starting tool registration...');
            // Register basic tools with standardized IDs
            this.registerToolWithId(new directoryListTool_1.DirectoryListTool(), 'listDir');
            this.registerToolWithId(new codeSearchTool_1.CodeSearchTool(), 'codeSearch');
            this.registerToolWithId(new terminalCommandTool_1.TerminalCommandTool(), 'terminal_command');
            this.registerToolWithId(new webSearchTool_1.WebSearchTool(), 'webSearch');
            this.registerToolWithId(new webReadTool_1.WebReadTool(), 'webRead');
            this.registerToolWithId((0, memoryTool_1.createMemoryTool)(memoryManager_1.memoryManager), 'memory_management');
            this.registerToolWithId(new browserPreviewTool_1.BrowserPreviewTool(), 'browserPreview');
            this.registerToolWithId(new deployWebAppTool_1.DeployWebAppTool(), 'deployWebApp');
            this.registerToolWithId(new gitTool_1.GitTool(), 'git');
            this.registerToolWithId(new editorActionsTool_1.EditorActionsTool(), 'editor');
            this.registerToolWithId(new codeIntelligenceTool_1.CodeIntelligenceTool(), 'codeIntel');
            this.registerToolWithId(new codeGenerationTool_1.CodeGenerationTool(), 'codeGen');
            this.registerToolWithId(new lintDiagnosticsTool_1.LintDiagnosticsTool(), 'lint_diagnostics');
            this.registerToolWithId(new diagnosticsTool_1.DiagnosticsTool(), 'diagnostics');
            this.registerToolWithId(new enhancedFileTools_1.EnhancedFileSystemTool(), 'enhancedFile');
            this.registerToolWithId(new docsTool_1.DocumentationTool(), 'docs');
            // Register advanced code generation tools
            this.registerToolWithId(new advancedCodeGenerationTool_1.ExplainCodeTool(), 'explain');
            this.registerToolWithId(new advancedCodeGenerationTool_1.DocumentCodeTool(), 'document');
            this.registerToolWithId(new advancedCodeGenerationTool_1.GenerateTestsTool(), 'generateTests');
            this.registerToolWithId(new advancedCodeGenerationTool_1.MultiFileCodeGenTool(), 'multiFileGen');
            this.registerToolWithId(new advancedDocsTool_1.DocumentationGenTool(), 'docGen');
            this.registerToolWithId(new advancedDocsTool_1.DocumentationSearchTool(), 'docSearch');
            this.registerToolWithId(new advancedDocsTool_1.DocumentationSummaryTool(), 'docSummary');
            this.registerToolWithId(new advancedDocsTool_1.DocumentationVisualizationTool(), 'docViz');
            this.registerToolWithId(new advancedCodeSearchTool_1.FuzzySearchTool(), 'fuzzySearch');
            this.registerToolWithId(new advancedCodeSearchTool_1.SemanticSearchTool(), 'semanticSearch');
            this.registerToolWithId(new advancedCodeSearchTool_1.SearchPreviewTool(), 'searchPreview');
            this.registerToolWithId(new advancedEditorActionsTool_1.MultiCursorTool(), 'multiCursor');
            this.registerToolWithId(new advancedEditorActionsTool_1.ClipboardTool(), 'clipboard');
            this.registerToolWithId(new advancedEditorActionsTool_1.BatchEditTool(), 'batchEdit');
            this.registerToolWithId(new advancedEditorActionsTool_1.FindReplaceTool(), 'findReplace');
            this.registerToolWithId(new advancedGitTool_1.GitStashTool(), 'stash');
            this.registerToolWithId(new advancedGitTool_1.GitRevertTool(), 'revert');
            this.registerToolWithId(new advancedGitTool_1.GitCherryPickTool(), 'cherryPick');
            this.registerToolWithId(new advancedGitTool_1.GitRebaseTool(), 'rebase');
            this.registerToolWithId(new advancedGitTool_1.GitTagTool(), 'tag');
            this.registerToolWithId(new advancedGitTool_1.GitBranchGraphTool(), 'branchGraph');
            this.registerToolWithId(new advancedLintDiagnosticsTool_1.BatchLintTool(), 'batchLint');
            this.registerToolWithId(new advancedLintDiagnosticsTool_1.AutoFixAllTool(), 'autoFixAll');
            this.registerToolWithId(new advancedLintDiagnosticsTool_1.LintSummaryTool(), 'lintSummary');
            this.registerToolWithId(new advancedLintDiagnosticsTool_1.DiagnosticsSearchTool(), 'diagnosticsSearch');
            this.registerToolWithId(new advancedMemoryTool_1.MemorySearchTool(memoryManager_1.memoryManager), 'memorySearch');
            this.registerToolWithId(new advancedMemoryTool_1.MemoryUpdateTool(memoryManager_1.memoryManager), 'memoryUpdate');
            this.registerToolWithId(new advancedMemoryTool_1.MemoryTagTool(memoryManager_1.memoryManager), 'memoryTag');
            this.registerToolWithId(new advancedMemoryTool_1.MemoryVisualizationTool(memoryManager_1.memoryManager), 'memoryViz');
            this.registerToolWithId(new advancedWebTools_1.WebMultiSearchTool(), 'webMultiSearch');
            this.registerToolWithId(new advancedWebTools_1.WebContentExtractTool(), 'webExtract');
            this.registerToolWithId(new advancedWebTools_1.WebSnapshotTool(), 'webSnapshot');
            this.registerToolWithId(new advancedWebTools_1.WebDeployStatusTool(), 'deployStatus');
            this.registerToolWithId(new codeAnalysisTool_1.CodeComplexityTool(), 'codeComplexity');
            this.registerToolWithId(new codeAnalysisTool_2.SecurityVulnerabilityTool(), 'securityVulnerability');
            this.registerToolWithId(new dependencyAnalysisTool_1.DependencyAnalysisTool(), 'dependencyAnalysis');
            this.registerToolWithId(new codeRefactoringTool_1.CodeSmellDetectorTool(), 'codeSmellDetector');
            this.registerToolWithId(new codeRefactoringTool_1.RefactoringAdvisorTool(), 'refactoringAdvisor');
            this.registerToolWithId(new advancedCodeEditingTool_1.PrecisionEditTool(), 'precisionEdit');
            this.registerToolWithId(new advancedDiffTool_1.DiffTool(), 'diffTool');
            this.registerToolWithId(new largeFileEditTool_1.LargeFileEditTool(), 'largeFileEdit');
            // Register advanced code search tools
            this.registerToolWithId(new advancedCodeSearchTool_1.FuzzySearchTool(), 'fuzzySearch');
            this.registerToolWithId(new advancedCodeSearchTool_1.SemanticSearchTool(), 'semanticSearch');
            this.registerToolWithId(new advancedCodeSearchTool_1.SearchPreviewTool(), 'searchPreview');
            // Register advanced documentation tools
            this.registerToolWithId(new advancedDocsTool_1.DocumentationGenTool(), 'docGen');
            this.registerToolWithId(new advancedDocsTool_1.DocumentationSearchTool(), 'docSearch');
            this.registerToolWithId(new advancedDocsTool_1.DocumentationSummaryTool(), 'docSummary');
            this.registerToolWithId(new advancedDocsTool_1.DocumentationVisualizationTool(), 'docViz');
            // Register advanced editor action tools
            this.registerToolWithId(new advancedEditorActionsTool_1.MultiCursorTool(), 'multiCursor');
            this.registerToolWithId(new advancedEditorActionsTool_1.ClipboardTool(), 'clipboard');
            this.registerToolWithId(new advancedEditorActionsTool_1.BatchEditTool(), 'batchEdit');
            this.registerToolWithId(new advancedEditorActionsTool_1.FindReplaceTool(), 'findReplace');
            // Register advanced git tools
            this.registerToolWithId(new advancedGitTool_1.GitStashTool(), 'stash');
            this.registerToolWithId(new advancedGitTool_1.GitRevertTool(), 'revert');
            this.registerToolWithId(new advancedGitTool_1.GitCherryPickTool(), 'cherryPick');
            this.registerToolWithId(new advancedGitTool_1.GitRebaseTool(), 'rebase');
            this.registerToolWithId(new advancedGitTool_1.GitTagTool(), 'tag');
            this.registerToolWithId(new advancedGitTool_1.GitBranchGraphTool(), 'branchGraph');
            // Register advanced lint diagnostic tools
            this.registerToolWithId(new advancedLintDiagnosticsTool_1.BatchLintTool(), 'batchLint');
            this.registerToolWithId(new advancedLintDiagnosticsTool_1.AutoFixAllTool(), 'autoFixAll');
            this.registerToolWithId(new advancedLintDiagnosticsTool_1.LintSummaryTool(), 'lintSummary');
            this.registerToolWithId(new advancedLintDiagnosticsTool_1.DiagnosticsSearchTool(), 'diagnosticsSearch');
            // Register advanced memory tools
            this.registerToolWithId(new advancedMemoryTool_1.MemorySearchTool(memoryManager_1.memoryManager), 'memorySearch');
            this.registerToolWithId(new advancedMemoryTool_1.MemoryUpdateTool(memoryManager_1.memoryManager), 'memoryUpdate');
            this.registerToolWithId(new advancedMemoryTool_1.MemoryTagTool(memoryManager_1.memoryManager), 'memoryTag');
            this.registerToolWithId(new advancedMemoryTool_1.MemoryVisualizationTool(memoryManager_1.memoryManager), 'memoryViz');
            // Register advanced web tools
            this.registerToolWithId(new advancedWebTools_1.WebMultiSearchTool(), 'webMultiSearch');
            this.registerToolWithId(new advancedWebTools_1.WebContentExtractTool(), 'webExtract');
            this.registerToolWithId(new advancedWebTools_1.WebSnapshotTool(), 'webSnapshot');
            this.registerToolWithId(new advancedWebTools_1.WebDeployStatusTool(), 'deployStatus');
            // Register advanced code analysis tools
            this.registerToolWithId(new codeAnalysisTool_1.CodeComplexityTool(), 'codeComplexity');
            this.registerToolWithId(new codeAnalysisTool_2.SecurityVulnerabilityTool(), 'securityVulnerability');
            this.registerToolWithId(new dependencyAnalysisTool_1.DependencyAnalysisTool(), 'dependencyAnalysis');
            // Register advanced code refactoring tools
            this.registerToolWithId(new codeRefactoringTool_1.CodeSmellDetectorTool(), 'codeSmellDetector');
            this.registerToolWithId(new codeRefactoringTool_1.RefactoringAdvisorTool(), 'refactoringAdvisor');
            // Register advanced code editing tools
            this.registerToolWithId(new advancedCodeEditingTool_1.PrecisionEditTool(), 'precisionEdit');
            this.registerToolWithId(new advancedDiffTool_1.DiffTool(), 'diffTool');
            this.registerToolWithId(new largeFileEditTool_1.LargeFileEditTool(), 'largeFileEdit');
            // Phase 2: Register revolutionary tools
            this.registerToolWithId(new timeTravelDebuggingTool_1.TimeTravelDebuggingTool(), 'timeTravelDebug');
            this.registerToolWithId(new cascadeEditingTool_1.CascadeEditingTool(), 'cascadeEdit');
            // Phase 5: Register revolutionary tools
            this.registerRevolutionaryPhase5Tools();
            logger_1.Logger.instance.info(`Initialized ${this.tools.size} tools in registry (including Phase 5 revolutionary tools).`);
        }
        catch (error) {
            logger_1.Logger.instance.error('Error registering built-in tools:', error);
        }
    }
    /**
       * Register Phase 5 Revolutionary Tools
       */
    registerRevolutionaryPhase5Tools() {
        try {
            // Import and register revolutionary tools
            const { QuantumAnalysisTool } = require('./quantumAnalysisTool');
            const { NeuralCodeSynthesisTool } = require('./neuralCodeSynthesisTool');
            // Register Quantum Analysis Tool
            this.registerToolWithId(new QuantumAnalysisTool(), 'quantum_analysis');
            // Register Neural Code Synthesis Tool
            this.registerToolWithId(new NeuralCodeSynthesisTool(), 'neural_code_synthesis');
            // Update essential tools to include revolutionary tools
            this.updateEssentialToolsWithRevolutionary();
            logger_1.Logger.instance.info('Phase 5 revolutionary tools registered successfully');
        }
        catch (error) {
            logger_1.Logger.instance.error('Error registering Phase 5 revolutionary tools:', error);
        }
    }
    /**
       * Update essential tools to include revolutionary capabilities
       */
    updateEssentialToolsWithRevolutionary() {
        // Add revolutionary tools to essential tools for advanced agents
        const revolutionaryToolIds = [
            'quantum_analysis',
            'neural_code_synthesis',
            'timeTravelDebug'
        ];
        // These will be available to agents that request revolutionary capabilities
        logger_1.Logger.instance.debug(`Revolutionary tools available: ${revolutionaryToolIds.join(', ')}`);
    }
    /**
       * Registers a tool with the registry
       */
    registerToolWithId(tool, id) {
        // Override the tool's ID with our standardized ID
        Object.defineProperty(tool, 'id', {
            value: id,
            writable: false,
            configurable: false
        });
        this.registerTool(tool);
    }
    registerTool(tool) {
        if (this.tools.has(tool.id)) {
            logger_1.Logger.instance.warn(`Tool with ID '${tool.id}' is already registered. Overwriting.`);
        }
        this.tools.set(tool.id, tool);
        logger_1.Logger.instance.info(`Registered tool: ${tool.id} (${tool.name}) - ${tool.description}`);
        // Log the tool's actions if it's a multi-action tool
        if (tool.type === 'multi-action' && tool.actions) {
            Object.entries(tool.actions).forEach(([actionName, action]) => {
                logger_1.Logger.instance.debug(`  Action: ${actionName} - ${action.description || 'No description'}`);
            });
        }
    }
    /**
       * Gets a tool by ID
       */
    getTool(toolId) {
        const tool = this.tools.get(toolId);
        if (!tool) {
            const availableTools = Array.from(this.tools.keys()).join(', ');
            logger_1.Logger.instance.warn(`Tool with ID '${toolId}' not found. Available tools: ${availableTools}`);
        }
        return tool;
    }
    /**
       * Gets all registered tools
       */
    getAllTools() {
        return Array.from(this.tools.values());
    }
    /**
       * Converts array of tool IDs to a map of tool instances
       */
    getToolsByIds(toolIds) {
        const result = new Map();
        for (const id of toolIds) {
            const tool = this.getTool(id);
            if (tool) {
                result.set(id, tool);
            }
            else {
                logger_1.Logger.instance.warn(`Tool with ID '${id}' not found.`);
            }
        }
        return result;
    }
    /**
       * Get all essential tools for agents
       */
    getEssentialTools() {
        const essentialToolIds = [
            'listDir',
            'codeSearch',
            'memory_management',
            'editor',
            'terminal_command',
            'webSearch',
            'git',
            'codeIntel',
            'memorySearch',
            'memoryUpdate'
        ];
        return this.getToolsByIds(essentialToolIds);
    }
    /**
       * Get tools by category
       */
    getToolsByCategory(category) {
        return Array.from(this.tools.values()).filter(tool => tool.category === category);
    }
    /**
       * Get all available tool categories
       */
    getToolCategories() {
        const categories = new Set();
        for (const tool of this.tools.values()) {
            if (tool.category) {
                categories.add(tool.category);
            }
        }
        return Array.from(categories);
    }
}
exports.ToolRegistry = ToolRegistry;
//# sourceMappingURL=toolRegistry.js.map