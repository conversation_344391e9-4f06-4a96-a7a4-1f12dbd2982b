/**
 * Ultimate Codessa Workflow System
 * Supports all VSCode operation modes, SDLC methodologies, and advanced AI workflows
 */

import { ITool } from '../../tools/tool.ts.backup';
import { Agent, MultiAgentSystem } from '../agentUtilities/agent';
import { Codessa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge, WorkflowType, Methodology, EdgeType, OperationMode } from './types';
import { workflowRegistry } from './workflowRegistry';
import { promptManager } from '../../prompts/promptManager';
import { Logger } from '../../logger';
import {
  createMemoryRetrievalTool,
  createMemorySaveTool,
  createDocumentRetrievalTool,
  createCodeAnalysisTool,
  createTestingTool,
  createDocumentationTool,
  createDeploymentTool,
  createMonitoringTool,
  createCI_CDTool,
  // Removed duplicate imports
} from './vectorStores';

// ==================== TYPES AND INTERFACES ====================

interface WorkflowConfig {
  id: string;
  name: string;
  description: string;
  agent: Agent | MultiAgentSystem;
  type?: WorkflowType;
  methodology?: Methodology;
  version?: string;
  metadata?: Record<string, any>;
}

interface WorkflowComponents {
  nodes: GraphNode[];
  edges: GraphEdge[];
  startNodeId?: string;
}

// ==================== CORE WORKFLOW BUILDER ====================

class WorkflowBuilder {
  private config: WorkflowConfig;
  private components: WorkflowComponents;

  constructor(_config: any) {
    this.config = _config;
    this.components = {
      nodes: [],
      edges: []
    };
  }

  addNode(node: GraphNode): WorkflowBuilder {
    this.components.nodes.push(node);
    return this;
  }

  addEdge(edge: GraphEdge): WorkflowBuilder {
    this.components.edges.push(edge);
    return this;
  }

  build(): GraphDefinition {
    const workflow: GraphDefinition = {
      id: this.config.id,
      name: this.config.name,
      description: this.config.description,
      version: this.config.version || '1.0.0',
      operationMode: this.config.type as OperationMode || 'agentic',
      nodes: this.components.nodes,
      edges: this.components.edges,
      startNodeId: this.components.startNodeId || 'input',
      metadata: this.config.metadata || {},
      type: this.config.type || 'default',
      methodology: this.config.methodology
    };

    Logger.instance.info('Registering workflow: ' + this.config.name + ' (' + this.config.id + ')');
    workflowRegistry.registerWorkflow(workflow);
    return workflow;
  }
}

// ==================== WORKFLOW COMPONENT FACTORIES ====================

function createInputNode(): GraphNode {
  return Codessa.createInputNode('input', 'Input');
}

function createOutputNode(): GraphNode {
  return Codessa.createOutputNode('output', 'Output');
}

function createAgentNode(agent: Agent | undefined, name = 'Agent'): GraphNode {
  if (!agent) throw new Error(`Agent instance required for node: ${name}`);
  return Codessa.createAgentNode(`agent-${name.toLowerCase()}`, name, agent);
}

function createToolNode(tool: ITool, name: string, label?: string): GraphNode {
  return {
    id: tool.id,
    type: 'tool',
    name,
    label: label || name,
    tool,
    execute: async (state) => {
      // Map GraphState to ToolInput and call the tool's execute
      // You may need to adjust how actionName, input, and context are derived from state
      const actionName = (state as any).actionName;
      const input = (state as any).input;
      const context = (state as any).context;
      const toolResult = await tool.execute(actionName, input, context);
      // You may want to merge toolResult.output into the state.outputs or return as needed
      return {
        ...state,
        toolResult
      };
    }
  };
}

// ==================== WORKFLOW CREATORS ====================

export function createMemoryEnhancedWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Memory Enhanced workflow requires a single Agent instance');
  }

  builder
    .addNode(createInputNode())
    .addNode(createToolNode(createMemoryRetrievalTool(), 'Memory Retrieval'))
    .addNode(createAgentNode(_config.agent)) // Now _config.agent is guaranteed to be Agent
    .addNode(createToolNode(createMemorySaveTool(), 'Memory Save'))
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-memory', source: 'input', target: 'tool-memory-retrieval', type: _config.type as EdgeType || 'default' }) // Cast type
    .addEdge({ name: 'memory-to-agent', source: 'tool-memory-retrieval', target: 'agent-agent', type: _config.type as EdgeType || 'default' }) // Cast type
    .addEdge({ name: 'agent-to-save', source: 'agent-agent', target: 'tool-memory-save', type: _config.type as EdgeType || 'default' }) // Cast type
    .addEdge({ name: 'save-to-output', source: 'tool-memory-save', target: 'output', type: _config.type as EdgeType || 'default' }); // Cast type

  return builder.build();
}

export function createDocumentQAWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Document QA workflow requires a single Agent instance');
  }

  const docRetrieval = createToolNode(createDocumentRetrievalTool(), 'Document Retrieval');
  const infoExtraction: GraphNode = {
    id: 'info-extraction',
    type: 'agent',
    name: 'Information Extraction',
    label: 'Information Extraction',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in info-extraction node');
      }
      const prompt = `Task: Extract relevant information from documents.\nDocuments: ${JSON.stringify(state.outputs['tool-document-retrieval'])}\nQuery: ${state.messages.slice(-1)[0]?.content}`;
      const extraction = await state.agent.generate(prompt);
      state.extractedInfo = extraction;
      return state;
    }
  };

  const answerGen: GraphNode = {
    id: 'answer-generation',
    type: 'agent',
    name: 'Answer Generation',
    label: 'Answer Generation',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in answer-generation node');
      }
      const prompt = `Task: Generate comprehensive answer.\nExtracted Info: ${state.extractedInfo}\nQuery: ${state.messages.slice(-1)[0]?.content}`;
      const answer = await state.agent.generate(prompt);
      state.answer = answer;
      return state;
    }
  };

  builder
    .addNode(createInputNode())
    .addNode(docRetrieval)
    .addNode(infoExtraction)
    .addNode(answerGen)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-doc-retrieval', source: 'input', target: 'tool-document-retrieval', type: _config.type as EdgeType || 'default' }) // Cast type
    .addEdge({ name: 'doc-to-extraction', source: 'tool-document-retrieval', target: 'info-extraction', type: _config.type as EdgeType || 'default' }) // Cast type
    .addEdge({ name: 'extraction-to-answer', source: 'info-extraction', target: 'answer-generation', type: _config.type as EdgeType || 'default' }) // Cast type
    .addEdge({ name: 'answer-to-output', source: 'answer-generation', target: 'output', type: _config.type as EdgeType || 'default' }); // Cast type

  return builder.build();
}

export function createCodeRefactoringWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Code Refactoring workflow requires a single Agent instance');
  }

  const codeAnalysis: GraphNode = {
    id: 'code-analysis',
    type: 'agent',
    name: 'Code Analysis',
    label: 'Code Analysis',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in code-analysis node');
      }
      const prompt = `Task: Analyze code for refactoring opportunities.\nCode: ${state.messages.slice(-1)[0]?.content}`;
      state.analysis = await state.agent.generate(prompt);
      return state;
    }
  };

  const refactorPlan: GraphNode = {
    id: 'refactor-plan',
    type: 'agent',
    name: 'Refactoring Plan',
    label: 'Refactoring Plan',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in refactor-plan node');
      }
      const prompt = `Task: Create refactoring plan.\nAnalysis: ${state.analysis}`;
      state.plan = await state.agent.generate(prompt);
      return state;
    }
  };

  const refactorImpl: GraphNode = {
    id: 'refactor-impl',
    type: 'agent',
    name: 'Refactoring Implementation',
    label: 'Refactoring Implementation',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in refactor-impl node');
      }
      const prompt = `Task: Implement refactoring.\nPlan: ${state.plan}\nOriginal Code: ${state.messages.slice(-1)[0]?.content}`;
      state.refactoredCode = await state.agent.generate(prompt);
      return state;
    }
  };

  builder
    .addNode(createInputNode())
    .addNode(codeAnalysis)
    .addNode(refactorPlan)
    .addNode(refactorImpl)
    .addNode(createToolNode(createTestingTool(), 'Refactoring Tests'))
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'refactor' as EdgeType }) // Cast type
    .addEdge({ name: 'analysis-to-plan', source: 'code-analysis', target: 'refactor-plan', type: 'refactor' as EdgeType }) // Cast type
    .addEdge({ name: 'plan-to-impl', source: 'refactor-plan', target: 'refactor-impl', type: 'refactor' as EdgeType }) // Cast type
    .addEdge({ name: 'impl-to-test', source: 'refactor-impl', target: 'tool-refactoring-tests', type: 'refactor' as EdgeType }) // Cast type
    .addEdge({ name: 'test-to-output', source: 'tool-refactoring-tests', target: 'output', type: 'refactor' as EdgeType }) // Cast type
    ;

  return builder.build();
}

export function createDebuggingWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Debugging workflow requires a single Agent instance');
  }

  const errorAnalysis: GraphNode = {
    id: 'error-analysis',
    type: 'agent',
    name: 'Error Analysis',
    label: 'Error Analysis',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in error-analysis node');
      }
      // Construct a string prompt
      const prompt = `Task: Analyze error and identify root cause.
Error: ${state.messages.slice(-1)[0]?.content}`;

      state.errorReport = await state.agent.generate(prompt); // Use string prompt
      return state;
    }
  };

  const solutionGen: GraphNode = {
    id: 'solution-gen',
    type: 'agent',
    name: 'Solution Generation',
    label: 'Solution Generation',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in solution-gen node');
      }
      // Construct a string prompt
      const prompt = `Task: Generate solution for error.
Error Report: ${state.errorReport}`;

      state.solution = await state.agent.generate(prompt); // Use string prompt
      return state;
    }
  };

  const explanation: GraphNode = {
    id: 'explanation',
    type: 'agent',
    name: 'Solution Explanation',
    label: 'Solution Explanation',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in explanation node');
      }
      // Construct a string prompt
      const prompt = `Task: Explain solution and root cause.
Solution: ${state.solution}
Error Report: ${state.errorReport}`;

      state.explanation = await state.agent.generate(prompt); // Use string prompt
      return state;
    }
  };

  builder
    .addNode(createInputNode())
    .addNode(errorAnalysis)
    .addNode(solutionGen)
    .addNode(explanation)
    .addNode(createToolNode(createTestingTool(), 'Solution Validation'))
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-error', source: 'input', target: 'error-analysis', type: 'debug' as EdgeType }) // Cast type
    .addEdge({ name: 'error-to-solution', source: 'error-analysis', target: 'solution-gen', type: 'debug' as EdgeType }) // Cast type
    .addEdge({ name: 'solution-to-explanation', source: 'solution-gen', target: 'explanation', type: 'debug' as EdgeType }) // Cast type
    .addEdge({ name: 'explanation-to-validation', source: 'explanation', target: 'tool-solution-validation', type: 'debug' as EdgeType }) // Cast type
    .addEdge({ name: 'validation-to-output', source: 'tool-solution-validation', target: 'output', type: 'debug' as EdgeType }) // Cast type
    ;

  return builder.build();
}

export function createChatWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Chat workflow requires a single Agent instance');
  }

  builder
    .addNode(createInputNode())
    .addNode(createAgentNode(_config.agent, 'Chat Agent')) // Now _config.agent is guaranteed to be Agent
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-agent', source: 'input', target: 'agent-chat-agent', type: 'chat' as EdgeType }) // Cast type
    .addEdge({ name: 'agent-to-output', source: 'agent-chat-agent', target: 'output', type: 'chat' as EdgeType }); // Cast type

  return builder.build();
}

export function createAskWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Ask workflow requires a single Agent instance');
  }

  const contextRetriever = createToolNode(createDocumentRetrievalTool(), 'Context Retriever');
  const answerGenerator: GraphNode = {
    id: 'answer-generator',
    type: 'agent',
    name: 'Answer Generator',
    label: 'Answer Generator',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in answer-generator node');
      }
      // Construct a string prompt
      const prompt = `Task: Generate answer based on context.
Context: ${JSON.stringify(state.outputs['tool-context-retriever'])}
Query: ${state.messages.slice(-1)[0]?.content}`;

      const answer = await state.agent.generate(prompt); // Use string prompt
      state.answer = answer;
      return state;
    }
  };


  builder
    .addNode(createInputNode())
    .addNode(contextRetriever)
    .addNode(answerGenerator)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-context', source: 'input', target: 'tool-context-retriever', type: 'ask' as EdgeType }) // Cast type
    .addEdge({ name: 'context-to-answer', source: 'tool-context-retriever', target: 'answer-generator', type: 'ask' as EdgeType }) // Cast type
    .addEdge({ name: 'answer-to-output', source: 'answer-generator', target: 'output', type: 'ask' as EdgeType }); // Cast type

  return builder.build();
}

export function createEditWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  const codeAnalyzer = createToolNode(createCodeAnalysisTool(), 'Code Analyzer');
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Edit workflow requires a single Agent instance');
  }
  const editor: GraphNode = {
    id: 'code-editor',
    type: 'agent',
    name: 'Code Editor',
    label: 'Code Editor',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in code-editor node');
      }
      // Construct a string prompt using promptManager
      const prompt = promptManager.renderPrompt('workflow.codeEditor', {
        analysis: state.outputs['tool-code-analyzer'],
        originalCode: state.messages.slice(-1)[0]?.content || ''
      });

      const editedCode = await state.agent.generate(prompt); // Use string prompt
      state.editedCode = editedCode;
      return state;
    }
  };


  builder
    .addNode(createInputNode())
    .addNode(codeAnalyzer)
    .addNode(editor)
    .addNode(createToolNode(createTestingTool(), 'Edit Validator'))
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-analysis', source: 'input', target: 'tool-code-analyzer', type: 'edit' as EdgeType }) // Cast type
    .addEdge({ name: 'analysis-to-edit', source: 'tool-code-analyzer', target: 'code-editor', type: 'edit' as EdgeType }) // Cast type
    .addEdge({ name: 'edit-to-validation', source: 'code-editor', target: 'tool-edit-validator', type: 'edit' as EdgeType }) // Cast type
    .addEdge({ name: 'validation-to-output', source: 'tool-edit-validator', target: 'output', type: 'edit' as EdgeType }); // Cast type

  return builder.build();
}

export function createCodeGenWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  const specGenerator = createToolNode(createDocumentationTool(), 'Spec Generator');
  if (!(_config.agent instanceof Agent)) {
    throw new Error('CodeGen workflow requires a single Agent instance');
  }
  const codeGenerator: GraphNode = {
    id: 'code-generator',
    type: 'agent',
    name: 'Code Generator',
    label: 'Code Generator',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in code-generator node');
      }
      // Construct a string prompt using promptManager
      const prompt = promptManager.renderPrompt('workflow.codeGenerator', {
        specification: state.outputs['tool-spec-generator'],
        requirements: state.messages.slice(-1)[0]?.content || ''
      });

      const generatedCode = await state.agent.generate(prompt); // Use string prompt
      state.generatedCode = generatedCode;
      return state;
    }
  };
  const codeReviewer: GraphNode = {
    id: 'code-reviewer',
    type: 'agent',
    name: 'Code Reviewer',
    label: 'Code Reviewer',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in code-reviewer node');
      }
      // Construct a string prompt
      const prompt = `Task: Review generated code.
Code: ${state.generatedCode}
Specification: ${state.outputs['tool-spec-generator']}`;

      const reviewFeedback = await state.agent.generate(prompt); // Use string prompt
      state.reviewFeedback = reviewFeedback;
      return state;
    }
  };


  builder
    .addNode(createInputNode())
    .addNode(specGenerator)
    .addNode(codeGenerator)
    .addNode(codeReviewer)
    .addNode(createToolNode(createTestingTool(), 'Generated Code Tests'))
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-spec', source: 'input', target: 'tool-spec-generator', type: 'codegen' as EdgeType }) // Cast type
    .addEdge({ name: 'spec-to-code', source: 'tool-spec-generator', target: 'code-generator', type: 'codegen' as EdgeType }) // Cast type
    .addEdge({ name: 'code-to-review', source: 'code-generator', target: 'code-reviewer', type: 'codegen' as EdgeType }) // Cast type
    .addEdge({ name: 'review-to-test', source: 'code-reviewer', target: 'tool-generated-code-tests', type: 'codegen' as EdgeType }) // Cast type
    .addEdge({ name: 'test-to-output', source: 'tool-generated-code-tests', target: 'output', type: 'codegen' as EdgeType }); // Cast type

  return builder.build();
}

export function createAgenticWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Agentic workflow requires a single Agent instance');
  }

  const taskDefiner = createToolNode(createDocumentationTool(), 'Task Definer');
  const agentExecutor: GraphNode = {
    id: 'agent-executor',
    type: 'agent',
    name: 'Agent Executor',
    label: 'Agent Executor',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in agent-executor node');
      }
      // Construct a string prompt
      const prompt = `Task: Execute the defined task.
Task Definition: ${state.outputs['tool-task-definer']}
Input: ${state.messages.slice(-1)[0]?.content}`;

      const executionResult = await state.agent.generate(prompt); // Use string prompt
      state.executionResult = executionResult;
      return state;
    }
  };


  builder
    .addNode(createInputNode())
    .addNode(taskDefiner)
    .addNode(agentExecutor)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-task', source: 'input', target: 'tool-task-definer', type: 'agentic' as EdgeType }) // Cast type
    .addEdge({ name: 'task-to-execution', source: 'tool-task-definer', target: 'agent-executor', type: 'agentic' as EdgeType }) // Cast type
    .addEdge({ name: 'execution-to-output', source: 'agent-executor', target: 'output', type: 'agentic' as EdgeType }); // Cast type

  return builder.build();
}

export function createMultiAgentWorkflow(_config: any): GraphDefinition {
  if (!(_config.agent instanceof MultiAgentSystem)) {
    throw new Error('Multi-agent workflow requires a MultiAgentSystem instance');
  }

  const builder = new WorkflowBuilder(_config);

  const supervisor = createAgentNode(_config.agent.supervisor, 'Supervisor');
  const coordinator = createAgentNode(_config.agent.coordinator, 'Coordinator');
  const specialist = createAgentNode(_config.agent.specialist, 'Specialist');
  const executor = createAgentNode(_config.agent.executor, 'Executor');

  builder
    .addNode(createInputNode())
    .addNode(supervisor)
    .addNode(coordinator)
    .addNode(specialist)
    .addNode(executor)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-supervisor', source: 'input', target: 'agent-supervisor', type: 'multi-agent' })
    .addEdge({ name: 'supervisor-to-coordinator', source: 'agent-supervisor', target: 'agent-coordinator', type: 'multi-agent' })
    .addEdge({ name: 'coordinator-to-specialist', source: 'agent-coordinator', target: 'agent-specialist', type: 'multi-agent' })
    .addEdge({ name: 'specialist-to-executor', source: 'agent-specialist', target: 'agent-executor', type: 'multi-agent' })
    .addEdge({ name: 'executor-to-output', source: 'agent-executor', target: 'output', type: 'multi-agent' });

  return builder.build();
}

export function createUXDesignWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('UX Design workflow requires a single Agent instance');
  }

  const requirements = createToolNode(createDocumentationTool(), 'Requirements Gatherer', 'Requirements Gatherer');
  const wireframe: GraphNode = {
    id: 'wireframe-designer',
    type: 'agent',
    name: 'Wireframe Designer',
    label: 'Wireframe Designer',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in wireframe-designer node');
      }
      // Construct a string prompt
      const prompt = `Task: Design wireframes based on requirements.
Requirements: ${state.outputs['tool-requirements-gatherer']}
Input: ${state.messages.slice(-1)[0]?.content}`;

      const wireframes = await state.agent.generate(prompt); // Use string prompt
      state.wireframes = wireframes;
      return state;
    }
  };
  const prototype: GraphNode = {
    id: 'prototype-builder',
    type: 'agent',
    name: 'Prototype Builder',
    label: 'Prototype Builder',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in prototype-builder node');
      }
      // Construct a string prompt
      const prompt = `Task: Build a prototype based on wireframes.
Wireframes: ${state.wireframes}
Input: ${state.messages.slice(-1)[0]?.content}`;

      const prototypeResult = await state.agent.generate(prompt); // Use string prompt
      state.prototype = prototypeResult;
      return state;
    }
  };
  const implementation: GraphNode = {
    id: 'ui-implementer',
    type: 'agent',
    name: 'UI Implementer',
    label: 'UI Implementer',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in ui-implementer node');
      }
      // Construct a string prompt
      const prompt = `Task: Implement UI based on prototype.
Prototype: ${state.prototype}
Input: ${state.messages.slice(-1)[0]?.content}`;

      const implementedUI = await state.agent.generate(prompt); // Use string prompt
      state.implementedUI = implementedUI;
      return state;
    }
  };
  const tester = createToolNode(createTestingTool(), 'UX Tester', 'UX Tester');

  builder
    .addNode(createInputNode())
    .addNode(requirements)
    .addNode(wireframe)
    .addNode(prototype)
    .addNode(implementation)
    .addNode(tester)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-requirements', source: 'input', target: 'tool-requirements-gatherer', type: 'ux' as EdgeType }) // Cast type
    .addEdge({ name: 'requirements-to-wireframe', source: 'input', target: 'wireframe-designer', type: 'ux' as EdgeType }) // Corrected source
    .addEdge({ name: 'wireframe-to-prototype', source: 'wireframe-designer', target: 'prototype-builder', type: 'ux' as EdgeType }) // Cast type
    .addEdge({ name: 'prototype-to-implementation', source: 'prototype-builder', target: 'ui-implementer', type: 'ux' as EdgeType }) // Cast type
    .addEdge({ name: 'implementation-to-test', source: 'ui-implementer', target: 'tool-ux-tester', type: 'ux' as EdgeType }) // Cast type
    .addEdge({ name: 'test-to-output', source: 'tool-ux-tester', target: 'output', type: 'ux' as EdgeType }); // Cast type

  return builder.build();
}

export function createResearchWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Research workflow requires a single Agent instance');
  }

  const questionAnalyzer: GraphNode = {
    id: 'question-analyzer',
    type: 'agent',
    name: 'Question Analyzer',
    label: 'Question Analyzer',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in question-analyzer node');
      }
      // Construct a string prompt using promptManager
      const prompt = promptManager.renderPrompt('workflow.questionAnalyzer', {
        question: state.messages.slice(-1)[0]?.content || ''
      });

      const analyzedQuestion = await state.agent.generate(prompt); // Use string prompt
      state.analyzedQuestion = analyzedQuestion;
      return state;
    }
  };
  const researchEngine = createToolNode(createDocumentRetrievalTool(), 'Research Engine');
  const analysis: GraphNode = {
    id: 'research-analyst',
    type: 'agent',
    name: 'Research Analyst',
    label: 'Research Analyst',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in research-analyst node');
      }
      // Construct a string prompt
      const prompt = `Task: Analyze research results.
Research Results: ${state.outputs['tool-research-engine']}
Analyzed Question: ${state.analyzedQuestion}`;

      const analysisResult = await state.agent.generate(prompt); // Use string prompt
      state.analysisResult = analysisResult;
      return state;
    }
  };
  const reportGenerator: GraphNode = {
    id: 'report-generator',
    type: 'agent',
    name: 'Report Generator',
    label: 'Report Generator',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in report-generator node');
      }
      // Construct a string prompt
      const prompt = `Task: Generate a research report.
Analysis Results: ${state.analysisResult}
Input: ${state.messages.slice(-1)[0]?.content}`;

      const report = await state.agent.generate(prompt); // Use string prompt
      state.report = report;
      return state;
    }
  };

  builder
    .addNode(createInputNode())
    .addNode(questionAnalyzer)
    .addNode(researchEngine)
    .addNode(analysis)
    .addNode(reportGenerator)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-question', source: 'input', target: 'question-analyzer', type: 'research' as EdgeType }) // Cast type
    .addEdge({ name: 'question-to-research', source: 'question-analyzer', target: 'tool-research-engine', type: 'research' as EdgeType }) // Cast type
    .addEdge({ name: 'research-to-analysis', source: 'tool-research-engine', target: 'research-analyst', type: 'research' as EdgeType }) // Cast type
    .addEdge({ name: 'analysis-to-report', source: 'research-analyst', target: 'report-generator', type: 'research' as EdgeType }) // Cast type
    .addEdge({ name: 'report-to-output', source: 'report-generator', target: 'output', type: 'research' as EdgeType }); // Cast type

  return builder.build();
}

export function createDevOpsWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder(_config);

  // Check if _config.agent is a single Agent for this workflow (DevOps might not strictly require an Agent, but adding check for consistency)
  if (!(_config.agent instanceof Agent)) {
    // This workflow primarily uses tools, but if an agent node were added later, this check would be relevant.
    // For now, we'll allow MultiAgentSystem as well, but log a warning if it's not a single Agent.
    if (!(_config.agent instanceof MultiAgentSystem)) {
      Logger.instance.warn('DevOps workflow configured with neither a single Agent nor a MultiAgentSystem.');
    }
  }

  const codeCommit = createToolNode(createDocumentationTool(), 'Code Commit');
  const build = createToolNode(createCI_CDTool(), 'Build System');
  const test = createToolNode(createTestingTool(), 'Test Suite');
  const deploy = createToolNode(createDeploymentTool(), 'Deployment');
  const monitor = createToolNode(createMonitoringTool(), 'Monitoring');

  builder
    .addNode(createInputNode())
    .addNode(codeCommit)
    .addNode(build)
    .addNode(test)
    .addNode(deploy)
    .addNode(monitor)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-commit', source: 'input', target: 'tool-code-commit', type: 'devops' as EdgeType }) // Cast type
    .addEdge({ name: 'commit-to-build', source: 'tool-code-commit', target: 'tool-build-system', type: 'devops' as EdgeType }) // Cast type
    .addEdge({ name: 'build-to-test', source: 'tool-build-system', target: 'tool-test-suite', type: 'devops' as EdgeType }) // Cast type
    .addEdge({ name: 'test-to-deploy', source: 'tool-test-suite', target: 'tool-deployment', type: 'devops' as EdgeType }) // Cast type
    .addEdge({ name: 'deploy-to-monitor', source: 'tool-deployment', target: 'tool-monitoring', type: 'devops' as EdgeType }) // Cast type
    .addEdge({ name: 'monitor-to-output', source: 'tool-monitoring', target: 'output', type: 'devops' as EdgeType }); // Cast type

  return builder.build();
}

export function createAgileWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder({
    ..._config,
    methodology: 'agile'
  });

  const backlog = createToolNode(createDocumentationTool(), 'Product Backlog');
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Agile workflow requires a single Agent instance');
  }
  const planning = createAgentNode(_config.agent, 'Sprint Planning');
  const development = createAgentNode(_config.agent, 'Development');
  const review = createAgentNode(_config.agent, 'Sprint Review');
  const retrospective = createAgentNode(_config.agent, 'Retrospective');

  builder
    .addNode(createInputNode())
    .addNode(backlog)
    .addNode(planning)
    .addNode(development)
    .addNode(review)
    .addNode(retrospective)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-backlog', source: 'input', target: 'tool-product-backlog', type: 'agile' })
    .addEdge({ name: 'backlog-to-planning', source: 'tool-product-backlog', target: 'agent-sprint-planning', type: 'agile' })
    .addEdge({ name: 'planning-to-development', source: 'agent-sprint-planning', target: 'agent-development', type: 'agile' })
    .addEdge({ name: 'development-to-review', source: 'agent-development', target: 'agent-sprint-review', type: 'agile' })
    .addEdge({ name: 'review-to-retrospective', source: 'agent-sprint-review', target: 'agent-retrospective', type: 'agile' })
    .addEdge({ name: 'retrospective-to-output', source: 'agent-retrospective', target: 'output', type: 'agile' })
    .addEdge({ name: 'retrospective-to-backlog', source: 'agent-retrospective', target: 'tool-product-backlog', type: 'feedback' });

  return builder.build();
}

export function createXPWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder({
    ..._config,
    methodology: 'xp'
  });

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('XP workflow requires a single Agent instance');
  }

  const userStory = createToolNode(createDocumentationTool(), 'User Story');
  const pairProgramming: GraphNode = {
    id: 'pair-programming',
    type: 'agent',
    name: 'Pair Programming',
    label: 'Pair Programming',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in pair-programming node');
      }
      // Construct a string prompt
      const prompt = `Task: Perform pair programming on the user story.
User Story: ${state.outputs['tool-user-story']}
Input: ${state.messages.slice(-1)[0]?.content}`;

      const developedCode = await state.agent.generate(prompt); // Use string prompt
      state.developedCode = developedCode;
      return state;
    }
  };
  const tdd: GraphNode = {
    id: 'tdd-implementation',
    type: 'agent',
    name: 'TDD Implementation',
    label: 'TDD Implementation',
    agent: _config.agent, // Now _config.agent is guaranteed to be Agent
    execute: async (state) => {
      // Ensure agent is an Agent before calling generate
      if (!state.agent || !(state.agent instanceof Agent)) {
        throw new Error('Agent not available or not a single Agent instance in tdd-implementation node');
      }
      // Construct a string prompt
      const prompt = `Task: Implement code using Test-Driven Development.
Developed Code: ${state.developedCode}
Input: ${state.messages.slice(-1)[0]?.content}`;

      const tddCode = await state.agent.generate(prompt); // Use string prompt
      state.tddCode = tddCode;
      return state;
    }
  };
  const continuousIntegration = createToolNode(createCI_CDTool(), 'CI Pipeline');
  const customerFeedback = createToolNode(createDocumentationTool(), 'Customer Feedback');

  builder
    .addNode(createInputNode())
    .addNode(userStory)
    .addNode(pairProgramming)
    .addNode(tdd)
    .addNode(continuousIntegration)
    .addNode(customerFeedback)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-story', source: 'input', target: 'tool-user-story', type: 'xp' as EdgeType }) // Cast type
    .addEdge({ name: 'story-to-pairing', source: 'tool-user-story', target: 'pair-programming', type: 'xp' as EdgeType }) // Cast type
    .addEdge({ name: 'pairing-to-tdd', source: 'pair-programming', target: 'tdd-implementation', type: 'xp' as EdgeType }) // Cast type
    .addEdge({ name: 'tdd-to-ci', source: 'tdd-implementation', target: 'tool-ci-pipeline', type: 'xp' as EdgeType }) // Cast type
    .addEdge({ name: 'ci-to-feedback', source: 'tool-ci-pipeline', target: 'tool-customer-feedback', type: 'xp' as EdgeType }) // Cast type
    .addEdge({ name: 'feedback-to-output', source: 'tool-customer-feedback', target: 'output', type: 'xp' as EdgeType }) // Cast type
    .addEdge({ name: 'feedback-to-story', source: 'tool-customer-feedback', target: 'tool-user-story', type: 'feedback' as EdgeType }); // Cast type

  return builder.build();
}

export function createScrumWorkflow(_config: any): GraphDefinition {
  const builder = new WorkflowBuilder({
    ..._config,
    methodology: 'scrum'
  });

  // Check if _config.agent is a single Agent for this workflow
  if (!(_config.agent instanceof Agent)) {
    throw new Error('Scrum workflow requires a single Agent instance');
  }

  const productOwner = createAgentNode(_config.agent, 'Product Owner'); // Now _config.agent is guaranteed to be Agent
  const scrumMaster = createAgentNode(_config.agent, 'Scrum Master'); // Now _config.agent is guaranteed to be Agent
  const dailyScrum = createToolNode(createDocumentationTool(), 'Daily Scrum');
  const sprint = createAgentNode(_config.agent, 'Sprint Execution'); // Now _config.agent is guaranteed to be Agent
  const review = createAgentNode(_config.agent, 'Sprint Review'); // Now _config.agent is guaranteed to be Agent

  builder
    .addNode(createInputNode())
    .addNode(productOwner)
    .addNode(scrumMaster)
    .addNode(dailyScrum)
    .addNode(sprint)
    .addNode(review)
    .addNode(createOutputNode())
    .addEdge({ name: 'input-to-po', source: 'input', target: 'agent-product-owner', type: 'scrum' as EdgeType }) // Cast type
    .addEdge({ name: 'po-to-sm', source: 'agent-product-owner', target: 'agent-scrum-master', type: 'scrum' as EdgeType }) // Cast type
    .addEdge({ name: 'sm-to-daily', source: 'agent-scrum-master', target: 'tool-daily-scrum', type: 'scrum' as EdgeType }) // Cast type
    .addEdge({ name: 'daily-to-sprint', source: 'tool-daily-scrum', target: 'agent-sprint-execution', type: 'scrum' as EdgeType }) // Cast type
    .addEdge({ name: 'sprint-to-review', source: 'agent-sprint-execution', target: 'agent-sprint-review', type: 'scrum' as EdgeType }) // Cast type
    .addEdge({ name: 'review-to-output', source: 'agent-sprint-review', target: 'output', type: 'scrum' as EdgeType }); // Cast type

  return builder.build();
}

// ==================== WORKFLOW FACTORY ====================

export class WorkflowFactory {
  static create(_config: any): GraphDefinition {
    switch (_config.type) {
      case 'memory':
        return createMemoryEnhancedWorkflow(_config);
      case 'document-qa':
        return createDocumentQAWorkflow(_config);
      case 'refactoring':
        return createCodeRefactoringWorkflow(_config);
      case 'debugging':
        return createDebuggingWorkflow(_config);
      case 'chat':
        return createChatWorkflow(_config);
      case 'ask':
        return createAskWorkflow(_config);
      case 'edit':
        return createEditWorkflow(_config);
      case 'codegen':
        return createCodeGenWorkflow(_config);
      case 'agentic':
        return createAgenticWorkflow(_config);
      case 'multi-agent':
        return createMultiAgentWorkflow(_config);
      case 'ui-ux':
        return createUXDesignWorkflow(_config);
      case 'research':
        return createResearchWorkflow(_config);
      case 'devops':
        return createDevOpsWorkflow(_config);
      case 'agile':
        return createAgileWorkflow(_config);
      case 'xp':
        return createXPWorkflow(_config);
      case 'scrum':
        return createScrumWorkflow(_config);
      case 'pr-creation': {
        // Import dynamically to avoid circular dependencies
        const { createPRCreationWorkflow } = require('./prWorkflows');
        return createPRCreationWorkflow(_config);
      }
      case 'pr-review': {
        // Import dynamically to avoid circular dependencies
        const { createPRReviewWorkflow } = require('./prWorkflows');
        return createPRReviewWorkflow(_config);
      }
      case 'checkpoint': {
        // Import dynamically to avoid circular dependencies
        const { createCheckpointWorkflow } = require('./checkpointWorkflow');
        return createCheckpointWorkflow(_config);
      }
      case 'mcp': {
        // Import dynamically to avoid circular dependencies
        const { createMCPWorkflow } = require('./mcpWorkflow');
        return createMCPWorkflow(_config.agent, _config);
      }
      case 'pattern-refactoring': {
        // Import dynamically to avoid circular dependencies
        const { createPatternRefactoringWorkflow } = require('./advancedRefactoring');
        return createPatternRefactoringWorkflow(_config.agent, _config);
      }
      case 'technical-debt': {
        // Import dynamically to avoid circular dependencies
        const { createTechnicalDebtWorkflow } = require('./advancedRefactoring');
        return createTechnicalDebtWorkflow(_config.agent, _config);
      }
      default:
        throw new Error(`Unknown workflow type: ${_config.type}`);
    }
  }
}
