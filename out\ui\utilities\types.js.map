{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/ui/utilities/types.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface VSCodeAPI {\n    postMessage(message: any): void;\n    getState(): any;\n    setState(_state: any): void;\n}\n\nexport interface ChatMessage {\n    id: string;\n    role: 'user' | 'assistant' | 'system' | 'error';\n    content: string;\n    timestamp: number;\n}\n\nexport interface Provider {\n    id: string;\n    name: string;\n}\n\nexport interface Model {\n    id: string;\n    name: string;\n    provider: string;\n}\n\nexport interface ChatState {\n    isProcessing: boolean;\n    messages: ChatMessage[];\n    isTTSActive: boolean;\n    currentMode: string;\n    currentProvider: string;\n    currentModel: string;\n    availableProviders: Provider[];\n    availableModels: Model[];\n}"]}