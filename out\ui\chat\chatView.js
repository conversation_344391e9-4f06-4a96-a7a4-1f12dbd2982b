"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatPanel = exports.ChatSession = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
const utils_1 = require("../../utils");
const agentManager_1 = require("../../agents/agentUtilities/agentManager");
const audioService_1 = require("../../services/audioService");
const ttsSettingsSection_1 = require("../settings/ttsSettingsSection");
const llmService_1 = require("../../llm/llmService");
const statusBar_1 = require("../feedback/statusBar");
function getMimeType(filePath) {
    const extension = filePath.split('.').pop()?.toLowerCase() || '';
    switch (extension) {
        // Image formats
        case 'png': return 'image/png';
        case 'jpg': return 'image/jpeg';
        case 'jpeg': return 'image/jpeg';
        case 'gif': return 'image/gif';
        case 'webp': return 'image/webp';
        case 'bmp': return 'image/bmp';
        case 'svg': return 'image/svg+xml';
        case 'ico': return 'image/vnd.microsoft.icon';
        case 'tif': return 'image/tiff';
        case 'tiff': return 'image/tiff';
        // Text / Markup / Documents
        case 'txt': return 'text/plain';
        case 'md': return 'text/markdown';
        case 'markdown': return 'text/markdown';
        case 'mdx': return 'text/mdx';
        case 'rtf': return 'application/rtf';
        case 'html': return 'text/html';
        case 'htm': return 'text/html';
        case 'xml': return 'application/xml';
        case 'xhtml': return 'application/xhtml+xml';
        case 'css': return 'text/css';
        case 'csv': return 'text/csv';
        case 'tsv': return 'text/tab-separated-values';
        // Office / PDF
        case 'pdf': return 'application/pdf';
        case 'doc': return 'application/msword';
        case 'docx': return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        case 'xls': return 'application/vnd.ms-excel';
        case 'xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        case 'ppt': return 'application/vnd.ms-powerpoint';
        case 'pptx': return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
        case 'odt': return 'application/vnd.oasis.opendocument.text';
        case 'ods': return 'application/vnd.oasis.opendocument.spreadsheet';
        case 'odp': return 'application/vnd.oasis.opendocument.presentation';
        // Archives / Packages
        case 'zip': return 'application/zip';
        case 'tar': return 'application/x-tar';
        case 'gz': return 'application/gzip';
        case 'tgz': return 'application/gzip';
        case 'bz2': return 'application/x-bzip2';
        case 'xz': return 'application/x-xz';
        case '7z': return 'application/x-7z-compressed';
        case 'rar': return 'application/vnd.rar';
        case 'jar': return 'application/java-archive';
        case 'war': return 'application/java-archive';
        case 'ear': return 'application/java-archive';
        case 'deb': return 'application/vnd.debian.binary-package';
        case 'rpm': return 'application/x-redhat-package-manager';
        case 'msi': return 'application/x-msi';
        case 'exe': return 'application/vnd.microsoft.portable-executable';
        case 'dmg': return 'application/x-apple-diskimage';
        case 'pkg': return 'application/vnd.apple.installer+xml';
        case 'apk': return 'application/vnd.android.package-archive';
        case 'aar': return 'application/java-archive';
        case 'appx': return 'application/vnd.microsoft.appx';
        case 'debuginfo': return 'application/octet-stream';
        case 'so': return 'application/x-sharedlib';
        case 'dll': return 'application/x-msdownload';
        case 'dylib': return 'application/x-mach-binary';
        // Code & Scripts
        case 'js': return 'application/javascript';
        case 'mjs': return 'application/javascript';
        case 'cjs': return 'application/javascript';
        case 'ts': return 'application/typescript';
        case 'tsx': return 'text/tsx';
        case 'jsx': return 'text/jsx';
        case 'coffee': return 'text/coffeescript';
        case 'py': return 'text/x-python';
        case 'pyc': return 'application/octet-stream';
        case 'pyo': return 'application/octet-stream';
        case 'pyd': return 'application/octet-stream';
        case 'java': return 'text/x-java-source';
        case 'class': return 'application/java-vm';
        case 'kt': return 'text/x-kotlin';
        case 'kts': return 'text/x-kotlin';
        case 'swift': return 'text/x-swift';
        case 'cs': return 'text/x-csharp';
        case 'cpp': return 'text/x-c++src';
        case 'cc': return 'text/x-c++src';
        case 'cxx': return 'text/x-c++src';
        case 'c': return 'text/x-c';
        case 'h': return 'text/x-c';
        case 'hh': return 'text/x-c++src';
        case 'hpp': return 'text/x-c++src';
        case 'm': return 'text/x-objectivec';
        case 'mm': return 'text/x-objectivec++';
        case 'go': return 'text/x-go';
        case 'rs': return 'text/rust';
        case 'dart': return 'application/dart';
        case 'rb': return 'text/x-ruby';
        case 'erb': return 'text/x-ruby';
        case 'php': return 'application/x-httpd-php';
        case 'phtml': return 'application/x-httpd-php';
        case 'pl': return 'text/x-perl';
        case 'pm': return 'text/x-perl';
        case 'sh': return 'application/x-sh';
        case 'bash': return 'application/x-sh';
        case 'zsh': return 'application/x-sh';
        case 'ksh': return 'application/x-sh';
        case 'fish': return 'application/x-sh';
        case 'ps1': return 'text/x-powershell';
        case 'psm1': return 'text/x-powershell';
        case 'cmd': return 'application/cmd';
        case 'bat': return 'application/bat';
        case 'asm': return 'text/x-asm';
        case 's': return 'text/x-asm';
        case 'hs': return 'text/x-haskell';
        case 'lhs': return 'text/x-literate-haskell';
        case 'erl': return 'text/x-erlang';
        case 'ex': return 'text/x-elixir';
        case 'exs': return 'text/x-elixir';
        case 'lua': return 'text/x-lua';
        case 'jl': return 'text/x-julia';
        case 'scala': return 'text/x-scala';
        case 'groovy': return 'text/x-groovy';
        case 'gradle': return 'text/x-groovy';
        case 'dockerfile': return 'text/plain';
        case 'env': return 'text/plain';
        case 'json': return 'application/json';
        case 'jsonc': return 'application/json';
        case 'yaml': return 'application/x-yaml';
        case 'yml': return 'application/x-yaml';
        case 'toml': return 'application/toml';
        case 'ini': return 'text/plain';
        case 'cfg': return 'text/plain';
        case 'conf': return 'text/plain';
        case 'log': return 'text/plain';
        case 'lock': return 'text/plain';
        // Markup & Templates
        case 'xaml': return 'application/xaml+xml';
        case 'ejs': return 'text/html';
        case 'hbs': return 'text/x-handlebars-template';
        case 'njk': return 'text/x-nunjucks';
        case 'twig': return 'text/html';
        case 'svelte': return 'application/svelte';
        case 'vue': return 'text/x-vue';
        // Data / Config Formats
        case 'sql': return 'application/sql';
        case 'graphql': return 'application/graphql';
        case 'gql': return 'application/graphql';
        case 'proto': return 'text/x-protobuf';
        // Notebooks
        case 'ipynb': return 'application/x-ipynb+json';
        // CI/CD configs
        case 'travis.yml': return 'application/x-yaml';
        case 'gitlab-ci.yml': return 'application/x-yaml';
        // Default binary
        default: return 'application/octet-stream';
    }
}
/**
 * Manages a chat session's state and messages
 */
class ChatSession {
    context;
    messages = [];
    processing = false;
    attachedFiles = [];
    branches = [];
    activeBranchId = 'main';
    sessionId = `session_${Date.now()}`;
    sessionName = 'Chat Session';
    constructor(context) {
        this.context = context;
    }
    addMessage(message) {
        this.messages.push(message);
    }
    getMessages() {
        return [...this.messages];
    }
    removeMessage(messageId) {
        this.messages = this.messages.filter(msg => msg.id !== messageId);
    }
    updateMessage(updatedMessage) {
        const index = this.messages.findIndex(msg => msg.id === updatedMessage.id);
        if (index !== -1) {
            this.messages[index] = updatedMessage;
        }
    }
    clearMessages() {
        this.messages = [];
    }
    getProcessing() {
        return this.processing;
    }
    setProcessing(processing) {
        this.processing = processing;
    }
    addAttachedFile(file) {
        this.attachedFiles.push(file);
    }
    getAttachedFiles() {
        return [...this.attachedFiles];
    }
    clearAttachedFiles() {
        this.attachedFiles = [];
    }
    async save() {
        try {
            await this.context.globalState.update('chatSession', {
                messages: this.messages,
                processing: this.processing,
                attachedFiles: this.attachedFiles.map(file => file.name),
                branches: this.branches,
                activeBranchId: this.activeBranchId,
                sessionId: this.sessionId,
                sessionName: this.sessionName
            });
            logger_1.logger.debug('Chat session saved successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to save chat session:', error);
        }
    }
    async load() {
        try {
            const saved = this.context.globalState.get('chatSession');
            if (saved) {
                this.messages = saved.messages;
                this.processing = saved.processing;
                this.attachedFiles = saved.attachedFiles.map(name => ({
                    name,
                    path: '',
                    content: '',
                    type: ''
                }));
                this.branches = saved.branches || [];
                this.activeBranchId = saved.activeBranchId || 'main';
                this.sessionId = saved.sessionId || this.sessionId;
                this.sessionName = saved.sessionName || this.sessionName;
                logger_1.logger.debug(`Loaded chat session with ${this.messages.length} messages`);
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to load chat session:', error);
        }
    }
    createNewSession(name) {
        this.sessionId = `session_${Date.now()}`;
        this.sessionName = name || 'Chat Session';
        this.messages = [];
        this.attachedFiles = [];
        this.branches = [{
                id: 'main',
                name: 'Main',
                messages: [],
                isActive: true,
                createdAt: Date.now()
            }];
        this.activeBranchId = 'main';
        return this.sessionId;
    }
    branchConversation(messageId, branchName) {
        const branchId = `branch_${Date.now()}`;
        const newBranch = {
            id: branchId,
            name: branchName || `Branch ${this.branches.length + 1}`,
            parentMessageId: messageId,
            messages: [],
            isActive: false,
            createdAt: Date.now()
        };
        const msgIndex = this.messages.findIndex(m => m.id === messageId);
        if (msgIndex >= 0) {
            newBranch.messages = this.messages.slice(0, msgIndex + 1).map(m => ({ ...m }));
        }
        this.branches.push(newBranch);
        this.activeBranchId = branchId;
        this.branches = this.branches.map(b => ({ ...b, isActive: b.id === this.activeBranchId }));
        return branchId;
    }
    switchBranch(branchId) {
        if (this.branches.find(b => b.id === branchId)) {
            this.activeBranchId = branchId;
            this.branches = this.branches.map(b => ({ ...b, isActive: b.id === branchId }));
        }
    }
}
exports.ChatSession = ChatSession;
class ChatPanel {
    static currentPanel;
    static viewType = 'codessaChat';
    _panel;
    agent = null;
    agentManager = null;
    _mode;
    _extensionUri;
    _disposables = [];
    _chatSession;
    _audioService;
    _ttsSettingsView;
    _isRecording = false;
    _voiceEnabled = false;
    _collaborative = false;
    _cancelTokenSource;
    _context;
    constructor(panel, agent, extensionUri, context, mode = 'chat') {
        this._panel = panel;
        this.agent = agent;
        this.agentManager = agentManager_1.AgentManager.getInstance();
        this._extensionUri = extensionUri;
        this._context = context;
        this._mode = mode;
        this._chatSession = new ChatSession(context);
        this._audioService = audioService_1.AudioService.getInstance(context);
        this._ttsSettingsView = ttsSettingsSection_1.TTSSettingsView.getInstance(context);
        this._isRecording = false;
        // Set initial HTML content
        this._updateWebview();
        // Listen for panel disposal
        this._panel.onDidDispose(() => this.dispose(), undefined, this._disposables);
        // Update content when panel becomes visible
        this._panel.onDidChangeViewState(() => {
            if (this._panel.visible) {
                this._updateWebview();
            }
        }, null, this._disposables);
        // Handle messages from webview
        this._panel.webview.onDidReceiveMessage(this._handleWebviewMessage.bind(this), undefined, this._disposables);
        // Send initial data to the webview
        this._sendInitialData();
    }
    async _handleSwitchAgent(agentId) {
        if (!this.agentManager) {
            logger_1.logger.error('Agent manager not initialized');
            return;
        }
        try {
            // Get the agent directly since getAgents might not be available
            const agent = this.agentManager.getAgent(agentId);
            if (!agent) {
                throw new Error(`Agent with ID ${agentId} not found`);
            }
            // Store the current agent's state if needed
            // ...
            // Switch to the new agent
            this.agent = agent;
            // Update the UI to reflect the agent switch
            await this._panel.webview.postMessage({
                command: 'agentSwitched',
                agentId: agent.id,
                agentName: agent.name,
                description: agent.description || ''
            });
            logger_1.logger.info(`Switched to agent: ${agent.name} (${agent.id})`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to switch agent: ${error}`);
            await this._panel.webview.postMessage({
                command: 'error',
                text: `Failed to switch agent: ${error}`
            });
        }
    }
    async _handleWebviewMessage(message) {
        // Validate the message
        if (!message || (typeof message !== 'object')) {
            logger_1.logger.warn('Received invalid message from webview:', message);
            return;
        }
        // Extract command from message with type safety
        const command = message.command || message.type;
        if (!command || typeof command !== 'string') {
            logger_1.logger.warn('Received message without a valid command/type:', message);
            return;
        }
        logger_1.logger.debug(`Processing webview command: ${command}`, { message });
        try {
            // Handle agent-related commands
            if (command === 'switchAgent') {
                if (message.agentId && typeof message.agentId === 'string') {
                    await this._handleSwitchAgent(message.agentId);
                }
                else {
                    throw new Error('Invalid or missing agentId for switchAgent command');
                }
                return;
            }
            if (command === 'getAgents') {
                // Get all available agents from the manager with proper typing
                const agents = [];
                try {
                    // Safely get agent IDs with fallback
                    const agentIds = this.agentManager ? this.agentManager.getAgentIds() : [];
                    // Process each agent
                    for (const agentId of agentIds) {
                        try {
                            const agent = this.agentManager?.getAgent(agentId);
                            if (agent) {
                                agents.push({
                                    id: agent.id,
                                    name: agent.name || 'Unnamed Agent',
                                    description: agent.description || '',
                                    isActive: agent.id === this.agent?.id
                                });
                            }
                        }
                        catch (agentError) {
                            logger_1.logger.warn(`Error processing agent ${agentId}:`, agentError);
                        }
                    }
                    // Send the agents list to the webview
                    this._panel.webview.postMessage({
                        type: 'updateAgents',
                        agents,
                        currentAgentId: this.agent?.id || ''
                    });
                }
                catch (error) {
                    logger_1.logger.error('Failed to get agents:', error);
                    throw new Error('Failed to retrieve available agents');
                }
                return;
            }
            if (command === 'getCurrentAgent') {
                // Send current agent info to the webview
                this._panel.webview.postMessage({
                    type: 'currentAgent',
                    agentId: this.agent?.id || '',
                    agentName: this.agent?.name || '',
                    description: this.agent?.description || ''
                });
                return;
            }
            // Handle other command types with proper validation
            try {
                switch (command) {
                    case 'sendMessage':
                        if (!message.text || typeof message.text !== 'string') {
                            throw new Error('Missing or invalid text for sendMessage');
                        }
                        await this._handleMessage(message.text);
                        break;
                    case 'newSession':
                        this._chatSession.createNewSession();
                        await this._chatSession.save();
                        vscode.window.showInformationMessage('New chat session created');
                        break;
                    case 'branchConversation':
                        if (message.text) {
                            const lastMsg = [...this._chatSession.getMessages()].reverse().find(m => m.content.includes(message.text));
                            if (lastMsg) {
                                const branchId = this._chatSession.branchConversation(lastMsg.id);
                                await this._chatSession.save();
                                vscode.window.showInformationMessage(`Branched conversation: ${branchId}`);
                            }
                        }
                        break;
                    case 'switchBranch':
                        if (message.text) {
                            this._chatSession.switchBranch(message.text);
                            await this._chatSession.save();
                            vscode.window.showInformationMessage(`Switched to branch: ${message.text}`);
                        }
                        break;
                    case 'toggleVoice':
                        this._voiceEnabled = !this._voiceEnabled;
                        vscode.window.showInformationMessage(`Voice mode ${this._voiceEnabled ? 'enabled' : 'disabled'}`);
                        break;
                    case 'shareSession':
                        await this._handleShareSession();
                        break;
                    case 'joinSession':
                        await this._handleJoinSession(message.text || '');
                        break;
                    case 'openSettings':
                        await this._handleOpenSettings();
                        break;
                    case 'exportChat':
                        await this._handleExportChat();
                        break;
                    case 'toggleTTS':
                        if (typeof message.state === 'boolean') {
                            await this._handleToggleTTS(message.state);
                        }
                        else {
                            logger_1.logger.warn('Invalid toggleTTS state:', message.state);
                        }
                        break;
                    case 'startRecording':
                        await this._handleStartRecording();
                        break;
                    case 'stopRecording':
                        await this._handleStopRecording();
                        break;
                    case 'addContext':
                        await this._handleAddContext();
                        break;
                    case 'attachFile':
                        await this._handleAttachFile();
                        break;
                    case 'getProviders':
                        await this._handleGetProviders();
                        break;
                    case 'getModels':
                        await this._handleGetModels();
                        break;
                    case 'changeMode':
                        if (message.mode && typeof message.mode === 'string') {
                            await this._handleChangeMode(message.mode);
                        }
                        else {
                            logger_1.logger.warn('Invalid mode for changeMode:', message.mode);
                        }
                        break;
                    case 'changeProvider':
                        if (message.provider && typeof message.provider === 'string') {
                            await this._handleChangeProvider(message.provider);
                        }
                        else {
                            logger_1.logger.warn('Invalid provider for changeProvider:', message.provider);
                        }
                        break;
                    case 'changeModel':
                        if (message.model && typeof message.model === 'string') {
                            await this._handleChangeModel(message.model);
                        }
                        else {
                            logger_1.logger.warn('Invalid model for changeModel:', message.model);
                        }
                        break;
                    case 'cancel':
                        await this._handleCancel();
                        break;
                    default:
                        logger_1.logger.warn(`Unknown command received: ${command}`, message);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
                logger_1.logger.error(`Error handling command '${command}':`, error);
                // Create error message for the UI
                const errorMsg = {
                    id: `error_${Date.now()}`,
                    role: 'error',
                    content: `Error: ${errorMessage}`,
                    timestamp: Date.now()
                };
                // Update chat session and UI
                this._chatSession.addMessage(errorMsg);
                await this._chatSession.save();
                this._panel.webview.postMessage({
                    type: 'addMessage',
                    message: errorMsg
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Error handling webview message:', error);
        }
    }
    _updateWebview() {
        if (!this._panel) {
            return;
        }
        this._panel.webview.html = this._getHtmlForWebview(this._panel.webview);
    }
    _getHtmlForWebview(webview) {
        // Get the local path to the script and styles for the webview
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chatView.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chatView.css'));
        const codiconUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'node_modules', '@vscode/codicons', 'dist', 'codicon.css'));
        // Use a nonce to whitelist which scripts can be run
        const nonce = (0, utils_1.getNonce)();
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src ${webview.cspSource} data:; style-src ${webview.cspSource} 'unsafe-inline'; font-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="${styleUri}" rel="stylesheet" />
            <link href="${codiconUri}" rel="stylesheet" />
            <title>Codessa Chat</title>
        </head>
        <body>
            <div class="chat-container">
                <div class="chat-header">
                    <div class="header-left">
                        <h1>Codessa Chat</h1>
                        <div class="agent-selector">
                            <select id="agent-selector" title="Select Agent">
                                <!-- Agent options will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="toolbar">
                        <button id="clear-chat" title="Clear Chat">
                            <span class="codicon codicon-clear-all"></span>
                        </button>
                        <button id="export-chat" title="Export Chat">
                            <span class="codicon codicon-export"></span>
                        </button>
                        <button id="tts-toggle" title="Toggle TTS">
                            <span class="codicon codicon-unmute"></span>
                        </button>
                        <button id="record-audio" title="Record Audio">
                            <span class="codicon codicon-mic"></span>
                        </button>
                        <button id="attach-file" title="Attach File">
                            <span class="codicon codicon-add"></span>
                        </button>
                        <button id="attach-folder" title="Attach Folder">
                            <span class="codicon codicon-folder-opened"></span>
                        </button>
                        <button id="upload-image" title="Upload Image">
                            <span class="codicon codicon-file-media"></span>
                        </button>
                    </div>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <!-- Messages will be inserted here -->
                </div>
                
                <div class="chat-input-container">
                    <div class="attached-files" id="attached-files">
                        <!-- Attached files will be shown here -->
                    </div>
                    <div class="input-wrapper">
                        <textarea id="message-input" placeholder="Type your message here..." rows="3"></textarea>
                        <button id="send-message" title="Send Message">
                            <span class="codicon codicon-send"></span>
                        </button>
                    </div>
                </div>
            </div>
            
            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`;
    }
    async _sendInitialData() {
        try {
            // Get all available agents with proper typing
            const availableAgents = [];
            // Get agent IDs using type assertion as a fallback
            const agentIds = this.agentManager ? this.agentManager.getAgentIds() : [];
            for (const agentId of agentIds) {
                const agent = this.agentManager?.getAgent(agentId);
                if (agent) {
                    availableAgents.push({
                        id: agent.id,
                        name: agent.name,
                        description: agent.description || '',
                        isActive: agent.id === this.agent?.id
                    });
                }
            }
            if (availableAgents.length === 0) {
                logger_1.logger.warn('No agents available in AgentManager');
            }
            // Send initial data to the webview with proper typing
            const initialData = {
                type: 'initialData',
                messages: this._chatSession.getMessages(),
                isRecording: this._isRecording,
                mode: this._mode,
                model: this.agent?.llmModel || 'default',
                provider: this.agent?.llmProvider || 'default',
                availableAgents,
                currentAgentId: this.agent?.id || '',
                currentAgentName: this.agent?.name || 'Unknown Agent',
                currentAgentDescription: this.agent?.description || ''
            };
            logger_1.logger.debug('Sending initial data to webview:', {
                agentCount: availableAgents.length,
                currentAgent: this.agent?.id || '',
                messageCount: this._chatSession.getMessages().length
            });
            this._panel.webview.postMessage(initialData);
            // Also send a separate agent update for consistency
            this._panel.webview.postMessage({
                type: 'updateAgents',
                agents: availableAgents,
                currentAgentId: this.agent?.id || ''
            });
            // Send current agent info
            this._panel.webview.postMessage({
                type: 'currentAgent',
                agentId: this.agent?.id || '',
                agentName: this.agent?.name || 'Unknown Agent',
                description: this.agent?.description || ''
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to send initial data to webview:', error);
            // Send error to webview
            this._panel.webview.postMessage({
                type: 'initializationError',
                message: 'Failed to initialize chat session. Please try again.'
            });
            // Log the error to the chat
            const errorMessage = {
                id: `error-${Date.now()}`,
                role: 'error',
                content: 'Failed to initialize chat session. Some features may not be available.',
                timestamp: Date.now()
            };
            this._chatSession.addMessage(errorMessage);
            await this._chatSession.save();
            this._panel.webview.postMessage({
                type: 'addMessage',
                message: errorMessage
            });
        }
    }
    static createOrShow(extensionUri, agent, context, mode = 'chat', column = vscode.ViewColumn.Beside) {
        // If we already have a panel, show it
        if (ChatPanel.currentPanel) {
            ChatPanel.currentPanel._panel.reveal(column);
            return;
        }
        // Otherwise, create a new panel
        const panel = vscode.window.createWebviewPanel(ChatPanel.viewType, 'Codessa', column, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [
                vscode.Uri.joinPath(extensionUri, 'media'),
                vscode.Uri.joinPath(extensionUri, 'out', 'compiled'),
                vscode.Uri.joinPath(extensionUri, 'node_modules'),
            ]
        });
        ChatPanel.currentPanel = new ChatPanel(panel, agent, extensionUri, context, mode);
    }
    _update() {
        this._updateWebview();
    }
    dispose() {
        // Clean up resources
        this._disposables.forEach(disposable => disposable.dispose());
        this._cancelTokenSource?.dispose();
        // Clear the current panel reference
        if (ChatPanel.currentPanel === this) {
            ChatPanel.currentPanel = undefined;
        }
    }
    async _handleGetProviders() {
        try {
            const providers = llmService_1.llmService.getAllProviders();
            const providerList = providers.map(provider => ({
                id: provider.providerId,
                name: provider.providerId, // Using providerId as name since providerName doesn't exist
                isConfigured: provider.isConfigured()
            }));
            if (this._panel?.webview) {
                this._panel.webview.postMessage({
                    type: 'providers',
                    providers: providerList
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to get providers:', error);
            vscode.window.showErrorMessage('Failed to get providers.');
        }
    }
    async _handleChangeMode(mode) {
        try {
            await vscode.workspace.getConfiguration('codessa').update('operationMode', mode, true);
            vscode.window.showInformationMessage(`Operation mode changed to: ${mode}`);
            if (this._panel?.webview) {
                this._panel.webview.postMessage({
                    type: 'modeChanged',
                    mode: mode
                });
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to change operation mode';
            logger_1.logger.error('Error changing operation mode:', error);
            vscode.window.showErrorMessage(`Failed to change operation mode: ${errorMessage}`);
        }
    }
    async _handleCancel() {
        try {
            if (this._cancelTokenSource) {
                this._cancelTokenSource.cancel();
                this._cancelTokenSource.dispose();
                this._cancelTokenSource = undefined;
                this._chatSession.setProcessing(false);
                await this._chatSession.save();
                if (this._panel?.webview) {
                    this._panel.webview.postMessage({
                        type: 'processingStateChanged',
                        isProcessing: false
                    });
                }
                vscode.window.showInformationMessage('Operation cancelled');
            }
        }
        catch (error) {
            logger_1.logger.error('Error cancelling operation:', error);
            vscode.window.showErrorMessage('Failed to cancel operation');
        }
    }
    async _handleMessage(text, attachments = []) {
        if (!text.trim() && attachments.length === 0) {
            return;
        }
        const messageId = `msg_${Date.now()}`;
        const userMessage = {
            id: messageId,
            role: 'user',
            content: text,
            timestamp: Date.now(),
            attachments: attachments.length > 0 ? attachments : undefined
        };
        this._chatSession.addMessage(userMessage);
        this._chatSession.setProcessing(true);
        await this._chatSession.save();
        this._panel.webview.postMessage({ type: 'addMessage', message: userMessage });
        this._panel.webview.postMessage({ type: 'processingStateChanged', isProcessing: true });
        this._cancelTokenSource = new vscode.CancellationTokenSource();
        statusBar_1.statusBarManager.setActivityIndicator(true);
        let finalResponse = 'An unexpected error occurred.';
        try {
            if (!this.agentManager) {
                throw new Error('Agent manager not initialized');
            }
            const receiverAgent = this.agentManager.getReceiverAgent();
            if (!receiverAgent) {
                throw new Error('Receiver agent not available');
            }
            const supervisorAgent = this.agentManager.getSupervisorAgent();
            if (!supervisorAgent) {
                throw new Error('Supervisor agent not available. The system cannot proceed.');
            }
            const activeEditor = vscode.window.activeTextEditor;
            const activeDocument = activeEditor?.document;
            const selection = activeEditor?.selection;
            const agentRunContext = {
                variables: {
                    message: { id: messageId, content: text, attachments, timestamp: Date.now() },
                    chatHistory: this._chatSession.getMessages().slice(0, -1),
                    attachedFiles: this._chatSession.getAttachedFiles()
                },
                workspace: {
                    currentFile: activeDocument?.uri.fsPath,
                    selection: selection && activeDocument ? {
                        text: activeDocument.getText(selection),
                        range: {
                            start: activeDocument.offsetAt(selection.start),
                            end: activeDocument.offsetAt(selection.end)
                        }
                    } : undefined,
                    workspaceFolders: vscode.workspace.workspaceFolders?.map(f => f.uri.fsPath) || []
                },
                tools: new Map(Array.from(this.agent?.tools?.entries() || [])),
                streamingContext: {
                    streamId: `stream-${messageId}`,
                    onStream: (event) => {
                        if (event?.type === 'data' && event.content) {
                            this._panel.webview.postMessage({ type: 'token', token: event.content });
                        }
                    },
                    cancellationToken: this._cancelTokenSource.token
                },
            };
            // Step 1: Process input through the receiver agent
            const receiverResult = await receiverAgent.run({
                prompt: text,
                mode: 'receive',
                context: agentRunContext
            });
            if (!receiverResult?.output) {
                throw new Error('Receiver agent produced no output.');
            }
            logger_1.logger.debug('Receiver agent processed input:', { processed: receiverResult.output });
            // Step 2: Format the receiver's output into a structured JSON message for the supervisor
            const communicationMessage = {
                type: 'agent-to-agent-stream',
                source: { agentId: receiverAgent.id, agentName: receiverAgent.name },
                target: { agentId: supervisorAgent.id, agentName: supervisorAgent.name },
                payload: {
                    contentType: 'text/plain',
                    content: receiverResult.output,
                },
                metadata: {
                    timestamp: new Date().toISOString(),
                    conversationId: this._chatSession.getMessages()[0]?.id || messageId,
                    originalMessageId: messageId,
                }
            };
            const supervisorPrompt = JSON.stringify(communicationMessage);
            logger_1.logger.debug('Sending the following JSON to supervisor:', supervisorPrompt);
            // Step 3: Send the structured message to the supervisor agent
            // Determine mode dynamically without changing UI
            const chosenMode = this._determineResponseMode(text) || this._mode?.id || 'chat';
            const supervisorResult = await supervisorAgent.run({
                prompt: supervisorPrompt,
                mode: chosenMode,
                context: agentRunContext
            });
            finalResponse = supervisorResult?.output || 'No response from supervisor agent.';
        }
        catch (error) {
            logger_1.logger.error('Error in agent execution pipeline:', error);
            finalResponse = `Error: ${error instanceof Error ? error.message : String(error)}`;
        }
        finally {
            // This block ensures that a response, even an error, is always sent to the user.
            const assistantMessage = {
                id: (0, utils_1.getNonce)(),
                role: 'assistant',
                content: finalResponse,
                timestamp: Date.now()
            };
            this._chatSession.addMessage(assistantMessage);
            this._chatSession.setProcessing(false);
            await this._chatSession.save();
            this._panel.webview.postMessage({ type: 'addMessage', message: assistantMessage });
            this._panel.webview.postMessage({ type: 'processingStateChanged', isProcessing: false });
            this._cancelTokenSource?.dispose();
            this._cancelTokenSource = undefined;
            statusBar_1.statusBarManager.setActivityIndicator(false);
        }
    }
    _determineResponseMode(content) {
        const lower = content.toLowerCase();
        if (lower.includes('debug') || lower.includes('error'))
            return 'debug';
        if (lower.includes('edit') || lower.includes('change') || lower.includes('modify'))
            return 'edit';
        if (lower.includes('explain') || lower.includes('what') || lower.includes('why') || lower.includes('how'))
            return 'ask';
        if (lower.includes('generate') || lower.includes('create') || lower.includes('build'))
            return 'agent';
        if (lower.includes('refactor') || lower.includes('improve'))
            return 'refactor';
        return 'chat';
    }
    async _handleShareSession() {
        try {
            const shareLink = `codessa://chat/join/${this._chatSession?.sessionId || Date.now()}`;
            await vscode.env.clipboard.writeText(shareLink);
            this._collaborative = true;
            vscode.window.showInformationMessage('Session link copied to clipboard');
        }
        catch (error) {
            logger_1.logger.error('Error sharing session:', error);
            vscode.window.showErrorMessage('Failed to share session');
        }
    }
    async _handleJoinSession(sessionId) {
        try {
            if (!sessionId) {
                vscode.window.showWarningMessage('No session ID provided');
                return;
            }
            this._collaborative = true;
            vscode.window.showInformationMessage(`Joining session: ${sessionId}`);
        }
        catch (error) {
            logger_1.logger.error('Error joining session:', error);
            vscode.window.showErrorMessage('Failed to join session');
        }
    }
    async _handleOpenSettings() {
        try {
            const { AllSettingsPanel } = await Promise.resolve().then(() => __importStar(require('../settings/allSettingsPanel')));
            const { ProviderManager } = await Promise.resolve().then(() => __importStar(require('../../llm/providerManager')));
            const { MemoryManager } = await Promise.resolve().then(() => __importStar(require('../../memory/memoryManager')));
            const providerManager = ProviderManager.getInstance();
            const memoryManager = new MemoryManager();
            await memoryManager.initialize(this._context);
            if (AllSettingsPanel) {
                await AllSettingsPanel.createOrShow(this._context, this._extensionUri, memoryManager, providerManager);
            }
            else {
                throw new Error('Failed to load AllSettingsPanel');
            }
        }
        catch (error) {
            logger_1.logger.error('Error opening settings:', error);
            vscode.window.showErrorMessage('Failed to open settings');
            // Fallback to the default settings
            try {
                await vscode.commands.executeCommand('workbench.action.openSettings', 'codessa');
            }
            catch (fallbackError) {
                logger_1.logger.error('Failed to open fallback settings:', fallbackError);
            }
        }
    }
    async _handleExportChat() {
        try {
            const messages = this._chatSession.getMessages();
            if (messages.length === 0) {
                vscode.window.showInformationMessage('No messages to export');
                return;
            }
            const content = messages.map((msg) => `[${new Date(msg.timestamp).toISOString()}] ${msg.role}: ${msg.content}`).join('\n\n');
            const uri = await vscode.window.showSaveDialog({
                filters: { 'Text': ['txt'], 'Markdown': ['md'] },
                defaultUri: vscode.Uri.file('chat_export.md')
            });
            if (uri) {
                await vscode.workspace.fs.writeFile(uri, Buffer.from(content, 'utf8'));
                vscode.window.showInformationMessage(`Chat exported to ${uri.fsPath}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Error exporting chat:', error);
            vscode.window.showErrorMessage('Failed to export chat');
        }
    }
    async _handleToggleTTS(enabled) {
        try {
            await vscode.workspace.getConfiguration('codessa').update('tts.enabled', enabled, true);
            if (enabled) {
                vscode.window.showInformationMessage('Text-to-speech enabled');
            }
            else {
                vscode.window.showInformationMessage('Text-to-speech disabled');
            }
        }
        catch (error) {
            logger_1.logger.error('Error toggling TTS:', error);
            vscode.window.showErrorMessage('Failed to toggle text-to-speech');
        }
    }
    async _handleStartRecording() {
        try {
            this._isRecording = true;
            this._cancelTokenSource = new vscode.CancellationTokenSource();
            vscode.window.showInformationMessage('Recording started...');
            await this._audioService.startRecording();
            vscode.window.showInformationMessage('Audio recording started. Use the stop button when done.');
        }
        catch (error) {
            if (error instanceof vscode.CancellationError) {
                vscode.window.showInformationMessage('Recording cancelled');
            }
            else {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                logger_1.logger.error('Error during recording:', error);
                vscode.window.showErrorMessage(`Recording failed: ${errorMessage}`);
            }
            this._isRecording = false;
        }
    }
    async _handleStopRecording() {
        if (!this._isRecording)
            return;
        try {
            const audioData = await this._audioService.stopRecording();
            this._isRecording = false;
            if (!audioData) {
                vscode.window.showInformationMessage('No audio data was recorded');
                return;
            }
            // The audioData is a base64 data URL, so we need to extract the base64 part and decode it.
            const base64Data = audioData.split(',')[1];
            const binaryString = atob(base64Data);
            const len = binaryString.length;
            const audioBuffer = new Uint8Array(len);
            for (let i = 0; i < len; i++) {
                audioBuffer[i] = binaryString.charCodeAt(i);
            }
            if (audioBuffer && audioBuffer.length > 0) {
                await this._processAudioInput(audioBuffer);
            }
            else {
                throw new Error('Failed to process audio data: empty or invalid format');
            }
        }
        catch (error) {
            logger_1.logger.error('Error processing recording:', error);
            vscode.window.showErrorMessage(`Error processing recording: ${error instanceof Error ? error.message : String(error)}`);
        }
        finally {
            this._isRecording = false;
            this._cancelTokenSource?.dispose();
            this._cancelTokenSource = undefined;
        }
    }
    async _handleAddContext() {
        try {
            const text = await vscode.window.showInputBox({
                prompt: 'Enter additional context for the conversation',
                placeHolder: 'Context information...'
            });
            if (text) {
                const contextMessage = {
                    id: `context_${Date.now()}`,
                    role: 'system',
                    content: `[Context] ${text}`,
                    timestamp: Date.now()
                };
                this._chatSession.addMessage(contextMessage);
                await this._chatSession.save();
                this._updateWebview();
            }
        }
        catch (error) {
            logger_1.logger.error('Error adding context:', error);
            vscode.window.showErrorMessage('Failed to add context');
        }
    }
    async _handleAttachFile() {
        try {
            const uris = await vscode.window.showOpenDialog({
                canSelectFiles: true,
                canSelectFolders: false,
                canSelectMany: true,
                openLabel: 'Attach Files',
                filters: {
                    'All Files': ['*']
                }
            });
            if (!uris || uris.length === 0) {
                return;
            }
            const files = [];
            for (const uri of uris) {
                try {
                    const content = await vscode.workspace.fs.readFile(uri);
                    const fileName = uri.fsPath.split(/[\\/]/).pop() || 'file';
                    const fileContent = Buffer.from(content).toString('utf8');
                    const fileType = getMimeType(uri.fsPath);
                    files.push({
                        name: fileName,
                        path: uri.fsPath,
                        content: fileContent,
                        type: fileType
                    });
                }
                catch (error) {
                    logger_1.logger.error(`Error reading file ${uri.fsPath}:`, error);
                    vscode.window.showErrorMessage(`Failed to read file: ${uri.fsPath}`);
                    continue;
                }
            }
            if (files.length > 0) {
                // Add files to the chat session
                for (const file of files) {
                    this._chatSession.addAttachedFile(file);
                }
                // Create a message that represents the file attachments
                const fileList = files.map(f => f.name).join(', ');
                const messageText = `[Attached files: ${fileList}]`;
                // Route the message with attachments through the receiver agent
                await this._handleMessage(messageText, files);
                // Update the UI
                await this._chatSession.save();
                this._updateWebview();
            }
            else {
                vscode.window.showWarningMessage('No files could be attached.');
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            logger_1.logger.error('Error attaching files:', error);
            vscode.window.showErrorMessage(`Failed to attach files: ${errorMessage}`);
        }
    }
    async _handleUploadImage() {
        try {
            const uris = await vscode.window.showOpenDialog({
                canSelectFiles: true,
                canSelectFolders: false,
                canSelectMany: true,
                filters: {
                    'Images': ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp']
                },
                openLabel: 'Select Images to Upload'
            });
            if (!uris || uris.length === 0) {
                return;
            }
            const images = [];
            for (const uri of uris) {
                try {
                    const content = await vscode.workspace.fs.readFile(uri);
                    const fileName = uri.fsPath.split(/[\\/]/).pop() || 'image';
                    const mimeType = getMimeType(uri.fsPath);
                    // For images, we'll store them as base64 data URLs
                    const base64Content = Buffer.from(content).toString('base64');
                    const dataUrl = `data:${mimeType};base64,${base64Content}`;
                    images.push({
                        name: fileName,
                        path: uri.fsPath,
                        content: dataUrl,
                        type: mimeType
                    });
                }
                catch (error) {
                    logger_1.logger.error(`Error reading image ${uri.fsPath}:`, error);
                    vscode.window.showErrorMessage(`Failed to read image: ${uri.fsPath}`);
                    continue;
                }
            }
            if (images.length > 0) {
                // Add images to the chat session
                for (const img of images) {
                    this._chatSession.addAttachedFile(img);
                }
                // Create a message that represents the image attachments
                const imageList = images.map(img => img.name).join(', ');
                const messageText = `[Attached images: ${imageList}]`;
                // Route the message with image attachments through the receiver agent
                await this._handleMessage(messageText, images);
                // Update the UI
                await this._chatSession.save();
                this._updateWebview();
                vscode.window.showInformationMessage(`Uploaded ${images.length} images`);
            }
            else {
                vscode.window.showWarningMessage('No images could be attached.');
            }
        }
        catch (error) {
            logger_1.logger.error('Error uploading images:', error);
            vscode.window.showErrorMessage('Failed to upload images');
        }
    }
    async _processAudioInput(audioData) {
        try {
            if (!this._audioService) {
                throw new Error('Audio service not available');
            }
            // Show processing indicator
            const status = vscode.window.setStatusBarMessage('Processing audio...');
            try {
                const text = await this._audioService.speechToText(audioData);
                status.dispose(); // Clear status bar
                if (text && text.trim()) {
                    await this._handleMessage(text.trim());
                }
                else {
                    vscode.window.showWarningMessage('No speech was detected in the audio.');
                }
            }
            catch (error) {
                status.dispose(); // Ensure status bar is cleared on error
                throw error;
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            logger_1.logger.error('Error processing audio input:', error);
            vscode.window.showErrorMessage(`Failed to process audio: ${errorMessage}`);
        }
    }
    async _handleChangeModel(modelId) {
        if (!modelId) {
            vscode.window.showWarningMessage('No model selected');
            return;
        }
        try {
            await vscode.workspace.getConfiguration('codessa').update('selectedModel', modelId, true);
            vscode.window.showInformationMessage(`Model changed to: ${modelId}`);
            if (this._panel) {
                this._panel.webview.postMessage({
                    type: 'currentModel',
                    model: modelId
                });
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to change model';
            logger_1.logger.error('Error changing model:', error);
            vscode.window.showErrorMessage(`Failed to change model: ${errorMessage}`);
        }
    }
    async _handleGetModels() {
        try {
            const providers = llmService_1.llmService.getAllProviders();
            const currentProvider = providers.find(p => p.isConfigured()) || providers[0];
            if (!currentProvider) {
                vscode.window.showWarningMessage('No provider configured.');
                return;
            }
            const models = await currentProvider.listModels();
            const modelList = models.map(model => ({
                id: model.id,
                name: model.name || model.id,
                description: model.description || ''
            }));
            const currentModel = modelList[0]?.id;
            if (this._panel && this._panel.webview) {
                this._panel.webview.postMessage({
                    type: 'models',
                    models: modelList
                });
                if (currentModel) {
                    this._panel.webview.postMessage({
                        type: 'currentModel',
                        model: currentModel
                    });
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to get models:', error);
            vscode.window.showErrorMessage('Failed to get models.');
        }
    }
    async _handleChangeProvider(providerId) {
        try {
            if (!providerId) {
                vscode.window.showWarningMessage('No provider selected.');
                return;
            }
            await vscode.workspace.getConfiguration('codessa').update('selectedProvider', providerId, true);
            vscode.window.showInformationMessage(`Provider changed to: ${providerId}`);
            const providers = llmService_1.llmService.getAllProviders();
            const provider = providers.find(p => p.providerId === providerId);
            if (provider && this._panel?.webview) {
                const models = await provider.listModels();
                const modelList = models.map(model => ({
                    id: model.id,
                    name: model.name || model.id,
                    description: model.description || ''
                }));
                const currentModel = modelList[0]?.id;
                this._panel.webview.postMessage({
                    type: 'models',
                    models: modelList
                });
                if (currentModel) {
                    this._panel.webview.postMessage({
                        type: 'currentModel',
                        model: currentModel
                    });
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to change provider:', error);
            vscode.window.showErrorMessage('Failed to change provider.');
        }
    }
}
exports.ChatPanel = ChatPanel;
//# sourceMappingURL=chatView.js.map