/* Use VS Code CSS variables for consistent theming */
:root {
  --vscode-font-family: var(--vscode-font-family);
  --vscode-font-size: var(--vscode-font-size);
  --vscode-foreground: var(--vscode-foreground);
  --vscode-background: var(--vscode-editor-background);
  --vscode-button-background: var(--vscode-button-background);
  --vscode-button-foreground: var(--vscode-button-foreground);
  --vscode-button-hoverBackground: var(--vscode-button-hoverBackground);
  --vscode-input-background: var(--vscode-input-background);
  --vscode-input-foreground: var(--vscode-input-foreground);
  --vscode-input-border: var(--vscode-input-border);
  --vscode-scrollbar-shadow: var(--vscode-scrollbar-shadow);
  --vscode-widget-shadow: var(--vscode-widget-shadow);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  color: var(--vscode-foreground);
  background-color: var(--vscode-background);
  height: 100vh;
  overflow: hidden;
}

/* Chat Container */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

/* Toolbars */
.top-toolbar,
.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid var(--vscode-input-border);
  background: var(--vscode-background);
  flex-shrink: 0;
}

.top-toolbar-left,
.top-toolbar-right,
.input-toolbar-left,
.input-toolbar-right {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Toolbar buttons */
.toolbar-button {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: 1px solid var(--vscode-input-border);
  padding: 6px 8px;
  border-radius: 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
}

.toolbar-button:hover {
  background: var(--vscode-button-hoverBackground);
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dropdowns */
.mode-selector,
.provider-selector,
.model-selector {
  background: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border: 1px solid var(--vscode-input-border);
  padding: 4px 8px;
  border-radius: 2px;
  font-size: 13px;
  min-width: 100px;
  max-width: 150px;
}

/* Chat Messages Container */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  position: relative;
  background: var(--vscode-background);
}

.chat-messages::-webkit-scrollbar {
  width: 14px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 8px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Empty state */
.empty-chat-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  color: var(--vscode-descriptionForeground);
}

.empty-chat-content h3 {
  margin: 0 0 8px 0;
  color: var(--vscode-foreground);
}

.empty-chat-content p {
  margin: 0;
  opacity: 0.8;
}

/* Messages */
.message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 12px;
}

.message.user {
  flex-direction: row-reverse;
}

.message.user .content-wrapper {
  background: var(--vscode-button-background);
  border-radius: 12px 12px 4px 12px;
}

.message.assistant .content-wrapper {
  background: var(--vscode-input-background);
  border-radius: 12px 12px 12px 4px;
}

.message.system .content-wrapper {
  background: var(--vscode-textCodeBlock-background);
  border-left: 3px solid var(--vscode-textLink-foreground);
  border-radius: 4px;
  font-style: italic;
}

.message.error .content-wrapper {
  background: var(--vscode-inputValidation-errorBackground);
  border-left: 3px solid var(--vscode-inputValidation-errorBorder);
  border-radius: 4px;
}

/* Message avatar */
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--vscode-button-background);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--vscode-button-foreground);
}

.message.user .avatar {
  background: var(--vscode-button-secondaryBackground);
}

/* Message content */
.content-wrapper {
  max-width: 80%;
  padding: 12px 16px;
  word-wrap: break-word;
}

.content {
  margin-bottom: 4px;
}

.timestamp {
  font-size: 11px;
  opacity: 0.6;
  color: var(--vscode-descriptionForeground);
}

/* Thinking indicator */
.thinking-dots {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.thinking-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--vscode-foreground);
  opacity: 0.3;
  animation: thinking 1.5s ease-in-out infinite;
}

.thinking-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes thinking {
  0%, 60%, 100% { opacity: 0.3; }
  30% { opacity: 1; }
}

/* Typing indicator */
.typing-indicator {
  padding: 8px 16px;
  display: none;
  align-items: center;
  gap: 8px;
  color: var(--vscode-descriptionForeground);
  border-top: 1px solid var(--vscode-input-border);
}

.typing-indicator.visible {
  display: flex;
}

.typing-text {
  font-size: 13px;
}

/* Chat Input Container */
.chat-input-container {
  border-top: 1px solid var(--vscode-input-border);
  background: var(--vscode-background);
  padding: 12px;
  flex-shrink: 0;
}

.input-wrapper-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-bottom: 8px;
}

.input-actions-main,
.input-actions-secondary {
  display: flex;
  gap: 4px;
}

.input-wrapper {
  flex: 1;
}

.message-input {
  width: 100%;
  min-height: 36px;
  max-height: 250px;
  padding: 8px 12px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  resize: none;
  outline: none;
}

.message-input:focus {
  border-color: var(--vscode-focusBorder);
}

.message-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Input action buttons */
.input-action-button,
.send-button,
.cancel-button,
.tts-button {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: 1px solid var(--vscode-input-border);
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.input-action-button:hover,
.send-button:hover,
.cancel-button:hover,
.tts-button:hover {
  background: var(--vscode-button-hoverBackground);
}

.send-button {
  background: var(--vscode-button-background);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cancel-button {
  background: var(--vscode-inputValidation-errorBackground);
  border-color: var(--vscode-inputValidation-errorBorder);
  color: var(--vscode-inputValidation-errorForeground);
  display: none;
}

.cancel-button.visible {
  display: flex;
}

.tts-button.active {
  background: var(--vscode-button-secondaryBackground);
}

.input-secondary-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

/* Code blocks */
pre {
  background: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  padding: 12px;
  margin: 8px 0;
  overflow-x: auto;
  position: relative;
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background: var(--vscode-input-background);
  border-bottom: 1px solid var(--vscode-input-border);
  font-size: 12px;
}

.code-block-language {
  color: var(--vscode-descriptionForeground);
  font-weight: 500;
}

.copy-code-button {
  background: transparent;
  border: 1px solid var(--vscode-input-border);
  color: var(--vscode-button-foreground);
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.copy-code-button:hover {
  background: var(--vscode-button-hoverBackground);
}

/* Background animation effects */
.background-logo-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.water-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

.water-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, 
    rgba(var(--vscode-button-background), 0.01), 
    rgba(var(--vscode-button-background), 0.03));
  z-index: -1;
}

/* Agent status indicators */
.agent-status-indicator {
  position: fixed;
  top: 10px;
  right: 10px;
  background: var(--vscode-notifications-background);
  border: 1px solid var(--vscode-notifications-border);
  color: var(--vscode-notifications-foreground);
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  box-shadow: var(--vscode-widget-shadow);
}

.agent-status-indicator.status-starting {
  border-left: 3px solid var(--vscode-textLink-foreground);
}

.agent-status-indicator.status-progress {
  border-left: 3px solid var(--vscode-progressBar-background);
}

.agent-status-indicator.status-complete {
  border-left: 3px solid var(--vscode-testing-iconPassed);
}

.agent-status-indicator.status-error {
  border-left: 3px solid var(--vscode-inputValidation-errorBorder);
}

/* Responsive design */
@media (max-width: 600px) {
  .top-toolbar,
  .input-toolbar {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .mode-selector,
  .provider-selector,
  .model-selector {
    min-width: 80px;
    max-width: 120px;
  }
  
  .content-wrapper {
    max-width: 90%;
  }
}

/* Dark theme specific adjustments */
body.vscode-dark {
  --chat-shadow: rgba(0, 0, 0, 0.3);
}

/* Light theme specific adjustments */  
body.vscode-light {
  --chat-shadow: rgba(0, 0, 0, 0.1);
}

/* High contrast theme adjustments */
body.vscode-high-contrast {
  .toolbar-button,
  .input-action-button,
  .send-button,
  .cancel-button,
  .tts-button {
    border-width: 2px;
  }
  
  .message-input {
    border-width: 2px;
  }
}

/* Preserved existing agent selector styles */
.agent-selector select {
  background-color: var(--vscode-dropdown-background);
  color: var(--vscode-dropdown-foreground);
  border: 1px solid var(--vscode-dropdown-border);
  border-radius: 2px;
  padding: 2px 20px 2px 6px;
  font-size: 12px;
  line-height: 16px;
  height: 24px;
  min-width: 120px;
  max-width: 200px;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M4.5%207L8%2010.5L11.5%207H4.5Z%22%20fill%3D%22%23CCCCCC%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 4px center;
  background-size: 16px;
}

.agent-selector select:focus {
  outline: 1px solid var(--vscode-focusBorder);
  outline-offset: -1px;
}

.agent-selector select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Preserved agent-specific message styling */
.message[data-agent]::before {
  content: attr(data-agent);
  display: block;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--vscode-descriptionForeground);
  margin-bottom: 2px;
}