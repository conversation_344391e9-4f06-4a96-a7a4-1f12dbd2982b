import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../types/agent';
import { MemoryManager } from '../memory/memoryManager';
import { z } from 'zod';

export class MemorySearchTool implements ITool {
  readonly id = 'memorySearch';
  readonly name = 'Memory Search';
  readonly description = 'Search memories by keyword or tag.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    query: z.string().describe('Keyword or tag to search.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      query: { type: 'string', description: 'Keyword or tag to search.' }
    },
    required: ['query']
  };
  private memoryManager: MemoryManager;
  constructor(memoryManager: MemoryManager) { this.memoryManager = memoryManager; }
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const query = input.query as string;
    if (!query) {
      return { success: false, error: '\'query\' is required.', toolId: this.id, actionName };
    }
    const entries = await this.memoryManager.getMemories();
    const results = entries.filter(e =>
      (e.content && e.content.includes(query)) ||
            (e.metadata?.tags && e.metadata.tags.includes(query))
    );
    return { success: true, output: results, toolId: this.id, actionName };
  }
}

export class MemoryUpdateTool implements ITool {
  readonly id = 'memoryUpdate';
  readonly name = 'Memory Update';
  readonly description = 'Update a memory entry by ID.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    id: z.string().describe('Memory ID to update.'),
    content: z.string().describe('New content.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      id: { type: 'string', description: 'Memory ID to update.' },
      content: { type: 'string', description: 'New content.' }
    },
    required: ['id', 'content']
  };
  private memoryManager: MemoryManager;
  constructor(memoryManager: MemoryManager) { this.memoryManager = memoryManager; }
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const id = input.id as string;
    const content = input.content as string;

    if (!id || !content) {
      return { success: false, error: '\'id\' and \'content\' are required.', toolId: this.id, actionName };
    }

    const entries = await this.memoryManager.getMemories();
    const entry = entries.find(e => e.id === id);

    if (!entry) {
      return { success: false, output: 'Memory entry not found', toolId: this.id, actionName };
    }

    entry.content = content;

    if ((this.memoryManager as any).memoryProvider && (this.memoryManager as any).memoryProvider.updateMemory) {
      const ok = await (this.memoryManager as any).memoryProvider.updateMemory(id, entry);
      return {
        success: ok,
        output: ok ? 'Updated' : 'Not found',
        toolId: this.id,
        actionName
      };
    }

    return {
      success: true,
      output: 'Updated (in-memory only)',
      toolId: this.id,
      actionName
    };
  }
}

export class MemoryTagTool implements ITool {
  readonly id = 'memoryTag';
  readonly name = 'Memory Tag';
  readonly description = 'Add or remove tags from a memory entry.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    id: z.string().describe('Memory ID.'),
    tags: z.array(z.string()).describe('Tags to add/remove.'),
    mode: z.enum(['add', 'remove']).describe('Add or remove tags.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      id: { type: 'string', description: 'Memory ID.' },
      tags: { type: 'array', items: { type: 'string' }, description: 'Tags to add/remove.' },
      mode: { type: 'string', enum: ['add', 'remove'], description: 'Add or remove tags.' }
    },
    required: ['id', 'tags', 'mode']
  };
  private memoryManager: MemoryManager;
  constructor(memoryManager: MemoryManager) { this.memoryManager = memoryManager; }
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const id = input.id as string;
    const tags = input.tags as string[];
    const mode = input.mode as string;

    if (!id || !tags || !mode) {
      return { success: false, error: '\'id\', \'tags\', and \'mode\' are required.', toolId: this.id, actionName };
    }

    const entries = await this.memoryManager.getMemories();
    const entry = entries.find(e => e.id === id);

    if (!entry) {
      return { success: false, output: 'Memory entry not found', toolId: this.id, actionName };
    }

    if (!entry.metadata.tags) {
      entry.metadata.tags = [];
    }

    if (mode === 'add') {
      for (const tag of tags) {
        if (!entry.metadata.tags.includes(tag)) {
          entry.metadata.tags.push(tag);
        }
      }
    } else if (mode === 'remove') {
      entry.metadata.tags = entry.metadata.tags.filter((t: string) => !tags.includes(t));
    } else {
      return { success: false, error: 'Invalid mode. Must be "add" or "remove".', toolId: this.id, actionName };
    }

    if ((this.memoryManager as any).memoryProvider && (this.memoryManager as any).memoryProvider.updateMemory) {
      const ok = await (this.memoryManager as any).memoryProvider.updateMemory(id, entry);
      return {
        success: ok,
        output: ok ? `Tags ${mode}ed` : 'Not found',
        toolId: this.id,
        actionName
      };
    }

    return {
      success: true,
      output: `Tags ${mode}ed (in-memory only)`,
      toolId: this.id,
      actionName
    };
  }
}

export class MemoryVisualizationTool implements ITool {
  readonly id = 'memoryViz';
  readonly name = 'Memory Visualization';
  readonly description = 'Visualize memory graph or clusters.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({});
  readonly inputSchema = {
    type: 'object',
    properties: {},
    required: []
  };
  private memoryManager: MemoryManager;
  constructor(memoryManager: MemoryManager) { this.memoryManager = memoryManager; }
  async execute(actionName: string | undefined, _input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      const entries = await this.memoryManager.getMemories();

      if (!entries || entries.length === 0) {
        return {
          success: true,
          output: { message: 'No memory entries found to visualize', clusters: {}, connections: [] },
          toolId: this.id,
          actionName
        };
      }

      // Cluster memories by tags
      const clusters: Record<string, any[]> = {};
      const untagged: any[] = [];

      // Track connections between memories (memories that share tags)
      const connections: Array<{source: string, target: string, strength: number}> = [];

      // First pass: organize by tags
      for (const entry of entries) {
        if (entry.metadata?.tags && entry.metadata.tags.length > 0) {
          for (const tag of entry.metadata.tags) {
            if (!clusters[tag]) {
              clusters[tag] = [];
            }
            clusters[tag].push({
              id: entry.id,
              content: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),
              tags: entry.metadata.tags,
              created: entry.metadata.timestamp || 'unknown'
            });
          }
        } else {
          untagged.push({
            id: entry.id,
            content: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),
            created: entry.metadata.timestamp || 'unknown'
          });
        }
      }

      // Second pass: find connections between memories
      const processedPairs = new Set<string>();

      for (const entry of entries) {
        if (!entry.metadata?.tags || entry.metadata.tags.length === 0) continue;

        for (const otherEntry of entries) {
          if (entry.id === otherEntry.id) continue;
          if (!otherEntry.metadata?.tags || otherEntry.metadata.tags.length === 0) continue;

          // Create a unique key for this pair to avoid duplicates
          const pairKey = [entry.id, otherEntry.id].sort().join('-');
          if (processedPairs.has(pairKey)) continue;
          processedPairs.add(pairKey);

          // Calculate connection strength based on shared tags
          const entryTags = new Set(entry.metadata.tags);
          const otherTags = new Set(otherEntry.metadata.tags);
          // Convert Set to Array before using filter to avoid downlevelIteration issues
          const entryTagsArray = Array.from(entryTags);
          const sharedTags = entryTagsArray.filter(tag => otherTags.has(tag));

          if (sharedTags.length > 0) {
            connections.push({
              source: entry.id,
              target: otherEntry.id,
              strength: sharedTags.length
            });
          }
        }
      }

      // Add untagged memories to the result if there are any
      if (untagged.length > 0) {
        clusters['untagged'] = untagged;
      }

      return {
        success: true,
        output: {
          totalMemories: entries.length,
          clusters,
          connections,
          clusterCount: Object.keys(clusters).length,
          connectionCount: connections.length
        },
        toolId: this.id,
        actionName
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Memory visualization failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }
}
