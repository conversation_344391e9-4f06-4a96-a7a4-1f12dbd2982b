// Agents section logic and rendering
import { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';

export interface Agent {
    name: string;
    description: string;
    systemPrompt: string;
    provider: string;
    modelId: string;
    supervisor: boolean;
    enabled: boolean;
    chainedAgentIds: string[];
}

interface AgentsSettings {
    agents: Agent[];
    perAgentLLMEnabled?: boolean;
    [key: string]: unknown; // Allow additional properties
}

let agents: Agent[] = [];
let editingAgentIdx: number | null = null;
const sectionTheme: UIThemeConfig['section'] = defaultUIThemeConfig.section;

/**
 * Renders the Agents settings section in the settings UI
 * @param container - The HTMLElement where the section should be rendered
 * @param settings - An object containing the current agents settings
 */
export function renderAgentsSettingsSection(container: HTMLElement, settings: AgentsSettings): void {
  // Sync from settings
  agents = Array.isArray(settings.agents) ? settings.agents : [];
  const perAgentLLMEnabled = !!settings.perAgentLLMEnabled;
  renderAgentsTable(container);
  // Add button listeners
  const addBtn = document.getElementById('addAgentBtn');
  if (addBtn) addBtn.onclick = () => showAgentModal(container, {}, null, perAgentLLMEnabled);
  // Modal buttons
  const cancelAgentBtn = document.getElementById('cancelAgentBtn');
  if (cancelAgentBtn) cancelAgentBtn.onclick = () => hideAgentModal(container);
  const saveAgentBtn = document.getElementById('saveAgentBtn');
  if (saveAgentBtn) saveAgentBtn.onclick = () => saveAgent(container, settings);
  // Show a hint if per-agent LLM is disabled
  const section = container.querySelector('#agentsSection') as HTMLElement;
  if (section && !perAgentLLMEnabled) {
    const hint = document.createElement('div');
    hint.style.color = '#c2410c';
    hint.style.fontSize = '1em';
    hint.style.margin = '12px 0';
    hint.innerText = 'Per-agent LLM/model configuration is disabled. All agents use the global provider/model.';
    section.prepend(hint);
  }
}

/**
 * Renders the agents table in the settings UI
 * @param container - The HTMLElement where the table should be rendered
 */
function renderAgentsTable(container: HTMLElement): void {
  const section = container.querySelector('#agentsSection') as HTMLElement;
  if (!agents || agents.length === 0) {
    section.innerHTML = '<div style="color:#aaa;font-size:1.1em;padding:24px 0;text-align:center;">🧑‍💻 No agents defined.</div>';
    return;
  }
  let html = `<style>
        .crud-table th, .crud-table td { padding: 6px 10px; }
        .crud-table th {
            background: ${sectionTheme.headerBg};
            color: ${sectionTheme.headerColor};
            font-weight: 600;
        }
        .crud-table tbody tr:nth-child(even) { background: #fafbfc; }
        .crud-table tbody tr:hover { background: #e8f0fe; }
        .agent-badge { display:inline-block; padding:2px 8px; border-radius:8px; font-size:0.9em; margin-right:2px; }
        .badge-enabled { background: #d1fae5; color: #059669; }
        .badge-disabled { background: #fee2e2; color: #b91c1c; }
        .badge-supervisor { background: #fef9c3; color: #b45309; }
        .btn-agent {
            background:${sectionTheme.button.background};
            color:${sectionTheme.button.color};
            border:${sectionTheme.button.border};
            border-radius:${sectionTheme.button.borderRadius};
            padding:3px 10px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s;
        }
        .btn-agent:hover { background:#1d4ed8; }
        .btn-agent[disabled] { background:#e5e7eb; color:#888; cursor:not-allowed; }
        .agent-action-icons { font-size:1.1em; cursor:pointer; margin:0 2px; }
        .agent-action-icons.edit { color:${sectionTheme.accent}; }
        .agent-action-icons.delete { color:#b91c1c; }
        .agent-action-icons.edit:hover { color:#1d4ed8; }
        .agent-action-icons.delete:hover { color:#dc2626; }
    </style>`;
  html += '<table class="crud-table"><thead><tr>' +
        '<th>👤 Name</th><th>📝 Description</th><th title="System Prompt">💬 Prompt</th><th>🔗 Provider</th><th>🤖 Model ID</th><th title="Supervisor Agent">🦸 Supervisor</th><th>⚡ Enabled</th><th>🔗 Chained Agents</th><th>⚙️ Actions</th>' +
        '</tr></thead><tbody>';
  agents.forEach((a, idx) => {
    html += `<tr>
            <td>${a.name || ''}</td>
            <td>${a.description || ''}</td>
            <td>${(a.systemPrompt||'').slice(0,40)}${(a.systemPrompt||'').length>40?'...':''}</td>
            <td>${a.provider||''}</td>
            <td>${a.modelId||''}</td>
            <td>${a.supervisor ? 'Yes' : 'No'}</td>
            <td>${a.enabled ? 'Yes' : 'No'}</td>
            <td>${(a.chainedAgentIds||[]).join(',')}</td>
            <td>
                <button type="button" data-edit="${idx}">Edit</button>
                <button type="button" data-delete="${idx}">Delete</button>
            </td>
        </tr>`;
  });
  html += '</tbody></table>';
  section.innerHTML = html;
  // Attach edit/delete event listeners
  section.querySelectorAll('button[data-edit]').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const idxAttr = (e.target as HTMLElement).getAttribute('data-edit');
      const idx = parseInt(safeGetString(idxAttr));
      showAgentModal(container, agents[idx], idx);
    });
  });
  section.querySelectorAll('button[data-delete]').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const idxAttr = (e.target as HTMLElement).getAttribute('data-delete');
      const idx = parseInt(safeGetString(idxAttr));
      deleteAgent(container, idx);
    });
  });
}

/**
 * Shows the agent modal for adding/editing an agent
 * @param container - The container element
 * @param agent - The agent data to edit, or an empty object for a new agent
 * @param idx - The index of the agent being edited, or null for a new agent
 * @param perAgentLLMEnabled - Whether per-agent LLM configuration is enabled
 */
function showAgentModal(container: HTMLElement, agent: Partial<Agent> = {}, idx: number | null = null, perAgentLLMEnabled = false): void {
  const modal = document.getElementById('agentModal');
  const title = document.getElementById('agentModalTitle');
  const nameInput = document.getElementById('agentName') as HTMLInputElement | null;
  const descInput = document.getElementById('agentDesc') as HTMLInputElement | null;
  const promptInput = document.getElementById('agentPrompt') as HTMLInputElement | null;
  const providerInput = document.getElementById('agentProvider') as HTMLInputElement | null;
  const modelIdInput = document.getElementById('agentModelId') as HTMLInputElement | null;
  const supervisorInput = document.getElementById('agentSupervisor') as HTMLInputElement | null;
  const enabledInput = document.getElementById('agentEnabled') as HTMLInputElement | null;
  const chainedInput = document.getElementById('agentChained') as HTMLInputElement | null;
  if (modal) modal.style.display = 'flex';
  if (title) title.innerText = idx == null ? 'Add Agent' : 'Edit Agent';
  if (nameInput) nameInput.value = agent?.name || '';
  if (descInput) descInput.value = agent?.description || '';
  if (promptInput) promptInput.value = agent?.systemPrompt || '';
  if (providerInput) {
    providerInput.value = agent?.provider || '';
    providerInput.disabled = perAgentLLMEnabled === false;
  }
  if (modelIdInput) {
    modelIdInput.value = safeGetString(agent?.modelId) || '';
    modelIdInput.disabled = perAgentLLMEnabled === false;
  }
  if (supervisorInput) supervisorInput.checked = !!agent?.supervisor;
  if (enabledInput) enabledInput.checked = !!agent?.enabled;
  if (chainedInput) chainedInput.value = (agent?.chainedAgentIds||[]).join(',');
  editingAgentIdx = idx;
}

/**
 * Hides the agent modal
 * @param container - The container element
 */
function hideAgentModal(container: HTMLElement): void {
  const modal = document.getElementById('agentModal');
  if (modal) modal.style.display = 'none';
  // Clear any validation errors in the container
  const errorElements = container.querySelectorAll('.validation-error');
  errorElements.forEach(el => el.remove());
}

/**
 * Saves the agent data from the modal
 * @param container - The container element
 * @param settings - The current settings object
 */
function saveAgent(container: HTMLElement, settings: AgentsSettings): void {
  // Get all input elements with proper type assertions
  const nameInput = container.querySelector<HTMLInputElement>('#agentName');
  const descInput = container.querySelector<HTMLInputElement>('#agentDesc');
  const promptInput = container.querySelector<HTMLInputElement>('#agentPrompt');
  const providerInput = container.querySelector<HTMLInputElement>('#agentProvider');
  const modelIdInput = container.querySelector<HTMLInputElement>('#agentModelId');
  const supervisorInput = container.querySelector<HTMLInputElement>('#agentSupervisor');
  const enabledInput = container.querySelector<HTMLInputElement>('#agentEnabled');
  const chainedInput = container.querySelector<HTMLInputElement>('#agentChained');
  
  // Validate required inputs
  if (!nameInput?.value) {
    showValidationError(container, 'agentName', 'Agent name is required');
    return;
  }
  
  // Create agent object with proper type checking
  const agent: Agent = {
    name: safeGetString(nameInput?.value),
    description: safeGetString(descInput?.value),
    systemPrompt: safeGetString(promptInput?.value),
    provider: safeGetString(providerInput?.value),
    modelId: safeGetString(modelIdInput?.value),
    supervisor: supervisorInput?.checked ?? false,
    enabled: enabledInput?.checked ?? true,
    chainedAgentIds: chainedInput?.value
      .split(',')
      .map(s => s.trim())
      .filter(Boolean) || []
  };
  
  // Update agents array based on edit or add
  if (editingAgentIdx === null) {
    agents.push(agent);
  } else if (editingAgentIdx >= 0 && editingAgentIdx < agents.length) {
    agents[editingAgentIdx] = agent;
  } else {
    console.error('Invalid agent index for editing:', editingAgentIdx);
    return;
  }
  
  // Update settings and UI
  settings.agents = [...agents]; // Create new array to trigger reactivity
  hideAgentModal(container);
  renderAgentsTable(container);
  
  // Show success message
  showStatus(container, `Agent "${agent.name}" ${editingAgentIdx === null ? 'added' : 'updated'} successfully`, 'success');
}

/**
 * Shows a validation error message for a form field
 * @param container - The container element
 * @param fieldId - The ID of the field with the error
 * @param message - The error message to display
 */
function showValidationError(container: HTMLElement, fieldId: string, message: string): void {
  // Remove any existing error for this field
  const existingError = container.querySelector(`#${fieldId}-error`);
  if (existingError) {
    existingError.remove();
  }
  
  // Create and show new error message
  const errorElement = document.createElement('div');
  errorElement.id = `${fieldId}-error`;
  errorElement.className = 'validation-error';
  errorElement.style.color = '#dc3545';
  errorElement.style.fontSize = '0.85em';
  errorElement.style.marginTop = '0.25em';
  errorElement.textContent = message;
  
  // Insert after the field
  const field = container.querySelector(`#${fieldId}`);
  if (field && field.parentNode) {
    field.parentNode.insertBefore(errorElement, field.nextSibling);
  }
  
  // Focus the field
  field?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  (field as HTMLElement)?.focus();
}

/**
 * Shows a status message in the UI
 * @param container - The container element
 * @param message - The message to display
 * @param type - The type of message (success, error, or info)
 */
function showStatus(container: HTMLElement, message: string, type: 'success' | 'error' | 'info' = 'info'): void {
  const statusElement = container.querySelector<HTMLElement>('#status-message') || document.createElement('div');
  statusElement.id = 'status-message';
  statusElement.textContent = message;
  
  // Apply styles based on message type
  const styles: Partial<CSSStyleDeclaration> = {
    display: 'block',
    padding: '0.75em',
    margin: '1em 0',
    borderRadius: '4px',
    backgroundColor: type === 'error' ? '#f8d7da' : 
                   type === 'success' ? '#d4edda' : '#d1ecf1',
    color: type === 'error' ? '#721c24' : 
           type === 'success' ? '#155724' : '#0c5460',
    border: type === 'error' ? '1px solid #f5c6cb' : 
           type === 'success' ? '1px solid #c3e6cb' : '1px solid #bee5eb'
  };
  
  Object.assign(statusElement.style, styles);
  
  // Insert status message if not already in DOM
  if (!statusElement.parentNode) {
    const form = container.querySelector('form');
    form?.insertBefore(statusElement, form.firstChild);
  }
  
  // Auto-hide after 5 seconds
  setTimeout(() => {
    statusElement.style.opacity = '0';
    setTimeout(() => {
      statusElement.style.display = 'none';
      statusElement.style.opacity = '1';
    }, 300);
  }, 5000);
}

import { showModal } from '../components/modal';

/**
 * Deletes an agent after confirmation
 * @param container - The container element
 * @param idx - The index of the agent to delete
 */
function deleteAgent(container: HTMLElement, idx: number): void {
  const agentName = agents[idx]?.name || 'this agent';
  showModal({
    title: 'Delete Agent',
    content: `Are you sure you want to permanently delete the agent "${agentName}"? This action cannot be undone.`,
    onConfirm: () => {
      agents.splice(idx, 1);
      const settings = (window as any).settings || {};
      settings.agents = agents;
      renderAgentsTable(container);
    }
  });
}

/**
 * Safely gets a string value, returning an empty string if null or undefined
 * @param val - The value to check
 * @returns The string value or an empty string
 */
function safeGetString(val: string | null | undefined): string {
  return typeof val === 'string' ? val : '';
}
