{"version": 3, "file": "docsTool.js", "sourceRoot": "", "sources": ["../../src/tools/docsTool.ts"], "names": [], "mappings": ";;;AACA,sCAAmC;AAEnC,kDAA+C;AAE/C,yDAA6I;AAC7I,6BAAwB;AAGxB;;;;GAIG;AACH,MAAa,iBAAiB;IAC5B,iCAAiC;IACxB,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QACzB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qEAAqE,CAAC;QAC7G,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;QACrE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;QAC3E,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8CAA8C,CAAC;QACvF,IAAI,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC;KAC5D,CAAC,CAAC;IAEH,mCAAmC;IACnC,IAAI,WAAW,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAEzC,6BAA6B;IACpB,YAAY,GAAG,OAAC,CAAC,MAAM,CAAC;QAC/B,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;QACpB,MAAM,EAAE,OAAC,CAAC,GAAG,EAAE;QACf,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC5B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAClC,CAAC,CAAC;IAEK,SAAS,GAAc;QAC7B,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,eAAe;QACxB,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;KAC9B,CAAC;IACM,UAAU,CAAM,CAAC,6BAA6B;IAC7C,EAAE,GAAG,MAAM,CAAC;IACZ,IAAI,GAAG,0BAA0B,CAAC;IAClC,WAAW,GAAG,kFAAkF,CAAC;IACjG,IAAI,GAAG,cAAc,CAAC,CAAC,oBAAoB;IAC3C,OAAO,GAAwB;QACtC,QAAQ,EAAE;YACR,GAAG,IAAI,0CAAuB,EAAE;YAChC,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;gBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kCAAkC,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;gBACrG,CAAC;gBACD,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,qCAAqC,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YAC/G,CAAC;SACF;QACD,UAAU,EAAE;YACV,GAAG,IAAI,uCAAoB,EAAE;YAC7B,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;gBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kCAAkC,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;gBACvG,CAAC;gBACD,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,gCAAgC,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;YAC5G,CAAC;SACF;QACD,WAAW,EAAE;YACX,GAAG,IAAI,2CAAwB,EAAE;YACjC,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;gBACxC,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oCAAoC,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;gBAC1G,CAAC;gBACD,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,6BAA6B,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;YAChI,CAAC;SACF;QACD,WAAW,EAAE;YACX,GAAG,IAAI,iDAA8B,EAAE;YACvC,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAW,CAAC;gBAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,iCAAiC,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;gBACvG,CAAC;gBACD,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,6CAA6C,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;YACnH,CAAC;SACF;QACD,KAAK,EAAE;YACL,EAAE,EAAE,KAAK;YACT,IAAI,EAAE,mBAAmB;YACzB,WAAW,EAAE,4CAA4C;YACzD,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;aAC9E,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,iDAAiD,EAAE;iBAC1F;gBACD,QAAQ,EAAE,CAAC,OAAO,CAAC;aACpB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;gBACrF,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;gBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kCAAkC,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;gBAClG,CAAC;gBACD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,KAAK,GAAG,CAAC,CAAC;gBACvE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;gBAC7G,MAAM,iBAAiB,GAAc;oBACnC,QAAQ,EAAE,QAAQ;oBAClB,OAAO,EAAE,eAAe;oBACxB,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;iBAC9B,CAAC;gBACF,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;gBAC1E,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gEAAgE,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;gBAChI,CAAC;gBACD,IAAI,CAAC;oBACH,MAAM,YAAY,GAAG,qWAAqW,CAAC;oBAC3X,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC;wBACrC,MAAM,EAAE,KAAK;wBACb,YAAY,EAAE,YAAY;wBAC1B,OAAO,EAAE,iBAAiB,CAAC,OAAO;wBAClC,OAAO,EAAE,iBAAiB,CAAC,OAAO;wBAClC,IAAI,EAAE,UAAU;qBACjB,CAAC,CAAC;oBACH,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;wBACjB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;oBAC5E,CAAC;oBACD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;gBAC9E,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;gBACxH,CAAC;YACH,CAAC;SACF;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,KAAgB,EAAE,OAAsB;QACpD,oEAAoE;QACpE,MAAM,UAAU,GAAG,KAAK,CAAC,MAA4B,CAAC;QAEtD,qEAAqE;QACrE,IAAI,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAE5C,6CAA6C;YAC7C,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACvB,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACxD,mDAAmD;gBACnD,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;oBACjC,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;gBACjC,CAAC;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,4EAA4E;QAC5E,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;QAEpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kCAAkC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACpG,CAAC;QAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,KAAK,GAAG,CAAC,CAAC;QAEvE,8EAA8E;QAC9E,2CAA2C;QAC3C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;QAE7G,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gEAAgE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAClI,CAAC;QAED,IAAI,CAAC;YACH,MAAM,YAAY,GAAG;;wGAE6E,CAAC;YAEnG,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC;gBACrC,MAAM,EAAE,KAAK;gBACb,YAAY,EAAE,YAAY;gBAC1B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;gBAC/B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;gBAC/B,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YAChH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAChF,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gDAAgD,KAAK,IAAI,EAAE,KAAK,CAAC,CAAC;YACxF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1H,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAyB;QACtC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAChD,CAAC;CACF;AAzMD,8CAyMC;AAEY,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC", "sourcesContent": ["import { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { Logger } from '../logger';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { llmService } from '../llm/llmService';\nimport { LLMConfig } from '../config';\nimport { DocumentationGenTool, DocumentationSearchTool, DocumentationSummaryTool, DocumentationVisualizationTool } from './advancedDocsTool';\nimport { z } from 'zod';\nimport { LLMGenerateParams, LLMGenerateResult } from '../llm/types';\n\n/**\n * A tool for searching documentation or asking general knowledge questions.\n * This is currently implemented as a pass-through to the LLM, but could be extended\n * to use web search or other documentation sources.\n */\nexport class DocumentationTool implements ITool {\n  // Schema definition for the tool\n  readonly schema = z.object({\n    action: z.string().optional().describe('The action to perform (search, generate, summarize, visualize, ask)'),\n    query: z.string().optional().describe('The search query or question'),\n    topic: z.string().optional().describe('Topic for documentation generation'),\n    content: z.string().optional().describe('Content to process (e.g., for summarization)'),\n    data: z.any().optional().describe('Data for visualization')\n  });\n  \n  // Alias for backward compatibility\n  get inputSchema() { return this.schema; }\n  \n  // Output schema for the tool\n  readonly outputSchema = z.object({\n    success: z.boolean(),\n    output: z.any(),\n    error: z.string().optional(),\n    toolId: z.string(),\n    actionName: z.string().optional()\n  });\n  \n  private llmConfig: LLMConfig = {\n    provider: 'openai',\n    modelId: 'gpt-3.5-turbo',\n    options: { temperature: 0.3 }\n  };\n  private llmService: any; // Define llmService property\n  readonly id = 'docs';\n  readonly name = 'Documentation (Advanced)';\n  readonly description = 'Search, generate, summarize, and visualize technical documentation or knowledge.';\n  readonly type = 'multi-action'; // Required by ITool\n  readonly actions: Record<string, any> = {\n    'search': {\n      ...new DocumentationSearchTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const query = input.query as string;\n        if (!query) {\n          return { success: false, error: '\\'query\\' parameter is required.', toolId: 'search', actionName };\n        }\n        // Placeholder implementation\n        return { success: true, output: `Documentation search results for: ${query}`, toolId: 'search', actionName };\n      }\n    },\n    'generate': {\n      ...new DocumentationGenTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const topic = input.topic as string;\n        if (!topic) {\n          return { success: false, error: '\\'topic\\' parameter is required.', toolId: 'generate', actionName };\n        }\n        // Placeholder implementation\n        return { success: true, output: `Generated documentation for: ${topic}`, toolId: 'generate', actionName };\n      }\n    },\n    'summarize': {\n      ...new DocumentationSummaryTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const content = input.content as string;\n        if (!content) {\n          return { success: false, error: '\\'content\\' parameter is required.', toolId: 'summarize', actionName };\n        }\n        // Placeholder implementation\n        return { success: true, output: `Summarized documentation: ${content.substring(0, 50)}...`, toolId: 'summarize', actionName };\n      }\n    },\n    'visualize': {\n      ...new DocumentationVisualizationTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const data = input.data as any;\n        if (!data) {\n          return { success: false, error: '\\'data\\' parameter is required.', toolId: 'visualize', actionName };\n        }\n        // Placeholder implementation\n        return { success: true, output: 'Visualization created for the provided data', toolId: 'visualize', actionName };\n      }\n    },\n    'ask': {\n      id: 'ask',\n      name: 'Ask Documentation',\n      description: 'Ask a documentation or technical question.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        query: z.string().describe('The documentation or technical question to ask.')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          query: { type: 'string', description: 'The documentation or technical question to ask.' }\n        },\n        required: ['query']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n        const query = input.query as string;\n        if (!query) {\n          return { success: false, error: '\\'query\\' parameter is required.', toolId: 'ask', actionName };\n        }\n        Logger.instance.info(`Documentation search requested for: \"${query}\"`);\n        Logger.instance.warn('Using LLM for documentation search. This may not be accurate for recent information.');\n        const researchLLMConfig: LLMConfig = {\n          provider: 'openai',\n          modelId: 'gpt-3.5-turbo',\n          options: { temperature: 0.3 }\n        };\n        const provider = await llmService.getProviderForConfig(researchLLMConfig);\n        if (!provider) {\n          return { success: false, error: 'LLM provider for documentation search not found or configured.', toolId: 'ask', actionName };\n        }\n        try {\n          const systemPrompt = 'You are a documentation researcher. Your task is to answer the following query with accurate, technical information.\\nBe concise but thorough. Include code examples where appropriate. If you don\\'t know the answer, say so instead of making things up.\\nOnly answer what is asked - do not try to provide additional information beyond the scope of the query.';\n          const result = await provider.generate({\n            prompt: query,\n            systemPrompt: systemPrompt,\n            modelId: researchLLMConfig.modelId,\n            options: researchLLMConfig.options,\n            mode: 'generate'\n          });\n          if (result.error) {\n            return { success: false, error: result.error, toolId: 'ask', actionName };\n          }\n          return { success: true, output: result.content, toolId: 'ask', actionName };\n        } catch (error: any) {\n          return { success: false, error: `Documentation search failed: ${error.message || error}`, toolId: 'ask', actionName };\n        }\n      }\n    }\n  };\n\n  async execute(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // For multi-action tools, extract action from input if not provided\n    const actionName = input.action as string | undefined;\n    \n    // If an action is specified, delegate to the appropriate action tool\n    if (actionName && this.actions[actionName]) {\n      const actionTool = this.actions[actionName];\n      \n      // Handle both new and old style action tools\n      if (actionTool.execute) {\n        const result = await actionTool.execute(input, context);\n        // Ensure the action name is included in the result\n        if (result && !result.actionName) {\n          result.actionName = actionName;\n        }\n        return result;\n      }\n    }\n\n    // Default behavior - treat as a documentation search if no action specified\n    const query = input.query as string;\n\n    if (!query) {\n      return { success: false, error: '\\'query\\' parameter is required.', toolId: this.id, actionName };\n    }\n\n    Logger.instance.info(`Documentation search requested for: \"${query}\"`);\n\n    // In a real implementation, we might call a search API or a dedicated service\n    // For now, let's use the LLM as a fallback\n    Logger.instance.warn('Using LLM for documentation search. This may not be accurate for recent information.');\n\n    const provider = await llmService.getProviderForConfig(this.llmConfig);\n    if (!provider) {\n      return { success: false, error: 'LLM provider for documentation search not found or configured.', toolId: this.id, actionName };\n    }\n\n    try {\n      const systemPrompt = `You are a documentation researcher. Your task is to answer the following query with accurate, technical information.\nBe concise but thorough. Include code examples where appropriate. If you don't know the answer, say so instead of making things up.\nOnly answer what is asked - do not try to provide additional information beyond the scope of the query.`;\n\n      const result = await provider.generate({\n        prompt: query,\n        systemPrompt: systemPrompt,\n        modelId: this.llmConfig.modelId,\n        options: this.llmConfig.options,\n        mode: 'generate'\n      });\n\n      if (result.error) {\n        return { success: false, error: `Documentation search failed: ${result.error}`, toolId: this.id, actionName };\n      }\n\n      return { success: true, output: result.content, toolId: this.id, actionName };\n    } catch (error: any) {\n      Logger.instance.error(`Error during documentation search for query \"${query}\":`, error);\n      return { success: false, error: `Documentation search failed: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  async generate(params: LLMGenerateParams): Promise<LLMGenerateResult> {\n    return await this.llmService.generate(params);\n  }\n}\n\nexport const documentationTool = new DocumentationTool();\n"]}