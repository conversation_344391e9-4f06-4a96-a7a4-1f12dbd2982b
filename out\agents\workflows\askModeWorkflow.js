"use strict";
/**
 * Codessa Ask Mode Workflow
 *
 * This module provides workflow templates for Ask Mode:
 * - Question analysis
 * - Context retrieval
 * - Answer generation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAskModeWorkflow = createAskModeWorkflow;
exports.createCodeQuestionWorkflow = createCodeQuestionWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
/**
 * Create an Ask Mode workflow for answering questions with context
 */
function createAskModeWorkflow(id, name, description, qaAgent, tools = []) {
    logger_1.Logger.instance.info(`Creating Ask Mode workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const questionAnalysisNode = graph_1.Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);
    const contextRetrievalNode = graph_1.Codessa.createAgentNode('context-retrieval', 'Context Retrieval', qaAgent);
    const answerGenerationNode = graph_1.Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);
    const answerRefinementNode = graph_1.Codessa.createAgentNode('answer-refinement', 'Answer Refinement', qaAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-question', source: 'input', target: 'question-analysis', type: 'default' },
        { name: 'question-to-context', source: 'question-analysis', target: 'context-retrieval', type: 'default' },
        { name: 'context-to-answer', source: 'context-retrieval', target: 'answer-generation', type: 'default' },
        { name: 'answer-to-refinement', source: 'answer-generation', target: 'answer-refinement', type: 'default' },
        { name: 'refinement-to-output', source: 'answer-refinement', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect context retrieval to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `context-to-tool-${index}`,
                source: 'context-retrieval',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to answer generation
            edges.push({
                name: `tool-${index}-to-answer`,
                source: `tool-${index}`,
                target: 'answer-generation',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            questionAnalysisNode,
            contextRetrievalNode,
            answerGenerationNode,
            answerRefinementNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'ask',
        tags: ['ask', 'question-answering', 'context']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a specialized Code Question workflow for answering code-related questions
 */
function createCodeQuestionWorkflow(id, name, description, qaAgent, tools = []) {
    logger_1.Logger.instance.info(`Creating Code Question workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const questionAnalysisNode = graph_1.Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);
    const codebaseSearchNode = graph_1.Codessa.createAgentNode('codebase-search', 'Codebase Search', qaAgent);
    const codeAnalysisNode = graph_1.Codessa.createAgentNode('code-analysis', 'Code Analysis', qaAgent);
    const documentationSearchNode = graph_1.Codessa.createAgentNode('documentation-search', 'Documentation Search', qaAgent);
    const answerGenerationNode = graph_1.Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);
    const codeExampleGenerationNode = graph_1.Codessa.createAgentNode('code-example-generation', 'Code Example Generation', qaAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-question', source: 'input', target: 'question-analysis', type: 'default' },
        { name: 'question-to-codebase', source: 'question-analysis', target: 'codebase-search', type: 'default' },
        { name: 'question-to-documentation', source: 'question-analysis', target: 'documentation-search', type: 'default' },
        { name: 'codebase-to-analysis', source: 'codebase-search', target: 'code-analysis', type: 'default' },
        { name: 'analysis-to-answer', source: 'code-analysis', target: 'answer-generation', type: 'default' },
        { name: 'documentation-to-answer', source: 'documentation-search', target: 'answer-generation', type: 'default' },
        { name: 'answer-to-example', source: 'answer-generation', target: 'code-example-generation', type: 'default' },
        { name: 'example-to-output', source: 'code-example-generation', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect code analysis to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `analysis-to-tool-${index}`,
                source: 'code-analysis',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to answer generation
            edges.push({
                name: `tool-${index}-to-answer`,
                source: `tool-${index}`,
                target: 'answer-generation',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            questionAnalysisNode,
            codebaseSearchNode,
            codeAnalysisNode,
            documentationSearchNode,
            answerGenerationNode,
            codeExampleGenerationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'ask',
        tags: ['ask', 'code', 'question-answering']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=askModeWorkflow.js.map