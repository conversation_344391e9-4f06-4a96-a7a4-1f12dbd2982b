import { GraphDefinition } from './graphTypes';
import { logger } from '../../logger';
import { goddessPersonalityEngine } from '../../personality/goddessMode';
import { v4 as uuidv4 } from 'uuid';

type WorkflowEnhancementOptions = {
  enableGoddess?: boolean;
  enableQuantum?: boolean;
  enableNeural?: boolean;
  safeMode?: boolean;
};

/**
 * Revolutionary Workflow Registry with Enhanced AI Capabilities
 */
class WorkflowRegistry {
  private workflows: Map<string, GraphDefinition> = new Map();
  private workflowMetrics: Map<string, {
    executionCount: number;
    successRate: number;
    averageDuration: number;
    revolutionaryFeaturesUsage: Record<string, number>;
    userSatisfactionScore: number;
    goddessRating: number;
  }> = new Map();

  private revolutionaryWorkflowTemplates: Map<string, () => GraphDefinition> = new Map();
  private isInitialized: boolean = false;
  private initializationPromise: Promise<void> | null = null;
  private enhancementOptions: WorkflowEnhancementOptions = {
    enableGoddess: true,
    enableQuantum: true,
    enableNeural: true,
    safeMode: false
  };

  constructor(options: Partial<WorkflowEnhancementOptions> = {}) {
    this.enhancementOptions = { ...this.enhancementOptions, ...options };
  }

  /**
   * Initialize the workflow registry with revolutionary features
   */
  async initialize(): Promise<void> {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = (async () => {
      try {
        logger.info('Initializing Workflow Registry with revolutionary features...');

        // Initialize revolutionary workflow templates
        this.initializeRevolutionaryTemplates();

        // Load core workflows with revolutionary enhancements
        await this.loadRevolutionaryWorkflows();

        // Initialize Goddess Mode for workflow guidance if enabled
        if (this.enhancementOptions.enableGoddess && goddessPersonalityEngine) {
          try {
            goddessPersonalityEngine.updatePersonality({
              adaptiveLevel: 95,
              emotionalIntelligence: 90,
              motivationalStyle: 'adaptive',
              wisdomLevel: 95,
              creativityLevel: 85
            });
          } catch (error) {
            logger.warn('Failed to initialize Goddess personality engine:', error);
          }
        }

        this.isInitialized = true;
        logger.info('Workflow Registry initialized successfully with revolutionary features');
      } catch (error) {
        logger.error('Failed to initialize Workflow Registry:', error);
        throw error;
      }
    })();

    return this.initializationPromise;
  }

  /**
     * Instantiate a workflow by ID, with optional initial state/context
     */
  createWorkflowInstance(id: string, initialState: any = {}): { definition: GraphDefinition, state: any } | undefined {
    const definition = this.getWorkflow(id);
    if (!definition) return undefined;
    // Return a new instance with the definition and a fresh state
    return { definition, state: initialState };
  }

  /**
   * Register a workflow in the registry
   * @param workflow The workflow to register
   * @returns The registered workflow
   */
  public registerWorkflow(workflow: GraphDefinition): GraphDefinition {
    if (!workflow?.id) {
      throw new Error('Cannot register workflow: Missing required ID');
    }

    // Enhance workflow with revolutionary capabilities
    const enhancedWorkflow = this.enhanceWorkflowWithRevolutionaryFeatures(workflow);

    // Only set metrics if this is a new workflow
    if (!this.workflows.has(enhancedWorkflow.id)) {
      this.workflowMetrics.set(enhancedWorkflow.id, {
        executionCount: 0,
        successRate: 100,
        averageDuration: 0,
        revolutionaryFeaturesUsage: {
          goddessMode: 0,
          quantumAnalysis: 0,
          neuralSynthesis: 0,
          timeTravelDebugging: 0
        },
        userSatisfactionScore: 85,
        goddessRating: 90
      });

      logger.info(`Registered revolutionary workflow: ${enhancedWorkflow.name} (${enhancedWorkflow.id}) with enhanced AI capabilities`);
    } else {
      logger.debug(`Updating existing workflow: ${enhancedWorkflow.id}`);
    }

    this.workflows.set(enhancedWorkflow.id, enhancedWorkflow);
    return enhancedWorkflow;
  }

  /**
     * Enhance workflow with revolutionary features
     */
  /**
   * Safely enhance a workflow with revolutionary features
   */
  private enhanceWorkflowWithRevolutionaryFeatures(workflow: GraphDefinition): GraphDefinition {
    // Skip enhancement in safe mode or if not initialized
    if (this.enhancementOptions.safeMode || !this.isInitialized) {
      return workflow;
    }

    // Skip if already enhanced
    if ((workflow as any).__enhanced) {
      return workflow;
    }

    const { enableGoddess, enableQuantum, enableNeural } = this.enhancementOptions;

    // Create a safe wrapper for goddess blessings
    const getGoddessBlessing = () => {
      try {
        if (!enableGoddess || !goddessPersonalityEngine?.generateGoddessResponse) {
          return 'Goddess blessings are currently disabled.';
        }

        // Use a type assertion to handle the DeveloperMood type
        const response = goddessPersonalityEngine.generateGoddessResponse?.(
          `Blessing workflow: ${workflow.name}`,
          { /* empty object as we don't need mood */ } as any
        );

        return response?.wisdomSharing || 'May this workflow bring wisdom and efficiency to your coding journey.';
      } catch (error) {
        logger.warn('Failed to generate goddess blessing:', error);
        return 'Blessed with divine inspiration.'; // Fallback message
      }
    };

    const enhanced: any = {
      ...workflow,
      __enhanced: true, // Mark as enhanced to prevent double enhancement
      metadata: {
        ...(workflow as any).metadata || {},
        revolutionaryFeatures: {
          goddessMode: enableGoddess,
          quantumAnalysis: enableQuantum,
          neuralSynthesis: enableNeural,
          timeTravelDebugging: enableQuantum, // Tied to quantum features
          adaptivePersonality: enableGoddess  // Tied to goddess features
        },
        enhancementLevel: 'revolutionary',
        aiCapabilities: [
          enableGoddess && 'emotional_intelligence',
          enableQuantum && 'quantum_pattern_recognition',
          enableNeural && 'neural_code_synthesis',
          enableQuantum && 'future_prediction',
          enableGoddess && 'adaptive_personality'
        ].filter(Boolean) as string[],
        goddessBlessing: getGoddessBlessing()
      }
    };

    // Enhance nodes with revolutionary capabilities
    enhanced.nodes = workflow.nodes.map(node => ({
      ...node,
      revolutionaryEnhancements: {
        goddessGuidance: true,
        quantumAnalysis: node.type === 'agent' || node.type === 'tool',
        neuralInsights: true,
        timeTravelPredictions: node.type === 'agent'
      }
    }));

    return enhanced;
  }

  /**
     * Get a workflow by ID
     */
  getWorkflow(id: string): GraphDefinition | undefined {
    return this.workflows.get(id);
  }

  /**
     * Get all registered workflows
     */
  getAllWorkflows(): GraphDefinition[] {
    return Array.from(this.workflows.values());
  }

  /**
     * Get workflows by tag
     */
  getWorkflowsByTag(tag: string): GraphDefinition[] {
    return this.getAllWorkflows().filter(workflow =>
      workflow.tags?.includes(tag)
    );
  }

  /**
     * Get workflows by methodology
     */
  getWorkflowsByMethodology(methodology: string): GraphDefinition[] {
    return this.getAllWorkflows().filter(workflow =>
      workflow.methodology === methodology
    );
  }

  /**
   * Load all built-in revolutionary workflows from their respective modules
   */
  /**
   * Load and register all revolutionary workflows
   * @returns The number of workflows successfully loaded
   */
  private async loadRevolutionaryWorkflows(): Promise<number> {
    logger.info('Loading and registering revolutionary workflows...');

    try {
      // Import all workflow creation functions with error handling
      const modulePromises = [
        { name: 'templates', import: import('./templates') },
        { name: 'advancedTemplates', import: import('./advancedTemplates') },
        { name: 'specializedTemplates', import: import('./specializedTemplates') },
        { name: 'sdlcWorkflows', import: import('./sdlcWorkflows') },
        { name: 'prWorkflows', import: import('./prWorkflows') }
      ];

      // Process each module in sequence for better error isolation
      for (const { name, import: importPromise } of modulePromises) {
        try {
          const module = await importPromise;
          this.registerWorkflowsFromModule(module, name);
        } catch (error) {
          logger.error(`Failed to load workflow module ${name}:`, error);
        }
      }
    } catch (error) {
      logger.error('Unexpected error loading workflows:', error);
      throw error;
    }

    logger.info(`Finished loading workflows. Total registered: ${this.workflows.size}`);
    return this.workflows.size;
  }

  /**
   * Register all workflows from a module
   */
  private registerWorkflowsFromModule(module: any, moduleName: string = 'unknown'): void {
    if (!module || typeof module !== 'object') {
      logger.warn(`Skipping invalid workflow module: ${moduleName}`);
      return;
    }

    for (const [key, value] of Object.entries(module)) {
      try {
        if (typeof value === 'function' && key.startsWith('create')) {
          // Only call factory functions that expect no arguments
          if (value.length === 0) {
            logger.info(`[${moduleName}] Creating workflow from ${key}...`);
            const workflow = value();
            if (workflow?.id) {
              this.registerWorkflow(workflow);
              logger.info(`[${moduleName}] Successfully registered workflow: ${workflow.id}`);
            } else {
              logger.warn(`[${moduleName}] Invalid workflow from ${key}: Missing ID`);
            }
          } else {
            logger.info(`[${moduleName}] Skipping parameterized workflow factory: ${key}`);
          }
        }
      } catch (error) {
        logger.error(`[${moduleName}] Failed to process workflow factory ${key}:`, error);
      }
    }
  }

  /**
   * Check if the registry is ready
   */
  get isReady(): boolean {
    return this.isInitialized;
  }

  /**
     * Initialize revolutionary workflow templates
     */
  private initializeRevolutionaryTemplates(): void {
    // Goddess-guided workflow template
    this.revolutionaryWorkflowTemplates.set('goddess-guided', () => ({
      id: 'goddess-guided-workflow',
      name: 'Goddess-Guided Development',
      description: 'A workflow enhanced with divine coding wisdom and emotional intelligence',
      version: '1.0.0',
      operationMode: 'agentic' as any,
      nodes: [],
      edges: [],
      startNodeId: 'goddess-blessing',
      metadata: {
        revolutionaryFeatures: {
          goddessMode: true,
          quantumAnalysis: true,
          neuralSynthesis: true,
          timeTravelDebugging: true,
          adaptivePersonality: true
        }
      }
    }));

    // Quantum-enhanced workflow template
    this.revolutionaryWorkflowTemplates.set('quantum-enhanced', () => ({
      id: 'quantum-enhanced-workflow',
      name: 'Quantum Code Analysis Workflow',
      description: 'Leverages quantum-inspired algorithms for superior code analysis',
      version: '1.0.0',
      operationMode: 'research' as any,
      nodes: [],
      edges: [],
      startNodeId: 'quantum-initialization',
      metadata: {
        revolutionaryFeatures: {
          quantumAnalysis: true,
          parallelUniverseTesting: true,
          quantumEntanglement: true
        }
      }
    }));

    // Neural synthesis workflow template
    this.revolutionaryWorkflowTemplates.set('neural-synthesis', () => ({
      id: 'neural-synthesis-workflow',
      name: 'Neural Code Synthesis Workflow',
      description: 'Brain-inspired code generation and optimization',
      version: '1.0.0',
      operationMode: 'codegen' as any,
      nodes: [],
      edges: [],
      startNodeId: 'neural-activation',
      metadata: {
        revolutionaryFeatures: {
          neuralSynthesis: true,
          consciousnessAnalysis: true,
          synapticLearning: true
        }
      }
    }));
  }



  /**
     * Get workflows with advanced features
     */
  getAdvancedWorkflows(): GraphDefinition[] {
    return this.getAllWorkflows().filter(workflow => {
      const metadata = (workflow as any).metadata;
      return metadata?.advancedFeatures &&
        Object.values(metadata.advancedFeatures).some(enabled => enabled);
    });
  }

  /**
     * Get workflows with revolutionary features (legacy method)
     */
  getRevolutionaryWorkflows(): GraphDefinition[] {
    return this.getAdvancedWorkflows();
  }

  /**
     * Get workflow metrics
     */
  getWorkflowMetrics(workflowId: string): any {
    return this.workflowMetrics.get(workflowId);
  }

  /**
     * Update workflow metrics after execution
     */
  updateWorkflowMetrics(workflowId: string, executionData: {
    success: boolean;
    duration: number;
    revolutionaryFeaturesUsed: string[];
    userSatisfaction?: number;
  }): void {
    const metrics = this.workflowMetrics.get(workflowId);
    if (!metrics) return;

    metrics.executionCount++;
    metrics.averageDuration = (metrics.averageDuration * (metrics.executionCount - 1) + executionData.duration) / metrics.executionCount;
    metrics.successRate = ((metrics.successRate * (metrics.executionCount - 1)) + (executionData.success ? 100 : 0)) / metrics.executionCount;

    // Update revolutionary features usage
    executionData.revolutionaryFeaturesUsed.forEach(feature => {
      metrics.revolutionaryFeaturesUsage[feature] = (metrics.revolutionaryFeaturesUsage[feature] || 0) + 1;
    });

    if (executionData.userSatisfaction !== undefined) {
      metrics.userSatisfactionScore = (metrics.userSatisfactionScore + executionData.userSatisfaction) / 2;
    }

    // Update goddess rating based on revolutionary features usage
    const revolutionaryUsage = Object.values(metrics.revolutionaryFeaturesUsage).reduce((sum, count) => sum + count, 0);
    metrics.goddessRating = Math.min(100, 80 + (revolutionaryUsage / metrics.executionCount) * 20);

    this.workflowMetrics.set(workflowId, metrics);
  }

  /**
     * Get recommended workflows based on context
     */
  getRecommendedWorkflows(context: {
    operationMode?: string;
    complexity?: 'low' | 'medium' | 'high';
    advancedFeaturesPreferred?: string[];
  }): GraphDefinition[] {
    const allWorkflows = this.getAllWorkflows();

    return allWorkflows
      .filter(workflow => {
        // Filter by operation mode
        if (context.operationMode && workflow.operationMode !== context.operationMode) {
          return false;
        }

        // Filter by advanced features
        if (context.advancedFeaturesPreferred?.length) {
          const metadata = (workflow as any).metadata;
          const workflowFeatures = metadata?.advancedFeatures || metadata?.revolutionaryFeatures || {};
          const hasPreferredFeatures = context.advancedFeaturesPreferred.some(
            feature => workflowFeatures[feature]
          );
          if (!hasPreferredFeatures) return false;
        }

        return true;
      })
      .sort((a, b) => {
        // Sort by goddess rating and success rate
        const metricsA = this.workflowMetrics.get(a.id);
        const metricsB = this.workflowMetrics.get(b.id);

        const scoreA = (metricsA?.goddessRating || 80) + (metricsA?.successRate || 80);
        const scoreB = (metricsB?.goddessRating || 80) + (metricsB?.successRate || 80);

        return scoreB - scoreA;
      })
      .slice(0, 5); // Return top 5 recommendations
  }
}

// Export a singleton instance
export const workflowRegistry = new WorkflowRegistry();