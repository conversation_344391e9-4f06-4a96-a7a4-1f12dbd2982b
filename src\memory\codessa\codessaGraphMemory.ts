import { logger } from '../../logger';
import {
  StateGraph,
  ChatPromptTemplate,
  MessagesPlaceholder,
  HumanMessage,
  AIMessage,
  SystemMessage
} from '../../agents/workflows/corePolyfill';
import type {
  BaseChatModel
} from '../../agents/workflows/corePolyfill';
import { llmService } from '../../llm/llmService';
import type { CodessaMemoryProvider } from './codessaMemory';
import type { MemoryEntry, MemorySource, MemoryType } from '../types';

/**
 * Codessa memory state
 */
interface MemoryState {
  messages: (HumanMessage | AIMessage | SystemMessage)[];
  context: string[];
  memories: MemoryEntry[];
  currentTask: string;
  nextAction: string;
}

/**
 * Codessa Memory
 * Implements memory workflows using Codessa
 */
export class CodessaGraphMemory {
  private graph: StateGraph | undefined;
  private compiledGraph: unknown;
  private model: BaseChatModel | undefined;
  private initialized = false;
  private memoryProvider: CodessaMemoryProvider;

  constructor(memoryProvider: CodessaMemoryProvider) {
    this.memoryProvider = memoryProvider;
  }

  /**
     * Initialize Codessa memory
     */
  public async initialize(): Promise<void> {
    try {
      // Get model from LLM service
      const provider = await llmService.getDefaultProvider();

      if (!provider) {
        throw new Error('No default provider available');
      }

      // Create model adapter
      this.model = {
        invoke: async (messages: (HumanMessage | AIMessage | SystemMessage)[], options?: { temperature?: number; maxTokens?: number; stop?: string[] }) => {
          try {
            const result = await provider.generate({
              prompt: messages.map((m: HumanMessage | AIMessage | SystemMessage) => {
                if (m.type) {
                  if (m.type === 'human') {
                    return `User: ${m.content}`;
                  } else if (m.type === 'ai') {
                    return `Assistant: ${m.content}`;
                  } else if (m.type === 'system') {
                    return `System: ${m.content}`;
                  }
                }
                return `User: ${String(m.content)}`;
              }).join('\n'),
              modelId: 'default',
              options: {
                temperature: options?.temperature ?? 0.7,
                maxTokens: options?.maxTokens,
                stopSequences: options?.stop
              },
              mode: 'chat'
            });

            return new AIMessage(result.content ?? '');
          } catch (error) {
            logger.error('Error invoking model:', error);
            throw error;
          }
        },
        // Add required methods to satisfy BaseChatModel interface
        _llmType: () => 'custom',
        _modelType: () => 'chat'
      } as unknown as BaseChatModel;

      // Create memory graph
      this.graph = this.createMemoryGraph();

      this.initialized = true;
      logger.info('Codessa memory initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Codessa memory:', error);
      throw error;
    }
  }

  /**
     * Create memory graph
     */
  private createMemoryGraph(): StateGraph {
    // Create state graph
    const graph = new StateGraph({
      channels: {
        messages: {
          value: [],
          default: (): (HumanMessage | AIMessage | SystemMessage)[] => []
        },
        context: {
          value: [],
          default: (): string[] => []
        },
        memories: {
          value: [],
          default: (): MemoryEntry[] => []
        },
        currentTask: {
          value: '',
          default: (): string => ''
        },
        nextAction: {
          value: '',
          default: (): string => ''
        }
      }
    });

    // Add nodes

    // 1. Retrieve relevant memories
    graph.addNode('retrieveMemories', async (state: MemoryState): Promise<MemoryState> => {
      try {
        // Get last message
        const lastMessage = state.messages[state.messages.length - 1];

        if (!lastMessage || (lastMessage as { type?: string }).type !== 'human') {
          return { ...state };
        }

        // Search for relevant memories
        const query = (lastMessage as HumanMessage).content;
        const memories = await this.memoryProvider.searchSimilarMemories(query);

        // Extract context from memories
        const context = memories.map(memory => memory.content);

        return {
          ...state,
          context,
          memories
        };
      } catch (error) {
        logger.error('Error retrieving memories:', error);
        return { ...state };
      }
    });

    // 2. Determine next action
    graph.addNode('determineNextAction', async (state: MemoryState): Promise<MemoryState> => {
      try {
        if (!this.model) {
          return { ...state, nextAction: 'respond' };
        }

        // Create prompt
        const prompt = ChatPromptTemplate.fromMessages([
          new SystemMessage(
            'You are a memory management assistant. Based on the conversation history and the user\'s latest message, ' +
            'determine what action to take next. Options are:\n' +
            '- "respond": Generate a normal response\n' +
            '- "store": Store important information from the conversation\n' +
            '- "retrieve": Retrieve more specific information from memory\n' +
            '- "summarize": Summarize the conversation so far\n' +
            'Respond with just one of these action names.'
          ),
          new MessagesPlaceholder('messages'),
          new HumanMessage('What action should I take next?')
        ]);

        // Invoke model
        const chain = prompt.pipe(this.model) as { invoke: (input: { messages: (HumanMessage | AIMessage | SystemMessage)[] }) => Promise<{ content: string }> };
        const result = await chain.invoke({
          messages: state.messages
        });

        // Extract action
        const action = String(result.content).toLowerCase().trim();

        // Validate action
        const validActions = ['respond', 'store', 'retrieve', 'summarize'];
        const nextAction = validActions.includes(action) ? action : 'respond';

        return {
          ...state,
          nextAction
        };
      } catch (error) {
        logger.error('Error determining next action:', error);
        return { ...state, nextAction: 'respond' };
      }
    });

    // 3. Store memory
    graph.addNode('storeMemory', async (state: MemoryState): Promise<MemoryState> => {
      try {
        // Get last message pair (human and AI)
        const messages = state.messages;

        if (messages.length < 2) {
          return { ...state };
        }

        const lastHumanIndex = messages.map((m: HumanMessage | AIMessage | SystemMessage) => (m as { type: string }).type).lastIndexOf('human');
        const lastAIIndex = messages.map((m: HumanMessage | AIMessage | SystemMessage) => (m as { type: string }).type).lastIndexOf('ai');

        if (lastHumanIndex === -1 || lastAIIndex === -1) {
          return { ...state };
        }

        const humanMessage = messages[lastHumanIndex];
        const aiMessage = messages[lastAIIndex];

        // Create memory entry
        const memoryEntry: Omit<MemoryEntry, 'id' | 'timestamp'> = {
          content: `User: ${(humanMessage as HumanMessage).content}\nAssistant: ${(aiMessage as AIMessage).content}`,
          metadata: {
            source: 'conversation' as MemorySource,
            type: 'conversation' as MemoryType,
            tags: ['conversation']
          }
        };

        // Add to memory
        await this.memoryProvider.addMemory(memoryEntry);

        return { ...state };
      } catch (error) {
        logger.error('Error storing memory:', error);
        return { ...state };
      }
    });

    // Define edges
    graph.addEdge('start', 'retrieveMemories');
    graph.addEdge('retrieveMemories', 'determineNextAction');
    graph.addEdge('determineNextAction', 'storeMemory');
    graph.addEdge('determineNextAction', 'end');
    graph.addEdge('storeMemory', 'end');

    return graph;
  }

  /**
     * Compile the memory graph
     */
  public async compile(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.graph) {
      throw new Error('Memory graph not initialized');
    }

    this.compiledGraph = this.graph.compile();
  }

  /**
     * Process a new message
     * @param message The message to process
     * @returns The AI response as a string
     */
  public async processMessage(message: string | HumanMessage | AIMessage | SystemMessage): Promise<string> {
    if (!this.compiledGraph) {
      await this.compile();
    }

    if (!this.compiledGraph) {
      throw new Error('Memory graph not compiled');
    }

    // Convert string message to HumanMessage if needed
    const humanMessage = typeof message === 'string'
      ? { type: 'human', content: message } as HumanMessage
      : message;

    // Create initial state
    const initialState: MemoryState = {
      messages: [humanMessage],
      context: [],
      memories: [],
      currentTask: '',
      nextAction: ''
    };

    // Run the graph
    const result = await (this.compiledGraph as { invoke: (state: MemoryState) => Promise<MemoryState> }).invoke(initialState);

    // Extract AI response from the result
    const aiMessages = result.messages.filter((m: HumanMessage | AIMessage | SystemMessage) => (m as { type: string }).type === 'ai');

    if (aiMessages.length === 0) {
      return 'I don\'t have a response at this time.';
    }

    const lastAIMessage = aiMessages[aiMessages.length - 1] as AIMessage;
    return typeof lastAIMessage.content === 'string'
      ? lastAIMessage.content
      : String(lastAIMessage.content);
  }
}