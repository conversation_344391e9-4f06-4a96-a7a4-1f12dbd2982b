{"version": 3, "file": "quantumAnalysisTool.js", "sourceRoot": "", "sources": ["../../src/tools/quantumAnalysisTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,sCAAmC;AA2EnC;;GAEG;AACH,MAAa,mBAAmB;IACrB,EAAE,GAAG,kBAAkB,CAAC;IACxB,IAAI,GAAG,uBAAuB,CAAC;IAC/B,WAAW,GAAG,gGAAgG,CAAC;IAC/G,QAAQ,GAAG,UAAU,CAAC;IACtB,IAAI,GAAG,cAAuB,CAAC;IAExC,8CAA8C;IACtC,aAAa,GAA8B,IAAI,GAAG,EAAE,CAAC;IACrD,kBAAkB,GAAoC,IAAI,GAAG,EAAE,CAAC;IAChE,iBAAiB,GAAwC,IAAI,GAAG,EAAE,CAAC;IAE3E;;SAEK;IACL,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,UAAU,IAAI,KAAK,CAAC,MAAgB,CAAC;YACpD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,MAAM,EAAE,CAAC,CAAC;YAE9D,IAAI,MAAW,CAAC;YAEhB,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,6BAA6B;oBAChC,MAAM,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,IAAc,EAAE,KAAK,CAAC,IAAc,CAAC,CAAC;oBACjG,MAAM;gBAER,KAAK,2BAA2B;oBAC9B,MAAM,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,IAAc,EAAE,KAAK,CAAC,aAAyB,CAAC,CAAC;oBAC1G,MAAM;gBAER,KAAK,mBAAmB;oBACtB,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,IAAc,EAAE,KAAK,CAAC,cAAwB,CAAC,CAAC;oBAClG,MAAM;gBAER,KAAK,uBAAuB;oBAC1B,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,IAAc,CAAC,CAAC;oBAClE,MAAM;gBAER,KAAK,wBAAwB;oBAC3B,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAc,CAAC,CAAC;oBAC/D,MAAM;gBAER;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,oCAAoC,MAAM,EAAE;wBACnD,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU,EAAE,MAAM;qBACnB,CAAC;YACN,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,MAAM;aACnB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,UAAU;aACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gCAAgC,CAAC,IAAa,EAAE,IAAa;QAMzE,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtE,MAAM,WAAW,GAAG,IAAI,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAEjD,wDAAwD;YACxD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;YAE7E,0DAA0D;YAC1D,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC;YAExE,oDAAoD;YACpD,MAAM,gBAAgB,GAAG,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAC;YAE7E,wCAAwC;YACxC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE;gBAClC,aAAa,EAAE,KAAK;gBACpB,SAAS,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC;gBACtC,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC;gBAC9D,cAAc,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACzC,KAAK,EAAE,CAAC,CAAC,IAAI;oBACb,WAAW,EAAE,CAAC,CAAC,UAAU;oBACzB,OAAO,EAAE,CAAC,CAAC,IAAI;iBAChB,CAAC,CAAC;aACJ,CAAC,CAAC;YAEH,+CAA+C;YAC/C,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtF,qCAAqC;YACrC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;YAEhG,OAAO;gBACL,QAAQ,EAAE,gBAAgB;gBAC1B,iBAAiB;gBACjB,UAAU,EAAE,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC;gBAC7D,gBAAgB;aACjB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,uCAAuC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,8BAA8B,CAAC,IAAY,EAAE,SAAoB;QAS7E,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,SAAS,IAAI,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;YAC3E,MAAM,iBAAiB,GAA2B,EAAE,CAAC;YAErD,mDAAmD;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;gBAC5F,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,CAAC;YAED,mDAAmD;YACnD,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;YAE9E,yDAAyD;YACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;YAEzE,OAAO;gBACL,SAAS,EAAE,iBAAiB;gBAC5B,mBAAmB;gBACnB,gBAAgB;aACjB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,qCAAqC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACjH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB,CAAC,IAAY,EAAE,cAAsB;QACxE,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAEnE,oEAAoE;YACpE,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAEvF,oDAAoD;YACpD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAEzE,mDAAmD;YACnD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAEpF,OAAO;gBACL,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACzC,YAAY;gBACZ,qBAAqB;gBACrB,aAAa;gBACb,iBAAiB;aAClB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB,CAAC,IAAY;QAahD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,aAAa,GAAuB,EAAE,CAAC;YAE7C,uCAAuC;YACvC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEtD,KAAK,MAAM,SAAS,IAAI,cAAc,EAAE,CAAC;gBACvC,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;oBACvB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;oBACrE,IAAI,YAAY,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC,CAAC,2BAA2B;wBAC5D,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAEjD,+BAA+B;YAC/B,MAAM,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;YAE3E,qCAAqC;YACrC,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAE9D,OAAO;gBACL,aAAa;gBACb,mBAAmB;gBACnB,aAAa;aACd,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAClH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,IAAY;QAU7C,IAAI,CAAC;YACH,mDAAmD;YACnD,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAE/D,iEAAiE;YACjE,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAEvD,0DAA0D;YAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;YAEvE,gCAAgC;YAChC,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,CAAC;YAEjF,OAAO;gBACL,mBAAmB;gBACnB,cAAc;gBACd,cAAc;gBACd,kBAAkB;aACnB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAED,wCAAwC;IAChC,KAAK,CAAC,QAAQ,CAAC,IAAY;QACjC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC/D,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,IAAY;QACnD,wDAAwD;QACxD,MAAM,QAAQ,GAAqB,EAAE,CAAC;QAEtC,8BAA8B;QAC9B,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC;QAEvD,8BAA8B;QAC9B,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC;QAEvD,+BAA+B;QAC/B,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC;QAExD,4BAA4B;QAC5B,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;QAErD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,wBAAwB,CAAC,QAA0B;QACzD,0DAA0D;QAC1D,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9B,GAAG,OAAO;YACV,UAAU,EAAE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,kCAAkC,CAAC,OAAO,CAAC;SAClF,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,4BAA4B,CAAC,QAA0B;QAC7D,mEAAmE;QACnE,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;IAC9D,CAAC;IAEO,wBAAwB,CAAC,OAAuB;QACtD,oDAAoD;QACpD,OAAO,MAAM,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAC7E,CAAC;IAEO,0BAA0B,CAAC,QAA0B;QAC3D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACpC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC9E,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,KAAa,EAAE,QAA0B;QAChF,yCAAyC;QACzC,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9B,OAAO,EAAE,OAAO,CAAC,IAAI;YACrB,gBAAgB,EAAE,qBAAqB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC,yBAAyB;SACrG,CAAC,CAAC,CAAC;IACN,CAAC;IAED,uDAAuD;IAC/C,4BAA4B,CAAC,KAAa;QAChD,OAAO,CAAC,kBAAkB,EAAE,YAAY,EAAE,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;IACtF,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAa,EAAE,QAAgB,EAAE,UAAkB;QACtF,OAAO;YACL,UAAU;YACV,QAAQ;YACR,cAAc,EAAE;gBACd,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;gBAC3D,EAAE,SAAS,EAAE,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;aACjE;YACD,QAAQ,EAAE;gBACR,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE;gBACnD,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE;aACrD;YACD,mBAAmB,EAAE,KAAK;SAC3B,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,SAAiC;QACjE,gDAAgD;QAChD,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,uBAAuB;IACtD,CAAC;IAEO,uBAAuB,CAAC,SAAiC;QAK/D,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAChC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC;YACnD,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,SAAS;SACpD,CAAC,CAAC,CAAC;IACN,CAAC;IAED,yDAAyD;IACjD,kBAAkB,CAAC,KAAa,EAAE,eAAuB;QAC/D,OAAO;YACL,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,GAAG;YAChB,cAAc,EAAE,EAAE;SACnB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAa,EAAE,eAAuB;QAK1E,OAAO;YACL;gBACE,aAAa,EAAE,wBAAwB;gBACvC,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,CAAC,wBAAwB,EAAE,wBAAwB,CAAC;aACtE;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,eAAuB;QAKpE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAa,EAAE,eAAuB;QAC3E,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,aAAa,CAAC,cAAsB;QAC1C,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,6BAA6B;QAC7B,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,aAAa;IAChD,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAa,EAAE,KAAa;QAC5D,OAAO;YACL,KAAK;YACL,KAAK;YACL,gBAAgB,EAAE,YAAY;YAC9B,QAAQ,EAAE,GAAG;YACb,kBAAkB,EAAE,GAAG;YACvB,oBAAoB,EAAE,GAAG;YACzB,cAAc,EAAE,KAAK;SACtB,CAAC;IACJ,CAAC;IAEO,0BAA0B,CAAC,aAAiC;QAKlE,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC7B,WAAW,EAAE,GAAG,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,KAAK,EAAE;YACxC,QAAQ,EAAE,CAAC,CAAC,kBAAkB;YAC9B,IAAI,EAAE,CAAC,CAAC,gBAAgB;SACzB,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,mBAAmB,CAAC,aAAiC;QAK3D,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtE,MAAM,EAAE,aAAa,CAAC,CAAC,KAAK,EAAE;YAC9B,QAAQ,EAAE,CAAC,CAAC,oBAAoB;YAChC,MAAM,EAAE,qBAAqB,CAAC,CAAC,KAAK,EAAE;SACvC,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,uBAAuB,CAAC,IAAY;QAK1C,OAAO;YACL,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE;YAC5E,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE;YAC9E,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,mBAAmB,EAAE,cAAc,CAAC,EAAE;SAC9F,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,IAAY;QACvC,oDAAoD;QACpD,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACnE,CAAC;IAEO,qBAAqB,CAAC,MAA2E;QACvG,sCAAsC;QACtC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CACpC,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CACtD,CAAC,KAAK,CAAC;IACV,CAAC;IAEO,2BAA2B,CAAC,MAA2E;QAC7G,8CAA8C;QAC9C,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CACtC,OAAO,GAAG,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,EAAE,CAAC,CACvE,CAAC;IACJ,CAAC;IAED,2BAA2B;IACnB,yBAAyB,CAAC,IAAY;QAC5C,OAAO,CAAC;gBACN,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE,GAAG;gBACf,gBAAgB,EAAE,eAAe;gBACjC,sBAAsB,EAAE,EAAE;gBAC1B,iBAAiB,EAAE,EAAE;aACtB,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,IAAY;QAC5C,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,0BAA0B,CAAC,IAAY;QAC7C,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,uBAAuB,CAAC,IAAY;QAC1C,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,kCAAkC,CAAC,OAAuB;QAChE,+CAA+C;QAC/C,OAAO,GAAG,CAAC,CAAC,kCAAkC;IAChD,CAAC;CACF;AA/gBD,kDA+gBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { Logger } from '../logger';\n\n/**\n * Quantum Code Analysis Tool - Revolutionary code analysis using quantum-inspired algorithms\n * Provides superior analysis through quantum pattern recognition and parallel universe testing\n */\n\nexport interface QuantumState {\n  superposition: boolean;\n  entangled: boolean;\n  collapsed: boolean;\n  probability: number;\n  parallelStates: Array<{\n    state: string;\n    probability: number;\n    outcome: string;\n  }>;\n}\n\nexport interface QuantumPattern {\n  id: string;\n  name: string;\n  type: 'structural' | 'behavioral' | 'performance' | 'security';\n  confidence: number;\n  quantumSignature: string;\n  parallelManifestations: Array<{\n    universe: string;\n    manifestation: string;\n    probability: number;\n  }>;\n  entangledElements: string[];\n}\n\nexport interface ParallelUniverseTest {\n  universeId: string;\n  scenario: string;\n  testConditions: Array<{\n    condition: string;\n    value: any;\n    probability: number;\n  }>;\n  outcomes: Array<{\n    outcome: string;\n    probability: number;\n    impact: number;\n  }>;\n  quantumInterference: boolean;\n}\n\nexport interface QuantumDebugging {\n  bugId: string;\n  quantumState: QuantumState;\n  superpositionAnalysis: Array<{\n    possibleCause: string;\n    probability: number;\n    quantumEvidence: string[];\n  }>;\n  entangledBugs: Array<{\n    relatedBug: string;\n    entanglementStrength: number;\n    causality: 'forward' | 'backward' | 'bidirectional';\n  }>;\n  parallelDebugging: ParallelUniverseTest[];\n}\n\nexport interface CodeEntanglement {\n  file1: string;\n  file2: string;\n  entanglementType: 'functional' | 'data' | 'temporal' | 'semantic';\n  strength: number; // 0-1, quantum entanglement strength\n  quantumCorrelation: number;\n  spookyActionDistance: number; // How far apart the entangled code can be\n  observerEffect: boolean; // Whether observing one affects the other\n}\n\n/**\n * Quantum Code Analysis Tool Implementation\n */\nexport class QuantumAnalysisTool implements ITool {\n  readonly id = 'quantum_analysis';\n  readonly name = 'Quantum Code Analysis';\n  readonly description = 'Revolutionary code analysis using quantum-inspired algorithms for superior pattern recognition';\n  readonly category = 'analysis';\n  readonly type = 'multi-action' as const;\n\n  // Quantum state storage for advanced analysis\n  private quantumStates: Map<string, QuantumState> = new Map();\n  private entanglementMatrix: Map<string, CodeEntanglement[]> = new Map();\n  private parallelUniverses: Map<string, ParallelUniverseTest[]> = new Map();\n\n  /**\n     * Execute quantum analysis\n     */\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      const action = actionName || input.action as string;\n      Logger.instance.info(`Executing quantum analysis: ${action}`);\n\n      let result: any;\n\n      switch (action) {\n        case 'quantum_pattern_recognition':\n          result = await this.performQuantumPatternRecognition(input.code as string, input.file as string);\n          break;\n\n        case 'parallel_universe_testing':\n          result = await this.performParallelUniverseTesting(input.code as string, input.testScenarios as string[]);\n          break;\n\n        case 'quantum_debugging':\n          result = await this.performQuantumDebugging(input.code as string, input.bugDescription as string);\n          break;\n\n        case 'entanglement_analysis':\n          result = await this.analyzeCodeEntanglement(input.file as string);\n          break;\n\n        case 'superposition_analysis':\n          result = await this.analyzeSuperposition(input.code as string);\n          break;\n\n        default:\n          return {\n            success: false,\n            error: `Unknown quantum analysis action: ${action}`,\n            toolId: this.id,\n            actionName: action\n          };\n      }\n\n      return {\n        success: true,\n        output: result,\n        toolId: this.id,\n        actionName: action\n      };\n\n    } catch (error) {\n      Logger.instance.error('Error in quantum analysis:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : String(error),\n        toolId: this.id,\n        actionName: actionName\n      };\n    }\n  }\n\n  /**\n     * Quantum Pattern Recognition - Identify complex patterns using quantum algorithms\n     */\n  private async performQuantumPatternRecognition(code?: string, file?: string): Promise<{\n    patterns: QuantumPattern[];\n    quantumSignatures: string[];\n    confidence: number;\n    parallelAnalysis: any[];\n  }> {\n    try {\n      const codeToAnalyze = code || (file ? await this.readFile(file) : '');\n      const analysisKey = file || `code_${Date.now()}`;\n\n      // Create quantum superposition of all possible patterns\n      const quantumPatterns = await this.createPatternSuperposition(codeToAnalyze);\n\n      // Apply quantum interference to enhance pattern detection\n      const enhancedPatterns = this.applyQuantumInterference(quantumPatterns);\n\n      // Collapse the superposition to observable patterns\n      const observedPatterns = this.collapsePatternSuperposition(enhancedPatterns);\n\n      // Store quantum state for this analysis\n      this.quantumStates.set(analysisKey, {\n        superposition: false,\n        entangled: observedPatterns.length > 1,\n        collapsed: true,\n        probability: this.calculateQuantumConfidence(observedPatterns),\n        parallelStates: observedPatterns.map(p => ({\n          state: p.name,\n          probability: p.confidence,\n          outcome: p.type\n        }))\n      });\n\n      // Generate quantum signatures for each pattern\n      const quantumSignatures = observedPatterns.map(p => this.generateQuantumSignature(p));\n\n      // Perform parallel universe analysis\n      const parallelAnalysis = await this.analyzeInParallelUniverses(codeToAnalyze, observedPatterns);\n\n      return {\n        patterns: observedPatterns,\n        quantumSignatures,\n        confidence: this.calculateQuantumConfidence(observedPatterns),\n        parallelAnalysis\n      };\n\n    } catch (error) {\n      Logger.instance.error('Quantum pattern recognition failed:', error);\n      throw new Error(`Quantum pattern recognition failed: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n     * Parallel Universe Testing - Test code in multiple scenarios simultaneously\n     */\n  private async performParallelUniverseTesting(code: string, scenarios?: string[]): Promise<{\n    universes: ParallelUniverseTest[];\n    quantumInterference: boolean;\n    collapsedResults: Array<{\n      scenario: string;\n      probability: number;\n      outcome: string;\n    }>;\n  }> {\n    try {\n      const testScenarios = scenarios || this.generateDefaultTestScenarios(code);\n      const parallelUniverses: ParallelUniverseTest[] = [];\n\n      // Create parallel universes for each test scenario\n      for (let i = 0; i < testScenarios.length; i++) {\n        const universe = await this.createParallelUniverse(code, testScenarios[i], `universe_${i}`);\n        parallelUniverses.push(universe);\n      }\n\n      // Check for quantum interference between universes\n      const quantumInterference = this.detectQuantumInterference(parallelUniverses);\n\n      // Collapse the parallel results into observable outcomes\n      const collapsedResults = this.collapseParallelResults(parallelUniverses);\n\n      return {\n        universes: parallelUniverses,\n        quantumInterference,\n        collapsedResults\n      };\n\n    } catch (error) {\n      Logger.instance.error('Parallel universe testing failed:', error);\n      throw new Error(`Parallel universe testing failed: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n     * Quantum Debugging - Debug using quantum superposition analysis\n     */\n  private async performQuantumDebugging(code: string, bugDescription: string): Promise<QuantumDebugging> {\n    try {\n      // Create quantum state for the bug\n      const quantumState = this.createQuantumState(code, bugDescription);\n\n      // Analyze bug in superposition (all possible causes simultaneously)\n      const superpositionAnalysis = await this.analyzeBugSuperposition(code, bugDescription);\n\n      // Find entangled bugs (bugs that affect each other)\n      const entangledBugs = await this.findEntangledBugs(code, bugDescription);\n\n      // Perform parallel debugging in multiple universes\n      const parallelDebugging = await this.performParallelDebugging(code, bugDescription);\n\n      return {\n        bugId: this.generateBugId(bugDescription),\n        quantumState,\n        superpositionAnalysis,\n        entangledBugs,\n        parallelDebugging\n      };\n\n    } catch (error) {\n      Logger.instance.error('Quantum debugging failed:', error);\n      throw new Error(`Quantum debugging failed: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n     * Analyze Code Entanglement - Find quantum entangled code relationships\n     */\n  private async analyzeCodeEntanglement(file: string): Promise<{\n    entanglements: CodeEntanglement[];\n    quantumCorrelations: Array<{\n      correlation: string;\n      strength: number;\n      type: string;\n    }>;\n    spookyActions: Array<{\n      action: string;\n      distance: number;\n      effect: string;\n    }>;\n  }> {\n    try {\n      const _code = await this.readFile(file);\n      const entanglements: CodeEntanglement[] = [];\n\n      // Find quantum entangled relationships\n      const workspaceFiles = await this.getWorkspaceFiles();\n\n      for (const otherFile of workspaceFiles) {\n        if (otherFile !== file) {\n          const entanglement = await this.measureEntanglement(file, otherFile);\n          if (entanglement.strength > 0.3) { // Significant entanglement\n            entanglements.push(entanglement);\n          }\n        }\n      }\n\n      // Store entanglement matrix\n      this.entanglementMatrix.set(file, entanglements);\n\n      // Analyze quantum correlations\n      const quantumCorrelations = this.analyzeQuantumCorrelations(entanglements);\n\n      // Detect spooky action at a distance\n      const spookyActions = this.detectSpookyActions(entanglements);\n\n      return {\n        entanglements,\n        quantumCorrelations,\n        spookyActions\n      };\n\n    } catch (error) {\n      Logger.instance.error('Code entanglement analysis failed:', error);\n      throw new Error(`Code entanglement analysis failed: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n     * Analyze Superposition - Analyze code in quantum superposition\n     */\n  private async analyzeSuperposition(code: string): Promise<{\n    superpositionStates: Array<{\n      state: string;\n      probability: number;\n      properties: string[];\n    }>;\n    observerEffect: boolean;\n    collapsedState: string;\n    quantumUncertainty: number;\n  }> {\n    try {\n      // Create superposition of all possible code states\n      const superpositionStates = this.createCodeSuperposition(code);\n\n      // Check for observer effect (does analyzing the code change it?)\n      const observerEffect = this.detectObserverEffect(code);\n\n      // Collapse the superposition to a single observable state\n      const collapsedState = this.collapseSuperposition(superpositionStates);\n\n      // Calculate quantum uncertainty\n      const quantumUncertainty = this.calculateQuantumUncertainty(superpositionStates);\n\n      return {\n        superpositionStates,\n        observerEffect,\n        collapsedState,\n        quantumUncertainty\n      };\n\n    } catch (error) {\n      Logger.instance.error('Superposition analysis failed:', error);\n      throw new Error(`Superposition analysis failed: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  // Helper methods for quantum operations\n  private async readFile(file: string): Promise<string> {\n    try {\n      const document = await vscode.workspace.openTextDocument(file);\n      return document.getText();\n    } catch (error) {\n      throw new Error(`Failed to read file ${file}: ${error}`);\n    }\n  }\n\n  private async createPatternSuperposition(code: string): Promise<QuantumPattern[]> {\n    // Create quantum superposition of all possible patterns\n    const patterns: QuantumPattern[] = [];\n\n    // Analyze structural patterns\n    patterns.push(...this.analyzeStructuralPatterns(code));\n\n    // Analyze behavioral patterns\n    patterns.push(...this.analyzeBehavioralPatterns(code));\n\n    // Analyze performance patterns\n    patterns.push(...this.analyzePerformancePatterns(code));\n\n    // Analyze security patterns\n    patterns.push(...this.analyzeSecurityPatterns(code));\n\n    return patterns;\n  }\n\n  private applyQuantumInterference(patterns: QuantumPattern[]): QuantumPattern[] {\n    // Apply quantum interference to enhance pattern detection\n    return patterns.map(pattern => ({\n      ...pattern,\n      confidence: pattern.confidence * this.calculateInterferenceAmplification(pattern)\n    }));\n  }\n\n  private collapsePatternSuperposition(patterns: QuantumPattern[]): QuantumPattern[] {\n    // Collapse superposition to observable patterns (confidence > 0.5)\n    return patterns.filter(pattern => pattern.confidence > 0.5);\n  }\n\n  private generateQuantumSignature(pattern: QuantumPattern): string {\n    // Generate unique quantum signature for the pattern\n    return `QS_${pattern.type}_${pattern.confidence.toFixed(3)}_${Date.now()}`;\n  }\n\n  private calculateQuantumConfidence(patterns: QuantumPattern[]): number {\n    if (patterns.length === 0) return 0;\n    return patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length;\n  }\n\n  private async analyzeInParallelUniverses(_code: string, patterns: QuantumPattern[]): Promise<any[]> {\n    // Analyze patterns in parallel universes\n    return patterns.map(pattern => ({\n      pattern: pattern.name,\n      universeAnalysis: `Pattern exists in ${Math.floor(pattern.confidence * 100)}% of parallel universes`\n    }));\n  }\n\n  // Simplified implementations for other quantum methods\n  private generateDefaultTestScenarios(_code: string): string[] {\n    return ['normal_execution', 'edge_cases', 'error_conditions', 'performance_stress'];\n  }\n\n  private async createParallelUniverse(_code: string, scenario: string, universeId: string): Promise<ParallelUniverseTest> {\n    return {\n      universeId,\n      scenario,\n      testConditions: [\n        { condition: 'input_valid', value: true, probability: 0.8 },\n        { condition: 'memory_available', value: true, probability: 0.9 }\n      ],\n      outcomes: [\n        { outcome: 'success', probability: 0.7, impact: 1 },\n        { outcome: 'failure', probability: 0.3, impact: -1 }\n      ],\n      quantumInterference: false\n    };\n  }\n\n  private detectQuantumInterference(universes: ParallelUniverseTest[]): boolean {\n    // Detect if universes interfere with each other\n    return universes.length > 3; // Simplified detection\n  }\n\n  private collapseParallelResults(universes: ParallelUniverseTest[]): Array<{\n    scenario: string;\n    probability: number;\n    outcome: string;\n  }> {\n    return universes.map(universe => ({\n      scenario: universe.scenario,\n      probability: universe.outcomes[0]?.probability || 0,\n      outcome: universe.outcomes[0]?.outcome || 'unknown'\n    }));\n  }\n\n  // Additional helper methods would be implemented here...\n  private createQuantumState(_code: string, _bugDescription: string): QuantumState {\n    return {\n      superposition: true,\n      entangled: false,\n      collapsed: false,\n      probability: 0.5,\n      parallelStates: []\n    };\n  }\n\n  private async analyzeBugSuperposition(_code: string, _bugDescription: string): Promise<Array<{\n    possibleCause: string;\n    probability: number;\n    quantumEvidence: string[];\n  }>> {\n    return [\n      {\n        possibleCause: 'Null pointer exception',\n        probability: 0.6,\n        quantumEvidence: ['quantum_null_detection', 'superposition_analysis']\n      }\n    ];\n  }\n\n  private async findEntangledBugs(_code: string, _bugDescription: string): Promise<Array<{\n    relatedBug: string;\n    entanglementStrength: number;\n    causality: 'forward' | 'backward' | 'bidirectional';\n  }>> {\n    return [];\n  }\n\n  private async performParallelDebugging(_code: string, _bugDescription: string): Promise<ParallelUniverseTest[]> {\n    return [];\n  }\n\n  private generateBugId(bugDescription: string): string {\n    return `BUG_${Date.now()}_${bugDescription.substring(0, 10)}`;\n  }\n\n  private async getWorkspaceFiles(): Promise<string[]> {\n    // Get all files in workspace\n    return ['file1.ts', 'file2.ts']; // Simplified\n  }\n\n  private async measureEntanglement(file1: string, file2: string): Promise<CodeEntanglement> {\n    return {\n      file1,\n      file2,\n      entanglementType: 'functional',\n      strength: 0.5,\n      quantumCorrelation: 0.7,\n      spookyActionDistance: 100,\n      observerEffect: false\n    };\n  }\n\n  private analyzeQuantumCorrelations(entanglements: CodeEntanglement[]): Array<{\n    correlation: string;\n    strength: number;\n    type: string;\n  }> {\n    return entanglements.map(e => ({\n      correlation: `${e.file1} <-> ${e.file2}`,\n      strength: e.quantumCorrelation,\n      type: e.entanglementType\n    }));\n  }\n\n  private detectSpookyActions(entanglements: CodeEntanglement[]): Array<{\n    action: string;\n    distance: number;\n    effect: string;\n  }> {\n    return entanglements.filter(e => e.spookyActionDistance > 50).map(e => ({\n      action: `Change in ${e.file1}`,\n      distance: e.spookyActionDistance,\n      effect: `Instant effect on ${e.file2}`\n    }));\n  }\n\n  private createCodeSuperposition(code: string): Array<{\n    state: string;\n    probability: number;\n    properties: string[];\n  }> {\n    return [\n      { state: 'working', probability: 0.7, properties: ['functional', 'tested'] },\n      { state: 'buggy', probability: 0.2, properties: ['error-prone', 'needs-fix'] },\n      { state: 'optimizable', probability: 0.1, properties: ['performance-issue', 'refactorable'] }\n    ];\n  }\n\n  private detectObserverEffect(code: string): boolean {\n    // Detect if analyzing the code changes its behavior\n    return code.includes('console.log') || code.includes('debugger');\n  }\n\n  private collapseSuperposition(states: Array<{ state: string; probability: number; properties: string[] }>): string {\n    // Collapse to the most probable state\n    return states.reduce((max, current) =>\n      current.probability > max.probability ? current : max\n    ).state;\n  }\n\n  private calculateQuantumUncertainty(states: Array<{ state: string; probability: number; properties: string[] }>): number {\n    // Calculate quantum uncertainty using entropy\n    return states.reduce((entropy, state) =>\n      entropy - state.probability * Math.log2(state.probability || 0.001), 0\n    );\n  }\n\n  // Pattern analysis methods\n  private analyzeStructuralPatterns(code: string): QuantumPattern[] {\n    return [{\n      id: 'struct_1',\n      name: 'Class Structure Pattern',\n      type: 'structural',\n      confidence: 0.8,\n      quantumSignature: 'QS_STRUCT_001',\n      parallelManifestations: [],\n      entangledElements: []\n    }];\n  }\n\n  private analyzeBehavioralPatterns(code: string): QuantumPattern[] {\n    return [];\n  }\n\n  private analyzePerformancePatterns(code: string): QuantumPattern[] {\n    return [];\n  }\n\n  private analyzeSecurityPatterns(code: string): QuantumPattern[] {\n    return [];\n  }\n\n  private calculateInterferenceAmplification(pattern: QuantumPattern): number {\n    // Calculate quantum interference amplification\n    return 1.2; // Simplified amplification factor\n  }\n}\n"]}