{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/ui/settings/types.ts"], "names": [], "mappings": ";AAAA,6CAA6C", "sourcesContent": ["// Shared types/interfaces for settings panel\n\n// Base types\nexport type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'trace';\nexport type Theme = 'light' | 'dark' | 'system' | 'high-contrast';\nexport type ChatLayout = 'default' | 'compact' | 'detailed';\nexport type VectorStoreType = 'memory' | 'pinecone' | 'weaviate' | 'chroma' | 'redis';\n\n// Provider and Model types\nexport type ProviderType = \n  | 'openai' \n  | 'googleai' \n  | 'anthropic' \n  | 'mistral' \n  | 'ollama' \n  | 'ai21'\n  | 'cohere'\n  | 'togetherai'\n  | 'openrouter'\n  | 'huggingface'\n  | 'codellama'\n  | 'santacoder'\n  | 'starcoder'\n  | 'xwincoder'\n  | 'wizardcoder'\n  | 'stablestudio'\n  | 'noushermes'\n  | 'custom';\n\nexport interface ProviderConfig {\n    id: string;\n    name: string;\n    type: ProviderType;\n    apiKey?: string;\n    baseUrl?: string;\n    isActive: boolean;\n    defaultModel?: string;\n    maxTokens?: number;\n    temperature?: number;\n}\n\nexport interface ModelConfig {\n    id: string;\n    name: string;\n    provider: string;\n    contextWindow: number;\n    maxTokens: number;\n    trainingData: string;\n    capabilities: string[];\n}\n\n// Agent and Workflow types\nexport interface AgentConfig {\n    id: string;\n    name: string;\n    description: string;\n    role: string;\n    model?: string;\n    provider?: string;\n    temperature?: number;\n    maxTokens?: number;\n    systemPrompt?: string;\n    isActive: boolean;\n    tools?: string[];\n    memoryConfig?: {\n        enabled: boolean;\n        maxMemories: number;\n        relevanceThreshold: number;\n    };\n}\n\nexport interface WorkflowStep {\n    id: string;\n    type: 'agent' | 'tool' | 'condition' | 'loop';\n    config: Record<string, unknown>;\n    nextStepId?: string;\n}\n\nexport interface WorkflowConfig {\n    id: string;\n    name: string;\n    description: string;\n    steps: WorkflowStep[];\n    isActive: boolean;\n    triggerEvents: string[];\n}\n\n// TTS and Voice types\nexport type TTSProvider = 'system' | 'elevenlabs' | 'google' | 'amazon' | 'microsoft';\n\nexport interface TTSConfig {\n    enabled: boolean;\n    provider: TTSProvider;\n    voice: string;\n    rate: number;\n    pitch: number;\n    volume: number;\n    autoSpeak: boolean;\n    apiKeys: {\n        elevenlabs?: string;\n        google?: string;\n        amazon?: string;\n        microsoft?: string;\n    };\n    advanced: {\n        ssmlEnabled: boolean;\n        emotionControl: boolean;\n        qualityMode: 'fast' | 'balanced' | 'high';\n        cacheEnabled: boolean;\n        streamingEnabled: boolean;\n    };\n}\n\n// Database types\nexport type DatabaseType = 'sqlite' | 'postgres' | 'mongodb' | 'mysql';\n\nexport interface DatabaseConfig {\n    type: DatabaseType;\n    connectionString?: string;\n    host?: string;\n    port?: number;\n    database?: string;\n    username?: string;\n    password?: string;\n    ssl?: boolean;\n    synchronize: boolean;\n    logging: boolean;\n}\n\n// Knowledgebase types\nexport interface KnowledgebaseSource {\n    id: string;\n    type: 'file' | 'folder' | 'url' | 'git' | 'database';\n    path: string;\n    name: string;\n    description?: string;\n    isActive: boolean;\n    lastSynced?: number;\n    syncStatus: 'idle' | 'syncing' | 'error' | 'success';\n    error?: string;\n    metadata?: Record<string, unknown>;\n}\n\nexport interface KnowledgebaseConfig {\n    enabled: boolean;\n    sources: KnowledgebaseSource[];\n    autoSync: boolean;\n    syncInterval: number;\n    chunkSize: number;\n    chunkOverlap: number;\n    vectorStore: VectorStoreType;\n}\n\n// Workspace types\nexport type FileType = 'file' | 'directory' | 'symlink' | 'unknown';\n\nexport interface WorkspaceFile {\n    path: string;\n    name: string;\n    type: FileType;\n    size?: number;\n    lastModified?: number;\n    tags: string[];\n    metadata: Record<string, unknown>;\n    isExcluded: boolean;\n    isBinary: boolean;\n    encoding: string;\n    languageId?: string;\n}\n\nexport type TeamRole = 'owner' | 'admin' | 'developer' | 'viewer' | 'guest';\n\nexport interface WorkspaceTeamMember {\n    id: string;\n    name: string;\n    email: string;\n    role: TeamRole;\n    avatarUrl?: string;\n    lastActive?: number;\n    permissions: string[];\n    isActive: boolean;\n    joinedAt: number;\n}\n\nexport type DocumentationItemType = 'file' | 'link' | 'note' | 'code' | 'image' | 'diagram' | 'api';\n\nexport interface WorkspaceDocumentationItem {\n    id: string;\n    type: DocumentationItemType;\n    title: string;\n    content: string;\n    path?: string;\n    url?: string;\n    tags: string[];\n    createdAt: number;\n    updatedAt: number;\n    createdBy: string;\n    updatedBy: string;\n    metadata: Record<string, unknown>;\n    isPinned: boolean;\n    isArchived: boolean;\n}\n\nexport interface WorkspaceKnowledgebaseConfig {\n    sources: KnowledgebaseSource[];\n    shared: boolean;\n    autoSync: boolean;\n    syncInterval: number;\n    lastSynced?: number;\n    syncStatus: 'idle' | 'syncing' | 'error' | 'success';\n    lastError?: string;\n    chunkSize: number;\n    chunkOverlap: number;\n    includePatterns: string[];\n    excludePatterns: string[];\n}\n\nexport interface Workspace {\n    id: string;\n    name: string;\n    path: string;\n    description: string;\n    tags: string[];\n    files: WorkspaceFile[];\n    team: WorkspaceTeamMember[];\n    memory: string;\n    documentation: WorkspaceDocumentationItem[];\n    knowledgebase: WorkspaceKnowledgebaseConfig;\n    createdAt: number;\n    updatedAt: number;\n    isActive: boolean;\n    settings: {\n        autoSave: boolean;\n        autoFormat: boolean;\n        lintOnSave: boolean;\n        formatOnSave: boolean;\n        experimentalFeatures: boolean;\n    };\n}\n\n// Settings Section Type\nexport interface SettingsSection {\n    id: string;\n    title: string;\n    icon: string;\n    description: string;\n    order: number;\n    component: string;\n}\n\n// Main Settings interface\nexport interface Settings {\n    // General Settings\n    logLevel: LogLevel;\n    theme: Theme;\n    fontSize: number;\n    chatLayout: ChatLayout;\n    autoSave: boolean;\n    autoSaveDelay: number;\n    showLineNumbers: boolean;\n    showMinimap: boolean;\n    \n    // Provider & Model Settings\n    defaultProvider: string;\n    defaultModel: string;\n    providers: ProviderConfig[];\n    models: ModelConfig[];\n    \n    // Agent Settings\n    agents: AgentConfig[];\n    defaultAgent: string;\n    \n    // Workflow Settings\n    workflows: WorkflowConfig[];\n    \n    // Memory Settings\n    memoryEnabled: boolean;\n    maxMemories: number;\n    relevanceThreshold: number;\n    contextWindowSize: number;\n    conversationHistorySize: number;\n    vectorStore: VectorStoreType;\n    \n    // TTS Settings\n    tts: TTSConfig;\n    \n    // Database Settings\n    database: DatabaseConfig;\n    \n    // Knowledgebase Settings\n    knowledgebase: KnowledgebaseConfig;\n    \n    // Workspace Settings\n    workspace?: Workspace;\n    workspaces?: Workspace[];\n    activeWorkspace?: string;\n    \n    // UI/UX Settings\n    showTimestamps: boolean;\n    showCodeLens: boolean;\n    showInlineSuggestions: boolean;\n    showStatusBar: boolean;\n    \n    // Advanced Settings\n    experimentalFeatures: boolean;\n    developerMode: boolean;\n    telemetryEnabled: boolean;\n    crashReporting: boolean;\n}\n"]}