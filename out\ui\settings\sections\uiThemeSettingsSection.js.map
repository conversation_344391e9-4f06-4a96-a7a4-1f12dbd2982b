{"version": 3, "file": "uiThemeSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/uiThemeSettingsSection.ts"], "names": [], "mappings": ";;AAIA,oEA2KC;AA/KD,gDAAqE;AACrE,+CAAoD;AAEpD,yCAAyC;AACzC,SAAgB,4BAA4B,CAAC,SAAsB,EAAE,QAAa;IAChF,iBAAiB;IACjB,kCAAkC;IAClC,MAAM,WAAW,GAAkB,QAAQ,CAAC,WAAW,IAAI,EAAE,GAAG,kCAAoB,EAAE,CAAC;IACvF,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC;IAC/B,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;IAC5C,MAAM,QAAQ,GAAG,OAAO,QAAQ,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACvI,MAAM,MAAM,GAAG,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9E,MAAM,OAAO,GAAG,OAAO,QAAQ,CAAC,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAEjF,SAAS,iBAAiB,CAAC,GAAW,EAAE,KAAU;QAChD,8CAA8C;QAC9C,IAAI,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9C,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YACtC,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9C,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACL,WAAmB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpC,CAAC;QACD,IAAA,qBAAa,EAAC,WAAW,CAAC,CAAC,CAAC,oBAAoB;QAChD,QAAQ,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IACvC,CAAC;IAED,0BAA0B;IAC1B,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAEjC,SAAS,OAAO,CAAC,GAAQ,EAAE,IAAY,EAAE,KAAU;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACvC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QACD,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IACrC,CAAC;IAED,SAAS,CAAC,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iIA6ByG,WAAW;oGACxC,WAAW;;;;;;yCAMtE,WAAW;4FACwC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;;;uEAGnE,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,UAAU,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,WAAW,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,kBAAkB,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY;;;;;iEAKlL,WAAW,CAAC,KAAK,CAAC,UAAU;;qEAExB,WAAW,CAAC,KAAK,CAAC,MAAM;;sFAEP,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC;gDAC9E,WAAW,CAAC,KAAK,CAAC,YAAY;;oEAEV,WAAW,CAAC,KAAK,CAAC,SAAS;;mGAEI,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC;;kEAEjF,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE;;;;0EAInD,WAAW,CAAC,OAAO,CAAC,QAAQ;;6EAEzB,WAAW,CAAC,OAAO,CAAC,WAAW;;uEAErC,WAAW,CAAC,OAAO,CAAC,MAAM;;uEAE1B,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU;;0EAElC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;;4FAEd,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;sDACvF,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY;;;;;;;KAOxF,CAAC;IAEJ,oBAAoB;IACnB,SAAS,CAAC,aAAa,CAAC,UAAU,KAAK,EAAE,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;IAEhF,yBAAyB;IACzB,MAAM,WAAW,GAAG,SAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAiC,CAAC;IACtG,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1B,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,CAAC,CAAC,MAA0B,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;YAC9B,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC5B,aAAa,CAAC,QAAQ,EAAG,SAAS,CAAC,aAAa,CAAC,qBAAqB,CAAsB,CAAC,KAAK,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC,qBAAqB,CAAqB,CAAC;IACvF,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE;QACxC,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC;QACnC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QACjC,SAAS,CAAC,aAAa,CAAC,qBAAqB,CAAiB,CAAC,WAAW,GAAG,QAAQ,CAAC;QACvF,aAAa,CAAE,SAAS,CAAC,aAAa,CAAC,6BAA6B,CAAsB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC9G,CAAC,CAAC,CAAC;IAEH,cAAc;IACd,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,oBAAoB,CAAsB,CAAC;IACnF,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;QACrC,IAAI,MAAM;YAAE,MAAM,EAAE,CAAC;;YAChB,KAAK,CAAC,iBAAiB,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,eAAe;IACf,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,CAAC,qBAAqB,CAAsB,CAAC;IACrF,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;QACtC,IAAI,OAAO;YAAE,OAAO,EAAE,CAAC;QACvB,uBAAuB;QACtB,SAAS,CAAC,aAAa,CAAC,eAAe,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAC9E,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;QAC7B,SAAS,CAAC,aAAa,CAAC,qBAAqB,CAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;QACxF,aAAa,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACnC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5B,QAAQ,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,SAAS,aAAa,CAAC,UAAkB,EAAE,MAAc;QACvD,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,mBAAmB,CAAgB,CAAC;QACxE,OAAO,CAAC,aAAa,CAAC,MAAM,CAAE,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;QACpD,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAgB,CAAC;QAC3E,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;IAED,0CAA0C;IACzC,SAAS,CAAC,aAAa,CAAC,mBAAmB,CAAiB,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC7E,CAAC", "sourcesContent": ["import { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';\nimport { setModalTheme } from '../components/modal';\n\n// UI & Theme section logic and rendering\nexport function renderUIThemeSettingsSection(container: HTMLElement, settings: any) {\n  // Default values\n  // Use or initialize UIThemeConfig\n  const themeConfig: UIThemeConfig = settings.themeConfig || { ...defaultUIThemeConfig };\n  const theme = themeConfig.mode;\n  const accentColor = themeConfig.accentColor;\n  const onChange = typeof settings.onChange === 'function' ? settings.onChange : (key: string, value: any) => { settings[key] = value; };\n  const onSave = typeof settings.onSave === 'function' ? settings.onSave : null;\n  const onReset = typeof settings.onReset === 'function' ? settings.onReset : null;\n\n  function updateThemeConfig(key: string, value: any) {\n    // Only update top-level or modal/section keys\n    if (key.startsWith('modal.')) {\n      const sub = key.split('.').slice(1).join('.');\n      setDeep(themeConfig.modal, sub, value);\n    } else if (key.startsWith('section.')) {\n      const sub = key.split('.').slice(1).join('.');\n      setDeep(themeConfig.section, sub, value);\n    } else {\n      (themeConfig as any)[key] = value;\n    }\n    setModalTheme(themeConfig); // Live update modal\n    onChange('themeConfig', themeConfig);\n  }\n  \n  // Initialize theme config\n  updateThemeConfig('mode', theme);\n\n  function setDeep(obj: any, path: string, value: any) {\n    const parts = path.split('.');\n    let o = obj;\n    for (let i = 0; i < parts.length - 1; i++) {\n      if (!(parts[i] in o)) o[parts[i]] = {};\n      o = o[parts[i]];\n    }\n    o[parts[parts.length - 1]] = value;\n  }\n\n  container.innerHTML = `\n        <style>\n            .ui-theme-section { max-width:480px; margin:0 auto; padding:22px 0; }\n            .ui-theme-title { font-size:1.18em; font-weight:600; margin-bottom:14px; color:#222; }\n            .ui-theme-group { margin-bottom:22px; }\n            .ui-theme-label { font-weight:500; display:block; margin-bottom:7px; color:#374151; }\n            .ui-theme-radio-group { display:flex; gap:22px; margin-bottom:8px; }\n            .ui-theme-radio { display:flex; align-items:center; gap:7px; }\n            .ui-theme-accent-row { display:flex; align-items:center; gap:12px; }\n            .ui-theme-preview { margin-top:18px; padding:14px 18px; border-radius:8px; background:#f8fafc; border:1px solid #e5e7eb; }\n            .ui-theme-btn-row { display:flex; gap:14px; margin-top:22px; }\n            .ui-theme-btn { padding:7px 22px; border-radius:6px; border:none; font-weight:500; font-size:1em; cursor:pointer; }\n            .ui-theme-btn.save { background:#2563eb; color:#fff; }\n            .ui-theme-btn.reset { background:#f3f4f6; color:#222; border:1px solid #e5e7eb; }\n            .ui-theme-accent-input { width:34px; height:34px; border-radius:50%; border:1.5px solid #d1d5db; background:transparent; cursor:pointer; }\n        </style>\n        <div class=\"ui-theme-section\" role=\"region\" aria-labelledby=\"ui-theme-title\">\n            <div class=\"ui-theme-title\" id=\"ui-theme-title\">UI & Theme Settings</div>\n            <div class=\"ui-theme-group\">\n                <label class=\"ui-theme-label\" for=\"theme-select\">Theme</label>\n                <div class=\"ui-theme-radio-group\" id=\"theme-radio-group\">\n                    <label class=\"ui-theme-radio\"><input type=\"radio\" name=\"theme\" value=\"light\" id=\"theme-light\"> Light</label>\n                    <label class=\"ui-theme-radio\"><input type=\"radio\" name=\"theme\" value=\"dark\" id=\"theme-dark\"> Dark</label>\n                    <label class=\"ui-theme-radio\"><input type=\"radio\" name=\"theme\" value=\"system\" id=\"theme-system\"> System</label>\n                </div>\n            </div>\n            <div class=\"ui-theme-group\">\n                <label class=\"ui-theme-label\" for=\"accent-color-input\">Accent Color</label>\n                <div class=\"ui-theme-accent-row\">\n                    <input type=\"color\" id=\"accent-color-input\" class=\"ui-theme-accent-input\" aria-label=\"Accent Color\" value=\"${accentColor}\">\n                    <span id=\"accent-color-value\" style=\"font-family:monospace;font-size:0.98em;\">${accentColor}</span>\n                </div>\n            </div>\n            <div class=\"ui-theme-preview\" id=\"ui-theme-preview\" aria-live=\"polite\">\n                <div style=\"font-weight:500;\">Live Preview:</div>\n                <div style=\"margin-top:7px;\">\n                    <span style=\"color:${accentColor};font-weight:600;\">Accent Color Example</span>\n                    <span style=\"margin-left:18px;\">Theme: <span id=\"theme-preview-label\">${theme.charAt(0).toUpperCase() + theme.slice(1)}</span></span>\n                </div>\n                <div style=\"margin-top:14px;\">\n                    <button id=\"modal-preview-btn\" style=\"background:${themeConfig.modal.button.background};color:${themeConfig.modal.button.color};border:${themeConfig.modal.button.border};border-radius:${themeConfig.modal.button.borderRadius};padding:7px 22px;font-size:1em;cursor:pointer;\">Show Modal Preview</button>\n                </div>\n            </div>\n            <div class=\"ui-theme-group\">\n                <label class=\"ui-theme-label\">Modal Background</label>\n                <input type=\"color\" id=\"modal-bg-input\" value=\"${themeConfig.modal.background}\">\n                <label class=\"ui-theme-label\">Modal Border</label>\n                <input type=\"color\" id=\"modal-border-input\" value=\"${themeConfig.modal.border}\">\n                <label class=\"ui-theme-label\">Modal Border Radius</label>\n                <input type=\"range\" id=\"modal-radius-input\" min=\"4\" max=\"32\" value=\"${parseInt(themeConfig.modal.borderRadius)}\">\n                <span id=\"modal-radius-value\">${themeConfig.modal.borderRadius}</span>\n                <label class=\"ui-theme-label\">Modal Shadow</label>\n                <input type=\"text\" id=\"modal-shadow-input\" value=\"${themeConfig.modal.boxShadow}\" style=\"width:220px;\">\n                <label class=\"ui-theme-label\">Modal Animation Duration (s)</label>\n                <input type=\"number\" id=\"modal-anim-input\" min=\"0.05\" max=\"2\" step=\"0.01\" value=\"${parseFloat(themeConfig.modal.animation.duration)}\">\n                <label class=\"ui-theme-label\">Modal Icon (SVG allowed)</label>\n                <input type=\"text\" id=\"modal-icon-input\" value=\"${themeConfig.modal.warningIcon.replace(/'/g, '&apos;') || ''}\" style=\"width:220px;\">\n            </div>\n            <div class=\"ui-theme-group\">\n                <label class=\"ui-theme-label\">Section Header Background</label>\n                <input type=\"color\" id=\"section-header-bg-input\" value=\"${themeConfig.section.headerBg}\">\n                <label class=\"ui-theme-label\">Section Header Color</label>\n                <input type=\"color\" id=\"section-header-color-input\" value=\"${themeConfig.section.headerColor}\">\n                <label class=\"ui-theme-label\">Section Accent</label>\n                <input type=\"color\" id=\"section-accent-input\" value=\"${themeConfig.section.accent}\">\n                <label class=\"ui-theme-label\">Section Button Background</label>\n                <input type=\"color\" id=\"section-btn-bg-input\" value=\"${themeConfig.section.button.background}\">\n                <label class=\"ui-theme-label\">Section Button Color</label>\n                <input type=\"color\" id=\"section-btn-color-input\" value=\"${themeConfig.section.button.color}\">\n                <label class=\"ui-theme-label\">Section Button Border Radius</label>\n                <input type=\"range\" id=\"section-btn-radius-input\" min=\"2\" max=\"20\" value=\"${parseInt(themeConfig.section.button.borderRadius)}\">\n                <span id=\"section-btn-radius-value\">${themeConfig.section.button.borderRadius}</span>\n            </div>\n            <div class=\"ui-theme-btn-row\">\n                <button class=\"ui-theme-btn save\" id=\"ui-theme-save-btn\" type=\"button\">Save</button>\n                <button class=\"ui-theme-btn reset\" id=\"ui-theme-reset-btn\" type=\"button\">Reset</button>\n            </div>\n        </div>\n    `;\n\n  // Set initial radio\n  (container.querySelector(`#theme-${theme}`) as HTMLInputElement).checked = true;\n\n  // --- Event handlers ---\n  const themeRadios = container.querySelectorAll('input[name=\"theme\"]') as NodeListOf<HTMLInputElement>;\n  themeRadios.forEach(radio => {\n    radio.addEventListener('change', (e) => {\n      const target = e.target as HTMLInputElement;\n      console.log('Theme changed to:', target.value);\n      const newTheme = target.value;\n      onChange('theme', newTheme);\n      updatePreview(newTheme, (container.querySelector('#accent-color-input') as HTMLInputElement).value);\n    });\n  });\n\n  const accentInput = container.querySelector('#accent-color-input') as HTMLInputElement;\n  accentInput.addEventListener('input', e => {\n    const newColor = accentInput.value;\n    onChange('accentColor', newColor);\n    (container.querySelector('#accent-color-value') as HTMLElement).textContent = newColor;\n    updatePreview((container.querySelector('input[name=\"theme\"]:checked') as HTMLInputElement).value, newColor);\n  });\n\n  // Save button\n  const saveBtn = container.querySelector('#ui-theme-save-btn') as HTMLButtonElement;\n  saveBtn.addEventListener('click', () => {\n    if (onSave) onSave();\n    else alert('Settings saved!');\n  });\n\n  // Reset button\n  const resetBtn = container.querySelector('#ui-theme-reset-btn') as HTMLButtonElement;\n  resetBtn.addEventListener('click', () => {\n    if (onReset) onReset();\n    // Reset UI to defaults\n    (container.querySelector('#theme-system') as HTMLInputElement).checked = true;\n    accentInput.value = '#2563eb';\n    (container.querySelector('#accent-color-value') as HTMLElement).textContent = '#2563eb';\n    updatePreview('system', '#2563eb');\n    onChange('theme', 'system');\n    onChange('accentColor', '#2563eb');\n  });\n\n  // --- Live preview helper ---\n  function updatePreview(themeValue: string, accent: string) {\n    const preview = container.querySelector('#ui-theme-preview') as HTMLElement;\n        preview.querySelector('span')!.style.color = accent;\n        const label = preview.querySelector('#theme-preview-label') as HTMLElement;\n        label.textContent = themeValue.charAt(0).toUpperCase() + themeValue.slice(1);\n  }\n\n  // --- Accessibility: focus management ---\n  (container.querySelector('.ui-theme-section') as HTMLElement).tabIndex = 0;\n}\n\n"]}