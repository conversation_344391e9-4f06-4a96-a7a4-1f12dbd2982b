/**
 * <PERSON><PERSON> Settings Panel Styles - Enhanced Version
 */

* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    line-height: 1.6;
}

.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.settings-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--vscode-editor-foreground);
}

.settings-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.btn-primary:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.btn-secondary {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.btn-secondary:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.settings-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--vscode-panel-border);
    overflow-x: auto;
}

.settings-tab {
    padding: 12px 20px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--vscode-descriptionForeground);
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 120px;
    text-align: center;
}

.settings-tab:hover {
    color: var(--vscode-editor-foreground);
    background-color: var(--vscode-list-hoverBackground);
}

.settings-tab.active {
    color: var(--vscode-editor-foreground);
    border-bottom-color: var(--vscode-focusBorder);
    background-color: var(--vscode-list-activeSelectionBackground);
}

.settings-content {
    min-height: 400px;
}

.settings-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.settings-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-content {
    max-width: 800px;
}

.section-content h2 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--vscode-editor-foreground);
}

.section-content p {
    margin: 0 0 30px 0;
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--vscode-editor-foreground);
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="number"],
.form-group input[type="email"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus,
.form-group input[type="number"]:focus,
.form-group input[type="email"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 2px var(--vscode-focusBorder);
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

.form-group input[type="checkbox"] + label {
    display: inline;
    margin-bottom: 0;
    cursor: pointer;
}

.setting-description {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    margin-top: 4px;
    line-height: 1.4;
}

.feature-group {
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.feature-group h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--vscode-editor-foreground);
}

.setting-error {
    color: var(--vscode-errorForeground);
    font-size: 12px;
    margin-top: 4px;
    padding: 4px 8px;
    background-color: var(--vscode-inputValidation-errorBackground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    border-radius: 3px;
}

.status-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 500;
}

.status-success {
    background-color: var(--vscode-notificationsInfoBackground);
    color: var(--vscode-notificationsInfoForeground);
    border: 1px solid var(--vscode-notificationsInfoBorder);
}

.status-error {
    background-color: var(--vscode-notificationsErrorBackground);
    color: var(--vscode-notificationsErrorForeground);
    border: 1px solid var(--vscode-notificationsErrorBorder);
}

.status-info {
    background-color: var(--vscode-notificationsInfoBackground);
    color: var(--vscode-notificationsInfoForeground);
    border: 1px solid var(--vscode-notificationsInfoBorder);
}

.status-warning {
    background-color: var(--vscode-notificationsWarningBackground);
    color: var(--vscode-notificationsWarningForeground);
    border: 1px solid var(--vscode-notificationsWarningBorder);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--vscode-progressBar-background);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .settings-container {
        padding: 10px;
    }
    
    .settings-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .settings-tabs {
        flex-wrap: wrap;
    }
    
    .settings-tab {
        min-width: auto;
        flex: 1;
        padding: 10px 12px;
        font-size: 13px;
    }
    
    .form-group input[type="text"],
    .form-group input[type="password"],
    .form-group input[type="number"],
    .form-group input[type="email"],
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Dark theme specific adjustments */
@media (prefers-color-scheme: dark) {
    .feature-group {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* Light theme specific adjustments */
@media (prefers-color-scheme: light) {
    .feature-group {
        background-color: rgba(0, 0, 0, 0.02);
    }
}

/* Accessibility improvements */
.settings-tab:focus,
.btn:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid var(--vscode-focusBorder);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .settings-tab {
        border: 1px solid var(--vscode-panel-border);
    }
    
    .settings-tab.active {
        border: 2px solid var(--vscode-focusBorder);
    }
    
    .btn {
        border: 1px solid var(--vscode-button-border);
    }
}
