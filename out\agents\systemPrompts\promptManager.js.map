{"version": 3, "file": "promptManager.js", "sourceRoot": "", "sources": ["../../../src/agents/systemPrompts/promptManager.ts"], "names": [], "mappings": ";;;AAAA,yCAAoE;AACpE,yCAAsC;AACtC,qDAAwD;AAExD,MAAM,aAAa;IACT,aAAa,GAA2B,EAAE,CAAC;IAC3C,SAAS,GAA2B,EAAE,CAAC;IAE/C;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,WAAW;QACT,MAAM,WAAW,GAAG,IAAA,yBAAgB,GAAE,CAAC;QACvC,iEAAiE;QACjE,IAAI,CAAC,aAAa,GAAG,EAAE,GAAG,qCAAoB,EAAE,GAAG,WAAW,EAAE,CAAC;QACjE,eAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,kBAAkB,CAAC,CAAC;IAClF,CAAC;IAED,aAAa;QACX,IAAI,CAAC,SAAS,GAAG,IAAA,2BAAkB,GAAE,CAAC;QACtC,eAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,oBAAoB,CAAC,CAAC;IAChF,CAAC;IAED,eAAe,CAAC,IAAY,EAAE,iBAAyC,EAAE;QACvE,IAAI,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,wBAAwB,IAAI,cAAc,CAAC,CAAC;YACxD,kDAAkD;YAClD,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;YACrD,IAAI,CAAC,cAAc;gBAAE,OAAO,SAAS,CAAC,CAAC,qBAAqB;QAC9D,CAAC;QAED,6CAA6C;QAC7C,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,cAAc,EAAE,CAAC;QAEzD,4CAA4C;QAC5C,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;gBAC3E,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,oCAAoC;YAChH,CAAC,CAAC,CAAC;YACH,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,cAAc,CAAC,CAAC,uCAAuC;QAChE,CAAC;IACH,CAAC;IAED,eAAe;QACb,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACzC,CAAC;IAED;;SAEK;IACL,oBAAoB,CAAC,IAAY;QAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc;YAAE,OAAO,SAAS,CAAC;QAEtC,4CAA4C;QAC5C,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACvD,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5D,OAAO,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAClD,CAAC;QAED,OAAO,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;IAChF,CAAC;CAGF;AAEY,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC", "sourcesContent": ["import { getSystemPrompts, getPromptVariables } from '../../config';\nimport { logger } from '../../logger';\nimport { defaultSystemPrompts } from './defaultPrompts';\n\nclass PromptManager {\n  private systemPrompts: Record<string, string> = {};\n  private variables: Record<string, string> = {};\n\n  constructor() {\n    this.loadPrompts();\n    this.loadVariables();\n  }\n\n  loadPrompts(): void {\n    const userPrompts = getSystemPrompts();\n    // Merge default and user prompts, user prompts override defaults\n    this.systemPrompts = { ...defaultSystemPrompts, ...userPrompts };\n    logger.info(`Loaded ${Object.keys(this.systemPrompts).length} system prompts.`);\n  }\n\n  loadVariables(): void {\n    this.variables = getPromptVariables();\n    logger.info(`Loaded ${Object.keys(this.variables).length} prompt variables.`);\n  }\n\n  getSystemPrompt(name: string, additionalVars: Record<string, string> = {}): string | undefined {\n    let promptTemplate = this.systemPrompts[name];\n    if (!promptTemplate) {\n      logger.warn(`System prompt named '${name}' not found.`);\n      // Fallback to a generic default if name not found\n      promptTemplate = this.systemPrompts['default_coder'];\n      if (!promptTemplate) return undefined; // No fallback either\n    }\n\n    // Combine global and task-specific variables\n    const allVars = { ...this.variables, ...additionalVars };\n\n    // Replace placeholders like {variable_name}\n    try {\n      const filledPrompt = promptTemplate.replace(/\\{(\\w+)\\}/g, (match, varName) => {\n        return allVars[varName] !== undefined ? String(allVars[varName]) : match; // Keep placeholder if var not found\n      });\n      return filledPrompt;\n    } catch (error) {\n      logger.error(`Error processing prompt template '${name}':`, error);\n      return promptTemplate; // Return unprocessed template on error\n    }\n  }\n\n  listPromptNames(): string[] {\n    return Object.keys(this.systemPrompts);\n  }\n\n  /**\n     * Gets the description of a prompt by extracting the first line or returning a default\n     */\n  getPromptDescription(name: string): string | undefined {\n    const promptTemplate = this.systemPrompts[name];\n    if (!promptTemplate) return undefined;\n\n    // Extract the first line as the description\n    const firstLine = promptTemplate.split('\\n')[0].trim();\n    if (firstLine.startsWith('#') || firstLine.startsWith('//')) {\n      return firstLine.replace(/^[#/\\s]+/, '').trim();\n    }\n\n    return firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine;\n  }\n\n  // Add methods for creating/editing prompts and variables via UI later\n}\n\nexport const promptManager = new PromptManager();\n"]}