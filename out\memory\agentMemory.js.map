{"version": 3, "file": "agentMemory.js", "sourceRoot": "", "sources": ["../../src/memory/agentMemory.ts"], "names": [], "mappings": ";;;AAyMA,wCAeC;AAvND,sCAAmC;AAEnC,mDAAgD;AAChD,sCAAsC;AAEtC;;GAEG;AACH,MAAa,WAAW;IACd,QAAQ,CAAiB;IACzB,mBAAmB,GAAkB,EAAE,CAAC;IACxC,gBAAgB,GAAkB,EAAE,CAAC;IAE7C,YAAY,KAAY;QACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,IAAY,KAAK;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,KAA4C;QACjE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC5D,CAAC;YAED,yCAAyC;YACzC,MAAM,MAAM,GAAG,MAAM,6BAAa,CAAC,SAAS,CAAC;gBAC3C,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,QAAQ,EAAE;oBACR,GAAG,KAAK,CAAC,QAAQ;oBACjB,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;iBACtB;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;YAC5E,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU,CAAC,IAA0B,EAAE,OAAe;QACjE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,eAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;gBAC1E,OAAO;YACT,CAAC;YAED,sBAAsB;YACtB,MAAM,MAAM,GAAG,MAAM,6BAAa,CAAC,SAAS,CAAC;gBAC3C,OAAO;gBACP,QAAQ,EAAE;oBACR,MAAM,EAAE,cAA8B;oBACtC,IAAI,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,OAAqB,CAAC,CAAC,CAAC,IAAkB;oBAClE,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;iBACtB;aACF,CAAC,CAAC;YAEH,8BAA8B;YAC9B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEtC,sCAAsC;YACtC,MAAM,cAAc,GAAG,IAAA,kBAAS,EAAS,gCAAgC,EAAE,GAAG,CAAC,CAAC;YAChF,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,cAAc,EAAE,CAAC;gBACrD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,CAAC;YAC7E,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,SAAS,IAAI,6BAA6B,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;QACxF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACvC,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,mBAAmB,CAAC,KAAa;QAC5C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,eAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;gBACpF,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,6BAA6B;YAC7B,MAAM,aAAa,GAAG,IAAA,kBAAS,EAAU,gBAAgB,EAAE,IAAI,CAAC,CAAC;YACjE,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,+BAA+B;YAC/B,IAAI,CAAC,gBAAgB,GAAG,MAAM,6BAAa,CAAC,qBAAqB,CAAC,KAAK,EAAE;gBACvE,KAAK,EAAE,IAAA,kBAAS,EAAS,0BAA0B,EAAE,CAAC,CAAC;gBACvD,MAAM,EAAE;oBACN,MAAM,EAAE,cAAc;oBACtB,OAAO,EAAE,KAAK,CAAC,EAAE;iBAClB;aACF,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,wBAAwB;QAC7B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;IAChC,CAAC;IAED;;SAEK;IACE,uBAAuB,CAAC,QAAuB;QACpD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC9C,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;YACrE,OAAO,GAAG,IAAI,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhB,OAAO,uCAAuC,iBAAiB,MAAM,CAAC;IACxE,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,eAAM,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;gBACjF,OAAO,wCAAwC,CAAC;YAClD,CAAC;YAED,kCAAkC;YAClC,MAAM,QAAQ,GAAG,MAAM,6BAAa,CAAC,cAAc,CAAC;gBAClD,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE;oBACN,OAAO,EAAE,KAAK,CAAC,EAAE;iBAClB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,wBAAwB,CAAC;YAClC,CAAC;YAED,gBAAgB;YAChB,MAAM,UAAU,GAA2B,EAAE,CAAC;YAC9C,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAClC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjD,CAAC;YAED,iBAAiB;YACjB,MAAM,OAAO,GAAG;gBACd,mBAAmB,QAAQ,CAAC,MAAM,EAAE;gBACpC,eAAe;gBACf,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC;aAC5E,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEb,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,+BAA+B,CAAC;QACzC,CAAC;IACH,CAAC;CACF;AAzLD,kCAyLC;AAED;;GAEG;AACH,MAAM,aAAa,GAAG,IAAI,OAAO,EAAsB,CAAC;AAExD,SAAgB,cAAc,CAAC,KAAY;IACzC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;IAC/D,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import type { Agent } from '../agents/agentUtilities/agent';\nimport { logger } from '../logger';\nimport type { MemoryEntry, MemorySource, MemoryType } from './types';\nimport { memoryManager } from './memoryManager';\nimport { getConfig } from '../config';\n\n/**\n * Agent memory integration\n */\nexport class AgentMemory {\n  private agentRef: WeakRef<Agent>;\n  private conversationHistory: MemoryEntry[] = [];\n  private relevantMemories: MemoryEntry[] = [];\n\n  constructor(agent: Agent) {\n    this.agentRef = new WeakRef(agent);\n  }\n\n  private get agent(): Agent | undefined {\n    return this.agentRef.deref();\n  }\n\n  /**\n     * Add a memory entry to the agent's memory\n     */\n  public async addMemory(entry: Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry> {\n    try {\n      const agent = this.agent;\n      if (!agent) {\n        throw new Error('Agent reference is no longer available');\n      }\n\n      // Create memory entry with agent context\n      const memory = await memoryManager.addMemory({\n        content: entry.content,\n        metadata: {\n          ...entry.metadata,\n          agentId: agent.id,\n          agentName: agent.name\n        }\n      });\n\n      logger.debug(`Added memory to agent: ${entry.content.substring(0, 50)}...`);\n      return memory;\n    } catch (error) {\n      logger.error('Failed to add memory to agent:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Add a message to the agent's memory\n     */\n  public async addMessage(role: 'user' | 'assistant', content: string): Promise<void> {\n    try {\n      const agent = this.agent;\n      if (!agent) {\n        logger.warn('Cannot add message: Agent reference is no longer available');\n        return;\n      }\n\n      // Create memory entry\n      const memory = await memoryManager.addMemory({\n        content,\n        metadata: {\n          source: 'conversation' as MemorySource,\n          type: role === 'user' ? 'human' as MemoryType : 'ai' as MemoryType,\n          agentId: agent.id,\n          agentName: agent.name\n        }\n      });\n\n      // Add to conversation history\n      this.conversationHistory.push(memory);\n\n      // Trim conversation history if needed\n      const maxHistorySize = getConfig<number>('memory.conversationHistorySize', 100);\n      if (this.conversationHistory.length > maxHistorySize) {\n        this.conversationHistory = this.conversationHistory.slice(-maxHistorySize);\n      }\n\n      logger.debug(`Added ${role} message to agent memory: ${content.substring(0, 50)}...`);\n    } catch (error) {\n      logger.error('Failed to add message to agent memory:', error);\n    }\n  }\n\n  /**\n     * Get conversation history\n     */\n  public getConversationHistory(): MemoryEntry[] {\n    return [...this.conversationHistory];\n  }\n\n  /**\n     * Get relevant memories for a query\n     */\n  public async getRelevantMemories(query: string): Promise<MemoryEntry[]> {\n    try {\n      const agent = this.agent;\n      if (!agent) {\n        logger.warn('Cannot get relevant memories: Agent reference is no longer available');\n        return [];\n      }\n\n      // Check if memory is enabled\n      const memoryEnabled = getConfig<boolean>('memory.enabled', true);\n      if (!memoryEnabled) {\n        return [];\n      }\n\n      // Search for relevant memories\n      this.relevantMemories = await memoryManager.searchSimilarMemories(query, {\n        limit: getConfig<number>('memory.contextWindowSize', 5),\n        filter: {\n          source: 'conversation',\n          agentId: agent.id\n        }\n      });\n\n      return this.relevantMemories;\n    } catch (error) {\n      logger.error('Failed to get relevant memories:', error);\n      return [];\n    }\n  }\n\n  /**\n     * Clear conversation history\n     */\n  public clearConversationHistory(): void {\n    this.conversationHistory = [];\n  }\n\n  /**\n     * Format memories for inclusion in prompts\n     */\n  public formatMemoriesForPrompt(memories: MemoryEntry[]): string {\n    if (memories.length === 0) {\n      return '';\n    }\n\n    const formattedMemories = memories.map(memory => {\n      const role = memory.metadata.type === 'human' ? 'User' : 'Assistant';\n      return `${role}: ${memory.content}`;\n    }).join('\\n\\n');\n\n    return `\\n\\nRelevant conversation history:\\n${formattedMemories}\\n\\n`;\n  }\n\n  /**\n     * Get a summary of the agent's memory\n     */\n  public async getMemorySummary(): Promise<string> {\n    try {\n      const agent = this.agent;\n      if (!agent) {\n        logger.warn('Cannot get memory summary: Agent reference is no longer available');\n        return 'Agent reference is no longer available';\n      }\n\n      // Get all memories for this agent\n      const memories = await memoryManager.searchMemories({\n        query: '',\n        limit: 1000,\n        filter: {\n          agentId: agent.id\n        }\n      });\n\n      if (memories.length === 0) {\n        return 'No memories available.';\n      }\n\n      // Count by type\n      const typeCounts: Record<string, number> = {};\n      for (const memory of memories) {\n        const type = memory.metadata.type;\n        typeCounts[type] = (typeCounts[type] || 0) + 1;\n      }\n\n      // Format summary\n      const summary = [\n        `Total memories: ${memories.length}`,\n        'Memory types:',\n        ...Object.entries(typeCounts).map(([type, count]) => `- ${type}: ${count}`)\n      ].join('\\n');\n\n      return summary;\n    } catch (error) {\n      logger.error('Failed to get memory summary:', error);\n      return 'Failed to get memory summary.';\n    }\n  }\n}\n\n/**\n * Get agent memory for an agent\n */\nconst agentMemories = new WeakMap<Agent, AgentMemory>();\n\nexport function getAgentMemory(agent: Agent): AgentMemory {\n  if (!agent) {\n    throw new Error('Agent cannot be null or undefined');\n  }\n\n  if (!agentMemories.has(agent)) {\n    agentMemories.set(agent, new AgentMemory(agent));\n  }\n\n  const memory = agentMemories.get(agent);\n  if (!memory) {\n    throw new Error('Failed to create or retrieve agent memory');\n  }\n\n  return memory;\n}\n"]}