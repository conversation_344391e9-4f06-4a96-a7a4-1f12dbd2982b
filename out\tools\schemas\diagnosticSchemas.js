"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagnosticResult = exports.DiagnosticInput = exports.DiagnosticSeverityEnum = void 0;
const zod_1 = require("zod");
exports.DiagnosticSeverityEnum = zod_1.z.enum(['error', 'warning', 'info', 'hint']);
exports.DiagnosticInput = zod_1.z.object({
    filePath: zod_1.z.string().optional(),
    severity: zod_1.z.union([exports.DiagnosticSeverityEnum, zod_1.z.array(exports.DiagnosticSeverityEnum)]).optional(),
    code: zod_1.z.union([zod_1.z.string(), zod_1.z.array(zod_1.z.string())]).optional(),
    source: zod_1.z.union([zod_1.z.string(), zod_1.z.array(zod_1.z.string())]).optional(),
    message: zod_1.z.string().optional(),
    limit: zod_1.z.number().min(1).optional()
});
exports.DiagnosticResult = zod_1.z.object({
    severity: exports.DiagnosticSeverityEnum,
    code: zod_1.z.string().optional(),
    source: zod_1.z.string().optional(),
    message: zod_1.z.string(),
    file: zod_1.z.string(),
    line: zod_1.z.number(),
    column: zod_1.z.number(),
    endLine: zod_1.z.number().optional(),
    endColumn: zod_1.z.number().optional(),
    relatedInformation: zod_1.z.array(zod_1.z.object({
        message: zod_1.z.string(),
        file: zod_1.z.string(),
        line: zod_1.z.number(),
        column: zod_1.z.number()
    })).optional()
});
//# sourceMappingURL=diagnosticSchemas.js.map