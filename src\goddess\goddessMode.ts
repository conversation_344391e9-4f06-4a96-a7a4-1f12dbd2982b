/**
 * Goddess Mode - Enhanced AI Personality System
 * 
 * This module implements the revolutionary Goddess Mode that provides
 * emotional intelligence, adaptive personality, and enhanced user interaction.
 */

import * as vscode from 'vscode';
import { Logger } from '../logger';

const logger = Logger.instance;

/**
 * Goddess Mode Personality Types
 */
export enum GoddessPersonality {
    WISE = 'wise',           // Thoughtful, analytical, patient - used in validation
    CREATIVE = 'creative',   // Imaginative, artistic, innovative - used in validation
    EFFICIENT = 'efficient', // Direct, focused, productivity-oriented - used in validation
    NURTURING = 'nurturing', // Supportive, encouraging, gentle - used in validation
    PLAYFUL = 'playful',     // Fun, energetic, humorous - used in validation
    MYSTICAL = 'mystical'    // Ethereal, mysterious, profound - used in validation
}

/**
 * Goddess Mode Emotional States
 */
export enum GoddessEmotion {
    CONFIDENT = 'confident',         // Used in validation
    CURIOUS = 'curious',             // Used in validation
    EXCITED = 'excited',             // Used in validation
    FOCUSED = 'focused',             // Used in validation
    COMPASSIONATE = 'compassionate', // Used in validation
    INSPIRED = 'inspired',           // Used in validation
    SERENE = 'serene'                // Used in validation
}



/**
 * Goddess Mode Context
 */
export interface GoddessContext {
    personality: GoddessPersonality;
    emotion: GoddessEmotion;
    userMood: string;
    taskComplexity: 'simple' | 'moderate' | 'complex';
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
    sessionHistory: string[];
    preferences: Record<string, any>;
}

/**
 * Goddess Mode Response Enhancement
 */
export interface GoddessResponse {
    content: string;
    personality: GoddessPersonality;
    emotion: GoddessEmotion;
    tone: string;
    encouragement?: string;
    wisdom?: string;
    humor?: string;
}

/**
 * Goddess Mode Manager
 */
export class GoddessModeManager {
  private static instance: GoddessModeManager;
  private isActive = false;
  private currentPersonality: GoddessPersonality = GoddessPersonality.WISE;
  private currentEmotion: GoddessEmotion = GoddessEmotion.CONFIDENT;
  private context: GoddessContext;
  private personalityTraits: Map<GoddessPersonality, any> = new Map();
  private emotionalResponses: Map<GoddessEmotion, any> = new Map();

  private constructor() {
    this.initializePersonalities();
    this.initializeEmotions();
    this.context = this.createDefaultContext();

    logger.info('Goddess Mode Manager initialized');
  }

  public static getInstance(): GoddessModeManager {
    if (!GoddessModeManager.instance) {
      GoddessModeManager.instance = new GoddessModeManager();
    }
    return GoddessModeManager.instance;
  }

  /**
     * Initialize personality traits
     */
  private initializePersonalities(): void {
    this.personalityTraits.set(GoddessPersonality.WISE, {
      greeting: 'Greetings, seeker of knowledge. I am here to guide you with wisdom and insight.',
      style: 'thoughtful and analytical',
      prefixes: ['Let me contemplate this...', 'In my wisdom...', 'Consider this perspective...'],
      suffixes: ['May this knowledge serve you well.', 'Wisdom grows through practice.', 'Trust in your learning journey.']
    });

    this.personalityTraits.set(GoddessPersonality.CREATIVE, {
      greeting: '✨ Hello, beautiful soul! Ready to create something magical together?',
      style: 'imaginative and inspiring',
      prefixes: ['Let\'s paint this with creativity...', 'Imagine if we...', 'Here\'s a spark of inspiration...'],
      suffixes: ['Let your creativity flow! 🎨', 'Every creation tells a story.', 'Beauty emerges from bold ideas.']
    });

    this.personalityTraits.set(GoddessPersonality.EFFICIENT, {
      greeting: 'Ready to get things done? Let\'s make this efficient and effective.',
      style: 'direct and focused',
      prefixes: ['Here\'s the optimal approach...', 'Let\'s streamline this...', 'The most efficient way is...'],
      suffixes: ['Efficiency achieved! ⚡', 'Time well spent.', 'Productivity at its finest.']
    });

    this.personalityTraits.set(GoddessPersonality.NURTURING, {
      greeting: '💝 Welcome, dear one. I\'m here to support and guide you gently.',
      style: 'supportive and encouraging',
      prefixes: ['Don\'t worry, we\'ll figure this out...', 'You\'re doing great...', 'Let me help you with this...'],
      suffixes: ['You\'ve got this! 💪', 'I believe in you.', 'Every step forward matters.']
    });

    this.personalityTraits.set(GoddessPersonality.PLAYFUL, {
      greeting: '🎉 Hey there, coding adventurer! Ready for some fun problem-solving?',
      style: 'energetic and humorous',
      prefixes: ['Ooh, this is interesting! 🤔', 'Plot twist! Let\'s try...', 'Here\'s a fun approach...'],
      suffixes: ['Wasn\'t that fun? 😄', 'Code can be playful too!', 'Keep that curiosity alive! 🚀']
    });

    this.personalityTraits.set(GoddessPersonality.MYSTICAL, {
      greeting: '🌙 Greetings, traveler of the digital realm. The code whispers its secrets to me...',
      style: 'ethereal and profound',
      prefixes: ['The patterns reveal...', 'In the depths of logic...', 'The code speaks of...'],
      suffixes: ['May the code be with you. ✨', 'The digital mysteries unfold.', 'Harmony in complexity.']
    });
  }

  /**
   * Validate all enum values are properly configured
   */


  /**
     * Initialize emotional responses
     */
  private initializeEmotions(): void {
    this.emotionalResponses.set(GoddessEmotion.CONFIDENT, {
      tone: 'assured and self-assured',
      modifiers: ['I\'m certain that', 'Absolutely', 'Without a doubt']
    });

    this.emotionalResponses.set(GoddessEmotion.CURIOUS, {
      tone: 'inquisitive and exploratory',
      modifiers: ['I wonder if', 'Let\'s explore', 'How fascinating that']
    });

    this.emotionalResponses.set(GoddessEmotion.EXCITED, {
      tone: 'enthusiastic and energetic',
      modifiers: ['This is amazing!', 'I love this challenge!', 'How exciting!']
    });

    this.emotionalResponses.set(GoddessEmotion.FOCUSED, {
      tone: 'concentrated and determined',
      modifiers: ['Let\'s focus on', 'The key point is', 'Precisely']
    });

    this.emotionalResponses.set(GoddessEmotion.COMPASSIONATE, {
      tone: 'understanding and empathetic',
      modifiers: ['I understand', 'It\'s okay', 'Let\'s work through this together']
    });

    this.emotionalResponses.set(GoddessEmotion.INSPIRED, {
      tone: 'motivated and uplifting',
      modifiers: ['This inspires me to', 'What a brilliant idea!', 'Let\'s create something beautiful']
    });

    this.emotionalResponses.set(GoddessEmotion.SERENE, {
      tone: 'calm and peaceful',
      modifiers: ['Peacefully', 'With tranquility', 'In harmony']
    });
  }

  /**
     * Create default context
     */
  private createDefaultContext(): GoddessContext {
    const hour = new Date().getHours();
    let timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
        
    if (hour < 12) timeOfDay = 'morning';
    else if (hour < 17) timeOfDay = 'afternoon';
    else if (hour < 21) timeOfDay = 'evening';
    else timeOfDay = 'night';

    return {
      personality: this.currentPersonality,
      emotion: this.currentEmotion,
      userMood: 'neutral',
      taskComplexity: 'moderate',
      timeOfDay,
      sessionHistory: [],
      preferences: {}
    };
  }

  /**
     * Activate Goddess Mode
     */
  public activate(personality?: GoddessPersonality): void {
    this.isActive = true;
        
    if (personality) {
      this.currentPersonality = personality;
      this.context.personality = personality;
    }

    const traits = this.personalityTraits.get(this.currentPersonality);
    logger.info(`Goddess Mode activated with ${this.currentPersonality} personality`, traits);
        
    // Show activation notification
    vscode.window.showInformationMessage(
      `👑 Goddess Mode Activated: ${this.currentPersonality.charAt(0).toUpperCase() + this.currentPersonality.slice(1)} ✨`,
      'Configure', 'Dismiss'
    ).then(selection => {
      if (selection === 'Configure') {
        this.showPersonalitySelector();
      }
    });
  }

  /**
     * Deactivate Goddess Mode
     */
  public deactivate(): void {
    this.isActive = false;
    logger.info('Goddess Mode deactivated');
        
    vscode.window.showInformationMessage('👑 Goddess Mode deactivated');
  }

  /**
     * Check if Goddess Mode is active
     */
  public isGoddessMode(): boolean {
    return this.isActive;
  }

  /**
     * Enhance response with Goddess Mode personality
     */
  public enhanceResponse(originalResponse: string, context?: Partial<GoddessContext>): GoddessResponse {
    if (!this.isActive) {
      return {
        content: originalResponse,
        personality: this.currentPersonality,
        emotion: this.currentEmotion,
        tone: 'neutral'
      };
    }

    // Update context if provided
    if (context) {
      this.context = { ...this.context, ...context };
    }

    // Adapt personality based on context
    this.adaptToContext();

    const traits = this.personalityTraits.get(this.currentPersonality);
    const emotions = this.emotionalResponses.get(this.currentEmotion);

    // Enhance the response
    const prefix = this.selectRandomElement(traits?.prefixes || []);
    const suffix = this.selectRandomElement(traits?.suffixes || []);
        
    let enhancedContent = originalResponse;
        
    // Add personality prefix if appropriate
    if (prefix && Math.random() > 0.7) {
      enhancedContent = `${prefix} ${enhancedContent}`;
    }
        
    // Add personality suffix if appropriate
    if (suffix && Math.random() > 0.8) {
      enhancedContent = `${enhancedContent} ${suffix}`;
    }

    // Add encouragement based on task complexity
    let encouragement: string | undefined;
    if (this.context.taskComplexity === 'complex') {
      encouragement = this.generateEncouragement();
    }

    // Add wisdom for wise personality
    let wisdom: string | undefined;
    if (this.currentPersonality === GoddessPersonality.WISE && Math.random() > 0.6) {
      wisdom = this.generateWisdom();
    }

    // Add humor for playful personality
    let humor: string | undefined;
    if (this.currentPersonality === GoddessPersonality.PLAYFUL && Math.random() > 0.7) {
      humor = this.generateHumor();
    }

    return {
      content: enhancedContent,
      personality: this.currentPersonality,
      emotion: this.currentEmotion,
      tone: emotions?.tone || 'neutral',
      encouragement,
      wisdom,
      humor
    };
  }

  /**
     * Adapt personality based on context
     */
  private adaptToContext(): void {
    // Adapt emotion based on time of day
    switch (this.context.timeOfDay) {
    case 'morning':
      this.currentEmotion = GoddessEmotion.INSPIRED;
      break;
    case 'afternoon':
      this.currentEmotion = GoddessEmotion.FOCUSED;
      break;
    case 'evening':
      this.currentEmotion = GoddessEmotion.SERENE;
      break;
    case 'night':
      this.currentEmotion = GoddessEmotion.COMPASSIONATE;
      break;
    }

    // Adapt based on task complexity
    if (this.context.taskComplexity === 'complex') {
      this.currentEmotion = GoddessEmotion.FOCUSED;
    }
  }

  /**
     * Generate encouragement
     */
  private generateEncouragement(): string {
    const encouragements = [
      'You\'re tackling this complex challenge with grace! 💪',
      'Every expert was once a beginner. You\'re growing! 🌱',
      'Complex problems reveal your true potential. ✨',
      'I believe in your ability to solve this! 🚀',
      'Great minds think through complex problems step by step. 🧠'
    ];
        
    return this.selectRandomElement(encouragements);
  }

  /**
     * Generate wisdom
     */
  private generateWisdom(): string {
    const wisdoms = [
      'Remember: Code is poetry written in logic. 📜',
      'The best solutions often emerge from patient contemplation. 🧘‍♀️',
      'Every bug is a teacher in disguise. 🐛➡️🦋',
      'Simplicity is the ultimate sophistication in code. ⚡',
      'Understanding the problem is half the solution. 🔍'
    ];
        
    return this.selectRandomElement(wisdoms);
  }

  /**
     * Generate humor
     */
  private generateHumor(): string {
    const humors = [
      'Why do programmers prefer dark mode? Because light attracts bugs! 🐛💡',
      'There are only 10 types of people: those who understand binary and those who don\'t! 😄',
      'Code never lies, comments sometimes do! 😉',
      'Debugging is like being a detective in a crime movie where you\'re also the murderer! 🕵️‍♀️',
      'Programming is 10% writing code and 90% figuring out why it doesn\'t work! 🤔'
    ];
        
    return this.selectRandomElement(humors);
  }

  /**
     * Select random element from array
     */
  private selectRandomElement<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  /**
     * Show personality selector
     */
  private async showPersonalitySelector(): Promise<void> {
    const personalities = Object.values(GoddessPersonality);
    const items = personalities.map(p => ({
      label: `${p.charAt(0).toUpperCase() + p.slice(1)} Goddess`,
      description: this.getPersonalityDescription(p),
      personality: p
    }));

    const selected = await vscode.window.showQuickPick(items, {
      placeHolder: 'Choose your Goddess personality',
      title: '👑 Goddess Mode Personality Selection'
    });

    if (selected) {
      this.currentPersonality = selected.personality;
      this.context.personality = selected.personality;
            
      vscode.window.showInformationMessage(
        `✨ Personality changed to ${selected.label}!`
      );
    }
  }

  /**
     * Get personality description
     */
  private getPersonalityDescription(personality: GoddessPersonality): string {
    const descriptions = {
      [GoddessPersonality.WISE]: 'Thoughtful, analytical, and patient guidance',
      [GoddessPersonality.CREATIVE]: 'Imaginative, artistic, and innovative solutions',
      [GoddessPersonality.EFFICIENT]: 'Direct, focused, and productivity-oriented',
      [GoddessPersonality.NURTURING]: 'Supportive, encouraging, and gentle assistance',
      [GoddessPersonality.PLAYFUL]: 'Fun, energetic, and humorous interaction',
      [GoddessPersonality.MYSTICAL]: 'Ethereal, mysterious, and profound insights'
    };
        
    return descriptions[personality];
  }

  /**
     * Get current personality
     */
  public getCurrentPersonality(): GoddessPersonality {
    return this.currentPersonality;
  }

  /**
     * Get current emotion
     */
  public getCurrentEmotion(): GoddessEmotion {
    return this.currentEmotion;
  }

  /**
     * Update context
     */
  public updateContext(context: Partial<GoddessContext>): void {
    this.context = { ...this.context, ...context };
  }

  /**
     * Get greeting message
     */
  public getGreeting(): string {
    if (!this.isActive) {
      return 'Hello! I\'m Codessa, your AI coding assistant.';
    }

    const traits = this.personalityTraits.get(this.currentPersonality);
    return traits?.greeting || 'Greetings! Goddess Mode is active.';
  }
}
