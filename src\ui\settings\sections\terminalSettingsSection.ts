import * as vscode from 'vscode';

// Types for terminal settings
export type FontWeight =
  | 'normal'
  | 'bold'
  | '100'
  | '200'
  | '300'
  | '400'
  | '500'
  | '600'
  | '700'
  | '800'
  | '900';

export const STANDARD_COLORS = [
  { label: 'Default (Theme)', value: '' },
  { label: 'Black', value: '#000000' },
  { label: 'White', value: '#FFFFFF' },
  { label: 'Red', value: '#FF0000' },
  { label: 'Green', value: '#00FF00' },
  { label: 'Blue', value: '#0000FF' },
  { label: 'Yellow', value: '#FFFF00' },
  { label: 'Cyan', value: '#00FFFF' },
  { label: 'Magenta', value: '#FF00FF' },
  { label: 'Gray', value: '#808080' },
  { label: 'Dark Red', value: '#800000' },
  { label: 'Dark Green', value: '#008000' },
  { label: 'Dark Blue', value: '#000080' },
  { label: 'Dark Yellow', value: '#808000' },
  { label: 'Dark Cyan', value: '#008080' },
  { label: 'Dark Magenta', value: '#800080' },
  { label: 'Light Gray', value: '#C0C0C0' },
  { label: 'Dark Gray', value: '#404040' },
  { label: 'Custom...', value: 'custom' },
];

export const STANDARD_FONTS = [
  'Consolas',
  'Courier New',
  'Droid Sans Mono',
  'Fira Code',
  'Inconsolata',
  'JetBrains Mono',
  'Menlo',
  'Monaco',
  'Roboto Mono',
  'Source Code Pro',
  'Ubuntu Mono',
  'Cascadia Code',
  'Hack',
  'Iosevka',
  'Fira Mono',
  'DejaVu Sans Mono',
  'Liberation Mono',
  'PT Mono',
  'Space Mono',
  'Anonymous Pro',
];

export enum TerminalTheme {
  System = 'system',
  Light = 'light',
  Dark = 'dark',
  HighContrast = 'highContrast',
}

export enum TerminalViewType {
  SideView = 'sideView',
  DefaultTerminal = 'defaultTerminal',
  Both = 'both',
}

export interface TerminalColorTheme {
  foreground?: string;
  background?: string;
  cursor?: string;
  selection?: string;
  black?: string;
  red?: string;
  green?: string;
  yellow?: string;
  blue?: string;
  magenta?: string;
  cyan?: string;
  white?: string;
  brightBlack?: string;
  brightRed?: string;
  brightGreen?: string;
  brightYellow?: string;
  brightBlue?: string;
  brightMagenta?: string;
  brightCyan?: string;
  brightWhite?: string;
}

export interface TerminalSettings {
  // Display settings
  viewType: TerminalViewType;
  showTerminal: boolean;

  // Font settings
  fontFamily: string;
  fontSize: number;
  fontWeight: FontWeight;

  // Color settings
  theme: TerminalTheme;
  foregroundColor: string;
  backgroundColor: string;
  cursorColor: string;
  selectionColor: string;
  customTheme: TerminalColorTheme;
}

export interface TerminalSettings {
  // Display settings
  viewType: TerminalViewType;
  showTerminal: boolean;

  // Font settings
  fontFamily: string;
  fontSize: number;
  fontWeight: FontWeight;

  // Color settings
  theme: TerminalTheme;
  foregroundColor: string;
  backgroundColor: string;
  cursorColor: string;
  selectionColor: string;
  customTheme: TerminalColorTheme;
}

export class TerminalSettingsSection implements vscode.Disposable {
  private readonly sectionName = 'codessa.terminal';
  private _onDidChangeSettings = new vscode.EventEmitter<void>();
  readonly onDidChangeSettings = this._onDidChangeSettings.event;

  private readonly defaultSettings: TerminalSettings = {
    // Display settings
    viewType: TerminalViewType.Both,
    showTerminal: true,

    // Font settings
    fontFamily:
      '"Courier New", Consolas, "Droid Sans Mono", "Fira Code", "Inconsolata", "Source Code Pro", "Ubuntu Mono", monospace',
    fontSize: 14,
    fontWeight: 'normal',

    // Color settings
    theme: TerminalTheme.System,
    foregroundColor: '',
    backgroundColor: '',
    cursorColor: '',
    selectionColor: '',
    customTheme: {},
  };

  private disposables: vscode.Disposable[] = [];

  constructor() {
    // Listen for configuration changes
    this.disposables.push(
      vscode.workspace.onDidChangeConfiguration((e) => {
        if (e.affectsConfiguration(this.sectionName)) {
          this._onDidChangeSettings.fire();
        }
      }),
    );
  }

  dispose() {
    this._onDidChangeSettings.dispose();
    this.disposables.forEach((d) => d.dispose());
  }

  public getSettings(): TerminalSettings {
    const config = vscode.workspace.getConfiguration(this.sectionName);
    const settings = this.defaultSettings;

    // Get all settings with their default values
    const result: TerminalSettings = {
      viewType: config.get<TerminalViewType>('viewType', settings.viewType),
      showTerminal: config.get<boolean>('showTerminal', settings.showTerminal),
      fontFamily: config.get<string>('fontFamily', settings.fontFamily),
      fontSize: config.get<number>('fontSize', settings.fontSize),
      fontWeight: config.get<FontWeight>('fontWeight', settings.fontWeight),
      theme: config.get<TerminalTheme>('theme', settings.theme),
      foregroundColor: config.get<string>(
        'foregroundColor',
        settings.foregroundColor,
      ),
      backgroundColor: config.get<string>(
        'backgroundColor',
        settings.backgroundColor,
      ),
      cursorColor: config.get<string>('cursorColor', settings.cursorColor),
      selectionColor: config.get<string>(
        'selectionColor',
        settings.selectionColor,
      ),
      customTheme:
        config.get<TerminalColorTheme>('customTheme', settings.customTheme) ||
        {},
    };

    return result;
  }

  public getSetting<K extends keyof TerminalSettings>(
    key: K,
  ): TerminalSettings[K] {
    const config = vscode.workspace.getConfiguration(this.sectionName);
    return config.get(key, this.defaultSettings[key]) as TerminalSettings[K];
  }

  public async updateSettings(
    settings: Partial<TerminalSettings>,
  ): Promise<void> {
    const config = vscode.workspace.getConfiguration(this.sectionName);

    // Update each setting individually
    for (const [key, value] of Object.entries(settings)) {
      if (value !== undefined) {
        await config.update(key, value, vscode.ConfigurationTarget.Global);
      }
    }

    this._onDidChangeSettings.fire();
  }

  public async updateSetting<K extends keyof TerminalSettings>(
    key: K,
    value: TerminalSettings[K],
    target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Global,
  ): Promise<void> {
    const config = vscode.workspace.getConfiguration(this.sectionName);
    await config.update(key, value, target);
    this._onDidChangeSettings.fire();
  }

  public async resetToDefault<K extends keyof TerminalSettings>(
    key: K,
  ): Promise<void> {
    const config = vscode.workspace.getConfiguration(this.sectionName);
    await config.update(key, undefined, true);
    this._onDidChangeSettings.fire();
  }

  public async resetAllToDefault(): Promise<void> {
    const config = vscode.workspace.getConfiguration(this.sectionName);
    for (const key of Object.keys(this.defaultSettings)) {
      await config.update(key, undefined, true);
    }
    this._onDidChangeSettings.fire();
  }

  public async toggleViewType(): Promise<void> {
    const current = this.getSettings();
    let nextViewType: TerminalViewType;

    switch (current.viewType) {
      case TerminalViewType.SideView:
        nextViewType = TerminalViewType.DefaultTerminal;
        break;
      case TerminalViewType.DefaultTerminal:
        nextViewType = TerminalViewType.Both;
        break;
      case TerminalViewType.Both:
      default:
        nextViewType = TerminalViewType.SideView;
    }

    await this.updateSettings({ viewType: nextViewType });
  }

  public async showColorPicker(
    currentColor: string,
  ): Promise<string | undefined> {
    const color = await vscode.window.showInputBox({
      value: currentColor || '#000000',
      prompt: 'Enter a color in hex format (e.g., #FF0000 for red)',
      validateInput: (value) => {
        if (!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
          return 'Please enter a valid hex color (e.g., #FF0000 or #F00)';
        }
        return null;
      },
    });

    return color || undefined;
  }

  public async showFontPicker(
    currentFont: string,
  ): Promise<string | undefined> {
    const quickPick = vscode.window.createQuickPick();
    quickPick.placeholder = 'Select a font or type to search';
    quickPick.items = STANDARD_FONTS.map((font) => ({
      label: font,
      description: font === currentFont ? 'Current font' : '',
      alwaysShow: font === currentFont,
    }));

    quickPick.onDidChangeValue((value) => {
      if (value && !STANDARD_FONTS.includes(value)) {
        quickPick.items = [
          { label: value, description: 'Custom font' },
          ...STANDARD_FONTS.map((font) => ({
            label: font,
            description: font === currentFont ? 'Current font' : '',
          })),
        ];
      } else {
        quickPick.items = STANDARD_FONTS.map((font) => ({
          label: font,
          description: font === currentFont ? 'Current font' : '',
        }));
      }
    });

    const selected = await new Promise<vscode.QuickPickItem | undefined>(
      (resolve) => {
        quickPick.onDidAccept(() => {
          resolve(quickPick.selectedItems[0]);
          quickPick.hide();
        });
        quickPick.onDidHide(() => resolve(undefined));
        quickPick.show();
      },
    );

    quickPick.dispose();
    return selected?.label;
  }

  public getCurrentThemeColors() {
    const colorTheme = vscode.window.activeColorTheme;
    const themeKind =
      colorTheme.kind === 1
        ? 'light'
        : colorTheme.kind === 2
          ? 'dark'
          : 'highContrast';

    // Return theme color identifiers for use in webview CSS
    return {
      foreground: 'var(--vscode-terminal-foreground)',
      background: 'var(--vscode-terminal-background)',
      cursor: 'var(--vscode-terminalCursor-foreground)',
      selection: 'var(--vscode-terminal-selectionBackground)',
      theme: themeKind,
    };
  }

  public async showSettings(): Promise<void> {
    const settings = this.getSettings();
    const themeColors = this.getCurrentThemeColors();

    const categoryItems: vscode.QuickPickItem[] = [
      {
        label: '$(symbol-color) Appearance',
        description: 'Font, colors, and theme settings',
      },
      { label: '$(gear) Advanced', description: 'Advanced terminal settings' },
      {
        label: '$(discard) Reset All',
        description: 'Reset all settings to default',
      },
    ];

    const selectedCategory = await vscode.window.showQuickPick(categoryItems, {
      placeHolder: 'Select a settings category',
    });

    if (!selectedCategory) return;

    if (selectedCategory.label.includes('Appearance')) {
      await this.showAppearanceSettings(settings, themeColors);
    } else if (selectedCategory.label.includes('Advanced')) {
      await this.showAdvancedSettings(settings);
    } else if (selectedCategory.label.includes('Reset')) {
      await this.confirmResetAll();
    }
  }

  private async showAppearanceSettings(
    settings: TerminalSettings,
    themeColors: any,
  ): Promise<void> {
    const items: vscode.QuickPickItem[] = [
      {
        label: 'Font Family',
        description: settings.fontFamily,
        detail: 'Change the terminal font family',
        alwaysShow: true,
      },
      {
        label: 'Font Size',
        description: settings.fontSize.toString(),
        detail: 'Change the terminal font size',
        alwaysShow: true,
      },
      {
        label: 'Font Weight',
        description: settings.fontWeight,
        detail: 'Change the terminal font weight',
        alwaysShow: true,
      },
      {
        label: 'Text Color',
        description: settings.foregroundColor || 'Theme Default',
        detail: `Change the terminal text color (Default: ${themeColors.foreground})`,
        alwaysShow: true,
      },
      {
        label: 'Background Color',
        description: settings.backgroundColor || 'Theme Default',
        detail: `Change the terminal background color (Default: ${themeColors.background})`,
        alwaysShow: true,
      },
      {
        label: 'Cursor Color',
        description: settings.cursorColor || 'Theme Default',
        detail: `Change the terminal cursor color (Default: ${themeColors.cursor})`,
        alwaysShow: true,
      },
      {
        label: 'Color Theme',
        description:
          settings.theme.charAt(0).toUpperCase() + settings.theme.slice(1),
        detail: 'Change the terminal color theme',
        alwaysShow: true,
      },
    ];

    const selected = await vscode.window.showQuickPick(items, {
      placeHolder: 'Select a setting to change',
      matchOnDetail: true,
    });

    if (!selected) return;

    switch (selected.label) {
      case 'Font Family':
        await this.handleFontFamilyChange(settings);
        break;
      case 'Font Size':
        await this.handleFontSizeChange(settings);
        break;
      case 'Font Weight':
        await this.handleFontWeightChange(settings);
        break;
      case 'Text Color':
        await this.handleColorChange('foregroundColor');
        break;
      case 'Background Color':
        await this.handleColorChange('backgroundColor');
        break;
      case 'Cursor Color':
        await this.handleColorChange('cursorColor');
        break;
      case 'Color Theme':
        await this.handleThemeChange(settings);
        break;
    }

    // Show the settings again after making a change
    await this.showAppearanceSettings(this.getSettings(), themeColors);
  }

  private async showAdvancedSettings(
    settings: TerminalSettings,
  ): Promise<void> {
    const items: vscode.QuickPickItem[] = [
      {
        label: 'View Type',
        description:
          settings.viewType === 'sideView'
            ? 'Side View'
            : settings.viewType === 'defaultTerminal'
              ? 'Default Terminal'
              : 'Both',
        detail: 'Change where the terminal output is displayed',
        alwaysShow: true,
      },
      {
        label: 'Show Terminal',
        description: settings.showTerminal ? 'Yes' : 'No',
        detail: 'Toggle terminal visibility',
        alwaysShow: true,
      },
      {
        label: 'Reset to Defaults',
        description: 'Reset all settings to their default values',
        detail: 'This will reset all terminal settings',
        alwaysShow: true,
      },
    ];

    const selected = await vscode.window.showQuickPick(items, {
      placeHolder: 'Select an advanced setting to change',
      matchOnDetail: true,
    });

    if (!selected) return;

    switch (selected.label) {
      case 'View Type':
        await this.toggleViewType();
        break;
      case 'Show Terminal':
        await this.updateSetting('showTerminal', !settings.showTerminal);
        break;
      case 'Reset to Defaults':
        await this.confirmResetAll();
        break;
    }

    // Show the settings again after making a change
    await this.showAdvancedSettings(this.getSettings());
  }

  private async confirmResetAll(): Promise<void> {
    const confirm = await vscode.window.showWarningMessage(
      'Are you sure you want to reset all terminal settings to their default values?',
      { modal: true },
      'Reset All',
      'Cancel',
    );

    if (confirm === 'Reset All') {
      await this.resetAllToDefault();
      vscode.window.showInformationMessage(
        'Terminal settings have been reset to default.',
      );
    }
  }

  private async handleFontFamilyChange(
    settings: TerminalSettings,
  ): Promise<void> {
    const newFont = await this.showFontPicker(settings.fontFamily);
    if (newFont !== undefined) {
      await this.updateSetting('fontFamily', newFont);
    }
  }

  private async handleFontSizeChange(
    settings: TerminalSettings,
  ): Promise<void> {
    const newSize = await vscode.window.showInputBox({
      value: settings.fontSize.toString(),
      prompt: 'Enter font size (8-32)',
      validateInput: (value) => {
        const size = parseInt(value, 10);
        if (isNaN(size) || size < 8 || size > 32) {
          return 'Please enter a number between 8 and 32';
        }
        return null;
      },
    });

    if (newSize) {
      await this.updateSetting('fontSize', parseInt(newSize, 10));
    }
  }

  private async handleFontWeightChange(
    settings: TerminalSettings,
  ): Promise<void> {
    const weights: FontWeight[] = [
      'normal',
      'bold',
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
    ];
    const selected = await vscode.window.showQuickPick(
      weights.map((weight) => ({
        label: weight,
        description: weight === settings.fontWeight ? 'Current' : '',
      })),
      { placeHolder: 'Select a font weight' },
    );

    if (selected) {
      await this.updateSetting('fontWeight', selected.label as FontWeight);
    }
  }

  private async handleColorChange(
    colorKey: keyof TerminalSettings,
  ): Promise<void> {
    const currentColor = this.getSettings()[colorKey] as string;
    const colorItems = STANDARD_COLORS.map((color) => ({
      label: color.label,
      description: color.value === currentColor ? 'Current' : '',
      detail: color.value ? `Color: ${color.value}` : 'Use theme default',
      value: color.value,
    }));

    const selected = await vscode.window.showQuickPick(colorItems, {
      placeHolder: `Select a ${colorKey
        .replace(/([A-Z])/g, ' $1')
        .toLowerCase()
        .trim()}`,
      matchOnDetail: true,
    });

    if (!selected) return;

    if (selected.value === 'custom') {
      const customColor = await this.showColorPicker(currentColor);
      if (customColor) {
        await this.updateSetting(colorKey, customColor);
      }
    } else if (selected.value === '') {
      await this.updateSetting(colorKey, '');
    } else if (selected.value) {
      await this.updateSetting(colorKey, selected.value);
    }
  }

  private async handleThemeChange(settings: TerminalSettings): Promise<void> {
    const themes = [
      { label: 'System', value: TerminalTheme.System },
      { label: 'Light', value: TerminalTheme.Light },
      { label: 'Dark', value: TerminalTheme.Dark },
      { label: 'High Contrast', value: TerminalTheme.HighContrast },
    ];

    const selected = await vscode.window.showQuickPick(
      themes.map((theme) => ({
        label: theme.label,
        description: theme.value === settings.theme ? 'Current' : '',
        value: theme.value,
      })),
      { placeHolder: 'Select a color theme' },
    );

    if (selected) {
      await this.updateSetting('theme', selected.value);
    }
  }
}

// Export a singleton instance
export const terminalSettings = new TerminalSettingsSection();
