import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../types/agent';
import * as vscode from 'vscode';
import { z } from 'zod';

export class BrowserPreviewTool implements ITool {
  readonly id = 'browserPreview';
  readonly name = 'Browser Preview';
  readonly description = 'Opens a browser preview for a local web server.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    url: z.string().describe('URL of the local web server (e.g. http://localhost:3000)')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      url: { type: 'string', description: 'URL of the local web server (e.g. http://localhost:3000)' }
    },
    required: ['url']
  };

  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    const url = input.url as string;
    if (!url) {
      return { success: false, error: '\'url\' is required.', toolId: this.id, actionName };
    }
    try {
      await vscode.env.openExternal(vscode.Uri.parse(url));
      return { success: true, output: `Opened browser preview for ${url}`, toolId: this.id, actionName };
    } catch (error: any) {
      return { success: false, error: `Failed to open browser preview: ${error.message || error}`, toolId: this.id, actionName };
    }
  }
}

export const browserPreviewTool = new BrowserPreviewTool();
