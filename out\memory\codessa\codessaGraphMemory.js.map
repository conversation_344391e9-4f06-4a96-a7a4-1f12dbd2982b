{"version": 3, "file": "codessaGraphMemory.js", "sourceRoot": "", "sources": ["../../../src/memory/codessa/codessaGraphMemory.ts"], "names": [], "mappings": ";;;AAAA,yCAAsC;AACtC,sEAO6C;AAI7C,qDAAkD;AAelD;;;GAGG;AACH,MAAa,kBAAkB;IACrB,KAAK,CAAyB;IAC9B,aAAa,CAAU;IACvB,KAAK,CAA4B;IACjC,WAAW,GAAG,KAAK,CAAC;IACpB,cAAc,CAAwB;IAE9C,YAAY,cAAqC;QAC/C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;YAEvD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,KAAK,GAAG;gBACX,MAAM,EAAE,KAAK,EAAE,QAAsD,EAAE,OAAuE,EAAE,EAAE;oBAChJ,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC;4BACrC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAA2C,EAAE,EAAE;gCACnE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;oCACX,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;wCACvB,OAAO,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;oCAC9B,CAAC;yCAAM,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;wCAC3B,OAAO,cAAc,CAAC,CAAC,OAAO,EAAE,CAAC;oCACnC,CAAC;yCAAM,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wCAC/B,OAAO,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;oCAChC,CAAC;gCACH,CAAC;gCACD,OAAO,SAAS,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;4BACtC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;4BACb,OAAO,EAAE,SAAS;4BAClB,OAAO,EAAE;gCACP,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;gCACxC,SAAS,EAAE,OAAO,EAAE,SAAS;gCAC7B,aAAa,EAAE,OAAO,EAAE,IAAI;6BAC7B;4BACD,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEH,OAAO,IAAI,wBAAS,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;oBAC7C,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;wBAC7C,MAAM,KAAK,CAAC;oBACd,CAAC;gBACH,CAAC;gBACD,0DAA0D;gBAC1D,QAAQ,EAAE,GAAG,EAAE,CAAC,QAAQ;gBACxB,UAAU,EAAE,GAAG,EAAE,CAAC,MAAM;aACG,CAAC;YAE9B,sBAAsB;YACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iBAAiB;QACvB,qBAAqB;QACrB,MAAM,KAAK,GAAG,IAAI,yBAAU,CAAC;YAC3B,QAAQ,EAAE;gBACR,QAAQ,EAAE;oBACR,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,GAAiD,EAAE,CAAC,EAAE;iBAChE;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,GAAa,EAAE,CAAC,EAAE;iBAC5B;gBACD,QAAQ,EAAE;oBACR,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,GAAkB,EAAE,CAAC,EAAE;iBACjC;gBACD,WAAW,EAAE;oBACX,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,GAAW,EAAE,CAAC,EAAE;iBAC1B;gBACD,UAAU,EAAE;oBACV,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,GAAW,EAAE,CAAC,EAAE;iBAC1B;aACF;SACF,CAAC,CAAC;QAEH,YAAY;QAEZ,gCAAgC;QAChC,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,KAAK,EAAE,KAAkB,EAAwB,EAAE;YACnF,IAAI,CAAC;gBACH,mBAAmB;gBACnB,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAE9D,IAAI,CAAC,WAAW,IAAK,WAAiC,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBACxE,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC;gBACtB,CAAC;gBAED,+BAA+B;gBAC/B,MAAM,KAAK,GAAI,WAA4B,CAAC,OAAO,CAAC;gBACpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAExE,gCAAgC;gBAChC,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAEvD,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP,QAAQ;iBACT,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBAClD,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,KAAK,EAAE,KAAkB,EAAwB,EAAE;YACtF,IAAI,CAAC;gBACH,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAChB,OAAO,EAAE,GAAG,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;gBAC7C,CAAC;gBAED,gBAAgB;gBAChB,MAAM,MAAM,GAAG,iCAAkB,CAAC,YAAY,CAAC;oBAC7C,IAAI,4BAAa,CACf,2GAA2G;wBAC3G,oDAAoD;wBACpD,2CAA2C;wBAC3C,gEAAgE;wBAChE,gEAAgE;wBAChE,oDAAoD;wBACpD,8CAA8C,CAC/C;oBACD,IAAI,kCAAmB,CAAC,UAAU,CAAC;oBACnC,IAAI,2BAAY,CAAC,iCAAiC,CAAC;iBACpD,CAAC,CAAC;gBAEH,eAAe;gBACf,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAoH,CAAC;gBACzJ,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC;oBAChC,QAAQ,EAAE,KAAK,CAAC,QAAQ;iBACzB,CAAC,CAAC;gBAEH,iBAAiB;gBACjB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;gBAE3D,kBAAkB;gBAClB,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBACnE,MAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;gBAEtE,OAAO;oBACL,GAAG,KAAK;oBACR,UAAU;iBACX,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACtD,OAAO,EAAE,GAAG,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,kBAAkB;QAClB,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,KAAkB,EAAwB,EAAE;YAC9E,IAAI,CAAC;gBACH,uCAAuC;gBACvC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;gBAEhC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxB,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC;gBACtB,CAAC;gBAED,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAA2C,EAAE,EAAE,CAAE,CAAsB,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACxI,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAA2C,EAAE,EAAE,CAAE,CAAsB,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAElI,IAAI,cAAc,KAAK,CAAC,CAAC,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;oBAChD,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC;gBACtB,CAAC;gBAED,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC;gBAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAExC,sBAAsB;gBACtB,MAAM,WAAW,GAA0C;oBACzD,OAAO,EAAE,SAAU,YAA6B,CAAC,OAAO,gBAAiB,SAAuB,CAAC,OAAO,EAAE;oBAC1G,QAAQ,EAAE;wBACR,MAAM,EAAE,cAA8B;wBACtC,IAAI,EAAE,cAA4B;wBAClC,IAAI,EAAE,CAAC,cAAc,CAAC;qBACvB;iBACF,CAAC;gBAEF,gBAAgB;gBAChB,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAEjD,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC;YACtB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;gBAC7C,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC3C,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,CAAC;QACzD,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC;QACpD,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC5C,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAEpC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,OAAO;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IAC5C,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,cAAc,CAAC,OAA0D;QACpF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,mDAAmD;QACnD,MAAM,YAAY,GAAG,OAAO,OAAO,KAAK,QAAQ;YAC9C,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAkB;YACrD,CAAC,CAAC,OAAO,CAAC;QAEZ,uBAAuB;QACvB,MAAM,YAAY,GAAgB;YAChC,QAAQ,EAAE,CAAC,YAAY,CAAC;YACxB,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,EAAE;YACf,UAAU,EAAE,EAAE;SACf,CAAC;QAEF,gBAAgB;QAChB,MAAM,MAAM,GAAG,MAAO,IAAI,CAAC,aAA0E,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAE3H,sCAAsC;QACtC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAA2C,EAAE,EAAE,CAAE,CAAsB,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAElI,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,wCAAwC,CAAC;QAClD,CAAC;QAED,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAc,CAAC;QACrE,OAAO,OAAO,aAAa,CAAC,OAAO,KAAK,QAAQ;YAC9C,CAAC,CAAC,aAAa,CAAC,OAAO;YACvB,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;CACF;AA5RD,gDA4RC", "sourcesContent": ["import { logger } from '../../logger';\nimport {\n  StateGraph,\n  ChatPromptTemplate,\n  MessagesPlaceholder,\n  HumanMessage,\n  AIMessage,\n  SystemMessage\n} from '../../agents/workflows/corePolyfill';\nimport type {\n  BaseChatModel\n} from '../../agents/workflows/corePolyfill';\nimport { llmService } from '../../llm/llmService';\nimport type { CodessaMemoryProvider } from './codessaMemory';\nimport type { MemoryEntry, MemorySource, MemoryType } from '../types';\n\n/**\n * Codessa memory state\n */\ninterface MemoryState {\n  messages: (HumanMessage | AIMessage | SystemMessage)[];\n  context: string[];\n  memories: MemoryEntry[];\n  currentTask: string;\n  nextAction: string;\n}\n\n/**\n * Codessa Memory\n * Implements memory workflows using Codessa\n */\nexport class CodessaGraphMemory {\n  private graph: StateGraph | undefined;\n  private compiledGraph: unknown;\n  private model: BaseChatModel | undefined;\n  private initialized = false;\n  private memoryProvider: CodessaMemoryProvider;\n\n  constructor(memoryProvider: CodessaMemoryProvider) {\n    this.memoryProvider = memoryProvider;\n  }\n\n  /**\n     * Initialize Codessa memory\n     */\n  public async initialize(): Promise<void> {\n    try {\n      // Get model from LLM service\n      const provider = await llmService.getDefaultProvider();\n\n      if (!provider) {\n        throw new Error('No default provider available');\n      }\n\n      // Create model adapter\n      this.model = {\n        invoke: async (messages: (HumanMessage | AIMessage | SystemMessage)[], options?: { temperature?: number; maxTokens?: number; stop?: string[] }) => {\n          try {\n            const result = await provider.generate({\n              prompt: messages.map((m: HumanMessage | AIMessage | SystemMessage) => {\n                if (m.type) {\n                  if (m.type === 'human') {\n                    return `User: ${m.content}`;\n                  } else if (m.type === 'ai') {\n                    return `Assistant: ${m.content}`;\n                  } else if (m.type === 'system') {\n                    return `System: ${m.content}`;\n                  }\n                }\n                return `User: ${String(m.content)}`;\n              }).join('\\n'),\n              modelId: 'default',\n              options: {\n                temperature: options?.temperature ?? 0.7,\n                maxTokens: options?.maxTokens,\n                stopSequences: options?.stop\n              },\n              mode: 'chat'\n            });\n\n            return new AIMessage(result.content ?? '');\n          } catch (error) {\n            logger.error('Error invoking model:', error);\n            throw error;\n          }\n        },\n        // Add required methods to satisfy BaseChatModel interface\n        _llmType: () => 'custom',\n        _modelType: () => 'chat'\n      } as unknown as BaseChatModel;\n\n      // Create memory graph\n      this.graph = this.createMemoryGraph();\n\n      this.initialized = true;\n      logger.info('Codessa memory initialized successfully');\n    } catch (error) {\n      logger.error('Failed to initialize Codessa memory:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Create memory graph\n     */\n  private createMemoryGraph(): StateGraph {\n    // Create state graph\n    const graph = new StateGraph({\n      channels: {\n        messages: {\n          value: [],\n          default: (): (HumanMessage | AIMessage | SystemMessage)[] => []\n        },\n        context: {\n          value: [],\n          default: (): string[] => []\n        },\n        memories: {\n          value: [],\n          default: (): MemoryEntry[] => []\n        },\n        currentTask: {\n          value: '',\n          default: (): string => ''\n        },\n        nextAction: {\n          value: '',\n          default: (): string => ''\n        }\n      }\n    });\n\n    // Add nodes\n\n    // 1. Retrieve relevant memories\n    graph.addNode('retrieveMemories', async (state: MemoryState): Promise<MemoryState> => {\n      try {\n        // Get last message\n        const lastMessage = state.messages[state.messages.length - 1];\n\n        if (!lastMessage || (lastMessage as { type?: string }).type !== 'human') {\n          return { ...state };\n        }\n\n        // Search for relevant memories\n        const query = (lastMessage as HumanMessage).content;\n        const memories = await this.memoryProvider.searchSimilarMemories(query);\n\n        // Extract context from memories\n        const context = memories.map(memory => memory.content);\n\n        return {\n          ...state,\n          context,\n          memories\n        };\n      } catch (error) {\n        logger.error('Error retrieving memories:', error);\n        return { ...state };\n      }\n    });\n\n    // 2. Determine next action\n    graph.addNode('determineNextAction', async (state: MemoryState): Promise<MemoryState> => {\n      try {\n        if (!this.model) {\n          return { ...state, nextAction: 'respond' };\n        }\n\n        // Create prompt\n        const prompt = ChatPromptTemplate.fromMessages([\n          new SystemMessage(\n            'You are a memory management assistant. Based on the conversation history and the user\\'s latest message, ' +\n            'determine what action to take next. Options are:\\n' +\n            '- \"respond\": Generate a normal response\\n' +\n            '- \"store\": Store important information from the conversation\\n' +\n            '- \"retrieve\": Retrieve more specific information from memory\\n' +\n            '- \"summarize\": Summarize the conversation so far\\n' +\n            'Respond with just one of these action names.'\n          ),\n          new MessagesPlaceholder('messages'),\n          new HumanMessage('What action should I take next?')\n        ]);\n\n        // Invoke model\n        const chain = prompt.pipe(this.model) as { invoke: (input: { messages: (HumanMessage | AIMessage | SystemMessage)[] }) => Promise<{ content: string }> };\n        const result = await chain.invoke({\n          messages: state.messages\n        });\n\n        // Extract action\n        const action = String(result.content).toLowerCase().trim();\n\n        // Validate action\n        const validActions = ['respond', 'store', 'retrieve', 'summarize'];\n        const nextAction = validActions.includes(action) ? action : 'respond';\n\n        return {\n          ...state,\n          nextAction\n        };\n      } catch (error) {\n        logger.error('Error determining next action:', error);\n        return { ...state, nextAction: 'respond' };\n      }\n    });\n\n    // 3. Store memory\n    graph.addNode('storeMemory', async (state: MemoryState): Promise<MemoryState> => {\n      try {\n        // Get last message pair (human and AI)\n        const messages = state.messages;\n\n        if (messages.length < 2) {\n          return { ...state };\n        }\n\n        const lastHumanIndex = messages.map((m: HumanMessage | AIMessage | SystemMessage) => (m as { type: string }).type).lastIndexOf('human');\n        const lastAIIndex = messages.map((m: HumanMessage | AIMessage | SystemMessage) => (m as { type: string }).type).lastIndexOf('ai');\n\n        if (lastHumanIndex === -1 || lastAIIndex === -1) {\n          return { ...state };\n        }\n\n        const humanMessage = messages[lastHumanIndex];\n        const aiMessage = messages[lastAIIndex];\n\n        // Create memory entry\n        const memoryEntry: Omit<MemoryEntry, 'id' | 'timestamp'> = {\n          content: `User: ${(humanMessage as HumanMessage).content}\\nAssistant: ${(aiMessage as AIMessage).content}`,\n          metadata: {\n            source: 'conversation' as MemorySource,\n            type: 'conversation' as MemoryType,\n            tags: ['conversation']\n          }\n        };\n\n        // Add to memory\n        await this.memoryProvider.addMemory(memoryEntry);\n\n        return { ...state };\n      } catch (error) {\n        logger.error('Error storing memory:', error);\n        return { ...state };\n      }\n    });\n\n    // Define edges\n    graph.addEdge('start', 'retrieveMemories');\n    graph.addEdge('retrieveMemories', 'determineNextAction');\n    graph.addEdge('determineNextAction', 'storeMemory');\n    graph.addEdge('determineNextAction', 'end');\n    graph.addEdge('storeMemory', 'end');\n\n    return graph;\n  }\n\n  /**\n     * Compile the memory graph\n     */\n  public async compile(): Promise<void> {\n    if (!this.initialized) {\n      await this.initialize();\n    }\n\n    if (!this.graph) {\n      throw new Error('Memory graph not initialized');\n    }\n\n    this.compiledGraph = this.graph.compile();\n  }\n\n  /**\n     * Process a new message\n     * @param message The message to process\n     * @returns The AI response as a string\n     */\n  public async processMessage(message: string | HumanMessage | AIMessage | SystemMessage): Promise<string> {\n    if (!this.compiledGraph) {\n      await this.compile();\n    }\n\n    if (!this.compiledGraph) {\n      throw new Error('Memory graph not compiled');\n    }\n\n    // Convert string message to HumanMessage if needed\n    const humanMessage = typeof message === 'string'\n      ? { type: 'human', content: message } as HumanMessage\n      : message;\n\n    // Create initial state\n    const initialState: MemoryState = {\n      messages: [humanMessage],\n      context: [],\n      memories: [],\n      currentTask: '',\n      nextAction: ''\n    };\n\n    // Run the graph\n    const result = await (this.compiledGraph as { invoke: (state: MemoryState) => Promise<MemoryState> }).invoke(initialState);\n\n    // Extract AI response from the result\n    const aiMessages = result.messages.filter((m: HumanMessage | AIMessage | SystemMessage) => (m as { type: string }).type === 'ai');\n\n    if (aiMessages.length === 0) {\n      return 'I don\\'t have a response at this time.';\n    }\n\n    const lastAIMessage = aiMessages[aiMessages.length - 1] as AIMessage;\n    return typeof lastAIMessage.content === 'string'\n      ? lastAIMessage.content\n      : String(lastAIMessage.content);\n  }\n}"]}