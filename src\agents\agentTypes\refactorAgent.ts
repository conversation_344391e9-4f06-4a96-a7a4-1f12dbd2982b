import { Agent, AgentContext, AgentRunInput, AgentRunResult } from '../agentUtilities/agent';
import { ITool } from '../../tools/types';
import { logger } from '../../logger';

export class RefactorAgent extends Agent {
  constructor(_config: any) {
    super({
      ..._config,
      role: 'assistant',
      capabilities: ['refactor'],
      llmProvider: 'openai',
      llmModel: 'gpt-4'
    });
  }

  override async run(input: AgentRunInput, context: AgentContext = {}): Promise<AgentRunResult> {
    logger.info(`RefactorAgent processing: "${input.prompt.substring(0, 50)}..."`);

    // --- Gather relevant workflow for refactor mode ---
    let workflow: any = undefined;
    if (context.variables?.workflowManager) {
      workflow = await context.variables.workflowManager.getWorkflowForMode('refactor');
    }

    // --- Gather all relevant tools ---
    let tools = this.tools;
    if (context.tools) {
      tools = context.tools as Map<string, ITool>;
    }

    // --- Gather memory and knowledgebase context ---
    let memoryContext = '';
    if (this.getMemory) {
      const memories = await this.getMemory().getRelevantMemories(input.prompt);
      if (memories?.length) {
        memoryContext = this.getMemory().formatMemoriesForPrompt(memories);
      }
    }
    let kbContext = '';
    if (context.variables?.knowledgebaseManager) {
      kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input.prompt);
    }

    // --- Gather MCP context ---
    let mcpContext = '';
    if (context.variables?.mcpManager) {
      mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext() || {});
    }

    // --- Use external prompt if available ---
    let externalPrompt = '';
    if (context.variables?.promptManager) {
      externalPrompt = context.variables.promptManager.getSystemPrompt('refactorAgent', context.variables) || '';
    }

    // --- Compose final prompt ---
    const refactorPrompt = `
# Refactor Mode Processing

${externalPrompt}

## User Request:
${input.prompt}

## Memory Context:
${memoryContext}

## Knowledgebase Context:
${kbContext}

## MCP Context:
${mcpContext}

## Workflow:
${workflow ? JSON.stringify(workflow) : 'None'}

## Code to Refactor:
${context.workspace?.currentFile ? (typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile) : 'No code context available'}

## Your Task:
1. Analyze the code for refactoring opportunities
2. Suggest specific refactoring changes
3. Explain the benefits of each change
Use all available workflows, tools, memory, knowledgebase, and context. Stream output if possible.
`;
    input.prompt = refactorPrompt;
    // Pass all gathered context and tools to super.run
    return super.run(input, { ...context, tools });
  }
}