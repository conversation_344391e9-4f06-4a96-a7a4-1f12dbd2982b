{"version": 3, "file": "projectWideIntelligence.js", "sourceRoot": "", "sources": ["../../src/intelligence/projectWideIntelligence.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AAKzB,sCAAmC;AACnC,4DAAyD;AAiHzD,MAAa,uBAAuB;IAC1B,eAAe,CAAkB;IACjC,YAAY,CAAe;IAC3B,aAAa,CAAgB;IAC7B,aAAa,CAAsB;IAE3C,iBAAiB;IACT,aAAa,GAA0E,IAAI,GAAG,EAAE,CAAC;IACxF,WAAW,GAAG,OAAO,CAAC,CAAC,SAAS;IAEjD,uBAAuB;IACf,WAAW,GAAoC,IAAI,CAAC;IACpD,kBAAkB,GAAG,KAAK,CAAC;IAEnC,YACE,eAAgC,EAChC,YAA0B,EAC1B,aAA4B,EAC5B,aAAkC;QAElC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,qBAAqB,CAAC,WAAoB;QACrD,MAAM,aAAa,GAAG,WAAW,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QACxF,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,oBAAoB;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,MAAM,EAAE,CAAC;YACX,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC5D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YAEvE,wBAAwB;YACxB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAE/D,sBAAsB;YACtB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAEvF,yBAAyB;YACzB,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAE7F,2BAA2B;YAC3B,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAEjG,0BAA0B;YAC1B,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAE7F,0BAA0B;YAC1B,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAE/F,8BAA8B;YAC9B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;gBACzD,WAAW;gBACX,gBAAgB;gBAChB,mBAAmB;gBACnB,qBAAqB;gBACrB,kBAAkB;gBAClB,oBAAoB;aACrB,CAAC,CAAC;YAEH,MAAM,MAAM,GAA8B;gBACxC,WAAW;gBACX,gBAAgB;gBAChB,mBAAmB;gBACnB,qBAAqB;gBACrB,kBAAkB;gBAClB,oBAAoB;gBACpB,eAAe;gBACf,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC;YAEF,mBAAmB;YACnB,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAE5C,0BAA0B;YAC1B,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAEvC,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAClE,OAAO,MAAM,CAAC;QAEhB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,WAAmB;QAChD,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAExC,MAAM,GAAG,GAAgB;YACvB,WAAW;YACX,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,EAAE;YACf,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,EAAE;YACb,kBAAkB,EAAE,EAAE;SACvB,CAAC;QAEF,sCAAsC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE;gBACpD,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE,IAAI;gBACf,aAAa,EAAE,KAAK;aACrB,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,GAAG,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACnF,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAElD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,sBAAsB,CAAC,WAAmB,EAAE,WAAwB;QAChF,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAE7C,MAAM,MAAM,GAAoB,EAAE,CAAC;QAEnC,sCAAsC;QACtC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC/D,IAAI,YAAY,EAAE,CAAC;YACjB,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC1C,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrD,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE;wBACpD,QAAQ,EAAE,GAAG,CAAC,IAAI;qBACnB,CAAC,CAAC;oBAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;wBAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;4BACvD,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,QAAQ;4BACpC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,SAAS;4BAC7B,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,yBAAyB;4BAC3D,IAAI,EAAE,GAAG,CAAC,IAAI;4BACd,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,cAAc,EAAE,KAAK,CAAC,cAAc,IAAI,2BAA2B;4BACnE,KAAK,EAAE,KAAK,CAAC,KAAK;yBACnB,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,cAAc,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QACpF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;QAErF,OAAO;YACL,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,cAAc;YACd,MAAM;YACN,aAAa;SACd,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,yBAAyB,CAAC,WAAmB,EAAE,WAAwB;QACnF,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAEhD,MAAM,QAAQ,GAAyB,EAAE,CAAC;QAC1C,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,yCAAyC;QACzC,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAEpG,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,wBAAwB;YACnE,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC9D,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACjF,QAAQ,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;YACvC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,qCAAqC,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAC7E,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,iBAAiB,GAAG,EAAE,CAAC,CAAC,CAAC;QAEzF,OAAO;YACL,QAAQ;YACR,YAAY;YACZ,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,2BAA2B,CAAC,WAAmB,EAAE,WAAwB;QACrF,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAElD,MAAM,IAAI,GAAuB,EAAE,CAAC;QACpC,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAC5B,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,kCAAkC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAEpG,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,wBAAwB;gBACnE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,sBAAsB,EAAE;oBAC3D,QAAQ,EAAE,IAAI,CAAC,IAAI;iBACpB,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBACpC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;oBAC/B,cAAc,IAAI,QAAQ,CAAC,cAAc,IAAI,CAAC,CAAC;oBAC/C,mBAAmB,IAAI,QAAQ,CAAC,mBAAmB,IAAI,CAAC,CAAC;oBAEzD,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;wBAClB,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC9B,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,kBAAkB,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjG,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,kBAAkB,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAEzE,OAAO;YACL,kBAAkB;YAClB,IAAI;YACJ,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,0BAA0B,CAAC,WAAmB,EAAE,WAAwB;QACpF,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAEjD,wCAAwC;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAChE,IAAI,YAAY,GAAqB,EAAE,CAAC;QAExC,qDAAqD;QACrD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;QAClD,eAAM,CAAC,KAAK,CAAC,iCAAiC,SAAS,QAAQ,CAAC,CAAC;QAEjE,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBAC1D,WAAW,EAAE,WAAW;aACzB,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;YAClD,CAAC;QACH,CAAC;QAED,MAAM,oBAAoB,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC;QACpF,MAAM,sBAAsB,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;QAChG,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,oBAAoB,GAAG,CAAC,CAAC,GAAG,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAC,CAAC;QAElG,OAAO;YACL,iBAAiB,EAAE,YAAY,CAAC,MAAM;YACtC,oBAAoB;YACpB,sBAAsB;YACtB,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,0BAA0B,CAAC,WAAmB,EAAE,WAAwB;QACpF,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAEjD,MAAM,QAAQ,GAA0B,EAAE,CAAC;QAE3C,2CAA2C;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAC;QAEjE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;YAC5C,MAAM;YACN,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACrE,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QACnC,CAAC;QAED,2CAA2C;QAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAC;QACrE,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,CAAC;QAE3D,OAAO;YACL,QAAQ;YACR,UAAU;YACV,oBAAoB;SACrB,CAAC;IACJ,CAAC;IAED,iBAAiB;IACT,KAAK,CAAC,yBAAyB,CAAC,SAAc,EAAE,WAAmB;QACzE,+CAA+C;QAC/C,MAAM,KAAK,GAAoB,EAAE,CAAC;QAClC,eAAM,CAAC,KAAK,CAAC,+CAA+C,WAAW,EAAE,CAAC,CAAC;QAE3E,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;gBAC7B,MAAM,IAAI,GAAkB;oBAC1B,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC9B,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC;oBACpB,UAAU,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC;oBACnD,UAAU,EAAE,CAAC;oBACb,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,GAAG,EAAE;iBAC9C,CAAC;gBAEF,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC/C,IAAI,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClE,CAAC;gBAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,sBAAsB,CAAC,GAAgB;QAC7C,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAEvE,4BAA4B;QAC5B,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACxC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,GAAgB,EAAE,WAAmB;QACtE,MAAM,eAAe,GAAG;YACtB,MAAM,EAAE,CAAC,gBAAgB,EAAE,yBAAyB,EAAE,OAAO,EAAE,YAAY,EAAE,gBAAgB,CAAC;YAC9F,IAAI,EAAE,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,EAAE,WAAW,CAAC;YACjF,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,CAAC;YACtD,KAAK,EAAE,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,kBAAkB,CAAC;SACvE,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;gBAE1D,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;oBACvE,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;gBACD,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;oBACrE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;gBACD,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;oBACrE,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACxC,CAAC;gBACD,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;oBACtE,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,UAAU,CAAC,QAAgB;QACjC,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACjG,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,cAAc,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,WAAW,GAA8B;YAC7C,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,GAAG;YACT,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,KAAK;SACd,CAAC;QACF,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC;IACvC,CAAC;IAEO,uBAAuB,CAAC,QAAgB;QAC9C,qDAAqD;QACrD,IAAI,UAAU,GAAG,GAAG,CAAC,CAAC,kBAAkB;QAExC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,UAAU,IAAI,GAAG,CAAC;QACjF,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC;YAAE,UAAU,IAAI,GAAG,CAAC;QACxF,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,UAAU,IAAI,GAAG,CAAC;QAC9E,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,UAAU,GAAG,GAAG,CAAC;QAErF,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;IAC9C,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,QAAgB;QACpD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACnE,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;gBACzE,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBACpC,OAAO,MAAM,CAAC,MAAM,CAAC,oBAAoB,IAAI,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,sCAAsC,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,QAAgB,EAAE,OAAe;QACpE,MAAM,QAAQ,GAAyB,EAAE,CAAC;QAE1C,sFAAsF;QACtF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,yCAAyC;YACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjD,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,YAAY;oBAClB,QAAQ,EAAE,QAAQ;oBAClB,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,GAAG,CAAC;oBACX,WAAW,EAAE,uBAAuB;oBACpC,MAAM,EAAE,kDAAkD;oBAC1D,UAAU,EAAE,qEAAqE;oBACjF,oBAAoB,EAAE,gCAAgC;iBACvD,CAAC,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBAChE,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,KAAK;oBACf,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,GAAG,CAAC;oBACX,WAAW,EAAE,sBAAsB;oBACnC,MAAM,EAAE,gDAAgD;oBACxD,UAAU,EAAE,iDAAiD;oBAC7D,oBAAoB,EAAE,uBAAuB;iBAC9C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,+BAA+B,CAAC,WAAwB;QAC9D,MAAM,aAAa,GAAG,WAAW,CAAC,WAAW;aAC1C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aACzD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;aACZ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;aACxD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC;aACpD,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,KAAK,KAAK,QAAQ,CAAC;aACjD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW;aACxC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;aACrD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,OAAO,6BAAa,CAAC,YAAY,CAAC,uBAAuB,EAAE;YACzD,aAAa;YACb,SAAS;YACT,WAAW;SACZ,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,QAAgB;QAChD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,OAAO,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,0CAA0C,KAAK,EAAE,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,+BAA+B,CAAC,WAAwB;QAC9D,gDAAgD;QAChD,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa;QACvE,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,qBAAqB;QAClF,UAAU,IAAI,WAAW,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,mBAAmB;QAExE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,QAA4C;QAChF,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YAC9E,eAAe,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,gBAAgB,CAAC,cAAc,uCAAuC,CAAC,CAAC;QACnH,CAAC;QAED,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,mBAAmB,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;YACnF,eAAe,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QACtF,CAAC;QAED,gCAAgC;QAChC,IAAI,QAAQ,CAAC,qBAAqB,IAAI,QAAQ,CAAC,qBAAqB,CAAC,kBAAkB,GAAG,EAAE,EAAE,CAAC;YAC7F,eAAe,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC/E,CAAC;QAED,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,kBAAkB,IAAI,QAAQ,CAAC,kBAAkB,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;YACxF,eAAe,CAAC,IAAI,CAAC,UAAU,QAAQ,CAAC,kBAAkB,CAAC,oBAAoB,wBAAwB,CAAC,CAAC;QAC3G,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,gBAAgB;QACtB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACtC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAEpE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE;gBAChC,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE;gBAChC,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE;gBAChC,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,WAAmB;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACnD,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC/D,OAAO,MAAM,CAAC,MAAM,CAAC;QACvB,CAAC;QACD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,WAAmB,EAAE,MAAiC;QAC5E,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACzE,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAiC;QACjE,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAC1C,uCAAuC,EACvC;gBACE,QAAQ,EAAE,MAAM,CAAC,WAAW,CAAC,WAAW;gBACxC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC;aAC9D,EACD,QAAQ,EACR,EAAE,IAAI,EAAE,CAAC,sBAAsB,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,CACzD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;IACpD,CAAC;CACF;AAvnBD,0DAunBC", "sourcesContent": ["/**\n * Project-Wide Intelligence System - Complete project understanding\n * \n * Provides codebase mapping, dependency intelligence, architecture analysis,\n * performance insights, security analysis, and documentation intelligence\n * while integrating with existing Codessa infrastructure.\n */\n\nimport * as vscode from 'vscode';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport { SupervisorAgent } from '../agents/agentTypes/supervisorAgent';\nimport { ToolRegistry } from '../tools/toolRegistry';\nimport { MemoryManager } from '../memory/memoryManager';\nimport { QuantumMemorySystem } from '../memory/quantum/quantumMemorySystem';\nimport { logger } from '../logger';\nimport { promptManager } from '../prompts/promptManager';\n\nexport interface CodebaseMap {\n  projectRoot: string;\n  totalFiles: number;\n  totalLines: number;\n  languages: { [language: string]: number };\n  directories: DirectoryNode[];\n  dependencies: DependencyInfo[];\n  architecture: ArchitecturePattern[];\n  entryPoints: string[];\n  configFiles: string[];\n  testFiles: string[];\n  documentationFiles: string[];\n}\n\nexport interface DirectoryNode {\n  name: string;\n  path: string;\n  type: 'directory' | 'file';\n  size: number;\n  children?: DirectoryNode[];\n  language?: string;\n  importance: number; // 0-1 scale\n  complexity: number;\n  lastModified: number;\n}\n\nexport interface DependencyInfo {\n  name: string;\n  version: string;\n  type: 'production' | 'development' | 'peer' | 'optional';\n  source: string; // package.json, requirements.txt, etc.\n  usageCount: number;\n  securityIssues: SecurityIssue[];\n  updateAvailable?: string;\n  licenseInfo?: string;\n}\n\nexport interface ArchitecturePattern {\n  pattern: string;\n  confidence: number;\n  description: string;\n  files: string[];\n  benefits: string[];\n  concerns: string[];\n}\n\nexport interface SecurityIssue {\n  severity: 'low' | 'medium' | 'high' | 'critical';\n  type: string;\n  description: string;\n  file?: string;\n  line?: number;\n  recommendation: string;\n  cveId?: string;\n}\n\nexport interface PerformanceInsight {\n  type: 'bottleneck' | 'optimization' | 'memory' | 'cpu' | 'io';\n  severity: 'low' | 'medium' | 'high';\n  file: string;\n  function?: string;\n  line?: number;\n  description: string;\n  impact: string;\n  suggestion: string;\n  estimatedImprovement: string;\n}\n\nexport interface DocumentationGap {\n  type: 'missing' | 'outdated' | 'incomplete';\n  file: string;\n  function?: string;\n  class?: string;\n  severity: 'low' | 'medium' | 'high';\n  description: string;\n  suggestedContent: string;\n}\n\nexport interface ProjectIntelligenceReport {\n  codebaseMap: CodebaseMap;\n  securityAnalysis: {\n    totalIssues: number;\n    criticalIssues: number;\n    issues: SecurityIssue[];\n    securityScore: number; // 0-100\n  };\n  performanceAnalysis: {\n    insights: PerformanceInsight[];\n    overallScore: number; // 0-100\n    bottlenecks: string[];\n  };\n  documentationAnalysis: {\n    coveragePercentage: number;\n    gaps: DocumentationGap[];\n    qualityScore: number; // 0-100\n  };\n  dependencyAnalysis: {\n    totalDependencies: number;\n    outdatedDependencies: number;\n    vulnerableDependencies: number;\n    healthScore: number; // 0-100\n  };\n  architectureAnalysis: {\n    patterns: ArchitecturePattern[];\n    complexity: number;\n    maintainabilityScore: number; // 0-100\n  };\n  recommendations: string[];\n  generatedAt: number;\n}\n\nexport class ProjectWideIntelligence {\n  private supervisorAgent: SupervisorAgent;\n  private toolRegistry: ToolRegistry;\n  private memoryManager: MemoryManager;\n  private quantumMemory: QuantumMemorySystem;\n\n  // Analysis cache\n  private analysisCache: Map<string, { report: ProjectIntelligenceReport; timestamp: number }> = new Map();\n  private readonly cacheExpiry = 3600000; // 1 hour\n\n  // Real-time monitoring\n  private fileWatcher: vscode.FileSystemWatcher | null = null;\n  private analysisInProgress = false;\n\n  constructor(\n    supervisorAgent: SupervisorAgent,\n    toolRegistry: ToolRegistry,\n    memoryManager: MemoryManager,\n    quantumMemory: QuantumMemorySystem\n  ) {\n    this.supervisorAgent = supervisorAgent;\n    this.toolRegistry = toolRegistry;\n    this.memoryManager = memoryManager;\n    this.quantumMemory = quantumMemory;\n\n    this.setupFileWatcher();\n  }\n\n  /**\n     * Generate comprehensive project intelligence report\n     */\n  public async generateProjectReport(projectPath?: string): Promise<ProjectIntelligenceReport> {\n    const workspaceRoot = projectPath || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    if (!workspaceRoot) {\n      throw new Error('No workspace folder found');\n    }\n\n    // Check cache first\n    const cached = this.getCachedReport(workspaceRoot);\n    if (cached) {\n      logger.info('Returning cached project intelligence report');\n      return cached;\n    }\n\n    if (this.analysisInProgress) {\n      throw new Error('Project analysis already in progress');\n    }\n\n    this.analysisInProgress = true;\n\n    try {\n      logger.info('Generating comprehensive project intelligence report...');\n\n      // 1. Build codebase map\n      const codebaseMap = await this.buildCodebaseMap(workspaceRoot);\n\n      // 2. Analyze security\n      const securityAnalysis = await this.analyzeProjectSecurity(workspaceRoot, codebaseMap);\n\n      // 3. Analyze performance\n      const performanceAnalysis = await this.analyzeProjectPerformance(workspaceRoot, codebaseMap);\n\n      // 4. Analyze documentation\n      const documentationAnalysis = await this.analyzeProjectDocumentation(workspaceRoot, codebaseMap);\n\n      // 5. Analyze dependencies\n      const dependencyAnalysis = await this.analyzeProjectDependencies(workspaceRoot, codebaseMap);\n\n      // 6. Analyze architecture\n      const architectureAnalysis = await this.analyzeProjectArchitecture(workspaceRoot, codebaseMap);\n\n      // 7. Generate recommendations\n      const recommendations = await this.generateRecommendations({\n        codebaseMap,\n        securityAnalysis,\n        performanceAnalysis,\n        documentationAnalysis,\n        dependencyAnalysis,\n        architectureAnalysis\n      });\n\n      const report: ProjectIntelligenceReport = {\n        codebaseMap,\n        securityAnalysis,\n        performanceAnalysis,\n        documentationAnalysis,\n        dependencyAnalysis,\n        architectureAnalysis,\n        recommendations,\n        generatedAt: Date.now()\n      };\n\n      // Cache the report\n      this.setCachedReport(workspaceRoot, report);\n\n      // Store in quantum memory\n      await this.storeReportInMemory(report);\n\n      logger.info('Project intelligence report generated successfully');\n      return report;\n\n    } finally {\n      this.analysisInProgress = false;\n    }\n  }\n\n  /**\n     * Build comprehensive codebase map\n     */\n  private async buildCodebaseMap(projectRoot: string): Promise<CodebaseMap> {\n    logger.info('Building codebase map...');\n\n    const map: CodebaseMap = {\n      projectRoot,\n      totalFiles: 0,\n      totalLines: 0,\n      languages: {},\n      directories: [],\n      dependencies: [],\n      architecture: [],\n      entryPoints: [],\n      configFiles: [],\n      testFiles: [],\n      documentationFiles: []\n    };\n\n    // Use existing directory listing tool\n    const dirTool = this.toolRegistry.getTool('listDir');\n    if (dirTool) {\n      const result = await dirTool.execute('listDirectory', {\n        path: projectRoot,\n        recursive: true,\n        includeHidden: false\n      });\n\n      if (result.success && result.output) {\n        map.directories = await this.processDirectoryStructure(result.output, projectRoot);\n        this.calculateCodebaseStats(map);\n      }\n    }\n\n    // Identify special files\n    await this.identifySpecialFiles(map, projectRoot);\n\n    return map;\n  }\n\n  /**\n     * Analyze project security\n     */\n  private async analyzeProjectSecurity(projectRoot: string, codebaseMap: CodebaseMap): Promise<ProjectIntelligenceReport['securityAnalysis']> {\n    logger.info('Analyzing project security...');\n\n    const issues: SecurityIssue[] = [];\n\n    // Use existing security scanning tool\n    const securityTool = this.toolRegistry.getTool('securityScan');\n    if (securityTool) {\n      for (const dir of codebaseMap.directories) {\n        if (dir.type === 'file' && this.isCodeFile(dir.path)) {\n          const result = await securityTool.execute('scanFile', {\n            filePath: dir.path\n          });\n\n          if (result.success && result.output?.issues) {\n            issues.push(...result.output.issues.map((issue: any) => ({\n              severity: issue.severity || 'medium',\n              type: issue.type || 'unknown',\n              description: issue.description || 'Security issue detected',\n              file: dir.path,\n              line: issue.line,\n              recommendation: issue.recommendation || 'Review and fix this issue',\n              cveId: issue.cveId\n            })));\n          }\n        }\n      }\n    }\n\n    // Analyze dependencies for vulnerabilities\n    for (const dep of codebaseMap.dependencies) {\n      issues.push(...dep.securityIssues);\n    }\n\n    const criticalIssues = issues.filter(issue => issue.severity === 'critical').length;\n    const securityScore = Math.max(0, 100 - (issues.length * 5) - (criticalIssues * 20));\n\n    return {\n      totalIssues: issues.length,\n      criticalIssues,\n      issues,\n      securityScore\n    };\n  }\n\n  /**\n     * Analyze project performance\n     */\n  private async analyzeProjectPerformance(projectRoot: string, codebaseMap: CodebaseMap): Promise<ProjectIntelligenceReport['performanceAnalysis']> {\n    logger.info('Analyzing project performance...');\n\n    const insights: PerformanceInsight[] = [];\n    const bottlenecks: string[] = [];\n\n    // Use AI to analyze performance patterns\n    const codeFiles = codebaseMap.directories.filter(d => d.type === 'file' && this.isCodeFile(d.path));\n\n    for (const file of codeFiles.slice(0, 20)) { // Limit for performance\n      try {\n        const content = await fs.promises.readFile(file.path, 'utf8');\n        const performanceInsight = await this.analyzeFilePerformance(file.path, content);\n        insights.push(...performanceInsight);\n      } catch (error) {\n        logger.warn(`Failed to analyze performance for ${file.path}: ${error}`);\n      }\n    }\n\n    // Calculate overall score\n    const highSeverityCount = insights.filter(i => i.severity === 'high').length;\n    const overallScore = Math.max(0, 100 - (insights.length * 3) - (highSeverityCount * 15));\n\n    return {\n      insights,\n      overallScore,\n      bottlenecks\n    };\n  }\n\n  /**\n     * Analyze project documentation\n     */\n  private async analyzeProjectDocumentation(projectRoot: string, codebaseMap: CodebaseMap): Promise<ProjectIntelligenceReport['documentationAnalysis']> {\n    logger.info('Analyzing project documentation...');\n\n    const gaps: DocumentationGap[] = [];\n    let documentedFunctions = 0;\n    let totalFunctions = 0;\n\n    // Use existing documentation tool\n    const docTool = this.toolRegistry.getTool('docs');\n    if (docTool) {\n      const codeFiles = codebaseMap.directories.filter(d => d.type === 'file' && this.isCodeFile(d.path));\n\n      for (const file of codeFiles.slice(0, 10)) { // Limit for performance\n        const result = await docTool.execute('analyzeDocumentation', {\n          filePath: file.path\n        });\n\n        if (result.success && result.output) {\n          const analysis = result.output;\n          totalFunctions += analysis.totalFunctions || 0;\n          documentedFunctions += analysis.documentedFunctions || 0;\n\n          if (analysis.gaps) {\n            gaps.push(...analysis.gaps);\n          }\n        }\n      }\n    }\n\n    const coveragePercentage = totalFunctions > 0 ? (documentedFunctions / totalFunctions) * 100 : 0;\n    const qualityScore = Math.max(0, coveragePercentage - (gaps.length * 5));\n\n    return {\n      coveragePercentage,\n      gaps,\n      qualityScore\n    };\n  }\n\n  /**\n     * Analyze project dependencies\n     */\n  private async analyzeProjectDependencies(projectRoot: string, codebaseMap: CodebaseMap): Promise<ProjectIntelligenceReport['dependencyAnalysis']> {\n    logger.info('Analyzing project dependencies...');\n\n    // Use existing dependency analysis tool\n    const depTool = this.toolRegistry.getTool('dependencyAnalysis');\n    let dependencies: DependencyInfo[] = [];\n\n    // Analyze codebase structure for dependency patterns\n    const fileCount = Object.keys(codebaseMap).length;\n    logger.debug(`Analyzing dependencies across ${fileCount} files`);\n\n    if (depTool) {\n      const result = await depTool.execute('analyzeDependencies', {\n        projectPath: projectRoot\n      });\n\n      if (result.success && result.output) {\n        dependencies = result.output.dependencies || [];\n      }\n    }\n\n    const outdatedDependencies = dependencies.filter(dep => dep.updateAvailable).length;\n    const vulnerableDependencies = dependencies.filter(dep => dep.securityIssues.length > 0).length;\n    const healthScore = Math.max(0, 100 - (outdatedDependencies * 2) - (vulnerableDependencies * 10));\n\n    return {\n      totalDependencies: dependencies.length,\n      outdatedDependencies,\n      vulnerableDependencies,\n      healthScore\n    };\n  }\n\n  /**\n     * Analyze project architecture\n     */\n  private async analyzeProjectArchitecture(projectRoot: string, codebaseMap: CodebaseMap): Promise<ProjectIntelligenceReport['architectureAnalysis']> {\n    logger.info('Analyzing project architecture...');\n\n    const patterns: ArchitecturePattern[] = [];\n\n    // Use AI to identify architecture patterns\n    const prompt = this.buildArchitectureAnalysisPrompt(codebaseMap);\n\n    const result = await this.supervisorAgent.run({\n      prompt,\n      mode: 'ask'\n    });\n\n    if (result.success && result.output) {\n      const parsedPatterns = this.parseArchitecturePatterns(result.output);\n      patterns.push(...parsedPatterns);\n    }\n\n    // Calculate complexity and maintainability\n    const complexity = this.calculateArchitectureComplexity(codebaseMap);\n    const maintainabilityScore = Math.max(0, 100 - complexity);\n\n    return {\n      patterns,\n      complexity,\n      maintainabilityScore\n    };\n  }\n\n  // Helper methods\n  private async processDirectoryStructure(dirOutput: any, projectRoot: string): Promise<DirectoryNode[]> {\n    // Process directory structure from tool output\n    const nodes: DirectoryNode[] = [];\n    logger.debug(`Processing directory structure for project: ${projectRoot}`);\n\n    if (Array.isArray(dirOutput)) {\n      for (const item of dirOutput) {\n        const node: DirectoryNode = {\n          name: path.basename(item.path),\n          path: item.path,\n          type: item.type,\n          size: item.size || 0,\n          importance: this.calculateFileImportance(item.path),\n          complexity: 0,\n          lastModified: item.lastModified || Date.now()\n        };\n\n        if (item.type === 'file') {\n          node.language = this.detectLanguage(item.path);\n          node.complexity = await this.calculateFileComplexity(item.path);\n        }\n\n        nodes.push(node);\n      }\n    }\n\n    return nodes;\n  }\n\n  private calculateCodebaseStats(map: CodebaseMap): void {\n    map.totalFiles = map.directories.filter(d => d.type === 'file').length;\n\n    // Count lines and languages\n    for (const dir of map.directories) {\n      if (dir.type === 'file' && dir.language) {\n        map.languages[dir.language] = (map.languages[dir.language] || 0) + 1;\n      }\n    }\n  }\n\n  private async identifySpecialFiles(map: CodebaseMap, projectRoot: string): Promise<void> {\n    const specialPatterns = {\n      config: [/package\\.json$/, /\\.config\\.(js|ts|json)$/, /\\.env/, /Dockerfile/, /docker-compose/],\n      test: [/\\.test\\.(js|ts|py)$/, /\\.spec\\.(js|ts|py)$/, /test_.*\\.py$/, /__tests__/],\n      docs: [/README/, /\\.md$/, /docs\\//, /documentation\\//],\n      entry: [/index\\.(js|ts|py)$/, /main\\.(js|ts|py)$/, /app\\.(js|ts|py)$/]\n    };\n\n    for (const dir of map.directories) {\n      if (dir.type === 'file') {\n        const relativePath = path.relative(projectRoot, dir.path);\n\n        if (specialPatterns.config.some(pattern => pattern.test(relativePath))) {\n          map.configFiles.push(dir.path);\n        }\n        if (specialPatterns.test.some(pattern => pattern.test(relativePath))) {\n          map.testFiles.push(dir.path);\n        }\n        if (specialPatterns.docs.some(pattern => pattern.test(relativePath))) {\n          map.documentationFiles.push(dir.path);\n        }\n        if (specialPatterns.entry.some(pattern => pattern.test(relativePath))) {\n          map.entryPoints.push(dir.path);\n        }\n      }\n    }\n  }\n\n  private isCodeFile(filePath: string): boolean {\n    const codeExtensions = ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.cs', '.go', '.rs', '.php'];\n    return codeExtensions.some(ext => filePath.endsWith(ext));\n  }\n\n  private detectLanguage(filePath: string): string {\n    const ext = path.extname(filePath).toLowerCase();\n    const languageMap: { [key: string]: string } = {\n      '.js': 'javascript',\n      '.ts': 'typescript',\n      '.py': 'python',\n      '.java': 'java',\n      '.cpp': 'cpp',\n      '.c': 'c',\n      '.cs': 'csharp',\n      '.go': 'go',\n      '.rs': 'rust',\n      '.php': 'php'\n    };\n    return languageMap[ext] || 'unknown';\n  }\n\n  private calculateFileImportance(filePath: string): number {\n    // Calculate file importance based on various factors\n    let importance = 0.5; // Base importance\n\n    if (filePath.includes('index.') || filePath.includes('main.')) importance += 0.3;\n    if (filePath.includes('config') || filePath.includes('package.json')) importance += 0.2;\n    if (filePath.includes('test') || filePath.includes('spec')) importance -= 0.1;\n    if (filePath.includes('node_modules') || filePath.includes('.git')) importance = 0.1;\n\n    return Math.max(0, Math.min(1, importance));\n  }\n\n  private async calculateFileComplexity(filePath: string): Promise<number> {\n    try {\n      const complexityTool = this.toolRegistry.getTool('codeComplexity');\n      if (complexityTool) {\n        const result = await complexityTool.execute('analyzeFile', { filePath });\n        if (result.success && result.output) {\n          return result.output.cyclomaticComplexity || 0;\n        }\n      }\n    } catch (error) {\n      logger.warn(`Failed to calculate complexity for ${filePath}: ${error}`);\n    }\n    return 0;\n  }\n\n  private async analyzeFilePerformance(filePath: string, content: string): Promise<PerformanceInsight[]> {\n    const insights: PerformanceInsight[] = [];\n\n    // Simple heuristic analysis (in real implementation, use more sophisticated analysis)\n    const lines = content.split('\\n');\n\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i];\n\n      // Check for potential performance issues\n      if (line.includes('for') && line.includes('for')) {\n        insights.push({\n          type: 'bottleneck',\n          severity: 'medium',\n          file: filePath,\n          line: i + 1,\n          description: 'Nested loops detected',\n          impact: 'May cause performance issues with large datasets',\n          suggestion: 'Consider optimizing nested loops or using more efficient algorithms',\n          estimatedImprovement: '20-50% performance improvement'\n        });\n      }\n\n      if (line.includes('setTimeout') || line.includes('setInterval')) {\n        insights.push({\n          type: 'memory',\n          severity: 'low',\n          file: filePath,\n          line: i + 1,\n          description: 'Timer usage detected',\n          impact: 'Potential memory leaks if not properly cleared',\n          suggestion: 'Ensure timers are cleared when no longer needed',\n          estimatedImprovement: 'Prevents memory leaks'\n        });\n      }\n    }\n\n    return insights;\n  }\n\n  private buildArchitectureAnalysisPrompt(codebaseMap: CodebaseMap): string {\n    const fileStructure = codebaseMap.directories\n      .filter(d => d.type === 'file' && this.isCodeFile(d.path))\n      .slice(0, 20)\n      .map(d => path.relative(codebaseMap.projectRoot, d.path))\n      .join('\\n');\n\n    const languages = Object.entries(codebaseMap.languages)\n      .map(([lang, count]) => `${lang}: ${count} files`)\n      .join('\\n');\n\n    const entryPoints = codebaseMap.entryPoints\n      .map(ep => path.relative(codebaseMap.projectRoot, ep))\n      .join('\\n');\n\n    return promptManager.renderPrompt('analysis.architecture', {\n      fileStructure,\n      languages,\n      entryPoints\n    });\n  }\n\n  private parseArchitecturePatterns(response: string): ArchitecturePattern[] {\n    try {\n      const jsonMatch = response.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0]);\n        return parsed.patterns || [];\n      }\n    } catch (error) {\n      logger.warn(`Failed to parse architecture patterns: ${error}`);\n    }\n    return [];\n  }\n\n  private calculateArchitectureComplexity(codebaseMap: CodebaseMap): number {\n    // Calculate complexity based on various factors\n    let complexity = 0;\n\n    complexity += Math.min(50, codebaseMap.totalFiles * 0.1); // File count\n    complexity += Object.keys(codebaseMap.languages).length * 5; // Language diversity\n    complexity += codebaseMap.dependencies.length * 0.2; // Dependency count\n\n    return Math.min(100, complexity);\n  }\n\n  private async generateRecommendations(analysis: Partial<ProjectIntelligenceReport>): Promise<string[]> {\n    const recommendations: string[] = [];\n\n    // Security recommendations\n    if (analysis.securityAnalysis && analysis.securityAnalysis.criticalIssues > 0) {\n      recommendations.push(`Address ${analysis.securityAnalysis.criticalIssues} critical security issues immediately`);\n    }\n\n    // Performance recommendations\n    if (analysis.performanceAnalysis && analysis.performanceAnalysis.overallScore < 70) {\n      recommendations.push('Consider performance optimizations to improve overall score');\n    }\n\n    // Documentation recommendations\n    if (analysis.documentationAnalysis && analysis.documentationAnalysis.coveragePercentage < 50) {\n      recommendations.push('Improve documentation coverage - currently below 50%');\n    }\n\n    // Dependency recommendations\n    if (analysis.dependencyAnalysis && analysis.dependencyAnalysis.outdatedDependencies > 5) {\n      recommendations.push(`Update ${analysis.dependencyAnalysis.outdatedDependencies} outdated dependencies`);\n    }\n\n    return recommendations;\n  }\n\n  private setupFileWatcher(): void {\n    if (vscode.workspace.workspaceFolders) {\n      this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/*');\n\n      this.fileWatcher.onDidChange(() => {\n        this.invalidateCache();\n      });\n\n      this.fileWatcher.onDidCreate(() => {\n        this.invalidateCache();\n      });\n\n      this.fileWatcher.onDidDelete(() => {\n        this.invalidateCache();\n      });\n    }\n  }\n\n  private getCachedReport(projectPath: string): ProjectIntelligenceReport | null {\n    const cached = this.analysisCache.get(projectPath);\n    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {\n      return cached.report;\n    }\n    if (cached) {\n      this.analysisCache.delete(projectPath);\n    }\n    return null;\n  }\n\n  private setCachedReport(projectPath: string, report: ProjectIntelligenceReport): void {\n    this.analysisCache.set(projectPath, { report, timestamp: Date.now() });\n  }\n\n  private invalidateCache(): void {\n    this.analysisCache.clear();\n  }\n\n  private async storeReportInMemory(report: ProjectIntelligenceReport): Promise<void> {\n    try {\n      await this.quantumMemory.storeTemporalMemory(\n        'Project intelligence report generated',\n        {\n          filePath: report.codebaseMap.projectRoot,\n          projectContext: path.basename(report.codebaseMap.projectRoot)\n        },\n        'create',\n        { tags: ['project-intelligence', 'analysis', 'report'] }\n      );\n    } catch (error) {\n      logger.warn(`Failed to store report in memory: ${error}`);\n    }\n  }\n\n  /**\n     * Dispose project intelligence system\n     */\n  public dispose(): void {\n    if (this.fileWatcher) {\n      this.fileWatcher.dispose();\n    }\n    this.analysisCache.clear();\n    logger.info('Project-Wide Intelligence disposed');\n  }\n}\n"]}