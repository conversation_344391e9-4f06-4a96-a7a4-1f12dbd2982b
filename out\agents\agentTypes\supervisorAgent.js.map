{"version": 3, "file": "supervisorAgent.js", "sourceRoot": "", "sources": ["../../../src/agents/agentTypes/supervisorAgent.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mDAA6F;AAE7F,yCAAsC;AAGtC,oEAAiE;AAEjE,2DAA+D;AAC/D,2DAAwD;AACxD,8DAA2D;AAC3D,mCAAsC;AAEtC,+DAA4D;AAoF5D,MAAa,eAAgB,SAAQ,aAAK;IAChC,UAAU,GAAuB,IAAI,GAAG,EAAE,CAAC;IAC3C,aAAa,CAAgB;IAC7B,WAAW,CAAqB;IAChC,YAAY,CAAe;IAEnC,8BAA8B;IACtB,aAAa,GAAiB,IAAI,qBAAY,EAAE,CAAC;IACjD,aAAa,GAAkC,IAAI,GAAG,EAAE,CAAC;IACzD,0BAA0B,GAA8B,IAAI,GAAG,EAAE,CAAC;IAE1E,oCAAoC;IAC5B,YAAY,GAAiC,IAAI,GAAG,EAAE,CAAC;IACvD,cAAc,GAA+B,IAAI,GAAG,EAAE,CAAC;IAE/D,mCAAmC;IAC3B,gBAAgB,GAAG,CAAC,CAAC;IACrB,aAAa,GAAG,CAAC,CAAC;IAClB,WAAW,GAAiB,EAAE,CAAC;IAC/B,cAAc,GAA8B,IAAI,GAAG,EAAE,CAAC;IAE9D,YAAY,OAAyB,EAAE,aAA4B;QACjE,KAAK,CAAC;YACJ,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,kBAAkB;YACpC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,kBAAkB;YACxC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,kBAAkB;YAChE,GAAG,OAAO;YACV,IAAI,EAAE,YAAY;YAClB,YAAY,EAAE,CAAC,WAAW,CAAC;YAC3B,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,gCAAkB,CAAC,WAAW,EAAE,CAAC;QACpD,IAAI,CAAC,YAAY,GAAG,2BAAY,CAAC,QAAQ,CAAC;QAE1C,mCAAmC;QACnC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,SAAS,GAAG,mCAAgB,CAAC,eAAe,EAAE,CAAC;YACrD,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC7B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,oCAAoC,IAAI,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,CAAC;QACtF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,IAAI,CAAC;YACH,kCAAkC;YAClC,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAuB;QAC9C,eAAM,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,OAAO,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAEO,mBAAmB,CAAC,KAAuB;QACjD,eAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAEO,mBAAmB,CAAC,KAAuB;QACjD,eAAM,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,OAAO,YAAY,CAAC,CAAC;QAChD,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAEO,gBAAgB,CAAC,KAAuB;QAC9C,eAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAEO,kBAAkB,CAAC,KAAuB;QAChD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACrC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB,CAAC,IAAY,EAAE,KAAY;QAC1C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAEjC,6CAA6C;QAC7C,MAAM,OAAO,GAAG,IAAI,qBAAY,EAAE,CAAC;QACnC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAEvD,eAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,6BAA6B,CAAC,CAAC;IAC/E,CAAC;IAED,YAAY,CAAC,IAAY;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,IAAY,EAAE,IAAY,EAAE,WAAmB;QACnE,oDAAoD;QACpD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,yBAAa,WAAW,IAAI,OAAO,uCAAC,CAAC;YACzD,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAEvF,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,WAAW,GAAgB;oBAC/B,EAAE,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC3B,IAAI,EAAE,IAAI;oBACV,WAAW,EAAE,WAAW;oBACxB,gBAAgB,EAAE,GAAG,IAAI,OAAO;oBAChC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;oBAC/C,IAAI,EAAE,YAAY;oBAClB,YAAY,EAAE,CAAC,IAAI,CAAC;oBACpB,WAAW,EAAE,QAAQ;oBACrB,QAAQ,EAAE,OAAO;oBACjB,WAAW,EAAE,GAAG;oBAChB,SAAS,EAAE,IAAI;iBAChB,CAAC;gBAEF,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;gBAC1C,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACpC,eAAM,CAAC,IAAI,CAAC,uBAAuB,IAAI,QAAQ,CAAC,CAAC;gBACjD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,4BAA4B;QAC5B,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YAC3B,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,WAAW;YACxB,gBAAgB,EAAE,GAAG,IAAI,OAAO;YAChC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;YAC/C,IAAI,EAAE,YAAY;YAClB,YAAY,EAAE,CAAC,WAAW,CAAC;YAC3B,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;YACjB,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;SAChB,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,aAAK,CAAC,WAAW,CAAC,CAAC;QACrC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACpC,eAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,EAAE,CAAC,CAAC;QAEnE,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACL,kBAAkB,CAAC,IAAY,EAAE,IAAY;QAC3C,oBAAoB;QACpB,MAAM,SAAS,GAAG,mCAAgB,CAAC,eAAe,EAAE,CAAC;QAErD,mEAAmE;QACnE,IAAI,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAChC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;YAChB,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAC7B,CAAC;QAEF,qDAAqD;QACrD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAC5B,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;gBACd,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAC5C,CAAC;QACJ,CAAC;QAED,kEAAkE;QAClE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC/D,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,IAAY,EAAE,UAAmC,EAAE;QACrF,eAAM,CAAC,IAAI,CAAC,yCAAyC,IAAI,WAAW,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;QAElG,2CAA2C;QAC3C,MAAM,gBAAgB,GAAsB,OAAO,CAAC,gBAAqC,IAAI;YAC3F,QAAQ,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;YAChC,QAAQ,EAAE,OAAO,CAAC,QAA2D;SAC9E,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QAEpE,IAAI,CAAC;YACH,mBAAmB;YACnB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAE7D,iEAAiE;YACjE,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;YAChF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE;gBACjE,GAAG,OAAO;gBACV,gBAAgB;aACjB,CAAC,CAAC;YAEH,sEAAsE;YACtE,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;YAC1E,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAE1E,6DAA6D;YAC7D,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;YAC9E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAEvE,qDAAqD;YACrD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;YAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,YAAY,CAAC,WAAqB,CAAC,CAAC;YAEnF,wEAAwE;YACxE,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;YACtE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAC3C,aAAa,EACb,MAAM,EACN,QAAQ,EACR,IAAI,EACJ;gBACE,GAAG,OAAO;gBACV,gBAAgB;aACjB,CACF,CAAC;YAEF,yCAAyC;YACzC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;YACzE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAEvF,wBAAwB;YACxB,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;YAExE,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,KAAc,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,IAA8B,EAAE,OAAe,EAAE,IAAc,EAAE,KAAa;QACpG,MAAM,KAAK,GAAqB;YAC9B,IAAI;YACJ,OAAO;YACP,IAAI;YACJ,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,WAAmB,EAAE,SAAiB,EAAE,OAAgB;QACtF,MAAM,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE;gBACxB,IAAI,EAAE,WAAW;gBACjB,EAAE,EAAE,SAAS;gBACb,OAAO;gBACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,qBAAqB,WAAW,OAAO,SAAS,EAAE,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,4CAA4C,SAAS,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,WAAmB,EAAE,OAAgB;QAC/D,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YAC3D,IAAI,OAAO,KAAK,WAAW,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;oBACxB,IAAI,EAAE,WAAW;oBACjB,OAAO;oBACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,+BAA+B,WAAW,gBAAgB,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACI,4BAA4B,CAAC,OAAe;QACjD,OAAO,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,oBAAoB;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAEjD,6BAA6B;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEpC,OAAO,CAAC,GAAG,QAAQ,EAAE,GAAG,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,qCAAqC;QACrC,MAAM,QAAQ,GAAc,EAAE,CAAC;QAE/B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE;YAC/C,2BAA2B;YAC3B,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACrB,MAAM,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBACtD,GAAG,IAAI;oBACP,MAAM,EAAE,OAAO,IAAI,EAAE;iBACtB,CAAC,CAAC,CAAC;gBACJ,QAAQ,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,wBAAwB;QAC7B,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,yBAAyB,CACpC,KAA+E,EAC/E,mBAA4C;QAE5C,MAAM,OAAO,GAAsB,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAE1C,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE;YAC1C,MAAM,EAAE,oBAAoB;YAC5B,SAAS,EAAE,KAAK,CAAC,MAAM;YACvB,QAAQ;SACT,CAAC,CAAC;QAEH,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;gBAC/D,GAAG,IAAI,CAAC,OAAO;gBACf,iBAAiB,EAAE,IAAI;gBACvB,mBAAmB;gBACnB,gBAAgB,EAAE;oBAChB,QAAQ,EAAE,GAAG,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;oBACxC,cAAc,EAAE,QAAQ;oBACxB,QAAQ,EAAE,mBAAmB,CAAC,QAAQ;iBACvC;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEpD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE;gBAC7C,MAAM,EAAE,oBAAoB;gBAC5B,OAAO,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;gBAC9D,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE;gBAC1C,MAAM,EAAE,oBAAoB;gBAC5B,QAAQ;aACT,EAAE,KAAc,CAAC,CAAC;YACnB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW,CAAC,KAAa,EAAE,IAAY,EAAE,OAAgC;QACrF,eAAM,CAAC,IAAI,CAAC,sBAAsB,IAAI,OAAO,CAAC,CAAC;QAE/C,mEAAmE;QACnE,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBACnE,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YACrE,CAAC;iBAAM,CAAC;gBACN,yCAAyC;gBACzC,MAAM,QAAQ,GAAG,MAAM,6BAAa,CAAC,cAAc,CAAC;oBAClD,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE;wBACN,IAAI,EAAE,cAAc;wBACpB,MAAM,EAAE,MAAM;qBACf;iBACF,CAAC,CAAC;gBACH,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxB,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAsB,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjF,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;QAED,sCAAsC;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEnD,qDAAqD;QACrD,MAAM,cAAc,GAAG,6BAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE;YACpE,gBAAgB,EAAE,KAAK;YACvB,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;YAChD,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAgC;YAC7F,aAAa;YACb,IAAI;SACL,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAE3D,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;YAC/D,4BAA4B;YAC5B,OAAO;gBACL,WAAW,EAAE,sBAAsB;gBACnC,cAAc,EAAE,CAAC,IAAI,CAAC;gBACtB,aAAa,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;gBAC3C,YAAY,EAAE,WAAW;gBACzB,iBAAiB,EAAE,KAAK;aACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,wBAAwB,CAAC,YAAqC,EAAE,IAAY;QACxF,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,yCAAyC;QACzC,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CACpC,IAAI,EACJ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EACvD,aAAa,IAAI,OAAO,CACzB,CAAC;QACJ,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEvB,qCAAqC;QACrC,IAAI,YAAY,CAAC,cAAc,EAAE,CAAC;YAChC,KAAK,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC,cAA0B,IAAI,EAAE,CAAC,EAAE,CAAC;gBACxE,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;oBACvB,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oBACzC,IAAI,CAAC,KAAK,EAAE,CAAC;wBACX,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAChC,SAAS,EACT,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EACjE,aAAa,SAAS,QAAQ,CAC/B,CAAC;oBACJ,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAC/B,KAAa,EACb,MAAe,EACf,QAAqC,EACrC,IAAY,EACZ,OAAgC;QAEhC,8DAA8D;QAC9D,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,eAAM,CAAC,IAAI,CAAC,6BAA6B,IAAI,sCAAsC,CAAC,CAAC;YACrF,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,iCAAiC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAE9D,uBAAuB;QACvB,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QACvC,IAAI,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC;QAEzC,OAAO,aAAa,IAAI,aAAa,KAAK,QAAQ,EAAE,CAAC;YACnD,yBAAyB;YACzB,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;gBACpC,eAAM,CAAC,IAAI,CAAC,sCAAsC,aAAa,aAAa,CAAC,CAAC;gBAC9E,MAAM;YACR,CAAC;YACD,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAEhC,wBAAwB;YACxB,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,aAAa,CAAC,CAAC;YAC3E,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,eAAM,CAAC,KAAK,CAAC,QAAQ,aAAa,yBAAyB,CAAC,CAAC;gBAC7D,MAAM;YACR,CAAC;YAED,mBAAmB;YACnB,IAAI,UAAU,GAAG,EAAE,CAAC;YACpB,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBACjC,2CAA2C;gBAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBACnG,IAAI,SAAS,EAAE,CAAC;oBACd,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,KAAK,EAAE;wBACxD,GAAG,OAAO;wBACV,WAAW,EAAE,WAAW,CAAC,EAAE;wBAC3B,eAAe,EAAE,OAAO;qBACzB,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,sDAAsD;oBACtD,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE;wBACxD,GAAG,OAAO;wBACV,WAAW,EAAE,WAAW,CAAC,EAAE;wBAC3B,eAAe,EAAE,OAAO;qBACzB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,IAAI,WAAW,CAAC,IAAI,KAAK,MAAM,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;gBAC3D,mBAAmB;gBACnB,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;wBACvD,KAAK;wBACL,OAAO,EAAE;4BACP,GAAG,OAAO;4BACV,WAAW,EAAE,WAAW,CAAC,EAAE;4BAC3B,eAAe,EAAE,OAAO;yBACzB;qBACF,CAAC,CAAC;oBACH,UAAU,GAAG,UAAU,CAAC,MAAM,IAAI,EAAE,CAAC;gBACvC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,WAAW,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC;oBACpE,UAAU,GAAG,UAAU,KAAK,EAAE,CAAC;gBACjC,CAAC;YACH,CAAC;YAED,iBAAiB;YACjB,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3B,CAAC;YAED,qBAAqB;YACrB,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;YACnF,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,eAAM,CAAC,IAAI,CAAC,+BAA+B,aAAa,+BAA+B,CAAC,CAAC;gBACzF,MAAM;YACR,CAAC;YAED,oCAAoC;YACpC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC1C,CAAC;QAED,sBAAsB;QACtB,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,qBAAqB,CAAC,YAAoB,EAAE,IAAI,GAAG,OAAO;QAC9D,eAAM,CAAC,IAAI,CAAC,uCAAuC,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAExF,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,mCAAmC;YACnC,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gBACrD,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,CAAC;YAED,oEAAoE;YACpE,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAC/D,IAAI,QAAkB,CAAC;YAEvB,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;gBACvC,eAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;gBAC3E,QAAQ,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,YAAY,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YACpF,CAAC;iBAAM,CAAC;gBACN,yCAAyC;gBACzC,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC3D,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,0BAA0B,QAAQ,CAAC,KAAK,CAAC,MAAM,uBAAuB,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;YAE1G,4EAA4E;YAC5E,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAEhE,qDAAqD;YACrD,IAAI,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEjE,4DAA4D;YAC5D,IAAI,YAAY,GAAG,IAAI,CAAC,CAAC,sBAAsB;YAC/C,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACzE,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,iEAAiE,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAEvG,6CAA6C;gBAC7C,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;oBAC9B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;oBAChE,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,iBAAiB;gBACrE,CAAC;gBAED,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC1E,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC/D,CAAC;YAED,gEAAgE;YAChE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAClE,eAAM,CAAC,IAAI,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACzC,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;YAE7B,qCAAqC;YACrC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,CAAC,qBAAqB;gBACvD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,SAAS,WAAW,IAAI,CAAC,gBAAgB,yBAAyB,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;YACjI,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEzC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAChG,SAAS,EAAE,EAAE;gBACb,iBAAiB,EAAE,EAAE;gBACrB,SAAS;gBACT,MAAM,EAAE,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACjE,CAAC;QACJ,CAAC;IACH,CAAC;IAEQ,KAAK,CAAC,GAAG,CAAC,KAAoB,EAAE,UAAwB,EAAE;QACjE,qCAAqC;QACrC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC;QAElC,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAE7E,IAAI,mBAAmB,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC5C,mDAAmD;gBACnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAEpE,sCAAsC;gBACtC,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;oBACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,EAAE;wBACxD,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;qBACxD,CAAC,CAAC;oBACH,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;oBAC1B,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;wBAC3B,MAAM,IAAI,OAAO,QAAQ,CAAC,aAAa,EAAE,CAAC;oBAC5C,CAAC;gBACH,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC;iBACjC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,mEAAmE;gBACnE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,OAAkC,CAAC,CAAC;gBAEjG,sCAAsC;gBACtC,IAAI,MAAM,GAAG,MAAM,CAAC;gBACpB,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;oBACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBAC1D,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;gBAC5B,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,MAAM;iBACf,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;YACvD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;aAC7F,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,KAAY,EAAE,KAAa,EAAE,OAAgC;QACzF,eAAM,CAAC,IAAI,CAAC,wBAAwB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAElD,0CAA0C;QAC1C,MAAM,kBAAkB,GAAG,cAAc,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAClE,MAAM,gBAAgB,GAAqB;YACzC,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAG,OAAO,CAAC,gBAAqC,EAAE,QAAQ;YACxE,QAAQ,EAAE,CAAC,KAAuB,EAAE,EAAE;gBACpC,4CAA4C;gBAC5C,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE;oBAC/C,cAAc,EAAE,KAAK,CAAC,IAAI;oBAC1B,cAAc,EAAE,KAAK;iBACtB,CAAC,CAAC;gBAEH,6CAA6C;gBAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAoC,CAAC;gBAClE,IAAI,YAAY,EAAE,QAAQ,EAAE,CAAC;oBAC3B,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;SACF,CAAC;QAEF,8BAA8B;QAC9B,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE;YAC/C,MAAM,EAAE,OAAO;YACf,cAAc,EAAE,KAAK,CAAC,IAAI;YAC1B,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;SACvC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAkB;YAChC,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,MAAM;SACb,CAAC;QAEF,MAAM,YAAY,GAAiB;YACjC,GAAG,OAAO;YACV,gBAAgB;YAChB,SAAS,EAAE;gBACT,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;gBAC5B,WAAW,EAAE,IAAI,CAAC,IAAI;gBACtB,aAAa,EAAE,KAAK;gBACpB,YAAY,EAAE,IAAI,CAAC,EAAE;aACtB;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YAEzD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,eAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,IAAI,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC5D,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE;oBAC/C,MAAM,EAAE,OAAO;oBACf,cAAc,EAAE,KAAK,CAAC,IAAI;oBAC1B,KAAK,EAAE,MAAM,CAAC,KAAK;iBACpB,CAAC,CAAC;gBACH,MAAM,IAAI,KAAK,CAAC,SAAS,KAAK,CAAC,IAAI,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACjE,CAAC;YAED,gCAAgC;YAChC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE;gBAC/C,MAAM,EAAE,UAAU;gBAClB,cAAc,EAAE,KAAK,CAAC,IAAI;gBAC1B,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;aACjD,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE;gBAC/C,MAAM,EAAE,OAAO;gBACf,cAAc,EAAE,KAAK,CAAC,IAAI;gBAC1B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,aAAqB,EAAE,IAAY,EAAE,OAAgC;QACnH,eAAM,CAAC,IAAI,CAAC,uCAAuC,IAAI,OAAO,CAAC,CAAC;QAEhE,gDAAgD;QAChD,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC;QAEpG,2CAA2C;QAC3C,MAAM,YAAY,GAAG;;;;;;UAMf,aAAa;;;UAGb,MAAM;;;UAGN,WAAW;;;;;;yDAMoC,IAAI;;;;SAIpD,CAAC;QAEN,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACrC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,cAAc,CAAC,YAAoB,EAAE,IAAY;QAC7D,MAAM,cAAc,GAAG;;;;;;UAMjB,YAAY;;;UAGZ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;SA0BL,CAAC;QAEN,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAEvD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACpC,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;gBACnC,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBAC/D,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;gBACvB,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE;gBACrC,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,MAAM;gBAC3C,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,QAAQ;gBACvC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,IAAI,CAAC,6BAA6B,CAAC;aAC/E,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;YACpD,sBAAsB;YACtB,OAAO;gBACL,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;gBACxB,WAAW,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBAC3C,KAAK,EAAE,CAAC;wBACN,EAAE,EAAE,QAAQ;wBACZ,WAAW,EAAE,kBAAkB;wBAC/B,IAAI,EAAE,gBAAgB;wBACtB,KAAK,EAAE,IAAI;wBACX,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;wBACnC,cAAc,EAAE,iBAAiB;wBACjC,YAAY,EAAE,EAAE;qBACjB,CAAC;gBACF,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,MAAM;gBACrB,UAAU,EAAE,QAAQ;gBACpB,kBAAkB,EAAE,CAAC,6BAA6B,CAAC;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAAC,QAAkB,EAAE,oBAA4B;QAC5E,eAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;QAE5D,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,iBAAiB,GAAuB,EAAE,CAAC;QACjD,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,oCAAoC;QACpC,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QACxC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE9C,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC;gBACH,sCAAsC;gBACtC,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjF,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/B,eAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,kCAAkC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC3F,+EAA+E;gBACjF,CAAC;gBAED,eAAM,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBAEnD,0CAA0C;gBAC1C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE/C,+BAA+B;gBAC/B,MAAM,WAAW,GAAG;oBAClB,oBAAoB;oBACpB,QAAQ;oBACR,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,WAAW;oBAC5B,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;iBACzC,CAAC;gBAEF,mBAAmB;gBACnB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;gBAEpE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,CAAC;gBAC/C,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAE3B,wCAAwC;gBACxC,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;oBACnC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,QAAQ,GAAG,QAAQ,IAAI,CAAC,EAAE,YAAY,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrG,eAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,SAAS;YACT,iBAAiB;YACjB,SAAS,EAAE,CAAC,EAAE,wBAAwB;YACtC,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;SAC/C,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAAC,IAAc;QAC1C,8CAA8C;QAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,wCAAwC;YACxC,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAChC,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EACnE,aAAa,IAAI,CAAC,KAAK,QAAQ,CAChC,CAAC;QACJ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW,CAAC,IAAc,EAAE,KAAY,EAAE,OAAgC;QACtF,MAAM,UAAU,GAAG;;;;gBAIP,IAAI,CAAC,EAAE;yBACE,IAAI,CAAC,WAAW;kBACvB,IAAI,CAAC,IAAI;6BACE,IAAI,CAAC,cAAc;;;UAGtC,OAAO,CAAC,oBAAoB;;;UAG5B,KAAK,CAAC,IAAI,CAAE,OAAO,CAAC,eAAuC,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;UAG5H,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;SAMtB,CAAC;QAEN,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QACtE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,MAAkB,EAAE,QAAkB;QACrE,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEzC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,oBAAoB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,sCAAsC;QACtC,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YACpD,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG;;;;kBAIf,SAAS;;;kBAGT,MAAM,CAAC,MAAM;;;kBAGb,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;iBAKlC,CAAC;gBAEV,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAE/D,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACrD,MAAM,CAAC,IAAI,CAAC,0BAA0B,SAAS,MAAM,gBAAgB,EAAE,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,yBAAyB,SAAS,MAAM,KAAK,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAC/B,YAAoB,EACpB,YAAwB,EACxB,UAAgD;QAEhD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,eAAM,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;QAEhE,qDAAqD;QACrD,MAAM,gBAAgB,GAAG;;;;UAInB,YAAY;;;UAGZ,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;;;UAGrC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;SAS7B,CAAC;QAEN,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QACjE,eAAM,CAAC,IAAI,CAAC,wBAAwB,kBAAkB,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAE/E,0DAA0D;QAC1D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAC7C,GAAG,YAAY,qDAAqD,kBAAkB,EAAE,EACxF,OAAO,CACR,CAAC;QAEF,6BAA6B;QAC7B,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,QAAkB,EAAE,MAAkB,EAAE,UAAmC;QAC1G,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAE/C,MAAM,WAAW,GAAG,GAAG,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QAEtF,MAAM,YAAY,GAAG;YACnB,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU,EAAE,IAAI,CAAC,gBAAgB;YACjC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;SACvD,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAEnD,iDAAiD;QACjD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;oBAC/B,OAAO,EAAE,4BAA4B,QAAQ,CAAC,WAAW,EAAE;oBAC3D,QAAQ,EAAE;wBACR,MAAM,EAAE,OAAuB;wBAC/B,IAAI,EAAE,SAAuB;wBAC7B,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;wBAClC,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC;qBACtD;iBACF,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,MAAc,EAAE,IAAY;QAC1D,0DAA0D;QAC1D,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,kCAAkC;QAClC,MAAM,oBAAoB,GAAG;YAC3B,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU;YACrD,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU;YACrD,gBAAgB,EAAE,gBAAgB,EAAE,mBAAmB;SACxD,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACzC,MAAM,uBAAuB,GAAG,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CACpE,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAChC,CAAC;QAEF,wEAAwE;QACxE,OAAO,uBAAuB,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACvE,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,YAAoB;QAClD,MAAM,iBAAiB,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QACrD,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,uBAAuB;QAE9C,KAAK,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YAC5E,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAC7C,iBAAiB,EACjB,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAChD,CAAC;YAEF,IAAI,UAAU,GAAG,SAAS,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBACnD,eAAM,CAAC,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;gBAC7F,OAAO,YAAY,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,0BAA0B,CAAC,YAAoB,EAAE,IAAY,EAAE,WAAyB;QACpG,MAAM,YAAY,GAAG,WAAW,CAAC,QAAQ,CAAC;QAE1C,2EAA2E;QAC3E,MAAM,aAAa,GAAa;YAC9B,EAAE,EAAE,kBAAkB,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YAC1C,WAAW,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;YAC3C,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBACvC,GAAG,IAAI;gBACP,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,cAAc,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBAChD,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,gCAAgC;aAC/D,CAAC,CAAC;YACH,YAAY,EAAE,YAAY,CAAC,YAAY,IAAI,EAAE;YAC7C,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,aAAa,GAAG,GAAG,EAAE,KAAK,CAAC,EAAE,sBAAsB;YACxF,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,IAAI,CAAC,GAAG,IAAI,8BAA8B,CAAC;SAC/F,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QAC1E,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,KAAa,EAAE,KAAa;QAC1D,2CAA2C;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE5D,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEzD,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;QAExD,OAAO,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,OAAO;YACL,WAAW;YACX,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,OAAO;YACP,UAAU;YACV,OAAO;SACR,CAAC;IACJ,CAAC;CACF;AArxCD,0CAqxCC;AAED,8CAA8C;AAC9C,kBAAe,eAAe,CAAC", "sourcesContent": ["import { <PERSON>, <PERSON><PERSON>onte<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>n<PERSON>, AgentRunResult } from '../agentUtilities/agent';\nimport { AgentConfig, AgentRole } from '../../types/agent';\nimport { logger } from '../../logger';\nimport { MemorySource, MemoryType } from '../../memory/types';\nimport receiverAgent from './receiverAgent';\nimport { workflowRegistry } from '../workflows/workflowRegistry';\nimport { GraphDefinition } from '../workflows/graphTypes';\nimport { GoddessModeManager } from '../../goddess/goddessMode';\nimport { ToolRegistry } from '../../tools/toolRegistry';\nimport { memoryManager } from '../../memory/memoryManager';\nimport { EventEmitter } from 'events';\nimport * as vscode from 'vscode';\nimport { promptManager } from '../../prompts/promptManager';\n\n// Streaming interfaces for real-time agent communication\nexport interface AgentStreamEvent {\n  type: 'start' | 'progress' | 'data' | 'tool_call' | 'delegation' | 'complete' | 'error';\n  agentId: string;\n  data?: unknown;\n  error?: Error;\n  timestamp: number;\n}\n\ninterface MCPConnection {\n  tools?: Array<{\n    name: string;\n    description?: string;\n    parameters?: Record<string, unknown>;\n    [key: string]: unknown;\n  }>;\n  [key: string]: unknown;\n}\n\ninterface LearningData {\n  taskPlan: TaskPlan;\n  result: TaskResult;\n  validation: unknown;\n  iterations: number;\n  timestamp: number;\n  success: boolean;\n}\n\ninterface SupervisorConfig extends Partial<AgentConfig> {\n  id?: string;\n  name?: string;\n  systemPromptName?: string;\n  role?: AgentRole;\n  capabilities?: string[];\n  llmProvider?: string;\n  llmModel?: string;\n}\n\nexport interface StreamingContext {\n  streamId: string;\n  parentStreamId?: string;\n  onStream?: (_streamEvent: AgentStreamEvent) => void;\n  cancellationToken?: vscode.CancellationToken;\n}\n\n// Phase 2 interfaces for autonomous capabilities\nexport interface TaskPlan {\n    id: string;\n    description: string;\n    steps: TaskStep[];\n    dependencies: string[];\n    estimatedTime: number;\n    complexity: 'low' | 'medium' | 'high';\n    validationCriteria: string[];\n}\n\nexport interface TaskStep {\n    id: string;\n    description: string;\n    type: 'analysis' | 'implementation' | 'testing' | 'validation';\n    agent: string;\n    tools: string[];\n    expectedOutput: string;\n    dependencies: string[];\n}\n\nexport interface TaskResult {\n    success: boolean;\n    output: string;\n    artifacts: string[];\n    validationResults: ValidationResult[];\n    timeSpent: number;\n    errors?: string[];\n}\n\nexport interface ValidationResult {\n    criterion: string;\n    passed: boolean;\n    details: string;\n    suggestions?: string[];\n}\n\nexport class SupervisorAgent extends Agent {\n  private modeAgents: Map<string, Agent> = new Map();\n  private receiverAgent: receiverAgent;\n  private goddessMode: GoddessModeManager;\n  private toolRegistry: ToolRegistry;\n\n  // Streaming and communication\n  private streamEmitter: EventEmitter = new EventEmitter();\n  private activeStreams: Map<string, StreamingContext> = new Map();\n  private agentCommunicationChannels: Map<string, EventEmitter> = new Map();\n\n  // Enhanced tool and workflow access\n  private allWorkflows: Map<string, GraphDefinition> = new Map();\n  private mcpConnections: Map<string, MCPConnection> = new Map();\n\n  // Phase 2: Autonomous capabilities\n  private currentIteration = 0;\n  private maxIterations = 5;\n  private taskHistory: TaskResult[] = [];\n  private learningMemory: Map<string, LearningData> = new Map();\n\n  constructor(_config: SupervisorConfig, receiverAgent: receiverAgent) {\n    super({\n      id: _config.id || 'supervisor-agent',\n      name: _config.name || 'Supervisor Agent',\n      systemPromptName: _config.systemPromptName || 'agent.supervisor',\n      ..._config,\n      role: 'supervisor',\n      capabilities: ['supervise'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4'\n    });\n    this.receiverAgent = receiverAgent;\n    this.goddessMode = GoddessModeManager.getInstance();\n    this.toolRegistry = ToolRegistry.instance;\n\n    // Initialize enhanced capabilities\n    this.initializeWorkflows();\n    this.initializeMCPConnections();\n    this.setupStreamingHandlers();\n  }\n\n  /**\n   * Initialize all available workflows for supervisor access\n   */\n  private initializeWorkflows(): void {\n    try {\n      // Get all registered workflows\n      const workflows = workflowRegistry.getAllWorkflows();\n      workflows.forEach((workflow) => {\n        this.allWorkflows.set(workflow.id, workflow);\n      });\n\n      logger.info(`SupervisorAgent initialized with ${this.allWorkflows.size} workflows`);\n    } catch (error) {\n      logger.error('Failed to initialize workflows:', error);\n    }\n  }\n\n  /**\n   * Initialize MCP connections for enhanced tool access\n   */\n  private initializeMCPConnections(): void {\n    try {\n      // Initialize MCP connections here\n      logger.info('SupervisorAgent MCP connections initialized');\n    } catch (error) {\n      logger.error('Failed to initialize MCP connections:', error);\n    }\n  }\n\n  /**\n   * Setup streaming event handlers for real-time communication\n   */\n  private setupStreamingHandlers(): void {\n    this.streamEmitter.on('agentStart', this.handleAgentStart.bind(this));\n    this.streamEmitter.on('agentProgress', this.handleAgentProgress.bind(this));\n    this.streamEmitter.on('agentComplete', this.handleAgentComplete.bind(this));\n    this.streamEmitter.on('agentError', this.handleAgentError.bind(this));\n  }\n    \n  /**\n   * Streaming event handlers\n   */\n  private handleAgentStart(event: AgentStreamEvent): void {\n    logger.info(`Agent ${event.agentId} started`);\n    this.broadcastToStreams(event);\n  }\n\n  private handleAgentProgress(event: AgentStreamEvent): void {\n    logger.debug(`Agent ${event.agentId} progress: ${event.data}`);\n    this.broadcastToStreams(event);\n  }\n\n  private handleAgentComplete(event: AgentStreamEvent): void {\n    logger.info(`Agent ${event.agentId} completed`);\n    this.broadcastToStreams(event);\n    this.activeStreams.delete(event.agentId);\n  }\n\n  private handleAgentError(event: AgentStreamEvent): void {\n    logger.error(`Agent ${event.agentId} error:`, event.error);\n    this.broadcastToStreams(event);\n    this.activeStreams.delete(event.agentId);\n  }\n\n  private broadcastToStreams(event: AgentStreamEvent): void {\n    this.activeStreams.forEach((context) => {\n      if (context.onStream) {\n        context.onStream(event);\n      }\n    });\n  }\n\n  registerModeAgent(mode: string, agent: Agent): void {\n    this.modeAgents.set(mode, agent);\n\n    // Setup communication channel for this agent\n    const channel = new EventEmitter();\n    this.agentCommunicationChannels.set(agent.id, channel);\n\n    logger.info(`Registered agent for mode: ${mode} with communication channel`);\n  }\n    \n  getModeAgent(mode: string): Agent | undefined {\n    return this.modeAgents.get(mode);\n  }\n    \n  async createModeAgent(mode: string, name: string, description: string): Promise<Agent> {\n    // Try to dynamically import the mode-specific agent\n    try {\n      const agentModule = await import(`./modes/${mode}Agent`);\n      const AgentClass = agentModule[`${mode.charAt(0).toUpperCase() + mode.slice(1)}Agent`];\n            \n      if (AgentClass) {\n        const agentConfig: AgentConfig = {\n          id: `${mode}-${Date.now()}`,\n          name: name,\n          description: description,\n          systemPromptName: `${mode}Agent`,\n          tools: ['file', 'search', 'memory', 'workflow'],\n          role: 'specialist',\n          capabilities: [mode],\n          llmProvider: 'openai',\n          llmModel: 'gpt-4',\n          temperature: 0.7,\n          maxTokens: 2000\n        };\n                \n        const agent = new AgentClass(agentConfig);\n        this.registerModeAgent(mode, agent);\n        logger.info(`Dynamically created ${mode} agent`);\n        return agent;\n      }\n    } catch (error) {\n      logger.warn(`Failed to import specialized agent for mode ${mode}: ${error}`);\n    }\n        \n    // Fallback to generic agent\n    const agentConfig: AgentConfig = {\n      id: `${mode}-${Date.now()}`,\n      name: name,\n      description: description,\n      systemPromptName: `${mode}Agent`,\n      tools: ['file', 'search', 'memory', 'workflow'],\n      role: 'supervisor',\n      capabilities: ['supervise'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4',\n      temperature: 0.7,\n      maxTokens: 2000\n    };\n        \n    const agent = new Agent(agentConfig);\n    this.registerModeAgent(mode, agent);\n    logger.info(`Dynamically created generic agent for mode: ${mode}`);\n        \n    return agent;\n  }\n    \n  /**\n     * Get appropriate workflow for a mode and task\n     */\n  getWorkflowForMode(mode: string, task: string): GraphDefinition | undefined {\n    // Get all workflows\n    const workflows = workflowRegistry.getAllWorkflows();\n        \n    // First try to find a workflow specifically for this mode and task\n    let workflow = workflows.find(w => \n      w.tags?.includes(mode) && \n            w.tags?.includes(task)\n    );\n        \n    // If not found, try to find a workflow for this mode\n    if (!workflow) {\n      workflow = workflows.find(w => \n        w.tags?.includes(mode) || \n                w.name.toLowerCase().includes(mode)\n      );\n    }\n        \n    // If still not found, use a default workflow based on methodology\n    if (!workflow) {\n      if (mode === 'edit' || mode === 'refactor' || mode === 'debug') {\n        workflow = workflows.find(w => w.methodology === 'devops');\n      } else {\n        workflow = workflows.find(w => w.methodology === 'waterfall');\n      }\n    }\n        \n    return workflow;\n  }\n    \n  async processRequest(input: string, mode: string, context: Record<string, unknown> = {}): Promise<string> {\n    logger.info(`SupervisorAgent processing request in ${mode} mode: \"${input.substring(0, 50)}...\"`);\n\n    // Create streaming context if not provided\n    const streamingContext: StreamingContext = (context.streamingContext as StreamingContext) || {\n      streamId: `stream_${Date.now()}`,\n      onStream: context.onStream as ((event: AgentStreamEvent) => void) | undefined\n    };\n\n    this.activeStreams.set(streamingContext.streamId, streamingContext);\n\n    try {\n      // Emit start event\n      this.emitStreamEvent('start', 'supervisor', { input, mode });\n\n      // Step 1: Process input through the ReceiverAgent with streaming\n      this.emitStreamEvent('progress', 'supervisor', { step: 'receiver_processing' });\n      const enhancedInput = await this.receiverAgent.processInput(input, {\n        ...context,\n        streamingContext\n      });\n\n      // Step 2: Analyze the task to determine required agents and workflows\n      this.emitStreamEvent('progress', 'supervisor', { step: 'task_analysis' });\n      const taskAnalysis = await this.analyzeTask(enhancedInput, mode, context);\n\n      // Step 3: Get or create the appropriate agents for this task\n      this.emitStreamEvent('progress', 'supervisor', { step: 'agent_preparation' });\n      const agents = await this.getOrCreateAgentsForTask(taskAnalysis, mode);\n\n      // Step 4: Get the appropriate workflow for this task\n      this.emitStreamEvent('progress', 'supervisor', { step: 'workflow_selection' });\n      const workflow = this.getWorkflowForMode(mode, taskAnalysis.primaryTask as string);\n\n      // Step 5: Execute the task using the workflow and agents with streaming\n      this.emitStreamEvent('progress', 'supervisor', { step: 'execution' });\n      const result = await this.executeWithWorkflow(\n        enhancedInput,\n        agents,\n        workflow,\n        mode,\n        {\n          ...context,\n          streamingContext\n        }\n      );\n\n      // Step 6: Review and finalize the result\n      this.emitStreamEvent('progress', 'supervisor', { step: 'finalization' });\n      const finalResult = await this.reviewAndFinalize(result, enhancedInput, mode, context);\n\n      // Emit completion event\n      this.emitStreamEvent('complete', 'supervisor', { result: finalResult });\n\n      return finalResult;\n    } catch (error) {\n      this.emitStreamEvent('error', 'supervisor', undefined, error as Error);\n      throw error;\n    } finally {\n      this.activeStreams.delete(streamingContext.streamId);\n    }\n  }\n\n  /**\n   * Emit streaming events for real-time updates\n   */\n  private emitStreamEvent(type: AgentStreamEvent['type'], agentId: string, data?: unknown, error?: Error): void {\n    const event: AgentStreamEvent = {\n      type,\n      agentId,\n      data,\n      error,\n      timestamp: Date.now()\n    };\n\n    this.streamEmitter.emit(`agent${type.charAt(0).toUpperCase() + type.slice(1)}`, event);\n  }\n\n  /**\n   * Enable direct agent-to-agent communication\n   */\n  public enableAgentCommunication(fromAgentId: string, toAgentId: string, message: unknown): void {\n    const toChannel = this.agentCommunicationChannels.get(toAgentId);\n    if (toChannel) {\n      toChannel.emit('message', {\n        from: fromAgentId,\n        to: toAgentId,\n        message,\n        timestamp: Date.now()\n      });\n\n      logger.info(`Message sent from ${fromAgentId} to ${toAgentId}`);\n    } else {\n      logger.warn(`No communication channel found for agent ${toAgentId}`);\n    }\n  }\n\n  /**\n   * Broadcast message to all agents\n   */\n  public broadcastToAllAgents(fromAgentId: string, message: unknown): void {\n    this.agentCommunicationChannels.forEach((channel, agentId) => {\n      if (agentId !== fromAgentId) {\n        channel.emit('broadcast', {\n          from: fromAgentId,\n          message,\n          timestamp: Date.now()\n        });\n      }\n    });\n\n    logger.info(`Broadcast message sent from ${fromAgentId} to all agents`);\n  }\n\n  /**\n   * Get communication channel for an agent\n   */\n  public getAgentCommunicationChannel(agentId: string): EventEmitter | undefined {\n    return this.agentCommunicationChannels.get(agentId);\n  }\n\n  /**\n   * Enhanced tool access - supervisor has access to ALL tools\n   */\n  public getAllAvailableTools(): unknown[] {\n    const allTools = this.toolRegistry.getAllTools();\n\n    // Add MCP tools if available\n    const mcpTools = this.getMCPTools();\n\n    return [...allTools, ...mcpTools];\n  }\n\n  /**\n   * Get MCP tools\n   */\n  private getMCPTools(): unknown[] {\n    // Implementation for MCP tool access\n    const mcpTools: unknown[] = [];\n\n    this.mcpConnections.forEach((connection, name) => {\n      // Add MCP connection tools\n      if (connection.tools) {\n        const toolsWithSource = connection.tools.map((tool) => ({\n          ...tool,\n          source: `mcp:${name}`\n        }));\n        mcpTools.push(...toolsWithSource);\n      }\n    });\n\n    return mcpTools;\n  }\n\n  /**\n   * Enhanced workflow access - supervisor knows all workflows\n   */\n  public getAllAvailableWorkflows(): Map<string, GraphDefinition> {\n    return new Map(this.allWorkflows);\n  }\n\n  /**\n   * Execute parallel agent tasks with streaming coordination\n   */\n  public async executeParallelAgentTasks(\n    tasks: Array<{ agent: Agent; input: string; context: Record<string, unknown> }>,\n    coordinationContext: Record<string, unknown>\n  ): Promise<string[]> {\n    const results: Promise<string>[] = [];\n    const streamId = `parallel_${Date.now()}`;\n\n    this.emitStreamEvent('start', 'supervisor', {\n      action: 'parallel_execution',\n      taskCount: tasks.length,\n      streamId\n    });\n\n    for (const task of tasks) {\n      const taskPromise = this.delegateToAgent(task.agent, task.input, {\n        ...task.context,\n        parallelExecution: true,\n        coordinationContext,\n        streamingContext: {\n          streamId: `${streamId}_${task.agent.id}`,\n          parentStreamId: streamId,\n          onStream: coordinationContext.onStream\n        }\n      });\n\n      results.push(taskPromise);\n    }\n\n    try {\n      const completedResults = await Promise.all(results);\n\n      this.emitStreamEvent('complete', 'supervisor', {\n        action: 'parallel_execution',\n        results: completedResults.map(r => r.substring(0, 50) + '...'),\n        streamId\n      });\n\n      return completedResults;\n    } catch (error) {\n      this.emitStreamEvent('error', 'supervisor', {\n        action: 'parallel_execution',\n        streamId\n      }, error as Error);\n      throw error;\n    }\n  }\n    \n  /**\n     * Analyze the task to determine required agents and workflows\n     */\n  private async analyzeTask(input: string, mode: string, context: Record<string, unknown>): Promise<Record<string, unknown>> {\n    logger.info(`Analyzing task for ${mode} mode`);\n\n    // Get relevant memories for context using AgentMemory if available\n    let memoryContext = '';\n    try {\n      if (this.getMemory) {\n        const relevant = await this.getMemory().getRelevantMemories(input);\n        memoryContext = this.getMemory().formatMemoriesForPrompt(relevant);\n      } else {\n        // Fallback to direct memoryManager usage\n        const memories = await memoryManager.searchMemories({\n          query: input,\n          limit: 5,\n          filter: {\n            type: 'conversation',\n            source: 'user'\n          }\n        });\n        if (memories.length > 0) {\n          memoryContext = memories.map((m: { content: string }) => m.content).join('\\n');\n        }\n      }\n    } catch (error) {\n      logger.warn('Failed to retrieve memories for task analysis:', error);\n    }\n\n    // Get available agents for delegation\n    const availableAgents = this._getAvailableAgents();\n\n    // Use promptManager for consistent prompt generation\n    const analysisPrompt = promptManager.renderPrompt('agent.supervisor', {\n      processedRequest: input,\n      availableAgents: JSON.stringify(availableAgents),\n      contextContent: context ? JSON.stringify(context, null, 2) : 'No additional context provided',\n      memoryContext,\n      mode\n    });\n\n    const analysisResult = await this.generate(analysisPrompt);\n        \n    try {\n      return JSON.parse(analysisResult);\n    } catch (error) {\n      logger.error(`Failed to parse task analysis result: ${error}`);\n      // Return a default analysis\n      return {\n        primaryTask: 'process user request',\n        requiredAgents: [mode],\n        requiredTools: ['file', 'search', 'memory'],\n        workflowType: 'waterfall',\n        parallelExecution: false\n      };\n    }\n  }\n    \n  /**\n     * Get or create agents needed for a task\n     */\n  private async getOrCreateAgentsForTask(taskAnalysis: Record<string, unknown>, mode: string): Promise<Agent[]> {\n    const agents: Agent[] = [];\n        \n    // Always include the mode-specific agent\n    let modeAgent = this.getModeAgent(mode);\n    if (!modeAgent) {\n      modeAgent = await this.createModeAgent(\n        mode, \n        `${mode.charAt(0).toUpperCase() + mode.slice(1)} Agent`, \n        `Agent for ${mode} mode`\n      );\n    }\n    agents.push(modeAgent);\n        \n    // Add any additional required agents\n    if (taskAnalysis.requiredAgents) {\n      for (const agentType of (taskAnalysis.requiredAgents as string[] || [])) {\n        if (agentType !== mode) {\n          let agent = this.getModeAgent(agentType);\n          if (!agent) {\n            agent = await this.createModeAgent(\n              agentType,\n              `${agentType.charAt(0).toUpperCase() + agentType.slice(1)} Agent`,\n              `Agent for ${agentType} tasks`\n            );\n          }\n          agents.push(agent);\n        }\n      }\n    }\n        \n    return agents;\n  }\n    \n  /**\n     * Execute a task using a workflow and agents\n     */\n  private async executeWithWorkflow(\n    input: string, \n    agents: Agent[], \n    workflow: GraphDefinition | undefined, \n    mode: string, \n    context: Record<string, unknown>\n  ): Promise<string> {\n    // If no workflow is available, use the primary agent directly\n    if (!workflow || !workflow.nodes || workflow.nodes.length === 0) {\n      logger.info(`No workflow available for ${mode} mode. Using primary agent directly.`);\n      return this.delegateToAgent(agents[0], input, context);\n    }\n        \n    logger.info(`Executing task with workflow: ${workflow.name}`);\n        \n    // Execute the workflow\n    const results: string[] = [];\n    const visitedNodes = new Set<string>();\n    let currentNodeId = workflow.startNodeId;\n        \n    while (currentNodeId && currentNodeId !== 'output') {\n      // Prevent infinite loops\n      if (visitedNodes.has(currentNodeId)) {\n        logger.warn(`Detected cycle in workflow at node ${currentNodeId}. Breaking.`);\n        break;\n      }\n      visitedNodes.add(currentNodeId);\n            \n      // Find the current node\n      const currentNode = workflow.nodes.find(node => node.id === currentNodeId);\n      if (!currentNode) {\n        logger.error(`Node ${currentNodeId} not found in workflow.`);\n        break;\n      }\n            \n      // Process the node\n      let nodeResult = '';\n      if (currentNode.type === 'agent') {\n        // Find the appropriate agent for this node\n        const nodeAgent = agents.find(a => a.name.toLowerCase().includes(currentNode.label.toLowerCase()));\n        if (nodeAgent) {\n          nodeResult = await this.delegateToAgent(nodeAgent, input, {\n            ...context,\n            currentNode: currentNode.id,\n            previousResults: results\n          });\n        } else {\n          // Use the primary agent if no specific agent is found\n          nodeResult = await this.delegateToAgent(agents[0], input, {\n            ...context,\n            currentNode: currentNode.id,\n            previousResults: results\n          });\n        }\n      } else if (currentNode.type === 'tool' && currentNode.tool) {\n        // Execute the tool\n        try {\n          const toolResult = await currentNode.tool.execute('run', {\n            input,\n            context: {\n              ...context,\n              currentNode: currentNode.id,\n              previousResults: results\n            }\n          });\n          nodeResult = toolResult.output || '';\n        } catch (error) {\n          logger.error(`Error executing tool ${currentNode.label}: ${error}`);\n          nodeResult = `Error: ${error}`;\n        }\n      }\n            \n      // Add the result\n      if (nodeResult) {\n        results.push(nodeResult);\n      }\n            \n      // Find the next node\n      const outgoingEdges = workflow.edges.filter(edge => edge.source === currentNodeId);\n      if (outgoingEdges.length === 0) {\n        logger.warn(`No outgoing edges from node ${currentNodeId}. Workflow execution stopped.`);\n        break;\n      }\n            \n      // For now, just take the first edge\n      currentNodeId = outgoingEdges[0].target;\n    }\n        \n    // Combine the results\n    return results.join('\\n\\n');\n  }\n    \n  /**\n     * Phase 2: Autonomous task execution with self-correction (Optimized)\n     */\n  async executeAutonomousTask(requirements: string, mode = 'agent'): Promise<TaskResult> {\n    logger.info(`Starting autonomous task execution: ${requirements.substring(0, 100)}...`);\n\n    this.currentIteration = 0;\n    const startTime = Date.now();\n\n    try {\n      // Early validation of requirements\n      if (!requirements || requirements.trim().length < 10) {\n        throw new Error('Requirements must be at least 10 characters long');\n      }\n\n      // Check if we have similar task in learning memory for optimization\n      const similarTask = this.findSimilarTaskInMemory(requirements);\n      let taskPlan: TaskPlan;\n\n      if (similarTask && similarTask.success) {\n        logger.info('Found similar successful task in memory, optimizing plan...');\n        taskPlan = await this.optimizeTaskPlanFromMemory(requirements, mode, similarTask);\n      } else {\n        // Step 1: Create comprehensive task plan\n        taskPlan = await this.createTaskPlan(requirements, mode);\n      }\n\n      logger.info(`Created task plan with ${taskPlan.steps.length} steps (complexity: ${taskPlan.complexity})`);\n\n      // Step 2: Execute the task plan with early termination on critical failures\n      let result = await this.executeTaskPlan(taskPlan, requirements);\n\n      // Step 3: Validate the result (optimized validation)\n      let validation = await this.validateTaskResult(result, taskPlan);\n\n      // Step 4: Self-correct if needed (with exponential backoff)\n      let backoffDelay = 1000; // Start with 1 second\n      while (!validation.success && this.currentIteration < this.maxIterations) {\n        this.currentIteration++;\n        logger.info(`Task validation failed. Attempting self-correction (iteration ${this.currentIteration})`);\n\n        // Add backoff delay to prevent rapid retries\n        if (this.currentIteration > 1) {\n          await new Promise(resolve => setTimeout(resolve, backoffDelay));\n          backoffDelay = Math.min(backoffDelay * 2, 10000); // Max 10 seconds\n        }\n\n        result = await this.selfCorrectAndRetry(requirements, result, validation);\n        validation = await this.validateTaskResult(result, taskPlan);\n      }\n\n      // Step 5: Learn from the experience (async to not block return)\n      this.learnFromExecution(taskPlan, result, validation).catch(error => {\n        logger.warn(`Learning from execution failed: ${error}`);\n      });\n\n      const timeSpent = Date.now() - startTime;\n      result.timeSpent = timeSpent;\n\n      // Store in history (with size limit)\n      this.taskHistory.push(result);\n      if (this.taskHistory.length > 50) { // Limit history size\n        this.taskHistory = this.taskHistory.slice(-50);\n      }\n\n      logger.info(`Autonomous task completed in ${timeSpent}ms with ${this.currentIteration} iterations (success: ${result.success})`);\n      return result;\n\n    } catch (error) {\n      logger.error(`Autonomous task execution failed: ${error}`);\n      const timeSpent = Date.now() - startTime;\n\n      return {\n        success: false,\n        output: `Autonomous execution failed: ${error instanceof Error ? error.message : String(error)}`,\n        artifacts: [],\n        validationResults: [],\n        timeSpent,\n        errors: [error instanceof Error ? error.message : String(error)]\n      };\n    }\n  }\n\n  override async run(input: AgentRunInput, context: AgentContext = {}): Promise<AgentRunResult> {\n    // Extract mode from input or context\n    const mode = input.mode || 'chat';\n\n    try {\n      // Check if this should be an autonomous task\n      const isAutonomousRequest = this.shouldUseAutonomousMode(input.prompt, mode);\n\n      if (isAutonomousRequest && mode === 'agent') {\n        // Use autonomous execution for complex agent tasks\n        const result = await this.executeAutonomousTask(input.prompt, mode);\n\n        // Enhance with Goddess Mode if active\n        let output = result.output;\n        if (this.goddessMode.isGoddessMode()) {\n          const enhanced = this.goddessMode.enhanceResponse(output, {\n            taskComplexity: result.success ? 'moderate' : 'complex'\n          });\n          output = enhanced.content;\n          if (enhanced.encouragement) {\n            output += `\\n\\n${enhanced.encouragement}`;\n          }\n        }\n\n        return {\n          success: result.success,\n          output: output,\n          error: result.errors?.join('; ')\n        };\n      } else {\n        // Use standard processing for simpler tasks with streaming context\n        const result = await this.processRequest(input.prompt, mode, context as Record<string, unknown>);\n\n        // Enhance with Goddess Mode if active\n        let output = result;\n        if (this.goddessMode.isGoddessMode()) {\n          const enhanced = this.goddessMode.enhanceResponse(output);\n          output = enhanced.content;\n        }\n\n        return {\n          success: true,\n          output: output\n        };\n      }\n    } catch (error) {\n      logger.error(`Error in SupervisorAgent.run: ${error}`);\n      return {\n        success: false,\n        error: `Error processing request: ${error instanceof Error ? error.message : String(error)}`\n      };\n    }\n  }\n    \n  private async delegateToAgent(agent: Agent, input: string, context: Record<string, unknown>): Promise<string> {\n    logger.info(`Delegating to agent: ${agent.name}`);\n\n    // Create streaming context for delegation\n    const delegationStreamId = `delegation_${agent.id}_${Date.now()}`;\n    const streamingContext: StreamingContext = {\n      streamId: delegationStreamId,\n      parentStreamId: (context.streamingContext as StreamingContext)?.streamId,\n      onStream: (event: AgentStreamEvent) => {\n        // Forward agent events to supervisor stream\n        this.emitStreamEvent('delegation', 'supervisor', {\n          delegatedAgent: agent.name,\n          delegatedEvent: event\n        });\n\n        // Also forward to parent stream if available\n        const streamingCtx = context.streamingContext as StreamingContext;\n        if (streamingCtx?.onStream) {\n          streamingCtx.onStream(event);\n        }\n      }\n    };\n\n    // Emit delegation start event\n    this.emitStreamEvent('delegation', 'supervisor', {\n      action: 'start',\n      delegatedAgent: agent.name,\n      input: input.substring(0, 100) + '...'\n    });\n\n    const agentInput: AgentRunInput = {\n      prompt: input,\n      mode: 'task'\n    };\n\n    const agentContext: AgentContext = {\n      ...context,\n      streamingContext,\n      variables: {\n        ...(context.variables || {}),\n        delegatedBy: this.name,\n        originalInput: input,\n        supervisorId: this.id\n      }\n    };\n\n    try {\n      const result = await agent.run(agentInput, agentContext);\n\n      if (!result.success) {\n        logger.error(`Agent ${agent.name} failed: ${result.error}`);\n        this.emitStreamEvent('delegation', 'supervisor', {\n          action: 'error',\n          delegatedAgent: agent.name,\n          error: result.error\n        });\n        throw new Error(`Agent ${agent.name} failed: ${result.error}`);\n      }\n\n      // Emit delegation success event\n      this.emitStreamEvent('delegation', 'supervisor', {\n        action: 'complete',\n        delegatedAgent: agent.name,\n        result: result.output?.substring(0, 100) + '...'\n      });\n\n      return result.output || '';\n    } catch (error) {\n      this.emitStreamEvent('delegation', 'supervisor', {\n        action: 'error',\n        delegatedAgent: agent.name,\n        error: error instanceof Error ? error.message : String(error)\n      });\n      throw error;\n    }\n  }\n    \n  private async reviewAndFinalize(result: string, originalInput: string, mode: string, context: Record<string, unknown>): Promise<string> {\n    logger.info(`Reviewing and finalizing result for ${mode} mode`);\n\n    // Use context for additional review information\n    const contextInfo = context.variables ? JSON.stringify(context.variables) : 'No additional context';\n\n    // Create a prompt for reviewing the result\n    const reviewPrompt = `\n        # Result Review Task\n\n        You are the SupervisorAgent reviewing the result from the workflow execution.\n\n        ## Original Enhanced Request:\n        ${originalInput}\n\n        ## Workflow Result:\n        ${result}\n\n        ## Context Information:\n        ${contextInfo}\n\n        ## Your Task:\n        1. Verify that the result fully addresses the user's request\n        2. Check for any errors or omissions\n        3. Enhance the result if necessary\n        4. Format the final response appropriately for ${mode} mode\n        5. Consider the context information for additional insights\n\n        ## Final Response:\n        `;\n        \n    return this.generate(reviewPrompt);\n  }\n\n  /**\n     * Phase 2: Create comprehensive task plan for autonomous execution\n     */\n  private async createTaskPlan(requirements: string, mode: string): Promise<TaskPlan> {\n    const planningPrompt = `\n        # Autonomous Task Planning\n\n        You are the SupervisorAgent creating a comprehensive plan for autonomous task execution.\n\n        ## Requirements:\n        ${requirements}\n\n        ## Mode:\n        ${mode}\n\n        ## Your Task:\n        Create a detailed task plan that breaks down the requirements into executable steps.\n        Consider dependencies, validation criteria, and potential failure points.\n\n        ## Output Format (JSON):\n        {\n            \"id\": \"unique_task_id\",\n            \"description\": \"Brief description of the overall task\",\n            \"complexity\": \"low|medium|high\",\n            \"estimatedTime\": 300000,\n            \"validationCriteria\": [\"criterion1\", \"criterion2\"],\n            \"steps\": [\n                {\n                    \"id\": \"step_1\",\n                    \"description\": \"What this step does\",\n                    \"type\": \"analysis|implementation|testing|validation\",\n                    \"agent\": \"agent_type\",\n                    \"tools\": [\"tool1\", \"tool2\"],\n                    \"expectedOutput\": \"What should be produced\",\n                    \"dependencies\": [\"step_id\"]\n                }\n            ],\n            \"dependencies\": [\"external_dependency\"]\n        }\n        `;\n\n    const planResult = await this.generate(planningPrompt);\n\n    try {\n      const plan = JSON.parse(planResult);\n      return {\n        id: plan.id || `task_${Date.now()}`,\n        description: plan.description || requirements.substring(0, 100),\n        steps: plan.steps || [],\n        dependencies: plan.dependencies || [],\n        estimatedTime: plan.estimatedTime || 300000,\n        complexity: plan.complexity || 'medium',\n        validationCriteria: plan.validationCriteria || ['Task completed successfully']\n      };\n    } catch (error) {\n      logger.error(`Failed to parse task plan: ${error}`);\n      // Return a basic plan\n      return {\n        id: `task_${Date.now()}`,\n        description: requirements.substring(0, 100),\n        steps: [{\n          id: 'step_1',\n          description: 'Execute the task',\n          type: 'implementation',\n          agent: mode,\n          tools: ['file', 'search', 'memory'],\n          expectedOutput: 'Task completion',\n          dependencies: []\n        }],\n        dependencies: [],\n        estimatedTime: 300000,\n        complexity: 'medium',\n        validationCriteria: ['Task completed successfully']\n      };\n    }\n  }\n\n  /**\n     * Phase 2: Execute task plan with step-by-step execution\n     */\n  private async executeTaskPlan(taskPlan: TaskPlan, originalRequirements: string): Promise<TaskResult> {\n    logger.info(`Executing task plan: ${taskPlan.description}`);\n\n    const artifacts: string[] = [];\n    const validationResults: ValidationResult[] = [];\n    const outputs: string[] = [];\n    const errors: string[] = [];\n\n    // Execute steps in dependency order\n    const executedSteps = new Set<string>();\n    const stepResults = new Map<string, string>();\n\n    for (const step of taskPlan.steps) {\n      try {\n        // Check if dependencies are satisfied\n        const unsatisfiedDeps = step.dependencies.filter(dep => !executedSteps.has(dep));\n        if (unsatisfiedDeps.length > 0) {\n          logger.warn(`Step ${step.id} has unsatisfied dependencies: ${unsatisfiedDeps.join(', ')}`);\n          // For now, continue anyway - in a full implementation, we'd handle this better\n        }\n\n        logger.info(`Executing step: ${step.description}`);\n\n        // Get the appropriate agent for this step\n        const agent = await this.getAgentForStep(step);\n\n        // Prepare context for the step\n        const stepContext = {\n          originalRequirements,\n          taskPlan,\n          currentStep: step,\n          previousResults: stepResults,\n          executedSteps: Array.from(executedSteps)\n        };\n\n        // Execute the step\n        const stepResult = await this.executeStep(step, agent, stepContext);\n\n        stepResults.set(step.id, stepResult);\n        outputs.push(`Step ${step.id}: ${stepResult}`);\n        executedSteps.add(step.id);\n\n        // Check if this step produced artifacts\n        if (step.type === 'implementation') {\n          artifacts.push(`Step ${step.id} output`);\n        }\n\n      } catch (error) {\n        const errorMsg = `Step ${step.id} failed: ${error instanceof Error ? error.message : String(error)}`;\n        logger.error(errorMsg);\n        errors.push(errorMsg);\n      }\n    }\n\n    return {\n      success: errors.length === 0,\n      output: outputs.join('\\n\\n'),\n      artifacts,\n      validationResults,\n      timeSpent: 0, // Will be set by caller\n      errors: errors.length > 0 ? errors : undefined\n    };\n  }\n\n  /**\n     * Phase 2: Get appropriate agent for a task step\n     */\n  private async getAgentForStep(step: TaskStep): Promise<Agent> {\n    // Try to get existing agent for the step type\n    let agent = this.getModeAgent(step.agent);\n\n    if (!agent) {\n      // Create a new agent for this step type\n      agent = await this.createModeAgent(\n        step.agent,\n        `${step.agent.charAt(0).toUpperCase() + step.agent.slice(1)} Agent`,\n        `Agent for ${step.agent} tasks`\n      );\n    }\n\n    return agent;\n  }\n\n  /**\n     * Phase 2: Execute individual task step\n     */\n  private async executeStep(step: TaskStep, agent: Agent, context: Record<string, unknown>): Promise<string> {\n    const stepPrompt = `\n        # Task Step Execution\n\n        ## Step Details:\n        - ID: ${step.id}\n        - Description: ${step.description}\n        - Type: ${step.type}\n        - Expected Output: ${step.expectedOutput}\n\n        ## Original Requirements:\n        ${context.originalRequirements}\n\n        ## Previous Results:\n        ${Array.from((context.previousResults as Map<string, string>).entries()).map((entry) => `${entry[0]}: ${entry[1]}`).join('\\n')}\n\n        ## Available Tools:\n        ${step.tools.join(', ')}\n\n        ## Your Task:\n        Execute this specific step according to its description and type.\n        Use the available tools as needed.\n        Provide clear output that matches the expected output format.\n        `;\n\n    const result = await this.delegateToAgent(agent, stepPrompt, context);\n    return result;\n  }\n\n  /**\n     * Phase 2: Validate task result against criteria\n     */\n  private async validateTaskResult(result: TaskResult, taskPlan: TaskPlan): Promise<{success: boolean, issues: string[]}> {\n    logger.info('Validating task result...');\n\n    const issues: string[] = [];\n\n    // Basic validation\n    if (!result.success) {\n      issues.push('Task execution reported failure');\n    }\n\n    if (!result.output || result.output.trim().length === 0) {\n      issues.push('No output produced');\n    }\n\n    if (result.errors && result.errors.length > 0) {\n      issues.push(`Errors occurred: ${result.errors.join(', ')}`);\n    }\n\n    // Validate against criteria using LLM\n    for (const criterion of taskPlan.validationCriteria) {\n      try {\n        const validationPrompt = `\n                # Task Result Validation\n\n                ## Validation Criterion:\n                ${criterion}\n\n                ## Task Output:\n                ${result.output}\n\n                ## Task Plan:\n                ${JSON.stringify(taskPlan, null, 2)}\n\n                ## Your Task:\n                Determine if the task output satisfies the validation criterion.\n                Respond with \"PASS\" if it satisfies the criterion, or \"FAIL: reason\" if it doesn't.\n                `;\n\n        const validationResult = await this.generate(validationPrompt);\n\n        if (!validationResult.toLowerCase().includes('pass')) {\n          issues.push(`Validation failed for \"${criterion}\": ${validationResult}`);\n        }\n      } catch (error) {\n        issues.push(`Validation error for \"${criterion}\": ${error}`);\n      }\n    }\n\n    return {\n      success: issues.length === 0,\n      issues\n    };\n  }\n\n  /**\n     * Phase 2: Self-correct and retry failed task\n     */\n  private async selfCorrectAndRetry(\n    requirements: string,\n    failedResult: TaskResult,\n    validation: {success: boolean, issues: string[]}\n  ): Promise<TaskResult> {\n    this.currentIteration++;\n    logger.info(`Self-correction attempt ${this.currentIteration}`);\n\n    // Analyze what went wrong and create corrective plan\n    const correctionPrompt = `\n        # Self-Correction Analysis\n\n        ## Original Requirements:\n        ${requirements}\n\n        ## Failed Result:\n        ${JSON.stringify(failedResult, null, 2)}\n\n        ## Validation Issues:\n        ${validation.issues.join('\\n')}\n\n        ## Your Task:\n        1. Analyze what went wrong in the previous execution\n        2. Identify the root causes of the failures\n        3. Create a corrective action plan\n        4. Re-execute the task with improvements\n\n        Focus on addressing the specific validation issues identified.\n        `;\n\n    const correctionAnalysis = await this.generate(correctionPrompt);\n    logger.info(`Correction analysis: ${correctionAnalysis.substring(0, 200)}...`);\n\n    // Create a new task plan based on the correction analysis\n    const correctedPlan = await this.createTaskPlan(\n      `${requirements}\\n\\nCorrection needed based on previous failure:\\n${correctionAnalysis}`,\n      'agent'\n    );\n\n    // Execute the corrected plan\n    return await this.executeTaskPlan(correctedPlan, requirements);\n  }\n\n  /**\n     * Phase 2: Learn from task execution for future improvements\n     */\n  private async learnFromExecution(taskPlan: TaskPlan, result: TaskResult, validation: Record<string, unknown>): Promise<void> {\n    logger.info('Learning from task execution...');\n\n    const learningKey = `${taskPlan.complexity}_${taskPlan.description.substring(0, 50)}`;\n\n    const learningData = {\n      taskPlan,\n      result,\n      validation,\n      iterations: this.currentIteration,\n      timestamp: Date.now(),\n      success: result.success && Boolean(validation.success)\n    };\n\n    this.learningMemory.set(learningKey, learningData);\n\n    // Store learning in agent memory for persistence\n    if (this.getMemory) {\n      try {\n        await this.getMemory().addMemory({\n          content: `Task execution learning: ${taskPlan.description}`,\n          metadata: {\n            source: 'agent' as MemorySource,\n            type: 'insight' as MemoryType,\n            data: JSON.stringify(learningData),\n            tags: ['learning', 'autonomous', taskPlan.complexity]\n          }\n        });\n      } catch (error) {\n        logger.warn(`Failed to store learning data: ${error}`);\n      }\n    }\n  }\n\n  /**\n     * Phase 2: Determine if request should use autonomous mode\n     */\n  private shouldUseAutonomousMode(prompt: string, mode: string): boolean {\n    // Use autonomous mode for agent mode and complex requests\n    if (mode !== 'agent') {\n      return false;\n    }\n\n    // Check for complexity indicators\n    const complexityIndicators = [\n      'implement', 'create', 'build', 'develop', 'refactor',\n      'fix bug', 'debug', 'optimize', 'analyze', 'generate',\n      'multiple files', 'entire project', 'complete solution'\n    ];\n\n    const promptLower = prompt.toLowerCase();\n    const hasComplexityIndicators = complexityIndicators.some(indicator =>\n      promptLower.includes(indicator)\n    );\n\n    // Use autonomous mode for complex requests or when explicitly requested\n    return hasComplexityIndicators || promptLower.includes('autonomous');\n  }\n\n  /**\n     * Phase 2: Find similar task in learning memory for optimization\n     */\n  private findSimilarTaskInMemory(requirements: string): LearningData | null {\n    const requirementsLower = requirements.toLowerCase();\n    const threshold = 0.7; // Similarity threshold\n\n    for (const [key, learningData] of Array.from(this.learningMemory.entries())) {\n      const similarity = this.calculateTextSimilarity(\n        requirementsLower,\n        learningData.taskPlan.description.toLowerCase()\n      );\n\n      if (similarity > threshold && learningData.success) {\n        logger.info(`Found similar task '${key}' with ${(similarity * 100).toFixed(1)}% similarity`);\n        return learningData;\n      }\n    }\n\n    return null;\n  }\n\n  /**\n     * Phase 2: Optimize task plan based on memory\n     */\n  private async optimizeTaskPlanFromMemory(requirements: string, mode: string, similarTask: LearningData): Promise<TaskPlan> {\n    const baseTaskPlan = similarTask.taskPlan;\n\n    // Create optimized plan based on successful patterns for the specific mode\n    const optimizedPlan: TaskPlan = {\n      id: `optimized_task_${mode}_${Date.now()}`,\n      description: requirements.substring(0, 100),\n      steps: baseTaskPlan.steps.map((step) => ({\n        ...step,\n        id: `${step.id}_optimized_${mode}_${Date.now()}`,\n        description: step.description // Keep successful step patterns\n      })),\n      dependencies: baseTaskPlan.dependencies || [],\n      estimatedTime: Math.max(baseTaskPlan.estimatedTime * 0.8, 60000), // 20% faster estimate\n      complexity: baseTaskPlan.complexity,\n      validationCriteria: baseTaskPlan.validationCriteria || [`${mode} task completed successfully`]\n    };\n\n    logger.info('Optimized task plan created from successful memory pattern');\n    return optimizedPlan;\n  }\n\n  /**\n     * Phase 2: Calculate text similarity for memory optimization\n     */\n  private calculateTextSimilarity(text1: string, text2: string): number {\n    // Simple word-based similarity calculation\n    const words1 = text1.split(/\\s+/).filter(w => w.length > 2);\n    const words2 = text2.split(/\\s+/).filter(w => w.length > 2);\n\n    if (words1.length === 0 || words2.length === 0) return 0;\n\n    const commonWords = words1.filter(word => words2.includes(word));\n    const totalWords = new Set([...words1, ...words2]).size;\n\n    return commonWords.length / totalWords;\n  }\n\n  /**\n   * Get list of available agents for delegation\n   */\n  private _getAvailableAgents(): string[] {\n    return [\n      'developer',\n      'architect',\n      'tester',\n      'documenter',\n      'refactor',\n      'debug',\n      'research',\n      'ui-ux'\n    ];\n  }\n}\n\n// Export the SupervisorAgent class as default\nexport default SupervisorAgent;"]}