"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceMonitor = void 0;
exports.measurePerformance = measurePerformance;
exports.measureAsync = measureAsync;
const perf_hooks_1 = require("perf_hooks");
class PerformanceMonitor {
    static instance;
    metrics = [];
    enabled = true;
    observer = null;
    constructor() {
        this.setupPerformanceObserver();
    }
    static getInstance() {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    }
    setupPerformanceObserver() {
        this.observer = new perf_hooks_1.PerformanceObserver((items) => {
            items.getEntries().forEach((entry) => {
                this.metrics.push({
                    name: entry.name,
                    duration: entry.duration,
                    timestamp: perf_hooks_1.performance.now(),
                });
            });
        });
        this.observer.observe({ entryTypes: ['measure'] });
    }
    measure(name, fn) {
        if (!this.enabled)
            return fn();
        const startMark = `${name}-start`;
        const endMark = `${name}-end`;
        perf_hooks_1.performance.mark(startMark);
        const result = fn();
        if (result instanceof Promise) {
            return result.finally(() => {
                perf_hooks_1.performance.mark(endMark);
                perf_hooks_1.performance.measure(name, startMark, endMark);
                perf_hooks_1.performance.clearMarks(startMark);
                perf_hooks_1.performance.clearMarks(endMark);
            });
        }
        else {
            perf_hooks_1.performance.mark(endMark);
            perf_hooks_1.performance.measure(name, startMark, endMark);
            perf_hooks_1.performance.clearMarks(startMark);
            perf_hooks_1.performance.clearMarks(endMark);
            return result;
        }
    }
    getMetrics() {
        return [...this.metrics];
    }
    clearMetrics() {
        this.metrics = [];
    }
    enable() {
        this.enabled = true;
    }
    disable() {
        this.enabled = false;
    }
}
exports.performanceMonitor = PerformanceMonitor.getInstance();
// Decorator for measuring method execution time
function measurePerformance(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    descriptor.value = function (...args) {
        return exports.performanceMonitor.measure(`${target.constructor.name}.${propertyKey}`, () => originalMethod.apply(this, args));
    };
    return descriptor;
}
// Utility function to measure async operations
async function measureAsync(name, operation, metadata) {
    const start = perf_hooks_1.performance.now();
    try {
        const result = await operation();
        const duration = perf_hooks_1.performance.now() - start;
        exports.performanceMonitor['metrics'].push({
            name,
            duration,
            timestamp: Date.now(),
            metadata
        });
        return result;
    }
    catch (error) {
        const duration = perf_hooks_1.performance.now() - start;
        exports.performanceMonitor['metrics'].push({
            name: `${name}-error`,
            duration,
            timestamp: Date.now(),
            metadata: {
                ...metadata,
                error: error instanceof Error ? error.message : String(error)
            }
        });
        throw error;
    }
}
//# sourceMappingURL=performance.js.map