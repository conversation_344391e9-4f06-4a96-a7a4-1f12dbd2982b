{"version": 3, "file": "lintDiagnosticsTool.js", "sourceRoot": "", "sources": ["../../src/tools/lintDiagnosticsTool.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmgCH,8DAGC;AApgCD,+CAAiC;AAGjC,+DAAwD,CAAC,wCAAwC;AACjG,6BAAwB,CAAC,4BAA4B;AACrD,kDAAkD;AAClD,sCAAmC,CAAC,wCAAwC;AAC5E,sCAAiD,CAAC,8CAA8C;AAChG,+CAAiC,CAAC,sBAAsB;AASxD,2BAA2B;AAC3B,MAAM,0BAA0B,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,wEAAwE;IACxE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mEAAmE,CAAC;IAC7G,+FAA+F;IAC/F,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;IAC3K,0CAA0C;IAC1C,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;IAC3E,iCAAiC;IACjC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC;IAC/E,iEAAiE;IACjE,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kDAAkD,CAAC;IAC/G,+EAA+E;IAC/E,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0EAA0E,CAAC;CACnI,CAAC,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC;AAE9C,MAAM,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,gFAAgF;IAChF,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gFAAgF,CAAC;IACtH,wHAAwH;IACxH,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,qDAAqD,CAAC;IAC/G,4IAA4I;IAC5I,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kDAAkD,CAAC;CACnI,CAAC,CAAC,QAAQ,CAAC,0DAA0D,CAAC,CAAC;AAExE,MAAM,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IACtC,mJAAmJ;IACnJ,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4HAA4H,CAAC;IACtK,kFAAkF;IAClF,IAAI,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,0DAA0D,CAAC;CACjH,CAAC,CAAC,QAAQ,CAAC,2CAA2C,CAAC,CAAC;AAEzD,MAAM,2BAA2B,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,mGAAmG;IACnG,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,8DAA8D,CAAC;IACpG,0DAA0D;IAC1D,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC;IACvL,sDAAsD;IACtD,IAAI,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;IACtH,6CAA6C;IAC7C,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;IAC/G,uFAAuF;IACvF,WAAW,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,oFAAoF,CAAC;IACjJ,mGAAmG;IACnG,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;QACd,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;QACpG,GAAG,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;KACnG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iEAAiE,CAAC;CAC1F,CAAC,CAAC,QAAQ,CAAC,8DAA8D,CAAC,CAAC;AAG5E,MAAM,+BAA+B,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/C,6HAA6H;IAC7H,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iGAAiG,CAAC;IAC3I,8DAA8D;IAC9D,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8CAA8C,CAAC;IAC3L,iDAAiD;IACjD,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;IACnH,8EAA8E;IAC9E,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6EAA6E,CAAC;CAC/I,CAAC,CAAC,QAAQ,CAAC,oCAAoC,CAAC,CAAC;AAElD,MAAM,4BAA4B,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,uGAAuG;IACvG,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,uDAAuD,CAAC;IAC1F,0FAA0F;IAC1F,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;IACtF,6DAA6D;IAC7D,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;IAC3K,gDAAgD;IAChD,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;IACnG,sDAAsD;IACtD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;CACrG,CAAC,CAAC,QAAQ,CAAC,kCAAkC,CAAC,CAAC;AAGhD,2DAA2D;AAC3D,MAAM,iCAAiC,GAAG,OAAC,CAAC,MAAM,CAAC;IACjD,oEAAoE;IACpE,oBAAoB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IAC5D,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IACxD,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACnC,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE,iCAAiC;IAC5F,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE,iCAAiC;IAC9F,wBAAwB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IAChD,mCAAmC;CACpC,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,+CAA+C,CAAC,CAAC;AAGvE;;;;GAIG;AACH,SAAS,iCAAiC,CAAC,KAAoC;IAC7E,IAAI,KAAK,KAAK,SAAS;QAAE,OAAO,SAAS,CAAC;IAC1C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IACvC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,CAAC;AAGD;;;GAGG;AACH,MAAa,mBAAmB;IACrB,EAAE,GAAG,kBAAkB,CAAC,CAAC,YAAY;IACrC,IAAI,GAAG,kBAAkB,CAAC,CAAC,wBAAwB;IACnD,WAAW,GAAG,2JAA2J,CAAC;IAC1K,QAAQ,GAAG,aAAa,CAAC,CAAC,gBAAgB;IAC1C,IAAI,GAAG,cAAc,CAAC,CAAC,oBAAoB;IAE3C,OAAO,CAAuC;IAEvD;;;SAGK;IACL,YAAY,OAAsB;QAChC,iDAAiD;QACjD,IAAI,CAAC,OAAO,GAAG;YACb,MAAM,EAAE;gBACN,WAAW,EAAE,oLAAoL;gBACjM,WAAW,EAAE,0BAA0B;aACxC;YACD,KAAK,EAAE;gBACL,WAAW,EAAE,2HAA2H;gBACxI,WAAW,EAAE,wBAAwB;aACtC;YACD,oBAAoB,EAAE;gBACpB,WAAW,EAAE,gMAAgM;gBAC7M,WAAW,EAAE,2BAA2B;aACzC;YACD,UAAU,EAAE;gBACV,WAAW,EAAE,2FAA2F;gBACxG,WAAW,EAAE,sBAAsB;aACpC;YACD,WAAW,EAAE;gBACX,WAAW,EAAE,6KAA6K;gBAC1L,WAAW,EAAE,+BAA+B;aAC7C;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE,+JAA+J;gBAC5K,WAAW,EAAE,4BAA4B;aAC1C;YACD,cAAc,EAAE;gBACd,WAAW,EAAE,2EAA2E;gBACxF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,yBAAyB,CAAC,EAAE,gCAAgC;aAChG;YACD,iBAAiB,EAAE;gBACjB,WAAW,EAAE,+HAA+H;gBAC5I,WAAW,EAAE,iCAAiC;aAC/C;SACF,CAAC;QAEF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAC3D,CAAC;IAGD;;;;;SAKK;IACL,qBAAqB;QACnB,+FAA+F;QAC/F,0EAA0E;QAC1E,gDAAgD;QAChD,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,EAAE;YAClE,0EAA0E;YAC1E,8DAA8D;YAC9D,MAAM,YAAY,GAAI,SAAS,CAAC,WAAmB,CAAC,MAAM,CAAC,CAAC,CAAE,SAAS,CAAC,WAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAEjH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yFAAyF,UAAU,IAAI,CAAC,CAAC;gBAC/H,yDAAyD;gBACzD,OAAO;oBACL,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;oBACnC,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,gBAAgB,UAAU,UAAU;oBAC1E,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,+BAA+B;iBAChF,CAAC;YACJ,CAAC;YAED,2EAA2E;YAC3E,oFAAoF;YACpF,IAAI,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC;gBACtC,YAAY,CAAC,UAAU,CAAC,QAAQ,GAAG;oBACjC,IAAI,EAAE,OAAO,EAAE,4CAA4C;oBAC3D,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;oBACrE,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW;oBACzD,WAAW,EAAE,IAAI;iBAClB,CAAC;YACJ,CAAC;YACD,IAAI,YAAY,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC;gBAClC,YAAY,CAAC,UAAU,CAAC,IAAI,GAAG;oBAC7B,IAAI,EAAE,OAAO,EAAE,4CAA4C;oBAC3D,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW;oBACrD,WAAW,EAAE,IAAI;iBAClB,CAAC;YACJ,CAAC;YACD,IAAI,YAAY,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;gBACpC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG;oBAC/B,IAAI,EAAE,OAAO,EAAE,4CAA4C;oBAC3D,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW;oBACvD,WAAW,EAAE,IAAI;iBAClB,CAAC;YACJ,CAAC;YACD,IAAI,YAAY,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;gBACnC,kDAAkD;gBAClD,YAAY,CAAC,UAAU,CAAC,KAAK,GAAG;oBAC9B,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,IAAI,oEAAoE;oBAC9H,UAAU,EAAE;wBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE;wBACrK,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,EAAE,eAAe,EAAE;qBAClK;oBACD,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;iBAC3B,CAAC;YACJ,CAAC;YAGD,OAAO;gBACL,iCAAiC;gBACjC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;gBACnC,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,UAAU,EAAE,YAAY,EAAE,4BAA4B;aACvD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;SAUK;IACL,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,QAAQ,GAAG,wEAAwE,CAAC;YAC1F,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1E,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,oDAAoD,UAAU,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAC/I,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1E,CAAC;QAED,0EAA0E;QAC1E,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClD,MAAM,QAAQ,GAAG,6BAA6B,UAAU,MAAM,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;iBACzF,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC;iBACpC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAM,KAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;iBACrE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAChB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,CAAC,CAAC;YAC7E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1E,CAAC;QACD,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC;QAGxC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,UAAU,GAAG,CAAC,CAAC;QAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qBAAqB,UAAU,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC,sBAAsB;QAElG,IAAI,CAAC;YACH,IAAI,YAAiB,CAAC,CAAC,gCAAgC;YACvD,IAAI,aAAa,GAAG,IAAI,CAAC,CAAC,kBAAkB;YAE5C,4CAA4C;YAC5C,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,MAAM;oBACT,MAAM,SAAS,GAAG,cAA4D,CAAC;oBAC/E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;oBAC1D,YAAY,GAAG,WAAW,CAAC,CAAC,iCAAiC;oBAC7D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,WAAW,CAAC,MAAM,eAAe,CAAC,CAAC;oBAC1F,MAAM;gBAER,KAAK,KAAK;oBACR,MAAM,QAAQ,GAAG,cAA0D,CAAC;oBAC5E,mDAAmD;oBACnD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;oBAClE,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC;oBAChC,aAAa,GAAG,SAAS,CAAC,OAAO,CAAC;oBAClC,IAAI,SAAS,CAAC,KAAK;wBAAE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;oBACtF,MAAM;gBAER,KAAK,oBAAoB;oBACvB,MAAM,UAAU,GAAG,cAA6D,CAAC;oBACjF,oEAAoE;oBACpE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAC5D,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC;oBAClC,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC;oBACpC,IAAI,WAAW,CAAC,KAAK;wBAAE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;oBACzG,MAAM;gBAER,KAAK,UAAU;oBACb,MAAM,YAAY,GAAG,cAAwD,CAAC;oBAC9E,wCAAwC;oBACxC,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;oBACvC,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAC,4CAA4C,CAAC,CAAC,uCAAuC;oBACvJ,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC5C,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,IAAI,KAAK,CAAC;oBAExC,IAAI,CAAC;wBACH,yCAAyC;wBACzC,IAAI,SAAkC,CAAC;wBACvC,IAAI,QAAQ,EAAE,CAAC;4BACb,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;4BAC9C,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,iCAAiC;4BAC/G,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;gCACxC,wDAAwD;gCACxD,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,KAAK,cAAc,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC;4BAC1G,CAAC;4BACD,IAAI,CAAC,SAAS,EAAE,CAAC;gCACf,MAAM,IAAI,KAAK,CAAC,SAAS,QAAQ,cAAc,CAAC,CAAC;4BACnD,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,qDAAqD;4BACrD,SAAS,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;4BAChI,IAAI,CAAC,SAAS,EAAE,CAAC;gCACf,6DAA6D;gCAC7D,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;4BACnG,CAAC;4BACD,IAAI,CAAC,SAAS,EAAE,CAAC;gCACf,MAAM,IAAI,KAAK,CAAC,wGAAwG,CAAC,CAAC;4BAC5H,CAAC;4BACD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2DAA2D,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;wBACpG,CAAC;wBAGD,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;wBAE5D,IAAI,IAAI,EAAE,CAAC;4BACT,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,SAAS,CAAC,IAAI,kBAAkB,CAAC,CAAC;4BAC5E,iCAAiC;4BACjC,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;gCAClC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;oCACtD,IAAI,CAAC,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;wCAC9B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,SAAU,CAAC,IAAI,6BAA6B,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;wCACzF,UAAU,CAAC,OAAO,EAAE,CAAC;wCACrB,OAAO,EAAE,CAAC;oCACZ,CAAC;gCACH,CAAC,CAAC,CAAC;gCACH,4FAA4F;gCAC5F,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE;oCAClC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,SAAU,CAAC,IAAI,qBAAqB,IAAA,kBAAS,EAAS,uBAAuB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,uBAAuB;oCAC3J,UAAU,CAAC,OAAO,EAAE,CAAC;oCACrB,OAAO,EAAE,CAAC,CAAC,0BAA0B;gCACvC,CAAC,EAAE,IAAA,kBAAS,EAAS,uBAAuB,EAAE,KAAK,CAAC,CAAC,CAAC;4BACxD,CAAC,CAAC,CAAC;wBACL,CAAC;wBAGD,YAAY,GAAG,iBAAiB,SAAS,CAAC,IAAI,cAAc,IAAI,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,EAAE,sCAAsC,CAAC;wBAC3I,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAErC,CAAC;oBAAC,OAAO,KAAU,EAAE,CAAC;wBACpB,aAAa,GAAG,KAAK,CAAC;wBACtB,YAAY,GAAG,mCAAmC,QAAQ,IAAI,SAAS,MAAM,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;wBACtG,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;oBAC7C,CAAC;oBACD,MAAM;gBAER,KAAK,WAAW;oBACd,MAAM,cAAc,GAAG,cAAiE,CAAC;oBACzF,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;oBAC/D,aAAa,GAAG,IAAI,CAAC,CAAC,6CAA6C;oBACnE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;oBACxD,MAAM;gBAER,KAAK,QAAQ;oBACX,MAAM,WAAW,GAAG,cAA8D,CAAC;oBACnF,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;oBACzD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,YAAY,CAAC,MAAM,WAAW,CAAC,CAAC;oBACzF,MAAM;gBAER,KAAK,cAAc;oBACjB,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAChD,YAAY,GAAG,YAAY,CAAC;oBAC5B,aAAa,GAAG,IAAI,CAAC;oBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;oBAC3D,MAAM;gBAER,KAAK,iBAAiB;oBACpB,MAAM,mBAAmB,GAAG,cAAmE,CAAC;oBAChG,iCAAiC;oBACjC,4EAA4E;oBAC5E,MAAM,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;oBACvD,YAAY,GAAG,EAAE,MAAM,EAAE,kBAAkB,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBACzF,aAAa,GAAG,IAAI,CAAC;oBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;oBAC9D,MAAM;gBAGR;oBACE,8FAA8F;oBAC9F,MAAM,2BAA2B,GAAG,0CAA0C,UAAU,EAAE,CAAC;oBAC3F,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;oBACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YAC/F,CAAC;YAED,sBAAsB;YACtB,OAAO;gBACL,OAAO,EAAE,aAAa;gBACtB,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,UAAU;gBACtB,kCAAkC;aACnC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,qDAAqD;YACrD,MAAM,YAAY,GAAG,4CAA4C,UAAU,MAAM,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;YAC1G,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC9E,CAAC;IACH,CAAC;IAED,0CAA0C;IAE1C;;;;;;SAMK;IACG,oBAAoB,CAAC,QAAgB;QAC3C,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,iDAAiD;YACjD,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAChF,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,mEAAmE;YACnE,oEAAoE;YACpE,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,iCAAiC,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAGD;;;;;;SAMK;IACG,KAAK,CAAC,sBAAsB,CAAC,aAA2B,EAAE,OAAmD;QACnH,MAAM,cAAc,GAAwB,EAAE,CAAC;QAE/C,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC;YAChC,8DAA8D;YAC9D,IAAI,CAAC;gBACH,4GAA4G;gBAC5G,mFAAmF;gBACnF,iEAAiE;gBACjE,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,mDAAmD;gBAE7G,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBAE7D,gBAAgB;gBAChB,MAAM,oBAAoB,GAAG,iCAAiC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACjF,MAAM,eAAe,GAAG,iCAAiC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACxE,MAAM,iBAAiB,GAAG,iCAAiC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC5E,wDAAwD;gBAExD,MAAM,mBAAmB,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;oBACxD,IAAI,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAAE,OAAO,KAAK,CAAC;oBACjI,IAAI,eAAe,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAAE,OAAO,KAAK,CAAC;oBACzI,IAAI,iBAAiB,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;wBAAE,OAAO,KAAK,CAAC;oBACtJ,OAAO,IAAI,CAAC;gBACd,CAAC,CAAC,CAAC;gBAEH,2CAA2C;gBAC3C,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC/D,sCAAsC;oBACtC,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,wBAAwB;oBACrD,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,2CAA2C;oBAC3C,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,kBAAkB;oBAC5C,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC;oBACzH,gEAAgE;oBAChE,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,4BAA4B,CAC5F,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EACvD,EAAE,CAAC,OAAO,CACX,CAAC;oBACF,sBAAsB;oBACtB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAC,CAAC;gBAEJ,cAAc,CAAC,IAAI,CAAC,GAAG,uBAAuB,CAAC,CAAC;YAElD,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBACxF,yBAAyB;YAC3B,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,MAAM,uBAAuB,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QAExG,sFAAsF;QACtF,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAGD;;;;SAIK;IACG,KAAK,CAAC,eAAe,CAAC,OAAmD;QAC/E,IAAI,aAAa,GAAiB,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,IAAI,CAAC;gBACH,qCAAqC;gBACrC,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC5D,sDAAsD;gBACtD,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACxC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC3F,gEAAgE;gBAChE,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,wDAAwD;YACxD,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAGD;;;;SAIK;IACG,KAAK,CAAC,0BAA0B,CAAC,OAAiD;QACxF,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAE5D,iFAAiF;YACjF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAClE,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,yBAAyB;YAEzE,2CAA2C;YAC3C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAEvD,sCAAsC;YACtC,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAClD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,OAAO,CAAC,eAAe,aAAa,OAAO,CAAC,QAAQ,mDAAmD,EAAE,CAAC;YAC5K,CAAC;YAED,wGAAwG;YACxG,+CAA+C;YAC/C,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CACtD,kCAAkC,EAClC,OAAO,EACP,UAAU,CAAC,KAAK,EAChB,EAAE,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,2BAA2B;aAC3E,CAAC;YAEF,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gDAAgD,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;gBACjG,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gDAAgD,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;YAC/G,CAAC;YAED,uCAAuC;YACvC,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sCAAsC,OAAO,CAAC,eAAe,8BAA8B,WAAW,CAAC,MAAM,SAAS,EAAE,CAAC;YAC3J,CAAC;YAED,wEAAwE;YACxE,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBACzB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC3E,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;YAChH,CAAC;iBAAM,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;gBAC7B,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;gBACxE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACpE,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,IAAI,KAAK,CAAC,iDAAiD,YAAY,CAAC,KAAK,IAAI,CAAC,CAAC;gBAC3F,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,qBAAqB,YAAY,CAAC,KAAK,qEAAqE,CAAC,CAAC;YAChI,CAAC;YAED,2CAA2C;YAC3C,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,4DAA4D;YAC5D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAGvD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,YAAY,CAAC,KAAK,qCAAqC,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;QAEpI,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kDAAkD,OAAO,CAAC,eAAe,OAAO,OAAO,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAClI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,CAAC;QACrF,CAAC;IACH,CAAC;IAGD;;;;SAIK;IACG,KAAK,CAAC,gBAAgB,CAAC,OAAoD;QACjF,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAE5D,yCAAyC;YACzC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAClE,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,0BAA0B;YAE1E,kDAAkD;YAClD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YAE7M,kFAAkF;YAClF,MAAM,cAAc,GAAa,EAAE,CAAC;YACpC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,8EAA8E;gBAC9E,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAC9D,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,gCAAgC;gBACtE,6CAA6C;YAC/C,CAAC;YACD,oEAAoE;YACpE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,qCAAqC;gBAC/D,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACN,wEAAwE;gBACxE,sDAAsD;YACxD,CAAC;YAGD,6EAA6E;YAC7E,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CACtD,kCAAkC,EAClC,OAAO,EACP,KAAK,EACL,EAAE,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,yBAAyB;aAC7D,CAAC;YAEF,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,OAAO,CAAC,QAAQ,oCAAoC,CAAC,CAAC;gBAC7G,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,0CAA0C,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;YAChG,CAAC;YAED,qFAAqF;YACrF,MAAM,oBAAoB,GAAG,iCAAiC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjF,MAAM,eAAe,GAAG,iCAAiC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACxE,MAAM,iBAAiB,GAAG,iCAAiC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAE5E,MAAM,eAAe,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBAClD,+DAA+D;gBAC/D,IAAI,oBAAoB,IAAI,eAAe,IAAI,iBAAiB,EAAE,CAAC;oBACjE,qFAAqF;oBACrF,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;wBAAE,OAAO,KAAK,CAAC,CAAC,yCAAyC;oBAEnH,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACpC,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC1I,MAAM,SAAS,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC7I,MAAM,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC5J,OAAO,aAAa,IAAI,SAAS,IAAI,WAAW,CAAC,CAAC,iDAAiD;oBACrG,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,wDAAwD;oBACxD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC,CAAC,CAAC;YAGH,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yDAAyD,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACnG,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,yCAAyC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;YAC/F,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,eAAe,CAAC,MAAM,iDAAiD,CAAC,CAAC;YAExG,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,mGAAmG;YACnG,sFAAsF;YACtF,0EAA0E;YAC1E,oCAAoC;YACpC,MAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,6BAA6B;YAC3L,MAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7E,yDAAyD;YACzD,IAAI,OAAO,CAAC,WAAW,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,aAAa,CAAC,MAAM,qBAAqB,CAAC,CAAC;gBAC7E,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;oBACnC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wBACnB,IAAI,CAAC;4BACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;4BAClG,YAAY,EAAE,CAAC;4BACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;4BAClE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc;wBACxE,CAAC;wBAAC,OAAO,WAAgB,EAAE,CAAC;4BAC1B,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,KAAK,QAAQ,OAAO,CAAC,QAAQ,GAAG,EAAE,WAAW,CAAC,CAAC;wBACjH,CAAC;oBACH,CAAC;yBAAM,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;wBACvB,IAAI,CAAC;4BACH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;4BAC9D,IAAI,OAAO,EAAE,CAAC;gCACZ,YAAY,EAAE,CAAC;gCACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yBAAyB,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;gCAC/D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc;4BACxE,CAAC;iCAAM,CAAC;gCACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iDAAiD,MAAM,CAAC,KAAK,QAAQ,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;4BACjH,CAAC;wBACH,CAAC;wBAAC,OAAO,SAAc,EAAE,CAAC;4BACxB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gDAAgD,MAAM,CAAC,KAAK,IAAI,EAAE,SAAS,CAAC,CAAC;wBACrG,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,mFAAmF;gBACnF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,YAAY,CAAC,MAAM,mBAAmB,CAAC,CAAC;gBAC1E,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE,CAAC;oBAClC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wBACnB,IAAI,CAAC;4BACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;4BAClG,YAAY,EAAE,CAAC;4BACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;4BACjE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;wBACvE,CAAC;wBAAC,OAAO,WAAgB,EAAE,CAAC;4BAC1B,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,KAAK,QAAQ,OAAO,CAAC,QAAQ,GAAG,EAAE,WAAW,CAAC,CAAC;wBACjH,CAAC;oBACH,CAAC;yBAAM,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;wBACvB,IAAI,CAAC;4BACH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;4BAC9D,IAAI,OAAO,EAAE,CAAC;gCACZ,YAAY,EAAE,CAAC;gCACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;gCAC9D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;4BACvE,CAAC;iCAAM,CAAC;gCACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,MAAM,CAAC,KAAK,QAAQ,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;4BACzG,CAAC;wBACH,CAAC;wBAAC,OAAO,SAAc,EAAE,CAAC;4BACxB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,MAAM,CAAC,KAAK,IAAI,EAAE,SAAS,CAAC,CAAC;wBAC7F,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,KAAK,QAAQ,OAAO,CAAC,QAAQ,oEAAoE,CAAC,CAAC;oBACjJ,CAAC;gBACH,CAAC;YACH,CAAC;YAGD,2CAA2C;YAC3C,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,8CAA8C;YAC9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAGvD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,YAAY,oBAAoB,OAAO,CAAC,QAAQ,GAAG,EAAE,YAAY,EAAE,CAAC;QAEjH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,OAAO,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,iCAAiC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;QAC/G,CAAC;IACH,CAAC;IAGD;;;;SAIK;IACG,KAAK,CAAC,oBAAoB,CAAC,OAAwD;QACzF,IAAI,aAAa,GAAiB,EAAE,CAAC;QACrC,IAAI,gBAAgB,GAAG,WAAW,CAAC;QAEnC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC5D,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAoB;gBAC7D,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC;YACtC,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC3F,2CAA2C;gBAC3C,OAAO;oBACL,KAAK,EAAE,OAAO,CAAC,QAAQ;oBACvB,KAAK,EAAE,mBAAmB,OAAO,CAAC,QAAQ,EAAE;oBAC5C,gBAAgB,EAAE,CAAC;oBACnB,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;oBAC5D,yBAAyB,EAAE,EAAE;oBAC7B,SAAS,EAAE,EAAE;oBACb,cAAc,EAAE,OAAO;iBACxB,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,0DAA0D;YAC1D,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACnE,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBACxE,OAAO;oBACL,KAAK,EAAE,+BAA+B;oBACtC,gBAAgB,EAAE,CAAC;oBACnB,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;oBAC5D,yBAAyB,EAAE,EAAE;oBAC7B,SAAS,EAAE,EAAE;oBACb,cAAc,EAAE,OAAO;iBACxB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,6FAA6F;QAC7F,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE;YAC/E,4CAA4C;YAC5C,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;YAC1E,mDAAmD;SACpD,CAAC,CAAC;QAGH,2CAA2C;QAC3C,MAAM,MAAM,GAAqF;YAC/F,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;YAClC,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;YACpC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;YACjC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;SAClC,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,uBAAuB,EAAE,CAAC;YAC3C,2EAA2E;YAC3E,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;YAC5C,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,4CAA4C;gBACtE,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC7B,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjG,CAAC;QACH,CAAC;QAED,mDAAmD;QACnD,MAAM,WAAW,GAA8J,EAAE,CAAC;QAElL,KAAK,MAAM,IAAI,IAAI,uBAAuB,EAAE,CAAC;YAC3C,oFAAoF;YACpF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;YACxC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,gCAAgC;YAE9D,MAAM,GAAG,GAAG,GAAG,QAAQ,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,EAAE,CAAC;YAEvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;YAC9K,CAAC;YACD,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;QAC3B,CAAC;QAED,gDAAgD;QAChD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAElF,8BAA8B;QAC9B,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,oBAAoB;QACzE,MAAM,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;QAG/D,MAAM,OAAO,GAAG;YACd,KAAK,EAAE,gBAAgB;YACvB,gBAAgB,EAAE,uBAAuB,CAAC,MAAM;YAChD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK;gBACzB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK;gBAC7B,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK;gBACvB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK;aACxB;YACD,yBAAyB,EAAE,MAAM,EAAE,qBAAqB;YACxD,SAAS,EAAE,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACxC,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,eAAe,EAAE,KAAK,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,cAAc,EAAE,OAAO;SACxB,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;SAIK;IACG,KAAK,CAAC,iBAAiB,CAAC,OAAqD;QACnF,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACnB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;YACpF,OAAO,EAAE,CAAC,CAAC,mDAAmD;QAChE,CAAC;QAED,+EAA+E;QAC/E,mFAAmF;QACnF,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC;YACtD,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;YAC1E,iDAAiD;SAClD,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAE/C,mFAAmF;QACnF,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC7D,6GAA6G;YAC7G,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACrE,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC,sCAAsC;YAChH,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC,wCAAwC;YAEtH,OAAO,YAAY,IAAI,SAAS,IAAI,WAAW,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC;QAEzG,qFAAqF;QACrF,OAAO,cAAc,CAAC;IACxB,CAAC;IAGD;;;SAGK;IACG,mBAAmB;QACzB,sDAAsD;QACtD,uEAAuE;QACvE,OAAO;YACL,0EAA0E;YAC1E,6DAA6D;YAC7D,4DAA4D;YAC5D,0EAA0E;YAC1E,WAAW;YACX,gHAAgH;YAChH,gHAAgH;YAChH,sEAAsE;YACtE,0GAA0G;YAC1G,gIAAgI;YAChI,0FAA0F;YAC1F,qBAAqB,EAAE;gBACrB,oBAAoB,EAAE,IAAA,kBAAS,EAAS,4CAA4C,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;gBACvG,gBAAgB,EAAE,IAAA,kBAAS,EAAS,wCAAwC,EAAE,KAAK,CAAC;gBACpF,YAAY,EAAE,IAAA,kBAAS,EAAS,oCAAoC,EAAE,IAAA,qCAAe,GAAE,CAAC;gBACxF,eAAe,EAAE,IAAA,kBAAS,EAAuB,uCAAuC,EAAE,SAAS,CAAC;gBACpG,iBAAiB,EAAE,IAAA,kBAAS,EAAuB,yCAAyC,EAAE,SAAS,CAAC;gBACxG,wBAAwB,EAAE,IAAA,kBAAS,EAAU,gDAAgD,EAAE,KAAK,CAAC;aACtG;SACF,CAAC;IACJ,CAAC;IAED;;;;SAIK;IACG,KAAK,CAAC,sBAAsB,CAAC,QAA2D;QAC9F,oFAAoF;QACpF,mDAAmD;QACnD,WAAW;QACX,yDAAyD;QACzD,kGAAkG;QAClG,IAAI;QACJ,yDAAyD;QACzD,kGAAkG;QAClG,IAAI;QACJ,qFAAqF;QACrF,IAAI,QAAQ,CAAC,oBAAoB,KAAK,SAAS;YAAE,MAAM,IAAA,kBAAS,EAAC,4CAA4C,EAAE,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QAC9I,IAAI,QAAQ,CAAC,gBAAgB,KAAK,SAAS;YAAE,MAAM,IAAA,kBAAS,EAAC,wCAAwC,EAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAClI,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS;YAAE,MAAM,IAAA,kBAAS,EAAC,oCAAoC,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtH,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS;YAAE,MAAM,IAAA,kBAAS,EAAC,uCAAuC,EAAE,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,kDAAkD;QAClL,IAAI,QAAQ,CAAC,iBAAiB,KAAK,SAAS;YAAE,MAAM,IAAA,kBAAS,EAAC,yCAAyC,EAAE,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,kDAAkD;QACxL,IAAI,QAAQ,CAAC,wBAAwB,KAAK,SAAS;YAAE,MAAM,IAAA,kBAAS,EAAC,gDAAgD,EAAE,QAAQ,CAAC,wBAAwB,CAAC,CAAC;QAG1J,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,EAAE,QAAQ,CAAC,CAAC;QAC9E,6FAA6F;IAC/F,CAAC;CACF;AAl4BD,kDAk4BC;AAED,wDAAwD;AACxD,mFAAmF;AACnF,yCAAyC;AACzC,SAAgB,yBAAyB,CAAC,OAAsB;IAC9D,mEAAmE;IACnE,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAC1C,CAAC;AAED,sDAAsD;AACzC,QAAA,mBAAmB,GAAG,yBAAyB,EAAE,CAAC", "sourcesContent": ["/**\n * AI Agent Tool for comprehensive Linting and Diagnostics management in VS Code.\n * Provides actions to list, filter, fix, run lint tasks, batch process,\n * summarize, and search through diagnostics across the workspace.\n * Implemented as a multi-action tool coordinating VS Code APIs.\n */\n\nimport * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult, ToolActionDefinition } from './tool.ts.backup'; // Assuming tool.ts defines ITool, etc.\nimport { AgentContext } from '../agents/agentUtilities/agent'; // Assuming AgentContext is defined here\nimport { getDefaultShell } from './terminalCommandTool'; // Assuming AgentContext is defined here\nimport { z } from 'zod'; // Zod for schema validation\n// Assume these are available paths in the project\nimport { Logger } from '../logger'; // Assuming logging utility is available\nimport { getConfig, setConfig } from '../config'; // Assuming configuration utility is available\nimport * as nodePath from 'path'; // Node.js path module\n\nimport {\n  DiagnosticInput,\n  DiagnosticR<PERSON>ult,\n  DiagnosticInputType,\n  DiagnosticResultType\n} from './schemas/diagnosticSchemas';\n\n// Use the imported schemas\nconst ListDiagnosticsInputSchema = z.object({\n  /** Optional file path or glob pattern to filter diagnostics by file. */\n  filePath: z.string().optional().describe('Optional file path or glob pattern to filter diagnostics by file.'),\n  /** Optional filter by diagnostic severity. Can be a single severity or array of severities. */\n  severity: z.union([z.enum(['error', 'warning', 'info', 'hint']), z.enum(['error', 'warning', 'info', 'hint']).array()]).optional().describe('Optional filter by severity.'),\n  /** Optional filter by diagnostic code. */\n  code: z.string().optional().describe('Optional filter by diagnostic code.'),\n  /** Optional filter by source. */\n  source: z.string().optional().describe('Optional filter by diagnostic source.'),\n  /** Optional maximum number of diagnostics to return per file. */\n  maxPerFile: z.number().int().positive().optional().describe('Optional maximum number of diagnostics per file.'),\n  /** Optional maximum total number of diagnostics to return across all files. */\n  limit: z.number().int().positive().optional().describe('Optional maximum total number of diagnostics to return across all files.'),\n}).describe('Input for listing diagnostics.');\n\nconst FixDiagnosticInputSchema = z.object({\n  /** Required file path (absolute or relative to workspace) of the diagnostic. */\n  filePath: z.string().min(1).describe('Required file path (absolute or relative to workspace root) of the diagnostic.'),\n  /** Required zero-based index of the diagnostic within the list for the specified file (obtained from \"list\" action). */\n  diagnosticIndex: z.number().int().nonnegative().describe('Required zero-based index of the diagnostic to fix.'),\n  /** Optional index of the code action to apply if multiple quick fixes are available for the diagnostic. Defaults to 0 (first available). */\n  codeActionIndex: z.number().int().nonnegative().optional().default(0).describe('Optional index of the quick fix action to apply.'),\n}).describe('Input for applying a quick fix to a specific diagnostic.');\n\nconst RunLintTaskInputSchema = z.object({\n  /** Optional name of the specific lint task to run. Defaults to the workspace's default build task if omitted, or attempts a common \"lint\" task. */\n  taskName: z.string().optional().describe('Optional name of the specific lint task to run. Defaults to the workspace\\'s default build task or attempts a \"lint\" task.'),\n  /** Optional wait for the task to complete before returning. Defaults to false. */\n  wait: z.boolean().optional().default(false).describe('If true, wait for the task to complete before returning.'),\n}).describe('Input for running a configured lint task.');\n\nconst ApplyCodeActionsInputSchema = z.object({\n  /** Required file path (absolute or relative to workspace) where code actions should be applied. */\n  filePath: z.string().min(1).describe('Required file path (absolute or relative to workspace root).'),\n  /** Optional filter by diagnostic severity for actions. */\n  severity: z.union([z.enum(['error', 'warning', 'info', 'hint']), z.enum(['error', 'warning', 'info', 'hint']).array()]).optional().describe('Optional filter by severity for actions.'),\n  /** Optional filter by diagnostic code for actions. */\n  code: z.union([z.string(), z.string().array()]).optional().describe('Optional filter by diagnostic code for actions.'),\n  /** Optional filter by source for actions. */\n  source: z.union([z.string(), z.string().array()]).optional().describe('Optional filter by source for actions.'),\n  /** Optional flag to apply only \"fix all\" actions if available and matching filters. */\n  applyFixAll: z.boolean().optional().default(false).describe('If true, attempt to apply \"fix all\" actions if available for the file and filters.'),\n  /** Optional range to limit where code actions are sought (e.g., for fixing a specific section). */\n  range: z.object({\n    start: z.object({ line: z.number().int().nonnegative(), character: z.number().int().nonnegative() }),\n    end: z.object({ line: z.number().int().nonnegative(), character: z.number().int().nonnegative() }),\n  }).optional().describe('Optional range within the file to apply code actions (0-based).'),\n}).describe('Input for applying available code actions (fixes) to a file.');\n\n\nconst SummarizeDiagnosticsInputSchema = z.object({\n  /** Optional file path to summarize diagnostics for. If omitted, summarizes for the entire workspace (all open documents). */\n  filePath: z.string().optional().describe('Optional file path to summarize diagnostics for. If omitted, summarizes for all open documents.'),\n  /** Optional filter by diagnostic severity for the summary. */\n  severity: z.union([z.enum(['error', 'warning', 'info', 'hint']), z.enum(['error', 'warning', 'info', 'hint']).array()]).optional().describe('Optional filter by severity for the summary.'),\n  /** Optional filter by source for the summary. */\n  source: z.union([z.string(), z.string().array()]).optional().describe('Optional filter by source for the summary.'),\n  /** Optional limit on the number of top issues to highlight in the summary. */\n  topIssuesLimit: z.number().int().positive().optional().describe('Optional limit on the number of top diagnostic messages/codes to highlight.'),\n}).describe('Input for summarizing diagnostics.');\n\nconst SearchDiagnosticsInputSchema = z.object({\n  /** The search query string to find matching diagnostics. Searches within message, code, and source. */\n  query: z.string().min(1).describe('The search query string to find matching diagnostics.'),\n  /** Optional file path to limit the search to. If omitted, searches all open documents. */\n  filePath: z.string().optional().describe('Optional file path to limit the search to.'),\n  /** Optional filter by diagnostic severity for the search. */\n  severity: z.union([z.enum(['error', 'warning', 'info', 'hint']), z.enum(['error', 'warning', 'info', 'hint']).array()]).optional().describe('Optional filter by severity.'),\n  /** Optional filter by source for the search. */\n  source: z.union([z.string(), z.string().array()]).optional().describe('Optional filter by source.'),\n  /** Optional limit on the number of search results. */\n  limit: z.number().int().positive().optional().describe('Optional maximum number of search results.'),\n}).describe('Input for searching diagnostics.');\n\n\n// Schema for tool-specific configuration updates (example)\nconst UpdateLintToolSettingsInputSchema = z.object({\n  // Define properties matching the config structure this tool manages\n  maxOutputBufferBytes: z.number().int().positive().optional(),\n  defaultTimeoutMs: z.number().int().positive().optional(),\n  defaultShell: z.string().optional(),\n  allowedCommands: z.string().array().optional().nullable(), // Allow setting to null to clear\n  forbiddenCommands: z.string().array().optional().nullable(), // Allow setting to null to clear\n  useExecForSimpleCommands: z.boolean().optional(),\n  // Add other relevant settings here\n}).partial().describe('Partial settings for the LintDiagnosticsTool.');\n\n\n/**\n * Helper function to convert Zod enum or string union input to a string array.\n * @param input The input from the Zod schema.\n * @returns A string array or undefined.\n */\nfunction normalizeStringOrStringArrayInput(input: string | string[] | undefined): string[] | undefined {\n  if (input === undefined) return undefined;\n  if (Array.isArray(input)) return input;\n  return [input];\n}\n\n\n/**\n * Implements the ITool interface for comprehensive Linting and Diagnostics management.\n * This is a multi-action tool coordinating operations via VS Code APIs.\n */\nexport class LintDiagnosticsTool implements ITool {\n  readonly id = 'lint_diagnostics'; // Unique ID\n  readonly name = 'lint_diagnostics'; // Machine-friendly name\n  readonly description = 'Provides actions to manage code diagnostics (linting/compiler issues): list, filter, apply fixes, run tasks, summarize, search, and update tool settings.';\n  readonly category = 'Development'; // Tool category\n  readonly type = 'multi-action'; // Multi-action tool\n\n  readonly actions: Record<string, ToolActionDefinition>;\n\n  /**\n     * Constructs a new LintDiagnosticsTool instance.\n     * @param context Optional AgentContext (not strictly needed for this tool's current implementation).\n     */\n  constructor(context?: AgentContext) {\n    // Define the available actions using Zod schemas\n    this.actions = {\n      'list': {\n        description: 'Lists diagnostics (errors, warnings, infos, hints) for a specific file or all currently open files, optionally filtered by severity, code, source, message substring, and limited.',\n        inputSchema: ListDiagnosticsInputSchema,\n      },\n      'fix': {\n        description: 'Applies the default quick fix to a specific diagnostic identified by file path and its index from a previous list action.',\n        inputSchema: FixDiagnosticInputSchema,\n      },\n      'apply_code_actions': {\n        description: 'Applies available quick fixes or other code actions to diagnostics in a file or a specific range, optionally filtered by severity, code, and source. Can apply \"fix all\" actions if requested.',\n        inputSchema: ApplyCodeActionsInputSchema,\n      },\n      'run_task': {\n        description: 'Runs a configured VS Code task, typically used for triggering linters or build processes.',\n        inputSchema: RunLintTaskInputSchema,\n      },\n      'summarize': {\n        description: 'Generates a summary report of diagnostics for a file or the entire workspace (all open documents), optionally filtered by severity and source, and highlighting top issues.',\n        inputSchema: SummarizeDiagnosticsInputSchema,\n      },\n      'search': {\n        description: 'Searches diagnostics across open documents based on a query string within the message, code, or source, optionally filtered by severity, source, and limited.',\n        inputSchema: SearchDiagnosticsInputSchema,\n      },\n      'get_settings': {\n        description: 'Retrieves the current configuration settings for the LintDiagnosticsTool.',\n        inputSchema: z.object({}).describe('No parameters required.'), // Empty schema for get_settings\n      },\n      'update_settings': {\n        description: 'Updates configuration settings for the LintDiagnosticsTool. Requires specific input parameters corresponding to setting keys.',\n        inputSchema: UpdateLintToolSettingsInputSchema,\n      },\n    };\n\n    Logger.instance.info('LintDiagnosticsTool initialized.');\n  }\n\n\n  /**\n     * Provides the structured definition for the LLM.\n     * Generates JSON schema definitions for each action.\n     * Conforms to ITool.getDefinitionForModel.\n     * @returns An array of JSON schema definitions suitable for LLM tool calling.\n     */\n  getDefinitionForModel(): any {\n    // For a multi-action tool, the LLM typically sees each action as a distinct callable function.\n    // The names should be unique across all tools/actions exposed to the LLM.\n    // A common pattern is \"tool_name__action_name\".\n    return Object.entries(this.actions).map(([actionName, actionDef]) => {\n      // Generate JSON schema for the action's input schema using Zod's toJSON()\n      // Use casting for robustness against potential Zod variations\n      const schemaObject = (actionDef.inputSchema as any).toJSON ? (actionDef.inputSchema as any).toJSON() : undefined;\n\n      if (!schemaObject) {\n        Logger.instance.error(`LintDiagnosticsTool getDefinitionForModel: Failed to generate JSON schema for action '${actionName}'.`);\n        // Return a minimal definition if schema generation fails\n        return {\n          name: `${this.name}__${actionName}`,\n          description: actionDef.description || `Performs the ${actionName} action.`,\n          parameters: { type: 'object', properties: {} }, // Empty params if schema fails\n        };\n      }\n\n      // Apply specific schema adjustments for better LLM compatibility if needed\n      // Example: For 'severity' or 'code' which can be string or string[], clarify to LLM\n      if (schemaObject.properties?.severity) {\n        schemaObject.properties.severity = {\n          type: 'array', // Represent as array for simplicity for LLM\n          items: { type: 'string', enum: ['error', 'warning', 'info', 'hint'] },\n          description: schemaObject.properties.severity.description,\n          uniqueItems: true,\n        };\n      }\n      if (schemaObject.properties?.code) {\n        schemaObject.properties.code = {\n          type: 'array', // Represent as array for simplicity for LLM\n          items: { type: 'string' },\n          description: schemaObject.properties.code.description,\n          uniqueItems: true,\n        };\n      }\n      if (schemaObject.properties?.source) {\n        schemaObject.properties.source = {\n          type: 'array', // Represent as array for simplicity for LLM\n          items: { type: 'string' },\n          description: schemaObject.properties.source.description,\n          uniqueItems: true,\n        };\n      }\n      if (schemaObject.properties?.range) {\n        // Adjust the range representation for LLM clarity\n        schemaObject.properties.range = {\n          type: 'object',\n          description: schemaObject.properties.range.description || 'A range with start and end positions (0-based line and character).',\n          properties: {\n            start: { type: 'object', properties: { line: { type: 'integer' }, character: { type: 'integer' } }, required: ['line', 'character'], description: 'Start position.' },\n            end: { type: 'object', properties: { line: { type: 'integer' }, character: { type: 'integer' } }, required: ['line', 'character'], description: 'End position.' },\n          },\n          required: ['start', 'end'],\n        };\n      }\n\n\n      return {\n        // Action name format for the LLM\n        name: `${this.name}__${actionName}`,\n        description: actionDef.description,\n        parameters: schemaObject, // The generated JSON schema\n      };\n    });\n  }\n\n  /**\n     * Executes the specified linting or diagnostics action.\n     * The calling framework is responsible for parsing the LLM's tool call,\n     * extracting the action name and parameters, and validating parameters\n     * against the specific action's schema before calling this method.\n     *\n     * @param actionName - The name of the action to execute (e.g., 'list', 'fix').\n     * @param input - The validated input parameters for the action.\n     * @param context - Optional agent context.\n     * @returns A promise resolving to the tool's result.\n     */\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    if (!actionName) {\n      const errorMsg = 'LintDiagnosticsTool requires an actionName for multi-action execution.';\n      Logger.instance.error(errorMsg, { input });\n      return { success: false, error: errorMsg, toolId: this.id, actionName };\n    }\n\n    const actionDefinition = this.actions[actionName];\n    if (!actionDefinition) {\n      const errorMsg = `LintDiagnosticsTool received unknown actionName: ${actionName}. Available actions: ${Object.keys(this.actions).join(', ')}.`;\n      Logger.instance.error(errorMsg, { input });\n      return { success: false, error: errorMsg, toolId: this.id, actionName };\n    }\n\n    // Re-validate input here for robustness, although framework should do it.\n    const parsedInput = actionDefinition.inputSchema.safeParse(input);\n    if (!parsedInput.success) {\n      const formattedError = parsedInput.error.format();\n      const errorMsg = `Invalid input for action '${actionName}': ${Object.entries(formattedError)\n        .filter(([key]) => key !== '_errors')\n        .map(([key, value]) => `${key}: ${(value as any)._errors.join(', ')}`)\n        .join('; ')}`;\n      Logger.instance.error(errorMsg, { input, validationErrors: formattedError });\n      return { success: false, error: errorMsg, toolId: this.id, actionName };\n    }\n    const validatedInput = parsedInput.data;\n\n\n    Logger.instance.info(`Executing LintDiagnosticsTool action: \"${actionName}\"`);\n    Logger.instance.debug(`Input for action \"${actionName}\":`, validatedInput); // Log validated input\n\n    try {\n      let resultOutput: any; // Output of the specific action\n      let successStatus = true; // Default success\n\n      // --- Execute the specific action logic ---\n      switch (actionName) {\n        case 'list':\n          const listInput = validatedInput as z.infer<typeof ListDiagnosticsInputSchema>;\n          const diagnostics = await this.listDiagnostics(listInput);\n          resultOutput = diagnostics; // Return the list of diagnostics\n          Logger.instance.info(`Action 'list' completed. Found ${diagnostics.length} diagnostics.`);\n          break;\n\n        case 'fix':\n          const fixInput = validatedInput as z.infer<typeof FixDiagnosticInputSchema>;\n          // Call helper to apply a single quick fix by index\n          const fixResult = await this.applySpecificDiagnosticFix(fixInput);\n          resultOutput = fixResult.output;\n          successStatus = fixResult.success;\n          if (fixResult.error) Logger.instance.error(`Action 'fix' failed: ${fixResult.error}`);\n          break;\n\n        case 'apply_code_actions':\n          const applyInput = validatedInput as z.infer<typeof ApplyCodeActionsInputSchema>;\n          // Call helper to apply multiple code actions based on filters/range\n          const applyResult = await this.applyCodeActions(applyInput);\n          resultOutput = applyResult.output;\n          successStatus = applyResult.success;\n          if (applyResult.error) Logger.instance.error(`Action 'apply_code_actions' failed: ${applyResult.error}`);\n          break;\n\n        case 'run_task':\n          const runTaskInput = validatedInput as z.infer<typeof RunLintTaskInputSchema>;\n          // Execute VS Code command to run a task\n          const taskName = runTaskInput.taskName;\n          const taskCommand = taskName ? 'workbench.action.tasks.runTask' : 'workbench.action.tasks.runDefaultBuildTask'; // Often lint is the default build task\n          const taskArgs = taskName ? [taskName] : [];\n          const wait = runTaskInput.wait ?? false;\n\n          try {\n            // Find the task first if a name is given\n            let taskToRun: vscode.Task | undefined;\n            if (taskName) {\n              const tasks = await vscode.tasks.fetchTasks();\n              taskToRun = tasks.find(t => t.name === taskName && t.source !== 'Workspace'); // Find non-workspace tasks first\n              if (!taskToRun && vscode.workspace.name) {\n                // Also look for tasks defined in workspace (tasks.json)\n                taskToRun = tasks.find(t => t.name === taskName && t.source === `Workspace (${vscode.workspace.name})`);\n              }\n              if (!taskToRun) {\n                throw new Error(`Task '${taskName}' not found.`);\n              }\n            } else {\n              // Attempt to get default build task if no name given\n              taskToRun = await vscode.tasks.fetchTasks({ type: 'shell' }).then(tasks => tasks.find(t => t.group === vscode.TaskGroup.Build));\n              if (!taskToRun) {\n                // Fallback to attempt running a task named 'lint' or 'build'\n                taskToRun = (await vscode.tasks.fetchTasks()).find(t => t.name === 'lint' || t.name === 'build');\n              }\n              if (!taskToRun) {\n                throw new Error('No specific task name provided and couldn\\'t find default build task or task named \\'lint\\'/\\'build\\'.');\n              }\n              Logger.instance.info(`No task name specified. Attempting to run default task: ${taskToRun.name}`);\n            }\n\n\n            const execution = await vscode.tasks.executeTask(taskToRun);\n\n            if (wait) {\n              Logger.instance.info(`Waiting for task '${taskToRun.name}' to complete...`);\n              // Wait for the task to terminate\n              await new Promise<void>((resolve) => {\n                const disposable = vscode.tasks.onDidEndTaskProcess(e => {\n                  if (e.execution === execution) {\n                    Logger.instance.info(`Task '${taskToRun!.name}' finished with exit code ${e.exitCode}.`);\n                    disposable.dispose();\n                    resolve();\n                  }\n                });\n                // Add a timeout in case the task never ends or onDidEndTaskProcess doesn't fire as expected\n                const waitTimeout = setTimeout(() => {\n                  Logger.instance.warn(`Wait for task '${taskToRun!.name}' timed out after ${getConfig<number>('tools.taskWaitTimeout', 30000)}ms.`); // Configurable timeout\n                  disposable.dispose();\n                  resolve(); // Resolve even on timeout\n                }, getConfig<number>('tools.taskWaitTimeout', 30000));\n              });\n            }\n\n\n            resultOutput = `VS Code task '${taskToRun.name}' triggered${wait ? ' and waited for completion' : ''}. Check VS Code terminal for output.`;\n            Logger.instance.info(resultOutput);\n\n          } catch (error: any) {\n            successStatus = false;\n            resultOutput = `Failed to trigger VS Code task '${taskName || 'default'}': ${error.message || error}`;\n            Logger.instance.error(resultOutput, error);\n          }\n          break;\n\n        case 'summarize':\n          const summarizeInput = validatedInput as z.infer<typeof SummarizeDiagnosticsInputSchema>;\n          resultOutput = await this.summarizeDiagnostics(summarizeInput);\n          successStatus = true; // Summary generation is generally successful\n          Logger.instance.info('Action \\'summarize\\' completed.');\n          break;\n\n        case 'search':\n          const searchInput = validatedInput as z.infer<typeof SearchDiagnosticsInputSchema>;\n          resultOutput = await this.searchDiagnostics(searchInput);\n          Logger.instance.info(`Action 'search' completed. Found ${resultOutput.length} matches.`);\n          break;\n\n        case 'get_settings':\n          const toolSettings = this.getLintToolSettings();\n          resultOutput = toolSettings;\n          successStatus = true;\n          Logger.instance.info('Action \\'get_settings\\' completed.');\n          break;\n\n        case 'update_settings':\n          const updateSettingsInput = validatedInput as z.infer<typeof UpdateLintToolSettingsInputSchema>;\n          // Call helper to update settings\n          // This helper should handle persistence (e.g., writing to VS Code settings)\n          await this.updateLintToolSettings(updateSettingsInput);\n          resultOutput = { status: 'settings updated', updated: Object.keys(updateSettingsInput) };\n          successStatus = true;\n          Logger.instance.info('Action \\'update_settings\\' completed.');\n          break;\n\n\n        default:\n          // This case should ideally not be reached due to Zod validation of actionName prior to switch\n          const defensiveUnknownActionError = `Defensive check failed: Unknown action ${actionName}`;\n          Logger.instance.error(defensiveUnknownActionError);\n          return { success: false, error: defensiveUnknownActionError, toolId: this.id, actionName };\n      }\n\n      // Return final result\n      return {\n        success: successStatus,\n        output: resultOutput,\n        toolId: this.id,\n        actionName: actionName,\n        // Add usage/metadata if available\n      };\n\n    } catch (error: any) {\n      // Catch any unhandled errors during action execution\n      const errorMessage = `Error during LintDiagnosticsTool action \"${actionName}\": ${error.message || error}`;\n      Logger.instance.error(errorMessage, error);\n      return { success: false, error: errorMessage, toolId: this.id, actionName };\n    }\n  }\n\n  // --- Helper methods for action logic ---\n\n  /**\n     * Helper to resolve a file path string to a VS Code Uri.\n     * Handles absolute paths and paths relative to the first workspace folder.\n     * @param filePath The input file path string.\n     * @returns The corresponding VS Code Uri.\n     * @throws Error if no workspace folder is open and the path is relative, or if path is invalid.\n     */\n  private resolveFilePathToUri(filePath: string): vscode.Uri {\n    if (nodePath.isAbsolute(filePath)) {\n      return vscode.Uri.file(filePath);\n    }\n\n    const workspaceFolders = vscode.workspace.workspaceFolders;\n    if (workspaceFolders && workspaceFolders.length > 0) {\n      // Resolve relative to the first workspace folder\n      const absolutePath = nodePath.resolve(workspaceFolders[0].uri.fsPath, filePath);\n      return vscode.Uri.file(absolutePath);\n    } else {\n      // If no workspace folder, and path is relative, this is ambiguous.\n      // Could throw or resolve relative to process.cwd(), safer to throw.\n      throw new Error(`Cannot resolve relative path \"${filePath}\": No workspace folder is open.`);\n    }\n  }\n\n\n  /**\n     * Helper to list diagnostics for specified URIs, with filtering and limiting.\n     * Internal helper used by 'list', 'summarize', 'search'.\n     * @param urisToProcess Array of VS Code Uris to process.\n     * @param options Filtering and limiting options.\n     * @returns An array of serializable diagnostic objects.\n     */\n  private async listDiagnosticsForUris(urisToProcess: vscode.Uri[], options: z.infer<typeof ListDiagnosticsInputSchema>): Promise<any[]> {\n    const allDiagnostics: vscode.Diagnostic[] = [];\n\n    for (const uri of urisToProcess) {\n      // Use ensureDocumentOpen to make sure diagnostics are current\n      try {\n        // await vscode.workspace.openTextDocument(uri); // Opening doc ensures diagnostics are pushed by extensions\n        // A lighter approach is to just get diagnostics, assuming they are updated asyncly\n        // But opening is safer for getting latest state before querying.\n        const doc = await vscode.workspace.openTextDocument(uri); // Open and implicitly wait for diagnostics update?\n\n        const fileDiagnostics = vscode.languages.getDiagnostics(uri);\n\n        // Apply filters\n        const normalizedSeverities = normalizeStringOrStringArrayInput(options.severity);\n        const normalizedCodes = normalizeStringOrStringArrayInput(options.code);\n        const normalizedSources = normalizeStringOrStringArrayInput(options.source);\n        // Message filter removed as it's not part of the schema\n\n        const filteredDiagnostics = fileDiagnostics.filter(diag => {\n          if (normalizedSeverities && !normalizedSeverities.includes(vscode.DiagnosticSeverity[diag.severity].toLowerCase())) return false;\n          if (normalizedCodes && !(diag.code === undefined || diag.code === null) && !normalizedCodes.includes(diag.code.toString())) return false;\n          if (normalizedSources && !(diag.source === undefined || diag.source === null) && !normalizedSources.includes(diag.source.toLowerCase())) return false;\n          return true;\n        });\n\n        // Add uri and range info for serialization\n        const serializableDiagnostics = filteredDiagnostics.map(diag => ({\n          // Copy relevant diagnostic properties\n          severity: diag.severity,\n          code: diag.code?.toString(), // Ensure code is string\n          source: diag.source,\n          message: diag.message,\n          // Include location info for the diagnostic\n          resource: uri.toString(), // File URI string\n          range: new vscode.Range(diag.range.start.line, diag.range.start.character, diag.range.end.line, diag.range.end.character),\n          // Include related information if any (also needs serialization)\n          relatedInformation: diag.relatedInformation?.map(ri => new vscode.DiagnosticRelatedInformation(\n            new vscode.Location(ri.location.uri, ri.location.range),\n            ri.message\n          )),\n          // Include tags if any\n          tags: diag.tags,\n        }));\n\n        allDiagnostics.push(...serializableDiagnostics);\n\n      } catch (error: any) {\n        Logger.instance.error(`Error processing diagnostics for URI ${uri.toString()}:`, error);\n        // Continue with next URI\n      }\n    }\n\n    // Apply overall limit if specified\n    const finalLimitedDiagnostics = options.limit ? allDiagnostics.slice(0, options.limit) : allDiagnostics;\n\n    // Note: Currently returns a flat array. Could group by file path if needed in output.\n    return finalLimitedDiagnostics;\n  }\n\n\n  /**\n     * Helper for the 'list' action. Gets diagnostics for a file or all open files.\n     * @param options List filtering options.\n     * @returns An array of serializable diagnostic objects.\n     */\n  private async listDiagnostics(options: z.infer<typeof ListDiagnosticsInputSchema>): Promise<any[]> {\n    let urisToProcess: vscode.Uri[] = [];\n\n    if (options.filePath) {\n      try {\n        // Resolve and validate the file path\n        const fileUri = this.resolveFilePathToUri(options.filePath);\n        // Check if the file exists before getting diagnostics\n        await vscode.workspace.fs.stat(fileUri);\n        urisToProcess.push(fileUri);\n      } catch (error: any) {\n        Logger.instance.warn(`File not found for diagnostics listing: ${options.filePath}`, error);\n        // Return empty array if file not found or path resolution fails\n        return [];\n      }\n    } else {\n      // Get diagnostics for all currently open text documents\n      urisToProcess = vscode.workspace.textDocuments.map(doc => doc.uri);\n    }\n\n    return this.listDiagnosticsForUris(urisToProcess, options);\n  }\n\n\n  /**\n     * Helper for the 'fix' action. Applies a specific quick fix (code action) to a diagnostic by index.\n     * @param options File path, diagnostic index, and code action index.\n     * @returns Result indicating success or failure.\n     */\n  private async applySpecificDiagnosticFix(options: z.infer<typeof FixDiagnosticInputSchema>): Promise<{ success: boolean; output?: string; error?: string }> {\n    try {\n      const fileUri = this.resolveFilePathToUri(options.filePath);\n\n      // Ensure the document is open and active for quick fixes to be readily available\n      const document = await vscode.workspace.openTextDocument(fileUri);\n      await vscode.window.showTextDocument(document); // Make it visible/active\n\n      // Get current diagnostics for the document\n      const diags = vscode.languages.getDiagnostics(fileUri);\n\n      // Find the target diagnostic by index\n      const targetDiag = diags[options.diagnosticIndex];\n      if (!targetDiag) {\n        return { success: false, error: `No diagnostic found at index ${options.diagnosticIndex} for file ${options.filePath}. Diagnostic indices are volatile and can change.` };\n      }\n\n      // Use VS Code's executeCodeActionProvider command to find applicable actions for the diagnostic's range\n      // Pass 'quickfix' kind to get only quick fixes\n      const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>(\n        'vscode.executeCodeActionProvider',\n        fileUri,\n        targetDiag.range,\n        { only: vscode.CodeActionKind.QuickFix.value } // Request only quick fixes\n      );\n\n      if (!codeActions || codeActions.length === 0) {\n        Logger.instance.info(`No quick fixes found for diagnostic at index ${options.diagnosticIndex}.`);\n        return { success: false, error: `No quick fixes found for diagnostic at index ${options.diagnosticIndex}.` };\n      }\n\n      // Find the target code action by index\n      const targetAction = codeActions[options.codeActionIndex];\n      if (!targetAction) {\n        return { success: false, error: `No quick fix action found at index ${options.codeActionIndex} for the diagnostic. Found ${codeActions.length} fixes.` };\n      }\n\n      // Execute the command associated with the code action or apply the edit\n      if (targetAction.command) {\n        Logger.instance.debug(`Applying quick fix command: ${targetAction.title}`);\n        await vscode.commands.executeCommand(targetAction.command.command, ...(targetAction.command.arguments || []));\n      } else if (targetAction.edit) {\n        Logger.instance.debug(`Applying quick fix edit: ${targetAction.title}`);\n        const success = await vscode.workspace.applyEdit(targetAction.edit);\n        if (!success) {\n          throw new Error(`Failed to apply workspace edit for quick fix \"${targetAction.title}\".`);\n        }\n      } else {\n        throw new Error(`Quick fix action \"${targetAction.title}\" has neither a command nor an edit. Cannot apply programmatically.`);\n      }\n\n      // Save the document after applying changes\n      await document.save();\n      // Give VS Code a moment to update diagnostics after the fix\n      await new Promise(resolve => setTimeout(resolve, 100));\n\n\n      return { success: true, output: `Quick fix \"${targetAction.title}\" applied for diagnostic at index ${options.diagnosticIndex}.` };\n\n    } catch (error: any) {\n      Logger.instance.error(`Failed to apply quick fix for diagnostic index ${options.diagnosticIndex} in ${options.filePath}:`, error);\n      return { success: false, error: `Failed to apply fix: ${error.message || error}` };\n    }\n  }\n\n\n  /**\n     * Helper for the 'apply_code_actions' action. Applies multiple code actions (fixes) to a file based on filters.\n     * @param options File path, optional range, and filters.\n     * @returns Result indicating success and count of applied actions.\n     */\n  private async applyCodeActions(options: z.infer<typeof ApplyCodeActionsInputSchema>): Promise<{ success: boolean; output?: string; error?: string; appliedCount?: number }> {\n    try {\n      const fileUri = this.resolveFilePathToUri(options.filePath);\n\n      // Ensure the document is open and active\n      const document = await vscode.workspace.openTextDocument(fileUri);\n      await vscode.window.showTextDocument(document); // Makes it visible/active\n\n      // Determine the range to request code actions for\n      const range = options.range ? new vscode.Range(options.range.start.line, options.range.start.character, options.range.end.line, options.range.end.character) : new vscode.Range(0, 0, document.lineCount, 0);\n\n      // Define the kinds of code actions to request. Prioritize 'fix all' if requested.\n      const kindsToRequest: string[] = [];\n      if (options.applyFixAll) {\n        // Request source.fixAll actions and common specific ones like 'eslint.fixAll'\n        kindsToRequest.push(vscode.CodeActionKind.SourceFixAll.value);\n        kindsToRequest.push('eslint.fixAll'); // Common specific source action\n        // Add other known fix-all kinds if necessary\n      }\n      // Always request QuickFixes unless only fix-all is explicitly meant\n      if (!options.applyFixAll) { // Only include if not *only* fix-all\n        kindsToRequest.push(vscode.CodeActionKind.QuickFix.value);\n      } else {\n        // If applyFixAll is true, still allow individual quick fixes if needed?\n        // For now, let's stick to explicitly requested kinds.\n      }\n\n\n      // Use VS Code's executeCodeActionProvider command to find applicable actions\n      const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>(\n        'vscode.executeCodeActionProvider',\n        fileUri,\n        range,\n        { only: kindsToRequest.join(',') } // Request specific kinds\n      );\n\n      if (!codeActions || codeActions.length === 0) {\n        Logger.instance.info(`No code actions found for file ${options.filePath} within the specified range/kinds.`);\n        return { success: true, output: 'No code actions found matching criteria.', appliedCount: 0 };\n      }\n\n      // Filter actions based on associated diagnostics' severity, code, source if provided\n      const normalizedSeverities = normalizeStringOrStringArrayInput(options.severity);\n      const normalizedCodes = normalizeStringOrStringArrayInput(options.code);\n      const normalizedSources = normalizeStringOrStringArrayInput(options.source);\n\n      const filteredActions = codeActions.filter(action => {\n        // Filter based on diagnostic properties if filters are applied\n        if (normalizedSeverities || normalizedCodes || normalizedSources) {\n          // Action must have diagnostics, and at least one diagnostic must match *all* filters\n          if (!action.diagnostics || action.diagnostics.length === 0) return false; // Action has no diagnostics to filter by\n\n          return action.diagnostics.some(diag => {\n            const severityMatch = normalizedSeverities ? normalizedSeverities.includes(vscode.DiagnosticSeverity[diag.severity].toLowerCase()) : true;\n            const codeMatch = normalizedCodes ? (diag.code !== undefined && diag.code !== null && normalizedCodes.includes(diag.code.toString())) : true;\n            const sourceMatch = normalizedSources ? (diag.source !== undefined && diag.source !== null && normalizedSources.includes(diag.source.toLowerCase())) : true;\n            return severityMatch && codeMatch && sourceMatch; // All filters must match at least one diagnostic\n          });\n        } else {\n          // No filters applied, include all actions found by kind\n          return true;\n        }\n      });\n\n\n      if (filteredActions.length === 0) {\n        Logger.instance.info(`No code actions found after applying filters for file ${options.filePath}.`);\n        return { success: true, output: 'No code actions found matching filters.', appliedCount: 0 };\n      }\n\n      Logger.instance.debug(`Found ${filteredActions.length} code actions after filtering. Applying them...`);\n\n      let appliedCount = 0;\n      // Execute each applicable action. Prioritize 'fix all' actions if they exist in the filtered list.\n      // Note: Applying multiple actions simultaneously might have side effects or overlaps.\n      // A robust approach might involve applying them sequentially with delays.\n      // For polyfill, apply sequentially.\n      const fixAllActions = filteredActions.filter(a => a.kind?.value.includes(vscode.CodeActionKind.SourceFixAll.value) || a.command?.command.includes('fixAll')); // Heuristic for command name\n      const otherActions = filteredActions.filter(a => !fixAllActions.includes(a));\n\n      // Apply fix-all actions first if requested and available\n      if (options.applyFixAll && fixAllActions.length > 0) {\n        Logger.instance.debug(`Applying ${fixAllActions.length} Fix All actions...`);\n        for (const action of fixAllActions) {\n          if (action.command) {\n            try {\n              await vscode.commands.executeCommand(action.command.command, ...(action.command.arguments || []));\n              appliedCount++;\n              Logger.instance.debug(`Applied Fix All command: ${action.title}`);\n              await new Promise(resolve => setTimeout(resolve, 100)); // Short delay\n            } catch (actionError: any) {\n              Logger.instance.error(`Failed to apply Fix All action \"${action.title}\" in ${options.filePath}:`, actionError);\n            }\n          } else if (action.edit) {\n            try {\n              const success = await vscode.workspace.applyEdit(action.edit);\n              if (success) {\n                appliedCount++;\n                Logger.instance.debug(`Applied Fix All edit: ${action.title}`);\n                await new Promise(resolve => setTimeout(resolve, 100)); // Short delay\n              } else {\n                Logger.instance.warn(`Failed to apply Fix All edit for code action \"${action.title}\" in ${options.filePath}.`);\n              }\n            } catch (editError: any) {\n              Logger.instance.error(`Error applying Fix All edit for code action \"${action.title}\":`, editError);\n            }\n          }\n        }\n      } else {\n        // If applyFixAll wasn't requested or no fix-all actions found, apply other actions\n        Logger.instance.debug(`Applying ${otherActions.length} other actions...`);\n        for (const action of otherActions) {\n          if (action.command) {\n            try {\n              await vscode.commands.executeCommand(action.command.command, ...(action.command.arguments || []));\n              appliedCount++;\n              Logger.instance.debug(`Applied command action: ${action.title}`);\n              await new Promise(resolve => setTimeout(resolve, 50)); // Short delay\n            } catch (actionError: any) {\n              Logger.instance.error(`Failed to apply command action \"${action.title}\" in ${options.filePath}:`, actionError);\n            }\n          } else if (action.edit) {\n            try {\n              const success = await vscode.workspace.applyEdit(action.edit);\n              if (success) {\n                appliedCount++;\n                Logger.instance.debug(`Applied edit action: ${action.title}`);\n                await new Promise(resolve => setTimeout(resolve, 50)); // Short delay\n              } else {\n                Logger.instance.warn(`Failed to apply edit for code action \"${action.title}\" in ${options.filePath}.`);\n              }\n            } catch (editError: any) {\n              Logger.instance.error(`Error applying edit for code action \"${action.title}\":`, editError);\n            }\n          } else {\n            Logger.instance.warn(`Code action \"${action.title}\" in ${options.filePath} has neither a command nor an edit. Cannot apply programmatically.`);\n          }\n        }\n      }\n\n\n      // Save the document after applying changes\n      await document.save();\n      // Give VS Code a moment to update diagnostics\n      await new Promise(resolve => setTimeout(resolve, 100));\n\n\n      return { success: true, output: `Applied ${appliedCount} code actions to ${options.filePath}.`, appliedCount };\n\n    } catch (error: any) {\n      Logger.instance.error(`Failed during applyCodeActions for ${options.filePath}:`, error);\n      return { success: false, error: `Failed to apply code actions: ${error.message || error}`, appliedCount: 0 };\n    }\n  }\n\n\n  /**\n     * Helper for the 'summarize' action. Summarizes diagnostics for a file or workspace.\n     * @param options Summary scope and filters.\n     * @returns A structured summary object.\n     */\n  private async summarizeDiagnostics(options: z.infer<typeof SummarizeDiagnosticsInputSchema>): Promise<any> {\n    let urisToProcess: vscode.Uri[] = [];\n    let scopeDescription = 'workspace';\n\n    if (options.filePath) {\n      try {\n        const fileUri = this.resolveFilePathToUri(options.filePath);\n        await vscode.workspace.fs.stat(fileUri); // Check file exists\n        urisToProcess.push(fileUri);\n        scopeDescription = options.filePath;\n      } catch (error: any) {\n        Logger.instance.warn(`File not found for diagnostics summary: ${options.filePath}`, error);\n        // Return summary indicating file not found\n        return {\n          scope: options.filePath,\n          error: `File not found: ${options.filePath}`,\n          totalDiagnostics: 0,\n          countsBySeverity: { error: 0, warning: 0, info: 0, hint: 0 },\n          countsBySourceAndSeverity: {},\n          topIssues: [],\n          filtersApplied: options,\n        };\n      }\n    } else {\n      // Summarize for the entire workspace (all open documents)\n      urisToProcess = vscode.workspace.textDocuments.map(doc => doc.uri);\n      if (urisToProcess.length === 0) {\n        Logger.instance.info('No open documents to summarize diagnostics for.');\n        return {\n          scope: 'workspace (no open documents)',\n          totalDiagnostics: 0,\n          countsBySeverity: { error: 0, warning: 0, info: 0, hint: 0 },\n          countsBySourceAndSeverity: {},\n          topIssues: [],\n          filtersApplied: options,\n        };\n      }\n    }\n\n    // Get all relevant diagnostics using the list helper, but apply filters here before counting\n    const allCandidateDiagnostics = await this.listDiagnosticsForUris(urisToProcess, {\n      // Pass through filters from summarize input\n      severity: options.severity,\n      source: Array.isArray(options.source) ? options.source[0] : options.source,\n      // No limit needed here as we need all for counting\n    });\n\n\n    // Count diagnostics by severity and source\n    const counts: { [severity: string]: { total: number; sources: { [source: string]: number } } } = {\n      'error': { total: 0, sources: {} },\n      'warning': { total: 0, sources: {} },\n      'info': { total: 0, sources: {} },\n      'hint': { total: 0, sources: {} },\n    };\n\n    for (const diag of allCandidateDiagnostics) {\n      // Use the already normalized severity string from the list helper's output\n      const severityName = diag.severity;\n      const sourceName = diag.source || 'unknown';\n      if (counts[severityName]) { // Ensure severity is one of the known types\n        counts[severityName].total++;\n        counts[severityName].sources[sourceName] = (counts[severityName].sources[sourceName] || 0) + 1;\n      }\n    }\n\n    // Get top issues (most frequent messages or codes)\n    const issueCounts: { [key: string]: { count: number; severity: string; source: string; code: string; message: string; exampleLocation?: { resource: string; range: any } } } = {};\n\n    for (const diag of allCandidateDiagnostics) {\n      // Create a key combining severity, source, code, and message for unique issue types\n      const severity = diag.severity;\n      const source = diag.source || 'unknown';\n      const code = diag.code || 'unknown';\n      const message = diag.message; // Use full message for grouping\n\n      const key = `${severity}:${source}:${code}:${message}`;\n\n      if (!issueCounts[key]) {\n        issueCounts[key] = { count: 0, severity, source, code, message, exampleLocation: diag.resource && diag.range ? { resource: diag.resource, range: diag.range } : undefined };\n      }\n      issueCounts[key].count++;\n    }\n\n    // Convert to array and sort by count descending\n    const sortedIssues = Object.values(issueCounts).sort((a, b) => b.count - a.count);\n\n    // Take top issues up to limit\n    const topIssuesLimit = options.topIssuesLimit ?? 10; // Default to top 10\n    const limitedTopIssues = sortedIssues.slice(0, topIssuesLimit);\n\n\n    const summary = {\n      scope: scopeDescription,\n      totalDiagnostics: allCandidateDiagnostics.length,\n      countsBySeverity: {\n        error: counts.error.total,\n        warning: counts.warning.total,\n        info: counts.info.total,\n        hint: counts.hint.total,\n      },\n      countsBySourceAndSeverity: counts, // Detailed breakdown\n      topIssues: limitedTopIssues.map(issue => ({ // Return structured top issues\n        message: issue.message,\n        count: issue.count,\n        severity: issue.severity,\n        source: issue.source,\n        code: issue.code,\n        exampleLocation: issue.exampleLocation,\n      })),\n      filtersApplied: options,\n    };\n\n    return summary;\n  }\n\n  /**\n     * Helper for the 'search' action. Searches for diagnostics based on a query string within messages, codes, or sources.\n     * @param options Search query and filters.\n     * @returns An array of serializable matching diagnostic objects.\n     */\n  private async searchDiagnostics(options: z.infer<typeof SearchDiagnosticsInputSchema>): Promise<any[]> {\n    if (!options.query) {\n      Logger.instance.warn('Diagnostics search query is empty. Returning empty results.');\n      return []; // Query is required by schema, but defensive check\n    }\n\n    // Get diagnostics based on filePath filter (or all open) using the list helper\n    // We apply the severity/source filters from the search options here during listing\n    const candidateDiagnostics = await this.listDiagnostics({\n      filePath: options.filePath,\n      severity: options.severity,\n      source: Array.isArray(options.source) ? options.source[0] : options.source,\n      // No overall limit yet, apply search query first\n    });\n\n    const queryLower = options.query.toLowerCase();\n\n    // Filter candidate diagnostics by the query string across message, code, or source\n    const matchingDiagnostics = candidateDiagnostics.filter(diag => {\n      // diag objects from listDiagnosticsForUris already have severity, code, source, message as strings/lowercase\n      const messageMatch = diag.message.toLowerCase().includes(queryLower);\n      const codeMatch = diag.code?.toLowerCase().includes(queryLower) ?? false; // Use ?. for potential undefined code\n      const sourceMatch = diag.source?.toLowerCase().includes(queryLower) ?? false; // Use ?. for potential undefined source\n\n      return messageMatch || codeMatch || sourceMatch;\n    });\n\n    // Apply final limit\n    const limitedMatches = options.limit ? matchingDiagnostics.slice(0, options.limit) : matchingDiagnostics;\n\n    // The diagnostics returned by listDiagnosticsForUris are already serialized/enriched\n    return limitedMatches;\n  }\n\n\n  /**\n     * Helper to get configuration settings specific to this tool.\n     * @returns An object with tool-specific settings.\n     */\n  private getLintToolSettings(): any {\n    // Retrieve configuration values specific to this tool\n    // Assuming config keys follow a pattern like 'tools.lintDiagnostics.*'\n    return {\n      // Example setting: maxOutputBufferBytes is from the terminal tool config,\n      // illustrating that settings might be relevant across tools.\n      // Replace with actual settings for this tool if they exist.\n      // E.g., including/excluding diagnostic sources, auto-fix preferences etc.\n      // Example:\n      // includeDiagnosticSources: getConfig<string[] | undefined>('tools.lintDiagnostics.includeSources', undefined),\n      // excludeDiagnosticSources: getConfig<string[] | undefined>('tools.lintDiagnostics.excludeSources', undefined),\n      // Example: settings related to task running or summarization defaults\n      // defaultLintTaskName: getConfig<string | undefined>('tools.lintDiagnostics.defaultTaskName', undefined),\n      // defaultSummaryTopIssuesLimit: getConfig<number | undefined>('tools.lintDiagnostics.defaultSummaryTopIssuesLimit', undefined),\n      // For now, return relevant terminal tool settings as a placeholder if this tool uses them\n      terminalCommandConfig: {\n        maxOutputBufferBytes: getConfig<number>('tools.terminalCommand.maxOutputBufferBytes', 10 * 1024 * 1024),\n        defaultTimeoutMs: getConfig<number>('tools.terminalCommand.defaultTimeoutMs', 60000),\n        defaultShell: getConfig<string>('tools.terminalCommand.defaultShell', getDefaultShell()),\n        allowedCommands: getConfig<string[] | undefined>('tools.terminalCommand.allowedCommands', undefined),\n        forbiddenCommands: getConfig<string[] | undefined>('tools.terminalCommand.forbiddenCommands', undefined),\n        useExecForSimpleCommands: getConfig<boolean>('tools.terminalCommand.useExecForSimpleCommands', false),\n      }\n    };\n  }\n\n  /**\n     * Helper to update configuration settings specific to this tool.\n     * @param settings A partial object with settings to update, conforming to UpdateLintToolSettingsInputSchema.\n     * @returns A promise indicating success.\n     */\n  private async updateLintToolSettings(settings: z.infer<typeof UpdateLintToolSettingsInputSchema>): Promise<void> {\n    // Use setConfig utility to update settings. This utility should handle persistence.\n    // Map the input object keys to actual config keys.\n    // Example:\n    // if (settings.includeDiagnosticSources !== undefined) {\n    //     await setConfig('tools.lintDiagnostics.includeSources', settings.includeDiagnosticSources);\n    // }\n    // if (settings.excludeDiagnosticSources !== undefined) {\n    //     await setConfig('tools.lintDiagnostics.excludeSources', settings.excludeDiagnosticSources);\n    // }\n    // Example: Update settings from terminalCommandTool config if this tool manages them\n    if (settings.maxOutputBufferBytes !== undefined) await setConfig('tools.terminalCommand.maxOutputBufferBytes', settings.maxOutputBufferBytes);\n    if (settings.defaultTimeoutMs !== undefined) await setConfig('tools.terminalCommand.defaultTimeoutMs', settings.defaultTimeoutMs);\n    if (settings.defaultShell !== undefined) await setConfig('tools.terminalCommand.defaultShell', settings.defaultShell);\n    if (settings.allowedCommands !== undefined) await setConfig('tools.terminalCommand.allowedCommands', settings.allowedCommands); // setConfig should handle null/undefined to clear\n    if (settings.forbiddenCommands !== undefined) await setConfig('tools.terminalCommand.forbiddenCommands', settings.forbiddenCommands); // setConfig should handle null/undefined to clear\n    if (settings.useExecForSimpleCommands !== undefined) await setConfig('tools.terminalCommand.useExecForSimpleCommands', settings.useExecForSimpleCommands);\n\n\n    Logger.instance.info('Lint/diagnostics settings update attempted.', settings);\n    // The setConfig utility should handle firing events or re-initializing components if needed.\n  }\n}\n\n// Export a factory function to create the tool instance\n// If the tool needs dependencies (like MemoryManager for advanced search/summary),\n// those should be passed to the factory.\nexport function createLintDiagnosticsTool(context?: AgentContext): LintDiagnosticsTool {\n  // Pass dependencies from context if needed by the tool constructor\n  return new LintDiagnosticsTool(context);\n}\n\n// Export a singleton instance created via the factory\nexport const lintDiagnosticsTool = createLintDiagnosticsTool();"]}