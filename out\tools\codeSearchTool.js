"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.codeSearchTool = exports.CodeSearchTool = void 0;
const vscode = __importStar(require("vscode"));
const cp = __importStar(require("child_process"));
const advancedCodeSearchTool_1 = require("./advancedCodeSearchTool");
const zod_1 = require("zod");
class CodeSearchTool {
    id = 'codeSearch';
    name = 'Code Search (ripgrep + advanced)';
    description = 'Searches codebase using ripgrep (rg), fuzzy search, semantic search, and provides result previews.';
    type = 'multi-action';
    schema = zod_1.z.object({
        action: zod_1.z.string().describe('The search action to perform'),
        query: zod_1.z.string().describe('The search query'),
        dirPath: zod_1.z.string().optional().describe('Directory path to search in'),
        topK: zod_1.z.number().optional().describe('Number of results for semantic search'),
        filePath: zod_1.z.string().optional().describe('File path for search preview'),
        line: zod_1.z.number().optional().describe('Line number for search preview'),
        context: zod_1.z.number().optional().describe('Context lines for search preview')
    });
    actions = {
        'basic': {
            id: 'basic',
            name: 'Basic Code Search',
            description: 'Searches codebase using ripgrep (rg). Returns matching lines.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                query: zod_1.z.string().describe('The search query for ripgrep'),
                dirPath: zod_1.z.string().optional().describe('Directory path to search in (default: current directory)')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    query: { type: 'string', description: 'The search query for ripgrep' },
                    dirPath: { type: 'string', description: 'Directory path to search in (default: current directory)', default: '.' }
                },
                required: ['query']
            },
            async execute(actionName, input, _context) {
                const query = input.query;
                const dirPath = input.dirPath || '.';
                if (!query) {
                    return { success: false, error: '\'query\' is required.', toolId: 'basic', actionName };
                }
                let cwd = dirPath;
                if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
                    const workspaceRoot = vscode.workspace.workspaceFolders[0].uri.fsPath;
                    cwd = dirPath.startsWith('/') || dirPath.match(/^.:\\/) ? dirPath : `${workspaceRoot}/${dirPath}`;
                }
                try {
                    const result = await new Promise((resolve, reject) => {
                        cp.exec(`rg --no-heading --line-number --color never "${query}"`, { cwd }, (err, stdout, stderr) => {
                            if (err && !stdout)
                                return reject(stderr || err.message);
                            resolve(stdout);
                        });
                    });
                    return { success: true, output: result.trim(), toolId: 'basic', actionName };
                }
                catch (error) {
                    return { success: false, error: `Code search failed: ${error.message || error}`, toolId: 'basic', actionName };
                }
            }
        },
        'fuzzySearch': {
            ...new advancedCodeSearchTool_1.FuzzySearchTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, _context) {
                try {
                    const query = input.query;
                    if (!query) {
                        return { success: false, error: '\'query\' is required.', toolId: 'fuzzySearch', actionName };
                    }
                    const dirPath = input.dirPath || '.';
                    let cwd = dirPath;
                    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
                        const workspaceRoot = vscode.workspace.workspaceFolders[0].uri.fsPath;
                        cwd = dirPath.startsWith('/') || dirPath.match(/^.:\\/) ? dirPath : `${workspaceRoot}/${dirPath}`;
                    }
                    else {
                        return {
                            success: false,
                            error: 'No workspace folder open. Fuzzy search requires an open workspace.',
                            toolId: 'fuzzySearch',
                            actionName
                        };
                    }
                    // Use ripgrep with fuzzy search options
                    const result = await new Promise((resolve, reject) => {
                        // Use smart-case and fuzzy matching options
                        cp.exec(`rg --no-heading --line-number --color never --smart-case "${query}"`, { cwd }, (err, stdout, stderr) => {
                            if (err && !stdout)
                                return reject(stderr || err.message);
                            resolve(stdout || '');
                        });
                    });
                    if (!result.trim()) {
                        return {
                            success: true,
                            output: `No fuzzy search results found for query: "${query}"`,
                            toolId: 'fuzzySearch',
                            actionName
                        };
                    }
                    return {
                        success: true,
                        output: result.trim(),
                        toolId: 'fuzzySearch',
                        actionName
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        error: `Fuzzy search failed: ${error.message || error}`,
                        toolId: 'fuzzySearch',
                        actionName
                    };
                }
            }
        },
        'semanticSearch': {
            ...new advancedCodeSearchTool_1.SemanticSearchTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                try {
                    const query = input.query;
                    if (!query) {
                        return { success: false, error: '\'query\' is required.', toolId: 'semanticSearch', actionName };
                    }
                    const topK = input.topK || 10;
                    const semanticContext = context;
                    try {
                        // Import polyfill classes if needed
                        let vectorStore;
                        try {
                            const { MemoryVectorStore, Embeddings } = require('../workflows/corePolyfill');
                            vectorStore = semanticContext?.vectorStore || new MemoryVectorStore();
                            // Create embeddings only if needed for vectorStore initialization
                            if (!semanticContext?.vectorStore && !semanticContext?.embeddings) {
                                const embeddings = new Embeddings();
                                // Store in context for future use
                                if (semanticContext) {
                                    semanticContext.embeddings = embeddings;
                                }
                            }
                        }
                        catch (_err) {
                            // If polyfill not available, try to use langchain directly
                            try {
                                const { MemoryVectorStore } = require('langchain/vectorstores/memory');
                                const { OpenAIEmbeddings } = require('langchain/embeddings/openai');
                                vectorStore = semanticContext?.vectorStore || new MemoryVectorStore();
                                // Create embeddings and store in context if needed
                                if (!semanticContext?.embeddings) {
                                    const langchainEmbeddings = new OpenAIEmbeddings();
                                    if (semanticContext) {
                                        semanticContext.embeddings = langchainEmbeddings;
                                    }
                                }
                            }
                            catch (err2) {
                                return {
                                    success: false,
                                    error: 'Semantic search requires either codessa polyfill or langchain libraries.',
                                    toolId: 'semanticSearch',
                                    actionName
                                };
                            }
                        }
                        // If vectorStore is empty, we need to index the codebase first
                        if (!vectorStore.memoryVectors || vectorStore.memoryVectors.length === 0) {
                            return {
                                success: false,
                                error: 'Vector store is empty. Please index the codebase first.',
                                toolId: 'semanticSearch',
                                actionName
                            };
                        }
                        // Perform similarity search
                        const docs = await vectorStore.similaritySearch(query, topK);
                        if (!docs || docs.length === 0) {
                            return {
                                success: true,
                                output: 'No similar code snippets found.',
                                toolId: 'semanticSearch',
                                actionName
                            };
                        }
                        // Format the results
                        const formattedResults = docs.map((doc, index) => {
                            return `Result ${index + 1}:\n${doc.pageContent || doc.content}\n${doc.metadata ? `File: ${doc.metadata.source || 'unknown'}` : ''}\n---`;
                        }).join('\n');
                        return {
                            success: true,
                            output: formattedResults,
                            toolId: 'semanticSearch',
                            actionName
                        };
                    }
                    catch (err) {
                        return {
                            success: false,
                            error: `Semantic search failed: ${err.message || err}`,
                            toolId: 'semanticSearch',
                            actionName
                        };
                    }
                }
                catch (error) {
                    return {
                        success: false,
                        error: `Semantic search failed: ${error.message || error}`,
                        toolId: 'semanticSearch',
                        actionName
                    };
                }
            }
        },
        'searchPreview': {
            ...new advancedCodeSearchTool_1.SearchPreviewTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, _context) {
                try {
                    const filePath = input.filePath;
                    // Use line instead of lineNumber to match the schema
                    const line = input.line;
                    const contextLines = input.context || 3;
                    if (!filePath) {
                        return {
                            success: false,
                            error: '\'filePath\' is required.',
                            toolId: 'searchPreview',
                            actionName
                        };
                    }
                    if (line === undefined || line === null) {
                        return {
                            success: false,
                            error: '\'line\' is required.',
                            toolId: 'searchPreview',
                            actionName
                        };
                    }
                    try {
                        // Open the document
                        const doc = await vscode.workspace.openTextDocument(filePath);
                        // Calculate the range of lines to show
                        const start = Math.max(0, line - contextLines);
                        const end = Math.min(doc.lineCount - 1, line + contextLines);
                        // Get the lines
                        const lines = [];
                        for (let i = start; i <= end; i++) {
                            // Add line number prefix and highlight the target line
                            const lineText = doc.lineAt(i).text;
                            const linePrefix = i === line ? '> ' : '  ';
                            lines.push(`${linePrefix}${i + 1}: ${lineText}`);
                        }
                        // Format the output
                        const output = [
                            `File: ${filePath}`,
                            `Line: ${line + 1}`,
                            '',
                            ...lines
                        ].join('\n');
                        return {
                            success: true,
                            output,
                            toolId: 'searchPreview',
                            actionName,
                            metadata: {
                                filePath,
                                line,
                                contextLines,
                                totalLines: end - start + 1
                            }
                        };
                    }
                    catch (error) {
                        return {
                            success: false,
                            error: `Failed to open file: ${error.message}`,
                            toolId: 'searchPreview',
                            actionName
                        };
                    }
                }
                catch (error) {
                    return {
                        success: false,
                        error: `Search preview failed: ${error.message || error}`,
                        toolId: 'searchPreview',
                        actionName
                    };
                }
            }
        },
    };
    async execute(input, context) {
        // For backward compatibility, check if actionName is undefined and try to get it from input
        const actionId = input.action || 'basic';
        if (!actionId) {
            return { success: false, error: `Action parameter is required. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id };
        }
        const actionTool = this.actions[actionId];
        if (!actionTool) {
            return { success: false, error: `Unknown code search action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id };
        }
        const actionInput = { ...input };
        if ('action' in actionInput) {
            delete actionInput.action;
        }
        // Check if the action tool has the new execute method signature
        if (actionTool.execute.length >= 2) {
            // Pass the actionId as the actionName parameter to the nested tool
            return actionTool.execute(actionId, actionInput, context);
        }
        else {
            // Fallback for older tools that don't have the new signature
            const result = await actionTool.execute(actionInput, context);
            // Add the actionName to the result if it's not already there
            if (result && !result.actionName) {
                result.actionName = actionId;
            }
            return result;
        }
    }
}
exports.CodeSearchTool = CodeSearchTool;
exports.codeSearchTool = new CodeSearchTool();
//# sourceMappingURL=codeSearchTool.js.map