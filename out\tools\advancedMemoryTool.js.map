{"version": 3, "file": "advancedMemoryTool.js", "sourceRoot": "", "sources": ["../../src/tools/advancedMemoryTool.ts"], "names": [], "mappings": ";;;AAGA,6BAAwB;AAExB,MAAa,gBAAgB;IAClB,EAAE,GAAG,cAAc,CAAC;IACpB,IAAI,GAAG,eAAe,CAAC;IACvB,WAAW,GAAG,oCAAoC,CAAC;IACnD,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;KACxD,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,2BAA2B,EAAE;SACpE;QACD,QAAQ,EAAE,CAAC,OAAO,CAAC;KACpB,CAAC;IACM,aAAa,CAAgB;IACrC,YAAY,aAA4B,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC;IACjF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;QACpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1F,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACjC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAC5D,CAAC;QACF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IACzE,CAAC;CACF;AA9BD,4CA8BC;AAED,MAAa,gBAAgB;IAClB,EAAE,GAAG,cAAc,CAAC;IACpB,IAAI,GAAG,eAAe,CAAC;IACvB,WAAW,GAAG,8BAA8B,CAAC;IAC7C,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC;QAC/C,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;KAC7C,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAE;YAC3D,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,cAAc,EAAE;SACzD;QACD,QAAQ,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;KAC5B,CAAC;IACM,aAAa,CAAgB;IACrC,YAAY,aAA4B,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC;IACjF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,EAAE,GAAG,KAAK,CAAC,EAAY,CAAC;QAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QAExC,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sCAAsC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACxG,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC3F,CAAC;QAED,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAExB,IAAK,IAAI,CAAC,aAAqB,CAAC,cAAc,IAAK,IAAI,CAAC,aAAqB,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;YAC1G,MAAM,EAAE,GAAG,MAAO,IAAI,CAAC,aAAqB,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACpF,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;gBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,0BAA0B;YAClC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,UAAU;SACX,CAAC;IACJ,CAAC;CACF;AAtDD,4CAsDC;AAED,MAAa,aAAa;IACf,EAAE,GAAG,WAAW,CAAC;IACjB,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,yCAAyC,CAAC;IACxD,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;QACrC,IAAI,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,qBAAqB,CAAC;QACzD,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,qBAAqB,CAAC;KAChE,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE;YACjD,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,WAAW,EAAE,qBAAqB,EAAE;YACtF,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE;SACtF;QACD,QAAQ,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;KACjC,CAAC;IACM,aAAa,CAAgB;IACrC,YAAY,aAA4B,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC;IACjF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,EAAE,GAAG,KAAK,CAAC,EAAY,CAAC;QAC9B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAgB,CAAC;QACpC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;QAElC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC1B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,8CAA8C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAChH,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC3F,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACrF,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0CAA0C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC5G,CAAC;QAED,IAAK,IAAI,CAAC,aAAqB,CAAC,cAAc,IAAK,IAAI,CAAC,aAAqB,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;YAC1G,MAAM,EAAE,GAAG,MAAO,IAAI,CAAC,aAAqB,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACpF,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW;gBAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,QAAQ,IAAI,qBAAqB;YACzC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,UAAU;SACX,CAAC;IACJ,CAAC;CACF;AAvED,sCAuEC;AAED,MAAa,uBAAuB;IACzB,EAAE,GAAG,WAAW,CAAC;IACjB,IAAI,GAAG,sBAAsB,CAAC;IAC9B,WAAW,GAAG,qCAAqC,CAAC;IACpD,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAClC,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE;KACb,CAAC;IACM,aAAa,CAAgB;IACrC,YAAY,aAA4B,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC;IACjF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,MAAiB,EAAE,QAAuB;QACtF,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YAEvD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,QAAQ,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE;oBAC1F,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,2BAA2B;YAC3B,MAAM,QAAQ,GAA0B,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAU,EAAE,CAAC;YAE3B,gEAAgE;YAChE,MAAM,WAAW,GAA8D,EAAE,CAAC;YAElF,+BAA+B;YAC/B,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3D,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;wBACtC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BACnB,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;wBACrB,CAAC;wBACD,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BACjB,EAAE,EAAE,KAAK,CAAC,EAAE;4BACZ,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;4BACpF,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI;4BACzB,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,SAAS,IAAI,SAAS;yBAC/C,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,IAAI,CAAC;wBACZ,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;wBACpF,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,SAAS,IAAI,SAAS;qBAC/C,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,iDAAiD;YACjD,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;YAEzC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;gBAExE,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE,CAAC;oBACjC,IAAI,KAAK,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE;wBAAE,SAAS;oBACzC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;wBAAE,SAAS;oBAElF,wDAAwD;oBACxD,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC3D,IAAI,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;wBAAE,SAAS;oBAC1C,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAE5B,qDAAqD;oBACrD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC/C,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACpD,8EAA8E;oBAC9E,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC7C,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBAEpE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC1B,WAAW,CAAC,IAAI,CAAC;4BACf,MAAM,EAAE,KAAK,CAAC,EAAE;4BAChB,MAAM,EAAE,UAAU,CAAC,EAAE;4BACrB,QAAQ,EAAE,UAAU,CAAC,MAAM;yBAC5B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,uDAAuD;YACvD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,QAAQ,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;YAClC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,QAAQ;oBACR,WAAW;oBACX,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM;oBAC1C,eAAe,EAAE,WAAW,CAAC,MAAM;iBACpC;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,gCAAgC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC/D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAnHD,0DAmHC", "sourcesContent": ["import { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../types/agent';\nimport { MemoryManager } from '../memory/memoryManager';\nimport { z } from 'zod';\n\nexport class MemorySearchTool implements ITool {\n  readonly id = 'memorySearch';\n  readonly name = 'Memory Search';\n  readonly description = 'Search memories by keyword or tag.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    query: z.string().describe('Keyword or tag to search.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      query: { type: 'string', description: 'Keyword or tag to search.' }\n    },\n    required: ['query']\n  };\n  private memoryManager: MemoryManager;\n  constructor(memoryManager: MemoryManager) { this.memoryManager = memoryManager; }\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const query = input.query as string;\n    if (!query) {\n      return { success: false, error: '\\'query\\' is required.', toolId: this.id, actionName };\n    }\n    const entries = await this.memoryManager.getMemories();\n    const results = entries.filter(e =>\n      (e.content && e.content.includes(query)) ||\n            (e.metadata?.tags && e.metadata.tags.includes(query))\n    );\n    return { success: true, output: results, toolId: this.id, actionName };\n  }\n}\n\nexport class MemoryUpdateTool implements ITool {\n  readonly id = 'memoryUpdate';\n  readonly name = 'Memory Update';\n  readonly description = 'Update a memory entry by ID.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    id: z.string().describe('Memory ID to update.'),\n    content: z.string().describe('New content.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      id: { type: 'string', description: 'Memory ID to update.' },\n      content: { type: 'string', description: 'New content.' }\n    },\n    required: ['id', 'content']\n  };\n  private memoryManager: MemoryManager;\n  constructor(memoryManager: MemoryManager) { this.memoryManager = memoryManager; }\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const id = input.id as string;\n    const content = input.content as string;\n\n    if (!id || !content) {\n      return { success: false, error: '\\'id\\' and \\'content\\' are required.', toolId: this.id, actionName };\n    }\n\n    const entries = await this.memoryManager.getMemories();\n    const entry = entries.find(e => e.id === id);\n\n    if (!entry) {\n      return { success: false, output: 'Memory entry not found', toolId: this.id, actionName };\n    }\n\n    entry.content = content;\n\n    if ((this.memoryManager as any).memoryProvider && (this.memoryManager as any).memoryProvider.updateMemory) {\n      const ok = await (this.memoryManager as any).memoryProvider.updateMemory(id, entry);\n      return {\n        success: ok,\n        output: ok ? 'Updated' : 'Not found',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    return {\n      success: true,\n      output: 'Updated (in-memory only)',\n      toolId: this.id,\n      actionName\n    };\n  }\n}\n\nexport class MemoryTagTool implements ITool {\n  readonly id = 'memoryTag';\n  readonly name = 'Memory Tag';\n  readonly description = 'Add or remove tags from a memory entry.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    id: z.string().describe('Memory ID.'),\n    tags: z.array(z.string()).describe('Tags to add/remove.'),\n    mode: z.enum(['add', 'remove']).describe('Add or remove tags.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      id: { type: 'string', description: 'Memory ID.' },\n      tags: { type: 'array', items: { type: 'string' }, description: 'Tags to add/remove.' },\n      mode: { type: 'string', enum: ['add', 'remove'], description: 'Add or remove tags.' }\n    },\n    required: ['id', 'tags', 'mode']\n  };\n  private memoryManager: MemoryManager;\n  constructor(memoryManager: MemoryManager) { this.memoryManager = memoryManager; }\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const id = input.id as string;\n    const tags = input.tags as string[];\n    const mode = input.mode as string;\n\n    if (!id || !tags || !mode) {\n      return { success: false, error: '\\'id\\', \\'tags\\', and \\'mode\\' are required.', toolId: this.id, actionName };\n    }\n\n    const entries = await this.memoryManager.getMemories();\n    const entry = entries.find(e => e.id === id);\n\n    if (!entry) {\n      return { success: false, output: 'Memory entry not found', toolId: this.id, actionName };\n    }\n\n    if (!entry.metadata.tags) {\n      entry.metadata.tags = [];\n    }\n\n    if (mode === 'add') {\n      for (const tag of tags) {\n        if (!entry.metadata.tags.includes(tag)) {\n          entry.metadata.tags.push(tag);\n        }\n      }\n    } else if (mode === 'remove') {\n      entry.metadata.tags = entry.metadata.tags.filter((t: string) => !tags.includes(t));\n    } else {\n      return { success: false, error: 'Invalid mode. Must be \"add\" or \"remove\".', toolId: this.id, actionName };\n    }\n\n    if ((this.memoryManager as any).memoryProvider && (this.memoryManager as any).memoryProvider.updateMemory) {\n      const ok = await (this.memoryManager as any).memoryProvider.updateMemory(id, entry);\n      return {\n        success: ok,\n        output: ok ? `Tags ${mode}ed` : 'Not found',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    return {\n      success: true,\n      output: `Tags ${mode}ed (in-memory only)`,\n      toolId: this.id,\n      actionName\n    };\n  }\n}\n\nexport class MemoryVisualizationTool implements ITool {\n  readonly id = 'memoryViz';\n  readonly name = 'Memory Visualization';\n  readonly description = 'Visualize memory graph or clusters.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({});\n  readonly inputSchema = {\n    type: 'object',\n    properties: {},\n    required: []\n  };\n  private memoryManager: MemoryManager;\n  constructor(memoryManager: MemoryManager) { this.memoryManager = memoryManager; }\n  async execute(actionName: string | undefined, _input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      const entries = await this.memoryManager.getMemories();\n\n      if (!entries || entries.length === 0) {\n        return {\n          success: true,\n          output: { message: 'No memory entries found to visualize', clusters: {}, connections: [] },\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Cluster memories by tags\n      const clusters: Record<string, any[]> = {};\n      const untagged: any[] = [];\n\n      // Track connections between memories (memories that share tags)\n      const connections: Array<{source: string, target: string, strength: number}> = [];\n\n      // First pass: organize by tags\n      for (const entry of entries) {\n        if (entry.metadata?.tags && entry.metadata.tags.length > 0) {\n          for (const tag of entry.metadata.tags) {\n            if (!clusters[tag]) {\n              clusters[tag] = [];\n            }\n            clusters[tag].push({\n              id: entry.id,\n              content: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),\n              tags: entry.metadata.tags,\n              created: entry.metadata.timestamp || 'unknown'\n            });\n          }\n        } else {\n          untagged.push({\n            id: entry.id,\n            content: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),\n            created: entry.metadata.timestamp || 'unknown'\n          });\n        }\n      }\n\n      // Second pass: find connections between memories\n      const processedPairs = new Set<string>();\n\n      for (const entry of entries) {\n        if (!entry.metadata?.tags || entry.metadata.tags.length === 0) continue;\n\n        for (const otherEntry of entries) {\n          if (entry.id === otherEntry.id) continue;\n          if (!otherEntry.metadata?.tags || otherEntry.metadata.tags.length === 0) continue;\n\n          // Create a unique key for this pair to avoid duplicates\n          const pairKey = [entry.id, otherEntry.id].sort().join('-');\n          if (processedPairs.has(pairKey)) continue;\n          processedPairs.add(pairKey);\n\n          // Calculate connection strength based on shared tags\n          const entryTags = new Set(entry.metadata.tags);\n          const otherTags = new Set(otherEntry.metadata.tags);\n          // Convert Set to Array before using filter to avoid downlevelIteration issues\n          const entryTagsArray = Array.from(entryTags);\n          const sharedTags = entryTagsArray.filter(tag => otherTags.has(tag));\n\n          if (sharedTags.length > 0) {\n            connections.push({\n              source: entry.id,\n              target: otherEntry.id,\n              strength: sharedTags.length\n            });\n          }\n        }\n      }\n\n      // Add untagged memories to the result if there are any\n      if (untagged.length > 0) {\n        clusters['untagged'] = untagged;\n      }\n\n      return {\n        success: true,\n        output: {\n          totalMemories: entries.length,\n          clusters,\n          connections,\n          clusterCount: Object.keys(clusters).length,\n          connectionCount: connections.length\n        },\n        toolId: this.id,\n        actionName\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Memory visualization failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n}\n"]}