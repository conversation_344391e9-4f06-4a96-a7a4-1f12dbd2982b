{"version": 3, "file": "toolsView.js", "sourceRoot": "", "sources": ["../../../src/ui/tools/toolsView.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgIA,sDAcC;AA9ID,+CAAiC;AACjC,2DAAwD;AACxD,yCAAsC;AACtC,2CAA6B;AAC7B,4DAAyD;AAEzD;;GAEG;AACH,MAAM,YAAa,SAAQ,2BAAY;IAEf;IACA;IACA;IACA;IAJtB,YACsB,MAAc,EACd,IAAY,EACZ,eAAuB,EACvB,cAAc,KAAK;QAEvC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QALxF,WAAM,GAAN,MAAM,CAAQ;QACd,SAAI,GAAJ,IAAI,CAAQ;QACZ,oBAAe,GAAf,eAAe,CAAQ;QACvB,gBAAW,GAAX,WAAW,CAAQ;QAIvC,IAAI,CAAC,IAAI,CAAC;YACN,EAAE,EAAE,MAAM;YACV,OAAO,EAAE,eAAe;YACxB,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,MAAM;SACvB,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,GAAG;YACZ,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YACrE,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SACtE,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;AACH,MAAa,qBAAqB;IACxB,oBAAoB,GAAgD,IAAI,MAAM,CAAC,YAAY,EAA0B,CAAC;IACrH,mBAAmB,GAAyC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAErG;;SAEK;IACL,OAAO;QACL,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;SAEK;IACL,WAAW,CAAC,OAAwB;QAClC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,8BAA8B;YAC9B,OAAO,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC/B,CAAC;QACD,yEAAyE;QACzE,MAAM,EAAE,GAAI,OAAwB,CAAC,EAAE,CAAC;QACxC,IAAI,CAAC,EAAE;YAAE,OAAO,EAAE,CAAC;QACnB,MAAM,IAAI,GAAG,MAAM,2BAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACrD,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,EAAE;gBAC3D,MAAM,IAAI,GAAI,OAAe,CAAC,IAAI,IAAI,KAAK,CAAC;gBAC5C,MAAM,WAAW,GAAI,OAAe,CAAC,WAAW,IAAI,EAAE,CAAC;gBACvD,MAAM,UAAU,GAAG,CAAC,CAAE,OAAe,CAAC,OAAO,CAAC;gBAC9C,OAAO,IAAI,YAAY,CACrB,GAAG,IAAI,CAAC,EAAE,IAAI,KAAK,EAAE,EACrB,IAAI,EACJ,WAAW,EACX,UAAU,CACX,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,QAAQ;QACpB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,2BAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YACxD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,QAAQ,CACrC,oBAAoB,EACpB,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACrC,CAAC;gBACF,WAAW,CAAC,OAAO,GAAG,sDAAsD,CAAC;gBAC7E,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC;gBACrC,OAAO,CAAC,WAAW,CAAC,CAAC;YACvB,CAAC;YACD,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACtB,MAAM,IAAI,GAAI,IAAY,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;gBAC3C,MAAM,WAAW,GAAI,IAAY,CAAC,WAAW,IAAI,EAAE,CAAC;gBACpD,MAAM,UAAU,GAAG,CAAC,CAAE,IAAY,CAAC,OAAO,CAAC;gBAC3C,OAAO,IAAI,YAAY,CACrB,IAAI,CAAC,EAAE,EACP,IAAI,EACJ,WAAW,EACX,UAAU,CACX,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,QAAQ,CACnC,6BAA6B,EAC7B,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACrC,CAAC;YACF,SAAS,CAAC,OAAO,GAAG;gBAClB,OAAO,EAAE,sBAAsB;gBAC/B,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,EAAE;aACd,CAAC;YACF,SAAS,CAAC,OAAO,GAAG,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACrG,OAAO,CAAC,SAAS,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;CACF;AAvFD,sDAuFC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,OAAgC;IACpE,MAAM,gBAAgB,GAAG,IAAI,qBAAqB,EAAE,CAAC;IACrD,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE;QAChE,gBAAgB;QAChB,eAAe,EAAE,KAAK;QACtB,aAAa,EAAE,KAAK;KACrB,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,QAAQ,EACR,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAC9F,CAAC;IAEF,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ToolRegistry } from '../../tools/toolRegistry';\nimport { Logger } from '../../logger';\nimport * as path from 'path';\nimport { BaseTreeItem } from '../treeItems/baseTreeItem';\n\n/**\n * TreeItem representing a Tool in the sidebar tree view\n */\nclass ToolTreeItem extends BaseTreeItem {\n  constructor(\n        public readonly toolId: string,\n        public readonly name: string,\n        public readonly toolDescription: string,\n        public readonly collapsible = false\n  ) {\n    super(name, collapsible ? vscode.TreeItemCollapsibleState.Collapsed : vscode.TreeItemCollapsibleState.None);\n\n    this.init({\n        id: toolId,\n        tooltip: toolDescription,\n        description: '',\n        contextValue: 'tool'\n    });\n\n    const resourcesPath = path.join(__dirname, '..', '..', 'resources');\n    this.iconPath = {\n        light: vscode.Uri.file(path.join(resourcesPath, 'light', 'tool.svg')),\n        dark: vscode.Uri.file(path.join(resourcesPath, 'dark', 'tool.svg'))\n    };\n  }\n}\n\n/**\n * Tree data provider for the tools sidebar\n */\nexport class ToolsTreeDataProvider implements vscode.TreeDataProvider<vscode.TreeItem> {\n  private _onDidChangeTreeData: vscode.EventEmitter<vscode.TreeItem | null> = new vscode.EventEmitter<vscode.TreeItem | null>();\n  readonly onDidChangeTreeData: vscode.Event<vscode.TreeItem | null> = this._onDidChangeTreeData.event;\n\n  /**\n     * Refresh the entire tree\n     */\n  refresh(): void {\n    this._onDidChangeTreeData.fire(null);\n  }\n\n  /**\n     * Get tree item representation for an element\n     */\n  getTreeItem(element: vscode.TreeItem): vscode.TreeItem {\n    return element;\n  }\n\n  /**\n     * Get children of the provided element, or root elements if no element provided\n     */\n  async getChildren(element?: vscode.TreeItem): Promise<vscode.TreeItem[]> {\n    if (!element) {\n      // Root level - show all tools\n      return await this.getTools();\n    }\n    // If the element is a parent tool with sub-actions, show its sub-actions\n    const id = (element as ToolTreeItem).id;\n    if (!id) return [];\n    const tool = await ToolRegistry.instance.getTool(id);\n    if (tool && tool.actions) {\n      return Object.entries(tool.actions).map(([subId, subTool]) => {\n        const name = (subTool as any).name || subId;\n        const description = (subTool as any).description || '';\n        const hasActions = !!(subTool as any).actions;\n        return new ToolTreeItem(\n          `${tool.id}.${subId}`,\n          name,\n          description,\n          hasActions\n        );\n      });\n    }\n    return [];\n  }\n\n  /**\n     * Get all tools as tree items\n     */\n  private async getTools(): Promise<vscode.TreeItem[]> {\n    try {\n      const tools = await ToolRegistry.instance.getAllTools();\n      if (tools.length === 0) {\n        const noToolsItem = new vscode.TreeItem(\n          'No tools available',\n          vscode.TreeItemCollapsibleState.None\n        );\n        noToolsItem.tooltip = 'Tools will appear here once a provider is configured';\n        noToolsItem.contextValue = 'noTools';\n        return [noToolsItem];\n      }\n      return tools.map(tool => {\n        const name = (tool as any).name || tool.id;\n        const description = (tool as any).description || '';\n        const hasActions = !!(tool as any).actions;\n        return new ToolTreeItem(\n          tool.id,\n          name,\n          description,\n          hasActions\n        );\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting tools for tree view:', error);\n      const errorItem = new vscode.TreeItem(\n        'Click to configure settings',\n        vscode.TreeItemCollapsibleState.None\n      );\n      errorItem.command = {\n        command: 'codessa.openSettings',\n        title: 'Open Settings',\n        arguments: []\n      };\n      errorItem.tooltip = `Error loading tools: ${error instanceof Error ? error.message : String(error)}`;\n      return [errorItem];\n    }\n  }\n}\n\n/**\n * Setup and register the tools tree view\n */\nexport function registerToolsTreeView(context: vscode.ExtensionContext): vscode.TreeView<vscode.TreeItem> {\n  const treeDataProvider = new ToolsTreeDataProvider();\n  const treeView = vscode.window.createTreeView('codessaToolsView', {\n    treeDataProvider,\n    showCollapseAll: false,\n    canSelectMany: false\n  });\n\n  context.subscriptions.push(\n    treeView,\n    vscode.commands.registerCommand('codessa.refreshToolsView', () => treeDataProvider.refresh())\n  );\n\n  return treeView;\n}\n"]}