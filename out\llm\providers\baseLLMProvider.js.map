{"version": 3, "file": "baseLLMProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/baseLLMProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,yCAAsC;AACtC,wDAAqD;AACrD,6EAA0E;AAE1E;;GAEG;AACH,MAAsB,eAAe;IACzB,MAAM,GAAsB,EAAE,CAAC;IAC/B,OAAO,CAA2B;IAE5C,YAAY,OAAiC;QAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAsBC;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,MAAM,GAAG,MAAM,iCAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACjG,eAAM,CAAC,KAAK,CAAC,qCAAqC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;gBAErE,qFAAqF;gBACrF,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/C,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,uCAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;wBAC3G,IAAI,MAAM,EAAE,CAAC;4BACX,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;wBAC9B,CAAC;oBACH,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,eAAM,CAAC,KAAK,CAAC,uBAAuB,IAAI,CAAC,UAAU,SAAS,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2DAA2D;gBAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAChE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAoC,WAAW,CAAC,IAAI,EAAE,CAAC;gBACnF,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;gBAC/C,eAAM,CAAC,KAAK,CAAC,qCAAqC,IAAI,CAAC,UAAU,qBAAqB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACvH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6CAA6C,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACrF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,4DAA4D;QAC5D,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,yDAAyD;QACzD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,0EAA0E;YAC1E,6EAA6E;YAC7E,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,SAAS;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,OAAY;QACpC,IAAI,CAAC;YACH,mDAAmD;YACnD,IAAI,IAAI,CAAC,OAAO,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;gBAClF,MAAM,WAAW,GAAG,OAAO,CAAC,MAA4B,CAAC;gBACzD,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrE,IAAI,CAAC;wBACH,MAAM,uCAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;wBAClH,gDAAgD;wBAChD,OAAO,OAAO,CAAC,MAAM,CAAC;wBACtB,sBAAsB;wBACtB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;oBAC1C,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,eAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,CAAC,UAAU,sBAAsB,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAC/H,CAAC;gBACH,CAAC;qBAAM,IAAI,WAAW,KAAK,EAAE,EAAE,CAAC;oBAC9B,iCAAiC;oBACjC,IAAI,CAAC;wBACH,MAAM,uCAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;oBACjG,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,eAAM,CAAC,IAAI,CAAC,+BAA+B,IAAI,CAAC,UAAU,sBAAsB,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAChI,CAAC;oBACD,OAAO,OAAO,CAAC,MAAM,CAAC;oBACtB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC5B,CAAC;YACH,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM,OAAO,GAAG,MAAM,iCAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBAC/G,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,IAAI,KAAK,CAAC,+CAA+C,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,oDAAoD;gBACpD,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAClE,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAoC,WAAW,CAAC,IAAI,EAAE,CAAC;gBAErF,uDAAuD;gBACvD,MAAM,gBAAgB,GAAG;oBACvB,GAAG,SAAS;oBACZ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,OAAO;iBAC3B,CAAC;gBAEF,qCAAqC;gBACrC,IAAI,CAAC;oBACH,yBAAyB;oBACzB,MAAM,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;oBACxF,eAAM,CAAC,IAAI,CAAC,6CAA6C,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;gBAC9E,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,eAAM,CAAC,IAAI,CAAC,0CAA0C,WAAW,6BAA6B,CAAC,CAAC;oBAEhG,wCAAwC;oBACxC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACtF,MAAM,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;wBAC3F,eAAM,CAAC,IAAI,CAAC,gDAAgD,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;oBACjF,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;oBACzF,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;YACtB,eAAM,CAAC,IAAI,CAAC,sCAAsC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+CAA+C,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACvF,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,4CAA4C,YAAY,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;OAEG;IACI,sBAAsB;QAC3B,MAAM,MAAM,GAA2K,EAAE,CAAC;QAE1L,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,eAAe,IAAI,CAAC,WAAW,EAAE;gBAC9C,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,oBAAoB,IAAI,CAAC,WAAW,EAAE;gBACnD,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;QACL,CAAC;QAED,MAAM,CAAC,IAAI,CAAC;YACV,EAAE,EAAE,cAAc;YAClB,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,4BAA4B,IAAI,CAAC,WAAW,EAAE;YAC3D,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,QAAQ;SACf,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,kBAAkB;QAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC;CACJ;AAlND,0CAkNC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { ILLMProvider, LLMModelInfo, LLMProviderConfig } from '../llmProvider';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { logger } from '../../logger';\nimport { providerManager } from '../providerManager';\nimport { credentialsManager } from '../../credentials/credentialsManager';\n\n/**\n * Base class for LLM providers that implements common functionality\n */\nexport abstract class BaseLLMProvider implements ILLMProvider {\n  protected config: LLMProviderConfig = {};\n  protected context?: vscode.ExtensionContext;\n\n  constructor(context?: vscode.ExtensionContext) {\n    this.context = context;\n    if (context) {\n      this.loadConfig();\n    }\n  }\n\n    // Abstract properties that must be implemented by subclasses\n    abstract readonly providerId: string;\n    abstract readonly displayName: string;\n    abstract readonly description: string;\n    abstract readonly website: string;\n    abstract readonly requiresApiKey: boolean;\n    abstract readonly supportsEndpointConfiguration: boolean;\n    abstract readonly defaultEndpoint?: string;\n    abstract readonly defaultModel?: string;\n\n    // Abstract methods that must be implemented by subclasses\n    abstract generate(\n        _params: LLMGenerateParams,\n        _cancellationToken?: vscode.CancellationToken,\n        _tools?: Map<string, ITool>\n    ): Promise<LLMGenerateResult>;\n\n    abstract listModels(): Promise<LLMModelInfo[]>;\n    abstract testConnection(_modelId: string): Promise<{success: boolean, message: string}>;\n\n    /**\n     * Load the provider configuration\n     */\n    public async loadConfig(): Promise<void> {\n      try {\n        if (this.context) {\n          this.config = await providerManager.getInstance(this.context).getProviderConfig(this.providerId);\n          logger.debug(`Loaded configuration for provider ${this.providerId}`);\n\n          // Securely hydrate API key from secret storage if required and not present in config\n          if (this.requiresApiKey && !this.config.apiKey) {\n            try {\n              const secret = await credentialsManager.getInstance(this.context).getCredential(this.providerId, 'apiKey');\n              if (secret) {\n                this.config.apiKey = secret;\n              }\n            } catch (e) {\n              logger.debug(`No secret found for ${this.providerId}.apiKey`);\n            }\n          }\n        } else {\n          // If no context, try to get config from workspace settings\n          const config = vscode.workspace.getConfiguration('codessa.llm');\n          const providers = config.get<Record<string, LLMProviderConfig>>('providers') || {};\n          this.config = providers[this.providerId] || {};\n          logger.debug(`Loaded configuration for provider ${this.providerId} without context: ${JSON.stringify(this.config)}`);\n        }\n      } catch (error) {\n        logger.error(`Failed to load configuration for provider ${this.providerId}:`, error);\n        this.config = {};\n      }\n    }\n\n    /**\n     * Check if the provider is configured and ready to use\n     */\n    public isConfigured(): boolean {\n      if (!this.requiresApiKey) {\n        return true;\n      }\n\n      // If API key present in runtime config, consider configured\n      if (this.config.apiKey && this.config.apiKey.trim().length > 0) {\n        return true;\n      }\n\n      // Fallback: check secret storage if context is available\n      if (this.context) {\n        // Note: synchronous check not available; conservatively return false here\n        // Providers that call loadConfig() will have the secret hydrated into config\n        return false;\n      }\n      return false;\n    }\n\n    /**\n     * Get the current configuration\n     */\n    public getConfig(): LLMProviderConfig {\n      return { ...this.config };\n    }\n\n    /**\n     * Update the provider configuration\n     */\n    public async updateConfig(_config: any): Promise<void> {\n      try {\n        // Intercept and securely store API key if provided\n        if (this.context && _config && typeof _config === 'object' && 'apiKey' in _config) {\n          const apiKeyValue = _config.apiKey as string | undefined;\n          if (typeof apiKeyValue === 'string' && apiKeyValue.trim().length > 0) {\n            try {\n              await credentialsManager.getInstance(this.context).storeCredential(this.providerId, 'apiKey', apiKeyValue.trim());\n              // Remove plaintext apiKey from persisted config\n              delete _config.apiKey;\n              // Keep runtime access\n              this.config.apiKey = apiKeyValue.trim();\n            } catch (e) {\n              logger.warn(`Failed to store apiKey for ${this.providerId} in SecretStorage: ${e instanceof Error ? e.message : String(e)}`);\n            }\n          } else if (apiKeyValue === '') {\n            // Empty string indicates removal\n            try {\n              await credentialsManager.getInstance(this.context).deleteCredential(this.providerId, 'apiKey');\n            } catch (e) {\n              logger.warn(`Failed to delete apiKey for ${this.providerId} in SecretStorage: ${e instanceof Error ? e.message : String(e)}`);\n            }\n            delete _config.apiKey;\n            delete this.config.apiKey;\n          }\n        }\n\n        if (this.context) {\n          const success = await providerManager.getInstance(this.context).updateProviderConfig(this.providerId, _config);\n          if (!success) {\n            throw new Error(`Failed to update configuration for provider ${this.providerId}`);\n          }\n        } else {\n          // If no context, update workspace settings directly\n          const vsConfig = vscode.workspace.getConfiguration('codessa.llm');\n          const providers = vsConfig.get<Record<string, LLMProviderConfig>>('providers') || {};\n\n          // Update the specific provider in the providers object\n          const updatedProviders = {\n            ...providers,\n            [this.providerId]: _config\n          };\n\n          // Update the entire providers object\n          try {\n            // First try Global level\n            await vsConfig.update('providers', updatedProviders, vscode.ConfigurationTarget.Global);\n            logger.info(`Updated global configuration for provider ${this.providerId}`);\n          } catch (globalError) {\n            logger.warn(`Failed to update global configuration: ${globalError}. Trying workspace level...`);\n\n            // Then try Workspace level if available\n            if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n              await vsConfig.update('providers', updatedProviders, vscode.ConfigurationTarget.Workspace);\n              logger.info(`Updated workspace configuration for provider ${this.providerId}`);\n            } else {\n              throw new Error('Failed to update configuration: No valid configuration target found');\n            }\n          }\n        }\n        this.config = _config;\n        logger.info(`Updated configuration for provider ${this.providerId}`);\n      } catch (error) {\n        logger.error(`Failed to update configuration for provider ${this.providerId}:`, error);\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        throw new Error(`Failed to update provider configuration: ${errorMessage}`);\n      }\n    }\n\n    /**\n     * Get the required configuration fields for this provider\n     */\n    public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select' | 'file' | 'directory', options?: string[]}> {\n      const fields: Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select' | 'file' | 'directory', options?: string[]}> = [];\n\n      if (this.requiresApiKey) {\n        fields.push({\n          id: 'apiKey',\n          name: 'API Key',\n          description: `API key for ${this.displayName}`,\n          required: true,\n          type: 'string'\n        });\n      }\n\n      if (this.supportsEndpointConfiguration) {\n        fields.push({\n          id: 'apiEndpoint',\n          name: 'API Endpoint',\n          description: `API endpoint for ${this.displayName}`,\n          required: false,\n          type: 'string'\n        });\n      }\n\n      fields.push({\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: `Default model to use for ${this.displayName}`,\n        required: false,\n        type: 'string'\n      });\n\n      return fields;\n    }\n\n    /**\n     * Backward compatibility method for getAvailableModels\n     * @deprecated Use listModels instead\n     */\n    public async getAvailableModels(): Promise<string[]> {\n      const models = await this.listModels();\n      return models.map(model => model.id);\n    }\n}\n"]}