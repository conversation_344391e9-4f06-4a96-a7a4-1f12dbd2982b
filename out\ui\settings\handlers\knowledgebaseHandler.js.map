{"version": 3, "file": "knowledgebaseHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/knowledgebaseHandler.ts"], "names": [], "mappings": ";;AACA,gEA6DC;AA9DD,wDAAwD;AACxD,SAAgB,0BAA0B,CAAC,OAAY,EAAE,KAAU;IACjE,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;IAChG,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtJ,IAAI,CAAC;QACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,mBAAmB,CAAC,CAAC,CAAC;gBACzB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC;gBACxC,MAAM;YACR,CAAC;YACD,KAAK,kBAAkB,CAAC,CAAC,CAAC;gBACxB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC9B,QAAQ,CAAC,KAAK,GAAG,wCAAwC,CAAC;oBAC1D,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;gBACnB,MAAM;YACR,CAAC;YACD,KAAK,mBAAmB,CAAC,CAAC,CAAC;gBACzB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;oBAClB,QAAQ,CAAC,KAAK,GAAG,4BAA4B,CAAC;oBAC9C,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1E,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,0BAA0B,CAAC;oBAC5C,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;gBAC1E,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBAC7C,MAAM;YACR,CAAC;YACD,KAAK,qBAAqB,CAAC,CAAC,CAAC;gBAC3B,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,4BAA4B,CAAC;oBAC9C,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBACvE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,0BAA0B,CAAC;oBAC5C,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBACvC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC3C,CAAC;QACD,CAAC;IACH,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for Knowledgebase settings messages and logic\nexport function handleKnowledgebaseMessage(message: any, panel: any) {\n  const settings = (window as any).settings || {};\n  settings.knowledgebases = Array.isArray(settings.knowledgebases) ? settings.knowledgebases : [];\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: message.type, success: false, data: null, error: null };\n  try {\n    switch (message.type) {\n    case 'getKnowledgebases': {\n      response.success = true;\n      response.data = settings.knowledgebases;\n      break;\n    }\n    case 'addKnowledgebase': {\n      const kb = message.kb;\n      if (!kb || !kb.name || !kb.id) {\n        response.error = 'Missing required knowledgebase fields.';\n        break;\n      }\n      settings.knowledgebases.push(kb);\n      response.success = true;\n      response.data = kb;\n      break;\n    }\n    case 'editKnowledgebase': {\n      const kb = message.kb;\n      if (!kb || !kb.id) {\n        response.error = 'Knowledgebase id required.';\n        break;\n      }\n      const idx = settings.knowledgebases.findIndex((k: any) => k.id === kb.id);\n      if (idx === -1) {\n        response.error = 'Knowledgebase not found.';\n        break;\n      }\n      settings.knowledgebases[idx] = { ...settings.knowledgebases[idx], ...kb };\n      response.success = true;\n      response.data = settings.knowledgebases[idx];\n      break;\n    }\n    case 'deleteKnowledgebase': {\n      const id = message.id;\n      if (!id) {\n        response.error = 'Knowledgebase id required.';\n        break;\n      }\n      const idx = settings.knowledgebases.findIndex((k: any) => k.id === id);\n      if (idx === -1) {\n        response.error = 'Knowledgebase not found.';\n        break;\n      }\n      settings.knowledgebases.splice(idx, 1);\n      response.success = true;\n      break;\n    }\n    default: {\n      response.error = 'Unknown message type.';\n    }\n    }\n  } catch (err: any) {\n    response.error = err?.message || String(err);\n  }\n  panel.postMessage(response);\n}\n\n"]}