{"version": 3, "file": "diagnosticsTool.js", "sourceRoot": "", "sources": ["../../src/tools/diagnosticsTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,6BAAwB;AAGxB,sCAAmC;AAEnC;;GAEG;AACH,IAAY,kBAKX;AALD,WAAY,kBAAkB;IAC5B,qCAAe,CAAA;IACf,yCAAmB,CAAA;IACnB,iDAA2B,CAAA;IAC3B,mCAAa,CAAA;AACf,CAAC,EALW,kBAAkB,kCAAlB,kBAAkB,QAK7B;AAED;;GAEG;AACH,IAAY,gBASX;AATD,WAAY,gBAAgB;IAC1B,6CAAyB,CAAA;IACzB,qCAAiB,CAAA;IACjB,2CAAuB,CAAA;IACvB,yCAAqB,CAAA;IACrB,qCAAiB,CAAA;IACjB,2CAAuB,CAAA;IACvB,qCAAiB,CAAA;IACjB,mCAAe,CAAA;AACjB,CAAC,EATW,gBAAgB,gCAAhB,gBAAgB,QAS3B;AAkCD;;GAEG;AACH,MAAa,eAAe;IACjB,EAAE,GAAG,aAAa,CAAC;IACnB,IAAI,GAAG,kBAAkB,CAAC;IAC1B,WAAW,GAAG,2EAA2E,CAAC;IAC1F,IAAI,GAAG,cAAc,CAAC;IAC/B,0EAA0E;IACjE,OAAO,GAA2B;QACzC,KAAK,EAAE;YACL,WAAW,EAAE,mEAAmE;YAChF,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yGAAyG,CAAC;gBACzJ,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;gBAC3I,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;gBACtG,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+DAA+D,CAAC;aACvG,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yGAAyG,CAAC;gBACzJ,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;gBAC3I,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;gBACtG,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+DAA+D,CAAC;aACvG,CAAC;SACH;QACD,SAAS,EAAE;YACT,WAAW,EAAE,0CAA0C;YACvD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8FAA8F,CAAC;gBAC9I,OAAO,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+DAA+D,CAAC;aAC7I,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8FAA8F,CAAC;gBAC9I,OAAO,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+DAA+D,CAAC;aAC7I,CAAC;SACH;QACD,KAAK,EAAE;YACL,WAAW,EAAE,0CAA0C;YACvD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iGAAiG,CAAC;gBACjJ,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;gBAClJ,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qEAAqE,CAAC;gBAC7G,MAAM,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qFAAqF,CAAC;aAC/H,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iGAAiG,CAAC;gBACjJ,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;gBAClJ,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qEAAqE,CAAC;gBAC7G,MAAM,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qFAAqF,CAAC;aAC/H,CAAC;SACH;QACD,SAAS,EAAE;YACT,WAAW,EAAE,mDAAmD;YAChE,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uFAAuF,CAAC;aACnI,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uFAAuF,CAAC;aACnI,CAAC;SACH;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,UAAU,IAAI,WAAW,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACjH,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAC5C,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAChD,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAC5C,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAChD;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,UAAU,EAAE;wBACtC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAClF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,4BAA4B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC3D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,UAAU,CAAC,KAAgB,EAAE,UAAkB;QAC3D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAA6B,CAAC;YAClD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkD,CAAC;YAC1E,MAAM,MAAM,GAAG,KAAK,CAAC,MAA4B,CAAC;YAClD,MAAM,KAAK,GAAG,KAAK,CAAC,KAA2B,CAAC;YAEhD,kBAAkB;YAClB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAErD,gBAAgB;YAChB,IAAI,mBAAmB,GAAG,WAAW,CAAC;YAEtC,IAAI,QAAQ,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;gBACnC,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;YACjF,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;YAC7E,CAAC;YAED,cAAc;YACd,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACrC,mBAAmB,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,WAAW,EAAE,mBAAmB;oBAChC,KAAK,EAAE,mBAAmB,CAAC,MAAM;oBACjC,UAAU,EAAE,WAAW,CAAC,MAAM;iBAC/B;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,KAAK;oBACL,QAAQ;oBACR,MAAM;oBACN,KAAK;iBACN;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC7D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,cAAc,CAAC,KAAgB,EAAE,UAAkB;QAC/D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAA6B,CAAC;YAClD,MAAM,OAAO,GAAI,KAAK,CAAC,OAAkB,IAAI,MAAM,CAAC;YAEpD,kBAAkB;YAClB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAErD,oBAAoB;YACpB,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAEvE,oBAAoB;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;YAEjF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,kBAAkB;oBAClB,QAAQ;oBACR,UAAU,EAAE,WAAW,CAAC,MAAM;iBAC/B;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,KAAK;oBACL,OAAO;iBACR;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACjE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,UAAU,CAAC,KAAgB,EAAE,UAAkB;QAC3D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAA6B,CAAC;YAClD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkD,CAAC;YAC1E,MAAM,MAAM,GAAG,KAAK,CAAC,MAA4B,CAAC;YAClD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC;YAErC,kBAAkB;YAClB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAErD,gBAAgB;YAChB,IAAI,mBAAmB,GAAG,WAAW,CAAC;YAEtC,IAAI,QAAQ,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;gBACnC,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;YACjF,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;YAC7E,CAAC;YAED,6BAA6B;YAC7B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAE1E,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,UAAU;oBACV,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM;oBAClD,UAAU,EAAE,mBAAmB,CAAC,MAAM;oBACtC,MAAM;iBACP;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,KAAK;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;iBACP;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC7D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,cAAc,CAAC,KAAgB,EAAE,UAAkB;QAC/D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC;YAEzC,sBAAsB;YACtB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAEhD,mBAAmB;YACnB,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAE5D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,UAAU,EAAE,WAAW,CAAC,MAAM;iBAC/B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YACtE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2CAA2C,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC1E,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,cAAc,CAAC,KAAgB;QAC3C,mCAAmC;QACnC,MAAM,cAAc,GAAqB,EAAE,CAAC;QAE5C,uEAAuE;QACvE,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,MAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;oBAChD,IAAI,CAAC,GAAG,EAAE,CAAC;wBACT,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;wBACjE,SAAS;oBACX,CAAC;oBAED,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;oBACzD,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACzE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7E,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,gCAAgC;YAChC,KAAK,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC;gBACnE,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;gBAC5B,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;SAEK;IACG,kBAAkB,CAAC,WAAgC,EAAE,QAAgB;QAC3E,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YACzB,4CAA4C;YAC5C,IAAI,QAA4B,CAAC;YACjC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACnB,KAAK,MAAM,CAAC,kBAAkB,CAAC,KAAK;oBAClC,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;oBACpC,MAAM;gBACR,KAAK,MAAM,CAAC,kBAAkB,CAAC,OAAO;oBACpC,QAAQ,GAAG,kBAAkB,CAAC,OAAO,CAAC;oBACtC,MAAM;gBACR,KAAK,MAAM,CAAC,kBAAkB,CAAC,WAAW;oBACxC,QAAQ,GAAG,kBAAkB,CAAC,WAAW,CAAC;oBAC1C,MAAM;gBACR,KAAK,MAAM,CAAC,kBAAkB,CAAC,IAAI;oBACjC,QAAQ,GAAG,kBAAkB,CAAC,IAAI,CAAC;oBACnC,MAAM;gBACR;oBACE,QAAQ,GAAG,kBAAkB,CAAC,WAAW,CAAC;YAC9C,CAAC;YAED,iCAAiC;YACjC,IAAI,MAA0B,CAAC;YAC/B,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;gBACb,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAChC,iCAAiC;gBACjC,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3D,MAAM,GAAG,gBAAgB,CAAC,UAAU,CAAC;gBACvC,CAAC;qBAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;gBACnC,CAAC;qBAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBACxC,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC;gBACtC,CAAC;qBAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;oBACvC,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC;gBACrC,CAAC;qBAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;gBACnC,CAAC;qBAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBACxC,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC;gBACtC,CAAC;qBAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,8BAA8B;YAC9B,MAAM,kBAAkB,GAAG,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1D,OAAO,EAAE,EAAE,CAAC,OAAO;gBACnB,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM;gBAC5B,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,qBAAqB;gBAClE,cAAc,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,qBAAqB;aAC5E,CAAC,CAAC,CAAC;YAEJ,eAAe;YACf,MAAM,IAAI,GAAa,EAAE,CAAC;YAC1B,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;gBACX,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;oBACzB,IAAI,GAAG,KAAK,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;wBAC7C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC3B,CAAC;yBAAM,IAAI,GAAG,KAAK,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;wBACnD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,CAAC,CAAC,OAAO;gBAClB,QAAQ;gBACR,MAAM;gBACN,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;gBACzC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,qBAAqB;gBACxD,cAAc,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,qBAAqB;gBAClE,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,qBAAqB;gBACpD,YAAY,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,qBAAqB;gBAC9D,kBAAkB;gBAClB,IAAI;gBACJ,QAAQ;aACT,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,gBAAgB,CAAC,WAA6B,EAAE,OAAe;QACrE,MAAM,OAAO,GAAqC,EAAE,CAAC;QAErD,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,GAAW,CAAC;YAEhB,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,MAAM;oBACT,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC;oBAC1B,MAAM;gBACR,KAAK,UAAU;oBACb,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC;oBAC1B,MAAM;gBACR,KAAK,QAAQ;oBACX,GAAG,GAAG,UAAU,CAAC,MAAM,IAAI,SAAS,CAAC;oBACrC,MAAM;gBACR,KAAK,MAAM;oBACT,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;oBAC5D,MAAM;gBACR;oBACE,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC;YAC9B,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClB,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YACpB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;SAEK;IACG,gBAAgB,CACtB,WAA6B,EAC7B,kBAAoD,EACpD,OAAe;QAEf,MAAM,QAAQ,GAAQ;YACpB,UAAU,EAAE,WAAW,CAAC,MAAM;YAC9B,cAAc,EAAE;gBACd,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/B,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnC,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;aAC7B;YACD,YAAY,EAAE,EAAE;YAChB,gBAAgB,EAAE,EAAE;YACpB,eAAe,EAAE,EAAE;SACpB,CAAC;QAEF,oBAAoB;QACpB,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAE/C,kBAAkB;YAClB,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,SAAS,CAAC;YAC9C,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3E,CAAC;QAED,0BAA0B;QAC1B,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAyD,CAAC;QAElF,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,GAAG,UAAU,CAAC,MAAM,IAAI,SAAS,IAAI,UAAU,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC;YAEhF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvB,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;YAChD,CAAC;YAED,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;YACjC,KAAK,CAAC,KAAK,EAAE,CAAC;YAEd,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,QAAQ,CAAC,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aACvD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACpC,GAAG;YACH,KAAK;YACL,QAAQ;SACT,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEf,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1D,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACxF,CAAC;QAED,IAAI,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,gDAAgD,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACpI,CAAC;QAED,8BAA8B;QAC9B,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,MAAM;gBACT,8BAA8B;gBAC9B,QAAQ,CAAC,mBAAmB,GAAG,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC;qBAC9D,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;oBACvB,IAAI;oBACJ,KAAK,EAAE,KAAK,CAAC,MAAM;oBACnB,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM;iBAC9E,CAAC,CAAC;qBACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;qBACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEf,IAAI,QAAQ,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5C,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,+BAA+B,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC;gBAClK,CAAC;gBACD,MAAM;YAER,KAAK,UAAU;gBACb,wCAAwC;gBACxC,IAAI,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;oBAC7D,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBACzF,CAAC;gBACD,MAAM;YAER,KAAK,QAAQ;gBACX,gCAAgC;gBAChC,QAAQ,CAAC,sBAAsB,GAAG,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC;qBACjE,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;oBACzB,MAAM;oBACN,KAAK,EAAE,KAAK,CAAC,MAAM;oBACnB,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM;iBAC9E,CAAC,CAAC;qBACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;qBACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEf,IAAI,QAAQ,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/C,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,qDAAqD,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,MAAM,UAAU,CAAC,CAAC;gBAC1I,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACG,mBAAmB,CAAC,QAA4B;QACtD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,kBAAkB,CAAC,KAAK;gBAC3B,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACzC,KAAK,kBAAkB,CAAC,OAAO;gBAC7B,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC3C,KAAK,kBAAkB,CAAC,WAAW;gBACjC,OAAO,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC;YAC/C,KAAK,kBAAkB,CAAC,IAAI;gBAC1B,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACxC;gBACE,OAAO,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC;QACjD,CAAC;IACH,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,QAAgB;QAC3C,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAE/D,iCAAiC;YACjC,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACvC,IAAI,GAAG,CAAC,MAAM;oBAAE,OAAO,GAAG,CAAC,CAAC,mBAAmB;YACjD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,2CAA2C;YAC7C,CAAC;YAED,wCAAwC;YACxC,OAAO,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;YAC7C,kCAAkC;YAClC,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QAED,sCAAsC;QACtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,QAAQ,qCAAqC,CAAC,CAAC;QACrG,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,cAAc,CAAC,WAA6B,EAAE,MAAe;QACzE,MAAM,OAAO,GAAgB,EAAE,CAAC;QAEhC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,6CAA6C;gBAC7C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC5B,UAAU,CAAC,SAAS,GAAG,CAAC,EAAE,qBAAqB;gBAC/C,UAAU,CAAC,cAAc,GAAG,CAAC,EAAE,qBAAqB;gBACpD,UAAU,CAAC,OAAO,GAAG,CAAC,EAAE,qBAAqB;gBAC7C,UAAU,CAAC,YAAY,GAAG,CAAC,CAAC,qBAAqB;iBAClD,CAAC;gBAEF,8BAA8B;gBAC9B,MAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,UAAU,CAC5C,KAAK,EACL,UAAU,CAAC,OAAO,EAClB,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAC9C,CAAC;gBAEF,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;oBACpB,gBAAgB,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;gBAC1C,CAAC;gBAED,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;oBACtB,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;gBAC9C,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC3D,IAAI,CAAC,GAAG,EAAE,CAAC;oBACT,OAAO,CAAC,IAAI,CAAC;wBACX,UAAU;wBACV,KAAK,EAAE,KAAK;wBACZ,MAAM,EAAE,6BAA6B;qBACtC,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC9D,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CACtD,kCAAkC,EAClC,GAAG,EACH,KAAK,EACL,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CACrC,CAAC;gBAEF,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC7C,OAAO,CAAC,IAAI,CAAC;wBACX,UAAU;wBACV,KAAK,EAAE,KAAK;wBACZ,MAAM,EAAE,2BAA2B;qBACpC,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBAED,iDAAiD;gBACjD,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAC1C,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAC3B,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO;oBAChC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CACvB,CACF,CAAC;gBAEF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC;wBACX,UAAU;wBACV,KAAK,EAAE,KAAK;wBACZ,MAAM,EAAE,+BAA+B;qBACxC,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBAED,uCAAuC;gBACvC,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;oBAC9B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAEjE,OAAO,CAAC,IAAI,CAAC;wBACX,UAAU;wBACV,KAAK,EAAE,OAAO;wBACd,MAAM,EAAE,SAAS,CAAC,KAAK;wBACvB,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB;qBACnD,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,IAAI,CAAC;wBACX,UAAU;wBACV,KAAK,EAAE,KAAK;wBACZ,MAAM,EAAE,SAAS,CAAC,KAAK;wBACvB,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,mBAAmB;qBACjD,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBACzD,OAAO,CAAC,IAAI,CAAC;oBACX,UAAU;oBACV,KAAK,EAAE,KAAK;oBACZ,MAAM,EAAE,UAAU,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;iBAC3C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;SAEK;IACG,eAAe,CAAC,WAA6B,EAAE,QAAiB;QACtE,gBAAgB;QAChB,MAAM,OAAO,GAAQ;YACnB,UAAU,EAAE,WAAW,CAAC,MAAM;YAC9B,cAAc,EAAE;gBACd,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/B,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnC,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;aAC7B;YACD,SAAS,EAAE,IAAI,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;YACzD,eAAe,EAAE,EAAE;SACpB,CAAC;QAEF,oBAAoB;QACpB,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAE9C,kBAAkB;YAClB,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,SAAS,CAAC;YAC9C,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/E,CAAC;QAED,wCAAwC;QACxC,IAAI,QAAQ,EAAE,CAAC;YACb,gBAAgB;YAChB,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE1D,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;gBACrE,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,MAAM;gBACnB,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM;gBAC7E,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,kBAAkB,CAAC,OAAO,CAAC,CAAC,MAAM;gBACjF,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,kBAAkB,CAAC,WAAW,CAAC,CAAC,MAAM;gBAClF,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC,MAAM;aAC5E,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAEtC,0BAA0B;YAC1B,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA8C,CAAC;YAEvE,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;gBACrC,MAAM,GAAG,GAAG,GAAG,UAAU,CAAC,MAAM,IAAI,SAAS,IAAI,UAAU,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC;gBAEhF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvB,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC/D,CAAC;gBAED,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC;YAED,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;iBAClD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBACnC,GAAG;gBACH,KAAK;gBACL,OAAO;aACR,CAAC,CAAC;iBACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;iBACjC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CAGF;AAlwBD,0CAkwBC;AAED,sDAAsD;AACzC,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { z } from 'zod';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport { Logger } from '../logger';\n\n/**\n * Diagnostic severity levels\n */\nexport enum DiagnosticSeverity {\n  Error = 'error',\n  Warning = 'warning',\n  Information = 'information',\n  Hint = 'hint'\n}\n\n/**\n * Diagnostic source types\n */\nexport enum DiagnosticSource {\n  TypeScript = 'typescript',\n  ESLint = 'eslint',\n  Stylelint = 'stylelint',\n  Compiler = 'compiler',\n  Linter = 'linter',\n  Extension = 'extension',\n  VSCode = 'vscode',\n  Other = 'other'\n}\n\n/**\n * Interface for fix results\n */\ninterface FixResult {\n  diagnostic: DiagnosticInfo;\n  fixed: boolean;\n  reason: string;\n  action?: string;\n}\n\n/**\n * Interface for diagnostic information\n */\nexport interface DiagnosticInfo {\n  message: string;\n  severity: DiagnosticSeverity;\n  source?: DiagnosticSource | string;\n  code?: string | number;\n  startLine: number;\n  startCharacter: number;\n  endLine: number;\n  endCharacter: number;\n  relatedInformation?: {\n    message: string;\n    file?: string;\n    startLine?: number;\n    startCharacter?: number;\n  }[];\n  tags?: string[];\n  filePath: string;\n}\n\n/**\n * Tool for retrieving and analyzing diagnostics from the IDE\n */\nexport class DiagnosticsTool implements ITool {\n  readonly id = 'diagnostics';\n  readonly name = 'Diagnostics Tool';\n  readonly description = 'Retrieves and analyzes diagnostics (errors, warnings, etc.) from the IDE.';\n  readonly type = 'multi-action';\n  // Add index signature to fix \"Element implicitly has an 'any' type\" error\n  readonly actions: { [key: string]: any } = {\n    'get': {\n      description: 'Get diagnostics for specified files or all files in the workspace',\n      schema: z.object({\n        paths: z.array(z.string()).optional().describe('Optional list of file paths to get diagnostics for. If not provided, returns diagnostics for all files.'),\n        severity: z.enum(['error', 'warning', 'information', 'hint', 'all']).optional().describe('Filter diagnostics by severity. Default is all.'),\n        source: z.string().optional().describe('Filter diagnostics by source (e.g., \"typescript\", \"eslint\").'),\n        limit: z.number().optional().describe('Maximum number of diagnostics to return. Default is no limit.')\n      }),\n      inputSchema: z.object({\n        paths: z.array(z.string()).optional().describe('Optional list of file paths to get diagnostics for. If not provided, returns diagnostics for all files.'),\n        severity: z.enum(['error', 'warning', 'information', 'hint', 'all']).optional().describe('Filter diagnostics by severity. Default is all.'),\n        source: z.string().optional().describe('Filter diagnostics by source (e.g., \"typescript\", \"eslint\").'),\n        limit: z.number().optional().describe('Maximum number of diagnostics to return. Default is no limit.')\n      })\n    },\n    'analyze': {\n      description: 'Analyze diagnostics and provide insights',\n      schema: z.object({\n        paths: z.array(z.string()).optional().describe('Optional list of file paths to analyze diagnostics for. If not provided, analyzes all files.'),\n        groupBy: z.enum(['file', 'severity', 'source', 'code']).optional().describe('Group diagnostics by the specified property. Default is file.')\n      }),\n      inputSchema: z.object({\n        paths: z.array(z.string()).optional().describe('Optional list of file paths to analyze diagnostics for. If not provided, analyzes all files.'),\n        groupBy: z.enum(['file', 'severity', 'source', 'code']).optional().describe('Group diagnostics by the specified property. Default is file.')\n      })\n    },\n    'fix': {\n      description: 'Attempt to fix diagnostics automatically',\n      schema: z.object({\n        paths: z.array(z.string()).optional().describe('Optional list of file paths to fix diagnostics for. If not provided, attempts to fix all files.'),\n        severity: z.enum(['error', 'warning', 'information', 'hint', 'all']).optional().describe('Filter diagnostics by severity to fix. Default is all.'),\n        source: z.string().optional().describe('Filter diagnostics by source to fix (e.g., \"typescript\", \"eslint\").'),\n        dryRun: z.boolean().optional().describe('If true, only reports what would be fixed without making changes. Default is false.')\n      }),\n      inputSchema: z.object({\n        paths: z.array(z.string()).optional().describe('Optional list of file paths to fix diagnostics for. If not provided, attempts to fix all files.'),\n        severity: z.enum(['error', 'warning', 'information', 'hint', 'all']).optional().describe('Filter diagnostics by severity to fix. Default is all.'),\n        source: z.string().optional().describe('Filter diagnostics by source to fix (e.g., \"typescript\", \"eslint\").'),\n        dryRun: z.boolean().optional().describe('If true, only reports what would be fixed without making changes. Default is false.')\n      })\n    },\n    'summary': {\n      description: 'Get a summary of diagnostics across the workspace',\n      schema: z.object({\n        detailed: z.boolean().optional().describe('If true, includes detailed breakdown by file, severity, and source. Default is false.')\n      }),\n      inputSchema: z.object({\n        detailed: z.boolean().optional().describe('If true, includes detailed breakdown by file, severity, and source. Default is false.')\n      })\n    }\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      if (!actionName || !this.actions[actionName]) {\n        return {\n          success: false,\n          error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      switch (actionName) {\n        case 'get':\n          return this.executeGet(input, actionName);\n        case 'analyze':\n          return this.executeAnalyze(input, actionName);\n        case 'fix':\n          return this.executeFix(input, actionName);\n        case 'summary':\n          return this.executeSummary(input, actionName);\n        default:\n          return {\n            success: false,\n            error: `Unknown action: ${actionName}`,\n            toolId: this.id,\n            actionName\n          };\n      }\n    } catch (error: any) {\n      Logger.instance.error(`Error executing diagnostics action ${actionName}:`, error);\n      return {\n        success: false,\n        error: `Diagnostics tool failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Get diagnostics for specified files or all files\n     */\n  private async executeGet(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const paths = input.paths as string[] | undefined;\n      const severity = input.severity as DiagnosticSeverity | 'all' | undefined;\n      const source = input.source as string | undefined;\n      const limit = input.limit as number | undefined;\n\n      // Get diagnostics\n      const diagnostics = await this.getDiagnostics(paths);\n\n      // Apply filters\n      let filteredDiagnostics = diagnostics;\n\n      if (severity && severity !== 'all') {\n        filteredDiagnostics = filteredDiagnostics.filter(d => d.severity === severity);\n      }\n\n      if (source) {\n        filteredDiagnostics = filteredDiagnostics.filter(d => d.source === source);\n      }\n\n      // Apply limit\n      if (limit !== undefined && limit > 0) {\n        filteredDiagnostics = filteredDiagnostics.slice(0, limit);\n      }\n\n      return {\n        success: true,\n        output: {\n          diagnostics: filteredDiagnostics,\n          count: filteredDiagnostics.length,\n          totalCount: diagnostics.length\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          paths,\n          severity,\n          source,\n          limit\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error('Error getting diagnostics:', error);\n      return {\n        success: false,\n        error: `Failed to get diagnostics: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Analyze diagnostics and provide insights\n     */\n  private async executeAnalyze(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const paths = input.paths as string[] | undefined;\n      const groupBy = (input.groupBy as string) || 'file';\n\n      // Get diagnostics\n      const diagnostics = await this.getDiagnostics(paths);\n\n      // Group diagnostics\n      const groupedDiagnostics = this.groupDiagnostics(diagnostics, groupBy);\n\n      // Generate insights\n      const insights = this.generateInsights(diagnostics, groupedDiagnostics, groupBy);\n\n      return {\n        success: true,\n        output: {\n          groupedDiagnostics,\n          insights,\n          totalCount: diagnostics.length\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          paths,\n          groupBy\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error('Error analyzing diagnostics:', error);\n      return {\n        success: false,\n        error: `Failed to analyze diagnostics: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Attempt to fix diagnostics automatically\n     */\n  private async executeFix(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const paths = input.paths as string[] | undefined;\n      const severity = input.severity as DiagnosticSeverity | 'all' | undefined;\n      const source = input.source as string | undefined;\n      const dryRun = input.dryRun === true;\n\n      // Get diagnostics\n      const diagnostics = await this.getDiagnostics(paths);\n\n      // Apply filters\n      let filteredDiagnostics = diagnostics;\n\n      if (severity && severity !== 'all') {\n        filteredDiagnostics = filteredDiagnostics.filter(d => d.severity === severity);\n      }\n\n      if (source) {\n        filteredDiagnostics = filteredDiagnostics.filter(d => d.source === source);\n      }\n\n      // Attempt to fix diagnostics\n      const fixResults = await this.fixDiagnostics(filteredDiagnostics, dryRun);\n\n      return {\n        success: true,\n        output: {\n          fixResults,\n          fixedCount: fixResults.filter(r => r.fixed).length,\n          totalCount: filteredDiagnostics.length,\n          dryRun\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          paths,\n          severity,\n          source,\n          dryRun\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error('Error fixing diagnostics:', error);\n      return {\n        success: false,\n        error: `Failed to fix diagnostics: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Get a summary of diagnostics across the workspace\n     */\n  private async executeSummary(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const detailed = input.detailed === true;\n\n      // Get all diagnostics\n      const diagnostics = await this.getDiagnostics();\n\n      // Generate summary\n      const summary = this.generateSummary(diagnostics, detailed);\n\n      return {\n        success: true,\n        output: summary,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          detailed,\n          totalCount: diagnostics.length\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error('Error generating diagnostics summary:', error);\n      return {\n        success: false,\n        error: `Failed to generate diagnostics summary: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Get diagnostics from VS Code for specified files or all files\n     */\n  private async getDiagnostics(paths?: string[]): Promise<DiagnosticInfo[]> {\n    // Get all diagnostics from VS Code\n    const allDiagnostics: DiagnosticInfo[] = [];\n\n    // If specific paths are provided, get diagnostics only for those files\n    if (paths && paths.length > 0) {\n      for (const filePath of paths) {\n        try {\n          const uri = this.resolveWorkspacePath(filePath);\n          if (!uri) {\n            Logger.instance.warn(`Could not resolve file path: ${filePath}`);\n            continue;\n          }\n\n          const diagnostics = vscode.languages.getDiagnostics(uri);\n          allDiagnostics.push(...this.convertDiagnostics(diagnostics, filePath));\n        } catch (error) {\n          Logger.instance.error(`Error getting diagnostics for ${filePath}:`, error);\n        }\n      }\n    } else {\n      // Get diagnostics for all files\n      for (const [uri, diagnostics] of vscode.languages.getDiagnostics()) {\n        const filePath = uri.fsPath;\n        allDiagnostics.push(...this.convertDiagnostics(diagnostics, filePath));\n      }\n    }\n\n    return allDiagnostics;\n  }\n\n  /**\n     * Convert VS Code diagnostics to our DiagnosticInfo format\n     */\n  private convertDiagnostics(diagnostics: vscode.Diagnostic[], filePath: string): DiagnosticInfo[] {\n    return diagnostics.map(d => {\n      // Map VS Code severity to our severity enum\n      let severity: DiagnosticSeverity;\n      switch (d.severity) {\n        case vscode.DiagnosticSeverity.Error:\n          severity = DiagnosticSeverity.Error;\n          break;\n        case vscode.DiagnosticSeverity.Warning:\n          severity = DiagnosticSeverity.Warning;\n          break;\n        case vscode.DiagnosticSeverity.Information:\n          severity = DiagnosticSeverity.Information;\n          break;\n        case vscode.DiagnosticSeverity.Hint:\n          severity = DiagnosticSeverity.Hint;\n          break;\n        default:\n          severity = DiagnosticSeverity.Information;\n      }\n\n      // Extract source from diagnostic\n      let source: string | undefined;\n      if (d.source) {\n        source = d.source.toLowerCase();\n        // Map common sources to our enum\n        if (source.includes('typescript') || source.includes('ts')) {\n          source = DiagnosticSource.TypeScript;\n        } else if (source.includes('eslint')) {\n          source = DiagnosticSource.ESLint;\n        } else if (source.includes('stylelint')) {\n          source = DiagnosticSource.Stylelint;\n        } else if (source.includes('compiler')) {\n          source = DiagnosticSource.Compiler;\n        } else if (source.includes('linter')) {\n          source = DiagnosticSource.Linter;\n        } else if (source.includes('extension')) {\n          source = DiagnosticSource.Extension;\n        } else if (source.includes('vscode')) {\n          source = DiagnosticSource.VSCode;\n        } else {\n          source = DiagnosticSource.Other;\n        }\n      }\n\n      // Convert related information\n      const relatedInformation = d.relatedInformation?.map(ri => ({\n        message: ri.message,\n        file: ri.location.uri.fsPath,\n        startLine: ri.location.range.start.line + 1, // Convert to 1-based\n        startCharacter: ri.location.range.start.character + 1 // Convert to 1-based\n      }));\n\n      // Convert tags\n      const tags: string[] = [];\n      if (d.tags) {\n        for (const tag of d.tags) {\n          if (tag === vscode.DiagnosticTag.Unnecessary) {\n            tags.push('unnecessary');\n          } else if (tag === vscode.DiagnosticTag.Deprecated) {\n            tags.push('deprecated');\n          }\n        }\n      }\n\n      return {\n        message: d.message,\n        severity,\n        source,\n        code: d.code ? String(d.code) : undefined,\n        startLine: d.range.start.line + 1, // Convert to 1-based\n        startCharacter: d.range.start.character + 1, // Convert to 1-based\n        endLine: d.range.end.line + 1, // Convert to 1-based\n        endCharacter: d.range.end.character + 1, // Convert to 1-based\n        relatedInformation,\n        tags,\n        filePath\n      };\n    });\n  }\n\n  /**\n     * Group diagnostics by the specified property\n     */\n  private groupDiagnostics(diagnostics: DiagnosticInfo[], groupBy: string): Record<string, DiagnosticInfo[]> {\n    const grouped: Record<string, DiagnosticInfo[]> = {};\n\n    for (const diagnostic of diagnostics) {\n      let key: string;\n\n      switch (groupBy) {\n        case 'file':\n          key = diagnostic.filePath;\n          break;\n        case 'severity':\n          key = diagnostic.severity;\n          break;\n        case 'source':\n          key = diagnostic.source || 'unknown';\n          break;\n        case 'code':\n          key = diagnostic.code ? String(diagnostic.code) : 'unknown';\n          break;\n        default:\n          key = diagnostic.filePath;\n      }\n\n      if (!grouped[key]) {\n        grouped[key] = [];\n      }\n\n      grouped[key].push(diagnostic);\n    }\n\n    return grouped;\n  }\n\n  /**\n     * Generate insights from diagnostics\n     */\n  private generateInsights(\n    diagnostics: DiagnosticInfo[],\n    groupedDiagnostics: Record<string, DiagnosticInfo[]>,\n    groupBy: string\n  ): any {\n    const insights: any = {\n      totalCount: diagnostics.length,\n      severityCounts: {\n        [DiagnosticSeverity.Error]: 0,\n        [DiagnosticSeverity.Warning]: 0,\n        [DiagnosticSeverity.Information]: 0,\n        [DiagnosticSeverity.Hint]: 0\n      },\n      sourceCounts: {},\n      mostCommonIssues: [],\n      recommendations: []\n    };\n\n    // Count by severity\n    for (const diagnostic of diagnostics) {\n      insights.severityCounts[diagnostic.severity]++;\n\n      // Count by source\n      const source = diagnostic.source || 'unknown';\n      insights.sourceCounts[source] = (insights.sourceCounts[source] || 0) + 1;\n    }\n\n    // Find most common issues\n    const issueMap = new Map<string, { count: number, examples: DiagnosticInfo[] }>();\n\n    for (const diagnostic of diagnostics) {\n      const key = `${diagnostic.source || 'unknown'}-${diagnostic.code || 'unknown'}`;\n\n      if (!issueMap.has(key)) {\n        issueMap.set(key, { count: 0, examples: [] });\n      }\n\n      const issue = issueMap.get(key)!;\n      issue.count++;\n\n      if (issue.examples.length < 3) {\n        issue.examples.push(diagnostic);\n      }\n    }\n\n    // Sort issues by count\n    insights.mostCommonIssues = Array.from(issueMap.entries())\n      .map(([key, { count, examples }]) => ({\n        key,\n        count,\n        examples\n      }))\n      .sort((a, b) => b.count - a.count)\n      .slice(0, 5);\n\n    // Generate recommendations\n    if (insights.severityCounts[DiagnosticSeverity.Error] > 0) {\n      insights.recommendations.push('Fix all errors before proceeding with other changes.');\n    }\n\n    if (insights.mostCommonIssues.length > 0) {\n      insights.recommendations.push(`Focus on fixing the most common issue first: ${insights.mostCommonIssues[0].examples[0].message}`);\n    }\n\n    // Add group-specific insights\n    switch (groupBy) {\n      case 'file':\n        // Find files with most issues\n        insights.filesWithMostIssues = Object.entries(groupedDiagnostics)\n          .map(([file, diags]) => ({\n            file,\n            count: diags.length,\n            errorCount: diags.filter(d => d.severity === DiagnosticSeverity.Error).length\n          }))\n          .sort((a, b) => b.count - a.count)\n          .slice(0, 5);\n\n        if (insights.filesWithMostIssues.length > 0) {\n          insights.recommendations.push(`Prioritize fixing issues in ${insights.filesWithMostIssues[0].file} which has ${insights.filesWithMostIssues[0].count} issues.`);\n        }\n        break;\n\n      case 'severity':\n        // Add severity-specific recommendations\n        if (insights.severityCounts[DiagnosticSeverity.Warning] > 10) {\n          insights.recommendations.push('Consider addressing warnings to improve code quality.');\n        }\n        break;\n\n      case 'source':\n        // Find most problematic sources\n        insights.mostProblematicSources = Object.entries(groupedDiagnostics)\n          .map(([source, diags]) => ({\n            source,\n            count: diags.length,\n            errorCount: diags.filter(d => d.severity === DiagnosticSeverity.Error).length\n          }))\n          .sort((a, b) => b.count - a.count)\n          .slice(0, 3);\n\n        if (insights.mostProblematicSources.length > 0) {\n          insights.recommendations.push(`Consider running a linter or formatter to address ${insights.mostProblematicSources[0].source} issues.`);\n        }\n        break;\n    }\n\n    return insights;\n  }\n\n  /**\n     * Map our diagnostic severity to VS Code's DiagnosticSeverity\n     */\n  private mapSeverityToVSCode(severity: DiagnosticSeverity): vscode.DiagnosticSeverity {\n    switch (severity) {\n      case DiagnosticSeverity.Error:\n        return vscode.DiagnosticSeverity.Error;\n      case DiagnosticSeverity.Warning:\n        return vscode.DiagnosticSeverity.Warning;\n      case DiagnosticSeverity.Information:\n        return vscode.DiagnosticSeverity.Information;\n      case DiagnosticSeverity.Hint:\n        return vscode.DiagnosticSeverity.Hint;\n      default:\n        return vscode.DiagnosticSeverity.Information;\n    }\n  }\n\n  /**\n     * Resolve a workspace path to a vscode.Uri\n     */\n  private resolveWorkspacePath(filePath: string): vscode.Uri | undefined {\n    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n      const workspaceRoot = vscode.workspace.workspaceFolders[0].uri;\n\n      // Check if it's already absolute\n      try {\n        const uri = vscode.Uri.parse(filePath);\n        if (uri.scheme) return uri; // Already absolute\n      } catch (e) {\n        // Ignore parsing errors, treat as relative\n      }\n\n      // If relative, join with workspace root\n      return vscode.Uri.joinPath(workspaceRoot, filePath);\n    } else if (vscode.Uri.parse(filePath).scheme) {\n      // Absolute path outside workspace\n      return vscode.Uri.parse(filePath);\n    }\n\n    // Relative path but no workspace open\n    Logger.instance.warn(`Cannot resolve relative path \"${filePath}\" without an open workspace folder.`);\n    return undefined;\n  }\n\n  /**\n     * Attempt to fix diagnostics automatically\n     */\n  private async fixDiagnostics(diagnostics: DiagnosticInfo[], dryRun: boolean): Promise<FixResult[]> {\n    const results: FixResult[] = [];\n\n    for (const diagnostic of diagnostics) {\n      try {\n        // Create a VS Code range from the diagnostic\n        const range = new vscode.Range(\n          diagnostic.startLine - 1, // Convert to 0-based\n          diagnostic.startCharacter - 1, // Convert to 0-based\n          diagnostic.endLine - 1, // Convert to 0-based\n          diagnostic.endCharacter - 1 // Convert to 0-based\n        );\n\n        // Create a VS Code diagnostic\n        const vscodeDiagnostic = new vscode.Diagnostic(\n          range,\n          diagnostic.message,\n          this.mapSeverityToVSCode(diagnostic.severity)\n        );\n\n        if (diagnostic.code) {\n          vscodeDiagnostic.code = diagnostic.code;\n        }\n\n        if (diagnostic.source) {\n          vscodeDiagnostic.source = diagnostic.source;\n        }\n\n        // Try to get code actions for the diagnostic\n        const uri = this.resolveWorkspacePath(diagnostic.filePath);\n        if (!uri) {\n          results.push({\n            diagnostic,\n            fixed: false,\n            reason: 'Could not resolve file path'\n          });\n          continue;\n        }\n\n        const document = await vscode.workspace.openTextDocument(uri);\n        const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>(\n          'vscode.executeCodeActionProvider',\n          uri,\n          range,\n          vscode.CodeActionKind.QuickFix.value\n        );\n\n        if (!codeActions || codeActions.length === 0) {\n          results.push({\n            diagnostic,\n            fixed: false,\n            reason: 'No code actions available'\n          });\n          continue;\n        }\n\n        // Find a code action that can fix the diagnostic\n        const fixAction = codeActions.find(action =>\n          action.diagnostics?.some(d =>\n            d.message === diagnostic.message &&\n            d.range.isEqual(range)\n          )\n        );\n\n        if (!fixAction) {\n          results.push({\n            diagnostic,\n            fixed: false,\n            reason: 'No matching code action found'\n          });\n          continue;\n        }\n\n        // Apply the fix if not in dry run mode\n        if (!dryRun && fixAction.edit) {\n          const success = await vscode.workspace.applyEdit(fixAction.edit);\n\n          results.push({\n            diagnostic,\n            fixed: success,\n            action: fixAction.title,\n            reason: success ? 'Fixed' : 'Failed to apply edit'\n          });\n        } else {\n          results.push({\n            diagnostic,\n            fixed: false,\n            action: fixAction.title,\n            reason: dryRun ? 'Dry run' : 'No edit available'\n          });\n        }\n      } catch (error: any) {\n        Logger.instance.error('Error fixing diagnostic:', error);\n        results.push({\n          diagnostic,\n          fixed: false,\n          reason: `Error: ${error.message || error}`\n        });\n      }\n    }\n\n    return results;\n  }\n\n  /**\n     * Generate a summary of diagnostics\n     */\n  private generateSummary(diagnostics: DiagnosticInfo[], detailed: boolean): any {\n    // Basic summary\n    const summary: any = {\n      totalCount: diagnostics.length,\n      severityCounts: {\n        [DiagnosticSeverity.Error]: 0,\n        [DiagnosticSeverity.Warning]: 0,\n        [DiagnosticSeverity.Information]: 0,\n        [DiagnosticSeverity.Hint]: 0\n      },\n      fileCount: new Set(diagnostics.map(d => d.filePath)).size,\n      sourceBreakdown: {}\n    };\n\n    // Count by severity\n    for (const diagnostic of diagnostics) {\n      summary.severityCounts[diagnostic.severity]++;\n\n      // Count by source\n      const source = diagnostic.source || 'unknown';\n      summary.sourceBreakdown[source] = (summary.sourceBreakdown[source] || 0) + 1;\n    }\n\n    // Add detailed information if requested\n    if (detailed) {\n      // Group by file\n      const byFile = this.groupDiagnostics(diagnostics, 'file');\n\n      summary.fileBreakdown = Object.entries(byFile).map(([file, diags]) => ({\n        file,\n        count: diags.length,\n        errorCount: diags.filter(d => d.severity === DiagnosticSeverity.Error).length,\n        warningCount: diags.filter(d => d.severity === DiagnosticSeverity.Warning).length,\n        infoCount: diags.filter(d => d.severity === DiagnosticSeverity.Information).length,\n        hintCount: diags.filter(d => d.severity === DiagnosticSeverity.Hint).length\n      })).sort((a, b) => b.count - a.count);\n\n      // Find most common issues\n      const issueMap = new Map<string, { count: number, message: string }>();\n\n      for (const diagnostic of diagnostics) {\n        const key = `${diagnostic.source || 'unknown'}-${diagnostic.code || 'unknown'}`;\n\n        if (!issueMap.has(key)) {\n          issueMap.set(key, { count: 0, message: diagnostic.message });\n        }\n\n        issueMap.get(key)!.count++;\n      }\n\n      summary.commonIssues = Array.from(issueMap.entries())\n        .map(([key, { count, message }]) => ({\n          key,\n          count,\n          message\n        }))\n        .sort((a, b) => b.count - a.count)\n        .slice(0, 10);\n    }\n\n    return summary;\n  }\n\n\n}\n\n// Export a singleton instance of the diagnostics tool\nexport const diagnosticsTool = new DiagnosticsTool();"]}