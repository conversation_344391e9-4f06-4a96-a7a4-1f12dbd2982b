import * as vscode from 'vscode';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import { z } from 'zod';

export class CodeIntelligenceTool implements ITool {
  readonly id = 'codeIntel';
  readonly name = 'Code Intelligence';
  readonly description = 'Go to definition, find references, symbol search, hover docs.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    action: z.enum(['gotoDefinition', 'findReferences', 'symbolSearch', 'hover']).describe('Code intelligence action.'),
    filePath: z.string().optional().describe('File path for action.'),
    position: z.object({
      line: z.number(),
      column: z.number()
    }).optional().describe('Cursor position.'),
    query: z.string().optional().describe('Query for symbol search.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      action: { type: 'string', enum: ['gotoDefinition', 'findReferences', 'symbolSearch', 'hover'], description: 'Code intelligence action.' },
      filePath: { type: 'string', description: 'File path for action.' },
      position: { type: 'object', description: 'Cursor position.', properties: { line: { type: 'number' }, column: { type: 'number' } } },
      query: { type: 'string', description: 'Query for symbol search.' }
    },
    required: ['action']
  };

  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    const action = input.action as string;
    try {
      if (action === 'gotoDefinition') {
        const { filePath, position } = input;
        if (!filePath || !position) return { success: false, error: '\'filePath\' and \'position\' are required.', toolId: this.id, actionName };
        const doc = await vscode.workspace.openTextDocument(filePath);
        const pos = new vscode.Position(position.line, position.column);
        const locations = await vscode.commands.executeCommand<vscode.Location[]>('vscode.executeDefinitionProvider', doc.uri, pos);
        return { success: true, output: locations, toolId: this.id, actionName };
      } else if (action === 'findReferences') {
        const { filePath, position } = input;
        if (!filePath || !position) return { success: false, error: '\'filePath\' and \'position\' are required.', toolId: this.id, actionName };
        const doc = await vscode.workspace.openTextDocument(filePath);
        const pos = new vscode.Position(position.line, position.column);
        const locations = await vscode.commands.executeCommand<vscode.Location[]>('vscode.executeReferenceProvider', doc.uri, pos);
        return { success: true, output: locations, toolId: this.id, actionName };
      } else if (action === 'symbolSearch') {
        const query = input.query as string;
        if (!query) return { success: false, error: '\'query\' is required for symbol search.', toolId: this.id, actionName };
        const symbols = await vscode.commands.executeCommand<any[]>('vscode.executeWorkspaceSymbolProvider', query);
        return { success: true, output: symbols, toolId: this.id, actionName };
      } else if (action === 'hover') {
        const { filePath, position } = input;
        if (!filePath || !position) return { success: false, error: '\'filePath\' and \'position\' are required.', toolId: this.id, actionName };
        const doc = await vscode.workspace.openTextDocument(filePath);
        const pos = new vscode.Position(position.line, position.column);
        const hovers = await vscode.commands.executeCommand<any[]>('vscode.executeHoverProvider', doc.uri, pos);
        return { success: true, output: hovers, toolId: this.id, actionName };
      } else {
        return { success: false, error: `Unknown code intelligence action: ${action}`, toolId: this.id, actionName };
      }
    } catch (error: any) {
      return { success: false, error: `Code intelligence action failed: ${error.message || error}`, toolId: this.id, actionName };
    }
  }
}

export const codeIntelligenceTool = new CodeIntelligenceTool();
