/**
 * Pattern Refactoring Workflow
 *
 * This module provides a workflow for pattern-based code refactoring:
 * - Identifying code patterns
 * - Applying design patterns
 * - Refactoring code to follow best practices
 */

import { ITool } from '../../tools/tool.ts.backup';
import { Agent } from '../agentUtilities/agent';
import { Codessa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';
import { workflowRegistry } from './workflowRegistry';
import { Logger } from '../../logger';
import { StructuredTool } from './corePolyfill';

/**
 * Create a Pattern Refactoring workflow
 */
export function createPatternRefactoringWorkflow(
  id: string,
  name: string,
  description: string,
  analyzerAgent: Agent,
  refactoringAgent: Agent,
  testingAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  Logger.instance.info(`Creating Pattern Refactoring workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const codeAnalysisNode = Codessa.createAgentNode('code-analysis', 'Code Analysis', analyzerAgent);
  const patternIdentificationNode = Codessa.createAgentNode('pattern-identification', 'Pattern Identification', analyzerAgent);
  const designPatternSelectionNode = Codessa.createAgentNode('design-pattern-selection', 'Design Pattern Selection', analyzerAgent);
  const refactoringPlanNode = Codessa.createAgentNode('refactoring-plan', 'Refactoring Plan', refactoringAgent);
  const codeTransformationNode = Codessa.createAgentNode('code-transformation', 'Code Transformation', refactoringAgent);
  const testingNode = Codessa.createAgentNode('testing', 'Testing', testingAgent);
  const documentationUpdateNode = Codessa.createAgentNode('documentation-update', 'Documentation Update', refactoringAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'default' },
    { name: 'analysis-to-pattern', source: 'code-analysis', target: 'pattern-identification', type: 'default' },
    { name: 'pattern-to-design', source: 'pattern-identification', target: 'design-pattern-selection', type: 'default' },
    { name: 'design-to-plan', source: 'design-pattern-selection', target: 'refactoring-plan', type: 'default' },
    { name: 'plan-to-transformation', source: 'refactoring-plan', target: 'code-transformation', type: 'default' },
    { name: 'transformation-to-testing', source: 'code-transformation', target: 'testing', type: 'default' },
    { name: 'testing-to-documentation', source: 'testing', target: 'documentation-update', type: 'default' },
    { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },

    // Feedback loops
    { name: 'testing-to-transformation', source: 'testing', target: 'code-transformation', type: 'feedback' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect code transformation to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `transformation-to-tool-${index}`,
        source: 'code-transformation',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to testing
      edges.push({
        name: `tool-${index}-to-testing`,
        source: `tool-${index}`,
        target: 'testing',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      codeAnalysisNode,
      patternIdentificationNode,
      designPatternSelectionNode,
      refactoringPlanNode,
      codeTransformationNode,
      testingNode,
      documentationUpdateNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'pattern-refactoring' as OperationMode,
    tags: ['refactoring', 'design-patterns', 'code-quality']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a specialized Design Pattern Implementation workflow
 */
export function createDesignPatternImplementationWorkflow(
  id: string,
  name: string,
  description: string,
  designerAgent: Agent,
  implementerAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  Logger.instance.info(`Creating Design Pattern Implementation workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const requirementsAnalysisNode = Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', designerAgent);
  const patternSelectionNode = Codessa.createAgentNode('pattern-selection', 'Pattern Selection', designerAgent);
  const patternAdaptationNode = Codessa.createAgentNode('pattern-adaptation', 'Pattern Adaptation', designerAgent);
  const classDesignNode = Codessa.createAgentNode('class-design', 'Class Design', designerAgent);
  const interfaceDesignNode = Codessa.createAgentNode('interface-design', 'Interface Design', designerAgent);
  const implementationNode = Codessa.createAgentNode('implementation', 'Implementation', implementerAgent);
  const testCaseGenerationNode = Codessa.createAgentNode('test-case-generation', 'Test Case Generation', implementerAgent);
  const documentationNode = Codessa.createAgentNode('documentation', 'Documentation', implementerAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },
    { name: 'requirements-to-pattern', source: 'requirements-analysis', target: 'pattern-selection', type: 'default' },
    { name: 'pattern-to-adaptation', source: 'pattern-selection', target: 'pattern-adaptation', type: 'default' },
    { name: 'adaptation-to-class', source: 'pattern-adaptation', target: 'class-design', type: 'default' },
    { name: 'adaptation-to-interface', source: 'pattern-adaptation', target: 'interface-design', type: 'default' },
    { name: 'class-to-implementation', source: 'class-design', target: 'implementation', type: 'default' },
    { name: 'interface-to-implementation', source: 'interface-design', target: 'implementation', type: 'default' },
    { name: 'implementation-to-test', source: 'implementation', target: 'test-case-generation', type: 'default' },
    { name: 'test-to-documentation', source: 'test-case-generation', target: 'documentation', type: 'default' },
    { name: 'documentation-to-output', source: 'documentation', target: 'output', type: 'default' },

    // Feedback loops
    { name: 'test-to-implementation', source: 'test-case-generation', target: 'implementation', type: 'feedback' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect implementation to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `implementation-to-tool-${index}`,
        source: 'implementation',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to test case generation
      edges.push({
        name: `tool-${index}-to-test`,
        source: `tool-${index}`,
        target: 'test-case-generation',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      requirementsAnalysisNode,
      patternSelectionNode,
      patternAdaptationNode,
      classDesignNode,
      interfaceDesignNode,
      implementationNode,
      testCaseGenerationNode,
      documentationNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'pattern-refactoring' as OperationMode,
    tags: ['design-patterns', 'implementation', 'architecture']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}
