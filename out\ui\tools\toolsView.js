"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolsTreeDataProvider = void 0;
exports.registerToolsTreeView = registerToolsTreeView;
const vscode = __importStar(require("vscode"));
const toolRegistry_1 = require("../../tools/toolRegistry");
const logger_1 = require("../../logger");
const path = __importStar(require("path"));
const baseTreeItem_1 = require("../treeItems/baseTreeItem");
/**
 * TreeItem representing a Tool in the sidebar tree view
 */
class ToolTreeItem extends baseTreeItem_1.BaseTreeItem {
    toolId;
    name;
    toolDescription;
    collapsible;
    constructor(toolId, name, toolDescription, collapsible = false) {
        super(name, collapsible ? vscode.TreeItemCollapsibleState.Collapsed : vscode.TreeItemCollapsibleState.None);
        this.toolId = toolId;
        this.name = name;
        this.toolDescription = toolDescription;
        this.collapsible = collapsible;
        this.init({
            id: toolId,
            tooltip: toolDescription,
            description: '',
            contextValue: 'tool'
        });
        const resourcesPath = path.join(__dirname, '..', '..', 'resources');
        this.iconPath = {
            light: vscode.Uri.file(path.join(resourcesPath, 'light', 'tool.svg')),
            dark: vscode.Uri.file(path.join(resourcesPath, 'dark', 'tool.svg'))
        };
    }
}
/**
 * Tree data provider for the tools sidebar
 */
class ToolsTreeDataProvider {
    _onDidChangeTreeData = new vscode.EventEmitter();
    onDidChangeTreeData = this._onDidChangeTreeData.event;
    /**
       * Refresh the entire tree
       */
    refresh() {
        this._onDidChangeTreeData.fire(null);
    }
    /**
       * Get tree item representation for an element
       */
    getTreeItem(element) {
        return element;
    }
    /**
       * Get children of the provided element, or root elements if no element provided
       */
    async getChildren(element) {
        if (!element) {
            // Root level - show all tools
            return await this.getTools();
        }
        // If the element is a parent tool with sub-actions, show its sub-actions
        const id = element.id;
        if (!id)
            return [];
        const tool = await toolRegistry_1.ToolRegistry.instance.getTool(id);
        if (tool && tool.actions) {
            return Object.entries(tool.actions).map(([subId, subTool]) => {
                const name = subTool.name || subId;
                const description = subTool.description || '';
                const hasActions = !!subTool.actions;
                return new ToolTreeItem(`${tool.id}.${subId}`, name, description, hasActions);
            });
        }
        return [];
    }
    /**
       * Get all tools as tree items
       */
    async getTools() {
        try {
            const tools = await toolRegistry_1.ToolRegistry.instance.getAllTools();
            if (tools.length === 0) {
                const noToolsItem = new vscode.TreeItem('No tools available', vscode.TreeItemCollapsibleState.None);
                noToolsItem.tooltip = 'Tools will appear here once a provider is configured';
                noToolsItem.contextValue = 'noTools';
                return [noToolsItem];
            }
            return tools.map(tool => {
                const name = tool.name || tool.id;
                const description = tool.description || '';
                const hasActions = !!tool.actions;
                return new ToolTreeItem(tool.id, name, description, hasActions);
            });
        }
        catch (error) {
            logger_1.Logger.instance.error('Error getting tools for tree view:', error);
            const errorItem = new vscode.TreeItem('Click to configure settings', vscode.TreeItemCollapsibleState.None);
            errorItem.command = {
                command: 'codessa.openSettings',
                title: 'Open Settings',
                arguments: []
            };
            errorItem.tooltip = `Error loading tools: ${error instanceof Error ? error.message : String(error)}`;
            return [errorItem];
        }
    }
}
exports.ToolsTreeDataProvider = ToolsTreeDataProvider;
/**
 * Setup and register the tools tree view
 */
function registerToolsTreeView(context) {
    const treeDataProvider = new ToolsTreeDataProvider();
    const treeView = vscode.window.createTreeView('codessaToolsView', {
        treeDataProvider,
        showCollapseAll: false,
        canSelectMany: false
    });
    context.subscriptions.push(treeView, vscode.commands.registerCommand('codessa.refreshToolsView', () => treeDataProvider.refresh()));
    return treeView;
}
//# sourceMappingURL=toolsView.js.map