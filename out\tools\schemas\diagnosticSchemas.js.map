{"version": 3, "file": "diagnosticSchemas.js", "sourceRoot": "", "sources": ["../../../src/tools/schemas/diagnosticSchemas.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AAEX,QAAA,sBAAsB,GAAG,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AAGtE,QAAA,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IACtC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC/B,QAAQ,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,8BAAsB,EAAE,OAAC,CAAC,KAAK,CAAC,8BAAsB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACvF,IAAI,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC3D,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC7D,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;CACpC,CAAC,CAAC;AAEU,QAAA,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,QAAQ,EAAE,8BAAsB;IAChC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC3B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC7B,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;IACnB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;IAChB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;IAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;IAClB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,kBAAkB,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QACnC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;QACnB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;QAChB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;QAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;KACnB,CAAC,CAAC,CAAC,QAAQ,EAAE;CACf,CAAC,CAAC", "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const DiagnosticSeverityEnum = z.enum(['error', 'warning', 'info', 'hint']);\r\nexport type DiagnosticSeverity = z.infer<typeof DiagnosticSeverityEnum>;\r\n\r\nexport const DiagnosticInput = z.object({\r\n  filePath: z.string().optional(),\r\n  severity: z.union([DiagnosticSeverityEnum, z.array(DiagnosticSeverityEnum)]).optional(),\r\n  code: z.union([z.string(), z.array(z.string())]).optional(),\r\n  source: z.union([z.string(), z.array(z.string())]).optional(),\r\n  message: z.string().optional(),\r\n  limit: z.number().min(1).optional()\r\n});\r\n\r\nexport const DiagnosticResult = z.object({\r\n  severity: DiagnosticSeverityEnum,\r\n  code: z.string().optional(),\r\n  source: z.string().optional(),\r\n  message: z.string(),\r\n  file: z.string(),\r\n  line: z.number(),\r\n  column: z.number(),\r\n  endLine: z.number().optional(),\r\n  endColumn: z.number().optional(),\r\n  relatedInformation: z.array(z.object({\r\n    message: z.string(),\r\n    file: z.string(),\r\n    line: z.number(),\r\n    column: z.number()\r\n  })).optional()\r\n});\r\n\r\nexport type DiagnosticInputType = z.infer<typeof DiagnosticInput>;\r\nexport type DiagnosticResultType = z.infer<typeof DiagnosticResult>;\r\n"]}