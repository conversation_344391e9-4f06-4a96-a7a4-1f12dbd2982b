"use strict";
/**
 * Model Context Protocol (MCP) Workflow
 *
 * This module provides a workflow for the Model Context Protocol (MCP),
 * which is a standard for exchanging context between different AI models
 * and tools.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMCPWorkflow = createMCPWorkflow;
const templates_1 = require("./templates");
// Import the MCPManager from its new location
const mcp_1 = require("../../mcp");
/**
 * Creates an MCP workflow
 */
function createMCPWorkflow(agent, options = {}) {
    const name = options.name || 'MCP Workflow';
    const description = options.description || 'Workflow for Model Context Protocol integration';
    // Log that we're creating an MCP workflow
    mcp_1.mcpManager.addToolResult('createMCPWorkflow', 'Creating a new MCP workflow', { name, description }, { workflowId: `mcp-workflow-${Date.now()}` });
    // Create workflow
    return (0, templates_1.createReActWorkflow)(`mcp-workflow-${Date.now()}`, name, description, agent, options.tools || []);
}
//# sourceMappingURL=mcpWorkflow.js.map