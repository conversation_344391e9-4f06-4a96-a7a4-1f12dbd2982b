{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EA,4BA24BC;AAz9BD,+CAAiC;AACjC,qCAA0C;AAC1C,iDAA8C;AAC9C,uEAAoE;AACpE,uDAAoD;AACpD,0EAAuE;AACvE,0DAAuD;AACvD,wEAAqE;AACrE,2EAAwE;AACxE,iDAA8C;AAC9C,2DAAwD;AACxD,iEAA8D;AAC9D,sFAA8D;AAC9D,yEAAsE;AACtE,uEAAuE;AACvE,kEAA+D;AAC/D,4DAAyD;AAEzD,qEAA0E;AAI1E,+CAAqD;AACrD,iEAA8D;AAC9D,2DAAwD;AACxD,qEAAkE;AAClE,yDAAsD;AACtD,qDAAkD;AAClD,mDAAgD;AAChD,mEAAgE;AAChE,+DAA4D;AAC5D,6CAA0C;AAC1C,iEAA8D;AAC9D,uEAAoE;AACpE,mEAAgE;AAChE,qEAAkE;AAClE,iEAAmE;AACnE,mCAAuC;AACvC,uDAA2D;AAC3D,qEAAkE;AAClE,2DAAwD;AACxD,mEAAgE;AAChE,mEAAgE;AAChE,+DAA4D;AAC5D,oFAAiF;AACjF,kFAA+E;AAC/E,qDAA8D;AAC9D,+DAA4D;AAE5D,yDAAsD;AACtD,sDAA6D;AAyB7D,kEAA+D;AAE/D,yDAAyD;AAClD,KAAK,UAAU,QAAQ,CAAC,OAAgC;IAC7D,eAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC3B,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IAE/C,IAAI,YAA0B,CAAC;IAE/B,oCAAoC;IACpC,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,iCAAe,CAAC,WAAW,EAAE,CAAC;QACtD,MAAM,eAAe,CAAC,UAAU,EAAE,CAAC;QACnC,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,YAAY,GAAG,kDAAkD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACvH,eAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,qDAAqD;QACrD,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAED,yBAAyB;IACzB,IAAI,CAAC;QACH,uBAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC/B,eAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACzC,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,YAAY,GAAG,8CAA8C,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACnH,eAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,qDAAqD;QACrD,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAED,4BAA4B;IAC5B,MAAM,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;IAC1C,IAAI,CAAC;QACH,MAAM,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACxC,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,YAAY,GAAG,iDAAiD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACtH,eAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,qDAAqD;QACrD,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAED,4BAA4B;IAC5B,IAAI,CAAC;QACH,MAAM,6BAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACxC,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,YAAY,GAAG,iDAAiD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACtH,eAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,qDAAqD;QACrD,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAED,mCAAmC;IACnC,IAAI,CAAC;QACH,MAAM,2CAAoB,CAAC,YAAY,CAAC,4EAA4E,CAAC,CAAC;QACtH,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACnD,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,YAAY,GAAG,wDAAwD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7H,eAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,qDAAqD;QACrD,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAED,qEAAqE;IACrE,IAAI,CAAC;QACH,YAAY,GAAG,MAAM,2BAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACtD,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,YAAY,GAAG,gDAAgD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACrH,eAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC7C,OAAO;IACT,CAAC;IAED,4FAA4F;IAC5F,IAAI,CAAC;QACH,YAAY,CAAC,kBAAkB,CAAC,IAAI,4BAAiB,EAAE,EAAE,MAAM,CAAC,CAAC;QACjE,YAAY,CAAC,kBAAkB,CAAC,IAAI,qCAAiB,EAAE,EAAE,SAAS,CAAC,CAAC;QACpE,YAAY,CAAC,kBAAkB,CAAC,IAAI,+BAAc,EAAE,EAAE,YAAY,CAAC,CAAC;QACpE,YAAY,CAAC,kBAAkB,CAAC,IAAI,yCAAmB,EAAE,EAAE,kBAAkB,CAAC,CAAC;QAC/E,YAAY,CAAC,kBAAkB,CAAC,IAAI,6BAAa,EAAE,EAAE,WAAW,CAAC,CAAC;QAClE,YAAY,CAAC,kBAAkB,CAAC,IAAI,yBAAW,EAAE,EAAE,SAAS,CAAC,CAAC;QAC9D,YAAY,CAAC,kBAAkB,CAAC,IAAI,uBAAU,CAAC,aAAa,CAAC,EAAE,mBAAmB,CAAC,CAAC;QACpF,YAAY,CAAC,kBAAkB,CAAC,IAAI,uCAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAC5E,YAAY,CAAC,kBAAkB,CAAC,IAAI,mCAAgB,EAAE,EAAE,cAAc,CAAC,CAAC;QACxE,YAAY,CAAC,kBAAkB,CAAC,IAAI,iBAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QACtD,YAAY,CAAC,kBAAkB,CAAC,IAAI,qCAAiB,EAAE,EAAE,QAAQ,CAAC,CAAC;QACnE,YAAY,CAAC,kBAAkB,CAAC,IAAI,2CAAoB,EAAE,EAAE,WAAW,CAAC,CAAC;QACzE,YAAY,CAAC,kBAAkB,CAAC,IAAI,uCAAkB,EAAE,EAAE,SAAS,CAAC,CAAC;QACrE,YAAY,CAAC,kBAAkB,CAAC,IAAI,yCAAmB,EAAE,EAAE,kBAAkB,CAAC,CAAC;QAC/E,YAAY,CAAC,kBAAkB,CAAC,IAAI,0CAAsB,EAAE,EAAE,cAAc,CAAC,CAAC;IAChF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC;IAED,gDAAgD;IAChD,MAAM,eAAe,GAAG,2BAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC5D,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjC,eAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAChE,CAAC;SAAM,CAAC;QACN,eAAM,CAAC,IAAI,CAAC,2BAA2B,eAAe,CAAC,MAAM,QAAQ,CAAC,CAAC;QACvE,2BAA2B;QAC3B,eAAe,CAAC,OAAO,CAAC,CAAC,IAAW,EAAE,EAAE;YACtC,eAAM,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,4DAA4D;IAC5D,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;QACzB,OAAO,EAAE,GAAG,EAAE;YACZ,IAAI,CAAC;gBACH,iBAAiB;gBACjB,MAAM,KAAK,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;gBACzC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;wBACjB,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;KACF,CAAC,CAAC;IAEH,qFAAqF;IACrF,oBAAoB;IACpB,IAAI,CAAC;QACH,MAAM,mCAAgB,CAAC,UAAU,EAAE,CAAC;QACpC,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oDAAoD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtI,CAAC;IAED,2BAA2B;IAC3B,IAAI,CAAC;QACH,MAAM,IAAA,6BAAa,EAAC,OAAO,CAAC,CAAC;QAC7B,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gDAAgD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClI,CAAC;IAED,6BAA6B;IAC7B,IAAI,CAAC;QACH,MAAM,qCAAqB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACrD,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAC7C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kDAAkD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpI,CAAC;IAED,wBAAwB;IACxB,IAAI,qBAAqB,CAAC;IAC1B,IAAI,CAAC;QACH,qBAAqB,GAAG,IAAI,uBAAa,CAAC;YACxC,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,gBAAgB;YACtB,gBAAgB,EAAE,UAAU;YAC5B,YAAY,EAAE,CAAC,SAAS,CAAC;YACzB,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2CAA2C,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7H,CAAC;IAED,uBAAuB;IACvB,IAAI,YAAY,CAAC;IACjB,IAAI,CAAC;QACH,MAAM,kBAAkB,GAAG,uBAAU,CAAC;QACtC,YAAY,GAAG;YACb,SAAS,EAAE;gBACT,eAAe,EAAf,iCAAe;gBACf,UAAU,EAAE,kBAAkB;gBAC9B,aAAa,EAAb,6BAAa;gBACb,oBAAoB,EAApB,2CAAoB;aACrB;SACF,CAAC;QACF,IAAI,qBAAqB,EAAE,CAAC;YAC1B,qBAAqB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC/C,MAAM,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;YAChD,YAAY,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;YACrD,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uCAAuC,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzH,CAAC;IAED,0BAA0B;IAC1B,IAAI,eAAe,CAAC;IACpB,IAAI,CAAC;QACH,eAAe,GAAG,IAAI,iCAAe,CAAC;YACpC,EAAE,EAAE,kBAAkB;YACtB,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,+CAA+C;YAC5D,gBAAgB,EAAE,iBAAiB;YACnC,KAAK,EAAE,CAAC,oBAAoB,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,CAAC;SAClE,EAAE,qBAAqB,CAAC,CAAC;QAC1B,IAAI,YAAY,IAAI,eAAe,EAAE,CAAC;YACpC,eAAe,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;YAChD,YAAY,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YACjD,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6CAA6C,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/H,CAAC;IAED,sCAAsC;IACtC,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,qCAAqB,CAAC,cAAc,EAAE,CAAC;QAC3D,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,SAAS,GAAG,IAAA,8BAAe,EAAC,WAAW,CAAC,EAAE,EAAE;gBAChD,EAAE,EAAE,QAAQ,WAAW,CAAC,EAAE,EAAE;gBAC5B,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,gBAAgB,EAAE,WAAW,CAAC,EAAE;aACjC,CAAC,CAAC;YACH,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;gBAChD,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACjC,eAAM,CAAC,IAAI,CAAC,uBAAuB,WAAW,CAAC,EAAE,eAAe,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+CAA+C,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjI,CAAC;IAED,+BAA+B;IAC/B,IAAI,CAAC;QACH,IAAI,OAAO,IAAI,qBAAqB,IAAI,eAAe,EAAE,CAAC;YACxD,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,EAAE,qBAAqB,CAAC,CAAC;YACnE,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;YAC/D,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kDAAkD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpI,CAAC;IAED,uBAAuB;IACvB,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACpD,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAClD,yBAAyB;QACzB,MAAM,kBAAkB,GAAG,gBAAU,CAAC,WAAW,EAAE,CAAC;QACpD,IAAI,kBAAkB,IAAI,OAAO,kBAAkB,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;YAC3E,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjD,CAAC;QACD,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAE/C,6BAA6B;QAC7B,MAAM,qBAAqB,GAAG,IAAI,iCAAqB,EAAE,CAAC;QAC1D,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,CAClF,CAAC;QACF,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC7C,MAAM,WAAW,GAAG,gCAAkB,CAAC,WAAW,EAAE,CAAC;QACrD,MAAM,iBAAiB,GAAG;YACxB,OAAO,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,GAAG,WAAuC,CAAC;gBACxD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;oBACpB,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,CAAC;YACH,CAAC;SACF,CAAC;QACF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5D,MAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAU,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC/E,IAAI,mBAAmB,EAAE,CAAC;YACxB,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,EAAE,uBAAU,CAAC,CAAC;QACrD,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACvD,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;IACnE,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uDAAuD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzI,CAAC;IAED,gEAAgE;IAChE,eAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;IAE3E,2CAA2C;IAC3C,IAAI,aAAa,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;IACzD,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACvD,MAAM,aAAa,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;QAC7D,aAAa,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC;QACvD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAChF,CAAC;QACD,mBAAmB,GAAG,IAAI,iDAAuB,CAC/C,eAAe,EACf,2BAAY,CAAC,QAAQ,EACrB,aAAa,EACb,aAAa,CACd,CAAC;QACF,gBAAgB,GAAG,IAAI,+CAAsB,CAC3C,eAAe,EACf,2BAAY,CAAC,QAAQ,EACrB,aAAa,EACb,mBAAmB,CACpB,CAAC;QACF,MAAM,gBAAgB,CAAC,UAAU,EAAE,CAAC;QACpC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAC;QACvE,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;QACjE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;YACzB,OAAO,EAAE,GAAG,EAAE;gBACZ,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBAC9B,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC7B,CAAC;SACF,CAAC,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IACtE,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uEAAuE,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzJ,CAAC;IAED,uBAAuB;IACvB,IAAI,wBAAwB,EAAE,WAAW,EAAE,UAAU,CAAC;IACtD,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACpD,MAAM,aAAa,GAAG,wDAAa,2BAA2B,GAAC,CAAC;QAChE,wBAAwB,GAAG,aAAa,CAAC,wBAAwB,CAAC;QAClE,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACxD,MAAM,aAAa,GAAG,wDAAa,6BAA6B,GAAC,CAAC;QAClE,WAAW,GAAG,IAAI,aAAa,CAAC,mBAAmB,EAAE,CAAC;QACtD,2BAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAChD,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,wDAAa,iCAAiC,GAAC,CAAC;QACrE,UAAU,GAAG,IAAI,YAAY,CAAC,uBAAuB,EAAE,CAAC;QACxD,2BAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC/C,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;IACzD,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uDAAuD,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzI,CAAC;IAED,oCAAoC;IACpC,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QAC7D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,gCAAgC,EAAE,CAAC,CAAC;QACjG,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,aAAa,GAAG,MAAM,IAAA,mCAAqB,GAAE,CAAC;YACpD,MAAM,cAAc,GAAG;gBACrB,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;gBAChD,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,kCAAkC;gBAC/C,IAAI,EAAE,YAAY;gBAClB,YAAY,EAAE,EAAE;gBAChB,WAAW,EAAE,aAAa,CAAC,QAAQ;gBACnC,QAAQ,EAAE,aAAa,CAAC,OAAO;gBAC/B,gBAAgB,EAAE,SAAS;gBAC3B,KAAK,EAAE,EAAE;aACV,CAAC;YACF,MAAM,aAAa,GAAG,IAAI,aAAK,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;YAChD,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,UAAU,SAAS,yBAAyB,CAAC,CAAC;QACrF,CAAC;IACH,CAAC,CAAC;IAEF,2CAA2C;IAC3C,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,MAAc,EAAE,EAAE;QAC5E,IAAI,CAAC;YACH,uEAAuE;YACvE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9G,eAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC,CAAC;IAEF,gDAAgD;IAChD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,CAAC,MAAc,EAAE,EAAE;QACjF,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,2BAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,MAAM,cAAc,CAAC,CAAC;gBACtE,OAAO;YACT,CAAC;YACD,mCAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9G,eAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC,CAAC,CAGH,CAAC;IACF,MAAM,gBAAgB,GAAG;QACvB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YACxE,IAAI,CAAC;gBACH,+CAA+C;gBAC/C,wBAAwB,CAAC,iBAAiB,CAAC;oBACzC,aAAa,EAAE,EAAE;oBACjB,qBAAqB,EAAE,EAAE;oBACzB,iBAAiB,EAAE,UAAU;oBAC7B,WAAW,EAAE,EAAE;oBACf,eAAe,EAAE,EAAE;iBACpB,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,4IAA4I,EAC5I,mBAAmB,EAAE,kBAAkB,CACxC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACjB,IAAI,SAAS,KAAK,mBAAmB,EAAE,CAAC;wBACtC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;oBACzD,CAAC;yBAAM,IAAI,SAAS,KAAK,kBAAkB,EAAE,CAAC;wBAC5C,MAAM,WAAW,GAAG,wBAAwB,CAAC,cAAc,EAAE,CAAC;wBAC9D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,uCAAuC,WAAW,CAAC,aAAa,SAAS,WAAW,CAAC,qBAAqB,aAAa,WAAW,CAAC,WAAW,GAAG,CAClJ,CAAC;oBACJ,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YACpE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,gDAAgD,CAAC,CAAC;oBACnF,OAAO;gBACT,CAAC;gBAED,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACvC,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC,6BAA6B,EAAE;oBACtE,MAAM,EAAE,6BAA6B;oBACrC,IAAI;iBACL,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBACpC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;oBAC7B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,uCAAuC,MAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,EAC/I,cAAc,EAAE,mBAAmB,CACpC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;wBACjB,IAAI,SAAS,KAAK,cAAc,EAAE,CAAC;4BACjC,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC;4BACjD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2CAA2C,CAAC,CAAC;wBACpF,CAAC;6BAAM,IAAI,SAAS,KAAK,mBAAmB,EAAE,CAAC;4BAC7C,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;wBACjE,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,MAAM,CAAC,KAAK,IAAI,eAAe,EAAE,CAAC,CAAC;gBAChG,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBAClD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YACzE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,kDAAkD,CAAC,CAAC;oBACrF,OAAO;gBACT,CAAC;gBAED,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACvC,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC,2BAA2B,EAAE;oBACpE,MAAM,EAAE,2BAA2B;oBACnC,IAAI;iBACL,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBACpC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;oBAC7B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,2CAA2C,MAAM,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,qCAAqC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,EAC/J,eAAe,EAAE,iBAAiB,CACnC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;wBACjB,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;4BAClC,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC;4BACvD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;wBAC7E,CAAC;6BAAM,IAAI,SAAS,KAAK,iBAAiB,EAAE,CAAC;4BAC3C,MAAM,UAAU,GAAG,MAAM,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,yBAAyB,CAAC;4BACtG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,UAAU,EAAE,CAAC,CAAC;wBAClF,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBAC3D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,CAAC,CAAC;YACrE,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YACxE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC9C,MAAM,EAAE,oCAAoC;oBAC5C,WAAW,EAAE,gEAAgE;iBAC9E,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM;oBAAE,OAAO;gBAEpB,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE;oBACvD,MAAM,EAAE,eAAe;oBACvB,MAAM;iBACP,CAAC,CAAC;gBAEH,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC9C,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;oBAC7B,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;wBACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;wBACzC,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;4BAC9B,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,2CAA2C,MAAM,CAAC,eAAe,OAAO,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC;wBACjI,CAAC,CAAC,CAAC;wBAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,+CAA+C,MAAM,CAAC,eAAe,mBAAmB,MAAM,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,SAAS,EAC/H,eAAe,CAChB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;4BACjB,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;gCAClC,MAAM,QAAQ,GAAG,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,uBAAuB,CAAC;gCACvF,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uBAAuB,QAAQ,EAAE,CAAC,CAAC;4BAC1E,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,MAAM,CAAC,KAAK,IAAI,eAAe,EAAE,CAAC,CAAC;gBACrG,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBACvD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;YACjE,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YACpE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,qDAAqD,CAAC,CAAC;oBACxF,OAAO;gBACT,CAAC;gBAED,MAAM,cAAc,GAAG,2BAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;gBACxE,IAAI,CAAC,cAAc,EAAE,CAAC;oBACpB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0CAA0C,CAAC,CAAC;oBAC3E,OAAO;gBACT,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBACjE,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM;oBACpC,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;oBAClC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,sCAAsC,WAAW,CAAC,iBAAiB,YAAY,WAAW,CAAC,sBAAsB,EAAE,MAAM,IAAI,CAAC,kBAAkB,WAAW,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,sBAAsB,EACjN,eAAe,EAAE,iBAAiB,CACnC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;wBACjB,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;4BAClC,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,WAAW,CAAC,CAAC;4BACrD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;wBAC7E,CAAC;6BAAM,IAAI,SAAS,KAAK,iBAAiB,EAAE,CAAC;4BAC3C,MAAM,UAAU,GAAG,WAAW,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,yBAAyB,CAAC;4BAC3G,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,UAAU,EAAE,CAAC,CAAC;wBAClF,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBACvD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;YACjE,CAAC;QACH,CAAC,CAAC;QAEF,6BAA6B;QAC7B,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC3E,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;oBACrD;wBACE,KAAK,EAAE,2BAA2B;wBAClC,WAAW,EAAE,+DAA+D;wBAC5E,MAAM,EAAE,sEAAsE;qBAC/E;oBACD;wBACE,KAAK,EAAE,8BAA8B;wBACrC,WAAW,EAAE,6DAA6D;wBAC1E,MAAM,EAAE,uDAAuD;qBAChE;oBACD;wBACE,KAAK,EAAE,8BAA8B;wBACrC,WAAW,EAAE,iDAAiD;wBAC9D,MAAM,EAAE,uDAAuD;qBAChE;oBACD;wBACE,KAAK,EAAE,kCAAkC;wBACzC,WAAW,EAAE,qCAAqC;wBAClD,MAAM,EAAE,4DAA4D;qBACrE;oBACD;wBACE,KAAK,EAAE,+BAA+B;wBACtC,WAAW,EAAE,kDAAkD;wBAC/D,MAAM,EAAE,6DAA6D;qBACtE;iBACF,EAAE;oBACD,WAAW,EAAE,gDAAgD;oBAC7D,KAAK,EAAE,6BAA6B;iBACrC,CAAC,CAAC;gBAEH,IAAI,CAAC,YAAY;oBAAE,OAAO;gBAE1B,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBACpD,MAAM,EAAE,yCAAyC;oBACjD,WAAW,EAAE,sBAAsB;oBACnC,KAAK,EAAE,+BAA+B;iBACvC,CAAC,CAAC;gBAEH,IAAI,CAAC,YAAY;oBAAE,OAAO;gBAE1B,8CAA8C;gBAC9C,MAAM,EAAE,gBAAgB,EAAE,GAAG,wDAAa,qCAAqC,GAAC,CAAC;gBACjF,MAAM,EAAE,QAAQ,EAAE,GAAG,wDAAa,mCAAmC,GAAC,CAAC;gBAEvE,yCAAyC;gBACzC,MAAM,gBAAgB,GAAG;oBACvB,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC5B,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,sBAAsB,YAAY,CAAC,KAAK,EAAE;oBACvD,OAAO,EAAE,OAAO;oBAChB,KAAK,EAAE,EAAE;oBACT,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,EAAE;oBACX,WAAW,EAAE,gBAAgB;iBAC9B,CAAC;gBAEF,6CAA6C;gBAC7C,MAAM,eAAe,GAAG;oBACtB,EAAE,EAAE,gBAAgB,CAAC,EAAE;oBACvB,IAAI,EAAE,gBAAgB,CAAC,IAAI;oBAC3B,WAAW,EAAE,gBAAgB,CAAC,WAAW;oBACzC,OAAO,EAAE,gBAAgB,CAAC,OAAO;oBACjC,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,EAAE;oBACT,WAAW,EAAE,gBAAgB;oBAC7B,aAAa,EAAE,YAAY,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAkB;oBACrF,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;iBACrD,CAAC;gBAEF,oCAAoC;gBACpC,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAEhD,8CAA8C;gBAC9C,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC3C,QAAQ,CAAC,sBAAsB,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC;gBACpF,CAAC;qBAAM,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClD,QAAQ,CAAC,sBAAsB,CAAC,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7D,CAAC;qBAAM,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACjD,QAAQ,CAAC,sBAAsB,CAAC,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7D,CAAC;qBAAM,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;oBACtD,QAAQ,CAAC,sBAAsB,CAAC,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC;gBACjE,CAAC;qBAAM,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;oBACnD,QAAQ,CAAC,sBAAsB,CAAC;wBAC9B,WAAW,EAAE,IAAI;wBACjB,eAAe,EAAE,IAAI;wBACrB,eAAe,EAAE,IAAI;wBACrB,mBAAmB,EAAE,IAAI;wBACzB,mBAAmB,EAAE,IAAI;qBAC1B,CAAC,CAAC;gBACL,CAAC;gBAED,wBAAwB;gBACxB,gBAAgB,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;gBAEnD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,yBAAyB,YAAY,+BAA+B,YAAY,CAAC,KAAK,gBAAgB,EACtG,qBAAqB,EAAE,eAAe,CACvC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACjB,IAAI,SAAS,KAAK,qBAAqB,EAAE,CAAC;wBACxC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAC;oBAC9D,CAAC;yBAAM,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;wBACzC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAA6B,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC;oBACrF,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;gBACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oCAAoC,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,CAAC;QACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,KAAK,EAAE,UAAmB,EAAE,EAAE;YAC3F,IAAI,CAAC;gBACH,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,uBAAuB;oBACvB,MAAM,EAAE,gBAAgB,EAAE,GAAG,wDAAa,qCAAqC,GAAC,CAAC;oBACjF,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;oBAClE,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,gDAAgD,EAChD,0BAA0B,CAC3B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;4BACjB,IAAI,SAAS,EAAE,CAAC;gCACd,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;4BACnE,CAAC;wBACH,CAAC,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;oBAaD,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CACvD,iBAAuD,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;wBAClE,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,IAAI,EAAE,CAAC;wBACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,qBAAqB,IAAI,EAAE,CAAC;wBACnF,OAAO;4BACL,KAAK,EAAE,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE;4BAC9K,WAAW,EAAE,EAAE,CAAC,WAAW,IAAI,EAAE;4BACjC,MAAM,EAAE,aAAa,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;iCAC1C,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;iCACzC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC;iCAC3B,IAAI,CAAC,IAAI,CAAC,EAAE;4BACf,UAAU,EAAE,EAAE,CAAC,EAAE;yBAClB,CAAC;oBACJ,CAAC,CAAC,EACF;wBACE,WAAW,EAAE,oCAAoC;wBACjD,KAAK,EAAE,0BAA0B;qBAClC,CACF,CAAC;oBACF,IAAI,CAAC,gBAAgB;wBAAE,OAAO;oBAC9B,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC;gBAC3C,CAAC;gBACD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,+DAA+D,EAC/D,eAAe,CAChB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACjB,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;wBAClC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAC;oBAC9D,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACxD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC;KACH,CAAC;IAEF,iCAAiC;IACjC,IAAI,OAAO,EAAE,CAAC;QACZ,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,iDAAiD;IACjD,IAAI,gBAA8C,CAAC;IACnD,IAAI,CAAC;QACH,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QACvE,MAAM,0BAA0B,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAC1E,kBAAkB,EAClB,gBAAgB,EAChB;YACE,cAAc,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE;SAClD,CACF,CAAC;QAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACvD,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IAC9D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QAChE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;IAC9H,CAAC;IAED,uEAAuE;IACvE,IAAI,gBAAgB,IAAI,qBAAqB,IAAI,eAAe,EAAE,CAAC;QACjE,IAAI,CAAC;YACH,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;YAC1C,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wDAAwD,EAAE,KAAK,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,oCAAoC;IACpC,MAAM,EAAE,qBAAqB,EAAE,GAAG,wDAAa,uBAAuB,GAAC,CAAC;IACxE,MAAM,EAAE,qBAAqB,EAAE,GAAG,wDAAa,sBAAsB,GAAC,CAAC;IAEvE,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAC/B,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAC/B,eAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;IAEhE,yBAAyB;IACzB,MAAM,YAAY,GAAG,IAAI,2BAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC5D,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CACvC,2BAAY,CAAC,QAAQ,EACrB,YAAY,CACb,CACF,CAAC;IAEF,kCAAkC;IAClC,MAAM,iBAAiB,GAAG,qCAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACjE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAE9C,yCAAyC;IACzC,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QACpE,IAAI,CAAC;YACH,MAAM,iBAAiB,CAAC,eAAe,EAAE,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;QAChE,CAAC;IACH,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAClE,IAAI,CAAC;YACH,iCAAe,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QACvE,IAAI,CAAC;YACH,MAAM,iBAAiB,CAAC,cAAc,EAAE,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;QACnE,CAAC;IACH,CAAC,CAAC,CACH,CAAC;IAEF,6BAA6B;IAC7B,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QACjE,IAAI,CAAC;YACH,MAAM,mCAAgB,CAAC,YAAY,CACjC,OAAO,EACP,OAAO,CAAC,YAAY,EACpB,aAAa,EACb,iCAAe,CAAC,WAAW,EAAE,CAC9B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAClE,CAAC;IACH,CAAC,CAAC;IAEF,yCAAyC;IACzC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;QACxE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChF,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAU,wBAAwB,EAAE,IAAI,CAAC,CAAC;YAEjH,MAAM,OAAO,GAAG;uBACD,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;8BACpB,cAAc;uBACrB,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAC9C,eAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAClE,CAAC;IACH,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;QACrE,IAAI,CAAC;YACH,MAAM,mCAAgB,CAAC,YAAY,CACjC,OAAO,EACP,OAAO,CAAC,YAAY,EACpB,aAAa,EACb,iCAAe,CAAC,WAAW,EAAE,CAC9B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;QACjE,CAAC;IACH,CAAC,CAAC,CACH,CAAC;IACF,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;IAEzD,iCAAiC;IACjC,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE;QAC/D,IAAI,CAAC;YACH,IAAA,mCAAgB,EAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;QACjE,CAAC;IACH,CAAC,CAAC,CACH,CAAC;IACF,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;IAE7D,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,0BAA0B,EAAE,wBAAwB,CAAC,CAAC;IACjF,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAC/D,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;IAClE,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;AACnE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger, logger } from './logger';\nimport { llmService } from './llm/llmService';\nimport { AgentManager } from './agents/agentUtilities/agentManager';\nimport { ToolRegistry } from './tools/toolRegistry';\nimport { workflowRegistry } from './agents/workflows/workflowRegistry';\nimport { MemoryManager } from './memory/memoryManager';\nimport { workflowManager } from './agents/workflows/workflowManager';\nimport { knowledgebaseManager } from './knowledge/knowledgebaseManager';\nimport { mcpManager } from './mcp/mcpManager';\nimport { promptManager } from './prompts/promptManager';\nimport { ChatViewProvider } from './ui/chat/chatViewProvider';\nimport receiverAgent from './agents/agentTypes/receiverAgent';\nimport { SupervisorAgent } from './agents/agentTypes/supervisorAgent';\nimport { createModeAgent } from './agents/agentUtilities/agentFactory';\nimport { openModeSelector } from './commands/openModeSelector';\nimport { registerModes } from './commands/registerModes';\n\nimport { operationModeRegistry } from './agents/agentModes/operationMode';\nimport { OperationMode } from './agents/workflows/types';\nimport { ITool } from './tools/tool.ts.backup';\n\nimport { DocumentationTool } from './tools/docsTool';\nimport { DirectoryListTool } from './tools/directoryListTool';\nimport { CodeSearchTool } from './tools/codeSearchTool';\nimport { TerminalCommandTool } from './tools/terminalCommandTool';\nimport { WebSearchTool } from './tools/webSearchTool';\nimport { WebReadTool } from './tools/webReadTool';\nimport { MemoryTool } from './tools/memoryTool';\nimport { BrowserPreviewTool } from './tools/browserPreviewTool';\nimport { DeployWebAppTool } from './tools/deployWebAppTool';\nimport { GitTool } from './tools/gitTool';\nimport { EditorActionsTool } from './tools/editorActionsTool';\nimport { CodeIntelligenceTool } from './tools/codeIntelligenceTool';\nimport { CodeGenerationTool } from './tools/codeGenerationTool';\nimport { LintDiagnosticsTool } from './tools/lintDiagnosticsTool';\nimport { EnhancedFileSystemTool } from './tools/enhancedFileTools';\nimport { MCPManager } from './mcp/mcp';\nimport { GoddessModeManager } from './goddess/goddessMode';\nimport { AllSettingsPanel } from './ui/settings/allSettingsPanel';\nimport { providerManager } from './llm/providerManager';\nimport { SettingsManager } from './ui/settings/settingsManager';\nimport { FileChangeTracker } from './ui/chat/fileChangeTracker';\nimport { FileChangePanel } from './ui/chat/fileChangePanel';\nimport { ProjectWideIntelligence } from './intelligence/projectWideIntelligence';\nimport { CrossRepositoryContext } from './intelligence/crossRepositoryContext';\nimport { AgentTreeDataProvider } from './ui/agents/agentTree';\nimport { TerminalView } from './ui/components/TerminalView';\n\nimport { Agent } from './agents/agentUtilities/agent';\nimport { getDefaultModelConfig } from './config/modelConfig';\nimport { WorkflowDefinition } from './agents/workflows/workflowEngine';\n\n// Code Action Integration\nimport { codeActionManager } from './tools/codeActionProvider';\n\ninterface WorkflowMetadata {\n  advancedFeatures?: {\n    goddessMode?: boolean;\n    quantumAnalysis?: boolean;\n    neuralSynthesis?: boolean;\n    timeTravelDebugging?: boolean;\n    [key: string]: boolean | undefined;\n  };\n  revolutionaryFeatures?: Record<string, boolean>;\n  [key: string]: unknown;\n}\n\ninterface Workflow extends WorkflowDefinition {\n  metadata?: WorkflowMetadata;\n}\n\ninterface WorkflowQuickPickItem extends vscode.QuickPickItem {\n  workflowId: string;\n}\nimport { ToolDetailsPanel } from './ui/tools/toolDetailsPanel';\n\n// This method is called when your extension is activated\nexport async function activate(context: vscode.ExtensionContext): Promise<void> {\n  Logger.initialize(context);\n  logger.info('Codessa extension activating...');\n\n  let toolRegistry: ToolRegistry;\n\n  // Initialize settings manager first\n  try {\n    const settingsManager = SettingsManager.getInstance();\n    await settingsManager.initialize();\n    logger.info('Settings manager initialized');\n  } catch (e) {\n    const errorMessage = 'Codessa: SettingsManager initialization failed: ' + (e instanceof Error ? e.message : String(e));\n    logger.error(errorMessage);\n    // Defer showing error message until after activation\n    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);\n  }\n\n  // Initialize LLM service\n  try {\n    llmService.initialize(context);\n    logger.info('LLM service initialized');\n  } catch (e) {\n    const errorMessage = 'Codessa: LLM service initialization failed: ' + (e instanceof Error ? e.message : String(e));\n    logger.error(errorMessage);\n    // Defer showing error message until after activation\n    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);\n  }\n\n  // Initialize memory manager\n  const memoryManager = new MemoryManager();\n  try {\n    await memoryManager.initialize(context);\n    logger.info('Memory manager initialized');\n  } catch (e) {\n    const errorMessage = 'Codessa: Memory manager initialization failed: ' + (e instanceof Error ? e.message : String(e));\n    logger.error(errorMessage);\n    // Defer showing error message until after activation\n    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);\n  }\n\n  // Initialize prompt manager\n  try {\n    await promptManager.initialize(context);\n    logger.info('Prompt manager initialized');\n  } catch (e) {\n    const errorMessage = 'Codessa: Prompt manager initialization failed: ' + (e instanceof Error ? e.message : String(e));\n    logger.error(errorMessage);\n    // Defer showing error message until after activation\n    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);\n  }\n\n  // Initialize knowledgebase manager\n  try {\n    await knowledgebaseManager.addKnowledge('Codessa is a powerful, agentic AI coding assistant for Visual Studio Code.');\n    logger.info('Knowledgebase manager initialized');\n  } catch (e) {\n    const errorMessage = 'Codessa: Knowledgebase manager initialization failed: ' + (e instanceof Error ? e.message : String(e));\n    logger.error(errorMessage);\n    // Defer showing error message until after activation\n    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);\n  }\n\n  // Initialize tool registry first since other components depend on it\n  try {\n    toolRegistry = await ToolRegistry.initialize(context);\n    logger.info('Tool registry initialized');\n  } catch (e) {\n    const errorMessage = 'Codessa: Tool registry initialization failed: ' + (e instanceof Error ? e.message : String(e));\n    logger.error(errorMessage);\n    vscode.window.showErrorMessage(errorMessage);\n    return;\n  }\n\n  // Register tools with standardized IDs - these will be updated to include schema properties\n  try {\n    toolRegistry.registerToolWithId(new DocumentationTool(), 'docs');\n    toolRegistry.registerToolWithId(new DirectoryListTool(), 'listDir');\n    toolRegistry.registerToolWithId(new CodeSearchTool(), 'codeSearch');\n    toolRegistry.registerToolWithId(new TerminalCommandTool(), 'terminal_command');\n    toolRegistry.registerToolWithId(new WebSearchTool(), 'webSearch');\n    toolRegistry.registerToolWithId(new WebReadTool(), 'webRead');\n    toolRegistry.registerToolWithId(new MemoryTool(memoryManager), 'memory_management');\n    toolRegistry.registerToolWithId(new BrowserPreviewTool(), 'browserPreview');\n    toolRegistry.registerToolWithId(new DeployWebAppTool(), 'deployWebApp');\n    toolRegistry.registerToolWithId(new GitTool(), 'git');\n    toolRegistry.registerToolWithId(new EditorActionsTool(), 'editor');\n    toolRegistry.registerToolWithId(new CodeIntelligenceTool(), 'codeIntel');\n    toolRegistry.registerToolWithId(new CodeGenerationTool(), 'codeGen');\n    toolRegistry.registerToolWithId(new LintDiagnosticsTool(), 'lint_diagnostics');\n    toolRegistry.registerToolWithId(new EnhancedFileSystemTool(), 'enhancedFile');\n  } catch (error) {\n    logger.warn('Some tools may not have proper schema definitions:', error);\n  }\n\n  // Verify all tools were registered successfully\n  const registeredTools = ToolRegistry.instance.getAllTools();\n  if (registeredTools.length === 0) {\n    logger.error('No tools were registered in the tool registry');\n  } else {\n    logger.info(`Successfully registered ${registeredTools.length} tools`);\n    // Log each registered tool\n    registeredTools.forEach((tool: ITool) => {\n      logger.info(`Registered tool: ${tool.id} (${tool.name})`);\n    });\n  }\n\n  // Store the tool registry instance in the extension context\n  context.subscriptions.push({\n    dispose: () => {\n      try {\n        // Clean up tools\n        const tools = toolRegistry.getAllTools();\n        tools.forEach(tool => {\n          if (tool.dispose) {\n            tool.dispose();\n          }\n        });\n      } catch (error) {\n        logger.error('Error cleaning up tools:', error);\n      }\n    }\n  });\n\n  // Initialize core services that other components depend on (after tool registration)\n  // Workflow registry\n  try {\n    await workflowRegistry.initialize();\n    logger.info('Workflow registry initialized');\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Workflow registry initialization failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Register operation modes\n  try {\n    await registerModes(context);\n    logger.info('Operation modes registered');\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Operation modes registration failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Initialize operation modes\n  try {\n    await operationModeRegistry.initializeModes(context);\n    logger.info('Operation modes initialized');\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Operation modes initialization failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Create receiver agent\n  let receiverAgentInstance;\n  try {\n    receiverAgentInstance = new receiverAgent({\n      id: 'receiver',\n      name: 'Receiver Agent',\n      systemPromptName: 'receiver',\n      capabilities: ['receive'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4'\n    });\n    logger.info('Receiver agent created');\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Receiver agent creation failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Set up agent context\n  let agentContext;\n  try {\n    const mcpManagerInstance = mcpManager;\n    agentContext = {\n      variables: {\n        workflowManager,\n        mcpManager: mcpManagerInstance,\n        promptManager,\n        knowledgebaseManager\n      }\n    };\n    if (receiverAgentInstance) {\n      receiverAgentInstance.setContext(agentContext);\n      const agentManager = AgentManager.getInstance();\n      agentManager.setReceiverAgent(receiverAgentInstance);\n      logger.info('Receiver agent initialized');\n    }\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Agent context setup failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Create supervisor agent\n  let supervisorAgent;\n  try {\n    supervisorAgent = new SupervisorAgent({\n      id: 'supervisor-agent',\n      name: 'Supervisor Agent',\n      description: 'Orchestrates specialized agents and workflows',\n      systemPromptName: 'supervisorAgent',\n      tools: ['enhancedFileSystem', 'codeSearch', 'memory', 'workflow']\n    }, receiverAgentInstance);\n    if (agentContext && supervisorAgent) {\n      supervisorAgent.setContext(agentContext);\n      const agentManager = AgentManager.getInstance();\n      agentManager.setSupervisorAgent(supervisorAgent);\n      logger.info('Supervisor agent initialized');\n    }\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Supervisor agent creation failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Create default operation mode agent\n  try {\n    const defaultMode = operationModeRegistry.getDefaultMode();\n    if (defaultMode) {\n      const modeAgent = createModeAgent(defaultMode.id, {\n        id: `mode_${defaultMode.id}`,\n        name: defaultMode.displayName,\n        systemPromptName: defaultMode.id\n      });\n      if (modeAgent) {\n        const agentManager = AgentManager.getInstance();\n        agentManager.addAgent(modeAgent);\n        logger.info(`Default mode agent (${defaultMode.id}) initialized`);\n      }\n    }\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Default mode agent creation failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Store agents in global state\n  try {\n    if (context && receiverAgentInstance && supervisorAgent) {\n      context.globalState.update('receiverAgent', receiverAgentInstance);\n      context.globalState.update('supervisorAgent', supervisorAgent);\n      logger.info('Multi-agent system initialized successfully');\n    }\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Storing agents in global state failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // VS Code integrations\n  try {\n    logger.info('Initializing VS Code integrations...');\n    logger.info('✅ Language Model integration ready');\n    // Initialize MCP Manager\n    const mcpManagerInstance = MCPManager.getInstance();\n    if (mcpManagerInstance && typeof mcpManagerInstance.dispose === 'function') {\n      context.subscriptions.push(mcpManagerInstance);\n    }\n    logger.info('✅ Enhanced MCP 2025 initialized');\n\n    // Initialize Agent Tree View\n    const agentTreeDataProvider = new AgentTreeDataProvider();\n    context.subscriptions.push(\n      vscode.window.registerTreeDataProvider('codessaAgentView', agentTreeDataProvider)\n    );\n    logger.info('✅ Agent tree view initialized');\n    const goddessMode = GoddessModeManager.getInstance();\n    const goddessDisposable = {\n      dispose: () => {\n        const manager = goddessMode as { dispose?: () => void };\n        if (manager.dispose) {\n          manager.dispose();\n        }\n      }\n    };\n    context.subscriptions.push(goddessDisposable);\n    const config = vscode.workspace.getConfiguration('codessa');\n    const autoActivateGoddess = config.get<boolean>('goddess.autoActivate', false);\n    if (autoActivateGoddess) {\n      goddessMode.activate();\n      logger.info('✅ Goddess Mode auto-activated');\n    } else {\n      logger.info('✅ Goddess Mode initialized (inactive)');\n    }\n    context.globalState.update('mcpManager', mcpManager);\n    context.globalState.update('goddessMode', goddessMode);\n    logger.info('🚀 VS Code integrations initialized successfully!');\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: VS Code integrations initialization failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // UI enhancements will be registered later to avoid duplication\n  logger.info('UI enhancements will be registered with tree view providers');\n\n  // Advanced memory and intelligence systems\n  let quantumMemory, projectIntelligence, crossRepoContext;\n  try {\n    logger.info('Initializing advanced memory systems...');\n    await memoryManager.initializeQuantumMemory(supervisorAgent);\n    quantumMemory = memoryManager.getQuantumMemorySystem();\n    if (!quantumMemory) {\n      throw new Error('Quantum memory system is required for project intelligence');\n    }\n    projectIntelligence = new ProjectWideIntelligence(\n      supervisorAgent,\n      ToolRegistry.instance,\n      memoryManager,\n      quantumMemory\n    );\n    crossRepoContext = new CrossRepositoryContext(\n      supervisorAgent,\n      ToolRegistry.instance,\n      quantumMemory,\n      projectIntelligence\n    );\n    await crossRepoContext.initialize();\n    context.globalState.update('projectIntelligence', projectIntelligence);\n    context.globalState.update('crossRepoContext', crossRepoContext);\n    context.subscriptions.push({\n      dispose: () => {\n        projectIntelligence.dispose();\n        crossRepoContext.dispose();\n      }\n    });\n    logger.info('🧠 Advanced memory systems initialized successfully!');\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Advanced memory/intelligence systems initialization failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Advanced AI features\n  let goddessPersonalityEngine, quantumTool, neuralTool;\n  try {\n    logger.info('Initializing advanced AI features...');\n    const goddessModule = await import('./personality/goddessMode');\n    goddessPersonalityEngine = goddessModule.goddessPersonalityEngine;\n    logger.info('✅ Goddess Personality Engine initialized');\n    const quantumModule = await import('./tools/quantumAnalysisTool');\n    quantumTool = new quantumModule.QuantumAnalysisTool();\n    ToolRegistry.instance.registerTool(quantumTool);\n    logger.info('✅ Quantum Analysis Tool registered');\n    const neuralModule = await import('./tools/neuralCodeSynthesisTool');\n    neuralTool = new neuralModule.NeuralCodeSynthesisTool();\n    ToolRegistry.instance.registerTool(neuralTool);\n    logger.info('✅ Neural Code Synthesis Tool registered');\n  } catch (e) {\n    vscode.window.showErrorMessage('Codessa: Advanced AI features initialization failed: ' + (e instanceof Error ? e.message : String(e)));\n  }\n\n  // Register codessa.addAgent command\n  context.subscriptions.push(\n    vscode.commands.registerCommand('codessa.addAgent', async () => {\n      const agentName = await vscode.window.showInputBox({ prompt: 'Enter a name for the new agent' });\n      if (agentName) {\n        const defaultConfig = await getDefaultModelConfig();\n        const newAgentConfig = {\n          id: agentName.toLowerCase().replace(/\\s+/g, '-'),\n          name: agentName,\n          description: 'A new agent created by the user.',\n          role: 'specialist',\n          capabilities: [],\n          llmProvider: defaultConfig.provider,\n          llmModel: defaultConfig.modelId,\n          systemPromptName: 'default',\n          tools: [],\n        };\n        const agentInstance = new Agent(newAgentConfig);\n        const agentManager = AgentManager.getInstance();\n        agentManager.addAgent(agentInstance);\n        vscode.window.showInformationMessage(`Agent '${agentName}' created successfully.`);\n      }\n    }),\n\n    // Register codessa.openToolDetails command\n    vscode.commands.registerCommand('codessa.openToolDetails', (toolId: string) => {\n      try {\n        // Forward to openToolDetailsPanel which already has the implementation\n        vscode.commands.executeCommand('codessa.openToolDetailsPanel', toolId);\n      } catch (error) {\n        const errorMessage = `Failed to open tool details: ${error instanceof Error ? error.message : String(error)}`;\n        logger.error(errorMessage);\n        vscode.window.showErrorMessage(errorMessage);\n      }\n    }),\n\n    // Register codessa.openToolDetailsPanel command\n    vscode.commands.registerCommand('codessa.openToolDetailsPanel', (toolId: string) => {\n      try {\n        const tool = ToolRegistry.getTool(toolId);\n        if (!tool) {\n          vscode.window.showErrorMessage(`Tool with ID '${toolId}' not found.`);\n          return;\n        }\n        ToolDetailsPanel.createOrShow(context.extensionUri, tool);\n      } catch (error) {\n        const errorMessage = `Failed to open tool details: ${error instanceof Error ? error.message : String(error)}`;\n        logger.error(errorMessage);\n        vscode.window.showErrorMessage(errorMessage);\n      }\n    }),\n\n    // Chat view provider will be registered later to avoid duplication\n  );\n  const advancedCommands = [\n    vscode.commands.registerCommand('codessa.activateGoddessMode', async () => {\n      try {\n        // Update goddess personality to be more active\n        goddessPersonalityEngine.updatePersonality({\n          adaptiveLevel: 90,\n          emotionalIntelligence: 95,\n          motivationalStyle: 'adaptive',\n          wisdomLevel: 95,\n          creativityLevel: 90\n        });\n\n        vscode.window.showInformationMessage(\n          '✨ Goddess Mode Activated! I am now Codessa, your divine coding companion with enhanced emotional intelligence and adaptive personality! 👑',\n          'Chat with Goddess', 'View Personality'\n        ).then(selection => {\n          if (selection === 'Chat with Goddess') {\n            vscode.commands.executeCommand('codessa.openChatView');\n          } else if (selection === 'View Personality') {\n            const personality = goddessPersonalityEngine.getPersonality();\n            vscode.window.showInformationMessage(\n              `Goddess Personality: Adaptive Level ${personality.adaptiveLevel}%, EQ ${personality.emotionalIntelligence}%, Wisdom ${personality.wisdomLevel}%`\n            );\n          }\n        });\n      } catch (error) {\n        logger.error('Error activating Goddess Mode:', error);\n        vscode.window.showErrorMessage('Failed to activate Goddess Mode');\n      }\n    }),\n\n    vscode.commands.registerCommand('codessa.quantumAnalysis', async () => {\n      try {\n        const editor = vscode.window.activeTextEditor;\n        if (!editor) {\n          vscode.window.showWarningMessage('Please open a file to perform quantum analysis');\n          return;\n        }\n\n        const code = editor.document.getText();\n        const result = await quantumTool.execute('quantum_pattern_recognition', {\n          action: 'quantum_pattern_recognition',\n          code\n        });\n\n        if (result.success && result.output) {\n          const output = result.output;\n          vscode.window.showInformationMessage(\n            `🔬 Quantum Analysis Complete: Found ${output.patterns?.length || 0} quantum patterns with ${(output.confidence * 100).toFixed(1)}% confidence`,\n            'View Details', 'Run Parallel Test'\n          ).then(selection => {\n            if (selection === 'View Details') {\n              logger.info('Quantum Analysis Results:', result);\n              vscode.window.showInformationMessage('Quantum analysis details logged to output');\n            } else if (selection === 'Run Parallel Test') {\n              vscode.commands.executeCommand('codessa.parallelUniverseTest');\n            }\n          });\n        } else {\n          vscode.window.showErrorMessage(`Quantum analysis failed: ${result.error || 'Unknown error'}`);\n        }\n      } catch (error) {\n        logger.error('Error in quantum analysis:', error);\n        vscode.window.showErrorMessage('Quantum analysis failed');\n      }\n    }),\n\n    vscode.commands.registerCommand('codessa.parallelUniverseTest', async () => {\n      try {\n        const editor = vscode.window.activeTextEditor;\n        if (!editor) {\n          vscode.window.showWarningMessage('Please open a file to test in parallel universes');\n          return;\n        }\n\n        const code = editor.document.getText();\n        const result = await quantumTool.execute('parallel_universe_testing', {\n          action: 'parallel_universe_testing',\n          code\n        });\n\n        if (result.success && result.output) {\n          const output = result.output;\n          vscode.window.showInformationMessage(\n            `🌌 Parallel Universe Testing: Tested in ${output.universes?.length || 0} universes. Quantum interference: ${output.quantumInterference ? 'Detected' : 'None'}`,\n            'View Timeline', 'See Predictions'\n          ).then(selection => {\n            if (selection === 'View Timeline') {\n              logger.info('Parallel Universe Test Results:', result);\n              vscode.window.showInformationMessage('Timeline analysis logged to output');\n            } else if (selection === 'See Predictions') {\n              const mitigation = output.mitigationStrategies?.slice(0, 3).join('\\n• ') || 'No strategies available';\n              vscode.window.showInformationMessage(`Mitigation Strategies:\\n• ${mitigation}`);\n            }\n          });\n        }\n      } catch (error) {\n        logger.error('Error in parallel universe testing:', error);\n        vscode.window.showErrorMessage('Parallel universe testing failed');\n      }\n    }),\n\n    vscode.commands.registerCommand('codessa.neuralCodeSynthesis', async () => {\n      try {\n        const prompt = await vscode.window.showInputBox({\n          prompt: 'Enter your code generation request',\n          placeHolder: 'e.g., Create a function to sort an array using neural networks'\n        });\n\n        if (!prompt) return;\n\n        const result = await neuralTool.execute('generate_code', {\n          action: 'generate_code',\n          prompt\n        });\n\n        const editor = vscode.window.activeTextEditor;\n        if (editor && result.success && result.output) {\n          const output = result.output;\n          if (output.generatedCode) {\n            const position = editor.selection.active;\n            await editor.edit(editBuilder => {\n              editBuilder.insert(position, `\\n// Neural-generated code (Creativity: ${output.creativityScore}%)\\n${output.generatedCode}\\n`);\n            });\n\n            vscode.window.showInformationMessage(\n              `🧠 Neural Code Generated! Creativity Score: ${output.creativityScore}%, Neural Path: ${output.neuralPath?.length || 0} layers`,\n              'View Insights'\n            ).then(selection => {\n              if (selection === 'View Insights') {\n                const insights = output.consciousnessInsights?.join('\\n• ') || 'No insights available';\n                vscode.window.showInformationMessage(`Neural Insights:\\n• ${insights}`);\n              }\n            });\n          }\n        } else {\n          vscode.window.showErrorMessage(`Neural code synthesis failed: ${result.error || 'Unknown error'}`);\n        }\n      } catch (error) {\n        logger.error('Error in neural code synthesis:', error);\n        vscode.window.showErrorMessage('Neural code synthesis failed');\n      }\n    }),\n\n    vscode.commands.registerCommand('codessa.timeTravelDebug', async () => {\n      try {\n        const editor = vscode.window.activeTextEditor;\n        if (!editor) {\n          vscode.window.showWarningMessage('Please open a file to perform time-travel debugging');\n          return;\n        }\n\n        const timeTravelTool = ToolRegistry.instance.getTool('timeTravelDebug');\n        if (!timeTravelTool) {\n          vscode.window.showErrorMessage('Time-travel debugging tool not available');\n          return;\n        }\n\n        const result = await timeTravelTool.execute('predictFutureIssues', {\n          filePath: editor.document.uri.fsPath,\n          timeHorizon: '3-months'\n        });\n\n        if (result.success) {\n          const predictions = result.output;\n          vscode.window.showInformationMessage(\n            `⏰ Time-Travel Analysis: Risk Score ${predictions.combinedRiskScore}%, Found ${predictions.traditionalPredictions?.length || 0} traditional + ${predictions.quantumPredictions?.length || 0} quantum predictions`,\n            'View Timeline', 'See Predictions'\n          ).then(selection => {\n            if (selection === 'View Timeline') {\n              logger.info('Time-travel predictions:', predictions);\n              vscode.window.showInformationMessage('Timeline analysis logged to output');\n            } else if (selection === 'See Predictions') {\n              const mitigation = predictions.mitigationStrategies?.slice(0, 3).join('\\n• ') || 'No strategies available';\n              vscode.window.showInformationMessage(`Mitigation Strategies:\\n• ${mitigation}`);\n            }\n          });\n        }\n      } catch (error) {\n        logger.error('Error in time-travel debugging:', error);\n        vscode.window.showErrorMessage('Time-travel debugging failed');\n      }\n    }),\n\n    // Advanced Workflow Commands\n    vscode.commands.registerCommand('codessa.createAdvancedWorkflow', async () => {\n      try {\n        const workflowType = await vscode.window.showQuickPick([\n          {\n            label: '✨ Goddess-Guided Workflow',\n            description: 'Enhanced with divine coding wisdom and emotional intelligence',\n            detail: 'Perfect for complex development tasks requiring guidance and support'\n          },\n          {\n            label: '🔬 Quantum-Enhanced Workflow',\n            description: 'Leverages quantum-inspired algorithms for superior analysis',\n            detail: 'Ideal for code analysis and pattern recognition tasks'\n          },\n          {\n            label: '🧠 Neural Synthesis Workflow',\n            description: 'Brain-inspired code generation and optimization',\n            detail: 'Best for code generation and creative problem-solving'\n          },\n          {\n            label: '⏰ Time-Travel Debugging Workflow',\n            description: 'Predicts and prevents future issues',\n            detail: 'Essential for maintaining code quality and preventing bugs'\n          },\n          {\n            label: '🚀 Ultimate Advanced Workflow',\n            description: 'Combines all advanced features for maximum power',\n            detail: 'The most advanced workflow with all AI capabilities enabled'\n          }\n        ], {\n          placeHolder: 'Select the type of advanced workflow to create',\n          title: '🚀 Create Advanced Workflow'\n        });\n\n        if (!workflowType) return;\n\n        const workflowName = await vscode.window.showInputBox({\n          prompt: 'Enter a name for your advanced workflow',\n          placeHolder: 'My Advanced Workflow',\n          title: '✨ Name Your Advanced Workflow'\n        });\n\n        if (!workflowName) return;\n\n        // Create advanced workflow based on selection\n        const { workflowRegistry } = await import('./agents/workflows/workflowRegistry');\n        const { Workflow } = await import('./agents/workflows/workflowEngine');\n\n        // Create an advanced workflow definition\n        const advancedWorkflow = {\n          id: `advanced-${Date.now()}`,\n          name: workflowName,\n          description: `Advanced workflow: ${workflowType.label}`,\n          version: '1.0.0',\n          steps: [],\n          inputs: [],\n          outputs: [],\n          startStepId: 'advanced-start'\n        };\n\n        // Create a graph definition for registration\n        const graphDefinition = {\n          id: advancedWorkflow.id,\n          name: advancedWorkflow.name,\n          description: advancedWorkflow.description,\n          version: advancedWorkflow.version,\n          nodes: [],\n          edges: [],\n          startNodeId: 'advanced-start',\n          operationMode: workflowType.label.toLowerCase().replace(/\\s+/g, '-') as OperationMode,\n          tags: ['advanced', workflowType.label.toLowerCase()]\n        };\n\n        // Create and configure the workflow\n        const workflow = new Workflow(advancedWorkflow);\n\n        // Enable advanced features based on selection\n        if (workflowType.label.includes('Goddess')) {\n          workflow.enableAdvancedFeatures({ goddessMode: true, adaptivePersonality: true });\n        } else if (workflowType.label.includes('Quantum')) {\n          workflow.enableAdvancedFeatures({ quantumAnalysis: true });\n        } else if (workflowType.label.includes('Neural')) {\n          workflow.enableAdvancedFeatures({ neuralSynthesis: true });\n        } else if (workflowType.label.includes('Time-Travel')) {\n          workflow.enableAdvancedFeatures({ timeTravelDebugging: true });\n        } else if (workflowType.label.includes('Ultimate')) {\n          workflow.enableAdvancedFeatures({\n            goddessMode: true,\n            quantumAnalysis: true,\n            neuralSynthesis: true,\n            timeTravelDebugging: true,\n            adaptivePersonality: true\n          });\n        }\n\n        // Register the workflow\n        workflowRegistry.registerWorkflow(graphDefinition);\n\n        vscode.window.showInformationMessage(\n          `🚀 Advanced workflow \"${workflowName}\" created successfully with ${workflowType.label} capabilities!`,\n          'Open Workflow Panel', 'Test Workflow'\n        ).then(selection => {\n          if (selection === 'Open Workflow Panel') {\n            vscode.commands.executeCommand('codessa.openWorkflowPanel');\n          } else if (selection === 'Test Workflow') {\n            vscode.commands.executeCommand('codessa.runAdvancedWorkflow', advancedWorkflow.id);\n          }\n        });\n      } catch (error) {\n        logger.error('Error creating advanced workflow:', error);\n        vscode.window.showErrorMessage('Failed to create advanced workflow');\n      }\n    }),\n    vscode.commands.registerCommand('codessa.runAdvancedWorkflow', async (workflowId?: string) => {\n      try {\n        if (!workflowId) {\n          // Show workflow picker\n          const { workflowRegistry } = await import('./agents/workflows/workflowRegistry');\n          const advancedWorkflows = workflowRegistry.getAdvancedWorkflows();\n          if (advancedWorkflows.length === 0) {\n            vscode.window.showInformationMessage(\n              'No advanced workflows found. Create one first!',\n              'Create Advanced Workflow'\n            ).then(selection => {\n              if (selection) {\n                vscode.commands.executeCommand('codessa.createAdvancedWorkflow');\n              }\n            });\n            return;\n          }\n          // Define workflow interface to match the expected structure\n          interface WorkflowWithMetadata {\n            id: string;\n            name: string;\n            description: string;\n            metadata?: {\n              advancedFeatures?: Record<string, boolean>;\n              revolutionaryFeatures?: Record<string, boolean>;\n              [key: string]: any;\n            };\n          }\n\n          const selectedWorkflow = await vscode.window.showQuickPick(\n            (advancedWorkflows as unknown as WorkflowWithMetadata[]).map((wf) => {\n              const metadata = wf.metadata || {};\n              const features = metadata.advancedFeatures || metadata.revolutionaryFeatures || {};\n              return {\n                label: `${features.goddessMode ? '✨' : ''}${features.quantumAnalysis ? '🔬' : ''}${features.neuralSynthesis ? '🧠' : ''}${features.timeTravelDebugging ? '⏰' : ''} ${wf.name}`,\n                description: wf.description || '',\n                detail: `Features: ${Object.entries(features)\n                  .filter(([, enabled]) => Boolean(enabled))\n                  .map(([feature]) => feature)\n                  .join(', ')}`,\n                workflowId: wf.id\n              };\n            }),\n            {\n              placeHolder: 'Select an advanced workflow to run',\n              title: '🚀 Run Advanced Workflow'\n            }\n          );\n          if (!selectedWorkflow) return;\n          workflowId = selectedWorkflow.workflowId;\n        }\n        vscode.window.showInformationMessage(\n          '🚀 Running advanced workflow with enhanced AI capabilities...',\n          'View Progress'\n        ).then(selection => {\n          if (selection === 'View Progress') {\n            vscode.commands.executeCommand('codessa.openWorkflowPanel');\n          }\n        });\n      } catch (error) {\n        logger.error('Error running advanced workflow:', error);\n        vscode.window.showErrorMessage('Failed to run advanced workflow');\n      }\n    })\n  ];\n  \n  // Register all advanced commands\n  if (context) {\n    advancedCommands.forEach(command => context.subscriptions.push(command));\n  }\n\n  // Register ChatViewProvider for the chat sidebar\n  let chatViewProvider: ChatViewProvider | undefined;\n  try {\n    chatViewProvider = new ChatViewProvider(context.extensionUri, context);\n    const chatViewProviderDisposable = vscode.window.registerWebviewViewProvider(\n      'codessa.chatView',\n      chatViewProvider,\n      {\n        webviewOptions: { retainContextWhenHidden: true }\n      }\n    );\n\n    context.subscriptions.push(chatViewProviderDisposable);\n    logger.info('✅ Chat view provider registered successfully');\n  } catch (error) {\n    logger.error('❌ Failed to register chat view provider:', error);\n    vscode.window.showErrorMessage(`Failed to register chat view: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n\n  // Refresh agent references in ChatViewProvider after agents are set up\n  if (chatViewProvider && receiverAgentInstance && supervisorAgent) {\n    try {\n      chatViewProvider.refreshAgentReferences();\n      logger.info('✅ ChatViewProvider agent references refreshed');\n    } catch (error) {\n      logger.error('❌ Failed to refresh ChatViewProvider agent references:', error);\n    }\n  }\n\n  // Register tree view data providers\n  const { registerAgentTreeView } = await import('./ui/agents/agentTree');\n  const { registerToolsTreeView } = await import('./ui/tools/toolsView');\n\n  registerAgentTreeView(context);\n  registerToolsTreeView(context);\n  logger.info('Tree view data providers registered successfully');\n\n  // Register terminal view\n  const terminalView = new TerminalView(context.extensionUri);\n  context.subscriptions.push(\n    vscode.window.registerWebviewViewProvider(\n      TerminalView.viewType,\n      terminalView\n    )\n  );\n\n  // Initialize file change tracking\n  const fileChangeTracker = FileChangeTracker.getInstance(context);\n  context.subscriptions.push(fileChangeTracker);\n\n  // Register file change tracking commands\n  context.subscriptions.push(\n    vscode.commands.registerCommand('codessa.showFileChanges', async () => {\n      try {\n        await fileChangeTracker.showFileChanges();\n      } catch (error) {\n        logger.error('Failed to show file changes:', error);\n        vscode.window.showErrorMessage('Failed to show file changes');\n      }\n    }),\n\n    vscode.commands.registerCommand('codessa.openFileChangePanel', () => {\n      try {\n        FileChangePanel.createOrShow(context.extensionUri, context);\n      } catch (error) {\n        logger.error('Failed to open file change panel:', error);\n        vscode.window.showErrorMessage('Failed to open file change panel');\n      }\n    }),\n\n    vscode.commands.registerCommand('codessa.refreshFileChanges', async () => {\n      try {\n        await fileChangeTracker.refreshChanges();\n        vscode.window.showInformationMessage('File changes refreshed');\n      } catch (error) {\n        logger.error('Failed to refresh file changes:', error);\n        vscode.window.showErrorMessage('Failed to refresh file changes');\n      }\n    })\n  );\n\n  // Register settings commands\n  context.subscriptions.push(\n    vscode.commands.registerCommand('codessa.openSettings', async () => {\n      try {\n        await AllSettingsPanel.createOrShow(\n          context,\n          context.extensionUri,\n          memoryManager,\n          providerManager.getInstance()\n        );\n      } catch (error) {\n        logger.error('Failed to open settings panel:', error);\n        vscode.window.showErrorMessage('Failed to open settings panel');\n      }\n    }),\n\n    // Debug command to check provider status\n    vscode.commands.registerCommand('codessa.debugProviderStatus', async () => {\n      try {\n        const config = vscode.workspace.getConfiguration('codessa.llm');\n        const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n        const suppressErrors = vscode.workspace.getConfiguration('codessa').get<boolean>('suppressProviderErrors', true);\n\n        const message = `Provider Status Debug:\n- Enabled Providers: ${enabledProviders.join(', ')}\n- Suppress Provider Errors: ${suppressErrors}\n- LM Studio Enabled: ${enabledProviders.includes('lmstudio')}`;\n\n        vscode.window.showInformationMessage(message);\n        logger.info(message);\n      } catch (error) {\n        logger.error('Failed to get provider status:', error);\n        vscode.window.showErrorMessage('Failed to get provider status');\n      }\n    }),\n    vscode.commands.registerCommand('codessa.openSettingsView', async () => {\n      try {\n        await AllSettingsPanel.createOrShow(\n          context,\n          context.extensionUri,\n          memoryManager,\n          providerManager.getInstance()\n        );\n      } catch (error) {\n        logger.error('Failed to open settings view:', error);\n        vscode.window.showErrorMessage('Failed to open settings view');\n      }\n    })\n  );\n  logger.info('Settings commands registered successfully');\n\n  // Register mode selector command\n  context.subscriptions.push(\n    vscode.commands.registerCommand('codessa.openModeSelector', () => {\n      try {\n        openModeSelector(context);\n      } catch (error) {\n        logger.error('Failed to open mode selector:', error);\n        vscode.window.showErrorMessage('Failed to open mode selector');\n      }\n    })\n  );\n  logger.info('Mode selector command registered successfully');\n\n  context.globalState.update('goddessPersonalityEngine', goddessPersonalityEngine);\n  context.globalState.update('quantumAnalysisTool', quantumTool);\n  context.globalState.update('neuralCodeSynthesisTool', neuralTool);\n  logger.info('🚀 Advanced AI features initialized successfully!');\n}\n"]}