/**
 * Codessa workflow implementation
 *
 * Exports types, graph implementation, registry, memory implementation, and
 * various templates for creating Codessa workflows.
 */

// Export types
export * from './types';

// Export graph implementation
export * from './graph';

// Export registry
export * from './workflowRegistry';

// Export memory implementation
export * from './memory';

// Export templates (excluding createMemoryEnhancedWorkflow to avoid ambiguity)
export {
  createChatWorkflow,
  createReActWorkflow,
  createMultiAgentWorkflow,
  createTDDWorkflow,
  createSprintPlanningWorkflow
} from './templates';

// Export advanced templates
export * from './advancedTemplates';

// Export specialized templates
// Use explicit re-exports to avoid naming conflicts
export {
  // Explicitly re-export functions with unique names to avoid conflicts
  createCodeRefactoringWorkflow,
  createDebuggingWorkflow,
  createAskWorkflow,
  createEditWorkflow,
  createCodeGenWorkflow,
  createAgenticWorkflow,
  createUXDesignWorkflow,
  createDevOpsWorkflow,
  createAgileWorkflow,
  createXPWorkflow,
  createScrumWorkflow,
  WorkflowFactory
} from './specializedTemplates';

// Export PR workflows
export {
  createPRCreationWorkflow,
  createPRReviewWorkflow
} from './prWorkflows';

// Export checkpoint workflow
export { createCheckpointWorkflow } from './checkpointWorkflow';

// Re-export checkpoint manager from its new location
export { checkpointManager } from '../../checkpoint';
export type { CheckpointManager, Checkpoint } from '../../checkpoint';

// Export MCP workflow
export { createMCPWorkflow } from './mcpWorkflow';

// Re-export MCP manager from its new location
export { mcpManager } from '../../mcp';
export type { MCPManager, MCPContext } from '../../mcp';

// Export advanced refactoring workflows
export {
  createPatternRefactoringWorkflow,
  createTechnicalDebtWorkflow
} from './advancedRefactoring';

// Export vector stores
export * from './vectorStores';

// Export polyfills
export * from './corePolyfill';
