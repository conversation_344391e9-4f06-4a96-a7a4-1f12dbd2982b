{"version": 3, "file": "databaseFactory.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/databases/databaseFactory.ts"], "names": [], "mappings": ";;;AACA,qDAAkD;AAClD,yDAAsD;AACtD,mDAAgD;AAChD,uDAAoD;AACpD,mDAAgD;AAChD,4CAAyC;AAEzC;;GAEG;AACH,MAAa,eAAe;IAC1B;;;;SAIK;IACE,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,IAAY;QAC7C,eAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;QAElD,QAAQ,IAAI,EAAE,CAAC;YACf,KAAK,QAAQ;gBACX,OAAO,IAAI,+BAAc,EAAE,CAAC;YAC9B,KAAK,OAAO;gBACV,OAAO,IAAI,6BAAa,EAAE,CAAC;YAC7B,KAAK,UAAU;gBACb,OAAO,IAAI,mCAAgB,EAAE,CAAC;YAChC,KAAK,SAAS;gBACZ,OAAO,IAAI,iCAAe,EAAE,CAAC;YAC/B,KAAK,OAAO;gBACV,OAAO,IAAI,6BAAa,EAAE,CAAC;YAC7B;gBACE,eAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,mCAAmC,CAAC,CAAC;gBAC/E,OAAO,IAAI,+BAAc,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;CACF;AAzBD,0CAyBC", "sourcesContent": ["import { IDatabase } from '../../types';\nimport { SQLiteDatabase } from './sqliteDatabase';\nimport { PostgresDatabase } from './postgresDatabase';\nimport { MySQLDatabase } from './mysqlDatabase';\nimport { MongoDBDatabase } from './mongodbDatabase';\nimport { RedisDatabase } from './redisDatabase';\nimport { logger } from '../../../logger';\n\n/**\n * Factory for creating databases\n */\nexport class DatabaseFactory {\n  /**\n     * Create a database\n     * @param type Database type\n     * @returns Database instance\n     */\n  public static async createDatabase(type: string): Promise<IDatabase> {\n    logger.info(`Creating database of type: ${type}`);\n\n    switch (type) {\n    case 'sqlite':\n      return new SQLiteDatabase();\n    case 'mysql':\n      return new MySQLDatabase();\n    case 'postgres':\n      return new PostgresDatabase();\n    case 'mongodb':\n      return new MongoDBDatabase();\n    case 'redis':\n      return new RedisDatabase();\n    default:\n      logger.warn(`Unknown database type: ${type}, falling back to SQLite database`);\n      return new SQLiteDatabase();\n    }\n  }\n}\n"]}