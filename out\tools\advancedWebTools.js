"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebDeployStatusTool = exports.WebSnapshotTool = exports.WebContentExtractTool = exports.WebMultiSearchTool = void 0;
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
class WebMultiSearchTool {
    id = 'webMultiSearch';
    name = 'Web Multi-Search';
    description = 'Performs a web search using multiple providers (DuckDuckGo, Bing, Google).';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        query: zod_1.z.string().describe('Search query.')
    });
    inputSchema = {
        type: 'object',
        properties: { query: { type: 'string', description: 'Search query.' } },
        required: ['query']
    };
    async execute(actionName, input, _context) {
        const query = input.query;
        if (!query) {
            return { success: false, error: '\'query\' is required.', toolId: this.id, actionName };
        }
        const results = {};
        // DuckDuckGo
        try {
            const ddg = await (0, axios_1.default)({ method: 'get', url: `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1` });
            results['DuckDuckGo'] = ddg.data.AbstractText || ddg.data.Answer || ddg.data.RelatedTopics?.[0]?.Text || 'No answer.';
        }
        catch (error) {
            results['DuckDuckGo'] = `Error: ${error.message || 'Unknown error'}`;
        }
        // Bing (placeholder)
        results['Bing'] = 'Bing search integration not implemented.';
        // Google (placeholder)
        results['Google'] = 'Google search integration not implemented.';
        return { success: true, output: results, toolId: this.id, actionName };
    }
}
exports.WebMultiSearchTool = WebMultiSearchTool;
class WebContentExtractTool {
    id = 'webExtract';
    name = 'Web Content Extract';
    description = 'Extracts main content (article, text) from a web page.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        url: zod_1.z.string().describe('URL to extract content from.')
    });
    inputSchema = {
        type: 'object',
        properties: { url: { type: 'string', description: 'URL to extract content from.' } },
        required: ['url']
    };
    async execute(actionName, input, _context) {
        const url = input.url;
        if (!url) {
            return { success: false, error: '\'url\' is required.', toolId: this.id, actionName };
        }
        // Placeholder: In real implementation, use a library like mercury-parser or readability
        return { success: true, output: `Extracted content from: ${url}`, toolId: this.id, actionName };
    }
}
exports.WebContentExtractTool = WebContentExtractTool;
class WebSnapshotTool {
    id = 'webSnapshot';
    name = 'Web Page Snapshot';
    description = 'Take a snapshot (HTML or screenshot) of a web page.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        url: zod_1.z.string().describe('URL to snapshot.')
    });
    inputSchema = {
        type: 'object',
        properties: { url: { type: 'string', description: 'URL to snapshot.' } },
        required: ['url']
    };
    async execute(actionName, input, _context) {
        const url = input.url;
        if (!url) {
            return { success: false, error: '\'url\' is required.', toolId: this.id, actionName };
        }
        // Placeholder: Actual screenshot would require headless browser integration
        return { success: true, output: `Snapshot data for: ${url}`, toolId: this.id, actionName };
    }
}
exports.WebSnapshotTool = WebSnapshotTool;
class WebDeployStatusTool {
    id = 'deployStatus';
    name = 'Web Deploy Status';
    description = 'Check the deployment status of a web app.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        deploymentId: zod_1.z.string().describe('Deployment ID or URL.')
    });
    inputSchema = {
        type: 'object',
        properties: { deploymentId: { type: 'string', description: 'Deployment ID or URL.' } },
        required: ['deploymentId']
    };
    async execute(actionName, input, _context) {
        const deploymentId = input.deploymentId;
        if (!deploymentId) {
            return { success: false, error: '\'deploymentId\' is required.', toolId: this.id, actionName };
        }
        // Placeholder: In real implementation, check deployment provider API
        return { success: true, output: `Deployment status for: ${deploymentId}`, toolId: this.id, actionName };
    }
}
exports.WebDeployStatusTool = WebDeployStatusTool;
//# sourceMappingURL=advancedWebTools.js.map