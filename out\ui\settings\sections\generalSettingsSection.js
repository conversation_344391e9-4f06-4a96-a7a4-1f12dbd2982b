"use strict";
// General section logic and rendering
Object.defineProperty(exports, "__esModule", { value: true });
exports.renderGeneralSettingsSection = renderGeneralSettingsSection;
/**
 * Renders the General settings section in the settings UI.
 * @param container - The HTMLElement where the section should be rendered
 * @param settings - An object containing the current settings values and callbacks
 */
function renderGeneralSettingsSection(container, settings = {}) {
    // Default values if not provided
    const { theme = 'system', language = 'en', autoSave = true, perAgentLLMEnabled = false, onChange = (key, value) => {
        console.log(`Setting ${key} changed to:`, value);
    }, onSave = () => {
        console.log('Settings saved');
    }, onReset = () => {
        console.log('Settings reset');
    } } = settings;
    container.innerHTML = `
        <form class="general-settings-form" autocomplete="off" style="max-width: 420px; margin: 0 auto;">
            <h2 style="margin-bottom: 1em;">General Settings</h2>
            
            <div class="form-group" style="margin-bottom: 1em;">
                <label style="font-weight:500;">
                    <input type="checkbox" id="general-peragentllm-checkbox" style="margin-right:0.5em;">
                    Allow each agent to use its own LLM provider/model
                </label>
                <div class="setting-description" style="font-size: 0.9em; color: #666; margin-top: 0.25em;">
                    When enabled, each agent can be configured with its own AI provider and model
                </div>
            </div>

            <div class="form-group" style="margin-bottom: 1em;">
                <label for="general-theme-select" style="display:block; font-weight:500;">Theme</label>
                <select id="general-theme-select" class="input" style="width:100%; padding:0.5em;">
                    <option value="system">System Default</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                </select>
                <div class="setting-description" style="font-size: 0.9em; color: #666; margin-top: 0.25em;">
                    Choose the visual theme for the extension interface
                </div>
            </div>

            <div class="form-group" style="margin-bottom: 1em;">
                <label for="general-language-select" style="display:block; font-weight:500;">Language</label>
                <select id="general-language-select" class="input" style="width:100%; padding:0.5em;">
                    <option value="en">English</option>
                    <option value="de">German</option>
                    <option value="fr">French</option>
                    <option value="es">Spanish</option>
                    <option value="zh">Chinese</option>
                </select>
                <div class="setting-description" style="font-size: 0.9em; color: #666; margin-top: 0.25em;">
                    Select the language for the extension interface
                </div>
            </div>

            <div class="form-group" style="margin-bottom: 1em;">
                <label style="font-weight:500;">
                    <input type="checkbox" id="general-autosave-checkbox" style="margin-right:0.5em;">
                    Enable Auto-Save
                </label>
                <div class="setting-description" style="font-size: 0.9em; color: #666; margin-top: 0.25em;">
                    Automatically save settings changes without requiring manual save
                </div>
            </div>

            <div class="form-actions" style="margin-top: 2em; display: flex; gap: 1em; justify-content: flex-end;">
                <button type="button" id="general-reset-btn" class="btn btn-secondary" style="padding: 0.5em 1em;">
                    Reset to Defaults
                </button>
                <button type="button" id="general-save-btn" class="btn btn-primary" style="padding: 0.5em 1em;">
                    Save Changes
                </button>
            </div>

            <div id="general-status" style="margin-top: 1em; padding: 0.5em; border-radius: 4px; display: none;"></div>
        </form>
    `;
    // Set initial values with proper type assertions
    const themeSelect = container.querySelector('#general-theme-select');
    if (themeSelect) {
        themeSelect.value = theme;
    }
    const languageSelect = container.querySelector('#general-language-select');
    if (languageSelect) {
        languageSelect.value = language;
    }
    const autoSaveCheckbox = container.querySelector('#general-autosave-checkbox');
    if (autoSaveCheckbox) {
        autoSaveCheckbox.checked = autoSave;
    }
    const perAgentLLMCheckbox = container.querySelector('#general-peragentllm-checkbox');
    if (perAgentLLMCheckbox) {
        perAgentLLMCheckbox.checked = perAgentLLMEnabled;
    }
    // Event listeners with proper typing
    if (themeSelect) {
        themeSelect.addEventListener('change', (e) => {
            const target = e.target;
            const newValue = target.value;
            // Send updateSetting message to VS Code extension
            const vscode = window.acquireVsCodeApi?.();
            if (vscode) {
                vscode.postMessage({
                    command: 'updateSetting',
                    section: 'general',
                    key: 'theme',
                    value: newValue
                });
            }
            onChange('theme', newValue);
            showStatus('Theme updated', 'success');
        });
    }
    if (languageSelect) {
        languageSelect.addEventListener('change', (e) => {
            const target = e.target;
            const newValue = target.value;
            // Send updateSetting message to VS Code extension
            const vscode = window.acquireVsCodeApi?.();
            if (vscode) {
                vscode.postMessage({
                    command: 'updateSetting',
                    section: 'general',
                    key: 'language',
                    value: newValue
                });
            }
            onChange('language', newValue);
            showStatus('Language updated', 'success');
        });
    }
    if (autoSaveCheckbox) {
        autoSaveCheckbox.addEventListener('change', (e) => {
            const target = e.target;
            const isChecked = target.checked;
            // Send updateSetting message to VS Code extension
            const vscode = window.acquireVsCodeApi?.();
            if (vscode) {
                vscode.postMessage({
                    command: 'updateSetting',
                    section: 'general',
                    key: 'autoSave',
                    value: isChecked
                });
            }
            onChange('autoSave', isChecked);
            showStatus('Auto-save setting updated', 'success');
        });
    }
    if (perAgentLLMCheckbox) {
        perAgentLLMCheckbox.addEventListener('change', (e) => {
            const target = e.target;
            const isChecked = target.checked;
            // Send updateSetting message to VS Code extension
            const vscode = window.acquireVsCodeApi?.();
            if (vscode) {
                vscode.postMessage({
                    command: 'updateSetting',
                    section: 'agents',
                    key: 'perAgentLLMEnabled',
                    value: isChecked
                });
            }
            onChange('agents.perAgentLLMEnabled', isChecked);
            showStatus('Per-agent LLM setting updated', 'success');
        });
    }
    // Save button with proper typing
    const saveBtn = container.querySelector('#general-save-btn');
    if (saveBtn) {
        saveBtn.addEventListener('click', () => {
            onSave();
            showStatus('Settings saved successfully!', 'success');
        });
    }
    // Reset button with proper typing
    const resetBtn = container.querySelector('#general-reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to reset all settings to their default values?')) {
                onReset();
                showStatus('Settings have been reset to defaults', 'info');
            }
        });
    }
    /**
     * Shows a status message in the UI
     * @param message - The message to display
     * @param type - The type of message (success, error, or info)
     */
    function showStatus(message, type = 'info') {
        const statusElement = container.querySelector('#general-status');
        if (!statusElement)
            return;
        // Set message content
        statusElement.textContent = message;
        // Apply styles based on message type
        const styles = {
            display: 'block',
            padding: '0.75em',
            borderRadius: '4px',
            marginTop: '1em',
            backgroundColor: type === 'error' ? '#ffebee' :
                type === 'success' ? '#e8f5e9' : '#e3f2fd',
            color: type === 'error' ? '#c62828' :
                type === 'success' ? '#2e7d32' : '#1565c0',
            transition: 'opacity 0.3s ease-in-out'
        };
        // Apply styles
        Object.assign(statusElement.style, styles);
        // Auto-hide after 5 seconds
        const hideTimeout = setTimeout(() => {
            statusElement.style.opacity = '0';
            setTimeout(() => {
                statusElement.style.display = 'none';
                statusElement.style.opacity = '1';
            }, 300);
        }, 5000);
        // Clear timeout if the element is hovered
        statusElement.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
            statusElement.style.opacity = '1';
        });
        statusElement.addEventListener('mouseleave', () => {
            setTimeout(() => {
                statusElement.style.opacity = '0';
                setTimeout(() => {
                    statusElement.style.display = 'none';
                    statusElement.style.opacity = '1';
                }, 300);
            }, 1000);
        });
    }
    // Add CSS for status messages
    const style = document.createElement('style');
    style.textContent = `
        .status-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    `;
    container.appendChild(style);
}
//# sourceMappingURL=generalSettingsSection.js.map