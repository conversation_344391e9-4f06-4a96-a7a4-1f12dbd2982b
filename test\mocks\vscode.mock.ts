/**
 * Mock VS Code API for testing outside of VS Code
 * This provides a minimal implementation of the VS Code API needed for the workflow benchmark
 */

// Mock event emitter
class MockEventEmitter<T> {
  private listeners: ((e: T) => any)[] = [];
  
  event = (listener: (e: T) => any, _thisArgs?: any, _disposables?: any[]) => {
    this.listeners.push(listener);
    return {
      dispose: () => {
        const index = this.listeners.indexOf(listener);
        if (index !== -1) {
          this.listeners.splice(index, 1);
        }
      }
    };
  };
  
  fire(data: T): void {
    for (const listener of this.listeners) {
      try {
        listener(data);
      } catch (error) {
        console.error('Error in event listener:', error);
      }
    }
  }
  
  dispose(): void {
    this.listeners = [];
  }
}

// Mock workspace configuration
class MockWorkspaceConfiguration {
  constructor(private config: Record<string, any> = {}) {}
  
  get<T>(section: string, defaultValue: T): T {
    return this.config[section] ?? defaultValue;
  }
  
  update(section: string, value: any, _target?: any): Thenable<void> {
    this.config[section] = value;
    return Promise.resolve();
  }
  
  has(section: string): boolean {
    return section in this.config;
  }
  
  inspect<T>(section: string): { key: string; defaultValue?: T; globalValue?: T; workspaceValue?: T } | undefined {
    return {
      key: section,
      defaultValue: undefined,
      globalValue: this.config[section],
      workspaceValue: this.config[section]
    };
  }
}

// Mock output channel
class MockOutputChannel {
  constructor(public name: string) {}
  
  append(value: string): void {
    process.stdout.write(value);
  }
  
  appendLine(value: string): void {
    console.log(`[${this.name}] ${value}`);
  }
  
  clear(): void {}
  show(_preserveFocus?: boolean): void {}
  hide(): void {}
  dispose(): void {}
}

// Mock VS Code API
const vscode = {
  // Window
  window: {
    showInformationMessage: (message: string, ..._items: string[]) => {
      console.log(`[INFO] ${message}`);
      return Promise.resolve(undefined);
    },
    showErrorMessage: (message: string, ..._items: string[]) => {
      console.error(`[ERROR] ${message}`);
      return Promise.resolve(undefined);
    },
    showWarningMessage: (message: string, ..._items: string[]) => {
      console.warn(`[WARN] ${message}`);
      return Promise.resolve(undefined);
    },
    showQuickPick: (items: string[] | Thenable<string[]>, _options?: any) => {
      return Promise.resolve(Array.isArray(items) ? items[0] : undefined);
    },
    showInputBox: (_options?: any) => Promise.resolve(''),
    createOutputChannel: (name: string) => new MockOutputChannel(name),
    withProgress: (_options: any, task: any) => {
      const progress = {
        report: (value: { message?: string; increment?: number }) => {
          // No-op for benchmark
        }
      };
      return task(progress, new vscode.CancellationTokenSource().token);
    },
    onDidChangeActiveTextEditor: () => ({ dispose: () => {} }),
    activeTextEditor: undefined,
    visibleTextEditors: []
  },
  
  // Commands
  commands: {
    executeCommand: <T>(command: string, ..._args: any[]): Thenable<T | undefined> => {
      console.log(`[COMMAND] ${command}`);
      return Promise.resolve(undefined);
    },
    registerCommand: (command: string, callback: (...args: any[]) => any, _thisArg?: any) => ({
      dispose: () => {}
    })
  },
  
  // Workspace
  workspace: {
    workspaceFolders: [{
      uri: {
        fsPath: process.cwd(),
        toString: () => process.cwd(),
        with: (changes: any) => ({
          fsPath: changes.path || process.cwd(),
          toString: () => changes.path || process.cwd()
        })
      },
      name: 'workspace',
      index: 0
    }],
    
    getConfiguration: (section?: string) => {
      const config = {
        // Workflow engine configuration
        'codessa.workflow.logLevel': 'debug',
        'codessa.workflow.maxParallelSteps': 5,
        'codessa.workflow.enableQuantumAnalysis': false,
        'codessa.workflow.enableNeuralSynthesis': false,
        'codessa.workflow.enableTimeTravelDebugging': false,
        'codessa.workflow.enableAdaptivePersonality': false,
        'codessa.workflow.cacheEnabled': true,
        'codessa.workflow.cacheTTL': 300000, // 5 minutes
        'codessa.workflow.maxHistoryItems': 1000,
        'codessa.workflow.enablePerformanceMonitoring': true,
        'codessa.workflow.performanceSamplingRate': 0.1,
        'codessa.workflow.maxMemoryUsage': 1024, // MB
        'codessa.workflow.timeout': 300000, // 5 minutes
        'codessa.workflow.retryAttempts': 3,
        'codessa.workflow.retryDelay': 1000, // 1 second
        'codessa.workflow.enableGoddessMode': false,
        'codessa.workflow.enableQuantumEntanglement': false,
        'codessa.workflow.enableNeuralMemory': false,
        'codessa.workflow.enableConsciousness': false,
        'codessa.workflow.enableEmotionalIntelligence': false,
        'codessa.workflow.enableRevolutionaryFeatures': false
      };
      
      return new MockWorkspaceConfiguration(section ? {
        [section]: config
      } : config);
    },
    
    openTextDocument: (uriOrFileName: string | { path: string }) => {
      const path = typeof uriOrFileName === 'string' ? uriOrFileName : uriOrFileName.path;
      return Promise.resolve({
        fileName: path,
        getText: () => {
          try {
            const fs = require('fs');
            return fs.readFileSync(path, 'utf-8');
          } catch (error) {
            return '';
          }
        },
        save: () => Promise.resolve(true),
        isDirty: false,
        languageId: path.split('.').pop() || 'plaintext',
        lineCount: 1,
        positionAt: () => ({ line: 0, character: 0 }),
        offsetAt: () => 0,
        validatePosition: (position: any) => position,
        validateRange: (range: any) => range,
        getTextInRange: () => ''
      });
    },
    
    fs: {
      readFile: (uri: { fsPath: string }) => 
        Promise.resolve(Buffer.from('test content', 'utf-8')),
      writeFile: () => Promise.resolve(),
      stat: () => Promise.resolve({
        mtime: Date.now(),
        size: 1024,
        type: 1, // File
        ctime: Date.now() - 1000 * 60 * 60,
        permissions: 0o666
      }),
      readDirectory: () => Promise.resolve([])
    },
    
    findFiles: () => Promise.resolve([]),
    asRelativePath: (pathOrUri: string | { fsPath: string }) => 
      typeof pathOrUri === 'string' ? pathOrUri : pathOrUri.fsPath,
    
    createFileSystemWatcher: () => ({
      onDidCreate: () => ({ dispose: () => {} }),
      onDidChange: () => ({ dispose: () => {} }),
      onDidDelete: () => ({ dispose: () => {} }),
      dispose: () => {}
    }),
    
    onDidChangeWorkspaceFolders: () => ({ dispose: () => {} }),
    onDidChangeTextDocument: () => ({ dispose: () => {} }),
    onDidOpenTextDocument: () => ({ dispose: () => {} }),
    onDidCloseTextDocument: () => ({ dispose: () => {} }),
    onDidSaveTextDocument: () => ({ dispose: () => {} }),
    onWillSaveTextDocument: () => ({ dispose: () => {} }),
    onDidChangeConfiguration: () => ({ dispose: () => {} })
  },
  
  // Environment
  env: {
    appName: 'Codessa Benchmark',
    appRoot: process.cwd(),
    machineId: 'benchmark-machine-id',
    sessionId: `benchmark-session-${Date.now()}`,
    shell: process.env.SHELL || 'bash',
    uiKind: 1, // UIKind.Desktop
    remoteName: undefined,
    uriScheme: 'vscode',
    appHost: 'desktop',
    language: 'en',
    clipboard: {
      readText: () => Promise.resolve(''),
      writeText: () => Promise.resolve()
    },
    asExternalUri: (uri: any) => Promise.resolve(uri),
    openExternal: () => Promise.resolve(true)
  },
  
  // Extensions
  extensions: {
    getExtension: (extensionId: string) => {
      if (extensionId === 'codessa') {
        return {
          id: 'codessa',
          extensionPath: process.cwd(),
          isActive: true,
          packageJSON: {
            name: 'codessa',
            displayName: 'Codessa - The goddess of code',
            description: 'Revolutionary AI-powered coding assistant',
            version: '0.0.1',
            publisher: 'codessa',
            engines: {
              vscode: '^1.60.0'
            },
            categories: ['Programming Languages', 'Other'],
            activationEvents: ['*'],
            main: './out/extension',
            contributes: {
              commands: [],
              configuration: {}
            }
          },
          activate: () => Promise.resolve({}),
          exports: {}
        };
      }
      return undefined;
    },
    all: [],
    onDidChange: () => ({ dispose: () => {} })
  },
  
  // URI
  Uri: {
    parse: (path: string) => ({
      fsPath: path,
      toString: () => path,
      scheme: 'file',
      authority: '',
      path: path,
      query: '',
      fragment: '',
      with: (changes: any) => ({
        ...vscode.Uri.parse(path),
        ...changes
      })
    }),
    file: (path: string) => ({
      fsPath: path,
      toString: () => path,
      with: (changes: any) => ({
        ...vscode.Uri.file(path),
        ...changes
      })
    }),
    joinPath: (base: any, ...pathSegments: string[]) => {
      const path = require('path');
      const fullPath = path.join(base.fsPath || base, ...pathSegments);
      return vscode.Uri.file(fullPath);
    }
  },
  
  // Configuration targets
  ConfigurationTarget: {
    Global: 1,
    Workspace: 2,
    WorkspaceFolder: 3,
    Default: 0,
    Memory: 4,
    MemoryConfiguration: 5
  },
  
  // Cancellation
  CancellationTokenSource: class {
    private _isCancellationRequested = false;
    private _onCancellationRequested = new MockEventEmitter<void>();
    
    get token(): any {
      return {
        isCancellationRequested: this._isCancellationRequested,
        onCancellationRequested: this._onCancellationRequested.event
      };
    }
    
    cancel(): void {
      this._isCancellationRequested = true;
      this._onCancellationRequested.fire();
    }
    
    dispose(): void {
      this._onCancellationRequested.dispose();
    }
  },
  
  // Disposable
  Disposable: class {
    static from(...disposables: any[]): any {
      return {
        dispose: () => {
          for (const disposable of disposables) {
            if (disposable && typeof disposable.dispose === 'function') {
              disposable.dispose();
            }
          }
        }
      };
    }
    
    constructor(private _dispose: () => void) {}
    
    dispose(): void {
      this._dispose();
    }
  },
  
  // Progress location
  ProgressLocation: {
    SourceControl: 1,
    Window: 10,
    Notification: 15
  },
  
  // View column
  ViewColumn: {
    Active: -1,
    Beside: -2,
    One: 1,
    Two: 2,
    Three: 3,
    Four: 4,
    Five: 5,
    Six: 6,
    Seven: 7,
    Eight: 8,
    Nine: 9
  }
};

// Export the mock VS Code API
module.exports = vscode;
