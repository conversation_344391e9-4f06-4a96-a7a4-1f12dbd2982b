{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/memory/types.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6hBA,wDASC;AAQD,kDAQC;AAOD,4DAQC;AA7FD,+EAA+E;AAC/E,mDAAmD;AACnD,+EAA+E;AAE/E,kDAAkD;AAClD,iDAA+D;AAAtD,8GAAA,aAAa,OAAA;AAAE,8GAAA,aAAa,OAAA;AAErC,uBAAuB;AACvB,+CAAqD;AAA5C,mHAAA,mBAAmB,OAAA;AAG5B,2BAA2B;AAC3B,6CAA4D;AAAnD,0GAAA,WAAW,OAAA;AAAE,6GAAA,cAAc,OAAA;AAEpC,uBAAuB;AACvB,+CAAoE;AAA3D,gHAAA,gBAAgB,OAAA;AAAE,gHAAA,gBAAgB,OAAA;AAE3C,mDAAmD;AACnD,yDAAuF;AAA9E,sHAAA,qBAAqB,OAAA;AAAE,sHAAA,qBAAqB,OAAA;AAErD,uBAAuB;AACvB,mEAAkE;AAAzD,wHAAA,kBAAkB,OAAA;AAE3B,wBAAwB;AACxB,uDAA6D;AAApD,mHAAA,mBAAmB,OAAA;AAE5B,2BAA2B;AAC3B,uEAAsE;AAA7D,kHAAA,eAAe,OAAA;AACxB,qEAAoE;AAA3D,gHAAA,cAAc,OAAA;AACvB,yEAAwE;AAA/D,oHAAA,gBAAgB,OAAA;AACzB,mEAAkE;AAAzD,8GAAA,aAAa,OAAA;AACtB,uEAAsE;AAA7D,kHAAA,eAAe,OAAA;AACxB,mEAAkE;AAAzD,8GAAA,aAAa,OAAA;AAEtB,+BAA+B;AAC/B,gFAA+E;AAAtE,wHAAA,kBAAkB,OAAA;AAC3B,8EAA6E;AAApE,sHAAA,iBAAiB,OAAA;AAC1B,8EAA6E;AAApE,sHAAA,iBAAiB,OAAA;AAC1B,kFAAiF;AAAxE,0HAAA,mBAAmB,OAAA;AAE5B,mCAAmC;AACnC,qEAAoE;AAA3D,0HAAA,mBAAmB,OAAA;AAE5B,+EAA+E;AAC/E,iCAAiC;AACjC,+EAA+E;AAE/E;;;;;GAKG;AACI,KAAK,UAAU,sBAAsB,CAAC,OAAgC;IAC3E,IAAI,CAAC;QACH,MAAM,EAAE,aAAa,EAAE,GAAG,wDAAa,iBAAiB,GAAC,CAAC;QAC1D,MAAM,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,mBAAmB;IACvC,IAAI,CAAC;QACH,MAAM,EAAE,aAAa,EAAE,GAAG,wDAAa,iBAAiB,GAAC,CAAC;QAC1D,OAAO,aAAa,CAAC,aAAa,EAAE,CAAC;IACvC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,wBAAwB;IAC5C,IAAI,CAAC;QACH,MAAM,EAAE,aAAa,EAAE,GAAG,wDAAa,iBAAiB,GAAC,CAAC;QAC1D,OAAO,aAAa,CAAC,kBAAkB,EAAE,CAAC;IAC5C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\n\n/**\n * Memory System - Types, Interfaces, and Main Export Point\n *\n * This file serves as both the type definitions and the centralized export point\n * for all memory system components. Import from this file to access any memory\n * system functionality throughout the Codessa extension.\n *\n * Usage:\n * ```typescript\n * import { memoryManager, MemoryEntry, MemorySource } from '../memory/types';\n * import { initializeMemorySystem, isMemorySystemReady } from '../memory/types';\n * ```\n */\n\n// Core memory entry interface - UNIFIED VERSION\nexport interface MemoryEntry {\n    id: string;\n    content: string;\n    timestamp: number;\n    embedding?: number[];\n    metadata: MemoryMetadata;\n}\n\n// Comprehensive metadata interface\nexport interface MemoryMetadata {\n    source: MemorySource;\n    type: MemoryType;\n    tags?: string[];\n    agentId?: string;\n    agentName?: string;\n    namespace?: string;\n    filePath?: string;\n    lineNumber?: number;\n    functionName?: string;\n    className?: string;\n    projectContext?: string;\n    version?: number;\n    changeType?: 'create' | 'update' | 'delete' | 'refactor';\n    confidence?: number;\n    importance?: number;\n    [key: string]: string | number | boolean | string[] | undefined;\n}\n\n// Memory source types - UNIFIED\nexport type MemorySource =\n    | 'conversation'\n    | 'temporal'\n    | 'user'\n    | 'system'\n    | 'agent'\n    | 'file'\n    | 'code'\n    | 'documentation'\n    | 'collaboration'\n    | 'prediction'\n    | 'workspace'\n    | 'database';\n\n// Memory content types - UNIFIED\nexport type MemoryType =\n    | 'text'\n    | 'semantic'\n    | 'episodic'\n    | 'procedural'\n    | 'human'\n    | 'ai'\n    | 'system'\n    | 'code'\n    | 'insight'\n    | 'pattern'\n    | 'conversation'\n    | 'project'\n    | 'user_preference'\n    | 'document'\n    | 'image'\n    | 'audio'\n    | 'video'\n    | 'binary';\n\n// Unified search options interface - ENHANCED\nexport interface MemorySearchOptions {\n    query: string;\n    limit?: number;\n    offset?: number;\n    threshold?: number;\n    relevanceThreshold?: number;\n    filter?: MemoryFilter;\n    sortBy?: 'timestamp' | 'relevance' | 'importance';\n    sortOrder?: 'asc' | 'desc';\n    includeMetadata?: boolean;\n    contextual?: boolean;\n}\n\n// Comprehensive filter interface - UNIFIED\nexport interface MemoryFilter {\n    source?: MemorySource | MemorySource[];\n    type?: MemoryType | MemoryType[];\n    tags?: string[];\n    agentId?: string;\n    namespace?: string;\n    filePath?: string;\n    fromTimestamp?: number;\n    toTimestamp?: number;\n    minConfidence?: number;\n    minImportance?: number;\n    changeType?: 'create' | 'update' | 'delete' | 'refactor';\n    [key: string]: string | number | boolean | string[] | Date | undefined;\n}\n\n/**\n * Comprehensive Memory Settings Interface - UNIFIED\n */\nexport interface MemorySettings {\n    // Core settings\n    enabled: boolean;\n    system: 'basic' | 'codessa' | 'quantum';\n    maxMemories: number;\n    maxMemoriesPerFile: number;\n    memoryRetentionDays: number;\n    relevanceThreshold: number;\n    contextWindowSize: number;\n    conversationHistorySize: number;\n\n    // Phase 4 settings\n    enableQuantumMemory: boolean;\n    enablePredictiveInsights: boolean;\n    enableMemoryVisualization: boolean;\n    enableCollaborativeMemory: boolean;\n\n    // Vector store settings\n    vectorStore: 'memory' | 'chroma' | 'pinecone' | 'weaviate' | 'hnswlib';\n    vectorStoreSettings: {\n        chroma: {\n            directory: string;\n            collectionName: string;\n        };\n        pinecone: {\n            apiKey: string;\n            environment: string;\n            indexName: string;\n        };\n    };\n\n    // Database settings\n    database: 'sqlite' | 'mysql' | 'postgres' | 'mongodb' | 'redis';\n    databaseSettings: {\n        sqlite: {\n            filename: string;\n        };\n        mysql: {\n            host: string;\n            port: number;\n            user: string;\n            password: string;\n            database: string;\n            table: string;\n        };\n        postgres: {\n            connectionString: string;\n            schema: string;\n        };\n        mongodb: {\n            connectionString: string;\n            database: string;\n            collection: string;\n        };\n        redis: {\n            url: string;\n            keyPrefix: string;\n        };\n    };\n\n    // File processing settings\n    fileChunking: {\n        chunkSize: number;\n        chunkOverlap: number;\n        maxChunksPerFile: number;\n    };\n\n    // Performance settings\n    cacheSize: number;\n    cacheExpiry: number;\n    batchSize: number;\n    maxConcurrentOperations: number;\n}\n\n/**\n * Interface for memory operations needed by tools\n * This breaks circular dependencies between MemoryManager and tools\n */\nexport interface IMemoryOperations {\n  addMemory(memory: Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry>;\n  getMemory(id: string): Promise<MemoryEntry | undefined>;\n  getMemories(limit?: number): Promise<MemoryEntry[]>;\n  deleteMemory(id: string): Promise<boolean>;\n  clearMemories(): Promise<void>;\n  searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]>;\n  searchSimilarMemories(query: string, options?: Partial<MemorySearchOptions>): Promise<MemoryEntry[]>;\n  updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined>;\n  getMemorySettings(): MemorySettings;\n  updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean>;\n  registerMemoryChangeListener: (listener: () => void) => void;\n}\n\n/**\n * Base memory provider interface\n */\nexport interface IMemoryProvider {\n    /**\n     * Initialize the memory provider\n     */\n    initialize(context: vscode.ExtensionContext): Promise<void>;\n\n    /**\n     * Add a memory\n     */\n    addMemory(memory: Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry>;\n\n    /**\n     * Get all memories\n     */\n    getMemories(): Promise<MemoryEntry[]>;\n\n    /**\n     * Get a memory by ID\n     */\n    getMemory(id: string): Promise<MemoryEntry | undefined>;\n\n    /**\n     * Delete a memory by ID\n     */\n    deleteMemory(id: string): Promise<boolean>;\n\n    /**\n     * Clear all memories\n     */\n    clearMemories(): Promise<void>;\n\n    /**\n     * Search memories\n     */\n    searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]>;\n\n    /**\n     * Search memories by semantic similarity\n     */\n    searchSimilarMemories(query: string, options?: Partial<MemorySearchOptions>): Promise<MemoryEntry[]>;\n\n    /**\n     * Get memory settings\n     */\n    getMemorySettings(): MemorySettings;\n\n    /**\n     * Update memory settings\n     */\n    updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean>;\n\n    /**\n     * Update an existing memory\n     * @param id The ID of the memory to update\n     * @param updates The fields to update\n     * @returns The updated memory entry, or undefined if not found\n     */\n    updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined>;\n}\n\n/**\n * Vector store interface\n */\nexport interface IVectorStore {\n    /**\n     * Initialize the vector store\n     */\n    initialize(): Promise<void>;\n\n    /**\n     * Add a vector\n     */\n    addVector(id: string, vector: number[], metadata?: Record<string, string | number | boolean | undefined>): Promise<void>;\n\n    /**\n     * Get a vector by ID\n     */\n    getVector(id: string): Promise<number[] | undefined>;\n\n    /**\n     * Delete a vector by ID\n     */\n    deleteVector(id: string): Promise<boolean>;\n\n    /**\n     * Clear all vectors\n     */\n    clearVectors(): Promise<void>;\n\n    /**\n     * Search for similar vectors\n     */\n    searchSimilarVectors(vector: number[], limit?: number, filter?: Record<string, string | number | boolean | undefined>): Promise<Array<{id: string, score: number}>>;\n\n    /**\n     * Get a retriever interface for this vector store\n     */\n    asRetriever?(k?: number): { getRelevantDocuments: (query: string) => Promise<Array<{ pageContent: string; metadata: Record<string, unknown> }>> };\n}\n\n/**\n * Database interface\n */\nexport interface IDatabase {\n    /**\n     * Initialize the database\n     */\n    initialize(): Promise<void>;\n\n    /**\n     * Add a record\n     */\n    addRecord(collection: string, record: Record<string, unknown>): Promise<string>;\n\n    /**\n     * Get a record by ID\n     */\n    getRecord(collection: string, id: string): Promise<Record<string, unknown> | undefined>;\n\n    /**\n     * Get multiple records by IDs\n     */\n    getRecordsByIds?(collection: string, ids: string[]): Promise<Record<string, unknown>[]>;\n\n    /**\n     * Update a record\n     */\n    updateRecord(collection: string, id: string, record: Record<string, unknown>): Promise<boolean>;\n\n    /**\n     * Delete a record\n     */\n    deleteRecord(collection: string, id: string): Promise<boolean>;\n\n    /**\n     * Delete multiple records\n     */\n    deleteRecords?(collection: string, query: Record<string, unknown>): Promise<number>;\n\n    /**\n     * Query records\n     */\n    queryRecords(collection: string, query: Record<string, unknown>, limit?: number, sort?: Record<string, 1 | -1>): Promise<Record<string, unknown>[]>;\n\n    /**\n     * Clear all records in a collection\n     */\n    clearCollection(collection: string): Promise<void>;\n\n    /**\n     * Ensure a collection exists\n     */\n    ensureCollection?(collection: string): Promise<void>;\n\n    /**\n     * Check if the database supports text search\n     */\n    readonly supportsTextSearch?: boolean;\n}\n\n// Additional interfaces for Phase 4 integration\nexport interface SharedMemoryItem {\n    id: string;\n    memoryId: string;\n    sharedBy: string;\n    sharedWith: string[];\n    permissions: 'read' | 'write' | 'admin';\n    sharedAt: number;\n    expiresAt?: number;\n    context: string;\n    memory: MemoryEntry;\n}\n\nexport interface MemoryAnalytics {\n    totalMemories: number;\n    temporalMemories: number;\n    collaborativeShares: number;\n    predictiveInsights: number;\n    memoryGrowthRate: number;\n    averageRetrievalTime: number;\n    mostAccessedMemories: string[];\n    memoryPatterns: {\n        commonTags: string[];\n        frequentContexts: string[];\n        peakUsageTimes: number[];\n    };\n    userBehaviorInsights: {\n        preferredMemoryTypes: string[];\n        searchPatterns: string[];\n        collaborationFrequency: number;\n    };\n}\n\nexport interface MemoryVisualization {\n    nodes: MemoryNode[];\n    edges: MemoryEdge[];\n    clusters: MemoryCluster[];\n    temporalFlow: TemporalFlow[];\n    metadata: VisualizationMetadata;\n}\n\nexport interface MemoryNode {\n    id: string;\n    label: string;\n    type: string;\n    timestamp: number;\n    context: string;\n    size: number;\n    color: string;\n    importance: number;\n}\n\nexport interface MemoryEdge {\n    from: string;\n    to: string;\n    type: 'version' | 'context' | 'temporal' | 'collaboration';\n    label: string;\n    weight?: number;\n    strength?: number;\n}\n\nexport interface MemoryCluster {\n    id: string;\n    name: string;\n    members: string[];\n    type: 'file-based' | 'temporal' | 'semantic' | 'collaborative';\n    strength: number;\n}\n\nexport interface TemporalFlow {\n    from: string;\n    to: string;\n    timeDelta: number;\n    type: 'temporal';\n    strength: number;\n}\n\nexport interface VisualizationMetadata {\n    totalNodes: number;\n    totalEdges: number;\n    centerNode?: string;\n    generatedAt: number;\n    timespan?: {\n        start: number;\n        end: number;\n        duration: number;\n    };\n    complexity: number;\n}\n\n/**\n * Chat message interface\n */\n/**\n * Memory access log entry\n */\nexport interface MemoryAccessLog {\n    id: string;\n    memoryId: string;\n    agentId: string;\n    action: 'read' | 'write' | 'share' | 'revoke';\n    timestamp: number;\n    success: boolean;\n    error?: string;\n    metadata?: Record<string, unknown>;\n}\n\n/**\n * Chat message interface\n */\nexport interface ChatMessage {\n    id: string;\n    sessionId: string;\n    type: 'human' | 'ai' | 'system' | 'unknown';\n    content: string;\n    timestamp: number;\n    metadata?: Record<string, unknown>;\n}\n\n// ============================================================================\n// MEMORY SYSTEM EXPORTS - Centralized Export Point\n// ============================================================================\n\n// Main Memory Manager (Primary Integration Point)\nexport { memoryManager, MemoryManager } from './memoryManager';\n\n// Vector Memory System\nexport { vectorMemoryManager } from './vectorMemory';\nexport type { VectorMemoryManager } from './vectorMemory';\n\n// Agent Memory Integration\nexport { AgentMemory, getAgentMemory } from './agentMemory';\n\n// Memory Configuration\nexport { getMemoryEnabled, setMemoryEnabled } from './memoryConfig';\n\n// Codessa Memory Provider (Advanced Memory System)\nexport { codessaMemoryProvider, CodessaMemoryProvider } from './codessa/codessaMemory';\n\n// Codessa Graph Memory\nexport { CodessaGraphMemory } from './codessa/codessaGraphMemory';\n\n// File Chunking Service\nexport { FileChunkingService } from './codessa/fileChunking';\n\n// Database Implementations\nexport { DatabaseFactory } from './codessa/databases/databaseFactory';\nexport { SQLiteDatabase } from './codessa/databases/sqliteDatabase';\nexport { PostgresDatabase } from './codessa/databases/postgresDatabase';\nexport { MySQLDatabase } from './codessa/databases/mysqlDatabase';\nexport { MongoDBDatabase } from './codessa/databases/mongodbDatabase';\nexport { RedisDatabase } from './codessa/databases/redisDatabase';\n\n// Vector Store Implementations\nexport { VectorStoreFactory } from './codessa/vectorStores/vectorStoreFactory';\nexport { MemoryVectorStore } from './codessa/vectorStores/memoryVectorStore';\nexport { ChromaVectorStore } from './codessa/vectorStores/chromaVectorStore';\nexport { PineconeVectorStore } from './codessa/vectorStores/pineconeVectorStore';\n\n// Quantum Memory System (Advanced)\nexport { QuantumMemorySystem } from './quantum/quantumMemorySystem';\n\n// ============================================================================\n// MEMORY SYSTEM HELPER FUNCTIONS\n// ============================================================================\n\n/**\n * Memory System Initialization Helper\n *\n * Provides a convenient way to initialize the entire memory system\n * with proper error handling and fallbacks.\n */\nexport async function initializeMemorySystem(context: vscode.ExtensionContext): Promise<boolean> {\n  try {\n    const { memoryManager } = await import('./memoryManager');\n    await memoryManager.initialize(context);\n    return true;\n  } catch (error) {\n    console.error('Failed to initialize memory system:', error);\n    return false;\n  }\n}\n\n/**\n * Memory System Status Check\n *\n * Provides a way to check if the memory system is properly initialized\n * and ready for use.\n */\nexport async function isMemorySystemReady(): Promise<boolean> {\n  try {\n    const { memoryManager } = await import('./memoryManager');\n    return memoryManager.isInitialized();\n  } catch (error) {\n    console.error('Failed to check memory system status:', error);\n    return false;\n  }\n}\n\n/**\n * Get Memory System Analytics\n *\n * Provides access to memory system analytics and statistics.\n */\nexport async function getMemorySystemAnalytics(): Promise<MemoryAnalytics | null> {\n  try {\n    const { memoryManager } = await import('./memoryManager');\n    return memoryManager.getMemoryAnalytics();\n  } catch (error) {\n    console.error('Failed to get memory system analytics:', error);\n    return null;\n  }\n}\n"]}