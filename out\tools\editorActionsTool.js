"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.editorActionsTool = exports.EditorActionsTool = void 0;
const vscode = __importStar(require("vscode"));
const advancedEditorActionsTool_1 = require("./advancedEditorActionsTool");
const zod_1 = require("zod");
class EditorActionsTool {
    id = 'editor';
    name = 'Editor Actions';
    description = 'Open, close, navigate, and edit files/tabs; move cursor; select text; multi-cursor; clipboard; batch edit; find/replace.';
    type = 'multi-action'; // Required by ITool
    actions = {
        'open': {
            id: 'open',
            name: 'Open File',
            description: 'Open a file in the editor.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                filePath: zod_1.z.string().describe('Path to the file to open')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to open' }
                },
                required: ['filePath']
            },
            async execute(actionName, input, context) {
                const filePath = input.filePath;
                const doc = await vscode.workspace.openTextDocument(filePath);
                await vscode.window.showTextDocument(doc);
                return { success: true, output: `Opened ${filePath}`, toolId: 'open', actionName };
            }
        },
        'close': {
            id: 'close',
            name: 'Close Editor',
            description: 'Close the active editor.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({}),
            inputSchema: {
                type: 'object',
                properties: {},
                required: []
            },
            async execute(actionName, input, context) {
                const editor = vscode.window.activeTextEditor;
                if (editor) {
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                    return { success: true, output: 'Closed active editor', toolId: 'close', actionName };
                }
                else {
                    return { success: false, error: 'No active editor to close.', toolId: 'close', actionName };
                }
            }
        },
        'goto': {
            id: 'goto',
            name: 'Go To Position',
            description: 'Move cursor to a specific position.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                filePath: zod_1.z.string().describe('Path to the file to navigate to'),
                line: zod_1.z.number().describe('Line number to navigate to (0-based)'),
                column: zod_1.z.number().optional().describe('Column number to navigate to (0-based)')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to navigate to' },
                    line: { type: 'number', description: 'Line number to navigate to (0-based)' },
                    column: { type: 'number', description: 'Column number to navigate to (0-based)' }
                },
                required: ['filePath', 'line']
            },
            async execute(actionName, input, context) {
                const filePath = input.filePath;
                const line = input.line;
                const column = input.column || 0;
                const doc = await vscode.workspace.openTextDocument(filePath);
                const editor = await vscode.window.showTextDocument(doc);
                const pos = new vscode.Position(line, column);
                editor.selection = new vscode.Selection(pos, pos);
                editor.revealRange(new vscode.Range(pos, pos));
                return { success: true, output: `Moved cursor to ${filePath}:${line + 1}:${column + 1}`, toolId: 'goto', actionName };
            }
        },
        'edit': {
            id: 'edit',
            name: 'Edit File',
            description: 'Edit file at a range or cursor.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                filePath: zod_1.z.string().describe('Path to the file to edit'),
                text: zod_1.z.string().describe('Text to insert or replace with'),
                range: zod_1.z.object({
                    start: zod_1.z.object({
                        line: zod_1.z.number(),
                        column: zod_1.z.number()
                    }),
                    end: zod_1.z.object({
                        line: zod_1.z.number(),
                        column: zod_1.z.number()
                    })
                }).optional().describe('Range to replace (optional)')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to edit' },
                    text: { type: 'string', description: 'Text to insert or replace with' },
                    range: { type: 'object', description: 'Range to replace (optional)' }
                },
                required: ['filePath', 'text']
            },
            async execute(actionName, input, context) {
                const filePath = input.filePath;
                const text = input.text;
                const range = input.range;
                const doc = await vscode.workspace.openTextDocument(filePath);
                const editor = await vscode.window.showTextDocument(doc);
                await editor.edit(editBuilder => {
                    if (range && range.start && range.end) {
                        const start = new vscode.Position(range.start.line, range.start.column);
                        const end = new vscode.Position(range.end.line, range.end.column);
                        editBuilder.replace(new vscode.Range(start, end), text);
                    }
                    else {
                        editBuilder.insert(editor.selection.active, text);
                    }
                });
                return { success: true, output: `Edited ${filePath}`, toolId: 'edit', actionName };
            }
        },
        'select': {
            id: 'select',
            name: 'Select Text',
            description: 'Select text in a file.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                filePath: zod_1.z.string().describe('Path to the file to select text in'),
                range: zod_1.z.object({
                    start: zod_1.z.object({
                        line: zod_1.z.number(),
                        column: zod_1.z.number()
                    }),
                    end: zod_1.z.object({
                        line: zod_1.z.number(),
                        column: zod_1.z.number()
                    })
                }).describe('Range to select')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to select text in' },
                    range: { type: 'object', description: 'Range to select' }
                },
                required: ['filePath', 'range']
            },
            async execute(actionName, input, context) {
                const filePath = input.filePath;
                const range = input.range;
                const doc = await vscode.workspace.openTextDocument(filePath);
                const editor = await vscode.window.showTextDocument(doc);
                const start = new vscode.Position(range.start.line, range.start.column);
                const end = new vscode.Position(range.end.line, range.end.column);
                editor.selection = new vscode.Selection(start, end);
                editor.revealRange(new vscode.Range(start, end));
                return { success: true, output: `Selected text in ${filePath}`, toolId: 'select', actionName };
            }
        },
        'multiCursor': {
            ...new advancedEditorActionsTool_1.MultiCursorTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                return { success: true, output: 'Multi-cursor operation performed', toolId: 'multiCursor', actionName };
            }
        },
        'clipboard': {
            ...new advancedEditorActionsTool_1.ClipboardTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                return { success: true, output: 'Clipboard operation performed', toolId: 'clipboard', actionName };
            }
        },
        'batchEdit': {
            ...new advancedEditorActionsTool_1.BatchEditTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                return { success: true, output: 'Batch edit operation performed', toolId: 'batchEdit', actionName };
            }
        },
        'findReplace': {
            ...new advancedEditorActionsTool_1.FindReplaceTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                return { success: true, output: 'Find and replace operation performed', toolId: 'findReplace', actionName };
            }
        },
    };
    async execute(actionName, input, context) {
        // For backward compatibility, check if actionName is undefined and try to get it from input
        const actionId = actionName || input.action;
        if (!actionId) {
            return { success: false, error: `Action parameter is required. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };
        }
        const actionTool = this.actions[actionId];
        if (!actionTool) {
            return { success: false, error: `Unknown editor action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };
        }
        const actionInput = { ...input };
        if ('action' in actionInput) {
            delete actionInput.action;
        }
        // Check if the action tool has the new execute method signature
        if (actionTool.execute.length >= 2) {
            // Pass the actionId as the actionName parameter to the nested tool
            return actionTool.execute(actionId, actionInput, context);
        }
        else {
            // Fallback for older tools that don't have the new signature
            const result = await actionTool.execute(actionInput, context);
            // Add the actionName to the result if it's not already there
            if (result && !result.actionName) {
                result.actionName = actionId;
            }
            return result;
        }
    }
}
exports.EditorActionsTool = EditorActionsTool;
exports.editorActionsTool = new EditorActionsTool();
//# sourceMappingURL=editorActionsTool.js.map