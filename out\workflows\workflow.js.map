{"version": 3, "file": "workflow.js", "sourceRoot": "", "sources": ["../../src/workflows/workflow.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAKtC,MAAa,QAAQ;IACF,GAAG,CAAS;IACZ,KAAK,CAAS;IACd,MAAM,GAA8B,IAAI,GAAG,EAAE,CAAC;IAC9C,YAAY,GAA6B,IAAI,GAAG,EAAE,CAAC;IAC5D,cAAc,GAAkB,IAAI,CAAC;IACrC,QAAQ,GAAiB,IAAI,qBAAY,EAAE,CAAC;IAEpD,YAAY,EAAU,EAAE,IAAY;QAClC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACX,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,IAAkB;QAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,CAAC,EAAE,iCAAiC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACxF,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,MAAc;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,iBAAiB,MAAM,4BAA4B,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QAClF,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,UAAkB,EAAE,QAAgB;QACvD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,gBAAgB,UAAU,4BAA4B,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACrF,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,gBAAgB,QAAQ,4BAA4B,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACnF,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,aAAqB,EAAE,MAA0B;QAClE,0DAA0D;QAC1D,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAClE,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,8CAA8C;QAC1E,CAAC;QAED,gDAAgD;QAChD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACzD,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YACnD,OAAO,QAAQ,IAAI,IAAI,CAAC;QAC1B,CAAC;QAED,gBAAgB;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,OAAwB;QAC3C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,uCAAuC;QACvC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QACxC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;QACpD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;QAC1C,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;QAC7C,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;QAClC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;QAC1C,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;QAClC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;QACpD,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAE3B,IAAI,aAAa,GAAkB,IAAI,CAAC,cAAc,CAAC;QACvD,IAAI,UAAU,GAA8B,IAAI,CAAC;QAEjD,4BAA4B;QAC5B,OAAO,aAAa,EAAE,CAAC;YACrB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC5C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,SAAS,aAAa,4BAA4B,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;YACjF,CAAC;YAED,mBAAmB;YACnB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACH,wBAAwB;gBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC,CAAC;gBAEtE,mBAAmB;gBACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC3C,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAE3B,4BAA4B;gBAC5B,MAAM,YAAY,GAAyB;oBACzC,MAAM,EAAE,aAAa;oBACrB,SAAS;oBACT,OAAO;oBACP,MAAM;oBACN,eAAe,EAAE,MAAM,CAAC,QAAQ,EAAE,eAAe;oBACjD,cAAc,EAAE,MAAM,CAAC,QAAQ,EAAE,cAAc;oBAC/C,eAAe,EAAE,MAAM,CAAC,QAAQ,EAAE,eAAe;iBAClD,CAAC;gBAEF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnC,UAAU,GAAG,MAAM,CAAC;gBAEpB,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE;oBACjC,MAAM,EAAE,aAAa;oBACrB,MAAM;oBACN,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE;iBAClD,CAAC,CAAC;gBAEH,oCAAoC;gBACpC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpB,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;oBAC1B,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;oBAC7B,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,oBAAoB;gBACpB,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAC3B,MAAM,WAAW,GAAuB;oBACtC,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC7D,MAAM,EAAE,SAAS;oBACjB,QAAQ,EAAE;wBACR,KAAK,EAAE;4BACL,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO;4BACnD,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;4BAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;4BACvD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACF;iBACF,CAAC;gBAEF,yBAAyB;gBACzB,MAAM,YAAY,GAAyB;oBACzC,MAAM,EAAE,aAAa,IAAI,SAAS;oBAClC,SAAS;oBACT,OAAO,EAAE,OAAO,IAAI,IAAI,IAAI,EAAE,EAAE,kCAAkC;oBAClE,MAAM,EAAE;wBACN,OAAO,EAAE,WAAW,CAAC,OAAO;wBAC5B,MAAM,EAAE,WAAW,CAAC,MAAM;wBAC1B,KAAK,EAAE,WAAW,CAAC,KAAK;wBACxB,UAAU,EAAE,WAAW,CAAC,UAAU;wBAClC,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,EAAE;qBACrC;iBACF,CAAC;gBAEF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnC,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC1B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;gBAE1B,wBAAwB;gBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE;oBAC9B,MAAM,EAAE,aAAa;oBACrB,KAAK,EAAE,KAAc;oBACrB,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE;iBAClD,CAAC,CAAC;gBAEH,OAAO,WAAW,CAAC;YACrB,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;QAC7B,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;YAC7B,OAAO;YACP,MAAM,EAAE,UAAU,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,oBAAoB,EAAE;SACtE,CAAC,CAAC;QAEH,OAAO,UAAU,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,EAAE,CAAC,KAA8D,EAAE,QAAkC;QAC1G,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,GAAG,CAAC,KAA8D,EAAE,QAAkC;QAC3G,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,KAAK;QACV,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAEpD,cAAc;QACd,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;YACxC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;QAED,oBAAoB;QACpB,KAAK,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,QAAQ,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA7PD,4BA6PC", "sourcesContent": ["import { EventEmitter } from 'events';\nimport { WorkflowContext, WorkflowStepResult, WorkflowHistoryEntry } from './workflowContext';\nimport { WorkflowStep } from './workflowStep';\nimport type { WorkflowStepOptions } from './workflowContext';\n\nexport class Workflow {\n  private readonly _id: string;\n  private readonly _name: string;\n  private readonly _steps: Map<string, WorkflowStep> = new Map();\n  private readonly _transitions: Map<string, Set<string>> = new Map();\n  private _initialStepId: string | null = null;\n  private _emitter: EventEmitter = new EventEmitter();\n\n  constructor(id: string, name: string) {\n    this._id = id;\n    this._name = name;\n  }\n\n  /**\n   * Get the workflow ID\n   */\n  public get id(): string {\n    return this._id;\n  }\n\n  /**\n   * Get the workflow name\n   */\n  public get name(): string {\n    return this._name;\n  }\n\n  /**\n   * Add a step to the workflow\n   */\n  public addStep(step: WorkflowStep): void {\n    if (this._steps.has(step.id)) {\n      throw new Error(`Step with ID '${step.id}' already exists in workflow '${this._id}'`);\n    }\n    this._steps.set(step.id, step);\n    this._transitions.set(step.id, new Set());\n  }\n\n  /**\n   * Set the initial step of the workflow\n   */\n  public setInitialStep(stepId: string): void {\n    if (!this._steps.has(stepId)) {\n      throw new Error(`Step with ID '${stepId}' not found in workflow '${this._id}'`);\n    }\n    this._initialStepId = stepId;\n  }\n\n  /**\n   * Add a transition between two steps\n   */\n  public addTransition(fromStepId: string, toStepId: string): void {\n    if (!this._steps.has(fromStepId)) {\n      throw new Error(`Source step '${fromStepId}' not found in workflow '${this._id}'`);\n    }\n    if (!this._steps.has(toStepId)) {\n      throw new Error(`Target step '${toStepId}' not found in workflow '${this._id}'`);\n    }\n    \n    const transitions = this._transitions.get(fromStepId);\n    if (transitions) {\n      transitions.add(toStepId);\n    } else {\n      this._transitions.set(fromStepId, new Set([toStepId]));\n    }\n  }\n\n  /**\n   * Get the next step based on the current step and result\n   */\n  public getNextStep(currentStepId: string, result: WorkflowStepResult): string | null {\n    // If nextStepId is explicitly set in the result, use that\n    if (result.nextStepId !== undefined && result.nextStepId !== null) {\n      return result.nextStepId; // This is now properly typed as string | null\n    }\n    \n    // Otherwise, use the first available transition\n    const transitions = this._transitions.get(currentStepId);\n    if (transitions && transitions.size > 0) {\n      const nextStep = transitions.values().next().value;\n      return nextStep || null;\n    }\n    \n    // No more steps\n    return null;\n  }\n\n  /**\n   * Execute the workflow with the given context\n   */\n  public async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n    if (!this._initialStepId) {\n      throw new Error('No initial step set for workflow');\n    }\n\n    // Initialize context with proper types\n    context.history = context.history || [];\n    context.startTime = context.startTime || new Date();\n    context.endTime = context.endTime || null;\n    context.status = context.status || 'pending';\n    context.data = context.data || {};\n    context.metadata = context.metadata || {};\n    context.data = context.data || {};\n    context.startTime = context.startTime || new Date();\n    context.status = 'running';\n    \n    let currentStepId: string | null = this._initialStepId;\n    let lastResult: WorkflowStepResult | null = null;\n\n    // Execute steps in sequence\n    while (currentStepId) {\n      const step = this._steps.get(currentStepId);\n      if (!step) {\n        throw new Error(`Step '${currentStepId}' not found in workflow '${this._id}'`);\n      }\n\n      // Execute the step\n      const startTime = new Date();\n      try {\n        // Emit step start event\n        this._emitter.emit('stepStart', { stepId: currentStepId, startTime });\n        \n        // Execute the step\n        const result = await step.execute(context);\n        const endTime = new Date();\n        \n        // Record the step execution\n        const historyEntry: WorkflowHistoryEntry = {\n          stepId: currentStepId,\n          startTime,\n          endTime,\n          result,\n          quantumAnalysis: result.metadata?.quantumAnalysis,\n          neuralInsights: result.metadata?.neuralInsights,\n          goddessGuidance: result.metadata?.goddessGuidance\n        };\n        \n        context.history.push(historyEntry);\n        lastResult = result;\n        \n        // Emit step complete event\n        this._emitter.emit('stepComplete', { \n          stepId: currentStepId, \n          result,\n          duration: endTime.getTime() - startTime.getTime()\n        });\n        \n        // If step failed, stop the workflow\n        if (!result.success) {\n          context.status = 'failed';\n          context.endTime = new Date();\n          return result;\n        }\n        \n        // Get the next step\n        currentStepId = this.getNextStep(currentStepId, result);\n      } catch (error) {\n        const endTime = new Date();\n        const errorResult: WorkflowStepResult = {\n          success: false,\n          error: error instanceof Error ? error.message : String(error),\n          output: undefined,\n          metadata: {\n            error: {\n              name: error instanceof Error ? error.name : 'Error',\n              message: error instanceof Error ? error.message : String(error),\n              stack: error instanceof Error ? error.stack : undefined,\n              timestamp: new Date().toISOString()\n            }\n          }\n        };\n        \n        // Record the failed step\n        const historyEntry: WorkflowHistoryEntry = {\n          stepId: currentStepId ?? 'unknown',\n          startTime,\n          endTime: endTime || new Date(), // Ensure endTime is always a Date\n          result: {\n            success: errorResult.success,\n            output: errorResult.output,\n            error: errorResult.error,\n            nextStepId: errorResult.nextStepId,\n            metadata: errorResult.metadata || {}\n          }\n        };\n        \n        context.history.push(historyEntry);\n        context.status = 'failed';\n        context.endTime = endTime;\n        \n        // Emit step error event\n        this._emitter.emit('stepError', { \n          stepId: currentStepId, \n          error: error as Error,\n          duration: endTime.getTime() - startTime.getTime()\n        });\n        \n        return errorResult;\n      }\n    }\n    \n    // Workflow completed successfully\n    context.status = 'completed';\n    context.endTime = new Date();\n    \n    // Emit workflow complete event\n    this._emitter.emit('complete', { \n      context,\n      result: lastResult || { success: true, output: 'Workflow completed' }\n    });\n    \n    return lastResult || { success: true, output: 'Workflow completed' };\n  }\n\n  /**\n   * Add an event listener\n   */\n  public on(event: 'stepStart' | 'stepComplete' | 'stepError' | 'complete', listener: (...args: any[]) => void): void {\n    this._emitter.on(event, listener);\n  }\n\n  /**\n   * Remove an event listener\n   */\n  public off(event: 'stepStart' | 'stepComplete' | 'stepError' | 'complete', listener: (...args: any[]) => void): void {\n    this._emitter.off(event, listener);\n  }\n\n  /**\n   * Create a shallow copy of the workflow\n   */\n  public clone(): Workflow {\n    const workflow = new Workflow(this._id, this._name);\n    \n    // Clone steps\n    for (const step of this._steps.values()) {\n      workflow.addStep(step);\n    }\n    \n    // Clone transitions\n    for (const [fromStepId, toStepIds] of this._transitions.entries()) {\n      for (const toStepId of toStepIds) {\n        workflow.addTransition(fromStepId, toStepId);\n      }\n    }\n    \n    // Set initial step\n    if (this._initialStepId) {\n      workflow.setInitialStep(this._initialStepId);\n    }\n    \n    return workflow;\n  }\n}\n"]}