{"version": 3, "file": "webReadTool.js", "sourceRoot": "", "sources": ["../../src/tools/webReadTool.ts"], "names": [], "mappings": ";;;;;;AAEA,kDAA0B;AAC1B,yDAA4E;AAC5E,6BAAwB;AAExB,MAAa,WAAW;IACb,EAAE,GAAG,SAAS,CAAC;IACf,IAAI,GAAG,qBAAqB,CAAC;IAC7B,WAAW,GAAG,gEAAgE,CAAC;IAC/E,IAAI,GAAG,cAAc,CAAC,CAAC,oBAAoB;IAC3C,OAAO,GAAwB;QACtC,MAAM,EAAE;YACN,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,6CAA6C;YAC1D,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;aAC7C,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,kBAAkB,EAAE;iBACzD;gBACD,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;gBACrF,MAAM,GAAG,GAAG,KAAK,CAAC,GAAa,CAAC;gBAChC,IAAI,CAAC,GAAG,EAAE,CAAC;oBACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;gBACvF,CAAC;gBACD,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;oBACrD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC;oBAC9B,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;wBACvE,MAAM,EAAE,MAAM;wBACd,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,uBAAuB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;wBACtD,MAAM,EAAE,MAAM;wBACd,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;SACF;QACD,SAAS,EAAE;YACT,GAAG,IAAI,wCAAqB,EAAE;YAC9B,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,GAAG,GAAG,KAAK,CAAC,GAAa,CAAC;gBAChC,IAAI,CAAC,GAAG,EAAE,CAAC;oBACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC;gBAC7F,CAAC;gBACD,wFAAwF;gBACxF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,2BAA2B,GAAG,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC;YACvG,CAAC;SACF;QACD,UAAU,EAAE;YACV,GAAG,IAAI,kCAAe,EAAE;YACxB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,GAAG,GAAG,KAAK,CAAC,GAAa,CAAC;gBAChC,IAAI,CAAC,GAAG,EAAE,CAAC;oBACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;gBAC9F,CAAC;gBACD,4EAA4E;gBAC5E,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,sBAAsB,GAAG,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;YACnG,CAAC;SACF;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,qEAAqE;QACrE,IAAI,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC5C,gEAAgE;YAChE,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACnC,mDAAmD;gBACnD,OAAO,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,6DAA6D;gBAC7D,MAAM,MAAM,GAAG,MAAO,UAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACjE,6DAA6D;gBAC7D,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;oBACjC,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;gBACjC,CAAC;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,kEAAkE;QAClE,MAAM,GAAG,GAAG,KAAK,CAAC,GAAa,CAAC;QAChC,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACxF,CAAC;QACD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC9B,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBACvE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uBAAuB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACtD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AApHD,kCAoHC;AAEY,QAAA,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC", "sourcesContent": ["import { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport axios from 'axios';\nimport { WebContentExtractTool, WebSnapshotTool } from './advancedWebTools';\nimport { z } from 'zod';\n\nexport class WebReadTool implements ITool {\n  readonly id = 'webRead';\n  readonly name = 'Web Read (Advanced)';\n  readonly description = 'Reads and extracts content, snapshot, and more from web pages.';\n  readonly type = 'multi-action'; // Required by ITool\n  readonly actions: Record<string, any> = {\n    'read': {\n      id: 'read',\n      name: 'Read Web Page',\n      description: 'Read the content of a web page (HTML/text).',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        url: z.string().describe('The URL to read.')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          url: { type: 'string', description: 'The URL to read.' }\n        },\n        required: ['url']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n        const url = input.url as string;\n        if (!url) {\n          return { success: false, error: '\\'url\\' is required.', toolId: 'read', actionName };\n        }\n        try {\n          const response = await axios({ method: 'get', url });\n          const content = response.data;\n          return {\n            success: true,\n            output: typeof content === 'string' ? content : JSON.stringify(content),\n            toolId: 'read',\n            actionName\n          };\n        } catch (error: any) {\n          return {\n            success: false,\n            error: `Failed to read URL: ${error.message || error}`,\n            toolId: 'read',\n            actionName\n          };\n        }\n      }\n    },\n    'extract': {\n      ...new WebContentExtractTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const url = input.url as string;\n        if (!url) {\n          return { success: false, error: '\\'url\\' is required.', toolId: 'webExtract', actionName };\n        }\n        // Placeholder: In real implementation, use a library like mercury-parser or readability\n        return { success: true, output: `Extracted content from: ${url}`, toolId: 'webExtract', actionName };\n      }\n    },\n    'snapshot': {\n      ...new WebSnapshotTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const url = input.url as string;\n        if (!url) {\n          return { success: false, error: '\\'url\\' is required.', toolId: 'webSnapshot', actionName };\n        }\n        // Placeholder: Actual screenshot would require headless browser integration\n        return { success: true, output: `Snapshot data for: ${url}`, toolId: 'webSnapshot', actionName };\n      }\n    },\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // If an action is specified, delegate to the appropriate action tool\n    if (actionName && this.actions[actionName]) {\n      const actionTool = this.actions[actionName];\n      // Check if the action tool has the new execute method signature\n      if (actionTool.execute.length >= 2) {\n        // Pass the actionName parameter to the nested tool\n        return actionTool.execute(actionName, input, context);\n      } else {\n        // Fallback for older tools that don't have the new signature\n        const result = await (actionTool as any).execute(input, context);\n        // Add the actionName to the result if it's not already there\n        if (result && !result.actionName) {\n          result.actionName = actionName;\n        }\n        return result;\n      }\n    }\n\n    // Default behavior (backward compatibility) - treat as a web read\n    const url = input.url as string;\n    if (!url) {\n      return { success: false, error: '\\'url\\' is required.', toolId: this.id, actionName };\n    }\n    try {\n      const response = await axios({ method: 'get', url });\n      const content = response.data;\n      return {\n        success: true,\n        output: typeof content === 'string' ? content : JSON.stringify(content),\n        toolId: this.id,\n        actionName\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Failed to read URL: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n}\n\nexport const webReadTool = new WebReadTool();\n"]}