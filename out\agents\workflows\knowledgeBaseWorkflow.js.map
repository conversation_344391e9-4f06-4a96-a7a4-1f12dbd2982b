{"version": 3, "file": "knowledgeBaseWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/knowledgeBaseWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAYH,kEAsFC;AAKD,4EA2DC;AA/JD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,2BAA2B,CACzC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,cAAqB,EACrB,QAA0B,EAAE;IAE5B,eAAM,CAAC,IAAI,CAAC,gDAAgD,IAAI,EAAE,CAAC,CAAC;IAEpE,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAClH,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAClH,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,cAAc,CAAC,CAAC;IAC9H,MAAM,YAAY,GAAG,eAAO,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACrF,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,CAAC,CAAC;IACrH,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,cAAc,CAAC,CAAC;IAC/G,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9F,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChH,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvH,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9G,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAE7F,iBAAiB;QACjB,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,UAAU,EAAE;KACrH,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,sCAAsC;QACtC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,sBAAsB,KAAK,EAAE;gBACnC,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,+CAA+C;YAC/C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,kBAAkB;gBACrC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,wBAAwB;gBAChC,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,qBAAqB;YACrB,qBAAqB;YACrB,yBAAyB;YACzB,YAAY;YACZ,sBAAsB;YACtB,oBAAoB;YACpB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,gBAAiC;QAChD,IAAI,EAAE,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,uBAAuB,CAAC;KACzE,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,gCAAgC,CAC9C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,cAAqB;IAErB,eAAM,CAAC,IAAI,CAAC,0CAA0C,IAAI,EAAE,CAAC,CAAC;IAE9D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACtG,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;IACzG,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACtG,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAClH,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACtG,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAClH,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,CAAC,CAAC;IACrH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtF,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KACnG,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,iBAAiB;YACjB,kBAAkB;YAClB,iBAAiB;YACjB,qBAAqB;YACrB,iBAAiB;YACjB,qBAAqB;YACrB,sBAAsB;YACtB,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,gBAAiC;QAChD,IAAI,EAAE,CAAC,qBAAqB,EAAE,QAAQ,EAAE,uBAAuB,CAAC;KACjE,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Knowledge Base Management Workflow\n *\n * This module provides a workflow for managing knowledge bases:\n * - Ingesting and processing documents\n * - Organizing knowledge\n * - Retrieving relevant information\n */\n\nimport { Agent } from '../agentUtilities/agent';\nimport { Codessa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n\n/**\n * Create a Knowledge Base Management workflow\n */\nexport function createKnowledgeBaseWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  knowledgeAgent: Agent,\n  tools: StructuredTool[] = []\n): GraphDefinition {\n  logger.info(`Creating Knowledge Base Management workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const documentIngestionNode = Codessa.createAgentNode('document-ingestion', 'Document Ingestion', knowledgeAgent);\n  const contentExtractionNode = Codessa.createAgentNode('content-extraction', 'Content Extraction', knowledgeAgent);\n  const knowledgeOrganizationNode = Codessa.createAgentNode('knowledge-organization', 'Knowledge Organization', knowledgeAgent);\n  const indexingNode = Codessa.createAgentNode('indexing', 'Indexing', knowledgeAgent);\n  const metadataEnrichmentNode = Codessa.createAgentNode('metadata-enrichment', 'Metadata Enrichment', knowledgeAgent);\n  const qualityAssuranceNode = Codessa.createAgentNode('quality-assurance', 'Quality Assurance', knowledgeAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-ingestion', source: 'input', target: 'document-ingestion', type: 'default' },\n    { name: 'ingestion-to-extraction', source: 'document-ingestion', target: 'content-extraction', type: 'default' },\n    { name: 'extraction-to-organization', source: 'content-extraction', target: 'knowledge-organization', type: 'default' },\n    { name: 'organization-to-indexing', source: 'knowledge-organization', target: 'indexing', type: 'default' },\n    { name: 'indexing-to-enrichment', source: 'indexing', target: 'metadata-enrichment', type: 'default' },\n    { name: 'enrichment-to-quality', source: 'metadata-enrichment', target: 'quality-assurance', type: 'default' },\n    { name: 'quality-to-output', source: 'quality-assurance', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'quality-to-organization', source: 'quality-assurance', target: 'knowledge-organization', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect content extraction to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `extraction-to-tool-${index}`,\n        source: 'content-extraction',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to knowledge organization\n      edges.push({\n        name: `tool-${index}-to-organization`,\n        source: `tool-${index}`,\n        target: 'knowledge-organization',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      documentIngestionNode,\n      contentExtractionNode,\n      knowledgeOrganizationNode,\n      indexingNode,\n      metadataEnrichmentNode,\n      qualityAssuranceNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'knowledge-base' as OperationMode,\n    tags: ['knowledge-base', 'document-processing', 'information-retrieval']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized Knowledge Retrieval workflow\n */\nexport function createKnowledgeRetrievalWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  retrievalAgent: Agent\n): GraphDefinition {\n  logger.info(`Creating Knowledge Retrieval workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const queryAnalysisNode = Codessa.createAgentNode('query-analysis', 'Query Analysis', retrievalAgent);\n  const semanticSearchNode = Codessa.createAgentNode('semantic-search', 'Semantic Search', retrievalAgent);\n  const keywordSearchNode = Codessa.createAgentNode('keyword-search', 'Keyword Search', retrievalAgent);\n  const metadataFilteringNode = Codessa.createAgentNode('metadata-filtering', 'Metadata Filtering', retrievalAgent);\n  const resultRankingNode = Codessa.createAgentNode('result-ranking', 'Result Ranking', retrievalAgent);\n  const contextGenerationNode = Codessa.createAgentNode('context-generation', 'Context Generation', retrievalAgent);\n  const responseFormattingNode = Codessa.createAgentNode('response-formatting', 'Response Formatting', retrievalAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },\n    { name: 'query-to-semantic', source: 'query-analysis', target: 'semantic-search', type: 'default' },\n    { name: 'query-to-keyword', source: 'query-analysis', target: 'keyword-search', type: 'default' },\n    { name: 'semantic-to-filtering', source: 'semantic-search', target: 'metadata-filtering', type: 'default' },\n    { name: 'keyword-to-filtering', source: 'keyword-search', target: 'metadata-filtering', type: 'default' },\n    { name: 'filtering-to-ranking', source: 'metadata-filtering', target: 'result-ranking', type: 'default' },\n    { name: 'ranking-to-context', source: 'result-ranking', target: 'context-generation', type: 'default' },\n    { name: 'context-to-formatting', source: 'context-generation', target: 'response-formatting', type: 'default' },\n    { name: 'formatting-to-output', source: 'response-formatting', target: 'output', type: 'default' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      queryAnalysisNode,\n      semanticSearchNode,\n      keywordSearchNode,\n      metadataFilteringNode,\n      resultRankingNode,\n      contextGenerationNode,\n      responseFormattingNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'knowledge-base' as OperationMode,\n    tags: ['knowledge-retrieval', 'search', 'information-retrieval']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}