import { createHash } from 'crypto';
import { extname } from 'path';

/**
 * Error thrown when a security violation is detected
 */
export class SecurityError extends Error {
    constructor(message: string, public readonly details?: unknown) {
        super(message);
        this.name = 'SecurityError';
    }
}

/**
 * Security utilities for clipboard operations
 */
export class SecurityUtils {
    /** Maximum allowed file path length */
    private static readonly MAX_PATH_LENGTH = 255;
    
    /** Allowed file extensions for images */
    private static readonly ALLOWED_IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.webp', '.gif'];
    
    /** Dangerous HTML tags that could contain scripts */
    private static readonly DANGEROUS_HTML_TAGS = [
        'script',
        'iframe',
        'object',
        'embed',
        'form',
        'input',
        'applet',
        'meta',
        'base',
        'link',
        'style'
    ];

    /** Known dangerous mime types */
    private static readonly DANGEROUS_MIME_TYPES = [
        'text/javascript',
        'application/javascript',
        'application/x-javascript',
        'application/ecmascript',
        'text/ecmascript',
        'application/x-httpd-php',
        'application/x-sh',
        'application/x-csh',
        'text/x-script',
        'application/x-shellscript'
    ];

    /** Pattern for detecting potential command injection */
    private static readonly COMMAND_INJECTION_PATTERN = /[<>|&;$()]/;

    /**
     * Sanitize HTML content to prevent XSS attacks
     */
    public static sanitizeHtml(html: string): string {
        // Remove dangerous tags
        let sanitized = html;
        for (const tag of SecurityUtils.DANGEROUS_HTML_TAGS) {
            sanitized = sanitized.replace(
                new RegExp(`<${tag}[^>]*>.*?</${tag}>`, 'gis'),
                ''
            );
            sanitized = sanitized.replace(
                new RegExp(`<${tag}[^>]*/>`, 'gi'),
                ''
            );
        }

        // Remove on* event handlers
        sanitized = sanitized.replace(
            /\s+(on\w+)\s*=\s*["']?[^"'>]+["']?/gi,
            ''
        );

        // Remove javascript: URLs
        sanitized = sanitized.replace(
            /\s+href\s*=\s*["']?\s*javascript:[^"'>]+["']?/gi,
            ''
        );

        // Remove data: URLs except for safe image types
        sanitized = sanitized.replace(
            /\s+src\s*=\s*["']?\s*data:(?!image\/(jpeg|png|gif|webp);)[^"'>]+["']?/gi,
            ''
        );

        return sanitized;
    }

    /**
     * Validate and sanitize a file path
     */
    public static validatePath(path: string): boolean {
        if (!path || path.length > SecurityUtils.MAX_PATH_LENGTH) {
            return false;
        }

        // Check for path traversal attempts
        if (path.includes('..') || path.includes('\\\\')) {
            return false;
        }

        // Check for null bytes
        if (path.includes('\0')) {
            return false;
        }

        // Check for command injection characters
        if (SecurityUtils.COMMAND_INJECTION_PATTERN.test(path)) {
            return false;
        }

        return true;
    }

    /**
     * Validate image data for security
     */
    public static validateImageData(data: Buffer): boolean {
        // Check for known image file signatures
        const signatures = {
            png: Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]),
            jpeg: Buffer.from([0xFF, 0xD8, 0xFF]),
            gif87a: Buffer.from('GIF87a'),
            gif89a: Buffer.from('GIF89a'),
            webp: Buffer.from('RIFF')
        };

        // Check file signature
        return (
            data.slice(0, 8).equals(signatures.png) ||
            data.slice(0, 3).equals(signatures.jpeg) ||
            data.slice(0, 6).equals(signatures.gif87a) ||
            data.slice(0, 6).equals(signatures.gif89a) ||
            (data.slice(0, 4).equals(signatures.webp) &&
                data.slice(8, 12).toString() === 'WEBP')
        );
    }

    /**
     * Check if a mime type is potentially dangerous
     */
    public static isSafeMimeType(mimeType: string): boolean {
        return !SecurityUtils.DANGEROUS_MIME_TYPES.includes(mimeType.toLowerCase());
    }

    /**
     * Generate a secure hash for content validation
     */
    public static generateContentHash(content: Buffer | string): string {
        const buffer = Buffer.isBuffer(content) ? content : Buffer.from(content);
        return createHash('sha256').update(buffer).digest('hex');
    }

    /**
     * Validate base64 data
     */
    public static validateBase64(data: string): boolean {
        try {
            // Check if it's valid base64
            if (!/^[A-Za-z0-9+/]*={0,2}$/.test(data)) {
                return false;
            }

            // Check if decoded data contains any null bytes or control characters
            const decoded = Buffer.from(data, 'base64');
            return !decoded.includes(0x00) && 
                   !decoded.some(byte => byte < 32 && ![9, 10, 13].includes(byte));
        } catch {
            return false;
        }
    }

    /**
     * Calculate entropy of data to detect potential malicious content
     */
    public static calculateEntropy(data: Buffer | string): number {
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
        const frequencies = new Array(256).fill(0);
        
        for (const byte of buffer) {
            frequencies[byte]++;
        }

        let entropy = 0;
        const length = buffer.length;

        for (const freq of frequencies) {
            if (freq > 0) {
                const probability = freq / length;
                entropy -= probability * Math.log2(probability);
            }
        }

        return entropy;
    }

    /**
     * Detect potential shellcode in binary data
     */
    public static detectShellcode(data: Buffer): boolean {
        // Check for repeated characters (like NOP sleds)
        const hexData = data.toString('hex');
        if (/(.)\1{15,}/.test(hexData)) {
            return true;
        }

        // Check for known shellcode signatures
        const signatures = [
            Buffer.from([0x90, 0x90, 0x90, 0x90, 0x90]), // NOP sled
            Buffer.from([0x00, 0x00, 0x00, 0x00, 0x00])  // Null bytes
        ];

        return signatures.some(sig => data.includes(sig));
    }

    /**
     * Validate file extension
     */
    public static isAllowedImageExtension(filename: string): boolean {
        const ext = extname(filename).toLowerCase();
        return SecurityUtils.ALLOWED_IMAGE_EXTENSIONS.includes(ext);
    }
}
