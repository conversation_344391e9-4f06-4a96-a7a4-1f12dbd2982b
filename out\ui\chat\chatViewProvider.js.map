{"version": 3, "file": "chatViewProvider.js", "sourceRoot": "", "sources": ["../../../src/ui/chat/chatViewProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAuC;AACvC,sDAAyD;AACzD,2EAAwE;AACxE,6EAA8G;AAC9G,yCAAsD;AACtD,yCAAsC;AACtC,qDAAkD;AAElD,8DAA2D;AAC3D,uEAAiE;AACjE,mEAAgE;AAChE,8DAA2D;AAC3D,+DAA4D;AAC5D,mCAAsC;AAEtC,MAAa,gBAAgB;IAiBR;IACA;IAjBZ,MAAM,CAAU,QAAQ,GAAG,kBAAkB,CAAC;IAC7C,KAAK,CAAsB;IAC3B,YAAY,GAAwB,EAAE,CAAC;IACvC,YAAY,CAAc;IAC1B,aAAa,CAAe;IAC5B,gBAAgB,CAAkB;IAClC,gBAAgB,CAAmB;IAE3C,6BAA6B;IACrB,iBAAiB,GAAiB,IAAI,qBAAY,EAAE,CAAC;IACrD,cAAc,GAAkC,IAAI,GAAG,EAAE,CAAC;IAC1D,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;IAEjD,yBAAyB,CAAkC;IAEnE,YACmB,aAAyB,EACzB,QAAiC;QADjC,kBAAa,GAAb,aAAa,CAAY;QACzB,aAAQ,GAAR,QAAQ,CAAyB;QAElD,IAAI,CAAC,YAAY,GAAG,IAAI,sBAAW,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,gBAAgB,GAAG,oCAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC9D,4DAA4D;QAC5D,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,8BAA8B;QAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;YAC9C,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,kEAAkE;QAClE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED;;OAEG;IACI,sBAAsB;QAC3B,MAAM,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,eAAe,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC;QAC1D,IAAI,CAAC,gBAAgB,GAAG,eAAe,YAAY,iCAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC;QAEjG,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC/E,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAEM,kBAAkB,CACvB,WAA+B,EAC/B,QAA0C,EAC1C,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC5B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAClB,IAAI,CAAC,aAAa;aACnB;SACF,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,wFAAwF;QACxF,KAAK,QAAQ,CAAC,CAAC,6EAA6E;QAC5F,MAAM,CAAC,uBAAuB,CAAC,GAAG,EAAE;YAClC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,+DAA+D;QAC/D,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACxD,gCAAgC;YAChC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE;oBACJ,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;oBACzC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;iBAChD;aACF,CAAC,CAAC;YAEH,sDAAsD;YACtD,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YAC3E,4BAA4B;YAC5B,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE;oBACJ,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;oBACzC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;iBAChD;aACF,CAAC,CAAC;YAEH,wDAAwD;YACxD,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACrD,2DAA2D;YAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;YAC9C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;YAEzE,QAAQ,WAAW,EAAE,CAAC;gBACpB,KAAK,iBAAiB,CAAC,CAAC,CAAC;oBACvB,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,IAAI,UAAU,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;wBAC3D,8BAA8B;wBAC9B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;wBAEjE,uCAAuC;wBACvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;wBAE5D,yBAAyB;wBACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,QAAQ;4BACd,MAAM;4BACN,QAAQ,EAAE,UAAU;yBACrB,CAAC,CAAC;wBAEH,iCAAiC;wBACjC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACtB,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;4BAChE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,cAAc;gCACpB,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;6BACpB,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,cAAc,CAAC,CAAC,CAAC;oBACpB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;oBAC3B,IAAI,OAAO,EAAE,CAAC;wBACZ,0CAA0C;wBAC1C,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;wBAC3D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,OAAO,EAAE,CAAC,CAAC;oBACvD,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,aAAa,CAAC,CAAC,CAAC;oBACnB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;oBACpE,IAAI,CAAC,WAAW,EAAE,CAAC;wBACjB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;wBAClE,MAAM;oBACR,CAAC;oBAED,gCAAgC;oBAChC,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;wBACnC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC;wBACxC,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,CAAC;oBAC3C,CAAC;oBACD,IAAI,CAAC,yBAAyB,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;oBACtE,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC;oBAE/D,mBAAmB;oBACnB,MAAM,WAAW,GAAgB;wBAC/B,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;wBACxB,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,WAAW;wBACpB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;qBACtB,CAAC;oBACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;oBAC1C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;oBACtC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;oBAEzB,mCAAmC;oBACnC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;wBAC9B,IAAI,EAAE,iBAAiB;wBACvB,YAAY,EAAE,IAAI;qBACnB,CAAC,CAAC;oBAEH,IAAI,CAAC;wBACH,+CAA+C;wBAC/C,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,QAAQ,CAAC;wBACvF,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,QAAQ,CAAC;wBAEjF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,gBAAgB,YAAY,aAAa,EAAE,CAAC,CAAC;wBAErF,+DAA+D;wBAC/D,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;wBAC5D,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;wBAC7F,MAAM,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,aAAa,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;wBAEvF,iDAAiD;wBACjD,MAAM,QAAQ,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;wBACtC,MAAM,gBAAgB,GAAqB;4BACzC,QAAQ;4BACR,QAAQ,EAAE,CAAC,KAAuB,EAAE,EAAE;gCACpC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;4BAClD,CAAC;4BACD,iBAAiB;yBAClB,CAAC;wBAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;wBAEpD,MAAM,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;wBAChD,MAAM,aAAa,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC;wBACtD,MAAM,eAAe,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC;wBAC1D,MAAM,eAAe,GACnB,eAAe,YAAY,iCAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC;wBAE3E,IAAI,CAAC,aAAa,IAAI,CAAC,eAAe,EAAE,CAAC;4BACvC,MAAM,aAAa,GAAgB;gCACjC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;gCACzB,IAAI,EAAE,OAAO;gCACb,OAAO,EAAE,yDAAyD;gCAClE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;6BACtB,CAAC;4BAEF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;4BAC5C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;4BACvC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;4BAEzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,YAAY;gCAClB,OAAO,EAAE,aAAa;6BACvB,CAAC,CAAC;4BAEH,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,iBAAiB;gCACvB,YAAY,EAAE,KAAK;6BACpB,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBAED,kCAAkC;wBAClC,MAAM,uBAAuB,GAAgB;4BAC3C,EAAE,EAAE,qBAAqB,IAAI,CAAC,GAAG,EAAE,EAAE;4BACrC,IAAI,EAAE,WAAW;4BACjB,OAAO,EAAE,4CAA4C;4BACrD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;4BACrB,UAAU,EAAE,IAAI;yBACjB,CAAC;wBACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC;wBACtD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,YAAY;4BAClB,OAAO,EAAE,uBAAuB;yBACjC,CAAC,CAAC;wBAEH,wCAAwC;wBACxC,MAAM,cAAc,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC;4BAC7C,MAAM,EAAE,WAAW;4BACnB,IAAI,EAAE,MAAM;yBACb,EAAE;4BACD,SAAS,EAAE,2BAAe,CAAC,sBAAsB,EAAE;4BACnD,SAAS,EAAE;gCACT,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;6BACpD;4BACD,gBAAgB;4BAChB,iBAAiB;yBAClB,CAAC,CAAC;wBAEH,+CAA+C;wBAC/C,uBAAuB,CAAC,OAAO,GAAG,uFAAuF,CAAC;wBAC1H,uBAAuB,CAAC,UAAU,GAAG,KAAK,CAAC;wBAC3C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;wBACzD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,eAAe;4BACrB,OAAO,EAAE,uBAAuB;yBACjC,CAAC,CAAC;wBAEH,0CAA0C;wBAC1C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;wBAEvD,IAAI,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;4BAC9C,MAAM,YAAY,GAAgB;gCAChC,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,EAAE;gCAC7B,IAAI,EAAE,WAAW;gCACjB,OAAO,EAAE,sBAAsB;gCAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;6BACtB,CAAC;4BACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;4BAC3C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;4BACvC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;4BACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,YAAY;gCAClB,OAAO,EAAE,YAAY;6BACtB,CAAC,CAAC;4BACH,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,iBAAiB;gCACvB,YAAY,EAAE,KAAK;6BACpB,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBAED,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;4BAC5B,MAAM,IAAI,KAAK,CAAC,yBAAyB,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;wBACnE,CAAC;wBAED,oCAAoC;wBACpC,MAAM,yBAAyB,GAAgB;4BAC7C,EAAE,EAAE,uBAAuB,IAAI,CAAC,GAAG,EAAE,EAAE;4BACvC,IAAI,EAAE,WAAW;4BACjB,OAAO,EAAE,qEAAqE;4BAC9E,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;4BACrB,UAAU,EAAE,IAAI;yBACjB,CAAC;wBACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,yBAAyB,CAAC,CAAC;wBACxD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,YAAY;4BAClB,OAAO,EAAE,yBAAyB;yBACnC,CAAC,CAAC;wBAEH,wDAAwD;wBACxD,MAAM,gBAAgB,GAAG,MAAM,eAAe,CAAC,GAAG,CAAC;4BACjD,MAAM,EAAE,cAAc,CAAC,MAAM,IAAI,EAAE;4BACnC,IAAI,EAAE,MAAM;yBACb,EAAE;4BACD,SAAS,EAAE,2BAAe,CAAC,sBAAsB,EAAE;4BACnD,SAAS,EAAE;gCACT,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;gCACnD,cAAc,EAAE,cAAc,CAAC,MAAM;6BACtC;4BACD,gBAAgB;4BAChB,iBAAiB;yBAClB,CAAC,CAAC;wBAEH,IAAI,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;4BAC9C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;4BAC9D,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;4BACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,eAAe;gCACrB,SAAS,EAAE,yBAAyB,CAAC,EAAE;6BACxC,CAAC,CAAC;4BACH,MAAM,YAAY,GAAgB;gCAChC,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,EAAE;gCAC7B,IAAI,EAAE,WAAW;gCACjB,OAAO,EAAE,sBAAsB;gCAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;6BACtB,CAAC;4BACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;4BAC3C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;4BACvC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;4BACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,YAAY;gCAClB,OAAO,EAAE,YAAY;6BACtB,CAAC,CAAC;4BACH,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,iBAAiB;gCACvB,YAAY,EAAE,KAAK;6BACpB,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBAED,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;4BAC9B,qCAAqC;4BACrC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;4BAC9D,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;4BACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,eAAe;gCACrB,SAAS,EAAE,yBAAyB,CAAC,EAAE;6BACxC,CAAC,CAAC;4BACH,MAAM,IAAI,KAAK,CAAC,2BAA2B,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC;wBACvE,CAAC;wBAED,wDAAwD;wBACxD,yBAAyB,CAAC,OAAO,GAAG,iEAAiE,CAAC;wBACtG,yBAAyB,CAAC,UAAU,GAAG,KAAK,CAAC;wBAC7C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;wBAC3D,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,eAAe;4BACrB,OAAO,EAAE,yBAAyB;yBACnC,CAAC,CAAC;wBAEH,4CAA4C;wBAC5C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;wBAEvD,+CAA+C;wBAC/C,MAAM,gBAAgB,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC;wBAC5D,IAAI,WAAW,GAAG,gBAAgB,CAAC,MAAM,CAAC;wBAE1C,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE,CAAC;4BACrC,MAAM,wBAAwB,GAAgB;gCAC5C,EAAE,EAAE,sBAAsB,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gCAClD,IAAI,EAAE,WAAW;gCACjB,OAAO,EAAE,QAAQ,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE,iCAAiC;gCACxE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gCACrB,UAAU,EAAE,IAAI;6BACjB,CAAC;4BACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC;4BACvD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;4BACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,YAAY;gCAClB,OAAO,EAAE,wBAAwB;6BAClC,CAAC,CAAC;4BAEH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC;gCAC7B,MAAM,EAAE,WAAW,IAAI,EAAE;gCACzB,IAAI,EAAE,MAAM;6BACb,EAAE;gCACD,SAAS,EAAE,2BAAe,CAAC,sBAAsB,EAAE;gCACnD,SAAS,EAAE;oCACT,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;oCACnD,cAAc,EAAE,cAAc,CAAC,MAAM;oCACrC,gBAAgB,EAAE,gBAAgB,CAAC,MAAM;iCAC1C;gCACD,gBAAgB;gCAChB,iBAAiB;6BAClB,CAAC,CAAC;4BAEH,gDAAgD;4BAChD,wBAAwB,CAAC,OAAO,GAAG,OAAO,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE,mBAAmB,CAAC;4BACpF,wBAAwB,CAAC,UAAU,GAAG,KAAK,CAAC;4BAC5C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;4BAC1D,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;4BACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gCAC9B,IAAI,EAAE,eAAe;gCACrB,OAAO,EAAE,wBAAwB;6BAClC,CAAC,CAAC;4BAEH,IAAI,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;gCAC9C,MAAM,YAAY,GAAgB;oCAChC,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,EAAE;oCAC7B,IAAI,EAAE,WAAW;oCACjB,OAAO,EAAE,sBAAsB;oCAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iCACtB,CAAC;gCACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;gCAC3C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gCACvC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gCACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;oCAC9B,IAAI,EAAE,YAAY;oCAClB,OAAO,EAAE,YAAY;iCACtB,CAAC,CAAC;gCACH,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;oCAC9B,IAAI,EAAE,iBAAiB;oCACvB,YAAY,EAAE,KAAK;iCACpB,CAAC,CAAC;gCACH,OAAO;4BACT,CAAC;4BAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gCACpC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;4BAC9B,CAAC;4BAED,6BAA6B;4BAC7B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;wBACzD,CAAC;wBAED,qDAAqD;wBACrD,MAAM,gBAAgB,GAAgB;4BACpC,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,EAAE;4BAC7B,IAAI,EAAE,WAAW;4BACjB,OAAO,EAAE,WAAW,IAAI,uEAAuE;4BAC/F,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;yBACtB,CAAC;wBAEF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;wBAC/C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;wBACvC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBAEzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,YAAY;4BAClB,OAAO,EAAE,gBAAgB;yBAC1B,CAAC,CAAC;wBAEH,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,iBAAiB;4BACvB,YAAY,EAAE,KAAK;yBACpB,CAAC,CAAC;wBAEH,6BAA6B;wBAC7B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBAErC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;oBAEpE,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;wBAC1D,MAAM,YAAY,GAAgB;4BAChC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;4BACzB,IAAI,EAAE,OAAO;4BACb,OAAO,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;4BAC3E,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;yBACtB,CAAC;wBAEF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;wBAC3C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;wBACvC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBAEzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,YAAY;4BAClB,OAAO,EAAE,YAAY;yBACtB,CAAC,CAAC;wBAEH,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,iBAAiB;4BACvB,YAAY,EAAE,KAAK;yBACpB,CAAC,CAAC;oBACL,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,cAAc,CAAC,CAAC,CAAC;oBACpB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;oBAC/C,IAAI,CAAC;wBACH,wBAAwB;wBACxB,MAAM,aAAa,GAAG,6BAAa,CAAC,WAAW,EAAE,CAAC;wBAClD,MAAM,eAAe,GAAG,iCAAe,CAAC,WAAW,EAAE,CAAC;wBAEtD,4BAA4B;wBAC5B,MAAM,mCAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;oBACzG,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;wBAC/D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;oBAClE,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,WAAW,CAAC,CAAC,CAAC;oBACjB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;oBACzB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,KAAK,EAAE,CAAC,CAAC;oBAC9C,IAAI,CAAC;wBACH,mBAAmB;wBACnB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBAC1C,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;oBACxD,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,WAAW,CAAC,CAAC,CAAC;oBACjB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;oBAC9C,IAAI,CAAC;wBACH,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;wBAClC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBACzB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;4BAC9B,IAAI,EAAE,eAAe;yBACtB,CAAC,CAAC;oBACL,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;oBACxD,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,YAAY,CAAC,CAAC,CAAC;oBAClB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBACvC,IAAI,CAAC;wBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;wBACjD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;wBAErD,mBAAmB;wBACnB,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;4BAC7C,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,0BAA0B,CAAC;4BACvD,OAAO,EAAE;gCACP,YAAY,EAAE,CAAC,MAAM,CAAC;gCACtB,WAAW,EAAE,CAAC,GAAG,CAAC;6BACnB;yBACF,CAAC,CAAC;wBAEH,IAAI,GAAG,EAAE,CAAC;4BACR,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;4BAC1E,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;wBACrE,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;wBACvD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;oBAC1D,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,4CAA4C;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1E,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAChD,MAAM,KAAK,GAAG,IAAA,gBAAQ,GAAE,CAAC;QACzB,MAAM,GAAG,GAAG,+BAA+B,OAAO,CAAC,SAAS,oCAAoC,KAAK,gCAAgC,OAAO,CAAC,SAAS,GAAG,CAAC;QAE1J,oBAAoB;QACpB,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;QACtG,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QAEpG,OAAO;;;;wDAI6C,GAAG;;;iCAG1B,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBA0ExB,KAAK;;;;;;;;;;;;;mBAaL,KAAK,UAAU,SAAS;;QAEnC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAO5B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,uBAAU,CAAC,eAAe,EAAE,CAAC;YAC/C,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACzB,EAAE,EAAE,CAAC,CAAC,UAAU;gBAChB,IAAI,EAAE,CAAC,CAAC,WAAW;gBACnB,WAAW,EAAE,CAAC,CAAC,WAAW;gBAC1B,UAAU,EAAE,CAAC,CAAC,YAAY,EAAE;gBAC5B,OAAO,EAAE,CAAC,CAAC,UAAU,KAAK,QAAQ,IAAI,CAAC,CAAC,UAAU,KAAK,UAAU;aAClE,CAAC,CAAC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,UAAkB;QACpD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACpD,IAAI,QAAQ,IAAI,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAC3C,OAAO,MAAM,CAAC;YAChB,CAAC;YACD,OAAO,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,4BAA4B,CAAC,UAAkB;QACrD,MAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,QAAQ,EAAE,YAAY,EAAE,CAAC;YAC3B,OAAO,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;QACzC,CAAC;QACD,iDAAiD;QACjD,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC5B,OAAO,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC5B,OAAO,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,OAAO,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,WAA+B;QAC7D,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChD,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,WAAW;gBACjB,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,WAA+B,EAAE,UAAmB;QACvF,IAAI,CAAC;YACH,IAAI,gBAAgB,GAAG,UAAU,IAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAY,CAAC;YACnG,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,MAAM,GAAG,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;gBAClD,gBAAgB,GAAG,GAAG,EAAE,UAAU,IAAI,QAAQ,CAAC;YACjD,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;YAClE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,QAAQ;gBACd,MAAM;gBACN,QAAQ,EAAE,gBAAgB;aAC3B,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,YAAY,GAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAY,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACjG,IAAI,YAAY,EAAE,CAAC;gBACjB,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,YAAY;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,6BAA6B,CAAC,WAA+B;QACzE,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAE1C,oCAAoC;YACpC,IAAI,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAuB,CAAC;YAC/F,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,MAAM,GAAG,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;gBAClD,gBAAgB,GAAG,GAAG,EAAE,UAAU,IAAI,QAAQ,CAAC;gBAC/C,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YACzE,CAAC;YAED,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9C,CAAC;IAEO,YAAY,CAAC,QAAgB;QACnC,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACpD,MAAM,GAAG,GAA2B;YAClC,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,iBAAiB;YACvB,IAAI,EAAE,iBAAiB;YACvB,MAAM,EAAE,kBAAkB;YAC1B,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,eAAe;YACtB,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,YAAY;SACpB,CAAC;QACF,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,0BAA0B,CAAC;IACtD,CAAC;IAEO,sBAAsB,CAAC,KAAuB,EAAE,WAA+B;QACrF,IAAI,CAAC;YACH,kDAAkD;YAClD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACrD,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YACtD,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAClC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACnD,CAAC;YAED,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,aAAa;gBACnB,KAAK;aACN,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAEM,kBAAkB;QACvB,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;YACvC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;SACtD,CAAC;IACJ,CAAC;IAEM,MAAM;QACX,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,0CAA0C,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;;AAj1BH,4CAk1BC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { getNonce } from '../../utils';\nimport { WorkspaceHelper } from '../workspace/workspace';\nimport { AgentManager } from '../../agents/agentUtilities/agentManager';\nimport { SupervisorAgent, AgentStreamEvent, StreamingContext } from '../../agents/agentTypes/supervisorAgent';\nimport { ChatMessage, ChatSession } from './chatView';\nimport { Logger } from '../../logger';\nimport { llmService } from '../../llm/llmService';\nimport { LLMModelInfo } from '../../llm/llmProvider';\nimport { AudioService } from '../../services/audioService';\nimport { TTSSettingsView } from '../settings/ttsSettingsSection';\nimport { AllSettingsPanel } from '../settings/allSettingsPanel';\nimport { MemoryManager } from '../../memory/memoryManager';\nimport { ProviderManager } from '../../llm/providerManager';\nimport { EventEmitter } from 'events';\n\nexport class ChatViewProvider implements vscode.WebviewViewProvider {\n  public static readonly viewType = 'codessa.chatView';\n  private _view?: vscode.WebviewView;\n  private _disposables: vscode.Disposable[] = [];\n  private _chatSession: ChatSession;\n  private _audioService: AudioService;\n  private _ttsSettingsView: TTSSettingsView;\n  private _supervisorAgent?: SupervisorAgent;\n\n  // Enhanced streaming support\n  private _streamingEmitter: EventEmitter = new EventEmitter();\n  private _activeStreams: Map<string, StreamingContext> = new Map();\n  private _agentStatusMap: Map<string, string> = new Map();\n\n  private _currentCancelTokenSource?: vscode.CancellationTokenSource;\n\n  constructor(\n    private readonly _extensionUri: vscode.Uri,\n    private readonly _context: vscode.ExtensionContext\n  ) {\n    this._chatSession = new ChatSession(_context);\n    this._audioService = AudioService.getInstance(_context);\n    this._ttsSettingsView = TTSSettingsView.getInstance(_context);\n    // Initialize supervisor agent reference (will be set later)\n    this._supervisorAgent = undefined;\n    this.refreshAgentReferences();\n\n    // Load any saved chat session\n    this._chatSession.load().catch((error: Error) => {\n      Logger.instance.error('Failed to load chat session:', error);\n    });\n\n    // Ensure the setting exists and defaults to [\"ollama\"] if not set\n    const config = vscode.workspace.getConfiguration('codessa.llm');\n    if (!config.get('enabledProviders')) {\n      config.update('enabledProviders', ['ollama'], vscode.ConfigurationTarget.Global);\n    }\n  }\n\n  /**\n   * Refresh agent references from AgentManager\n   */\n  public refreshAgentReferences(): void {\n    const agentManager = AgentManager.getInstance();\n    const maybeSupervisor = agentManager.getSupervisorAgent();\n    this._supervisorAgent = maybeSupervisor instanceof SupervisorAgent ? maybeSupervisor : undefined;\n\n    if (this._supervisorAgent) {\n      Logger.instance.info('ChatViewProvider: Supervisor agent reference updated');\n    } else {\n      Logger.instance.warn('ChatViewProvider: No supervisor agent available');\n    }\n  }\n\n  public resolveWebviewView(\n    webviewView: vscode.WebviewView,\n    _context: vscode.WebviewViewResolveContext,\n    _token: vscode.CancellationToken,\n  ) {\n    this._view = webviewView;\n\n    webviewView.webview.options = {\n      enableScripts: true,\n      localResourceRoots: [\n        this._extensionUri\n      ]\n    };\n\n    webviewView.webview.html = this._getWebviewContent(webviewView.webview);\n\n    // Use the provided resolve context and cancellation token to satisfy lints meaningfully\n    void _context; // explicit usage to indicate we intentionally ignore additional details here\n    _token.onCancellationRequested(() => {\n      Logger.instance.info('Chat view resolve was cancelled');\n    });\n\n    // Initialize providers and models before sending initial state\n    this._initializeProvidersAndModels(webviewView).then(() => {\n      // Send initial state to webview\n      webviewView.webview.postMessage({\n        type: 'initialize',\n        data: {\n          messages: this._chatSession.getMessages(),\n          isProcessing: this._chatSession.getProcessing()\n        }\n      });\n\n      // Send provider info immediately after initialization\n      this._sendProviderInfo(webviewView);\n    }).catch(error => {\n      Logger.instance.error('Failed to initialize providers and models:', error);\n      // Send initial state anyway\n      webviewView.webview.postMessage({\n        type: 'initialize',\n        data: {\n          messages: this._chatSession.getMessages(),\n          isProcessing: this._chatSession.getProcessing()\n        }\n      });\n\n      // Still send provider info even if initialization fails\n      this._sendProviderInfo(webviewView);\n    });\n\n    webviewView.webview.onDidReceiveMessage(async (data) => {\n      // Support both 'type' and 'command' from frontend messages\n      const messageType = data.type || data.command;\n      Logger.instance.info(`Chat view received message: ${messageType}`, data);\n\n      switch (messageType) {\n        case 'providerChanged': {\n          const providerId = data.provider;\n          if (providerId) {\n            Logger.instance.info(`Provider changed to: ${providerId}`);\n            // Store the selected provider\n            this._context.globalState.update('selectedProvider', providerId);\n\n            // Get models for the selected provider\n            const models = await this._getModelsForProvider(providerId);\n\n            // Send models to webview\n            webviewView.webview.postMessage({\n              type: 'models',\n              models,\n              provider: providerId\n            });\n\n            // Set default model if available\n            if (models.length > 0) {\n              this._context.globalState.update('selectedModel', models[0].id);\n              webviewView.webview.postMessage({\n                type: 'currentModel',\n                model: models[0].id\n              });\n            }\n          }\n          break;\n        }\n        case 'modelChanged': {\n          const modelId = data.model;\n          if (modelId) {\n            // Store the selected model in the context\n            this._context.globalState.update('selectedModel', modelId);\n            Logger.instance.info(`Model changed to: ${modelId}`);\n          }\n          break;\n        }\n        case 'sendMessage': {\n          const messageText = data.text || data.message?.text || data.content;\n          if (!messageText) {\n            Logger.instance.warn('Received sendMessage with no text content');\n            break;\n          }\n\n          // Cancel any previous operation\n          if (this._currentCancelTokenSource) {\n            this._currentCancelTokenSource.cancel();\n            this._currentCancelTokenSource.dispose();\n          }\n          this._currentCancelTokenSource = new vscode.CancellationTokenSource();\n          const cancellationToken = this._currentCancelTokenSource.token;\n\n          // Add user message\n          const userMessage: ChatMessage = {\n            id: `user_${Date.now()}`,\n            role: 'user',\n            content: messageText,\n            timestamp: Date.now()\n          };\n          this._chatSession.addMessage(userMessage);\n          this._chatSession.setProcessing(true);\n          this._chatSession.save();\n\n          // Send processing state to webview\n          webviewView.webview.postMessage({\n            type: 'processingState',\n            isProcessing: true\n          });\n\n          try {\n            // Get selected provider and model from context\n            const selectedProvider = this._context.globalState.get('selectedProvider') || 'ollama';\n            const selectedModel = this._context.globalState.get('selectedModel') || 'llama3';\n\n            Logger.instance.info(`Using provider: ${selectedProvider}, model: ${selectedModel}`);\n\n            // Update the default LLM config to use selected provider/model\n            const config = vscode.workspace.getConfiguration('codessa');\n            await config.update('selectedProvider', selectedProvider, vscode.ConfigurationTarget.Global);\n            await config.update('selectedModel', selectedModel, vscode.ConfigurationTarget.Global);\n\n            // Create streaming context for real-time updates\n            const streamId = `chat_${Date.now()}`;\n            const streamingContext: StreamingContext = {\n              streamId,\n              onStream: (event: AgentStreamEvent) => {\n                this.handleAgentStreamEvent(event, webviewView);\n              },\n              cancellationToken\n            };\n\n            this._activeStreams.set(streamId, streamingContext);\n\n            const agentManager = AgentManager.getInstance();\n            const receiverAgent = agentManager.getReceiverAgent();\n            const maybeSupervisor = agentManager.getSupervisorAgent();\n            const supervisorAgent: SupervisorAgent | undefined =\n              maybeSupervisor instanceof SupervisorAgent ? maybeSupervisor : undefined;\n\n            if (!receiverAgent || !supervisorAgent) {\n              const errorResponse: ChatMessage = {\n                id: `error_${Date.now()}`,\n                role: 'error',\n                content: 'Required agents (receiver or supervisor) not available.',\n                timestamp: Date.now()\n              };\n\n              this._chatSession.addMessage(errorResponse);\n              this._chatSession.setProcessing(false);\n              this._chatSession.save();\n\n              webviewView.webview.postMessage({\n                type: 'addMessage',\n                message: errorResponse\n              });\n\n              webviewView.webview.postMessage({\n                type: 'processingState',\n                isProcessing: false\n              });\n              return;\n            }\n\n            // --- Receiver agent thinking ---\n            const receiverThinkingMessage: ChatMessage = {\n              id: `receiver_thinking_${Date.now()}`,\n              role: 'assistant',\n              content: 'Receiver agent is processing your input...',\n              timestamp: Date.now(),\n              isThinking: true\n            };\n            this._chatSession.addMessage(receiverThinkingMessage);\n            this._chatSession.save();\n            webviewView.webview.postMessage({\n              type: 'addMessage',\n              message: receiverThinkingMessage\n            });\n\n            // First, process through receiver agent\n            const receiverResult = await receiverAgent.run({\n              prompt: messageText,\n              mode: 'chat'\n            }, {\n              workspace: WorkspaceHelper.createWorkspaceContext(),\n              variables: {\n                attachedFiles: this._chatSession.getAttachedFiles()\n              },\n              streamingContext,\n              cancellationToken\n            });\n\n            // Update receiver thinking message with result\n            receiverThinkingMessage.content = '✅ **Receiver Agent** completed analysis:\\\\n\\\\n*Enhanced request ready for supervisor*';\n            receiverThinkingMessage.isThinking = false;\n            this._chatSession.updateMessage(receiverThinkingMessage);\n            this._chatSession.save();\n            webviewView.webview.postMessage({\n              type: 'updateMessage',\n              message: receiverThinkingMessage\n            });\n\n            // Brief pause to show receiver completion\n            await new Promise(resolve => setTimeout(resolve, 800));\n\n            if (cancellationToken.isCancellationRequested) {\n              const cancelledMsg: ChatMessage = {\n                id: `cancelled_${Date.now()}`,\n                role: 'assistant',\n                content: 'Operation cancelled.',\n                timestamp: Date.now()\n              };\n              this._chatSession.addMessage(cancelledMsg);\n              this._chatSession.setProcessing(false);\n              this._chatSession.save();\n              webviewView.webview.postMessage({\n                type: 'addMessage',\n                message: cancelledMsg\n              });\n              webviewView.webview.postMessage({\n                type: 'processingState',\n                isProcessing: false\n              });\n              return;\n            }\n\n            if (!receiverResult.success) {\n              throw new Error(`Receiver agent error: ${receiverResult.error}`);\n            }\n\n            // --- Supervisor agent thinking ---\n            const supervisorThinkingMessage: ChatMessage = {\n              id: `supervisor_thinking_${Date.now()}`,\n              role: 'assistant',\n              content: '🎯 **Supervisor Agent** analyzing and orchestrating your request...',\n              timestamp: Date.now(),\n              isThinking: true\n            };\n            this._chatSession.addMessage(supervisorThinkingMessage);\n            this._chatSession.save();\n            webviewView.webview.postMessage({\n              type: 'addMessage',\n              message: supervisorThinkingMessage\n            });\n\n            // Then, process through supervisor agent with streaming\n            const supervisorResult = await supervisorAgent.run({\n              prompt: receiverResult.output || '',\n              mode: 'chat'\n            }, {\n              workspace: WorkspaceHelper.createWorkspaceContext(),\n              variables: {\n                attachedFiles: this._chatSession.getAttachedFiles(),\n                receiverOutput: receiverResult.output\n              },\n              streamingContext,\n              cancellationToken\n            });\n\n            if (cancellationToken.isCancellationRequested) {\n              this._chatSession.removeMessage(supervisorThinkingMessage.id);\n              this._chatSession.save();\n              webviewView.webview.postMessage({\n                type: 'removeMessage',\n                messageId: supervisorThinkingMessage.id\n              });\n              const cancelledMsg: ChatMessage = {\n                id: `cancelled_${Date.now()}`,\n                role: 'assistant',\n                content: 'Operation cancelled.',\n                timestamp: Date.now()\n              };\n              this._chatSession.addMessage(cancelledMsg);\n              this._chatSession.setProcessing(false);\n              this._chatSession.save();\n              webviewView.webview.postMessage({\n                type: 'addMessage',\n                message: cancelledMsg\n              });\n              webviewView.webview.postMessage({\n                type: 'processingState',\n                isProcessing: false\n              });\n              return;\n            }\n\n            if (!supervisorResult.success) {\n              // Remove supervisor thinking message\n              this._chatSession.removeMessage(supervisorThinkingMessage.id);\n              this._chatSession.save();\n              webviewView.webview.postMessage({\n                type: 'removeMessage',\n                messageId: supervisorThinkingMessage.id\n              });\n              throw new Error(`Supervisor agent error: ${supervisorResult.error}`);\n            }\n\n            // Update supervisor thinking message to show completion\n            supervisorThinkingMessage.content = '✅ **Supervisor Agent** completed orchestration and coordination';\n            supervisorThinkingMessage.isThinking = false;\n            this._chatSession.updateMessage(supervisorThinkingMessage);\n            this._chatSession.save();\n            webviewView.webview.postMessage({\n              type: 'updateMessage',\n              message: supervisorThinkingMessage\n            });\n\n            // Brief pause to show supervisor completion\n            await new Promise(resolve => setTimeout(resolve, 800));\n\n            // --- Delegated agents processing (if any) ---\n            const additionalAgents = agentManager.getAdditionalAgents();\n            let finalOutput = supervisorResult.output;\n\n            for (const agent of additionalAgents) {\n              const delegatedThinkingMessage: ChatMessage = {\n                id: `delegated_thinking_${agent.id}_${Date.now()}`,\n                role: 'assistant',\n                content: `🔄 **${agent.name || agent.id}** processing delegated task...`,\n                timestamp: Date.now(),\n                isThinking: true\n              };\n              this._chatSession.addMessage(delegatedThinkingMessage);\n              this._chatSession.save();\n              webviewView.webview.postMessage({\n                type: 'addMessage',\n                message: delegatedThinkingMessage\n              });\n\n              const result = await agent.run({\n                prompt: finalOutput || '',\n                mode: 'chat'\n              }, {\n                workspace: WorkspaceHelper.createWorkspaceContext(),\n                variables: {\n                  attachedFiles: this._chatSession.getAttachedFiles(),\n                  receiverOutput: receiverResult.output,\n                  supervisorOutput: supervisorResult.output\n                },\n                streamingContext,\n                cancellationToken\n              });\n\n              // Update delegated thinking message with result\n              delegatedThinkingMessage.content = `✅ **${agent.name || agent.id}** completed task`;\n              delegatedThinkingMessage.isThinking = false;\n              this._chatSession.updateMessage(delegatedThinkingMessage);\n              this._chatSession.save();\n              webviewView.webview.postMessage({\n                type: 'updateMessage',\n                message: delegatedThinkingMessage\n              });\n\n              if (cancellationToken.isCancellationRequested) {\n                const cancelledMsg: ChatMessage = {\n                  id: `cancelled_${Date.now()}`,\n                  role: 'assistant',\n                  content: 'Operation cancelled.',\n                  timestamp: Date.now()\n                };\n                this._chatSession.addMessage(cancelledMsg);\n                this._chatSession.setProcessing(false);\n                this._chatSession.save();\n                webviewView.webview.postMessage({\n                  type: 'addMessage',\n                  message: cancelledMsg\n                });\n                webviewView.webview.postMessage({\n                  type: 'processingState',\n                  isProcessing: false\n                });\n                return;\n              }\n\n              if (result.success && result.output) {\n                finalOutput = result.output;\n              }\n\n              // Brief pause between agents\n              await new Promise(resolve => setTimeout(resolve, 500));\n            }\n\n            // Add final assistant message with complete response\n            const assistantMessage: ChatMessage = {\n              id: `assistant_${Date.now()}`,\n              role: 'assistant',\n              content: finalOutput || 'I apologize, but I was unable to generate a response to your request.',\n              timestamp: Date.now()\n            };\n\n            this._chatSession.addMessage(assistantMessage);\n            this._chatSession.setProcessing(false);\n            this._chatSession.save();\n\n            webviewView.webview.postMessage({\n              type: 'addMessage',\n              message: assistantMessage\n            });\n\n            webviewView.webview.postMessage({\n              type: 'processingState',\n              isProcessing: false\n            });\n\n            // Clean up streaming context\n            this._activeStreams.delete(streamId);\n\n            Logger.instance.info('Message processing completed successfully');\n\n          } catch (error) {\n            Logger.instance.error('Error processing message:', error);\n            const errorMessage: ChatMessage = {\n              id: `error_${Date.now()}`,\n              role: 'error',\n              content: `Error: ${error instanceof Error ? error.message : String(error)}`,\n              timestamp: Date.now()\n            };\n\n            this._chatSession.addMessage(errorMessage);\n            this._chatSession.setProcessing(false);\n            this._chatSession.save();\n\n            webviewView.webview.postMessage({\n              type: 'addMessage',\n              message: errorMessage\n            });\n\n            webviewView.webview.postMessage({\n              type: 'processingState',\n              isProcessing: false\n            });\n          }\n          break;\n        }\n        case 'openSettings': {\n          Logger.instance.info('Opening settings panel');\n          try {\n            // Get required managers\n            const memoryManager = MemoryManager.getInstance();\n            const providerManager = ProviderManager.getInstance();\n\n            // Open the AllSettingsPanel\n            await AllSettingsPanel.createOrShow(this._context, this._extensionUri, memoryManager, providerManager);\n          } catch (error) {\n            Logger.instance.error('Failed to open settings panel:', error);\n            vscode.window.showErrorMessage('Failed to open settings panel');\n          }\n          break;\n        }\n        case 'toggleTTS': {\n          const state = data.state;\n          Logger.instance.info(`TTS toggled: ${state}`);\n          try {\n            // Toggle TTS state\n            this._audioService.setTTSEnabled(state);\n          } catch (error) {\n            Logger.instance.error('Failed to toggle TTS:', error);\n          }\n          break;\n        }\n        case 'clearChat': {\n          Logger.instance.info('Clearing chat history');\n          try {\n            this._chatSession.clearMessages();\n            this._chatSession.save();\n            webviewView.webview.postMessage({\n              type: 'clearMessages'\n            });\n          } catch (error) {\n            Logger.instance.error('Failed to clear chat:', error);\n          }\n          break;\n        }\n        case 'exportChat': {\n          Logger.instance.info('Exporting chat');\n          try {\n            const messages = this._chatSession.getMessages();\n            const exportData = JSON.stringify(messages, null, 2);\n\n            // Show save dialog\n            const uri = await vscode.window.showSaveDialog({\n              defaultUri: vscode.Uri.file('codessa-chat-export.json'),\n              filters: {\n                'JSON Files': ['json'],\n                'All Files': ['*']\n              }\n            });\n\n            if (uri) {\n              await vscode.workspace.fs.writeFile(uri, Buffer.from(exportData, 'utf8'));\n              vscode.window.showInformationMessage('Chat exported successfully');\n            }\n          } catch (error) {\n            Logger.instance.error('Failed to export chat:', error);\n            vscode.window.showErrorMessage('Failed to export chat');\n          }\n          break;\n        }\n        // Other message handlers remain the same...\n      }\n    });\n\n    webviewView.onDidDispose(() => this.dispose(), null, this._disposables);\n  }\n\n  private _getWebviewContent(webview: vscode.Webview): string {\n    const nonce = getNonce();\n    const csp = `default-src 'none'; img-src ${webview.cspSource} https: data:; script-src 'nonce-${nonce}'; style-src 'unsafe-inline' ${webview.cspSource};`;\n\n    // Get resource URIs\n    const chatCssUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chat.css'));\n    const chatJsUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chat.js'));\n\n    return `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\" />\n  <meta http-equiv=\"Content-Security-Policy\" content=\"${csp}\" />\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n  <title>Codessa Chat</title>\n  <link rel=\"stylesheet\" href=\"${chatCssUri}\">\n</head>\n<body>\n  <div class=\"chat-container\">\n    <!-- Top Toolbar -->\n    <div class=\"top-toolbar\">\n      <div class=\"toolbar-left\">\n        <select id=\"mode-selector\" class=\"dropdown\">\n          <option value=\"chat\">Chat</option>\n          <option value=\"ask\">Ask</option>\n          <option value=\"debug\">Debug</option>\n          <option value=\"edit\">Edit</option>\n          <option value=\"agent\">Agent</option>\n          <option value=\"multiAgent\">Multi-Agent</option>\n          <option value=\"research\">Research</option>\n          <option value=\"documentation\">Documentation</option>\n        </select>\n        <select id=\"provider-selector\" class=\"dropdown\">\n          <option value=\"ollama\">Ollama</option>\n        </select>\n        <select id=\"model-selector\" class=\"dropdown\">\n          <option value=\"llama3\">Llama 3</option>\n        </select>\n      </div>\n      <div class=\"toolbar-right\">\n        <button id=\"btn-settings\" class=\"btn-icon\" title=\"Settings\">⚙️</button>\n        <button id=\"btn-export\" class=\"btn-icon\" title=\"Export Chat\">📤</button>\n        <button id=\"btn-clear\" class=\"btn-icon\" title=\"Clear Chat\">🗑️</button>\n      </div>\n    </div>\n\n    <!-- Messages Container -->\n    <div id=\"chat-messages\" class=\"messages-container\">\n      <div id=\"empty-chat-message\" class=\"empty-message\">\n        Start a conversation with Codessa...\n      </div>\n    </div>\n\n    <!-- Typing Indicator -->\n    <div id=\"typing-indicator\" class=\"typing-indicator\" style=\"display: none;\">\n      <div class=\"typing-dots\">\n        <span></span><span></span><span></span>\n      </div>\n      <span class=\"typing-text\">Codessa is thinking...</span>\n    </div>\n\n    <!-- Input Area -->\n    <div class=\"input-container\">\n      <div class=\"input-toolbar\">\n        <button id=\"btn-add-context\" class=\"btn-icon\" title=\"Add Context\">📎</button>\n        <button id=\"btn-attach-file\" class=\"btn-icon\" title=\"Attach File\">📄</button>\n        <button id=\"btn-attach-folder\" class=\"btn-icon\" title=\"Attach Folder\">📁</button>\n        <button id=\"btn-upload-image\" class=\"btn-icon\" title=\"Upload Image\">🖼️</button>\n      </div>\n\n      <div class=\"input-wrapper\">\n        <textarea id=\"message-input\" placeholder=\"Type your message...\" rows=\"1\"></textarea>\n        <div class=\"input-actions\">\n          <button id=\"btn-record-audio\" class=\"btn-icon\" title=\"Record Audio\">🎤</button>\n          <button id=\"btn-toggle-tts\" class=\"btn-icon\" title=\"Toggle TTS\">🔊</button>\n          <button id=\"btn-send\" class=\"btn-primary\" title=\"Send\">Send</button>\n          <button id=\"btn-cancel\" class=\"btn-secondary\" title=\"Cancel\" style=\"display: none;\">Cancel</button>\n        </div>\n      </div>\n\n      <div class=\"input-secondary-actions\">\n        <button id=\"btn-input-copy\" class=\"btn-icon\" title=\"Copy\">📋</button>\n        <button id=\"btn-input-cut\" class=\"btn-icon\" title=\"Cut\">✂️</button>\n        <button id=\"btn-input-paste\" class=\"btn-icon\" title=\"Paste\">📋</button>\n        <button id=\"btn-input-clear\" class=\"btn-icon\" title=\"Clear Input\">🗑️</button>\n      </div>\n    </div>\n  </div>\n\n  <script nonce=\"${nonce}\">\n    // Initialize state for chat.js\n    const initialState = {\n      isProcessing: false,\n      messages: [],\n      isTTSActive: false,\n      currentMode: 'chat',\n      currentProvider: 'ollama',\n      currentModel: 'llama3',\n      availableProviders: [],\n      availableModels: []\n    };\n  </script>\n  <script nonce=\"${nonce}\" src=\"${chatJsUri}\"></script>\n</body>\n</html>`;\n  }\n\n  private async _getProviderList(): Promise<Array<{\n    id: string;\n    name: string;\n    description: string;\n    configured: boolean;\n    isLocal: boolean;\n  }>> {\n    try {\n      const providers = llmService.getAllProviders();\n      return providers.map(p => ({\n        id: p.providerId,\n        name: p.displayName,\n        description: p.description,\n        configured: p.isConfigured(),\n        isLocal: p.providerId === 'ollama' || p.providerId === 'lmstudio'\n      }));\n    } catch (error) {\n      Logger.instance.warn('Failed to get provider list:', error);\n      return [];\n    }\n  }\n\n  private async _getModelsForProvider(providerId: string): Promise<LLMModelInfo[]> {\n    try {\n      const provider = llmService.getProvider(providerId);\n      if (provider && provider.isConfigured()) {\n        const models = await provider.listModels();\n        return models;\n      }\n      return this._getDefaultModelsForProvider(providerId);\n    } catch (error) {\n      Logger.instance.warn(`Failed to list models for provider ${providerId}:`, error);\n      return this._getDefaultModelsForProvider(providerId);\n    }\n  }\n\n  private _getDefaultModelsForProvider(providerId: string): LLMModelInfo[] {\n    const provider = llmService.getProvider(providerId);\n    if (provider?.defaultModel) {\n      return [{ id: provider.defaultModel }];\n    }\n    // Minimal sensible defaults per common providers\n    if (providerId === 'ollama') {\n      return [{ id: 'llama3' }];\n    }\n    if (providerId === 'openai') {\n      return [{ id: 'gpt-4o' }];\n    }\n    if (providerId === 'lmstudio') {\n      return [{ id: 'default' }];\n    }\n    return [];\n  }\n\n  private async _sendProviderInfo(webviewView: vscode.WebviewView): Promise<void> {\n    try {\n      const providers = await this._getProviderList();\n      webviewView.webview.postMessage({\n        type: 'providers',\n        providers\n      });\n    } catch (error) {\n      Logger.instance.warn('Failed to send provider info:', error);\n    }\n  }\n\n  private async _sendModelsForProvider(webviewView: vscode.WebviewView, providerId?: string): Promise<void> {\n    try {\n      let targetProviderId = providerId || (this._context.globalState.get('selectedProvider') as string);\n      if (!targetProviderId) {\n        const def = await llmService.getDefaultProvider();\n        targetProviderId = def?.providerId || 'ollama';\n      }\n\n      const models = await this._getModelsForProvider(targetProviderId);\n      webviewView.webview.postMessage({\n        type: 'models',\n        models,\n        provider: targetProviderId\n      });\n\n      // Determine current model\n      const currentModel = (this._context.globalState.get('selectedModel') as string) || models[0]?.id;\n      if (currentModel) {\n        webviewView.webview.postMessage({\n          type: 'currentModel',\n          model: currentModel\n        });\n      }\n    } catch (error) {\n      Logger.instance.warn('Failed to send models for provider:', error);\n    }\n  }\n\n  private async _initializeProvidersAndModels(webviewView: vscode.WebviewView): Promise<void> {\n    try {\n      await this._sendProviderInfo(webviewView);\n\n      // Ensure a selected provider exists\n      let selectedProvider = this._context.globalState.get('selectedProvider') as string | undefined;\n      if (!selectedProvider) {\n        const def = await llmService.getDefaultProvider();\n        selectedProvider = def?.providerId || 'ollama';\n        this._context.globalState.update('selectedProvider', selectedProvider);\n      }\n\n      await this._sendModelsForProvider(webviewView, selectedProvider);\n    } catch (error) {\n      Logger.instance.warn('Failed to initialize providers/models:', error);\n    }\n  }\n\n  public dispose(): void {\n    this._disposables.forEach(d => d.dispose());\n  }\n\n  private _getMimeType(filePath: string): string {\n    const ext = filePath.toLowerCase().split('.').pop();\n    const map: Record<string, string> = {\n      'txt': 'text/plain',\n      'md': 'text/markdown',\n      'js': 'text/javascript',\n      'ts': 'text/typescript',\n      'json': 'application/json',\n      'png': 'image/png',\n      'jpg': 'image/jpeg',\n      'jpeg': 'image/jpeg',\n      'gif': 'image/gif',\n      'svg': 'image/svg+xml',\n      'wav': 'audio/wav',\n      'mp3': 'audio/mpeg'\n    };\n    return map[ext || ''] || 'application/octet-stream';\n  }\n\n  private handleAgentStreamEvent(event: AgentStreamEvent, webviewView: vscode.WebviewView): void {\n    try {\n      // Track agent status map for quick status queries\n      if (event.type === 'start') {\n        this._agentStatusMap.set(event.agentId, 'running');\n      } else if (event.type === 'complete') {\n        this._agentStatusMap.set(event.agentId, 'complete');\n      } else if (event.type === 'error') {\n        this._agentStatusMap.set(event.agentId, 'error');\n      }\n\n      webviewView.webview.postMessage({\n        type: 'agentStream',\n        event\n      });\n    } catch (error) {\n      Logger.instance.warn('Failed to forward agent stream event:', error);\n    }\n  }\n\n  public getStreamingStatus(): { activeStreams: number; activeAgents: string[] } {\n    return {\n      activeStreams: this._activeStreams.size,\n      activeAgents: Array.from(this._agentStatusMap.keys())\n    };\n  }\n\n  public reveal(): void {\n    if (this._view) {\n      this._view.show?.(true);\n    } else {\n      vscode.commands.executeCommand('workbench.view.extension.codessa-sidebar');\n    }\n  }\n}"]}