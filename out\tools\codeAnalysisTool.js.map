{"version": 3, "file": "codeAnalysisTool.js", "sourceRoot": "", "sources": ["../../src/tools/codeAnalysisTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,6BAAwB;AACxB,2CAA6B;AAC7B,kDAA+C;AAI/C;;GAEG;AACH,MAAa,kBAAkB;IACpB,EAAE,GAAG,gBAAgB,CAAC;IACtB,IAAI,GAAG,0BAA0B,CAAC;IAClC,WAAW,GAAG,qHAAqH,CAAC;IACpI,IAAI,GAAG,eAAe,CAAC;IACvB,OAAO,GAAwB,EAAE,CAAC;IAClC,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;QAC7D,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC;KAC7I,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8BAA8B,EAAE;YACzE,OAAO,EAAE;gBACP,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,KAAK,CAAC;iBAC5D;gBACD,WAAW,EAAE,uCAAuC;aACrD;SACF;QACD,QAAQ,EAAE,CAAC,UAAU,CAAC;KACvB,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,OAAO,GAAI,KAAK,CAAC,OAAoB,IAAI,CAAC,KAAK,CAAC,CAAC;YAEvD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAE3D,MAAM,OAAO,GAAwB,EAAE,CAAC;YAExC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9D,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAC/E,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC7D,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACnE,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YACpF,CAAC;YAED,OAAO,CAAC,KAAK,GAAG;gBACd,KAAK,EAAE,QAAQ,CAAC,SAAS;gBACzB,UAAU,EAAE,IAAI,CAAC,MAAM;gBACvB,QAAQ,EAAE,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI;gBACpD,QAAQ,EAAE,QAAQ,CAAC,UAAU;aAC9B,CAAC;YAEF,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAEhE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO;iBAC5F;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,oCAAoC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACnE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,6BAA6B,CAAC,IAAY,EAAE,aAAqB;QACvE,kEAAkE;QAClE,0EAA0E;QAC1E,MAAM,gBAAgB,GAA8B;YAClD,KAAK,EAAE,+CAA+C;YACtD,KAAK,EAAE,+CAA+C;YACtD,KAAK,EAAE,2CAA2C;YAClD,OAAO,EAAE,+CAA+C;YACxD,KAAK,EAAE,+CAA+C;YACtD,MAAM,EAAE,+CAA+C;YACvD,IAAI,EAAE,yCAAyC;YAC/C,KAAK,EAAE,8CAA8C;YACrD,MAAM,EAAE,+CAA+C;YACvD,KAAK,EAAE,gCAAgC;YACvC,KAAK,EAAE,qCAAqC;YAC5C,QAAQ,EAAE,+CAA+C;YACzD,KAAK,EAAE,6CAA6C;YACpD,OAAO,EAAE,+CAA+C;SACzD,CAAC;QAEF,4DAA4D;QAC5D,MAAM,OAAO,GAAG,gBAAgB,CAAC,aAAa,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC3E,2CAA2C;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/C,0BAA0B;QAC1B,0EAA0E;QAC1E,MAAM,gBAAgB,GAA8B;YAClD,KAAK,EAAE,oBAAoB;YAC3B,KAAK,EAAE,oBAAoB;YAC3B,KAAK,EAAE,mBAAmB;YAC1B,OAAO,EAAE,oEAAoE;YAC7E,KAAK,EAAE,6EAA6E;YACpF,MAAM,EAAE,+BAA+B;YACvC,IAAI,EAAE,+BAA+B;YACrC,KAAK,EAAE,cAAc;YACrB,MAAM,EAAE,sEAAsE;YAC9E,KAAK,EAAE,oBAAoB;YAC3B,KAAK,EAAE,kBAAkB;YACzB,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,mBAAmB;YAC1B,OAAO,EAAE,+BAA+B;SACzC,CAAC;QAEF,MAAM,WAAW,GAAG,gBAAgB,CAAC,aAAa,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC/E,2CAA2C;QAC3C,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;QAEzD,oCAAoC;QACpC,MAAM,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YACpD,OAAO;gBACL,QAAQ,EAAE,YAAY,KAAK,GAAG,CAAC,EAAE;gBACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,0CAA0C;aAC1F,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,KAAK,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,yCAAyC;YACpE,WAAW,EAAE,kBAAkB;YAC/B,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAC1C,OAAO,EAAE;gBACP,cAAc,EAAE,OAAO,CAAC,MAAM;gBAC9B,SAAS,EAAE,SAAS,CAAC,MAAM;aAC5B;SACF,CAAC;IACJ,CAAC;IAEO,4BAA4B,CAAC,IAAY,EAAE,aAAqB;QACtE,8CAA8C;QAC9C,qEAAqE;QAErE,2BAA2B;QAC3B,0EAA0E;QAC1E,MAAM,eAAe,GAA8B;YACjD,KAAK,EAAE,kCAAkC;YACzC,KAAK,EAAE,kCAAkC;YACzC,KAAK,EAAE,sCAAsC,EAAE,wBAAwB;YACvE,OAAO,EAAE,kCAAkC;YAC3C,KAAK,EAAE,kCAAkC;YACzC,MAAM,EAAE,kCAAkC;YAC1C,IAAI,EAAE,kCAAkC;YACxC,KAAK,EAAE,sCAAsC;YAC7C,MAAM,EAAE,kCAAkC;YAC1C,KAAK,EAAE,4BAA4B;YACnC,KAAK,EAAE,iCAAiC;YACxC,QAAQ,EAAE,kCAAkC;YAC5C,KAAK,EAAE,gCAAgC;YACvC,OAAO,EAAE,kCAAkC;SAC5C,CAAC;QAEF,MAAM,OAAO,GAAG,eAAe,CAAC,aAAa,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;QACzE,2CAA2C;QAC3C,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,kBAAkB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;QAE7D,0BAA0B;QAC1B,0EAA0E;QAC1E,MAAM,eAAe,GAA8B;YACjD,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,iBAAiB;YACxB,OAAO,EAAE,YAAY;YACrB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,YAAY;YAClB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,YAAY;YACtB,KAAK,EAAE,YAAY;YACnB,OAAO,EAAE,YAAY;SACtB,CAAC;QAEF,MAAM,cAAc,GAAG,eAAe,CAAC,aAAa,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;QAChF,2CAA2C;QAC3C,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACtD,MAAM,cAAc,GAAG,kBAAkB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;QAEpE,uCAAuC;QACvC,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;YACxB,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC;gBACf,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAC9C,CAAC;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;gBACxB,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,cAAc,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC;QAEnF,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YACjC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;YACtC,OAAO,EAAE;gBACP,YAAY,EAAE,QAAQ;gBACtB,gBAAgB,EAAE,cAAc,CAAC,MAAM;gBACvC,oBAAoB,EAAE,OAAO,CAAC,MAAM;aACrC;SACF,CAAC;IACJ,CAAC;IAEO,6BAA6B,CAAC,IAAY,EAAE,aAAqB;QACvE,+CAA+C;QAC/C,mGAAmG;QAEnG,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACzE,MAAM,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC,KAAK,CAAC;QAE3F,kCAAkC;QAClC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,GAAG,oBAAoB,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACvG,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;QAEhE,IAAI,MAAM,CAAC;QACX,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YACvB,MAAM,GAAG,WAAW,CAAC;QACvB,CAAC;aAAM,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YAC9B,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;aAAM,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YAC9B,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;QAED,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;YAC/B,MAAM;YACN,OAAO,EAAE;gBACP,WAAW,EAAE,KAAK;gBAClB,cAAc;gBACd,oBAAoB;aACrB;SACF,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,aAAqB;QACjE,yCAAyC;QACzC,qCAAqC;QACrC,+FAA+F;QAE/F,+BAA+B;QAC/B,0EAA0E;QAC1E,MAAM,gBAAgB,GAA8B;YAClD,KAAK,EAAE,oCAAoC;YAC3C,KAAK,EAAE,oCAAoC;YAC3C,KAAK,EAAE,oCAAoC;YAC3C,OAAO,EAAE,oCAAoC;YAC7C,KAAK,EAAE,oCAAoC;YAC3C,MAAM,EAAE,oCAAoC;YAC5C,IAAI,EAAE,oCAAoC;YAC1C,KAAK,EAAE,oCAAoC;YAC3C,MAAM,EAAE,oCAAoC;YAC5C,KAAK,EAAE,oCAAoC;YAC3C,KAAK,EAAE,oCAAoC;YAC3C,QAAQ,EAAE,oCAAoC;YAC9C,KAAK,EAAE,oCAAoC;YAC3C,OAAO,EAAE,oCAAoC;SAC9C,CAAC;QAEF,MAAM,eAAe,GAAG,gBAAgB,CAAC,aAAa,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACnF,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAEpD,yDAAyD;QACzD,0EAA0E;QAC1E,MAAM,eAAe,GAA8B;YACjD,KAAK,EAAE,yDAAyD;YAChE,KAAK,EAAE,yDAAyD;YAChE,KAAK,EAAE,yDAAyD;YAChE,OAAO,EAAE,iDAAiD;YAC1D,KAAK,EAAE,iDAAiD;YACxD,MAAM,EAAE,iDAAiD;YACzD,IAAI,EAAE,iDAAiD;YACvD,KAAK,EAAE,iDAAiD;YACxD,MAAM,EAAE,iDAAiD;YACzD,KAAK,EAAE,iDAAiD;YACxD,KAAK,EAAE,iDAAiD;YACxD,QAAQ,EAAE,iDAAiD;YAC3D,KAAK,EAAE,iDAAiD;YACxD,OAAO,EAAE,iDAAiD;SAC3D,CAAC;QAEF,MAAM,cAAc,GAAG,eAAe,CAAC,aAAa,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;QAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAElD,0CAA0C;QAC1C,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;QAChD,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;QAE9C,4BAA4B;QAC5B,MAAM,UAAU,GAAG,eAAe,GAAG,cAAc,CAAC;QACpD,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAElD,IAAI,UAAU,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE/B,MAAM,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,6BAA6B;IACvE,CAAC;IAEO,SAAS,CAAC,KAAa;QAC7B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,KAAK,CAAC;QAC9B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QACjC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC/B,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,uBAAuB,CAAC,OAA4B;QAC1D,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,KAAK,SAAS,EAAE,CAAC;YACtF,eAAe,CAAC,IAAI,CAAC,8FAA8F,CAAC,CAAC;QACvH,CAAC;QACD,IAAI,OAAO,CAAC,SAAS,EAAE,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,EAAE,MAAM,KAAK,SAAS,EAAE,CAAC;YACpF,eAAe,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;QACjH,CAAC;QACD,IAAI,OAAO,CAAC,eAAe,EAAE,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,eAAe,EAAE,MAAM,KAAK,MAAM,EAAE,CAAC;YAC7F,eAAe,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;QACxG,CAAC;QACD,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAChF,CAAC;QACD,OAAO,eAAe,CAAC;IACzB,CAAC;CACF;AA/WD,gDA+WC;AAED;;GAEG;AACH,MAAa,yBAAyB;IAC3B,EAAE,GAAG,uBAAuB,CAAC;IAC7B,IAAI,GAAG,iCAAiC,CAAC;IACzC,WAAW,GAAG,0FAA0F,CAAC;IACzG,IAAI,GAAG,eAAe,CAAC;IACvB,OAAO,GAAwB,EAAE,CAAC;IAClC,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;QAC7D,kBAAkB,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;KAChM,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8BAA8B,EAAE;YACzE,kBAAkB,EAAE;gBAClB,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,KAAK,CAAC;iBACnF;gBACD,WAAW,EAAE,wDAAwD;aACtE;SACF;QACD,QAAQ,EAAE,CAAC,UAAU,CAAC;KACvB,CAAC;IAEM,SAAS,GAAc;QAC7B,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,eAAe;QACxB,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;KAC9B,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,kBAAkB,GAAI,KAAK,CAAC,kBAA+B,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7E,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACtC,0EAA0E;gBAC1E,IAAI,MAAM,GAAG,KAAK,CAAC;gBACnB,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACpC,MAAM,GAAG,IAAI,CAAC;gBAChB,CAAC;gBAAC,MAAM,CAAC;oBACP,MAAM,GAAG,KAAK,CAAC;gBACjB,CAAC;gBAED,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;wBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,yBAAyB,KAAK,EAAE;oBACvC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,mBAAmB;YACnB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3D,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;YAErC,gDAAgD;YAChD,MAAM,cAAc,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,EAAE,aAAa,EAAE,kBAAkB,CAAC,CAAC;YAEpG,2CAA2C;YAC3C,IAAI,UAAU,GAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;YAC3C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACvE,IAAI,QAAQ,EAAE,CAAC;oBACb,UAAU,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,QAAQ,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;gBACrG,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,GAAG;oBACX,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,wBAAwB,KAAK,EAAE;iBACvC,CAAC;YACJ,CAAC;YAED,kBAAkB;YAClB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YAExE,2BAA2B;YAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;YAEtE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,eAAe,EAAE,eAAe;oBAChC,eAAe;oBACf,OAAO,EAAE;wBACP,oBAAoB,EAAE,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC;wBACrE,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC;wBAC7D,QAAQ,EAAE;4BACR,IAAI,EAAE,QAAQ;4BACd,QAAQ;4BACR,IAAI,EAAE,IAAI,CAAC,MAAM;yBAClB;qBACF;iBACF;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,kBAAkB,EAAE,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACtD,CAAC,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC,CAAC;wBACvE,kBAAkB;iBACrB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2CAA2C,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC1E,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,8BAA8B,CAAC,IAAY,EAAE,aAAqB,EAAE,KAAe;QACzF,MAAM,OAAO,GAA0B;YACrC,eAAe,EAAE,EAAE;YACnB,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,EAAE;YACV,gBAAgB,EAAE,EAAE;YACpB,iBAAiB,EAAE,EAAE;SACtB,CAAC;QAEF,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEvC,yBAAyB;QACzB,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAChD,MAAM,WAAW,GAAG;gBAClB,EAAE,OAAO,EAAE,sDAAsD,EAAE,QAAQ,EAAE,MAAM,EAAE;gBACrF,EAAE,OAAO,EAAE,wDAAwD,EAAE,QAAQ,EAAE,MAAM,EAAE;gBACvF,EAAE,OAAO,EAAE,4BAA4B,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBAC7D,EAAE,OAAO,EAAE,yBAAyB,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBAC1D,EAAE,OAAO,EAAE,+CAA+C,EAAE,QAAQ,EAAE,MAAM,EAAE;gBAC9E,EAAE,OAAO,EAAE,kDAAkD,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,kCAAkC;aACpH,CAAC;YAEF,KAAK,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,WAAW,EAAE,CAAC;gBAChD,6FAA6F;gBAC7F,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC5B,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC;wBAC5B,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;wBAChD,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,QAAQ;wBACR,WAAW,EAAE,kGAAkG;qBAChH,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,eAAe;QACf,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,MAAM,WAAW,GAAG;gBAClB,EAAE,OAAO,EAAE,6BAA6B,EAAE,QAAQ,EAAE,MAAM,EAAE;gBAC5D,EAAE,OAAO,EAAE,iCAAiC,EAAE,QAAQ,EAAE,MAAM,EAAE;gBAChE,EAAE,OAAO,EAAE,4BAA4B,EAAE,QAAQ,EAAE,MAAM,EAAE;gBAC3D,EAAE,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,MAAM,EAAE;gBACrD,EAAE,OAAO,EAAE,iCAAiC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,SAAS;gBAC7E,EAAE,OAAO,EAAE,6BAA6B,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,qBAAqB;aAClF,CAAC;YAEF,KAAK,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,WAAW,EAAE,CAAC;gBAChD,6FAA6F;gBAC7F,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC5B,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;wBAClB,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;wBAChD,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,QAAQ;wBACR,WAAW,EAAE,4HAA4H;qBAC1I,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACvC,MAAM,YAAY,GAAG;gBACnB,EAAE,OAAO,EAAE,sCAAsC,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBACvE,EAAE,OAAO,EAAE,kCAAkC,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBACnE,EAAE,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,SAAS;gBACnE,EAAE,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,mCAAmC;aACvF,CAAC;YAEF,KAAK,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,YAAY,EAAE,CAAC;gBACjD,6FAA6F;gBAC7F,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC5B,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;wBACnB,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;wBAChD,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,QAAQ;wBACR,WAAW,EAAE,iHAAiH;qBAC/H,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACjD,MAAM,YAAY,GAAG;gBACnB,EAAE,OAAO,EAAE,sEAAsE,EAAE,QAAQ,EAAE,MAAM,EAAE;gBACrG,EAAE,OAAO,EAAE,6BAA6B,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBAC9D,EAAE,OAAO,EAAE,wCAAwC,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBACzE,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,sBAAsB;aAClE,CAAC;YAEF,KAAK,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,YAAY,EAAE,CAAC;gBACjD,6FAA6F;gBAC7F,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC5B,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC;wBAC7B,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;wBAChD,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,QAAQ;wBACR,WAAW,EAAE,4HAA4H;qBAC1I,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAClD,MAAM,cAAc,GAAG;gBACrB,EAAE,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,0BAA0B;gBAC9E,EAAE,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,6BAA6B;gBAChF,EAAE,OAAO,EAAE,uCAAuC,EAAE,QAAQ,EAAE,MAAM,EAAE;gBACtE,EAAE,OAAO,EAAE,wCAAwC,EAAE,QAAQ,EAAE,MAAM,EAAE;gBACvE,EAAE,OAAO,EAAE,yCAAyC,EAAE,QAAQ,EAAE,MAAM,EAAE;gBACxE,EAAE,OAAO,EAAE,yCAAyC,EAAE,QAAQ,EAAE,MAAM,EAAE;gBACxE,EAAE,OAAO,EAAE,6BAA6B,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,iCAAiC;aACjG,CAAC;YAEF,KAAK,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,cAAc,EAAE,CAAC;gBACnD,6FAA6F;gBAC7F,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC5B,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;wBAC9B,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;wBAChD,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,QAAQ;wBACR,WAAW,EAAE,mGAAmG;qBACjH,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,IAAY,EAAE,QAAgB,EAAE,KAAe,EAAE,QAAa;QACvG,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC;YAC7B,CAAC,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC,CAAC;YACvE,KAAK,CAAC;QAER,yBAAyB;QACzB,MAAM,MAAM,GAAG;wBACK,QAAQ,iEAAiE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;QAShH,QAAQ;EACd,IAAI;;;;;;;;;;;;;;;;;;;;;CAqBL,CAAC;QAEE,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC;YACrC,MAAM;YACN,YAAY,EAAE,+IAA+I;YAC7J,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;YAC/B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;SAChC,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;QACnD,CAAC;QAED,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACtD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,OAAO;oBACL,SAAS,EAAE,IAAI;oBACf,eAAe,EAAE,YAAY,CAAC,eAAe,IAAI,EAAE;iBACpD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,0CAA0C;iBAClD,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,iCAAiC,KAAK,EAAE;aAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,cAAqC,EAAE,UAAe;QAC3E,MAAM,QAAQ,GAA0B,EAAE,GAAG,cAAc,EAAE,CAAC;QAE9D,gEAAgE;QAChE,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;YACvD,KAAK,MAAM,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACjF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpB,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACtB,CAAC;gBAED,wDAAwD;gBACxD,KAAK,MAAM,IAAI,IAAI,eAAwB,EAAE,CAAC;oBAC5C,sEAAsE;oBACtE,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CACjD,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;wBAC3B,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAC5B,CAAC;oBAEF,IAAI,CAAC,WAAW,EAAE,CAAC;wBACjB,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;4BAClB,GAAG,IAAI;4BACP,MAAM,EAAE,KAAK,CAAC,0BAA0B;yBACzC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,uBAAuB,CAAC,OAA8B;QAC5D,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,gCAAgC;QAChC,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpE,eAAe,CAAC,IAAI,CAAC,mGAAmG,CAAC,CAAC;YAC1H,eAAe,CAAC,IAAI,CAAC,8FAA8F,CAAC,CAAC;YACrH,eAAe,CAAC,IAAI,CAAC,kGAAkG,CAAC,CAAC;QAC3H,CAAC;QAED,sBAAsB;QACtB,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,eAAe,CAAC,IAAI,CAAC,2GAA2G,CAAC,CAAC;YAClI,eAAe,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;YACjG,eAAe,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;QACrG,CAAC;QAED,uBAAuB;QACvB,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,eAAe,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;YACtF,eAAe,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;YACxF,eAAe,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QACxF,CAAC;QAED,iCAAiC;QACjC,IAAI,OAAO,CAAC,gBAAgB,CAAC,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,eAAe,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;YAC7F,eAAe,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;YACjG,eAAe,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QACtF,CAAC;QAED,kCAAkC;QAClC,IAAI,OAAO,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxE,eAAe,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;YAC7H,eAAe,CAAC,IAAI,CAAC,mFAAmF,CAAC,CAAC;YAC1G,eAAe,CAAC,IAAI,CAAC,kGAAkG,CAAC,CAAC;QAC3H,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,yBAAyB,CAAC,OAA8B;QAC9D,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAClF,CAAC;IAEO,oBAAoB,CAAC,OAA8B;QACzD,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAEjD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,EAAE,CAAC;oBAC/B,SAAS,CAAC,IAAI,CAAC,QAAkC,CAAC,EAAE,CAAC;gBACvD,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,aAAa,CAAC,IAAY,EAAE,KAAa;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnD,OAAO,KAAK,CAAC,MAAM,CAAC;IACtB,CAAC;CACF;AA3bD,8DA2bC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { z } from 'zod';\nimport * as path from 'path';\nimport { llmService } from '../llm/llmService';\nimport { CodeAnalysis } from './timeTravelDebuggingTool';\nimport { LLMConfig } from '../config';\n\n/**\n * Analyzes code complexity using various metrics\n */\nexport class CodeComplexityTool implements ITool {\n  readonly id = 'codeComplexity';\n  readonly name = 'Code Complexity Analysis';\n  readonly description = 'Analyzes code complexity using metrics like cyclomatic complexity, cognitive complexity, and maintainability index.';\n  readonly type = 'single-action';\n  readonly actions: Record<string, any> = {};\n  readonly singleActionSchema = z.object({\n    filePath: z.string().describe('Path to the file to analyze.'),\n    metrics: z.array(z.enum(['cyclomatic', 'cognitive', 'maintainability', 'all'])).optional().describe('Metrics to calculate. Default is all.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      filePath: { type: 'string', description: 'Path to the file to analyze.' },\n      metrics: {\n        type: 'array',\n        items: {\n          type: 'string',\n          enum: ['cyclomatic', 'cognitive', 'maintainability', 'all']\n        },\n        description: 'Metrics to calculate. Default is all.'\n      }\n    },\n    required: ['filePath']\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const metrics = (input.metrics as string[]) || ['all'];\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      const uri = vscode.Uri.file(filePath);\n      try {\n        await vscode.workspace.fs.stat(uri);\n      } catch {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      const document = await vscode.workspace.openTextDocument(uri);\n      const text = document.getText();\n      const fileExtension = path.extname(filePath).toLowerCase();\n\n      const results: Record<string, any> = {};\n\n      if (metrics.includes('all') || metrics.includes('cyclomatic')) {\n        results.cyclomatic = this.calculateCyclomaticComplexity(text, fileExtension);\n      }\n\n      if (metrics.includes('all') || metrics.includes('cognitive')) {\n        results.cognitive = this.calculateCognitiveComplexity(text, fileExtension);\n      }\n\n      if (metrics.includes('all') || metrics.includes('maintainability')) {\n        results.maintainability = this.calculateMaintainabilityIndex(text, fileExtension);\n      }\n\n      results.stats = {\n        lines: document.lineCount,\n        characters: text.length,\n        fileSize: (await vscode.workspace.fs.stat(uri)).size,\n        language: document.languageId\n      };\n\n      results.recommendations = this.generateRecommendations(results);\n\n      return {\n        success: true,\n        output: results,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          metrics: metrics.includes('all') ? ['cyclomatic', 'cognitive', 'maintainability'] : metrics\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Code complexity analysis failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private calculateCyclomaticComplexity(code: string, fileExtension: string): any {\n    // Count decision points (if, for, while, case, &&, ||, ?:, catch)\n    // Add index signature to fix \"Element implicitly has an 'any' type\" error\n    const decisionPatterns: { [key: string]: RegExp } = {\n      '.js': /\\b(if|for|while|switch|catch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.ts': /\\b(if|for|while|switch|catch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.py': /\\b(if|for|while|except)\\b|\\band\\b|\\bor\\b/g,\n      '.java': /\\b(if|for|while|switch|catch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.cs': /\\b(if|for|while|switch|catch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.cpp': /\\b(if|for|while|switch|catch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.c': /\\b(if|for|while|switch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.rb': /\\b(if|for|while|case|rescue)\\b|\\&\\&|\\|\\||\\?/g,\n      '.php': /\\b(if|for|while|switch|catch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.go': /\\b(if|for|switch)\\b|\\&\\&|\\|\\|/g,\n      '.rs': /\\b(if|for|while|match)\\b|\\&\\&|\\|\\|/g,\n      '.swift': /\\b(if|for|while|switch|catch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.kt': /\\b(if|for|while|when|catch)\\b|\\&\\&|\\|\\||\\?/g,\n      '.dart': /\\b(if|for|while|switch|catch)\\b|\\&\\&|\\|\\||\\?/g,\n    };\n\n    // Default to JavaScript pattern if extension not recognized\n    const pattern = decisionPatterns[fileExtension] || decisionPatterns['.js'];\n    // Add null check for regex match operation\n    const matchResult = code.match(pattern);\n    const matches = matchResult ? matchResult : [];\n\n    // Count functions/methods\n    // Add index signature to fix \"Element implicitly has an 'any' type\" error\n    const functionPatterns: { [key: string]: RegExp } = {\n      '.js': /\\b(function|=>)\\b/g,\n      '.ts': /\\b(function|=>)\\b/g,\n      '.py': /\\bdef\\s+\\w+\\s*\\(/g,\n      '.java': /\\b(public|private|protected|static)?\\s*\\w+\\s+\\w+\\s*\\([^)]*\\)\\s*\\{/g,\n      '.cs': /\\b(public|private|protected|internal|static)?\\s*\\w+\\s+\\w+\\s*\\([^)]*\\)\\s*\\{/g,\n      '.cpp': /\\b\\w+\\s+\\w+\\s*\\([^)]*\\)\\s*\\{/g,\n      '.c': /\\b\\w+\\s+\\w+\\s*\\([^)]*\\)\\s*\\{/g,\n      '.rb': /\\bdef\\s+\\w+/g,\n      '.php': /\\b(function|public|private|protected|static)?\\s*function\\s+\\w+\\s*\\(/g,\n      '.go': /\\bfunc\\s+\\w+\\s*\\(/g,\n      '.rs': /\\bfn\\s+\\w+\\s*\\(/g,\n      '.swift': /\\bfunc\\s+\\w+\\s*\\(/g,\n      '.kt': /\\bfun\\s+\\w+\\s*\\(/g,\n      '.dart': /\\b\\w+\\s+\\w+\\s*\\([^)]*\\)\\s*\\{/g,\n    };\n\n    const funcPattern = functionPatterns[fileExtension] || functionPatterns['.js'];\n    // Add null check for regex match operation\n    const funcMatchResult = code.match(funcPattern);\n    const functions = funcMatchResult ? funcMatchResult : [];\n\n    // Calculate complexity per function\n    const functionComplexity = functions.map((_, index) => {\n      return {\n        function: `Function ${index + 1}`,\n        complexity: Math.floor(Math.random() * 10) + 1 // Placeholder for per-function complexity\n      };\n    });\n\n    return {\n      total: matches.length + 1, // Base complexity of 1 + decision points\n      perFunction: functionComplexity,\n      rating: this.getRating(matches.length + 1),\n      details: {\n        decisionPoints: matches.length,\n        functions: functions.length\n      }\n    };\n  }\n\n  private calculateCognitiveComplexity(code: string, fileExtension: string): any {\n    // Simplified cognitive complexity calculation\n    // In a real implementation, this would track nesting levels and more\n\n    // Count nesting indicators\n    // Add index signature to fix \"Element implicitly has an 'any' type\" error\n    const nestingPatterns: { [key: string]: RegExp } = {\n      '.js': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n      '.ts': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n      '.py': /\\b(if|for|while|def|class)\\b|\\s{4,}/g, // Indentation in Python\n      '.java': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n      '.cs': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n      '.cpp': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n      '.c': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n      '.rb': /\\b(if|for|while|def|class|do|end)\\b/g,\n      '.php': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n      '.go': /\\{|\\}|\\b(if|for|switch)\\b/g,\n      '.rs': /\\{|\\}|\\b(if|for|while|match)\\b/g,\n      '.swift': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n      '.kt': /\\{|\\}|\\b(if|for|while|when)\\b/g,\n      '.dart': /\\{|\\}|\\b(if|for|while|switch)\\b/g,\n    };\n\n    const pattern = nestingPatterns[fileExtension] || nestingPatterns['.js'];\n    // Add null check for regex match operation\n    const nestingMatchResult = code.match(pattern);\n    const matches = nestingMatchResult ? nestingMatchResult : [];\n\n    // Count logical operators\n    // Add index signature to fix \"Element implicitly has an 'any' type\" error\n    const logicalPatterns: { [key: string]: RegExp } = {\n      '.js': /\\&\\&|\\|\\|/g,\n      '.ts': /\\&\\&|\\|\\|/g,\n      '.py': /\\band\\b|\\bor\\b/g,\n      '.java': /\\&\\&|\\|\\|/g,\n      '.cs': /\\&\\&|\\|\\|/g,\n      '.cpp': /\\&\\&|\\|\\|/g,\n      '.c': /\\&\\&|\\|\\|/g,\n      '.rb': /\\&\\&|\\|\\|/g,\n      '.php': /\\&\\&|\\|\\|/g,\n      '.go': /\\&\\&|\\|\\|/g,\n      '.rs': /\\&\\&|\\|\\|/g,\n      '.swift': /\\&\\&|\\|\\|/g,\n      '.kt': /\\&\\&|\\|\\|/g,\n      '.dart': /\\&\\&|\\|\\|/g,\n    };\n\n    const logicalPattern = logicalPatterns[fileExtension] || logicalPatterns['.js'];\n    // Add null check for regex match operation\n    const logicalMatchResult = code.match(logicalPattern);\n    const logicalMatches = logicalMatchResult ? logicalMatchResult : [];\n\n    // Calculate nesting depth (simplified)\n    let maxDepth = 0;\n    let currentDepth = 0;\n\n    for (const char of code) {\n      if (char === '{') {\n        currentDepth++;\n        maxDepth = Math.max(maxDepth, currentDepth);\n      } else if (char === '}') {\n        currentDepth = Math.max(0, currentDepth - 1);\n      }\n    }\n\n    const cognitiveScore = matches.length * 0.5 + logicalMatches.length + maxDepth * 2;\n\n    return {\n      score: Math.round(cognitiveScore),\n      rating: this.getRating(cognitiveScore),\n      details: {\n        nestingDepth: maxDepth,\n        logicalOperators: logicalMatches.length,\n        structuralComplexity: matches.length\n      }\n    };\n  }\n\n  private calculateMaintainabilityIndex(code: string, fileExtension: string): any {\n    // Simplified maintainability index calculation\n    // MI = 171 - 5.2 * ln(Halstead Volume) - 0.23 * (Cyclomatic Complexity) - 16.2 * ln(Lines of Code)\n\n    const lines = code.split('\\n').length;\n    const halsteadVolume = this.calculateHalsteadVolume(code, fileExtension);\n    const cyclomaticComplexity = this.calculateCyclomaticComplexity(code, fileExtension).total;\n\n    // Calculate maintainability index\n    const mi = 171 - 5.2 * Math.log(halsteadVolume) - 0.23 * cyclomaticComplexity - 16.2 * Math.log(lines);\n    const normalizedMI = Math.max(0, Math.min(100, mi * 100 / 171));\n\n    let rating;\n    if (normalizedMI >= 85) {\n      rating = 'Excellent';\n    } else if (normalizedMI >= 65) {\n      rating = 'Good';\n    } else if (normalizedMI >= 40) {\n      rating = 'Fair';\n    } else {\n      rating = 'Poor';\n    }\n\n    return {\n      score: Math.round(normalizedMI),\n      rating,\n      details: {\n        linesOfCode: lines,\n        halsteadVolume,\n        cyclomaticComplexity\n      }\n    };\n  }\n\n  private calculateHalsteadVolume(code: string, fileExtension: string): number {\n    // Simplified Halstead volume calculation\n    // Volume = (N1 + N2) * log2(n1 + n2)\n    // where N1 = total operators, N2 = total operands, n1 = unique operators, n2 = unique operands\n\n    // Simplified operator counting\n    // Add index signature to fix \"Element implicitly has an 'any' type\" error\n    const operatorPatterns: { [key: string]: RegExp } = {\n      '.js': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.ts': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.py': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.java': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.cs': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.cpp': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.c': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.rb': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.php': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.go': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.rs': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.swift': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.kt': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n      '.dart': /[\\+\\-\\*\\/\\=\\<\\>\\!\\&\\|\\^\\~\\?\\:\\%]+/g,\n    };\n\n    const operatorPattern = operatorPatterns[fileExtension] || operatorPatterns['.js'];\n    const operators = code.match(operatorPattern) || [];\n\n    // Simplified operand counting (identifiers and literals)\n    // Add index signature to fix \"Element implicitly has an 'any' type\" error\n    const operandPatterns: { [key: string]: RegExp } = {\n      '.js': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|`[^`]*`|\\d+/g,\n      '.ts': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|`[^`]*`|\\d+/g,\n      '.py': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|`[^`]*`|\\d+/g,\n      '.java': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.cs': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.cpp': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.c': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.rb': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.php': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.go': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.rs': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.swift': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.kt': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n      '.dart': /\\b[a-zA-Z_][a-zA-Z0-9_]*\\b|\"[^\"]*\"|'[^']*'|\\d+/g,\n    };\n\n    const operandPattern = operandPatterns[fileExtension] || operandPatterns['.js'];\n    const operands = code.match(operandPattern) || [];\n\n    // Calculate unique operators and operands\n    const uniqueOperators = new Set(operators).size;\n    const uniqueOperands = new Set(operands).size;\n\n    // Calculate Halstead volume\n    const vocabulary = uniqueOperators + uniqueOperands;\n    const length = operators.length + operands.length;\n\n    if (vocabulary === 0) return 0;\n\n    const volume = length * Math.log2(vocabulary);\n    return Math.max(1, Math.round(volume)); // Ensure minimum volume of 1\n  }\n\n  private getRating(score: number): string {\n    if (score <= 10) return 'Low';\n    if (score <= 20) return 'Medium';\n    if (score <= 50) return 'High';\n    return 'Extreme';\n  }\n\n  private generateRecommendations(results: Record<string, any>): string[] {\n    const recommendations: string[] = [];\n\n    if (results.cyclomatic?.rating === 'High' || results.cyclomatic?.rating === 'Extreme') {\n      recommendations.push('Cyclomatic complexity is high. Consider refactoring to reduce the number of decision points.');\n    }\n    if (results.cognitive?.rating === 'High' || results.cognitive?.rating === 'Extreme') {\n      recommendations.push('Cognitive complexity is high. Simplify nested structures and reduce logical operators.');\n    }\n    if (results.maintainability?.rating === 'Fair' || results.maintainability?.rating === 'Poor') {\n      recommendations.push('Maintainability index is low. Improve code readability and reduce complexity.');\n    }\n    if (recommendations.length === 0) {\n      recommendations.push('Code complexity metrics are within acceptable limits.');\n    }\n    return recommendations;\n  }\n}\n\n/**\n * Analyzes code for potential security vulnerabilities\n */\nexport class SecurityVulnerabilityTool implements ITool {\n  readonly id = 'securityVulnerability';\n  readonly name = 'Security Vulnerability Analysis';\n  readonly description = 'Analyzes code for potential security vulnerabilities like SQL injection, XSS, CSRF, etc.';\n  readonly type = 'single-action';\n  readonly actions: Record<string, any> = {};\n  readonly singleActionSchema = z.object({\n    filePath: z.string().describe('Path to the file to analyze.'),\n    vulnerabilityTypes: z.array(z.enum(['sql-injection', 'xss', 'csrf', 'path-traversal', 'insecure-crypto', 'all'])).optional().describe('Types of vulnerabilities to check for. Default is all.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      filePath: { type: 'string', description: 'Path to the file to analyze.' },\n      vulnerabilityTypes: {\n        type: 'array',\n        items: {\n          type: 'string',\n          enum: ['sql-injection', 'xss', 'csrf', 'path-traversal', 'insecure-crypto', 'all']\n        },\n        description: 'Types of vulnerabilities to check for. Default is all.'\n      }\n    },\n    required: ['filePath']\n  };\n\n  private llmConfig: LLMConfig = {\n    provider: 'openai',\n    modelId: 'gpt-3.5-turbo',\n    options: { temperature: 0.1 }\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const vulnerabilityTypes = (input.vulnerabilityTypes as string[]) || ['all'];\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate file exists\n      try {\n        const uri = vscode.Uri.file(filePath);\n        // Replace Promise.then().catch() with try/catch for better error handling\n        let exists = false;\n        try {\n          await vscode.workspace.fs.stat(uri);\n          exists = true;\n        } catch {\n          exists = false;\n        }\n\n        if (!exists) {\n          return {\n            success: false,\n            error: `File not found: ${filePath}`,\n            toolId: this.id,\n            actionName\n          };\n        }\n      } catch (error) {\n        return {\n          success: false,\n          error: `Error accessing file: ${error}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Get file content\n      const document = await vscode.workspace.openTextDocument(filePath);\n      const text = document.getText();\n      const fileExtension = path.extname(filePath).toLowerCase();\n      const language = document.languageId;\n\n      // Perform pattern-based vulnerability detection\n      const patternResults = this.detectVulnerabilitiesByPattern(text, fileExtension, vulnerabilityTypes);\n\n      // Use LLM for deeper analysis if available\n      let llmResults: any = { available: false };\n      try {\n        const provider = await llmService.getProviderForConfig(this.llmConfig);\n        if (provider) {\n          llmResults = await this.detectVulnerabilitiesWithLLM(text, language, vulnerabilityTypes, provider);\n        }\n      } catch (error) {\n        llmResults = {\n          available: false,\n          error: `LLM analysis failed: ${error}`\n        };\n      }\n\n      // Combine results\n      const combinedResults = this.combineResults(patternResults, llmResults);\n\n      // Generate recommendations\n      const recommendations = this.generateRecommendations(combinedResults);\n\n      return {\n        success: true,\n        output: {\n          vulnerabilities: combinedResults,\n          recommendations,\n          summary: {\n            totalVulnerabilities: this.countTotalVulnerabilities(combinedResults),\n            severityBreakdown: this.getSeverityBreakdown(combinedResults),\n            fileInfo: {\n              path: filePath,\n              language,\n              size: text.length\n            }\n          }\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          vulnerabilityTypes: vulnerabilityTypes.includes('all') ?\n            ['sql-injection', 'xss', 'csrf', 'path-traversal', 'insecure-crypto'] :\n            vulnerabilityTypes\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Security vulnerability analysis failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private detectVulnerabilitiesByPattern(code: string, fileExtension: string, types: string[]): any {\n    const results: Record<string, any[]> = {\n      'sql-injection': [],\n      'xss': [],\n      'csrf': [],\n      'path-traversal': [],\n      'insecure-crypto': []\n    };\n\n    const checkAll = types.includes('all');\n\n    // SQL Injection patterns\n    if (checkAll || types.includes('sql-injection')) {\n      const sqlPatterns = [\n        { pattern: /\\b(SELECT|INSERT|UPDATE|DELETE|DROP)\\b.*\\+\\s*[\\w'\"]/i, severity: 'high' },\n        { pattern: /\\bexecute(?:Query|Update|Statement)\\s*\\(.*\\+\\s*[\\w'\"]/i, severity: 'high' },\n        { pattern: /\\bquery\\s*\\(.*\\+\\s*[\\w'\"]/i, severity: 'medium' },\n        { pattern: /\\bsql\\s*=.*\\+\\s*[\\w'\"]/i, severity: 'medium' },\n        { pattern: /\\b(SELECT|INSERT|UPDATE|DELETE|DROP)\\b.*\\$\\{/i, severity: 'high' },\n        { pattern: /\\b(SELECT|INSERT|UPDATE|DELETE|DROP)\\b.*\\?\\s*\\)/i, severity: 'low' } // Parameterized queries are safer\n      ];\n\n      for (const { pattern, severity } of sqlPatterns) {\n        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues\n        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));\n        for (const match of matches) {\n          results['sql-injection'].push({\n            line: this.getLineNumber(code, match.index || 0),\n            code: match[0],\n            severity,\n            description: 'Potential SQL injection vulnerability. User input should be properly sanitized or parameterized.'\n          });\n        }\n      }\n    }\n\n    // XSS patterns\n    if (checkAll || types.includes('xss')) {\n      const xssPatterns = [\n        { pattern: /\\binnerHTML\\s*=\\s*.*[\\w'\"]/i, severity: 'high' },\n        { pattern: /\\bdocument\\.write\\s*\\(.*[\\w'\"]/i, severity: 'high' },\n        { pattern: /\\bevalScript\\s*\\(.*[\\w'\"]/i, severity: 'high' },\n        { pattern: /\\beval\\s*\\(.*[\\w'\"]/i, severity: 'high' },\n        { pattern: /\\$\\s*\\(.*\\)\\.html\\s*\\(.*[\\w'\"]/i, severity: 'medium' }, // jQuery\n        { pattern: /\\brender\\s*\\(.*\\{.*\\}\\s*\\)/i, severity: 'low' } // Template rendering\n      ];\n\n      for (const { pattern, severity } of xssPatterns) {\n        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues\n        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));\n        for (const match of matches) {\n          results['xss'].push({\n            line: this.getLineNumber(code, match.index || 0),\n            code: match[0],\n            severity,\n            description: 'Potential Cross-Site Scripting (XSS) vulnerability. User input should be properly sanitized before being rendered in HTML.'\n          });\n        }\n      }\n    }\n\n    // CSRF patterns\n    if (checkAll || types.includes('csrf')) {\n      const csrfPatterns = [\n        { pattern: /\\b(POST|PUT|DELETE)\\b.*\\bfetch\\s*\\(/i, severity: 'medium' },\n        { pattern: /\\baxios\\.(post|put|delete)\\s*\\(/i, severity: 'medium' },\n        { pattern: /\\$\\.(post|ajax)\\s*\\(/i, severity: 'medium' }, // jQuery\n        { pattern: /\\bcsrf\\b|\\bxsrf\\b/i, severity: 'low' } // Might be implementing protection\n      ];\n\n      for (const { pattern, severity } of csrfPatterns) {\n        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues\n        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));\n        for (const match of matches) {\n          results['csrf'].push({\n            line: this.getLineNumber(code, match.index || 0),\n            code: match[0],\n            severity,\n            description: 'Potential Cross-Site Request Forgery (CSRF) vulnerability. Implement CSRF tokens for state-changing operations.'\n          });\n        }\n      }\n    }\n\n    // Path Traversal patterns\n    if (checkAll || types.includes('path-traversal')) {\n      const pathPatterns = [\n        { pattern: /\\b(readFile|writeFile|readFileSync|writeFileSync)\\s*\\(.*\\+\\s*[\\w'\"]/i, severity: 'high' },\n        { pattern: /\\b(fs|path)\\b.*\\+\\s*[\\w'\"]/i, severity: 'medium' },\n        { pattern: /\\b(open|read|write)\\s*\\(.*\\+\\s*[\\w'\"]/i, severity: 'medium' },\n        { pattern: /\\.\\.\\//g, severity: 'medium' } // Directory traversal\n      ];\n\n      for (const { pattern, severity } of pathPatterns) {\n        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues\n        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));\n        for (const match of matches) {\n          results['path-traversal'].push({\n            line: this.getLineNumber(code, match.index || 0),\n            code: match[0],\n            severity,\n            description: 'Potential Path Traversal vulnerability. User input should be validated and sanitized before being used in file operations.'\n          });\n        }\n      }\n    }\n\n    // Insecure Crypto patterns\n    if (checkAll || types.includes('insecure-crypto')) {\n      const cryptoPatterns = [\n        { pattern: /\\bMD5\\b|\\bSHA1\\b/i, severity: 'high' }, // Weak hashing algorithms\n        { pattern: /\\bDES\\b|\\bRC4\\b/i, severity: 'high' }, // Weak encryption algorithms\n        { pattern: /\\bcreateHash\\s*\\(\\s*['\"]md5['\"]\\s*\\)/i, severity: 'high' },\n        { pattern: /\\bcreateHash\\s*\\(\\s*['\"]sha1['\"]\\s*\\)/i, severity: 'high' },\n        { pattern: /\\bcreateCipher\\s*\\(\\s*['\"]des['\"]\\s*\\)/i, severity: 'high' },\n        { pattern: /\\bcreateCipher\\s*\\(\\s*['\"]rc4['\"]\\s*\\)/i, severity: 'high' },\n        { pattern: /\\bpassword\\b.*\\b(==|===)\\b/i, severity: 'medium' } // Plain text password comparison\n      ];\n\n      for (const { pattern, severity } of cryptoPatterns) {\n        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues\n        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));\n        for (const match of matches) {\n          results['insecure-crypto'].push({\n            line: this.getLineNumber(code, match.index || 0),\n            code: match[0],\n            severity,\n            description: 'Potential Insecure Cryptography usage. Use modern, secure cryptographic algorithms and practices.'\n          });\n        }\n      }\n    }\n\n    return results;\n  }\n\n  private async detectVulnerabilitiesWithLLM(code: string, language: string, types: string[], provider: any): Promise<any> {\n    const checkAll = types.includes('all');\n    const typesToCheck = checkAll ?\n      ['sql-injection', 'xss', 'csrf', 'path-traversal', 'insecure-crypto'] :\n      types;\n\n    // Prepare prompt for LLM\n    const prompt = `\nAnalyze the following ${language} code for security vulnerabilities, specifically focusing on: ${typesToCheck.join(', ')}.\nFor each vulnerability found, provide:\n1. The line number\n2. The vulnerable code snippet\n3. The severity (high, medium, low)\n4. A brief description of the vulnerability\n5. A recommendation for fixing it\n\nCode to analyze:\n\\`\\`\\`${language}\n${code}\n\\`\\`\\`\n\nFormat your response as JSON with the following structure:\n{\n  \"vulnerabilities\": {\n    \"sql-injection\": [\n      {\n        \"line\": number,\n        \"code\": \"string\",\n        \"severity\": \"high|medium|low\",\n        \"description\": \"string\",\n        \"recommendation\": \"string\"\n      }\n    ],\n    \"xss\": [...],\n    \"csrf\": [...],\n    \"path-traversal\": [...],\n    \"insecure-crypto\": [...]\n  }\n}\n`;\n\n    const result = await provider.generate({\n      prompt,\n      systemPrompt: 'You are an expert security analyst specializing in code security. Analyze the provided code for security vulnerabilities with high precision.',\n      modelId: this.llmConfig.modelId,\n      options: this.llmConfig.options\n    });\n\n    if (result.error) {\n      return { available: false, error: result.error };\n    }\n\n    try {\n      // Try to parse the LLM response as JSON\n      const jsonMatch = result.content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const parsedResult = JSON.parse(jsonMatch[0]);\n        return {\n          available: true,\n          vulnerabilities: parsedResult.vulnerabilities || {}\n        };\n      } else {\n        return {\n          available: false,\n          error: 'LLM response could not be parsed as JSON'\n        };\n      }\n    } catch (error) {\n      return {\n        available: false,\n        error: `Failed to parse LLM response: ${error}`\n      };\n    }\n  }\n\n  private combineResults(patternResults: Record<string, any[]>, llmResults: any): Record<string, any[]> {\n    const combined: Record<string, any[]> = { ...patternResults };\n\n    // If LLM results are available, merge them with pattern results\n    if (llmResults.available && llmResults.vulnerabilities) {\n      for (const [type, vulnerabilities] of Object.entries(llmResults.vulnerabilities)) {\n        if (!combined[type]) {\n          combined[type] = [];\n        }\n\n        // Add LLM-detected vulnerabilities, avoiding duplicates\n        for (const vuln of vulnerabilities as any[]) {\n          // Check if this vulnerability is already detected by pattern matching\n          const isDuplicate = combined[type].some(existing =>\n            existing.line === vuln.line &&\n            existing.code === vuln.code\n          );\n\n          if (!isDuplicate) {\n            combined[type].push({\n              ...vuln,\n              source: 'llm' // Mark as coming from LLM\n            });\n          }\n        }\n      }\n    }\n\n    return combined;\n  }\n\n  private generateRecommendations(results: Record<string, any[]>): string[] {\n    const recommendations: string[] = [];\n\n    // SQL Injection recommendations\n    if (results['sql-injection'] && results['sql-injection'].length > 0) {\n      recommendations.push('Use parameterized queries or prepared statements instead of string concatenation for SQL queries.');\n      recommendations.push('Implement input validation and sanitization for all user inputs used in database operations.');\n      recommendations.push('Consider using an ORM (Object-Relational Mapping) library that handles SQL injection protection.');\n    }\n\n    // XSS recommendations\n    if (results['xss'] && results['xss'].length > 0) {\n      recommendations.push('Use context-specific output encoding when rendering user input in HTML, JavaScript, CSS, or URL contexts.');\n      recommendations.push('Implement Content Security Policy (CSP) headers to mitigate XSS attacks.');\n      recommendations.push('Consider using safe templating libraries that automatically escape output.');\n    }\n\n    // CSRF recommendations\n    if (results['csrf'] && results['csrf'].length > 0) {\n      recommendations.push('Implement anti-CSRF tokens for all state-changing operations.');\n      recommendations.push('Use the SameSite cookie attribute to limit cross-site requests.');\n      recommendations.push('Verify the Origin or Referer header for sensitive operations.');\n    }\n\n    // Path Traversal recommendations\n    if (results['path-traversal'] && results['path-traversal'].length > 0) {\n      recommendations.push('Validate and sanitize user input before using it in file operations.');\n      recommendations.push('Use path normalization functions to prevent directory traversal attacks.');\n      recommendations.push('Consider using a whitelist of allowed files or directories.');\n    }\n\n    // Insecure Crypto recommendations\n    if (results['insecure-crypto'] && results['insecure-crypto'].length > 0) {\n      recommendations.push('Replace weak cryptographic algorithms (MD5, SHA1, DES, RC4) with modern alternatives (SHA-256, AES).');\n      recommendations.push('Use established cryptographic libraries rather than implementing crypto yourself.');\n      recommendations.push('For password storage, use specialized password hashing functions like bcrypt, Argon2, or PBKDF2.');\n    }\n\n    return recommendations;\n  }\n\n  private countTotalVulnerabilities(results: Record<string, any[]>): number {\n    return Object.values(results).reduce((total, vulns) => total + vulns.length, 0);\n  }\n\n  private getSeverityBreakdown(results: Record<string, any[]>): Record<string, number> {\n    const breakdown = { high: 0, medium: 0, low: 0 };\n\n    for (const vulns of Object.values(results)) {\n      for (const vuln of vulns) {\n        if (vuln.severity in breakdown) {\n          breakdown[vuln.severity as keyof typeof breakdown]++;\n        }\n      }\n    }\n\n    return breakdown;\n  }\n\n  private getLineNumber(code: string, index: number): number {\n    const lines = code.substring(0, index).split('\\n');\n    return lines.length;\n  }\n}\n"]}