"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodessaGraphMemory = void 0;
const logger_1 = require("../../logger");
const corePolyfill_1 = require("../../agents/workflows/corePolyfill");
const llmService_1 = require("../../llm/llmService");
/**
 * Codessa Memory
 * Implements memory workflows using Codessa
 */
class CodessaGraphMemory {
    graph;
    compiledGraph;
    model;
    initialized = false;
    memoryProvider;
    constructor(memoryProvider) {
        this.memoryProvider = memoryProvider;
    }
    /**
       * Initialize Codessa memory
       */
    async initialize() {
        try {
            // Get model from LLM service
            const provider = await llmService_1.llmService.getDefaultProvider();
            if (!provider) {
                throw new Error('No default provider available');
            }
            // Create model adapter
            this.model = {
                invoke: async (messages, options) => {
                    try {
                        const result = await provider.generate({
                            prompt: messages.map((m) => {
                                if (m.type) {
                                    if (m.type === 'human') {
                                        return `User: ${m.content}`;
                                    }
                                    else if (m.type === 'ai') {
                                        return `Assistant: ${m.content}`;
                                    }
                                    else if (m.type === 'system') {
                                        return `System: ${m.content}`;
                                    }
                                }
                                return `User: ${String(m.content)}`;
                            }).join('\n'),
                            modelId: 'default',
                            options: {
                                temperature: options?.temperature ?? 0.7,
                                maxTokens: options?.maxTokens,
                                stopSequences: options?.stop
                            },
                            mode: 'chat'
                        });
                        return new corePolyfill_1.AIMessage(result.content ?? '');
                    }
                    catch (error) {
                        logger_1.logger.error('Error invoking model:', error);
                        throw error;
                    }
                },
                // Add required methods to satisfy BaseChatModel interface
                _llmType: () => 'custom',
                _modelType: () => 'chat'
            };
            // Create memory graph
            this.graph = this.createMemoryGraph();
            this.initialized = true;
            logger_1.logger.info('Codessa memory initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Codessa memory:', error);
            throw error;
        }
    }
    /**
       * Create memory graph
       */
    createMemoryGraph() {
        // Create state graph
        const graph = new corePolyfill_1.StateGraph({
            channels: {
                messages: {
                    value: [],
                    default: () => []
                },
                context: {
                    value: [],
                    default: () => []
                },
                memories: {
                    value: [],
                    default: () => []
                },
                currentTask: {
                    value: '',
                    default: () => ''
                },
                nextAction: {
                    value: '',
                    default: () => ''
                }
            }
        });
        // Add nodes
        // 1. Retrieve relevant memories
        graph.addNode('retrieveMemories', async (state) => {
            try {
                // Get last message
                const lastMessage = state.messages[state.messages.length - 1];
                if (!lastMessage || lastMessage.type !== 'human') {
                    return { ...state };
                }
                // Search for relevant memories
                const query = lastMessage.content;
                const memories = await this.memoryProvider.searchSimilarMemories(query);
                // Extract context from memories
                const context = memories.map(memory => memory.content);
                return {
                    ...state,
                    context,
                    memories
                };
            }
            catch (error) {
                logger_1.logger.error('Error retrieving memories:', error);
                return { ...state };
            }
        });
        // 2. Determine next action
        graph.addNode('determineNextAction', async (state) => {
            try {
                if (!this.model) {
                    return { ...state, nextAction: 'respond' };
                }
                // Create prompt
                const prompt = corePolyfill_1.ChatPromptTemplate.fromMessages([
                    new corePolyfill_1.SystemMessage('You are a memory management assistant. Based on the conversation history and the user\'s latest message, ' +
                        'determine what action to take next. Options are:\n' +
                        '- "respond": Generate a normal response\n' +
                        '- "store": Store important information from the conversation\n' +
                        '- "retrieve": Retrieve more specific information from memory\n' +
                        '- "summarize": Summarize the conversation so far\n' +
                        'Respond with just one of these action names.'),
                    new corePolyfill_1.MessagesPlaceholder('messages'),
                    new corePolyfill_1.HumanMessage('What action should I take next?')
                ]);
                // Invoke model
                const chain = prompt.pipe(this.model);
                const result = await chain.invoke({
                    messages: state.messages
                });
                // Extract action
                const action = String(result.content).toLowerCase().trim();
                // Validate action
                const validActions = ['respond', 'store', 'retrieve', 'summarize'];
                const nextAction = validActions.includes(action) ? action : 'respond';
                return {
                    ...state,
                    nextAction
                };
            }
            catch (error) {
                logger_1.logger.error('Error determining next action:', error);
                return { ...state, nextAction: 'respond' };
            }
        });
        // 3. Store memory
        graph.addNode('storeMemory', async (state) => {
            try {
                // Get last message pair (human and AI)
                const messages = state.messages;
                if (messages.length < 2) {
                    return { ...state };
                }
                const lastHumanIndex = messages.map((m) => m.type).lastIndexOf('human');
                const lastAIIndex = messages.map((m) => m.type).lastIndexOf('ai');
                if (lastHumanIndex === -1 || lastAIIndex === -1) {
                    return { ...state };
                }
                const humanMessage = messages[lastHumanIndex];
                const aiMessage = messages[lastAIIndex];
                // Create memory entry
                const memoryEntry = {
                    content: `User: ${humanMessage.content}\nAssistant: ${aiMessage.content}`,
                    metadata: {
                        source: 'conversation',
                        type: 'conversation',
                        tags: ['conversation']
                    }
                };
                // Add to memory
                await this.memoryProvider.addMemory(memoryEntry);
                return { ...state };
            }
            catch (error) {
                logger_1.logger.error('Error storing memory:', error);
                return { ...state };
            }
        });
        // Define edges
        graph.addEdge('start', 'retrieveMemories');
        graph.addEdge('retrieveMemories', 'determineNextAction');
        graph.addEdge('determineNextAction', 'storeMemory');
        graph.addEdge('determineNextAction', 'end');
        graph.addEdge('storeMemory', 'end');
        return graph;
    }
    /**
       * Compile the memory graph
       */
    async compile() {
        if (!this.initialized) {
            await this.initialize();
        }
        if (!this.graph) {
            throw new Error('Memory graph not initialized');
        }
        this.compiledGraph = this.graph.compile();
    }
    /**
       * Process a new message
       * @param message The message to process
       * @returns The AI response as a string
       */
    async processMessage(message) {
        if (!this.compiledGraph) {
            await this.compile();
        }
        if (!this.compiledGraph) {
            throw new Error('Memory graph not compiled');
        }
        // Convert string message to HumanMessage if needed
        const humanMessage = typeof message === 'string'
            ? { type: 'human', content: message }
            : message;
        // Create initial state
        const initialState = {
            messages: [humanMessage],
            context: [],
            memories: [],
            currentTask: '',
            nextAction: ''
        };
        // Run the graph
        const result = await this.compiledGraph.invoke(initialState);
        // Extract AI response from the result
        const aiMessages = result.messages.filter((m) => m.type === 'ai');
        if (aiMessages.length === 0) {
            return 'I don\'t have a response at this time.';
        }
        const lastAIMessage = aiMessages[aiMessages.length - 1];
        return typeof lastAIMessage.content === 'string'
            ? lastAIMessage.content
            : String(lastAIMessage.content);
    }
}
exports.CodessaGraphMemory = CodessaGraphMemory;
//# sourceMappingURL=codessaGraphMemory.js.map