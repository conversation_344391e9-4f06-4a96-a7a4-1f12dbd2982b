{"version": 3, "file": "providerSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/providerSettingsSection.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,iDAAiD;AACjD,wDAAqD;AACrD,4CAAyC;AAGzC;;GAEG;AACH,MAAa,qBAAqB;IACzB,MAAM,CAAC,YAAY,CAAoC;IACtD,MAAM,CAAU,QAAQ,GAAG,kBAAkB,CAAC;IAErC,MAAM,CAAsB;IAC5B,aAAa,CAAa;IAC1B,QAAQ,CAA0B;IAC3C,WAAW,CAAS;IACpB,YAAY,GAAwB,EAAE,CAAC;IAE/C;;SAEK;IACE,MAAM,CAAC,YAAY,CAAC,YAAwB,EAAE,UAAkB,EAAE,OAAgC;QACvG,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YAC3C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEd,sCAAsC;QACtC,IAAI,qBAAqB,CAAC,YAAY,EAAE,CAAC;YACvC,qBAAqB,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACzD,qBAAqB,CAAC,YAAY,CAAC,WAAW,GAAG,UAAU,CAAC;YAC5D,qBAAqB,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,gCAAgC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,qBAAqB,CAAC,QAAQ,EAC9B,GAAG,UAAU,WAAW,EACxB,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;YACE,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;gBAC1C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC;aAC/C;YACD,uBAAuB,EAAE,IAAI;SAC9B,CACF,CAAC;QAEF,qBAAqB,CAAC,YAAY,GAAG,IAAI,qBAAqB,CAAC,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3G,CAAC;IAED,YAAoB,KAA0B,EAAE,YAAwB,EAAE,UAAkB,EAAE,OAAgC;QAC5H,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,yCAAyC;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,wCAAwC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACrC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChB,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxB,KAAK,mBAAmB;oBACtB,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBACtC,MAAM;gBACR,KAAK,oBAAoB;oBACvB,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnD,MAAM;gBACR,KAAK,gBAAgB;oBACnB,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC/C,MAAM;gBACR,KAAK,oBAAoB;oBACvB,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACvC,MAAM;gBACR,KAAK,WAAW;oBACd,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC9B,MAAM;gBACR,KAAK,iBAAiB;oBACpB,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBACpC,MAAM;gBACR,KAAK,gBAAgB;oBACnB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;oBACtC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBACtC,MAAM;gBACR,KAAK,eAAe;oBAClB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAClC,MAAM;gBACR,KAAK,cAAc;oBACjB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACjD,MAAM;gBACR,KAAK,mBAAmB;oBACtB,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACnE,MAAM;gBACR,KAAK,iBAAiB;oBACpB,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACnD,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACtB,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,OAAO,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;wBAC9B,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACzC,CAAC;yBAAM,IAAI,OAAO,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;wBACpC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACxC,CAAC;yBAAM,IAAI,OAAO,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;wBACrC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACzC,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACxC,CAAC;oBACD,MAAM;gBACR,KAAK,gBAAgB;oBACnB,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAC5C,MAAM;YACV,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CAClB,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,wBAAwB;QACpC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,WAAW,YAAY,CAAC,CAAC;YAC5D,CAAC;YAED,iCAAiC;YACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAEpC,+BAA+B;YAC/B,MAAM,MAAM,GAAG,QAAQ,CAAC,sBAAsB,EAAE,CAAC;YAEjD,wBAAwB;YACxB,MAAM,QAAQ,GAAG;gBACf,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,cAAc,EAAE,QAAQ,CAAC,cAAc;gBACvC,6BAA6B,EAAE,QAAQ,CAAC,6BAA6B;gBACrE,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,YAAY,EAAE,QAAQ,CAAC,YAAY;aACpC,CAAC;YAEF,wCAAwC;YACxC,MAAM,eAAe,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;YAC9D,MAAM,SAAS,GAAG,eAAe,EAAE,UAAU,KAAK,IAAI,CAAC,WAAW,CAAC;YAEnE,+BAA+B;YAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,gBAAgB;gBACtB,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,yCAAyC,KAAK,EAAE;aAC1D,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,yBAAyB,CAAC,IAAuB;QAC7D,IAAI,CAAC;YACH,gFAAgF;YAChF,MAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,WAAW,YAAY,CAAC,CAAC;YAC5D,CAAC;YAED,2FAA2F;YAC3F,MAAM,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC;YAErB,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,YAAY;oBAClB,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,qBAAqB,IAAI,CAAC,WAAW,sBAAsB;iBACrE,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,oCAAoC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAE9D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,wCAAwC,KAAK,EAAE;aACzD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CAAC,IAAyB;QAC3D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,WAAW,YAAY,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,WAAW,2DAA2D,CAAC,CAAC;YAC3G,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,sBAAsB;gBAC5B,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,OAAO,EAAE,UAAU,CAAC,OAAO;aAC5B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAE1D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,sBAAsB;gBAC5B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2BAA2B,KAAK,EAAE;aAC5C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,yBAAyB;QACrC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,uBAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAEtE,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,2BAA2B;iBACxD,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,CAAC,WAAW,sBAAsB,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAEhE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mCAAmC,KAAK,EAAE;aACpD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,WAAW,YAAY,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,WAAW,2DAA2D,CAAC,CAAC;YAC3G,CAAC;YAED,2BAA2B;YAC3B,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;YAE3C,iCAAiC;YACjC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAEtD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,yBAAyB,KAAK,EAAE;aAC1C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,OAAO;QACb,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;SAEK;IACG,kBAAkB,CAAC,OAAuB;QAChD,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CACpC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,qBAAqB,CAAC,CACxE,CAAC;QAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CACnC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,sBAAsB,CAAC,CACzE,CAAC;QAEF,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,kBAAkB,CAAC,CACzE,CAAC;QAEF,MAAM,KAAK,GAAG,IAAA,gBAAQ,GAAE,CAAC;QAEzB,4BAA4B;QAC5B,OAAO;;;;;yBAKc,IAAI,CAAC,WAAW;+CACM,QAAQ;kGAC2C,OAAO,CAAC,SAAS,8BAA8B,KAAK,gCAAgC,OAAO,CAAC,SAAS;;;;;oCAKnK,OAAO;;;;;;;;;;;;;;uEAc4B,IAAI,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAgGtD,KAAK;;0CAEI,IAAI,CAAC,WAAW;;iCAEzB,KAAK,UAAU,SAAS;;oBAErC,CAAC;IACnB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CAAC,OAAiC;QACnE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE3D,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBAElC,sCAAsC;gBACtC,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;oBACtD,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAE3D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,8BAA8B,KAAK,EAAE;aAC/C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,SAAS,GAAG,uBAAU,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC9D,EAAE,EAAE,QAAQ,CAAC,UAAU;gBACvB,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,YAAY,EAAE,QAAQ,CAAC,YAAY,EAAE;aACtC,CAAC,CAAC,CAAC;YAEJ,oCAAoC;YACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,cAAc;gBACpB,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAE7D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,4BAA4B,KAAK,EAAE;aAC7C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,uBAAU,CAAC,WAAW,CAAC,MAAM,CAAQ,CAAC;YAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,2BAA2B;YAC3B,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,UAAU,EAAE,CAAC;YAE/C,iCAAiC;YACjC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;oBAClC,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;oBAC5F,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;iBAC7F,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAE3D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,8BAA8B,KAAK,EAAE;aAC/C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAChD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,uBAAU,CAAC,WAAW,CAAC,MAAM,CAAQ,CAAC;YAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,gBAAgB;YAChB,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAE9D,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,0BAA0B;iBACpC,CAAC,CAAC;gBAEH,0BAA0B;gBAC1B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAEzD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,4BAA4B,KAAK,EAAE;aAC7C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,wBAAwB,CAAC,GAAW,EAAE,QAAgB;QAClE,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,uBAAU,CAAC,WAAW,CAAC,MAAM,CAAQ,CAAC;YAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,qBAAqB;YACrB,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEhE,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,qBAAqB;oBAC3B,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,+BAA+B;iBACzC,CAAC,CAAC;gBAEH,0BAA0B;gBAC1B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAE9D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,iCAAiC,KAAK,EAAE;aAClD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,sBAAsB,CAAC,OAAe;QAClD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,uBAAU,CAAC,WAAW,CAAC,MAAM,CAAQ,CAAC;YAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,mBAAmB;YACnB,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAExD,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,4BAA4B;iBACtC,CAAC,CAAC;gBAEH,0BAA0B;gBAC1B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAE3D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,8BAA8B,KAAK,EAAE;aAC/C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,qBAAqB,CAAC,YAAY,GAAG,SAAS,CAAC;QAE/C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAC3C,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;;AAlpBH,sDAmpBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { getNonce } from '../../utilities/utils';\nimport { llmService } from '../../../llm/llmService';\nimport { Logger } from '../../../logger';\nimport { LLMProviderConfig } from '../../../llm/llmProvider';\n\n/**\n * Panel for configuring LLM provider settings\n */\nexport class ProviderSettingsPanel {\n  public static currentPanel: ProviderSettingsPanel | undefined;\n  private static readonly viewType = 'providerSettings';\n\n  private readonly _panel: vscode.WebviewPanel;\n  private readonly _extensionUri: vscode.Uri;\n  private readonly _context: vscode.ExtensionContext;\n  private _providerId: string;\n  private _disposables: vscode.Disposable[] = [];\n\n  /**\n     * Create or show the provider settings panel\n     */\n  public static createOrShow(extensionUri: vscode.Uri, providerId: string, context: vscode.ExtensionContext) {\n    const column = vscode.window.activeTextEditor\n      ? vscode.window.activeTextEditor.viewColumn\n      : undefined;\n\n    // If we already have a panel, show it\n    if (ProviderSettingsPanel.currentPanel) {\n      ProviderSettingsPanel.currentPanel._panel.reveal(column);\n      ProviderSettingsPanel.currentPanel._providerId = providerId;\n      ProviderSettingsPanel.currentPanel._update();\n      return;\n    }\n\n    // Otherwise, create a new panel\n    const panel = vscode.window.createWebviewPanel(\n      ProviderSettingsPanel.viewType,\n      `${providerId} Settings`,\n      column || vscode.ViewColumn.One,\n      {\n        enableScripts: true,\n        localResourceRoots: [\n          vscode.Uri.joinPath(extensionUri, 'media'),\n          vscode.Uri.joinPath(extensionUri, 'resources')\n        ],\n        retainContextWhenHidden: true\n      }\n    );\n\n    ProviderSettingsPanel.currentPanel = new ProviderSettingsPanel(panel, extensionUri, providerId, context);\n  }\n\n  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, providerId: string, context: vscode.ExtensionContext) {\n    this._panel = panel;\n    this._extensionUri = extensionUri;\n    this._providerId = providerId;\n    this._context = context;\n\n    // Set the webview's initial html content\n    this._update();\n\n    // Listen for when the panel is disposed\n    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);\n\n    // Handle messages from the webview\n    this._panel.webview.onDidReceiveMessage(\n      async (message) => {\n        switch (message.command) {\n          case 'getProviderConfig':\n            await this._handleGetProviderConfig();\n            break;\n          case 'saveProviderConfig':\n            await this._handleSaveProviderConfig(message.data);\n            break;\n          case 'testConnection':\n            await this._handleTestConnection(message.data);\n            break;\n          case 'setDefaultProvider':\n            await this._handleSetDefaultProvider();\n            break;\n          case 'getModels':\n            await this._handleGetModels();\n            break;\n          case 'getAllProviders':\n            await this._handleGetAllProviders();\n            break;\n          case 'selectProvider':\n            this._providerId = message.providerId;\n            await this._handleGetProviderConfig();\n            break;\n          case 'getGGUFModels':\n            await this._handleGetGGUFModels();\n            break;\n          case 'addGGUFModel':\n            await this._handleAddGGUFModel(message.filePath);\n            break;\n          case 'downloadGGUFModel':\n            await this._handleDownloadGGUFModel(message.url, message.fileName);\n            break;\n          case 'removeGGUFModel':\n            await this._handleRemoveGGUFModel(message.modelId);\n            break;\n          case 'cancel':\n            this._panel.dispose();\n            break;\n          case 'log':\n            if (message.level === 'error') {\n              Logger.instance.error(message.message);\n            } else if (message.level === 'warn') {\n              Logger.instance.warn(message.message);\n            } else if (message.level === 'debug') {\n              Logger.instance.debug(message.message);\n            } else {\n              Logger.instance.info(message.message);\n            }\n            break;\n          case 'showOpenDialog':\n            this._handleShowOpenDialog(message.options);\n            break;\n        }\n      },\n      null,\n      this._disposables\n    );\n  }\n\n  /**\n     * Handle getting the provider configuration\n     */\n  private async _handleGetProviderConfig(): Promise<void> {\n    try {\n      const provider = llmService.getProvider(this._providerId);\n      if (!provider) {\n        throw new Error(`Provider ${this._providerId} not found`);\n      }\n\n      // Get the provider configuration\n      const config = provider.getConfig();\n\n      // Get the configuration fields\n      const fields = provider.getConfigurationFields();\n\n      // Get provider metadata\n      const metadata = {\n        providerId: provider.providerId,\n        displayName: provider.displayName,\n        description: provider.description,\n        website: provider.website,\n        requiresApiKey: provider.requiresApiKey,\n        supportsEndpointConfiguration: provider.supportsEndpointConfiguration,\n        defaultEndpoint: provider.defaultEndpoint,\n        defaultModel: provider.defaultModel\n      };\n\n      // Check if this is the default provider\n      const defaultProvider = await llmService.getDefaultProvider();\n      const isDefault = defaultProvider?.providerId === this._providerId;\n\n      // Send the data to the webview\n      this._panel.webview.postMessage({\n        type: 'providerConfig',\n        config,\n        fields,\n        metadata,\n        isDefault\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting provider config:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error getting provider configuration: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle saving the provider configuration\n     */\n  private async _handleSaveProviderConfig(data: LLMProviderConfig): Promise<void> {\n    try {\n      // IMPORTANT: Allow providers to securely store apiKey via provider.updateConfig\n      const provider = llmService.getProvider(this._providerId);\n      if (!provider) {\n        throw new Error(`Provider ${this._providerId} not found`);\n      }\n\n      // Delegate update to provider so it can use SecretStorage via BaseLLMProvider.updateConfig\n      await provider.updateConfig(data);\n      const success = true;\n\n      if (success) {\n        this._panel.webview.postMessage({\n          type: 'saveResult',\n          success: true,\n          message: `Configuration for ${this._providerId} saved successfully.`\n        });\n      } else {\n        throw new Error(`Failed to save configuration for ${this._providerId}`);\n      }\n    } catch (error) {\n      Logger.instance.error('Error saving provider config:', error);\n\n      this._panel.webview.postMessage({\n        type: 'saveResult',\n        success: false,\n        message: `Error saving provider configuration: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle testing the connection to the provider\n     */\n  private async _handleTestConnection(data: { modelId: string }): Promise<void> {\n    try {\n      const provider = llmService.getProvider(this._providerId);\n      if (!provider) {\n        throw new Error(`Provider ${this._providerId} not found`);\n      }\n\n      if (!provider.isConfigured()) {\n        throw new Error(`Provider ${this._providerId} is not configured. Please save your configuration first.`);\n      }\n\n      const testResult = await provider.testConnection(data.modelId);\n\n      this._panel.webview.postMessage({\n        type: 'connectionTestResult',\n        success: testResult.success,\n        message: testResult.message\n      });\n    } catch (error) {\n      Logger.instance.error('Error testing connection:', error);\n\n      this._panel.webview.postMessage({\n        type: 'connectionTestResult',\n        success: false,\n        message: `Connection test failed: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle setting this provider as the default\n     */\n  private async _handleSetDefaultProvider(): Promise<void> {\n    try {\n      const success = await llmService.setDefaultProvider(this._providerId);\n\n      if (success) {\n        this._panel.webview.postMessage({\n          type: 'setDefaultResult',\n          success: true,\n          message: `${this._providerId} set as default provider.`\n        });\n      } else {\n        throw new Error(`Failed to set ${this._providerId} as default provider`);\n      }\n    } catch (error) {\n      Logger.instance.error('Error setting default provider:', error);\n\n      this._panel.webview.postMessage({\n        type: 'setDefaultResult',\n        success: false,\n        message: `Error setting default provider: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle getting the available models from the provider\n     */\n  private async _handleGetModels(): Promise<void> {\n    try {\n      const provider = llmService.getProvider(this._providerId);\n      if (!provider) {\n        throw new Error(`Provider ${this._providerId} not found`);\n      }\n\n      if (!provider.isConfigured()) {\n        throw new Error(`Provider ${this._providerId} is not configured. Please save your configuration first.`);\n      }\n\n      // Get the available models\n      const models = await provider.listModels();\n\n      // Send the models to the webview\n      this._panel.webview.postMessage({\n        type: 'models',\n        models: models\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting models:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error getting models: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Generate HTML for the webview panel\n     */\n  private _update() {\n    const webview = this._panel.webview;\n    webview.html = this._getWebviewContent(webview);\n  }\n\n  /**\n     * Generate the webview HTML content for provider settings\n     */\n  private _getWebviewContent(webview: vscode.Webview): string {\n    const scriptUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'media', 'providerSettings.js')\n    );\n\n    const styleUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'media', 'providerSettings.css')\n    );\n\n    const logoUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'resources', 'codessa-logo.png')\n    );\n\n    const nonce = getNonce();\n\n    // Generate the HTML content\n    return `<!DOCTYPE html>\n            <html lang=\"en\">\n            <head>\n                <meta charset=\"UTF-8\">\n                <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n                <title>${this._providerId} Settings</title>\n                <link rel=\"stylesheet\" href=\"${styleUri}\">\n                <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; img-src ${webview.cspSource} https:; script-src 'nonce-${nonce}' 'unsafe-inline'; style-src ${webview.cspSource};\">\n            </head>\n            <body>\n                <div class=\"config-container\">\n                    <header class=\"config-header\">\n                        <img src=\"${logoUri}\" alt=\"Codessa Logo\" class=\"logo\" />\n                        <h1>LLM Provider Settings</h1>\n                    </header>\n\n                    <div class=\"tabs-container\">\n                        <div class=\"tabs-header\">\n                            <div class=\"tab active\" data-tab=\"provider-settings\">Provider Settings</div>\n                            <div class=\"tab\" data-tab=\"providers-list\">All Providers</div>\n                            <div class=\"tab\" data-tab=\"gguf-models\">GGUF Models</div>\n                        </div>\n\n                        <!-- Provider Settings Tab -->\n                        <div id=\"provider-settings\" class=\"tab-content active\">\n                            <div class=\"provider-info\">\n                                <h2><span id=\"provider-display-name\">${this._providerId}</span></h2>\n                                <p id=\"provider-description\"></p>\n                                <p id=\"provider-website\"></p>\n                            </div>\n\n                            <div class=\"config-form\">\n                                <div class=\"form-section\">\n                                    <h2>Provider Configuration</h2>\n                                    <div id=\"config-fields-container\">\n                                        <!-- Configuration fields will be inserted here -->\n                                    </div>\n                                </div>\n\n                                <div class=\"form-section\">\n                                    <h2>Models</h2>\n                                    <div class=\"form-group\">\n                                        <label for=\"default-model\">Default Model:</label>\n                                        <select id=\"default-model\" class=\"form-control\">\n                                            <option value=\"\">-- Select a model --</option>\n                                            <!-- Models will be populated dynamically -->\n                                        </select>\n                                        <div class=\"description\">The default model to use with this provider</div>\n                                    </div>\n                                    <div class=\"form-group\">\n                                        <button id=\"btn-refresh-models\" class=\"btn secondary\">Refresh Models</button>\n                                        <span id=\"refresh-status\"></span>\n                                    </div>\n                                    <div class=\"form-group models-list-container\">\n                                        <label>Available Models:</label>\n                                        <div id=\"models-list\" class=\"models-list\">\n                                            <!-- Models will be listed here -->\n                                            <div class=\"loading-models\">Loading models...</div>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                <div class=\"form-section\">\n                                    <h2>Connection Test</h2>\n                                    <div class=\"form-group\">\n                                        <label for=\"test-model\">Model to Test:</label>\n                                        <select id=\"test-model\" class=\"form-control\">\n                                            <option value=\"\">-- Select a model --</option>\n                                            <!-- Models will be populated dynamically -->\n                                        </select>\n                                    </div>\n                                    <div class=\"form-group\">\n                                        <button id=\"btn-test-connection\" class=\"btn secondary\">Test Connection</button>\n                                        <span id=\"connection-status\"></span>\n                                    </div>\n                                </div>\n\n                                <div class=\"form-section\">\n                                    <h2>Default Provider</h2>\n                                    <div class=\"form-group\">\n                                        <button id=\"btn-set-default\" class=\"btn secondary\">Set as Default Provider</button>\n                                        <span id=\"default-status\"></span>\n                                    </div>\n                                </div>\n\n                                <div class=\"form-actions\">\n                                    <button id=\"btn-save\" class=\"btn primary\">Save Configuration</button>\n                                    <button id=\"btn-cancel\" class=\"btn secondary\">Close</button>\n                                </div>\n                            </div>\n                        </div>\n\n                        <!-- All Providers Tab -->\n                        <div id=\"providers-list\" class=\"tab-content\">\n                            <h2>Available LLM Providers</h2>\n                            <p>Select a provider to configure:</p>\n                            <div id=\"providers-container\">\n                                <!-- Providers will be listed here by category -->\n                                <div class=\"loading\">Loading providers...</div>\n                            </div>\n                        </div>\n\n                        <!-- GGUF Models Tab -->\n                        <div id=\"gguf-models\" class=\"tab-content\">\n                            <h2>GGUF Model Management</h2>\n                            <p>Manage your local GGUF models for use with the GGUF provider:</p>\n\n                            <div class=\"gguf-models-container\">\n                                <div class=\"gguf-model-actions\">\n                                    <button id=\"btn-add-gguf-model\" class=\"btn primary\">Add Local Model</button>\n                                    <button id=\"btn-download-gguf-model\" class=\"btn secondary\">Download Model</button>\n                                </div>\n\n                                <div id=\"gguf-models-list\" class=\"gguf-model-list\">\n                                    <!-- GGUF models will be listed here -->\n                                    <div class=\"loading\">Loading GGUF models...</div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <script nonce=\"${nonce}\">\n                    // Initial data\n                    const providerId = \"${this._providerId}\";\n                </script>\n                <script nonce=\"${nonce}\" src=\"${scriptUri}\"></script>\n            </body>\n            </html>`;\n  }\n\n  /**\n     * Handle showing an open dialog\n     */\n  private async _handleShowOpenDialog(options: vscode.OpenDialogOptions): Promise<void> {\n    try {\n      const result = await vscode.window.showOpenDialog(options);\n\n      if (result && result.length > 0) {\n        const filePath = result[0].fsPath;\n\n        // If this is for a GGUF model, add it\n        if (options.filters && options.filters['GGUF Models']) {\n          await this._handleAddGGUFModel(filePath);\n        }\n      }\n    } catch (error) {\n      Logger.instance.error('Error showing open dialog:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error showing file dialog: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle getting all providers\n     */\n  private async _handleGetAllProviders(): Promise<void> {\n    try {\n      // Get all providers\n      const providers = llmService.getAllProviders().map(provider => ({\n        id: provider.providerId,\n        displayName: provider.displayName,\n        description: provider.description,\n        website: provider.website,\n        isConfigured: provider.isConfigured()\n      }));\n\n      // Send the providers to the webview\n      this._panel.webview.postMessage({\n        type: 'allProviders',\n        providers\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting all providers:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error getting providers: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle getting GGUF models\n     */\n  private async _handleGetGGUFModels(): Promise<void> {\n    try {\n      const ggufProvider = llmService.getProvider('gguf') as any;\n      if (!ggufProvider) {\n        throw new Error('GGUF provider not found');\n      }\n\n      // Get the available models\n      const models = await ggufProvider.listModels();\n\n      // Send the models to the webview\n      this._panel.webview.postMessage({\n        type: 'ggufModels',\n        models: models.map((model: any) => ({\n          id: model.id,\n          name: model.name,\n          path: model.description?.includes('Path:') ? model.description.split('Path:')[1].trim() : '',\n          size: model.description?.includes('Size:') ? model.description.split('Size:')[1].trim() : ''\n        }))\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting GGUF models:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error getting GGUF models: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle adding a GGUF model\n     */\n  private async _handleAddGGUFModel(filePath: string): Promise<void> {\n    try {\n      const ggufProvider = llmService.getProvider('gguf') as any;\n      if (!ggufProvider) {\n        throw new Error('GGUF provider not found');\n      }\n\n      // Add the model\n      const success = await ggufProvider.addModelFromFile(filePath);\n\n      if (success) {\n        this._panel.webview.postMessage({\n          type: 'ggufModelAdded',\n          success: true,\n          message: 'Model added successfully'\n        });\n\n        // Refresh the models list\n        await this._handleGetGGUFModels();\n      } else {\n        throw new Error('Failed to add model');\n      }\n    } catch (error) {\n      Logger.instance.error('Error adding GGUF model:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error adding GGUF model: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle downloading a GGUF model\n     */\n  private async _handleDownloadGGUFModel(url: string, fileName: string): Promise<void> {\n    try {\n      const ggufProvider = llmService.getProvider('gguf') as any;\n      if (!ggufProvider) {\n        throw new Error('GGUF provider not found');\n      }\n\n      // Download the model\n      const success = await ggufProvider.downloadModel(url, fileName);\n\n      if (success) {\n        this._panel.webview.postMessage({\n          type: 'ggufModelDownloaded',\n          success: true,\n          message: 'Model downloaded successfully'\n        });\n\n        // Refresh the models list\n        await this._handleGetGGUFModels();\n      } else {\n        throw new Error('Failed to download model');\n      }\n    } catch (error) {\n      Logger.instance.error('Error downloading GGUF model:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error downloading GGUF model: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Handle removing a GGUF model\n     */\n  private async _handleRemoveGGUFModel(modelId: string): Promise<void> {\n    try {\n      const ggufProvider = llmService.getProvider('gguf') as any;\n      if (!ggufProvider) {\n        throw new Error('GGUF provider not found');\n      }\n\n      // Remove the model\n      const success = await ggufProvider.removeModel(modelId);\n\n      if (success) {\n        this._panel.webview.postMessage({\n          type: 'ggufModelRemoved',\n          success: true,\n          message: 'Model removed successfully'\n        });\n\n        // Refresh the models list\n        await this._handleGetGGUFModels();\n      } else {\n        throw new Error('Failed to remove model');\n      }\n    } catch (error) {\n      Logger.instance.error('Error removing GGUF model:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error removing GGUF model: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Clean up resources\n     */\n  public dispose() {\n    ProviderSettingsPanel.currentPanel = undefined;\n\n    this._panel.dispose();\n\n    while (this._disposables.length) {\n      const disposable = this._disposables.pop();\n      if (disposable) {\n        disposable.dispose();\n      }\n    }\n  }\n}\n"]}