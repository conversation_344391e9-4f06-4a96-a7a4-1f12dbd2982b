import { WorkflowStepResult } from '../../src/agents/workflows/workflowEngine';

/**
 * Extended WorkflowStepResult with benchmark-specific metrics
 */
export interface BenchmarkStepResult extends WorkflowStepResult {
  /** Duration in milliseconds */
  duration: number;
  
  /** Memory usage in MB */
  memoryUsage: number;
  
  /** Timestamp of when the step completed */
  timestamp: string;
  
  /** Additional metadata */
  metadata?: Record<string, any>;
}

/**
 * Benchmark configuration for a workflow
 */
export interface BenchmarkWorkflowConfig {
  /** Number of steps in the workflow */
  steps: number;
  
  /** Delay between steps in milliseconds */
  stepDelay: number;
  
  /** Memory operations configuration */
  memoryOperations: {
    read: number;
    write: number;
    search?: number;
    update?: number;
  };
  
  /** Revolutionary features configuration */
  revolutionaryFeatures?: {
    quantumAnalysis?: boolean;
    neuralSynthesis?: boolean;
  };
}

/**
 * Benchmark statistics
 */
export interface BenchmarkStats {
  /** Minimum value */
  min: number;
  
  /** Maximum value */
  max: number;
  
  /** Average value */
  average: number;
  
  /** Median value */
  median: number;
  
  /** 95th percentile */
  p95: number;
  
  /** 99th percentile */
  p99: number;
  
  /** Total value (sum of all values) */
  total: number;
}

/**
 * Benchmark result for a single workflow run
 */
export interface WorkflowBenchmarkResult {
  /** Workflow complexity level */
  complexity: 'simple' | 'medium' | 'complex';
  
  /** Timestamp of when the benchmark was run */
  timestamp: string;
  
  /** Number of iterations */
  iterations: number;
  
  /** Success rate (0-1) */
  successRate: number;
  
  /** Duration statistics */
  duration: BenchmarkStats;
  
  /** Memory usage statistics */
  memory: BenchmarkStats;
  
  /** Statistics by step */
  steps: Record<string, BenchmarkStats>;
  
  /** Configuration used for this benchmark */
  config: BenchmarkWorkflowConfig;
}
