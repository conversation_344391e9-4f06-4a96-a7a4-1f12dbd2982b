{"version": 3, "file": "checkpointWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/checkpointWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAaH,4DA4MC;AAtND,2CAA2D;AAE3D,yCAAsC;AAEtC,qDAAqD;AACrD,iDAAqD;AAErD;;GAEG;AACH,SAAgB,wBAAwB,CAAC,OAIxC;IACC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,qBAAqB,CAAC;IACnD,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,wCAAwC,CAAC;IAEpF,uDAAuD;IACvD,MAAM,QAAQ,GAAoB;QAChC,EAAE,EAAE,uBAAuB,IAAI,CAAC,GAAG,EAAE,EAAE;QACvC,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,YAAY;QAC3B,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE;YACL;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,OAAO;aACf;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,QAAQ;aAChB;SACF;QACD,KAAK,EAAE;YACL;gBACE,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,cAAc;aACrB;SACF;QACD,WAAW,EAAE,OAAO;KACrB,CAAC;IAEF,kDAAkD;IAClD,MAAM,oBAAoB,GAAc;QACtC,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,mBAAmB;QACzB,KAAK,EAAE,mBAAmB;QAC1B,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAE5C,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,sBAAsB,CAAC;YACxE,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC;YAExC,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,8BAAiB,CAAC,gBAAgB,CACzD,WAAW,EACX,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EACtC,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE,CAC7B,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE;wBACP,iBAAiB,EAAE,UAAU;qBAC9B;oBACD,QAAQ,EAAE;wBACR,IAAI,sBAAW,CACb,mBAAmB,EACnB,EAAE,EACF,uBAAuB,UAAU,CAAC,EAAE,MAAM,WAAW,EAAE,CACxD;qBACF;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE;wBACP,iBAAiB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE;qBAC5C;oBACD,QAAQ,EAAE;wBACR,IAAI,sBAAW,CACb,mBAAmB,EACnB,EAAE,EACF,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAC9C;qBACF;iBACF,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;IAEF,MAAM,sBAAsB,GAAc;QACxC,EAAE,EAAE,qBAAqB;QACzB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,wBAAwB;QAC9B,KAAK,EAAE,wBAAwB;QAC/B,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAEnD,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC;YAEhD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO;oBACL,OAAO,EAAE;wBACP,mBAAmB,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;qBAC5D;oBACD,QAAQ,EAAE;wBACR,IAAI,sBAAW,CACb,qBAAqB,EACrB,EAAE,EACF,kCAAkC,CACnC;qBACF;iBACF,CAAC;YACJ,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,8BAAiB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;gBAE3E,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO;wBACL,OAAO,EAAE;4BACP,mBAAmB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;yBACvC;wBACD,QAAQ,EAAE;4BACR,IAAI,sBAAW,CACb,qBAAqB,EACrB,EAAE,EACF,2CAA2C,YAAY,EAAE,CAC1D;yBACF;qBACF,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,OAAO;wBACL,OAAO,EAAE;4BACP,mBAAmB,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE;yBAClD;wBACD,QAAQ,EAAE;4BACR,IAAI,sBAAW,CACb,qBAAqB,EACrB,EAAE,EACF,wBAAwB,CACzB;yBACF;qBACF,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE;wBACP,mBAAmB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE;qBAC9C;oBACD,QAAQ,EAAE;wBACR,IAAI,sBAAW,CACb,qBAAqB,EACrB,EAAE,EACF,qCAAqC,KAAK,CAAC,OAAO,EAAE,CACrD;qBACF;iBACF,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;IAEF,wBAAwB;IACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC1C,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAE5C,iCAAiC;IACjC,QAAQ,CAAC,KAAK,GAAG;QACf;YACE,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,cAAc;SACrB;QACD;YACE,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,mBAAmB;YAC3B,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,sBAAsB;YAC5B,SAAS,EAAE,KAAK,EAAE,KAAU,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,KAAK,mBAAmB,CAAC;YAC9F,aAAa,EAAE,QAAQ;SACxB;QACD;YACE,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,qBAAqB;YAC7B,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,wBAAwB;YAC9B,SAAS,EAAE,KAAK,EAAE,KAAU,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,KAAK,qBAAqB,CAAC;YAChG,aAAa,EAAE,QAAQ;SACxB;QACD;YACE,MAAM,EAAE,mBAAmB;YAC3B,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,kCAAkC;SACzC;QACD;YACE,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,oCAAoC;SAC3C;KACF,CAAC;IAEF,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Checkpoint Workflow\n *\n * This module provides a workflow for managing code checkpoints:\n * - Creating checkpoints before making changes\n * - Rolling back to previous checkpoints\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { ToolMessage, StructuredTool } from 'src/managers';\nimport { GraphDefinition, GraphState, GraphNode } from './types';\nimport { Logger } from '../../logger';\n\n// Import the CheckpointManager from its new location\nimport { checkpointManager } from '../../checkpoint';\n\n/**\n * Creates a checkpoint workflow\n */\nexport function createCheckpointWorkflow(options: {\n  name?: string;\n  description?: string;\n  tools?: (ITool | StructuredTool)[];\n}): GraphDefinition {\n  const name = options.name || 'Checkpoint Workflow';\n  const description = options.description || 'Workflow for managing code checkpoints';\n\n  // Create a simple workflow with input and output nodes\n  const workflow: GraphDefinition = {\n    id: `checkpoint-workflow-${Date.now()}`,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'checkpoint',\n    type: 'checkpoint',\n    nodes: [\n      {\n        id: 'input',\n        type: 'input',\n        name: 'Input',\n        label: 'Input'\n      },\n      {\n        id: 'output',\n        type: 'output',\n        name: 'Output',\n        label: 'Output'\n      }\n    ],\n    edges: [\n      {\n        source: 'input',\n        target: 'output',\n        type: 'checkpoint',\n        name: 'Default Flow'\n      }\n    ],\n    startNodeId: 'input'\n  };\n\n  // Add specialized nodes for checkpoint management\n  const createCheckpointNode: GraphNode = {\n    id: 'create_checkpoint',\n    type: 'tool',\n    name: 'Create Checkpoint',\n    label: 'Create Checkpoint',\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Creating checkpoint');\n\n      const description = state.inputs?.description || 'Automatic checkpoint';\n      const files = state.inputs?.files || [];\n\n      try {\n        const checkpoint = await checkpointManager.createCheckpoint(\n          description,\n          Array.isArray(files) ? files : [files],\n          state.inputs?.metadata || {}\n        );\n\n        return {\n          outputs: {\n            create_checkpoint: checkpoint\n          },\n          messages: [\n            new ToolMessage(\n              'create_checkpoint',\n              {},\n              `Checkpoint created: ${checkpoint.id} - ${description}`\n            )\n          ]\n        };\n      } catch (error: any) {\n        return {\n          outputs: {\n            create_checkpoint: { error: error.message }\n          },\n          messages: [\n            new ToolMessage(\n              'create_checkpoint',\n              {},\n              `Error creating checkpoint: ${error.message}`\n            )\n          ]\n        };\n      }\n    }\n  };\n\n  const rollbackCheckpointNode: GraphNode = {\n    id: 'rollback_checkpoint',\n    type: 'tool',\n    name: 'Rollback to Checkpoint',\n    label: 'Rollback to Checkpoint',\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Rolling back to checkpoint');\n\n      const checkpointId = state.inputs?.checkpointId;\n\n      if (!checkpointId) {\n        return {\n          outputs: {\n            rollback_checkpoint: { error: 'No checkpoint ID provided' }\n          },\n          messages: [\n            new ToolMessage(\n              'rollback_checkpoint',\n              {},\n              'Error: No checkpoint ID provided'\n            )\n          ]\n        };\n      }\n\n      try {\n        const success = await checkpointManager.rollbackToCheckpoint(checkpointId);\n\n        if (success) {\n          return {\n            outputs: {\n              rollback_checkpoint: { success: true }\n            },\n            messages: [\n              new ToolMessage(\n                'rollback_checkpoint',\n                {},\n                `Successfully rolled back to checkpoint: ${checkpointId}`\n              )\n            ]\n          };\n        } else {\n          return {\n            outputs: {\n              rollback_checkpoint: { error: 'Rollback failed' }\n            },\n            messages: [\n              new ToolMessage(\n                'rollback_checkpoint',\n                {},\n                'Error: Rollback failed'\n              )\n            ]\n          };\n        }\n      } catch (error: any) {\n        return {\n          outputs: {\n            rollback_checkpoint: { error: error.message }\n          },\n          messages: [\n            new ToolMessage(\n              'rollback_checkpoint',\n              {},\n              `Error rolling back to checkpoint: ${error.message}`\n            )\n          ]\n        };\n      }\n    }\n  };\n\n  // Add nodes to workflow\n  workflow.nodes.push(createCheckpointNode);\n  workflow.nodes.push(rollbackCheckpointNode);\n\n  // Add edges to connect the nodes\n  workflow.edges = [\n    {\n      source: 'input',\n      target: 'output',\n      type: 'checkpoint',\n      name: 'Default Flow'\n    },\n    {\n      source: 'input',\n      target: 'create_checkpoint',\n      type: 'conditional',\n      name: 'To Create Checkpoint',\n      condition: async (state: any) => Promise.resolve(state.inputs?.action === 'create_checkpoint'),\n      conditionType: 'custom'\n    },\n    {\n      source: 'input',\n      target: 'rollback_checkpoint',\n      type: 'conditional',\n      name: 'To Rollback Checkpoint',\n      condition: async (state: any) => Promise.resolve(state.inputs?.action === 'rollback_checkpoint'),\n      conditionType: 'custom'\n    },\n    {\n      source: 'create_checkpoint',\n      target: 'output',\n      type: 'checkpoint',\n      name: 'From Create Checkpoint To Output'\n    },\n    {\n      source: 'rollback_checkpoint',\n      target: 'output',\n      type: 'checkpoint',\n      name: 'From Rollback Checkpoint To Output'\n    }\n  ];\n\n  return workflow;\n}\n"]}