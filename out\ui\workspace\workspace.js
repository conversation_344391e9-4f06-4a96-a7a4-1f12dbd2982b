"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceHelper = void 0;
const vscode = __importStar(require("vscode"));
/**
 * Helper functions for workspace context
 */
class WorkspaceHelper {
    /**
     * Create workspace context with proper types
     */
    static createWorkspaceContext() {
        const editor = vscode.window.activeTextEditor;
        return {
            currentFile: editor?.document.uri.toString(),
            selection: editor ? {
                text: editor.document.getText(editor.selection) || '',
                range: {
                    start: editor.document.offsetAt(editor.selection.start),
                    end: editor.document.offsetAt(editor.selection.end)
                }
            } : undefined,
            workspaceFolders: Array.from(vscode.workspace.workspaceFolders ?? [])
                .map(folder => folder.uri.toString())
        };
    }
    /**
     * Get the text content of the current file
     */
    static getCurrentFileText() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return undefined;
        }
        return editor.document.getText();
    }
    /**
     * Get the path of the current file
     */
    static getCurrentFilePath() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return undefined;
        }
        return editor.document.uri.fsPath;
    }
}
exports.WorkspaceHelper = WorkspaceHelper;
//# sourceMappingURL=workspace.js.map