/**
 * Professional Settings Manager
 * Handles all settings operations with proper validation, persistence, and error handling
 */

import * as vscode from 'vscode';
import { getConfig as _getConfig, setConfig } from '../../config';
import { Logger } from '../../logger';
import { Settings, SettingsSection } from './types';

export interface SettingsValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

export class SettingsManager {
  private static instance: SettingsManager;
  private settings: Map<string, unknown> = new Map();
  private validators: Map<string, (value: unknown) => SettingsValidationResult> = new Map();
  private changeListeners: Map<string, ((value: unknown) => void)[]> = new Map();
  private saveTimeout: NodeJS.Timeout | null = null;
  private isInitialized = false;

  public static getInstance(): SettingsManager {
    if (!SettingsManager.instance) {
      SettingsManager.instance = new SettingsManager();
    }
    return SettingsManager.instance;
  }

  private constructor() {
    this.initializeValidators();
  }

  /**
     * Initialize the settings manager (should be called after VS Code is ready)
     */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }
        
    try {
      await this.loadAllSettings();
      this.isInitialized = true;
      Logger.instance.info('Settings manager initialized successfully');
    } catch (error) {
      Logger.instance.error('Failed to initialize settings manager:', error);
      throw error;
    }
  }

  /**
     * Initialize validation rules for all settings
     */
  private initializeValidators(): void {
    // Revolutionary Features Validators
    this.addValidator('revolutionaryFeatures.goddessMode.adaptiveLevel', (value: number) => {
      const errors: string[] = [];
      const warnings: string[] = [];
            
      if (typeof value !== 'number' || value < 0 || value > 100) {
        errors.push('Adaptive level must be a number between 0 and 100');
      } else if (value < 50) {
        warnings.push('Low adaptive level may reduce Goddess Mode effectiveness');
      }
            
      return { isValid: errors.length === 0, errors, warnings };
    });

    this.addValidator('revolutionaryFeatures.quantumAnalysis.confidenceThreshold', (value: number) => {
      const errors: string[] = [];
      const warnings: string[] = [];
            
      if (typeof value !== 'number' || value < 0 || value > 1) {
        errors.push('Confidence threshold must be a number between 0 and 1');
      } else if (value < 0.5) {
        warnings.push('Low confidence threshold may produce unreliable results');
      }
            
      return { isValid: errors.length === 0, errors, warnings };
    });

    this.addValidator('revolutionaryFeatures.neuralSynthesis.learningRate', (value: number) => {
      const errors: string[] = [];
      const warnings: string[] = [];
            
      if (typeof value !== 'number' || value < 0.001 || value > 0.1) {
        errors.push('Learning rate must be a number between 0.001 and 0.1');
      } else if (value > 0.05) {
        warnings.push('High learning rate may cause instability');
      }
            
      return { isValid: errors.length === 0, errors, warnings };
    });

    // Provider Settings Validators
    this.addValidator('providers.*.apiKey', (value: string) => {
      const errors: string[] = [];
      const warnings: string[] = [];
            
      if (typeof value !== 'string' || value.trim().length === 0) {
        errors.push('API key is required');
      } else if (value.length < 10) {
        warnings.push('API key seems too short');
      }
            
      return { isValid: errors.length === 0, errors, warnings };
    });

    // Memory Settings Validators
    this.addValidator('memory.maxMemories', (value: number) => {
      const errors: string[] = [];
      const warnings: string[] = [];
            
      if (typeof value !== 'number' || value < 10 || value > 10000) {
        errors.push('Max memories must be a number between 10 and 10000');
      } else if (value > 5000) {
        warnings.push('High memory count may impact performance');
      }
            
      return { isValid: errors.length === 0, errors, warnings };
    });

    // General Settings Validators
    this.addValidator('general.theme', (value: string) => {
      const errors: string[] = [];
      const validThemes = ['system', 'light', 'dark'];
            
      if (typeof value !== 'string' || !validThemes.includes(value)) {
        errors.push('Theme must be one of: system, light, dark');
      }
            
      return { isValid: errors.length === 0, errors, warnings: [] };
    });

    this.addValidator('general.language', (value: string) => {
      const errors: string[] = [];
      const validLanguages = ['en', 'de', 'fr', 'es', 'zh'];
            
      if (typeof value !== 'string' || !validLanguages.includes(value)) {
        errors.push('Language must be one of: en, de, fr, es, zh');
      }
            
      return { isValid: errors.length === 0, errors, warnings: [] };
    });
  }

  /**
     * Add a validator for a specific setting key
     */
  public addValidator<T = unknown>(key: string, validator: (value: T) => SettingsValidationResult): void {
    // Use type assertion to handle the generic type
    this.validators.set(key, validator as (value: unknown) => SettingsValidationResult);
  }

  /**
     * Load all settings from VS Code configuration
     */
  private async loadAllSettings(): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration('codessa');
            
      // Load all configuration keys by inspecting the configuration object
      const configValues = JSON.parse(JSON.stringify(config));
      const allKeys = Object.keys(configValues);
            
      for (const key of allKeys) {
        try {
          const value = config.get(key);
          this.settings.set(key, value);
        } catch (error) {
          Logger.instance.warn(`Failed to load setting ${key}:`, error);
        }
      }
            
      Logger.instance.info(`Loaded ${this.settings.size} settings successfully`);
    } catch (error) {
      Logger.instance.error('Failed to load settings:', error);
      throw error;
    }
  }

  /**
     * Get a setting value with type safety
     */
  public getSetting<T>(key: string, defaultValue: T): T {
    if (!this.isInitialized) {
      Logger.instance.warn('Settings manager not initialized, returning default value');
      return defaultValue;
    }
        
    const value = this.settings.get(key);
    return value !== undefined ? (value as T) : defaultValue;
  }

  /**
     * Set a setting value with validation
     */
  public async setSetting<T = unknown>(key: string, value: T, immediate = false): Promise<SettingsValidationResult> {
    if (!this.isInitialized) {
      Logger.instance.warn('Settings manager not initialized, attempting to initialize');
      await this.initialize();
    }

    // Validate the value
    const validation = this.validateSetting(key, value);
        
    if (!validation.isValid) {
      Logger.instance.warn(`Invalid setting value for ${key}:`, validation.errors);
      return validation;
    }

    // Update local cache
    this.settings.set(key, value);

    // Notify listeners
    this.notifyChangeListeners(key, value);

    // Save to VS Code configuration
    if (immediate) {
      await this.saveSettingImmediate(key, value);
    } else {
      this.debouncedSave(key, value);
    }

    return validation;
  }

  /**
     * Validate a setting value
     */
  private validateSetting(key: string, value: unknown): SettingsValidationResult {
    const validator = this.validators.get(key);
    if (validator) {
      // Safe to call with any value since validators are added with proper type checking
      return validator(value);
    }

    // Check for wildcard validators
    for (const [validatorKey, validatorFn] of this.validators.entries()) {
      if (validatorKey.includes('*')) {
        const pattern = validatorKey.replace('*', '.*');
        const regex = new RegExp(`^${pattern}$`);
        if (regex.test(key)) {
          return validatorFn(value);
        }
      }
    }

    // Default validation - just check if value is not undefined
    return {
      isValid: value !== undefined,
      errors: value === undefined ? ['Value cannot be undefined'] : [],
      warnings: []
    };
  }

  /**
     * Save setting immediately to VS Code configuration
     */
  private async saveSettingImmediate(key: string, value: unknown): Promise<void> {
    try {
      const success = await setConfig(key, value);
      if (!success) {
        throw new Error(`Failed to save setting ${key}`);
      }
      Logger.instance.debug(`Setting saved: ${key}`);
    } catch (error) {
      Logger.instance.error(`Failed to save setting ${key}:`, error);
      throw error;
    }
  }

  /**
     * Debounced save to avoid excessive writes
     */
  private debouncedSave(key: string, value: unknown): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(async () => {
      try {
        await this.saveSettingImmediate(key, value);
      } catch (error) {
        console.error('Debounced save error:', error);
      }
    }, 500); // 500ms debounce
  }

  /**
     * Add a change listener for a specific setting
     */
  public addChangeListener<T = unknown>(key: string, listener: (value: T) => void): void {
    console.log('Adding change listener for key:', key);
    if (!this.changeListeners.has(key)) {
      this.changeListeners.set(key, []);
    }
    // Use type assertion to handle the generic type
    (this.changeListeners.get(key) as ((value: unknown) => void)[]).push(listener as (value: unknown) => void);
  }

  /**
     * Remove a change listener
     */
  public removeChangeListener<T = unknown>(key: string, listener: (value: T) => void): void {
    console.log('Removing change listener for key:', key);
    const listeners = this.changeListeners.get(key) as (((value: T) => void) & ((value: unknown) => void))[] | undefined;
    if (listeners) {
      const index = listeners.indexOf(listener as (value: unknown) => void);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
     * Notify all change listeners for a setting
     */
  private notifyChangeListeners(key: string, value: unknown): void {
    const listeners = this.changeListeners.get(key);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          // Safe to call with any value since listeners are added with proper type checking
          listener(value);
        } catch (error) {
          Logger.instance.error(`Error in change listener for ${key}:`, error);
        }
      });
    }
  }

  /**
     * Get all settings as a plain object
     */
  public getAllSettings(): Record<string, unknown> {
    const result: Record<string, unknown> = {};
    for (const [key, value] of this.settings.entries()) {
      result[key] = value;
    }
    return result;
  }

  /**
     * Reset a setting to its default value
     */
  public async resetSetting(key: string): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration('codessa');
      const inspection = config.inspect(key);
            
      if (inspection?.defaultValue !== undefined) {
        await this.setSetting(key, inspection.defaultValue, true);
      } else {
        // Remove the setting to use VS Code default
        await setConfig(key, undefined);
        this.settings.delete(key);
      }
            
      Logger.instance.info(`Setting reset: ${key}`);
    } catch (error) {
      Logger.instance.error(`Failed to reset setting ${key}:`, error);
      throw error;
    }
  }

  /**
     * Reset all settings to defaults
     */
  public async resetAllSettings(): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration('codessa');
      console.log('Resetting all settings for config:', config.keys());
      const allKeys = Array.from(this.settings.keys());
            
      for (const key of allKeys) {
        await this.resetSetting(key);
      }
            
      Logger.instance.info('All settings reset to defaults');
    } catch (error) {
      Logger.instance.error('Failed to reset all settings:', error);
      throw error;
    }
  }

  /**
     * Export settings to JSON
     */
  public exportSettings(): string {
    return JSON.stringify(this.getAllSettings(), null, 2);
  }

  /**
     * Import settings from JSON
     */
  public async importSettings(jsonString: string): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
        
    try {
      const importedSettings = JSON.parse(jsonString) as Record<string, unknown>;
            
      for (const [key, value] of Object.entries(importedSettings)) {
        try {
          const validation = await this.setSetting(key, value, true);
          if (!validation.isValid) {
            errors.push(`${key}: ${validation.errors.join(', ')}`);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          errors.push(`${key}: Failed to import - ${errorMessage}`);
        }
      }
            
      return { success: errors.length === 0, errors };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Invalid JSON format';
      return { success: false, errors: [errorMessage] };
    }
  }

  /**
     * Get setting sections configuration
     */
  public getSettingSections(): SettingsSection[] {
    return [
      {
        id: 'revolutionary',
        title: 'Revolutionary Features',
        icon: '✨',
        description: 'Configure advanced AI capabilities including Goddess Mode, Quantum Analysis, and Neural Synthesis',
        order: 1,
        component: 'revolutionaryFeaturesSection'
      },
      {
        id: 'general',
        title: 'General',
        icon: '⚙️',
        description: 'Basic extension settings and preferences',
        order: 2,
        component: 'generalSettingsSection'
      },
      {
        id: 'providers',
        title: 'AI Providers',
        icon: '🤖',
        description: 'Configure AI model providers and API settings',
        order: 3,
        component: 'providersModelsSettingsSection'
      },
      {
        id: 'agents',
        title: 'Agents',
        icon: '👥',
        description: 'Manage AI agents and their configurations',
        order: 4,
        component: 'agentsSettingsSection'
      },
      {
        id: 'memory',
        title: 'Memory',
        icon: '🧠',
        description: 'Configure memory system and storage options',
        order: 5,
        component: 'memorySettingsSection'
      },
      {
        id: 'workflows',
        title: 'Workflows',
        icon: '🔄',
        description: 'Manage automated workflows and templates',
        order: 6,
        component: 'workflowsSettingsSection'
      },
      {
        id: 'ui',
        title: 'Interface',
        icon: '🎨',
        description: 'User interface and theme customization',
        order: 7,
        component: 'uiThemeSettingsSection'
      },
      {
        id: 'tts',
        title: 'Text-to-Speech',
        icon: '🔊',
        description: 'Configure text-to-speech and voice settings',
        order: 8,
        component: 'ttsSettingsSection'
      },
      {
        id: 'advanced',
        title: 'Advanced',
        icon: '🔧',
        description: 'Advanced settings and developer options',
        order: 9,
        component: 'advancedSettingsSection'
      }
    ].sort((a, b) => a.order - b.order);
  }

  /**
     * Check if settings manager is initialized
     */
  public isReady(): boolean {
    return this.isInitialized;
  }

  /**
     * Dispose of the settings manager
     */
  public dispose(): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }
        
    this.changeListeners.clear();
    this.settings.clear();
    this.validators.clear();
    this.isInitialized = false;
  }
}
