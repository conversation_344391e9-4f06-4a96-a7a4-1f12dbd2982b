{"version": 3, "file": "allSettingsPanel.js", "sourceRoot": "", "sources": ["../../../src/ui/settings/allSettingsPanel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,yCAAsC;AACtC,uDAAoD;AACpD,8EAA2E;AA2B3E,MAAa,gBAAgB;IAUR;IAEA;IACA;IAZX,MAAM,CAAU,QAAQ,GAAG,kBAAkB,CAAC;IAC9C,MAAM,CAAC,YAAY,CAA+B;IACzC,KAAK,CAAsB;IAC3B,YAAY,CAAa;IAClC,WAAW,GAAwB,EAAE,CAAC;IACtC,eAAe,CAAkB;IACjC,cAAc,CAAwB;IAE9C,YACmB,OAAgC,EACjD,YAAwB,EACP,aAA4B,EAC5B,eAAgC;QAHhC,YAAO,GAAP,OAAO,CAAyB;QAEhC,kBAAa,GAAb,aAAa,CAAe;QAC5B,oBAAe,GAAf,eAAe,CAAiB;QAEjD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,iCAAe,CAAC,WAAW,EAAE,CAAC;QAErD,yCAAyC;QACzC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC3C,gBAAgB,CAAC,QAAQ,EACzB,kBAAkB,EAClB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;SACjE,CACF,CAAC;QAEF,wDAAwD;QACxD,IAAI,CAAC,cAAc,GAAG,IAAI,6CAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACnD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,YAAY,CAC9B,OAAgC,EAChC,YAAwB,EACxB,aAA4B,EAC5B,eAAgC;QAEhC,IAAI,gBAAgB,CAAC,YAAY,EAAE,CAAC;YAClC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAClE,OAAO;QACT,CAAC;QAED,gBAAgB,CAAC,YAAY,GAAG,IAAI,gBAAgB,CAClD,OAAO,EACP,YAAY,EACZ,aAAa,EACb,eAAe,CAChB,CAAC;QAEF,kDAAkD;QAClD,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,MAAM,gBAAgB,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,6CAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CACpC,KAAK,EAAE,OAAuB,EAAE,EAAE;YAChC,IAAI,CAAC;gBACH,2CAA2C;gBAC3C,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;oBACxB,KAAK,eAAe;wBAClB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAA+B,CAAC,CAAC;wBAChE,OAAO;oBAET,KAAK,wBAAwB;wBAC3B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBACjC,OAAO;oBAET,KAAK,yBAAyB;wBAC5B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAClC,OAAO;oBAET,KAAK,cAAc;wBACjB,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAA8B,CAAC,CAAC;wBAC9D,OAAO;oBAET,KAAK,gBAAgB;wBACnB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAClC,OAAO;oBAET,KAAK,gBAAgB;wBACnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAgC,CAAC,CAAC;wBAClE,OAAO;gBACX,CAAC;gBAED,uCAAuC;gBACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9E,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAChD,MAAM,eAAe,GAAG;wBACtB,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,UAAU;wBACrC,GAAG,CAAC,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;qBAChG,CAAC;oBAEF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBACvD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC7B,OAAO,EAAE,OAAO;oBAChB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW,CACjB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAA6B;QAC7D,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;QAC/B,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,eAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACnD,OAAO;QACT,CAAC;QACD,MAAM,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,IAAI,GAAG,EAAE,CAAC;QAC5C,eAAM,CAAC,KAAK,CAAC,qBAAqB,OAAO,MAAM,KAAK,EAAE,CAAC,CAAC;QAExD,IAAI,CAAC;YACH,iDAAiD;YACjD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAE/E,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACvB,sCAAsC;gBACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC7B,OAAO,EAAE,gBAAgB;oBACzB,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,GAAG;oBACH,KAAK;oBACL,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,UAAU,CAAC,QAAQ;iBAC9B,CAAC,CAAC;gBAEH,uBAAuB;gBACvB,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;oBAClC,QAAQ,GAAG,EAAE,CAAC;wBACZ,KAAK,OAAO;4BACV,gCAAgC;4BAChC,MAAM;wBACR,KAAK,UAAU;4BACb,mCAAmC;4BACnC,MAAM;oBACV,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,uCAAuC;gBACvC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC7B,OAAO,EAAE,gBAAgB;oBACzB,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,GAAG;oBACH,KAAK;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;oBACnC,QAAQ,EAAE,UAAU,CAAC,QAAQ;iBAC9B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,gBAAgB;gBACzB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,GAAG;gBACH,KAAK;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAA4B;QAC3D,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,eAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAClD,OAAO;QACT,CAAC;QACD,MAAM,OAAO,GAAG,GAAG,OAAO,CAAC,OAAO,IAAI,GAAG,EAAE,CAAC;QAE5C,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,GAAG;gBACH,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,GAAG;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC;YAC3D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,kBAAkB;gBAC3B,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,kBAAkB;gBAC3B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAA8B;QAC/D,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,eAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEnE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC7B,OAAO,EAAE,kBAAkB;oBAC3B,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;gBACH,iCAAiC;gBACjC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC7B,OAAO,EAAE,kBAAkB;oBAC3B,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,MAAM,CAAC,MAAM;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,kBAAkB;gBAC3B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB;aAC1E,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,uDAAuD;YACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,EAAyB,CAAC;YAC9E,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;YAE3D,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBACnC,IAAI,EAAE,iBAAiB;gBACvB,QAAQ;gBACR,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,gBAAgB;gBACzB,QAAQ,EAAE,EAAE;gBACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB;aAC1E,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC;YACzD,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC;YAEnE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,wBAAwB;gBACjC,SAAS;gBACT,cAAc;aACf,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7B,OAAO,EAAE,wBAAwB;gBACjC,SAAS,EAAE,EAAE;gBACb,cAAc,EAAE,IAAI;gBACpB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,kCAAkC;aACnF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,KAAK,CAAC,YAAY,CACrB,GAAG,EAAE;YACH,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC;YAC1C,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW,CACjB,CAAC;IACJ,CAAC;IAEO,OAAO;QACb,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YAC1C,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAC/C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,aAAa,CAAC,CAC/D,CAAC;QACF,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAC/C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAC1E,CAAC;QAEF,OAAO;;;;;;0BAMe,SAAS;;;;2BAIR,SAAS;;;;;;;;;;;;;;gBAcpB,CAAC;IACf,CAAC;;AApWH,4CAqWC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { MemoryManager } from '../../memory/memoryManager';\nimport { ProviderManager } from '../../llm/providerManager';\nimport { logger } from '../../logger';\nimport { SettingsManager } from './settingsManager';\nimport { WebviewMessageHandler } from '../utilities/webviewMessageHandler';\nimport { Settings, SettingsSection } from './types';\n\n// Define message types for the webview communication\ninterface WebviewMessage {\n    command: string;\n    [key: string]: unknown;\n}\n\ninterface UpdateSettingMessage extends WebviewMessage {\n    command: 'updateSetting';\n    key: string;\n    value: unknown;\n}\n\ninterface ResetSettingMessage extends WebviewMessage {\n    command: 'resetSetting';\n    key: string;\n}\n\ninterface ImportSettingsMessage extends WebviewMessage {\n    command: 'importSettings';\n    settings: string; // JSON string of settings\n}\n\ntype SettingsChangeListener = (key: string, value: unknown) => void;\n\nexport class AllSettingsPanel {\n  private static readonly viewType = 'codessa.settings';\n  private static currentPanel: AllSettingsPanel | undefined;\n  private readonly panel: vscode.WebviewPanel;\n  private readonly extensionUri: vscode.Uri;\n  private disposables: vscode.Disposable[] = [];\n  private settingsManager: SettingsManager;\n  private messageHandler: WebviewMessageHandler;\n\n  private constructor(\n    private readonly context: vscode.ExtensionContext,\n    extensionUri: vscode.Uri,\n    private readonly memoryManager: MemoryManager,\n    private readonly providerManager: ProviderManager\n  ) {\n    this.extensionUri = extensionUri;\n    this.settingsManager = SettingsManager.getInstance();\n\n    // Create and configure the webview panel\n    this.panel = vscode.window.createWebviewPanel(\n      AllSettingsPanel.viewType,\n      'Codessa Settings',\n      vscode.ViewColumn.One,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true,\n        localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'media')]\n      }\n    );\n\n    // Initialize the message handler after panel is created\n    this.messageHandler = new WebviewMessageHandler(this.context);\n\n    this.panel.webview.html = this.getWebviewContent();\n    this.setupMessageHandlers();\n    this.setupPanelEvents();\n  }\n\n  public static async createOrShow(\n    context: vscode.ExtensionContext,\n    extensionUri: vscode.Uri,\n    memoryManager: MemoryManager,\n    providerManager: ProviderManager\n  ) {\n    if (AllSettingsPanel.currentPanel) {\n      AllSettingsPanel.currentPanel.panel.reveal(vscode.ViewColumn.One);\n      return;\n    }\n\n    AllSettingsPanel.currentPanel = new AllSettingsPanel(\n      context,\n      extensionUri,\n      memoryManager,\n      providerManager\n    );\n\n    // Initialize settings manager if not already done\n    if (!AllSettingsPanel.currentPanel.settingsManager.isReady()) {\n      await AllSettingsPanel.currentPanel.settingsManager.initialize();\n    }\n  }\n\n  private setupMessageHandlers(): void {\n    this.messageHandler = new WebviewMessageHandler(this.context);\n\n    this.panel.webview.onDidReceiveMessage(\n      async (message: WebviewMessage) => {\n        try {\n          // First try the settings-specific handlers\n          switch (message.command) {\n            case 'updateSetting':\n              await this.handleSettingUpdate(message as UpdateSettingMessage);\n              return;\n\n            case 'requestInitialSettings':\n              await this.sendInitialSettings();\n              return;\n\n            case 'requestProviderSettings':\n              await this.sendProviderSettings();\n              return;\n\n            case 'resetSetting':\n              await this.handleResetSetting(message as ResetSettingMessage);\n              return;\n\n            case 'exportSettings':\n              await this.handleExportSettings();\n              return;\n\n            case 'importSettings':\n              await this.handleImportSettings(message as ImportSettingsMessage);\n              return;\n          }\n\n          // Then try the generic message handler\n          const response = await this.messageHandler.handleMessage(message, this.panel);\n          if (response !== null && response !== undefined) {\n            const responseMessage = {\n              command: `${message.command}Response`,\n              ...(typeof response === 'object' && !Array.isArray(response) ? response : { result: response })\n            };\n\n            this.panel.webview.postMessage(responseMessage);\n          }\n        } catch (error) {\n          logger.error('Error handling webview message:', error);\n          this.panel.webview.postMessage({\n            command: 'error',\n            error: error instanceof Error ? error.message : String(error)\n          });\n        }\n      },\n      null,\n      this.disposables\n    );\n  }\n\n  private async handleSettingUpdate(message: UpdateSettingMessage): Promise<void> {\n    const { key, value } = message;\n    if (!key) {\n      logger.error('No key provided for setting update');\n      return;\n    }\n    const fullKey = `${message.section}.${key}`;\n    logger.debug(`Updating setting: ${fullKey} = ${value}`);\n\n    try {\n      // Use the settings manager to update the setting\n      const validation = await this.settingsManager.setSetting(fullKey, value, true);\n\n      if (validation.isValid) {\n        // Notify webview of successful update\n        this.panel.webview.postMessage({\n          command: 'settingUpdated',\n          section: message.section,\n          key,\n          value,\n          success: true,\n          warnings: validation.warnings\n        });\n\n        // Handle special cases\n        if (message.section === 'general') {\n          switch (key) {\n            case 'theme':\n              // Handle theme change if needed\n              break;\n            case 'language':\n              // Handle language change if needed\n              break;\n          }\n        }\n      } else {\n        // Notify webview of validation failure\n        this.panel.webview.postMessage({\n          command: 'settingUpdated',\n          section: message.section,\n          key,\n          value,\n          success: false,\n          error: validation.errors.join(', '),\n          warnings: validation.warnings\n        });\n      }\n    } catch (error) {\n      logger.error('Failed to update setting:', error);\n      this.panel.webview.postMessage({\n        command: 'settingUpdated',\n        section: message.section,\n        key,\n        value,\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n  }\n\n  private async handleResetSetting(message: ResetSettingMessage): Promise<void> {\n    const { key } = message;\n    if (!key) {\n      logger.error('No key provided for reset setting');\n      return;\n    }\n    const fullKey = `${message.section}.${key}`;\n\n    try {\n      await this.settingsManager.resetSetting(fullKey);\n      this.panel.webview.postMessage({\n        command: 'settingReset',\n        section: message.section,\n        key,\n        success: true\n      });\n    } catch (error) {\n      logger.error('Failed to reset setting:', error);\n      this.panel.webview.postMessage({\n        command: 'settingReset',\n        section: message.section,\n        key,\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n  }\n\n  private async handleExportSettings(): Promise<void> {\n    try {\n      const settingsJson = this.settingsManager.exportSettings();\n      this.panel.webview.postMessage({\n        command: 'settingsExported',\n        settings: settingsJson,\n        success: true\n      });\n    } catch (error) {\n      logger.error('Failed to export settings:', error);\n      this.panel.webview.postMessage({\n        command: 'settingsExported',\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n  }\n\n  private async handleImportSettings(message: ImportSettingsMessage): Promise<void> {\n    const { settings } = message;\n    if (!settings) {\n      logger.error('No settings provided for import');\n      return;\n    }\n\n    try {\n      const result = await this.settingsManager.importSettings(settings);\n\n      if (result.success) {\n        this.panel.webview.postMessage({\n          command: 'settingsImported',\n          success: true\n        });\n        // Reload settings in the webview\n        await this.sendInitialSettings();\n      } else {\n        this.panel.webview.postMessage({\n          command: 'settingsImported',\n          success: false,\n          errors: result.errors\n        });\n      }\n    } catch (error) {\n      logger.error('Failed to import settings:', error);\n      this.panel.webview.postMessage({\n        command: 'settingsImported',\n        success: false,\n        error: error instanceof Error ? error.message : 'Failed to load settings'\n      });\n    }\n  }\n\n  private async sendInitialSettings(): Promise<void> {\n    try {\n      // Get settings as unknown first, then cast to Settings\n      const settings = this.settingsManager.getAllSettings() as unknown as Settings;\n      const sections = this.settingsManager.getSettingSections();\n      \n      await this.panel.webview.postMessage({\n        type: 'initialSettings',\n        settings,\n        sections\n      });\n    } catch (error) {\n      logger.error('Failed to load initial settings:', error);\n      this.panel.webview.postMessage({\n        command: 'updateSettings',\n        settings: {},\n        error: error instanceof Error ? error.message : 'Failed to load settings'\n      });\n    }\n  }\n\n  private async sendProviderSettings(): Promise<void> {\n    try {\n      const providers = this.providerManager.getAllProviders();\n      const activeProvider = this.providerManager.getDefaultProviderId();\n\n      this.panel.webview.postMessage({\n        command: 'updateProviderSettings',\n        providers,\n        activeProvider\n      });\n    } catch (error) {\n      logger.error('Failed to load provider settings:', error);\n      this.panel.webview.postMessage({\n        command: 'updateProviderSettings',\n        providers: [],\n        activeProvider: null,\n        error: error instanceof Error ? error.message : 'Failed to load provider settings'\n      });\n    }\n  }\n\n  private setupPanelEvents() {\n    this.panel.onDidDispose(\n      () => {\n        AllSettingsPanel.currentPanel = undefined;\n        this.dispose();\n      },\n      null,\n      this.disposables\n    );\n  }\n\n  private dispose() {\n    if (this.panel) {\n      this.panel.dispose();\n    }\n    while (this.disposables.length) {\n      const disposable = this.disposables.pop();\n      if (disposable) {\n        disposable.dispose();\n      }\n    }\n  }\n\n  private getWebviewContent(): string {\n    const scriptUri = this.panel.webview.asWebviewUri(\n      vscode.Uri.joinPath(this.extensionUri, 'media', 'settings.js')\n    );\n    const stylesUri = this.panel.webview.asWebviewUri(\n      vscode.Uri.joinPath(this.extensionUri, 'media', 'styles', 'settings.css')\n    );\n\n    return `<!DOCTYPE html>\n        <html lang=\"en\">\n        <head>\n            <meta charset=\"UTF-8\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n            <title>Codessa Settings</title>\n            <link href=\"${stylesUri}\" rel=\"stylesheet\">\n        </head>\n        <body>\n            <div id=\"codessa-settings-root\"></div>\n            <script src=\"${scriptUri}\"></script>\n            <script>\n                // Ensure initial settings are always requested after script loads\n                window.addEventListener('DOMContentLoaded', function() {\n                    if (window.acquireVsCodeApi) {\n                        const vscode = window.acquireVsCodeApi();\n                        vscode.postMessage({ command: 'requestInitialSettings' });\n                    } else if (window.parent) {\n                        // fallback for some webview environments\n                        window.parent.postMessage({ command: 'requestInitialSettings' }, '*');\n                    }\n                });\n            </script>\n        </body>\n        </html>`;\n  }\n}\n"]}