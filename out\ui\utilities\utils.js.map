{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/ui/utilities/utils.ts"], "names": [], "mappings": ";AAAA;;GAEG;;AAMH,4BAOC;AAOD,0CAOC;AAOD,gCAOC;AAOD,gCAOC;AAQD,wCAKC;AAlED;;;GAGG;AACH,SAAgB,QAAQ;IACtB,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,MAAM,QAAQ,GAAG,gEAAgE,CAAC;IAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IACvE,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,SAAgB,eAAe,CAAC,SAAiB;IAC/C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;IACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE;QACxC,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;KAClB,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,IAAY;IACrC,OAAO,IAAI;SACR,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;SACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;SACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;SACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;SACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC7B,CAAC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,GAAW;IACpC,IAAI,CAAC;QACH,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,GAAW,EAAE,SAAiB;IAC3D,IAAI,GAAG,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IACD,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AACjD,CAAC", "sourcesContent": ["/**\n * UI Utilities for Codessa\n */\n\n/**\n * Generate a nonce string for Content Security Policy\n * @returns A random nonce string\n */\nexport function getNonce(): string {\n  let text = '';\n  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  for (let i = 0; i < 32; i++) {\n    text += possible.charAt(Math.floor(Math.random() * possible.length));\n  }\n  return text;\n}\n\n/**\n * Format a timestamp to a readable date/time string\n * @param timestamp The timestamp to format\n * @returns A formatted date/time string\n */\nexport function formatTimestamp(timestamp: number): string {\n  const date = new Date(timestamp);\n  return date.toLocaleTimeString(undefined, { \n    hour: '2-digit', \n    minute: '2-digit',\n    second: '2-digit'\n  });\n}\n\n/**\n * Escape HTML special characters to prevent XSS\n * @param text The text to escape\n * @returns Escaped text safe for HTML insertion\n */\nexport function escapeHtml(text: string): string {\n  return text\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#039;');\n}\n\n/**\n * Check if a string is a valid URL\n * @param str The string to check\n * @returns True if the string is a valid URL\n */\nexport function isValidUrl(str: string): boolean {\n  try {\n    new URL(str);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Truncate a string to a maximum length with ellipsis\n * @param str The string to truncate\n * @param maxLength Maximum length before truncation\n * @returns Truncated string with ellipsis if needed\n */\nexport function truncateString(str: string, maxLength: number): string {\n  if (str.length <= maxLength) {\n    return str;\n  }\n  return str.substring(0, maxLength - 3) + '...';\n}\n"]}