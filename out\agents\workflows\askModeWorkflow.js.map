{"version": 3, "file": "askModeWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/askModeWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAaH,sDA6EC;AAKD,gEAoFC;AA/KD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,qBAAqB,CACnC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,OAAc,EACd,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;IAE5D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC5F,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1G,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACxG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KACjG,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,qCAAqC;QACrC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,mBAAmB,KAAK,EAAE;gBAChC,MAAM,EAAE,mBAAmB;gBAC3B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,0CAA0C;YAC1C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,YAAY;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,mBAAmB;gBAC3B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,oBAAoB;YACpB,oBAAoB;YACpB,oBAAoB;YACpB,oBAAoB;YACpB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,KAAsB;QACrC,IAAI,EAAE,CAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,CAAC;KAC/C,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CACxC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,OAAc,EACd,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,IAAI,EAAE,CAAC,CAAC;IAEjE,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;IAClG,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;IAC5F,MAAM,uBAAuB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;IACjH,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,OAAO,CAAC,CAAC;IACzH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC5F,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnH,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjH,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,yBAAyB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9G,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KACpG,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,iCAAiC;QACjC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,oBAAoB,KAAK,EAAE;gBACjC,MAAM,EAAE,eAAe;gBACvB,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,0CAA0C;YAC1C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,YAAY;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,mBAAmB;gBAC3B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,oBAAoB;YACpB,kBAAkB;YAClB,gBAAgB;YAChB,uBAAuB;YACvB,oBAAoB;YACpB,yBAAyB;YACzB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,KAAsB;QACrC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,oBAAoB,CAAC;KAC5C,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa Ask Mode Workflow\n *\n * This module provides workflow templates for Ask Mode:\n * - Question analysis\n * - Context retrieval\n * - Answer generation\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { Logger } from '../../logger';\nimport { StructuredTool } from 'src/managers';\n\n/**\n * Create an Ask Mode workflow for answering questions with context\n */\nexport function createAskModeWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  qaAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating Ask Mode workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const questionAnalysisNode = Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);\n  const contextRetrievalNode = Codessa.createAgentNode('context-retrieval', 'Context Retrieval', qaAgent);\n  const answerGenerationNode = Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);\n  const answerRefinementNode = Codessa.createAgentNode('answer-refinement', 'Answer Refinement', qaAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-question', source: 'input', target: 'question-analysis', type: 'default' },\n    { name: 'question-to-context', source: 'question-analysis', target: 'context-retrieval', type: 'default' },\n    { name: 'context-to-answer', source: 'context-retrieval', target: 'answer-generation', type: 'default' },\n    { name: 'answer-to-refinement', source: 'answer-generation', target: 'answer-refinement', type: 'default' },\n    { name: 'refinement-to-output', source: 'answer-refinement', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect context retrieval to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `context-to-tool-${index}`,\n        source: 'context-retrieval',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to answer generation\n      edges.push({\n        name: `tool-${index}-to-answer`,\n        source: `tool-${index}`,\n        target: 'answer-generation',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      questionAnalysisNode,\n      contextRetrievalNode,\n      answerGenerationNode,\n      answerRefinementNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'ask' as OperationMode,\n    tags: ['ask', 'question-answering', 'context']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized Code Question workflow for answering code-related questions\n */\nexport function createCodeQuestionWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  qaAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating Code Question workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const questionAnalysisNode = Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);\n  const codebaseSearchNode = Codessa.createAgentNode('codebase-search', 'Codebase Search', qaAgent);\n  const codeAnalysisNode = Codessa.createAgentNode('code-analysis', 'Code Analysis', qaAgent);\n  const documentationSearchNode = Codessa.createAgentNode('documentation-search', 'Documentation Search', qaAgent);\n  const answerGenerationNode = Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);\n  const codeExampleGenerationNode = Codessa.createAgentNode('code-example-generation', 'Code Example Generation', qaAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-question', source: 'input', target: 'question-analysis', type: 'default' },\n    { name: 'question-to-codebase', source: 'question-analysis', target: 'codebase-search', type: 'default' },\n    { name: 'question-to-documentation', source: 'question-analysis', target: 'documentation-search', type: 'default' },\n    { name: 'codebase-to-analysis', source: 'codebase-search', target: 'code-analysis', type: 'default' },\n    { name: 'analysis-to-answer', source: 'code-analysis', target: 'answer-generation', type: 'default' },\n    { name: 'documentation-to-answer', source: 'documentation-search', target: 'answer-generation', type: 'default' },\n    { name: 'answer-to-example', source: 'answer-generation', target: 'code-example-generation', type: 'default' },\n    { name: 'example-to-output', source: 'code-example-generation', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect code analysis to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `analysis-to-tool-${index}`,\n        source: 'code-analysis',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to answer generation\n      edges.push({\n        name: `tool-${index}-to-answer`,\n        source: `tool-${index}`,\n        target: 'answer-generation',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      questionAnalysisNode,\n      codebaseSearchNode,\n      codeAnalysisNode,\n      documentationSearchNode,\n      answerGenerationNode,\n      codeExampleGenerationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'ask' as OperationMode,\n    tags: ['ask', 'code', 'question-answering']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}