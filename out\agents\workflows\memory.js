"use strict";
/**
 * Memory-Enhanced Workflow and interfaces for Codessa workflows
 *
 * This module provides workflows for memory-enhanced operations and re-exports memory types and interfaces.
 * - Storing and retrieving information from memory
 * - Using memory to enhance responses
 * - Managing short-term and long-term memory
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowMemoryManager = exports.WorkflowMemoryManager = exports.CodessaMemoryProvider = exports.codessaMemoryManager = void 0;
exports.createMemoryEnhancedWorkflow = createMemoryEnhancedWorkflow;
exports.createConversationMemoryWorkflow = createConversationMemoryWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
// Import the memoryManager singleton for workflow memory operations
const memoryManager_1 = require("../../memory/memoryManager");
// Import CodessaMemoryProvider for advanced memory functionality
const codessaMemory_1 = require("../../memory/codessa/codessaMemory");
// Re-export the CodessaMemoryProvider from its new location
var codessaMemory_2 = require("../../memory/codessa/codessaMemory");
Object.defineProperty(exports, "codessaMemoryManager", { enumerable: true, get: function () { return codessaMemory_2.codessaMemoryProvider; } });
Object.defineProperty(exports, "CodessaMemoryProvider", { enumerable: true, get: function () { return codessaMemory_2.CodessaMemoryProvider; } });
/**
 * Create a Memory-Enhanced workflow
 */
function createMemoryEnhancedWorkflow(id, name, description, memoryAgent, tools = []) {
    logger_1.logger.info(`Creating Memory-Enhanced workflow: ${name}`);
    // Initialize memory provider for this workflow
    const memoryProvider = new codessaMemory_1.CodessaMemoryProvider();
    // Create nodes with memory integration
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    // Create agent nodes with memory functionality embedded in the agent
    const queryAnalysisNode = graph_1.Codessa.createAgentNode('query-analysis', 'Query Analysis', memoryAgent);
    const memoryRetrievalNode = graph_1.Codessa.createAgentNode('memory-retrieval', 'Memory Retrieval', memoryAgent);
    const contextEnhancementNode = graph_1.Codessa.createAgentNode('context-enhancement', 'Context Enhancement', memoryAgent);
    const responseGenerationNode = graph_1.Codessa.createAgentNode('response-generation', 'Response Generation', memoryAgent);
    const memorySaveNode = graph_1.Codessa.createAgentNode('memory-save', 'Memory Save', memoryAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Initialize memory functionality for this workflow
    initializeWorkflowMemory(id, memoryProvider);
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },
        { name: 'query-to-memory', source: 'query-analysis', target: 'memory-retrieval', type: 'default' },
        { name: 'memory-to-context', source: 'memory-retrieval', target: 'context-enhancement', type: 'default' },
        { name: 'context-to-response', source: 'context-enhancement', target: 'response-generation', type: 'default' },
        { name: 'response-to-memory', source: 'response-generation', target: 'memory-save', type: 'default' },
        { name: 'memory-to-output', source: 'memory-save', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect context enhancement to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `context-to-tool-${index}`,
                source: 'context-enhancement',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to response generation
            edges.push({
                name: `tool-${index}-to-response`,
                source: `tool-${index}`,
                target: 'response-generation',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            queryAnalysisNode,
            memoryRetrievalNode,
            contextEnhancementNode,
            responseGenerationNode,
            memorySaveNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'memory',
        tags: ['memory', 'context', 'retrieval']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a specialized Conversation Memory workflow
 */
function createConversationMemoryWorkflow(id, name, description, memoryAgent) {
    logger_1.logger.info(`Creating Conversation Memory workflow: ${name}`);
    // Initialize conversation memory provider
    const conversationMemoryProvider = new codessaMemory_1.CodessaMemoryProvider();
    // Create nodes with conversation memory integration
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const messageAnalysisNode = graph_1.Codessa.createAgentNode('message-analysis', 'Message Analysis', memoryAgent);
    const conversationHistoryNode = graph_1.Codessa.createAgentNode('conversation-history', 'Conversation History', memoryAgent);
    const relevanceFilteringNode = graph_1.Codessa.createAgentNode('relevance-filtering', 'Relevance Filtering', memoryAgent);
    const contextIntegrationNode = graph_1.Codessa.createAgentNode('context-integration', 'Context Integration', memoryAgent);
    const responseGenerationNode = graph_1.Codessa.createAgentNode('response-generation', 'Response Generation', memoryAgent);
    const conversationUpdateNode = graph_1.Codessa.createAgentNode('conversation-update', 'Conversation Update', memoryAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Initialize conversation memory for this workflow
    initializeConversationMemory(id, conversationMemoryProvider);
    // Create edges
    const edges = [
        { name: 'input-to-message', source: 'input', target: 'message-analysis', type: 'default' },
        { name: 'message-to-history', source: 'message-analysis', target: 'conversation-history', type: 'default' },
        { name: 'history-to-filtering', source: 'conversation-history', target: 'relevance-filtering', type: 'default' },
        { name: 'filtering-to-integration', source: 'relevance-filtering', target: 'context-integration', type: 'default' },
        { name: 'integration-to-response', source: 'context-integration', target: 'response-generation', type: 'default' },
        { name: 'response-to-update', source: 'response-generation', target: 'conversation-update', type: 'default' },
        { name: 'update-to-output', source: 'conversation-update', target: 'output', type: 'default' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            messageAnalysisNode,
            conversationHistoryNode,
            relevanceFilteringNode,
            contextIntegrationNode,
            responseGenerationNode,
            conversationUpdateNode,
            outputNode
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'memory',
        tags: ['memory', 'conversation', 'context']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Initialize workflow memory using both memory systems
 */
function initializeWorkflowMemory(workflowId, memoryProvider) {
    logger_1.logger.info(`Initializing memory for workflow: ${workflowId}`);
    // Set up memory context for the workflow
    memoryManager_1.memoryManager.addMemory({
        content: `Workflow initialized: ${workflowId}`,
        metadata: {
            workflowId,
            timestamp: Date.now(),
            source: 'system',
            type: 'conversation'
        }
    }).catch(error => {
        logger_1.logger.error('Error initializing workflow memory:', error);
    });
    // Initialize memory provider context
    memoryProvider.addMemory({
        content: `Workflow ${workflowId} started`,
        metadata: {
            workflowId,
            source: 'system',
            type: 'conversation'
        }
    }).catch(error => {
        logger_1.logger.error('Error initializing memory provider:', error);
    });
}
/**
 * Initialize conversation memory using both memory systems
 */
function initializeConversationMemory(workflowId, memoryProvider) {
    logger_1.logger.info(`Initializing conversation memory for workflow: ${workflowId}`);
    // Set up conversation context
    memoryManager_1.memoryManager.addMemory({
        content: `Conversation workflow initialized: ${workflowId}`,
        metadata: {
            workflowId,
            timestamp: Date.now(),
            source: 'system',
            type: 'conversation'
        }
    }).catch(error => {
        logger_1.logger.error('Error initializing conversation memory:', error);
    });
    // Initialize conversation provider
    memoryProvider.addMemory({
        content: `Conversation workflow ${workflowId} ready`,
        metadata: {
            workflowId,
            source: 'system',
            type: 'conversation'
        }
    }).catch(error => {
        logger_1.logger.error('Error initializing conversation memory provider:', error);
    });
}
/**
 * Enhanced memory operations using both memoryManager and CodessaMemoryProvider
 */
class WorkflowMemoryManager {
    memoryProvider;
    constructor() {
        this.memoryProvider = new codessaMemory_1.CodessaMemoryProvider();
    }
    /**
     * Initialize memory for a workflow session
     */
    async initializeWorkflowMemory(workflowId, sessionId) {
        logger_1.logger.info(`Initializing memory for workflow ${workflowId}, session ${sessionId}`);
        // Initialize session in memoryManager
        await memoryManager_1.memoryManager.addMemory({
            content: `Workflow session started: ${workflowId}`,
            metadata: {
                workflowId,
                sessionId,
                timestamp: Date.now(),
                source: 'system',
                type: 'conversation'
            }
        });
        // Initialize session in CodessaMemoryProvider
        await this.memoryProvider.addMemory({
            content: `Session ${sessionId} started for workflow ${workflowId}`,
            metadata: {
                workflowId,
                sessionId,
                source: 'system',
                type: 'conversation'
            }
        });
    }
    /**
     * Store workflow step results with enhanced memory features
     */
    async storeStepResult(workflowId, stepId, input, output, metadata = {}) {
        // Store in memoryManager with structured data
        await memoryManager_1.memoryManager.addMemory({
            content: JSON.stringify({ input, output }),
            metadata: {
                workflowId,
                stepId,
                timestamp: Date.now(),
                source: 'system',
                type: 'conversation',
                ...metadata
            }
        });
        // Store in CodessaMemoryProvider with enhanced features
        await this.memoryProvider.addMemory({
            content: `Step ${stepId}: ${JSON.stringify(output)}`,
            metadata: {
                workflowId,
                stepId,
                source: 'system',
                type: 'conversation',
                ...metadata
            }
        });
    }
    /**
     * Retrieve workflow context using both memory systems
     */
    async getWorkflowContext(workflowId, query) {
        // Get context from memoryManager
        const memoryContext = await memoryManager_1.memoryManager.searchMemories({
            query,
            limit: 20,
            filter: { type: 'conversation' }
        });
        // Get enhanced context from CodessaMemoryProvider
        const enhancedContext = await this.memoryProvider.searchMemories({
            query,
            limit: 10,
            filter: { type: 'conversation' }
        });
        return {
            memoryContext,
            enhancedContext,
            combinedInsights: this.combineMemoryInsights(memoryContext, enhancedContext)
        };
    }
    /**
     * Combine insights from both memory systems
     */
    combineMemoryInsights(memoryContext, enhancedContext) {
        return {
            totalMemories: memoryContext.length,
            enhancedFeatures: enhancedContext,
            patterns: this.extractPatterns(memoryContext),
            recommendations: this.generateRecommendations(memoryContext, enhancedContext)
        };
    }
    /**
     * Extract patterns from memory data
     */
    extractPatterns(memories) {
        // Analyze patterns in workflow execution
        const stepFrequency = memories.reduce((acc, memory) => {
            const stepId = memory.metadata?.stepId;
            if (stepId) {
                acc[stepId] = (acc[stepId] || 0) + 1;
            }
            return acc;
        }, {});
        return { stepFrequency };
    }
    /**
     * Generate recommendations based on memory analysis
     */
    generateRecommendations(memoryContext, enhancedContext) {
        const recommendations = [];
        if (memoryContext.length > 100) {
            recommendations.push('Consider archiving older workflow memories');
        }
        if (enhancedContext && enhancedContext.patterns) {
            recommendations.push('Enhanced patterns detected - optimize workflow based on insights');
        }
        return recommendations;
    }
}
exports.WorkflowMemoryManager = WorkflowMemoryManager;
// Export singleton instance for use in workflows
exports.workflowMemoryManager = new WorkflowMemoryManager();
//# sourceMappingURL=memory.js.map