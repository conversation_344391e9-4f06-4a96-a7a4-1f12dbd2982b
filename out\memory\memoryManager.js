"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.memoryManager = exports.MemoryManager = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../logger");
const config_1 = require("../config");
const codessaMemory_1 = require("./codessa/codessaMemory");
const codessaGraphMemory_1 = require("./codessa/codessaGraphMemory");
const fileChunking_1 = require("./codessa/fileChunking");
const uuid_1 = require("uuid");
const quantumMemorySystem_1 = require("./quantum/quantumMemorySystem");
/**
 * Dynamic import helper to break circular dependency
 */
async function getVectorMemoryManager() {
    const { VectorMemoryManager } = await Promise.resolve().then(() => __importStar(require('./vectorMemory')));
    return VectorMemoryManager.getInstance();
}
/**
 * Memory Manager - Core class for managing agent memories and cross-agent memory sharing.
 *
 * @class MemoryManager
 * @implements {IMemoryManagerAdapter}
 * @description Centralized memory management system that handles storage, retrieval, and sharing of memories
 * across different agents. Supports multiple memory backends and provides advanced features like
 * cross-agent memory sharing, access control, and memory analytics.
 *
 * @example
 * ```typescript
 * const memoryManager = new MemoryManager();
 * await memoryManager.initialize(context);
 *
 * // Add a new memory
 * const memory = await memoryManager.addMemory('Important information');
 *
 * // Search memories
 * const results = await memoryManager.searchMemories({ query: 'important' });
 * ```
 * Central manager for all memory operations
 */
class MemoryManager {
    /** Extension context for VS Code integration */
    context;
    /** Active memory provider instance for storage and retrieval */
    memoryProvider;
    /** Vector memory manager for semantic search capabilities */
    vectorMemoryManager;
    /** Graph-based memory system for complex relationships */
    codessaGraphMemory;
    /** Quantum memory system for advanced memory operations */
    quantumMemorySystem;
    /** Tracks whether the memory manager has been initialized */
    initialized = false;
    /** Event emitter for memory change notifications */
    _onMemoriesChanged = new vscode.EventEmitter();
    /**
     * Event that fires when memories are added, updated, or removed
     * @event
     */
    onMemoriesChanged = this._onMemoriesChanged.event;
    /**
     * Register a listener for memory changes (for IMemoryOperations interface)
     * This method is required by the IMemoryOperations interface
     */
    registerMemoryChangeListener(listener) {
        this._onMemoriesChanged.event(listener);
    }
    /**
     * Map of agent IDs to their cross-agent memory states
     * @private
     */
    crossAgentMemories = new Map();
    /**
     * Global access log for auditing all memory operations
     * @private
     */
    globalAccessLog = [];
    /**
     * Maximum number of entries to keep in the access log
     * @private
     */
    maxAccessLogSize = 1000;
    /**
     * Cache for search results to improve performance
     * @private
     */
    memorySearchCache = new Map();
    /**
     * Time in milliseconds after which search cache entries expire
     * @private
     * @readonly
     */
    searchCacheExpiry = 2 * 60 * 1000; // 2 minutes
    /**
     * Maximum number of search results to cache
     * @private
     * @readonly
     */
    maxSearchCacheSize = 100;
    /**
     * Global flag indicating if memory features are enabled
     * @static
     * @private
     */
    static memoryEnabled = false;
    /**
     * Checks if the memory system is globally enabled
     * @returns {boolean} True if memory features are enabled, false otherwise
     */
    static getMemoryEnabled() {
        return MemoryManager.memoryEnabled;
    }
    /**
     * Enables or disables the memory system globally
     * @param {boolean} enabled - Whether to enable or disable the memory system
     */
    static setMemoryEnabled(enabled) {
        MemoryManager.memoryEnabled = enabled;
        logger_1.Logger.instance.info(`Memory ${enabled ? 'enabled' : 'disabled'}`);
    }
    /**
     * Initializes the memory manager with the provided VS Code extension context.
     * This method sets up the memory provider, vector memory manager, and other components.
     *
     * @param {vscode.ExtensionContext} context - The VS Code extension context
     * @returns {Promise<void>} A promise that resolves when initialization is complete
     * @throws {Error} If initialization fails
     *
     * @example
     * ```typescript
     * await memoryManager.initialize(vscodeContext);
     * ```
     */
    async initialize(context) {
        if (this.initialized) {
            return;
        }
        try {
            this.context = context;
            // Check if memory is enabled
            const memoryEnabled = (0, config_1.getConfig)('memory.enabled', true);
            if (!memoryEnabled) {
                logger_1.Logger.instance.info('Memory system is disabled');
                return;
            }
            // Initialize memory provider based on configuration
            const memorySystem = (0, config_1.getConfig)('memory.system', 'codessa');
            if (memorySystem === 'codessa') {
                try {
                    // Initialize Codessa memory provider
                    this.memoryProvider = codessaMemory_1.codessaMemoryProvider;
                    await this.memoryProvider.initialize(context);
                    // Try to initialize Codessa Graph memory
                    try {
                        this.codessaGraphMemory = new codessaGraphMemory_1.CodessaGraphMemory(this.memoryProvider);
                        await this.codessaGraphMemory.initialize();
                        logger_1.Logger.instance.info('Codessa Graph memory initialized');
                    }
                    catch (graphError) {
                        // Log but don't fail if graph memory can't initialize
                        logger_1.Logger.instance.warn('Failed to initialize Codessa Graph memory, continuing without it:', graphError);
                    }
                    logger_1.Logger.instance.info('Codessa memory provider initialized');
                }
                catch (error) {
                    // If Codessa memory provider fails, fall back to basic vector memory
                    logger_1.Logger.instance.warn('Failed to initialize Codessa memory provider, falling back to basic vector memory:', error);
                    // Initialize basic vector memory manager as fallback
                    this.memoryProvider = undefined;
                    this.vectorMemoryManager = await getVectorMemoryManager();
                    // Set up the memory operations for vector memory manager to break circular dependency
                    this.vectorMemoryManager.setMemoryOperations(this);
                    await this.vectorMemoryManager.initialize(context);
                    logger_1.Logger.instance.info('Basic vector memory manager initialized as fallback');
                }
            }
            else {
                // Initialize basic vector memory manager
                this.vectorMemoryManager = await getVectorMemoryManager();
                // Set up the memory operations for vector memory manager to break circular dependency
                this.vectorMemoryManager.setMemoryOperations(this);
                await this.vectorMemoryManager.initialize(context);
                logger_1.Logger.instance.info('Basic vector memory manager initialized');
            }
            // Initialize Quantum Memory System
            try {
                // We need SupervisorAgent for quantum memory, but it might not be available yet
                // So we'll initialize it later when needed
                logger_1.Logger.instance.info('Quantum Memory System will be initialized when SupervisorAgent is available');
            }
            catch (error) {
                logger_1.Logger.instance.warn('Failed to initialize Quantum Memory System:', error);
            }
            // Register event handlers
            if (this.memoryProvider) {
                if ('onMemoriesChanged' in this.memoryProvider) {
                    this.memoryProvider.onMemoriesChanged(() => {
                        this._onMemoriesChanged.fire();
                    });
                }
            }
            if (this.vectorMemoryManager) {
                this.vectorMemoryManager.onMemoriesChanged(() => {
                    this._onMemoriesChanged.fire();
                });
            }
            this.initialized = true;
            logger_1.Logger.instance.info('Memory manager initialized successfully');
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to initialize memory manager:', error);
            throw error;
        }
    }
    /**
     * Adds a new memory or updates an existing one if an ID is provided.
     *
     * @param {string | Omit<MemoryEntry, 'id' | 'timestamp'>} contentOrEntry - The content as a string or a partial memory entry
     * @returns {Promise<MemoryEntry>} The created or updated memory entry
     * @throws {Error} If the memory provider is not initialized or the operation fails
     *
     * @example
     * ```typescript
     * // Add a simple text memory
     * const memory = await memoryManager.addMemory('Important note');
     *
     * // Add a memory with metadata
     * const memoryWithMeta = await memoryManager.addMemory({
     *   content: 'Project requirements',
     *   metadata: {
     *     type: 'requirements',
     *     priority: 'high'
     *   }
     * });
     * ```
     */
    async addMemory(contentOrEntry) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            // Create memory entry with proper type handling
            const baseMemory = typeof contentOrEntry === 'string'
                ? {
                    content: contentOrEntry,
                    metadata: {
                        source: 'user',
                        type: 'semantic'
                    }
                }
                : contentOrEntry;
            // Create final memory entry with ID and timestamp
            const memoryEntry = {
                ...baseMemory,
                id: (0, uuid_1.v4)(), // Always generate a new ID for memoryEntry
                timestamp: Date.now()
            };
            // Add to memory provider
            if (this.memoryProvider) {
                return await this.memoryProvider.addMemory(memoryEntry);
            }
            else if (this.vectorMemoryManager) {
                return await this.vectorMemoryManager.addMemory(memoryEntry);
            }
            else {
                throw new Error('No memory provider initialized');
            }
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to add memory:', error);
            throw error;
        }
    }
    /**
     * Retrieves all stored memories.
     *
     * @returns {Promise<MemoryEntry[]>} An array of all memory entries
     *
     * @example
     * ```typescript
     * const allMemories = await memoryManager.getMemories();
     * console.log(`Total memories: ${allMemories.length}`);
     * ```
     */
    async getMemories() {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            if (this.memoryProvider) {
                return await this.memoryProvider.getMemories();
            }
            else if (this.vectorMemoryManager) {
                return await this.vectorMemoryManager.getMemories();
            }
            else {
                return [];
            }
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to get memories:', error);
            return [];
        }
    }
    /**
     * Retrieves a memory entry by its unique identifier.
     *
     * @param {string} id - The unique identifier of the memory to retrieve
     * @returns {Promise<MemoryEntry | undefined>} The memory entry if found, undefined otherwise
     *
     * @example
     * ```typescript
     * const memory = await memoryManager.getMemory('memory-123');
     * if (memory) {
     *   console.log('Found memory:', memory.content);
     * }
     * ```
     */
    async getMemory(id) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            if (this.memoryProvider) {
                return await this.memoryProvider.getMemory(id);
            }
            else if (this.vectorMemoryManager) {
                return await this.vectorMemoryManager.getMemory(id);
            }
            else {
                return undefined;
            }
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to get memory ${id}:`, error);
            return undefined;
        }
    }
    /**
     * Updates an existing memory with new content or metadata.
     *
     * @param {string} id - The ID of the memory to update
     * @param {Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>} updates - The fields to update
     * @returns {Promise<MemoryEntry | undefined>} The updated memory entry, or undefined if not found
     * @throws {Error} If the update operation fails
     *
     * @example
     * ```typescript
     * // Update memory content
     * const updated = await memoryManager.updateMemory('memory-123', {
     *   content: 'Updated content',
     *   metadata: { updatedAt: Date.now() }
     * });
     * ```
     */
    async updateMemory(id, updates) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            if (this.memoryProvider) {
                return await this.memoryProvider.updateMemory(id, updates);
            }
            else if (this.vectorMemoryManager) {
                return await this.vectorMemoryManager.updateMemory(id, updates);
            }
            else {
                return undefined;
            }
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to update memory ${id}:`, error);
            return undefined;
        }
    }
    /**
     * Deletes a memory by its ID.
     *
     * @param {string} id - The ID of the memory to delete
     * @returns {Promise<boolean>} True if the memory was deleted, false if not found
     *
     * @example
     * ```typescript
     * const wasDeleted = await memoryManager.deleteMemory('memory-123');
     * console.log(wasDeleted ? 'Memory deleted' : 'Memory not found');
     * ```
     */
    async deleteMemory(id) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            if (this.memoryProvider) {
                return await this.memoryProvider.deleteMemory(id);
            }
            else if (this.vectorMemoryManager) {
                return await this.vectorMemoryManager.deleteMemory(id);
            }
            else {
                return false;
            }
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to delete memory ${id}:`, error);
            return false;
        }
    }
    /**
     * Removes all stored memories.
     *
     * @returns {Promise<void>} A promise that resolves when all memories are cleared
     *
     * @example
     * ```typescript
     * // Clear all memories (use with caution)
     * await memoryManager.clearMemories();
     * ```
     */
    async clearMemories() {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            if (this.memoryProvider) {
                await this.memoryProvider.clearMemories();
            }
            else if (this.vectorMemoryManager) {
                await this.vectorMemoryManager.clearMemories();
            }
            logger_1.Logger.instance.info('All memories cleared');
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to clear memories:', error);
            throw error;
        }
    }
    /**
     * Searches through stored memories using the provided query and options.
     * Supports contextual ranking and filtering of results.
     *
     * @param {MemorySearchOptions} options - Search configuration and query parameters
     * @returns {Promise<MemoryEntry[]>} An array of matching memory entries, sorted by relevance
     *
     * @example
     * ```typescript
     * // Basic search
     * const results = await memoryManager.searchMemories({
     *   query: 'important note',
     *   limit: 10,
     *   minRelevance: 0.5
     * });
     *
     * // Search with filters
     * const filteredResults = await memoryManager.searchMemories({
     *   query: 'error',
     *   filters: {
     *     type: 'error',
     *     timestamp: { $gt: Date.now() - 86400000 } // Last 24 hours
     *   }
     * });
     * ```
     */
    async searchMemories(options) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            let results = [];
            if (this.memoryProvider) {
                results = await this.memoryProvider.searchMemories(options);
            }
            else if (this.vectorMemoryManager) {
                // Convert options to vector memory manager format
                results = await this.vectorMemoryManager.searchMemories(options.query, options.limit);
            }
            else {
                return [];
            }
            // Apply contextual ranking if enabled
            if ((0, config_1.getConfig)('phase4.enableContextualRanking', true)) {
                results = this.applyContextualRanking(results, options.query);
            }
            return results;
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to search memories:', error);
            return [];
        }
    }
    /**
       * Apply contextual ranking to search results
       */
    applyContextualRanking(results, query) {
        try {
            // Get current workspace context
            const workspaceContext = this.getCurrentWorkspaceContext();
            // Score results based on relevance to current context
            const scoredResults = results.map(result => ({
                entry: result,
                score: this.calculateContextualScore(result, query, workspaceContext)
            }));
            // Sort by score and return entries
            return scoredResults
                .sort((a, b) => b.score - a.score)
                .map(item => item.entry);
        }
        catch (error) {
            logger_1.Logger.instance.warn('Failed to apply contextual ranking:', error);
            return results;
        }
    }
    /**
       * Calculate contextual relevance score for search results
       */
    calculateContextualScore(entry, query, context) {
        let score = 0;
        // Base relevance score (simple text matching)
        const queryLower = query.toLowerCase();
        const contentLower = entry.content.toLowerCase();
        if (contentLower.includes(queryLower))
            score += 10;
        // Context-based scoring
        if (context.currentFile && entry.metadata.tags?.includes('file:' + context.currentFile)) {
            score += 15; // High relevance for current file
        }
        if (context.language && entry.metadata.tags?.includes(context.language)) {
            score += 8; // Medium relevance for same language
        }
        if (context.project && entry.metadata.tags?.includes('project:' + context.project)) {
            score += 5; // Low relevance for same project
        }
        // Recency bonus (newer memories get slight boost)
        const daysSinceCreation = (Date.now() - new Date(entry.timestamp).getTime()) / (1000 * 60 * 60 * 24);
        score += Math.max(0, 5 - daysSinceCreation); // Up to 5 points for recent memories
        // Type-based scoring
        if (entry.metadata.type === 'insight')
            score += 3;
        if (entry.metadata.type === 'code')
            score += 2;
        return score;
    }
    /**
       * Get current workspace context for memory operations
       */
    getCurrentWorkspaceContext() {
        try {
            const context = {};
            // Get active editor information from VS Code API
            try {
                // Use VS Code API directly instead of window object
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor) {
                    context.currentFile = activeEditor.document.fileName;
                    context.language = activeEditor.document.languageId;
                    // Get workspace folder
                    const workspaceFolder = vscode.workspace.getWorkspaceFolder(activeEditor.document.uri);
                    if (workspaceFolder) {
                        context.project = workspaceFolder.name;
                    }
                }
            }
            catch (vscodeError) {
                // VS Code API not available, return empty context
                logger_1.Logger.instance.debug('VS Code API not available for workspace context');
            }
            return context;
        }
        catch (error) {
            logger_1.Logger.instance.warn('Failed to get workspace context:', error);
            return {};
        }
    }
    /**
       * Search memories by semantic similarity
       */
    async searchSimilarMemories(query, options = {}) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            if (this.memoryProvider) {
                return await this.memoryProvider.searchSimilarMemories(query, options);
            }
            else if (this.vectorMemoryManager) {
                // Convert options to vector memory manager format
                return await this.vectorMemoryManager.searchSimilarMemories(query, options.limit);
            }
            else {
                return [];
            }
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to search similar memories:', error);
            return [];
        }
    }
    /**
       * Process a message with Codessa Graph memory
       */
    async processMessage(message) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            // Check if Codessa Graph memory is available
            if ((0, config_1.getConfig)('memory.system', 'codessa') === 'codessa' && this.codessaGraphMemory) {
                return await this.codessaGraphMemory.processMessage(message);
            }
            else {
                throw new Error('Codessa Graph memory not available');
            }
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to process message with Codessa Graph memory:', error);
            throw error;
        }
    }
    /**
       * Chunk a file and store in memory
       */
    async chunkFile(filePath) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            // Check if Codessa memory is available
            if ((0, config_1.getConfig)('memory.system', 'codessa') === 'codessa') {
                return await fileChunking_1.FileChunkingService.chunkFile(filePath);
            }
            else {
                throw new Error('Codessa memory not available for file chunking');
            }
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to chunk file ${filePath}:`, error);
            throw error;
        }
    }
    /**
       * Chunk a workspace folder and store in memory
       */
    async chunkWorkspace(folderPath, includePatterns = ['**/*.{js,ts,jsx,tsx,py,java,c,cpp,cs,go,rb,php,html,css,md,json}'], excludePatterns = ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.git/**']) {
        if (!this.initialized && this.context) {
            await this.initialize(this.context);
        }
        try {
            // Check if Codessa memory is available
            if ((0, config_1.getConfig)('memory.system', 'codessa') === 'codessa') {
                return await fileChunking_1.FileChunkingService.chunkWorkspace(folderPath, includePatterns, excludePatterns);
            }
            else {
                throw new Error('Codessa memory not available for workspace chunking');
            }
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to chunk workspace ${folderPath}:`, error);
            throw error;
        }
    }
    /**
       * Get comprehensive memory settings - OPTIMIZED
       */
    getMemorySettings() {
        if (this.memoryProvider && 'getMemorySettings' in this.memoryProvider) {
            return this.memoryProvider.getMemorySettings();
        }
        else {
            // Return comprehensive default settings
            return {
                // Core settings
                enabled: (0, config_1.getConfig)('memory.enabled', true),
                system: (0, config_1.getConfig)('memory.system', 'codessa'),
                maxMemories: (0, config_1.getConfig)('memory.maxMemories', 1000),
                maxMemoriesPerFile: (0, config_1.getConfig)('phase4.maxMemoriesPerFile', 100),
                memoryRetentionDays: (0, config_1.getConfig)('phase4.memoryRetentionDays', 30),
                relevanceThreshold: (0, config_1.getConfig)('memory.relevanceThreshold', 0.7),
                contextWindowSize: (0, config_1.getConfig)('memory.contextWindowSize', 5),
                conversationHistorySize: (0, config_1.getConfig)('memory.conversationHistorySize', 100),
                // Advanced memory system settings
                enableQuantumMemory: (0, config_1.getConfig)('phase4.enableQuantumMemory', true),
                enablePredictiveInsights: (0, config_1.getConfig)('phase4.enablePredictiveInsights', true),
                enableMemoryVisualization: (0, config_1.getConfig)('phase4.enableMemoryVisualization', true),
                enableCollaborativeMemory: (0, config_1.getConfig)('phase4.enableCollaborativeMemory', true),
                // Vector store settings
                vectorStore: (0, config_1.getConfig)('memory.vectorStore', 'chroma'),
                vectorStoreSettings: {
                    chroma: {
                        directory: (0, config_1.getConfig)('memory.vectorStore.chroma.directory', './.codessa/chroma'),
                        collectionName: (0, config_1.getConfig)('memory.vectorStore.chroma.collectionName', 'codessa_memories')
                    },
                    pinecone: {
                        apiKey: (0, config_1.getConfig)('memory.vectorStore.pinecone.apiKey', ''),
                        environment: (0, config_1.getConfig)('memory.vectorStore.pinecone.environment', ''),
                        indexName: (0, config_1.getConfig)('memory.vectorStore.pinecone.indexName', 'codessa-memories')
                    }
                },
                // Database settings
                database: (0, config_1.getConfig)('memory.database', 'sqlite'),
                databaseSettings: {
                    sqlite: {
                        filename: (0, config_1.getConfig)('memory.database.sqlite.filename', './.codessa/memory.db')
                    },
                    mysql: {
                        host: (0, config_1.getConfig)('memory.database.mysql.host', 'localhost'),
                        port: (0, config_1.getConfig)('memory.database.mysql.port', 3306),
                        user: (0, config_1.getConfig)('memory.database.mysql.user', 'root'),
                        password: (0, config_1.getConfig)('memory.database.mysql.password', ''),
                        database: (0, config_1.getConfig)('memory.database.mysql.database', 'codessa'),
                        table: (0, config_1.getConfig)('memory.database.mysql.table', 'memories')
                    },
                    postgres: {
                        connectionString: (0, config_1.getConfig)('memory.database.postgres.connectionString', ''),
                        schema: (0, config_1.getConfig)('memory.database.postgres.schema', 'codessa')
                    },
                    mongodb: {
                        connectionString: (0, config_1.getConfig)('memory.database.mongodb.connectionString', ''),
                        database: (0, config_1.getConfig)('memory.database.mongodb.database', 'codessa'),
                        collection: (0, config_1.getConfig)('memory.database.mongodb.collection', 'memories')
                    },
                    redis: {
                        url: (0, config_1.getConfig)('memory.database.redis.url', ''),
                        keyPrefix: (0, config_1.getConfig)('memory.database.redis.keyPrefix', 'codessa:')
                    }
                },
                // File processing settings
                fileChunking: {
                    chunkSize: (0, config_1.getConfig)('memory.fileChunking.chunkSize', 1000),
                    chunkOverlap: (0, config_1.getConfig)('memory.fileChunking.chunkOverlap', 200),
                    maxChunksPerFile: (0, config_1.getConfig)('memory.fileChunking.maxChunksPerFile', 100)
                },
                // Performance settings
                cacheSize: (0, config_1.getConfig)('memory.cacheSize', 1000),
                cacheExpiry: (0, config_1.getConfig)('memory.cacheExpiry', 300000), // 5 minutes
                batchSize: (0, config_1.getConfig)('memory.batchSize', 50),
                maxConcurrentOperations: (0, config_1.getConfig)('memory.maxConcurrentOperations', 10)
            };
        }
    }
    /**
       * Update memory settings
       */
    async updateMemorySettings(settings) {
        try {
            if (this.memoryProvider && 'updateMemorySettings' in this.memoryProvider) {
                const updateMethod = this.memoryProvider.updateMemorySettings;
                const result = await Promise.resolve(updateMethod(settings));
                return result === true || result === undefined; // Handle both boolean returns and void returns
            }
            else {
                logger_1.Logger.instance.error('Memory provider does not support updating settings');
                return false;
            }
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to update memory settings:', error);
            return false;
        }
    }
    /**
       * Initialize Quantum Memory System with SupervisorAgent
       */
    async initializeQuantumMemory(supervisorAgent) {
        try {
            if (!this.quantumMemorySystem) {
                // Create an adapter that delegates all IMemoryManagerAdapter methods to this instance
                const memoryManagerAdapter = {
                    // Core memory operations
                    addMemory: (contentOrEntry) => this.addMemory(contentOrEntry),
                    getMemory: (id) => this.getMemory(id),
                    getMemories: () => this.getMemories(),
                    deleteMemory: (id) => this.deleteMemory(id),
                    clearMemories: () => this.clearMemories(),
                    searchMemories: (options) => this.searchMemories(options),
                    searchSimilarMemories: (query, options) => this.searchSimilarMemories(query, options),
                    // Memory settings and configuration
                    getMemorySettings: () => this.getMemorySettings(),
                    updateMemorySettings: (settings) => this.updateMemorySettings(settings),
                    // Cross-agent memory sharing
                    shareMemoryWithAgent: (memoryId, fromAgentId, toAgentId, accessLevel, expiresIn) => this.shareMemoryWithAgent(memoryId, fromAgentId, toAgentId, accessLevel, expiresIn),
                    getSharedMemoriesForAgent: (agentId) => this.getSharedMemoriesForAgent(agentId),
                    revokeSharedMemory: (memoryId, fromAgentId, toAgentId) => this.revokeSharedMemory(memoryId, fromAgentId, toAgentId),
                    searchSharedMemories: (agentId, query, limit) => this.searchSharedMemories(agentId, query, limit),
                    // Access logging and stats
                    getMemoryAccessLog: (agentId, limit) => this.getMemoryAccessLog(agentId, limit),
                    getMemoryAccessStats: () => this.getMemoryAccessStats(),
                    // Advanced operations
                    processMessage: (message) => this.processMessage(message),
                    chunkFile: (filePath) => this.chunkFile(filePath),
                    chunkWorkspace: (folderPath, includePatterns, excludePatterns) => this.chunkWorkspace(folderPath, includePatterns, excludePatterns),
                    // Event handling
                    onMemoriesChanged: this.onMemoriesChanged
                };
                this.quantumMemorySystem = new quantumMemorySystem_1.QuantumMemorySystem(memoryManagerAdapter, this.vectorMemoryManager ?? await getVectorMemoryManager(), supervisorAgent);
                await this.quantumMemorySystem.initialize();
                logger_1.Logger.instance.info('Quantum Memory System initialized successfully');
            }
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to initialize Quantum Memory System:', error);
        }
    }
    /**
       * Get Quantum Memory System instance
       */
    getQuantumMemorySystem() {
        return this.quantumMemorySystem;
    }
    /**
       * Store temporal memory with versioning
       */
    async storeTemporalMemory(content, context, // Typed context
    changeType = 'create', metadata = {} // Use MemoryMetadata type from types.ts
    ) {
        if (this.quantumMemorySystem) {
            return await this.quantumMemorySystem.storeTemporalMemory(content, context, changeType, metadata);
        }
        else {
            logger_1.Logger.instance.warn('Quantum Memory System not initialized, falling back to regular memory');
            // Fallback to regular memory
            const memoryEntry = await this.addMemory({
                content,
                metadata: {
                    source: 'temporal', // Use imported type
                    type: 'semantic', // Use imported type
                    tags: ['temporal', changeType, ...(metadata.tags ?? [])],
                    ...metadata
                }
            });
            return memoryEntry.id;
        }
    }
    /**
       * Retrieve contextual memories based on query
       */
    async retrieveContextualMemories(query) {
        if (this.quantumMemorySystem) {
            // Convert string query to ContextualMemoryQuery
            const contextualQuery = {
                query: query,
                context: {}, // Typed context
                maxResults: 10
            };
            return await this.quantumMemorySystem.retrieveContextualMemories(contextualQuery);
        }
        else {
            logger_1.Logger.instance.warn('Quantum Memory System not initialized, falling back to regular search');
            // Fallback to regular search
            return await this.searchMemories({ query: query, limit: 10 });
        }
    }
    /**
       * Generate predictive insights from memory
       */
    async generatePredictiveInsights(context) {
        if (this.quantumMemorySystem) {
            return await this.quantumMemorySystem.generatePredictiveInsights(context);
        }
        else {
            logger_1.Logger.instance.warn('Quantum Memory System not initialized, cannot generate predictive insights');
            return [];
        }
    }
    /**
     * Check if the memory manager is initialized
     */
    isInitialized() {
        return this.initialized;
    }
    /**
       * Get memory usage analytics and statistics
       */
    getMemoryAnalytics() {
        if (this.quantumMemorySystem) {
            return this.quantumMemorySystem.getAnalytics();
        }
        else {
            logger_1.Logger.instance.warn('Quantum Memory System not initialized, returning basic analytics');
            return {
                totalMemories: 0,
                temporalMemories: 0,
                collaborativeShares: 0,
                predictiveInsights: 0,
                memoryGrowthRate: 0,
                averageRetrievalTime: 0,
                mostAccessedMemories: [],
                memoryPatterns: {
                    commonTags: [],
                    frequentContexts: [],
                    peakUsageTimes: []
                },
                userBehaviorInsights: {
                    preferredMemoryTypes: [],
                    searchPatterns: [],
                    collaborationFrequency: 0
                }
            };
        }
    }
    /**
     * Shares a memory with another agent, granting specified access permissions.
     *
     * @param {string} memoryId - The ID of the memory to share
     * @param {string} fromAgentId - The ID of the agent sharing the memory
     * @param {string} toAgentId - The ID of the agent to share with
     * @param {'read' | 'write' | 'admin'} [accessLevel='read'] - The level of access to grant
     * @param {number} [expiresIn] - Optional expiration time in milliseconds
     * @returns {Promise<boolean>} True if sharing was successful, false otherwise
     *
     * @example
     * ```typescript
     * // Share a memory with read access
     * await memoryManager.shareMemoryWithAgent(
     *   'memory-123',
     *   'agent-1',
     *   'agent-2',
     *   'read'
     * );
     *
     * // Share with write access that expires in 1 hour
     * await memoryManager.shareMemoryWithAgent(
     *   'memory-123',
     *   'agent-1',
     *   'agent-2',
     *   'write',
     *   60 * 60 * 1000 // 1 hour in milliseconds
     * );
     * ```
     */
    async shareMemoryWithAgent(memoryId, fromAgentId, toAgentId, accessLevel = 'read', expiresIn) {
        try {
            // Get the memory to share
            const memory = await this.getMemory(memoryId);
            if (!memory) {
                this.logMemoryAccess(fromAgentId, memoryId, 'share', false);
                return false;
            }
            // Create shared memory item
            const sharedMemory = {
                id: `shared_${memoryId}_${(0, uuid_1.v4)()}`,
                memoryId: memoryId,
                sharedBy: fromAgentId,
                sharedWith: [toAgentId],
                permissions: accessLevel,
                sharedAt: Date.now(),
                expiresAt: expiresIn ? Date.now() + expiresIn : undefined,
                context: `Shared from ${fromAgentId} to ${toAgentId}`,
                memory: memory
            };
            // Ensure cross-agent memory exists for target agent
            if (!this.crossAgentMemories.has(toAgentId)) {
                this.crossAgentMemories.set(toAgentId, {
                    agentId: toAgentId,
                    sharedMemories: new Map(),
                    accessLog: []
                });
            }
            // Add shared memory to target agent
            const targetAgentMemory = this.crossAgentMemories.get(toAgentId);
            if (!targetAgentMemory) {
                this.logMemoryAccess(fromAgentId, memoryId, 'share', false, 'Failed to get or create agent memory');
                return false;
            }
            targetAgentMemory.sharedMemories.set(memoryId, sharedMemory);
            this.logMemoryAccess(fromAgentId, memoryId, 'share', true);
            logger_1.Logger.instance.info(`Memory ${memoryId} shared from ${fromAgentId} to ${toAgentId} with ${accessLevel} access`);
            return true;
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to share memory ${memoryId}:`, error);
            this.logMemoryAccess(fromAgentId, memoryId, 'share', false);
            return false;
        }
    }
    /**
     * Retrieves all memories that have been shared with a specific agent.
     *
     * @param {string} agentId - The ID of the agent
     * @returns {SharedMemoryItem[]} An array of shared memory items
     *
     * @example
     * ```typescript
     * const sharedMemories = memoryManager.getSharedMemoriesForAgent('agent-2');
     * console.log(`Agent has ${sharedMemories.length} shared memories`);
     * ```
     */
    getSharedMemoriesForAgent(agentId) {
        const agentMemory = this.crossAgentMemories.get(agentId);
        if (!agentMemory) {
            return [];
        }
        // Filter out expired memories
        const now = Date.now();
        const validMemories = [];
        for (const [memoryId, sharedMemory] of agentMemory.sharedMemories) {
            if (!sharedMemory.expiresAt || sharedMemory.expiresAt > now) {
                validMemories.push(sharedMemory);
            }
            else {
                // Remove expired memory
                agentMemory.sharedMemories.delete(memoryId);
            }
        }
        return validMemories;
    }
    /**
       * Revoke shared memory access between agents
       */
    async revokeSharedMemory(memoryId, fromAgentId, toAgentId) {
        try {
            const agentMemory = this.crossAgentMemories.get(toAgentId);
            if (!agentMemory) {
                return false;
            }
            const removed = agentMemory.sharedMemories.delete(memoryId);
            if (removed) {
                this.logMemoryAccess(fromAgentId, memoryId, 'revoke', true);
                logger_1.Logger.instance.info(`Revoked shared memory ${memoryId} from agent ${toAgentId}`);
            }
            // Add a minimal async operation to justify the async
            await Promise.resolve();
            return removed;
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to revoke shared memory ${memoryId}:`, error);
            this.logMemoryAccess(fromAgentId, memoryId, 'revoke', false);
            return false;
        }
    }
    /**
     * Searches through memories shared with a specific agent.
     *
     * @param {string} agentId - The ID of the agent to search shared memories for
     * @param {string} query - The search query
     * @param {number} [limit=10] - Maximum number of results to return
     * @returns {Promise<SharedMemoryItem[]>} An array of matching shared memory items
     *
     * @example
     * ```typescript
     * // Search through shared memories
     * const results = await memoryManager.searchSharedMemories(
     *   'agent-2',
     *   'important project',
     *   5 // Limit to 5 results
     * );
     * ```
     */
    async searchSharedMemories(agentId, query, limit = 10) {
        // Cleanup expired caches periodically
        if (Math.random() < 0.1) { // 10% chance to cleanup on each search
            this.cleanupExpiredCaches();
        }
        // Add minimal async operation to justify async
        await Promise.resolve();
        // Check cache first
        const cacheKey = `search_${agentId}_${query}_${limit}`;
        const cached = this.getSearchCache(cacheKey);
        if (cached) {
            return cached;
        }
        const sharedMemories = this.getSharedMemoriesForAgent(agentId);
        if (sharedMemories.length === 0) {
            return [];
        }
        // Optimized text-based search with early termination
        const queryLower = query.toLowerCase();
        const queryWords = queryLower.split(/\s+/).filter(w => w.length > 2);
        const matches = [];
        for (const memory of sharedMemories) {
            const score = this.calculateOptimizedRelevanceScore(memory, queryWords, queryLower);
            if (score > 0) {
                matches.push({ memory, score });
                // Early termination for performance if we have enough high-scoring matches
                if (matches.length >= limit * 3 && score < 5) {
                    break;
                }
            }
        }
        // Sort by relevance score (descending)
        matches.sort((a, b) => b.score - a.score);
        const results = matches.slice(0, limit).map(m => m.memory);
        // Cache the results
        this.setSearchCache(cacheKey, results);
        return results;
    }
    /**
     * Retrieves the access log for a specific agent, showing all memory operations.
     *
     * @param {string} agentId - The ID of the agent
     * @param {number} [limit=100] - Maximum number of log entries to return
     * @returns {MemoryAccessLog[]} An array of access log entries
     *
     * @example
     * ```typescript
     * // Get the 50 most recent access log entries
     * const accessLog = memoryManager.getMemoryAccessLog('agent-1', 50);
     * accessLog.forEach(entry => {
     *   console.log(`${entry.action} ${entry.memoryId} - ${entry.success ? 'success' : 'failed'}`);
     * });
     * ```
     */
    getMemoryAccessLog(agentId, limit = 100) {
        return this.globalAccessLog
            .filter(log => log.agentId === agentId)
            .slice(-limit)
            .reverse();
    }
    /**
       * Get global memory access statistics and metrics
       */
    getMemoryAccessStats() {
        const agentStats = new Map();
        let totalAccesses = 0;
        let successfulAccesses = 0;
        let failedAccesses = 0;
        for (const log of this.globalAccessLog) {
            totalAccesses++;
            if (log.success) {
                successfulAccesses++;
            }
            else {
                failedAccesses++;
            }
            if (!agentStats.has(log.agentId)) {
                agentStats.set(log.agentId, { reads: 0, writes: 0, shares: 0, revokes: 0 });
            }
            const stats = agentStats.get(log.agentId);
            if (stats) {
                switch (log.action) {
                    case 'read':
                        stats.reads++;
                        break;
                    case 'write':
                        stats.writes++;
                        break;
                    case 'share':
                        stats.shares++;
                        break;
                    case 'revoke':
                        stats.revokes++;
                        break;
                }
            }
        }
        return {
            totalAccesses,
            successfulAccesses,
            failedAccesses,
            agentStats
        };
    }
    /**
       * Log memory access for security and auditing
       */
    logMemoryAccess(agentId, memoryId, action, success, error) {
        const logEntry = {
            id: `log_${(0, uuid_1.v4)()}`,
            agentId,
            memoryId,
            action,
            timestamp: Date.now(),
            success,
            error
        };
        this.globalAccessLog.push(logEntry);
        // Maintain log size limit
        if (this.globalAccessLog.length > this.maxAccessLogSize) {
            this.globalAccessLog = this.globalAccessLog.slice(-this.maxAccessLogSize);
        }
        // Also add to agent-specific log
        const agentMemory = this.crossAgentMemories.get(agentId);
        if (agentMemory) {
            agentMemory.accessLog.push(logEntry);
            if (agentMemory.accessLog.length > 100) {
                agentMemory.accessLog = agentMemory.accessLog.slice(-100);
            }
        }
    }
    /**
       * Calculate relevance score for shared memory search results
       */
    calculateOptimizedRelevanceScore(memory, queryWords, queryLower) {
        let score = 0;
        const contentLower = memory.memory.content.toLowerCase();
        const contextLower = memory.context.toLowerCase();
        // Word-based matching in memory content (more accurate than regex)
        for (const word of queryWords) {
            if (contentLower.includes(word)) {
                score += 2;
                // Bonus for exact word boundaries
                if (contentLower.match(new RegExp(`\\b${word}\\b`))) {
                    score += 1;
                }
            }
            // Also search in context
            if (contextLower.includes(word)) {
                score += 1;
            }
        }
        // Tag matches (high priority) - use memory.metadata.tags
        const tags = memory.memory.metadata.tags ?? [];
        for (const tag of tags) {
            const tagLower = tag.toLowerCase();
            for (const word of queryWords) {
                if (tagLower.includes(word)) {
                    score += 5; // Tags are more important
                }
            }
        }
        // Exact phrase match bonus
        if (contentLower.includes(queryLower)) {
            score += 10;
        }
        // Recency bonus (optimized calculation) - use sharedAt timestamp
        const ageInHours = (Date.now() - memory.sharedAt) / (1000 * 60 * 60);
        if (ageInHours < 24) {
            score += 5; // Recent memories are more relevant
        }
        else if (ageInHours < 168) { // 1 week
            score += 2;
        }
        // Permission level bonus (higher access = more important)
        if (memory.permissions === 'admin') {
            score += 3;
        }
        else if (memory.permissions === 'write') {
            score += 1;
        }
        return score;
    }
    /**
       * Performance optimization: Search cache management
       */
    getSearchCache(key) {
        const cached = this.memorySearchCache.get(key);
        if (!cached)
            return null;
        // Check if cache entry has expired
        if (Date.now() - cached.timestamp > this.searchCacheExpiry) {
            this.memorySearchCache.delete(key);
            return null;
        }
        return cached.results;
    }
    setSearchCache(key, results) {
        // Implement LRU cache behavior
        if (this.memorySearchCache.size >= this.maxSearchCacheSize) {
            // Remove oldest entry
            const firstKey = this.memorySearchCache.keys().next().value;
            if (firstKey) {
                this.memorySearchCache.delete(firstKey);
            }
        }
        this.memorySearchCache.set(key, {
            results,
            timestamp: Date.now()
        });
    }
    /**
       * Performance optimization: Cleanup expired caches
       */
    cleanupExpiredCaches() {
        const now = Date.now();
        // Clean search cache
        for (const [key, cached] of this.memorySearchCache.entries()) {
            if (now - cached.timestamp > this.searchCacheExpiry) {
                this.memorySearchCache.delete(key);
            }
        }
    }
}
exports.MemoryManager = MemoryManager;
// Export singleton instance
exports.memoryManager = new MemoryManager();
//# sourceMappingURL=memoryManager.js.map