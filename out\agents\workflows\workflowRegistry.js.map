{"version": 3, "file": "workflowRegistry.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/workflowRegistry.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,yCAAsC;AACtC,+DAAyE;AAUzE;;GAEG;AACH,MAAM,gBAAgB;IACZ,SAAS,GAAiC,IAAI,GAAG,EAAE,CAAC;IACpD,eAAe,GAOlB,IAAI,GAAG,EAAE,CAAC;IAEP,8BAA8B,GAAuC,IAAI,GAAG,EAAE,CAAC;IAC/E,aAAa,GAAY,KAAK,CAAC;IAC/B,qBAAqB,GAAyB,IAAI,CAAC;IACnD,kBAAkB,GAA+B;QACvD,aAAa,EAAE,IAAI;QACnB,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,IAAI;QAClB,QAAQ,EAAE,KAAK;KAChB,CAAC;IAEF,YAAY,UAA+C,EAAE;QAC3D,IAAI,CAAC,kBAAkB,GAAG,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,OAAO,EAAE,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,qBAAqB,GAAG,CAAC,KAAK,IAAI,EAAE;YACvC,IAAI,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAE7E,8CAA8C;gBAC9C,IAAI,CAAC,gCAAgC,EAAE,CAAC;gBAExC,sDAAsD;gBACtD,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAExC,2DAA2D;gBAC3D,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,IAAI,sCAAwB,EAAE,CAAC;oBACtE,IAAI,CAAC;wBACH,sCAAwB,CAAC,iBAAiB,CAAC;4BACzC,aAAa,EAAE,EAAE;4BACjB,qBAAqB,EAAE,EAAE;4BACzB,iBAAiB,EAAE,UAAU;4BAC7B,WAAW,EAAE,EAAE;4BACf,eAAe,EAAE,EAAE;yBACpB,CAAC,CAAC;oBACL,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,IAAI,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;oBACzE,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;YACxF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBAC/D,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAED;;SAEK;IACL,sBAAsB,CAAC,EAAU,EAAE,eAAoB,EAAE;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACxC,IAAI,CAAC,UAAU;YAAE,OAAO,SAAS,CAAC;QAClC,8DAA8D;QAC9D,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,QAAyB;QAC/C,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,mDAAmD;QACnD,MAAM,gBAAgB,GAAG,IAAI,CAAC,wCAAwC,CAAC,QAAQ,CAAC,CAAC;QAEjF,6CAA6C;QAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE;gBAC5C,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,CAAC;gBAClB,0BAA0B,EAAE;oBAC1B,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,CAAC;oBAClB,eAAe,EAAE,CAAC;oBAClB,mBAAmB,EAAE,CAAC;iBACvB;gBACD,qBAAqB,EAAE,EAAE;gBACzB,aAAa,EAAE,EAAE;aAClB,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,sCAAsC,gBAAgB,CAAC,IAAI,KAAK,gBAAgB,CAAC,EAAE,iCAAiC,CAAC,CAAC;QACpI,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,KAAK,CAAC,+BAA+B,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAC1D,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;SAEK;IACL;;OAEG;IACK,wCAAwC,CAAC,QAAyB;QACxE,sDAAsD;QACtD,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,2BAA2B;QAC3B,IAAK,QAAgB,CAAC,UAAU,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAE/E,8CAA8C;QAC9C,MAAM,kBAAkB,GAAG,GAAG,EAAE;YAC9B,IAAI,CAAC;gBACH,IAAI,CAAC,aAAa,IAAI,CAAC,sCAAwB,EAAE,uBAAuB,EAAE,CAAC;oBACzE,OAAO,2CAA2C,CAAC;gBACrD,CAAC;gBAED,wDAAwD;gBACxD,MAAM,QAAQ,GAAG,sCAAwB,CAAC,uBAAuB,EAAE,CACjE,sBAAsB,QAAQ,CAAC,IAAI,EAAE,EACrC,EAAE,wCAAwC,CAAS,CACpD,CAAC;gBAEF,OAAO,QAAQ,EAAE,aAAa,IAAI,uEAAuE,CAAC;YAC5G,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;gBAC3D,OAAO,kCAAkC,CAAC,CAAC,mBAAmB;YAChE,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,QAAQ,GAAQ;YACpB,GAAG,QAAQ;YACX,UAAU,EAAE,IAAI,EAAE,iDAAiD;YACnE,QAAQ,EAAE;gBACR,GAAI,QAAgB,CAAC,QAAQ,IAAI,EAAE;gBACnC,qBAAqB,EAAE;oBACrB,WAAW,EAAE,aAAa;oBAC1B,eAAe,EAAE,aAAa;oBAC9B,eAAe,EAAE,YAAY;oBAC7B,mBAAmB,EAAE,aAAa,EAAE,2BAA2B;oBAC/D,mBAAmB,EAAE,aAAa,CAAE,2BAA2B;iBAChE;gBACD,gBAAgB,EAAE,eAAe;gBACjC,cAAc,EAAE;oBACd,aAAa,IAAI,wBAAwB;oBACzC,aAAa,IAAI,6BAA6B;oBAC9C,YAAY,IAAI,uBAAuB;oBACvC,aAAa,IAAI,mBAAmB;oBACpC,aAAa,IAAI,sBAAsB;iBACxC,CAAC,MAAM,CAAC,OAAO,CAAa;gBAC7B,eAAe,EAAE,kBAAkB,EAAE;aACtC;SACF,CAAC;QAEF,gDAAgD;QAChD,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3C,GAAG,IAAI;YACP,yBAAyB,EAAE;gBACzB,eAAe,EAAE,IAAI;gBACrB,eAAe,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;gBAC9D,cAAc,EAAE,IAAI;gBACpB,qBAAqB,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;aAC7C;SACF,CAAC,CAAC,CAAC;QAEJ,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACL,WAAW,CAAC,EAAU;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IAED;;SAEK;IACL,eAAe;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;SAEK;IACL,iBAAiB,CAAC,GAAW;QAC3B,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAC9C,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAC7B,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,yBAAyB,CAAC,WAAmB;QAC3C,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAC9C,QAAQ,CAAC,WAAW,KAAK,WAAW,CACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH;;;OAGG;IACK,KAAK,CAAC,0BAA0B;QACtC,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAElE,IAAI,CAAC;YACH,6DAA6D;YAC7D,MAAM,cAAc,GAAG;gBACrB,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,oDAAS,aAAa,GAAC,EAAE;gBACpD,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,oDAAS,qBAAqB,GAAC,EAAE;gBACpE,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,oDAAS,wBAAwB,GAAC,EAAE;gBAC1E,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,oDAAS,iBAAiB,GAAC,EAAE;gBAC5D,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,oDAAS,eAAe,GAAC,EAAE;aACzD,CAAC;YAEF,6DAA6D;YAC7D,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,cAAc,EAAE,CAAC;gBAC7D,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC;oBACnC,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBACjD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,iDAAiD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,MAAW,EAAE,aAAqB,SAAS;QAC7E,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC1C,eAAM,CAAC,IAAI,CAAC,qCAAqC,UAAU,EAAE,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QAED,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAClD,IAAI,CAAC;gBACH,IAAI,OAAO,KAAK,KAAK,UAAU,IAAI,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC5D,uDAAuD;oBACvD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACvB,eAAM,CAAC,IAAI,CAAC,IAAI,UAAU,4BAA4B,GAAG,KAAK,CAAC,CAAC;wBAChE,MAAM,QAAQ,GAAG,KAAK,EAAE,CAAC;wBACzB,IAAI,QAAQ,EAAE,EAAE,EAAE,CAAC;4BACjB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;4BAChC,eAAM,CAAC,IAAI,CAAC,IAAI,UAAU,uCAAuC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;wBAClF,CAAC;6BAAM,CAAC;4BACN,eAAM,CAAC,IAAI,CAAC,IAAI,UAAU,2BAA2B,GAAG,cAAc,CAAC,CAAC;wBAC1E,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,IAAI,CAAC,IAAI,UAAU,8CAA8C,GAAG,EAAE,CAAC,CAAC;oBACjF,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,IAAI,UAAU,wCAAwC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;SAEK;IACG,gCAAgC;QACtC,mCAAmC;QACnC,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC;YAC/D,EAAE,EAAE,yBAAyB;YAC7B,IAAI,EAAE,4BAA4B;YAClC,WAAW,EAAE,0EAA0E;YACvF,OAAO,EAAE,OAAO;YAChB,aAAa,EAAE,SAAgB;YAC/B,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,kBAAkB;YAC/B,QAAQ,EAAE;gBACR,qBAAqB,EAAE;oBACrB,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,IAAI;oBACrB,eAAe,EAAE,IAAI;oBACrB,mBAAmB,EAAE,IAAI;oBACzB,mBAAmB,EAAE,IAAI;iBAC1B;aACF;SACF,CAAC,CAAC,CAAC;QAEJ,qCAAqC;QACrC,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC;YACjE,EAAE,EAAE,2BAA2B;YAC/B,IAAI,EAAE,gCAAgC;YACtC,WAAW,EAAE,kEAAkE;YAC/E,OAAO,EAAE,OAAO;YAChB,aAAa,EAAE,UAAiB;YAChC,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,wBAAwB;YACrC,QAAQ,EAAE;gBACR,qBAAqB,EAAE;oBACrB,eAAe,EAAE,IAAI;oBACrB,uBAAuB,EAAE,IAAI;oBAC7B,mBAAmB,EAAE,IAAI;iBAC1B;aACF;SACF,CAAC,CAAC,CAAC;QAEJ,qCAAqC;QACrC,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC;YACjE,EAAE,EAAE,2BAA2B;YAC/B,IAAI,EAAE,gCAAgC;YACtC,WAAW,EAAE,iDAAiD;YAC9D,OAAO,EAAE,OAAO;YAChB,aAAa,EAAE,SAAgB;YAC/B,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,mBAAmB;YAChC,QAAQ,EAAE;gBACR,qBAAqB,EAAE;oBACrB,eAAe,EAAE,IAAI;oBACrB,qBAAqB,EAAE,IAAI;oBAC3B,gBAAgB,EAAE,IAAI;iBACvB;aACF;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAID;;SAEK;IACL,oBAAoB;QAClB,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAC9C,MAAM,QAAQ,GAAI,QAAgB,CAAC,QAAQ,CAAC;YAC5C,OAAO,QAAQ,EAAE,gBAAgB;gBAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACL,yBAAyB;QACvB,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACrC,CAAC;IAED;;SAEK;IACL,kBAAkB,CAAC,UAAkB;QACnC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAED;;SAEK;IACL,qBAAqB,CAAC,UAAkB,EAAE,aAKzC;QACC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,OAAO,CAAC,cAAc,EAAE,CAAC;QACzB,OAAO,CAAC,eAAe,GAAG,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;QACrI,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;QAE1I,sCAAsC;QACtC,aAAa,CAAC,yBAAyB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACxD,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,IAAI,aAAa,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACjD,OAAO,CAAC,qBAAqB,GAAG,CAAC,OAAO,CAAC,qBAAqB,GAAG,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACvG,CAAC;QAED,8DAA8D;QAC9D,MAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QACpH,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC,kBAAkB,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;QAE/F,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;SAEK;IACL,uBAAuB,CAAC,OAIvB;QACC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,OAAO,YAAY;aAChB,MAAM,CAAC,QAAQ,CAAC,EAAE;YACjB,2BAA2B;YAC3B,IAAI,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,KAAK,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC9E,OAAO,KAAK,CAAC;YACf,CAAC;YAED,8BAA8B;YAC9B,IAAI,OAAO,CAAC,yBAAyB,EAAE,MAAM,EAAE,CAAC;gBAC9C,MAAM,QAAQ,GAAI,QAAgB,CAAC,QAAQ,CAAC;gBAC5C,MAAM,gBAAgB,GAAG,QAAQ,EAAE,gBAAgB,IAAI,QAAQ,EAAE,qBAAqB,IAAI,EAAE,CAAC;gBAC7F,MAAM,oBAAoB,GAAG,OAAO,CAAC,yBAAyB,CAAC,IAAI,CACjE,OAAO,CAAC,EAAE,CAAC,gBAAgB,CAAC,OAAO,CAAC,CACrC,CAAC;gBACF,IAAI,CAAC,oBAAoB;oBAAE,OAAO,KAAK,CAAC;YAC1C,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACb,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;YAC/E,MAAM,MAAM,GAAG,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;YAE/E,OAAO,MAAM,GAAG,MAAM,CAAC;QACzB,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,+BAA+B;IACjD,CAAC;CACF;AAED,8BAA8B;AACjB,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC", "sourcesContent": ["import { GraphDefinition } from './graphTypes';\nimport { logger } from '../../logger';\nimport { goddessPersonalityEngine } from '../../personality/goddessMode';\nimport { v4 as uuidv4 } from 'uuid';\n\ntype WorkflowEnhancementOptions = {\n  enableGoddess?: boolean;\n  enableQuantum?: boolean;\n  enableNeural?: boolean;\n  safeMode?: boolean;\n};\n\n/**\n * Revolutionary Workflow Registry with Enhanced AI Capabilities\n */\nclass WorkflowRegistry {\n  private workflows: Map<string, GraphDefinition> = new Map();\n  private workflowMetrics: Map<string, {\n    executionCount: number;\n    successRate: number;\n    averageDuration: number;\n    revolutionaryFeaturesUsage: Record<string, number>;\n    userSatisfactionScore: number;\n    goddessRating: number;\n  }> = new Map();\n\n  private revolutionaryWorkflowTemplates: Map<string, () => GraphDefinition> = new Map();\n  private isInitialized: boolean = false;\n  private initializationPromise: Promise<void> | null = null;\n  private enhancementOptions: WorkflowEnhancementOptions = {\n    enableGoddess: true,\n    enableQuantum: true,\n    enableNeural: true,\n    safeMode: false\n  };\n\n  constructor(options: Partial<WorkflowEnhancementOptions> = {}) {\n    this.enhancementOptions = { ...this.enhancementOptions, ...options };\n  }\n\n  /**\n   * Initialize the workflow registry with revolutionary features\n   */\n  async initialize(): Promise<void> {\n    if (this.initializationPromise) {\n      return this.initializationPromise;\n    }\n\n    this.initializationPromise = (async () => {\n      try {\n        logger.info('Initializing Workflow Registry with revolutionary features...');\n\n        // Initialize revolutionary workflow templates\n        this.initializeRevolutionaryTemplates();\n\n        // Load core workflows with revolutionary enhancements\n        await this.loadRevolutionaryWorkflows();\n\n        // Initialize Goddess Mode for workflow guidance if enabled\n        if (this.enhancementOptions.enableGoddess && goddessPersonalityEngine) {\n          try {\n            goddessPersonalityEngine.updatePersonality({\n              adaptiveLevel: 95,\n              emotionalIntelligence: 90,\n              motivationalStyle: 'adaptive',\n              wisdomLevel: 95,\n              creativityLevel: 85\n            });\n          } catch (error) {\n            logger.warn('Failed to initialize Goddess personality engine:', error);\n          }\n        }\n\n        this.isInitialized = true;\n        logger.info('Workflow Registry initialized successfully with revolutionary features');\n      } catch (error) {\n        logger.error('Failed to initialize Workflow Registry:', error);\n        throw error;\n      }\n    })();\n\n    return this.initializationPromise;\n  }\n\n  /**\n     * Instantiate a workflow by ID, with optional initial state/context\n     */\n  createWorkflowInstance(id: string, initialState: any = {}): { definition: GraphDefinition, state: any } | undefined {\n    const definition = this.getWorkflow(id);\n    if (!definition) return undefined;\n    // Return a new instance with the definition and a fresh state\n    return { definition, state: initialState };\n  }\n\n  /**\n   * Register a workflow in the registry\n   * @param workflow The workflow to register\n   * @returns The registered workflow\n   */\n  public registerWorkflow(workflow: GraphDefinition): GraphDefinition {\n    if (!workflow?.id) {\n      throw new Error('Cannot register workflow: Missing required ID');\n    }\n\n    // Enhance workflow with revolutionary capabilities\n    const enhancedWorkflow = this.enhanceWorkflowWithRevolutionaryFeatures(workflow);\n\n    // Only set metrics if this is a new workflow\n    if (!this.workflows.has(enhancedWorkflow.id)) {\n      this.workflowMetrics.set(enhancedWorkflow.id, {\n        executionCount: 0,\n        successRate: 100,\n        averageDuration: 0,\n        revolutionaryFeaturesUsage: {\n          goddessMode: 0,\n          quantumAnalysis: 0,\n          neuralSynthesis: 0,\n          timeTravelDebugging: 0\n        },\n        userSatisfactionScore: 85,\n        goddessRating: 90\n      });\n\n      logger.info(`Registered revolutionary workflow: ${enhancedWorkflow.name} (${enhancedWorkflow.id}) with enhanced AI capabilities`);\n    } else {\n      logger.debug(`Updating existing workflow: ${enhancedWorkflow.id}`);\n    }\n\n    this.workflows.set(enhancedWorkflow.id, enhancedWorkflow);\n    return enhancedWorkflow;\n  }\n\n  /**\n     * Enhance workflow with revolutionary features\n     */\n  /**\n   * Safely enhance a workflow with revolutionary features\n   */\n  private enhanceWorkflowWithRevolutionaryFeatures(workflow: GraphDefinition): GraphDefinition {\n    // Skip enhancement in safe mode or if not initialized\n    if (this.enhancementOptions.safeMode || !this.isInitialized) {\n      return workflow;\n    }\n\n    // Skip if already enhanced\n    if ((workflow as any).__enhanced) {\n      return workflow;\n    }\n\n    const { enableGoddess, enableQuantum, enableNeural } = this.enhancementOptions;\n\n    // Create a safe wrapper for goddess blessings\n    const getGoddessBlessing = () => {\n      try {\n        if (!enableGoddess || !goddessPersonalityEngine?.generateGoddessResponse) {\n          return 'Goddess blessings are currently disabled.';\n        }\n\n        // Use a type assertion to handle the DeveloperMood type\n        const response = goddessPersonalityEngine.generateGoddessResponse?.(\n          `Blessing workflow: ${workflow.name}`,\n          { /* empty object as we don't need mood */ } as any\n        );\n\n        return response?.wisdomSharing || 'May this workflow bring wisdom and efficiency to your coding journey.';\n      } catch (error) {\n        logger.warn('Failed to generate goddess blessing:', error);\n        return 'Blessed with divine inspiration.'; // Fallback message\n      }\n    };\n\n    const enhanced: any = {\n      ...workflow,\n      __enhanced: true, // Mark as enhanced to prevent double enhancement\n      metadata: {\n        ...(workflow as any).metadata || {},\n        revolutionaryFeatures: {\n          goddessMode: enableGoddess,\n          quantumAnalysis: enableQuantum,\n          neuralSynthesis: enableNeural,\n          timeTravelDebugging: enableQuantum, // Tied to quantum features\n          adaptivePersonality: enableGoddess  // Tied to goddess features\n        },\n        enhancementLevel: 'revolutionary',\n        aiCapabilities: [\n          enableGoddess && 'emotional_intelligence',\n          enableQuantum && 'quantum_pattern_recognition',\n          enableNeural && 'neural_code_synthesis',\n          enableQuantum && 'future_prediction',\n          enableGoddess && 'adaptive_personality'\n        ].filter(Boolean) as string[],\n        goddessBlessing: getGoddessBlessing()\n      }\n    };\n\n    // Enhance nodes with revolutionary capabilities\n    enhanced.nodes = workflow.nodes.map(node => ({\n      ...node,\n      revolutionaryEnhancements: {\n        goddessGuidance: true,\n        quantumAnalysis: node.type === 'agent' || node.type === 'tool',\n        neuralInsights: true,\n        timeTravelPredictions: node.type === 'agent'\n      }\n    }));\n\n    return enhanced;\n  }\n\n  /**\n     * Get a workflow by ID\n     */\n  getWorkflow(id: string): GraphDefinition | undefined {\n    return this.workflows.get(id);\n  }\n\n  /**\n     * Get all registered workflows\n     */\n  getAllWorkflows(): GraphDefinition[] {\n    return Array.from(this.workflows.values());\n  }\n\n  /**\n     * Get workflows by tag\n     */\n  getWorkflowsByTag(tag: string): GraphDefinition[] {\n    return this.getAllWorkflows().filter(workflow =>\n      workflow.tags?.includes(tag)\n    );\n  }\n\n  /**\n     * Get workflows by methodology\n     */\n  getWorkflowsByMethodology(methodology: string): GraphDefinition[] {\n    return this.getAllWorkflows().filter(workflow =>\n      workflow.methodology === methodology\n    );\n  }\n\n  /**\n   * Load all built-in revolutionary workflows from their respective modules\n   */\n  /**\n   * Load and register all revolutionary workflows\n   * @returns The number of workflows successfully loaded\n   */\n  private async loadRevolutionaryWorkflows(): Promise<number> {\n    logger.info('Loading and registering revolutionary workflows...');\n\n    try {\n      // Import all workflow creation functions with error handling\n      const modulePromises = [\n        { name: 'templates', import: import('./templates') },\n        { name: 'advancedTemplates', import: import('./advancedTemplates') },\n        { name: 'specializedTemplates', import: import('./specializedTemplates') },\n        { name: 'sdlcWorkflows', import: import('./sdlcWorkflows') },\n        { name: 'prWorkflows', import: import('./prWorkflows') }\n      ];\n\n      // Process each module in sequence for better error isolation\n      for (const { name, import: importPromise } of modulePromises) {\n        try {\n          const module = await importPromise;\n          this.registerWorkflowsFromModule(module, name);\n        } catch (error) {\n          logger.error(`Failed to load workflow module ${name}:`, error);\n        }\n      }\n    } catch (error) {\n      logger.error('Unexpected error loading workflows:', error);\n      throw error;\n    }\n\n    logger.info(`Finished loading workflows. Total registered: ${this.workflows.size}`);\n    return this.workflows.size;\n  }\n\n  /**\n   * Register all workflows from a module\n   */\n  private registerWorkflowsFromModule(module: any, moduleName: string = 'unknown'): void {\n    if (!module || typeof module !== 'object') {\n      logger.warn(`Skipping invalid workflow module: ${moduleName}`);\n      return;\n    }\n\n    for (const [key, value] of Object.entries(module)) {\n      try {\n        if (typeof value === 'function' && key.startsWith('create')) {\n          // Only call factory functions that expect no arguments\n          if (value.length === 0) {\n            logger.info(`[${moduleName}] Creating workflow from ${key}...`);\n            const workflow = value();\n            if (workflow?.id) {\n              this.registerWorkflow(workflow);\n              logger.info(`[${moduleName}] Successfully registered workflow: ${workflow.id}`);\n            } else {\n              logger.warn(`[${moduleName}] Invalid workflow from ${key}: Missing ID`);\n            }\n          } else {\n            logger.info(`[${moduleName}] Skipping parameterized workflow factory: ${key}`);\n          }\n        }\n      } catch (error) {\n        logger.error(`[${moduleName}] Failed to process workflow factory ${key}:`, error);\n      }\n    }\n  }\n\n  /**\n   * Check if the registry is ready\n   */\n  get isReady(): boolean {\n    return this.isInitialized;\n  }\n\n  /**\n     * Initialize revolutionary workflow templates\n     */\n  private initializeRevolutionaryTemplates(): void {\n    // Goddess-guided workflow template\n    this.revolutionaryWorkflowTemplates.set('goddess-guided', () => ({\n      id: 'goddess-guided-workflow',\n      name: 'Goddess-Guided Development',\n      description: 'A workflow enhanced with divine coding wisdom and emotional intelligence',\n      version: '1.0.0',\n      operationMode: 'agentic' as any,\n      nodes: [],\n      edges: [],\n      startNodeId: 'goddess-blessing',\n      metadata: {\n        revolutionaryFeatures: {\n          goddessMode: true,\n          quantumAnalysis: true,\n          neuralSynthesis: true,\n          timeTravelDebugging: true,\n          adaptivePersonality: true\n        }\n      }\n    }));\n\n    // Quantum-enhanced workflow template\n    this.revolutionaryWorkflowTemplates.set('quantum-enhanced', () => ({\n      id: 'quantum-enhanced-workflow',\n      name: 'Quantum Code Analysis Workflow',\n      description: 'Leverages quantum-inspired algorithms for superior code analysis',\n      version: '1.0.0',\n      operationMode: 'research' as any,\n      nodes: [],\n      edges: [],\n      startNodeId: 'quantum-initialization',\n      metadata: {\n        revolutionaryFeatures: {\n          quantumAnalysis: true,\n          parallelUniverseTesting: true,\n          quantumEntanglement: true\n        }\n      }\n    }));\n\n    // Neural synthesis workflow template\n    this.revolutionaryWorkflowTemplates.set('neural-synthesis', () => ({\n      id: 'neural-synthesis-workflow',\n      name: 'Neural Code Synthesis Workflow',\n      description: 'Brain-inspired code generation and optimization',\n      version: '1.0.0',\n      operationMode: 'codegen' as any,\n      nodes: [],\n      edges: [],\n      startNodeId: 'neural-activation',\n      metadata: {\n        revolutionaryFeatures: {\n          neuralSynthesis: true,\n          consciousnessAnalysis: true,\n          synapticLearning: true\n        }\n      }\n    }));\n  }\n\n\n\n  /**\n     * Get workflows with advanced features\n     */\n  getAdvancedWorkflows(): GraphDefinition[] {\n    return this.getAllWorkflows().filter(workflow => {\n      const metadata = (workflow as any).metadata;\n      return metadata?.advancedFeatures &&\n        Object.values(metadata.advancedFeatures).some(enabled => enabled);\n    });\n  }\n\n  /**\n     * Get workflows with revolutionary features (legacy method)\n     */\n  getRevolutionaryWorkflows(): GraphDefinition[] {\n    return this.getAdvancedWorkflows();\n  }\n\n  /**\n     * Get workflow metrics\n     */\n  getWorkflowMetrics(workflowId: string): any {\n    return this.workflowMetrics.get(workflowId);\n  }\n\n  /**\n     * Update workflow metrics after execution\n     */\n  updateWorkflowMetrics(workflowId: string, executionData: {\n    success: boolean;\n    duration: number;\n    revolutionaryFeaturesUsed: string[];\n    userSatisfaction?: number;\n  }): void {\n    const metrics = this.workflowMetrics.get(workflowId);\n    if (!metrics) return;\n\n    metrics.executionCount++;\n    metrics.averageDuration = (metrics.averageDuration * (metrics.executionCount - 1) + executionData.duration) / metrics.executionCount;\n    metrics.successRate = ((metrics.successRate * (metrics.executionCount - 1)) + (executionData.success ? 100 : 0)) / metrics.executionCount;\n\n    // Update revolutionary features usage\n    executionData.revolutionaryFeaturesUsed.forEach(feature => {\n      metrics.revolutionaryFeaturesUsage[feature] = (metrics.revolutionaryFeaturesUsage[feature] || 0) + 1;\n    });\n\n    if (executionData.userSatisfaction !== undefined) {\n      metrics.userSatisfactionScore = (metrics.userSatisfactionScore + executionData.userSatisfaction) / 2;\n    }\n\n    // Update goddess rating based on revolutionary features usage\n    const revolutionaryUsage = Object.values(metrics.revolutionaryFeaturesUsage).reduce((sum, count) => sum + count, 0);\n    metrics.goddessRating = Math.min(100, 80 + (revolutionaryUsage / metrics.executionCount) * 20);\n\n    this.workflowMetrics.set(workflowId, metrics);\n  }\n\n  /**\n     * Get recommended workflows based on context\n     */\n  getRecommendedWorkflows(context: {\n    operationMode?: string;\n    complexity?: 'low' | 'medium' | 'high';\n    advancedFeaturesPreferred?: string[];\n  }): GraphDefinition[] {\n    const allWorkflows = this.getAllWorkflows();\n\n    return allWorkflows\n      .filter(workflow => {\n        // Filter by operation mode\n        if (context.operationMode && workflow.operationMode !== context.operationMode) {\n          return false;\n        }\n\n        // Filter by advanced features\n        if (context.advancedFeaturesPreferred?.length) {\n          const metadata = (workflow as any).metadata;\n          const workflowFeatures = metadata?.advancedFeatures || metadata?.revolutionaryFeatures || {};\n          const hasPreferredFeatures = context.advancedFeaturesPreferred.some(\n            feature => workflowFeatures[feature]\n          );\n          if (!hasPreferredFeatures) return false;\n        }\n\n        return true;\n      })\n      .sort((a, b) => {\n        // Sort by goddess rating and success rate\n        const metricsA = this.workflowMetrics.get(a.id);\n        const metricsB = this.workflowMetrics.get(b.id);\n\n        const scoreA = (metricsA?.goddessRating || 80) + (metricsA?.successRate || 80);\n        const scoreB = (metricsB?.goddessRating || 80) + (metricsB?.successRate || 80);\n\n        return scoreB - scoreA;\n      })\n      .slice(0, 5); // Return top 5 recommendations\n  }\n}\n\n// Export a singleton instance\nexport const workflowRegistry = new WorkflowRegistry();"]}