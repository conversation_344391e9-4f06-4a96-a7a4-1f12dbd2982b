{"version": 3, "file": "browserPreviewTool.js", "sourceRoot": "", "sources": ["../../src/tools/browserPreviewTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,+CAAiC;AACjC,6BAAwB;AAExB,MAAa,kBAAkB;IACpB,EAAE,GAAG,gBAAgB,CAAC;IACtB,IAAI,GAAG,iBAAiB,CAAC;IACzB,WAAW,GAAG,iDAAiD,CAAC;IAChE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;KACrF,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0DAA0D,EAAE;SACjG;QACD,QAAQ,EAAE,CAAC,KAAK,CAAC;KAClB,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,MAAM,GAAG,GAAG,KAAK,CAAC,GAAa,CAAC;QAChC,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACxF,CAAC;QACD,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACrD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,8BAA8B,GAAG,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACrG,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mCAAmC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7H,CAAC;IACH,CAAC;CACF;AA7BD,gDA6BC;AAEY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["import { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../types/agent';\nimport * as vscode from 'vscode';\nimport { z } from 'zod';\n\nexport class BrowserPreviewTool implements ITool {\n  readonly id = 'browserPreview';\n  readonly name = 'Browser Preview';\n  readonly description = 'Opens a browser preview for a local web server.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    url: z.string().describe('URL of the local web server (e.g. http://localhost:3000)')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      url: { type: 'string', description: 'URL of the local web server (e.g. http://localhost:3000)' }\n    },\n    required: ['url']\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    const url = input.url as string;\n    if (!url) {\n      return { success: false, error: '\\'url\\' is required.', toolId: this.id, actionName };\n    }\n    try {\n      await vscode.env.openExternal(vscode.Uri.parse(url));\n      return { success: true, output: `Opened browser preview for ${url}`, toolId: this.id, actionName };\n    } catch (error: any) {\n      return { success: false, error: `Failed to open browser preview: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n\nexport const browserPreviewTool = new BrowserPreviewTool();\n"]}