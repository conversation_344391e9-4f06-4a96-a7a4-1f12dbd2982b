{"_type": "UMLClass", "_id": "AAAAAAGH1ChromaVectorStore=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "ChromaVectorStore", "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAGH1ChromaVectorStoreAttr1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "vectorStore", "visibility": "private", "type": "IChromaPolyfillInstance | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1ChromaVectorStoreAttr2=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "embeddings", "visibility": "private", "type": "Embeddings"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1ChromaVectorStoreAttr3=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "config", "visibility": "private", "type": "ChromaVectorStoreConfig"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1ChromaVectorStoreAttr4=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "isInitialized", "visibility": "private", "type": "boolean", "defaultValue": "false"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1ChromaVectorStoreAttr5=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "isInitializing", "visibility": "private", "type": "boolean", "defaultValue": "false"}], "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1ChromaVectorStoreOp1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "constructor", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp1P1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp1="}, "name": "embeddings", "type": "Embeddings"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1ChromaVectorStoreOp2=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "initialize", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp2P1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp2="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1ChromaVectorStoreOp3=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "assertInitialized", "visibility": "private", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp3P1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp3="}, "type": "void", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1ChromaVectorStoreOp4=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "addVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp4P1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp4="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp4P2=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp4="}, "name": "vector", "type": "number[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp4P3=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp4="}, "name": "metadata", "type": "Record<string, any>"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp4P4=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp4="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1ChromaVectorStoreOp5=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "getVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp5P1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp5="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp5P2=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp5="}, "type": "Promise<number[] | undefined>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1ChromaVectorStoreOp6=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "deleteVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp6P1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp6="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp6P2=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp6="}, "type": "Promise<boolean>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1ChromaVectorStoreOp7=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "clearVectors", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp7P1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp7="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1ChromaVectorStoreOp8=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStore="}, "name": "searchSimilarVectors", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp8P1=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp8="}, "name": "vector", "type": "number[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp8P2=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp8="}, "name": "limit", "type": "number", "defaultValue": "5"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp8P3=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp8="}, "name": "filter", "type": "Record<string, any>", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1ChromaVectorStoreOp8P4=", "_parent": {"$ref": "AAAAAAGH1ChromaVectorStoreOp8="}, "type": "Promise<Array<{id: string, score: number}>>", "direction": "return"}]}], "isAbstract": false}