{"compilerOptions": {"module": "commonjs", "target": "ES2022", "outDir": "out", "lib": ["ES2021", "DOM"], "sourceMap": true, "inlineSources": true, "sourceRoot": "", "rootDir": "src", "baseUrl": "./src", "strict": true, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUnusedParameters": false, "esModuleInterop": true, "typeRoots": ["./src/@types", "./node_modules/@types"], "paths": {"workflows/corePolyfill": ["workflows/corePolyfill"], "codessa": ["workflows/codessa"], "src/managers": ["managers/index"], "../lib/tree-sitter": ["./node_modules/web-tree-sitter"]}, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "noEmitOnError": false, "ignoreDeprecations": "5.0", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "downlevelIteration": true, "noImplicitAny": false, "types": ["node", "vscode", "tool-framework"], "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src", "src/test/**/*.ts", "media/chatView.js"], "exclude": ["node_modules", ".vscode-test"]}