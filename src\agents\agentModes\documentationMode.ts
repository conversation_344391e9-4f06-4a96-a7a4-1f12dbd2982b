import * as vscode from 'vscode';
import { OperationMode, ContextSource, ContextType } from './operationMode';
import { Agent } from '../agentUtilities/agent';
import { LLMGenerateParams } from '../../llm/types';
import { Logger } from '../../logger';
import { contextManager } from './contextManager';

/**
 * Documentation Mode - Generate comprehensive documentation
 */
export class DocumentationMode extends OperationMode {
  readonly id = 'documentation';
  readonly displayName = 'Documentation';
  readonly description = 'Generate comprehensive documentation for code';
  readonly icon = '$(book)';
  readonly defaultContextType = ContextType.SELECTED_FILES;
  readonly requiresHumanVerification = false;
  readonly supportsMultipleAgents = false;

  /**
     * Process a user message in Documentation mode
     */
  async processMessage(
    message: string,
    agent: Agent,
    contextSource: ContextSource,
    additionalParams?: Record<string, any>
  ): Promise<string> {
    try {
      Logger.instance.info(`Processing message in Documentation mode: ${message}`);

      // Get context content
      const contextContent = await contextManager.getContextContent(contextSource);

      // Add memory context if available
      let memoryContext = '';
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          const relevantMemories = await agentMemory.getRelevantMemories(message);
          if (relevantMemories && relevantMemories.length > 0) {
            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to documentation context`);
          }
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to retrieve memory context for documentation:', memoryError);
        // Continue without memory context
      }

      // Prepare the documentation prompt
      const prompt = `
You are a technical documentation specialist. Generate comprehensive documentation for the following code.

Documentation Request: ${message}

Code to Document:
${contextContent}

${memoryContext}

Please provide:
1. Overview and purpose
2. API documentation with parameters and return values
3. Usage examples
4. Implementation details
5. Best practices and considerations
6. Related components or dependencies

Format the documentation in clear, professional markdown with proper headings and code examples.
`;

      // Generate response using the agent
      const response = await agent.generate(prompt, this.getLLMParams());

      // Store the documentation session in memory
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          await agentMemory.addMessage('user', `Documentation request: ${message}`);
          await agentMemory.addMessage('assistant', response);
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to store documentation session in memory:', memoryError);
        // Continue without storing in memory
      }

      return response;
    } catch (error) {
      Logger.instance.error('Error processing message in Documentation mode:', error);
      return `Error processing your documentation request: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
     * Get LLM parameters specific to Documentation mode
     */
  getLLMParams(): LLMGenerateParams {
    return {
      prompt: '',
      modelId: '',
      temperature: 0.2, // Low temperature for consistent documentation
      maxTokens: 2500,  // High token limit for comprehensive docs
      stopSequences: [],
      mode: 'generate'
    };
  }
}
