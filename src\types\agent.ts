import { LLMConfig } from '../llm/types';

export type AgentRole = 'receiver' | 'supervisor' | 'assistant' | 'specialist';

export interface AgentConfig {
    id: string;
    name: string;
    description?: string;
    systemPromptName: string;
    llm?: LLMConfig;
    tools?: string[];
    chainedAgentIds?: string[];
    role: AgentRole;
    capabilities: string[];
    llmProvider: string;
    llmModel: string;
    temperature?: number;
    maxTokens?: number;
    isSupervisor?: boolean;
}

import { IWorkflowManager, IMCPManager, IPromptManager, IKnowledgebaseManager } from '../managers';

export interface AgentContext {
    workspace?: {
        currentFile?: string;
        selection?: { text: string; range: { start: number; end: number } };
        workspaceFolders?: string[];
    };
    debugData?: {
        bugOrigin?: unknown;
        analysisTimestamp?: number;
        lastError?: {
            message: string;
            timestamp: number;
            details?: string;
        };
    };
    variables?: {
        workflowManager?: IWorkflowManager;
        mcpManager?: IMCPManager;
        promptManager?: IPromptManager;
        knowledgebaseManager?: IKnowledgebaseManager;
        [key: string]: unknown;
    };
    cancellationToken?: { isCancellationRequested: boolean };
    tools?: Map<string, unknown>;
    filePath?: string;
    streamingContext?: {
        streamId: string;
        parentStreamId?: string;
        onStream?: (event: AgentStreamEvent) => void;
        cancellationToken?: { isCancellationRequested: boolean };
    };
}

export interface AgentStreamEvent {
    type: 'start' | 'progress' | 'data' | 'tool_call' | 'delegation' | 'complete' | 'error';
    agentId: string;
    timestamp: number;
    data?: any;
    content?: string;
    metadata?: Record<string, unknown>;
    error?: Error;
}

export interface AgentRunInput {
    prompt: string;
    mode: string;
    context?: AgentContext;
}

export interface AgentRunResult {
    success: boolean;
    output?: string;
    error?: string;
    toolResults?: ToolResult[];
}

export interface ToolResult {
    /** Indicates if the tool executed successfully. */
    success: boolean;
    /** The output payload of the tool execution on success. Can be any serializable data. */
    output?: unknown;
    /** An error message or object if the tool execution failed. */
    error?: string | Error;
    /** Optional usage metrics (e.g., tokens consumed, cost). */
    usage?: { [key: string]: number };
    /** The ID of the tool that was executed. */
    toolId: string;
    /** The name of the specific action executed if the tool has multiple actions. */
    actionName?: string;
    /** Additional metadata about the execution or result. */
    metadata?: Record<string, unknown>;
    /** The arguments passed to the tool. */
    args: Record<string, unknown>;
    /** The result of the tool execution. */
    result?: unknown;
}
