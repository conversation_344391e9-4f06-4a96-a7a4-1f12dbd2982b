"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.terminalSettings = exports.TerminalSettingsSection = exports.TerminalViewType = exports.TerminalTheme = exports.STANDARD_FONTS = exports.STANDARD_COLORS = void 0;
const vscode = __importStar(require("vscode"));
exports.STANDARD_COLORS = [
    { label: 'Default (Theme)', value: '' },
    { label: 'Black', value: '#000000' },
    { label: 'White', value: '#FFFFFF' },
    { label: 'Red', value: '#FF0000' },
    { label: 'Green', value: '#00FF00' },
    { label: 'Blue', value: '#0000FF' },
    { label: 'Yellow', value: '#FFFF00' },
    { label: 'Cyan', value: '#00FFFF' },
    { label: 'Magenta', value: '#FF00FF' },
    { label: 'Gray', value: '#808080' },
    { label: 'Dark Red', value: '#800000' },
    { label: 'Dark Green', value: '#008000' },
    { label: 'Dark Blue', value: '#000080' },
    { label: 'Dark Yellow', value: '#808000' },
    { label: 'Dark Cyan', value: '#008080' },
    { label: 'Dark Magenta', value: '#800080' },
    { label: 'Light Gray', value: '#C0C0C0' },
    { label: 'Dark Gray', value: '#404040' },
    { label: 'Custom...', value: 'custom' },
];
exports.STANDARD_FONTS = [
    'Consolas',
    'Courier New',
    'Droid Sans Mono',
    'Fira Code',
    'Inconsolata',
    'JetBrains Mono',
    'Menlo',
    'Monaco',
    'Roboto Mono',
    'Source Code Pro',
    'Ubuntu Mono',
    'Cascadia Code',
    'Hack',
    'Iosevka',
    'Fira Mono',
    'DejaVu Sans Mono',
    'Liberation Mono',
    'PT Mono',
    'Space Mono',
    'Anonymous Pro',
];
var TerminalTheme;
(function (TerminalTheme) {
    TerminalTheme["System"] = "system";
    TerminalTheme["Light"] = "light";
    TerminalTheme["Dark"] = "dark";
    TerminalTheme["HighContrast"] = "highContrast";
})(TerminalTheme || (exports.TerminalTheme = TerminalTheme = {}));
var TerminalViewType;
(function (TerminalViewType) {
    TerminalViewType["SideView"] = "sideView";
    TerminalViewType["DefaultTerminal"] = "defaultTerminal";
    TerminalViewType["Both"] = "both";
})(TerminalViewType || (exports.TerminalViewType = TerminalViewType = {}));
class TerminalSettingsSection {
    sectionName = 'codessa.terminal';
    _onDidChangeSettings = new vscode.EventEmitter();
    onDidChangeSettings = this._onDidChangeSettings.event;
    defaultSettings = {
        // Display settings
        viewType: TerminalViewType.Both,
        showTerminal: true,
        // Font settings
        fontFamily: '"Courier New", Consolas, "Droid Sans Mono", "Fira Code", "Inconsolata", "Source Code Pro", "Ubuntu Mono", monospace',
        fontSize: 14,
        fontWeight: 'normal',
        // Color settings
        theme: TerminalTheme.System,
        foregroundColor: '',
        backgroundColor: '',
        cursorColor: '',
        selectionColor: '',
        customTheme: {},
    };
    disposables = [];
    constructor() {
        // Listen for configuration changes
        this.disposables.push(vscode.workspace.onDidChangeConfiguration((e) => {
            if (e.affectsConfiguration(this.sectionName)) {
                this._onDidChangeSettings.fire();
            }
        }));
    }
    dispose() {
        this._onDidChangeSettings.dispose();
        this.disposables.forEach((d) => d.dispose());
    }
    getSettings() {
        const config = vscode.workspace.getConfiguration(this.sectionName);
        const settings = this.defaultSettings;
        // Get all settings with their default values
        const result = {
            viewType: config.get('viewType', settings.viewType),
            showTerminal: config.get('showTerminal', settings.showTerminal),
            fontFamily: config.get('fontFamily', settings.fontFamily),
            fontSize: config.get('fontSize', settings.fontSize),
            fontWeight: config.get('fontWeight', settings.fontWeight),
            theme: config.get('theme', settings.theme),
            foregroundColor: config.get('foregroundColor', settings.foregroundColor),
            backgroundColor: config.get('backgroundColor', settings.backgroundColor),
            cursorColor: config.get('cursorColor', settings.cursorColor),
            selectionColor: config.get('selectionColor', settings.selectionColor),
            customTheme: config.get('customTheme', settings.customTheme) ||
                {},
        };
        return result;
    }
    getSetting(key) {
        const config = vscode.workspace.getConfiguration(this.sectionName);
        return config.get(key, this.defaultSettings[key]);
    }
    async updateSettings(settings) {
        const config = vscode.workspace.getConfiguration(this.sectionName);
        // Update each setting individually
        for (const [key, value] of Object.entries(settings)) {
            if (value !== undefined) {
                await config.update(key, value, vscode.ConfigurationTarget.Global);
            }
        }
        this._onDidChangeSettings.fire();
    }
    async updateSetting(key, value, target = vscode.ConfigurationTarget.Global) {
        const config = vscode.workspace.getConfiguration(this.sectionName);
        await config.update(key, value, target);
        this._onDidChangeSettings.fire();
    }
    async resetToDefault(key) {
        const config = vscode.workspace.getConfiguration(this.sectionName);
        await config.update(key, undefined, true);
        this._onDidChangeSettings.fire();
    }
    async resetAllToDefault() {
        const config = vscode.workspace.getConfiguration(this.sectionName);
        for (const key of Object.keys(this.defaultSettings)) {
            await config.update(key, undefined, true);
        }
        this._onDidChangeSettings.fire();
    }
    async toggleViewType() {
        const current = this.getSettings();
        let nextViewType;
        switch (current.viewType) {
            case TerminalViewType.SideView:
                nextViewType = TerminalViewType.DefaultTerminal;
                break;
            case TerminalViewType.DefaultTerminal:
                nextViewType = TerminalViewType.Both;
                break;
            case TerminalViewType.Both:
            default:
                nextViewType = TerminalViewType.SideView;
        }
        await this.updateSettings({ viewType: nextViewType });
    }
    async showColorPicker(currentColor) {
        const color = await vscode.window.showInputBox({
            value: currentColor || '#000000',
            prompt: 'Enter a color in hex format (e.g., #FF0000 for red)',
            validateInput: (value) => {
                if (!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
                    return 'Please enter a valid hex color (e.g., #FF0000 or #F00)';
                }
                return null;
            },
        });
        return color || undefined;
    }
    async showFontPicker(currentFont) {
        const quickPick = vscode.window.createQuickPick();
        quickPick.placeholder = 'Select a font or type to search';
        quickPick.items = exports.STANDARD_FONTS.map((font) => ({
            label: font,
            description: font === currentFont ? 'Current font' : '',
            alwaysShow: font === currentFont,
        }));
        quickPick.onDidChangeValue((value) => {
            if (value && !exports.STANDARD_FONTS.includes(value)) {
                quickPick.items = [
                    { label: value, description: 'Custom font' },
                    ...exports.STANDARD_FONTS.map((font) => ({
                        label: font,
                        description: font === currentFont ? 'Current font' : '',
                    })),
                ];
            }
            else {
                quickPick.items = exports.STANDARD_FONTS.map((font) => ({
                    label: font,
                    description: font === currentFont ? 'Current font' : '',
                }));
            }
        });
        const selected = await new Promise((resolve) => {
            quickPick.onDidAccept(() => {
                resolve(quickPick.selectedItems[0]);
                quickPick.hide();
            });
            quickPick.onDidHide(() => resolve(undefined));
            quickPick.show();
        });
        quickPick.dispose();
        return selected?.label;
    }
    getCurrentThemeColors() {
        const colorTheme = vscode.window.activeColorTheme;
        const themeKind = colorTheme.kind === 1
            ? 'light'
            : colorTheme.kind === 2
                ? 'dark'
                : 'highContrast';
        // Return theme color identifiers for use in webview CSS
        return {
            foreground: 'var(--vscode-terminal-foreground)',
            background: 'var(--vscode-terminal-background)',
            cursor: 'var(--vscode-terminalCursor-foreground)',
            selection: 'var(--vscode-terminal-selectionBackground)',
            theme: themeKind,
        };
    }
    async showSettings() {
        const settings = this.getSettings();
        const themeColors = this.getCurrentThemeColors();
        const categoryItems = [
            {
                label: '$(symbol-color) Appearance',
                description: 'Font, colors, and theme settings',
            },
            { label: '$(gear) Advanced', description: 'Advanced terminal settings' },
            {
                label: '$(discard) Reset All',
                description: 'Reset all settings to default',
            },
        ];
        const selectedCategory = await vscode.window.showQuickPick(categoryItems, {
            placeHolder: 'Select a settings category',
        });
        if (!selectedCategory)
            return;
        if (selectedCategory.label.includes('Appearance')) {
            await this.showAppearanceSettings(settings, themeColors);
        }
        else if (selectedCategory.label.includes('Advanced')) {
            await this.showAdvancedSettings(settings);
        }
        else if (selectedCategory.label.includes('Reset')) {
            await this.confirmResetAll();
        }
    }
    async showAppearanceSettings(settings, themeColors) {
        const items = [
            {
                label: 'Font Family',
                description: settings.fontFamily,
                detail: 'Change the terminal font family',
                alwaysShow: true,
            },
            {
                label: 'Font Size',
                description: settings.fontSize.toString(),
                detail: 'Change the terminal font size',
                alwaysShow: true,
            },
            {
                label: 'Font Weight',
                description: settings.fontWeight,
                detail: 'Change the terminal font weight',
                alwaysShow: true,
            },
            {
                label: 'Text Color',
                description: settings.foregroundColor || 'Theme Default',
                detail: `Change the terminal text color (Default: ${themeColors.foreground})`,
                alwaysShow: true,
            },
            {
                label: 'Background Color',
                description: settings.backgroundColor || 'Theme Default',
                detail: `Change the terminal background color (Default: ${themeColors.background})`,
                alwaysShow: true,
            },
            {
                label: 'Cursor Color',
                description: settings.cursorColor || 'Theme Default',
                detail: `Change the terminal cursor color (Default: ${themeColors.cursor})`,
                alwaysShow: true,
            },
            {
                label: 'Color Theme',
                description: settings.theme.charAt(0).toUpperCase() + settings.theme.slice(1),
                detail: 'Change the terminal color theme',
                alwaysShow: true,
            },
        ];
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select a setting to change',
            matchOnDetail: true,
        });
        if (!selected)
            return;
        switch (selected.label) {
            case 'Font Family':
                await this.handleFontFamilyChange(settings);
                break;
            case 'Font Size':
                await this.handleFontSizeChange(settings);
                break;
            case 'Font Weight':
                await this.handleFontWeightChange(settings);
                break;
            case 'Text Color':
                await this.handleColorChange('foregroundColor');
                break;
            case 'Background Color':
                await this.handleColorChange('backgroundColor');
                break;
            case 'Cursor Color':
                await this.handleColorChange('cursorColor');
                break;
            case 'Color Theme':
                await this.handleThemeChange(settings);
                break;
        }
        // Show the settings again after making a change
        await this.showAppearanceSettings(this.getSettings(), themeColors);
    }
    async showAdvancedSettings(settings) {
        const items = [
            {
                label: 'View Type',
                description: settings.viewType === 'sideView'
                    ? 'Side View'
                    : settings.viewType === 'defaultTerminal'
                        ? 'Default Terminal'
                        : 'Both',
                detail: 'Change where the terminal output is displayed',
                alwaysShow: true,
            },
            {
                label: 'Show Terminal',
                description: settings.showTerminal ? 'Yes' : 'No',
                detail: 'Toggle terminal visibility',
                alwaysShow: true,
            },
            {
                label: 'Reset to Defaults',
                description: 'Reset all settings to their default values',
                detail: 'This will reset all terminal settings',
                alwaysShow: true,
            },
        ];
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select an advanced setting to change',
            matchOnDetail: true,
        });
        if (!selected)
            return;
        switch (selected.label) {
            case 'View Type':
                await this.toggleViewType();
                break;
            case 'Show Terminal':
                await this.updateSetting('showTerminal', !settings.showTerminal);
                break;
            case 'Reset to Defaults':
                await this.confirmResetAll();
                break;
        }
        // Show the settings again after making a change
        await this.showAdvancedSettings(this.getSettings());
    }
    async confirmResetAll() {
        const confirm = await vscode.window.showWarningMessage('Are you sure you want to reset all terminal settings to their default values?', { modal: true }, 'Reset All', 'Cancel');
        if (confirm === 'Reset All') {
            await this.resetAllToDefault();
            vscode.window.showInformationMessage('Terminal settings have been reset to default.');
        }
    }
    async handleFontFamilyChange(settings) {
        const newFont = await this.showFontPicker(settings.fontFamily);
        if (newFont !== undefined) {
            await this.updateSetting('fontFamily', newFont);
        }
    }
    async handleFontSizeChange(settings) {
        const newSize = await vscode.window.showInputBox({
            value: settings.fontSize.toString(),
            prompt: 'Enter font size (8-32)',
            validateInput: (value) => {
                const size = parseInt(value, 10);
                if (isNaN(size) || size < 8 || size > 32) {
                    return 'Please enter a number between 8 and 32';
                }
                return null;
            },
        });
        if (newSize) {
            await this.updateSetting('fontSize', parseInt(newSize, 10));
        }
    }
    async handleFontWeightChange(settings) {
        const weights = [
            'normal',
            'bold',
            '100',
            '200',
            '300',
            '400',
            '500',
            '600',
            '700',
            '800',
            '900',
        ];
        const selected = await vscode.window.showQuickPick(weights.map((weight) => ({
            label: weight,
            description: weight === settings.fontWeight ? 'Current' : '',
        })), { placeHolder: 'Select a font weight' });
        if (selected) {
            await this.updateSetting('fontWeight', selected.label);
        }
    }
    async handleColorChange(colorKey) {
        const currentColor = this.getSettings()[colorKey];
        const colorItems = exports.STANDARD_COLORS.map((color) => ({
            label: color.label,
            description: color.value === currentColor ? 'Current' : '',
            detail: color.value ? `Color: ${color.value}` : 'Use theme default',
            value: color.value,
        }));
        const selected = await vscode.window.showQuickPick(colorItems, {
            placeHolder: `Select a ${colorKey
                .replace(/([A-Z])/g, ' $1')
                .toLowerCase()
                .trim()}`,
            matchOnDetail: true,
        });
        if (!selected)
            return;
        if (selected.value === 'custom') {
            const customColor = await this.showColorPicker(currentColor);
            if (customColor) {
                await this.updateSetting(colorKey, customColor);
            }
        }
        else if (selected.value === '') {
            await this.updateSetting(colorKey, '');
        }
        else if (selected.value) {
            await this.updateSetting(colorKey, selected.value);
        }
    }
    async handleThemeChange(settings) {
        const themes = [
            { label: 'System', value: TerminalTheme.System },
            { label: 'Light', value: TerminalTheme.Light },
            { label: 'Dark', value: TerminalTheme.Dark },
            { label: 'High Contrast', value: TerminalTheme.HighContrast },
        ];
        const selected = await vscode.window.showQuickPick(themes.map((theme) => ({
            label: theme.label,
            description: theme.value === settings.theme ? 'Current' : '',
            value: theme.value,
        })), { placeHolder: 'Select a color theme' });
        if (selected) {
            await this.updateSetting('theme', selected.value);
        }
    }
}
exports.TerminalSettingsSection = TerminalSettingsSection;
// Export a singleton instance
exports.terminalSettings = new TerminalSettingsSection();
//# sourceMappingURL=terminalSettingsSection.js.map