import * as vscode from 'vscode';
import { Logger } from '../logger';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import { diffEngine } from '../diff/diffEngine';
import { TextDecoder, TextEncoder } from 'util';
import { z } from 'zod';
import * as path from 'path';
import * as fs from 'fs';
import * as crypto from 'crypto';
import * as os from 'os';

const decoder = new TextDecoder('utf-8');
const encoder = new TextEncoder();

/**
 * Enhanced file system tool that combines basic file operations with advanced capabilities
 */
export class EnhancedFileSystemTool implements ITool {
  readonly id = 'file';
  readonly name = 'Enhanced File System Operations';
  readonly description = 'Comprehensive file system operations with advanced capabilities including reading, writing, diffing, patching, and file management with automatic backups and integrity checks.';
  readonly type = 'multi-action';

  // Backup management
  private static backups = new Map<string, { content: string, timestamp: number, hash: string }>();
  private static readonly BACKUP_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours

  readonly actions: Record<string, any> = {
    'readFile': {
      description: 'Reads the content of a specified file.',
      schema: z.object({
        filePath: z.string().describe('Path to the file (relative to workspace root or absolute).')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file (relative to workspace root or absolute).')
      })
    },
    'writeFile': {
      description: 'Writes content to a specified file, overwriting existing content. Creates the file if it does not exist.',
      schema: z.object({
        filePath: z.string().describe('Path to the file (relative to workspace root or absolute).'),
        content: z.string().describe('The content to write to the file.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before writing. Default is true.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file (relative to workspace root or absolute).'),
        content: z.string().describe('The content to write to the file.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before writing. Default is true.')
      })
    },
    'createDiff': {
      description: 'Creates a unified diff patch between the content of a file and new provided content.',
      schema: z.object({
        filePath: z.string().describe('Path to the original file (relative or absolute).'),
        newContent: z.string().describe('The proposed new content for the file.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the original file (relative or absolute).'),
        newContent: z.string().describe('The proposed new content for the file.')
      })
    },
    'applyDiff': {
      description: 'Applies a unified diff patch to a specified file. IMPORTANT: The file content should match the state the patch was created against.',
      schema: z.object({
        filePath: z.string().describe('Path to the file to patch (relative or absolute).'),
        patch: z.string().describe('The unified diff patch string.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before patching. Default is true.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to patch (relative or absolute).'),
        patch: z.string().describe('The unified diff patch string.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before patching. Default is true.')
      })
    },
    'createFile': {
      description: 'Creates a new file at the specified path.',
      schema: z.object({
        filePath: z.string().describe('Path to the new file (relative or absolute).'),
        content: z.string().optional().describe('Initial content for the file. Default is empty.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the new file (relative or absolute).'),
        content: z.string().optional().describe('Initial content for the file. Default is empty.')
      })
    },
    'deleteFile': {
      description: 'Deletes the specified file.',
      schema: z.object({
        filePath: z.string().describe('Path to the file to delete (relative or absolute).'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before deleting. Default is true.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to delete (relative or absolute).'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before deleting. Default is true.')
      })
    },
    'renameFile': {
      description: 'Renames a file from oldPath to newPath.',
      schema: z.object({
        oldPath: z.string().describe('Current file path.'),
        newPath: z.string().describe('New file path.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before renaming. Default is true.')
      }),
      inputSchema: z.object({
        oldPath: z.string().describe('Current file path.'),
        newPath: z.string().describe('New file path.'),
        createBackup: z.boolean().optional().describe('Whether to create a backup before renaming. Default is true.')
      })
    },
    'copyFile': {
      description: 'Copies a file from sourcePath to destPath.',
      schema: z.object({
        sourcePath: z.string().describe('Source file path.'),
        destPath: z.string().describe('Destination file path.')
      }),
      inputSchema: z.object({
        sourcePath: z.string().describe('Source file path.'),
        destPath: z.string().describe('Destination file path.')
      })
    },
    'createDir': {
      description: 'Creates a new directory at the specified path.',
      schema: z.object({
        dirPath: z.string().describe('Path to the new directory (relative or absolute).')
      }),
      inputSchema: z.object({
        dirPath: z.string().describe('Path to the new directory (relative or absolute).')
      })
    },
    'deleteDir': {
      description: 'Deletes the specified directory and its contents.',
      schema: z.object({
        dirPath: z.string().describe('Path to the directory to delete (relative or absolute).')
      }),
      inputSchema: z.object({
        dirPath: z.string().describe('Path to the directory to delete (relative or absolute).')
      })
    },
    'restoreBackup': {
      description: 'Restores a file from backup if available.',
      schema: z.object({
        filePath: z.string().describe('Path to the file to restore.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to restore.')
      })
    },
    'listBackups': {
      description: 'Lists all available file backups.',
      schema: z.object({}),
      inputSchema: z.object({})
    }
  };

  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    // For backward compatibility, check if actionName is undefined and try to get it from input
    const actionId = actionName || input.action as string;

    if (!actionId) {
      return {
        success: false,
        error: `Action parameter is required. Available actions: ${Object.keys(this.actions).join(', ')}`,
        toolId: this.id,
        actionName
      };
    }

    const actionInfo = this.actions[actionId];

    if (!actionInfo) {
      return {
        success: false,
        error: `Unknown file system action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`,
        toolId: this.id,
        actionName
      };
    }

    // Pass the rest of the input to the specific action handler
    const actionInput = { ...input };
    if ('action' in actionInput) {
      delete actionInput.action;
    }

    try {
      switch (actionId) {
        case 'readFile':
          return await this.executeReadFile(actionInput, actionId);
        case 'writeFile':
          return await this.executeWriteFile(actionInput, actionId);
        case 'createDiff':
          return await this.executeCreateDiff(actionInput, actionId);
        case 'applyDiff':
          return await this.executeApplyDiff(actionInput, actionId);
        case 'createFile':
          return await this.executeCreateFile(actionInput, actionId);
        case 'deleteFile':
          return await this.executeDeleteFile(actionInput, actionId);
        case 'renameFile':
          return await this.executeRenameFile(actionInput, actionId);
        case 'copyFile':
          return await this.executeCopyFile(actionInput, actionId);
        case 'createDir':
          return await this.executeCreateDir(actionInput, actionId);
        case 'deleteDir':
          return await this.executeDeleteDir(actionInput, actionId);
        case 'restoreBackup':
          return await this.executeRestoreBackup(actionInput, actionId);
        case 'listBackups':
          return await this.executeListBackups(actionInput, actionId);
        default:
          return {
            success: false,
            error: `Unknown action: ${actionId}`,
            toolId: this.id,
            actionName: actionId
          };
      }
    } catch (error: any) {
      Logger.instance.error(`Error executing file action ${actionId}:`, error);
      return {
        success: false,
        error: `Failed to execute ${actionId}: ${error.message || error}`,
        toolId: this.id,
        actionName: actionId
      };
    }
  }

  /**
     * Create a backup of a file before editing
     */
  private async createBackup(filePath: string): Promise<string> {
    try {
      // Clean up old backups
      this.cleanupOldBackups();

      // Read the file content
      const fileUri = this.resolveWorkspacePath(filePath);
      if (!fileUri) {
        throw new Error(`Could not resolve file path: ${filePath}`);
      }

      let content = '';
      try {
        const contentUint8 = await vscode.workspace.fs.readFile(fileUri);
        content = decoder.decode(contentUint8);
      } catch (error: any) {
        if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
          // If file doesn't exist, backup is empty
          content = '';
        } else {
          throw error;
        }
      }

      // Generate a hash of the content
      const hash = crypto.createHash('sha256').update(content).digest('hex');

      // Store the backup
      EnhancedFileSystemTool.backups.set(filePath, {
        content,
        timestamp: Date.now(),
        hash
      });

      Logger.instance.info(`Created backup for ${filePath}`);
      return hash;
    } catch (error) {
      Logger.instance.error(`Failed to create backup of ${filePath}:`, error);
      throw new Error(`Failed to create backup of ${filePath}: ${error}`);
    }
  }

  /**
     * Restore a file from backup
     */
  private async restoreFromBackup(filePath: string): Promise<boolean> {
    try {
      const backup = EnhancedFileSystemTool.backups.get(filePath);
      if (!backup) {
        return false;
      }

      const fileUri = this.resolveWorkspacePath(filePath);
      if (!fileUri) {
        throw new Error(`Could not resolve file path: ${filePath}`);
      }

      // Write the backup content back to the file
      const contentUint8 = encoder.encode(backup.content);
      await vscode.workspace.fs.writeFile(fileUri, contentUint8);

      Logger.instance.info(`Restored ${filePath} from backup`);
      return true;
    } catch (error) {
      Logger.instance.error(`Failed to restore ${filePath} from backup:`, error);
      throw new Error(`Failed to restore ${filePath} from backup: ${error}`);
    }
  }

  /**
     * Clean up old backups
     */
  private cleanupOldBackups(): void {
    const now = Date.now();
    // Convert entries() iterator to array before iterating
    const entries = Array.from(EnhancedFileSystemTool.backups.entries());
    for (const [filePath, backup] of entries) {
      if (now - backup.timestamp > EnhancedFileSystemTool.BACKUP_EXPIRY_MS) {
        EnhancedFileSystemTool.backups.delete(filePath);
        Logger.instance.debug(`Cleaned up old backup for ${filePath}`);
      }
    }
  }

  /**
     * Resolve a workspace path to a vscode.Uri
     */
  private resolveWorkspacePath(filePath: string): vscode.Uri | undefined {
    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
      const workspaceRoot = vscode.workspace.workspaceFolders[0].uri;

      // Check if it's already absolute
      try {
        const uri = vscode.Uri.parse(filePath);
        if (uri.scheme) return uri; // Already absolute
      } catch (e) {
        // Ignore parsing errors, treat as relative
      }

      // If relative, join with workspace root
      return vscode.Uri.joinPath(workspaceRoot, filePath);
    } else if (vscode.Uri.parse(filePath).scheme) {
      // Absolute path outside workspace
      return vscode.Uri.parse(filePath);
    }

    // Relative path but no workspace open
    Logger.instance.warn(`Cannot resolve relative path "${filePath}" without an open workspace folder.`);
    return undefined;
  }

  /**
     * Execute read file action
     */
  private async executeReadFile(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;

    if (!filePath) {
      return { success: false, error: '\'filePath\' is required.', toolId: this.id, actionName };
    }

    const fileUri = this.resolveWorkspacePath(filePath);
    if (!fileUri) {
      return {
        success: false,
        error: `Could not resolve file path: ${filePath}. Make sure it's relative to an open workspace or absolute.`,
        toolId: this.id,
        actionName
      };
    }

    try {
      Logger.instance.debug(`Reading file: ${fileUri.fsPath}`);
      const fileContentUint8 = await vscode.workspace.fs.readFile(fileUri);
      const fileContent = decoder.decode(fileContentUint8);
      Logger.instance.info(`Successfully read ${fileContent.length} characters from ${fileUri.fsPath}`);
      return { success: true, output: fileContent, toolId: this.id, actionName };
    } catch (error: any) {
      Logger.instance.error(`Error reading file ${fileUri.fsPath}:`, error);

      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
        return { success: false, error: `File not found: ${filePath}`, toolId: this.id, actionName };
      }

      return { success: false, error: `Failed to read file: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute write file action
     */
  private async executeWriteFile(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;
    const content = input.content as string;
    const createBackup = input.createBackup !== false; // Default to true

    if (!filePath || content === undefined) {
      return { success: false, error: '\'filePath\' and \'content\' are required.', toolId: this.id, actionName };
    }

    if (typeof content !== 'string') {
      return { success: false, error: '\'content\' must be a string.', toolId: this.id, actionName };
    }

    const fileUri = this.resolveWorkspacePath(filePath);
    if (!fileUri) {
      return {
        success: false,
        error: `Could not resolve file path: ${filePath}. Make sure it's relative to an open workspace or absolute.`,
        toolId: this.id,
        actionName
      };
    }

    try {
      // Create backup if requested
      if (createBackup) {
        try {
          await this.createBackup(filePath);
        } catch (error) {
          Logger.instance.warn(`Failed to create backup for ${filePath}, continuing with write operation:`, error);
        }
      }

      Logger.instance.debug(`Writing to file: ${fileUri.fsPath}`);
      const contentUint8 = encoder.encode(content);
      await vscode.workspace.fs.writeFile(fileUri, contentUint8);
      Logger.instance.info(`Successfully wrote ${content.length} characters to ${fileUri.fsPath}`);
      return {
        success: true,
        output: `File ${filePath} written successfully.`,
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          size: content.length,
          backupCreated: createBackup
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error writing file ${fileUri.fsPath}:`, error);
      return { success: false, error: `Failed to write file: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute create diff action
     */
  private async executeCreateDiff(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;
    const newContent = input.newContent as string;

    if (!filePath || newContent === undefined) {
      return { success: false, error: '\'filePath\' and \'newContent\' are required.', toolId: this.id, actionName };
    }

    const fileUri = this.resolveWorkspacePath(filePath);
    if (!fileUri) {
      return { success: false, error: `Could not resolve file path: ${filePath}.`, toolId: this.id, actionName };
    }

    try {
      // Read the original file content
      let originalContent = '';
      try {
        const originalContentUint8 = await vscode.workspace.fs.readFile(fileUri);
        originalContent = decoder.decode(originalContentUint8);
      } catch (error: any) {
        if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
          // If file doesn't exist, treat original content as empty for diff creation
          Logger.instance.debug(`File ${filePath} not found, creating diff against empty content.`);
          originalContent = '';
        } else {
          throw error; // Re-throw other read errors
        }
      }

      const patch = diffEngine.createPatch(filePath, filePath, originalContent, newContent);
      Logger.instance.info(`Successfully created diff patch for ${filePath}`);
      return {
        success: true,
        output: patch,
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          originalSize: originalContent.length,
          newSize: newContent.length
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error creating diff for ${filePath}:`, error);
      return { success: false, error: `Failed to create diff: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute apply diff action
     */
  private async executeApplyDiff(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;
    const patch = input.patch as string;
    const createBackup = input.createBackup !== false; // Default to true

    if (!filePath || !patch) {
      return { success: false, error: '\'filePath\' and \'patch\' are required.', toolId: this.id, actionName };
    }

    const fileUri = this.resolveWorkspacePath(filePath);
    if (!fileUri) {
      return { success: false, error: `Could not resolve file path: ${filePath}.`, toolId: this.id, actionName };
    }

    try {
      // Create backup if requested
      if (createBackup) {
        try {
          await this.createBackup(filePath);
        } catch (error) {
          Logger.instance.warn(`Failed to create backup for ${filePath}, continuing with patch operation:`, error);
        }
      }

      // 1. Read the current content of the file
      let currentContent = '';
      try {
        const currentContentUint8 = await vscode.workspace.fs.readFile(fileUri);
        currentContent = decoder.decode(currentContentUint8);
      } catch (error: any) {
        if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
          // If the file doesn't exist, maybe the patch is creating it?
          Logger.instance.warn(`File ${filePath} not found. Attempting to apply patch to empty content.`);
          currentContent = '';
        } else {
          throw error; // Re-throw other read errors
        }
      }

      // 2. Apply the patch
      const patchedContent = diffEngine.applyPatch(patch, currentContent);

      if (patchedContent === false) {
        // Patch failed to apply
        return {
          success: false,
          error: `Patch could not be applied cleanly to ${filePath}. The file content may have changed, or the patch is invalid/malformed.`,
          toolId: this.id,
          actionName
        };
      }

      // 3. Write the patched content back to the file
      Logger.instance.debug(`Writing patched content back to: ${fileUri.fsPath}`);
      const patchedContentUint8 = encoder.encode(patchedContent);
      await vscode.workspace.fs.writeFile(fileUri, patchedContentUint8);

      Logger.instance.info(`Successfully applied patch to ${filePath}`);
      return {
        success: true,
        output: `Patch applied successfully to ${filePath}.`,
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          backupCreated: createBackup
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error applying patch to ${filePath}:`, error);

      // Try to restore from backup if patching failed
      if (createBackup) {
        try {
          await this.restoreFromBackup(filePath);
          Logger.instance.info(`Restored ${filePath} from backup after failed patch operation`);
        } catch (restoreError) {
          Logger.instance.error(`Failed to restore ${filePath} from backup:`, restoreError);
        }
      }

      return { success: false, error: `Failed to apply patch: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute create file action
     */
  private async executeCreateFile(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;
    const content = (input.content as string) || '';

    if (!filePath) {
      return { success: false, error: '\'filePath\' is required.', toolId: this.id, actionName };
    }

    const fileUri = this.resolveWorkspacePath(filePath);
    if (!fileUri) {
      return {
        success: false,
        error: `Could not resolve file path: ${filePath}. Make sure it's relative to an open workspace or absolute.`,
        toolId: this.id,
        actionName
      };
    }

    try {
      // Check if file already exists
      try {
        await vscode.workspace.fs.stat(fileUri);
        return {
          success: false,
          error: `File already exists: ${filePath}. Use 'writeFile' action to overwrite.`,
          toolId: this.id,
          actionName
        };
      } catch (error: any) {
        // File doesn't exist, which is what we want
        if (!(error instanceof vscode.FileSystemError && error.code === 'FileNotFound')) {
          throw error; // Re-throw unexpected errors
        }
      }

      // Create the file
      Logger.instance.debug(`Creating file: ${fileUri.fsPath}`);
      const contentUint8 = encoder.encode(content);
      await vscode.workspace.fs.writeFile(fileUri, contentUint8);

      Logger.instance.info(`Successfully created file ${filePath}`);
      return {
        success: true,
        output: `File created: ${filePath}`,
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          size: content.length
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error creating file ${fileUri.fsPath}:`, error);
      return { success: false, error: `Failed to create file: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute delete file action
     */
  private async executeDeleteFile(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;
    const createBackup = input.createBackup !== false; // Default to true

    if (!filePath) {
      return { success: false, error: '\'filePath\' is required.', toolId: this.id, actionName };
    }

    const fileUri = this.resolveWorkspacePath(filePath);
    if (!fileUri) {
      return {
        success: false,
        error: `Could not resolve file path: ${filePath}. Make sure it's relative to an open workspace or absolute.`,
        toolId: this.id,
        actionName
      };
    }

    try {
      // Create backup if requested
      if (createBackup) {
        try {
          await this.createBackup(filePath);
        } catch (error) {
          Logger.instance.warn(`Failed to create backup for ${filePath}, continuing with delete operation:`, error);
        }
      }

      // Delete the file
      Logger.instance.debug(`Deleting file: ${fileUri.fsPath}`);
      await vscode.workspace.fs.delete(fileUri);

      Logger.instance.info(`Successfully deleted file ${filePath}`);
      return {
        success: true,
        output: `File deleted: ${filePath}`,
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          backupCreated: createBackup
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error deleting file ${fileUri.fsPath}:`, error);

      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
        return { success: false, error: `File not found: ${filePath}`, toolId: this.id, actionName };
      }

      return { success: false, error: `Failed to delete file: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute rename file action
     */
  private async executeRenameFile(input: ToolInput, actionName: string): Promise<ToolResult> {
    const oldPath = input.oldPath as string;
    const newPath = input.newPath as string;
    const createBackup = input.createBackup !== false; // Default to true

    if (!oldPath || !newPath) {
      return { success: false, error: '\'oldPath\' and \'newPath\' are required.', toolId: this.id, actionName };
    }

    const oldUri = this.resolveWorkspacePath(oldPath);
    const newUri = this.resolveWorkspacePath(newPath);

    if (!oldUri || !newUri) {
      return {
        success: false,
        error: 'Could not resolve file paths. Make sure they\'re relative to an open workspace or absolute.',
        toolId: this.id,
        actionName
      };
    }

    try {
      // Create backup if requested
      if (createBackup) {
        try {
          await this.createBackup(oldPath);
        } catch (error) {
          Logger.instance.warn(`Failed to create backup for ${oldPath}, continuing with rename operation:`, error);
        }
      }

      // Rename the file
      Logger.instance.debug(`Renaming file: ${oldUri.fsPath} -> ${newUri.fsPath}`);
      await vscode.workspace.fs.rename(oldUri, newUri);

      Logger.instance.info(`Successfully renamed file ${oldPath} -> ${newPath}`);
      return {
        success: true,
        output: `Renamed file: ${oldPath} -> ${newPath}`,
        toolId: this.id,
        actionName,
        metadata: {
          oldPath,
          newPath,
          backupCreated: createBackup
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error renaming file ${oldUri.fsPath} -> ${newUri.fsPath}:`, error);

      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
        return { success: false, error: `Source file not found: ${oldPath}`, toolId: this.id, actionName };
      }

      return { success: false, error: `Failed to rename file: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute copy file action
     */
  private async executeCopyFile(input: ToolInput, actionName: string): Promise<ToolResult> {
    const sourcePath = input.sourcePath as string;
    const destPath = input.destPath as string;

    if (!sourcePath || !destPath) {
      return { success: false, error: '\'sourcePath\' and \'destPath\' are required.', toolId: this.id, actionName };
    }

    const sourceUri = this.resolveWorkspacePath(sourcePath);
    const destUri = this.resolveWorkspacePath(destPath);

    if (!sourceUri || !destUri) {
      return {
        success: false,
        error: 'Could not resolve file paths. Make sure they\'re relative to an open workspace or absolute.',
        toolId: this.id,
        actionName
      };
    }

    try {
      // Copy the file
      Logger.instance.debug(`Copying file: ${sourceUri.fsPath} -> ${destUri.fsPath}`);
      await vscode.workspace.fs.copy(sourceUri, destUri);

      Logger.instance.info(`Successfully copied file ${sourcePath} -> ${destPath}`);
      return {
        success: true,
        output: `Copied file: ${sourcePath} -> ${destPath}`,
        toolId: this.id,
        actionName,
        metadata: {
          sourcePath,
          destPath
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error copying file ${sourceUri.fsPath} -> ${destUri.fsPath}:`, error);

      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
        return { success: false, error: `Source file not found: ${sourcePath}`, toolId: this.id, actionName };
      }

      return { success: false, error: `Failed to copy file: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute create directory action
     */
  private async executeCreateDir(input: ToolInput, actionName: string): Promise<ToolResult> {
    const dirPath = input.dirPath as string;

    if (!dirPath) {
      return { success: false, error: '\'dirPath\' is required.', toolId: this.id, actionName };
    }

    const dirUri = this.resolveWorkspacePath(dirPath);
    if (!dirUri) {
      return {
        success: false,
        error: `Could not resolve directory path: ${dirPath}. Make sure it's relative to an open workspace or absolute.`,
        toolId: this.id,
        actionName
      };
    }

    try {
      // Create the directory
      Logger.instance.debug(`Creating directory: ${dirUri.fsPath}`);
      await vscode.workspace.fs.createDirectory(dirUri);

      Logger.instance.info(`Successfully created directory ${dirPath}`);
      return {
        success: true,
        output: `Directory created: ${dirPath}`,
        toolId: this.id,
        actionName,
        metadata: {
          dirPath
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error creating directory ${dirUri.fsPath}:`, error);
      return { success: false, error: `Failed to create directory: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute delete directory action
     */
  private async executeDeleteDir(input: ToolInput, actionName: string): Promise<ToolResult> {
    const dirPath = input.dirPath as string;

    if (!dirPath) {
      return { success: false, error: '\'dirPath\' is required.', toolId: this.id, actionName };
    }

    const dirUri = this.resolveWorkspacePath(dirPath);
    if (!dirUri) {
      return {
        success: false,
        error: `Could not resolve directory path: ${dirPath}. Make sure it's relative to an open workspace or absolute.`,
        toolId: this.id,
        actionName
      };
    }

    try {
      // Delete the directory
      Logger.instance.debug(`Deleting directory: ${dirUri.fsPath}`);
      await vscode.workspace.fs.delete(dirUri, { recursive: true });

      Logger.instance.info(`Successfully deleted directory ${dirPath}`);
      return {
        success: true,
        output: `Directory deleted: ${dirPath}`,
        toolId: this.id,
        actionName,
        metadata: {
          dirPath
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error deleting directory ${dirUri.fsPath}:`, error);

      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {
        return { success: false, error: `Directory not found: ${dirPath}`, toolId: this.id, actionName };
      }

      return { success: false, error: `Failed to delete directory: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute restore backup action
     */
  private async executeRestoreBackup(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;

    if (!filePath) {
      return { success: false, error: '\'filePath\' is required.', toolId: this.id, actionName };
    }

    try {
      const restored = await this.restoreFromBackup(filePath);

      if (!restored) {
        return {
          success: false,
          error: `No backup found for ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      return {
        success: true,
        output: `Successfully restored ${filePath} from backup`,
        toolId: this.id,
        actionName,
        metadata: {
          filePath
        }
      };
    } catch (error: any) {
      Logger.instance.error(`Error restoring backup for ${filePath}:`, error);
      return { success: false, error: `Failed to restore backup: ${error.message || error}`, toolId: this.id, actionName };
    }
  }

  /**
     * Execute list backups action
     */
  private async executeListBackups(input: ToolInput, actionName: string): Promise<ToolResult> {
    try {
      const backups = Array.from(EnhancedFileSystemTool.backups.entries()).map(([filePath, backup]) => ({
        filePath,
        timestamp: new Date(backup.timestamp).toISOString(),
        age: Math.round((Date.now() - backup.timestamp) / (60 * 1000)) + ' minutes',
        hash: backup.hash
      }));

      return {
        success: true,
        output: backups,
        toolId: this.id,
        actionName,
        metadata: {
          count: backups.length
        }
      };
    } catch (error: any) {
      Logger.instance.error('Error listing backups:', error);
      return { success: false, error: `Failed to list backups: ${error.message || error}`, toolId: this.id, actionName };
    }
  }
}

// Export a singleton instance of the enhanced file system tool
export const enhancedFileSystemTool = new EnhancedFileSystemTool();
