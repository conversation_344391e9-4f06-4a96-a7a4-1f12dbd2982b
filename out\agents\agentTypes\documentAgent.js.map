{"version": 3, "file": "documentAgent.js", "sourceRoot": "", "sources": ["../../../src/agents/agentTypes/documentAgent.ts"], "names": [], "mappings": ";;;AAAA,mDAA6F;AAE7F,yCAAsC;AAEtC,MAAa,aAAc,SAAQ,aAAK;IACtC,YAAY,OAAY;QACtB,KAAK,CAAC;YACJ,GAAG,OAAO;YACV,IAAI,EAAE,WAAW;YACjB,YAAY,EAAE,CAAC,UAAU,CAAC;YAC1B,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,GAAG,CAAC,KAAoB,EAAE,UAAwB,EAAE;QACjE,eAAM,CAAC,IAAI,CAAC,8BAA8B,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;QAE/E,qDAAqD;QACrD,IAAI,QAAQ,GAAQ,SAAS,CAAC;QAC9B,IAAI,OAAO,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC;YACvC,QAAQ,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QACpF,CAAC;QAED,oCAAoC;QACpC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,GAAG,OAAO,CAAC,KAA2B,CAAC;QAC9C,CAAC;QAED,kDAAkD;QAClD,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;gBACrB,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QACD,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,OAAO,CAAC,SAAS,EAAE,oBAAoB,EAAE,CAAC;YAC5C,SAAS,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC9F,CAAC;QAED,6BAA6B;QAC7B,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;YAClC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,2CAA2C;QAC3C,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,IAAI,OAAO,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;YACrC,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,eAAe,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAC7G,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,GAAG;;;EAGzB,cAAc;;;EAGd,KAAK,CAAC,MAAM;;;EAGZ,aAAa;;;EAGb,SAAS;;;EAGT,UAAU;;;EAGV,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM;;;EAG5C,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,2BAA2B;;;;;;;CAOnL,CAAC;QACE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC;QAC9B,mDAAmD;QACnD,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IACjD,CAAC;CACF;AArFD,sCAqFC", "sourcesContent": ["import { Agent, AgentContext, AgentRunInput, AgentRunResult } from '../agentUtilities/agent';\nimport { ITool } from '../../tools/types';\nimport { logger } from '../../logger';\n\nexport class DocumentAgent extends Agent {\n  constructor(_config: any) {\n    super({\n      ..._config,\n      role: 'assistant',\n      capabilities: ['document'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4'\n    });\n  }\n\n  override async run(input: AgentRunInput, context: AgentContext = {}): Promise<AgentRunResult> {\n    logger.info(`DocumentAgent processing: \"${input.prompt.substring(0, 50)}...\"`);\n\n    // --- Gather relevant workflow for document mode ---\n    let workflow: any = undefined;\n    if (context.variables?.workflowManager) {\n      workflow = await context.variables.workflowManager.getWorkflowForMode('document');\n    }\n\n    // --- Gather all relevant tools ---\n    let tools = this.tools;\n    if (context.tools) {\n      tools = context.tools as Map<string, ITool>;\n    }\n\n    // --- Gather memory and knowledgebase context ---\n    let memoryContext = '';\n    if (this.getMemory) {\n      const memories = await this.getMemory().getRelevantMemories(input.prompt);\n      if (memories?.length) {\n        memoryContext = this.getMemory().formatMemoriesForPrompt(memories);\n      }\n    }\n    let kbContext = '';\n    if (context.variables?.knowledgebaseManager) {\n      kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input.prompt);\n    }\n\n    // --- Gather MCP context ---\n    let mcpContext = '';\n    if (context.variables?.mcpManager) {\n      mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext() || {});\n    }\n\n    // --- Use external prompt if available ---\n    let externalPrompt = '';\n    if (context.variables?.promptManager) {\n      externalPrompt = context.variables.promptManager.getSystemPrompt('documentAgent', context.variables) || '';\n    }\n\n    // --- Compose final prompt ---\n    const documentPrompt = `\n# Document Mode Processing\n\n${externalPrompt}\n\n## User Request:\n${input.prompt}\n\n## Memory Context:\n${memoryContext}\n\n## Knowledgebase Context:\n${kbContext}\n\n## MCP Context:\n${mcpContext}\n\n## Workflow:\n${workflow ? JSON.stringify(workflow) : 'None'}\n\n## Code to Document:\n${context.workspace?.currentFile ? (typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile) : 'No code context available'}\n\n## Your Task:\n1. Create appropriate documentation for the code\n2. Follow best practices for the language/framework\n3. Include examples where helpful\nUse all available workflows, tools, memory, knowledgebase, and context. Stream output if possible.\n`;\n    input.prompt = documentPrompt;\n    // Pass all gathered context and tools to super.run\n    return super.run(input, { ...context, tools });\n  }\n}"]}