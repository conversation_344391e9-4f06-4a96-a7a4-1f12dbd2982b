"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.webReadTool = exports.WebReadTool = void 0;
const axios_1 = __importDefault(require("axios"));
const advancedWebTools_1 = require("./advancedWebTools");
const zod_1 = require("zod");
class WebReadTool {
    id = 'webRead';
    name = 'Web Read (Advanced)';
    description = 'Reads and extracts content, snapshot, and more from web pages.';
    type = 'multi-action'; // Required by ITool
    actions = {
        'read': {
            id: 'read',
            name: 'Read Web Page',
            description: 'Read the content of a web page (HTML/text).',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                url: zod_1.z.string().describe('The URL to read.')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    url: { type: 'string', description: 'The URL to read.' }
                },
                required: ['url']
            },
            async execute(actionName, input, _context) {
                const url = input.url;
                if (!url) {
                    return { success: false, error: '\'url\' is required.', toolId: 'read', actionName };
                }
                try {
                    const response = await (0, axios_1.default)({ method: 'get', url });
                    const content = response.data;
                    return {
                        success: true,
                        output: typeof content === 'string' ? content : JSON.stringify(content),
                        toolId: 'read',
                        actionName
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        error: `Failed to read URL: ${error.message || error}`,
                        toolId: 'read',
                        actionName
                    };
                }
            }
        },
        'extract': {
            ...new advancedWebTools_1.WebContentExtractTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                const url = input.url;
                if (!url) {
                    return { success: false, error: '\'url\' is required.', toolId: 'webExtract', actionName };
                }
                // Placeholder: In real implementation, use a library like mercury-parser or readability
                return { success: true, output: `Extracted content from: ${url}`, toolId: 'webExtract', actionName };
            }
        },
        'snapshot': {
            ...new advancedWebTools_1.WebSnapshotTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                const url = input.url;
                if (!url) {
                    return { success: false, error: '\'url\' is required.', toolId: 'webSnapshot', actionName };
                }
                // Placeholder: Actual screenshot would require headless browser integration
                return { success: true, output: `Snapshot data for: ${url}`, toolId: 'webSnapshot', actionName };
            }
        },
    };
    async execute(actionName, input, context) {
        // If an action is specified, delegate to the appropriate action tool
        if (actionName && this.actions[actionName]) {
            const actionTool = this.actions[actionName];
            // Check if the action tool has the new execute method signature
            if (actionTool.execute.length >= 2) {
                // Pass the actionName parameter to the nested tool
                return actionTool.execute(actionName, input, context);
            }
            else {
                // Fallback for older tools that don't have the new signature
                const result = await actionTool.execute(input, context);
                // Add the actionName to the result if it's not already there
                if (result && !result.actionName) {
                    result.actionName = actionName;
                }
                return result;
            }
        }
        // Default behavior (backward compatibility) - treat as a web read
        const url = input.url;
        if (!url) {
            return { success: false, error: '\'url\' is required.', toolId: this.id, actionName };
        }
        try {
            const response = await (0, axios_1.default)({ method: 'get', url });
            const content = response.data;
            return {
                success: true,
                output: typeof content === 'string' ? content : JSON.stringify(content),
                toolId: this.id,
                actionName
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Failed to read URL: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
}
exports.WebReadTool = WebReadTool;
exports.webReadTool = new WebReadTool();
//# sourceMappingURL=webReadTool.js.map