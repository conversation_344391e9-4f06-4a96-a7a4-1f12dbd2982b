{"version": 3, "file": "providerManager.js", "sourceRoot": "", "sources": ["../../src/llm/providerManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,sCAAmC;AAEnC,0EAAuE;AAGvE;;;;;;;;GAQG;AACH,MAAa,eAAe;IAClB,MAAM,CAAC,QAAQ,CAAkB;IACjC,OAAO,CAA2B;IAClC,SAAS,GAA8B,IAAI,GAAG,EAAE,CAAC;IACxC,aAAa,GAAG,aAAa,CAAC;IAE/C,YAAoB,OAAiC;QACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,WAAW,CAAC,OAAiC;QACzD,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;YAC9E,CAAC;YACD,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;SAEK;IACE,UAAU,CAAC,OAAgC;QAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;IAED;;SAEK;IACE,gBAAgB,CAAC,QAAsB;QAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,eAAM,CAAC,IAAI,CAAC,qBAAqB,QAAQ,CAAC,UAAU,uCAAuC,CAAC,CAAC;QAC/F,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClD,eAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;SAEK;IACE,WAAW,CAAC,UAAkB;QACnC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED;;SAEK;IACE,eAAe;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;SAEK;IACE,iBAAiB;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;aACvC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACE,kBAAkB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC7D,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,wCAAwC;QACxC,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEvD,sGAAsG;QACtG,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;YAC1C,eAAM,CAAC,IAAI,CAAC,qBAAqB,iBAAiB,iEAAiE,CAAC,CAAC;YACrH,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC1D,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACE,oBAAoB;QACzB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrE,OAAO,MAAM,CAAC,GAAG,CAAS,iBAAiB,CAAC,IAAI,QAAQ,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QAClD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAErE,+CAA+C;YAC/C,IAAI,CAAC;gBACH,yCAAyC;gBACzC,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,UAAU,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;oBACtF,eAAM,CAAC,IAAI,CAAC,2BAA2B,UAAU,kBAAkB,CAAC,CAAC;oBACrE,OAAO,IAAI,CAAC;gBACd,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,eAAM,CAAC,IAAI,CAAC,mDAAmD,WAAW,6BAA6B,CAAC,CAAC;gBAC3G,CAAC;gBAED,wCAAwC;gBACxC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtF,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,UAAU,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;oBACzF,eAAM,CAAC,IAAI,CAAC,2BAA2B,UAAU,qBAAqB,CAAC,CAAC;oBACxE,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,uCAAuC;gBACvC,eAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,uCAAuC,CAAC,CAAC;gBACrG,OAAO,KAAK,CAAC;YACf,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,KAAK,CAAC,mCAAmC,WAAW,EAAE,CAAC,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QAC/C,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;YAED,wDAAwD;YACxD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAoC,WAAW,CAAC,IAAI,EAAE,CAAC;YACnF,MAAM,cAAc,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAEnD,6CAA6C;YAC7C,MAAM,MAAM,GAAG,MAAM,uCAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEtG,OAAO;gBACL,GAAG,cAAc;gBACjB,MAAM,EAAE,MAAM,IAAI,cAAc,CAAC,MAAM;aACxC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,oBAAoB,CAAC,UAAkB,EAAE,OAAY;QAChE,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;YAED,kCAAkC;YAClC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,uCAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAChE,UAAU,EACV,QAAQ,EACR,OAAO,CAAC,MAAM,CACf,CAAC;gBAEF,wEAAwE;gBACxE,MAAM,EAAE,MAAM,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO,CAAC;gBAClD,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC7D,OAAO,GAAG,kBAAkB,CAAC;YAC/B,CAAC;YAED,yDAAyD;YACzD,IAAI,CAAC;gBACH,gCAAgC;gBAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAEvE,mCAAmC;gBACnC,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBAElD,uDAAuD;gBACvD,MAAM,gBAAgB,GAAG;oBACvB,GAAG,SAAS;oBACZ,CAAC,UAAU,CAAC,EAAE,OAAO;iBACtB,CAAC;gBAEF,gEAAgE;gBAChE,eAAM,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAC9E,eAAM,CAAC,KAAK,CAAC,oBAAoB,GAAG,UAAU,GAAG,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC7F,eAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;gBAE/E,qCAAqC;gBACrC,IAAI,CAAC;oBACH,uDAAuD;oBACvD,IAAI,CAAC;wBACH,oEAAoE;wBACpE,MAAM,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;wBACxF,eAAM,CAAC,IAAI,CAAC,6CAA6C,UAAU,EAAE,CAAC,CAAC;wBAEvE,4CAA4C;wBAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBAChD,IAAI,QAAQ,EAAE,CAAC;4BACb,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;wBAC9B,CAAC;wBAED,OAAO,IAAI,CAAC;oBACd,CAAC;oBAAC,OAAO,SAAS,EAAE,CAAC;wBACnB,eAAM,CAAC,IAAI,CAAC,0CAA0C,SAAS,6BAA6B,CAAC,CAAC;oBAChG,CAAC;oBAED,wCAAwC;oBACxC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACtF,MAAM,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;wBAC3F,eAAM,CAAC,IAAI,CAAC,gDAAgD,UAAU,EAAE,CAAC,CAAC;wBAE1E,4CAA4C;wBAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBAChD,IAAI,QAAQ,EAAE,CAAC;4BACb,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;wBAC9B,CAAC;wBAED,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,uCAAuC;oBACvC,eAAM,CAAC,KAAK,CAAC,+CAA+C,UAAU,uCAAuC,CAAC,CAAC;oBAC/G,OAAO,KAAK,CAAC;gBACf,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,eAAM,CAAC,KAAK,CAAC,mCAAmC,WAAW,EAAE,CAAC,CAAC;oBAC/D,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,+CAA+C,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;gBAClF,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QAClD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,UAAU,iBAAiB,EAAE,KAAK,CAAC,CAAC;YAChF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;;;SAMK;IACE,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QAC/C,IAAI,CAAC;YACH,8DAA8D;YAC9D,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEhF,kFAAkF;YAClF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC3C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,KAAK,CAAC;YACf,CAAC;YAED,sCAAsC;YACtC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;gBAC7B,OAAO,KAAK,CAAC;YACf,CAAC;YAED,6DAA6D;YAC7D,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAsB,WAAW,CAAC,IAAI,EAAE,CAAC;YACrE,MAAM,cAAc,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAEnD,kEAAkE;YAClE,IAAI,cAAc,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACrC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,UAAU,cAAc,EAAE,KAAK,CAAC,CAAC;YAC7E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,QAAQ,CACnB,UAAkB,EAClB,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,aAAa,UAAU,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,aAAa,UAAU,qBAAqB,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU,CAAC,UAAkB;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,aAAa,UAAU,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,aAAa,UAAU,qBAAqB,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,QAAQ,CAAC,UAAU,EAAE,CAAC;IAC/B,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,OAAe;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,aAAa,UAAU,aAAa;aAC9C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;YAC7B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,aAAa,UAAU,qBAAqB;aACtD,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;CACF;AAjYD,0CAiYC;AAED,4BAA4B;AACf,QAAA,eAAe,GAAG;IAC7B,WAAW,EAAE,eAAe,CAAC,WAAW;CACzC,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { logger } from '../logger';\nimport { ILLMProvider, LLMGenerateParams, LLMGenerateResult, LLMModelInfo, LLMProviderConfig } from './llmProvider';\nimport { credentialsManager } from '../credentials/credentialsManager';\nimport { ITool } from '../tools/tool.ts.backup';\n\n/**\n * Manages all aspects of LLM providers:\n * - Provider registration and initialization\n * - Provider configuration and settings\n * - Credentials management\n * - Model listing and selection\n *\n * This class consolidates functionality that was previously spread across multiple files.\n */\nexport class ProviderManager {\n  private static instance: ProviderManager;\n  private context?: vscode.ExtensionContext;\n  private providers: Map<string, ILLMProvider> = new Map();\n  private readonly configSection = 'codessa.llm';\n\n  private constructor(context?: vscode.ExtensionContext) {\n    this.context = context;\n  }\n\n  /**\n     * Get the singleton instance of ProviderManager\n     */\n  public static getInstance(context?: vscode.ExtensionContext): ProviderManager {\n    if (!ProviderManager.instance) {\n      if (!context) {\n        throw new Error('ProviderManager must be initialized with a context first');\n      }\n      ProviderManager.instance = new ProviderManager(context);\n    }\n    return ProviderManager.instance;\n  }\n\n  /**\n     * Initialize the provider manager with the extension context\n     */\n  public initialize(context: vscode.ExtensionContext): void {\n    this.context = context;\n    logger.info('Provider manager initialized');\n  }\n\n  /**\n     * Register a provider with the manager\n     */\n  public registerProvider(provider: ILLMProvider): void {\n    if (this.providers.has(provider.providerId)) {\n      logger.warn(`Provider with ID '${provider.providerId}' is already registered. Overwriting.`);\n    }\n    this.providers.set(provider.providerId, provider);\n    logger.info(`Registered provider: ${provider.providerId}`);\n  }\n\n  /**\n     * Get a provider by ID\n     */\n  public getProvider(providerId: string): ILLMProvider | undefined {\n    return this.providers.get(providerId);\n  }\n\n  /**\n     * Get all registered providers\n     */\n  public getAllProviders(): ILLMProvider[] {\n    return Array.from(this.providers.values());\n  }\n\n  /**\n     * Get all provider IDs\n     */\n  public getAllProviderIds(): string[] {\n    return Array.from(this.providers.keys());\n  }\n\n  /**\n     * Get all configured providers\n     */\n  public getConfiguredProviders(): ILLMProvider[] {\n    return Array.from(this.providers.values())\n      .filter(provider => provider.isConfigured());\n  }\n\n  /**\n     * Get the default provider based on settings\n     */\n  public getDefaultProvider(): ILLMProvider | undefined {\n    if (!this.context) {\n      logger.error('ProviderManager not initialized with context');\n      return undefined;\n    }\n\n    // Get default provider ID from settings\n    const defaultProviderId = this.getDefaultProviderId();\n    const provider = this.providers.get(defaultProviderId);\n\n    // If the default provider is not available or not configured, try to find another configured provider\n    if (!provider || !provider.isConfigured()) {\n      logger.warn(`Default provider '${defaultProviderId}' not found or not configured. Trying to find another provider.`);\n      const configuredProviders = this.getConfiguredProviders();\n      if (configuredProviders.length > 0) {\n        return configuredProviders[0];\n      }\n      return undefined;\n    }\n\n    return provider;\n  }\n\n  /**\n     * Get the default provider ID from settings\n     */\n  public getDefaultProviderId(): string {\n    try {\n      const config = vscode.workspace.getConfiguration(this.configSection);\n      return config.get<string>('defaultProvider') || 'ollama';\n    } catch (error) {\n      logger.error('Failed to get default provider ID:', error);\n      return 'ollama';\n    }\n  }\n\n  /**\n     * Set the default provider ID\n     */\n  public async setDefaultProviderId(providerId: string): Promise<boolean> {\n    try {\n      const config = vscode.workspace.getConfiguration(this.configSection);\n\n      // Try different configuration targets in order\n      try {\n        // First try updating at the Global level\n        try {\n          await config.update('defaultProvider', providerId, vscode.ConfigurationTarget.Global);\n          logger.info(`Set default provider to ${providerId} at global level`);\n          return true;\n        } catch (globalError) {\n          logger.warn(`Failed to set default provider at global level: ${globalError}. Trying workspace level...`);\n        }\n\n        // Then try Workspace level if available\n        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n          await config.update('defaultProvider', providerId, vscode.ConfigurationTarget.Workspace);\n          logger.info(`Set default provider to ${providerId} at workspace level`);\n          return true;\n        }\n\n        // If we get here, both attempts failed\n        logger.error(`Failed to set default provider to ${providerId}: No valid configuration target found`);\n        return false;\n      } catch (updateError) {\n        logger.error(`Failed to set default provider: ${updateError}`);\n        return false;\n      }\n    } catch (error) {\n      logger.error(`Failed to set default provider to ${providerId}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Get the configuration for a provider\n     */\n  public async getProviderConfig(providerId: string): Promise<LLMProviderConfig> {\n    try {\n      if (!this.context) {\n        throw new Error('ProviderManager not initialized with context');\n      }\n\n      // Get non-sensitive settings from VS Code configuration\n      const config = vscode.workspace.getConfiguration(this.configSection);\n      const providers = config.get<Record<string, LLMProviderConfig>>('providers') || {};\n      const providerConfig = providers[providerId] || {};\n\n      // Get sensitive settings from secure storage\n      const apiKey = await credentialsManager.getInstance(this.context).getCredential(providerId, 'apiKey');\n\n      return {\n        ...providerConfig,\n        apiKey: apiKey || providerConfig.apiKey\n      };\n    } catch (error) {\n      logger.error(`Failed to get provider config for ${providerId}:`, error);\n      return {};\n    }\n  }\n\n  /**\n     * Update the configuration for a provider\n     */\n  public async updateProviderConfig(providerId: string, _config: any): Promise<boolean> {\n    try {\n      if (!this.context) {\n        throw new Error('ProviderManager not initialized with context');\n      }\n\n      // Store API key in secure storage\n      if (_config.apiKey) {\n        await credentialsManager.getInstance(this.context).storeCredential(\n          providerId,\n          'apiKey',\n          _config.apiKey\n        );\n\n        // Remove API key from the config object that will be stored in settings\n        const { apiKey, ...nonSensitiveConfig } = _config;\n        logger.debug('Storing provider API key securely:', !!apiKey);\n        _config = nonSensitiveConfig;\n      }\n\n      // Update non-sensitive settings in VS Code configuration\n      try {\n        // Get the current configuration\n        const vsConfig = vscode.workspace.getConfiguration(this.configSection);\n\n        // Get the current providers object\n        const providers = vsConfig.get('providers') || {};\n\n        // Update the specific provider in the providers object\n        const updatedProviders = {\n          ...providers,\n          [providerId]: _config\n        };\n\n        // Log the current configuration and what we're trying to update\n        logger.debug(`Current providers configuration: ${JSON.stringify(providers)}`);\n        logger.debug('Updating provider ' + providerId + ' with config: ' + JSON.stringify(_config));\n        logger.debug(`Updated providers will be: ${JSON.stringify(updatedProviders)}`);\n\n        // Update the entire providers object\n        try {\n          // First try updating at the User level (most reliable)\n          try {\n            // Use ConfigurationTarget.Global explicitly instead of boolean true\n            await vsConfig.update('providers', updatedProviders, vscode.ConfigurationTarget.Global);\n            logger.info(`Updated global configuration for provider ${providerId}`);\n\n            // Update the provider instance if it exists\n            const provider = this.providers.get(providerId);\n            if (provider) {\n              await provider.loadConfig();\n            }\n\n            return true;\n          } catch (userError) {\n            logger.warn(`Failed to update global configuration: ${userError}. Trying workspace level...`);\n          }\n\n          // Then try Workspace level if available\n          if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n            await vsConfig.update('providers', updatedProviders, vscode.ConfigurationTarget.Workspace);\n            logger.info(`Updated workspace configuration for provider ${providerId}`);\n\n            // Update the provider instance if it exists\n            const provider = this.providers.get(providerId);\n            if (provider) {\n              await provider.loadConfig();\n            }\n\n            return true;\n          }\n\n          // If we get here, both attempts failed\n          logger.error(`Failed to update configuration for provider ${providerId}: No valid configuration target found`);\n          return false;\n        } catch (updateError) {\n          logger.error(`Failed to update configuration: ${updateError}`);\n          return false;\n        }\n      } catch (error) {\n        logger.error(`Failed to update configuration for provider ${providerId}:`, error);\n        return false;\n      }\n    } catch (error) {\n      logger.error(`Failed to update provider config for ${providerId}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Check if a provider is configured\n     */\n  public async isProviderConfigured(providerId: string): Promise<boolean> {\n    try {\n      const provider = this.providers.get(providerId);\n      if (!provider) {\n        return false;\n      }\n      return provider.isConfigured();\n    } catch (error) {\n      logger.error(`Failed to check if provider ${providerId} is configured:`, error);\n      return false;\n    }\n  }\n    \n  /**\n     * Check if a provider is enabled\n     * A provider is considered enabled if:\n     * 1. It's in the enabledProviders list AND\n     * 2. It's configured AND\n     * 3. It's not explicitly disabled in its config\n     */\n  public async isProviderEnabled(providerId: string): Promise<boolean> {\n    try {\n      // First check if the provider is in the enabledProviders list\n      const config = vscode.workspace.getConfiguration(this.configSection);\n      const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n      \n      // If no providers are enabled, or this provider is not in the list, it's disabled\n      if (!enabledProviders.includes(providerId)) {\n        return false;\n      }\n      \n      const provider = this.providers.get(providerId);\n      if (!provider) {\n        return false;\n      }\n            \n      // Check if the provider is configured\n      if (!provider.isConfigured()) {\n        return false;\n      }\n            \n      // Check if the provider is explicitly disabled in its config\n      const providers = config.get<Record<string, any>>('providers') || {};\n      const providerConfig = providers[providerId] || {};\n            \n      // If disabled is explicitly set to true, the provider is disabled\n      if (providerConfig.disabled === true) {\n        return false;\n      }\n            \n      return true;\n    } catch (error) {\n      logger.error(`Failed to check if provider ${providerId} is enabled:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Generate text with a provider\n     */\n  public async generate(\n    providerId: string,\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    const provider = this.providers.get(providerId);\n    if (!provider) {\n      throw new Error(`Provider '${providerId}' not found`);\n    }\n\n    if (!provider.isConfigured()) {\n      throw new Error(`Provider '${providerId}' is not configured`);\n    }\n\n    return provider.generate(params, cancellationToken, tools);\n  }\n\n  /**\n     * List models for a provider\n     */\n  public async listModels(providerId: string): Promise<LLMModelInfo[]> {\n    const provider = this.providers.get(providerId);\n    if (!provider) {\n      throw new Error(`Provider '${providerId}' not found`);\n    }\n\n    if (!provider.isConfigured()) {\n      throw new Error(`Provider '${providerId}' is not configured`);\n    }\n\n    return provider.listModels();\n  }\n\n  /**\n     * Test connection to a provider\n     */\n  public async testConnection(providerId: string, modelId: string): Promise<{success: boolean, message: string}> {\n    const provider = this.providers.get(providerId);\n    if (!provider) {\n      return {\n        success: false,\n        message: `Provider '${providerId}' not found`\n      };\n    }\n\n    if (!provider.isConfigured()) {\n      return {\n        success: false,\n        message: `Provider '${providerId}' is not configured`\n      };\n    }\n\n    return provider.testConnection(modelId);\n  }\n}\n\n// Export singleton instance\nexport const providerManager = {\n  getInstance: ProviderManager.getInstance\n};\n"]}