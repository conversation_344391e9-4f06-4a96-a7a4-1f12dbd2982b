{"version": 3, "file": "advancedDiffTool.js", "sourceRoot": "", "sources": ["../../src/tools/advancedDiffTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,6BAAwB;AACxB,2CAA6B;AAC7B,uCAAyB;AACzB,2CAA6B;AAE7B,kDAAoC;AACpC,2CAA6B;AAO7B;;GAEG;AACH,MAAa,QAAQ;IACV,EAAE,GAAG,UAAU,CAAC;IAChB,IAAI,GAAG,WAAW,CAAC;IACnB,WAAW,GAAG,yFAAyF,CAAC;IACxG,IAAI,GAAG,cAAc,CAAC;IACtB,OAAO,GAAG;QACjB,cAAc,EAAE;YACd,WAAW,EAAE,uCAAuC;YACpD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;gBACzD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;gBAC1D,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC;gBAC1I,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;gBAC7G,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;aAClG,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;gBACzD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;gBAC1D,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC;gBAC1I,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;gBAC7G,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;aAClG,CAAC;SACH;QACD,gBAAgB,EAAE;YAChB,WAAW,EAAE,yCAAyC;YACtD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;gBACxD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;gBACzD,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC;gBAC1I,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;aAC9G,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;gBACxD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;gBACzD,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC;gBAC1I,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;aAC9G,CAAC;SACH;QACD,aAAa,EAAE;YACb,WAAW,EAAE,oCAAoC;YACjD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;gBAC5D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;gBAC5D,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mFAAmF,CAAC;gBAC/H,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;aAClG,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;gBAC5D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;gBAC5D,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mFAAmF,CAAC;gBAC/H,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;aAClG,CAAC;SACH;QACD,YAAY,EAAE;YACZ,WAAW,EAAE,yBAAyB;YACtC,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;gBAC3D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;gBACzD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qFAAqF,CAAC;gBACjI,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;aAC9G,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;gBAC3D,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;gBACzD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qFAAqF,CAAC;gBACjI,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;aAC9G,CAAC;SACH;QACD,SAAS,EAAE;YACT,WAAW,EAAE,8CAA8C;YAC3D,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sCAAsC,CAAC;gBAClE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iGAAiG,CAAC;gBACxI,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBACvE,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;aACpG,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sCAAsC,CAAC;gBAClE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iGAAiG,CAAC;gBACxI,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBACvE,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;aACpG,CAAC;SACH;QACD,cAAc,EAAE;YACd,WAAW,EAAE,0DAA0D;YACvE,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;gBACzD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;gBAC1D,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,2FAA2F,CAAC;aACtI,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;gBACzD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;gBAC1D,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,2FAA2F,CAAC;aACtI,CAAC;SACH;KACF,CAAC;IAEe,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAEvD,oBAAoB;IACZ,MAAM,CAAC,OAAO,GAAG,IAAI,GAAG,EAAkD,CAAC;IAC3E,MAAM,CAAU,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;IAE3E,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,mDAAmD;YACnD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC;gBACnF,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,UAAU,IAAI,WAAW,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACjH,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,QAAQ,UAAU,EAAE,CAAC;gBACrB,KAAK,cAAc;oBACjB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACrD,KAAK,gBAAgB;oBACnB,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACvD,KAAK,aAAa;oBAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACpD,KAAK,YAAY;oBACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACnD,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAChD,KAAK,cAAc;oBACjB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACrD;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,UAAU,EAAE;wBACtC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,0BAA0B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACzD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,wBAAwB;YACxB,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE7D,mBAAmB;YACnB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC7B,OAAO;gBACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;YAED,4CAA4C;YAC5C,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE9D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,iBAAiB,KAAK,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iBAAiB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,uDAAuD;QACvD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvD,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;YACzC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,QAAQ,CAAC,gBAAgB,EAAE,CAAC;gBACvD,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAgB,EAAE,UAAkB;QACpE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,MAAM,SAAS,GAAI,KAAK,CAAC,SAAoB,IAAI,OAAO,CAAC;YACzD,MAAM,MAAM,GAAI,KAAK,CAAC,MAAiB,IAAI,SAAS,CAAC;YACrD,MAAM,YAAY,GAAI,KAAK,CAAC,YAAuB,IAAI,CAAC,CAAC;YAEzD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,SAAS,EAAE;oBACrC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,SAAS,EAAE;oBACrC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,qBAAqB;YACrB,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAC/D,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAE/D,gBAAgB;YAChB,IAAI,UAA2B,CAAC;YAEhC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACzB,wEAAwE;gBACxE,UAAU,GAAI,IAAY,CAAC,WAAW,CACpC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EACxB,QAAQ,EACR,QAAQ,EACR,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EACxB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EACxB,YAAY,CACb,CAAC;YACJ,CAAC;iBAAM,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;gBACrC,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACvE,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;iBAAM,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACvE,UAAU,GAAG,OAAO,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,MAAM,EAAE;oBACtC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE;wBACL,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,QAAQ,CAAC,MAAM;wBACrB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;qBACnC;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,QAAQ,CAAC,MAAM;wBACrB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;qBACnC;oBACD,SAAS;oBACT,MAAM;iBACP;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,SAAS;oBACT,SAAS;oBACT,SAAS;oBACT,MAAM;iBACP;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACxD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAAgB,EAAE,UAAkB;QACtE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;YACxC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;YACxC,MAAM,SAAS,GAAI,KAAK,CAAC,SAAoB,IAAI,OAAO,CAAC;YACzD,MAAM,MAAM,GAAI,KAAK,CAAC,MAAiB,IAAI,SAAS,CAAC;YAErD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0BAA0B;oBACjC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0BAA0B;oBACjC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,gBAAgB;YAChB,IAAI,UAA2B,CAAC;YAEhC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACzB,wEAAwE;gBACxE,UAAU,GAAI,IAAY,CAAC,WAAW,CACpC,QAAQ,EACR,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,CACX,CAAC;YACJ,CAAC;iBAAM,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;gBACrC,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACrE,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;iBAAM,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACrE,UAAU,GAAG,OAAO,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,MAAM,EAAE;oBACtC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,IAAI,EAAE,UAAU;oBAChB,SAAS;oBACT,MAAM;oBACN,KAAK,EAAE;wBACL,OAAO,EAAE,OAAO,CAAC,MAAM;wBACvB,OAAO,EAAE,OAAO,CAAC,MAAM;wBACvB,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;wBAClC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;qBACnC;iBACF;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,SAAS;oBACT,MAAM;iBACP;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC1D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAgB,EAAE,UAAkB;QACnE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,MAAM,UAAU,GAAG,KAAK,CAAC,UAAoB,CAAC;YAC9C,MAAM,YAAY,GAAI,KAAK,CAAC,YAAuB,IAAI,CAAC,CAAC;YAEzD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,SAAS,EAAE;oBACrC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,SAAS,EAAE;oBACrC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,qBAAqB;YACrB,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAC/D,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAE/D,iBAAiB;YACjB,wEAAwE;YACxE,MAAM,YAAY,GAAI,IAAY,CAAC,WAAW,CAC5C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EACxB,QAAQ,EACR,QAAQ,EACR,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EACxB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EACxB,YAAY,CACb,CAAC;YAEF,uCAAuC;YACvC,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;YAChE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,KAAK,EAAE,YAAY;oBACnB,UAAU,EAAE,UAAU,IAAI,IAAI;oBAC9B,KAAK,EAAE;wBACL,KAAK,EAAE;4BACL,IAAI,EAAE,SAAS;4BACf,IAAI,EAAE,QAAQ,CAAC,MAAM;4BACrB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;yBACnC;wBACD,KAAK,EAAE;4BACL,IAAI,EAAE,SAAS;4BACf,IAAI,EAAE,QAAQ,CAAC,MAAM;4BACrB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;yBACnC;qBACF;iBACF;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,SAAS;oBACT,SAAS;oBACT,UAAU;iBACX;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACvD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAgB,EAAE,UAAkB;QAClE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,MAAM,UAAU,GAAG,KAAK,CAAC,UAAoB,CAAC;YAC9C,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB;YAErE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,yBAAyB,SAAS,EAAE;oBAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,qBAAqB;YACrB,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEnE,6BAA6B;YAC7B,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,CAAC;gBACH,wBAAwB;gBACxB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;gBAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBAE7D,IAAI,cAAc,KAAK,KAAK,EAAE,CAAC;oBAC7B,yCAAyC;oBACzC,IAAI,YAAY,EAAE,CAAC;wBACjB,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBACzC,CAAC;oBAED,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mFAAmF;wBAC1F,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAED,4BAA4B;gBAC5B,IAAI,UAAU,EAAE,CAAC;oBACf,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;gBAClE,CAAC;qBAAM,CAAC;oBACN,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;gBAChE,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE;wBACN,OAAO,EAAE,iCAAiC,UAAU,IAAI,QAAQ,EAAE;wBAClE,aAAa,EAAE,YAAY;qBAC5B;oBACD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;oBACV,QAAQ,EAAE;wBACR,QAAQ;wBACR,SAAS;wBACT,UAAU;qBACX;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,yCAAyC;gBACzC,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBACzC,CAAC;gBAED,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uBAAuB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACtD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,KAAgB,EAAE,UAAkB;QAC/D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;YACpC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;YACpC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,IAAI,EAAE,CAAC;YAC9C,MAAM,MAAM,GAAI,KAAK,CAAC,MAAiB,IAAI,QAAQ,CAAC;YAEpD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB;oBAC/B,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,6BAA6B;YAC7B,IAAI,OAAO,GAAG,UAAU,CAAC;YAEzB,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBACtB,OAAO,IAAI,kGAAkG,CAAC;YAChH,CAAC;iBAAM,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC7B,OAAO,IAAI,SAAS,CAAC;YACvB,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,IAAI,IAAI,OAAO,EAAE,CAAC;YAC3B,CAAC;YAED,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,IAAI,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,KAAK,KAAK,GAAG,CAAC;YAC3B,CAAC;YAED,IAAI,CAAC;gBACH,2BAA2B;gBAC3B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAE3D,IAAI,MAAM,EAAE,CAAC;oBACX,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,MAAM,EAAE;wBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAED,IAAI,MAAM,GAAoB,MAAM,CAAC;gBAErC,iCAAiC;gBACjC,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;oBACvC,IAAI,CAAC;wBACH,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACjC,MAAM,OAAO,GAAa,EAAE,CAAC;wBAE7B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;4BACzB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCACpB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;gCACnF,OAAO,CAAC,IAAI,CAAC;oCACX,IAAI;oCACJ,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;iCAC3B,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAED,MAAM,GAAG,OAAO,CAAC;oBACnB,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,2CAA2C;wBAC3C,MAAM,GAAG,MAAM,CAAC;oBAClB,CAAC;gBACH,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE;wBACN,IAAI,EAAE,MAAM;wBACZ,MAAM;wBACN,OAAO;qBACR;oBACD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;oBACV,QAAQ,EAAE;wBACR,KAAK;wBACL,KAAK;wBACL,MAAM;qBACP;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,SAAc,EAAE,CAAC;gBACxB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,8BAA8B,SAAS,CAAC,OAAO,IAAI,SAAS,EAAE;oBACrE,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,oBAAoB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACnD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAgB,EAAE,UAAkB;QACpE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;YAC5C,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAExC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B;oBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,SAAS,EAAE;oBACrC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,SAAS,EAAE;oBACrC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,qDAAqD;YACrD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBAClD,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;YAChD,CAAC;YAED,qBAAqB;YACrB,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAC/D,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAE/D,yBAAyB;YACzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEnF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,IAAI,EAAE,YAAY;oBAClB,QAAQ;oBACR,KAAK,EAAE;wBACL,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,QAAQ,CAAC,MAAM;wBACrB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;qBACnC;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,QAAQ,CAAC,MAAM;wBACrB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;qBACnC;iBACF;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,SAAS;oBACT,SAAS;oBACT,QAAQ;iBACT;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACxD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,kBAAkB,CAAC,MAAc,EAAE,MAAc,EAAE,SAAiB;QAC1E,wEAAwE;QACxE,MAAM,OAAO,GAAG,IAAW,CAAC;QAE5B,QAAQ,SAAS,EAAE,CAAC;YACpB,KAAK,OAAO;gBACV,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC3C,KAAK,OAAO;gBACV,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC3C,KAAK,OAAO;gBACV,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC3C,KAAK,WAAW;gBACd,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC/C,KAAK,KAAK;gBACR,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACzC,KAAK,MAAM;gBACT,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAClC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAClC,OAAO,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC1C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,+CAA+C;oBAC/C,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC3C,CAAC;YACH;gBACE,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,OAAc;QACzC,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,OAAO,CAAC,OAAO,CAAC,CAAC,MAA6D,EAAE,EAAE;YAChF,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAC/D,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAErH,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI,IAAI,CAAC;YAClC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,GAAW;QAC1C,MAAM,YAAY,GAA2B;YAC3C,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,GAAG;YACT,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,OAAO;YACjB,KAAK,EAAE,MAAM;YACb,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,UAAU;YACjB,KAAK,EAAE,OAAO;SACf,CAAC;QAEF,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC;IACrC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,UAAkB,EAAE,UAAkB,EAAE,QAAgB;QACzF,uCAAuC;QACvC,wEAAwE;QACxE,MAAM,QAAQ,GAAI,IAAY,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAEjE,0DAA0D;QAC1D,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAExE,OAAO;YACL,OAAO,EAAE,eAAe;YACxB,OAAO,EAAE,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC;SAC3D,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,OAAc,EAAE,QAAgB;QAC7D,MAAM,eAAe,GAAU,EAAE,CAAC;QAClC,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACvC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;gBAEtD,yDAAyD;gBACzD,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAEzE,eAAe,CAAC,IAAI,CAAC;oBACnB,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,UAAU;oBACrB,OAAO,EAAE,UAAU,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;oBACtC,OAAO,EAAE,MAAM,CAAC,KAAK;oBACrB,YAAY;iBACb,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBAClB,UAAU,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YAChD,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;SAEK;IACG,iBAAiB,CAAC,KAAe,EAAE,QAAgB,EAAE,UAAkB;QAC7E,MAAM,IAAI,GAAQ;YAChB,UAAU,EAAE,SAAS;YACrB,IAAI,EAAE,EAAE;YACR,OAAO,EAAE,EAAE;SACZ,CAAC;QAEF,kCAAkC;QAClC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9B,gDAAgD;QAChD,QAAQ,QAAQ,EAAE,CAAC;YACnB,KAAK,YAAY,CAAC;YAClB,KAAK,YAAY;gBACf,qBAAqB;gBACrB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,iFAAiF,CAAC,CAAC;gBACpH,IAAI,aAAa,EAAE,CAAC;oBAClB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;oBAC7B,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC;oBACpF,MAAM;gBACR,CAAC;gBAED,QAAQ;gBACR,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBAC/C,IAAI,UAAU,EAAE,CAAC;oBACf,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;oBAC1B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;oBAC1B,MAAM;gBACR,CAAC;gBAED,WAAW;gBACX,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;gBAC7D,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;oBAC7B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACxB,MAAM;gBACR,CAAC;gBAED,SAAS;gBACT,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACxE,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;oBAC3B,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBACD,MAAM;YAER,KAAK,QAAQ;gBACX,WAAW;gBACX,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBACvD,IAAI,eAAe,EAAE,CAAC;oBACpB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;oBAC7B,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/B,MAAM;gBACR,CAAC;gBAED,QAAQ;gBACR,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBACjD,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;oBAC1B,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC5B,MAAM;gBACR,CAAC;gBAED,SAAS;gBACT,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;gBAC5D,IAAI,aAAa,EAAE,CAAC;oBAClB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;oBAC3B,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;oBAC7B,MAAM;gBACR,CAAC;gBACD,MAAM;YAEA,+BAA+B;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;SAEK;IACG,2BAA2B,CAAC,OAAc;QAChD,MAAM,MAAM,GAA2B,EAAE,CAAC;QAC1C,MAAM,YAAY,GAA2B,EAAE,CAAC;QAEhD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAErD,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACxE,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC/D,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;QAED,IAAI,OAAO,GAAG,SAAS,OAAO,CAAC,MAAM,sBAAsB,CAAC;QAE5D,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,OAAO,IAAI,KAAK,MAAM,CAAC,KAAK,gBAAgB,CAAC;QAC/C,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,OAAO,IAAI,KAAK,MAAM,CAAC,OAAO,eAAe,CAAC;QAChD,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,qBAAqB,CAAC;YAEjC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;gBACxD,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtC,OAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,IAAI,IAAI,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;;AA1hCH,4BA2hCC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../types/agent';\nimport { z } from 'zod';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport * as diff from 'diff';\nimport * as os from 'os';\nimport * as cp from 'child_process';\nimport * as util from 'util';\n\ninterface Change {\n    type: 'added' | 'removed' | 'unchanged';\n    content: string;\n}\n\n/**\n * Advanced diff tool for comparing files and generating patches\n */\nexport class DiffTool implements ITool {\n  readonly id = 'diffTool';\n  readonly name = 'Diff Tool';\n  readonly description = 'Advanced diff tool for comparing files and generating patches with multiple algorithms.';\n  readonly type = 'multi-action';\n  readonly actions = {\n    'compareFiles': {\n      description: 'Compare two files and generate a diff',\n      schema: z.object({\n        filePathA: z.string().describe('Path to the first file.'),\n        filePathB: z.string().describe('Path to the second file.'),\n        algorithm: z.enum(['chars', 'words', 'lines', 'sentences', 'css', 'json']).optional().describe('Diff algorithm to use. Default is lines.'),\n        format: z.enum(['unified', 'line-by-line', 'json']).optional().describe('Output format. Default is unified.'),\n        contextLines: z.number().optional().describe('Number of context lines to include. Default is 3.')\n      }),\n      inputSchema: z.object({\n        filePathA: z.string().describe('Path to the first file.'),\n        filePathB: z.string().describe('Path to the second file.'),\n        algorithm: z.enum(['chars', 'words', 'lines', 'sentences', 'css', 'json']).optional().describe('Diff algorithm to use. Default is lines.'),\n        format: z.enum(['unified', 'line-by-line', 'json']).optional().describe('Output format. Default is unified.'),\n        contextLines: z.number().optional().describe('Number of context lines to include. Default is 3.')\n      })\n    },\n    'compareStrings': {\n      description: 'Compare two strings and generate a diff',\n      schema: z.object({\n        stringA: z.string().describe('First string to compare.'),\n        stringB: z.string().describe('Second string to compare.'),\n        algorithm: z.enum(['chars', 'words', 'lines', 'sentences', 'css', 'json']).optional().describe('Diff algorithm to use. Default is lines.'),\n        format: z.enum(['unified', 'line-by-line', 'json']).optional().describe('Output format. Default is unified.')\n      }),\n      inputSchema: z.object({\n        stringA: z.string().describe('First string to compare.'),\n        stringB: z.string().describe('Second string to compare.'),\n        algorithm: z.enum(['chars', 'words', 'lines', 'sentences', 'css', 'json']).optional().describe('Diff algorithm to use. Default is lines.'),\n        format: z.enum(['unified', 'line-by-line', 'json']).optional().describe('Output format. Default is unified.')\n      })\n    },\n    'createPatch': {\n      description: 'Create a patch file from two files',\n      schema: z.object({\n        filePathA: z.string().describe('Path to the original file.'),\n        filePathB: z.string().describe('Path to the modified file.'),\n        outputPath: z.string().optional().describe('Path to save the patch file. If not provided, the patch content will be returned.'),\n        contextLines: z.number().optional().describe('Number of context lines to include. Default is 3.')\n      }),\n      inputSchema: z.object({\n        filePathA: z.string().describe('Path to the original file.'),\n        filePathB: z.string().describe('Path to the modified file.'),\n        outputPath: z.string().optional().describe('Path to save the patch file. If not provided, the patch content will be returned.'),\n        contextLines: z.number().optional().describe('Number of context lines to include. Default is 3.')\n      })\n    },\n    'applyPatch': {\n      description: 'Apply a patch to a file',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to patch.'),\n        patchPath: z.string().describe('Path to the patch file.'),\n        outputPath: z.string().optional().describe('Path to save the patched file. If not provided, the original file will be modified.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before patching. Default is true.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to patch.'),\n        patchPath: z.string().describe('Path to the patch file.'),\n        outputPath: z.string().optional().describe('Path to save the patched file. If not provided, the original file will be modified.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before patching. Default is true.')\n      })\n    },\n    'gitDiff': {\n      description: 'Use git diff to compare files or directories',\n      schema: z.object({\n        pathA: z.string().describe('Path to the first file or directory.'),\n        pathB: z.string().optional().describe('Path to the second file or directory. If not provided, will compare with the working directory.'),\n        options: z.string().optional().describe('Additional git diff options.'),\n        format: z.enum(['normal', 'json', 'stat']).optional().describe('Output format. Default is normal.')\n      }),\n      inputSchema: z.object({\n        pathA: z.string().describe('Path to the first file or directory.'),\n        pathB: z.string().optional().describe('Path to the second file or directory. If not provided, will compare with the working directory.'),\n        options: z.string().optional().describe('Additional git diff options.'),\n        format: z.enum(['normal', 'json', 'stat']).optional().describe('Output format. Default is normal.')\n      })\n    },\n    'semanticDiff': {\n      description: 'Generate a semantic diff that understands code structure',\n      schema: z.object({\n        filePathA: z.string().describe('Path to the first file.'),\n        filePathB: z.string().describe('Path to the second file.'),\n        language: z.string().optional().describe('Programming language of the files. If not provided, will be inferred from file extension.')\n      }),\n      inputSchema: z.object({\n        filePathA: z.string().describe('Path to the first file.'),\n        filePathB: z.string().describe('Path to the second file.'),\n        language: z.string().optional().describe('Programming language of the files. If not provided, will be inferred from file extension.')\n      })\n    }\n  };\n\n  private readonly execPromise = util.promisify(cp.exec);\n\n  // Backup management\n  private static backups = new Map<string, { content: string, timestamp: number }>();\n  private static readonly BACKUP_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      // Check if the action exists in the actions object\n      if (!actionName || !Object.prototype.hasOwnProperty.call(this.actions, actionName)) {\n        return {\n          success: false,\n          error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      switch (actionName) {\n      case 'compareFiles':\n        return this.executeCompareFiles(input, actionName);\n      case 'compareStrings':\n        return this.executeCompareStrings(input, actionName);\n      case 'createPatch':\n        return this.executeCreatePatch(input, actionName);\n      case 'applyPatch':\n        return this.executeApplyPatch(input, actionName);\n      case 'gitDiff':\n        return this.executeGitDiff(input, actionName);\n      case 'semanticDiff':\n        return this.executeSemanticDiff(input, actionName);\n      default:\n        return {\n          success: false,\n          error: `Unknown action: ${actionName}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Diff operation failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Create a backup of a file before editing\n     */\n  private async createBackup(filePath: string): Promise<void> {\n    try {\n      // Clean up old backups\n      this.cleanupOldBackups();\n\n      // Read the file content\n      const content = await fs.promises.readFile(filePath, 'utf8');\n\n      // Store the backup\n      DiffTool.backups.set(filePath, {\n        content,\n        timestamp: Date.now()\n      });\n    } catch (error) {\n      throw new Error(`Failed to create backup of ${filePath}: ${error}`);\n    }\n  }\n\n  /**\n     * Restore a file from backup\n     */\n  private async restoreFromBackup(filePath: string): Promise<boolean> {\n    try {\n      const backup = DiffTool.backups.get(filePath);\n      if (!backup) {\n        return false;\n      }\n\n      // Write the backup content back to the file\n      await fs.promises.writeFile(filePath, backup.content, 'utf8');\n\n      return true;\n    } catch (error) {\n      throw new Error(`Failed to restore ${filePath} from backup: ${error}`);\n    }\n  }\n\n  /**\n     * Clean up old backups\n     */\n  private cleanupOldBackups(): void {\n    const now = Date.now();\n    // Convert entries() iterator to array before iterating\n    const entries = Array.from(DiffTool.backups.entries());\n    for (const [filePath, backup] of entries) {\n      if (now - backup.timestamp > DiffTool.BACKUP_EXPIRY_MS) {\n        DiffTool.backups.delete(filePath);\n      }\n    }\n  }\n\n  private async executeCompareFiles(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePathA = input.filePathA as string;\n      const filePathB = input.filePathB as string;\n      const algorithm = (input.algorithm as string) || 'lines';\n      const format = (input.format as string) || 'unified';\n      const contextLines = (input.contextLines as number) || 3;\n\n      if (!filePathA) {\n        return {\n          success: false,\n          error: '\\'filePathA\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!filePathB) {\n        return {\n          success: false,\n          error: '\\'filePathB\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate files exist\n      if (!fs.existsSync(filePathA)) {\n        return {\n          success: false,\n          error: `File not found: ${filePathA}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!fs.existsSync(filePathB)) {\n        return {\n          success: false,\n          error: `File not found: ${filePathB}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Read file contents\n      const contentA = await fs.promises.readFile(filePathA, 'utf8');\n      const contentB = await fs.promises.readFile(filePathB, 'utf8');\n\n      // Generate diff\n      let diffResult: string | object;\n\n      if (format === 'unified') {\n        // Use type assertion to handle missing type definitions in diff library\n        diffResult = (diff as any).createPatch(\n          path.basename(filePathB),\n          contentA,\n          contentB,\n          path.basename(filePathA),\n          path.basename(filePathB),\n          contextLines\n        );\n      } else if (format === 'line-by-line') {\n        const changes = this.getDiffByAlgorithm(contentA, contentB, algorithm);\n        diffResult = this.formatLineByLineDiff(changes);\n      } else if (format === 'json') {\n        const changes = this.getDiffByAlgorithm(contentA, contentB, algorithm);\n        diffResult = changes;\n      } else {\n        return {\n          success: false,\n          error: `Unsupported format: ${format}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      return {\n        success: true,\n        output: {\n          diff: diffResult,\n          fileA: {\n            path: filePathA,\n            size: contentA.length,\n            lines: contentA.split('\\n').length\n          },\n          fileB: {\n            path: filePathB,\n            size: contentB.length,\n            lines: contentB.split('\\n').length\n          },\n          algorithm,\n          format\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePathA,\n          filePathB,\n          algorithm,\n          format\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Compare files failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async executeCompareStrings(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const stringA = input.stringA as string;\n      const stringB = input.stringB as string;\n      const algorithm = (input.algorithm as string) || 'lines';\n      const format = (input.format as string) || 'unified';\n\n      if (stringA === undefined) {\n        return {\n          success: false,\n          error: '\\'stringA\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (stringB === undefined) {\n        return {\n          success: false,\n          error: '\\'stringB\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Generate diff\n      let diffResult: string | object;\n\n      if (format === 'unified') {\n        // Use type assertion to handle missing type definitions in diff library\n        diffResult = (diff as any).createPatch(\n          'string',\n          stringA,\n          stringB,\n          'original',\n          'modified'\n        );\n      } else if (format === 'line-by-line') {\n        const changes = this.getDiffByAlgorithm(stringA, stringB, algorithm);\n        diffResult = this.formatLineByLineDiff(changes);\n      } else if (format === 'json') {\n        const changes = this.getDiffByAlgorithm(stringA, stringB, algorithm);\n        diffResult = changes;\n      } else {\n        return {\n          success: false,\n          error: `Unsupported format: ${format}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      return {\n        success: true,\n        output: {\n          diff: diffResult,\n          algorithm,\n          format,\n          stats: {\n            lengthA: stringA.length,\n            lengthB: stringB.length,\n            linesA: stringA.split('\\n').length,\n            linesB: stringB.split('\\n').length\n          }\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          algorithm,\n          format\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Compare strings failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async executeCreatePatch(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePathA = input.filePathA as string;\n      const filePathB = input.filePathB as string;\n      const outputPath = input.outputPath as string;\n      const contextLines = (input.contextLines as number) || 3;\n\n      if (!filePathA) {\n        return {\n          success: false,\n          error: '\\'filePathA\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!filePathB) {\n        return {\n          success: false,\n          error: '\\'filePathB\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate files exist\n      if (!fs.existsSync(filePathA)) {\n        return {\n          success: false,\n          error: `File not found: ${filePathA}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!fs.existsSync(filePathB)) {\n        return {\n          success: false,\n          error: `File not found: ${filePathB}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Read file contents\n      const contentA = await fs.promises.readFile(filePathA, 'utf8');\n      const contentB = await fs.promises.readFile(filePathB, 'utf8');\n\n      // Generate patch\n      // Use type assertion to handle missing type definitions in diff library\n      const patchContent = (diff as any).createPatch(\n        path.basename(filePathB),\n        contentA,\n        contentB,\n        path.basename(filePathA),\n        path.basename(filePathB),\n        contextLines\n      );\n\n      // Save patch if outputPath is provided\n      if (outputPath) {\n        await fs.promises.writeFile(outputPath, patchContent, 'utf8');\n      }\n\n      return {\n        success: true,\n        output: {\n          patch: patchContent,\n          outputPath: outputPath || null,\n          stats: {\n            fileA: {\n              path: filePathA,\n              size: contentA.length,\n              lines: contentA.split('\\n').length\n            },\n            fileB: {\n              path: filePathB,\n              size: contentB.length,\n              lines: contentB.split('\\n').length\n            }\n          }\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePathA,\n          filePathB,\n          outputPath\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Create patch failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async executeApplyPatch(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const patchPath = input.patchPath as string;\n      const outputPath = input.outputPath as string;\n      const createBackup = input.createBackup !== false; // Default to true\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!patchPath) {\n        return {\n          success: false,\n          error: '\\'patchPath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate files exist\n      if (!fs.existsSync(filePath)) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!fs.existsSync(patchPath)) {\n        return {\n          success: false,\n          error: `Patch file not found: ${patchPath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Read file contents\n      const fileContent = await fs.promises.readFile(filePath, 'utf8');\n      const patchContent = await fs.promises.readFile(patchPath, 'utf8');\n\n      // Create backup if requested\n      if (createBackup) {\n        await this.createBackup(filePath);\n      }\n\n      try {\n        // Parse and apply patch\n        const patches = diff.parsePatch(patchContent);\n        const patchedContent = diff.applyPatch(fileContent, patches);\n\n        if (patchedContent === false) {\n          // Restore from backup if patching failed\n          if (createBackup) {\n            await this.restoreFromBackup(filePath);\n          }\n\n          return {\n            success: false,\n            error: 'Failed to apply patch. The patch may not be applicable to the current file state.',\n            toolId: this.id,\n            actionName\n          };\n        }\n\n        // Write the patched content\n        if (outputPath) {\n          await fs.promises.writeFile(outputPath, patchedContent, 'utf8');\n        } else {\n          await fs.promises.writeFile(filePath, patchedContent, 'utf8');\n        }\n\n        return {\n          success: true,\n          output: {\n            message: `Successfully applied patch to ${outputPath || filePath}`,\n            backupCreated: createBackup\n          },\n          toolId: this.id,\n          actionName,\n          metadata: {\n            filePath,\n            patchPath,\n            outputPath\n          }\n        };\n      } catch (error) {\n        // Restore from backup if patching failed\n        if (createBackup) {\n          await this.restoreFromBackup(filePath);\n        }\n\n        throw error;\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Apply patch failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async executeGitDiff(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const pathA = input.pathA as string;\n      const pathB = input.pathB as string;\n      const options = input.options as string || '';\n      const format = (input.format as string) || 'normal';\n\n      if (!pathA) {\n        return {\n          success: false,\n          error: '\\'pathA\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Construct git diff command\n      let command = 'git diff';\n\n      if (format === 'json') {\n        command += ' --no-color --output-indicator-new=\"+\" --output-indicator-old=\"-\" --output-indicator-context=\" \"';\n      } else if (format === 'stat') {\n        command += ' --stat';\n      }\n\n      if (options) {\n        command += ` ${options}`;\n      }\n\n      if (pathB) {\n        command += ` \"${pathA}\" \"${pathB}\"`;\n      } else {\n        command += ` \"${pathA}\"`;\n      }\n\n      try {\n        // Execute git diff command\n        const { stdout, stderr } = await this.execPromise(command);\n\n        if (stderr) {\n          return {\n            success: false,\n            error: `Git diff error: ${stderr}`,\n            toolId: this.id,\n            actionName\n          };\n        }\n\n        let result: string | object = stdout;\n\n        // Parse JSON output if requested\n        if (format === 'json' && stdout.trim()) {\n          try {\n            const lines = stdout.split('\\n');\n            const changes: Change[] = [];\n\n            for (const line of lines) {\n              if (line.length > 0) {\n                const type = line[0] === '+' ? 'added' : line[0] === '-' ? 'removed' : 'unchanged';\n                changes.push({\n                  type,\n                  content: line.substring(1)\n                });\n              }\n            }\n\n            result = changes;\n          } catch (parseError) {\n            // Fall back to raw output if parsing fails\n            result = stdout;\n          }\n        }\n\n        return {\n          success: true,\n          output: {\n            diff: result,\n            format,\n            command\n          },\n          toolId: this.id,\n          actionName,\n          metadata: {\n            pathA,\n            pathB,\n            format\n          }\n        };\n      } catch (execError: any) {\n        return {\n          success: false,\n          error: `Git diff execution failed: ${execError.message || execError}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Git diff failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async executeSemanticDiff(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const filePathA = input.filePathA as string;\n      const filePathB = input.filePathB as string;\n      let language = input.language as string;\n\n      if (!filePathA) {\n        return {\n          success: false,\n          error: '\\'filePathA\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!filePathB) {\n        return {\n          success: false,\n          error: '\\'filePathB\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate files exist\n      if (!fs.existsSync(filePathA)) {\n        return {\n          success: false,\n          error: `File not found: ${filePathA}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (!fs.existsSync(filePathB)) {\n        return {\n          success: false,\n          error: `File not found: ${filePathB}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Infer language from file extension if not provided\n      if (!language) {\n        const ext = path.extname(filePathA).toLowerCase();\n        language = this.getLanguageFromExtension(ext);\n      }\n\n      // Read file contents\n      const contentA = await fs.promises.readFile(filePathA, 'utf8');\n      const contentB = await fs.promises.readFile(filePathB, 'utf8');\n\n      // Generate semantic diff\n      const semanticDiff = await this.generateSemanticDiff(contentA, contentB, language);\n\n      return {\n        success: true,\n        output: {\n          diff: semanticDiff,\n          language,\n          fileA: {\n            path: filePathA,\n            size: contentA.length,\n            lines: contentA.split('\\n').length\n          },\n          fileB: {\n            path: filePathB,\n            size: contentB.length,\n            lines: contentB.split('\\n').length\n          }\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePathA,\n          filePathB,\n          language\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Semantic diff failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Get diff based on the specified algorithm\n     */\n  private getDiffByAlgorithm(oldStr: string, newStr: string, algorithm: string): any[] {\n    // Use type assertion to handle missing type definitions in diff library\n    const diffLib = diff as any;\n\n    switch (algorithm) {\n    case 'chars':\n      return diffLib.diffChars(oldStr, newStr);\n    case 'words':\n      return diffLib.diffWords(oldStr, newStr);\n    case 'lines':\n      return diffLib.diffLines(oldStr, newStr);\n    case 'sentences':\n      return diffLib.diffSentences(oldStr, newStr);\n    case 'css':\n      return diffLib.diffCss(oldStr, newStr);\n    case 'json':\n      try {\n        const oldObj = JSON.parse(oldStr);\n        const newObj = JSON.parse(newStr);\n        return diffLib.diffJson(oldObj, newObj);\n      } catch (error) {\n        // Fall back to line diff if JSON parsing fails\n        return diffLib.diffLines(oldStr, newStr);\n      }\n    default:\n      return diffLib.diffLines(oldStr, newStr);\n    }\n  }\n\n  /**\n     * Format diff changes as line-by-line output\n     */\n  private formatLineByLineDiff(changes: any[]): string {\n    let result = '';\n\n    changes.forEach((change: { added?: boolean; removed?: boolean; value: string }) => {\n      const prefix = change.added ? '+' : change.removed ? '-' : ' ';\n      const lines = change.value.split('\\n').filter((line: string) => line.length > 0 || !change.added && !change.removed);\n\n      for (const line of lines) {\n        result += `${prefix} ${line}\\n`;\n      }\n    });\n\n    return result;\n  }\n\n  /**\n     * Get language from file extension\n     */\n  private getLanguageFromExtension(ext: string): string {\n    const extensionMap: Record<string, string> = {\n      '.js': 'javascript',\n      '.jsx': 'javascript',\n      '.ts': 'typescript',\n      '.tsx': 'typescript',\n      '.py': 'python',\n      '.java': 'java',\n      '.c': 'c',\n      '.cpp': 'cpp',\n      '.cs': 'csharp',\n      '.go': 'go',\n      '.rb': 'ruby',\n      '.php': 'php',\n      '.swift': 'swift',\n      '.rs': 'rust',\n      '.html': 'html',\n      '.css': 'css',\n      '.json': 'json',\n      '.xml': 'xml',\n      '.md': 'markdown',\n      '.sh': 'shell'\n    };\n\n    return extensionMap[ext] || 'text';\n  }\n\n  /**\n     * Generate a semantic diff that understands code structure\n     */\n  private async generateSemanticDiff(oldContent: string, newContent: string, language: string): Promise<any> {\n    // First, generate a standard line diff\n    // Use type assertion to handle missing type definitions in diff library\n    const lineDiff = (diff as any).diffLines(oldContent, newContent);\n\n    // Analyze the changes based on language-specific patterns\n    const semanticChanges = this.analyzeSemanticChanges(lineDiff, language);\n\n    return {\n      changes: semanticChanges,\n      summary: this.generateSemanticDiffSummary(semanticChanges)\n    };\n  }\n\n  /**\n     * Analyze semantic changes based on language-specific patterns\n     */\n  private analyzeSemanticChanges(changes: any[], language: string): any[] {\n    const semanticChanges: any[] = [];\n    let lineNumber = 1;\n\n    for (const change of changes) {\n      if (change.added || change.removed) {\n        const lines = change.value.split('\\n');\n        const changeType = change.added ? 'added' : 'removed';\n\n        // Analyze the change based on language-specific patterns\n        const semanticInfo = this.analyzeCodeChange(lines, language, changeType);\n\n        semanticChanges.push({\n          type: changeType,\n          lineStart: lineNumber,\n          lineEnd: lineNumber + lines.length - 1,\n          content: change.value,\n          semanticInfo\n        });\n      }\n\n      if (!change.added) {\n        lineNumber += change.value.split('\\n').length;\n      }\n    }\n\n    return semanticChanges;\n  }\n\n  /**\n     * Analyze code changes based on language-specific patterns\n     */\n  private analyzeCodeChange(lines: string[], language: string, changeType: string): any {\n    const info: any = {\n      entityType: 'unknown',\n      name: '',\n      details: {}\n    };\n\n    // Join lines for pattern matching\n    const code = lines.join('\\n');\n\n    // Detect common code patterns based on language\n    switch (language) {\n    case 'javascript':\n    case 'typescript':\n      // Function or method\n      const functionMatch = code.match(/(?:function\\s+(\\w+)|(\\w+)\\s*=\\s*function|\\(.*\\)\\s*=>\\s*{|\\s*(\\w+)\\s*\\(.*\\)\\s*{)/);\n      if (functionMatch) {\n        info.entityType = 'function';\n        info.name = functionMatch[1] || functionMatch[2] || functionMatch[3] || 'anonymous';\n        break;\n      }\n\n      // Class\n      const classMatch = code.match(/class\\s+(\\w+)/);\n      if (classMatch) {\n        info.entityType = 'class';\n        info.name = classMatch[1];\n        break;\n      }\n\n      // Variable\n      const varMatch = code.match(/(?:const|let|var)\\s+(\\w+)\\s*=/);\n      if (varMatch) {\n        info.entityType = 'variable';\n        info.name = varMatch[1];\n        break;\n      }\n\n      // Import\n      const importMatch = code.match(/import\\s+.*\\s+from\\s+['\"]([^'\"]+)['\"]/);\n      if (importMatch) {\n        info.entityType = 'import';\n        info.name = importMatch[1];\n        break;\n      }\n      break;\n\n    case 'python':\n      // Function\n      const pyFunctionMatch = code.match(/def\\s+(\\w+)\\s*\\(/);\n      if (pyFunctionMatch) {\n        info.entityType = 'function';\n        info.name = pyFunctionMatch[1];\n        break;\n      }\n\n      // Class\n      const pyClassMatch = code.match(/class\\s+(\\w+)/);\n      if (pyClassMatch) {\n        info.entityType = 'class';\n        info.name = pyClassMatch[1];\n        break;\n      }\n\n      // Import\n      const pyImportMatch = code.match(/(?:import|from)\\s+(\\w+)/);\n      if (pyImportMatch) {\n        info.entityType = 'import';\n        info.name = pyImportMatch[1];\n        break;\n      }\n      break;\n\n            // Add more languages as needed\n    }\n\n    return info;\n  }\n\n  /**\n     * Generate a summary of semantic changes\n     */\n  private generateSemanticDiffSummary(changes: any[]): string {\n    const counts: Record<string, number> = {};\n    const entityCounts: Record<string, number> = {};\n\n    for (const change of changes) {\n      counts[change.type] = (counts[change.type] || 0) + 1;\n\n      if (change.semanticInfo && change.semanticInfo.entityType !== 'unknown') {\n        const key = `${change.type}_${change.semanticInfo.entityType}`;\n        entityCounts[key] = (entityCounts[key] || 0) + 1;\n      }\n    }\n\n    let summary = `Found ${changes.length} semantic changes:\\n`;\n\n    if (counts.added) {\n      summary += `- ${counts.added} addition(s)\\n`;\n    }\n\n    if (counts.removed) {\n      summary += `- ${counts.removed} removal(s)\\n`;\n    }\n\n    if (Object.keys(entityCounts).length > 0) {\n      summary += '\\nEntity changes:\\n';\n\n      for (const [key, count] of Object.entries(entityCounts)) {\n        const [type, entity] = key.split('_');\n        summary += `- ${count} ${entity}(s) ${type}\\n`;\n      }\n    }\n\n    return summary;\n  }\n}\n"]}