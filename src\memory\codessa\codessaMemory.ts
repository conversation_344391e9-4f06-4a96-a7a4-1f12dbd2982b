import * as vscode from 'vscode';
import { v4 as uuidv4 } from 'uuid';

// Core Polyfills & LangChain Components (Using relative paths)
import type { MemoryMetadata, MemorySource, MemoryType } from '../types';
import {
  BufferMemory,
  ConversationSummaryMemory,
  VectorStoreRetrieverMemory,
  ChatMessageHistory,
  HumanMessage,
  AIMessage,
  SystemMessage,
  MemoryVectorStore,
  Embeddings,
  OpenAIEmbeddings,
  AsyncCaller // Used in custom embeddings
} from '../../agents/workflows/corePolyfill';
import type {
  BaseMessage, // Import BaseMessage for broader type usage
  BaseLanguageModel // Needed for summary memory
} from '../../agents/workflows/corePolyfill';

// Local project imports (Ensure paths are correct)
import { logger } from '../../logger';
import type {
  IMemoryProvider,
  MemoryEntry,
  MemorySearchOptions,
  MemorySettings,
  IVectorStore,
  IDatabase,
  ChatMessage, // Define or import ChatMessage type if needed separately
  MemoryFilter,
} from '../types'; // Assuming ChatMessage might be defined here
import { getConfig, setConfig } from '../../config';
import { llmService } from '../../llm/llmService'; // Used for embeddings and potentially summarization
import { VectorStoreFactory } from './vectorStores/vectorStoreFactory';
import { DatabaseFactory } from './databases/databaseFactory';
import { FileChunkingService } from './fileChunking';

// Constants for database table/collection names
const MEMORIES_COLLECTION = 'memories';
const CHAT_HISTORY_COLLECTION = 'chat_history';

/**
 * Codessa Memory Provider
 * Implements the IMemoryProvider interface using pluggable vector stores,
 * databases, and integrates LangChain memory components for enhanced functionality.
 * Manages both general memory entries (like file chunks) and structured chat history.
 */
export class CodessaMemoryProvider implements IMemoryProvider {
  private context: vscode.ExtensionContext | undefined;
  private vectorStore: IVectorStore | undefined;
  private database: IDatabase | undefined;
  private embeddings: Embeddings | undefined;
  private initialized = false;
  private isInitializing = false; // Prevent race conditions during init

  // Event emitter for memory changes
  private _onMemoriesChanged = new vscode.EventEmitter<void>();
  readonly onMemoriesChanged = this._onMemoriesChanged.event;

  /**
     * Initialize the memory provider, setting up embeddings, vector store, and database.
     * This method is idempotent and handles concurrent initialization calls.
     * @param context The VS Code extension context.
     */
  public async initialize(context: vscode.ExtensionContext): Promise<void> {
    if (this.initialized || this.isInitializing) {
      // If already initialized or initialization is in progress, wait if necessary
      if (this.isInitializing) {
        // Wait for the ongoing initialization to complete
        await new Promise<void>(resolve => {
          const checkInterval = setInterval(() => {
            if (!this.isInitializing) {
              clearInterval(checkInterval);
              resolve();
            }
          }, 100);
        });
      }
      if (this.initialized) {
        logger.debug('CodessaMemoryProvider already initialized.');
      }
      return;
    }

    this.isInitializing = true;
    logger.info('Initializing Codessa memory provider...');

    try {
      this.context = context;

      // 1. Initialize Embeddings
      logger.debug('Initializing embeddings...');
      this.embeddings = await this.createEmbeddings();
      logger.info('Embeddings initialized.');

      // 2. Initialize Vector Store
      const vectorStoreType = getConfig<string>('memory.vectorStore', 'chroma');
      logger.debug(`Initializing vector store (Type: ${vectorStoreType})...`);
      // Pass the already created embeddings instance
      this.vectorStore = await VectorStoreFactory.createVectorStore(vectorStoreType, this.embeddings);
      await this.vectorStore.initialize(); // Initialize the specific vector store instance
      logger.info(`Vector store '${vectorStoreType}' initialized.`);

      // 3. Initialize Database
      const databaseType = getConfig<string>('memory.database', 'sqlite');
      logger.debug(`Initializing database (Type: ${databaseType})...`);
      this.database = await DatabaseFactory.createDatabase(databaseType);
      await this.database.initialize(); // Initialize the specific database instance
      // Ensure necessary collections/tables exist
      if (this.database.ensureCollection) {
        await this.database.ensureCollection(MEMORIES_COLLECTION);
        await this.database.ensureCollection(CHAT_HISTORY_COLLECTION);
      } else {
        logger.warn('Database does not support ensureCollection method. Collections may need to be created manually.');
      }
      logger.info(`Database '${databaseType}' initialized.`);

      // 4. Initialize FileChunkingService
      logger.debug('Initializing FileChunkingService...');
      // Create a memory storer adapter that uses this provider to store memories
      const memoryStorer = {
        storeMemory: async (entryData: { content: string | Buffer; metadata: Record<string, unknown> }): Promise<MemoryEntry> => {
          // Convert Buffer to string if needed
          const content = Buffer.isBuffer(entryData.content)
            ? entryData.content.toString('utf-8')
            : entryData.content;

          // Ensure metadata has required fields
          const metadata: MemoryMetadata = {
            ...entryData.metadata,
            source: (entryData.metadata.source as MemorySource) || 'file',
            type: (entryData.metadata.type as MemoryType) || 'code',
            tags: Array.isArray(entryData.metadata.tags) ? entryData.metadata.tags : ['file', 'chunk']
          };

          return this.addMemory({
            content,
            metadata
          });
        }
      };
      FileChunkingService.initialize(memoryStorer);
      logger.info('FileChunkingService initialized.');

      this.initialized = true;
      logger.info('Codessa memory provider initialized successfully.');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.warn('Error during Codessa memory provider initialization:', { message: errorMessage });

      try {
        // Attempt to initialize with fallback components
        logger.info('Attempting to initialize with fallback components...');

        // 1. Fallback Embeddings - Use our simple implementation
        if (!this.embeddings) {
          logger.info('Using fallback embeddings implementation');
          class FallbackEmbeddings extends Embeddings {
            async embedDocuments(texts: string[]): Promise<number[][]> {
              // Add minimal async operation to justify async
              await Promise.resolve();
              return texts.map(text => this.generateSimpleVector(text));
            }

            async embedQuery(text: string): Promise<number[]> {
              // Add minimal async operation to justify async
              await Promise.resolve();
              return this.generateSimpleVector(text);
            }

            private generateSimpleVector(text: string, dimensions = 384): number[] {
              const vector: number[] = new Array(dimensions).fill(0) as number[];
              const normalizedText = text.toLowerCase().trim();

              for (let i = 0; i < normalizedText.length && i < dimensions; i++) {
                vector[i % dimensions] += normalizedText.charCodeAt(i) / 255;
              }

              const magnitude = Math.sqrt(vector.reduce((sum: number, val: number) => sum + val * val, 0));
              if (magnitude > 0) {
                for (let i = 0; i < dimensions; i++) {
                  vector[i] = vector[i] / magnitude;
                }
              }

              return vector;
            }
          }

          this.embeddings = new FallbackEmbeddings();
        }

        // 2. Fallback Vector Store - Use in-memory store
        if (!this.vectorStore) {
          logger.info('Using in-memory vector store as fallback');
          this.vectorStore = await VectorStoreFactory.createVectorStore('memory', this.embeddings);
          await this.vectorStore.initialize();
        }

        // 3. Fallback Database - Use SQLite
        if (!this.database) {
          logger.info('Using SQLite database as fallback');
          this.database = await DatabaseFactory.createDatabase('sqlite');
          await this.database.initialize();

          if (this.database.ensureCollection) {
            await this.database.ensureCollection(MEMORIES_COLLECTION);
            await this.database.ensureCollection(CHAT_HISTORY_COLLECTION);
          }
        }

        // 4. Initialize FileChunkingService with fallback components
        const memoryStorer = {
          storeMemory: async (entryData: { content: string | Buffer; metadata: Record<string, unknown> }): Promise<MemoryEntry> => {
            const content = Buffer.isBuffer(entryData.content)
              ? entryData.content.toString('utf-8')
              : entryData.content;

            const metadata: MemoryMetadata = {
              ...entryData.metadata,
              source: (entryData.metadata.source as MemorySource) || 'file',
              type: (entryData.metadata.type as MemoryType) || 'code',
              tags: Array.isArray(entryData.metadata.tags) ? entryData.metadata.tags : ['file', 'chunk']
            };

            return this.addMemory({
              content,
              metadata
            });
          }
        };

        FileChunkingService.initialize(memoryStorer);

        this.initialized = true;
        logger.info('Codessa memory provider initialized with fallback components');

      } catch (fallbackError) {
        const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);
        const originalErrorMessage = error instanceof Error ? error.message : String(error);
        // If even the fallback initialization fails, log and set initialized to false
        logger.error('Failed to initialize Codessa memory provider with fallbacks:',
          { message: fallbackErrorMessage, originalError: originalErrorMessage });

        // Reset state on failure
        this.initialized = false;
        this.embeddings = undefined;
        this.vectorStore = undefined;
        this.database = undefined;
      }
    } finally {
      this.isInitializing = false; // Mark initialization as complete (success or fail)
    }
  }

  /**
     * Ensures the provider is initialized before proceeding.
     * Attempts to initialize components if missing.
     */
  private async assertInitialized(): Promise<void> {
    // If already fully initialized, return immediately
    if (this.initialized && this.database && this.vectorStore && this.embeddings) {
      return;
    }

    // If not initialized at all, try to initialize
    if (!this.initialized) {
      logger.warn('Memory provider accessed before initialization. Attempting to initialize now.');
      if (this.context) {
        try {
          await this.initialize(this.context);
        } catch (error) {
          // Error already logged in initialize method
        }
      } else {
        logger.error('Cannot initialize memory provider: No context available');
      }
    }

    // Check if we're still missing components after initialization attempt
    if (!this.initialized || !this.database || !this.vectorStore || !this.embeddings) {
      // Try to create missing components individually
      try {
        logger.warn('Memory provider missing components. Attempting to create fallbacks.');

        // Create embeddings if missing
        if (!this.embeddings) {
          logger.info('Creating fallback embeddings');
          class FallbackEmbeddings extends Embeddings {
            async embedDocuments(texts: string[]): Promise<number[][]> {
              // Add minimal async operation to justify async
              await Promise.resolve();
              return texts.map(text => this.generateSimpleVector(text));
            }

            async embedQuery(text: string): Promise<number[]> {
              // Add minimal async operation to justify async
              await Promise.resolve();
              return this.generateSimpleVector(text);
            }

            private generateSimpleVector(text: string, dimensions = 384): number[] {
              const vector: number[] = new Array(dimensions).fill(0) as number[];
              const normalizedText = text.toLowerCase().trim();

              for (let i = 0; i < normalizedText.length && i < dimensions; i++) {
                vector[i % dimensions] += normalizedText.charCodeAt(i) / 255;
              }

              const magnitude = Math.sqrt(vector.reduce((sum: number, val: number) => sum + val * val, 0));
              if (magnitude > 0) {
                for (let i = 0; i < dimensions; i++) {
                  vector[i] = vector[i] / magnitude;
                }
              }

              return vector;
            }
          }

          this.embeddings = new FallbackEmbeddings();
        }

        // Create vector store if missing
        if (!this.vectorStore) {
          logger.info('Creating fallback vector store');
          this.vectorStore = await VectorStoreFactory.createVectorStore('memory', this.embeddings);
          await this.vectorStore.initialize();
        }

        // Create database if missing
        if (!this.database) {
          logger.info('Creating fallback database');
          this.database = await DatabaseFactory.createDatabase('sqlite');
          await this.database.initialize();

          if (this.database.ensureCollection) {
            await this.database.ensureCollection(MEMORIES_COLLECTION);
            await this.database.ensureCollection(CHAT_HISTORY_COLLECTION);
          }
        }

        // Mark as initialized if we have all components now
        if (this.database && this.vectorStore && this.embeddings) {
          this.initialized = true;
          logger.info('Memory provider successfully initialized with fallback components');
        }
      } catch (error) {
        logger.error('Failed to create fallback components:', error);
        throw new Error('Memory provider could not be initialized with fallbacks');
      }
    }
  }

  /**
     * Creates an Embeddings instance based on the configured LLM provider or fallback.
     */
  private async createEmbeddings(): Promise<Embeddings> {
    const provider = await llmService.getDefaultProvider();

    // Check if the provider has a dedicated, potentially optimized embedding method
    if (provider?.getEmbeddings) {
      try {
        logger.info('Using provider\'s dedicated getEmbeddings() method.');
        const providerEmbeddings = await provider.getEmbeddings() as unknown;
        if (providerEmbeddings &&
            typeof (providerEmbeddings as { embedQuery?: unknown }).embedQuery === 'function' &&
            typeof (providerEmbeddings as { embedDocuments?: unknown }).embedDocuments === 'function') {
          return providerEmbeddings as Embeddings;
        } else {
          logger.warn('Provider\'s getEmbeddings() did not return a valid Embeddings instance. Falling back...');
        }
      } catch (e) {
        const errorMessage = e instanceof Error ? e.message : String(e);
        logger.warn(`Error obtaining embeddings via provider.getEmbeddings(): ${errorMessage}. Falling back...`);
      }
    }


    // Fallback 1: Use provider's generic generateEmbedding function if available
    if (provider?.generateEmbedding) {
      logger.info('Using provider\'s generateEmbedding function for custom Embeddings wrapper.');
      const generateEmbedding = provider.generateEmbedding.bind(provider);
      // Create a custom embeddings class adhering to the LangChain interface
      class ProviderEmbeddingsWrapper extends Embeddings {
        // Leverage AsyncCaller for concurrency and retries if needed
        caller: AsyncCaller;
        dim = 1536; // Default dimension for embeddings
        seed = 'provider_embeddings_wrapper';

        constructor() {
          super();
          this.caller = new AsyncCaller({ /* options */ });
        }

        async generateVector(text: string): Promise<number[]> {
          return generateEmbedding(text);
        }

        async embedDocuments(texts: string[]): Promise<number[][]> {
          // Could potentially batch if the provider supports it, otherwise loop
          const embeddings: number[][] = [];
          for (const text of texts) {
            // Wrap the call with the caller for potential retries/concurrency
            const embedding = await this.caller.call(async () => generateEmbedding(text));
            embeddings.push(embedding);
          }
          return embeddings;
        }

        async embedQuery(text: string): Promise<number[]> {
          // Wrap the call with the caller
          return this.caller.call(async () => generateEmbedding(text));
        }
        // Implement batch embedding if the underlying provider supports it for efficiency
        // async embedBatch?(texts: string[]): Promise<number[][]>;
      }
      return new ProviderEmbeddingsWrapper();
    }

    // Fallback 2: Use OpenAI embeddings if API key is available
    const openAIApiKey = process.env.OPENAI_API_KEY ?? getConfig<string | undefined>('llm.providers.openai.apiKey', undefined);
    if (openAIApiKey) {
      logger.warn('LLM provider does not support embeddings. Falling back to OpenAIEmbeddings.');
      try {
        return new OpenAIEmbeddings({ openAIApiKey });
      } catch (e) {
        const errorMessage = e instanceof Error ? e.message : String(e);
        logger.error(`Failed to initialize OpenAIEmbeddings fallback: ${errorMessage}`);
        // Continue to final error
      }
    }

    // Final Fallback: Use a simple in-memory embedding implementation
    logger.warn('No standard embedding capability available. Using simple fallback embeddings.');

    // Create a simple fallback embeddings class that uses a basic hashing approach
    class FallbackEmbeddings extends Embeddings {
      // Generate a simple vector from text using character codes
      private generateSimpleVector(text: string, dimensions = 384): number[] {
        // Initialize vector with zeros
        const vector: number[] = new Array(dimensions).fill(0) as number[];

        // Normalize text
        const normalizedText = text.toLowerCase().trim();

        // Fill vector with values derived from text
        for (let i = 0; i < normalizedText.length && i < dimensions; i++) {
          // Use character code as a basis for the vector value
          vector[i % dimensions] += normalizedText.charCodeAt(i) / 255;
        }

        // Normalize vector to unit length
        const magnitude = Math.sqrt(vector.reduce((sum: number, val: number) => sum + val * val, 0));
        if (magnitude > 0) {
          for (let i = 0; i < dimensions; i++) {
            vector[i] = vector[i] / magnitude;
          }
        }

        return vector;
      }

      async embedDocuments(texts: string[]): Promise<number[][]> {
        // Add minimal async operation to justify async
        await Promise.resolve();
        return texts.map(text => this.generateSimpleVector(text));
      }

      async embedQuery(text: string): Promise<number[]> {
        // Add minimal async operation to justify async
        await Promise.resolve();
        return this.generateSimpleVector(text);
      }
    }

    logger.info('Using fallback embeddings implementation. This is less accurate but allows the memory system to function.');
    return new FallbackEmbeddings();
  }

  /**
     * Adds a general memory entry (e.g., file chunk, web snippet) to the database and vector store.
     * @param memory The memory data, excluding id and timestamp.
     * @returns The created MemoryEntry with assigned id and timestamp.
     */
  public async addMemory(memory: Omit<MemoryEntry, 'id' | 'timestamp' | 'embedding'>): Promise<MemoryEntry> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();

    const id = `mem_${uuidv4()}`;
    const timestamp = Date.now();
    const contentString = typeof memory.content === 'string' ? memory.content : JSON.stringify(memory.content); // Ensure content is string for embedding

    if (!contentString || contentString.trim().length === 0) {
      logger.warn('Attempted to add memory with empty content. Skipping. Metadata:', memory.metadata);
      throw new Error('Cannot add memory with empty content.');
    }

    const newMemory: MemoryEntry = {
      id,
      content: memory.content, // Store original content (could be object)
      timestamp,
      metadata: memory.metadata ?? {
        source: 'unknown',
        type: 'unknown'
      },
      embedding: undefined, // Initialize embedding as undefined
    };

    try {
      logger.debug(`Adding memory entry ${id}...`);

      // 1. Generate Embedding
      if (this.embeddings) {
        // Use contentString for embedding generation
        const embedding = await this.embeddings.embedQuery(contentString);
        newMemory.embedding = embedding; // Store embedding within the entry object

        // 2. Store in Database (Store the complete MemoryEntry including the embedding)
        // Ensure the database adapter can handle the 'embedding' field (e.g., store as JSON/Blob or handle separately)
        if (this.database) {
          await this.database.addRecord(MEMORIES_COLLECTION, newMemory as unknown as Record<string, unknown>);
        }
        logger.debug(`Memory entry ${id} stored in database.`);

        // 3. Store in Vector Store
        // The vector store needs the ID, the vector, and searchable metadata.
        // Pass only relevant metadata for vector search filtering.
        const vectorMetadata = this.prepareVectorMetadata(newMemory.metadata);
        if (this.vectorStore) {
          await this.vectorStore.addVector(id, embedding, vectorMetadata as Record<string, string | number | boolean | undefined>);
        }
        logger.debug(`Memory entry ${id} added to vector store.`);

        this._onMemoriesChanged.fire();
        logger.info(`Successfully added memory entry ${id}.`);
        return newMemory;
      } else {
        throw new Error('Embeddings not initialized');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : '';
      logger.error(`Failed to add memory entry ${id}:`, { message: errorMessage, stack: errorStack });
      // Attempt cleanup if partial additions occurred (optional, depends on desired atomicity)
      // await this.database!.deleteRecord(MEMORIES_COLLECTION, id).catch(e => logger.warn(`Cleanup failed for DB entry ${id}: ${e.message}`));
      // await this.vectorStore!.deleteVector(id).catch(e => logger.warn(`Cleanup failed for VS entry ${id}: ${e.message}`));
      throw new Error(`Failed to add memory: ${errorMessage}`);
    }
  }

  /**
     * Prepares metadata specifically for the vector store, removing potentially large or irrelevant fields.
     */
  private prepareVectorMetadata(originalMetadata: Record<string, unknown>): Record<string, unknown> {
    const vectorMetadata: Record<string, unknown> = {};
    const allowedKeys = ['source', 'type', 'tags', 'filePath', 'fileName', 'extension', 'chunkIndex', 'chunkId', 'url', 'sessionId', 'userId']; // Add keys relevant for filtering
    const maxTagLength = 50; // Limit tag length
    const maxTags = 20; // Limit number of tags

    for (const key in originalMetadata) {
      if (allowedKeys.includes(key)) {
        const value = originalMetadata[key];
        if (key === 'tags' && Array.isArray(value)) {
          // Sanitize and limit tags
          vectorMetadata[key] = value
            .map(tag => String(tag).substring(0, maxTagLength))
            .slice(0, maxTags);
        } else if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
          // Only include simple types directly suitable for filtering
          vectorMetadata[key] = value;
        }
        // Add more specific handling if needed (e.g., date ranges)
      }
    }
    // Ensure essential fields for linking back are present if available
    if (originalMetadata.chunkId) vectorMetadata.chunkId = originalMetadata.chunkId;
    if (originalMetadata.sessionId) vectorMetadata.sessionId = originalMetadata.sessionId;

    return vectorMetadata;
  }


  /**
     * Retrieves all general memory entries from the database.
     * @param limit Max number of entries to retrieve.
     * @returns An array of MemoryEntry objects.
     */
  public async getMemories(limit?: number): Promise<MemoryEntry[]> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();
    try {
      const effectiveLimit = limit ?? getConfig<number>('memory.maxMemories', 1000);
      // Query database, potentially sorting by timestamp descending
      if (this.database) {
        const records = await this.database.queryRecords(
          MEMORIES_COLLECTION,
          {}, // Empty filter for all
          effectiveLimit,
          { timestamp: -1 } // Sort by timestamp descending
        );
        // Cast needed as queryRecords returns generic objects
        return records as unknown as MemoryEntry[];
      }
      return [];
    } catch (error) {
      logger.error('Failed to get memories:', error);
      return [];
    }
  }

  /**
     * Retrieves a specific memory entry by its ID from the database.
     * @param id The unique ID of the memory entry.
     * @returns The MemoryEntry or undefined if not found.
     */
  public async getMemory(id: string): Promise<MemoryEntry | undefined> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();
    try {
      if (this.database) {
        const record = await this.database.getRecord(MEMORIES_COLLECTION, id);
        return record as MemoryEntry | undefined; // Cast needed
      }
      return undefined;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      // Log expected "not found" errors differently?
      if (errorMessage.toLowerCase().includes('not found')) {
        logger.debug(`Memory entry ${id} not found in database.`);
      } else {
        logger.error(`Failed to get memory ${id}:`, error);
      }
      return undefined;
    }
  }

  /**
     * Deletes a memory entry by its ID from both the database and vector store.
     * @param id The unique ID of the memory entry.
     * @returns True if deletion was successful in both stores, false otherwise.
     */
  public async deleteMemory(id: string): Promise<boolean> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();
    let dbSuccess = false;
    let vsSuccess = false;
    try {
      logger.debug(`Attempting to delete memory entry ${id}...`);
      // Delete from database
      if (this.database) {
        dbSuccess = await this.database.deleteRecord(MEMORIES_COLLECTION, id);
      }
      logger.debug(`Database deletion result for ${id}: ${dbSuccess}`);

      // Delete from vector store
      if (this.vectorStore) {
        vsSuccess = await this.vectorStore.deleteVector(id);
      }
      logger.debug(`Vector store deletion result for ${id}: ${vsSuccess}`);

      if (dbSuccess || vsSuccess) { // Fire event if deleted from at least one place
        this._onMemoriesChanged.fire();
        logger.info(`Deletion result for memory ${id}: DB=${dbSuccess}, VS=${vsSuccess}`);
      } else {
        logger.warn(`Memory entry ${id} not found for deletion in either DB or VS.`);
      }
      // Return true only if deleted from both (or if it didn't exist in one initially)
      // A more nuanced return might be needed depending on strictness.
      // Let's consider it successful if the final state is "it's gone".
      return true; // Assume success if no errors thrown and attempts made.

    } catch (error) {
      logger.error(`Failed to delete memory ${id}:`, error);
      return false; // Explicit failure on error
    }
  }

  /**
     * Clears all general memory entries from the database and vector store.
     * Warning: This is a destructive operation.
     */
  public async clearMemories(): Promise<void> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();
    logger.warn('Clearing ALL general memory entries...');
    try {
      // Clear database collection
      if (this.database) {
        await this.database.clearCollection(MEMORIES_COLLECTION);
      }
      logger.debug(`Cleared database collection: ${MEMORIES_COLLECTION}`);

      // Clear vector store
      if (this.vectorStore) {
        await this.vectorStore.clearVectors();
      }
      logger.debug('Cleared vector store.');

      this._onMemoriesChanged.fire();
      logger.info('All general memory entries cleared successfully.');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('Failed to clear memories:', error);
      throw new Error(`Failed to clear memories: ${errorMessage}`);
    }
  }

  /**
     * Searches general memory entries based on metadata filters and optional text query using the database.
     * @param options Search criteria including filters and limit.
     * @returns An array of matching MemoryEntry objects.
     */
  public async searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();
    try {
      const { query, limit = 10, filter } = options;
      logger.debug('Searching memories with options:', { query: query ? 'present' : 'absent', limit, filter });

      // Build database query from filter
      const dbQuery = this.buildDbQuery(filter);

      // Add text search if query is provided and database supports it
      if (query && this.database?.supportsTextSearch) {
        // Adapt the text search syntax based on the specific database implementation
        // This might require changes in the IDatabase interface or specific adapters
        // Example for a MongoDB-like syntax:
        // dbQuery['$text'] = { $search: query };
        logger.warn('Text search query construction depends on the database implementation. Using placeholder logic.');
        // Placeholder: Assume metadata contains searchable text fields if no $text support
        // dbQuery['metadata.content_summary'] = { $regex: query, $options: 'i' }; // Example fallback
      } else if (query) {
        logger.warn(`Database (${getConfig<string>('memory.database', 'sqlite')}) does not support text search, or query was empty. Searching based on filters only.`);
      }


      // Execute query
      if (this.database) {
        const records = await this.database.queryRecords(
          MEMORIES_COLLECTION,
          dbQuery,
          limit,
          { timestamp: -1 } // Default sort
        );
        logger.debug(`Database query returned ${records.length} records.`);
        return records as unknown as MemoryEntry[]; // Cast needed
      }
      return [];
    } catch (error) {
      logger.error('Failed to search memories:', error);
      return [];
    }
  }

  /**
     * Searches memory entries by semantic similarity to a query string using the vector store.
     * @param query The query string.
     * @param options Optional search options including filters, limit, and relevance threshold.
     * @returns An array of MemoryEntry objects, sorted by relevance, potentially including relevance score in metadata.
     */
  public async searchSimilarMemories(query: string, options: Partial<MemorySearchOptions> = {}): Promise<MemoryEntry[]> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();

    const { limit = getConfig<number>('memory.contextWindowSize', 5), filter: metadataFilter, relevanceThreshold = getConfig<number>('memory.relevanceThreshold', 0.7) } = options;
    logger.debug(`Searching similar memories for query "${query.substring(0, 50)}..."`, { limit, metadataFilter, relevanceThreshold });

    try {
      if (this.embeddings && this.vectorStore) {
        // 1. Generate embedding for the query
        const queryEmbedding = await this.embeddings.embedQuery(query);

        // 2. Prepare vector store filter from MemoryFilter
        const vectorFilter = this.prepareVectorMetadata(metadataFilter ?? {}); // Reuse metadata prep logic

        // 3. Search vector store
        const results = await this.vectorStore.searchSimilarVectors(queryEmbedding, limit, vectorFilter as Record<string, string | number | boolean | undefined>);
        logger.debug(`Vector store search returned ${results.length} raw results.`);

        // 4. Filter by relevance threshold
        const relevantResults = results.filter(result => result.score >= relevanceThreshold);
        logger.debug(`Found ${relevantResults.length} results above relevance threshold ${relevanceThreshold}.`);

        if (relevantResults.length === 0) {
          return [];
        }

        // 5. Retrieve full memory entries from the database
        // Optimize by fetching multiple IDs at once if the database supports it
        const idsToFetch = relevantResults.map(r => r.id);
        const memoryMap = await this.getMemoriesByIds(idsToFetch);

        // 6. Combine results and add relevance score
        const finalMemories: MemoryEntry[] = [];
        for (const result of relevantResults) {
          const memory = memoryMap.get(result.id);
          if (memory) {
            finalMemories.push({
              ...memory,
              metadata: {
                ...memory.metadata,
                relevance: result.score, // Add relevance score
              },
            });
          } else {
            logger.warn(`Memory entry ${result.id} found in vector store but not in database.`);
          }
        }

        // Ensure sorting by relevance score (descending)
        finalMemories.sort((a, b) => Number(b.metadata.relevance ?? 0) - Number(a.metadata.relevance ?? 0));

        logger.info(`Returning ${finalMemories.length} similar memories for query.`);
        return finalMemories;
      }
      return [];
    } catch (error) {
      logger.error(`Failed to search similar memories for query "${query.substring(0, 50)}...":`, error);
      // Optional: Fall back to text search on error?
      // logger.warn("Falling back to text search due to similarity search error.");
      // return this.searchMemories({ query, limit, filter: metadataFilter });
      return []; // Return empty on error for now
    }
  }

  /**
     * Helper to fetch multiple memory entries by IDs efficiently.
     * @param ids Array of memory entry IDs.
     * @returns A Map where keys are IDs and values are MemoryEntry objects.
     */
  private async getMemoriesByIds(ids: string[]): Promise<Map<string, MemoryEntry>> {
    if (ids.length === 0) {
      return new Map();
    }
    await this.assertInitialized();
    try {
      // Use database's batch get method if available, otherwise loop (less efficient)
      if (this.database?.getRecordsByIds) {
        const records = await this.database.getRecordsByIds(MEMORIES_COLLECTION, ids);
        const map = new Map<string, MemoryEntry>();
        (records as unknown as MemoryEntry[]).forEach(record => map.set(record.id, record));
        return map;
      } else {
        // Fallback to individual gets
        logger.warn('Database does not support batch getRecordsByIds. Fetching IDs individually.');
        const map = new Map<string, MemoryEntry>();
        for (const id of ids) {
          const memory = await this.getMemory(id);
          if (memory) {
            map.set(id, memory);
          }
        }
        return map;
      }
    } catch (error) {
      logger.error(`Failed to fetch memories by IDs: ${ids.join(', ')}`, error);
      return new Map(); // Return empty map on error
    }
  }

  /**
     * Builds a database query object from the MemoryFilter.
     * Needs adaptation based on the specific database query language.
     */
  private buildDbQuery(filter?: MemoryFilter): Record<string, unknown> {
    const dbQuery: Record<string, unknown> = {};
    if (!filter) {
      return dbQuery;
    }

    // Example mapping (adjust based on IDatabase implementation details)
    if (filter.source) dbQuery['metadata.source'] = filter.source;
    if (filter.type) dbQuery['metadata.type'] = filter.type;
    if (filter.tags && filter.tags.length > 0) dbQuery['metadata.tags'] = { $all: filter.tags }; // Assumes MongoDB-like $all operator
    if (filter.fromTimestamp && filter.toTimestamp) {
      dbQuery.timestamp = { $gte: filter.fromTimestamp, $lte: filter.toTimestamp };
    } else if (filter.fromTimestamp) {
      dbQuery.timestamp = { $gte: filter.fromTimestamp };
    } else if (filter.toTimestamp) {
      dbQuery.timestamp = { $lte: filter.toTimestamp };
    }

    // Add custom filters (assuming they target metadata fields)
    for (const key in filter) {
      if (!['source', 'type', 'tags', 'fromTimestamp', 'toTimestamp'].includes(key)) {
        // Be cautious with direct key mapping - might need sanitization or specific handling
        dbQuery[`metadata.${key}`] = filter[key];
      }
    }
    return dbQuery;
  }

  /**
   * Updates an existing memory entry with the provided updates.
   * @param id The ID of the memory to update
   * @param updates The updates to apply to the memory
   * @returns The updated memory entry, or undefined if not found
   */
  public async updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();

    try {
      // Get the existing memory
      const existingMemory = await this.getMemory(id);
      if (!existingMemory) {
        logger.warn(`Cannot update memory: No memory found with ID ${id}`);
        return undefined;
      }

      // Create the updated memory object
      const updatedMemory: MemoryEntry = {
        ...existingMemory,
        ...updates,
        // Ensure these fields can't be updated
        id: existingMemory.id,
        timestamp: existingMemory.timestamp,
        // Merge metadata if it exists in updates
        metadata: {
          ...existingMemory.metadata,
          ...(updates.metadata ?? {}),
          // Update the lastUpdated timestamp
          lastUpdated: Date.now()
        }
      };

      // Update in the database
      let success = false;
      if (this.database) {
        success = await this.database.updateRecord(
          MEMORIES_COLLECTION,
          id,
          {
            content: updatedMemory.content,
            timestamp: updatedMemory.timestamp,
            metadata: updatedMemory.metadata
          }
        );
      }

      if (!success) {
        throw new Error(`Failed to update memory ${id} in database`);
      }

      // Update in the vector store if content changed
      if (this.vectorStore && (updates.content !== undefined || updates.metadata !== undefined)) {
        // 1. Delete the old vector
        await this.vectorStore.deleteVector(id);

        // 2. Generate a new embedding
        const contentString = typeof updatedMemory.content === 'string' ? updatedMemory.content : JSON.stringify(updatedMemory.content);
        if (this.embeddings) {
          const newEmbedding = await this.embeddings.embedQuery(contentString);
          updatedMemory.embedding = newEmbedding;

          // 3. Add the new vector
          const vectorMetadata = this.prepareVectorMetadata(updatedMemory.metadata);
          await this.vectorStore.addVector(id, newEmbedding, vectorMetadata as Record<string, string | number | boolean | undefined>);
        }
      }

      // Emit change event
      this._onMemoriesChanged.fire();

      logger.debug(`Updated memory with ID: ${id}`);
      return updatedMemory;
    } catch (error) {
      logger.error(`Failed to update memory ${id}:`, error);
      throw error;
    }
  }

  // --- Chat History Management ---

  /**
     * Adds a chat message to the history for a specific session.
     * @param sessionId Identifier for the conversation session.
     * @param message A HumanMessage or AIMessage object.
     */
  public async addChatMessage(sessionId: string, message: HumanMessage | AIMessage): Promise<void> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();

    if (!sessionId) throw new Error('sessionId cannot be empty.');

    // Convert LangChain message to a storable format
    const chatRecord: ChatMessage = {
      id: `chat_${uuidv4()}`,
      sessionId: sessionId,
      // Use instanceof for type checking BaseMessage subclasses
      type: message instanceof HumanMessage ? 'human' : message instanceof AIMessage ? 'ai' : 'unknown',
      content: typeof message.content === 'string' ? message.content : JSON.stringify(message.content), // Handle potential non-string content
      timestamp: Date.now(),
      metadata: message.additional_kwargs ?? {}, // Store additional arguments if present
    };

    try {
      logger.debug(`Adding chat message for session ${sessionId}...`);
      if (this.database) {
        await this.database.addRecord(CHAT_HISTORY_COLLECTION, chatRecord as unknown as Record<string, unknown>);
      }
      logger.debug(`Chat message ${chatRecord.id} added for session ${sessionId}.`);
      // Optionally fire a different event for chat history changes?
      // this._onChatHistoryChanged.fire(sessionId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to add chat message for session ${sessionId}:`, error);
      throw new Error(`Failed to add chat message: ${errorMessage}`);
    }
  }

  /**
     * Retrieves the chat history for a specific session.
     * @param sessionId Identifier for the conversation session.
     * @param limit Optional limit on the number of messages to retrieve (most recent).
     * @returns A ChatMessageHistory object populated with the messages.
     */
  public async getChatHistory(sessionId: string, limit?: number): Promise<ChatMessageHistory> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();

    if (!sessionId) throw new Error('sessionId cannot be empty.');

    const effectiveLimit = limit ?? getConfig<number>('memory.conversationHistorySize', 100);

    try {
      logger.debug(`Getting chat history for session ${sessionId} (limit: ${effectiveLimit})...`);
      if (this.database) {
        const records = await this.database.queryRecords(
          CHAT_HISTORY_COLLECTION,
          { sessionId: sessionId }, // Filter by session ID
          effectiveLimit,
          { timestamp: -1 } // Sort by timestamp descending (most recent first)
        );

        // Convert stored records back to LangChain BaseMessage objects
        const messages: BaseMessage[] = (records as unknown as ChatMessage[])
          .reverse() // Reverse to get chronological order (oldest first)
          .map(record => {
            const content = record.content; // Assume content is stored as string
            // We could use metadata for additional kwargs if needed
            if (record.type === 'human') {
              return new HumanMessage(content);
            } else if (record.type === 'ai') {
              return new AIMessage(content);
            } else {
              // Handle system messages or unknown types if necessary
              logger.warn(`Unknown chat message type "${record.type}" encountered for session ${sessionId}.`);
              // Could potentially return a SystemMessage or skip
              return new SystemMessage(`[Unknown Type: ${record.type}] ${content}`);
            }
          });

        logger.debug(`Retrieved ${messages.length} messages for session ${sessionId}.`);
        // Create a new history with the messages
        const history = new ChatMessageHistory();
        // Add messages to history
        for (const message of messages) {
          await history.addMessage(message);
        }
        return history;
      }
      return new ChatMessageHistory();
    } catch (error) {
      logger.error(`Failed to get chat history for session ${sessionId}:`, error);
      // Return empty history on error
      return new ChatMessageHistory();
    }
  }

  /**
     * Clears the chat history for a specific session.
     * @param sessionId Identifier for the conversation session.
     */
  public async clearChatHistory(sessionId: string): Promise<void> {
    if (this.context) {
      await this.initialize(this.context);
    }
    await this.assertInitialized();
    if (!sessionId) throw new Error('sessionId cannot be empty.');
    logger.warn(`Clearing chat history for session ${sessionId}...`);
    try {
      // Use database's deleteRecords if available, otherwise fall back to individual deletes
      if (this.database?.deleteRecords) {
        const deletedCount = await this.database.deleteRecords(CHAT_HISTORY_COLLECTION, { sessionId });
        logger.info(`Cleared ${deletedCount} chat messages for session ${sessionId}.`);
      } else if (this.database) {
        // Fallback: Get all records and delete them individually
        const records = await this.database.queryRecords(CHAT_HISTORY_COLLECTION, { sessionId });
        let deletedCount = 0;
        for (const record of records) {
          if (await this.database.deleteRecord(CHAT_HISTORY_COLLECTION, (record as { id: string }).id)) {
            deletedCount++;
          }
        }
        logger.info(`Cleared ${deletedCount} chat messages for session ${sessionId} (individual deletes).`);
      }
      // Optionally fire event
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to clear chat history for session ${sessionId}:`, error);
      throw new Error(`Failed to clear chat history: ${errorMessage}`);
    }
  }

  // --- LangChain Memory Component Factory Methods (Optional Conveniences) ---

  /**
     * Creates a BufferMemory instance pre-populated with history for a session.
     * @param sessionId The conversation session ID.
     * @param k The number of last messages to keep in the buffer (optional).
     * @param memoryKey The key to use for the memory variables (default: "history").
     * @returns A Promise resolving to a BufferMemory instance.
     */
  public async getBufferedMemory(sessionId: string, k?: number, memoryKey = 'history'): Promise<BufferMemory> {
    const chatHistory = await this.getChatHistory(sessionId);
    return new BufferMemory({
      chatHistory: chatHistory,
      memoryKey: memoryKey,
      k: k ?? getConfig<number>('memory.conversationHistorySize', 100), // Use configured size if k not provided
      returnMessages: true, // Typically want BaseMessage objects back
    });
  }

  /**
     * Creates a ConversationSummaryMemory instance for a session.
     * Requires a BaseLanguageModel instance for summarization.
     * @param sessionId The conversation session ID.
     * @param llm The language model instance to use for summarization.
     * @param memoryKey The key to use for the memory variables (default: "history").
     * @returns A Promise resolving to a ConversationSummaryMemory instance.
     */
  public async getSummarizedMemory(sessionId: string, llm: BaseLanguageModel, memoryKey = 'history'): Promise<ConversationSummaryMemory> {
    const chatHistory = await this.getChatHistory(sessionId);
    return new ConversationSummaryMemory({
      llm: llm,
      chatHistory: chatHistory,
      memoryKey: memoryKey,
      returnMessages: true,
    });
  }

  /**
     * Creates a VectorStoreRetrieverMemory instance using this provider's vector store.
     * Note: This is more about configuring memory *for use in a chain* rather than
     * just retrieving data. The chain would typically instantiate this.
     * @param memoryKey The key for memory variables.
     * @param inputKey The key for the input variable to the chain.
     * @returns A VectorStoreRetrieverMemory instance.
     */
  public async getVectorStoreRetrieverMemory(memoryKey = 'history', inputKey?: string): Promise<VectorStoreRetrieverMemory> {
    await this.assertInitialized();
    if (!this.vectorStore) {
      throw new Error('Vector store is not initialized.');
    }

    if (!(this.vectorStore instanceof MemoryVectorStore) && !this.vectorStore.asRetriever) {
      throw new Error('The configured vector store cannot be used directly as a retriever for VectorStoreRetrieverMemory. Use searchSimilarMemories instead or ensure the vector store implements \'asRetriever\'.');
    }

    // The vector store needs an `asRetriever` method or be compatible.
    // MemoryVectorStore has this. Others might need wrapping.
    if (!this.vectorStore.asRetriever) {
      throw new Error('Vector store does not implement asRetriever method.');
    }

    const retriever = this.vectorStore.asRetriever(getConfig<number>('memory.contextWindowSize', 5));

    return new VectorStoreRetrieverMemory({
      vectorStoreRetriever: retriever,
      memoryKey: memoryKey,
      inputKey: inputKey, // Optional input key
    });
  }


  // --- Settings Management ---

  /**
   * Retrieves the current memory settings from the configuration.
   */
  public getMemorySettings(): MemorySettings {
    // This structure should match the MemorySettings type defined in ../types
    // It reads directly from the config system.
    // Get the vector store settings
    const vectorStoreType = getConfig<string>('memory.vectorStore', 'chroma');
    const chromaSettings = {
      directory: getConfig<string>('memory.vectorStore.chroma.directory', './.codessa/chroma'),
      collectionName: getConfig<string>('memory.vectorStore.chroma.collectionName', 'codessa_memories')
    };
    const pineconeSettings = {
      apiKey: getConfig<string>('memory.vectorStore.pinecone.apiKey', ''),
      environment: getConfig<string>('memory.vectorStore.pinecone.environment', ''),
      indexName: getConfig<string>('memory.vectorStore.pinecone.indexName', 'codessa-memories')
    };

    // Get the database settings
    const databaseType = getConfig<string>('memory.database', 'sqlite');
    const sqliteSettings = {
      filename: getConfig<string>('memory.database.sqlite.filename', './.codessa/memory.db')
    };
    const mysqlSettings = {
      host: getConfig<string>('memory.database.mysql.host', 'localhost'),
      port: getConfig<number>('memory.database.mysql.port', 3306),
      user: getConfig<string>('memory.database.mysql.user', 'root'),
      password: getConfig<string>('memory.database.mysql.password', ''),
      database: getConfig<string>('memory.database.mysql.database', 'codessa'),
      table: getConfig<string>('memory.database.mysql.table', 'memories')
    };
    const postgresSettings = {
      connectionString: getConfig<string>('memory.database.postgres.connectionString', ''),
      schema: getConfig<string>('memory.database.postgres.schema', 'public')
    };
    const mongodbSettings = {
      connectionString: getConfig<string>('memory.database.mongodb.connectionString', ''),
      database: getConfig<string>('memory.database.mongodb.database', 'codessa'),
      collection: getConfig<string>('memory.database.mongodb.collection', 'memories')
    };
    const redisSettings = {
      url: getConfig<string>('memory.database.redis.url', ''),
      keyPrefix: getConfig<string>('memory.database.redis.keyPrefix', 'codessa:')
    };

    // Get the file chunking settings
    const fileChunkingSettings = {
      chunkSize: getConfig<number>('memory.fileChunking.chunkSize', 1000),
      chunkOverlap: getConfig<number>('memory.fileChunking.chunkOverlap', 200),
      maxChunksPerFile: getConfig<number>('memory.fileChunking.maxChunksPerFile', 100)
    };

    return {
      enabled: getConfig<boolean>('memory.enabled', true),
      system: getConfig<'basic' | 'codessa' | 'quantum'>('memory.system', 'codessa'),
      maxMemories: getConfig<number>('memory.maxMemories', 1000),
      maxMemoriesPerFile: getConfig<number>('phase4.maxMemoriesPerFile', 100),
      memoryRetentionDays: getConfig<number>('phase4.memoryRetentionDays', 30),
      relevanceThreshold: getConfig<number>('memory.relevanceThreshold', 0.7),
      contextWindowSize: getConfig<number>('memory.contextWindowSize', 5),
      conversationHistorySize: getConfig<number>('memory.conversationHistorySize', 100),
      enableQuantumMemory: getConfig<boolean>('phase4.enableQuantumMemory', true),
      enablePredictiveInsights: getConfig<boolean>('phase4.enablePredictiveInsights', true),
      enableMemoryVisualization: getConfig<boolean>('phase4.enableMemoryVisualization', true),
      enableCollaborativeMemory: getConfig<boolean>('phase4.enableCollaborativeMemory', true),
      vectorStore: vectorStoreType as 'memory' | 'chroma' | 'pinecone' | 'weaviate' | 'hnswlib',
      vectorStoreSettings: {
        chroma: chromaSettings,
        pinecone: pineconeSettings
      },
      database: databaseType as 'sqlite' | 'mysql' | 'postgres' | 'mongodb' | 'redis',
      databaseSettings: {
        sqlite: sqliteSettings,
        mysql: mysqlSettings,
        postgres: postgresSettings,
        mongodb: mongodbSettings,
        redis: redisSettings
      },
      fileChunking: fileChunkingSettings,
      cacheSize: getConfig<number>('memory.cacheSize', 1000),
      cacheExpiry: getConfig<number>('memory.cacheExpiry', 300000),
      batchSize: getConfig<number>('memory.batchSize', 50),
      maxConcurrentOperations: getConfig<number>('memory.maxConcurrentOperations', 10)
    };
  }

  /**
   * Updates specific memory settings in the configuration.
   * Handles potential re-initialization of components if critical settings change.
   * @param settings A partial MemorySettings object with values to update.
   */
  public async updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean> {
    logger.info('Updating memory settings:', settings);
    const currentSettings = this.getMemorySettings();
    let reinitializeVectorStore = false;
    let reinitializeDatabase = false;

    try {
      const updates: Promise<boolean>[] = [];

      // Helper to update config and track success
      const update = (key: string, value: unknown): void => {
        if (value !== undefined) {
          updates.push(setConfig(key, value));
        }
      };

      // Update individual settings
      update('memory.enabled', settings.enabled);
      update('memory.maxMemories', settings.maxMemories);
      update('memory.relevanceThreshold', settings.relevanceThreshold);
      update('memory.contextWindowSize', settings.contextWindowSize);
      update('memory.conversationHistorySize', settings.conversationHistorySize);

      // Check for changes requiring re-initialization
      if (settings.vectorStore !== undefined && settings.vectorStore !== currentSettings.vectorStore) {
        update('memory.vectorStore', settings.vectorStore);
        reinitializeVectorStore = true;
      }
      if (settings.database !== undefined && settings.database !== currentSettings.database) {
        update('memory.database', settings.database);
        reinitializeDatabase = true;
      }

      // Update nested settings (handle potential partial updates)
      if (settings.vectorStoreSettings) {
        const currentVSettings = getConfig<Record<string, unknown>>('memory.vectorStore', {});
        if (currentVSettings.chroma && settings.vectorStoreSettings.chroma) {
          update('memory.vectorStore.chroma.directory', settings.vectorStoreSettings.chroma.directory);
          update('memory.vectorStore.chroma.collectionName', settings.vectorStoreSettings.chroma.collectionName);
          if (settings.vectorStoreSettings.chroma.directory !== (currentVSettings.chroma as Record<string, unknown>).directory) {
            reinitializeVectorStore = true;
          }
        }
      }
      if (settings.databaseSettings) {
        const currentDbSettings = getConfig<Record<string, unknown>>('memory.database', {});
        if (currentDbSettings.sqlite && settings.databaseSettings.sqlite) {
          update('memory.database.sqlite.filename', settings.databaseSettings.sqlite.filename);
          if (settings.databaseSettings.sqlite.filename !== (currentDbSettings.sqlite as Record<string, unknown>).filename) {
            reinitializeDatabase = true;
          }
        }
      }
      if (settings.fileChunking) {
        update('memory.fileChunking.chunkSize', settings.fileChunking.chunkSize);
        update('memory.fileChunking.chunkOverlap', settings.fileChunking.chunkOverlap);
        update('memory.fileChunking.maxChunksPerFile', settings.fileChunking.maxChunksPerFile);
      }

      // Wait for all config updates to complete
      const results = await Promise.all(updates);
      if (results.some(result => !result)) {
        throw new Error('One or more configuration settings failed to update.');
      }

      // Perform re-initialization if needed
      if (reinitializeVectorStore || reinitializeDatabase) {
        logger.info('Reinitializing components due to critical setting changes');
        try {
          if (!this.context) {
            throw new Error('Extension context is not available');
          }
          await this.initialize(this.context);
          logger.info('Memory settings updated successfully');
          return true;
        } catch (error) {
          logger.error('Error reinitializing components:', error);
          return false;
        }
      }

      logger.info('Memory settings updated successfully');
      return true;
    } catch (error) {
      logger.error('Error updating memory settings:', error);
      return false;
    }
  }
}

// Export singleton instance
export const codessaMemoryProvider = new CodessaMemoryProvider();
