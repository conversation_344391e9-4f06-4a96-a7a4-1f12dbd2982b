"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiffTool = void 0;
const zod_1 = require("zod");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const diff = __importStar(require("diff"));
const cp = __importStar(require("child_process"));
const util = __importStar(require("util"));
/**
 * Advanced diff tool for comparing files and generating patches
 */
class DiffTool {
    id = 'diffTool';
    name = 'Diff Tool';
    description = 'Advanced diff tool for comparing files and generating patches with multiple algorithms.';
    type = 'multi-action';
    actions = {
        'compareFiles': {
            description: 'Compare two files and generate a diff',
            schema: zod_1.z.object({
                filePathA: zod_1.z.string().describe('Path to the first file.'),
                filePathB: zod_1.z.string().describe('Path to the second file.'),
                algorithm: zod_1.z.enum(['chars', 'words', 'lines', 'sentences', 'css', 'json']).optional().describe('Diff algorithm to use. Default is lines.'),
                format: zod_1.z.enum(['unified', 'line-by-line', 'json']).optional().describe('Output format. Default is unified.'),
                contextLines: zod_1.z.number().optional().describe('Number of context lines to include. Default is 3.')
            }),
            inputSchema: zod_1.z.object({
                filePathA: zod_1.z.string().describe('Path to the first file.'),
                filePathB: zod_1.z.string().describe('Path to the second file.'),
                algorithm: zod_1.z.enum(['chars', 'words', 'lines', 'sentences', 'css', 'json']).optional().describe('Diff algorithm to use. Default is lines.'),
                format: zod_1.z.enum(['unified', 'line-by-line', 'json']).optional().describe('Output format. Default is unified.'),
                contextLines: zod_1.z.number().optional().describe('Number of context lines to include. Default is 3.')
            })
        },
        'compareStrings': {
            description: 'Compare two strings and generate a diff',
            schema: zod_1.z.object({
                stringA: zod_1.z.string().describe('First string to compare.'),
                stringB: zod_1.z.string().describe('Second string to compare.'),
                algorithm: zod_1.z.enum(['chars', 'words', 'lines', 'sentences', 'css', 'json']).optional().describe('Diff algorithm to use. Default is lines.'),
                format: zod_1.z.enum(['unified', 'line-by-line', 'json']).optional().describe('Output format. Default is unified.')
            }),
            inputSchema: zod_1.z.object({
                stringA: zod_1.z.string().describe('First string to compare.'),
                stringB: zod_1.z.string().describe('Second string to compare.'),
                algorithm: zod_1.z.enum(['chars', 'words', 'lines', 'sentences', 'css', 'json']).optional().describe('Diff algorithm to use. Default is lines.'),
                format: zod_1.z.enum(['unified', 'line-by-line', 'json']).optional().describe('Output format. Default is unified.')
            })
        },
        'createPatch': {
            description: 'Create a patch file from two files',
            schema: zod_1.z.object({
                filePathA: zod_1.z.string().describe('Path to the original file.'),
                filePathB: zod_1.z.string().describe('Path to the modified file.'),
                outputPath: zod_1.z.string().optional().describe('Path to save the patch file. If not provided, the patch content will be returned.'),
                contextLines: zod_1.z.number().optional().describe('Number of context lines to include. Default is 3.')
            }),
            inputSchema: zod_1.z.object({
                filePathA: zod_1.z.string().describe('Path to the original file.'),
                filePathB: zod_1.z.string().describe('Path to the modified file.'),
                outputPath: zod_1.z.string().optional().describe('Path to save the patch file. If not provided, the patch content will be returned.'),
                contextLines: zod_1.z.number().optional().describe('Number of context lines to include. Default is 3.')
            })
        },
        'applyPatch': {
            description: 'Apply a patch to a file',
            schema: zod_1.z.object({
                filePath: zod_1.z.string().describe('Path to the file to patch.'),
                patchPath: zod_1.z.string().describe('Path to the patch file.'),
                outputPath: zod_1.z.string().optional().describe('Path to save the patched file. If not provided, the original file will be modified.'),
                createBackup: zod_1.z.boolean().optional().describe('Whether to create a backup before patching. Default is true.')
            }),
            inputSchema: zod_1.z.object({
                filePath: zod_1.z.string().describe('Path to the file to patch.'),
                patchPath: zod_1.z.string().describe('Path to the patch file.'),
                outputPath: zod_1.z.string().optional().describe('Path to save the patched file. If not provided, the original file will be modified.'),
                createBackup: zod_1.z.boolean().optional().describe('Whether to create a backup before patching. Default is true.')
            })
        },
        'gitDiff': {
            description: 'Use git diff to compare files or directories',
            schema: zod_1.z.object({
                pathA: zod_1.z.string().describe('Path to the first file or directory.'),
                pathB: zod_1.z.string().optional().describe('Path to the second file or directory. If not provided, will compare with the working directory.'),
                options: zod_1.z.string().optional().describe('Additional git diff options.'),
                format: zod_1.z.enum(['normal', 'json', 'stat']).optional().describe('Output format. Default is normal.')
            }),
            inputSchema: zod_1.z.object({
                pathA: zod_1.z.string().describe('Path to the first file or directory.'),
                pathB: zod_1.z.string().optional().describe('Path to the second file or directory. If not provided, will compare with the working directory.'),
                options: zod_1.z.string().optional().describe('Additional git diff options.'),
                format: zod_1.z.enum(['normal', 'json', 'stat']).optional().describe('Output format. Default is normal.')
            })
        },
        'semanticDiff': {
            description: 'Generate a semantic diff that understands code structure',
            schema: zod_1.z.object({
                filePathA: zod_1.z.string().describe('Path to the first file.'),
                filePathB: zod_1.z.string().describe('Path to the second file.'),
                language: zod_1.z.string().optional().describe('Programming language of the files. If not provided, will be inferred from file extension.')
            }),
            inputSchema: zod_1.z.object({
                filePathA: zod_1.z.string().describe('Path to the first file.'),
                filePathB: zod_1.z.string().describe('Path to the second file.'),
                language: zod_1.z.string().optional().describe('Programming language of the files. If not provided, will be inferred from file extension.')
            })
        }
    };
    execPromise = util.promisify(cp.exec);
    // Backup management
    static backups = new Map();
    static BACKUP_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours
    async execute(actionName, input, _context) {
        try {
            // Check if the action exists in the actions object
            if (!actionName || !Object.prototype.hasOwnProperty.call(this.actions, actionName)) {
                return {
                    success: false,
                    error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,
                    toolId: this.id,
                    actionName
                };
            }
            switch (actionName) {
                case 'compareFiles':
                    return this.executeCompareFiles(input, actionName);
                case 'compareStrings':
                    return this.executeCompareStrings(input, actionName);
                case 'createPatch':
                    return this.executeCreatePatch(input, actionName);
                case 'applyPatch':
                    return this.executeApplyPatch(input, actionName);
                case 'gitDiff':
                    return this.executeGitDiff(input, actionName);
                case 'semanticDiff':
                    return this.executeSemanticDiff(input, actionName);
                default:
                    return {
                        success: false,
                        error: `Unknown action: ${actionName}`,
                        toolId: this.id,
                        actionName
                    };
            }
        }
        catch (error) {
            return {
                success: false,
                error: `Diff operation failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    /**
       * Create a backup of a file before editing
       */
    async createBackup(filePath) {
        try {
            // Clean up old backups
            this.cleanupOldBackups();
            // Read the file content
            const content = await fs.promises.readFile(filePath, 'utf8');
            // Store the backup
            DiffTool.backups.set(filePath, {
                content,
                timestamp: Date.now()
            });
        }
        catch (error) {
            throw new Error(`Failed to create backup of ${filePath}: ${error}`);
        }
    }
    /**
       * Restore a file from backup
       */
    async restoreFromBackup(filePath) {
        try {
            const backup = DiffTool.backups.get(filePath);
            if (!backup) {
                return false;
            }
            // Write the backup content back to the file
            await fs.promises.writeFile(filePath, backup.content, 'utf8');
            return true;
        }
        catch (error) {
            throw new Error(`Failed to restore ${filePath} from backup: ${error}`);
        }
    }
    /**
       * Clean up old backups
       */
    cleanupOldBackups() {
        const now = Date.now();
        // Convert entries() iterator to array before iterating
        const entries = Array.from(DiffTool.backups.entries());
        for (const [filePath, backup] of entries) {
            if (now - backup.timestamp > DiffTool.BACKUP_EXPIRY_MS) {
                DiffTool.backups.delete(filePath);
            }
        }
    }
    async executeCompareFiles(input, actionName) {
        try {
            const filePathA = input.filePathA;
            const filePathB = input.filePathB;
            const algorithm = input.algorithm || 'lines';
            const format = input.format || 'unified';
            const contextLines = input.contextLines || 3;
            if (!filePathA) {
                return {
                    success: false,
                    error: '\'filePathA\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            if (!filePathB) {
                return {
                    success: false,
                    error: '\'filePathB\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            // Validate files exist
            if (!fs.existsSync(filePathA)) {
                return {
                    success: false,
                    error: `File not found: ${filePathA}`,
                    toolId: this.id,
                    actionName
                };
            }
            if (!fs.existsSync(filePathB)) {
                return {
                    success: false,
                    error: `File not found: ${filePathB}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Read file contents
            const contentA = await fs.promises.readFile(filePathA, 'utf8');
            const contentB = await fs.promises.readFile(filePathB, 'utf8');
            // Generate diff
            let diffResult;
            if (format === 'unified') {
                // Use type assertion to handle missing type definitions in diff library
                diffResult = diff.createPatch(path.basename(filePathB), contentA, contentB, path.basename(filePathA), path.basename(filePathB), contextLines);
            }
            else if (format === 'line-by-line') {
                const changes = this.getDiffByAlgorithm(contentA, contentB, algorithm);
                diffResult = this.formatLineByLineDiff(changes);
            }
            else if (format === 'json') {
                const changes = this.getDiffByAlgorithm(contentA, contentB, algorithm);
                diffResult = changes;
            }
            else {
                return {
                    success: false,
                    error: `Unsupported format: ${format}`,
                    toolId: this.id,
                    actionName
                };
            }
            return {
                success: true,
                output: {
                    diff: diffResult,
                    fileA: {
                        path: filePathA,
                        size: contentA.length,
                        lines: contentA.split('\n').length
                    },
                    fileB: {
                        path: filePathB,
                        size: contentB.length,
                        lines: contentB.split('\n').length
                    },
                    algorithm,
                    format
                },
                toolId: this.id,
                actionName,
                metadata: {
                    filePathA,
                    filePathB,
                    algorithm,
                    format
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Compare files failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async executeCompareStrings(input, actionName) {
        try {
            const stringA = input.stringA;
            const stringB = input.stringB;
            const algorithm = input.algorithm || 'lines';
            const format = input.format || 'unified';
            if (stringA === undefined) {
                return {
                    success: false,
                    error: '\'stringA\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            if (stringB === undefined) {
                return {
                    success: false,
                    error: '\'stringB\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            // Generate diff
            let diffResult;
            if (format === 'unified') {
                // Use type assertion to handle missing type definitions in diff library
                diffResult = diff.createPatch('string', stringA, stringB, 'original', 'modified');
            }
            else if (format === 'line-by-line') {
                const changes = this.getDiffByAlgorithm(stringA, stringB, algorithm);
                diffResult = this.formatLineByLineDiff(changes);
            }
            else if (format === 'json') {
                const changes = this.getDiffByAlgorithm(stringA, stringB, algorithm);
                diffResult = changes;
            }
            else {
                return {
                    success: false,
                    error: `Unsupported format: ${format}`,
                    toolId: this.id,
                    actionName
                };
            }
            return {
                success: true,
                output: {
                    diff: diffResult,
                    algorithm,
                    format,
                    stats: {
                        lengthA: stringA.length,
                        lengthB: stringB.length,
                        linesA: stringA.split('\n').length,
                        linesB: stringB.split('\n').length
                    }
                },
                toolId: this.id,
                actionName,
                metadata: {
                    algorithm,
                    format
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Compare strings failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async executeCreatePatch(input, actionName) {
        try {
            const filePathA = input.filePathA;
            const filePathB = input.filePathB;
            const outputPath = input.outputPath;
            const contextLines = input.contextLines || 3;
            if (!filePathA) {
                return {
                    success: false,
                    error: '\'filePathA\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            if (!filePathB) {
                return {
                    success: false,
                    error: '\'filePathB\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            // Validate files exist
            if (!fs.existsSync(filePathA)) {
                return {
                    success: false,
                    error: `File not found: ${filePathA}`,
                    toolId: this.id,
                    actionName
                };
            }
            if (!fs.existsSync(filePathB)) {
                return {
                    success: false,
                    error: `File not found: ${filePathB}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Read file contents
            const contentA = await fs.promises.readFile(filePathA, 'utf8');
            const contentB = await fs.promises.readFile(filePathB, 'utf8');
            // Generate patch
            // Use type assertion to handle missing type definitions in diff library
            const patchContent = diff.createPatch(path.basename(filePathB), contentA, contentB, path.basename(filePathA), path.basename(filePathB), contextLines);
            // Save patch if outputPath is provided
            if (outputPath) {
                await fs.promises.writeFile(outputPath, patchContent, 'utf8');
            }
            return {
                success: true,
                output: {
                    patch: patchContent,
                    outputPath: outputPath || null,
                    stats: {
                        fileA: {
                            path: filePathA,
                            size: contentA.length,
                            lines: contentA.split('\n').length
                        },
                        fileB: {
                            path: filePathB,
                            size: contentB.length,
                            lines: contentB.split('\n').length
                        }
                    }
                },
                toolId: this.id,
                actionName,
                metadata: {
                    filePathA,
                    filePathB,
                    outputPath
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Create patch failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async executeApplyPatch(input, actionName) {
        try {
            const filePath = input.filePath;
            const patchPath = input.patchPath;
            const outputPath = input.outputPath;
            const createBackup = input.createBackup !== false; // Default to true
            if (!filePath) {
                return {
                    success: false,
                    error: '\'filePath\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            if (!patchPath) {
                return {
                    success: false,
                    error: '\'patchPath\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            // Validate files exist
            if (!fs.existsSync(filePath)) {
                return {
                    success: false,
                    error: `File not found: ${filePath}`,
                    toolId: this.id,
                    actionName
                };
            }
            if (!fs.existsSync(patchPath)) {
                return {
                    success: false,
                    error: `Patch file not found: ${patchPath}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Read file contents
            const fileContent = await fs.promises.readFile(filePath, 'utf8');
            const patchContent = await fs.promises.readFile(patchPath, 'utf8');
            // Create backup if requested
            if (createBackup) {
                await this.createBackup(filePath);
            }
            try {
                // Parse and apply patch
                const patches = diff.parsePatch(patchContent);
                const patchedContent = diff.applyPatch(fileContent, patches);
                if (patchedContent === false) {
                    // Restore from backup if patching failed
                    if (createBackup) {
                        await this.restoreFromBackup(filePath);
                    }
                    return {
                        success: false,
                        error: 'Failed to apply patch. The patch may not be applicable to the current file state.',
                        toolId: this.id,
                        actionName
                    };
                }
                // Write the patched content
                if (outputPath) {
                    await fs.promises.writeFile(outputPath, patchedContent, 'utf8');
                }
                else {
                    await fs.promises.writeFile(filePath, patchedContent, 'utf8');
                }
                return {
                    success: true,
                    output: {
                        message: `Successfully applied patch to ${outputPath || filePath}`,
                        backupCreated: createBackup
                    },
                    toolId: this.id,
                    actionName,
                    metadata: {
                        filePath,
                        patchPath,
                        outputPath
                    }
                };
            }
            catch (error) {
                // Restore from backup if patching failed
                if (createBackup) {
                    await this.restoreFromBackup(filePath);
                }
                throw error;
            }
        }
        catch (error) {
            return {
                success: false,
                error: `Apply patch failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async executeGitDiff(input, actionName) {
        try {
            const pathA = input.pathA;
            const pathB = input.pathB;
            const options = input.options || '';
            const format = input.format || 'normal';
            if (!pathA) {
                return {
                    success: false,
                    error: '\'pathA\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            // Construct git diff command
            let command = 'git diff';
            if (format === 'json') {
                command += ' --no-color --output-indicator-new="+" --output-indicator-old="-" --output-indicator-context=" "';
            }
            else if (format === 'stat') {
                command += ' --stat';
            }
            if (options) {
                command += ` ${options}`;
            }
            if (pathB) {
                command += ` "${pathA}" "${pathB}"`;
            }
            else {
                command += ` "${pathA}"`;
            }
            try {
                // Execute git diff command
                const { stdout, stderr } = await this.execPromise(command);
                if (stderr) {
                    return {
                        success: false,
                        error: `Git diff error: ${stderr}`,
                        toolId: this.id,
                        actionName
                    };
                }
                let result = stdout;
                // Parse JSON output if requested
                if (format === 'json' && stdout.trim()) {
                    try {
                        const lines = stdout.split('\n');
                        const changes = [];
                        for (const line of lines) {
                            if (line.length > 0) {
                                const type = line[0] === '+' ? 'added' : line[0] === '-' ? 'removed' : 'unchanged';
                                changes.push({
                                    type,
                                    content: line.substring(1)
                                });
                            }
                        }
                        result = changes;
                    }
                    catch (parseError) {
                        // Fall back to raw output if parsing fails
                        result = stdout;
                    }
                }
                return {
                    success: true,
                    output: {
                        diff: result,
                        format,
                        command
                    },
                    toolId: this.id,
                    actionName,
                    metadata: {
                        pathA,
                        pathB,
                        format
                    }
                };
            }
            catch (execError) {
                return {
                    success: false,
                    error: `Git diff execution failed: ${execError.message || execError}`,
                    toolId: this.id,
                    actionName
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: `Git diff failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async executeSemanticDiff(input, actionName) {
        try {
            const filePathA = input.filePathA;
            const filePathB = input.filePathB;
            let language = input.language;
            if (!filePathA) {
                return {
                    success: false,
                    error: '\'filePathA\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            if (!filePathB) {
                return {
                    success: false,
                    error: '\'filePathB\' is required.',
                    toolId: this.id,
                    actionName
                };
            }
            // Validate files exist
            if (!fs.existsSync(filePathA)) {
                return {
                    success: false,
                    error: `File not found: ${filePathA}`,
                    toolId: this.id,
                    actionName
                };
            }
            if (!fs.existsSync(filePathB)) {
                return {
                    success: false,
                    error: `File not found: ${filePathB}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Infer language from file extension if not provided
            if (!language) {
                const ext = path.extname(filePathA).toLowerCase();
                language = this.getLanguageFromExtension(ext);
            }
            // Read file contents
            const contentA = await fs.promises.readFile(filePathA, 'utf8');
            const contentB = await fs.promises.readFile(filePathB, 'utf8');
            // Generate semantic diff
            const semanticDiff = await this.generateSemanticDiff(contentA, contentB, language);
            return {
                success: true,
                output: {
                    diff: semanticDiff,
                    language,
                    fileA: {
                        path: filePathA,
                        size: contentA.length,
                        lines: contentA.split('\n').length
                    },
                    fileB: {
                        path: filePathB,
                        size: contentB.length,
                        lines: contentB.split('\n').length
                    }
                },
                toolId: this.id,
                actionName,
                metadata: {
                    filePathA,
                    filePathB,
                    language
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Semantic diff failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    /**
       * Get diff based on the specified algorithm
       */
    getDiffByAlgorithm(oldStr, newStr, algorithm) {
        // Use type assertion to handle missing type definitions in diff library
        const diffLib = diff;
        switch (algorithm) {
            case 'chars':
                return diffLib.diffChars(oldStr, newStr);
            case 'words':
                return diffLib.diffWords(oldStr, newStr);
            case 'lines':
                return diffLib.diffLines(oldStr, newStr);
            case 'sentences':
                return diffLib.diffSentences(oldStr, newStr);
            case 'css':
                return diffLib.diffCss(oldStr, newStr);
            case 'json':
                try {
                    const oldObj = JSON.parse(oldStr);
                    const newObj = JSON.parse(newStr);
                    return diffLib.diffJson(oldObj, newObj);
                }
                catch (error) {
                    // Fall back to line diff if JSON parsing fails
                    return diffLib.diffLines(oldStr, newStr);
                }
            default:
                return diffLib.diffLines(oldStr, newStr);
        }
    }
    /**
       * Format diff changes as line-by-line output
       */
    formatLineByLineDiff(changes) {
        let result = '';
        changes.forEach((change) => {
            const prefix = change.added ? '+' : change.removed ? '-' : ' ';
            const lines = change.value.split('\n').filter((line) => line.length > 0 || !change.added && !change.removed);
            for (const line of lines) {
                result += `${prefix} ${line}\n`;
            }
        });
        return result;
    }
    /**
       * Get language from file extension
       */
    getLanguageFromExtension(ext) {
        const extensionMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.c': 'c',
            '.cpp': 'cpp',
            '.cs': 'csharp',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.swift': 'swift',
            '.rs': 'rust',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.md': 'markdown',
            '.sh': 'shell'
        };
        return extensionMap[ext] || 'text';
    }
    /**
       * Generate a semantic diff that understands code structure
       */
    async generateSemanticDiff(oldContent, newContent, language) {
        // First, generate a standard line diff
        // Use type assertion to handle missing type definitions in diff library
        const lineDiff = diff.diffLines(oldContent, newContent);
        // Analyze the changes based on language-specific patterns
        const semanticChanges = this.analyzeSemanticChanges(lineDiff, language);
        return {
            changes: semanticChanges,
            summary: this.generateSemanticDiffSummary(semanticChanges)
        };
    }
    /**
       * Analyze semantic changes based on language-specific patterns
       */
    analyzeSemanticChanges(changes, language) {
        const semanticChanges = [];
        let lineNumber = 1;
        for (const change of changes) {
            if (change.added || change.removed) {
                const lines = change.value.split('\n');
                const changeType = change.added ? 'added' : 'removed';
                // Analyze the change based on language-specific patterns
                const semanticInfo = this.analyzeCodeChange(lines, language, changeType);
                semanticChanges.push({
                    type: changeType,
                    lineStart: lineNumber,
                    lineEnd: lineNumber + lines.length - 1,
                    content: change.value,
                    semanticInfo
                });
            }
            if (!change.added) {
                lineNumber += change.value.split('\n').length;
            }
        }
        return semanticChanges;
    }
    /**
       * Analyze code changes based on language-specific patterns
       */
    analyzeCodeChange(lines, language, changeType) {
        const info = {
            entityType: 'unknown',
            name: '',
            details: {}
        };
        // Join lines for pattern matching
        const code = lines.join('\n');
        // Detect common code patterns based on language
        switch (language) {
            case 'javascript':
            case 'typescript':
                // Function or method
                const functionMatch = code.match(/(?:function\s+(\w+)|(\w+)\s*=\s*function|\(.*\)\s*=>\s*{|\s*(\w+)\s*\(.*\)\s*{)/);
                if (functionMatch) {
                    info.entityType = 'function';
                    info.name = functionMatch[1] || functionMatch[2] || functionMatch[3] || 'anonymous';
                    break;
                }
                // Class
                const classMatch = code.match(/class\s+(\w+)/);
                if (classMatch) {
                    info.entityType = 'class';
                    info.name = classMatch[1];
                    break;
                }
                // Variable
                const varMatch = code.match(/(?:const|let|var)\s+(\w+)\s*=/);
                if (varMatch) {
                    info.entityType = 'variable';
                    info.name = varMatch[1];
                    break;
                }
                // Import
                const importMatch = code.match(/import\s+.*\s+from\s+['"]([^'"]+)['"]/);
                if (importMatch) {
                    info.entityType = 'import';
                    info.name = importMatch[1];
                    break;
                }
                break;
            case 'python':
                // Function
                const pyFunctionMatch = code.match(/def\s+(\w+)\s*\(/);
                if (pyFunctionMatch) {
                    info.entityType = 'function';
                    info.name = pyFunctionMatch[1];
                    break;
                }
                // Class
                const pyClassMatch = code.match(/class\s+(\w+)/);
                if (pyClassMatch) {
                    info.entityType = 'class';
                    info.name = pyClassMatch[1];
                    break;
                }
                // Import
                const pyImportMatch = code.match(/(?:import|from)\s+(\w+)/);
                if (pyImportMatch) {
                    info.entityType = 'import';
                    info.name = pyImportMatch[1];
                    break;
                }
                break;
            // Add more languages as needed
        }
        return info;
    }
    /**
       * Generate a summary of semantic changes
       */
    generateSemanticDiffSummary(changes) {
        const counts = {};
        const entityCounts = {};
        for (const change of changes) {
            counts[change.type] = (counts[change.type] || 0) + 1;
            if (change.semanticInfo && change.semanticInfo.entityType !== 'unknown') {
                const key = `${change.type}_${change.semanticInfo.entityType}`;
                entityCounts[key] = (entityCounts[key] || 0) + 1;
            }
        }
        let summary = `Found ${changes.length} semantic changes:\n`;
        if (counts.added) {
            summary += `- ${counts.added} addition(s)\n`;
        }
        if (counts.removed) {
            summary += `- ${counts.removed} removal(s)\n`;
        }
        if (Object.keys(entityCounts).length > 0) {
            summary += '\nEntity changes:\n';
            for (const [key, count] of Object.entries(entityCounts)) {
                const [type, entity] = key.split('_');
                summary += `- ${count} ${entity}(s) ${type}\n`;
            }
        }
        return summary;
    }
}
exports.DiffTool = DiffTool;
//# sourceMappingURL=advancedDiffTool.js.map