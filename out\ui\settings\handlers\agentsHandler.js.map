{"version": 3, "file": "agentsHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/agentsHandler.ts"], "names": [], "mappings": ";;AACA,kDA8DC;AA/DD,6CAA6C;AAC7C,SAAgB,mBAAmB,CAAC,OAAY,EAAE,KAAU;IAC1D,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IACxE,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtJ,IAAI,CAAC;QACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;gBAChC,MAAM;YACR,CAAC;YACD,KAAK,UAAU,CAAC,CAAC,CAAC;gBAChB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC5B,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;oBAC/D,QAAQ,CAAC,KAAK,GAAG,gCAAgC,CAAC;oBAClD,MAAM;gBACR,CAAC;gBACD,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,QAAQ,GAAG,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC9H,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC;gBACtB,MAAM;YACR,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC5B,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;oBACxB,QAAQ,CAAC,KAAK,GAAG,oBAAoB,CAAC;oBACtC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;gBACrE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,kBAAkB,CAAC;oBACpC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC;gBAC7D,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACrC,MAAM;YACR,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,oBAAoB,CAAC;oBACtC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC/D,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,kBAAkB,CAAC;oBACpC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC/B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC3C,CAAC;QACD,CAAC;IACH,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for Agents CRUD messages and logic\nexport function handleAgentsMessage(message: any, panel: any) {\n  const settings = (window as any).settings || {};\n  settings.agents = Array.isArray(settings.agents) ? settings.agents : [];\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: message.type, success: false, data: null, error: null };\n  try {\n    switch (message.type) {\n    case 'getAgents': {\n      response.success = true;\n      response.data = settings.agents;\n      break;\n    }\n    case 'addAgent': {\n      const agent = message.agent;\n      if (!agent || !agent.name || !agent.provider || !agent.modelId) {\n        response.error = 'Missing required agent fields.';\n        break;\n      }\n      agent.id = agent.id || (agent.name + '-' + agent.provider + '-' + agent.modelId).replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();\n      settings.agents.push(agent);\n      response.success = true;\n      response.data = agent;\n      break;\n    }\n    case 'editAgent': {\n      const agent = message.agent;\n      if (!agent || !agent.id) {\n        response.error = 'Agent id required.';\n        break;\n      }\n      const idx = settings.agents.findIndex((a: any) => a.id === agent.id);\n      if (idx === -1) {\n        response.error = 'Agent not found.';\n        break;\n      }\n      settings.agents[idx] = { ...settings.agents[idx], ...agent };\n      response.success = true;\n      response.data = settings.agents[idx];\n      break;\n    }\n    case 'deleteAgent': {\n      const id = message.id;\n      if (!id) {\n        response.error = 'Agent id required.';\n        break;\n      }\n      const idx = settings.agents.findIndex((a: any) => a.id === id);\n      if (idx === -1) {\n        response.error = 'Agent not found.';\n        break;\n      }\n      settings.agents.splice(idx, 1);\n      response.success = true;\n      break;\n    }\n    default: {\n      response.error = 'Unknown message type.';\n    }\n    }\n  } catch (err: any) {\n    response.error = err?.message || String(err);\n  }\n  panel.postMessage(response);\n}\n\n"]}