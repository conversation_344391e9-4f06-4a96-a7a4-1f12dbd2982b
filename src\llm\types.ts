export interface LLMConfig {
    provider: string;
    modelId: string;
    temperature?: number;
    maxTokens?: number;
    apiKey?: string;
    endpoint?: string;
}

export interface LLMGenerateParams {
    prompt: string;
    systemPrompt?: string;
    modelId?: string;
    temperature?: number;
    maxTokens?: number;
    mode: 'chat' | 'task' | 'edit' | 'generate' | 'inline';
    tools?: any[];
    context?: any;
    history?: Array<{
        role: string;
        content: string;
        name?: string;
        tool_call_id?: string;
    }>;
    stopSequences?: string[];
    options?: Record<string, any>;
}

export interface LLMToolCall {
    toolId?: string;  // For backward compatibility
    name?: string;    // For backward compatibility
    args?: Record<string, any>;  // For backward compatibility
    arguments?: Record<string, any>;  // For backward compatibility
}

export interface LLMGenerateResult {
    content?: string;
    toolCall?: LLMToolCall;
    toolCalls?: LLMToolCall[];
    toolCallRequest?: LLMToolCall;
    error?: string;
    finishReason?: string;
    usage?: {
        promptTokens?: number;
        completionTokens?: number;
        totalTokens?: number;
    };
}

export interface LLMProvider {
    generate(params: LLMGenerateParams): Promise<LLMGenerateResult>;
    getModels(): Promise<string[]>;
    validateConfig(_config: any): boolean;
} 