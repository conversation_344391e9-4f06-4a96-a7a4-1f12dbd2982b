/**
 * Quantum Memory System - Revolutionary memory architecture
 * 
 * Implements temporal memory, contextual retrieval, collaborative memory,
 * predictive memory, and memory analytics while integrating with existing
 * Codessa memory infrastructure.
 */

import * as vscode from 'vscode';
import { SupervisorAgent } from '../../agents/agentTypes/supervisorAgent';
import { logger } from '../../logger';
import {
  MemoryEntry,
  MemorySearchOptions,
  MemorySettings,
  SharedMemoryItem,
  MemoryAccessLog
} from '../types';

/**
 * Interface for VectorMemoryManager to avoid circular dependencies
 */
interface IVectorMemoryManagerForQuantum {
  searchSimilarMemories(query: string, limit?: number): Promise<MemoryEntry[]>;
  addMemory(entry: MemoryEntry): Promise<MemoryEntry>;
  getMemories(): Promise<MemoryEntry[]>;
  onMemoriesChanged(callback: () => void): void;
}

/**
 * Interface defining the contract between MemoryManager and QuantumMemorySystem
 * This breaks the circular dependency by only exposing required methods
 */
export interface IMemoryManagerAdapter {
  // Core memory operations
  addMemory(contentOrEntry: string | Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry>;
  getMemory(id: string): Promise<MemoryEntry | undefined>;
  getMemories(): Promise<MemoryEntry[]>;
  deleteMemory(id: string): Promise<boolean>;
  clearMemories(): Promise<void>;
  searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]>;
  searchSimilarMemories(query: string, options?: Partial<MemorySearchOptions>): Promise<MemoryEntry[]>;

  // Memory settings and configuration
  getMemorySettings(): MemorySettings;
  updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean>;

  // Advanced operations
  processMessage(message: string): Promise<string>;
  chunkFile(filePath: string): Promise<MemoryEntry[]>;
  chunkWorkspace(
    folderPath: string,
    includePatterns?: string[],
    excludePatterns?: string[]
  ): Promise<MemoryEntry[]>;

  // Cross-agent memory sharing
  shareMemoryWithAgent(
    memoryId: string,
    fromAgentId: string,
    toAgentId: string,
    accessLevel?: 'read' | 'write' | 'admin',
    expiresIn?: number
  ): Promise<boolean>;
  getSharedMemoriesForAgent(agentId: string): SharedMemoryItem[];
  revokeSharedMemory(memoryId: string, fromAgentId: string, toAgentId: string): Promise<boolean>;
  searchSharedMemories(agentId: string, query: string, limit?: number): Promise<SharedMemoryItem[]>;
  getMemoryAccessLog(agentId: string, limit?: number): MemoryAccessLog[];
  getMemoryAccessStats(): {
    totalAccesses: number;
    successfulAccesses: number;
    failedAccesses: number;
    agentStats: Map<string, { reads: number, writes: number, shares: number, revokes: number }>;
  };

  // Event handling
  onMemoriesChanged: vscode.Event<void>;
}

export interface TemporalMemoryEntry {
  id: string;
  content: string;
  timestamp: number;
  version: number;
  previousVersionId?: string;
  nextVersionId?: string;
  changeType: 'create' | 'update' | 'delete' | 'refactor';
  context: {
    filePath: string;
    lineNumber?: number;
    functionName?: string;
    className?: string;
    projectContext: string;
  };
  metadata: {
    author?: string;
    commitHash?: string;
    branchName?: string;
    tags: string[];
  };
}

export interface ContextualMemoryQuery {
  query: string;
  context: {
    currentFile?: string;
    currentFunction?: string;
    currentClass?: string;
    workspaceRoot?: string;
    recentFiles?: string[];
    activeProject?: string;
  };
  temporalConstraints?: {
    startTime?: number;
    endTime?: number;
    maxAge?: number;
  };
  relevanceThreshold?: number;
  maxResults?: number;
}

export interface PredictiveMemoryInsight {
  id: string;
  type: 'pattern' | 'suggestion' | 'warning' | 'opportunity';
  confidence: number;
  prediction: string;
  reasoning: string;
  suggestedActions: string[];
  relatedMemories: string[];
  expiresAt: number;
}

export interface CollaborativeMemoryShare {
  id: string;
  memoryId: string;
  sharedBy: string;
  sharedWith: string[];
  permissions: 'read' | 'write' | 'admin';
  sharedAt: number;
  expiresAt?: number;
  context: string;
}

export interface MemoryAnalytics {
  totalMemories: number;
  temporalMemories: number;
  collaborativeShares: number;
  predictiveInsights: number;
  memoryGrowthRate: number;
  averageRetrievalTime: number;
  mostAccessedMemories: string[];
  memoryPatterns: {
    commonTags: string[];
    frequentContexts: string[];
    peakUsageTimes: number[];
  };
  userBehaviorInsights: {
    preferredMemoryTypes: string[];
    searchPatterns: string[];
    collaborationFrequency: number;
  };
}

export class QuantumMemorySystem {
  private memoryManager: IMemoryManagerAdapter;
  private vectorMemory: IVectorMemoryManagerForQuantum;
  private supervisorAgent: SupervisorAgent;

  // Quantum memory stores
  private temporalStore: Map<string, TemporalMemoryEntry[]> = new Map();
  private contextualIndex: Map<string, Set<string>> = new Map();
  private collaborativeShares: Map<string, CollaborativeMemoryShare> = new Map();
  private predictiveInsights: Map<string, PredictiveMemoryInsight> = new Map();

  // Performance optimization
  private queryCache: Map<string, { result: unknown; timestamp: number }> = new Map();
  private readonly cacheExpiry = 300000; // 5 minutes
  private readonly maxCacheSize = 1000;

  // Analytics tracking
  private analytics: MemoryAnalytics;
  private queryHistory: Array<{ query: string; timestamp: number; resultCount: number }> = [];

  constructor(
    memoryManager: IMemoryManagerAdapter,
    vectorMemory: IVectorMemoryManagerForQuantum,
    supervisorAgent: SupervisorAgent
  ) {
    this.memoryManager = memoryManager;
    this.vectorMemory = vectorMemory;
    this.supervisorAgent = supervisorAgent;

    this.analytics = this.initializeAnalytics();
    this.setupEventListeners();
    this.startPredictiveEngine();
  }

  /**
     * Initialize the quantum memory system
     */
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing Quantum Memory System...');

      // Load existing temporal memories
      await this.loadTemporalMemories();

      // Load collaborative shares
      await this.loadCollaborativeShares();

      // Initialize predictive engine
      await this.initializePredictiveEngine();

      // Build contextual index
      await this.buildContextualIndex();

      logger.info('Quantum Memory System initialized successfully');
    } catch (error) {
      logger.error(`Failed to initialize Quantum Memory System: ${error}`);
      throw error;
    }
  }

  /**
     * Store temporal memory with version tracking
     */
  public async storeTemporalMemory(
    content: string,
    context: TemporalMemoryEntry['context'],
    changeType: TemporalMemoryEntry['changeType'] = 'create',
    metadata: Partial<TemporalMemoryEntry['metadata']> = {}
  ): Promise<string> {
    const id = `temporal_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Find previous version if updating
    let previousVersionId: string | undefined;
    const existingVersions = this.temporalStore.get(context.filePath) || [];
    if (changeType === 'update' && existingVersions.length > 0) {
      const lastVersion = existingVersions[existingVersions.length - 1];
      previousVersionId = lastVersion.id;
      lastVersion.nextVersionId = id;
    }

    const temporalEntry: TemporalMemoryEntry = {
      id,
      content,
      timestamp: Date.now(),
      version: existingVersions.length + 1,
      previousVersionId,
      changeType,
      context,
      metadata: {
        tags: [],
        ...metadata
      }
    };

    // Store in temporal store
    if (!this.temporalStore.has(context.filePath)) {
      this.temporalStore.set(context.filePath, []);
    }
    const temporalEntries = this.temporalStore.get(context.filePath);
    if (temporalEntries) {
      temporalEntries.push(temporalEntry);
    }

    // Store in regular memory system for compatibility
    await this.memoryManager.addMemory({
      content: JSON.stringify(temporalEntry),
      metadata: {
        source: 'temporal',
        type: 'semantic',
        tags: ['temporal', changeType, ...metadata.tags || []]
      }
    });

    // Update contextual index
    this.updateContextualIndex(temporalEntry);

    // Update analytics
    this.analytics.temporalMemories++;
    this.analytics.totalMemories++;

    logger.info(`Stored temporal memory: ${id} (${changeType})`);
    return id;
  }

  /**
     * Advanced contextual memory retrieval
     */
  public async retrieveContextualMemories(query: ContextualMemoryQuery): Promise<TemporalMemoryEntry[]> {
    const cacheKey = this.generateQueryCacheKey(query);

    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached && Array.isArray(cached)) {
      return cached as TemporalMemoryEntry[];
    }

    const startTime = Date.now();
    const results: TemporalMemoryEntry[] = [];

    try {
      // 1. Get base memories from existing system
      const baseMemories = await this.memoryManager.searchMemories({
        query: query.query,
        limit: query.maxResults || 20
      });

      // 1.5. Enhance with vector memory search for semantic similarity
      try {
        const vectorResults = await this.vectorMemory.searchSimilarMemories(query.query, query.maxResults || 20);
        logger.debug(`Vector memory found ${vectorResults.length} additional semantic matches`);
        // Vector results would be merged with base memories in a full implementation
      } catch (error) {
        logger.debug(`Vector memory search failed: ${error}`);
      }

      // 2. Convert to temporal entries and apply contextual filtering
      const temporalMemories = baseMemories.map(memory => this.convertToTemporalEntry(memory));
      const contextualResults = await this.applyContextualFiltering(temporalMemories, query);

      // 3. Apply temporal constraints
      const temporalResults = this.applyTemporalConstraints(contextualResults, query.temporalConstraints);

      // 4. Enhance with quantum insights
      const enhancedResults = await this.enhanceWithQuantumInsights(temporalResults, query);

      // 5. Sort by relevance and context
      const sortedResults = this.sortByRelevanceAndContext(enhancedResults, query);

      results.push(...sortedResults.slice(0, query.maxResults || 10));

      // Cache the results
      this.setCache(cacheKey, results);

      // Update analytics
      const retrievalTime = Date.now() - startTime;
      this.updateRetrievalAnalytics(query.query, results.length, retrievalTime);

      logger.info(`Retrieved ${results.length} contextual memories in ${retrievalTime}ms`);
      return results;

    } catch (error) {
      logger.error(`Contextual memory retrieval failed: ${error}`);
      return [];
    }
  }

  /**
     * Generate predictive memory insights
     */
  public async generatePredictiveInsights(context: string): Promise<PredictiveMemoryInsight[]> {
    try {
      // Analyze recent memory patterns
      const recentMemories = await this.getRecentMemories(24 * 60 * 60 * 1000); // Last 24 hours

      // Use AI to identify patterns and predict needs
      const prompt = this.buildPredictivePrompt(recentMemories, context);

      const result = await this.supervisorAgent.run({
        prompt,
        mode: 'ask'
      });

      if (result.success && result.output) {
        const insights = this.parsePredictiveInsights(result.output);

        // Store insights
        for (const insight of insights) {
          this.predictiveInsights.set(insight.id, insight);
        }

        this.analytics.predictiveInsights += insights.length;

        logger.info(`Generated ${insights.length} predictive insights`);
        return insights;
      }

      return [];
    } catch (error) {
      logger.error(`Failed to generate predictive insights: ${error}`);
      return [];
    }
  }

  /**
     * Share memory collaboratively
     */
  public async shareMemory(
    memoryId: string,
    sharedWith: string[],
    permissions: CollaborativeMemoryShare['permissions'] = 'read',
    context = '',
    expiresIn?: number
  ): Promise<string> {
    const shareId = `share_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const share: CollaborativeMemoryShare = {
      id: shareId,
      memoryId,
      sharedBy: 'current-user', // In real implementation, get from user context
      sharedWith,
      permissions,
      sharedAt: Date.now(),
      expiresAt: expiresIn ? Date.now() + expiresIn : undefined,
      context
    };

    this.collaborativeShares.set(shareId, share);
    this.analytics.collaborativeShares++;

    logger.info(`Shared memory ${memoryId} with ${sharedWith.length} users`);
    return shareId;
  }

  /**
     * Get memory analytics
     */
  public getAnalytics(): MemoryAnalytics {
    // Update real-time analytics
    this.updateAnalytics();
    return { ...this.analytics };
  }

  /**
     * Visualize memory connections (Enhanced for Phase 4)
     */
  public async generateMemoryVisualization(centerMemoryId?: string): Promise<Record<string, unknown>> {
    try {
      const memories = centerMemoryId
        ? await this.getRelatedMemories(centerMemoryId, 50)
        : Array.from(this.temporalStore.values()).flat().slice(0, 100);

      const nodes = memories.map(memory => ({
        id: memory.id,
        label: memory.content.substring(0, 50) + '...',
        type: memory.changeType,
        timestamp: memory.timestamp,
        context: memory.context.filePath,
        size: this.calculateNodeSize(memory),
        color: this.getNodeColor(memory.changeType),
        importance: this.calculateMemoryImportance(memory)
      }));

      const edges = this.generateMemoryConnections(memories);

      // Add clustering information
      const clusters = this.identifyMemoryClusters(memories);

      // Add temporal flow information
      const temporalFlow = this.analyzeTemporalFlow(memories);

      return {
        nodes,
        edges,
        clusters,
        temporalFlow,
        metadata: {
          totalNodes: nodes.length,
          totalEdges: edges.length,
          centerNode: centerMemoryId,
          generatedAt: Date.now(),
          timespan: this.calculateTimespan(memories),
          complexity: this.calculateVisualizationComplexity(nodes, edges)
        }
      };
    } catch (error) {
      logger.error(`Failed to generate memory visualization: ${error}`);
      return { nodes: [], edges: [], clusters: [], temporalFlow: [], metadata: {} };
    }
  }

  /**
     * Advanced Memory Analytics (Enhanced for Phase 4)
     */
  public async generateAdvancedAnalytics(): Promise<MemoryAnalytics & Record<string, unknown>> {
    try {
      const allMemories = Array.from(this.temporalStore.values()).flat();

      return {
        ...this.analytics,
        advancedMetrics: {
          memoryDensity: this.calculateMemoryDensity(allMemories),
          contextualCoverage: this.calculateContextualCoverage(allMemories),
          temporalDistribution: this.analyzeTemporalDistribution(allMemories),
          collaborationPatterns: this.analyzeCollaborationPatterns(),
          predictiveAccuracy: this.calculatePredictiveAccuracy(),
          memoryEfficiency: this.calculateMemoryEfficiency(),
          knowledgeGaps: this.identifyKnowledgeGaps(allMemories),
          learningVelocity: this.calculateLearningVelocity(allMemories)
        },
        visualizations: {
          memoryMap: await this.generateMemoryVisualization(),
          temporalTimeline: this.generateTemporalTimeline(allMemories),
          contextualHeatmap: this.generateContextualHeatmap(allMemories),
          collaborationNetwork: this.generateCollaborationNetwork()
        }
      };
    } catch (error) {
      logger.error(`Failed to generate advanced analytics: ${error}`);
      return { ...this.analytics };
    }
  }

  // Private helper methods
  private initializeAnalytics(): MemoryAnalytics {
    return {
      totalMemories: 0,
      temporalMemories: 0,
      collaborativeShares: 0,
      predictiveInsights: 0,
      memoryGrowthRate: 0,
      averageRetrievalTime: 0,
      mostAccessedMemories: [],
      memoryPatterns: {
        commonTags: [],
        frequentContexts: [],
        peakUsageTimes: []
      },
      userBehaviorInsights: {
        preferredMemoryTypes: [],
        searchPatterns: [],
        collaborationFrequency: 0
      }
    };
  }

  private setupEventListeners(): void {
    // Listen for file changes to update temporal memory
    vscode.workspace.onDidChangeTextDocument(event => {
      this.onDocumentChanged(event);
    });

    // Listen for file saves to create temporal snapshots
    vscode.workspace.onDidSaveTextDocument(document => {
      this.onDocumentSaved(document);
    });
  }

  private async onDocumentChanged(event: vscode.TextDocumentChangeEvent): Promise<void> {
    if (event.contentChanges.length > 0) {
      // Store temporal memory for significant changes
      const change = event.contentChanges[0];
      if (change.text.length > 10 || change.rangeLength > 10) {
        await this.storeTemporalMemory(
          `Document change: ${change.text}`,
          {
            filePath: event.document.uri.fsPath,
            lineNumber: change.range.start.line,
            projectContext: vscode.workspace.name || 'unknown'
          },
          'update',
          { tags: ['auto-save', 'document-change'] }
        );
      }
    }
  }

  private async onDocumentSaved(document: vscode.TextDocument): Promise<void> {
    // Create temporal snapshot on save
    await this.storeTemporalMemory(
      `File saved: ${document.fileName}`,
      {
        filePath: document.uri.fsPath,
        projectContext: vscode.workspace.name || 'unknown'
      },
      'update',
      { tags: ['save', 'snapshot'] }
    );
  }

  private startPredictiveEngine(): void {
    // Run predictive analysis every 30 minutes
    setInterval(async () => {
      try {
        const context = vscode.window.activeTextEditor?.document.uri.fsPath || 'general';
        await this.generatePredictiveInsights(context);
      } catch (error) {
        logger.warn(`Predictive engine error: ${error}`);
      }
    }, 30 * 60 * 1000);
  }

  private async loadTemporalMemories(): Promise<void> {
    try {
      const memories = await this.memoryManager.searchMemories({
        query: 'temporal',
        limit: 1000,
        filter: {
          tags: ['temporal']
        }
      });
      for (const memory of memories) {
        try {
          const temporalEntry = JSON.parse(memory.content) as TemporalMemoryEntry;
          if (!this.temporalStore.has(temporalEntry.context.filePath)) {
            this.temporalStore.set(temporalEntry.context.filePath, []);
          }
          const temporalEntries = this.temporalStore.get(temporalEntry.context.filePath);
          if (temporalEntries) {
            temporalEntries.push(temporalEntry);
          }
        } catch (error) {
          logger.warn(`Failed to parse temporal memory: ${error}`);
        }
      }
      logger.info(`Loaded ${this.temporalStore.size} temporal memory files`);
    } catch (error) {
      logger.warn(`Failed to load temporal memories: ${error}`);
    }
  }

  private async loadCollaborativeShares(): Promise<void> {
    // In real implementation, load from persistent storage
    logger.info('Collaborative shares loaded');
  }

  private async initializePredictiveEngine(): Promise<void> {
    // Initialize predictive models and patterns
    logger.info('Predictive engine initialized');
  }

  private async buildContextualIndex(): Promise<void> {
    // Build index for fast contextual lookups
    for (const [, memories] of this.temporalStore) {
      for (const memory of memories) {
        this.updateContextualIndex(memory);
      }
    }
    logger.info(`Built contextual index with ${this.contextualIndex.size} entries`);
  }

  private updateContextualIndex(memory: TemporalMemoryEntry): void {
    const contexts = [
      memory.context.filePath,
      memory.context.functionName,
      memory.context.className,
      memory.context.projectContext,
      ...memory.metadata.tags
    ].filter(Boolean) as string[];

    for (const context of contexts) {
      if (!this.contextualIndex.has(context)) {
        this.contextualIndex.set(context, new Set());
      }
      const contextualSet = this.contextualIndex.get(context);
      if (contextualSet) {
        contextualSet.add(memory.id);
      }
    }
  }

  private generateQueryCacheKey(query: ContextualMemoryQuery): string {
    return JSON.stringify(query);
  }

  private getFromCache(key: string): unknown {
    const cached = this.queryCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.result;
    }
    if (cached) {
      this.queryCache.delete(key);
    }
    return null;
  }

  private setCache(key: string, result: unknown): void {
    if (this.queryCache.size >= this.maxCacheSize) {
      const firstKey = this.queryCache.keys().next().value;
      if (firstKey) {
        this.queryCache.delete(firstKey);
      }
    }
    this.queryCache.set(key, { result, timestamp: Date.now() });
  }

  private async applyContextualFiltering(memories: TemporalMemoryEntry[], query: ContextualMemoryQuery): Promise<TemporalMemoryEntry[]> {
    // Filter memories based on context
    const filtered: TemporalMemoryEntry[] = [];

    for (const memory of memories) {
      try {
        const temporalEntry = JSON.parse(memory.content) as TemporalMemoryEntry;

        // Apply context filters
        if (query.context.currentFile &&
          !temporalEntry.context.filePath.includes(query.context.currentFile)) {
          continue;
        }

        if (query.context.currentFunction &&
          temporalEntry.context.functionName !== query.context.currentFunction) {
          continue;
        }

        filtered.push(temporalEntry);
      } catch (error) {
        // Skip invalid entries
        continue;
      }
    }

    return filtered;
  }

  private applyTemporalConstraints(
    memories: TemporalMemoryEntry[],
    constraints?: ContextualMemoryQuery['temporalConstraints']
  ): TemporalMemoryEntry[] {
    if (!constraints) return memories;

    return memories.filter(memory => {
      if (constraints.startTime && memory.timestamp < constraints.startTime) return false;
      if (constraints.endTime && memory.timestamp > constraints.endTime) return false;
      if (constraints.maxAge && Date.now() - memory.timestamp > constraints.maxAge) return false;
      return true;
    });
  }

  private convertToTemporalEntry(memory: MemoryEntry): TemporalMemoryEntry {
    return {
      ...memory,
      version: 1,
      changeType: 'create',
      context: {
        filePath: memory.metadata.filePath as string || '',
        functionName: memory.metadata.functionName as string,
        className: memory.metadata.className as string,
        lineNumber: memory.metadata.lineNumber as number,
        projectContext: memory.metadata.projectContext as string || ''
      },
      metadata: {
        author: memory.metadata.author as string,
        commitHash: memory.metadata.commitHash as string,
        branchName: memory.metadata.branchName as string,
        tags: memory.metadata.tags as string[] || []
      },
      previousVersionId: undefined
    };
  }

  private async enhanceWithQuantumInsights(
    memories: TemporalMemoryEntry[],
    query: ContextualMemoryQuery
  ): Promise<TemporalMemoryEntry[]> {
    // Add quantum insights to memories based on query context
    logger.debug(`Enhancing ${memories.length} memories with quantum insights for context: ${query.context}`);

    // For now, return memories as-is but log the enhancement attempt
    return memories;
  }

  private sortByRelevanceAndContext(
    memories: TemporalMemoryEntry[],
    query: ContextualMemoryQuery
  ): TemporalMemoryEntry[] {
    return memories.sort((a, b) => {
      // Sort by timestamp (most recent first) and relevance
      const timeScore = b.timestamp - a.timestamp;
      const contextScore = this.calculateContextScore(a, query) - this.calculateContextScore(b, query);
      return contextScore !== 0 ? contextScore : timeScore;
    });
  }

  private calculateContextScore(memory: TemporalMemoryEntry, query: ContextualMemoryQuery): number {
    let score = 0;

    if (query.context.currentFile && memory.context.filePath.includes(query.context.currentFile)) {
      score += 10;
    }

    if (query.context.currentFunction && memory.context.functionName === query.context.currentFunction) {
      score += 5;
    }

    if (query.context.currentClass && memory.context.className === query.context.currentClass) {
      score += 5;
    }

    return score;
  }

  private async getRecentMemories(timeWindow: number): Promise<TemporalMemoryEntry[]> {
    const cutoff = Date.now() - timeWindow;
    const recent: TemporalMemoryEntry[] = [];

    for (const memories of this.temporalStore.values()) {
      for (const memory of memories) {
        if (memory.timestamp >= cutoff) {
          recent.push(memory);
        }
      }
    }

    return recent.sort((a, b) => b.timestamp - a.timestamp);
  }

  private buildPredictivePrompt(memories: TemporalMemoryEntry[], context: string): string {
    const recentChanges = memories.slice(0, 10).map(m =>
      `${m.changeType}: ${m.content} (${m.context.filePath})`
    ).join('\n');

    return `
# Predictive Memory Analysis

## Recent Memory Patterns:
${recentChanges}

## Current Context:
${context}

## Task:
Analyze the recent memory patterns and predict:
1. What the developer might need next
2. Potential issues or opportunities
3. Suggested actions or improvements

Provide insights in JSON format:
{
  "insights": [
    {
      "type": "pattern|suggestion|warning|opportunity",
      "confidence": 0.8,
      "prediction": "Brief prediction",
      "reasoning": "Why this prediction makes sense",
      "suggestedActions": ["action1", "action2"]
    }
  ]
}
        `;
  }

  private parsePredictiveInsights(response: string): PredictiveMemoryInsight[] {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return parsed.insights.map((insight: { type?: string; confidence?: number; prediction?: string; reasoning?: string; suggestedActions?: string[] }, index: number) => ({
          id: `insight_${Date.now()}_${index}`,
          type: insight.type || 'suggestion',
          confidence: insight.confidence || 0.5,
          prediction: insight.prediction || '',
          reasoning: insight.reasoning || '',
          suggestedActions: insight.suggestedActions || [],
          relatedMemories: [],
          expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
        }));
      }
    } catch (error) {
      logger.warn(`Failed to parse predictive insights: ${error}`);
    }
    return [];
  }

  private async getRelatedMemories(memoryId: string, limit: number): Promise<TemporalMemoryEntry[]> {
    // Find memories related to the given memory
    const related: TemporalMemoryEntry[] = [];

    for (const memories of this.temporalStore.values()) {
      for (const memory of memories) {
        if (memory.id === memoryId || memory.previousVersionId === memoryId || memory.nextVersionId === memoryId) {
          related.push(memory);
        }
      }
    }

    return related.slice(0, limit);
  }

  private generateMemoryConnections(memories: TemporalMemoryEntry[]): Array<{ from: string; to: string; type: string; weight: number; label?: string }> {
    const edges: Array<{ from: string; to: string; type: string; weight: number; label?: string }> = [];

    for (const memory of memories) {
      if (memory.previousVersionId) {
        edges.push({
          from: memory.previousVersionId,
          to: memory.id,
          type: 'version',
          weight: 1.0,
          label: 'evolves to'
        });
      }

      // Add context-based connections
      const relatedMemories = memories.filter(m =>
        m.id !== memory.id &&
        m.context.filePath === memory.context.filePath
      );

      for (const related of relatedMemories.slice(0, 3)) {
        edges.push({
          from: memory.id,
          to: related.id,
          type: 'context',
          weight: 0.8,
          label: 'related'
        });
      }
    }

    return edges;
  }

  private updateRetrievalAnalytics(query: string, resultCount: number, retrievalTime: number): void {
    this.queryHistory.push({ query, timestamp: Date.now(), resultCount });

    // Update average retrieval time
    const totalTime = this.analytics.averageRetrievalTime * (this.queryHistory.length - 1) + retrievalTime;
    this.analytics.averageRetrievalTime = totalTime / this.queryHistory.length;

    // Keep only recent query history
    if (this.queryHistory.length > 1000) {
      this.queryHistory = this.queryHistory.slice(-500);
    }
  }

  private updateAnalytics(): void {
    // Update real-time analytics
    this.analytics.totalMemories = Array.from(this.temporalStore.values()).reduce((sum, memories) => sum + memories.length, 0);
    this.analytics.temporalMemories = this.analytics.totalMemories;
    this.analytics.collaborativeShares = this.collaborativeShares.size;
    this.analytics.predictiveInsights = this.predictiveInsights.size;

    // Calculate growth rate (simplified)
    const recentMemories = this.queryHistory.filter(q => Date.now() - q.timestamp < 24 * 60 * 60 * 1000);
    this.analytics.memoryGrowthRate = recentMemories.length;
  }

  // Enhanced visualization helper methods for Phase 4
  private calculateNodeSize(memory: TemporalMemoryEntry): number {
    // Calculate node size based on memory importance and content length
    let size = 10; // Base size
    size += Math.min(memory.content.length / 100, 20); // Content length factor
    size += memory.metadata.tags.length * 2; // Tag factor
    return Math.min(size, 50); // Max size limit
  }

  private getNodeColor(changeType: TemporalMemoryEntry['changeType']): string {
    const colors = {
      'create': '#4CAF50',    // Green
      'update': '#2196F3',    // Blue
      'delete': '#F44336',    // Red
      'refactor': '#FF9800'   // Orange
    };
    return colors[changeType] || '#9E9E9E';
  }

  private calculateMemoryImportance(memory: TemporalMemoryEntry): number {
    let importance = 0.5; // Base importance

    // Recent memories are more important
    const age = Date.now() - memory.timestamp;
    const dayInMs = 24 * 60 * 60 * 1000;
    if (age < dayInMs) importance += 0.3;
    else if (age < 7 * dayInMs) importance += 0.2;
    else if (age < 30 * dayInMs) importance += 0.1;

    // More tags indicate more context
    importance += Math.min(memory.metadata.tags.length * 0.1, 0.3);

    return Math.min(importance, 1.0);
  }

  private identifyMemoryClusters(memories: TemporalMemoryEntry[]): Array<{ id: string; name: string; members: string[]; type: string; strength: number }> {
    const clusters: Array<{ id: string; name: string; members: string[]; type: string; strength: number }> = [];
    const fileGroups = new Map<string, TemporalMemoryEntry[]>();

    // Group by file path
    for (const memory of memories) {
      const filePath = memory.context.filePath;
      if (!fileGroups.has(filePath)) {
        fileGroups.set(filePath, []);
      }
      const fileGroup = fileGroups.get(filePath);
      if (fileGroup) {
        fileGroup.push(memory);
      }
    }

    // Create clusters for files with multiple memories
    for (const [filePath, fileMemories] of fileGroups) {
      if (fileMemories.length > 1) {
        clusters.push({
          id: `cluster_${filePath.replace(/[^a-zA-Z0-9]/g, '_')}`,
          name: `File: ${filePath.split('/').pop()}`,
          members: fileMemories.map(m => m.id),
          type: 'file-based',
          strength: fileMemories.length / memories.length
        });
      }
    }

    return clusters;
  }

  private analyzeTemporalFlow(memories: TemporalMemoryEntry[]): Array<{ from: string; to: string; timeDelta: number; type: string; strength: number }> {
    const flow: Array<{ from: string; to: string; timeDelta: number; type: string; strength: number }> = [];
    const sortedMemories = memories.sort((a, b) => a.timestamp - b.timestamp);

    for (let i = 0; i < sortedMemories.length - 1; i++) {
      const current = sortedMemories[i];
      const next = sortedMemories[i + 1];

      // Check if memories are related (same file or linked versions)
      if (current.context.filePath === next.context.filePath ||
        current.nextVersionId === next.id) {
        flow.push({
          from: current.id,
          to: next.id,
          timeDelta: next.timestamp - current.timestamp,
          type: 'temporal',
          strength: this.calculateTemporalStrength(current, next)
        });
      }
    }

    return flow;
  }

  private calculateTemporalStrength(memory1: TemporalMemoryEntry, memory2: TemporalMemoryEntry): number {
    const timeDelta = Math.abs(memory2.timestamp - memory1.timestamp);
    const hourInMs = 60 * 60 * 1000;

    // Closer in time = stronger connection
    if (timeDelta < hourInMs) return 1.0;
    if (timeDelta < 24 * hourInMs) return 0.7;
    if (timeDelta < 7 * 24 * hourInMs) return 0.4;
    return 0.1;
  }

  private calculateTimespan(memories: TemporalMemoryEntry[]): { start: number; end: number; duration: number } {
    if (memories.length === 0) {
      return { start: 0, end: 0, duration: 0 };
    }

    const timestamps = memories.map(m => m.timestamp);
    const start = Math.min(...timestamps);
    const end = Math.max(...timestamps);

    return { start, end, duration: end - start };
  }

  private calculateVisualizationComplexity(nodes: unknown[], edges: unknown[]): number {
    // Simple complexity metric based on nodes and edges
    return Math.min((nodes.length + edges.length) / 100, 1.0);
  }

  // Advanced analytics helper methods
  private calculateMemoryDensity(memories: TemporalMemoryEntry[]): number {
    if (memories.length === 0) return 0;

    const timespan = this.calculateTimespan(memories);
    if (timespan.duration === 0) return 1;

    const dayInMs = 24 * 60 * 60 * 1000;
    return memories.length / (timespan.duration / dayInMs);
  }

  private calculateContextualCoverage(memories: TemporalMemoryEntry[]): number {
    const uniqueFiles = new Set(memories.map(m => m.context.filePath));
    const uniqueFunctions = new Set(memories.map(m => m.context.functionName).filter(Boolean));
    const uniqueClasses = new Set(memories.map(m => m.context.className).filter(Boolean));

    // Simple coverage metric
    return Math.min((uniqueFiles.size + uniqueFunctions.size + uniqueClasses.size) / 100, 1.0);
  }

  private analyzeTemporalDistribution(memories: TemporalMemoryEntry[]): { hourly: number[]; daily: number[]; monthly: number[] } {
    const distribution = {
      hourly: new Array(24).fill(0),
      daily: new Array(7).fill(0),
      monthly: new Array(12).fill(0)
    };

    for (const memory of memories) {
      const date = new Date(memory.timestamp);
      distribution.hourly[date.getHours()]++;
      distribution.daily[date.getDay()]++;
      distribution.monthly[date.getMonth()]++;
    }

    return distribution;
  }

  private analyzeCollaborationPatterns(): { totalShares: number; averageShareDuration: number; mostActiveCollaborators: string[]; collaborationFrequency: number } {
    // Analyze collaboration patterns from shared memories
    return {
      totalShares: this.collaborativeShares.size,
      averageShareDuration: 0,
      mostActiveCollaborators: [],
      collaborationFrequency: 0
    };
  }

  private calculatePredictiveAccuracy(): number {
    // Calculate accuracy of past predictions
    const expiredInsights = Array.from(this.predictiveInsights.values())
      .filter(insight => Date.now() > insight.expiresAt);

    if (expiredInsights.length === 0) return 0.5; // Default

    // Simple accuracy calculation (would need actual validation in real implementation)
    return 0.75; // Placeholder
  }

  private calculateMemoryEfficiency(): number {
    // Calculate memory system efficiency
    const totalMemories = Array.from(this.temporalStore.values()).reduce((sum, memories) => sum + memories.length, 0);
    const cacheHitRate = this.queryCache.size > 0 ? 0.8 : 0; // Simplified

    return Math.min((totalMemories / 1000) * cacheHitRate, 1.0);
  }

  private identifyKnowledgeGaps(memories: TemporalMemoryEntry[]): string[] {
    const gaps: string[] = [];

    // Identify files with no recent memories
    const recentCutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days
    const recentFiles = new Set(
      memories
        .filter(m => m.timestamp > recentCutoff)
        .map(m => m.context.filePath)
    );

    const allFiles = new Set(memories.map(m => m.context.filePath));

    for (const file of allFiles) {
      if (!recentFiles.has(file)) {
        gaps.push(`No recent activity in ${file}`);
      }
    }

    return gaps.slice(0, 10); // Limit to top 10
  }

  private calculateLearningVelocity(memories: TemporalMemoryEntry[]): number {
    // Calculate how quickly new knowledge is being acquired
    const recentCutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days
    const recentMemories = memories.filter(m => m.timestamp > recentCutoff);

    return recentMemories.length / 7; // Memories per day
  }

  private generateTemporalTimeline(memories: TemporalMemoryEntry[]): Array<{ timestamp: number; event: string; file: string; content: string }> {
    const timeline = memories
      .sort((a, b) => a.timestamp - b.timestamp)
      .map(memory => ({
        timestamp: memory.timestamp,
        event: memory.changeType,
        file: memory.context.filePath,
        content: memory.content.substring(0, 100)
      }));

    return timeline;
  }

  private generateContextualHeatmap(memories: TemporalMemoryEntry[]): { [filePath: string]: number } {
    const heatmap: { [filePath: string]: number } = {};

    for (const memory of memories) {
      const filePath = memory.context.filePath;
      heatmap[filePath] = (heatmap[filePath] || 0) + 1;
    }

    return heatmap;
  }

  private generateCollaborationNetwork(): { nodes: Array<{ id: string; label: string; type: string }>; edges: Array<{ from: string; to: string; weight: number; type: string }> } {
    const network: { nodes: Array<{ id: string; label: string; type: string }>; edges: Array<{ from: string; to: string; weight: number; type: string }> } = {
      nodes: [],
      edges: []
    };

    // Generate collaboration network from shared memories
    for (const share of this.collaborativeShares.values()) {
      network.nodes.push({
        id: share.sharedBy,
        type: 'user',
        label: share.sharedBy
      });

      for (const recipient of share.sharedWith) {
        network.nodes.push({
          id: recipient,
          type: 'user',
          label: recipient
        });

        network.edges.push({
          from: share.sharedBy,
          to: recipient,
          type: 'collaboration',
          weight: 1
        });
      }
    }

    return network;
  }

  /**
     * Dispose quantum memory system
     */
  public dispose(): void {
    this.queryCache.clear();
    this.temporalStore.clear();
    this.contextualIndex.clear();
    this.collaborativeShares.clear();
    this.predictiveInsights.clear();
    this.queryHistory = [];
    logger.info('Quantum Memory System disposed');
  }
}
