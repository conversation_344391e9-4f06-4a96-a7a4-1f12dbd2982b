"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentationVisualizationTool = exports.DocumentationSummaryTool = exports.DocumentationSearchTool = exports.DocumentationGenTool = void 0;
const llmService_1 = require("../llm/llmService");
const zod_1 = require("zod");
class DocumentationGenTool {
    id = 'docGen';
    name = 'Documentation Generation';
    description = 'Generate documentation for code or APIs.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        code: zod_1.z.string().describe('Code to document.'),
        type: zod_1.z.enum(['function', 'class', 'module', 'api']).describe('Type of documentation.')
    });
    inputSchema = {
        type: 'object',
        properties: {
            code: { type: 'string', description: 'Code to document.' },
            type: { type: 'string', enum: ['function', 'class', 'module', 'api'], description: 'Type of documentation.' }
        },
        required: ['code', 'type']
    };
    llmConfig = {
        provider: 'openai',
        modelId: 'gpt-3.5-turbo',
        options: { temperature: 0.25 }
    };
    async execute(actionName, input, _context) {
        const code = input.code;
        const type = input.type;
        const provider = await llmService_1.llmService.getProviderForConfig(this.llmConfig);
        if (!provider)
            return { success: false, error: 'No LLM provider configured.', toolId: this.id, actionName };
        const prompt = `Generate ${type} documentation for the following code.\n\n${code}`;
        const result = await provider.generate({ prompt, systemPrompt: 'You are an expert technical writer.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });
        if (result.error)
            return { success: false, error: result.error, toolId: this.id, actionName };
        return { success: true, output: result.content, toolId: this.id, actionName };
    }
}
exports.DocumentationGenTool = DocumentationGenTool;
class DocumentationSearchTool {
    id = 'docSearch';
    name = 'Documentation Search (Advanced)';
    description = 'Search documentation using web or local sources.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        query: zod_1.z.string().describe('Documentation search query.')
    });
    inputSchema = {
        type: 'object',
        properties: {
            query: { type: 'string', description: 'Documentation search query.' }
        },
        required: ['query']
    };
    async execute(actionName, input, _context) {
        // Placeholder: In real implementation, this would use web search or offline docs
        return { success: true, output: `Searched documentation for: ${input.query}`, toolId: this.id, actionName };
    }
}
exports.DocumentationSearchTool = DocumentationSearchTool;
class DocumentationSummaryTool {
    id = 'docSummary';
    name = 'Documentation Summary';
    description = 'Summarize documentation or technical articles.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        text: zod_1.z.string().describe('Documentation or article to summarize.')
    });
    inputSchema = {
        type: 'object',
        properties: {
            text: { type: 'string', description: 'Documentation or article to summarize.' }
        },
        required: ['text']
    };
    llmConfig = {
        provider: 'openai',
        modelId: 'gpt-3.5-turbo',
        options: { temperature: 0.2 }
    };
    async execute(actionName, input, _context) {
        const text = input.text;
        const provider = await llmService_1.llmService.getProviderForConfig(this.llmConfig);
        if (!provider)
            return { success: false, error: 'No LLM provider configured.', toolId: this.id, actionName };
        const prompt = `Summarize the following documentation or article for a developer audience.\n\n${text}`;
        const result = await provider.generate({ prompt, systemPrompt: 'You are an expert technical summarizer.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });
        if (result.error)
            return { success: false, error: result.error, toolId: this.id, actionName };
        return { success: true, output: result.content, toolId: this.id, actionName };
    }
}
exports.DocumentationSummaryTool = DocumentationSummaryTool;
class DocumentationVisualizationTool {
    id = 'docViz';
    name = 'Documentation Visualization';
    description = 'Visualize documentation structure or API relationships.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        source: zod_1.z.string().describe('Source code or API spec to visualize.')
    });
    inputSchema = {
        type: 'object',
        properties: {
            source: { type: 'string', description: 'Source code or API spec to visualize.' }
        },
        required: ['source']
    };
    async execute(actionName, input, _context) {
        // Placeholder: actual visualization would require UI integration
        return { success: true, output: `Visualization data for: ${input.source}`, toolId: this.id, actionName };
    }
}
exports.DocumentationVisualizationTool = DocumentationVisualizationTool;
//# sourceMappingURL=advancedDocsTool.js.map