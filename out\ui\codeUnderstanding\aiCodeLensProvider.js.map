{"version": 3, "file": "aiCodeLensProvider.js", "sourceRoot": "", "sources": ["../../../src/ui/codeUnderstanding/aiCodeLensProvider.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AAIjC,yCAAsC;AACtC,+DAA4D;AA6B5D,MAAa,kBAAkB;IACrB,eAAe,CAAkB;IACjC,YAAY,CAAe;IAC3B,aAAa,CAAgB;IAC7B,WAAW,GAAwB,EAAE,CAAC;IAE9C,2BAA2B;IACnB,aAAa,GAAmE,IAAI,GAAG,EAAE,CAAC;IACjF,WAAW,GAAG,KAAK,CAAC,CAAC,WAAW;IAChC,YAAY,GAAG,EAAE,CAAC;IAEnC,iBAAiB;IACT,WAAW,GAAyB,IAAI,GAAG,EAAE,CAAC;IAEtD,YACE,eAAgC,EAChC,YAA0B,EAC1B,aAA4B;QAE5B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,kCAAkC;QAClC,MAAM,SAAS,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAEhG,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CACxD,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,EAC5B,IAAI,CACL,CAAC;YACF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,uCAAuC,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;IACnF,CAAC;IAEO,gBAAgB;QACtB,yCAAyC;QACzC,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC,KAAmB,EAAE,QAA6B,EAAE,EAAE;YACnI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEtC,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,KAAmB,EAAE,QAA6B,EAAE,EAAE;YACrI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEvC,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,CAAC,KAAmB,EAAE,QAA6B,EAAE,EAAE;YAC3I,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAE5C,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,KAAmB,EAAE,QAA6B,EAAE,EAAE;YACrI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEvC,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC,KAAmB,EAAE,QAA6B,EAAE,EAAE;YACrI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEvC,+CAA+C;QAC/C,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,KAAK,EAAE,OAAoB,EAAE,KAAoB,EAAE,QAA8B,EAAE,EAAE;YACrK,gEAAgE;YAChE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC;YACrE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,OAAO,CAAC,KAAK,OAAO,OAAO,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClJ,IAAI,SAAS,KAAK,YAAY,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBACxD,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE1C,MAAM,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,KAAK,EAAE,QAA4B,EAAE,EAAE;YACjI,gDAAgD;YAChD,MAAM,WAAW,GAAa,EAAE,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC5C,WAAW,CAAC,IAAI,CAAC,eAAe,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC5D,WAAW,CAAC,IAAI,CAAC,iBAAiB,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;YACjE,WAAW,CAAC,IAAI,CAAC,sBAAsB,QAAQ,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;YAC3E,WAAW,CAAC,IAAI,CAAC,oBAAoB,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;YACtE,WAAW,CAAC,IAAI,CAAC,kBAAkB,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;YACnE,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClC,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACpC,WAAW,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;YAC1F,CAAC;YACD,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/G,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAE/C,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,EAAE,KAAoB,EAAE,QAA8B,EAAE,EAAE;YAC7J,MAAM,SAAS,GAAG,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;YACvE,IAAI,CAAC,SAAS;gBAAE,OAAO;YACvB,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC/D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,sCAAsC,CAAC,CAAC;gBACzE,OAAO;YACT,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YAC1F,MAAM,WAAW,GAAI,MAAM,CAAC,MAAmD,CAAC,MAAM,CAAC;YACvF,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBAClE,MAAM,MAAM,GAAG,WAAW,CAAC;gBAC3B,MAAM,KAAK,GAAG,CAAC,mBAAmB,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC3F,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAEjD,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,KAAK,EAAE,KAAoB,EAAE,QAA8B,EAAE,EAAE;YAC/I,MAAM,SAAS,GAAG,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;YACvE,IAAI,CAAC,SAAS;gBAAE,OAAO;YACvB,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBAC5C,MAAM,EAAE,6FAA6F,SAAS,CAAC,UAAU,KAAK,IAAI,UAAU;gBAC5I,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACxC,MAAM,WAAW,GAAG,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;gBAC5E,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACxD,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;YAClE,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,iBAAiB,CACrB,QAA6B,EAC7B,KAA+B;QAE/B,IAAI,CAAC;YACH,0DAA0D;YAC1D,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5C,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,+BAA+B;YAC/B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YACnE,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,iCAAiC;YACjC,MAAM,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAE9E,eAAM,CAAC,IAAI,CAAC,YAAY,UAAU,CAAC,MAAM,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjF,OAAO,UAAU,CAAC;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;YAClD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,eAAe,CACnB,QAAyB,EACzB,KAA+B;QAE/B,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAClC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,UAAU,GAAG,QAAsB,CAAC;QAE1C,oCAAoC;QACpC,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC/E,UAAU,CAAC,OAAO,GAAG;YACnB,KAAK,EAAE,GAAG,mBAAmB,IAAI,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE;YAC3D,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,aAAa,IAAI,qBAAqB;YAClE,SAAS,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC;SAClD,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,QAA6B;QAC3D,0CAA0C;QAC1C,IAAI,QAAQ,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,8BAA8B;QAC9B,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QACxD,MAAM,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACpG,IAAI,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAC3B,QAA6B,EAC7B,KAA+B;QAE/B,MAAM,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;QAE9D,oBAAoB;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC;YACH,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEvE,mBAAmB;YACnB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAEjD,OAAO,cAAc,CAAC;QACxB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAC/B,QAA6B,EAC7B,MAAgC;QAEhC,MAAM,QAAQ,GAAkB,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG;YACd,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC;YACf,WAAW,EAAE,CAAC;SACf,CAAC;QACF,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACnE,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,gBAAgB,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,aAAa,EAAE;oBACnE,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM;oBAC7B,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE;iBAC5B,CAAC,CAAC;gBAEH,IAAI,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;oBACxD,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAA0E,CAAC;oBAC/G,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC,oBAAoB,IAAI,CAAC,CAAC;oBAC1D,IAAI,OAAO,UAAU,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;wBACxD,OAAO,CAAC,eAAe,GAAG,UAAU,CAAC,oBAAoB,CAAC;oBAC5D,CAAC;yBAAM,CAAC;wBACN,mEAAmE;wBACnE,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;oBACtE,CAAC;oBAED,0BAA0B;oBAC1B,IAAI,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;wBAC5B,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,YAAY;4BAClB,KAAK,EAAE,oBAAoB,OAAO,CAAC,UAAU,GAAG;4BAChD,WAAW,EAAE,+EAA+E;4BAC5F,QAAQ,EAAE,SAAS;4BACnB,aAAa,EAAE,sBAAsB;yBACtC,CAAC,CAAC;wBACH,WAAW,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;oBACxF,CAAC;gBACH,CAAC;YACH,CAAC;YAED,oBAAoB;YACpB,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC/D,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,cAAc,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE;oBAC5D,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM;iBAC9B,CAAC,CAAC;gBAEH,IAAI,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;oBACpD,MAAM,eAAe,GAAG,cAAc,CAAC,MAAqE,CAAC;oBAC7G,IAAI,eAAe,CAAC,MAAM,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAChE,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,UAAU;4BAChB,KAAK,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,oBAAoB;4BAC3D,WAAW,EAAE,6CAA6C;4BAC1D,QAAQ,EAAE,OAAO;4BACjB,aAAa,EAAE,4BAA4B;yBAC5C,CAAC,CAAC;wBACH,WAAW,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;YACH,CAAC;YAED,gCAAgC;YAChC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBAChD,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBACpE,QAAQ,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC,CAAC;gBACtC,4DAA4D;gBAC5D,OAAO,CAAC,WAAW,GAAG,mBAAmB,CAAC,MAAM,CAAC;YACnD,CAAC;YAED,yBAAyB;YACzB,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBAChD,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;gBACxE,QAAQ,CAAC,IAAI,CAAC,GAAG,qBAAqB,CAAC,CAAC;YAC1C,CAAC;YAED,mBAAmB;YACnB,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBAChD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACjE,QAAQ,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;gBAClC,iCAAiC;gBACjC,OAAO,CAAC,YAAY,GAAG,eAAe,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAChE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,MAAM,GAAuB,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;QAEtE,gEAAgE;QAChE,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;gBACjC,OAAO,EAAE,qBAAqB,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM,yBAAyB,OAAO,CAAC,UAAU,qBAAqB,OAAO,CAAC,eAAe,iBAAiB,OAAO,CAAC,WAAW,kBAAkB,OAAO,CAAC,YAAY,GAAG;gBACvO,QAAQ,EAAE;oBACR,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC;iBAChD;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,eAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,QAA6B;QAC5D,MAAM,QAAQ,GAAkB,EAAE,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,sBAAsB,EAAE;gBAChE,WAAW,EAAE,QAAQ,CAAC,OAAO,EAAE;gBAC/B,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBAC5C,MAAM;gBACN,IAAI,EAAE,KAAK;aACZ,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACxD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;gBAClE,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,QAA6B;QAC9D,MAAM,QAAQ,GAAkB,EAAE,CAAC;QACnC,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAEhC,8DAA8D;QAC9D,MAAM,aAAa,GAAG,gDAAgD,CAAC;QACvE,MAAM,QAAQ,GAAG,2CAA2C,CAAC;QAE7D,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;QAC3D,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEjD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACjE,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,WAAW,EAAE,GAAG,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,uCAAuC;gBACrF,QAAQ,EAAE,YAAY;gBACtB,aAAa,EAAE,sBAAsB;aACtC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,QAA6B;QAC7D,MAAM,QAAQ,GAAkB,EAAE,CAAC;QAEnC,+BAA+B;QAC/B,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrD,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;YACnC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,0CAA0C;YAC1C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC3E,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,gBAAgB;oBACvB,WAAW,EAAE,kDAAkD;oBAC/D,QAAQ,EAAE,YAAY;oBACtB,aAAa,EAAE,uBAAuB;iBACvC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACG,2BAA2B,CACjC,cAAkC,EAClC,QAA6B;QAE7B,MAAM,UAAU,GAAiB,EAAE,CAAC;QAEpC,6BAA6B;QAC7B,KAAK,MAAM,OAAO,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC9C,kCAAkC;YAClC,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yBAAyB;YAEnE,4CAA4C;YAC5C,IAAI,OAAO,CAAC,IAAI,KAAK,YAAY,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;gBACpE,4BAA4B;gBAC5B,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;gBAC/E,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnF,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;YAED,MAAM,QAAQ,GAAe;gBAC3B,KAAK;gBACL,OAAO;gBACP,UAAU,EAAE,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC;gBACpD,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa;gBACnC,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE;oBACP,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,OAAO,EAAE,OAAO,CAAC,aAAa,IAAI,qBAAqB;oBACvD,SAAS,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;iBACtC;aACF,CAAC;YAEF,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAED,kCAAkC;QAClC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,eAAe,GAAe;gBAClC,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACnC,OAAO,EAAE;oBACP,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,qBAAqB,cAAc,CAAC,QAAQ,CAAC,MAAM,WAAW;oBACrE,WAAW,EAAE,sCAAsC;oBACnD,QAAQ,EAAE,MAAM;oBAChB,aAAa,EAAE,0BAA0B;iBAC1C;gBACD,UAAU,EAAE,GAAG;gBACf,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE;oBACP,KAAK,EAAE,qBAAqB,cAAc,CAAC,QAAQ,CAAC,MAAM,WAAW;oBACrE,OAAO,EAAE,0BAA0B;oBACnC,SAAS,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC;iBACtC;aACF,CAAC;YAEF,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;SAEK;IACG,eAAe,CAAC,QAAgB,EAAE,IAAY;QACpD,MAAM,QAAQ,GAAkB,EAAE,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,MAAM,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;wBACtC,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,IAA2B;4BACjC,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,YAAY;4BACpC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;4BACtC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,MAAM;4BACpC,aAAa,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;yBAClD,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,IAAY;QAC1C,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,aAAa,CAAC,CAAC,OAAO,sBAAsB,CAAC;YAClD,KAAK,YAAY,CAAC,CAAC,OAAO,sBAAsB,CAAC;YACjD,KAAK,eAAe,CAAC,CAAC,OAAO,sBAAsB,CAAC;YACpD,KAAK,SAAS,CAAC,CAAC,OAAO,uBAAuB,CAAC;YAC/C,KAAK,UAAU,CAAC,CAAC,OAAO,qBAAqB,CAAC;YAC9C,OAAO,CAAC,CAAC,OAAO,qBAAqB,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;SAEK;IACG,0BAA0B,CAAC,OAAoB;QACrD,+CAA+C;QAC/C,IAAI,UAAU,GAAG,GAAG,CAAC;QAErB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO;YAAE,UAAU,GAAG,GAAG,CAAC;aAC9C,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS;YAAE,UAAU,GAAG,GAAG,CAAC;aACrD,IAAI,OAAO,CAAC,QAAQ,KAAK,YAAY;YAAE,UAAU,GAAG,GAAG,CAAC;QAE7D,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,UAAkB;QAC/C,IAAI,UAAU,IAAI,GAAG;YAAE,OAAO,IAAI,CAAC;QACnC,IAAI,UAAU,IAAI,GAAG;YAAE,OAAO,IAAI,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAClB,KAAK,CAAC,WAAW,CAAC,KAAmB,EAAE,QAA6B;QAC1E,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;YAC5C,MAAM,EAAE,6BAA6B,QAAQ,CAAC,UAAU,KAAK,IAAI,UAAU;YAC3E,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAmB,EAAE,QAA6B;QAC3E,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;YAC5C,MAAM,EAAE,8CAA8C,QAAQ,CAAC,UAAU,KAAK,IAAI,UAAU;YAC5F,IAAI,EAAE,MAAM;SACb,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAmB,EAAE,QAA6B;QAC5E,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;YAC5C,MAAM,EAAE,6CAA6C,QAAQ,CAAC,UAAU,KAAK,IAAI,UAAU;YAC3F,IAAI,EAAE,OAAO;SACd,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,6CAA6C;YAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBACrD,OAAO,EAAE,WAAW;gBACpB,QAAQ,EAAE,QAAQ,CAAC,UAAU;aAC9B,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAmB,EAAE,QAA6B;QAC3E,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;YAC5C,MAAM,EAAE,mDAAmD,QAAQ,CAAC,UAAU,KAAK,IAAI,UAAU;YACjG,IAAI,EAAE,UAAU;SACjB,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAmB,EAAE,QAA6B;QAC3E,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;YAC5C,MAAM,EAAE,wDAAwD,QAAQ,CAAC,UAAU,KAAK,IAAI,UAAU;YACtG,IAAI,EAAE,eAAe;SACtB,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED,iBAAiB;IACT,iBAAiB,CAAC,GAAW;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC/D,OAAO,MAAM,CAAC,MAAM,CAAC;QACvB,CAAC;QACD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,iBAAiB,CAAC,GAAW,EAAE,MAA0B;QAC/D,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YACxD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACjE,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAChD,uCAAuC;QACvC,MAAM,SAAS,GAAG;YAChB,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;YAC1C,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;YAC1C,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;SACrE,CAAC;QAEF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC1D,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,MAAM,CAAC;gBACP,+BAA+B;YACjC,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO;QACL,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;CACF;AA7rBD,gDA6rBC", "sourcesContent": ["/**\n * AI-Powered CodeLens Provider - Immersive code understanding\n * \n * Provides intelligent CodeLens insights directly in the editor using existing\n * Codessa agents and tools for deep code analysis.\n */\n\nimport * as vscode from 'vscode';\nimport { SupervisorAgent } from '../../agents/agentTypes/supervisorAgent';\nimport { ToolRegistry } from '../../tools/toolRegistry';\nimport { MemoryManager } from '../../memory/memoryManager';\nimport { logger } from '../../logger';\nimport { promptManager } from '../../prompts/promptManager';\n\nexport interface AICodeLens extends vscode.CodeLens {\n  insight: CodeInsight;\n  confidence: number;\n  actionable: boolean;\n}\n\nexport interface CodeInsight {\n  type: 'complexity' | 'performance' | 'security' | 'documentation' | 'testing' | 'refactoring' | 'explanation';\n  title: string;\n  description: string;\n  severity: 'info' | 'warning' | 'error' | 'suggestion';\n  actionCommand?: string;\n  actionArgs?: unknown[];\n  relatedCode?: vscode.Range[];\n}\n\nexport interface CodeAnalysisResult {\n  insights: CodeInsight[];\n  metrics: {\n    complexity: number;\n    maintainability: number;\n    testCoverage: number;\n    performance: number;\n  };\n  suggestions: string[];\n}\n\nexport class AICodeLensProvider implements vscode.CodeLensProvider {\n  private supervisorAgent: SupervisorAgent;\n  private toolRegistry: ToolRegistry;\n  private memoryManager: MemoryManager;\n  private disposables: vscode.Disposable[] = [];\n\n  // Performance optimization\n  private analysisCache: Map<string, { result: CodeAnalysisResult; timestamp: number }> = new Map();\n  private readonly cacheExpiry = 60000; // 1 minute\n  private readonly maxCacheSize = 50;\n\n  // Analysis state\n  private isAnalyzing: Map<string, boolean> = new Map();\n\n  constructor(\n    supervisorAgent: SupervisorAgent,\n    toolRegistry: ToolRegistry,\n    memoryManager: MemoryManager\n  ) {\n    this.supervisorAgent = supervisorAgent;\n    this.toolRegistry = toolRegistry;\n    this.memoryManager = memoryManager;\n\n    this.registerProvider();\n    this.registerCommands();\n  }\n\n  private registerProvider(): void {\n    // Register for multiple languages\n    const languages = ['typescript', 'javascript', 'python', 'java', 'csharp', 'cpp', 'go', 'rust'];\n\n    for (const language of languages) {\n      const provider = vscode.languages.registerCodeLensProvider(\n        { language, scheme: 'file' },\n        this\n      );\n      this.disposables.push(provider);\n    }\n\n    logger.info(`AI CodeLens provider registered for ${languages.length} languages`);\n  }\n\n  private registerCommands(): void {\n    // Register commands for CodeLens actions\n    const explainCommand = vscode.commands.registerCommand('codessa.explainCode', (range: vscode.Range, document: vscode.TextDocument) => {\n      this.explainCode(range, document);\n    });\n    this.disposables.push(explainCommand);\n\n    const optimizeCommand = vscode.commands.registerCommand('codessa.optimizeCode', (range: vscode.Range, document: vscode.TextDocument) => {\n      this.optimizeCode(range, document);\n    });\n    this.disposables.push(optimizeCommand);\n\n    const generateTestsCommand = vscode.commands.registerCommand('codessa.generateTests', (range: vscode.Range, document: vscode.TextDocument) => {\n      this.generateTests(range, document);\n    });\n    this.disposables.push(generateTestsCommand);\n\n    const refactorCommand = vscode.commands.registerCommand('codessa.refactorCode', (range: vscode.Range, document: vscode.TextDocument) => {\n      this.refactorCode(range, document);\n    });\n    this.disposables.push(refactorCommand);\n\n    const documentCommand = vscode.commands.registerCommand('codessa.documentCode', (range: vscode.Range, document: vscode.TextDocument) => {\n      this.documentCode(range, document);\n    });\n    this.disposables.push(documentCommand);\n\n    // Additional commands referenced by CodeLenses\n    const showInsightCommand = vscode.commands.registerCommand('codessa.showInsight', async (insight: CodeInsight, range?: vscode.Range, document?: vscode.TextDocument) => {\n      // Display a quick detail and offer to run the associated action\n      const actionLabel = insight.actionCommand ? 'Run Action' : undefined;\n      const selection = await vscode.window.showInformationMessage(`${insight.title}\\n\\n${insight.description}`, ...(actionLabel ? [actionLabel] : []));\n      if (selection === 'Run Action' && insight.actionCommand) {\n        await vscode.commands.executeCommand(insight.actionCommand, range, document);\n      }\n    });\n    this.disposables.push(showInsightCommand);\n\n    const showCodeAnalysisCommand = vscode.commands.registerCommand('codessa.showCodeAnalysis', async (analysis: CodeAnalysisResult) => {\n      // Open a virtual document with analysis details\n      const detailLines: string[] = [];\n      detailLines.push('# Code Analysis Summary');\n      detailLines.push(`- Insights: ${analysis.insights.length}`);\n      detailLines.push(`- Complexity: ${analysis.metrics.complexity}`);\n      detailLines.push(`- Maintainability: ${analysis.metrics.maintainability}`);\n      detailLines.push(`- Test Coverage: ${analysis.metrics.testCoverage}`);\n      detailLines.push(`- Performance: ${analysis.metrics.performance}`);\n      detailLines.push('\\n## Insights');\n      for (const ins of analysis.insights) {\n        detailLines.push(`- [${ins.severity}] (${ins.type}) ${ins.title} - ${ins.description}`);\n      }\n      const doc = await vscode.workspace.openTextDocument({ language: 'markdown', content: detailLines.join('\\n') });\n      await vscode.window.showTextDocument(doc, { preview: true });\n    });\n    this.disposables.push(showCodeAnalysisCommand);\n\n    const showSecurityIssuesCommand = vscode.commands.registerCommand('codessa.showSecurityIssues', async (range?: vscode.Range, document?: vscode.TextDocument) => {\n      const activeDoc = document ?? vscode.window.activeTextEditor?.document;\n      if (!activeDoc) return;\n      const securityTool = this.toolRegistry.getTool('securityScan');\n      if (!securityTool) {\n        vscode.window.showWarningMessage('Security scan tool is not available.');\n        return;\n      }\n      const result = await securityTool.execute('scanFile', { filePath: activeDoc.uri.fsPath });\n      const issuesArray = (result.output as { issues?: Array<{ message?: string }> }).issues;\n      if (result.success && result.output && Array.isArray(issuesArray)) {\n        const issues = issuesArray;\n        const lines = ['# Security Issues', ...issues.map((i, idx) => `${idx + 1}. ${i.message}`)];\n        const doc = await vscode.workspace.openTextDocument({ language: 'markdown', content: lines.join('\\n') });\n        await vscode.window.showTextDocument(doc, { preview: true });\n      } else {\n        vscode.window.showInformationMessage('No security issues found.');\n      }\n    });\n    this.disposables.push(showSecurityIssuesCommand);\n\n    const fixSecurityCommand = vscode.commands.registerCommand('codessa.fixSecurity', async (range?: vscode.Range, document?: vscode.TextDocument) => {\n      const activeDoc = document ?? vscode.window.activeTextEditor?.document;\n      if (!activeDoc) return;\n      const code = range ? activeDoc.getText(range) : activeDoc.getText();\n      const result = await this.supervisorAgent.run({\n        prompt: `Identify and fix security vulnerabilities in this code. Provide fixed code only.\\n\\n\\`\\`\\`${activeDoc.languageId}\\n${code}\\n\\`\\`\\``,\n        mode: 'refactor'\n      });\n      if (result.success && result.output) {\n        const edit = new vscode.WorkspaceEdit();\n        const targetRange = range ?? new vscode.Range(0, 0, activeDoc.lineCount, 0);\n        edit.replace(activeDoc.uri, targetRange, result.output);\n        await vscode.workspace.applyEdit(edit);\n        vscode.window.showInformationMessage('Applied security fixes.');\n      }\n    });\n    this.disposables.push(fixSecurityCommand);\n  }\n\n  /**\n     * Main CodeLens provider method\n     */\n  async provideCodeLenses(\n    document: vscode.TextDocument,\n    token: vscode.CancellationToken\n  ): Promise<vscode.CodeLens[]> {\n    try {\n      // Check if we should provide CodeLenses for this document\n      if (!this.shouldProvideCodeLenses(document)) {\n        return [];\n      }\n\n      // Get or perform code analysis\n      const analysisResult = await this.getCodeAnalysis(document, token);\n      if (!analysisResult) {\n        return [];\n      }\n\n      // Convert insights to CodeLenses\n      const codeLenses = this.convertInsightsToCodeLenses(analysisResult, document);\n\n      logger.info(`Provided ${codeLenses.length} CodeLenses for ${document.fileName}`);\n      return codeLenses;\n\n    } catch (error) {\n      logger.error(`CodeLens provider error: ${error}`);\n      return [];\n    }\n  }\n\n  /**\n     * Resolve CodeLens with additional information\n     */\n  async resolveCodeLens(\n    codeLens: vscode.CodeLens,\n    token: vscode.CancellationToken\n  ): Promise<vscode.CodeLens> {\n    if (token.isCancellationRequested) {\n      return codeLens;\n    }\n\n    const aiCodeLens = codeLens as AICodeLens;\n\n    // Add confidence indicator to title\n    const confidenceIndicator = this.getConfidenceIndicator(aiCodeLens.confidence);\n    aiCodeLens.command = {\n      title: `${confidenceIndicator} ${aiCodeLens.insight.title}`,\n      command: aiCodeLens.insight.actionCommand || 'codessa.showInsight',\n      arguments: [aiCodeLens.insight, aiCodeLens.range]\n    };\n\n    return aiCodeLens;\n  }\n\n  /**\n     * Determine if we should provide CodeLenses for this document\n     */\n  private shouldProvideCodeLenses(document: vscode.TextDocument): boolean {\n    // Skip for very large files (performance)\n    if (document.lineCount > 1000) {\n      return false;\n    }\n\n    // Skip for certain file types\n    const skipExtensions = ['.md', '.txt', '.json', '.xml'];\n    const fileExtension = document.fileName.toLowerCase().substring(document.fileName.lastIndexOf('.'));\n    if (skipExtensions.includes(fileExtension)) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n     * Get or perform code analysis\n     */\n  private async getCodeAnalysis(\n    document: vscode.TextDocument,\n    token: vscode.CancellationToken\n  ): Promise<CodeAnalysisResult | null> {\n    const cacheKey = `${document.uri.fsPath}_${document.version}`;\n\n    // Check cache first\n    const cached = this.getCachedAnalysis(cacheKey);\n    if (cached) {\n      return cached;\n    }\n\n    // Check if already analyzing\n    if (this.isAnalyzing.get(cacheKey)) {\n      return null;\n    }\n\n    this.isAnalyzing.set(cacheKey, true);\n\n    try {\n      if (token.isCancellationRequested) {\n        return null;\n      }\n      const analysisResult = await this.performCodeAnalysis(document, token);\n\n      // Cache the result\n      this.setCachedAnalysis(cacheKey, analysisResult);\n\n      return analysisResult;\n    } finally {\n      this.isAnalyzing.delete(cacheKey);\n    }\n  }\n\n  /**\n     * Perform comprehensive code analysis using existing tools\n     */\n  private async performCodeAnalysis(\n    document: vscode.TextDocument,\n    _token: vscode.CancellationToken\n  ): Promise<CodeAnalysisResult> {\n    const insights: CodeInsight[] = [];\n    const metrics = {\n      complexity: 0,\n      maintainability: 0,\n      testCoverage: 0,\n      performance: 0\n    };\n    const suggestions: string[] = [];\n\n    try {\n      // Use existing code analysis tools\n      const complexityTool = this.toolRegistry.getTool('codeComplexity');\n      if (complexityTool) {\n        const complexityResult = await complexityTool.execute('analyzeFile', {\n          filePath: document.uri.fsPath,\n          content: document.getText()\n        });\n\n        if (complexityResult.success && complexityResult.output) {\n          const complexity = complexityResult.output as { cyclomaticComplexity?: number; maintainabilityIndex?: number };\n          metrics.complexity = complexity.cyclomaticComplexity || 0;\n          if (typeof complexity.maintainabilityIndex === 'number') {\n            metrics.maintainability = complexity.maintainabilityIndex;\n          } else {\n            // Heuristic maintainability if not provided: inverse of complexity\n            metrics.maintainability = Math.max(0, 100 - metrics.complexity * 5);\n          }\n\n          // Add complexity insights\n          if (metrics.complexity > 10) {\n            insights.push({\n              type: 'complexity',\n              title: `High Complexity (${metrics.complexity})`,\n              description: 'This function has high cyclomatic complexity and may be difficult to maintain',\n              severity: 'warning',\n              actionCommand: 'codessa.refactorCode'\n            });\n            suggestions.push('Consider breaking complex functions into smaller, testable units.');\n          }\n        }\n      }\n\n      // Security analysis\n      const securityTool = this.toolRegistry.getTool('securityScan');\n      if (securityTool) {\n        const securityResult = await securityTool.execute('scanFile', {\n          filePath: document.uri.fsPath\n        });\n\n        if (securityResult.success && securityResult.output) {\n          const vulnerabilities = securityResult.output as { issues?: Array<{ severity?: string; message?: string }> };\n          if (vulnerabilities.issues && vulnerabilities.issues.length > 0) {\n            insights.push({\n              type: 'security',\n              title: `${vulnerabilities.issues.length} Security Issue(s)`,\n              description: 'Potential security vulnerabilities detected',\n              severity: 'error',\n              actionCommand: 'codessa.showSecurityIssues'\n            });\n            suggestions.push('Review security issues and apply recommended fixes.');\n          }\n        }\n      }\n\n      // Performance analysis using AI\n      if (!(_token && _token.isCancellationRequested)) {\n        const performanceInsights = await this.analyzePerformance(document);\n        insights.push(...performanceInsights);\n        // Simple performance metric: number of performance insights\n        metrics.performance = performanceInsights.length;\n      }\n\n      // Documentation analysis\n      if (!(_token && _token.isCancellationRequested)) {\n        const documentationInsights = await this.analyzeDocumentation(document);\n        insights.push(...documentationInsights);\n      }\n\n      // Testing analysis\n      if (!(_token && _token.isCancellationRequested)) {\n        const testingInsights = await this.analyzeTestCoverage(document);\n        insights.push(...testingInsights);\n        // Heuristic test coverage metric\n        metrics.testCoverage = testingInsights.length === 0 ? 80 : 40;\n      }\n\n    } catch (error) {\n      logger.error(`Code analysis failed: ${error}`);\n    }\n\n    const result: CodeAnalysisResult = { insights, metrics, suggestions };\n\n    // Persist a concise record to memory for later retrieval/search\n    try {\n      await this.memoryManager.addMemory({\n        content: `Code analysis for ${document.fileName}: ${insights.length} insights. Complexity ${metrics.complexity}, Maintainability ${metrics.maintainability}, Performance ${metrics.performance}, TestCoverage ${metrics.testCoverage}.`,\n        metadata: {\n          source: 'code',\n          type: 'insight',\n          tags: ['code', 'analysis', document.languageId]\n        }\n      });\n    } catch (e) {\n      logger.debug('Failed to persist analysis memory (non-critical):', e);\n    }\n\n    return result;\n  }\n\n  /**\n     * Analyze performance using AI\n     */\n  private async analyzePerformance(document: vscode.TextDocument): Promise<CodeInsight[]> {\n    const insights: CodeInsight[] = [];\n\n    try {\n      const prompt = promptManager.renderPrompt('analysis.performance', {\n        codeContent: document.getText(),\n        languageId: document.languageId\n      });\n\n      const result = await this.supervisorAgent.run({\n        prompt,\n        mode: 'ask'\n      });\n\n      if (result.success && typeof result.output === 'string') {\n        const parsed = this.parseAIInsights(result.output, 'performance');\n        insights.push(...parsed);\n      }\n    } catch (error) {\n      logger.warn(`Performance analysis failed: ${error}`);\n    }\n\n    return insights;\n  }\n\n  /**\n     * Analyze documentation coverage\n     */\n  private async analyzeDocumentation(document: vscode.TextDocument): Promise<CodeInsight[]> {\n    const insights: CodeInsight[] = [];\n    const text = document.getText();\n\n    // Simple heuristic: check for functions without documentation\n    const functionRegex = /(?:function|def|class|interface|type)\\s+(\\w+)/g;\n    const docRegex = /\\/\\*\\*[\\s\\S]*?\\*\\/|#\\s*.*|\"\"\"[\\s\\S]*?\"\"\"/g;\n\n    const functions = Array.from(text.matchAll(functionRegex));\n    const docs = Array.from(text.matchAll(docRegex));\n\n    if (functions.length > 0 && docs.length < functions.length * 0.5) {\n      insights.push({\n        type: 'documentation',\n        title: 'Missing Documentation',\n        description: `${functions.length - docs.length} functions/classes lack documentation`,\n        severity: 'suggestion',\n        actionCommand: 'codessa.documentCode'\n      });\n    }\n\n    return insights;\n  }\n\n  /**\n     * Analyze test coverage\n     */\n  private async analyzeTestCoverage(document: vscode.TextDocument): Promise<CodeInsight[]> {\n    const insights: CodeInsight[] = [];\n\n    // Check if this is a test file\n    const isTestFile = document.fileName.includes('.test.') ||\n      document.fileName.includes('.spec.') ||\n      document.fileName.includes('test/') ||\n      document.fileName.includes('tests/');\n\n    if (!isTestFile) {\n      // Check if corresponding test file exists\n      const testFileExists = await this.checkTestFileExists(document.uri.fsPath);\n      if (!testFileExists) {\n        insights.push({\n          type: 'testing',\n          title: 'No Tests Found',\n          description: 'No corresponding test file found for this module',\n          severity: 'suggestion',\n          actionCommand: 'codessa.generateTests'\n        });\n      }\n    }\n\n    return insights;\n  }\n\n  /**\n     * Convert insights to CodeLenses\n     */\n  private convertInsightsToCodeLenses(\n    analysisResult: CodeAnalysisResult,\n    document: vscode.TextDocument\n  ): AICodeLens[] {\n    const codeLenses: AICodeLens[] = [];\n\n    // Add insights as CodeLenses\n    for (const insight of analysisResult.insights) {\n      // Determine position for CodeLens\n      let range = new vscode.Range(0, 0, 0, 0); // Default to top of file\n\n      // Try to find relevant line for the insight\n      if (insight.type === 'complexity' || insight.type === 'performance') {\n        // Find function definitions\n        const functionMatch = document.getText().match(/(?:function|def|class)\\s+\\w+/);\n        if (functionMatch) {\n          const position = document.positionAt(document.getText().indexOf(functionMatch[0]));\n          range = new vscode.Range(position, position);\n        }\n      }\n\n      const codeLens: AICodeLens = {\n        range,\n        insight,\n        confidence: this.calculateInsightConfidence(insight),\n        actionable: !!insight.actionCommand,\n        isResolved: true,\n        command: {\n          title: insight.title,\n          command: insight.actionCommand || 'codessa.showInsight',\n          arguments: [insight, range, document]\n        }\n      };\n\n      codeLenses.push(codeLens);\n    }\n\n    // Add summary CodeLens at the top\n    if (analysisResult.insights.length > 0) {\n      const summaryCodeLens: AICodeLens = {\n        range: new vscode.Range(0, 0, 0, 0),\n        insight: {\n          type: 'explanation',\n          title: `📊 Code Analysis: ${analysisResult.insights.length} insights`,\n          description: 'Click to view detailed code analysis',\n          severity: 'info',\n          actionCommand: 'codessa.showCodeAnalysis'\n        },\n        confidence: 1.0,\n        actionable: true,\n        isResolved: true,\n        command: {\n          title: `📊 Code Analysis: ${analysisResult.insights.length} insights`,\n          command: 'codessa.showCodeAnalysis',\n          arguments: [analysisResult, document]\n        }\n      };\n\n      codeLenses.unshift(summaryCodeLens);\n    }\n\n    return codeLenses;\n  }\n\n  /**\n     * Parse AI insights from response\n     */\n  private parseAIInsights(response: string, type: string): CodeInsight[] {\n    const insights: CodeInsight[] = [];\n\n    try {\n      const jsonMatch = response.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0]);\n        if (parsed.insights && Array.isArray(parsed.insights)) {\n          for (const insight of parsed.insights) {\n            insights.push({\n              type: type as CodeInsight['type'],\n              title: insight.title || 'AI Insight',\n              description: insight.description || '',\n              severity: insight.severity || 'info',\n              actionCommand: this.getActionCommandForType(type)\n            });\n          }\n        }\n      }\n    } catch (error) {\n      logger.warn(`Failed to parse AI insights: ${error}`);\n    }\n\n    return insights;\n  }\n\n  /**\n     * Get action command for insight type\n     */\n  private getActionCommandForType(type: string): string {\n    switch (type) {\n      case 'performance': return 'codessa.optimizeCode';\n      case 'complexity': return 'codessa.refactorCode';\n      case 'documentation': return 'codessa.documentCode';\n      case 'testing': return 'codessa.generateTests';\n      case 'security': return 'codessa.fixSecurity';\n      default: return 'codessa.explainCode';\n    }\n  }\n\n  /**\n     * Calculate confidence for insight\n     */\n  private calculateInsightConfidence(insight: CodeInsight): number {\n    // Base confidence on insight type and severity\n    let confidence = 0.5;\n\n    if (insight.severity === 'error') confidence = 0.9;\n    else if (insight.severity === 'warning') confidence = 0.7;\n    else if (insight.severity === 'suggestion') confidence = 0.6;\n\n    return confidence;\n  }\n\n  /**\n     * Get confidence indicator emoji\n     */\n  private getConfidenceIndicator(confidence: number): string {\n    if (confidence >= 0.8) return '🎯';\n    if (confidence >= 0.6) return '💡';\n    return '💭';\n  }\n\n  // Command implementations\n  private async explainCode(range: vscode.Range, document: vscode.TextDocument): Promise<void> {\n    const code = document.getText(range);\n    const result = await this.supervisorAgent.run({\n      prompt: `Explain this code:\\n\\`\\`\\`${document.languageId}\\n${code}\\n\\`\\`\\``,\n      mode: 'ask'\n    });\n\n    if (result.success && result.output) {\n      vscode.window.showInformationMessage(result.output);\n    }\n  }\n\n  private async optimizeCode(range: vscode.Range, document: vscode.TextDocument): Promise<void> {\n    const code = document.getText(range);\n    const result = await this.supervisorAgent.run({\n      prompt: `Optimize this code for performance:\\n\\`\\`\\`${document.languageId}\\n${code}\\n\\`\\`\\``,\n      mode: 'edit'\n    });\n\n    if (result.success && result.output) {\n      const edit = new vscode.WorkspaceEdit();\n      edit.replace(document.uri, range, result.output);\n      vscode.workspace.applyEdit(edit);\n    }\n  }\n\n  private async generateTests(range: vscode.Range, document: vscode.TextDocument): Promise<void> {\n    const code = document.getText(range);\n    const result = await this.supervisorAgent.run({\n      prompt: `Generate unit tests for this code:\\n\\`\\`\\`${document.languageId}\\n${code}\\n\\`\\`\\``,\n      mode: 'agent'\n    });\n\n    if (result.success && result.output) {\n      // Create new test file or show in new editor\n      const testContent = result.output;\n      const newDoc = await vscode.workspace.openTextDocument({\n        content: testContent,\n        language: document.languageId\n      });\n      vscode.window.showTextDocument(newDoc);\n    }\n  }\n\n  private async refactorCode(range: vscode.Range, document: vscode.TextDocument): Promise<void> {\n    const code = document.getText(range);\n    const result = await this.supervisorAgent.run({\n      prompt: `Refactor this code to reduce complexity:\\n\\`\\`\\`${document.languageId}\\n${code}\\n\\`\\`\\``,\n      mode: 'refactor'\n    });\n\n    if (result.success && result.output) {\n      const edit = new vscode.WorkspaceEdit();\n      edit.replace(document.uri, range, result.output);\n      vscode.workspace.applyEdit(edit);\n    }\n  }\n\n  private async documentCode(range: vscode.Range, document: vscode.TextDocument): Promise<void> {\n    const code = document.getText(range);\n    const result = await this.supervisorAgent.run({\n      prompt: `Add comprehensive documentation to this code:\\n\\`\\`\\`${document.languageId}\\n${code}\\n\\`\\`\\``,\n      mode: 'documentation'\n    });\n\n    if (result.success && result.output) {\n      const edit = new vscode.WorkspaceEdit();\n      edit.replace(document.uri, range, result.output);\n      vscode.workspace.applyEdit(edit);\n    }\n  }\n\n  // Helper methods\n  private getCachedAnalysis(key: string): CodeAnalysisResult | null {\n    const cached = this.analysisCache.get(key);\n    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {\n      return cached.result;\n    }\n    if (cached) {\n      this.analysisCache.delete(key);\n    }\n    return null;\n  }\n\n  private setCachedAnalysis(key: string, result: CodeAnalysisResult): void {\n    if (this.analysisCache.size >= this.maxCacheSize) {\n      const firstKey = this.analysisCache.keys().next().value;\n      if (firstKey) {\n        this.analysisCache.delete(firstKey);\n      }\n    }\n    this.analysisCache.set(key, { result, timestamp: Date.now() });\n  }\n\n  private async checkTestFileExists(filePath: string): Promise<boolean> {\n    // Simple check for test file existence\n    const testPaths = [\n      filePath.replace(/\\.([^.]+)$/, '.test.$1'),\n      filePath.replace(/\\.([^.]+)$/, '.spec.$1'),\n      filePath.replace(/src\\//, 'test/').replace(/\\.([^.]+)$/, '.test.$1')\n    ];\n\n    for (const testPath of testPaths) {\n      try {\n        await vscode.workspace.fs.stat(vscode.Uri.file(testPath));\n        return true;\n      } catch {\n        // File doesn't exist, continue\n      }\n    }\n    return false;\n  }\n\n  dispose(): void {\n    this.disposables.forEach(d => d.dispose());\n    this.analysisCache.clear();\n    this.isAnalyzing.clear();\n  }\n}\n"]}