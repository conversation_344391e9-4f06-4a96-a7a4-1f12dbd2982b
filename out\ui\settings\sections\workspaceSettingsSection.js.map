{"version": 3, "file": "workspaceSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/workspaceSettingsSection.ts"], "names": [], "mappings": ";;AAsEA,wEAeC;AAzED,4CAA4C;AAC5C,SAAS,kBAAkB,CAAC,IAAS;IACnC,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC;AAClF,CAAC;AAUD,sDAAsD;AACtD,SAAS,eAAe,CAAC,IAAS;IAChC,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,IAAI,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,UAAmB;IAC3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;IACD,wEAAwE;IACxE,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;QACtB,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAmBD,IAAI,UAAU,GAAgB,EAAE,CAAC;AACjC,IAAI,mBAAmB,GAAkB,IAAI,CAAC;AAE9C;;;;GAIG;AACH,SAAgB,8BAA8B,CAAC,SAAsB,EAAE,QAA2B;IAChG,sCAAsC;IACtC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACtC,8CAA8C;IAC9C,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;IACvC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAC7B,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC1C,sBAAsB;IACtB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;IAC1D,IAAI,MAAM;QAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACrF,gBAAgB;IAChB,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;IAChE,IAAI,SAAS;QAAE,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IACvE,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;IAC5D,IAAI,OAAO;QAAE,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC1E,CAAC;AAED;;;;GAIG;AACH,SAAS,oBAAoB,CAAC,SAAsB,EAAE,QAA2B;IAC/E,sCAAsC;IACtC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;IAC7C,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAE7B,gDAAgD;IAChD,MAAM,eAAe,GAAG,UAA2D,CAAC;IAEpF,IAAI,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;aA+DP,CAAC;IACZ,WAAW,IAAI,kDAAkD,CAAC;IAClE,WAAW,IAAI,oHAAoH,CAAC;IACpI,WAAW,IAAI,6BAA6B,CAAC;IAC7C,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjC,WAAW,IAAI,yIAAyI,CAAC;IAC3J,CAAC;SAAM,CAAC;QACN,WAAW,IAAI,uCAAuC;YACpD,+GAA+G;YAC/G,sBAAsB,CAAC;QACzB,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;YAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,eAAe,KAAK,EAAE,CAAC,EAAE,CAAC;YACpD,WAAW,IAAI;6CACwB,EAAE,CAAC,IAAI,IAAI,EAAE;6CACb,EAAE,CAAC,IAAI,IAAI,EAAE;0CAChB,EAAE,CAAC,WAAW,IAAI,EAAE;sBACxC,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,8BAA8B,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;sBAC/E,QAAQ,CAAC,CAAC,CAAC,6CAA6C,CAAC,CAAC,CAAC,EAAE;;+EAEJ,GAAG,sCAAsC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ;6EAC1E,GAAG;sFACM,GAAG;;kBAEvE,CAAC;QACf,CAAC,CAAC,CAAC;QACH,WAAW,IAAI,kBAAkB,CAAC;IACpC,CAAC;IACD,WAAW,IAAI,QAAQ,CAAC;IACxB,oBAAoB;IACpB,WAAW,IAAI,uDAAuD,CAAC;IACvE,SAAS,CAAC,SAAS,GAAG,WAAW,CAAC;IAClC,mBAAmB;IACnB,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,mBAAmB,CAAgB,CAAC;IAC5E,OAAO,EAAE,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC3D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACnE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;oBAAE,kBAAkB,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;YACjF,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC7D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;oBAAE,eAAe,CAAC,SAAS,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC7D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;oBAAE,eAAe,CAAC,SAAS,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,aAAa;IACb,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;IAC1D,IAAI,MAAM;QAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACvF,CAAC;AAED,iFAA4I;AAE5I,SAAS,sBAAsB,CAAC,MAAc,EAAE,MAA4B;IAC1E,OAAO,CAAC,GAAG,CAAC,yBAAyB,MAAM,EAAE,EAAE,MAAM,CAAC,CAAC;AACzD,CAAC;AAED;;;;;;GAMG;AACH,SAAS,kBAAkB,CAAC,SAAsB,EAAE,KAAyB,EAAE,EAAE,MAAqB,IAAI,EAAE,QAA2B;IACrI,mBAAmB,GAAG,GAAG,CAAC;IAC1B,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,iBAAiB,CAAgB,CAAC;IACxE,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAqB,CAAC;IAC/E,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAqB,CAAC;IAC/E,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAwB,CAAC;IACzF,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAqB,CAAC;IAEnF,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE,CAAC;QACrE,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACnD,OAAO;IACT,CAAC;IAED,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAA6B,CAAC;IACxF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAA6B,CAAC;IAE5F,MAAM,aAAa,GAA2B;QAC5C,KAAK;QACL,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;KACV,CAAC;IACF,OAAO;IACP,MAAM,IAAI,GAAG;QACX,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;QACnC,EAAE,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE;QAC/C,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;QAC/B,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE;QACrC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE;QACvC,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE;KACvC,CAAC;IACF,IAAI,SAAS,GAAG,SAAS,CAAC;IAC1B,MAAM,UAAU,GAAG,UAAU,WAAmB;QAC9C,SAAS,GAAG,WAAW,CAAC;QACxB,sBAAsB,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;QAC7G,MAAM,IAAI,GAAG;;sBAEK,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB;;sBAEjD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,+BAA+B,WAAW,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,eAAe,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,KAAK,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;;;;;eAQrJ,CAAC;QACZ,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAC9B,gBAAgB;QAChB,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACrD,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBAClC,MAAM,KAAK,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBACjE,IAAI,KAAK;oBAAE,UAAU,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,gBAAgB;QAChB,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;QAChE,IAAI,SAAS;YAAE,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACvE,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;QAC5D,IAAI,OAAO;YAAE,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACxE,qBAAqB;QACrB,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,SAAS,gBAAgB,CAAC,KAAa;QACrC,MAAM,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,sBAAsB,CAAgB,CAAC;QAC3E,IAAI,CAAC,OAAO;YAAE,OAAO;QACrB,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO,CAAC,SAAS,GAAG;;uEAE6C,EAAE,CAAC,IAAI,IAAI,EAAE;;;uEAGb,EAAE,CAAC,IAAI,IAAI,EAAE;;;qFAGC,EAAE,CAAC,WAAW,IAAI,EAAE;;aAE5F,CAAC;QACV,CAAC;aAAM,IAAI,KAAK,KAAK,eAAe,EAAE,CAAC;YACrC,MAAM,sBAAsB,GAAG,EAAE,CAAC,aAAa,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YAClF,MAAM,aAAa,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;YACrE,OAAO,CAAC,SAAS,GAAG;;2EAEiD,aAAa;;;aAG3E,CAAC;YACR,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,gCAAgC,CAAgB,CAAC;YACzF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAqB,CAAC;YAI1F,IAAI,SAAS,GAA0B,EAAE,CAAC;YAC1C,IAAI,sBAAsB,CAAC,MAAM,EAAE,CAAC;gBAClC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC;oBACxD,QAAQ,CAAC,oBAA6C,CAAC,CAAC,CAAC,EAAE,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC;oBACzD,sBAAsB,CAAC,OAAgC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjE,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;YAEjD,uDAAuD;YACvD,MAAM,wBAAwB,GAAG,SAAS,CAAC;YAO3C,MAAM,WAAW,GAAG,MAA4C,CAAC;YACjE,MAAM,eAAe,GAAG,WAAW,CAAC,oBAAoB,CAAC;YACzD,WAAW,CAAC,oBAAoB,GAAG,wBAAwB,CAAC;YAE5D,IAAA,uDAAwB,EAAC,SAAS,CAAC,CAAC;YAEpC,2BAA2B;YAC1B,MAAc,CAAC,oBAAoB,GAAG,eAAe,CAAC;YAEvD,iEAAiE;YACjE,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;YAC/D,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;oBACpC,IAAA,qDAAsB,EAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC,CAAC,CAAC;YACL,CAAC;YACD,gBAAgB,EAAE,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE;gBACjD,sBAAsB,CAAC,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC;gBACzD,gBAAgB,CAAC,eAAe,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;YAC7B,2BAA2B;YAC3B,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC;YAC1B,MAAM,gBAAgB,GAAG;gBACvB,IAAI,IAAI,GAAG,kHAAkH,CAAC;gBAC9H,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACvC,IAAI,IAAI,+DAA+D,CAAC;gBAC1E,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,gJAAgJ,CAAC;oBACzJ,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;wBAC7B,0BAA0B;wBAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;wBACvF,IAAI,IAAI;oDACgC,IAAI,CAAC,IAAI,IAAI,EAAE;oDACf,IAAI,CAAC,IAAI,IAAI,EAAE;oDACf,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;kCAC7C,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,8BAA8B,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;yFAE1B,GAAG;kGACM,GAAG;;8BAEvE,CAAC;oBACrB,CAAC,CAAC,CAAC;oBACH,IAAI,IAAI,kBAAkB,CAAC;gBAC7B,CAAC;gBACD,OAAO,CAAC,SAAS,GAAG,IAAI,GAAG,2DAA2D,CAAC;gBACvF,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;wBACnE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gCAAE,aAAa,CAAC,GAAG,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;wBACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;gCAChD,EAAE,CAAC,KAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gCACzB,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;gCACtC,gBAAgB,EAAE,CAAC;4BACrB,CAAC;wBACH,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;gBAC9D,IAAI,MAAM;oBAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC,CAAC;YACF,MAAM,aAAa,GAAG,UAAU,OAAsB;gBACpD,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAgB,CAAC;gBAC1E,MAAM,IAAI,GAAG,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBACpG,KAAK,CAAC,SAAS,GAAG;;;kCAGQ,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW;wFACW,IAAI,CAAC,IAAI,IAAI,EAAE;wFACf,IAAI,CAAC,IAAI,IAAI,EAAE;;;0DAG7C,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;yDACxC,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;0DACrC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;2DACtC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;;;0GAGO,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;2BAM3G,CAAC;gBACpB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBAC7B,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC9D,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC7F,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC7F,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAuB,CAAC,KAAK,CAAC;oBACvF,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACpI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;wBAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;wBAAC,OAAO;oBAAC,CAAC;oBACrE,+CAA+C;oBAC/C,MAAM,OAAO,GAAkB;wBAC7B,IAAI;wBACJ,IAAI;wBACJ,IAAI,EAAE,IAAgB,EAAE,sDAAsD;wBAC9E,IAAI;wBACJ,QAAQ,EAAE,EAAE;wBACZ,UAAU,EAAE,KAAK;wBACjB,QAAQ,EAAE,KAAK;wBACf,QAAQ,EAAE,OAAO;qBAClB,CAAC;oBACF,IAAI,OAAO,KAAK,IAAI;wBAAE,EAAE,CAAC,KAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;wBACzC,EAAE,CAAC,KAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;oBAClC,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;oBACtC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;oBAC7B,gBAAgB,EAAE,CAAC;gBACrB,CAAC,CAAC;YACJ,CAAC,CAAC;YACF,gBAAgB,EAAE,CAAC;QACrB,CAAC;aAAM,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;YAC5B,0BAA0B;YAC1B,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;YACxB,MAAM,eAAe,GAAG;gBACtB,IAAI,IAAI,GAAG,oHAAoH,CAAC;gBAChI,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrC,IAAI,IAAI,sEAAsE,CAAC;gBACjF,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,iIAAiI,CAAC;oBAC1I,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;wBAC9B,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBACzH,MAAM,SAAS,GAAG,CAAC,IAAc,EAAU,EAAE;4BAC3C,QAAQ,IAAI,EAAE,CAAC;gCACb,KAAK,OAAO;oCACV,OAAO,4CAA4C,CAAC;gCACtD,KAAK,OAAO;oCACV,OAAO,4CAA4C,CAAC;gCACtD,KAAK,WAAW;oCACd,OAAO,6CAA6C,CAAC;gCACvD,KAAK,OAAO;oCACV,OAAO,qCAAqC,CAAC;gCAC/C,KAAK,QAAQ,CAAC;gCACd;oCACE,OAAO,sCAAsC,CAAC;4BAClD,CAAC;wBACH,CAAC,CAAC;wBAEF,yBAAyB;wBACzB,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBAC/C,IAAI,IAAI;8EAC0D,QAAQ,WAAW,MAAM,CAAC,IAAI,IAAI,EAAE;gDAClE,MAAM,CAAC,KAAK,IAAI,EAAE;+CACnB,SAAS;;yFAEiC,GAAG;kGACM,GAAG;;8BAEvE,CAAC;oBACrB,CAAC,CAAC,CAAC;oBACH,IAAI,IAAI,kBAAkB,CAAC;gBAC7B,CAAC;gBACD,OAAO,CAAC,SAAS,GAAG,IAAI,GAAG,2DAA2D,CAAC;gBACvF,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;wBACnE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gCAAE,aAAa,CAAC,GAAG,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;wBACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,0BAA0B,CAAC,EAAE,CAAC;gCACvD,EAAE,CAAC,IAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gCACxB,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;gCACtC,eAAe,EAAE,CAAC;4BACpB,CAAC;wBACH,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;gBAC9D,IAAI,MAAM;oBAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC,CAAC;YACF,MAAM,aAAa,GAAG,UAAU,OAAsB;gBACpD,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAgB,CAAC;gBAC1E,MAAM,MAAM,GAAG,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;gBAC9F,KAAK,CAAC,SAAS,GAAG;;;kCAGQ,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa;wFACO,MAAM,CAAC,IAAI,IAAI,EAAE;0FACf,MAAM,CAAC,KAAK,IAAI,EAAE;;;2DAGjD,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;4DACzC,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;4DAC3C,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;;;;;;;;2BAQ5E,CAAC;gBACpB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBAC7B,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC9D,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC7F,MAAM,KAAK,GAAI,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC/F,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAuB,CAAC,KAAK,CAAC;oBACvF,IAAI,CAAC,IAAI,EAAE,CAAC;wBAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;wBAAC,OAAO;oBAAC,CAAC;oBAClD,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC7E,qDAAqD;oBACrD,MAAM,SAAS,GAAwB;wBACrC,EAAE;wBACF,IAAI;wBACJ,KAAK;wBACL,IAAI,EAAE,IAAgB,EAAE,sDAAsD;wBAC9E,WAAW,EAAE,EAAE;wBACf,QAAQ,EAAE,IAAI;wBACd,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;qBACrB,CAAC;oBACF,IAAI,OAAO,KAAK,IAAI;wBAAE,EAAE,CAAC,IAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;wBAC1C,EAAE,CAAC,IAAK,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;oBACnC,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;oBACtC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;oBAC7B,eAAe,EAAE,CAAC;gBACpB,CAAC,CAAC;YACJ,CAAC,CAAC;YACF,eAAe,EAAE,CAAC;QACpB,CAAC;QACD,YAAY;QACZ,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;YACtB,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC;YAC1B,MAAM,gBAAgB,GAAG;gBACvB,IAAI,IAAI,GAAG,0FAA0F,CAAC;gBACtG,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACvC,IAAI,IAAI,kEAAkE,CAAC;gBAC7E,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,+HAA+H,CAAC;oBACxI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;wBAC7B,IAAI,IAAI;kCACc,IAAI,CAAC,IAAI,IAAI,EAAE;kCACf,IAAI,CAAC,IAAI,IAAI,EAAE;kCACf,IAAI,CAAC,IAAI,IAAI,EAAE;kCACf,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;mEAEK,GAAG;qEACD,GAAG;;8BAE1C,CAAC;oBACrB,CAAC,CAAC,CAAC;oBACH,IAAI,IAAI,kBAAkB,CAAC;gBAC7B,CAAC;gBACD,OAAO,CAAC,SAAS,GAAG,IAAI,GAAG,2DAA2D,CAAC;gBACvF,kCAAkC;gBAClC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;wBACnE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gCAAE,aAAa,CAAC,GAAG,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;wBACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;gCAChD,EAAE,CAAC,KAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gCACzB,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;gCACtC,gBAAgB,EAAE,CAAC;4BACrB,CAAC;wBACH,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;gBAC9D,IAAI,MAAM;oBAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC,CAAC;YACF,MAAM,aAAa,GAAG,UAAU,OAAsB;gBACpD,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAgB,CAAC;gBAC1E,MAAM,IAAI,GAAG,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBAChG,KAAK,CAAC,SAAS,GAAG;;;kCAGQ,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW;wFACW,IAAI,CAAC,IAAI,IAAI,EAAE;wFACf,IAAI,CAAC,IAAI,IAAI,EAAE;wFACf,IAAI,CAAC,IAAI,IAAI,EAAE;0GACG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;2BAM3G,CAAC;gBACpB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBAC7B,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC9D,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC7F,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC7F,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC7F,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACnI,IAAI,CAAC,IAAI,EAAE,CAAC;wBAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;wBAAC,OAAO;oBAAC,CAAC;oBAClD,MAAM,OAAO,GAAkB;wBAC7B,IAAI;wBACJ,IAAI;wBACJ,IAAI,EAAE,IAAgB;wBACtB,IAAI;wBACJ,QAAQ,EAAE,EAAE;wBACZ,UAAU,EAAE,KAAK;wBACjB,QAAQ,EAAE,KAAK;wBACf,QAAQ,EAAE,OAAO;qBAClB,CAAC;oBACF,IAAI,OAAO,KAAK,IAAI;wBAAE,EAAE,CAAC,KAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;wBACzC,EAAE,CAAC,KAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;oBAClC,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;oBACtC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;oBAC7B,gBAAgB,EAAE,CAAC;gBACrB,CAAC,CAAC;YACJ,CAAC,CAAC;YACF,gBAAgB,EAAE,CAAC;QACrB,CAAC;QAAC,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;YACvB,6BAA6B;YAC7B,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;YACxB,MAAM,eAAe,GAAG;gBACtB,IAAI,IAAI,GAAG,4FAA4F,CAAC;gBACxG,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrC,IAAI,IAAI,yEAAyE,CAAC;gBACpF,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,mHAAmH,CAAC;oBAC5H,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;wBAC9B,IAAI,IAAI;kCACc,MAAM,CAAC,IAAI,IAAI,EAAE;kCACjB,MAAM,CAAC,KAAK,IAAI,EAAE;kCAClB,MAAM,CAAC,IAAI,IAAI,EAAE;;yFAEsC,GAAG;kGACM,GAAG;;8BAEvE,CAAC;oBACrB,CAAC,CAAC,CAAC;oBACH,IAAI,IAAI,kBAAkB,CAAC;gBAC7B,CAAC;gBACD,OAAO,CAAC,SAAS,GAAG,IAAI,GAAG,2DAA2D,CAAC;gBACvF,kCAAkC;gBAClC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;wBACnE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gCAAE,aAAa,CAAC,GAAG,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;wBACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,0BAA0B,CAAC,EAAE,CAAC;gCACvD,EAAE,CAAC,IAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gCACxB,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;gCACtC,eAAe,EAAE,CAAC;4BACpB,CAAC;wBACH,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;gBAC9D,IAAI,MAAM;oBAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC,CAAC;YACF,MAAM,aAAa,GAAG,UAAU,SAAwB;gBACtD,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAgB,CAAC;gBAC1E,MAAM,MAAM,GAAG,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBACpG,KAAK,CAAC,SAAS,GAAG;;;kCAGQ,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa;wFACK,MAAM,CAAC,IAAI,IAAI,EAAE;0FACf,MAAM,CAAC,KAAK,IAAI,EAAE;wFACpB,MAAM,CAAC,IAAI,IAAI,EAAE;;;;;;2BAM9E,CAAC;gBACpB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBAC7B,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC9D,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC7F,MAAM,KAAK,GAAI,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC/F,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC7F,IAAI,CAAC,IAAI,EAAE,CAAC;wBAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;wBAAC,OAAO;oBAAC,CAAC;oBAClD,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC7E,qDAAqD;oBACrD,MAAM,SAAS,GAAwB;wBACrC,EAAE;wBACF,IAAI;wBACJ,KAAK;wBACL,IAAI,EAAE,IAAgB,EAAE,sDAAsD;wBAC9E,WAAW,EAAE,EAAE;wBACf,QAAQ,EAAE,IAAI;wBACd,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;qBACrB,CAAC;oBACF,IAAI,SAAS,KAAK,IAAI;wBAAE,EAAE,CAAC,IAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;wBAC5C,EAAE,CAAC,IAAK,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;oBACrC,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;oBACtC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;oBAC7B,eAAe,EAAE,CAAC;gBACpB,CAAC,CAAC;YACJ,CAAC,CAAC;YACF,eAAe,EAAE,CAAC;QACpB,CAAC;aAAM,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,CAAC,SAAS,GAAG;gHACsF,EAAE,CAAC,MAAM,IAAI,EAAE;mBAC5G,CAAC;QAChB,CAAC;aAAM,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;YAC5B,mCAAmC;YACnC,EAAE,CAAC,aAAa,GAAG,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC;YAC1C,MAAM,eAAe,GAAG;gBACtB,IAAI,IAAI,GAAG,0HAA0H,CAAC;gBACtI,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACvD,IAAI,IAAI,uEAAuE,CAAC;gBAClF,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,iIAAiI,CAAC;oBAC1I,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBACpC,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC7G,IAAI,IAAI;+CAC2B,QAAQ,IAAI,GAAG,CAAC,IAAI;gDACnB,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAE,GAAqB,CAAC,KAAK,IAAI,UAAU;kDAC9E,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;4BAC3D,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;4BAC1E,CAAE,GAAqB,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;gCACzC,GAAqB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;gCAChD,GAAqB,CAAC,KAAK,IAAI,EAAE,CAAC;;yFAEoC,GAAG;kGACM,GAAG;;8BAEvE,CAAC;oBACrB,CAAC,CAAC,CAAC;oBACH,IAAI,IAAI,kBAAkB,CAAC;gBAC7B,CAAC;gBACD,OAAO,CAAC,SAAS,GAAG,IAAI,GAAG,0DAA0D,CAAC;gBACtF,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;wBACnE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gCAAE,YAAY,CAAC,GAAG,CAAC,CAAC;wBACrC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;wBACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;4BAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,iCAAiC,CAAC,EAAE,CAAC;gCAC9D,EAAE,CAAC,aAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gCACjC,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;gCACtC,eAAe,EAAE,CAAC;4BACpB,CAAC;wBACH,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;gBAC7D,IAAI,MAAM;oBAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC,CAAC;YACF,MAAM,YAAY,GAAG,UAAU,MAAqB;gBAClD,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAgB,CAAC;gBACzE,MAAM,GAAG,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,aAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxD,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE;oBACvB,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE,EAAE;oBACT,OAAO,EAAE,EAAE;oBACX,IAAI,EAAE,EAAE;oBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,SAAS,EAAE,MAAM;oBACjB,SAAS,EAAE,MAAM;oBACjB,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,KAAK;oBACf,UAAU,EAAE,KAAK;iBAClB,CAAC;gBACF,KAAK,CAAC,SAAS,GAAG;;;kCAGQ,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,oBAAoB;;;0DAGpC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;0DACtC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;0DACtC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;;;yFAGP,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;6FACvE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;;;;;;2BAM/I,CAAC;gBACpB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBAC7B,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACpG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC7D,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAuB,CAAC,KAAiC,CAAC;oBAClH,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAA4B,CAAC;oBAC3F,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAA4B,CAAC;oBAC/F,MAAM,KAAK,GAAG,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;oBAC7C,MAAM,OAAO,GAAG,YAAY,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;oBACjD,IAAI,CAAC,OAAO,EAAE,CAAC;wBAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;wBAAC,OAAO;oBAAC,CAAC;oBACxD,4DAA4D;oBAC5D,MAAM,MAAM,GAA+B;wBACzC,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;wBACvB,IAAI,EAAE,IAA6B;wBACnC,KAAK,EAAE,KAAK;wBACZ,OAAO,EAAE,OAAO;wBAChB,IAAI,EAAE,EAAE;wBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,SAAS,EAAE,QAAQ;wBACnB,SAAS,EAAE,QAAQ;wBACnB,QAAQ,EAAE,EAAE;wBACZ,QAAQ,EAAE,KAAK;wBACf,UAAU,EAAE,KAAK;qBAClB,CAAC;oBACF,IAAI,MAAM,KAAK,IAAI;wBAAE,EAAE,CAAC,aAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;wBAC/C,EAAE,CAAC,aAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;oBACxC,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;oBACtC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;oBAC7B,eAAe,EAAE,CAAC;gBACpB,CAAC,CAAC;YACJ,CAAC,CAAC;YACF,eAAe,EAAE,CAAC;QACpB,CAAC;QACD,gDAAgD;QAChD,EAAE,CAAC,aAAa,GAAG,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC;QAC1C,MAAM,eAAe,GAAG;YACtB,IAAI,IAAI,GAAG,kGAAkG,CAAC;YAC9G,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvD,IAAI,IAAI,0EAA0E,CAAC;YACrF,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,oHAAoH,CAAC;gBAC7H,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;oBACpC,IAAI,IAAI;kCACgB,GAAG,CAAC,IAAI;kCACR,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,GAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;kCACtG,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;wBAC7C,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;wBAC1E,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;4BACpB,CAAE,GAAqB,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAE,GAAqB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAE,GAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;4BAC7H,EAAE;;mEAEiD,GAAG;qEACD,GAAG;;8BAE1C,CAAC;gBACvB,CAAC,CAAC,CAAC;gBACH,IAAI,IAAI,kBAAkB,CAAC;YAC7B,CAAC;YACD,OAAO,CAAC,SAAS,GAAG,IAAI,GAAG,0DAA0D,CAAC;YACtF,kCAAkC;YAClC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;oBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;oBACnE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;wBACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;wBAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;4BAAE,YAAY,CAAC,GAAG,CAAC,CAAC;oBACrC,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;oBAClC,MAAM,MAAM,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;oBACrE,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;wBACpB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;wBAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,iCAAiC,CAAC,EAAE,CAAC;4BAC9D,EAAE,CAAC,aAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;4BACjC,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;4BACtC,eAAe,EAAE,CAAC;wBACpB,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;YAC7D,IAAI,MAAM;gBAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC;QACF,MAAM,YAAY,GAAG,UAAU,MAAqB;YAClD,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAgB,CAAC;YACzE,MAAM,GAAG,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,aAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxD,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE;gBACvB,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;gBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS,EAAE,MAAM;gBACjB,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,KAAK;aAClB,CAAC;YACF,KAAK,CAAC,SAAS,GAAG;;;kCAGU,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,oBAAoB;;;0DAGpC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;0DACtC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;0DACtC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;;;yFAGP,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;6FACvE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;;;;;;2BAM/I,CAAC;YACtB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;YAC7B,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAE,CAAC,OAAO,GAAG,GAAG,EAAE;gBAC7D,MAAM,IAAI,GAAI,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAuB,CAAC,KAAiC,CAAC;gBAClH,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAA4B,CAAC;gBAC3F,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAA4B,CAAC;gBAC/F,MAAM,KAAK,GAAG,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;gBAC7C,MAAM,OAAO,GAAG,YAAY,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;gBACjD,IAAI,CAAC,OAAO,EAAE,CAAC;oBAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;oBAAC,OAAO;gBAAC,CAAC;gBACxD,4DAA4D;gBAC5D,MAAM,MAAM,GAA+B;oBACzC,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;oBACvB,IAAI,EAAE,IAA6B;oBACnC,KAAK,EAAE,KAAK;oBACZ,OAAO,EAAE,OAAO;oBAChB,IAAI,EAAE,EAAE;oBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,SAAS,EAAE,QAAQ;oBACnB,SAAS,EAAE,QAAQ;oBACnB,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,KAAK;oBACf,UAAU,EAAE,KAAK;iBAClB,CAAC;gBACF,IAAI,MAAM,KAAK,IAAI;oBAAE,EAAE,CAAC,aAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;oBAC/C,EAAE,CAAC,aAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;gBACxC,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;gBACtC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBAC7B,eAAe,EAAE,CAAC;YACpB,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,eAAe,EAAE,CAAC;IACpB,CAAC;AACH,CAAC;AACD,SAAS,kBAAkB,CAAC,SAAsB;IAChD,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,iBAAiB,CAAgB,CAAC;IACxE,IAAI,KAAK,EAAE,CAAC;QACV,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IAC/B,CAAC;IACD,mBAAmB,GAAG,IAAI,CAAC;AAC7B,CAAC;AAED;;;;GAIG;AACH,SAAS,aAAa,CAAC,SAAsB,EAAE,QAA2B;IACxE,sCAAsC;IACtC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACtC,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAA4B,CAAC;IACtF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAA4B,CAAC;IACtF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAA4B,CAAC;IAC7F,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS;QAAE,OAAO;IACrC,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACpC,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACpC,MAAM,WAAW,GAAG,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;IAClD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACrC,OAAO;IACT,CAAC;IACD,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;IAG5E,SAAS,kBAAkB,CAAC,IAAuB;QACjD,OAAO,OAAO,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED,MAAM,YAAY,GAAc;QAC9B,EAAE;QACF,IAAI;QACJ,IAAI;QACJ,WAAW;QACX,IAAI,EAAE,EAAE;QACR,KAAK,EAAE,EAAE;QACT,IAAI,EAAE,EAAE;QACR,MAAM,EAAE,EAAE;QACV,aAAa,EAAE,EAAE;QACjB,aAAa,EAAE;YACb,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,MAAM;YAClB,SAAS,EAAE,IAAI;YACf,YAAY,EAAE,GAAG;YACjB,eAAe,EAAE,EAAE;YACnB,eAAe,EAAE,EAAE;SACpB;QACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE;YACR,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,oBAAoB,EAAE,KAAK;SAC5B;KACF,CAAC;IACF,IAAI,mBAAmB,KAAK,IAAI,EAAE,CAAC;QACjC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,UAAU,CAAC,mBAAmB,CAAC,GAAG,YAAY,CAAC;IACjD,CAAC;IACD,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;IACtC,IAAI,CAAC,QAAQ,CAAC,eAAe;QAAE,QAAQ,CAAC,eAAe,GAAG,YAAY,CAAC,EAAE,CAAC;IAC1E,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAC9B,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;GAKG;AACH,SAAS,eAAe,CAAC,SAAsB,EAAE,GAAW,EAAE,QAA2B;IACvF,sCAAsC;IACtC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACtC,IAAI,CAAC,OAAO,CAAC,+CAA+C,CAAC;QAAE,OAAO;IACtE,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1B,QAAQ,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;IACtC,mDAAmD;IACnD,IAAI,QAAQ,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QAC3F,QAAQ,CAAC,eAAe,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAClF,CAAC;IACD,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;GAKG;AACH,SAAS,eAAe,CAAC,SAAsB,EAAE,GAAW,EAAE,QAA2B;IACvF,sCAAsC;IACtC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACtC,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAAC,EAAE;QAAE,OAAO;IAChB,QAAQ,CAAC,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC;IACjC,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC5C,CAAC", "sourcesContent": ["\n// Workspace section logic and rendering\nimport {\n  Workspace,\n  TeamRole,\n  WorkspaceFile,\n  FileType,\n  WorkspaceTeamMember,\n  WorkspaceDocumentationItem,\n  DocumentationItemType\n} from '../types';\n\n// Type guard for WorkspaceDocumentationItem\nfunction isWorkspaceDocItem(item: any): item is WorkspaceDocumentationItem & { title: string; content: string } {\n  return item && typeof item === 'object' && 'title' in item && 'content' in item;\n}\n\n// Type for legacy documentation items\ninterface LegacyDocItem {\n  type: string;\n  label: string;\n  value: string;\n  [key: string]: any;\n}\n\n// Type guard to check if an item is a legacy doc item\nfunction isLegacyDocItem(item: any): item is LegacyDocItem {\n  return item && typeof item === 'object' && 'value' in item;\n}\n\n/**\n * Type assertion function to ensure a value is a Workspace array with index signature\n */\nfunction assertWorkspaces(workspaces: unknown): asserts workspaces is Array<Workspace & { [key: string]: unknown }> {\n  if (!Array.isArray(workspaces)) {\n    throw new Error('Workspaces must be an array');\n  }\n  // Basic validation - in a real app, you'd want more thorough validation\n  workspaces.forEach(ws => {\n    if (typeof ws !== 'object' || ws === null) {\n      throw new Error('Each workspace must be an object');\n    }\n  });\n}\n\ninterface WorkspaceSettings {\n  workspaces: Workspace[];\n  activeWorkspace?: string;\n  [key: string]: unknown; // Allow additional properties\n}\n\ninterface WorkspaceModalElements {\n  modal: HTMLElement;\n  nameInput: HTMLInputElement;\n  pathInput: HTMLInputElement;\n  descInput: HTMLTextAreaElement;\n  activeInput: HTMLInputElement;\n  saveBtn: HTMLButtonElement | null;\n  cancelBtn: HTMLButtonElement | null;\n  [key: string]: HTMLElement | null; // Allow string indexing with null for dynamic properties\n}\n\nlet workspaces: Workspace[] = [];\nlet editingWorkspaceIdx: number | null = null;\n\n/**\n * Renders the Workspace settings section in the settings UI\n * @param container - The HTMLElement where the section should be rendered\n * @param settings - An object containing the current workspace settings\n */\nexport function renderWorkspaceSettingsSection(container: HTMLElement, settings: WorkspaceSettings): void {\n  // Ensure workspaces is properly typed\n  assertWorkspaces(settings.workspaces);\n  // Sync from settings and ensure proper typing\n  workspaces = settings.workspaces || [];\n  assertWorkspaces(workspaces);\n  renderWorkspaceTable(container, settings);\n  // Add button listener\n  const addBtn = document.getElementById('addWorkspaceBtn');\n  if (addBtn) addBtn.onclick = () => showWorkspaceModal(container, {}, null, settings);\n  // Modal buttons\n  const cancelBtn = document.getElementById('cancelWorkspaceBtn');\n  if (cancelBtn) cancelBtn.onclick = () => hideWorkspaceModal(container);\n  const saveBtn = document.getElementById('saveWorkspaceBtn');\n  if (saveBtn) saveBtn.onclick = () => saveWorkspace(container, settings);\n}\n\n/**\n * Renders the workspaces table in the settings UI\n * @param container - The container element\n * @param settings - The current workspace settings\n */\nfunction renderWorkspaceTable(container: HTMLElement, settings: WorkspaceSettings): void {\n  // Ensure workspaces is properly typed\n  const workspaces = settings.workspaces || [];\n  assertWorkspaces(workspaces);\n\n  // Create a properly typed version of workspaces\n  const typedWorkspaces = workspaces as Array<Workspace & { [key: string]: unknown }>;\n\n  let htmlContent = `\n    <style>\n      .crud-table th, .crud-table td { padding: 7px 12px; }\n      .crud-table th { background: #f6f6f9; color: #222; font-weight: 600; font-size: 1.05em; }\n      .crud-table tbody tr:nth-child(even) { background: #f8fafc; }\n      .crud-table tbody tr:hover { background: #e0e7ef; }\n      .ws-badge { \n        display: inline-block; \n        padding: 2px 8px; \n        border-radius: 8px; \n        font-size: 0.88em; \n        margin-right: 2px; \n        background: #f3f4f6; \n        color: #2563eb; \n      }\n      .ws-badge.active { \n        background: #d1fae5; \n        color: #059669; \n        font-weight: 600; \n      }\n      .ws-badge.tag { \n        background: #fef3c7; \n        color: #b45309; \n      }\n      .ws-action-btn { \n        background: #2563eb; \n        color: #fff; \n        border: none; \n        border-radius: 5px; \n        padding: 3px 10px; \n        margin: 0 2px; \n        font-size: 1em; \n        cursor: pointer; \n        transition: background 0.15s; \n      }\n      .ws-action-btn:hover { \n        background: #1d4ed8; \n      }\n      .ws-action-btn.delete { \n        background: #fee2e2; \n        color: #b91c1c; \n      }\n      .ws-action-btn.delete:hover { \n        background: #fecaca; \n      }\n      .ws-action-btn[disabled] { \n        background: #e5e7eb; \n        color: #888; \n        cursor: not-allowed; \n      }\n      .ws-table-title { \n        font-size: 1.25em; \n        font-weight: 600; \n        margin-bottom: 8px; \n        color: #222; \n        letter-spacing: 0.01em; \n      }\n      .ws-empty { \n        color: #aaa; \n        font-size: 1.1em; \n        padding: 24px 0; \n        text-align: center; \n      }\n    </style>`;\n  htmlContent += '<div class=\"ws-table-title\">🗂️ Workspaces</div>';\n  htmlContent += '<div style=\"margin-bottom:12px;\"><button id=\"addWorkspaceBtn\" class=\"ws-action-btn\">➕ Add Workspace</button></div>';\n  htmlContent += '<div id=\"workspaceSection\">';\n  if (typedWorkspaces.length === 0) {\n    htmlContent += '<div class=\"ws-empty\">No workspaces defined.<br><span style=\"font-size:0.95em;\">Click <b>Add Workspace</b> to get started!</span></div>';\n  } else {\n    htmlContent += '<table class=\"crud-table\"><thead><tr>' +\n      '<th>🏷️ Name</th><th>📁 Path</th><th>📝 Description</th><th>🏷️ Tags</th><th>✅ Active</th><th>⚙️ Actions</th>' +\n      '</tr></thead><tbody>';\n    typedWorkspaces.forEach((ws, idx) => {\n      const isActive = settings.activeWorkspace === ws.id;\n      htmlContent += `<tr>\n                <td title=\"Workspace Name\">${ws.name || ''}</td>\n                <td title=\"Workspace Path\">${ws.path || ''}</td>\n                <td title=\"Description\">${ws.description || ''}</td>\n                <td>${(ws.tags || []).map(tag => `<span class='ws-badge tag'>${tag}</span>`).join('')}</td>\n                <td>${isActive ? '<span class=\"ws-badge active\">Active</span>' : ''}</td>\n                <td>\n                    <button type=\"button\" class=\"ws-action-btn\" data-switch=\"${idx}\" title=\"Switch to this workspace\">${isActive ? 'Current' : 'Switch'}</button>\n                    <button type=\"button\" class=\"ws-action-btn\" data-edit=\"${idx}\" title=\"Edit workspace\">Edit</button>\n                    <button type=\"button\" class=\"ws-action-btn delete\" data-delete=\"${idx}\" title=\"Delete workspace\">Delete</button>\n                </td>\n            </tr>`;\n    });\n    htmlContent += '</tbody></table>';\n  }\n  htmlContent += '</div>';\n  // Modal placeholder\n  htmlContent += '<div id=\"workspaceModal\" style=\"display:none;\"></div>';\n  container.innerHTML = htmlContent;\n  // Attach listeners\n  const section = container.querySelector('#workspaceSection') as HTMLElement;\n  section?.querySelectorAll('button[data-edit]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxStr = (e.target as HTMLElement).getAttribute('data-edit');\n      if (idxStr !== null) {\n        const idx = parseInt(idxStr);\n        if (!isNaN(idx)) showWorkspaceModal(container, workspaces[idx], idx, settings);\n      }\n    });\n  });\n  section?.querySelectorAll('button[data-delete]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxStr = (e.target as HTMLElement).getAttribute('data-delete');\n      if (idxStr !== null) {\n        const idx = parseInt(idxStr);\n        if (!isNaN(idx)) deleteWorkspace(container, idx, settings);\n      }\n    });\n  });\n  section?.querySelectorAll('button[data-switch]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxStr = (e.target as HTMLElement).getAttribute('data-switch');\n      if (idxStr !== null) {\n        const idx = parseInt(idxStr);\n        if (!isNaN(idx)) switchWorkspace(container, idx, settings);\n      }\n    });\n  });\n  // Add button\n  const addBtn = document.getElementById('addWorkspaceBtn');\n  if (addBtn) addBtn.onclick = () => showWorkspaceModal(container, {}, null, settings);\n}\n\nimport { renderKnowledgebaseTable, KnowledgebaseSource, showKnowledgebaseModal, deleteKnowledgebase } from './knowledgebaseSettingsSection';\n\nfunction logKnowledgebaseAction(action: string, source?: KnowledgebaseSource): void {\n  console.log(`Knowledgebase action: ${action}`, source);\n}\n\n/**\n * Shows the workspace modal for adding/editing a workspace\n * @param container - The container element\n * @param ws - The workspace data to edit, or an empty object for a new workspace\n * @param idx - The index of the workspace being edited, or null for a new workspace\n * @param settings - The current workspace settings\n */\nfunction showWorkspaceModal(container: HTMLElement, ws: Partial<Workspace> = {}, idx: number | null = null, settings: WorkspaceSettings): void {\n  editingWorkspaceIdx = idx;\n  const modal = container.querySelector('#workspaceModal') as HTMLElement;\n  const nameInput = document.getElementById('workspaceName') as HTMLInputElement;\n  const pathInput = document.getElementById('workspacePath') as HTMLInputElement;\n  const descInput = document.getElementById('workspaceDescription') as HTMLTextAreaElement;\n  const activeInput = document.getElementById('workspaceActive') as HTMLInputElement;\n\n  if (!modal || !nameInput || !pathInput || !descInput || !activeInput) {\n    console.error('Required modal elements not found');\n    return;\n  }\n\n  const saveBtn = document.getElementById('saveWorkspaceBtn') as HTMLButtonElement | null;\n  const cancelBtn = document.getElementById('cancelWorkspaceBtn') as HTMLButtonElement | null;\n\n  const modalElements: WorkspaceModalElements = {\n    modal,\n    nameInput,\n    pathInput,\n    descInput,\n    activeInput,\n    saveBtn,\n    cancelBtn\n  };\n  // Tabs\n  const tabs = [\n    { id: 'general', label: 'General' },\n    { id: 'knowledgebase', label: 'Knowledgebase' },\n    { id: 'files', label: 'Files' },\n    { id: 'team', label: 'Team Members' },\n    { id: 'memory', label: 'Memory/Notes' },\n    { id: 'docs', label: 'Documentation' }\n  ];\n  let activeTab = 'general';\n  const renderTabs = function (selectedTab: string) {\n    activeTab = selectedTab;\n    logKnowledgebaseAction('renderTabs', { label: selectedTab, type: 'tab', value: selectedTab, shared: false });\n    const html = `<div class=\"modal-overlay\">\n            <div class=\"modal\">\n                <h3>${idx === null ? 'Add Workspace' : 'Edit Workspace'}</h3>\n                <div class=\"workspace-tabs\" style=\"display:flex;gap:8px;margin-bottom:8px;\">\n                    ${tabs.map(tab => `<button class=\"workspace-tab${selectedTab === tab.id ? ' active' : ''}\" data-tab=\"${tab.id}\">${tab.label}</button>`).join('')}\n                </div>\n                <div id=\"workspaceTabContent\"></div>\n                <div style=\"margin-top:10px;\">\n                    <button id=\"saveWorkspaceBtn\">Save</button>\n                    <button id=\"cancelWorkspaceBtn\">Cancel</button>\n                </div>\n            </div>\n        </div>`;\n    modal.innerHTML = html;\n    modal.style.display = 'block';\n    // Tab switching\n    modal.querySelectorAll('.workspace-tab').forEach(btn => {\n      btn.addEventListener('click', (e) => {\n        const tabId = (e.target as HTMLElement).getAttribute('data-tab');\n        if (tabId) renderTabs(tabId);\n      });\n    });\n    // Modal buttons\n    const cancelBtn = document.getElementById('cancelWorkspaceBtn');\n    if (cancelBtn) cancelBtn.onclick = () => hideWorkspaceModal(container);\n    const saveBtn = document.getElementById('saveWorkspaceBtn');\n    if (saveBtn) saveBtn.onclick = () => saveWorkspace(container, settings);\n    // Render tab content\n    renderTabContent(selectedTab);\n  };\n  function renderTabContent(tabId: string) {\n    const content = modal.querySelector('#workspaceTabContent') as HTMLElement;\n    if (!content) return;\n    if (tabId === 'general') {\n      content.innerHTML = `\n                <div>\n                    <label>Name:<br><input id=\"workspaceName\" value=\"${ws.name || ''}\" /></label>\n                </div>\n                <div>\n                    <label>Path:<br><input id=\"workspacePath\" value=\"${ws.path || ''}\" /></label>\n                </div>\n                <div>\n                    <label>Description:<br><input id=\"workspaceDescription\" value=\"${ws.description || ''}\" /></label>\n                </div>\n            `;\n    } else if (tabId === 'knowledgebase') {\n      const workspaceKnowledgebase = ws.knowledgebase || { sources: [], shared: false };\n      const sharedChecked = workspaceKnowledgebase.shared ? 'checked' : '';\n      content.innerHTML = `\n                <div style=\"margin-bottom:10px;\">\n                    <label><input type=\"checkbox\" id=\"workspaceKbShared\" ${sharedChecked}/> Use shared knowledgebase</label>\n                </div>\n                <div id=\"workspaceKnowledgebaseSection\"></div>\n            `;\n      const kbSection = content.querySelector('#workspaceKnowledgebaseSection') as HTMLElement;\n      const kbSharedCheckbox = document.getElementById('workspaceKbShared') as HTMLInputElement;\n      // Define the knowledgebase sources type\n      type KnowledgebaseSource = string; // Adjust this type according to your actual data structure\n\n      let kbSources: KnowledgebaseSource[] = [];\n      if (workspaceKnowledgebase.shared) {\n        kbSources = Array.isArray(settings.knowledgebaseSources) ?\n          settings.knowledgebaseSources as KnowledgebaseSource[] : [];\n      } else {\n        kbSources = Array.isArray(workspaceKnowledgebase.sources) ?\n          workspaceKnowledgebase.sources as KnowledgebaseSource[] : [];\n      }\n      console.log('Knowledgebase sources:', kbSources);\n\n      // Set up temporary knowledgebase sources for rendering\n      const tempKnowledgebaseSources = kbSources;\n\n      // Extend Window interface to include knowledgebaseSources\n      interface WindowWithKnowledgebase extends Window {\n        knowledgebaseSources?: KnowledgebaseSource[];\n      }\n\n      const windowTyped = window as unknown as WindowWithKnowledgebase;\n      const originalSources = windowTyped.knowledgebaseSources;\n      windowTyped.knowledgebaseSources = tempKnowledgebaseSources;\n\n      renderKnowledgebaseTable(kbSection);\n\n      // Restore original sources\n      (window as any).knowledgebaseSources = originalSources;\n\n      // Add custom event handlers for workspace-specific knowledgebase\n      const addBtn = kbSection.querySelector('#addKnowledgebaseBtn');\n      if (addBtn) {\n        addBtn.addEventListener('click', () => {\n          showKnowledgebaseModal(null);\n        });\n      }\n      kbSharedCheckbox?.addEventListener('change', (e) => {\n        workspaceKnowledgebase.shared = kbSharedCheckbox.checked;\n        renderTabContent('knowledgebase');\n      });\n    } else if (tabId === 'files') {\n      // --- Files Management ---\n      ws.files = ws.files || [];\n      const renderFilesTable = function () {\n        let html = '<div style=\"margin-bottom:8px;\"><button id=\"addWorkspaceFileBtn\" class=\"ws-action-btn\">➕ Add File</button></div>';\n        if (!ws.files || ws.files.length === 0) {\n          html += '<div class=\"ws-empty\">No files added to this workspace.</div>';\n        } else {\n          html += '<table class=\"crud-table\"><thead><tr><th>📄 Name</th><th>📁 Path</th><th>🗂️ Type</th><th>🏷️ Tags</th><th>⚙️ Actions</th></tr></thead><tbody>';\n          ws.files.forEach((file, idx) => {\n            // Map file types to icons\n            const typeIcon = file.type === 'file' ? '📄' : file.type === 'directory' ? '📁' : '🔍';\n            html += `<tr>\n                            <td title=\"File Name\">${file.name || ''}</td>\n                            <td title=\"File Path\">${file.path || ''}</td>\n                            <td title=\"File Type\">${typeIcon} ${file.type || ''}</td>\n                            <td>${(file.tags || []).map(tag => `<span class='ws-badge tag'>${tag}</span>`).join('')}</td>\n                            <td>\n                                <button type=\"button\" class=\"ws-action-btn\" data-edit=\"${idx}\" title=\"Edit file\">Edit</button>\n                                <button type=\"button\" class=\"ws-action-btn delete\" data-delete=\"${idx}\" title=\"Delete file\">Delete</button>\n                            </td>\n                        </tr>`;\n          });\n          html += '</tbody></table>';\n        }\n        content.innerHTML = html + '<div id=\"workspaceFileModal\" style=\"display:none;\"></div>';\n        content.querySelectorAll('button[data-edit]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-edit');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx)) showFileModal(idx);\n            }\n          });\n        });\n        content.querySelectorAll('button[data-delete]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-delete');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx) && confirm('Delete this file?')) {\n                ws.files!.splice(idx, 1);\n                settings.workspaces = [...workspaces];\n                renderFilesTable();\n              }\n            }\n          });\n        });\n        const addBtn = document.getElementById('addWorkspaceFileBtn');\n        if (addBtn) addBtn.onclick = () => showFileModal(null);\n      };\n      const showFileModal = function (fileIdx: number | null) {\n        const modal = content.querySelector('#workspaceFileModal') as HTMLElement;\n        const file = fileIdx !== null ? ws.files![fileIdx] : { name: '', path: '', type: 'code', tags: [] };\n        modal.innerHTML = `\n                    <div class=\"modal-overlay\" style=\"display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1000;\">\n                        <div class=\"modal\" style=\"min-width:320px;\">\n                            <h4>${fileIdx === null ? 'Add File' : 'Edit File'}</h4>\n                            <div><label>Name:<br><input id=\"workspaceFileName\" value=\"${file.name || ''}\" /></label></div>\n                            <div><label>Path:<br><input id=\"workspaceFilePath\" value=\"${file.path || ''}\" /></label></div>\n                            <div><label>Type:<br>\n                                <select id=\"workspaceFileType\">\n                                    <option value=\"code\"${file.type === 'code' ? ' selected' : ''}>💻 Code</option>\n                                    <option value=\"doc\"${file.type === 'doc' ? ' selected' : ''}>📄 Doc</option>\n                                    <option value=\"data\"${file.type === 'data' ? ' selected' : ''}>📊 Data</option>\n                                    <option value=\"other\"${file.type === 'other' ? ' selected' : ''}>📁 Other</option>\n                                </select>\n                            </label></div>\n                            <div><label>Tags (comma separated):<br><input id=\"workspaceFileTags\" value=\"${(file.tags || []).join(', ')}\" /></label></div>\n                            <div style=\"margin-top:10px;\">\n                                <button id=\"saveWorkspaceFileBtn\">Save</button>\n                                <button id=\"cancelWorkspaceFileBtn\">Cancel</button>\n                            </div>\n                        </div>\n                    </div>`;\n        modal.style.display = 'flex';\n        document.getElementById('cancelWorkspaceFileBtn')!.onclick = () => { modal.style.display = 'none'; };\n        document.getElementById('saveWorkspaceFileBtn')!.onclick = () => {\n          const name = (document.getElementById('workspaceFileName') as HTMLInputElement).value.trim();\n          const path = (document.getElementById('workspaceFilePath') as HTMLInputElement).value.trim();\n          const type = (document.getElementById('workspaceFileType') as HTMLSelectElement).value;\n          const tags = (document.getElementById('workspaceFileTags') as HTMLInputElement).value.split(',').map(t => t.trim()).filter(Boolean);\n          if (!name || !path) { alert('Name and path are required.'); return; }\n          // Create a properly typed WorkspaceFile object\n          const newFile: WorkspaceFile = {\n            path,\n            name,\n            type: type as FileType, // Cast to FileType since we know the values are valid\n            tags,\n            metadata: {},\n            isExcluded: false,\n            isBinary: false,\n            encoding: 'utf-8'\n          };\n          if (fileIdx === null) ws.files!.push(newFile);\n          else ws.files![fileIdx] = newFile;\n          settings.workspaces = [...workspaces];\n          modal.style.display = 'none';\n          renderFilesTable();\n        };\n      };\n      renderFilesTable();\n    } else if (tabId === 'team') {\n      // --- Team Management ---\n      ws.team = ws.team || [];\n      const renderTeamTable = function () {\n        let html = '<div style=\"margin-bottom:8px;\"><button id=\"addWorkspaceTeamBtn\" class=\"ws-action-btn\">➕ Add Member</button></div>';\n        if (!ws.team || ws.team.length === 0) {\n          html += '<div class=\"ws-empty\">No team members added to this workspace.</div>';\n        } else {\n          html += '<table class=\"crud-table\"><thead><tr><th>🧑 Member</th><th>📧 Email</th><th>🎓 Role</th><th>⚙️ Actions</th></tr></thead><tbody>';\n          ws.team.forEach((member, idx) => {\n            const initials = member.name ? member.name.split(' ').map((n: string) => n[0]).join('').toUpperCase().slice(0, 2) : '👤';\n            const roleBadge = (role: TeamRole): string => {\n              switch (role) {\n                case 'admin':\n                  return '<span class=\"ws-badge active\">Admin</span>';\n                case 'owner':\n                  return '<span class=\"ws-badge active\">Owner</span>';\n                case 'developer':\n                  return '<span class=\"ws-badge tag\">Developer</span>';\n                case 'guest':\n                  return '<span class=\"ws-badge\">Guest</span>';\n                case 'viewer':\n                default:\n                  return '<span class=\"ws-badge\">Viewer</span>';\n              }\n            };\n\n            // Then use it like this:\n            const memberRoleBadge = roleBadge(member.role);\n            html += `<tr>\n                            <td title=\"Name\"><span style=\"font-weight:600;\">${initials}</span> ${member.name || ''}</td>\n                            <td title=\"Email\">${member.email || ''}</td>\n                            <td title=\"Role\">${roleBadge}</td>\n                            <td>\n                                <button type=\"button\" class=\"ws-action-btn\" data-edit=\"${idx}\" title=\"Edit member\">Edit</button>\n                                <button type=\"button\" class=\"ws-action-btn delete\" data-delete=\"${idx}\" title=\"Delete member\">Delete</button>\n                            </td>\n                        </tr>`;\n          });\n          html += '</tbody></table>';\n        }\n        content.innerHTML = html + '<div id=\"workspaceTeamModal\" style=\"display:none;\"></div>';\n        content.querySelectorAll('button[data-edit]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-edit');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx)) showTeamModal(idx);\n            }\n          });\n        });\n        content.querySelectorAll('button[data-delete]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-delete');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx) && confirm('Delete this team member?')) {\n                ws.team!.splice(idx, 1);\n                settings.workspaces = [...workspaces];\n                renderTeamTable();\n              }\n            }\n          });\n        });\n        const addBtn = document.getElementById('addWorkspaceTeamBtn');\n        if (addBtn) addBtn.onclick = () => showTeamModal(null);\n      };\n      const showTeamModal = function (teamIdx: number | null) {\n        const modal = content.querySelector('#workspaceTeamModal') as HTMLElement;\n        const member = teamIdx !== null ? ws.team![teamIdx] : { name: '', email: '', role: 'viewer' };\n        modal.innerHTML = `\n                    <div class=\"modal-overlay\" style=\"display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1000;\">\n                        <div class=\"modal\" style=\"min-width:320px;\">\n                            <h4>${teamIdx === null ? 'Add Member' : 'Edit Member'}</h4>\n                            <div><label>Name:<br><input id=\"workspaceTeamName\" value=\"${member.name || ''}\" /></label></div>\n                            <div><label>Email:<br><input id=\"workspaceTeamEmail\" value=\"${member.email || ''}\" /></label></div>\n                            <div><label>Role:<br>\n                                <select id=\"workspaceTeamRole\">\n                                    <option value=\"admin\"${member.role === 'admin' ? ' selected' : ''}>Admin</option>\n                                    <option value=\"editor\"${member.role === 'editor' ? ' selected' : ''}>Editor</option>\n                                    <option value=\"viewer\"${member.role === 'viewer' ? ' selected' : ''}>Viewer</option>\n                                </select>\n                            </label></div>\n                            <div style=\"margin-top:10px;\">\n                                <button id=\"saveWorkspaceTeamBtn\">Save</button>\n                                <button id=\"cancelWorkspaceTeamBtn\">Cancel</button>\n                            </div>\n                        </div>\n                    </div>`;\n        modal.style.display = 'flex';\n        document.getElementById('cancelWorkspaceTeamBtn')!.onclick = () => { modal.style.display = 'none'; };\n        document.getElementById('saveWorkspaceTeamBtn')!.onclick = () => {\n          const name = (document.getElementById('workspaceTeamName') as HTMLInputElement).value.trim();\n          const email = (document.getElementById('workspaceTeamEmail') as HTMLInputElement).value.trim();\n          const role = (document.getElementById('workspaceTeamRole') as HTMLSelectElement).value;\n          if (!name) { alert('Name is required.'); return; }\n          const id = (name + '-' + email).replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();\n          // Create a properly typed WorkspaceTeamMember object\n          const newMember: WorkspaceTeamMember = {\n            id,\n            name,\n            email,\n            role: role as TeamRole, // Cast to TeamRole since we know the values are valid\n            permissions: [],\n            isActive: true,\n            joinedAt: Date.now()\n          };\n          if (teamIdx === null) ws.team!.push(newMember);\n          else ws.team![teamIdx] = newMember;\n          settings.workspaces = [...workspaces];\n          modal.style.display = 'none';\n          renderTeamTable();\n        };\n      };\n      renderTeamTable();\n    }\n    // Files tab\n    if (tabId === 'files') {\n      ws.files = ws.files || [];\n      const renderFilesTable = function () {\n        let html = '<div style=\"margin-bottom:8px;\"><button id=\"addWorkspaceFileBtn\">Add File</button></div>';\n        if (!ws.files || ws.files.length === 0) {\n          html += '<div style=\"color:#aaa;\">No files added to this workspace.</div>';\n        } else {\n          html += '<table class=\"crud-table\"><thead><tr><th>Name</th><th>Path</th><th>Type</th><th>Tags</th><th>Actions</th></tr></thead><tbody>';\n          ws.files.forEach((file, idx) => {\n            html += `<tr>\n                            <td>${file.name || ''}</td>\n                            <td>${file.path || ''}</td>\n                            <td>${file.type || ''}</td>\n                            <td>${(file.tags || []).join(', ')}</td>\n                            <td>\n                                <button type=\"button\" data-edit=\"${idx}\">Edit</button>\n                                <button type=\"button\" data-delete=\"${idx}\">Delete</button>\n                            </td>\n                        </tr>`;\n          });\n          html += '</tbody></table>';\n        }\n        content.innerHTML = html + '<div id=\"workspaceFileModal\" style=\"display:none;\"></div>';\n        // Add/Edit/Delete event listeners\n        content.querySelectorAll('button[data-edit]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-edit');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx)) showFileModal(idx);\n            }\n          });\n        });\n        content.querySelectorAll('button[data-delete]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-delete');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx) && confirm('Delete this file?')) {\n                ws.files!.splice(idx, 1);\n                settings.workspaces = [...workspaces];\n                renderFilesTable();\n              }\n            }\n          });\n        });\n        const addBtn = document.getElementById('addWorkspaceFileBtn');\n        if (addBtn) addBtn.onclick = () => showFileModal(null);\n      };\n      const showFileModal = function (fileIdx: number | null) {\n        const modal = content.querySelector('#workspaceFileModal') as HTMLElement;\n        const file = fileIdx !== null ? ws.files![fileIdx] : { path: '', name: '', type: '', tags: [] };\n        modal.innerHTML = `\n                    <div class=\"modal-overlay\" style=\"display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1000;\">\n                        <div class=\"modal\" style=\"min-width:320px;\">\n                            <h4>${fileIdx === null ? 'Add File' : 'Edit File'}</h4>\n                            <div><label>Path:<br><input id=\"workspaceFilePath\" value=\"${file.path || ''}\" /></label></div>\n                            <div><label>Name:<br><input id=\"workspaceFileName\" value=\"${file.name || ''}\" /></label></div>\n                            <div><label>Type:<br><input id=\"workspaceFileType\" value=\"${file.type || ''}\" /></label></div>\n                            <div><label>Tags (comma separated):<br><input id=\"workspaceFileTags\" value=\"${(file.tags || []).join(', ')}\" /></label></div>\n                            <div style=\"margin-top:10px;\">\n                                <button id=\"saveWorkspaceFileBtn\">Save</button>\n                                <button id=\"cancelWorkspaceFileBtn\">Cancel</button>\n                            </div>\n                        </div>\n                    </div>`;\n        modal.style.display = 'flex';\n        document.getElementById('cancelWorkspaceFileBtn')!.onclick = () => { modal.style.display = 'none'; };\n        document.getElementById('saveWorkspaceFileBtn')!.onclick = () => {\n          const path = (document.getElementById('workspaceFilePath') as HTMLInputElement).value.trim();\n          const name = (document.getElementById('workspaceFileName') as HTMLInputElement).value.trim();\n          const type = (document.getElementById('workspaceFileType') as HTMLInputElement).value.trim();\n          const tags = (document.getElementById('workspaceFileTags') as HTMLInputElement).value.split(',').map(t => t.trim()).filter(t => t);\n          if (!path) { alert('Path is required.'); return; }\n          const newFile: WorkspaceFile = {\n            path,\n            name,\n            type: type as FileType,\n            tags,\n            metadata: {},\n            isExcluded: false,\n            isBinary: false,\n            encoding: 'utf-8'\n          };\n          if (fileIdx === null) ws.files!.push(newFile);\n          else ws.files![fileIdx] = newFile;\n          settings.workspaces = [...workspaces];\n          modal.style.display = 'none';\n          renderFilesTable();\n        };\n      };\n      renderFilesTable();\n    } if (tabId === 'team') {\n      // Team Members Management UI\n      ws.team = ws.team || [];\n      const renderTeamTable = function () {\n        let html = '<div style=\"margin-bottom:8px;\"><button id=\"addWorkspaceTeamBtn\">Add Member</button></div>';\n        if (!ws.team || ws.team.length === 0) {\n          html += '<div style=\"color:#aaa;\">No team members added to this workspace.</div>';\n        } else {\n          html += '<table class=\"crud-table\"><thead><tr><th>Name</th><th>Email</th><th>Role</th><th>Actions</th></tr></thead><tbody>';\n          ws.team.forEach((member, idx) => {\n            html += `<tr>\n                            <td>${member.name || ''}</td>\n                            <td>${member.email || ''}</td>\n                            <td>${member.role || ''}</td>\n                            <td>\n                                <button type=\"button\" class=\"ws-action-btn\" data-edit=\"${idx}\" title=\"Edit member\">Edit</button>\n                                <button type=\"button\" class=\"ws-action-btn delete\" data-delete=\"${idx}\" title=\"Delete member\">Delete</button>\n                            </td>\n                        </tr>`;\n          });\n          html += '</tbody></table>';\n        }\n        content.innerHTML = html + '<div id=\"workspaceTeamModal\" style=\"display:none;\"></div>';\n        // Add/Edit/Delete event listeners\n        content.querySelectorAll('button[data-edit]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-edit');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx)) showTeamModal(idx);\n            }\n          });\n        });\n        content.querySelectorAll('button[data-delete]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-delete');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx) && confirm('Delete this team member?')) {\n                ws.team!.splice(idx, 1);\n                settings.workspaces = [...workspaces];\n                renderTeamTable();\n              }\n            }\n          });\n        });\n        const addBtn = document.getElementById('addWorkspaceTeamBtn');\n        if (addBtn) addBtn.onclick = () => showTeamModal(null);\n      };\n      const showTeamModal = function (memberIdx: number | null) {\n        const modal = content.querySelector('#workspaceTeamModal') as HTMLElement;\n        const member = memberIdx !== null ? ws.team![memberIdx] : { id: '', name: '', email: '', role: '' };\n        modal.innerHTML = `\n                    <div class=\"modal-overlay\" style=\"display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1000;\">\n                        <div class=\"modal\" style=\"min-width:320px;\">\n                            <h4>${memberIdx === null ? 'Add Member' : 'Edit Member'}</h4>\n                            <div><label>Name:<br><input id=\"workspaceTeamName\" value=\"${member.name || ''}\" /></label></div>\n                            <div><label>Email:<br><input id=\"workspaceTeamEmail\" value=\"${member.email || ''}\" /></label></div>\n                            <div><label>Role:<br><input id=\"workspaceTeamRole\" value=\"${member.role || ''}\" /></label></div>\n                            <div style=\"margin-top:10px;\">\n                                <button id=\"saveWorkspaceTeamBtn\">Save</button>\n                                <button id=\"cancelWorkspaceTeamBtn\">Cancel</button>\n                            </div>\n                        </div>\n                    </div>`;\n        modal.style.display = 'flex';\n        document.getElementById('cancelWorkspaceTeamBtn')!.onclick = () => { modal.style.display = 'none'; };\n        document.getElementById('saveWorkspaceTeamBtn')!.onclick = () => {\n          const name = (document.getElementById('workspaceTeamName') as HTMLInputElement).value.trim();\n          const email = (document.getElementById('workspaceTeamEmail') as HTMLInputElement).value.trim();\n          const role = (document.getElementById('workspaceTeamRole') as HTMLInputElement).value.trim();\n          if (!name) { alert('Name is required.'); return; }\n          const id = (name + '-' + email).replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();\n          // Create a properly typed WorkspaceTeamMember object\n          const newMember: WorkspaceTeamMember = {\n            id,\n            name,\n            email,\n            role: role as TeamRole, // Cast to TeamRole since we know the values are valid\n            permissions: [],\n            isActive: true,\n            joinedAt: Date.now()\n          };\n          if (memberIdx === null) ws.team!.push(newMember);\n          else ws.team![memberIdx] = newMember;\n          settings.workspaces = [...workspaces];\n          modal.style.display = 'none';\n          renderTeamTable();\n        };\n      };\n      renderTeamTable();\n    } else if (tabId === 'memory') {\n      content.innerHTML = `<div>\n                <label>Workspace Memory/Notes:<br><textarea id=\"workspaceMemory\" rows=\"6\" style=\"width:100%;\">${ws.memory || ''}</textarea></label>\n            </div>`;\n    } else if (tabId === 'docs') {\n      // --- Documentation Management ---\n      ws.documentation = ws.documentation || [];\n      const renderDocsTable = function () {\n        let html = '<div style=\"margin-bottom:8px;\"><button id=\"addWorkspaceDocBtn\" class=\"ws-action-btn\">➕ Add Documentation</button></div>';\n        if (!ws.documentation || ws.documentation.length === 0) {\n          html += '<div class=\"ws-empty\">No documentation added to this workspace.</div>';\n        } else {\n          html += '<table class=\"crud-table\"><thead><tr><th>📄 Type</th><th>🏷️ Label</th><th>🔗 Value</th><th>⚙️ Actions</th></tr></thead><tbody>';\n          ws.documentation.forEach((doc, idx) => {\n            const typeIcon = doc.type === 'note' ? '📝' : doc.type === 'link' ? '🔗' : doc.type === 'file' ? '📁' : '📄';\n            html += `<tr>\n                            <td title=\"Type\">${typeIcon} ${doc.type}</td>\n                            <td title=\"Title\">${isWorkspaceDocItem(doc) ? doc.title : (doc as LegacyDocItem).label || 'Untitled'}</td>\n                            <td title=\"Content\">${isWorkspaceDocItem(doc) ?\n                (doc.content.length > 40 ? doc.content.slice(0, 40) + '…' : doc.content) :\n                ((doc as LegacyDocItem).value?.length > 40 ?\n                  (doc as LegacyDocItem).value.slice(0, 40) + '…' :\n                  (doc as LegacyDocItem).value || '')}</td>\n                            <td>\n                                <button type=\"button\" class=\"ws-action-btn\" data-edit=\"${idx}\" title=\"Edit documentation\">Edit</button>\n                                <button type=\"button\" class=\"ws-action-btn delete\" data-delete=\"${idx}\" title=\"Delete documentation\">Delete</button>\n                            </td>\n                        </tr>`;\n          });\n          html += '</tbody></table>';\n        }\n        content.innerHTML = html + '<div id=\"workspaceDocModal\" style=\"display:none;\"></div>';\n        content.querySelectorAll('button[data-edit]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-edit');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx)) showDocModal(idx);\n            }\n          });\n        });\n        content.querySelectorAll('button[data-delete]').forEach(btn => {\n          btn.addEventListener('click', (e) => {\n            const idxStr = (e.target as HTMLElement).getAttribute('data-delete');\n            if (idxStr !== null) {\n              const idx = parseInt(idxStr);\n              if (!isNaN(idx) && confirm('Delete this documentation item?')) {\n                ws.documentation!.splice(idx, 1);\n                settings.workspaces = [...workspaces];\n                renderDocsTable();\n              }\n            }\n          });\n        });\n        const addBtn = document.getElementById('addWorkspaceDocBtn');\n        if (addBtn) addBtn.onclick = () => showDocModal(null);\n      };\n      const showDocModal = function (docIdx: number | null) {\n        const modal = content.querySelector('#workspaceDocModal') as HTMLElement;\n        const doc = docIdx !== null ? ws.documentation![docIdx] : {\n          id: crypto.randomUUID(),\n          type: 'note',\n          title: '',\n          content: '',\n          tags: [],\n          createdAt: Date.now(),\n          updatedAt: Date.now(),\n          createdBy: 'user',\n          updatedBy: 'user',\n          metadata: {},\n          isPinned: false,\n          isArchived: false\n        };\n        modal.innerHTML = `\n                    <div class=\"modal-overlay\" style=\"display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1000;\">\n                        <div class=\"modal\" style=\"min-width:320px;\">\n                            <h4>${docIdx === null ? 'Add Documentation' : 'Edit Documentation'}</h4>\n                            <div><label>Type:<br>\n                                <select id=\"workspaceDocType\">\n                                    <option value=\"note\"${doc.type === 'note' ? ' selected' : ''}>📝 Note</option>\n                                    <option value=\"link\"${doc.type === 'link' ? ' selected' : ''}>🔗 Link</option>\n                                    <option value=\"file\"${doc.type === 'file' ? ' selected' : ''}>📁 File</option>\n                                </select>\n                            </label></div>\n                            <div><label>Title:<br><input id=\"workspaceDocTitle\" value=\"${isWorkspaceDocItem(doc) ? doc.title : isLegacyDocItem(doc) ? doc.label : ''}\" /></label></div>\n                            <div><label>Content:<br><input id=\"workspaceDocContent\" value=\"${isWorkspaceDocItem(doc) ? doc.content : isLegacyDocItem(doc) ? doc.value : ''}\" /></label></div>\n                            <div style=\"margin-top:10px;\">\n                                <button id=\"saveWorkspaceDocBtn\">Save</button>\n                                <button id=\"cancelWorkspaceDocBtn\">Cancel</button>\n                            </div>\n                        </div>\n                    </div>`;\n        modal.style.display = 'flex';\n        document.getElementById('cancelWorkspaceDocBtn')!.onclick = () => { modal.style.display = 'none'; };\n        document.getElementById('saveWorkspaceDocBtn')!.onclick = () => {\n          const type = (document.getElementById('workspaceDocType') as HTMLSelectElement).value as 'note' | 'link' | 'file';\n          const titleInput = document.getElementById('workspaceDocTitle') as HTMLInputElement | null;\n          const contentInput = document.getElementById('workspaceDocContent') as HTMLInputElement | null;\n          const title = titleInput?.value.trim() || '';\n          const content = contentInput?.value.trim() || '';\n          if (!content) { alert('Content is required.'); return; }\n          // Create a properly typed WorkspaceDocumentationItem object\n          const newDoc: WorkspaceDocumentationItem = {\n            id: `doc-${Date.now()}`,\n            type: type as DocumentationItemType,\n            title: title,\n            content: content,\n            tags: [],\n            createdAt: Date.now(),\n            updatedAt: Date.now(),\n            createdBy: 'system',\n            updatedBy: 'system',\n            metadata: {},\n            isPinned: false,\n            isArchived: false\n          };\n          if (docIdx === null) ws.documentation!.push(newDoc);\n          else ws.documentation![docIdx] = newDoc;\n          settings.workspaces = [...workspaces];\n          modal.style.display = 'none';\n          renderDocsTable();\n        };\n      };\n      renderDocsTable();\n    }\n    // Project/Workspace Documentation Management UI\n    ws.documentation = ws.documentation || [];\n    const renderDocsTable = function () {\n      let html = '<div style=\"margin-bottom:8px;\"><button id=\"addWorkspaceDocBtn\">Add Documentation</button></div>';\n      if (!ws.documentation || ws.documentation.length === 0) {\n        html += '<div style=\"color:#aaa;\">No documentation added to this workspace.</div>';\n      } else {\n        html += '<table class=\"crud-table\"><thead><tr><th>Type</th><th>Label</th><th>Value</th><th>Actions</th></tr></thead><tbody>';\n        ws.documentation.forEach((doc, idx) => {\n          html += `<tr>\n                            <td>${doc.type}</td>\n                            <td>${isWorkspaceDocItem(doc) ? doc.title : isLegacyDocItem(doc) ? (doc as LegacyDocItem).label : 'Untitled'}</td>\n                            <td>${isWorkspaceDocItem(doc) ?\n              (doc.content.length > 40 ? doc.content.slice(0, 40) + '…' : doc.content) :\n              isLegacyDocItem(doc) ?\n                ((doc as LegacyDocItem).value.length > 40 ? (doc as LegacyDocItem).value.slice(0, 40) + '…' : (doc as LegacyDocItem).value) :\n                ''}</td>\n                            <td>\n                                <button type=\"button\" data-edit=\"${idx}\">Edit</button>\n                                <button type=\"button\" data-delete=\"${idx}\">Delete</button>\n                            </td>\n                        </tr>`;\n        });\n        html += '</tbody></table>';\n      }\n      content.innerHTML = html + '<div id=\"workspaceDocModal\" style=\"display:none;\"></div>';\n      // Add/Edit/Delete event listeners\n      content.querySelectorAll('button[data-edit]').forEach(btn => {\n        btn.addEventListener('click', (e) => {\n          const idxStr = (e.target as HTMLElement).getAttribute('data-edit');\n          if (idxStr !== null) {\n            const idx = parseInt(idxStr);\n            if (!isNaN(idx)) showDocModal(idx);\n          }\n        });\n      });\n      content.querySelectorAll('button[data-delete]').forEach(btn => {\n        btn.addEventListener('click', (e) => {\n          const idxStr = (e.target as HTMLElement).getAttribute('data-delete');\n          if (idxStr !== null) {\n            const idx = parseInt(idxStr);\n            if (!isNaN(idx) && confirm('Delete this documentation item?')) {\n              ws.documentation!.splice(idx, 1);\n              settings.workspaces = [...workspaces];\n              renderDocsTable();\n            }\n          }\n        });\n      });\n      const addBtn = document.getElementById('addWorkspaceDocBtn');\n      if (addBtn) addBtn.onclick = () => showDocModal(null);\n    };\n    const showDocModal = function (docIdx: number | null) {\n      const modal = content.querySelector('#workspaceDocModal') as HTMLElement;\n      const doc = docIdx !== null ? ws.documentation![docIdx] : {\n        id: crypto.randomUUID(),\n        type: 'note',\n        title: '',\n        content: '',\n        tags: [],\n        createdAt: Date.now(),\n        updatedAt: Date.now(),\n        createdBy: 'user',\n        updatedBy: 'user',\n        metadata: {},\n        isPinned: false,\n        isArchived: false\n      };\n      modal.innerHTML = `\n                    <div class=\"modal-overlay\" style=\"display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1000;\">\n                        <div class=\"modal\" style=\"min-width:320px;\">\n                            <h4>${docIdx === null ? 'Add Documentation' : 'Edit Documentation'}</h4>\n                            <div><label>Type:<br>\n                                <select id=\"workspaceDocType\">\n                                    <option value=\"note\"${doc.type === 'note' ? ' selected' : ''}>Note</option>\n                                    <option value=\"link\"${doc.type === 'link' ? ' selected' : ''}>Link</option>\n                                    <option value=\"file\"${doc.type === 'file' ? ' selected' : ''}>File</option>\n                                </select>\n                            </label></div>\n                            <div><label>Title:<br><input id=\"workspaceDocTitle\" value=\"${isWorkspaceDocItem(doc) ? doc.title : isLegacyDocItem(doc) ? doc.label : ''}\" /></label></div>\n                            <div><label>Content:<br><input id=\"workspaceDocContent\" value=\"${isWorkspaceDocItem(doc) ? doc.content : isLegacyDocItem(doc) ? doc.value : ''}\" /></label></div>\n                            <div style=\"margin-top:10px;\">\n                                <button id=\"saveWorkspaceDocBtn\">Save</button>\n                                <button id=\"cancelWorkspaceDocBtn\">Cancel</button>\n                            </div>\n                        </div>\n                    </div>`;\n      modal.style.display = 'flex';\n      document.getElementById('cancelWorkspaceDocBtn')!.onclick = () => { modal.style.display = 'none'; };\n      document.getElementById('saveWorkspaceDocBtn')!.onclick = () => {\n        const type = (document.getElementById('workspaceDocType') as HTMLSelectElement).value as 'note' | 'link' | 'file';\n        const titleInput = document.getElementById('workspaceDocTitle') as HTMLInputElement | null;\n        const contentInput = document.getElementById('workspaceDocContent') as HTMLInputElement | null;\n        const title = titleInput?.value.trim() || '';\n        const content = contentInput?.value.trim() || '';\n        if (!content) { alert('Content is required.'); return; }\n        // Create a properly typed WorkspaceDocumentationItem object\n        const newDoc: WorkspaceDocumentationItem = {\n          id: `doc-${Date.now()}`,\n          type: type as DocumentationItemType,\n          title: title,\n          content: content,\n          tags: [],\n          createdAt: Date.now(),\n          updatedAt: Date.now(),\n          createdBy: 'system',\n          updatedBy: 'system',\n          metadata: {},\n          isPinned: false,\n          isArchived: false\n        };\n        if (docIdx === null) ws.documentation!.push(newDoc);\n        else ws.documentation![docIdx] = newDoc;\n        settings.workspaces = [...workspaces];\n        modal.style.display = 'none';\n        renderDocsTable();\n      };\n    };\n    renderDocsTable();\n  }\n}\nfunction hideWorkspaceModal(container: HTMLElement): void {\n  const modal = container.querySelector('#workspaceModal') as HTMLElement;\n  if (modal) {\n    modal.innerHTML = '';\n    modal.style.display = 'none';\n  }\n  editingWorkspaceIdx = null;\n}\n\n/**\n * Saves the workspace data from the modal\n * @param container - The container element\n * @param settings - The current workspace settings\n */\nfunction saveWorkspace(container: HTMLElement, settings: WorkspaceSettings): void {\n  // Ensure workspaces is properly typed\n  assertWorkspaces(settings.workspaces);\n  const nameInput = document.getElementById('workspaceName') as HTMLInputElement | null;\n  const pathInput = document.getElementById('workspacePath') as HTMLInputElement | null;\n  const descInput = document.getElementById('workspaceDescription') as HTMLInputElement | null;\n  if (!nameInput || !pathInput) return;\n  const name = nameInput.value.trim();\n  const path = pathInput.value.trim();\n  const description = descInput?.value.trim() || '';\n  if (!name || !path) {\n    alert('Name and Path are required.');\n    return;\n  }\n  const id = (name + '-' + path).replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();\n  type DocumentationItem = WorkspaceDocumentationItem | { type: string; value: string; label: string; };\n\n  function isWorkspaceDocItem(item: DocumentationItem): item is WorkspaceDocumentationItem {\n    return 'title' in item && 'content' in item;\n  }\n\n  const newWorkspace: Workspace = {\n    id,\n    name,\n    path,\n    description,\n    tags: [],\n    files: [],\n    team: [],\n    memory: '',\n    documentation: [],\n    knowledgebase: {\n      sources: [],\n      shared: false,\n      autoSync: false,\n      syncInterval: 3600,\n      syncStatus: 'idle',\n      chunkSize: 1000,\n      chunkOverlap: 200,\n      includePatterns: [],\n      excludePatterns: []\n    },\n    createdAt: Date.now(),\n    updatedAt: Date.now(),\n    isActive: true,\n    settings: {\n      autoSave: true,\n      autoFormat: true,\n      lintOnSave: true,\n      formatOnSave: true,\n      experimentalFeatures: false\n    }\n  };\n  if (editingWorkspaceIdx === null) {\n    workspaces.push(newWorkspace);\n  } else {\n    workspaces[editingWorkspaceIdx] = newWorkspace;\n  }\n  settings.workspaces = [...workspaces];\n  if (!settings.activeWorkspace) settings.activeWorkspace = newWorkspace.id;\n  hideWorkspaceModal(container);\n  renderWorkspaceTable(container, settings);\n}\n\n/**\n * Deletes a workspace after confirmation\n * @param container - The container element\n * @param idx - The index of the workspace to delete\n * @param settings - The current workspace settings\n */\nfunction deleteWorkspace(container: HTMLElement, idx: number, settings: WorkspaceSettings): void {\n  // Ensure workspaces is properly typed\n  assertWorkspaces(settings.workspaces);\n  if (!confirm('Delete this workspace? This cannot be undone.')) return;\n  workspaces.splice(idx, 1);\n  settings.workspaces = [...workspaces];\n  // If deleted the active workspace, clear or switch\n  if (settings.activeWorkspace && !workspaces.find(ws => ws.id === settings.activeWorkspace)) {\n    settings.activeWorkspace = workspaces.length > 0 ? workspaces[0].id : undefined;\n  }\n  renderWorkspaceTable(container, settings);\n}\n\n/**\n * Switches the active workspace\n * @param container - The container element\n * @param idx - The index of the workspace to switch to\n * @param settings - The current workspace settings\n */\nfunction switchWorkspace(container: HTMLElement, idx: number, settings: WorkspaceSettings): void {\n  // Ensure workspaces is properly typed\n  assertWorkspaces(settings.workspaces);\n  const ws = workspaces[idx];\n  if (!ws) return;\n  settings.activeWorkspace = ws.id;\n  renderWorkspaceTable(container, settings);\n}\n"]}