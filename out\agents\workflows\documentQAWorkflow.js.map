{"version": 3, "file": "documentQAWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/documentQAWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAeH,4DA2IC;AAKD,kDAsJC;AAjTD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAEtC,sEAAmG;AAGnG;;GAEG;AACH,SAAgB,wBAAwB,CACtC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,OAAc,EACd,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,kCAAkC,IAAI,EAAE,CAAC,CAAC;IAEtD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACrG,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAC9G,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IAExG,MAAM,uBAAuB,GAAG;QAC9B,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,WAAgC,EAAE;YAC5E,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO;oBACP,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAsB;wBAC9B,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;wBAC7B,GAAG,QAAQ;qBACZ;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,uBAAuB,CAAC,KAAa;YACzC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,eAAM,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,8CAA8C,CAAC,CAAC;gBACxF,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,2DAA2D,EAAE,KAAK,CAAC,CAAC;gBACjF,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,MAAc;YAClD,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;oBAC7C,QAAQ,EAAE;wBACR,MAAM,EAAE,cAA8B;wBACtC,IAAI,EAAE,cAA4B;wBAClC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC;qBAC/C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;KACF,CAAC;IAEF,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAC9G,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1F,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC5G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9G,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnH,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KACrG,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,qCAAqC;QACrC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,qBAAqB,KAAK,EAAE;gBAClC,MAAM,EAAE,mBAAmB;gBAC3B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,0CAA0C;YAC1C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,gBAAgB;gBACnC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,mBAAmB;gBAC3B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,mBAAmB;YACnB,sBAAsB;YACtB,oBAAoB;YACpB,oBAAoB;YACpB,oBAAoB;YACpB,sBAAsB;YACtB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,aAA8B;QAC7C,IAAI,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,mBAAmB,CAAC;QAChE,QAAQ,EAAE;YACR,aAAa,EAAE,uBAAuB;SACvC;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CACjC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,OAAc,EACd,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAC;IAEjD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IACtF,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IACtF,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;IAClG,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACrG,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACrG,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IAErG,MAAM,kBAAkB,GAAG;QACzB,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,WAAmB,EAAE,WAAgC,EAAE;YAC5F,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO;oBACP,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAsB;wBAC9B,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,CAAC;wBACtC,GAAG,QAAQ;qBACZ;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,aAAa,WAAW,oBAAoB,CAAC,CAAC;YAC5D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,WAAW,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,KAAK,CAAC,0BAA0B,CAAC,KAAa;YAC5C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE;wBACN,IAAI,EAAE,CAAC,KAAK,CAAC;qBACd;iBACF,CAAC,CAAC;gBAEH,eAAM,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,yCAAyC,CAAC,CAAC;gBACnF,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;gBAC5E,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,MAAc;YACrD,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;oBAC7C,QAAQ,EAAE;wBACR,MAAM,EAAE,cAA8B;wBACtC,IAAI,EAAE,cAA4B;wBAClC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC;qBACtD;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;KACF,CAAC;IAEF,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACxG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACrF,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7F,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9F,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChG,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChG,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpG,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1G,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC7F,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,oCAAoC;QACpC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,oBAAoB,KAAK,EAAE;gBACjC,MAAM,EAAE,kBAAkB;gBAC1B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,0CAA0C;YAC1C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,cAAc;gBACjC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,mBAAmB;gBAC3B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,cAAc;YACd,cAAc;YACd,kBAAkB;YAClB,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,aAA8B;QAC7C,IAAI,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,oBAAoB,CAAC;QAClD,QAAQ,EAAE;YACR,aAAa,EAAE,kBAAkB;SAClC;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Document QA Workflow\n *\n * This module provides a workflow for document question-answering:\n * - Loading and processing documents\n * - Analyzing document content\n * - Answering questions based on document content\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\nimport { codessaMemoryProvider as codessaMemoryManager } from '../../memory/codessa/codessaMemory';\nimport { MemorySource, MemoryType } from '../../memory/types';\n\n/**\n * Create a Document QA workflow for answering questions about documents\n */\nexport function createDocumentQAWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  qaAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Document QA workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const documentLoadingNode = Codessa.createAgentNode('document-loading', 'Document Loading', qaAgent);\n  const documentProcessingNode = Codessa.createAgentNode('document-processing', 'Document Processing', qaAgent);\n  const questionAnalysisNode = Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);\n  const contextRetrievalNode = Codessa.createAgentNode('context-retrieval', 'Context Retrieval', qaAgent);\n\n  const documentQAMemoryManager = {\n    async storeDocumentContent(content: string, metadata: Record<string, any> = {}): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content,\n          metadata: {\n            source: 'file' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['document', 'content'],\n            ...metadata\n          }\n        });\n        logger.info('Saved document content to memory');\n      } catch (error) {\n        logger.error('Failed to save document content to memory:', error);\n      }\n    },\n\n    async retrieveRelevantContent(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5\n        });\n\n        logger.info(`Retrieved ${memories.length} relevant document content items from memory`);\n        return memories;\n      } catch (error) {\n        logger.error('Failed to retrieve relevant document content from memory:', error);\n        return [];\n      }\n    },\n\n    async storeQuestion(question: string, answer: string): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify({ question, answer }),\n          metadata: {\n            source: 'conversation' as MemorySource,\n            type: 'conversation' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['document', 'qa', 'question', 'answer']\n          }\n        });\n        logger.info('Saved question-answer pair to memory');\n      } catch (error) {\n        logger.error('Failed to save question-answer pair to memory:', error);\n      }\n    }\n  };\n\n  const answerGenerationNode = Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);\n  const answerVerificationNode = Codessa.createAgentNode('answer-verification', 'Answer Verification', qaAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-loading', source: 'input', target: 'document-loading', type: 'default' },\n    { name: 'loading-to-processing', source: 'document-loading', target: 'document-processing', type: 'default' },\n    { name: 'processing-to-question', source: 'document-processing', target: 'question-analysis', type: 'default' },\n    { name: 'question-to-retrieval', source: 'question-analysis', target: 'context-retrieval', type: 'default' },\n    { name: 'retrieval-to-generation', source: 'context-retrieval', target: 'answer-generation', type: 'default' },\n    { name: 'generation-to-verification', source: 'answer-generation', target: 'answer-verification', type: 'default' },\n    { name: 'verification-to-output', source: 'answer-verification', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect context retrieval to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `retrieval-to-tool-${index}`,\n        source: 'context-retrieval',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to answer generation\n      edges.push({\n        name: `tool-${index}-to-generation`,\n        source: `tool-${index}`,\n        target: 'answer-generation',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      documentLoadingNode,\n      documentProcessingNode,\n      questionAnalysisNode,\n      contextRetrievalNode,\n      answerGenerationNode,\n      answerVerificationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'document-qa' as OperationMode,\n    tags: ['document-qa', 'question-answering', 'document-analysis'],\n    metadata: {\n      memoryManager: documentQAMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized PDF QA workflow\n */\nexport function createPDFQAWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  qaAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating PDF QA workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const pdfLoadingNode = Codessa.createAgentNode('pdf-loading', 'PDF Loading', qaAgent);\n  const pdfParsingNode = Codessa.createAgentNode('pdf-parsing', 'PDF Parsing', qaAgent);\n  const textExtractionNode = Codessa.createAgentNode('text-extraction', 'Text Extraction', qaAgent);\n  const imageExtractionNode = Codessa.createAgentNode('image-extraction', 'Image Extraction', qaAgent);\n  const tableExtractionNode = Codessa.createAgentNode('table-extraction', 'Table Extraction', qaAgent);\n  const contentAnalysisNode = Codessa.createAgentNode('content-analysis', 'Content Analysis', qaAgent);\n\n  const pdfQAMemoryManager = {\n    async storePDFContent(content: string, contentType: string, metadata: Record<string, any> = {}): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content,\n          metadata: {\n            source: 'file' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['pdf', 'document', contentType],\n            ...metadata\n          }\n        });\n        logger.info(`Saved PDF ${contentType} content to memory`);\n      } catch (error) {\n        logger.error(`Failed to save PDF ${contentType} content to memory:`, error);\n      }\n    },\n\n    async retrieveRelevantPDFContent(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5,\n          filter: {\n            tags: ['pdf']\n          }\n        });\n\n        logger.info(`Retrieved ${memories.length} relevant PDF content items from memory`);\n        return memories;\n      } catch (error) {\n        logger.error('Failed to retrieve relevant PDF content from memory:', error);\n        return [];\n      }\n    },\n\n    async storePDFQuestion(question: string, answer: string): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify({ question, answer }),\n          metadata: {\n            source: 'conversation' as MemorySource,\n            type: 'conversation' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['pdf', 'document', 'qa', 'question', 'answer']\n          }\n        });\n        logger.info('Saved PDF question-answer pair to memory');\n      } catch (error) {\n        logger.error('Failed to save PDF question-answer pair to memory:', error);\n      }\n    }\n  };\n\n  const questionAnalysisNode = Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);\n  const answerGenerationNode = Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-loading', source: 'input', target: 'pdf-loading', type: 'default' },\n    { name: 'loading-to-parsing', source: 'pdf-loading', target: 'pdf-parsing', type: 'default' },\n    { name: 'parsing-to-text', source: 'pdf-parsing', target: 'text-extraction', type: 'default' },\n    { name: 'parsing-to-image', source: 'pdf-parsing', target: 'image-extraction', type: 'default' },\n    { name: 'parsing-to-table', source: 'pdf-parsing', target: 'table-extraction', type: 'default' },\n    { name: 'text-to-analysis', source: 'text-extraction', target: 'content-analysis', type: 'default' },\n    { name: 'image-to-analysis', source: 'image-extraction', target: 'content-analysis', type: 'default' },\n    { name: 'table-to-analysis', source: 'table-extraction', target: 'content-analysis', type: 'default' },\n    { name: 'analysis-to-question', source: 'content-analysis', target: 'question-analysis', type: 'default' },\n    { name: 'question-to-answer', source: 'question-analysis', target: 'answer-generation', type: 'default' },\n    { name: 'answer-to-output', source: 'answer-generation', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect content analysis to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `analysis-to-tool-${index}`,\n        source: 'content-analysis',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to question analysis\n      edges.push({\n        name: `tool-${index}-to-question`,\n        source: `tool-${index}`,\n        target: 'question-analysis',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      pdfLoadingNode,\n      pdfParsingNode,\n      textExtractionNode,\n      imageExtractionNode,\n      tableExtractionNode,\n      contentAnalysisNode,\n      questionAnalysisNode,\n      answerGenerationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'document-qa' as OperationMode,\n    tags: ['pdf', 'document-qa', 'question-answering'],\n    metadata: {\n      memoryManager: pdfQAMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}