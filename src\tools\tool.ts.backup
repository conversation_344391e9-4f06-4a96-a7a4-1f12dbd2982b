/**
 * Interfaces and types for AI Agent Tools, supporting modern features
 * like structured inputs, multiple actions, and multimodal data.
 * These definitions aim for production quality and completeness, leveraging Zod
 * for schema validation.
 */

// Assuming AgentContext is defined elsewhere, providing environment/state access
import { AgentContext } from '../agents/agentUtilities/agent';
import { IToolOperationMemory } from './toolFrame';

// Zod for schema validation
import { z } from 'zod';

// Zod schema for common tool input
export const ToolInputSchema = z.object({
    action: z.string(),
}).passthrough();

// Zod schema for tool result
export const ToolResultSchema = z.object({
    success: z.boolean(),
    action: z.string(),
    result: z.unknown().optional(),
    error: z.string().optional()
});

/**
 * Represents structured or unstructured data that can be stored in tool input/output
 */
export type ToolData = string | number | boolean | null | Record<string, unknown> | Array<unknown>;

/**
 * Represents the validated and parsed input parameters for a tool execution.
 * The specific structure is defined by the tool's schema(s).
 */
export interface ToolInput {
    [key: string]: ToolData;
    // Multimodal data structure could be standardized here if needed,
    // e.g., images?: { type: 'url' | 'base64' | 'uri', value: string }[];
}

/**
 * Represents the result of a tool execution with memory tracking capabilities.
 */
export interface ToolResult {
    /** Indicates if the tool executed successfully. */
    success: boolean;
    /** The output payload of the tool execution on success. Can be any serializable data. */
    output?: unknown;
    /** An error message or object if the tool execution failed. */
    error?: string | Error;
    /** Optional usage metrics (e.g., tokens consumed, cost). */
    usage?: { [key: string]: number };
    /** The ID of the tool that was executed. */
    toolId: string;
    /** The name of the specific action executed if the tool has multiple actions. */
    actionName?: string;
    /** Additional metadata about the execution or result. */
    metadata?: Record<string, unknown>;
    /** Memory-related information for this tool execution */
    memory?: {
        /** The memory record for this tool operation */
        operationMemory: IToolOperationMemory;
        /** List of related memory IDs created or accessed during execution */
        relatedMemories?: string[];
        /** Whether this operation's memory should be preserved long-term */
        preserve?: boolean;
        /** Optional importance score (0-1) for memory retention */
        importance?: number;
    };
}

/**
 * Defines the structure for a single named action within a multi-action tool.
 */
/**
 * Cost information for tool actions
 */
export interface ToolCost {
    /** The estimated cost in credits/tokens */
    amount: number;
    /** The currency or unit of cost (e.g., 'credits', 'tokens') */
    unit: string;
    /** Additional cost details */
    details?: Record<string, unknown>;
}

/**
 * Represents a tool's metadata
 */
export interface ToolMetadata {
    /** The version of this tool/action */
    version?: string;
    /** When the tool/action was created or last updated */
    updated?: string;
    /** Dependencies required by this tool/action */
    dependencies?: string[];
    /** Additional tool-specific metadata */
    [key: string]: unknown;
}

export interface ToolActionDefinition {
    /** A clear, concise description of what this specific action does. */
    description: string;
    /** A Zod schema defining the expected input parameters for this action. */
    inputSchema: z.ZodType<ToolInput>;
    /** Optional usage cost/pricing information for this specific action. */
    cost?: ToolCost;
    /** Additional metadata specific to this action. */
    metadata?: ToolMetadata;
}

/**
 * Defines an AI Agent Tool. Tools are capabilities that an agent can use
 * to interact with the external world, perform specific tasks, or access information.
 * Supports single-action and multi-action tools, structured inputs via Zod schemas,
 * and multimodal data handling enforced by those schemas.
 */
/**
 * Context options for tool execution
 */
export interface ToolContextOptions {
  /** Whether to validate input against the schema */
  validateInput?: boolean;
  /** Additional context data */
  context?: Record<string, unknown>;
  /** Callback for validation errors */
  onValidationError?: (error: z.ZodError) => unknown;
}

/**
 * Tool configuration options
 */
export interface ToolConfig {
    /** Whether the tool is enabled */
    enabled?: boolean;
    /** Maximum execution time in milliseconds */
    timeout?: number;
    /** Maximum retries on failure */
    maxRetries?: number;
    /** Tool-specific configuration options */
    [key: string]: unknown;
}

export interface ITool {
    /** A unique identifier for the tool. */
    readonly id: string;
    /** The human-readable name of the tool (should be concise and descriptive for the LLM). */
    readonly name: string;
    /** A detailed description of the tool's overall capability. */
    readonly description: string;
    /** Optional category for grouping tools (e.g., 'File System', 'Web Search', 'Database'). */
    readonly category?: string;
    /** Indicates the type of the tool: 'single-action' or 'multi-action'. */
    readonly type: 'single-action' | 'multi-action';
    /** Schema for validating tool input */
    readonly schema: z.ZodType<ToolInput>;
    /** Alias for schema for backward compatibility */
    readonly inputSchema?: z.ZodType<ToolInput>;
    /** Schema for validating tool output */
    readonly outputSchema?: z.ZodType<ToolResult>;
    
    // Multi-action support
    readonly actions?: Record<string, ToolActionDefinition>;
    
    // Configuration and Metadata
    readonly config?: ToolConfig;
    readonly metadata?: ToolMetadata;
    
    // Core Execution
    execute(input: ToolInput, context?: AgentContext): Promise<ToolResult>;
    
    /**
     * Enhanced execution with additional options and context
     * @param input The input to the tool
     * @param options Additional execution options
     * @returns A promise resolving to the tool result
     */
    invoke?(
      input: unknown, 
      options?: ToolContextOptions & { context?: AgentContext }
    ): Promise<ToolResult | unknown>;
    
    // Lifecycle Methods
    initialize?(context?: AgentContext): Promise<void>;
    dispose?(): Promise<void>;
    
    // Advanced Features
    validateInput?(input: unknown): { valid: boolean; error?: string };
    getSchemaForAction?(actionName?: string): z.ZodType<any> | undefined;
    
    // Context Management
    setContext?(context: AgentContext): void;
    getContext?(): AgentContext | undefined;
    
    // Metadata and Documentation
    getDocumentation?(): string;
    getExamples?(): Array<{input: any; output: any; description?: string}>;
    
    // Error Handling
    onError?(error: Error, context?: AgentContext): Promise<void>;
    
    // Performance and Monitoring
    getMetrics?(): Record<string, any>;
    resetMetrics?(): void;
}

// Keep backward compatibility aliases
/**
 * Alias for ITool, maintaining backward compatibility.
 */
export type StructuredTool = ITool;
export type Tool = ITool;

/**
 * Alias for ToolInput, maintaining backward compatibility.
 */
export type ToolRunParams = ToolInput;

/**
 * Alias for ToolResult, maintaining backward compatibility.
 */
export type ToolRunResult = ToolResult;

/**
 * Tool execution metrics
 */
export interface ToolMetrics {
    /** Execution duration in milliseconds */
    duration: number;
    /** CPU usage percentage */
    cpuUsage?: number;
    /** Memory usage in bytes */
    memoryUsage?: number;
    /** Number of API calls made */
    apiCalls?: number;
    /** Number of file operations */
    fileOperations?: number;
    /** Additional metrics */
    [key: string]: unknown;
}

/**
 * Example input/output pair for tool documentation
 */
export interface ToolExample {
    /** Example input */
    input: ToolInput;
    /** Expected output */
    output: ToolResult;
    /** Description of what the example demonstrates */
    description?: string;
}

/**
 * Abstract base class providing common tool functionality
 */
export abstract class BaseTool implements ITool {
    // Core Identification
    public readonly id: string;
    public readonly type: 'single-action' | 'multi-action' = 'single-action';
    public abstract readonly description: string;
    public abstract readonly schema: z.ZodType<ToolInput>;
    public readonly actions?: Record<string, ToolActionDefinition>;
    
    // Context Management
    protected _context?: AgentContext;
    
    // Configuration
    public readonly config: ToolConfig = {};
    public readonly metadata: ToolMetadata = {
        version: '1.0.0'
    };
    
    constructor(public readonly name: string, category?: string) {
        this.id = name.toLowerCase().replace(/\s+/g, '-');
        if (category) {
            this.metadata.category = category;
        }
    }
    
    // Core Execution
    abstract execute(input: ToolInput, context?: AgentContext): Promise<ToolResult>;
    
    /**
     * Enhanced execution with additional options and context
     */
    async execute(
        input: ToolInput, 
        context?: AgentContext,
        options?: ToolContextOptions
    ): Promise<ToolResult> {
        const { validateInput = true, context, onValidationError } = options;
        
        try {
            // Set context if provided
            if (context) {
                this.setContext?.(context);
            }
            
            // Validate input if enabled
            if (validateInput) {
                const validation = this.validateInput?.(input) || { valid: true };
                if (!validation.valid) {
                    const error = new Error(validation.error || 'Input validation failed');
                    onValidationError?.(error as z.ZodError);
                    throw error;
                }
            }
            
            // Execute with current context
            const currentContext = this.getContext?.() || context;
            return await this.execute(
                typeof input === 'object' && input !== null && 'action' in input 
                    ? (input as { action: string }).action 
                    : undefined,
                input as ToolInput,
                currentContext
            );
        } catch (error) {
            await this.onError?.(error as Error, context);
            throw error;
        }
    }
    
    // Lifecycle Methods
    async initialize(context?: AgentContext): Promise<void> {
        if (context) {
            this.setContext?.(context);
        }
    }
    
    async dispose(): Promise<void> {
        // Default implementation does nothing
    }
    
    // Validation
    validateInput(input: unknown): { valid: boolean; error?: string } {
        try {
            this.schema.parse(input);
            return { valid: true };
        } catch (error) {
            return {
                valid: false,
                error: error instanceof Error ? error.message : 'Validation failed'
            };
        }
    }
    
    // Context Management
    setContext(context: AgentContext): void {
        this._context = context;
    }
    
    getContext(): AgentContext | undefined {
        return this._context;
    }
    
    // Error Handling
    async onError(error: Error, context?: AgentContext): Promise<void> {
        // Default error handling - can be overridden by subclasses
        console.error(`[${this.name}] Error:`, error);
    }
    
    // Utility Methods
    getSchemaForAction(actionName?: string): z.ZodType<any> | undefined {
        if (!actionName || !this.actions) {
            return this.schema;
        }
        return this.actions[actionName]?.inputSchema;
    }
    
    getDocumentation(): string {
        return this.description;
    }
    
    getExamples(): Array<{input: any; output: any; description?: string}> {
        return [];
    }
    
    getMetrics(): Record<string, any> {
        return {};
    }
    
    resetMetrics(): void {
        // Default implementation does nothing
    }
    
    // Backward compatibility
    async run(input: unknown): Promise<z.infer<typeof ToolResultSchema>> {
        try {
            const result = await this.invoke(input);
            return {
                success: true,
                action: 'run',
                result: result,
                error: undefined
            };
        } catch (error) {
            // Return error result that matches schema
            const normalizedError = {
                success: false,
                action: input && typeof input === 'object' && 'action' in input ?
                    String(input.action) : 'unknown',
                error: error instanceof Error ? error.message : String(error)
            };

            return normalizedError;
        }
    }

    /**
     * Tool-specific implementation to be provided by subclasses
     * @param actionName The name of the action to execute
     * @param input The input to the tool
     * @param context Optional agent context
     * @returns A Promise resolving to the tool result
     */
    abstract execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult>;
    }

/**
 * Interface for schema-validated tools
 * @deprecated Use ITool directly instead
 */
export interface ISchemaValidatedTool extends ITool {
    /**
     * Validates and runs the tool with the given input
     * @deprecated Use invoke() instead for better type safety and features
     */
    run(input: unknown): Promise<z.infer<typeof ToolResultSchema>>;
}
