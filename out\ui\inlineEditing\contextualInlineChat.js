"use strict";
/**
 * Contextual Inline Chat - Advanced inline chat that understands cursor context
 *
 * Provides intelligent inline chat that integrates with existing Codessa agents
 * and maintains context awareness throughout the conversation.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextualInlineChat = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
class ContextualInlineChat {
    supervisorAgent;
    memoryManager;
    goddessMode;
    disposables = [];
    // Session management
    activeSessions = new Map();
    currentSessionId = null;
    // UI elements
    inlineChatWidget = null;
    statusBarItem;
    constructor(supervisorAgent, memoryManager, goddessMode) {
        this.supervisorAgent = supervisorAgent;
        this.memoryManager = memoryManager;
        this.goddessMode = goddessMode;
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.statusBarItem.text = '$(comment-discussion) Codessa Chat';
        this.statusBarItem.command = 'codessa.toggleInlineChat';
        this.statusBarItem.tooltip = 'Toggle Contextual Inline Chat';
        this.statusBarItem.show();
        this.registerCommands();
        this.setupEventListeners();
    }
    registerCommands() {
        // Register inline chat commands
        const toggleCommand = vscode.commands.registerCommand('codessa.toggleInlineChat', () => {
            this.toggleInlineChat();
        });
        this.disposables.push(toggleCommand);
        const startChatCommand = vscode.commands.registerCommand('codessa.startInlineChat', (args) => {
            this.startInlineChat(args?.position, args?.selection);
        });
        this.disposables.push(startChatCommand);
        const sendMessageCommand = vscode.commands.registerCommand('codessa.sendInlineChatMessage', (message) => {
            this.sendMessage(message);
        });
        this.disposables.push(sendMessageCommand);
    }
    setupEventListeners() {
        // Listen for cursor position changes to update context
        const selectionListener = vscode.window.onDidChangeTextEditorSelection(event => {
            this.updateChatContext(event);
        });
        this.disposables.push(selectionListener);
        // Listen for document changes
        const documentListener = vscode.workspace.onDidChangeTextDocument(event => {
            this.onDocumentChanged(event);
        });
        this.disposables.push(documentListener);
        // Listen for editor changes
        const editorListener = vscode.window.onDidChangeActiveTextEditor(editor => {
            this.onActiveEditorChanged(editor);
        });
        this.disposables.push(editorListener);
    }
    /**
       * Toggle inline chat visibility
       */
    toggleInlineChat() {
        if (this.inlineChatWidget) {
            this.closeInlineChat();
        }
        else {
            this.openInlineChat();
        }
    }
    /**
       * Start a new inline chat session
       */
    async startInlineChat(position, selection) {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor for inline chat');
            return;
        }
        // Use provided position/selection or current cursor position
        const chatPosition = position || editor.selection.active;
        const chatSelection = selection || editor.selection;
        // Build context for the chat session
        const context = await this.buildChatContext(editor.document, chatPosition, chatSelection);
        // Create new session
        const sessionId = `session_${Date.now()}`;
        const session = {
            id: sessionId,
            startTime: Date.now(),
            context,
            messages: [],
            isActive: true
        };
        this.activeSessions.set(sessionId, session);
        this.currentSessionId = sessionId;
        // Open chat widget if not already open
        if (!this.inlineChatWidget) {
            this.openInlineChat();
        }
        // Update UI with new session
        this.updateChatUI(session);
        logger_1.logger.info(`Started inline chat session ${sessionId} at position ${chatPosition.line}:${chatPosition.character}`);
    }
    /**
       * Send a message in the current chat session
       */
    async sendMessage(content) {
        if (!this.currentSessionId) {
            await this.startInlineChat();
        }
        const session = this.activeSessions.get(this.currentSessionId);
        if (!session) {
            logger_1.logger.error('No active chat session found');
            return;
        }
        // Add user message
        const userMessage = {
            id: `msg_${Date.now()}`,
            role: 'user',
            content,
            timestamp: Date.now(),
            context: {
                position: session.context.position,
                selection: session.context.selection,
                codeSnippet: this.getCodeSnippetAtPosition(session.context.document, session.context.position)
            }
        };
        session.messages.push(userMessage);
        // Generate AI response
        const assistantMessage = await this.generateAssistantResponse(session, userMessage);
        session.messages.push(assistantMessage);
        // Update UI
        this.updateChatUI(session);
        // Store conversation in memory
        await this.storeConversationInMemory(session);
    }
    /**
       * Generate AI response using existing Codessa agents
       */
    async generateAssistantResponse(session, userMessage) {
        try {
            // Build comprehensive prompt with context
            const prompt = this.buildContextualPrompt(session, userMessage);
            // Use SupervisorAgent with appropriate mode
            const mode = this.determineAppropriateMode(userMessage.content);
            const result = await this.supervisorAgent.run({
                prompt,
                mode
            }, {
                workspace: {
                    currentFile: session.context.document.uri.toString(),
                    selection: {
                        text: session.context.document.getText(session.context.selection),
                        range: { start: 0, end: 0 }
                    },
                    workspaceFolders: Array.from(vscode.workspace.workspaceFolders ?? []).map(folder => folder.uri.toString())
                }
            });
            let responseContent = result.output || 'I apologize, but I encountered an issue processing your request.';
            // Apply Goddess Mode enhancement if active
            if (this.goddessMode.isGoddessMode()) {
                const enhanced = this.goddessMode.enhanceResponse(responseContent, {
                    taskComplexity: 'moderate',
                    userMood: 'focused'
                });
                responseContent = enhanced.content;
            }
            return {
                id: `msg_${Date.now()}`,
                role: 'assistant',
                content: responseContent,
                timestamp: Date.now(),
                context: {
                    position: session.context.position,
                    selection: session.context.selection,
                    codeSnippet: userMessage.context.codeSnippet
                }
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to generate assistant response: ${error}`);
            return {
                id: `msg_${Date.now()}`,
                role: 'assistant',
                content: 'I apologize, but I encountered an error while processing your request. Please try again.',
                timestamp: Date.now(),
                context: {
                    position: session.context.position,
                    selection: session.context.selection,
                    codeSnippet: userMessage.context.codeSnippet
                }
            };
        }
    }
    /**
       * Build contextual prompt for AI
       */
    buildContextualPrompt(session, userMessage) {
        const context = session.context;
        const conversationHistory = session.messages.slice(-5); // Last 5 messages for context
        return `
# Contextual Inline Chat Request

## Current Context:
- File: ${context.document.uri.fsPath}
- Language: ${context.document.languageId}
- Position: Line ${context.position.line + 1}, Column ${context.position.character + 1}
- Selection: ${context.selection.isEmpty ? 'None' : 'Text selected'}

## Code Context:
\`\`\`${context.document.languageId}
${context.surroundingCode}
\`\`\`

## Conversation History:
${conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

## Current User Message:
${userMessage.content}

## Instructions:
You are Codessa, an advanced AI coding assistant. Respond to the user's message with:
1. Context awareness of the current code and cursor position
2. Specific, actionable advice related to the code
3. Code examples when appropriate
4. Clear explanations that help the user understand

Keep responses concise but helpful, focusing on the immediate context and user needs.
        `;
    }
    /**
       * Determine appropriate agent mode based on user message
       */
    determineAppropriateMode(message) {
        const messageLower = message.toLowerCase();
        if (messageLower.includes('debug') || messageLower.includes('error') || messageLower.includes('bug')) {
            return 'debug';
        }
        if (messageLower.includes('edit') || messageLower.includes('change') || messageLower.includes('modify')) {
            return 'edit';
        }
        if (messageLower.includes('explain') || messageLower.includes('what') || messageLower.includes('how')) {
            return 'ask';
        }
        if (messageLower.includes('refactor') || messageLower.includes('improve') || messageLower.includes('optimize')) {
            return 'refactor';
        }
        if (messageLower.includes('test') || messageLower.includes('unit test')) {
            return 'agent'; // Use agent mode for test generation
        }
        return 'chat'; // Default to chat mode
    }
    /**
       * Build comprehensive chat context with enhanced Phase 3 features
       */
    async buildChatContext(document, position, selection) {
        // Enhanced surrounding code analysis (20 lines for better context)
        const startLine = Math.max(0, position.line - 20);
        const endLine = Math.min(document.lineCount - 1, position.line + 20);
        const surroundingRange = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);
        const surroundingCode = document.getText(surroundingRange);
        // Get enhanced semantic information
        let semanticInfo = {};
        try {
            // Use existing memory system to get relevant context
            const memories = await this.memoryManager.searchMemories({
                query: `file:${document.uri.fsPath} line:${position.line}`,
                limit: 10
            });
            // Add code intelligence
            const codeIntelligence = await this.getCodeIntelligence(document, position);
            // Add project context
            const projectContext = await this.getProjectContext(document);
            semanticInfo = {
                relatedMemories: memories,
                codeIntelligence,
                projectContext,
                smartSuggestions: await this.generateSmartSuggestions(document, position, selection)
            };
        }
        catch (error) {
            logger_1.logger.warn(`Failed to get semantic info: ${error}`);
        }
        return {
            document,
            position,
            selection,
            surroundingCode,
            semanticInfo,
            conversationHistory: []
        };
    }
    /**
       * Get code intelligence information (Phase 3 enhancement)
       */
    async getCodeIntelligence(document, position) {
        try {
            // Get symbol information at cursor
            const symbols = await vscode.commands.executeCommand('vscode.executeDocumentSymbolProvider', document.uri);
            // Get hover information
            const hovers = await vscode.commands.executeCommand('vscode.executeHoverProvider', document.uri, position);
            // Get definition information
            const definitions = await vscode.commands.executeCommand('vscode.executeDefinitionProvider', document.uri, position);
            return {
                symbols: symbols?.slice(0, 5) || [], // Limit to 5 most relevant
                hovers: hovers?.slice(0, 3) || [],
                definitions: definitions?.slice(0, 3) || [],
                currentSymbol: this.findSymbolAtPosition(symbols || [], position)
            };
        }
        catch (error) {
            logger_1.logger.warn('Failed to get code intelligence:', error);
            return {};
        }
    }
    /**
       * Get project-wide context (Phase 3 enhancement)
       */
    async getProjectContext(document) {
        try {
            const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);
            if (!workspaceFolder)
                return {};
            // Get related files
            const relatedFiles = await this.findRelatedFiles(document);
            // Get project structure insights
            const projectStructure = await this.analyzeProjectStructure(workspaceFolder);
            return {
                workspaceFolder: workspaceFolder.name,
                relatedFiles,
                projectStructure,
                language: document.languageId,
                framework: this.detectFramework(document)
            };
        }
        catch (error) {
            logger_1.logger.warn('Failed to get project context:', error);
            return {};
        }
    }
    /**
       * Generate smart suggestions based on context (Phase 3 feature)
       */
    async generateSmartSuggestions(document, position, selection) {
        const suggestions = [];
        try {
            const currentLine = document.lineAt(position.line).text;
            const selectedText = selection.isEmpty ? '' : document.getText(selection);
            // Context-aware suggestions
            if (selectedText) {
                suggestions.push(`Explain this code: "${selectedText.substring(0, 50)}..."`);
                suggestions.push('Refactor this code');
                suggestions.push('Add error handling');
                suggestions.push('Generate tests for this');
            }
            else {
                suggestions.push('Explain what this function does');
                suggestions.push('Add documentation');
                suggestions.push('Suggest improvements');
                suggestions.push('Find potential bugs');
            }
            // Language-specific suggestions
            if (document.languageId === 'typescript' || document.languageId === 'javascript') {
                suggestions.push('Add TypeScript types');
                suggestions.push('Convert to async/await');
            }
            else if (document.languageId === 'python') {
                suggestions.push('Add type hints');
                suggestions.push('Add docstring');
            }
            return suggestions.slice(0, 6); // Limit to 6 suggestions
        }
        catch (error) {
            logger_1.logger.warn('Failed to generate smart suggestions:', error);
            return ['Ask me anything about this code'];
        }
    }
    /**
       * Open inline chat widget
       */
    openInlineChat() {
        this.inlineChatWidget = vscode.window.createWebviewPanel('codessaInlineChat', 'Codessa Inline Chat', vscode.ViewColumn.Beside, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [vscode.Uri.joinPath(vscode.workspace.workspaceFolders[0].uri, 'media')]
        });
        this.inlineChatWidget.webview.html = this.getInlineChatHTML();
        // Handle messages from webview
        this.inlineChatWidget.webview.onDidReceiveMessage(message => {
            this.handleWebviewMessage(message);
        });
        // Handle panel disposal
        this.inlineChatWidget.onDidDispose(() => {
            this.inlineChatWidget = null;
        });
    }
    /**
       * Close inline chat widget
       */
    closeInlineChat() {
        if (this.inlineChatWidget) {
            this.inlineChatWidget.dispose();
            this.inlineChatWidget = null;
        }
    }
    /**
       * Update chat UI with session data
       */
    updateChatUI(session) {
        if (!this.inlineChatWidget)
            return;
        this.inlineChatWidget.webview.postMessage({
            command: 'updateChat',
            session: {
                id: session.id,
                messages: session.messages,
                context: {
                    fileName: session.context.document.fileName,
                    position: session.context.position,
                    language: session.context.document.languageId
                }
            }
        });
    }
    /**
       * Handle messages from webview
       */
    handleWebviewMessage(message) {
        switch (message.command) {
            case 'sendMessage':
                this.sendMessage(message.content);
                break;
            case 'clearChat':
                this.clearCurrentSession();
                break;
            case 'newSession':
                this.startInlineChat();
                break;
        }
    }
    /**
       * Get HTML for inline chat webview
       */
    getInlineChatHTML() {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Codessa Inline Chat</title>
            <style>
                body { 
                    font-family: var(--vscode-font-family);
                    background: var(--vscode-editor-background);
                    color: var(--vscode-editor-foreground);
                    margin: 0;
                    padding: 10px;
                }
                .chat-container {
                    display: flex;
                    flex-direction: column;
                    height: 100vh;
                }
                .chat-header {
                    padding: 10px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                    background: var(--vscode-panel-background);
                }
                .chat-messages {
                    flex: 1;
                    overflow-y: auto;
                    padding: 10px;
                }
                .message {
                    margin-bottom: 15px;
                    padding: 8px 12px;
                    border-radius: 8px;
                }
                .user-message {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    margin-left: 20px;
                }
                .assistant-message {
                    background: var(--vscode-input-background);
                    border: 1px solid var(--vscode-input-border);
                    margin-right: 20px;
                }
                .chat-input {
                    display: flex;
                    padding: 10px;
                    border-top: 1px solid var(--vscode-panel-border);
                    background: var(--vscode-panel-background);
                }
                .chat-input input {
                    flex: 1;
                    padding: 8px;
                    background: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border: 1px solid var(--vscode-input-border);
                    border-radius: 4px;
                }
                .chat-input button {
                    margin-left: 8px;
                    padding: 8px 16px;
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                }
                .context-info {
                    font-size: 0.9em;
                    color: var(--vscode-descriptionForeground);
                    margin-bottom: 10px;
                }
            </style>
        </head>
        <body>
            <div class="chat-container">
                <div class="chat-header">
                    <h3>Codessa Inline Chat</h3>
                    <div class="context-info" id="contextInfo">Ready to help with your code</div>
                </div>
                <div class="chat-messages" id="chatMessages"></div>
                <div class="chat-input">
                    <input type="text" id="messageInput" placeholder="Ask about your code..." />
                    <button onclick="sendMessage()">Send</button>
                    <button onclick="clearChat()">Clear</button>
                </div>
            </div>
            
            <script>
                const vscode = acquireVsCodeApi();
                
                function sendMessage() {
                    const input = document.getElementById('messageInput');
                    const message = input.value.trim();
                    if (message) {
                        vscode.postMessage({
                            command: 'sendMessage',
                            content: message
                        });
                        input.value = '';
                    }
                }
                
                function clearChat() {
                    vscode.postMessage({ command: 'clearChat' });
                }
                
                document.getElementById('messageInput').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
                
                window.addEventListener('message', event => {
                    const message = event.data;
                    if (message.command === 'updateChat') {
                        updateChatDisplay(message.session);
                    }
                });
                
                function updateChatDisplay(session) {
                    const messagesContainer = document.getElementById('chatMessages');
                    const contextInfo = document.getElementById('contextInfo');
                    
                    // Update context info
                    contextInfo.textContent = \`\${session.context.fileName} - Line \${session.context.position.line + 1}\`;
                    
                    // Update messages
                    messagesContainer.innerHTML = '';
                    session.messages.forEach(msg => {
                        const messageDiv = document.createElement('div');
                        messageDiv.className = \`message \${msg.role}-message\`;
                        messageDiv.textContent = msg.content;
                        messagesContainer.appendChild(messageDiv);
                    });
                    
                    // Scroll to bottom
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
            </script>
        </body>
        </html>
        `;
    }
    // Helper methods
    getCodeSnippetAtPosition(document, position) {
        const line = document.lineAt(position.line);
        return line.text;
    }
    async storeConversationInMemory(session) {
        try {
            const lastMessage = session.messages[session.messages.length - 1];
            await this.memoryManager.addMemory({
                content: `Inline chat: ${lastMessage.content.substring(0, 100)}\n\n${JSON.stringify({
                    sessionId: session.id,
                    filePath: session.context.document.uri.fsPath,
                    position: session.context.position,
                    conversation: session.messages.slice(-2) // Last 2 messages
                })}`,
                metadata: {
                    source: 'conversation',
                    type: 'conversation',
                    tags: ['inline-chat', 'conversation', session.context.document.languageId]
                }
            });
        }
        catch (error) {
            logger_1.logger.warn(`Failed to store conversation in memory: ${error}`);
        }
    }
    clearCurrentSession() {
        if (this.currentSessionId) {
            const session = this.activeSessions.get(this.currentSessionId);
            if (session) {
                session.messages = [];
                this.updateChatUI(session);
            }
        }
    }
    updateChatContext(event) {
        if (this.currentSessionId) {
            const session = this.activeSessions.get(this.currentSessionId);
            if (session && session.context.document === event.textEditor.document) {
                session.context.position = event.textEditor.selection.active;
                session.context.selection = event.textEditor.selection;
            }
        }
    }
    onDocumentChanged(event) {
        // Update context for active sessions
        for (const session of this.activeSessions.values()) {
            if (session.context.document === event.document) {
                // Context may have changed, could update surrounding code here
            }
        }
    }
    onActiveEditorChanged(editor) {
        if (editor && this.currentSessionId) {
            // Could start new session for new editor or update existing session
        }
    }
    /**
       * Find symbol at specific position
       */
    findSymbolAtPosition(symbols, position) {
        for (const symbol of symbols) {
            if (symbol.range.contains(position)) {
                // Check children first for more specific symbol
                const childSymbol = this.findSymbolAtPosition(symbol.children || [], position);
                return childSymbol || symbol;
            }
        }
        return undefined;
    }
    /**
       * Find files related to current document
       */
    async findRelatedFiles(document) {
        try {
            const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);
            if (!workspaceFolder)
                return [];
            const fileName = document.fileName;
            const baseName = fileName.replace(/\.[^/.]+$/, '');
            // Look for related files (tests, types, etc.)
            const patterns = [
                `${baseName}.test.*`,
                `${baseName}.spec.*`,
                `${baseName}.d.ts`,
                `${baseName}.types.*`
            ];
            const relatedFiles = [];
            for (const pattern of patterns) {
                const files = await vscode.workspace.findFiles(pattern, null, 5);
                relatedFiles.push(...files.map(f => f.fsPath));
            }
            return relatedFiles.slice(0, 5);
        }
        catch (error) {
            logger_1.logger.warn('Failed to find related files:', error);
            return [];
        }
    }
    /**
       * Analyze project structure for context
       */
    async analyzeProjectStructure(workspaceFolder) {
        try {
            // Look for common project files
            const projectFiles = ['package.json', 'tsconfig.json', 'pyproject.toml', 'Cargo.toml', 'go.mod'];
            const foundFiles = [];
            for (const file of projectFiles) {
                const files = await vscode.workspace.findFiles(new vscode.RelativePattern(workspaceFolder, file), null, 1);
                if (files.length > 0) {
                    foundFiles.push(file);
                }
            }
            return {
                projectFiles: foundFiles,
                hasTests: await this.hasTestDirectory(workspaceFolder),
                hasDocs: await this.hasDocsDirectory(workspaceFolder)
            };
        }
        catch (error) {
            logger_1.logger.warn('Failed to analyze project structure:', error);
            return {};
        }
    }
    /**
       * Detect framework being used
       */
    detectFramework(document) {
        const content = document.getText();
        // Simple framework detection
        if (content.includes('import React') || content.includes('from "react"'))
            return 'React';
        if (content.includes('import Vue') || content.includes('from "vue"'))
            return 'Vue';
        if (content.includes('import { Component }') && content.includes('@angular'))
            return 'Angular';
        if (content.includes('import express') || content.includes('from "express"'))
            return 'Express';
        if (content.includes('from flask') || content.includes('import flask'))
            return 'Flask';
        if (content.includes('from django') || content.includes('import django'))
            return 'Django';
        return undefined;
    }
    /**
       * Check if workspace has test directory
       */
    async hasTestDirectory(workspaceFolder) {
        try {
            const testDirs = await vscode.workspace.findFiles(new vscode.RelativePattern(workspaceFolder, '{test,tests,__tests__,spec}'), null, 1);
            return testDirs.length > 0;
        }
        catch {
            return false;
        }
    }
    /**
       * Check if workspace has docs directory
       */
    async hasDocsDirectory(workspaceFolder) {
        try {
            const docsDirs = await vscode.workspace.findFiles(new vscode.RelativePattern(workspaceFolder, '{docs,documentation,doc}'), null, 1);
            return docsDirs.length > 0;
        }
        catch {
            return false;
        }
    }
    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.statusBarItem.dispose();
        if (this.inlineChatWidget) {
            this.inlineChatWidget.dispose();
        }
        this.activeSessions.clear();
    }
}
exports.ContextualInlineChat = ContextualInlineChat;
//# sourceMappingURL=contextualInlineChat.js.map