{"version": 3, "file": "agentConfigPanel.js", "sourceRoot": "", "sources": ["../../../src/ui/agents/agentConfigPanel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,6DAA0D;AAC1D,2EAAwE;AAGxE,yCAAsC;AACtC,2DAAwD;AACxD,8CAA8C;AAC9C,qDAAkD;AAClD,+DAA4D;AAG5D;;GAEG;AACH,MAAa,gBAAgB;IACpB,MAAM,CAAC,YAAY,CAA+B;IACjD,MAAM,CAAU,QAAQ,GAAG,oBAAoB,CAAC;IAEvC,MAAM,CAAsB;IAC5B,aAAa,CAAa;IACnC,YAAY,GAAwB,EAAE,CAAC;IACvC,MAAM,CAAoB;IAC1B,cAAc,CAAU;IAEhC;;SAEK;IACE,MAAM,CAAC,YAAY,CACxB,YAAwB,EACxB,OAAgB,EAChB,OAAiC;QAEjC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YAC3C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEd,+EAA+E;QAC/E,IAAI,gBAAgB,CAAC,YAAY,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,IAAI,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,KAAK,OAAO,EAAE,CAAC;gBACrE,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACpD,OAAO,gBAAgB,CAAC,YAAY,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,0DAA0D;gBAC1D,gBAAgB,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC1C,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,gBAAgB,CAAC,QAAQ,EACzB,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,EACvC,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;gBAC1C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC;aAC/C;SACF,CACF,CAAC;QAEF,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,2BAAY,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACjF,MAAM,aAAa,GAAG,CAAC,KAAK,CAAC;QAE7B,oCAAoC;QACpC,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,KAAK,GAAG,eAAe,KAAK,CAAC,IAAI,EAAE,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,KAAK,GAAG,kBAAkB,CAAC;QACnC,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;QAEpF,oDAAoD;QACpD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,YACE,KAA0B,EAC1B,YAAwB,EACxB,KAAwB,EACxB,aAAsB;QAEtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,2BAA2B;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACrC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChB,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxB,KAAK,WAAW;oBACd,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC1C,MAAM;gBACR,KAAK,oBAAoB;oBACvB,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACvC,MAAM;gBACR,KAAK,mBAAmB;oBACtB,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBACtC,MAAM;gBACR,KAAK,qBAAqB;oBACxB,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;oBACxC,MAAM;gBACR,KAAK,oBAAoB;oBACvB,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACvC,MAAM;gBACR,KAAK,gBAAgB;oBACnB,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC/C,MAAM;YACV,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CAClB,CAAC;QAEF,0BAA0B;QAC1B,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC;IACvC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,IAAyK;QACtM,IAAI,CAAC;YACH,0CAA0C;YAC1C,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM,WAAW,GAAgB;oBAC/B,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;oBACvC,GAAG,EAAE,IAAI,CAAC,SAAS;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;oBACvB,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,EAAE;oBAC3C,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW;oBACpD,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;oBACpD,WAAW,EAAE,QAAQ;oBACrB,QAAQ,EAAE,OAAO;oBACjB,YAAY,EAAE,IAAI,CAAC,YAAY;iBAChC,CAAC;gBACF,MAAM,KAAK,GAAG,MAAM,2BAAY,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAExE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,eAAe,KAAK,CAAC,IAAI,EAAE,CAAC;gBAEhD,uCAAuC;gBACvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,YAAY;oBAClB,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,4BAA4B;oBACrC,OAAO,EAAE,KAAK,CAAC,EAAE;iBAClB,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,UAAU,KAAK,CAAC,IAAI,yBAAyB,CAAC,CAAC;YACtF,CAAC;iBAAM,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACvB,wBAAwB;gBACxB,MAAM,kBAAkB,GAAgB;oBACtC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAClB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;oBACvC,GAAG,EAAE,IAAI,CAAC,SAAS;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;oBACvB,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,EAAE;oBAC3C,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW;oBACpD,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;oBACpD,WAAW,EAAE,QAAQ;oBACrB,QAAQ,EAAE,OAAO;oBACjB,YAAY,EAAE,IAAI,CAAC,YAAY;iBAChC,CAAC;gBACF,MAAM,oBAAoB,GAAG,IAAI,aAAK,CAAC,kBAAkB,CAAC,CAAC;gBAC3D,MAAM,aAAa,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,oBAAoB,CAAC,CAAC;gBAEnG,IAAI,aAAa,EAAE,CAAC;oBAClB,IAAI,CAAC,MAAM,GAAG,oBAAoB,CAAC;oBACnC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,eAAe,oBAAoB,CAAC,IAAI,EAAE,CAAC;oBAE/D,uCAAuC;oBACvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;wBAC9B,IAAI,EAAE,YAAY;wBAClB,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,4BAA4B;wBACrC,OAAO,EAAE,oBAAoB,CAAC,EAAE;qBACjC,CAAC,CAAC;oBAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,UAAU,oBAAoB,CAAC,IAAI,yBAAyB,CAAC,CAAC;gBACrG,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAEpD,qCAAqC;YACrC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uBAAuB,KAAK,EAAE;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,yBAAyB;QACrC,IAAI,CAAC;YACH,MAAM,SAAS,GAAa,uBAAU,CAAC,eAAe,EAAE,CAAC;YACzD,MAAM,gBAAgB,GAA0B,EAAE,CAAC;YAEnD,KAAK,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC;gBACnC,MAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;gBACpD,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC;wBACH,yCAAyC;wBACzC,MAAM,YAAY,GAAU,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;wBAExD,6CAA6C;wBAC7C,gBAAgB,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;wBAC5C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,UAAU,QAAQ,YAAY,CAAC,MAAM,mBAAmB,CAAC,CAAC;oBAC7F,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;wBAChF,gCAAgC;wBAChC,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC;gCAC9B,EAAE,EAAE,SAAS;gCACb,IAAI,EAAE,eAAe;gCACrB,WAAW,EAAE,iCAAiC;6BAC/C,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,iBAAiB;gBACvB,SAAS;gBACT,gBAAgB;aACjB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAEhE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,0BAA0B,KAAK,EAAE;aAC3C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,wBAAwB;QACpC,IAAI,CAAC;YACH,MAAM,KAAK,GAA0E,2BAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpI,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,OAAO;aACnC,CAAC,CAAC,CAAC;YAEJ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,gBAAgB;gBACtB,KAAK;aACN,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,yBAAyB,KAAK,EAAE;aAC1C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,0BAA0B;QACtC,IAAI,CAAC;YACH,MAAM,OAAO,GAA4C,6BAAa,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpG,IAAI;gBACJ,WAAW,EAAE,6BAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE;aAC5D,CAAC,CAAC,CAAC;YAEJ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,kBAAkB;gBACxB,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAEjE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,2BAA2B,KAAK,EAAE;aAC5C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;SAGK;IACG,KAAK,CAAC,yBAAyB;QACrC,IAAI,CAAC;YACH,MAAM,cAAc,GAAuB,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAE3D,wDAAwD;YACxD,MAAM,MAAM,GAAwD,2BAAY,CAAC,WAAW,EAAE,CAAC,YAAY,EAAE;iBAC1G,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,cAAc,CAAC;iBAC5C,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACb,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;aACrC,CAAC,CAAC,CAAC;YAEN,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,iBAAiB;gBACvB,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAEhE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,0BAA0B,KAAK,EAAE;aAC3C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CAAC,IAA2C;QAC7E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,QAAQ,YAAY,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,QAAQ,iDAAiD,CAAC,CAAC;YAC9F,CAAC;YAED,iDAAiD;YACjD,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,sBAAsB;gBAC5B,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,OAAO,EAAE,UAAU,CAAC,OAAO;aAC5B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAE1D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,sBAAsB;gBAC5B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2BAA2B,KAAK,EAAE;aAC5C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,OAAO;QACb,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;SAEK;IACG,kBAAkB,CAAC,OAAuB;QAChD,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CACpC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,gBAAgB,CAAC,CACnE,CAAC;QAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CACnC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,iBAAiB,CAAC,CACpE,CAAC;QAEF,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,kBAAkB,CAAC,CACzE,CAAC;QAEF,MAAM,KAAK,GAAG,IAAA,gBAAQ,GAAE,CAAC;QAEzB,wCAAwC;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9B,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;YAClB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,EAAE;YAC1C,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC9C,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YAC3D,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SAC5C,CAAC,CAAC,CAAC;YACF,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,EAAE;YACR,WAAW,EAAE,EAAE;YACf,gBAAgB,EAAE,eAAe;YACjC,GAAG,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE;YAC9C,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;SACxB,CAAC;QAEF,4BAA4B;QAC5B,OAAO;;;;;yBAKc,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY;+CAC7B,QAAQ;kGAC2C,OAAO,CAAC,SAAS,8BAA8B,KAAK,gCAAgC,OAAO,CAAC,SAAS;;;;;oCAKnK,OAAO;8BACb,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAsFpD,KAAK;;wCAEE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;4CACrB,IAAI,CAAC,cAAc;;iCAE9B,KAAK,UAAU,SAAS;;oBAErC,CAAC;IACnB,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC;QAE1C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAC3C,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;;AAjhBH,4CAkhBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Agent } from '../../agents/agentUtilities/agent';\nimport { AgentManager } from '../../agents/agentUtilities/agentManager';\nimport { getAgentMemory } from '../../memory/agentMemory';\nimport { getDefaultModelConfig } from '../../config';\nimport { Logger } from '../../logger';\nimport { ToolRegistry } from '../../tools/toolRegistry';\nimport { getNonce } from '../utilities/utils';\nimport { llmService } from '../../llm/llmService';\nimport { promptManager } from '../../prompts/promptManager';\nimport { AgentConfig, AgentRole } from '../../types/agent';\n\n/**\n * Panel for configuring agent settings with a rich UI\n */\nexport class AgentConfigPanel {\n  public static currentPanel: AgentConfigPanel | undefined;\n  private static readonly viewType = 'codessaAgentConfig';\n\n  private readonly _panel: vscode.WebviewPanel;\n  private readonly _extensionUri: vscode.Uri;\n  private _disposables: vscode.Disposable[] = [];\n  private _agent: Agent | undefined;\n  private _isCreatingNew: boolean;\n\n  /**\n     * Create or show an agent configuration panel\n     */\n  public static createOrShow(\n    extensionUri: vscode.Uri,\n    agentId?: string,\n    context?: vscode.ExtensionContext\n  ): AgentConfigPanel {\n    const column = vscode.window.activeTextEditor\n      ? vscode.window.activeTextEditor.viewColumn\n      : undefined;\n\n    // If we already have a panel, show it (unless we're editing a different agent)\n    if (AgentConfigPanel.currentPanel) {\n      if (!agentId || AgentConfigPanel.currentPanel._agent?.id === agentId) {\n        AgentConfigPanel.currentPanel._panel.reveal(column);\n        return AgentConfigPanel.currentPanel;\n      } else {\n        // If editing a different agent, dispose the current panel\n        AgentConfigPanel.currentPanel.dispose();\n      }\n    }\n\n    // Otherwise, create a new panel\n    const panel = vscode.window.createWebviewPanel(\n      AgentConfigPanel.viewType,\n      agentId ? 'Edit Agent' : 'Create Agent',\n      column || vscode.ViewColumn.One,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true,\n        localResourceRoots: [\n          vscode.Uri.joinPath(extensionUri, 'media'),\n          vscode.Uri.joinPath(extensionUri, 'resources')\n        ]\n      }\n    );\n\n    const agent = agentId ? AgentManager.getInstance().getAgent(agentId) : undefined;\n    const isCreatingNew = !agent;\n\n    // Update panel title based on agent\n    if (agent) {\n      panel.title = `Edit Agent: ${agent.name}`;\n    } else {\n      panel.title = 'Create New Agent';\n    }\n\n    const configPanel = new AgentConfigPanel(panel, extensionUri, agent, isCreatingNew);\n\n    // Register panel with extension context if provided\n    if (context) {\n      context.subscriptions.push(panel);\n    }\n\n    return configPanel;\n  }\n\n  private constructor(\n    panel: vscode.WebviewPanel,\n    extensionUri: vscode.Uri,\n    agent: Agent | undefined,\n    isCreatingNew: boolean\n  ) {\n    this._panel = panel;\n    this._extensionUri = extensionUri;\n    this._agent = agent;\n    this._isCreatingNew = isCreatingNew;\n\n    // Set initial HTML content\n    this._update();\n\n    // Handle panel disposal\n    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);\n\n    // Handle messages from the webview\n    this._panel.webview.onDidReceiveMessage(\n      async (message) => {\n        switch (message.command) {\n          case 'saveAgent':\n            await this._handleSaveAgent(message.data);\n            break;\n          case 'getAvailableModels':\n            await this._handleGetAvailableModels();\n            break;\n          case 'getAvailableTools':\n            await this._handleGetAvailableTools();\n            break;\n          case 'getAvailablePrompts':\n            await this._handleGetAvailablePrompts();\n            break;\n          case 'getAvailableAgents':\n            await this._handleGetAvailableAgents();\n            break;\n          case 'testConnection':\n            await this._handleTestConnection(message.data);\n            break;\n        }\n      },\n      null,\n      this._disposables\n    );\n\n    // Cache the current panel\n    AgentConfigPanel.currentPanel = this;\n  }\n\n  /**\n     * Save agent configuration\n     */\n  private async _handleSaveAgent(data: { id: string; name: string; description?: string; systemPromptName: string; llmConfig?: any; tools?: string[]; chainedAgentIds?: string[]; isSupervisor?: boolean }): Promise<void> {\n    try {\n      // Create new agent or update existing one\n      if (this._isCreatingNew) {\n        const agentConfig: AgentConfig = {\n          id: data.id,\n          name: data.name,\n          description: data.description,\n          systemPromptName: data.systemPromptName,\n          llm: data.llmConfig,\n          tools: data.tools || [],\n          chainedAgentIds: data.chainedAgentIds || [],\n          role: data.isSupervisor ? 'supervisor' : 'assistant',\n          capabilities: data.isSupervisor ? ['supervise'] : [],\n          llmProvider: 'openai',\n          llmModel: 'gpt-4',\n          isSupervisor: data.isSupervisor\n        };\n        const agent = await AgentManager.getInstance().createAgent(agentConfig);\n\n        this._agent = agent;\n        this._isCreatingNew = false;\n        this._panel.title = `Edit Agent: ${agent.name}`;\n\n        // Send success message back to webview\n        this._panel.webview.postMessage({\n          type: 'saveResult',\n          success: true,\n          message: 'Agent created successfully',\n          agentId: agent.id\n        });\n\n        vscode.window.showInformationMessage(`Agent \"${agent.name}\" created successfully!`);\n      } else if (this._agent) {\n        // Update existing agent\n        const updatedAgentConfig: AgentConfig = {\n          id: this._agent.id,\n          name: data.name,\n          description: data.description,\n          systemPromptName: data.systemPromptName,\n          llm: data.llmConfig,\n          tools: data.tools || [],\n          chainedAgentIds: data.chainedAgentIds || [],\n          role: data.isSupervisor ? 'supervisor' : 'assistant',\n          capabilities: data.isSupervisor ? ['supervise'] : [],\n          llmProvider: 'openai',\n          llmModel: 'gpt-4',\n          isSupervisor: data.isSupervisor\n        };\n        const updatedAgentInstance = new Agent(updatedAgentConfig);\n        const updateSuccess = AgentManager.getInstance().updateAgent(this._agent.id, updatedAgentInstance);\n\n        if (updateSuccess) {\n          this._agent = updatedAgentInstance;\n          this._panel.title = `Edit Agent: ${updatedAgentInstance.name}`;\n\n          // Send success message back to webview\n          this._panel.webview.postMessage({\n            type: 'saveResult',\n            success: true,\n            message: 'Agent updated successfully',\n            agentId: updatedAgentInstance.id\n          });\n\n          vscode.window.showInformationMessage(`Agent \"${updatedAgentInstance.name}\" updated successfully!`);\n        } else {\n          throw new Error('Failed to update agent');\n        }\n      }\n    } catch (error) {\n      Logger.instance.error('Error saving agent:', error);\n\n      // Send error message back to webview\n      this._panel.webview.postMessage({\n        type: 'saveResult',\n        success: false,\n        message: `Error saving agent: ${error}`\n      });\n\n      vscode.window.showErrorMessage(`Failed to save agent: ${error}`);\n    }\n  }\n\n  /**\n     * Fetch and send available models to the webview\n     */\n  private async _handleGetAvailableModels(): Promise<void> {\n    try {\n      const providers: string[] = llmService.listProviderIds();\n      const modelsByProvider: Record<string, any[]> = {};\n\n      for (const providerId of providers) {\n        const provider = llmService.getProvider(providerId);\n        if (provider) {\n          try {\n            // Get models using the listModels method\n            const modelObjects: any[] = await provider.listModels();\n\n            // Store the full model objects, not just IDs\n            modelsByProvider[providerId] = modelObjects;\n            Logger.instance.info(`Provider ${providerId} has ${modelObjects.length} models available`);\n          } catch (error) {\n            Logger.instance.warn(`Failed to get models for provider ${providerId}:`, error);\n            // Create a default model object\n            modelsByProvider[providerId] = [{\n              id: 'default',\n              name: 'Default Model',\n              description: 'Default model for this provider'\n            }];\n          }\n        }\n      }\n\n      this._panel.webview.postMessage({\n        type: 'availableModels',\n        providers,\n        modelsByProvider\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting available models:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error fetching models: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Fetch and send available tools to the webview\n     */\n  private async _handleGetAvailableTools(): Promise<void> {\n    try {\n      const tools: { id: string; name: string; description: string; category: string }[] = ToolRegistry.instance.getAllTools().map(tool => ({\n        id: tool.id,\n        name: tool.name,\n        description: tool.description || '',\n        category: tool.category || 'Other'\n      }));\n\n      this._panel.webview.postMessage({\n        type: 'availableTools',\n        tools\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting available tools:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error fetching tools: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Fetch and send available system prompts to the webview\n     */\n  private async _handleGetAvailablePrompts(): Promise<void> {\n    try {\n      const prompts: { name: string; description: string }[] = promptManager.listPromptNames().map(name => ({\n        name,\n        description: promptManager.getPromptDescription(name) || ''\n      }));\n\n      this._panel.webview.postMessage({\n        type: 'availablePrompts',\n        prompts\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting available prompts:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error fetching prompts: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Fetch and send available agents to the webview\n     * (for supervisor agent chaining)\n     */\n  private async _handleGetAvailableAgents(): Promise<void> {\n    try {\n      const currentAgentId: string | undefined = this._agent?.id;\n\n      // Filter out current agent to avoid circular references\n      const agents: { id: string; name: string; description: string }[] = AgentManager.getInstance().getAllAgents()\n        .filter(agent => agent.id !== currentAgentId)\n        .map(agent => ({\n          id: agent.id,\n          name: agent.name,\n          description: agent.description || ''\n        }));\n\n      this._panel.webview.postMessage({\n        type: 'availableAgents',\n        agents\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting available agents:', error);\n\n      this._panel.webview.postMessage({\n        type: 'error',\n        message: `Error fetching agents: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Test connection to the selected LLM provider\n     */\n  private async _handleTestConnection(data: { provider: string; modelId: string }): Promise<void> {\n    try {\n      const provider = llmService.getProvider(data.provider);\n      if (!provider) {\n        throw new Error(`Provider ${data.provider} not found`);\n      }\n\n      if (!provider.isConfigured()) {\n        throw new Error(`Provider ${data.provider} is not configured. Please check your settings.`);\n      }\n\n      // All providers now have a testConnection method\n      const testResult = await provider.testConnection(data.modelId);\n\n      this._panel.webview.postMessage({\n        type: 'connectionTestResult',\n        success: testResult.success,\n        message: testResult.message\n      });\n    } catch (error) {\n      Logger.instance.error('Error testing connection:', error);\n\n      this._panel.webview.postMessage({\n        type: 'connectionTestResult',\n        success: false,\n        message: `Connection test failed: ${error}`\n      });\n    }\n  }\n\n  /**\n     * Generate HTML for the webview panel\n     */\n  private _update() {\n    const webview = this._panel.webview;\n    webview.html = this._getWebviewContent(webview);\n  }\n\n  /**\n     * Generate the webview HTML content for agent configuration\n     */\n  private _getWebviewContent(webview: vscode.Webview): string {\n    const scriptUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'media', 'agentConfig.js')\n    );\n\n    const styleUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'media', 'agentConfig.css')\n    );\n\n    const logoUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'resources', 'codessa-logo.png')\n    );\n\n    const nonce = getNonce();\n\n    // Initialize agent data (for edit mode)\n    const agentData = this._agent ? {\n      id: this._agent.id,\n      name: this._agent.name,\n      description: this._agent.description || '',\n      systemPromptName: this._agent.systemPromptName,\n      llm: this._agent.llmConfig || { provider: '', modelId: '' },\n      tools: Array.from(this._agent.tools.keys()),\n    } : {\n      id: '',\n      name: '',\n      description: '',\n      systemPromptName: 'default_coder',\n      llm: { provider: 'openai', modelId: 'gpt-4o' },\n      tools: ['file', 'docs'],\n    };\n\n    // Generate the HTML content\n    return `<!DOCTYPE html>\n            <html lang=\"en\">\n            <head>\n                <meta charset=\"UTF-8\">\n                <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n                <title>${this._isCreatingNew ? 'Create Agent' : 'Edit Agent'}</title>\n                <link rel=\"stylesheet\" href=\"${styleUri}\">\n                <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; img-src ${webview.cspSource} https:; script-src 'nonce-${nonce}' 'unsafe-inline'; style-src ${webview.cspSource};\">\n            </head>\n            <body>\n                <div class=\"config-container\">\n                    <header class=\"config-header\">\n                        <img src=\"${logoUri}\" alt=\"Codessa Logo\" class=\"logo\" />\n                        <h1>${this._isCreatingNew ? 'Create New Agent' : 'Edit Agent'}</h1>\n                    </header>\n\n                    <div class=\"config-form\">\n                        <div class=\"form-section\">\n                            <h2>Basic Information</h2>\n                            <div class=\"form-group\">\n                                <label for=\"agent-name\">Name:</label>\n                                <input type=\"text\" id=\"agent-name\" class=\"form-control\" placeholder=\"Enter agent name\" required />\n                            </div>\n\n                            <div class=\"form-group\">\n                                <label for=\"agent-description\">Description:</label>\n                                <textarea id=\"agent-description\" class=\"form-control\" placeholder=\"Enter agent description\" rows=\"3\"></textarea>\n                            </div>\n                        </div>\n\n                        <div class=\"form-section\">\n                            <h2>Model Configuration</h2>\n                            <div class=\"form-group\">\n                                <label for=\"llm-provider\">LLM Provider:</label>\n                                <select id=\"llm-provider\" class=\"form-control\">\n                                    <option value=\"\">-- Select Provider --</option>\n                                </select>\n                                <div class=\"description\">Select the AI provider for this agent</div>\n                            </div>\n\n                            <div class=\"form-group\">\n                                <label for=\"llm-model\">Model:</label>\n                                <select id=\"llm-model\" class=\"form-control\">\n                                    <option value=\"\">-- Select Model --</option>\n                                </select>\n                                <div class=\"description\">Select the model to use</div>\n                            </div>\n\n                            <div class=\"form-group\">\n                                <button id=\"btn-test-connection\" class=\"btn secondary\">Test Connection</button>\n                                <span id=\"connection-status\"></span>\n                            </div>\n                        </div>\n\n                        <div class=\"form-section\">\n                            <h2>Prompt & Tools</h2>\n                            <div class=\"form-group\">\n                                <label for=\"system-prompt\">System Prompt:</label>\n                                <select id=\"system-prompt\" class=\"form-control\">\n                                    <option value=\"\">-- Select System Prompt --</option>\n                                </select>\n                                <div class=\"description\">Select the system prompt that defines agent behavior</div>\n                            </div>\n\n                            <div class=\"form-group\">\n                                <label>Available Tools:</label>\n                                <div id=\"tools-container\" class=\"checkbox-container\">\n                                    <!-- Tool checkboxes will be inserted here -->\n                                </div>\n                                <div class=\"description\">Select the tools this agent can use</div>\n                            </div>\n                        </div>\n\n                        <div class=\"form-section\">\n                            <h2>Advanced Settings</h2>\n                            <div class=\"form-group\">\n                                <label class=\"checkbox-label\">\n                                    <input type=\"checkbox\" id=\"is-supervisor\" />\n                                    This is a supervisor agent\n                                </label>\n                                <div class=\"description\">Supervisor agents can orchestrate other agents</div>\n                            </div>\n\n                            <div id=\"chained-agents-section\" class=\"form-group\" style=\"display: none;\">\n                                <label>Chained Agents:</label>\n                                <div id=\"chained-agents-container\" class=\"checkbox-container\">\n                                    <!-- Agent checkboxes will be inserted here -->\n                                </div>\n                                <div class=\"description\">Select the agents this supervisor can orchestrate</div>\n                            </div>\n                        </div>\n\n                        <div class=\"form-actions\">\n                            <button id=\"btn-save\" class=\"btn primary\">Save Agent</button>\n                            <button id=\"btn-cancel\" class=\"btn secondary\">Cancel</button>\n                        </div>\n                    </div>\n                </div>\n\n                <script nonce=\"${nonce}\">\n                    // Initial agent data\n                    const agentData = ${JSON.stringify(agentData)};\n                    const isCreatingNew = ${this._isCreatingNew};\n                </script>\n                <script nonce=\"${nonce}\" src=\"${scriptUri}\"></script>\n            </body>\n            </html>`;\n  }\n\n  /**\n     * Clean up resources\n     */\n  public dispose() {\n    AgentConfigPanel.currentPanel = undefined;\n\n    this._panel.dispose();\n\n    while (this._disposables.length) {\n      const disposable = this._disposables.pop();\n      if (disposable) {\n        disposable.dispose();\n      }\n    }\n  }\n}"]}