{"version": 3, "file": "dashboardPanel.js", "sourceRoot": "", "sources": ["../../../src/ui/dashboard/dashboardPanel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2BAA2B;AAC3B,+CAAiC;AAEjC,MAAa,cAAc;IAClB,MAAM,CAAC,YAAY,CAA6B;IACtC,MAAM,CAAsB;IAC5B,aAAa,CAAa;IAEpC,MAAM,CAAU,QAAQ,GAAG,mBAAmB,CAAC;IAEtD,YAAoB,KAA0B,EAAE,YAAwB;QACtE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACvD,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,YAAwB;QACjD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;QACtG,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;YAChC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAClD,OAAO;QACT,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,cAAc,CAAC,QAAQ,EACvB,mBAAmB,EACnB,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B,EAAE,aAAa,EAAE,IAAI,EAAE,CACxB,CAAC;QACF,cAAc,CAAC,YAAY,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACxE,CAAC;IAEO,kBAAkB;QACxB,qDAAqD;QACrD,OAAO;;;;;;;;;;QAUH,CAAC;IACP,CAAC;;AAzCH,wCA0CC", "sourcesContent": ["// src/ui/dashboardPanel.ts\nimport * as vscode from 'vscode';\n\nexport class DashboardPanel {\n  public static currentPanel: DashboardPanel | undefined;\n  private readonly _panel: vscode.WebviewPanel;\n  private readonly _extensionUri: vscode.Uri;\n\n  public static readonly viewType = 'codessa.dashboard';\n\n  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {\n    this._panel = panel;\n    this._extensionUri = extensionUri;\n    this._panel.webview.html = this._getHtmlForWebview();\n  }\n\n  public static createOrShow(extensionUri: vscode.Uri) {\n    const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined;\n    if (DashboardPanel.currentPanel) {\n      DashboardPanel.currentPanel._panel.reveal(column);\n      return;\n    }\n    const panel = vscode.window.createWebviewPanel(\n      DashboardPanel.viewType,\n      'Codessa Dashboard',\n      column || vscode.ViewColumn.One,\n      { enableScripts: true }\n    );\n    DashboardPanel.currentPanel = new DashboardPanel(panel, extensionUri);\n  }\n\n  private _getHtmlForWebview(): string {\n    // Basic HTML content; replace with your dashboard UI\n    return `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <title>Codessa Dashboard</title>\n</head>\n<body>\n  <h1>Codessa Dashboard</h1>\n  <p>Welcome to the Codessa extension!</p>\n</body>\n</html>`;\n  }\n}\n"]}