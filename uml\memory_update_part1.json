{"_type": "UMLClass", "_id": "AAAAAAGH1CodessaMemoryProvider=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "CodessaMemory<PERSON>", "stereotype": "", "documentation": "Implements the IMemoryProvider interface using pluggable vector stores, databases, and integrates LangChain memory components for enhanced functionality.", "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaMemoryProviderAttr1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "context", "visibility": "private", "type": "vscode.ExtensionContext | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaMemoryProviderAttr2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "vectorStore", "visibility": "private", "type": "IVectorStore | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaMemoryProviderAttr3=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "database", "visibility": "private", "type": "IDatabase | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaMemoryProviderAttr4=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "embeddings", "visibility": "private", "type": "Embeddings | undefined"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaMemoryProviderAttr5=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "initialized", "visibility": "private", "type": "boolean", "defaultValue": "false"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaMemoryProviderAttr6=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "isInitializing", "visibility": "private", "type": "boolean", "defaultValue": "false"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaMemoryProviderAttr7=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "_onMemoriesChanged", "visibility": "private", "type": "vscode.EventEmitter<void>"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1CodessaMemoryProviderAttr8=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "onMemoriesChanged", "visibility": "public", "type": "vscode.Event<void>", "isReadOnly": true}], "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaMemoryProviderOp1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "initialize", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp1P1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp1="}, "name": "context", "type": "vscode.ExtensionContext"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp1P2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp1="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaMemoryProviderOp2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "addMemory", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp2P1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp2="}, "name": "memory", "type": "Omit<MemoryEntry, 'id' | 'timestamp' | 'embedding'>"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp2P2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp2="}, "type": "Promise<MemoryEntry>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaMemoryProviderOp3=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "getMemories", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp3P1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp3="}, "name": "limit", "type": "number", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp3P2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp3="}, "type": "Promise<MemoryEntry[]>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaMemoryProviderOp4=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "get<PERSON><PERSON>ory", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp4P1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp4="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp4P2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp4="}, "type": "Promise<MemoryEntry | undefined>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaMemoryProviderOp5=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "deleteMemory", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp5P1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp5="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp5P2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp5="}, "type": "Promise<boolean>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaMemoryProviderOp6=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "clearMemories", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp6P1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp6="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaMemoryProviderOp7=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "searchMemories", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp7P1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp7="}, "name": "options", "type": "MemorySearchOptions"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp7P2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp7="}, "type": "Promise<MemoryEntry[]>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1CodessaMemoryProviderOp8=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProvider="}, "name": "searchSimilarMemories", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp8P1=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp8="}, "name": "query", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp8P2=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp8="}, "name": "options", "type": "Partial<MemorySearchOptions>", "defaultValue": "{}"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1CodessaMemoryProviderOp8P3=", "_parent": {"$ref": "AAAAAAGH1CodessaMemoryProviderOp8="}, "type": "Promise<MemoryEntry[]>", "direction": "return"}]}], "isAbstract": false}