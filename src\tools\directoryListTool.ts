import * as vscode from 'vscode';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import { z } from 'zod';

export class DirectoryListTool implements ITool {
  readonly id = 'listDir';
  readonly name = 'List Directory';
  readonly description = 'Lists the contents of a directory (files and subdirectories).';
  readonly type = 'single-action';
  readonly schema = z.object({
    dirPath: z.string().describe('Path to the directory (relative to workspace root or absolute).')
  });
  readonly actions: Record<string, any> = {};

  async execute(input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const dirPath = input.dirPath as string;
    if (!dirPath) {
      return { success: false, error: '\'dirPath\' is required.', toolId: this.id };
    }
    let dirUri: vscode.Uri | undefined;
    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
      const workspaceRoot = vscode.workspace.workspaceFolders[0].uri;
      try {
        const uri = vscode.Uri.parse(dirPath);
        if (uri.scheme) dirUri = uri;
        else dirUri = vscode.Uri.joinPath(workspaceRoot, dirPath);
      } catch {
        dirUri = vscode.Uri.joinPath(workspaceRoot, dirPath);
      }
    } else {
      try {
        const uri = vscode.Uri.parse(dirPath);
        if (uri.scheme) dirUri = uri;
      } catch {
        // Handle empty catch block by doing nothing
      }
    }
    if (!dirUri) {
      return { success: false, error: `Could not resolve directory path: ${dirPath}.`, toolId: this.id };
    }
    try {
      const entries = await vscode.workspace.fs.readDirectory(dirUri);
      const result = entries.map(([name, type]) => ({
        name,
        type: type === vscode.FileType.Directory ? 'directory' : 'file'
      }));
      return { success: true, output: result, toolId: this.id };
    } catch (error: any) {
      return { success: false, error: `Failed to list directory: ${error.message || error}`, toolId: this.id };
    }
  }
}

export const directoryListTool = new DirectoryListTool();
