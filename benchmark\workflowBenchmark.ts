import * as path from 'path';
import { performance } from 'perf_hooks';
import { Workflow, WorkflowStep, WorkflowContext, WorkflowStepResult, workflowRegistry, WorkflowDefinition, commonWorkflowSteps } from '../src/agents/workflows/workflowEngine';
import { performanceMonitor } from '../src/utils/performance';
import { Logger } from '../src/logger';
import { Agent } from '../src/agents/agentUtilities/agent';

// Simple logger for benchmark
const logger = {
  info: console.log,
  debug: console.debug,
  warn: console.warn,
  error: console.error
};

class WorkflowBenchmark {
  private workflow: Workflow;
  private agent: Agent;
  private results: Array<{
    stepId: string;
    duration: number;
    success: boolean;
    error?: string;
  }> = [];

  constructor(steps: number) {
    // Create a simple workflow with the specified number of steps
    const workflowDefinition = this.createWorkflowDefinition(steps);
    this.workflow = new Workflow(workflowDefinition);
    
    // Create a mock agent
    this.agent = {
      id: 'benchmark-agent',
      name: 'Benchmark Agent',
      type: 'benchmark',
      execute: async () => ({ success: true, output: 'Benchmark completed' })
    } as unknown as Agent;
    
    // Set up the workflow
    this.workflow.setAgent(this.agent);
    this.workflow.setInputs({ input: 'test' });
  }

  private createWorkflowDefinition(steps: number): WorkflowDefinition {
    const workflowSteps: WorkflowStep[] = [];

    // Create a chain of steps
    for (let i = 0; i < steps; i++) {
      const step: WorkflowStep = {
        id: `step-${i}`,
        name: `Test Step ${i}`,
        description: `A test workflow step ${i}`,
        nextSteps: i < steps - 1 ? [`step-${i + 1}`] : [],
        execute: async (context: WorkflowContext) => {
          // Simulate some work
          const input = context.inputs?.input || '';
          await new Promise((resolve) => setTimeout(resolve, 10)); // 10ms delay
          
          const result: WorkflowStepResult = {
            success: true,
            output: `${input} -> Processed by step-${i}`,
            nextStepId: i < steps - 1 ? `step-${i + 1}` : undefined
          };
          
          return result;
        }
      };
      
      workflowSteps.push(step);
    }

    // Add a final step that doesn't have a next step
    const finalStep: WorkflowStep = {
      id: `step-${steps}`,
      name: 'Final Step',
      description: 'Final step in the workflow',
      nextSteps: [],
      execute: async () => ({
        success: true,
        output: 'Workflow completed',
      }),
    };
    
    workflowSteps.push(finalStep);

    const workflowDef: WorkflowDefinition = {
      id: 'test-workflow',
      name: 'Test Workflow',
      description: 'A test workflow for benchmarking',
      version: '1.0.0',
      steps: workflowSteps,
      inputs: [{
        id: 'input',
        name: 'Input',
        description: 'Input value',
        type: 'string',
        required: true
      }],
      outputs: [{
        id: 'output',
        name: 'Output',
        description: 'Output value',
        type: 'string'
      }],
      startStepId: 'step-0',
    };
    
    return workflowDef;
  }

  async run() {
    const startTime = performance.now();
    
    try {
      // Register the workflow definition
      workflowRegistry.registerWorkflow(this.workflow.getDefinition());
      
      // Create a new workflow instance from the registry
      const workflowInstance = workflowRegistry.createWorkflowInstance('test-workflow');
      
      // Set up the workflow instance
      workflowInstance.setAgent(this.agent);
      workflowInstance.setInputs({ input: 'test' });
      
      // Create the workflow context
      const context: WorkflowContext = {
        workflow: workflowInstance,
        agent: this.agent,
        inputs: { input: 'test' },
        outputs: {},
        variables: {},
        history: [],
        revolutionaryFeatures: {
          goddessMode: false,
          quantumAnalysis: false,
          neuralSynthesis: false,
          timeTravelDebugging: false,
          adaptivePersonality: false
        },
        emotionalContext: {
          developerMood: 'neutral',
          motivationalLevel: 50,
          stressLevel: 30,
          creativityBoost: 0
        },
        quantumState: {
          superposition: false,
          entangled: false,
          parallelExecutions: 1,
          quantumInterference: false
        },
        neuralMemory: {
          synapticConnections: new Map(),
          learningHistory: [],
          consciousnessLevel: 0
        }
      };
      
      // Execute the workflow steps directly
      let currentStepId = workflowInstance.getDefinition().startStepId;
      let result: WorkflowStepResult | undefined;
      
      while (currentStepId) {
        const step = workflowInstance.getDefinition().steps.find(s => s.id === currentStepId);
        if (!step) {
          throw new Error(`Step ${currentStepId} not found`);
        }
        
        const stepStartTime = performance.now();
        result = await step.execute(context);
        const stepDuration = performance.now() - stepStartTime;
        
        // Record step execution
        this.results.push({
          stepId: step.id,
          duration: stepDuration,
          success: result.success,
          error: result.error
        });
        
        // Update context
        context.history.push({
          stepId: step.id,
          startTime: new Date(stepStartTime),
          endTime: new Date(),
          result
        });
        
        // Move to next step
        currentStepId = result.nextStepId || '';
      }
      
      return {
        success: true,
        duration: performance.now() - startTime,
        steps: this.results,
        outputs: context.outputs,
        result
      };
    } catch (error) {
      return {
        success: false,
        duration: performance.now() - startTime,
        steps: this.results,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
};

// Benchmark function
async function runBenchmark(iterations: number, stepCount: number) {
  console.log(`\n🚀 Starting benchmark: ${iterations} iterations with ${stepCount} steps each\n`);
  
  const executionTimes = {
    withCache: [] as number[],
    withoutCache: [] as number[],
  };

  // Test with caching enabled (default)
  console.log('🔧 Testing with caching enabled...');
  for (let i = 0; i < iterations; i++) {
    const benchmark = new WorkflowBenchmark(stepCount);
    
    // Measure execution time
    const start = performance.now();
    const result = await benchmark.run();
    const end = performance.now();
    
    if (!result.success) {
      console.error(`❌ Iteration ${i + 1} failed: ${result.error}`);
      continue;
    }
    
    executionTimes.withCache.push(end - start);
    console.log(`  ✓ Iteration ${i + 1}: ${(end - start).toFixed(2)}ms`);
    
    // Clear cache between iterations
    performanceMonitor.clearMetrics();
  }

  // Test with caching disabled
  console.log('\n🔧 Testing with caching disabled...');
  for (let i = 0; i < iterations; i++) {
    const benchmark = new WorkflowBenchmark(stepCount);
    
    // Disable caching for this test
    benchmark['workflow']['stepCache'] = new Map();
    
    // Measure execution time
    const start = performance.now();
    const result = await benchmark.run();
    const end = performance.now();
    
    if (!result.success) {
      console.error(`❌ Iteration ${i + 1} failed: ${result.error}`);
      continue;
    }
    
    executionTimes.withoutCache.push(end - start);
    console.log(`  ✓ Iteration ${i + 1}: ${(end - start).toFixed(2)}ms`);
    
    // Clear cache between iterations
    performanceMonitor.clearMetrics();
  }

  // Calculate statistics
  const calculateStats = (times: number[]) => {
    const sum = times.reduce((a, b) => a + b, 0);
    const avg = sum / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);
    
    // Calculate standard deviation
    const squareDiffs = times.map(time => Math.pow(time - avg, 2));
    const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / times.length;
    const stdDev = Math.sqrt(avgSquareDiff);
    
    return { avg, min, max, stdDev };
  };

  const withCacheStats = calculateStats(executionTimes.withCache);
  const withoutCacheStats = calculateStats(executionTimes.withoutCache);
  const improvement = ((withoutCacheStats.avg - withCacheStats.avg) / withoutCacheStats.avg) * 100;

  // Print results
  console.log('\n📊 Benchmark Results:');
  console.log('='.repeat(50));
  console.log(`Test Configuration:`);
  console.log(`- Iterations: ${iterations}`);
  console.log(`- Steps per workflow: ${stepCount}`);
  console.log('\nPerformance Metrics:');
  console.log('| Metric           | With Cache | Without Cache | Improvement |');
  console.log('|------------------|------------|---------------|-------------|');
  console.log(`| Avg Time (ms)    | ${withCacheStats.avg.toFixed(2)} | ${withoutCacheStats.avg.toFixed(2)} | ${improvement.toFixed(2)}% |`);
  console.log(`| Min Time (ms)    | ${withCacheStats.min.toFixed(2)} | ${withoutCacheStats.min.toFixed(2)} |`);
  console.log(`| Max Time (ms)    | ${withCacheStats.max.toFixed(2)} | ${withoutCacheStats.max.toFixed(2)} |`);
  console.log(`| Std Dev (ms)     | ${withCacheStats.stdDev.toFixed(2)} | ${withoutCacheStats.stdDev.toFixed(2)} |`);
  console.log('\n💡 Note: Lower values are better for all metrics.');

  // Additional insights
  console.log('\n🔍 Insights:');
  if (improvement > 0) {
    console.log(`✅ Caching improved performance by ${improvement.toFixed(2)}% on average`);
  } else {
    console.log('ℹ️  Caching did not improve performance in this test. This could be due to:');
    console.log('   - All steps having unique inputs (no cache hits)');
    console.log('   - The overhead of caching being higher than the work being cached');
    console.log('   - The test workload being too simple to benefit from caching');
  }
}

// Run the benchmark
async function main() {
  try {
    console.log('🏁 Starting Workflow Engine Benchmark\n');
    
    // Warm-up run
    console.log('🔥 Warming up...');
    await runBenchmark(1, 5);
    
    // Main benchmark runs
    console.log('\n🚀 Running main benchmark...');
    await runBenchmark(10, 10); // 10 iterations, 10 steps each
    
    // Test with more steps
    console.log('\n📈 Testing with more steps...');
    await runBenchmark(5, 50); // 5 iterations, 50 steps each
    
    // Test with more iterations
    console.log('\n⏱️  Testing with more iterations...');
    await runBenchmark(50, 5); // 50 iterations, 5 steps each
    
    console.log('\n✅ Benchmark completed successfully!');
  } catch (error) {
    console.error('❌ Benchmark failed:', error);
    process.exit(1);
  }
}

// Run the benchmark
main();
