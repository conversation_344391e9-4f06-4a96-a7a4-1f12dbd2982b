"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.YiCodeProvider = void 0;
const vscode = __importStar(require("vscode"));
const baseLLMProvider_1 = require("./baseLLMProvider");
const logger_1 = require("../../logger");
// Use require for axios to avoid TypeScript issues
const axios = require('axios');
/**
 * Provider for Yi-Code models from 01.ai
 *
 * Yi-Code is a powerful code model from 01.ai
 * It can be used via Hugging Face or self-hosted
 */
class YiCodeProvider extends baseLLMProvider_1.BaseLLMProvider {
    providerId = 'yicode';
    displayName = 'Yi-Code';
    description = 'Powerful code model from 01.ai';
    website = 'https://01.ai/';
    requiresApiKey = true; // Required for Hugging Face API
    supportsEndpointConfiguration = true;
    defaultEndpoint = 'https://api-inference.huggingface.co/models';
    defaultModel = '01-ai/Yi-34B-Code';
    client = null;
    constructor(context) {
        super(context);
        this.initializeClient();
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('codessa.llm.providers')) {
                logger_1.Logger.instance.info('Yi-Code configuration changed, re-initializing client.');
                this.loadConfig().then(() => this.initializeClient());
            }
        });
    }
    initializeClient() {
        const apiKey = this.config.apiKey;
        const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;
        if (!apiKey) {
            logger_1.Logger.instance.warn('API key not set for Yi-Code provider.');
            this.client = null;
            return;
        }
        try {
            // Initialize axios client with proper configuration
            this.client = axios.create({
                baseURL: baseUrl,
                timeout: 120000, // 2 minutes timeout for model loading
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            logger_1.Logger.instance.info('Yi-Code client initialized successfully.');
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to initialize Yi-Code client:', error);
            this.client = null;
        }
    }
    isConfigured() {
        return !!this.client;
    }
    /**
       * Generate text using Yi-Code models
       */
    async generate(params, cancellationToken, tools) {
        if (!this.client) {
            return { content: '', error: 'Yi-Code provider not configured (API key missing?)' };
        }
        try {
            // Prepare the model endpoint - for Hugging Face we need to specify the model in the URL
            const modelId = params.modelId || this.config.defaultModel || this.defaultModel;
            const endpoint = `${modelId}`;
            // Prepare the prompt
            let prompt = '';
            // Yi-Code uses a specific format for chat
            if (params.systemPrompt) {
                prompt += `<|im_start|>system\n${params.systemPrompt}<|im_end|>\n\n`;
            }
            // Add history if provided
            if (params.history && params.history.length > 0) {
                for (const message of params.history) {
                    if (message.role === 'user') {
                        prompt += `<|im_start|>user\n${message.content}<|im_end|>\n\n`;
                    }
                    else if (message.role === 'assistant') {
                        prompt += `<|im_start|>assistant\n${message.content}<|im_end|>\n\n`;
                    }
                    else if (message.role === 'system') {
                        prompt += `<|im_start|>system\n${message.content}<|im_end|>\n\n`;
                    }
                }
            }
            else {
                // Just add the user prompt
                prompt += `<|im_start|>user\n${params.prompt}<|im_end|>\n\n`;
            }
            // Add the assistant prefix to indicate we want the model to generate a response
            prompt += '<|im_start|>assistant\n';
            // Check for cancellation before making the request
            if (cancellationToken?.isCancellationRequested) {
                return { content: '', error: 'Request cancelled before sending' };
            }
            // Prepare request data
            const requestData = {
                inputs: prompt,
                parameters: {
                    max_new_tokens: params.maxTokens || 1024,
                    temperature: params.temperature || 0.7,
                    top_p: 0.95,
                    do_sample: true,
                    return_full_text: false,
                    stop: params.stopSequences || ['<|im_start|>', '<|im_end|>']
                }
            };
            logger_1.Logger.instance.debug(`Sending request to Yi-Code model ${modelId}`);
            // Create cancellation token source to abort the request if needed
            let abortController;
            if (cancellationToken) {
                if (typeof AbortController !== 'undefined') {
                    abortController = new AbortController();
                    cancellationToken.onCancellationRequested(() => {
                        logger_1.Logger.instance.info('Yi-Code request cancelled by user');
                        abortController?.abort();
                    });
                }
                else {
                    logger_1.Logger.instance.warn('AbortController not available in this environment, cancellation may not work properly');
                }
            }
            // Make the API request
            const response = await this.client.post(endpoint, requestData, {
                signal: abortController?.signal
            });
            // Check for cancellation again after API call
            if (cancellationToken?.isCancellationRequested) {
                return { content: '', error: 'Request cancelled during processing' };
            }
            // Parse the response
            const result = response.data;
            // Hugging Face Inference API returns an array of generated texts
            let content = '';
            if (Array.isArray(result) && result.length > 0) {
                if (result[0].generated_text) {
                    content = result[0].generated_text;
                }
            }
            return {
                content,
                finishReason: 'stop',
                usage: {
                    promptTokens: prompt.length / 4, // Rough estimate
                    completionTokens: content.length / 4, // Rough estimate
                }
            };
        }
        catch (error) {
            logger_1.Logger.instance.error('Yi-Code generate error:', error);
            let errorMessage = 'Failed to call Yi-Code API.';
            if (error.response) {
                errorMessage = `Yi-Code API Error (${error.response.status}): ${error.response.data?.error || error.message}`;
            }
            else if (error.name === 'AbortError') {
                errorMessage = 'Request cancelled by user';
            }
            else if (error instanceof Error) {
                errorMessage = error.message;
            }
            return {
                content: '',
                error: errorMessage,
                finishReason: 'error'
            };
        }
    }
    /**
       * List available Yi-Code models
       */
    async listModels() {
        // Yi-Code models are fixed, so we return a predefined list
        return [
            {
                id: '01-ai/Yi-34B-Code',
                name: 'Yi-34B-Code',
                description: 'Large Yi-Code model (34B parameters)',
                contextWindow: 32768,
                pricingInfo: 'Free with Hugging Face API'
            },
            {
                id: '01-ai/Yi-34B-Chat',
                name: 'Yi-34B-Chat',
                description: 'General-purpose Yi chat model (34B parameters)',
                contextWindow: 32768,
                pricingInfo: 'Free with Hugging Face API'
            },
            {
                id: '01-ai/Yi-6B-Chat',
                name: 'Yi-6B-Chat',
                description: 'Smaller Yi chat model (6B parameters)',
                contextWindow: 4096,
                pricingInfo: 'Free with Hugging Face API'
            }
        ];
    }
    /**
       * Test connection to Yi-Code
       */
    async testConnection(modelId) {
        if (!this.client) {
            return {
                success: false,
                message: 'Yi-Code client not initialized. Please check your API key.'
            };
        }
        try {
            // Simple test request to check if the API is working
            const endpoint = modelId;
            const response = await this.client.post(endpoint, {
                inputs: 'def hello_world():',
                parameters: {
                    max_new_tokens: 10,
                    return_full_text: false
                }
            });
            if (response.data) {
                return {
                    success: true,
                    message: `Successfully connected to Hugging Face API and tested model '${modelId}'.`
                };
            }
            else {
                return {
                    success: false,
                    message: 'Connected to API but received an unexpected response.'
                };
            }
        }
        catch (error) {
            logger_1.Logger.instance.error('Yi-Code connection test failed:', error);
            let errorMessage = 'Failed to connect to Hugging Face API';
            if (error.response) {
                errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;
            }
            else if (error instanceof Error) {
                errorMessage = error.message;
            }
            return {
                success: false,
                message: errorMessage
            };
        }
    }
    /**
       * Get the configuration fields for this provider
       */
    getConfigurationFields() {
        return [
            {
                id: 'apiKey',
                name: 'Hugging Face API Key',
                description: 'Your Hugging Face API key (from https://huggingface.co/settings/tokens)',
                required: true,
                type: 'string'
            },
            {
                id: 'apiEndpoint',
                name: 'API Endpoint',
                description: 'The Hugging Face Inference API endpoint (default: https://api-inference.huggingface.co/models)',
                required: false,
                type: 'string'
            },
            {
                id: 'defaultModel',
                name: 'Default Model',
                description: 'The default Yi-Code model to use',
                required: false,
                type: 'select',
                options: [
                    '01-ai/Yi-34B-Code',
                    '01-ai/Yi-34B-Chat',
                    '01-ai/Yi-6B-Chat'
                ]
            }
        ];
    }
}
exports.YiCodeProvider = YiCodeProvider;
//# sourceMappingURL=yiCodeProvider.js.map