{"version": 3, "file": "redisDatabase.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/databases/redisDatabase.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AACzC,4CAA4C;AAC5C,iCAAqC;AAGrC;;GAEG;AACH,MAAa,aAAa;IAChB,MAAM,CAA8B;IACpC,GAAG,CAAS;IACZ,SAAS,CAAS;IAClB,WAAW,GAAG,KAAK,CAAC;IAE5B;QACE,IAAI,CAAC,GAAG,GAAG,IAAA,kBAAS,EAAS,2BAA2B,EAAE,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,GAAG,IAAA,kBAAS,EAAS,iCAAiC,EAAE,UAAU,CAAC,CAAC;IACpF,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,sBAAsB;YACtB,IAAI,CAAC,MAAM,GAAG,IAAA,oBAAY,EAAC;gBACzB,GAAG,EAAE,IAAI,CAAC,GAAG;aACd,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAE5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACG,UAAU,CAAC,UAAkB,EAAE,EAAU;QAC/C,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,IAAI,EAAE,EAAE,CAAC;IAChD,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,MAA+B;QACxE,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAY,CAAC;YAC/B,MAAM,SAAS,GAAG,MAAM,CAAC,SAA+B,CAAC;YACzD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAA+C,CAAC;YAExE,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAE5C,eAAe;YACf,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YAEnD,0BAA0B;YAC1B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,MAAM,EAAE,EAAE,CAAC,CAAC;YAEjE,yBAAyB;YACzB,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,MAAM,GAAoB;oBAC9B,KAAK,EAAE,SAAS;oBAChB,KAAK,EAAE,EAAE;iBACV,CAAC;gBACF,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,aAAa,EAAE,MAAM,CAAC,CAAC;YAC9E,CAAC;YAED,0BAA0B;YAC1B,IAAI,QAAQ,EAAE,CAAC;gBACb,sBAAsB;gBACtB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAA4B,CAAC;gBACrD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,WAAW,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;gBAChF,CAAC;gBAED,oBAAoB;gBACpB,MAAM,IAAI,GAAG,QAAQ,CAAC,IAA0B,CAAC;gBACjD,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,SAAS,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC5E,CAAC;gBAED,qBAAqB;gBACrB,MAAM,IAAI,GAAG,QAAQ,CAAC,IAA4B,CAAC;gBACnD,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;wBACvB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAClF,CAAC;gBACH,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACvE,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,EAAU;QACnD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAE5C,aAAa;YACb,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAExC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzF,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU,EAAE,MAA+B;QACvF,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,SAAS,GAAG,MAAM,CAAC,SAA+B,CAAC;YACzD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAA+C,CAAC;YAExE,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAE5C,yBAAyB;YACzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAE7C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;YAED,sBAAsB;YACtB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAA4B,CAAC,CAAC,CAAC,EAAE,CAAC;YAE/F,mCAAmC;YACnC,MAAM,gBAAgB,GAAG,cAAc,CAAC,QAA+C,CAAC;YACxF,IAAI,gBAAgB,EAAE,CAAC;gBACrB,2BAA2B;gBAC3B,MAAM,cAAc,GAAG,gBAAgB,CAAC,MAA4B,CAAC;gBACrE,IAAI,cAAc,EAAE,CAAC;oBACnB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,WAAW,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC;gBACxF,CAAC;gBAED,yBAAyB;gBACzB,MAAM,YAAY,GAAG,gBAAgB,CAAC,IAA0B,CAAC;gBACjE,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,SAAS,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC;gBACpF,CAAC;gBAED,0BAA0B;gBAC1B,MAAM,YAAY,GAAG,gBAAgB,CAAC,IAA4B,CAAC;gBACnE,IAAI,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;oBAChD,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;wBAC/B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAClF,CAAC;gBACH,CAAC;YACH,CAAC;YAED,yBAAyB;YACzB,MAAM,iBAAiB,GAAG,cAAc,CAAC,SAA+B,CAAC;YACzE,IAAI,iBAAiB,IAAI,SAAS,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBACtE,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,aAAa,EAAE,EAAE,CAAC,CAAC;gBACxE,MAAM,MAAM,GAAoB;oBAC9B,KAAK,EAAE,SAAS;oBAChB,KAAK,EAAE,EAAE;iBACV,CAAC;gBACF,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,aAAa,EAAE,MAAM,CAAC,CAAC;YAC9E,CAAC;YAED,uBAAuB;YACvB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YAEnD,0BAA0B;YAC1B,IAAI,QAAQ,EAAE,CAAC;gBACb,sBAAsB;gBACtB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAA4B,CAAC;gBACrD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,WAAW,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;gBAChF,CAAC;gBAED,oBAAoB;gBACpB,MAAM,IAAI,GAAG,QAAQ,CAAC,IAA0B,CAAC;gBACjD,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,SAAS,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC5E,CAAC;gBAED,qBAAqB;gBACrB,MAAM,IAAI,GAAG,QAAQ,CAAC,IAA4B,CAAC;gBACnD,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;wBACvB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAClF,CAAC;gBACH,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACzE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,kBAAkB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1F,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU;QACtD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAE5C,sBAAsB;YACtB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEhD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAEhD,+BAA+B;YAC/B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,MAAM,EAAE,EAAE,CAAC,CAAC;YAEjE,8BAA8B;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,aAAa,EAAE,EAAE,CAAC,CAAC;YAExE,+BAA+B;YAC/B,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAC5B,2BAA2B;gBAC3B,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACnC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,WAAW,cAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;gBACxG,CAAC;gBAED,yBAAyB;gBACzB,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACjC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,SAAS,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;gBACpG,CAAC;gBAED,0BAA0B;gBAC1B,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChF,KAAK,MAAM,GAAG,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;wBAC/C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;YACH,CAAC;YAED,gBAAgB;YAChB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAE3B,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,oBAAoB,UAAU,EAAE,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5F,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,KAA8B,EAAE,KAAc;QAC1F,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,GAAG,GAAa,EAAE,CAAC;YAEvB,iCAAiC;YACjC,MAAM,cAAc,GAAG,KAAK,CAAC,SAAyD,CAAC;YACvF,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,GAAG,GAAG,cAAc,CAAC,IAAI,IAAI,MAAM,CAAC;gBAC1C,MAAM,GAAG,GAAG,cAAc,CAAC,IAAI,IAAI,MAAM,CAAC;gBAE1C,6BAA6B;gBAC7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;gBAElH,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,OAAO,EAAE,CAAC;gBACZ,CAAC;gBAED,GAAG,GAAG,QAAQ,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,cAAc;gBACd,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,MAAM,CAAC,CAAC;gBAEvE,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrB,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC;YAED,0BAA0B;YAC1B,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;gBACxB,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;oBAC3C,SAAS;gBACX,CAAC;gBAED,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;oBAChC,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAErC,IAAI,WAAW,KAAK,QAAQ,EAAE,CAAC;wBAC7B,mBAAmB;wBACnB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,WAAW,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBACpG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjD,CAAC;yBAAM,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC;wBAClC,iBAAiB;wBACjB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,SAAS,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBAChG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC/C,CAAC;yBAAM,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC;wBAClC,iBAAiB;wBACjB,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAoC,CAAC;wBAChE,IAAI,SAAS,EAAE,IAAI,EAAE,CAAC;4BACpB,KAAK,MAAM,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gCACjC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gCAC/F,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;4BAC9C,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrB,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,cAAc;YACd,MAAM,OAAO,GAA8B,EAAE,CAAC;YAE9C,cAAc;YACd,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;gBAChC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC5B,CAAC;YAED,cAAc;YACd,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC;gBACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBAEpD,IAAI,MAAM,EAAE,CAAC;oBACX,qBAAqB;oBACrB,MAAM,SAAS,GAAG,KAAK,CAAC,KAAwC,CAAC;oBACjE,IAAI,SAAS,EAAE,OAAO,EAAE,CAAC;wBACvB,MAAM,aAAa,GAAI,MAAkC,CAAC,OAAiB,CAAC;wBAC5E,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;4BAC3E,SAAS;wBACX,CAAC;oBACH,CAAC;oBAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,oBAAoB;YACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpB,MAAM,UAAU,GAAI,CAAC,CAAC,SAAoB,IAAI,CAAC,CAAC;gBAChD,MAAM,UAAU,GAAI,CAAC,CAAC,SAAoB,IAAI,CAAC,CAAC;gBAChD,OAAO,UAAU,GAAG,UAAU,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,eAAe,CAAC,UAAkB;QAC7C,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,MAAM,CAAC,CAAC;YAE7E,qBAAqB;YACrB,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC;gBACrB,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC1C,CAAC;YAED,4BAA4B;YAC5B,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,MAAM,CAAC,CAAC;YAC5D,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,aAAa,CAAC,CAAC;YAEnE,uCAAuC;YACvC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,IAAI,CAAC,CAAC;YAExE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACnC,mEAAmE;gBACnE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,sBAAsB,UAAU,EAAE,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA3aD,sCA2aC", "sourcesContent": ["import type { IDatabase } from '../../types';\nimport { logger } from '../../../logger';\nimport { getConfig } from '../../../config';\nimport { createClient } from 'redis';\nimport type { RedisClientType, RedisZAddMember } from 'redis';\n\n/**\n * Redis database implementation\n */\nexport class RedisDatabase implements IDatabase {\n  private client: RedisClientType | undefined;\n  private url: string;\n  private keyPrefix: string;\n  private initialized = false;\n\n  constructor() {\n    this.url = getConfig<string>('memory.database.redis.url', '');\n    this.keyPrefix = getConfig<string>('memory.database.redis.keyPrefix', 'codessa:');\n  }\n\n  /**\n     * Initialize the database\n     */\n  public async initialize(): Promise<void> {\n    try {\n      if (!this.url) {\n        throw new Error('Redis URL not configured');\n      }\n            \n      // Create Redis client\n      this.client = createClient({\n        url: this.url\n      });\n            \n      // Connect to Redis\n      await this.client.connect();\n            \n      this.initialized = true;\n      logger.info('Redis database initialized successfully');\n    } catch (error) {\n      logger.error('Failed to initialize Redis database:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Get full key with prefix\n     */\n  private getFullKey(collection: string, id: string): string {\n    return `${this.keyPrefix}${collection}:${id}`;\n  }\n\n  /**\n     * Add a record\n     */\n  public async addRecord(collection: string, record: Record<string, unknown>): Promise<string> {\n    if (!this.client || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      // Extract and type the record fields\n      const id = record.id as string;\n      const timestamp = record.timestamp as number | undefined;\n      const metadata = record.metadata as Record<string, unknown> | undefined;\n\n      const key = this.getFullKey(collection, id);\n\n      // Store record\n      await this.client.set(key, JSON.stringify(record));\n\n      // Add to collection index\n      await this.client.sAdd(`${this.keyPrefix}${collection}:ids`, id);\n\n      // Add to timestamp index\n      if (timestamp) {\n        const member: RedisZAddMember = {\n          score: timestamp,\n          value: id\n        };\n        await this.client.zAdd(`${this.keyPrefix}${collection}:timestamps`, member);\n      }\n\n      // Add to metadata indexes\n      if (metadata) {\n        // Add to source index\n        const source = metadata.source as string | undefined;\n        if (source) {\n          await this.client.sAdd(`${this.keyPrefix}${collection}:source:${source}`, id);\n        }\n                \n        // Add to type index\n        const type = metadata.type as string | undefined;\n        if (type) {\n          await this.client.sAdd(`${this.keyPrefix}${collection}:type:${type}`, id);\n        }\n\n        // Add to tag indexes\n        const tags = metadata.tags as string[] | undefined;\n        if (tags && Array.isArray(tags)) {\n          for (const tag of tags) {\n            await this.client.sAdd(`${this.keyPrefix}${collection}:tag:${String(tag)}`, id);\n          }\n        }\n      }\n            \n      logger.debug(`Added record with ID ${id} to collection ${collection}`);\n      return id;\n    } catch (error) {\n      logger.error(`Failed to add record to collection ${collection}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n     * Get a record by ID\n     */\n  public async getRecord(collection: string, id: string): Promise<Record<string, unknown> | undefined> {\n    if (!this.client || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      const key = this.getFullKey(collection, id);\n            \n      // Get record\n      const data = await this.client.get(key);\n            \n      if (!data) {\n        return undefined;\n      }\n            \n      return JSON.parse(data);\n    } catch (error) {\n      logger.error(`Failed to get record with ID ${id} from collection ${collection}:`, error);\n      return undefined;\n    }\n  }\n\n  /**\n     * Update a record\n     */\n  public async updateRecord(collection: string, id: string, record: Record<string, unknown>): Promise<boolean> {\n    if (!this.client || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      // Extract and type the record fields\n      const timestamp = record.timestamp as number | undefined;\n      const metadata = record.metadata as Record<string, unknown> | undefined;\n\n      const key = this.getFullKey(collection, id);\n\n      // Check if record exists\n      const exists = await this.client.exists(key);\n\n      if (!exists) {\n        return false;\n      }\n\n      // Get existing record\n      const existingData = await this.client.get(key);\n      const existingRecord = existingData ? JSON.parse(existingData) as Record<string, unknown> : {};\n            \n      // Delete existing metadata indexes\n      const existingMetadata = existingRecord.metadata as Record<string, unknown> | undefined;\n      if (existingMetadata) {\n        // Delete from source index\n        const existingSource = existingMetadata.source as string | undefined;\n        if (existingSource) {\n          await this.client.sRem(`${this.keyPrefix}${collection}:source:${existingSource}`, id);\n        }\n\n        // Delete from type index\n        const existingType = existingMetadata.type as string | undefined;\n        if (existingType) {\n          await this.client.sRem(`${this.keyPrefix}${collection}:type:${existingType}`, id);\n        }\n\n        // Delete from tag indexes\n        const existingTags = existingMetadata.tags as string[] | undefined;\n        if (existingTags && Array.isArray(existingTags)) {\n          for (const tag of existingTags) {\n            await this.client.sRem(`${this.keyPrefix}${collection}:tag:${String(tag)}`, id);\n          }\n        }\n      }\n            \n      // Update timestamp index\n      const existingTimestamp = existingRecord.timestamp as number | undefined;\n      if (existingTimestamp && timestamp && existingTimestamp !== timestamp) {\n        await this.client.zRem(`${this.keyPrefix}${collection}:timestamps`, id);\n        const member: RedisZAddMember = {\n          score: timestamp,\n          value: id\n        };\n        await this.client.zAdd(`${this.keyPrefix}${collection}:timestamps`, member);\n      }\n            \n      // Store updated record\n      await this.client.set(key, JSON.stringify(record));\n            \n      // Add to metadata indexes\n      if (metadata) {\n        // Add to source index\n        const source = metadata.source as string | undefined;\n        if (source) {\n          await this.client.sAdd(`${this.keyPrefix}${collection}:source:${source}`, id);\n        }\n\n        // Add to type index\n        const type = metadata.type as string | undefined;\n        if (type) {\n          await this.client.sAdd(`${this.keyPrefix}${collection}:type:${type}`, id);\n        }\n\n        // Add to tag indexes\n        const tags = metadata.tags as string[] | undefined;\n        if (tags && Array.isArray(tags)) {\n          for (const tag of tags) {\n            await this.client.sAdd(`${this.keyPrefix}${collection}:tag:${String(tag)}`, id);\n          }\n        }\n      }\n            \n      logger.debug(`Updated record with ID ${id} in collection ${collection}`);\n      return true;\n    } catch (error) {\n      logger.error(`Failed to update record with ID ${id} in collection ${collection}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Delete a record\n     */\n  public async deleteRecord(collection: string, id: string): Promise<boolean> {\n    if (!this.client || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      const key = this.getFullKey(collection, id);\n            \n      // Get existing record\n      const existingData = await this.client.get(key);\n            \n      if (!existingData) {\n        return false;\n      }\n            \n      const existingRecord = JSON.parse(existingData);\n            \n      // Delete from collection index\n      await this.client.sRem(`${this.keyPrefix}${collection}:ids`, id);\n            \n      // Delete from timestamp index\n      await this.client.zRem(`${this.keyPrefix}${collection}:timestamps`, id);\n            \n      // Delete from metadata indexes\n      if (existingRecord.metadata) {\n        // Delete from source index\n        if (existingRecord.metadata.source) {\n          await this.client.sRem(`${this.keyPrefix}${collection}:source:${existingRecord.metadata.source}`, id);\n        }\n                \n        // Delete from type index\n        if (existingRecord.metadata.type) {\n          await this.client.sRem(`${this.keyPrefix}${collection}:type:${existingRecord.metadata.type}`, id);\n        }\n                \n        // Delete from tag indexes\n        if (existingRecord.metadata.tags && Array.isArray(existingRecord.metadata.tags)) {\n          for (const tag of existingRecord.metadata.tags) {\n            await this.client.sRem(`${this.keyPrefix}${collection}:tag:${tag}`, id);\n          }\n        }\n      }\n            \n      // Delete record\n      await this.client.del(key);\n            \n      logger.debug(`Deleted record with ID ${id} from collection ${collection}`);\n      return true;\n    } catch (error) {\n      logger.error(`Failed to delete record with ID ${id} from collection ${collection}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Query records\n     */\n  public async queryRecords(collection: string, query: Record<string, unknown>, limit?: number): Promise<Record<string, unknown>[]> {\n    if (!this.client || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      // Get all IDs in the collection\n      let ids: string[] = [];\n            \n      // Handle timestamp range queries\n      const timestampQuery = query.timestamp as { $gte?: number; $lte?: number } | undefined;\n      if (timestampQuery) {\n        const min = timestampQuery.$gte || '-inf';\n        const max = timestampQuery.$lte || '+inf';\n                \n        // Get IDs in timestamp range\n        const rangeIds = await this.client.zRange(`${this.keyPrefix}${collection}:timestamps`, min, max, { BY: 'SCORE' });\n                \n        if (rangeIds.length === 0) {\n          return [];\n        }\n                \n        ids = rangeIds;\n      } else {\n        // Get all IDs\n        ids = await this.client.sMembers(`${this.keyPrefix}${collection}:ids`);\n                \n        if (ids.length === 0) {\n          return [];\n        }\n      }\n            \n      // Handle metadata filters\n      for (const key in query) {\n        if (key === 'timestamp' || key === '$text') {\n          continue;\n        }\n                \n        if (key.startsWith('metadata.')) {\n          const metadataKey = key.substring(9);\n                    \n          if (metadataKey === 'source') {\n            // Filter by source\n            const sourceIds = await this.client.sMembers(`${this.keyPrefix}${collection}:source:${query[key]}`);\n            ids = ids.filter(id => sourceIds.includes(id));\n          } else if (metadataKey === 'type') {\n            // Filter by type\n            const typeIds = await this.client.sMembers(`${this.keyPrefix}${collection}:type:${query[key]}`);\n            ids = ids.filter(id => typeIds.includes(id));\n          } else if (metadataKey === 'tags') {\n            // Filter by tags\n            const tagsQuery = query[key] as { $all?: string[] } | undefined;\n            if (tagsQuery?.$all) {\n              for (const tag of tagsQuery.$all) {\n                const tagIds = await this.client.sMembers(`${this.keyPrefix}${collection}:tag:${String(tag)}`);\n                ids = ids.filter(id => tagIds.includes(id));\n              }\n            }\n          }\n        }\n      }\n            \n      if (ids.length === 0) {\n        return [];\n      }\n            \n      // Get records\n      const records: Record<string, unknown>[] = [];\n            \n      // Apply limit\n      if (limit && ids.length > limit) {\n        ids = ids.slice(0, limit);\n      }\n            \n      // Get records\n      for (const id of ids) {\n        const record = await this.getRecord(collection, id);\n                \n        if (record) {\n          // Handle text search\n          const textQuery = query.$text as { $search: string } | undefined;\n          if (textQuery?.$search) {\n            const recordContent = (record as Record<string, unknown>).content as string;\n            if (!recordContent.toLowerCase().includes(textQuery.$search.toLowerCase())) {\n              continue;\n            }\n          }\n                    \n          records.push(record);\n        }\n      }\n            \n      // Sort by timestamp\n      records.sort((a, b) => {\n        const aTimestamp = (a.timestamp as number) || 0;\n        const bTimestamp = (b.timestamp as number) || 0;\n        return bTimestamp - aTimestamp;\n      });\n            \n      return records;\n    } catch (error) {\n      logger.error(`Failed to query records from collection ${collection}:`, error);\n      return [];\n    }\n  }\n\n  /**\n     * Clear all records in a collection\n     */\n  public async clearCollection(collection: string): Promise<void> {\n    if (!this.client || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      // Get all IDs in the collection\n      const ids = await this.client.sMembers(`${this.keyPrefix}${collection}:ids`);\n            \n      // Delete all records\n      for (const id of ids) {\n        await this.deleteRecord(collection, id);\n      }\n            \n      // Delete collection indexes\n      await this.client.del(`${this.keyPrefix}${collection}:ids`);\n      await this.client.del(`${this.keyPrefix}${collection}:timestamps`);\n            \n      // Find and delete all metadata indexes\n      const keys = await this.client.keys(`${this.keyPrefix}${collection}:*`);\n            \n      if (keys.length > 0 && this.client) {\n        // Delete each key one by one since del() only accepts a single key\n        const client = this.client;\n        await Promise.all(keys.map(key => client.del(key)));\n      }\n            \n      logger.info(`Cleared collection ${collection}`);\n    } catch (error) {\n      logger.error(`Failed to clear collection ${collection}:`, error);\n      throw error;\n    }\n  }\n}\n"]}