// Mock VS Code API for testing outside of VS Code
const vscode = {
  window: {
    showInformationMessage: () => Promise.resolve(undefined),
    showErrorMessage: () => Promise.resolve(undefined),
    showWarningMessage: () => Promise.resolve(undefined),
    showQuickPick: () => Promise.resolve(undefined),
    showInputBox: () => Promise.resolve(''),
    createOutputChannel: () => ({
      appendLine: console.log,
      show: () => {}
    })
  },
  commands: {
    executeCommand: () => Promise.resolve()
  },
  Uri: {
    parse: (path: string) => ({ path }),
    file: (path: string) => ({ path })
  },
  workspace: {
    workspaceFolders: [],
    getConfiguration: () => ({
      get: () => ({}),
      update: () => Promise.resolve()
    }),
    openTextDocument: () => Promise.resolve({
      getText: () => '',
      save: () => Promise.resolve(true)
    })
  },
  env: {
    appName: 'Codessa Test',
    appRoot: '',
    machineId: 'test-machine-id',
    sessionId: 'test-session-id'
  },
  extensions: {
    getExtension: () => ({
      packageJSON: {
        version: '0.0.1',
        displayName: 'Codessa Test',
        publisher: 'codessa',
        name: 'codessa',
        engines: {
          vscode: '^1.60.0'
        }
      },
      extensionPath: '',
      isActive: true,
      activate: () => Promise.resolve()
    })
  },
  ConfigurationTarget: {
    Global: 1,
    Workspace: 2,
    WorkspaceFolder: 3
  }
};

export = vscode;
