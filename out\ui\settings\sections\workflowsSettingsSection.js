"use strict";
/**
 * Revolutionary Workflows Settings Section
 * Enhanced with revolutionary AI capabilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.renderWorkflowsSettingsSection = renderWorkflowsSettingsSection;
const config_1 = require("../../../config");
function renderWorkflowsSettingsSection(container, settings) {
    console.log('Rendering workflows settings with settings:', settings);
    container.innerHTML = `
        <div class="revolutionary-workflow-settings">
            <div class="workflow-header">
                <h2>🚀 Revolutionary Workflow Settings</h2>
                <p class="workflow-description">
                    Configure workflow execution with revolutionary AI enhancements including
                    Goddess Mode guidance, Quantum optimization, Neural insights, and Time-travel predictions.
                </p>
            </div>

            <!-- Core Workflow Settings -->
            <div class="settings-section workflow-core-settings">
                <h3>⚙️ Core Settings</h3>
                <div class="settings-grid">
                    <div class="setting-group">
                        <label for="workflow-engine">Default Workflow Engine</label>
                        <select id="workflow-engine" class="settings-select">
                            <option value="codessa">Codessa Engine (Revolutionary)</option>
                            <option value="langgraph">LangGraph Engine</option>
                        </select>
                        <small>Choose the engine for workflow execution</small>
                    </div>

                    <div class="setting-group">
                        <label for="workflow-timeout">Workflow Timeout (seconds)</label>
                        <div class="slider-container">
                            <input type="range" id="workflow-timeout" min="30" max="3600" value="300" class="slider">
                            <span class="slider-value">300s</span>
                        </div>
                        <small>Maximum time for workflow execution</small>
                    </div>
                </div>

                <div class="checkbox-grid">
                    <label class="checkbox-item">
                        <input type="checkbox" id="auto-save-workflows" checked>
                        <span>Auto-save Workflows</span>
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" id="workflow-debugging" checked>
                        <span>Enable Workflow Debugging</span>
                    </label>
                </div>
            </div>

            <!-- Revolutionary Workflow Enhancements -->
            <div class="settings-section revolutionary-enhancements">
                <div class="section-header">
                    <h3>✨ Revolutionary Enhancements</h3>
                    <div class="feature-toggle">
                        <label class="toggle-switch">
                            <input type="checkbox" id="revolutionary-features-enabled" checked>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">Enable Revolutionary Features</span>
                    </div>
                </div>

                <div class="enhancement-grid">
                    <div class="enhancement-card goddess-enhancement">
                        <div class="enhancement-header">
                            <span class="enhancement-icon">✨</span>
                            <h4>Goddess Guidance</h4>
                            <label class="mini-toggle">
                                <input type="checkbox" id="goddess-guidance" checked>
                                <span class="mini-slider"></span>
                            </label>
                        </div>
                        <p>Divine wisdom and emotional intelligence for workflow execution</p>
                        <div class="enhancement-settings">
                            <label>Guidance Level:</label>
                            <select id="goddess-guidance-level" class="mini-select">
                                <option value="subtle">Subtle</option>
                                <option value="moderate" selected>Moderate</option>
                                <option value="comprehensive">Comprehensive</option>
                            </select>
                        </div>
                    </div>

                    <div class="enhancement-card quantum-enhancement">
                        <div class="enhancement-header">
                            <span class="enhancement-icon">🔬</span>
                            <h4>Quantum Optimization</h4>
                            <label class="mini-toggle">
                                <input type="checkbox" id="quantum-optimization" checked>
                                <span class="mini-slider"></span>
                            </label>
                        </div>
                        <p>Quantum-inspired workflow optimization and parallel execution</p>
                        <div class="enhancement-settings">
                            <label>Optimization Level:</label>
                            <select id="quantum-optimization-level" class="mini-select">
                                <option value="basic">Basic</option>
                                <option value="advanced" selected>Advanced</option>
                                <option value="expert">Expert</option>
                            </select>
                        </div>
                    </div>

                    <div class="enhancement-card neural-enhancement">
                        <div class="enhancement-header">
                            <span class="enhancement-icon">🧠</span>
                            <h4>Neural Insights</h4>
                            <label class="mini-toggle">
                                <input type="checkbox" id="neural-insights" checked>
                                <span class="mini-slider"></span>
                            </label>
                        </div>
                        <p>Brain-inspired workflow analysis and adaptive execution</p>
                        <div class="enhancement-settings">
                            <label>Insight Depth:</label>
                            <select id="neural-insight-depth" class="mini-select">
                                <option value="surface">Surface</option>
                                <option value="deep" selected>Deep</option>
                                <option value="consciousness">Consciousness</option>
                            </select>
                        </div>
                    </div>

                    <div class="enhancement-card timetravel-enhancement">
                        <div class="enhancement-header">
                            <span class="enhancement-icon">⏰</span>
                            <h4>Time-Travel Predictions</h4>
                            <label class="mini-toggle">
                                <input type="checkbox" id="timetravel-predictions" checked>
                                <span class="mini-slider"></span>
                            </label>
                        </div>
                        <p>Predict and prevent future workflow issues</p>
                        <div class="enhancement-settings">
                            <label>Prediction Scope:</label>
                            <select id="prediction-scope" class="mini-select">
                                <option value="immediate">Immediate</option>
                                <option value="short-term" selected>Short-term</option>
                                <option value="long-term">Long-term</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance & Analytics -->
            <div class="settings-section workflow-analytics">
                <h3>📊 Performance & Analytics</h3>

                <div class="checkbox-grid">
                    <label class="checkbox-item">
                        <input type="checkbox" id="performance-metrics" checked>
                        <span>Performance Metrics</span>
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" id="satisfaction-tracking" checked>
                        <span>User Satisfaction Tracking</span>
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" id="goddess-rating" checked>
                        <span>Goddess Rating System</span>
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" id="adaptive-execution" checked>
                        <span>Adaptive Execution</span>
                    </label>
                </div>
            </div>

            <!-- Auto-Enhancement Settings -->
            <div class="settings-section auto-enhancement">
                <h3>🤖 Auto-Enhancement</h3>

                <div class="settings-grid">
                    <div class="setting-group">
                        <label for="enhancement-aggressiveness">Enhancement Aggressiveness</label>
                        <div class="slider-container">
                            <input type="range" id="enhancement-aggressiveness" min="1" max="10" value="7" class="slider">
                            <span class="slider-value">7</span>
                        </div>
                        <small>How aggressively to apply revolutionary enhancements</small>
                    </div>
                </div>

                <div class="checkbox-grid">
                    <label class="checkbox-item">
                        <input type="checkbox" id="auto-enhancement" checked>
                        <span>Auto-Enhancement</span>
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" id="learning-execution" checked>
                        <span>Learning from Execution</span>
                    </label>
                </div>
            </div>

            <!-- Save Actions -->
            <div class="settings-actions">
                <button id="save-workflow-settings" class="save-button">
                    🚀 Save Workflow Settings
                </button>
                <button id="reset-workflow-settings" class="reset-button">
                    🔄 Reset to Defaults
                </button>
            </div>
        </div>
    `;
    // Add event listeners
    setupWorkflowSettingsEventListeners();
}
function setupWorkflowSettingsEventListeners() {
    // Slider value updates
    document.querySelectorAll('.slider').forEach(slider => {
        slider.addEventListener('input', (e) => {
            const target = e.target;
            const valueSpan = target.parentElement?.querySelector('.slider-value');
            if (valueSpan) {
                const value = target.value;
                if (target.id === 'workflow-timeout') {
                    valueSpan.textContent = `${value}s`;
                }
                else {
                    valueSpan.textContent = value;
                }
            }
        });
    });
    // Save and reset buttons
    document.getElementById('save-workflow-settings')?.addEventListener('click', saveWorkflowSettings);
    document.getElementById('reset-workflow-settings')?.addEventListener('click', resetWorkflowSettings);
    // Revolutionary features toggle
    document.getElementById('revolutionary-features-enabled')?.addEventListener('change', (e) => {
        const enabled = e.target.checked;
        const enhancementCards = document.querySelectorAll('.enhancement-card');
        enhancementCards.forEach(card => {
            const toggle = card.querySelector('input[type="checkbox"]');
            if (toggle) {
                toggle.disabled = !enabled;
                if (!enabled) {
                    toggle.checked = false;
                }
            }
        });
    });
}
async function saveWorkflowSettings() {
    try {
        // Get current config to merge with new settings
        const currentConfig = (0, config_1.getConfig)('workflows', {});
        // Collect all workflow settings
        const workflowSettings = {
            engine: document.getElementById('workflow-engine')?.value || 'codessa',
            timeout: parseInt(document.getElementById('workflow-timeout')?.value || '300'),
            autoSave: document.getElementById('auto-save-workflows')?.checked || false,
            debugging: document.getElementById('workflow-debugging')?.checked || false,
            revolutionaryFeatures: {
                enabled: document.getElementById('revolutionary-features-enabled')?.checked || false,
                goddessGuidance: document.getElementById('goddess-guidance')?.checked || false,
                goddessGuidanceLevel: document.getElementById('goddess-guidance-level')?.value || 'moderate',
                quantumOptimization: document.getElementById('quantum-optimization')?.checked || false,
                quantumOptimizationLevel: document.getElementById('quantum-optimization-level')?.value || 'advanced',
                neuralInsights: document.getElementById('neural-insights')?.checked || false,
                neuralInsightDepth: document.getElementById('neural-insight-depth')?.value || 'deep',
                timeTravelPredictions: document.getElementById('timetravel-predictions')?.checked || false,
                predictionScope: document.getElementById('prediction-scope')?.value || 'short-term'
            },
            analytics: {
                performanceMetrics: document.getElementById('performance-metrics')?.checked || false,
                satisfactionTracking: document.getElementById('satisfaction-tracking')?.checked || false,
                goddessRating: document.getElementById('goddess-rating')?.checked || false,
                adaptiveExecution: document.getElementById('adaptive-execution')?.checked || false
            },
            autoEnhancement: {
                enabled: document.getElementById('auto-enhancement')?.checked || false,
                aggressiveness: parseInt(document.getElementById('enhancement-aggressiveness')?.value || '7'),
                learningExecution: document.getElementById('learning-execution')?.checked || false
            }
        };
        // Merge with current config and save to VS Code settings
        const mergedSettings = { ...currentConfig, ...workflowSettings };
        await (0, config_1.setConfig)('workflows', mergedSettings);
        // Show success message
        const saveButton = document.getElementById('save-workflow-settings');
        if (saveButton) {
            const originalText = saveButton.textContent;
            saveButton.textContent = '✅ Saved!';
            saveButton.classList.add('success');
            setTimeout(() => {
                saveButton.textContent = originalText;
                saveButton.classList.remove('success');
            }, 2000);
        }
    }
    catch (error) {
        console.error('Error saving workflow settings:', error);
        // Show error message
        const saveButton = document.getElementById('save-workflow-settings');
        if (saveButton) {
            const originalText = saveButton.textContent;
            saveButton.textContent = '❌ Error!';
            saveButton.classList.add('error');
            setTimeout(() => {
                saveButton.textContent = originalText;
                saveButton.classList.remove('error');
            }, 2000);
        }
    }
}
async function resetWorkflowSettings() {
    // Reset all form elements to default values
    const confirmReset = confirm('Are you sure you want to reset all workflow settings to defaults?');
    if (confirmReset) {
        // Reset form elements
        document.getElementById('workflow-engine').value = 'codessa';
        document.getElementById('workflow-timeout').value = '300';
        document.getElementById('auto-save-workflows').checked = true;
        document.getElementById('workflow-debugging').checked = true;
        // Reset revolutionary features
        document.getElementById('revolutionary-features-enabled').checked = true;
        document.getElementById('goddess-guidance').checked = true;
        document.getElementById('goddess-guidance-level').value = 'moderate';
        document.getElementById('quantum-optimization').checked = true;
        document.getElementById('quantum-optimization-level').value = 'advanced';
        document.getElementById('neural-insights').checked = true;
        document.getElementById('neural-insight-depth').value = 'deep';
        document.getElementById('timetravel-predictions').checked = true;
        document.getElementById('prediction-scope').value = 'short-term';
        // Reset analytics
        document.getElementById('performance-metrics').checked = true;
        document.getElementById('satisfaction-tracking').checked = true;
        document.getElementById('goddess-rating').checked = true;
        document.getElementById('adaptive-execution').checked = true;
        // Reset auto-enhancement
        document.getElementById('auto-enhancement').checked = true;
        document.getElementById('enhancement-aggressiveness').value = '7';
        document.getElementById('learning-execution').checked = true;
        // Update slider displays
        document.querySelectorAll('.slider').forEach(slider => {
            const event = new Event('input');
            slider.dispatchEvent(event);
        });
    }
}
//# sourceMappingURL=workflowsSettingsSection.js.map