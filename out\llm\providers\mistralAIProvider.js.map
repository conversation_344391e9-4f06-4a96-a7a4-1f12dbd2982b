{"version": 3, "file": "mistralAIProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/mistralAIProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uDAAoD;AAGpD,yCAAsC;AACtC,+CAAiC;AAGjC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B,oCAAoC;AACpC,IAAI,aAAkB,CAAC;AACvB,IAAI,CAAC;IACH,aAAa,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC,aAAa,CAAC;IAC9D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;AAC5D,CAAC;AAAC,OAAO,MAAM,EAAE,CAAC;IAChB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;IAC3E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;AAC/G,CAAC;AAED,MAAa,iBAAkB,SAAQ,iCAAe;IAC3C,UAAU,GAAG,WAAW,CAAC;IACzB,WAAW,GAAG,YAAY,CAAC;IAC3B,WAAW,GAAG,kEAAkE,CAAC;IACjF,OAAO,GAAG,qBAAqB,CAAC;IAChC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,2BAA2B,CAAC;IAC9C,YAAY,GAAG,eAAe,CAAC;IAEhC,MAAM,GAAQ,IAAI,CAAC;IACnB,aAAa,GAAQ,IAAI,CAAC;IAC1B,OAAO,GAAG,EAAE,CAAC;IAErB,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBAClF,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;oBAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAElC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACpD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,iDAAiD;YACjD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,MAAM,EAAE;oBACnC,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,KAAK,CAAC,qBAAqB;aACrC,CAAC,CAAC;YAEH,4CAA4C;YAC5C,IAAI,aAAa,EAAE,CAAC;gBAClB,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC3E,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACxE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,2FAA2F;aACnG,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,mBAAmB;YACnB,MAAM,QAAQ,GAA6C,EAAE,CAAC;YAE9D,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACxB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,MAAM,CAAC,YAAY;qBACpB,CAAC,CAAC;gBACd,CAAC;gBACD,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;iBACd,CAAC,CAAC;YACd,CAAC;YAED,2BAA2B;YAC3B,IAAI,SAAS,GAA8E,SAAS,CAAC;YACrG,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC5B,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAClD,IAAI,EAAE,IAAI,CAAC,EAAE;oBACb,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,UAAU,EAAG,IAAY,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE;iBAC1F,CAAC,CAAC,CAAC;YACN,CAAC;YAED,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;wBAC7D,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBAChH,CAAC;YACH,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,IAAI,QAAQ,CAAC;YAEb,uDAAuD;YACvD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,cAAc;gBACd,MAAM,UAAU,GAAQ;oBACtB,KAAK,EAAE,OAAO;oBACd,QAAQ,EAAE,QAAQ;oBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,MAAM,EAAE,KAAK;iBACd,CAAC;gBAEF,yBAAyB;gBACzB,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtC,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC;oBAC7B,UAAU,CAAC,UAAU,GAAG,MAAM,CAAC;gBACjC,CAAC;gBAED,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAErD,+BAA+B;gBAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;gBAE5D,oBAAoB;gBACpB,IAAI,eAAe,GAAkC,SAAS,CAAC;gBAC/D,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,UAAU,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClG,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBAC3D,eAAe,GAAG;wBAChB,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;wBAC9B,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;qBAC9C,CAAC;gBACJ,CAAC;gBAED,OAAO;oBACL,OAAO;oBACP,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,MAAM;oBAC1D,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,QAAQ,EAAE,eAAe;iBAC1B,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,mBAAmB;gBACnB,MAAM,WAAW,GAAQ;oBACvB,KAAK,EAAE,OAAO;oBACd,QAAQ,EAAE,QAAQ;oBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,UAAU,EAAE,MAAM,CAAC,SAAS;iBAC7B,CAAC;gBAEF,yBAAyB;gBACzB,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtC,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;oBAC9B,WAAW,CAAC,WAAW,GAAG,MAAM,CAAC;gBACnC,CAAC;gBAED,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE;oBAClE,MAAM,EAAE,eAAe,EAAE,MAAM;iBAChC,CAAC,CAAC;gBAEH,+BAA+B;gBAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;gBAEjE,oBAAoB;gBACpB,IAAI,eAAe,GAAkC,SAAS,CAAC;gBAC/D,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,UAAU,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5G,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBAChE,eAAe,GAAG;wBAChB,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;wBAC9B,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;qBAC9C,CAAC;gBACJ,CAAC;gBAED,OAAO;oBACL,OAAO;oBACP,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,MAAM;oBAC/D,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;oBAC1B,QAAQ,EAAE,eAAe;iBAC1B,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,YAAY,GAAG,gCAAgC,CAAC;YAEpD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,yBAAyB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAC5H,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YAC/E,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAElD,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACxC,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;gBACrC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,SAAS,CAAC,MAAM,4BAA4B,CAAC,CAAC;gBAE7F,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;oBACpC,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;oBACnC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC/C,aAAa,EAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtD,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;iBACnD,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mEAAmE,EAAE,KAAK,CAAC,CAAC;QACnG,CAAC;QAED,6CAA6C;QAC7C,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACpC,CAAC;IAED;;SAEK;IACG,mBAAmB;QACzB,OAAO;YACL,qBAAqB;YACrB;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,6CAA6C;gBAC1D,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,mBAAmB;aACjC;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,mCAAmC;gBAChD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,mBAAmB;aACjC;YACD;gBACE,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,mDAAmD;gBAChE,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,mBAAmB;aACjC;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,uDAAuD;gBACpE,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,mBAAmB;aACjC;YACD,cAAc;YACd;gBACE,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,8BAA8B;gBAC3C,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,mDAAmD;gBAChE,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,qBAAqB;gBACzB,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,oCAAoC;gBACjD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,sCAAsC;gBACnD,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,cAAc,CAAC,OAAe;QACpC,2CAA2C;QAC3C,MAAM,QAAQ,GAA2B;YACvC,cAAc,EAAE,cAAc;YAC9B,eAAe,EAAE,eAAe;YAChC,gBAAgB,EAAE,gBAAgB;YAClC,eAAe,EAAE,eAAe;YAChC,iBAAiB,EAAE,iBAAiB;YACpC,mBAAmB,EAAE,mBAAmB;YACxC,qBAAqB,EAAE,qBAAqB;YAC5C,uBAAuB,EAAE,uBAAuB;SACjD,CAAC;QAEF,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC;IACtC,CAAC;IAED;;SAEK;IACG,mBAAmB,CAAC,OAAe;QACzC,MAAM,cAAc,GAA2B;YAC7C,cAAc,EAAE,6CAA6C;YAC7D,eAAe,EAAE,mCAAmC;YACpD,gBAAgB,EAAE,mDAAmD;YACrE,eAAe,EAAE,uDAAuD;YACxE,iBAAiB,EAAE,8BAA8B;YACjD,mBAAmB,EAAE,mDAAmD;YACxE,qBAAqB,EAAE,oCAAoC;YAC3D,uBAAuB,EAAE,sCAAsC;SAChE,CAAC;QAEF,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,kBAAkB,CAAC;IACvD,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,OAAe;QAC9C,yDAAyD;QACzD,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3F,OAAO,KAAK,CAAC,CAAC,qBAAqB;QACrC,CAAC;QACD,OAAO,IAAI,CAAC,CAAC,qCAAqC;IACpD,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,OAAe;QAC5C,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACtF,OAAO,qBAAqB,CAAC;QAC/B,CAAC;QACD,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uDAAuD;aACjE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBAC9C,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,8DAA8D,OAAO,IAAI;iBACnF,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uDAAuD;aACjE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YACnE,IAAI,YAAY,GAAG,qCAAqC,CAAC;YAEzD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACjH,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,qEAAqE;IAErE;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,yBAAyB;gBACtC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,kEAAkE;gBAC/E,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,0BAA0B;gBACvC,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,cAAc;oBACd,eAAe;oBACf,gBAAgB;oBAChB,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,qBAAqB;oBACrB,uBAAuB;iBACxB;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAzdD,8CAydC", "sourcesContent": ["import { BaseLL<PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { Logger } from '../../logger';\nimport * as vscode from 'vscode';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n// Import MistralClient if available\nlet MistralClient: any;\ntry {\n  MistralClient = require('@mistralai/mistralai').MistralClient;\n  Logger.instance.info('MistralAI SDK loaded successfully');\n} catch (_error) {\n  Logger.instance.warn('MistralAI SDK not available, falling back to axios');\n  Logger.instance.debug('To use MistralAI SDK, install the dependency with: npm install @mistralai/mistralai');\n}\n\nexport class MistralAIProvider extends BaseLLMProvider {\n  readonly providerId = 'mistralai';\n  readonly displayName = 'Mistral AI';\n  readonly description = 'Mistral AI language models including Mistral 7B and Mixtral 8x7B';\n  readonly website = 'https://mistral.ai/';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api.mistral.ai/v1';\n  readonly defaultModel = 'mistral-large';\n\n  private client: any = null;\n  private mistralClient: any = null;\n  private baseUrl = '';\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        Logger.instance.info('Mistral AI configuration changed, re-initializing client.');\n        this.loadConfig().then(() => {\n          this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n          this.initializeClient();\n        });\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n\n    if (!apiKey) {\n      Logger.instance.warn('Mistral AI API key not set.');\n      this.client = null;\n      this.mistralClient = null;\n      return;\n    }\n\n    try {\n      // Initialize the Axios client for REST API calls\n      this.client = axios.create({\n        baseURL: this.baseUrl,\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        timeout: 60000 // 60 seconds timeout\n      });\n\n      // Initialize the MistralClient if available\n      if (MistralClient) {\n        this.mistralClient = new MistralClient(apiKey, { endpoint: this.baseUrl });\n        Logger.instance.info('Mistral AI SDK client initialized.');\n      } else {\n        Logger.instance.info('Mistral AI REST client initialized.');\n      }\n    } catch (error) {\n      Logger.instance.error('Failed to initialize Mistral AI client:', error);\n      this.client = null;\n      this.mistralClient = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!(this.client || this.mistralClient);\n  }\n\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.isConfigured()) {\n      return {\n        content: '',\n        error: 'Mistral AI provider not configured (API key missing). Please set the API key in settings.'\n      };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare messages\n      const messages: Array<{ role: string; content: string }> = [];\n\n      if (params.history && params.history.length > 0) {\n        messages.push(...params.history);\n      } else {\n        if (params.systemPrompt) {\n          messages.push({\n            role: 'system',\n            content: params.systemPrompt\n          } as const);\n        }\n        messages.push({\n          role: 'user',\n          content: params.prompt\n        } as const);\n      }\n\n      // Format tools if provided\n      let functions: Array<{ name: string; description: string; parameters: any }> | undefined = undefined;\n      if (tools && tools.size > 0) {\n        functions = Array.from(tools.values()).map(tool => ({\n          name: tool.id,\n          description: tool.description,\n          parameters: (tool as any).singleActionSchema?.shape || { type: 'object', properties: {} }\n        }));\n      }\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            Logger.instance.info('Mistral AI request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          Logger.instance.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      let response;\n\n      // Use the SDK if available, otherwise use the REST API\n      if (this.mistralClient) {\n        // Use the SDK\n        const chatParams: any = {\n          model: modelId,\n          messages: messages,\n          temperature: params.temperature ?? 0.7,\n          maxTokens: params.maxTokens,\n          stream: false\n        };\n\n        // Add tools if available\n        if (functions && functions.length > 0) {\n          chatParams.tools = functions;\n          chatParams.toolChoice = 'auto';\n        }\n\n        response = await this.mistralClient.chat(chatParams);\n\n        // Extract the response content\n        const content = response.choices[0]?.message?.content || '';\n\n        // Handle tool calls\n        let toolCallRequest: LLMGenerateResult['toolCall'] = undefined;\n        if (response.choices[0]?.message?.tool_calls && response.choices[0].message.tool_calls.length > 0) {\n          const toolCall = response.choices[0].message.tool_calls[0];\n          toolCallRequest = {\n            toolId: toolCall.function.name,\n            args: JSON.parse(toolCall.function.arguments)\n          };\n        }\n\n        return {\n          content,\n          finishReason: response.choices[0]?.finish_reason || 'stop',\n          usage: response.usage,\n          toolCall: toolCallRequest\n        };\n      } else {\n        // Use the REST API\n        const requestData: any = {\n          model: modelId,\n          messages: messages,\n          temperature: params.temperature ?? 0.7,\n          max_tokens: params.maxTokens\n        };\n\n        // Add tools if available\n        if (functions && functions.length > 0) {\n          requestData.tools = functions;\n          requestData.tool_choice = 'auto';\n        }\n\n        response = await this.client.post('/chat/completions', requestData, {\n          signal: abortController?.signal\n        });\n\n        // Extract the response content\n        const content = response.data.choices[0]?.message?.content || '';\n\n        // Handle tool calls\n        let toolCallRequest: LLMGenerateResult['toolCall'] = undefined;\n        if (response.data.choices[0]?.message?.tool_calls && response.data.choices[0].message.tool_calls.length > 0) {\n          const toolCall = response.data.choices[0].message.tool_calls[0];\n          toolCallRequest = {\n            toolId: toolCall.function.name,\n            args: JSON.parse(toolCall.function.arguments)\n          };\n        }\n\n        return {\n          content,\n          finishReason: response.data.choices[0]?.finish_reason || 'stop',\n          usage: response.data.usage,\n          toolCall: toolCallRequest\n        };\n      }\n    } catch (error: any) {\n      Logger.instance.error('Mistral AI generate error:', error);\n      let errorMessage = 'Failed to call Mistral AI API.';\n\n      if (error.response) {\n        errorMessage = `Mistral AI API Error (${error.response.status}): ${error.response.data?.error?.message || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  async listModels(): Promise<LLMModelInfo[]> {\n    if (!this.isConfigured()) {\n      Logger.instance.warn('Cannot fetch Mistral AI models, client not configured.');\n      return [];\n    }\n\n    try {\n      // Try to fetch models from the API\n      const response = await this.client.get('/models');\n\n      if (response.data && response.data.data) {\n        const apiModels = response.data.data;\n        Logger.instance.info(`Provider mistralai has ${apiModels.length} models available from API`);\n\n        return apiModels.map((model: any) => ({\n          id: model.id,\n          name: this.getDisplayName(model.id),\n          description: this.getModelDescription(model.id),\n          contextWindow: this.getContextWindowForModel(model.id),\n          pricingInfo: this.getPricingInfoForModel(model.id)\n        }));\n      }\n    } catch (error) {\n      Logger.instance.warn('Failed to fetch Mistral AI models from API, using predefined list', error);\n    }\n\n    // Return predefined models if API call fails\n    return this.getPredefinedModels();\n  }\n\n  /**\n     * Get predefined models\n     */\n  private getPredefinedModels(): LLMModelInfo[] {\n    return [\n      // Mistral API models\n      {\n        id: 'mistral-tiny',\n        name: 'Mistral Tiny',\n        description: 'Fast and efficient model for everyday tasks',\n        contextWindow: 8192,\n        pricingInfo: 'API usage pricing'\n      },\n      {\n        id: 'mistral-small',\n        name: 'Mistral Small',\n        description: 'Balanced model for most use cases',\n        contextWindow: 8192,\n        pricingInfo: 'API usage pricing'\n      },\n      {\n        id: 'mistral-medium',\n        name: 'Mistral Medium',\n        description: 'Advanced model with strong reasoning capabilities',\n        contextWindow: 32768,\n        pricingInfo: 'API usage pricing'\n      },\n      {\n        id: 'mistral-large',\n        name: 'Mistral Large',\n        description: 'Most powerful Mistral model with superior performance',\n        contextWindow: 32768,\n        pricingInfo: 'API usage pricing'\n      },\n      // Open models\n      {\n        id: 'open-mistral-7b',\n        name: 'Open Mistral 7B',\n        description: 'Open-source Mistral 7B model',\n        contextWindow: 8192,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'open-mixtral-8x7b',\n        name: 'Open Mixtral 8x7B',\n        description: 'Open-source Mixtral 8x7B model (MoE architecture)',\n        contextWindow: 32768,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'mistral-7b-instruct',\n        name: 'Mistral 7B Instruct',\n        description: 'Instruction-tuned Mistral 7B model',\n        contextWindow: 8192,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'mixtral-8x7b-instruct',\n        name: 'Mixtral 8x7B Instruct',\n        description: 'Instruction-tuned Mixtral 8x7B model',\n        contextWindow: 32768,\n        pricingInfo: 'Free (open weights)'\n      }\n    ];\n  }\n\n  /**\n     * Get display name for a model\n     */\n  private getDisplayName(modelId: string): string {\n    // Convert model ID to a more readable name\n    const modelMap: Record<string, string> = {\n      'mistral-tiny': 'Mistral Tiny',\n      'mistral-small': 'Mistral Small',\n      'mistral-medium': 'Mistral Medium',\n      'mistral-large': 'Mistral Large',\n      'open-mistral-7b': 'Open Mistral 7B',\n      'open-mixtral-8x7b': 'Open Mixtral 8x7B',\n      'mistral-7b-instruct': 'Mistral 7B Instruct',\n      'mixtral-8x7b-instruct': 'Mixtral 8x7B Instruct'\n    };\n\n    return modelMap[modelId] || modelId;\n  }\n\n  /**\n     * Get description for a model\n     */\n  private getModelDescription(modelId: string): string {\n    const descriptionMap: Record<string, string> = {\n      'mistral-tiny': 'Fast and efficient model for everyday tasks',\n      'mistral-small': 'Balanced model for most use cases',\n      'mistral-medium': 'Advanced model with strong reasoning capabilities',\n      'mistral-large': 'Most powerful Mistral model with superior performance',\n      'open-mistral-7b': 'Open-source Mistral 7B model',\n      'open-mixtral-8x7b': 'Open-source Mixtral 8x7B model (MoE architecture)',\n      'mistral-7b-instruct': 'Instruction-tuned Mistral 7B model',\n      'mixtral-8x7b-instruct': 'Instruction-tuned Mixtral 8x7B model'\n    };\n\n    return descriptionMap[modelId] || 'Mistral AI model';\n  }\n\n  /**\n     * Get context window size for a model\n     */\n  private getContextWindowForModel(modelId: string): number {\n    // Context window sizes based on Mistral AI documentation\n    if (modelId.includes('medium') || modelId.includes('large') || modelId.includes('mixtral')) {\n      return 32768; // 32K context window\n    }\n    return 8192; // 8K context window for other models\n  }\n\n  /**\n     * Get pricing information for a model\n     */\n  private getPricingInfoForModel(modelId: string): string {\n    if (modelId.startsWith('open-') || modelId.includes('7b') || modelId.includes('8x7b')) {\n      return 'Free (open weights)';\n    }\n    return 'API usage pricing';\n  }\n\n  /**\n     * Test connection to Mistral AI\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.isConfigured()) {\n      return {\n        success: false,\n        message: 'Mistral AI not configured. Please check your API key.'\n      };\n    }\n\n    try {\n      // Make a simple test request\n      const response = await this.client.post('/chat/completions', {\n        model: modelId,\n        messages: [{ role: 'user', content: 'Hello' }],\n        max_tokens: 10\n      });\n\n      if (response.data && response.data.choices) {\n        return {\n          success: true,\n          message: `Successfully connected to Mistral AI API and tested model '${modelId}'.`\n        };\n      }\n\n      return {\n        success: false,\n        message: 'Connected to API but received an unexpected response.'\n      };\n    } catch (error: any) {\n      Logger.instance.error('Mistral AI connection test failed:', error);\n      let errorMessage = 'Failed to connect to Mistral AI API';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error?.message || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  // Use the parent class implementation for getConfig and updateConfig\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your Mistral AI API key',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The Mistral AI API endpoint (default: https://api.mistral.ai/v1)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use',\n        required: false,\n        type: 'select',\n        options: [\n          'mistral-tiny',\n          'mistral-small',\n          'mistral-medium',\n          'mistral-large',\n          'open-mistral-7b',\n          'open-mixtral-8x7b',\n          'mistral-7b-instruct',\n          'mixtral-8x7b-instruct'\n        ]\n      }\n    ];\n  }\n}\n\n\n"]}