{"version": 3, "file": "agentTree.js", "sourceRoot": "", "sources": ["../../../src/ui/agents/agentTree.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4NA,sDAuBC;AAnPD,+CAAiC;AAEjC,2EAAwE;AACxE,yCAAsC;AACtC,2CAA6B;AAC7B,4DAAyD;AAEzD;;GAEG;AACH,MAAM,aAAc,SAAQ,2BAAY;IAEhB;IACA;IAFpB,YACoB,KAAY,EACZ,gBAAiD;QAEjE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAHpB,UAAK,GAAL,KAAK,CAAO;QACZ,qBAAgB,GAAhB,gBAAgB,CAAiC;QAIjE,IAAI,CAAC,IAAI,CAAC;YACN,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,OAAO,EAAE,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,IAAI;YACxC,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,OAAO;SACxB,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,WAAW,CAAC;QAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,GAAG;YACZ,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YACnE,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;SACpE,CAAC;QAEF,+CAA+C;QAC/C,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,EAAE,wBAAwB;YACjC,KAAK,EAAE,YAAY;YACnB,SAAS,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;SACxB,CAAC;IACN,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,iBAAkB,SAAQ,MAAM,CAAC,QAAQ;IAEvB;IACA;IACA;IACA;IACA;IALpB,YACoB,KAAa,EACb,MAAa,EACb,IAAuC,EACvC,KAAa,EACb,gBAAiD;QAEjE,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QANf,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAO;QACb,SAAI,GAAJ,IAAI,CAAmC;QACvC,UAAK,GAAL,KAAK,CAAQ;QACb,qBAAgB,GAAhB,gBAAgB,CAAiC;QAIjE,gBAAgB;QAChB,IAAI,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,CAAC;QAE1C,iDAAiD;QACjD,IAAI,CAAC,YAAY,GAAG,iBAAiB,IAAI,EAAE,CAAC;QAE5C,uBAAuB;QACvB,IAAI,QAAQ,GAAG,cAAc,CAAC;QAC9B,IAAI,IAAI,KAAK,MAAM;YAAE,QAAQ,GAAG,UAAU,CAAC;QAC3C,IAAI,IAAI,KAAK,OAAO;YAAE,QAAQ,GAAG,WAAW,CAAC;QAC7C,IAAI,IAAI,KAAK,cAAc;YAAE,QAAQ,GAAG,WAAW,CAAC;QAEpD,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,GAAG;YACZ,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YACnE,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;SACpE,CAAC;QAEF,uDAAuD;QACvD,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;YAC7C,IAAI,CAAC,OAAO,GAAG;gBACX,OAAO,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,wBAAwB;gBAC/E,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE,CAAC,KAAK,CAAC;aACrB,CAAC;QACN,CAAC;IACL,CAAC;CACJ;AAED;;GAEG;AACH,MAAa,qBAAqB;IACtB,oBAAoB,GAAgD,IAAI,MAAM,CAAC,YAAY,EAA0B,CAAC;IACrH,mBAAmB,GAAyC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAErG;QACI,+CAA+C;QAC/C,2BAAY,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAAwB;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAAyB;QACjC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,+BAA+B;YAC/B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7C,CAAC;aAAM,IAAI,OAAO,YAAY,aAAa,EAAE,CAAC;YAC1C,sCAAsC;YACtC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,SAAS;QACb,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC,YAAY,EAAE,CAAC;YAEzD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,kDAAkD;gBAClD,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,QAAQ,CACpC,sBAAsB,EACtB,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC;gBAEF,sCAAsC;gBACtC,YAAY,CAAC,OAAO,GAAG;oBACnB,OAAO,EAAE,kBAAkB;oBAC3B,KAAK,EAAE,WAAW;oBAClB,SAAS,EAAE,EAAE;iBAChB,CAAC;gBAEF,kCAAkC;gBAClC,YAAY,CAAC,OAAO,GAAG,iEAAiE,CAAC;gBAEzF,6CAA6C;gBAC7C,YAAY,CAAC,YAAY,GAAG,UAAU,CAAC;gBAEvC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC1B,CAAC;YAED,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,aAAa,CACxC,KAAK,EACL,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;gBAClB,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,SAAS;gBAC3C,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAC7C,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAEpE,sCAAsC;YACtC,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,QAAQ,CACjC,6BAA6B,EAC7B,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC;YAEF,iCAAiC;YACjC,SAAS,CAAC,OAAO,GAAG;gBAChB,OAAO,EAAE,sBAAsB;gBAC/B,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,EAAE;aAChB,CAAC;YAEF,mCAAmC;YACnC,SAAS,CAAC,OAAO,GAAG,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAEtG,OAAO,CAAC,SAAS,CAAC,CAAC;QACvB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAY;QACnC,MAAM,KAAK,GAAsB,EAAE,CAAC;QAEpC,qBAAqB;QACrB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,UAAU,KAAK,CAAC,SAAS,CAAC,QAAQ,MAAM,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACrF,KAAK,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAC5B,UAAU,EACV,KAAK,EACL,OAAO,EACP,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,EACxD,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;QACP,CAAC;QAED,YAAY;QACZ,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACvB,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBACjD,KAAK,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAC5B,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,EAAE,EAC9B,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AAlID,sDAkIC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,OAAgC;IAClE,MAAM,gBAAgB,GAAG,IAAI,qBAAqB,EAAE,CAAC;IACrD,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE;QAC9D,gBAAgB;QAChB,eAAe,EAAE,IAAI;QACrB,aAAa,EAAE,KAAK;KACvB,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,QAAQ,EACR,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAC7F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAC7F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,CAAC,OAAe,EAAE,EAAE;QAC1E,kEAAkE;QAClE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,OAAO,CAAC,CAAC;IAC7E,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,MAAc,EAAE,EAAE;QAC1E,mEAAmE;QACnE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;IAC3E,CAAC,CAAC,CACL,CAAC;IAEF,OAAO,QAAQ,CAAC;AACpB,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Agent } from '../../agents/agentUtilities/agent';\nimport { AgentManager } from '../../agents/agentUtilities/agentManager';\nimport { Logger } from '../../logger';\nimport * as path from 'path';\nimport { BaseTreeItem } from '../treeItems/baseTreeItem';\n\n/**\n * TreeItem representing an Agent in the sidebar tree view\n */\nclass AgentTreeItem extends BaseTreeItem {\n    constructor(\n        public readonly agent: Agent,\n        public readonly collapsibleState: vscode.TreeItemCollapsibleState\n    ) {\n        super(agent.name, collapsibleState);\n\n        this.init({\n            id: agent.id,\n            tooltip: agent.description || agent.name,\n            description: '',\n            contextValue: 'agent'\n        });\n\n        const iconName = 'agent.svg';\n        const resourcesPath = path.join(__dirname, '..', '..', 'resources');\n        this.iconPath = {\n            light: vscode.Uri.file(path.join(resourcesPath, 'light', iconName)),\n            dark: vscode.Uri.file(path.join(resourcesPath, 'dark', iconName))\n        };\n\n        // Command to run when the tree item is clicked\n        this.command = {\n            command: 'codessa.openAgentPanel',\n            title: 'Open Agent',\n            arguments: [agent.id]\n        };\n    }\n}\n\n/**\n * TreeItem representing agent operations like tools\n */\nclass AgentPropertyItem extends vscode.TreeItem {\n    constructor(\n        public readonly label: string,\n        public readonly parent: Agent,\n        public readonly type: 'tool' | 'model' | 'chainedAgent',\n        public readonly value: string,\n        public readonly collapsibleState: vscode.TreeItemCollapsibleState\n    ) {\n        super(label, collapsibleState);\n\n        // Set unique ID\n        this.id = `${parent.id}-${type}-${value}`;\n\n        // Define contextValue for context menu filtering\n        this.contextValue = `agentProperty-${type}`;\n\n        // Set appropriate icon\n        let iconName = 'property.svg';\n        if (type === 'tool') iconName = 'tool.svg';\n        if (type === 'model') iconName = 'model.svg';\n        if (type === 'chainedAgent') iconName = 'chain.svg';\n\n        const resourcesPath = path.join(__dirname, '..', '..', 'resources');\n        this.iconPath = {\n            light: vscode.Uri.file(path.join(resourcesPath, 'light', iconName)),\n            dark: vscode.Uri.file(path.join(resourcesPath, 'dark', iconName))\n        };\n\n        // Clicking opens details panel for some property types\n        if (type === 'tool' || type === 'chainedAgent') {\n            this.command = {\n                command: type === 'tool' ? 'codessa.openToolDetails' : 'codessa.openAgentPanel',\n                title: 'Open Details',\n                arguments: [value]\n            };\n        }\n    }\n}\n\n/**\n * Tree data provider for the agent sidebar\n */\nexport class AgentTreeDataProvider implements vscode.TreeDataProvider<vscode.TreeItem> {\n    private _onDidChangeTreeData: vscode.EventEmitter<vscode.TreeItem | null> = new vscode.EventEmitter<vscode.TreeItem | null>();\n    readonly onDidChangeTreeData: vscode.Event<vscode.TreeItem | null> = this._onDidChangeTreeData.event;\n\n    constructor() {\n        // Listen for agent changes to refresh the tree\n        AgentManager.getInstance().onAgentsChanged(() => this.refresh());\n    }\n\n    /**\n     * Refresh the entire tree\n     */\n    refresh(): void {\n        this._onDidChangeTreeData.fire(null);\n    }\n\n    /**\n     * Get tree item representation for an element\n     */\n    getTreeItem(element: vscode.TreeItem): vscode.TreeItem {\n        return element;\n    }\n\n    /**\n     * Get children of the provided element, or root elements if no element provided\n     */\n    getChildren(element?: vscode.TreeItem): Thenable<vscode.TreeItem[]> {\n        if (!element) {\n            // Root level - show all agents\n            return Promise.resolve(this.getAgents());\n        } else if (element instanceof AgentTreeItem) {\n            // Agent level - show agent properties\n            return Promise.resolve(this.getAgentProperties(element.agent));\n        }\n\n        return Promise.resolve([]);\n    }\n\n    /**\n     * Get all agents as tree items\n     */\n    private getAgents(): vscode.TreeItem[] {\n        try {\n            const agents = AgentManager.getInstance().getAllAgents();\n\n            if (agents.length === 0) {\n                // Create a more helpful message with instructions\n                const noAgentsItem = new vscode.TreeItem(\n                    'No agents configured',\n                    vscode.TreeItemCollapsibleState.None\n                );\n\n                // Add a command to create a new agent\n                noAgentsItem.command = {\n                    command: 'codessa.addAgent',\n                    title: 'Add Agent',\n                    arguments: []\n                };\n\n                // Add a tooltip with instructions\n                noAgentsItem.tooltip = 'Click to create a new agent, or use the + button in the toolbar';\n\n                // Add a context value for menu contributions\n                noAgentsItem.contextValue = 'noAgents';\n\n                return [noAgentsItem];\n            }\n\n            return agents.map(agent => new AgentTreeItem(\n                agent,\n                (agent.tools.size > 0)\n                    ? vscode.TreeItemCollapsibleState.Collapsed\n                    : vscode.TreeItemCollapsibleState.None\n            ));\n        } catch (error) {\n            Logger.instance.error('Error getting agents for tree view:', error);\n\n            // Create a more helpful error message\n            const errorItem = new vscode.TreeItem(\n                'Click to configure settings',\n                vscode.TreeItemCollapsibleState.None\n            );\n\n            // Add a command to open settings\n            errorItem.command = {\n                command: 'codessa.openSettings',\n                title: 'Open Settings',\n                arguments: []\n            };\n\n            // Add a tooltip with error details\n            errorItem.tooltip = `Error loading agents: ${error instanceof Error ? error.message : String(error)}`;\n\n            return [errorItem];\n        }\n    }\n\n    /**\n     * Get agent properties as tree items\n     */\n    private getAgentProperties(agent: Agent): vscode.TreeItem[] {\n        const items: vscode.TreeItem[] = [];\n\n        // Add LLM model info\n        if (agent.llmConfig) {\n            const modelLabel = `Model: ${agent.llmConfig.provider} / ${agent.llmConfig.modelId}`;\n            items.push(new AgentPropertyItem(\n                modelLabel,\n                agent,\n                'model',\n                `${agent.llmConfig.provider}-${agent.llmConfig.modelId}`,\n                vscode.TreeItemCollapsibleState.None\n            ));\n        }\n\n        // Add tools\n        if (agent.tools.size > 0) {\n            for (const [toolId, tool] of agent.tools.entries()) {\n                items.push(new AgentPropertyItem(\n                    `Tool: ${tool.name || toolId}`,\n                    agent,\n                    'tool',\n                    toolId,\n                    vscode.TreeItemCollapsibleState.None\n                ));\n            }\n        }\n\n        return items;\n    }\n}\n\n/**\n * Setup and register the agent tree view\n */\nexport function registerAgentTreeView(context: vscode.ExtensionContext): vscode.TreeView<vscode.TreeItem> {\n    const treeDataProvider = new AgentTreeDataProvider();\n    const treeView = vscode.window.createTreeView('codessaAgentView', {\n        treeDataProvider,\n        showCollapseAll: true,\n        canSelectMany: false\n    });\n\n    context.subscriptions.push(\n        treeView,\n        vscode.commands.registerCommand('codessa.refreshAgentTree', () => treeDataProvider.refresh()),\n        vscode.commands.registerCommand('codessa.refreshAgentView', () => treeDataProvider.refresh()),\n        vscode.commands.registerCommand('codessa.openAgentPanel', (agentId: string) => {\n            // Command handler to open agent panel implemented in extension.ts\n            vscode.commands.executeCommand('codessa.openAgentDetailsPanel', agentId);\n        }),\n        vscode.commands.registerCommand('codessa.openToolDetails', (toolId: string) => {\n            // Command handler to open tool details implemented in extension.ts\n            vscode.commands.executeCommand('codessa.openToolDetailsPanel', toolId);\n        })\n    );\n\n    return treeView;\n}"]}