{"version": 3, "file": "pseudoTerminal.js", "sourceRoot": "", "sources": ["../../src/tools/pseudoTerminal.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AAC7B,iDAAoD;AACpD,mCAAsC;AACtC,sCAAmC;AAEnC,0CAA0C;AAC1C,6DAG8B;AA0F9B;;GAEG;AACH,MAAa,sBAAuB,SAAQ,+CAA0B;IACjD,gBAAgB,GAAgB,IAAI,GAAG,EAAE,CAAC;IACnD,aAAa,GAAW,EAAE,CAAC;IAC3B,iBAAiB,GAA0B,IAAI,CAAC;IAChD,oBAAoB,CAA8B;IAClD,WAAW,GAAgC,IAAI,GAAG,EAAE,CAAC;IACrD,YAAY,GAAsC,IAAI,GAAG,EAAE,CAAC;IACnD,WAAW,GAAG,IAAI,MAAM,CAAC,YAAY,EAIlD,CAAC;IACY,eAAe,GAAG,IAAI,MAAM,CAAC,YAAY,EAGtD,CAAC;IAAqB,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;IAC9C,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;IAErD,mBAAmB,CAAC,MAAc,EAAE,OAA6B;QACpE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAEM,cAAc,CAAC,MAAc;QAChC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAEM,iBAAiB,CAAC,MAAc,EAAE,OAAsC;QAC3E,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,GAAG,QAAQ,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC;IAED;QACI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,mCAAmC;QACnC,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;YACtF,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,2BAA2B;SAC3C,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,kBAAkB;YACxB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;YAC1F,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,6BAA6B;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,kBAAkB;YACxB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;YACxF,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,6BAA6B;SAC7C,CAAC,CAAC;IACP,CAAC;IAEO,0BAA0B;QAC9B,sCAAsC;QACtC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,6BAA6B;QAC7B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAEO,yBAAyB;QAC7B,oBAAoB;QACpB,IAAI,CAAC,eAAe,CAAC;YACjB,OAAO,EAAE,6BAA6B;YACtC,MAAM,EAAE,yBAAyB;YACjC,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,gBAAgB;YAC1B,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,6BAA6B,GAAG,EAAE,EAClF,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,iCAAiC;oBAC1C,KAAK,EAAE,WAAW,GAAG,EAAE;oBACvB,SAAS,EAAE,CAAC,GAAG,CAAC;iBACnB,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,OAAO,EAAE,YAAY,GAAG,aAAa;wBACrC,KAAK;wBACL,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,UAAU;qBACrB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,eAAe,CAAC;YACjB,OAAO,EAAE,2BAA2B;YACpC,MAAM,EAAE,iBAAiB;YACzB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,UAAU;YACpB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,wBAAwB,EAC3E,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CACpD,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,iCAAiC;oBAC1C,KAAK,EAAE,iBAAiB;oBACxB,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBAC5C,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,OAAO,EAAE,qBAAqB,KAAK,CAAC,CAAC,CAAC,EAAE;wBACxC,KAAK;wBACL,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,UAAU;qBACrB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,iBAAiB;QACjB,IAAI,CAAC,eAAe,CAAC;YACjB,OAAO,EAAE,uCAAuC;YAChD,MAAM,EAAE,aAAa;YACrB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,kBAAkB;YAC5B,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,yBAAyB,EAC3E,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,6BAA6B;oBACtC,KAAK,EAAE,aAAa;oBACpB,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBAC5C,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,OAAO,EAAE,kBAAkB,KAAK,CAAC,CAAC,CAAC,EAAE;wBACrC,KAAK;wBACL,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,OAAO;wBAC3C,MAAM,EAAE,UAAU;qBACrB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;IAEO,yBAAyB;QAC7B,eAAe;QACf,IAAI,CAAC,eAAe,CAAC;YACjB,OAAO,EAAE,8CAA8C;YACvD,MAAM,EAAE,iBAAiB;YACzB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,OAAO;YACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,kBAAkB,EAClE,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CACjD,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,gCAAgC;oBACzC,KAAK,EAAE,iBAAiB;oBACxB,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBAC5C,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,OAAO,EAAE,gBAAgB,KAAK,CAAC,CAAC,CAAC,EAAE;wBACnC,KAAK;wBACL,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,UAAU;wBAClB,kBAAkB,EAAE,CAAC;gCACjB,QAAQ,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC;gCAC7C,OAAO,EAAE,sBAAsB;6BAClC,CAAC;qBACL,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,eAAe,CAAC;YACjB,OAAO,EAAE,kEAAkE;YAC3E,MAAM,EAAE,oBAAoB;YAC5B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,YAAY;YACtB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;gBACxE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,gCAAgC,UAAU,EAAE,EAC9F,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CACtD,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,oCAAoC;oBAC7C,KAAK,EAAE,oBAAoB;oBAC3B,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBAC5C,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,OAAO,EAAE,uBAAuB,UAAU,EAAE;wBAC5C,KAAK;wBACL,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,OAAO;wBAC3C,MAAM,EAAE,UAAU;wBAClB,IAAI,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC;wBACxC,kBAAkB,EAAE,CAAC;gCACjB,QAAQ,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC;gCAC7C,OAAO,EAAE,oBAAoB,UAAU,qBAAqB;6BAC/D,CAAC;qBACL,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,MAAc;QACrC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAE3B,MAAM,gBAAgB;YAClB,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAC5C,UAAU,GAAG,UAAU,CAAC;YACxB,OAAO,GAAG,CAAC,CAAC;YACZ,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YAEtC,OAAO,CAAC,KAAoB;gBACxB,IAAI,CAAC,KAAK;oBAAE,OAAO,MAAM,CAAC;gBAC1B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;oBACtC,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACzF,CAAC;gBACD,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;gBACxE,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACrE,MAAM,SAAS,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC3C,aAAa,CAAC,SAAS,CAAC,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACtF,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC;YAED,MAAM,CAAC,cAAwC;gBAC3C,MAAM,IAAI,GAAG,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;gBACvF,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC/B,OAAO;oBACH,UAAU,EAAE,IAAI;oBAChB,IAAI;oBACJ,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;oBACnD,uBAAuB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;oBACzE,gCAAgC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;oBACnD,mBAAmB,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC;iBAChD,CAAC;YACN,CAAC;SACJ;QAED,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACvE,MAAM,OAAO,GAAsB;YAC/B,QAAQ,EAAE,IAAI,gBAAgB,EAAyB;YACvD,KAAK;YACL,WAAW,EAAE,EAAE;YACf,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,UAAU;SACrB,CAAC;QAEF,2BAA2B;QAC3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAE/C,4CAA4C;QAC5C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAEnE,4BAA4B;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAE7D,qDAAqD;QACrD,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC;aACnE,GAAG,CAAC,MAAM,CAAC,EAAE;YACV,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;YACnF,MAAM,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACvE,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC,CAAC;QAEP,uCAAuC;QACvC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAEpC,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,aAAa,CAAC,MAAc;QAChC,uCAAuC;QACvC,MAAM,eAAe,GAAG,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,IAAI,eAAe,CAAC;QAEtC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,GAAG,CAAC,CAAC;IAC9E,CAAC;IAEO,4BAA4B,CAAC,MAAc;QAC/C,yCAAyC;QACzC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpB,IAAI,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,EAAE,CAAC;gBACxC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;YACtE,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC9B,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;YACxE,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,EAAE,CAAC;gBACxC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;YACxE,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC3B,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;YACrE,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEO,kBAAkB;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QACxC,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC;oBACD,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC5B,qCAAqC;wBACrC,eAAe,CAAC,IAAI,CAChB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;4BACrD,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE,CAAC;gCAC7B,MAAM,SAAS,GAAG,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;gCAC1D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oCAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oCACrB,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oCAC7B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oCAC1C,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gCAC1C,CAAC;4BACL,CAAC;wBACL,CAAC,CAAC,CACL,CAAC;oBACN,CAAC;yBAAM,CAAC;wBACJ,wCAAwC;wBACxC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;wBACtD,MAAM,SAAS,GAAG,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;wBAE1D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;4BAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BACrB,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;4BAC7B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;4BAC1C,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;wBAC1C,CAAC;oBACL,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,wCAAwC,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;gBACpF,CAAC;YACL,CAAC;QACL,CAAC;QAED,iDAAiD;QACjD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,cAAc,CAAC,OAAwB,EAAE,OAAyB;QACtE,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC5B,OAAO,CAAC,CAAC,CAAC,EACV,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAChD,CAAC;QACF,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC;QAC/B,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;QAEjC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CACzB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,EACrC,CAAC,UAAU,CAAC,CACf,CAAC;IACN,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC3C,QAAQ,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YAC7B,KAAK,OAAO,CAAC;YACb,KAAK,UAAU;gBACX,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC3C,KAAK,SAAS,CAAC;YACf,KAAK,YAAY;gBACb,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC7C,KAAK,MAAM,CAAC;YACZ,KAAK,UAAU;gBACX,OAAO,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC;YACjD;gBACI,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC;QAC9C,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,CACpB,QAA6B,EAC7B,KAAmB,EACnB,OAAiC,EACjC,KAA+B;QAE/B,yBAAyB;QACzB,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,EAAE,CAAC;QACd,CAAC;QAED,wEAAwE;QACxE,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,UAAU,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvE,+EAA+E;YAC/E,2EAA2E;YAC3E,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAE5C,+CAA+C;YAC/C,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBAC3B,oEAAoE;gBACpE,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACnC,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC;wBAC9B,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,CAClC,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC;QAED,uDAAuD;QACvD,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,OAAwB,EAAE,OAAyB;QACvE,MAAM,aAAa,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAyB,CAAC;QAC5F,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhD,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAC/D,MAAM,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QAC/E,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEnE,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,kBAAkB,CAAC,OAA4B;QACnD,MAAM,WAAW,GAAwB,EAAE,CAAC;QAC5C,+BAA+B;QAE/B,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACrB,IAAI,MAAM,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC;gBAC7B,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC9B,sDAAsD;oBACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;oBAClC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;oBAE9B,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,EAClC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,CAChB,CAAC;oBACF,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC;oBAC/B,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC;oBACrC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,CAAC,GAAG,CACzB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,EACrC,WAAW,CACd,CAAC;IACN,CAAC;IAEO,6BAA6B,CAAC,QAAgB;QAClD,QAAQ,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YAC7B,KAAK,UAAU;gBACX,OAAO,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC1C,KAAK,UAAU;gBACX,OAAO,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC7D,KAAK,OAAO;gBACR,OAAO,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC1D,KAAK,YAAY;gBACb,OAAO,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC/D,KAAK,UAAU;gBACX,OAAO,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC;YACjD;gBACI,OAAO,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;QAC9C,CAAC;IACL,CAAC;IAEM,eAAe,CAAC,OAAyC;QAC5D,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAEO,WAAW,CAAC,OAAyC;QACzD,OAAO,WAAW,IAAI,OAAO,IAAI,iBAAiB,IAAI,OAAO,CAAC;IAClE,CAAC;IAEM,eAAe,CAAC,SAAiB;QACpC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IAEM,YAAY;QACf,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,gBAAgB,CACzB,OAAuB,EACvB,OAAyB;QAEzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;QAEhD,IAAI,OAAgB,CAAC;QACrB,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACrB,OAAO,EAAE,CAAC;QACd,CAAC;QAED,gDAAgD;QAChD,+CAA+C;QAC/C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAExE,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YACpD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACzB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC;gBAC3D,CAAC,CAAC,CAAC;gBACH,OAAO,OAAO,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC;gBAChE,OAAO,CAAC,OAAO,CAAC,CAAC;YACrB,CAAC;QACL,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,SAAqB,EAAE,OAAgB;QAC/D,0CAA0C;QAC1C,yDAAyD;QACzD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QACnE,OAAO,OAAO,CAAC,OAAO,CAAC;YACnB,UAAU,EAAE,GAAG,MAAM,gBAAgB;YACrC,UAAU,EAAE,IAAI;YAChB,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,OAAO;SACV,CAAC,CAAC;IACP,CAAC;IAEO,WAAW,CAAC,QAAgB,EAAE,OAAgB;QAClD,2CAA2C;QAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACpD,OAAO,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,MAAc;QACvC,0DAA0D;QAC1D,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC3B,iCAAiC;QACjC,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACrC,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC;oBACD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBACtD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC5F,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,MAAyB;QAChD,IAAI,CAAC;YACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC9D,IAAI,OAAO,EAAE,CAAC;oBACV,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,SAAS,CAAC,CAAC;gBAC1D,CAAC;gBACD,OAAO,OAAO,CAAC;YACnB,CAAC;iBAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;gBAChG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,SAAS,CAAC,CAAC;gBACtD,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,OAAO,KAAK,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEM,oBAAoB;QACvB,OAAO,sCAAiB,CAAC;IAC7B,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;QACpC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;CACJ;AAxmBD,wDAwmBC;AAqID;;GAEG;AACH,MAAa,eAAgB,SAAQ,qBAAY;IAC7B,EAAE,CAAS;IACX,MAAM,CAAiB;IACvB,KAAK,CAAgB;IACrB,KAAK,CAAgB;IACrB,MAAM,CAAiB;IAE/B,SAAS,GAA2B,IAAI,CAAC;IACzC,WAAW,GAAG,KAAK,CAAC;IACpB,cAAc,GAAG,KAAK,CAAC;IACvB,eAAe,GAAa,EAAE,CAAC;IAC/B,aAAa,GAAG,EAAE,CAAC;IACnB,QAAQ,CAAU;IAClB,UAAU,CAAwB;IAClC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC1B,gBAAgB,GAAG,CAAC,CAAC;IACrB,cAAc,GAAa,EAAE,CAAC;IAC9B,2BAA2B,CAA6B;IAEhE,iBAAiB;IACT,OAAO,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IAC5C,OAAO,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IAC5C,QAAQ,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;IAC3C,SAAS,GAAG,IAAI,MAAM,CAAC,YAAY,EAAqC,CAAC;IACzE,UAAU,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IAC/C,QAAQ,GAAG,IAAI,MAAM,CAAC,YAAY,EAAS,CAAC;IAC5C,cAAc,GAAG,IAAI,MAAM,CAAC,YAAY,EAAiB,CAAC;IAElE,8CAA8C;IACtC,YAAY,CAA8B;IAC1C,YAAY,CAA8B;IAC1C,aAAa,CAA4B;IACzC,cAAc,CAAyD;IACvE,eAAe,CAA8B;IAC7C,aAAa,CAA6B;IAC1C,mBAAmB,CAAqC;IAEhE,sBAAsB;IACd,YAAY,GAAwB,EAAE,CAAC;IACvC,WAAW,GAAa,EAAE,CAAC;IAC3B,UAAU,GAAG,KAAK,CAAC;IACnB,iBAAiB,GAA0B,IAAI,CAAC;IAExD,YAAY,SAAyB,EAAE;QACnC,KAAK,EAAE,CAAC,CAAC,sCAAsC;QAE/C,IAAI,CAAC,EAAE,GAAG,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QAElF,2CAA2C;QAC3C,IAAI,CAAC,2BAA2B,GAAG,IAAI,+CAA0B,EAAE,CAAC;QAEpE,sBAAsB;QACtB,IAAI,CAAC,MAAM,GAAG;YACV,IAAI,EAAE,mBAAmB;YACzB,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAClE,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;YACzC,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;YAClB,GAAG,EAAE,EAAE;YACP,UAAU,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;YACtC,kBAAkB,EAAE,IAAI;YACxB,oBAAoB,EAAE,IAAI;YAC1B,oBAAoB,EAAE,IAAI;YAC1B,UAAU,EAAE,IAAI;YAChB,oBAAoB,EAAE,IAAI;YAC1B,iBAAiB,EAAE,IAAI;YACvB,kBAAkB,EAAE,KAAK;YACzB,wBAAwB,EAAE,IAAI;YAC9B,GAAG,MAAM;SACZ,CAAC;QAEF,mBAAmB;QACnB,IAAI,CAAC,KAAK,GAAG;YACT,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,KAAK;YACd,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;YAClD,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;YACnD,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;YAChE,WAAW,EAAE,EAAE;YACf,cAAc,EAAE,EAAE;YAClB,MAAM,EAAE,KAAK;SAChB,CAAC;QAEF,mBAAmB;QACnB,IAAI,CAAC,KAAK,GAAG;YACT,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,CAAC;YACnB,mBAAmB,EAAE,CAAC;YACtB,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;SACjB,CAAC;QAEF,oBAAoB;QACpB,IAAI,CAAC,MAAM,GAAG;YACV,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YAC1B,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YAC1B,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC5B,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;YAC9B,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;YAChC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC5B,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK;SAC3C,CAAC;QAEF,4CAA4C;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC;QAE/C,IAAI,CAAC,YAAY,CAAC,IAAI,CAClB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,cAAc,CACtB,CAAC;IACN,CAAC;IAEO,YAAY;QAChB,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACvB,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAC5G,CAAC;aAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,UAAU,CAAC;QAC3C,CAAC;aAAM,CAAC;YACJ,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC;QAC5C,CAAC;IACL,CAAC;IAEO,aAAa;QACjB,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QAEvD,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACvB,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzD,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,oBAAoB;YACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;QACtC,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACnC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,iCAAiC;QACjC,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;QACvC,MAAM,aAAa,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC5D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAE3C,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC;gBACD,yEAAyE;gBACzE,OAAO,QAAQ,CAAC;YACpB,CAAC;YAAC,MAAM,CAAC;gBACL,SAAS;YACb,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC,CAAC,uBAAuB;IACzC,CAAC;IAEM,KAAK,CAAC,UAAU;QACnB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,gDAAgD;YAChD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC1C,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,mBAAmB;gBAC7C,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnD,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;gBACrC,GAAG,EAAE;oBACD,GAAG,OAAO,CAAC,GAAG;oBACd,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;oBAClB,IAAI,EAAE,gBAAgB;oBACtB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;oBACnE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;iBACrE;gBACD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI;gBAC5C,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,KAAK;gBAC/C,QAAQ,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;aAC7C,CAAC,CAAC;YAEH,yBAAyB;YACzB,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;gBAC9D,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC9B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEtC,8BAA8B;YAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;YAED,6CAA6C;YAC7C,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;YAE1B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAEhD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACxB,MAAM,GAAG,CAAC;QACd,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,MAAM,YAAY,GAAG;YACjB,OAAO,EAAG,eAAe;YACzB,gBAAgB,EAAG,oBAAoB;YACvC,WAAW,EAAG,cAAc;YAC5B,aAAa,EAAG,sCAAsC;YACtD,aAAa,EAAG,0BAA0B;YAC1C,gCAAgC,EAAG,kBAAkB;YACrD,KAAK,EAAG,wBAAwB;YAChC,SAAS,CAAE,WAAW;SACzB,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;QAC1D,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACnF,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,EAAU;QACrB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,KAAK,CAAC,IAAY;QAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC5D,CAAC;QAED,oDAAoD;QACpD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;YAClD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAY;QAC7B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,OAK5C;QACG,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACnE,OAAO,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,OAAe,EAAE,OAKpD;QACG,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAE5C,MAAM,MAAM,GAAG;YACX,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,KAAK,EAAE,4BAA4B;YAChE,YAAY,EAAE,OAAO,EAAE,YAAY,IAAI,IAAI;YAC3C,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,KAAK;YACxC,gBAAgB,EAAE,OAAO,EAAE,gBAAgB;SAC9C,CAAC;QAEF,IAAI,CAAC;YACD,wCAAwC;YACxC,IAAI,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,KAAK,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBACrF,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAC5D,CAAC;YAED,2BAA2B;YAC3B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAE3B,iDAAiD;YACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAErE,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACvC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,6BAA6B,CAAC,YAAY,CAAC,CAAC;YAElF,OAAO,MAAM,CAAC;QAElB,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,OAAe,EAAE,MAIxD;QACG,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,YAAY,GAAG,EAAE,CAAC;YACtB,IAAI,gBAAgB,GAAG,KAAK,CAAC;YAE7B,gDAAgD;YAChD,MAAM,YAAY,GAAG;gBACjB,mBAAmB;gBACnB,MAAM,EAAE,mBAAmB,EAAE,kBAAkB;gBAC/C,uCAAuC;gBACvC,KAAK,EAAE,YAAY;gBACnB,KAAK,EAAE,WAAW;gBAClB,KAAK,EAAE,cAAc;gBAErB,kBAAkB;gBAClB,cAAc;gBACd,iBAAiB;gBAEjB,aAAa;gBACb,uCAAuC;gBAEvC,kBAAkB;gBAClB,KAAK,EAAE,KAAK,EAAE,YAAY;aAC7B,CAAC;YAEF,0BAA0B;YAC1B,MAAM,aAAa,GAAG;gBAClB,8BAA8B;gBAC9B,6BAA6B;gBAC7B,yBAAyB;gBACzB,UAAU,EAAE,UAAU,EAAE,UAAU;gBAClC,mBAAmB;aACtB,CAAC;YAEF,2BAA2B;YAC3B,MAAM,kBAAkB,GAAG;gBACvB,iBAAiB,EAAE,WAAW;gBAC9B,eAAe,EAAE,gBAAgB;gBACjC,cAAc,EAAE,gBAAgB;aACnC,CAAC;YAEF,MAAM,eAAe,GAAG,CAAC,IAAY,EAAE,EAAE;gBACrC,YAAY,IAAI,IAAI,CAAC;gBAErB,sCAAsC;gBACtC,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;oBACpB,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE,CAAC;wBACvC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;4BAC5B,gBAAgB,GAAG,IAAI,CAAC;4BACxB,YAAY,CAAC,SAAS,CAAC,CAAC;4BACxB,OAAO,CAAC;gCACJ,OAAO;gCACP,MAAM,EAAE,YAAY;gCACpB,QAAQ,EAAE,CAAC;gCACX,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gCAChC,OAAO,EAAE,IAAI;gCACb,UAAU,EAAE,IAAI;gCAChB,WAAW,EAAE,IAAI,IAAI,EAAE;6BAC1B,CAAC,CAAC;4BACH,OAAO;wBACX,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,2BAA2B;gBAC3B,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;oBAClC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;wBAC5B,gBAAgB,GAAG,IAAI,CAAC;wBACxB,YAAY,CAAC,SAAS,CAAC,CAAC;wBACxB,OAAO,CAAC;4BACJ,OAAO;4BACP,MAAM,EAAE,YAAY;4BACpB,QAAQ,EAAE,CAAC;4BACX,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;4BAChC,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;4BAClB,WAAW,EAAE,IAAI,IAAI,EAAE;yBAC1B,CAAC,CAAC;wBACH,OAAO;oBACX,CAAC;gBACL,CAAC;gBAED,qCAAqC;gBACrC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACtB,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;wBACjC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;4BAC5B,gBAAgB,GAAG,IAAI,CAAC;4BACxB,YAAY,CAAC,SAAS,CAAC,CAAC;4BACxB,OAAO,CAAC;gCACJ,OAAO;gCACP,MAAM,EAAE,YAAY;gCACpB,QAAQ,EAAE,CAAC;gCACX,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gCAChC,OAAO,EAAE,IAAI;gCACb,WAAW,EAAE,IAAI,IAAI,EAAE;6BAC1B,CAAC,CAAC;4BACH,OAAO;wBACX,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC,CAAC;YAEF,iBAAiB;YACjB,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACpB,OAAO,CAAC;wBACJ,OAAO;wBACP,MAAM,EAAE,YAAY;wBACpB,QAAQ,EAAE,CAAC,CAAC;wBACZ,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;wBAChC,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,2BAA2B,MAAM,CAAC,OAAO,IAAI;wBACpD,WAAW,EAAE,IAAI,IAAI,EAAE;qBAC1B,CAAC,CAAC;gBACP,CAAC;YACL,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAEnB,yBAAyB;YACzB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAEzD,kCAAkC;YAClC,MAAM,OAAO,GAAG,GAAG,EAAE;gBACjB,YAAY,CAAC,OAAO,EAAE,CAAC;gBACvB,YAAY,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC,CAAC;YAEF,sCAAsC;YACtC,MAAM,eAAe,GAAG,OAAO,CAAC;YAChC,OAAO,GAAG,CAAC,CAAC,MAAqB,EAAE,EAAE;gBACjC,OAAO,EAAE,CAAC;gBACV,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5B,CAAC,CAAmB,CAAC;YAErB,qCAAqC;YACrC,MAAM,cAAc,GAAG,MAAM,CAAC;YAC9B,MAAM,GAAG,CAAC,CAAC,KAAc,EAAE,EAAE;gBACzB,OAAO,EAAE,CAAC;gBACV,cAAc,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAkB,CAAC;QACxB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,6BAA6B,CAAC,mBAA2B;QAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,mBAAmB,CAAC;QAC/B,CAAC;QAED,iEAAiE;QACjE,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,mBAAmB;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,CAAC;QAE5D,OAAO,eAAe,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,mBAAmB,GAAG,KAAK,CAAC;IACvE,CAAC;IAEO,aAAa,CAAC,IAAY;QAC9B,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,iEAAiE;QACjE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC;QACvF,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,SAAS,GAAG,YAAY,SAAS,WAAW,CAAC;QACjD,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE7D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC;YACD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;gBACvC,IAAI,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC1B,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;oBAC7C,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,MAAM,CAAC;gBAChD,CAAC;YACL,CAAC;QACL,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAC5B,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAY,EAAE,UAAmB;QAC/D,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,IAAI,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,IAAY,EAAE,IAAY;QAC1C,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAE7B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QAE7C,kDAAkD;QAClD,2CAA2C;IAC/C,CAAC;IAEM,KAAK,CAAC,KAAK;QACd,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAEM,KAAK,CAAC,KAAK;QACd,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,iBAAiB;QAClE,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,MAAc;QAClC,mDAAmD;QACnD,yDAAyD;QACzD,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,mDAAmD,CAAC,CAAC;IACrF,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,IAAY;QACzC,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAChD,CAAC;IAEM,iBAAiB;QACpB,OAAO,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;IACrC,CAAC;IAEM,mBAAmB;QACtB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAChD,CAAC;IAEO,oBAAoB,CAAC,OAAe;QACxC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB;YAAE,OAAO;QAE9C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,IAAI,CAAC,EAAE,CAAC;YACxE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;IAC1D,CAAC;IAEM,aAAa,CAAC,OAAgB;QACjC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAEM,cAAc;QACjB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;IAC9B,CAAC;IAEO,0BAA0B;QAC9B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QACjH,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,QAAgB;QAChC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;QAE3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAE5C,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEL,8CAA8C;IAEnC,OAAO;QACV,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAE7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,eAAe;QACf,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAClC,CAAC;QAED,mBAAmB;QACnB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAC1B,IAAI,CAAC;gBACD,CAAC,CAAC,OAAO,EAAE,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,kBAAkB;QAClB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,gBAAgB;QAChB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC5B,CAAC;CACJ;AAnqBD,0CAmqBC;AAED;;GAEG;AACH,MAAa,cAAc;IAkBF;IACA;IAlBJ,kBAAkB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IACvD,kBAAkB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IAExD,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;IAC3C,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;IAEnD,aAAa,GAAwB,IAAI,CAAC;IAC1C,OAAO,GAAG,KAAK,CAAC;IAChB,WAAW,GAAG,KAAK,CAAC;IACpB,aAAa,GAAG,EAAE,CAAC;IACnB,iBAAiB,GAA0B,IAAI,CAAC;IAChD,WAAW,CAAwC;IACnD,IAAI,CAAS;IACb,MAAM,CAAS;IACf,UAAU,CAAW;IAE7B,YACqB,SAAyB,EAAE,EAC3B,YAAqD;QADrD,WAAM,GAAN,MAAM,CAAqB;QAC3B,iBAAY,GAAZ,YAAY,CAAyC;QAEtE,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,UAAU,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QACnE,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;IAC1D,CAAC;IAEM,IAAI,CAAC,iBAAwD;QAChE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAE7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,iBAAiB,IAAI,IAAI,CAAC,WAAW,CAAC;QAEzD,IAAI,CAAC;YACD,iCAAiC;YACjC,IAAI,CAAC,aAAa,GAAG,IAAA,qBAAK,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrD,GAAG,EAAE,IAAI,CAAC,IAAI;gBACd,GAAG,EAAE;oBACD,GAAG,OAAO,CAAC,GAAG;oBACd,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;oBAClB,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,KAAK;oBACvD,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,IAAI;iBACpD;gBACD,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CAAC,CAAC;YAEH,8CAA8C;YAC9C,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACnD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACnD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;gBAC3C,MAAM,QAAQ,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;gBACxD,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,CAAC,OAAO,MAAM,CAAC,CAAC;gBAC/C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QAEP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAClE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;IACL,CAAC;IAEO,YAAY;QAChB,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACvB,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAC5G,CAAC;aAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,UAAU,CAAC;QAC3C,CAAC;aAAM,CAAC;YACJ,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC;QAC5C,CAAC;IACL,CAAC;IAEO,aAAa;QACjB,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QAEvD,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACvB,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzD,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,oBAAoB;YACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;QACtC,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACnC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,iCAAiC;QACjC,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;QACvC,MAAM,aAAa,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC5D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAE3C,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC;gBACD,yEAAyE;gBACzE,OAAO,QAAQ,CAAC;YACpB,CAAC;YAAC,MAAM,CAAC;gBACL,SAAS;YACb,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC,CAAC,uBAAuB;IACzC,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAEM,WAAW,CAAC,IAAY;QAC3B,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,IAAY;QAC9B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAEtC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;QAE3B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,EAAE;YACrC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,2BAA2B;IACvC,CAAC;IAEO,kBAAkB;QACtB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjD,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAClC,CAAC;IACL,CAAC;IAEM,aAAa,CAAC,UAAqC;QACtD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACnD,sCAAsC;YACtC,IAAI,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAE7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAED,qBAAqB;IACd,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAAe;QACvC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC/E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACnC,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC5B,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;gBACnD,CAAC,EAAE,KAAK,CAAC,CAAC;gBAEV,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;oBACjC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC9B,CAAC,CAAC;gBAEF,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;oBAClD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACtD,CAAC;gBACD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;oBAClD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACtD,CAAC;gBAED,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;oBACjD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;gBACnD,CAAC;gBAED,+DAA+D;gBAC/D,UAAU,CAAC,GAAG,EAAE;oBACZ,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;wBAClD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;oBACvD,CAAC;oBACD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;wBAClD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;oBACvD,CAAC;oBACD,OAAO,CAAC,MAAM,CAAC,CAAC;gBACpB,CAAC,EAAE,IAAI,CAAC,CAAC;YACb,CAAC,CAAC,CAAC;QACP,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChD,CAAC;IAEM,QAAQ;QACX,2CAA2C;QAC3C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO;gBACH,aAAa,EAAE,CAAC;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,EAAE,CAAC;gBACtB,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;gBACpF,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,CAAC;aACjB,CAAC;QACN,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,QAAQ;QACX,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO;gBACH,WAAW,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM;gBACvC,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM;gBACnC,gBAAgB,EAAE,IAAI,CAAC,IAAI;gBAC3B,SAAS,EAAE,IAAI,CAAC,MAAM;gBACtB,UAAU,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC1D,WAAW,EAAE,EAAE;gBACf,cAAc,EAAE,EAAE;gBAClB,MAAM,EAAE,KAAK;gBACb,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,SAAS;aACrD,CAAC;QACN,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AArRD,wCAqRC;AAED;;GAEG;AACH,MAAa,eAAe;IACP,QAAQ,CAAqB;IAC7B,iBAAiB,CAA6B;IAC9C,cAAc,GAA2B,EAAE,CAAC;IAC5C,cAAc,GAAmC,IAAI,GAAG,EAAE,CAAC;IACpE,UAAU,GAAY,IAAI,CAAC;IAEnC,YAAY,OAA2B;QACnC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,sCAAiB,CAAC,WAAW,CAAC,UAAU,CAA+B;eACzF,IAAI,+CAA0B,EAAE,CAAC;QAExC,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAEO,WAAW;QACf,iDAAiD;QACjD,MAAM,QAAQ,GAAG;YACb,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvG,CAAC;QAEF,gCAAgC;QAChC,MAAM,sBAAsB,GAAG,IAAI,CAAC,QAAsE,CAAC;QAC3G,sBAAsB,CAAC,WAAW,GAAG,sBAAsB,CAAC,WAAW,IAAI,EAAE,CAAC;QAC9E,sBAAsB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAErD,yBAAyB;QACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,oBAAoB;QACxB,8CAA8C;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YACnD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YACxC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAClD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,MAAM,sBAAsB,GAAG,IAAI,CAAC,QAAsE,CAAC;QAC3G,sBAAsB,CAAC,WAAW,GAAG,sBAAsB,CAAC,WAAW,IAAI,EAAE,CAAC;QAC9E,sBAAsB,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;IAC3E,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAAc;QAC/C,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAErE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,iCAAiC;gBACjC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC9B,MAAM,QAAQ,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE,CAAC;oBAC1D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBAEH,0CAA0C;gBAC1C,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,SAAS,OAAO,CAAC,MAAM,wCAAwC,EAC/D,kBAAkB,EAClB,gBAAgB,EAChB,QAAQ,CACX,CAAC;gBAEF,QAAQ,WAAW,EAAE,CAAC;oBAClB,KAAK,kBAAkB;wBACnB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAClC,MAAM;oBACV,KAAK,gBAAgB;wBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;wBACrC,MAAM;gBACd,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,KAAY;QACrC,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QAEjD,oCAAoC;QACpC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAC/C,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;QAEF,MAAM,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,6BAA6B;YACtC,KAAK,EAAE,wBAAwB;YAC/B,SAAS,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;SAC7B,CAAC;QAEF,MAAM,QAAQ,GAAG,kBAAkB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE1C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC1B,4BAA4B,KAAK,CAAC,OAAO,EAAE,EAC3C,UAAU,CACb,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;gBAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAChC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAe;QACzC,IAAI,CAAC;YACD,IAAI,cAAc,GAAG,EAAE,CAAC;YAExB,gEAAgE;YAChE,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAElC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACvB,6CAA6C;gBAC7C,cAAc,GAAG,iBAAiB,OAAO,qBAAqB,OAAO,EAAE,CAAC;YAC5E,CAAC;iBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC/B,uBAAuB;gBACvB,cAAc,GAAG,gBAAgB,OAAO,EAAE,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACJ,uCAAuC;gBACvC,cAAc,GAAG,0CAA0C,OAAO,2BAA2B,OAAO,yBAAyB,OAAO,EAAE,CAAC;YAC3I,CAAC;YAED,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE5C,MAAM,MAAM,GAAyB;gBACjC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,WAAW,OAAO,EAAE,CAAC;gBACnD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,cAAc,OAAO,KAAK;aACrC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,OAAO,KAAK,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC;QAC7E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,WAAmB;QAC9C,IAAI,CAAC;YACD,6BAA6B;YAC7B,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvE,sDAAsD;gBACtD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBAC9D,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;YAC5D,CAAC;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3C,mCAAmC;gBACnC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAChE,CAAC;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3C,0CAA0C;gBAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,qDAAqD,CAAC,CAAC;YACvF,CAAC;iBAAM,CAAC;gBACJ,sCAAsC;gBACtC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;gBACvD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;gBACrE,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,MAAM,GAAyB;gBACjC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC;gBAClD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,2BAA2B;aACtC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QACjD,IAAI,CAAC;YACD,wBAAwB;YACxB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;YAEjF,qCAAqC;YACrC,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9D,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;YAC9E,CAAC;YAED,MAAM,MAAM,GAAyB;gBACjC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC;gBACrD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,yBAAyB;aACpC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QACjD,IAAI,CAAC;YACD,qCAAqC;YACrC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,0BAA0B;YAC5E,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,WAAW;YAE/D,uBAAuB;YACvB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YAEnD,wDAAwD;YACxD,IAAI,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;YAC1F,CAAC;YAED,MAAM,MAAM,GAAyB;gBACjC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC;gBACrD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,kDAAkD;aAC7D,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,YAAoB;QACnD,IAAI,CAAC;YACD,+BAA+B;YAC/B,IAAI,YAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,sCAAsC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAChF,IAAI,OAAO,EAAE,CAAC;oBACV,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBACpC,OAAO;gBACX,CAAC;YACL,CAAC;YAED,2BAA2B;YAC3B,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;QAC3E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QAEzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;YACtE,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjC,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,WAAW;YAC9C,MAAM,EAAE,MAAM;SACjB,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;YACtD,WAAW,EAAE,sCAAsC;SACtD,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAA4B;QACvD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEjC,4CAA4C;QAC5C,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAAyB;QAChD,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG;gBACb,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC;gBAC1C,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE;gBACjB,sBAAsB,EAAE,GAAG,EAAE,CAAC,SAAS;gBACvC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;gBACjE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;gBACjB,UAAU,EAAE,GAAG,EAAE,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC3C,aAAa,EAAE,CAAC,KAAmB,EAAE,EAAE,CAAC,KAAK;gBAC7C,gBAAgB,EAAE,CAAC,QAAyB,EAAE,EAAE,CAAC,QAAQ;aAC1B,CAAC;YAEpC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAE7E,MAAM,MAAM,GAAyB;gBACjC,MAAM;gBACN,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,wBAAwB;aAC7E,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,gCAAgC,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAEM,gBAAgB;QACnB,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC;IAEM,kBAAkB;QACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAEM,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEM,gBAAgB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;CACJ;AA/VD,0CA+VC;AAED;;GAEG;AACH,MAAa,kBAAkB;IACX,EAAE,CAAS;IACX,MAAM,CAAiB;IACvB,KAAK,CAAgB;IACrB,KAAK,CAAgB;IACrB,MAAM,CAAiB;IAEvC,oDAAoD;IACnC,iBAAiB,GAAsB,EAAE,CAAC;IAC1C,aAAa,GAA8B,IAAI,GAAG,EAAE,CAAC;IACrD,cAAc,GAA2B,EAAE,CAAC;IAC5C,cAAc,GAAmC,IAAI,GAAG,EAAE,CAAC;IACpE,qBAAqB,GAAY,IAAI,CAAC;IAE9C,+BAA+B;IACvB,gBAAgB,GAA2B,IAAI,CAAC;IAChD,eAAe,CAAyB;IAExC,IAAI,CAAiB;IACrB,SAAS,GAA2B,IAAI,CAAC;IACzC,WAAW,GAAG,KAAK,CAAC;IACpB,eAAe,GAAwB,EAAE,CAAC;IAElD,mBAAmB;IACX,YAAY,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IACjD,YAAY,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IACjD,aAAa,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;IAChD,cAAc,GAAG,IAAI,MAAM,CAAC,YAAY,EAAqC,CAAC;IAC9E,eAAe,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IACpD,aAAa,GAAG,IAAI,MAAM,CAAC,YAAY,EAAS,CAAC;IACjD,mBAAmB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAiB,CAAC;IAEvD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;IACjC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;IACjC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IACnC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IACrC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;IACvC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IACnC,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;IAI/D,YAAY,SAAyB,EAAE;QACnC,IAAI,CAAC,EAAE,GAAG,uBAAuB,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QAE7F,IAAI,CAAC,MAAM,GAAG;YACV,IAAI,EAAE,8BAA8B;YACpC,GAAG,MAAM;SACZ,CAAC;QAEF,mCAAmC;QACnC,IAAI,CAAC,eAAe,GAAG,IAAI,sBAAsB,EAAE,CAAC;QACpD,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,CAAC,IAAI,GAAG,IAAI,cAAc,CAC1B;YACI,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM;YAClC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;SACxC,EACD,CAAC,QAAQ,EAAE,EAAE;YACT,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;QAC1C,CAAC,CACJ,CAAC;QAEF,4DAA4D;QAC5D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjC,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,KAAK,GAAG;YACT,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,KAAK;YACd,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;YAClD,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM;YACtC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;YAChE,WAAW,EAAE,EAAE;YACf,cAAc,EAAE,EAAE;YAClB,MAAM,EAAE,KAAK;SAChB,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG;YACT,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,CAAC;YACnB,mBAAmB,EAAE,CAAC;YACtB,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;SACjB,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG;YACV,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,aAAa,EAAE,IAAI,CAAC,aAAa;SACpC,CAAC;QAEF,kCAAkC;QAClC,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,8BAA8B;QAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YAC1C,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,8BAA8B;YACxD,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,QAAQ,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAC1B,CAAC;IACL,CAAC;IAEO,2BAA2B;QAC/B,0BAA0B;QAC1B,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,qCAAqC;YAC9C,MAAM,EAAE,yBAAyB;YACjC,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,UAAU;YACpB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,4BAA4B,OAAO,EAAE,EACrC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,wBAAwB;oBACjC,KAAK,EAAE,WAAW,OAAO,EAAE;oBAC3B,SAAS,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;iBACnC,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,KAAK;wBACL,OAAO,EAAE,YAAY,OAAO,aAAa;wBACzC,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,UAAU;qBACrB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,qCAAqC;YAC9C,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,SAAS;YACnB,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE;gBACzB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,iBAAiB,QAAQ,gBAAgB,EACzC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,qBAAqB;oBAC9B,KAAK,EAAE,gBAAgB,QAAQ,EAAE;oBACjC,SAAS,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;iBACtC,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,oDAAoD;YAC7D,MAAM,EAAE,cAAc;YACtB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,YAAY;YACtB,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE;gBACzB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,iBAAiB,QAAQ,gBAAgB,EACzC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,qBAAqB;oBAC9B,KAAK,EAAE,gBAAgB,QAAQ,EAAE;oBACjC,SAAS,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;iBACtC,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,yBAAyB;YAClC,MAAM,EAAE,iBAAiB;YACzB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,QAAQ;YAClB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,oBAAoB,QAAQ,EAAE,EAC9B,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,EAChE,SAAS,QAAQ,QAAQ,CAAC,CAAC;gBAC/B,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,4CAA4C;YACrD,MAAM,EAAE,cAAc;YACtB,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,UAAU;YACpB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,uBAAuB,KAAK,EAAE,EAC9B,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,qBAAqB;oBAC9B,KAAK,EAAE,OAAO,KAAK,EAAE;oBACrB,SAAS,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC;iBACjC,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;IAEO,uBAAuB;QAC3B,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC;YACvC,QAAQ,EAAE,YAAY;YACtB,WAAW,EAAE,sCAAsC;SACtD,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC;YACnC,QAAQ,EAAE,OAAO;YACjB,WAAW,EAAE,gDAAgD;SAChE,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;YACrC,QAAQ,EAAE,SAAS;YACnB,WAAW,EAAE,6CAA6C;SAC7D,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAClC,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,qCAAqC;SACrD,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC;YACnC,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,0CAA0C;SAC1D,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;YACrC,QAAQ,EAAE,YAAY;YACtB,WAAW,EAAE,mCAAmC;SACnD,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YACtC,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,2CAA2C;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC;YACnC,QAAQ,EAAE,OAAO;YACjB,WAAW,EAAE,kCAAkC;SAClD,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpC,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,yCAAyC;SACzD,CAAC,CAAC;IACP,CAAC;IAEO,WAAW,CAAC,OAAwB;QACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAEO,aAAa,CAAC,IAAkB;QACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAEO,mBAAmB,CAAC,QAAgB;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,CAAC;IAED,+BAA+B;IACvB,KAAK,CAAC,sBAAsB,CAAC,MAAc;QAC/C,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO;QAExC,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YAEtD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,iCAAiC;gBACjC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC9B,MAAM,QAAQ,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE,CAAC;oBAC1D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBAEH,0CAA0C;gBAC1C,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,SAAS,OAAO,CAAC,MAAM,wCAAwC,EAC/D,kBAAkB,EAClB,gBAAgB,EAChB,QAAQ,CACX,CAAC;gBAEF,QAAQ,WAAW,EAAE,CAAC;oBAClB,KAAK,kBAAkB;wBACnB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAClC,MAAM;oBACV,KAAK,gBAAgB;wBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;wBACrC,MAAM;gBACd,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAEO,wBAAwB,CAAC,MAAc;QAC3C,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC;oBACD,wDAAwD;oBACxD,MAAM,QAAQ,GAAG;wBACb,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC;wBAC1C,OAAO,EAAE,GAAG,EAAE,CAAC,MAAM;qBACD,CAAC;oBAEzB,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAEhD,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAA2B,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;oBAC9E,MAAM,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC/E,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACxD,CAAC;YACL,CAAC;QACL,CAAC;QAED,wDAAwD;QACxD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QAEtC,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QAEzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;YACtE,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjC,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,WAAW;YAC9C,MAAM,EAAE,MAAM;SACjB,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;YACtD,WAAW,EAAE,sCAAsC;SACtD,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAA4B;QACvD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEjC,4CAA4C;QAC5C,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAAyB;QAChD,IAAI,CAAC;YACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC9D,IAAI,OAAO,EAAE,CAAC;oBACV,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,gCAAgC,CAAC,CAAC;gBAC3E,CAAC;gBACD,OAAO,OAAO,CAAC;YACnB,CAAC;iBAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;gBAChG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,gCAAgC,CAAC,CAAC;gBACvE,OAAO,IAAI,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACJ,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAC5D,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED,6BAA6B;IACtB,gBAAgB;QACnB,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC;IAEM,kBAAkB;QACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,iBAAiB;QACpB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACtC,CAAC;IAEM,kBAAkB;QACrB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvC,CAAC;IAEM,oBAAoB;QACvB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAEM,gBAAgB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,uCAAuC;IAChC,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAEM,qBAAqB;QACxB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;QACnC,CAAC;IACL,CAAC;IAEM,sBAAsB;QACzB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;IAEM,wBAAwB;QAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC7E,CAAC;IAEM,wBAAwB;QAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACjF,CAAC;IAEM,0BAA0B;QAC7B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;QAC/C,CAAC;IACL,CAAC;IAED,kCAAkC;IAC3B,KAAK,CAAC,UAAU;QACnB,kEAAkE;QAClE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACrD,CAAC;IAEM,KAAK,CAAC,KAAK,CAAC,IAAY;QAC3B,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE5B,6CAA6C;QAC7C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAY;QAC7B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,OAK5C;QACG,6CAA6C;QAC7C,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,IAAI,KAAK,CAAC;QAC1C,MAAM,gBAAgB,GAAG,OAAO,EAAE,gBAAgB,CAAC;QAEnD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;QACjD,IAAI,YAAY,EAAE,CAAC;YACf,4CAA4C;YAC5C,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,YAAY,IAAI,gBAAgB,CAAC,EAAE,CAAC;gBAC9E,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8CAA8C,OAAO,CAAC,UAAU,kBAAkB,OAAO,CAAC,YAAY,sBAAsB,gBAAgB,aAAa,OAAO,EAAE,CAAC,CAAC;YAC7L,CAAC;YACD,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChD,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,OAAe,EAAE,OAKpD;QACG,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;QACjD,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YACvC,uDAAuD;YACnD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAEvD,yDAAyD;gBACzD,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/F,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,EAAE;wBACpD,OAAO;wBACP,OAAO,EAAE,OAAO,CAAC,OAAO;wBACxB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;wBAC1C,UAAU,EAAE,OAAO,CAAC,UAAU;wBAC9B,YAAY,EAAE,OAAO,CAAC,YAAY;qBACrC,CAAC,CAAC;gBACP,CAAC;gBAED,OAAO;oBACH,QAAQ,EAAE,CAAC;oBACX,MAAM;oBACN,OAAO;oBACP,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAChC,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC;YACN,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO;oBACH,QAAQ,EAAE,CAAC,CAAC;oBACZ,MAAM,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC9D,OAAO;oBACP,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAChC,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC;YACN,CAAC;QACT,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChD,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,IAAY,EAAE,IAAY;QAC1C,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAC7B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACrD,CAAC;IAEM,KAAK,CAAC,KAAK;QACd,MAAM,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACvC,CAAC;IAEM,KAAK,CAAC,KAAK;QACd,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,MAAc;QAClC,qCAAqC;QACrC,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,qCAAqC,CAAC,CAAC;IACvE,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACvC,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,IAAY;QACzC,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACnC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACrD,CAAC;IAEM,iBAAiB;QACpB,sDAAsD;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACnC,OAAO,KAAK,EAAE,cAAc,IAAI,EAAE,CAAC;IACvC,CAAC;IAEM,mBAAmB;QACtB,iFAAiF;QACjF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACpD,CAAC;IAEM,aAAa,CAAC,OAAgB;QACjC,iDAAiD;QACjD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,OAAO,CAAC,CAAC,CAAC,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9E,CAAC;IAEM,cAAc;QACjB,kBAAkB;QAClB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC7C,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO;QAE7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAEpB,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;IACvC,CAAC;CACJ;AA5oBD,gDA4oBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as os from 'os';\nimport * as path from 'path';\nimport { spawn, ChildProcess } from 'child_process';\nimport { EventEmitter } from 'events';\nimport { Logger } from '../logger';\n\n// Import code action provider integration\nimport {\n    codeAction<PERSON>anager,\n    TerminalCodeActionProvider\n} from './codeActionProvider';\n\n/**\n * Enhanced Pseudo Terminal with AI-powered code actions and visual indicators\n * Integrates with VS Code's terminal system and provides advanced features\n * Now fully integrated with the CodeActionProvider system for comprehensive error handling\n */\n\n/**\n * Core interfaces for terminal integration\n */\n\n// AI context interface for enhanced terminal analysis\nexport interface IAIContext {\n    modelId: string;\n    promptTemplate: string;\n    temperature: number;\n    maxTokens: number;\n    stop?: string[];\n}\n\n// Quick fix pattern interface for terminal actions\nexport interface QuickFixPattern {\n    pattern: RegExp;\n    action: string;\n    priority: number;\n    category: string;\n    fix: (match: RegExpMatchArray, document: vscode.TextDocument, range: vscode.Range) => vscode.CodeAction;\n}\n\n// Code action context for terminal operations\nexport interface CodeActionContext {\n    document: vscode.TextDocument;\n    range: vscode.Range;\n    diagnostics: readonly vscode.Diagnostic[];\n    trigger: 'manual' | 'auto' | 'onSave' | 'onType';\n    source: string;\n    only?: vscode.CodeActionKind;\n}\n\n// Code action result with metadata\nexport interface CodeActionResult {\n    action: vscode.CodeAction;\n    priority: number;\n    category: string;\n    confidence: number;\n    metadata: {\n        source: string;\n        trigger: string;\n        context: Record<string, unknown>;\n    };\n}\n\n// Quick fix icon definition\nexport interface QuickFixIcon {\n    name: string;\n    icon: vscode.ThemeIcon;\n    category: string;\n    description: string;\n}\n\nexport interface IAIToolPattern extends QuickFixPattern {\n    aiContext?: IAIContext;\n    contextProvider?: (match: RegExpMatchArray) => Promise<unknown>;\n    resultProcessor?: (result: unknown) => vscode.CodeAction | vscode.CodeAction[];\n}\n\nexport interface IToolTerminalContext {\n    session: InteractiveSession;\n    provider: TerminalActionProvider;\n    actionContext: {\n        toolId: string;\n        category: string;\n        priority: number;\n        confidence: number;\n    };\n}\n\nexport interface ITerminalToolEvents extends TerminalEvents {\n    onAIAction: vscode.Event<{\n        action: vscode.CodeAction;\n        context: unknown;\n        aiResponse?: unknown;\n    }>;\n    onPatternMatch: vscode.Event<{\n        pattern: IAIToolPattern;\n        matches: RegExpMatchArray;\n    }>;\n}\n\n/**\n * Terminal action provider that combines code actions and terminal capabilities.\n */\nexport class TerminalActionProvider extends TerminalCodeActionProvider {\n    private readonly _quickFixHistory: Set<string> = new Set();\n    private _outputBuffer: string = '';\n    private _outputFlushTimer: NodeJS.Timeout | null = null;\n    private _terminalDiagnostics: vscode.DiagnosticCollection;\n    private _aiPatterns: Map<string, IAIToolPattern> = new Map();\n    private _toolContext: Map<string, IToolTerminalContext> = new Map();\n    private readonly _onAIAction = new vscode.EventEmitter<{\n        action: vscode.CodeAction;\n        context: unknown;\n        aiResponse?: unknown;\n    }>();\n    private readonly _onPatternMatch = new vscode.EventEmitter<{\n        pattern: IAIToolPattern;\n        matches: RegExpMatchArray;\n    }>();     public readonly onAIAction = this._onAIAction.event;\n    public readonly onPatternMatch = this._onPatternMatch.event;\n\n    public registerToolContext(toolId: string, context: IToolTerminalContext): void {\n        this._toolContext.set(toolId, context);\n    }\n\n    public getToolContext(toolId: string): IToolTerminalContext | undefined {\n        return this._toolContext.get(toolId);\n    }\n\n    public updateToolContext(toolId: string, context: Partial<IToolTerminalContext>): void {\n        const existing = this._toolContext.get(toolId);\n        if (existing) {\n            this._toolContext.set(toolId, { ...existing, ...context });\n        }\n    }\n\n    constructor() {\n        super();\n        this._terminalDiagnostics = vscode.languages.createDiagnosticCollection('terminal');\n        this.initializeTerminalPatterns();\n        \n        // Register terminal-specific icons\n        this._registerIcon({\n            name: 'terminal-error',\n            icon: new vscode.ThemeIcon('error', new vscode.ThemeColor('terminalError.foreground')),\n            category: 'terminal-error',\n            description: 'Terminal error indicators'\n        });\n        \n        this._registerIcon({\n            name: 'terminal-warning',\n            icon: new vscode.ThemeIcon('warning', new vscode.ThemeColor('terminalWarning.foreground')),\n            category: 'terminal-warning',\n            description: 'Terminal warning indicators'\n        });\n        \n        this._registerIcon({\n            name: 'terminal-success',\n            icon: new vscode.ThemeIcon('check', new vscode.ThemeColor('terminalSuccess.foreground')),\n            category: 'terminal-success',\n            description: 'Terminal success indicators'\n        });\n    }\n\n    private initializeTerminalPatterns(): void {\n        // Register standard terminal patterns\n        this._registerStandardPatterns();\n        // Register enhanced patterns\n        this._registerEnhancedPatterns();\n    }\n\n    private _registerStandardPatterns(): void {\n        // Command not found\n        this.registerPattern({\n            pattern: /command not found:\\s+(.+)$/m,\n            action: 'install-missing-command',\n            priority: 100,\n            category: 'terminal-error',\n            fix: (match, doc, range) => {\n                const cmd = match[1];\n                const action = new vscode.CodeAction(\n                    `${this._getIconForCategory('error', 'terminal')} Install missing command: ${cmd}`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.terminal.installCommand',\n                    title: `Install ${cmd}`,\n                    arguments: [cmd]\n                };\n                action.diagnostics = [{\n                    message: `Command '${cmd}' not found`,\n                    range,\n                    severity: vscode.DiagnosticSeverity.Error,\n                    source: 'terminal'\n                }];\n                return action;\n            }\n        });\n\n        // Permission denied\n        this.registerPattern({\n            pattern: /permission denied|EACCES/i,\n            action: 'fix-permissions',\n            priority: 90,\n            category: 'security',\n            fix: (match, doc, range) => {\n                const action = new vscode.CodeAction(\n                    `${this._getIconForCategory('security', 'terminal')} Fix permission issues`,\n                    vscode.CodeActionKind.QuickFix.append('security')\n                );\n                action.command = {\n                    command: 'codessa.terminal.fixPermissions',\n                    title: 'Fix permissions',\n                    arguments: [match[0], doc.uri.toString()]\n                };\n                action.diagnostics = [{\n                    message: `Permission error: ${match[0]}`,\n                    range,\n                    severity: vscode.DiagnosticSeverity.Error,\n                    source: 'terminal'\n                }];\n                return action;\n            }\n        });\n\n        // Network issues\n        this.registerPattern({\n            pattern: /ECONNREFUSED|ETIMEDOUT|network error/i,\n            action: 'fix-network',\n            priority: 85,\n            category: 'terminal-warning',\n            fix: (match, doc, range) => {\n                const action = new vscode.CodeAction(\n                    `${this._getIconForCategory('warning', 'terminal')} Fix network connection`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.terminal.fixNetwork',\n                    title: 'Fix network',\n                    arguments: [match[0], doc.uri.toString()]\n                };\n                action.diagnostics = [{\n                    message: `Network error: ${match[0]}`,\n                    range,\n                    severity: vscode.DiagnosticSeverity.Warning,\n                    source: 'terminal'\n                }];\n                return action;\n            }\n        });\n    }\n\n    private _registerEnhancedPatterns(): void {\n        // Build errors\n        this.registerPattern({\n            pattern: /error TS\\d+:|error C\\d+:|compilation failed/i,\n            action: 'fix-build-error',\n            priority: 95,\n            category: 'debug',\n            fix: (match, doc, range) => {\n                const action = new vscode.CodeAction(\n                    `${this._getIconForCategory('error', 'terminal')} Fix build error`,\n                    vscode.CodeActionKind.QuickFix.append('debug')\n                );\n                action.command = {\n                    command: 'codessa.terminal.fixBuildError',\n                    title: 'Fix build error',\n                    arguments: [match[0], doc.uri.toString()]\n                };\n                action.diagnostics = [{\n                    message: `Build error: ${match[0]}`,\n                    range,\n                    severity: vscode.DiagnosticSeverity.Error,\n                    source: 'terminal',\n                    relatedInformation: [{\n                        location: new vscode.Location(doc.uri, range),\n                        message: 'Build process failed'\n                    }]\n                }];\n                return action;\n            }\n        });\n\n        // Missing dependencies\n        this.registerPattern({\n            pattern: /cannot find module|module not found|package .* is not installed/i,\n            action: 'install-dependency',\n            priority: 95,\n            category: 'dependency',\n            fix: (match, doc, range) => {\n                const moduleName = match[0].match(/['\"]([^'\"]+)['\"]/)?.[1] || 'package';\n                const action = new vscode.CodeAction(\n                    `${this._getIconForCategory('package', 'terminal')} Install missing dependency: ${moduleName}`,\n                    vscode.CodeActionKind.QuickFix.append('dependency')\n                );\n                action.command = {\n                    command: 'codessa.terminal.installDependency',\n                    title: 'Install dependency',\n                    arguments: [match[0], doc.uri.toString()]\n                };\n                action.diagnostics = [{\n                    message: `Missing dependency: ${moduleName}`,\n                    range,\n                    severity: vscode.DiagnosticSeverity.Warning,\n                    source: 'terminal',\n                    tags: [vscode.DiagnosticTag.Unnecessary],\n                    relatedInformation: [{\n                        location: new vscode.Location(doc.uri, range),\n                        message: `Run 'npm install ${moduleName}' to fix this issue`\n                    }]\n                }];\n                return action;\n            }\n        });\n    }\n\n    public async analyzeOutput(output: string): Promise<vscode.CodeAction[]> {\n        this._appendOutput(output);\n        \n        class TerminalDocument implements Partial<vscode.TextDocument> {\n            uri = vscode.Uri.parse('terminal://output');\n            languageId = 'terminal';\n            version = 1;\n            lineCount = output.split('\\n').length;\n            \n            getText(range?: vscode.Range): string {\n                if (!range) return output;\n                const lines = output.split('\\n');\n                if (range.start.line === range.end.line) {\n                    return lines[range.start.line].substring(range.start.character, range.end.character);\n                }\n                const selectedLines = lines.slice(range.start.line, range.end.line + 1);\n                selectedLines[0] = selectedLines[0].substring(range.start.character);\n                const lastIndex = selectedLines.length - 1;\n                selectedLines[lastIndex] = selectedLines[lastIndex].substring(0, range.end.character);\n                return selectedLines.join('\\n');\n            }\n            \n            lineAt(lineOrPosition: number | vscode.Position): vscode.TextLine {\n                const line = typeof lineOrPosition === 'number' ? lineOrPosition : lineOrPosition.line;\n                const lines = output.split('\\n');\n                const text = lines[line] || '';\n                return {\n                    lineNumber: line,\n                    text,\n                    range: new vscode.Range(line, 0, line, text.length),\n                    rangeIncludingLineBreak: new vscode.Range(line, 0, line, text.length + 1),\n                    firstNonWhitespaceCharacterIndex: text.search(/\\S/),\n                    isEmptyOrWhitespace: text.trim().length === 0\n                };\n            }\n        }\n        \n        const range = new vscode.Range(0, 0, output.split('\\n').length - 1, 0);\n        const context: CodeActionContext = {\n            document: new TerminalDocument() as vscode.TextDocument,\n            range,\n            diagnostics: [],\n            trigger: 'auto',\n            source: 'terminal'\n        };\n\n        // Get standard quick fixes\n        const quickFixes = await this._getQuickFixes();\n        \n        // Get pattern-based actions from base class\n        const patternActions = await this._getPatternBasedActions(context);\n        \n        // Get context-aware actions\n        const contextActions = this._getContextAwareActions(context);\n        \n        // Combine all actions with proper icons and metadata\n        const allActions = [...quickFixes, ...patternActions, ...contextActions]\n            .map(action => {\n                const category = action.kind?.value?.includes('terminal') ? 'terminal' : 'command';\n                action.title = `${this._getIconForCategory(category)} ${action.title}`;\n                return action;\n            });\n\n        // Add diagnostics for relevant actions\n        this._updateDiagnostics(allActions);\n\n        return allActions;\n    }\n\n    private _appendOutput(output: string): void {\n        // Process output for visual indicators\n        const processedOutput = this._processOutputWithIndicators(output);\n        this._outputBuffer += processedOutput;\n        \n        if (this._outputFlushTimer) {\n            clearTimeout(this._outputFlushTimer);\n        }\n        \n        this._outputFlushTimer = setTimeout(() => this._flushOutputBuffer(), 100);\n    }\n\n    private _processOutputWithIndicators(output: string): string {\n        // Add visual indicators based on content\n        const lines = output.split('\\n');\n        return lines.map(line => {\n            if (line.match(/error|failed|exception/i)) {\n                return `${this._getIconForCategory('error', 'terminal')} ${line}`;\n            }\n            if (line.match(/warning|warn/i)) {\n                return `${this._getIconForCategory('warning', 'terminal')} ${line}`;\n            }\n            if (line.match(/success|completed|done/i)) {\n                return `${this._getIconForCategory('success', 'terminal')} ${line}`;\n            }\n            if (line.match(/info|note/i)) {\n                return `${this._getIconForCategory('info', 'terminal')} ${line}`;\n            }\n            return line;\n        }).join('\\n');\n    }\n\n    private _flushOutputBuffer(): void {\n        this._outputBuffer = '';\n        this._outputFlushTimer = null;\n    }\n\n    private async _getQuickFixes(): Promise<vscode.CodeAction[]> {\n        const actions: vscode.CodeAction[] = [];\n        const uniqueActions = new Set<string>();\n        const processPatterns: Promise<void>[] = [];\n\n        for (const pattern of this._quickFixPatterns) {\n            const matches = this._outputBuffer.match(pattern.pattern);\n            if (matches && !this._quickFixHistory.has(pattern.action)) {\n                try {\n                    if (this.isAIPattern(pattern)) {\n                        // Process AI patterns asynchronously\n                        processPatterns.push(\n                            this.processAIPattern(pattern, matches).then(aiActions => {\n                                for (const action of aiActions) {\n                                    const actionKey = `${action.title}-${action.kind?.value}`;\n                                    if (!uniqueActions.has(actionKey)) {\n                                        actions.push(action);\n                                        uniqueActions.add(actionKey);\n                                        this._quickFixHistory.add(pattern.action);\n                                        this._addDiagnostic(pattern, matches);\n                                    }\n                                }\n                            })\n                        );\n                    } else {\n                        // Process standard patterns immediately\n                        const action = this._createQuickFix(pattern, matches);\n                        const actionKey = `${action.title}-${action.kind?.value}`;\n                        \n                        if (!uniqueActions.has(actionKey)) {\n                            actions.push(action);\n                            uniqueActions.add(actionKey);\n                            this._quickFixHistory.add(pattern.action);\n                            this._addDiagnostic(pattern, matches);\n                        }\n                    }\n                } catch (error) {\n                    console.error(`Error creating quick fix for pattern ${pattern.action}:`, error);\n                }\n            }\n        }\n\n        // Wait for all AI pattern processing to complete\n        if (processPatterns.length > 0) {\n            await Promise.all(processPatterns);\n        }\n\n        return actions;\n    }\n\n    private _addDiagnostic(pattern: QuickFixPattern, matches: RegExpMatchArray): void {\n        const diagnostic = new vscode.Diagnostic(\n            new vscode.Range(0, 0, 0, 0),\n            matches[0],\n            this._getDiagnosticSeverity(pattern.category)\n        );\n        diagnostic.source = 'terminal';\n        diagnostic.code = pattern.action;\n\n        this._terminalDiagnostics.set(\n            vscode.Uri.parse('terminal://output'),\n            [diagnostic]\n        );\n    }\n\n    private _getDiagnosticSeverity(category: string): vscode.DiagnosticSeverity {\n        switch (category.toLowerCase()) {\n            case 'error':\n            case 'security':\n                return vscode.DiagnosticSeverity.Error;\n            case 'warning':\n            case 'dependency':\n                return vscode.DiagnosticSeverity.Warning;\n            case 'info':\n            case 'terminal':\n                return vscode.DiagnosticSeverity.Information;\n            default:\n                return vscode.DiagnosticSeverity.Hint;\n        }\n    }\n\n    async provideCodeActions(\n        document: vscode.TextDocument,\n        range: vscode.Range,\n        context: vscode.CodeActionContext,\n        token: vscode.CancellationToken\n    ): Promise<vscode.CodeAction[]> {\n        // Check for cancellation\n        if (token.isCancellationRequested) {\n            return [];\n        }\n\n        // First check if this is a terminal document and has diagnostic context\n        if (document.uri.scheme === 'terminal' && context.diagnostics.length > 0) {\n            // Focus our analysis on the specific range where the code action was requested\n            // Use the relevant text from the specific range for more targeted analysis\n            const relevantText = document.getText(range);\n            this._appendOutput(relevantText);\n            const actions = await this._getQuickFixes();\n\n            // Filter and sort actions based on diagnostics\n            return actions.filter(action => {\n                // Only include actions that are relevant to the current diagnostics\n                return context.diagnostics.some(diag => {\n                    return diag.range.intersection(range) && \n                           action.diagnostics?.some(actionDiag => \n                               actionDiag.message === diag.message);\n                });\n            });\n        }\n\n        // For non-terminal documents, let the parent handle it\n        return [];\n    }\n\n    private _createQuickFix(pattern: QuickFixPattern, matches: RegExpMatchArray): vscode.CodeAction {\n        const dummyDocument = { uri: vscode.Uri.parse('terminal://output') } as vscode.TextDocument;\n        const dummyRange = new vscode.Range(0, 0, 0, 0);\n        \n        const action = pattern.fix(matches, dummyDocument, dummyRange);\n        action.title = `${this._getIconForCategory(pattern.category)} ${action.title}`;\n        action.kind = this._getCodeActionKindForCategory(pattern.category);\n        \n        return action;\n    }\n\n    private _updateDiagnostics(actions: vscode.CodeAction[]): void {\n        const diagnostics: vscode.Diagnostic[] = [];\n        // Remove unused lines variable\n\n        actions.forEach(action => {\n            if (action.diagnostics?.length) {\n                action.diagnostics.forEach(diag => {\n                    // Convert line-based range to position in full output\n                    const startPos = diag.range.start;\n                    const endPos = diag.range.end;\n                    \n                    const diagnostic = new vscode.Diagnostic(\n                        new vscode.Range(startPos, endPos),\n                        diag.message,\n                        diag.severity\n                    );\n                    diagnostic.source = 'terminal';\n                    diagnostic.code = action.kind?.value;\n                    diagnostics.push(diagnostic);\n                });\n            }\n        });\n\n        this._terminalDiagnostics.set(\n            vscode.Uri.parse('terminal://output'),\n            diagnostics\n        );\n    }\n\n    private _getCodeActionKindForCategory(category: string): vscode.CodeActionKind {\n        switch (category.toLowerCase()) {\n            case 'terminal':\n                return vscode.CodeActionKind.QuickFix;\n            case 'security':\n                return vscode.CodeActionKind.QuickFix.append('security');\n            case 'debug':\n                return vscode.CodeActionKind.QuickFix.append('debug');\n            case 'dependency':\n                return vscode.CodeActionKind.QuickFix.append('dependency');\n            case 'refactor':\n                return vscode.CodeActionKind.RefactorExtract;\n            default:\n                return vscode.CodeActionKind.QuickFix;\n        }\n    }\n\n    public registerPattern(pattern: QuickFixPattern | IAIToolPattern): void {\n        if (this.isAIPattern(pattern)) {\n            this._aiPatterns.set(pattern.action, pattern);\n        }\n        this._quickFixPatterns.push(pattern);\n    }\n\n    private isAIPattern(pattern: QuickFixPattern | IAIToolPattern): pattern is IAIToolPattern {\n        return 'aiContext' in pattern || 'contextProvider' in pattern;\n    }\n\n    public markPatternUsed(patternId: string): void {\n        this._quickFixHistory.add(patternId);\n    }\n\n    public clearHistory(): void {\n        this._quickFixHistory.clear();\n        this._terminalDiagnostics.clear();\n    }\n\n    public async processAIPattern(\n        pattern: IAIToolPattern, \n        matches: RegExpMatchArray\n    ): Promise<vscode.CodeAction[]> {\n        this._onPatternMatch.fire({ pattern, matches });\n\n        let context: unknown;\n        if (pattern.contextProvider) {\n            context = await pattern.contextProvider(matches);\n        }\n\n        if (!pattern.aiContext) {\n            return [];\n        }\n\n        // Here you would integrate with your AI service\n        // This is just a placeholder for the structure\n        const aiResponse = await this.processWithAI(pattern.aiContext, context);\n\n        if (pattern.resultProcessor) {\n            const actions = pattern.resultProcessor(aiResponse);\n            if (Array.isArray(actions)) {\n                actions.forEach(action => {\n                    this._onAIAction.fire({ action, context, aiResponse });\n                });\n                return actions;\n            } else {\n                this._onAIAction.fire({ action: actions, context, aiResponse });\n                return [actions];\n            }\n        }\n\n        return [];\n    }\n\n    private async processWithAI(aiContext: IAIContext, context: unknown): Promise<unknown> {\n        // Implement your AI processing logic here\n        // This is where you would integrate with your AI service\n        const prompt = this.buildPrompt(aiContext.promptTemplate, context);\n        return Promise.resolve({\n            suggestion: `${prompt} - AI response`,\n            confidence: 0.95,\n            modelId: aiContext.modelId,\n            context\n        });\n    }\n\n    private buildPrompt(template: string, context: unknown): string {\n        // Convert context to string representation\n        const contextStr = JSON.stringify(context, null, 2);\n        return template.replace('{{context}}', contextStr);\n    }\n\n    /**\n     * Enhanced terminal integration methods\n     */\n    public analyzeTerminalOutput(output: string): vscode.CodeAction[] {\n        // Use the terminal code action provider to analyze output\n        this._appendOutput(output);\n        // Return synchronous quick fixes\n        const actions: vscode.CodeAction[] = [];\n        this._quickFixPatterns.forEach(pattern => {\n            const matches = output.match(pattern.pattern);\n            if (matches) {\n                try {\n                    const action = this._createQuickFix(pattern, matches);\n                    actions.push(action);\n                } catch (error) {\n                    Logger.instance.error(`Error creating quick fix for pattern ${pattern.action}:`, error);\n                }\n            }\n        });\n        return actions;\n    }\n\n    public async applyQuickFix(action: vscode.CodeAction): Promise<boolean> {\n        try {\n            if (action.edit) {\n                const success = await vscode.workspace.applyEdit(action.edit);\n                if (success) {\n                    this.markPatternUsed(action.kind?.value || 'unknown');\n                }\n                return success;\n            } else if (action.command) {\n                await vscode.commands.executeCommand(action.command.command, ...action.command.arguments || []);\n                this.markPatternUsed(action.kind?.value || 'unknown');\n                return true;\n            }\n            return false;\n        } catch (error) {\n            Logger.instance.error('Failed to apply quick fix:', error);\n            return false;\n        }\n    }\n\n    public getCodeActionManager(): typeof codeActionManager {\n        return codeActionManager;\n    }\n\n    public dispose(): void {\n        this._terminalDiagnostics.dispose();\n        if (this._outputFlushTimer) {\n            clearTimeout(this._outputFlushTimer);\n        }\n    }\n}\n\nexport interface QuickFixIcon {\n    name: string;\n    icon: vscode.ThemeIcon;\n    category: string;\n    description: string;\n}\n\nexport interface TerminalActionResult {\n    action: vscode.CodeAction;\n    applied: boolean;\n    timestamp: Date;\n    output: string;\n}\n\n/**\n * Terminal configuration options\n */\nexport interface TerminalConfig {\n    name?: string;\n    shell?: string;\n    args?: string[];\n    cwd?: string;\n    env?: { [key: string]: string | undefined };\n    dimensions?: { columns: number; rows: number };\n    enableColorSupport?: boolean;\n    enableUnicodeSupport?: boolean;\n    enableBracketedPaste?: boolean;\n    scrollback?: number;\n    fontSize?: number;\n    fontFamily?: string;\n    theme?: 'light' | 'dark' | 'system';\n    hideFromUser?: boolean;\n    isTransient?: boolean;\n    enableCommandHistory?: boolean;\n    maxCommandHistory?: number;\n    enableAutoComplete?: boolean;\n    customPrompt?: string;\n    workingDirectoryTracking?: boolean;\n}\n\n/**\n * Result of command execution with detailed information\n */\nexport interface CommandResult {\n    command: string;\n    output: string;\n    exitCode?: number;\n    duration: number;\n    success: boolean;\n    error?: string;\n    background?: boolean;\n    completedAt: Date;\n}\n\n/**\n * Terminal state information\n */\nexport interface TerminalState {\n    isConnected: boolean;\n    isReady: boolean;\n    currentDirectory: string;\n    shellType: string;\n    dimensions: { columns: number; rows: number };\n    lastCommand: string;\n    commandHistory: string[];\n    isBusy: boolean;\n    exitCode?: number;\n}\n\n/**\n * Enhanced terminal statistics for monitoring\n */\nexport interface TerminalStats {\n    totalCommands: number;\n    totalOutputBytes: number;\n    averageResponseTime: number;\n    uptimeSeconds: number;\n    errorCount: number;\n    resizeCount: number;\n}\n\n/**\n * Advanced terminal event types\n */\nexport interface TerminalEvents {\n    onData: vscode.Event<string>;\n    onExit: vscode.Event<number>;\n    onReady: vscode.Event<void>;\n    onResize: vscode.Event<{ columns: number; rows: number }>;\n    onCommand: vscode.Event<string>;\n    onError: vscode.Event<Error>;\n    onStateChange: vscode.Event<TerminalState>;\n}\n\n/**\n * Interface for a terminal process with advanced capabilities\n */\nexport interface ITerminalProcess extends vscode.Disposable {\n    readonly id: string;\n    readonly config: TerminalConfig;\n    readonly state: TerminalState;\n    readonly stats: TerminalStats;\n    readonly events: TerminalEvents;\n\n    initialize(): Promise<void>;\n    write(data: string): Promise<void>;\n    writeLn(data: string): Promise<void>;\n    executeCommand(command: string, options?: {\n        timeout?: number;\n        expectPrompt?: boolean;\n        background?: boolean;\n        workingDirectory?: string;\n    }): Promise<string>;\n    executeCommandAdvanced(command: string, options?: {\n        timeout?: number;\n        expectPrompt?: boolean;\n        background?: boolean;\n        workingDirectory?: string;\n    }): Promise<CommandResult>;\n    resize(cols: number, rows: number): Promise<void>;\n    clear(): Promise<void>;\n    reset(): Promise<void>;\n    sendSignal(signal: string): Promise<void>;\n    getWorkingDirectory(): Promise<string>;\n    setWorkingDirectory(path: string): Promise<void>;\n    getCommandHistory(): string[];\n    clearCommandHistory(): void;\n    enableLogging(logPath?: string): void;\n    disableLogging(): void;\n}\n\n/**\n * Terminal process with advanced features extending EventEmitter\n */\nexport class TerminalProcess extends EventEmitter implements ITerminalProcess {\n    public readonly id: string;\n    public readonly config: TerminalConfig;\n    public readonly state: TerminalState;\n    public readonly stats: TerminalStats;\n    public readonly events: TerminalEvents;\n\n    private _terminal: vscode.Terminal | null = null;\n    private _isDisposed = false;\n    private _isInitialized = false;\n    private _commandHistory: string[] = [];\n    private _outputBuffer = '';\n    private _logPath?: string;\n    private _logStream?: vscode.OutputChannel;\n    private _startupTime = Date.now();\n    private _lastCommandTime = 0;\n    private _responseTimes: number[] = [];\n    private _terminalCodeActionProvider: TerminalCodeActionProvider;\n\n    // Event emitters\n    private _onData = new vscode.EventEmitter<string>();\n    private _onExit = new vscode.EventEmitter<number>();\n    private _onReady = new vscode.EventEmitter<void>();\n    private _onResize = new vscode.EventEmitter<{ columns: number; rows: number }>();\n    private _onCommand = new vscode.EventEmitter<string>();\n    private _onError = new vscode.EventEmitter<Error>();\n    private _onStateChange = new vscode.EventEmitter<TerminalState>();\n\n    // Private emitter references for internal use\n    private _dataEmitter: vscode.EventEmitter<string>;\n    private _exitEmitter: vscode.EventEmitter<number>;\n    private _readyEmitter: vscode.EventEmitter<void>;\n    private _resizeEmitter: vscode.EventEmitter<{ columns: number; rows: number }>;\n    private _commandEmitter: vscode.EventEmitter<string>;\n    private _errorEmitter: vscode.EventEmitter<Error>;\n    private _stateChangeEmitter: vscode.EventEmitter<TerminalState>;\n\n    // Resource management\n    private _disposables: vscode.Disposable[] = [];\n    private _writeQueue: string[] = [];\n    private _isWriting = false;\n    private _outputFlushTimer: NodeJS.Timeout | null = null;\n\n    constructor(config: TerminalConfig = {}) {\n        super(); // Call EventEmitter constructor first\n\n        this.id = `terminal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;\n\n        // Initialize terminal code action provider\n        this._terminalCodeActionProvider = new TerminalCodeActionProvider();\n\n        // Merge with defaults\n        this.config = {\n            name: 'Enhanced Terminal',\n            shell: this._resolveShellPath(config.shell || this._detectShell()),\n            args: config.args || this._getShellArgs(),\n            cwd: process.cwd(),\n            env: {},\n            dimensions: { columns: 120, rows: 30 },\n            enableColorSupport: true,\n            enableUnicodeSupport: true,\n            enableBracketedPaste: true,\n            scrollback: 1000,\n            enableCommandHistory: true,\n            maxCommandHistory: 1000,\n            enableAutoComplete: false,\n            workingDirectoryTracking: true,\n            ...config\n        };\n\n        // Initialize state\n        this.state = {\n            isConnected: false,\n            isReady: false,\n            currentDirectory: this.config.cwd || process.cwd(),\n            shellType: this.config.shell || this._detectShell(),\n            dimensions: this.config.dimensions || { columns: 120, rows: 30 },\n            lastCommand: '',\n            commandHistory: [],\n            isBusy: false\n        };\n\n        // Initialize stats\n        this.stats = {\n            totalCommands: 0,\n            totalOutputBytes: 0,\n            averageResponseTime: 0,\n            uptimeSeconds: 0,\n            errorCount: 0,\n            resizeCount: 0\n        };\n\n        // Initialize events\n        this.events = {\n            onData: this._onData.event,\n            onExit: this._onExit.event,\n            onReady: this._onReady.event,\n            onResize: this._onResize.event,\n            onCommand: this._onCommand.event,\n            onError: this._onError.event,\n            onStateChange: this._onStateChange.event\n        };\n\n        // Store emitter references for internal use\n        this._dataEmitter = this._onData;\n        this._exitEmitter = this._onExit;\n        this._readyEmitter = this._onReady;\n        this._resizeEmitter = this._onResize;\n        this._commandEmitter = this._onCommand;\n        this._errorEmitter = this._onError;\n        this._stateChangeEmitter = this._onStateChange;\n\n        this._disposables.push(\n            this._onData,\n            this._onExit,\n            this._onReady,\n            this._onResize,\n            this._onCommand,\n            this._onError,\n            this._onStateChange\n        );\n    }\n\n    private _detectShell(): string {\n        const platform = os.platform();\n        if (platform === 'win32') {\n            return process.env.ComSpec || path.join(process.env.SystemRoot || 'C:\\\\Windows', 'System32', 'cmd.exe');\n        } else if (platform === 'darwin') {\n            return process.env.SHELL || '/bin/zsh';\n        } else {\n            return process.env.SHELL || '/bin/bash';\n        }\n    }\n\n    private _getShellArgs(): string[] {\n        const platform = os.platform();\n        const shell = this.config.shell || this._detectShell();\n\n        if (platform === 'win32') {\n            if (shell.includes('powershell') || shell.includes('pwsh')) {\n                return ['-NoExit', '-Command', ''];\n            } else if (shell.includes('cmd')) {\n                return ['/k'];\n            }\n        } else {\n            // Unix-like systems\n            return ['-i']; // Interactive mode\n        }\n\n        return [];\n    }\n\n    private _resolveShellPath(shell: string): string {\n        if (path.isAbsolute(shell)) {\n            return shell;\n        }\n\n        // Try to resolve shell from PATH\n        const pathEnv = process.env.PATH || '';\n        const pathSeparator = os.platform() === 'win32' ? ';' : ':';\n        const paths = pathEnv.split(pathSeparator);\n\n        for (const dir of paths) {\n            const fullPath = path.join(dir, shell);\n            try {\n                // In a real implementation, you'd check if file exists and is executable\n                return fullPath;\n            } catch {\n                continue;\n            }\n        }\n\n        return shell; // Fallback to original\n    }\n\n    public async initialize(): Promise<void> {\n        if (this._isInitialized || this._isDisposed) {\n            return;\n        }\n\n        try {\n            // Create VS Code terminal with enhanced options\n            this._terminal = vscode.window.createTerminal({\n                name: this.config.name || 'Enhanced Terminal',\n                shellPath: this.config.shell || this._detectShell(),\n                cwd: this.config.cwd || process.cwd(),\n                env: {\n                    ...process.env,\n                    ...this.config.env,\n                    TERM: 'xterm-256color',\n                    COLORTERM: this.config.enableColorSupport ? 'truecolor' : undefined,\n                    LANG: this.config.enableUnicodeSupport ? 'en_US.UTF-8' : undefined\n                },\n                isTransient: this.config.isTransient ?? true,\n                hideFromUser: this.config.hideFromUser ?? false,\n                iconPath: new vscode.ThemeIcon('terminal')\n            });\n\n            // Set up event listeners\n            const closeListener = vscode.window.onDidCloseTerminal(terminal => {\n                if (terminal === this._terminal) {\n                    this._handleExit(0);\n                }\n            });\n\n            this._disposables.push(closeListener);\n\n            // Show terminal if not hidden\n            if (!this.config.hideFromUser) {\n                this._terminal.show(true);\n            }\n\n            // Initialize terminal with enhanced settings\n            await this._initializeTerminal();\n\n            this._isInitialized = true;\n            this.state.isConnected = true;\n            this.state.isReady = true;\n\n            this._onReady.fire();\n            this._onStateChange.fire({ ...this.state });\n\n        } catch (error) {\n            const err = error instanceof Error ? error : new Error(String(error));\n            this._onError.fire(err);\n            this.stats.errorCount++;\n            throw err;\n        }\n    }\n\n    private async _initializeTerminal(): Promise<void> {\n        if (!this._terminal) return;\n\n        const initCommands = [\n            'clear',  // Clear screen\n            '\\x1b[2J\\x1b[0f',  // Alternative clear\n            '\\x1b[?25h',  // Show cursor\n            '\\x1b[?2004h',  // Enable bracketed paste if supported\n            '\\x1b[?1049h',  // Alternate screen buffer\n            'echo \"Enhanced Terminal Ready\"',  // Ready indicator\n            'pwd',  // Get current directory\n            'echo \"\"'  // New line\n        ];\n\n        for (const cmd of initCommands) {\n            await this._sendTextToTerminal(cmd, false);\n            await this._delay(50); // Small delay between commands\n        }\n\n        // Set initial dimensions\n        if (this.config.dimensions) {\n            await this.resize(this.config.dimensions.columns, this.config.dimensions.rows);\n        }\n    }\n\n    private _delay(ms: number): Promise<void> {\n        return new Promise(resolve => setTimeout(resolve, ms));\n    }\n\n    public async write(data: string): Promise<void> {\n        if (!this._isInitialized || this._isDisposed || !this._terminal) {\n            throw new Error('Terminal not initialized or disposed');\n        }\n\n        // Process input for special characters and commands\n        const processedData = this._processInput(data);\n\n        this._writeQueue.push(processedData);\n        await this._processWriteQueue();\n\n        if (this.config.enableCommandHistory && data.trim()) {\n            this._addToCommandHistory(data.trim());\n        }\n    }\n\n    public async writeLn(data: string): Promise<void> {\n        await this.write(data + '\\n');\n    }\n\n    public async executeCommand(command: string, options?: {\n        timeout?: number;\n        expectPrompt?: boolean;\n        background?: boolean;\n        workingDirectory?: string;\n    }): Promise<string> {\n        const result = await this.executeCommandAdvanced(command, options);\n        return result.output;\n    }\n\n    public async executeCommandAdvanced(command: string, options?: {\n        timeout?: number;\n        expectPrompt?: boolean;\n        background?: boolean;\n        workingDirectory?: string;\n    }): Promise<CommandResult> {\n        const startTime = Date.now();\n        this._lastCommandTime = startTime;\n        this.state.isBusy = true;\n        this.state.lastCommand = command;\n        this._onStateChange.fire({ ...this.state });\n\n        const config = {\n            timeout: options?.timeout ?? 30000, // 30 second default timeout\n            expectPrompt: options?.expectPrompt ?? true,\n            background: options?.background ?? false,\n            workingDirectory: options?.workingDirectory\n        };\n\n        try {\n            // Change working directory if specified\n            if (config.workingDirectory && config.workingDirectory !== this.state.currentDirectory) {\n                await this.setWorkingDirectory(config.workingDirectory);\n            }\n\n            // Send command to terminal\n            await this.writeLn(command);\n            this._onCommand.fire(command);\n            this.stats.totalCommands++;\n\n            // Use sophisticated command completion detection\n            const result = await this._waitForCommandCompletion(command, config);\n\n            const responseTime = Date.now() - startTime;\n            this._responseTimes.push(responseTime);\n            this._updateAverageResponseTime();\n            this.stats.averageResponseTime = this._calculateAverageResponseTime(responseTime);\n\n            return result;\n\n        } finally {\n            this.state.isBusy = false;\n            this._onStateChange.fire({ ...this.state });\n        }\n    }\n\n    private async _waitForCommandCompletion(command: string, config: {\n        timeout: number;\n        expectPrompt: boolean;\n        background: boolean;\n    }): Promise<CommandResult> {\n        return new Promise((resolve, reject) => {\n            const startTime = Date.now();\n            let outputBuffer = '';\n            let commandCompleted = false;\n\n            // Sophisticated shell prompt detection patterns\n            const shellPrompts = [\n                // Bash/Zsh prompts\n                /^\\$ /, /^bash-\\d+\\.\\d+\\$ /, /^zsh-\\d+\\.\\d+\\$ /,\n                /^[a-zA-Z0-9_]+@[a-zA-Z0-9_]+:[^$]*\\$ /,\n                /^➜ /, // Oh My Zsh\n                /^❯ /, // Starship\n                /^→ /, // Pure prompt\n\n                // Windows prompts\n                /^[A-Z]:\\\\.*>/,\n                /^PS [A-Z]:\\\\.*>/,\n\n                // Fish shell\n                /^[a-zA-Z0-9_]+@[a-zA-Z0-9_]+ [^$]*\\$ /,\n\n                // Generic prompts\n                /^> /, /^# /, /^\\[.*\\]\\$ /,\n            ];\n\n            // Error pattern detection\n            const errorPatterns = [\n                /^bash: .*: command not found/,\n                /^zsh: command not found: .*/,\n                /'.*': command not found/,\n                /^Error: /, /^error: /, /^ERROR: /,\n                /^Command failed: /,\n            ];\n\n            // Background job detection\n            const backgroundPatterns = [\n                /^\\[\\d+\\]\\s+\\d+$/, // [1] 1234\n                /^\\[\\d+\\]\\s+\\+/, // [1] + running\n                /^\\[\\d+\\]\\s+-/, // [1] - stopped\n            ];\n\n            const checkCompletion = (data: string) => {\n                outputBuffer += data;\n\n                // Check for background job indicators\n                if (config.background) {\n                    for (const pattern of backgroundPatterns) {\n                        if (pattern.test(data.trim())) {\n                            commandCompleted = true;\n                            clearTimeout(timeoutId);\n                            resolve({\n                                command,\n                                output: outputBuffer,\n                                exitCode: 0,\n                                duration: Date.now() - startTime,\n                                success: true,\n                                background: true,\n                                completedAt: new Date()\n                            });\n                            return;\n                        }\n                    }\n                }\n\n                // Check for error patterns\n                for (const pattern of errorPatterns) {\n                    if (pattern.test(data.trim())) {\n                        commandCompleted = true;\n                        clearTimeout(timeoutId);\n                        resolve({\n                            command,\n                            output: outputBuffer,\n                            exitCode: 1,\n                            duration: Date.now() - startTime,\n                            success: false,\n                            error: data.trim(),\n                            completedAt: new Date()\n                        });\n                        return;\n                    }\n                }\n\n                // Check for shell prompt if expected\n                if (config.expectPrompt) {\n                    for (const pattern of shellPrompts) {\n                        if (pattern.test(data.trim())) {\n                            commandCompleted = true;\n                            clearTimeout(timeoutId);\n                            resolve({\n                                command,\n                                output: outputBuffer,\n                                exitCode: 0,\n                                duration: Date.now() - startTime,\n                                success: true,\n                                completedAt: new Date()\n                            });\n                            return;\n                        }\n                    }\n                }\n            };\n\n            // Set up timeout\n            const timeoutId = setTimeout(() => {\n                if (!commandCompleted) {\n                    resolve({\n                        command,\n                        output: outputBuffer,\n                        exitCode: -1,\n                        duration: Date.now() - startTime,\n                        success: false,\n                        error: `Command timed out after ${config.timeout}ms`,\n                        completedAt: new Date()\n                    });\n                }\n            }, config.timeout);\n\n            // Listen for output data\n            const dataListener = this.events.onData(checkCompletion);\n\n            // Clean up listener on completion\n            const cleanup = () => {\n                dataListener.dispose();\n                clearTimeout(timeoutId);\n            };\n\n            // Override resolve to include cleanup\n            const originalResolve = resolve;\n            resolve = ((result: CommandResult) => {\n                cleanup();\n                originalResolve(result);\n            }) as typeof resolve;\n\n            // Override reject to include cleanup\n            const originalReject = reject;\n            reject = ((error: unknown) => {\n                cleanup();\n                originalReject(error);\n            }) as typeof reject;\n        });\n    }\n\n    private _calculateAverageResponseTime(currentResponseTime: number): number {\n        if (this._responseTimes.length === 0) {\n            return currentResponseTime;\n        }\n\n        // Use exponential moving average for better performance tracking\n        const alpha = 0.1; // Smoothing factor\n        const previousAverage = this.stats.averageResponseTime || 0;\n\n        return previousAverage * (1 - alpha) + currentResponseTime * alpha;\n    }\n\n    private _processInput(data: string): string {\n        let processed = data;\n\n        // Handle backspace - using character code to avoid ESLint issues\n        if (data.includes(String.fromCharCode(8))) {\n            processed = data.replace(new RegExp(String.fromCharCode(8), 'g'), '\\x1b[D \\x1b[D');\n        }\n\n        // Handle special control sequences\n        if (this.config.enableBracketedPaste && data.length > 1) {\n            processed = `\\x1b[200~${processed}\\x1b[201~`;\n        }\n\n        return processed;\n    }\n\n    private async _processWriteQueue(): Promise<void> {\n        if (this._isWriting || this._writeQueue.length === 0) return;\n\n        this._isWriting = true;\n\n        try {\n            while (this._writeQueue.length > 0 && !this._isDisposed) {\n                const chunk = this._writeQueue.shift();\n                if (chunk && this._terminal) {\n                    await this._sendTextToTerminal(chunk, false);\n                    this.stats.totalOutputBytes += chunk.length;\n                }\n            }\n        } finally {\n            this._isWriting = false;\n        }\n    }\n\n    private async _sendTextToTerminal(text: string, addNewLine: boolean): Promise<void> {\n        if (!this._terminal) return;\n\n        try {\n            this._terminal.sendText(text, addNewLine);\n        } catch (error) {\n            console.error('Error sending text to terminal:', error);\n            this.stats.errorCount++;\n        }\n    }\n\n    public async resize(cols: number, rows: number): Promise<void> {\n        if (this._isDisposed) return;\n\n        this.state.dimensions = { columns: cols, rows };\n        this.stats.resizeCount++;\n        this._onResize.fire({ columns: cols, rows });\n\n        // VS Code handles terminal resizing automatically\n        // but we can track the intended dimensions\n    }\n\n    public async clear(): Promise<void> {\n        await this._sendTextToTerminal('\\x1b[2J\\x1b[0f', false);\n    }\n\n    public async reset(): Promise<void> {\n        await this._sendTextToTerminal('\\x1b[c', false); // Reset terminal\n        await this._delay(100);\n        await this._initializeTerminal();\n    }\n\n    public async sendSignal(signal: string): Promise<void> {\n        // VS Code doesn't support sending signals directly\n        // This is a placeholder for potential future enhancement\n        console.log(`Signal ${signal} requested but not supported in VS Code terminals`);\n    }\n\n    public async getWorkingDirectory(): Promise<string> {\n        if (this.config.workingDirectoryTracking) {\n            return this.state.currentDirectory;\n        }\n        return this.config.cwd || process.cwd();\n    }\n\n    public async setWorkingDirectory(path: string): Promise<void> {\n        await this.executeCommand(`cd \"${path}\"`);\n        this.state.currentDirectory = path;\n        this._onStateChange.fire({ ...this.state });\n    }\n\n    public getCommandHistory(): string[] {\n        return [...this._commandHistory];\n    }\n\n    public clearCommandHistory(): void {\n        this._commandHistory = [];\n        this.state.commandHistory = [];\n        this._onStateChange.fire({ ...this.state });\n    }\n\n    private _addToCommandHistory(command: string): void {\n        if (!this.config.enableCommandHistory) return;\n\n        this._commandHistory.push(command);\n        if (this._commandHistory.length > (this.config.maxCommandHistory || 1000)) {\n            this._commandHistory.shift();\n        }\n\n        this.state.commandHistory = [...this._commandHistory];\n    }\n\n    public enableLogging(logPath?: string): void {\n        if (logPath) {\n            this._logPath = logPath;\n        }\n\n        if (!this._logStream) {\n            this._logStream = vscode.window.createOutputChannel('Terminal Logs');\n        }\n    }\n\n    public disableLogging(): void {\n        if (this._logStream) {\n            this._logStream.dispose();\n            this._logStream = undefined;\n        }\n        this._logPath = undefined;\n    }\n\n    private _updateAverageResponseTime(): void {\n        if (this._responseTimes.length > 0) {\n            this.stats.averageResponseTime = this._responseTimes.reduce((a, b) => a + b, 0) / this._responseTimes.length;\n        }\n    }\n\n    private _handleExit(exitCode: number): void {\n        this.state.exitCode = exitCode;\n        this.state.isConnected = false;\n        this.state.isReady = false;\n\n        this._onExit.fire(exitCode);\n        this._onStateChange.fire({ ...this.state });\n\n        this.dispose();\n    }\n\n//Method removed - moved to InteractiveSession\n\n    public dispose(): void {\n        if (this._isDisposed) return;\n\n        this._isDisposed = true;\n\n        // Clear timers\n        if (this._outputFlushTimer) {\n            clearTimeout(this._outputFlushTimer);\n            this._outputFlushTimer = null;\n        }\n\n        // Dispose terminal\n        if (this._terminal) {\n            try {\n                this._terminal.dispose();\n            } catch (error) {\n                console.error('Error disposing terminal:', error);\n            }\n            this._terminal = null;\n        }\n\n        // Dispose all resources\n        this._disposables.forEach(d => {\n            try {\n                d.dispose();\n            } catch (error) {\n                console.error('Error disposing resource:', error);\n            }\n        });\n        this._disposables = [];\n\n        // Disable logging\n        this.disableLogging();\n\n        // Clear buffers\n        this._writeQueue = [];\n        this._outputBuffer = '';\n    }\n}\n\n/**\n * Pseudoterminal with advanced features using actual child processes\n */\nexport class PseudoTerminal implements vscode.Pseudoterminal {\n    private readonly _onDidWriteEmitter = new vscode.EventEmitter<string>();\n    private readonly _onDidCloseEmitter = new vscode.EventEmitter<number>();\n\n    public readonly onDidWrite = this._onDidWriteEmitter.event;\n    public readonly onDidClose = this._onDidCloseEmitter.event;\n\n    private _childProcess: ChildProcess | null = null;\n    private _isOpen = false;\n    private _isDisposed = false;\n    private _outputBuffer = '';\n    private _outputFlushTimer: NodeJS.Timeout | null = null;\n    private _dimensions: vscode.TerminalDimensions | undefined;\n    private _cwd: string;\n    private _shell: string;\n    private _shellArgs: string[];\n\n    constructor(\n        private readonly config: TerminalConfig = {},\n        private readonly exitCallback?: (exitCode: number | undefined) => void\n    ) {\n        this._dimensions = config.dimensions || { columns: 120, rows: 30 };\n        this._cwd = config.cwd || process.cwd();\n        this._shell = this._resolveShellPath(config.shell || this._detectShell());\n        this._shellArgs = config.args || this._getShellArgs();\n    }\n\n    public open(initialDimensions: vscode.TerminalDimensions | undefined): void {\n        if (this._isOpen || this._isDisposed) return;\n\n        this._isOpen = true;\n        this._dimensions = initialDimensions || this._dimensions;\n\n        try {\n            // Spawn the actual shell process\n            this._childProcess = spawn(this._shell, this._shellArgs, {\n                cwd: this._cwd,\n                env: {\n                    ...process.env,\n                    ...this.config.env,\n                    TERM: 'xterm-256color',\n                    COLUMNS: this._dimensions?.columns?.toString() || '120',\n                    LINES: this._dimensions?.rows?.toString() || '30'\n                },\n                stdio: ['pipe', 'pipe', 'pipe']\n            });\n\n            // Set up event handlers for the child process\n            this._childProcess.stdout?.on('data', (data: Buffer) => {\n                this._handleOutput(data.toString());\n            });\n\n            this._childProcess.stderr?.on('data', (data: Buffer) => {\n                this._handleOutput(data.toString());\n            });\n\n            this._childProcess.on('exit', (code, signal) => {\n                const exitCode = code !== null ? code : (signal ? -1 : 0);\n                this._cleanupAndClose(exitCode);\n            });\n\n            this._childProcess.on('error', (error) => {\n                Logger.instance.error('Terminal process error:', error);\n                this._handleOutput(`\\r\\n${error.message}\\r\\n`);\n                this._cleanupAndClose(-1);\n            });\n\n        } catch (error) {\n            Logger.instance.error('Failed to spawn terminal process:', error);\n            this._cleanupAndClose(-1);\n        }\n    }\n\n    private _detectShell(): string {\n        const platform = os.platform();\n        if (platform === 'win32') {\n            return process.env.ComSpec || path.join(process.env.SystemRoot || 'C:\\\\Windows', 'System32', 'cmd.exe');\n        } else if (platform === 'darwin') {\n            return process.env.SHELL || '/bin/zsh';\n        } else {\n            return process.env.SHELL || '/bin/bash';\n        }\n    }\n\n    private _getShellArgs(): string[] {\n        const platform = os.platform();\n        const shell = this.config.shell || this._detectShell();\n\n        if (platform === 'win32') {\n            if (shell.includes('powershell') || shell.includes('pwsh')) {\n                return ['-NoExit', '-Command', ''];\n            } else if (shell.includes('cmd')) {\n                return ['/k'];\n            }\n        } else {\n            // Unix-like systems\n            return ['-i']; // Interactive mode\n        }\n\n        return [];\n    }\n\n    private _resolveShellPath(shell: string): string {\n        if (path.isAbsolute(shell)) {\n            return shell;\n        }\n\n        // Try to resolve shell from PATH\n        const pathEnv = process.env.PATH || '';\n        const pathSeparator = os.platform() === 'win32' ? ';' : ':';\n        const paths = pathEnv.split(pathSeparator);\n\n        for (const dir of paths) {\n            const fullPath = path.join(dir, shell);\n            try {\n                // In a real implementation, you'd check if file exists and is executable\n                return fullPath;\n            } catch {\n                continue;\n            }\n        }\n\n        return shell; // Fallback to original\n    }\n\n    public close(): void {\n        this._cleanupAndClose(0);\n    }\n\n    public handleInput(data: string): void {\n        if (this._childProcess && this._childProcess.stdin && !this._isDisposed) {\n            this._childProcess.stdin.write(data);\n        }\n    }\n\n    private _handleOutput(data: string): void {\n        if (!data || this._isDisposed) return;\n\n        this._outputBuffer += data;\n\n        if (this._outputFlushTimer) {\n            clearTimeout(this._outputFlushTimer);\n        }\n\n        this._outputFlushTimer = setTimeout(() => {\n            this._flushOutputBuffer();\n        }, 16); // ~60fps for smooth output\n    }\n\n    private _flushOutputBuffer(): void {\n        if (this._isDisposed || !this._outputBuffer) {\n            this._outputFlushTimer = null;\n            return;\n        }\n\n        try {\n            this._onDidWriteEmitter.fire(this._outputBuffer);\n            this._outputBuffer = '';\n        } catch (error) {\n            console.error('Error flushing terminal output:', error);\n        } finally {\n            this._outputFlushTimer = null;\n        }\n    }\n\n    public setDimensions(dimensions: vscode.TerminalDimensions): void {\n        this._dimensions = dimensions;\n\n        if (this._childProcess && !this._childProcess.killed) {\n            // Send resize signal to child process\n            try {\n                this._childProcess.kill('SIGWINCH');\n            } catch (error) {\n                Logger.instance.error('Error resizing terminal:', error);\n            }\n        }\n    }\n\n    private _cleanupAndClose(exitCode: number): void {\n        if (this._outputFlushTimer) {\n            clearTimeout(this._outputFlushTimer);\n            this._outputFlushTimer = null;\n            this._flushOutputBuffer();\n        }\n\n        if (this._childProcess && !this._childProcess.killed) {\n            this._childProcess.kill();\n            this._childProcess = null;\n        }\n\n        this._isOpen = false;\n        this._onDidCloseEmitter.fire(exitCode);\n        this.exitCallback?.(exitCode);\n    }\n\n    public dispose(): void {\n        if (this._isDisposed) return;\n\n        this._isDisposed = true;\n        this._cleanupAndClose(0);\n    }\n\n    // Additional methods\n    public getChildProcess(): ChildProcess | null {\n        return this._childProcess;\n    }\n\n    public async executeCommand(command: string): Promise<string> {\n        if (this._childProcess && this._childProcess.stdin && !this._childProcess.killed) {\n            return new Promise((resolve, reject) => {\n                let output = '';\n                const timeout = setTimeout(() => {\n                    reject(new Error('Command execution timeout'));\n                }, 30000);\n\n                const dataHandler = (data: Buffer) => {\n                    output += data.toString();\n                };\n\n                if (this._childProcess && this._childProcess.stdout) {\n                    this._childProcess.stdout.on('data', dataHandler);\n                }\n                if (this._childProcess && this._childProcess.stderr) {\n                    this._childProcess.stderr.on('data', dataHandler);\n                }\n\n                if (this._childProcess && this._childProcess.stdin) {\n                    this._childProcess.stdin.write(command + '\\n');\n                }\n\n                // Simple command completion detection (this could be enhanced)\n                setTimeout(() => {\n                    clearTimeout(timeout);\n                    if (this._childProcess && this._childProcess.stdout) {\n                        this._childProcess.stdout.off('data', dataHandler);\n                    }\n                    if (this._childProcess && this._childProcess.stderr) {\n                        this._childProcess.stderr.off('data', dataHandler);\n                    }\n                    resolve(output);\n                }, 1000);\n            });\n        }\n        throw new Error('Terminal not initialized');\n    }\n\n    public getStats(): TerminalStats | null {\n        // Return basic stats for the child process\n        if (this._childProcess) {\n            return {\n                totalCommands: 0,\n                totalOutputBytes: 0,\n                averageResponseTime: 0,\n                uptimeSeconds: Math.floor((Date.now() - this._childProcess.spawnargs.length) / 1000),\n                errorCount: 0,\n                resizeCount: 0\n            };\n        }\n        return null;\n    }\n\n    public getState(): TerminalState | null {\n        if (this._childProcess) {\n            return {\n                isConnected: !this._childProcess.killed,\n                isReady: !this._childProcess.killed,\n                currentDirectory: this._cwd,\n                shellType: this._shell,\n                dimensions: this._dimensions || { columns: 120, rows: 30 },\n                lastCommand: '',\n                commandHistory: [],\n                isBusy: false,\n                exitCode: this._childProcess.exitCode || undefined\n            };\n        }\n        return null;\n    }\n}\n\n/**\n * Terminal Actions integration class\n */\nexport class TerminalActions {\n    private readonly _session: InteractiveSession;\n    private readonly _terminalProvider: TerminalCodeActionProvider;\n    private readonly _actionHistory: TerminalActionResult[] = [];\n    private readonly _activeActions: Map<string, vscode.CodeAction> = new Map();\n    private _isEnabled: boolean = true;\n\n    constructor(session: InteractiveSession) {\n        this._session = session;\n        this._terminalProvider = codeActionManager.getProvider('terminal') as TerminalCodeActionProvider\n            || new TerminalCodeActionProvider();\n\n        this._initialize();\n    }\n\n    private _initialize(): void {\n        // Register command handlers for terminal actions\n        const commands = [\n            vscode.commands.registerCommand('codessa.installCommand', this._installCommand.bind(this)),\n            vscode.commands.registerCommand('codessa.fixPackageError', this._fixPackageError.bind(this)),\n            vscode.commands.registerCommand('codessa.fixPermissionError', this._fixPermissionError.bind(this)),\n            vscode.commands.registerCommand('codessa.fixConnectionError', this._fixConnectionError.bind(this)),\n            vscode.commands.registerCommand('codessa.runTerminalQuickFix', this._runTerminalQuickFix.bind(this)),\n            vscode.commands.registerCommand('codessa.showTerminalActions', this._showTerminalActions.bind(this))\n        ];\n\n        // Store disposables for cleanup\n        const sessionWithDisposables = this._session as InteractiveSession & { disposables?: vscode.Disposable[] };\n        sessionWithDisposables.disposables = sessionWithDisposables.disposables || [];\n        sessionWithDisposables.disposables.push(...commands);\n\n        // Set up event listeners\n        this._setupEventListeners();\n    }\n\n    private _setupEventListeners(): void {\n        // Listen for terminal output to detect errors\n        const outputListener = this._session.onData((output) => {\n            if (this._isEnabled) {\n                this._analyzeTerminalOutput(output);\n            }\n        });\n\n        const errorListener = this._session.onError((error) => {\n            this._handleTerminalError(error);\n        });\n\n        const sessionWithDisposables = this._session as InteractiveSession & { disposables?: vscode.Disposable[] };\n        sessionWithDisposables.disposables = sessionWithDisposables.disposables || [];\n        sessionWithDisposables.disposables.push(outputListener, errorListener);\n    }\n\n    private async _analyzeTerminalOutput(output: string): Promise<void> {\n        try {\n            const actions = this._terminalProvider.analyzeTerminalOutput(output);\n\n            if (actions.length > 0) {\n                // Register actions for later use\n                actions.forEach((action, index) => {\n                    const actionId = `terminal-action-${Date.now()}-${index}`;\n                    this._activeActions.set(actionId, action);\n                });\n\n                // Show notification with quick fix option\n                const showActions = await vscode.window.showInformationMessage(\n                    `Found ${actions.length} potential fix(es) for terminal output`,\n                    'Show Quick Fixes',\n                    'Apply Best Fix',\n                    'Ignore'\n                );\n\n                switch (showActions) {\n                    case 'Show Quick Fixes':\n                        await this._showTerminalActions();\n                        break;\n                    case 'Apply Best Fix':\n                        await this._applyBestAction(actions);\n                        break;\n                }\n            }\n        } catch (error) {\n            console.error('Error analyzing terminal output:', error);\n        }\n    }\n\n    private _handleTerminalError(error: Error): void {\n        console.error('Terminal error detected:', error);\n\n        // Create a generic error fix action\n        const action = new vscode.CodeAction(\n            `$(error) Fix terminal error: ${error.message}`,\n            vscode.CodeActionKind.QuickFix\n        );\n\n        action.command = {\n            command: 'codessa.runTerminalQuickFix',\n            title: 'Run terminal error fix',\n            arguments: [error.message]\n        };\n\n        const actionId = `terminal-error-${Date.now()}`;\n        this._activeActions.set(actionId, action);\n\n        vscode.window.showErrorMessage(\n            `Terminal error detected: ${error.message}`,\n            'Show Fix'\n        ).then(selection => {\n            if (selection === 'Show Fix') {\n                this._showTerminalActions();\n            }\n        });\n    }\n\n    private async _installCommand(command: string): Promise<void> {\n        try {\n            let installCommand = '';\n\n            // Determine the appropriate install command based on the system\n            const platform = process.platform;\n\n            if (platform === 'win32') {\n                // Windows - try Chocolatey first, then scoop\n                installCommand = `choco install ${command} || scoop install ${command}`;\n            } else if (platform === 'darwin') {\n                // macOS - try Homebrew\n                installCommand = `brew install ${command}`;\n            } else {\n                // Linux - try apt, then yum, then snap\n                installCommand = `sudo apt update && sudo apt install -y ${command} || sudo yum install -y ${command} || sudo snap install ${command}`;\n            }\n\n            await this._session.writeLn(installCommand);\n\n            const result: TerminalActionResult = {\n                action: new vscode.CodeAction(`Install ${command}`),\n                applied: true,\n                timestamp: new Date(),\n                output: `Installing ${command}...`\n            };\n\n            this._actionHistory.push(result);\n\n            vscode.window.showInformationMessage(`Installing ${command}...`);\n        } catch (error) {\n            vscode.window.showErrorMessage(`Failed to install ${command}: ${error}`);\n        }\n    }\n\n    private async _fixPackageError(errorOutput: string): Promise<void> {\n        try {\n            // Common package error fixes\n            if (errorOutput.includes('EACCES') || errorOutput.includes('permission')) {\n                // Permission error - try with sudo or fix permissions\n                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm');\n                await this._session.writeLn('sudo chmod -R 755 ~/.npm');\n            } else if (errorOutput.includes('ENOTFOUND')) {\n                // Network error - check connection\n                await this._session.writeLn('ping -c 4 registry.npmjs.org');\n            } else if (errorOutput.includes('ETIMEDOUT')) {\n                // Timeout - retry with different registry\n                await this._session.writeLn('npm config set registry https://registry.npmjs.org/');\n            } else {\n                // Generic fix - clear cache and retry\n                await this._session.writeLn('npm cache clean --force');\n                await this._session.writeLn('rm -rf node_modules package-lock.json');\n                await this._session.writeLn('npm install');\n            }\n\n            const result: TerminalActionResult = {\n                action: new vscode.CodeAction('Fix package error'),\n                applied: true,\n                timestamp: new Date(),\n                output: 'Applied package error fix'\n            };\n\n            this._actionHistory.push(result);\n        } catch (error) {\n            vscode.window.showErrorMessage(`Failed to fix package error: ${error}`);\n        }\n    }\n\n    private async _fixPermissionError(errorOutput: string): Promise<void> {\n        try {\n            // Fix permission issues\n            await this._session.writeLn('chmod +x $(pwd)/*');\n            await this._session.writeLn('find . -type f -name \"*.sh\" -exec chmod +x {} \\\\;');\n\n            // If it's a Node.js permission issue\n            if (errorOutput.includes('node') || errorOutput.includes('npm')) {\n                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm ~/.node-gyp');\n            }\n\n            const result: TerminalActionResult = {\n                action: new vscode.CodeAction('Fix permission error'),\n                applied: true,\n                timestamp: new Date(),\n                output: 'Fixed permission issues'\n            };\n\n            this._actionHistory.push(result);\n\n            vscode.window.showInformationMessage('Fixed permission issues');\n        } catch (error) {\n            vscode.window.showErrorMessage(`Failed to fix permissions: ${error}`);\n        }\n    }\n\n    private async _fixConnectionError(errorOutput: string): Promise<void> {\n        try {\n            // Diagnose and fix connection issues\n            await this._session.writeLn('ping -c 4 8.8.8.8'); // Test basic connectivity\n            await this._session.writeLn('nslookup google.com'); // Test DNS\n\n            // Check proxy settings\n            await this._session.writeLn('env | grep -i proxy');\n\n            // If it's a specific service, try alternative endpoints\n            if (errorOutput.includes('registry.npmjs.org')) {\n                await this._session.writeLn('npm config set registry https://registry.npmmirror.com');\n            }\n\n            const result: TerminalActionResult = {\n                action: new vscode.CodeAction('Fix connection error'),\n                applied: true,\n                timestamp: new Date(),\n                output: 'Diagnosed and attempted to fix connection issues'\n            };\n\n            this._actionHistory.push(result);\n\n            vscode.window.showInformationMessage('Diagnosed connection issues');\n        } catch (error) {\n            vscode.window.showErrorMessage(`Failed to fix connection: ${error}`);\n        }\n    }\n\n    private async _runTerminalQuickFix(errorMessage: string): Promise<void> {\n        try {\n            // Generic terminal error fixes\n            if (errorMessage.includes('command not found')) {\n                const command = errorMessage.match(/command not found:?\\s+(.+?)(?:\\s|$)/i)?.[1];\n                if (command) {\n                    await this._installCommand(command);\n                    return;\n                }\n            }\n\n            // Run a generic diagnostic\n            await this._session.writeLn('echo \"Terminal diagnostic:\"');\n            await this._session.writeLn('pwd && ls -la');\n            await this._session.writeLn('echo $PATH');\n            await this._session.writeLn('which bash');\n\n            vscode.window.showInformationMessage('Ran terminal diagnostic');\n        } catch (error) {\n            vscode.window.showErrorMessage(`Failed to run terminal fix: ${error}`);\n        }\n    }\n\n    private async _showTerminalActions(): Promise<void> {\n        const actions = Array.from(this._activeActions.values());\n\n        if (actions.length === 0) {\n            vscode.window.showInformationMessage('No terminal actions available');\n            return;\n        }\n\n        const items = actions.map(action => ({\n            label: action.title,\n            description: action.kind?.value || 'Quick Fix',\n            action: action\n        }));\n\n        const selected = await vscode.window.showQuickPick(items, {\n            placeHolder: 'Select a terminal quick fix to apply'\n        });\n\n        if (selected) {\n            await this._applyAction(selected.action);\n        }\n    }\n\n    private async _applyBestAction(actions: vscode.CodeAction[]): Promise<void> {\n        if (actions.length === 0) return;\n\n        // Apply the first (highest priority) action\n        await this._applyAction(actions[0]);\n    }\n\n    private async _applyAction(action: vscode.CodeAction): Promise<void> {\n        try {\n            const dummyDoc = {\n                uri: vscode.Uri.parse('terminal://output'),\n                getText: () => '',\n                getWordRangeAtPosition: () => undefined,\n                lineAt: () => ({ text: '', range: new vscode.Range(0, 0, 0, 0) }),\n                offsetAt: () => 0,\n                positionAt: () => new vscode.Position(0, 0),\n                validateRange: (range: vscode.Range) => range,\n                validatePosition: (position: vscode.Position) => position\n            } as unknown as vscode.TextDocument;\n\n            const success = await this._terminalProvider.applyQuickFix(action, dummyDoc);\n\n            const result: TerminalActionResult = {\n                action,\n                applied: success,\n                timestamp: new Date(),\n                output: success ? 'Action applied successfully' : 'Failed to apply action'\n            };\n\n            this._actionHistory.push(result);\n\n            if (success) {\n                vscode.window.showInformationMessage('Quick fix applied successfully');\n            } else {\n                vscode.window.showErrorMessage('Failed to apply quick fix');\n            }\n        } catch (error) {\n            vscode.window.showErrorMessage(`Error applying action: ${error}`);\n        }\n    }\n\n    public getActionHistory(): TerminalActionResult[] {\n        return [...this._actionHistory];\n    }\n\n    public clearActionHistory(): void {\n        this._actionHistory.length = 0;\n    }\n\n    public enable(): void {\n        this._isEnabled = true;\n    }\n\n    public disable(): void {\n        this._isEnabled = false;\n    }\n\n    public isEnabled(): boolean {\n        return this._isEnabled;\n    }\n\n    public getActiveActions(): vscode.CodeAction[] {\n        return Array.from(this._activeActions.values());\n    }\n\n    public dispose(): void {\n        this._activeActions.clear();\n        this._actionHistory.length = 0;\n    }\n}\n\n/**\n * Interactive session with Code Action integration\n */\nexport class InteractiveSession implements ITerminalProcess {\n    public readonly id: string;\n    public readonly config: TerminalConfig;\n    public readonly state: TerminalState;\n    public readonly stats: TerminalStats;\n    public readonly events: TerminalEvents;\n\n    // Code Action patterns for terminal error detection\n    private readonly _quickFixPatterns: QuickFixPattern[] = [];\n    private readonly _iconRegistry: Map<string, QuickFixIcon> = new Map();\n    private readonly _actionHistory: TerminalActionResult[] = [];\n    private readonly _activeActions: Map<string, vscode.CodeAction> = new Map();\n    private _isCodeActionsEnabled: boolean = true;\n\n    // Terminal Actions integration\n    private _terminalActions: TerminalActions | null = null;\n    private _actionProvider: TerminalActionProvider;\n    \n    private _pty: PseudoTerminal;\n    private _terminal: vscode.Terminal | null = null;\n    private _isDisposed = false;\n    private _pendingActions: vscode.CodeAction[] = [];\n\n    // Event forwarding\n    private _dataEmitter = new vscode.EventEmitter<string>();\n    private _exitEmitter = new vscode.EventEmitter<number>();\n    private _readyEmitter = new vscode.EventEmitter<void>();\n    private _resizeEmitter = new vscode.EventEmitter<{ columns: number; rows: number }>();\n    private _commandEmitter = new vscode.EventEmitter<string>();\n    private _errorEmitter = new vscode.EventEmitter<Error>();\n    private _stateChangeEmitter = new vscode.EventEmitter<TerminalState>();\n\n    public readonly onData = this._dataEmitter.event;\n    public readonly onExit = this._exitEmitter.event;\n    public readonly onReady = this._readyEmitter.event;\n    public readonly onResize = this._resizeEmitter.event;\n    public readonly onCommand = this._commandEmitter.event;\n    public readonly onError = this._errorEmitter.event;\n    public readonly onStateChange = this._stateChangeEmitter.event;\n\n\n\n    constructor(config: TerminalConfig = {}) {\n        this.id = `interactive-session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;\n\n        this.config = {\n            name: 'Interactive Terminal Session',\n            ...config\n        };\n\n        // Initialize providers and actions\n        this._actionProvider = new TerminalActionProvider();\n        this._terminalActions = new TerminalActions(this);\n\n        this._pty = new PseudoTerminal(\n            {\n                shell: this.config.shell || 'bash',\n                cwd: this.config.cwd || process.cwd()\n            },\n            (exitCode) => {\n                this._exitEmitter.fire(exitCode || 0);\n            }\n        );\n\n        // Forward events from the pseudoterminal and analyze output\n        this._pty.onDidWrite((data) => {\n            this._dataEmitter.fire(data);\n            this._analyzeTerminalOutput(data);\n        });\n\n        this._pty.onDidClose((exitCode) => {\n            this._exitEmitter.fire(exitCode);\n            this.dispose();\n        });\n\n        // Initialize state and stats\n        this.state = {\n            isConnected: false,\n            isReady: false,\n            currentDirectory: this.config.cwd || process.cwd(),\n            shellType: this.config.shell || 'bash',\n            dimensions: this.config.dimensions || { columns: 120, rows: 30 },\n            lastCommand: '',\n            commandHistory: [],\n            isBusy: false\n        };\n\n        this.stats = {\n            totalCommands: 0,\n            totalOutputBytes: 0,\n            averageResponseTime: 0,\n            uptimeSeconds: 0,\n            errorCount: 0,\n            resizeCount: 0\n        };\n\n        this.events = {\n            onData: this.onData,\n            onExit: this.onExit,\n            onReady: this.onReady,\n            onResize: this.onResize,\n            onCommand: this.onCommand,\n            onError: this.onError,\n            onStateChange: this.onStateChange\n        };\n\n        // Initialize Code Action patterns\n        this._initializeQuickFixPatterns();\n        this._initializeIconRegistry();\n\n        // Initialize Terminal Actions\n        this._terminalActions = new TerminalActions(this);\n\n        this._createTerminal();\n    }\n\n    private _createTerminal(): void {\n        this._terminal = vscode.window.createTerminal({\n            name: this.config.name || 'Interactive Terminal Session',\n            pty: this._pty,\n            iconPath: new vscode.ThemeIcon('terminal')\n        });\n\n        if (!this.config.hideFromUser) {\n            this._terminal.show();\n        }\n    }\n\n    private _initializeQuickFixPatterns(): void {\n        // Terminal Error Patterns\n        this._addPattern({\n            pattern: /bash:\\s+(.+?):\\s+command not found/i,\n            action: 'install-missing-command',\n            priority: 100,\n            category: 'terminal',\n            fix: (match, doc, range) => {\n                const command = match[1];\n                const action = new vscode.CodeAction(\n                    `Install missing command: ${command}`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.installCommand',\n                    title: `Install ${command}`,\n                    arguments: [command, 'terminal']\n                };\n                action.diagnostics = [{\n                    range,\n                    message: `Command '${command}' not found`,\n                    severity: vscode.DiagnosticSeverity.Error,\n                    source: 'terminal'\n                }];\n                return action;\n            }\n        });\n\n        // Import Error Patterns\n        this._addPattern({\n            pattern: /Cannot find module\\s+['\"](.+?)['\"]/i,\n            action: 'fix-import',\n            priority: 95,\n            category: 'imports',\n            fix: (match, _doc, _range) => {\n                const property = match[1];\n                const action = new vscode.CodeAction(\n                    `Add property '${property}' to interface`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.addProperty',\n                    title: `Add property ${property}`,\n                    arguments: [property, _doc, _range]\n                };\n                return action;\n            }\n        });\n\n        // TypeScript Error Patterns\n        this._addPattern({\n            pattern: /Property\\s+['\"](.+?)['\"]\\s+does not exist on type/i,\n            action: 'add-property',\n            priority: 90,\n            category: 'typescript',\n            fix: (match, _doc, _range) => {\n                const property = match[1];\n                const action = new vscode.CodeAction(\n                    `Add property '${property}' to interface`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.addProperty',\n                    title: `Add property ${property}`,\n                    arguments: [property, _doc, _range]\n                };\n                return action;\n            }\n        });\n\n        // ESLint Error Patterns\n        this._addPattern({\n            pattern: /(.+?)\\s+is not defined/i,\n            action: 'define-variable',\n            priority: 85,\n            category: 'eslint',\n            fix: (match, doc, range) => {\n                const variable = match[1];\n                const action = new vscode.CodeAction(\n                    `Define variable: ${variable}`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.edit = new vscode.WorkspaceEdit();\n                action.edit.insert(doc.uri, new vscode.Position(range.start.line, 0),\n                    `const ${variable} = ;\\n`);\n                return action;\n            }\n        });\n\n        // Security Vulnerability Patterns\n        this._addPattern({\n            pattern: /Potential security vulnerability:\\s+(.+?)/i,\n            action: 'fix-security',\n            priority: 100,\n            category: 'security',\n            fix: (match, doc, range) => {\n                const issue = match[1];\n                const action = new vscode.CodeAction(\n                    `Fix security issue: ${issue}`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.fixSecurity',\n                    title: `Fix ${issue}`,\n                    arguments: [issue, doc, range]\n                };\n                return action;\n            }\n        });\n    }\n\n    private _initializeIconRegistry(): void {\n        this._registerIcon({\n            name: 'lightbulb',\n            icon: new vscode.ThemeIcon('lightbulb'),\n            category: 'suggestion',\n            description: 'General suggestions and improvements'\n        });\n\n        this._registerIcon({\n            name: 'error',\n            icon: new vscode.ThemeIcon('error'),\n            category: 'error',\n            description: 'Error conditions requiring immediate attention'\n        });\n\n        this._registerIcon({\n            name: 'warning',\n            icon: new vscode.ThemeIcon('warning'),\n            category: 'warning',\n            description: 'Warning conditions that should be addressed'\n        });\n\n        this._registerIcon({\n            name: 'info',\n            icon: new vscode.ThemeIcon('info'),\n            category: 'info',\n            description: 'Informational suggestions and hints'\n        });\n\n        this._registerIcon({\n            name: 'tools',\n            icon: new vscode.ThemeIcon('tools'),\n            category: 'refactor',\n            description: 'Refactoring and code improvement actions'\n        });\n\n        this._registerIcon({\n            name: 'package',\n            icon: new vscode.ThemeIcon('package'),\n            category: 'dependency',\n            description: 'Package and dependency management'\n        });\n\n        this._registerIcon({\n            name: 'terminal',\n            icon: new vscode.ThemeIcon('terminal'),\n            category: 'terminal',\n            description: 'Terminal and command-line related actions'\n        });\n\n        this._registerIcon({\n            name: 'debug',\n            icon: new vscode.ThemeIcon('debug'),\n            category: 'debug',\n            description: 'Debugging and diagnostic actions'\n        });\n\n        this._registerIcon({\n            name: 'security',\n            icon: new vscode.ThemeIcon('shield'),\n            category: 'security',\n            description: 'Security-related fixes and improvements'\n        });\n    }\n\n    private _addPattern(pattern: QuickFixPattern): void {\n        this._quickFixPatterns.push(pattern);\n    }\n\n    private _registerIcon(icon: QuickFixIcon): void {\n        this._iconRegistry.set(icon.name, icon);\n    }\n\n    private _getIconForCategory(category: string): string {\n        const icon = this._iconRegistry.get(category);\n        return icon ? `$(${icon.icon.id})` : '💡';\n    }\n\n    // Code Action analysis methods\n    private async _analyzeTerminalOutput(output: string): Promise<void> {\n        if (!this._isCodeActionsEnabled) return;\n\n        try {\n            const actions = this._analyzeOutputForActions(output);\n\n            if (actions.length > 0) {\n                // Register actions for later use\n                actions.forEach((action, index) => {\n                    const actionId = `terminal-action-${Date.now()}-${index}`;\n                    this._activeActions.set(actionId, action);\n                });\n\n                // Show notification with quick fix option\n                const showActions = await vscode.window.showInformationMessage(\n                    `Found ${actions.length} potential fix(es) for terminal output`,\n                    'Show Quick Fixes',\n                    'Apply Best Fix',\n                    'Ignore'\n                );\n\n                switch (showActions) {\n                    case 'Show Quick Fixes':\n                        await this._showTerminalActions();\n                        break;\n                    case 'Apply Best Fix':\n                        await this._applyBestAction(actions);\n                        break;\n                }\n            }\n        } catch (error) {\n            console.error('Error analyzing terminal output:', error);\n        }\n    }\n\n    private _analyzeOutputForActions(output: string): vscode.CodeAction[] {\n        const actions: vscode.CodeAction[] = [];\n\n        for (const pattern of this._quickFixPatterns) {\n            const matches = output.match(pattern.pattern);\n            if (matches) {\n                try {\n                    // Create a dummy document and range for the pattern fix\n                    const dummyDoc = {\n                        uri: vscode.Uri.parse('terminal://output'),\n                        getText: () => output\n                    } as vscode.TextDocument;\n\n                    const dummyRange = new vscode.Range(0, 0, 0, 0);\n\n                    const action = pattern.fix(matches as RegExpMatchArray, dummyDoc, dummyRange);\n                    action.title = `${this._getIconForCategory(pattern.category)} ${action.title}`;\n                    actions.push(action);\n                } catch (error) {\n                    console.error('Error creating code action:', error);\n                }\n            }\n        }\n\n        // Store actions in pending actions for later processing\n        this._pendingActions.push(...actions);\n\n        return actions;\n    }\n\n    private async _showTerminalActions(): Promise<void> {\n        const actions = Array.from(this._activeActions.values());\n\n        if (actions.length === 0) {\n            vscode.window.showInformationMessage('No terminal actions available');\n            return;\n        }\n\n        const items = actions.map(action => ({\n            label: action.title,\n            description: action.kind?.value || 'Quick Fix',\n            action: action\n        }));\n\n        const selected = await vscode.window.showQuickPick(items, {\n            placeHolder: 'Select a terminal quick fix to apply'\n        });\n\n        if (selected) {\n            await this._applyAction(selected.action);\n        }\n    }\n\n    private async _applyBestAction(actions: vscode.CodeAction[]): Promise<void> {\n        if (actions.length === 0) return;\n\n        // Apply the first (highest priority) action\n        await this._applyAction(actions[0]);\n    }\n\n    private async _applyAction(action: vscode.CodeAction): Promise<boolean> {\n        try {\n            if (action.edit) {\n                const success = await vscode.workspace.applyEdit(action.edit);\n                if (success) {\n                    vscode.window.showInformationMessage('Quick fix applied successfully');\n                }\n                return success;\n            } else if (action.command) {\n                await vscode.commands.executeCommand(action.command.command, ...action.command.arguments || []);\n                vscode.window.showInformationMessage('Quick fix applied successfully');\n                return true;\n            } else {\n                return false;\n            }\n        } catch (error) {\n            console.error('Error applying quick fix:', error);\n            vscode.window.showErrorMessage('Failed to apply quick fix');\n            return false;\n        }\n    }\n\n    // Public Code Action methods\n    public getActionHistory(): TerminalActionResult[] {\n        return [...this._actionHistory];\n    }\n\n    public clearActionHistory(): void {\n        this._actionHistory.length = 0;\n    }\n\n    public enableCodeActions(): void {\n        this._isCodeActionsEnabled = true;\n    }\n\n    public disableCodeActions(): void {\n        this._isCodeActionsEnabled = false;\n    }\n\n    public isCodeActionsEnabled(): boolean {\n        return this._isCodeActionsEnabled;\n    }\n\n    public getActiveActions(): vscode.CodeAction[] {\n        return Array.from(this._activeActions.values());\n    }\n\n    // Terminal Actions integration methods\n    public getTerminalActions(): TerminalActions | null {\n        return this._terminalActions;\n    }\n\n    public enableTerminalActions(): void {\n        if (this._terminalActions) {\n            this._terminalActions.enable();\n        }\n    }\n\n    public disableTerminalActions(): void {\n        if (this._terminalActions) {\n            this._terminalActions.disable();\n        }\n    }\n\n    public isTerminalActionsEnabled(): boolean {\n        return this._terminalActions ? this._terminalActions.isEnabled() : false;\n    }\n\n    public getTerminalActionHistory(): TerminalActionResult[] {\n        return this._terminalActions ? this._terminalActions.getActionHistory() : [];\n    }\n\n    public clearTerminalActionHistory(): void {\n        if (this._terminalActions) {\n            this._terminalActions.clearActionHistory();\n        }\n    }\n\n    // ITerminalProcess implementation\n    public async initialize(): Promise<void> {\n        // The terminal is already created and will initialize when opened\n        this.state.isConnected = true;\n        this.state.isReady = true;\n        this._readyEmitter.fire();\n        this._stateChangeEmitter.fire({ ...this.state });\n    }\n\n    public async write(data: string): Promise<void> {\n        if (this._isDisposed) return;\n        this._pty.handleInput(data);\n\n        // Analyze output for Code Actions if enabled\n        this._analyzeTerminalOutput(data);\n    }\n\n    public async writeLn(data: string): Promise<void> {\n        await this.write(data + '\\n');\n    }\n\n    public async executeCommand(command: string, options?: {\n        timeout?: number;\n        expectPrompt?: boolean;\n        background?: boolean;\n        workingDirectory?: string;\n    }): Promise<string> {\n        // Use options parameter for timeout handling\n        const timeout = options?.timeout || 30000;\n        const workingDirectory = options?.workingDirectory;\n\n        const childProcess = this._pty.getChildProcess();\n        if (childProcess) {\n            // Log execution details if options provided\n            if (options && (options.background || options.expectPrompt || workingDirectory)) {\n                Logger.instance.info(`Executing command with options: background=${options.background}, expectPrompt=${options.expectPrompt}, workingDirectory=${workingDirectory}, timeout=${timeout}`);\n            }\n            return await this._pty.executeCommand(command);\n        }\n        throw new Error('Terminal not initialized');\n    }\n\n    public async executeCommandAdvanced(command: string, options?: {\n        timeout?: number;\n        expectPrompt?: boolean;\n        background?: boolean;\n        workingDirectory?: string;\n    }): Promise<CommandResult> {\n        const childProcess = this._pty.getChildProcess();\n        if (childProcess && !childProcess.killed) {\n            // Create a basic CommandResult using the child process\n                const startTime = Date.now();\n                try {\n                    const output = await this._pty.executeCommand(command);\n\n                    // Log command execution details if options were provided\n                    if (options && (options.timeout !== undefined || options.workingDirectory || options.background)) {\n                        Logger.instance.info('Executing command with options:', {\n                            command,\n                            timeout: options.timeout,\n                            workingDirectory: options.workingDirectory,\n                            background: options.background,\n                            expectPrompt: options.expectPrompt\n                        });\n                    }\n\n                    return {\n                        exitCode: 0,\n                        output,\n                        command,\n                        duration: Date.now() - startTime,\n                        success: true,\n                        completedAt: new Date()\n                    };\n                } catch (error) {\n                    return {\n                        exitCode: -1,\n                        output: error instanceof Error ? error.message : String(error),\n                        command,\n                        duration: Date.now() - startTime,\n                        success: false,\n                        completedAt: new Date()\n                    };\n                }\n        }\n        throw new Error('Terminal not initialized');\n    }\n\n    public async resize(cols: number, rows: number): Promise<void> {\n        if (this._isDisposed) return;\n        this._pty.setDimensions({ columns: cols, rows });\n        this.state.dimensions = { columns: cols, rows };\n        this._resizeEmitter.fire({ columns: cols, rows });\n        this._stateChangeEmitter.fire({ ...this.state });\n    }\n\n    public async clear(): Promise<void> {\n        await this.write('\\x1b[2J\\x1b[0f');\n    }\n\n    public async reset(): Promise<void> {\n        await this.write('\\x1b[c');\n    }\n\n    public async sendSignal(signal: string): Promise<void> {\n        // Not supported in VS Code terminals\n        console.log(`Signal ${signal} not supported in VS Code terminals`);\n    }\n\n    public async getWorkingDirectory(): Promise<string> {\n        return this.state.currentDirectory;\n    }\n\n    public async setWorkingDirectory(path: string): Promise<void> {\n        await this.executeCommand(`cd \"${path}\"`);\n        this.state.currentDirectory = path;\n        this._stateChangeEmitter.fire({ ...this.state });\n    }\n\n    public getCommandHistory(): string[] {\n        // Return command history from the child process state\n        const state = this._pty.getState();\n        return state?.commandHistory || [];\n    }\n\n    public clearCommandHistory(): void {\n        // Clear command history - this would need to be implemented in the child process\n        Logger.instance.info('Command history cleared');\n    }\n\n    public enableLogging(logPath?: string): void {\n        // Enable logging - store the path for future use\n        Logger.instance.info(`Logging enabled${logPath ? ` to ${logPath}` : ''}`);\n    }\n\n    public disableLogging(): void {\n        // Disable logging\n        Logger.instance.info('Logging disabled');\n    }\n\n    public dispose(): void {\n        if (this._isDisposed) return;\n\n        this._isDisposed = true;\n\n        if (this._terminal) {\n            this._terminal.dispose();\n            this._terminal = null;\n        }\n\n        this._pty.dispose();\n\n        // Dispose emitters\n        this._dataEmitter.dispose();\n        this._exitEmitter.dispose();\n        this._readyEmitter.dispose();\n        this._resizeEmitter.dispose();\n        this._commandEmitter.dispose();\n        this._errorEmitter.dispose();\n        this._stateChangeEmitter.dispose();\n    }\n}\n\n// Legacy interfaces for backward compatibility\nexport interface ILegacyTerminalProcess {\n    onData: vscode.Event<string>;\n    onExit: vscode.Event<number>;\n    write(data: string): void;\n    resize(cols: number, rows: number): void;\n    dispose(): void;\n}\n\nexport interface ILegacyInteractiveSession {\n    id: string;\n    write(data: string): Promise<void>;\n    resize(cols: number, rows: number): Promise<void>;\n    dispose(): void;\n    onData: vscode.Event<string>;\n    onExit: vscode.Event<{ exitCode?: number }>;\n}\n"]}