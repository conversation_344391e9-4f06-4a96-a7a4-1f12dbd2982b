{"version": 3, "file": "pineconeVectorStore.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/vectorStores/pineconeVectorStore.ts"], "names": [], "mappings": ";;;;;;AAEA,4CAAyC,CAAC,oDAAoD;AAC9F,4CAA4C,CAAC,oDAAoD;AACjG,2EAAmD,CAAC,4BAA4B;AA8BhF;;;GAGG;AACH,MAAa,mBAAmB;IACtB,UAAU,CAAa;IACvB,MAAM,CAAiB;IACvB,cAAc,CAA6B,CAAC,2BAA2B;IACvE,aAAa,CAA4B;IACjD,kFAAkF;IAClF,sEAAsE;IACtE,kEAAkE;IAClE,uEAAuE;IAC/D,sBAAsB,CAA4B;IAClD,WAAW,GAAG,KAAK,CAAC;IACpB,cAAc,GAAG,KAAK,CAAC;IAE/B;;;SAGK;IACL,YAAY,UAAsB;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC,+DAA+D;QAE7F,MAAM,MAAM,GAAG,IAAA,kBAAS,EAAS,oCAAoC,EAAE,EAAE,CAAC,CAAC;QAC3E,MAAM,WAAW,GAAG,IAAA,kBAAS,EAAS,yCAAyC,EAAE,EAAE,CAAC,CAAC;QACrF,MAAM,SAAS,GAAG,IAAA,kBAAS,EAAS,uCAAuC,EAAE,kBAAkB,CAAC,CAAC;QACjG,MAAM,SAAS,GAAG,IAAA,kBAAS,EAAqB,uCAAuC,EAAE,SAAS,CAAC,CAAC,CAAC,4BAA4B;QAEjI,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QACzF,CAAC;QAED,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM;YACN,WAAW;YACX,SAAS;YACT,SAAS;SACV,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,0CAA0C,IAAI,CAAC,MAAM,CAAC,SAAS,mBAAmB,IAAI,CAAC,MAAM,CAAC,WAAW,iBAAiB,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,WAAW,GAAG,CAAC,CAAC;IACjL,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,kDAAkD;gBAClD,MAAM,IAAI,OAAO,CAAO,OAAO,CAAC,EAAE;oBAChC,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;wBACrC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;4BACzB,aAAa,CAAC,aAAa,CAAC,CAAC;4BAC7B,OAAO,EAAE,CAAC;wBACZ,CAAC;oBACH,CAAC,EAAE,GAAG,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC3D,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,eAAM,CAAC,IAAI,CAAC,2DAA2D,IAAI,CAAC,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC;QAEpG,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,CAAC,cAAc,GAAG,IAAK,kBAAgD,EAAE,CAAC,CAAC,4BAA4B;YAC3G,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;YACD,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBAC7B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC1B,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;aACrC,CAAC,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAE7C,sBAAsB;YACtB,kEAAkE;YAClE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACtE,eAAM,CAAC,KAAK,CAAC,uCAAuC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;YAE/E,mEAAmE;YACnE,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;gBAC5D,eAAM,CAAC,IAAI,CAAC,6CAA6C,IAAI,CAAC,MAAM,CAAC,SAAS,WAAW,EAAE,KAAK,CAAC,CAAC;gBAClG,0DAA0D;YAC5D,CAAC;YAAC,OAAO,UAAmB,EAAE,CAAC;gBAC7B,MAAM,YAAY,GAAG,UAAU,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBAC3F,eAAM,CAAC,KAAK,CAAC,uDAAuD,IAAI,CAAC,MAAM,CAAC,SAAS,gEAAgE,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;gBACtL,MAAM,IAAI,KAAK,CAAC,wCAAwC,IAAI,CAAC,MAAM,CAAC,SAAS,MAAM,YAAY,EAAE,CAAC,CAAC;YACrG,CAAC;YAED,4FAA4F;YAC5F,gGAAgG;YAChG,qEAAqE;YACrE,yFAAyF;YACzF,yCAAyC;YACzC,wCAAwC;YACxC,MAAM;YACN,2EAA2E;YAG3E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,6DAA6D,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;QAEtG,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAClL,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;YAC/B,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,mCAAmC,YAAY,EAAE,CAAC,CAAC;QACrE,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrE,eAAM,CAAC,KAAK,CAAC,gEAAgE,EAAE;gBAC7E,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc;gBAChC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa;aAC/B,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED;;;;;;;SAOK;IACG,gBAAgB,CAAC,QAAiC,EAAE,WAAoB;QAC9E,MAAM,SAAS,GAAqB,EAAE,CAAC;QACvC,MAAM,yBAAyB,GAAG,IAAI,CAAC,CAAC,kCAAkC;QAC1E,MAAM,cAAc,GAAG,GAAG,CAAC;QAC3B,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC;gBACzD,SAAS;YACX,CAAC;YAED,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;YAE5B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,yBAAyB,CAAC,CAAC;YACjE,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/D,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACzB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACzB,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;gBACjF,uDAAuD;gBACvD,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK;qBACnB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;qBAC5C,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,+BAA+B;gBAC/B,eAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,mBAAmB,GAAG,2BAA2B,OAAO,KAAK,uBAAuB,CAAC,CAAC;YAC7H,CAAC;QACH,CAAC;QAED,wFAAwF;QACxF,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,6CAA6C;YACjF,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC,0BAA0B;QACtG,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;SAOK;IACE,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,MAAgB,EAAE,WAAoC,EAAE;QACzF,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,wDAAwD;QACxD,MAAM,WAAW,GAAG,QAAQ,EAAE,OAAO,IAAI,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC,8BAA8B;QAC7F,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAEjH,MAAM,cAAc,GAAG;YACrB,EAAE;YACF,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,aAAa;SACxB,CAAC;QAEF,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,8BAA8B,EAAE,gBAAgB,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,MAAM,CAAC,CAAC;YAC7H,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,CAAC,cAAc,CAAC;gBACzB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;aACjC,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YACD,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC/C,eAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,0CAA0C,EAAE,GAAG,CAAC,CAAC;QACxF,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,qCAAqC,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YACtK,wDAAwD;YACxD,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAI,KAAK,EAAE,CAAC;gBAC9D,MAAM,QAAQ,GAAG,KAA0C,CAAC;gBAC5D,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;oBAC5B,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,2BAA2B,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC7F,CAAC;YACH,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,wBAAwB,EAAE,iBAAiB,YAAY,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED;;;;;;SAMK;IACE,KAAK,CAAC,SAAS,CAAC,EAAU;QAC/B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,6BAA6B,EAAE,gBAAgB,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,MAAM,CAAC,CAAC;YAC5H,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YACD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAEtG,MAAM,MAAM,GAAG,aAAa,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5C,IAAI,MAAM,EAAE,MAAM,EAAE,CAAC;gBACnB,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,wCAAwC,EAAE,GAAG,CAAC,CAAC;gBACrF,OAAO,MAAM,CAAC,MAAM,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,oBAAoB,EAAE,aAAa,CAAC,CAAC;gBAC3E,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,oCAAoC,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YACrK,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAI,KAAK,EAAE,CAAC;gBAC9D,MAAM,QAAQ,GAAG,KAA0C,CAAC;gBAC5D,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;oBAC5B,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,2BAA2B,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC7F,CAAC;YACH,CAAC;YACD,iEAAiE;YACjE,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,YAAY,CAAC,EAAU;QAClC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,6BAA6B,EAAE,gBAAgB,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,MAAM,CAAC,CAAC;YAC5H,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YACD,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,4BAA4B;YACxG,eAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,6CAA6C,EAAE,wCAAwC,CAAC,CAAC;YAC9H,OAAO,IAAI,CAAC,CAAC,mEAAmE;QAClF,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,qCAAqC,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YACtK,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAI,KAAK,EAAE,CAAC;gBAC9D,MAAM,QAAQ,GAAG,KAA0C,CAAC;gBAC5D,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;oBAC5B,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,2BAA2B,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC7F,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;;;SAMK;IACE,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC;QACnG,eAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,0CAA0C,MAAM,KAAK,CAAC,CAAC;QAE5F,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;oBACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACpD,CAAC;gBACD,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,uEAAuE;gBACvE,kCAAkC;gBAClC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;oBACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACpD,CAAC;gBACD,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzC,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,uCAAuC,MAAM,GAAG,CAAC,CAAC;QACzF,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,kCAAkC,MAAM,GAAG,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YACnK,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAI,KAAK,EAAE,CAAC;gBAC9D,MAAM,QAAQ,GAAG,KAA0C,CAAC;gBAC5D,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;oBAC5B,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,2BAA2B,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC7F,CAAC;YACH,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;;;;;;;;SASK;IACE,KAAK,CAAC,oBAAoB,CAAC,MAAgB,EAAE,KAAK,GAAG,CAAC,EAAE,MAAgC;QAC7F,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,eAAM,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,mBAAmB,KAAK,gCAAgC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YAEhL,MAAM,YAAY,GAAG;gBACnB,MAAM;gBACN,IAAI,EAAE,KAAK;gBACX,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;gBAChC,MAAM,EAAE,MAAM,EAAE,0DAA0D;gBAC1E,eAAe,EAAE,KAAK,EAAE,8CAA8C;gBACtE,aAAa,EAAE,KAAK,EAAE,2BAA2B;aAClD,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YACD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAEnE,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,IAAI,EAAE,CAAC;YAC5C,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,6BAA6B,OAAO,CAAC,MAAM,WAAW,CAAC,CAAC;YAE9F,mEAAmE;YACnE,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAoC,EAAE,EAAE,CAAC,CAAC;gBACrE,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,qFAAqF;gBACrF,KAAK,EAAE,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACzD,CAAC,CAAC,CAAC;YAEJ,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,qCAAqC,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAC5K,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAI,KAAK,EAAE,CAAC;gBAC9D,MAAM,QAAQ,GAAG,KAA0C,CAAC;gBAC5D,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;oBAC5B,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,2BAA2B,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC7F,CAAC;YACH,CAAC;YACD,wDAAwD;YACxD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACE,gBAAgB;QACrB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;CACF;AAjaD,kDAiaC", "sourcesContent": ["import type { Embeddings, PineconeStore } from '../../../agents/workflows/corePolyfill'; // Used if addDocuments is chosen over addVectors\nimport type { IVectorStore } from '../../types'; // Assuming path relative to the final file location\nimport { logger } from '../../../logger'; // Assuming path relative to the final file location\nimport { getConfig } from '../../../config'; // Assuming path relative to the final file location\nimport Pinecone from '@pinecone-database/pinecone'; // Import the default export\n\n// Define the structure for Pinecone metadata more explicitly\ntype PineconeMetadata = Record<string, string | number | boolean | string[]>;\n\n// Define interfaces for Pinecone client types\ninterface PineconeClient {\n  init(config: { apiKey: string; environment: string }): Promise<void>;\n  Index(indexName: string): PineconeIndex;\n}\n\ninterface PineconeIndex {\n  upsert(request: { vectors: { id: string; values: number[]; metadata?: PineconeMetadata }[]; namespace?: string }): Promise<unknown>;\n  fetch(request: { ids: string[]; namespace?: string }): Promise<{ vectors?: Record<string, { values?: number[] }> }>;\n  delete1(request: { id: string; namespace?: string; deleteAll?: boolean }): Promise<unknown>;\n  deleteAll(request: { namespace?: string }): Promise<unknown>;\n  query(request: { vector: number[]; topK: number; filter?: Record<string, unknown>; namespace?: string }): Promise<{ matches?: { id: string; score: number }[] }>;\n  describeIndexStats(): Promise<unknown>;\n}\n\n/**\n * Configuration interface for PineconeVectorStore\n */\ninterface PineconeConfig {\n  apiKey: string;\n  environment: string;\n  indexName: string;\n  namespace?: string; // Optional namespace for multi-tenancy\n}\n\n/**\n * Pinecone vector store implementation using the official Pinecone client and LangChain integration.\n * Handles initialization, adding, retrieving, deleting, clearing, and searching vectors in a Pinecone index.\n */\nexport class PineconeVectorStore implements IVectorStore {\n  private embeddings: Embeddings;\n  private config: PineconeConfig;\n  private pineconeClient: PineconeClient | undefined; // Pinecone client instance\n  private pineconeIndex: PineconeIndex | undefined;\n  // PineconeStore from LangChain - used primarily for search abstraction if needed,\n  // but direct client usage is often preferred for full feature access.\n  // We will prioritize direct client usage for clarity and control,\n  // but keep the instance if needed for LangChain compatibility methods.\n  private langchainPineconeStore: PineconeStore | undefined;\n  private initialized = false;\n  private isInitializing = false;\n\n  /**\n     * Creates an instance of PineconeVectorStore.\n     * @param embeddings - The embeddings model instance to use (primarily for LangChain compatibility if used).\n     */\n  constructor(embeddings: Embeddings) {\n    this.embeddings = embeddings; // Store embeddings, mainly for potential LangChain Store usage\n\n    const apiKey = getConfig<string>('memory.vectorStore.pinecone.apiKey', '');\n    const environment = getConfig<string>('memory.vectorStore.pinecone.environment', '');\n    const indexName = getConfig<string>('memory.vectorStore.pinecone.indexName', 'codessa-memories');\n    const namespace = getConfig<string | undefined>('memory.vectorStore.pinecone.namespace', undefined); // Allow undefined namespace\n\n    if (!apiKey || !environment || !indexName) {\n      throw new Error('Pinecone configuration (apiKey, environment, indexName) is missing.');\n    }\n\n    this.config = {\n      apiKey,\n      environment,\n      indexName,\n      namespace,\n    };\n\n    logger.info(`PineconeVectorStore configured: Index='${this.config.indexName}', Environment='${this.config.environment}', Namespace='${this.config.namespace ?? '(default)'}'`);\n  }\n\n  /**\n     * Initializes the connection to the Pinecone index.\n     * Ensures the Pinecone client is ready and the target index is accessible.\n     * This method is idempotent.\n     * @throws {Error} If initialization fails (e.g., invalid credentials, index not found).\n     */\n  public async initialize(): Promise<void> {\n    if (this.initialized || this.isInitializing) {\n      if (this.isInitializing) {\n        // Wait for the ongoing initialization to complete\n        await new Promise<void>(resolve => {\n          const checkInterval = setInterval(() => {\n            if (!this.isInitializing) {\n              clearInterval(checkInterval);\n              resolve();\n            }\n          }, 100);\n        });\n      }\n      if (this.initialized) {\n        logger.debug('PineconeVectorStore already initialized.');\n      }\n      return;\n    }\n\n    this.isInitializing = true;\n    logger.info(`Initializing Pinecone vector store connection to index '${this.config.indexName}'...`);\n\n    try {\n      // 1. Initialize Pinecone Client\n      this.pineconeClient = new (Pinecone as unknown as new () => PineconeClient)(); // Cast to bypass type error\n      if (!this.pineconeClient) {\n        throw new Error('Failed to create Pinecone client');\n      }\n      await this.pineconeClient.init({\n        apiKey: this.config.apiKey,\n        environment: this.config.environment,\n      });\n      logger.debug('Pinecone client initialized.');\n\n      // 2. Get Index Handle\n      // Note: This does NOT create the index. It must exist beforehand.\n      this.pineconeIndex = this.pineconeClient.Index(this.config.indexName);\n      logger.debug(`Handle obtained for Pinecone index '${this.config.indexName}'.`);\n\n      // 3. Optional: Verify Index Connection (e.g., by describing stats)\n      try {\n        const stats = await this.pineconeIndex.describeIndexStats();\n        logger.info(`Successfully connected to Pinecone index '${this.config.indexName}'. Stats:`, stats);\n        // Store stats if needed, e.g., dimension: stats.dimension\n      } catch (statsError: unknown) {\n        const errorMessage = statsError instanceof Error ? statsError.message : String(statsError);\n        logger.error(`Failed to verify connection or get stats for index '${this.config.indexName}'. Please ensure the index exists and credentials are correct.`, { message: errorMessage });\n        throw new Error(`Failed to connect to Pinecone index '${this.config.indexName}': ${errorMessage}`);\n      }\n\n      // 4. Optional: Initialize LangChain PineconeStore if specific LangChain features are needed\n      // This might be useful if integrating with chains that expect a LangChain VectorStore instance.\n      // Otherwise, direct client usage (this.pineconeIndex) is sufficient.\n      // this.langchainPineconeStore = await PineconeStore.fromExistingIndex(this.embeddings, {\n      //     pineconeIndex: this.pineconeIndex,\n      //     namespace: this.config.namespace,\n      // });\n      // logger.debug('LangChain PineconeStore wrapper initialized (optional).');\n\n\n      this.initialized = true;\n      logger.info(`Pinecone vector store initialized successfully for index '${this.config.indexName}'.`);\n\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error('Failed to initialize Pinecone vector store:', { message: errorMessage, stack: errorStack, environment: this.config.environment, namespace: this.config.namespace });\n      this.initialized = false;\n      this.pineconeClient = undefined;\n      this.pineconeIndex = undefined;\n      this.langchainPineconeStore = undefined;\n      throw new Error(`Pinecone initialization failed: ${errorMessage}`);\n    } finally {\n      this.isInitializing = false;\n    }\n  }\n\n  /**\n     * Throws an error if the vector store is not initialized.\n     */\n  private assertInitialized(): void {\n    if (!this.initialized || !this.pineconeClient || !this.pineconeIndex) {\n      logger.error('PineconeVectorStore accessed before successful initialization.', {\n        initialized: this.initialized,\n        hasClient: !!this.pineconeClient,\n        hasIndex: !!this.pineconeIndex,\n      });\n      throw new Error('PineconeVectorStore is not initialized. Call initialize() first.');\n    }\n  }\n\n  /**\n     * Prepares metadata for Pinecone, ensuring compatibility.\n     * Converts values to supported types (string, number, boolean, string[]).\n     * Removes nested objects and unsupported types. Includes the original text content if provided.\n     * @param metadata Raw metadata object.\n     * @param textContent Optional text content associated with the vector.\n     * @returns Sanitized metadata object suitable for Pinecone.\n     */\n  private sanitizeMetadata(metadata: Record<string, unknown>, textContent?: string): PineconeMetadata {\n    const sanitized: PineconeMetadata = {};\n    const MAX_METADATA_VALUE_LENGTH = 1024; // Example limit, adjust as needed\n    const MAX_TAG_LENGTH = 100;\n    const MAX_TAGS = 50;\n\n    for (const key in metadata) {\n      if (!Object.prototype.hasOwnProperty.call(metadata, key)) {\n        continue;\n      }\n\n      const value = metadata[key];\n\n      if (typeof value === 'string') {\n        sanitized[key] = value.substring(0, MAX_METADATA_VALUE_LENGTH);\n      } else if (typeof value === 'number' && Number.isFinite(value)) {\n        sanitized[key] = value;\n      } else if (typeof value === 'boolean') {\n        sanitized[key] = value;\n      } else if (Array.isArray(value) && value.every(item => typeof item === 'string')) {\n        // Ensure all items are strings and sanitize/limit them\n        sanitized[key] = value\n          .map(tag => tag.substring(0, MAX_TAG_LENGTH))\n          .slice(0, MAX_TAGS);\n      } else {\n        // Log unsupported types/values\n        logger.warn(`[${this.config.indexName}] Metadata key '${key}' has unsupported type '${typeof value}' or value. Skipping.`);\n      }\n    }\n\n    // Optionally include the original text content in metadata if provided and not too long\n    if (textContent && !sanitized.text) { // Avoid overwriting if 'text' already exists\n      sanitized.text = textContent.substring(0, MAX_METADATA_VALUE_LENGTH * 5); // Allow longer text field\n    }\n\n    return sanitized;\n  }\n\n  /**\n     * Adds a vector with associated metadata to the Pinecone index.\n     * Uses the direct Pinecone client for upserting.\n     * @param id - A unique identifier for the vector.\n     * @param vector - The vector embedding.\n     * @param metadata - Metadata associated with the vector. Should contain simple key-value pairs.\n     * @throws {Error} If the store is not initialized or if the upsert operation fails.\n     */\n  public async addVector(id: string, vector: number[], metadata: Record<string, unknown> = {}): Promise<void> {\n    this.assertInitialized();\n\n    // Extract potential text content for metadata inclusion\n    const textContent = metadata?.content ?? metadata?.text ?? ''; // Look for common text fields\n    const sanitizedMeta = this.sanitizeMetadata(metadata, typeof textContent === 'string' ? textContent : undefined);\n\n    const vectorToUpsert = {\n      id,\n      values: vector,\n      metadata: sanitizedMeta,\n    };\n\n    try {\n      logger.debug(`[${this.config.indexName}] Upserting vector with ID ${id} (Namespace: ${this.config.namespace ?? 'none'})...`);\n      const upsertRequest = {\n        vectors: [vectorToUpsert],\n        namespace: this.config.namespace,\n      };\n      if (!this.pineconeIndex) {\n        throw new Error('Pinecone index not initialized');\n      }\n      await this.pineconeIndex.upsert(upsertRequest);\n      logger.info(`[${this.config.indexName}] Successfully upserted vector with ID ${id}.`);\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`[${this.config.indexName}] Failed to upsert vector with ID ${id}:`, { message: errorMessage, stack: errorStack, id, namespace: this.config.namespace });\n      // Provide more context from Pinecone errors if possible\n      if (error && typeof error === 'object' && 'response' in error) {\n        const errorObj = error as { response?: { body?: unknown } };\n        if (errorObj.response?.body) {\n          logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);\n        }\n      }\n      throw new Error(`Failed to add vector ${id} to Pinecone: ${errorMessage}`);\n    }\n  }\n\n  /**\n     * Retrieves a vector embedding by its ID directly from Pinecone.\n     * Note: This uses the direct Pinecone client, as it's not a standard part of the basic LangChain VectorStore interface.\n     * @param id - The unique identifier of the vector to retrieve.\n     * @returns The vector embedding as an array of numbers, or undefined if not found or on error.\n     * @throws {Error} If the store is not initialized.\n     */\n  public async getVector(id: string): Promise<number[] | undefined> {\n    this.assertInitialized();\n\n    try {\n      logger.debug(`[${this.config.indexName}] Fetching vector with ID ${id} (Namespace: ${this.config.namespace ?? 'none'})...`);\n      if (!this.pineconeIndex) {\n        throw new Error('Pinecone index not initialized');\n      }\n      const fetchResponse = await this.pineconeIndex.fetch({ ids: [id], namespace: this.config.namespace });\n\n      const record = fetchResponse?.vectors?.[id];\n      if (record?.values) {\n        logger.debug(`[${this.config.indexName}] Successfully fetched vector for ID ${id}.`);\n        return record.values;\n      } else {\n        logger.debug(`[${this.config.indexName}] Vector with ID ${id} not found.`);\n        return undefined;\n      }\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`[${this.config.indexName}] Failed to fetch vector with ID ${id}:`, { message: errorMessage, stack: errorStack, id, namespace: this.config.namespace });\n      if (error && typeof error === 'object' && 'response' in error) {\n        const errorObj = error as { response?: { body?: unknown } };\n        if (errorObj.response?.body) {\n          logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);\n        }\n      }\n      // Don't throw, return undefined as per method signature on error\n      return undefined;\n    }\n  }\n\n  /**\n     * Deletes a vector by its ID from the Pinecone index.\n     * @param id - The unique identifier of the vector to delete.\n     * @returns `true` if deletion was successful or the vector didn't exist, `false` on error.\n     * @throws {Error} If the store is not initialized.\n     */\n  public async deleteVector(id: string): Promise<boolean> {\n    this.assertInitialized();\n\n    try {\n      logger.debug(`[${this.config.indexName}] Deleting vector with ID ${id} (Namespace: ${this.config.namespace ?? 'none'})...`);\n      if (!this.pineconeIndex) {\n        throw new Error('Pinecone index not initialized');\n      }\n      await this.pineconeIndex.delete1({ id, namespace: this.config.namespace }); // Use delete1 for single ID\n      logger.info(`[${this.config.indexName}] Delete request successful for vector ID ${id} (vector may or may not have existed).`);\n      return true; // Pinecone delete is idempotent, succeeds even if ID doesn't exist\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`[${this.config.indexName}] Failed to delete vector with ID ${id}:`, { message: errorMessage, stack: errorStack, id, namespace: this.config.namespace });\n      if (error && typeof error === 'object' && 'response' in error) {\n        const errorObj = error as { response?: { body?: unknown } };\n        if (errorObj.response?.body) {\n          logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);\n        }\n      }\n      return false;\n    }\n  }\n\n  /**\n     * Clears vectors from the Pinecone index.\n     * If a namespace is configured, only that namespace is cleared.\n     * If no namespace is configured, this will clear **ALL** vectors in the index.\n     * **Warning:** This is a destructive operation.\n     * @throws {Error} If the store is not initialized or if clearing fails.\n     */\n  public async clearVectors(): Promise<void> {\n    this.assertInitialized();\n\n    const target = this.config.namespace ? `namespace '${this.config.namespace}'` : 'the entire index';\n    logger.warn(`[${this.config.indexName}] Attempting to clear all vectors from ${target}...`);\n\n    try {\n      if (this.config.namespace) {\n        if (!this.pineconeIndex) {\n          throw new Error('Pinecone index not initialized');\n        }\n        await this.pineconeIndex.deleteAll({ namespace: this.config.namespace });\n      } else {\n        // Double-check or add safety config before allowing full index delete?\n        // For now, proceed as configured.\n        if (!this.pineconeIndex) {\n          throw new Error('Pinecone index not initialized');\n        }\n        await this.pineconeIndex.deleteAll({});\n      }\n      logger.info(`[${this.config.indexName}] Successfully cleared vectors from ${target}.`);\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`[${this.config.indexName}] Failed to clear vectors from ${target}:`, { message: errorMessage, stack: errorStack, namespace: this.config.namespace });\n      if (error && typeof error === 'object' && 'response' in error) {\n        const errorObj = error as { response?: { body?: unknown } };\n        if (errorObj.response?.body) {\n          logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);\n        }\n      }\n      throw new Error(`Failed to clear Pinecone vectors: ${errorMessage}`);\n    }\n  }\n\n  /**\n     * Searches for vectors similar to the provided query vector within the Pinecone index.\n     * Supports metadata filtering using Pinecone's filter syntax.\n     * @param vector - The query vector embedding.\n     * @param limit - The maximum number of similar vectors to return.\n     * @param filter - Optional metadata filter object conforming to Pinecone's filter syntax.\n     *                 Example: `{ \"genre\": { \"$eq\": \"fiction\" }, \"year\": { \"$gte\": 2020 } }`\n     * @returns A promise resolving to an array of objects, each containing the ID and similarity score.\n     * @throws {Error} If the store is not initialized or if the search fails.\n     */\n  public async searchSimilarVectors(vector: number[], limit = 5, filter?: Record<string, unknown>): Promise<{ id: string; score: number }[]> {\n    this.assertInitialized();\n\n    if (!Array.isArray(vector) || vector.length === 0) {\n      logger.error('Invalid query vector provided for similarity search.');\n      throw new Error('Query vector must be a non-empty array of numbers.');\n    }\n\n    try {\n      logger.debug(`[${this.config.indexName}] Searching for ${limit} similar vectors (Namespace: ${this.config.namespace ?? 'none'})...`, { filter: filter ? 'present' : 'absent' });\n\n      const queryRequest = {\n        vector,\n        topK: limit,\n        namespace: this.config.namespace,\n        filter: filter, // Pass filter directly, assuming it's Pinecone compatible\n        includeMetadata: false, // Don't need metadata here, just ID and score\n        includeValues: false, // Don't need vector values\n      };\n\n      if (!this.pineconeIndex) {\n        throw new Error('Pinecone index not initialized');\n      }\n      const queryResponse = await this.pineconeIndex.query(queryRequest);\n\n      const matches = queryResponse.matches ?? [];\n      logger.debug(`[${this.config.indexName}] Pinecone query returned ${matches.length} matches.`);\n\n      // Map results to the required format { id: string, score: number }\n      const results = matches.map((match: { id: string; score: number }) => ({\n        id: match.id,\n        // Ensure score is a number, default to 0 if missing (shouldn't happen with Pinecone)\n        score: typeof match.score === 'number' ? match.score : 0,\n      }));\n\n      return results;\n\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`[${this.config.indexName}] Failed to search similar vectors:`, { message: errorMessage, stack: errorStack, limit, filter, namespace: this.config.namespace });\n      if (error && typeof error === 'object' && 'response' in error) {\n        const errorObj = error as { response?: { body?: unknown } };\n        if (errorObj.response?.body) {\n          logger.error(`[${this.config.indexName}] Pinecone error details:`, errorObj.response.body);\n        }\n      }\n      // Return empty array on error as per original signature\n      return [];\n    }\n  }\n\n  /**\n     * Provides direct access to the initialized Pinecone Index object for advanced operations.\n     * Use with caution, as it bypasses the IVectorStore abstraction.\n     * @returns The Pinecone Index instance.\n     * @throws {Error} If the store is not initialized.\n     */\n  public getPineconeIndex(): unknown {\n    this.assertInitialized();\n    if (!this.pineconeIndex) {\n      throw new Error('Pinecone index not initialized');\n    }\n    return this.pineconeIndex;\n  }\n}"]}