import { MemoryEntry, MemorySource, MemoryType, IMemoryProvider } from '../../src/memory/types';
import { performance } from 'perf_hooks';

/**
 * Mock Memory Provider for testing
 */
export class MockMemoryProvider implements IMemoryProvider {
  private memories: Map<string, MemoryEntry> = new Map();
  private searchResults: Map<string, MemoryEntry[]> = new Map();
  
  constructor() {
    // Initialize with some test data
    this.initializeTestData();
  }

  private initializeTestData(): void {
    // Add some test memories
    for (let i = 0; i < 1000; i++) {
      const id = `memory-${i}`;
      const type: MemoryType = i % 3 === 0 ? 'fact' : i % 3 === 1 ? 'conversation' : 'code';
      const source: MemorySource = i % 2 === 0 ? 'user' : 'system';
      
      this.memories.set(id, {
        id,
        content: `This is a test memory ${i} of type ${type}`,
        type,
        source,
        timestamp: Date.now() - (i * 1000 * 60 * 60), // Staggered timestamps
        metadata: {
          agentId: 'test-agent',
          relevance: Math.random(),
          tags: [`test`, `type-${type}`, `source-${source}`, `memory-${i}`]
        },
        embedding: this.generateRandomEmbedding(1536)
      });
    }
    
    // Pre-calculate some search results
    this.searchResults.set('test', Array.from(this.memories.values()).slice(0, 5));
    this.searchResults.set('memory', Array.from(this.memories.values()).slice(5, 10));
  }
  
  private generateRandomEmbedding(dimensions: number): number[] {
    return Array.from({ length: dimensions }, () => Math.random() * 2 - 1);
  }

  async addMemory(entry: Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry> {
    const id = `memory-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const memory: MemoryEntry = {
      ...entry,
      id,
      timestamp: Date.now()
    };
    
    this.memories.set(id, memory);
    return memory;
  }

  async getMemory(id: string): Promise<MemoryEntry | undefined> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 10 + Math.random() * 20));
    return this.memories.get(id);
  }

  async searchMemories(
    query: string,
    options: {
      limit?: number;
      minScore?: number;
      type?: MemoryType;
      source?: MemorySource;
      includeEmbeddings?: boolean;
    } = {}
  ): Promise<MemoryEntry[]> {
    // Simulate network delay and search time
    await new Promise(resolve => setTimeout(resolve, 20 + Math.random() * 30));
    
    // Return pre-calculated results or a subset of memories
    const results = this.searchResults.get(query) || 
      Array.from(this.memories.values())
        .filter(memory => {
          if (options.type && memory.type !== options.type) return false;
          if (options.source && memory.source !== options.source) return false;
          return true;
        })
        .slice(0, options.limit || 5);
    
    return results;
  }

  async updateMemory(id: string, updates: Partial<MemoryEntry>): Promise<MemoryEntry | undefined> {
    const memory = this.memories.get(id);
    if (!memory) return undefined;
    
    const updatedMemory = { ...memory, ...updates, updatedAt: Date.now() };
    this.memories.set(id, updatedMemory);
    return updatedMemory;
  }

  async deleteMemory(id: string): Promise<boolean> {
    return this.memories.delete(id);
  }

  async clearMemories(): Promise<void> {
    this.memories.clear();
  }
  
  // Additional methods for testing
  getMemoryCount(): number {
    return this.memories.size;
  }
  
  // Simulate memory operations for benchmarking
  async simulateMemoryOperations(operations: {
    reads: number;
    writes: number;
    searches: number;
    updates: number;
  }): Promise<{
    duration: number;
    operations: number;
  }> {
    const start = performance.now();
    const totalOperations = operations.reads + operations.writes + operations.searches + operations.updates;
    
    // Execute reads
    for (let i = 0; i < operations.reads; i++) {
      const memories = Array.from(this.memories.values());
      if (memories.length > 0) {
        const randomMemory = memories[Math.floor(Math.random() * memories.length)];
        await this.getMemory(randomMemory.id);
      }
    }
    
    // Execute writes
    for (let i = 0; i < operations.writes; i++) {
      await this.addMemory({
        content: `Test memory ${Date.now()}-${i}`,
        type: 'fact',
        source: 'test',
        metadata: { test: true },
        embedding: this.generateRandomEmbedding(1536)
      });
    }
    
    // Execute searches
    for (let i = 0; i < operations.searches; i++) {
      const terms = ['test', 'memory', 'code', 'conversation', 'fact'];
      const term = terms[Math.floor(Math.random() * terms.length)];
      await this.searchMemories(term, { limit: 5 });
    }
    
    // Execute updates
    for (let i = 0; i < operations.updates; i++) {
      const memories = Array.from(this.memories.values());
      if (memories.length > 0) {
        const randomMemory = memories[Math.floor(Math.random() * memories.length)];
        await this.updateMemory(randomMemory.id, {
          metadata: { ...randomMemory.metadata, updated: true, updateCount: ((randomMemory.metadata.updateCount as number) || 0) + 1 }
        });
      }
    }
    
    return {
      duration: performance.now() - start,
      operations: totalOperations
    };
  }
}
