{"version": 3, "file": "mcpWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/mcpWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAaH,8CA2BC;AArCD,2CAAkD;AAIlD,8CAA8C;AAC9C,mCAAuC;AAEvC;;GAEG;AACH,SAAgB,iBAAiB,CAC/B,KAAY,EACZ,UAII,EAAE;IAEN,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,cAAc,CAAC;IAC5C,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,iDAAiD,CAAC;IAE7F,0CAA0C;IAC1C,gBAAU,CAAC,aAAa,CACtB,mBAAmB,EACnB,6BAA6B,EAC7B,EAAE,IAAI,EAAE,WAAW,EAAE,EACrB,EAAE,UAAU,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,CAC7C,CAAC;IAEF,kBAAkB;IAClB,OAAO,IAAA,+BAAmB,EACxB,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE,EAC5B,IAAI,EACJ,WAAW,EACX,KAAK,EACL,OAAO,CAAC,KAAK,IAAI,EAAE,CACpB,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Model Context Protocol (MCP) Workflow\n *\n * This module provides a workflow for the Model Context Protocol (MCP),\n * which is a standard for exchanging context between different AI models\n * and tools.\n */\n\nimport { StructuredTool } from './corePolyfill';\nimport { createReActWorkflow } from './templates';\nimport { GraphDefinition } from './types';\nimport { Agent } from '../agentUtilities/agent';\n\n// Import the MCPManager from its new location\nimport { mcpManager } from '../../mcp';\n\n/**\n * Creates an MCP workflow\n */\nexport function createMCPWorkflow(\n  agent: Agent,\n  options: {\n    name?: string;\n    description?: string;\n    tools?: StructuredTool[];\n  } = {}\n): GraphDefinition {\n  const name = options.name || 'MCP Workflow';\n  const description = options.description || 'Workflow for Model Context Protocol integration';\n\n  // Log that we're creating an MCP workflow\n  mcpManager.addToolResult(\n    'createMCPWorkflow',\n    'Creating a new MCP workflow',\n    { name, description },\n    { workflowId: `mcp-workflow-${Date.now()}` }\n  );\n\n  // Create workflow\n  return createReActWorkflow(\n    `mcp-workflow-${Date.now()}`,\n    name,\n    description,\n    agent,\n    options.tools || []\n  );\n}\n"]}