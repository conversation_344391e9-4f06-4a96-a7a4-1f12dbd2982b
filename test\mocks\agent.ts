import { Agent as BaseAgent, <PERSON><PERSON><PERSON><PERSON>n<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '../../src/agents/agentUtilities/agent';
import { ToolRegistry } from '../../src/tools/toolRegistry';
import { v4 as uuidv4 } from 'uuid';
import { performance } from 'perf_hooks';

/**
 * Mock Tool for testing
 */
class MockTool {
  name: string;
  description: string;
  
  constructor(name: string, description: string) {
    this.name = name;
    this.description = description;
  }
  
  async execute(_input: any): Promise<any> {
    // Simulate tool execution time
    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
    return { success: true, output: `Mock output from ${this.name}` };
  }
}

/**
 * Mock Agent for testing
 */
export class MockAgent extends BaseAgent {
  private responseTime: number;
  private successRate: number;
  
  constructor(config: {
    id?: string;
    name: string;
    description?: string;
    role?: AgentRole;
    responseTime?: number;
    successRate?: number;
  }) {
    super({
      id: config.id || `agent-${uuidv4()}`,
      name: config.name,
      description: config.description || `A mock agent for testing: ${config.name}`,
      role: config.role || 'assistant',
      llmProvider: 'mock',
      llmModel: 'mock-model',
      temperature: 0.7,
      maxTokens: 2000,
      systemPromptName: 'mock-prompt',
      tools: ['mock-tool-1', 'mock-tool-2'],
      capabilities: ['testing', 'benchmarking']
    });
    
    this.responseTime = config.responseTime || 100; // ms
    this.successRate = config.successRate ?? 0.95; // 95% success rate by default
    
    // Register mock tools
    const toolRegistry = ToolRegistry.instance;
    if (!toolRegistry.getTool('mock-tool-1')) {
      toolRegistry.registerTool(new MockTool('mock-tool-1', 'First mock tool for testing'));
    }
    if (!toolRegistry.getTool('mock-tool-2')) {
      toolRegistry.registerTool(new MockTool('mock-tool-2', 'Second mock tool for testing'));
    }
  }
  
  /**
   * Simulate agent execution
   */
  public async run(input: AgentRunInput): Promise<AgentRunResult> {
    const startTime = performance.now();
    
    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, this.responseTime * (0.8 + Math.random() * 0.4)));
      
      // Simulate occasional failures
      const shouldFail = Math.random() > this.successRate;
      if (shouldFail) {
        throw new Error('Simulated agent execution failure');
      }
      
      // Generate a response
      const response = {
        output: `Mock response to: ${input.prompt || 'No prompt provided'}`,
        metadata: {
          model: 'mock-model',
          tokens: 42,
          duration: performance.now() - startTime,
          success: true
        }
      };
      
      return response;
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          model: 'mock-model',
          duration: performance.now() - startTime,
          success: false
        }
      };
    }
  }
  
  /**
   * Simulate tool execution
   */
  public async executeTool(toolName: string, input: any): Promise<ToolResult> {
    const startTime = performance.now();
    
    try {
      const tool = ToolRegistry.instance.getTool(toolName);
      if (!tool) {
        throw new Error(`Tool not found: ${toolName}`);
      }
      
      const result = await tool.execute(input);
      
      return {
        tool: toolName,
        input,
        output: result,
        metadata: {
          duration: performance.now() - startTime,
          success: true
        }
      };
    } catch (error) {
      return {
        tool: toolName,
        input,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          duration: performance.now() - startTime,
          success: false
        }
      };
    }
  }
  
  /**
   * Simulate a workflow step execution
   */
  public async executeWorkflowStep(step: { id: string; name: string }, context: any): Promise<any> {
    const startTime = performance.now();
    
    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, this.responseTime * (0.8 + Math.random() * 0.4)));
      
      // Simulate occasional failures
      const shouldFail = Math.random() > this.successRate;
      if (shouldFail) {
        throw new Error(`Simulated failure in step: ${step.name}`);
      }
      
      // Return a mock result
      return {
        success: true,
        output: `Completed step: ${step.name}`,
        metadata: {
          stepId: step.id,
          duration: performance.now() - startTime,
          agent: this.name
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          stepId: step.id,
          duration: performance.now() - startTime,
          agent: this.name
        }
      };
    }
  }
}
