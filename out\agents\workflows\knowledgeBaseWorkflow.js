"use strict";
/**
 * Knowledge Base Management Workflow
 *
 * This module provides a workflow for managing knowledge bases:
 * - Ingesting and processing documents
 * - Organizing knowledge
 * - Retrieving relevant information
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createKnowledgeBaseWorkflow = createKnowledgeBaseWorkflow;
exports.createKnowledgeRetrievalWorkflow = createKnowledgeRetrievalWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
/**
 * Create a Knowledge Base Management workflow
 */
function createKnowledgeBaseWorkflow(id, name, description, knowledgeAgent, tools = []) {
    logger_1.logger.info(`Creating Knowledge Base Management workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const documentIngestionNode = graph_1.Codessa.createAgentNode('document-ingestion', 'Document Ingestion', knowledgeAgent);
    const contentExtractionNode = graph_1.Codessa.createAgentNode('content-extraction', 'Content Extraction', knowledgeAgent);
    const knowledgeOrganizationNode = graph_1.Codessa.createAgentNode('knowledge-organization', 'Knowledge Organization', knowledgeAgent);
    const indexingNode = graph_1.Codessa.createAgentNode('indexing', 'Indexing', knowledgeAgent);
    const metadataEnrichmentNode = graph_1.Codessa.createAgentNode('metadata-enrichment', 'Metadata Enrichment', knowledgeAgent);
    const qualityAssuranceNode = graph_1.Codessa.createAgentNode('quality-assurance', 'Quality Assurance', knowledgeAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-ingestion', source: 'input', target: 'document-ingestion', type: 'default' },
        { name: 'ingestion-to-extraction', source: 'document-ingestion', target: 'content-extraction', type: 'default' },
        { name: 'extraction-to-organization', source: 'content-extraction', target: 'knowledge-organization', type: 'default' },
        { name: 'organization-to-indexing', source: 'knowledge-organization', target: 'indexing', type: 'default' },
        { name: 'indexing-to-enrichment', source: 'indexing', target: 'metadata-enrichment', type: 'default' },
        { name: 'enrichment-to-quality', source: 'metadata-enrichment', target: 'quality-assurance', type: 'default' },
        { name: 'quality-to-output', source: 'quality-assurance', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'quality-to-organization', source: 'quality-assurance', target: 'knowledge-organization', type: 'feedback' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect content extraction to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `extraction-to-tool-${index}`,
                source: 'content-extraction',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to knowledge organization
            edges.push({
                name: `tool-${index}-to-organization`,
                source: `tool-${index}`,
                target: 'knowledge-organization',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            documentIngestionNode,
            contentExtractionNode,
            knowledgeOrganizationNode,
            indexingNode,
            metadataEnrichmentNode,
            qualityAssuranceNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'knowledge-base',
        tags: ['knowledge-base', 'document-processing', 'information-retrieval']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a specialized Knowledge Retrieval workflow
 */
function createKnowledgeRetrievalWorkflow(id, name, description, retrievalAgent) {
    logger_1.logger.info(`Creating Knowledge Retrieval workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const queryAnalysisNode = graph_1.Codessa.createAgentNode('query-analysis', 'Query Analysis', retrievalAgent);
    const semanticSearchNode = graph_1.Codessa.createAgentNode('semantic-search', 'Semantic Search', retrievalAgent);
    const keywordSearchNode = graph_1.Codessa.createAgentNode('keyword-search', 'Keyword Search', retrievalAgent);
    const metadataFilteringNode = graph_1.Codessa.createAgentNode('metadata-filtering', 'Metadata Filtering', retrievalAgent);
    const resultRankingNode = graph_1.Codessa.createAgentNode('result-ranking', 'Result Ranking', retrievalAgent);
    const contextGenerationNode = graph_1.Codessa.createAgentNode('context-generation', 'Context Generation', retrievalAgent);
    const responseFormattingNode = graph_1.Codessa.createAgentNode('response-formatting', 'Response Formatting', retrievalAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create edges
    const edges = [
        { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },
        { name: 'query-to-semantic', source: 'query-analysis', target: 'semantic-search', type: 'default' },
        { name: 'query-to-keyword', source: 'query-analysis', target: 'keyword-search', type: 'default' },
        { name: 'semantic-to-filtering', source: 'semantic-search', target: 'metadata-filtering', type: 'default' },
        { name: 'keyword-to-filtering', source: 'keyword-search', target: 'metadata-filtering', type: 'default' },
        { name: 'filtering-to-ranking', source: 'metadata-filtering', target: 'result-ranking', type: 'default' },
        { name: 'ranking-to-context', source: 'result-ranking', target: 'context-generation', type: 'default' },
        { name: 'context-to-formatting', source: 'context-generation', target: 'response-formatting', type: 'default' },
        { name: 'formatting-to-output', source: 'response-formatting', target: 'output', type: 'default' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            queryAnalysisNode,
            semanticSearchNode,
            keywordSearchNode,
            metadataFilteringNode,
            resultRankingNode,
            contextGenerationNode,
            responseFormattingNode,
            outputNode
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'knowledge-base',
        tags: ['knowledge-retrieval', 'search', 'information-retrieval']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=knowledgeBaseWorkflow.js.map