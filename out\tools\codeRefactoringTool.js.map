{"version": 3, "file": "codeRefactoringTool.js", "sourceRoot": "", "sources": ["../../src/tools/codeRefactoringTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,6BAAwB;AACxB,2CAA6B;AAE7B,kDAA+C;AAG/C;;GAEG;AACH,MAAa,qBAAqB;IACvB,EAAE,GAAG,mBAAmB,CAAC;IACzB,IAAI,GAAG,qBAAqB,CAAC;IAC7B,WAAW,GAAG,6DAA6D,CAAC;IAC5E,IAAI,GAAG,eAAe,CAAC;IACvB,OAAO,GAAwB,EAAE,CAAC;IAClC,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;QAC7D,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,IAAI,CAAC;YACzB,aAAa;YACb,aAAa;YACb,qBAAqB;YACrB,qBAAqB;YACrB,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;YACnB,KAAK;SACN,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;KAC9E,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8BAA8B,EAAE;YACzE,UAAU,EAAE;gBACV,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE;wBACJ,aAAa;wBACb,aAAa;wBACb,qBAAqB;wBACrB,qBAAqB;wBACrB,gBAAgB;wBAChB,cAAc;wBACd,iBAAiB;wBACjB,mBAAmB;wBACnB,KAAK;qBACN;iBACF;gBACD,WAAW,EAAE,oDAAoD;aAClE;SACF;QACD,QAAQ,EAAE,CAAC,UAAU,CAAC;KACvB,CAAC;IAEM,SAAS,GAAc;QAC7B,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,eAAe;QACxB,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;KAC9B,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;YAC1C,MAAM,UAAU,GAAI,KAAK,CAAC,UAAuB,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B;oBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACtC,0EAA0E;gBAC1E,IAAI,MAAM,GAAG,KAAK,CAAC;gBACnB,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACpC,MAAM,GAAG,IAAI,CAAC;gBAChB,CAAC;gBAAC,MAAM,CAAC;oBACP,MAAM,GAAG,KAAK,CAAC;gBACjB,CAAC;gBAED,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;wBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,yBAAyB,KAAK,EAAE;oBACvC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,mBAAmB;YACnB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3D,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;YAErC,6CAA6C;YAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;YAEvF,2CAA2C;YAC3C,IAAI,UAAU,GAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;YAC3C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACvE,IAAI,QAAQ,EAAE,CAAC;oBACb,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,GAAG;oBACX,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,wBAAwB,KAAK,EAAE;iBACvC,CAAC;YACJ,CAAC;YAED,kBAAkB;YAClB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YAExE,2BAA2B;YAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;YAEtE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,UAAU,EAAE,eAAe;oBAC3B,eAAe;oBACf,OAAO,EAAE;wBACP,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;wBACnD,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC;wBAC7D,QAAQ,EAAE;4BACR,IAAI,EAAE,QAAQ;4BACd,QAAQ;4BACR,IAAI,EAAE,IAAI,CAAC,MAAM;4BACjB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;yBAC/B;qBACF;iBACF;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACtC,CAAC,aAAa,EAAE,aAAa,EAAE,qBAAqB,EAAE,qBAAqB;4BACzE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,CAAC;wBAC7E,UAAU;iBACb;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,gCAAgC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC/D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,yBAAyB,CAAC,IAAY,EAAE,aAAqB,EAAE,KAAe;QACpF,MAAM,OAAO,GAA0B;YACrC,aAAa,EAAE,EAAE;YACjB,aAAa,EAAE,EAAE;YACjB,qBAAqB,EAAE,EAAE;YACzB,qBAAqB,EAAE,EAAE;YACzB,gBAAgB,EAAE,EAAE;YACpB,cAAc,EAAE,EAAE;YAClB,iBAAiB,EAAE,EAAE;YACrB,mBAAmB,EAAE,EAAE;SACxB,CAAC;QAEF,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE/B,wBAAwB;QACxB,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9C,6CAA6C;YAC7C,MAAM,cAAc,GAA2B;gBAC7C,KAAK,EAAE,yHAAyH;gBAChI,KAAK,EAAE,oJAAoJ;gBAC3J,KAAK,EAAE,6BAA6B;gBACpC,OAAO,EAAE,qEAAqE;gBAC9E,KAAK,EAAE,8EAA8E;gBACrF,MAAM,EAAE,mCAAmC;gBAC3C,KAAK,EAAE,aAAa;gBACpB,KAAK,EAAE,+BAA+B;gBACtC,KAAK,EAAE,6BAA6B;aACrC,CAAC;YAEF,MAAM,OAAO,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;YAEvE,yBAAyB;YACzB,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;YACrB,IAAI,UAAU,GAAG,EAAE,CAAC;YACpB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;YAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,yBAAyB;gBACzB,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBAClC,IAAI,KAAK,EAAE,CAAC;wBACV,WAAW,GAAG,CAAC,CAAC;wBAChB,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC;wBACzE,QAAQ,GAAG,IAAI,CAAC;wBAChB,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;wBAEjF,kCAAkC;wBAClC,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;4BAC5B,4CAA4C;4BAC5C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;4BACd,2CAA2C;4BAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;4BACvC,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;4BAE3D,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gCACxB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gCAC1B,2CAA2C;gCAC3C,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gCAC/C,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gCAEnE,2EAA2E;gCAC3E,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,UAAU,IAAI,UAAU,EAAE,CAAC;oCACvD,MAAM;gCACR,CAAC;gCAED,CAAC,EAAE,CAAC;4BACN,CAAC;4BAED,MAAM,YAAY,GAAG,CAAC,GAAG,WAAW,CAAC;4BAErC,qDAAqD;4BACrD,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;gCACtB,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;oCAC1B,IAAI,EAAE,UAAU;oCAChB,SAAS,EAAE,WAAW,GAAG,CAAC,EAAE,uBAAuB;oCACnD,OAAO,EAAE,CAAC;oCACV,MAAM,EAAE,YAAY;oCACpB,QAAQ,EAAE,YAAY,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;oCAC3E,WAAW,EAAE,WAAW,UAAU,QAAQ,YAAY,iEAAiE;iCACxH,CAAC,CAAC;4BACL,CAAC;4BAED,QAAQ,GAAG,KAAK,CAAC;wBACnB,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;oBACnC,8CAA8C;oBAC9C,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBAElF,4BAA4B;oBAC5B,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;wBACrB,MAAM,YAAY,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC;wBAEzC,qDAAqD;wBACrD,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;4BACtB,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;gCAC1B,IAAI,EAAE,UAAU;gCAChB,SAAS,EAAE,WAAW,GAAG,CAAC,EAAE,uBAAuB;gCACnD,OAAO,EAAE,CAAC,GAAG,CAAC;gCACd,MAAM,EAAE,YAAY;gCACpB,QAAQ,EAAE,YAAY,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;gCAC3E,WAAW,EAAE,WAAW,UAAU,QAAQ,YAAY,iEAAiE;6BACxH,CAAC,CAAC;wBACL,CAAC;wBAED,QAAQ,GAAG,KAAK,CAAC;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9C,mCAAmC;YACnC,MAAM,aAAa,GAA2B;gBAC5C,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,eAAe;gBACtB,OAAO,EAAE,eAAe;gBACxB,KAAK,EAAE,eAAe;gBACtB,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,uBAAuB;gBAC9B,KAAK,EAAE,gBAAgB;aACxB,CAAC;YAEF,MAAM,OAAO,GAAG,aAAa,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;YAErE,wBAAwB;YACxB,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC;YACpB,IAAI,SAAS,GAAG,EAAE,CAAC;YACnB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,WAAW,GAAG,CAAC,CAAC;YAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,wBAAwB;gBACxB,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBAClC,IAAI,KAAK,EAAE,CAAC;wBACV,UAAU,GAAG,CAAC,CAAC;wBACf,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBACrB,OAAO,GAAG,IAAI,CAAC;wBACf,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;wBACjF,WAAW,GAAG,CAAC,CAAC;wBAEhB,kCAAkC;wBAClC,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;4BAC5B,2CAA2C;4BAC3C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;4BACd,2CAA2C;4BAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;4BACvC,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;4BAE3D,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gCACxB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gCAC1B,2CAA2C;gCAC3C,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gCAC/C,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gCAEnE,gBAAgB;gCAChB,IAAI,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;oCACpC,WAAW,EAAE,CAAC;gCAChB,CAAC;gCAED,0EAA0E;gCAC1E,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,UAAU,IAAI,UAAU,EAAE,CAAC;oCACvD,MAAM;gCACR,CAAC;gCAED,CAAC,EAAE,CAAC;4BACN,CAAC;4BAED,MAAM,WAAW,GAAG,CAAC,GAAG,UAAU,CAAC;4BAEnC,qEAAqE;4BACrE,IAAI,WAAW,GAAG,GAAG,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;gCAC1C,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;oCAC1B,IAAI,EAAE,SAAS;oCACf,SAAS,EAAE,UAAU,GAAG,CAAC,EAAE,uBAAuB;oCAClD,OAAO,EAAE,CAAC;oCACV,MAAM,EAAE,WAAW;oCACnB,OAAO,EAAE,WAAW;oCACpB,QAAQ,EAAE,WAAW,GAAG,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;oCACnH,WAAW,EAAE,UAAU,SAAS,QAAQ,WAAW,oBAAoB,WAAW,gEAAgE;iCACnJ,CAAC,CAAC;4BACL,CAAC;4BAED,OAAO,GAAG,KAAK,CAAC;wBAClB,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;oBACnC,8CAA8C;oBAC9C,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBAElF,gBAAgB;oBAChB,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC;wBAC5E,WAAW,EAAE,CAAC;oBAChB,CAAC;oBAED,2BAA2B;oBAC3B,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;wBACrB,MAAM,WAAW,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;wBAEvC,qEAAqE;wBACrE,IAAI,WAAW,GAAG,GAAG,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;4BAC1C,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;gCAC1B,IAAI,EAAE,SAAS;gCACf,SAAS,EAAE,UAAU,GAAG,CAAC,EAAE,uBAAuB;gCAClD,OAAO,EAAE,CAAC,GAAG,CAAC;gCACd,MAAM,EAAE,WAAW;gCACnB,OAAO,EAAE,WAAW;gCACpB,QAAQ,EAAE,WAAW,GAAG,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;gCACnH,WAAW,EAAE,UAAU,SAAS,QAAQ,WAAW,oBAAoB,WAAW,gEAAgE;6BACnJ,CAAC,CAAC;wBACL,CAAC;wBAED,OAAO,GAAG,KAAK,CAAC;oBAClB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACtD,2CAA2C;YAC3C,MAAM,aAAa,GAA2B;gBAC5C,KAAK,EAAE,iHAAiH;gBACxH,KAAK,EAAE,4IAA4I;gBACnJ,KAAK,EAAE,yBAAyB;gBAChC,OAAO,EAAE,gEAAgE;gBACzE,KAAK,EAAE,yEAAyE;gBAChF,MAAM,EAAE,8BAA8B;gBACtC,KAAK,EAAE,2BAA2B;gBAClC,KAAK,EAAE,0BAA0B;gBACjC,KAAK,EAAE,wBAAwB;aAChC,CAAC;YAEF,MAAM,OAAO,GAAG,aAAa,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;YAErE,yCAAyC;YACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAElC,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC9E,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;oBAEzE,gFAAgF;oBAChF,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;wBACnB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;wBAC/C,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;wBAE9D,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;4BAClC,IAAI,EAAE,UAAU;4BAChB,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,uBAAuB;4BACpC,cAAc,EAAE,UAAU;4BAC1B,UAAU,EAAE,MAAM;4BAClB,QAAQ,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;4BACrE,WAAW,EAAE,WAAW,UAAU,SAAS,UAAU,qEAAqE;yBAC3H,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACpD,2BAA2B;YAC3B,MAAM,aAAa,GAAG,0BAA0B,CAAC;YACjD,IAAI,KAAK,CAAC;YAEV,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBACnD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;gBAErE,uCAAuC;gBACvC,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC;gBAChC,IAAI,UAAU,GAAG,CAAC,CAAC;gBACnB,IAAI,GAAG,GAAG,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBAExC,OAAO,UAAU,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;oBAC3C,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;wBAAE,UAAU,EAAE,CAAC;oBACpC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG;wBAAE,UAAU,EAAE,CAAC;oBACpC,GAAG,EAAE,CAAC;gBACR,CAAC;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gBACpD,MAAM,SAAS,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;gBAE9D,6EAA6E;gBAC7E,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;oBAClB,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC;wBAChC,IAAI,EAAE,UAAU;wBAChB,SAAS;wBACT,QAAQ,EAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;wBACpE,WAAW,EAAE,yBAAyB,SAAS,4EAA4E;qBAC5H,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,6CAA6C;QAC7C,IAAI,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACtD,4DAA4D;YAC5D,MAAM,cAAc,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAEhH,wDAAwD;YACxD,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;gBAClC,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,MAAM,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;gBACrD,6FAA6F;gBAC7F,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;gBAEvD,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;oBACxB,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;wBAClC,aAAa,EAAE,IAAI;wBACnB,WAAW,EAAE,OAAO,CAAC,MAAM;wBAC3B,QAAQ,EAAE,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;wBAC/E,WAAW,EAAE,oCAAoC,IAAI,MAAM,OAAO,CAAC,MAAM,yDAAyD;qBACnI,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,IAAY,EAAE,QAAgB,EAAE,KAAe,EAAE,QAAa;QAClG,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC;YAC7B,CAAC,aAAa,EAAE,aAAa,EAAE,qBAAqB,EAAE,qBAAqB;gBACzE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,CAAC;YAC7E,KAAK,CAAC;QAER,yBAAyB;QACzB,MAAM,MAAM,GAAG;wBACK,QAAQ,oDAAoD,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;QASnG,QAAQ;EACd,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BL,CAAC;QAEE,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC;YACrC,MAAM;YACN,YAAY,EAAE,kJAAkJ;YAChK,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;YAC/B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;SAChC,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;QACnD,CAAC;QAED,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACtD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,OAAO;oBACL,SAAS,EAAE,IAAI;oBACf,UAAU,EAAE,YAAY,CAAC,UAAU,IAAI,EAAE;iBAC1C,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,0CAA0C;iBAClD,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,iCAAiC,KAAK,EAAE;aAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,cAAqC,EAAE,UAAe;QAC3E,MAAM,QAAQ,GAA0B,EAAE,GAAG,cAAc,EAAE,CAAC;QAE9D,gEAAgE;QAChE,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAClD,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACnE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpB,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACtB,CAAC;gBAED,oDAAoD;gBACpD,KAAK,MAAM,KAAK,IAAI,MAAe,EAAE,CAAC;oBACpC,mEAAmE;oBACnE,IAAI,WAAW,GAAG,KAAK,CAAC;oBAExB,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,KAAK,aAAa,EAAE,CAAC;wBACrD,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAC3C,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;4BAC5B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CACnD,CAAC;oBACJ,CAAC;yBAAM,IAAI,IAAI,KAAK,qBAAqB,EAAE,CAAC;wBAC1C,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAC3C,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;4BAC5B,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAC7B,CAAC;oBACJ,CAAC;yBAAM,IAAI,IAAI,KAAK,mBAAmB,EAAE,CAAC;wBACxC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAC3C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CACzC,CAAC;oBACJ,CAAC;yBAAM,IAAI,IAAI,KAAK,qBAAqB,EAAE,CAAC;wBAC1C,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAC3C,QAAQ,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,CAC/C,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAC3C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CACnD,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC,WAAW,EAAE,CAAC;wBACjB,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;4BAClB,GAAG,KAAK;4BACR,MAAM,EAAE,KAAK,CAAC,0BAA0B;yBACzC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,uBAAuB,CAAC,OAA8B;QAC5D,MAAM,eAAe,GAA6B,EAAE,CAAC;QAErD,8BAA8B;QAC9B,IAAI,OAAO,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChE,eAAe,CAAC,aAAa,CAAC,GAAG;gBAC/B,qDAAqD;gBACrD,2EAA2E;gBAC3E,2FAA2F;gBAC3F,mGAAmG;aACpG,CAAC;QACJ,CAAC;QAED,8BAA8B;QAC9B,IAAI,OAAO,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChE,eAAe,CAAC,aAAa,CAAC,GAAG;gBAC/B,yDAAyD;gBACzD,8FAA8F;gBAC9F,oEAAoE;gBACpE,+DAA+D;aAChE,CAAC;QACJ,CAAC;QAED,sCAAsC;QACtC,IAAI,OAAO,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,qBAAqB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChF,eAAe,CAAC,qBAAqB,CAAC,GAAG;gBACvC,gEAAgE;gBAChE,6DAA6D;gBAC7D,gFAAgF;gBAChF,iFAAiF;aAClF,CAAC;QACJ,CAAC;QAED,sCAAsC;QACtC,IAAI,OAAO,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,qBAAqB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChF,eAAe,CAAC,qBAAqB,CAAC,GAAG;gBACvC,uDAAuD;gBACvD,yDAAyD;gBACzD,kGAAkG;gBAClG,wGAAwG;aACzG,CAAC;QACJ,CAAC;QAED,iCAAiC;QACjC,IAAI,OAAO,CAAC,gBAAgB,CAAC,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,eAAe,CAAC,gBAAgB,CAAC,GAAG;gBAClC,iEAAiE;gBACjE,wEAAwE;gBACxE,8DAA8D;gBAC9D,mDAAmD;aACpD,CAAC;QACJ,CAAC;QAED,+BAA+B;QAC/B,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClE,eAAe,CAAC,cAAc,CAAC,GAAG;gBAChC,wDAAwD;gBACxD,0CAA0C;gBAC1C,8EAA8E;gBAC9E,8DAA8D;aAC/D,CAAC;QACJ,CAAC;QAED,kCAAkC;QAClC,IAAI,OAAO,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxE,eAAe,CAAC,iBAAiB,CAAC,GAAG;gBACnC,sDAAsD;gBACtD,0DAA0D;gBAC1D,2DAA2D;gBAC3D,4DAA4D;aAC7D,CAAC;QACJ,CAAC;QAED,oCAAoC;QACpC,IAAI,OAAO,CAAC,mBAAmB,CAAC,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5E,eAAe,CAAC,mBAAmB,CAAC,GAAG;gBACrC,8CAA8C;gBAC9C,oEAAoE;gBACpE,+DAA+D;gBAC/D,8EAA8E;aAC/E,CAAC;QACJ,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,gBAAgB,CAAC,OAA8B;QACrD,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACpF,CAAC;IAEO,oBAAoB,CAAC,OAA8B;QACzD,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAEjD,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5C,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,QAAQ,IAAI,SAAS,EAAE,CAAC;oBAChC,SAAS,CAAC,KAAK,CAAC,QAAkC,CAAC,EAAE,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAztBD,sDAytBC;AAED;;GAEG;AACH,MAAa,sBAAsB;IACxB,EAAE,GAAG,oBAAoB,CAAC;IAC1B,IAAI,GAAG,qBAAqB,CAAC;IAC7B,WAAW,GAAG,oEAAoE,CAAC;IACnF,IAAI,GAAG,cAAc,CAAC;IAC/B,0EAA0E;IACjE,OAAO,GAA2B;QACzC,SAAS,EAAE;YACT,WAAW,EAAE,yCAAyC;YACtD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC7D,gBAAgB,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,IAAI,CAAC;oBAC/B,gBAAgB;oBAChB,eAAe;oBACf,4BAA4B;oBAC5B,uCAAuC;oBACvC,mBAAmB;oBACnB,sBAAsB;oBACtB,KAAK;iBACN,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kDAAkD,CAAC;aAC5E,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC7D,gBAAgB,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,IAAI,CAAC;oBAC/B,gBAAgB;oBAChB,eAAe;oBACf,4BAA4B;oBAC5B,uCAAuC;oBACvC,mBAAmB;oBACnB,sBAAsB;oBACtB,KAAK;iBACN,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kDAAkD,CAAC;aAC5E,CAAC;SACH;QACD,SAAS,EAAE;YACT,WAAW,EAAE,gCAAgC;YAC7C,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+BAA+B,CAAC;gBAC9D,eAAe,EAAE,OAAC,CAAC,IAAI,CAAC;oBACtB,gBAAgB;oBAChB,eAAe;oBACf,4BAA4B;oBAC5B,uCAAuC;oBACvC,mBAAmB;oBACnB,sBAAsB;iBACvB,CAAC,CAAC,QAAQ,CAAC,iCAAiC,CAAC;gBAC9C,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;gBACrE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;gBACjE,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;aACtG,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+BAA+B,CAAC;gBAC9D,eAAe,EAAE,OAAC,CAAC,IAAI,CAAC;oBACtB,gBAAgB;oBAChB,eAAe;oBACf,4BAA4B;oBAC5B,uCAAuC;oBACvC,mBAAmB;oBACnB,sBAAsB;iBACvB,CAAC,CAAC,QAAQ,CAAC,iCAAiC,CAAC;gBAC9C,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;gBACrE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;gBACjE,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;aACtG,CAAC;SACH;KACF,CAAC;IAEM,SAAS,GAAc;QAC7B,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,eAAe;QACxB,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;KAC9B,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,UAAU,IAAI,WAAW,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACjH,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACrD,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACpD;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,UAAU,EAAE;wBACtC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC9D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAgB,EAAE,UAAkB;QACpE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,gBAAgB,GAAI,KAAK,CAAC,gBAA6B,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B;gBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,0EAA0E;YAC1E,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpC,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,mBAAmB;YACnB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;YAErC,kCAAkC;YAClC,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wDAAwD;oBAC/D,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAEtG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACzB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,WAAW,EAAE,WAAW,CAAC,YAAY;oBACrC,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,YAAY,CAAC;iBACnE;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,gBAAgB,EAAE,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBAClD,CAAC,gBAAgB,EAAE,eAAe,EAAE,4BAA4B;4BAC9D,uCAAuC,EAAE,mBAAmB,EAAE,sBAAsB,CAAC,CAAC,CAAC;wBACzF,gBAAgB;iBACnB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACjE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAgB,EAAE,UAAkB;QACnE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,eAAe,GAAG,KAAK,CAAC,eAAyB,CAAC;QACxD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAmB,CAAC;QAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QACxC,MAAM,OAAO,GAAG,KAAK,CAAC,OAA8B,IAAI,EAAE,CAAC;QAE3D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B;gBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC;gBACzC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YAClD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,4BAA4B;gBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YAC9C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,0BAA0B;gBACjC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,0EAA0E;YAC1E,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpC,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,mBAAmB;YACnB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;YAErC,sBAAsB;YACtB,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;YAErC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,SAAS,EAAE,CAAC;gBAC3C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,sBAAsB,SAAS,cAAc,SAAS,SAAS;oBACtE,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,SAAS,EAAE,CAAC;gBAC/C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,oBAAoB,OAAO,cAAc,SAAS,2BAA2B,SAAS,GAAG;oBAChG,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,+BAA+B;YAC/B,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7E,MAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;YACrE,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE9D,iCAAiC;YACjC,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,oDAAoD;oBAC3D,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAE7I,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,QAAQ,EAAE;wBACR,IAAI,EAAE,cAAc;wBACpB,SAAS;wBACT,OAAO;qBACR;oBACD,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,eAAe;iBAChB;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,eAAe;oBACf,SAAS;oBACT,OAAO;iBACR;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC9D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,IAAY,EAAE,QAAgB,EAAE,KAAe,EAAE,QAAa;QACrG,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC;YAC7B,CAAC,gBAAgB,EAAE,eAAe,EAAE,4BAA4B;gBAC9D,uCAAuC,EAAE,mBAAmB,EAAE,sBAAsB,CAAC,CAAC,CAAC;YACzF,KAAK,CAAC;QAER,yBAAyB;QACzB,MAAM,MAAM,GAAG;wBACK,QAAQ,0EAA0E,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;QAQzH,QAAQ;EACd,IAAI;;;;;;;;;;;;;;;CAeL,CAAC;QAEE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC;gBACrC,MAAM;gBACN,YAAY,EAAE,wKAAwK;gBACtL,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;gBAC/B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;YACjD,CAAC;YAED,wCAAwC;YACxC,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACtD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,YAAY,EAAE,YAAY,CAAC,YAAY,IAAI,EAAE;iBAC9C,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0CAA0C;iBAClD,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,0CAA0C,KAAK,EAAE;aACzD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,QAAgB,EAAE,cAAsB,EAAE,SAAiB,EAAE,OAAe,EAAE,eAAuB,EAAE,QAAgB,EAAE,OAA4B,EAAE,QAAa;QAC1M,yBAAyB;QACzB,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;aAC1C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;aACzD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,yBAAyB;QACzB,MAAM,MAAM,GAAG;gDAC6B,QAAQ,oBAAoB,eAAe;;mCAExD,SAAS,IAAI,OAAO;QAC/C,QAAQ;EACd,cAAc;;;EAGd,aAAa,CAAC,CAAC,CAAC,wBAAwB,aAAa,IAAI,CAAC,CAAC,CAAC,EAAE;;;QAGxD,QAAQ;EACd,QAAQ;;;;;;;;;;;;;;;;;;;;;CAqBT,CAAC;QAEE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC;gBACrC,MAAM;gBACN,YAAY,EAAE,4IAA4I;gBAC1J,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;gBAC/B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;YACjD,CAAC;YAED,wCAAwC;YACxC,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACtD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,YAAY,CAAC,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;oBACxE,WAAW,EAAE,YAAY,CAAC,WAAW,IAAI,0BAA0B;iBACpE,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0CAA0C;iBAClD,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC,KAAK,EAAE;aACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,0BAA0B,CAAC,YAAmB;QACpD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,qCAAqC,CAAC;QAC/C,CAAC;QAED,6BAA6B;QAC7B,MAAM,mBAAmB,GAA2B,EAAE,CAAC;QAEvD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;YAC9B,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACnE,CAAC;QAED,mBAAmB;QACnB,MAAM,OAAO,GAAG;YACd,SAAS,YAAY,CAAC,MAAM,6BAA6B;YACzD,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,iBAAiB,CAAC;SACnG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA7fD,wDA6fC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport * as cp from 'child_process';\nimport { z } from 'zod';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport { llmService } from '../llm/llmService';\nimport { LLMConfig } from '../config';\n\n/**\n * Detects code smells and suggests refactoring opportunities\n */\nexport class CodeSmellDetectorTool implements ITool {\n  readonly id = 'codeSmellDetector';\n  readonly name = 'Code Smell Detector';\n  readonly description = 'Detects code smells and suggests refactoring opportunities.';\n  readonly type = 'single-action';\n  readonly actions: Record<string, any> = {};\n  readonly singleActionSchema = z.object({\n    filePath: z.string().describe('Path to the file to analyze.'),\n    smellTypes: z.array(z.enum([\n      'long-method',\n      'large-class',\n      'primitive-obsession',\n      'long-parameter-list',\n      'duplicate-code',\n      'feature-envy',\n      'shotgun-surgery',\n      'switch-statements',\n      'all'\n    ])).optional().describe('Types of code smells to check for. Default is all.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      filePath: { type: 'string', description: 'Path to the file to analyze.' },\n      smellTypes: {\n        type: 'array',\n        items: {\n          type: 'string',\n          enum: [\n            'long-method',\n            'large-class',\n            'primitive-obsession',\n            'long-parameter-list',\n            'duplicate-code',\n            'feature-envy',\n            'shotgun-surgery',\n            'switch-statements',\n            'all'\n          ]\n        },\n        description: 'Types of code smells to check for. Default is all.'\n      }\n    },\n    required: ['filePath']\n  };\n\n  private llmConfig: LLMConfig = {\n    provider: 'openai',\n    modelId: 'gpt-3.5-turbo',\n    options: { temperature: 0.1 }\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      const filePath = input.filePath as string;\n      const smellTypes = (input.smellTypes as string[]) || ['all'];\n\n      if (!filePath) {\n        return {\n          success: false,\n          error: '\\'filePath\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Validate file exists\n      try {\n        const uri = vscode.Uri.file(filePath);\n        // Replace Promise.then().catch() with try/catch for better error handling\n        let exists = false;\n        try {\n          await vscode.workspace.fs.stat(uri);\n          exists = true;\n        } catch {\n          exists = false;\n        }\n\n        if (!exists) {\n          return {\n            success: false,\n            error: `File not found: ${filePath}`,\n            toolId: this.id,\n            actionName\n          };\n        }\n      } catch (error) {\n        return {\n          success: false,\n          error: `Error accessing file: ${error}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Get file content\n      const document = await vscode.workspace.openTextDocument(filePath);\n      const text = document.getText();\n      const fileExtension = path.extname(filePath).toLowerCase();\n      const language = document.languageId;\n\n      // Perform pattern-based code smell detection\n      const patternResults = this.detectCodeSmellsByPattern(text, fileExtension, smellTypes);\n\n      // Use LLM for deeper analysis if available\n      let llmResults: any = { available: false };\n      try {\n        const provider = await llmService.getProviderForConfig(this.llmConfig);\n        if (provider) {\n          llmResults = await this.detectCodeSmellsWithLLM(text, language, smellTypes, provider);\n        }\n      } catch (error) {\n        llmResults = {\n          available: false,\n          error: `LLM analysis failed: ${error}`\n        };\n      }\n\n      // Combine results\n      const combinedResults = this.combineResults(patternResults, llmResults);\n\n      // Generate recommendations\n      const recommendations = this.generateRecommendations(combinedResults);\n\n      return {\n        success: true,\n        output: {\n          codeSmells: combinedResults,\n          recommendations,\n          summary: {\n            totalSmells: this.countTotalSmells(combinedResults),\n            severityBreakdown: this.getSeverityBreakdown(combinedResults),\n            fileInfo: {\n              path: filePath,\n              language,\n              size: text.length,\n              lines: text.split('\\n').length\n            }\n          }\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          smellTypes: smellTypes.includes('all') ?\n            ['long-method', 'large-class', 'primitive-obsession', 'long-parameter-list',\n              'duplicate-code', 'feature-envy', 'shotgun-surgery', 'switch-statements'] :\n            smellTypes\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Code smell detection failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private detectCodeSmellsByPattern(code: string, fileExtension: string, types: string[]): any {\n    const results: Record<string, any[]> = {\n      'long-method': [],\n      'large-class': [],\n      'primitive-obsession': [],\n      'long-parameter-list': [],\n      'duplicate-code': [],\n      'feature-envy': [],\n      'shotgun-surgery': [],\n      'switch-statements': []\n    };\n\n    const checkAll = types.includes('all');\n    const lines = code.split('\\n');\n\n    // Long Method detection\n    if (checkAll || types.includes('long-method')) {\n      // Detect methods/functions based on language\n      const methodPatterns: Record<string, RegExp> = {\n        '.js': /function\\s+(\\w+)\\s*\\([^)]*\\)\\s*\\{|\\w+\\s*=\\s*function\\s*\\([^)]*\\)\\s*\\{|(\\w+)\\s*\\([^)]*\\)\\s*\\{|(\\w+)\\s*=\\s*\\([^)]*\\)\\s*=>/,\n        '.ts': /function\\s+(\\w+)\\s*\\([^)]*\\)\\s*\\{|\\w+\\s*=\\s*function\\s*\\([^)]*\\)\\s*\\{|(\\w+)\\s*\\([^)]*\\)\\s*\\{|(\\w+)\\s*=\\s*\\([^)]*\\)\\s*=>|(\\w+)\\s*:\\s*\\([^)]*\\)\\s*=>/,\n        '.py': /def\\s+(\\w+)\\s*\\([^)]*\\)\\s*:/,\n        '.java': /(?:public|private|protected|static)?\\s*\\w+\\s+(\\w+)\\s*\\([^)]*\\)\\s*\\{/,\n        '.cs': /(?:public|private|protected|internal|static)?\\s*\\w+\\s+(\\w+)\\s*\\([^)]*\\)\\s*\\{/,\n        '.php': /function\\s+(\\w+)\\s*\\([^)]*\\)\\s*\\{/,\n        '.rb': /def\\s+(\\w+)/,\n        '.go': /func\\s+(\\w+)\\s*\\([^)]*\\)\\s*\\{/,\n        '.rs': /fn\\s+(\\w+)\\s*\\([^)]*\\)\\s*\\{/\n      };\n\n      const pattern = methodPatterns[fileExtension] || methodPatterns['.js'];\n\n      // Find method boundaries\n      let methodStart = -1;\n      let methodName = '';\n      let braceCount = 0;\n      let inMethod = false;\n\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i];\n\n        // Check for method start\n        if (!inMethod) {\n          const match = line.match(pattern);\n          if (match) {\n            methodStart = i;\n            methodName = match[1] || match[2] || match[3] || match[4] || 'anonymous';\n            inMethod = true;\n            braceCount = (line.match(/\\{/g) || []).length - (line.match(/\\}/g) || []).length;\n\n            // For Python, we don't use braces\n            if (fileExtension === '.py') {\n              // Find the end of the method by indentation\n              let j = i + 1;\n              // Add null check for regex match operation\n              const indentMatch = line.match(/^\\s*/);\n              const baseIndent = indentMatch ? indentMatch[0].length : 0;\n\n              while (j < lines.length) {\n                const nextLine = lines[j];\n                // Add null check for regex match operation\n                const nextIndentMatch = nextLine.match(/^\\s*/);\n                const nextIndent = nextIndentMatch ? nextIndentMatch[0].length : 0;\n\n                // If we find a line with same or less indentation, we've exited the method\n                if (nextLine.trim() !== '' && nextIndent <= baseIndent) {\n                  break;\n                }\n\n                j++;\n              }\n\n              const methodLength = j - methodStart;\n\n              // Consider methods with more than 20 lines as \"long\"\n              if (methodLength > 20) {\n                results['long-method'].push({\n                  name: methodName,\n                  startLine: methodStart + 1, // 1-based line numbers\n                  endLine: j,\n                  length: methodLength,\n                  severity: methodLength > 50 ? 'high' : methodLength > 30 ? 'medium' : 'low',\n                  description: `Method '${methodName}' is ${methodLength} lines long, which exceeds the recommended maximum of 20 lines.`\n                });\n              }\n\n              inMethod = false;\n            }\n          }\n        } else if (fileExtension !== '.py') {\n          // Update brace count for non-Python languages\n          braceCount += (line.match(/\\{/g) || []).length - (line.match(/\\}/g) || []).length;\n\n          // Check if method has ended\n          if (braceCount === 0) {\n            const methodLength = i - methodStart + 1;\n\n            // Consider methods with more than 20 lines as \"long\"\n            if (methodLength > 20) {\n              results['long-method'].push({\n                name: methodName,\n                startLine: methodStart + 1, // 1-based line numbers\n                endLine: i + 1,\n                length: methodLength,\n                severity: methodLength > 50 ? 'high' : methodLength > 30 ? 'medium' : 'low',\n                description: `Method '${methodName}' is ${methodLength} lines long, which exceeds the recommended maximum of 20 lines.`\n              });\n            }\n\n            inMethod = false;\n          }\n        }\n      }\n    }\n\n    // Large Class detection\n    if (checkAll || types.includes('large-class')) {\n      // Detect classes based on language\n      const classPatterns: Record<string, RegExp> = {\n        '.js': /class\\s+(\\w+)/,\n        '.ts': /class\\s+(\\w+)/,\n        '.py': /class\\s+(\\w+)/,\n        '.java': /class\\s+(\\w+)/,\n        '.cs': /class\\s+(\\w+)/,\n        '.php': /class\\s+(\\w+)/,\n        '.rb': /class\\s+(\\w+)/,\n        '.go': /type\\s+(\\w+)\\s+struct/,\n        '.rs': /struct\\s+(\\w+)/\n      };\n\n      const pattern = classPatterns[fileExtension] || classPatterns['.js'];\n\n      // Find class boundaries\n      let classStart = -1;\n      let className = '';\n      let braceCount = 0;\n      let inClass = false;\n      let methodCount = 0;\n\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i];\n\n        // Check for class start\n        if (!inClass) {\n          const match = line.match(pattern);\n          if (match) {\n            classStart = i;\n            className = match[1];\n            inClass = true;\n            braceCount = (line.match(/\\{/g) || []).length - (line.match(/\\}/g) || []).length;\n            methodCount = 0;\n\n            // For Python, we don't use braces\n            if (fileExtension === '.py') {\n              // Find the end of the class by indentation\n              let j = i + 1;\n              // Add null check for regex match operation\n              const indentMatch = line.match(/^\\s*/);\n              const baseIndent = indentMatch ? indentMatch[0].length : 0;\n\n              while (j < lines.length) {\n                const nextLine = lines[j];\n                // Add null check for regex match operation\n                const nextIndentMatch = nextLine.match(/^\\s*/);\n                const nextIndent = nextIndentMatch ? nextIndentMatch[0].length : 0;\n\n                // Count methods\n                if (nextLine.match(/^\\s*def\\s+\\w+/)) {\n                  methodCount++;\n                }\n\n                // If we find a line with same or less indentation, we've exited the class\n                if (nextLine.trim() !== '' && nextIndent <= baseIndent) {\n                  break;\n                }\n\n                j++;\n              }\n\n              const classLength = j - classStart;\n\n              // Consider classes with more than 200 lines or 10 methods as \"large\"\n              if (classLength > 200 || methodCount > 10) {\n                results['large-class'].push({\n                  name: className,\n                  startLine: classStart + 1, // 1-based line numbers\n                  endLine: j,\n                  length: classLength,\n                  methods: methodCount,\n                  severity: classLength > 500 || methodCount > 20 ? 'high' : classLength > 300 || methodCount > 15 ? 'medium' : 'low',\n                  description: `Class '${className}' is ${classLength} lines long with ${methodCount} methods, which may indicate it has too many responsibilities.`\n                });\n              }\n\n              inClass = false;\n            }\n          }\n        } else if (fileExtension !== '.py') {\n          // Update brace count for non-Python languages\n          braceCount += (line.match(/\\{/g) || []).length - (line.match(/\\}/g) || []).length;\n\n          // Count methods\n          if (line.match(/\\w+\\s*\\([^)]*\\)\\s*\\{/) || line.match(/function\\s+\\w+\\s*\\(/)) {\n            methodCount++;\n          }\n\n          // Check if class has ended\n          if (braceCount === 0) {\n            const classLength = i - classStart + 1;\n\n            // Consider classes with more than 200 lines or 10 methods as \"large\"\n            if (classLength > 200 || methodCount > 10) {\n              results['large-class'].push({\n                name: className,\n                startLine: classStart + 1, // 1-based line numbers\n                endLine: i + 1,\n                length: classLength,\n                methods: methodCount,\n                severity: classLength > 500 || methodCount > 20 ? 'high' : classLength > 300 || methodCount > 15 ? 'medium' : 'low',\n                description: `Class '${className}' is ${classLength} lines long with ${methodCount} methods, which may indicate it has too many responsibilities.`\n              });\n            }\n\n            inClass = false;\n          }\n        }\n      }\n    }\n\n    // Long Parameter List detection\n    if (checkAll || types.includes('long-parameter-list')) {\n      // Detect methods with long parameter lists\n      const paramPatterns: Record<string, RegExp> = {\n        '.js': /function\\s+\\w+\\s*\\(([^)]*)\\)|\\w+\\s*=\\s*function\\s*\\(([^)]*)\\)|\\w+\\s*\\(([^)]*)\\)\\s*\\{|\\w+\\s*=\\s*\\(([^)]*)\\)\\s*=>/,\n        '.ts': /function\\s+\\w+\\s*\\(([^)]*)\\)|\\w+\\s*=\\s*function\\s*\\(([^)]*)\\)|\\w+\\s*\\(([^)]*)\\)\\s*\\{|\\w+\\s*=\\s*\\(([^)]*)\\)\\s*=>|\\w+\\s*:\\s*\\(([^)]*)\\)\\s*=>/,\n        '.py': /def\\s+\\w+\\s*\\(([^)]*)\\)/,\n        '.java': /(?:public|private|protected|static)?\\s*\\w+\\s+\\w+\\s*\\(([^)]*)\\)/,\n        '.cs': /(?:public|private|protected|internal|static)?\\s*\\w+\\s+\\w+\\s*\\(([^)]*)\\)/,\n        '.php': /function\\s+\\w+\\s*\\(([^)]*)\\)/,\n        '.rb': /def\\s+\\w+(?:\\(([^)]*)\\))?/,\n        '.go': /func\\s+\\w+\\s*\\(([^)]*)\\)/,\n        '.rs': /fn\\s+\\w+\\s*\\(([^)]*)\\)/\n      };\n\n      const pattern = paramPatterns[fileExtension] || paramPatterns['.js'];\n\n      // Find methods with long parameter lists\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i];\n        const match = line.match(pattern);\n\n        if (match) {\n          const params = match[1] || match[2] || match[3] || match[4] || match[5] || '';\n          const paramCount = params.split(',').filter(p => p.trim() !== '').length;\n\n          // Consider methods with more than 4 parameters as having \"long parameter lists\"\n          if (paramCount > 4) {\n            const methodMatch = line.match(/\\b(\\w+)\\s*\\(/);\n            const methodName = methodMatch ? methodMatch[1] : 'anonymous';\n\n            results['long-parameter-list'].push({\n              name: methodName,\n              line: i + 1, // 1-based line numbers\n              parameterCount: paramCount,\n              parameters: params,\n              severity: paramCount > 7 ? 'high' : paramCount > 5 ? 'medium' : 'low',\n              description: `Method '${methodName}' has ${paramCount} parameters, which exceeds the recommended maximum of 4 parameters.`\n            });\n          }\n        }\n      }\n    }\n\n    // Switch Statement detection\n    if (checkAll || types.includes('switch-statements')) {\n      // Detect switch statements\n      const switchPattern = /switch\\s*\\([^)]*\\)\\s*\\{/g;\n      let match;\n\n      while ((match = switchPattern.exec(code)) !== null) {\n        const lineNumber = code.substring(0, match.index).split('\\n').length;\n\n        // Find the end of the switch statement\n        const switchStart = match.index;\n        let braceCount = 1;\n        let pos = switchStart + match[0].length;\n\n        while (braceCount > 0 && pos < code.length) {\n          if (code[pos] === '{') braceCount++;\n          if (code[pos] === '}') braceCount--;\n          pos++;\n        }\n\n        const switchCode = code.substring(switchStart, pos);\n        const caseCount = (switchCode.match(/case\\s+/g) || []).length;\n\n        // Consider switch statements with more than 5 cases as potential code smells\n        if (caseCount > 5) {\n          results['switch-statements'].push({\n            line: lineNumber,\n            caseCount,\n            severity: caseCount > 10 ? 'high' : caseCount > 7 ? 'medium' : 'low',\n            description: `Switch statement with ${caseCount} cases found. Consider using polymorphism or the strategy pattern instead.`\n          });\n        }\n      }\n    }\n\n    // Primitive Obsession detection (simplified)\n    if (checkAll || types.includes('primitive-obsession')) {\n      // Look for patterns that might indicate primitive obsession\n      const primitiveTypes = ['string', 'int', 'float', 'double', 'boolean', 'bool', 'char', 'byte', 'short', 'long'];\n\n      // Check for repeated primitive type usage in parameters\n      for (const type of primitiveTypes) {\n        const typePattern = new RegExp(`\\\\b${type}\\\\b`, 'g');\n        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues\n        const matches = Array.from(code.matchAll(typePattern));\n\n        if (matches.length > 10) {\n          results['primitive-obsession'].push({\n            primitiveType: type,\n            occurrences: matches.length,\n            severity: matches.length > 20 ? 'high' : matches.length > 15 ? 'medium' : 'low',\n            description: `Excessive use of primitive type '${type}' (${matches.length} occurrences). Consider creating domain-specific types.`\n          });\n        }\n      }\n    }\n\n    return results;\n  }\n\n  private async detectCodeSmellsWithLLM(code: string, language: string, types: string[], provider: any): Promise<any> {\n    const checkAll = types.includes('all');\n    const typesToCheck = checkAll ?\n      ['long-method', 'large-class', 'primitive-obsession', 'long-parameter-list',\n        'duplicate-code', 'feature-envy', 'shotgun-surgery', 'switch-statements'] :\n      types;\n\n    // Prepare prompt for LLM\n    const prompt = `\nAnalyze the following ${language} code for code smells, specifically focusing on: ${typesToCheck.join(', ')}.\nFor each code smell found, provide:\n1. The type of code smell\n2. The location (line number or method/class name)\n3. The severity (high, medium, low)\n4. A brief description of the issue\n5. A recommendation for refactoring\n\nCode to analyze:\n\\`\\`\\`${language}\n${code}\n\\`\\`\\`\n\nFormat your response as JSON with the following structure:\n{\n  \"codeSmells\": {\n    \"long-method\": [\n      {\n        \"name\": \"methodName\",\n        \"startLine\": number,\n        \"endLine\": number,\n        \"length\": number,\n        \"severity\": \"high|medium|low\",\n        \"description\": \"string\",\n        \"recommendation\": \"string\"\n      }\n    ],\n    \"large-class\": [...],\n    \"primitive-obsession\": [...],\n    \"long-parameter-list\": [...],\n    \"duplicate-code\": [...],\n    \"feature-envy\": [...],\n    \"shotgun-surgery\": [...],\n    \"switch-statements\": [...]\n  }\n}\n`;\n\n    const result = await provider.generate({\n      prompt,\n      systemPrompt: 'You are an expert software engineer specializing in code quality and refactoring. Analyze the provided code for code smells with high precision.',\n      modelId: this.llmConfig.modelId,\n      options: this.llmConfig.options\n    });\n\n    if (result.error) {\n      return { available: false, error: result.error };\n    }\n\n    try {\n      // Try to parse the LLM response as JSON\n      const jsonMatch = result.content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const parsedResult = JSON.parse(jsonMatch[0]);\n        return {\n          available: true,\n          codeSmells: parsedResult.codeSmells || {}\n        };\n      } else {\n        return {\n          available: false,\n          error: 'LLM response could not be parsed as JSON'\n        };\n      }\n    } catch (error) {\n      return {\n        available: false,\n        error: `Failed to parse LLM response: ${error}`\n      };\n    }\n  }\n\n  private combineResults(patternResults: Record<string, any[]>, llmResults: any): Record<string, any[]> {\n    const combined: Record<string, any[]> = { ...patternResults };\n\n    // If LLM results are available, merge them with pattern results\n    if (llmResults.available && llmResults.codeSmells) {\n      for (const [type, smells] of Object.entries(llmResults.codeSmells)) {\n        if (!combined[type]) {\n          combined[type] = [];\n        }\n\n        // Add LLM-detected code smells, avoiding duplicates\n        for (const smell of smells as any[]) {\n          // Check if this code smell is already detected by pattern matching\n          let isDuplicate = false;\n\n          if (type === 'long-method' || type === 'large-class') {\n            isDuplicate = combined[type].some(existing =>\n              existing.name === smell.name &&\n              Math.abs(existing.startLine - smell.startLine) < 5\n            );\n          } else if (type === 'long-parameter-list') {\n            isDuplicate = combined[type].some(existing =>\n              existing.name === smell.name &&\n              existing.line === smell.line\n            );\n          } else if (type === 'switch-statements') {\n            isDuplicate = combined[type].some(existing =>\n              Math.abs(existing.line - smell.line) < 5\n            );\n          } else if (type === 'primitive-obsession') {\n            isDuplicate = combined[type].some(existing =>\n              existing.primitiveType === smell.primitiveType\n            );\n          } else {\n            isDuplicate = combined[type].some(existing =>\n              JSON.stringify(existing) === JSON.stringify(smell)\n            );\n          }\n\n          if (!isDuplicate) {\n            combined[type].push({\n              ...smell,\n              source: 'llm' // Mark as coming from LLM\n            });\n          }\n        }\n      }\n    }\n\n    return combined;\n  }\n\n  private generateRecommendations(results: Record<string, any[]>): Record<string, string[]> {\n    const recommendations: Record<string, string[]> = {};\n\n    // Long Method recommendations\n    if (results['long-method'] && results['long-method'].length > 0) {\n      recommendations['long-method'] = [\n        'Extract smaller, focused methods from long methods.',\n        'Use the Extract Method refactoring pattern to break down complex methods.',\n        'Consider using the Single Responsibility Principle: each method should do only one thing.',\n        'Look for comment blocks within methods - these often indicate where you can extract a new method.'\n      ];\n    }\n\n    // Large Class recommendations\n    if (results['large-class'] && results['large-class'].length > 0) {\n      recommendations['large-class'] = [\n        'Split large classes into smaller, more focused classes.',\n        'Use the Extract Class refactoring pattern to move related fields and methods to a new class.',\n        'Consider using composition instead of creating monolithic classes.',\n        'Apply the Single Responsibility Principle at the class level.'\n      ];\n    }\n\n    // Primitive Obsession recommendations\n    if (results['primitive-obsession'] && results['primitive-obsession'].length > 0) {\n      recommendations['primitive-obsession'] = [\n        'Create small, specialized classes to replace primitive values.',\n        'Use the Replace Data Value with Object refactoring pattern.',\n        'Consider using enums or sealed classes for values with a fixed set of options.',\n        'Look for primitive values that have special meaning or validation requirements.'\n      ];\n    }\n\n    // Long Parameter List recommendations\n    if (results['long-parameter-list'] && results['long-parameter-list'].length > 0) {\n      recommendations['long-parameter-list'] = [\n        'Create parameter objects to group related parameters.',\n        'Use the Introduce Parameter Object refactoring pattern.',\n        'Consider whether the method needs all those parameters or if some can be obtained in other ways.',\n        'Look for parameter groups that are always passed together - these are good candidates for a new class.'\n      ];\n    }\n\n    // Duplicate Code recommendations\n    if (results['duplicate-code'] && results['duplicate-code'].length > 0) {\n      recommendations['duplicate-code'] = [\n        'Extract duplicated code into shared methods or utility classes.',\n        'Use the Extract Method refactoring pattern for duplicated code blocks.',\n        'Consider using inheritance or composition to share behavior.',\n        'Apply the DRY (Don\\'t Repeat Yourself) principle.'\n      ];\n    }\n\n    // Feature Envy recommendations\n    if (results['feature-envy'] && results['feature-envy'].length > 0) {\n      recommendations['feature-envy'] = [\n        'Move methods to the class they\\'re most interested in.',\n        'Use the Move Method refactoring pattern.',\n        'Consider whether the current class structure accurately reflects the domain.',\n        'Apply the Tell, Don\\'t Ask principle to reduce feature envy.'\n      ];\n    }\n\n    // Shotgun Surgery recommendations\n    if (results['shotgun-surgery'] && results['shotgun-surgery'].length > 0) {\n      recommendations['shotgun-surgery'] = [\n        'Move related fields and methods into a single class.',\n        'Use the Move Field and Move Method refactoring patterns.',\n        'Consider whether you need to introduce a new abstraction.',\n        'Look for concepts that are spread across multiple classes.'\n      ];\n    }\n\n    // Switch Statements recommendations\n    if (results['switch-statements'] && results['switch-statements'].length > 0) {\n      recommendations['switch-statements'] = [\n        'Replace switch statements with polymorphism.',\n        'Use the Replace Conditional with Polymorphism refactoring pattern.',\n        'Consider using the Strategy pattern for algorithms that vary.',\n        'Look for switch statements that check the same condition in multiple places.'\n      ];\n    }\n\n    return recommendations;\n  }\n\n  private countTotalSmells(results: Record<string, any[]>): number {\n    return Object.values(results).reduce((total, smells) => total + smells.length, 0);\n  }\n\n  private getSeverityBreakdown(results: Record<string, any[]>): Record<string, number> {\n    const breakdown = { high: 0, medium: 0, low: 0 };\n\n    for (const smells of Object.values(results)) {\n      for (const smell of smells) {\n        if (smell.severity in breakdown) {\n          breakdown[smell.severity as keyof typeof breakdown]++;\n        }\n      }\n    }\n\n    return breakdown;\n  }\n}\n\n/**\n * Suggests and applies refactoring patterns to improve code quality\n */\nexport class RefactoringAdvisorTool implements ITool {\n  readonly id = 'refactoringAdvisor';\n  readonly name = 'Refactoring Advisor';\n  readonly description = 'Suggests and applies refactoring patterns to improve code quality.';\n  readonly type = 'multi-action';\n  // Add index signature to fix \"Element implicitly has an 'any' type\" error\n  readonly actions: { [key: string]: any } = {\n    'suggest': {\n      description: 'Suggest refactoring patterns for a file',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to analyze.'),\n        refactoringTypes: z.array(z.enum([\n          'extract-method',\n          'extract-class',\n          'introduce-parameter-object',\n          'replace-conditional-with-polymorphism',\n          'encapsulate-field',\n          'replace-magic-number',\n          'all'\n        ])).optional().describe('Types of refactoring to suggest. Default is all.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to analyze.'),\n        refactoringTypes: z.array(z.enum([\n          'extract-method',\n          'extract-class',\n          'introduce-parameter-object',\n          'replace-conditional-with-polymorphism',\n          'encapsulate-field',\n          'replace-magic-number',\n          'all'\n        ])).optional().describe('Types of refactoring to suggest. Default is all.')\n      })\n    },\n    'preview': {\n      description: 'Preview a specific refactoring',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to refactor.'),\n        refactoringType: z.enum([\n          'extract-method',\n          'extract-class',\n          'introduce-parameter-object',\n          'replace-conditional-with-polymorphism',\n          'encapsulate-field',\n          'replace-magic-number'\n        ]).describe('Type of refactoring to preview.'),\n        startLine: z.number().describe('Start line of the code to refactor.'),\n        endLine: z.number().describe('End line of the code to refactor.'),\n        options: z.record(z.string(), z.any()).optional().describe('Additional options for the refactoring.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to refactor.'),\n        refactoringType: z.enum([\n          'extract-method',\n          'extract-class',\n          'introduce-parameter-object',\n          'replace-conditional-with-polymorphism',\n          'encapsulate-field',\n          'replace-magic-number'\n        ]).describe('Type of refactoring to preview.'),\n        startLine: z.number().describe('Start line of the code to refactor.'),\n        endLine: z.number().describe('End line of the code to refactor.'),\n        options: z.record(z.string(), z.any()).optional().describe('Additional options for the refactoring.')\n      })\n    }\n  };\n\n  private llmConfig: LLMConfig = {\n    provider: 'openai',\n    modelId: 'gpt-3.5-turbo',\n    options: { temperature: 0.1 }\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      if (!actionName || !this.actions[actionName]) {\n        return {\n          success: false,\n          error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      switch (actionName) {\n        case 'suggest':\n          return this.suggestRefactorings(input, actionName);\n        case 'preview':\n          return this.previewRefactoring(input, actionName);\n        default:\n          return {\n            success: false,\n            error: `Unknown action: ${actionName}`,\n            toolId: this.id,\n            actionName\n          };\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Refactoring advisor failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async suggestRefactorings(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const refactoringTypes = (input.refactoringTypes as string[]) || ['all'];\n\n    if (!filePath) {\n      return {\n        success: false,\n        error: '\\'filePath\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Validate file exists\n      const uri = vscode.Uri.file(filePath);\n      // Replace Promise.then().catch() with try/catch for better error handling\n      let exists = false;\n      try {\n        await vscode.workspace.fs.stat(uri);\n        exists = true;\n      } catch {\n        exists = false;\n      }\n\n      if (!exists) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Get file content\n      const document = await vscode.workspace.openTextDocument(filePath);\n      const text = document.getText();\n      const language = document.languageId;\n\n      // Use LLM to suggest refactorings\n      const provider = await llmService.getProviderForConfig(this.llmConfig);\n      if (!provider) {\n        return {\n          success: false,\n          error: 'No LLM provider available for refactoring suggestions.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      const suggestions = await this.suggestRefactoringsWithLLM(text, language, refactoringTypes, provider);\n\n      if (!suggestions.success) {\n        return {\n          success: false,\n          error: suggestions.error,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      return {\n        success: true,\n        output: {\n          suggestions: suggestions.refactorings,\n          summary: this.generateRefactoringSummary(suggestions.refactorings)\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          refactoringTypes: refactoringTypes.includes('all') ?\n            ['extract-method', 'extract-class', 'introduce-parameter-object',\n              'replace-conditional-with-polymorphism', 'encapsulate-field', 'replace-magic-number'] :\n            refactoringTypes\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Refactoring suggestion failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async previewRefactoring(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const refactoringType = input.refactoringType as string;\n    const startLine = input.startLine as number;\n    const endLine = input.endLine as number;\n    const options = input.options as Record<string, any> || {};\n\n    if (!filePath) {\n      return {\n        success: false,\n        error: '\\'filePath\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    if (!refactoringType) {\n      return {\n        success: false,\n        error: '\\'refactoringType\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    if (startLine === undefined || startLine === null) {\n      return {\n        success: false,\n        error: '\\'startLine\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    if (endLine === undefined || endLine === null) {\n      return {\n        success: false,\n        error: '\\'endLine\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Validate file exists\n      const uri = vscode.Uri.file(filePath);\n      // Replace Promise.then().catch() with try/catch for better error handling\n      let exists = false;\n      try {\n        await vscode.workspace.fs.stat(uri);\n        exists = true;\n      } catch {\n        exists = false;\n      }\n\n      if (!exists) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Get file content\n      const document = await vscode.workspace.openTextDocument(filePath);\n      const text = document.getText();\n      const language = document.languageId;\n\n      // Validate line range\n      const lineCount = document.lineCount;\n\n      if (startLine < 1 || startLine > lineCount) {\n        return {\n          success: false,\n          error: `Invalid startLine: ${startLine}. File has ${lineCount} lines.`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      if (endLine < startLine || endLine > lineCount) {\n        return {\n          success: false,\n          error: `Invalid endLine: ${endLine}. File has ${lineCount} lines and startLine is ${startLine}.`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Extract the code to refactor\n      const startOffset = document.offsetAt(new vscode.Position(startLine - 1, 0));\n      const endOffset = document.offsetAt(new vscode.Position(endLine, 0));\n      const codeToRefactor = text.substring(startOffset, endOffset);\n\n      // Use LLM to preview refactoring\n      const provider = await llmService.getProviderForConfig(this.llmConfig);\n      if (!provider) {\n        return {\n          success: false,\n          error: 'No LLM provider available for refactoring preview.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      const preview = await this.previewRefactoringWithLLM(text, codeToRefactor, startLine, endLine, refactoringType, language, options, provider);\n\n      if (!preview.success) {\n        return {\n          success: false,\n          error: preview.error,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      return {\n        success: true,\n        output: {\n          original: {\n            code: codeToRefactor,\n            startLine,\n            endLine\n          },\n          refactored: preview.refactored,\n          explanation: preview.explanation,\n          refactoringType\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          refactoringType,\n          startLine,\n          endLine\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Refactoring preview failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async suggestRefactoringsWithLLM(code: string, language: string, types: string[], provider: any): Promise<any> {\n    const checkAll = types.includes('all');\n    const typesToCheck = checkAll ?\n      ['extract-method', 'extract-class', 'introduce-parameter-object',\n        'replace-conditional-with-polymorphism', 'encapsulate-field', 'replace-magic-number'] :\n      types;\n\n    // Prepare prompt for LLM\n    const prompt = `\nAnalyze the following ${language} code and suggest refactoring opportunities, specifically focusing on: ${typesToCheck.join(', ')}.\nFor each refactoring opportunity, provide:\n1. The type of refactoring\n2. The location (line numbers)\n3. A brief description of why this refactoring would improve the code\n4. A brief explanation of how to implement the refactoring\n\nCode to analyze:\n\\`\\`\\`${language}\n${code}\n\\`\\`\\`\n\nFormat your response as JSON with the following structure:\n{\n  \"refactorings\": [\n    {\n      \"type\": \"extract-method|extract-class|introduce-parameter-object|replace-conditional-with-polymorphism|encapsulate-field|replace-magic-number\",\n      \"startLine\": number,\n      \"endLine\": number,\n      \"description\": \"string\",\n      \"implementation\": \"string\"\n    }\n  ]\n}\n`;\n\n    try {\n      const result = await provider.generate({\n        prompt,\n        systemPrompt: 'You are an expert software engineer specializing in code quality and refactoring. Analyze the provided code and suggest refactoring opportunities with high precision.',\n        modelId: this.llmConfig.modelId,\n        options: this.llmConfig.options\n      });\n\n      if (result.error) {\n        return { success: false, error: result.error };\n      }\n\n      // Try to parse the LLM response as JSON\n      const jsonMatch = result.content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const parsedResult = JSON.parse(jsonMatch[0]);\n        return {\n          success: true,\n          refactorings: parsedResult.refactorings || []\n        };\n      } else {\n        return {\n          success: false,\n          error: 'LLM response could not be parsed as JSON'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: `Failed to get refactoring suggestions: ${error}`\n      };\n    }\n  }\n\n  private async previewRefactoringWithLLM(fullCode: string, codeToRefactor: string, startLine: number, endLine: number, refactoringType: string, language: string, options: Record<string, any>, provider: any): Promise<any> {\n    // Prepare options string\n    const optionsString = Object.entries(options)\n      .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)\n      .join('\\n');\n\n    // Prepare prompt for LLM\n    const prompt = `\nYou are tasked with refactoring the following ${language} code using the \"${refactoringType}\" refactoring pattern.\n\nOriginal code to refactor (lines ${startLine}-${endLine}):\n\\`\\`\\`${language}\n${codeToRefactor}\n\\`\\`\\`\n\n${optionsString ? `Additional options:\\n${optionsString}\\n` : ''}\n\nContext (full file):\n\\`\\`\\`${language}\n${fullCode}\n\\`\\`\\`\n\nPlease provide:\n1. The refactored code\n2. A detailed explanation of the changes made\n3. Any additional files that need to be created (if applicable)\n\nFormat your response as JSON with the following structure:\n{\n  \"refactored\": {\n    \"code\": \"string (the refactored code)\",\n    \"additionalFiles\": [\n      {\n        \"path\": \"string (relative path to the new file)\",\n        \"content\": \"string (content of the new file)\"\n      }\n    ]\n  },\n  \"explanation\": \"string (detailed explanation of the refactoring)\"\n}\n`;\n\n    try {\n      const result = await provider.generate({\n        prompt,\n        systemPrompt: 'You are an expert software engineer specializing in code quality and refactoring. Implement the requested refactoring with high precision.',\n        modelId: this.llmConfig.modelId,\n        options: this.llmConfig.options\n      });\n\n      if (result.error) {\n        return { success: false, error: result.error };\n      }\n\n      // Try to parse the LLM response as JSON\n      const jsonMatch = result.content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const parsedResult = JSON.parse(jsonMatch[0]);\n        return {\n          success: true,\n          refactored: parsedResult.refactored || { code: '', additionalFiles: [] },\n          explanation: parsedResult.explanation || 'No explanation provided.'\n        };\n      } else {\n        return {\n          success: false,\n          error: 'LLM response could not be parsed as JSON'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: `Failed to preview refactoring: ${error}`\n      };\n    }\n  }\n\n  private generateRefactoringSummary(refactorings: any[]): string {\n    if (refactorings.length === 0) {\n      return 'No refactoring opportunities found.';\n    }\n\n    // Group refactorings by type\n    const groupedRefactorings: Record<string, number> = {};\n\n    for (const refactoring of refactorings) {\n      const type = refactoring.type;\n      groupedRefactorings[type] = (groupedRefactorings[type] || 0) + 1;\n    }\n\n    // Generate summary\n    const summary = [\n      `Found ${refactorings.length} refactoring opportunities:`,\n      ...Object.entries(groupedRefactorings).map(([type, count]) => `- ${count} ${type} refactoring(s)`)\n    ].join('\\n');\n\n    return summary;\n  }\n}\n"]}