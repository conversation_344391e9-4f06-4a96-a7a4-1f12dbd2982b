{"version": 3, "file": "workflowManager.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/workflowManager.ts"], "names": [], "mappings": ";;;AACA,yDAAsD;AAEtD,yCAAsC;AAEtC;;GAEG;AACH,MAAa,eAAe;IAC1B,IAAW,MAAM;QACf,OAAO,eAAM,CAAC;IAChB,CAAC;IACO,MAAM,CAAC,QAAQ,CAAkB;IAEzC,gBAAwB,CAAC;IAEzB;;OAEG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAC1C,eAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;QAElD,4CAA4C;QAC5C,MAAM,SAAS,GAAG,mCAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE3D,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,qCAAqC;YACrC,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QAED,+DAA+D;QAC/D,MAAM,gBAAgB,GAAG,mCAAgB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAEvE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe;QAC1B,OAAO,mCAAgB,CAAC,eAAe,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB,CAAC,GAAW;QACxC,OAAO,mCAAgB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;IACjD,CAAC;CACF;AAvDD,0CAuDC;AAED,4BAA4B;AACf,QAAA,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import { GraphDefinition } from './graphTypes';\nimport { workflowRegistry } from './workflowRegistry';\nimport { IWorkflowManager } from '../../managers';\nimport { logger } from '../../logger';\n\n/**\n * Workflow Manager for handling workflow operations\n */\nexport class WorkflowManager implements IWorkflowManager {\n  public get logger() {\n    return logger;\n  }\n  private static instance: WorkflowManager;\n\n  private constructor() { }\n\n  /**\n   * Get the singleton instance\n   */\n  public static getInstance(): WorkflowManager {\n    if (!WorkflowManager.instance) {\n      WorkflowManager.instance = new WorkflowManager();\n    }\n    return WorkflowManager.instance;\n  }\n\n  /**\n   * Get a workflow for a specific mode\n   */\n  public async getWorkflowForMode(mode: string): Promise<GraphDefinition | undefined> {\n    logger.info(`Getting workflow for mode: ${mode}`);\n\n    // Get workflows with the specified mode tag\n    const workflows = workflowRegistry.getWorkflowsByTag(mode);\n\n    if (workflows.length > 0) {\n      // Return the first matching workflow\n      return workflows[0];\n    }\n\n    // If no specific workflow found, try to get a default workflow\n    const defaultWorkflows = workflowRegistry.getWorkflowsByTag('default');\n\n    if (defaultWorkflows.length > 0) {\n      return defaultWorkflows[0];\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Get all available workflows\n   */\n  public async getAllWorkflows(): Promise<GraphDefinition[]> {\n    return workflowRegistry.getAllWorkflows();\n  }\n\n  /**\n   * Get workflows by tag\n   */\n  public async getWorkflowsByTag(tag: string): Promise<GraphDefinition[]> {\n    return workflowRegistry.getWorkflowsByTag(tag);\n  }\n}\n\n// Export singleton instance\nexport const workflowManager = WorkflowManager.getInstance();"]}