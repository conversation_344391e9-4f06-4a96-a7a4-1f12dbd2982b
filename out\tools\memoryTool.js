"use strict";
/**
 * AI Agent Tool for comprehensive memory management.
 * Provides actions for saving, retrieving, updating, deleting, searching,
 * tagging, and visualizing memory entries via a MemoryManager.
 * Implemented as a multi-action tool.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryTool = void 0;
exports.createMemoryTool = createMemoryTool;
const zod_1 = require("zod");
const logger_1 = require("../logger"); // Correct path
// Define Zod schemas for each action's input
const SaveMemoryInputSchema = zod_1.z.object({
    /** The content of the memory entry. Can be string or structured data. */
    content: zod_1.z.any().describe('The content of the memory entry (string, object, etc.).'),
    /** Metadata for the memory entry, requiring source and type and allowing other properties. */
    metadata: zod_1.z.object({
        source: zod_1.z.string().describe('The source of the memory entry.'),
        type: zod_1.z.string().describe('The type of the memory entry.'),
        tags: zod_1.z.string().array().optional().describe('Optional list of tags for the memory entry.'),
    }).catchall(zod_1.z.any()).describe('Metadata for the memory entry, requiring source and type and allowing other properties.'), // Use catchall for additional properties
    /** Optional specific ID to assign to the memory entry. If not provided, one is generated. */
    id: zod_1.z.string().optional().describe('Optional specific ID to assign. Defaults to generated UUID.'),
}).describe('Input for saving a memory entry.');
const GetMemoryInputSchema = zod_1.z.object({
    /** The unique ID of the memory entry to retrieve. */
    id: zod_1.z.string().min(1).describe('The unique ID of the memory entry to retrieve.'),
}).describe('Input for retrieving a memory entry by ID.');
const DeleteMemoryInputSchema = zod_1.z.object({
    /** The unique ID of the memory entry to delete. */
    id: zod_1.z.string().min(1).describe('The unique ID of the memory entry to delete.'),
}).describe('Input for deleting a memory entry by ID.');
const ListMemoriesInputSchema = zod_1.z.object({
// No input parameters needed as getMemories expects no arguments
}).describe('Input for listing memory entries.');
// Assuming MemorySearchOptions is defined elsewhere and has filter, limit, query
const SearchMemoriesInputSchema = zod_1.z.object({
    /** The search query string for semantic or text search. */
    query: zod_1.z.string().min(1).describe('The search query string.'),
    /** Optional limit on the number of results. */
    limit: zod_1.z.number().int().positive().optional().describe('Optional maximum number of search results.'),
    /** Optional relevance threshold for search results. */
    threshold: zod_1.z.number().optional().describe('Optional relevance threshold for search results.'),
    /** Optional filter criteria for searching memories. */
    filter: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional().describe('Optional filter criteria for search (e.g., { source: "user" }).'),
    // Removed semantic property as it's not in MemorySearchOptions
}).describe('Input for searching memory entries.');
const ClearMemoriesInputSchema = zod_1.z.object({
    /** Optional filter to clear only memories matching the criteria. If omitted, clears all memories. Use with caution. */
    filter: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional().describe('Optional filter to clear only memories matching criteria. Use with caution.'),
    /** Confirmation flag to prevent accidental clearing of all memories. Required if no filter is provided. */
    confirm: zod_1.z.boolean().optional().describe('Required confirmation to clear all memories if no filter is specified.'),
}).describe('Input for clearing memory entries.');
// Action to get tool's own settings
const GetMemorySettingsInputSchema = zod_1.z.object({}).describe('Input to get current memory settings.');
// Action to update tool's own settings
const UpdateMemorySettingsInputSchema = zod_1.z.object({
    // Define the structure for settings updates based on your MemorySettings type
    // Example: enabled, maxMemories, relevanceThreshold, vectorStore, database, etc.
    enabled: zod_1.z.boolean().optional(),
    maxMemories: zod_1.z.number().int().positive().optional(),
    relevanceThreshold: zod_1.z.number().min(0).max(1).optional(),
    contextWindowSize: zod_1.z.number().int().positive().optional(),
    conversationHistorySize: zod_1.z.number().int().positive().optional(),
    vectorStore: zod_1.z.enum(['memory', 'chroma', 'pinecone', 'weaviate', 'hnswlib']).optional().describe('Optional vector store to use ("memory", "chroma", "pinecone", "weaviate", "hnswlib").'), // Use z.enum
    database: zod_1.z.enum(['sqlite', 'mysql', 'postgres', 'mongodb', 'redis']).optional().describe('Optional database to use ("sqlite", "mysql", "postgres", "mongodb", "redis").'), // Use z.enum
    // Add other settings properties that can be updated
}).partial().describe('Partial memory settings to update.');
/**
 * Implements the ITool interface for comprehensive memory management actions.
 * This is a multi-action tool coordinating operations via a MemoryManager.
 */
class MemoryTool {
    id = 'memory_management'; // Unique ID for the tool
    name = 'memory_manager'; // Machine-friendly name for the LLM
    description = 'Provides comprehensive capabilities to save, retrieve, update, delete, search, tag, list, clear, and visualize memory entries used by the AI. Memory entries store diverse information like code snippets, documents, conversation summaries, etc.';
    category = 'Memory'; // Tool category
    type = 'multi-action'; // This is a multi-action tool
    // Define the available actions using ToolActionDefinition
    actions;
    memory; // Dependency on memory operations
    /**
       * Constructs a new MemoryTool instance.
       * @param memory - The memory operations instance to use.
       * @throws {Error} If the memory operations instance is not provided.
       */
    constructor(memory) {
        if (!memory) {
            throw new Error('MemoryTool requires a memory operations instance.');
        }
        this.memory = memory;
        logger_1.Logger.instance.info('MemoryTool initialized.');
        // Define the actions using the Zod schemas
        this.actions = {
            'save': {
                description: 'Saves a new memory entry or updates an existing one if an ID is provided.',
                inputSchema: SaveMemoryInputSchema,
            },
            'get': {
                description: 'Retrieves a specific memory entry by its unique ID.',
                inputSchema: GetMemoryInputSchema,
            },
            'delete': {
                description: 'Deletes a memory entry by its unique ID.',
                inputSchema: DeleteMemoryInputSchema,
            },
            'list': {
                description: 'Lists existing memory entries, optionally filtered, limited, and sorted.',
                inputSchema: ListMemoriesInputSchema,
            },
            'search': {
                description: 'Searches for memory entries semantically or by keyword query, optionally with metadata filters. Returns relevant entries ordered by relevance/match.',
                inputSchema: SearchMemoriesInputSchema,
            },
            // Removed 'update' action as updateMemory method does not exist
            // Removed 'tag' action as tagMemory method does not exist
            'clear': {
                description: 'Clears all memory entries or only those matching an optional filter. USE WITH EXTREME CAUTION.',
                inputSchema: ClearMemoriesInputSchema,
            },
            // Removed 'visualize' action as visualizeMemories method does not exist
            'get_settings': {
                description: 'Retrieves the current configuration settings for the memory system.',
                inputSchema: GetMemorySettingsInputSchema,
            },
            'update_settings': {
                description: 'Updates the configuration settings for the memory system. Requires specific input parameters matching setting keys. Some updates may require re-initialization.',
                inputSchema: UpdateMemorySettingsInputSchema,
            },
        };
    }
    /**
       * Provides the structured definition for the LLM.
       * Generates JSON schema definitions for each action.
       * Conforms to ITool.getDefinitionForModel.
       * @returns An array of JSON schema definitions suitable for LLM tool calling (e.g., OpenAI function definitions).
       */
    getDefinitionForModel() {
        // For a multi-action tool, the LLM typically sees each action as a distinct callable function.
        // The names should be unique across all tools/actions exposed to the LLM.
        // A common pattern is "tool_name__action_name".
        return Object.entries(this.actions).map(([actionName, actionDef]) => {
            // Generate JSON schema for the action's input schema using Zod's toJSON()
            // Use casting for robustness against potential Zod variations
            const schemaObject = actionDef.inputSchema.toJSON ? actionDef.inputSchema.toJSON() : undefined;
            if (!schemaObject) {
                logger_1.Logger.instance.error(`MemoryTool getDefinitionForModel: Failed to generate JSON schema for action '${actionName}'.`);
                // Return a minimal definition if schema generation fails
                return {
                    name: `${this.name}__${actionName}`,
                    description: actionDef.description || `Performs the ${actionName} action.`,
                    parameters: { type: 'object', properties: {} }, // Empty params if schema fails
                };
            }
            // Potential schema adjustments for complex types (e.g., metadata)
            if (schemaObject.properties?.metadata) {
                schemaObject.properties.metadata = {
                    type: 'object',
                    description: schemaObject.properties.metadata.description || 'Optional metadata for the memory entry.',
                    additionalProperties: { type: ['string', 'number', 'boolean', 'array', 'object', 'null'] }, // Allow various types for metadata values
                };
            }
            if (schemaObject.properties?.filter) {
                schemaObject.properties.filter = {
                    type: 'object',
                    description: schemaObject.properties.filter.description || 'Filter criteria.',
                    additionalProperties: { type: ['string', 'number', 'boolean', 'array', 'object', 'null'] },
                };
            }
            return {
                // Action name format for the LLM
                name: `${this.name}__${actionName}`,
                description: actionDef.description,
                parameters: schemaObject, // The generated JSON schema
            };
        });
    }
    /**
       * Executes the specified memory action.
       * The calling framework is responsible for parsing the LLM's tool call,
       * extracting the action name and parameters, and validating parameters
       * against the specific action's schema before calling this method.
       *
       * @param actionName - The name of the action to execute (e.g., 'save', 'get').
       * @param input - The validated input parameters for the action.
       * @param context - Optional agent context (provides access to memoryManager if not dependency injected).
       * @returns A promise resolving to the tool's result.
       * @throws {Error} If the action execution fails.
       */
    async execute(actionName, input, _context) {
        if (!actionName) {
            const errorMsg = 'MemoryTool requires an actionName for multi-action execution.';
            logger_1.Logger.instance.error(errorMsg, { input });
            return { success: false, error: errorMsg, toolId: this.id, actionName };
        }
        const actionDefinition = this.actions[actionName];
        if (!actionDefinition) {
            const errorMsg = `MemoryTool received unknown actionName: ${actionName}.`;
            logger_1.Logger.instance.error(errorMsg, { input });
            return { success: false, error: errorMsg, toolId: this.id, actionName };
        }
        logger_1.Logger.instance.info(`Executing MemoryTool action: "${actionName}"`);
        logger_1.Logger.instance.debug(`Input for action "${actionName}":`, input);
        try {
            // Input is assumed to be validated by the framework already,
            // but a final parse could be added here if paranoia is needed.
            // const validatedInput = actionDefinition.inputSchema.parse(input);
            let resultOutput; // Output of the specific action
            let successStatus = true; // Default success
            // --- Execute the specific action logic ---
            switch (actionName) {
                case 'save':
                    // Use SaveMemoryInputSchema type for input clarity
                    const saveInput = input;
                    // memoryManager.addMemory should ideally take { content, metadata, id, tags }
                    // Merge tags from input into metadata.tags if present
                    // Ensure required metadata fields are present
                    const metadataWithTags = {
                        ...saveInput.metadata,
                        ...(saveInput.metadata.tags ? { tags: saveInput.metadata.tags } : {}),
                        // Ensure required fields have default values if not provided
                        source: saveInput.metadata.source || 'user',
                        type: saveInput.metadata.type || 'text'
                    };
                    const memory = await this.memory.addMemory({
                        content: saveInput.content,
                        metadata: {
                            source: metadataWithTags.source,
                            type: metadataWithTags.type,
                            tags: metadataWithTags.tags
                        },
                    });
                    resultOutput = memory; // Return the saved entry details
                    logger_1.Logger.instance.info(`Memory entry saved/updated with ID: ${memory.id}`);
                    break;
                case 'get':
                    const getInput = input;
                    const entry = await this.memory.getMemory(input.id);
                    resultOutput = entry || null; // Return the entry or null
                    successStatus = !!entry; // Success only if entry was found
                    if (successStatus)
                        logger_1.Logger.instance.info(`Memory entry retrieved with ID: ${getInput.id}`);
                    else
                        logger_1.Logger.instance.warn(`Memory entry not found with ID: ${getInput.id}`);
                    break;
                case 'delete':
                    const deleteInput = input;
                    const ok = await this.memory.deleteMemory(input.id);
                    resultOutput = { id: deleteInput.id, deleted: ok }; // Return status of deletion
                    successStatus = ok; // Success only if deletion occurred
                    if (successStatus)
                        logger_1.Logger.instance.info(`Memory entry deleted with ID: ${deleteInput.id}`);
                    else
                        logger_1.Logger.instance.warn(`Memory entry not found for deletion with ID: ${deleteInput.id}`);
                    break;
                case 'list':
                    // Removed listOptions as getMemories takes no arguments
                    const entries = await this.memory.getMemories(); // Call without arguments
                    resultOutput = entries; // Return array of entries
                    logger_1.Logger.instance.info(`Listed ${entries.length} memory entries.`);
                    break;
                case 'search':
                    const searchInput = input;
                    // Map schema input to MemorySearchOptions
                    const searchOptions = {
                        query: searchInput.query,
                        limit: searchInput.limit,
                        filter: searchInput.filter,
                        // Removed semantic property
                        // Add other search options if MemorySearchOptions includes them
                    };
                    // MemoryManager should expose both search and searchSimilar
                    // Assuming a unified search method that handles both based on query/flags
                    const searchResults = await this.memory.searchMemories(searchOptions); // Assuming this method exists
                    resultOutput = searchResults; // Return array of search results (MemoryEntry[])
                    logger_1.Logger.instance.info(`Found ${searchResults.length} memory entries for search query.`);
                    break;
                // Removed 'update' action
                // Removed 'tag' action
                case 'clear':
                    const clearInput = input;
                    // Check confirmation for clearing all
                    if (!clearInput.filter && !clearInput.confirm) {
                        const confirmError = 'Clearing all memories requires \'confirm: true\' input.';
                        logger_1.Logger.instance.error(confirmError);
                        return { success: false, error: confirmError, toolId: this.id, actionName };
                    }
                    try {
                        // MemoryManager's clearMemories doesn't return a count and doesn't accept a filter
                        // We'll need to implement filtering ourselves if needed
                        if (clearInput.filter) {
                            // Get all memories first
                            const memories = await this.memory.searchMemories({
                                query: input.query,
                                limit: input.limit || 10,
                                filter: input.filter,
                                sortBy: input.sortBy,
                                sortOrder: input.sortOrder
                            });
                            // Filter memories based on criteria
                            const memoriesToDelete = memories.filter(memory => {
                                // Check each filter property against the memory
                                for (const [key, value] of Object.entries(clearInput.filter)) {
                                    // Handle metadata properties
                                    if (key.startsWith('metadata.')) {
                                        const metadataKey = key.substring(9);
                                        if (memory.metadata[metadataKey] !== value) {
                                            return false;
                                        }
                                    }
                                    // Handle source and type directly
                                    else if (key === 'source') {
                                        if (memory.metadata.source !== value) {
                                            return false;
                                        }
                                    }
                                    else if (key === 'type') {
                                        if (memory.metadata.type !== value) {
                                            return false;
                                        }
                                    }
                                    // Handle tags (array contains)
                                    else if (key === 'tags') {
                                        const filterTags = Array.isArray(value) ? value : [value];
                                        if (!memory.metadata.tags || !filterTags.every(tag => memory.metadata.tags.includes(tag))) {
                                            return false;
                                        }
                                    }
                                    // Handle direct properties
                                    else if (memory[key] !== value) {
                                        return false;
                                    }
                                }
                                return true;
                            });
                            // Delete each filtered memory
                            let deleteCount = 0;
                            for (const memory of memoriesToDelete) {
                                const success = await this.memory.deleteMemory(input.id);
                                if (success)
                                    deleteCount++;
                            }
                            resultOutput = {
                                count: deleteCount,
                                totalFiltered: memoriesToDelete.length,
                                filter: clearInput.filter
                            };
                            logger_1.Logger.instance.info(`Cleared ${deleteCount} memory entries matching filter.`);
                        }
                        else {
                            // Clear all memories
                            const beforeCount = (await this.memory.getMemories()).length;
                            await this.memory.clearMemories();
                            resultOutput = {
                                count: beforeCount,
                                filter: 'all'
                            };
                            logger_1.Logger.instance.info(`Cleared all memory entries (approximately ${beforeCount}).`);
                        }
                        successStatus = true; // Clearing operation itself is successful
                    }
                    catch (error) {
                        logger_1.Logger.instance.error(`Error during memory clear operation: ${error.message}`, error);
                        return {
                            success: false,
                            error: `Failed to clear memories: ${error.message}`,
                            toolId: this.id,
                            actionName
                        };
                    }
                    break;
                // Removed 'visualize' action
                // Action for getting memory settings
                case 'get_settings':
                    logger_1.Logger.instance.info('Executing MemoryTool get_settings action');
                    const settings = await this.memory.getMemorySettings();
                    resultOutput = settings;
                    successStatus = true;
                    logger_1.Logger.instance.info('Retrieved memory settings.');
                    break;
                case 'update_settings':
                    const updateSettingsInput = input;
                    // MemoryManager should expose a method to update settings
                    await this.memory.updateMemorySettings(input.settings); // Assuming updateMemorySettings handles partial updates
                    resultOutput = { status: 'settings updated', updated: Object.keys(updateSettingsInput) };
                    successStatus = true; // Update operation itself is successful
                    logger_1.Logger.instance.info('Updated memory settings.');
                    break;
                default:
                    // This case should ideally not be reached if parsing/routing is correct,
                    // but handle defensively.
                    const unknownActionError = `Unknown action: ${actionName}. Available actions: ${Object.keys(this.actions).join(', ')}`;
                    logger_1.Logger.instance.error(unknownActionError);
                    return { success: false, error: unknownActionError, toolId: this.id, actionName };
            }
            // Return the result in the standard ToolResult format
            return {
                success: successStatus,
                output: resultOutput,
                toolId: this.id,
                actionName: actionName,
                // Add usage/metadata if available
            };
        }
        catch (error) {
            // Catch any unhandled errors during action execution
            const errorMessage = `Error during MemoryTool action "${actionName}": ${error.message || error}`;
            logger_1.Logger.instance.error(errorMessage, error);
            return { success: false, error: errorMessage, toolId: this.id, actionName };
        }
    }
}
exports.MemoryTool = MemoryTool;
// Factory function to create the MemoryTool, injecting dependencies
function createMemoryTool(memory) {
    return new MemoryTool(memory);
}
// Example usage (conceptual):
// const memoryManager = getMemoryManagerInstance(); // Assume this function exists
// const memoryTool = createMemoryTool(memoryManager);
// const toolResult = await memoryTool.execute('save', { content: 'User likes blue.', metadata: { user: 'abc' } });
// console.log(toolResult);
//# sourceMappingURL=memoryTool.js.map