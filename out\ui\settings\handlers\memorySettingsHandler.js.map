{"version": 3, "file": "memorySettingsHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/memorySettingsHandler.ts"], "names": [], "mappings": ";;AACA,kEA6DC;AA9DD,mDAAmD;AACnD,SAAgB,2BAA2B,CAAC,OAAY,EAAE,KAAU;IAClE,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9E,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtJ,IAAI,CAAC;QACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC;gBAClC,MAAM;YACR,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC9B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBAC7C,QAAQ,CAAC,KAAK,GAAG,iCAAiC,CAAC;oBACnD,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;gBACvB,MAAM;YACR,CAAC;YACD,KAAK,YAAY,CAAC,CAAC,CAAC;gBAClB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC9B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;oBAC1B,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;gBACxE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,mBAAmB,CAAC;oBACrC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC;gBAClE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACvC,MAAM;YACR,CAAC;YACD,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBACjE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,mBAAmB,CAAC;oBACrC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBACjC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC3C,CAAC;QACD,CAAC;IACH,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for Memory Management messages and logic\nexport function handleMemorySettingsMessage(message: any, panel: any) {\n  const settings = (window as any).settings || {};\n  settings.memories = Array.isArray(settings.memories) ? settings.memories : [];\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: message.type, success: false, data: null, error: null };\n  try {\n    switch (message.type) {\n    case 'getMemories': {\n      response.success = true;\n      response.data = settings.memories;\n      break;\n    }\n    case 'addMemory': {\n      const memory = message.memory;\n      if (!memory || !memory.id || !memory.content) {\n        response.error = 'Missing required memory fields.';\n        break;\n      }\n      settings.memories.push(memory);\n      response.success = true;\n      response.data = memory;\n      break;\n    }\n    case 'editMemory': {\n      const memory = message.memory;\n      if (!memory || !memory.id) {\n        response.error = 'Memory id required.';\n        break;\n      }\n      const idx = settings.memories.findIndex((m: any) => m.id === memory.id);\n      if (idx === -1) {\n        response.error = 'Memory not found.';\n        break;\n      }\n      settings.memories[idx] = { ...settings.memories[idx], ...memory };\n      response.success = true;\n      response.data = settings.memories[idx];\n      break;\n    }\n    case 'deleteMemory': {\n      const id = message.id;\n      if (!id) {\n        response.error = 'Memory id required.';\n        break;\n      }\n      const idx = settings.memories.findIndex((m: any) => m.id === id);\n      if (idx === -1) {\n        response.error = 'Memory not found.';\n        break;\n      }\n      settings.memories.splice(idx, 1);\n      response.success = true;\n      break;\n    }\n    default: {\n      response.error = 'Unknown message type.';\n    }\n    }\n  } catch (err: any) {\n    response.error = err?.message || String(err);\n  }\n  panel.postMessage(response);\n}\n\n"]}