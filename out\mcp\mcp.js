"use strict";
/**
 * Enhanced Model Context Protocol (MCP) 2025 Implementation
 *
 * This module implements the latest MCP 2025 specification with enhanced
 * capabilities for tool integration, resource management, and protocol extensions.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPManager = exports.MCPResourceType = exports.MCPMessageType = exports.MCP_VERSION = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../logger");
const toolRegistry_1 = require("../tools/toolRegistry");
const logger = logger_1.Logger.instance;
/**
 * MCP 2025 Protocol Version
 */
exports.MCP_VERSION = '2025.1';
/**
 * MCP Message Types
 */
var MCPMessageType;
(function (MCPMessageType) {
    MCPMessageType["REQUEST"] = "request";
    MCPMessageType["RESPONSE"] = "response";
    MCPMessageType["NOTIFICATION"] = "notification";
    MCPMessageType["ERROR"] = "error";
})(MCPMessageType || (exports.MCPMessageType = MCPMessageType = {}));
/**
 * MCP Resource Types
 */
var MCPResourceType;
(function (MCPResourceType) {
    MCPResourceType["FILE"] = "file";
    MCPResourceType["DIRECTORY"] = "directory";
    MCPResourceType["URL"] = "url";
    MCPResourceType["MEMORY"] = "memory";
    MCPResourceType["TOOL"] = "tool";
    MCPResourceType["AGENT"] = "agent";
    MCPResourceType["WORKFLOW"] = "workflow";
})(MCPResourceType || (exports.MCPResourceType = MCPResourceType = {}));
/**
 * Enhanced MCP 2025 Manager
 */
class MCPManager {
    static instance;
    servers = new Map();
    messageHandlers = new Map();
    resourceSubscriptions = new Map();
    toolRegistry;
    tools = [];
    context;
    constructor() {
        this.toolRegistry = toolRegistry_1.ToolRegistry.instance;
        this.setupMessageHandlers();
        // Use vscode import to register configuration change handler
        vscode.workspace.onDidChangeConfiguration(() => {
            this.handleConfigurationChange();
        });
        // Initialize tools using ITool interface
        this.tools = this.toolRegistry.getAllTools();
        // Initialize context using MCPContext type
        this.context = {
            version: exports.MCP_VERSION,
            metadata: {
                source: 'codessa-mcp2025',
                timestamp: new Date().toISOString(),
                sessionId: Date.now().toString()
            },
            content: {}
        };
        logger.info('MCP 2025 Manager initialized');
    }
    handleConfigurationChange() {
        const config = vscode.workspace.getConfiguration('codessa.mcp');
        // Update MCP-specific configuration
        const logLevel = config.get('logLevel', 'info');
        logger.info(`MCP configuration updated - Log Level: ${logLevel}`);
        // Update active connections timeout and cleanup
        const connectionTimeout = config.get('connectionTimeout', 3600000);
        const now = Date.now();
        for (const [clientId, data] of this.activeConnections.entries()) {
            if (now - data.lastActive > connectionTimeout) {
                this.activeConnections.delete(clientId);
                logger.info(`Cleaned up inactive connection: ${clientId}`);
            }
        }
        // Apply configuration to the MCP context with preserved content
        this.context = {
            version: exports.MCP_VERSION,
            metadata: {
                source: 'codessa-mcp2025',
                timestamp: new Date().toISOString(),
                sessionId: Date.now().toString(),
                logLevel: logLevel
            },
            content: this.context.content
        };
    }
    static getInstance() {
        if (!MCPManager.instance) {
            MCPManager.instance = new MCPManager();
        }
        return MCPManager.instance;
    }
    /**
       * Setup message handlers
       */
    setupMessageHandlers() {
        this.messageHandlers.set('initialize', this.handleInitialize.bind(this));
        this.messageHandlers.set('tools/list', this.handleToolsList.bind(this));
        this.messageHandlers.set('tools/call', this.handleToolCall.bind(this));
        this.messageHandlers.set('resources/list', this.handleResourcesList.bind(this));
        this.messageHandlers.set('resources/read', this.handleResourceRead.bind(this));
        this.messageHandlers.set('resources/subscribe', this.handleResourceSubscribe.bind(this));
        this.messageHandlers.set('resources/unsubscribe', this.handleResourceUnsubscribe.bind(this));
        this.messageHandlers.set('prompts/list', this.handlePromptsList.bind(this));
        this.messageHandlers.set('prompts/get', this.handlePromptGet.bind(this));
        this.messageHandlers.set('logging/setLevel', this.handleLoggingSetLevel.bind(this));
        // Enhanced 2025 methods
        this.messageHandlers.set('agents/list', this.handleAgentsList.bind(this));
        this.messageHandlers.set('agents/invoke', this.handleAgentInvoke.bind(this));
        this.messageHandlers.set('workflows/list', this.handleWorkflowsList.bind(this));
        this.messageHandlers.set('workflows/execute', this.handleWorkflowExecute.bind(this));
        this.messageHandlers.set('memory/search', this.handleMemorySearch.bind(this));
        this.messageHandlers.set('memory/store', this.handleMemoryStore.bind(this));
        // VS Code specific handlers for enhanced integration
        this.messageHandlers.set('vscode/workspace', this.handleVSCodeWorkspace.bind(this));
        this.messageHandlers.set('vscode/editor', this.handleVSCodeEditor.bind(this));
        this.messageHandlers.set('vscode/chat', this.handleVSCodeChat.bind(this));
        this.messageHandlers.set('vscode/diagnostics', this.handleVSCodeDiagnostics.bind(this));
    }
    /**
       * Register an MCP server
       */
    registerServer(server) {
        this.servers.set(server.name, server);
        logger.info(`MCP server registered: ${server.name} v${server.version}`);
    }
    /**
       * Process an MCP message
       */
    async processMessage(message) {
        try {
            logger.debug(`Processing MCP message: ${message.method}`);
            if (!message.method) {
                throw new Error('Message method is required');
            }
            // Update client's last active timestamp
            const clientConnection = this.activeConnections.get(message.id);
            if (clientConnection) {
                clientConnection.lastActive = Date.now();
            }
            const handler = this.messageHandlers.get(message.method);
            if (!handler) {
                throw new Error(`Unknown method: ${message.method}`);
            }
            const result = await handler(message);
            // Track successful message processing in context
            this.updateContext({
                lastProcessedMessage: {
                    id: message.id,
                    method: message.method,
                    timestamp: Date.now()
                }
            });
            return {
                id: message.id,
                type: MCPMessageType.RESPONSE,
                result: result,
                timestamp: Date.now(),
                version: exports.MCP_VERSION
            };
        }
        catch (error) {
            logger.error('Error processing MCP message:', error);
            return {
                id: message.id,
                type: MCPMessageType.ERROR,
                error: {
                    code: -1,
                    message: error instanceof Error ? error.message : String(error)
                },
                timestamp: Date.now(),
                version: exports.MCP_VERSION
            };
        }
    }
    /**
       * Handle initialize request
       */
    activeConnections = new Map();
    async handleInitialize(message) {
        logger.debug(`Initializing MCP connection for client ${message.id}`);
        // Track active connections for session management
        this.activeConnections.set(message.id, { lastActive: Date.now() });
        return {
            protocolVersion: exports.MCP_VERSION,
            capabilities: {
                tools: {
                    listChanged: true,
                    call: true
                },
                resources: {
                    subscribe: true,
                    listChanged: true
                },
                prompts: {
                    listChanged: true
                },
                logging: {
                    level: 'info'
                },
                experimental: {
                    agents: true,
                    workflows: true,
                    memory: true,
                    goddess_mode: true
                }
            },
            serverInfo: {
                name: 'Codessa MCP Server',
                version: '1.0.0'
            }
        };
    }
    /**
       * Type guards for parameter types
       */
    isToolParams(params) {
        return 'name' in params;
    }
    isResourceParams(params) {
        return 'uri' in params;
    }
    isAgentParams(params) {
        return 'agentId' in params && 'task' in params;
    }
    isWorkflowParams(params) {
        return 'workflowId' in params;
    }
    isMemoryParams(params) {
        return 'query' in params;
    }
    isChatParams(params) {
        return 'action' in params && 'data' in params;
    }
    /**
       * Handle tools list request
       */
    async handleToolsList(message) {
        logger.debug(`Handling tools list request [${message.id}]`);
        const tools = this.toolRegistry.getAllTools();
        return {
            tools: tools.map(tool => {
                // Create basic schema that matches the interface
                const mcpSchema = {
                    type: 'object',
                    properties: {},
                    required: []
                };
                // If the tool has a schema, try to extract its structure
                if (tool.schema && typeof tool.schema === 'object') {
                    const rawSchema = tool.schema;
                    const properties = rawSchema._def?.shape || {};
                    // Process each property in the schema
                    for (const [key, value] of Object.entries(properties)) {
                        const prop = value;
                        if (prop?._def) {
                            if (!mcpSchema.properties) {
                                mcpSchema.properties = {};
                            }
                            mcpSchema.properties[key] = {
                                type: prop._def.typeName || 'unknown',
                                description: prop.description
                            };
                            if (!prop._def.isOptional) {
                                if (!mcpSchema.required) {
                                    mcpSchema.required = [];
                                }
                                mcpSchema.required.push(key);
                            }
                        }
                    }
                }
                return {
                    name: tool.id,
                    description: tool.description || `Tool: ${tool.id}`,
                    inputSchema: mcpSchema
                };
            })
        };
    }
    /**
       * Handle tool call request
       */
    async handleToolCall(message) {
        if (!message.params || !this.isToolParams(message.params)) {
            throw new Error('Tool call requires name parameter');
        }
        const tool = this.toolRegistry.getTool(message.params.name);
        if (!tool) {
            throw new Error(`Tool not found: ${message.params.name}`);
        }
        const result = await tool.execute(undefined, message.params.arguments || {});
        return {
            content: [
                {
                    type: 'text',
                    text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
                }
            ]
        };
    }
    /**
       * Handle resources list request
       */
    async handleResourcesList(message) {
        logger.debug(`Handling resources list request [${message.id}]`);
        const resources = [
            {
                uri: 'file://workspace',
                type: MCPResourceType.DIRECTORY,
                name: 'Workspace',
                description: 'Current VS Code workspace'
            },
            {
                uri: 'memory://codessa',
                type: MCPResourceType.MEMORY,
                name: 'Codessa Memory',
                description: 'Codessa memory system'
            }
        ];
        return { resources };
    }
    /**
       * Handle resource read request
       */
    async handleResourceRead(message) {
        if (!message.params || !this.isResourceParams(message.params)) {
            throw new Error('URI parameter is required');
        }
        // Implementation would depend on the resource type
        return {
            contents: [
                {
                    uri: message.params.uri,
                    mimeType: 'text/plain',
                    text: `Content of resource: ${message.params.uri}`
                }
            ]
        };
    }
    /**
       * Handle resource subscribe request
       */
    async handleResourceSubscribe(message) {
        if (!message.params || !this.isResourceParams(message.params)) {
            throw new Error('URI parameter is required');
        }
        if (!this.resourceSubscriptions.has(message.params.uri)) {
            this.resourceSubscriptions.set(message.params.uri, new Set());
        }
        const subscriptions = this.resourceSubscriptions.get(message.params.uri);
        if (subscriptions) {
            subscriptions.add(message.id);
        }
        return { success: true };
    }
    /**
       * Handle resource unsubscribe request
       */
    async handleResourceUnsubscribe(message) {
        if (!message.params || !this.isResourceParams(message.params)) {
            throw new Error('URI parameter is required');
        }
        const subscriptions = this.resourceSubscriptions.get(message.params.uri);
        if (subscriptions) {
            subscriptions.delete(message.id);
        }
        return { success: true };
    }
    /**
       * Handle prompts list request
       */
    async handlePromptsList(message) {
        logger.debug(`Handling prompts list request [${message.id}]`);
        return {
            prompts: [
                {
                    name: 'code_analysis',
                    description: 'Analyze code for issues and improvements',
                    arguments: [
                        {
                            name: 'code',
                            description: 'Code to analyze',
                            required: true
                        }
                    ]
                },
                {
                    name: 'goddess_mode',
                    description: 'Activate Goddess Mode for enhanced AI interaction',
                    arguments: [
                        {
                            name: 'personality',
                            description: 'Personality type to activate',
                            required: false
                        }
                    ]
                }
            ]
        };
    }
    /**
       * Handle prompt get request
       */
    async handlePromptGet(message) {
        if (!message.params || !this.isToolParams(message.params)) {
            throw new Error('Name parameter is required');
        }
        return {
            description: `Prompt: ${message.params.name}`,
            messages: [
                {
                    role: 'user',
                    content: {
                        type: 'text',
                        text: `Execute ${message.params.name} with arguments: ${JSON.stringify(message.params.arguments || {})}`
                    }
                }
            ]
        };
    }
    /**
       * Handle logging set level request
       */
    async handleLoggingSetLevel(message) {
        if (!message.params || !('level' in message.params)) {
            throw new Error('Level parameter is required');
        }
        logger.info(`Setting log level to: ${message.params.level}`);
        return { success: true };
    }
    /**
       * Handle agents list request (Enhanced 2025)
       */
    async handleAgentsList(message) {
        logger.debug(`Handling agents list request [${message.id}]`);
        // Implementation would list available agents
        return {
            agents: [
                {
                    id: 'supervisor',
                    name: 'Supervisor Agent',
                    description: 'Coordinates multiple agents',
                    capabilities: ['delegation', 'coordination', 'workflow_management']
                }
            ]
        };
    }
    /**
       * Handle agent invoke request (Enhanced 2025)
       */
    async handleAgentInvoke(message) {
        if (!message.params || !this.isAgentParams(message.params)) {
            throw new Error('AgentId and task parameters are required');
        }
        // Implementation would invoke the specified agent
        return {
            result: `Agent ${message.params.agentId} executed task: ${message.params.task}`,
            status: 'completed'
        };
    }
    /**
       * Handle workflows list request (Enhanced 2025)
       */
    async handleWorkflowsList(message) {
        logger.debug(`Handling workflows list request [${message.id}]`);
        return {
            workflows: [
                {
                    id: 'code_analysis',
                    name: 'Code Analysis Workflow',
                    description: 'Comprehensive code analysis and improvement suggestions'
                },
                {
                    id: 'refactoring',
                    name: 'Refactoring Workflow',
                    description: 'Automated code refactoring and optimization'
                }
            ]
        };
    }
    /**
       * Handle workflow execute request (Enhanced 2025)
       */
    async handleWorkflowExecute(message) {
        if (!message.params || !this.isWorkflowParams(message.params)) {
            throw new Error('WorkflowId parameter is required');
        }
        // Implementation would execute the specified workflow
        return {
            result: `Workflow ${message.params.workflowId} executed successfully`,
            outputs: message.params.inputs || {},
            status: 'completed'
        };
    }
    /**
       * Handle memory search request (Enhanced 2025)
       */
    async handleMemorySearch(message) {
        if (!message.params || !this.isMemoryParams(message.params)) {
            throw new Error('Query parameter is required');
        }
        const limit = message.params.limit ?? 10;
        // Implementation would search memory system
        return {
            results: [
                {
                    id: '1',
                    content: `Memory result for query: ${message.params.query}`,
                    relevance: 0.95,
                    timestamp: Date.now()
                }
            ].slice(0, limit),
            total: 1
        };
    }
    /**
       * Handle memory store request (Enhanced 2025)
       */
    async handleMemoryStore(message) {
        if (!message.params || !('content' in message.params)) {
            throw new Error('Content parameter is required');
        }
        // Implementation would store content in memory system
        return {
            id: Date.now().toString(),
            stored: true,
            timestamp: Date.now()
        };
    }
    /**
       * Handle VS Code workspace request
       */
    async handleVSCodeWorkspace(message) {
        logger.debug(`Handling VS Code workspace request [${message.id}]`);
        try {
            // Get workspace information if available
            const workspaceInfo = {
                folders: [],
                name: 'Unknown',
                hasWorkspace: false
            };
            // Get workspace folders from real VS Code API
            const folders = vscode.workspace.workspaceFolders;
            if (folders) {
                workspaceInfo.folders = folders.map(folder => ({
                    name: folder.name,
                    uri: folder.uri.toString()
                }));
                workspaceInfo.hasWorkspace = true;
                workspaceInfo.name = vscode.workspace.name || 'Workspace';
            }
            return {
                workspace: workspaceInfo,
                capabilities: ['file_access', 'editor_integration', 'diagnostics']
            };
        }
        catch (error) {
            logger.error('Error handling VS Code workspace request:', error);
            return {
                workspace: { folders: [], name: 'Unknown', hasWorkspace: false },
                capabilities: []
            };
        }
    }
    /**
       * Handle VS Code editor request
       */
    async handleVSCodeEditor(message) {
        try {
            logger.debug(`Processing editor request: ${message.id} (${message.method})`);
            const editorInfo = {
                activeEditor: null,
                visibleEditors: [],
                selection: null
            };
            // Get editor info from real VS Code API
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                editorInfo.activeEditor = {
                    fileName: activeEditor.document.fileName,
                    languageId: activeEditor.document.languageId,
                    lineCount: activeEditor.document.lineCount
                };
                if (!activeEditor.selection.isEmpty) {
                    editorInfo.selection = {
                        start: {
                            line: activeEditor.selection.start.line,
                            character: activeEditor.selection.start.character
                        },
                        end: {
                            line: activeEditor.selection.end.line,
                            character: activeEditor.selection.end.character
                        },
                        text: activeEditor.document.getText(activeEditor.selection)
                    };
                }
            }
            return {
                editor: editorInfo,
                capabilities: ['text_manipulation', 'selection_access', 'cursor_control']
            };
        }
        catch (error) {
            logger.error('Error handling VS Code editor request:', error);
            return {
                editor: { activeEditor: null, visibleEditors: [], selection: null },
                capabilities: []
            };
        }
    }
    /**
       * Handle VS Code chat request
       */
    async handleVSCodeChat(message) {
        try {
            if (!message.params || !this.isChatParams(message.params)) {
                throw new Error('Action and data parameters are required');
            }
            switch (message.params.action) {
                case 'send_message': {
                    const msg = message.params.data.message;
                    if (typeof msg !== 'string') {
                        throw new Error('Message must be a string');
                    }
                    return {
                        success: true,
                        messageId: Date.now().toString(),
                        response: `Processed chat message: ${msg}`
                    };
                }
                case 'get_history':
                    return {
                        success: true,
                        messages: [],
                        total: 0
                    };
                case 'clear_history':
                    return {
                        success: true,
                        cleared: 0
                    };
                default:
                    throw new Error(`Unknown chat action: ${message.params.action}`);
            }
        }
        catch (error) {
            logger.error('Error handling VS Code chat request:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
       * Handle VS Code diagnostics request
       */
    async handleVSCodeDiagnostics(message) {
        try {
            if (!message.params || !this.isResourceParams(message.params)) {
                throw new Error('URI parameter is required');
            }
            const diagnostics = {
                uri: message.params.uri,
                problems: [],
                summary: {
                    errors: 0,
                    warnings: 0,
                    info: 0
                }
            };
            return {
                diagnostics,
                capabilities: ['problem_detection', 'code_analysis', 'fix_suggestions']
            };
        }
        catch (error) {
            logger.error('Error handling VS Code diagnostics request:', error);
            return {
                diagnostics: { uri: 'unknown', problems: [], summary: { errors: 0, warnings: 0, info: 0 } },
                capabilities: []
            };
        }
    }
    /**
       * Create an MCP message
       */
    createMessage(type, method, params, result, error) {
        return {
            id: Date.now().toString(),
            type,
            method,
            params,
            result,
            error,
            timestamp: Date.now(),
            version: exports.MCP_VERSION
        };
    }
    /**
       * Dispose of the MCP manager
       */
    dispose() {
        this.servers.clear();
        this.messageHandlers.clear();
        this.resourceSubscriptions.clear();
        logger.info('MCP 2025 Manager disposed');
    }
    /**
     * Get the current context (IMCPManager interface implementation)
     */
    getCurrentContext() {
        return {
            version: exports.MCP_VERSION,
            metadata: {
                source: 'codessa-mcp2025',
                timestamp: new Date().toISOString(),
                sessionId: Date.now().toString()
            },
            content: {}
        };
    }
    /**
     * Update the context with new content (IMCPManager interface implementation)
     */
    updateContext(content) {
        logger.info('MCP2025Manager: updateContext called with content');
        // Update the context with the new content
        this.context.content = {
            ...this.context.content,
            ...content
        };
    }
    /**
     * Add code to the context (IMCPManager interface implementation)
     */
    addCode(language, content, path) {
        logger.info(`MCP2025Manager: addCode called for ${path || 'unnamed'}`);
    }
    /**
     * Add a file to the context (IMCPManager interface implementation)
     */
    addFile(path, content, language) {
        logger.info(`MCP2025Manager: addFile called for ${path}`);
        // Add file content to the context
        this.context.content[path] = {
            content,
            language: language || 'text',
            timestamp: new Date().toISOString()
        };
    }
}
exports.MCPManager = MCPManager;
//# sourceMappingURL=mcp.js.map