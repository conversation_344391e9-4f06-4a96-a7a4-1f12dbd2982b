{"version": 3, "file": "codessaMemory.js", "sourceRoot": "", "sources": ["../../../src/memory/codessa/codessaMemory.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+BAAoC;AAIpC,sEAY6C;AAM7C,mDAAmD;AACnD,yCAAsC;AAWtC,yCAAoD;AACpD,qDAAkD,CAAC,oDAAoD;AACvG,0EAAuE;AACvE,iEAA8D;AAC9D,iDAAqD;AAErD,gDAAgD;AAChD,MAAM,mBAAmB,GAAG,UAAU,CAAC;AACvC,MAAM,uBAAuB,GAAG,cAAc,CAAC;AAE/C;;;;;GAKG;AACH,MAAa,qBAAqB;IACxB,OAAO,CAAsC;IAC7C,WAAW,CAA2B;IACtC,QAAQ,CAAwB;IAChC,UAAU,CAAyB;IACnC,WAAW,GAAG,KAAK,CAAC;IACpB,cAAc,GAAG,KAAK,CAAC,CAAC,sCAAsC;IAEtE,mCAAmC;IAC3B,kBAAkB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;IACpD,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;IAE3D;;;;SAIK;IACE,KAAK,CAAC,UAAU,CAAC,OAAgC;QACtD,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5C,6EAA6E;YAC7E,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,kDAAkD;gBAClD,MAAM,IAAI,OAAO,CAAO,OAAO,CAAC,EAAE;oBAChC,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;wBACrC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;4BACzB,aAAa,CAAC,aAAa,CAAC,CAAC;4BAC7B,OAAO,EAAE,CAAC;wBACZ,CAAC;oBACH,CAAC,EAAE,GAAG,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAC7D,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAEvD,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YAEvB,2BAA2B;YAC3B,eAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC3C,IAAI,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChD,eAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAEvC,6BAA6B;YAC7B,MAAM,eAAe,GAAG,IAAA,kBAAS,EAAS,oBAAoB,EAAE,QAAQ,CAAC,CAAC;YAC1E,eAAM,CAAC,KAAK,CAAC,oCAAoC,eAAe,MAAM,CAAC,CAAC;YACxE,+CAA+C;YAC/C,IAAI,CAAC,WAAW,GAAG,MAAM,uCAAkB,CAAC,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAChG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,gDAAgD;YACrF,eAAM,CAAC,IAAI,CAAC,iBAAiB,eAAe,gBAAgB,CAAC,CAAC;YAE9D,yBAAyB;YACzB,MAAM,YAAY,GAAG,IAAA,kBAAS,EAAS,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,gCAAgC,YAAY,MAAM,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,GAAG,MAAM,iCAAe,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACnE,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,4CAA4C;YAC9E,4CAA4C;YAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;gBAC1D,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,IAAI,CAAC,iGAAiG,CAAC,CAAC;YACjH,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,aAAa,YAAY,gBAAgB,CAAC,CAAC;YAEvD,oCAAoC;YACpC,eAAM,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACpD,2EAA2E;YAC3E,MAAM,YAAY,GAAG;gBACnB,WAAW,EAAE,KAAK,EAAE,SAA0E,EAAwB,EAAE;oBACtH,qCAAqC;oBACrC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC;wBAChD,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;wBACrC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC;oBAEtB,sCAAsC;oBACtC,MAAM,QAAQ,GAAmB;wBAC/B,GAAG,SAAS,CAAC,QAAQ;wBACrB,MAAM,EAAG,SAAS,CAAC,QAAQ,CAAC,MAAuB,IAAI,MAAM;wBAC7D,IAAI,EAAG,SAAS,CAAC,QAAQ,CAAC,IAAmB,IAAI,MAAM;wBACvD,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;qBAC3F,CAAC;oBAEF,OAAO,IAAI,CAAC,SAAS,CAAC;wBACpB,OAAO;wBACP,QAAQ;qBACT,CAAC,CAAC;gBACL,CAAC;aACF,CAAC;YACF,kCAAmB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC7C,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAEhD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAEnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,eAAM,CAAC,IAAI,CAAC,sDAAsD,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;YAE/F,IAAI,CAAC;gBACH,iDAAiD;gBACjD,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;gBAEpE,yDAAyD;gBACzD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;oBACrB,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;oBACxD,MAAM,kBAAmB,SAAQ,yBAAU;wBACzC,KAAK,CAAC,cAAc,CAAC,KAAe;4BAClC,+CAA+C;4BAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;4BACxB,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC5D,CAAC;wBAED,KAAK,CAAC,UAAU,CAAC,IAAY;4BAC3B,+CAA+C;4BAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;4BACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;wBACzC,CAAC;wBAEO,oBAAoB,CAAC,IAAY,EAAE,UAAU,GAAG,GAAG;4BACzD,MAAM,MAAM,GAAa,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAa,CAAC;4BACnE,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;4BAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;gCACjE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;4BAC/D,CAAC;4BAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;4BAC7F,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gCAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;oCACpC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;gCACpC,CAAC;4BACH,CAAC;4BAED,OAAO,MAAM,CAAC;wBAChB,CAAC;qBACF;oBAED,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB,EAAE,CAAC;gBAC7C,CAAC;gBAED,iDAAiD;gBACjD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACtB,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;oBACxD,IAAI,CAAC,WAAW,GAAG,MAAM,uCAAkB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBACzF,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;gBACtC,CAAC;gBAED,oCAAoC;gBACpC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACnB,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;oBACjD,IAAI,CAAC,QAAQ,GAAG,MAAM,iCAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC/D,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;oBAEjC,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;wBACnC,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;wBAC1D,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC;gBAED,6DAA6D;gBAC7D,MAAM,YAAY,GAAG;oBACnB,WAAW,EAAE,KAAK,EAAE,SAA0E,EAAwB,EAAE;wBACtH,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC;4BAChD,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;4BACrC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC;wBAEtB,MAAM,QAAQ,GAAmB;4BAC/B,GAAG,SAAS,CAAC,QAAQ;4BACrB,MAAM,EAAG,SAAS,CAAC,QAAQ,CAAC,MAAuB,IAAI,MAAM;4BAC7D,IAAI,EAAG,SAAS,CAAC,QAAQ,CAAC,IAAmB,IAAI,MAAM;4BACvD,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;yBAC3F,CAAC;wBAEF,OAAO,IAAI,CAAC,SAAS,CAAC;4BACpB,OAAO;4BACP,QAAQ;yBACT,CAAC,CAAC;oBACL,CAAC;iBACF,CAAC;gBAEF,kCAAmB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;gBAE7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;YAE9E,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,MAAM,oBAAoB,GAAG,aAAa,YAAY,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBAC5G,MAAM,oBAAoB,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACpF,8EAA8E;gBAC9E,eAAM,CAAC,KAAK,CAAC,8DAA8D,EACzE,EAAE,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,oBAAoB,EAAE,CAAC,CAAC;gBAE1E,yBAAyB;gBACzB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;gBAC7B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;YAC5B,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,oDAAoD;QACnF,CAAC;IACH,CAAC;IAED;;;SAGK;IACG,KAAK,CAAC,iBAAiB;QAC7B,mDAAmD;QACnD,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC7E,OAAO;QACT,CAAC;QAED,+CAA+C;QAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,eAAM,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;YAC7F,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,4CAA4C;gBAC9C,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,uEAAuE;QACvE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACjF,gDAAgD;YAChD,IAAI,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;gBAEnF,+BAA+B;gBAC/B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;oBACrB,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;oBAC5C,MAAM,kBAAmB,SAAQ,yBAAU;wBACzC,KAAK,CAAC,cAAc,CAAC,KAAe;4BAClC,+CAA+C;4BAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;4BACxB,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC5D,CAAC;wBAED,KAAK,CAAC,UAAU,CAAC,IAAY;4BAC3B,+CAA+C;4BAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;4BACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;wBACzC,CAAC;wBAEO,oBAAoB,CAAC,IAAY,EAAE,UAAU,GAAG,GAAG;4BACzD,MAAM,MAAM,GAAa,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAa,CAAC;4BACnE,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;4BAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;gCACjE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;4BAC/D,CAAC;4BAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;4BAC7F,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gCAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;oCACpC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;gCACpC,CAAC;4BACH,CAAC;4BAED,OAAO,MAAM,CAAC;wBAChB,CAAC;qBACF;oBAED,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB,EAAE,CAAC;gBAC7C,CAAC;gBAED,iCAAiC;gBACjC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACtB,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;oBAC9C,IAAI,CAAC,WAAW,GAAG,MAAM,uCAAkB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBACzF,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;gBACtC,CAAC;gBAED,6BAA6B;gBAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACnB,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;oBAC1C,IAAI,CAAC,QAAQ,GAAG,MAAM,iCAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC/D,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;oBAEjC,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;wBACnC,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;wBAC1D,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC;gBAED,oDAAoD;gBACpD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBACzD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,eAAM,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;gBACnF,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB;QAC5B,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;QAEvD,gFAAgF;QAChF,IAAI,QAAQ,EAAE,aAAa,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACnE,MAAM,kBAAkB,GAAG,MAAM,QAAQ,CAAC,aAAa,EAAa,CAAC;gBACrE,IAAI,kBAAkB;oBAClB,OAAQ,kBAA+C,CAAC,UAAU,KAAK,UAAU;oBACjF,OAAQ,kBAAmD,CAAC,cAAc,KAAK,UAAU,EAAE,CAAC;oBAC9F,OAAO,kBAAgC,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,yFAAyF,CAAC,CAAC;gBACzG,CAAC;YACH,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,MAAM,YAAY,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAChE,eAAM,CAAC,IAAI,CAAC,4DAA4D,YAAY,mBAAmB,CAAC,CAAC;YAC3G,CAAC;QACH,CAAC;QAGD,6EAA6E;QAC7E,IAAI,QAAQ,EAAE,iBAAiB,EAAE,CAAC;YAChC,eAAM,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;YAC3F,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpE,uEAAuE;YACvE,MAAM,yBAA0B,SAAQ,yBAAU;gBAChD,6DAA6D;gBAC7D,MAAM,CAAc;gBACpB,GAAG,GAAG,IAAI,CAAC,CAAC,mCAAmC;gBAC/C,IAAI,GAAG,6BAA6B,CAAC;gBAErC;oBACE,KAAK,EAAE,CAAC;oBACR,IAAI,CAAC,MAAM,GAAG,IAAI,0BAAW,CAAC,EAAE,aAAa,CAAE,CAAC,CAAC;gBACnD,CAAC;gBAED,KAAK,CAAC,cAAc,CAAC,IAAY;oBAC/B,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;gBAED,KAAK,CAAC,cAAc,CAAC,KAAe;oBAClC,sEAAsE;oBACtE,MAAM,UAAU,GAAe,EAAE,CAAC;oBAClC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,kEAAkE;wBAClE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC9E,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC7B,CAAC;oBACD,OAAO,UAAU,CAAC;gBACpB,CAAC;gBAED,KAAK,CAAC,UAAU,CAAC,IAAY;oBAC3B,gCAAgC;oBAChC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/D,CAAC;aAGF;YACD,OAAO,IAAI,yBAAyB,EAAE,CAAC;QACzC,CAAC;QAED,4DAA4D;QAC5D,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,IAAA,kBAAS,EAAqB,6BAA6B,EAAE,SAAS,CAAC,CAAC;QAC3H,IAAI,YAAY,EAAE,CAAC;YACjB,eAAM,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;YAC3F,IAAI,CAAC;gBACH,OAAO,IAAI,+BAAgB,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,MAAM,YAAY,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAChE,eAAM,CAAC,KAAK,CAAC,mDAAmD,YAAY,EAAE,CAAC,CAAC;gBAChF,0BAA0B;YAC5B,CAAC;QACH,CAAC;QAED,kEAAkE;QAClE,eAAM,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;QAE7F,+EAA+E;QAC/E,MAAM,kBAAmB,SAAQ,yBAAU;YACzC,2DAA2D;YACnD,oBAAoB,CAAC,IAAY,EAAE,UAAU,GAAG,GAAG;gBACzD,+BAA+B;gBAC/B,MAAM,MAAM,GAAa,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAa,CAAC;gBAEnE,iBAAiB;gBACjB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;gBAEjD,4CAA4C;gBAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;oBACjE,qDAAqD;oBACrD,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;gBAC/D,CAAC;gBAED,kCAAkC;gBAClC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7F,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;oBAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;wBACpC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;oBACpC,CAAC;gBACH,CAAC;gBAED,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,KAAK,CAAC,cAAc,CAAC,KAAe;gBAClC,+CAA+C;gBAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;YAC5D,CAAC;YAED,KAAK,CAAC,UAAU,CAAC,IAAY;gBAC3B,+CAA+C;gBAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YACzC,CAAC;SACF;QAED,eAAM,CAAC,IAAI,CAAC,2GAA2G,CAAC,CAAC;QACzH,OAAO,IAAI,kBAAkB,EAAE,CAAC;IAClC,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,SAAS,CAAC,MAA2D;QAChF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,MAAM,EAAE,GAAG,OAAO,IAAA,SAAM,GAAE,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,aAAa,GAAG,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,yCAAyC;QAErJ,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,eAAM,CAAC,IAAI,CAAC,iEAAiE,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChG,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,SAAS,GAAgB;YAC7B,EAAE;YACF,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,2CAA2C;YACpE,SAAS;YACT,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI;gBAC3B,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;aAChB;YACD,SAAS,EAAE,SAAS,EAAE,oCAAoC;SAC3D,CAAC;QAEF,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAE7C,wBAAwB;YACxB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,6CAA6C;gBAC7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;gBAClE,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,0CAA0C;gBAE3E,gFAAgF;gBAChF,+GAA+G;gBAC/G,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,SAA+C,CAAC,CAAC;gBACtG,CAAC;gBACD,eAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,CAAC;gBAEvD,2BAA2B;gBAC3B,sEAAsE;gBACtE,2DAA2D;gBAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACtE,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,cAAuE,CAAC,CAAC;gBAC3H,CAAC;gBACD,eAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,yBAAyB,CAAC,CAAC;gBAE1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBAC/B,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC;gBACtD,OAAO,SAAS,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7D,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;YAChG,yFAAyF;YACzF,yIAAyI;YACzI,uHAAuH;YACvH,MAAM,IAAI,KAAK,CAAC,yBAAyB,YAAY,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;SAEK;IACG,qBAAqB,CAAC,gBAAyC;QACrE,MAAM,cAAc,GAA4B,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,kCAAkC;QAC9K,MAAM,YAAY,GAAG,EAAE,CAAC,CAAC,mBAAmB;QAC5C,MAAM,OAAO,GAAG,EAAE,CAAC,CAAC,uBAAuB;QAE3C,KAAK,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;YACnC,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,GAAG,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC3C,0BAA0B;oBAC1B,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK;yBACxB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;yBAClD,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBACvB,CAAC;qBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;oBAChG,4DAA4D;oBAC5D,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;gBAC9B,CAAC;gBACD,2DAA2D;YAC7D,CAAC;QACH,CAAC;QACD,oEAAoE;QACpE,IAAI,gBAAgB,CAAC,OAAO;YAAE,cAAc,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;QAChF,IAAI,gBAAgB,CAAC,SAAS;YAAE,cAAc,CAAC,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC;QAEtF,OAAO,cAAc,CAAC;IACxB,CAAC;IAGD;;;;SAIK;IACE,KAAK,CAAC,WAAW,CAAC,KAAc;QACrC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,KAAK,IAAI,IAAA,kBAAS,EAAS,oBAAoB,EAAE,IAAI,CAAC,CAAC;YAC9E,8DAA8D;YAC9D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAC9C,mBAAmB,EACnB,EAAE,EAAE,uBAAuB;gBAC3B,cAAc,EACd,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,+BAA+B;iBAClD,CAAC;gBACF,sDAAsD;gBACtD,OAAO,OAAmC,CAAC;YAC7C,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,SAAS,CAAC,EAAU;QAC/B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;gBACtE,OAAO,MAAiC,CAAC,CAAC,cAAc;YAC1D,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,+CAA+C;YAC/C,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACrD,eAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,yBAAyB,CAAC,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACrD,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,YAAY,CAAC,EAAU;QAClC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,uBAAuB;YACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,SAAS,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;YACxE,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,SAAS,EAAE,CAAC,CAAC;YAEjE,2BAA2B;YAC3B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,SAAS,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACtD,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,SAAS,EAAE,CAAC,CAAC;YAErE,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC,CAAC,gDAAgD;gBAC5E,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBAC/B,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,QAAQ,SAAS,QAAQ,SAAS,EAAE,CAAC,CAAC;YACpF,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,6CAA6C,CAAC,CAAC;YAC/E,CAAC;YACD,iFAAiF;YACjF,iEAAiE;YACjE,kEAAkE;YAClE,OAAO,IAAI,CAAC,CAAC,wDAAwD;QAEvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC,CAAC,4BAA4B;QAC5C,CAAC;IACH,CAAC;IAED;;;SAGK;IACE,KAAK,CAAC,aAAa;QACxB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACtD,IAAI,CAAC;YACH,4BAA4B;YAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;YAC3D,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,gCAAgC,mBAAmB,EAAE,CAAC,CAAC;YAEpE,qBAAqB;YACrB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxC,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAEtC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YAC/B,eAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,cAAc,CAAC,OAA4B;QACtD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;YAC9C,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAEzG,mCAAmC;YACnC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAE1C,gEAAgE;YAChE,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,kBAAkB,EAAE,CAAC;gBAC/C,6EAA6E;gBAC7E,6EAA6E;gBAC7E,qCAAqC;gBACrC,yCAAyC;gBACzC,eAAM,CAAC,IAAI,CAAC,iGAAiG,CAAC,CAAC;gBAC/G,mFAAmF;gBACnF,8FAA8F;YAChG,CAAC;iBAAM,IAAI,KAAK,EAAE,CAAC;gBACjB,eAAM,CAAC,IAAI,CAAC,aAAa,IAAA,kBAAS,EAAS,iBAAiB,EAAE,QAAQ,CAAC,sFAAsF,CAAC,CAAC;YACjK,CAAC;YAGD,gBAAgB;YAChB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAC9C,mBAAmB,EACnB,OAAO,EACP,KAAK,EACL,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,eAAe;iBAClC,CAAC;gBACF,eAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,CAAC,MAAM,WAAW,CAAC,CAAC;gBACnE,OAAO,OAAmC,CAAC,CAAC,cAAc;YAC5D,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,qBAAqB,CAAC,KAAa,EAAE,UAAwC,EAAE;QAC1F,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,MAAM,EAAE,KAAK,GAAG,IAAA,kBAAS,EAAS,0BAA0B,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,kBAAkB,GAAG,IAAA,kBAAS,EAAS,2BAA2B,EAAE,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QAC/K,eAAM,CAAC,KAAK,CAAC,yCAAyC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC,CAAC;QAEnI,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACxC,sCAAsC;gBACtC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAE/D,mDAAmD;gBACnD,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,CAAC,4BAA4B;gBAEnG,yBAAyB;gBACzB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,cAAc,EAAE,KAAK,EAAE,YAAqE,CAAC,CAAC;gBAC1J,eAAM,CAAC,KAAK,CAAC,gCAAgC,OAAO,CAAC,MAAM,eAAe,CAAC,CAAC;gBAE5E,mCAAmC;gBACnC,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,IAAI,kBAAkB,CAAC,CAAC;gBACrF,eAAM,CAAC,KAAK,CAAC,SAAS,eAAe,CAAC,MAAM,sCAAsC,kBAAkB,GAAG,CAAC,CAAC;gBAEzG,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACjC,OAAO,EAAE,CAAC;gBACZ,CAAC;gBAED,oDAAoD;gBACpD,wEAAwE;gBACxE,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAClD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBAE1D,6CAA6C;gBAC7C,MAAM,aAAa,GAAkB,EAAE,CAAC;gBACxC,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;oBACrC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACxC,IAAI,MAAM,EAAE,CAAC;wBACX,aAAa,CAAC,IAAI,CAAC;4BACjB,GAAG,MAAM;4BACT,QAAQ,EAAE;gCACR,GAAG,MAAM,CAAC,QAAQ;gCAClB,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,sBAAsB;6BAChD;yBACF,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,EAAE,6CAA6C,CAAC,CAAC;oBACtF,CAAC;gBACH,CAAC;gBAED,iDAAiD;gBACjD,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC;gBAEpG,eAAM,CAAC,IAAI,CAAC,aAAa,aAAa,CAAC,MAAM,8BAA8B,CAAC,CAAC;gBAC7E,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gDAAgD,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACnG,+CAA+C;YAC/C,8EAA8E;YAC9E,wEAAwE;YACxE,OAAO,EAAE,CAAC,CAAC,gCAAgC;QAC7C,CAAC;IACH,CAAC;IAED;;;;SAIK;IACG,KAAK,CAAC,gBAAgB,CAAC,GAAa;QAC1C,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,IAAI,CAAC;YACH,gFAAgF;YAChF,IAAI,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAE,CAAC;gBACnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;gBAC9E,MAAM,GAAG,GAAG,IAAI,GAAG,EAAuB,CAAC;gBAC1C,OAAoC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;gBACpF,OAAO,GAAG,CAAC;YACb,CAAC;iBAAM,CAAC;gBACN,8BAA8B;gBAC9B,eAAM,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;gBAC3F,MAAM,GAAG,GAAG,IAAI,GAAG,EAAuB,CAAC;gBAC3C,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC;oBACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;oBACxC,IAAI,MAAM,EAAE,CAAC;wBACX,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;oBACtB,CAAC;gBACH,CAAC;gBACD,OAAO,GAAG,CAAC;YACb,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAC1E,OAAO,IAAI,GAAG,EAAE,CAAC,CAAC,4BAA4B;QAChD,CAAC;IACH,CAAC;IAED;;;SAGK;IACG,YAAY,CAAC,MAAqB;QACxC,MAAM,OAAO,GAA4B,EAAE,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,qEAAqE;QACrE,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAC9D,IAAI,MAAM,CAAC,IAAI;YAAE,OAAO,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC;QACxD,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,qCAAqC;QAClI,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YAC/C,OAAO,CAAC,SAAS,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;QAC/E,CAAC;aAAM,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YAChC,OAAO,CAAC,SAAS,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC;QACrD,CAAC;aAAM,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YAC9B,OAAO,CAAC,SAAS,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;QACnD,CAAC;QAED,4DAA4D;QAC5D,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9E,qFAAqF;gBACrF,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,OAAuD;QAC3F,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,IAAI,CAAC;YACH,0BAA0B;YAC1B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAChD,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,eAAM,CAAC,IAAI,CAAC,iDAAiD,EAAE,EAAE,CAAC,CAAC;gBACnE,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,mCAAmC;YACnC,MAAM,aAAa,GAAgB;gBACjC,GAAG,cAAc;gBACjB,GAAG,OAAO;gBACV,uCAAuC;gBACvC,EAAE,EAAE,cAAc,CAAC,EAAE;gBACrB,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,yCAAyC;gBACzC,QAAQ,EAAE;oBACR,GAAG,cAAc,CAAC,QAAQ;oBAC1B,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;oBAC3B,mCAAmC;oBACnC,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;iBACxB;aACF,CAAC;YAEF,yBAAyB;YACzB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CACxC,mBAAmB,EACnB,EAAE,EACF;oBACE,OAAO,EAAE,aAAa,CAAC,OAAO;oBAC9B,SAAS,EAAE,aAAa,CAAC,SAAS;oBAClC,QAAQ,EAAE,aAAa,CAAC,QAAQ;iBACjC,CACF,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;YAC/D,CAAC;YAED,gDAAgD;YAChD,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE,CAAC;gBAC1F,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;gBAExC,8BAA8B;gBAC9B,MAAM,aAAa,GAAG,OAAO,aAAa,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAChI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;oBACrE,aAAa,CAAC,SAAS,GAAG,YAAY,CAAC;oBAEvC,wBAAwB;oBACxB,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBAC1E,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,EAAE,YAAY,EAAE,cAAuE,CAAC,CAAC;gBAC9H,CAAC;YACH,CAAC;YAED,oBAAoB;YACpB,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YAE/B,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;YAC9C,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,kCAAkC;IAElC;;;;SAIK;IACE,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,OAAiC;QAC9E,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAE9D,iDAAiD;QACjD,MAAM,UAAU,GAAgB;YAC9B,EAAE,EAAE,QAAQ,IAAA,SAAM,GAAE,EAAE;YACtB,SAAS,EAAE,SAAS;YACpB,0DAA0D;YAC1D,IAAI,EAAE,OAAO,YAAY,2BAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,YAAY,wBAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACjG,OAAO,EAAE,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,sCAAsC;YACxI,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,QAAQ,EAAE,OAAO,CAAC,iBAAiB,IAAI,EAAE,EAAE,wCAAwC;SACpF,CAAC;QAEF,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,mCAAmC,SAAS,KAAK,CAAC,CAAC;YAChE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,UAAgD,CAAC,CAAC;YAC3G,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,gBAAgB,UAAU,CAAC,EAAE,sBAAsB,SAAS,GAAG,CAAC,CAAC;YAC9E,8DAA8D;YAC9D,8CAA8C;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,eAAM,CAAC,KAAK,CAAC,0CAA0C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,+BAA+B,YAAY,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,KAAc;QAC3D,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAE9D,MAAM,cAAc,GAAG,KAAK,IAAI,IAAA,kBAAS,EAAS,gCAAgC,EAAE,GAAG,CAAC,CAAC;QAEzF,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,oCAAoC,SAAS,YAAY,cAAc,MAAM,CAAC,CAAC;YAC5F,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAC9C,uBAAuB,EACvB,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,uBAAuB;gBACjD,cAAc,EACd,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,mDAAmD;iBACtE,CAAC;gBAEF,+DAA+D;gBAC/D,MAAM,QAAQ,GAAmB,OAAoC;qBAClE,OAAO,EAAE,CAAC,oDAAoD;qBAC9D,GAAG,CAAC,MAAM,CAAC,EAAE;oBACZ,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,qCAAqC;oBACrE,wDAAwD;oBACxD,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;wBAC5B,OAAO,IAAI,2BAAY,CAAC,OAAO,CAAC,CAAC;oBACnC,CAAC;yBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;wBAChC,OAAO,IAAI,wBAAS,CAAC,OAAO,CAAC,CAAC;oBAChC,CAAC;yBAAM,CAAC;wBACN,uDAAuD;wBACvD,eAAM,CAAC,IAAI,CAAC,8BAA8B,MAAM,CAAC,IAAI,6BAA6B,SAAS,GAAG,CAAC,CAAC;wBAChG,mDAAmD;wBACnD,OAAO,IAAI,4BAAa,CAAC,kBAAkB,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,CAAC;oBACxE,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEL,eAAM,CAAC,KAAK,CAAC,aAAa,QAAQ,CAAC,MAAM,yBAAyB,SAAS,GAAG,CAAC,CAAC;gBAChF,yCAAyC;gBACzC,MAAM,OAAO,GAAG,IAAI,iCAAkB,EAAE,CAAC;gBACzC,0BAA0B;gBAC1B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,MAAM,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBACpC,CAAC;gBACD,OAAO,OAAO,CAAC;YACjB,CAAC;YACD,OAAO,IAAI,iCAAkB,EAAE,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,gCAAgC;YAChC,OAAO,IAAI,iCAAkB,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;;SAGK;IACE,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QAC7C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAC9D,eAAM,CAAC,IAAI,CAAC,qCAAqC,SAAS,KAAK,CAAC,CAAC;QACjE,IAAI,CAAC;YACH,uFAAuF;YACvF,IAAI,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC;gBACjC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,uBAAuB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;gBAC/F,eAAM,CAAC,IAAI,CAAC,WAAW,YAAY,8BAA8B,SAAS,GAAG,CAAC,CAAC;YACjF,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACzB,yDAAyD;gBACzD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,uBAAuB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;gBACzF,IAAI,YAAY,GAAG,CAAC,CAAC;gBACrB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oBAC7B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,uBAAuB,EAAG,MAAyB,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC7F,YAAY,EAAE,CAAC;oBACjB,CAAC;gBACH,CAAC;gBACD,eAAM,CAAC,IAAI,CAAC,WAAW,YAAY,8BAA8B,SAAS,wBAAwB,CAAC,CAAC;YACtG,CAAC;YACD,wBAAwB;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,eAAM,CAAC,KAAK,CAAC,4CAA4C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,6EAA6E;IAE7E;;;;;;SAMK;IACE,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,CAAU,EAAE,SAAS,GAAG,SAAS;QACjF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACzD,OAAO,IAAI,2BAAY,CAAC;YACtB,WAAW,EAAE,WAAW;YACxB,SAAS,EAAE,SAAS;YACpB,CAAC,EAAE,CAAC,IAAI,IAAA,kBAAS,EAAS,gCAAgC,EAAE,GAAG,CAAC,EAAE,wCAAwC;YAC1G,cAAc,EAAE,IAAI,EAAE,0CAA0C;SACjE,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;SAOK;IACE,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,GAAsB,EAAE,SAAS,GAAG,SAAS;QAC/F,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACzD,OAAO,IAAI,wCAAyB,CAAC;YACnC,GAAG,EAAE,GAAG;YACR,WAAW,EAAE,WAAW;YACxB,SAAS,EAAE,SAAS;YACpB,cAAc,EAAE,IAAI;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;SAOK;IACE,KAAK,CAAC,6BAA6B,CAAC,SAAS,GAAG,SAAS,EAAE,QAAiB;QACjF,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,YAAY,gCAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YACtF,MAAM,IAAI,KAAK,CAAC,6LAA6L,CAAC,CAAC;QACjN,CAAC;QAED,mEAAmE;QACnE,0DAA0D;QAC1D,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAA,kBAAS,EAAS,0BAA0B,EAAE,CAAC,CAAC,CAAC,CAAC;QAEjG,OAAO,IAAI,yCAA0B,CAAC;YACpC,oBAAoB,EAAE,SAAS;YAC/B,SAAS,EAAE,SAAS;YACpB,QAAQ,EAAE,QAAQ,EAAE,qBAAqB;SAC1C,CAAC,CAAC;IACL,CAAC;IAGD,8BAA8B;IAE9B;;OAEG;IACI,iBAAiB;QACtB,0EAA0E;QAC1E,4CAA4C;QAC5C,gCAAgC;QAChC,MAAM,eAAe,GAAG,IAAA,kBAAS,EAAS,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC1E,MAAM,cAAc,GAAG;YACrB,SAAS,EAAE,IAAA,kBAAS,EAAS,qCAAqC,EAAE,mBAAmB,CAAC;YACxF,cAAc,EAAE,IAAA,kBAAS,EAAS,0CAA0C,EAAE,kBAAkB,CAAC;SAClG,CAAC;QACF,MAAM,gBAAgB,GAAG;YACvB,MAAM,EAAE,IAAA,kBAAS,EAAS,oCAAoC,EAAE,EAAE,CAAC;YACnE,WAAW,EAAE,IAAA,kBAAS,EAAS,yCAAyC,EAAE,EAAE,CAAC;YAC7E,SAAS,EAAE,IAAA,kBAAS,EAAS,uCAAuC,EAAE,kBAAkB,CAAC;SAC1F,CAAC;QAEF,4BAA4B;QAC5B,MAAM,YAAY,GAAG,IAAA,kBAAS,EAAS,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QACpE,MAAM,cAAc,GAAG;YACrB,QAAQ,EAAE,IAAA,kBAAS,EAAS,iCAAiC,EAAE,sBAAsB,CAAC;SACvF,CAAC;QACF,MAAM,aAAa,GAAG;YACpB,IAAI,EAAE,IAAA,kBAAS,EAAS,4BAA4B,EAAE,WAAW,CAAC;YAClE,IAAI,EAAE,IAAA,kBAAS,EAAS,4BAA4B,EAAE,IAAI,CAAC;YAC3D,IAAI,EAAE,IAAA,kBAAS,EAAS,4BAA4B,EAAE,MAAM,CAAC;YAC7D,QAAQ,EAAE,IAAA,kBAAS,EAAS,gCAAgC,EAAE,EAAE,CAAC;YACjE,QAAQ,EAAE,IAAA,kBAAS,EAAS,gCAAgC,EAAE,SAAS,CAAC;YACxE,KAAK,EAAE,IAAA,kBAAS,EAAS,6BAA6B,EAAE,UAAU,CAAC;SACpE,CAAC;QACF,MAAM,gBAAgB,GAAG;YACvB,gBAAgB,EAAE,IAAA,kBAAS,EAAS,2CAA2C,EAAE,EAAE,CAAC;YACpF,MAAM,EAAE,IAAA,kBAAS,EAAS,iCAAiC,EAAE,QAAQ,CAAC;SACvE,CAAC;QACF,MAAM,eAAe,GAAG;YACtB,gBAAgB,EAAE,IAAA,kBAAS,EAAS,0CAA0C,EAAE,EAAE,CAAC;YACnF,QAAQ,EAAE,IAAA,kBAAS,EAAS,kCAAkC,EAAE,SAAS,CAAC;YAC1E,UAAU,EAAE,IAAA,kBAAS,EAAS,oCAAoC,EAAE,UAAU,CAAC;SAChF,CAAC;QACF,MAAM,aAAa,GAAG;YACpB,GAAG,EAAE,IAAA,kBAAS,EAAS,2BAA2B,EAAE,EAAE,CAAC;YACvD,SAAS,EAAE,IAAA,kBAAS,EAAS,iCAAiC,EAAE,UAAU,CAAC;SAC5E,CAAC;QAEF,iCAAiC;QACjC,MAAM,oBAAoB,GAAG;YAC3B,SAAS,EAAE,IAAA,kBAAS,EAAS,+BAA+B,EAAE,IAAI,CAAC;YACnE,YAAY,EAAE,IAAA,kBAAS,EAAS,kCAAkC,EAAE,GAAG,CAAC;YACxE,gBAAgB,EAAE,IAAA,kBAAS,EAAS,sCAAsC,EAAE,GAAG,CAAC;SACjF,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAA,kBAAS,EAAU,gBAAgB,EAAE,IAAI,CAAC;YACnD,MAAM,EAAE,IAAA,kBAAS,EAAkC,eAAe,EAAE,SAAS,CAAC;YAC9E,WAAW,EAAE,IAAA,kBAAS,EAAS,oBAAoB,EAAE,IAAI,CAAC;YAC1D,kBAAkB,EAAE,IAAA,kBAAS,EAAS,2BAA2B,EAAE,GAAG,CAAC;YACvE,mBAAmB,EAAE,IAAA,kBAAS,EAAS,4BAA4B,EAAE,EAAE,CAAC;YACxE,kBAAkB,EAAE,IAAA,kBAAS,EAAS,2BAA2B,EAAE,GAAG,CAAC;YACvE,iBAAiB,EAAE,IAAA,kBAAS,EAAS,0BAA0B,EAAE,CAAC,CAAC;YACnE,uBAAuB,EAAE,IAAA,kBAAS,EAAS,gCAAgC,EAAE,GAAG,CAAC;YACjF,mBAAmB,EAAE,IAAA,kBAAS,EAAU,4BAA4B,EAAE,IAAI,CAAC;YAC3E,wBAAwB,EAAE,IAAA,kBAAS,EAAU,iCAAiC,EAAE,IAAI,CAAC;YACrF,yBAAyB,EAAE,IAAA,kBAAS,EAAU,kCAAkC,EAAE,IAAI,CAAC;YACvF,yBAAyB,EAAE,IAAA,kBAAS,EAAU,kCAAkC,EAAE,IAAI,CAAC;YACvF,WAAW,EAAE,eAA4E;YACzF,mBAAmB,EAAE;gBACnB,MAAM,EAAE,cAAc;gBACtB,QAAQ,EAAE,gBAAgB;aAC3B;YACD,QAAQ,EAAE,YAAqE;YAC/E,gBAAgB,EAAE;gBAChB,MAAM,EAAE,cAAc;gBACtB,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE,gBAAgB;gBAC1B,OAAO,EAAE,eAAe;gBACxB,KAAK,EAAE,aAAa;aACrB;YACD,YAAY,EAAE,oBAAoB;YAClC,SAAS,EAAE,IAAA,kBAAS,EAAS,kBAAkB,EAAE,IAAI,CAAC;YACtD,WAAW,EAAE,IAAA,kBAAS,EAAS,oBAAoB,EAAE,MAAM,CAAC;YAC5D,SAAS,EAAE,IAAA,kBAAS,EAAS,kBAAkB,EAAE,EAAE,CAAC;YACpD,uBAAuB,EAAE,IAAA,kBAAS,EAAS,gCAAgC,EAAE,EAAE,CAAC;SACjF,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,oBAAoB,CAAC,QAAiC;QACjE,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,QAAQ,CAAC,CAAC;QACnD,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACjD,IAAI,uBAAuB,GAAG,KAAK,CAAC;QACpC,IAAI,oBAAoB,GAAG,KAAK,CAAC;QAEjC,IAAI,CAAC;YACH,MAAM,OAAO,GAAuB,EAAE,CAAC;YAEvC,4CAA4C;YAC5C,MAAM,MAAM,GAAG,CAAC,GAAW,EAAE,KAAc,EAAQ,EAAE;gBACnD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACxB,OAAO,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC,CAAC;YAEF,6BAA6B;YAC7B,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC3C,MAAM,CAAC,oBAAoB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;YACnD,MAAM,CAAC,2BAA2B,EAAE,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YACjE,MAAM,CAAC,0BAA0B,EAAE,QAAQ,CAAC,iBAAiB,CAAC,CAAC;YAC/D,MAAM,CAAC,gCAAgC,EAAE,QAAQ,CAAC,uBAAuB,CAAC,CAAC;YAE3E,gDAAgD;YAChD,IAAI,QAAQ,CAAC,WAAW,KAAK,SAAS,IAAI,QAAQ,CAAC,WAAW,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;gBAC/F,MAAM,CAAC,oBAAoB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACnD,uBAAuB,GAAG,IAAI,CAAC;YACjC,CAAC;YACD,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ,EAAE,CAAC;gBACtF,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC7C,oBAAoB,GAAG,IAAI,CAAC;YAC9B,CAAC;YAED,4DAA4D;YAC5D,IAAI,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBACjC,MAAM,gBAAgB,GAAG,IAAA,kBAAS,EAA0B,oBAAoB,EAAE,EAAE,CAAC,CAAC;gBACtF,IAAI,gBAAgB,CAAC,MAAM,IAAI,QAAQ,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;oBACnE,MAAM,CAAC,qCAAqC,EAAE,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAC7F,MAAM,CAAC,0CAA0C,EAAE,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;oBACvG,IAAI,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,KAAM,gBAAgB,CAAC,MAAkC,CAAC,SAAS,EAAE,CAAC;wBACrH,uBAAuB,GAAG,IAAI,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,MAAM,iBAAiB,GAAG,IAAA,kBAAS,EAA0B,iBAAiB,EAAE,EAAE,CAAC,CAAC;gBACpF,IAAI,iBAAiB,CAAC,MAAM,IAAI,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;oBACjE,MAAM,CAAC,iCAAiC,EAAE,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACrF,IAAI,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,KAAM,iBAAiB,CAAC,MAAkC,CAAC,QAAQ,EAAE,CAAC;wBACjH,oBAAoB,GAAG,IAAI,CAAC;oBAC9B,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC1B,MAAM,CAAC,+BAA+B,EAAE,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACzE,MAAM,CAAC,kCAAkC,EAAE,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;gBAC/E,MAAM,CAAC,sCAAsC,EAAE,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;YACzF,CAAC;YAED,0CAA0C;YAC1C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;YAC1E,CAAC;YAED,sCAAsC;YACtC,IAAI,uBAAuB,IAAI,oBAAoB,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBACzE,IAAI,CAAC;oBACH,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;wBAClB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;oBACxD,CAAC;oBACD,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACpC,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;oBACpD,OAAO,IAAI,CAAC;gBACd,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;oBACxD,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AApzCD,sDAozCC;AAED,4BAA4B;AACf,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\n// Core Polyfills & LangChain Components (Using relative paths)\r\nimport type { MemoryMetadata, MemorySource, MemoryType } from '../types';\r\nimport {\r\n  BufferMemory,\r\n  ConversationSummaryMemory,\r\n  VectorStoreRetrieverMemory,\r\n  ChatMessageHistory,\r\n  HumanMessage,\r\n  AIMessage,\r\n  SystemMessage,\r\n  MemoryVectorStore,\r\n  Embeddings,\r\n  OpenAIEmbeddings,\r\n  AsyncCaller // Used in custom embeddings\r\n} from '../../agents/workflows/corePolyfill';\r\nimport type {\r\n  BaseMessage, // Import BaseMessage for broader type usage\r\n  BaseLanguageModel // Needed for summary memory\r\n} from '../../agents/workflows/corePolyfill';\r\n\r\n// Local project imports (Ensure paths are correct)\r\nimport { logger } from '../../logger';\r\nimport type {\r\n  IMemoryProvider,\r\n  MemoryEntry,\r\n  MemorySearchOptions,\r\n  MemorySettings,\r\n  IVectorStore,\r\n  IDatabase,\r\n  ChatMessage, // Define or import ChatMessage type if needed separately\r\n  MemoryFilter,\r\n} from '../types'; // Assuming ChatMessage might be defined here\r\nimport { getConfig, setConfig } from '../../config';\r\nimport { llmService } from '../../llm/llmService'; // Used for embeddings and potentially summarization\r\nimport { VectorStoreFactory } from './vectorStores/vectorStoreFactory';\r\nimport { DatabaseFactory } from './databases/databaseFactory';\r\nimport { FileChunkingService } from './fileChunking';\r\n\r\n// Constants for database table/collection names\r\nconst MEMORIES_COLLECTION = 'memories';\r\nconst CHAT_HISTORY_COLLECTION = 'chat_history';\r\n\r\n/**\r\n * Codessa Memory Provider\r\n * Implements the IMemoryProvider interface using pluggable vector stores,\r\n * databases, and integrates LangChain memory components for enhanced functionality.\r\n * Manages both general memory entries (like file chunks) and structured chat history.\r\n */\r\nexport class CodessaMemoryProvider implements IMemoryProvider {\r\n  private context: vscode.ExtensionContext | undefined;\r\n  private vectorStore: IVectorStore | undefined;\r\n  private database: IDatabase | undefined;\r\n  private embeddings: Embeddings | undefined;\r\n  private initialized = false;\r\n  private isInitializing = false; // Prevent race conditions during init\r\n\r\n  // Event emitter for memory changes\r\n  private _onMemoriesChanged = new vscode.EventEmitter<void>();\r\n  readonly onMemoriesChanged = this._onMemoriesChanged.event;\r\n\r\n  /**\r\n     * Initialize the memory provider, setting up embeddings, vector store, and database.\r\n     * This method is idempotent and handles concurrent initialization calls.\r\n     * @param context The VS Code extension context.\r\n     */\r\n  public async initialize(context: vscode.ExtensionContext): Promise<void> {\r\n    if (this.initialized || this.isInitializing) {\r\n      // If already initialized or initialization is in progress, wait if necessary\r\n      if (this.isInitializing) {\r\n        // Wait for the ongoing initialization to complete\r\n        await new Promise<void>(resolve => {\r\n          const checkInterval = setInterval(() => {\r\n            if (!this.isInitializing) {\r\n              clearInterval(checkInterval);\r\n              resolve();\r\n            }\r\n          }, 100);\r\n        });\r\n      }\r\n      if (this.initialized) {\r\n        logger.debug('CodessaMemoryProvider already initialized.');\r\n      }\r\n      return;\r\n    }\r\n\r\n    this.isInitializing = true;\r\n    logger.info('Initializing Codessa memory provider...');\r\n\r\n    try {\r\n      this.context = context;\r\n\r\n      // 1. Initialize Embeddings\r\n      logger.debug('Initializing embeddings...');\r\n      this.embeddings = await this.createEmbeddings();\r\n      logger.info('Embeddings initialized.');\r\n\r\n      // 2. Initialize Vector Store\r\n      const vectorStoreType = getConfig<string>('memory.vectorStore', 'chroma');\r\n      logger.debug(`Initializing vector store (Type: ${vectorStoreType})...`);\r\n      // Pass the already created embeddings instance\r\n      this.vectorStore = await VectorStoreFactory.createVectorStore(vectorStoreType, this.embeddings);\r\n      await this.vectorStore.initialize(); // Initialize the specific vector store instance\r\n      logger.info(`Vector store '${vectorStoreType}' initialized.`);\r\n\r\n      // 3. Initialize Database\r\n      const databaseType = getConfig<string>('memory.database', 'sqlite');\r\n      logger.debug(`Initializing database (Type: ${databaseType})...`);\r\n      this.database = await DatabaseFactory.createDatabase(databaseType);\r\n      await this.database.initialize(); // Initialize the specific database instance\r\n      // Ensure necessary collections/tables exist\r\n      if (this.database.ensureCollection) {\r\n        await this.database.ensureCollection(MEMORIES_COLLECTION);\r\n        await this.database.ensureCollection(CHAT_HISTORY_COLLECTION);\r\n      } else {\r\n        logger.warn('Database does not support ensureCollection method. Collections may need to be created manually.');\r\n      }\r\n      logger.info(`Database '${databaseType}' initialized.`);\r\n\r\n      // 4. Initialize FileChunkingService\r\n      logger.debug('Initializing FileChunkingService...');\r\n      // Create a memory storer adapter that uses this provider to store memories\r\n      const memoryStorer = {\r\n        storeMemory: async (entryData: { content: string | Buffer; metadata: Record<string, unknown> }): Promise<MemoryEntry> => {\r\n          // Convert Buffer to string if needed\r\n          const content = Buffer.isBuffer(entryData.content)\r\n            ? entryData.content.toString('utf-8')\r\n            : entryData.content;\r\n\r\n          // Ensure metadata has required fields\r\n          const metadata: MemoryMetadata = {\r\n            ...entryData.metadata,\r\n            source: (entryData.metadata.source as MemorySource) || 'file',\r\n            type: (entryData.metadata.type as MemoryType) || 'code',\r\n            tags: Array.isArray(entryData.metadata.tags) ? entryData.metadata.tags : ['file', 'chunk']\r\n          };\r\n\r\n          return this.addMemory({\r\n            content,\r\n            metadata\r\n          });\r\n        }\r\n      };\r\n      FileChunkingService.initialize(memoryStorer);\r\n      logger.info('FileChunkingService initialized.');\r\n\r\n      this.initialized = true;\r\n      logger.info('Codessa memory provider initialized successfully.');\r\n\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      logger.warn('Error during Codessa memory provider initialization:', { message: errorMessage });\r\n\r\n      try {\r\n        // Attempt to initialize with fallback components\r\n        logger.info('Attempting to initialize with fallback components...');\r\n\r\n        // 1. Fallback Embeddings - Use our simple implementation\r\n        if (!this.embeddings) {\r\n          logger.info('Using fallback embeddings implementation');\r\n          class FallbackEmbeddings extends Embeddings {\r\n            async embedDocuments(texts: string[]): Promise<number[][]> {\r\n              // Add minimal async operation to justify async\r\n              await Promise.resolve();\r\n              return texts.map(text => this.generateSimpleVector(text));\r\n            }\r\n\r\n            async embedQuery(text: string): Promise<number[]> {\r\n              // Add minimal async operation to justify async\r\n              await Promise.resolve();\r\n              return this.generateSimpleVector(text);\r\n            }\r\n\r\n            private generateSimpleVector(text: string, dimensions = 384): number[] {\r\n              const vector: number[] = new Array(dimensions).fill(0) as number[];\r\n              const normalizedText = text.toLowerCase().trim();\r\n\r\n              for (let i = 0; i < normalizedText.length && i < dimensions; i++) {\r\n                vector[i % dimensions] += normalizedText.charCodeAt(i) / 255;\r\n              }\r\n\r\n              const magnitude = Math.sqrt(vector.reduce((sum: number, val: number) => sum + val * val, 0));\r\n              if (magnitude > 0) {\r\n                for (let i = 0; i < dimensions; i++) {\r\n                  vector[i] = vector[i] / magnitude;\r\n                }\r\n              }\r\n\r\n              return vector;\r\n            }\r\n          }\r\n\r\n          this.embeddings = new FallbackEmbeddings();\r\n        }\r\n\r\n        // 2. Fallback Vector Store - Use in-memory store\r\n        if (!this.vectorStore) {\r\n          logger.info('Using in-memory vector store as fallback');\r\n          this.vectorStore = await VectorStoreFactory.createVectorStore('memory', this.embeddings);\r\n          await this.vectorStore.initialize();\r\n        }\r\n\r\n        // 3. Fallback Database - Use SQLite\r\n        if (!this.database) {\r\n          logger.info('Using SQLite database as fallback');\r\n          this.database = await DatabaseFactory.createDatabase('sqlite');\r\n          await this.database.initialize();\r\n\r\n          if (this.database.ensureCollection) {\r\n            await this.database.ensureCollection(MEMORIES_COLLECTION);\r\n            await this.database.ensureCollection(CHAT_HISTORY_COLLECTION);\r\n          }\r\n        }\r\n\r\n        // 4. Initialize FileChunkingService with fallback components\r\n        const memoryStorer = {\r\n          storeMemory: async (entryData: { content: string | Buffer; metadata: Record<string, unknown> }): Promise<MemoryEntry> => {\r\n            const content = Buffer.isBuffer(entryData.content)\r\n              ? entryData.content.toString('utf-8')\r\n              : entryData.content;\r\n\r\n            const metadata: MemoryMetadata = {\r\n              ...entryData.metadata,\r\n              source: (entryData.metadata.source as MemorySource) || 'file',\r\n              type: (entryData.metadata.type as MemoryType) || 'code',\r\n              tags: Array.isArray(entryData.metadata.tags) ? entryData.metadata.tags : ['file', 'chunk']\r\n            };\r\n\r\n            return this.addMemory({\r\n              content,\r\n              metadata\r\n            });\r\n          }\r\n        };\r\n\r\n        FileChunkingService.initialize(memoryStorer);\r\n\r\n        this.initialized = true;\r\n        logger.info('Codessa memory provider initialized with fallback components');\r\n\r\n      } catch (fallbackError) {\r\n        const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);\r\n        const originalErrorMessage = error instanceof Error ? error.message : String(error);\r\n        // If even the fallback initialization fails, log and set initialized to false\r\n        logger.error('Failed to initialize Codessa memory provider with fallbacks:',\r\n          { message: fallbackErrorMessage, originalError: originalErrorMessage });\r\n\r\n        // Reset state on failure\r\n        this.initialized = false;\r\n        this.embeddings = undefined;\r\n        this.vectorStore = undefined;\r\n        this.database = undefined;\r\n      }\r\n    } finally {\r\n      this.isInitializing = false; // Mark initialization as complete (success or fail)\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Ensures the provider is initialized before proceeding.\r\n     * Attempts to initialize components if missing.\r\n     */\r\n  private async assertInitialized(): Promise<void> {\r\n    // If already fully initialized, return immediately\r\n    if (this.initialized && this.database && this.vectorStore && this.embeddings) {\r\n      return;\r\n    }\r\n\r\n    // If not initialized at all, try to initialize\r\n    if (!this.initialized) {\r\n      logger.warn('Memory provider accessed before initialization. Attempting to initialize now.');\r\n      if (this.context) {\r\n        try {\r\n          await this.initialize(this.context);\r\n        } catch (error) {\r\n          // Error already logged in initialize method\r\n        }\r\n      } else {\r\n        logger.error('Cannot initialize memory provider: No context available');\r\n      }\r\n    }\r\n\r\n    // Check if we're still missing components after initialization attempt\r\n    if (!this.initialized || !this.database || !this.vectorStore || !this.embeddings) {\r\n      // Try to create missing components individually\r\n      try {\r\n        logger.warn('Memory provider missing components. Attempting to create fallbacks.');\r\n\r\n        // Create embeddings if missing\r\n        if (!this.embeddings) {\r\n          logger.info('Creating fallback embeddings');\r\n          class FallbackEmbeddings extends Embeddings {\r\n            async embedDocuments(texts: string[]): Promise<number[][]> {\r\n              // Add minimal async operation to justify async\r\n              await Promise.resolve();\r\n              return texts.map(text => this.generateSimpleVector(text));\r\n            }\r\n\r\n            async embedQuery(text: string): Promise<number[]> {\r\n              // Add minimal async operation to justify async\r\n              await Promise.resolve();\r\n              return this.generateSimpleVector(text);\r\n            }\r\n\r\n            private generateSimpleVector(text: string, dimensions = 384): number[] {\r\n              const vector: number[] = new Array(dimensions).fill(0) as number[];\r\n              const normalizedText = text.toLowerCase().trim();\r\n\r\n              for (let i = 0; i < normalizedText.length && i < dimensions; i++) {\r\n                vector[i % dimensions] += normalizedText.charCodeAt(i) / 255;\r\n              }\r\n\r\n              const magnitude = Math.sqrt(vector.reduce((sum: number, val: number) => sum + val * val, 0));\r\n              if (magnitude > 0) {\r\n                for (let i = 0; i < dimensions; i++) {\r\n                  vector[i] = vector[i] / magnitude;\r\n                }\r\n              }\r\n\r\n              return vector;\r\n            }\r\n          }\r\n\r\n          this.embeddings = new FallbackEmbeddings();\r\n        }\r\n\r\n        // Create vector store if missing\r\n        if (!this.vectorStore) {\r\n          logger.info('Creating fallback vector store');\r\n          this.vectorStore = await VectorStoreFactory.createVectorStore('memory', this.embeddings);\r\n          await this.vectorStore.initialize();\r\n        }\r\n\r\n        // Create database if missing\r\n        if (!this.database) {\r\n          logger.info('Creating fallback database');\r\n          this.database = await DatabaseFactory.createDatabase('sqlite');\r\n          await this.database.initialize();\r\n\r\n          if (this.database.ensureCollection) {\r\n            await this.database.ensureCollection(MEMORIES_COLLECTION);\r\n            await this.database.ensureCollection(CHAT_HISTORY_COLLECTION);\r\n          }\r\n        }\r\n\r\n        // Mark as initialized if we have all components now\r\n        if (this.database && this.vectorStore && this.embeddings) {\r\n          this.initialized = true;\r\n          logger.info('Memory provider successfully initialized with fallback components');\r\n        }\r\n      } catch (error) {\r\n        logger.error('Failed to create fallback components:', error);\r\n        throw new Error('Memory provider could not be initialized with fallbacks');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Creates an Embeddings instance based on the configured LLM provider or fallback.\r\n     */\r\n  private async createEmbeddings(): Promise<Embeddings> {\r\n    const provider = await llmService.getDefaultProvider();\r\n\r\n    // Check if the provider has a dedicated, potentially optimized embedding method\r\n    if (provider?.getEmbeddings) {\r\n      try {\r\n        logger.info('Using provider\\'s dedicated getEmbeddings() method.');\r\n        const providerEmbeddings = await provider.getEmbeddings() as unknown;\r\n        if (providerEmbeddings &&\r\n            typeof (providerEmbeddings as { embedQuery?: unknown }).embedQuery === 'function' &&\r\n            typeof (providerEmbeddings as { embedDocuments?: unknown }).embedDocuments === 'function') {\r\n          return providerEmbeddings as Embeddings;\r\n        } else {\r\n          logger.warn('Provider\\'s getEmbeddings() did not return a valid Embeddings instance. Falling back...');\r\n        }\r\n      } catch (e) {\r\n        const errorMessage = e instanceof Error ? e.message : String(e);\r\n        logger.warn(`Error obtaining embeddings via provider.getEmbeddings(): ${errorMessage}. Falling back...`);\r\n      }\r\n    }\r\n\r\n\r\n    // Fallback 1: Use provider's generic generateEmbedding function if available\r\n    if (provider?.generateEmbedding) {\r\n      logger.info('Using provider\\'s generateEmbedding function for custom Embeddings wrapper.');\r\n      const generateEmbedding = provider.generateEmbedding.bind(provider);\r\n      // Create a custom embeddings class adhering to the LangChain interface\r\n      class ProviderEmbeddingsWrapper extends Embeddings {\r\n        // Leverage AsyncCaller for concurrency and retries if needed\r\n        caller: AsyncCaller;\r\n        dim = 1536; // Default dimension for embeddings\r\n        seed = 'provider_embeddings_wrapper';\r\n\r\n        constructor() {\r\n          super();\r\n          this.caller = new AsyncCaller({ /* options */ });\r\n        }\r\n\r\n        async generateVector(text: string): Promise<number[]> {\r\n          return generateEmbedding(text);\r\n        }\r\n\r\n        async embedDocuments(texts: string[]): Promise<number[][]> {\r\n          // Could potentially batch if the provider supports it, otherwise loop\r\n          const embeddings: number[][] = [];\r\n          for (const text of texts) {\r\n            // Wrap the call with the caller for potential retries/concurrency\r\n            const embedding = await this.caller.call(async () => generateEmbedding(text));\r\n            embeddings.push(embedding);\r\n          }\r\n          return embeddings;\r\n        }\r\n\r\n        async embedQuery(text: string): Promise<number[]> {\r\n          // Wrap the call with the caller\r\n          return this.caller.call(async () => generateEmbedding(text));\r\n        }\r\n        // Implement batch embedding if the underlying provider supports it for efficiency\r\n        // async embedBatch?(texts: string[]): Promise<number[][]>;\r\n      }\r\n      return new ProviderEmbeddingsWrapper();\r\n    }\r\n\r\n    // Fallback 2: Use OpenAI embeddings if API key is available\r\n    const openAIApiKey = process.env.OPENAI_API_KEY ?? getConfig<string | undefined>('llm.providers.openai.apiKey', undefined);\r\n    if (openAIApiKey) {\r\n      logger.warn('LLM provider does not support embeddings. Falling back to OpenAIEmbeddings.');\r\n      try {\r\n        return new OpenAIEmbeddings({ openAIApiKey });\r\n      } catch (e) {\r\n        const errorMessage = e instanceof Error ? e.message : String(e);\r\n        logger.error(`Failed to initialize OpenAIEmbeddings fallback: ${errorMessage}`);\r\n        // Continue to final error\r\n      }\r\n    }\r\n\r\n    // Final Fallback: Use a simple in-memory embedding implementation\r\n    logger.warn('No standard embedding capability available. Using simple fallback embeddings.');\r\n\r\n    // Create a simple fallback embeddings class that uses a basic hashing approach\r\n    class FallbackEmbeddings extends Embeddings {\r\n      // Generate a simple vector from text using character codes\r\n      private generateSimpleVector(text: string, dimensions = 384): number[] {\r\n        // Initialize vector with zeros\r\n        const vector: number[] = new Array(dimensions).fill(0) as number[];\r\n\r\n        // Normalize text\r\n        const normalizedText = text.toLowerCase().trim();\r\n\r\n        // Fill vector with values derived from text\r\n        for (let i = 0; i < normalizedText.length && i < dimensions; i++) {\r\n          // Use character code as a basis for the vector value\r\n          vector[i % dimensions] += normalizedText.charCodeAt(i) / 255;\r\n        }\r\n\r\n        // Normalize vector to unit length\r\n        const magnitude = Math.sqrt(vector.reduce((sum: number, val: number) => sum + val * val, 0));\r\n        if (magnitude > 0) {\r\n          for (let i = 0; i < dimensions; i++) {\r\n            vector[i] = vector[i] / magnitude;\r\n          }\r\n        }\r\n\r\n        return vector;\r\n      }\r\n\r\n      async embedDocuments(texts: string[]): Promise<number[][]> {\r\n        // Add minimal async operation to justify async\r\n        await Promise.resolve();\r\n        return texts.map(text => this.generateSimpleVector(text));\r\n      }\r\n\r\n      async embedQuery(text: string): Promise<number[]> {\r\n        // Add minimal async operation to justify async\r\n        await Promise.resolve();\r\n        return this.generateSimpleVector(text);\r\n      }\r\n    }\r\n\r\n    logger.info('Using fallback embeddings implementation. This is less accurate but allows the memory system to function.');\r\n    return new FallbackEmbeddings();\r\n  }\r\n\r\n  /**\r\n     * Adds a general memory entry (e.g., file chunk, web snippet) to the database and vector store.\r\n     * @param memory The memory data, excluding id and timestamp.\r\n     * @returns The created MemoryEntry with assigned id and timestamp.\r\n     */\r\n  public async addMemory(memory: Omit<MemoryEntry, 'id' | 'timestamp' | 'embedding'>): Promise<MemoryEntry> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n\r\n    const id = `mem_${uuidv4()}`;\r\n    const timestamp = Date.now();\r\n    const contentString = typeof memory.content === 'string' ? memory.content : JSON.stringify(memory.content); // Ensure content is string for embedding\r\n\r\n    if (!contentString || contentString.trim().length === 0) {\r\n      logger.warn('Attempted to add memory with empty content. Skipping. Metadata:', memory.metadata);\r\n      throw new Error('Cannot add memory with empty content.');\r\n    }\r\n\r\n    const newMemory: MemoryEntry = {\r\n      id,\r\n      content: memory.content, // Store original content (could be object)\r\n      timestamp,\r\n      metadata: memory.metadata ?? {\r\n        source: 'unknown',\r\n        type: 'unknown'\r\n      },\r\n      embedding: undefined, // Initialize embedding as undefined\r\n    };\r\n\r\n    try {\r\n      logger.debug(`Adding memory entry ${id}...`);\r\n\r\n      // 1. Generate Embedding\r\n      if (this.embeddings) {\r\n        // Use contentString for embedding generation\r\n        const embedding = await this.embeddings.embedQuery(contentString);\r\n        newMemory.embedding = embedding; // Store embedding within the entry object\r\n\r\n        // 2. Store in Database (Store the complete MemoryEntry including the embedding)\r\n        // Ensure the database adapter can handle the 'embedding' field (e.g., store as JSON/Blob or handle separately)\r\n        if (this.database) {\r\n          await this.database.addRecord(MEMORIES_COLLECTION, newMemory as unknown as Record<string, unknown>);\r\n        }\r\n        logger.debug(`Memory entry ${id} stored in database.`);\r\n\r\n        // 3. Store in Vector Store\r\n        // The vector store needs the ID, the vector, and searchable metadata.\r\n        // Pass only relevant metadata for vector search filtering.\r\n        const vectorMetadata = this.prepareVectorMetadata(newMemory.metadata);\r\n        if (this.vectorStore) {\r\n          await this.vectorStore.addVector(id, embedding, vectorMetadata as Record<string, string | number | boolean | undefined>);\r\n        }\r\n        logger.debug(`Memory entry ${id} added to vector store.`);\r\n\r\n        this._onMemoriesChanged.fire();\r\n        logger.info(`Successfully added memory entry ${id}.`);\r\n        return newMemory;\r\n      } else {\r\n        throw new Error('Embeddings not initialized');\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      const errorStack = error instanceof Error ? error.stack : '';\r\n      logger.error(`Failed to add memory entry ${id}:`, { message: errorMessage, stack: errorStack });\r\n      // Attempt cleanup if partial additions occurred (optional, depends on desired atomicity)\r\n      // await this.database!.deleteRecord(MEMORIES_COLLECTION, id).catch(e => logger.warn(`Cleanup failed for DB entry ${id}: ${e.message}`));\r\n      // await this.vectorStore!.deleteVector(id).catch(e => logger.warn(`Cleanup failed for VS entry ${id}: ${e.message}`));\r\n      throw new Error(`Failed to add memory: ${errorMessage}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Prepares metadata specifically for the vector store, removing potentially large or irrelevant fields.\r\n     */\r\n  private prepareVectorMetadata(originalMetadata: Record<string, unknown>): Record<string, unknown> {\r\n    const vectorMetadata: Record<string, unknown> = {};\r\n    const allowedKeys = ['source', 'type', 'tags', 'filePath', 'fileName', 'extension', 'chunkIndex', 'chunkId', 'url', 'sessionId', 'userId']; // Add keys relevant for filtering\r\n    const maxTagLength = 50; // Limit tag length\r\n    const maxTags = 20; // Limit number of tags\r\n\r\n    for (const key in originalMetadata) {\r\n      if (allowedKeys.includes(key)) {\r\n        const value = originalMetadata[key];\r\n        if (key === 'tags' && Array.isArray(value)) {\r\n          // Sanitize and limit tags\r\n          vectorMetadata[key] = value\r\n            .map(tag => String(tag).substring(0, maxTagLength))\r\n            .slice(0, maxTags);\r\n        } else if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\r\n          // Only include simple types directly suitable for filtering\r\n          vectorMetadata[key] = value;\r\n        }\r\n        // Add more specific handling if needed (e.g., date ranges)\r\n      }\r\n    }\r\n    // Ensure essential fields for linking back are present if available\r\n    if (originalMetadata.chunkId) vectorMetadata.chunkId = originalMetadata.chunkId;\r\n    if (originalMetadata.sessionId) vectorMetadata.sessionId = originalMetadata.sessionId;\r\n\r\n    return vectorMetadata;\r\n  }\r\n\r\n\r\n  /**\r\n     * Retrieves all general memory entries from the database.\r\n     * @param limit Max number of entries to retrieve.\r\n     * @returns An array of MemoryEntry objects.\r\n     */\r\n  public async getMemories(limit?: number): Promise<MemoryEntry[]> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n    try {\r\n      const effectiveLimit = limit ?? getConfig<number>('memory.maxMemories', 1000);\r\n      // Query database, potentially sorting by timestamp descending\r\n      if (this.database) {\r\n        const records = await this.database.queryRecords(\r\n          MEMORIES_COLLECTION,\r\n          {}, // Empty filter for all\r\n          effectiveLimit,\r\n          { timestamp: -1 } // Sort by timestamp descending\r\n        );\r\n        // Cast needed as queryRecords returns generic objects\r\n        return records as unknown as MemoryEntry[];\r\n      }\r\n      return [];\r\n    } catch (error) {\r\n      logger.error('Failed to get memories:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Retrieves a specific memory entry by its ID from the database.\r\n     * @param id The unique ID of the memory entry.\r\n     * @returns The MemoryEntry or undefined if not found.\r\n     */\r\n  public async getMemory(id: string): Promise<MemoryEntry | undefined> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n    try {\r\n      if (this.database) {\r\n        const record = await this.database.getRecord(MEMORIES_COLLECTION, id);\r\n        return record as MemoryEntry | undefined; // Cast needed\r\n      }\r\n      return undefined;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      // Log expected \"not found\" errors differently?\r\n      if (errorMessage.toLowerCase().includes('not found')) {\r\n        logger.debug(`Memory entry ${id} not found in database.`);\r\n      } else {\r\n        logger.error(`Failed to get memory ${id}:`, error);\r\n      }\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Deletes a memory entry by its ID from both the database and vector store.\r\n     * @param id The unique ID of the memory entry.\r\n     * @returns True if deletion was successful in both stores, false otherwise.\r\n     */\r\n  public async deleteMemory(id: string): Promise<boolean> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n    let dbSuccess = false;\r\n    let vsSuccess = false;\r\n    try {\r\n      logger.debug(`Attempting to delete memory entry ${id}...`);\r\n      // Delete from database\r\n      if (this.database) {\r\n        dbSuccess = await this.database.deleteRecord(MEMORIES_COLLECTION, id);\r\n      }\r\n      logger.debug(`Database deletion result for ${id}: ${dbSuccess}`);\r\n\r\n      // Delete from vector store\r\n      if (this.vectorStore) {\r\n        vsSuccess = await this.vectorStore.deleteVector(id);\r\n      }\r\n      logger.debug(`Vector store deletion result for ${id}: ${vsSuccess}`);\r\n\r\n      if (dbSuccess || vsSuccess) { // Fire event if deleted from at least one place\r\n        this._onMemoriesChanged.fire();\r\n        logger.info(`Deletion result for memory ${id}: DB=${dbSuccess}, VS=${vsSuccess}`);\r\n      } else {\r\n        logger.warn(`Memory entry ${id} not found for deletion in either DB or VS.`);\r\n      }\r\n      // Return true only if deleted from both (or if it didn't exist in one initially)\r\n      // A more nuanced return might be needed depending on strictness.\r\n      // Let's consider it successful if the final state is \"it's gone\".\r\n      return true; // Assume success if no errors thrown and attempts made.\r\n\r\n    } catch (error) {\r\n      logger.error(`Failed to delete memory ${id}:`, error);\r\n      return false; // Explicit failure on error\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Clears all general memory entries from the database and vector store.\r\n     * Warning: This is a destructive operation.\r\n     */\r\n  public async clearMemories(): Promise<void> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n    logger.warn('Clearing ALL general memory entries...');\r\n    try {\r\n      // Clear database collection\r\n      if (this.database) {\r\n        await this.database.clearCollection(MEMORIES_COLLECTION);\r\n      }\r\n      logger.debug(`Cleared database collection: ${MEMORIES_COLLECTION}`);\r\n\r\n      // Clear vector store\r\n      if (this.vectorStore) {\r\n        await this.vectorStore.clearVectors();\r\n      }\r\n      logger.debug('Cleared vector store.');\r\n\r\n      this._onMemoriesChanged.fire();\r\n      logger.info('All general memory entries cleared successfully.');\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      logger.error('Failed to clear memories:', error);\r\n      throw new Error(`Failed to clear memories: ${errorMessage}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Searches general memory entries based on metadata filters and optional text query using the database.\r\n     * @param options Search criteria including filters and limit.\r\n     * @returns An array of matching MemoryEntry objects.\r\n     */\r\n  public async searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n    try {\r\n      const { query, limit = 10, filter } = options;\r\n      logger.debug('Searching memories with options:', { query: query ? 'present' : 'absent', limit, filter });\r\n\r\n      // Build database query from filter\r\n      const dbQuery = this.buildDbQuery(filter);\r\n\r\n      // Add text search if query is provided and database supports it\r\n      if (query && this.database?.supportsTextSearch) {\r\n        // Adapt the text search syntax based on the specific database implementation\r\n        // This might require changes in the IDatabase interface or specific adapters\r\n        // Example for a MongoDB-like syntax:\r\n        // dbQuery['$text'] = { $search: query };\r\n        logger.warn('Text search query construction depends on the database implementation. Using placeholder logic.');\r\n        // Placeholder: Assume metadata contains searchable text fields if no $text support\r\n        // dbQuery['metadata.content_summary'] = { $regex: query, $options: 'i' }; // Example fallback\r\n      } else if (query) {\r\n        logger.warn(`Database (${getConfig<string>('memory.database', 'sqlite')}) does not support text search, or query was empty. Searching based on filters only.`);\r\n      }\r\n\r\n\r\n      // Execute query\r\n      if (this.database) {\r\n        const records = await this.database.queryRecords(\r\n          MEMORIES_COLLECTION,\r\n          dbQuery,\r\n          limit,\r\n          { timestamp: -1 } // Default sort\r\n        );\r\n        logger.debug(`Database query returned ${records.length} records.`);\r\n        return records as unknown as MemoryEntry[]; // Cast needed\r\n      }\r\n      return [];\r\n    } catch (error) {\r\n      logger.error('Failed to search memories:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Searches memory entries by semantic similarity to a query string using the vector store.\r\n     * @param query The query string.\r\n     * @param options Optional search options including filters, limit, and relevance threshold.\r\n     * @returns An array of MemoryEntry objects, sorted by relevance, potentially including relevance score in metadata.\r\n     */\r\n  public async searchSimilarMemories(query: string, options: Partial<MemorySearchOptions> = {}): Promise<MemoryEntry[]> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n\r\n    const { limit = getConfig<number>('memory.contextWindowSize', 5), filter: metadataFilter, relevanceThreshold = getConfig<number>('memory.relevanceThreshold', 0.7) } = options;\r\n    logger.debug(`Searching similar memories for query \"${query.substring(0, 50)}...\"`, { limit, metadataFilter, relevanceThreshold });\r\n\r\n    try {\r\n      if (this.embeddings && this.vectorStore) {\r\n        // 1. Generate embedding for the query\r\n        const queryEmbedding = await this.embeddings.embedQuery(query);\r\n\r\n        // 2. Prepare vector store filter from MemoryFilter\r\n        const vectorFilter = this.prepareVectorMetadata(metadataFilter ?? {}); // Reuse metadata prep logic\r\n\r\n        // 3. Search vector store\r\n        const results = await this.vectorStore.searchSimilarVectors(queryEmbedding, limit, vectorFilter as Record<string, string | number | boolean | undefined>);\r\n        logger.debug(`Vector store search returned ${results.length} raw results.`);\r\n\r\n        // 4. Filter by relevance threshold\r\n        const relevantResults = results.filter(result => result.score >= relevanceThreshold);\r\n        logger.debug(`Found ${relevantResults.length} results above relevance threshold ${relevanceThreshold}.`);\r\n\r\n        if (relevantResults.length === 0) {\r\n          return [];\r\n        }\r\n\r\n        // 5. Retrieve full memory entries from the database\r\n        // Optimize by fetching multiple IDs at once if the database supports it\r\n        const idsToFetch = relevantResults.map(r => r.id);\r\n        const memoryMap = await this.getMemoriesByIds(idsToFetch);\r\n\r\n        // 6. Combine results and add relevance score\r\n        const finalMemories: MemoryEntry[] = [];\r\n        for (const result of relevantResults) {\r\n          const memory = memoryMap.get(result.id);\r\n          if (memory) {\r\n            finalMemories.push({\r\n              ...memory,\r\n              metadata: {\r\n                ...memory.metadata,\r\n                relevance: result.score, // Add relevance score\r\n              },\r\n            });\r\n          } else {\r\n            logger.warn(`Memory entry ${result.id} found in vector store but not in database.`);\r\n          }\r\n        }\r\n\r\n        // Ensure sorting by relevance score (descending)\r\n        finalMemories.sort((a, b) => Number(b.metadata.relevance ?? 0) - Number(a.metadata.relevance ?? 0));\r\n\r\n        logger.info(`Returning ${finalMemories.length} similar memories for query.`);\r\n        return finalMemories;\r\n      }\r\n      return [];\r\n    } catch (error) {\r\n      logger.error(`Failed to search similar memories for query \"${query.substring(0, 50)}...\":`, error);\r\n      // Optional: Fall back to text search on error?\r\n      // logger.warn(\"Falling back to text search due to similarity search error.\");\r\n      // return this.searchMemories({ query, limit, filter: metadataFilter });\r\n      return []; // Return empty on error for now\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Helper to fetch multiple memory entries by IDs efficiently.\r\n     * @param ids Array of memory entry IDs.\r\n     * @returns A Map where keys are IDs and values are MemoryEntry objects.\r\n     */\r\n  private async getMemoriesByIds(ids: string[]): Promise<Map<string, MemoryEntry>> {\r\n    if (ids.length === 0) {\r\n      return new Map();\r\n    }\r\n    await this.assertInitialized();\r\n    try {\r\n      // Use database's batch get method if available, otherwise loop (less efficient)\r\n      if (this.database?.getRecordsByIds) {\r\n        const records = await this.database.getRecordsByIds(MEMORIES_COLLECTION, ids);\r\n        const map = new Map<string, MemoryEntry>();\r\n        (records as unknown as MemoryEntry[]).forEach(record => map.set(record.id, record));\r\n        return map;\r\n      } else {\r\n        // Fallback to individual gets\r\n        logger.warn('Database does not support batch getRecordsByIds. Fetching IDs individually.');\r\n        const map = new Map<string, MemoryEntry>();\r\n        for (const id of ids) {\r\n          const memory = await this.getMemory(id);\r\n          if (memory) {\r\n            map.set(id, memory);\r\n          }\r\n        }\r\n        return map;\r\n      }\r\n    } catch (error) {\r\n      logger.error(`Failed to fetch memories by IDs: ${ids.join(', ')}`, error);\r\n      return new Map(); // Return empty map on error\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Builds a database query object from the MemoryFilter.\r\n     * Needs adaptation based on the specific database query language.\r\n     */\r\n  private buildDbQuery(filter?: MemoryFilter): Record<string, unknown> {\r\n    const dbQuery: Record<string, unknown> = {};\r\n    if (!filter) {\r\n      return dbQuery;\r\n    }\r\n\r\n    // Example mapping (adjust based on IDatabase implementation details)\r\n    if (filter.source) dbQuery['metadata.source'] = filter.source;\r\n    if (filter.type) dbQuery['metadata.type'] = filter.type;\r\n    if (filter.tags && filter.tags.length > 0) dbQuery['metadata.tags'] = { $all: filter.tags }; // Assumes MongoDB-like $all operator\r\n    if (filter.fromTimestamp && filter.toTimestamp) {\r\n      dbQuery.timestamp = { $gte: filter.fromTimestamp, $lte: filter.toTimestamp };\r\n    } else if (filter.fromTimestamp) {\r\n      dbQuery.timestamp = { $gte: filter.fromTimestamp };\r\n    } else if (filter.toTimestamp) {\r\n      dbQuery.timestamp = { $lte: filter.toTimestamp };\r\n    }\r\n\r\n    // Add custom filters (assuming they target metadata fields)\r\n    for (const key in filter) {\r\n      if (!['source', 'type', 'tags', 'fromTimestamp', 'toTimestamp'].includes(key)) {\r\n        // Be cautious with direct key mapping - might need sanitization or specific handling\r\n        dbQuery[`metadata.${key}`] = filter[key];\r\n      }\r\n    }\r\n    return dbQuery;\r\n  }\r\n\r\n  /**\r\n   * Updates an existing memory entry with the provided updates.\r\n   * @param id The ID of the memory to update\r\n   * @param updates The updates to apply to the memory\r\n   * @returns The updated memory entry, or undefined if not found\r\n   */\r\n  public async updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n\r\n    try {\r\n      // Get the existing memory\r\n      const existingMemory = await this.getMemory(id);\r\n      if (!existingMemory) {\r\n        logger.warn(`Cannot update memory: No memory found with ID ${id}`);\r\n        return undefined;\r\n      }\r\n\r\n      // Create the updated memory object\r\n      const updatedMemory: MemoryEntry = {\r\n        ...existingMemory,\r\n        ...updates,\r\n        // Ensure these fields can't be updated\r\n        id: existingMemory.id,\r\n        timestamp: existingMemory.timestamp,\r\n        // Merge metadata if it exists in updates\r\n        metadata: {\r\n          ...existingMemory.metadata,\r\n          ...(updates.metadata ?? {}),\r\n          // Update the lastUpdated timestamp\r\n          lastUpdated: Date.now()\r\n        }\r\n      };\r\n\r\n      // Update in the database\r\n      let success = false;\r\n      if (this.database) {\r\n        success = await this.database.updateRecord(\r\n          MEMORIES_COLLECTION,\r\n          id,\r\n          {\r\n            content: updatedMemory.content,\r\n            timestamp: updatedMemory.timestamp,\r\n            metadata: updatedMemory.metadata\r\n          }\r\n        );\r\n      }\r\n\r\n      if (!success) {\r\n        throw new Error(`Failed to update memory ${id} in database`);\r\n      }\r\n\r\n      // Update in the vector store if content changed\r\n      if (this.vectorStore && (updates.content !== undefined || updates.metadata !== undefined)) {\r\n        // 1. Delete the old vector\r\n        await this.vectorStore.deleteVector(id);\r\n\r\n        // 2. Generate a new embedding\r\n        const contentString = typeof updatedMemory.content === 'string' ? updatedMemory.content : JSON.stringify(updatedMemory.content);\r\n        if (this.embeddings) {\r\n          const newEmbedding = await this.embeddings.embedQuery(contentString);\r\n          updatedMemory.embedding = newEmbedding;\r\n\r\n          // 3. Add the new vector\r\n          const vectorMetadata = this.prepareVectorMetadata(updatedMemory.metadata);\r\n          await this.vectorStore.addVector(id, newEmbedding, vectorMetadata as Record<string, string | number | boolean | undefined>);\r\n        }\r\n      }\r\n\r\n      // Emit change event\r\n      this._onMemoriesChanged.fire();\r\n\r\n      logger.debug(`Updated memory with ID: ${id}`);\r\n      return updatedMemory;\r\n    } catch (error) {\r\n      logger.error(`Failed to update memory ${id}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // --- Chat History Management ---\r\n\r\n  /**\r\n     * Adds a chat message to the history for a specific session.\r\n     * @param sessionId Identifier for the conversation session.\r\n     * @param message A HumanMessage or AIMessage object.\r\n     */\r\n  public async addChatMessage(sessionId: string, message: HumanMessage | AIMessage): Promise<void> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n\r\n    if (!sessionId) throw new Error('sessionId cannot be empty.');\r\n\r\n    // Convert LangChain message to a storable format\r\n    const chatRecord: ChatMessage = {\r\n      id: `chat_${uuidv4()}`,\r\n      sessionId: sessionId,\r\n      // Use instanceof for type checking BaseMessage subclasses\r\n      type: message instanceof HumanMessage ? 'human' : message instanceof AIMessage ? 'ai' : 'unknown',\r\n      content: typeof message.content === 'string' ? message.content : JSON.stringify(message.content), // Handle potential non-string content\r\n      timestamp: Date.now(),\r\n      metadata: message.additional_kwargs ?? {}, // Store additional arguments if present\r\n    };\r\n\r\n    try {\r\n      logger.debug(`Adding chat message for session ${sessionId}...`);\r\n      if (this.database) {\r\n        await this.database.addRecord(CHAT_HISTORY_COLLECTION, chatRecord as unknown as Record<string, unknown>);\r\n      }\r\n      logger.debug(`Chat message ${chatRecord.id} added for session ${sessionId}.`);\r\n      // Optionally fire a different event for chat history changes?\r\n      // this._onChatHistoryChanged.fire(sessionId);\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      logger.error(`Failed to add chat message for session ${sessionId}:`, error);\r\n      throw new Error(`Failed to add chat message: ${errorMessage}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Retrieves the chat history for a specific session.\r\n     * @param sessionId Identifier for the conversation session.\r\n     * @param limit Optional limit on the number of messages to retrieve (most recent).\r\n     * @returns A ChatMessageHistory object populated with the messages.\r\n     */\r\n  public async getChatHistory(sessionId: string, limit?: number): Promise<ChatMessageHistory> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n\r\n    if (!sessionId) throw new Error('sessionId cannot be empty.');\r\n\r\n    const effectiveLimit = limit ?? getConfig<number>('memory.conversationHistorySize', 100);\r\n\r\n    try {\r\n      logger.debug(`Getting chat history for session ${sessionId} (limit: ${effectiveLimit})...`);\r\n      if (this.database) {\r\n        const records = await this.database.queryRecords(\r\n          CHAT_HISTORY_COLLECTION,\r\n          { sessionId: sessionId }, // Filter by session ID\r\n          effectiveLimit,\r\n          { timestamp: -1 } // Sort by timestamp descending (most recent first)\r\n        );\r\n\r\n        // Convert stored records back to LangChain BaseMessage objects\r\n        const messages: BaseMessage[] = (records as unknown as ChatMessage[])\r\n          .reverse() // Reverse to get chronological order (oldest first)\r\n          .map(record => {\r\n            const content = record.content; // Assume content is stored as string\r\n            // We could use metadata for additional kwargs if needed\r\n            if (record.type === 'human') {\r\n              return new HumanMessage(content);\r\n            } else if (record.type === 'ai') {\r\n              return new AIMessage(content);\r\n            } else {\r\n              // Handle system messages or unknown types if necessary\r\n              logger.warn(`Unknown chat message type \"${record.type}\" encountered for session ${sessionId}.`);\r\n              // Could potentially return a SystemMessage or skip\r\n              return new SystemMessage(`[Unknown Type: ${record.type}] ${content}`);\r\n            }\r\n          });\r\n\r\n        logger.debug(`Retrieved ${messages.length} messages for session ${sessionId}.`);\r\n        // Create a new history with the messages\r\n        const history = new ChatMessageHistory();\r\n        // Add messages to history\r\n        for (const message of messages) {\r\n          await history.addMessage(message);\r\n        }\r\n        return history;\r\n      }\r\n      return new ChatMessageHistory();\r\n    } catch (error) {\r\n      logger.error(`Failed to get chat history for session ${sessionId}:`, error);\r\n      // Return empty history on error\r\n      return new ChatMessageHistory();\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Clears the chat history for a specific session.\r\n     * @param sessionId Identifier for the conversation session.\r\n     */\r\n  public async clearChatHistory(sessionId: string): Promise<void> {\r\n    if (this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n    await this.assertInitialized();\r\n    if (!sessionId) throw new Error('sessionId cannot be empty.');\r\n    logger.warn(`Clearing chat history for session ${sessionId}...`);\r\n    try {\r\n      // Use database's deleteRecords if available, otherwise fall back to individual deletes\r\n      if (this.database?.deleteRecords) {\r\n        const deletedCount = await this.database.deleteRecords(CHAT_HISTORY_COLLECTION, { sessionId });\r\n        logger.info(`Cleared ${deletedCount} chat messages for session ${sessionId}.`);\r\n      } else if (this.database) {\r\n        // Fallback: Get all records and delete them individually\r\n        const records = await this.database.queryRecords(CHAT_HISTORY_COLLECTION, { sessionId });\r\n        let deletedCount = 0;\r\n        for (const record of records) {\r\n          if (await this.database.deleteRecord(CHAT_HISTORY_COLLECTION, (record as { id: string }).id)) {\r\n            deletedCount++;\r\n          }\r\n        }\r\n        logger.info(`Cleared ${deletedCount} chat messages for session ${sessionId} (individual deletes).`);\r\n      }\r\n      // Optionally fire event\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      logger.error(`Failed to clear chat history for session ${sessionId}:`, error);\r\n      throw new Error(`Failed to clear chat history: ${errorMessage}`);\r\n    }\r\n  }\r\n\r\n  // --- LangChain Memory Component Factory Methods (Optional Conveniences) ---\r\n\r\n  /**\r\n     * Creates a BufferMemory instance pre-populated with history for a session.\r\n     * @param sessionId The conversation session ID.\r\n     * @param k The number of last messages to keep in the buffer (optional).\r\n     * @param memoryKey The key to use for the memory variables (default: \"history\").\r\n     * @returns A Promise resolving to a BufferMemory instance.\r\n     */\r\n  public async getBufferedMemory(sessionId: string, k?: number, memoryKey = 'history'): Promise<BufferMemory> {\r\n    const chatHistory = await this.getChatHistory(sessionId);\r\n    return new BufferMemory({\r\n      chatHistory: chatHistory,\r\n      memoryKey: memoryKey,\r\n      k: k ?? getConfig<number>('memory.conversationHistorySize', 100), // Use configured size if k not provided\r\n      returnMessages: true, // Typically want BaseMessage objects back\r\n    });\r\n  }\r\n\r\n  /**\r\n     * Creates a ConversationSummaryMemory instance for a session.\r\n     * Requires a BaseLanguageModel instance for summarization.\r\n     * @param sessionId The conversation session ID.\r\n     * @param llm The language model instance to use for summarization.\r\n     * @param memoryKey The key to use for the memory variables (default: \"history\").\r\n     * @returns A Promise resolving to a ConversationSummaryMemory instance.\r\n     */\r\n  public async getSummarizedMemory(sessionId: string, llm: BaseLanguageModel, memoryKey = 'history'): Promise<ConversationSummaryMemory> {\r\n    const chatHistory = await this.getChatHistory(sessionId);\r\n    return new ConversationSummaryMemory({\r\n      llm: llm,\r\n      chatHistory: chatHistory,\r\n      memoryKey: memoryKey,\r\n      returnMessages: true,\r\n    });\r\n  }\r\n\r\n  /**\r\n     * Creates a VectorStoreRetrieverMemory instance using this provider's vector store.\r\n     * Note: This is more about configuring memory *for use in a chain* rather than\r\n     * just retrieving data. The chain would typically instantiate this.\r\n     * @param memoryKey The key for memory variables.\r\n     * @param inputKey The key for the input variable to the chain.\r\n     * @returns A VectorStoreRetrieverMemory instance.\r\n     */\r\n  public async getVectorStoreRetrieverMemory(memoryKey = 'history', inputKey?: string): Promise<VectorStoreRetrieverMemory> {\r\n    await this.assertInitialized();\r\n    if (!this.vectorStore) {\r\n      throw new Error('Vector store is not initialized.');\r\n    }\r\n\r\n    if (!(this.vectorStore instanceof MemoryVectorStore) && !this.vectorStore.asRetriever) {\r\n      throw new Error('The configured vector store cannot be used directly as a retriever for VectorStoreRetrieverMemory. Use searchSimilarMemories instead or ensure the vector store implements \\'asRetriever\\'.');\r\n    }\r\n\r\n    // The vector store needs an `asRetriever` method or be compatible.\r\n    // MemoryVectorStore has this. Others might need wrapping.\r\n    if (!this.vectorStore.asRetriever) {\r\n      throw new Error('Vector store does not implement asRetriever method.');\r\n    }\r\n\r\n    const retriever = this.vectorStore.asRetriever(getConfig<number>('memory.contextWindowSize', 5));\r\n\r\n    return new VectorStoreRetrieverMemory({\r\n      vectorStoreRetriever: retriever,\r\n      memoryKey: memoryKey,\r\n      inputKey: inputKey, // Optional input key\r\n    });\r\n  }\r\n\r\n\r\n  // --- Settings Management ---\r\n\r\n  /**\r\n   * Retrieves the current memory settings from the configuration.\r\n   */\r\n  public getMemorySettings(): MemorySettings {\r\n    // This structure should match the MemorySettings type defined in ../types\r\n    // It reads directly from the config system.\r\n    // Get the vector store settings\r\n    const vectorStoreType = getConfig<string>('memory.vectorStore', 'chroma');\r\n    const chromaSettings = {\r\n      directory: getConfig<string>('memory.vectorStore.chroma.directory', './.codessa/chroma'),\r\n      collectionName: getConfig<string>('memory.vectorStore.chroma.collectionName', 'codessa_memories')\r\n    };\r\n    const pineconeSettings = {\r\n      apiKey: getConfig<string>('memory.vectorStore.pinecone.apiKey', ''),\r\n      environment: getConfig<string>('memory.vectorStore.pinecone.environment', ''),\r\n      indexName: getConfig<string>('memory.vectorStore.pinecone.indexName', 'codessa-memories')\r\n    };\r\n\r\n    // Get the database settings\r\n    const databaseType = getConfig<string>('memory.database', 'sqlite');\r\n    const sqliteSettings = {\r\n      filename: getConfig<string>('memory.database.sqlite.filename', './.codessa/memory.db')\r\n    };\r\n    const mysqlSettings = {\r\n      host: getConfig<string>('memory.database.mysql.host', 'localhost'),\r\n      port: getConfig<number>('memory.database.mysql.port', 3306),\r\n      user: getConfig<string>('memory.database.mysql.user', 'root'),\r\n      password: getConfig<string>('memory.database.mysql.password', ''),\r\n      database: getConfig<string>('memory.database.mysql.database', 'codessa'),\r\n      table: getConfig<string>('memory.database.mysql.table', 'memories')\r\n    };\r\n    const postgresSettings = {\r\n      connectionString: getConfig<string>('memory.database.postgres.connectionString', ''),\r\n      schema: getConfig<string>('memory.database.postgres.schema', 'public')\r\n    };\r\n    const mongodbSettings = {\r\n      connectionString: getConfig<string>('memory.database.mongodb.connectionString', ''),\r\n      database: getConfig<string>('memory.database.mongodb.database', 'codessa'),\r\n      collection: getConfig<string>('memory.database.mongodb.collection', 'memories')\r\n    };\r\n    const redisSettings = {\r\n      url: getConfig<string>('memory.database.redis.url', ''),\r\n      keyPrefix: getConfig<string>('memory.database.redis.keyPrefix', 'codessa:')\r\n    };\r\n\r\n    // Get the file chunking settings\r\n    const fileChunkingSettings = {\r\n      chunkSize: getConfig<number>('memory.fileChunking.chunkSize', 1000),\r\n      chunkOverlap: getConfig<number>('memory.fileChunking.chunkOverlap', 200),\r\n      maxChunksPerFile: getConfig<number>('memory.fileChunking.maxChunksPerFile', 100)\r\n    };\r\n\r\n    return {\r\n      enabled: getConfig<boolean>('memory.enabled', true),\r\n      system: getConfig<'basic' | 'codessa' | 'quantum'>('memory.system', 'codessa'),\r\n      maxMemories: getConfig<number>('memory.maxMemories', 1000),\r\n      maxMemoriesPerFile: getConfig<number>('phase4.maxMemoriesPerFile', 100),\r\n      memoryRetentionDays: getConfig<number>('phase4.memoryRetentionDays', 30),\r\n      relevanceThreshold: getConfig<number>('memory.relevanceThreshold', 0.7),\r\n      contextWindowSize: getConfig<number>('memory.contextWindowSize', 5),\r\n      conversationHistorySize: getConfig<number>('memory.conversationHistorySize', 100),\r\n      enableQuantumMemory: getConfig<boolean>('phase4.enableQuantumMemory', true),\r\n      enablePredictiveInsights: getConfig<boolean>('phase4.enablePredictiveInsights', true),\r\n      enableMemoryVisualization: getConfig<boolean>('phase4.enableMemoryVisualization', true),\r\n      enableCollaborativeMemory: getConfig<boolean>('phase4.enableCollaborativeMemory', true),\r\n      vectorStore: vectorStoreType as 'memory' | 'chroma' | 'pinecone' | 'weaviate' | 'hnswlib',\r\n      vectorStoreSettings: {\r\n        chroma: chromaSettings,\r\n        pinecone: pineconeSettings\r\n      },\r\n      database: databaseType as 'sqlite' | 'mysql' | 'postgres' | 'mongodb' | 'redis',\r\n      databaseSettings: {\r\n        sqlite: sqliteSettings,\r\n        mysql: mysqlSettings,\r\n        postgres: postgresSettings,\r\n        mongodb: mongodbSettings,\r\n        redis: redisSettings\r\n      },\r\n      fileChunking: fileChunkingSettings,\r\n      cacheSize: getConfig<number>('memory.cacheSize', 1000),\r\n      cacheExpiry: getConfig<number>('memory.cacheExpiry', 300000),\r\n      batchSize: getConfig<number>('memory.batchSize', 50),\r\n      maxConcurrentOperations: getConfig<number>('memory.maxConcurrentOperations', 10)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Updates specific memory settings in the configuration.\r\n   * Handles potential re-initialization of components if critical settings change.\r\n   * @param settings A partial MemorySettings object with values to update.\r\n   */\r\n  public async updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean> {\r\n    logger.info('Updating memory settings:', settings);\r\n    const currentSettings = this.getMemorySettings();\r\n    let reinitializeVectorStore = false;\r\n    let reinitializeDatabase = false;\r\n\r\n    try {\r\n      const updates: Promise<boolean>[] = [];\r\n\r\n      // Helper to update config and track success\r\n      const update = (key: string, value: unknown): void => {\r\n        if (value !== undefined) {\r\n          updates.push(setConfig(key, value));\r\n        }\r\n      };\r\n\r\n      // Update individual settings\r\n      update('memory.enabled', settings.enabled);\r\n      update('memory.maxMemories', settings.maxMemories);\r\n      update('memory.relevanceThreshold', settings.relevanceThreshold);\r\n      update('memory.contextWindowSize', settings.contextWindowSize);\r\n      update('memory.conversationHistorySize', settings.conversationHistorySize);\r\n\r\n      // Check for changes requiring re-initialization\r\n      if (settings.vectorStore !== undefined && settings.vectorStore !== currentSettings.vectorStore) {\r\n        update('memory.vectorStore', settings.vectorStore);\r\n        reinitializeVectorStore = true;\r\n      }\r\n      if (settings.database !== undefined && settings.database !== currentSettings.database) {\r\n        update('memory.database', settings.database);\r\n        reinitializeDatabase = true;\r\n      }\r\n\r\n      // Update nested settings (handle potential partial updates)\r\n      if (settings.vectorStoreSettings) {\r\n        const currentVSettings = getConfig<Record<string, unknown>>('memory.vectorStore', {});\r\n        if (currentVSettings.chroma && settings.vectorStoreSettings.chroma) {\r\n          update('memory.vectorStore.chroma.directory', settings.vectorStoreSettings.chroma.directory);\r\n          update('memory.vectorStore.chroma.collectionName', settings.vectorStoreSettings.chroma.collectionName);\r\n          if (settings.vectorStoreSettings.chroma.directory !== (currentVSettings.chroma as Record<string, unknown>).directory) {\r\n            reinitializeVectorStore = true;\r\n          }\r\n        }\r\n      }\r\n      if (settings.databaseSettings) {\r\n        const currentDbSettings = getConfig<Record<string, unknown>>('memory.database', {});\r\n        if (currentDbSettings.sqlite && settings.databaseSettings.sqlite) {\r\n          update('memory.database.sqlite.filename', settings.databaseSettings.sqlite.filename);\r\n          if (settings.databaseSettings.sqlite.filename !== (currentDbSettings.sqlite as Record<string, unknown>).filename) {\r\n            reinitializeDatabase = true;\r\n          }\r\n        }\r\n      }\r\n      if (settings.fileChunking) {\r\n        update('memory.fileChunking.chunkSize', settings.fileChunking.chunkSize);\r\n        update('memory.fileChunking.chunkOverlap', settings.fileChunking.chunkOverlap);\r\n        update('memory.fileChunking.maxChunksPerFile', settings.fileChunking.maxChunksPerFile);\r\n      }\r\n\r\n      // Wait for all config updates to complete\r\n      const results = await Promise.all(updates);\r\n      if (results.some(result => !result)) {\r\n        throw new Error('One or more configuration settings failed to update.');\r\n      }\r\n\r\n      // Perform re-initialization if needed\r\n      if (reinitializeVectorStore || reinitializeDatabase) {\r\n        logger.info('Reinitializing components due to critical setting changes');\r\n        try {\r\n          if (!this.context) {\r\n            throw new Error('Extension context is not available');\r\n          }\r\n          await this.initialize(this.context);\r\n          logger.info('Memory settings updated successfully');\r\n          return true;\r\n        } catch (error) {\r\n          logger.error('Error reinitializing components:', error);\r\n          return false;\r\n        }\r\n      }\r\n\r\n      logger.info('Memory settings updated successfully');\r\n      return true;\r\n    } catch (error) {\r\n      logger.error('Error updating memory settings:', error);\r\n      return false;\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const codessaMemoryProvider = new CodessaMemoryProvider();\r\n"]}