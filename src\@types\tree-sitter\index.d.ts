// Type definitions for tree-sitter
// Project: https://github.com/tree-sitter/tree-sitter
// Definitions by: <PERSON> <https://github.com/maxbrunsfeld>

declare module 'tree-sitter' {
  export class Parser {
    parse(input: string | Parser.Input, previousTree?: Tree, options?: Parser.Options): Tree;
    getLanguage(): any;
    getLogger(): Logger;
    setLanguage(language: any): void;
    setLogger(logger: Logger | boolean): void;
  }

  export interface Tree {
    rootNode: SyntaxNode;
    getEditedRange(): Range;
    getEditedRange(previousTree: Tree): Range;
    getChangedRanges(previousTree: Tree): Range[];
    walk(): TreeCursor;
  }

  export interface SyntaxNode {
    type: string;
    typeId: number;
    text: string;
    startPosition: Point;
    endPosition: Point;
    startIndex: number;
    endIndex: number;
    parent: SyntaxNode | null;
    children: SyntaxNode[];
    namedChildren: SyntaxNode[];
    childCount: number;
    namedChildCount: number;
    firstChild: SyntaxNode | null;
    lastChild: SyntaxNode | null;
    firstNamedChild: SyntaxNode | null;
    lastNamedChild: SyntaxNode | null;
    nextSibling: SyntaxNode | null;
    nextNamedSibling: SyntaxNode | null;
    previousSibling: SyntaxNode | null;
    previousNamedSibling: SyntaxNode | null;
    hasError: boolean;
    isMissing: boolean;
    hasChanges: boolean;
    hasDifference: boolean;
    toString(): string;
    child(index: number): SyntaxNode | null;
    namedChild(index: number): SyntaxNode | null;
    firstChildForIndex(index: number): SyntaxNode | null;
    firstNamedChildForIndex(index: number): SyntaxNode | null;
    descendantForIndex(index: number): SyntaxNode;
    descendantForPosition(position: Point): SyntaxNode;
    namedDescendantForPosition(position: Point): SyntaxNode;
    descendantsOfType(types: string | string[], startPosition?: Point, endPosition?: Point): SyntaxNode[];
  }

  export interface TreeCursor {
    nodeType: string;
    nodeText: string;
    nodeIsNamed: boolean;
    startPosition: Point;
    endPosition: Point;
    startIndex: number;
    endIndex: number;
    currentNode: SyntaxNode;
    currentFieldName: string | null;
    gotoParent(): boolean;
    gotoFirstChild(): boolean;
    gotoFirstChildForIndex(index: number): boolean | null;
    gotoNextSibling(): boolean;
  }

  export interface Point {
    row: number;
    column: number;
  }

  export interface Range {
    startPosition: Point;
    endPosition: Point;
    startIndex: number;
    endIndex: number;
  }

  export interface Logger {
    log(message: string, params: any): void;
  }

  export namespace Parser {
    interface Options {
      includedRanges?: Range[];
    }

    interface Input {
      seek(index: number): void;
      read(): number | null;
    }
  }

  export default Parser;
}
