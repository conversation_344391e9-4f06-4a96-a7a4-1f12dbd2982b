{"name": "codessa", "displayName": "<PERSON><PERSON> - The goddess of code", "description": "Revolutionary AI-powered coding assistant with multi-agent collaboration, advanced workflows, and a personality engine.", "version": "1.0.0", "publisher": "teknerds-its", "icon": "images/icon.png", "repository": {"type": "git", "url": "https://github.com/djlaserman/Codessa"}, "engines": {"vscode": "^1.85.0"}, "categories": ["Programming Languages", "Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"configuration": {"title": "Codessa", "properties": {"codessa.terminal.viewType": {"type": "string", "enum": ["sideView", "defaultTerminal", "both"], "default": "both", "description": "Determines where the terminal output is displayed: in the side view, default terminal, or both."}, "codessa.terminal.showTerminal": {"type": "boolean", "default": true, "description": "Whether to show the terminal output in the selected view(s)."}, "codessa.terminal.fontFamily": {"type": "string", "default": "'Courier New', <PERSON><PERSON><PERSON>, 'Droid Sans Mono', 'Fira Code', 'Inconsolata', 'Source Code Pro', 'Ubuntu Mono', monospace", "description": "Font family for the terminal. Separate multiple fonts with commas for fallback.", "markdownDescription": "Font family for the terminal. Separate multiple fonts with commas for fallback. Example: `'Fira Code', 'Courier New', monospace`"}, "codessa.terminal.fontSize": {"type": "number", "default": 14, "minimum": 8, "maximum": 72, "description": "Font size in pixels for the terminal."}, "codessa.terminal.fontWeight": {"type": "string", "enum": ["normal", "bold", "100", "200", "300", "400", "500", "600", "700", "800", "900"], "default": "normal", "description": "Font weight for the terminal text."}, "codessa.terminal.foregroundColor": {"type": "string", "default": "", "description": "Custom foreground color for the terminal text. Leave empty to use theme default.", "format": "color-hex"}, "codessa.terminal.backgroundColor": {"type": "string", "default": "", "description": "Custom background color for the terminal. Leave empty to use theme default.", "format": "color-hex"}, "codessa.terminal.cursorColor": {"type": "string", "default": "", "description": "Custom color for the terminal cursor. Leave empty to use theme default.", "format": "color-hex"}, "codessa.terminal.selectionColor": {"type": "string", "default": "", "description": "Custom color for text selection in the terminal. Leave empty to use theme default.", "format": "color-hex"}, "codessa.terminal.theme": {"type": "string", "enum": ["system", "light", "dark", "highContrast"], "default": "system", "description": "Color theme for the terminal. 'system' will follow VS Code's theme."}, "codessa.terminal.customTheme": {"type": "object", "default": {}, "description": "Advanced: Custom theme colors for the terminal. Overrides other color settings.", "properties": {"foreground": {"type": "string", "format": "color-hex"}, "background": {"type": "string", "format": "color-hex"}, "cursor": {"type": "string", "format": "color-hex"}, "selection": {"type": "string", "format": "color-hex"}, "black": {"type": "string", "format": "color-hex"}, "red": {"type": "string", "format": "color-hex"}, "green": {"type": "string", "format": "color-hex"}, "yellow": {"type": "string", "format": "color-hex"}, "blue": {"type": "string", "format": "color-hex"}, "magenta": {"type": "string", "format": "color-hex"}, "cyan": {"type": "string", "format": "color-hex"}, "white": {"type": "string", "format": "color-hex"}, "brightBlack": {"type": "string", "format": "color-hex"}, "brightRed": {"type": "string", "format": "color-hex"}, "brightGreen": {"type": "string", "format": "color-hex"}, "brightYellow": {"type": "string", "format": "color-hex"}, "brightBlue": {"type": "string", "format": "color-hex"}, "brightMagenta": {"type": "string", "format": "color-hex"}, "brightCyan": {"type": "string", "format": "color-hex"}, "brightWhite": {"type": "string", "format": "color-hex"}}}}}, "commands": [{"command": "codessa.helloWorld", "title": "Hello World"}, {"command": "codessa.addAgent", "title": "Add Codessa Agent"}, {"command": "codessa.openAgentPanel", "title": "Open Agent Panel"}, {"command": "codessa.openToolDetails", "title": "Open Tool Details"}], "viewsContainers": {"activitybar": [{"id": "codessa-sidebar", "title": "Codessa", "icon": "images/icon.svg"}, {"id": "codessa-terminal", "title": "Codessa Terminal", "icon": "resources/terminal-icon.svg"}]}, "views": {"codessa-terminal": [{"id": "codessa.terminalView", "name": "Terminal", "contextualTitle": "Codessa Terminal"}], "codessa-sidebar": [{"id": "codessaAgentView", "name": "Agents", "icon": "$(account)"}, {"id": "codessaToolsView", "name": "Tools", "icon": "$(tools)"}, {"id": "codessa.chatView", "name": "Cha<PERSON>", "icon": "$(comment)"}]}}, "scripts": {"vscode:prepublish": "node -e \"console.log('skip prepublish')\"", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "benchmark": "npm run benchmark:workflow", "benchmark:workflow": "cross-env TS_NODE_PROJECT=./tsconfig.benchmark.json ts-node --files test/benchmark/workflowBenchmark.ts", "benchmark:simple": "cross-env COMPLEXITY=simple npm run benchmark:workflow", "benchmark:medium": "cross-env COMPLEXITY=medium npm run benchmark:workflow", "benchmark:complex": "cross-env COMPLEXITY=complex npm run benchmark:workflow", "benchmark:all": "npm run benchmark:simple && npm run benchmark:medium && npm run benchmark:complex", "profile:benchmark": "0x --node-arg=--inspect-brk -- node --loader ts-node/esm test/benchmark/workflowBenchmark.ts"}, "dependencies": {"@anthropic-ai/sdk": "0.57.0", "@mistralai/mistralai": "1.7.5", "@napi-rs/cli": "^2.16.2", "@pinecone-database/pinecone": "6.1.2", "axios": "1.11.0", "clipboardy": "4.0.0", "diff": "8.0.2", "mongodb": "6.18.0", "mysql2": "3.14.2", "openai": "5.10.2", "pg": "8.16.3", "redis": "5.6.1", "sharp": "0.34.3", "sqlite": "5.1.1", "sqlite3": "5.1.7", "tree-sitter-typescript": "0.23.2", "uuid": "11.1.0", "zod": "^3.22.4", "zod-to-json-schema": "3.24.6"}, "devDependencies": {"@eslint/js": "^9.0.0", "@types/clipboardy": "1.1.0", "@types/mocha": "^10.0.5", "@types/node": "20.19.9", "@types/prettier": "2.7.3", "@types/vscode": "^1.85.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.3.9", "0x": "4.1.4", "cross-env": "7.0.3", "eslint": "8.56.0", "glob": "^10.4.0", "ts-node": "10.9.2", "tslib": "2.8.1", "typescript": "5.8.3", "v8-profiler-next": "1.10.0"}}