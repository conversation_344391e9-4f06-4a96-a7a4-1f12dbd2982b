{"_type": "Project", "_id": "AAAAAAFF+h6SjaM2Hec=", "name": "Untitled", "ownedElements": [{"_type": "UMLModel", "_id": "AAAAAAFF+qBWK6M3Z8Y=", "_parent": {"$ref": "AAAAAAFF+h6SjaM2Hec="}, "name": "Model", "ownedElements": [{"_type": "UMLClassDiagram", "_id": "AAAAAAFF+qBtyKM79qY=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "Main", "defaultDiagram": true, "ownedViews": [{"_type": "UMLClassView", "_id": "AAAAAAGWhli6YGIu9bg=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAGWhli6YGIvpl0=", "_parent": {"$ref": "AAAAAAGWhli6YGIu9bg="}, "model": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAGWhli6YGIwFRI=", "_parent": {"$ref": "AAAAAAGWhli6YGIvpl0="}, "font": "<PERSON><PERSON>;13;0", "left": 325, "top": 53, "width": 300.96142578125, "height": 13, "text": "«factory»"}, {"_type": "LabelView", "_id": "AAAAAAGWhli6YGIx7pI=", "_parent": {"$ref": "AAAAAAGWhli6YGIvpl0="}, "font": "<PERSON><PERSON>;13;1", "left": 325, "top": 68, "width": 300.96142578125, "height": 13, "text": "DatabaseFactory"}, {"_type": "LabelView", "_id": "AAAAAAGWhli6YGIyWLI=", "_parent": {"$ref": "AAAAAAGWhli6YGIvpl0="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 384, "top": -384, "width": 73.67724609375, "height": 13, "text": "(from Model)"}, {"_type": "LabelView", "_id": "AAAAAAGWhli6YGIzVVM=", "_parent": {"$ref": "AAAAAAGWhli6YGIvpl0="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 384, "top": -384, "height": 13, "horizontalAlignment": 1}], "font": "<PERSON><PERSON>;13;0", "left": 320, "top": 48, "width": 310.96142578125, "height": 38, "stereotypeLabel": {"$ref": "AAAAAAGWhli6YGIwFRI="}, "nameLabel": {"$ref": "AAAAAAGWhli6YGIx7pI="}, "namespaceLabel": {"$ref": "AAAAAAGWhli6YGIyWLI="}, "propertyLabel": {"$ref": "AAAAAAGWhli6YGIzVVM="}}, {"_type": "UMLAttributeCompartmentView", "_id": "AAAAAAGWhli6YGI0FPw=", "_parent": {"$ref": "AAAAAAGWhli6YGIu9bg="}, "model": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "font": "<PERSON><PERSON>;13;0", "left": 320, "top": 86, "width": 310.96142578125, "height": 10}, {"_type": "UMLOperationCompartmentView", "_id": "AAAAAAGWhli6YGI1rr4=", "_parent": {"$ref": "AAAAAAGWhli6YGIu9bg="}, "model": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "subViews": [{"_type": "UMLOperationView", "_id": "AAAAAAGWhln3iWJavIU=", "_parent": {"$ref": "AAAAAAGWhli6YGI1rr4="}, "model": {"$ref": "AAAAAAGWhln3hGJXBtM="}, "font": "<PERSON><PERSON>;13;0", "left": 325, "top": 101, "width": 300.96142578125, "height": 13, "text": "+createDatabase(type: string): Promise<IDatabase>", "horizontalAlignment": 0}], "font": "<PERSON><PERSON>;13;0", "left": 320, "top": 96, "width": 310.96142578125, "height": 23}, {"_type": "UMLReceptionCompartmentView", "_id": "AAAAAAGWhli6YGI2zAU=", "_parent": {"$ref": "AAAAAAGWhli6YGIu9bg="}, "model": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 192, "top": -192, "width": 10, "height": 10}, {"_type": "UMLTemplateParameterCompartmentView", "_id": "AAAAAAGWhli6YGI3v4o=", "_parent": {"$ref": "AAAAAAGWhli6YGIu9bg="}, "model": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 192, "top": -192, "width": 10, "height": 10}], "font": "<PERSON><PERSON>;13;0", "containerChangeable": true, "left": 320, "top": 48, "width": 310.96142578125, "height": 71, "showProperty": false, "nameCompartment": {"$ref": "AAAAAAGWhli6YGIvpl0="}, "attributeCompartment": {"$ref": "AAAAAAGWhli6YGI0FPw="}, "operationCompartment": {"$ref": "AAAAAAGWhli6YGI1rr4="}, "receptionCompartment": {"$ref": "AAAAAAGWhli6YGI2zAU="}, "templateParameterCompartment": {"$ref": "AAAAAAGWhli6YGI3v4o="}}, {"_type": "UMLClassView", "_id": "AAAAAAGWhmFLyWKZ4yo=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAGWhmFLyWKa2gM=", "_parent": {"$ref": "AAAAAAGWhmFLyWKZ4yo="}, "model": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAGWhmFLyWKbu/o=", "_parent": {"$ref": "AAAAAAGWhmFLyWKa2gM="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1392, "top": -672, "height": 13}, {"_type": "LabelView", "_id": "AAAAAAGWhmFLyWKcHHc=", "_parent": {"$ref": "AAAAAAGWhmFLyWKa2gM="}, "font": "<PERSON><PERSON>;13;1", "left": 949, "top": 167, "width": 99.70263671875, "height": 13, "text": "SQLiteDatabase"}, {"_type": "LabelView", "_id": "AAAAAAGWhmFLyWKdVl8=", "_parent": {"$ref": "AAAAAAGWhmFLyWKa2gM="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1392, "top": -672, "width": 73.67724609375, "height": 13, "text": "(from Model)"}, {"_type": "LabelView", "_id": "AAAAAAGWhmFLyWKeWzQ=", "_parent": {"$ref": "AAAAAAGWhmFLyWKa2gM="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1392, "top": -672, "height": 13, "horizontalAlignment": 1}], "font": "<PERSON><PERSON>;13;0", "left": 944, "top": 160, "width": 109.70263671875, "height": 25, "stereotypeLabel": {"$ref": "AAAAAAGWhmFLyWKbu/o="}, "nameLabel": {"$ref": "AAAAAAGWhmFLyWKcHHc="}, "namespaceLabel": {"$ref": "AAAAAAGWhmFLyWKdVl8="}, "propertyLabel": {"$ref": "AAAAAAGWhmFLyWKeWzQ="}}, {"_type": "UMLAttributeCompartmentView", "_id": "AAAAAAGWhmFLyWKf3d4=", "_parent": {"$ref": "AAAAAAGWhmFLyWKZ4yo="}, "model": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "font": "<PERSON><PERSON>;13;0", "left": 944, "top": 185, "width": 109.70263671875, "height": 10}, {"_type": "UMLOperationCompartmentView", "_id": "AAAAAAGWhmFLyWKgZ7Q=", "_parent": {"$ref": "AAAAAAGWhmFLyWKZ4yo="}, "model": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "font": "<PERSON><PERSON>;13;0", "left": 944, "top": 195, "width": 109.70263671875, "height": 10}, {"_type": "UMLReceptionCompartmentView", "_id": "AAAAAAGWhmFLyWKhQu4=", "_parent": {"$ref": "AAAAAAGWhmFLyWKZ4yo="}, "model": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 696, "top": -336, "width": 10, "height": 10}, {"_type": "UMLTemplateParameterCompartmentView", "_id": "AAAAAAGWhmFLyWKiosQ=", "_parent": {"$ref": "AAAAAAGWhmFLyWKZ4yo="}, "model": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 696, "top": -336, "width": 10, "height": 10}], "font": "<PERSON><PERSON>;13;0", "containerChangeable": true, "left": 944, "top": 160, "width": 109.70263671875, "height": 45, "showProperty": false, "nameCompartment": {"$ref": "AAAAAAGWhmFLyWKa2gM="}, "attributeCompartment": {"$ref": "AAAAAAGWhmFLyWKf3d4="}, "operationCompartment": {"$ref": "AAAAAAGWhmFLyWKgZ7Q="}, "receptionCompartment": {"$ref": "AAAAAAGWhmFLyWKhQu4="}, "templateParameterCompartment": {"$ref": "AAAAAAGWhmFLyWKiosQ="}}, {"_type": "UMLClassView", "_id": "AAAAAAGWhmGb4WLEvLs=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAGWhmGb4WLFZHs=", "_parent": {"$ref": "AAAAAAGWhmGb4WLEvLs="}, "model": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAGWhmGb4WLGHi4=", "_parent": {"$ref": "AAAAAAGWhmGb4WLFZHs="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 784, "top": -336, "height": 13}, {"_type": "LabelView", "_id": "AAAAAAGWhmGb4WLHEOE=", "_parent": {"$ref": "AAAAAAGWhmGb4WLFZHs="}, "font": "<PERSON><PERSON>;13;1", "left": 653, "top": 311, "width": 102.5908203125, "height": 13, "text": "MySQLDatabase"}, {"_type": "LabelView", "_id": "AAAAAAGWhmGb4WLIygA=", "_parent": {"$ref": "AAAAAAGWhmGb4WLFZHs="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 784, "top": -336, "width": 73.67724609375, "height": 13, "text": "(from Model)"}, {"_type": "LabelView", "_id": "AAAAAAGWhmGb4WLJv5U=", "_parent": {"$ref": "AAAAAAGWhmGb4WLFZHs="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 784, "top": -336, "height": 13, "horizontalAlignment": 1}], "font": "<PERSON><PERSON>;13;0", "left": 648, "top": 304, "width": 112.5908203125, "height": 25, "stereotypeLabel": {"$ref": "AAAAAAGWhmGb4WLGHi4="}, "nameLabel": {"$ref": "AAAAAAGWhmGb4WLHEOE="}, "namespaceLabel": {"$ref": "AAAAAAGWhmGb4WLIygA="}, "propertyLabel": {"$ref": "AAAAAAGWhmGb4WLJv5U="}}, {"_type": "UMLAttributeCompartmentView", "_id": "AAAAAAGWhmGb4WLK3W0=", "_parent": {"$ref": "AAAAAAGWhmGb4WLEvLs="}, "model": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "font": "<PERSON><PERSON>;13;0", "left": 648, "top": 329, "width": 112.5908203125, "height": 10}, {"_type": "UMLOperationCompartmentView", "_id": "AAAAAAGWhmGb4WLLofY=", "_parent": {"$ref": "AAAAAAGWhmGb4WLEvLs="}, "model": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "font": "<PERSON><PERSON>;13;0", "left": 648, "top": 339, "width": 112.5908203125, "height": 10}, {"_type": "UMLReceptionCompartmentView", "_id": "AAAAAAGWhmGb4WLMQLk=", "_parent": {"$ref": "AAAAAAGWhmGb4WLEvLs="}, "model": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 392, "top": -168, "width": 10, "height": 10}, {"_type": "UMLTemplateParameterCompartmentView", "_id": "AAAAAAGWhmGb4WLNF7U=", "_parent": {"$ref": "AAAAAAGWhmGb4WLEvLs="}, "model": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 392, "top": -168, "width": 10, "height": 10}], "font": "<PERSON><PERSON>;13;0", "containerChangeable": true, "left": 648, "top": 304, "width": 112.5908203125, "height": 45, "showProperty": false, "nameCompartment": {"$ref": "AAAAAAGWhmGb4WLFZHs="}, "attributeCompartment": {"$ref": "AAAAAAGWhmGb4WLK3W0="}, "operationCompartment": {"$ref": "AAAAAAGWhmGb4WLLofY="}, "receptionCompartment": {"$ref": "AAAAAAGWhmGb4WLMQLk="}, "templateParameterCompartment": {"$ref": "AAAAAAGWhmGb4WLNF7U="}}, {"_type": "UMLClassView", "_id": "AAAAAAGWhmJL6WLvZRs=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAGWhmJL6WLwktQ=", "_parent": {"$ref": "AAAAAAGWhmJL6WLvZRs="}, "model": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAGWhmJL6WLxl34=", "_parent": {"$ref": "AAAAAAGWhmJL6WLwktQ="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1264, "top": -480, "height": 13}, {"_type": "LabelView", "_id": "AAAAAAGWhmJL6WLyFAk=", "_parent": {"$ref": "AAAAAAGWhmJL6WLwktQ="}, "font": "<PERSON><PERSON>;13;1", "left": 933, "top": 311, "width": 113.43896484375, "height": 13, "text": "PostgresDatabase"}, {"_type": "LabelView", "_id": "AAAAAAGWhmJL6WLzrHY=", "_parent": {"$ref": "AAAAAAGWhmJL6WLwktQ="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1264, "top": -480, "width": 73.67724609375, "height": 13, "text": "(from Model)"}, {"_type": "LabelView", "_id": "AAAAAAGWhmJL6WL0P6M=", "_parent": {"$ref": "AAAAAAGWhmJL6WLwktQ="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1264, "top": -480, "height": 13, "horizontalAlignment": 1}], "font": "<PERSON><PERSON>;13;0", "left": 928, "top": 304, "width": 123.43896484375, "height": 25, "stereotypeLabel": {"$ref": "AAAAAAGWhmJL6WLxl34="}, "nameLabel": {"$ref": "AAAAAAGWhmJL6WLyFAk="}, "namespaceLabel": {"$ref": "AAAAAAGWhmJL6WLzrHY="}, "propertyLabel": {"$ref": "AAAAAAGWhmJL6WL0P6M="}}, {"_type": "UMLAttributeCompartmentView", "_id": "AAAAAAGWhmJL6WL18AU=", "_parent": {"$ref": "AAAAAAGWhmJL6WLvZRs="}, "model": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "font": "<PERSON><PERSON>;13;0", "left": 928, "top": 329, "width": 123.43896484375, "height": 10}, {"_type": "UMLOperationCompartmentView", "_id": "AAAAAAGWhmJL6WL2Eaw=", "_parent": {"$ref": "AAAAAAGWhmJL6WLvZRs="}, "model": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "font": "<PERSON><PERSON>;13;0", "left": 928, "top": 339, "width": 123.43896484375, "height": 10}, {"_type": "UMLReceptionCompartmentView", "_id": "AAAAAAGWhmJL6WL3trg=", "_parent": {"$ref": "AAAAAAGWhmJL6WLvZRs="}, "model": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 632, "top": -240, "width": 10, "height": 10}, {"_type": "UMLTemplateParameterCompartmentView", "_id": "AAAAAAGWhmJL6WL4Khk=", "_parent": {"$ref": "AAAAAAGWhmJL6WLvZRs="}, "model": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 632, "top": -240, "width": 10, "height": 10}], "font": "<PERSON><PERSON>;13;0", "containerChangeable": true, "left": 928, "top": 304, "width": 123.43896484375, "height": 45, "showProperty": false, "nameCompartment": {"$ref": "AAAAAAGWhmJL6WLwktQ="}, "attributeCompartment": {"$ref": "AAAAAAGWhmJL6WL18AU="}, "operationCompartment": {"$ref": "AAAAAAGWhmJL6WL2Eaw="}, "receptionCompartment": {"$ref": "AAAAAAGWhmJL6WL3trg="}, "templateParameterCompartment": {"$ref": "AAAAAAGWhmJL6WL4Khk="}}, {"_type": "UMLClassView", "_id": "AAAAAAGWhmJeMWMZYhk=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAGWhmJeMWMaMVk=", "_parent": {"$ref": "AAAAAAGWhmJeMWMZYhk="}, "model": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAGWhmJeMWMbUE4=", "_parent": {"$ref": "AAAAAAGWhmJeMWMaMVk="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 384, "top": -736, "height": 13}, {"_type": "LabelView", "_id": "AAAAAAGWhmJeMWMcdGo=", "_parent": {"$ref": "AAAAAAGWhmJeMWMaMVk="}, "font": "<PERSON><PERSON>;13;1", "left": 501, "top": 311, "width": 119.17724609375, "height": 13, "text": "MongoDBDatabase"}, {"_type": "LabelView", "_id": "AAAAAAGWhmJeMWMd1DA=", "_parent": {"$ref": "AAAAAAGWhmJeMWMaMVk="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 384, "top": -736, "width": 73.67724609375, "height": 13, "text": "(from Model)"}, {"_type": "LabelView", "_id": "AAAAAAGWhmJeMWMeCdw=", "_parent": {"$ref": "AAAAAAGWhmJeMWMaMVk="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 384, "top": -736, "height": 13, "horizontalAlignment": 1}], "font": "<PERSON><PERSON>;13;0", "left": 496, "top": 304, "width": 129.17724609375, "height": 25, "stereotypeLabel": {"$ref": "AAAAAAGWhmJeMWMbUE4="}, "nameLabel": {"$ref": "AAAAAAGWhmJeMWMcdGo="}, "namespaceLabel": {"$ref": "AAAAAAGWhmJeMWMd1DA="}, "propertyLabel": {"$ref": "AAAAAAGWhmJeMWMeCdw="}}, {"_type": "UMLAttributeCompartmentView", "_id": "AAAAAAGWhmJeMWMfI4w=", "_parent": {"$ref": "AAAAAAGWhmJeMWMZYhk="}, "model": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "font": "<PERSON><PERSON>;13;0", "left": 496, "top": 329, "width": 129.17724609375, "height": 10}, {"_type": "UMLOperationCompartmentView", "_id": "AAAAAAGWhmJeMmMgSdI=", "_parent": {"$ref": "AAAAAAGWhmJeMWMZYhk="}, "model": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "font": "<PERSON><PERSON>;13;0", "left": 496, "top": 339, "width": 129.17724609375, "height": 10}, {"_type": "UMLReceptionCompartmentView", "_id": "AAAAAAGWhmJeMmMhL9A=", "_parent": {"$ref": "AAAAAAGWhmJeMWMZYhk="}, "model": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 192, "top": -368, "width": 10, "height": 10}, {"_type": "UMLTemplateParameterCompartmentView", "_id": "AAAAAAGWhmJeMmMi8Vk=", "_parent": {"$ref": "AAAAAAGWhmJeMWMZYhk="}, "model": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 192, "top": -368, "width": 10, "height": 10}], "font": "<PERSON><PERSON>;13;0", "containerChangeable": true, "left": 496, "top": 304, "width": 129.17724609375, "height": 45, "showProperty": false, "nameCompartment": {"$ref": "AAAAAAGWhmJeMWMaMVk="}, "attributeCompartment": {"$ref": "AAAAAAGWhmJeMWMfI4w="}, "operationCompartment": {"$ref": "AAAAAAGWhmJeMmMgSdI="}, "receptionCompartment": {"$ref": "AAAAAAGWhmJeMmMhL9A="}, "templateParameterCompartment": {"$ref": "AAAAAAGWhmJeMmMi8Vk="}}, {"_type": "UMLClassView", "_id": "AAAAAAGWhmKYnGNFX8g=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAGWhmKYnGNG6UE=", "_parent": {"$ref": "AAAAAAGWhmKYnGNFX8g="}, "model": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAGWhmKYnGNHsUw=", "_parent": {"$ref": "AAAAAAGWhmKYnGNG6UE="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1040, "top": -608, "height": 13}, {"_type": "LabelView", "_id": "AAAAAAGWhmKYnGNIyE4=", "_parent": {"$ref": "AAAAAAGWhmKYnGNG6UE="}, "font": "<PERSON><PERSON>;13;1", "left": 789, "top": 311, "width": 93.208984375, "height": 13, "text": "RedisDatabase"}, {"_type": "LabelView", "_id": "AAAAAAGWhmKYnGNJvyQ=", "_parent": {"$ref": "AAAAAAGWhmKYnGNG6UE="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1040, "top": -608, "width": 73.67724609375, "height": 13, "text": "(from Model)"}, {"_type": "LabelView", "_id": "AAAAAAGWhmKYnGNKr4o=", "_parent": {"$ref": "AAAAAAGWhmKYnGNG6UE="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 1040, "top": -608, "height": 13, "horizontalAlignment": 1}], "font": "<PERSON><PERSON>;13;0", "left": 784, "top": 304, "width": 103.208984375, "height": 25, "stereotypeLabel": {"$ref": "AAAAAAGWhmKYnGNHsUw="}, "nameLabel": {"$ref": "AAAAAAGWhmKYnGNIyE4="}, "namespaceLabel": {"$ref": "AAAAAAGWhmKYnGNJvyQ="}, "propertyLabel": {"$ref": "AAAAAAGWhmKYnGNKr4o="}}, {"_type": "UMLAttributeCompartmentView", "_id": "AAAAAAGWhmKYnGNLT18=", "_parent": {"$ref": "AAAAAAGWhmKYnGNFX8g="}, "model": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "font": "<PERSON><PERSON>;13;0", "left": 784, "top": 329, "width": 103.208984375, "height": 10}, {"_type": "UMLOperationCompartmentView", "_id": "AAAAAAGWhmKYnGNMR0A=", "_parent": {"$ref": "AAAAAAGWhmKYnGNFX8g="}, "model": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "font": "<PERSON><PERSON>;13;0", "left": 784, "top": 339, "width": 103.208984375, "height": 10}, {"_type": "UMLReceptionCompartmentView", "_id": "AAAAAAGWhmKYnGNNnZE=", "_parent": {"$ref": "AAAAAAGWhmKYnGNFX8g="}, "model": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 520, "top": -304, "width": 10, "height": 10}, {"_type": "UMLTemplateParameterCompartmentView", "_id": "AAAAAAGWhmKYnGNOl+c=", "_parent": {"$ref": "AAAAAAGWhmKYnGNFX8g="}, "model": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 520, "top": -304, "width": 10, "height": 10}], "font": "<PERSON><PERSON>;13;0", "containerChangeable": true, "left": 784, "top": 304, "width": 103.208984375, "height": 45, "showProperty": false, "nameCompartment": {"$ref": "AAAAAAGWhmKYnGNG6UE="}, "attributeCompartment": {"$ref": "AAAAAAGWhmKYnGNLT18="}, "operationCompartment": {"$ref": "AAAAAAGWhmKYnGNMR0A="}, "receptionCompartment": {"$ref": "AAAAAAGWhmKYnGNNnZE="}, "templateParameterCompartment": {"$ref": "AAAAAAGWhmKYnGNOl+c="}}, {"_type": "UMLInterfaceView", "_id": "AAAAAAGWjSrJ9hSzzBw=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAGWjSrJ9xS0dqU=", "_parent": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "model": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAGWjSrJ9xS1P4Y=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS0dqU="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 496, "top": -320, "width": 64.32080078125, "height": 13, "text": "«interface»"}, {"_type": "LabelView", "_id": "AAAAAAGWjSrJ9xS278M=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS0dqU="}, "font": "<PERSON><PERSON>;13;1", "left": 805, "top": 94, "width": 61.419921875, "height": 13, "text": "IDatabase"}, {"_type": "LabelView", "_id": "AAAAAAGWjSrJ9xS31ng=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS0dqU="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 496, "top": -320, "width": 73.67724609375, "height": 13, "text": "(from Model)"}, {"_type": "LabelView", "_id": "AAAAAAGWjSrJ9xS4SDE=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS0dqU="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 496, "top": -320, "height": 13, "horizontalAlignment": 1}], "font": "<PERSON><PERSON>;13;0", "left": 800, "top": 87, "width": 71.419921875, "height": 25, "stereotypeLabel": {"$ref": "AAAAAAGWjSrJ9xS1P4Y="}, "nameLabel": {"$ref": "AAAAAAGWjSrJ9xS278M="}, "namespaceLabel": {"$ref": "AAAAAAGWjSrJ9xS31ng="}, "propertyLabel": {"$ref": "AAAAAAGWjSrJ9xS4SDE="}}, {"_type": "UMLAttributeCompartmentView", "_id": "AAAAAAGWjSrJ9xS5ai0=", "_parent": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "model": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 248, "top": -160, "width": 10, "height": 10}, {"_type": "UMLOperationCompartmentView", "_id": "AAAAAAGWjSrJ9xS6pHs=", "_parent": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "model": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "subViews": [{"_type": "UMLOperationView", "_id": "AAAAAAGWjS0ZTRUdehY=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS6pHs="}, "model": {"$ref": "AAAAAAGWjS0ZLRUaCtc="}, "font": "<PERSON><PERSON>;13;0", "left": 253, "top": -155, "width": 80.57080078125, "height": 13, "text": "+Operation1()", "horizontalAlignment": 0}, {"_type": "UMLOperationView", "_id": "AAAAAAGWjS0rjRUjEwI=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS6pHs="}, "model": {"$ref": "AAAAAAGWjS0riBUgPd0="}, "font": "<PERSON><PERSON>;13;0", "left": 253, "top": -140, "width": 80.57080078125, "height": 13, "text": "+Operation2()", "horizontalAlignment": 0}, {"_type": "UMLOperationView", "_id": "AAAAAAGWjS1GABUpPqw=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS6pHs="}, "model": {"$ref": "AAAAAAGWjS1F6xUmbsE="}, "font": "<PERSON><PERSON>;13;0", "left": 253, "top": -125, "width": 80.57080078125, "height": 13, "text": "+Operation3()", "horizontalAlignment": 0}, {"_type": "UMLOperationView", "_id": "AAAAAAGWjS1gFBUvP+M=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS6pHs="}, "model": {"$ref": "AAAAAAGWjS1gDxUsSKQ="}, "font": "<PERSON><PERSON>;13;0", "left": 253, "top": -110, "width": 80.57080078125, "height": 13, "text": "+Operation4()", "horizontalAlignment": 0}, {"_type": "UMLOperationView", "_id": "AAAAAAGWjS17ohU1noY=", "_parent": {"$ref": "AAAAAAGWjSrJ9xS6pHs="}, "model": {"$ref": "AAAAAAGWjS17khUyBKo="}, "font": "<PERSON><PERSON>;13;0", "left": 253, "top": -95, "width": 80.57080078125, "height": 13, "text": "+Operation5()", "horizontalAlignment": 0}], "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 248, "top": -160, "width": 90.57080078125, "height": 83}, {"_type": "UMLReceptionCompartmentView", "_id": "AAAAAAGWjSrJ+BS7vLI=", "_parent": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "model": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 248, "top": -160, "width": 10, "height": 10}, {"_type": "UMLTemplateParameterCompartmentView", "_id": "AAAAAAGWjSrJ+BS8Xm0=", "_parent": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "model": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 248, "top": -160, "width": 10, "height": 10}], "font": "<PERSON><PERSON>;13;0", "containerChangeable": true, "left": 800, "top": 64, "width": 71.419921875, "height": 49, "stereotypeDisplay": "icon", "showProperty": false, "nameCompartment": {"$ref": "AAAAAAGWjSrJ9xS0dqU="}, "suppressAttributes": true, "suppressOperations": true, "attributeCompartment": {"$ref": "AAAAAAGWjSrJ9xS5ai0="}, "operationCompartment": {"$ref": "AAAAAAGWjSrJ9xS6pHs="}, "receptionCompartment": {"$ref": "AAAAAAGWjSrJ+BS7vLI="}, "templateParameterCompartment": {"$ref": "AAAAAAGWjSrJ+BS8Xm0="}}, {"_type": "UMLRealizationView", "_id": "AAAAAAGWjS5/3RVXefU=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWjS5/3RVVXjk="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAGWjS5/3RVYN3c=", "_parent": {"$ref": "AAAAAAGWjS5/3RVXefU="}, "model": {"$ref": "AAAAAAGWjS5/3RVVXjk="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 662, "top": 69, "width": 104.42529296875, "height": 13, "alpha": 1.6415988339861212, "distance": 10.816653826391969, "hostEdge": {"$ref": "AAAAAAGWjS5/3RVXefU="}, "edgePosition": 1, "text": "+<<implements>>"}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjS5/3RVZW/4=", "_parent": {"$ref": "AAAAAAGWjS5/3RVXefU="}, "model": {"$ref": "AAAAAAGWjS5/3RVVXjk="}, "font": "<PERSON><PERSON>;13;0", "left": 664, "top": 56, "width": 111.29345703125, "height": 13, "alpha": 1.4294522673271008, "distance": 24.331050121192877, "hostEdge": {"$ref": "AAAAAAGWjS5/3RVXefU="}, "edgePosition": 1, "text": "«implements»"}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjS5/3hVaM8U=", "_parent": {"$ref": "AAAAAAGWjS5/3RVXefU="}, "model": {"$ref": "AAAAAAGWjS5/3RVVXjk="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 714, "top": 94, "height": 13, "alpha": -1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjS5/3RVXefU="}, "edgePosition": 1}], "font": "<PERSON><PERSON>;13;0", "head": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "tail": {"$ref": "AAAAAAGWhli6YGIu9bg="}, "lineStyle": 1, "points": "631:85;799:88", "showVisibility": true, "showProperty": false, "nameLabel": {"$ref": "AAAAAAGWjS5/3RVYN3c="}, "stereotypeLabel": {"$ref": "AAAAAAGWjS5/3RVZW/4="}, "propertyLabel": {"$ref": "AAAAAAGWjS5/3hVaM8U="}}, {"_type": "UMLGeneralizationView", "_id": "AAAAAAGWjTA2bRVtPHQ=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWjTA2axVrg0E="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAGWjTA2bRVu7gY=", "_parent": {"$ref": "AAAAAAGWjTA2bRVtPHQ="}, "model": {"$ref": "AAAAAAGWjTA2axVrg0E="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 686, "top": 190, "height": 13, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTA2bRVtPHQ="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTA2bRVvu4c=", "_parent": {"$ref": "AAAAAAGWjTA2bRVtPHQ="}, "model": {"$ref": "AAAAAAGWjTA2axVrg0E="}, "visible": null, "font": "<PERSON><PERSON>;13;0", "left": 676, "top": 179, "height": 13, "alpha": 1.5707963267948966, "distance": 30, "hostEdge": {"$ref": "AAAAAAGWjTA2bRVtPHQ="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTA2bRVwakw=", "_parent": {"$ref": "AAAAAAGWjTA2bRVtPHQ="}, "model": {"$ref": "AAAAAAGWjTA2axVrg0E="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 705, "top": 213, "height": 13, "alpha": -1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTA2bRVtPHQ="}, "edgePosition": 1}], "font": "<PERSON><PERSON>;13;0", "head": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "tail": {"$ref": "AAAAAAGWhmJeMWMZYhk="}, "lineStyle": 1, "points": "587:303;806:113", "showVisibility": true, "showProperty": false, "nameLabel": {"$ref": "AAAAAAGWjTA2bRVu7gY="}, "stereotypeLabel": {"$ref": "AAAAAAGWjTA2bRVvu4c="}, "propertyLabel": {"$ref": "AAAAAAGWjTA2bRVwakw="}}, {"_type": "UMLGeneralizationView", "_id": "AAAAAAGWjTCKlhWCq2U=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWjTCKlhWAstw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAGWjTCKlhWDn7w=", "_parent": {"$ref": "AAAAAAGWjTCKlhWCq2U="}, "model": {"$ref": "AAAAAAGWjTCKlhWAstw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 754, "top": 194, "height": 13, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTCKlhWCq2U="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTCKlhWELgI=", "_parent": {"$ref": "AAAAAAGWjTCKlhWCq2U="}, "model": {"$ref": "AAAAAAGWjTCKlhWAstw="}, "visible": null, "font": "<PERSON><PERSON>;13;0", "left": 741, "top": 187, "height": 13, "alpha": 1.5707963267948966, "distance": 30, "hostEdge": {"$ref": "AAAAAAGWjTCKlhWCq2U="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTCKlhWF0cc=", "_parent": {"$ref": "AAAAAAGWjTCKlhWCq2U="}, "model": {"$ref": "AAAAAAGWjTCKlhWAstw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 781, "top": 209, "height": 13, "alpha": -1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTCKlhWCq2U="}, "edgePosition": 1}], "font": "<PERSON><PERSON>;13;0", "head": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "tail": {"$ref": "AAAAAAGWhmGb4WLEvLs="}, "lineStyle": 1, "points": "716:303;821:113", "showVisibility": true, "showProperty": false, "nameLabel": {"$ref": "AAAAAAGWjTCKlhWDn7w="}, "stereotypeLabel": {"$ref": "AAAAAAGWjTCKlhWELgI="}, "propertyLabel": {"$ref": "AAAAAAGWjTCKlhWF0cc="}}, {"_type": "UMLGeneralizationView", "_id": "AAAAAAGWjTC9xhWTI8Q=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWjTC9xhWR2aw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAGWjTC9xxWUcWo=", "_parent": {"$ref": "AAAAAAGWjTC9xhWTI8Q="}, "model": {"$ref": "AAAAAAGWjTC9xhWR2aw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 820, "top": 201, "height": 13, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTC9xhWTI8Q="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTC9xxWVoWc=", "_parent": {"$ref": "AAAAAAGWjTC9xhWTI8Q="}, "model": {"$ref": "AAAAAAGWjTC9xhWR2aw="}, "visible": null, "font": "<PERSON><PERSON>;13;0", "left": 805, "top": 201, "height": 13, "alpha": 1.5707963267948966, "distance": 30, "hostEdge": {"$ref": "AAAAAAGWjTC9xhWTI8Q="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTC9xxWWGdk=", "_parent": {"$ref": "AAAAAAGWjTC9xhWTI8Q="}, "model": {"$ref": "AAAAAAGWjTC9xhWR2aw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 850, "top": 202, "height": 13, "alpha": -1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTC9xhWTI8Q="}, "edgePosition": 1}], "font": "<PERSON><PERSON>;13;0", "head": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "tail": {"$ref": "AAAAAAGWhmKYnGNFX8g="}, "lineStyle": 1, "points": "835:303;835:113", "showVisibility": true, "showProperty": false, "nameLabel": {"$ref": "AAAAAAGWjTC9xxWUcWo="}, "stereotypeLabel": {"$ref": "AAAAAAGWjTC9xxWVoWc="}, "propertyLabel": {"$ref": "AAAAAAGWjTC9xxWWGdk="}}, {"_type": "UMLGeneralizationView", "_id": "AAAAAAGWjTECKxWlM20=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWjTECKhWjMnQ="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAGWjTECKxWmd+w=", "_parent": {"$ref": "AAAAAAGWjTECKxWlM20="}, "model": {"$ref": "AAAAAAGWjTECKhWjMnQ="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 899, "top": 210, "height": 13, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTECKxWlM20="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTECKxWnKqI=", "_parent": {"$ref": "AAAAAAGWjTECKxWlM20="}, "model": {"$ref": "AAAAAAGWjTECKhWjMnQ="}, "visible": null, "font": "<PERSON><PERSON>;13;0", "left": 886, "top": 218, "height": 13, "alpha": 1.5707963267948966, "distance": 30, "hostEdge": {"$ref": "AAAAAAGWjTECKxWlM20="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTECLBWo/Fk=", "_parent": {"$ref": "AAAAAAGWjTECKxWlM20="}, "model": {"$ref": "AAAAAAGWjTECKhWjMnQ="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 924, "top": 193, "height": 13, "alpha": -1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTECKxWlM20="}, "edgePosition": 1}], "font": "<PERSON><PERSON>;13;0", "head": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "tail": {"$ref": "AAAAAAGWhmJL6WLvZRs="}, "lineStyle": 1, "points": "974:303;851:113", "showVisibility": true, "showProperty": false, "nameLabel": {"$ref": "AAAAAAGWjTECKxWmd+w="}, "stereotypeLabel": {"$ref": "AAAAAAGWjTECKxWnKqI="}, "propertyLabel": {"$ref": "AAAAAAGWjTECLBWo/Fk="}}, {"_type": "UMLGeneralizationView", "_id": "AAAAAAGWjTNvfRXNxMA=", "_parent": {"$ref": "AAAAAAFF+qBtyKM79qY="}, "model": {"$ref": "AAAAAAGWjTNvfBXLSNw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAGWjTNvfRXOfXE=", "_parent": {"$ref": "AAAAAAGWjTNvfRXNxMA="}, "model": {"$ref": "AAAAAAGWjTNvfBXLSNw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 906, "top": 140, "height": 13, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTNvfRXNxMA="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTNvfRXPLXU=", "_parent": {"$ref": "AAAAAAGWjTNvfRXNxMA="}, "model": {"$ref": "AAAAAAGWjTNvfBXLSNw="}, "visible": null, "font": "<PERSON><PERSON>;13;0", "left": 898, "top": 153, "height": 13, "alpha": 1.5707963267948966, "distance": 30, "hostEdge": {"$ref": "AAAAAAGWjTNvfRXNxMA="}, "edgePosition": 1}, {"_type": "EdgeLabelView", "_id": "AAAAAAGWjTNvfRXQzbo=", "_parent": {"$ref": "AAAAAAGWjTNvfRXNxMA="}, "model": {"$ref": "AAAAAAGWjTNvfBXLSNw="}, "visible": false, "font": "<PERSON><PERSON>;13;0", "left": 921, "top": 115, "height": 13, "alpha": -1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAGWjTNvfRXNxMA="}, "edgePosition": 1}], "font": "<PERSON><PERSON>;13;0", "head": {"$ref": "AAAAAAGWjSrJ9hSzzBw="}, "tail": {"$ref": "AAAAAAGWhmFLyWKZ4yo="}, "lineStyle": 1, "points": "958:159;871:109", "showVisibility": true, "showProperty": false, "nameLabel": {"$ref": "AAAAAAGWjTNvfRXOfXE="}, "stereotypeLabel": {"$ref": "AAAAAAGWjTNvfRXPLXU="}, "propertyLabel": {"$ref": "AAAAAAGWjTNvfRXQzbo="}}]}, {"_type": "UMLClass", "_id": "AAAAAAGWhlgWCGHqihA=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "export class DatabaseFactory {"}, {"_type": "UMLClass", "_id": "AAAAAAGWhli6YGIsCSw=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "DatabaseFactory", "ownedElements": [{"_type": "UMLDependency", "_id": "AAAAAAGWhx7rPGSHcX8=", "_parent": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "source": {"$ref": "AAAAAAGWhmvZOWPMNAs="}, "target": {"$ref": "AAAAAAGWhli6YGIsCSw="}}, {"_type": "UMLInterfaceRealization", "_id": "AAAAAAGWjS2wLRU4RLw=", "_parent": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "source": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "target": {"$ref": "AAAAAAGWjSrJ9BSxinU="}}, {"_type": "UMLRealization", "_id": "AAAAAAGWjS5/3RVVXjk=", "_parent": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "source": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "target": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "stereotype": "implements"}], "stereotype": "factory", "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGWhln3hGJXBtM=", "_parent": {"$ref": "AAAAAAGWhli6YGIsCSw="}, "name": "createDatabase", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGWhlppm2JeHfk=", "_parent": {"$ref": "AAAAAAGWhln3hGJXBtM="}, "name": "type", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGWhl283mJjZZA=", "_parent": {"$ref": "AAAAAAGWhln3hGJXBtM="}, "type": "Promise<IDatabase>", "direction": "return"}]}]}, {"_type": "UMLClass", "_id": "AAAAAAGWhmFLyWKXzC4=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "SQLiteDatabase", "ownedElements": [{"_type": "UMLGeneralization", "_id": "AAAAAAGWhx+y5GS46GA=", "_parent": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "source": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "target": {"$ref": "AAAAAAGWhmvZOWPMNAs="}}, {"_type": "UMLGeneralization", "_id": "AAAAAAGWjTNvfBXLSNw=", "_parent": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "source": {"$ref": "AAAAAAGWhmFLyWKXzC4="}, "target": {"$ref": "AAAAAAGWjSrJ9BSxinU="}}]}, {"_type": "UMLClass", "_id": "AAAAAAGWhmGb4WLCkkw=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "MySQLDatabase", "ownedElements": [{"_type": "UMLGeneralization", "_id": "AAAAAAGWhx/bhGTa3cs=", "_parent": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "source": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "target": {"$ref": "AAAAAAGWhmvZOWPMNAs="}}, {"_type": "UMLGeneralization", "_id": "AAAAAAGWjTCKlhWAstw=", "_parent": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "source": {"$ref": "AAAAAAGWhmGb4WLCkkw="}, "target": {"$ref": "AAAAAAGWjSrJ9BSxinU="}}]}, {"_type": "UMLClass", "_id": "AAAAAAGWhmJL6WLtzFU=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "PostgresDatabase", "ownedElements": [{"_type": "UMLGeneralization", "_id": "AAAAAAGWhx/yXGTrxhc=", "_parent": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "source": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "target": {"$ref": "AAAAAAGWhmvZOWPMNAs="}}, {"_type": "UMLGeneralization", "_id": "AAAAAAGWjTECKhWjMnQ=", "_parent": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "source": {"$ref": "AAAAAAGWhmJL6WLtzFU="}, "target": {"$ref": "AAAAAAGWjSrJ9BSxinU="}}]}, {"_type": "UMLClass", "_id": "AAAAAAGWhmJeMWMXh14=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "MongoDBDatabase", "ownedElements": [{"_type": "UMLGeneralization", "_id": "AAAAAAGWhx/MvGTJ3fg=", "_parent": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "source": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "target": {"$ref": "AAAAAAGWhmvZOWPMNAs="}}, {"_type": "UMLGeneralization", "_id": "AAAAAAGWjTA2axVrg0E=", "_parent": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "source": {"$ref": "AAAAAAGWhmJeMWMXh14="}, "target": {"$ref": "AAAAAAGWjSrJ9BSxinU="}}]}, {"_type": "UMLClass", "_id": "AAAAAAGWhmKYnGND1Dw=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "RedisDatabase", "ownedElements": [{"_type": "UMLGeneralization", "_id": "AAAAAAGWhyZezWU3XLE=", "_parent": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "source": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "target": {"$ref": "AAAAAAGWhmvZOWPMNAs="}}, {"_type": "UMLGeneralization", "_id": "AAAAAAGWjTC9xhWR2aw=", "_parent": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "source": {"$ref": "AAAAAAGWhmKYnGND1Dw="}, "target": {"$ref": "AAAAAAGWjSrJ9BSxinU="}}]}, {"_type": "UMLClass", "_id": "AAAAAAGWhmvZOWPMNAs=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "IDatabase", "stereotype": "<<interface>>", "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGWhyD8B2Ue1SI=", "_parent": {"$ref": "AAAAAAGWhmvZOWPMNAs="}, "name": "Operation1"}]}, {"_type": "UMLInterface", "_id": "AAAAAAGWjSrJ9BSxinU=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "IDatabase", "stereotype": "<<interface>>", "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGWjS0ZLRUaCtc=", "_parent": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "name": "Operation1"}, {"_type": "UMLOperation", "_id": "AAAAAAGWjS0riBUgPd0=", "_parent": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "name": "Operation2"}, {"_type": "UMLOperation", "_id": "AAAAAAGWjS1F6xUmbsE=", "_parent": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "name": "Operation3"}, {"_type": "UMLOperation", "_id": "AAAAAAGWjS1gDxUsSKQ=", "_parent": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "name": "Operation4"}, {"_type": "UMLOperation", "_id": "AAAAAAGWjS17khUyBKo=", "_parent": {"$ref": "AAAAAAGWjSrJ9BSxinU="}, "name": "Operation5"}]}]}]}