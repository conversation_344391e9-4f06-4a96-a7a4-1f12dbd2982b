{"version": 3, "file": "ai21Provider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/ai21Provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAGpD,yCAAsC;AACtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAG/B;;GAEG;AACH,MAAa,YAAa,SAAQ,iCAAe;IACtC,UAAU,GAAG,MAAM,CAAC;IACpB,WAAW,GAAG,aAAa,CAAC;IAC5B,WAAW,GAAG,uDAAuD,CAAC;IACtE,OAAO,GAAG,6BAA6B,CAAC;IACxC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,gCAAgC,CAAC;IACnD,YAAY,GAAG,UAAU,CAAC;IAE3B,MAAM,GAAyB,IAAI,CAAC;IAE5C,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACnE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAClD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,EAAE,qBAAqB;gBACrC,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,MAAM,EAAE;oBACnC,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C;QAE5C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,iDAAiD,EAAE,CAAC;QACnF,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,qBAAqB;YACrB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,MAAM,CAAC;YACzC,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,SAAS,OAAO,CAAC,OAAO,MAAM,CAAC;oBAC3C,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,cAAc,OAAO,CAAC,OAAO,MAAM,CAAC;oBAChD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,iDAAiD;oBACnD,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;YAC1B,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;wBAC9C,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBACvG,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,WAAW,EAAE;gBAC9D,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,CAAC;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG;gBAClC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,IAAI,EAAE,IAAI;gBACV,aAAa,EAAE,MAAM,CAAC,aAAa,IAAI,EAAE;aAC1C,EAAE;gBACD,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,qBAAqB;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3D,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAEhD,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,MAAM;gBAC1D,KAAK,EAAE;oBACL,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;oBAC1E,gBAAgB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;iBAC7F;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC5C,IAAI,YAAY,GAAG,0BAA0B,CAAC;YAE9C,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,mBAAmB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAC7G,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,sEAAsE;QACtE,OAAO;YACL;gBACE,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,sCAAsC;gBACnD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,mCAAmC;gBAChD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,uCAAuC;gBACpD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,sCAAsC;gBACnD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yDAAyD;aACnE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,WAAW,EAAE;gBAC9D,MAAM,EAAE,eAAe;gBACvB,UAAU,EAAE,CAAC;gBACb,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC/C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,wDAAwD,OAAO,IAAI;iBAC7E,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uDAAuD;iBACjE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,IAAI,YAAY,GAAG,+BAA+B,CAAC;YAEnD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,kEAAkE;gBAC/E,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,iEAAiE;gBAC9E,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,+BAA+B;gBAC5C,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,gBAAgB;iBACjB;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAzRD,oCAyRC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Base<PERSON><PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { logger } from '../../logger';\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\nimport type { AxiosInstance } from 'axios';\n\n/**\n * Provider for AI21 Studio models\n */\nexport class AI21Provider extends BaseLLMProvider {\n  readonly providerId = 'ai21';\n  readonly displayName = 'AI21 Studio';\n  readonly description = 'AI21 Studio provides state-of-the-art language models';\n  readonly website = 'https://www.ai21.com/studio';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api.ai21.com/studio/v1';\n  readonly defaultModel = 'j2-ultra';\n\n  private client: AxiosInstance | null = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        logger.info('AI21 configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (!apiKey) {\n      logger.warn('API key not set for AI21 provider.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 60000, // 60 seconds timeout\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      logger.info('AI21 client initialized successfully.');\n    } catch (error) {\n      logger.error('Failed to initialize AI21 client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using AI21 models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'AI21 provider not configured (API key missing?)' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare the prompt\n      let prompt = '';\n\n      if (params.systemPrompt) {\n        prompt += `${params.systemPrompt}\\n\\n`;\n      }\n\n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            prompt += `User: ${message.content}\\n\\n`;\n          } else if (message.role === 'assistant') {\n            prompt += `Assistant: ${message.content}\\n\\n`;\n          } else if (message.role === 'system') {\n            // System messages already added at the beginning\n          }\n        }\n      } else {\n        // Just add the user prompt\n        prompt += params.prompt;\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            logger.info('AI21 request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          logger.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request\n      const response = await this.client.post(`/${modelId}/complete`, {\n        prompt: prompt,\n        numResults: 1,\n        maxTokens: params.maxTokens || 256,\n        temperature: params.temperature || 0.7,\n        topP: 0.95,\n        stopSequences: params.stopSequences || []\n      }, {\n        signal: abortController?.signal\n      });\n\n      // Parse the response\n      const result = response.data;\n\n      if (!result.completions || result.completions.length === 0) {\n        throw new Error('No completions returned from AI21 API');\n      }\n\n      const content = result.completions[0].data.text;\n\n      return {\n        content,\n        finishReason: result.completions[0].finishReason || 'stop',\n        usage: {\n          promptTokens: result.prompt.tokens || prompt.length / 4, // Rough estimate\n          completionTokens: result.completions[0].data.tokens || content.length / 4, // Rough estimate\n        }\n      };\n    } catch (error: any) {\n      logger.error('AI21 generate error:', error);\n      let errorMessage = 'Failed to call AI21 API.';\n\n      if (error.response) {\n        errorMessage = `AI21 API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available AI21 models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    // AI21 doesn't have a models endpoint, so we return a predefined list\n    return [\n      {\n        id: 'j2-ultra',\n        name: 'Jurassic-2 Ultra',\n        description: 'Most capable model for complex tasks',\n        contextWindow: 8192,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'j2-mid',\n        name: 'Jurassic-2 Mid',\n        description: 'Balanced model for most use cases',\n        contextWindow: 8192,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'j2-light',\n        name: 'Jurassic-2 Light',\n        description: 'Fastest and most cost-effective model',\n        contextWindow: 8192,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'jamba-instruct',\n        name: 'Jamba Instruct',\n        description: 'Latest model with improved reasoning',\n        contextWindow: 8192,\n        pricingInfo: 'Paid'\n      }\n    ];\n  }\n\n  /**\n     * Test connection to AI21\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'AI21 client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Simple test request to check if the API is working\n      const response = await this.client.post(`/${modelId}/complete`, {\n        prompt: 'Hello, world!',\n        numResults: 1,\n        maxTokens: 5,\n        temperature: 0.7\n      });\n\n      if (response.data && response.data.completions) {\n        return {\n          success: true,\n          message: `Successfully connected to AI21 API and tested model '${modelId}'.`\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Connected to API but received an unexpected response.'\n        };\n      }\n    } catch (error: any) {\n      logger.error('AI21 connection test failed:', error);\n      let errorMessage = 'Failed to connect to AI21 API';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your AI21 API key (from https://studio.ai21.com/account/api-key)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The AI21 API endpoint (default: https://api.ai21.com/studio/v1)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default AI21 model to use',\n        required: false,\n        type: 'select',\n        options: [\n          'j2-ultra',\n          'j2-mid',\n          'j2-light',\n          'jamba-instruct'\n        ]\n      }\n    ];\n  }\n}\n"]}