"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
const vscode = __importStar(require("vscode"));
const logger_1 = require("./logger");
const llmService_1 = require("./llm/llmService");
const agentManager_1 = require("./agents/agentUtilities/agentManager");
const toolRegistry_1 = require("./tools/toolRegistry");
const workflowRegistry_1 = require("./agents/workflows/workflowRegistry");
const memoryManager_1 = require("./memory/memoryManager");
const workflowManager_1 = require("./agents/workflows/workflowManager");
const knowledgebaseManager_1 = require("./knowledge/knowledgebaseManager");
const mcpManager_1 = require("./mcp/mcpManager");
const promptManager_1 = require("./prompts/promptManager");
const chatViewProvider_1 = require("./ui/chat/chatViewProvider");
const receiverAgent_1 = __importDefault(require("./agents/agentTypes/receiverAgent"));
const supervisorAgent_1 = require("./agents/agentTypes/supervisorAgent");
const agentFactory_1 = require("./agents/agentUtilities/agentFactory");
const openModeSelector_1 = require("./commands/openModeSelector");
const registerModes_1 = require("./commands/registerModes");
const operationMode_1 = require("./agents/agentModes/operationMode");
const docsTool_1 = require("./tools/docsTool");
const directoryListTool_1 = require("./tools/directoryListTool");
const codeSearchTool_1 = require("./tools/codeSearchTool");
const terminalCommandTool_1 = require("./tools/terminalCommandTool");
const webSearchTool_1 = require("./tools/webSearchTool");
const webReadTool_1 = require("./tools/webReadTool");
const memoryTool_1 = require("./tools/memoryTool");
const browserPreviewTool_1 = require("./tools/browserPreviewTool");
const deployWebAppTool_1 = require("./tools/deployWebAppTool");
const gitTool_1 = require("./tools/gitTool");
const editorActionsTool_1 = require("./tools/editorActionsTool");
const codeIntelligenceTool_1 = require("./tools/codeIntelligenceTool");
const codeGenerationTool_1 = require("./tools/codeGenerationTool");
const lintDiagnosticsTool_1 = require("./tools/lintDiagnosticsTool");
const enhancedFileTools_1 = require("./tools/enhancedFileTools");
const mcp_1 = require("./mcp/mcp");
const goddessMode_1 = require("./goddess/goddessMode");
const allSettingsPanel_1 = require("./ui/settings/allSettingsPanel");
const providerManager_1 = require("./llm/providerManager");
const settingsManager_1 = require("./ui/settings/settingsManager");
const fileChangeTracker_1 = require("./ui/chat/fileChangeTracker");
const fileChangePanel_1 = require("./ui/chat/fileChangePanel");
const projectWideIntelligence_1 = require("./intelligence/projectWideIntelligence");
const crossRepositoryContext_1 = require("./intelligence/crossRepositoryContext");
const agentTree_1 = require("./ui/agents/agentTree");
const TerminalView_1 = require("./ui/components/TerminalView");
const agent_1 = require("./agents/agentUtilities/agent");
const modelConfig_1 = require("./config/modelConfig");
const toolDetailsPanel_1 = require("./ui/tools/toolDetailsPanel");
// This method is called when your extension is activated
async function activate(context) {
    logger_1.Logger.initialize(context);
    logger_1.logger.info('Codessa extension activating...');
    let toolRegistry;
    // Initialize settings manager first
    try {
        const settingsManager = settingsManager_1.SettingsManager.getInstance();
        await settingsManager.initialize();
        logger_1.logger.info('Settings manager initialized');
    }
    catch (e) {
        const errorMessage = 'Codessa: SettingsManager initialization failed: ' + (e instanceof Error ? e.message : String(e));
        logger_1.logger.error(errorMessage);
        // Defer showing error message until after activation
        setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
    }
    // Initialize LLM service
    try {
        llmService_1.llmService.initialize(context);
        logger_1.logger.info('LLM service initialized');
    }
    catch (e) {
        const errorMessage = 'Codessa: LLM service initialization failed: ' + (e instanceof Error ? e.message : String(e));
        logger_1.logger.error(errorMessage);
        // Defer showing error message until after activation
        setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
    }
    // Initialize memory manager
    const memoryManager = new memoryManager_1.MemoryManager();
    try {
        await memoryManager.initialize(context);
        logger_1.logger.info('Memory manager initialized');
    }
    catch (e) {
        const errorMessage = 'Codessa: Memory manager initialization failed: ' + (e instanceof Error ? e.message : String(e));
        logger_1.logger.error(errorMessage);
        // Defer showing error message until after activation
        setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
    }
    // Initialize prompt manager
    try {
        await promptManager_1.promptManager.initialize(context);
        logger_1.logger.info('Prompt manager initialized');
    }
    catch (e) {
        const errorMessage = 'Codessa: Prompt manager initialization failed: ' + (e instanceof Error ? e.message : String(e));
        logger_1.logger.error(errorMessage);
        // Defer showing error message until after activation
        setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
    }
    // Initialize knowledgebase manager
    try {
        await knowledgebaseManager_1.knowledgebaseManager.addKnowledge('Codessa is a powerful, agentic AI coding assistant for Visual Studio Code.');
        logger_1.logger.info('Knowledgebase manager initialized');
    }
    catch (e) {
        const errorMessage = 'Codessa: Knowledgebase manager initialization failed: ' + (e instanceof Error ? e.message : String(e));
        logger_1.logger.error(errorMessage);
        // Defer showing error message until after activation
        setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
    }
    // Initialize tool registry first since other components depend on it
    try {
        toolRegistry = await toolRegistry_1.ToolRegistry.initialize(context);
        logger_1.logger.info('Tool registry initialized');
    }
    catch (e) {
        const errorMessage = 'Codessa: Tool registry initialization failed: ' + (e instanceof Error ? e.message : String(e));
        logger_1.logger.error(errorMessage);
        vscode.window.showErrorMessage(errorMessage);
        return;
    }
    // Register tools with standardized IDs - these will be updated to include schema properties
    try {
        toolRegistry.registerToolWithId(new docsTool_1.DocumentationTool(), 'docs');
        toolRegistry.registerToolWithId(new directoryListTool_1.DirectoryListTool(), 'listDir');
        toolRegistry.registerToolWithId(new codeSearchTool_1.CodeSearchTool(), 'codeSearch');
        toolRegistry.registerToolWithId(new terminalCommandTool_1.TerminalCommandTool(), 'terminal_command');
        toolRegistry.registerToolWithId(new webSearchTool_1.WebSearchTool(), 'webSearch');
        toolRegistry.registerToolWithId(new webReadTool_1.WebReadTool(), 'webRead');
        toolRegistry.registerToolWithId(new memoryTool_1.MemoryTool(memoryManager), 'memory_management');
        toolRegistry.registerToolWithId(new browserPreviewTool_1.BrowserPreviewTool(), 'browserPreview');
        toolRegistry.registerToolWithId(new deployWebAppTool_1.DeployWebAppTool(), 'deployWebApp');
        toolRegistry.registerToolWithId(new gitTool_1.GitTool(), 'git');
        toolRegistry.registerToolWithId(new editorActionsTool_1.EditorActionsTool(), 'editor');
        toolRegistry.registerToolWithId(new codeIntelligenceTool_1.CodeIntelligenceTool(), 'codeIntel');
        toolRegistry.registerToolWithId(new codeGenerationTool_1.CodeGenerationTool(), 'codeGen');
        toolRegistry.registerToolWithId(new lintDiagnosticsTool_1.LintDiagnosticsTool(), 'lint_diagnostics');
        toolRegistry.registerToolWithId(new enhancedFileTools_1.EnhancedFileSystemTool(), 'enhancedFile');
    }
    catch (error) {
        logger_1.logger.warn('Some tools may not have proper schema definitions:', error);
    }
    // Verify all tools were registered successfully
    const registeredTools = toolRegistry_1.ToolRegistry.instance.getAllTools();
    if (registeredTools.length === 0) {
        logger_1.logger.error('No tools were registered in the tool registry');
    }
    else {
        logger_1.logger.info(`Successfully registered ${registeredTools.length} tools`);
        // Log each registered tool
        registeredTools.forEach((tool) => {
            logger_1.logger.info(`Registered tool: ${tool.id} (${tool.name})`);
        });
    }
    // Store the tool registry instance in the extension context
    context.subscriptions.push({
        dispose: () => {
            try {
                // Clean up tools
                const tools = toolRegistry.getAllTools();
                tools.forEach(tool => {
                    if (tool.dispose) {
                        tool.dispose();
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('Error cleaning up tools:', error);
            }
        }
    });
    // Initialize core services that other components depend on (after tool registration)
    // Workflow registry
    try {
        await workflowRegistry_1.workflowRegistry.initialize();
        logger_1.logger.info('Workflow registry initialized');
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Workflow registry initialization failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Register operation modes
    try {
        await (0, registerModes_1.registerModes)(context);
        logger_1.logger.info('Operation modes registered');
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Operation modes registration failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Initialize operation modes
    try {
        await operationMode_1.operationModeRegistry.initializeModes(context);
        logger_1.logger.info('Operation modes initialized');
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Operation modes initialization failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Create receiver agent
    let receiverAgentInstance;
    try {
        receiverAgentInstance = new receiverAgent_1.default({
            id: 'receiver',
            name: 'Receiver Agent',
            systemPromptName: 'receiver',
            capabilities: ['receive'],
            llmProvider: 'openai',
            llmModel: 'gpt-4'
        });
        logger_1.logger.info('Receiver agent created');
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Receiver agent creation failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Set up agent context
    let agentContext;
    try {
        const mcpManagerInstance = mcpManager_1.mcpManager;
        agentContext = {
            variables: {
                workflowManager: workflowManager_1.workflowManager,
                mcpManager: mcpManagerInstance,
                promptManager: promptManager_1.promptManager,
                knowledgebaseManager: knowledgebaseManager_1.knowledgebaseManager
            }
        };
        if (receiverAgentInstance) {
            receiverAgentInstance.setContext(agentContext);
            const agentManager = agentManager_1.AgentManager.getInstance();
            agentManager.setReceiverAgent(receiverAgentInstance);
            logger_1.logger.info('Receiver agent initialized');
        }
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Agent context setup failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Create supervisor agent
    let supervisorAgent;
    try {
        supervisorAgent = new supervisorAgent_1.SupervisorAgent({
            id: 'supervisor-agent',
            name: 'Supervisor Agent',
            description: 'Orchestrates specialized agents and workflows',
            systemPromptName: 'supervisorAgent',
            tools: ['enhancedFileSystem', 'codeSearch', 'memory', 'workflow']
        }, receiverAgentInstance);
        if (agentContext && supervisorAgent) {
            supervisorAgent.setContext(agentContext);
            const agentManager = agentManager_1.AgentManager.getInstance();
            agentManager.setSupervisorAgent(supervisorAgent);
            logger_1.logger.info('Supervisor agent initialized');
        }
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Supervisor agent creation failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Create default operation mode agent
    try {
        const defaultMode = operationMode_1.operationModeRegistry.getDefaultMode();
        if (defaultMode) {
            const modeAgent = (0, agentFactory_1.createModeAgent)(defaultMode.id, {
                id: `mode_${defaultMode.id}`,
                name: defaultMode.displayName,
                systemPromptName: defaultMode.id
            });
            if (modeAgent) {
                const agentManager = agentManager_1.AgentManager.getInstance();
                agentManager.addAgent(modeAgent);
                logger_1.logger.info(`Default mode agent (${defaultMode.id}) initialized`);
            }
        }
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Default mode agent creation failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Store agents in global state
    try {
        if (context && receiverAgentInstance && supervisorAgent) {
            context.globalState.update('receiverAgent', receiverAgentInstance);
            context.globalState.update('supervisorAgent', supervisorAgent);
            logger_1.logger.info('Multi-agent system initialized successfully');
        }
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Storing agents in global state failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // VS Code integrations
    try {
        logger_1.logger.info('Initializing VS Code integrations...');
        logger_1.logger.info('✅ Language Model integration ready');
        // Initialize MCP Manager
        const mcpManagerInstance = mcp_1.MCPManager.getInstance();
        if (mcpManagerInstance && typeof mcpManagerInstance.dispose === 'function') {
            context.subscriptions.push(mcpManagerInstance);
        }
        logger_1.logger.info('✅ Enhanced MCP 2025 initialized');
        // Initialize Agent Tree View
        const agentTreeDataProvider = new agentTree_1.AgentTreeDataProvider();
        context.subscriptions.push(vscode.window.registerTreeDataProvider('codessaAgentView', agentTreeDataProvider));
        logger_1.logger.info('✅ Agent tree view initialized');
        const goddessMode = goddessMode_1.GoddessModeManager.getInstance();
        const goddessDisposable = {
            dispose: () => {
                const manager = goddessMode;
                if (manager.dispose) {
                    manager.dispose();
                }
            }
        };
        context.subscriptions.push(goddessDisposable);
        const config = vscode.workspace.getConfiguration('codessa');
        const autoActivateGoddess = config.get('goddess.autoActivate', false);
        if (autoActivateGoddess) {
            goddessMode.activate();
            logger_1.logger.info('✅ Goddess Mode auto-activated');
        }
        else {
            logger_1.logger.info('✅ Goddess Mode initialized (inactive)');
        }
        context.globalState.update('mcpManager', mcpManager_1.mcpManager);
        context.globalState.update('goddessMode', goddessMode);
        logger_1.logger.info('🚀 VS Code integrations initialized successfully!');
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: VS Code integrations initialization failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // UI enhancements will be registered later to avoid duplication
    logger_1.logger.info('UI enhancements will be registered with tree view providers');
    // Advanced memory and intelligence systems
    let quantumMemory, projectIntelligence, crossRepoContext;
    try {
        logger_1.logger.info('Initializing advanced memory systems...');
        await memoryManager.initializeQuantumMemory(supervisorAgent);
        quantumMemory = memoryManager.getQuantumMemorySystem();
        if (!quantumMemory) {
            throw new Error('Quantum memory system is required for project intelligence');
        }
        projectIntelligence = new projectWideIntelligence_1.ProjectWideIntelligence(supervisorAgent, toolRegistry_1.ToolRegistry.instance, memoryManager, quantumMemory);
        crossRepoContext = new crossRepositoryContext_1.CrossRepositoryContext(supervisorAgent, toolRegistry_1.ToolRegistry.instance, quantumMemory, projectIntelligence);
        await crossRepoContext.initialize();
        context.globalState.update('projectIntelligence', projectIntelligence);
        context.globalState.update('crossRepoContext', crossRepoContext);
        context.subscriptions.push({
            dispose: () => {
                projectIntelligence.dispose();
                crossRepoContext.dispose();
            }
        });
        logger_1.logger.info('🧠 Advanced memory systems initialized successfully!');
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Advanced memory/intelligence systems initialization failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Advanced AI features
    let goddessPersonalityEngine, quantumTool, neuralTool;
    try {
        logger_1.logger.info('Initializing advanced AI features...');
        const goddessModule = await Promise.resolve().then(() => __importStar(require('./personality/goddessMode')));
        goddessPersonalityEngine = goddessModule.goddessPersonalityEngine;
        logger_1.logger.info('✅ Goddess Personality Engine initialized');
        const quantumModule = await Promise.resolve().then(() => __importStar(require('./tools/quantumAnalysisTool')));
        quantumTool = new quantumModule.QuantumAnalysisTool();
        toolRegistry_1.ToolRegistry.instance.registerTool(quantumTool);
        logger_1.logger.info('✅ Quantum Analysis Tool registered');
        const neuralModule = await Promise.resolve().then(() => __importStar(require('./tools/neuralCodeSynthesisTool')));
        neuralTool = new neuralModule.NeuralCodeSynthesisTool();
        toolRegistry_1.ToolRegistry.instance.registerTool(neuralTool);
        logger_1.logger.info('✅ Neural Code Synthesis Tool registered');
    }
    catch (e) {
        vscode.window.showErrorMessage('Codessa: Advanced AI features initialization failed: ' + (e instanceof Error ? e.message : String(e)));
    }
    // Register codessa.addAgent command
    context.subscriptions.push(vscode.commands.registerCommand('codessa.addAgent', async () => {
        const agentName = await vscode.window.showInputBox({ prompt: 'Enter a name for the new agent' });
        if (agentName) {
            const defaultConfig = await (0, modelConfig_1.getDefaultModelConfig)();
            const newAgentConfig = {
                id: agentName.toLowerCase().replace(/\s+/g, '-'),
                name: agentName,
                description: 'A new agent created by the user.',
                role: 'specialist',
                capabilities: [],
                llmProvider: defaultConfig.provider,
                llmModel: defaultConfig.modelId,
                systemPromptName: 'default',
                tools: [],
            };
            const agentInstance = new agent_1.Agent(newAgentConfig);
            const agentManager = agentManager_1.AgentManager.getInstance();
            agentManager.addAgent(agentInstance);
            vscode.window.showInformationMessage(`Agent '${agentName}' created successfully.`);
        }
    }), 
    // Register codessa.openToolDetails command
    vscode.commands.registerCommand('codessa.openToolDetails', (toolId) => {
        try {
            // Forward to openToolDetailsPanel which already has the implementation
            vscode.commands.executeCommand('codessa.openToolDetailsPanel', toolId);
        }
        catch (error) {
            const errorMessage = `Failed to open tool details: ${error instanceof Error ? error.message : String(error)}`;
            logger_1.logger.error(errorMessage);
            vscode.window.showErrorMessage(errorMessage);
        }
    }), 
    // Register codessa.openToolDetailsPanel command
    vscode.commands.registerCommand('codessa.openToolDetailsPanel', (toolId) => {
        try {
            const tool = toolRegistry_1.ToolRegistry.getTool(toolId);
            if (!tool) {
                vscode.window.showErrorMessage(`Tool with ID '${toolId}' not found.`);
                return;
            }
            toolDetailsPanel_1.ToolDetailsPanel.createOrShow(context.extensionUri, tool);
        }
        catch (error) {
            const errorMessage = `Failed to open tool details: ${error instanceof Error ? error.message : String(error)}`;
            logger_1.logger.error(errorMessage);
            vscode.window.showErrorMessage(errorMessage);
        }
    }));
    const advancedCommands = [
        vscode.commands.registerCommand('codessa.activateGoddessMode', async () => {
            try {
                // Update goddess personality to be more active
                goddessPersonalityEngine.updatePersonality({
                    adaptiveLevel: 90,
                    emotionalIntelligence: 95,
                    motivationalStyle: 'adaptive',
                    wisdomLevel: 95,
                    creativityLevel: 90
                });
                vscode.window.showInformationMessage('✨ Goddess Mode Activated! I am now Codessa, your divine coding companion with enhanced emotional intelligence and adaptive personality! 👑', 'Chat with Goddess', 'View Personality').then(selection => {
                    if (selection === 'Chat with Goddess') {
                        vscode.commands.executeCommand('codessa.openChatView');
                    }
                    else if (selection === 'View Personality') {
                        const personality = goddessPersonalityEngine.getPersonality();
                        vscode.window.showInformationMessage(`Goddess Personality: Adaptive Level ${personality.adaptiveLevel}%, EQ ${personality.emotionalIntelligence}%, Wisdom ${personality.wisdomLevel}%`);
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('Error activating Goddess Mode:', error);
                vscode.window.showErrorMessage('Failed to activate Goddess Mode');
            }
        }),
        vscode.commands.registerCommand('codessa.quantumAnalysis', async () => {
            try {
                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    vscode.window.showWarningMessage('Please open a file to perform quantum analysis');
                    return;
                }
                const code = editor.document.getText();
                const result = await quantumTool.execute('quantum_pattern_recognition', {
                    action: 'quantum_pattern_recognition',
                    code
                });
                if (result.success && result.output) {
                    const output = result.output;
                    vscode.window.showInformationMessage(`🔬 Quantum Analysis Complete: Found ${output.patterns?.length || 0} quantum patterns with ${(output.confidence * 100).toFixed(1)}% confidence`, 'View Details', 'Run Parallel Test').then(selection => {
                        if (selection === 'View Details') {
                            logger_1.logger.info('Quantum Analysis Results:', result);
                            vscode.window.showInformationMessage('Quantum analysis details logged to output');
                        }
                        else if (selection === 'Run Parallel Test') {
                            vscode.commands.executeCommand('codessa.parallelUniverseTest');
                        }
                    });
                }
                else {
                    vscode.window.showErrorMessage(`Quantum analysis failed: ${result.error || 'Unknown error'}`);
                }
            }
            catch (error) {
                logger_1.logger.error('Error in quantum analysis:', error);
                vscode.window.showErrorMessage('Quantum analysis failed');
            }
        }),
        vscode.commands.registerCommand('codessa.parallelUniverseTest', async () => {
            try {
                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    vscode.window.showWarningMessage('Please open a file to test in parallel universes');
                    return;
                }
                const code = editor.document.getText();
                const result = await quantumTool.execute('parallel_universe_testing', {
                    action: 'parallel_universe_testing',
                    code
                });
                if (result.success && result.output) {
                    const output = result.output;
                    vscode.window.showInformationMessage(`🌌 Parallel Universe Testing: Tested in ${output.universes?.length || 0} universes. Quantum interference: ${output.quantumInterference ? 'Detected' : 'None'}`, 'View Timeline', 'See Predictions').then(selection => {
                        if (selection === 'View Timeline') {
                            logger_1.logger.info('Parallel Universe Test Results:', result);
                            vscode.window.showInformationMessage('Timeline analysis logged to output');
                        }
                        else if (selection === 'See Predictions') {
                            const mitigation = output.mitigationStrategies?.slice(0, 3).join('\n• ') || 'No strategies available';
                            vscode.window.showInformationMessage(`Mitigation Strategies:\n• ${mitigation}`);
                        }
                    });
                }
            }
            catch (error) {
                logger_1.logger.error('Error in parallel universe testing:', error);
                vscode.window.showErrorMessage('Parallel universe testing failed');
            }
        }),
        vscode.commands.registerCommand('codessa.neuralCodeSynthesis', async () => {
            try {
                const prompt = await vscode.window.showInputBox({
                    prompt: 'Enter your code generation request',
                    placeHolder: 'e.g., Create a function to sort an array using neural networks'
                });
                if (!prompt)
                    return;
                const result = await neuralTool.execute('generate_code', {
                    action: 'generate_code',
                    prompt
                });
                const editor = vscode.window.activeTextEditor;
                if (editor && result.success && result.output) {
                    const output = result.output;
                    if (output.generatedCode) {
                        const position = editor.selection.active;
                        await editor.edit(editBuilder => {
                            editBuilder.insert(position, `\n// Neural-generated code (Creativity: ${output.creativityScore}%)\n${output.generatedCode}\n`);
                        });
                        vscode.window.showInformationMessage(`🧠 Neural Code Generated! Creativity Score: ${output.creativityScore}%, Neural Path: ${output.neuralPath?.length || 0} layers`, 'View Insights').then(selection => {
                            if (selection === 'View Insights') {
                                const insights = output.consciousnessInsights?.join('\n• ') || 'No insights available';
                                vscode.window.showInformationMessage(`Neural Insights:\n• ${insights}`);
                            }
                        });
                    }
                }
                else {
                    vscode.window.showErrorMessage(`Neural code synthesis failed: ${result.error || 'Unknown error'}`);
                }
            }
            catch (error) {
                logger_1.logger.error('Error in neural code synthesis:', error);
                vscode.window.showErrorMessage('Neural code synthesis failed');
            }
        }),
        vscode.commands.registerCommand('codessa.timeTravelDebug', async () => {
            try {
                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    vscode.window.showWarningMessage('Please open a file to perform time-travel debugging');
                    return;
                }
                const timeTravelTool = toolRegistry_1.ToolRegistry.instance.getTool('timeTravelDebug');
                if (!timeTravelTool) {
                    vscode.window.showErrorMessage('Time-travel debugging tool not available');
                    return;
                }
                const result = await timeTravelTool.execute('predictFutureIssues', {
                    filePath: editor.document.uri.fsPath,
                    timeHorizon: '3-months'
                });
                if (result.success) {
                    const predictions = result.output;
                    vscode.window.showInformationMessage(`⏰ Time-Travel Analysis: Risk Score ${predictions.combinedRiskScore}%, Found ${predictions.traditionalPredictions?.length || 0} traditional + ${predictions.quantumPredictions?.length || 0} quantum predictions`, 'View Timeline', 'See Predictions').then(selection => {
                        if (selection === 'View Timeline') {
                            logger_1.logger.info('Time-travel predictions:', predictions);
                            vscode.window.showInformationMessage('Timeline analysis logged to output');
                        }
                        else if (selection === 'See Predictions') {
                            const mitigation = predictions.mitigationStrategies?.slice(0, 3).join('\n• ') || 'No strategies available';
                            vscode.window.showInformationMessage(`Mitigation Strategies:\n• ${mitigation}`);
                        }
                    });
                }
            }
            catch (error) {
                logger_1.logger.error('Error in time-travel debugging:', error);
                vscode.window.showErrorMessage('Time-travel debugging failed');
            }
        }),
        // Advanced Workflow Commands
        vscode.commands.registerCommand('codessa.createAdvancedWorkflow', async () => {
            try {
                const workflowType = await vscode.window.showQuickPick([
                    {
                        label: '✨ Goddess-Guided Workflow',
                        description: 'Enhanced with divine coding wisdom and emotional intelligence',
                        detail: 'Perfect for complex development tasks requiring guidance and support'
                    },
                    {
                        label: '🔬 Quantum-Enhanced Workflow',
                        description: 'Leverages quantum-inspired algorithms for superior analysis',
                        detail: 'Ideal for code analysis and pattern recognition tasks'
                    },
                    {
                        label: '🧠 Neural Synthesis Workflow',
                        description: 'Brain-inspired code generation and optimization',
                        detail: 'Best for code generation and creative problem-solving'
                    },
                    {
                        label: '⏰ Time-Travel Debugging Workflow',
                        description: 'Predicts and prevents future issues',
                        detail: 'Essential for maintaining code quality and preventing bugs'
                    },
                    {
                        label: '🚀 Ultimate Advanced Workflow',
                        description: 'Combines all advanced features for maximum power',
                        detail: 'The most advanced workflow with all AI capabilities enabled'
                    }
                ], {
                    placeHolder: 'Select the type of advanced workflow to create',
                    title: '🚀 Create Advanced Workflow'
                });
                if (!workflowType)
                    return;
                const workflowName = await vscode.window.showInputBox({
                    prompt: 'Enter a name for your advanced workflow',
                    placeHolder: 'My Advanced Workflow',
                    title: '✨ Name Your Advanced Workflow'
                });
                if (!workflowName)
                    return;
                // Create advanced workflow based on selection
                const { workflowRegistry } = await Promise.resolve().then(() => __importStar(require('./agents/workflows/workflowRegistry')));
                const { Workflow } = await Promise.resolve().then(() => __importStar(require('./agents/workflows/workflowEngine')));
                // Create an advanced workflow definition
                const advancedWorkflow = {
                    id: `advanced-${Date.now()}`,
                    name: workflowName,
                    description: `Advanced workflow: ${workflowType.label}`,
                    version: '1.0.0',
                    steps: [],
                    inputs: [],
                    outputs: [],
                    startStepId: 'advanced-start'
                };
                // Create a graph definition for registration
                const graphDefinition = {
                    id: advancedWorkflow.id,
                    name: advancedWorkflow.name,
                    description: advancedWorkflow.description,
                    version: advancedWorkflow.version,
                    nodes: [],
                    edges: [],
                    startNodeId: 'advanced-start',
                    operationMode: workflowType.label.toLowerCase().replace(/\s+/g, '-'),
                    tags: ['advanced', workflowType.label.toLowerCase()]
                };
                // Create and configure the workflow
                const workflow = new Workflow(advancedWorkflow);
                // Enable advanced features based on selection
                if (workflowType.label.includes('Goddess')) {
                    workflow.enableAdvancedFeatures({ goddessMode: true, adaptivePersonality: true });
                }
                else if (workflowType.label.includes('Quantum')) {
                    workflow.enableAdvancedFeatures({ quantumAnalysis: true });
                }
                else if (workflowType.label.includes('Neural')) {
                    workflow.enableAdvancedFeatures({ neuralSynthesis: true });
                }
                else if (workflowType.label.includes('Time-Travel')) {
                    workflow.enableAdvancedFeatures({ timeTravelDebugging: true });
                }
                else if (workflowType.label.includes('Ultimate')) {
                    workflow.enableAdvancedFeatures({
                        goddessMode: true,
                        quantumAnalysis: true,
                        neuralSynthesis: true,
                        timeTravelDebugging: true,
                        adaptivePersonality: true
                    });
                }
                // Register the workflow
                workflowRegistry.registerWorkflow(graphDefinition);
                vscode.window.showInformationMessage(`🚀 Advanced workflow "${workflowName}" created successfully with ${workflowType.label} capabilities!`, 'Open Workflow Panel', 'Test Workflow').then(selection => {
                    if (selection === 'Open Workflow Panel') {
                        vscode.commands.executeCommand('codessa.openWorkflowPanel');
                    }
                    else if (selection === 'Test Workflow') {
                        vscode.commands.executeCommand('codessa.runAdvancedWorkflow', advancedWorkflow.id);
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('Error creating advanced workflow:', error);
                vscode.window.showErrorMessage('Failed to create advanced workflow');
            }
        }),
        vscode.commands.registerCommand('codessa.runAdvancedWorkflow', async (workflowId) => {
            try {
                if (!workflowId) {
                    // Show workflow picker
                    const { workflowRegistry } = await Promise.resolve().then(() => __importStar(require('./agents/workflows/workflowRegistry')));
                    const advancedWorkflows = workflowRegistry.getAdvancedWorkflows();
                    if (advancedWorkflows.length === 0) {
                        vscode.window.showInformationMessage('No advanced workflows found. Create one first!', 'Create Advanced Workflow').then(selection => {
                            if (selection) {
                                vscode.commands.executeCommand('codessa.createAdvancedWorkflow');
                            }
                        });
                        return;
                    }
                    const selectedWorkflow = await vscode.window.showQuickPick(advancedWorkflows.map((wf) => {
                        const metadata = wf.metadata || {};
                        const features = metadata.advancedFeatures || metadata.revolutionaryFeatures || {};
                        return {
                            label: `${features.goddessMode ? '✨' : ''}${features.quantumAnalysis ? '🔬' : ''}${features.neuralSynthesis ? '🧠' : ''}${features.timeTravelDebugging ? '⏰' : ''} ${wf.name}`,
                            description: wf.description || '',
                            detail: `Features: ${Object.entries(features)
                                .filter(([, enabled]) => Boolean(enabled))
                                .map(([feature]) => feature)
                                .join(', ')}`,
                            workflowId: wf.id
                        };
                    }), {
                        placeHolder: 'Select an advanced workflow to run',
                        title: '🚀 Run Advanced Workflow'
                    });
                    if (!selectedWorkflow)
                        return;
                    workflowId = selectedWorkflow.workflowId;
                }
                vscode.window.showInformationMessage('🚀 Running advanced workflow with enhanced AI capabilities...', 'View Progress').then(selection => {
                    if (selection === 'View Progress') {
                        vscode.commands.executeCommand('codessa.openWorkflowPanel');
                    }
                });
            }
            catch (error) {
                logger_1.logger.error('Error running advanced workflow:', error);
                vscode.window.showErrorMessage('Failed to run advanced workflow');
            }
        })
    ];
    // Register all advanced commands
    if (context) {
        advancedCommands.forEach(command => context.subscriptions.push(command));
    }
    // Register ChatViewProvider for the chat sidebar
    let chatViewProvider;
    try {
        chatViewProvider = new chatViewProvider_1.ChatViewProvider(context.extensionUri, context);
        const chatViewProviderDisposable = vscode.window.registerWebviewViewProvider('codessa.chatView', chatViewProvider, {
            webviewOptions: { retainContextWhenHidden: true }
        });
        context.subscriptions.push(chatViewProviderDisposable);
        logger_1.logger.info('✅ Chat view provider registered successfully');
    }
    catch (error) {
        logger_1.logger.error('❌ Failed to register chat view provider:', error);
        vscode.window.showErrorMessage(`Failed to register chat view: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    // Refresh agent references in ChatViewProvider after agents are set up
    if (chatViewProvider && receiverAgentInstance && supervisorAgent) {
        try {
            chatViewProvider.refreshAgentReferences();
            logger_1.logger.info('✅ ChatViewProvider agent references refreshed');
        }
        catch (error) {
            logger_1.logger.error('❌ Failed to refresh ChatViewProvider agent references:', error);
        }
    }
    // Register tree view data providers
    const { registerAgentTreeView } = await Promise.resolve().then(() => __importStar(require('./ui/agents/agentTree')));
    const { registerToolsTreeView } = await Promise.resolve().then(() => __importStar(require('./ui/tools/toolsView')));
    registerAgentTreeView(context);
    registerToolsTreeView(context);
    logger_1.logger.info('Tree view data providers registered successfully');
    // Register terminal view
    const terminalView = new TerminalView_1.TerminalView(context.extensionUri);
    context.subscriptions.push(vscode.window.registerWebviewViewProvider(TerminalView_1.TerminalView.viewType, terminalView));
    // Initialize file change tracking
    const fileChangeTracker = fileChangeTracker_1.FileChangeTracker.getInstance(context);
    context.subscriptions.push(fileChangeTracker);
    // Register file change tracking commands
    context.subscriptions.push(vscode.commands.registerCommand('codessa.showFileChanges', async () => {
        try {
            await fileChangeTracker.showFileChanges();
        }
        catch (error) {
            logger_1.logger.error('Failed to show file changes:', error);
            vscode.window.showErrorMessage('Failed to show file changes');
        }
    }), vscode.commands.registerCommand('codessa.openFileChangePanel', () => {
        try {
            fileChangePanel_1.FileChangePanel.createOrShow(context.extensionUri, context);
        }
        catch (error) {
            logger_1.logger.error('Failed to open file change panel:', error);
            vscode.window.showErrorMessage('Failed to open file change panel');
        }
    }), vscode.commands.registerCommand('codessa.refreshFileChanges', async () => {
        try {
            await fileChangeTracker.refreshChanges();
            vscode.window.showInformationMessage('File changes refreshed');
        }
        catch (error) {
            logger_1.logger.error('Failed to refresh file changes:', error);
            vscode.window.showErrorMessage('Failed to refresh file changes');
        }
    }));
    // Register settings commands
    context.subscriptions.push(vscode.commands.registerCommand('codessa.openSettings', async () => {
        try {
            await allSettingsPanel_1.AllSettingsPanel.createOrShow(context, context.extensionUri, memoryManager, providerManager_1.providerManager.getInstance());
        }
        catch (error) {
            logger_1.logger.error('Failed to open settings panel:', error);
            vscode.window.showErrorMessage('Failed to open settings panel');
        }
    }), 
    // Debug command to check provider status
    vscode.commands.registerCommand('codessa.debugProviderStatus', async () => {
        try {
            const config = vscode.workspace.getConfiguration('codessa.llm');
            const enabledProviders = config.get('enabledProviders') || ['ollama'];
            const suppressErrors = vscode.workspace.getConfiguration('codessa').get('suppressProviderErrors', true);
            const message = `Provider Status Debug:
- Enabled Providers: ${enabledProviders.join(', ')}
- Suppress Provider Errors: ${suppressErrors}
- LM Studio Enabled: ${enabledProviders.includes('lmstudio')}`;
            vscode.window.showInformationMessage(message);
            logger_1.logger.info(message);
        }
        catch (error) {
            logger_1.logger.error('Failed to get provider status:', error);
            vscode.window.showErrorMessage('Failed to get provider status');
        }
    }), vscode.commands.registerCommand('codessa.openSettingsView', async () => {
        try {
            await allSettingsPanel_1.AllSettingsPanel.createOrShow(context, context.extensionUri, memoryManager, providerManager_1.providerManager.getInstance());
        }
        catch (error) {
            logger_1.logger.error('Failed to open settings view:', error);
            vscode.window.showErrorMessage('Failed to open settings view');
        }
    }));
    logger_1.logger.info('Settings commands registered successfully');
    // Register mode selector command
    context.subscriptions.push(vscode.commands.registerCommand('codessa.openModeSelector', () => {
        try {
            (0, openModeSelector_1.openModeSelector)(context);
        }
        catch (error) {
            logger_1.logger.error('Failed to open mode selector:', error);
            vscode.window.showErrorMessage('Failed to open mode selector');
        }
    }));
    logger_1.logger.info('Mode selector command registered successfully');
    context.globalState.update('goddessPersonalityEngine', goddessPersonalityEngine);
    context.globalState.update('quantumAnalysisTool', quantumTool);
    context.globalState.update('neuralCodeSynthesisTool', neuralTool);
    logger_1.logger.info('🚀 Advanced AI features initialized successfully!');
}
//# sourceMappingURL=extension.js.map