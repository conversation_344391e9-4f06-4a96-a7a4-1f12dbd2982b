import * as vscode from 'vscode';
import { ITool } from '../../tools/tool.ts.backup';

export class ToolDetailsPanel {
  public static currentPanel: ToolDetailsPanel | undefined;
  private readonly _panel: vscode.WebviewPanel;
  private readonly _extensionUri: vscode.Uri;
  private readonly _tool: ITool;

  public static readonly viewType = 'codessa.toolDetails';

  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, tool: ITool) {
    this._panel = panel;
    this._extensionUri = extensionUri;
    this._tool = tool;
    this._panel.webview.html = this._getHtmlForWebview();
  }

  public static createOrShow(extensionUri: vscode.Uri, tool: ITool) {
    const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined;
    if (ToolDetailsPanel.currentPanel) {
      ToolDetailsPanel.currentPanel._panel.reveal(column);
      ToolDetailsPanel.currentPanel._panel.webview.html = ToolDetailsPanel.currentPanel._getHtmlForWebview();
      return;
    }
    const panel = vscode.window.createWebviewPanel(
      ToolDetailsPanel.viewType,
      `Tool Details: ${tool.name || tool.id}`,
      column || vscode.ViewColumn.One,
      { enableScripts: true }
    );
    ToolDetailsPanel.currentPanel = new ToolDetailsPanel(panel, extensionUri, tool);
  }

  private _getHtmlForWebview(): string {
    const tool = this._tool;
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tool Details: ${tool.name || tool.id}</title>
    <style>
        body { font-family: sans-serif; padding: 16px; }
        h1 { font-size: 1.5em; }
        .section { margin-bottom: 1em; }
        .label { font-weight: bold; }
        .value { margin-left: 0.5em; }
        pre { background: #f4f4f4; padding: 8px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Tool Details</h1>
    <div class="section"><span class="label">ID:</span><span class="value">${tool.id}</span></div>
    <div class="section"><span class="label">Name:</span><span class="value">${tool.name || 'N/A'}</span></div>
    <div class="section"><span class="label">Description:</span><span class="value">${tool.description || 'No description provided.'}</span></div>
    <div class="section"><span class="label">Type:</span><span class="value">${tool.type || 'N/A'}</span></div>
    <div class="section"><span class="label">Actions:</span><span class="value">${tool.actions ? Object.keys(tool.actions).join(', ') : 'None'}</span></div>
    <div class="section"><span class="label">Config:</span><pre>${tool.config ? JSON.stringify(tool.config, null, 2) : 'None'}</pre></div>
</body>
</html>`;
  }
}
