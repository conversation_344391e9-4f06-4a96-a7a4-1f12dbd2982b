{"version": 3, "file": "multiAgentMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/multiAgentMode.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,mDAA4E;AAG5E,yCAAsC;AACtC,qDAAkD;AAClD,+DAA4D;AAuB5D;;GAEG;AACH,MAAa,cAAe,SAAQ,6BAAa;IACtC,EAAE,GAAG,aAAa,CAAC;IACnB,WAAW,GAAG,aAAa,CAAC;IAC5B,WAAW,GAAG,qDAAqD,CAAC;IACpE,IAAI,GAAG,iBAAiB,CAAC;IACzB,kBAAkB,GAAG,2BAAW,CAAC,eAAe,CAAC;IACjD,yBAAyB,GAAG,KAAK,CAAC;IAClC,sBAAsB,GAAG,IAAI,CAAC;IAE/B,SAAS,GAAG,KAAK,CAAC;IAClB,iBAAiB,CAA6C;IAC9D,gBAAgB,CAAwE;IACzF,aAAa,GAAqC,SAAS,CAAC;IAE3D,MAAM,GAA+B,IAAI,GAAG,EAAE,CAAC;IAC/C,eAAe,CAAoB;IACnC,eAAe,GAA0D,EAAE,CAAC;IAEpF,yBAAyB;IACR,eAAe,GAAgB;QAC9C;YACE,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,oEAAoE;YACjF,YAAY,EAAE,6BAAa,CAAC,YAAY,CAAC,6BAA6B,EAAE,EAAE,CAAC;SAC5E;QACD;YACE,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,gEAAgE;YAC7E,YAAY,EAAE,6BAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,EAAE,CAAC;SAC3E;QACD;YACE,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,0DAA0D;YACvE,YAAY,EAAE,6BAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,EAAE,CAAC;SAC3E;QACD;YACE,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,wEAAwE;YACrF,YAAY,EAAE,6BAAa,CAAC,YAAY,CAAC,yBAAyB,EAAE,EAAE,CAAC;SACxE;QACD;YACE,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,uDAAuD;YACpE,YAAY,EAAE,6BAAa,CAAC,YAAY,CAAC,2BAA2B,EAAE,EAAE,CAAC;SAC1E;KACF,CAAC;IAEF;;SAEK;IACL,KAAK,CAAC,UAAU,CAAC,OAAgC;QAG/C,yBAAyB;QACzB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC5F,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,mCAAmC,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,0BAA0B,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,8BAA8B,CAAC;QAC5D,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY,EACZ,aAA4B;QAE5B,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,OAAO,EAAE,CAAC,CAAC;YAE3E,wCAAwC;YACxC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;YAED,+BAA+B;YAC/B,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAEzD,OAAO,4GAA4G,CAAC;QACtH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YAC9E,OAAO,sCAAsC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QACxG,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW,CAAC,SAAgB;QACxC,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAEpB,iCAAiC;QACjC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACxC,uBAAuB;YACvB,MAAM,aAAa,GAAkB;gBACnC,EAAE,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE;gBACtB,IAAI;gBACJ,KAAK,EAAE,SAAS,EAAE,sEAAsE;gBACxF,QAAQ,EAAE,EAAE;aACb,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QAC1C,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;IACnC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CACjC,IAAY,EACZ,aAA4B;QAE5B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,qFAAqF,CAAC,CAAC;QACzG,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QAC9D,MAAM,KAAK,GAAiB,EAAE,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAEhF,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,sCAAsC,CAAC;YACjE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC5B,CAAC;QAED,2BAA2B;QAC3B,MAAM,CAAC,MAAM,CAAC,YAAY,CACxB;YACE,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;YAC9C,KAAK,EAAE,4BAA4B;YACnC,WAAW,EAAE,IAAI;SAClB,EACD,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;YAChC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;YAEjC,sBAAsB;YACtB,aAAa,CAAC,uBAAuB,CAAC,GAAG,EAAE;gBACzC,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;gBACjC,IAAI,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YAE/D,IAAI,CAAC;gBACH,sBAAsB;gBACtB,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBAE7E,kCAAkC;gBAClC,IAAI,aAAa,GAAG,EAAE,CAAC;gBACvB,IAAI,CAAC;oBACH,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;wBACzB,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;wBACrD,IAAI,WAAW,EAAE,CAAC;4BAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;4BACrE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;gCACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,2CAA2C,CAAC,CAAC;4BACrG,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oDAAoD,EAAE,WAAW,CAAC,CAAC;oBACxF,kCAAkC;gBACpC,CAAC;gBAED,yBAAyB;gBACzB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;gBAE1B,0CAA0C;gBAC1C,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAEpF,oBAAoB;gBACpB,IAAI,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,CAAC;gBAEzD,+BAA+B;gBAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yDAAyD,CAAC,CAAC;gBAEhG,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBAChE,IAAI,CAAC,oBAAoB,CAAC,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAE9F,0BAA0B;gBAC1B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4CAA4C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACvI,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAC9B,IAAY,EACZ,cAAsB,EACtB,aAAqB,EACrB,QAAmE,EACnE,KAAmB;QAEnB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,IAAI,gBAAgB,GAAG,CAAC,CAAC;QAEzB,oCAAoC;QACpC,MAAM,gBAAgB,GAAG;;;EAG3B,IAAI;;;;EAIJ,cAAc;;EAEd,aAAa;;;EAGb,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;CAS5G,CAAC;QAEE,wBAAwB;QACxB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QAE1D,wBAAwB;QACxB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,yDAAyD,EAAE,CAAC,CAAC;QAExF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CACtD,gBAAgB,EAChB,IAAI,CAAC,YAAY,EAAE,EACnB,KAAK,CACN,CAAC;QAEF,0BAA0B;QAC1B,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QAEzD,mCAAmC;QACnC,OAAO,gBAAgB,GAAG,cAAc,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;YAC9F,gBAAgB,EAAE,CAAC;YAEnB,kBAAkB;YAClB,QAAQ,CAAC,MAAM,CAAC;gBACd,OAAO,EAAE,aAAa,gBAAgB,qCAAqC;gBAC3E,SAAS,EAAE,GAAG,GAAG,cAAc;aAChC,CAAC,CAAC;YAEH,0DAA0D;YAC1D,KAAK,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC5D,IAAI,MAAM,KAAK,YAAY;oBAAE,SAAS;gBAEtC,iCAAiC;gBACjC,MAAM,WAAW,GAAG;cACd,aAAa,CAAC,IAAI,CAAC,IAAI;;EAEnC,IAAI;;;;EAIJ,YAAY;;yBAEW,aAAa,CAAC,IAAI,CAAC,WAAW;;;CAGtD,CAAC;gBAEM,mBAAmB;gBACnB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAE/C,0BAA0B;gBAC1B,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,gBAAgB,EAAE,CAAC,CAAC;gBAEzE,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,KAAK,CAAC,QAAQ,CACtD,WAAW,EACX,IAAI,CAAC,YAAY,EAAE,EACnB,KAAK,CACN,CAAC;gBAEF,qBAAqB;gBACrB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;gBAEpD,uCAAuC;gBACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1D,CAAC;YAED,2CAA2C;YAC3C,MAAM,kBAAkB,GAAG;;;QAGzB,IAAI;;;EAGV,YAAY;;;EAGZ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;iBACxB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,YAAY,CAAC;iBACrC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;gBACpD,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,eAAe,EAAE,CAAC;YACpE,CAAC,CAAC;iBACD,IAAI,CAAC,MAAM,CAAC;;;;CAItB,CAAC;YAEI,0BAA0B;YAC1B,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAE5D,+BAA+B;YAC/B,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,wCAAwC,EAAE,CAAC,CAAC;YAEvE,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAC9D,kBAAkB,EAClB,IAAI,CAAC,YAAY,EAAE,EACnB,KAAK,CACN,CAAC;YAEF,0BAA0B;YAC1B,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,oBAAoB,CAAC,CAAC;YAEjE,4BAA4B;YAC5B,IAAI,oBAAoB,IAAI,CAC1B,oBAAoB,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;gBAC5D,oBAAoB,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAChE,EAAE,CAAC;gBACF,MAAM;YACR,CAAC;YAED,uCAAuC;YACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,gCAAgC;QAChC,MAAM,aAAa,GAAG;;;QAGlB,IAAI;;;;;;;;;;;CAWX,CAAC;QAEE,qBAAqB;QACrB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QAEvD,mBAAmB;QACnB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC,CAAC;QAE5D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CACzD,aAAa,EACb,IAAI,CAAC,YAAY,EAAE,EACnB,KAAK,CACN,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;IAC9D,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,MAAc;QACzC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QAEnC,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,mCAAmC,CAAC;QAChE,CAAC;QAED,aAAa;QACb,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,MAAM,EAAE,CAAC,CAAC;IAChE,CAAC;IAED;;SAEK;IACG,UAAU,CAAC,OAAe,EAAE,IAAY,EAAE,OAAe;QAC/D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACxB,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,IAAI,IAAI,KAAK,OAAO,EAAE;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,OAAe;QAC5C,sDAAsD;QACtD,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe;aACvC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;QAEjF,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5D,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,YAAY;QACxB,0BAA0B;QAC1B,IAAI,aAAa,GAAG,+BAA+B,CAAC;QAEpD,gBAAgB;QAChB,aAAa,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC,cAAc,EAAE,MAAM,CAAC;QAEpE,mBAAmB;QACnB,aAAa,IAAI,qBAAqB,CAAC;QACvC,KAAK,MAAM,CAAC,EAAE,aAAa,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACtD,aAAa,IAAI,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;YACpD,aAAa,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,WAAW,MAAM,CAAC;QAC3D,CAAC;QAED,uBAAuB;QACvB,aAAa,IAAI,yBAAyB,CAAC;QAC3C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvC,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC;gBACrE,aAAa,IAAI,OAAO,SAAS,KAAK,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,OAAO,CAAC;gBAChF,aAAa,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,CAAC;YACpE,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACvD,OAAO,EAAE,aAAa;YACtB,QAAQ,EAAE,UAAU;SACrB,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG,EAAE,oDAAoD;YACtE,SAAS,EAAE,IAAI,EAAI,0CAA0C;YAC7D,IAAI,EAAE,MAAM;SACb,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,eAAe;QACnB,OAAO,6BAAa,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED;;SAEK;IACL,eAAe;QAKb,OAAO;YACL,YAAY,EAAE;;;;;;;;;;;;;;;;;;CAkBnB;YACK,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;CAoBnB;YACK,YAAY,EAAE;;;;;CAKnB;SACI,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,IAAe;QAClD,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,uBAAuB;gBAC1B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACxC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,aAAa,CAAC,GAAG,IAAI,CAAC;oBAC1C,MAAM,IAAI,CAAC,WAAW,CAAC,KAAc,CAAC,CAAC;oBACvC,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAc,EAAE,aAA8B,CAAC,CAAC;gBACnF,CAAC;gBACD,MAAM;YAER,KAAK,sBAAsB;gBACzB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACnB,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;oBACjC,IAAI,CAAC,oBAAoB,CAAC,8CAA8C,CAAC,CAAC;gBAC5E,CAAC;gBACD,MAAM;YAER,KAAK,cAAc;gBACjB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACrB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAc,CAAC;oBAClC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;CACF;AA/jBD,wCA+jBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { contextManager } from './contextManager';\nimport { promptManager } from '../../prompts/promptManager';\nimport { AgentContext } from '../../types/agent';\n\n/**\n * Agent role definition\n */\ninterface AgentRole {\n  id: string;\n  name: string;\n  description: string;\n  systemPrompt: string;\n}\n\n/**\n * Agent instance in a multi-agent system\n */\ninterface AgentInstance {\n  id: string;\n  role: AgentRole;\n  agent: Agent;\n  messages: { role: string; content: string }[];\n}\n\n/**\n * Multi-Agent Mode - Team of agents with supervisor coordination\n */\nexport class MultiAgentMode extends OperationMode {\n  readonly id = 'multi-agent';\n  readonly displayName = 'Multi-Agent';\n  readonly description = 'Team of AI agents working together on complex tasks';\n  readonly icon = '$(organization)';\n  readonly defaultContextType = ContextType.ENTIRE_CODEBASE;\n  readonly requiresHumanVerification = false;\n  readonly supportsMultipleAgents = true;\n\n  private isRunning = false;\n  private cancelTokenSource: vscode.CancellationTokenSource | undefined;\n  private progressReporter: vscode.Progress<{ message?: string; increment?: number }> | undefined;\n  public statusBarItem: vscode.StatusBarItem | undefined = undefined;\n\n  private agents: Map<string, AgentInstance> = new Map();\n  private supervisorAgent: Agent | undefined;\n  private conversationLog: { agent: string; message: string; timestamp: Date }[] = [];\n\n  // Predefined agent roles\n  private readonly predefinedRoles: AgentRole[] = [\n    {\n      id: 'supervisor',\n      name: 'Supervisor',\n      description: 'Coordinates the team and ensures the task is completed effectively',\n      systemPrompt: promptManager.renderPrompt('agent.multiAgent.supervisor', {})\n    },\n    {\n      id: 'architect',\n      name: 'Architect',\n      description: 'Designs the overall structure and architecture of the solution',\n      systemPrompt: promptManager.renderPrompt('agent.multiAgent.architect', {})\n    },\n    {\n      id: 'developer',\n      name: 'Developer',\n      description: 'Implements the solution based on the architect\\'s design',\n      systemPrompt: promptManager.renderPrompt('agent.multiAgent.developer', {})\n    },\n    {\n      id: 'tester',\n      name: 'Tester',\n      description: 'Tests the solution to ensure it meets requirements and is free of bugs',\n      systemPrompt: promptManager.renderPrompt('agent.multiAgent.tester', {})\n    },\n    {\n      id: 'reviewer',\n      name: 'Reviewer',\n      description: 'Reviews code and provides feedback to improve quality',\n      systemPrompt: promptManager.renderPrompt('agent.multiAgent.reviewer', {})\n    }\n  ];\n\n  /**\n     * Initialize the Multi-Agent mode\n     */\n  async initialize(context: vscode.ExtensionContext): Promise<void> {\n\n\n    // Create status bar item\n    this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);\n    this.statusBarItem.text = '$(organization) Multi-Agent: Idle';\n    this.statusBarItem.tooltip = 'Codessa Multi-Agent Mode';\n    this.statusBarItem.command = 'codessa.toggleMultiAgentMode';\n    context.subscriptions.push(this.statusBarItem);\n  }\n\n  /**\n     * Process a user message in Multi-Agent mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    contextSource: ContextSource\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Multi-Agent mode: ${message}`);\n\n      // If agents are not set up, create them\n      if (this.agents.size === 0) {\n        await this.setupAgents(agent);\n      }\n\n      // Start the multi-agent system\n      await this.startMultiAgentSystem(message, contextSource);\n\n      return 'Multi-Agent system started. The team will work on this task collaboratively and report back when finished.';\n    } catch (error) {\n      Logger.instance.error('Error processing message in Multi-Agent mode:', error);\n      return `Error starting multi-agent system: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Set up agents for the multi-agent system\n     */\n  private async setupAgents(baseAgent: Agent): Promise<void> {\n    // Clear existing agents\n    this.agents.clear();\n\n    // Create instances for each role\n    for (const role of this.predefinedRoles) {\n      // Clone the base agent\n      const agentInstance: AgentInstance = {\n        id: `agent-${role.id}`,\n        role,\n        agent: baseAgent, // In a real implementation, you would create separate agent instances\n        messages: []\n      };\n\n      this.agents.set(role.id, agentInstance);\n    }\n\n    // Set supervisor agent\n    this.supervisorAgent = baseAgent;\n  }\n\n  /**\n     * Start the multi-agent system\n     */\n  private async startMultiAgentSystem(\n    task: string,\n    contextSource: ContextSource\n  ): Promise<void> {\n    if (this.isRunning) {\n      throw new Error('Multi-Agent system is already running. Please wait for it to complete or cancel it.');\n    }\n\n    this.isRunning = true;\n    this.cancelTokenSource = new vscode.CancellationTokenSource();\n    const token: AgentContext = { cancellationToken: this.cancelTokenSource.token };\n\n    // Update status bar\n    if (this.statusBarItem) {\n      this.statusBarItem.text = '$(organization) Multi-Agent: Running';\n      this.statusBarItem.show();\n    }\n\n    // Start progress indicator\n    vscode.window.withProgress(\n      {\n        location: vscode.ProgressLocation.Notification,\n        title: 'Codessa Multi-Agent System',\n        cancellable: true\n      },\n      async (progress, progressToken) => {\n        this.progressReporter = progress;\n\n        // Handle cancellation\n        progressToken.onCancellationRequested(() => {\n          this.cancelTokenSource?.cancel();\n          this.stopMultiAgentSystem('User cancelled the operation');\n        });\n\n        progress.report({ message: 'Starting multi-agent system...' });\n\n        try {\n          // Get context content\n          const contextContent = await contextManager.getContextContent(contextSource);\n\n          // Add memory context if available\n          let memoryContext = '';\n          try {\n            if (this.supervisorAgent) {\n              const agentMemory = this.supervisorAgent.getMemory();\n              if (agentMemory) {\n                const relevantMemories = await agentMemory.getRelevantMemories(task);\n                if (relevantMemories && relevantMemories.length > 0) {\n                  memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n                  Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to multi-agent context`);\n                }\n              }\n            }\n          } catch (memoryError) {\n            Logger.instance.warn('Failed to retrieve memory context for multi-agent:', memoryError);\n            // Continue without memory context\n          }\n\n          // Clear conversation log\n          this.conversationLog = [];\n\n          // Initialize the task with the supervisor\n          await this.runSupervisorPhase(task, contextContent, memoryContext, progress, token);\n\n          // Complete the task\n          this.stopMultiAgentSystem('Task completed successfully');\n\n          // Show completion notification\n          vscode.window.showInformationMessage('Multi-Agent system has completed the task successfully.');\n\n          // Create a report document\n          await this.createReport();\n        } catch (error) {\n          Logger.instance.error('Error in multi-agent execution:', error);\n          this.stopMultiAgentSystem(`Error: ${error instanceof Error ? error.message : String(error)}`);\n\n          // Show error notification\n          vscode.window.showErrorMessage(`Multi-Agent system encountered an error: ${error instanceof Error ? error.message : String(error)}`);\n        }\n      }\n    );\n  }\n\n  /**\n     * Run the supervisor phase of the multi-agent system\n     */\n  private async runSupervisorPhase(\n    task: string,\n    contextContent: string,\n    memoryContext: string,\n    progress: vscode.Progress<{ message?: string; increment?: number }>,\n    token: AgentContext\n  ): Promise<void> {\n    if (!this.supervisorAgent) {\n      throw new Error('Supervisor agent not initialized');\n    }\n\n    // Maximum number of iterations\n    const MAX_ITERATIONS = 10;\n    let currentIteration = 0;\n\n    // Initial prompt for the supervisor\n    const supervisorPrompt = `\nYou are the supervisor of a multi-agent team working on the following task:\n\n${task}\n\nHere is the context you need:\n\n${contextContent}\n\n${memoryContext}\n\nYour team consists of the following agents:\n${Array.from(this.agents.values()).map(agent => `- ${agent.role.name}: ${agent.role.description}`).join('\\n')}\n\nAs the supervisor, you need to:\n1. Analyze the task and break it down into subtasks\n2. Assign each subtask to the appropriate team member\n3. Coordinate the work of the team\n4. Integrate the results into a final solution\n\nPlease start by analyzing the task and creating a plan.\n`;\n\n    // Log supervisor prompt\n    this.logMessage('supervisor', 'system', supervisorPrompt);\n\n    // Generate initial plan\n    progress.report({ message: 'Supervisor is analyzing the task and creating a plan...' });\n\n    const planResponse = await this.supervisorAgent.generate(\n      supervisorPrompt,\n      this.getLLMParams(),\n      token\n    );\n\n    // Log supervisor response\n    this.logMessage('supervisor', 'assistant', planResponse);\n\n    // Execute the multi-agent workflow\n    while (currentIteration < MAX_ITERATIONS && !token.cancellationToken?.isCancellationRequested) {\n      currentIteration++;\n\n      // Report progress\n      progress.report({\n        message: `Iteration ${currentIteration}: Executing multi-agent workflow...`,\n        increment: 100 / MAX_ITERATIONS\n      });\n\n      // For each agent (except supervisor), generate a response\n      for (const [roleId, agentInstance] of this.agents.entries()) {\n        if (roleId === 'supervisor') continue;\n\n        // Generate prompt for this agent\n        const agentPrompt = `\nYou are the ${agentInstance.role.name} in a multi-agent team working on the following task:\n\n${task}\n\nThe supervisor has created the following plan:\n\n${planResponse}\n\nYour specific role is: ${agentInstance.role.description}\n\nBased on the plan, what actions will you take for this iteration?\n`;\n\n        // Log agent prompt\n        this.logMessage(roleId, 'system', agentPrompt);\n\n        // Generate agent response\n        progress.report({ message: `${agentInstance.role.name} is working...` });\n\n        const agentResponse = await agentInstance.agent.generate(\n          agentPrompt,\n          this.getLLMParams(),\n          token\n        );\n\n        // Log agent response\n        this.logMessage(roleId, 'assistant', agentResponse);\n\n        // Small delay to prevent rate limiting\n        await new Promise(resolve => setTimeout(resolve, 1000));\n      }\n\n      // Generate supervisor coordination message\n      const coordinationPrompt = `\nYou are the supervisor of the multi-agent team. Here's the current state of the project:\n\nTask: ${task}\n\nYour initial plan:\n${planResponse}\n\nTeam member updates:\n${Array.from(this.agents.entries())\n          .filter(([id]) => id !== 'supervisor')\n          .map(([id, instance]) => {\n            const lastMessage = this.getLastMessageForAgent(id);\n            return `${instance.role.name}: ${lastMessage || 'No update yet'}`;\n          })\n          .join('\\n\\n')}\n\nBased on these updates, provide coordination and guidance for the next iteration.\nIf the task is complete, indicate that clearly.\n`;\n\n      // Log coordination prompt\n      this.logMessage('supervisor', 'system', coordinationPrompt);\n\n      // Generate supervisor response\n      progress.report({ message: 'Supervisor is coordinating the team...' });\n\n      const coordinationResponse = await this.supervisorAgent.generate(\n        coordinationPrompt,\n        this.getLLMParams(),\n        token\n      );\n\n      // Log supervisor response\n      this.logMessage('supervisor', 'assistant', coordinationResponse);\n\n      // Check if task is complete\n      if (coordinationResponse && (\n        coordinationResponse.toLowerCase().includes('task complete') ||\n        coordinationResponse.toLowerCase().includes('task is complete')\n      )) {\n        break;\n      }\n\n      // Small delay to prevent rate limiting\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    }\n\n    // Final summary from supervisor\n    const summaryPrompt = `\nYou are the supervisor of the multi-agent team. The task is now complete.\n\nTask: ${task}\n\nPlease provide a comprehensive summary of what the team has accomplished, including:\n1. The original task and objectives\n2. The approach taken by the team\n3. The contributions of each team member\n4. The final solution or outcome\n5. Any challenges faced and how they were overcome\n6. Recommendations for future work or improvements\n\nThis summary will be included in the final report.\n`;\n\n    // Log summary prompt\n    this.logMessage('supervisor', 'system', summaryPrompt);\n\n    // Generate summary\n    progress.report({ message: 'Generating final summary...' });\n\n    const summaryResponse = await this.supervisorAgent.generate(\n      summaryPrompt,\n      this.getLLMParams(),\n      token\n    );\n\n    // Log summary response\n    this.logMessage('supervisor', 'assistant', summaryResponse);\n  }\n\n  /**\n     * Stop the multi-agent system\n     */\n  private stopMultiAgentSystem(reason: string): void {\n    this.isRunning = false;\n    this.cancelTokenSource?.dispose();\n    this.cancelTokenSource = undefined;\n\n    // Update status bar\n    if (this.statusBarItem) {\n      this.statusBarItem.text = '$(organization) Multi-Agent: Idle';\n    }\n\n    // Log reason\n    Logger.instance.info(`Multi-Agent system stopped: ${reason}`);\n  }\n\n  /**\n     * Log a message in the conversation log\n     */\n  private logMessage(agentId: string, role: string, content: string): void {\n    this.conversationLog.push({\n      agent: agentId,\n      message: `[${role}] ${content}`,\n      timestamp: new Date()\n    });\n  }\n\n  /**\n     * Get the last message for a specific agent\n     */\n  private getLastMessageForAgent(agentId: string): string | undefined {\n    // Filter messages for this agent and get the last one\n    const agentMessages = this.conversationLog\n      .filter(log => log.agent === agentId && log.message.startsWith('[assistant]'));\n\n    if (agentMessages.length === 0) {\n      return undefined;\n    }\n\n    const lastMessage = agentMessages[agentMessages.length - 1];\n    return lastMessage.message.replace('[assistant] ', '');\n  }\n\n  /**\n     * Create a report document\n     */\n  private async createReport(): Promise<void> {\n    // Generate report content\n    let reportContent = '# Multi-Agent Task Report\\n\\n';\n\n    // Add timestamp\n    reportContent += `Generated on: ${new Date().toLocaleString()}\\n\\n`;\n\n    // Add team members\n    reportContent += '## Team Members\\n\\n';\n    for (const [, agentInstance] of this.agents.entries()) {\n      reportContent += `### ${agentInstance.role.name}\\n`;\n      reportContent += `${agentInstance.role.description}\\n\\n`;\n    }\n\n    // Add conversation log\n    reportContent += '## Conversation Log\\n\\n';\n    for (const log of this.conversationLog) {\n      if (log.message.startsWith('[assistant]')) {\n        const agentName = this.agents.get(log.agent)?.role.name || log.agent;\n        reportContent += `### ${agentName} (${log.timestamp.toLocaleTimeString()})\\n\\n`;\n        reportContent += `${log.message.replace('[assistant] ', '')}\\n\\n`;\n      }\n    }\n\n    // Create a document\n    const document = await vscode.workspace.openTextDocument({\n      content: reportContent,\n      language: 'markdown'\n    });\n\n    await vscode.window.showTextDocument(document);\n  }\n\n  /**\n     * Get LLM parameters specific to Multi-Agent mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.5, // Balanced temperature for creativity and precision\n      maxTokens: 2000,   // Longer responses for detailed reasoning\n      mode: 'task'\n    };\n  }\n\n  /**\n     * Get the system prompt for Multi-Agent mode\n     */\n  async getSystemPrompt(): Promise<string> {\n    return promptManager.renderPrompt('mode.multiAgent', {});\n  }\n\n  /**\n     * Get UI components specific to Multi-Agent mode\n     */\n  getUIComponents(): {\n    controlPanel?: string;\n    contextPanel?: string;\n    messageInput?: string;\n  } {\n    return {\n      controlPanel: `\n<div class=\"multi-agent-control-panel\">\n    <div class=\"multi-agent-status\">\n        <span id=\"multi-agent-status-indicator\" class=\"status-indicator\"></span>\n        <span id=\"multi-agent-status-text\">Idle</span>\n    </div>\n    <div class=\"multi-agent-controls\">\n        <button id=\"btn-start-multi-agent\" title=\"Start Multi-Agent System\"><i class=\"codicon codicon-play\"></i> Start</button>\n        <button id=\"btn-stop-multi-agent\" title=\"Stop Multi-Agent System\" disabled><i class=\"codicon codicon-stop\"></i> Stop</button>\n    </div>\n    <div class=\"multi-agent-team\">\n        <h4>Team Configuration</h4>\n        <div id=\"multi-agent-team-members\" class=\"team-members-list\">\n            <!-- Team members will be added here dynamically -->\n        </div>\n        <button id=\"btn-add-agent\" title=\"Add Agent\"><i class=\"codicon codicon-add\"></i> Add Agent</button>\n    </div>\n</div>\n`,\n      contextPanel: `\n<div class=\"context-panel\">\n    <div class=\"context-header\">\n        <h3>Multi-Agent Context</h3>\n        <div class=\"context-controls\">\n            <button id=\"btn-refresh-context\" title=\"Refresh Context\"><i class=\"codicon codicon-refresh\"></i></button>\n            <button id=\"btn-select-files\" title=\"Select Files\"><i class=\"codicon codicon-file-code\"></i></button>\n            <button id=\"btn-select-folders\" title=\"Select Folders\"><i class=\"codicon codicon-folder\"></i></button>\n        </div>\n    </div>\n    <div class=\"context-type\">\n        <select id=\"context-type-selector\">\n            <option value=\"entire_codebase\">Entire Codebase</option>\n            <option value=\"selected_files\">Selected Files</option>\n            <option value=\"current_file\">Current File</option>\n            <option value=\"custom\">Custom</option>\n        </select>\n    </div>\n    <div id=\"context-files-list\" class=\"context-files-list\"></div>\n</div>\n`,\n      messageInput: `\n<div class=\"message-input-container\">\n    <textarea id=\"message-input\" placeholder=\"Describe the task for the multi-agent team to complete...\"></textarea>\n    <button id=\"btn-send\" title=\"Send\"><i class=\"codicon codicon-send\"></i></button>\n</div>\n`\n    };\n  }\n\n  /**\n     * Handle mode-specific commands\n     */\n  async handleCommand(command: string, args: unknown[]): Promise<void> {\n    switch (command) {\n      case 'startMultiAgentSystem':\n        if (!this.isRunning && args.length >= 3) {\n          const [task, agent, contextSource] = args;\n          await this.setupAgents(agent as Agent);\n          await this.startMultiAgentSystem(task as string, contextSource as ContextSource);\n        }\n        break;\n\n      case 'stopMultiAgentSystem':\n        if (this.isRunning) {\n          this.cancelTokenSource?.cancel();\n          this.stopMultiAgentSystem('User manually stopped the multi-agent system');\n        }\n        break;\n\n      case 'addAgentRole':\n        if (args.length >= 1) {\n          const role = args[0] as AgentRole;\n          this.predefinedRoles.push(role);\n        }\n        break;\n    }\n  }\n}\n"]}