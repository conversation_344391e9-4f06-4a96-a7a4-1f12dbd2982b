{"version": 3, "file": "lmstudioProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/lmstudioProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAC/B,uDAAoD;AAIpD,yCAAsC;AACtC,mEAAwF;AAExF;;GAEG;AACH,MAAa,gBAAiB,SAAQ,iCAAe;IAC1C,UAAU,GAAG,UAAU,CAAC;IACxB,WAAW,GAAG,WAAW,CAAC;IAC1B,WAAW,GAAG,kDAAkD,CAAC;IACjE,OAAO,GAAG,qBAAqB,CAAC;IAChC,cAAc,GAAG,KAAK,CAAC;IACvB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,0BAA0B,CAAC;IAC7C,YAAY,GAAG,aAAa,CAAC;IAE9B,MAAM,GAAyB,IAAI,CAAC;IACpC,OAAO,CAAS;IAExB,YAAY,OAAgC;QAC1C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAE/D,6DAA6D;QAC7D,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,IAAI,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1C,eAAM,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;YACnE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC;YAC3D,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;YAE/D,mCAAmC;YACnC,IAAI,CAAC,MAAM,GAAG,EAAmB,CAAC,CAAC,2CAA2C;YAE9E,eAAM,CAAC,IAAI,CAAC,8CAA8C,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAE1E,gFAAgF;YAChF;;;kBAGM;QACR,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,YAAY;QACjB,yCAAyC;QACzC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,eAAM,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,kBAA6C,EAC7C,MAA2B;QAE3B,gEAAgE;QAChE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,eAAM,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC1E,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE;oBACL,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,CAAC;oBACnB,WAAW,EAAE,CAAC;iBACf;aACF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE;oBACL,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,CAAC;oBACnB,WAAW,EAAE,CAAC;iBACf;aACF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,mBAAmB;YACnB,MAAM,QAAQ,GAAmF,EAAE,CAAC;YAEpG,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,MAAM,CAAC,YAAY;iBACpB,CAAC,CAAC;YACd,CAAC;YAED,mCAAmC;YACnC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;iBACd,CAAC,CAAC;YACd,CAAC;YAED,wCAAwC;YACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CACnC,KAAK,IAAI,EAAE;gBACT,IAAI,CAAC;oBACH,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,mBAAmB,EAAE;wBAC1D,KAAK,EAAE,OAAO;wBACd,QAAQ,EAAE,QAAQ;wBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;wBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;wBACpC,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,EAAE;qBACjC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,mCAAmC;gBAC7D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,0CAA0C;oBAC1C,MAAM,UAAU,GAAG,KAAmB,CAAC;oBACvC,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;wBACvC,eAAM,CAAC,KAAK,CAAC,sCAAsC,IAAI,CAAC,OAAO,yBAAyB,CAAC,CAAC;wBAC1F,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;oBAC7F,CAAC;yBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;wBACpF,eAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,OAAO,aAAa,CAAC,CAAC;wBACtE,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;oBAC9F,CAAC;oBACD,MAAM,KAAK,CAAC,CAAC,+BAA+B;gBAC9C,CAAC;YACH,CAAC,EACD,CAAC,EAAE,qDAAqD;YACxD,IAAI,CAAC,0BAA0B;aAChC,CAAC;YAEF,+BAA+B;YAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;YAEjE,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,MAAM;gBAC/D,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI;oBAC5B,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,CAAC;oBACnB,WAAW,EAAE,CAAC;iBACf;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,KAAmB,CAAC;YACvC,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,UAAU,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,CAAC;YAEzE,kEAAkE;YAClE,MAAM,QAAQ,GAAG,oBAAoB,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,EAAE,CAAC;YAC1F,IAAI,CAAC,IAAA,mCAAmB,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACnC,IAAA,gCAAgB,EACd,QAAQ,EACR,eAAe,CAChB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACd,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,2BAA2B,CAAC,CAAC;oBAC/F,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,qDAAqD,QAAQ,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE;oBACL,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,CAAC;oBACnB,WAAW,EAAE,CAAC;iBACf;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,CAAC,cAAc,CAC1B,MAAyB,EACzB,iBAA4C;QAE5C,gEAAgE;QAChE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,eAAM,CAAC,KAAK,CAAC,kEAAkE,CAAC,CAAC;YACjF,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,iCAAiC;YACjC,MAAM,QAAQ,GAAmF,EAAE,CAAC;YAEpG,iCAAiC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,MAAM,CAAC,YAAY;iBACpB,CAAC,CAAC;YACd,CAAC;YAED,mCAAmC;YACnC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;YAED,yBAAyB;YACzB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;aACd,CAAC,CAAC;YAEZ,kDAAkD;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CACnC,KAAK,IAAI,EAAE;gBACT,IAAI,CAAC;oBACH,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,mBAAmB,EAAE;wBAC1D,KAAK,EAAE,OAAO;wBACd,QAAQ,EAAE,QAAQ;wBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;wBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;wBACpC,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,EAAE;wBAChC,MAAM,EAAE,IAAI;qBACb,EAAE;wBACD,YAAY,EAAE,QAAQ;wBACtB,OAAO,EAAE,KAAK,CAAC,mCAAmC;qBACnD,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,0CAA0C;oBAC1C,MAAM,UAAU,GAAG,KAAmB,CAAC;oBACvC,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;wBACvC,eAAM,CAAC,KAAK,CAAC,sCAAsC,IAAI,CAAC,OAAO,yBAAyB,CAAC,CAAC;wBAC1F,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;oBAC7F,CAAC;yBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;wBACpF,eAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,OAAO,aAAa,CAAC,CAAC;wBACtE,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;oBAC9F,CAAC;oBACD,MAAM,KAAK,CAAC,CAAC,+BAA+B;gBAC9C,CAAC;YACH,CAAC,EACD,CAAC,EAAE,oDAAoD;YACvD,IAAI,CAAC,0BAA0B;aAChC,CAAC;YAEF,iCAAiC;YACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;oBAC/C,MAAM;gBACR,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAE3D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,+BAA+B;wBAC/B,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;4BAC5B,SAAS;wBACX,CAAC;wBAED,sBAAsB;wBACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;wBAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;wBAElC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;4BACpD,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;wBACtC,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,eAAM,CAAC,KAAK,CAAC,wCAAwC,YAAY,EAAE,CAAC,CAAC;YAErE,mDAAmD;YACnD,uEAAuE;YACvE,eAAM,CAAC,KAAK,CAAC,sDAAsD,YAAY,EAAE,CAAC,CAAC;YAEnF,MAAM,IAAI,KAAK,CAAC,8BAA8B,YAAY,mEAAmE,CAAC,CAAC;QACjI,CAAC;IACH,CAAC;IAED;;;;;;SAMK;IACG,KAAK,CAAC,SAAS,CACrB,SAA2B,EAC3B,UAAU,GAAG,CAAC,EACd,YAAY,GAAG,GAAG;QAElB,IAAI,SAAc,CAAC;QACnB,IAAI,KAAK,GAAG,YAAY,CAAC;QAEzB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,OAAO,MAAM,SAAS,EAAE,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,CAAC;gBAClB,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;oBACzB,eAAM,CAAC,KAAK,CAAC,iBAAiB,OAAO,GAAG,CAAC,IAAI,UAAU,UAAU,KAAK,IAAI,CAAC,CAAC;oBAC5E,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;oBACzD,KAAK,IAAI,CAAC,CAAC,CAAC,sBAAsB;gBACpC,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,SAAS,CAAC;IAClB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,gEAAgE;QAChE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,eAAM,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAC3E,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,eAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACrE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,uCAAuC,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC;YAE3E,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CACnC,KAAK,IAAI,EAAE;gBACT,IAAI,CAAC;oBACH,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBACtE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,0CAA0C;oBAC1C,MAAM,UAAU,GAAG,KAAmB,CAAC;oBACvC,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;wBACvC,eAAM,CAAC,KAAK,CAAC,sCAAsC,IAAI,CAAC,OAAO,yBAAyB,CAAC,CAAC;wBAC1F,MAAM,IAAI,KAAK,CAAC,6EAA6E,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;oBAChH,CAAC;yBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;wBACpF,eAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,OAAO,aAAa,CAAC,CAAC;wBACtE,MAAM,IAAI,KAAK,CAAC,8EAA8E,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;oBACjH,CAAC;oBACD,MAAM,KAAK,CAAC,CAAC,+BAA+B;gBAC9C,CAAC;YACH,CAAC,CACF,CAAC;YAEF,oDAAoD;YACpD,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC;YACzC,eAAM,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;YAEvE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;gBAC7B,EAAE,EAAE,CAAC,CAAC,EAAE;gBACR,IAAI,EAAE,CAAC,CAAC,EAAE;gBACV,WAAW,EAAE,CAAC,CAAC,WAAW,IAAI,uBAAuB;gBACrD,aAAa,EAAE,CAAC,CAAC,cAAc,IAAI,IAAI;aACxC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAe,EAAE,CAAe,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,0CAA0C;YAC1C,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAE9E,sDAAsD;YACtD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEhF,IAAI,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1C,eAAM,CAAC,IAAI,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,sDAAsD,YAAY,EAAE,CAAC,CAAC;YACrF,CAAC;YAED,8DAA8D;YAC9D,MAAM,QAAQ,GAAG,qCAAqC,YAAY,EAAE,CAAC;YACrE,IAAI,CAAC,IAAA,mCAAmB,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACnC,+EAA+E;gBAC/E,IAAI,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC1C,eAAM,CAAC,IAAI,CAAC,+BAA+B,YAAY,EAAE,CAAC,CAAC;gBAC7D,CAAC;YACH,CAAC;YAED,8EAA8E;YAC9E,OAAO,CAAC;oBACN,EAAE,EAAE,aAAa;oBACjB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,yCAAyC;oBACtD,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,gEAAgE;QAChE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,eAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;YACzE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6CAA6C;aACvD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,kCAAkC;aAC5C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,mEAAmE;YACnE,MAAM,IAAI,CAAC,SAAS,CAClB,KAAK,IAAI,EAAE;gBACT,IAAI,CAAC;oBACH,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBACtE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,0CAA0C;oBAC1C,MAAM,UAAU,GAAG,KAAmB,CAAC;oBACvC,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;wBACvC,MAAM,IAAI,KAAK,CAAC,gEAAgE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;oBACnG,CAAC;yBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;wBACpF,MAAM,IAAI,KAAK,CAAC,iEAAiE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;oBACpG,CAAC;oBACD,MAAM,KAAK,CAAC,CAAC,+BAA+B;gBAC9C,CAAC;YACH,CAAC,EACD,CAAC,EAAE,sCAAsC;YACzC,GAAG,CAAC,yBAAyB;aAC9B,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,iDAAiD,IAAI,CAAC,OAAO,GAAG;aAC1E,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4EAA4E,CAAC,CAAC;YAE3F,mDAAmD;YACnD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,SAAS,CAClB,KAAK,IAAI,EAAE;oBACT,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,mBAAmB,EAAE;wBAC1D,KAAK,EAAE,OAAO;wBACd,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;wBAC9C,UAAU,EAAE,CAAC;qBACd,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBACxB,CAAC,EACD,CAAC,EAAE,sCAAsC;gBACzC,GAAG,CAAC,yBAAyB;iBAC9B,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,iDAAiD,IAAI,CAAC,OAAO,0BAA0B;iBACjG,CAAC;YACJ,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,6CAA6C;gBAC7C,MAAM,aAAa,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC/E,MAAM,cAAc,GAAG,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAE5F,MAAM,YAAY,GAAG,4CAA4C,IAAI,CAAC,OAAO,KAAK;oBACtE,4BAA4B,aAAa,IAAI;oBAC7C,sCAAsC,cAAc,EAAE,CAAC;gBAEnE,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,YAAY,CAAC,CAAC;gBAEhE,uEAAuE;gBACvE,6EAA6E;gBAC7E,IAAI,OAAO,KAAK,cAAc,EAAE,CAAC;oBAC/B,6EAA6E;oBAC7E,MAAM,QAAQ,GAAG,gDAAgD,CAAC;oBAElE,IAAI,CAAC,IAAA,mCAAmB,EAAC,QAAQ,CAAC,EAAE,CAAC;wBACnC,IAAA,gCAAgB,EACd,QAAQ,EACR,iBAAiB,EAAE,eAAe,CACnC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;4BACjB,IAAI,SAAS,KAAK,iBAAiB,EAAE,CAAC;gCACpC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;4BACpE,CAAC;iCAAM,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;gCACzC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;4BACjE,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,gFAAgF,IAAI,CAAC,OAAO,+BAA+B;iBACrI,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,OAAY;QACpC,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,sEAAsE;gBACnF,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,kDAAkD;gBAC/D,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AApkBD,4CAokBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport type { AxiosInstance, AxiosError } from 'axios';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\nimport { BaseLLMProvider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { logger } from '../../logger';\nimport { showErrorMessage, shouldSuppressError } from '../../ui/feedback/notifications';\n\n/**\n * Provider for LM Studio local LLM server\n */\nexport class LMStudioProvider extends BaseLLMProvider {\n  readonly providerId = 'lmstudio';\n  readonly displayName = 'LM Studio';\n  readonly description = 'Run large language models locally with LM Studio';\n  readonly website = 'https://lmstudio.ai';\n  readonly requiresApiKey = false;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'http://localhost:1234/v1';\n  readonly defaultModel = 'local-model';\n\n  private client: AxiosInstance | null = null;\n  private baseUrl: string;\n\n  constructor(context: vscode.ExtensionContext) {\n    super(context);\n    this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    // Check if LM Studio provider is enabled before initializing\n    const config = vscode.workspace.getConfiguration('codessa.llm');\n    const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n\n    if (enabledProviders.includes('lmstudio')) {\n      logger.debug('LM Studio provider is enabled, initializing client');\n      this.initializeClient();\n    } else {\n      logger.debug('LM Studio provider is disabled, skipping client initialization');\n    }\n  }\n\n  /**\n     * Initialize the Axios client for API requests\n     */\n  private async checkConnection(): Promise<void> {\n    try {\n      const response = await axios.get(`${this.baseUrl}/health`);\n      if (response.status === 200) {\n        logger.info('LM Studio connection successful');\n      }\n    } catch (error: unknown) {\n      throw new Error(`LM Studio connection failed: ${error}`);\n    }\n  }\n\n  private async initializeClient(): Promise<void> {\n    try {\n      this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n            \n      // Store the base URL for later use\n      this.client = {} as AxiosInstance; // Dummy instance, we'll use axios directly\n            \n      logger.info(`LM Studio client initialized for base URL: ${this.baseUrl}`);\n            \n      // Try a simple health check, but don't show notifications during initialization\n      /* await this.checkConnection().catch((error: Error) => {\n        logger.debug('LM Studio connection check failed during initialization:', error);\n        // Don't show notifications during initialization - let the error suppression handle it\n      }); */\n    } catch (error: unknown) {\n      logger.debug('Failed to initialize LM Studio client:', error);\n      this.client = null;\n    }\n  }\n\n  /**\n     * Check if the provider is configured\n     */\n  public isConfigured(): boolean {\n    // First check if the provider is enabled\n    const config = vscode.workspace.getConfiguration('codessa.llm');\n    const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n\n    if (!enabledProviders.includes('lmstudio')) {\n      logger.debug('LM Studio provider is disabled in settings');\n      return false;\n    }\n\n    return !!this.baseUrl;\n  }\n\n  /**\n     * Generate text using LM Studio\n     */\n  public async generate(\n    params: LLMGenerateParams,\n    _cancellationToken?: vscode.CancellationToken,\n    _tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    // Check if provider is enabled before attempting any operations\n    const config = vscode.workspace.getConfiguration('codessa.llm');\n    const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n\n    if (!enabledProviders.includes('lmstudio')) {\n      logger.debug('LM Studio provider is disabled, skipping generate request');\n      return {\n        content: '',\n        usage: {\n          promptTokens: 0,\n          completionTokens: 0,\n          totalTokens: 0\n        }\n      };\n    }\n\n    if (!this.client) {\n      return {\n        content: '',\n        usage: {\n          promptTokens: 0,\n          completionTokens: 0,\n          totalTokens: 0\n        }\n      };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare messages\n      const messages: Array<{ role: string; content: string; name?: string; tool_call_id?: string }> = [];\n\n      if (params.systemPrompt) {\n        messages.push({\n          role: 'system',\n          content: params.systemPrompt\n        } as const);\n      }\n\n      // Add history messages if provided\n      if (params.history && params.history.length > 0) {\n        messages.push(...params.history);\n      } else {\n        messages.push({\n          role: 'user',\n          content: params.prompt\n        } as const);\n      }\n\n      // Make the API request with retry logic\n      const response = await this.withRetry(\n        async () => {\n          try {\n            return await axios.post(`${this.baseUrl}/chat/completions`, {\n              model: modelId,\n              messages: messages,\n              temperature: params.temperature || 0.7,\n              max_tokens: params.maxTokens || 1024,\n              stop: params.stopSequences || []\n            }, { timeout: 30000 }); // 30 second timeout for generation\n          } catch (error) {\n            // Enhance error logging with more details\n            const axiosError = error as AxiosError;\n            if (axiosError.code === 'ECONNREFUSED') {\n              logger.error(`Connection refused to LM Studio at ${this.baseUrl}. Is LM Studio running?`);\n              throw new Error('Connection refused to LM Studio. Please make sure LM Studio is running.');\n            } else if (axiosError.code === 'ETIMEDOUT' || axiosError.code === 'ESOCKETTIMEDOUT') {\n              logger.error(`Connection to LM Studio at ${this.baseUrl} timed out.`);\n              throw new Error('Connection to LM Studio timed out. Please check if LM Studio is running.');\n            }\n            throw error; // Re-throw for retry mechanism\n          }\n        },\n        1, // Only retry once for generation to avoid long waits\n        1000 // Start with 1000ms delay\n      );\n\n      // Extract the response content\n      const content = response.data.choices[0]?.message?.content || '';\n\n      return {\n        content,\n        finishReason: response.data.choices[0]?.finish_reason || 'stop',\n        usage: response.data.usage || {\n          promptTokens: 0,\n          completionTokens: 0,\n          totalTokens: 0\n        }\n      };\n    } catch (error) {\n      const axiosError = error as AxiosError;\n      logger.error('LM Studio API error:', axiosError.response?.data || error);\n            \n      // Use error suppression instead of showing notifications directly\n      const errorMsg = `LM Studio error: ${axiosError.response?.data?.error?.message || error}`;\n      if (!shouldSuppressError(errorMsg)) {\n        showErrorMessage(\n          errorMsg,\n          'Open Settings'\n        ).then(action => {\n          if (action === 'Open Settings') {\n            vscode.commands.executeCommand('workbench.action.openSettings', '@ext:TekNerds-ITS.codessa');\n          }\n        });\n      } else {\n        logger.debug(`Suppressed LM Studio generate error notification: ${errorMsg}`);\n      }\n\n      return {\n        content: '',\n        usage: {\n          promptTokens: 0,\n          completionTokens: 0,\n          totalTokens: 0\n        }\n      };\n    }\n  }\n\n  /**\n     * Stream generate text using LM Studio\n     */\n  public async *streamGenerate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken\n  ): AsyncGenerator<string, void, unknown> {\n    // Check if provider is enabled before attempting any operations\n    const config = vscode.workspace.getConfiguration('codessa.llm');\n    const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n\n    if (!enabledProviders.includes('lmstudio')) {\n      logger.debug('LM Studio provider is disabled, skipping stream generate request');\n      return;\n    }\n\n    if (!this.baseUrl) {\n      throw new Error('LM Studio client not initialized');\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare messages for LM Studio\n      const messages: Array<{ role: string; content: string; name?: string; tool_call_id?: string }> = [];\n\n      // Add system message if provided\n      if (params.systemPrompt) {\n        messages.push({\n          role: 'system',\n          content: params.systemPrompt\n        } as const);\n      }\n\n      // Add history messages if provided\n      if (params.history && params.history.length > 0) {\n        messages.push(...params.history);\n      }\n\n      // Add the current prompt\n      messages.push({\n        role: 'user',\n        content: params.prompt\n      } as const);\n\n      // Make the streaming API request with retry logic\n      const response = await this.withRetry(\n        async () => {\n          try {\n            return await axios.post(`${this.baseUrl}/chat/completions`, {\n              model: modelId,\n              messages: messages,\n              temperature: params.temperature || 0.7,\n              max_tokens: params.maxTokens || 1024,\n              stop: params.stopSequences || [],\n              stream: true\n            }, {\n              responseType: 'stream',\n              timeout: 30000 // 30 second timeout for generation\n            });\n          } catch (error) {\n            // Enhance error logging with more details\n            const axiosError = error as AxiosError;\n            if (axiosError.code === 'ECONNREFUSED') {\n              logger.error(`Connection refused to LM Studio at ${this.baseUrl}. Is LM Studio running?`);\n              throw new Error('Connection refused to LM Studio. Please make sure LM Studio is running.');\n            } else if (axiosError.code === 'ETIMEDOUT' || axiosError.code === 'ESOCKETTIMEDOUT') {\n              logger.error(`Connection to LM Studio at ${this.baseUrl} timed out.`);\n              throw new Error('Connection to LM Studio timed out. Please check if LM Studio is running.');\n            }\n            throw error; // Re-throw for retry mechanism\n          }\n        },\n        1, // Only retry once for streaming to avoid long waits\n        1000 // Start with 1000ms delay\n      );\n\n      // Process the streaming response\n      const stream = response.data;\n\n      for await (const chunk of stream) {\n        if (cancellationToken?.isCancellationRequested) {\n          break;\n        }\n\n        try {\n          const lines = chunk.toString().split('\\n').filter(Boolean);\n\n          for (const line of lines) {\n            // Skip \"data: [DONE]\" messages\n            if (line === 'data: [DONE]') {\n              continue;\n            }\n\n            // Parse the JSON data\n            const jsonData = line.replace(/^data: /, '');\n            const data = JSON.parse(jsonData);\n\n            if (data.choices && data.choices[0]?.delta?.content) {\n              yield data.choices[0].delta.content;\n            }\n          }\n        } catch (error) {\n          logger.error('Error parsing LM Studio stream chunk:', error);\n        }\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      logger.error(`Error streaming text with LM Studio: ${errorMessage}`);\n\n      // Don't show any error notifications for LM Studio\n      // This prevents error popups for users who don't want to use LM Studio\n      logger.debug(`Suppressed LM Studio streaming error notification: ${errorMessage}`);\n\n      throw new Error(`LM Studio streaming error: ${errorMessage}. Please ensure LM Studio is running with the API server enabled.`);\n    }\n  }\n\n  /**\n     * Helper function to retry an operation with exponential backoff\n     * @param operation The operation to retry\n     * @param maxRetries Maximum number of retries\n     * @param initialDelay Initial delay in milliseconds\n     * @returns The result of the operation\n     */\n  private async withRetry<T>(\n    operation: () => Promise<T>,\n    maxRetries = 2,\n    initialDelay = 500\n  ): Promise<T> {\n    let lastError: any;\n    let delay = initialDelay;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        return await operation();\n      } catch (error) {\n        lastError = error;\n        if (attempt < maxRetries) {\n          logger.debug(`Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms`);\n          await new Promise(resolve => setTimeout(resolve, delay));\n          delay *= 2; // Exponential backoff\n        }\n      }\n    }\n\n    throw lastError;\n  }\n\n  /**\n     * List available models from LM Studio\n     */\n  public async listModels(): Promise<LLMModelInfo[]> {\n    // Check if provider is enabled before attempting any operations\n    const config = vscode.workspace.getConfiguration('codessa.llm');\n    const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n\n    if (!enabledProviders.includes('lmstudio')) {\n      logger.debug('LM Studio provider is disabled, returning empty model list');\n      return [];\n    }\n\n    if (!this.baseUrl) {\n      logger.warn('Cannot fetch LM Studio models, client not configured.');\n      return [];\n    }\n\n    try {\n      logger.debug(`Fetching LM Studio models list from ${this.baseUrl}/models`);\n\n      // Use retry logic for the API call\n      const response = await this.withRetry(\n        async () => {\n          try {\n            return await axios.get(`${this.baseUrl}/models`, { timeout: 5000 });\n          } catch (error) {\n            // Enhance error logging with more details\n            const axiosError = error as AxiosError;\n            if (axiosError.code === 'ECONNREFUSED') {\n              logger.error(`Connection refused to LM Studio at ${this.baseUrl}. Is LM Studio running?`);\n              throw new Error(`Connection refused to LM Studio. Please make sure LM Studio is running at ${this.baseUrl}.`);\n            } else if (axiosError.code === 'ETIMEDOUT' || axiosError.code === 'ESOCKETTIMEDOUT') {\n              logger.error(`Connection to LM Studio at ${this.baseUrl} timed out.`);\n              throw new Error(`Connection to LM Studio timed out. Please check if LM Studio is running at ${this.baseUrl}.`);\n            }\n            throw error; // Re-throw for retry mechanism\n          }\n        }\n      );\n\n      // LM Studio response format follows OpenAI's format\n      const models = response.data?.data || [];\n      logger.info(`Provider lmstudio has ${models.length} models available`);\n\n      return models.map((m: any) => ({\n        id: m.id,\n        name: m.id,\n        description: m.description || 'Local LM Studio model',\n        contextWindow: m.context_length || 4096\n      })).sort((a: LLMModelInfo, b: LLMModelInfo) => a.id.localeCompare(b.id));\n    } catch (error) {\n      // Provide more detailed error information\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n\n      // Check if LM Studio is enabled before logging errors\n      const config = vscode.workspace.getConfiguration('codessa.llm');\n      const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n\n      if (enabledProviders.includes('lmstudio')) {\n        logger.warn(`Failed to fetch LM Studio models: ${errorMessage}`);\n      } else {\n        logger.debug(`LM Studio models fetch failed (provider disabled): ${errorMessage}`);\n      }\n\n      // Use error suppression to check if we should show this error\n      const errorMsg = `Failed to fetch LM Studio models: ${errorMessage}`;\n      if (!shouldSuppressError(errorMsg)) {\n        // Only log as warning if error suppression is disabled and provider is enabled\n        if (enabledProviders.includes('lmstudio')) {\n          logger.warn(`LM Studio connection error: ${errorMessage}`);\n        }\n      }\n\n      // LM Studio might not support the /models endpoint, so return a default model\n      return [{\n        id: 'local-model',\n        name: 'Local Model',\n        description: 'The model currently loaded in LM Studio',\n        contextWindow: 4096\n      }];\n    }\n  }\n\n  /**\n     * Test connection to LM Studio\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    // Check if provider is enabled before attempting any operations\n    const config = vscode.workspace.getConfiguration('codessa.llm');\n    const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n\n    if (!enabledProviders.includes('lmstudio')) {\n      logger.debug('LM Studio provider is disabled, skipping connection test');\n      return {\n        success: false,\n        message: 'LM Studio provider is disabled in settings.'\n      };\n    }\n\n    if (!this.baseUrl) {\n      return {\n        success: false,\n        message: 'LM Studio client not initialized'\n      };\n    }\n\n    try {\n      // Check if we can connect to the LM Studio server with retry logic\n      await this.withRetry(\n        async () => {\n          try {\n            return await axios.get(`${this.baseUrl}/models`, { timeout: 5000 });\n          } catch (error) {\n            // Enhance error logging with more details\n            const axiosError = error as AxiosError;\n            if (axiosError.code === 'ECONNREFUSED') {\n              throw new Error(`Connection refused. Please make sure LM Studio is running at ${this.baseUrl}.`);\n            } else if (axiosError.code === 'ETIMEDOUT' || axiosError.code === 'ESOCKETTIMEDOUT') {\n              throw new Error(`Connection timed out. Please check if LM Studio is running at ${this.baseUrl}.`);\n            }\n            throw error; // Re-throw for retry mechanism\n          }\n        },\n        1, // Only retry once for test connection\n        500 // Start with 500ms delay\n      );\n\n      return {\n        success: true,\n        message: `Successfully connected to LM Studio server at ${this.baseUrl}.`\n      };\n    } catch (error) {\n      logger.debug('LM Studio /models endpoint test failed, trying chat completion as fallback');\n\n      // Try a simple chat completion request as fallback\n      try {\n        await this.withRetry(\n          async () => {\n            return await axios.post(`${this.baseUrl}/chat/completions`, {\n              model: modelId,\n              messages: [{ role: 'user', content: 'Hello' }],\n              max_tokens: 5\n            }, { timeout: 5000 });\n          },\n          1, // Only retry once for test connection\n          500 // Start with 500ms delay\n        );\n\n        return {\n          success: true,\n          message: `Successfully connected to LM Studio server at ${this.baseUrl} using chat completions.`\n        };\n      } catch (secondError) {\n        // Combine both errors for better diagnostics\n        const firstErrorMsg = error instanceof Error ? error.message : 'Unknown error';\n        const secondErrorMsg = secondError instanceof Error ? secondError.message : 'Unknown error';\n\n        const errorDetails = `Failed to connect to LM Studio server at ${this.baseUrl}:\\n` +\n                    `- Models endpoint error: ${firstErrorMsg}\\n` +\n                    `- Chat completions endpoint error: ${secondErrorMsg}`;\n\n        logger.error('LM Studio connection test failed:', errorDetails);\n\n        // Only show a notification if the user explicitly tests the connection\n        // This is different from automatic connection attempts during initialization\n        if (modelId !== 'silent-check') {\n          // Use the shouldSuppressError function to check if we should show this error\n          const errorMsg = 'Failed to connect to LM Studio. Is it running?';\n                    \n          if (!shouldSuppressError(errorMsg)) {\n            showErrorMessage(\n              errorMsg,\n              'Check LM Studio', 'Open Settings'\n            ).then(selection => {\n              if (selection === 'Check LM Studio') {\n                vscode.env.openExternal(vscode.Uri.parse('https://lmstudio.ai/'));\n              } else if (selection === 'Open Settings') {\n                vscode.commands.executeCommand('codessa.openProviderSettings');\n              }\n            });\n          } else {\n            logger.debug('Suppressed LM Studio connection test error notification');\n          }\n        }\n\n        return {\n          success: false,\n          message: `Failed to connect to LM Studio server. Please ensure LM Studio is running at ${this.baseUrl} with the API server enabled.`\n        };\n      }\n    }\n  }\n\n  /**\n     * Update the provider configuration\n     */\n  public async updateConfig(_config: any): Promise<void> {\n    await super.updateConfig(_config);\n    this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n    this.initializeClient();\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The URL of your LM Studio server (default: http://localhost:1234/v1)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use (usually \"local-model\")',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n\n\n"]}