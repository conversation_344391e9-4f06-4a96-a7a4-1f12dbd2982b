"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VSCodeClipboardService = exports.ContentSizeError = exports.ImageProcessingError = exports.UnsupportedContentError = exports.ClipboardAccessError = exports.ClipboardError = void 0;
const vscode = __importStar(require("vscode"));
const sharp_1 = __importDefault(require("sharp"));
const events_1 = require("events");
const perf_hooks_1 = require("perf_hooks");
const stream_1 = require("stream");
const streamPromises = __importStar(require("stream/promises"));
const securityUtils_1 = require("../security/securityUtils");
class ClipboardMetricsService extends events_1.EventEmitter {
    _successCount = 0;
    _errorCount = 0;
    _totalDuration = 0;
    _maxDuration = 0;
    _totalBytes = 0;
    operations = [];
    maxOperations = 1000;
    constructor() {
        super();
    }
    startOperation() {
        return perf_hooks_1.performance.now();
    }
    recordSuccess(startTime, bytes) {
        const duration = perf_hooks_1.performance.now() - startTime;
        this.recordOperation(duration, bytes, true);
    }
    recordError(startTime, error) {
        const duration = perf_hooks_1.performance.now() - startTime;
        this.recordOperation(duration, 0, false, error);
    }
    recordOperation(duration, bytes, success, error) {
        this.operations.push(duration);
        this._totalDuration += duration;
        this._maxDuration = Math.max(this._maxDuration, duration);
        if (success) {
            this._successCount++;
            this._totalBytes += bytes;
        }
        else {
            this._errorCount++;
        }
        // Maintain a rolling window of operations
        if (this.operations.length > this.maxOperations) {
            const removedDuration = this.operations.shift();
            if (removedDuration !== undefined) {
                this._totalDuration -= removedDuration;
            }
        }
        this.emit('metricsUpdated', {
            ...this.getMetrics(),
            lastError: error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : undefined
        });
    }
    getMetrics() {
        return {
            successCount: this._successCount,
            errorCount: this._errorCount,
            averageDuration: this.operations.length > 0
                ? this._totalDuration / this.operations.length
                : 0,
            maxDuration: this._maxDuration,
            bytesProcessed: this._totalBytes
        };
    }
    reset() {
        this._successCount = 0;
        this._errorCount = 0;
        this._totalDuration = 0;
        this._maxDuration = 0;
        this._totalBytes = 0;
        this.operations.length = 0;
        this.emit('metricsReset');
    }
}
/**
 * Base error class for clipboard-related errors
 */
class ClipboardError extends Error {
    cause;
    constructor(message, cause) {
        super(message);
        this.cause = cause;
        this.name = 'ClipboardError';
    }
}
exports.ClipboardError = ClipboardError;
/**
 * Error thrown when clipboard access is denied
 */
class ClipboardAccessError extends ClipboardError {
    constructor(message, cause) {
        super(message, cause);
        this.name = 'ClipboardAccessError';
    }
}
exports.ClipboardAccessError = ClipboardAccessError;
/**
 * Error thrown when unsupported content is encountered
 */
class UnsupportedContentError extends ClipboardError {
    constructor(message, cause) {
        super(message, cause);
        this.name = 'UnsupportedContentError';
    }
}
exports.UnsupportedContentError = UnsupportedContentError;
/**
 * Error thrown when image processing fails
 */
class ImageProcessingError extends ClipboardError {
    constructor(message, cause) {
        super(message, cause);
        this.name = 'ImageProcessingError';
    }
}
exports.ImageProcessingError = ImageProcessingError;
/**
 * Error thrown when content size exceeds limits
 */
class ContentSizeError extends ClipboardError {
    size;
    limit;
    constructor(message, size, limit) {
        super(`${message} (size: ${size}, limit: ${limit})`);
        this.size = size;
        this.limit = limit;
        this.name = 'ContentSizeError';
    }
}
exports.ContentSizeError = ContentSizeError;
/**
 * Default configuration values
 */
const DEFAULT_OPTIONS = {
    maxRetries: 3,
    retryDelay: 100,
    maxContentSize: 50 * 1024 * 1024, // 50MB
    chunkSize: 1024 * 1024 // 1MB
};
/**
 * A VS Code-specific implementation of the IClipboardService interface.
 * Provides clipboard functionality within the VS Code environment, including support for
 * both text and image data with automatic format conversion.
 *
 * @implements {IClipboardService}
 * @example
 * ```typescript
 * // Create a new instance with a custom logger
 * const clipboardService = new VSCodeClipboardService(customLogger);
 *
 * // Use the service
 * await clipboardService.writeText('Copied text');
 * const text = await clipboardService.readText();
 * ```
 */
class VSCodeClipboardService extends events_1.EventEmitter {
    logger;
    options;
    metricsService;
    supportedImageTypes = ['image/png', 'image/jpeg', 'image/webp'];
    isDisposed = false;
    constructor(logger, options = {}) {
        super();
        this.logger = logger || console;
        this.options = { ...DEFAULT_OPTIONS, ...options };
        this.metricsService = new ClipboardMetricsService();
        // Monitor clipboard changes by watching text document changes
        vscode.workspace.onDidChangeTextDocument(e => {
            if (e.contentChanges.length > 0) {
                this.emit('change', { type: 'text', source: e.document.uri });
            }
        });
    }
    async readText() {
        const startTime = this.metricsService.startOperation();
        try {
            const text = await this.retryOperation(async () => {
                const result = await vscode.env.clipboard.readText();
                // Size validation
                if (result.length > this.options.maxContentSize) {
                    throw new ContentSizeError('Clipboard text content exceeds size limit', result.length, this.options.maxContentSize);
                }
                // Content security checks
                if (/[<>|&;$()]/.test(result)) {
                    throw new securityUtils_1.SecurityError('Potential unsafe characters detected in clipboard content');
                }
                // Check for potentially dangerous content
                const entropy = securityUtils_1.SecurityUtils.calculateEntropy(result);
                if (entropy > 6.5) { // High entropy could indicate encrypted/compressed malicious content
                    this.logger.warn('High entropy content detected in clipboard', { entropy });
                }
                // Check for null bytes and control characters
                if (result.includes('\0')) {
                    throw new securityUtils_1.SecurityError('Null bytes detected in clipboard content');
                }
                return result;
            });
            this.metricsService.recordSuccess(startTime, Buffer.byteLength(text));
            this.logger.debug('Successfully read text from clipboard', {
                length: text.length,
                hash: securityUtils_1.SecurityUtils.generateContentHash(text)
            });
            return text;
        }
        catch (error) {
            this.metricsService.recordError(startTime, error);
            throw this.wrapError('Failed to read clipboard text', error);
        }
    }
    async readHtml() {
        const startTime = this.metricsService.startOperation();
        try {
            const text = await this.readText();
            if (!text.trim().startsWith('<')) {
                return null;
            }
            const result = {
                html: text,
                plainText: text.replace(/<[^>]+>/g, ''),
                metadata: {
                    charset: this.detectCharset(text)
                }
            };
            this.metricsService.recordSuccess(startTime, Buffer.byteLength(text));
            return result;
        }
        catch (error) {
            this.metricsService.recordError(startTime, error);
            throw this.wrapError('Failed to read HTML content', error);
        }
    }
    async writeHtml(content) {
        const startTime = this.metricsService.startOperation();
        try {
            if (Buffer.byteLength(content.html) > this.options.maxContentSize) {
                throw new ContentSizeError('HTML content exceeds size limit', Buffer.byteLength(content.html), this.options.maxContentSize);
            }
            await this.retryOperation(async () => {
                await vscode.env.clipboard.writeText(content.html);
            });
            this.metricsService.recordSuccess(startTime, Buffer.byteLength(content.html));
        }
        catch (error) {
            this.metricsService.recordError(startTime, error);
            throw this.wrapError('Failed to write HTML content', error);
        }
    }
    async readImage() {
        const startTime = this.metricsService.startOperation();
        try {
            const clipboardy = await Promise.resolve().then(() => __importStar(require('clipboardy')));
            const clipboard = clipboardy.default || clipboardy;
            const clipboardContent = await this.retryOperation(() => clipboard.read());
            const imageMatch = clipboardContent.match(/^data:(image\/[a-z]+);base64,([\s\S]+)$/i);
            if (!imageMatch) {
                this.logger.debug('No image data found in clipboard');
                return null;
            }
            const detectedMimeType = imageMatch[1];
            let base64Data = imageMatch[2];
            // Validate MIME type
            if (!securityUtils_1.SecurityUtils.isSafeMimeType(detectedMimeType)) {
                throw new securityUtils_1.SecurityError(`Unsafe MIME type detected: ${detectedMimeType}`);
            }
            // Validate base64 data
            if (!securityUtils_1.SecurityUtils.validateBase64(base64Data)) {
                throw new securityUtils_1.SecurityError('Invalid base64 data detected');
            }
            // Size validation
            const rawSize = Buffer.byteLength(base64Data, 'base64');
            if (rawSize > this.options.maxContentSize) {
                throw new ContentSizeError('Image data exceeds size limit', rawSize, this.options.maxContentSize);
            }
            // Decode and validate image data
            const imageBuffer = Buffer.from(base64Data, 'base64');
            // Check for potential shellcode
            if (securityUtils_1.SecurityUtils.detectShellcode(imageBuffer)) {
                throw new securityUtils_1.SecurityError('Potential malicious code detected in image data');
            }
            // Validate image format signature
            if (!securityUtils_1.SecurityUtils.validateImageData(imageBuffer)) {
                throw new securityUtils_1.SecurityError('Invalid image format signature');
            }
            let finalMimeType;
            let metadata = {};
            if (detectedMimeType === 'image/gif') {
                this.logger.debug('Converting GIF to PNG format');
                [finalMimeType, base64Data, metadata] = await this.convertGifToPng(base64Data);
            }
            else if (this.isSupportedImageType(detectedMimeType)) {
                finalMimeType = detectedMimeType;
                metadata = await this.getImageMetadata(imageBuffer);
            }
            else {
                throw new UnsupportedContentError(`Unsupported image type: ${detectedMimeType}. Supported types: ${this.supportedImageTypes.join(', ')}`);
            }
            // Calculate and verify entropy
            const entropy = securityUtils_1.SecurityUtils.calculateEntropy(imageBuffer);
            if (entropy > 7.99) { // Extremely high entropy might indicate steganography or encrypted content
                this.logger.warn('Suspicious image entropy detected', { entropy });
                throw new securityUtils_1.SecurityError('Suspicious image content detected');
            }
            const contentHash = securityUtils_1.SecurityUtils.generateContentHash(imageBuffer);
            const result = {
                mimeType: finalMimeType,
                base64Data,
                source: 'clipboard',
                metadata: {
                    ...metadata,
                    securityHash: contentHash
                }
            };
            this.metricsService.recordSuccess(startTime, rawSize);
            this.logger.debug(`Successfully read ${finalMimeType} image from clipboard`, {
                metadata,
                hash: contentHash,
                entropy
            });
            return result;
        }
        catch (error) {
            this.metricsService.recordError(startTime, error);
            throw this.wrapError('Failed to read image from clipboard', error);
        }
    }
    async writeImage(image) {
        const startTime = this.metricsService.startOperation();
        try {
            // Validate MIME type
            if (!this.isSupportedImageType(image.mimeType)) {
                throw new UnsupportedContentError(`Unsupported image type: ${image.mimeType}. Supported types: ${this.supportedImageTypes.join(', ')}`);
            }
            if (!securityUtils_1.SecurityUtils.isSafeMimeType(image.mimeType)) {
                throw new securityUtils_1.SecurityError(`Unsafe MIME type: ${image.mimeType}`);
            }
            // Validate base64 data
            if (!securityUtils_1.SecurityUtils.validateBase64(image.base64Data)) {
                throw new securityUtils_1.SecurityError('Invalid base64 data detected');
            }
            const imageBuffer = Buffer.from(image.base64Data, 'base64');
            // Size validation
            const rawSize = imageBuffer.length;
            if (rawSize > this.options.maxContentSize) {
                throw new ContentSizeError('Image data exceeds size limit', rawSize, this.options.maxContentSize);
            }
            // Validate image format signature
            if (!securityUtils_1.SecurityUtils.validateImageData(imageBuffer)) {
                throw new securityUtils_1.SecurityError('Invalid image format signature');
            }
            // Check for potential shellcode
            if (securityUtils_1.SecurityUtils.detectShellcode(imageBuffer)) {
                throw new securityUtils_1.SecurityError('Potential malicious code detected in image data');
            }
            // Calculate and verify entropy
            const entropy = securityUtils_1.SecurityUtils.calculateEntropy(imageBuffer);
            if (entropy > 7.99) {
                throw new securityUtils_1.SecurityError('Suspicious image content detected');
            }
            // Generate content hash for verification
            const originalHash = securityUtils_1.SecurityUtils.generateContentHash(imageBuffer);
            // Write image with content verification
            const dataUrl = `data:${image.mimeType};base64,${image.base64Data}`;
            await this.retryOperation(async () => {
                await Promise.resolve(vscode.env.clipboard.writeText(dataUrl));
                // Verify the written content
                const verificationContent = await this.readImage();
                if (!verificationContent) {
                    throw new securityUtils_1.SecurityError('Failed to verify written image content');
                }
                const verificationBuffer = Buffer.from(verificationContent.base64Data, 'base64');
                const verificationHash = securityUtils_1.SecurityUtils.generateContentHash(verificationBuffer);
                if (verificationHash !== originalHash) {
                    throw new securityUtils_1.SecurityError('Image content verification failed');
                }
            });
            this.metricsService.recordSuccess(startTime, rawSize);
            this.logger.debug(`Successfully wrote ${image.mimeType} image to clipboard`, {
                size: rawSize,
                hash: originalHash,
                entropy,
                metadata: image.metadata
            });
        }
        catch (error) {
            this.metricsService.recordError(startTime, error);
            throw this.wrapError('Failed to write image to clipboard', error);
        }
    }
    async writeText(text) {
        const startTime = this.metricsService.startOperation();
        const originalHash = securityUtils_1.SecurityUtils.generateContentHash(text);
        try {
            // Size validation
            const size = Buffer.byteLength(text);
            if (size > this.options.maxContentSize) {
                throw new ContentSizeError('Text content exceeds size limit', size, this.options.maxContentSize);
            }
            // Check for unsafe content
            if (text.includes('\0')) {
                throw new securityUtils_1.SecurityError('Null bytes detected in content');
            }
            // Check for potential script injection
            if (/<script[\s>]|javascript:/i.test(text)) {
                throw new securityUtils_1.SecurityError('Potential script injection detected');
            }
            // Check for command injection
            if (/[<>|&;$()]/.test(text)) {
                throw new securityUtils_1.SecurityError('Potential command injection detected');
            }
            // Check for high entropy (possible encrypted/compressed malicious content)
            const entropy = securityUtils_1.SecurityUtils.calculateEntropy(text);
            if (entropy > 6.5) {
                this.logger.warn('High entropy content detected', { entropy });
            }
            await this.retryOperation(async () => {
                await Promise.resolve(vscode.env.clipboard.writeText(text));
                // Verify content was written correctly
                const verificationText = await vscode.env.clipboard.readText();
                const verificationHash = securityUtils_1.SecurityUtils.generateContentHash(verificationText);
                if (verificationHash !== originalHash) {
                    throw new securityUtils_1.SecurityError('Clipboard content verification failed');
                }
            });
            this.metricsService.recordSuccess(startTime, size);
            this.logger.debug('Successfully wrote text to clipboard', {
                length: text.length,
                hash: originalHash,
                entropy
            });
        }
        catch (error) {
            this.metricsService.recordError(startTime, error);
            throw this.wrapError('Failed to write to clipboard', error);
        }
    }
    getMetrics() {
        return this.metricsService.getMetrics();
    }
    dispose() {
        if (this.isDisposed)
            return;
        this.isDisposed = true;
        this.removeAllListeners();
    }
    async convertGifToPng(gifData) {
        try {
            const buffer = Buffer.from(gifData, 'base64');
            const chunks = [];
            const stream = stream_1.Readable.from(buffer);
            await streamPromises.pipeline(stream, new stream_1.PassThrough({
                highWaterMark: this.options.chunkSize,
                async transform(chunk, encoding, callback) {
                    try {
                        const processed = await (0, sharp_1.default)(chunk)
                            .toFormat('png')
                            .toBuffer();
                        chunks.push(processed);
                        callback(null);
                    }
                    catch (err) {
                        callback(err);
                    }
                }
            }));
            const processedBuffer = Buffer.concat(chunks);
            const metadata = await this.getImageMetadata(processedBuffer);
            return ['image/png', processedBuffer.toString('base64'), metadata];
        }
        catch (error) {
            throw new ImageProcessingError('Failed to convert GIF to PNG', error);
        }
    }
    async getImageMetadata(buffer) {
        try {
            const metadata = await (0, sharp_1.default)(buffer).metadata();
            return {
                width: metadata.width,
                height: metadata.height,
                format: metadata.format,
                size: buffer.length
            };
        }
        catch (error) {
            this.logger.warn('Failed to extract image metadata', { error });
            return {};
        }
    }
    isSupportedImageType(mimeType) {
        return this.supportedImageTypes.includes(mimeType);
    }
    detectCharset(html) {
        const match = html.match(/<meta\s+charset=["']?([\w-]+)/i);
        return match?.[1] || 'utf-8';
    }
    async retryOperation(operation, maxAttempts = this.options.maxRetries) {
        let lastError;
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error;
                if (attempt < maxAttempts) {
                    const delay = this.options.retryDelay * Math.pow(2, attempt - 1);
                    this.logger.debug(`Retry attempt ${attempt}/${maxAttempts} after ${delay}ms`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError;
    }
    wrapError(message, error) {
        if (error instanceof ClipboardError) {
            return error;
        }
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('access') || errorMessage.includes('permission')) {
            return new ClipboardAccessError(`${message}: ${errorMessage}`, error);
        }
        return new ClipboardError(`${message}: ${errorMessage}`, error);
    }
}
exports.VSCodeClipboardService = VSCodeClipboardService;
//# sourceMappingURL=clipboardService.js.map