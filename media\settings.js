/**
 * Codessa Settings Panel JavaScript - Enhanced Version
 */

class SettingsManager {
  constructor() {
    this.settings = {
      general: {
        theme: 'system',
        language: 'en',
        autoSave: true,
        perAgentLLMEnabled: false
      },
      llmProviders: {},
      revolutionaryFeatures: {
        goddessMode: {},
        quantumAnalysis: {},
        neuralSynthesis: {},
        timeTravelDebugging: {}
      },
      memory: {},
      workflows: {},
      agents: {},
      ui: {},
      advanced: {}
    };
    this.rootElement = null;
    this.currentSection = 'general';
    this.pendingChanges = new Map();
    this.vscode = null;
  }

  initialize() {
    this.vscode = window.acquireVsCodeApi ? window.acquireVsCodeApi() : null;
    this.rootElement = document.getElementById('codessa-settings-root');
        
    if (!this.rootElement) {
      console.error('Settings root element not found');
      return;
    }

    this.setupEventListeners();
    this.render();
  }

  updateSettings(newSettings) {
    if (newSettings && typeof newSettings === 'object') {
      this.settings = { ...this.settings, ...newSettings };
      this.render();
    }
  }

  saveSetting(section, key, value) {
    if (!this.settings[section]) {
      this.settings[section] = {};
    }
        
    // Clear any previous errors
    this.clearErrors(section, key);
        
    // Validate before saving
    const error = this.validateSetting(section, key, value);
    if (error) {
      this.showError(section, key, error);
      return false;
    }

    this.settings[section][key] = value;
        
    // Store pending change
    this.pendingChanges.set(`${section}.${key}`, value);
        
    if (this.vscode) {
      this.vscode.postMessage({
        command: 'updateSetting',
        section,
        key,
        value
      });
    }
        
    return true;
  }

  validateSetting(section, key, value) {
    // General validation rules
    switch (section) {
    case 'general':
      switch (key) {
      case 'theme':
        if (!['system', 'light', 'dark'].includes(value)) {
          return 'Theme must be system, light, or dark';
        }
        break;
      case 'language':
        if (!['en', 'de', 'fr', 'es', 'zh'].includes(value)) {
          return 'Language must be en, de, fr, es, or zh';
        }
        break;
      case 'autoSave':
      case 'perAgentLLMEnabled':
        if (typeof value !== 'boolean') {
          return 'Value must be true or false';
        }
        break;
      }
      break;
    case 'revolutionaryFeatures':
      // Add validation for revolutionary features
      break;
    }
    return null;
  }

  showError(section, key, message) {
    const element = document.querySelector(`[data-setting="${section}.${key}"]`);
    if (element) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'setting-error';
      errorDiv.textContent = message;
      errorDiv.style.color = '#dc3545';
      errorDiv.style.fontSize = '0.8em';
      errorDiv.style.marginTop = '0.25em';
            
      // Remove any existing error
      const existingError = element.parentNode.querySelector('.setting-error');
      if (existingError) {
        existingError.remove();
      }
            
      element.parentNode.appendChild(errorDiv);
    }
  }

  clearErrors(section, key) {
    const element = document.querySelector(`[data-setting="${section}.${key}"]`);
    if (element) {
      const existingError = element.parentNode.querySelector('.setting-error');
      if (existingError) {
        existingError.remove();
      }
    }
  }

  showSuccess(section, key) {
    const element = document.querySelector(`[data-setting="${section}.${key}"]`);
    if (element) {
      element.style.borderColor = '#28a745';
      setTimeout(() => {
        element.style.borderColor = '';
      }, 2000);
    }
  }

  showLoading(section, isLoading) {
    const sectionElement = document.querySelector(`[data-section="${section}"]`);
    if (sectionElement) {
      if (isLoading) {
        sectionElement.style.opacity = '0.6';
        sectionElement.style.pointerEvents = 'none';
      } else {
        sectionElement.style.opacity = '1';
        sectionElement.style.pointerEvents = 'auto';
      }
    }
  }

  updateProviderSettings(providers, activeProvider) {
    this.settings.llmProviders = providers || {};
    this.settings.activeProvider = activeProvider;
    this.render();
  }

  setupEventListeners() {
    // Tab switching
    this.rootElement.addEventListener('click', (e) => {
      if (e.target.classList.contains('settings-tab')) {
        const section = e.target.dataset.section;
        this.showSection(section);
      }
    });

    // Setting changes
    this.rootElement.addEventListener('change', (e) => {
      if (e.target.dataset.setting) {
        const [section, key] = e.target.dataset.setting.split('.');
        const value = e.target.type === 'checkbox' 
          ? e.target.checked 
          : e.target.value;
        this.saveSetting(section, key, value);
      }
    });

    // Save all button
    this.rootElement.addEventListener('click', (e) => {
      if (e.target.id === 'save-all-settings') {
        this.saveAllSettings();
      }
    });

    // Export/Import buttons
    this.rootElement.addEventListener('click', (e) => {
      if (e.target.id === 'export-settings') {
        this.exportSettings();
      } else if (e.target.id === 'import-settings') {
        this.importSettings();
      }
    });
  }

  saveAllSettings() {
    // Save all pending changes
    for (const [key, value] of this.pendingChanges) {
      const [section, settingKey] = key.split('.');
      this.saveSetting(section, settingKey, value);
    }
    this.pendingChanges.clear();
        
    this.showStatus('All settings saved successfully!', 'success');
  }

  exportSettings() {
    if (this.vscode) {
      this.vscode.postMessage({ command: 'exportSettings' });
    }
  }

  importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target.result;
          if (this.vscode) {
            this.vscode.postMessage({
              command: 'importSettings',
              settingsJson: content
            });
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }

  showSection(section) {
    // Update active tab
    document.querySelectorAll('.settings-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    document.querySelector(`[data-section="${section}"]`)?.classList.add('active');

    // Show section content
    document.querySelectorAll('.settings-section').forEach(sectionEl => {
      sectionEl.style.display = 'none';
    });
    document.querySelector(`[data-section="${section}"]`)?.style.display = 'block';

    this.currentSection = section;
  }

  showStatus(message, type) {
    const statusEl = document.getElementById('settings-status');
    if (statusEl) {
      statusEl.textContent = message;
      statusEl.className = `status-${type}`;
      statusEl.style.display = 'block';
            
      setTimeout(() => {
        statusEl.style.display = 'none';
      }, 3000);
    }
  }

  render() {
    if (!this.rootElement) return;

    this.rootElement.innerHTML = `
            <div class="settings-container">
                <div class="settings-header">
                    <h1>Codessa Settings</h1>
                    <div class="settings-actions">
                        <button id="export-settings" class="btn btn-secondary">Export</button>
                        <button id="import-settings" class="btn btn-secondary">Import</button>
                        <button id="save-all-settings" class="btn btn-primary">Save All</button>
                    </div>
                </div>

                <div class="settings-tabs">
                    <button class="settings-tab active" data-section="general">⚙️ General</button>
                    <button class="settings-tab" data-section="revolutionary">✨ Revolutionary</button>
                    <button class="settings-tab" data-section="providers">🤖 Providers</button>
                    <button class="settings-tab" data-section="agents">👥 Agents</button>
                    <button class="settings-tab" data-section="memory">🧠 Memory</button>
                    <button class="settings-tab" data-section="workflows">🔄 Workflows</button>
                    <button class="settings-tab" data-section="ui">🎨 Interface</button>
                    <button class="settings-tab" data-section="advanced">🔧 Advanced</button>
                </div>

                <div class="settings-content">
                    <div class="settings-section active" data-section="general">
                        <div class="section-content">
                            <h2>General Settings</h2>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" data-setting="general.perAgentLLMEnabled" 
                                           ${this.settings.general?.perAgentLLMEnabled ? 'checked' : ''}>
                                    Allow each agent to use its own LLM provider/model
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="theme-select">Theme</label>
                                <select id="theme-select" data-setting="general.theme">
                                    <option value="system" ${this.settings.general?.theme === 'system' ? 'selected' : ''}>System Default</option>
                                    <option value="light" ${this.settings.general?.theme === 'light' ? 'selected' : ''}>Light</option>
                                    <option value="dark" ${this.settings.general?.theme === 'dark' ? 'selected' : ''}>Dark</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="language-select">Language</label>
                                <select id="language-select" data-setting="general.language">
                                    <option value="en" ${this.settings.general?.language === 'en' ? 'selected' : ''}>English</option>
                                    <option value="de" ${this.settings.general?.language === 'de' ? 'selected' : ''}>German</option>
                                    <option value="fr" ${this.settings.general?.language === 'fr' ? 'selected' : ''}>French</option>
                                    <option value="es" ${this.settings.general?.language === 'es' ? 'selected' : ''}>Spanish</option>
                                    <option value="zh" ${this.settings.general?.language === 'zh' ? 'selected' : ''}>Chinese</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" data-setting="general.autoSave" 
                                           ${this.settings.general?.autoSave ? 'checked' : ''}>
                                    Enable Auto-Save
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section" data-section="revolutionary">
                        <div class="section-content">
                            <h2>Revolutionary Features</h2>
                            <p>Configure advanced AI capabilities including Goddess Mode, Quantum Analysis, and Neural Synthesis.</p>
                            <div class="feature-group">
                                <h3>✨ Goddess Mode</h3>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" data-setting="revolutionaryFeatures.goddessMode.enabled" 
                                               ${this.settings.revolutionaryFeatures?.goddessMode?.enabled ? 'checked' : ''}>
                                        Enable Goddess Mode
                                    </label>
                                </div>
                            </div>
                            <div class="feature-group">
                                <h3>🔬 Quantum Analysis</h3>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" data-setting="revolutionaryFeatures.quantumAnalysis.enabled" 
                                               ${this.settings.revolutionaryFeatures?.quantumAnalysis?.enabled ? 'checked' : ''}>
                                        Enable Quantum Analysis
                                    </label>
                                </div>
                            </div>
                            <div class="feature-group">
                                <h3>🧠 Neural Synthesis</h3>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" data-setting="revolutionaryFeatures.neuralSynthesis.enabled" 
                                               ${this.settings.revolutionaryFeatures?.neuralSynthesis?.enabled ? 'checked' : ''}>
                                        Enable Neural Synthesis
                                    </label>
                                </div>
                            </div>
                            <div class="feature-group">
                                <h3>⏰ Time Travel Debugging</h3>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" data-setting="revolutionaryFeatures.timeTravelDebugging.enabled" 
                                               ${this.settings.revolutionaryFeatures?.timeTravelDebugging?.enabled ? 'checked' : ''}>
                                        Enable Time Travel Debugging
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section" data-section="providers">
                        <div class="section-content">
                            <h2>AI Providers</h2>
                            <p>Configure AI model providers and API settings.</p>
                            <div id="providers-content">
                                <p>Loading providers...</p>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section" data-section="agents">
                        <div class="section-content">
                            <h2>Agents</h2>
                            <p>Manage AI agents and their configurations.</p>
                            <div id="agents-content">
                                <p>Agent settings will be loaded here.</p>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section" data-section="memory">
                        <div class="section-content">
                            <h2>Memory</h2>
                            <p>Configure memory system and storage options.</p>
                            <div id="memory-content">
                                <p>Memory settings will be loaded here.</p>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section" data-section="workflows">
                        <div class="section-content">
                            <h2>Workflows</h2>
                            <p>Manage automated workflows and templates.</p>
                            <div id="workflows-content">
                                <p>Workflow settings will be loaded here.</p>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section" data-section="ui">
                        <div class="section-content">
                            <h2>Interface</h2>
                            <p>User interface and theme customization.</p>
                            <div id="ui-content">
                                <p>UI settings will be loaded here.</p>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section" data-section="advanced">
                        <div class="section-content">
                            <h2>Advanced</h2>
                            <p>Advanced settings and developer options.</p>
                            <div id="advanced-content">
                                <p>Advanced settings will be loaded here.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="settings-status" class="status-message" style="display: none;"></div>
            </div>
        `;

    // Re-attach event listeners
    this.setupEventListeners();
  }
}

// Initialize settings manager when DOM is loaded
window.addEventListener('DOMContentLoaded', () => {
  window.settingsManager = new SettingsManager();
  window.settingsManager.initialize();
});

// Handle messages from the extension
window.addEventListener('message', event => {
  const message = event.data;
    
  switch (message.command) {
  case 'updateSettings':
    window.settingsManager?.updateSettings(message.settings);
    break;
            
  case 'settingUpdateResult':
    const { section, key, success, error, warnings } = message;
    if (success) {
      window.settingsManager?.showSuccess(section, key);
      if (warnings && warnings.length > 0) {
        console.warn(`Warnings for ${section}.${key}:`, warnings);
      }
    } else {
      window.settingsManager?.showError(section, key, error || 'Failed to save setting');
    }
    break;
            
  case 'updateProviderSettings':
    window.settingsManager?.updateProviderSettings(message.providers, message.activeProvider);
    break;
            
  case 'showLoading':
    window.settingsManager?.showLoading(message.section, message.isLoading);
    break;

  case 'settingsExported':
    if (message.success) {
      // Create download link for exported settings
      const blob = new Blob([message.settings], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'codessa-settings.json';
      a.click();
      URL.revokeObjectURL(url);
      window.settingsManager?.showStatus('Settings exported successfully!', 'success');
    } else {
      window.settingsManager?.showStatus(`Export failed: ${message.error}`, 'error');
    }
    break;

  case 'settingsImported':
    if (message.success) {
      window.settingsManager?.showStatus('Settings imported successfully!', 'success');
    } else {
      const errorMsg = message.errors ? message.errors.join(', ') : message.error;
      window.settingsManager?.showStatus(`Import failed: ${errorMsg}`, 'error');
    }
    break;
  }
});
