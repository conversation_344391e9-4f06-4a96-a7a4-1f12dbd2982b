{"version": 3, "file": "goddessMode.js", "sourceRoot": "", "sources": ["../../src/personality/goddessMode.ts"], "names": [], "mappings": ";;;AAAA,sCAAmC;AACnC,sCAA4D;AAiD5D;;GAEG;AACH,MAAa,wBAAwB;IAC3B,WAAW,CAAqB;IAChC,sBAAsB,CAMzB;IACG,YAAY,CAMjB;IACK,YAAY,CAAgB;IAEpC;QACE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACvD,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;IACtG,CAAC;IAED;;SAEK;IACG,4BAA4B;QAClC,MAAM,QAAQ,GAAG,IAAA,uBAAc,GAAE,CAAC;QAClC,OAAO;YACL,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,EAAE;YAC3C,qBAAqB,EAAE,QAAQ,CAAC,qBAAqB,IAAI,EAAE;YAC3D,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,iBAAiB,IAAI,UAAU,CAAC;YACtF,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;YACvC,eAAe,EAAE,QAAQ,CAAC,eAAe,IAAI,EAAE;SAChD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,KAA2C;QACtE,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,aAAa;gBAChB,OAAO,aAAa,CAAC;YACvB,KAAK,aAAa;gBAChB,OAAO,aAAa,CAAC;YACvB,KAAK,YAAY;gBACf,OAAO,YAAY,CAAC;YACtB,mDAAmD;YACnD,KAAK,MAAM;gBACT,OAAO,YAAY,CAAC;YACtB,KAAK,SAAS;gBACZ,OAAO,aAAa,CAAC;YACvB;gBACE,OAAO,UAAU,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAmB,EAAE,GAAiC;QAChF,MAAM,UAAU,GAAG,GAAG,EAAE,cAAc,IAAI,EAAE,CAAC;QAC7C,2DAA2D;QAC3D,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAE3E,8FAA8F;QAC9F,MAAM,UAAU,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,MAAM,WAAW,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAErE,IAAI,OAAO,GAAG,UAAU;YAAE,OAAO,KAAK,CAAC;QACvC,IAAI,OAAO,GAAG,WAAW;YAAE,OAAO,MAAM,CAAC;QACzC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAAgB;QACzC,OAAO,6DAA6D,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtF,CAAC;IAEO,oBAAoB,CAAC,QAAgB;QAC3C,OAAO,yEAAyE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClG,CAAC;IAEO,qBAAqB,CAAC,QAAgB;QAC5C,MAAM,MAAM,GAAG,kCAAkC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjE,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;;SAEK;IACE,oBAAoB,CACzB,OAAe,EACf,OAKK;QAEL,MAAM,IAAI,GAAkB;YAC1B,MAAM,EAAE,EAAE;YACV,UAAU,EAAE,EAAE;YACd,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,EAAE;YACf,UAAU,EAAE,EAAE;YACd,oCAAoC;YACpC,OAAO,EAAE,EAAE;YACX,gBAAgB,EAAE,EAAE;YACpB,SAAS,EAAE,EAAE;YACb,gBAAgB,EAAE,EAAE;YACpB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;SACZ,CAAC;QAEF,8EAA8E;QAC9E,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,wDAAwD;QACxD,MAAM,cAAc,GAAG;YACrB,uBAAuB;YACvB,wEAAwE;YACxE,sBAAsB;YACtB,sEAAsE;YACtE,gBAAgB;YAChB,0DAA0D;YAC1D,kBAAkB;YAClB,8DAA8D;YAC9D,qBAAqB;YACrB,qEAAqE;SACtE,CAAC;QAEF,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC/B,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,mEAAmE;QACnE,MAAM,kBAAkB,GAAG;YACzB,iEAAiE;YACjE,mEAAmE;YACnE,gEAAgE;YAChE,wDAAwD;YACxD,8DAA8D;SAC/D,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,kEAAkE;YAClE,4DAA4D;YAC5D,8DAA8D;YAC9D,oDAAoD;SACrD,CAAC;QAEF,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACnC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACnC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,iEAAiE;QACjE,MAAM,eAAe,GAAG;YACtB,yDAAyD;YACzD,uDAAuD;YACvD,2DAA2D;YAC3D,wDAAwD;SACzD,CAAC;QAEF,MAAM,cAAc,GAAG;YACrB,2DAA2D;YAC3D,+DAA+D;YAC/D,6DAA6D;YAC7D,wDAAwD;SACzD,CAAC;QAEF,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC/B,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,8DAA8D;QAC9D,MAAM,mBAAmB,GAAG;YAC1B,8DAA8D;YAC9D,uDAAuD;YACvD,uDAAuD;YACvD,kDAAkD;YAClD,qDAAqD;SACtD,CAAC;QAEF,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACpC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrD,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,2CAA2C;QAE3C,sEAAsE;QACtE,MAAM,eAAe,GAAG;YACtB,4DAA4D;YAC5D,6DAA6D;YAC7D,wDAAwD;SACzD,CAAC;QAEF,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjD,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,gBAAgB,GAAG;YACvB,+DAA+D;YAC/D,uDAAuD;YACvD,wEAAwE;SACzE,CAAC;QAEF,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACjC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;gBAC3D,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,4EAA4E;QAC5E,MAAM,YAAY,GAAG;YACnB,uDAAuD;YACvD,mEAAmE;YACnE,yDAAyD;SAC1D,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC7B,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACnD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG;YACxB,oDAAoD;YACpD,uDAAuD;SACxD,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,oDAAoD;YACpD,gDAAgD;SACjD,CAAC;QAEF,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC1D,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACnC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC1D,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,oDAAoD;QACpD,MAAM,eAAe,GAAG;YACtB,yDAAyD;YACzD,mDAAmD;SACpD,CAAC;QAEF,MAAM,eAAe,GAAG;YACtB,iDAAiD;YACjD,iDAAiD;SAClD,CAAC;QAEF,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAClD,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAClD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sDAAsD;QACtD,MAAM,aAAa,GAAG;YACpB,wDAAwD;YACxD,oDAAoD;YACpD,8DAA8D;SAC/D,CAAC;QAEF,MAAM,iBAAiB,GAAG;YACxB,iDAAiD;YACjD,iDAAiD;SAClD,CAAC;QAEF,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC9B,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClC,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sDAAsD;QACtD,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;YAE/B,iEAAiE;YACjE,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;YACrB,CAAC;YAED,oEAAoE;iBAC/D,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;YAClB,CAAC;YAED,sEAAsE;iBACjE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACjB,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;YACvB,CAAC;YAED,yDAAyD;iBACpD,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;QAED,wDAAwD;QACxD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAEzD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,0CAA0C;gBACzD,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;YACpB,CAAC;iBAAM,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,qCAAqC;gBAC3D,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;YACrB,CAAC;iBAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,yBAAyB;gBAC9D,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;gBACrB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;QAED,wDAAwD;QACxD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,IAAI,OAAO,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC,CAAC,kBAAkB;gBACjD,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,gBAAgB,IAAI,EAAE,CAAC;YAC9B,CAAC;iBAAM,IAAI,OAAO,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC,sBAAsB;gBAC3D,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;YACpB,CAAC;iBAAM,IAAI,OAAO,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC,gCAAgC;gBACrE,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,mCAAmC;YACxD,CAAC;QACH,CAAC;QAED,4CAA4C;QAC5C,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,IAAI,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC,CAAC,kBAAkB;gBACnD,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACjB,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;YACvB,CAAC;iBAAM,IAAI,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC,CAAC,sBAAsB;gBAC9D,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,0BAA0B;YAC7C,CAAC;iBAAM,IAAI,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC,CAAC,iBAAiB;gBACzD,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,qBAAqB;gBAC3C,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,GAA0B,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAA0B,CAAC,CAAC,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;SAEK;IACE,uBAAuB,CAC5B,OAAe,EACf,YAA2B,EAC3B,OAAqC;QAErC,MAAM,QAAQ,GAAG,IAAA,uBAAc,GAAE,CAAC;QAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAEvC,iEAAiE;QACjE,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;QAC1G,MAAM,KAAK,GAAG;YACZ,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC3C,UAAU,EAAE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;YAC/C,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC;SAClD,CAAC;QAEF,MAAM,QAAQ,GAAoB;YAChC,OAAO,EAAE,EAAE;YACX,IAAI,EAAE,YAAY;YAClB,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC;SAC9D,CAAC;QAEF,iEAAiE;QACjE,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC3B,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;YACvB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC1E,CAAC;aAAM,CAAC;YACN,8EAA8E;YAC9E,IAAI,YAAY,CAAC,MAAM,GAAG,EAAE,IAAI,aAAa,KAAK,MAAM,EAAE,CAAC;gBACzD,QAAQ,CAAC,IAAI,GAAG,YAAY,CAAC;gBAC7B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAC5E,QAAQ,CAAC,mBAAmB,GAAG,QAAQ,CAAC,YAAY,IAAI,EAAE;oBACxD,CAAC,CAAC,gFAAgF;oBAClF,CAAC,CAAC,8CAA8C,CAAC;YACrD,CAAC;iBAAM,IAAI,YAAY,CAAC,WAAW,GAAG,EAAE,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC7D,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;gBAC9B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAC3E,QAAQ,CAAC,aAAa,GAAG,2DAA2D,CAAC;YACvF,CAAC;iBAAM,IAAI,YAAY,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;gBACxC,QAAQ,CAAC,IAAI,GAAG,YAAY,CAAC;gBAC7B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAC/E,QAAQ,CAAC,mBAAmB,GAAG,QAAQ,CAAC,YAAY,IAAI,EAAE;oBACxD,CAAC,CAAC,uDAAuD;oBACzD,CAAC,CAAC,wCAAwC,CAAC;YAC/C,CAAC;iBAAM,IAAI,YAAY,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;gBACxC,QAAQ,CAAC,IAAI,GAAG,aAAa,KAAK,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;gBACpE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBACvE,QAAQ,CAAC,aAAa,GAAG,qDAAqD,CAAC;YACjF,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;gBACvB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,IAAI,EAAE;gBAC5C,CAAC,CAAC,2EAA2E;gBAC7E,CAAC,CAAC,wCAAwC,CAAC;YAC7C,QAAQ,CAAC,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB;gBACzD,CAAC,CAAC,GAAG,QAAQ,CAAC,mBAAmB,IAAI,UAAU,EAAE;gBACjD,CAAC,CAAC,UAAU,CAAC;QACjB,CAAC;QAED,oCAAoC;QACpC,IAAI,KAAK,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACrD,QAAQ,CAAC,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB;gBACzD,CAAC,CAAC,GAAG,QAAQ,CAAC,mBAAmB,8BAA8B;gBAC/D,CAAC,CAAC,6BAA6B,CAAC;QACpC,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,EAAE,IAAI,QAAQ,CAAC,qBAAqB,EAAE,CAAC;YAC1E,QAAQ,CAAC,qBAAqB,GAAG,IAAI,CAAC,8BAA8B,CAAC,YAAY,CAAC,CAAC;QACrF,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,WAAW,EAAE,OAAO;YACpB,YAAY;YACZ,QAAQ;YACR,aAAa,EAAE,EAAE;SAClB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACG,4BAA4B,CAAC,OAAe,EAAE,IAAmB;QACvE,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAA,uBAAc,GAAE,CAAC;QAElC,yDAAyD;QACzD,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,MAAM,aAAa,GAAG;gBACpB,uFAAuF;gBACvF,6FAA6F;gBAC7F,yFAAyF;aAC1F,CAAC;YACF,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1F,MAAM,cAAc,GAAG;gBACrB,gFAAgF;gBAChF,kFAAkF;gBAClF,uFAAuF;aACxF,CAAC;YACF,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3E,CAAC;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACpD,MAAM,oBAAoB,GAAG;gBAC3B,wHAAwH;gBACxH,qHAAqH;gBACrH,mHAAmH;aACpH,CAAC;YACF,OAAO,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;QACvF,CAAC;QAED,mCAAmC;QACnC,MAAM,SAAS,GAAG;YAChB,qFAAqF;YACrF,4FAA4F;YAC5F,sEAAsE;YACtE,mGAAmG;SACpG,CAAC;QACF,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACG,2BAA2B,CAAC,OAAe,EAAE,IAAmB;QACtE,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAA,uBAAc,GAAE,CAAC;QAElC,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACtF,MAAM,kBAAkB,GAAG;gBACzB,yFAAyF;gBACzF,gGAAgG;gBAChG,gGAAgG;aACjG,CAAC;YACF,OAAO,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;QACnF,CAAC;QAED,sCAAsC;QACtC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1F,MAAM,oBAAoB,GAAG;gBAC3B,2GAA2G;gBAC3G,iGAAiG;gBACjG,sFAAsF;aACvF,CAAC;YACF,OAAO,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;QACvF,CAAC;QAED,uCAAuC;QACvC,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,IAAI,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACzD,MAAM,wBAAwB,GAAG;gBAC/B,gGAAgG;gBAChG,0GAA0G;gBAC1G,+FAA+F;aAChG,CAAC;YACF,OAAO,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/F,CAAC;QAED,iCAAiC;QACjC,MAAM,SAAS,GAAG;YAChB,sGAAsG;YACtG,mGAAmG;YACnG,gGAAgG;YAChG,gGAAgG;SACjG,CAAC;QACF,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACG,+BAA+B,CAAC,OAAe,EAAE,IAAmB;QAC1E,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAA,uBAAc,GAAE,CAAC;QAElC,oCAAoC;QACpC,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACrG,MAAM,iBAAiB,GAAG;gBACxB,uGAAuG;gBACvG,qGAAqG;gBACrG,sGAAsG;aACvG,CAAC;YACF,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjF,CAAC;QAED,iDAAiD;QACjD,IAAI,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnG,MAAM,iBAAiB,GAAG;gBACxB,+FAA+F;gBAC/F,gGAAgG;gBAChG,yFAAyF;aAC1F,CAAC;YACF,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjF,CAAC;QAED,4CAA4C;QAC5C,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,IAAI,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACxD,MAAM,oBAAoB,GAAG;gBAC3B,6FAA6F;gBAC7F,+FAA+F;gBAC/F,wGAAwG;aACzG,CAAC;YACF,OAAO,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;QACvF,CAAC;QAED,gCAAgC;QAChC,MAAM,SAAS,GAAG;YAChB,0GAA0G;YAC1G,2FAA2F;YAC3F,mGAAmG;YACnG,+FAA+F;SAChG,CAAC;QACF,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,OAAe,EAAE,IAAmB;QAClE,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAA,uBAAc,GAAE,CAAC;QAElC,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9F,MAAM,qBAAqB,GAAG;gBAC5B,mFAAmF;gBACnF,4EAA4E;gBAC5E,8EAA8E;aAC/E,CAAC;YACF,OAAO,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC;QACzF,CAAC;QAED,gCAAgC;QAChC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACzF,MAAM,mBAAmB,GAAG;gBAC1B,sFAAsF;gBACtF,kFAAkF;gBAClF,mFAAmF;aACpF,CAAC;YACF,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;QACrF,CAAC;QAED,uDAAuD;QACvD,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,IAAI,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACxD,MAAM,8BAA8B,GAAG;gBACrC,6FAA6F;gBAC7F,2FAA2F;gBAC3F,uFAAuF;aACxF,CAAC;YACF,OAAO,8BAA8B,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,8BAA8B,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3G,CAAC;QAED,6BAA6B;QAC7B,MAAM,SAAS,GAAG;YAChB,oFAAoF;YACpF,6FAA6F;YAC7F,wFAAwF;YACxF,gGAAgG;SACjG,CAAC;QACF,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,OAAe,EAAE,IAAmB;QACnE,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAEvC,2CAA2C;QAC3C,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/F,MAAM,iBAAiB,GAAG;gBACxB,+FAA+F;gBAC/F,wFAAwF;gBACxF,uFAAuF;aACxF,CAAC;YACF,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjF,CAAC;QAED,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAClG,MAAM,iBAAiB,GAAG;gBACxB,8EAA8E;gBAC9E,sFAAsF;gBACtF,oFAAoF;aACrF,CAAC;YACF,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjF,CAAC;QAED,+BAA+B;QAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,CAAC;YACpB,MAAM,gBAAgB,GAAG;gBACvB,qFAAqF;gBACrF,6EAA6E;gBAC7E,+EAA+E;aAChF,CAAC;YACF,OAAO,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/E,CAAC;QAED,8BAA8B;QAC9B,MAAM,SAAS,GAAG;YAChB,8FAA8F;YAC9F,uFAAuF;YACvF,+EAA+E;YAC/E,yEAAyE;SAC1E,CAAC;QACF,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,IAAmB;QAClD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE;YAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE;YAAE,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;YAAE,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1D,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE;YAAE,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;YAAE,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE;YAAE,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAErD,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC;IAChF,CAAC;IAED;;SAEK;IACG,8BAA8B,CAAC,IAAmB;QACxD,MAAM,UAAU,GAAgC,EAAE,CAAC;QAEnD,mDAAmD;QACnD,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACrB,UAAU,CAAC,iBAAiB,GAAG,YAAY,CAAC;QAC9C,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YAChC,UAAU,CAAC,iBAAiB,GAAG,aAAa,CAAC;QAC/C,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;SAEK;IACE,yBAAyB,CAC9B,aAAqB,EACrB,aAAqB,EACrB,YAAqB,EACrB,IAAoB,EACpB,OAAmF;QAEnF,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE;YACnC,aAAa;YACb,YAAY;YACZ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YACnC,OAAO,EAAE,OAAO,IAAI,EAAE;SACvB,CAAC,CAAC;QAEH,kDAAkD;QAClD,IAAI,IAAI,IAAI,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,kEAAkE;QAClE,IAAI,aAAa,GAAG,EAAE,EAAE,CAAC;YACvB,0CAA0C;YAC1C,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;YACnF,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,aAAa,GAAG,EAAE,EAAE,CAAC;gBACnD,2CAA2C;gBAC3C,IAAI,CAAC,WAAW,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;YACrG,CAAC;QACH,CAAC;aAAM,IAAI,aAAa,GAAG,EAAE,EAAE,CAAC;YAC9B,+CAA+C;YAC/C,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;YACnF,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,IAAI,aAAa,GAAG,EAAE,EAAE,CAAC;gBACvD,wCAAwC;gBACxC,IAAI,CAAC,WAAW,CAAC,iBAAiB,GAAG,YAAY,CAAC;YACpD,CAAC;QACH,CAAC;QAED,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kDAAkD,aAAa,aAAa,CAAC,CAAC;IACtG,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAAmB,EAAE,OAAyG,EAAE,aAAqB;QAC9K,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QAE/B,0CAA0C;QAC1C,IAAI,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACvC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC;YACtC,CAAC,CAAC,SAAS,KAAK,SAAS,CAC1B,CAAC;QAEF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG;gBACR,SAAS;gBACT,SAAS;gBACT,aAAa,EAAE,OAAO,CAAC,eAAe,IAAI,CAAC;gBAC3C,WAAW,EAAE,EAAE,GAAG,IAAI,EAAE;gBACxB,QAAQ,EAAE,EAAE;gBACZ,kBAAkB,EAAE,EAAE;aACvB,CAAC;YACF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,0BAA0B;YAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC9B,MAAM,OAAO,GAAG,GAA0B,CAAC;gBAC3C,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,4BAA4B;QAC5B,IAAI,aAAa,GAAG,EAAE,EAAE,CAAC;YACvB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvD,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAED,gDAAgD;QAChD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,OAAO;YACL,MAAM,EAAE,EAAE;YACV,UAAU,EAAE,EAAE;YACd,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,EAAE;YACf,UAAU,EAAE,EAAE;YACd,OAAO,EAAE,EAAE;YACX,gBAAgB,EAAE,EAAE;YACpB,SAAS,EAAE,EAAE;YACb,gBAAgB,EAAE,EAAE;YACpB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,IAAmB;QACzC,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE;YAAE,OAAO,eAAe,CAAC;QAC7C,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE;YAAE,OAAO,qBAAqB,CAAC;QACxD,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;YAAE,OAAO,kBAAkB,CAAC;QACpD,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;YAAE,OAAO,oBAAoB,CAAC;QACtD,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE;YAAE,OAAO,oBAAoB,CAAC;QACnD,IAAI,IAAI,CAAC,gBAAgB,GAAG,EAAE;YAAE,OAAO,kBAAkB,CAAC;QAC1D,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;SAEK;IACE,cAAc;QACnB,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IACjC,CAAC;IAED;;SAEK;IACE,iBAAiB,CAAC,OAAoC;QAC3D,IAAI,CAAC,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,EAAE,CAAC;QACvD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;CACF;AAr7BD,4DAq7BC;AAED,qBAAqB;AACR,QAAA,wBAAwB,GAAG,IAAI,wBAAwB,EAAE,CAAC", "sourcesContent": ["import { Logger } from '../logger';\nimport { getGoddessMode, GoddessSettings } from '../config';\n\n/**\n * Goddess Mode - Revolutionary AI personality with emotional intelligence\n * The unique AI personality that sets Codessa apart from all competitors\n */\n\nexport interface GoddessPersonality {\n    adaptiveLevel: number; // 0-100, how much personality adapts to user\n    emotionalIntelligence: number; // 0-100, emotional understanding level\n    motivationalStyle: 'encouraging' | 'challenging' | 'supportive' | 'adaptive';\n    wisdomLevel: number; // 0-100, programming wisdom and experience\n    creativityLevel: number; // 0-100, creative problem-solving approach\n}\n\nexport interface DeveloperMood {\n    stress: number; // 0-100, current stress level\n    confidence: number; // 0-100, confidence in current task\n    energy: number; // 0-100, energy level\n    focus: number; // 0-100, focus and concentration\n    frustration: number; // 0-100, frustration with current problem\n    excitement: number; // 0-100, excitement about project\n    // Advanced psychological indicators\n    burnout: number; // 0-100, signs of burnout/exhaustion\n    imposterSyndrome: number; // 0-100, imposter syndrome indicators\n    flowState: number; // 0-100, indicators of flow state\n    socialConnection: number; // 0-100, sense of belonging/connection\n    autonomy: number; // 0-100, sense of control/choice\n    mastery: number; // 0-100, sense of competence/growth\n}\n\nexport interface MoodPattern {\n    timeOfDay: number;\n    dayOfWeek: number;\n    sessionLength: number;\n    averageMood: DeveloperMood;\n    triggers: string[];\n    effectiveResponses: string[];\n}\n\nexport interface GoddessResponse {\n    message: string;\n    tone: 'encouraging' | 'wise' | 'playful' | 'supportive' | 'challenging';\n    emotionalContext: string;\n    personalityAdjustment?: Partial<GoddessPersonality>;\n    motivationalElement?: string;\n    wisdomSharing?: string;\n}\n\n/**\n * Goddess Personality Engine - The heart of Codessa's unique AI personality\n */\nexport class GoddessPersonalityEngine {\n  private personality: GoddessPersonality;\n  private userInteractionHistory: Array<{\n        timestamp: number;\n        userMessage: string;\n        detectedMood: DeveloperMood;\n        response: GoddessResponse;\n        effectiveness: number; // 0-100, how effective the response was\n    }>;\n  private learningData: Map<string, {\n    effectiveness: number;\n    userFeedback?: string;\n    timestamp: number;\n    mood: DeveloperMood;\n    context: { timeOfDay?: number; sessionDuration?: number; codeComplexity?: number; recentErrors?: number };\n  }>;\n  private moodPatterns: MoodPattern[];\n\n  constructor() {\n    this.personality = this.initializeDefaultPersonality();\n    this.userInteractionHistory = [];\n    this.learningData = new Map();\n    this.moodPatterns = [];\n\n    Logger.instance.info('Goddess Personality Engine initialized with advanced psychological analysis');\n  }\n\n  /**\n     * Initialize default goddess personality\n     */\n  private initializeDefaultPersonality(): GoddessPersonality {\n    const settings = getGoddessMode();\n    return {\n      adaptiveLevel: settings.adaptiveLevel ?? 75,\n      emotionalIntelligence: settings.emotionalIntelligence ?? 85,\n      motivationalStyle: this.mapMotivationalStyle(settings.motivationalStyle ?? 'adaptive'),\n      wisdomLevel: settings.wisdomLevel ?? 90,\n      creativityLevel: settings.creativityLevel ?? 80\n    };\n  }\n\n  /**\n   * Map broader UI motivational styles to the constrained engine set\n   */\n  private mapMotivationalStyle(style: GoddessSettings['motivationalStyle']): GoddessPersonality['motivationalStyle'] {\n    switch (style) {\n      case 'encouraging':\n        return 'encouraging';\n      case 'challenging':\n        return 'challenging';\n      case 'supportive':\n        return 'supportive';\n      // Map UI-only styles to closest engine equivalents\n      case 'wise':\n        return 'supportive';\n      case 'playful':\n        return 'encouraging';\n      default:\n        return 'adaptive';\n    }\n  }\n\n  /**\n   * Assess cognitive load using Yerkes–Dodson-inspired heuristic\n   */\n  private assessCognitiveLoad(mood: DeveloperMood, ctx?: { codeComplexity?: number }): 'low' | 'optimal' | 'high' {\n    const complexity = ctx?.codeComplexity ?? 50;\n    // Approximate arousal: stress + (100-energy) + frustration\n    const arousal = (mood.stress + (100 - mood.energy) + mood.frustration) / 3;\n\n    // Target arousal band depends on task complexity (higher complexity -> lower optimal arousal)\n    const optimalLow = complexity > 70 ? 35 : complexity > 50 ? 45 : 55;\n    const optimalHigh = complexity > 70 ? 55 : complexity > 50 ? 65 : 75;\n\n    if (arousal < optimalLow) return 'low';\n    if (arousal > optimalHigh) return 'high';\n    return 'optimal';\n  }\n\n  /**\n   * Lightweight SDT need detectors from language cues\n   */\n  private detectAutonomyNeed(lowerMsg: string): boolean {\n    return /(have to|must|forced|no choice|can't choose|blocked|policy)/.test(lowerMsg);\n  }\n\n  private detectCompetenceNeed(lowerMsg: string): boolean {\n    return /(confused|stuck|don't know|no idea|hard|difficult|can't|failing|broken)/.test(lowerMsg);\n  }\n\n  private detectRelatednessNeed(lowerMsg: string): boolean {\n    const lonely = /(alone|no help|no one|by myself)/.test(lowerMsg);\n    const teamSupport = /(team|pair|we )/.test(lowerMsg);\n    return lonely && !teamSupport;\n  }\n\n  /**\n     * Analyze developer mood from their message and context\n     */\n  public analyzeDeveloperMood(\n    message: string, \n    context: {\n            timeOfDay?: number;\n            recentErrors?: number;\n            sessionDuration?: number;\n            codeComplexity?: number;\n        }\n  ): DeveloperMood {\n    const mood: DeveloperMood = {\n      stress: 30,\n      confidence: 70,\n      energy: 60,\n      focus: 70,\n      frustration: 20,\n      excitement: 50,\n      // Advanced psychological indicators\n      burnout: 20,\n      imposterSyndrome: 30,\n      flowState: 50,\n      socialConnection: 60,\n      autonomy: 70,\n      mastery: 60\n    };\n\n    // Analyze message content for emotional indicators with comprehensive lexicon\n    const lowerMessage = message.toLowerCase();\n\n    // Advanced stress indicators (research-backed patterns)\n    const stressPatterns = [\n      // Crisis/urgency words\n      /\\b(urgent|deadline|asap|emergency|critical|crisis|panic|rush|hurry)\\b/g,\n      // Pressure indicators\n      /\\b(pressure|overwhelm|swamp|drown|buried|crush|breaking|collapse)\\b/g,\n      // Time pressure\n      /\\b(running out|no time|behind|late|overdue|yesterday)\\b/g,\n      // System failures\n      /\\b(crash|fail|break|down|error|bug|broken|corrupt|freeze)\\b/g,\n      // Emotional distress\n      /\\b(stress|anxiety|worry|concern|trouble|problem|issue|nightmare)\\b/g\n    ];\n\n    stressPatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.stress += Math.min(30, matches.length * 8);\n        mood.frustration += Math.min(20, matches.length * 5);\n      }\n    });\n\n    // Confidence indicators (expanded with imposter syndrome patterns)\n    const confidenceNegative = [\n      /\\b(confused|stuck|lost|clueless|don't know|no idea|helpless)\\b/g,\n      /\\b(imposter|fake|fraud|pretend|not good enough|terrible|awful)\\b/g,\n      /\\b(can't|unable|impossible|hopeless|give up|quit|surrender)\\b/g,\n      /\\b(stupid|dumb|idiot|incompetent|useless|worthless)\\b/g,\n      /\\b(doubt|uncertain|unsure|hesitant|scared|afraid|worried)\\b/g\n    ];\n\n    const confidencePositive = [\n      /\\b(working|progress|almost|getting|understand|figured|solved)\\b/g,\n      /\\b(confident|sure|certain|know|mastered|expert|skilled)\\b/g,\n      /\\b(accomplished|achieved|completed|finished|done|success)\\b/g,\n      /\\b(proud|satisfied|pleased|happy|glad|thrilled)\\b/g\n    ];\n\n    confidenceNegative.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.confidence -= Math.min(25, matches.length * 8);\n        mood.frustration += Math.min(15, matches.length * 5);\n      }\n    });\n\n    confidencePositive.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.confidence += Math.min(20, matches.length * 6);\n        mood.excitement += Math.min(15, matches.length * 4);\n      }\n    });\n\n    // Energy and fatigue indicators (circadian and burnout patterns)\n    const fatiguePatterns = [\n      /\\b(tired|exhausted|drained|burnt|burnout|weary|worn)\\b/g,\n      /\\b(sleepy|drowsy|groggy|zombie|dead|dying|kill me)\\b/g,\n      /\\b(can't focus|distracted|scattered|foggy|blank|empty)\\b/g,\n      /\\b(procrastinat|avoid|delay|put off|later|tomorrow)\\b/g\n    ];\n\n    const energyPatterns = [\n      /\\b(excited|energized|pumped|motivated|inspired|driven)\\b/g,\n      /\\b(awesome|amazing|fantastic|incredible|brilliant|perfect)\\b/g,\n      /\\b(love|enjoy|fun|great|wonderful|excellent|outstanding)\\b/g,\n      /\\b(flow|zone|focused|concentrated|immersed|engaged)\\b/g\n    ];\n\n    fatiguePatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.energy -= Math.min(35, matches.length * 10);\n        mood.focus -= Math.min(20, matches.length * 6);\n        mood.stress += Math.min(15, matches.length * 4);\n      }\n    });\n\n    energyPatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.energy += Math.min(30, matches.length * 8);\n        mood.excitement += Math.min(25, matches.length * 7);\n        mood.focus += Math.min(15, matches.length * 4);\n      }\n    });\n\n    // Frustration indicators (debugging and technical challenges)\n    const frustrationPatterns = [\n      /\\b(frustrat|annoying|irritat|aggravat|infuriat|mad|angry)\\b/g,\n      /\\b(why|what|how|wtf|damn|shit|fuck|hell|god|jesus)\\b/g,\n      /\\b(again|still|keep|won't|doesn't|refuse|stubborn)\\b/g,\n      /\\b(hate|despise|loathe|detest|sick of|fed up)\\b/g,\n      /\\b(stupid|ridiculous|absurd|insane|crazy|mental)\\b/g\n    ];\n\n    frustrationPatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.frustration += Math.min(30, matches.length * 9);\n        mood.stress += Math.min(20, matches.length * 6);\n        mood.focus -= Math.min(15, matches.length * 4);\n      }\n    });\n\n    // Advanced psychological pattern detection\n\n    // Burnout indicators (chronic exhaustion, cynicism, reduced efficacy)\n    const burnoutPatterns = [\n      /\\b(burnout|burnt|exhausted|drained|empty|numb|detached)\\b/g,\n      /\\b(pointless|meaningless|waste|useless|hopeless|give up)\\b/g,\n      /\\b(can't anymore|done|finished|quit|enough|over it)\\b/g\n    ];\n\n    burnoutPatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.burnout += Math.min(25, matches.length * 8);\n        mood.energy -= Math.min(20, matches.length * 6);\n        mood.socialConnection -= Math.min(15, matches.length * 4);\n      }\n    });\n\n    // Imposter syndrome indicators\n    const imposterPatterns = [\n      /\\b(imposter|fake|fraud|pretend|don't belong|not qualified)\\b/g,\n      /\\b(lucky|fluke|accident|mistake|shouldn't be here)\\b/g,\n      /\\b(everyone else|real developer|actual programmer|they'll find out)\\b/g\n    ];\n\n    imposterPatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.imposterSyndrome += Math.min(30, matches.length * 10);\n        mood.confidence -= Math.min(25, matches.length * 8);\n        mood.socialConnection -= Math.min(15, matches.length * 5);\n      }\n    });\n\n    // Flow state indicators (deep focus, time distortion, intrinsic motivation)\n    const flowPatterns = [\n      /\\b(flow|zone|focused|immersed|absorbed|lost track)\\b/g,\n      /\\b(hours flew|time flew|didn't notice|deep dive|tunnel vision)\\b/g,\n      /\\b(in the groove|clicking|smooth|effortless|natural)\\b/g\n    ];\n\n    flowPatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.flowState += Math.min(25, matches.length * 8);\n        mood.focus += Math.min(20, matches.length * 6);\n        mood.energy += Math.min(15, matches.length * 4);\n      }\n    });\n\n    // Social connection indicators\n    const isolationPatterns = [\n      /\\b(alone|lonely|isolated|no one|by myself|solo)\\b/g,\n      /\\b(no help|no support|on my own|figure out myself)\\b/g\n    ];\n\n    const connectionPatterns = [\n      /\\b(team|pair|together|collaborate|help|support)\\b/g,\n      /\\b(mentor|colleague|friend|community|group)\\b/g\n    ];\n\n    isolationPatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.socialConnection -= Math.min(20, matches.length * 7);\n        mood.stress += Math.min(15, matches.length * 5);\n      }\n    });\n\n    connectionPatterns.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.socialConnection += Math.min(15, matches.length * 5);\n        mood.confidence += Math.min(10, matches.length * 3);\n      }\n    });\n\n    // Autonomy indicators (sense of choice and control)\n    const autonomyThreats = [\n      /\\b(have to|must|forced|no choice|required|mandatory)\\b/g,\n      /\\b(policy|rule|regulation|compliance|dictated)\\b/g\n    ];\n\n    const autonomySupport = [\n      /\\b(choose|decide|option|prefer|want|like to)\\b/g,\n      /\\b(flexible|freedom|control|my way|approach)\\b/g\n    ];\n\n    autonomyThreats.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.autonomy -= Math.min(20, matches.length * 6);\n        mood.stress += Math.min(15, matches.length * 4);\n      }\n    });\n\n    autonomySupport.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.autonomy += Math.min(15, matches.length * 5);\n        mood.confidence += Math.min(10, matches.length * 3);\n      }\n    });\n\n    // Mastery indicators (sense of growth and competence)\n    const masteryGrowth = [\n      /\\b(learn|understand|master|improve|better|progress)\\b/g,\n      /\\b(skill|knowledge|expertise|competent|capable)\\b/g,\n      /\\b(figured out|solved|accomplished|achieved|breakthrough)\\b/g\n    ];\n\n    const masteryStagnation = [\n      /\\b(stuck|plateau|same|repeat|boring|routine)\\b/g,\n      /\\b(not learning|not growing|stagnant|static)\\b/g\n    ];\n\n    masteryGrowth.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.mastery += Math.min(20, matches.length * 6);\n        mood.confidence += Math.min(15, matches.length * 4);\n        mood.excitement += Math.min(10, matches.length * 3);\n      }\n    });\n\n    masteryStagnation.forEach(pattern => {\n      const matches = lowerMessage.match(pattern);\n      if (matches) {\n        mood.mastery -= Math.min(15, matches.length * 5);\n        mood.excitement -= Math.min(10, matches.length * 3);\n      }\n    });\n\n    // Context-based adjustments with circadian psychology\n    if (context.timeOfDay !== undefined) {\n      const hour = context.timeOfDay;\n\n      // Late night coding (22:00-06:00) - increased fatigue and stress\n      if (hour > 22 || hour < 6) {\n        mood.energy -= 20;\n        mood.stress += 15;\n        mood.focus -= 10;\n        mood.burnout += 10;\n      }\n\n      // Early morning (06:00-09:00) - variable energy based on chronotype\n      else if (hour >= 6 && hour < 9) {\n        mood.energy += 10;\n        mood.focus += 5;\n      }\n\n      // Peak hours (09:00-11:00, 14:00-16:00) - optimal performance windows\n      else if ((hour >= 9 && hour < 11) || (hour >= 14 && hour < 16)) {\n        mood.focus += 15;\n        mood.flowState += 10;\n      }\n\n      // Post-lunch dip (12:00-14:00) - natural energy decrease\n      else if (hour >= 12 && hour < 14) {\n        mood.energy -= 10;\n        mood.focus -= 5;\n      }\n    }\n\n    // Session duration effects (research-backed thresholds)\n    if (context.sessionDuration) {\n      const hours = context.sessionDuration / (60 * 60 * 1000);\n\n      if (hours > 6) { // Extended sessions increase burnout risk\n        mood.energy -= 30;\n        mood.focus -= 25;\n        mood.burnout += 20;\n        mood.stress += 15;\n      } else if (hours > 4) { // Long sessions reduce effectiveness\n        mood.energy -= 20;\n        mood.focus -= 15;\n        mood.burnout += 10;\n      } else if (hours >= 2 && hours <= 4) { // Optimal session length\n        mood.flowState += 10;\n        mood.focus += 5;\n      }\n    }\n\n    // Recent errors impact (failure tolerance and learning)\n    if (context.recentErrors) {\n      if (context.recentErrors > 10) { // High error rate\n        mood.frustration += 25;\n        mood.confidence -= 20;\n        mood.stress += 15;\n        mood.imposterSyndrome += 15;\n      } else if (context.recentErrors > 5) { // Moderate error rate\n        mood.frustration += 15;\n        mood.confidence -= 10;\n        mood.stress += 10;\n      } else if (context.recentErrors > 0) { // Some errors (normal learning)\n        mood.mastery += 5; // Errors as learning opportunities\n      }\n    }\n\n    // Code complexity effects on cognitive load\n    if (context.codeComplexity) {\n      if (context.codeComplexity > 80) { // High complexity\n        mood.stress += 15;\n        mood.focus -= 10;\n        mood.confidence -= 5;\n      } else if (context.codeComplexity > 60) { // Moderate complexity\n        mood.stress += 10;\n        mood.focus += 5; // Engaging but manageable\n      } else if (context.codeComplexity < 30) { // Low complexity\n        mood.excitement -= 5; // Potentially boring\n        mood.mastery -= 5;\n      }\n    }\n\n    // Ensure all values stay within bounds\n    Object.keys(mood).forEach(key => {\n      mood[key as keyof DeveloperMood] = Math.max(0, Math.min(100, mood[key as keyof DeveloperMood]));\n    });\n\n    return mood;\n  }\n\n  /**\n     * Generate goddess response based on message and detected mood\n     */\n  public generateGoddessResponse(\n    message: string,\n    detectedMood: DeveloperMood,\n    context?: { codeComplexity?: number }\n  ): GoddessResponse {\n    const settings = getGoddessMode();\n    const lowerMsg = message.toLowerCase();\n\n    // Cognitive load and basic needs assessment (Yerkes–Dodson, SDT)\n    const cognitiveLoad = this.assessCognitiveLoad(detectedMood, { codeComplexity: context?.codeComplexity });\n    const needs = {\n      autonomy: this.detectAutonomyNeed(lowerMsg),\n      competence: this.detectCompetenceNeed(lowerMsg),\n      relatedness: this.detectRelatednessNeed(lowerMsg)\n    };\n\n    const response: GoddessResponse = {\n      message: '',\n      tone: 'supportive',\n      emotionalContext: this.describeEmotionalContext(detectedMood)\n    };\n\n    // If mood analysis disabled, provide balanced technical guidance\n    if (!settings.moodAnalysis) {\n      response.tone = 'wise';\n      response.message = this.generateBalancedResponse(message, detectedMood);\n    } else {\n      // Adapt response based on mood and personality with cognitive-load moderation\n      if (detectedMood.stress > 70 || cognitiveLoad === 'high') {\n        response.tone = 'supportive';\n        response.message = this.generateStressReliefResponse(message, detectedMood);\n        response.motivationalElement = settings.empathyLevel >= 50\n          ? 'Let’s take a short reset and break this into 2–3 small steps. You’ve got this.'\n          : 'Break into smaller steps and proceed calmly.';\n      } else if (detectedMood.frustration > 60 || needs.competence) {\n        response.tone = 'encouraging';\n        response.message = this.generateFrustrationResponse(message, detectedMood);\n        response.wisdomSharing = 'Debugging is systematic: reproduce, isolate, fix, verify.';\n      } else if (detectedMood.confidence < 40) {\n        response.tone = 'supportive';\n        response.message = this.generateConfidenceBoostResponse(message, detectedMood);\n        response.motivationalElement = settings.empathyLevel >= 50\n          ? 'Your skills grow with every attempt—steady steps win.'\n          : 'Proceed step by step. You can do this.';\n      } else if (detectedMood.excitement > 70) {\n        response.tone = cognitiveLoad === 'low' ? 'challenging' : 'playful';\n        response.message = this.generateExcitedResponse(message, detectedMood);\n        response.wisdomSharing = 'Channel excitement into clean, incremental commits.';\n      } else {\n        response.tone = 'wise';\n        response.message = this.generateBalancedResponse(message, detectedMood);\n      }\n    }\n\n    // Autonomy support (offer choices)\n    if (needs.autonomy) {\n      const choiceText = settings.empathyLevel >= 50\n        ? 'Would you prefer a quick fix, a robust refactor, or a step-by-step guide?'\n        : 'Choose: quick fix, refactor, or steps.';\n      response.motivationalElement = response.motivationalElement\n        ? `${response.motivationalElement} ${choiceText}`\n        : choiceText;\n    }\n\n    // Relatedness support (we-language)\n    if (needs.relatedness && settings.empathyLevel >= 50) {\n      response.motivationalElement = response.motivationalElement\n        ? `${response.motivationalElement} We’ll handle this together.`\n        : 'We’ll handle this together.';\n    }\n\n    // Personality-driven adjustment\n    if (this.personality.adaptiveLevel > 50 && settings.personalityAdaptation) {\n      response.personalityAdjustment = this.calculatePersonalityAdjustment(detectedMood);\n    }\n\n    // Record interaction for future learning\n    this.userInteractionHistory.push({\n      timestamp: Date.now(),\n      userMessage: message,\n      detectedMood,\n      response,\n      effectiveness: 50\n    });\n\n    return response;\n  }\n\n  /**\n     * Generate stress relief response with contextual adaptation\n     */\n  private generateStressReliefResponse(message: string, mood: DeveloperMood): string {\n    const lowerMsg = message.toLowerCase();\n    const settings = getGoddessMode();\n\n    // Detect specific stress triggers for targeted responses\n    if (lowerMsg.includes('deadline') || lowerMsg.includes('urgent')) {\n      const timeResponses = [\n        'Time pressure can cloud our judgment. Let\\'s prioritize the essential parts first. 🎯',\n        'Deadlines are real, but panic won\\'t help us meet them. Let\\'s work smart, not just fast. ⏰',\n        'I understand the urgency. Let\\'s break this into must-have vs nice-to-have features. 📋'\n      ];\n      return timeResponses[Math.floor(Math.random() * timeResponses.length)];\n    }\n\n    if (lowerMsg.includes('error') || lowerMsg.includes('bug') || lowerMsg.includes('broken')) {\n      const debugResponses = [\n        'Bugs are just puzzles waiting to be solved. Let\\'s trace this step by step. 🔍',\n        'Every error is information. Let\\'s read what the system is trying to tell us. 📖',\n        'I know errors feel personal, but they\\'re just the code\\'s way of asking for help. 🤝'\n      ];\n      return debugResponses[Math.floor(Math.random() * debugResponses.length)];\n    }\n\n    // High empathy responses for severe stress\n    if (mood.stress > 80 && settings.empathyLevel >= 70) {\n      const highEmpathyResponses = [\n        'I can feel how overwhelming this is for you. You\\'re not alone in this struggle. Let\\'s pause and breathe together. 🌸',\n        'Your stress is valid and understandable. Even the best developers face moments like this. We\\'ll get through it. 💙',\n        'Sometimes the kindest thing we can do is step back for a moment. Your wellbeing matters more than any code. 🧘‍♀️'\n      ];\n      return highEmpathyResponses[Math.floor(Math.random() * highEmpathyResponses.length)];\n    }\n\n    // Standard stress relief responses\n    const responses = [\n      'I can sense you\\'re feeling overwhelmed. Let\\'s take this step by step together. 🌸',\n      'Deep breath, dear developer. Every problem has a solution, and we\\'ll find it together. 💙',\n      'Let\\'s break down the problem into smaller, manageable pieces. 🧘‍♀️',\n      'Stress is temporary, but the skills you\\'re building are permanent. Let\\'s tackle this calmly. 🌊'\n    ];\n    return responses[Math.floor(Math.random() * responses.length)];\n  }\n\n  /**\n     * Generate frustration response with pattern recognition\n     */\n  private generateFrustrationResponse(message: string, mood: DeveloperMood): string {\n    const lowerMsg = message.toLowerCase();\n    const settings = getGoddessMode();\n\n    // Detect debugging frustration\n    if (lowerMsg.includes('why') || lowerMsg.includes('how') || lowerMsg.includes('what')) {\n      const debuggingResponses = [\n        'Those \"why\" questions are the heart of debugging. Let\\'s investigate systematically. 🔍',\n        'Confusion is the first step toward understanding. Let\\'s trace through this logic together. 🧩',\n        'Every \"what the heck\" moment is a learning opportunity in disguise. Let\\'s decode this. 🕵️‍♀️'\n      ];\n      return debuggingResponses[Math.floor(Math.random() * debuggingResponses.length)];\n    }\n\n    // Detect repeated failure frustration\n    if (lowerMsg.includes('again') || lowerMsg.includes('still') || lowerMsg.includes('keep')) {\n      const persistenceResponses = [\n        'Persistence in the face of repeated challenges shows real developer grit. Let\\'s try a fresh approach. 🔄',\n        'Sometimes the solution requires a completely different angle. Let\\'s step back and reassess. 🎯',\n        'Repeated attempts aren\\'t failures—they\\'re data points leading us to the answer. 📊'\n      ];\n      return persistenceResponses[Math.floor(Math.random() * persistenceResponses.length)];\n    }\n\n    // High empathy for intense frustration\n    if (mood.frustration > 80 && settings.empathyLevel >= 70) {\n      const highFrustrationResponses = [\n        'I can feel how incredibly frustrated you are right now. That\\'s completely human and valid. 💔',\n        'This level of frustration tells me you really care about getting this right. That passion is valuable. ⚡',\n        'Sometimes we need to honor our frustration before we can move past it. I\\'m here with you. 🤗'\n      ];\n      return highFrustrationResponses[Math.floor(Math.random() * highFrustrationResponses.length)];\n    }\n\n    // Standard frustration responses\n    const responses = [\n      'I feel your frustration, and it\\'s completely valid. Let\\'s approach this from a different angle. 🔄',\n      'Sometimes the best solutions come after the biggest challenges. You\\'re closer than you think! 🎯',\n      'Frustration is just passion with nowhere to go. Let\\'s channel it into solving this problem! ⚡',\n      'Every bug you encounter makes you a stronger developer. Let\\'s squash this one together! 🐛➡️✨'\n    ];\n    return responses[Math.floor(Math.random() * responses.length)];\n  }\n\n  /**\n     * Generate confidence boost response with imposter syndrome awareness\n     */\n  private generateConfidenceBoostResponse(message: string, mood: DeveloperMood): string {\n    const lowerMsg = message.toLowerCase();\n    const settings = getGoddessMode();\n\n    // Detect imposter syndrome patterns\n    if (lowerMsg.includes('imposter') || lowerMsg.includes('fake') || lowerMsg.includes('don\\'t belong')) {\n      const imposterResponses = [\n        'Imposter syndrome affects 70% of developers. You\\'re in excellent company, and you DO belong here. 👑',\n        'The fact that you question your abilities shows self-awareness—a sign of a thoughtful developer. 🧠',\n        'Every expert you admire once felt exactly like you do now. Growth is uncomfortable but necessary. 🌱'\n      ];\n      return imposterResponses[Math.floor(Math.random() * imposterResponses.length)];\n    }\n\n    // Detect knowledge gaps (learning opportunities)\n    if (lowerMsg.includes('don\\'t know') || lowerMsg.includes('confused') || lowerMsg.includes('lost')) {\n      const learningResponses = [\n        'Not knowing something yet is just the beginning of learning it. Every expert started here. 📚',\n        'Confusion is your brain making room for new understanding. Let\\'s fill that space together. 🧩',\n        'The best developers are lifelong learners who embrace not knowing as an opportunity. 🎓'\n      ];\n      return learningResponses[Math.floor(Math.random() * learningResponses.length)];\n    }\n\n    // High empathy for severe confidence issues\n    if (mood.confidence < 20 && settings.empathyLevel >= 70) {\n      const highSupportResponses = [\n        'I see someone who cares deeply about doing good work. That caring is already a strength. 💎',\n        'Your willingness to keep trying despite doubts shows incredible courage and determination. 🦁',\n        'You\\'re being harder on yourself than you\\'d ever be on a friend. Show yourself that same kindness. 🤗'\n      ];\n      return highSupportResponses[Math.floor(Math.random() * highSupportResponses.length)];\n    }\n\n    // Standard confidence responses\n    const responses = [\n      'You\\'re more capable than you realize. Trust in your abilities and let\\'s work through this together. 💎',\n      'Every expert was once a beginner who refused to give up. You\\'re on that same journey! 🚀',\n      'Your willingness to ask questions shows wisdom, not weakness. Let\\'s find the answer together. 🧠',\n      'Confidence grows with every problem solved. Let\\'s add another victory to your collection! 🏆'\n    ];\n    return responses[Math.floor(Math.random() * responses.length)];\n  }\n\n  /**\n     * Generate excited response with energy channeling\n     */\n  private generateExcitedResponse(message: string, mood: DeveloperMood): string {\n    const lowerMsg = message.toLowerCase();\n    const settings = getGoddessMode();\n\n    // Detect breakthrough moments\n    if (lowerMsg.includes('figured') || lowerMsg.includes('solved') || lowerMsg.includes('works')) {\n      const breakthroughResponses = [\n        'That breakthrough feeling is pure developer joy! Let\\'s capture this momentum. 🎉',\n        'You\\'ve cracked the code! This is what makes all the debugging worth it. ⚡',\n        'Victory! Let\\'s document this solution so future you can celebrate again. 📝'\n      ];\n      return breakthroughResponses[Math.floor(Math.random() * breakthroughResponses.length)];\n    }\n\n    // Detect new project excitement\n    if (lowerMsg.includes('new') || lowerMsg.includes('start') || lowerMsg.includes('begin')) {\n      const newProjectResponses = [\n        'New projects are like blank canvases! Let\\'s create something beautiful together. 🎨',\n        'I love that fresh project energy! Let\\'s channel it into solid architecture. 🏗️',\n        'Starting something new is always exciting. Let\\'s make sure we build it right. 🚀'\n      ];\n      return newProjectResponses[Math.floor(Math.random() * newProjectResponses.length)];\n    }\n\n    // High excitement with guidance for sustainable energy\n    if (mood.excitement > 85 && settings.empathyLevel >= 50) {\n      const sustainableExcitementResponses = [\n        'Your enthusiasm is infectious! Let\\'s channel this energy into clean, maintainable code. ⚡✨',\n        'I love your passion! Remember to pace yourself—great code comes from sustained effort. 🌟',\n        'This excitement is wonderful! Let\\'s make sure we document our wins along the way. 📚'\n      ];\n      return sustainableExcitementResponses[Math.floor(Math.random() * sustainableExcitementResponses.length)];\n    }\n\n    // Standard excited responses\n    const responses = [\n      'I love your enthusiasm! Let\\'s harness that energy to create something amazing! ⚡✨',\n      'Your excitement is contagious! Let\\'s channel it into writing beautiful, efficient code! 🎨',\n      'This is the spirit that creates breakthrough innovations! Let\\'s make magic happen! 🪄',\n      'Your passion for coding shines through! Let\\'s turn that excitement into elegant solutions! 🌟'\n    ];\n    return responses[Math.floor(Math.random() * responses.length)];\n  }\n\n  /**\n     * Generate balanced response with contextual awareness\n     */\n  private generateBalancedResponse(message: string, mood: DeveloperMood): string {\n    const lowerMsg = message.toLowerCase();\n\n    // Detect planning/architecture discussions\n    if (lowerMsg.includes('plan') || lowerMsg.includes('design') || lowerMsg.includes('architect')) {\n      const planningResponses = [\n        'Good planning prevents poor performance. Let\\'s think through this architecture together. 🏗️',\n        'I appreciate your thoughtful approach to design. Let\\'s map out the key components. 📐',\n        'Strategic thinking upfront saves debugging time later. What\\'s our main objective? 🎯'\n      ];\n      return planningResponses[Math.floor(Math.random() * planningResponses.length)];\n    }\n\n    // Detect learning/exploration\n    if (lowerMsg.includes('learn') || lowerMsg.includes('understand') || lowerMsg.includes('explore')) {\n      const learningResponses = [\n        'Curiosity is a developer\\'s greatest asset. Let\\'s explore this together. 🔍',\n        'Learning never stops in our field. I\\'m here to guide you through this discovery. 📚',\n        'Understanding the \\'why\\' makes the \\'how\\' so much clearer. Let\\'s dig deeper. 🧠'\n      ];\n      return learningResponses[Math.floor(Math.random() * learningResponses.length)];\n    }\n\n    // Adapt to current focus level\n    if (mood.focus > 80) {\n      const focusedResponses = [\n        'I can sense your deep focus. Let\\'s make the most of this productive flow state. 🌊',\n        'Your concentration is impressive. I\\'ll provide clear, concise guidance. 🎯',\n        'You\\'re in the zone! Let\\'s keep this momentum going with smart next steps. ⚡'\n      ];\n      return focusedResponses[Math.floor(Math.random() * focusedResponses.length)];\n    }\n\n    // Standard balanced responses\n    const responses = [\n      'I\\'m here to help you achieve your coding goals. What would you like to work on together? 🤝',\n      'Let\\'s explore the best approach to solve your challenge. I\\'m ready when you are! 💫',\n      'Your steady approach is admirable. Let\\'s build something great together! 🏗️',\n      'I sense good energy for productive work. Let\\'s make the most of it! 🌊'\n    ];\n    return responses[Math.floor(Math.random() * responses.length)];\n  }\n\n  /**\n     * Describe emotional context\n     */\n  private describeEmotionalContext(mood: DeveloperMood): string {\n    const contexts: string[] = [];\n        \n    if (mood.stress > 60) contexts.push('high stress');\n    if (mood.frustration > 60) contexts.push('frustrated');\n    if (mood.confidence < 40) contexts.push('low confidence');\n    if (mood.energy < 30) contexts.push('low energy');\n    if (mood.excitement > 70) contexts.push('excited');\n    if (mood.focus > 80) contexts.push('highly focused');\n\n    return contexts.length > 0 ? contexts.join(', ') : 'balanced emotional state';\n  }\n\n  /**\n     * Calculate personality adjustment based on interaction\n     */\n  private calculatePersonalityAdjustment(mood: DeveloperMood): Partial<GoddessPersonality> {\n    const adjustment: Partial<GoddessPersonality> = {};\n\n    // Adjust based on what seems to work for this user\n    if (mood.stress > 70) {\n      adjustment.motivationalStyle = 'supportive';\n    } else if (mood.excitement > 70) {\n      adjustment.motivationalStyle = 'challenging';\n    }\n\n    return adjustment;\n  }\n\n  /**\n     * Learn from interaction effectiveness with advanced pattern recognition\n     */\n  public recordInteractionFeedback(\n    interactionId: string,\n    effectiveness: number,\n    userFeedback?: string,\n    mood?: DeveloperMood,\n    context?: { timeOfDay?: number; sessionDuration?: number; codeComplexity?: number }\n  ): void {\n    // Store comprehensive learning data\n    this.learningData.set(interactionId, {\n      effectiveness,\n      userFeedback,\n      timestamp: Date.now(),\n      mood: mood || this.getDefaultMood(),\n      context: context || {}\n    });\n\n    // Learn mood patterns for better future responses\n    if (mood && context) {\n      this.updateMoodPatterns(mood, context, effectiveness);\n    }\n\n    // Adjust personality based on feedback with more nuanced learning\n    if (effectiveness > 80) {\n      // This approach worked well, reinforce it\n      this.personality.adaptiveLevel = Math.min(100, this.personality.adaptiveLevel + 1);\n      if (mood && mood.stress > 70 && effectiveness > 85) {\n        // Stress relief was particularly effective\n        this.personality.emotionalIntelligence = Math.min(100, this.personality.emotionalIntelligence + 2);\n      }\n    } else if (effectiveness < 40) {\n      // This approach didn't work, try adapting more\n      this.personality.adaptiveLevel = Math.min(100, this.personality.adaptiveLevel + 2);\n      if (mood && mood.confidence < 30 && effectiveness < 30) {\n        // Confidence building needs improvement\n        this.personality.motivationalStyle = 'supportive';\n      }\n    }\n\n    Logger.instance.debug(`Goddess personality learning from interaction: ${effectiveness}% effective`);\n  }\n\n  /**\n   * Update mood patterns for predictive emotional intelligence\n   */\n  private updateMoodPatterns(mood: DeveloperMood, context: { timeOfDay?: number; sessionDuration?: number; codeComplexity?: number; recentErrors?: number }, effectiveness: number): void {\n    const now = new Date();\n    const timeOfDay = now.getHours();\n    const dayOfWeek = now.getDay();\n\n    // Find existing pattern or create new one\n    let pattern = this.moodPatterns.find(p =>\n      Math.abs(p.timeOfDay - timeOfDay) <= 1 &&\n      p.dayOfWeek === dayOfWeek\n    );\n\n    if (!pattern) {\n      pattern = {\n        timeOfDay,\n        dayOfWeek,\n        sessionLength: context.sessionDuration || 0,\n        averageMood: { ...mood },\n        triggers: [],\n        effectiveResponses: []\n      };\n      this.moodPatterns.push(pattern);\n    } else {\n      // Update running averages\n      Object.keys(mood).forEach(key => {\n        const moodKey = key as keyof DeveloperMood;\n        if (pattern) {\n          pattern.averageMood[moodKey] = (pattern.averageMood[moodKey] + mood[moodKey]) / 2;\n        }\n      });\n    }\n\n    // Track effective responses\n    if (effectiveness > 75) {\n      const responseType = this.getResponseType(mood);\n      if (!pattern.effectiveResponses.includes(responseType)) {\n        pattern.effectiveResponses.push(responseType);\n      }\n    }\n\n    // Limit pattern storage to prevent memory bloat\n    if (this.moodPatterns.length > 100) {\n      this.moodPatterns = this.moodPatterns.slice(-50);\n    }\n  }\n\n  /**\n   * Get default mood for fallback scenarios\n   */\n  private getDefaultMood(): DeveloperMood {\n    return {\n      stress: 30,\n      confidence: 70,\n      energy: 60,\n      focus: 70,\n      frustration: 20,\n      excitement: 50,\n      burnout: 20,\n      imposterSyndrome: 30,\n      flowState: 50,\n      socialConnection: 60,\n      autonomy: 70,\n      mastery: 60\n    };\n  }\n\n  /**\n   * Determine response type based on mood\n   */\n  private getResponseType(mood: DeveloperMood): string {\n    if (mood.stress > 70) return 'stress-relief';\n    if (mood.frustration > 60) return 'frustration-support';\n    if (mood.confidence < 40) return 'confidence-boost';\n    if (mood.excitement > 70) return 'excitement-channel';\n    if (mood.burnout > 60) return 'burnout-prevention';\n    if (mood.imposterSyndrome > 60) return 'imposter-support';\n    return 'balanced';\n  }\n\n  /**\n     * Get current personality state\n     */\n  public getPersonality(): GoddessPersonality {\n    return { ...this.personality };\n  }\n\n  /**\n     * Update personality settings\n     */\n  public updatePersonality(updates: Partial<GoddessPersonality>): void {\n    this.personality = { ...this.personality, ...updates };\n    Logger.instance.info('Goddess personality updated', updates);\n  }\n}\n\n// Singleton instance\nexport const goddessPersonalityEngine = new GoddessPersonalityEngine();\n"]}