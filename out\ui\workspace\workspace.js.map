{"version": 3, "file": "workspace.js", "sourceRoot": "", "sources": ["../../../src/ui/workspace/workspace.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC;;GAEG;AACH,MAAa,eAAe;IAC1B;;OAEG;IACI,MAAM,CAAC,sBAAsB;QAClC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAE9C,OAAO;YACL,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;YAC5C,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;gBAClB,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;gBACrD,KAAK,EAAE;oBACL,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;oBACvD,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;iBACpD;aACF,CAAC,CAAC,CAAC,SAAS;YACb,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,EAAE,CAAC;iBAClE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;SACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,kBAAkB;QAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,kBAAkB;QAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;IACpC,CAAC;CACF;AA5CD,0CA4CC", "sourcesContent": ["import * as vscode from 'vscode';\n\n/**\n * Helper functions for workspace context\n */\nexport class WorkspaceHelper {\n  /**\n   * Create workspace context with proper types\n   */\n  public static createWorkspaceContext() {\n    const editor = vscode.window.activeTextEditor;\n    \n    return {\n      currentFile: editor?.document.uri.toString(),\n      selection: editor ? { \n        text: editor.document.getText(editor.selection) || '',\n        range: { \n          start: editor.document.offsetAt(editor.selection.start), \n          end: editor.document.offsetAt(editor.selection.end) \n        }\n      } : undefined,\n      workspaceFolders: Array.from(vscode.workspace.workspaceFolders ?? [])\n        .map(folder => folder.uri.toString())\n    };\n  }\n  \n  /**\n   * Get the text content of the current file\n   */\n  public static getCurrentFileText(): string | undefined {\n    const editor = vscode.window.activeTextEditor;\n    if (!editor) {\n      return undefined;\n    }\n    \n    return editor.document.getText();\n  }\n  \n  /**\n   * Get the path of the current file\n   */\n  public static getCurrentFilePath(): string | undefined {\n    const editor = vscode.window.activeTextEditor;\n    if (!editor) {\n      return undefined;\n    }\n    \n    return editor.document.uri.fsPath;\n  }\n}"]}