"use strict";
/**
 * Codessa Operation Mode Workflows
 *
 * This module provides workflow templates for different operation modes:
 * - Research Mode
 * - UI-UX Mode
 * - Documentation Mode
 * - Technical Debt Mode
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createResearchWorkflow = createResearchWorkflow;
exports.createUIUXWorkflow = createUIUXWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
/**
 * Create a Research workflow for in-depth research tasks
 */
function createResearchWorkflow(id, name, description, researchAgent, analysisAgent, summaryAgent, tools = []) {
    logger_1.logger.info(`Creating Research workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const queryAnalysisNode = graph_1.Codessa.createAgentNode('query-analysis', 'Query Analysis', researchAgent);
    const informationRetrievalNode = graph_1.Codessa.createAgentNode('information-retrieval', 'Information Retrieval', researchAgent);
    const sourceEvaluationNode = graph_1.Codessa.createAgentNode('source-evaluation', 'Source Evaluation', analysisAgent);
    const dataAnalysisNode = graph_1.Codessa.createAgentNode('data-analysis', 'Data Analysis', analysisAgent);
    const synthesisNode = graph_1.Codessa.createAgentNode('synthesis', 'Synthesis', summaryAgent);
    const conclusionNode = graph_1.Codessa.createAgentNode('conclusion', 'Conclusion', summaryAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },
        { name: 'query-to-retrieval', source: 'query-analysis', target: 'information-retrieval', type: 'default' },
        { name: 'retrieval-to-evaluation', source: 'information-retrieval', target: 'source-evaluation', type: 'default' },
        { name: 'evaluation-to-analysis', source: 'source-evaluation', target: 'data-analysis', type: 'default' },
        { name: 'analysis-to-synthesis', source: 'data-analysis', target: 'synthesis', type: 'default' },
        { name: 'synthesis-to-conclusion', source: 'synthesis', target: 'conclusion', type: 'default' },
        { name: 'conclusion-to-output', source: 'conclusion', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect information retrieval to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `retrieval-to-tool-${index}`,
                source: 'information-retrieval',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to data analysis
            edges.push({
                name: `tool-${index}-to-analysis`,
                source: `tool-${index}`,
                target: 'data-analysis',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            queryAnalysisNode,
            informationRetrievalNode,
            sourceEvaluationNode,
            dataAnalysisNode,
            synthesisNode,
            conclusionNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'research',
        tags: ['research', 'analysis', 'information-retrieval']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a UI-UX workflow for design and user experience tasks
 */
function createUIUXWorkflow(id, name, description, designerAgent, developerAgent, userResearchAgent, tools = []) {
    logger_1.logger.info(`Creating UI-UX workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const requirementsAnalysisNode = graph_1.Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', userResearchAgent);
    const userResearchNode = graph_1.Codessa.createAgentNode('user-research', 'User Research', userResearchAgent);
    const wireframingNode = graph_1.Codessa.createAgentNode('wireframing', 'Wireframing', designerAgent);
    const designSystemNode = graph_1.Codessa.createAgentNode('design-system', 'Design System', designerAgent);
    const prototypeNode = graph_1.Codessa.createAgentNode('prototype', 'Prototype', designerAgent);
    const usabilityTestingNode = graph_1.Codessa.createAgentNode('usability-testing', 'Usability Testing', userResearchAgent);
    const implementationNode = graph_1.Codessa.createAgentNode('implementation', 'Implementation', developerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },
        { name: 'requirements-to-research', source: 'requirements-analysis', target: 'user-research', type: 'default' },
        { name: 'research-to-wireframing', source: 'user-research', target: 'wireframing', type: 'default' },
        { name: 'wireframing-to-design-system', source: 'wireframing', target: 'design-system', type: 'default' },
        { name: 'design-system-to-prototype', source: 'design-system', target: 'prototype', type: 'default' },
        { name: 'prototype-to-testing', source: 'prototype', target: 'usability-testing', type: 'default' },
        { name: 'testing-to-implementation', source: 'usability-testing', target: 'implementation', type: 'default' },
        { name: 'implementation-to-output', source: 'implementation', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'testing-to-wireframing', source: 'usability-testing', target: 'wireframing', type: 'feedback' },
        { name: 'implementation-to-testing', source: 'implementation', target: 'usability-testing', type: 'feedback' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect prototype to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `prototype-to-tool-${index}`,
                source: 'prototype',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to usability testing
            edges.push({
                name: `tool-${index}-to-testing`,
                source: `tool-${index}`,
                target: 'usability-testing',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            requirementsAnalysisNode,
            userResearchNode,
            wireframingNode,
            designSystemNode,
            prototypeNode,
            usabilityTestingNode,
            implementationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'ui-ux',
        tags: ['ui', 'ux', 'design', 'usability']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=operationModeWorkflows.js.map