2025-04-22 14:11:43.048 [info] [main] Log level: Info
2025-04-22 14:11:43.048 [info] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-04-22 14:11:43.105 [info] [main] Using git "2.47.1.windows.1" from "C:\Program Files\Git\cmd\git.exe"
2025-04-22 14:11:43.105 [info] [Model][doInitialScan] Initial repository scan started
2025-04-22 14:11:43.183 [info] > git rev-parse --show-toplevel [65ms]
2025-04-22 14:11:43.232 [info] > git rev-parse --git-dir --git-common-dir [45ms]
2025-04-22 14:11:43.240 [info] [Model][openRepository] Opened repository: e:\_2025_Coding_Projects\AI\Codessa
2025-04-22 14:11:43.313 [info] > git config --get commit.template [64ms]
2025-04-22 14:11:43.313 [info] > git rev-parse --show-toplevel [60ms]
2025-04-22 14:11:43.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [58ms]
2025-04-22 14:11:43.372 [info] > git rev-parse --show-toplevel [55ms]
2025-04-22 14:11:43.386 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-04-22 14:11:43.396 [info] > git status -z -uall [69ms]
2025-04-22 14:11:43.434 [info] > git rev-parse --show-toplevel [58ms]
2025-04-22 14:11:43.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [69ms]
2025-04-22 14:11:43.498 [info] > git config --get commit.template [63ms]
2025-04-22 14:11:43.529 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [86ms]
2025-04-22 14:11:43.529 [info] > git rev-parse --show-toplevel [91ms]
2025-04-22 14:11:43.551 [info] > git config --get --local branch.main.vscode-merge-base [54ms]
2025-04-22 14:11:43.595 [info] > git rev-parse --show-toplevel [62ms]
2025-04-22 14:11:43.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [57ms]
2025-04-22 14:11:43.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/origin/main refs/remotes/origin/main [53ms]
2025-04-22 14:11:43.615 [info] > git status -z -uall [77ms]
2025-04-22 14:11:43.649 [info] > git rev-parse --show-toplevel [51ms]
2025-04-22 14:11:43.667 [info] > git merge-base refs/heads/main refs/remotes/origin/main [52ms]
2025-04-22 14:11:43.678 [info] > git merge-base refs/heads/main refs/remotes/origin/main [50ms]
2025-04-22 14:11:43.702 [info] > git rev-parse --show-toplevel [50ms]
2025-04-22 14:11:43.747 [info] > git diff --name-status -z --diff-filter=ADMR 0967e5d317a51da99dae242568a43fdf3ffe783c...refs/remotes/origin/main [76ms]
2025-04-22 14:11:43.749 [info] > git diff --name-status -z --diff-filter=ADMR 0967e5d317a51da99dae242568a43fdf3ffe783c...refs/remotes/origin/main [66ms]
2025-04-22 14:11:43.750 [info] > git rev-parse --show-toplevel [44ms]
2025-04-22 14:11:43.796 [info] > git rev-parse --show-toplevel [44ms]
2025-04-22 14:11:43.852 [info] > git rev-parse --show-toplevel [52ms]
2025-04-22 14:11:43.855 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-04-22 14:11:43.855 [info] > git check-ignore -v -z --stdin [61ms]
2025-04-22 14:11:47.217 [info] > git config --get commit.template [49ms]
2025-04-22 14:11:47.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [46ms]
2025-04-22 14:11:47.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [45ms]
2025-04-22 14:11:47.279 [info] > git status -z -uall [56ms]
2025-04-22 14:11:52.343 [info] > git config --get commit.template [47ms]
2025-04-22 14:11:52.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [45ms]
2025-04-22 14:11:52.398 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [45ms]
2025-04-22 14:11:52.404 [info] > git status -z -uall [55ms]
2025-04-22 14:12:05.681 [info] > git config --get commit.template [47ms]
2025-04-22 14:12:05.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [46ms]
2025-04-22 14:12:05.737 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [44ms]
2025-04-22 14:12:05.744 [info] > git status -z -uall [56ms]
2025-04-22 14:12:17.558 [info] > git config --get commit.template [52ms]
2025-04-22 14:12:17.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [51ms]
2025-04-22 14:12:17.618 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [48ms]
2025-04-22 14:12:17.627 [info] > git status -z -uall [61ms]
2025-04-22 14:12:28.748 [info] > git config --get commit.template [51ms]
2025-04-22 14:12:28.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [49ms]
2025-04-22 14:12:28.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [49ms]
2025-04-22 14:12:28.814 [info] > git status -z -uall [61ms]
2025-04-22 14:12:33.883 [info] > git config --get commit.template [50ms]
2025-04-22 14:12:33.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [56ms]
2025-04-22 14:12:33.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [64ms]
2025-04-22 14:12:33.973 [info] > git status -z -uall [75ms]
2025-04-22 14:12:39.121 [info] > git config --get commit.template [131ms]
2025-04-22 14:12:39.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [154ms]
2025-04-22 14:12:39.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [77ms]
2025-04-22 14:12:39.252 [info] > git status -z -uall [99ms]
2025-04-22 14:12:50.172 [info] > git config --get commit.template [88ms]
2025-04-22 14:12:50.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [95ms]
2025-04-22 14:12:50.269 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [75ms]
2025-04-22 14:12:50.288 [info] > git status -z -uall [99ms]
2025-04-22 14:12:55.366 [info] > git config --get commit.template [48ms]
2025-04-22 14:12:55.368 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [46ms]
2025-04-22 14:12:55.425 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-04-22 14:12:55.442 [info] > git status -z -uall [71ms]
2025-04-22 19:46:38.471 [info] > git config --get commit.template [52ms]
2025-04-22 19:46:38.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [57ms]
2025-04-22 19:46:38.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-04-22 19:46:38.648 [info] > git status -z -uall [161ms]
2025-04-22 19:46:43.718 [info] > git config --get commit.template [47ms]
2025-04-22 19:46:43.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [45ms]
2025-04-22 19:46:43.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [42ms]
2025-04-22 19:46:43.776 [info] > git status -z -uall [53ms]
2025-04-22 19:46:47.078 [info] > git check-ignore -v -z --stdin [64ms]
2025-04-22 19:46:48.853 [info] > git config --get commit.template [50ms]
2025-04-22 19:46:48.858 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [50ms]
2025-04-22 19:46:48.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-04-22 19:46:48.931 [info] > git status -z -uall [68ms]
2025-04-22 19:46:53.502 [info] > git ls-files --stage -- src/extension.ts [66ms]
2025-04-22 19:46:53.508 [info] > git show --textconv :src/extension.ts [78ms]
2025-04-22 19:46:53.561 [info] > git cat-file -s a3cfbac8325b330b50c26072d38d5f359ca8e3a7 [55ms]
2025-04-22 19:46:53.693 [info] > git check-ignore -v -z --stdin [48ms]
2025-04-22 19:46:54.012 [info] > git config --get commit.template [58ms]
2025-04-22 19:46:54.018 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [59ms]
2025-04-22 19:46:54.079 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-04-22 19:46:54.084 [info] > git status -z -uall [61ms]
2025-04-22 19:46:54.875 [info] > git blame --root --incremental 0967e5d317a51da99dae242568a43fdf3ffe783c -- src/extension.ts [1274ms]
2025-04-22 19:46:59.184 [info] > git config --get commit.template [71ms]
2025-04-22 19:46:59.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [70ms]
2025-04-22 19:46:59.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [72ms]
2025-04-22 19:46:59.283 [info] > git status -z -uall [89ms]
2025-04-22 19:47:06.589 [info] > git config --get commit.template [48ms]
2025-04-22 19:47:06.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [47ms]
2025-04-22 19:47:06.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [48ms]
2025-04-22 19:47:06.652 [info] > git status -z -uall [56ms]
2025-04-22 19:47:11.717 [info] > git config --get commit.template [46ms]
2025-04-22 19:47:11.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [43ms]
2025-04-22 19:47:11.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [43ms]
2025-04-22 19:47:11.775 [info] > git status -z -uall [52ms]
2025-04-22 19:47:41.573 [info] > git config --get commit.template [44ms]
2025-04-22 19:47:41.579 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [46ms]
2025-04-22 19:47:41.637 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-04-22 19:47:41.643 [info] > git status -z -uall [60ms]
2025-04-22 19:48:07.292 [info] > git config --get commit.template [45ms]
2025-04-22 19:48:07.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [45ms]
2025-04-22 19:48:07.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [45ms]
2025-04-22 19:48:07.357 [info] > git status -z -uall [57ms]
2025-04-22 19:49:13.566 [info] > git config --get commit.template [76ms]
2025-04-22 19:49:13.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [75ms]
2025-04-22 19:49:13.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [57ms]
2025-04-22 19:49:13.639 [info] > git status -z -uall [65ms]
2025-04-22 19:53:16.666 [info] > git ls-files --stage -- node_modules/@types/vscode/index.d.ts [107ms]
2025-04-22 19:53:16.692 [info] > git show --textconv :node_modules/@types/vscode/index.d.ts [140ms]
2025-04-22 19:53:16.734 [info] > git cat-file -s e7b4f490edb9701c15044c02d79becc5db6ad54d [63ms]
2025-04-22 19:53:16.855 [info] > git check-ignore -v -z --stdin [59ms]
2025-04-22 19:53:16.912 [info] > git blame --root --incremental 0967e5d317a51da99dae242568a43fdf3ffe783c -- node_modules/@types/vscode/index.d.ts [122ms]
2025-04-22 19:53:41.750 [info] > git config --get commit.template [51ms]
2025-04-22 19:53:41.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [50ms]
2025-04-22 19:53:41.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-04-22 19:53:41.815 [info] > git status -z -uall [56ms]
2025-04-22 19:53:46.879 [info] > git config --get commit.template [47ms]
2025-04-22 19:53:46.882 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [47ms]
2025-04-22 19:53:46.939 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-04-22 19:53:46.944 [info] > git status -z -uall [58ms]
2025-04-22 19:55:41.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [56ms]
2025-04-22 19:55:41.736 [info] > git config --get commit.template [69ms]
2025-04-22 19:55:41.805 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [58ms]
2025-04-22 19:55:41.809 [info] > git status -z -uall [68ms]
2025-04-22 19:56:41.696 [info] > git config --get commit.template [63ms]
2025-04-22 19:56:41.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [65ms]
2025-04-22 19:56:41.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [49ms]
2025-04-22 19:56:41.772 [info] > git status -z -uall [63ms]
2025-04-22 19:56:44.523 [info] > git show --textconv :src/extension.ts [107ms]
2025-04-22 19:56:44.524 [info] > git ls-files --stage -- src/extension.ts [101ms]
2025-04-22 19:56:44.586 [info] > git cat-file -s a3cfbac8325b330b50c26072d38d5f359ca8e3a7 [59ms]
2025-04-22 19:57:37.725 [info] > git check-ignore -v -z --stdin [59ms]
2025-04-22 19:57:41.674 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [50ms]
2025-04-22 19:57:41.676 [info] > git config --get commit.template [58ms]
2025-04-22 19:57:41.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 19:57:41.744 [info] > git status -z -uall [66ms]
2025-04-22 19:57:54.274 [info] > git check-ignore -v -z --stdin [116ms]
2025-04-22 19:58:41.687 [info] > git config --get commit.template [44ms]
2025-04-22 19:58:41.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [44ms]
2025-04-22 19:58:41.744 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [45ms]
2025-04-22 19:58:41.752 [info] > git status -z -uall [58ms]
2025-04-22 19:58:59.047 [info] > git check-ignore -v -z --stdin [49ms]
2025-04-22 19:58:59.957 [info] > git config --get commit.template [44ms]
2025-04-22 19:58:59.961 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [44ms]
2025-04-22 19:59:00.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-04-22 19:59:00.029 [info] > git status -z -uall [66ms]
2025-04-22 19:59:41.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [47ms]
2025-04-22 19:59:41.720 [info] > git config --get commit.template [54ms]
2025-04-22 19:59:41.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [43ms]
2025-04-22 19:59:41.778 [info] > git status -z -uall [56ms]
2025-04-22 20:00:41.728 [info] > git config --get commit.template [47ms]
2025-04-22 20:00:41.732 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [46ms]
2025-04-22 20:00:41.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [46ms]
2025-04-22 20:00:41.792 [info] > git status -z -uall [56ms]
2025-04-22 20:00:59.007 [info] > git config --get commit.template [48ms]
2025-04-22 20:00:59.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [47ms]
2025-04-22 20:00:59.059 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [42ms]
2025-04-22 20:00:59.068 [info] > git status -z -uall [54ms]
2025-04-22 20:01:04.160 [info] > git config --get commit.template [65ms]
2025-04-22 20:01:04.163 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [63ms]
2025-04-22 20:01:04.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-04-22 20:01:04.224 [info] > git status -z -uall [57ms]
2025-04-22 20:01:27.665 [info] > git config --get commit.template [56ms]
2025-04-22 20:01:27.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [57ms]
2025-04-22 20:01:27.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 20:01:27.739 [info] > git status -z -uall [61ms]
2025-04-22 20:01:39.060 [info] > git check-ignore -v -z --stdin [45ms]
2025-04-22 20:01:41.732 [info] > git config --get commit.template [47ms]
2025-04-22 20:01:41.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [46ms]
2025-04-22 20:01:41.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [45ms]
2025-04-22 20:01:41.794 [info] > git status -z -uall [57ms]
2025-04-22 20:03:11.128 [info] > git config --get commit.template [108ms]
2025-04-22 20:03:11.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [103ms]
2025-04-22 20:03:11.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-04-22 20:03:11.208 [info] > git status -z -uall [69ms]
2025-04-22 20:03:16.288 [info] > git config --get commit.template [60ms]
2025-04-22 20:03:16.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [59ms]
2025-04-22 20:03:16.360 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [56ms]
2025-04-22 20:03:16.369 [info] > git status -z -uall [71ms]
2025-04-22 20:04:41.782 [info] > git config --get commit.template [67ms]
2025-04-22 20:04:41.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [78ms]
2025-04-22 20:04:41.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-04-22 20:04:41.872 [info] > git status -z -uall [65ms]
2025-04-22 20:05:41.808 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [47ms]
2025-04-22 20:05:41.816 [info] > git config --get commit.template [59ms]
2025-04-22 20:05:41.875 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 20:05:41.880 [info] > git status -z -uall [61ms]
2025-04-22 20:05:51.044 [info] > git config --get commit.template [51ms]
2025-04-22 20:05:51.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [50ms]
2025-04-22 20:05:51.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 20:05:51.116 [info] > git status -z -uall [65ms]
2025-04-22 20:06:41.821 [info] > git config --get commit.template [60ms]
2025-04-22 20:06:41.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [59ms]
2025-04-22 20:06:41.893 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [59ms]
2025-04-22 20:06:41.899 [info] > git status -z -uall [69ms]
2025-04-22 20:09:17.119 [info] > git config --get commit.template [160ms]
2025-04-22 20:09:17.158 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [168ms]
2025-04-22 20:09:17.453 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [238ms]
2025-04-22 20:09:17.507 [info] > git status -z -uall [332ms]
2025-04-22 20:09:42.029 [info] > git config --get commit.template [170ms]
2025-04-22 20:09:42.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [179ms]
2025-04-22 20:09:42.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [100ms]
2025-04-22 20:09:42.190 [info] > git status -z -uall [132ms]
2025-04-22 20:09:47.384 [info] > git config --get commit.template [132ms]
2025-04-22 20:09:47.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [129ms]
2025-04-22 20:09:47.592 [info] > git status -z -uall [197ms]
2025-04-22 20:09:47.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [211ms]
2025-04-22 20:10:41.881 [info] > git config --get commit.template [47ms]
2025-04-22 20:10:41.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [51ms]
2025-04-22 20:10:41.945 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-04-22 20:10:41.955 [info] > git status -z -uall [62ms]
2025-04-22 20:11:41.923 [info] > git config --get commit.template [50ms]
2025-04-22 20:11:41.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [51ms]
2025-04-22 20:11:41.983 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-04-22 20:11:41.992 [info] > git status -z -uall [59ms]
2025-04-22 20:12:20.265 [info] > git config --get commit.template [49ms]
2025-04-22 20:12:20.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [50ms]
2025-04-22 20:12:20.341 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [61ms]
2025-04-22 20:12:20.350 [info] > git status -z -uall [74ms]
2025-04-22 20:12:41.898 [info] > git config --get commit.template [49ms]
2025-04-22 20:12:41.902 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [49ms]
2025-04-22 20:12:41.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-04-22 20:12:41.964 [info] > git status -z -uall [56ms]
2025-04-22 20:19:54.029 [info] > git config --get commit.template [84ms]
2025-04-22 20:19:54.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [86ms]
2025-04-22 20:19:54.103 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-04-22 20:19:54.109 [info] > git status -z -uall [63ms]
2025-04-22 20:20:06.912 [info] > git config --get commit.template [57ms]
2025-04-22 20:20:06.916 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [57ms]
2025-04-22 20:20:06.974 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-04-22 20:20:06.984 [info] > git status -z -uall [65ms]
2025-04-22 20:20:26.209 [info] > git config --get commit.template [53ms]
2025-04-22 20:20:26.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [56ms]
2025-04-22 20:20:26.281 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [57ms]
2025-04-22 20:20:26.285 [info] > git status -z -uall [64ms]
2025-04-22 20:20:31.350 [info] > git config --get commit.template [48ms]
2025-04-22 20:20:31.354 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [47ms]
2025-04-22 20:20:31.406 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [45ms]
2025-04-22 20:20:31.416 [info] > git status -z -uall [59ms]
2025-04-22 20:20:36.490 [info] > git config --get commit.template [62ms]
2025-04-22 20:20:36.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [65ms]
2025-04-22 20:20:36.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [61ms]
2025-04-22 20:20:36.581 [info] > git status -z -uall [79ms]
2025-04-22 20:20:41.964 [info] > git config --get commit.template [60ms]
2025-04-22 20:20:41.970 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [60ms]
2025-04-22 20:20:42.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [61ms]
2025-04-22 20:20:42.050 [info] > git status -z -uall [77ms]
2025-04-22 20:25:29.484 [info] > git config --get commit.template [94ms]
2025-04-22 20:25:29.490 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [94ms]
2025-04-22 20:25:29.550 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-04-22 20:25:29.558 [info] > git status -z -uall [62ms]
2025-04-22 20:25:34.660 [info] > git config --get commit.template [76ms]
2025-04-22 20:25:34.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [74ms]
2025-04-22 20:25:34.729 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-04-22 20:25:34.735 [info] > git status -z -uall [65ms]
2025-04-22 20:28:23.245 [info] > git config --get commit.template [65ms]
2025-04-22 20:28:23.254 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [67ms]
2025-04-22 20:28:23.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [71ms]
2025-04-22 20:28:23.341 [info] > git status -z -uall [83ms]
2025-04-22 20:28:28.415 [info] > git config --get commit.template [60ms]
2025-04-22 20:28:28.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [64ms]
2025-04-22 20:28:28.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [68ms]
2025-04-22 20:28:28.510 [info] > git status -z -uall [81ms]
2025-04-22 20:28:33.576 [info] > git config --get commit.template [51ms]
2025-04-22 20:28:33.577 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [48ms]
2025-04-22 20:28:33.643 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-04-22 20:28:33.652 [info] > git status -z -uall [69ms]
2025-04-22 20:29:24.803 [info] > git config --get commit.template [49ms]
2025-04-22 20:29:24.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [48ms]
2025-04-22 20:29:24.859 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [44ms]
2025-04-22 20:29:24.867 [info] > git status -z -uall [57ms]
2025-04-22 20:29:29.929 [info] > git config --get commit.template [51ms]
2025-04-22 20:29:29.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [50ms]
2025-04-22 20:29:29.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [48ms]
2025-04-22 20:29:29.994 [info] > git status -z -uall [57ms]
2025-04-22 20:56:13.753 [info] > git config --get commit.template [146ms]
2025-04-22 20:56:13.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [144ms]
2025-04-22 20:56:13.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 20:56:13.833 [info] > git status -z -uall [63ms]
2025-04-22 20:56:18.905 [info] > git config --get commit.template [56ms]
2025-04-22 20:56:18.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [55ms]
2025-04-22 20:56:18.973 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-04-22 20:56:18.979 [info] > git status -z -uall [65ms]
2025-04-22 20:56:36.694 [info] > git config --get commit.template [66ms]
2025-04-22 20:56:36.703 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [72ms]
2025-04-22 20:56:36.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [77ms]
2025-04-22 20:56:36.798 [info] > git status -z -uall [89ms]
2025-04-22 20:56:41.870 [info] > git config --get commit.template [55ms]
2025-04-22 20:56:41.879 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [57ms]
2025-04-22 20:56:41.953 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [64ms]
2025-04-22 20:56:41.959 [info] > git status -z -uall [76ms]
2025-04-22 20:56:47.070 [info] > git config --get commit.template [93ms]
2025-04-22 20:56:47.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [100ms]
2025-04-22 20:56:47.206 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [105ms]
2025-04-22 20:56:47.288 [info] > git status -z -uall [197ms]
2025-04-22 20:59:05.312 [info] > git config --get commit.template [113ms]
2025-04-22 20:59:05.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [118ms]
2025-04-22 20:59:05.454 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [102ms]
2025-04-22 20:59:05.515 [info] > git status -z -uall [173ms]
2025-04-22 20:59:10.612 [info] > git config --get commit.template [64ms]
2025-04-22 20:59:10.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [66ms]
2025-04-22 20:59:10.704 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [73ms]
2025-04-22 20:59:10.717 [info] > git status -z -uall [91ms]
2025-04-22 20:59:15.785 [info] > git config --get commit.template [49ms]
2025-04-22 20:59:15.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [50ms]
2025-04-22 20:59:15.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-04-22 20:59:15.858 [info] > git status -z -uall [64ms]
2025-04-22 20:59:20.931 [info] > git config --get commit.template [53ms]
2025-04-22 20:59:20.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [53ms]
2025-04-22 20:59:20.994 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-04-22 20:59:21.001 [info] > git status -z -uall [62ms]
2025-04-22 20:59:26.080 [info] > git config --get commit.template [51ms]
2025-04-22 20:59:26.085 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [52ms]
2025-04-22 20:59:26.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-04-22 20:59:26.151 [info] > git status -z -uall [62ms]
2025-04-22 20:59:31.231 [info] > git config --get commit.template [64ms]
2025-04-22 20:59:31.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [62ms]
2025-04-22 20:59:31.306 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [60ms]
2025-04-22 20:59:31.319 [info] > git status -z -uall [78ms]
2025-04-22 20:59:35.323 [info] > git show --textconv :src/extension.ts [91ms]
2025-04-22 20:59:35.325 [info] > git ls-files --stage -- src/extension.ts [85ms]
2025-04-22 20:59:35.402 [info] > git cat-file -s a3cfbac8325b330b50c26072d38d5f359ca8e3a7 [70ms]
2025-04-22 20:59:36.401 [info] > git config --get commit.template [66ms]
2025-04-22 20:59:36.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [67ms]
2025-04-22 20:59:36.467 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 20:59:36.478 [info] > git status -z -uall [66ms]
2025-04-22 21:00:04.386 [info] > git config --get commit.template [72ms]
2025-04-22 21:00:04.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [71ms]
2025-04-22 21:00:04.476 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [74ms]
2025-04-22 21:00:04.491 [info] > git status -z -uall [95ms]
2025-04-22 21:00:09.563 [info] > git config --get commit.template [48ms]
2025-04-22 21:00:09.566 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [47ms]
2025-04-22 21:00:09.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-04-22 21:00:09.631 [info] > git status -z -uall [63ms]
2025-04-22 21:00:22.033 [info] > git config --get commit.template [55ms]
2025-04-22 21:00:22.038 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [54ms]
2025-04-22 21:00:22.100 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-04-22 21:00:22.107 [info] > git status -z -uall [66ms]
2025-04-22 21:00:27.174 [info] > git config --get commit.template [51ms]
2025-04-22 21:00:27.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [54ms]
2025-04-22 21:00:27.237 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [48ms]
2025-04-22 21:00:27.248 [info] > git status -z -uall [63ms]
2025-04-22 21:13:07.747 [info] > git config --get commit.template [93ms]
2025-04-22 21:13:07.752 [info] > git ls-files --stage -- src/extension.ts [86ms]
2025-04-22 21:13:07.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [81ms]
2025-04-22 21:13:07.807 [info] > git cat-file -s a3cfbac8325b330b50c26072d38d5f359ca8e3a7 [52ms]
2025-04-22 21:13:07.818 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 21:13:07.826 [info] > git status -z -uall [64ms]
2025-04-22 21:13:07.864 [info] > git show --textconv :src/extension.ts [51ms]
2025-04-22 21:13:12.892 [info] > git config --get commit.template [48ms]
2025-04-22 21:13:12.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [48ms]
2025-04-22 21:13:12.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-04-22 21:13:12.968 [info] > git status -z -uall [69ms]
2025-04-22 21:13:24.232 [info] > git config --get commit.template [60ms]
2025-04-22 21:13:24.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [59ms]
2025-04-22 21:13:24.300 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 21:13:24.306 [info] > git status -z -uall [65ms]
2025-04-22 21:13:29.365 [info] > git config --get commit.template [48ms]
2025-04-22 21:13:29.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [49ms]
2025-04-22 21:13:29.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-04-22 21:13:29.434 [info] > git status -z -uall [61ms]
2025-04-22 21:13:38.561 [info] > git config --get commit.template [73ms]
2025-04-22 21:13:38.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [71ms]
2025-04-22 21:13:38.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-04-22 21:13:38.648 [info] > git status -z -uall [79ms]
2025-04-22 21:14:07.781 [info] > git check-ignore -v -z --stdin [53ms]
2025-04-22 21:14:20.511 [info] > git config --get commit.template [63ms]
2025-04-22 21:14:20.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [70ms]
2025-04-22 21:14:20.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [59ms]
2025-04-22 21:14:20.598 [info] > git status -z -uall [72ms]
2025-04-22 21:14:25.661 [info] > git config --get commit.template [51ms]
2025-04-22 21:14:25.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [53ms]
2025-04-22 21:14:25.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-04-22 21:14:25.736 [info] > git status -z -uall [63ms]
2025-04-22 21:14:33.067 [info] > git check-ignore -v -z --stdin [47ms]
2025-04-22 21:14:34.001 [info] > git config --get commit.template [67ms]
2025-04-22 21:14:34.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [68ms]
2025-04-22 21:14:34.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-04-22 21:14:34.078 [info] > git status -z -uall [67ms]
2025-04-22 21:14:39.141 [info] > git config --get commit.template [47ms]
2025-04-22 21:14:39.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [48ms]
2025-04-22 21:14:39.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [49ms]
2025-04-22 21:14:39.208 [info] > git status -z -uall [57ms]
2025-04-22 21:15:24.942 [info] > git config --get commit.template [47ms]
2025-04-22 21:15:24.949 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [47ms]
2025-04-22 21:15:25.004 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [48ms]
2025-04-22 21:15:25.012 [info] > git status -z -uall [60ms]
2025-04-22 21:17:03.010 [info] > git config --get commit.template [52ms]
2025-04-22 21:17:03.012 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [50ms]
2025-04-22 21:17:03.065 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [44ms]
2025-04-22 21:17:03.074 [info] > git status -z -uall [59ms]
2025-04-22 21:22:02.974 [info] > git config --get commit.template [64ms]
2025-04-22 21:22:02.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [61ms]
2025-04-22 21:22:03.055 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [65ms]
2025-04-22 21:22:03.066 [info] > git status -z -uall [80ms]
2025-04-22 21:22:08.161 [info] > git config --get commit.template [65ms]
2025-04-22 21:22:08.164 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [60ms]
2025-04-22 21:22:08.240 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-04-22 21:22:08.251 [info] > git status -z -uall [79ms]
2025-04-22 21:46:25.060 [info] > git config --get commit.template [60ms]
2025-04-22 21:46:25.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [60ms]
2025-04-22 21:46:25.140 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [65ms]
2025-04-22 21:46:25.148 [info] > git status -z -uall [79ms]
2025-04-22 21:46:30.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [65ms]
2025-04-22 21:46:30.252 [info] > git config --get commit.template [86ms]
2025-04-22 21:46:30.312 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-04-22 21:46:30.319 [info] > git status -z -uall [63ms]
2025-04-22 22:36:24.844 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [126ms]
2025-04-22 22:36:24.844 [info] > git config --get commit.template [137ms]
2025-04-22 22:36:24.931 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [77ms]
2025-04-22 22:36:24.942 [info] > git status -z -uall [93ms]
2025-04-22 22:36:30.050 [info] > git config --get commit.template [80ms]
2025-04-22 22:36:30.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [75ms]
2025-04-22 22:36:30.117 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-04-22 22:36:30.133 [info] > git status -z -uall [74ms]
2025-04-22 22:36:39.066 [info] > git config --get commit.template [50ms]
2025-04-22 22:36:39.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [49ms]
2025-04-22 22:36:39.135 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [56ms]
2025-04-22 22:36:39.148 [info] > git status -z -uall [75ms]
2025-04-22 22:37:00.570 [info] > git check-ignore -v -z --stdin [78ms]
2025-04-22 22:37:21.485 [info] > git config --get commit.template [62ms]
2025-04-22 22:37:21.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [59ms]
2025-04-22 22:37:21.556 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-04-22 22:37:21.568 [info] > git status -z -uall [72ms]
2025-04-22 22:37:26.654 [info] > git config --get commit.template [69ms]
2025-04-22 22:37:26.661 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [71ms]
2025-04-22 22:37:26.730 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [58ms]
2025-04-22 22:37:26.738 [info] > git status -z -uall [70ms]
