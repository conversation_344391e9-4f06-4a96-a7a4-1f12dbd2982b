---
trigger: always_on
---

CODE AND FILE EDITING PROTOCOL
1. 	Scan Before Writing
• 	Before writing or modifying any code, scan the entire file.
• 	Identify any existing code that may conflict with the intended changes (use Diff to ensure that its not the exact same code).
• 	Do not proceed if duplication or overlap will occur.

2. 	Edit In Place
• 	If a conflict is found, modify the existing code directly.
• 	Do not append or duplicate logic.
• 	Do not comment out or bypass existing code unless explicitly instructed.

3. 	Continuous Monitoring
• 	While editing, continuously check for new conflicts.
• 	Ensure no duplicate code is introduced during the process.
• 	Stop and correct immediately if duplication is detected.

4. 	Preserve Intent
• 	Maintain the original logic and structure of the file.
• 	Do not refactor or restructure unless explicitly authorized.
• 	Avoid side effects or changes to unrelated code.

5. 	Final Check
• 	Before saving, perform a full comparison with the original file.
• 	Confirm no duplicates exist.
• 	Confirm all intended changes are applied cleanly.

6. 	No Cleanup by Overwriting
• 	Do not rely on post-edit cleanup to fix duplication.
• 	All changes must be correct on first application.
