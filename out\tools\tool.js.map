{"version": 3, "file": "tool.js", "sourceRoot": "", "sources": ["../../src/tools/tool.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAKH,4BAA4B;AAC5B,6BAAwB;AAExB,mCAAmC;AACtB,QAAA,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;CACrB,CAAC,CAAC,WAAW,EAAE,CAAC;AAEjB,6BAA6B;AAChB,QAAA,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;IACpB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;IAClB,MAAM,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;IAC1B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC/B,CAAC,CAAC;AA6IH;;GAEG;AACH,MAAsB,QAAQ;IAeE;IAd5B,sBAAsB;IACN,EAAE,CAAS;IACX,IAAI,GAAqC,eAAe,CAAC;IAIzE,qBAAqB;IACX,QAAQ,CAAgB;IAElC,gBAAgB;IACA,MAAM,GAAwB,EAAE,CAAC;IACjC,QAAQ,GAAwB,EAAE,CAAC;IACnC,OAAO,GAAW,OAAO,CAAC;IAE1C,YAA4B,IAAY,EAAE,QAAiB;QAA/B,SAAI,GAAJ,IAAI,CAAQ;QACpC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAClD,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACtC,CAAC;IACL,CAAC;IAKD;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,KAAc,EACd,UAA2D,EAAE;QAE7D,MAAM,EAAE,aAAa,GAAG,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;QAErE,IAAI,CAAC;YACD,0BAA0B;YAC1B,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;YAC/B,CAAC;YAED,4BAA4B;YAC5B,IAAI,aAAa,EAAE,CAAC;gBAChB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;gBAClE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;oBACpB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,IAAI,yBAAyB,CAAC,CAAC;oBACvE,iBAAiB,EAAE,CAAC,KAAmB,CAAC,CAAC;oBACzC,MAAM,KAAK,CAAC;gBAChB,CAAC;YACL,CAAC;YAED,+BAA+B;YAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,OAAO,CAAC;YACtD,OAAO,MAAM,IAAI,CAAC,OAAO,CACrB,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK;gBAC5D,CAAC,CAAE,KAA4B,CAAC,MAAM;gBACtC,CAAC,CAAC,SAAS,EACf,KAAkB,EAClB,cAAc,CACjB,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,KAAc,EAAE,OAAO,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,oBAAoB;IACpB,KAAK,CAAC,UAAU,CAAC,OAAsB;QACnC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO;QACT,sCAAsC;IAC1C,CAAC;IAED,aAAa;IACb,aAAa,CAAC,KAAc;QACxB,IAAI,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzB,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO;gBACH,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB;aACtE,CAAC;QACN,CAAC;IACL,CAAC;IAED,qBAAqB;IACrB,UAAU,CAAC,OAAqB;QAC5B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAED,UAAU;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,iBAAiB;IACjB,KAAK,CAAC,OAAO,CAAC,KAAY,EAAE,OAAsB;QAC9C,2DAA2D;QAC3D,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,UAAU,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAED,kBAAkB;IAClB,kBAAkB,CAAC,UAAmB;QAClC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,MAAM,CAAC;QACvB,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,WAAW,CAAC;IACjD,CAAC;IAED,gBAAgB;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,WAAW;QACP,OAAO,EAAE,CAAC;IACd,CAAC;IAED,UAAU;QACN,OAAO,EAAE,CAAC;IACd,CAAC;IAED,YAAY;QACR,sCAAsC;IAC1C,CAAC;IAED,yBAAyB;IACzB,KAAK,CAAC,GAAG,CAAC,KAAc;QACpB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACxC,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,SAAS;aACnB,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,0CAA0C;YAC1C,MAAM,eAAe,GAAG;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAI,KAAK,CAAC,CAAC;oBAC7D,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;gBACpC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC;YAEF,OAAO,eAAe,CAAC;QAC3B,CAAC;IACL,CAAC;CAUA;AA/JL,4BA+JK", "sourcesContent": ["/**\n * Interfaces and types for AI Agent Tools, supporting modern features\n * like structured inputs, multiple actions, and multimodal data.\n * These definitions aim for production quality and completeness, leveraging Zod\n * for schema validation.\n */\n\n// Assuming AgentContext is defined elsewhere, providing environment/state access\nimport { AgentContext } from '../agents/agentUtilities/agent';\n\n// Zod for schema validation\nimport { z } from 'zod';\n\n// Zod schema for common tool input\nexport const ToolInputSchema = z.object({\n    action: z.string(),\n}).passthrough();\n\n// Zod schema for tool result\nexport const ToolResultSchema = z.object({\n    success: z.boolean(),\n    action: z.string(),\n    result: z.any().optional(),\n    error: z.string().optional()\n});\n\n/**\n * Represents the validated and parsed input parameters for a tool execution.\n * The specific structure is defined by the tool's schema(s).\n */\nexport interface ToolInput {\n    [key: string]: any;\n    // Multimodal data structure could be standardized here if needed,\n    // e.g., images?: { type: 'url' | 'base64' | 'uri', value: string }[];\n}\n\n/**\n * Represents the result of a tool execution.\n */\nexport interface ToolResult {\n    /** Indicates if the tool executed successfully. */\n    success: boolean;\n    /** The output payload of the tool execution on success. Can be any serializable data. */\n    output?: any;\n    /** An error message or object if the tool execution failed. */\n    error?: string | Error;\n    /** Optional usage metrics (e.g., tokens consumed, cost). */\n    usage?: { [key: string]: number };\n    /** The ID of the tool that was executed. */\n    toolId: string;\n    /** The name of the specific action executed if the tool has multiple actions. */\n    actionName?: string;\n    /** Additional metadata about the execution or result. */\n    metadata?: Record<string, any>;\n}\n\n/**\n * Defines the structure for a single named action within a multi-action tool.\n */\nexport interface ToolActionDefinition {\n    /** A clear, concise description of what this specific action does. */\n    description: string;\n    /** A Zod schema defining the expected input parameters for this action. */\n    inputSchema: z.ZodType<any>;\n    /** Optional usage cost/pricing information for this specific action. */\n    cost?: any; // Standardize cost interface if needed\n    /** Additional metadata specific to this action. */\n    metadata?: Record<string, any>;\n}\n\n/**\n * Defines an AI Agent Tool. Tools are capabilities that an agent can use\n * to interact with the external world, perform specific tasks, or access information.\n * Supports single-action and multi-action tools, structured inputs via Zod schemas,\n * and multimodal data handling enforced by those schemas.\n */\n/**\n * Context options for tool execution\n */\nexport interface ToolContextOptions {\n  /** Whether to validate input against the schema */\n  validateInput?: boolean;\n  /** Additional context data */\n  context?: Record<string, unknown>;\n  /** Callback for validation errors */\n  onValidationError?: (error: z.ZodError) => unknown;\n}\n\nexport interface ITool {\n    /** A unique identifier for the tool. */\n    readonly id: string;\n    /** The human-readable name of the tool (should be concise and descriptive for the LLM). */\n    readonly name: string;\n    /** A detailed description of the tool's overall capability. */\n    readonly description: string;\n    /** Optional category for grouping tools (e.g., 'File System', 'Web Search', 'Database'). */\n    readonly category?: string;\n    /** Indicates the type of the tool: 'single-action' or 'multi-action'. */\n    readonly type: 'single-action' | 'multi-action';\n    readonly schema: z.ZodType<any>;\n    readonly inputSchema?: z.ZodType<any>;  // Alias for schema for backward compatibility\n    readonly outputSchema?: z.ZodType<any>;\n    \n    // Multi-action support\n    readonly actions?: Record<string, ToolActionDefinition>;\n    \n    // Configuration and Metadata\n    readonly config?: Record<string, any>;\n    readonly metadata?: Record<string, any>;\n    \n    // Core Execution\n    execute(input: ToolInput, context?: AgentContext): Promise<ToolResult>;\n    \n    /**\n     * Enhanced execution with additional options and context\n     * @param input The input to the tool\n     * @param options Additional execution options\n     * @returns A promise resolving to the tool result\n     */\n    invoke?(\n      input: unknown, \n      options?: ToolContextOptions & { context?: AgentContext }\n    ): Promise<ToolResult | unknown>;\n    \n    // Lifecycle Methods\n    initialize?(context?: AgentContext): Promise<void>;\n    dispose?(): Promise<void>;\n    \n    // Advanced Features\n    validateInput?(input: unknown): { valid: boolean; error?: string };\n    getSchemaForAction?(actionName?: string): z.ZodType<any> | undefined;\n    \n    // Context Management\n    setContext?(context: AgentContext): void;\n    getContext?(): AgentContext | undefined;\n    \n    // Metadata and Documentation\n    getDocumentation?(): string;\n    getExamples?(): Array<{input: any; output: any; description?: string}>;\n    \n    // Error Handling\n    onError?(error: Error, context?: AgentContext): Promise<void>;\n    \n    // Performance and Monitoring\n    getMetrics?(): Record<string, any>;\n    resetMetrics?(): void;\n}\n\n// Keep backward compatibility aliases\n/**\n * Alias for ITool, maintaining backward compatibility.\n */\nexport type StructuredTool = ITool;\nexport type Tool = ITool;\n\n/**\n * Alias for ToolInput, maintaining backward compatibility.\n */\nexport type ToolRunParams = ToolInput;\n\n/**\n * Alias for ToolResult, maintaining backward compatibility.\n */\nexport type ToolRunResult = ToolResult;\n\n/**\n * Abstract base class providing common tool functionality\n */\nexport abstract class BaseTool implements ITool {\n    // Core Identification\n    public readonly id: string;\n    public readonly type: 'single-action' | 'multi-action' = 'single-action';\n    public abstract readonly description: string;\n    public abstract readonly schema: z.ZodType<any>;\n    \n    // Context Management\n    protected _context?: AgentContext;\n    \n    // Configuration\n    public readonly config: Record<string, any> = {};\n    public readonly metadata: Record<string, any> = {};\n    public readonly version: string = '1.0.0';\n    \n    constructor(public readonly name: string, category?: string) {\n        this.id = name.toLowerCase().replace(/\\s+/g, '-');\n        if (category) {\n            this.metadata.category = category;\n        }\n    }\n    \n    // Core Execution\n    abstract execute(input: ToolInput, context?: AgentContext): Promise<ToolResult>;\n    \n    /**\n     * Enhanced execution with additional options and context\n     */\n    async invoke(\n        input: unknown, \n        options: ToolContextOptions & { context?: AgentContext } = {}\n    ): Promise<ToolResult | unknown> {\n        const { validateInput = true, context, onValidationError } = options;\n        \n        try {\n            // Set context if provided\n            if (context) {\n                this.setContext?.(context);\n            }\n            \n            // Validate input if enabled\n            if (validateInput) {\n                const validation = this.validateInput?.(input) || { valid: true };\n                if (!validation.valid) {\n                    const error = new Error(validation.error || 'Input validation failed');\n                    onValidationError?.(error as z.ZodError);\n                    throw error;\n                }\n            }\n            \n            // Execute with current context\n            const currentContext = this.getContext?.() || context;\n            return await this.execute(\n                typeof input === 'object' && input !== null && 'action' in input \n                    ? (input as { action: string }).action \n                    : undefined,\n                input as ToolInput,\n                currentContext\n            );\n        } catch (error) {\n            await this.onError?.(error as Error, context);\n            throw error;\n        }\n    }\n    \n    // Lifecycle Methods\n    async initialize(context?: AgentContext): Promise<void> {\n        if (context) {\n            this.setContext?.(context);\n        }\n    }\n    \n    async dispose(): Promise<void> {\n        // Default implementation does nothing\n    }\n    \n    // Validation\n    validateInput(input: unknown): { valid: boolean; error?: string } {\n        try {\n            this.schema.parse(input);\n            return { valid: true };\n        } catch (error) {\n            return {\n                valid: false,\n                error: error instanceof Error ? error.message : 'Validation failed'\n            };\n        }\n    }\n    \n    // Context Management\n    setContext(context: AgentContext): void {\n        this._context = context;\n    }\n    \n    getContext(): AgentContext | undefined {\n        return this._context;\n    }\n    \n    // Error Handling\n    async onError(error: Error, context?: AgentContext): Promise<void> {\n        // Default error handling - can be overridden by subclasses\n        console.error(`[${this.name}] Error:`, error);\n    }\n    \n    // Utility Methods\n    getSchemaForAction(actionName?: string): z.ZodType<any> | undefined {\n        if (!actionName || !this.actions) {\n            return this.schema;\n        }\n        return this.actions[actionName]?.inputSchema;\n    }\n    \n    getDocumentation(): string {\n        return this.description;\n    }\n    \n    getExamples(): Array<{input: any; output: any; description?: string}> {\n        return [];\n    }\n    \n    getMetrics(): Record<string, any> {\n        return {};\n    }\n    \n    resetMetrics(): void {\n        // Default implementation does nothing\n    }\n    \n    // Backward compatibility\n    async run(input: unknown): Promise<z.infer<typeof ToolResultSchema>> {\n        try {\n            const result = await this.invoke(input);\n            return {\n                success: true,\n                action: 'run',\n                result: result,\n                error: undefined\n            };\n        } catch (error) {\n            // Return error result that matches schema\n            const normalizedError = {\n                success: false,\n                action: input && typeof input === 'object' && 'action' in input ?\n                    String(input.action) : 'unknown',\n                error: error instanceof Error ? error.message : String(error)\n            };\n\n            return normalizedError;\n        }\n    }\n\n    /**\n     * Tool-specific implementation to be provided by subclasses\n     * @param actionName The name of the action to execute\n     * @param input The input to the tool\n     * @param context Optional agent context\n     * @returns A Promise resolving to the tool result\n     */\n    abstract execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult>;\n    }\n\n/**\n * Interface for schema-validated tools\n * @deprecated Use ITool directly instead\n */\nexport interface ISchemaValidatedTool extends ITool {\n    /**\n     * Validates and runs the tool with the given input\n     * @deprecated Use invoke() instead for better type safety and features\n     */\n    run(input: unknown): Promise<z.infer<typeof ToolResultSchema>>;\n}"]}