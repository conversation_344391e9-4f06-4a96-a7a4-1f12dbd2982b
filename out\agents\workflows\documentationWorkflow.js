"use strict";
/**
 * Codessa Documentation Workflow
 *
 * This module provides workflow templates for documentation tasks:
 * - Code documentation
 * - API documentation
 * - User guides
 * - Technical specifications
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDocumentationWorkflow = createDocumentationWorkflow;
exports.createAPIDocumentationWorkflow = createAPIDocumentationWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
/**
 * Create a Documentation workflow for generating comprehensive documentation
 */
function createDocumentationWorkflow(id, name, description, techWriterAgent, developerAgent, reviewerAgent, tools = []) {
    logger_1.logger.info(`Creating Documentation workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const codeAnalysisNode = graph_1.Codessa.createAgentNode('code-analysis', 'Code Analysis', developerAgent);
    const structurePlanningNode = graph_1.Codessa.createAgentNode('structure-planning', 'Structure Planning', techWriterAgent);
    const contentGenerationNode = graph_1.Codessa.createAgentNode('content-generation', 'Content Generation', techWriterAgent);
    const codeExamplesNode = graph_1.Codessa.createAgentNode('code-examples', 'Code Examples', developerAgent);
    const technicalReviewNode = graph_1.Codessa.createAgentNode('technical-review', 'Technical Review', developerAgent);
    const editorialReviewNode = graph_1.Codessa.createAgentNode('editorial-review', 'Editorial Review', reviewerAgent);
    const formattingNode = graph_1.Codessa.createAgentNode('formatting', 'Formatting', techWriterAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'default' },
        { name: 'analysis-to-structure', source: 'code-analysis', target: 'structure-planning', type: 'default' },
        { name: 'structure-to-content', source: 'structure-planning', target: 'content-generation', type: 'default' },
        { name: 'content-to-examples', source: 'content-generation', target: 'code-examples', type: 'default' },
        { name: 'examples-to-tech-review', source: 'code-examples', target: 'technical-review', type: 'default' },
        { name: 'tech-review-to-editorial', source: 'technical-review', target: 'editorial-review', type: 'default' },
        { name: 'editorial-to-formatting', source: 'editorial-review', target: 'formatting', type: 'default' },
        { name: 'formatting-to-output', source: 'formatting', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'tech-review-to-content', source: 'technical-review', target: 'content-generation', type: 'feedback' },
        { name: 'editorial-to-content', source: 'editorial-review', target: 'content-generation', type: 'feedback' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect content generation to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `content-to-tool-${index}`,
                source: 'content-generation',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to code examples
            edges.push({
                name: `tool-${index}-to-examples`,
                source: `tool-${index}`,
                target: 'code-examples',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            codeAnalysisNode,
            structurePlanningNode,
            contentGenerationNode,
            codeExamplesNode,
            technicalReviewNode,
            editorialReviewNode,
            formattingNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'documentation',
        tags: ['documentation', 'technical-writing', 'code-docs']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create an API Documentation workflow
 */
function createAPIDocumentationWorkflow(id, name, description, techWriterAgent, apiExpertAgent, tools = []) {
    logger_1.logger.info(`Creating API Documentation workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const apiAnalysisNode = graph_1.Codessa.createAgentNode('api-analysis', 'API Analysis', apiExpertAgent);
    const endpointDocumentationNode = graph_1.Codessa.createAgentNode('endpoint-documentation', 'Endpoint Documentation', techWriterAgent);
    const parameterDocumentationNode = graph_1.Codessa.createAgentNode('parameter-documentation', 'Parameter Documentation', techWriterAgent);
    const responseDocumentationNode = graph_1.Codessa.createAgentNode('response-documentation', 'Response Documentation', techWriterAgent);
    const exampleGenerationNode = graph_1.Codessa.createAgentNode('example-generation', 'Example Generation', apiExpertAgent);
    const errorHandlingDocNode = graph_1.Codessa.createAgentNode('error-handling-doc', 'Error Handling Documentation', techWriterAgent);
    const apiReferenceCompilationNode = graph_1.Codessa.createAgentNode('api-reference-compilation', 'API Reference Compilation', techWriterAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-analysis', source: 'input', target: 'api-analysis', type: 'default' },
        { name: 'analysis-to-endpoint', source: 'api-analysis', target: 'endpoint-documentation', type: 'default' },
        { name: 'endpoint-to-parameter', source: 'endpoint-documentation', target: 'parameter-documentation', type: 'default' },
        { name: 'parameter-to-response', source: 'parameter-documentation', target: 'response-documentation', type: 'default' },
        { name: 'response-to-examples', source: 'response-documentation', target: 'example-generation', type: 'default' },
        { name: 'examples-to-error', source: 'example-generation', target: 'error-handling-doc', type: 'default' },
        { name: 'error-to-compilation', source: 'error-handling-doc', target: 'api-reference-compilation', type: 'default' },
        { name: 'compilation-to-output', source: 'api-reference-compilation', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect example generation to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `example-to-tool-${index}`,
                source: 'example-generation',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to error handling
            edges.push({
                name: `tool-${index}-to-error`,
                source: `tool-${index}`,
                target: 'error-handling-doc',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            apiAnalysisNode,
            endpointDocumentationNode,
            parameterDocumentationNode,
            responseDocumentationNode,
            exampleGenerationNode,
            errorHandlingDocNode,
            apiReferenceCompilationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'documentation',
        tags: ['api', 'documentation', 'reference']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=documentationWorkflow.js.map