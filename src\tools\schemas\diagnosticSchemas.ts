import { z } from 'zod';

export const DiagnosticSeverityEnum = z.enum(['error', 'warning', 'info', 'hint']);
export type DiagnosticSeverity = z.infer<typeof DiagnosticSeverityEnum>;

export const DiagnosticInput = z.object({
  filePath: z.string().optional(),
  severity: z.union([DiagnosticSeverityEnum, z.array(DiagnosticSeverityEnum)]).optional(),
  code: z.union([z.string(), z.array(z.string())]).optional(),
  source: z.union([z.string(), z.array(z.string())]).optional(),
  message: z.string().optional(),
  limit: z.number().min(1).optional()
});

export const DiagnosticResult = z.object({
  severity: DiagnosticSeverityEnum,
  code: z.string().optional(),
  source: z.string().optional(),
  message: z.string(),
  file: z.string(),
  line: z.number(),
  column: z.number(),
  endLine: z.number().optional(),
  endColumn: z.number().optional(),
  relatedInformation: z.array(z.object({
    message: z.string(),
    file: z.string(),
    line: z.number(),
    column: z.number()
  })).optional()
});

export type DiagnosticInputType = z.infer<typeof DiagnosticInput>;
export type DiagnosticResultType = z.infer<typeof DiagnosticResult>;
