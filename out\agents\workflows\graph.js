"use strict";
/**
 * Codessa graph implementation
 *
 * This file provides the core graph implementation for Codessa workflows.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Codessa = void 0;
const logger_1 = require("../../logger");
/**
 * Codessa graph implementation
 */
class Codessa {
    definition;
    nodes;
    edges;
    /**
       * Create a new Codessa graph
       */
    constructor(definition) {
        this.definition = definition;
        this.nodes = new Map();
        this.edges = new Map();
        // Add nodes
        definition.nodes.forEach(node => {
            this.nodes.set(node.id, node);
        });
        // Add edges
        definition.edges.forEach(edge => {
            this.edges.set(edge.name, edge);
        });
        logger_1.Logger.instance.info(`Created Codessa graph: ${definition.name}`);
    }
    /**
       * Get the graph definition
       */
    getDefinition() {
        return this.definition;
    }
    /**
       * Get a node by ID
       */
    getNode(id) {
        return this.nodes.get(id);
    }
    /**
       * Get all nodes
       */
    getNodes() {
        return Array.from(this.nodes.values());
    }
    /**
       * Get an edge by name
       */
    getEdge(name) {
        return this.edges.get(name);
    }
    /**
       * Get all edges
       */
    getEdges() {
        return Array.from(this.edges.values());
    }
    /**
       * Get outgoing edges from a node
       */
    getOutgoingEdges(nodeId) {
        return this.getEdges().filter(edge => edge.source === nodeId);
    }
    /**
       * Get incoming edges to a node
       */
    getIncomingEdges(nodeId) {
        return this.getEdges().filter(edge => edge.target === nodeId);
    }
    /**
       * Create an input node
       */
    static createInputNode(id, name) {
        return {
            id,
            name,
            label: name,
            type: 'input'
        };
    }
    /**
       * Create an agent node
       */
    static createAgentNode(id, name, agent) {
        return {
            id,
            name,
            label: name,
            type: 'agent',
            agent
        };
    }
    /**
       * Create a tool node
       */
    static createToolNode(id, name, tool) {
        return {
            id,
            name,
            label: name,
            type: 'tool',
            tool
        };
    }
    /**
       * Create an output node
       */
    static createOutputNode(id, name) {
        return {
            id,
            name,
            label: name,
            type: 'output'
        };
    }
    /**
       * Execute the graph
       */
    async execute(input) {
        logger_1.Logger.instance.info(`Executing Codessa graph: ${this.definition.name}`);
        // Find input node
        const inputNode = this.getNodes().find(node => node.type === 'input');
        if (!inputNode) {
            throw new Error('No input node found in the graph');
        }
        // Execute the graph starting from the input node
        return this.executeNode(inputNode.id, input);
    }
    /**
       * Execute a node
       */
    async executeNode(nodeId, input) {
        const node = this.getNode(nodeId);
        if (!node) {
            throw new Error(`Node with ID '${nodeId}' not found`);
        }
        logger_1.Logger.instance.info(`Executing node: ${node.name} (${node.id})`);
        let output;
        // Process node based on type
        switch (node.type) {
            case 'input':
                output = input;
                break;
            case 'agent':
                if (!node.agent) {
                    throw new Error(`Agent not defined for node: ${node.name} (${node.id})`);
                }
                output = await node.agent.run({ prompt: input, mode: 'task' }, {});
                break;
            case 'tool':
                if (!node.tool) {
                    throw new Error(`Tool not defined for node: ${node.name} (${node.id})`);
                }
                output = await node.tool.execute(undefined, { input }, {});
                break;
            case 'output':
                return input;
            default:
                throw new Error(`Unknown node type: ${node.type}`);
        }
        // Get outgoing edges
        const outgoingEdges = this.getOutgoingEdges(nodeId);
        if (outgoingEdges.length === 0) {
            // No outgoing edges, return the output
            return output;
        }
        // Find the appropriate edge to follow
        let nextEdge;
        if (outgoingEdges.length === 1) {
            // Only one outgoing edge, use it
            nextEdge = outgoingEdges[0];
        }
        else {
            // Multiple outgoing edges, find the appropriate one based on the output
            nextEdge = outgoingEdges.find(edge => {
                if (edge.type === 'default') {
                    return true;
                }
                if (edge.type === 'conditional' && edge.condition) {
                    return edge.condition(output);
                }
                return false;
            });
        }
        if (!nextEdge) {
            throw new Error(`No valid outgoing edge found for node: ${node.name} (${node.id})`);
        }
        // Execute the next node
        return this.executeNode(nextEdge.target, output);
    }
}
exports.Codessa = Codessa;
//# sourceMappingURL=graph.js.map