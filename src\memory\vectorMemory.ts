import { logger } from '../logger';
import type { MemoryEntry, MemorySearchOptions, MemorySource, MemoryType, IMemoryOperations } from './types';
import { llmService } from '../llm/llmService';
import { getConfig } from '../config';
import * as vscode from 'vscode';

/**
 * Vector representation of a memory
 */
interface MemoryVector {
  memoryId: string;
  vector: number[];
}

/**
 * Vector memory manager for semantic search
 */
export class VectorMemoryManager {
  private static instance: VectorMemoryManager;
  private vectors: MemoryVector[] = [];
  private initialized = false;
  private memoryOperations: IMemoryOperations | undefined;

  private constructor() { }

  private _onMemoriesChanged = new vscode.EventEmitter<void>();

  /**
     * Register a listener for memory changes
     */
  public onMemoriesChanged(listener: () => void): vscode.Disposable {
    return this._onMemoriesChanged.event(listener);
  }

  /**
     * Get the singleton instance
     */
  public static getInstance(): VectorMemoryManager {
    if (!VectorMemoryManager.instance) {
      VectorMemoryManager.instance = new VectorMemoryManager();
    }
    return VectorMemoryManager.instance;
  }

  /**
     * Set the memory operations provider to break circular dependency
     */
  public setMemoryOperations(memoryOperations: IMemoryOperations): void {
    this.memoryOperations = memoryOperations;
  }

  /**
     * Initialize the vector memory manager
     */
  public async initialize(context?: vscode.ExtensionContext): Promise<void> {
    if (this.initialized) {
      return;
    }

    if (!this.memoryOperations) {
      logger.warn('Vector memory manager initialized without memory operations provider');
      this.initialized = true;
      return;
    }

    try {
      // Store context for potential future use (e.g., storing vector data in extension storage)
      if (context) {
        logger.debug('Vector memory manager initialized with VS Code extension context');
        // Context could be used for persisting vector data to extension storage
        // or for accessing extension-specific resources
      }

      // Load existing memories and generate vectors
      const memories = await this.memoryOperations.getMemories();
      logger.info(`Initializing vector memory with ${memories.length} memories`);

      // Generate vectors for all memories (in batches to avoid overloading)
      const batchSize = 10;
      for (let i = 0; i < memories.length; i += batchSize) {
        const batch = memories.slice(i, i + batchSize);
        await Promise.all(batch.map(memory => this.addMemoryVector(memory)));
        logger.debug(`Processed vector batch ${i / batchSize + 1}/${Math.ceil(memories.length / batchSize)}`);
      }

      // Listen for memory changes
      this.memoryOperations.registerMemoryChangeListener(() => {
        this.syncWithMemoryManager();
        this._onMemoriesChanged.fire();
      });

      this.initialized = true;
      logger.info('Vector memory initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize vector memory:', error);
    }
  }

  /**
     * Sync with memory manager
     */
  private async syncWithMemoryManager(): Promise<void> {
    if (!this.memoryOperations) {
      return;
    }

    try {
      const memories = await this.memoryOperations.getMemories();
      const memoryIds = new Set(memories.map(memory => memory.id));
      const vectorIds = new Set(this.vectors.map(vector => vector.memoryId));

      // Remove vectors for deleted memories
      this.vectors = this.vectors.filter(vector => memoryIds.has(vector.memoryId));

      // Add vectors for new memories
      const newMemories = memories.filter(memory => !vectorIds.has(memory.id));
      for (const memory of newMemories) {
        await this.addMemoryVector(memory);
      }

      logger.debug(`Synced vector memory: ${this.vectors.length} vectors for ${memories.length} memories`);
    } catch (error) {
      logger.error('Failed to sync vector memory:', error);
    }
  }

  /**
     * Add a vector for a memory
     */
  private async addMemoryVector(memory: MemoryEntry): Promise<void> {
    try {
      // Generate vector using the default provider
      const provider = await llmService.getDefaultProvider();
      if (!provider) {
        logger.warn('No default provider available for vector embedding');
        return;
      }

      // Check if provider supports embeddings
      if (!provider.generateEmbedding) {
        logger.warn(`Provider ${provider.providerId} does not support embeddings`);
        return;
      }

      // Generate embedding
      const embedding = await provider.generateEmbedding(memory.content);

      // Store vector
      this.vectors.push({
        memoryId: memory.id,
        vector: embedding
      });

      logger.debug(`Added vector for memory ${memory.id}`);
    } catch (error) {
      logger.error(`Failed to add vector for memory ${memory.id}:`, error);
    }
  }

  /**
     * Calculate cosine similarity between two vectors
     */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (normA * normB);
  }

  /**
     * Add a memory with content and metadata
     */
  public async addMemory(contentOrEntry: string | Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry> {
    if (!this.memoryOperations) {
      throw new Error('Memory operations not available');
    }

    try {
      // Convert string to memory entry if needed
      let entry: Omit<MemoryEntry, 'id' | 'timestamp'>;
      if (typeof contentOrEntry === 'string') {
        const defaultSource = getConfig<string>('memory.vector.defaultSource', 'unknown');
        const defaultType = getConfig<string>('memory.vector.defaultType', 'generic');
        entry = { content: contentOrEntry, metadata: { source: defaultSource as MemorySource, type: defaultType as MemoryType } };
      } else {
        entry = contentOrEntry;
      }
      // Create a memory entry using the memory manager
      const memoryEntry = await this.memoryOperations.addMemory(entry);

      // Add vector for the new memory
      await this.addMemoryVector(memoryEntry);

      return memoryEntry;
    } catch (error) {
      logger.error('Failed to add memory:', error);
      throw error;
    }
  }

  /**
     * Get all memories
     */
  public async getMemories(): Promise<MemoryEntry[]> {
    if (!this.memoryOperations) {
      return [];
    }
    return this.memoryOperations.getMemories();
  }

  /**
     * Get a memory by ID
     */
  public async getMemory(id: string): Promise<MemoryEntry | undefined> {
    if (!this.memoryOperations) {
      return undefined;
    }
    return this.memoryOperations.getMemory(id);
  }

  /**
   * Updates an existing memory with new content or metadata.
   * @param id The ID of the memory to update
   * @param updates The fields to update
   * @returns The updated memory entry, or undefined if not found
   */
  public async updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined> {
    if (!this.initialized) {
      throw new Error('VectorMemoryManager not initialized');
    }
    if (!this.memoryOperations) {
      return undefined;
    }
    try {
      const updatedMemory = await this.memoryOperations.updateMemory(id, updates);
      if (updatedMemory) {
        // Remove old vector
        this.vectors = this.vectors.filter(v => v.memoryId !== id);
        // Add new vector for the updated memory
        await this.addMemoryVector(updatedMemory);
        this._onMemoriesChanged.fire();
      }
      return updatedMemory;
    } catch (error) {
      logger.error(`Failed to update memory ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a memory by ID
   */
  public async deleteMemory(id: string): Promise<boolean> {
    // Remove from vector store
    this.vectors = this.vectors.filter(v => v.memoryId !== id);

    // Delete from memory manager
    if (!this.memoryOperations) {
      return false;
    }
    return await this.memoryOperations.deleteMemory(id);
  }

  /**
     * Clear all memories
     */
  public async clearMemories(): Promise<void> {
    // Clear vectors
    this.vectors = [];

    // Clear memories in memory manager
    if (this.memoryOperations) {
      await this.memoryOperations.clearMemories();
    }
  }

  /**
     * Search memories by text
     */
  public async searchMemories(query: string, limit = 10): Promise<MemoryEntry[]> {
    if (!this.memoryOperations) {
      return [];
    }
    return this.memoryOperations.searchMemories({
      query,
      limit
    });
  }

  /**
     * Search memories by semantic similarity
     */
  public async searchSimilarMemories(query: string, options: Partial<MemorySearchOptions> | number = {}): Promise<MemoryEntry[]> {
    // Handle the case where options is a number (limit)
    const opts: Partial<MemorySearchOptions> = typeof options === 'number' ? { limit: options } : options;

    if (!this.memoryOperations) {
      return [];
    }

    try {
      if (!this.initialized) {
        await this.initialize();
      }

      // Generate vector for query
      const provider = await llmService.getDefaultProvider();
      if (!provider || !provider.generateEmbedding) {
        logger.warn('No provider with embedding support available');
        // Fall back to text search
        return this.memoryOperations.searchMemories({
          query,
          limit: typeof options === 'number' ? options : options.limit,
          filter: typeof options === 'object' ? options.filter : undefined
        });
      }

      const queryVector = await provider.generateEmbedding(query);

      // Calculate similarity scores
      const scores = this.vectors.map(vector => ({
        memoryId: vector.memoryId,
        similarity: this.cosineSimilarity(queryVector, vector.vector)
      }));

      // Sort by similarity
      scores.sort((a, b) => b.similarity - a.similarity);

      // Apply relevance threshold
      const relevanceThreshold = getConfig<number>('memory.relevanceThreshold', 0.7);
      const relevantScores = scores.filter(score => score.similarity >= relevanceThreshold);

      // Get memories for relevant scores
      const memories = await Promise.all(relevantScores.map(async score => {
        const memory = await this.memoryOperations?.getMemory(score.memoryId);
        return memory ? { ...memory, relevance: score.similarity } : undefined;
      }));
      const validMemories = memories.filter(Boolean) as (MemoryEntry & { relevance: number })[];

      // Apply additional filters
      let filteredMemories = validMemories;
      if (opts.filter) {
        const filter = opts.filter;
        if (filter.source) {
          filteredMemories = filteredMemories.filter(
            memory => memory.metadata.source === filter.source
          );
        }

        if (filter.type) {
          filteredMemories = filteredMemories.filter(
            memory => memory.metadata.type === filter.type
          );
        }

        if (filter.tags && filter.tags.length > 0) {
          filteredMemories = filteredMemories.filter(
            memory => filter.tags?.every(tag => memory.metadata.tags?.includes(tag)) ?? false
          );
        }

        if (filter.fromTimestamp !== undefined) {
          const fromTimestamp = filter.fromTimestamp;
          filteredMemories = filteredMemories.filter(
            memory => memory.timestamp >= fromTimestamp
          );
        }

        if (filter.toTimestamp !== undefined) {
          const toTimestamp = filter.toTimestamp;
          filteredMemories = filteredMemories.filter(
            memory => memory.timestamp <= toTimestamp
          );
        }
      }

      // Apply limit
      const limit = opts.limit || 10;
      return filteredMemories.slice(0, limit);
    } catch (error) {
      logger.error('Error in semantic search:', error);
      // Fall back to text search
      return this.memoryOperations.searchMemories({ query, ...opts });
    }
  }
}

// Export singleton instance
export const vectorMemoryManager = VectorMemoryManager.getInstance();
