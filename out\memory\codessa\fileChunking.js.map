{"version": 3, "file": "fileChunking.js", "sourceRoot": "", "sources": ["../../../src/memory/codessa/fileChunking.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,+BAAoC;AACpC,sDAA6B,CAAC,4BAA4B;AAE1D,+BAA4B;AAE5B,wCAAwC;AACxC,sEAAqF;AACrF,kEAAkE;AAClE,2DAA2D;AAE3D,yDAAyD;AACzD,yCAAsC;AACtC,yCAAyC;AAkIzC,uBAAuB;AAEvB,MAAa,wBAAwB;IAClB,MAAM,CAA0B;IAChC,UAAU,CAAsB;IAChC,kBAAkB,CAAsB;IACxC,YAAY,CAAgB;IAE7C,YACE,MAA+B,EAC/B,UAA+B,EAC/B,kBAAuC,EACvC,YAA2B;QAE3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;QACvF,CAAC;QACD,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;QACvF,CAAC;QACD,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;IAEO,cAAc,CAAC,MAAwC;QAC7D,sDAAsD;QACtD,MAAM,eAAe,GAAG,EAAE,GAAG,MAAM,EAA6B,CAAC;QAEjE,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,CAAC;QACjF,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,IAAI,GAAG,CAAC,CAAC;QACrF,eAAe,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,kBAAkB,IAAI,GAAG,CAAC,CAAC;QACnF,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC;QAC7E,eAAe,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,CAAC;QAC1F,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,EAAE,aAAa,EAAE,GAAG,EAAE,iBAAiB,EAAE,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC;QACpH,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,SAAS,CAAC,aAAa,IAAI,GAAG,CAAC,CAAC;QAExG,4CAA4C;QAC5C,eAAe,CAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,IAAI,WAAW,CAAC;QAClF,eAAe,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,IAAI,EAAE,CAAC;QACvE,eAAe,CAAC,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAClH,eAAe,CAAC,sBAAsB,GAAG,MAAM,CAAC,sBAAsB,IAAI,OAAO,CAAC;QAElF,yCAAyC;QACzC,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,mBAAmB,EAAE,eAAe,CAAC,gBAAgB,GAAG,EAAE,CAAC,CAAC;QAC3H,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,YAAY,CACvB,WAA0B,EAC1B,gBAAkF;QAElF,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC;QACxC,eAAM,CAAC,IAAI,CAAC,iCAAiC,YAAY,kCAAkC,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE5H,MAAM,KAAK,GAAG,IAAA,iBAAM,EAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACnD,MAAM,OAAO,GAAoB,EAAE,CAAC;QACpC,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,MAAM,kBAAkB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAC3D,KAAK,CAAC,KAAK,IAA4B,EAAE;YACvC,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,eAAM,CAAC,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC,IAAI,YAAY,wBAAwB,GAAG,EAAE,CAAC,CAAC;YAC/E,IAAI,gBAAgB,EAAE,CAAC;gBACrB,gBAAgB,CAAC,cAAc,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;YACtD,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,IAAI,gBAAgB,EAAE,CAAC;gBACrB,mCAAmC;gBACnC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YACjD,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC,IAAI,YAAY,sBAAsB,GAAG,cAAc,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YACxG,OAAO,MAAM,CAAC,CAAC,iCAAiC;QAClD,CAAC,CAAC,CACH,CAAC;QAEF,kCAAkC;QAClC,MAAM,OAAO,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;QAE7C,oBAAoB;QACpB,MAAM,WAAW,GAAuB;YACtC,YAAY;YACZ,gBAAgB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM;YACtE,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;YAClE,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,MAAM;YAC/D,gBAAgB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;YACnE,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC,CAAC;SACxH,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,0CAA0C,WAAW,CAAC,gBAAgB,cAAc,WAAW,CAAC,cAAc,aAAa,WAAW,CAAC,aAAa,mBAAmB,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACnN,IAAI,WAAW,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YAClC,eAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,CAAC,aAAa,UAAU,CAAC,CAAC;QACnF,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAGD;;;;SAIK;IACE,KAAK,CAAC,iBAAiB,CAAC,UAAuB;QACpD,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QACtC,IAAI,QAAwC,CAAC;QAC7C,IAAI,MAA4B,CAAC;QACjC,IAAI,MAAM,GAAkB;YAC1B,SAAS;YACT,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,OAAO,EAAE,mBAAmB;YACpC,UAAU,EAAE,CAAC;YACb,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,8BAA8B;SACxC,CAAC;QAEF,IAAI,CAAC;YACH,kBAAkB;YAClB,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,uBAAuB,CAAC,CAAC;YACnD,QAAQ,GAAG,MAAM,UAAU,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,2BAA2B;YAEvD,uEAAuE;YACvE,IAAI,QAAQ,CAAC,UAAU,KAAK,YAAY,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC1D,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;gBACjD,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;oBACrD,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,yBAAyB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,KAAK,CAAC,CAAC;oBACrI,OAAO,EAAE,GAAG,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,4BAA4B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,KAAK,EAAE,CAAC;gBACzI,CAAC;gBACD,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBACxB,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,4BAA4B,CAAC,CAAC;oBACvD,OAAO,EAAE,GAAG,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;gBACvF,CAAC;YACH,CAAC;YACD,oDAAoD;YAEpD,6BAA6B;YAC7B,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,gCAAgC,CAAC,CAAC;YAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,+DAA+D,EAAE,QAAQ,CAAC,CAAC;gBACpG,OAAO,EAAE,GAAG,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;YAC3G,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,sBAAsB,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAEvE,wBAAwB;YACxB,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,6BAA6B,CAAC,CAAC;YACzD,MAAM,GAAG,MAAM,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAE7C,qBAAqB;YACrB,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,yBAAyB,CAAC,CAAC;YACrD,MAAM,gBAAgB,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACnE,MAAM,CAAC,oBAAoB,GAAG,gBAAgB,CAAC,WAAW,CAAC,CAAC,qBAAqB;YAEjF,iCAAiC;YACjC,IAAI,CAAC,OAAO,gBAAgB,CAAC,OAAO,KAAK,QAAQ,IAAI,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC;gBACzF,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvF,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,yCAAyC,CAAC,CAAC;gBACpE,8CAA8C;gBAC9C,OAAO,EAAE,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,6BAA6B,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;YAChH,CAAC;YAED,qCAAqC;YACrC,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,iDAAiD,gBAAgB,CAAC,WAAW,KAAK,CAAC,CAAC;YAC9G,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACxE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,qEAAqE,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC9H,OAAO,EAAE,GAAG,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,4BAA4B,gBAAgB,CAAC,WAAW,EAAE,EAAE,CAAC;YAC/H,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,8BAA8B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC7E,MAAM,CAAC,oBAAoB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB;YAEvE,4CAA4C;YAC5C,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,2CAA2C,CAAC,CAAC;YACvE,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,MAAM,YAAY,GAAkB,EAAE,CAAC;YAEvC,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACjF,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBACjD,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,kCAAkC,IAAI,CAAC,MAAM,CAAC,kBAAkB,uCAAuC,CAAC,CAAC;oBAClI,MAAM;gBACR,CAAC;gBAED,gFAAgF;gBAChF,MAAM,YAAY,GAAG,CAAC,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC;oBAC3F,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;gBAEjE,IAAI,YAAY,EAAE,CAAC;oBACjB,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,mCAAmC,UAAU,GAAG,CAAC,CAAC;oBAC5E,SAAS,CAAC,sDAAsD;gBAClE,CAAC;gBAED,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CACnD,QAAQ,EACR,gBAAgB,EAChB,KAAK,CAAC,QAAQ,EACd,UAAU,EACV,SAAS,CACV,CAAC;gBAEF,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;wBACrD,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,iCAAiC;wBACzD,QAAQ,EAAE,aAAa;qBACxB,CAAC,CAAC;oBACH,0DAA0D;oBAC1D,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;wBACnB,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,kDAAkD,UAAU,uBAAuB,CAAC,CAAC;wBAC9G,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;wBACtC,UAAU,CAAC,EAAE,GAAG,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,IAAA,SAAM,GAAE,EAAE,CAAC,CAAC,CAAC,gCAAgC;oBACjH,CAAC;oBACD,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,iBAAiB;oBACrF,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC9B,UAAU,EAAE,CAAC,CAAC,yDAAyD;gBACzE,CAAC;gBAAC,OAAO,UAAmB,EAAE,CAAC;oBAC7B,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,2BAA2B,UAAU,GAAG,EAAE,UAAU,CAAC,CAAC;oBAChF,qEAAqE;oBACrE,yDAAyD;oBACzD,MAAM,YAAY,GAAG,UAAU,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBAC3F,MAAM,IAAI,KAAK,CAAC,yBAAyB,UAAU,KAAK,YAAY,EAAE,CAAC,CAAC;gBAC1E,CAAC;YACH,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,uCAAuC,UAAU,UAAU,CAAC,CAAC;YACtF,MAAM,GAAG;gBACP,GAAG,MAAM;gBACT,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,WAAW;gBACnB,UAAU,EAAE,UAAU;gBACtB,YAAY,EAAE,YAAY;gBAC1B,OAAO,EAAE,+BAA+B,UAAU,UAAU;aAC7D,CAAC;YACF,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,6BAA6B,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;YACvG,yCAAyC;YACzC,MAAM,GAAG;gBACP,GAAG,MAAM,EAAE,4DAA4D;gBACvE,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,YAAY,IAAI,2BAA2B;aACrD,CAAC;YACF,OAAO,MAAM,CAAC;QAChB,CAAC;gBAAS,CAAC;YACT,yCAAyC;YACzC,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACvB,IAAI,CAAC;oBACH,eAAM,CAAC,KAAK,CAAC,IAAI,SAAS,8BAA8B,CAAC,CAAC;oBAC1D,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC7B,CAAC;gBAAC,OAAO,YAAqB,EAAE,CAAC;oBAC/B,eAAM,CAAC,IAAI,CAAC,IAAI,SAAS,qCAAqC,EAAE,YAAY,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;YACD,+EAA+E;YAC/E,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAChC,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,QAA4B;QAChD,sDAAsD;QACtD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IACzD,CAAC;IAEO,oBAAoB,CAAC,WAAmB;QAC9C,yDAAyD;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;IACtE,CAAC;IAEO,0BAA0B,CAChC,cAAkC,EAClC,gBAAkC,EAClC,aAAoE,EACpE,UAAkB,EAClB,SAAiB;QAEjB,mCAAmC;QACnC,MAAM,QAAQ,GAAG;YACf,cAAc;YACd,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,aAAa,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;YACxG,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,SAAS,EAAE,cAAc,CAAC,YAAY,EAAE,WAAW,EAAE;YAErD,kBAAkB;YAClB,oBAAoB,EAAE,gBAAgB,CAAC,WAAW;YAClD,GAAG,gBAAgB,CAAC,QAAQ,EAAE,kDAAkD;YAEhF,gBAAgB;YAChB,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,GAAG,SAAS,WAAW,UAAU,EAAE,EAAE,kCAAkC;YAChF,GAAG,aAAa,EAAE,mCAAmC;YAErD,4DAA4D;YAC5D,EAAE,EAAE,OAAO,IAAA,SAAM,GAAE,EAAE,EAAE,2BAA2B;YAClD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,wEAAwE;YACxE,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,UAAU,CAAC;YACzD,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,WAAW,EAAE,cAAc,CAAC,UAAU,CAAC;YACnF,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,gBAAgB,CAAC;SAC1D,CAAC;QAEF,+CAA+C;QAC/C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAClC,IAAI,QAAQ,CAAC,GAA4B,CAAC,KAAK,SAAS,EAAE,CAAC;gBACzD,OAAO,QAAQ,CAAC,GAA4B,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,uEAAuE;IAC/D,iBAAiB,CAAC,UAAkB;QAC1C,kBAAkB;QAClB,IAAI,UAAU,KAAK,YAAY,IAAI,UAAU,KAAK,UAAU,IAAI,UAAU,KAAK,WAAW;YAAE,OAAO,MAAM,CAAC;QAC1G,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACpF,6CAA6C;QAC7C,OAAO,WAAW,CAAC,CAAC,UAAU;IAChC,CAAC;IAEO,eAAe,CAAC,WAAmB,EAAE,UAAkB;QAC7D,+CAA+C;QAC/C,IAAI,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC;YAAE,OAAO,MAAM,CAAC;QACnD,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,UAAU,CAAC;QACnD,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YAAE,OAAO,UAAU,CAAC;QACjG,IAAI,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO,OAAO,CAAC;QACrD,IAAI,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO,OAAO,CAAC;QACrD,IAAI,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO,OAAO,CAAC;QACrD,IAAI,WAAW,KAAK,0BAA0B,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QAClG,sDAAsD;QACtD,IAAI,UAAU,KAAK,YAAY;YAAE,OAAO,MAAM,CAAC,CAAC,eAAe;QAC/D,OAAO,MAAM,CAAC,CAAC,UAAU;IAC3B,CAAC;IAEO,YAAY,CAAC,cAAkC,EAAE,gBAAkC;QACzF,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,WAAW,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;QAEtG,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YACjF,IAAI,GAAG,EAAE,CAAC;gBACR,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QACD,IAAI,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,QAAQ,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACrE,CAAC;QACD,iEAAiE;QACjE,IAAI,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;YACxC,IAAI,CAAC,GAAG,CAAC,QAAQ,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF;AAnXD,4DAmXC;AAGD,6CAA6C;AAE7C,uBAAuB;AAEvB,MAAa,eAAe;IACT,QAAQ,CAAS;IACjB,GAAG,CAAS;IAE7B,YAAY,QAAgB;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,wDAAwD,QAAQ,EAAE,CAAC,CAAC;QACtF,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,GAAG,GAAG,UAAU,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,sBAAsB;IAC9D,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1D,CAAC;YACD,8FAA8F;YAC9F,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEpD,OAAO;gBACL,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,UAAU,EAAE,YAAY;gBACxB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,YAAY,EAAE,KAAK,CAAC,KAAK;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACtC,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,6CAA6C;aACvE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,MAAM,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACzB,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,8BAA8B,EAAE,GAAG,CAAC,CAAC;gBAC9D,wCAAwC;YAC1C,CAAC,CAAC,CAAC;YACH,+CAA+C;YAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,oCAAoC,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAED,0EAA0E;IAClE,cAAc,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,OAAO,GAA2B;YACtC,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,iBAAiB;YAChH,OAAO,EAAE,kBAAkB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,iBAAiB;YACjF,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe;YAC9G,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW;YAC9D,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY;YAC1C,MAAM,EAAE,iBAAiB,EAAE,KAAK,EAAE,kBAAkB;YACpD,mBAAmB;SACpB,CAAC;QACF,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,0BAA0B,CAAC,CAAC,iBAAiB;IACtE,CAAC;CAIF;AA5ED,0CA4EC;AAED,6BAA6B;AAE7B,MAAa,aAAa;IACP,kBAAkB,CAAc;IAChC,eAAe,CAAiB;IAEjD,YAAY,qBAA+B,CAAC,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,EAAE,kBAAkC,OAAO;QACtM,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACtD,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAED,OAAO,KAAa,OAAO,eAAe,CAAC,CAAC,CAAC;IAE7C,QAAQ,CAAC,QAA4B;QACnC,yGAAyG;QACzG,MAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEnG,+CAA+C;QAC/C,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAC7B,IAAI,QAAQ,CAAC,UAAU,KAAK,YAAY,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC9D,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACpE,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YAExZ,gBAAgB,GAAG,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC;gBACvD,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,KAAK,0BAA0B,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO,eAAe,IAAI,gBAAgB,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAgB,EAAE,QAA4B;QAC1D,IAAI,CAAC;YACH,oDAAoD;YACpD,+EAA+E;YAC/E,4EAA4E;YAC5E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,6CAA6C;YAEpG,+DAA+D;YAC/D,4CAA4C;YAE5C,OAAO;gBACL,WAAW,EAAE,QAAQ,CAAC,QAAQ,IAAI,YAAY,EAAE,+BAA+B;gBAC/E,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE;oBACR,QAAQ,EAAE,IAAI,CAAC,eAAe;oBAC9B,2DAA2D;iBAC5D;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,GAAG,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,mCAAmC,YAAY,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAgB;QAC3C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAe,CAAC,CAAC,CAAC;QAC7E,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;CACF;AA7DD,sCA6DC;AAED,MAAa,eAAe;IAC1B,OAAO,KAAa,OAAO,iBAAiB,CAAC,CAAC,CAAC;IAE/C,QAAQ,CAAC,QAA4B;QACnC,kEAAkE;QAClE,0CAA0C;QAC1C,eAAM,CAAC,KAAK,CAAC,uDAAuD,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAgB,EAAE,QAA4B;QAC1D,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACjD,OAAO;gBACL,WAAW,EAAE,QAAQ,CAAC,QAAQ,IAAI,0BAA0B;gBAC5D,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,EAAE,EAAE,gDAAgD;aAC/D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,GAAG,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAgB;QAC3C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAe,CAAC,CAAC,CAAC;QAC7E,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;CACF;AAjCD,0CAiCC;AAED,8BAA8B;AAE9B,MAAa,oBAAoB;IAC/B,OAAO,KAAa,OAAO,sBAAsB,CAAC,CAAC,CAAC;IAEpD,QAAQ,CAAC,WAAmB;QAC1B,OAAO,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,CAAC,KAAK,CAAC,SAA2B,EAAE,MAA+B,EAAE,cAAkC;QAC3G,IAAI,OAAO,SAAS,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC1C,eAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,GAAG,2DAA2D,SAAS,CAAC,WAAW,aAAa,CAAC,CAAC;YACjI,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAClC,MAAM,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC;QAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,mBAAmB,CAAC;QAChD,MAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,oBAAoB;QAE9H,4CAA4C;QAC5C,MAAM,UAAU,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,qBAAqB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE1H,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,6CAA8B,CAAC;gBAClD,SAAS;gBACT,YAAY;gBACZ,UAAU;gBACV,aAAa,EAAE,KAAK;gBACpB,cAAc,EAAE,CAAC,IAAY,EAAU,EAAE,CAAC,IAAI,CAAC,MAAM;aACtD,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAErD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,MAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACnC,8BAA8B;gBAC9B,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACnC,MAAM;wBACJ,OAAO,EAAE,YAAY;wBACrB,QAAQ,EAAE;4BACR,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;4BACvB,cAAc,EAAE,YAAY,CAAC,MAAM;4BACnC,uEAAuE;yBACxE;qBACF,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,GAAG,kDAAkD,CAAC,GAAG,CAAC,CAAC;gBAC7F,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,GAAG,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACrF,4DAA4D;YAC5D,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,mCAAmC,YAAY,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAEO,oBAAoB;QAC1B,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACnD,CAAC;CACF;AA3DD,oDA2DC;AAED,MAAa,sBAAsB;IACjC,OAAO,KAAa,OAAO,wBAAwB,CAAC,CAAC,CAAC;IAEtD,QAAQ,CAAC,WAAmB;QAC1B,mEAAmE;QACnE,OAAO,WAAW,KAAK,0BAA0B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACxF,CAAC;IAED,KAAK,CAAC,CAAC,KAAK,CAAC,SAA2B,EAAE,MAA+B,EAAE,cAAkC;QAC3G,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;YACxC,eAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,GAAG,6DAA6D,SAAS,CAAC,WAAW,aAAa,CAAC,CAAC;YACnI,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC;QACjC,MAAM,SAAS,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC,iCAAiC;QAChF,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;QAEhC,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,OAAO,CAAC,6BAA6B;QACvC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,GAAG,2BAA2B,SAAS,+BAA+B,SAAS,SAAS,CAAC,CAAC;QAE1H,yDAAyD;QACzD,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,SAAS,CAAC,CAAC;YAC/C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YAE1D,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM;oBACJ,OAAO,EAAE,YAAY,EAAE,wBAAwB;oBAC/C,QAAQ,EAAE;wBACR,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;wBACvB,SAAS,EAAE,CAAC;wBACZ,OAAO,EAAE,GAAG;wBACZ,SAAS,EAAE,YAAY,CAAC,MAAM;qBAC/B;iBACF,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA5CD,wDA4CC;AAED;;;;GAIG;AACH,MAAa,mBAAmB;IACtB,MAAM,CAAC,QAAQ,GAAoC,IAAI,CAAC;IACxD,MAAM,CAAC,YAAY,GAAyB,IAAI,CAAC;IAEzD;;SAEK;IACE,MAAM,CAAC,UAAU,CAAC,YAA2B;QAClD,oBAAoB;QACpB,MAAM,UAAU,GAAwB;YACtC,IAAI,aAAa,EAAE;YACnB,IAAI,eAAe,EAAE,CAAC,qBAAqB;SAC5C,CAAC;QAEF,6BAA6B;QAC7B,MAAM,kBAAkB,GAAwB;YAC9C,IAAI,oBAAoB,EAAE;YAC1B,IAAI,sBAAsB,EAAE;SAC7B,CAAC;QAEF,wBAAwB;QACxB,MAAM,MAAM,GAA4B;YACtC,gBAAgB,EAAE,IAAA,kBAAS,EAAS,+BAA+B,EAAE,IAAI,CAAC;YAC1E,mBAAmB,EAAE,IAAA,kBAAS,EAAS,kCAAkC,EAAE,GAAG,CAAC;YAC/E,kBAAkB,EAAE,IAAA,kBAAS,EAAS,sCAAsC,EAAE,GAAG,CAAC;YAClF,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,WAAW;YACjC,mBAAmB,EAAE;gBACnB,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;gBAChD,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;gBAChD,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;gBAChD,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;gBAC/D,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;aAClD;YACD,qBAAqB,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;YAChE,sBAAsB,EAAE,OAAO;YAC/B,oBAAoB,EAAE,IAAI;YAC1B,SAAS,EAAE;gBACT,aAAa,EAAE,GAAG;gBAClB,iBAAiB,EAAE,EAAE;gBACrB,gBAAgB,EAAE,CAAC,oBAAoB,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;aACpF;SACF,CAAC;QAEF,sCAAsC;QACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,wBAAwB,CAAC,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,YAAY,CAAC,CAAC;QACnG,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC;IAED;;;;SAIK;IACE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,QAAgB;QAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,kCAAkC;QAClC,MAAM,UAAU,GAAG,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAErD,mBAAmB;QACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAEjE,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,yBAAyB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,MAAM,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;;;SAMK;IACE,MAAM,CAAC,KAAK,CAAC,cAAc,CAChC,UAAkB,EAClB,kBAA4B;QAC1B,gCAAgC;QAChC,kCAAkC;QAClC,uDAAuD;QACvD,mBAAmB;QACnB,qCAAqC;QACrC,iBAAiB;QACjB,+CAA+C;QAC/C,kBAAkB;QAClB,kDAAkD;QAClD,eAAe;QACf,iCAAiC;QACjC,mBAAmB;QACnB,+CAA+C;QAC/C,oDAAoD;QACpD,qBAAqB;QACrB,gCAAgC;QAChC,oBAAoB,EAAE,UAAU;QAChC,0BAA0B;QAC1B,gFAAgF;QAChF,sDAAsD;QAEtD,qCAAqC;QACrC,iCAAiC;QACjC,sEAAsE;QACtE,iBAAiB,EAAE,sBAAsB,EAAE,cAAc,EAAE,mBAAmB;QAC9E,eAAe,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW;QAC1D,qBAAqB,EAAE,YAAY,EAAE,iBAAiB,EAAE,mBAAmB;QAC3E,kBAAkB,EAAE,kBAAkB,EAAE,YAAY,EAAE,iBAAiB;QACvE,iBAAiB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa;QACnE,2BAA2B;QAC3B,UAAU,EAAE,+BAA+B,EAAE,gBAAgB,EAAE,mBAAmB;QAClF,oBAAoB,EAAE,6BAA6B;QACnD,QAAQ;QACR,4BAA4B,EAAE,mBAAmB,EAAE,wBAAwB;QAC3E,gBAAgB,EAAE,4BAA4B,EAAE,eAAe,EAAE,uBAAuB;QAExF,iCAAiC;QACjC,gEAAgE;QAChE,qCAAqC;QAErC,qCAAqC;QACrC,mEAAmE;QACnE,qBAAqB,EAAE,WAAW;QAElC,4BAA4B;QAC5B,2CAA2C;QAC3C,8BAA8B,EAAE,UAAU;QAC1C,gCAAgC;QAEhC,iCAAiC;QACjC,6DAA6D;QAC7D,2DAA2D;QAC3D,qDAAqD;QAErD,6BAA6B;QAC7B,uEAAuE;QACvE,2CAA2C;QAE3C,2BAA2B;QAC3B,6DAA6D;QAC7D,kCAAkC;QAClC,oCAAoC;QAEpC,mCAAmC;QACnC,0BAA0B,EAAE,YAAY;QACxC,+BAA+B;QAC/B,yBAAyB;QAEzB,iCAAiC;QACjC,sBAAsB,EAAE,YAAY;QACpC,kBAAkB,EAAE,QAAQ;QAC5B,qBAAqB,EAAE,iBAAiB;QACxC,0BAA0B,EAAE,aAAa;QAEzC,qCAAqC;QACrC,0CAA0C;QAC1C,6BAA6B;QAE7B,8BAA8B;QAC9B,uBAAuB,EAAE,qBAAqB;QAC9C,mBAAmB,EAAE,kBAAkB;QACvC,qEAAqE;QACrE,+EAA+E;QAE/E,yCAAyC;QACzC,kEAAkE;QAClE,4CAA4C,EAAE,uBAAuB;QACrE,wBAAwB,EAAE,uBAAuB;QAEjD,qCAAqC;QACrC,+DAA+D;QAC/D,oDAAoD;QAEpD,qCAAqC;QACrC,UAAU,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc;KAC5D,EACD,kBAA4B;QAC1B,iCAAiC;QACjC,oBAAoB,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc;QAC9E,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB;QACxE,kBAAkB;QAClB,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY;QACrD,uBAAuB;QACvB,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO;QAC/E,WAAW;QACX,cAAc,EAAE,cAAc,EAAE,gBAAgB;QAChD,4BAA4B;QAC5B,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY;QACtE,yBAAyB;QACzB,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,wBAAwB;QACpE,oCAAoC;QACpC,mBAAmB,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;QACvD,cAAc,EAAE,4BAA4B;QAC5C,eAAe,EAAE,cAAc;QAC/B,kCAAkC;QAClC,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,eAAe;QAChE,iBAAiB;QACjB,SAAS,EAAE,aAAa,EAAE,cAAc;QACxC,qDAAqD;QACrD,mDAAmD;QACnD,kDAAkD;QAClD,uCAAuC;QACvC,gBAAgB;QAChB,yCAAyC;KAC1C;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,0DAA0D;QAE1D,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,MAAM,IAAA,WAAI,EAAC,OAAO,EAAE;gBAChC,GAAG,EAAE,UAAU;gBACf,MAAM,EAAE,eAAe;gBACvB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YACH,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAC1B,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM,uBAAuB,UAAU,EAAE,CAAC,CAAC;QAEzE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,oCAAoC;QACpC,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAExE,oBAAoB;QACpB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAElE,IAAI,WAAW,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YAClC,eAAM,CAAC,IAAI,CAAC,mBAAmB,WAAW,CAAC,aAAa,uBAAuB,UAAU,EAAE,CAAC,CAAC;QAC/F,CAAC;QAED,4BAA4B;QAC5B,MAAM,UAAU,GAAkB,EAAE,CAAC;QACrC,KAAK,MAAM,MAAM,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACzC,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;;AA1PH,kDA2PC;AAED;;GAEG;AACH,MAAM,mBAAmB;IACf,QAAQ,CAAS;IAEzB,YAAY,QAAgB;QAC1B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,MAAM;QACJ,OAAO,UAAU,IAAI,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEnD,OAAO;YACL,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;YAClB,UAAU,EAAE,YAAY;YACxB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,YAAY,EAAE,IAAI,CAAC,KAAK;YACxB,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;SAC1C,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,+CAA+C;QAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAEO,WAAW,CAAC,QAAgB;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAEjD,+DAA+D;QAC/D,MAAM,SAAS,GAA2B;YACxC,0BAA0B;YAC1B,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,eAAe;YACtB,WAAW,EAAE,eAAe;YAC5B,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,iBAAiB;YACzB,OAAO,EAAE,eAAe;YACxB,WAAW,EAAE,eAAe;YAC5B,MAAM,EAAE,UAAU;YAClB,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,cAAc;YACxB,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,gBAAgB;YAC5B,SAAS,EAAE,eAAe;YAC1B,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,aAAa;YAEtB,2BAA2B;YAC3B,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,WAAW;YACnB,QAAQ,EAAE,uBAAuB;YACjC,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,sBAAsB;YAC9B,OAAO,EAAE,sBAAsB;YAC/B,MAAM,EAAE,eAAe;YACvB,SAAS,EAAE,wBAAwB;YACnC,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,eAAe;YACxB,SAAS,EAAE,eAAe;YAC1B,OAAO,EAAE,gBAAgB;YACzB,UAAU,EAAE,gBAAgB;YAE5B,0CAA0C;YAC1C,KAAK,EAAE,iBAAiB;YACxB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,UAAU;YAClB,KAAK,EAAE,iBAAiB;YACxB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,UAAU;YAClB,MAAM,EAAE,YAAY;YACpB,SAAS,EAAE,eAAe;YAC1B,QAAQ,EAAE,cAAc;YAExB,2BAA2B;YAC3B,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,0BAA0B;YACpC,MAAM,EAAE,YAAY;YAEpB,yBAAyB;YACzB,OAAO,EAAE,oBAAoB;YAC7B,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,cAAc;YACxB,KAAK,EAAE,cAAc;YACrB,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,eAAe;YAE1B,0BAA0B;YAC1B,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,UAAU;YAClB,MAAM,EAAE,YAAY;YAEpB,sBAAsB;YACtB,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;YACvB,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE,WAAW;YACnB,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,eAAe;YACvB,OAAO,EAAE,sBAAsB;YAC/B,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE,iBAAiB;YAC5B,WAAW,EAAE,iBAAiB;YAC9B,UAAU,EAAE,iBAAiB;YAC7B,QAAQ,EAAE,iBAAiB;YAC3B,SAAS,EAAE,iBAAiB;YAC5B,SAAS,EAAE,iBAAiB;YAC5B,SAAS,EAAE,iBAAiB;YAC5B,UAAU,EAAE,iBAAiB;YAC7B,SAAS,EAAE,iBAAiB;YAC5B,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,iBAAiB;YAE1B,6BAA6B;YAC7B,QAAQ,EAAE,cAAc;YACxB,IAAI,EAAE,mBAAmB;YACzB,KAAK,EAAE,qBAAqB;YAC5B,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,aAAa;YAEzB,kCAAkC;YAClC,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,aAAa;YACpB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,aAAa;YACpB,KAAK,EAAE,aAAa;YACpB,KAAK,EAAE,aAAa;YACpB,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,cAAc;YACrB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,gBAAgB;YACvB,MAAM,EAAE,yBAAyB;YACjC,KAAK,EAAE,cAAc;YACrB,MAAM,EAAE,cAAc;YACtB,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,gBAAgB;YACxB,OAAO,EAAE,gBAAgB;YACzB,OAAO,EAAE,gBAAgB;YACzB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,eAAe;YACvB,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;YACvB,SAAS,EAAE,eAAe;YAC1B,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,gBAAgB;YACvB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,aAAa;YACtB,IAAI,EAAE,cAAc;YACpB,QAAQ,EAAE,cAAc;YAExB,iCAAiC;YACjC,OAAO,EAAE,kBAAkB;YAC3B,QAAQ,EAAE,mBAAmB;YAC7B,QAAQ,EAAE,kBAAkB;YAC5B,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,kBAAkB;YAC3B,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,YAAY;YACrB,aAAa,EAAE,wBAAwB;YAEvC,8BAA8B;YAC9B,KAAK,EAAE,oBAAoB;YAC3B,OAAO,EAAE,oBAAoB;YAC7B,MAAM,EAAE,oBAAoB;YAC5B,OAAO,EAAE,aAAa;YACtB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,aAAa;YACtB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,oBAAoB;YAC7B,MAAM,EAAE,oBAAoB;YAC5B,MAAM,EAAE,oBAAoB;YAC5B,MAAM,EAAE,mBAAmB;YAC3B,OAAO,EAAE,mBAAmB;YAC5B,OAAO,EAAE,mBAAmB;YAC5B,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,aAAa;YACtB,MAAM,EAAE,YAAY;YACpB,SAAS,EAAE,eAAe;YAE1B,uCAAuC;YACvC,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,cAAc;YACxB,QAAQ,EAAE,cAAc;YACxB,SAAS,EAAE,eAAe;YAC1B,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,kBAAkB;YAC1B,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,eAAe;YAC1B,UAAU,EAAE,qBAAqB;YACjC,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE,aAAa;YACrB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,aAAa;YAEtB,6BAA6B;YAC7B,QAAQ,EAAE,qBAAqB;YAC/B,UAAU,EAAE,YAAY;YACxB,aAAa,EAAE,iBAAiB;YAChC,MAAM,EAAE,iBAAiB;YACzB,WAAW,EAAE,YAAY;YACzB,WAAW,EAAE,iBAAiB;YAC9B,kBAAkB,EAAE,iBAAiB;YACrC,UAAU,EAAE,YAAY;YAExB,2BAA2B;YAC3B,QAAQ,EAAE,cAAc;YACxB,SAAS,EAAE,WAAW;YACtB,QAAQ,EAAE,WAAW;YACrB,QAAQ,EAAE,WAAW;YACrB,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,aAAa;YACxB,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,aAAa;YACtB,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,WAAW;YAClB,WAAW,EAAE,iBAAiB;YAC9B,KAAK,EAAE,iBAAiB;YACxB,OAAO,EAAE,YAAY;YACrB,OAAO,EAAE,YAAY;YAErB,iCAAiC;YACjC,KAAK,EAAE,kBAAkB;YACzB,SAAS,EAAE,kBAAkB;YAC7B,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,kBAAkB;YAE1B,uCAAuC;YACvC,QAAQ,EAAE,iBAAiB;YAC3B,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE,eAAe;YAC1B,UAAU,EAAE,kBAAkB;YAC9B,UAAU,EAAE,kBAAkB;YAC9B,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,YAAY;YAEpB,uCAAuC;YACvC,WAAW,EAAE,iBAAiB;YAC9B,aAAa,EAAE,mBAAmB;YAClC,MAAM,EAAE,mBAAmB;YAC3B,OAAO,EAAE,aAAa;YACtB,QAAQ,EAAE,cAAc;YACxB,KAAK,EAAE,cAAc;YACrB,MAAM,EAAE,sBAAsB;YAC9B,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,aAAa;YACtB,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,eAAe;YAC1B,WAAW,EAAE,iBAAiB;YAC9B,aAAa,EAAE,mBAAmB;YAClC,YAAY,EAAE,kBAAkB;YAEhC,2CAA2C;YAC3C,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,cAAc;YACtB,UAAU,EAAE,gBAAgB;YAC5B,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,aAAa;YACtB,MAAM,EAAE,aAAa;YACrB,UAAU,EAAE,gBAAgB;YAC5B,KAAK,EAAE,sBAAsB;YAC7B,gBAAgB,EAAE,sBAAsB;YACxC,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,eAAe;YAE1B,qCAAqC;YACrC,MAAM,EAAE,mBAAmB;YAC3B,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,uBAAuB;YAC/B,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,aAAa;YACtB,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE,oBAAoB;YAC5B,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,aAAa;YACtB,SAAS,EAAE,eAAe;YAE1B,8BAA8B;YAC9B,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,YAAY;YACtB,OAAO,EAAE,YAAY;YACrB,QAAQ,EAAE,cAAc;YACxB,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,YAAY;YACxB,YAAY,EAAE,YAAY;YAC1B,SAAS,EAAE,YAAY;YACvB,UAAU,EAAE,YAAY;YACxB,eAAe,EAAE,YAAY;YAC7B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,YAAY;YAErB,yCAAyC;YACzC,MAAM,EAAE,oBAAoB;YAC5B,OAAO,EAAE,yEAAyE;YAClF,MAAM,EAAE,0BAA0B;YAClC,OAAO,EAAE,mEAAmE;YAC5E,MAAM,EAAE,+BAA+B;YACvC,OAAO,EAAE,2EAA2E;YACpF,MAAM,EAAE,iBAAiB;YAEzB,yCAAyC;YACzC,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,cAAc;YACtB,OAAO,EAAE,YAAY;YACrB,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,aAAa;YACrB,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,YAAY;SACtB,CAAC;QAEF,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,0BAA0B,CAAC;IACtD,CAAC;CACF", "sourcesContent": ["import * as fs from 'fs';\nimport * as path from 'path';\nimport { v4 as uuidv4 } from 'uuid';\nimport pLimit from 'p-limit'; // For concurrent processing\nimport type { Readable } from 'stream';\nimport { glob } from 'glob';\n\n// Core Polyfills (Using relative paths)\nimport { RecursiveCharacterTextSplitter } from '../../agents/workflows/corePolyfill';\n// Document might be used by specific chunkers or extractors later\n// import { Document } from '../../workflows/corePolyfill';\n\n// Local project imports (Ensure these paths are correct)\nimport { logger } from '../../logger';\nimport { getConfig } from '../../config';\nimport type { MemoryEntry, MemorySource, MemoryType } from '../types';\n\n// === Configuration ===\n\ninterface UniversalChunkingConfig {\n  // General\n  defaultChunkSize: number;\n  defaultChunkOverlap: number;\n  maxChunksPerSource: number; // Limit chunks per *any* source\n  concurrencyLimit: number; // Concurrency for batch processing\n\n  // Text specific\n  textChunkingStrategy: 'recursive' | 'semantic'; // Specific strategies only\n  recursiveSeparators: Record<string, string[]>; // Language specific separators\n  defaultTextSeparators: string[];\n\n  // Binary specific\n  binaryChunkingStrategy: 'fixed'; // Fixed strategy only\n  fixedBinaryChunkSize: number; // Size for fixed binary chunks\n\n  // Source specific (Examples)\n  localFile: {\n    maxFileSizeMB: number;\n    allowedExtensions: string[]; // If empty, allow all (after exclusion)\n    excludedPatterns: string[]; // Glob patterns for exclusion\n  };\n  // httpSource: { timeoutMs: number; maxRedirects: number; }; // Example\n  // s3Source: { region: string; }; // Example\n}\n\n// === Interfaces ===\n\n/**\n * Represents a source of data to be chunked (file, URL, DB query, etc.).\n */\ninterface IDataSource {\n  /** Unique identifier for the source (e.g., file path, URL, db://table/id) */\n  getUri(): string;\n  /** Provides metadata about the source */\n  getMetadata(): Promise<DataSourceMetadata>;\n  /** Returns a readable stream of the source's content */\n  getContentStream(): Promise<Readable>;\n  /** Optional: Cleanup resources associated with the source */\n  cleanup?(): Promise<void>;\n}\n\ninterface DataSourceMetadata {\n  uri: string;\n  sourceType: string; // e.g., 'local_file', 'http_url', 's3_object', 'postgres_query'\n  size?: number; // Size in bytes, if known\n  lastModified?: Date;\n  mimeType?: string; // Detected or provided MIME type\n  fileName?: string; // Original file name, if applicable\n  [key: string]: string | number | boolean | Date | undefined; // Allow for additional source-specific metadata\n}\n\n/**\n * Extracts meaningful content (text, raw bytes, structured data) from a data source stream.\n */\ninterface IContentExtractor {\n  /** Unique name for this extractor */\n  getName(): string;\n  /** Checks if this extractor can handle the given metadata */\n  supports(metadata: DataSourceMetadata): boolean;\n  /** Extracts content from the stream */\n  extract(stream: Readable, metadata: DataSourceMetadata): Promise<ExtractedContent>;\n}\n\ninterface ExtractedContent {\n  /** The primary content type extracted (e.g., 'text/plain', 'application/pdf', 'image/jpeg', 'application/octet-stream') */\n  contentType: string;\n  /** The extracted content (string for text, Buffer for binary/raw) */\n  content: string | Buffer;\n  /** Additional metadata extracted from the content (e.g., PDF pages, image dimensions, text language) */\n  metadata: Record<string, string | number | boolean | undefined>;\n}\n\n/**\n * Defines a strategy for splitting extracted content into chunks.\n */\ninterface IChunkingStrategy {\n  /** Unique name for this strategy */\n  getName(): string;\n  /** Checks if this strategy applies to the extracted content type */\n  supports(contentType: string): boolean;\n  /** Chunks the content using an AsyncGenerator */\n  chunk(content: ExtractedContent, config: UniversalChunkingConfig, sourceMetadata: DataSourceMetadata): AsyncGenerator<ChunkData>;\n}\n\ninterface ChunkData {\n  /** The content of the chunk (string or Buffer) */\n  content: string | Buffer;\n  /** Metadata specific to this chunk (e.g., chunk index, position) */\n  metadata: Record<string, string | number | boolean | undefined>;\n}\n\n/**\n * Interface for the final step: storing the generated memory entry.\n * (This was previously implicitly `codessaMemoryProvider`)\n */\ninterface IMemoryStorer {\n  storeMemory(entryData: { content: string | Buffer; metadata: Record<string, string | number | boolean | string[] | undefined> }): Promise<MemoryEntry>;\n}\n\n// === Results ===\n\ninterface ProcessResult {\n  sourceUri: string;\n  success: boolean;\n  status: 'processed' | 'skipped' | 'error';\n  chunkCount: number;\n  addedEntries: MemoryEntry[];\n  message?: string; // Reason for skip or error details\n  metadata?: DataSourceMetadata;\n  extractedContentType?: string;\n  chunkingStrategyUsed?: string;\n}\n\ninterface BatchProcessResult {\n  totalSources: number;\n  processedSources: number;\n  skippedSources: number;\n  failedSources: number;\n  totalChunksAdded: number;\n  results: ProcessResult[];\n  errors: { uri: string; error: string }[];\n}\n\n\n// === Core Service ===\n\nexport class UniversalChunkingService {\n  private readonly config: UniversalChunkingConfig;\n  private readonly extractors: IContentExtractor[];\n  private readonly chunkingStrategies: IChunkingStrategy[];\n  private readonly memoryStorer: IMemoryStorer;\n\n  constructor(\n    config: UniversalChunkingConfig,\n    extractors: IContentExtractor[],\n    chunkingStrategies: IChunkingStrategy[],\n    memoryStorer: IMemoryStorer\n  ) {\n    this.config = this.validateConfig(config);\n    this.extractors = extractors;\n    this.chunkingStrategies = chunkingStrategies;\n    this.memoryStorer = memoryStorer;\n\n    if (this.extractors.length === 0) {\n      throw new Error('UniversalChunkingService requires at least one IContentExtractor.');\n    }\n    if (this.chunkingStrategies.length === 0) {\n      throw new Error('UniversalChunkingService requires at least one IChunkingStrategy.');\n    }\n    logger.info('UniversalChunkingService initialized.');\n  }\n\n  private validateConfig(config: Partial<UniversalChunkingConfig>): UniversalChunkingConfig {\n    // Add thorough validation for all config options here\n    const validatedConfig = { ...config } as UniversalChunkingConfig;\n\n    validatedConfig.defaultChunkSize = Math.max(50, config.defaultChunkSize ?? 1000);\n    validatedConfig.defaultChunkOverlap = Math.max(0, config.defaultChunkOverlap ?? 200);\n    validatedConfig.maxChunksPerSource = Math.max(1, config.maxChunksPerSource ?? 500);\n    validatedConfig.concurrencyLimit = Math.max(1, config.concurrencyLimit ?? 5);\n    validatedConfig.fixedBinaryChunkSize = Math.max(128, config.fixedBinaryChunkSize ?? 4096);\n    validatedConfig.localFile = config.localFile ?? { maxFileSizeMB: 100, allowedExtensions: [], excludedPatterns: [] };\n    validatedConfig.localFile.maxFileSizeMB = Math.max(0.1, validatedConfig.localFile.maxFileSizeMB ?? 100);\n\n    // Set default values for missing properties\n    validatedConfig.textChunkingStrategy = config.textChunkingStrategy ?? 'recursive';\n    validatedConfig.recursiveSeparators = config.recursiveSeparators ?? {};\n    validatedConfig.defaultTextSeparators = config.defaultTextSeparators ?? ['\\n\\n', '\\n', '. ', '? ', '! ', ' ', ''];\n    validatedConfig.binaryChunkingStrategy = config.binaryChunkingStrategy ?? 'fixed';\n\n    // Ensure overlap is less than chunk size\n    validatedConfig.defaultChunkOverlap = Math.min(validatedConfig.defaultChunkOverlap, validatedConfig.defaultChunkSize - 10);\n    return validatedConfig;\n  }\n\n  /**\n     * Processes a batch of data sources concurrently.\n     * @param dataSources An array of IDataSource instances.\n     * @param progressCallback Optional callback for progress updates.\n     * @returns A promise resolving to a BatchProcessResult.\n     */\n  public async processBatch(\n    dataSources: IDataSource[],\n    progressCallback?: (processed: number, total: number, currentUri?: string) => void\n  ): Promise<BatchProcessResult> {\n    const totalSources = dataSources.length;\n    logger.info(`Starting batch processing for ${totalSources} data sources with concurrency ${this.config.concurrencyLimit}.`);\n\n    const limit = pLimit(this.config.concurrencyLimit);\n    const results: ProcessResult[] = [];\n    let processedCount = 0;\n\n    const processingPromises = dataSources.map((source, index) =>\n      limit(async (): Promise<ProcessResult> => {\n        const uri = source.getUri();\n        logger.debug(`[Batch ${index + 1}/${totalSources}] Processing source: ${uri}`);\n        if (progressCallback) {\n          progressCallback(processedCount, totalSources, uri);\n        }\n        const result = await this.processDataSource(source);\n        results.push(result);\n        processedCount++;\n        if (progressCallback) {\n          // Update progress after completion\n          progressCallback(processedCount, totalSources);\n        }\n        logger.debug(`[Batch ${index + 1}/${totalSources}] Finished source: ${uri} - Status: ${result.status}`);\n        return result; // p-limit expects a return value\n      })\n    );\n\n    // Wait for all promises to settle\n    await Promise.allSettled(processingPromises);\n\n    // Aggregate results\n    const batchResult: BatchProcessResult = {\n      totalSources,\n      processedSources: results.filter(r => r.status === 'processed').length,\n      skippedSources: results.filter(r => r.status === 'skipped').length,\n      failedSources: results.filter(r => r.status === 'error').length,\n      totalChunksAdded: results.reduce((sum, r) => sum + r.chunkCount, 0),\n      results: results,\n      errors: results.filter(r => r.status === 'error').map(r => ({ uri: r.sourceUri, error: r.message ?? 'Unknown error' })),\n    };\n\n    logger.info(`Batch processing completed. Processed: ${batchResult.processedSources}, Skipped: ${batchResult.skippedSources}, Failed: ${batchResult.failedSources}, Chunks Added: ${batchResult.totalChunksAdded}`);\n    if (batchResult.failedSources > 0) {\n      logger.warn(`Batch processing encountered ${batchResult.failedSources} errors.`);\n    }\n\n    return batchResult;\n  }\n\n\n  /**\n     * Processes a single data source: gets metadata, extracts content, chunks, and stores memory entries.\n     * @param dataSource The IDataSource instance to process.\n     * @returns A promise resolving to a ProcessResult.\n     */\n  public async processDataSource(dataSource: IDataSource): Promise<ProcessResult> {\n    const sourceUri = dataSource.getUri();\n    let metadata: DataSourceMetadata | undefined;\n    let stream: Readable | undefined;\n    let result: ProcessResult = { // Initialize result object\n      sourceUri,\n      success: false,\n      status: 'error', // Default to error\n      chunkCount: 0,\n      addedEntries: [],\n      message: 'Processing did not complete.',\n    };\n\n    try {\n      // 1. Get Metadata\n      logger.debug(`[${sourceUri}] Getting metadata...`);\n      metadata = await dataSource.getMetadata();\n      result.metadata = metadata; // Store metadata in result\n\n      // 2. Check Preconditions (e.g., size limits for specific source types)\n      if (metadata.sourceType === 'local_file' && metadata.size) {\n        const fileSizeMB = metadata.size / (1024 * 1024);\n        if (fileSizeMB > this.config.localFile.maxFileSizeMB) {\n          logger.warn(`[${sourceUri}] Skipping: File size ${fileSizeMB.toFixed(2)}MB exceeds limit ${this.config.localFile.maxFileSizeMB}MB.`);\n          return { ...result, success: false, status: 'skipped', message: `File size exceeds limit (${this.config.localFile.maxFileSizeMB}MB)` };\n        }\n        if (metadata.size === 0) {\n          logger.info(`[${sourceUri}] Skipping: File is empty.`);\n          return { ...result, success: false, status: 'skipped', message: 'Source is empty.' };\n        }\n      }\n      // Add more source-specific precondition checks here\n\n      // 3. Find Suitable Extractor\n      logger.debug(`[${sourceUri}] Finding content extractor...`);\n      const extractor = this.findExtractor(metadata);\n      if (!extractor) {\n        logger.warn(`[${sourceUri}] Skipping: No suitable content extractor found for metadata:`, metadata);\n        return { ...result, success: false, status: 'skipped', message: 'No suitable content extractor found.' };\n      }\n      logger.debug(`[${sourceUri}] Using extractor: ${extractor.getName()}`);\n\n      // 4. Get Content Stream\n      logger.debug(`[${sourceUri}] Getting content stream...`);\n      stream = await dataSource.getContentStream();\n\n      // 5. Extract Content\n      logger.debug(`[${sourceUri}] Extracting content...`);\n      const extractedContent = await extractor.extract(stream, metadata);\n      result.extractedContentType = extractedContent.contentType; // Store content type\n\n      // Handle empty extracted content\n      if ((typeof extractedContent.content === 'string' && extractedContent.content.length === 0) ||\n        (Buffer.isBuffer(extractedContent.content) && extractedContent.content.length === 0)) {\n        logger.info(`[${sourceUri}] Skipping: Extracted content is empty.`);\n        // Consider this success, but skipped chunking\n        return { ...result, success: true, status: 'skipped', message: 'Extracted content is empty.', chunkCount: 0 };\n      }\n\n      // 6. Find Suitable Chunking Strategy\n      logger.debug(`[${sourceUri}] Finding chunking strategy for content type: ${extractedContent.contentType}...`);\n      const chunker = this.findChunkingStrategy(extractedContent.contentType);\n      if (!chunker) {\n        logger.warn(`[${sourceUri}] Skipping: No suitable chunking strategy found for content type: ${extractedContent.contentType}`);\n        return { ...result, success: false, status: 'skipped', message: `No chunking strategy for ${extractedContent.contentType}` };\n      }\n      logger.debug(`[${sourceUri}] Using chunking strategy: ${chunker.getName()}`);\n      result.chunkingStrategyUsed = chunker.getName(); // Store strategy used\n\n      // 7. Chunk Content and Store Memory Entries\n      logger.debug(`[${sourceUri}] Chunking content and storing entries...`);\n      let chunkIndex = 0;\n      const addedEntries: MemoryEntry[] = [];\n\n      for await (const chunk of chunker.chunk(extractedContent, this.config, metadata)) {\n        if (chunkIndex >= this.config.maxChunksPerSource) {\n          logger.warn(`[${sourceUri}] Reached maximum chunk limit (${this.config.maxChunksPerSource}). Stopping chunking for this source.`);\n          break;\n        }\n\n        // Simple check for effectively empty chunks (whitespace string or empty buffer)\n        const isEmptyChunk = (typeof chunk.content === 'string' && chunk.content.trim().length === 0) ||\n          (Buffer.isBuffer(chunk.content) && chunk.content.length === 0);\n\n        if (isEmptyChunk) {\n          logger.debug(`[${sourceUri}] Skipping empty chunk at index ${chunkIndex}.`);\n          continue; // Don't increment chunkIndex for skipped empty chunks\n        }\n\n        const entryMetadata = this.prepareMemoryEntryMetadata(\n          metadata,\n          extractedContent,\n          chunk.metadata,\n          chunkIndex,\n          sourceUri\n        );\n\n        try {\n          const addedEntry = await this.memoryStorer.storeMemory({\n            content: chunk.content, // Pass string or Buffer directly\n            metadata: entryMetadata,\n          });\n          // Ensure the returned entry has an ID and merged metadata\n          if (!addedEntry.id) {\n            logger.warn(`[${sourceUri}] Memory storer did not return an ID for chunk ${chunkIndex}. Assigning fallback.`);\n            const chunkId = entryMetadata.chunkId;\n            addedEntry.id = (typeof chunkId === 'string' ? chunkId : `chunk_${uuidv4()}`); // Use generated chunkId or UUID\n          }\n          addedEntry.metadata = { ...entryMetadata, ...addedEntry.metadata }; // Merge metadata\n          addedEntries.push(addedEntry);\n          chunkIndex++; // Increment only for successfully added non-empty chunks\n        } catch (storeError: unknown) {\n          logger.error(`[${sourceUri}] Failed to store chunk ${chunkIndex}:`, storeError);\n          // Decide on error strategy: stop processing this source or continue?\n          // For now, stop processing this source on storage error.\n          const errorMessage = storeError instanceof Error ? storeError.message : String(storeError);\n          throw new Error(`Failed to store chunk ${chunkIndex}: ${errorMessage}`);\n        }\n      }\n\n      logger.info(`[${sourceUri}] Successfully processed and stored ${chunkIndex} chunks.`);\n      result = {\n        ...result,\n        success: true,\n        status: 'processed',\n        chunkCount: chunkIndex,\n        addedEntries: addedEntries,\n        message: `Processed successfully with ${chunkIndex} chunks.`,\n      };\n      return result;\n\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`[${sourceUri}] Failed to process source:`, { message: errorMessage, stack: errorStack });\n      // Ensure result reflects the error state\n      result = {\n        ...result, // Keep any previously set fields like metadata if available\n        success: false,\n        status: 'error',\n        message: errorMessage || 'Unknown processing error.',\n      };\n      return result;\n    } finally {\n      // 8. Cleanup Data Source (if applicable)\n      if (dataSource.cleanup) {\n        try {\n          logger.debug(`[${sourceUri}] Cleaning up data source...`);\n          await dataSource.cleanup();\n        } catch (cleanupError: unknown) {\n          logger.warn(`[${sourceUri}] Error during data source cleanup:`, cleanupError);\n        }\n      }\n      // Ensure stream is destroyed if it exists and wasn't fully consumed or errored\n      if (stream && !stream.destroyed) {\n        stream.destroy();\n      }\n    }\n  }\n\n  private findExtractor(metadata: DataSourceMetadata): IContentExtractor | undefined {\n    // Find the first extractor that supports the metadata\n    return this.extractors.find(e => e.supports(metadata));\n  }\n\n  private findChunkingStrategy(contentType: string): IChunkingStrategy | undefined {\n    // Find the first strategy that supports the content type\n    return this.chunkingStrategies.find(cs => cs.supports(contentType));\n  }\n\n  private prepareMemoryEntryMetadata(\n    sourceMetadata: DataSourceMetadata,\n    extractedContent: ExtractedContent,\n    chunkMetadata: Record<string, string | number | boolean | undefined>,\n    chunkIndex: number,\n    sourceUri: string\n  ): Record<string, string | number | boolean | string[] | undefined> {\n    // Combine metadata from all stages\n    const combined = {\n      // Source Info\n      sourceUri: sourceUri,\n      sourceType: sourceMetadata.sourceType,\n      fileName: sourceMetadata.fileName,\n      fileExtension: sourceMetadata.fileName ? path.extname(sourceMetadata.fileName).toLowerCase() : undefined,\n      fileSize: sourceMetadata.size,\n      fileMtime: sourceMetadata.lastModified?.toISOString(),\n\n      // Extraction Info\n      extractedContentType: extractedContent.contentType,\n      ...extractedContent.metadata, // Metadata from the extractor (e.g., page number)\n\n      // Chunking Info\n      chunkIndex: chunkIndex,\n      chunkId: `${sourceUri}::chunk_${chunkIndex}`, // Generate a predictable chunk ID\n      ...chunkMetadata, // Metadata from the chunker itself\n\n      // Standard Memory Entry Fields (Map from source/chunk info)\n      id: `mem_${uuidv4()}`, // Let storer override this\n      timestamp: Date.now(),\n      // Determine MemorySource and MemoryType based on sourceType/contentType\n      source: this.mapToMemorySource(sourceMetadata.sourceType),\n      type: this.mapToMemoryType(extractedContent.contentType, sourceMetadata.sourceType),\n      tags: this.generateTags(sourceMetadata, extractedContent),\n    };\n\n    // Remove undefined fields for cleaner metadata\n    Object.keys(combined).forEach(key => {\n      if (combined[key as keyof typeof combined] === undefined) {\n        delete combined[key as keyof typeof combined];\n      }\n    });\n    return combined;\n  }\n\n  // Helper methods to map to your specific MemorySource/MemoryType enums\n  private mapToMemorySource(sourceType: string): MemorySource {\n    // Example mapping\n    if (sourceType === 'local_file' || sourceType === 'http_url' || sourceType === 's3_object') return 'file';\n    if (sourceType.includes('db') || sourceType.includes('database')) return 'database';\n    // Add other mappings (WEB, USER_INPUT, etc.)\n    return 'workspace'; // Default\n  }\n\n  private mapToMemoryType(contentType: string, sourceType: string): MemoryType {\n    // Example mapping - refine based on your needs\n    if (contentType.startsWith('text/')) return 'text';\n    if (contentType.includes('pdf')) return 'document';\n    if (contentType.includes('word') || contentType.includes('opendocument.text')) return 'document';\n    if (contentType.startsWith('image/')) return 'image';\n    if (contentType.startsWith('audio/')) return 'audio';\n    if (contentType.startsWith('video/')) return 'video';\n    if (contentType === 'application/octet-stream' || contentType.includes('binary')) return 'binary';\n    // Fallback based on source if content type is generic\n    if (sourceType === 'local_file') return 'code'; // Generic file\n    return 'text'; // Default\n  }\n\n  private generateTags(sourceMetadata: DataSourceMetadata, extractedContent: ExtractedContent): string[] {\n    const tags = new Set<string>();\n    tags.add(this.mapToMemorySource(sourceMetadata.sourceType).toLowerCase());\n    tags.add(this.mapToMemoryType(extractedContent.contentType, sourceMetadata.sourceType).toLowerCase());\n\n    if (sourceMetadata.fileName) {\n      const ext = path.extname(sourceMetadata.fileName).toLowerCase().replace('.', '');\n      if (ext) {\n        tags.add(`ext:${ext}`);\n      }\n    }\n    if (extractedContent.contentType) {\n      tags.add(`mime:${extractedContent.contentType.replace('/', '_')}`);\n    }\n    // Add tags from extracted metadata if available (e.g., language)\n    if (extractedContent.metadata?.language) {\n      tags.add(`lang:${extractedContent.metadata.language}`);\n    }\n\n    return Array.from(tags);\n  }\n}\n\n\n// === Default Implementations (Examples) ===\n\n// --- Data Sources ---\n\nexport class LocalFileSource implements IDataSource {\n  private readonly filePath: string;\n  private readonly uri: string;\n\n  constructor(filePath: string) {\n    if (!path.isAbsolute(filePath)) {\n      throw new Error(`LocalFileSource requires an absolute path. Received: ${filePath}`);\n    }\n    this.filePath = filePath;\n    this.uri = `file://${this.filePath}`; // Use file URI scheme\n  }\n\n  getUri(): string {\n    return this.uri;\n  }\n\n  async getMetadata(): Promise<DataSourceMetadata> {\n    try {\n      const stats = await fs.promises.stat(this.filePath);\n      if (!stats.isFile()) {\n        throw new Error(`Path is not a file: ${this.filePath}`);\n      }\n      // Basic MIME type detection (can be improved with libraries like 'mime-types' or 'file-type')\n      const mimeType = this.detectMimeType(this.filePath);\n\n      return {\n        uri: this.uri,\n        sourceType: 'local_file',\n        size: stats.size,\n        lastModified: stats.mtime,\n        fileName: path.basename(this.filePath),\n        mimeType: mimeType,\n        filePath: this.filePath, // Include original path if needed downstream\n      };\n    } catch (error: unknown) {\n      logger.error(`[${this.uri}] Error getting metadata:`, error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Failed to get metadata for ${this.filePath}: ${errorMessage}`);\n    }\n  }\n\n  async getContentStream(): Promise<Readable> {\n    try {\n      // Create stream only when requested\n      const stream = fs.createReadStream(this.filePath);\n      stream.on('error', (err) => {\n        logger.error(`[${this.uri}] Error reading file stream:`, err);\n        // Error handling is crucial for streams\n      });\n      // Add minimal async operation to justify async\n      await Promise.resolve();\n      return stream;\n    } catch (error: unknown) {\n      logger.error(`[${this.uri}] Error creating read stream:`, error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Failed to create read stream for ${this.filePath}: ${errorMessage}`);\n    }\n  }\n\n  // Basic MIME detection based on extension - replace with a robust library\n  private detectMimeType(filePath: string): string | undefined {\n    const ext = path.extname(filePath).toLowerCase();\n    const mimeMap: Record<string, string> = {\n      '.txt': 'text/plain', '.md': 'text/markdown', '.html': 'text/html', '.css': 'text/css', '.js': 'text/javascript',\n      '.json': 'application/json', '.xml': 'application/xml', '.pdf': 'application/pdf',\n      '.png': 'image/png', '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg', '.gif': 'image/gif', '.svg': 'image/svg+xml',\n      '.wav': 'audio/wav', '.mp3': 'audio/mpeg', '.ogg': 'audio/ogg',\n      '.mp4': 'video/mp4', '.webm': 'video/webm',\n      '.zip': 'application/zip', '.gz': 'application/gzip',\n      // Add many more...\n    };\n    return mimeMap[ext] || 'application/octet-stream'; // Default binary\n  }\n\n  // No cleanup needed for simple file reads\n  // async cleanup(): Promise<void> {}\n}\n\n// --- Content Extractors ---\n\nexport class TextExtractor implements IContentExtractor {\n  private readonly supportedMimeTypes: Set<string>;\n  private readonly defaultEncoding: BufferEncoding;\n\n  constructor(supportedMimeTypes: string[] = ['text/plain', 'text/markdown', 'text/html', 'text/css', 'text/javascript', 'application/json', 'application/xml'], defaultEncoding: BufferEncoding = 'utf-8') {\n    this.supportedMimeTypes = new Set(supportedMimeTypes);\n    this.defaultEncoding = defaultEncoding;\n  }\n\n  getName(): string { return 'TextExtractor'; }\n\n  supports(metadata: DataSourceMetadata): boolean {\n    // Support if MIME type matches OR if it's a file with a known text extension and no specific binary MIME\n    const isSupportedMime = metadata.mimeType ? this.supportedMimeTypes.has(metadata.mimeType) : false;\n\n    // Check if it's a text file based on extension\n    let isLikelyTextFile = false;\n    if (metadata.sourceType === 'local_file' && metadata.fileName) {\n      const fileExtension = path.extname(metadata.fileName).toLowerCase();\n      const textExtensions = ['.txt', '.md', '.log', '.csv', '.tsv', '.html', '.css', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp', '.h', '.hpp', '.cs', '.go', '.rb', '.php', '.sh', '.bash', '.ps1', '.xml', '.json', '.yaml', '.yml', '.sql', '.graphql', '.gql', '.dockerfile', '.tf', '.hcl', '.swift', '.kt', '.kts', '.groovy', '.scala', '.rs', '.lua', '.pl', '.pm', '.r', '.dart', '.vue', '.svelte'];\n\n      isLikelyTextFile = textExtensions.includes(fileExtension) &&\n        (!metadata.mimeType || metadata.mimeType === 'application/octet-stream');\n    }\n\n    return isSupportedMime || isLikelyTextFile;\n  }\n\n  async extract(stream: Readable, metadata: DataSourceMetadata): Promise<ExtractedContent> {\n    try {\n      // Read the entire stream into a buffer, then decode\n      // For *very* large text files, stream processing line-by-line might be better,\n      // but requires changes in chunking strategies too. This is simpler for now.\n      const buffer = await this.streamToBuffer(stream);\n      const content = buffer.toString(this.defaultEncoding); // Assume UTF-8, could try detecting encoding\n\n      // TODO: Add language detection using a library (e.g., 'franc')\n      // const language = detectLanguage(content);\n\n      return {\n        contentType: metadata.mimeType ?? 'text/plain', // Use detected MIME or default\n        content: content,\n        metadata: {\n          encoding: this.defaultEncoding,\n          // language: language // Add detected language if available\n        },\n      };\n    } catch (error: unknown) {\n      logger.error(`[${metadata.uri}] Error extracting text content:`, error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Failed to extract text content: ${errorMessage}`);\n    }\n  }\n\n  private async streamToBuffer(stream: Readable): Promise<Buffer> {\n    const chunks: Buffer[] = [];\n    for await (const chunk of stream) {\n      chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk as string));\n    }\n    return Buffer.concat(chunks);\n  }\n}\n\nexport class BinaryExtractor implements IContentExtractor {\n  getName(): string { return 'BinaryExtractor'; }\n\n  supports(metadata: DataSourceMetadata): boolean {\n    // Acts as a fallback for anything not handled by other extractors\n    // Log the metadata for debugging purposes\n    logger.debug(`BinaryExtractor supporting fallback extraction for: ${metadata.uri}`);\n    return true;\n  }\n\n  async extract(stream: Readable, metadata: DataSourceMetadata): Promise<ExtractedContent> {\n    try {\n      // Read the entire stream into a buffer\n      const buffer = await this.streamToBuffer(stream);\n      return {\n        contentType: metadata.mimeType ?? 'application/octet-stream',\n        content: buffer,\n        metadata: {}, // No specific metadata extracted from raw bytes\n      };\n    } catch (error: unknown) {\n      logger.error(`[${metadata.uri}] Error extracting binary content:`, error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Failed to extract binary content: ${errorMessage}`);\n    }\n  }\n\n  private async streamToBuffer(stream: Readable): Promise<Buffer> {\n    const chunks: Buffer[] = [];\n    for await (const chunk of stream) {\n      chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk as string));\n    }\n    return Buffer.concat(chunks);\n  }\n}\n\n// --- Chunking Strategies ---\n\nexport class RecursiveTextChunker implements IChunkingStrategy {\n  getName(): string { return 'RecursiveTextChunker'; }\n\n  supports(contentType: string): boolean {\n    return contentType.startsWith('text/');\n  }\n\n  async *chunk(extracted: ExtractedContent, config: UniversalChunkingConfig, sourceMetadata: DataSourceMetadata): AsyncGenerator<ChunkData> {\n    if (typeof extracted.content !== 'string') {\n      logger.warn(`[${sourceMetadata.uri}] RecursiveTextChunker received non-string content type ${extracted.contentType}. Skipping.`);\n      return;\n    }\n\n    const content = extracted.content;\n    const chunkSize = config.defaultChunkSize;\n    const chunkOverlap = config.defaultChunkOverlap;\n    const extension = sourceMetadata.fileName ? path.extname(sourceMetadata.fileName).toLowerCase() : '.txt'; // Default extension\n\n    // Get separators based on config or default\n    const separators = config.recursiveSeparators?.[extension] ?? config.defaultTextSeparators ?? this.getDefaultSeparators();\n\n    try {\n      const splitter = new RecursiveCharacterTextSplitter({\n        chunkSize,\n        chunkOverlap,\n        separators,\n        keepSeparator: false,\n        lengthFunction: (text: string): number => text.length,\n      });\n\n      const textChunks = await splitter.splitText(content);\n\n      for (let i = 0; i < textChunks.length; i++) {\n        const chunkContent = textChunks[i];\n        // Skip whitespace-only chunks\n        if (chunkContent.trim().length > 0) {\n          yield {\n            content: chunkContent,\n            metadata: {\n              chunker: this.getName(),\n              originalLength: chunkContent.length,\n              // Add line numbers if feasible/needed (requires more complex tracking)\n            },\n          };\n        } else {\n          logger.debug(`[${sourceMetadata.uri}] Skipping whitespace-only text chunk at index ${i}.`);\n        }\n      }\n    } catch (error: unknown) {\n      logger.error(`[${sourceMetadata.uri}] Error during recursive text chunking:`, error);\n      // Re-throw or handle as needed; maybe yield an error chunk?\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Recursive text chunking failed: ${errorMessage}`);\n    }\n  }\n\n  private getDefaultSeparators(): string[] {\n    return ['\\n\\n', '\\n', '. ', '? ', '! ', ' ', ''];\n  }\n}\n\nexport class FixedSizeBinaryChunker implements IChunkingStrategy {\n  getName(): string { return 'FixedSizeBinaryChunker'; }\n\n  supports(contentType: string): boolean {\n    // Apply to generic binary or any type not handled by text chunkers\n    return contentType === 'application/octet-stream' || !contentType.startsWith('text/');\n  }\n\n  async *chunk(extracted: ExtractedContent, config: UniversalChunkingConfig, sourceMetadata: DataSourceMetadata): AsyncGenerator<ChunkData> {\n    if (!Buffer.isBuffer(extracted.content)) {\n      logger.warn(`[${sourceMetadata.uri}] FixedSizeBinaryChunker received non-buffer content type ${extracted.contentType}. Skipping.`);\n      return;\n    }\n\n    const buffer = extracted.content;\n    const chunkSize = config.fixedBinaryChunkSize; // Use binary-specific chunk size\n    const totalSize = buffer.length;\n\n    if (totalSize === 0) {\n      return; // No chunks for empty buffer\n    }\n\n    logger.debug(`[${sourceMetadata.uri}] Chunking binary data (${totalSize} bytes) into fixed sizes of ${chunkSize} bytes.`);\n\n    // Add minimal async operation to justify async generator\n    await Promise.resolve();\n\n    for (let i = 0; i < totalSize; i += chunkSize) {\n      const end = Math.min(i + chunkSize, totalSize);\n      const chunkContent = Buffer.from(buffer.subarray(i, end));\n\n      if (chunkContent.length > 0) {\n        yield {\n          content: chunkContent, // Yield Buffer directly\n          metadata: {\n            chunker: this.getName(),\n            startByte: i,\n            endByte: end,\n            chunkSize: chunkContent.length,\n          },\n        };\n      }\n    }\n  }\n}\n\n/**\n * FileChunkingService - A service for chunking files and workspace folders\n * This is a facade over the UniversalChunkingService that provides a simpler API\n * for common file chunking operations.\n */\nexport class FileChunkingService {\n  private static instance: UniversalChunkingService | null = null;\n  private static memoryStorer: IMemoryStorer | null = null;\n\n  /**\n     * Initialize the FileChunkingService with a memory storer\n     */\n  public static initialize(memoryStorer: IMemoryStorer): void {\n    // Create extractors\n    const extractors: IContentExtractor[] = [\n      new TextExtractor(),\n      new BinaryExtractor() // Fallback extractor\n    ];\n\n    // Create chunking strategies\n    const chunkingStrategies: IChunkingStrategy[] = [\n      new RecursiveTextChunker(),\n      new FixedSizeBinaryChunker()\n    ];\n\n    // Create default config\n    const config: UniversalChunkingConfig = {\n      defaultChunkSize: getConfig<number>('memory.fileChunking.chunkSize', 1000),\n      defaultChunkOverlap: getConfig<number>('memory.fileChunking.chunkOverlap', 200),\n      maxChunksPerSource: getConfig<number>('memory.fileChunking.maxChunksPerFile', 100),\n      concurrencyLimit: 5,\n      textChunkingStrategy: 'recursive',\n      recursiveSeparators: {\n        '.py': ['\\n\\n', '\\n', '. ', '? ', '! ', ' ', ''],\n        '.js': ['\\n\\n', '\\n', '. ', '? ', '! ', ' ', ''],\n        '.ts': ['\\n\\n', '\\n', '. ', '? ', '! ', ' ', ''],\n        '.md': ['\\n\\n', '\\n', '## ', '### ', '. ', '? ', '! ', ' ', ''],\n        '.txt': ['\\n\\n', '\\n', '. ', '? ', '! ', ' ', '']\n      },\n      defaultTextSeparators: ['\\n\\n', '\\n', '. ', '? ', '! ', ' ', ''],\n      binaryChunkingStrategy: 'fixed',\n      fixedBinaryChunkSize: 4096,\n      localFile: {\n        maxFileSizeMB: 100,\n        allowedExtensions: [],\n        excludedPatterns: ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.git/**']\n      }\n    };\n\n    // Create the UniversalChunkingService\n    this.instance = new UniversalChunkingService(config, extractors, chunkingStrategies, memoryStorer);\n    this.memoryStorer = memoryStorer;\n\n    logger.info('FileChunkingService initialized');\n  }\n\n  /**\n     * Chunk a file and store in memory\n     * @param filePath Path to the file to chunk\n     * @returns Array of memory entries created from the file\n     */\n  public static async chunkFile(filePath: string): Promise<MemoryEntry[]> {\n    if (!this.instance || !this.memoryStorer) {\n      throw new Error('FileChunkingService not initialized');\n    }\n\n    // Create a local file data source\n    const fileSource = new LocalFileDataSource(filePath);\n\n    // Process the file\n    const result = await this.instance.processDataSource(fileSource);\n\n    if (result.status === 'error') {\n      throw new Error(`Failed to chunk file: ${result.message}`);\n    }\n\n    return result.addedEntries;\n  }\n\n  /**\n     * Chunk a workspace folder and store in memory\n     * @param folderPath Path to the folder to chunk\n     * @param includePatterns Glob patterns to include\n     * @param excludePatterns Glob patterns to exclude\n     * @returns Array of memory entries created from the workspace\n     */\n  public static async chunkWorkspace(\n    folderPath: string,\n    includePatterns: string[] = [\n      // === Programming Languages ===\n      // JavaScript/TypeScript ecosystem\n      '**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,svelte,astro}',\n      // Python ecosystem\n      '**/*.{py,pyx,pyi,pyw,py3,ipynb,pth}',\n      // Java ecosystem\n      '**/*.{java,kt,kts,scala,sc,groovy,gradle,kts}',\n      // C/C++ ecosystem\n      '**/*.{c,cc,cpp,cxx,c++,h,hh,hpp,hxx,h++,inc,inl}',\n      // C# ecosystem\n      '**/*.{cs,csx,vb,vbx,fs,fsx,fsi}',\n      // Web technologies\n      '**/*.{html,htm,xhtml,xml,xsl,xslt,svg,mathml}',\n      '**/*.{css,scss,sass,less,styl,stylus,pcss,postcss}',\n      // Mobile development\n      '**/*.{swift,m,mm,dart,flutter}',\n      '**/*.{java,kt,xml}', // Android\n      // Other popular languages\n      '**/*.{go,rs,php,rb,pl,pm,lua,r,R,jl,elm,hs,lhs,ml,mli,ocaml,clj,cljs,cljc,edn}',\n      '**/*.{erl,hrl,ex,exs,elixir,nim,cr,zig,odin,v,vlang}',\n\n      // === Configuration & Data Files ===\n      // Package managers & build tools\n      '**/*.{json,json5,jsonc,yaml,yml,toml,ini,cfg,conf,config,properties}',\n      '**/package.json', '**/package-lock.json', '**/yarn.lock', '**/pnpm-lock.yaml',\n      '**/Cargo.toml', '**/Cargo.lock', '**/go.mod', '**/go.sum',\n      '**/requirements.txt', '**/Pipfile', '**/Pipfile.lock', '**/pyproject.toml',\n      '**/composer.json', '**/composer.lock', '**/Gemfile', '**/Gemfile.lock',\n      '**/build.gradle', '**/pom.xml', '**/CMakeLists.txt', '**/Makefile',\n      // Environment & deployment\n      '**/.env*', '**/docker-compose*.{yml,yaml}', '**/Dockerfile*', '**/Containerfile*',\n      '**/k8s*.{yml,yaml}', '**/kustomization.{yml,yaml}',\n      // CI/CD\n      '**/.github/**/*.{yml,yaml}', '**/.gitlab-ci.yml', '**/azure-pipelines.yml',\n      '**/Jenkinsfile', '**/bitbucket-pipelines.yml', '**/circle.yml', '**/.circleci/**/*.yml',\n\n      // === Documentation & Markup ===\n      '**/*.{md,mdx,markdown,rst,txt,rtf,adoc,asciidoc,org,tex,latex}',\n      '**/*.{wiki,textile,creole,pod,rdoc}',\n\n      // === Database & Query Languages ===\n      '**/*.{sql,mysql,pgsql,sqlite,nosql,cql,cypher,sparql,graphql,gql}',\n      '**/*.{hql,pig,hive}', // Big data\n\n      // === Shell & Scripting ===\n      '**/*.{sh,bash,zsh,fish,csh,tcsh,ksh,dash}',\n      '**/*.{bat,cmd,ps1,psm1,psd1}', // Windows\n      '**/*.{awk,sed,perl,tcl,expect}',\n\n      // === Microsoft Technologies ===\n      '**/*.{xaml,xml,resx,settings,config,manifest,targets,props}',\n      '**/*.{aspx,ascx,master,ashx,asmx,svc,cshtml,vbhtml,razor}',\n      '**/*.{csproj,vbproj,fsproj,vcxproj,vcproj,sln,slnx}',\n\n      // === Apple Technologies ===\n      '**/*.{plist,strings,storyboard,xib,xcconfig,xcscheme,xcworkspacedata}',\n      '**/*.{pbxproj,xcodeproj,xcworkspace}/**/*',\n\n      // === Game Development ===\n      '**/*.{unity,prefab,scene,asset,meta,shader,hlsl,glsl,cg,fx}',\n      '**/*.{ue4,uasset,umap,blueprint}',\n      '**/*.{gdscript,gd,tres,tscn,godot}',\n\n      // === Data Science & Analytics ===\n      '**/*.{ipynb,rmd,qmd,jmd}', // Notebooks\n      '**/*.{sas,spss,stata,dta,sav}',\n      '**/*.{weka,arff,libsvm}',\n\n      // === Infrastructure as Code ===\n      '**/*.{tf,tfvars,hcl}', // Terraform\n      '**/*.{bicep,arm}', // Azure\n      '**/*.{cfn,template}', // CloudFormation\n      '**/serverless.{yml,yaml}', // Serverless\n\n      // === API & Protocol Definitions ===\n      '**/*.{proto,avro,thrift,swagger,openapi}',\n      '**/*.{wsdl,xsd,dtd,rng,rnc}',\n\n      // === Specialized Formats ===\n      '**/*.{log,trace,dump}', // Logs and debugging\n      '**/*.{patch,diff}', // Version control\n      '**/*.{license,copyright,notice,authors,contributors,changelog,news}',\n      '**/{README,CHANGELOG,LICENSE,NOTICE,AUTHORS,CONTRIBUTORS,INSTALL,TODO,FIXME}*',\n\n      // === Legacy & Specialized Languages ===\n      '**/*.{cobol,cob,cbl,fortran,f90,f95,ada,pas,dpr,asm,s,nasm,masm}',\n      '**/*.{vhdl,vhd,verilog,v,sv,systemverilog}', // Hardware description\n      '**/*.{matlab,m,octave}', // Scientific computing\n\n      // === Template & Generator Files ===\n      '**/*.{mustache,handlebars,hbs,twig,jinja,j2,erb,ejs,pug,jade}',\n      '**/*.{liquid,smarty,velocity,freemarker,thymeleaf}',\n\n      // === Catch remaining text files ===\n      '**/*.txt', '**/*README*', '**/*CHANGELOG*', '**/*LICENSE*'\n    ],\n    excludePatterns: string[] = [\n      // Build outputs and dependencies\n      '**/node_modules/**', '**/dist/**', '**/build/**', '**/out/**', '**/target/**',\n      '**/bin/**', '**/obj/**', '**/.next/**', '**/.nuxt/**', '**/coverage/**',\n      // Version control\n      '**/.git/**', '**/.svn/**', '**/.hg/**', '**/.bzr/**',\n      // IDE and editor files\n      '**/.vscode/**', '**/.idea/**', '**/.*project', '**/*.swp', '**/*.swo', '**/*~',\n      // OS files\n      '**/.DS_Store', '**/Thumbs.db', '**/desktop.ini',\n      // Temporary and cache files\n      '**/tmp/**', '**/temp/**', '**/.cache/**', '**/.temp/**', '**/logs/**',\n      // Package manager caches\n      '**/.npm/**', '**/.yarn/**', '**/.pnpm/**', '**/bower_components/**',\n      // Language-specific build artifacts\n      '**/__pycache__/**', '**/*.pyc', '**/*.pyo', '**/*.pyd',\n      '**/vendor/**', // PHP/Go vendor directories\n      '**/.gradle/**', '**/gradle/**',\n      // Large binary or generated files\n      '**/*.min.js', '**/*.min.css', '**/*.bundle.js', '**/*.chunk.js',\n      // Database files\n      '**/*.db', '**/*.sqlite', '**/*.sqlite3',\n      // Media files (usually not useful for code analysis)\n      '**/*.{jpg,jpeg,png,gif,bmp,ico,svg,webp,tiff,tif}',\n      '**/*.{mp3,mp4,avi,mov,wmv,flv,webm,ogg,wav,flac}',\n      '**/*.{pdf,doc,docx,xls,xlsx,ppt,pptx}',\n      // Archive files\n      '**/*.{zip,tar,gz,bz2,xz,7z,rar,dmg,iso}'\n    ]\n  ): Promise<MemoryEntry[]> {\n    if (!this.instance || !this.memoryStorer) {\n      throw new Error('FileChunkingService not initialized');\n    }\n\n    // Find all files in the workspace that match the patterns\n\n    const allFiles: string[] = [];\n\n    for (const pattern of includePatterns) {\n      const files = await glob(pattern, {\n        cwd: folderPath,\n        ignore: excludePatterns,\n        absolute: true\n      });\n      allFiles.push(...files);\n    }\n\n    logger.info(`Found ${allFiles.length} files in workspace ${folderPath}`);\n\n    if (allFiles.length === 0) {\n      return [];\n    }\n\n    // Create data sources for each file\n    const dataSources = allFiles.map(file => new LocalFileDataSource(file));\n\n    // Process all files\n    const batchResult = await this.instance.processBatch(dataSources);\n\n    if (batchResult.failedSources > 0) {\n      logger.warn(`Failed to chunk ${batchResult.failedSources} files in workspace ${folderPath}`);\n    }\n\n    // Flatten all added entries\n    const allEntries: MemoryEntry[] = [];\n    for (const result of batchResult.results) {\n      allEntries.push(...result.addedEntries);\n    }\n\n    return allEntries;\n  }\n}\n\n/**\n * LocalFileDataSource - A data source for local files\n */\nclass LocalFileDataSource implements IDataSource {\n  private filePath: string;\n\n  constructor(filePath: string) {\n    this.filePath = filePath;\n  }\n\n  getUri(): string {\n    return `file://${this.filePath}`;\n  }\n\n  async getMetadata(): Promise<DataSourceMetadata> {\n    const stat = await fs.promises.stat(this.filePath);\n\n    return {\n      uri: this.getUri(),\n      sourceType: 'local_file',\n      size: stat.size,\n      lastModified: stat.mtime,\n      fileName: path.basename(this.filePath),\n      mimeType: this.getMimeType(this.filePath)\n    };\n  }\n\n  async getContentStream(): Promise<Readable> {\n    // Add minimal async operation to justify async\n    await Promise.resolve();\n    return fs.createReadStream(this.filePath);\n  }\n\n  private getMimeType(filePath: string): string {\n    const ext = path.extname(filePath).toLowerCase();\n\n    // Comprehensive MIME type mapping for all supported file types\n    const mimeTypes: Record<string, string> = {\n      // === Text and Markup ===\n      '.txt': 'text/plain',\n      '.md': 'text/markdown',\n      '.markdown': 'text/markdown',\n      '.mdx': 'text/markdown',\n      '.rst': 'text/x-rst',\n      '.rtf': 'application/rtf',\n      '.adoc': 'text/asciidoc',\n      '.asciidoc': 'text/asciidoc',\n      '.org': 'text/org',\n      '.tex': 'text/x-tex',\n      '.latex': 'text/x-latex',\n      '.wiki': 'text/x-wiki',\n      '.textile': 'text/x-textile',\n      '.creole': 'text/x-creole',\n      '.pod': 'text/x-pod',\n      '.rdoc': 'text/x-rdoc',\n\n      // === Web Technologies ===\n      '.html': 'text/html',\n      '.htm': 'text/html',\n      '.xhtml': 'application/xhtml+xml',\n      '.xml': 'application/xml',\n      '.xsl': 'application/xslt+xml',\n      '.xslt': 'application/xslt+xml',\n      '.svg': 'image/svg+xml',\n      '.mathml': 'application/mathml+xml',\n      '.css': 'text/css',\n      '.scss': 'text/x-scss',\n      '.sass': 'text/x-sass',\n      '.less': 'text/x-less',\n      '.styl': 'text/x-stylus',\n      '.stylus': 'text/x-stylus',\n      '.pcss': 'text/x-postcss',\n      '.postcss': 'text/x-postcss',\n\n      // === JavaScript/TypeScript Ecosystem ===\n      '.js': 'text/javascript',\n      '.mjs': 'text/javascript',\n      '.cjs': 'text/javascript',\n      '.jsx': 'text/jsx',\n      '.ts': 'text/typescript',\n      '.mts': 'text/typescript',\n      '.cts': 'text/typescript',\n      '.tsx': 'text/tsx',\n      '.vue': 'text/x-vue',\n      '.svelte': 'text/x-svelte',\n      '.astro': 'text/x-astro',\n\n      // === Python Ecosystem ===\n      '.py': 'text/x-python',\n      '.pyx': 'text/x-cython',\n      '.pyi': 'text/x-python',\n      '.pyw': 'text/x-python',\n      '.py3': 'text/x-python',\n      '.ipynb': 'application/x-ipynb+json',\n      '.pth': 'text/plain',\n\n      // === Java Ecosystem ===\n      '.java': 'text/x-java-source',\n      '.kt': 'text/x-kotlin',\n      '.kts': 'text/x-kotlin',\n      '.scala': 'text/x-scala',\n      '.sc': 'text/x-scala',\n      '.groovy': 'text/x-groovy',\n      '.gradle': 'text/x-gradle',\n\n      // === C/C++ Ecosystem ===\n      '.c': 'text/x-c',\n      '.cc': 'text/x-c++',\n      '.cpp': 'text/x-c++',\n      '.cxx': 'text/x-c++',\n      '.c++': 'text/x-c++',\n      '.h': 'text/x-c',\n      '.hh': 'text/x-c++',\n      '.hpp': 'text/x-c++',\n      '.hxx': 'text/x-c++',\n      '.h++': 'text/x-c++',\n      '.inc': 'text/x-c',\n      '.inl': 'text/x-c++',\n\n      // === C# and .NET ===\n      '.cs': 'text/x-csharp',\n      '.csx': 'text/x-csharp',\n      '.vb': 'text/x-vb',\n      '.vbx': 'text/x-vb',\n      '.fs': 'text/x-fsharp',\n      '.fsx': 'text/x-fsharp',\n      '.fsi': 'text/x-fsharp',\n      '.xaml': 'application/xaml+xml',\n      '.resx': 'application/x-resx',\n      '.config': 'application/xml',\n      '.manifest': 'application/xml',\n      '.targets': 'application/xml',\n      '.props': 'application/xml',\n      '.csproj': 'application/xml',\n      '.vbproj': 'application/xml',\n      '.fsproj': 'application/xml',\n      '.vcxproj': 'application/xml',\n      '.vcproj': 'application/xml',\n      '.sln': 'text/plain',\n      '.slnx': 'application/xml',\n\n      // === Mobile Development ===\n      '.swift': 'text/x-swift',\n      '.m': 'text/x-objectivec',\n      '.mm': 'text/x-objectivec++',\n      '.dart': 'text/x-dart',\n      '.flutter': 'text/x-dart',\n\n      // === Other Popular Languages ===\n      '.go': 'text/x-go',\n      '.rs': 'text/x-rust',\n      '.php': 'text/x-php',\n      '.rb': 'text/x-ruby',\n      '.pl': 'text/x-perl',\n      '.pm': 'text/x-perl',\n      '.lua': 'text/x-lua',\n      '.r': 'text/x-r',\n      '.R': 'text/x-r',\n      '.jl': 'text/x-julia',\n      '.elm': 'text/x-elm',\n      '.hs': 'text/x-haskell',\n      '.lhs': 'text/x-literate-haskell',\n      '.ml': 'text/x-ocaml',\n      '.mli': 'text/x-ocaml',\n      '.ocaml': 'text/x-ocaml',\n      '.clj': 'text/x-clojure',\n      '.cljs': 'text/x-clojure',\n      '.cljc': 'text/x-clojure',\n      '.edn': 'application/edn',\n      '.erl': 'text/x-erlang',\n      '.hrl': 'text/x-erlang',\n      '.ex': 'text/x-elixir',\n      '.exs': 'text/x-elixir',\n      '.elixir': 'text/x-elixir',\n      '.nim': 'text/x-nim',\n      '.cr': 'text/x-crystal',\n      '.zig': 'text/x-zig',\n      '.odin': 'text/x-odin',\n      '.v': 'text/x-vlang',\n      '.vlang': 'text/x-vlang',\n\n      // === Configuration and Data ===\n      '.json': 'application/json',\n      '.json5': 'application/json5',\n      '.jsonc': 'application/json',\n      '.yaml': 'text/yaml',\n      '.yml': 'text/yaml',\n      '.toml': 'application/toml',\n      '.ini': 'text/plain',\n      '.cfg': 'text/plain',\n      '.conf': 'text/plain',\n      '.properties': 'text/x-java-properties',\n\n      // === Shell and Scripting ===\n      '.sh': 'text/x-shellscript',\n      '.bash': 'text/x-shellscript',\n      '.zsh': 'text/x-shellscript',\n      '.fish': 'text/x-fish',\n      '.csh': 'text/x-csh',\n      '.tcsh': 'text/x-tcsh',\n      '.ksh': 'text/x-ksh',\n      '.dash': 'text/x-shellscript',\n      '.bat': 'text/x-msdos-batch',\n      '.cmd': 'text/x-msdos-batch',\n      '.ps1': 'text/x-powershell',\n      '.psm1': 'text/x-powershell',\n      '.psd1': 'text/x-powershell',\n      '.awk': 'text/x-awk',\n      '.sed': 'text/x-sed',\n      '.perl': 'text/x-perl',\n      '.tcl': 'text/x-tcl',\n      '.expect': 'text/x-expect',\n\n      // === Database and Query Languages ===\n      '.sql': 'text/x-sql',\n      '.mysql': 'text/x-mysql',\n      '.pgsql': 'text/x-pgsql',\n      '.sqlite': 'text/x-sqlite',\n      '.nosql': 'text/x-nosql',\n      '.cql': 'text/x-cassandra',\n      '.cypher': 'text/x-cypher',\n      '.sparql': 'text/x-sparql',\n      '.graphql': 'application/graphql',\n      '.gql': 'application/graphql',\n      '.hql': 'text/x-hive',\n      '.pig': 'text/x-pig',\n      '.hive': 'text/x-hive',\n\n      // === Apple Technologies ===\n      '.plist': 'application/x-plist',\n      '.strings': 'text/plain',\n      '.storyboard': 'application/xml',\n      '.xib': 'application/xml',\n      '.xcconfig': 'text/plain',\n      '.xcscheme': 'application/xml',\n      '.xcworkspacedata': 'application/xml',\n      '.pbxproj': 'text/plain',\n\n      // === Game Development ===\n      '.unity': 'text/x-unity',\n      '.prefab': 'text/yaml',\n      '.scene': 'text/yaml',\n      '.asset': 'text/yaml',\n      '.meta': 'text/yaml',\n      '.shader': 'text/x-glsl',\n      '.hlsl': 'text/x-hlsl',\n      '.glsl': 'text/x-glsl',\n      '.cg': 'text/x-cg',\n      '.fx': 'text/x-fx',\n      '.gdscript': 'text/x-gdscript',\n      '.gd': 'text/x-gdscript',\n      '.tres': 'text/plain',\n      '.tscn': 'text/plain',\n\n      // === Infrastructure as Code ===\n      '.tf': 'text/x-terraform',\n      '.tfvars': 'text/x-terraform',\n      '.hcl': 'text/x-hcl',\n      '.bicep': 'text/x-bicep',\n      '.arm': 'application/json',\n\n      // === API and Protocol Definitions ===\n      '.proto': 'text/x-protobuf',\n      '.avro': 'application/json',\n      '.thrift': 'text/x-thrift',\n      '.swagger': 'application/json',\n      '.openapi': 'application/json',\n      '.wsdl': 'application/xml',\n      '.xsd': 'application/xml',\n      '.dtd': 'application/xml-dtd',\n      '.rng': 'application/xml',\n      '.rnc': 'text/x-rnc',\n\n      // === Template and Generator Files ===\n      '.mustache': 'text/x-mustache',\n      '.handlebars': 'text/x-handlebars',\n      '.hbs': 'text/x-handlebars',\n      '.twig': 'text/x-twig',\n      '.jinja': 'text/x-jinja',\n      '.j2': 'text/x-jinja',\n      '.erb': 'text/x-ruby-template',\n      '.ejs': 'text/x-ejs',\n      '.pug': 'text/x-pug',\n      '.jade': 'text/x-jade',\n      '.liquid': 'text/x-liquid',\n      '.smarty': 'text/x-smarty',\n      '.velocity': 'text/x-velocity',\n      '.freemarker': 'text/x-freemarker',\n      '.thymeleaf': 'text/x-thymeleaf',\n\n      // === Legacy and Specialized Languages ===\n      '.cobol': 'text/x-cobol',\n      '.cob': 'text/x-cobol',\n      '.cbl': 'text/x-cobol',\n      '.fortran': 'text/x-fortran',\n      '.f90': 'text/x-fortran',\n      '.f95': 'text/x-fortran',\n      '.ada': 'text/x-ada',\n      '.pas': 'text/x-pascal',\n      '.dpr': 'text/x-pascal',\n      '.asm': 'text/x-asm',\n      '.s': 'text/x-asm',\n      '.nasm': 'text/x-nasm',\n      '.masm': 'text/x-masm',\n      '.vhdl': 'text/x-vhdl',\n      '.vhd': 'text/x-vhdl',\n      '.verilog': 'text/x-verilog',\n      '.sv': 'text/x-systemverilog',\n      '.systemverilog': 'text/x-systemverilog',\n      '.matlab': 'text/x-matlab',\n      '.octave': 'text/x-octave',\n\n      // === Data Science and Analytics ===\n      '.rmd': 'text/x-r-markdown',\n      '.qmd': 'text/x-quarto',\n      '.jmd': 'text/x-julia-markdown',\n      '.sas': 'text/x-sas',\n      '.spss': 'text/x-spss',\n      '.stata': 'text/x-stata',\n      '.dta': 'application/x-stata',\n      '.sav': 'application/x-spss',\n      '.weka': 'text/x-weka',\n      '.arff': 'text/x-arff',\n      '.libsvm': 'text/x-libsvm',\n\n      // === Specialized Formats ===\n      '.log': 'text/plain',\n      '.trace': 'text/plain',\n      '.dump': 'text/plain',\n      '.patch': 'text/x-patch',\n      '.diff': 'text/x-diff',\n      '.license': 'text/plain',\n      '.copyright': 'text/plain',\n      '.notice': 'text/plain',\n      '.authors': 'text/plain',\n      '.contributors': 'text/plain',\n      '.changelog': 'text/plain',\n      '.news': 'text/plain',\n\n      // === Microsoft Office and Documents ===\n      '.doc': 'application/msword',\n      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n      '.xls': 'application/vnd.ms-excel',\n      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n      '.ppt': 'application/vnd.ms-powerpoint',\n      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n      '.pdf': 'application/pdf',\n\n      // === Media Files (for completeness) ===\n      '.png': 'image/png',\n      '.jpg': 'image/jpeg',\n      '.jpeg': 'image/jpeg',\n      '.gif': 'image/gif',\n      '.bmp': 'image/bmp',\n      '.ico': 'image/x-icon',\n      '.webp': 'image/webp',\n      '.tiff': 'image/tiff',\n      '.tif': 'image/tiff',\n      '.mp3': 'audio/mpeg',\n      '.mp4': 'video/mp4',\n      '.avi': 'video/x-msvideo',\n      '.mov': 'video/quicktime',\n      '.wmv': 'video/x-ms-wmv',\n      '.flv': 'video/x-flv',\n      '.webm': 'video/webm',\n      '.ogg': 'audio/ogg',\n      '.wav': 'audio/wav',\n      '.flac': 'audio/flac'\n    };\n\n    return mimeTypes[ext] ?? 'application/octet-stream';\n  }\n}"]}