{"version": 3, "file": "terminalSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/terminalSettingsSection.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAgBpB,QAAA,eAAe,GAAG;IAC7B,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,EAAE,EAAE;IACvC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;IACpC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;IACpC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE;IAClC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;IACpC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;IACnC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;IACrC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;IACnC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;IACtC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;IACnC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;IACvC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE;IACzC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE;IACxC,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE;IAC1C,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE;IACxC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,EAAE;IAC3C,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE;IACzC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE;IACxC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;CACxC,CAAC;AAEW,QAAA,cAAc,GAAG;IAC5B,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,OAAO;IACP,QAAQ;IACR,aAAa;IACb,iBAAiB;IACjB,aAAa;IACb,eAAe;IACf,MAAM;IACN,SAAS;IACT,WAAW;IACX,kBAAkB;IAClB,iBAAiB;IACjB,SAAS;IACT,YAAY;IACZ,eAAe;CAChB,CAAC;AAEF,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,kCAAiB,CAAA;IACjB,gCAAe,CAAA;IACf,8BAAa,CAAA;IACb,8CAA6B,CAAA;AAC/B,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAED,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,yCAAqB,CAAA;IACrB,uDAAmC,CAAA;IACnC,iCAAa,CAAA;AACf,CAAC,EAJW,gBAAgB,gCAAhB,gBAAgB,QAI3B;AA+DD,MAAa,uBAAuB;IACjB,WAAW,GAAG,kBAAkB,CAAC;IAC1C,oBAAoB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;IACtD,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAE9C,eAAe,GAAqB;QACnD,mBAAmB;QACnB,QAAQ,EAAE,gBAAgB,CAAC,IAAI;QAC/B,YAAY,EAAE,IAAI;QAElB,gBAAgB;QAChB,UAAU,EACR,qHAAqH;QACvH,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,QAAQ;QAEpB,iBAAiB;QACjB,KAAK,EAAE,aAAa,CAAC,MAAM;QAC3B,eAAe,EAAE,EAAE;QACnB,eAAe,EAAE,EAAE;QACnB,WAAW,EAAE,EAAE;QACf,cAAc,EAAE,EAAE;QAClB,WAAW,EAAE,EAAE;KAChB,CAAC;IAEM,WAAW,GAAwB,EAAE,CAAC;IAE9C;QACE,mCAAmC;QACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE;YAC9C,IAAI,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;YACnC,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,OAAO;QACL,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;QACpC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEM,WAAW;QAChB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;QAEtC,6CAA6C;QAC7C,MAAM,MAAM,GAAqB;YAC/B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAmB,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC;YACrE,YAAY,EAAE,MAAM,CAAC,GAAG,CAAU,cAAc,EAAE,QAAQ,CAAC,YAAY,CAAC;YACxE,UAAU,EAAE,MAAM,CAAC,GAAG,CAAS,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC;YACjE,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAS,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC;YAC3D,UAAU,EAAE,MAAM,CAAC,GAAG,CAAa,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC;YACrE,KAAK,EAAE,MAAM,CAAC,GAAG,CAAgB,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC;YACzD,eAAe,EAAE,MAAM,CAAC,GAAG,CACzB,iBAAiB,EACjB,QAAQ,CAAC,eAAe,CACzB;YACD,eAAe,EAAE,MAAM,CAAC,GAAG,CACzB,iBAAiB,EACjB,QAAQ,CAAC,eAAe,CACzB;YACD,WAAW,EAAE,MAAM,CAAC,GAAG,CAAS,aAAa,EAAE,QAAQ,CAAC,WAAW,CAAC;YACpE,cAAc,EAAE,MAAM,CAAC,GAAG,CACxB,gBAAgB,EAChB,QAAQ,CAAC,cAAc,CACxB;YACD,WAAW,EACT,MAAM,CAAC,GAAG,CAAqB,aAAa,EAAE,QAAQ,CAAC,WAAW,CAAC;gBACnE,EAAE;SACL,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,UAAU,CACf,GAAM;QAEN,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAwB,CAAC;IAC3E,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,QAAmC;QAEnC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEnE,mCAAmC;QACnC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,aAAa,CACxB,GAAM,EACN,KAA0B,EAC1B,SAAqC,MAAM,CAAC,mBAAmB,CAAC,MAAM;QAEtE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,GAAM;QAEN,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YACpD,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,YAA8B,CAAC;QAEnC,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,gBAAgB,CAAC,QAAQ;gBAC5B,YAAY,GAAG,gBAAgB,CAAC,eAAe,CAAC;gBAChD,MAAM;YACR,KAAK,gBAAgB,CAAC,eAAe;gBACnC,YAAY,GAAG,gBAAgB,CAAC,IAAI,CAAC;gBACrC,MAAM;YACR,KAAK,gBAAgB,CAAC,IAAI,CAAC;YAC3B;gBACE,YAAY,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;IACxD,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,YAAoB;QAEpB,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,KAAK,EAAE,YAAY,IAAI,SAAS;YAChC,MAAM,EAAE,qDAAqD;YAC7D,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACvB,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtD,OAAO,wDAAwD,CAAC;gBAClE,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC,CAAC;QAEH,OAAO,KAAK,IAAI,SAAS,CAAC;IAC5B,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,WAAmB;QAEnB,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;QAClD,SAAS,CAAC,WAAW,GAAG,iCAAiC,CAAC;QAC1D,SAAS,CAAC,KAAK,GAAG,sBAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC9C,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;YACvD,UAAU,EAAE,IAAI,KAAK,WAAW;SACjC,CAAC,CAAC,CAAC;QAEJ,SAAS,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,EAAE;YACnC,IAAI,KAAK,IAAI,CAAC,sBAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7C,SAAS,CAAC,KAAK,GAAG;oBAChB,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE;oBAC5C,GAAG,sBAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;wBAC/B,KAAK,EAAE,IAAI;wBACX,WAAW,EAAE,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;qBACxD,CAAC,CAAC;iBACJ,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,KAAK,GAAG,sBAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBAC9C,KAAK,EAAE,IAAI;oBACX,WAAW,EAAE,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;iBACxD,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,OAAO,CAChC,CAAC,OAAO,EAAE,EAAE;YACV,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE;gBACzB,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,SAAS,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YACH,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;YAC9C,SAAS,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CACF,CAAC;QAEF,SAAS,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,QAAQ,EAAE,KAAK,CAAC;IACzB,CAAC;IAEM,qBAAqB;QAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAClD,MAAM,SAAS,GACb,UAAU,CAAC,IAAI,KAAK,CAAC;YACnB,CAAC,CAAC,OAAO;YACT,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;gBACrB,CAAC,CAAC,MAAM;gBACR,CAAC,CAAC,cAAc,CAAC;QAEvB,wDAAwD;QACxD,OAAO;YACL,UAAU,EAAE,mCAAmC;YAC/C,UAAU,EAAE,mCAAmC;YAC/C,MAAM,EAAE,yCAAyC;YACjD,SAAS,EAAE,4CAA4C;YACvD,KAAK,EAAE,SAAS;SACjB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,YAAY;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEjD,MAAM,aAAa,GAA2B;YAC5C;gBACE,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE,kCAAkC;aAChD;YACD,EAAE,KAAK,EAAE,kBAAkB,EAAE,WAAW,EAAE,4BAA4B,EAAE;YACxE;gBACE,KAAK,EAAE,sBAAsB;gBAC7B,WAAW,EAAE,+BAA+B;aAC7C;SACF,CAAC;QAEF,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE;YACxE,WAAW,EAAE,4BAA4B;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB;YAAE,OAAO;QAE9B,IAAI,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAC3D,CAAC;aAAM,IAAI,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;aAAM,IAAI,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,QAA0B,EAC1B,WAAgB;QAEhB,MAAM,KAAK,GAA2B;YACpC;gBACE,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,QAAQ,CAAC,UAAU;gBAChC,MAAM,EAAE,iCAAiC;gBACzC,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,KAAK,EAAE,WAAW;gBAClB,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACzC,MAAM,EAAE,+BAA+B;gBACvC,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,QAAQ,CAAC,UAAU;gBAChC,MAAM,EAAE,iCAAiC;gBACzC,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,KAAK,EAAE,YAAY;gBACnB,WAAW,EAAE,QAAQ,CAAC,eAAe,IAAI,eAAe;gBACxD,MAAM,EAAE,4CAA4C,WAAW,CAAC,UAAU,GAAG;gBAC7E,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,QAAQ,CAAC,eAAe,IAAI,eAAe;gBACxD,MAAM,EAAE,kDAAkD,WAAW,CAAC,UAAU,GAAG;gBACnF,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,KAAK,EAAE,cAAc;gBACrB,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,eAAe;gBACpD,MAAM,EAAE,8CAA8C,WAAW,CAAC,MAAM,GAAG;gBAC3E,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,KAAK,EAAE,aAAa;gBACpB,WAAW,EACT,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBAClE,MAAM,EAAE,iCAAiC;gBACzC,UAAU,EAAE,IAAI;aACjB;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;YACxD,WAAW,EAAE,4BAA4B;YACzC,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,QAAQ,QAAQ,CAAC,KAAK,EAAE,CAAC;YACvB,KAAK,aAAa;gBAChB,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;gBAC5C,MAAM;YACR,KAAK,WAAW;gBACd,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;gBAC1C,MAAM;YACR,KAAK,aAAa;gBAChB,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;gBAC5C,MAAM;YACR,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBAChD,MAAM;YACR,KAAK,kBAAkB;gBACrB,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBAChD,MAAM;YACR,KAAK,cAAc;gBACjB,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBAC5C,MAAM;YACR,KAAK,aAAa;gBAChB,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBACvC,MAAM;QACV,CAAC;QAED,gDAAgD;QAChD,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,WAAW,CAAC,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,QAA0B;QAE1B,MAAM,KAAK,GAA2B;YACpC;gBACE,KAAK,EAAE,WAAW;gBAClB,WAAW,EACT,QAAQ,CAAC,QAAQ,KAAK,UAAU;oBAC9B,CAAC,CAAC,WAAW;oBACb,CAAC,CAAC,QAAQ,CAAC,QAAQ,KAAK,iBAAiB;wBACvC,CAAC,CAAC,kBAAkB;wBACpB,CAAC,CAAC,MAAM;gBACd,MAAM,EAAE,+CAA+C;gBACvD,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;gBACjD,MAAM,EAAE,4BAA4B;gBACpC,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,KAAK,EAAE,mBAAmB;gBAC1B,WAAW,EAAE,4CAA4C;gBACzD,MAAM,EAAE,uCAAuC;gBAC/C,UAAU,EAAE,IAAI;aACjB;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;YACxD,WAAW,EAAE,sCAAsC;YACnD,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,QAAQ,QAAQ,CAAC,KAAK,EAAE,CAAC;YACvB,KAAK,WAAW;gBACd,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC5B,MAAM;YACR,KAAK,eAAe;gBAClB,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBACjE,MAAM;YACR,KAAK,mBAAmB;gBACtB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC7B,MAAM;QACV,CAAC;QAED,gDAAgD;QAChD,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,+EAA+E,EAC/E,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,WAAW,EACX,QAAQ,CACT,CAAC;QAEF,IAAI,OAAO,KAAK,WAAW,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,+CAA+C,CAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,QAA0B;QAE1B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,QAA0B;QAE1B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC/C,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACnC,MAAM,EAAE,wBAAwB;YAChC,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACvB,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBACjC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;oBACzC,OAAO,wCAAwC,CAAC;gBAClD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,QAA0B;QAE1B,MAAM,OAAO,GAAiB;YAC5B,QAAQ;YACR,MAAM;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;SACN,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAChD,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACvB,KAAK,EAAE,MAAM;YACb,WAAW,EAAE,MAAM,KAAK,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;SAC7D,CAAC,CAAC,EACH,EAAE,WAAW,EAAE,sBAAsB,EAAE,CACxC,CAAC;QAEF,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,QAAQ,CAAC,KAAmB,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,QAAgC;QAEhC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAW,CAAC;QAC5D,MAAM,UAAU,GAAG,uBAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACjD,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,WAAW,EAAE,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;YAC1D,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,mBAAmB;YACnE,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE;YAC7D,WAAW,EAAE,YAAY,QAAQ;iBAC9B,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC;iBAC1B,WAAW,EAAE;iBACb,IAAI,EAAE,EAAE;YACX,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YAChC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAC7D,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,KAAK,EAAE,EAAE,CAAC;YACjC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAA0B;QACxD,MAAM,MAAM,GAAG;YACb,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC,MAAM,EAAE;YAChD,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE;YAC9C,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,CAAC,IAAI,EAAE;YAC5C,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,aAAa,CAAC,YAAY,EAAE;SAC9D,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAChD,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACrB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,WAAW,EAAE,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;YAC5D,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC,CAAC,EACH,EAAE,WAAW,EAAE,sBAAsB,EAAE,CACxC,CAAC;QAEF,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;CACF;AAtgBD,0DAsgBC;AAED,8BAA8B;AACjB,QAAA,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\n\n// Types for terminal settings\nexport type FontWeight =\n  | 'normal'\n  | 'bold'\n  | '100'\n  | '200'\n  | '300'\n  | '400'\n  | '500'\n  | '600'\n  | '700'\n  | '800'\n  | '900';\n\nexport const STANDARD_COLORS = [\n  { label: 'Default (Theme)', value: '' },\n  { label: 'Black', value: '#000000' },\n  { label: 'White', value: '#FFFFFF' },\n  { label: 'Red', value: '#FF0000' },\n  { label: 'Green', value: '#00FF00' },\n  { label: 'Blue', value: '#0000FF' },\n  { label: 'Yellow', value: '#FFFF00' },\n  { label: 'Cyan', value: '#00FFFF' },\n  { label: 'Magenta', value: '#FF00FF' },\n  { label: 'Gray', value: '#808080' },\n  { label: 'Dark Red', value: '#800000' },\n  { label: 'Dark Green', value: '#008000' },\n  { label: 'Dark Blue', value: '#000080' },\n  { label: 'Dark Yellow', value: '#808000' },\n  { label: 'Dark Cyan', value: '#008080' },\n  { label: 'Dark Magenta', value: '#800080' },\n  { label: 'Light Gray', value: '#C0C0C0' },\n  { label: 'Dark Gray', value: '#404040' },\n  { label: 'Custom...', value: 'custom' },\n];\n\nexport const STANDARD_FONTS = [\n  'Consolas',\n  'Courier New',\n  'Droid Sans Mono',\n  'Fira Code',\n  'Inconsolata',\n  'JetBrains Mono',\n  'Menlo',\n  'Monaco',\n  'Roboto Mono',\n  'Source Code Pro',\n  'Ubuntu Mono',\n  'Cascadia Code',\n  'Hack',\n  'Iosevka',\n  'Fira Mono',\n  'DejaVu Sans Mono',\n  'Liberation Mono',\n  'PT Mono',\n  'Space Mono',\n  'Anonymous Pro',\n];\n\nexport enum TerminalTheme {\n  System = 'system',\n  Light = 'light',\n  Dark = 'dark',\n  HighContrast = 'highContrast',\n}\n\nexport enum TerminalViewType {\n  SideView = 'sideView',\n  DefaultTerminal = 'defaultTerminal',\n  Both = 'both',\n}\n\nexport interface TerminalColorTheme {\n  foreground?: string;\n  background?: string;\n  cursor?: string;\n  selection?: string;\n  black?: string;\n  red?: string;\n  green?: string;\n  yellow?: string;\n  blue?: string;\n  magenta?: string;\n  cyan?: string;\n  white?: string;\n  brightBlack?: string;\n  brightRed?: string;\n  brightGreen?: string;\n  brightYellow?: string;\n  brightBlue?: string;\n  brightMagenta?: string;\n  brightCyan?: string;\n  brightWhite?: string;\n}\n\nexport interface TerminalSettings {\n  // Display settings\n  viewType: TerminalViewType;\n  showTerminal: boolean;\n\n  // Font settings\n  fontFamily: string;\n  fontSize: number;\n  fontWeight: FontWeight;\n\n  // Color settings\n  theme: TerminalTheme;\n  foregroundColor: string;\n  backgroundColor: string;\n  cursorColor: string;\n  selectionColor: string;\n  customTheme: TerminalColorTheme;\n}\n\nexport interface TerminalSettings {\n  // Display settings\n  viewType: TerminalViewType;\n  showTerminal: boolean;\n\n  // Font settings\n  fontFamily: string;\n  fontSize: number;\n  fontWeight: FontWeight;\n\n  // Color settings\n  theme: TerminalTheme;\n  foregroundColor: string;\n  backgroundColor: string;\n  cursorColor: string;\n  selectionColor: string;\n  customTheme: TerminalColorTheme;\n}\n\nexport class TerminalSettingsSection implements vscode.Disposable {\n  private readonly sectionName = 'codessa.terminal';\n  private _onDidChangeSettings = new vscode.EventEmitter<void>();\n  readonly onDidChangeSettings = this._onDidChangeSettings.event;\n\n  private readonly defaultSettings: TerminalSettings = {\n    // Display settings\n    viewType: TerminalViewType.Both,\n    showTerminal: true,\n\n    // Font settings\n    fontFamily:\n      '\"Courier New\", Consolas, \"Droid Sans Mono\", \"Fira Code\", \"Inconsolata\", \"Source Code Pro\", \"Ubuntu Mono\", monospace',\n    fontSize: 14,\n    fontWeight: 'normal',\n\n    // Color settings\n    theme: TerminalTheme.System,\n    foregroundColor: '',\n    backgroundColor: '',\n    cursorColor: '',\n    selectionColor: '',\n    customTheme: {},\n  };\n\n  private disposables: vscode.Disposable[] = [];\n\n  constructor() {\n    // Listen for configuration changes\n    this.disposables.push(\n      vscode.workspace.onDidChangeConfiguration((e) => {\n        if (e.affectsConfiguration(this.sectionName)) {\n          this._onDidChangeSettings.fire();\n        }\n      }),\n    );\n  }\n\n  dispose() {\n    this._onDidChangeSettings.dispose();\n    this.disposables.forEach((d) => d.dispose());\n  }\n\n  public getSettings(): TerminalSettings {\n    const config = vscode.workspace.getConfiguration(this.sectionName);\n    const settings = this.defaultSettings;\n\n    // Get all settings with their default values\n    const result: TerminalSettings = {\n      viewType: config.get<TerminalViewType>('viewType', settings.viewType),\n      showTerminal: config.get<boolean>('showTerminal', settings.showTerminal),\n      fontFamily: config.get<string>('fontFamily', settings.fontFamily),\n      fontSize: config.get<number>('fontSize', settings.fontSize),\n      fontWeight: config.get<FontWeight>('fontWeight', settings.fontWeight),\n      theme: config.get<TerminalTheme>('theme', settings.theme),\n      foregroundColor: config.get<string>(\n        'foregroundColor',\n        settings.foregroundColor,\n      ),\n      backgroundColor: config.get<string>(\n        'backgroundColor',\n        settings.backgroundColor,\n      ),\n      cursorColor: config.get<string>('cursorColor', settings.cursorColor),\n      selectionColor: config.get<string>(\n        'selectionColor',\n        settings.selectionColor,\n      ),\n      customTheme:\n        config.get<TerminalColorTheme>('customTheme', settings.customTheme) ||\n        {},\n    };\n\n    return result;\n  }\n\n  public getSetting<K extends keyof TerminalSettings>(\n    key: K,\n  ): TerminalSettings[K] {\n    const config = vscode.workspace.getConfiguration(this.sectionName);\n    return config.get(key, this.defaultSettings[key]) as TerminalSettings[K];\n  }\n\n  public async updateSettings(\n    settings: Partial<TerminalSettings>,\n  ): Promise<void> {\n    const config = vscode.workspace.getConfiguration(this.sectionName);\n\n    // Update each setting individually\n    for (const [key, value] of Object.entries(settings)) {\n      if (value !== undefined) {\n        await config.update(key, value, vscode.ConfigurationTarget.Global);\n      }\n    }\n\n    this._onDidChangeSettings.fire();\n  }\n\n  public async updateSetting<K extends keyof TerminalSettings>(\n    key: K,\n    value: TerminalSettings[K],\n    target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Global,\n  ): Promise<void> {\n    const config = vscode.workspace.getConfiguration(this.sectionName);\n    await config.update(key, value, target);\n    this._onDidChangeSettings.fire();\n  }\n\n  public async resetToDefault<K extends keyof TerminalSettings>(\n    key: K,\n  ): Promise<void> {\n    const config = vscode.workspace.getConfiguration(this.sectionName);\n    await config.update(key, undefined, true);\n    this._onDidChangeSettings.fire();\n  }\n\n  public async resetAllToDefault(): Promise<void> {\n    const config = vscode.workspace.getConfiguration(this.sectionName);\n    for (const key of Object.keys(this.defaultSettings)) {\n      await config.update(key, undefined, true);\n    }\n    this._onDidChangeSettings.fire();\n  }\n\n  public async toggleViewType(): Promise<void> {\n    const current = this.getSettings();\n    let nextViewType: TerminalViewType;\n\n    switch (current.viewType) {\n      case TerminalViewType.SideView:\n        nextViewType = TerminalViewType.DefaultTerminal;\n        break;\n      case TerminalViewType.DefaultTerminal:\n        nextViewType = TerminalViewType.Both;\n        break;\n      case TerminalViewType.Both:\n      default:\n        nextViewType = TerminalViewType.SideView;\n    }\n\n    await this.updateSettings({ viewType: nextViewType });\n  }\n\n  public async showColorPicker(\n    currentColor: string,\n  ): Promise<string | undefined> {\n    const color = await vscode.window.showInputBox({\n      value: currentColor || '#000000',\n      prompt: 'Enter a color in hex format (e.g., #FF0000 for red)',\n      validateInput: (value) => {\n        if (!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {\n          return 'Please enter a valid hex color (e.g., #FF0000 or #F00)';\n        }\n        return null;\n      },\n    });\n\n    return color || undefined;\n  }\n\n  public async showFontPicker(\n    currentFont: string,\n  ): Promise<string | undefined> {\n    const quickPick = vscode.window.createQuickPick();\n    quickPick.placeholder = 'Select a font or type to search';\n    quickPick.items = STANDARD_FONTS.map((font) => ({\n      label: font,\n      description: font === currentFont ? 'Current font' : '',\n      alwaysShow: font === currentFont,\n    }));\n\n    quickPick.onDidChangeValue((value) => {\n      if (value && !STANDARD_FONTS.includes(value)) {\n        quickPick.items = [\n          { label: value, description: 'Custom font' },\n          ...STANDARD_FONTS.map((font) => ({\n            label: font,\n            description: font === currentFont ? 'Current font' : '',\n          })),\n        ];\n      } else {\n        quickPick.items = STANDARD_FONTS.map((font) => ({\n          label: font,\n          description: font === currentFont ? 'Current font' : '',\n        }));\n      }\n    });\n\n    const selected = await new Promise<vscode.QuickPickItem | undefined>(\n      (resolve) => {\n        quickPick.onDidAccept(() => {\n          resolve(quickPick.selectedItems[0]);\n          quickPick.hide();\n        });\n        quickPick.onDidHide(() => resolve(undefined));\n        quickPick.show();\n      },\n    );\n\n    quickPick.dispose();\n    return selected?.label;\n  }\n\n  public getCurrentThemeColors() {\n    const colorTheme = vscode.window.activeColorTheme;\n    const themeKind =\n      colorTheme.kind === 1\n        ? 'light'\n        : colorTheme.kind === 2\n          ? 'dark'\n          : 'highContrast';\n\n    // Return theme color identifiers for use in webview CSS\n    return {\n      foreground: 'var(--vscode-terminal-foreground)',\n      background: 'var(--vscode-terminal-background)',\n      cursor: 'var(--vscode-terminalCursor-foreground)',\n      selection: 'var(--vscode-terminal-selectionBackground)',\n      theme: themeKind,\n    };\n  }\n\n  public async showSettings(): Promise<void> {\n    const settings = this.getSettings();\n    const themeColors = this.getCurrentThemeColors();\n\n    const categoryItems: vscode.QuickPickItem[] = [\n      {\n        label: '$(symbol-color) Appearance',\n        description: 'Font, colors, and theme settings',\n      },\n      { label: '$(gear) Advanced', description: 'Advanced terminal settings' },\n      {\n        label: '$(discard) Reset All',\n        description: 'Reset all settings to default',\n      },\n    ];\n\n    const selectedCategory = await vscode.window.showQuickPick(categoryItems, {\n      placeHolder: 'Select a settings category',\n    });\n\n    if (!selectedCategory) return;\n\n    if (selectedCategory.label.includes('Appearance')) {\n      await this.showAppearanceSettings(settings, themeColors);\n    } else if (selectedCategory.label.includes('Advanced')) {\n      await this.showAdvancedSettings(settings);\n    } else if (selectedCategory.label.includes('Reset')) {\n      await this.confirmResetAll();\n    }\n  }\n\n  private async showAppearanceSettings(\n    settings: TerminalSettings,\n    themeColors: any,\n  ): Promise<void> {\n    const items: vscode.QuickPickItem[] = [\n      {\n        label: 'Font Family',\n        description: settings.fontFamily,\n        detail: 'Change the terminal font family',\n        alwaysShow: true,\n      },\n      {\n        label: 'Font Size',\n        description: settings.fontSize.toString(),\n        detail: 'Change the terminal font size',\n        alwaysShow: true,\n      },\n      {\n        label: 'Font Weight',\n        description: settings.fontWeight,\n        detail: 'Change the terminal font weight',\n        alwaysShow: true,\n      },\n      {\n        label: 'Text Color',\n        description: settings.foregroundColor || 'Theme Default',\n        detail: `Change the terminal text color (Default: ${themeColors.foreground})`,\n        alwaysShow: true,\n      },\n      {\n        label: 'Background Color',\n        description: settings.backgroundColor || 'Theme Default',\n        detail: `Change the terminal background color (Default: ${themeColors.background})`,\n        alwaysShow: true,\n      },\n      {\n        label: 'Cursor Color',\n        description: settings.cursorColor || 'Theme Default',\n        detail: `Change the terminal cursor color (Default: ${themeColors.cursor})`,\n        alwaysShow: true,\n      },\n      {\n        label: 'Color Theme',\n        description:\n          settings.theme.charAt(0).toUpperCase() + settings.theme.slice(1),\n        detail: 'Change the terminal color theme',\n        alwaysShow: true,\n      },\n    ];\n\n    const selected = await vscode.window.showQuickPick(items, {\n      placeHolder: 'Select a setting to change',\n      matchOnDetail: true,\n    });\n\n    if (!selected) return;\n\n    switch (selected.label) {\n      case 'Font Family':\n        await this.handleFontFamilyChange(settings);\n        break;\n      case 'Font Size':\n        await this.handleFontSizeChange(settings);\n        break;\n      case 'Font Weight':\n        await this.handleFontWeightChange(settings);\n        break;\n      case 'Text Color':\n        await this.handleColorChange('foregroundColor');\n        break;\n      case 'Background Color':\n        await this.handleColorChange('backgroundColor');\n        break;\n      case 'Cursor Color':\n        await this.handleColorChange('cursorColor');\n        break;\n      case 'Color Theme':\n        await this.handleThemeChange(settings);\n        break;\n    }\n\n    // Show the settings again after making a change\n    await this.showAppearanceSettings(this.getSettings(), themeColors);\n  }\n\n  private async showAdvancedSettings(\n    settings: TerminalSettings,\n  ): Promise<void> {\n    const items: vscode.QuickPickItem[] = [\n      {\n        label: 'View Type',\n        description:\n          settings.viewType === 'sideView'\n            ? 'Side View'\n            : settings.viewType === 'defaultTerminal'\n              ? 'Default Terminal'\n              : 'Both',\n        detail: 'Change where the terminal output is displayed',\n        alwaysShow: true,\n      },\n      {\n        label: 'Show Terminal',\n        description: settings.showTerminal ? 'Yes' : 'No',\n        detail: 'Toggle terminal visibility',\n        alwaysShow: true,\n      },\n      {\n        label: 'Reset to Defaults',\n        description: 'Reset all settings to their default values',\n        detail: 'This will reset all terminal settings',\n        alwaysShow: true,\n      },\n    ];\n\n    const selected = await vscode.window.showQuickPick(items, {\n      placeHolder: 'Select an advanced setting to change',\n      matchOnDetail: true,\n    });\n\n    if (!selected) return;\n\n    switch (selected.label) {\n      case 'View Type':\n        await this.toggleViewType();\n        break;\n      case 'Show Terminal':\n        await this.updateSetting('showTerminal', !settings.showTerminal);\n        break;\n      case 'Reset to Defaults':\n        await this.confirmResetAll();\n        break;\n    }\n\n    // Show the settings again after making a change\n    await this.showAdvancedSettings(this.getSettings());\n  }\n\n  private async confirmResetAll(): Promise<void> {\n    const confirm = await vscode.window.showWarningMessage(\n      'Are you sure you want to reset all terminal settings to their default values?',\n      { modal: true },\n      'Reset All',\n      'Cancel',\n    );\n\n    if (confirm === 'Reset All') {\n      await this.resetAllToDefault();\n      vscode.window.showInformationMessage(\n        'Terminal settings have been reset to default.',\n      );\n    }\n  }\n\n  private async handleFontFamilyChange(\n    settings: TerminalSettings,\n  ): Promise<void> {\n    const newFont = await this.showFontPicker(settings.fontFamily);\n    if (newFont !== undefined) {\n      await this.updateSetting('fontFamily', newFont);\n    }\n  }\n\n  private async handleFontSizeChange(\n    settings: TerminalSettings,\n  ): Promise<void> {\n    const newSize = await vscode.window.showInputBox({\n      value: settings.fontSize.toString(),\n      prompt: 'Enter font size (8-32)',\n      validateInput: (value) => {\n        const size = parseInt(value, 10);\n        if (isNaN(size) || size < 8 || size > 32) {\n          return 'Please enter a number between 8 and 32';\n        }\n        return null;\n      },\n    });\n\n    if (newSize) {\n      await this.updateSetting('fontSize', parseInt(newSize, 10));\n    }\n  }\n\n  private async handleFontWeightChange(\n    settings: TerminalSettings,\n  ): Promise<void> {\n    const weights: FontWeight[] = [\n      'normal',\n      'bold',\n      '100',\n      '200',\n      '300',\n      '400',\n      '500',\n      '600',\n      '700',\n      '800',\n      '900',\n    ];\n    const selected = await vscode.window.showQuickPick(\n      weights.map((weight) => ({\n        label: weight,\n        description: weight === settings.fontWeight ? 'Current' : '',\n      })),\n      { placeHolder: 'Select a font weight' },\n    );\n\n    if (selected) {\n      await this.updateSetting('fontWeight', selected.label as FontWeight);\n    }\n  }\n\n  private async handleColorChange(\n    colorKey: keyof TerminalSettings,\n  ): Promise<void> {\n    const currentColor = this.getSettings()[colorKey] as string;\n    const colorItems = STANDARD_COLORS.map((color) => ({\n      label: color.label,\n      description: color.value === currentColor ? 'Current' : '',\n      detail: color.value ? `Color: ${color.value}` : 'Use theme default',\n      value: color.value,\n    }));\n\n    const selected = await vscode.window.showQuickPick(colorItems, {\n      placeHolder: `Select a ${colorKey\n        .replace(/([A-Z])/g, ' $1')\n        .toLowerCase()\n        .trim()}`,\n      matchOnDetail: true,\n    });\n\n    if (!selected) return;\n\n    if (selected.value === 'custom') {\n      const customColor = await this.showColorPicker(currentColor);\n      if (customColor) {\n        await this.updateSetting(colorKey, customColor);\n      }\n    } else if (selected.value === '') {\n      await this.updateSetting(colorKey, '');\n    } else if (selected.value) {\n      await this.updateSetting(colorKey, selected.value);\n    }\n  }\n\n  private async handleThemeChange(settings: TerminalSettings): Promise<void> {\n    const themes = [\n      { label: 'System', value: TerminalTheme.System },\n      { label: 'Light', value: TerminalTheme.Light },\n      { label: 'Dark', value: TerminalTheme.Dark },\n      { label: 'High Contrast', value: TerminalTheme.HighContrast },\n    ];\n\n    const selected = await vscode.window.showQuickPick(\n      themes.map((theme) => ({\n        label: theme.label,\n        description: theme.value === settings.theme ? 'Current' : '',\n        value: theme.value,\n      })),\n      { placeHolder: 'Select a color theme' },\n    );\n\n    if (selected) {\n      await this.updateSetting('theme', selected.value);\n    }\n  }\n}\n\n// Export a singleton instance\nexport const terminalSettings = new TerminalSettingsSection();\n"]}