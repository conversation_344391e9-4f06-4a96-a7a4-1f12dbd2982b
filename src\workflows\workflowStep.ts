import { WorkflowContext, WorkflowStepResult, WorkflowStepOptions } from './workflowContext';

export class WorkflowStep {
  public readonly id: string;
  public readonly name: string;
  public readonly description: string;
  private readonly _execute: (context: WorkflowContext) => Promise<WorkflowStepResult>;
  private _retryCount: number;
  private _retryDelay: number;
  private _timeout: number;
  private _parallel: boolean;
  private _condition?: (context: WorkflowContext) => boolean | Promise<boolean>;
  private _onSuccess?: (context: WorkflowContext, result: WorkflowStepResult) => void | Promise<void>;
  private _onError?: (context: WorkflowContext, error: Error) => void | Promise<void>;
  private _onComplete?: (context: WorkflowContext, result: WorkflowStepResult) => void | Promise<void>;

  constructor(options: WorkflowStepOptions) {
    this.id = options.id;
    this.name = options.name;
    this.description = options.description || '';
    this._execute = options.execute;
    this._retryCount = options.retryCount ?? 0;
    this._retryDelay = options.retryDelay ?? 1000; // 1 second default delay
    this._timeout = options.timeout ?? 30000; // 30 seconds default timeout
    this._parallel = options.parallel ?? false;
    this._condition = options.condition;
    this._onSuccess = options.onSuccess;
    this._onError = options.onError;
    this._onComplete = options.onComplete;
  }

  /**
   * Execute the workflow step with the given context
   */
  public async execute(context: WorkflowContext): Promise<WorkflowStepResult> {
    const startTime = new Date();
    let lastError: Error | undefined;
    let attempts = 0;
    const maxAttempts = 1 + this._retryCount; // Initial attempt + retries

    // Check if the step should run based on the condition
    if (this._condition) {
      try {
        const shouldRun = await Promise.resolve(this._condition(context));
        if (!shouldRun) {
          return {
            success: true,
            output: 'Step skipped due to condition',
            nextStepId: undefined,
            metadata: {
              skipped: true,
              reason: 'Condition not met'
            }
          };
        }
      } catch (error) {
        return this.handleError(context, error as Error, startTime);
      }
    }

    // Execute the step with retries
    while (attempts < maxAttempts) {
      attempts++;
      
      try {
        // Set up timeout
        const timeoutPromise = new Promise<WorkflowStepResult>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Step '${this.name}' timed out after ${this._timeout}ms`));
          }, this._timeout);
        });

        // Execute the step with timeout
        const result = await Promise.race([
          this._execute(context),
          timeoutPromise
        ]);

        // Handle success
        if (result.success) {
          if (this._onSuccess) {
            await Promise.resolve(this._onSuccess(context, result));
          }
          
          if (this._onComplete) {
            await Promise.resolve(this._onComplete(context, result));
          }
          
          return result;
        } else {
          // If execution was not successful but didn't throw, treat as an error
          throw new Error(result.error?.toString() || 'Step execution failed');
        }
      } catch (error) {
        lastError = error as Error;
        
        // If we have retries left, wait before retrying
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, this._retryDelay));
        }
      }
    }

    // If we get here, all attempts failed
    return this.handleError(context, lastError || new Error('Unknown error'), startTime);
  }

  /**
   * Handle errors during step execution
   */
  private async handleError(context: WorkflowContext, error: Error, startTime: Date): Promise<WorkflowStepResult> {
    const errorResult: WorkflowStepResult = {
      success: false,
      error: error.message,
      output: undefined,
      nextStepId: undefined,
      metadata: {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        }
      }
    };

    // Call error handler if provided
    if (this._onError) {
      try {
        await Promise.resolve(this._onError(context, error));
      } catch (handlerError) {
        console.error('Error in onError handler:', handlerError);
      }
    }

    // Call complete handler if provided
    if (this._onComplete) {
      try {
        await Promise.resolve(this._onComplete(context, errorResult));
      } catch (handlerError) {
        console.error('Error in onComplete handler:', handlerError);
      }
    }

    return errorResult;
  }

  /**
   * Check if this step can run in parallel
   */
  public get isParallel(): boolean {
    return this._parallel;
  }

  /**
   * Create a shallow copy of the step with overridden options
   */
  public clone(overrides: Partial<WorkflowStepOptions>): WorkflowStep {
    return new WorkflowStep({
      id: overrides.id ?? this.id,
      name: overrides.name ?? this.name,
      description: overrides.description ?? this.description,
      execute: overrides.execute ?? this._execute,
      retryCount: overrides.retryCount ?? this._retryCount,
      retryDelay: overrides.retryDelay ?? this._retryDelay,
      timeout: overrides.timeout ?? this._timeout,
      parallel: overrides.parallel ?? this._parallel,
      condition: overrides.condition ?? this._condition,
      onSuccess: overrides.onSuccess ?? this._onSuccess,
      onError: overrides.onError ?? this._onError,
      onComplete: overrides.onComplete ?? this._onComplete
    });
  }
}
