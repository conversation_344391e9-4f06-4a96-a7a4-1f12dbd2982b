"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.documentationTool = exports.DocumentationTool = void 0;
const logger_1 = require("../logger");
const llmService_1 = require("../llm/llmService");
const advancedDocsTool_1 = require("./advancedDocsTool");
const zod_1 = require("zod");
/**
 * A tool for searching documentation or asking general knowledge questions.
 * This is currently implemented as a pass-through to the LLM, but could be extended
 * to use web search or other documentation sources.
 */
class DocumentationTool {
    // Schema definition for the tool
    schema = zod_1.z.object({
        action: zod_1.z.string().optional().describe('The action to perform (search, generate, summarize, visualize, ask)'),
        query: zod_1.z.string().optional().describe('The search query or question'),
        topic: zod_1.z.string().optional().describe('Topic for documentation generation'),
        content: zod_1.z.string().optional().describe('Content to process (e.g., for summarization)'),
        data: zod_1.z.any().optional().describe('Data for visualization')
    });
    // Alias for backward compatibility
    get inputSchema() { return this.schema; }
    // Output schema for the tool
    outputSchema = zod_1.z.object({
        success: zod_1.z.boolean(),
        output: zod_1.z.any(),
        error: zod_1.z.string().optional(),
        toolId: zod_1.z.string(),
        actionName: zod_1.z.string().optional()
    });
    llmConfig = {
        provider: 'openai',
        modelId: 'gpt-3.5-turbo',
        options: { temperature: 0.3 }
    };
    llmService; // Define llmService property
    id = 'docs';
    name = 'Documentation (Advanced)';
    description = 'Search, generate, summarize, and visualize technical documentation or knowledge.';
    type = 'multi-action'; // Required by ITool
    actions = {
        'search': {
            ...new advancedDocsTool_1.DocumentationSearchTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                const query = input.query;
                if (!query) {
                    return { success: false, error: '\'query\' parameter is required.', toolId: 'search', actionName };
                }
                // Placeholder implementation
                return { success: true, output: `Documentation search results for: ${query}`, toolId: 'search', actionName };
            }
        },
        'generate': {
            ...new advancedDocsTool_1.DocumentationGenTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                const topic = input.topic;
                if (!topic) {
                    return { success: false, error: '\'topic\' parameter is required.', toolId: 'generate', actionName };
                }
                // Placeholder implementation
                return { success: true, output: `Generated documentation for: ${topic}`, toolId: 'generate', actionName };
            }
        },
        'summarize': {
            ...new advancedDocsTool_1.DocumentationSummaryTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                const content = input.content;
                if (!content) {
                    return { success: false, error: '\'content\' parameter is required.', toolId: 'summarize', actionName };
                }
                // Placeholder implementation
                return { success: true, output: `Summarized documentation: ${content.substring(0, 50)}...`, toolId: 'summarize', actionName };
            }
        },
        'visualize': {
            ...new advancedDocsTool_1.DocumentationVisualizationTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                const data = input.data;
                if (!data) {
                    return { success: false, error: '\'data\' parameter is required.', toolId: 'visualize', actionName };
                }
                // Placeholder implementation
                return { success: true, output: 'Visualization created for the provided data', toolId: 'visualize', actionName };
            }
        },
        'ask': {
            id: 'ask',
            name: 'Ask Documentation',
            description: 'Ask a documentation or technical question.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                query: zod_1.z.string().describe('The documentation or technical question to ask.')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    query: { type: 'string', description: 'The documentation or technical question to ask.' }
                },
                required: ['query']
            },
            async execute(actionName, input, _context) {
                const query = input.query;
                if (!query) {
                    return { success: false, error: '\'query\' parameter is required.', toolId: 'ask', actionName };
                }
                logger_1.Logger.instance.info(`Documentation search requested for: "${query}"`);
                logger_1.Logger.instance.warn('Using LLM for documentation search. This may not be accurate for recent information.');
                const researchLLMConfig = {
                    provider: 'openai',
                    modelId: 'gpt-3.5-turbo',
                    options: { temperature: 0.3 }
                };
                const provider = await llmService_1.llmService.getProviderForConfig(researchLLMConfig);
                if (!provider) {
                    return { success: false, error: 'LLM provider for documentation search not found or configured.', toolId: 'ask', actionName };
                }
                try {
                    const systemPrompt = 'You are a documentation researcher. Your task is to answer the following query with accurate, technical information.\nBe concise but thorough. Include code examples where appropriate. If you don\'t know the answer, say so instead of making things up.\nOnly answer what is asked - do not try to provide additional information beyond the scope of the query.';
                    const result = await provider.generate({
                        prompt: query,
                        systemPrompt: systemPrompt,
                        modelId: researchLLMConfig.modelId,
                        options: researchLLMConfig.options,
                        mode: 'generate'
                    });
                    if (result.error) {
                        return { success: false, error: result.error, toolId: 'ask', actionName };
                    }
                    return { success: true, output: result.content, toolId: 'ask', actionName };
                }
                catch (error) {
                    return { success: false, error: `Documentation search failed: ${error.message || error}`, toolId: 'ask', actionName };
                }
            }
        }
    };
    async execute(input, context) {
        // For multi-action tools, extract action from input if not provided
        const actionName = input.action;
        // If an action is specified, delegate to the appropriate action tool
        if (actionName && this.actions[actionName]) {
            const actionTool = this.actions[actionName];
            // Handle both new and old style action tools
            if (actionTool.execute) {
                const result = await actionTool.execute(input, context);
                // Ensure the action name is included in the result
                if (result && !result.actionName) {
                    result.actionName = actionName;
                }
                return result;
            }
        }
        // Default behavior - treat as a documentation search if no action specified
        const query = input.query;
        if (!query) {
            return { success: false, error: '\'query\' parameter is required.', toolId: this.id, actionName };
        }
        logger_1.Logger.instance.info(`Documentation search requested for: "${query}"`);
        // In a real implementation, we might call a search API or a dedicated service
        // For now, let's use the LLM as a fallback
        logger_1.Logger.instance.warn('Using LLM for documentation search. This may not be accurate for recent information.');
        const provider = await llmService_1.llmService.getProviderForConfig(this.llmConfig);
        if (!provider) {
            return { success: false, error: 'LLM provider for documentation search not found or configured.', toolId: this.id, actionName };
        }
        try {
            const systemPrompt = `You are a documentation researcher. Your task is to answer the following query with accurate, technical information.
Be concise but thorough. Include code examples where appropriate. If you don't know the answer, say so instead of making things up.
Only answer what is asked - do not try to provide additional information beyond the scope of the query.`;
            const result = await provider.generate({
                prompt: query,
                systemPrompt: systemPrompt,
                modelId: this.llmConfig.modelId,
                options: this.llmConfig.options,
                mode: 'generate'
            });
            if (result.error) {
                return { success: false, error: `Documentation search failed: ${result.error}`, toolId: this.id, actionName };
            }
            return { success: true, output: result.content, toolId: this.id, actionName };
        }
        catch (error) {
            logger_1.Logger.instance.error(`Error during documentation search for query "${query}":`, error);
            return { success: false, error: `Documentation search failed: ${error.message || error}`, toolId: this.id, actionName };
        }
    }
    async generate(params) {
        return await this.llmService.generate(params);
    }
}
exports.DocumentationTool = DocumentationTool;
exports.documentationTool = new DocumentationTool();
//# sourceMappingURL=docsTool.js.map