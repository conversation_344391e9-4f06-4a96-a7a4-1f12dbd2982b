"use strict";
/**
 * Codessa workflow implementation
 *
 * Exports types, graph implementation, registry, memory implementation, and
 * various templates for creating Codessa workflows.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTechnicalDebtWorkflow = exports.createPatternRefactoringWorkflow = exports.mcpManager = exports.createMCPWorkflow = exports.checkpointManager = exports.createCheckpointWorkflow = exports.createPRReviewWorkflow = exports.createPRCreationWorkflow = exports.WorkflowFactory = exports.createScrumWorkflow = exports.createXPWorkflow = exports.createAgileWorkflow = exports.createDevOpsWorkflow = exports.createUXDesignWorkflow = exports.createAgenticWorkflow = exports.createCodeGenWorkflow = exports.createEditWorkflow = exports.createAskWorkflow = exports.createDebuggingWorkflow = exports.createCodeRefactoringWorkflow = exports.createSprintPlanningWorkflow = exports.createTDDWorkflow = exports.createMultiAgentWorkflow = exports.createReActWorkflow = exports.createChatWorkflow = void 0;
// Export types
__exportStar(require("./types"), exports);
// Export graph implementation
__exportStar(require("./graph"), exports);
// Export registry
__exportStar(require("./workflowRegistry"), exports);
// Export memory implementation
__exportStar(require("./memory"), exports);
// Export templates (excluding createMemoryEnhancedWorkflow to avoid ambiguity)
var templates_1 = require("./templates");
Object.defineProperty(exports, "createChatWorkflow", { enumerable: true, get: function () { return templates_1.createChatWorkflow; } });
Object.defineProperty(exports, "createReActWorkflow", { enumerable: true, get: function () { return templates_1.createReActWorkflow; } });
Object.defineProperty(exports, "createMultiAgentWorkflow", { enumerable: true, get: function () { return templates_1.createMultiAgentWorkflow; } });
Object.defineProperty(exports, "createTDDWorkflow", { enumerable: true, get: function () { return templates_1.createTDDWorkflow; } });
Object.defineProperty(exports, "createSprintPlanningWorkflow", { enumerable: true, get: function () { return templates_1.createSprintPlanningWorkflow; } });
// Export advanced templates
__exportStar(require("./advancedTemplates"), exports);
// Export specialized templates
// Use explicit re-exports to avoid naming conflicts
var specializedTemplates_1 = require("./specializedTemplates");
// Explicitly re-export functions with unique names to avoid conflicts
Object.defineProperty(exports, "createCodeRefactoringWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createCodeRefactoringWorkflow; } });
Object.defineProperty(exports, "createDebuggingWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createDebuggingWorkflow; } });
Object.defineProperty(exports, "createAskWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createAskWorkflow; } });
Object.defineProperty(exports, "createEditWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createEditWorkflow; } });
Object.defineProperty(exports, "createCodeGenWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createCodeGenWorkflow; } });
Object.defineProperty(exports, "createAgenticWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createAgenticWorkflow; } });
Object.defineProperty(exports, "createUXDesignWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createUXDesignWorkflow; } });
Object.defineProperty(exports, "createDevOpsWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createDevOpsWorkflow; } });
Object.defineProperty(exports, "createAgileWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createAgileWorkflow; } });
Object.defineProperty(exports, "createXPWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createXPWorkflow; } });
Object.defineProperty(exports, "createScrumWorkflow", { enumerable: true, get: function () { return specializedTemplates_1.createScrumWorkflow; } });
Object.defineProperty(exports, "WorkflowFactory", { enumerable: true, get: function () { return specializedTemplates_1.WorkflowFactory; } });
// Export PR workflows
var prWorkflows_1 = require("./prWorkflows");
Object.defineProperty(exports, "createPRCreationWorkflow", { enumerable: true, get: function () { return prWorkflows_1.createPRCreationWorkflow; } });
Object.defineProperty(exports, "createPRReviewWorkflow", { enumerable: true, get: function () { return prWorkflows_1.createPRReviewWorkflow; } });
// Export checkpoint workflow
var checkpointWorkflow_1 = require("./checkpointWorkflow");
Object.defineProperty(exports, "createCheckpointWorkflow", { enumerable: true, get: function () { return checkpointWorkflow_1.createCheckpointWorkflow; } });
// Re-export checkpoint manager from its new location
var checkpoint_1 = require("../../checkpoint");
Object.defineProperty(exports, "checkpointManager", { enumerable: true, get: function () { return checkpoint_1.checkpointManager; } });
// Export MCP workflow
var mcpWorkflow_1 = require("./mcpWorkflow");
Object.defineProperty(exports, "createMCPWorkflow", { enumerable: true, get: function () { return mcpWorkflow_1.createMCPWorkflow; } });
// Re-export MCP manager from its new location
var mcp_1 = require("../../mcp");
Object.defineProperty(exports, "mcpManager", { enumerable: true, get: function () { return mcp_1.mcpManager; } });
// Export advanced refactoring workflows
var advancedRefactoring_1 = require("./advancedRefactoring");
Object.defineProperty(exports, "createPatternRefactoringWorkflow", { enumerable: true, get: function () { return advancedRefactoring_1.createPatternRefactoringWorkflow; } });
Object.defineProperty(exports, "createTechnicalDebtWorkflow", { enumerable: true, get: function () { return advancedRefactoring_1.createTechnicalDebtWorkflow; } });
// Export vector stores
__exportStar(require("./vectorStores"), exports);
// Export polyfills
__exportStar(require("./corePolyfill"), exports);
//# sourceMappingURL=codessaWorkflows.js.map