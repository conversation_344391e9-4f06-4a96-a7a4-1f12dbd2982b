{"version": 3, "file": "promptsHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/promptsHandler.ts"], "names": [], "mappings": ";;AACA,oDA6DC;AA9DD,8CAA8C;AAC9C,SAAgB,oBAAoB,CAAC,OAAY,EAAE,KAAU;IAC3D,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3E,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtJ,IAAI,CAAC;QACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,YAAY,CAAC,CAAC,CAAC;gBAClB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC;gBACjC,MAAM;YACR,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC9B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;oBAC1C,QAAQ,CAAC,KAAK,GAAG,iCAAiC,CAAC;oBACnD,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC9B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;gBACvB,MAAM;YACR,CAAC;YACD,KAAK,YAAY,CAAC,CAAC,CAAC;gBAClB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC9B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;oBAC1B,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;gBACvE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,mBAAmB,CAAC;oBACrC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC;gBAChE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACtC,MAAM;YACR,CAAC;YACD,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAChE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,mBAAmB,CAAC;oBACrC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAChC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC3C,CAAC;QACD,CAAC;IACH,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for Prompts CRUD messages and logic\nexport function handlePromptsMessage(message: any, panel: any) {\n  const settings = (window as any).settings || {};\n  settings.prompts = Array.isArray(settings.prompts) ? settings.prompts : [];\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: message.type, success: false, data: null, error: null };\n  try {\n    switch (message.type) {\n    case 'getPrompts': {\n      response.success = true;\n      response.data = settings.prompts;\n      break;\n    }\n    case 'addPrompt': {\n      const prompt = message.prompt;\n      if (!prompt || !prompt.name || !prompt.id) {\n        response.error = 'Missing required prompt fields.';\n        break;\n      }\n      settings.prompts.push(prompt);\n      response.success = true;\n      response.data = prompt;\n      break;\n    }\n    case 'editPrompt': {\n      const prompt = message.prompt;\n      if (!prompt || !prompt.id) {\n        response.error = 'Prompt id required.';\n        break;\n      }\n      const idx = settings.prompts.findIndex((p: any) => p.id === prompt.id);\n      if (idx === -1) {\n        response.error = 'Prompt not found.';\n        break;\n      }\n      settings.prompts[idx] = { ...settings.prompts[idx], ...prompt };\n      response.success = true;\n      response.data = settings.prompts[idx];\n      break;\n    }\n    case 'deletePrompt': {\n      const id = message.id;\n      if (!id) {\n        response.error = 'Prompt id required.';\n        break;\n      }\n      const idx = settings.prompts.findIndex((p: any) => p.id === id);\n      if (idx === -1) {\n        response.error = 'Prompt not found.';\n        break;\n      }\n      settings.prompts.splice(idx, 1);\n      response.success = true;\n      break;\n    }\n    default: {\n      response.error = 'Unknown message type.';\n    }\n    }\n  } catch (err: any) {\n    response.error = err?.message || String(err);\n  }\n  panel.postMessage(response);\n}\n\n"]}