{"version": 3, "file": "debugAgent.js", "sourceRoot": "", "sources": ["../../../src/agents/agentTypes/debugAgent.ts"], "names": [], "mappings": ";;;AAAA,mDAA6F;AAE7F,yCAAsC;AACtC,iFAA8E;AAE9E,MAAa,UAAW,SAAQ,aAAK;IAC3B,cAAc,CAA0B;IAEhD,YAAY,OAAY;QACtB,KAAK,CAAC;YACJ,GAAG,OAAO;YACV,IAAI,EAAE,WAAW;YACjB,YAAY,EAAE,CAAC,OAAO,CAAC;YACvB,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,GAAG,IAAI,iDAAuB,EAAE,CAAC;IACtD,CAAC;IAEQ,KAAK,CAAC,GAAG,CAAC,KAAoB,EAAE,UAAwB,EAAE;QACjE,eAAM,CAAC,IAAI,CAAC,2BAA2B,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;QAE5E,kDAAkD;QAClD,IAAI,QAAQ,GAAQ,SAAS,CAAC;QAC9B,IAAI,OAAO,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC;YACvC,QAAQ,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACjF,CAAC;QAED,oCAAoC;QACpC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,GAAG,OAAO,CAAC,KAA2B,CAAC;QAC9C,CAAC;QAED,kDAAkD;QAClD,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;gBACrB,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QACD,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,OAAO,CAAC,SAAS,EAAE,oBAAoB,EAAE,CAAC;YAC5C,SAAS,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC9F,CAAC;QAED,6BAA6B;QAC7B,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;YAClC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,2CAA2C;QAC3C,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,IAAI,OAAO,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;YACrC,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAC1G,CAAC;QAED,wEAAwE;QACxE,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAE3B,IAAI,mBAAmB,IAAI,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC;YAC1D,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC;gBAEnI,uCAAuC;gBACvC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC9C,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;oBAC3D,iBAAiB,GAAG,8CAA8C,CAAC;gBACrE,CAAC;qBAAM,CAAC;oBACN,oDAAoD;oBACpD,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,EAAE;wBACpE,QAAQ;wBACR,SAAS,EAAE,EAAE;qBACd,CAAC,CAAC;oBAEH,iCAAiC;oBACjC,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;wBACxC,eAAe;wBACf,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC/B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,EAAE,KAAK,CAAC,CACxE;qBACF,CAAC,CAAC;oBAEH,IAAI,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;wBACpD,qEAAqE;wBACrE,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,CAAC;wBACvC,MAAM,eAAe,GAAG;4BACtB,GAAG,QAAQ;4BACX,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,gCAAgC;yBAC5E,CAAC;wBACF,iBAAiB,GAAG,+BAA+B,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;wBAE9F,6DAA6D;wBAC7D,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;4BAC5D,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;4BAC5C,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;wBAE/C,IAAI,WAAW,EAAE,CAAC;4BAChB,IAAI,CAAC;gCACH,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,mBAAmB,EAAE;oCACxE,QAAQ;oCACR,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,2BAA2B;oCAC3E,UAAU,EAAE,CAAC,CAAC,0BAA0B;iCACzC,CAAC,CAAC;gCAEH,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;oCACzC,gBAAgB;oCAChB,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC/B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,EAAE,KAAK,CAAC,CAC1E;iCACF,CAAC,CAAC;gCAEH,IAAI,eAAe,CAAC,OAAO,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;oCACtD,MAAM,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC;oCACvC,0CAA0C;oCAC1C,OAAO,CAAC,SAAS,GAAG;wCAClB,GAAG,OAAO,CAAC,SAAS;wCACpB,SAAS,EAAE,OAAO;wCAClB,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE;qCAC9B,CAAC;oCACF,iBAAiB,IAAI,8BAA8B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;gCACxF,CAAC;4BACH,CAAC;4BAAC,OAAO,QAAQ,EAAE,CAAC;gCAClB,MAAM,YAAY,GAAG,QAAQ,YAAY,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gCACnF,eAAM,CAAC,IAAI,CAAC,+BAA+B,YAAY,EAAE,CAAC,CAAC;gCAC3D,OAAO,CAAC,SAAS,GAAG;oCAClB,GAAG,OAAO,CAAC,SAAS;oCACpB,SAAS,EAAE;wCACT,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC;wCACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;qCACtB;iCACF,CAAC;gCACF,iBAAiB,IAAI,wDAAwD,CAAC;4BAChF,CAAC;wBACH,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,iBAAiB,GAAG,qDAAqD,CAAC;oBAC5E,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;gBACtD,iBAAiB,GAAG,uDAAuD,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,WAAW,GAAG;;;EAGtB,cAAc;;;EAGd,KAAK,CAAC,MAAM;;;EAGZ,aAAa;;;EAGb,SAAS;;;EAGT,UAAU;;;EAGV,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM;;;EAG5C,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,2BAA2B;;EAElL,iBAAiB;;;;;;;;;;;;;;;;CAgBlB,CAAC;QACE,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC;QAC3B,mDAAmD;QACnD,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACG,4BAA4B,CAAC,MAAc;QACjD,MAAM,oBAAoB,GAAG;YAC3B,qBAAqB;YACrB,cAAc;YACd,YAAY;YACZ,eAAe;YACf,cAAc;YACd,SAAS;YACT,UAAU;YACV,kBAAkB;YAClB,KAAK;YACL,QAAQ;YACR,OAAO;SACR,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACzC,OAAO,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;IACjF,CAAC;CACF;AAlND,gCAkNC", "sourcesContent": ["import { Agent, AgentContext, AgentRunInput, AgentRunResult } from '../agentUtilities/agent';\nimport { ITool } from '../../tools/types';\nimport { logger } from '../../logger';\nimport { TimeTravelDebuggingTool } from '../../tools/timeTravelDebuggingTool';\n\nexport class DebugAgent extends Agent {\n  private timeTravelTool: TimeTravelDebuggingTool;\n\n  constructor(_config: any) {\n    super({\n      ..._config,\n      role: 'assistant',\n      capabilities: ['debug'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4'\n    });\n    this.timeTravelTool = new TimeTravelDebuggingTool();\n  }\n\n  override async run(input: AgentRunInput, context: AgentContext = {}): Promise<AgentRunResult> {\n    logger.info(`DebugAgent processing: \"${input.prompt.substring(0, 50)}...\"`);\n\n    // --- Gather relevant workflow for debug mode ---\n    let workflow: any = undefined;\n    if (context.variables?.workflowManager) {\n      workflow = await context.variables.workflowManager.getWorkflowForMode('debug');\n    }\n\n    // --- Gather all relevant tools ---\n    let tools = this.tools;\n    if (context.tools) {\n      tools = context.tools as Map<string, ITool>;\n    }\n\n    // --- Gather memory and knowledgebase context ---\n    let memoryContext = '';\n    if (this.getMemory) {\n      const memories = await this.getMemory().getRelevantMemories(input.prompt);\n      if (memories?.length) {\n        memoryContext = this.getMemory().formatMemoriesForPrompt(memories);\n      }\n    }\n    let kbContext = '';\n    if (context.variables?.knowledgebaseManager) {\n      kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input.prompt);\n    }\n\n    // --- Gather MCP context ---\n    let mcpContext = '';\n    if (context.variables?.mcpManager) {\n      mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext() || {});\n    }\n\n    // --- Use external prompt if available ---\n    let externalPrompt = '';\n    if (context.variables?.promptManager) {\n      externalPrompt = context.variables.promptManager.getSystemPrompt('debugAgent', context.variables) || '';\n    }\n\n    // Phase 2: Enhanced debugging with time-travel capabilities (Optimized)\n    const shouldUseTimeTravel = this.shouldUseTimeTravelDebugging(input.prompt);\n    let timeTravelContext = '';\n\n    if (shouldUseTimeTravel && context.workspace?.currentFile) {\n      try {\n        const filePath = typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile;\n\n        // Validate file path before proceeding\n        if (!filePath || filePath.trim().length === 0) {\n          logger.warn('Invalid file path for time-travel debugging');\n          timeTravelContext = '\\n## Time-Travel Analysis: Invalid file path';\n        } else {\n          // Create timeline for the current file with timeout\n          const timelinePromise = this.timeTravelTool.execute('createTimeline', {\n            filePath,\n            maxEvents: 10\n          });\n\n          // Add timeout to prevent hanging\n          const timelineResult = await Promise.race([\n            timelinePromise,\n            new Promise<never>((_, reject) =>\n              setTimeout(() => reject(new Error('Timeline creation timeout')), 30000)\n            )\n          ]);\n\n          if (timelineResult.success && timelineResult.output) {\n            // Limit the size of timeline data to prevent overwhelming the prompt\n            const timeline = timelineResult.output;\n            const limitedTimeline = {\n              ...timeline,\n              events: timeline.events?.slice(0, 5) || [] // Limit to 5 most recent events\n            };\n            timeTravelContext = `\\n## Time-Travel Analysis:\\n${JSON.stringify(limitedTimeline, null, 2)}`;\n\n            // Try to identify bug origin if this looks like a bug report\n            const isBugReport = input.prompt.toLowerCase().includes('bug') ||\n              input.prompt.toLowerCase().includes('error') ||\n              input.prompt.toLowerCase().includes('issue');\n\n            if (isBugReport) {\n              try {\n                const bugOriginPromise = this.timeTravelTool.execute('identifyBugOrigin', {\n                  filePath,\n                  bugDescription: input.prompt.substring(0, 500), // Limit description length\n                  maxCommits: 5 // Reduced for performance\n                });\n\n                const bugOriginResult = await Promise.race([\n                  bugOriginPromise,\n                  new Promise<never>((_, reject) =>\n                    setTimeout(() => reject(new Error('Bug origin analysis timeout')), 20000)\n                  )\n                ]);\n\n                if (bugOriginResult.success && bugOriginResult.output) {\n                  const bugData = bugOriginResult.output;\n                  // Store bug analysis in context for reuse\n                  context.debugData = {\n                    ...context.debugData,\n                    bugOrigin: bugData,\n                    analysisTimestamp: Date.now()\n                  };\n                  timeTravelContext += `\\n## Bug Origin Analysis:\\n${JSON.stringify(bugData, null, 2)}`;\n                }\n              } catch (bugError) {\n                const errorDetails = bugError instanceof Error ? bugError.stack : String(bugError);\n                logger.warn(`Bug origin analysis failed: ${errorDetails}`);\n                context.debugData = {\n                  ...context.debugData,\n                  lastError: {\n                    message: String(bugError),\n                    timestamp: Date.now()\n                  }\n                };\n                timeTravelContext += '\\n## Bug Origin Analysis: Analysis failed or timed out';\n              }\n            }\n          } else {\n            timeTravelContext = '\\n## Time-Travel Analysis: Timeline creation failed';\n          }\n        }\n      } catch (error) {\n        logger.warn(`Time-travel debugging failed: ${error}`);\n        timeTravelContext = '\\n## Time-Travel Analysis: Not available due to error';\n      }\n    }\n\n    // --- Compose final prompt ---\n    const debugPrompt = `\n# Enhanced Debug Mode Processing with Time-Travel Capabilities\n\n${externalPrompt}\n\n## User Problem:\n${input.prompt}\n\n## Memory Context:\n${memoryContext}\n\n## Knowledgebase Context:\n${kbContext}\n\n## MCP Context:\n${mcpContext}\n\n## Workflow:\n${workflow ? JSON.stringify(workflow) : 'None'}\n\n## Code Context:\n${context.workspace?.currentFile ? (typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile) : 'No code context available'}\n\n${timeTravelContext}\n\n## Your Enhanced Debugging Task:\n1. Analyze the problem using all available context including time-travel data\n2. Identify the root cause of the issue (use timeline analysis if available)\n3. Explain the problem clearly with historical context\n4. Provide a specific solution with confidence level\n5. Suggest preventive measures based on historical patterns\n\n## Available Advanced Tools:\n- Time-travel debugging for historical analysis\n- Code timeline analysis\n- Bug origin identification\n- Dependency impact analysis\n\nUse all available workflows, tools, memory, knowledgebase, and time-travel context. Stream output if possible.\n`;\n    input.prompt = debugPrompt;\n    // Pass all gathered context and tools to super.run\n    return super.run(input, { ...context, tools });\n  }\n\n  /**\n     * Determine if time-travel debugging should be used\n     */\n  private shouldUseTimeTravelDebugging(prompt: string): boolean {\n    const timeTravelIndicators = [\n      'when did this break',\n      'what changed',\n      'regression',\n      'worked before',\n      'used to work',\n      'history',\n      'timeline',\n      'previous version',\n      'git',\n      'commit',\n      'blame'\n    ];\n\n    const promptLower = prompt.toLowerCase();\n    return timeTravelIndicators.some(indicator => promptLower.includes(indicator));\n  }\n}"]}