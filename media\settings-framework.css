/**
 * Professional Settings UI Framework
 * Following Material Design 3 and VS Code Design System principles
 */

:root {
    /* Color System */
    --settings-primary: var(--vscode-editor-foreground);
    --settings-secondary: var(--vscode-descriptionForeground);
    --settings-background: var(--vscode-editor-background);
    --settings-surface: var(--vscode-sideBar-background);
    --settings-surface-variant: var(--vscode-editor-inactiveSelectionBackground);
    --settings-outline: var(--vscode-panel-border);
    --settings-outline-variant: var(--vscode-input-border);
    
    /* Interactive Colors */
    --settings-accent: var(--vscode-button-background);
    --settings-accent-hover: var(--vscode-button-hoverBackground);
    --settings-accent-pressed: var(--vscode-button-background);
    --settings-success: #22c55e;
    --settings-warning: #f59e0b;
    --settings-error: #ef4444;
    --settings-info: #3b82f6;
    
    /* Typography Scale */
    --settings-font-family: var(--vscode-font-family);
    --settings-font-size-xs: 11px;
    --settings-font-size-sm: 12px;
    --settings-font-size-base: 13px;
    --settings-font-size-lg: 14px;
    --settings-font-size-xl: 16px;
    --settings-font-size-2xl: 18px;
    --settings-font-size-3xl: 24px;
    
    /* Spacing Scale */
    --settings-space-1: 4px;
    --settings-space-2: 8px;
    --settings-space-3: 12px;
    --settings-space-4: 16px;
    --settings-space-5: 20px;
    --settings-space-6: 24px;
    --settings-space-8: 32px;
    --settings-space-10: 40px;
    --settings-space-12: 48px;
    
    /* Border Radius */
    --settings-radius-sm: 4px;
    --settings-radius-md: 6px;
    --settings-radius-lg: 8px;
    --settings-radius-xl: 12px;
    
    /* Shadows */
    --settings-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --settings-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --settings-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Transitions */
    --settings-transition-fast: 150ms ease-in-out;
    --settings-transition-normal: 250ms ease-in-out;
    --settings-transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--settings-font-family);
    font-size: var(--settings-font-size-base);
    line-height: 1.5;
    color: var(--settings-primary);
    background-color: var(--settings-background);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Layout Components */
.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--settings-space-6);
    min-height: 100vh;
}

.settings-header {
    margin-bottom: var(--settings-space-8);
    padding-bottom: var(--settings-space-6);
    border-bottom: 1px solid var(--settings-outline);
}

.settings-title {
    font-size: var(--settings-font-size-3xl);
    font-weight: 600;
    margin: 0 0 var(--settings-space-2) 0;
    color: var(--settings-primary);
}

.settings-subtitle {
    font-size: var(--settings-font-size-lg);
    color: var(--settings-secondary);
    margin: 0;
}

/* Navigation Tabs */
.settings-nav {
    display: flex;
    flex-wrap: wrap;
    gap: var(--settings-space-1);
    margin-bottom: var(--settings-space-6);
    padding: var(--settings-space-2);
    background: var(--settings-surface);
    border-radius: var(--settings-radius-lg);
    border: 1px solid var(--settings-outline-variant);
}

.settings-nav-item {
    display: flex;
    align-items: center;
    gap: var(--settings-space-2);
    padding: var(--settings-space-3) var(--settings-space-4);
    border: none;
    background: transparent;
    color: var(--settings-secondary);
    font-size: var(--settings-font-size-sm);
    font-weight: 500;
    border-radius: var(--settings-radius-md);
    cursor: pointer;
    transition: all var(--settings-transition-fast);
    white-space: nowrap;
    position: relative;
}

.settings-nav-item:hover {
    background: var(--settings-surface-variant);
    color: var(--settings-primary);
}

.settings-nav-item.active {
    background: var(--settings-accent);
    color: white;
    box-shadow: var(--settings-shadow-sm);
}

.settings-nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: currentColor;
    border-radius: 1px;
}

.settings-nav-icon {
    font-size: var(--settings-font-size-lg);
}

/* Content Areas */
.settings-content {
    background: var(--settings-surface);
    border-radius: var(--settings-radius-xl);
    border: 1px solid var(--settings-outline-variant);
    overflow: hidden;
    box-shadow: var(--settings-shadow-sm);
}

.settings-section {
    padding: var(--settings-space-6);
    border-bottom: 1px solid var(--settings-outline-variant);
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--settings-space-5);
}

.settings-section-title {
    font-size: var(--settings-font-size-xl);
    font-weight: 600;
    margin: 0;
    color: var(--settings-primary);
    display: flex;
    align-items: center;
    gap: var(--settings-space-2);
}

.settings-section-description {
    font-size: var(--settings-font-size-sm);
    color: var(--settings-secondary);
    margin: var(--settings-space-2) 0 0 0;
    line-height: 1.4;
}

.settings-section-actions {
    display: flex;
    gap: var(--settings-space-2);
}

/* Form Components */
.settings-form-grid {
    display: grid;
    gap: var(--settings-space-5);
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.settings-form-group {
    display: flex;
    flex-direction: column;
    gap: var(--settings-space-2);
}

.settings-form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--settings-space-4);
    align-items: end;
}

.settings-label {
    font-size: var(--settings-font-size-sm);
    font-weight: 500;
    color: var(--settings-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--settings-space-2);
}

.settings-label-required::after {
    content: '*';
    color: var(--settings-error);
}

.settings-help-text {
    font-size: var(--settings-font-size-xs);
    color: var(--settings-secondary);
    margin: 0;
    line-height: 1.3;
}

/* Input Components */
.settings-input,
.settings-select,
.settings-textarea {
    width: 100%;
    padding: var(--settings-space-3);
    font-size: var(--settings-font-size-sm);
    font-family: var(--settings-font-family);
    color: var(--settings-primary);
    background: var(--vscode-input-background);
    border: 1px solid var(--settings-outline-variant);
    border-radius: var(--settings-radius-md);
    transition: all var(--settings-transition-fast);
}

.settings-input:focus,
.settings-select:focus,
.settings-textarea:focus {
    outline: none;
    border-color: var(--settings-accent);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.settings-input:disabled,
.settings-select:disabled,
.settings-textarea:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.settings-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Checkbox and Radio Components */
.settings-checkbox-group,
.settings-radio-group {
    display: flex;
    flex-direction: column;
    gap: var(--settings-space-3);
}

.settings-checkbox-item,
.settings-radio-item {
    display: flex;
    align-items: center;
    gap: var(--settings-space-3);
    padding: var(--settings-space-3);
    border-radius: var(--settings-radius-md);
    cursor: pointer;
    transition: background-color var(--settings-transition-fast);
}

.settings-checkbox-item:hover,
.settings-radio-item:hover {
    background: var(--settings-surface-variant);
}

.settings-checkbox,
.settings-radio {
    width: 16px;
    height: 16px;
    accent-color: var(--settings-accent);
}

.settings-checkbox-label,
.settings-radio-label {
    font-size: var(--settings-font-size-sm);
    color: var(--settings-primary);
    cursor: pointer;
    flex: 1;
}

/* Toggle Switch Component */
.settings-toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.settings-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.settings-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--settings-outline);
    transition: var(--settings-transition-fast);
    border-radius: 24px;
}

.settings-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--settings-transition-fast);
    border-radius: 50%;
    box-shadow: var(--settings-shadow-sm);
}

.settings-toggle input:checked + .settings-toggle-slider {
    background-color: var(--settings-accent);
}

.settings-toggle input:checked + .settings-toggle-slider:before {
    transform: translateX(20px);
}

/* Slider Component */
.settings-slider-group {
    display: flex;
    flex-direction: column;
    gap: var(--settings-space-2);
}

.settings-slider-container {
    display: flex;
    align-items: center;
    gap: var(--settings-space-3);
}

.settings-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--settings-outline-variant);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.settings-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--settings-accent);
    cursor: pointer;
    box-shadow: var(--settings-shadow-md);
    transition: all var(--settings-transition-fast);
}

.settings-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.settings-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--settings-accent);
    cursor: pointer;
    border: none;
    box-shadow: var(--settings-shadow-md);
    transition: all var(--settings-transition-fast);
}

.settings-slider-value {
    min-width: 60px;
    text-align: center;
    font-size: var(--settings-font-size-sm);
    font-weight: 500;
    color: var(--settings-primary);
    background: var(--settings-surface-variant);
    padding: var(--settings-space-1) var(--settings-space-2);
    border-radius: var(--settings-radius-sm);
}

/* Button Components */
.settings-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--settings-space-2);
    padding: var(--settings-space-3) var(--settings-space-4);
    font-size: var(--settings-font-size-sm);
    font-weight: 500;
    font-family: var(--settings-font-family);
    border: none;
    border-radius: var(--settings-radius-md);
    cursor: pointer;
    transition: all var(--settings-transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.settings-button-primary {
    background: var(--settings-accent);
    color: white;
}

.settings-button-primary:hover {
    background: var(--settings-accent-hover);
    transform: translateY(-1px);
    box-shadow: var(--settings-shadow-md);
}

.settings-button-secondary {
    background: var(--settings-surface-variant);
    color: var(--settings-primary);
    border: 1px solid var(--settings-outline-variant);
}

.settings-button-secondary:hover {
    background: var(--settings-outline-variant);
    transform: translateY(-1px);
}

.settings-button-danger {
    background: var(--settings-error);
    color: white;
}

.settings-button-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--settings-shadow-md);
}

.settings-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.settings-button-group {
    display: flex;
    gap: var(--settings-space-3);
    flex-wrap: wrap;
}

/* Status and Feedback Components */
.settings-status {
    display: flex;
    align-items: center;
    gap: var(--settings-space-2);
    padding: var(--settings-space-3) var(--settings-space-4);
    border-radius: var(--settings-radius-md);
    font-size: var(--settings-font-size-sm);
    font-weight: 500;
}

.settings-status-success {
    background: rgba(34, 197, 94, 0.1);
    color: var(--settings-success);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.settings-status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--settings-warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.settings-status-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--settings-error);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.settings-status-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--settings-info);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Card Components */
.settings-card {
    background: var(--settings-surface);
    border: 1px solid var(--settings-outline-variant);
    border-radius: var(--settings-radius-lg);
    padding: var(--settings-space-5);
    transition: all var(--settings-transition-fast);
}

.settings-card:hover {
    border-color: var(--settings-outline);
    box-shadow: var(--settings-shadow-md);
}

.settings-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--settings-space-4);
}

.settings-card-title {
    font-size: var(--settings-font-size-lg);
    font-weight: 600;
    margin: 0;
    color: var(--settings-primary);
}

.settings-card-description {
    font-size: var(--settings-font-size-sm);
    color: var(--settings-secondary);
    margin: var(--settings-space-2) 0 0 0;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        padding: var(--settings-space-4);
    }
    
    .settings-nav {
        flex-direction: column;
    }
    
    .settings-form-grid,
    .settings-form-row {
        grid-template-columns: 1fr;
    }
    
    .settings-button-group {
        flex-direction: column;
    }
    
    .settings-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--settings-space-3);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles */
.settings-button:focus-visible,
.settings-input:focus-visible,
.settings-select:focus-visible,
.settings-textarea:focus-visible {
    outline: 2px solid var(--settings-accent);
    outline-offset: 2px;
}

/* Loading States */
.settings-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.settings-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--settings-outline-variant);
    border-top: 2px solid var(--settings-accent);
    border-radius: 50%;
    animation: settings-spin 1s linear infinite;
}

@keyframes settings-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
