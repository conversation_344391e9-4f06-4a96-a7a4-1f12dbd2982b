{"version": 3, "file": "operationModeWorkflows.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/operationModeWorkflows.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;AAaH,wDAqFC;AAKD,gDA4FC;AA/LD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,sBAAsB,CACpC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,aAAoB,EACpB,YAAmB,EACnB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;IAEnD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACrG,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,aAAa,CAAC,CAAC;IAC1H,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC9G,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAClG,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IACtF,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;IACzF,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtF,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAClH,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;QAChG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/F,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1F,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,yCAAyC;QACzC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,qBAAqB,KAAK,EAAE;gBAClC,MAAM,EAAE,uBAAuB;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,sCAAsC;YACtC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,cAAc;gBACjC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,eAAe;gBACvB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,iBAAiB;YACjB,wBAAwB;YACxB,oBAAoB;YACpB,gBAAgB;YAChB,aAAa;YACb,cAAc;YACd,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,UAA2B;QAC1C,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,uBAAuB,CAAC;KACxD,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAChC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,cAAqB,EACrB,iBAAwB,EACxB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;IAEhD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;IAC9H,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;IACtG,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IAC7F,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAClG,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;IACvF,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;IAClH,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACvG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpG,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACpG,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEjG,iBAAiB;QACjB,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE;QACxG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,UAAU,EAAE;KAC/G,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,6BAA6B;QAC7B,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,qBAAqB,KAAK,EAAE;gBAClC,MAAM,EAAE,WAAW;gBACnB,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,0CAA0C;YAC1C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,aAAa;gBAChC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,mBAAmB;gBAC3B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,wBAAwB;YACxB,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,aAAa;YACb,oBAAoB;YACpB,kBAAkB;YAClB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,OAAwB;QACvC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC;KAC1C,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa Operation Mode Workflows\n *\n * This module provides workflow templates for different operation modes:\n * - Research Mode\n * - UI-UX Mode\n * - Documentation Mode\n * - Technical Debt Mode\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n\n/**\n * Create a Research workflow for in-depth research tasks\n */\nexport function createResearchWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  researchAgent: Agent,\n  analysisAgent: Agent,\n  summaryAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Research workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const queryAnalysisNode = Codessa.createAgentNode('query-analysis', 'Query Analysis', researchAgent);\n  const informationRetrievalNode = Codessa.createAgentNode('information-retrieval', 'Information Retrieval', researchAgent);\n  const sourceEvaluationNode = Codessa.createAgentNode('source-evaluation', 'Source Evaluation', analysisAgent);\n  const dataAnalysisNode = Codessa.createAgentNode('data-analysis', 'Data Analysis', analysisAgent);\n  const synthesisNode = Codessa.createAgentNode('synthesis', 'Synthesis', summaryAgent);\n  const conclusionNode = Codessa.createAgentNode('conclusion', 'Conclusion', summaryAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },\n    { name: 'query-to-retrieval', source: 'query-analysis', target: 'information-retrieval', type: 'default' },\n    { name: 'retrieval-to-evaluation', source: 'information-retrieval', target: 'source-evaluation', type: 'default' },\n    { name: 'evaluation-to-analysis', source: 'source-evaluation', target: 'data-analysis', type: 'default' },\n    { name: 'analysis-to-synthesis', source: 'data-analysis', target: 'synthesis', type: 'default' },\n    { name: 'synthesis-to-conclusion', source: 'synthesis', target: 'conclusion', type: 'default' },\n    { name: 'conclusion-to-output', source: 'conclusion', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect information retrieval to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `retrieval-to-tool-${index}`,\n        source: 'information-retrieval',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to data analysis\n      edges.push({\n        name: `tool-${index}-to-analysis`,\n        source: `tool-${index}`,\n        target: 'data-analysis',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      queryAnalysisNode,\n      informationRetrievalNode,\n      sourceEvaluationNode,\n      dataAnalysisNode,\n      synthesisNode,\n      conclusionNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'research' as OperationMode,\n    tags: ['research', 'analysis', 'information-retrieval']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a UI-UX workflow for design and user experience tasks\n */\nexport function createUIUXWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  designerAgent: Agent,\n  developerAgent: Agent,\n  userResearchAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating UI-UX workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const requirementsAnalysisNode = Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', userResearchAgent);\n  const userResearchNode = Codessa.createAgentNode('user-research', 'User Research', userResearchAgent);\n  const wireframingNode = Codessa.createAgentNode('wireframing', 'Wireframing', designerAgent);\n  const designSystemNode = Codessa.createAgentNode('design-system', 'Design System', designerAgent);\n  const prototypeNode = Codessa.createAgentNode('prototype', 'Prototype', designerAgent);\n  const usabilityTestingNode = Codessa.createAgentNode('usability-testing', 'Usability Testing', userResearchAgent);\n  const implementationNode = Codessa.createAgentNode('implementation', 'Implementation', developerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },\n    { name: 'requirements-to-research', source: 'requirements-analysis', target: 'user-research', type: 'default' },\n    { name: 'research-to-wireframing', source: 'user-research', target: 'wireframing', type: 'default' },\n    { name: 'wireframing-to-design-system', source: 'wireframing', target: 'design-system', type: 'default' },\n    { name: 'design-system-to-prototype', source: 'design-system', target: 'prototype', type: 'default' },\n    { name: 'prototype-to-testing', source: 'prototype', target: 'usability-testing', type: 'default' },\n    { name: 'testing-to-implementation', source: 'usability-testing', target: 'implementation', type: 'default' },\n    { name: 'implementation-to-output', source: 'implementation', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'testing-to-wireframing', source: 'usability-testing', target: 'wireframing', type: 'feedback' },\n    { name: 'implementation-to-testing', source: 'implementation', target: 'usability-testing', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect prototype to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `prototype-to-tool-${index}`,\n        source: 'prototype',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to usability testing\n      edges.push({\n        name: `tool-${index}-to-testing`,\n        source: `tool-${index}`,\n        target: 'usability-testing',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      requirementsAnalysisNode,\n      userResearchNode,\n      wireframingNode,\n      designSystemNode,\n      prototypeNode,\n      usabilityTestingNode,\n      implementationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'ui-ux' as OperationMode,\n    tags: ['ui', 'ux', 'design', 'usability']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}