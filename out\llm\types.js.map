{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/llm/types.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface LLMConfig {\n    provider: string;\n    modelId: string;\n    temperature?: number;\n    maxTokens?: number;\n    apiKey?: string;\n    endpoint?: string;\n}\n\nexport interface LLMGenerateParams {\n    prompt: string;\n    systemPrompt?: string;\n    modelId?: string;\n    temperature?: number;\n    maxTokens?: number;\n    mode: 'chat' | 'task' | 'edit' | 'generate' | 'inline';\n    tools?: any[];\n    context?: any;\n    history?: Array<{\n        role: string;\n        content: string;\n        name?: string;\n        tool_call_id?: string;\n    }>;\n    stopSequences?: string[];\n    options?: Record<string, any>;\n}\n\nexport interface LLMToolCall {\n    toolId?: string;  // For backward compatibility\n    name?: string;    // For backward compatibility\n    args?: Record<string, any>;  // For backward compatibility\n    arguments?: Record<string, any>;  // For backward compatibility\n}\n\nexport interface LLMGenerateResult {\n    content?: string;\n    toolCall?: LLMToolCall;\n    toolCalls?: LLMToolCall[];\n    toolCallRequest?: LLMToolCall;\n    error?: string;\n    finishReason?: string;\n    usage?: {\n        promptTokens?: number;\n        completionTokens?: number;\n        totalTokens?: number;\n    };\n}\n\nexport interface LLMProvider {\n    generate(params: LLMGenerateParams): Promise<LLMGenerateResult>;\n    getModels(): Promise<string[]>;\n    validateConfig(_config: any): boolean;\n} "]}