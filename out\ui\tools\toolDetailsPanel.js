"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolDetailsPanel = void 0;
const vscode = __importStar(require("vscode"));
class ToolDetailsPanel {
    static currentPanel;
    _panel;
    _extensionUri;
    _tool;
    static viewType = 'codessa.toolDetails';
    constructor(panel, extensionUri, tool) {
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._tool = tool;
        this._panel.webview.html = this._getHtmlForWebview();
    }
    static createOrShow(extensionUri, tool) {
        const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined;
        if (ToolDetailsPanel.currentPanel) {
            ToolDetailsPanel.currentPanel._panel.reveal(column);
            ToolDetailsPanel.currentPanel._panel.webview.html = ToolDetailsPanel.currentPanel._getHtmlForWebview();
            return;
        }
        const panel = vscode.window.createWebviewPanel(ToolDetailsPanel.viewType, `Tool Details: ${tool.name || tool.id}`, column || vscode.ViewColumn.One, { enableScripts: true });
        ToolDetailsPanel.currentPanel = new ToolDetailsPanel(panel, extensionUri, tool);
    }
    _getHtmlForWebview() {
        const tool = this._tool;
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tool Details: ${tool.name || tool.id}</title>
    <style>
        body { font-family: sans-serif; padding: 16px; }
        h1 { font-size: 1.5em; }
        .section { margin-bottom: 1em; }
        .label { font-weight: bold; }
        .value { margin-left: 0.5em; }
        pre { background: #f4f4f4; padding: 8px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Tool Details</h1>
    <div class="section"><span class="label">ID:</span><span class="value">${tool.id}</span></div>
    <div class="section"><span class="label">Name:</span><span class="value">${tool.name || 'N/A'}</span></div>
    <div class="section"><span class="label">Description:</span><span class="value">${tool.description || 'No description provided.'}</span></div>
    <div class="section"><span class="label">Type:</span><span class="value">${tool.type || 'N/A'}</span></div>
    <div class="section"><span class="label">Actions:</span><span class="value">${tool.actions ? Object.keys(tool.actions).join(', ') : 'None'}</span></div>
    <div class="section"><span class="label">Config:</span><pre>${tool.config ? JSON.stringify(tool.config, null, 2) : 'None'}</pre></div>
</body>
</html>`;
    }
}
exports.ToolDetailsPanel = ToolDetailsPanel;
//# sourceMappingURL=toolDetailsPanel.js.map