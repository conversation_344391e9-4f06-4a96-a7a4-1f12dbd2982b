"use strict";
/**
 * Model Context Protocol (MCP) Manager
 *
 * This module provides integration with the Model Context Protocol (MCP),
 * which is a standard for exchanging context between different AI models
 * and tools. It allows:
 * - Sharing context between different models
 * - Standardizing context format
 * - Integrating with external services
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.mcpManager = exports.MCPManager = void 0;
const logger_1 = require("../logger");
class MCPManager {
    currentContext;
    sessionId;
    constructor() {
        this.sessionId = `session-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        this.currentContext = this.createEmptyContext();
    }
    /**
       * Creates an empty MCP context
       */
    createEmptyContext() {
        return {
            version: '1.0',
            metadata: {
                source: 'codessa',
                timestamp: new Date().toISOString(),
                sessionId: this.sessionId
            },
            content: {}
        };
    }
    /**
       * Updates the current context with new content
       */
    updateContext(content) {
        this.currentContext.content = {
            ...this.currentContext.content,
            ...content
        };
        this.currentContext.metadata.timestamp = new Date().toISOString();
        logger_1.logger.info('Updated MCP context');
    }
    /**
       * Adds code to the context
       */
    addCode(language, content, path) {
        if (!this.currentContext.content.code) {
            this.currentContext.content.code = [];
        }
        this.currentContext.content.code.push({
            language,
            content,
            path
        });
        this.currentContext.metadata.timestamp = new Date().toISOString();
        logger_1.logger.info(`Added code to MCP context: ${path || 'unnamed'}`);
    }
    /**
       * Adds a file to the context
       */
    addFile(path, content, language) {
        if (!this.currentContext.content.files) {
            this.currentContext.content.files = [];
        }
        this.currentContext.content.files.push({
            path,
            content,
            language
        });
        this.currentContext.metadata.timestamp = new Date().toISOString();
        logger_1.logger.info(`Added file to MCP context: ${path}`);
    }
    /**
       * Adds a link to the context
       */
    addLink(url, title, description) {
        if (!this.currentContext.content.links) {
            this.currentContext.content.links = [];
        }
        this.currentContext.content.links.push({
            url,
            title,
            description
        });
        this.currentContext.metadata.timestamp = new Date().toISOString();
        logger_1.logger.info(`Added link to MCP context: ${url}`);
    }
    /**
       * Adds a tool result to the context
       */
    addToolResult(name, description, parameters, results) {
        if (!this.currentContext.tools) {
            this.currentContext.tools = [];
        }
        this.currentContext.tools.push({
            name,
            description,
            parameters,
            results
        });
        this.currentContext.metadata.timestamp = new Date().toISOString();
        logger_1.logger.info(`Added tool result to MCP context: ${name}`);
    }
    /**
       * Gets the current context
       */
    getContext() {
        return { ...this.currentContext };
    }
    /**
     * Gets the current context (alias for getContext for IMCPManager interface)
     */
    getCurrentContext() {
        return this.getContext();
    }
    /**
       * Exports the context to a JSON string
       */
    exportContext() {
        return JSON.stringify(this.currentContext, null, 2);
    }
    /**
       * Imports a context from a JSON string
       */
    importContext(json) {
        try {
            const context = JSON.parse(json);
            // Validate the context
            if (!context.version || !context.metadata || !context.metadata.source) {
                throw new Error('Invalid MCP context format');
            }
            this.currentContext = context;
            logger_1.logger.info('Imported MCP context');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error(`Error importing MCP context: ${errorMessage}`);
            throw error;
        }
    }
    /**
       * Clears the current context
       */
    clearContext() {
        this.currentContext = this.createEmptyContext();
        logger_1.logger.info('Cleared MCP context');
    }
}
exports.MCPManager = MCPManager;
// Create a singleton instance
exports.mcpManager = new MCPManager();
//# sourceMappingURL=mcpManager.js.map