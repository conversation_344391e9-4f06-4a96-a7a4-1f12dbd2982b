{"version": 3, "file": "documentationMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/documentationMode.ts"], "names": [], "mappings": ";;;AACA,mDAA4E;AAG5E,yCAAsC;AACtC,qDAAkD;AAElD;;GAEG;AACH,MAAa,iBAAkB,SAAQ,6BAAa;IACzC,EAAE,GAAG,eAAe,CAAC;IACrB,WAAW,GAAG,eAAe,CAAC;IAC9B,WAAW,GAAG,+CAA+C,CAAC;IAC9D,IAAI,GAAG,SAAS,CAAC;IACjB,kBAAkB,GAAG,2BAAW,CAAC,cAAc,CAAC;IAChD,yBAAyB,GAAG,KAAK,CAAC;IAClC,sBAAsB,GAAG,KAAK,CAAC;IAExC;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY,EACZ,aAA4B,EAC5B,gBAAsC;QAEtC,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,OAAO,EAAE,CAAC,CAAC;YAE7E,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE7E,kCAAkC;YAClC,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;wBACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,6CAA6C,CAAC,CAAC;oBACvG,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sDAAsD,EAAE,WAAW,CAAC,CAAC;gBAC1F,kCAAkC;YACpC,CAAC;YAED,mCAAmC;YACnC,MAAM,MAAM,GAAG;;;yBAGI,OAAO;;;EAG9B,cAAc;;EAEd,aAAa;;;;;;;;;;;CAWd,CAAC;YAEI,oCAAoC;YACpC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAEnE,4CAA4C;YAC5C,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,0BAA0B,OAAO,EAAE,CAAC,CAAC;oBAC1E,MAAM,WAAW,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kDAAkD,EAAE,WAAW,CAAC,CAAC;gBACtF,qCAAqC;YACvC,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YAChF,OAAO,gDAAgD,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAClH,CAAC;IACH,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG,EAAE,+CAA+C;YACjE,SAAS,EAAE,IAAI,EAAG,0CAA0C;YAC5D,aAAa,EAAE,EAAE;YACjB,IAAI,EAAE,UAAU;SACjB,CAAC;IACJ,CAAC;CACF;AAjGD,8CAiGC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { contextManager } from './contextManager';\n\n/**\n * Documentation Mode - Generate comprehensive documentation\n */\nexport class DocumentationMode extends OperationMode {\n  readonly id = 'documentation';\n  readonly displayName = 'Documentation';\n  readonly description = 'Generate comprehensive documentation for code';\n  readonly icon = '$(book)';\n  readonly defaultContextType = ContextType.SELECTED_FILES;\n  readonly requiresHumanVerification = false;\n  readonly supportsMultipleAgents = false;\n\n  /**\n     * Process a user message in Documentation mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    contextSource: ContextSource,\n    additionalParams?: Record<string, any>\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Documentation mode: ${message}`);\n\n      // Get context content\n      const contextContent = await contextManager.getContextContent(contextSource);\n\n      // Add memory context if available\n      let memoryContext = '';\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          const relevantMemories = await agentMemory.getRelevantMemories(message);\n          if (relevantMemories && relevantMemories.length > 0) {\n            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to documentation context`);\n          }\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to retrieve memory context for documentation:', memoryError);\n        // Continue without memory context\n      }\n\n      // Prepare the documentation prompt\n      const prompt = `\nYou are a technical documentation specialist. Generate comprehensive documentation for the following code.\n\nDocumentation Request: ${message}\n\nCode to Document:\n${contextContent}\n\n${memoryContext}\n\nPlease provide:\n1. Overview and purpose\n2. API documentation with parameters and return values\n3. Usage examples\n4. Implementation details\n5. Best practices and considerations\n6. Related components or dependencies\n\nFormat the documentation in clear, professional markdown with proper headings and code examples.\n`;\n\n      // Generate response using the agent\n      const response = await agent.generate(prompt, this.getLLMParams());\n\n      // Store the documentation session in memory\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          await agentMemory.addMessage('user', `Documentation request: ${message}`);\n          await agentMemory.addMessage('assistant', response);\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to store documentation session in memory:', memoryError);\n        // Continue without storing in memory\n      }\n\n      return response;\n    } catch (error) {\n      Logger.instance.error('Error processing message in Documentation mode:', error);\n      return `Error processing your documentation request: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Get LLM parameters specific to Documentation mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.2, // Low temperature for consistent documentation\n      maxTokens: 2500,  // High token limit for comprehensive docs\n      stopSequences: [],\n      mode: 'generate'\n    };\n  }\n}\n"]}