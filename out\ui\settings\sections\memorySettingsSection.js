"use strict";
// Memory Management section logic and rendering
Object.defineProperty(exports, "__esModule", { value: true });
exports.renderMemorySettingsSection = renderMemorySettingsSection;
// Modern memory settings section renderer
function renderMemorySettingsSection(container, settings) {
    const memorySettings = {
        enabled: typeof settings.enabled === 'boolean' ? settings.enabled : true,
        system: settings.system || 'codessa',
        maxMemories: typeof settings.maxMemories === 'number' ? settings.maxMemories : 1000,
        relevanceThreshold: typeof settings.relevanceThreshold === 'number' ? settings.relevanceThreshold : 0.7,
        contextWindowSize: typeof settings.contextWindowSize === 'number' ? settings.contextWindowSize : 5,
        conversationHistorySize: typeof settings.conversationHistorySize === 'number' ? settings.conversationHistorySize : 100,
        vectorStore: settings.vectorStore || 'chroma',
        vectorStoreSettings: settings.vectorStoreSettings || {
            chroma: { directory: '', collectionName: '' },
            pinecone: { apiKey: '', environment: '', indexName: '' }
        },
        database: settings.database || 'sqlite',
        databaseSettings: settings.databaseSettings || {
            sqlite: { filename: '' },
            mysql: { host: '', port: 3306, user: '', password: '', database: '', table: '' },
            postgres: { connectionString: '', schema: '' },
            mongodb: { connectionString: '', database: '', collection: '' },
            redis: { url: '', keyPrefix: '' }
        },
        fileChunking: settings.fileChunking || { chunkSize: 1000, chunkOverlap: 200, maxChunksPerFile: 100 }
    };
    container.innerHTML = `
        <style>
            .mem-section { max-width:520px; margin:0 auto; padding:20px 0; }
            .mem-title { font-size:1.18em; font-weight:600; margin-bottom:14px; color:#222; }
            .mem-group { margin-bottom:20px; padding:14px 16px; background:#f8fafc; border-radius:8px; }
            .mem-label { font-weight:500; display:block; margin-bottom:5px; color:#374151; }
            .mem-btn-row { display:flex; gap:14px; margin-top:22px; }
            .mem-btn { padding:7px 22px; border-radius:6px; border:none; font-weight:500; font-size:1em; cursor:pointer; }
            .mem-btn.save { background:#2563eb; color:#fff; }
            .mem-btn.reset { background:#f3f4f6; color:#222; border:1px solid #e5e7eb; }
            .mem-btn.clear { background:#fff0f0; color:#b91c1c; border:1px solid #e5e7eb; margin-left:auto; }
        </style>
        <div class="mem-section" role="region" aria-labelledby="mem-title">
            <div class="mem-title" id="mem-title">Memory Settings</div>
            <div class="mem-group">
                <label class="mem-label" for="memoryEnabled">Enable Memory
                    <input id="memoryEnabled" type="checkbox" ${memorySettings.enabled ? 'checked' : ''} />
                </label>
                <label class="mem-label" for="memorySystem">Memory System
                    <select id="memorySystem">
                        <option value="codessa" ${memorySettings.system === 'codessa' ? 'selected' : ''}>Codessa</option>
                        <option value="basic" ${memorySettings.system === 'basic' ? 'selected' : ''}>Basic</option>
                    </select>
                </label>
            </div>
            <div class="mem-group">
                <label class="mem-label" for="maxMemories">Max Memories
                    <input id="maxMemories" type="number" min="1" value="${memorySettings.maxMemories}" />
                </label>
                <label class="mem-label" for="relevanceThreshold">Relevance Threshold
                    <input id="relevanceThreshold" type="number" min="0" max="1" step="0.01" value="${memorySettings.relevanceThreshold}" />
                </label>
                <label class="mem-label" for="contextWindowSize">Context Window Size
                    <input id="contextWindowSize" type="number" min="1" value="${memorySettings.contextWindowSize}" />
                </label>
                <label class="mem-label" for="conversationHistorySize">Conversation History Size
                    <input id="conversationHistorySize" type="number" min="1" value="${memorySettings.conversationHistorySize}" />
                </label>
            </div>
            <div class="mem-group">
                <label class="mem-label" for="vectorStore">Vector Store
                    <select id="vectorStore">
                        <option value="memory" ${memorySettings.vectorStore === 'memory' ? 'selected' : ''}>In-Memory</option>
                        <option value="chroma" ${memorySettings.vectorStore === 'chroma' ? 'selected' : ''}>Chroma</option>
                        <option value="pinecone" ${memorySettings.vectorStore === 'pinecone' ? 'selected' : ''}>Pinecone</option>
                        <option value="weaviate" ${memorySettings.vectorStore === 'weaviate' ? 'selected' : ''}>Weaviate</option>
                        <option value="hnswlib" ${memorySettings.vectorStore === 'hnswlib' ? 'selected' : ''}>HNSWLib</option>
                    </select>
                </label>
                <div id="vectorStoreSettingsPanel"></div>
            </div>
            <div class="mem-group">
                <label class="mem-label" for="database">Database
                    <select id="database">
                        <option value="sqlite" ${memorySettings.database === 'sqlite' ? 'selected' : ''}>SQLite</option>
                        <option value="mysql" ${memorySettings.database === 'mysql' ? 'selected' : ''}>MySQL</option>
                        <option value="postgres" ${memorySettings.database === 'postgres' ? 'selected' : ''}>Postgres</option>
                        <option value="mongodb" ${memorySettings.database === 'mongodb' ? 'selected' : ''}>MongoDB</option>
                        <option value="redis" ${memorySettings.database === 'redis' ? 'selected' : ''}>Redis</option>
                    </select>
                </label>
                <div id="databaseSettingsPanel"></div>
            </div>
            <div class="mem-group">
                <label class="mem-label">File Chunking</label>
                <label>Chunk Size <input id="chunkSize" type="number" min="100" value="${memorySettings.fileChunking.chunkSize}" /></label>
                <label>Chunk Overlap <input id="chunkOverlap" type="number" min="0" value="${memorySettings.fileChunking.chunkOverlap}" /></label>
                <label>Max Chunks Per File <input id="maxChunksPerFile" type="number" min="1" value="${memorySettings.fileChunking.maxChunksPerFile}" /></label>
            </div>
            <div class="mem-btn-row">
                <button class="mem-btn save" id="saveMemorySettingsBtn" type="button">Save</button>
                <button class="mem-btn reset" id="resetMemorySettingsBtn" type="button">Reset</button>
                <button class="mem-btn clear" id="clearAllMemoriesBtn" type="button">Clear All Memories</button>
            </div>
        </div>
    `;
    // Advanced settings panels
    renderVectorStoreSettingsPanel(container, memorySettings);
    renderDatabaseSettingsPanel(container, memorySettings);
    // Add listeners for all fields
    [
        'memoryEnabled', 'memorySystem', 'maxMemories', 'relevanceThreshold', 'contextWindowSize', 'conversationHistorySize',
        'vectorStore', 'database', 'chunkSize', 'chunkOverlap', 'maxChunksPerFile'
    ].forEach((id) => {
        const el = container.querySelector(`#${id}`);
        if (el)
            el.addEventListener('input', () => updateMemorySettingsFromUI(settings, memorySettings));
        if (el && el.tagName === 'SELECT')
            el.addEventListener('change', () => updateMemorySettingsFromUI(settings, memorySettings));
    });
    // Save and Reset buttons
    const saveBtn = container.querySelector('#saveMemorySettingsBtn');
    if (saveBtn)
        saveBtn.addEventListener('click', () => {
            updateMemorySettingsFromUI(settings, memorySettings);
            if (typeof acquireVsCodeApi === 'function') {
                const msg = { command: 'saveMemorySettings', settings: memorySettings };
                console.log('Saving memory settings:', msg);
                acquireVsCodeApi().postMessage(msg);
            }
        });
    const resetBtn = container.querySelector('#resetMemorySettingsBtn');
    if (resetBtn)
        resetBtn.addEventListener('click', () => {
            renderMemorySettingsSection(container, settings); // Re-render from settings
        });
    // Clear all memories button
    const clearBtn = container.querySelector('#clearAllMemoriesBtn');
    if (clearBtn)
        clearBtn.addEventListener('click', () => {
            if (confirm('Clear all memories? This cannot be undone.')) {
                if (typeof acquireVsCodeApi === 'function') {
                    acquireVsCodeApi().postMessage({ command: 'clearAllMemories' });
                }
            }
        });
    // Advanced panel listeners
    function renderVectorStoreSettingsPanel(container, memorySettings) {
        const panel = container.querySelector('#vectorStoreSettingsPanel');
        if (!panel)
            return;
        let html = '';
        if (memorySettings.vectorStore === 'chroma') {
            html = `
                <label>Directory <input id="chromaDirectory" type="text" value="${memorySettings.vectorStoreSettings.chroma.directory}" /></label>
                <label>Collection Name <input id="chromaCollectionName" type="text" value="${memorySettings.vectorStoreSettings.chroma.collectionName}" /></label>
            `;
        }
        else if (memorySettings.vectorStore === 'pinecone') {
            html = `
                <label>API Key <input id="pineconeApiKey" type="text" value="${memorySettings.vectorStoreSettings.pinecone.apiKey}" /></label>
                <label>Environment <input id="pineconeEnvironment" type="text" value="${memorySettings.vectorStoreSettings.pinecone.environment}" /></label>
                <label>Index Name <input id="pineconeIndexName" type="text" value="${memorySettings.vectorStoreSettings.pinecone.indexName}" /></label>
            `;
        }
        else {
            html = '<div style="color:#888;">No additional settings for this vector store.</div>';
        }
        panel.innerHTML = html;
        ['chromaDirectory', 'chromaCollectionName', 'pineconeApiKey', 'pineconeEnvironment', 'pineconeIndexName'].forEach(id => {
            const el = panel.querySelector(`#${id}`);
            if (el)
                el.addEventListener('input', () => updateMemorySettingsFromUI(settings, memorySettings));
        });
    }
    function renderDatabaseSettingsPanel(container, memorySettings) {
        const panel = container.querySelector('#databaseSettingsPanel');
        if (!panel)
            return;
        let html = '';
        if (memorySettings.database === 'sqlite') {
            html = `<label>Filename <input id="sqliteFilename" type="text" value="${memorySettings.databaseSettings.sqlite.filename}" /></label>`;
        }
        else if (memorySettings.database === 'mysql') {
            html = `
                <label>Host <input id="mysqlHost" type="text" value="${memorySettings.databaseSettings.mysql.host}" /></label>
                <label>Port <input id="mysqlPort" type="number" value="${memorySettings.databaseSettings.mysql.port}" /></label>
                <label>User <input id="mysqlUser" type="text" value="${memorySettings.databaseSettings.mysql.user}" /></label>
                <label>Password <input id="mysqlPassword" type="password" value="${memorySettings.databaseSettings.mysql.password}" /></label>
                <label>Database <input id="mysqlDatabase" type="text" value="${memorySettings.databaseSettings.mysql.database}" /></label>
                <label>Table <input id="mysqlTable" type="text" value="${memorySettings.databaseSettings.mysql.table}" /></label>
            `;
        }
        else if (memorySettings.database === 'postgres') {
            html = `
                <label>Connection String <input id="postgresConnectionString" type="text" value="${memorySettings.databaseSettings.postgres.connectionString}" /></label>
                <label>Schema <input id="postgresSchema" type="text" value="${memorySettings.databaseSettings.postgres.schema}" /></label>
            `;
        }
        else if (memorySettings.database === 'mongodb') {
            html = `
                <label>Connection String <input id="mongodbConnectionString" type="text" value="${memorySettings.databaseSettings.mongodb.connectionString}" /></label>
                <label>Database <input id="mongodbDatabase" type="text" value="${memorySettings.databaseSettings.mongodb.database}" /></label>
                <label>Collection <input id="mongodbCollection" type="text" value="${memorySettings.databaseSettings.mongodb.collection}" /></label>
            `;
        }
        else if (memorySettings.database === 'redis') {
            html = `
                <label>URL <input id="redisUrl" type="text" value="${memorySettings.databaseSettings.redis.url}" /></label>
                <label>Key Prefix <input id="redisKeyPrefix" type="text" value="${memorySettings.databaseSettings.redis.keyPrefix}" /></label>
            `;
        }
        else {
            html = '<div style="color:#888;">No additional settings for this database.</div>';
        }
        panel.innerHTML = html;
        ['sqliteFilename', 'mysqlHost', 'mysqlPort', 'mysqlUser', 'mysqlPassword', 'mysqlDatabase', 'mysqlTable', 'postgresConnectionString', 'postgresSchema', 'mongodbConnectionString', 'mongodbDatabase', 'mongodbCollection', 'redisUrl', 'redisKeyPrefix'].forEach(id => {
            const el = panel.querySelector(`#${id}`);
            if (el)
                el.addEventListener('input', () => updateMemorySettingsFromUI(settings, memorySettings));
        });
    }
    // Re-render advanced settings panels on dropdown change
    const vectorStoreSelect = container.querySelector('#vectorStore');
    if (vectorStoreSelect)
        vectorStoreSelect.addEventListener('change', () => renderVectorStoreSettingsPanel(container, memorySettings));
    const databaseSelect = container.querySelector('#database');
    if (databaseSelect)
        databaseSelect.addEventListener('change', () => renderDatabaseSettingsPanel(container, memorySettings));
}
function updateMemorySettingsFromUI(settings, memorySettings) {
    const enabledInput = document.getElementById('memoryEnabled');
    const systemInput = document.getElementById('memorySystem');
    const maxMemoriesInput = document.getElementById('maxMemories');
    const relevanceThresholdInput = document.getElementById('relevanceThreshold');
    const contextWindowSizeInput = document.getElementById('contextWindowSize');
    const conversationHistorySizeInput = document.getElementById('conversationHistorySize');
    const vectorStoreInput = document.getElementById('vectorStore');
    const databaseInput = document.getElementById('database');
    const chunkSizeInput = document.getElementById('chunkSize');
    const chunkOverlapInput = document.getElementById('chunkOverlap');
    const maxChunksPerFileInput = document.getElementById('maxChunksPerFile');
    // Advanced panels
    const chromaDirectoryInput = document.getElementById('chromaDirectory');
    const chromaCollectionNameInput = document.getElementById('chromaCollectionName');
    const pineconeApiKeyInput = document.getElementById('pineconeApiKey');
    const pineconeEnvironmentInput = document.getElementById('pineconeEnvironment');
    const pineconeIndexNameInput = document.getElementById('pineconeIndexName');
    const sqliteFilenameInput = document.getElementById('sqliteFilename');
    const mysqlHostInput = document.getElementById('mysqlHost');
    const mysqlPortInput = document.getElementById('mysqlPort');
    const mysqlUserInput = document.getElementById('mysqlUser');
    const mysqlPasswordInput = document.getElementById('mysqlPassword');
    const mysqlDatabaseInput = document.getElementById('mysqlDatabase');
    const mysqlTableInput = document.getElementById('mysqlTable');
    const postgresConnectionStringInput = document.getElementById('postgresConnectionString');
    const postgresSchemaInput = document.getElementById('postgresSchema');
    const mongodbConnectionStringInput = document.getElementById('mongodbConnectionString');
    const mongodbDatabaseInput = document.getElementById('mongodbDatabase');
    const mongodbCollectionInput = document.getElementById('mongodbCollection');
    const redisUrlInput = document.getElementById('redisUrl');
    const redisKeyPrefixInput = document.getElementById('redisKeyPrefix');
    memorySettings = {
        enabled: !!enabledInput?.checked,
        system: systemInput?.value || 'codessa',
        maxMemories: parseInt(maxMemoriesInput?.value || '1000', 10),
        relevanceThreshold: parseFloat(relevanceThresholdInput?.value || '0.7'),
        contextWindowSize: parseInt(contextWindowSizeInput?.value || '5', 10),
        conversationHistorySize: parseInt(conversationHistorySizeInput?.value || '100', 10),
        vectorStore: vectorStoreInput?.value || 'chroma',
        vectorStoreSettings: {
            chroma: {
                directory: chromaDirectoryInput?.value || '',
                collectionName: chromaCollectionNameInput?.value || ''
            },
            pinecone: {
                apiKey: pineconeApiKeyInput?.value || '',
                environment: pineconeEnvironmentInput?.value || '',
                indexName: pineconeIndexNameInput?.value || ''
            }
        },
        database: databaseInput?.value || 'sqlite',
        databaseSettings: {
            sqlite: { filename: sqliteFilenameInput?.value || '' },
            mysql: {
                host: mysqlHostInput?.value || '',
                port: parseInt(mysqlPortInput?.value || '3306', 10),
                user: mysqlUserInput?.value || '',
                password: mysqlPasswordInput?.value || '',
                database: mysqlDatabaseInput?.value || '',
                table: mysqlTableInput?.value || ''
            },
            postgres: {
                connectionString: postgresConnectionStringInput?.value || '',
                schema: postgresSchemaInput?.value || ''
            },
            mongodb: {
                connectionString: mongodbConnectionStringInput?.value || '',
                database: mongodbDatabaseInput?.value || '',
                collection: mongodbCollectionInput?.value || ''
            },
            redis: {
                url: redisUrlInput?.value || '',
                keyPrefix: redisKeyPrefixInput?.value || ''
            }
        },
        fileChunking: {
            chunkSize: parseInt(chunkSizeInput?.value || '1000', 10),
            chunkOverlap: parseInt(chunkOverlapInput?.value || '200', 10),
            maxChunksPerFile: parseInt(maxChunksPerFileInput?.value || '100', 10)
        }
    };
    // Sync back to main settings object
    settings.enabled = memorySettings.enabled;
    settings.system = memorySettings.system;
    settings.maxMemories = memorySettings.maxMemories;
    settings.relevanceThreshold = memorySettings.relevanceThreshold;
    settings.contextWindowSize = memorySettings.contextWindowSize;
    settings.conversationHistorySize = memorySettings.conversationHistorySize;
    settings.vectorStore = memorySettings.vectorStore;
    settings.vectorStoreSettings = memorySettings.vectorStoreSettings;
    settings.database = memorySettings.database;
    settings.databaseSettings = memorySettings.databaseSettings;
    settings.fileChunking = memorySettings.fileChunking;
}
//# sourceMappingURL=memorySettingsSection.js.map