:root {
    --container-padding: 20px;
    --input-padding-vertical: 6px;
    --input-padding-horizontal: 4px;
    --input-margin-vertical: 4px;
    --input-margin-horizontal: 0;
}

body {
    padding: 0;
    margin: 0;
    color: var(--vscode-foreground);
    font-size: var(--vscode-font-size);
    font-weight: var(--vscode-font-weight);
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
}

.container {
    padding: var(--container-padding);
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.header h1 {
    margin: 0;
    padding: 0;
    font-size: 1.5em;
}

.actions {
    display: flex;
    gap: 10px;
}

button {
    padding: 8px 12px;
    border: none;
    cursor: pointer;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    border-radius: 2px;
}

button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.primary-button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.secondary-button {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.workflow-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.workflow-item {
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid var(--vscode-button-background);
}

.workflow-item.codessa {
    border-left: 4px solid var(--vscode-statusBarItem-prominentBackground);
}

.workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.workflow-title {
    font-size: 1.2em;
    font-weight: bold;
}

.workflow-actions {
    display: flex;
    gap: 8px;
}

.workflow-description {
    margin-bottom: 10px;
    color: var(--vscode-descriptionForeground);
}

.workflow-details {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: var(--vscode-descriptionForeground);
}

.empty-state p {
    margin: 5px 0;
}

.run-workflow {
    background-color: var(--vscode-button-background);
}

.edit-workflow {
    background-color: var(--vscode-statusBarItem-warningBackground);
}

.delete-workflow {
    background-color: var(--vscode-statusBarItem-errorBackground);
}

/* Revolutionary Workflow Enhancements */
.goddess-blessing {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    animation: goddessGlow 4s ease-in-out infinite;
    padding: 10px 15px;
    border-radius: 8px;
    margin: 10px 0;
    text-align: center;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.blessing-text {
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    margin: 0;
    font-style: italic;
}

@keyframes goddessGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.revolutionary-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.revolutionary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.goddess-button {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    transition: all 0.3s ease;
}

.goddess-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.quantum-button {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
    transition: all 0.3s ease;
}

.quantum-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 205, 196, 0.6);
}

.revolutionary-features-panel {
    background: var(--vscode-editor-inactiveSelectionBackground);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border: 2px solid var(--vscode-button-background);
}

.revolutionary-features-panel h3 {
    margin: 0 0 15px 0;
    color: var(--vscode-foreground);
    text-align: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.feature-card {
    background: var(--vscode-editor-background);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.feature-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.feature-card.goddess-mode {
    border-color: #ff6b6b;
}

.feature-card.goddess-mode:hover {
    border-color: #ff6b6b;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.feature-card.quantum-analysis {
    border-color: #4ecdc4;
}

.feature-card.quantum-analysis:hover {
    border-color: #4ecdc4;
    box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
}

.feature-card.neural-synthesis {
    border-color: #667eea;
}

.feature-card.neural-synthesis:hover {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.feature-card.time-travel {
    border-color: #feca57;
}

.feature-card.time-travel:hover {
    border-color: #feca57;
    box-shadow: 0 8px 25px rgba(254, 202, 87, 0.3);
}

.feature-icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.feature-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--vscode-foreground);
}

.feature-desc {
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
    line-height: 1.4;
}

.workflow-item.revolutionary {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    position: relative;
    overflow: hidden;
}

.workflow-item.revolutionary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.revolutionary-features-badge {
    display: inline-flex;
    gap: 5px;
    margin: 5px 0;
}

.feature-badge {
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

.feature-badge.goddess {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
}

.feature-badge.quantum {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.feature-badge.neural {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.feature-badge.timetravel {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
}

.workflow-metrics {
    display: flex;
    gap: 15px;
    margin-top: 10px;
    font-size: 0.85em;
}

.metric {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--vscode-descriptionForeground);
}

.metric-value {
    font-weight: bold;
    color: var(--vscode-foreground);
}

.goddess-rating {
    color: #ff6b6b;
}

.success-rate {
    color: #4ecdc4;
}

.execution-count {
    color: #667eea;
}
