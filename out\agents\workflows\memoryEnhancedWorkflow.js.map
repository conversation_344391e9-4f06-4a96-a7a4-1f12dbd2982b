{"version": 3, "file": "memoryEnhancedWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/memoryEnhancedWorkflow.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAkBH,oEAuFC;AAvGD,+BAAoC;AAIpC,yCAAsC;AACtC,iDAA2C;AAE3C;;;;;;;;GAQG;AACH,SAAgB,4BAA4B,CAC1C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,KAAY,EACZ,QAAiB,EAAE;IAEnB,OAAO;QACL,EAAE,EAAE,EAAE,IAAI,4BAA4B,IAAA,SAAM,GAAE,EAAE;QAChD,IAAI,EAAE,IAAI,IAAI,0BAA0B;QACxC,WAAW,EAAE,WAAW,IAAI,0DAA0D;QACtF,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,QAAQ;QAEvB,eAAe;QACf,KAAK,EAAE;YACL;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,OAAO;gBACd,WAAW,EAAE,gCAAgC;aAC9C;YACD;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,cAAc;gBACrB,WAAW,EAAE,gCAAgC;gBAC7C,KAAK,EAAE,KAAK;gBACZ,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;oBAC1D,IAAI,CAAC;wBACH,eAAM,CAAC,IAAI,CAAC,uCAAuC,IAAI,EAAE,CAAC,CAAC;wBAE3D,mCAAmC;wBACnC,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC;4BAC7B,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK;4BAC1B,IAAI,EAAE,MAAM;yBACb,EAAE;4BACD,KAAK,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;yBACnD,CAAC,CAAC;wBAEH,OAAO;4BACL,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,wBAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC7D,OAAO,EAAE;gCACP,MAAM,EAAE,MAAM;gCACd,QAAQ,EAAE;oCACR,UAAU,EAAE,EAAE;oCACd,YAAY,EAAE,IAAI;oCAClB,WAAW,EAAE,MAAM,CAAC,WAAW;iCAChC;6BACF;yBACF,CAAC;oBACJ,CAAC;oBAAC,OAAO,KAAU,EAAE,CAAC;wBACpB,eAAM,CAAC,KAAK,CAAC,6CAA6C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC3E,MAAM,KAAK,CAAC;oBACd,CAAC;gBACH,CAAC;aACF;YACD;gBACE,EAAE,EAAE,KAAK;gBACT,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,KAAK;gBACZ,WAAW,EAAE,2BAA2B;aACzC;SACF;QAED,eAAe;QACf,KAAK,EAAE;YACL;gBACE,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,OAAO;gBACf,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,gBAAgB;aACvB;YACD;gBACE,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,cAAc;aACrB;SACF;QAED,oBAAoB;QACpB,WAAW,EAAE,OAAO;KACrB,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Memory Enhanced Workflow\n * This file provides a workflow that enhances agents with memory capabilities\n */\n\nimport { v4 as uuidv4 } from 'uuid';\nimport { Agent } from '../agentUtilities/agent';\nimport { GraphDefinition, GraphState } from './types';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { logger } from '../../logger';\nimport { AIMessage } from './corePolyfill';\n\n/**\n * Creates a memory-enhanced workflow\n * @param id The workflow ID\n * @param name The workflow name\n * @param description The workflow description\n * @param agent The agent to use\n * @param tools The tools to use\n * @returns A workflow instance\n */\nexport function createMemoryEnhancedWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  agent: Agent,\n  tools: ITool[] = []\n): GraphDefinition {\n  return {\n    id: id || `memory-enhanced-workflow-${uuidv4()}`,\n    name: name || 'Memory Enhanced Workflow',\n    description: description || 'A workflow that enhances agents with memory capabilities',\n    version: '1.0.0',\n    operationMode: 'memory',\n\n    // Define nodes\n    nodes: [\n      {\n        id: 'start',\n        type: 'input',\n        name: 'Start',\n        label: 'Start',\n        description: 'Starting point of the workflow'\n      },\n      {\n        id: 'agent',\n        type: 'agent',\n        name: 'Memory Agent',\n        label: 'Memory Agent',\n        description: 'Agent with memory capabilities',\n        agent: agent,\n        execute: async (state: any): Promise<Partial<GraphState>> => {\n          try {\n            logger.info(`Executing memory-enhanced workflow: ${name}`);\n\n            // Execute the agent with the input\n            const result = await agent.run({\n              prompt: state.inputs.query,\n              mode: 'chat'\n            }, {\n              tools: new Map(tools.map(tool => [tool.id, tool]))\n            });\n\n            return {\n              messages: result.output ? [new AIMessage(result.output)] : [],\n              outputs: {\n                result: result,\n                metadata: {\n                  workflowId: id,\n                  workflowName: name,\n                  toolResults: result.toolResults\n                }\n              }\n            };\n          } catch (error: any) {\n            logger.error(`Error executing memory-enhanced workflow: ${error.message}`);\n            throw error;\n          }\n        }\n      },\n      {\n        id: 'end',\n        type: 'output',\n        name: 'End',\n        label: 'End',\n        description: 'End point of the workflow'\n      }\n    ],\n\n    // Define edges\n    edges: [\n      {\n        source: 'start',\n        target: 'agent',\n        type: 'default',\n        name: 'Start to Agent'\n      },\n      {\n        source: 'agent',\n        target: 'end',\n        type: 'success',\n        name: 'Agent to End'\n      }\n    ],\n\n    // Define start node\n    startNodeId: 'start'\n  };\n}\n"]}