import * as vscode from 'vscode';
import { OperationMode, ContextSource, ContextType } from './operationMode';
import { Agent } from '../agentUtilities/agent';
import { LLMGenerateParams } from '../../llm/types';
import { Logger } from '../../logger';
import { contextManager } from './contextManager';

/**
 * Research Mode - Comprehensive research and analysis
 */
export class ResearchMode extends OperationMode {
  readonly id = 'research';
  readonly displayName = 'Research';
  readonly description = 'Comprehensive research and analysis of topics';
  readonly icon = '$(search)';
  readonly defaultContextType = ContextType.ENTIRE_CODEBASE;
  readonly requiresHumanVerification = false;
  readonly supportsMultipleAgents = true;

  /**
     * Process a user message in Research mode
     */
  async processMessage(
    message: string,
    agent: Agent,
    contextSource: ContextSource,
    additionalParams?: Record<string, any>
  ): Promise<string> {
    try {
      Logger.instance.info(`Processing message in Research mode: ${message}`);

      // Get context content
      const contextContent = await contextManager.getContextContent(contextSource);

      // Add memory context if available
      let memoryContext = '';
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          const relevantMemories = await agentMemory.getRelevantMemories(message);
          if (relevantMemories && relevantMemories.length > 0) {
            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to research context`);
          }
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to retrieve memory context for research:', memoryError);
        // Continue without memory context
      }

      // Prepare the research prompt
      const prompt = `
You are a research assistant conducting comprehensive analysis.

Research Topic: ${message}

Available Context:
${contextContent}

${memoryContext}

Please provide:
1. Comprehensive analysis of the topic
2. Key findings and insights
3. Relevant code patterns or implementations
4. Best practices and recommendations
5. Additional resources or references

Focus on providing thorough, well-researched information with practical insights.
`;

      // Generate response using the agent
      const response = await agent.generate(prompt, this.getLLMParams());

      // Store the research session in memory
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          await agentMemory.addMessage('user', `Research topic: ${message}`);
          await agentMemory.addMessage('assistant', response);
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to store research session in memory:', memoryError);
        // Continue without storing in memory
      }

      return response;
    } catch (error) {
      Logger.instance.error('Error processing message in Research mode:', error);
      return `Error processing your research request: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
     * Get LLM parameters specific to Research mode
     */
  getLLMParams(): LLMGenerateParams {
    return {
      prompt: '',
      modelId: '',
      temperature: 0.3, // Lower temperature for more focused research
      maxTokens: 2000,  // Higher token limit for comprehensive responses
      stopSequences: [],
      mode: 'generate'
    };
  }
}
