{"version": 3, "file": "enhancedTerminalActions.js", "sourceRoot": "", "sources": ["../../src/tools/enhancedTerminalActions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,6DAAqF;AAcrF,MAAa,uBAAuB;IACf,QAAQ,CAA6B;IACrC,iBAAiB,CAA6B;IAC9C,cAAc,GAA2B,EAAE,CAAC;IAC5C,cAAc,GAAmC,IAAI,GAAG,EAAE,CAAC;IACpE,UAAU,GAAY,IAAI,CAAC;IAEnC,YAAY,OAAmC;QAC3C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,sCAAiB,CAAC,WAAW,CAAC,UAAU,CAA+B;eACzF,IAAI,+CAA0B,EAAE,CAAC;QAExC,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAEO,WAAW;QACf,iDAAiD;QACjD,MAAM,QAAQ,GAAG;YACb,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvG,CAAC;QAEF,gCAAgC;QAChC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAE/C,yBAAyB;QACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,oBAAoB;QACxB,8CAA8C;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YACnD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YACxC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAClD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAAc;QAC/C,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAErE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,iCAAiC;gBACjC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC9B,MAAM,QAAQ,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE,CAAC;oBAC1D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBAEH,0CAA0C;gBAC1C,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,SAAS,OAAO,CAAC,MAAM,wCAAwC,EAC/D,kBAAkB,EAClB,gBAAgB,EAChB,QAAQ,CACX,CAAC;gBAEF,QAAQ,WAAW,EAAE,CAAC;oBAClB,KAAK,kBAAkB;wBACnB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAClC,MAAM;oBACV,KAAK,gBAAgB;wBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;wBACrC,MAAM;gBACd,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,KAAY;QACrC,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QAEjD,oCAAoC;QACpC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAC/C,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;QAEF,MAAM,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,6BAA6B;YACtC,KAAK,EAAE,wBAAwB;YAC/B,SAAS,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;SAC7B,CAAC;QAEF,MAAM,QAAQ,GAAG,kBAAkB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE1C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC1B,4BAA4B,KAAK,CAAC,OAAO,EAAE,EAC3C,UAAU,CACb,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;gBAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAChC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,MAAc;QACzD,IAAI,CAAC;YACD,IAAI,cAAc,GAAG,EAAE,CAAC;YAExB,gEAAgE;YAChE,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAElC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACvB,6CAA6C;gBAC7C,cAAc,GAAG,iBAAiB,OAAO,qBAAqB,OAAO,EAAE,CAAC;YAC5E,CAAC;iBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC/B,uBAAuB;gBACvB,cAAc,GAAG,gBAAgB,OAAO,EAAE,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACJ,uCAAuC;gBACvC,cAAc,GAAG,0CAA0C,OAAO,2BAA2B,OAAO,yBAAyB,OAAO,EAAE,CAAC;YAC3I,CAAC;YAED,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE5C,MAAM,MAAM,GAAyB;gBACjC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,WAAW,OAAO,EAAE,CAAC;gBACnD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,cAAc,OAAO,KAAK;aACrC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,OAAO,KAAK,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC;QAC7E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,WAAmB;QAC9C,IAAI,CAAC;YACD,6BAA6B;YAC7B,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvE,sDAAsD;gBACtD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBAC9D,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;YAC5D,CAAC;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3C,mCAAmC;gBACnC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAChE,CAAC;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3C,0CAA0C;gBAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,qDAAqD,CAAC,CAAC;YACvF,CAAC;iBAAM,CAAC;gBACJ,sCAAsC;gBACtC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;gBACvD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;gBACrE,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,MAAM,GAAyB;gBACjC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC;gBAClD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,2BAA2B;aACtC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QACjD,IAAI,CAAC;YACD,wBAAwB;YACxB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;YAEjF,qCAAqC;YACrC,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9D,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;YAC9E,CAAC;YAED,MAAM,MAAM,GAAyB;gBACjC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC;gBACrD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,yBAAyB;aACpC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QACjD,IAAI,CAAC;YACD,qCAAqC;YACrC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,0BAA0B;YAC5E,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,WAAW;YAE/D,uBAAuB;YACvB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YAEnD,wDAAwD;YACxD,IAAI,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;YAC1F,CAAC;YAED,MAAM,MAAM,GAAyB;gBACjC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC;gBACrD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,kDAAkD;aAC7D,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,YAAoB;QACnD,IAAI,CAAC;YACD,+BAA+B;YAC/B,IAAI,YAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,sCAAsC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAChF,IAAI,OAAO,EAAE,CAAC;oBACV,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;oBAChD,OAAO;gBACX,CAAC;YACL,CAAC;YAED,2BAA2B;YAC3B,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;QAC3E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QAEzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;YACtE,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjC,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,WAAW;YAC9C,MAAM,EAAE,MAAM;SACjB,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;YACtD,WAAW,EAAE,sCAAsC;SACtD,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAA4B;QACvD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEjC,4CAA4C;QAC5C,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAAyB;QAChD,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,MAAM,EAAE,IAAW,CAAC,CAAC;YAEhF,MAAM,MAAM,GAAyB;gBACjC,MAAM;gBACN,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,wBAAwB;aAC7E,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjC,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,gCAAgC,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAEM,gBAAgB;QACnB,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC;IAEM,kBAAkB;QACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAEM,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEM,gBAAgB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;CACJ;AAhVD,0DAgVC;AAWD,8CAA8C;AAC9C,MAAM,mBAAmB,GAAI,MAAc,CAAC,0BAA0B,CAAC;AAEvE,IAAI,mBAAmB,EAAE,CAAC;IACtB,MAAM,aAAa,GAAG,mBAAmB,CAAC;IAE1C,0CAA0C;IAC1C,aAAa,CAAC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAChD,aAAa,CAAC,SAAS,CAAC,kBAAkB,GAAG;QACzC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC,CAAC;IAEF,aAAa,CAAC,SAAS,CAAC,iBAAiB,GAAG;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1C,OAAO,CAAC,MAAM,EAAE,CAAC;IACrB,CAAC,CAAC;IAEF,aAAa,CAAC,SAAS,CAAC,kBAAkB,GAAG;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1C,OAAO,CAAC,OAAO,EAAE,CAAC;IACtB,CAAC,CAAC;AACN,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\r\nimport { EnhancedInteractiveSession } from './pseudoTerminal';\r\nimport { codeActionManager, TerminalCodeActionProvider } from './codeActionProvider';\r\n\r\n/**\r\n * Enhanced Terminal Actions Integration\r\n * Provides comprehensive Code Action support within terminal sessions\r\n */\r\n\r\nexport interface TerminalActionResult {\r\n    action: vscode.CodeAction;\r\n    applied: boolean;\r\n    timestamp: Date;\r\n    output: string;\r\n}\r\n\r\nexport class EnhancedTerminalActions {\r\n    private readonly _session: EnhancedInteractiveSession;\r\n    private readonly _terminalProvider: TerminalCodeActionProvider;\r\n    private readonly _actionHistory: TerminalActionResult[] = [];\r\n    private readonly _activeActions: Map<string, vscode.CodeAction> = new Map();\r\n    private _isEnabled: boolean = true;\r\n\r\n    constructor(session: EnhancedInteractiveSession) {\r\n        this._session = session;\r\n        this._terminalProvider = codeActionManager.getProvider('terminal') as TerminalCodeActionProvider\r\n            || new TerminalCodeActionProvider();\r\n\r\n        this._initialize();\r\n    }\r\n\r\n    private _initialize(): void {\r\n        // Register command handlers for terminal actions\r\n        const commands = [\r\n            vscode.commands.registerCommand('codessa.installCommand', this._installCommand.bind(this)),\r\n            vscode.commands.registerCommand('codessa.fixPackageError', this._fixPackageError.bind(this)),\r\n            vscode.commands.registerCommand('codessa.fixPermissionError', this._fixPermissionError.bind(this)),\r\n            vscode.commands.registerCommand('codessa.fixConnectionError', this._fixConnectionError.bind(this)),\r\n            vscode.commands.registerCommand('codessa.runTerminalQuickFix', this._runTerminalQuickFix.bind(this)),\r\n            vscode.commands.registerCommand('codessa.showTerminalActions', this._showTerminalActions.bind(this))\r\n        ];\r\n\r\n        // Store disposables for cleanup\r\n        this._session['disposables'].push(...commands);\r\n\r\n        // Set up event listeners\r\n        this._setupEventListeners();\r\n    }\r\n\r\n    private _setupEventListeners(): void {\r\n        // Listen for terminal output to detect errors\r\n        const outputListener = this._session.onData((output) => {\r\n            if (this._isEnabled) {\r\n                this._analyzeTerminalOutput(output);\r\n            }\r\n        });\r\n\r\n        const errorListener = this._session.onError((error) => {\r\n            this._handleTerminalError(error);\r\n        });\r\n\r\n        this._session['disposables'].push(outputListener, errorListener);\r\n    }\r\n\r\n    private async _analyzeTerminalOutput(output: string): Promise<void> {\r\n        try {\r\n            const actions = this._terminalProvider.analyzeTerminalOutput(output);\r\n\r\n            if (actions.length > 0) {\r\n                // Register actions for later use\r\n                actions.forEach((action, index) => {\r\n                    const actionId = `terminal-action-${Date.now()}-${index}`;\r\n                    this._activeActions.set(actionId, action);\r\n                });\r\n\r\n                // Show notification with quick fix option\r\n                const showActions = await vscode.window.showInformationMessage(\r\n                    `Found ${actions.length} potential fix(es) for terminal output`,\r\n                    'Show Quick Fixes',\r\n                    'Apply Best Fix',\r\n                    'Ignore'\r\n                );\r\n\r\n                switch (showActions) {\r\n                    case 'Show Quick Fixes':\r\n                        await this._showTerminalActions();\r\n                        break;\r\n                    case 'Apply Best Fix':\r\n                        await this._applyBestAction(actions);\r\n                        break;\r\n                }\r\n            }\r\n        } catch (error) {\r\n            console.error('Error analyzing terminal output:', error);\r\n        }\r\n    }\r\n\r\n    private _handleTerminalError(error: Error): void {\r\n        console.error('Terminal error detected:', error);\r\n\r\n        // Create a generic error fix action\r\n        const action = new vscode.CodeAction(\r\n            `$(error) Fix terminal error: ${error.message}`,\r\n            vscode.CodeActionKind.QuickFix\r\n        );\r\n\r\n        action.command = {\r\n            command: 'codessa.runTerminalQuickFix',\r\n            title: 'Run terminal error fix',\r\n            arguments: [error.message]\r\n        };\r\n\r\n        const actionId = `terminal-error-${Date.now()}`;\r\n        this._activeActions.set(actionId, action);\r\n\r\n        vscode.window.showErrorMessage(\r\n            `Terminal error detected: ${error.message}`,\r\n            'Show Fix'\r\n        ).then(selection => {\r\n            if (selection === 'Show Fix') {\r\n                this._showTerminalActions();\r\n            }\r\n        });\r\n    }\r\n\r\n    private async _installCommand(command: string, source: string): Promise<void> {\r\n        try {\r\n            let installCommand = '';\r\n\r\n            // Determine the appropriate install command based on the system\r\n            const platform = process.platform;\r\n\r\n            if (platform === 'win32') {\r\n                // Windows - try Chocolatey first, then scoop\r\n                installCommand = `choco install ${command} || scoop install ${command}`;\r\n            } else if (platform === 'darwin') {\r\n                // macOS - try Homebrew\r\n                installCommand = `brew install ${command}`;\r\n            } else {\r\n                // Linux - try apt, then yum, then snap\r\n                installCommand = `sudo apt update && sudo apt install -y ${command} || sudo yum install -y ${command} || sudo snap install ${command}`;\r\n            }\r\n\r\n            await this._session.writeLn(installCommand);\r\n\r\n            const result: TerminalActionResult = {\r\n                action: new vscode.CodeAction(`Install ${command}`),\r\n                applied: true,\r\n                timestamp: new Date(),\r\n                output: `Installing ${command}...`\r\n            };\r\n\r\n            this._actionHistory.push(result);\r\n\r\n            vscode.window.showInformationMessage(`Installing ${command}...`);\r\n        } catch (error) {\r\n            vscode.window.showErrorMessage(`Failed to install ${command}: ${error}`);\r\n        }\r\n    }\r\n\r\n    private async _fixPackageError(errorOutput: string): Promise<void> {\r\n        try {\r\n            // Common package error fixes\r\n            if (errorOutput.includes('EACCES') || errorOutput.includes('permission')) {\r\n                // Permission error - try with sudo or fix permissions\r\n                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm');\r\n                await this._session.writeLn('sudo chmod -R 755 ~/.npm');\r\n            } else if (errorOutput.includes('ENOTFOUND')) {\r\n                // Network error - check connection\r\n                await this._session.writeLn('ping -c 4 registry.npmjs.org');\r\n            } else if (errorOutput.includes('ETIMEDOUT')) {\r\n                // Timeout - retry with different registry\r\n                await this._session.writeLn('npm config set registry https://registry.npmjs.org/');\r\n            } else {\r\n                // Generic fix - clear cache and retry\r\n                await this._session.writeLn('npm cache clean --force');\r\n                await this._session.writeLn('rm -rf node_modules package-lock.json');\r\n                await this._session.writeLn('npm install');\r\n            }\r\n\r\n            const result: TerminalActionResult = {\r\n                action: new vscode.CodeAction('Fix package error'),\r\n                applied: true,\r\n                timestamp: new Date(),\r\n                output: 'Applied package error fix'\r\n            };\r\n\r\n            this._actionHistory.push(result);\r\n        } catch (error) {\r\n            vscode.window.showErrorMessage(`Failed to fix package error: ${error}`);\r\n        }\r\n    }\r\n\r\n    private async _fixPermissionError(errorOutput: string): Promise<void> {\r\n        try {\r\n            // Fix permission issues\r\n            await this._session.writeLn('chmod +x $(pwd)/*');\r\n            await this._session.writeLn('find . -type f -name \"*.sh\" -exec chmod +x {} \\\\;');\r\n\r\n            // If it's a Node.js permission issue\r\n            if (errorOutput.includes('node') || errorOutput.includes('npm')) {\r\n                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm ~/.node-gyp');\r\n            }\r\n\r\n            const result: TerminalActionResult = {\r\n                action: new vscode.CodeAction('Fix permission error'),\r\n                applied: true,\r\n                timestamp: new Date(),\r\n                output: 'Fixed permission issues'\r\n            };\r\n\r\n            this._actionHistory.push(result);\r\n\r\n            vscode.window.showInformationMessage('Fixed permission issues');\r\n        } catch (error) {\r\n            vscode.window.showErrorMessage(`Failed to fix permissions: ${error}`);\r\n        }\r\n    }\r\n\r\n    private async _fixConnectionError(errorOutput: string): Promise<void> {\r\n        try {\r\n            // Diagnose and fix connection issues\r\n            await this._session.writeLn('ping -c 4 8.8.8.8'); // Test basic connectivity\r\n            await this._session.writeLn('nslookup google.com'); // Test DNS\r\n\r\n            // Check proxy settings\r\n            await this._session.writeLn('env | grep -i proxy');\r\n\r\n            // If it's a specific service, try alternative endpoints\r\n            if (errorOutput.includes('registry.npmjs.org')) {\r\n                await this._session.writeLn('npm config set registry https://registry.npmmirror.com');\r\n            }\r\n\r\n            const result: TerminalActionResult = {\r\n                action: new vscode.CodeAction('Fix connection error'),\r\n                applied: true,\r\n                timestamp: new Date(),\r\n                output: 'Diagnosed and attempted to fix connection issues'\r\n            };\r\n\r\n            this._actionHistory.push(result);\r\n\r\n            vscode.window.showInformationMessage('Diagnosed connection issues');\r\n        } catch (error) {\r\n            vscode.window.showErrorMessage(`Failed to fix connection: ${error}`);\r\n        }\r\n    }\r\n\r\n    private async _runTerminalQuickFix(errorMessage: string): Promise<void> {\r\n        try {\r\n            // Generic terminal error fixes\r\n            if (errorMessage.includes('command not found')) {\r\n                const command = errorMessage.match(/command not found:?\\s+(.+?)(?:\\s|$)/i)?.[1];\r\n                if (command) {\r\n                    await this._installCommand(command, 'terminal');\r\n                    return;\r\n                }\r\n            }\r\n\r\n            // Run a generic diagnostic\r\n            await this._session.writeLn('echo \"Terminal diagnostic:\"');\r\n            await this._session.writeLn('pwd && ls -la');\r\n            await this._session.writeLn('echo $PATH');\r\n            await this._session.writeLn('which bash');\r\n\r\n            vscode.window.showInformationMessage('Ran terminal diagnostic');\r\n        } catch (error) {\r\n            vscode.window.showErrorMessage(`Failed to run terminal fix: ${error}`);\r\n        }\r\n    }\r\n\r\n    private async _showTerminalActions(): Promise<void> {\r\n        const actions = Array.from(this._activeActions.values());\r\n\r\n        if (actions.length === 0) {\r\n            vscode.window.showInformationMessage('No terminal actions available');\r\n            return;\r\n        }\r\n\r\n        const items = actions.map(action => ({\r\n            label: action.title,\r\n            description: action.kind?.value || 'Quick Fix',\r\n            action: action\r\n        }));\r\n\r\n        const selected = await vscode.window.showQuickPick(items, {\r\n            placeHolder: 'Select a terminal quick fix to apply'\r\n        });\r\n\r\n        if (selected) {\r\n            await this._applyAction(selected.action);\r\n        }\r\n    }\r\n\r\n    private async _applyBestAction(actions: vscode.CodeAction[]): Promise<void> {\r\n        if (actions.length === 0) return;\r\n\r\n        // Apply the first (highest priority) action\r\n        await this._applyAction(actions[0]);\r\n    }\r\n\r\n    private async _applyAction(action: vscode.CodeAction): Promise<void> {\r\n        try {\r\n            const success = await this._terminalProvider.applyQuickFix(action, null as any);\r\n\r\n            const result: TerminalActionResult = {\r\n                action,\r\n                applied: success,\r\n                timestamp: new Date(),\r\n                output: success ? 'Action applied successfully' : 'Failed to apply action'\r\n            };\r\n\r\n            this._actionHistory.push(result);\r\n\r\n            if (success) {\r\n                vscode.window.showInformationMessage('Quick fix applied successfully');\r\n            } else {\r\n                vscode.window.showErrorMessage('Failed to apply quick fix');\r\n            }\r\n        } catch (error) {\r\n            vscode.window.showErrorMessage(`Error applying action: ${error}`);\r\n        }\r\n    }\r\n\r\n    public getActionHistory(): TerminalActionResult[] {\r\n        return [...this._actionHistory];\r\n    }\r\n\r\n    public clearActionHistory(): void {\r\n        this._actionHistory.length = 0;\r\n    }\r\n\r\n    public enable(): void {\r\n        this._isEnabled = true;\r\n    }\r\n\r\n    public disable(): void {\r\n        this._isEnabled = false;\r\n    }\r\n\r\n    public isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    public getActiveActions(): vscode.CodeAction[] {\r\n        return Array.from(this._activeActions.values());\r\n    }\r\n\r\n    public dispose(): void {\r\n        this._activeActions.clear();\r\n        this._actionHistory.length = 0;\r\n    }\r\n}\r\n\r\n// Integration with Enhanced Interactive Session\r\ndeclare module './pseudoTerminal' {\r\n    interface EnhancedInteractiveSession {\r\n        getTerminalActions(): EnhancedTerminalActions;\r\n        enableCodeActions(): void;\r\n        disableCodeActions(): void;\r\n    }\r\n}\r\n\r\n// Extend the EnhancedInteractiveSession class\r\nconst originalConstructor = (global as any).EnhancedInteractiveSession;\r\n\r\nif (originalConstructor) {\r\n    const OriginalClass = originalConstructor;\r\n\r\n    // Monkey patch to add Code Action support\r\n    OriginalClass.prototype._terminalActions = null;\r\n    OriginalClass.prototype.getTerminalActions = function() {\r\n        if (!this._terminalActions) {\r\n            this._terminalActions = new EnhancedTerminalActions(this);\r\n        }\r\n        return this._terminalActions;\r\n    };\r\n\r\n    OriginalClass.prototype.enableCodeActions = function() {\r\n        const actions = this.getTerminalActions();\r\n        actions.enable();\r\n    };\r\n\r\n    OriginalClass.prototype.disableCodeActions = function() {\r\n        const actions = this.getTerminalActions();\r\n        actions.disable();\r\n    };\r\n}\r\n"]}