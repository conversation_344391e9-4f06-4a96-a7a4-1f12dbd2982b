{"version": 3, "file": "fileChangeTracker.js", "sourceRoot": "", "sources": ["../../../src/ui/chat/fileChangeTracker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,yCAAsC;AACtC,qDAAyD;AACzD,2CAA6B;AAC7B,uCAAyB;AAEzB,MAAM,MAAM,GAAG,eAAM,CAAC,QAAQ,CAAC;AAgD/B;;;GAGG;AACH,MAAa,iBAAiB;IACpB,MAAM,CAAC,QAAQ,GAA6B,IAAI,CAAC;IACjD,SAAS,GAAgC,IAAI,CAAC;IAC9C,WAAW,GAAoC,IAAI,CAAC;IACpD,cAAc,GAA4B,IAAI,GAAG,EAAE,CAAC;IACpD,SAAS,GAAG,KAAK,CAAC;IAClB,OAAO,CAA0B;IACjC,SAAS,GAAwB,IAAI,GAAG,EAAE,CAAC;IAC3C,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;IACjD,aAAa,GAA8B,IAAI,GAAG,EAAE,CAAC;IACrD,YAAY,GAAgB,IAAI,GAAG,EAAE,CAAC;IACtC,iBAAiB,GAAG,IAAI,CAAC;IACzB,iBAAiB,GAAG,KAAK,CAAC;IAC1B,YAAY,GAAG,IAAI,CAAC;IAE5B,YAAoB,OAAgC;QAClD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,WAAW,CAAC,OAAiC;QACzD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI,OAAO,EAAE,CAAC;YAC3C,iBAAiB,CAAC,QAAQ,GAAG,IAAI,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;QAC7F,CAAC;QACD,OAAO,iBAAiB,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAEzF,4BAA4B;QAC5B,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE;YACnC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE;YACnC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE;YACnC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAChD,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAC9B,IAAI,CAAC,sCAAsC;SAC5C,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,yBAAyB,CAAC;QACnD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,GAAe,EAAE,IAAwC;QACtF,IAAI,CAAC;YACH,2BAA2B;YAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9C,OAAO;YACT,CAAC;YAED,0CAA0C;YAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAC1D,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC;gBACxC,OAAO;YACT,CAAC;YAED,gCAAgC;YAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBACjD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACvC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,OAAO;YACT,CAAC;YAED,uCAAuC;YACvC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAE1D,6BAA6B;YAC7B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAErE,0BAA0B;YAC1B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;YAE9D,4CAA4C;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAEvE,uDAAuD;YACvD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAE1D,gCAAgC;YAChC,MAAM,MAAM,GAAe;gBACzB,GAAG;gBACH,IAAI,EAAE,SAAS,CAAC,UAAU,IAAI,IAAI;gBAClC,YAAY;gBACZ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS;gBACT,SAAS,EAAE,SAAS,CAAC,MAAM;gBAC3B,eAAe;gBACf,YAAY;gBACZ,YAAY,EAAE,SAAS;gBACvB,IAAI;gBACJ,QAAQ;aACT,CAAC;YAEF,qCAAqC;YACrC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAE5C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC5C,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,uDAAuD;YACvD,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;gBAC5B,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,iCAAiC,YAAY,KAAK,IAAI,iBAAiB,QAAQ,EAAE,CAAC,CAAC;QAClG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,YAAoB;QAC3C,MAAM,cAAc,GAAG;YACrB,eAAe;YACf,OAAO;YACP,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;YACV,OAAO;YACP,OAAO;YACP,WAAW;YACX,WAAW;SACZ,CAAC;QAEF,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACnC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,OAAO,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC1C,CAAC;YACD,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBACvD,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAClC,CAAC;YACD,OAAO,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,GAAe;QAQhD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAClE,IAAI,YAAY,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1C,MAAM,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,IAAI,GAAG,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;oBAEtC,MAAM,UAAU,GAAI,MAAqC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC;oBAEzG,IAAI,UAAU,EAAE,CAAC;wBACf,IAAI,UAAU,GAAkE,UAAU,CAAC;wBAE3F,wCAAwC;wBACxC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;4BAAE,UAAU,GAAG,SAAS,CAAC,CAAC,cAAc;6BAC9D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;4BAAE,UAAU,GAAG,SAAS,CAAC,CAAC,gBAAgB;6BACrE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;4BAAE,UAAU,GAAG,SAAS,CAAC,CAAC,gBAAgB;6BACrE,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG;4BAAE,UAAU,GAAG,YAAY,CAAC,CAAC,aAAa;wBAE5E,OAAO;4BACL,UAAU,EAAE,IAAI;4BAChB,MAAM,EAAE,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,MAAM,CAAC;4BACxD,UAAU;4BACV,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,IAAI,SAAS;4BAC1C,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC;4BAClC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;yBACrC,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAED,oBAAoB;YACpB,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YAE3C,OAAO;gBACL,UAAU,EAAE,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,MAAM,EAAE,YAAY;gBACvD,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,UAAU;gBACtB,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACV,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE,UAAU;gBACtB,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACV,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,GAAe,EAAE,IAAY;QAM3D,IAAI,CAAC;YACH,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACxD,MAAM,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC5D,OAAO;oBACL,SAAS,EAAE,KAAK,CAAC,MAAM;oBACvB,SAAS,EAAE,CAAC;oBACZ,OAAO,EAAE,KAAK,CAAC,MAAM;oBACrB,KAAK,EAAE,CAAC;4BACN,QAAQ,EAAE,CAAC;4BACX,QAAQ,EAAE,CAAC;4BACX,QAAQ,EAAE,CAAC;4BACX,QAAQ,EAAE,KAAK,CAAC,MAAM;4BACtB,MAAM,EAAE,cAAc,KAAK,CAAC,MAAM,KAAK;4BACvC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gCACjC,IAAI,EAAE,UAAmB;gCACzB,aAAa,EAAE,KAAK,GAAG,CAAC;gCACxB,OAAO,EAAE,IAAI;6BACd,CAAC,CAAC;yBACJ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACvB,6CAA6C;gBAC7C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACrC,OAAO;oBACL,SAAS,EAAE,CAAC;oBACZ,SAAS,EAAE,KAAK,CAAC,MAAM;oBACvB,OAAO,EAAE,KAAK,CAAC,MAAM;oBACrB,KAAK,EAAE,CAAC;4BACN,QAAQ,EAAE,CAAC;4BACX,QAAQ,EAAE,KAAK,CAAC,MAAM;4BACtB,QAAQ,EAAE,CAAC;4BACX,QAAQ,EAAE,CAAC;4BACX,MAAM,EAAE,SAAS,KAAK,CAAC,MAAM,UAAU;4BACvC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gCACjC,IAAI,EAAE,UAAmB;gCACzB,aAAa,EAAE,KAAK,GAAG,CAAC;gCACxB,OAAO,EAAE,IAAI;6BACd,CAAC,CAAC;yBACJ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,6CAA6C;YAC7C,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,GAAe,EAAE,SAAuF;QACzI,IAAI,CAAC;YACH,cAAc;YACd,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,MAAM,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtD,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,+CAA+C,CAAC;YAC/F,CAAC;YAED,0BAA0B;YAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;YAE7D,IAAI,UAAU,GAAG,EAAE,CAAC;YAEpB,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;gBACrB,UAAU,GAAG,mEAAmE,CAAC;YACnF,CAAC;iBAAM,IAAI,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;gBACzD,UAAU,GAAG,kDAAkD,CAAC;YAClE,CAAC;iBAAM,IAAI,aAAa,KAAK,KAAK,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;gBAC9D,UAAU,GAAG,0DAA0D,CAAC;YAC1E,CAAC;iBAAM,IAAI,aAAa,KAAK,OAAO,EAAE,CAAC;gBACrC,UAAU,GAAG,uDAAuD,CAAC;YACvE,CAAC;iBAAM,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;gBACnC,UAAU,GAAG,6DAA6D,CAAC;YAC7E,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,oDAAoD,CAAC;YACpE,CAAC;YAED,mBAAmB;YACnB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC/C,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,+CAA+C,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,GAAe;QACjD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,eAAe,GAAG;gBACtB,cAAc;gBACd,SAAS;gBACT,UAAU;gBACV,kCAAkC;aACnC,CAAC;YAEF,OAAO,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,SAAuF,EAAE,YAAoB;QAC3I,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEjD,iBAAiB;QACjB,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;YACrC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC;YACtC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC7B,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACxC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,gBAAgB;QAChB,IAAI,UAAU,GAAG,GAAG;YAChB,aAAa,KAAK,KAAK,IAAI,UAAU,GAAG,EAAE;YAC1C,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACtD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,kBAAkB;QAClB,IAAI,UAAU,GAAG,EAAE,IAAI,aAAa,KAAK,KAAK,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;YAC1E,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,GAAe,EAAE,SAAuF;QACtI,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEjD,iBAAiB;QACjB,IAAI,aAAa,KAAK,KAAK,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;YACvD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,aAAa,KAAK,MAAM,IAAI,aAAa,KAAK,OAAO,EAAE,CAAC;YACjE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,aAAa,KAAK,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtB,CAAC;QAED,kBAAkB;QAClB,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;QAC7D,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,UAAU,GAAG,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,CAAC;QAED,qBAAqB;QACrB,IAAI,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxB,CAAC;aAAM,IAAI,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvB,CAAC;QAED,uBAAuB;QACvB,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QAE7C,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,6BAA6B;QAC7B,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QACzD,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAC5E,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC;QACpE,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1F,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1F,wCAAwC;QACxC,IAAI,UAAU,GAAG,qBAAqB,WAAW,QAAQ,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACxF,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,UAAU,IAAI,eAAe,aAAa,WAAW,CAAC;QACxD,CAAC;QACD,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,UAAU,IAAI,aAAa,aAAa,YAAY,CAAC;QACvD,CAAC;QACD,UAAU,IAAI,MAAM,cAAc,KAAK,cAAc,GAAG,CAAC;QAEzD,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAE7D,kCAAkC;QAClC,IAAI,aAAa,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;YACxF,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;QAChF,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;YAC1F,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;QAClF,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;YAC5F,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe;QAC1B,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;YAChE,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAA2B,EAAE,CAAC;QAEzC,mBAAmB;QACnB,KAAK,CAAC,IAAI,CACR;YACE,KAAK,EAAE,+BAA+B;YACtC,WAAW,EAAE,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,gBAAgB;YACjE,MAAM,EAAE,4BAA4B;SACrC,EACD;YACE,KAAK,EAAE,gCAAgC;YACvC,WAAW,EAAE,eAAe,IAAI,CAAC,cAAc,CAAC,IAAI,gBAAgB;YACpE,MAAM,EAAE,gDAAgD;SACzD,EACD,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,iBAAiB,CAAC,SAAS,EAAE,CACxD,CAAC;QAEF,uBAAuB;QACvB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;YAClD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,CAAC,IAAI,CAAC;gBACT,KAAK,EAAE,GAAG,IAAI,IAAI,MAAM,CAAC,YAAY,EAAE;gBACvC,WAAW,EAAE,MAAM,CAAC,IAAI;gBACxB,MAAM,EAAE,YAAY,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;aAC7D,CAAC,CAAC;QACL,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;YACxD,WAAW,EAAE,4CAA4C;YACzD,kBAAkB,EAAE,IAAI;YACxB,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QAED,iBAAiB;QACjB,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC9B,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,mCAAmC;YACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;YAC/D,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC;YAC/F,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,IAAmE;QACrF,QAAQ,IAAI,EAAE,CAAC;YACf,KAAK,UAAU,CAAC,CAAC,OAAO,SAAS,CAAC;YAClC,KAAK,SAAS,CAAC,CAAC,OAAO,QAAQ,CAAC;YAChC,KAAK,SAAS,CAAC,CAAC,OAAO,WAAW,CAAC;YACnC,KAAK,SAAS,CAAC,CAAC,OAAO,gBAAgB,CAAC;YACxC,KAAK,YAAY,CAAC,CAAC,OAAO,YAAY,CAAC;YACvC,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,SAAiB;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,GAAG,GAAG,SAAS,CAAC;QAE7B,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC;YACjB,OAAO,UAAU,CAAC;QACpB,CAAC;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;YACzC,OAAO,GAAG,OAAO,UAAU,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;YACzC,OAAO,GAAG,KAAK,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,MAAkB;QAC9C,MAAM,OAAO,GAA2B;YACtC;gBACE,KAAK,EAAE,qBAAqB;gBAC5B,WAAW,EAAE,+BAA+B;gBAC5C,MAAM,EAAE,qDAAqD;aAC9D;YACD;gBACE,KAAK,EAAE,uBAAuB;gBAC9B,WAAW,EAAE,2BAA2B;gBACxC,MAAM,EAAE,kCAAkC;aAC3C;YACD;gBACE,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE,8BAA8B;gBAC3C,MAAM,EAAE,qCAAqC;aAC9C;YACD;gBACE,KAAK,EAAE,yBAAyB;gBAChC,WAAW,EAAE,qBAAqB;gBAClC,MAAM,EAAE,wBAAwB;aACjC;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE;YAC1D,WAAW,EAAE,eAAe,MAAM,CAAC,YAAY,EAAE;SAClD,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QAED,QAAQ,IAAI,EAAE,CAAC;YACf,KAAK,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAC1C,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBACnC,MAAM;YACR,KAAK,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAC1C,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBACnC,MAAM;YACR,KAAK,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC;gBAC7C,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;gBACvC,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACjD,MAAM;QACR,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,MAAkB;QAC9C,IAAI,CAAC;YACH,sCAAsC;YACtC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,EAChD,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,EAC/C,MAAM,CAAC,GAAG,EACV,GAAG,MAAM,CAAC,YAAY,iBAAiB,CACxC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,MAAkB;QAC9C,IAAI,CAAC;YACH,gEAAgE;YAChE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,MAAkB;QACjD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,+CAA+C,MAAM,CAAC,YAAY,GAAG,EACrE,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,iBAAiB,CAClB,CAAC;YAEF,IAAI,OAAO,KAAK,iBAAiB,EAAE,CAAC;gBAClC,sCAAsC;gBACtC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEjE,uBAAuB;gBACvB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC9C,IAAI,CAAC,eAAe,EAAE,CAAC;gBAEvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YACvF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,4BAAgB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACxD,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,iBAAiB,EACrD,kBAAkB,CACnB,CAAC;YAEF,IAAI,OAAO,KAAK,kBAAkB,EAAE,CAAC;gBACnC,mDAAmD;gBACnD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;gBAEvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;QAC/D,CAAC;gBAAS,CAAC;YACT,4BAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,4BAAgB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,wCAAwC,IAAI,CAAC,cAAc,CAAC,IAAI,iBAAiB,EACjF,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,qBAAqB,CACtB,CAAC;YAEF,IAAI,OAAO,KAAK,qBAAqB,EAAE,CAAC;gBACtC,mCAAmC;gBACnC,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAC9B;oBACE,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;oBAC9C,KAAK,EAAE,oBAAoB;oBAC3B,WAAW,EAAE,KAAK;iBACnB,EACD,KAAK,EAAE,QAAQ,EAAE,EAAE;oBACjB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;oBACzD,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;oBAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBACxC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;wBAC1B,QAAQ,CAAC,MAAM,CAAC;4BACd,SAAS,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC;4BACxB,OAAO,EAAE,cAAc,MAAM,CAAC,YAAY,KAAK;yBAChD,CAAC,CAAC;wBAEH,IAAI,CAAC;4BACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;wBACnE,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,MAAM,CAAC,KAAK,CAAC,oBAAoB,MAAM,CAAC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;wBAClE,CAAC;oBACH,CAAC;oBAED,4BAA4B;oBAC5B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;oBAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,CAAC,CACF,CAAC;gBAEF,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iCAAiC,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAClE,CAAC;gBAAS,CAAC;YACT,4BAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,4BAAgB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC5C,wBAAwB;YACxB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAE5B,0BAA0B;YAC1B,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAClE,IAAI,YAAY,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1C,MAAM,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAE3C,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,YAAY,EAAE,CAAC;oBACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;oBAEtC,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;wBAC1B,MAAM,MAAM,GAAe;4BACzB,GAAG,EAAE,IAAI,CAAC,GAAG;4BACb,IAAI,EAAE,UAAU,EAAE,yCAAyC;4BAC3D,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC;4BACvD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;yBACtB,CAAC;wBAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBACnD,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC;gBAAS,CAAC;YACT,4BAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAAgB,EAAE,MAAkB;QAC7D,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACvD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE1C,qCAAqC;QACrC,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxB,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,8BAA8B,CAAC,MAAkB;QACvD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC9B,+BAA+B,MAAM,CAAC,YAAY,EAAE,EACpD,cAAc,EACd,SAAS,CACV,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACjB,IAAI,SAAS,KAAK,cAAc,EAAE,CAAC;gBACjC,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,MAAc;QAC7C,QAAQ,MAAM,EAAE,CAAC;YACjB,KAAK,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC;YACvB,KAAK,CAAC,CAAC,CAAC,OAAO,UAAU,CAAC;YAC1B,KAAK,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC;YACzB,KAAK,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC;YACzB,KAAK,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC;YACzB,KAAK,GAAG,CAAC,CAAC,OAAO,YAAY,CAAC;YAC9B,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,GAAe;QACjD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAClE,IAAI,YAAY,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1C,MAAM,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,IAAI,GAAG,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;oBACtD,OAAO,OAAO,IAAI,EAAE,CAAC;gBACvB,CAAC;YACH,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,GAAe;QAM9C,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC/D,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC7D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;YAEzD,wEAAwE;YACxE,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAExC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,SAAS,GAAe,EAAE,CAAC;YAEjC,gCAAgC;YAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClC,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAE5B,IAAI,WAAW,KAAK,SAAS,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;oBACvD,IAAI,WAAW,KAAK,OAAO,EAAE,CAAC;wBAC5B,gBAAgB;wBAChB,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,aAAa,EAAE,CAAC,GAAG,CAAC;4BACpB,OAAO,EAAE,OAAO;yBACjB,CAAC,CAAC;wBACH,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,aAAa,EAAE,CAAC,GAAG,CAAC;4BACpB,OAAO,EAAE,WAAW;yBACrB,CAAC,CAAC;wBACH,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,CAAC;oBACd,CAAC;yBAAM,CAAC;wBACN,eAAe;wBACf,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,SAAS;4BACf,aAAa,EAAE,CAAC,GAAG,CAAC;4BACpB,aAAa,EAAE,CAAC,GAAG,CAAC;4BACpB,OAAO,EAAE,WAAW;yBACrB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;qBAAM,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;oBACrC,aAAa;oBACb,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,UAAU;wBAChB,aAAa,EAAE,CAAC,GAAG,CAAC;wBACpB,OAAO,EAAE,WAAW;qBACrB,CAAC,CAAC;oBACH,SAAS,EAAE,CAAC;gBACd,CAAC;qBAAM,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;oBACjC,eAAe;oBACf,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,UAAU;wBAChB,aAAa,EAAE,CAAC,GAAG,CAAC;wBACpB,OAAO,EAAE,OAAO;qBACjB,CAAC,CAAC;oBACH,SAAS,EAAE,CAAC;gBACd,CAAC;YACH,CAAC;YAED,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,EAAE,SAAS,GAAG,SAAS;gBAC9B,KAAK,EAAE,CAAC;wBACN,QAAQ,EAAE,CAAC;wBACX,QAAQ,EAAE,QAAQ,CAAC,MAAM;wBACzB,QAAQ,EAAE,CAAC;wBACX,QAAQ,EAAE,YAAY,CAAC,MAAM;wBAC7B,MAAM,EAAE,SAAS,QAAQ,CAAC,MAAM,OAAO,YAAY,CAAC,MAAM,KAAK;wBAC/D,KAAK,EAAE,SAAS;qBACjB,CAAC;aACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAqB;QACjD,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAC5E,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC;QACpE,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1F,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1F,IAAI,OAAO,GAAG,GAAG,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;QACnF,OAAO,IAAI,IAAI,cAAc,gBAAgB,cAAc,cAAc,CAAC;QAE1E,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,MAAM,aAAa,mBAAmB,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QACtF,CAAC;QAED,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,MAAM,aAAa,YAAY,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QAC/E,CAAC;QAED,OAAO,IAAI,2BAA2B,CAAC;QACvC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QACD,iBAAiB,CAAC,QAAQ,GAAG,IAAI,CAAC;IACpC,CAAC;;AA/gCH,8CAghCC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from '../../logger';\nimport { statusBarManager } from '../feedback/statusBar';\nimport * as path from 'path';\nimport * as fs from 'fs';\n\nconst logger = Logger.instance;\n\n/**\n * Interface for tracked file changes with advanced diff capabilities\n */\nexport interface FileChange {\n  uri: vscode.Uri;\n  type: 'modified' | 'created' | 'deleted' | 'renamed' | 'conflicted';\n  relativePath: string;\n  timestamp: number;\n  diffStats?: {\n    additions: number;\n    deletions: number;\n    changes: number;\n    hunks: DiffHunk[];\n  };\n  gitStatus?: string;\n  conflictMarkers?: boolean;\n  aiSuggestion?: string;\n  reviewStatus?: 'pending' | 'approved' | 'rejected' | 'needs_review';\n  tags?: string[];\n  priority?: 'low' | 'medium' | 'high' | 'critical';\n}\n\n/**\n * Interface for diff hunks with line-by-line changes\n */\nexport interface DiffHunk {\n  oldStart: number;\n  oldLines: number;\n  newStart: number;\n  newLines: number;\n  header: string;\n  lines: DiffLine[];\n}\n\n/**\n * Interface for individual diff lines\n */\nexport interface DiffLine {\n  type: 'context' | 'addition' | 'deletion';\n  oldLineNumber?: number;\n  newLineNumber?: number;\n  content: string;\n  highlighted?: boolean;\n  aiAnnotation?: string;\n}\n\n/**\n * Advanced File Change Tracker - Professional change tracking with embedded diffs\n * Features comprehensive diff analysis and intelligent change management\n */\nexport class FileChangeTracker {\n  private static instance: FileChangeTracker | null = null;\n  private changeBar: vscode.StatusBarItem | null = null;\n  private fileWatcher: vscode.FileSystemWatcher | null = null;\n  private trackedChanges: Map<string, FileChange> = new Map();\n  private isVisible = false;\n  private context: vscode.ExtensionContext;\n  private diffCache: Map<string, string> = new Map();\n  private aiAnalysisCache: Map<string, string> = new Map();\n  private changeHistory: Map<string, FileChange[]> = new Map();\n  private smartFilters: Set<string> = new Set();\n  private autoReviewEnabled = true;\n  private collaborativeMode = false;\n  private realTimeSync = true;\n\n  private constructor(context: vscode.ExtensionContext) {\n    this.context = context;\n    this.setupFileWatcher();\n    this.setupChangeBar();\n  }\n\n  /**\n   * Get singleton instance\n   */\n  public static getInstance(context?: vscode.ExtensionContext): FileChangeTracker {\n    if (!FileChangeTracker.instance && context) {\n      FileChangeTracker.instance = new FileChangeTracker(context);\n    }\n    if (!FileChangeTracker.instance) {\n      throw new Error('FileChangeTracker not initialized. Call getInstance with context first.');\n    }\n    return FileChangeTracker.instance;\n  }\n\n  /**\n   * Setup file system watcher\n   */\n  private setupFileWatcher(): void {\n    if (!vscode.workspace.workspaceFolders) {\n      return;\n    }\n\n    // Watch for file changes in the workspace\n    this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/*', false, false, false);\n\n    // Handle file modifications\n    this.fileWatcher.onDidChange((uri) => {\n      this.handleFileChange(uri, 'modified');\n    });\n\n    // Handle file creation\n    this.fileWatcher.onDidCreate((uri) => {\n      this.handleFileChange(uri, 'created');\n    });\n\n    // Handle file deletion\n    this.fileWatcher.onDidDelete((uri) => {\n      this.handleFileChange(uri, 'deleted');\n    });\n\n    logger.info('File change watcher initialized');\n  }\n\n  /**\n   * Setup the change tracking status bar\n   */\n  private setupChangeBar(): void {\n    this.changeBar = vscode.window.createStatusBarItem(\n      vscode.StatusBarAlignment.Left,\n      1000 // High priority to appear on the left\n    );\n\n    this.changeBar.command = 'codessa.showFileChanges';\n    this.updateChangeBar();\n  }\n\n  /**\n   * Handle file change events with revolutionary analysis\n   */\n  private async handleFileChange(uri: vscode.Uri, type: 'modified' | 'created' | 'deleted'): Promise<void> {\n    try {\n      // Skip if not in workspace\n      if (!vscode.workspace.getWorkspaceFolder(uri)) {\n        return;\n      }\n\n      // Skip certain file types and directories\n      const relativePath = vscode.workspace.asRelativePath(uri);\n      if (this.shouldIgnoreFile(relativePath)) {\n        return;\n      }\n\n      // Advanced git status detection\n      const gitStatus = await this.getAdvancedGitStatus(uri);\n      if (!gitStatus.isModified && type === 'modified') {\n        this.trackedChanges.delete(uri.fsPath);\n        this.updateChangeBar();\n        return;\n      }\n\n      // Generate comprehensive diff analysis\n      const diffStats = await this.generateDiffStats(uri, type);\n\n      // AI-powered change analysis\n      const aiSuggestion = await this.generateAISuggestion(uri, diffStats);\n\n      // Detect conflict markers\n      const conflictMarkers = await this.detectConflictMarkers(uri);\n\n      // Determine priority based on change impact\n      const priority = this.calculateChangePriority(diffStats, relativePath);\n\n      // Auto-generate tags based on file content and changes\n      const tags = await this.generateSmartTags(uri, diffStats);\n\n      // Create enhanced change object\n      const change: FileChange = {\n        uri,\n        type: gitStatus.changeType || type,\n        relativePath,\n        timestamp: Date.now(),\n        diffStats,\n        gitStatus: gitStatus.status,\n        conflictMarkers,\n        aiSuggestion,\n        reviewStatus: 'pending',\n        tags,\n        priority\n      };\n\n      // Store change history for analytics\n      this.addToChangeHistory(uri.fsPath, change);\n\n      this.trackedChanges.set(uri.fsPath, change);\n      this.updateChangeBar();\n\n      // Trigger real-time notifications for critical changes\n      if (priority === 'critical') {\n        this.showCriticalChangeNotification(change);\n      }\n\n      logger.debug(`Advanced file change tracked: ${relativePath} (${type}) - Priority: ${priority}`);\n    } catch (error) {\n      logger.error('Error handling file change:', error);\n    }\n  }\n\n  /**\n   * Check if file should be ignored\n   */\n  private shouldIgnoreFile(relativePath: string): boolean {\n    const ignorePatterns = [\n      'node_modules/',\n      '.git/',\n      'out/',\n      'dist/',\n      'build/',\n      '.vscode/',\n      '*.log',\n      '*.tmp',\n      '.DS_Store',\n      'Thumbs.db'\n    ];\n\n    return ignorePatterns.some(pattern => {\n      if (pattern.endsWith('/')) {\n        return relativePath.startsWith(pattern);\n      }\n      if (pattern.includes('*')) {\n        const regex = new RegExp(pattern.replace(/\\*/g, '.*'));\n        return regex.test(relativePath);\n      }\n      return relativePath.includes(pattern);\n    });\n  }\n\n  /**\n   * Advanced git status detection with comprehensive analysis\n   */\n  private async getAdvancedGitStatus(uri: vscode.Uri): Promise<{\n    isModified: boolean;\n    status: string;\n    changeType: 'modified' | 'created' | 'deleted' | 'renamed' | 'conflicted';\n    branch: string;\n    ahead: number;\n    behind: number;\n  }> {\n    try {\n      const gitExtension = vscode.extensions.getExtension('vscode.git');\n      if (gitExtension && gitExtension.isActive) {\n        const git = gitExtension.exports.getAPI(1);\n        const repo = git.getRepository(uri);\n        if (repo) {\n          const status = await repo.getStatus();\n          type GitStatusItem = { uri: vscode.Uri; status: number };\n          const fileStatus = (status as unknown as GitStatusItem[]).find((item) => item.uri.fsPath === uri.fsPath);\n\n          if (fileStatus) {\n            let changeType: 'modified' | 'created' | 'deleted' | 'renamed' | 'conflicted' = 'modified';\n\n            // Determine change type from git status\n            if (fileStatus.status === 1) changeType = 'created'; // INDEX_ADDED\n            else if (fileStatus.status === 6) changeType = 'deleted'; // INDEX_DELETED\n            else if (fileStatus.status === 3) changeType = 'renamed'; // INDEX_RENAMED\n            else if (fileStatus.status === 128) changeType = 'conflicted'; // CONFLICTED\n\n            return {\n              isModified: true,\n              status: this.convertGitStatusToString(fileStatus.status),\n              changeType,\n              branch: repo.state.HEAD?.name || 'unknown',\n              ahead: repo.state.HEAD?.ahead || 0,\n              behind: repo.state.HEAD?.behind || 0\n            };\n          }\n        }\n      }\n\n      // Fallback analysis\n      const stats = await fs.promises.stat(uri.fsPath);\n      const now = Date.now();\n      const modifiedTime = stats.mtime.getTime();\n\n      return {\n        isModified: (now - modifiedTime) < 300000, // 5 minutes\n        status: 'modified',\n        changeType: 'modified',\n        branch: 'unknown',\n        ahead: 0,\n        behind: 0\n      };\n    } catch (error) {\n      return {\n        isModified: true,\n        status: 'unknown',\n        changeType: 'modified',\n        branch: 'unknown',\n        ahead: 0,\n        behind: 0\n      };\n    }\n  }\n\n  /**\n   * Generate comprehensive diff statistics with line-by-line analysis\n   */\n  private async generateDiffStats(uri: vscode.Uri, type: string): Promise<{\n    additions: number;\n    deletions: number;\n    changes: number;\n    hunks: DiffHunk[];\n  }> {\n    try {\n      if (type === 'created') {\n        const content = await vscode.workspace.fs.readFile(uri);\n        const lines = new TextDecoder().decode(content).split('\\n');\n        return {\n          additions: lines.length,\n          deletions: 0,\n          changes: lines.length,\n          hunks: [{\n            oldStart: 0,\n            oldLines: 0,\n            newStart: 1,\n            newLines: lines.length,\n            header: `@@ -0,0 +1,${lines.length} @@`,\n            lines: lines.map((line, index) => ({\n              type: 'addition' as const,\n              newLineNumber: index + 1,\n              content: line\n            }))\n          }]\n        };\n      }\n\n      if (type === 'deleted') {\n        // Try to get the last known content from git\n        const gitContent = await this.getFileContentFromGit(uri);\n        const lines = gitContent.split('\\n');\n        return {\n          additions: 0,\n          deletions: lines.length,\n          changes: lines.length,\n          hunks: [{\n            oldStart: 1,\n            oldLines: lines.length,\n            newStart: 0,\n            newLines: 0,\n            header: `@@ -1,${lines.length} +0,0 @@`,\n            lines: lines.map((line, index) => ({\n              type: 'deletion' as const,\n              oldLineNumber: index + 1,\n              content: line\n            }))\n          }]\n        };\n      }\n\n      // For modified files, generate detailed diff\n      return await this.createDetailedDiff(uri);\n    } catch (error) {\n      logger.error('Error generating diff stats:', error);\n      return { additions: 0, deletions: 0, changes: 0, hunks: [] };\n    }\n  }\n\n  /**\n   * Generate AI-powered suggestions for changes\n   */\n  private async generateAISuggestion(uri: vscode.Uri, diffStats: { additions: number; deletions: number; changes: number; hunks: DiffHunk[] }): Promise<string> {\n    try {\n      // Cache check\n      const cacheKey = `${uri.fsPath}-${diffStats.changes}`;\n      if (this.aiAnalysisCache.has(cacheKey)) {\n        return this.aiAnalysisCache.get(cacheKey) ?? '📋 Change detected - Review before committing';\n      }\n\n      // Analyze change patterns\n      const fileExtension = path.extname(uri.fsPath);\n      const changeSize = diffStats.additions + diffStats.deletions;\n\n      let suggestion = '';\n\n      if (changeSize > 100) {\n        suggestion = '🔍 Large change detected - Consider breaking into smaller commits';\n      } else if (diffStats.deletions > diffStats.additions * 2) {\n        suggestion = '🗑️ Major refactoring - Ensure tests are updated';\n      } else if (fileExtension === '.ts' || fileExtension === '.js') {\n        suggestion = '⚡ Code change - Run tests and check for breaking changes';\n      } else if (fileExtension === '.json') {\n        suggestion = '⚙️ Configuration change - Verify settings are correct';\n      } else if (fileExtension === '.md') {\n        suggestion = '📝 Documentation update - Check for spelling and formatting';\n      } else {\n        suggestion = '✨ File modified - Review changes before committing';\n      }\n\n      // Cache the result\n      this.aiAnalysisCache.set(cacheKey, suggestion);\n      return suggestion;\n    } catch (error) {\n      return '📋 Change detected - Review before committing';\n    }\n  }\n\n  /**\n   * Detect conflict markers in files\n   */\n  private async detectConflictMarkers(uri: vscode.Uri): Promise<boolean> {\n    try {\n      const content = await vscode.workspace.fs.readFile(uri);\n      const text = new TextDecoder().decode(content);\n\n      const conflictMarkers = [\n        '<<<<<<< HEAD',\n        '=======',\n        '>>>>>>> ',\n        '|||||||| merged common ancestors'\n      ];\n\n      return conflictMarkers.some(marker => text.includes(marker));\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Calculate change priority based on impact analysis\n   */\n  private calculateChangePriority(diffStats: { additions: number; deletions: number; changes: number; hunks: DiffHunk[] }, relativePath: string): 'low' | 'medium' | 'high' | 'critical' {\n    const changeSize = diffStats.additions + diffStats.deletions;\n    const fileExtension = path.extname(relativePath);\n\n    // Critical files\n    if (relativePath.includes('package.json') ||\n        relativePath.includes('tsconfig.json') ||\n        relativePath.includes('.env') ||\n        relativePath.includes('Dockerfile')) {\n      return 'critical';\n    }\n\n    // High priority\n    if (changeSize > 200 ||\n        fileExtension === '.ts' && changeSize > 50 ||\n        relativePath.includes('src/') && changeSize > 100) {\n      return 'high';\n    }\n\n    // Medium priority\n    if (changeSize > 20 || fileExtension === '.ts' || fileExtension === '.js') {\n      return 'medium';\n    }\n\n    return 'low';\n  }\n\n  /**\n   * Generate smart tags based on file analysis\n   */\n  private async generateSmartTags(uri: vscode.Uri, diffStats: { additions: number; deletions: number; changes: number; hunks: DiffHunk[] }): Promise<string[]> {\n    const tags: string[] = [];\n    const relativePath = vscode.workspace.asRelativePath(uri);\n    const fileExtension = path.extname(relativePath);\n\n    // File type tags\n    if (fileExtension === '.ts' || fileExtension === '.js') {\n      tags.push('code');\n    } else if (fileExtension === '.css' || fileExtension === '.scss') {\n      tags.push('styles');\n    } else if (fileExtension === '.md') {\n      tags.push('docs');\n    } else if (fileExtension === '.json') {\n      tags.push('config');\n    }\n\n    // Size-based tags\n    const changeSize = diffStats.additions + diffStats.deletions;\n    if (changeSize > 100) {\n      tags.push('large-change');\n    } else if (changeSize < 10) {\n      tags.push('small-change');\n    }\n\n    // Pattern-based tags\n    if (diffStats.deletions > diffStats.additions) {\n      tags.push('refactor');\n    } else if (diffStats.additions > diffStats.deletions * 2) {\n      tags.push('feature');\n    }\n\n    // Directory-based tags\n    if (relativePath.includes('test/') || relativePath.includes('spec/')) {\n      tags.push('tests');\n    } else if (relativePath.includes('src/')) {\n      tags.push('source');\n    }\n\n    return tags;\n  }\n\n  /**\n   * Update the change bar display with advanced analytics\n   */\n  private updateChangeBar(): void {\n    if (!this.changeBar) {\n      return;\n    }\n\n    const changeCount = this.trackedChanges.size;\n\n    if (changeCount === 0) {\n      this.hideChangeBar();\n      return;\n    }\n\n    // Calculate advanced metrics\n    const changes = Array.from(this.trackedChanges.values());\n    const criticalCount = changes.filter(c => c.priority === 'critical').length;\n    const conflictCount = changes.filter(c => c.conflictMarkers).length;\n    const totalAdditions = changes.reduce((sum, c) => sum + (c.diffStats?.additions || 0), 0);\n    const totalDeletions = changes.reduce((sum, c) => sum + (c.diffStats?.deletions || 0), 0);\n\n    // Enhanced status bar text with metrics\n    let statusText = `$(source-control) ${changeCount} file${changeCount === 1 ? '' : 's'}`;\n    if (criticalCount > 0) {\n      statusText += ` $(warning) ${criticalCount} critical`;\n    }\n    if (conflictCount > 0) {\n      statusText += ` $(error) ${conflictCount} conflicts`;\n    }\n    statusText += ` (+${totalAdditions}/-${totalDeletions})`;\n\n    this.changeBar.text = statusText;\n    this.changeBar.tooltip = this.createAdvancedTooltip(changes);\n\n    // Dynamic color based on priority\n    if (criticalCount > 0 || conflictCount > 0) {\n      this.changeBar.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');\n      this.changeBar.color = new vscode.ThemeColor('statusBarItem.errorForeground');\n    } else if (changes.some(c => c.priority === 'high')) {\n      this.changeBar.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');\n      this.changeBar.color = new vscode.ThemeColor('statusBarItem.warningForeground');\n    } else {\n      this.changeBar.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');\n      this.changeBar.color = new vscode.ThemeColor('statusBarItem.prominentForeground');\n    }\n\n    this.showChangeBar();\n  }\n\n  /**\n   * Show the change bar\n   */\n  private showChangeBar(): void {\n    if (this.changeBar && !this.isVisible) {\n      this.changeBar.show();\n      this.isVisible = true;\n    }\n  }\n\n  /**\n   * Hide the change bar\n   */\n  private hideChangeBar(): void {\n    if (this.changeBar && this.isVisible) {\n      this.changeBar.hide();\n      this.isVisible = false;\n    }\n  }\n\n  /**\n   * Show file changes quick pick\n   */\n  public async showFileChanges(): Promise<void> {\n    if (this.trackedChanges.size === 0) {\n      vscode.window.showInformationMessage('No file changes to show');\n      return;\n    }\n\n    const items: vscode.QuickPickItem[] = [];\n\n    // Add action items\n    items.push(\n      {\n        label: '$(check-all) Keep All Changes',\n        description: `Keep all ${this.trackedChanges.size} changed files`,\n        detail: 'Accept all current changes'\n      },\n      {\n        label: '$(discard) Discard All Changes',\n        description: `Discard all ${this.trackedChanges.size} changed files`,\n        detail: 'Revert all files to their last committed state'\n      },\n      { label: '', kind: vscode.QuickPickItemKind.Separator }\n    );\n\n    // Add individual files\n    for (const change of this.trackedChanges.values()) {\n      const icon = this.getFileIcon(change.type);\n      items.push({\n        label: `${icon} ${change.relativePath}`,\n        description: change.type,\n        detail: `Modified ${this.formatTimestamp(change.timestamp)}`\n      });\n    }\n\n    const selected = await vscode.window.showQuickPick(items, {\n      placeHolder: 'Select an action or file to manage changes',\n      matchOnDescription: true,\n      matchOnDetail: true\n    });\n\n    if (!selected) {\n      return;\n    }\n\n    // Handle actions\n    if (selected.label.includes('Keep All')) {\n      await this.keepAllChanges();\n    } else if (selected.label.includes('Discard All')) {\n      await this.discardAllChanges();\n    } else {\n      // Handle individual file selection\n      const fileName = selected.label.replace(/^\\$\\([^)]+\\)\\s+/, '');\n      const change = Array.from(this.trackedChanges.values()).find(c => c.relativePath === fileName);\n      if (change) {\n        await this.showFileActions(change);\n      }\n    }\n  }\n\n  /**\n   * Get icon for file change type\n   */\n  private getFileIcon(type: 'modified' | 'created' | 'deleted' | 'renamed' | 'conflicted'): string {\n    switch (type) {\n    case 'modified': return '$(edit)';\n    case 'created': return '$(add)';\n    case 'deleted': return '$(remove)';\n    case 'renamed': return '$(arrow-right)';\n    case 'conflicted': return '$(warning)';\n    default: return '$(file)';\n    }\n  }\n\n  /**\n   * Format timestamp for display\n   */\n  private formatTimestamp(timestamp: number): string {\n    const now = Date.now();\n    const diff = now - timestamp;\n\n    if (diff < 60000) {\n      return 'just now';\n    } else if (diff < 3600000) {\n      const minutes = Math.floor(diff / 60000);\n      return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;\n    } else {\n      const hours = Math.floor(diff / 3600000);\n      return `${hours} hour${hours === 1 ? '' : 's'} ago`;\n    }\n  }\n\n  /**\n   * Show actions for individual file\n   */\n  private async showFileActions(change: FileChange): Promise<void> {\n    const actions: vscode.QuickPickItem[] = [\n      {\n        label: '$(eye) View Changes',\n        description: 'Open diff view to see changes',\n        detail: 'Compare current version with last committed version'\n      },\n      {\n        label: '$(check) Keep Changes',\n        description: 'Keep changes in this file',\n        detail: 'Accept the current modifications'\n      },\n      {\n        label: '$(discard) Discard Changes',\n        description: 'Discard changes in this file',\n        detail: 'Revert file to last committed state'\n      },\n      {\n        label: '$(go-to-file) Open File',\n        description: 'Open file in editor',\n        detail: 'View and edit the file'\n      }\n    ];\n\n    const selected = await vscode.window.showQuickPick(actions, {\n      placeHolder: `Actions for ${change.relativePath}`\n    });\n\n    if (!selected) {\n      return;\n    }\n\n    switch (true) {\n    case selected.label.includes('View Changes'):\n      await this.viewFileChanges(change);\n      break;\n    case selected.label.includes('Keep Changes'):\n      await this.keepFileChanges(change);\n      break;\n    case selected.label.includes('Discard Changes'):\n      await this.discardFileChanges(change);\n      break;\n    case selected.label.includes('Open File'):\n      await vscode.window.showTextDocument(change.uri);\n      break;\n    }\n  }\n\n  /**\n   * View changes for a specific file\n   */\n  private async viewFileChanges(change: FileChange): Promise<void> {\n    try {\n      // Use VS Code's built-in diff command\n      await vscode.commands.executeCommand('vscode.diff',\n        vscode.Uri.parse(`git:${change.uri.path}?HEAD`),\n        change.uri,\n        `${change.relativePath} (Working Tree)`\n      );\n    } catch (error) {\n      logger.error('Error viewing file changes:', error);\n      vscode.window.showErrorMessage(`Failed to view changes for ${change.relativePath}`);\n    }\n  }\n\n  /**\n   * Keep changes for a specific file\n   */\n  private async keepFileChanges(change: FileChange): Promise<void> {\n    try {\n      // For now, just remove from tracking (changes are already kept)\n      this.trackedChanges.delete(change.uri.fsPath);\n      this.updateChangeBar();\n\n      vscode.window.showInformationMessage(`Changes kept for ${change.relativePath}`);\n    } catch (error) {\n      logger.error('Error keeping file changes:', error);\n      vscode.window.showErrorMessage(`Failed to keep changes for ${change.relativePath}`);\n    }\n  }\n\n  /**\n   * Discard changes for a specific file\n   */\n  private async discardFileChanges(change: FileChange): Promise<void> {\n    try {\n      const confirm = await vscode.window.showWarningMessage(\n        `Are you sure you want to discard changes in ${change.relativePath}?`,\n        { modal: true },\n        'Discard Changes'\n      );\n\n      if (confirm === 'Discard Changes') {\n        // Use git checkout to discard changes\n        await vscode.commands.executeCommand('git.checkout', change.uri);\n\n        // Remove from tracking\n        this.trackedChanges.delete(change.uri.fsPath);\n        this.updateChangeBar();\n\n        vscode.window.showInformationMessage(`Changes discarded for ${change.relativePath}`);\n      }\n    } catch (error) {\n      logger.error('Error discarding file changes:', error);\n      vscode.window.showErrorMessage(`Failed to discard changes for ${change.relativePath}`);\n    }\n  }\n\n  /**\n   * Keep all changes\n   */\n  private async keepAllChanges(): Promise<void> {\n    try {\n      statusBarManager.setActivityIndicator(true);\n      const confirm = await vscode.window.showInformationMessage(\n        `Keep all ${this.trackedChanges.size} changed files?`,\n        'Keep All Changes'\n      );\n\n      if (confirm === 'Keep All Changes') {\n        // Clear all tracked changes (they're already kept)\n        this.trackedChanges.clear();\n        this.updateChangeBar();\n\n        vscode.window.showInformationMessage('All changes have been kept');\n      }\n    } catch (error) {\n      logger.error('Error keeping all changes:', error);\n      vscode.window.showErrorMessage('Failed to keep all changes');\n    } finally {\n      statusBarManager.setActivityIndicator(false);\n    }\n  }\n\n  /**\n   * Discard all changes\n   */\n  private async discardAllChanges(): Promise<void> {\n    try {\n      statusBarManager.setActivityIndicator(true);\n      const confirm = await vscode.window.showWarningMessage(\n        `Are you sure you want to discard all ${this.trackedChanges.size} changed files?`,\n        { modal: true },\n        'Discard All Changes'\n      );\n\n      if (confirm === 'Discard All Changes') {\n        // Show progress for multiple files\n        await vscode.window.withProgress(\n          {\n            location: vscode.ProgressLocation.Notification,\n            title: 'Discarding changes',\n            cancellable: false\n          },\n          async (progress) => {\n            const changes = Array.from(this.trackedChanges.values());\n            const total = changes.length;\n\n            for (let i = 0; i < changes.length; i++) {\n              const change = changes[i];\n              progress.report({\n                increment: (100 / total),\n                message: `Discarding ${change.relativePath}...`\n              });\n\n              try {\n                await vscode.commands.executeCommand('git.checkout', change.uri);\n              } catch (error) {\n                logger.error(`Error discarding ${change.relativePath}:`, error);\n              }\n            }\n\n            // Clear all tracked changes\n            this.trackedChanges.clear();\n            this.updateChangeBar();\n          }\n        );\n\n        vscode.window.showInformationMessage('All changes have been discarded');\n      }\n    } catch (error) {\n      logger.error('Error discarding all changes:', error);\n      vscode.window.showErrorMessage('Failed to discard all changes');\n    } finally {\n      statusBarManager.setActivityIndicator(false);\n    }\n  }\n\n  /**\n   * Get current tracked changes\n   */\n  public getTrackedChanges(): FileChange[] {\n    return Array.from(this.trackedChanges.values());\n  }\n\n  /**\n   * Clear all tracked changes\n   */\n  public clearTrackedChanges(): void {\n    this.trackedChanges.clear();\n    this.updateChangeBar();\n  }\n\n  /**\n   * Refresh change tracking\n   */\n  public async refreshChanges(): Promise<void> {\n    try {\n      statusBarManager.setActivityIndicator(true);\n      // Clear current changes\n      this.trackedChanges.clear();\n\n      // Re-scan for git changes\n      const gitExtension = vscode.extensions.getExtension('vscode.git');\n      if (gitExtension && gitExtension.isActive) {\n        const git = gitExtension.exports.getAPI(1);\n\n        for (const repo of git.repositories) {\n          const status = await repo.getStatus();\n\n          for (const item of status) {\n            const change: FileChange = {\n              uri: item.uri,\n              type: 'modified', // Git status provides more detailed info\n              relativePath: vscode.workspace.asRelativePath(item.uri),\n              timestamp: Date.now()\n            };\n\n            this.trackedChanges.set(item.uri.fsPath, change);\n          }\n        }\n      }\n\n      this.updateChangeBar();\n      logger.info('File changes refreshed');\n    } catch (error) {\n      logger.error('Error refreshing changes:', error);\n    } finally {\n      statusBarManager.setActivityIndicator(false);\n    }\n  }\n\n  /**\n   * Add change to history for analytics\n   */\n  private addToChangeHistory(filePath: string, change: FileChange): void {\n    const history = this.changeHistory.get(filePath) ?? [];\n    history.push(change);\n    this.changeHistory.set(filePath, history);\n\n    // Keep only last 10 changes per file\n    if (history.length > 10) {\n      history.shift();\n    }\n  }\n\n  /**\n   * Show critical change notification\n   */\n  private showCriticalChangeNotification(change: FileChange): void {\n    vscode.window.showWarningMessage(\n      `Critical change detected in ${change.relativePath}`,\n      'View Changes',\n      'Dismiss'\n    ).then(selection => {\n      if (selection === 'View Changes') {\n        this.showFileChanges();\n      }\n    });\n  }\n\n  /**\n   * Convert git status to string\n   */\n  private convertGitStatusToString(status: number): string {\n    switch (status) {\n    case 1: return 'added';\n    case 2: return 'modified';\n    case 3: return 'renamed';\n    case 4: return 'copied';\n    case 5: return 'updated';\n    case 6: return 'deleted';\n    case 128: return 'conflicted';\n    default: return 'unknown';\n    }\n  }\n\n  /**\n   * Get file content from git\n   */\n  private async getFileContentFromGit(uri: vscode.Uri): Promise<string> {\n    try {\n      const gitExtension = vscode.extensions.getExtension('vscode.git');\n      if (gitExtension && gitExtension.isActive) {\n        const git = gitExtension.exports.getAPI(1);\n        const repo = git.getRepository(uri);\n        if (repo) {\n          const relativePath = path.relative(repo.rootUri.fsPath, uri.fsPath);\n          const content = await repo.show('HEAD', relativePath);\n          return content || '';\n        }\n      }\n      return '';\n    } catch (error) {\n      return '';\n    }\n  }\n\n  /**\n   * Create detailed diff for modified files\n   */\n  private async createDetailedDiff(uri: vscode.Uri): Promise<{\n    additions: number;\n    deletions: number;\n    changes: number;\n    hunks: DiffHunk[];\n  }> {\n    try {\n      const currentContent = await vscode.workspace.fs.readFile(uri);\n      const currentText = new TextDecoder().decode(currentContent);\n      const gitContent = await this.getFileContentFromGit(uri);\n\n      // Simple diff implementation (in production, use a proper diff library)\n      const currentLines = currentText.split('\\n');\n      const gitLines = gitContent.split('\\n');\n\n      let additions = 0;\n      let deletions = 0;\n      const diffLines: DiffLine[] = [];\n\n      // Basic line-by-line comparison\n      const maxLines = Math.max(currentLines.length, gitLines.length);\n      for (let i = 0; i < maxLines; i++) {\n        const currentLine = currentLines[i];\n        const gitLine = gitLines[i];\n\n        if (currentLine !== undefined && gitLine !== undefined) {\n          if (currentLine !== gitLine) {\n            // Modified line\n            diffLines.push({\n              type: 'deletion',\n              oldLineNumber: i + 1,\n              content: gitLine\n            });\n            diffLines.push({\n              type: 'addition',\n              newLineNumber: i + 1,\n              content: currentLine\n            });\n            additions++;\n            deletions++;\n          } else {\n            // Context line\n            diffLines.push({\n              type: 'context',\n              oldLineNumber: i + 1,\n              newLineNumber: i + 1,\n              content: currentLine\n            });\n          }\n        } else if (currentLine !== undefined) {\n          // Added line\n          diffLines.push({\n            type: 'addition',\n            newLineNumber: i + 1,\n            content: currentLine\n          });\n          additions++;\n        } else if (gitLine !== undefined) {\n          // Deleted line\n          diffLines.push({\n            type: 'deletion',\n            oldLineNumber: i + 1,\n            content: gitLine\n          });\n          deletions++;\n        }\n      }\n\n      return {\n        additions,\n        deletions,\n        changes: additions + deletions,\n        hunks: [{\n          oldStart: 1,\n          oldLines: gitLines.length,\n          newStart: 1,\n          newLines: currentLines.length,\n          header: `@@ -1,${gitLines.length} +1,${currentLines.length} @@`,\n          lines: diffLines\n        }]\n      };\n    } catch (error) {\n      logger.error('Error creating detailed diff:', error);\n      return { additions: 0, deletions: 0, changes: 0, hunks: [] };\n    }\n  }\n\n  /**\n   * Create advanced tooltip with metrics\n   */\n  private createAdvancedTooltip(changes: FileChange[]): string {\n    const criticalCount = changes.filter(c => c.priority === 'critical').length;\n    const conflictCount = changes.filter(c => c.conflictMarkers).length;\n    const totalAdditions = changes.reduce((sum, c) => sum + (c.diffStats?.additions || 0), 0);\n    const totalDeletions = changes.reduce((sum, c) => sum + (c.diffStats?.deletions || 0), 0);\n\n    let tooltip = `${changes.length} file${changes.length === 1 ? '' : 's'} changed\\n`;\n    tooltip += `+${totalAdditions} additions, -${totalDeletions} deletions\\n`;\n\n    if (criticalCount > 0) {\n      tooltip += `⚠️ ${criticalCount} critical change${criticalCount === 1 ? '' : 's'}\\n`;\n    }\n\n    if (conflictCount > 0) {\n      tooltip += `🔥 ${conflictCount} conflict${conflictCount === 1 ? '' : 's'}\\n`;\n    }\n\n    tooltip += '\\nClick to manage changes';\n    return tooltip;\n  }\n\n  /**\n   * Dispose resources\n   */\n  public dispose(): void {\n    if (this.fileWatcher) {\n      this.fileWatcher.dispose();\n    }\n    if (this.changeBar) {\n      this.changeBar.dispose();\n    }\n    FileChangeTracker.instance = null;\n  }\n}\n"]}