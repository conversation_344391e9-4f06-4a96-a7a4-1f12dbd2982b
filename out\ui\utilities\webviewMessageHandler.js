"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebviewMessageHandler = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
class WebviewMessageHandler {
    context;
    constructor(context) {
        this.context = context;
    }
    getWebview(webview) {
        if ('webview' in webview) {
            return webview.webview;
        }
        return webview.webview || webview;
    }
    async handleMessage(message, webview) {
        try {
            switch (message.command) {
                case 'showConfirmDialog':
                    return this.handleConfirmDialog(message);
                case 'clearStorage':
                    return this.handleClearStorage();
                case 'showInformationMessage':
                    vscode.window.showInformationMessage(message.message);
                    return true;
                case 'showErrorMessage':
                    vscode.window.showErrorMessage(message.message);
                    return true;
                case 'reloadWindow':
                    vscode.commands.executeCommand('workbench.action.reloadWindow');
                    return true;
                default:
                    logger_1.logger.warn(`Unknown command: ${message.command}`);
                    return null;
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error(`Error handling message: ${errorMessage}`);
            return { error: errorMessage };
        }
    }
    async handleConfirmDialog(message) {
        const result = await vscode.window.showInformationMessage(message.message, { modal: true }, 'Yes', 'No');
        return { confirmed: result === 'Yes' };
    }
    async handleClearStorage() {
        try {
            // Clear global state
            await this.context.globalState.update('codessa', undefined);
            // Clear workspace state
            await this.context.workspaceState.update('codessa', undefined);
            // Clear all secrets
            const secrets = await this.context.secrets.get('codessa.secrets');
            if (secrets) {
                const secretsObj = JSON.parse(secrets);
                for (const key in secretsObj) {
                    await this.context.secrets.delete(`codessa.secrets.${key}`);
                }
            }
            return { success: true };
        }
        catch (error) {
            logger_1.logger.error('Failed to clear storage:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}
exports.WebviewMessageHandler = WebviewMessageHandler;
//# sourceMappingURL=webviewMessageHandler.js.map