"use strict";
// Providers & Models section logic and rendering
Object.defineProperty(exports, "__esModule", { value: true });
exports.renderProvidersModelsSettingsSection = renderProvidersModelsSettingsSection;
// Utility to ensure parseInt always gets a string
function safeGetString(val) {
    return typeof val === 'string' ? val : '';
}
const themeConfig_1 = require("../themeConfig");
let providerModels = [];
let editingProviderModelIdx = null;
const sectionTheme = themeConfig_1.defaultUIThemeConfig.section;
// List of all known providers (should match those in llmService)
const ALL_PROVIDERS = [
    { id: 'ollama', name: '<PERSON><PERSON><PERSON>' },
    { id: 'openai', name: 'OpenAI' },
    { id: 'anthropic', name: 'Anthropic' },
    { id: 'lmstudio', name: 'LM Studio' },
    { id: 'googleai', name: 'Google AI' },
    { id: 'mistral<PERSON>', name: 'Mistral AI' },
    { id: 'cohere', name: 'Cohere' },
    { id: 'deepseek', name: 'DeepSeek' },
    { id: 'openrouter', name: 'OpenRouter' },
    { id: 'huggingface', name: 'HuggingFace' },
    { id: 'starcoder', name: 'StarCoder' },
    { id: 'codellama', name: 'CodeLlama' },
    { id: 'replit', name: 'Replit' },
    { id: 'wizardcoder', name: 'WizardCoder' },
    { id: 'xwincoder', name: 'XwinCoder' },
    { id: 'phi', name: 'Phi' },
    { id: 'yicode', name: 'YiCode' },
    { id: 'codegemma', name: 'CodeGemma' },
    { id: 'santacoder', name: 'SantaCoder' },
    { id: 'stablecode', name: 'StableCode' },
    { id: 'perplexity', name: 'Perplexity' },
];
function renderProvidersModelsSettingsSection(container, settings) {
    // Sync from settings
    providerModels = Array.isArray(settings.providerModels) ? settings.providerModels : [];
    renderProvidersModelsTable(container);
    // --- Provider toggles UI ---
    const enabledProviders = Array.isArray(settings.enabledProviders) ? settings.enabledProviders : ['ollama'];
    let togglesHtml = '<div style="margin-bottom:1em;"><h3>Enable/Disable LLM Providers</h3>';
    ALL_PROVIDERS.forEach(p => {
        togglesHtml += `
            <label style="display:block;margin-bottom:4px;">
                <input type="checkbox" class="provider-toggle" data-provider-id="${p.id}" ${enabledProviders.includes(p.id) ? 'checked' : ''}>
                ${p.name}
            </label>`;
    });
    togglesHtml += '</div>';
    const section = container.querySelector('#providersModelsSection');
    if (section) {
        section.insertAdjacentHTML('afterbegin', togglesHtml);
        // Add event listeners for toggles
        section.querySelectorAll('.provider-toggle').forEach((el) => {
            el.addEventListener('change', (e) => {
                const id = e.target.getAttribute('data-provider-id');
                let newEnabled = [...enabledProviders];
                if (e.target.checked) {
                    if (!newEnabled.includes(id))
                        newEnabled.push(id);
                }
                else {
                    newEnabled = newEnabled.filter(pid => pid !== id);
                }
                // Send updateSetting message to VS Code extension
                if (window.acquireVsCodeApi) {
                    const vscode = window.acquireVsCodeApi();
                    vscode.postMessage({
                        command: 'updateSetting',
                        section: 'llm',
                        key: 'enabledProviders',
                        value: newEnabled
                    });
                }
                // Also call onChange for backward compatibility
                if (typeof settings.onChange === 'function') {
                    settings.onChange('enabledProviders', newEnabled);
                }
            });
        });
    }
    // Add button listeners
    const addBtn = document.getElementById('addProviderModelBtn');
    if (addBtn)
        addBtn.onclick = () => showProviderModelModal(container, {}, null);
    // Modal buttons
    const cancelBtn = document.getElementById('cancelProviderModelBtn');
    if (cancelBtn)
        cancelBtn.onclick = () => hideProviderModelModal(container);
    const saveBtn = document.getElementById('saveProviderModelBtn');
    if (saveBtn)
        saveBtn.onclick = () => saveProviderModel(container, settings);
}
function renderProvidersModelsTable(container) {
    const section = container.querySelector('#providersModelsSection');
    if (!providerModels || providerModels.length === 0) {
        section.innerHTML = '<div style="color:#aaa;">No providers/models defined.</div>';
        return;
    }
    let html = `<style>
        .crud-table th, .crud-table td { padding: 6px 10px; }
        .crud-table th {
            background: ${sectionTheme.headerBg};
            color: ${sectionTheme.headerColor};
            font-weight: 600;
        }
        .crud-table tbody tr:nth-child(even) { background: #fafbfc; }
        .crud-table tbody tr:hover { background: #e8f0fe; }
        .btn-provider-model {
            background:${sectionTheme.button.background};
            color:${sectionTheme.button.color};
            border:${sectionTheme.button.border};
            border-radius:${sectionTheme.button.borderRadius};
            padding:3px 10px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s;
        }
        .btn-provider-model:hover { background:#1d4ed8; }
    </style>`;
    html += '<table class="crud-table"><thead><tr>' +
        '<th>Provider</th><th>Model ID</th><th>API Key</th><th>Enabled</th><th>Actions</th>' +
        '</tr></thead><tbody>';
    providerModels.forEach((pm, idx) => {
        html += `<tr>
            <td>${pm.provider || ''}</td>
            <td>${pm.modelId || ''}</td>
            <td>${pm.apiKey ? '••••••••' : ''}</td>
            <td>${pm.enabled ? 'Yes' : 'No'}</td>
            <td>
                <button type="button" data-edit="${idx}">Edit</button>
                <button type="button" data-delete="${idx}">Delete</button>
            </td>
        </tr>`;
    });
    html += '</tbody></table>';
    section.innerHTML = html;
    // Attach edit/delete event listeners
    section.querySelectorAll('button[data-edit]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const idxAttr = e.target.getAttribute('data-edit');
            const idx = parseInt(safeGetString(idxAttr));
            showProviderModelModal(container, providerModels[idx], idx);
        });
    });
    section.querySelectorAll('button[data-delete]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const idxAttr = e.target.getAttribute('data-delete');
            const idx = parseInt(safeGetString(idxAttr));
            deleteProviderModel(container, idx);
        });
    });
}
function showProviderModelModal(container, pm, idx) {
    const modal = document.getElementById('providerModelModal');
    const title = document.getElementById('providerModelModalTitle');
    const providerInput = document.getElementById('providerModelProvider');
    const modelIdInput = document.getElementById('providerModelModelId');
    const apiKeyInput = document.getElementById('providerModelApiKey');
    const enabledInput = document.getElementById('providerModelEnabled');
    if (modal)
        modal.style.display = 'flex';
    if (title)
        title.innerText = idx == null ? 'Add Provider/Model' : 'Edit Provider/Model';
    if (providerInput)
        providerInput.value = pm?.provider || '';
    if (modelIdInput)
        modelIdInput.value = pm?.modelId || '';
    if (apiKeyInput)
        apiKeyInput.value = pm?.apiKey || '';
    if (enabledInput)
        enabledInput.checked = !!pm?.enabled;
    editingProviderModelIdx = idx;
}
function hideProviderModelModal(container) {
    const modal = document.getElementById('providerModelModal');
    if (modal)
        modal.style.display = 'none';
    // Clear validation errors in container
    const errors = container.querySelectorAll('.validation-error');
    errors.forEach(error => error.remove());
    editingProviderModelIdx = null;
}
function saveProviderModel(container, settings) {
    const providerInput = document.getElementById('providerModelProvider');
    const modelIdInput = document.getElementById('providerModelModelId');
    const apiKeyInput = document.getElementById('providerModelApiKey');
    const enabledInput = document.getElementById('providerModelEnabled');
    const pm = {
        provider: providerInput?.value || '',
        modelId: modelIdInput?.value || '',
        apiKey: apiKeyInput?.value || '',
        enabled: enabledInput?.checked || false,
    };
    if (editingProviderModelIdx == null) {
        providerModels.push(pm);
    }
    else {
        providerModels[editingProviderModelIdx] = pm;
    }
    settings.providerModels = providerModels;
    hideProviderModelModal(container);
    renderProvidersModelsTable(container);
}
const modal_1 = require("../components/modal");
function deleteProviderModel(container, idx) {
    const pm = providerModels[idx];
    const pmLabel = pm ? `${pm.provider} - ${pm.modelId}` : 'this provider/model';
    (0, modal_1.showModal)({
        title: 'Delete Provider/Model',
        content: `Are you sure you want to remove the provider/model "${pmLabel}"? This cannot be undone.`,
        onConfirm: () => {
            providerModels.splice(idx, 1);
            const settings = window.settings || {};
            settings.providerModels = providerModels;
            renderProvidersModelsTable(container);
        }
    });
}
//# sourceMappingURL=providersModelsSettingsSection.js.map