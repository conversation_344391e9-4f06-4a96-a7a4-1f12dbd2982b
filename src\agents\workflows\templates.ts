/**
 * Codessa workflow templates
 */

import { Tool } from './corePolyfill';
import { z } from 'zod';
import { Agent } from '../agentUtilities/agent';
import { Codessa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge } from './types';
import { workflowRegistry } from './workflowRegistry';
import { Logger } from '../../logger';
import { MemoryVectorStore } from '../../memory/codessa/vectorStores/memoryVectorStore';

// Custom tool implementations
class MemoryRetrievalTool extends Tool {
  name = 'memory-retrieval';
  description = 'Retrieves relevant memories based on the current conversation';
  schema = z.object({
    input: z.string().optional().describe('The query to search for in memories (optional string)')
  }).strip().transform((obj: { input?: string }) => obj.input ?? '');

  constructor(private _vectorStore: MemoryVectorStore) {
    super('memory-retrieval', 'Retrieves relevant memories based on the current conversation');
  }

  protected async _call({ input }: { input?: string }): Promise<string> {
    const query = input || '';
    Logger.instance.info('Retrieving memories for:', query);
    const results: any[] = await this._vectorStore.similaritySearch(query, 5);
    return results.map(r => (r.content ?? r.pageContent ?? '')).join('\n\n');
  }
}

class MemorySaveTool extends Tool {
  name = 'memory-save';
  description = 'Saves important information from the conversation to memory';
  schema = z.object({
    input: z.string().optional().describe('The content to save to memory (optional string)')
  }).strip().transform((obj: { input?: string }) => obj.input ?? '');

  constructor(private _vectorStore: MemoryVectorStore) {
    super('memory-save', 'Saves important information from the conversation to memory');
  }

  protected async _call({ input }: { input?: string }): Promise<string> {
    const content = input || '';
    Logger.instance.info('Saving memory:', content);
    await this._vectorStore.addDocuments([{ pageContent: content, metadata: {} }]);
    return 'Memory saved successfully';
  }
}

/**
 * Create a simple chat workflow
 */
export function createChatWorkflow(
  id: string,
  name: string,
  description: string,
  agent: Agent
): GraphDefinition {
  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const agentNode = Codessa.createAgentNode('agent', 'Agent', agent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-agent', source: 'input', target: 'agent', type: 'default' },
    { name: 'agent-to-output', source: 'agent', target: 'output', type: 'default' }
  ];

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [inputNode, agentNode, outputNode],
    edges,
    startNodeId: 'input',
    operationMode: 'ask'
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a ReAct agent workflow with tool integration
 */
export function createReActWorkflow(
  id: string,
  name: string,
  description: string,
  agent: Agent,
  tools: Tool[]
): GraphDefinition {
  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const agentNode = Codessa.createAgentNode('agent', 'Agent', agent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Create tool nodes
  const toolNodes: GraphNode[] = tools.map((tool, index) => {
    return Codessa.createToolNode(`tool-${index}`, tool.name, tool);
  });

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-agent', source: 'input', target: 'agent', type: 'default' }
  ];

  // Add edges from agent to tools and back
  toolNodes.forEach((toolNode, index) => {
    edges.push({
      name: `agent-to-tool-${index}`,
      source: 'agent',
      target: toolNode.id,
      type: 'default',
      condition: async (state) => {
        const lastOutput = state.outputs['agent'];
        return lastOutput && lastOutput.includes(`Using tool: ${tools[index].name}`);
      }
    });

    edges.push({
      name: `tool-${index}-to-agent`,
      source: toolNode.id,
      target: 'agent',
      type: 'default'
    });
  });

  // Add edge from agent to output
  edges.push({
    name: 'agent-to-output',
    source: 'agent',
    target: 'output',
    type: 'default',
    condition: async (state) => {
      const lastOutput = state.outputs['agent'];
      return lastOutput && lastOutput.includes('Final Answer:');
    }
  });

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [inputNode, agentNode, ...toolNodes, outputNode],
    edges,
    startNodeId: 'input',
    operationMode: 'chat'
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a multi-agent workflow for collaborative problem solving
 */
export function createMultiAgentWorkflow(
  id: string,
  name: string,
  description: string,
  agents: Agent[],
  supervisorAgent: Agent
): GraphDefinition {
  if (agents.length === 0) {
    throw new Error('At least one agent is required');
  }

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const supervisorNode = Codessa.createAgentNode('supervisor', 'Supervisor', supervisorAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Create agent nodes
  const agentNodes: GraphNode[] = agents.map((agent, index) => {
    return Codessa.createAgentNode(`agent-${index}`, agent.name, agent);
  });

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-supervisor', source: 'input', target: 'supervisor', type: 'default' }
  ];

  // Add edges from supervisor to agents
  agentNodes.forEach((agentNode, index) => {
    edges.push({
      name: `supervisor-to-agent-${index}`,
      source: 'supervisor',
      target: agentNode.id,
      type: 'default',
      condition: async (state) => {
        const lastOutput = state.outputs['supervisor'];
        return lastOutput && lastOutput.includes(`Delegating to: ${agents[index].name}`);
      }
    });

    edges.push({
      name: `agent-${index}-to-supervisor`,
      source: agentNode.id,
      target: 'supervisor',
      type: 'default'
    });
  });

  // Add edge from supervisor to output
  edges.push({
    name: 'supervisor-to-output',
    source: 'supervisor',
    target: 'output',
    type: 'default',
    condition: async (state) => {
      const lastOutput = state.outputs['supervisor'];
      return lastOutput && lastOutput.includes('Final Consensus:');
    }
  });

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [inputNode, supervisorNode, ...agentNodes, outputNode],
    edges,
    startNodeId: 'input',
    operationMode: 'multi-agent'
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a memory-enhanced workflow with vector store integration
 */
export function createMemoryEnhancedWorkflow(
  id: string,
  name: string,
  description: string,
  agent: Agent,
  vectorStore: MemoryVectorStore
): GraphDefinition {
  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const memoryRetrievalTool = new MemoryRetrievalTool(vectorStore);
  const memoryRetrievalNode = Codessa.createToolNode(
    'memory-retrieval',
    'Memory Retrieval',
    memoryRetrievalTool
  );
  const agentNode = Codessa.createAgentNode('agent', 'Agent', agent);
  const memorySaveTool = new MemorySaveTool(vectorStore);
  const memorySaveNode = Codessa.createToolNode(
    'memory-save',
    'Memory Save',
    memorySaveTool
  );
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-memory-retrieval', source: 'input', target: 'memory-retrieval', type: 'default' },
    { name: 'memory-retrieval-to-agent', source: 'memory-retrieval', target: 'agent', type: 'default' },
    { name: 'agent-to-memory-save', source: 'agent', target: 'memory-save', type: 'default' },
    { name: 'memory-save-to-output', source: 'memory-save', target: 'output', type: 'default' }
  ];

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [inputNode, memoryRetrievalNode, agentNode, memorySaveNode, outputNode],
    edges,
    startNodeId: 'input',
    operationMode: 'chat'
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a workflow for Test-Driven Development (TDD)
 */
export function createTDDWorkflow(
  id: string,
  name: string,
  description: string,
  implementationAgent: Agent,
  testingAgent: Agent,
  refactoringAgent: Agent
): GraphDefinition {
  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const testWriterNode = Codessa.createAgentNode('test-writer', 'Test Writer', testingAgent);
  const implementerNode = Codessa.createAgentNode('implementer', 'Implementation', implementationAgent);
  const testerNode = Codessa.createAgentNode('tester', 'Test Runner', testingAgent);
  const refactorNode = Codessa.createAgentNode('refactor', 'Refactoring', refactoringAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Create edges for TDD cycle
  const edges: GraphEdge[] = [
    { name: 'input-to-test-writer', source: 'input', target: 'test-writer', type: 'default' },
    { name: 'test-writer-to-implementer', source: 'test-writer', target: 'implementer', type: 'default' },
    { name: 'implementer-to-tester', source: 'implementer', target: 'tester', type: 'default' },
    {
      name: 'tester-to-refactor',
      source: 'tester',
      target: 'refactor',
      type: 'default',
      condition: async (state) => {
        const testOutput = state.outputs['tester'];
        return testOutput && testOutput.includes('Tests passed');
      }
    },
    {
      name: 'tester-to-implementer',
      source: 'tester',
      target: 'implementer',
      type: 'default',
      condition: async (state) => {
        const testOutput = state.outputs['tester'];
        return testOutput && testOutput.includes('Tests failed');
      }
    },
    { name: 'refactor-to-tester', source: 'refactor', target: 'tester', type: 'default' },
    {
      name: 'tester-to-output',
      source: 'tester',
      target: 'output',
      type: 'default',
      condition: async (state) => {
        const testOutput = state.outputs['tester'];
        return testOutput &&
          testOutput.includes('Tests passed') &&
          testOutput.includes('Code quality metrics satisfied');
      }
    }
  ];

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [inputNode, testWriterNode, implementerNode, testerNode, refactorNode, outputNode],
    edges,
    startNodeId: 'input',
    operationMode: 'debugging'
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a workflow for Agile Sprint Planning
 */
export function createSprintPlanningWorkflow(
  id: string,
  name: string,
  description: string,
  productOwnerAgent: Agent,
  techLeadAgent: Agent,
  estimatorAgent: Agent
): GraphDefinition {
  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const backlogGroomingNode = Codessa.createAgentNode('backlog-grooming', 'Backlog Grooming', productOwnerAgent);
  const technicalAnalysisNode = Codessa.createAgentNode('tech-analysis', 'Technical Analysis', techLeadAgent);
  const estimationNode = Codessa.createAgentNode('estimation', 'Story Estimation', estimatorAgent);
  const prioritizationNode = Codessa.createAgentNode('prioritization', 'Sprint Prioritization', productOwnerAgent);
  const capacityPlanningNode = Codessa.createAgentNode('capacity-planning', 'Capacity Planning', techLeadAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Create edges for sprint planning flow
  const edges: GraphEdge[] = [
    { name: 'input-to-backlog-grooming', source: 'input', target: 'backlog-grooming', type: 'default' },
    { name: 'backlog-grooming-to-tech-analysis', source: 'backlog-grooming', target: 'tech-analysis', type: 'default' },
    { name: 'tech-analysis-to-estimation', source: 'tech-analysis', target: 'estimation', type: 'default' },
    { name: 'estimation-to-prioritization', source: 'estimation', target: 'prioritization', type: 'default' },
    { name: 'prioritization-to-capacity-planning', source: 'prioritization', target: 'capacity-planning', type: 'default' },
    { name: 'capacity-planning-to-output', source: 'capacity-planning', target: 'output', type: 'default' }
  ];

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      backlogGroomingNode,
      technicalAnalysisNode,
      estimationNode,
      prioritizationNode,
      capacityPlanningNode,
      outputNode
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'agentic'
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}
