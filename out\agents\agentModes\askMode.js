"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AskMode = void 0;
// Remove unused vscode import
const operationMode_1 = require("./operationMode");
const logger_1 = require("../../logger");
const contextManager_1 = require("./contextManager");
const promptManager_1 = require("../../prompts/promptManager");
/**
 * Ask Mode - Default mode that uses the entire codebase as context
 */
class AskMode extends operationMode_1.OperationMode {
    id = 'ask';
    displayName = 'Ask';
    description = 'Ask questions about your codebase';
    icon = '$(question)';
    defaultContextType = operationMode_1.ContextType.ENTIRE_CODEBASE;
    requiresHumanVerification = false;
    supportsMultipleAgents = false;
    /**
       * Process a user message in Ask mode
       */
    async processMessage(message, agent, contextSource, _additionalParams) {
        try {
            logger_1.Logger.instance.info(`Processing message in Ask mode: ${message}`);
            // Get context content
            const contextContent = await contextManager_1.contextManager.getContextContent(contextSource);
            // Add memory context if available
            let memoryContext = '';
            try {
                const agentMemory = agent.getMemory();
                if (agentMemory) {
                    const relevantMemories = await agentMemory.getRelevantMemories(message);
                    if (relevantMemories && relevantMemories.length > 0) {
                        memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
                        logger_1.Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to ask context`);
                    }
                }
            }
            catch (memoryError) {
                logger_1.Logger.instance.warn('Failed to retrieve memory context for ask:', memoryError);
                // Continue without memory context
            }
            // Prepare the prompt using promptManager
            const prompt = promptManager_1.promptManager.renderPrompt('mode.ask', {
                contextContent,
                memoryContext,
                message
            });
            // Generate response using the agent
            const response = await agent.generate(prompt, this.getLLMParams());
            // Store the conversation in memory
            try {
                const agentMemory = agent.getMemory();
                if (agentMemory) {
                    await agentMemory.addMessage('user', message);
                    await agentMemory.addMessage('assistant', response);
                }
            }
            catch (memoryError) {
                logger_1.Logger.instance.warn('Failed to store conversation in memory:', memoryError);
                // Continue without storing in memory
            }
            return response;
        }
        catch (error) {
            logger_1.Logger.instance.error('Error processing message in Ask mode:', error);
            return `Error processing your question: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
    /**
       * Get LLM parameters specific to Ask mode
       */
    getLLMParams() {
        return {
            prompt: '',
            modelId: '',
            temperature: 0.7,
            maxTokens: 2000,
            mode: 'task'
        };
    }
    /**
       * Get the system prompt for Ask mode
       */
    async getSystemPrompt(_agent, _contextSource) {
        return promptManager_1.promptManager.renderPrompt('mode.ask', {});
    }
    /**
       * Get UI components specific to Ask mode
       */
    getUIComponents() {
        return {
            contextPanel: `
<div class="context-panel">
    <div class="context-header">
        <h3>Context</h3>
        <div class="context-controls">
            <button id="btn-refresh-context" title="Refresh Context"><i class="codicon codicon-refresh"></i></button>
            <button id="btn-select-files" title="Select Files"><i class="codicon codicon-file-code"></i></button>
            <button id="btn-select-folders" title="Select Folders"><i class="codicon codicon-folder"></i></button>
        </div>
    </div>
    <div class="context-type">
        <select id="context-type-selector">
            <option value="entire_codebase">Entire Codebase</option>
            <option value="selected_files">Selected Files</option>
            <option value="current_file">Current File</option>
            <option value="custom">Custom</option>
        </select>
    </div>
    <div id="context-files-list" class="context-files-list"></div>
</div>
`,
            messageInput: `
<div class="message-input-container">
    <textarea id="message-input" placeholder="Ask a question about your code..."></textarea>
    <button id="btn-send" title="Send"><i class="codicon codicon-send"></i></button>
</div>
`
        };
    }
}
exports.AskMode = AskMode;
//# sourceMappingURL=askMode.js.map