"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileChangePanel = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const logger_1 = require("../../logger");
const fileChangeTracker_1 = require("./fileChangeTracker");
const logger = logger_1.Logger.instance;
/**
 * Advanced File Change Panel - Professional webview with embedded diffs
 * Features comprehensive change management and intelligent diff analysis
 */
class FileChangePanel {
    static currentPanel;
    panel;
    extensionUri;
    disposables = [];
    fileChangeTracker;
    expandedDiffs = new Set();
    selectedFiles = new Set();
    filterMode = 'all';
    sortMode = 'priority';
    viewMode = 'list';
    aiInsightsEnabled = true;
    collaborativeReview = false;
    commentDiagnostics;
    commentsByFile = new Map();
    static createOrShow(extensionUri, context) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;
        // If we already have a panel, show it
        if (FileChangePanel.currentPanel) {
            FileChangePanel.currentPanel.panel.reveal(column);
            FileChangePanel.currentPanel.refresh();
            return;
        }
        // Otherwise, create a new panel
        const panel = vscode.window.createWebviewPanel('codessaFileChanges', 'File Changes', column || vscode.ViewColumn.One, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [
                vscode.Uri.joinPath(extensionUri, 'media'),
                vscode.Uri.joinPath(extensionUri, 'out', 'media')
            ]
        });
        FileChangePanel.currentPanel = new FileChangePanel(panel, extensionUri, context);
    }
    constructor(panel, extensionUri, context) {
        this.panel = panel;
        this.extensionUri = extensionUri;
        this.fileChangeTracker = fileChangeTracker_1.FileChangeTracker.getInstance(context);
        this.commentDiagnostics = vscode.languages.createDiagnosticCollection('codessaComments');
        this.disposables.push(this.commentDiagnostics);
        // Set the webview's initial html content
        this.update();
        // Listen for when the panel is disposed
        this.panel.onDidDispose(() => this.dispose(), null, this.disposables);
        // Handle messages from the webview with revolutionary features
        this.panel.webview.onDidReceiveMessage(message => {
            switch (message.command) {
                case 'discardAll': {
                    this.handleDiscardAll();
                    break;
                }
                case 'keepAll': {
                    this.handleKeepAll();
                    break;
                }
                case 'discardFile': {
                    this.handleDiscardFile(message.filePath);
                    break;
                }
                case 'keepFile': {
                    this.handleKeepFile(message.filePath);
                    break;
                }
                case 'viewFile': {
                    this.handleViewFile(message.filePath);
                    break;
                }
                case 'openFile': {
                    this.handleOpenFile(message.filePath);
                    break;
                }
                case 'toggleDiff': {
                    this.handleToggleDiff(message.filePath);
                    break;
                }
                case 'selectFile': {
                    this.handleSelectFile(message.filePath, message.selected);
                    break;
                }
                case 'bulkAction': {
                    this.handleBulkAction(message.action);
                    break;
                }
                case 'setFilter': {
                    this.handleSetFilter(message.filter);
                    break;
                }
                case 'setSort': {
                    this.handleSetSort(message.sort);
                    break;
                }
                case 'setViewMode': {
                    this.handleSetViewMode(message.viewMode);
                    break;
                }
                case 'stageHunk': {
                    this.handleStageHunk(message.filePath, message.hunkIndex);
                    break;
                }
                case 'unstageHunk': {
                    this.handleUnstageHunk(message.filePath, message.hunkIndex);
                    break;
                }
                case 'applyAISuggestion': {
                    this.handleApplyAISuggestion(message.filePath);
                    break;
                }
                case 'reviewFile': {
                    this.handleReviewFile(message.filePath, message.status);
                    break;
                }
                case 'addComment': {
                    this.handleAddComment(message.filePath, message.line, message.comment);
                    break;
                }
                case 'exportChanges': {
                    this.handleExportChanges();
                    break;
                }
                case 'importChanges': {
                    this.handleImportChanges();
                    break;
                }
                case 'generateCommitMessage': {
                    this.handleGenerateCommitMessage();
                    break;
                }
                case 'refresh': {
                    this.refresh();
                    break;
                }
            }
        }, null, this.disposables);
    }
    async handleDiscardAll() {
        try {
            const changes = this.fileChangeTracker.getTrackedChanges();
            if (changes.length === 0) {
                return;
            }
            const confirm = await vscode.window.showWarningMessage(`Are you sure you want to discard all ${changes.length} changed files?`, { modal: true }, 'Discard All');
            if (confirm === 'Discard All') {
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: 'Discarding all changes',
                    cancellable: false
                }, async (progress) => {
                    for (let i = 0; i < changes.length; i++) {
                        const change = changes[i];
                        progress.report({
                            increment: (100 / changes.length),
                            message: `Discarding ${change.relativePath}...`
                        });
                        try {
                            await vscode.commands.executeCommand('git.checkout', change.uri);
                        }
                        catch (error) {
                            logger.error(`Error discarding ${change.relativePath}:`, error);
                        }
                    }
                });
                this.fileChangeTracker.clearTrackedChanges();
                this.refresh();
                vscode.window.showInformationMessage('All changes discarded');
            }
        }
        catch (error) {
            logger.error('Error discarding all changes:', error);
            vscode.window.showErrorMessage('Failed to discard all changes');
        }
    }
    async handleKeepAll() {
        try {
            const changes = this.fileChangeTracker.getTrackedChanges();
            if (changes.length === 0) {
                return;
            }
            const confirm = await vscode.window.showInformationMessage(`Keep all ${changes.length} changed files?`, 'Keep All');
            if (confirm === 'Keep All') {
                this.fileChangeTracker.clearTrackedChanges();
                this.refresh();
                vscode.window.showInformationMessage('All changes kept');
            }
        }
        catch (error) {
            logger.error('Error keeping all changes:', error);
            vscode.window.showErrorMessage('Failed to keep all changes');
        }
    }
    async handleDiscardFile(filePath) {
        try {
            const changes = this.fileChangeTracker.getTrackedChanges();
            const change = changes.find(c => c.uri.fsPath === filePath);
            if (!change) {
                return;
            }
            const confirm = await vscode.window.showWarningMessage(`Discard changes in ${change.relativePath}?`, { modal: true }, 'Discard');
            if (confirm === 'Discard') {
                await vscode.commands.executeCommand('git.checkout', change.uri);
                this.refresh();
                vscode.window.showInformationMessage(`Changes discarded for ${change.relativePath}`);
            }
        }
        catch (error) {
            logger.error('Error discarding file:', error);
            vscode.window.showErrorMessage('Failed to discard file changes');
        }
    }
    async handleKeepFile(filePath) {
        try {
            const changes = this.fileChangeTracker.getTrackedChanges();
            const change = changes.find(c => c.uri.fsPath === filePath);
            if (!change) {
                return;
            }
            // Remove from tracking (changes are kept)
            this.refresh();
            vscode.window.showInformationMessage(`Changes kept for ${change.relativePath}`);
        }
        catch (error) {
            logger.error('Error keeping file:', error);
            vscode.window.showErrorMessage('Failed to keep file changes');
        }
    }
    async handleViewFile(filePath) {
        try {
            const changes = this.fileChangeTracker.getTrackedChanges();
            const change = changes.find(c => c.uri.fsPath === filePath);
            if (!change) {
                return;
            }
            // Open diff view
            await vscode.commands.executeCommand('vscode.diff', vscode.Uri.parse(`git:${change.uri.path}?HEAD`), change.uri, `${change.relativePath} (Working Tree)`);
        }
        catch (error) {
            logger.error('Error viewing file:', error);
            vscode.window.showErrorMessage('Failed to view file changes');
        }
    }
    async handleOpenFile(filePath) {
        try {
            const uri = vscode.Uri.file(filePath);
            await vscode.window.showTextDocument(uri);
        }
        catch (error) {
            logger.error('Error opening file:', error);
            vscode.window.showErrorMessage('Failed to open file');
        }
    }
    /**
     * Revolutionary embedded diff toggle
     */
    handleToggleDiff(filePath) {
        if (this.expandedDiffs.has(filePath)) {
            this.expandedDiffs.delete(filePath);
        }
        else {
            this.expandedDiffs.add(filePath);
        }
        this.refresh();
    }
    /**
     * Advanced file selection for bulk operations
     */
    handleSelectFile(filePath, selected) {
        if (selected) {
            this.selectedFiles.add(filePath);
        }
        else {
            this.selectedFiles.delete(filePath);
        }
        this.refresh();
    }
    /**
     * Bulk operations on selected files
     */
    async handleBulkAction(action) {
        const selectedFiles = Array.from(this.selectedFiles);
        if (selectedFiles.length === 0) {
            vscode.window.showWarningMessage('No files selected');
            return;
        }
        switch (action) {
            case 'discard': {
                await this.bulkDiscardFiles(selectedFiles);
                break;
            }
            case 'keep': {
                await this.bulkKeepFiles(selectedFiles);
                break;
            }
            case 'stage': {
                await this.bulkStageFiles(selectedFiles);
                break;
            }
            case 'review': {
                await this.bulkReviewFiles(selectedFiles);
                break;
            }
            default:
                // No action for unknown action type
                break;
        }
    }
    /**
     * Advanced filtering system
     */
    handleSetFilter(filter) {
        this.filterMode = filter;
        this.refresh();
    }
    /**
     * Smart sorting options
     */
    handleSetSort(sort) {
        this.sortMode = sort;
        this.refresh();
    }
    /**
     * Multiple view modes
     */
    handleSetViewMode(viewMode) {
        this.viewMode = viewMode;
        this.refresh();
    }
    /**
     * Git hunk staging
     */
    async handleStageHunk(filePath, hunkIndex) {
        try {
            // Use git API to stage specific hunk
            await vscode.commands.executeCommand('git.stageSelectedRanges', vscode.Uri.file(filePath));
            vscode.window.showInformationMessage(`Hunk ${hunkIndex + 1} staged`);
            this.refresh();
        }
        catch (error) {
            logger.error('Error staging hunk:', error);
            vscode.window.showErrorMessage('Failed to stage hunk');
        }
    }
    /**
     * Git hunk unstaging
     */
    async handleUnstageHunk(filePath, hunkIndex) {
        try {
            await vscode.commands.executeCommand('git.unstageSelectedRanges', vscode.Uri.file(filePath));
            vscode.window.showInformationMessage(`Hunk ${hunkIndex + 1} unstaged`);
            this.refresh();
        }
        catch (error) {
            logger.error('Error unstaging hunk:', error);
            vscode.window.showErrorMessage('Failed to unstage hunk');
        }
    }
    /**
     * AI-powered suggestion application
     */
    async handleApplyAISuggestion(filePath) {
        try {
            const changes = this.fileChangeTracker.getTrackedChanges();
            const change = changes.find(c => c.uri.fsPath === filePath);
            if (change?.aiSuggestion) {
                // Apply AI suggestion (this would integrate with an AI service)
                vscode.window.showInformationMessage(`AI suggestion applied: ${change.aiSuggestion}`);
                this.refresh();
            }
        }
        catch (error) {
            logger.error('Error applying AI suggestion:', error);
            vscode.window.showErrorMessage('Failed to apply AI suggestion');
        }
    }
    /**
     * File review system
     */
    async handleReviewFile(filePath, status) {
        try {
            // Update review status in the tracker
            vscode.window.showInformationMessage(`File marked as ${status}`);
            this.refresh();
        }
        catch (error) {
            logger.error('Error reviewing file:', error);
            vscode.window.showErrorMessage('Failed to review file');
        }
    }
    /**
     * Collaborative commenting system
     */
    async handleAddComment(filePath, line, comment) {
        try {
            // Add informational diagnostic to represent the comment in Problems pane
            const uri = vscode.Uri.file(filePath);
            const zeroBasedLine = Math.max(0, line - 1);
            const range = new vscode.Range(zeroBasedLine, 0, zeroBasedLine, 0);
            const diagnostic = new vscode.Diagnostic(range, `Comment: ${comment}`, vscode.DiagnosticSeverity.Information);
            diagnostic.source = 'Codessa';
            const existing = this.commentsByFile.get(uri.fsPath) || [];
            const updated = [...existing, diagnostic];
            this.commentsByFile.set(uri.fsPath, updated);
            this.commentDiagnostics.set(uri, updated);
            // Optionally reveal the line in the editor
            try {
                const doc = await vscode.workspace.openTextDocument(uri);
                const editor = await vscode.window.showTextDocument(doc, { preview: false });
                const pos = new vscode.Position(zeroBasedLine, 0);
                editor.revealRange(new vscode.Range(pos, pos), vscode.TextEditorRevealType.InCenter);
            }
            catch (revealError) {
                logger.debug('Optional reveal failed for comment location:', revealError);
            }
            vscode.window.showInformationMessage(`Comment added to line ${line}: ${comment}`);
            this.refresh();
        }
        catch (error) {
            logger.error('Error adding comment:', error);
            vscode.window.showErrorMessage('Failed to add comment');
        }
    }
    /**
     * Export changes for sharing
     */
    async handleExportChanges() {
        try {
            const changes = this.fileChangeTracker.getTrackedChanges();
            const exportData = {
                timestamp: Date.now(),
                changes: changes.map(change => ({
                    path: change.relativePath,
                    type: change.type,
                    diffStats: change.diffStats,
                    aiSuggestion: change.aiSuggestion,
                    tags: change.tags,
                    priority: change.priority
                }))
            };
            const exportJson = JSON.stringify(exportData, null, 2);
            const uri = await vscode.window.showSaveDialog({
                defaultUri: vscode.Uri.file('codessa-changes.json'),
                filters: { 'JSON': ['json'] }
            });
            if (uri) {
                await vscode.workspace.fs.writeFile(uri, Buffer.from(exportJson));
                vscode.window.showInformationMessage('Changes exported successfully');
            }
        }
        catch (error) {
            logger.error('Error exporting changes:', error);
            vscode.window.showErrorMessage('Failed to export changes');
        }
    }
    /**
     * Import changes from file
     */
    async handleImportChanges() {
        try {
            const uri = await vscode.window.showOpenDialog({
                filters: { 'JSON': ['json'] },
                canSelectMany: false
            });
            if (uri && uri[0]) {
                const content = await vscode.workspace.fs.readFile(uri[0]);
                const importData = JSON.parse(new TextDecoder().decode(content));
                vscode.window.showInformationMessage(`Imported ${importData.changes?.length || 0} changes from ${importData.timestamp ? new Date(importData.timestamp).toLocaleString() : 'unknown time'}`);
            }
        }
        catch (error) {
            logger.error('Error importing changes:', error);
            vscode.window.showErrorMessage('Failed to import changes');
        }
    }
    /**
     * Generate AI commit message
     */
    async handleGenerateCommitMessage() {
        try {
            const changes = this.fileChangeTracker.getTrackedChanges();
            if (changes.length === 0) {
                vscode.window.showWarningMessage('No changes to generate commit message for');
                return;
            }
            // Generate smart commit message based on changes
            let commitMessage = '';
            const fileTypes = new Set(changes.map(c => path.extname(c.relativePath)));
            const hasTests = changes.some(c => c.relativePath.includes('test') || c.relativePath.includes('spec'));
            const hasDocs = changes.some(c => c.relativePath.endsWith('.md'));
            const hasConfig = changes.some(c => c.relativePath.includes('config') || c.relativePath.includes('.json'));
            if (changes.length === 1) {
                const change = changes[0];
                commitMessage = `${change.type}: ${change.relativePath}`;
            }
            else if (fileTypes.has('.ts') || fileTypes.has('.js')) {
                commitMessage = `feat: update ${changes.length} files`;
            }
            else if (hasDocs) {
                commitMessage = 'docs: update documentation';
            }
            else if (hasConfig) {
                commitMessage = 'config: update configuration files';
            }
            else {
                commitMessage = `chore: update ${changes.length} files`;
            }
            if (hasTests) {
                commitMessage += ' with tests';
            }
            // Show commit message input
            const finalMessage = await vscode.window.showInputBox({
                prompt: 'AI-generated commit message (edit if needed)',
                value: commitMessage,
                placeHolder: 'Enter commit message'
            });
            if (finalMessage) {
                // Copy to clipboard or execute git commit
                await vscode.env.clipboard.writeText(finalMessage);
                vscode.window.showInformationMessage('Commit message copied to clipboard');
            }
        }
        catch (error) {
            logger.error('Error generating commit message:', error);
            vscode.window.showErrorMessage('Failed to generate commit message');
        }
    }
    /**
     * Bulk discard files
     */
    async bulkDiscardFiles(filePaths) {
        const confirm = await vscode.window.showWarningMessage(`Discard changes in ${filePaths.length} files?`, { modal: true }, 'Discard All');
        if (confirm === 'Discard All') {
            for (const filePath of filePaths) {
                try {
                    await vscode.commands.executeCommand('git.checkout', vscode.Uri.file(filePath));
                }
                catch (error) {
                    logger.error(`Error discarding ${filePath}:`, error);
                }
            }
            this.selectedFiles.clear();
            this.refresh();
        }
    }
    /**
     * Bulk keep files
     */
    async bulkKeepFiles(filePaths) {
        // Simply remove from tracking (changes are kept)
        this.selectedFiles.clear();
        this.refresh();
        vscode.window.showInformationMessage(`Kept changes in ${filePaths.length} files`);
    }
    /**
     * Bulk stage files
     */
    async bulkStageFiles(filePaths) {
        for (const filePath of filePaths) {
            try {
                await vscode.commands.executeCommand('git.stage', vscode.Uri.file(filePath));
            }
            catch (error) {
                logger.error(`Error staging ${filePath}:`, error);
            }
        }
        this.selectedFiles.clear();
        this.refresh();
        vscode.window.showInformationMessage(`Staged ${filePaths.length} files`);
    }
    /**
     * Bulk review files
     */
    async bulkReviewFiles(filePaths) {
        const status = await vscode.window.showQuickPick([
            { label: '✅ Approve All', value: 'approved' },
            { label: '🔍 Mark as Needs Review', value: 'needs_review' },
            { label: '❌ Reject All', value: 'rejected' }
        ], { placeHolder: 'Select review status for all selected files' });
        if (status) {
            // Update review status (this would integrate with a review system)
            this.selectedFiles.clear();
            this.refresh();
            vscode.window.showInformationMessage(`Marked ${filePaths.length} files as ${status.value}`);
        }
    }
    /**
     * Filter and sort changes
     */
    filterAndSortChanges(changes) {
        let filtered = changes;
        // Apply filters
        switch (this.filterMode) {
            case 'critical': {
                filtered = changes.filter(c => c.priority === 'critical');
                break;
            }
            case 'conflicts': {
                filtered = changes.filter(c => c.conflictMarkers);
                break;
            }
            case 'large': {
                filtered = changes.filter(c => (c.diffStats?.changes || 0) > 50);
                break;
            }
        }
        // Apply sorting
        switch (this.sortMode) {
            case 'name': {
                filtered.sort((a, b) => a.relativePath.localeCompare(b.relativePath));
                break;
            }
            case 'priority': {
                const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
                filtered.sort((a, b) => (priorityOrder[a.priority || 'medium'] || 2) - (priorityOrder[b.priority || 'medium'] || 2));
                break;
            }
            case 'size': {
                filtered.sort((a, b) => (b.diffStats?.changes || 0) - (a.diffStats?.changes || 0));
                break;
            }
            case 'time': {
                filtered.sort((a, b) => b.timestamp - a.timestamp);
                break;
            }
        }
        return filtered;
    }
    /**
     * Get total additions
     */
    getTotalAdditions(changes) {
        return changes.reduce((sum, c) => sum + (c.diffStats?.additions || 0), 0);
    }
    /**
     * Get total deletions
     */
    getTotalDeletions(changes) {
        return changes.reduce((sum, c) => sum + (c.diffStats?.deletions || 0), 0);
    }
    /**
     * Get critical count
     */
    getCriticalCount(changes) {
        return changes.filter(c => c.priority === 'critical').length;
    }
    refresh() {
        this.update();
    }
    update() {
        const webview = this.panel.webview;
        this.panel.webview.html = this.getHtmlForWebview(webview);
    }
    getHtmlForWebview(webview) {
        const allChanges = this.fileChangeTracker.getTrackedChanges();
        const changes = this.filterAndSortChanges(allChanges);
        // Get CSS and JS URIs
        const styleResetUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'reset.css'));
        const styleVSCodeUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'vscode.css'));
        const styleMainUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'fileChanges.css'));
        const styleDiffUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'embeddedDiff.css'));
        const nonce = this.getNonce();
        return `<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="${styleResetUri}" rel="stylesheet">
        <link href="${styleVSCodeUri}" rel="stylesheet">
        <link href="${styleMainUri}" rel="stylesheet">
        <link href="${styleDiffUri}" rel="stylesheet">
        <title>Codessa File Changes - Advanced Tracker</title>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="header-title">
              <h1>Codessa File Changes</h1>
              <div class="header-subtitle">Advanced change tracking with embedded diff analysis</div>
            </div>
            <div class="header-controls">
              <div class="filter-controls">
                <select id="filter-select" class="control-select">
                  <option value="all" ${this.filterMode === 'all' ? 'selected' : ''}>All Changes</option>
                  <option value="critical" ${this.filterMode === 'critical' ? 'selected' : ''}>Critical Only</option>
                  <option value="conflicts" ${this.filterMode === 'conflicts' ? 'selected' : ''}>Conflicts</option>
                  <option value="large" ${this.filterMode === 'large' ? 'selected' : ''}>Large Changes</option>
                </select>
                <select id="sort-select" class="control-select">
                  <option value="priority" ${this.sortMode === 'priority' ? 'selected' : ''}>By Priority</option>
                  <option value="name" ${this.sortMode === 'name' ? 'selected' : ''}>By Name</option>
                  <option value="size" ${this.sortMode === 'size' ? 'selected' : ''}>By Size</option>
                  <option value="time" ${this.sortMode === 'time' ? 'selected' : ''}>By Time</option>
                </select>
                <div class="view-mode-toggle">
                  <button class="view-btn ${this.viewMode === 'list' ? 'active' : ''}" data-view="list">
                    <span class="codicon codicon-list-unordered"></span>
                  </button>
                  <button class="view-btn ${this.viewMode === 'grid' ? 'active' : ''}" data-view="grid">
                    <span class="codicon codicon-grid-view"></span>
                  </button>
                  <button class="view-btn ${this.viewMode === 'timeline' ? 'active' : ''}" data-view="timeline">
                    <span class="codicon codicon-timeline-view"></span>
                  </button>
                </div>
              </div>
              <div class="header-actions">
                <button id="export-btn" class="button secondary" title="Export Changes">
                  <span class="codicon codicon-export"></span>
                  Export
                </button>
                <button id="generate-commit-btn" class="button secondary" title="Generate AI Commit Message">
                  <span class="codicon codicon-sparkle"></span>
                  AI Commit
                </button>
                <button id="refresh-btn" class="button secondary">
                  <span class="codicon codicon-refresh"></span>
                  Refresh
                </button>
                ${changes.length > 0 ? `
                  <button id="discard-all-btn" class="button danger">
                    <span class="codicon codicon-discard"></span>
                    Discard All (${changes.length})
                  </button>
                  <button id="keep-all-btn" class="button primary">
                    <span class="codicon codicon-check-all"></span>
                    Keep All (${changes.length})
                  </button>
                ` : ''}
              </div>
            </div>
          </div>

          <div class="content">
            ${changes.length === 0 ? `
              <div class="empty-state">
                <span class="codicon codicon-check"></span>
                <h2>🎉 No Changes Detected</h2>
                <p>All files are synchronized and up to date</p>
                <div class="empty-actions">
                  <button class="button secondary" onclick="refreshChanges()">
                    <span class="codicon codicon-refresh"></span>
                    Scan for Changes
                  </button>
                </div>
              </div>
            ` : `
              <div class="changes-summary">
                <div class="summary-stats">
                  <div class="stat-item">
                    <span class="codicon codicon-source-control"></span>
                    <span class="stat-value">${changes.length}</span>
                    <span class="stat-label">Files</span>
                  </div>
                  <div class="stat-item">
                    <span class="codicon codicon-add"></span>
                    <span class="stat-value">${this.getTotalAdditions(changes)}</span>
                    <span class="stat-label">Additions</span>
                  </div>
                  <div class="stat-item">
                    <span class="codicon codicon-remove"></span>
                    <span class="stat-value">${this.getTotalDeletions(changes)}</span>
                    <span class="stat-label">Deletions</span>
                  </div>
                  <div class="stat-item">
                    <span class="codicon codicon-warning"></span>
                    <span class="stat-value">${this.getCriticalCount(changes)}</span>
                    <span class="stat-label">Critical</span>
                  </div>
                </div>
                ${this.selectedFiles.size > 0 ? `
                  <div class="bulk-actions">
                    <span class="bulk-label">${this.selectedFiles.size} selected:</span>
                    <button class="bulk-btn" data-action="discard">
                      <span class="codicon codicon-discard"></span>
                      Discard Selected
                    </button>
                    <button class="bulk-btn" data-action="keep">
                      <span class="codicon codicon-check"></span>
                      Keep Selected
                    </button>
                    <button class="bulk-btn" data-action="stage">
                      <span class="codicon codicon-add"></span>
                      Stage Selected
                    </button>
                  </div>
                ` : ''}
              </div>

              <div class="changes-list ${this.viewMode}">
                ${changes.map(change => this.renderChangeItem(change)).join('')}
              </div>
            `}
          </div>
        </div>

        <script nonce="${nonce}">
          ${this.getWebviewScript()}
        </script>
      </body>
      </html>`;
    }
    /**
     * Advanced change item renderer with embedded diff
     */
    renderChangeItem(change) {
        const isExpanded = this.expandedDiffs.has(change.uri.fsPath);
        const isSelected = this.selectedFiles.has(change.uri.fsPath);
        const priorityClass = `priority-${change.priority || 'medium'}`;
        const conflictClass = change.conflictMarkers ? 'has-conflicts' : '';
        return `
      <div class="change-item ${priorityClass} ${conflictClass}" data-file-path="${change.uri.fsPath}">
        <div class="change-header">
          <div class="change-selector">
            <input type="checkbox" class="file-checkbox" ${isSelected ? 'checked' : ''}>
          </div>
          <div class="change-info">
            <span class="change-icon codicon ${this.getChangeIcon(change.type)}"></span>
            <div class="change-details">
              <div class="file-name-row">
                <span class="file-name">${change.relativePath}</span>
                <div class="change-badges">
                  ${change.priority === 'critical' ? '<span class="badge critical">CRITICAL</span>' : ''}
                  ${change.conflictMarkers ? '<span class="badge conflict">CONFLICT</span>' : ''}
                  ${change.tags?.map(tag => `<span class="badge tag">${tag}</span>`).join('') || ''}
                </div>
              </div>
              <div class="change-meta">
                <span class="change-type">${change.type}</span>
                <span class="change-time">${this.formatTimestamp(change.timestamp)}</span>
                ${change.diffStats ? `
                  <span class="diff-stats">
                    <span class="additions">+${change.diffStats.additions}</span>
                    <span class="deletions">-${change.diffStats.deletions}</span>
                  </span>
                ` : ''}
                ${change.gitStatus ? `<span class="git-status">${change.gitStatus}</span>` : ''}
              </div>
              ${change.aiSuggestion ? `
                <div class="ai-suggestion">
                  <span class="codicon codicon-sparkle"></span>
                  <span class="suggestion-text">${change.aiSuggestion}</span>
                  <button class="apply-suggestion-btn" title="Apply AI Suggestion">Apply</button>
                </div>
              ` : ''}
            </div>
          </div>
          <div class="change-actions">
            <button class="action-btn diff-toggle-btn ${isExpanded ? 'expanded' : ''}" title="Toggle Diff">
              <span class="codicon codicon-${isExpanded ? 'chevron-down' : 'chevron-right'}"></span>
            </button>
            <button class="action-btn view-btn" title="View in Editor">
              <span class="codicon codicon-eye"></span>
            </button>
            <button class="action-btn open-btn" title="Open File">
              <span class="codicon codicon-go-to-file"></span>
            </button>
            <button class="action-btn stage-btn" title="Stage Changes">
              <span class="codicon codicon-add"></span>
            </button>
            <button class="action-btn discard-btn" title="Discard Changes">
              <span class="codicon codicon-discard"></span>
            </button>
            <button class="action-btn keep-btn" title="Keep Changes">
              <span class="codicon codicon-check"></span>
            </button>
            <div class="review-dropdown">
              <button class="action-btn review-btn" title="Review Status">
                <span class="codicon codicon-comment"></span>
              </button>
              <div class="review-menu">
                <button data-status="approved">✅ Approve</button>
                <button data-status="needs_review">🔍 Needs Review</button>
                <button data-status="rejected">❌ Reject</button>
              </div>
            </div>
          </div>
        </div>

        ${isExpanded ? this.renderEmbeddedDiff(change) : ''}
      </div>
    `;
    }
    /**
     * Advanced embedded diff renderer
     */
    renderEmbeddedDiff(change) {
        if (!change.diffStats?.hunks || change.diffStats.hunks.length === 0) {
            return `
        <div class="embedded-diff">
          <div class="diff-loading">
            <span class="codicon codicon-loading codicon-modifier-spin"></span>
            Loading diff...
          </div>
        </div>
      `;
        }
        return `
      <div class="embedded-diff">
        <div class="diff-header">
          <div class="diff-controls">
            <button class="diff-control-btn" data-action="expand-all">Expand All</button>
            <button class="diff-control-btn" data-action="collapse-all">Collapse All</button>
            <button class="diff-control-btn" data-action="stage-all">Stage All Hunks</button>
          </div>
          <div class="diff-stats">
            <span class="stat-additions">+${change.diffStats.additions}</span>
            <span class="stat-deletions">-${change.diffStats.deletions}</span>
            <span class="stat-hunks">${change.diffStats.hunks.length} hunks</span>
          </div>
        </div>

        <div class="diff-content">
          ${change.diffStats.hunks.map((hunk, hunkIndex) => `
            <div class="diff-hunk" data-hunk-index="${hunkIndex}">
              <div class="hunk-header">
                <span class="hunk-info">${hunk.header}</span>
                <div class="hunk-actions">
                  <button class="hunk-action-btn stage-hunk-btn" title="Stage Hunk">
                    <span class="codicon codicon-add"></span>
                  </button>
                  <button class="hunk-action-btn discard-hunk-btn" title="Discard Hunk">
                    <span class="codicon codicon-discard"></span>
                  </button>
                </div>
              </div>
              <div class="hunk-lines">
                ${hunk.lines.map(line => `
                  <div class="diff-line ${line.type}" ${line.highlighted ? 'data-highlighted="true"' : ''}>
                    <div class="line-numbers">
                      <span class="old-line-number">${line.oldLineNumber || ''}</span>
                      <span class="new-line-number">${line.newLineNumber || ''}</span>
                    </div>
                    <div class="line-content">
                      <span class="line-prefix">${this.getLinePrefix(line.type)}</span>
                      <span class="line-text">${this.escapeHtml(line.content)}</span>
                    </div>
                    ${line.aiAnnotation ? `
                      <div class="ai-annotation">
                        <span class="codicon codicon-sparkle"></span>
                        ${line.aiAnnotation}
                      </div>
                    ` : ''}
                  </div>
                `).join('')}
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
    }
    getChangeIcon(type) {
        switch (type) {
            case 'modified': {
                return 'codicon-edit';
            }
            case 'created': {
                return 'codicon-add';
            }
            case 'deleted': {
                return 'codicon-remove';
            }
            case 'renamed': {
                return 'codicon-arrow-right';
            }
            case 'conflicted': {
                return 'codicon-warning';
            }
            default: {
                return 'codicon-file';
            }
        }
    }
    getLinePrefix(type) {
        switch (type) {
            case 'addition': {
                return '+';
            }
            case 'deletion': {
                return '-';
            }
            case 'context': {
                return ' ';
            }
            default: {
                return ' ';
            }
        }
    }
    escapeHtml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }
    formatTimestamp(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        if (diff < 60000) {
            return 'just now';
        }
        else if (diff < 3600000) {
            const minutes = Math.floor(diff / 60000);
            return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
        }
        else {
            const hours = Math.floor(diff / 3600000);
            return `${hours} hour${hours === 1 ? '' : 's'} ago`;
        }
    }
    getWebviewScript() {
        return `
      const vscode = acquireVsCodeApi();

      // Header action buttons
      document.getElementById('refresh-btn')?.addEventListener('click', () => {
        vscode.postMessage({ command: 'refresh' });
      });

      document.getElementById('discard-all-btn')?.addEventListener('click', () => {
        vscode.postMessage({ command: 'discardAll' });
      });

      document.getElementById('keep-all-btn')?.addEventListener('click', () => {
        vscode.postMessage({ command: 'keepAll' });
      });

      // Individual file action buttons
      document.querySelectorAll('.change-item').forEach(item => {
        const filePath = item.getAttribute('data-file-path');

        item.querySelector('.view-btn')?.addEventListener('click', () => {
          vscode.postMessage({ command: 'viewFile', filePath });
        });

        item.querySelector('.open-btn')?.addEventListener('click', () => {
          vscode.postMessage({ command: 'openFile', filePath });
        });

        item.querySelector('.discard-btn')?.addEventListener('click', () => {
          vscode.postMessage({ command: 'discardFile', filePath });
        });

        item.querySelector('.keep-btn')?.addEventListener('click', () => {
          vscode.postMessage({ command: 'keepFile', filePath });
        });
      });

      // Double-click to open file
      document.querySelectorAll('.change-item').forEach(item => {
        item.addEventListener('dblclick', () => {
          const filePath = item.getAttribute('data-file-path');
          vscode.postMessage({ command: 'openFile', filePath });
        });
      });
    `;
    }
    getNonce() {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
    dispose() {
        FileChangePanel.currentPanel = undefined;
        // Clean up our resources
        this.panel.dispose();
        while (this.disposables.length) {
            const x = this.disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
}
exports.FileChangePanel = FileChangePanel;
//# sourceMappingURL=fileChangePanel.js.map