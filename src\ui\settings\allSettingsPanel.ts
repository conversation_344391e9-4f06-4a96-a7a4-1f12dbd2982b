import * as vscode from 'vscode';
import { MemoryManager } from '../../memory/memoryManager';
import { ProviderManager } from '../../llm/providerManager';
import { logger } from '../../logger';
import { SettingsManager } from './settingsManager';
import { WebviewMessageHandler } from '../utilities/webviewMessageHandler';
import { Settings, SettingsSection } from './types';

// Define message types for the webview communication
interface WebviewMessage {
    command: string;
    [key: string]: unknown;
}

interface UpdateSettingMessage extends WebviewMessage {
    command: 'updateSetting';
    key: string;
    value: unknown;
}

interface ResetSettingMessage extends WebviewMessage {
    command: 'resetSetting';
    key: string;
}

interface ImportSettingsMessage extends WebviewMessage {
    command: 'importSettings';
    settings: string; // JSON string of settings
}

type SettingsChangeListener = (key: string, value: unknown) => void;

export class AllSettingsPanel {
  private static readonly viewType = 'codessa.settings';
  private static currentPanel: AllSettingsPanel | undefined;
  private readonly panel: vscode.WebviewPanel;
  private readonly extensionUri: vscode.Uri;
  private disposables: vscode.Disposable[] = [];
  private settingsManager: SettingsManager;
  private messageHandler: WebviewMessageHandler;

  private constructor(
    private readonly context: vscode.ExtensionContext,
    extensionUri: vscode.Uri,
    private readonly memoryManager: MemoryManager,
    private readonly providerManager: ProviderManager
  ) {
    this.extensionUri = extensionUri;
    this.settingsManager = SettingsManager.getInstance();

    // Create and configure the webview panel
    this.panel = vscode.window.createWebviewPanel(
      AllSettingsPanel.viewType,
      'Codessa Settings',
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'media')]
      }
    );

    // Initialize the message handler after panel is created
    this.messageHandler = new WebviewMessageHandler(this.context);

    this.panel.webview.html = this.getWebviewContent();
    this.setupMessageHandlers();
    this.setupPanelEvents();
  }

  public static async createOrShow(
    context: vscode.ExtensionContext,
    extensionUri: vscode.Uri,
    memoryManager: MemoryManager,
    providerManager: ProviderManager
  ) {
    if (AllSettingsPanel.currentPanel) {
      AllSettingsPanel.currentPanel.panel.reveal(vscode.ViewColumn.One);
      return;
    }

    AllSettingsPanel.currentPanel = new AllSettingsPanel(
      context,
      extensionUri,
      memoryManager,
      providerManager
    );

    // Initialize settings manager if not already done
    if (!AllSettingsPanel.currentPanel.settingsManager.isReady()) {
      await AllSettingsPanel.currentPanel.settingsManager.initialize();
    }
  }

  private setupMessageHandlers(): void {
    this.messageHandler = new WebviewMessageHandler(this.context);

    this.panel.webview.onDidReceiveMessage(
      async (message: WebviewMessage) => {
        try {
          // First try the settings-specific handlers
          switch (message.command) {
            case 'updateSetting':
              await this.handleSettingUpdate(message as UpdateSettingMessage);
              return;

            case 'requestInitialSettings':
              await this.sendInitialSettings();
              return;

            case 'requestProviderSettings':
              await this.sendProviderSettings();
              return;

            case 'resetSetting':
              await this.handleResetSetting(message as ResetSettingMessage);
              return;

            case 'exportSettings':
              await this.handleExportSettings();
              return;

            case 'importSettings':
              await this.handleImportSettings(message as ImportSettingsMessage);
              return;
          }

          // Then try the generic message handler
          const response = await this.messageHandler.handleMessage(message, this.panel);
          if (response !== null && response !== undefined) {
            const responseMessage = {
              command: `${message.command}Response`,
              ...(typeof response === 'object' && !Array.isArray(response) ? response : { result: response })
            };

            this.panel.webview.postMessage(responseMessage);
          }
        } catch (error) {
          logger.error('Error handling webview message:', error);
          this.panel.webview.postMessage({
            command: 'error',
            error: error instanceof Error ? error.message : String(error)
          });
        }
      },
      null,
      this.disposables
    );
  }

  private async handleSettingUpdate(message: UpdateSettingMessage): Promise<void> {
    const { key, value } = message;
    if (!key) {
      logger.error('No key provided for setting update');
      return;
    }
    const fullKey = `${message.section}.${key}`;
    logger.debug(`Updating setting: ${fullKey} = ${value}`);

    try {
      // Use the settings manager to update the setting
      const validation = await this.settingsManager.setSetting(fullKey, value, true);

      if (validation.isValid) {
        // Notify webview of successful update
        this.panel.webview.postMessage({
          command: 'settingUpdated',
          section: message.section,
          key,
          value,
          success: true,
          warnings: validation.warnings
        });

        // Handle special cases
        if (message.section === 'general') {
          switch (key) {
            case 'theme':
              // Handle theme change if needed
              break;
            case 'language':
              // Handle language change if needed
              break;
          }
        }
      } else {
        // Notify webview of validation failure
        this.panel.webview.postMessage({
          command: 'settingUpdated',
          section: message.section,
          key,
          value,
          success: false,
          error: validation.errors.join(', '),
          warnings: validation.warnings
        });
      }
    } catch (error) {
      logger.error('Failed to update setting:', error);
      this.panel.webview.postMessage({
        command: 'settingUpdated',
        section: message.section,
        key,
        value,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async handleResetSetting(message: ResetSettingMessage): Promise<void> {
    const { key } = message;
    if (!key) {
      logger.error('No key provided for reset setting');
      return;
    }
    const fullKey = `${message.section}.${key}`;

    try {
      await this.settingsManager.resetSetting(fullKey);
      this.panel.webview.postMessage({
        command: 'settingReset',
        section: message.section,
        key,
        success: true
      });
    } catch (error) {
      logger.error('Failed to reset setting:', error);
      this.panel.webview.postMessage({
        command: 'settingReset',
        section: message.section,
        key,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async handleExportSettings(): Promise<void> {
    try {
      const settingsJson = this.settingsManager.exportSettings();
      this.panel.webview.postMessage({
        command: 'settingsExported',
        settings: settingsJson,
        success: true
      });
    } catch (error) {
      logger.error('Failed to export settings:', error);
      this.panel.webview.postMessage({
        command: 'settingsExported',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async handleImportSettings(message: ImportSettingsMessage): Promise<void> {
    const { settings } = message;
    if (!settings) {
      logger.error('No settings provided for import');
      return;
    }

    try {
      const result = await this.settingsManager.importSettings(settings);

      if (result.success) {
        this.panel.webview.postMessage({
          command: 'settingsImported',
          success: true
        });
        // Reload settings in the webview
        await this.sendInitialSettings();
      } else {
        this.panel.webview.postMessage({
          command: 'settingsImported',
          success: false,
          errors: result.errors
        });
      }
    } catch (error) {
      logger.error('Failed to import settings:', error);
      this.panel.webview.postMessage({
        command: 'settingsImported',
        success: false,
        error: error instanceof Error ? error.message : 'Failed to load settings'
      });
    }
  }

  private async sendInitialSettings(): Promise<void> {
    try {
      // Get settings as unknown first, then cast to Settings
      const settings = this.settingsManager.getAllSettings() as unknown as Settings;
      const sections = this.settingsManager.getSettingSections();
      
      await this.panel.webview.postMessage({
        type: 'initialSettings',
        settings,
        sections
      });
    } catch (error) {
      logger.error('Failed to load initial settings:', error);
      this.panel.webview.postMessage({
        command: 'updateSettings',
        settings: {},
        error: error instanceof Error ? error.message : 'Failed to load settings'
      });
    }
  }

  private async sendProviderSettings(): Promise<void> {
    try {
      const providers = this.providerManager.getAllProviders();
      const activeProvider = this.providerManager.getDefaultProviderId();

      this.panel.webview.postMessage({
        command: 'updateProviderSettings',
        providers,
        activeProvider
      });
    } catch (error) {
      logger.error('Failed to load provider settings:', error);
      this.panel.webview.postMessage({
        command: 'updateProviderSettings',
        providers: [],
        activeProvider: null,
        error: error instanceof Error ? error.message : 'Failed to load provider settings'
      });
    }
  }

  private setupPanelEvents() {
    this.panel.onDidDispose(
      () => {
        AllSettingsPanel.currentPanel = undefined;
        this.dispose();
      },
      null,
      this.disposables
    );
  }

  private dispose() {
    if (this.panel) {
      this.panel.dispose();
    }
    while (this.disposables.length) {
      const disposable = this.disposables.pop();
      if (disposable) {
        disposable.dispose();
      }
    }
  }

  private getWebviewContent(): string {
    const scriptUri = this.panel.webview.asWebviewUri(
      vscode.Uri.joinPath(this.extensionUri, 'media', 'settings.js')
    );
    const stylesUri = this.panel.webview.asWebviewUri(
      vscode.Uri.joinPath(this.extensionUri, 'media', 'styles', 'settings.css')
    );

    return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Codessa Settings</title>
            <link href="${stylesUri}" rel="stylesheet">
        </head>
        <body>
            <div id="codessa-settings-root"></div>
            <script src="${scriptUri}"></script>
            <script>
                // Ensure initial settings are always requested after script loads
                window.addEventListener('DOMContentLoaded', function() {
                    if (window.acquireVsCodeApi) {
                        const vscode = window.acquireVsCodeApi();
                        vscode.postMessage({ command: 'requestInitialSettings' });
                    } else if (window.parent) {
                        // fallback for some webview environments
                        window.parent.postMessage({ command: 'requestInitialSettings' }, '*');
                    }
                });
            </script>
        </body>
        </html>`;
  }
}
