"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMemoryEnabled = getMemoryEnabled;
exports.setMemoryEnabled = setMemoryEnabled;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../logger");
let memoryEnabled = false;
function getMemoryEnabled() {
    return memoryEnabled;
}
function setMemoryEnabled(enabled) {
    const oldState = memoryEnabled;
    memoryEnabled = enabled;
    // Only log if state actually changed
    if (oldState !== enabled) {
        logger_1.Logger.instance.info(`Memory ${enabled ? 'enabled' : 'disabled'}`);
        // Emit event for state change
        void vscode.commands.executeCommand('codessa.memoryStateChanged', enabled);
        // Update configuration
        void vscode.workspace.getConfiguration('codessa').update('memory.enabled', enabled, true);
    }
}
//# sourceMappingURL=memoryConfig.js.map