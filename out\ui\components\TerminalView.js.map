{"version": 3, "file": "TerminalView.js", "sourceRoot": "", "sources": ["../../../src/ui/components/TerminalView.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+DAA4D;AAC5D,0FAAkG;AAElG,MAAa,YAAY;IAOQ;IANtB,MAAM,CAAU,QAAQ,GAAG,sBAAsB,CAAC;IACjD,KAAK,CAAsB;IAC3B,SAAS,CAAkB;IAC3B,YAAY,GAAwB,EAAE,CAAC;IACvC,aAAa,GAAW,EAAE,CAAC;IAEnC,YAA6B,aAAyB;QAAzB,kBAAa,GAAb,aAAa,CAAY;QAClD,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CAClB,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC1C,IAAI,CAAC,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,UAAU,EAAE,CAAC;YACtB,CAAC;QACL,CAAC,CAAC,CACL,CAAC;IACN,CAAC;IAEM,kBAAkB,CAAC,WAA+B;QACrD,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;SAC3C,CAAC;QAEF,yCAAyC;QACzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAClB,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CACvC,CACJ,CAAC;QAEF,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO;QAExB,MAAM,QAAQ,GAAG,0CAAgB,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY;YACvB,CAAC,QAAQ,CAAC,QAAQ,KAAK,UAAU,IAAI,QAAQ,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;QAEpF,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAEjD,0CAA0C;YAC1C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC3B,OAAO,EAAE,gBAAgB;oBACzB,IAAI,EAAE,IAAI,CAAC,aAAa;iBAC3B,CAAC,CAAC;gBACH,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;YAC5B,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvD,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAChF,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAE7F,IAAI,CAAC,SAAS,GAAG,IAAI,+BAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAEhD,yBAAyB;QACzB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE;YACvB,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACpC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE,IAAI;aACb,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACJ,+CAA+C;YAC/C,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;QAC/B,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,OAAY;QACrC,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;YACtB,KAAK,eAAe;gBAChB,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM;YAEV,KAAK,oBAAoB;gBACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;QACd,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,MAAM,QAAQ,GAAG,0CAAgB,CAAC,WAAW,EAAE,CAAC;QAChD,IAAI,WAA6B,CAAC;QAElC,QAAQ,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACxB,KAAK,0CAAgB,CAAC,QAAQ;gBAC1B,WAAW,GAAG,0CAAgB,CAAC,eAAe,CAAC;gBAC/C,MAAM;YACV,KAAK,0CAAgB,CAAC,eAAe;gBACjC,WAAW,GAAG,0CAAgB,CAAC,IAAI,CAAC;gBACpC,MAAM;YACV,KAAK,0CAAgB,CAAC,IAAI,CAAC;YAC3B;gBACI,WAAW,GAAG,0CAAgB,CAAC,QAAQ,CAAC;QAChD,CAAC;QAED,MAAM,0CAAgB,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC5B,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IAC3B,CAAC;IAEO,eAAe;QACnB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAsGK,CAAC;IACjB,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;;oBAMK,CAAC;IACjB,CAAC;IAED,OAAO;QACH,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;;AAhQL,oCAiQC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { PseudoTerminal } from '../../tools/pseudoTerminal';\nimport { terminalSettings, TerminalViewType } from '../settings/sections/terminalSettingsSection';\n\nexport class TerminalView implements vscode.WebviewViewProvider {\n    public static readonly viewType = 'codessa.terminalView';\n    private _view?: vscode.WebviewView;\n    private _terminal?: PseudoTerminal;\n    private _disposables: vscode.Disposable[] = [];\n    private _outputBuffer: string = '';\n\n    constructor(private readonly _extensionUri: vscode.Uri) {\n        // Handle configuration changes\n        this._disposables.push(\n            vscode.workspace.onDidChangeConfiguration(e => {\n                if (e.affectsConfiguration('codessa.terminal')) {\n                    this.updateView();\n                }\n            })\n        );\n    }\n\n    public resolveWebviewView(webviewView: vscode.WebviewView) {\n        this._view = webviewView;\n        \n        webviewView.webview.options = {\n            enableScripts: true,\n            localResourceRoots: [this._extensionUri]\n        };\n\n        // Set up message handler for the webview\n        this._disposables.push(\n            webviewView.webview.onDidReceiveMessage(\n                this.handleWebviewMessage.bind(this)\n            )\n        );\n\n        this.updateView();\n    }\n\n    private async updateView() {\n        if (!this._view) return;\n        \n        const settings = terminalSettings.getSettings();\n        const shouldShow = settings.showTerminal && \n                         (settings.viewType === 'sideView' || settings.viewType === 'both');\n\n        if (shouldShow) {\n            await this.initializeTerminal();\n            this._view.webview.html = this.getTerminalHtml();\n            \n            // Send any buffered output to the webview\n            if (this._outputBuffer) {\n                this._view.webview.postMessage({\n                    command: 'terminalOutput',\n                    text: this._outputBuffer\n                });\n                this._outputBuffer = '';\n            }\n        } else {\n            this.disposeTerminal();\n            this._view.webview.html = this.getEmptyStateHtml();\n        }\n    }\n\n    private async initializeTerminal() {\n        if (this._terminal) return;\n        \n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();\n        const shell = vscode.env.shell || (process.platform === 'win32' ? 'powershell.exe' : 'bash');\n        \n        this._terminal = new PseudoTerminal(shell, cwd);\n        \n        // Handle terminal output\n        this._terminal.onOutput(data => {\n            this.sendOutputToWebview(data);\n        });\n        \n        // Handle terminal close\n        this._terminal.onExit(() => {\n            this.disposeTerminal();\n        });\n    }\n\n    private sendOutputToWebview(text: string) {\n        if (this._view) {\n            this._view.webview.postMessage({\n                command: 'terminalOutput',\n                text: text\n            });\n        } else {\n            // Buffer the output until the webview is ready\n            this._outputBuffer += text;\n        }\n    }\n\n    private handleWebviewMessage(message: any) {\n        switch (message.command) {\n            case 'terminalInput':\n                this._terminal?.write(message.text);\n                break;\n                \n            case 'toggleTerminalView':\n                this.toggleTerminalView();\n                break;\n        }\n    }\n    \n    private async toggleTerminalView() {\n        const settings = terminalSettings.getSettings();\n        let newViewType: TerminalViewType;\n        \n        switch (settings.viewType) {\n            case TerminalViewType.SideView:\n                newViewType = TerminalViewType.DefaultTerminal;\n                break;\n            case TerminalViewType.DefaultTerminal:\n                newViewType = TerminalViewType.Both;\n                break;\n            case TerminalViewType.Both:\n            default:\n                newViewType = TerminalViewType.SideView;\n        }\n        \n        await terminalSettings.updateSettings({ viewType: newViewType });\n        this.updateView();\n    }\n\n    private disposeTerminal() {\n        if (this._terminal) {\n            this._terminal.dispose();\n            this._terminal = undefined;\n        }\n        this._outputBuffer = '';\n    }\n    \n    public dispose() {\n        this.disposeTerminal();\n        this._disposables.forEach(d => d.dispose());\n        this._disposables = [];\n    }\n\n    private getTerminalHtml() {\n        return `\n            <!DOCTYPE html>\n            <html>\n            <head>\n                <style>\n                    body { \n                        margin: 0; \n                        padding: 0;\n                        font-family: var(--vscode-font-family);\n                        color: var(--vscode-foreground);\n                        background-color: var(--vscode-editor-background);\n                    }\n                    .terminal-header {\n                        display: flex;\n                        justify-content: space-between;\n                        align-items: center;\n                        padding: 8px 12px;\n                        border-bottom: 1px solid var(--vscode-panel-border);\n                    }\n                    .terminal-title {\n                        font-weight: 600;\n                    }\n                    .terminal-toggle {\n                        background: var(--vscode-button-background);\n                        color: var(--vscode-button-foreground);\n                        border: none;\n                        padding: 4px 12px;\n                        border-radius: 2px;\n                        cursor: pointer;\n                        font-size: 12px;\n                    }\n                    .terminal-toggle:hover {\n                        background: var(--vscode-button-hoverBackground);\n                    }\n                    #terminal { \n                        background: var(--vscode-terminal-background);\n                        color: var(--vscode-terminal-foreground);\n                        padding: 8px;\n                        height: calc(100vh - 60px);\n                        overflow: auto;\n                        font-family: var(--vscode-editor-font-family);\n                        font-size: var(--vscode-editor-font-size);\n                        white-space: pre-wrap;\n                        line-height: 1.4;\n                    }\n                    #terminal:focus {\n                        outline: none;\n                    }\n                </style>\n            </head>\n            <body>\n                <div class=\"terminal-header\">\n                    <div class=\"terminal-title\">Terminal</div>\n                    <button class=\"terminal-toggle\" id=\"toggleTerminal\">Show in Default Terminal</button>\n                </div>\n                <div id=\"terminal\" tabindex=\"0\"></div>\n                <script>\n                    const terminal = document.getElementById('terminal');\n                    const toggleBtn = document.getElementById('toggleTerminal');\n                    \n                    // Focus the terminal div for keyboard input\n                    terminal.focus();\n                    \n                    // Handle terminal toggle\n                    toggleBtn.addEventListener('click', () => {\n                        vscode.postMessage({\n                            command: 'toggleTerminalView'\n                        });\n                    });\n                    \n                    // Handle keyboard input\n                    terminal.addEventListener('keydown', (e) => {\n                        // Don't handle special keys\n                        if (e.ctrlKey || e.altKey || e.metaKey) {\n                            return;\n                        }\n                        \n                        // Send the key to the extension\n                        vscode.postMessage({\n                            command: 'terminalInput',\n                            text: e.key\n                        });\n                        \n                        // Prevent default to avoid double input\n                        e.preventDefault();\n                    });\n                    \n                    // Handle messages from the extension\n                    window.addEventListener('message', event => {\n                        const message = event.data;\n                        switch (message.command) {\n                            case 'terminalOutput':\n                                terminal.textContent += message.text;\n                                terminal.scrollTop = terminal.scrollHeight;\n                                break;\n                            case 'terminalClear':\n                                terminal.textContent = '';\n                                break;\n                        }\n                    });\n                </script>\n            </body>\n            </html>`;\n    }\n\n    private getEmptyStateHtml() {\n        return `\n            <!DOCTYPE html>\n            <html>\n            <body style=\"padding: 20px; font-family: var(--vscode-font-family);\">\n                <p>Terminal is disabled or set to show in default terminal view.</p>\n            </body>\n            </html>`;\n    }\n\n    dispose() {\n        this.disposeTerminal();\n    }\n}\n"]}