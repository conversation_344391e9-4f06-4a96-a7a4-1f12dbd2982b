"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConfig = getConfig;
exports.setConfig = setConfig;
exports.deleteConfig = deleteConfig;
exports.getAllConfigKeys = getAllConfigKeys;
exports.getConfigInspection = getConfigInspection;
exports.getLogLevel = getLogLevel;
exports.getMaxToolIterations = getMaxToolIterations;
exports.getDefaultModelConfig = getDefaultModelConfig;
exports.getMemoryEnabled = getMemoryEnabled;
exports.getMaxMemories = getMaxMemories;
exports.getMemoryRelevanceThreshold = getMemoryRelevanceThreshold;
exports.getMemoryContextWindowSize = getMemoryContextWindowSize;
exports.getConversationHistorySize = getConversationHistorySize;
exports.getOpenAIApiKey = getOpenAIApiKey;
exports.getOpenAIBaseUrl = getOpenAIBaseUrl;
exports.getOpenAIOrganization = getOpenAIOrganization;
exports.getGoogleAIApiKey = getGoogleAIApiKey;
exports.getMistralAIApiKey = getMistralAIApiKey;
exports.getAnthropicApiKey = getAnthropicApiKey;
exports.getOllamaBaseUrl = getOllamaBaseUrl;
exports.getLMStudioBaseUrl = getLMStudioBaseUrl;
exports.getAgents = getAgents;
exports.saveAgents = saveAgents;
exports.getSystemPrompts = getSystemPrompts;
exports.getPromptVariables = getPromptVariables;
exports.getGoddessMode = getGoddessMode;
exports.getQuantumAnalysis = getQuantumAnalysis;
exports.getNeuralSynthesis = getNeuralSynthesis;
exports.getTimeTravelDebugging = getTimeTravelDebugging;
exports.getWorkflowEnhancements = getWorkflowEnhancements;
exports.getUIEnhancements = getUIEnhancements;
exports.getAdvancedAI = getAdvancedAI;
exports.setGoddessMode = setGoddessMode;
exports.setQuantumAnalysis = setQuantumAnalysis;
exports.setNeuralSynthesis = setNeuralSynthesis;
exports.setTimeTravelDebugging = setTimeTravelDebugging;
exports.setWorkflowEnhancements = setWorkflowEnhancements;
exports.setUIEnhancements = setUIEnhancements;
exports.setAdvancedAI = setAdvancedAI;
const vscode = __importStar(require("vscode"));
const logger_1 = require("./logger");
/**
 * Get a configuration value from VS Code settings
 */
function getConfig(key, defaultValue) {
    try {
        const config = vscode.workspace.getConfiguration('codessa');
        const value = config.get(key);
        return value !== undefined ? value : defaultValue;
    }
    catch (error) {
        logger_1.logger.error(`Error reading configuration key 'codessa.${key}':`, error);
        return defaultValue;
    }
}
/**
 * Set a configuration value in VS Code settings
 */
async function setConfig(key, value, target = vscode.ConfigurationTarget.Global) {
    try {
        const config = vscode.workspace.getConfiguration('codessa');
        // Validate the key
        if (!key || typeof key !== 'string') {
            throw new Error('Invalid configuration key');
        }
        // Try with the specified target first
        try {
            await config.update(key, value, target);
            logger_1.logger.info(`Updated configuration key 'codessa.${key}' at target level ${target}`);
            return true;
        }
        catch (targetError) {
            logger_1.logger.warn(`Failed to update at target level ${target}: ${targetError}. Trying alternative targets...`);
        }
        // If the specified target fails, try Global
        if (target !== vscode.ConfigurationTarget.Global) {
            try {
                await config.update(key, value, vscode.ConfigurationTarget.Global);
                logger_1.logger.info(`Updated configuration key 'codessa.${key}' at Global level`);
                return true;
            }
            catch (globalError) {
                logger_1.logger.warn(`Failed to update at Global level: ${globalError}. Trying Workspace level...`);
            }
        }
        // If Global fails, try Workspace if available
        if (target !== vscode.ConfigurationTarget.Workspace &&
            vscode.workspace.workspaceFolders &&
            vscode.workspace.workspaceFolders.length > 0) {
            try {
                await config.update(key, value, vscode.ConfigurationTarget.Workspace);
                logger_1.logger.info(`Updated configuration key 'codessa.${key}' at Workspace level`);
                return true;
            }
            catch (workspaceError) {
                logger_1.logger.warn(`Failed to update at Workspace level: ${workspaceError}`);
            }
        }
        // If we get here, all attempts failed
        throw new Error(`Failed to update setting 'codessa.${key}' at any target level`);
    }
    catch (error) {
        logger_1.logger.error(`Error writing configuration key 'codessa.${key}':`, error);
        return false;
    }
}
/**
 * Delete a configuration value
 */
async function deleteConfig(key) {
    try {
        const config = vscode.workspace.getConfiguration('codessa');
        await config.update(key, undefined, vscode.ConfigurationTarget.Global);
        logger_1.logger.info(`Deleted configuration key 'codessa.${key}'`);
        return true;
    }
    catch (error) {
        logger_1.logger.error(`Error deleting configuration key 'codessa.${key}':`, error);
        return false;
    }
}
/**
 * Get all configuration keys
 */
function getAllConfigKeys() {
    try {
        const config = vscode.workspace.getConfiguration('codessa');
        return config.keys();
    }
    catch (error) {
        logger_1.logger.error('Error getting all configuration keys:', error);
        return [];
    }
}
/**
 * Get configuration inspection (includes default, global, workspace values)
 */
function getConfigInspection(key) {
    try {
        const config = vscode.workspace.getConfiguration('codessa');
        return config.inspect(key);
    }
    catch (error) {
        logger_1.logger.error(`Error inspecting configuration key 'codessa.${key}':`, error);
        return undefined;
    }
}
// Core settings
function getLogLevel() {
    return getConfig('logLevel', 'info');
}
function getMaxToolIterations() {
    return getConfig('maxToolIterations', 5);
}
function getDefaultModelConfig() {
    // Get selected provider and model from chat view settings
    const selectedProvider = getConfig('selectedProvider', 'ollama');
    const selectedModel = getConfig('selectedModel', 'llama3');
    const defaultConfig = {
        provider: selectedProvider,
        modelId: selectedModel,
        options: {
            temperature: 0.7,
            maxTokens: 2000
        }
    };
    return defaultConfig;
}
// Memory settings
function getMemoryEnabled() {
    return getConfig('memory.enabled', true);
}
function getMaxMemories() {
    return getConfig('memory.maxMemories', 1000);
}
function getMemoryRelevanceThreshold() {
    return getConfig('memory.relevanceThreshold', 0.7);
}
function getMemoryContextWindowSize() {
    return getConfig('memory.contextWindowSize', 5);
}
function getConversationHistorySize() {
    return getConfig('memory.conversationHistorySize', 100);
}
// Provider configurations
function getOpenAIApiKey() {
    return getConfig('providers.openai.apiKey', '');
}
function getOpenAIBaseUrl() {
    const defaultUrl = 'https://api.openai.com/v1';
    return getConfig('providers.openai.baseUrl', defaultUrl);
}
function getOpenAIOrganization() {
    return getConfig('providers.openai.organization', '');
}
function getGoogleAIApiKey() {
    return getConfig('providers.googleai.apiKey', '');
}
function getMistralAIApiKey() {
    return getConfig('providers.mistralai.apiKey', '');
}
function getAnthropicApiKey() {
    return getConfig('providers.anthropic.apiKey', '');
}
function getOllamaBaseUrl() {
    return getConfig('providers.ollama.baseUrl', 'http://localhost:11434');
}
function getLMStudioBaseUrl() {
    return getConfig('providers.lmstudio.baseUrl', 'http://localhost:1234/v1');
}
// Agent configuration
function getAgents() {
    return getConfig('agents', []);
}
async function saveAgents(agents) {
    return await setConfig('agents', agents);
}
// Prompt configuration
function getSystemPrompts() {
    return getConfig('systemPrompts', {});
}
function getPromptVariables() {
    return getConfig('promptVariables', {});
}
// Advanced Features Settings
function getGoddessMode() {
    const defaultSettings = {
        enabled: true,
        adaptiveLevel: 85,
        emotionalIntelligence: 90,
        motivationalStyle: 'adaptive',
        wisdomLevel: 95,
        creativityLevel: 80,
        empathyLevel: 85,
        personalityAdaptation: true,
        moodAnalysis: true,
        contextualResponses: true,
        divineGuidance: true
    };
    return getConfig('advancedFeatures.goddessMode', defaultSettings);
}
function getQuantumAnalysis() {
    const defaultSettings = {
        enabled: true,
        patternRecognition: true,
        superpositionAnalysis: true,
        entanglementDetection: true,
        parallelUniverseTesting: true,
        quantumInterference: true,
        confidenceThreshold: 0.7,
        maxParallelUniverses: 5,
        quantumComplexityLevel: 'advanced'
    };
    return getConfig('advancedFeatures.quantumAnalysis', defaultSettings);
}
function getNeuralSynthesis() {
    const defaultSettings = {
        enabled: true,
        brainInspiredGeneration: true,
        synapticConnections: true,
        consciousnessAnalysis: true,
        creativityBoost: true,
        neuralLearning: true,
        networkComplexity: 'medium',
        learningRate: 0.01,
        creativityThreshold: 0.7,
        consciousnessLevel: 75
    };
    return getConfig('advancedFeatures.neuralSynthesis', defaultSettings);
}
function getTimeTravelDebugging() {
    const defaultSettings = {
        enabled: true,
        futureIssuePrediction: true,
        alternativeTimelines: true,
        mitigationStrategies: true,
        temporalAnalysis: true,
        predictionAccuracy: 'high',
        timeHorizon: '3-months',
        riskThreshold: 0.6,
        maxPredictions: 10
    };
    return getConfig('advancedFeatures.timeTravelDebugging', defaultSettings);
}
function getWorkflowEnhancements() {
    const defaultSettings = {
        advancedFeaturesEnabled: true,
        autoEnhancement: true,
        goddessGuidance: true,
        quantumOptimization: true,
        neuralInsights: true,
        timeTravelPredictions: true,
        adaptiveExecution: true,
        performanceMetrics: true,
        userSatisfactionTracking: true,
        goddessRatingSystem: true
    };
    return getConfig('advancedFeatures.workflowEnhancements', defaultSettings);
}
function getUIEnhancements() {
    const defaultSettings = {
        advancedTheme: true,
        goddessAnimations: true,
        quantumEffects: true,
        neuralVisualizations: true,
        timeTravelIndicators: true,
        adaptiveInterface: true,
        emotionalFeedback: true,
        motivationalElements: true,
        wisdomTooltips: true,
        creativityIndicators: true
    };
    return getConfig('advancedFeatures.uiEnhancements', defaultSettings);
}
function getAdvancedAI() {
    const defaultSettings = {
        multiModalProcessing: true,
        contextualUnderstanding: true,
        emotionalIntelligence: true,
        creativeThinking: true,
        intuitiveProblemSolving: true,
        adaptiveLearning: true,
        consciousnessSimulation: true,
        quantumInspiredAlgorithms: true,
        neuralNetworkIntegration: true,
        temporalReasoningCapabilities: true
    };
    return getConfig('advancedFeatures.advancedAI', defaultSettings);
}
// Setter functions for advanced features
async function setGoddessMode(settings) {
    return await setConfig('advancedFeatures.goddessMode', settings);
}
async function setQuantumAnalysis(settings) {
    return await setConfig('advancedFeatures.quantumAnalysis', settings);
}
async function setNeuralSynthesis(settings) {
    return await setConfig('advancedFeatures.neuralSynthesis', settings);
}
async function setTimeTravelDebugging(settings) {
    return await setConfig('advancedFeatures.timeTravelDebugging', settings);
}
async function setWorkflowEnhancements(settings) {
    return await setConfig('advancedFeatures.workflowEnhancements', settings);
}
async function setUIEnhancements(settings) {
    return await setConfig('advancedFeatures.uiEnhancements', settings);
}
async function setAdvancedAI(settings) {
    return await setConfig('advancedFeatures.advancedAI', settings);
}
//# sourceMappingURL=config.js.map