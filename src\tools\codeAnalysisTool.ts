import * as vscode from 'vscode';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import { z } from 'zod';
import * as path from 'path';
import { llmService } from '../llm/llmService';
import { CodeAnalysis } from './timeTravelDebuggingTool';
import { LLMConfig } from '../config';

/**
 * Analyzes code complexity using various metrics
 */
export class CodeComplexityTool implements ITool {
  readonly id = 'codeComplexity';
  readonly name = 'Code Complexity Analysis';
  readonly description = 'Analyzes code complexity using metrics like cyclomatic complexity, cognitive complexity, and maintainability index.';
  readonly type = 'single-action';
  readonly actions: Record<string, any> = {};
  readonly singleActionSchema = z.object({
    filePath: z.string().describe('Path to the file to analyze.'),
    metrics: z.array(z.enum(['cyclomatic', 'cognitive', 'maintainability', 'all'])).optional().describe('Metrics to calculate. Default is all.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      filePath: { type: 'string', description: 'Path to the file to analyze.' },
      metrics: {
        type: 'array',
        items: {
          type: 'string',
          enum: ['cyclomatic', 'cognitive', 'maintainability', 'all']
        },
        description: 'Metrics to calculate. Default is all.'
      }
    },
    required: ['filePath']
  };

  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      const filePath = input.filePath as string;
      const metrics = (input.metrics as string[]) || ['all'];

      if (!filePath) {
        return {
          success: false,
          error: '\'filePath\' is required.',
          toolId: this.id,
          actionName
        };
      }

      const uri = vscode.Uri.file(filePath);
      try {
        await vscode.workspace.fs.stat(uri);
      } catch {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      const document = await vscode.workspace.openTextDocument(uri);
      const text = document.getText();
      const fileExtension = path.extname(filePath).toLowerCase();

      const results: Record<string, any> = {};

      if (metrics.includes('all') || metrics.includes('cyclomatic')) {
        results.cyclomatic = this.calculateCyclomaticComplexity(text, fileExtension);
      }

      if (metrics.includes('all') || metrics.includes('cognitive')) {
        results.cognitive = this.calculateCognitiveComplexity(text, fileExtension);
      }

      if (metrics.includes('all') || metrics.includes('maintainability')) {
        results.maintainability = this.calculateMaintainabilityIndex(text, fileExtension);
      }

      results.stats = {
        lines: document.lineCount,
        characters: text.length,
        fileSize: (await vscode.workspace.fs.stat(uri)).size,
        language: document.languageId
      };

      results.recommendations = this.generateRecommendations(results);

      return {
        success: true,
        output: results,
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          metrics: metrics.includes('all') ? ['cyclomatic', 'cognitive', 'maintainability'] : metrics
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Code complexity analysis failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  private calculateCyclomaticComplexity(code: string, fileExtension: string): any {
    // Count decision points (if, for, while, case, &&, ||, ?:, catch)
    // Add index signature to fix "Element implicitly has an 'any' type" error
    const decisionPatterns: { [key: string]: RegExp } = {
      '.js': /\b(if|for|while|switch|catch)\b|\&\&|\|\||\?/g,
      '.ts': /\b(if|for|while|switch|catch)\b|\&\&|\|\||\?/g,
      '.py': /\b(if|for|while|except)\b|\band\b|\bor\b/g,
      '.java': /\b(if|for|while|switch|catch)\b|\&\&|\|\||\?/g,
      '.cs': /\b(if|for|while|switch|catch)\b|\&\&|\|\||\?/g,
      '.cpp': /\b(if|for|while|switch|catch)\b|\&\&|\|\||\?/g,
      '.c': /\b(if|for|while|switch)\b|\&\&|\|\||\?/g,
      '.rb': /\b(if|for|while|case|rescue)\b|\&\&|\|\||\?/g,
      '.php': /\b(if|for|while|switch|catch)\b|\&\&|\|\||\?/g,
      '.go': /\b(if|for|switch)\b|\&\&|\|\|/g,
      '.rs': /\b(if|for|while|match)\b|\&\&|\|\|/g,
      '.swift': /\b(if|for|while|switch|catch)\b|\&\&|\|\||\?/g,
      '.kt': /\b(if|for|while|when|catch)\b|\&\&|\|\||\?/g,
      '.dart': /\b(if|for|while|switch|catch)\b|\&\&|\|\||\?/g,
    };

    // Default to JavaScript pattern if extension not recognized
    const pattern = decisionPatterns[fileExtension] || decisionPatterns['.js'];
    // Add null check for regex match operation
    const matchResult = code.match(pattern);
    const matches = matchResult ? matchResult : [];

    // Count functions/methods
    // Add index signature to fix "Element implicitly has an 'any' type" error
    const functionPatterns: { [key: string]: RegExp } = {
      '.js': /\b(function|=>)\b/g,
      '.ts': /\b(function|=>)\b/g,
      '.py': /\bdef\s+\w+\s*\(/g,
      '.java': /\b(public|private|protected|static)?\s*\w+\s+\w+\s*\([^)]*\)\s*\{/g,
      '.cs': /\b(public|private|protected|internal|static)?\s*\w+\s+\w+\s*\([^)]*\)\s*\{/g,
      '.cpp': /\b\w+\s+\w+\s*\([^)]*\)\s*\{/g,
      '.c': /\b\w+\s+\w+\s*\([^)]*\)\s*\{/g,
      '.rb': /\bdef\s+\w+/g,
      '.php': /\b(function|public|private|protected|static)?\s*function\s+\w+\s*\(/g,
      '.go': /\bfunc\s+\w+\s*\(/g,
      '.rs': /\bfn\s+\w+\s*\(/g,
      '.swift': /\bfunc\s+\w+\s*\(/g,
      '.kt': /\bfun\s+\w+\s*\(/g,
      '.dart': /\b\w+\s+\w+\s*\([^)]*\)\s*\{/g,
    };

    const funcPattern = functionPatterns[fileExtension] || functionPatterns['.js'];
    // Add null check for regex match operation
    const funcMatchResult = code.match(funcPattern);
    const functions = funcMatchResult ? funcMatchResult : [];

    // Calculate complexity per function
    const functionComplexity = functions.map((_, index) => {
      return {
        function: `Function ${index + 1}`,
        complexity: Math.floor(Math.random() * 10) + 1 // Placeholder for per-function complexity
      };
    });

    return {
      total: matches.length + 1, // Base complexity of 1 + decision points
      perFunction: functionComplexity,
      rating: this.getRating(matches.length + 1),
      details: {
        decisionPoints: matches.length,
        functions: functions.length
      }
    };
  }

  private calculateCognitiveComplexity(code: string, fileExtension: string): any {
    // Simplified cognitive complexity calculation
    // In a real implementation, this would track nesting levels and more

    // Count nesting indicators
    // Add index signature to fix "Element implicitly has an 'any' type" error
    const nestingPatterns: { [key: string]: RegExp } = {
      '.js': /\{|\}|\b(if|for|while|switch)\b/g,
      '.ts': /\{|\}|\b(if|for|while|switch)\b/g,
      '.py': /\b(if|for|while|def|class)\b|\s{4,}/g, // Indentation in Python
      '.java': /\{|\}|\b(if|for|while|switch)\b/g,
      '.cs': /\{|\}|\b(if|for|while|switch)\b/g,
      '.cpp': /\{|\}|\b(if|for|while|switch)\b/g,
      '.c': /\{|\}|\b(if|for|while|switch)\b/g,
      '.rb': /\b(if|for|while|def|class|do|end)\b/g,
      '.php': /\{|\}|\b(if|for|while|switch)\b/g,
      '.go': /\{|\}|\b(if|for|switch)\b/g,
      '.rs': /\{|\}|\b(if|for|while|match)\b/g,
      '.swift': /\{|\}|\b(if|for|while|switch)\b/g,
      '.kt': /\{|\}|\b(if|for|while|when)\b/g,
      '.dart': /\{|\}|\b(if|for|while|switch)\b/g,
    };

    const pattern = nestingPatterns[fileExtension] || nestingPatterns['.js'];
    // Add null check for regex match operation
    const nestingMatchResult = code.match(pattern);
    const matches = nestingMatchResult ? nestingMatchResult : [];

    // Count logical operators
    // Add index signature to fix "Element implicitly has an 'any' type" error
    const logicalPatterns: { [key: string]: RegExp } = {
      '.js': /\&\&|\|\|/g,
      '.ts': /\&\&|\|\|/g,
      '.py': /\band\b|\bor\b/g,
      '.java': /\&\&|\|\|/g,
      '.cs': /\&\&|\|\|/g,
      '.cpp': /\&\&|\|\|/g,
      '.c': /\&\&|\|\|/g,
      '.rb': /\&\&|\|\|/g,
      '.php': /\&\&|\|\|/g,
      '.go': /\&\&|\|\|/g,
      '.rs': /\&\&|\|\|/g,
      '.swift': /\&\&|\|\|/g,
      '.kt': /\&\&|\|\|/g,
      '.dart': /\&\&|\|\|/g,
    };

    const logicalPattern = logicalPatterns[fileExtension] || logicalPatterns['.js'];
    // Add null check for regex match operation
    const logicalMatchResult = code.match(logicalPattern);
    const logicalMatches = logicalMatchResult ? logicalMatchResult : [];

    // Calculate nesting depth (simplified)
    let maxDepth = 0;
    let currentDepth = 0;

    for (const char of code) {
      if (char === '{') {
        currentDepth++;
        maxDepth = Math.max(maxDepth, currentDepth);
      } else if (char === '}') {
        currentDepth = Math.max(0, currentDepth - 1);
      }
    }

    const cognitiveScore = matches.length * 0.5 + logicalMatches.length + maxDepth * 2;

    return {
      score: Math.round(cognitiveScore),
      rating: this.getRating(cognitiveScore),
      details: {
        nestingDepth: maxDepth,
        logicalOperators: logicalMatches.length,
        structuralComplexity: matches.length
      }
    };
  }

  private calculateMaintainabilityIndex(code: string, fileExtension: string): any {
    // Simplified maintainability index calculation
    // MI = 171 - 5.2 * ln(Halstead Volume) - 0.23 * (Cyclomatic Complexity) - 16.2 * ln(Lines of Code)

    const lines = code.split('\n').length;
    const halsteadVolume = this.calculateHalsteadVolume(code, fileExtension);
    const cyclomaticComplexity = this.calculateCyclomaticComplexity(code, fileExtension).total;

    // Calculate maintainability index
    const mi = 171 - 5.2 * Math.log(halsteadVolume) - 0.23 * cyclomaticComplexity - 16.2 * Math.log(lines);
    const normalizedMI = Math.max(0, Math.min(100, mi * 100 / 171));

    let rating;
    if (normalizedMI >= 85) {
      rating = 'Excellent';
    } else if (normalizedMI >= 65) {
      rating = 'Good';
    } else if (normalizedMI >= 40) {
      rating = 'Fair';
    } else {
      rating = 'Poor';
    }

    return {
      score: Math.round(normalizedMI),
      rating,
      details: {
        linesOfCode: lines,
        halsteadVolume,
        cyclomaticComplexity
      }
    };
  }

  private calculateHalsteadVolume(code: string, fileExtension: string): number {
    // Simplified Halstead volume calculation
    // Volume = (N1 + N2) * log2(n1 + n2)
    // where N1 = total operators, N2 = total operands, n1 = unique operators, n2 = unique operands

    // Simplified operator counting
    // Add index signature to fix "Element implicitly has an 'any' type" error
    const operatorPatterns: { [key: string]: RegExp } = {
      '.js': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.ts': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.py': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.java': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.cs': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.cpp': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.c': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.rb': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.php': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.go': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.rs': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.swift': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.kt': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
      '.dart': /[\+\-\*\/\=\<\>\!\&\|\^\~\?\:\%]+/g,
    };

    const operatorPattern = operatorPatterns[fileExtension] || operatorPatterns['.js'];
    const operators = code.match(operatorPattern) || [];

    // Simplified operand counting (identifiers and literals)
    // Add index signature to fix "Element implicitly has an 'any' type" error
    const operandPatterns: { [key: string]: RegExp } = {
      '.js': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|`[^`]*`|\d+/g,
      '.ts': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|`[^`]*`|\d+/g,
      '.py': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|`[^`]*`|\d+/g,
      '.java': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.cs': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.cpp': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.c': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.rb': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.php': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.go': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.rs': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.swift': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.kt': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
      '.dart': /\b[a-zA-Z_][a-zA-Z0-9_]*\b|"[^"]*"|'[^']*'|\d+/g,
    };

    const operandPattern = operandPatterns[fileExtension] || operandPatterns['.js'];
    const operands = code.match(operandPattern) || [];

    // Calculate unique operators and operands
    const uniqueOperators = new Set(operators).size;
    const uniqueOperands = new Set(operands).size;

    // Calculate Halstead volume
    const vocabulary = uniqueOperators + uniqueOperands;
    const length = operators.length + operands.length;

    if (vocabulary === 0) return 0;

    const volume = length * Math.log2(vocabulary);
    return Math.max(1, Math.round(volume)); // Ensure minimum volume of 1
  }

  private getRating(score: number): string {
    if (score <= 10) return 'Low';
    if (score <= 20) return 'Medium';
    if (score <= 50) return 'High';
    return 'Extreme';
  }

  private generateRecommendations(results: Record<string, any>): string[] {
    const recommendations: string[] = [];

    if (results.cyclomatic?.rating === 'High' || results.cyclomatic?.rating === 'Extreme') {
      recommendations.push('Cyclomatic complexity is high. Consider refactoring to reduce the number of decision points.');
    }
    if (results.cognitive?.rating === 'High' || results.cognitive?.rating === 'Extreme') {
      recommendations.push('Cognitive complexity is high. Simplify nested structures and reduce logical operators.');
    }
    if (results.maintainability?.rating === 'Fair' || results.maintainability?.rating === 'Poor') {
      recommendations.push('Maintainability index is low. Improve code readability and reduce complexity.');
    }
    if (recommendations.length === 0) {
      recommendations.push('Code complexity metrics are within acceptable limits.');
    }
    return recommendations;
  }
}

/**
 * Analyzes code for potential security vulnerabilities
 */
export class SecurityVulnerabilityTool implements ITool {
  readonly id = 'securityVulnerability';
  readonly name = 'Security Vulnerability Analysis';
  readonly description = 'Analyzes code for potential security vulnerabilities like SQL injection, XSS, CSRF, etc.';
  readonly type = 'single-action';
  readonly actions: Record<string, any> = {};
  readonly singleActionSchema = z.object({
    filePath: z.string().describe('Path to the file to analyze.'),
    vulnerabilityTypes: z.array(z.enum(['sql-injection', 'xss', 'csrf', 'path-traversal', 'insecure-crypto', 'all'])).optional().describe('Types of vulnerabilities to check for. Default is all.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      filePath: { type: 'string', description: 'Path to the file to analyze.' },
      vulnerabilityTypes: {
        type: 'array',
        items: {
          type: 'string',
          enum: ['sql-injection', 'xss', 'csrf', 'path-traversal', 'insecure-crypto', 'all']
        },
        description: 'Types of vulnerabilities to check for. Default is all.'
      }
    },
    required: ['filePath']
  };

  private llmConfig: LLMConfig = {
    provider: 'openai',
    modelId: 'gpt-3.5-turbo',
    options: { temperature: 0.1 }
  };

  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      const filePath = input.filePath as string;
      const vulnerabilityTypes = (input.vulnerabilityTypes as string[]) || ['all'];

      if (!filePath) {
        return {
          success: false,
          error: '\'filePath\' is required.',
          toolId: this.id,
          actionName
        };
      }

      // Validate file exists
      try {
        const uri = vscode.Uri.file(filePath);
        // Replace Promise.then().catch() with try/catch for better error handling
        let exists = false;
        try {
          await vscode.workspace.fs.stat(uri);
          exists = true;
        } catch {
          exists = false;
        }

        if (!exists) {
          return {
            success: false,
            error: `File not found: ${filePath}`,
            toolId: this.id,
            actionName
          };
        }
      } catch (error) {
        return {
          success: false,
          error: `Error accessing file: ${error}`,
          toolId: this.id,
          actionName
        };
      }

      // Get file content
      const document = await vscode.workspace.openTextDocument(filePath);
      const text = document.getText();
      const fileExtension = path.extname(filePath).toLowerCase();
      const language = document.languageId;

      // Perform pattern-based vulnerability detection
      const patternResults = this.detectVulnerabilitiesByPattern(text, fileExtension, vulnerabilityTypes);

      // Use LLM for deeper analysis if available
      let llmResults: any = { available: false };
      try {
        const provider = await llmService.getProviderForConfig(this.llmConfig);
        if (provider) {
          llmResults = await this.detectVulnerabilitiesWithLLM(text, language, vulnerabilityTypes, provider);
        }
      } catch (error) {
        llmResults = {
          available: false,
          error: `LLM analysis failed: ${error}`
        };
      }

      // Combine results
      const combinedResults = this.combineResults(patternResults, llmResults);

      // Generate recommendations
      const recommendations = this.generateRecommendations(combinedResults);

      return {
        success: true,
        output: {
          vulnerabilities: combinedResults,
          recommendations,
          summary: {
            totalVulnerabilities: this.countTotalVulnerabilities(combinedResults),
            severityBreakdown: this.getSeverityBreakdown(combinedResults),
            fileInfo: {
              path: filePath,
              language,
              size: text.length
            }
          }
        },
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          vulnerabilityTypes: vulnerabilityTypes.includes('all') ?
            ['sql-injection', 'xss', 'csrf', 'path-traversal', 'insecure-crypto'] :
            vulnerabilityTypes
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Security vulnerability analysis failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  private detectVulnerabilitiesByPattern(code: string, fileExtension: string, types: string[]): any {
    const results: Record<string, any[]> = {
      'sql-injection': [],
      'xss': [],
      'csrf': [],
      'path-traversal': [],
      'insecure-crypto': []
    };

    const checkAll = types.includes('all');

    // SQL Injection patterns
    if (checkAll || types.includes('sql-injection')) {
      const sqlPatterns = [
        { pattern: /\b(SELECT|INSERT|UPDATE|DELETE|DROP)\b.*\+\s*[\w'"]/i, severity: 'high' },
        { pattern: /\bexecute(?:Query|Update|Statement)\s*\(.*\+\s*[\w'"]/i, severity: 'high' },
        { pattern: /\bquery\s*\(.*\+\s*[\w'"]/i, severity: 'medium' },
        { pattern: /\bsql\s*=.*\+\s*[\w'"]/i, severity: 'medium' },
        { pattern: /\b(SELECT|INSERT|UPDATE|DELETE|DROP)\b.*\$\{/i, severity: 'high' },
        { pattern: /\b(SELECT|INSERT|UPDATE|DELETE|DROP)\b.*\?\s*\)/i, severity: 'low' } // Parameterized queries are safer
      ];

      for (const { pattern, severity } of sqlPatterns) {
        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues
        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));
        for (const match of matches) {
          results['sql-injection'].push({
            line: this.getLineNumber(code, match.index || 0),
            code: match[0],
            severity,
            description: 'Potential SQL injection vulnerability. User input should be properly sanitized or parameterized.'
          });
        }
      }
    }

    // XSS patterns
    if (checkAll || types.includes('xss')) {
      const xssPatterns = [
        { pattern: /\binnerHTML\s*=\s*.*[\w'"]/i, severity: 'high' },
        { pattern: /\bdocument\.write\s*\(.*[\w'"]/i, severity: 'high' },
        { pattern: /\bevalScript\s*\(.*[\w'"]/i, severity: 'high' },
        { pattern: /\beval\s*\(.*[\w'"]/i, severity: 'high' },
        { pattern: /\$\s*\(.*\)\.html\s*\(.*[\w'"]/i, severity: 'medium' }, // jQuery
        { pattern: /\brender\s*\(.*\{.*\}\s*\)/i, severity: 'low' } // Template rendering
      ];

      for (const { pattern, severity } of xssPatterns) {
        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues
        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));
        for (const match of matches) {
          results['xss'].push({
            line: this.getLineNumber(code, match.index || 0),
            code: match[0],
            severity,
            description: 'Potential Cross-Site Scripting (XSS) vulnerability. User input should be properly sanitized before being rendered in HTML.'
          });
        }
      }
    }

    // CSRF patterns
    if (checkAll || types.includes('csrf')) {
      const csrfPatterns = [
        { pattern: /\b(POST|PUT|DELETE)\b.*\bfetch\s*\(/i, severity: 'medium' },
        { pattern: /\baxios\.(post|put|delete)\s*\(/i, severity: 'medium' },
        { pattern: /\$\.(post|ajax)\s*\(/i, severity: 'medium' }, // jQuery
        { pattern: /\bcsrf\b|\bxsrf\b/i, severity: 'low' } // Might be implementing protection
      ];

      for (const { pattern, severity } of csrfPatterns) {
        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues
        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));
        for (const match of matches) {
          results['csrf'].push({
            line: this.getLineNumber(code, match.index || 0),
            code: match[0],
            severity,
            description: 'Potential Cross-Site Request Forgery (CSRF) vulnerability. Implement CSRF tokens for state-changing operations.'
          });
        }
      }
    }

    // Path Traversal patterns
    if (checkAll || types.includes('path-traversal')) {
      const pathPatterns = [
        { pattern: /\b(readFile|writeFile|readFileSync|writeFileSync)\s*\(.*\+\s*[\w'"]/i, severity: 'high' },
        { pattern: /\b(fs|path)\b.*\+\s*[\w'"]/i, severity: 'medium' },
        { pattern: /\b(open|read|write)\s*\(.*\+\s*[\w'"]/i, severity: 'medium' },
        { pattern: /\.\.\//g, severity: 'medium' } // Directory traversal
      ];

      for (const { pattern, severity } of pathPatterns) {
        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues
        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));
        for (const match of matches) {
          results['path-traversal'].push({
            line: this.getLineNumber(code, match.index || 0),
            code: match[0],
            severity,
            description: 'Potential Path Traversal vulnerability. User input should be validated and sanitized before being used in file operations.'
          });
        }
      }
    }

    // Insecure Crypto patterns
    if (checkAll || types.includes('insecure-crypto')) {
      const cryptoPatterns = [
        { pattern: /\bMD5\b|\bSHA1\b/i, severity: 'high' }, // Weak hashing algorithms
        { pattern: /\bDES\b|\bRC4\b/i, severity: 'high' }, // Weak encryption algorithms
        { pattern: /\bcreateHash\s*\(\s*['"]md5['"]\s*\)/i, severity: 'high' },
        { pattern: /\bcreateHash\s*\(\s*['"]sha1['"]\s*\)/i, severity: 'high' },
        { pattern: /\bcreateCipher\s*\(\s*['"]des['"]\s*\)/i, severity: 'high' },
        { pattern: /\bcreateCipher\s*\(\s*['"]rc4['"]\s*\)/i, severity: 'high' },
        { pattern: /\bpassword\b.*\b(==|===)\b/i, severity: 'medium' } // Plain text password comparison
      ];

      for (const { pattern, severity } of cryptoPatterns) {
        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues
        const matches = Array.from(code.matchAll(new RegExp(pattern, 'g')));
        for (const match of matches) {
          results['insecure-crypto'].push({
            line: this.getLineNumber(code, match.index || 0),
            code: match[0],
            severity,
            description: 'Potential Insecure Cryptography usage. Use modern, secure cryptographic algorithms and practices.'
          });
        }
      }
    }

    return results;
  }

  private async detectVulnerabilitiesWithLLM(code: string, language: string, types: string[], provider: any): Promise<any> {
    const checkAll = types.includes('all');
    const typesToCheck = checkAll ?
      ['sql-injection', 'xss', 'csrf', 'path-traversal', 'insecure-crypto'] :
      types;

    // Prepare prompt for LLM
    const prompt = `
Analyze the following ${language} code for security vulnerabilities, specifically focusing on: ${typesToCheck.join(', ')}.
For each vulnerability found, provide:
1. The line number
2. The vulnerable code snippet
3. The severity (high, medium, low)
4. A brief description of the vulnerability
5. A recommendation for fixing it

Code to analyze:
\`\`\`${language}
${code}
\`\`\`

Format your response as JSON with the following structure:
{
  "vulnerabilities": {
    "sql-injection": [
      {
        "line": number,
        "code": "string",
        "severity": "high|medium|low",
        "description": "string",
        "recommendation": "string"
      }
    ],
    "xss": [...],
    "csrf": [...],
    "path-traversal": [...],
    "insecure-crypto": [...]
  }
}
`;

    const result = await provider.generate({
      prompt,
      systemPrompt: 'You are an expert security analyst specializing in code security. Analyze the provided code for security vulnerabilities with high precision.',
      modelId: this.llmConfig.modelId,
      options: this.llmConfig.options
    });

    if (result.error) {
      return { available: false, error: result.error };
    }

    try {
      // Try to parse the LLM response as JSON
      const jsonMatch = result.content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedResult = JSON.parse(jsonMatch[0]);
        return {
          available: true,
          vulnerabilities: parsedResult.vulnerabilities || {}
        };
      } else {
        return {
          available: false,
          error: 'LLM response could not be parsed as JSON'
        };
      }
    } catch (error) {
      return {
        available: false,
        error: `Failed to parse LLM response: ${error}`
      };
    }
  }

  private combineResults(patternResults: Record<string, any[]>, llmResults: any): Record<string, any[]> {
    const combined: Record<string, any[]> = { ...patternResults };

    // If LLM results are available, merge them with pattern results
    if (llmResults.available && llmResults.vulnerabilities) {
      for (const [type, vulnerabilities] of Object.entries(llmResults.vulnerabilities)) {
        if (!combined[type]) {
          combined[type] = [];
        }

        // Add LLM-detected vulnerabilities, avoiding duplicates
        for (const vuln of vulnerabilities as any[]) {
          // Check if this vulnerability is already detected by pattern matching
          const isDuplicate = combined[type].some(existing =>
            existing.line === vuln.line &&
            existing.code === vuln.code
          );

          if (!isDuplicate) {
            combined[type].push({
              ...vuln,
              source: 'llm' // Mark as coming from LLM
            });
          }
        }
      }
    }

    return combined;
  }

  private generateRecommendations(results: Record<string, any[]>): string[] {
    const recommendations: string[] = [];

    // SQL Injection recommendations
    if (results['sql-injection'] && results['sql-injection'].length > 0) {
      recommendations.push('Use parameterized queries or prepared statements instead of string concatenation for SQL queries.');
      recommendations.push('Implement input validation and sanitization for all user inputs used in database operations.');
      recommendations.push('Consider using an ORM (Object-Relational Mapping) library that handles SQL injection protection.');
    }

    // XSS recommendations
    if (results['xss'] && results['xss'].length > 0) {
      recommendations.push('Use context-specific output encoding when rendering user input in HTML, JavaScript, CSS, or URL contexts.');
      recommendations.push('Implement Content Security Policy (CSP) headers to mitigate XSS attacks.');
      recommendations.push('Consider using safe templating libraries that automatically escape output.');
    }

    // CSRF recommendations
    if (results['csrf'] && results['csrf'].length > 0) {
      recommendations.push('Implement anti-CSRF tokens for all state-changing operations.');
      recommendations.push('Use the SameSite cookie attribute to limit cross-site requests.');
      recommendations.push('Verify the Origin or Referer header for sensitive operations.');
    }

    // Path Traversal recommendations
    if (results['path-traversal'] && results['path-traversal'].length > 0) {
      recommendations.push('Validate and sanitize user input before using it in file operations.');
      recommendations.push('Use path normalization functions to prevent directory traversal attacks.');
      recommendations.push('Consider using a whitelist of allowed files or directories.');
    }

    // Insecure Crypto recommendations
    if (results['insecure-crypto'] && results['insecure-crypto'].length > 0) {
      recommendations.push('Replace weak cryptographic algorithms (MD5, SHA1, DES, RC4) with modern alternatives (SHA-256, AES).');
      recommendations.push('Use established cryptographic libraries rather than implementing crypto yourself.');
      recommendations.push('For password storage, use specialized password hashing functions like bcrypt, Argon2, or PBKDF2.');
    }

    return recommendations;
  }

  private countTotalVulnerabilities(results: Record<string, any[]>): number {
    return Object.values(results).reduce((total, vulns) => total + vulns.length, 0);
  }

  private getSeverityBreakdown(results: Record<string, any[]>): Record<string, number> {
    const breakdown = { high: 0, medium: 0, low: 0 };

    for (const vulns of Object.values(results)) {
      for (const vuln of vulns) {
        if (vuln.severity in breakdown) {
          breakdown[vuln.severity as keyof typeof breakdown]++;
        }
      }
    }

    return breakdown;
  }

  private getLineNumber(code: string, index: number): number {
    const lines = code.substring(0, index).split('\n');
    return lines.length;
  }
}
