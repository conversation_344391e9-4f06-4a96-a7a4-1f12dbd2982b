"use strict";
/**
 * Advanced Codessa workflow templates.
 * Provides production-quality, complete, and robust workflow definitions
 * using various agents and tools.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchStructuredTool = void 0;
exports.createRAGWorkflow = createRAGWorkflow;
exports.createCollaborativeWorkflow = createCollaborativeWorkflow;
exports.createMemoryEnhancedAgentWorkflow = createMemoryEnhancedAgentWorkflow;
exports.createCodeGenerationWorkflow = createCodeGenerationWorkflow;
exports.createResearchWorkflow = createResearchWorkflow;
const zod_1 = require("zod");
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const promptManager_1 = require("../../prompts/promptManager");
const logger_1 = require("../../logger");
const codessaMemory_1 = require("../../memory/codessa/codessaMemory");
// Base class for all enhanced tools
class BaseTool {
    // Optional dispose method for cleanup
    async dispose() {
        // Default implementation does nothing
    }
}
/**
 * Base class for enhanced tool wrappers that provide additional functionality
 * like input validation, context management, and error handling.
 */
class EnhancedToolWrapper extends BaseTool {
    baseTool;
    _context;
    _toolName;
    _baseToolHasSchema;
    _schemaValidated;
    constructor(baseTool, name = 'EnhancedTool', description = 'An enhanced tool wrapper', initialContext) {
        super(); // Call BaseTool constructor
        // Store the base tool
        this.baseTool = baseTool;
        // Store context if provided
        this._context = initialContext;
        // Initialize tool name and description
        this._toolName = name;
        // Check if the base tool has a schema property
        this._baseToolHasSchema = 'schema' in this.baseTool &&
            this.baseTool.schema !== undefined &&
            typeof this.baseTool.schema === 'object';
        // Initialize schema validation flag
        this._schemaValidated = false;
        // Initialize required ITool properties
        this.id = name.toLowerCase().replace(/\s+/g, '-');
        this.type = 'single-action';
    }
    // Helper method to create a standard ToolResult
    createToolResult(success, output, error) {
        return {
            success,
            output,
            error,
            timestamp: new Date().toISOString()
        };
    }
    // Getter for context
    get context() {
        return this._context;
    }
    // Update context
    updateContext(context) {
        this._context = context;
    }
    /**
     * Invokes the tool with the given input and options
     * @param input The input to the tool
     * @param options Optional tool context options
     * @returns The result of the tool execution
     */
    async invoke(input, options = {}) {
        try {
            // Validate input if schema is available
            if (this.schema) {
                const result = this.schema.safeParse(input);
                if (!result.success) {
                    if (options.onValidationError) {
                        options.onValidationError(result.error);
                    }
                    throw new Error(`Input validation failed: ${result.error.message}`);
                }
                input = result.data;
            }
            // Execute the tool with context if available
            const context = options.context || this._context;
            if (context) {
                return await this._call(input, context);
            }
            return await this._call(input);
        }
        catch (error) {
            logger_1.Logger.instance.error(`Error in ${this._toolName}:`, error);
            throw error;
        }
    }
    /**
     * Updates the context for the tool
     * @param context The new context to set
     */
    updateContext(context) {
        this._context = context;
    }
    /**
     * Internal call method to invoke the wrapped tool.
     * Handles different potential method names (`invoke`, `call`, `execute`).
     * Assumes the wrapped tool's method matches the schema's input type after transformation.
     * @param input - The input to the wrapper (pre-schema transformation).
     * @returns The result of the wrapped tool's execution.
     * @throws {Error} If the wrapped tool does not have a valid execution method.
     */
    /**
     * Internal method to execute the tool with the given input
     * @param input The input to the tool
     * @returns The result of the tool execution
     */
    async _call(input) {
        try {
            // Validate and transform input using the concrete schema
            const validatedInput = this.schema.parse(input);
            logger_1.Logger.instance.debug(`[${this._toolName}] Input validated/transformed.`);
            // Log context information if available
            if (this._context) {
                logger_1.Logger.instance.debug(`[${this._toolName}] Context available:`, {
                    hasWorkspace: !!this._context.workspace,
                    hasVariables: !!this._context.variables,
                    hasStreamingContext: !!this._context.streamingContext
                });
            }
            // Determine which method to call on the base tool
            let result;
            const baseTool = this.baseTool;
            // Try to call the most specific method first
            if (typeof baseTool.invoke === 'function') {
                // Pass context if the method accepts it
                result = baseTool.invoke.length >= 2
                    ? await baseTool.invoke(validatedInput, this._context)
                    : await baseTool.invoke(validatedInput);
            }
            else if (typeof baseTool.call === 'function') {
                result = baseTool.call.length >= 2
                    ? await baseTool.call(validatedInput, this._context)
                    : await baseTool.call(validatedInput);
            }
            else if (typeof baseTool.execute === 'function') {
                result = baseTool.execute.length >= 2
                    ? await baseTool.execute(validatedInput, this._context)
                    : await baseTool.execute(validatedInput);
            }
            else {
                throw new Error(`Wrapped tool '${this.name}' does not have a valid execution method. ` +
                    `Expected 'invoke', 'call', or 'execute' method.`);
            }
            logger_1.Logger.instance.debug(`[${this._toolName}] Tool execution completed successfully.`);
            // Ensure the result is in the expected GraphState format
            return this._normalizeResult(result);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.Logger.instance.error(`[${this._toolName}] Error during tool execution:`, {
                message: errorMessage,
                stack: errorStack,
                input: input
            });
            // Enhance the error with context information if available
            if (error instanceof Error && this._context) {
                error.context = {
                    toolName: this._toolName,
                    contextType: this._context.constructor?.name,
                    hasWorkspace: !!this._context.workspace,
                    timestamp: new Date().toISOString()
                };
            }
            // Re-throw the error to be handled by the workflow node
            throw error;
        }
    }
    /**
     * Creates a minimal valid GraphState with required properties
     * @param result The result to include in the state
     * @param outputs Additional outputs to include
     * @returns A valid GraphState object
     */
    _createGraphState(result, outputs = {}) {
        const baseState = {
            messages: [],
            inputs: {},
            currentNode: 'unknown',
            history: [],
            outputs: {}
        };
        // Add the main result to outputs
        const resultKey = this._toolName;
        if (result !== undefined && result !== null) {
            baseState.outputs = {
                ...baseState.outputs,
                [resultKey]: result,
                ...outputs
            };
        }
        // For string results, add to messages using a properly typed message object
        if (typeof result === 'string') {
            const messageContent = result;
            const toolName = this._toolName;
            // Create a properly typed message object that implements BaseMessage
            const toolMessage = {
                _getType: () => 'tool',
                content: messageContent,
                additional_kwargs: { name: toolName },
                toDict: () => ({
                    type: 'tool',
                    data: {
                        content: messageContent,
                        additional_kwargs: { name: toolName }
                    }
                })
            };
            baseState.messages = [
                ...(baseState.messages || []),
                toolMessage
            ];
        }
        return baseState;
    }
    /**
     * Normalizes the result from the tool to ensure it's a valid GraphState
     * @param result The raw result from the tool
     * @returns A normalized GraphState
     */
    _normalizeResult(result) {
        // If already a GraphState, ensure it has required properties
        if (result && typeof result === 'object' && 'outputs' in result) {
            const state = result;
            return {
                messages: state.messages || [],
                inputs: state.inputs || {},
                currentNode: state.currentNode || 'unknown',
                history: state.history || [],
                outputs: state.outputs || {},
                ...state
            };
        }
        // For string results, create a simple state
        if (typeof result === 'string') {
            return this._createGraphState(result, {
                [this._toolName]: result
            });
        }
        // For object results, include all properties
        if (result && typeof result === 'object') {
            return this._createGraphState(result, {
                [this._toolName]: result,
                ...result
            });
        }
        // Fallback for primitive values
        return this._createGraphState(result, {
            [this._toolName]: result,
            value: result
        });
    }
}
/**
 * Enhanced wrapper for document retrieval tools that leverages context for improved search
 */
class DocumentRetrievalStructuredTool extends EnhancedToolWrapper {
    // Implement the abstract actions property from ToolSchemaWrapper
    actions = {
        default: {
            description: 'Retrieves documents based on a query',
            inputSchema: zod_1.z.object({
                query: zod_1.z.string().describe('The search query'),
                options: zod_1.z.object({
                    limit: zod_1.z.number().int().positive().optional(),
                    minScore: zod_1.z.number().min(0).max(1).optional(),
                    includeMetadata: zod_1.z.boolean().optional(),
                    filters: zod_1.z.record(zod_1.z.any()).optional(),
                    semanticSearch: zod_1.z.boolean().optional()
                }).optional()
            })
        }
    };
    // Implement required ITool properties
    id;
    name;
    description;
    type = 'single-action';
    _defaultOptions = {
        limit: 5,
        minScore: 0.7,
        includeMetadata: true,
        semanticSearch: true
    };
    /**
     * Invokes the document retrieval with enhanced context support
     * @param input The input query or parameters
     * @param options Optional invocation options including context
     * @returns The retrieval results in a GraphState format
     */
    async invoke(input, options = {}) {
        try {
            // Update context if provided in options
            if (options.context) {
                this.updateContext(options.context);
            }
            return await super.invoke(input, options);
        }
        catch (error) {
            logger_1.Logger.instance.error('Error in DocumentRetrievalStructuredTool.invoke:', error);
            throw error;
        }
    }
    /**
     * Schema for document retrieval input
     * Can be a simple query string or an object with additional parameters
     */
    schema = zod_1.z.union([
        // Simple string query
        zod_1.z.string().describe('The query string to search for documents.'),
        // Or an object with query and options
        zod_1.z.object({
            query: zod_1.z.string().describe('The query string to search for documents.'),
            options: zod_1.z.object({
                limit: zod_1.z.number().int().positive().optional()
                    .describe('Maximum number of documents to return'),
                minScore: zod_1.z.number().min(0).max(1).optional()
                    .describe('Minimum relevance score (0-1)'),
                includeMetadata: zod_1.z.boolean().optional()
                    .describe('Whether to include document metadata'),
                filters: zod_1.z.record(zod_1.z.any()).optional()
                    .describe('Additional filters for document retrieval'),
                semanticSearch: zod_1.z.boolean().optional()
                    .describe('Whether to use semantic search (true) or keyword search (false)')
            }).optional()
        })
    ]).transform((input) => {
        // Normalize input to always return a query string
        if (typeof input === 'string') {
            return { query: input, options: {} };
        }
        return { query: input.query, options: input.options || {} };
    });
    /**
     * Creates a new DocumentRetrievalStructuredTool instance
     * @param retrievalTool The underlying retrieval tool to wrap
     * @param name Optional name for the tool (defaults to the base tool's name)
     * @param description Optional description for the tool
     * @param defaultOptions Default retrieval options
     * @param context Optional initial context
     */
    constructor(retrievalTool, name = 'DocumentRetrievalStructuredTool', description = 'Retrieves documents based on a query with contextual enhancement', defaultOptions = {}, context) {
        super(retrievalTool, name, description, context);
        // Merge provided default options with built-in defaults
        this._defaultOptions = { ...this._defaultOptions, ...defaultOptions };
        this.id = name.toLowerCase().replace(/\s+/g, '-');
        this.name = name;
        this.description = description;
        // Merge provided default options with built-in defaults
        this._defaultOptions = { ...this._defaultOptions, ...defaultOptions };
        if (!('invoke' in retrievalTool || 'call' in retrievalTool || 'execute' in retrievalTool)) {
            logger_1.Logger.instance.warn(`DocumentRetrievalStructuredTool wrapper created for tool '${this.name}' ` +
                `which lacks standard execution methods (invoke, call, execute).`);
        }
        // Bind methods to maintain proper 'this' context
        this._enhanceWithContext = this._enhanceWithContext.bind(this);
    }
    /**
     * Internal method to enhance retrieval options with context information
     * @param baseOptions Base retrieval options
     * @param context Current execution context
     * @returns Enhanced retrieval options with context
     */
    _enhanceWithContext(baseOptions, context) {
        if (!context) {
            return baseOptions;
        }
        // Create a deep copy of base options to avoid mutating the original
        const enhancedOptions = {
            ...baseOptions,
            filters: baseOptions.filters ? { ...baseOptions.filters } : {}
        };
        // Ensure filters object exists
        enhancedOptions.filters = enhancedOptions.filters || {};
        // Initialize tags array if not present
        if (!enhancedOptions.filters.tags) {
            enhancedOptions.filters.tags = [];
        }
        // Add workspace context if available
        if (context.workspace) {
            // Add current file as a filter if available
            if (context.workspace.currentFile) {
                enhancedOptions.filters.filePath = context.workspace.currentFile;
            }
            // Add workspace folders as tags
            if (context.workspace.workspaceFolders?.length) {
                const currentTags = enhancedOptions.filters.tags || [];
                enhancedOptions.filters.tags = [
                    ...currentTags,
                    'workspace',
                    ...context.workspace.workspaceFolders
                ];
            }
        }
        // Add variables context if available
        if (context.variables) {
            const currentTags = enhancedOptions.filters?.tags || [];
            const variableTags = Object.entries(context.variables)
                .filter(([_, value]) => value && typeof value === 'object')
                .map(([key]) => `var_${key}`);
            enhancedOptions.filters.tags = [
                ...currentTags,
                ...variableTags
            ];
        }
        return enhancedOptions;
    }
    /**
     * Executes the document retrieval with enhanced context handling
     * @param input The normalized input (from schema transformation)
     * @returns The retrieval results in a GraphState format
     */
    async _call(input) {
        try {
            const { query, options } = input;
            const context = this.context;
            // Merge options with defaults and enhance with context
            const mergedOptions = {
                ...this._defaultOptions,
                ...options,
                filters: { ...this._defaultOptions.filters, ...(options.filters || {}) }
            };
            const enhancedOptions = this._enhanceWithContext(mergedOptions, context);
            logger_1.Logger.instance.debug(`[DocumentRetrieval] Executing query: "${query}"`, {
                options: enhancedOptions,
                hasContext: !!context,
                contextType: context?.constructor?.name
            });
            // Execute the base tool with the enhanced options
            const baseInput = {
                query,
                ...(Object.keys(enhancedOptions).length > 0 ? { options: enhancedOptions } : {})
            };
            // Call the base tool's invoke method with context if it accepts it
            let result;
            const baseTool = this.baseTool;
            if (typeof baseTool.invoke === 'function') {
                result = baseTool.invoke.length >= 2
                    ? await baseTool.invoke(baseInput, context)
                    : await baseTool.invoke(baseInput);
            }
            else if (typeof baseTool.call === 'function') {
                result = baseTool.call.length >= 2
                    ? await baseTool.call(baseInput, context)
                    : await baseTool.call(baseInput);
            }
            else if (typeof baseTool.execute === 'function') {
                result = baseTool.execute.length >= 2
                    ? await baseTool.execute(baseInput, context)
                    : await baseTool.execute(baseInput);
            }
            else {
                throw new Error('No valid execution method found on base tool');
            }
            // Process and normalize the result
            return this._processRetrievalResult(result, query, enhancedOptions);
        }
        catch (error) {
            logger_1.Logger.instance.error('Error in DocumentRetrievalStructuredTool._call:', error);
            throw error;
        }
    }
    /**
     * Processes and normalizes the retrieval result into a valid GraphState
     * @param result The raw result from the base tool
     * @param originalQuery The original search query
     * @param options The retrieval options used
     * @returns A normalized GraphState with the retrieval results
     */
    async _processRetrievalResult(result, originalQuery, options) {
        try {
            // Normalize different result formats
            let documents = [];
            if (Array.isArray(result)) {
                // Handle array of documents
                documents = result.map(doc => {
                    if (typeof doc === 'string') {
                        return { content: doc };
                    }
                    else if (doc && typeof doc === 'object' && 'content' in doc) {
                        return {
                            content: String(doc.content),
                            metadata: doc.metadata || {}
                        };
                    }
                    return { content: JSON.stringify(doc) };
                });
            }
            else if (result && typeof result === 'object') {
                // Handle single document or object result
                const doc = result;
                documents = [{
                        content: 'content' in doc ? String(doc.content) : JSON.stringify(doc),
                        metadata: 'metadata' in doc ? doc.metadata || {} : {}
                    }];
            }
            else if (result !== undefined && result !== null) {
                // Handle primitive values
                documents = [{ content: String(result) }];
            }
            // Apply post-processing based on options
            if (options.limit && documents.length > options.limit) {
                documents = documents.slice(0, options.limit);
            }
            // Filter by score if minScore is specified
            if (options.minScore !== undefined) {
                documents = documents.filter(doc => {
                    const score = doc.metadata?.score ?? 1;
                    return Number(score) >= (options.minScore || 0);
                });
            }
            // Remove metadata if not requested
            if (!options.includeMetadata) {
                documents = documents.map(({ content }) => ({ content }));
            }
            // Create a structured result
            const formattedResults = documents.map((doc, index) => ({
                id: `doc_${index + 1}`,
                content: doc.content,
                ...(options.includeMetadata && doc.metadata ? { metadata: doc.metadata } : {})
            }));
            // Create a valid GraphState with all required properties
            return this._createGraphState(formattedResults, {
                documents: formattedResults,
                count: formattedResults.length,
                query: originalQuery,
                options: options,
                metadata: {
                    retrievalOptions: options,
                    timestamp: new Date().toISOString()
                }
            });
        }
        catch (error) {
            logger_1.Logger.instance.error('Error processing retrieval result:', error);
            // Create an error state with all required GraphState properties
            const errorMessage = error instanceof Error ? error.message : String(error);
            return this._createGraphState(null, {
                error: 'Failed to process retrieval results',
                details: errorMessage,
                metadata: {
                    error: {
                        message: 'Failed to process retrieval results',
                        cause: error,
                        timestamp: new Date().toISOString()
                    }
                }
            });
        }
    }
}
/**
 * Enhanced wrapper for search tools that leverages context for improved search
 */
class SearchStructuredTool extends EnhancedToolSchemaWrapper {
    // Implement the abstract actions property from ToolSchemaWrapper
    actions = {
        default: {
            description: 'Performs a search query',
            inputSchema: zod_1.z.object({
                query: zod_1.z.string().describe('The search query'),
                options: zod_1.z.object({
                    limit: zod_1.z.number().int().positive().optional(),
                    includeMetadata: zod_1.z.boolean().optional(),
                    filters: zod_1.z.record(zod_1.z.any()).optional()
                }).optional()
            })
        }
    };
    // Implement required ITool properties
    id;
    name;
    description;
    type = 'single-action';
    _defaultOptions;
    // Schema for input validation and transformation with proper type handling
    schema = zod_1.z.union([
        // Simple string query
        zod_1.z.string().describe('The search query string.'),
        // Or an object with query and options
        zod_1.z.object({
            query: zod_1.z.string().describe('The search query string.'),
            options: zod_1.z.object({
                limit: zod_1.z.number().int().positive().optional()
                    .describe('Maximum number of results to return'),
                includeMetadata: zod_1.z.boolean().optional()
                    .describe('Whether to include metadata in the results'),
                filters: zod_1.z.record(zod_1.z.any()).optional()
                    .describe('Additional filters for the search')
            }).optional()
        })
    ]).transform((input) => {
        // Normalize input to always return a query string and options
        if (typeof input === 'string') {
            return { query: input, options: {} };
        }
        return { query: input.query, options: input.options || {} };
    });
    /**
     * Creates a new SearchStructuredTool instance
     * @param searchTool The underlying search tool to wrap
     * @param name Optional name for the tool (defaults to the base tool's name)
     * @param description Optional description for the tool
     * @param defaultOptions Default search options
     * @param initialContext Optional initial context
     */
    constructor(searchTool, name = 'SearchStructuredTool', description = 'Performs a general search based on a query with contextual enhancement', defaultOptions = {}, initialContext) {
        super(searchTool, name, description, initialContext);
        this.id = name.toLowerCase().replace(/\s+/g, '-');
        this.name = name;
        this.description = description;
        // Initialize default options
        this._defaultOptions = {
            limit: 10,
            includeMetadata: true,
            ...defaultOptions
        };
        if (!('invoke' in searchTool || 'call' in searchTool || 'execute' in searchTool)) {
            logger_1.Logger.instance.warn(`SearchStructuredTool wrapper created for tool '${this.name}' ` +
                `which lacks standard execution methods (invoke, call, execute).`);
        }
        // Bind methods to maintain proper 'this' context
        this._enhanceWithContext = this._enhanceWithContext.bind(this);
    }
    /**
     * Invokes the search with enhanced context support
     * @param input The input query or parameters
     * @param options Optional invocation options including context
     * @returns The search results in a GraphState format
     */
    async invoke(input, options = {}) {
        try {
            // Update context if provided in options
            if (options.context) {
                this.updateContext(options.context);
            }
            return await super.invoke(input, options);
        }
        catch (error) {
            logger_1.Logger.instance.error('Error in SearchStructuredTool.invoke:', error);
            throw error;
        }
    }
    /**
     * Internal method to enhance search options with context information
     * @param baseOptions Base search options
     * @param context Current execution context
     * @returns Enhanced search options with context
     */
    _enhanceWithContext(baseOptions, context) {
        if (!context) {
            return baseOptions;
        }
        // Create a deep copy of base options to avoid mutating the original
        const enhancedOptions = {
            ...baseOptions,
            filters: baseOptions.filters ? { ...baseOptions.filters } : {}
        };
        // Ensure filters object exists
        enhancedOptions.filters = enhancedOptions.filters || {};
        // Add workspace context if available
        if (context.workspace) {
            // Add current file as a filter if available
            if (context.workspace.currentFile) {
                enhancedOptions.filters.filePath = context.workspace.currentFile;
            }
            // Add workspace folders as tags
            if (context.workspace.workspaceFolders?.length) {
                const currentTags = Array.isArray(enhancedOptions.filters.tags)
                    ? enhancedOptions.filters.tags
                    : [];
                enhancedOptions.filters.tags = [
                    ...currentTags,
                    'workspace',
                    ...context.workspace.workspaceFolders
                ];
            }
        }
        // Add variables context if available
        if (context.variables) {
            const currentTags = Array.isArray(enhancedOptions.filters.tags)
                ? enhancedOptions.filters.tags
                : [];
            const variableTags = Object.entries(context.variables)
                .filter(([_, value]) => value && typeof value === 'object')
                .map(([key]) => `var_${key}`);
            enhancedOptions.filters.tags = [
                ...currentTags,
                ...variableTags
            ];
        }
        return enhancedOptions;
    }
    /**
     * Executes the search with enhanced context handling
     * @param input The normalized input (from schema transformation)
     * @returns The search results in a GraphState format
     */
    async _call(input) {
        try {
            const { query, options } = input;
            const context = this.context;
            // Merge options with defaults and enhance with context
            const mergedOptions = {
                ...this._defaultOptions,
                ...options,
                filters: {
                    ...(this._defaultOptions.filters || {}),
                    ...(options.filters || {})
                }
            };
            const enhancedOptions = this._enhanceWithContext(mergedOptions, context);
            logger_1.Logger.instance.debug(`[Search] Executing query: "${query}"`, {
                options: enhancedOptions,
                hasContext: !!context,
                contextType: context?.constructor?.name
            });
            // Execute the base tool with the enhanced options
            const baseInput = {
                query,
                ...(Object.keys(enhancedOptions).length > 0 ? { options: enhancedOptions } : {})
            };
            // Call the base tool's invoke method with context if it accepts it
            let result;
            const baseTool = this.baseTool;
            if (typeof baseTool.invoke === 'function') {
                result = baseTool.invoke.length >= 2
                    ? await baseTool.invoke(baseInput, context)
                    : await baseTool.invoke(baseInput);
            }
            else if (typeof baseTool.call === 'function') {
                result = baseTool.call.length >= 2
                    ? await baseTool.call(baseInput, context)
                    : await baseTool.call(baseInput);
            }
            else if (typeof baseTool.execute === 'function') {
                result = baseTool.execute.length >= 2
                    ? await baseTool.execute(baseInput, context)
                    : await baseTool.execute(baseInput);
            }
            else {
                throw new Error('No valid execution method found on base tool');
            }
            // Process and normalize the result
            return this._processSearchResult(result, query, enhancedOptions);
        }
        catch (error) {
            logger_1.Logger.instance.error('Error in SearchStructuredTool._call:', error);
            throw error;
        }
    }
    /**
     * Processes and normalizes the search result
     * @param result - The raw result from the base tool
     * @param originalQuery - The original search query
     * @param options - The search options used
     * @returns A normalized GraphState with the search results
     */
    async _processSearchResult(result, originalQuery, options) {
        try {
            // Normalize different result formats
            let items = [];
            if (Array.isArray(result)) {
                // Handle array of items
                items = result.map(item => {
                    if (typeof item === 'string') {
                        return { content: item };
                    }
                    else if (item && typeof item === 'object' && 'content' in item) {
                        return {
                            content: String(item.content),
                            metadata: item.metadata || {}
                        };
                    }
                    return { content: JSON.stringify(item) };
                });
            }
            else if (result && typeof result === 'object') {
                // Handle single item or object result
                const item = result;
                items = [{
                        content: 'content' in item ? String(item.content) : JSON.stringify(item),
                        metadata: 'metadata' in item ? item.metadata || {} : {}
                    }];
            }
            else if (result !== undefined && result !== null) {
                // Handle primitive values
                items = [{ content: String(result) }];
            }
            // Apply post-processing based on options
            if (options.limit && items.length > options.limit) {
                items = items.slice(0, options.limit);
            }
            // Remove metadata if not requested
            if (!options.includeMetadata) {
                items = items.map(({ content }) => ({ content }));
            }
            // Create a structured result
            const formattedResults = items.map((item, index) => ({
                id: `result_${index + 1}`,
                content: item.content,
                ...(options.includeMetadata && item.metadata ? { metadata: item.metadata } : {})
            }));
            // Create a valid GraphState with all required properties
            return this._createGraphState(formattedResults, {
                results: formattedResults,
                count: formattedResults.length,
                query: originalQuery,
                options: options,
                metadata: {
                    searchOptions: options,
                    timestamp: new Date().toISOString()
                }
            });
        }
        catch (error) {
            logger_1.Logger.instance.error('Error processing search result:', error);
            // Create an error state with all required GraphState properties
            const errorMessage = error instanceof Error ? error.message : String(error);
            return this._createGraphState(null, {
                error: 'Failed to process search results',
                details: errorMessage,
                metadata: {
                    error: {
                        message: 'Failed to process search results',
                        cause: error,
                        timestamp: new Date().toISOString()
                    }
                }
            });
        }
    }
}
exports.SearchStructuredTool = SearchStructuredTool;
/**
 * Creates a Retrieval-Augmented Generation workflow
 * @param id - Unique identifier for the workflow
 * @param name - Display name of the workflow
 * @param description - Description of the workflow's purpose
 * @param agent - The agent instance to use for generation
 * @param retrievalTool - The retrieval tool to use for document retrieval
 * @returns A GraphDefinition representing the RAG workflow
 * @throws {Error} If required parameters are missing or invalid
 */
function createRAGWorkflow(id, name, description, agent, retrievalTool) {
    if (!id || !name || !description) {
        throw new Error('Workflow ID, name, and description are required.');
    }
    if (!agent) {
        throw new Error('Agent instance is required for RAG workflow.');
    }
    if (!retrievalTool) {
        throw new Error('Retrieval tool is required for RAG workflow.');
    }
    logger_1.Logger.instance.info(`Creating RAG workflow: ${name} (${id})`);
    // Define the workflow nodes
    const retrieveNode = {
        id: 'retrieve',
        label: 'Retrieve Documents',
        name: 'retrieve',
        type: 'tool',
        description: 'Retrieves relevant documents based on the query',
        tool: retrievalTool,
        execute: async (state) => {
            try {
                const { query } = state;
                if (!query) {
                    throw new Error('No query provided for document retrieval');
                }
                // Use the appropriate method based on what's available on the tool
                let result;
                if (typeof retrievalTool.invoke === 'function') {
                    result = await retrievalTool.invoke({ query });
                }
                else if (typeof retrievalTool.call === 'function') {
                    result = await retrievalTool.call({ query });
                }
                else if (typeof retrievalTool.execute === 'function') {
                    result = await retrievalTool.execute({ query });
                }
                else {
                    throw new Error('No valid execution method found on retrieval tool');
                }
                return { documents: result };
            }
            catch (error) {
                logger_1.Logger.instance.error('Error in retrieveNode:', error);
                throw error;
            }
        },
    };
    const generateNode = {
        id: 'generate',
        label: 'Generate Response',
        name: 'generate',
        type: 'agent',
        description: 'Generates a response using the retrieved documents',
        agent: agent,
        execute: async (state) => {
            try {
                const { query, documents } = state;
                if (!query) {
                    throw new Error('No query provided for generation');
                }
                // Prepare the prompt with retrieved documents
                const prompt = {
                    query,
                    context: documents,
                };
                // Use the appropriate method based on what's available on the agent
                let response;
                if (typeof agent.invoke === 'function') {
                    response = await agent.invoke(prompt);
                }
                else if (typeof agent.call === 'function') {
                    response = await agent.call(prompt);
                }
                else if (typeof agent.execute === 'function') {
                    response = await agent.execute(prompt);
                }
                else {
                    throw new Error('No valid execution method found on agent');
                }
                return { response };
            }
            catch (error) {
                logger_1.Logger.instance.error('Error in generateNode:', error);
                throw error;
            }
        },
    };
    // Define the edges (workflow connections)
    const edges = [
        {
            source: 'retrieve',
            target: 'generate',
            type: 'success',
            name: 'retrieve-to-generate',
            description: 'Pass retrieved documents to generation',
            condition: (state) => !!state.documents
        },
        {
            source: 'retrieve',
            target: 'generate',
            type: 'failure',
            name: 'retrieve-failure',
            description: 'Handle retrieval failure',
            condition: (state) => !state.documents || (Array.isArray(state.documents) && state.documents.length === 0)
        }
    ];
    // Return the complete workflow definition
    return {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [retrieveNode, generateNode],
        edges,
        startNodeId: 'retrieve',
        operationMode: 'document-qa',
        tags: ['rag', 'retrieval-augmented-generation', 'document-qa'],
        metadata: {
            created: new Date().toISOString(),
            lastModified: new Date().toISOString()
        }
    };
}
/**
 * Create a multi-agent collaborative workflow definition.
 * This workflow coordinates multiple specialized agents to solve complex tasks,
 * potentially routing tasks based on analysis.
 *
 * @param id - Unique ID for the workflow.
 * @param name - Human-readable name.
 * @param description - Description of the workflow's purpose.
 * @param specialistAgents - Array of specialist agents with their expertise.
 * @param supervisorAgent - The supervisor agent responsible for task analysis and integration.
 * @returns The defined GraphDefinition object.
 * @throws {Error} If required inputs are missing or invalid.
 */
function createCollaborativeWorkflow(id, name, description, specialistAgents, supervisorAgent) {
    if (!id || !name || !description)
        throw new Error('Workflow ID, name, and description are required.');
    if (!specialistAgents || specialistAgents.length === 0)
        throw new Error('At least one specialist agent is required.');
    if (!supervisorAgent)
        throw new Error('Supervisor agent is required for collaborative workflow.');
    logger_1.Logger.instance.info(`Creating Collaborative workflow: ${name} (${id})`);
    // --- Define Nodes ---
    const inputNode = graph_1.Codessa.createInputNode('input', 'Workflow Input');
    const taskAnalysisNode = {
        id: 'task-analysis', // Node ID
        type: 'agent', // Agent node type
        name: 'Task Analysis (Supervisor)', // Node name
        label: 'Task Analysis',
        agent: supervisorAgent, // The supervisor agent performs analysis
        // execute method defines the logic run by this node
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing task-analysis node with supervisor.');
            // Get the latest relevant input/message for the supervisor to analyze
            // This might be the original input or the output of a previous node, depending on graph structure
            const lastMessage = Array.isArray(state.messages) && state.messages.length > 0 ? state.messages[state.messages.length - 1] : null;
            const userQuery = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : '';
            // Create a detailed prompt for the supervisor
            const prompt = `
            You are the supervisor for a team of specialist AI agents. Your current task is to analyze a user request and route it to the appropriate specialist agent or determine if collaboration is needed.

            User Request: "${userQuery}"

            Your team consists of the following specialists:
            ${specialistAgents.map(spec => `- **${spec.name}** (ID: ${spec.id}): Expert in ${spec.expertise}`).join('\n')}

            Based on the user's request, determine the *most suitable specialist agent ID* to handle this task.
            If the task requires input or expertise from *multiple* specialists, respond with the special ID "COLLABORATE".
            If the task is unclear or cannot be routed to any specialist, respond with the special ID "CLARIFICATION_NEEDED".

            Respond ONLY with one of the specialist agent IDs, "COLLABORATE", or "CLARIFICATION_NEEDED".
            `;
            // Invoke the supervisor agent
            const analysisResult = await supervisorAgent.generate(prompt); // Assuming generate returns the agent's text response
            logger_1.Logger.instance.debug(`Supervisor analysis result: ${analysisResult}`);
            // Return the analysis result as state update for conditional routing
            return {
                // Store the supervisor's raw output
                outputs: { 'task-analysis': analysisResult },
                // Store the routing decision in a specific state key
                routing_decision: analysisResult.trim().toUpperCase(), // Normalize the decision
                // Pass the original query or last relevant message for specialist agents
                user_query: userQuery,
            };
        }
    };
    // Create specialist agent nodes dynamically
    const specialistNodes = specialistAgents.map(specialist => {
        return {
            id: `specialist_${specialist.id}`, // Unique ID for the specialist node
            type: 'agent', // Agent node type
            name: specialist.name, // Node name from specialist config
            label: `Specialist: ${specialist.name}`,
            agent: specialist.agent, // The specialist agent instance
            execute: async (state) => {
                logger_1.Logger.instance.debug(`Executing specialist agent node: ${specialist.name} (${specialist.id})`);
                // Access the user query stored by the task-analysis node
                const userQuery = state.user_query || state.inputs?.user_query || 'No query available.';
                // Create a prompt tailored for the specialist
                const prompt = `
                 You are ${specialist.name}, a specialist in ${specialist.expertise}.
                 The user requires your expertise for the following task: "${userQuery}"

                 Please provide a detailed response, analysis, or solution based on your specialization.
                 If you need assistance from other specialists (in a collaborative workflow), indicate this in your response.
                 `;
                // Invoke the specialist agent
                const specialistResponse = await specialist.agent.generate(prompt); // Assuming generate returns text
                logger_1.Logger.instance.debug(`${specialist.name} response: ${specialistResponse.substring(0, 100)}...`);
                // Store the specialist's response and return the updated state
                return {
                    ...state, // Spread existing state
                    outputs: {
                        ...(state.outputs || {}),
                        [`specialist_${specialist.id}`]: specialistResponse
                    },
                    [`specialist_response_${specialist.id}`]: specialistResponse, // Add/update specialist response
                    user_query: userQuery, // Ensure user_query is passed along
                    // Other state properties from the original state are included by ...state
                }; // Cast to GraphState to satisfy the return type
            }
        };
    });
    const integrationNode = {
        id: 'integration', // Node ID
        type: 'agent', // Agent node type (supervisor performs integration)
        name: 'Integrate Responses (Supervisor)', // Node name
        label: 'Integrate Responses',
        agent: supervisorAgent, // The supervisor agent handles integration
        // execute method for the integration logic
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing integration node with supervisor.');
            // Access the original user query and all specialist responses from the state
            const userQuery = state.user_query || 'No query available.';
            // Gather specialist responses dynamically from state keys
            const specialistResponses = specialistAgents.map(spec => {
                const response = state[`specialist_response_${spec.id}`] || 'No response provided';
                return { name: spec.name, expertise: spec.expertise, response };
            }).filter(sr => sr.response !== 'No response provided' || sr.response.trim().length > 0); // Filter out empty responses
            // Create a prompt for the supervisor to synthesize the responses
            const prompt = `
            You are the supervisor. Your task is to synthesize the responses from the specialist agents into a single, coherent, and comprehensive answer for the user.

            User Request: "${userQuery}"

            Specialist Responses:
            ${specialistResponses.map(sr => `--- ${sr.name} (${sr.expertise}) ---\n${sr.response}`).join('\n\n')}
            ---

            Please integrate these responses. If specialists provided conflicting information or identified gaps, note this and synthesize the best possible answer. Ensure the final response directly addresses the user's original request.
            `;
            // Invoke the supervisor agent to generate the integrated response
            const integratedResponse = await supervisorAgent.generate(prompt); // Assuming generate returns text
            logger_1.Logger.instance.debug('Integrated response generated.');
            // Store the final integrated response in the state
            return {
                outputs: { 'integration': integratedResponse }, // Store raw output
                final_response: integratedResponse, // Store the final response in a dedicated key
            };
        }
    };
    const clarificationNode = {
        id: 'clarification_needed', // Node ID for clarification
        type: 'input', // Changed from 'node' to 'input'
        name: 'Clarification Needed', // Node name
        label: 'Clarification Needed',
        // execute logic for clarification
        execute: async () => {
            logger_1.Logger.instance.debug('Executing clarification_needed node.');
            // This node signifies that the supervisor determined clarification is needed.
            // We'll generate a simple response asking the user for more info.
            const message = 'I need more information to understand your request. Could you please provide more details?';
            logger_1.Logger.instance.info('Clarification requested.');
            return {
                outputs: { 'clarification_needed': message },
                final_response: message, // Store the clarification message
            };
        }
    };
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Workflow Output');
    // --- Define Edges ---
    const edges = [
        // Start from input, go to task analysis
        { source: inputNode.id, target: taskAnalysisNode.id, type: 'default', name: 'input_to_taskAnalysis' }
    ];
    // Conditional edges from task analysis based on routing decision
    specialistAgents.forEach(spec => {
        edges.push({
            source: taskAnalysisNode.id,
            target: `specialist_${spec.id}`,
            type: 'default',
            name: `taskAnalysis_to_specialist_${spec.id}`,
            condition: async (state) => {
                const routingDecision = state.routing_decision?.toUpperCase() || '';
                return routingDecision === spec.id.toUpperCase();
            }
        });
    });
    // Edge from task analysis to integration if collaboration is needed
    edges.push({
        source: taskAnalysisNode.id,
        target: integrationNode.id,
        type: 'default',
        name: 'taskAnalysis_to_integration',
        condition: async (state) => {
            const routingDecision = state.routing_decision?.toUpperCase() || '';
            return routingDecision === 'COLLABORATE';
        }
    });
    // Edge from task analysis to clarification node if clarification is needed
    edges.push({
        source: taskAnalysisNode.id,
        target: clarificationNode.id,
        type: 'default',
        name: 'taskAnalysis_to_clarification',
        condition: async (state) => {
            const routingDecision = state.routing_decision?.toUpperCase() || '';
            return routingDecision === 'CLARIFICATION_NEEDED';
        }
    });
    // Edges from each specialist node back to the integration node
    // If a specialist finishes, they pass their response to the integration step
    specialistAgents.forEach(spec => {
        edges.push({
            source: `specialist_${spec.id}`,
            target: integrationNode.id,
            type: 'default',
            name: `specialist_${spec.id}_to_integration`
        });
    });
    // Edges from integration and clarification nodes to the final output
    edges.push({ source: integrationNode.id, target: outputNode.id, type: 'default', name: 'integration_to_output' }, { source: clarificationNode.id, target: outputNode.id, type: 'default', name: 'clarification_to_output' });
    // --- Create Workflow Definition ---
    const workflowDefinition = {
        id,
        name,
        description,
        version: '1.0.0',
        operationMode: 'multi-agent', // OperationMode for collaborative workflow
        nodes: [
            inputNode,
            taskAnalysisNode,
            ...specialistNodes, // Include all dynamically created specialist nodes
            integrationNode,
            clarificationNode, // Include clarification node
            outputNode
        ],
        edges,
        startNodeId: inputNode.id // The workflow starts at the input node
    };
    // Register workflow definition
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflowDefinition);
    logger_1.Logger.instance.info(`Collaborative workflow '${name}' (${id}) created and registered.`);
    return workflowDefinition;
}
/**
 * Create a memory-enhanced agent workflow definition with long-term recall and saving.
 * This workflow integrates memory retrieval *before* the agent runs
 * and memory saving *after* the agent responds.
 *
 * @param id - Unique ID for the workflow.
 * @param name - Human-readable name.
 * @param description - Description of the workflow's purpose.
 * @param agent - The agent instance to be enhanced with memory.
 * @returns The defined GraphDefinition object.
 * @throws {Error} If required inputs are missing or invalid.
 */
function createMemoryEnhancedAgentWorkflow(id, name, description, agent) {
    if (!id || !name || !description)
        throw new Error('Workflow ID, name, and description are required.');
    if (!agent)
        throw new Error('Agent instance is required for memory-enhanced workflow.');
    // ... (rest of the code remains the same)
    logger_1.Logger.instance.info(`Creating Memory-Enhanced Agent workflow: ${name} (${id})`);
    // --- Define Nodes ---
    const inputNode = graph_1.Codessa.createInputNode('input', 'Workflow Input');
    // Create memory tools
    const memoryRetrievalTool = {
        id: 'memory_retriever',
        name: 'memory_retriever',
        description: 'Retrieves memories based on a query with contextual enhancement',
        type: 'single-action',
        singleActionSchema: zod_1.z.object({
            query: zod_1.z.string().describe('The query to search for in memory')
        }),
        execute: async (_actionName, input, _context) => {
            const query = input.query || '';
            try {
                // Build comprehensive search options using full context
                const baseFilter = {
                    source: undefined,
                    type: ['text', 'code', 'conversation', 'insight'],
                    tags: [],
                    filePath: undefined,
                    changeType: undefined
                };
                // Create search options with proper typing
                const searchOptions = {
                    query,
                    limit: 20,
                    threshold: 0.7,
                    includeMetadata: true,
                    contextual: true,
                    filter: baseFilter
                };
                // Helper function to safely update filter
                const updateFilter = (updates) => {
                    if (!searchOptions.filter) {
                        searchOptions.filter = baseFilter;
                    }
                    searchOptions.filter = { ...searchOptions.filter, ...updates };
                };
                // Use workspace context for file-specific and project-specific memory retrieval
                if (_context?.workspace) {
                    // Use current file for precise context
                    if (_context.workspace.currentFile) {
                        updateFilter({ filePath: _context.workspace.currentFile });
                    }
                    // Use workspace folders for project-level context
                    if (_context.workspace.workspaceFolders && _context.workspace.workspaceFolders.length > 0) {
                        updateFilter({
                            tags: [...(searchOptions.filter?.tags || []), 'workspace', _context.workspace.workspaceFolders[0]]
                        });
                    }
                    // Use text selection for more targeted search
                    if (_context.workspace.selection?.text) {
                        // Enhance query with selected text context
                        searchOptions.query = `${query} ${_context.workspace.selection.text}`;
                    }
                }
                // Use file path context if available (more general than currentFile)
                if (_context?.filePath) {
                    updateFilter({
                        filePath: _context.filePath,
                        tags: [...(searchOptions.filter?.tags || []), 'file_context']
                    });
                }
                // Use debug context for error-related memory retrieval
                if (_context?.debugData) {
                    if (_context.debugData.lastError) {
                        // Include error message in query for better context
                        searchOptions.query = `${query} error: ${_context.debugData.lastError.message}`;
                        updateFilter({
                            tags: [...(searchOptions.filter?.tags || []), 'error_context', 'debug']
                        });
                    }
                    // Use analysis timestamp for temporal filtering
                    if (_context.debugData.analysisTimestamp) {
                        updateFilter({
                            fromTimestamp: _context.debugData.analysisTimestamp - (24 * 60 * 60 * 1000), // 24 hours before analysis
                            toTimestamp: Date.now()
                        });
                    }
                }
                // Use variables from context for enhanced metadata filtering
                if (_context?.variables) {
                    const contextTags = ['context_variables'];
                    // Extract meaningful tags from available managers
                    if (_context.variables.workflowManager)
                        contextTags.push('workflow_context');
                    if (_context.variables.mcpManager)
                        contextTags.push('mcp_context');
                    if (_context.variables.promptManager)
                        contextTags.push('prompt_context');
                    if (_context.variables.knowledgebaseManager)
                        contextTags.push('knowledge_context');
                    // Add any additional context variables as tags
                    Object.keys(_context.variables).forEach(key => {
                        if (!['workflowManager', 'mcpManager', 'promptManager', 'knowledgebaseManager'].includes(key)) {
                            contextTags.push(`var_${key}`);
                        }
                    });
                    updateFilter({
                        tags: [...(searchOptions.filter?.tags || []), ...contextTags]
                    });
                }
                // Use streaming context if available
                if (_context?.streamingContext) {
                    updateFilter({
                        tags: [...(searchOptions.filter?.tags || []), 'streaming_context', _context.streamingContext.streamId]
                    });
                    // Check for cancellation during memory search
                    if (_context.streamingContext.cancellationToken?.isCancellationRequested) {
                        return {
                            success: false,
                            error: 'Memory retrieval cancelled',
                            toolId: 'memory_retriever'
                        };
                    }
                }
                // Use tools context for tool-related memory
                if (_context?.tools && _context.tools.size > 0) {
                    updateFilter({
                        tags: [...(searchOptions.filter?.tags || []), 'tools_context', ...Array.from(_context.tools.keys())]
                    });
                }
                // Use cancellation token if available
                if (_context?.cancellationToken?.isCancellationRequested) {
                    return {
                        success: false,
                        error: 'Memory retrieval cancelled',
                        toolId: 'memory_retriever'
                    };
                }
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories(searchOptions);
                const memoryContent = memories.map(m => {
                    const metadata = m.metadata;
                    return `[${new Date(m.timestamp).toISOString()}] ${metadata.filePath ? `File: ${metadata.filePath}` : ''} ${metadata.tags ? `Tags: ${metadata.tags.join(', ')}` : ''}\n${m.content}`;
                }).join('\n\n');
                return {
                    success: true,
                    output: memoryContent,
                    toolId: 'memory_retriever',
                    metadata: {
                        memoriesFound: memories.length,
                        contextUsed: {
                            workspace: !!_context?.workspace,
                            debugData: !!_context?.debugData,
                            variables: !!_context?.variables,
                            filePath: !!_context?.filePath,
                            streaming: !!_context?.streamingContext,
                            tools: !!_context?.tools
                        }
                    }
                };
            }
            catch (error) {
                logger_1.Logger.instance.error('Error retrieving memories:', error);
                return {
                    success: false,
                    error: 'Failed to retrieve memories',
                    toolId: 'memory_retriever'
                };
            }
        }
    };
    const memorySaveTool = {
        id: 'memory_saver',
        name: 'memory_saver',
        description: 'Saves content to memory',
        type: 'single-action',
        singleActionSchema: zod_1.z.object({
            content: zod_1.z.string().describe('The content to save to memory'),
            metadata: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional().describe('Optional metadata to associate with the memory')
        }),
        execute: async (_actionName, input, _context) => {
            const content = input.content || '';
            const metadata = input.metadata || {};
            // Use context to enhance metadata if available
            const enhancedMetadata = {
                source: 'system',
                type: 'text',
                timestamp: Date.now(),
                // Add context information if available
                ...(!!_context && {
                    hasContext: true,
                    contextType: _context.constructor?.name || 'AgentContext'
                }),
                ...metadata
            };
            try {
                const memory = await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content,
                    metadata: enhancedMetadata
                });
                return {
                    success: true,
                    output: `Memory saved successfully with ID: ${memory.id}`,
                    toolId: 'memory_saver'
                };
            }
            catch (error) {
                logger_1.Logger.instance.error('Error saving memory:', error);
                return {
                    success: false,
                    error: 'Failed to save memory',
                    toolId: 'memory_saver'
                };
            }
        }
    };
    // Wrap tools if they aren't already StructuredTools
    const memoryRetrievalStructuredTool = memoryRetrievalTool instanceof StructuredTool
        ? memoryRetrievalTool
        // Create a minimal schema wrapper if the tool isn't structured
        : new (class extends ToolSchemaWrapper {
            schema = zod_1.z.object({ query: zod_1.z.string() }).transform((obj) => obj.query);
            constructor(baseTool) { super(baseTool, 'memory_retriever'); }
        })(memoryRetrievalTool);
    const memorySaveStructuredTool = memorySaveTool instanceof StructuredTool
        ? memorySaveTool
        // Create a minimal schema wrapper if the tool isn't structured
        : new (class extends ToolSchemaWrapper {
            schema = zod_1.z.object({ content: zod_1.z.string(), metadata: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional() });
            constructor(baseTool) { super(baseTool, 'memory_saver'); }
        })(memorySaveTool);
    const memoryRetrievalNode = graph_1.Codessa.createToolNode('memory-retrieval', // Node ID
    'Memory Retrieval', // Node name
    memoryRetrievalStructuredTool // The StructuredTool instance for retrieval
    );
    const contextEnhancementNode = {
        id: 'context-enhancement', // Node ID
        type: 'tool', // Changed from 'node' to 'tool'
        name: 'Enhance Context with Memories', // Node name
        label: 'Enhance Context',
        // execute method defines the logic
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing context-enhancement node.');
            // Get the results from the 'memory-retrieval' node's output
            const retrievedMemories = state.outputs?.['memory-retrieval']; // Access using node ID
            // Format retrieved memories into a string context for the agent
            let enhancedContext = '';
            if (typeof retrievedMemories === 'string' && retrievedMemories.length > 0) {
                enhancedContext = `
 Relevant insights from past interactions:
 ---
 ${retrievedMemories}
 ---
 Use these insights to inform your response and maintain continuity.
 `;
            }
            else if (Array.isArray(retrievedMemories) && retrievedMemories.length > 0) {
                // Assume retrievedMemories is an array of formatted strings or objects with 'content'
                enhancedContext = `
 Relevant insights from past interactions:
 ---
 ${retrievedMemories.map((mem, index) => {
                    const content = typeof mem === 'string' ? mem : (typeof mem === 'object' && mem !== null && 'content' in mem) ? String(mem.content) : JSON.stringify(mem);
                    return `Memory ${index + 1}: ${content}`;
                }).join('\n\n')}
 ---
 Use these insights to inform your response and maintain continuity.
 `;
            }
            else {
                enhancedContext = ''; // No memories found, provide empty context
                logger_1.Logger.instance.debug('Context enhancement received no retrieved memories.');
            }
            // Store the enhanced context in the state for the agent node
            return {
                enhanced_context: enhancedContext, // Store formatted context
                // Optional: Directly add to a 'context' field if agent expects it there
                context: enhancedContext,
            };
        }
    };
    const agentNode = {
        id: 'agent', // Node ID
        type: 'agent', // Agent node type
        name: 'Memory Agent', // Node name
        label: 'Memory Agent',
        agent: agent, // The agent instance
        // execute logic for the agent
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing memory-enhanced agent node.');
            // Get the original user query and the enhanced context
            const messages = state.messages || [];
            const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
            const userQuery = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : '';
            const enhancedContext = state.enhanced_context || state.context || ''; // Get context from state
            // Construct the prompt for the agent using promptManager
            const prompt = promptManager_1.promptManager.renderPrompt('workflow.memoryAgent', {
                enhancedContext,
                userQuery
            });
            // Invoke the agent's generation method
            const agentResponse = await agent.generate(prompt); // Assuming generate returns text
            logger_1.Logger.instance.debug('Memory agent generated response.');
            // Return the agent's response as node output and in state
            return {
                outputs: { agent: agentResponse },
                agent_response: agentResponse,
            };
        }
    };
    const memorySaveNode = graph_1.Codessa.createToolNode('memory-save', // Node ID
    'Memory Save', // Node name
    memorySaveStructuredTool // The StructuredTool instance for saving
    );
    // A custom node might be needed *before* memory-save to format the content/metadata to save
    const prepareMemorySaveNode = {
        id: 'prepare-memory-save',
        type: 'tool', // Changed from 'node' to 'tool'
        name: 'Prepare Memory Content',
        label: 'Prepare Memory Content',
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing prepare-memory-save node.');
            // Get the original user query and the agent's response
            const messages = state.messages || [];
            const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
            const userQuery = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : '';
            const agentResponse = state.agent_response || state.outputs?.agent || ''; // Get agent's response
            // Decide what content and metadata to save to memory
            // This is a crucial step - extract key information or conversation summary
            const memoryContent = `Conversation Summary:\nUser: "${userQuery.substring(0, 200)}..."\nAgent: "${agentResponse.substring(0, 200)}..."`;
            // Prepare metadata for the memory entry
            const memoryMetadata = {
                source: 'conversation', // Source type
                type: 'dialogue_summary', // Specific type
                userQuerySnippet: userQuery.substring(0, 50), // Snippet for quick identification
                agentResponseSnippet: agentResponse.substring(0, 50),
                timestamp: Date.now(),
                // Add other relevant context like session ID, user ID if available in state
                // sessionId: state.sessionId,
                // userId: state.userId,
            };
            // Store the prepared input for the 'memory-save' tool node
            return {
                outputs: {
                    'prepare-memory-save': {
                        content: memoryContent,
                        metadata: memoryMetadata
                    }
                },
                // Store the prepared content/metadata in state for potential later use/debugging
                memory_to_save_content: memoryContent,
                memory_to_save_metadata: memoryMetadata,
            };
        }
    };
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Workflow Output');
    // --- Define Edges ---
    const edges = [
        { source: inputNode.id, target: memoryRetrievalNode.id, type: 'default', name: `${inputNode.id}-to-${memoryRetrievalNode.id}` }, // Input -> Retrieval
        { source: memoryRetrievalNode.id, target: contextEnhancementNode.id, type: 'default', name: `${memoryRetrievalNode.id}-to-${contextEnhancementNode.id}` }, // Retrieval Results -> Context Enhancement
        { source: contextEnhancementNode.id, target: agentNode.id, type: 'default', name: `${contextEnhancementNode.id}-to-${agentNode.id}` }, // Enhanced Context -> Agent
        // Agent output goes to prepare for saving
        { source: agentNode.id, target: prepareMemorySaveNode.id, type: 'default', name: `${agentNode.id}-to-${prepareMemorySaveNode.id}` },
        // Prepared content goes to memory saving tool
        { source: prepareMemorySaveNode.id, target: memorySaveNode.id, type: 'default', name: `${prepareMemorySaveNode.id}-to-${memorySaveNode.id}` },
        // Memory save result goes to final output
        { source: memorySaveNode.id, target: outputNode.id, type: 'default', name: `${memorySaveNode.id}-to-${outputNode.id}` }
    ];
    // --- Create Workflow Definition ---
    const workflowDefinition = {
        id,
        name,
        description,
        version: '1.0.0',
        operationMode: 'memory', // OperationMode for memory-enhanced workflow
        nodes: [
            inputNode,
            memoryRetrievalNode,
            contextEnhancementNode,
            agentNode,
            prepareMemorySaveNode, // Include the preparation node
            memorySaveNode,
            outputNode
        ],
        edges,
        startNodeId: inputNode.id // The workflow starts at the input node
    };
    // Register workflow definition
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflowDefinition);
    logger_1.Logger.instance.info(`Memory-Enhanced Agent workflow '${name}' (${id}) created and registered.`);
    return workflowDefinition;
}
/**
 * Create a code generation and review workflow definition.
 * This workflow orchestrates specialized agents for iterative code creation and review.
 *
 * @param id - Unique ID for the workflow.
 * @param name - Human-readable name.
 * @param description - Description of the workflow's purpose.
 * @param codeGenerationAgent - The agent instance specialized in code generation.
 * @param codeReviewAgent - The agent instance specialized in code review.
 * @returns The defined GraphDefinition object.
 * @throws {Error} If required inputs are missing or invalid.
 */
function createCodeGenerationWorkflow(id, name, description, codeGenerationAgent, codeReviewAgent) {
    if (!id || !name || !description)
        throw new Error('Workflow ID, name, and description are required.');
    if (!codeGenerationAgent)
        throw new Error('Code generation agent is required.');
    if (!codeReviewAgent)
        throw new Error('Code review agent is required.');
    logger_1.Logger.instance.info(`Creating Code Generation workflow: ${name} (${id})`);
    // --- Define Nodes ---
    const inputNode = graph_1.Codessa.createInputNode('input', 'Workflow Input (Requirements)');
    const requirementsAnalysisNode = {
        id: 'requirements-analysis',
        type: 'agent',
        name: 'Analyze Requirements',
        label: 'Analyze Requirements',
        agent: codeGenerationAgent, // Code generation agent can also analyze requirements
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing requirements-analysis node.');
            const messages = state.messages || [];
            const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
            const userRequest = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : state.inputs?.input || ''; // Get input
            const prompt = `
            You are an AI assistant specializing in analyzing software requirements.
            Analyze the following user request and break it down into a clear, structured set of requirements for a code generation agent.

            User Request: "${typeof userRequest === 'string' ? userRequest : JSON.stringify(userRequest)}"

            Provide the requirements in a structured format, including:
            - Programming Language: (e.g., Python, TypeScript, Java)
            - Core Functionality: Detailed description of what the code should do.
            - Inputs: What data does the code take?
            - Outputs: What data does the code produce?
            - Constraints & Edge Cases: Any specific limitations, performance needs, security considerations, or unusual scenarios to handle.
            - Desired Output Format: (e.g., just the code, code with explanations, specific file structure)

            Format your response clearly so it can be directly used by a code generator.
            `;
            const analysisResult = await codeGenerationAgent.generate(prompt);
            logger_1.Logger.instance.debug('Requirements analysis generated.');
            return {
                outputs: { 'requirements-analysis': analysisResult },
                requirements: analysisResult, // Store the analysis in state
                user_request: userRequest, // Pass original request
            };
        }
    };
    const codeGenerationNode = {
        id: 'code-generation',
        type: 'agent',
        name: 'Generate Code',
        label: 'Generate Code',
        agent: codeGenerationAgent,
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing code-generation node.');
            const requirements = state.requirements || 'No requirements provided.';
            const prompt = `
            You are an AI code generation expert. Write code based on the following requirements.
            Aim for clean, readable, and efficient code. Include comments where necessary.

            Requirements:
            ---
            ${requirements}
            ---

            Provide the generated code.
            `;
            const generatedCode = await codeGenerationAgent.generate(prompt);
            logger_1.Logger.instance.debug('Code generated.');
            return {
                outputs: { 'code-generation': generatedCode },
                generated_code: generatedCode, // Store generated code
                requirements: requirements, // Pass requirements along
                user_request: state.user_request, // Pass user request along
            };
        }
    };
    const codeReviewNode = {
        id: 'code-review',
        type: 'agent',
        name: 'Review Code',
        label: 'Review Code',
        agent: codeReviewAgent,
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing code-review node.');
            const generatedCode = state.generated_code || 'No code provided.';
            const requirements = state.requirements || 'No requirements provided.';
            const prompt = `
            You are an AI code reviewer. Review the following code against the provided requirements.

            Requirements:
            ---
            ${requirements}
            ---

            Generated Code:
            ---
            ${generatedCode}
            ---

            Critique the code based on:
            1. Correctness (Does it meet requirements?)
            2. Efficiency & Performance
            3. Readability & Documentation
            4. Adherence to Best Practices & Idioms (for the specified language)
            5. Potential Security Vulnerabilities

            Provide constructive feedback and clear suggestions for improvement. If the code looks good, state that clearly.
            `;
            const reviewResult = await codeReviewAgent.generate(prompt);
            logger_1.Logger.instance.debug('Code review completed.');
            return {
                outputs: { 'code-review': reviewResult },
                code_review: reviewResult, // Store the review result
                generated_code: generatedCode, // Pass code along
                requirements: requirements, // Pass requirements along
                user_request: state.user_request, // Pass user request along
            };
        }
    };
    // Custom node to decide if refinement is needed based on the review
    const reviewDecisionNode = {
        id: 'review-decision',
        type: 'process', // Changed from 'conditional' to 'process'
        name: 'Decide Refinement',
        label: 'Decide Refinement',
        // execute determines the next step based on state (the review result)
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing review-decision node.');
            const codeReview = state.code_review || '';
            // Analyze the review to decide if refinement is necessary.
            // This is a simplified heuristic. A real implementation might use an LLM call here
            // or a more sophisticated parser.
            // Look for keywords indicating issues or requests for changes.
            const needsRefinement = codeReview.toLowerCase().includes('suggestion') ||
                codeReview.toLowerCase().includes('consider') ||
                codeReview.toLowerCase().includes('improve') ||
                codeReview.toLowerCase().includes('change') ||
                codeReview.toLowerCase().includes('feedback') ||
                codeReview.toLowerCase().includes('issue');
            if (needsRefinement) {
                logger_1.Logger.instance.info('Code review indicates refinement is needed. Routing to code-refinement.');
                return 'code-refinement'; // Return the target node ID
            }
            else {
                logger_1.Logger.instance.info('Code review indicates no significant issues. Routing directly to output.');
                return 'output'; // Return the target node ID
            }
        }
    };
    const codeRefinementNode = {
        id: 'code-refinement',
        type: 'agent',
        name: 'Refine Code',
        label: 'Refine Code',
        agent: codeGenerationAgent, // Code generation agent refines based on review
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing code-refinement node.');
            const generatedCode = state.generated_code || 'No code provided.';
            const codeReview = state.code_review || 'No review provided.';
            const requirements = state.requirements || 'No requirements provided.'; // Pass requirements to refinement prompt
            const prompt = `
            You are an AI code expert tasked with refining code based on feedback.

            Original Code:
            ---
            ${generatedCode}
            ---

            Code Review Feedback:
            ---
            ${codeReview}
            ---

            Requirements:
            ---
            ${requirements}
            ---

            Carefully address the feedback from the code review. Provide the improved code, and include a brief summary of the changes you made.
            `;
            const refinedResult = await codeGenerationAgent.generate(prompt); // Assuming generates refined code + summary
            logger_1.Logger.instance.debug('Code refinement completed.');
            return {
                outputs: { 'code-refinement': refinedResult },
                final_code_result: refinedResult, // Store the final result (code + summary)
            };
        }
    };
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Workflow Output');
    // --- Define Edges ---
    const edges = [
        // Start from input, go to requirements analysis
        { source: inputNode.id, target: requirementsAnalysisNode.id, type: 'default', name: 'input-to-requirementsAnalysis' },
        // Analysis -> Generation
        { source: requirementsAnalysisNode.id, target: codeGenerationNode.id, type: 'default', name: 'requirementsAnalysis-to-codeGeneration' },
        // Generation -> Review
        { source: codeGenerationNode.id, target: codeReviewNode.id, type: 'default', name: 'codeGeneration-to-codeReview' },
        // Review -> Decision node (conditional routing)
        { source: codeReviewNode.id, target: reviewDecisionNode.id, type: 'default', name: 'codeReview-to-reviewDecision' },
        // Conditional edges from Decision node
        { source: reviewDecisionNode.id, target: codeRefinementNode.id, type: 'default', name: 'reviewDecision-to-codeRefinement', condition: async (state) => Promise.resolve(state.next_node_id === 'code-refinement') },
        { source: reviewDecisionNode.id, target: outputNode.id, type: 'default', name: 'reviewDecision-to-output', condition: async (state) => Promise.resolve(state.next_node_id === 'output') },
        // Refinement -> Output
        { source: codeRefinementNode.id, target: outputNode.id, type: 'default', name: 'codeRefinement-to-output' },
    ];
    // The reviewDecisionNode execute method should explicitly return the ID of the next node.
    // Update the execute method of reviewDecisionNode to return string ID:
    // execute: async (state: any): Promise<string> => { ... return 'code-refinement' | 'output'; }
    // And the edge conditions should check state.outputs['review-decision'] or state.next_node_id
    // Let's refactor reviewDecisionNode execute to return the string ID directly
    reviewDecisionNode.execute = async (state) => {
        logger_1.Logger.instance.debug('Executing review-decision node.');
        const codeReview = state.code_review || '';
        // Analyze the review to decide if refinement is necessary.
        // Look for keywords indicating issues or requests for changes.
        const needsRefinement = codeReview.toLowerCase().includes('suggestion') ||
            codeReview.toLowerCase().includes('consider') ||
            codeReview.toLowerCase().includes('improve') ||
            codeReview.toLowerCase().includes('change') ||
            codeReview.toLowerCase().includes('feedback') ||
            codeReview.toLowerCase().includes('issue');
        if (needsRefinement) {
            logger_1.Logger.instance.info('Code review indicates refinement is needed. Routing to code-refinement.');
            return 'code-refinement'; // Return the target node ID
        }
        else {
            logger_1.Logger.instance.info('Code review indicates no significant issues. Routing directly to output.');
            return 'output'; // Return the target node ID
        }
    };
    // Adjust the conditional edges to use the execute method's return value
    // Codessa handles this transition based on the string returned by execute for conditional nodes.
    // The 'condition' property is not needed for the node that itself determines the next state.
    // Remove the conditional edges above and rely on the reviewDecisionNode.execute return value.
    // This means we need to remove the `type: 'conditional'` from edges leaving the reviewDecisionNode
    // and update the execute method return type to `Promise<string>`.
    // Remove redundant conditional edges and redefine based on node.execute returning string:
    edges.splice(edges.findIndex(e => e.source === reviewDecisionNode.id && e.target === codeRefinementNode.id), 1);
    edges.splice(edges.findIndex(e => e.source === reviewDecisionNode.id && e.target === outputNode.id), 1);
    // Add new edges where the decision node has no defined target type, relying on execute.
    // Codessa interprets a non-default, non-conditional edge from a node whose execute
    // returns a string as a transition to that string's ID.
    edges.push({ source: reviewDecisionNode.id, target: codeRefinementNode.id, type: 'default', name: `${reviewDecisionNode.id}->${codeRefinementNode.id}` }, // Added type 'default' and name
    { source: reviewDecisionNode.id, target: outputNode.id, type: 'default', name: `${reviewDecisionNode.id}->${outputNode.id}` });
    // --- Create Workflow Definition ---
    const workflowDefinition = {
        id,
        name,
        description,
        version: '1.0.0',
        operationMode: 'codegen', // OperationMode for code generation workflow
        nodes: [
            inputNode,
            requirementsAnalysisNode,
            codeGenerationNode,
            codeReviewNode,
            reviewDecisionNode, // Include the decision node
            codeRefinementNode,
            outputNode
        ],
        edges,
        startNodeId: inputNode.id // The workflow starts at the input node
    };
    // Register workflow definition
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflowDefinition);
    logger_1.Logger.instance.info(`Code Generation workflow '${name}' (${id}) created and registered.`);
    return workflowDefinition;
}
/**
 * Create an autonomous research agent workflow definition.
 * This workflow coordinates an agent with a search tool to research a topic,
 * gather, analyze, and synthesize information.
 *
 * @param id - Unique ID for the workflow.
 * @param name - Human-readable name.
 * @param description - Description of the workflow's purpose.
 * @param agent - The agent instance responsible for analysis and synthesis.
 * @param searchTool - The tool to use for searching information.
 * @returns The defined GraphDefinition object.
 * @throws {Error} If required inputs are missing or invalid.
 */
function createResearchWorkflow(id, name, description, agent, searchTool) {
    if (!id || !name || !description)
        throw new Error('Workflow ID, name, and description are required.');
    if (!agent)
        throw new Error('Agent instance is required for research workflow.');
    if (!searchTool)
        throw new Error('Search tool is required for research workflow.');
    logger_1.Logger.instance.info(`Creating Research workflow: ${name} (${id})`);
    // Ensure the search tool is properly wrapped
    const wrappedSearchTool = searchTool instanceof SearchStructuredTool
        ? searchTool
        : new SearchStructuredTool(searchTool, 'SearchTool', 'Performs web searches'); // Wrap if not already wrapped
    // --- Define Nodes ---
    const inputNode = graph_1.Codessa.createInputNode('input', 'Workflow Input (Research Topic)');
    const topicAnalysisNode = {
        id: 'topic-analysis',
        type: 'agent',
        name: 'Analyze Topic',
        label: 'Analyze Topic',
        agent: agent, // Agent performs analysis
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing topic-analysis node.');
            const messages = state.messages || [];
            const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
            const userTopic = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : state.inputs?.input || ''; // Get input
            if (typeof userTopic !== 'string' || userTopic.trim().length === 0) {
                const errorMsg = 'Research topic input is missing or empty.';
                logger_1.Logger.instance.error(errorMsg);
                // Route to a failure or output node indicating the error
                return { error: errorMsg, next_node_id: 'output' }; // Use next_node_id for routing
            }
            const prompt = promptManager_1.promptManager.renderPrompt('workflow.topicAnalysis', {
                topic: userTopic
            });
            const analysisResult = await agent.generate(prompt); // Assuming generate returns text
            logger_1.Logger.instance.debug('Topic analysis generated.');
            // Attempt to extract search queries from the analysis result.
            // This is a simple heuristic; a more robust solution would use an LLM to parse the analysis.
            const extractedQueries = [];
            const lines = analysisResult.split('\n');
            lines.forEach(line => {
                // Simple pattern matching for lines that look like search queries
                const match = line.match(/Search Queries?:?\s*["']?([^"']+)["']?/i);
                if (match && match[1]) {
                    extractedQueries.push(match[1].trim());
                }
                else {
                    // Fallback: if analysis didn't yield structured queries, use generic ones
                    // This fallback might overwrite carefully extracted ones, improve extraction logic.
                    if (line.includes('search query') || line.includes('investigate')) {
                        const potentialQuery = line.replace(/.*(search query|investigate)[:-]?\s*/i, '').trim();
                        if (potentialQuery.length > 5 && potentialQuery.length < 100) { // Basic length check
                            extractedQueries.push(potentialQuery);
                        }
                    }
                }
            });
            // Ensure at least one query if extraction failed
            if (extractedQueries.length === 0) {
                logger_1.Logger.instance.warn('Failed to extract specific search queries from analysis. Using fallback queries.');
                extractedQueries.push(userTopic, `latest research on ${userTopic}`, `${userTopic} challenges and solutions`);
            }
            // Limit the number of queries to avoid excessive tool calls
            const limitedQueries = extractedQueries.slice(0, 5); // Limit to 5 queries
            return {
                outputs: { 'topic-analysis': analysisResult }, // Store analysis text
                research_plan: analysisResult, // Store plan in state
                search_queries: limitedQueries, // Store extracted queries for next step
                user_topic: userTopic, // Pass original topic
            };
        }
    };
    // Create information gathering node
    graph_1.Codessa.createToolNode('information-gathering', // Node ID
    'Gather Information', // Node name
    searchStructuredTool // The StructuredTool instance for searching
    );
    // Custom node to process search queries iteratively
    const processQueriesNode = {
        id: 'process-queries',
        type: 'tool', // Changed from 'node' to 'tool'
        name: 'Process Search Queries',
        label: 'Process Search Queries',
        // This node will iterate through the `search_queries` state variable.
        // execute will trigger the 'information-gathering' tool for each query.
        // It needs to manage the list of queries to process and aggregate results.
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing process-queries node.');
            const queriesToProcess = state.search_queries || [];
            const allSearchResults = state.all_search_results || []; // Aggregate results
            if (queriesToProcess.length === 0) {
                logger_1.Logger.instance.info('No search queries left to process. Routing to information-analysis.');
                // No more queries, signal transition to analysis
                return { next_node_id: 'information-analysis', all_search_results: allSearchResults };
            }
            // Take the next query from the list
            const currentQuery = queriesToProcess[0];
            const remainingQueries = queriesToProcess.slice(1);
            logger_1.Logger.instance.debug(`Processing query: "${currentQuery}"`);
            let currentResult;
            try {
                // Search for information (using the wrapped search tool)
                const searchResults = await wrappedSearchTool.invoke({
                    query: currentQuery,
                    options: { limit: 10 }
                }); // Use the wrapper's invoke
                currentResult = typeof searchResults === 'string' ? searchResults : JSON.stringify(searchResults);
                logger_1.Logger.instance.debug(`Search result for "${currentQuery.substring(0, 50)}...": ${currentResult.substring(0, 100)}...`);
            }
            catch (error) {
                logger_1.Logger.instance.error(`Error during search tool execution for "${currentQuery}":`, error);
                currentResult = `Error: ${error instanceof Error ? error.message : String(error)}`;
            }
            // Add the result to the aggregated results
            allSearchResults.push({ query: currentQuery, result: currentResult });
            // Update state with remaining queries and aggregated results
            // Signal self-loop back to process-queries node if more queries remain
            if (remainingQueries.length > 0) {
                logger_1.Logger.instance.debug(`Queries remaining (${remainingQueries.length}). Looping back to process-queries.`);
                return {
                    search_queries: remainingQueries, // Update with remaining queries
                    all_search_results: allSearchResults, // Update with aggregated results
                    user_topic: state.user_topic, // Pass topic along
                    research_plan: state.research_plan, // Pass plan along
                    // next_node_id is implicitly this node ('process-queries') if execute doesn't return a string ID
                    // or we can explicitly set it for clarity if allowed by Codessa polyfill
                    // next_node_id: 'process-queries' // Uncomment if needed and supported
                };
            }
            else {
                logger_1.Logger.instance.info('All search queries processed. Routing to information-analysis.');
                // All queries processed, signal transition to information-analysis
                return {
                    search_queries: [], // Empty the query list
                    all_search_results: allSearchResults, // Final aggregated results
                    user_topic: state.user_topic, // Pass topic along
                    research_plan: state.research_plan, // Pass plan along
                    next_node_id: 'information-analysis' // Explicitly route
                };
            }
        }
    };
    const informationAnalysisNode = {
        id: 'information-analysis',
        type: 'agent',
        name: 'Analyze Information',
        label: 'Analyze Information',
        agent: agent, // Agent performs analysis
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing information-analysis node.');
            const searchResults = state.all_search_results || []; // Get aggregated results
            const userTopic = state.user_topic || 'the topic'; // Get original topic
            const researchPlan = state.research_plan || ''; // Get research plan
            // Format search results for analysis prompt
            const formattedResults = searchResults.map((sr, index) => `--- Search Result ${index + 1} (Query: "${sr.query.substring(0, 100)}...") ---\n${sr.result}\n---`).join('\n\n');
            const prompt = `
            You are an AI assistant specializing in synthesizing research findings.
            Analyze the following search results related to "${userTopic}".

            Search Results:
            ---
            ${formattedResults}
            ---

            Research Plan/Goals:
            ---
            ${researchPlan}
            ---

            Based on the search results and the research plan:
            1. Extract the most important facts, insights, and key takeaways.
            2. Identify any recurring themes, common viewpoints, or significant findings.
            3. Note any contradictions, inconsistencies, or differing perspectives found across the sources.
            4. Identify any obvious gaps in the information where the search results were insufficient or unclear.
            5. Briefly comment on the apparent relevance or reliability of the sources if possible from the text.

            Provide your analysis in a clear, structured format.
            `;
            const analysisResult = await agent.generate(prompt); // Assuming generate returns text
            logger_1.Logger.instance.debug('Information analysis generated.');
            return {
                outputs: { 'information-analysis': analysisResult },
                information_analysis: analysisResult, // Store the analysis in state
                user_topic: userTopic, // Pass topic along
                research_plan: researchPlan, // Pass plan along
            };
        }
    };
    const findingsSynthesisNode = {
        id: 'findings-synthesis',
        type: 'agent',
        name: 'Synthesize Findings',
        label: 'Synthesize Findings',
        agent: agent, // Agent performs synthesis
        execute: async (state) => {
            logger_1.Logger.instance.debug('Executing findings-synthesis node.');
            const userTopic = state.user_topic || 'the research topic'; // Get original topic
            const informationAnalysis = state.information_analysis || 'No analysis provided.';
            const researchPlan = state.research_plan || ''; // Pass plan along
            const prompt = `
            You are an AI research reporter. Your task is to synthesize the conducted research and analysis into a comprehensive report.

            Original Research Topic: "${userTopic}"

            Research Plan/Goals:
            ---
            ${researchPlan}
            ---

            Information Analysis:
            ---
            ${informationAnalysis}
            ---

            Create a well-structured research report that:
            1. Provides an executive summary of the key findings.
            2. Presents the main insights, organized logically (e.g., by sub-question from the plan or by theme).
            3. Discusses any limitations, conflicting information, or remaining gaps identified during analysis.
            4. Concludes with implications or suggestions for further research if appropriate.

            Format your report with clear headings and sections.
            `;
            const synthesisResult = await agent.generate(prompt); // Assuming generate returns text
            logger_1.Logger.instance.debug('Findings synthesis completed.');
            return {
                outputs: { 'findings-synthesis': synthesisResult },
                final_research_report: synthesisResult, // Store the final report
            };
        }
    };
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Workflow Output');
    // --- Define Edges ---
    const edges = [
        // Start from input, go to topic analysis
        { name: 'input-to-topic-analysis', source: inputNode.id, target: topicAnalysisNode.id, type: 'default' },
        // Analysis -> Process Queries node (initiates the search loop)
        { name: 'topic-analysis-to-process-queries', source: topicAnalysisNode.id, target: processQueriesNode.id, type: 'default' },
        // Process Queries -> Information Analysis (conditional exit from loop, handled by execute returning ID)
        // { name: 'process-queries-to-information-analysis', source: processQueriesNode.id, target: informationAnalysisNode.id, type: 'conditional' } // Not needed if execute returns ID
        // Information Analysis -> Synthesis
        { name: 'information-analysis-to-synthesis', source: informationAnalysisNode.id, target: findingsSynthesisNode.id, type: 'default' },
        // Synthesis -> Output
        { name: 'synthesis-to-output', source: findingsSynthesisNode.id, target: outputNode.id, type: 'default' }
    ];
    // Note: The self-loop within 'process-queries' is handled by its execute method
    // returning no explicit next_node_id when more queries remain, or returning
    // 'information-analysis' when done. The 'information-gathering' tool node is
    // called *within* the 'process-queries' node's execute method in this design.
    // --- Create Workflow Definition ---
    const workflowDefinition = {
        id,
        name,
        description,
        version: '1.0.0',
        operationMode: 'research', // OperationMode for research workflow
        nodes: [
            inputNode,
            topicAnalysisNode,
            processQueriesNode, // Include the iterative processing node
            // informationGatheringNode, // Note: informationGatheringNode is called *from* processQueriesNode's execute, not a separate graph node transition in this design.
            informationAnalysisNode,
            findingsSynthesisNode,
            outputNode
        ],
        edges,
        startNodeId: inputNode.id // The workflow starts at the input node
    };
    // Register workflow definition
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflowDefinition);
    logger_1.Logger.instance.info(`Research workflow '${name}' (${id}) created and registered.`);
    return workflowDefinition;
}
//# sourceMappingURL=advancedTemplates.js.map