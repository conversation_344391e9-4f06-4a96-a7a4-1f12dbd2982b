"use strict";
/**
 * Advanced Code Refactoring Workflows
 *
 * This module provides specialized workflow templates for advanced code refactoring:
 * - Pattern-based refactoring
 * - Architecture refactoring
 * - Performance optimization
 * - Technical debt reduction
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPatternRefactoringWorkflow = createPatternRefactoringWorkflow;
exports.createTechnicalDebtWorkflow = createTechnicalDebtWorkflow;
const managers_1 = require("src/managers");
const logger_1 = require("../../logger");
const codessaMemory_1 = require("../../memory/codessa/codessaMemory");
/**
 * Creates a pattern-based refactoring workflow
 * This workflow helps with refactoring code based on design patterns:
 * 1. Analyzing the code structure
 * 2. Identifying applicable patterns
 * 3. Applying the patterns
 * 4. Validating the refactored code
 */
function createPatternRefactoringWorkflow(agent, options = {}) {
    const name = options.name || 'Pattern-Based Refactoring Workflow';
    const description = options.description || 'Workflow for refactoring code using design patterns';
    // Create base workflow
    const workflow = {
        id: `pattern-refactoring-workflow-${Date.now()}`,
        name,
        description,
        version: '1.0.0',
        operationMode: 'pattern-refactoring',
        type: 'pattern-refactoring',
        nodes: [
            {
                id: 'input',
                type: 'input',
                name: 'Input',
                label: 'Input'
            },
            {
                id: 'output',
                type: 'output',
                name: 'Output',
                label: 'Output'
            }
        ],
        edges: [
            {
                source: 'input',
                target: 'output',
                type: 'pattern-refactoring',
                name: 'Default Flow'
            }
        ],
        startNodeId: 'input'
    };
    // Add specialized nodes for pattern-based refactoring
    const analyzeCodeNode = {
        id: 'analyze_code',
        type: 'tool',
        name: 'Analyze Code Structure',
        label: 'Analyze Code Structure',
        execute: async (state) => {
            logger_1.Logger.instance.info('Analyzing code structure for pattern-based refactoring');
            // Use memory to retrieve relevant code patterns if available
            let codePatterns = [];
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query: 'code patterns design patterns',
                    limit: 5
                });
                if (memories && memories.length > 0) {
                    codePatterns = memories.map((m) => m.content);
                    logger_1.Logger.instance.info(`Retrieved ${codePatterns.length} pattern examples from memory`);
                }
            }
            catch (error) {
                logger_1.Logger.instance.warn('Failed to retrieve patterns from memory:', error);
            }
            // Simulated code analysis result
            return {
                outputs: {
                    analyze_code: {
                        codeStructure: {
                            classes: ['UserService', 'UserController', 'UserRepository'],
                            dependencies: [
                                { from: 'UserController', to: 'UserService' },
                                { from: 'UserService', to: 'UserRepository' }
                            ],
                            patterns: {
                                current: ['Repository'],
                                potential: ['Factory', 'Strategy', 'Observer']
                            }
                        }
                    }
                },
                messages: [
                    new managers_1.ToolMessage('analyze_code', {}, JSON.stringify({
                        codeStructure: {
                            classes: ['UserService', 'UserController', 'UserRepository'],
                            dependencies: [
                                { from: 'UserController', to: 'UserService' },
                                { from: 'UserService', to: 'UserRepository' }
                            ],
                            patterns: {
                                current: ['Repository'],
                                potential: ['Factory', 'Strategy', 'Observer']
                            }
                        }
                    }))
                ]
            };
        }
    };
    const identifyPatternsNode = {
        id: 'identify_patterns',
        type: 'agent',
        name: 'Identify Applicable Patterns',
        label: 'Identify Applicable Patterns',
        agent,
        execute: async (state) => {
            logger_1.Logger.instance.info('Identifying applicable design patterns');
            const codeStructure = state.outputs?.analyze_code?.codeStructure;
            // Generate pattern recommendations
            const patternRecommendations = [
                {
                    pattern: 'Factory',
                    reason: 'Simplify object creation for UserService',
                    impact: 'High',
                    complexity: 'Medium'
                },
                {
                    pattern: 'Strategy',
                    reason: 'Allow different user authentication strategies',
                    impact: 'Medium',
                    complexity: 'Low'
                }
            ];
            // Use the agent to generate a detailed explanation
            const prompt = `Analyze the following code structure and recommend design patterns:
            ${JSON.stringify(codeStructure, null, 2)}`;
            const explanation = await agent.generate(prompt);
            return {
                outputs: {
                    identify_patterns: {
                        recommendations: patternRecommendations,
                        explanation
                    }
                },
                messages: [
                    new managers_1.AIMessage(`I've identified the following design patterns that could improve the code:

1. **Factory Pattern** - Simplify object creation for UserService
   - Impact: High
   - Complexity: Medium

2. **Strategy Pattern** - Allow different user authentication strategies
   - Impact: Medium
   - Complexity: Low

${explanation}`)
                ]
            };
        }
    };
    const applyPatternsNode = {
        id: 'apply_patterns',
        type: 'agent',
        name: 'Apply Design Patterns',
        label: 'Apply Design Patterns',
        agent,
        execute: async (state) => {
            logger_1.Logger.instance.info('Applying design patterns');
            const recommendations = state.outputs?.identify_patterns?.recommendations;
            // Save pattern recommendations to memory for future reference
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(recommendations),
                    metadata: {
                        source: 'system',
                        type: 'pattern',
                        timestamp: new Date().toISOString(),
                        tags: ['design-pattern', 'refactoring']
                    }
                });
                logger_1.Logger.instance.info('Saved pattern recommendations to memory');
            }
            catch (error) {
                logger_1.Logger.instance.warn('Failed to save pattern recommendations to memory:', error);
            }
            // Generate refactored code
            const prompt = `Apply the following design patterns to the code:
            ${JSON.stringify(recommendations, null, 2)}`;
            const refactoredCode = await agent.generate(prompt);
            return {
                outputs: {
                    apply_patterns: {
                        refactoredCode,
                        patternsApplied: recommendations.map((r) => r.pattern)
                    }
                },
                messages: [
                    new managers_1.AIMessage(`I've applied the recommended design patterns to the code:

\`\`\`typescript
${refactoredCode}
\`\`\`

The code now implements the Factory and Strategy patterns.`)
                ]
            };
        }
    };
    const validateCodeNode = {
        id: 'validate_code',
        type: 'tool',
        name: 'Validate Refactored Code',
        label: 'Validate Refactored Code',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Validating refactored code');
            // Simulated validation result
            const validationResult = {
                success: true,
                tests: {
                    passed: 15,
                    failed: 0,
                    skipped: 2
                },
                performance: {
                    before: {
                        executionTime: '245ms',
                        memoryUsage: '32MB'
                    },
                    after: {
                        executionTime: '198ms',
                        memoryUsage: '28MB'
                    }
                }
            };
            return {
                outputs: {
                    validate_code: validationResult
                },
                messages: [
                    new managers_1.ToolMessage('validate_code', {}, JSON.stringify(validationResult))
                ]
            };
        }
    };
    // Add nodes to workflow
    workflow.nodes.push(analyzeCodeNode);
    workflow.nodes.push(identifyPatternsNode);
    workflow.nodes.push(applyPatternsNode);
    workflow.nodes.push(validateCodeNode);
    // Add edges to connect the nodes
    workflow.edges = [
        {
            source: 'input',
            target: 'analyze_code',
            type: 'pattern-refactoring',
            name: 'To Code Analysis'
        },
        {
            source: 'analyze_code',
            target: 'identify_patterns',
            type: 'pattern-refactoring',
            name: 'To Pattern Identification'
        },
        {
            source: 'identify_patterns',
            target: 'apply_patterns',
            type: 'pattern-refactoring',
            name: 'To Pattern Application'
        },
        {
            source: 'apply_patterns',
            target: 'validate_code',
            type: 'pattern-refactoring',
            name: 'To Code Validation'
        },
        {
            source: 'validate_code',
            target: 'output',
            type: 'pattern-refactoring',
            name: 'To Output'
        }
    ];
    return workflow;
}
/**
 * Creates a technical debt reduction workflow
 * This workflow helps with identifying and reducing technical debt:
 * 1. Analyzing the codebase for technical debt
 * 2. Prioritizing debt items
 * 3. Refactoring to reduce debt
 * 4. Validating improvements
 */
function createTechnicalDebtWorkflow(agent, options = {}) {
    const name = options.name || 'Technical Debt Reduction Workflow';
    const description = options.description || 'Workflow for identifying and reducing technical debt';
    // Create base workflow
    const workflow = {
        id: `tech-debt-workflow-${Date.now()}`,
        name,
        description,
        version: '1.0.0',
        operationMode: 'technical-debt',
        type: 'technical-debt',
        nodes: [
            {
                id: 'input',
                type: 'input',
                name: 'Input',
                label: 'Input'
            },
            {
                id: 'output',
                type: 'output',
                name: 'Output',
                label: 'Output'
            }
        ],
        edges: [
            {
                source: 'input',
                target: 'output',
                type: 'technical-debt',
                name: 'Default Flow'
            }
        ],
        startNodeId: 'input'
    };
    // Add specialized nodes for technical debt reduction
    const analyzeDebtNode = {
        id: 'analyze_debt',
        type: 'tool',
        name: 'Analyze Technical Debt',
        label: 'Analyze Technical Debt',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Analyzing codebase for technical debt');
            // Simulated technical debt analysis
            return {
                outputs: {
                    analyze_debt: {
                        debtItems: [
                            {
                                type: 'Duplicate Code',
                                location: 'src/utils/helpers.ts',
                                severity: 'High',
                                effort: 'Medium'
                            },
                            {
                                type: 'Complex Method',
                                location: 'src/services/dataProcessor.ts:processData',
                                severity: 'High',
                                effort: 'High'
                            },
                            {
                                type: 'Outdated Dependencies',
                                location: 'package.json',
                                severity: 'Medium',
                                effort: 'Low'
                            }
                        ],
                        metrics: {
                            codeSmells: 24,
                            duplications: '15%',
                            technicalDebtRatio: '0.8%',
                            coverage: '65%'
                        }
                    }
                },
                messages: [
                    new managers_1.ToolMessage('analyze_debt', {}, JSON.stringify({
                        debtItems: [
                            {
                                type: 'Duplicate Code',
                                location: 'src/utils/helpers.ts',
                                severity: 'High',
                                effort: 'Medium'
                            },
                            {
                                type: 'Complex Method',
                                location: 'src/services/dataProcessor.ts:processData',
                                severity: 'High',
                                effort: 'High'
                            },
                            {
                                type: 'Outdated Dependencies',
                                location: 'package.json',
                                severity: 'Medium',
                                effort: 'Low'
                            }
                        ],
                        metrics: {
                            codeSmells: 24,
                            duplications: '15%',
                            technicalDebtRatio: '0.8%',
                            coverage: '65%'
                        }
                    }))
                ]
            };
        }
    };
    const prioritizeDebtNode = {
        id: 'prioritize_debt',
        type: 'agent',
        name: 'Prioritize Technical Debt',
        label: 'Prioritize Technical Debt',
        agent,
        execute: async (state) => {
            logger_1.Logger.instance.info('Prioritizing technical debt items');
            const debtItems = state.outputs?.analyze_debt?.debtItems;
            // Generate prioritized debt items
            const prompt = `Prioritize the following technical debt items:
            ${JSON.stringify(debtItems, null, 2)}`;
            const prioritization = await agent.generate(prompt);
            // Simulated prioritized items
            const prioritizedItems = [
                {
                    type: 'Complex Method',
                    location: 'src/services/dataProcessor.ts:processData',
                    severity: 'High',
                    effort: 'High',
                    priority: 1,
                    reason: 'High impact on maintainability and performance'
                },
                {
                    type: 'Duplicate Code',
                    location: 'src/utils/helpers.ts',
                    severity: 'High',
                    effort: 'Medium',
                    priority: 2,
                    reason: 'Affects multiple parts of the codebase'
                },
                {
                    type: 'Outdated Dependencies',
                    location: 'package.json',
                    severity: 'Medium',
                    effort: 'Low',
                    priority: 3,
                    reason: 'Security implications but relatively easy to fix'
                }
            ];
            return {
                outputs: {
                    prioritize_debt: {
                        prioritizedItems,
                        explanation: prioritization
                    }
                },
                messages: [
                    new managers_1.AIMessage(`I've prioritized the technical debt items:

1. **Complex Method** in src/services/dataProcessor.ts:processData
   - High impact on maintainability and performance

2. **Duplicate Code** in src/utils/helpers.ts
   - Affects multiple parts of the codebase

3. **Outdated Dependencies** in package.json
   - Security implications but relatively easy to fix

${prioritization}`)
                ]
            };
        }
    };
    const refactorCodeNode = {
        id: 'refactor_code',
        type: 'agent',
        name: 'Refactor Code',
        label: 'Refactor Code',
        agent,
        execute: async (state) => {
            logger_1.Logger.instance.info('Refactoring code to reduce technical debt');
            const prioritizedItems = state.outputs?.prioritize_debt?.prioritizedItems;
            const topItem = prioritizedItems[0];
            // Generate refactored code for the top priority item
            const prompt = `Refactor the following code to reduce technical debt:
            ${JSON.stringify(topItem, null, 2)}`;
            const refactoredCode = await agent.generate(prompt);
            return {
                outputs: {
                    refactor_code: {
                        item: topItem,
                        refactoredCode,
                        changes: [
                            {
                                file: topItem.location,
                                type: 'Refactor',
                                description: `Refactored ${topItem.type}`
                            }
                        ]
                    }
                },
                messages: [
                    new managers_1.AIMessage(`I've refactored the complex method in src/services/dataProcessor.ts:

\`\`\`typescript
${refactoredCode}
\`\`\`

The refactoring breaks down the complex method into smaller, more focused functions with clear responsibilities.`)
                ]
            };
        }
    };
    const validateImprovementsNode = {
        id: 'validate_improvements',
        type: 'tool',
        name: 'Validate Improvements',
        label: 'Validate Improvements',
        execute: async (_state) => {
            logger_1.Logger.instance.info('Validating technical debt improvements');
            // Simulated validation result
            const validationResult = {
                success: true,
                metrics: {
                    before: {
                        codeSmells: 24,
                        duplications: '15%',
                        technicalDebtRatio: '0.8%',
                        coverage: '65%'
                    },
                    after: {
                        codeSmells: 18,
                        duplications: '12%',
                        technicalDebtRatio: '0.6%',
                        coverage: '68%'
                    }
                },
                improvements: {
                    codeSmells: '-25%',
                    duplications: '-20%',
                    technicalDebtRatio: '-25%',
                    coverage: '+4.6%'
                }
            };
            return {
                outputs: {
                    validate_improvements: validationResult
                },
                messages: [
                    new managers_1.ToolMessage('validate_improvements', {}, JSON.stringify(validationResult))
                ]
            };
        }
    };
    // Add nodes to workflow
    workflow.nodes.push(analyzeDebtNode);
    workflow.nodes.push(prioritizeDebtNode);
    workflow.nodes.push(refactorCodeNode);
    workflow.nodes.push(validateImprovementsNode);
    // Add edges to connect the nodes
    workflow.edges = [
        {
            source: 'input',
            target: 'analyze_debt',
            type: 'technical-debt',
            name: 'To Debt Analysis'
        },
        {
            source: 'analyze_debt',
            target: 'prioritize_debt',
            type: 'technical-debt',
            name: 'To Debt Prioritization'
        },
        {
            source: 'prioritize_debt',
            target: 'refactor_code',
            type: 'technical-debt',
            name: 'To Code Refactoring'
        },
        {
            source: 'refactor_code',
            target: 'validate_improvements',
            type: 'technical-debt',
            name: 'To Validation'
        },
        {
            source: 'validate_improvements',
            target: 'output',
            type: 'technical-debt',
            name: 'To Output'
        }
    ];
    return workflow;
}
// No need to re-export, they're already exported above
//# sourceMappingURL=advancedRefactoring.js.map