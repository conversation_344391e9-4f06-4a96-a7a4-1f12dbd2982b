{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../../src/memory/codessa/logger.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,yCAAoD;AAEpD;;GAEG;AACU,QAAA,MAAM,GAAG,eAAU,CAAC", "sourcesContent": ["/**\n * Logger utility for Codessa memory system\n */\n\nimport { logger as mainLogger } from '../../logger';\n\n/**\n * Re-export the main logger for use in memory components\n */\nexport const logger = mainLogger;\n"]}