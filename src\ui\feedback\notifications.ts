import * as vscode from 'vscode';
import { Logger } from '../../logger';
import { getConfig } from '../../config';

// List of error message patterns to suppress
const SUPPRESSED_ERROR_PATTERNS = [
  // Local provider connection errors
  'Failed to fetch Code Llama models',
  'LM Studio connection test failed',
  'Failed to initialize Codessa memory provider',
  'Connection refused',
  'connect ECONNREFUSED',
  'timeout',
  'ETIMEDOUT',
  'ESOCKETTIMEDOUT',
  'Failed to connect to lmstudio provider',
  'Failed to connect to ollama provider',
  'Failed to list models for provider',
  'Failed to initialize',
  'ECONNRESET',
  'socket hang up',
  'ECONNABORTED',
  'network error',
  'network timeout',
  'Failed to fetch models',
  'Failed to load models',
  'Could not connect to',
  'Unable to connect to',
  'Connection error',
  'Connection failed',
  'No connection',
  'Not running',
  'is not running',
  'is not available',
  'is not responding',
  'is offline',
  'could not be reached',
  'unreachable',
  'not reachable',
  'Failed to import',
  'Module not found',
  'Cannot find module',
  'Failed to load module',
  'Failed to resolve module',
  'Failed to open settings panel',
  'Failed to open AllSettingsPanel'
];

/**
 * Show an information message to the user
 * @param message The message to show
 * @param items Optional items to include as buttons
 * @returns A promise that resolves to the selected item or undefined
 */
export function showInformationMessage(message: string, ...items: string[]): Thenable<string | undefined> {
  return vscode.window.showInformationMessage(message, ...items);
}

/**
 * Show an error message to the user
 * @param message The message to show
 * @param items Optional items to include as buttons
 * @param options Additional options for showing the error
 * @returns A promise that resolves to the selected item or undefined
 */
export function showErrorMessage(
  message: string,
  ...items: string[]
): Thenable<string | undefined> {
  // Check if we should suppress this error message
  if (shouldSuppressError(message)) {
    // Log the suppressed error but don't show it to the user
    Logger.instance.debug(`Suppressed error notification: ${message}`);
    return Promise.resolve(undefined);
  }

  return vscode.window.showErrorMessage(message, ...items);
}

/**
 * Show a warning message to the user
 * @param message The message to show
 * @param items Optional items to include as buttons
 * @returns A promise that resolves to the selected item or undefined
 */
export function showWarningMessage(message: string, ...items: string[]): Thenable<string | undefined> {
  // Check if we should suppress this warning message
  if (shouldSuppressError(message)) {
    // Log the suppressed warning but don't show it to the user
    Logger.instance.debug(`Suppressed warning notification: ${message}`);
    return Promise.resolve(undefined);
  }

  return vscode.window.showWarningMessage(message, ...items);
}

/**
 * Determines if an error message should be suppressed based on its content and provider configuration
 * @param message The error message to check
 * @returns True if the message should be suppressed, false otherwise
 */
export function shouldSuppressError(message: string): boolean {
  // Get the suppression setting
  const suppressProviderErrors = getConfig<boolean>('suppressProviderErrors', true);

  if (!suppressProviderErrors) {
    return false;
  }

  // Get enabled providers from settings
  const config = vscode.workspace.getConfiguration('codessa.llm');
  const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];

  // Always suppress errors for disabled providers
  const allProviders = [
    'ollama', 'lmstudio', 'openai', 'anthropic', 'googleai', 'mistralai', 'cohere', 'deepseek',
    'openrouter', 'huggingface', 'starcoder', 'codellama', 'replit', 'wizardcoder',
    'xwincoder', 'phi', 'yicode', 'codegemma', 'santacoder', 'stablecode', 'perplexity'
  ];

  const disabledProviders = allProviders.filter(provider => !enabledProviders.includes(provider));

  // Check if the error is from a disabled provider
  for (const provider of disabledProviders) {
    if (message.toLowerCase().includes(provider.toLowerCase())) {
      Logger.instance.debug(`Suppressing error for disabled provider ${provider}: ${message}`);
      return true;
    }
  }

  // Specifically suppress all LM Studio errors if LM Studio is not enabled
  if (message.toLowerCase().includes('lm studio') || message.toLowerCase().includes('lmstudio')) {
    if (!enabledProviders.includes('lmstudio')) {
      Logger.instance.debug(`Suppressing LM Studio error (provider disabled): ${message}`);
      return true;
    }
  }

  // Always suppress errors for local providers during startup/initialization
  if (message.toLowerCase().includes('ollama') || message.toLowerCase().includes('lmstudio')) {
    // Check if this is an explicit user action or automatic initialization
    // If the message contains "test" or "check", it's likely from a user-initiated test
    const isUserInitiatedTest = message.toLowerCase().includes('test') ||
                                   message.toLowerCase().includes('check') ||
                                   message.toLowerCase().includes('manual') ||
                                   message.toLowerCase().includes('explicit');

    if (!isUserInitiatedTest) {
      // Suppress automatic initialization errors for local providers
      Logger.instance.debug(`Suppressing automatic initialization error for local provider: ${message}`);
      return true;
    }
  }

  // Suppress errors for disabled providers
  if (message.toLowerCase().includes('provider') &&
        (message.toLowerCase().includes('disabled') ||
         message.toLowerCase().includes('not configured') ||
         message.toLowerCase().includes('not enabled'))) {
    Logger.instance.debug(`Suppressing provider configuration error: ${message}`);
    return true;
  }

  // Suppress connection errors for providers that are not enabled
  const connectionErrorPatterns = [
    'connection refused', 'connection timed out', 'failed to connect',
    'econnrefused', 'etimedout', 'esockettimedout', 'network error',
    'connection error', 'timeout', 'unreachable', 'is it running',
    'please check if', 'make sure', 'server at', 'localhost:1234'
  ];

  if (connectionErrorPatterns.some(pattern => message.toLowerCase().includes(pattern))) {
    // Check if this error is for a disabled provider
    for (const provider of disabledProviders) {
      if (message.toLowerCase().includes(provider.toLowerCase())) {
        Logger.instance.debug(`Suppressing connection error for disabled provider ${provider}: ${message}`);
        return true;
      }
    }
  }

  // Check if the message contains any of the suppressed patterns
  const shouldSuppress = SUPPRESSED_ERROR_PATTERNS.some(pattern =>
    message.toLowerCase().includes(pattern.toLowerCase())
  );

  if (shouldSuppress) {
    Logger.instance.debug(`Suppressing error matching pattern: ${message}`);
  }

  return shouldSuppress;
}

/**
 * Show a detailed error message with an option to view more details
 * @param message The main error message
 * @param details The detailed error information
 * @param buttons Additional buttons to show
 * @returns A promise that resolves to the selected item or undefined
 */
export async function showDetailedErrorMessage(
  message: string,
  details?: string,
  buttons: string[] = []
): Promise<string | undefined> {
  // Check if we should suppress this error message
  if (shouldSuppressError(message)) {
    // Log the suppressed error but don't show it to the user
    Logger.instance.debug(`Suppressed detailed error notification: ${message}`);
    if (details) {
      Logger.instance.debug(`Suppressed error details: ${details}`);
    }
    return Promise.resolve(undefined);
  }

  const items = [...buttons];
  if (details) {
    items.push('Show Details');
  }

  const selection = await vscode.window.showErrorMessage(message, ...items);

  if (selection === 'Show Details' && details) {
    const detailsDoc = await vscode.workspace.openTextDocument({
      content: details,
      language: 'text'
    });
    await vscode.window.showTextDocument(detailsDoc);
  }

  return selection;
}
