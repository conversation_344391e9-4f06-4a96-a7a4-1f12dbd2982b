"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedCodeEditingTool = void 0;
const vscode = __importStar(require("vscode"));
const events_1 = require("events");
const perf_hooks_1 = require("perf_hooks");
// Core framework imports
const toolFramework_1 = require("./toolFramework");
class AdvancedCodeEditingTool extends toolFramework_1.AITerminalTool {
    // Public readonly properties
    name = 'AdvancedCodeEditing';
    description = 'Advanced file editing tool for precise code modifications with automatic backups, integrity checks, and full framework capabilities';
    version = '1.0.0';
    category = 'Development';
    // Private properties
    toolEvents;
    memorySchema;
    activeOperations = new Map();
    operationHistory = [];
    // Service dependencies
    logger;
    fileSystem;
    workspaceKnowledge;
    memoryOperations;
    aiContext;
    constructor(terminalSession, aiContext, toolContext, services) {
        super({ terminalSession, aiContext, toolContext });
        // Initialize service dependencies
        this.logger = services.logger || console;
        this.fileSystem = services.fileSystem;
        this.workspaceKnowledge = services.workspace || null;
        this.memoryOperations = services.memoryManager;
        this.aiContext = aiContext;
        // Initialize event emitter and memory schema
        this.toolEvents = new events_1.EventEmitter();
        this.memorySchema = this.createMemorySchema();
        // Initialize file system watchers and AI patterns
        this.initializeFileSystemWatchers();
        this.initializeAIPatterns();
    }
    // Implement required abstract method from AITerminalTool
    async executeAIOperation(context, input) {
        try {
            this.logger.info('Executing AI operation', { input });
            // Implement actual AI operation logic here
            return { success: true, result: 'Operation completed' };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error in executeAIOperation', { error: errorMessage });
            throw error;
        }
    }
    /**
     * Analyze a code snippet and provide optimization suggestions
     * @param codeSnippet The code to analyze
     * @param language The programming language of the code
     */
    async analyzeCodeSnippet(codeSnippet, language = 'typescript', context) {
        const startTime = perf_hooks_1.performance.now();
        try {
            // Use the AI context to analyze the code
            const analysis = await this.aiContext.analyzeCode({
                code: codeSnippet,
                language,
                features: ['complexity', 'optimization', 'security']
            });
            const duration = perf_hooks_1.performance.now() - startTime;
            const metadata = {
                duration,
                language,
                complexity: analysis.complexity || 0,
                suggestions: analysis.suggestions?.length || 0
            };
            // Log the successful analysis
            await this.logOperation('analyzeCodeSnippet', 'snippet', true, undefined);
            // Store detailed analysis in memory
            await this.memoryManager.storeMemory({
                id: `analysis:${Date.now()}`,
                content: JSON.stringify({
                    codeSnippet,
                    analysis,
                    metadata
                }),
                metadata: {
                    type: 'code_analysis',
                    source: 'ai_analysis',
                    timestamp: Date.now(),
                    ...metadata
                }
            });
            return {
                originalCode: codeSnippet,
                suggestedCode: analysis.suggestedCode || codeSnippet,
                description: analysis.summary || 'No specific issues found',
                complexity: analysis.complexity || 0,
                improvements: analysis.suggestions?.map(s => s.description) || []
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            await this.logOperation('analyzeCodeSnippet', 'snippet', false, errorMessage);
            // Return a basic analysis result in case of errors
            return {
                originalCode: codeSnippet,
                suggestedCode: codeSnippet,
                description: 'Error during analysis: ' + errorMessage,
                complexity: 0,
                improvements: []
            };
        }
    }
    activeOperations = new Map();
    terminalEvents;
    memorySchema;
    toolEvents;
    constructor(terminalSession, aiContext, toolContext, services) {
        super({ terminalSession, aiContext, toolContext });
        this.logger = services.logger || console;
        this.fileSystem = services.fileSystem;
        this.workspaceKnowledge = services.workspace;
        this.memoryOperations = services.memoryManager;
        this.toolEvents = new events_1.EventEmitter();
        // Initialize memory schema
        this.memorySchema = this.createMemorySchema();
        // Initialize file system watchers
        this.initializeFileSystemWatchers();
        // Initialize AI patterns
        this.initializeAIPatterns();
        // Initialize memory schema
        this.memorySchema = {
            type: types_1.MemoryType.CodeEdit,
            source: types_1.MemorySource.Tool,
            metadata: {
                toolName: this.name,
                version: this.version,
                categories: ['code', 'refactoring', 'optimization']
            }
        };
        // Initialize AI patterns and capabilities
        this.initializeAIPatterns();
        this.initializeFileSystemWatchers();
        // Set up terminal event listeners
        this.setupTerminalEventListeners();
        this.logger.info('AdvancedCodeEditingTool initialized with full framework capabilities');
    }
    /**
     * Initialize file system watchers for relevant file changes
     */
    initializeFileSystemWatchers() {
        // Watch for file changes to update workspace knowledge
        const watcher = vscode.workspace.createFileSystemWatcher('**/*.{ts,js,jsx,tsx,json}');
        watcher.onDidChange(async (uri) => {
            this.logger.debug(`File changed: ${uri.fsPath}`);
            await this.updateWorkspaceKnowledge(uri.fsPath);
        });
        watcher.onDidCreate(async (uri) => {
            this.logger.debug(`File created: ${uri.fsPath}`);
            await this.updateWorkspaceKnowledge(uri.fsPath);
        });
        watcher.onDidDelete((uri) => {
            this.logger.debug(`File deleted: ${uri.fsPath}`);
            this.removeFromWorkspaceKnowledge(uri.fsPath);
        });
    }
    /**
     * Update workspace knowledge with file changes
     */
    async updateWorkspaceKnowledge(filePath) {
        try {
            const content = await this.fileSystem.readFile(filePath);
            // Store file content in memory for quick access
            await this.memoryManager.storeMemory({
                id: `file:${filePath}`,
                content,
                metadata: {
                    type: 'file_content',
                    path: filePath,
                    lastModified: Date.now(),
                    source: 'file_system_watcher'
                }
            });
            this.logger.debug(`Updated workspace knowledge for: ${filePath}`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`Failed to update workspace knowledge for ${filePath}: ${errorMessage}`, { error: errorMessage });
        }
    }
    /**
     * Remove file from workspace knowledge
     */
    async removeFromWorkspaceKnowledge(filePath) {
        try {
            await this.memoryManager.deleteMemory(`file:${filePath}`);
            this.logger.debug(`Removed from workspace knowledge: ${filePath}`);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`Failed to remove ${filePath} from workspace knowledge: ${errorMessage}`, { error: errorMessage });
        }
    }
    /**
     * Initialize AI patterns for code editing
     */
    initializeAIPatterns() {
        try {
            if (!this.workspaceKnowledge) {
                this.logger.warn('Workspace knowledge not available, skipping AI patterns initialization');
                return;
            }
            // Register quick fix patterns for common issues
            const quickFixPatterns = [
                {
                    id: 'missing-import',
                    pattern: /Cannot find name '([^']+)'/,
                    title: 'Add missing import',
                    description: 'Add missing import statement for $1',
                    action: async (match) => {
                        // Implementation for adding missing imports
                        return { success: true };
                    }
                },
                {
                    id: 'unused-variable',
                    pattern: /'([^']+)' is declared but its value is never read/,
                    title: 'Remove unused variable',
                    description: 'Remove unused variable $1',
                    action: async (match) => {
                        // Implementation for removing unused variables
                        return { success: true };
                    }
                }
            ];
            if (typeof this.workspaceKnowledge.registerPatterns === 'function') {
                this.workspaceKnowledge.registerPatterns(quickFixPatterns);
                this.logger.info('Registered AI patterns for code editing');
            }
        }
        catch (error) {
            this.logger.error('Failed to initialize AI patterns', {
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    /**
     * Log an operation to history and memory
     */
    async logOperation(operation, filePath, success, error) {
        try {
            const logEntry = {
                operation,
                filePath,
                timestamp: Date.now(),
                success,
                error
            };
            // Here you would typically use the memory manager to store the log
            // For now, we'll just log to the console
            if (success) {
                this.logger.info(`Operation completed: ${operation}`, { filePath });
            }
            else {
                this.logger.error(`Operation failed: ${operation}`, { filePath, error });
            }
            // If you have a memory manager with a store method, you could do:
            // await this.memoryManager.store('operation-logs', logEntry);
        }
        catch (error) {
            this.logger.error('Failed to log operation', {
                operation,
                filePath,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
}
exports.AdvancedCodeEditingTool = AdvancedCodeEditingTool;
// Ensure metadata has the correct types and required fields
const { metadata: existingMetadata = {} } = updatedMemory;
const metadata = {
    source: 'conversation',
    type: 'text',
    success: true,
    ...existingMetadata,
    // Ensure these required fields are not overridden
    toolName: this.name,
    operation: updatedMemory.operation,
    executionId: updatedMemory.executionId || `exec_${Date.now()}`
};
// Use the memory manager to update the memory
await this.memoryManager.addMemory({
    content: JSON.stringify(updatedMemory.input),
    metadata
});
async;
_execute(actionName, string, validatedInput, unknown);
Promise < { output: unknown, memoriesCreated: string[] } > {
    const: memory = this.createOperationMemory(actionName, validatedInput),
    try: {
        let, result: unknown,
        switch(actionName) {
        },
        case: 'replaceLines',
        result = await this.executeReplaceLines(validatedInput),
        break: ,
        case: 'insertLines',
        result = await this.executeInsertLines(validatedInput),
        break: ,
        case: 'deleteLines',
        result = await this.executeDeleteLines(validatedInput),
        break: ,
        default: ,
        throw: new Error(`Unsupported action: ${actionName}`)
    }
    // Update memory with successful result
    ,
    // Update memory with successful result
    await, this: .updateOperationMemory(memory, {
        output: result,
        state: {
            ...memory.state,
            status: 'completed',
            endTime: Date.now(),
            progress: 100
        },
        duration: Date.now() - memory.state.startTime
    }),
    return: { output: result }
};
try { }
catch (error) {
    // Update memory with error result for framework tracking
    const errorMsg = error instanceof Error ? error.message : String(error);
    await this.updateOperationMemory(memory, {
        state: {
            ...memory.state,
            status: 'failed',
            endTime: Date.now()
        },
        error: {
            message: errorMsg,
            stack: error instanceof Error ? error.stack : undefined
        },
        duration: Date.now() - memory.state.startTime
    });
    throw error;
}
async;
executeFormatCode(input, { filePath: string, options: any });
Promise < { success: boolean } > {
    try: {
        this: .logger.info(`Formatting code in file: ${input.filePath}`),
        // Format the code using the file system directly since workspace.formatDocument is not available
        const: content = await this.fileSystem.readFile(input.filePath),
        // Basic formatting - trim whitespace and ensure consistent line endings
        const: formattedContent = content
            .split('\n')
            .map(line => line.trimEnd())
            .join('\n')
            .replace(/\r\n/g, '\n'),
        await, this: .fileSystem.writeFile(input.filePath, formattedContent),
        return: { success: true }
    }, catch(error) {
        this.logger.error(`Failed to format code: ${error}`);
        throw error;
    }
};
async;
executeApplyCodeAction(input, { filePath: string, action: string, range: any });
Promise < { success: boolean } > {
    try: {
        this: .logger.info(`Applying code action to file: ${input.filePath}, action: ${input.action}`),
        // Read the current file content
        const: content = await this.fileSystem.readFile(input.filePath),
        let, newContent = content,
        // Handle common code actions
        switch(input) { }, : .action.toLowerCase()
    }
};
{
    'trimwhitespace';
    newContent = content.split('\n').map(line => line.trimEnd()).join('\n');
    break;
    'removetrailingnewline';
    newContent = content.replace(/\n+$/, '');
    break;
    'ensurenonewline';
    newContent = content.trimEnd() + '\n';
    break;
    // Add more code actions as needed
}
// Only write if content changed
if (newContent !== content) {
    await this.fileSystem.writeFile(input.filePath, newContent);
}
return { success: true };
try { }
catch (error) {
    this.logger.error(`Failed to apply code action: ${error}`);
    throw error;
}
async;
executeApplyDiff(input, { filePath: string, diff: string, options: any });
Promise < { success: boolean } > {
    try: {
        this: .logger.info(`Applying diff to file: ${input.filePath}`),
        : .fileSystem && typeof this.fileSystem.applyDiff === 'function'
    }
};
{
    await this.fileSystem.applyDiff(input.filePath, input.diff, input.options);
}
{
    // Fallback implementation
    const content = await this.fileSystem.readFile(input.filePath);
    const newContent = this.applyPatch(content, input.diff);
    await this.fileSystem.writeFile(input.filePath, newContent);
}
return { success: true };
try { }
catch (error) {
    this.logger.error(`Failed to apply diff: ${error}`);
    throw error;
}
applyPatch(originalContent, string, patch, string);
string;
{
    // This is a simplified implementation
    // In a real-world scenario, you'd want to use a proper diff/patch library
    const lines = originalContent.split('\n');
    const patchLines = patch.split('\n');
    const result = [];
    let i = 0;
    while (i < patchLines.length) {
        const line = patchLines[i];
        if (line.startsWith('@@')) {
            // Parse the hunk header
            const hunkMatch = line.match(/^@@ -\d+(?:,\d+)? \+\d+(?:,\d+)? @@/);
            if (hunkMatch) {
                i++;
                // Skip the hunk header and process the hunk
                while (i < patchLines.length && !patchLines[i].startsWith('@@')) {
                    const hunkLine = patchLines[i];
                    if (hunkLine.startsWith('+') && !hunkLine.startsWith('+++')) {
                        result.push(hunkLine.substring(1));
                    }
                    else if (!hunkLine.startsWith('-') && !hunkLine.startsWith('---')) {
                        result.push(hunkLine);
                    }
                    i++;
                }
            }
        }
        else {
            i++;
        }
    }
    return result.join('\n');
}
async;
executeVerifyCodeChange(input, { filePath: string, expectedChecksum: string });
Promise < { success: boolean, matches: boolean } > {
    try: {
        : .fileSystem, throw: new Error('File system not available'),
        const: content = await this.fileSystem.readFile(input.filePath),
        const: checksum = await this.calculateChecksum(content),
        const: matches = checksum === input.expectedChecksum,
        return: { success: true, matches }
    }, catch(error) {
        this.logger.error(`Failed to verify code change: ${error}`);
        return { success: false, matches: false };
    }
};
async;
calculateChecksum(content, string);
Promise < string > {
    const: crypto = await Promise.resolve().then(() => __importStar(require('crypto'))),
    return: crypto
        .createHash('sha256')
        .update(content, 'utf8')
        .digest('hex')
};
async;
executeReplaceLines(input, ReplaceLinesInput);
Promise < { success: boolean, message: string, memoryId: string } > {
    try: {
        : .fileSystem, throw: new Error('File system not available'),
        const: { filePath, startLine, endLine, newContent, createBackup } = input,
        // Read original content
        const: originalContent = await this.fileSystem.readFile(filePath),
        const: lines = originalContent ? originalContent.split('\n') : [],
        // Validate line numbers
        if(startLine, , ) { }
    } || endLine > lines.length || startLine > endLine
};
{
    throw new Error(`Invalid line range: ${startLine}-${endLine}`);
}
// Create backup if requested
if (createBackup) {
    const backupPath = `${filePath}.bak`;
    await this.fileSystem.writeFile(backupPath, originalContent);
}
// Replace the lines
const deletedLines = lines.splice(startLine - 1, endLine - startLine + 1, ...newContent.split('\n'));
// Write the modified content back to the file
await this.fileSystem.writeFile(filePath, lines.join('\n'));
// Create operation memory with proper typing and correct memory source/type
const memory = this.createOperationMemory('replaceLines', {
    filePath,
    startLine,
    endLine,
    linesReplaced: deletedLines.length,
    newLineCount: newContent.split('\n').length
}, {
    source: 'conversation',
    type: 'text',
    success: true,
    affectedFiles: [filePath]
});
// Update memory with success status and proper state
await this.updateOperationMemory(memory, {
    state: {
        status: 'completed',
        startTime: Date.now(),
        endTime: Date.now()
    }
});
return {
    success: true,
    message: `Successfully replaced lines ${startLine}-${endLine}`,
    memoryId: memory.id
};
try { }
catch (error) {
    this.logger.error(`Error in executeReplaceLines: ${error}`);
    throw error;
}
async;
executeInsertLines(input, InsertLinesInput);
Promise < { success: boolean, message: string, memoryId: string } > {
    try: {
        : .fileSystem, throw: new Error('File system not available'),
        const: { filePath, line, newContent, createBackup } = input,
        // Read original content
        const: originalContent = await this.fileSystem.readFile(filePath),
        const: lines = originalContent ? originalContent.split('\n') : [],
        // Validate line number
        if(line, , ) { }
    } || line > lines.length
};
{
    throw new Error(`Invalid line number: ${line}`);
}
// Create backup if requested
if (createBackup) {
    const backupPath = `${filePath}.bak`;
    await this.fileSystem.writeFile(backupPath, originalContent);
}
// Insert the new content at the specified line
const insertPosition = line === 0 ? 0 : line;
const newLines = newContent.split('\n');
lines.splice(insertPosition, 0, ...newLines);
// Write the modified content back to the file
await this.fileSystem.writeFile(filePath, lines.join('\n'));
// Create operation memory with proper typing and correct memory source/type
const memory = this.createOperationMemory('insertLines', {
    filePath,
    line,
    linesInserted: newLines.length,
    contentPreview: newContent.length > 50 ? newContent.substring(0, 50) + '...' : newContent
}, {
    source: 'conversation',
    type: 'text',
    success: true,
    affectedFiles: [filePath]
});
// Update memory with success status and proper state
await this.updateOperationMemory(memory, {
    state: {
        status: 'completed',
        startTime: Date.now(),
        endTime: Date.now()
    }
});
return {
    success: true,
    message: `Successfully inserted ${newLines.length} lines at position ${line}`,
    memoryId: memory.id
};
try { }
catch (error) {
    this.logger.error(`Failed to insert lines: ${error}`);
    throw error;
}
async;
executeDeleteLines(input, DeleteLinesInput);
Promise < { success: boolean, message: string, memoryId: string } > {
    try: {
        : .fileSystem, throw: new Error('File system not available'),
        const: { filePath, startLine, endLine, createBackup } = input,
        // Read original content
        const: originalContent = await this.fileSystem.readFile(filePath),
        const: lines = originalContent ? originalContent.split('\n') : [],
        // Validate line numbers
        if(startLine, , ) { }
    } || endLine > lines.length || startLine > endLine
};
{
    throw new Error(`Invalid line range: ${startLine}-${endLine}`);
}
// Create backup if requested
if (createBackup) {
    const backupPath = `${filePath}.bak`;
    await this.fileSystem.writeFile(backupPath, originalContent);
}
// Remove the specified lines and capture deleted content
const deletedLines = lines.splice(startLine - 1, endLine - startLine + 1);
const deletedContent = deletedLines.join('\n');
// Write the modified content back to the file
await this.fileSystem.writeFile(filePath, lines.join('\n'));
// Create operation memory with proper typing
const memory = this.createOperationMemory('deleteLines', {
    filePath,
    startLine,
    endLine,
    linesDeleted: endLine - startLine + 1,
    deletedContentPreview: deletedContent.length > 100
        ? deletedContent.substring(0, 100) + '...'
        : deletedContent
}, {
    source: 'conversation',
    type: 'text',
    success: true,
    affectedFiles: [filePath]
});
// Update memory with success status and proper state
await this.updateOperationMemory(memory, {
    state: {
        status: 'completed',
        startTime: Date.now(),
        endTime: Date.now()
    }
});
return {
    success: true,
    message: `Successfully deleted ${endLine - startLine + 1} lines (${startLine}-${endLine})`,
    memoryId: memory.id
};
try { }
catch (error) {
    this.logger.error(`Failed to delete lines: ${error}`);
    throw error;
}
async;
executeGenerateDiff(input, GenerateDiffInput);
Promise < {
    diff: string,
    format: toolFramework_1.DiffFormat,
    summary: { additions: number, deletions: number, changes: number }
} > {
    try: {
        : .fileSystem, throw: new Error('File system not available'),
        const: {
            filePath,
            comparedTo,
            format = toolFramework_1.DiffFormat.Unified,
            contextLines = 3,
            ignoreWhitespace = false,
            ignoreCase = false,
            caseSensitive = true
        } = input,
        // Get the current file content
        const: currentContent = await this.fileSystem.readFile(filePath),
        // Get the content to compare against
        let, comparedContent = '',
        if(comparedTo) {
            // If a comparison file is provided, read it
            comparedContent = await this.fileSystem.readFile(comparedTo);
        }
        // Generate diff using the file system's diff utility
        ,
        // Generate diff using the file system's diff utility
        const: diff = await this.fileSystem.diffContent(comparedContent, currentContent, {
            format,
            contextLines,
            ignoreWhitespace,
            ignoreCase,
            fileHeader: true,
            hunkHeaders: true,
            lineNumbers: true
        }),
        // For JSON format, parse the diff to get the change counts
        let, summary = { additions: 0, deletions: 0, changes: 0 },
        if(format) { }
    } === toolFramework_1.DiffFormat.Json
};
{
    try {
        const diffData = JSON.parse(diff);
        if (diffData.changes && Array.isArray(diffData.changes)) {
            summary = {
                additions: diffData.changes.filter((c) => c.type === 'add').length,
                deletions: diffData.changes.filter((c) => c.type === 'remove').length,
                changes: diffData.changes.length
            };
        }
    }
    catch (e) {
        const error = e;
        this.logger.warn('Failed to parse JSON diff for summary', { error: error.message });
        // Fallback to analyzing the diff text for other formats
        summary = this.analyzeDiff(diff);
    }
}
{
    // For non-JSON formats, analyze the diff text
    summary = this.analyzeDiff(diff);
}
return {
    diff,
    format,
    summary
};
try { }
catch (error) {
    this.logger.error(`Failed to generate diff: ${error}`);
    throw error;
}
generateJsonDiff(newContent, string, oldContent, string);
Array < {
    type: 'add' | 'remove' | 'change',
    oldLine: string,
    newLine: string,
    lineNumber: number
} > {
    const: newLines = newContent.split('\n'),
    const: oldLines = oldContent.split('\n'),
    const: changes, Array() {
        type: 'add' | 'remove' | 'change';
        oldLine ?  : string;
        newLine ?  : string;
        lineNumber: number;
    }
} > ;
[];
for (let i = 0; i < Math.max(newLines.length, oldLines.length); i++) {
    const newLine = newLines[i];
    const oldLine = oldLines[i];
    if (newLine !== oldLine) {
        if (newLine === undefined) {
            changes.push({ type: 'remove', oldLine, lineNumber: i + 1 });
        }
        else if (oldLine === undefined) {
            changes.push({ type: 'add', newLine, lineNumber: i + 1 });
        }
        else {
            changes.push({
                type: 'change',
                oldLine,
                newLine,
                lineNumber: i + 1
            });
        }
    }
}
return changes;
analyzeDiff(diff, string);
{
    additions: number;
    deletions: number;
    changes: number;
}
{
    const lines = diff.split('\n');
    let additions = 0;
    let deletions = 0;
    for (const line of lines) {
        if (line.startsWith('+') && !line.startsWith('+++')) {
            additions++;
        }
        else if (line.startsWith('-') && !line.startsWith('---')) {
            deletions++;
        }
    }
    return {
        additions,
        deletions,
        changes: additions + deletions
    };
}
async;
executeValidateFile(input, ValidateFileInput);
Promise < {
    isValid: boolean,
    lineCount: number,
    size: number,
    checksum: string,
    issues: string[]
} > {
    try: {
        : .fileSystem
    }
};
{
    throw new Error('File system service not available');
}
const { filePath, checkSyntax = false } = input;
let content;
// Check if file exists by attempting to read it
try {
    content = await this.fileSystem.readFile(filePath);
}
catch (error) {
    if (error.code === 'ENOENT') {
        throw new Error(`File not found: ${filePath}`);
    }
    else {
        throw new Error(`File cannot be read: ${filePath}`);
    }
}
// Basic validation
const lines = content.split('\n');
const lineCount = lines.length;
const size = Buffer.byteLength(content, 'utf8');
const issues = [];
// Generate checksum
const crypto = await Promise.resolve().then(() => __importStar(require('crypto')));
const checksum = crypto.createHash('sha256').update(content).digest('hex');
// Check if file is empty
if (size === 0) {
    issues.push('File is empty');
}
// Check line endings (CRLF vs LF)
if (content.includes('\r\n')) {
    issues.push('File contains Windows-style line endings (CRLF)');
}
// Check for trailing whitespace
lines.forEach((line, index) => {
    if (line.endsWith(' ') || line.endsWith('\t')) {
        issues.push(`Trailing whitespace found on line ${index + 1}`);
    }
});
// Check for mixed indentation
const hasTabs = content.includes('\t');
const hasSpaces = content.match(/^ +/m) !== null;
if (hasTabs && hasSpaces) {
    issues.push('File contains mixed indentation (tabs and spaces)');
}
// Check for BOM
if (content.length > 0 && content.charCodeAt(0) === 0xFEFF) {
    issues.push('File contains a BOM (Byte Order Mark)');
}
// Note: Removed workspace-specific syntax checking since it's not in the interface
// If syntax checking is needed, it should be implemented in a different way
return {
    isValid: issues.length === 0,
    lineCount,
    size,
    checksum,
    issues: issues.length > 0 ? issues : undefined
};
try { }
catch (error) {
    this.logger.error(`Error validating file ${input.filePath}: ${error}`);
    throw error;
}
//# sourceMappingURL=advancedCodeEditingTool.js.map