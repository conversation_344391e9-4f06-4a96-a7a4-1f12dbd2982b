{"version": 3, "file": "workflowsSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/workflowsSettingsSection.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAIH,wEAgNC;AAlND,4CAAuD;AAEvD,SAAgB,8BAA8B,CAAC,SAAsB,EAAE,QAAa;IAClF,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE,QAAQ,CAAC,CAAC;IACrE,SAAS,CAAC,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0MnB,CAAC;IAEJ,sBAAsB;IACtB,mCAAmC,EAAE,CAAC;AACxC,CAAC;AAED,SAAS,mCAAmC;IAC1C,uBAAuB;IACvB,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACpD,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,CAAC,CAAC,MAA0B,CAAC;YAC5C,MAAM,SAAS,GAAG,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;YACvE,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;gBAC3B,IAAI,MAAM,CAAC,EAAE,KAAK,kBAAkB,EAAE,CAAC;oBACrC,SAAS,CAAC,WAAW,GAAG,GAAG,KAAK,GAAG,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,yBAAyB;IACzB,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;IACnG,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;IAErG,gCAAgC;IAChC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAC,EAAE,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE;QAC1F,MAAM,OAAO,GAAI,CAAC,CAAC,MAA2B,CAAC,OAAO,CAAC;QACvD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QACxE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,wBAAwB,CAAqB,CAAC;YAChF,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;gBAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,oBAAoB;IACjC,IAAI,CAAC;QACH,gDAAgD;QAChD,MAAM,aAAa,GAAG,IAAA,kBAAS,EAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAEjD,gCAAgC;QAChC,MAAM,gBAAgB,GAAG;YACvB,MAAM,EAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAuB,EAAE,KAAK,IAAI,SAAS;YAC7F,OAAO,EAAE,QAAQ,CAAE,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAsB,EAAE,KAAK,IAAI,KAAK,CAAC;YACpG,QAAQ,EAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAsB,EAAE,OAAO,IAAI,KAAK;YAChG,SAAS,EAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB,EAAE,OAAO,IAAI,KAAK;YAEhG,qBAAqB,EAAE;gBACrB,OAAO,EAAG,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAsB,EAAE,OAAO,IAAI,KAAK;gBAC1G,eAAe,EAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAsB,EAAE,OAAO,IAAI,KAAK;gBACpG,oBAAoB,EAAG,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAuB,EAAE,KAAK,IAAI,UAAU;gBACnH,mBAAmB,EAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAsB,EAAE,OAAO,IAAI,KAAK;gBAC5G,wBAAwB,EAAG,QAAQ,CAAC,cAAc,CAAC,4BAA4B,CAAuB,EAAE,KAAK,IAAI,UAAU;gBAC3H,cAAc,EAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAsB,EAAE,OAAO,IAAI,KAAK;gBAClG,kBAAkB,EAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAuB,EAAE,KAAK,IAAI,MAAM;gBAC3G,qBAAqB,EAAG,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAsB,EAAE,OAAO,IAAI,KAAK;gBAChH,eAAe,EAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAuB,EAAE,KAAK,IAAI,YAAY;aAC3G;YAED,SAAS,EAAE;gBACT,kBAAkB,EAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAsB,EAAE,OAAO,IAAI,KAAK;gBAC1G,oBAAoB,EAAG,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAsB,EAAE,OAAO,IAAI,KAAK;gBAC9G,aAAa,EAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAsB,EAAE,OAAO,IAAI,KAAK;gBAChG,iBAAiB,EAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB,EAAE,OAAO,IAAI,KAAK;aACzG;YAED,eAAe,EAAE;gBACf,OAAO,EAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAsB,EAAE,OAAO,IAAI,KAAK;gBAC5F,cAAc,EAAE,QAAQ,CAAE,QAAQ,CAAC,cAAc,CAAC,4BAA4B,CAAsB,EAAE,KAAK,IAAI,GAAG,CAAC;gBACnH,iBAAiB,EAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB,EAAE,OAAO,IAAI,KAAK;aACzG;SACF,CAAC;QAEF,yDAAyD;QACzD,MAAM,cAAc,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,gBAAgB,EAAE,CAAC;QACjE,MAAM,IAAA,kBAAS,EAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAE7C,uBAAuB;QACvB,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;QACrE,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;YAC5C,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC;YACpC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACpC,UAAU,CAAC,GAAG,EAAE;gBACd,UAAU,CAAC,WAAW,GAAG,YAAY,CAAC;gBACtC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACzC,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,qBAAqB;QACrB,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;QACrE,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;YAC5C,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC;YACpC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAClC,UAAU,CAAC,GAAG,EAAE;gBACd,UAAU,CAAC,WAAW,GAAG,YAAY,CAAC;gBACtC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,UAAU,qBAAqB;IAClC,4CAA4C;IAC5C,MAAM,YAAY,GAAG,OAAO,CAAC,mEAAmE,CAAC,CAAC;IAClG,IAAI,YAAY,EAAE,CAAC;QACjB,sBAAsB;QACrB,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAuB,CAAC,KAAK,GAAG,SAAS,CAAC;QACnF,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAsB,CAAC,KAAK,GAAG,KAAK,CAAC;QAC/E,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QACnF,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAEnF,+BAA+B;QAC9B,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAC9F,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAChF,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAuB,CAAC,KAAK,GAAG,UAAU,CAAC;QAC3F,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QACpF,QAAQ,CAAC,cAAc,CAAC,4BAA4B,CAAuB,CAAC,KAAK,GAAG,UAAU,CAAC;QAC/F,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/E,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAuB,CAAC,KAAK,GAAG,MAAM,CAAC;QACrF,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QACtF,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAuB,CAAC,KAAK,GAAG,YAAY,CAAC;QAExF,kBAAkB;QACjB,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QACnF,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QACrF,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAC9E,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAEnF,yBAAyB;QACxB,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAChF,QAAQ,CAAC,cAAc,CAAC,4BAA4B,CAAsB,CAAC,KAAK,GAAG,GAAG,CAAC;QACvF,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QAEnF,yBAAyB;QACzB,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACpD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC", "sourcesContent": ["/**\n * Revolutionary Workflows Settings Section\n * Enhanced with revolutionary AI capabilities\n */\n\nimport { getConfig, setConfig } from '../../../config';\n\nexport function renderWorkflowsSettingsSection(container: HTMLElement, settings: any): void {\n  console.log('Rendering workflows settings with settings:', settings);\n  container.innerHTML = `\n        <div class=\"revolutionary-workflow-settings\">\n            <div class=\"workflow-header\">\n                <h2>🚀 Revolutionary Workflow Settings</h2>\n                <p class=\"workflow-description\">\n                    Configure workflow execution with revolutionary AI enhancements including\n                    Goddess Mode guidance, Quantum optimization, Neural insights, and Time-travel predictions.\n                </p>\n            </div>\n\n            <!-- Core Workflow Settings -->\n            <div class=\"settings-section workflow-core-settings\">\n                <h3>⚙️ Core Settings</h3>\n                <div class=\"settings-grid\">\n                    <div class=\"setting-group\">\n                        <label for=\"workflow-engine\">Default Workflow Engine</label>\n                        <select id=\"workflow-engine\" class=\"settings-select\">\n                            <option value=\"codessa\">Codessa Engine (Revolutionary)</option>\n                            <option value=\"langgraph\">LangGraph Engine</option>\n                        </select>\n                        <small>Choose the engine for workflow execution</small>\n                    </div>\n\n                    <div class=\"setting-group\">\n                        <label for=\"workflow-timeout\">Workflow Timeout (seconds)</label>\n                        <div class=\"slider-container\">\n                            <input type=\"range\" id=\"workflow-timeout\" min=\"30\" max=\"3600\" value=\"300\" class=\"slider\">\n                            <span class=\"slider-value\">300s</span>\n                        </div>\n                        <small>Maximum time for workflow execution</small>\n                    </div>\n                </div>\n\n                <div class=\"checkbox-grid\">\n                    <label class=\"checkbox-item\">\n                        <input type=\"checkbox\" id=\"auto-save-workflows\" checked>\n                        <span>Auto-save Workflows</span>\n                    </label>\n                    <label class=\"checkbox-item\">\n                        <input type=\"checkbox\" id=\"workflow-debugging\" checked>\n                        <span>Enable Workflow Debugging</span>\n                    </label>\n                </div>\n            </div>\n\n            <!-- Revolutionary Workflow Enhancements -->\n            <div class=\"settings-section revolutionary-enhancements\">\n                <div class=\"section-header\">\n                    <h3>✨ Revolutionary Enhancements</h3>\n                    <div class=\"feature-toggle\">\n                        <label class=\"toggle-switch\">\n                            <input type=\"checkbox\" id=\"revolutionary-features-enabled\" checked>\n                            <span class=\"toggle-slider\"></span>\n                        </label>\n                        <span class=\"toggle-label\">Enable Revolutionary Features</span>\n                    </div>\n                </div>\n\n                <div class=\"enhancement-grid\">\n                    <div class=\"enhancement-card goddess-enhancement\">\n                        <div class=\"enhancement-header\">\n                            <span class=\"enhancement-icon\">✨</span>\n                            <h4>Goddess Guidance</h4>\n                            <label class=\"mini-toggle\">\n                                <input type=\"checkbox\" id=\"goddess-guidance\" checked>\n                                <span class=\"mini-slider\"></span>\n                            </label>\n                        </div>\n                        <p>Divine wisdom and emotional intelligence for workflow execution</p>\n                        <div class=\"enhancement-settings\">\n                            <label>Guidance Level:</label>\n                            <select id=\"goddess-guidance-level\" class=\"mini-select\">\n                                <option value=\"subtle\">Subtle</option>\n                                <option value=\"moderate\" selected>Moderate</option>\n                                <option value=\"comprehensive\">Comprehensive</option>\n                            </select>\n                        </div>\n                    </div>\n\n                    <div class=\"enhancement-card quantum-enhancement\">\n                        <div class=\"enhancement-header\">\n                            <span class=\"enhancement-icon\">🔬</span>\n                            <h4>Quantum Optimization</h4>\n                            <label class=\"mini-toggle\">\n                                <input type=\"checkbox\" id=\"quantum-optimization\" checked>\n                                <span class=\"mini-slider\"></span>\n                            </label>\n                        </div>\n                        <p>Quantum-inspired workflow optimization and parallel execution</p>\n                        <div class=\"enhancement-settings\">\n                            <label>Optimization Level:</label>\n                            <select id=\"quantum-optimization-level\" class=\"mini-select\">\n                                <option value=\"basic\">Basic</option>\n                                <option value=\"advanced\" selected>Advanced</option>\n                                <option value=\"expert\">Expert</option>\n                            </select>\n                        </div>\n                    </div>\n\n                    <div class=\"enhancement-card neural-enhancement\">\n                        <div class=\"enhancement-header\">\n                            <span class=\"enhancement-icon\">🧠</span>\n                            <h4>Neural Insights</h4>\n                            <label class=\"mini-toggle\">\n                                <input type=\"checkbox\" id=\"neural-insights\" checked>\n                                <span class=\"mini-slider\"></span>\n                            </label>\n                        </div>\n                        <p>Brain-inspired workflow analysis and adaptive execution</p>\n                        <div class=\"enhancement-settings\">\n                            <label>Insight Depth:</label>\n                            <select id=\"neural-insight-depth\" class=\"mini-select\">\n                                <option value=\"surface\">Surface</option>\n                                <option value=\"deep\" selected>Deep</option>\n                                <option value=\"consciousness\">Consciousness</option>\n                            </select>\n                        </div>\n                    </div>\n\n                    <div class=\"enhancement-card timetravel-enhancement\">\n                        <div class=\"enhancement-header\">\n                            <span class=\"enhancement-icon\">⏰</span>\n                            <h4>Time-Travel Predictions</h4>\n                            <label class=\"mini-toggle\">\n                                <input type=\"checkbox\" id=\"timetravel-predictions\" checked>\n                                <span class=\"mini-slider\"></span>\n                            </label>\n                        </div>\n                        <p>Predict and prevent future workflow issues</p>\n                        <div class=\"enhancement-settings\">\n                            <label>Prediction Scope:</label>\n                            <select id=\"prediction-scope\" class=\"mini-select\">\n                                <option value=\"immediate\">Immediate</option>\n                                <option value=\"short-term\" selected>Short-term</option>\n                                <option value=\"long-term\">Long-term</option>\n                            </select>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <!-- Performance & Analytics -->\n            <div class=\"settings-section workflow-analytics\">\n                <h3>📊 Performance & Analytics</h3>\n\n                <div class=\"checkbox-grid\">\n                    <label class=\"checkbox-item\">\n                        <input type=\"checkbox\" id=\"performance-metrics\" checked>\n                        <span>Performance Metrics</span>\n                    </label>\n                    <label class=\"checkbox-item\">\n                        <input type=\"checkbox\" id=\"satisfaction-tracking\" checked>\n                        <span>User Satisfaction Tracking</span>\n                    </label>\n                    <label class=\"checkbox-item\">\n                        <input type=\"checkbox\" id=\"goddess-rating\" checked>\n                        <span>Goddess Rating System</span>\n                    </label>\n                    <label class=\"checkbox-item\">\n                        <input type=\"checkbox\" id=\"adaptive-execution\" checked>\n                        <span>Adaptive Execution</span>\n                    </label>\n                </div>\n            </div>\n\n            <!-- Auto-Enhancement Settings -->\n            <div class=\"settings-section auto-enhancement\">\n                <h3>🤖 Auto-Enhancement</h3>\n\n                <div class=\"settings-grid\">\n                    <div class=\"setting-group\">\n                        <label for=\"enhancement-aggressiveness\">Enhancement Aggressiveness</label>\n                        <div class=\"slider-container\">\n                            <input type=\"range\" id=\"enhancement-aggressiveness\" min=\"1\" max=\"10\" value=\"7\" class=\"slider\">\n                            <span class=\"slider-value\">7</span>\n                        </div>\n                        <small>How aggressively to apply revolutionary enhancements</small>\n                    </div>\n                </div>\n\n                <div class=\"checkbox-grid\">\n                    <label class=\"checkbox-item\">\n                        <input type=\"checkbox\" id=\"auto-enhancement\" checked>\n                        <span>Auto-Enhancement</span>\n                    </label>\n                    <label class=\"checkbox-item\">\n                        <input type=\"checkbox\" id=\"learning-execution\" checked>\n                        <span>Learning from Execution</span>\n                    </label>\n                </div>\n            </div>\n\n            <!-- Save Actions -->\n            <div class=\"settings-actions\">\n                <button id=\"save-workflow-settings\" class=\"save-button\">\n                    🚀 Save Workflow Settings\n                </button>\n                <button id=\"reset-workflow-settings\" class=\"reset-button\">\n                    🔄 Reset to Defaults\n                </button>\n            </div>\n        </div>\n    `;\n\n  // Add event listeners\n  setupWorkflowSettingsEventListeners();\n}\n\nfunction setupWorkflowSettingsEventListeners(): void {\n  // Slider value updates\n  document.querySelectorAll('.slider').forEach(slider => {\n    slider.addEventListener('input', (e) => {\n      const target = e.target as HTMLInputElement;\n      const valueSpan = target.parentElement?.querySelector('.slider-value');\n      if (valueSpan) {\n        const value = target.value;\n        if (target.id === 'workflow-timeout') {\n          valueSpan.textContent = `${value}s`;\n        } else {\n          valueSpan.textContent = value;\n        }\n      }\n    });\n  });\n\n  // Save and reset buttons\n  document.getElementById('save-workflow-settings')?.addEventListener('click', saveWorkflowSettings);\n  document.getElementById('reset-workflow-settings')?.addEventListener('click', resetWorkflowSettings);\n\n  // Revolutionary features toggle\n  document.getElementById('revolutionary-features-enabled')?.addEventListener('change', (e) => {\n    const enabled = (e.target as HTMLInputElement).checked;\n    const enhancementCards = document.querySelectorAll('.enhancement-card');\n    enhancementCards.forEach(card => {\n      const toggle = card.querySelector('input[type=\"checkbox\"]') as HTMLInputElement;\n      if (toggle) {\n        toggle.disabled = !enabled;\n        if (!enabled) {\n          toggle.checked = false;\n        }\n      }\n    });\n  });\n}\n\nasync function saveWorkflowSettings(): Promise<void> {\n  try {\n    // Get current config to merge with new settings\n    const currentConfig = getConfig('workflows', {});\n    \n    // Collect all workflow settings\n    const workflowSettings = {\n      engine: (document.getElementById('workflow-engine') as HTMLSelectElement)?.value || 'codessa',\n      timeout: parseInt((document.getElementById('workflow-timeout') as HTMLInputElement)?.value || '300'),\n      autoSave: (document.getElementById('auto-save-workflows') as HTMLInputElement)?.checked || false,\n      debugging: (document.getElementById('workflow-debugging') as HTMLInputElement)?.checked || false,\n\n      revolutionaryFeatures: {\n        enabled: (document.getElementById('revolutionary-features-enabled') as HTMLInputElement)?.checked || false,\n        goddessGuidance: (document.getElementById('goddess-guidance') as HTMLInputElement)?.checked || false,\n        goddessGuidanceLevel: (document.getElementById('goddess-guidance-level') as HTMLSelectElement)?.value || 'moderate',\n        quantumOptimization: (document.getElementById('quantum-optimization') as HTMLInputElement)?.checked || false,\n        quantumOptimizationLevel: (document.getElementById('quantum-optimization-level') as HTMLSelectElement)?.value || 'advanced',\n        neuralInsights: (document.getElementById('neural-insights') as HTMLInputElement)?.checked || false,\n        neuralInsightDepth: (document.getElementById('neural-insight-depth') as HTMLSelectElement)?.value || 'deep',\n        timeTravelPredictions: (document.getElementById('timetravel-predictions') as HTMLInputElement)?.checked || false,\n        predictionScope: (document.getElementById('prediction-scope') as HTMLSelectElement)?.value || 'short-term'\n      },\n\n      analytics: {\n        performanceMetrics: (document.getElementById('performance-metrics') as HTMLInputElement)?.checked || false,\n        satisfactionTracking: (document.getElementById('satisfaction-tracking') as HTMLInputElement)?.checked || false,\n        goddessRating: (document.getElementById('goddess-rating') as HTMLInputElement)?.checked || false,\n        adaptiveExecution: (document.getElementById('adaptive-execution') as HTMLInputElement)?.checked || false\n      },\n\n      autoEnhancement: {\n        enabled: (document.getElementById('auto-enhancement') as HTMLInputElement)?.checked || false,\n        aggressiveness: parseInt((document.getElementById('enhancement-aggressiveness') as HTMLInputElement)?.value || '7'),\n        learningExecution: (document.getElementById('learning-execution') as HTMLInputElement)?.checked || false\n      }\n    };\n\n    // Merge with current config and save to VS Code settings\n    const mergedSettings = { ...currentConfig, ...workflowSettings };\n    await setConfig('workflows', mergedSettings);\n\n    // Show success message\n    const saveButton = document.getElementById('save-workflow-settings');\n    if (saveButton) {\n      const originalText = saveButton.textContent;\n      saveButton.textContent = '✅ Saved!';\n      saveButton.classList.add('success');\n      setTimeout(() => {\n        saveButton.textContent = originalText;\n        saveButton.classList.remove('success');\n      }, 2000);\n    }\n  } catch (error) {\n    console.error('Error saving workflow settings:', error);\n    // Show error message\n    const saveButton = document.getElementById('save-workflow-settings');\n    if (saveButton) {\n      const originalText = saveButton.textContent;\n      saveButton.textContent = '❌ Error!';\n      saveButton.classList.add('error');\n      setTimeout(() => {\n        saveButton.textContent = originalText;\n        saveButton.classList.remove('error');\n      }, 2000);\n    }\n  }\n}\n\nasync function resetWorkflowSettings(): Promise<void> {\n  // Reset all form elements to default values\n  const confirmReset = confirm('Are you sure you want to reset all workflow settings to defaults?');\n  if (confirmReset) {\n    // Reset form elements\n    (document.getElementById('workflow-engine') as HTMLSelectElement).value = 'codessa';\n    (document.getElementById('workflow-timeout') as HTMLInputElement).value = '300';\n    (document.getElementById('auto-save-workflows') as HTMLInputElement).checked = true;\n    (document.getElementById('workflow-debugging') as HTMLInputElement).checked = true;\n\n    // Reset revolutionary features\n    (document.getElementById('revolutionary-features-enabled') as HTMLInputElement).checked = true;\n    (document.getElementById('goddess-guidance') as HTMLInputElement).checked = true;\n    (document.getElementById('goddess-guidance-level') as HTMLSelectElement).value = 'moderate';\n    (document.getElementById('quantum-optimization') as HTMLInputElement).checked = true;\n    (document.getElementById('quantum-optimization-level') as HTMLSelectElement).value = 'advanced';\n    (document.getElementById('neural-insights') as HTMLInputElement).checked = true;\n    (document.getElementById('neural-insight-depth') as HTMLSelectElement).value = 'deep';\n    (document.getElementById('timetravel-predictions') as HTMLInputElement).checked = true;\n    (document.getElementById('prediction-scope') as HTMLSelectElement).value = 'short-term';\n\n    // Reset analytics\n    (document.getElementById('performance-metrics') as HTMLInputElement).checked = true;\n    (document.getElementById('satisfaction-tracking') as HTMLInputElement).checked = true;\n    (document.getElementById('goddess-rating') as HTMLInputElement).checked = true;\n    (document.getElementById('adaptive-execution') as HTMLInputElement).checked = true;\n\n    // Reset auto-enhancement\n    (document.getElementById('auto-enhancement') as HTMLInputElement).checked = true;\n    (document.getElementById('enhancement-aggressiveness') as HTMLInputElement).value = '7';\n    (document.getElementById('learning-execution') as HTMLInputElement).checked = true;\n\n    // Update slider displays\n    document.querySelectorAll('.slider').forEach(slider => {\n      const event = new Event('input');\n      slider.dispatchEvent(event);\n    });\n  }\n}\n"]}