"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMProvider = void 0;
class LLMProvider {
    config;
    constructor(_config) {
        this.config = _config;
    }
    getModelId(params) {
        return params.modelId || this.config.modelId;
    }
    getTemperature(params) {
        return params.temperature ?? this.config.temperature ?? 0.7;
    }
    getMaxTokens(params) {
        return params.maxTokens ?? this.config.maxTokens ?? 2000;
    }
}
exports.LLMProvider = LLMProvider;
//# sourceMappingURL=llmProvider.js.map