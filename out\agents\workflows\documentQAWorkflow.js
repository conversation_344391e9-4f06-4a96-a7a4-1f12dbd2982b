"use strict";
/**
 * Document QA Workflow
 *
 * This module provides a workflow for document question-answering:
 * - Loading and processing documents
 * - Analyzing document content
 * - Answering questions based on document content
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDocumentQAWorkflow = createDocumentQAWorkflow;
exports.createPDFQAWorkflow = createPDFQAWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
const codessaMemory_1 = require("../../memory/codessa/codessaMemory");
/**
 * Create a Document QA workflow for answering questions about documents
 */
function createDocumentQAWorkflow(id, name, description, qaAgent, tools = []) {
    logger_1.logger.info(`Creating Document QA workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const documentLoadingNode = graph_1.Codessa.createAgentNode('document-loading', 'Document Loading', qaAgent);
    const documentProcessingNode = graph_1.Codessa.createAgentNode('document-processing', 'Document Processing', qaAgent);
    const questionAnalysisNode = graph_1.Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);
    const contextRetrievalNode = graph_1.Codessa.createAgentNode('context-retrieval', 'Context Retrieval', qaAgent);
    const documentQAMemoryManager = {
        async storeDocumentContent(content, metadata = {}) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content,
                    metadata: {
                        source: 'file',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['document', 'content'],
                        ...metadata
                    }
                });
                logger_1.logger.info('Saved document content to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save document content to memory:', error);
            }
        },
        async retrieveRelevantContent(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5
                });
                logger_1.logger.info(`Retrieved ${memories.length} relevant document content items from memory`);
                return memories;
            }
            catch (error) {
                logger_1.logger.error('Failed to retrieve relevant document content from memory:', error);
                return [];
            }
        },
        async storeQuestion(question, answer) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify({ question, answer }),
                    metadata: {
                        source: 'conversation',
                        type: 'conversation',
                        timestamp: new Date().toISOString(),
                        tags: ['document', 'qa', 'question', 'answer']
                    }
                });
                logger_1.logger.info('Saved question-answer pair to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save question-answer pair to memory:', error);
            }
        }
    };
    const answerGenerationNode = graph_1.Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);
    const answerVerificationNode = graph_1.Codessa.createAgentNode('answer-verification', 'Answer Verification', qaAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-loading', source: 'input', target: 'document-loading', type: 'default' },
        { name: 'loading-to-processing', source: 'document-loading', target: 'document-processing', type: 'default' },
        { name: 'processing-to-question', source: 'document-processing', target: 'question-analysis', type: 'default' },
        { name: 'question-to-retrieval', source: 'question-analysis', target: 'context-retrieval', type: 'default' },
        { name: 'retrieval-to-generation', source: 'context-retrieval', target: 'answer-generation', type: 'default' },
        { name: 'generation-to-verification', source: 'answer-generation', target: 'answer-verification', type: 'default' },
        { name: 'verification-to-output', source: 'answer-verification', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect context retrieval to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `retrieval-to-tool-${index}`,
                source: 'context-retrieval',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to answer generation
            edges.push({
                name: `tool-${index}-to-generation`,
                source: `tool-${index}`,
                target: 'answer-generation',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            documentLoadingNode,
            documentProcessingNode,
            questionAnalysisNode,
            contextRetrievalNode,
            answerGenerationNode,
            answerVerificationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'document-qa',
        tags: ['document-qa', 'question-answering', 'document-analysis'],
        metadata: {
            memoryManager: documentQAMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a specialized PDF QA workflow
 */
function createPDFQAWorkflow(id, name, description, qaAgent, tools = []) {
    logger_1.logger.info(`Creating PDF QA workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const pdfLoadingNode = graph_1.Codessa.createAgentNode('pdf-loading', 'PDF Loading', qaAgent);
    const pdfParsingNode = graph_1.Codessa.createAgentNode('pdf-parsing', 'PDF Parsing', qaAgent);
    const textExtractionNode = graph_1.Codessa.createAgentNode('text-extraction', 'Text Extraction', qaAgent);
    const imageExtractionNode = graph_1.Codessa.createAgentNode('image-extraction', 'Image Extraction', qaAgent);
    const tableExtractionNode = graph_1.Codessa.createAgentNode('table-extraction', 'Table Extraction', qaAgent);
    const contentAnalysisNode = graph_1.Codessa.createAgentNode('content-analysis', 'Content Analysis', qaAgent);
    const pdfQAMemoryManager = {
        async storePDFContent(content, contentType, metadata = {}) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content,
                    metadata: {
                        source: 'file',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['pdf', 'document', contentType],
                        ...metadata
                    }
                });
                logger_1.logger.info(`Saved PDF ${contentType} content to memory`);
            }
            catch (error) {
                logger_1.logger.error(`Failed to save PDF ${contentType} content to memory:`, error);
            }
        },
        async retrieveRelevantPDFContent(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5,
                    filter: {
                        tags: ['pdf']
                    }
                });
                logger_1.logger.info(`Retrieved ${memories.length} relevant PDF content items from memory`);
                return memories;
            }
            catch (error) {
                logger_1.logger.error('Failed to retrieve relevant PDF content from memory:', error);
                return [];
            }
        },
        async storePDFQuestion(question, answer) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify({ question, answer }),
                    metadata: {
                        source: 'conversation',
                        type: 'conversation',
                        timestamp: new Date().toISOString(),
                        tags: ['pdf', 'document', 'qa', 'question', 'answer']
                    }
                });
                logger_1.logger.info('Saved PDF question-answer pair to memory');
            }
            catch (error) {
                logger_1.logger.error('Failed to save PDF question-answer pair to memory:', error);
            }
        }
    };
    const questionAnalysisNode = graph_1.Codessa.createAgentNode('question-analysis', 'Question Analysis', qaAgent);
    const answerGenerationNode = graph_1.Codessa.createAgentNode('answer-generation', 'Answer Generation', qaAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-loading', source: 'input', target: 'pdf-loading', type: 'default' },
        { name: 'loading-to-parsing', source: 'pdf-loading', target: 'pdf-parsing', type: 'default' },
        { name: 'parsing-to-text', source: 'pdf-parsing', target: 'text-extraction', type: 'default' },
        { name: 'parsing-to-image', source: 'pdf-parsing', target: 'image-extraction', type: 'default' },
        { name: 'parsing-to-table', source: 'pdf-parsing', target: 'table-extraction', type: 'default' },
        { name: 'text-to-analysis', source: 'text-extraction', target: 'content-analysis', type: 'default' },
        { name: 'image-to-analysis', source: 'image-extraction', target: 'content-analysis', type: 'default' },
        { name: 'table-to-analysis', source: 'table-extraction', target: 'content-analysis', type: 'default' },
        { name: 'analysis-to-question', source: 'content-analysis', target: 'question-analysis', type: 'default' },
        { name: 'question-to-answer', source: 'question-analysis', target: 'answer-generation', type: 'default' },
        { name: 'answer-to-output', source: 'answer-generation', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect content analysis to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `analysis-to-tool-${index}`,
                source: 'content-analysis',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to question analysis
            edges.push({
                name: `tool-${index}-to-question`,
                source: `tool-${index}`,
                target: 'question-analysis',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            pdfLoadingNode,
            pdfParsingNode,
            textExtractionNode,
            imageExtractionNode,
            tableExtractionNode,
            contentAnalysisNode,
            questionAnalysisNode,
            answerGenerationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'document-qa',
        tags: ['pdf', 'document-qa', 'question-answering'],
        metadata: {
            memoryManager: pdfQAMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=documentQAWorkflow.js.map