{"version": 3, "file": "themeConfig.js", "sourceRoot": "", "sources": ["../../../src/ui/settings/themeConfig.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,sEAAsE;;;AAsDzD,QAAA,oBAAoB,GAAkB;IACjD,IAAI,EAAE,QAAQ;IACd,WAAW,EAAE,SAAS;IACtB,KAAK,EAAE;QACL,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE,MAAM;QAClB,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,MAAM;QACpB,SAAS,EAAE,8BAA8B;QACzC,gBAAgB,EAAE,KAAK;QACvB,cAAc,EAAE,QAAQ;QACxB,eAAe,EAAE,QAAQ;QACzB,MAAM,EAAE;YACN,UAAU,EAAE,SAAS;YACrB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,KAAK;YACnB,MAAM,EAAE,MAAM;SACf;QACD,iBAAiB,EAAE;YACjB,UAAU,EAAE,SAAS;YACrB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,qBAAqB;YAC7B,YAAY,EAAE,KAAK;YACnB,MAAM,EAAE,kCAAkC;SAC3C;QACD,SAAS,EAAE;YACT,QAAQ,EAAE,OAAO;YACjB,KAAK,EAAE,MAAM;SACd;QACD,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,iWAAiW;KAC/W;IACD,OAAO,EAAE;QACP,WAAW,EAAE,MAAM;QACnB,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE;YACN,UAAU,EAAE,SAAS;YACrB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,KAAK;SACpB;KACF;CACF,CAAC", "sourcesContent": ["// Centralized theme config for modals and settings sections\n// Modern, accessible, appealing defaults for both light and dark mode\n\nexport type ModalTheme = {\n    background: string;\n    foreground: string;\n    border: string;\n    borderRadius: string;\n    boxShadow: string;\n    headerFontWeight: string;\n    headerFontSize: string;\n    contentFontSize: string;\n    button: {\n        background: string;\n        color: string;\n        border: string;\n        borderRadius: string;\n        shadow: string;\n    };\n    buttonDestructive: {\n        background: string;\n        color: string;\n        border: string;\n        borderRadius: string;\n        shadow: string;\n    };\n    animation: {\n        duration: string;\n        scale: string;\n    };\n    warningColor: string;\n    warningIcon: string;\n};\n\nexport type SectionTheme = {\n    headerColor: string;\n    headerBg: string;\n    border: string;\n    borderRadius: string;\n    accent: string;\n    button: {\n        background: string;\n        color: string;\n        border: string;\n        borderRadius: string;\n    };\n};\n\nexport type UIThemeConfig = {\n    mode: 'light' | 'dark' | 'system';\n    accentColor: string;\n    modal: ModalTheme;\n    section: SectionTheme;\n};\n\nexport const defaultUIThemeConfig: UIThemeConfig = {\n  mode: 'system',\n  accentColor: '#2563eb',\n  modal: {\n    background: '#fff',\n    foreground: '#222',\n    border: '#e5e7eb',\n    borderRadius: '14px',\n    boxShadow: '0 12px 36px rgba(0,0,0,0.20)',\n    headerFontWeight: '700',\n    headerFontSize: '1.35em',\n    contentFontSize: '1.08em',\n    button: {\n      background: '#2563eb',\n      color: '#fff',\n      border: 'none',\n      borderRadius: '5px',\n      shadow: 'none',\n    },\n    buttonDestructive: {\n      background: '#dc2626',\n      color: '#fff',\n      border: '1.5px solid #dc2626',\n      borderRadius: '5px',\n      shadow: '0 1px 6px 0 rgba(220,38,38,0.10)',\n    },\n    animation: {\n      duration: '0.22s',\n      scale: '0.96',\n    },\n    warningColor: '#b91c1c',\n    warningIcon: '<svg width=\\'28\\' height=\\'28\\' viewBox=\\'0 0 20 20\\' fill=\\'none\\' aria-hidden=\\'true\\' focusable=\\'false\\'><circle cx=\\'10\\' cy=\\'10\\' r=\\'9\\' fill=\\'#fff3f3\\' stroke=\\'#dc2626\\' stroke-width=\\'2\\'/><path d=\\'M10 6v5\\' stroke=\\'#dc2626\\' stroke-width=\\'1.7\\' stroke-linecap=\\'round\\'/><circle cx=\\'10\\' cy=\\'14.2\\' r=\\'1.2\\' fill=\\'#dc2626\\'/></svg>',\n  },\n  section: {\n    headerColor: '#222',\n    headerBg: '#f8fafc',\n    border: '#e5e7eb',\n    borderRadius: '10px',\n    accent: '#2563eb',\n    button: {\n      background: '#2563eb',\n      color: '#fff',\n      border: 'none',\n      borderRadius: '5px',\n    },\n  },\n};\n"]}