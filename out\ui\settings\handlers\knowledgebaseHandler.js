"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleKnowledgebaseMessage = handleKnowledgebaseMessage;
// Handler for Knowledgebase settings messages and logic
function handleKnowledgebaseMessage(message, panel) {
    const settings = window.settings || {};
    settings.knowledgebases = Array.isArray(settings.knowledgebases) ? settings.knowledgebases : [];
    const response = { type: message.type, success: false, data: null, error: null };
    try {
        switch (message.type) {
            case 'getKnowledgebases': {
                response.success = true;
                response.data = settings.knowledgebases;
                break;
            }
            case 'addKnowledgebase': {
                const kb = message.kb;
                if (!kb || !kb.name || !kb.id) {
                    response.error = 'Missing required knowledgebase fields.';
                    break;
                }
                settings.knowledgebases.push(kb);
                response.success = true;
                response.data = kb;
                break;
            }
            case 'editKnowledgebase': {
                const kb = message.kb;
                if (!kb || !kb.id) {
                    response.error = 'Knowledgebase id required.';
                    break;
                }
                const idx = settings.knowledgebases.findIndex((k) => k.id === kb.id);
                if (idx === -1) {
                    response.error = 'Knowledgebase not found.';
                    break;
                }
                settings.knowledgebases[idx] = { ...settings.knowledgebases[idx], ...kb };
                response.success = true;
                response.data = settings.knowledgebases[idx];
                break;
            }
            case 'deleteKnowledgebase': {
                const id = message.id;
                if (!id) {
                    response.error = 'Knowledgebase id required.';
                    break;
                }
                const idx = settings.knowledgebases.findIndex((k) => k.id === id);
                if (idx === -1) {
                    response.error = 'Knowledgebase not found.';
                    break;
                }
                settings.knowledgebases.splice(idx, 1);
                response.success = true;
                break;
            }
            default: {
                response.error = 'Unknown message type.';
            }
        }
    }
    catch (err) {
        response.error = err?.message || String(err);
    }
    panel.postMessage(response);
}
//# sourceMappingURL=knowledgebaseHandler.js.map