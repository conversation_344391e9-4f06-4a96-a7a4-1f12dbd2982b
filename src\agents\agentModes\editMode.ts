import * as vscode from 'vscode';
import { OperationMode, ContextSource, ContextType } from './operationMode';
import { Agent } from '../agentUtilities/agent';
import { LLMGenerateParams } from '../../llm/types';
import { Logger } from '../../logger';
import { contextManager } from './contextManager';
import { promptManager } from '../../prompts/promptManager';

/**
 * Edit Mode - Autonomous code editing with human verification
 */
export class EditMode extends OperationMode {
  readonly id = 'edit';
  readonly displayName = 'Edit';
  readonly description = 'AI-assisted code editing with human verification';
  readonly icon = '$(edit)';
  readonly defaultContextType = ContextType.SELECTED_FILES;
  readonly requiresHumanVerification = true;
  readonly supportsMultipleAgents = false;

  private pendingEdits: Map<string, { content: string, description: string }> = new Map();

  /**
     * Process a user message in Edit mode
     */
  async processMessage(
    message: string,
    agent: Agent,
    contextSource: ContextSource,
    _additionalParams?: Record<string, unknown>
  ): Promise<string> {
    try {
      Logger.instance.info(`Processing message in Edit mode: ${message}`);

      // Get context content
      const contextContent = await contextManager.getContextContent(contextSource);

      // Add memory context if available
      let memoryContext = '';
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          const relevantMemories = await agentMemory.getRelevantMemories(message);
          if (relevantMemories && relevantMemories.length > 0) {
            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to edit context`);
          }
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to retrieve memory context for edit:', memoryError);
        // Continue without memory context
      }

      // Prepare the prompt using promptManager
      const prompt = promptManager.renderPrompt('mode.edit', {
        contextContent,
        memoryContext,
        message
      });

      // Generate response using the agent
      const response = await agent.generate(prompt, this.getLLMParams());

      // Parse the response to extract file changes
      const fileChanges = this.parseFileChanges(response);

      // Store pending edits for user verification
      for (const [filePath, change] of Object.entries(fileChanges)) {
        this.pendingEdits.set(filePath, change);
      }

      // Store the edit session in memory
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          await agentMemory.addMessage('user', `Edit request: ${message}`);
          await agentMemory.addMessage('assistant', response);
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to store edit session in memory:', memoryError);
        // Continue without storing in memory
      }

      // Add verification UI to the response
      const responseWithVerification = `
${response}

---

I've analyzed the code and proposed the changes above. Would you like me to apply these changes?

[Apply Changes] [Review Changes] [Cancel]
`;

      return responseWithVerification;
    } catch (error) {
      Logger.instance.error('Error processing message in Edit mode:', error);
      return `Error processing your edit request: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
     * Parse file changes from the agent's response
     */
  private parseFileChanges(response: string): Record<string, { content: string, description: string }> {
    const fileChanges: Record<string, { content: string, description: string }> = {};

    // Simple regex-based parsing (could be improved with a more robust parser)
    const fileRegex = /### \[(.*?)\][\s\S]*?\`\`\`after([\s\S]*?)\`\`\`[\s\S]*?((?=### \[)|$)/g;
    const matches = response.matchAll(fileRegex);

    for (const match of matches) {
      const filePath = match[1].trim();
      const content = match[2].trim();
      const description = match[3]?.trim() || 'No description provided';

      fileChanges[filePath] = { content, description };
    }

    return fileChanges;
  }

  /**
     * Apply pending edits to files
     */
  async applyEdits(): Promise<void> {
    for (const [filePath, change] of this.pendingEdits.entries()) {
      try {
        // Create a URI for the file
        const uri = vscode.Uri.file(filePath);

        // Read the file
        const document = await vscode.workspace.openTextDocument(uri);

        // Create a WorkspaceEdit
        const edit = new vscode.WorkspaceEdit();

        // Replace the entire content of the file
        const fullRange = new vscode.Range(
          document.positionAt(0),
          document.positionAt(document.getText().length)
        );

        edit.replace(uri, fullRange, change.content);

        // Apply the edit
        await vscode.workspace.applyEdit(edit);

        Logger.instance.info(`Applied edit to ${filePath}`);
      } catch (error) {
        Logger.instance.error(`Error applying edit to ${filePath}:`, error);
        throw new Error(`Failed to apply edit to ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Clear pending edits
    this.pendingEdits.clear();
  }

  /**
     * Get LLM parameters specific to Edit mode
     */
  getLLMParams(): LLMGenerateParams {
    return {
      prompt: '',
      modelId: '',
      temperature: 0.3, // Lower temperature for more precise editing
      maxTokens: 2000,   // Longer responses for detailed edits
      mode: 'edit'
    };
  }

  /**
     * Get the system prompt for Edit mode
     */
  async getSystemPrompt(
    _agent: Agent,
    _contextSource: ContextSource
  ): Promise<string> {
    return `
You are an AI assistant specialized in editing code.
Your task is to help the user make changes to their codebase.
Analyze the code carefully and propose specific, well-thought-out changes.

When suggesting changes:
1. Provide a detailed plan of the changes to be made
2. For each file that needs to be modified, provide:
   a. The file path
   b. The exact code to be changed (before)
   c. The new code (after)
   d. A brief explanation of the change

Be precise and thorough in your analysis and suggestions.
Ensure that your proposed changes maintain the integrity and functionality of the codebase.
Consider potential side effects of your changes and address them in your plan.
`;
  }

  /**
     * Get UI components specific to Edit mode
     */
  getUIComponents(): {
    controlPanel?: string;
    contextPanel?: string;
    messageInput?: string;
  } {
    return {
      contextPanel: `
<div class="context-panel">
    <div class="context-header">
        <h3>Edit Context</h3>
        <div class="context-controls">
            <button id="btn-refresh-context" title="Refresh Context"><i class="codicon codicon-refresh"></i></button>
            <button id="btn-select-files" title="Select Files"><i class="codicon codicon-file-code"></i></button>
            <button id="btn-select-folders" title="Select Folders"><i class="codicon codicon-folder"></i></button>
        </div>
    </div>
    <div class="context-type">
        <select id="context-type-selector">
            <option value="selected_files">Selected Files</option>
            <option value="current_file">Current File</option>
            <option value="custom">Custom</option>
        </select>
    </div>
    <div id="context-files-list" class="context-files-list"></div>
</div>
`,
      messageInput: `
<div class="message-input-container">
    <textarea id="message-input" placeholder="Describe the changes you want to make..."></textarea>
    <button id="btn-send" title="Send"><i class="codicon codicon-send"></i></button>
</div>
`
    };
  }

  /**
     * Handle mode-specific commands
     */
  async handleCommand(command: string,
    _args: unknown[]
  ): Promise<void> {
    switch (command) {
      case 'applyEdits':
        await this.applyEdits();
        break;

      case 'reviewEdits':
        // Open a diff view for each pending edit
        for (const [filePath, change] of this.pendingEdits.entries()) {
          try {
            const uri = vscode.Uri.file(filePath);
            const document = await vscode.workspace.openTextDocument(uri);
            // Store original content for potential rollback
            const _originalContent = document.getText();

            // Create a temporary file for the diff
            const tempUri = uri.with({ scheme: 'untitled', path: `${uri.path}.new` });
            // Create temporary document for diff view
            await vscode.workspace.openTextDocument(tempUri);
            const edit = new vscode.WorkspaceEdit();
            edit.insert(tempUri, new vscode.Position(0, 0), change.content);
            await vscode.workspace.applyEdit(edit);

            // Show diff
            await vscode.commands.executeCommand('vscode.diff', uri, tempUri, `${filePath} (Changes)`);
          } catch (error) {
            Logger.instance.error(`Error showing diff for ${filePath}:`, error);
          }
        }
        break;

      case 'cancelEdits':
        this.pendingEdits.clear();
        break;
    }
  }
}
