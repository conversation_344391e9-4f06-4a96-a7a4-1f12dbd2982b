"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.showInformationMessage = showInformationMessage;
exports.showErrorMessage = showErrorMessage;
exports.showWarningMessage = showWarningMessage;
exports.shouldSuppressError = shouldSuppressError;
exports.showDetailedErrorMessage = showDetailedErrorMessage;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
const config_1 = require("../../config");
// List of error message patterns to suppress
const SUPPRESSED_ERROR_PATTERNS = [
    // Local provider connection errors
    'Failed to fetch Code Llama models',
    'LM Studio connection test failed',
    'Failed to initialize Codessa memory provider',
    'Connection refused',
    'connect ECONNREFUSED',
    'timeout',
    'ETIMEDOUT',
    'ESOCKETTIMEDOUT',
    'Failed to connect to lmstudio provider',
    'Failed to connect to ollama provider',
    'Failed to list models for provider',
    'Failed to initialize',
    'ECONNRESET',
    'socket hang up',
    'ECONNABORTED',
    'network error',
    'network timeout',
    'Failed to fetch models',
    'Failed to load models',
    'Could not connect to',
    'Unable to connect to',
    'Connection error',
    'Connection failed',
    'No connection',
    'Not running',
    'is not running',
    'is not available',
    'is not responding',
    'is offline',
    'could not be reached',
    'unreachable',
    'not reachable',
    'Failed to import',
    'Module not found',
    'Cannot find module',
    'Failed to load module',
    'Failed to resolve module',
    'Failed to open settings panel',
    'Failed to open AllSettingsPanel'
];
/**
 * Show an information message to the user
 * @param message The message to show
 * @param items Optional items to include as buttons
 * @returns A promise that resolves to the selected item or undefined
 */
function showInformationMessage(message, ...items) {
    return vscode.window.showInformationMessage(message, ...items);
}
/**
 * Show an error message to the user
 * @param message The message to show
 * @param items Optional items to include as buttons
 * @param options Additional options for showing the error
 * @returns A promise that resolves to the selected item or undefined
 */
function showErrorMessage(message, ...items) {
    // Check if we should suppress this error message
    if (shouldSuppressError(message)) {
        // Log the suppressed error but don't show it to the user
        logger_1.Logger.instance.debug(`Suppressed error notification: ${message}`);
        return Promise.resolve(undefined);
    }
    return vscode.window.showErrorMessage(message, ...items);
}
/**
 * Show a warning message to the user
 * @param message The message to show
 * @param items Optional items to include as buttons
 * @returns A promise that resolves to the selected item or undefined
 */
function showWarningMessage(message, ...items) {
    // Check if we should suppress this warning message
    if (shouldSuppressError(message)) {
        // Log the suppressed warning but don't show it to the user
        logger_1.Logger.instance.debug(`Suppressed warning notification: ${message}`);
        return Promise.resolve(undefined);
    }
    return vscode.window.showWarningMessage(message, ...items);
}
/**
 * Determines if an error message should be suppressed based on its content and provider configuration
 * @param message The error message to check
 * @returns True if the message should be suppressed, false otherwise
 */
function shouldSuppressError(message) {
    // Get the suppression setting
    const suppressProviderErrors = (0, config_1.getConfig)('suppressProviderErrors', true);
    if (!suppressProviderErrors) {
        return false;
    }
    // Get enabled providers from settings
    const config = vscode.workspace.getConfiguration('codessa.llm');
    const enabledProviders = config.get('enabledProviders') || ['ollama'];
    // Always suppress errors for disabled providers
    const allProviders = [
        'ollama', 'lmstudio', 'openai', 'anthropic', 'googleai', 'mistralai', 'cohere', 'deepseek',
        'openrouter', 'huggingface', 'starcoder', 'codellama', 'replit', 'wizardcoder',
        'xwincoder', 'phi', 'yicode', 'codegemma', 'santacoder', 'stablecode', 'perplexity'
    ];
    const disabledProviders = allProviders.filter(provider => !enabledProviders.includes(provider));
    // Check if the error is from a disabled provider
    for (const provider of disabledProviders) {
        if (message.toLowerCase().includes(provider.toLowerCase())) {
            logger_1.Logger.instance.debug(`Suppressing error for disabled provider ${provider}: ${message}`);
            return true;
        }
    }
    // Specifically suppress all LM Studio errors if LM Studio is not enabled
    if (message.toLowerCase().includes('lm studio') || message.toLowerCase().includes('lmstudio')) {
        if (!enabledProviders.includes('lmstudio')) {
            logger_1.Logger.instance.debug(`Suppressing LM Studio error (provider disabled): ${message}`);
            return true;
        }
    }
    // Always suppress errors for local providers during startup/initialization
    if (message.toLowerCase().includes('ollama') || message.toLowerCase().includes('lmstudio')) {
        // Check if this is an explicit user action or automatic initialization
        // If the message contains "test" or "check", it's likely from a user-initiated test
        const isUserInitiatedTest = message.toLowerCase().includes('test') ||
            message.toLowerCase().includes('check') ||
            message.toLowerCase().includes('manual') ||
            message.toLowerCase().includes('explicit');
        if (!isUserInitiatedTest) {
            // Suppress automatic initialization errors for local providers
            logger_1.Logger.instance.debug(`Suppressing automatic initialization error for local provider: ${message}`);
            return true;
        }
    }
    // Suppress errors for disabled providers
    if (message.toLowerCase().includes('provider') &&
        (message.toLowerCase().includes('disabled') ||
            message.toLowerCase().includes('not configured') ||
            message.toLowerCase().includes('not enabled'))) {
        logger_1.Logger.instance.debug(`Suppressing provider configuration error: ${message}`);
        return true;
    }
    // Suppress connection errors for providers that are not enabled
    const connectionErrorPatterns = [
        'connection refused', 'connection timed out', 'failed to connect',
        'econnrefused', 'etimedout', 'esockettimedout', 'network error',
        'connection error', 'timeout', 'unreachable', 'is it running',
        'please check if', 'make sure', 'server at', 'localhost:1234'
    ];
    if (connectionErrorPatterns.some(pattern => message.toLowerCase().includes(pattern))) {
        // Check if this error is for a disabled provider
        for (const provider of disabledProviders) {
            if (message.toLowerCase().includes(provider.toLowerCase())) {
                logger_1.Logger.instance.debug(`Suppressing connection error for disabled provider ${provider}: ${message}`);
                return true;
            }
        }
    }
    // Check if the message contains any of the suppressed patterns
    const shouldSuppress = SUPPRESSED_ERROR_PATTERNS.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()));
    if (shouldSuppress) {
        logger_1.Logger.instance.debug(`Suppressing error matching pattern: ${message}`);
    }
    return shouldSuppress;
}
/**
 * Show a detailed error message with an option to view more details
 * @param message The main error message
 * @param details The detailed error information
 * @param buttons Additional buttons to show
 * @returns A promise that resolves to the selected item or undefined
 */
async function showDetailedErrorMessage(message, details, buttons = []) {
    // Check if we should suppress this error message
    if (shouldSuppressError(message)) {
        // Log the suppressed error but don't show it to the user
        logger_1.Logger.instance.debug(`Suppressed detailed error notification: ${message}`);
        if (details) {
            logger_1.Logger.instance.debug(`Suppressed error details: ${details}`);
        }
        return Promise.resolve(undefined);
    }
    const items = [...buttons];
    if (details) {
        items.push('Show Details');
    }
    const selection = await vscode.window.showErrorMessage(message, ...items);
    if (selection === 'Show Details' && details) {
        const detailsDoc = await vscode.workspace.openTextDocument({
            content: details,
            language: 'text'
        });
        await vscode.window.showTextDocument(detailsDoc);
    }
    return selection;
}
//# sourceMappingURL=notifications.js.map