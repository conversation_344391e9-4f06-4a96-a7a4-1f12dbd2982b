{"version": 3, "file": "modeSelectorView.js", "sourceRoot": "", "sources": ["../../../src/ui/chat/modeSelectorView.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAuC;AACvC,yEAA8F;AAC9F,yCAAsC;AAEtC;;GAEG;AACH,MAAa,gBAAgB;IACpB,MAAM,CAAU,QAAQ,GAAG,sBAAsB,CAAC;IACjD,MAAM,CAAC,YAAY,CAA+B;IAEzC,MAAM,CAAsB;IAC5B,aAAa,CAAa;IACnC,YAAY,GAAwB,EAAE,CAAC;IACvC,eAAe,GAAwC,IAAI,MAAM,CAAC,YAAY,EAAkB,CAAC;IAEzF,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;IAE5D;;SAEK;IACE,MAAM,CAAC,YAAY,CAAC,YAAwB,EAAE,OAAgC;QACnF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YAC3C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEd,sCAAsC;QACtC,IAAI,gBAAgB,CAAC,YAAY,EAAE,CAAC;YAClC,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACpD,OAAO,gBAAgB,CAAC,YAAY,CAAC;QACvC,CAAC;QAED,gCAAgC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,gBAAgB,CAAC,QAAQ,EACzB,uBAAuB,EACvB,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;gBAC1C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC;aAC/C;SACF,CACF,CAAC;QAEF,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAEnE,wCAAwC;QACxC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAElC,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,YAAoB,KAA0B,EAAE,YAAwB;QACtE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,2BAA2B;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACrC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChB,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxB,KAAK,YAAY;oBACf,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAC1C,MAAM;YACV,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CAClB,CAAC;QAEF,0BAA0B;QAC1B,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC;IACvC,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,MAAc;QACzC,MAAM,IAAI,GAAG,qCAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,IAAI,EAAE,CAAC;YACT,eAAM,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YAC/D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;SAEK;IACG,OAAO;QACb,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;SAEK;IACG,kBAAkB,CAAC,OAAuB;QAChD,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;QAC5G,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC,CAAC;QAC5G,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC;QAC/G,MAAM,KAAK,GAAG,IAAA,gBAAQ,GAAE,CAAC;QAEzB,0BAA0B;QAC1B,MAAM,KAAK,GAAG,qCAAqB,CAAC,WAAW,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAgG,EAAE,EAAE,CAAC,CAAC;YACtJ,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,yBAAyB,EAAE,IAAI,CAAC,yBAAyB;YACzD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;SACpD,CAAC,CAAC,CAAC,CAAC;QAEL,OAAO;;;;;;+CAMoC,QAAQ;kGAC2C,OAAO,CAAC,SAAS,8BAA8B,KAAK,gBAAgB,OAAO,CAAC,SAAS;;;;;oCAKnJ,OAAO;;;;;;;;;iCASV,KAAK;;oCAEF,eAAe;;iCAElB,KAAK,UAAU,SAAS;;oBAErC,CAAC;IACnB,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC;QAE1C,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAC3C,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;;AAnKH,4CAoKC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { getNonce } from '../../utils';\nimport { IOperationMode, operationModeRegistry } from '../../agents/agentModes/operationMode';\nimport { logger } from '../../logger';\n\n/**\n * Mode selector webview panel\n */\nexport class ModeSelectorView {\n  public static readonly viewType = 'codessa.modeSelector';\n  private static currentPanel: ModeSelectorView | undefined;\n\n  private readonly _panel: vscode.WebviewPanel;\n  private readonly _extensionUri: vscode.Uri;\n  private _disposables: vscode.Disposable[] = [];\n  private _onModeSelected: vscode.EventEmitter<IOperationMode> = new vscode.EventEmitter<IOperationMode>();\n\n  public readonly onModeSelected = this._onModeSelected.event;\n\n  /**\n     * Create or show the mode selector panel\n     */\n  public static createOrShow(extensionUri: vscode.Uri, context: vscode.ExtensionContext): ModeSelectorView {\n    const column = vscode.window.activeTextEditor\n      ? vscode.window.activeTextEditor.viewColumn\n      : undefined;\n\n    // If we already have a panel, show it\n    if (ModeSelectorView.currentPanel) {\n      ModeSelectorView.currentPanel._panel.reveal(column);\n      return ModeSelectorView.currentPanel;\n    }\n\n    // Otherwise, create a new panel\n    const panel = vscode.window.createWebviewPanel(\n      ModeSelectorView.viewType,\n      'Codessa - Select Mode',\n      column || vscode.ViewColumn.One,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true,\n        localResourceRoots: [\n          vscode.Uri.joinPath(extensionUri, 'media'),\n          vscode.Uri.joinPath(extensionUri, 'resources')\n        ]\n      }\n    );\n\n    const modeSelectorView = new ModeSelectorView(panel, extensionUri);\n\n    // Register panel with extension context\n    context.subscriptions.push(panel);\n\n    return modeSelectorView;\n  }\n\n  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {\n    this._panel = panel;\n    this._extensionUri = extensionUri;\n\n    // Set initial HTML content\n    this._update();\n\n    // Handle panel disposal\n    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);\n\n    // Handle messages from the webview\n    this._panel.webview.onDidReceiveMessage(\n      async (message) => {\n        switch (message.command) {\n          case 'selectMode':\n            this._handleModeSelection(message.modeId);\n            break;\n        }\n      },\n      null,\n      this._disposables\n    );\n\n    // Cache the current panel\n    ModeSelectorView.currentPanel = this;\n  }\n\n  /**\n     * Handle mode selection\n     */\n  private _handleModeSelection(modeId: string): void {\n    const mode = operationModeRegistry.getMode(modeId);\n\n    if (mode) {\n      logger.info(`Selected mode: ${mode.displayName} (${mode.id})`);\n      this._onModeSelected.fire(mode);\n      this._panel.dispose();\n    } else {\n      logger.error(`Mode not found: ${modeId}`);\n    }\n  }\n\n  /**\n     * Update the webview content\n     */\n  private _update() {\n    const webview = this._panel.webview;\n    webview.html = this._getWebviewContent(webview);\n  }\n\n  /**\n     * Generate the webview HTML content\n     */\n  private _getWebviewContent(webview: vscode.Webview): string {\n    const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'modeSelector.js'));\n    const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'modeSelector.css'));\n    const logoUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'resources', 'codessa-logo.png'));\n    const nonce = getNonce();\n\n    // Get all available modes\n    const modes = operationModeRegistry.getAllModes();\n    const serializedModes = JSON.stringify(modes.map((mode: IOperationMode & { requiresHumanVerification?: boolean; supportsMultipleAgents?: boolean }) => ({\n      id: mode.id,\n      displayName: mode.displayName,\n      description: mode.description,\n      icon: mode.icon,\n      requiresHumanVerification: mode.requiresHumanVerification,\n      supportsMultipleAgents: mode.supportsMultipleAgents\n    })));\n\n    return `<!DOCTYPE html>\n            <html lang=\"en\">\n            <head>\n                <meta charset=\"UTF-8\">\n                <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n                <title>Codessa - Select Mode</title>\n                <link rel=\"stylesheet\" href=\"${styleUri}\">\n                <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; img-src ${webview.cspSource} https:; script-src 'nonce-${nonce}'; style-src ${webview.cspSource};\">\n            </head>\n            <body>\n                <div class=\"mode-selector-container\">\n                    <header class=\"mode-selector-header\">\n                        <img src=\"${logoUri}\" alt=\"Codessa Logo\" class=\"logo\" />\n                        <h1>Select Operation Mode</h1>\n                    </header>\n                    \n                    <div class=\"modes-grid\" id=\"modes-grid\">\n                        <!-- Modes will be inserted here by JavaScript -->\n                    </div>\n                </div>\n                \n                <script nonce=\"${nonce}\">\n                    // Available modes\n                    const modes = ${serializedModes};\n                </script>\n                <script nonce=\"${nonce}\" src=\"${scriptUri}\"></script>\n            </body>\n            </html>`;\n  }\n\n  /**\n     * Dispose of the panel\n     */\n  public dispose() {\n    ModeSelectorView.currentPanel = undefined;\n\n    // Clean up resources\n    this._panel.dispose();\n\n    while (this._disposables.length) {\n      const disposable = this._disposables.pop();\n      if (disposable) {\n        disposable.dispose();\n      }\n    }\n  }\n}\n"]}