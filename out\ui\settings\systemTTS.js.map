{"version": 3, "file": "systemTTS.js", "sourceRoot": "", "sources": ["../../../src/ui/settings/systemTTS.ts"], "names": [], "mappings": ";;;AACA,iDAAqC;AAErC,+CAAsD;AAEtD,MAAa,SAAS;IACpB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,MAAc;QACzF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,uCAAuC;YACvC,MAAM,OAAO,GAAG,wJAAwJ,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,qCAAqC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,qBAAqB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,mBAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;YAAM,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC1Y,IAAA,oBAAI,EAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBACtB,IAAI,KAAK;wBAAE,MAAM,CAAC,KAAK,CAAC,CAAC;;wBACpB,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,2BAA2B;YAC3B,MAAM,OAAO,GAAG,WAAW,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;YACnG,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,IAAA,oBAAI,EAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBACtB,IAAI,KAAK;wBAAE,MAAM,CAAC,KAAK,CAAC,CAAC;;wBACpB,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YAChC,gCAAgC;YAChC,MAAM,OAAO,GAAG,cAAc,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;YACnG,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,IAAA,oBAAI,EAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBACtB,IAAI,KAAK;wBAAE,MAAM,CAAC,KAAK,CAAC,CAAC;;wBACpB,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,SAAS;QACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,8CAA8C;YAC9C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC7B,IAAA,oBAAI,EAAC,gLAAgL,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;oBACrM,IAAI,GAAG;wBAAE,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC;oBAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAChE,EAAE,EAAE,IAAI;wBACR,IAAI;wBACJ,QAAQ,EAAE,EAAE;wBACZ,QAAQ,EAAE,yBAAW,CAAC,SAAS;qBAChC,CAAC,CAAC,CAAC;oBACJ,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,uCAAuC;YACvC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC7B,IAAA,oBAAI,EAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;oBAC/B,IAAI,GAAG;wBAAE,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC;oBAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBAC9D,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBACjC,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,yBAAW,CAAC,IAAI,EAAE,CAAC;oBACtE,CAAC,CAAC,CAAC;oBACH,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YAChC,6BAA6B;YAC7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC7B,IAAA,oBAAI,EAAC,iBAAiB,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;oBACtC,IAAI,GAAG;wBAAE,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC;oBAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC7C,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBACvC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;4BAAE,OAAO,IAAI,CAAC;wBAClC,OAAO,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,yBAAW,CAAC,IAAI,EAAE,CAAC;oBAC1F,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACnB,OAAO,CAAC,MAAoB,CAAC,CAAC;gBAChC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AAhFD,8BAgFC", "sourcesContent": ["import * as _vscode from 'vscode';\nimport { exec } from 'child_process';\nimport { Logger as _Logger } from '../../logger';\nimport { TTSProvider, TTSVoice } from './ttsSettings';\n\nexport class SystemTTS {\n  static async speak(text: string, voice: string, rate: number, pitch: number, volume: number): Promise<void> {\n    const platform = process.platform;\n    if (platform === 'win32') {\n      // Windows: Use PowerShell SAPI.SpVoice\n      const command = `PowerShell -Command \"$speak = New-Object -ComObject SAPI.SpVoice; try { $speak.Voice = ($speak.GetVoices() | Where-Object { $_.GetDescription() -eq '${voice.replace(/'/g, \"''\")}' })[0] } catch {}; $speak.Rate = ${Math.round((rate - 1) * 10)}; $speak.Volume = ${Math.round(volume * 100)}; $speak.Speak('${text.replace(/'/g, \"''\")}')\"`;      return new Promise((resolve, reject) => {\n        exec(command, (error) => {\n          if (error) reject(error);\n          else resolve();\n        });\n      });\n    } else if (platform === 'darwin') {\n      // macOS: Use 'say' command\n      const command = `say -v '${voice}' -r ${Math.round(rate * 200)} -- '${text.replace(/'/g, \"\\\\'\")}'`;\n      return new Promise((resolve, reject) => {\n        exec(command, (error) => {\n          if (error) reject(error);\n          else resolve();\n        });\n      });\n    } else if (platform === 'linux') {\n      // Linux: Use espeak or festival\n      const command = `espeak -v '${voice}' -s ${Math.round(rate * 100)} '${text.replace(/'/g, \"\\\\'\")}'`;\n      return new Promise((resolve, reject) => {\n        exec(command, (error) => {\n          if (error) reject(error);\n          else resolve();\n        });\n      });\n    } else {\n      throw new Error('System TTS not supported on this platform.');\n    }\n  }\n\n  static async getVoices(): Promise<TTSVoice[]> {\n    const platform = process.platform;\n    if (platform === 'win32') {\n      // Windows: Use PowerShell to list SAPI voices\n      return new Promise((resolve) => {\n        exec('PowerShell -Command \"Add-Type -AssemblyName System.Speech; (New-Object System.Speech.Synthesis.SpeechSynthesizer).GetInstalledVoices() | ForEach-Object { $_.VoiceInfo.Name }\"', (err, stdout) => {\n          if (err) return resolve([]);\n          const voices = stdout.split(/\\r?\\n/).filter(Boolean).map(name => ({\n            id: name,\n            name,\n            language: '',\n            provider: TTSProvider.MICROSOFT\n          }));\n          resolve(voices);\n        });\n      });\n    } else if (platform === 'darwin') {\n      // macOS: Use 'say -v ?' to list voices\n      return new Promise((resolve) => {\n        exec('say -v ?', (err, stdout) => {\n          if (err) return resolve([]);\n          const voices = stdout.split(/\\r?\\n/).filter(Boolean).map(line => {\n            const [name] = line.split(/\\s+/);\n            return { id: name, name, language: '', provider: TTSProvider.EDGE };\n          });\n          resolve(voices);\n        });\n      });\n    } else if (platform === 'linux') {\n      // Linux: Use espeak --voices\n      return new Promise((resolve) => {\n        exec('espeak --voices', (err, stdout) => {\n          if (err) return resolve([]);\n          const lines = stdout.split(/\\r?\\n/).slice(1);\n          const voices = lines.map(line => {\n            const parts = line.trim().split(/\\s+/);\n            if (parts.length < 4) return null;\n            return { id: parts[3], name: parts[3], language: parts[1], provider: TTSProvider.EDGE };\n          }).filter(Boolean);\n          resolve(voices as TTSVoice[]);\n        });\n      });\n    } else {\n      return [];\n    }\n  }\n}"]}