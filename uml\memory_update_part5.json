{"_type": "UMLClass", "_id": "AAAAAAGH1MemoryVectorStore=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "MemoryVectorStore", "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAGH1MemoryVectorStoreAttr1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "documents", "visibility": "private", "type": "Document[]", "defaultValue": "[]"}, {"_type": "UMLAttribute", "_id": "AAAAAAGH1MemoryVectorStoreAttr2=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "embeddings", "visibility": "private", "type": "Embeddings"}], "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1MemoryVectorStoreOp1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "constructor", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp1P1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp1="}, "name": "embeddings", "type": "Embeddings"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1MemoryVectorStoreOp2=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "initialize", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp2P1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp2="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1MemoryVectorStoreOp3=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "addDocuments", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp3P1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp3="}, "name": "documents", "type": "Document[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp3P2=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp3="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1MemoryVectorStoreOp4=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "addVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp4P1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp4="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp4P2=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp4="}, "name": "vector", "type": "number[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp4P3=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp4="}, "name": "metadata", "type": "Record<string, any>", "defaultValue": "{}"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp4P4=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp4="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1MemoryVectorStoreOp5=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "getVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp5P1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp5="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp5P2=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp5="}, "type": "Promise<number[] | undefined>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1MemoryVectorStoreOp6=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "deleteVector", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp6P1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp6="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp6P2=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp6="}, "type": "Promise<boolean>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1MemoryVectorStoreOp7=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "clearVectors", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp7P1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp7="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1MemoryVectorStoreOp8=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStore="}, "name": "searchSimilarVectors", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp8P1=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp8="}, "name": "vector", "type": "number[]"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp8P2=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp8="}, "name": "limit", "type": "number", "defaultValue": "5"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp8P3=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp8="}, "name": "filter", "type": "Record<string, any>", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1MemoryVectorStoreOp8P4=", "_parent": {"$ref": "AAAAAAGH1MemoryVectorStoreOp8="}, "type": "Promise<Array<{id: string, score: number}>>", "direction": "return"}]}], "isAbstract": false}