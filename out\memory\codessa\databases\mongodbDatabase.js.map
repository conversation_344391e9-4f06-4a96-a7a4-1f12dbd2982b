{"version": 3, "file": "mongodbDatabase.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/databases/mongodbDatabase.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AACzC,4CAA4C;AAC5C,qCAAsC;AAWtC;;GAEG;AACH,MAAa,eAAe;IAClB,MAAM,CAA0B;IAChC,EAAE,CAAiB;IACnB,WAAW,GAA8C,EAAE,CAAC;IAC5D,gBAAgB,CAAS;IACzB,YAAY,CAAS;IAE7B;QACE,IAAI,CAAC,gBAAgB,GAAG,IAAA,kBAAS,EAAS,0CAA0C,EAAE,EAAE,CAAC,CAAC;QAC1F,IAAI,CAAC,YAAY,GAAG,IAAA,kBAAS,EAAS,kCAAkC,EAAE,SAAS,CAAC,CAAC;IACvF,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;YAED,qBAAqB;YACrB,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAE5B,eAAe;YACf,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE5C,iCAAiC;YACjC,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAE3D,iBAAiB;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC;YACtE,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC,CAAC;YACpE,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC,CAAC;YACpE,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAEjE,eAAM,CAAC,IAAI,CAAC,2DAA2D,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAC9F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,MAAqB;QAC9D,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,UAAU,kBAAkB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC;YACH,gBAAgB;YAChB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAEpE,eAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,kBAAkB,UAAU,EAAE,CAAC,CAAC;YAC9F,OAAO,MAAM,CAAC,EAAE,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,EAAU;QACnD,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,UAAU,kBAAkB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC;YACH,aAAa;YACb,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAElE,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,iFAAiF;YACjF,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC;YAC3B,MAAM,WAAW,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;YAClC,OAAO,WAAW,CAAC,GAAG,CAAC;YAEvB,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,kBAAkB,MAAM,CAAC,OAAO,CAAC,qBAAqB,UAAU,EAAE,CAAC,CAAC;YACnH,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzF,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU,EAAE,MAA8B;QACtF,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,UAAU,kBAAkB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC;YACH,gBAAgB;YAChB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,SAAS,CACzD,EAAE,EAAE,EAAE,EACN,EAAE,IAAI,EAAE,MAAM,EAAE,CACjB,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACzE,OAAO,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,kBAAkB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1F,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU;QACtD,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,UAAU,kBAAkB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC;YACH,gBAAgB;YAChB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAEpE,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,oBAAoB,UAAU,EAAE,CAAC,CAAC;YAC3E,OAAO,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5F,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,KAA8B,EAAE,KAAc;QAC1F,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,UAAU,kBAAkB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,UAAU,GAA4B,EAAE,CAAC;YAE/C,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;gBACxB,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;oBACpB,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAwB,CAAC;oBACpD,UAAU,CAAC,KAAK,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,gBAAgB;YAChB,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE3D,WAAW;YACX,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAExC,YAAY;YACZ,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;YAED,cAAc;YACd,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;YAEvC,sFAAsF;YACtF,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC1B,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC;gBAC3B,MAAM,WAAW,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;gBAClC,OAAO,WAAW,CAAC,GAAG,CAAC;gBAEvB,eAAM,CAAC,KAAK,CAAC,gCAAgC,WAAW,CAAC,EAAE,kBAAkB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACjG,OAAO,WAAW,CAAC;YACrB,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,eAAe,CAAC,UAAkB;QAC7C,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,UAAU,kBAAkB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC;YACH,qBAAqB;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAElD,eAAM,CAAC,IAAI,CAAC,sBAAsB,UAAU,EAAE,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA7MD,0CA6MC", "sourcesContent": ["import type { IDatabase } from '../../types';\nimport { logger } from '../../../logger';\nimport { getConfig } from '../../../config';\nimport { MongoClient } from 'mongodb';\nimport type { Collection, Db } from 'mongodb';\n\n/**\n * Interface for MongoDB documents\n */\ninterface MongoDocument {\n    id: string;\n    [key: string]: unknown;\n}\n\n/**\n * MongoDB database implementation\n */\nexport class MongoDBDatabase implements IDatabase {\n  private client: MongoClient | undefined;\n  private db: Db | undefined;\n  private collections: Record<string, Collection<MongoDocument>> = {};\n  private connectionString: string;\n  private databaseName: string;\n\n  constructor() {\n    this.connectionString = getConfig<string>('memory.database.mongodb.connectionString', '');\n    this.databaseName = getConfig<string>('memory.database.mongodb.database', 'codessa');\n  }\n\n  /**\n     * Initialize the database\n     */\n  public async initialize(): Promise<void> {\n    try {\n      if (!this.connectionString) {\n        throw new Error('MongoDB connection string not configured');\n      }\n            \n      // Connect to MongoDB\n      this.client = new MongoClient(this.connectionString);\n      await this.client.connect();\n            \n      // Get database\n      this.db = this.client.db(this.databaseName);\n            \n      // Create collections and indexes\n      this.collections.memories = this.db.collection('memories');\n            \n      // Create indexes\n      await this.collections.memories.createIndex({ timestamp: -1 });\n      await this.collections.memories.createIndex({ 'metadata.source': 1 });\n      await this.collections.memories.createIndex({ 'metadata.type': 1 });\n      await this.collections.memories.createIndex({ 'metadata.tags': 1 });\n      await this.collections.memories.createIndex({ content: 'text' });\n            \n      logger.info(`MongoDB database initialized successfully with database ${this.databaseName}`);\n    } catch (error) {\n      logger.error('Failed to initialize MongoDB database:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Add a record\n     */\n  public async addRecord(collection: string, record: MongoDocument): Promise<string> {\n    if (!this.db || !this.collections[collection]) {\n      throw new Error(`Database or collection ${collection} not initialized`);\n    }\n\n    try {\n      // Insert record\n      const result = await this.collections[collection].insertOne(record);\n            \n      logger.debug(`Added record with ID ${String(result.insertedId)} to collection ${collection}`);\n      return record.id;\n    } catch (error) {\n      logger.error(`Failed to add record to collection ${collection}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n     * Get a record by ID\n     */\n  public async getRecord(collection: string, id: string): Promise<MongoDocument | undefined> {\n    if (!this.db || !this.collections[collection]) {\n      throw new Error(`Database or collection ${collection} not initialized`);\n    }\n\n    try {\n      // Get record\n      const record = await this.collections[collection].findOne({ id });\n            \n      if (!record) {\n        return undefined;\n      }\n            \n      // Extract MongoDB's internal _id for logging and create clean application record\n      const mongoId = record._id;\n      const cleanRecord = { ...record };\n      delete cleanRecord._id;\n\n      logger.debug(`Retrieved record with app ID ${id} (MongoDB _id: ${String(mongoId)}) from collection ${collection}`);\n      return cleanRecord;\n    } catch (error) {\n      logger.error(`Failed to get record with ID ${id} from collection ${collection}:`, error);\n      return undefined;\n    }\n  }\n\n  /**\n     * Update a record\n     */\n  public async updateRecord(collection: string, id: string, record: Partial<MongoDocument>): Promise<boolean> {\n    if (!this.db || !this.collections[collection]) {\n      throw new Error(`Database or collection ${collection} not initialized`);\n    }\n\n    try {\n      // Update record\n      const result = await this.collections[collection].updateOne(\n        { id },\n        { $set: record }\n      );\n            \n      logger.debug(`Updated record with ID ${id} in collection ${collection}`);\n      return result.modifiedCount > 0;\n    } catch (error) {\n      logger.error(`Failed to update record with ID ${id} in collection ${collection}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Delete a record\n     */\n  public async deleteRecord(collection: string, id: string): Promise<boolean> {\n    if (!this.db || !this.collections[collection]) {\n      throw new Error(`Database or collection ${collection} not initialized`);\n    }\n\n    try {\n      // Delete record\n      const result = await this.collections[collection].deleteOne({ id });\n            \n      logger.debug(`Deleted record with ID ${id} from collection ${collection}`);\n      return result.deletedCount > 0;\n    } catch (error) {\n      logger.error(`Failed to delete record with ID ${id} from collection ${collection}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Query records\n     */\n  public async queryRecords(collection: string, query: Record<string, unknown>, limit?: number): Promise<MongoDocument[]> {\n    if (!this.db || !this.collections[collection]) {\n      throw new Error(`Database or collection ${collection} not initialized`);\n    }\n\n    try {\n      // Build MongoDB query\n      const mongoQuery: Record<string, unknown> = {};\n            \n      for (const key in query) {\n        if (key === '$text') {\n          const textQuery = query[key] as { $search: string };\n          mongoQuery.$text = { $search: textQuery.$search };\n        } else {\n          mongoQuery[key] = query[key];\n        }\n      }\n            \n      // Execute query\n      let cursor = this.collections[collection].find(mongoQuery);\n            \n      // Add sort\n      cursor = cursor.sort({ timestamp: -1 });\n            \n      // Add limit\n      if (limit) {\n        cursor = cursor.limit(limit);\n      }\n            \n      // Get results\n      const records = await cursor.toArray();\n            \n      // Transform MongoDB records to application format, logging MongoDB _ids for debugging\n      return records.map(record => {\n        const mongoId = record._id;\n        const cleanRecord = { ...record };\n        delete cleanRecord._id;\n\n        logger.debug(`Processed record with app ID ${cleanRecord.id} (MongoDB _id: ${String(mongoId)})`);\n        return cleanRecord;\n      });\n    } catch (error) {\n      logger.error(`Failed to query records from collection ${collection}:`, error);\n      return [];\n    }\n  }\n\n  /**\n     * Clear all records in a collection\n     */\n  public async clearCollection(collection: string): Promise<void> {\n    if (!this.db || !this.collections[collection]) {\n      throw new Error(`Database or collection ${collection} not initialized`);\n    }\n\n    try {\n      // Delete all records\n      await this.collections[collection].deleteMany({});\n            \n      logger.info(`Cleared collection ${collection}`);\n    } catch (error) {\n      logger.error(`Failed to clear collection ${collection}:`, error);\n      throw error;\n    }\n  }\n}\n"]}