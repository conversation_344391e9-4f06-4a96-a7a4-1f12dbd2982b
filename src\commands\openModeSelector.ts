import * as vscode from 'vscode';
import { ModeSelectorView } from '../ui/chat/modeSelectorView';
import { ChatPanel } from '../ui/chat/chatView';
import { agentManager } from '../agents/agentUtilities/agentManager';
import { logger } from '../logger';
import { IOperationMode, operationModeRegistry } from '../agents/agentModes/operationMode';
import receiverAgent from '../agents/agentTypes/receiverAgent';
import { SupervisorAgent } from '../agents/agentTypes/supervisorAgent';
import { createModeAgent } from '../agents/agentUtilities/agentFactory';

/**
 * Initialize the multi-agent system with specialized agents for each mode
 */
async function initializeAgentSystem(context: vscode.ExtensionContext): Promise<SupervisorAgent> {
  try {
    logger.info('Initializing multi-agent system...');

    // Create receiver agent for preprocessing user input
    const receiverAgentInstance = new receiverAgent({
      id: 'receiver-agent',
      name: 'Receiver Agent',
      description: 'Preprocesses user input to enhance clarity and detail',
      systemPromptName: 'receiverAgent',
      tools: ['file', 'search', 'memory']
    });

    // Create supervisor agent for orchestrating everything
    const supervisorAgent = new SupervisorAgent({
      id: 'supervisor-agent',
      name: 'Supervisor Agent',
      description: 'Orchestrates specialized agents and workflows',
      systemPromptName: 'supervisorAgent',
      tools: ['file', 'search', 'memory', 'workflow'],
      isSupervisor: true
    }, receiverAgentInstance);

    // Register the core agents
    agentManager.addAgent(receiverAgentInstance);
    agentManager.addAgent(supervisorAgent);

    // Create specialized agents for each mode
    const modes = operationModeRegistry.getAllModes();
    for (const mode of modes) {
      const agentConfig = {
        id: `${mode.id}-agent`,
        name: `${mode.displayName} Agent`,
        description: `Specialized agent for ${mode.displayName} mode`,
        systemPromptName: `${mode.id}Agent`,
        tools: ['file', 'search', 'memory', 'workflow']
      };

      // Create a specialized agent for this mode
      const modeAgent = createModeAgent(mode.id, agentConfig);
      agentManager.addAgent(modeAgent);

      // Register with supervisor
      supervisorAgent.registerModeAgent(mode.id, modeAgent);
      logger.info(`Created and registered specialized agent for ${mode.displayName} mode`);
    }

    // Store in global state for access from other parts of the extension
    context.globalState.update('receiverAgent', receiverAgentInstance);
    context.globalState.update('supervisorAgent', supervisorAgent);

    logger.info('Multi-agent system initialized successfully');
    return supervisorAgent;
  } catch (error) {
    logger.error('Error initializing multi-agent system:', error);
    throw error;
  }
}

/**
 * Get the supervisor agent or initialize the agent system if needed
 */
async function getSupervisorAgent(context: vscode.ExtensionContext): Promise<SupervisorAgent | null> {
  // Check if supervisor agent exists in global state
  let supervisorAgent = context.globalState.get('supervisorAgent') as SupervisorAgent;

  if (!supervisorAgent) {
    try {
      // Initialize the agent system
      supervisorAgent = await initializeAgentSystem(context);
    } catch (error) {
      logger.error('Failed to initialize multi-agent system:', error);
      vscode.window.showErrorMessage('Failed to initialize multi-agent system');
      return null;
    }
  }

  return supervisorAgent;
}

/**
 * Open the mode selector
 */
export async function openModeSelector(context: vscode.ExtensionContext): Promise<void> {
  try {
    logger.info('Opening mode selector...');

    // Create the mode selector view
    const modeSelectorView = ModeSelectorView.createOrShow(context.extensionUri, context);

    // Handle mode selection
    modeSelectorView.onModeSelected(async (mode: IOperationMode) => {
      logger.info(`Mode selected: ${mode.displayName} (${mode.id})`);

      // Get the supervisor agent
      const supervisorAgent = await getSupervisorAgent(context);
      if (!supervisorAgent) {
        return;
      }

      // Open chat panel with the selected mode and supervisor agent
      ChatPanel.createOrShow(context.extensionUri, supervisorAgent, context, mode);
    });

    logger.info('Mode selector opened successfully');
  } catch (error) {
    logger.error('Error opening mode selector:', error);
    vscode.window.showErrorMessage(`Failed to open mode selector: ${error instanceof Error ? error.message : String(error)}`);
  }
}