{"version": 3, "file": "providersModelsHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/providersModelsHandler.ts"], "names": [], "mappings": ";;AACA,oEA6GC;AA9GD,yDAAyD;AACzD,SAAgB,4BAA4B,CAAC,OAAY,EAAE,KAAU;IACnE,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IACjF,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IACxE,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtJ,IAAI,CAAC;QACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC;gBACnC,MAAM;YACR,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;gBAChC,MAAM;YACR,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAChD,QAAQ,CAAC,KAAK,GAAG,mCAAmC,CAAC;oBACrD,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACzB,MAAM;YACR,CAAC;YACD,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAC9B,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;oBACzC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC3E,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC;gBACtE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACxC,MAAM;YACR,CAAC;YACD,KAAK,gBAAgB,CAAC,CAAC,CAAC;gBACtB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;oBACzC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAClE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAClC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,KAAK,UAAU,CAAC,CAAC,CAAC;gBAChB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC5B,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;oBACvC,QAAQ,CAAC,KAAK,GAAG,gCAAgC,CAAC;oBAClD,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC;gBACtB,MAAM;YACR,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC5B,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;oBACxB,QAAQ,CAAC,KAAK,GAAG,oBAAoB,CAAC;oBACtC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;gBACrE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,kBAAkB,CAAC;oBACpC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC;gBAC7D,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACrC,MAAM;YACR,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,oBAAoB,CAAC;oBACtC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC/D,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,kBAAkB,CAAC;oBACpC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC/B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC3C,CAAC;QACD,CAAC;IACH,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for Providers & Models CRUD messages and logic\nexport function handleProvidersModelsMessage(message: any, panel: any) {\n  const settings = (window as any).settings || {};\n  settings.providers = Array.isArray(settings.providers) ? settings.providers : [];\n  settings.models = Array.isArray(settings.models) ? settings.models : [];\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: message.type, success: false, data: null, error: null };\n  try {\n    switch (message.type) {\n    case 'getProviders': {\n      response.success = true;\n      response.data = settings.providers;\n      break;\n    }\n    case 'getModels': {\n      response.success = true;\n      response.data = settings.models;\n      break;\n    }\n    case 'addProvider': {\n      const provider = message.provider;\n      if (!provider || !provider.name || !provider.id) {\n        response.error = 'Missing required provider fields.';\n        break;\n      }\n      settings.providers.push(provider);\n      response.success = true;\n      response.data = provider;\n      break;\n    }\n    case 'editProvider': {\n      const provider = message.provider;\n      if (!provider || !provider.id) {\n        response.error = 'Provider id required.';\n        break;\n      }\n      const idx = settings.providers.findIndex((p: any) => p.id === provider.id);\n      if (idx === -1) {\n        response.error = 'Provider not found.';\n        break;\n      }\n      settings.providers[idx] = { ...settings.providers[idx], ...provider };\n      response.success = true;\n      response.data = settings.providers[idx];\n      break;\n    }\n    case 'deleteProvider': {\n      const id = message.id;\n      if (!id) {\n        response.error = 'Provider id required.';\n        break;\n      }\n      const idx = settings.providers.findIndex((p: any) => p.id === id);\n      if (idx === -1) {\n        response.error = 'Provider not found.';\n        break;\n      }\n      settings.providers.splice(idx, 1);\n      response.success = true;\n      break;\n    }\n    case 'addModel': {\n      const model = message.model;\n      if (!model || !model.name || !model.id) {\n        response.error = 'Missing required model fields.';\n        break;\n      }\n      settings.models.push(model);\n      response.success = true;\n      response.data = model;\n      break;\n    }\n    case 'editModel': {\n      const model = message.model;\n      if (!model || !model.id) {\n        response.error = 'Model id required.';\n        break;\n      }\n      const idx = settings.models.findIndex((m: any) => m.id === model.id);\n      if (idx === -1) {\n        response.error = 'Model not found.';\n        break;\n      }\n      settings.models[idx] = { ...settings.models[idx], ...model };\n      response.success = true;\n      response.data = settings.models[idx];\n      break;\n    }\n    case 'deleteModel': {\n      const id = message.id;\n      if (!id) {\n        response.error = 'Model id required.';\n        break;\n      }\n      const idx = settings.models.findIndex((m: any) => m.id === id);\n      if (idx === -1) {\n        response.error = 'Model not found.';\n        break;\n      }\n      settings.models.splice(idx, 1);\n      response.success = true;\n      break;\n    }\n    default: {\n      response.error = 'Unknown message type.';\n    }\n    }\n  } catch (err: any) {\n    response.error = err?.message || String(err);\n  }\n  panel.postMessage(response);\n}\n\n"]}