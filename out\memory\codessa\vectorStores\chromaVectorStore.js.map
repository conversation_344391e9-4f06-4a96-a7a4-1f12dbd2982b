{"version": 3, "file": "chromaVectorStore.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/vectorStores/chromaVectorStore.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,yEAAgE;AAChE,yEAAkE;AAElE,4CAAyC,CAAC,oEAAoE;AAC9G,4CAA4C,CAAC,oEAAoE;AACjH,2CAA6B;AAC7B,uCAAyB;AAmDzB;;;GAGG;AACH,MAAa,iBAAiB;IAC5B,0CAA0C;IAClC,WAAW,CAAsC;IACxC,UAAU,CAAa;IACvB,MAAM,CAA0B;IACzC,aAAa,GAAG,KAAK,CAAC;IACtB,cAAc,GAAG,KAAK,CAAC,CAAC,0BAA0B;IAE1D;;;SAGK;IACL,YAAY,UAAsB;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,MAAM,cAAc,GAAG,IAAA,kBAAS,EAAS,0CAA0C,EAAE,kBAAkB,CAAC,CAAC;QACzG,MAAM,SAAS,GAAG,IAAA,kBAAS,EAAS,qCAAqC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,uBAAuB;QACnJ,MAAM,WAAW,GAAG,IAAA,kBAAS,EAAyB,uCAAuC,EAAE,QAAQ,CAAC,CAAC;QAEzG,IAAI,CAAC,MAAM,GAAG;YACZ,cAAc;YACd,SAAS;YACT,WAAW;SACZ,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,6CAA6C,IAAI,CAAC,MAAM,CAAC,cAAc,iBAAiB,IAAI,CAAC,MAAM,CAAC,SAAS,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC;IACpK,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,kDAAkD;gBAClD,MAAM,IAAI,OAAO,CAAO,OAAO,CAAC,EAAE;oBAChC,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;wBACrC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;4BACzB,aAAa,CAAC,aAAa,CAAC,CAAC;4BAC7B,OAAO,EAAE,CAAC;wBACZ,CAAC;oBACH,CAAC,EAAE,GAAG,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,eAAM,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;YACzD,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,eAAM,CAAC,IAAI,CAAC,uCAAuC,IAAI,CAAC,MAAM,CAAC,SAAS,mBAAmB,IAAI,CAAC,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;QAE5H,IAAI,CAAC;YACH,oDAAoD;YACpD,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,kCAAkC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAExE,gEAAgE;YAChE,6EAA6E;YAC7E,4EAA4E;YAC5E,yCAAyC;YACzC,IAAI,CAAC,WAAW,GAAG,MAAM,qBAAM,CAAC,aAAa,CAC3C,EAAE,EAAE,6DAA6D;YACjE,IAAI,CAAC,UAAU,EACf;gBACE,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;gBAC1C,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,mCAAmC;gBAC5E,kBAAkB,EAAE;oBAClB,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;iBACtC;gBACD,8DAA8D;aAC/D;YACD,0DAA0D;aACrB,CAAC;YAGxC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;YAC9E,CAAC;YAED,oFAAoF;YACpF,uDAAuD;YACvD,gEAAgE;YAEhE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,6DAA6D,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QACzG,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE;gBACxD,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;YACH,kCAAkC;YAClC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;QACnE,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC7C,eAAM,CAAC,KAAK,CAAC,8DAA8D,EAAE;gBAC3E,WAAW,EAAE,IAAI,CAAC,aAAa;gBAC/B,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW;aAC7B,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAED;;;;;;;;SAQK;IACE,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,MAAgB,EAAE,QAAgE;QACnH,qDAAqD;QACrD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,kDAAkD,EAAE,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC;QACrC,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YACjE,eAAM,CAAC,IAAI,CAAC,4DAA4D,EAAE,sBAAsB,CAAC,CAAC;YAClG,MAAM,IAAI,KAAK,CAAC,oEAAoE,EAAE,EAAE,CAAC,CAAC;QAC5F,CAAC;QAED,8DAA8D;QAC9D,iCAAiC;QACjC,MAAM,QAAQ,GAAG,IAAI,uBAAQ,CAAC;YAC5B,WAAW,EAAE,WAAW;YACxB,QAAQ,EAAE;gBACR,GAAG,QAAQ;gBACX,wDAAwD;gBACxD,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,yBAAyB,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YACjG,oEAAoE;YACpE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAEhF,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gBACjE,eAAM,CAAC,IAAI,CAAC,8DAA8D,EAAE,eAAe,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACvH,0DAA0D;gBAC1D,2EAA2E;YAC7E,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,aAAa,CAAC,CAAC;YACtE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,aAAa,EAAE;gBAC9D,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,UAAU;gBACjB,EAAE,EAAE,EAAE;gBACN,mFAAmF;gBACnF,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;aACpC,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,+BAA+B,EAAE,KAAK,YAAY,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,SAAS,CAAC,EAAU;QAC/B,qDAAqD;QACrD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,2BAA2B,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAEjH,0DAA0D;YAC1D,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YACD,MAAM,MAAM,GAAsB,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;gBAC3D,GAAG,EAAE,CAAC,EAAE,CAAC;gBACT,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC,0BAA0B;aACnD,CAAC,CAAC;YAEH,iCAAiC;YACjC,IAAI,MAAM,EAAE,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnF,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACrC,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7C,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,GAAG,CAAC,CAAC;oBAC5D,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,mCAAmC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC;YACnG,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,6DAA6D;YAC7D,IAAI,YAAY,EAAE,QAAQ,CAAC,iBAAiB,CAAC,IAAI,YAAY,EAAE,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC5F,eAAM,CAAC,IAAI,CAAC,4DAA4D,EAAE,kCAAkC,CAAC,CAAC;YAChH,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,eAAe,EAAE;oBAC9D,OAAO,EAAE,YAAY;oBACrB,KAAK,EAAE,UAAU;oBACjB,EAAE,EAAE,EAAE;iBACP,CAAC,CAAC;YACL,CAAC;YACD,+EAA+E;YAC/E,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,YAAY,CAAC,EAAU;QAClC,qDAAqD;QACrD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,2BAA2B,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC/G,6DAA6D;YAC7D,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YACD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAEhE,qGAAqG;YACrG,yDAAyD;YACzD,eAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE,0BAA0B,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;YAC1I,OAAO,IAAI,CAAC,CAAC,uDAAuD;QAEtE,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,eAAe,EAAE;gBACjE,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,UAAU;gBACjB,EAAE,EAAE,EAAE;aACP,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,YAAY;QACvB,qDAAqD;QACrD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,eAAM,CAAC,IAAI,CAAC,0DAA0D,IAAI,CAAC,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;QAEvG,IAAI,CAAC;YACH,qEAAqE;YACrE,+EAA+E;YAC/E,IAAI,CAAC;gBACH,+CAA+C;gBAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACtB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;gBAClD,CAAC;gBACD,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC7C,eAAM,CAAC,IAAI,CAAC,2DAA2D,IAAI,CAAC,MAAM,CAAC,cAAc,4BAA4B,CAAC,CAAC;gBAC/H,OAAO,CAAC,qBAAqB;YAC/B,CAAC;YAAC,OAAO,CAAU,EAAE,CAAC;gBACpB,MAAM,YAAY,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAChE,yEAAyE;gBACzE,eAAM,CAAC,IAAI,CAAC,+EAA+E,YAAY,0BAA0B,CAAC,CAAC;YACrI,CAAC;YAED,kEAAkE;YAClE,eAAM,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;YACtE,4FAA4F;YAC5F,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YACD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;gBACjD,4BAA4B;gBAC5B,OAAO,EAAE,EAAE,EAAE,gBAAgB;gBAC7B,2DAA2D;aAC5D,CAAC,CAAC;YAEH,IAAI,eAAe,EAAE,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3D,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC;gBACnC,eAAM,CAAC,KAAK,CAAC,aAAa,MAAM,CAAC,MAAM,2CAA2C,CAAC,CAAC;gBAEpF,+DAA+D;gBAC/D,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,8BAA8B;gBACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;oBAClD,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;oBAChD,6CAA6C;oBAC7C,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACjD,eAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG,SAAS,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC7D,CAAC;gBACD,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,CAAC,MAAM,mCAAmC,IAAI,CAAC,MAAM,CAAC,cAAc,0BAA0B,CAAC,CAAC;YAChJ,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,MAAM,CAAC,cAAc,6CAA6C,CAAC,CAAC;YAC5G,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,kDAAkD,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,EAAE;gBAC5F,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,UAAU;aAClB,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,4BAA4B,YAAY,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;;;;;;;SAQK;IACE,KAAK,CAAC,oBAAoB,CAAC,MAAgB,EAAE,KAAK,GAAG,CAAC,EAAE,MAA8D;QAC3H,qDAAqD;QACrD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,eAAM,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,iBAAiB,KAAK,yCAAyC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YAErJ,IAAI,iBAAiB,GAAyB,EAAE,CAAC;YAEjD,2DAA2D;YAC3D,2DAA2D;YAC3D,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,iCAAiC,EAAE,CAAC;gBACvD,eAAM,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;gBAC3D,iBAAiB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,iCAAiC,CAC1E,MAAM,EACN,KAAK,EACL,MAAM,CACP,CAAC;YACJ,CAAC;iBAAM,CAAC,CAAC,kEAAkE;gBACzE,eAAM,CAAC,IAAI,CAAC,mIAAmI,CAAC,CAAC;gBACjJ,mDAAmD;gBACnD,kFAAkF;gBAClF,iBAAiB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAClE,iCAAiC,EAAE,qBAAqB;gBACxD,KAAK,EACL,MAAM,CACP,CAAC;YACJ,CAAC;YACD,+FAA+F;YAC/F,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,iCAAiC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC;gBACvG,MAAM,IAAI,KAAK,CAAC,gJAAgJ,CAAC,CAAC;YACpK,CAAC;YAGD,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACvB,eAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBACrE,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,oEAAoE;YACpE,MAAM,aAAa,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC3D,uEAAuE;gBACvE,MAAM,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,MAA4B,CAAC;gBACtD,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,eAAM,CAAC,IAAI,CAAC,wDAAwD,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACpF,OAAO,IAAI,CAAC,CAAC,+CAA+C;gBAC9D,CAAC;gBAED,gFAAgF;gBAChF,yEAAyE;gBACzE,qEAAqE;gBACrE,gEAAgE;gBAChE,MAAM,eAAe,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC;gBAElG,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;YACpD,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAoC,CAAC,CAAC,mBAAmB;YAE5F,eAAM,CAAC,IAAI,CAAC,SAAS,aAAa,CAAC,MAAM,uCAAuC,CAAC,CAAC;YAClF,OAAO,aAAa,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YACpE,eAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE;gBAC9D,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,KAAK;gBACZ,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,wCAAwC;aACxF,CAAC,CAAC;YACH,8BAA8B;YAC9B,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AA3aD,8CA2aC", "sourcesContent": ["import type { Embeddings } from '../../../agents/workflows/corePolyfill';\nimport { Chroma } from '../../../agents/workflows/corePolyfill';\nimport { Document } from '../../../agents/workflows/corePolyfill';\nimport type { IVectorStore } from '../../types'; // Assuming this path is correct relative to the final file location\nimport { logger } from '../../../logger'; // Assuming this path is correct relative to the final file location\nimport { getConfig } from '../../../config'; // Assuming this path is correct relative to the final file location\nimport * as path from 'path';\nimport * as fs from 'fs';\n\n// Define expected response structures if not provided by the polyfill's types\nexport interface ChromaGetResponse {\n  ids: string[];\n  embeddings: number[][] | null;\n  documents: string[] | null;\n  metadatas: Record<string, unknown>[] | null;\n}\n\n// Interface representing the expected methods on the polyfilled Chroma class instance\n// Based on common LangChain Chroma wrapper patterns and the methods used in the original code.\nexport interface IChromaPolyfillInstance {\n  addDocuments(documents: Document[], options?: { ids?: string[] }): Promise<string[]>;\n  get(options?: {\n    ids?: string[];\n    where?: Record<string, unknown>;\n    limit?: number;\n    offset?: number;\n    include?: ('documents' | 'metadatas' | 'embeddings')[];\n    whereDocument?: Record<string, unknown>;\n  }): Promise<ChromaGetResponse>;\n  delete(options?: {\n    ids?: string[];\n    where?: Record<string, unknown>;\n    whereDocument?: Record<string, unknown>;\n  }): Promise<string[]>; // Returns IDs of deleted items\n  similaritySearchWithScore(\n    query: string, // LangChain standard often uses text query\n    k: number,\n    filter?: Record<string, unknown> | undefined,\n    // include?: ('documents' | 'metadatas' | 'distances')[] | undefined // Not standard in similaritySearchWithScore\n  ): Promise<[Document, number][]>; // Returns Documents and scores\n\n  // If the polyfill *truly* supports direct vector search (less common in basic LangChain wrappers):\n  similaritySearchByVectorWithScore?(\n    embedding: number[],\n    k: number,\n    filter?: Record<string, unknown> | undefined,\n  ): Promise<[Document, number][]>;\n}\n\n/**\n * Configuration interface for ChromaVectorStore\n */\ninterface ChromaVectorStoreConfig {\n  collectionName: string;\n  directory: string;\n  vectorSpace: 'cosine' | 'l2' | 'ip'; // Inner product\n}\n\n/**\n * Chroma vector store implementation using a polyfill.\n * Provides production-ready features for managing vector embeddings with Chroma.\n */\nexport class ChromaVectorStore implements IVectorStore {\n  // Use the specific polyfill instance type\n  private vectorStore: IChromaPolyfillInstance | undefined;\n  private readonly embeddings: Embeddings;\n  private readonly config: ChromaVectorStoreConfig;\n  private isInitialized = false;\n  private isInitializing = false; // Prevent race conditions\n\n  /**\n     * Creates an instance of ChromaVectorStore.\n     * @param embeddings - The embeddings model instance to use.\n     */\n  constructor(embeddings: Embeddings) {\n    this.embeddings = embeddings;\n\n    const collectionName = getConfig<string>('memory.vectorStore.chroma.collectionName', 'codessa_memories');\n    const directory = getConfig<string>('memory.vectorStore.chroma.directory', path.resolve(process.cwd(), '.codessa/chroma')); // Ensure absolute path\n    const vectorSpace = getConfig<'cosine' | 'l2' | 'ip'>('memory.vectorStore.chroma.vectorSpace', 'cosine');\n\n    this.config = {\n      collectionName,\n      directory,\n      vectorSpace,\n    };\n\n    logger.info(`ChromaVectorStore configured: Collection='${this.config.collectionName}', Directory='${this.config.directory}', Space='${this.config.vectorSpace}'`);\n  }\n\n  /**\n     * Initializes the Chroma vector store.\n     * Creates the persistence directory if it doesn't exist and connects to the Chroma collection.\n     * This method must be called before any other operations and is idempotent.\n     * @throws {Error} If initialization fails.\n     */\n  public async initialize(): Promise<void> {\n    if (this.isInitialized || this.isInitializing) {\n      if (this.isInitializing) {\n        // Wait for the ongoing initialization to complete\n        await new Promise<void>(resolve => {\n          const checkInterval = setInterval(() => {\n            if (!this.isInitializing) {\n              clearInterval(checkInterval);\n              resolve();\n            }\n          }, 100);\n        });\n      }\n      if (this.isInitialized) {\n        logger.debug('ChromaVectorStore already initialized.');\n      }\n      return;\n    }\n\n    this.isInitializing = true;\n    logger.info(`Initializing Chroma vector store at ${this.config.directory} for collection ${this.config.collectionName}...`);\n\n    try {\n      // Ensure directory exists using async fs operations\n      await fs.promises.mkdir(this.config.directory, { recursive: true });\n      logger.debug(`Persistence directory ensured: ${this.config.directory}`);\n\n      // Initialize Chroma using Chroma.fromDocuments factory pattern.\n      // This pattern is common for creating/connecting to a persistent collection.\n      // Using it with an empty array ensures the collection exists or is created.\n      // The options object needs correct keys.\n      this.vectorStore = await Chroma.fromDocuments(\n        [], // Empty documents array to ensure collection exists/connects\n        this.embeddings,\n        {\n          collectionName: this.config.collectionName,\n          persistDirectory: this.config.directory, // Correct key for persistence path\n          collectionMetadata: { // Correct key for metadata\n            'hnsw:space': this.config.vectorSpace,\n          },\n          // url: undefined, // Explicitly undefined if using local path\n        }\n        // Cast to our expected interface after the await resolves\n      ) as unknown as IChromaPolyfillInstance;\n\n\n      if (!this.vectorStore) {\n        throw new Error('Failed to get a valid Chroma instance from the polyfill.');\n      }\n\n      // Optional: Verify connection if possible (e.g., try a simple operation like count)\n      // This depends heavily on the polyfill's capabilities.\n      // await this.vectorStore.count(); // Example, if count() exists\n\n      this.isInitialized = true;\n      logger.info(`Chroma vector store initialized successfully. Collection: ${this.config.collectionName}`);\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error('Failed to initialize Chroma vector store:', {\n        message: errorMessage,\n        stack: errorStack,\n        config: this.config\n      });\n      // Clear potentially partial state\n      this.vectorStore = undefined;\n      this.isInitialized = false;\n      throw new Error(`Chroma initialization failed: ${errorMessage}`);\n    } finally {\n      this.isInitializing = false;\n    }\n  }\n\n  /**\n     * Throws an error if the vector store is not initialized.\n     */\n  private assertInitialized(): void {\n    if (!this.isInitialized || !this.vectorStore) {\n      logger.error('ChromaVectorStore accessed before successful initialization.', {\n        initialized: this.isInitialized,\n        hasStore: !!this.vectorStore,\n      });\n      throw new Error('ChromaVectorStore is not initialized. Call initialize() first.');\n    }\n  }\n\n  /**\n     * Adds a document to the vector store.\n     * The vector embedding is generated internally by Chroma using the provided text content\n     * and the embeddings model configured during initialization. The `vector` parameter is ignored.\n     * @param id - A unique identifier for the vector/document.\n     * @param vector - The vector embedding (ignored in this implementation).\n     * @param metadata - Metadata associated with the vector, must include `content` field for embedding generation.\n     * @throws {Error} If the store is not initialized or if the addition fails.\n     */\n  public async addVector(id: string, vector: number[], metadata?: Record<string, string | number | boolean | undefined>): Promise<void> {\n    // Call assertInitialized correctly inside the method\n    this.assertInitialized();\n\n    if (!metadata) {\n      throw new Error(`Metadata is required for adding vector with ID ${id}`);\n    }\n\n    const pageContent = metadata.content;\n    if (typeof pageContent !== 'string' || pageContent.trim() === '') {\n      logger.warn(`Metadata must include a non-empty 'content' field for ID ${id}. Skipping addition.`);\n      throw new Error(`Missing or empty 'content' in metadata for adding vector with ID ${id}`);\n    }\n\n    // Create a document suitable for Chroma's addDocuments method\n    // Correct Document instantiation\n    const document = new Document({\n      pageContent: pageContent,\n      metadata: {\n        ...metadata,\n        // Use a consistent key, e.g., `doc_id` to avoid clashes\n        doc_id: id\n      }\n    });\n\n    try {\n      logger.debug(`Adding document with ID ${id} to Chroma collection ${this.config.collectionName}`);\n      // Correct call to addDocuments, passing an array and options object\n      if (!this.vectorStore) {\n        throw new Error('Vector store not initialized');\n      }\n      const addedIds = await this.vectorStore.addDocuments([document], { ids: [id] });\n\n      if (!addedIds || addedIds.length === 0 || !addedIds.includes(id)) {\n        logger.warn(`Chroma addDocuments may not have confirmed addition for ID ${id}. Response: ${JSON.stringify(addedIds)}`);\n        // Optionally throw error depending on strictness required\n        // throw new Error(`Failed to confirm addition of document with ID ${id}`);\n      } else {\n        logger.info(`Successfully added document with ID ${id} to Chroma.`);\n      }\n\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`Failed to add document with ID ${id} to Chroma:`, {\n        message: errorMessage,\n        stack: errorStack,\n        id: id,\n        // Avoid logging potentially large/sensitive metadata directly in production errors\n        metadataKeys: Object.keys(metadata)\n      });\n      throw new Error(`Failed to add vector for ID ${id}: ${errorMessage}`);\n    }\n  }\n\n  /**\n     * Retrieves a vector embedding by its ID.\n     * @param id - The unique identifier of the vector to retrieve.\n     * @returns The vector embedding as an array of numbers, or undefined if not found or on error.\n     * @throws {Error} If the store is not initialized.\n     */\n  public async getVector(id: string): Promise<number[] | undefined> {\n    // Call assertInitialized correctly inside the method\n    this.assertInitialized();\n\n    try {\n      logger.debug(`Attempting to retrieve vector with ID ${id} from Chroma collection ${this.config.collectionName}`);\n\n      // Correct call to 'get' method with proper options object\n      if (!this.vectorStore) {\n        throw new Error('Vector store not initialized');\n      }\n      const result: ChromaGetResponse = await this.vectorStore.get({\n        ids: [id],\n        include: ['embeddings'] // Request only embeddings\n      });\n\n      // Process the response correctly\n      if (result?.embeddings && result.embeddings.length > 0 && result.ids?.includes(id)) {\n        const index = result.ids.indexOf(id);\n        if (index !== -1 && result.embeddings[index]) {\n          logger.debug(`Successfully retrieved vector for ID ${id}.`);\n          return result.embeddings[index];\n        }\n      }\n\n      logger.debug(`Vector with ID ${id} not found in Chroma collection ${this.config.collectionName}.`);\n      return undefined;\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      // Handle cases where 'get' might not be implemented or fails\n      if (errorMessage?.includes('not implemented') || errorMessage?.includes('method not found')) {\n        logger.warn(`Chroma polyfill does not support the 'get' method for ID ${id}. Direct retrieval not possible.`);\n      } else {\n        logger.error(`Failed to get vector with ID ${id} from Chroma:`, {\n          message: errorMessage,\n          stack: errorStack,\n          id: id\n        });\n      }\n      // Regardless of error reason, return undefined as vector couldn't be retrieved\n      return undefined;\n    }\n  }\n\n  /**\n     * Deletes a vector by its ID.\n     * @param id - The unique identifier of the vector to delete.\n     * @returns `true` if deletion was successful or the vector didn't exist, `false` on error.\n     * @throws {Error} If the store is not initialized.\n     */\n  public async deleteVector(id: string): Promise<boolean> {\n    // Call assertInitialized correctly inside the method\n    this.assertInitialized();\n\n    try {\n      logger.debug(`Attempting to delete vector with ID ${id} from Chroma collection ${this.config.collectionName}`);\n      // Correct call to 'delete' method with proper options object\n      if (!this.vectorStore) {\n        throw new Error('Vector store not initialized');\n      }\n      const deletedIds = await this.vectorStore.delete({ ids: [id] });\n\n      // Chroma's delete often returns the IDs *attempted* to delete, not necessarily confirming existence.\n      // Success here means the API call didn't throw an error.\n      logger.info(`Delete request successful for vector ID ${id}. Attempted to delete: ${deletedIds ? JSON.stringify(deletedIds) : 'unknown'}`);\n      return true; // Return true as the operation completed without error\n\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`Failed to delete vector with ID ${id} from Chroma:`, {\n        message: errorMessage,\n        stack: errorStack,\n        id: id\n      });\n      return false;\n    }\n  }\n\n  /**\n     * Clears all vectors from the collection.\n     * Warning: This is a destructive operation.\n     * @throws {Error} If the store is not initialized or if clearing fails.\n     */\n  public async clearVectors(): Promise<void> {\n    // Call assertInitialized correctly inside the method\n    this.assertInitialized();\n    logger.warn(`Attempting to clear all vectors from Chroma collection ${this.config.collectionName}...`);\n\n    try {\n      // Try deleting all documents using a broad filter (e.g., where: {}).\n      // This depends on the polyfill supporting this feature in the 'delete' method.\n      try {\n        // Correct call to 'delete' with 'where' option\n        if (!this.vectorStore) {\n          throw new Error('Vector store not initialized');\n        }\n        await this.vectorStore.delete({ where: {} });\n        logger.info(`Successfully cleared all vectors from Chroma collection ${this.config.collectionName} using empty where clause.`);\n        return; // Exit if successful\n      } catch (e: unknown) {\n        const errorMessage = e instanceof Error ? e.message : String(e);\n        // If `where: {}` is not supported or fails, log it and try the fallback.\n        logger.warn(`Clearing using empty 'where' clause failed or is not supported by polyfill: ${errorMessage}. Attempting fallback...`);\n      }\n\n      // Fallback: Get all document IDs and delete them (less efficient)\n      logger.debug('Fallback: Retrieving all document IDs for clearing...');\n      // Correct call to 'get' to retrieve all IDs (potentially limited by Chroma server settings)\n      if (!this.vectorStore) {\n        throw new Error('Vector store not initialized');\n      }\n      const allDocsResponse = await this.vectorStore.get({\n        // No filter needed, get all\n        include: [], // Only need IDs\n        // limit: large_number // Optional: if needed and supported\n      });\n\n      if (allDocsResponse?.ids && allDocsResponse.ids.length > 0) {\n        const allIds = allDocsResponse.ids;\n        logger.debug(`Retrieved ${allIds.length} IDs for clearing. Deleting in batches...`);\n\n        // Delete IDs in batches to avoid potential request size limits\n        const batchSize = 100; // Adjust batch size as needed\n        for (let i = 0; i < allIds.length; i += batchSize) {\n          const batchIds = allIds.slice(i, i + batchSize);\n          // Correct call to 'delete' with 'ids' option\n          await this.vectorStore.delete({ ids: batchIds });\n          logger.debug(`Deleted batch ${i / batchSize + 1} of IDs.`);\n        }\n        logger.info(`Successfully cleared all ${allIds.length} vectors from Chroma collection ${this.config.collectionName} using ID list fallback.`);\n      } else {\n        logger.info(`Chroma collection ${this.config.collectionName} is already empty or no IDs were retrieved.`);\n      }\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error(`Failed to clear Chroma vector store collection ${this.config.collectionName}:`, {\n        message: errorMessage,\n        stack: errorStack,\n      });\n      throw new Error(`Failed to clear vectors: ${errorMessage}`);\n    }\n  }\n\n  /**\n     * Searches for vectors similar to the provided query vector.\n     * Uses `similaritySearchByVectorWithScore` if available, otherwise falls back to `similaritySearchWithScore`.\n     * @param vector - The query vector embedding.\n     * @param limit - The maximum number of similar vectors to return.\n     * @param filter - Optional metadata filter to apply during the search (key-value pairs, syntax depends on Chroma implementation).\n     * @returns A promise resolving to an array of objects, each containing the ID and similarity score.\n     * @throws {Error} If the store is not initialized or if the search fails.\n     */\n  public async searchSimilarVectors(vector: number[], limit = 5, filter?: Record<string, string | number | boolean | undefined>): Promise<{ id: string; score: number }[]> {\n    // Call assertInitialized correctly inside the method\n    this.assertInitialized();\n\n    if (!Array.isArray(vector) || vector.length === 0) {\n      logger.error('Invalid query vector provided for similarity search.');\n      throw new Error('Query vector must be a non-empty array of numbers.');\n    }\n\n    try {\n      logger.debug(`Searching for ${limit} similar vectors in Chroma collection ${this.config.collectionName}`, { filter: filter ? 'present' : 'absent' });\n\n      let resultsWithScores: [Document, number][] = [];\n\n      // Prefer direct vector search if available in the polyfill\n      // Prefer direct vector search if available in the polyfill\n      if (!this.vectorStore) {\n        throw new Error('Vector store not initialized');\n      }\n\n      if (this.vectorStore.similaritySearchByVectorWithScore) {\n        logger.debug('Using similaritySearchByVectorWithScore...');\n        resultsWithScores = await this.vectorStore.similaritySearchByVectorWithScore(\n          vector,\n          limit,\n          filter\n        );\n      } else { // Fallback to text-based search if vector search is not available\n        logger.warn('similaritySearchByVectorWithScore not available. Falling back to similaritySearchWithScore with a dummy query. Accuracy may vary.');\n        // Use text-based search with a dummy query string.\n        // A dummy query string is used as similaritySearchWithScore expects a text query.\n        resultsWithScores = await this.vectorStore.similaritySearchWithScore(\n          'dummy_query_for_vector_fallback', // Dummy query string\n          limit,\n          filter\n        );\n      }\n      // If neither method is available, the code will proceed to the check below and throw an error.\n      if (!this.vectorStore.similaritySearchByVectorWithScore && !this.vectorStore.similaritySearchWithScore) {\n        throw new Error('Chroma polyfill instance does not support required similarity search methods (similaritySearchByVectorWithScore or similaritySearchWithScore).');\n      }\n\n\n      if (!resultsWithScores) {\n        logger.warn('Similarity search returned null or undefined results.');\n        return [];\n      }\n\n      // Map results to the required format: { id: string, score: number }\n      const mappedResults = resultsWithScores.map(([doc, score]) => {\n        // Attempt to extract the original ID stored in metadata under 'doc_id'\n        const id = doc.metadata?.doc_id as string | undefined;\n        if (!id) {\n          logger.warn('Found similar document without \\'doc_id\\' in metadata:', doc.metadata);\n          return null; // Skip results without our designated ID field\n        }\n\n        // Score interpretation: LangChain's similaritySearchWithScore typically returns\n        // a similarity score (higher is better), often cosine similarity [0, 1].\n        // If it returns distance, conversion is needed (as done previously).\n        // We assume the returned 'score' is already a similarity score.\n        const similarityScore = typeof score === 'number' ? score : 0; // Default to 0 if score is invalid\n\n        return { id: String(id), score: similarityScore };\n      }).filter(result => result !== null) as { id: string; score: number }[]; // Filter out nulls\n\n      logger.info(`Found ${mappedResults.length} similar vector(s) matching criteria.`);\n      return mappedResults;\n\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      logger.error('Failed to search for similar vectors in Chroma:', {\n        message: errorMessage,\n        stack: errorStack,\n        limit: limit,\n        filterKeys: filter ? Object.keys(filter) : [], // Avoid logging sensitive filter values\n      });\n      // Return empty array on error\n      return [];\n    }\n  }\n}"]}