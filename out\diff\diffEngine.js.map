{"version": 3, "file": "diffEngine.js", "sourceRoot": "", "sources": ["../../src/diff/diffEngine.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA2C;AAC3C,2CAA6B;AAC7B,sCAAmC;AACnC,+CAAiC;AAMjC,MAAM,UAAU;IACd;;;;;;;;SAQK;IACL,WAAW,CAAC,WAAmB,EAAE,WAAmB,EAAE,MAAc,EAAE,MAAc,EAAE,OAA4B;QAChH,eAAM,CAAC,KAAK,CAAC,2BAA2B,WAAW,UAAU,WAAW,GAAG,CAAC,CAAC;QAC7E,IAAI,CAAC;YACH,6EAA6E;YAC7E,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAClD,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAClD,2CAA2C;YAC3C,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC;YACjC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAChH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,WAAW,IAAI,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC,CAAC,mCAAmC;QAClD,CAAC;IACH,CAAC;IAED;;;;;;;SAOK;IACL,UAAU,CAAC,KAAa,EAAE,MAAc,EAAE,OAA2B;QACnE,eAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAC7C,IAAI,CAAC;YACH,8DAA8D;YAC9D,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAClD,yEAAyE;YACzE,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAE5D,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gBACrB,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACnD,4DAA4D;gBAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACtC,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAC9C,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,2DAA2D;YAC3D,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,4CAA4C;gBAC5C,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,6BAA6B,KAAK,CAAC,OAAO,sEAAsE,CAAC,CAAC;YACrJ,CAAC;YACD,OAAO,KAAK,CAAC,CAAC,mBAAmB;QACnC,CAAC;IACH,CAAC;IAED;;;;SAIK;IACL,UAAU,CAAC,KAAa;QACtB,eAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACtC,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;SAOK;IACL,oBAAoB,CAAC,OAAiB,EAAE,UAAkB,EAAE,OAA2B;QACrF,eAAM,CAAC,KAAK,CAAC,YAAY,OAAO,CAAC,MAAM,wBAAwB,CAAC,CAAC;QACjE,IAAI,cAAc,GAAmB,UAAU,CAAC;QAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACzB,wDAAwD;YACxD,IAAI,OAAO,cAAc,KAAK,SAAS,EAAE,CAAC;gBACxC,eAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBACpE,OAAO,KAAK,CAAC,CAAC,kCAAkC;YAClD,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC3C,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAEjE,IAAI,cAAc,KAAK,KAAK,EAAE,CAAC;gBAC7B,eAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAChD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,wBAAwB,OAAO,CAAC,MAAM,WAAW,CAAC,CAAC;QAC/D,OAAO,cAAc,CAAC;IACxB,CAAC;CACF;AAEY,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC", "sourcesContent": ["/// <reference path=\"../types/diff.d.ts\" />\nimport * as diff from 'diff';\nimport { logger } from '../logger';\nimport * as vscode from 'vscode';\n\nexport type DiffHunk = diff.Hunk\nexport type ApplyPatchOptions = diff.ApplyPatchOptions\nexport type CreatePatchOptions = diff.CreatePatchOptions\n\nclass DiffEngine {\n  /**\n     * Creates a unified diff patch string between two text contents.\n     * @param oldFileName Name/path of the old file (for patch header).\n     * @param newFileName Name/path of the new file (for patch header).\n     * @param oldStr Content of the old file.\n     * @param newStr Content of the new file.\n     * @param options Optional diff options.\n     * @returns The unified diff patch string.\n     */\n  createPatch(oldFileName: string, newFileName: string, oldStr: string, newStr: string, options?: CreatePatchOptions): string {\n    logger.debug(`Creating patch between \"${oldFileName}\" and \"${newFileName}\"`);\n    try {\n      // Ensure consistent line endings (Unix-style) before diffing for reliability\n      const cleanOldStr = oldStr.replace(/\\r\\n/g, '\\n');\n      const cleanNewStr = newStr.replace(/\\r\\n/g, '\\n');\n      // Extract context from options if provided\n      const context = options?.context;\n      return diff.createPatch(oldFileName, cleanOldStr, cleanNewStr, '', '', context ? String(context) : undefined);\n    } catch (error) {\n      logger.error(`Error creating patch for \"${oldFileName}\":`, error);\n      throw error; // Re-throw to be handled by caller\n    }\n  }\n\n  /**\n     * Applies a unified diff patch to a string.\n     * IMPORTANT: This is sensitive to the exact content the patch was created against.\n     * @param patch The unified diff patch string.\n     * @param oldStr The original string the patch was created against.\n     * @param options Optional patch application options (e.g., fuzz factor).\n     * @returns The patched string, or false if the patch cannot be applied cleanly.\n     */\n  applyPatch(patch: string, oldStr: string, options?: ApplyPatchOptions): string | false {\n    logger.debug('Attempting to apply patch...');\n    try {\n      // Ensure consistent line endings (Unix-style) before patching\n      const cleanOldStr = oldStr.replace(/\\r\\n/g, '\\n');\n      // The patch itself should ideally already use \\n, but we parse it anyway\n      const result = diff.applyPatch(cleanOldStr, patch, options);\n\n      if (result === false) {\n        logger.warn('Patch could not be applied cleanly.');\n        // Optionally try parsing the patch to see which hunk failed\n        const parsed = diff.parsePatch(patch);\n        logger.debug('Parsed patch hunks:', parsed);\n      } else {\n        logger.debug('Patch applied successfully.');\n      }\n      return result;\n    } catch (error: any) {\n      // `diff.applyPatch` can throw errors for malformed patches\n      logger.error('Error applying patch:', error);\n      if (error.message?.includes('hunk')) {\n        // More specific error from the diff library\n        vscode.window.showWarningMessage(`Patch application failed: ${error.message}. The file content might have changed since the patch was generated.`);\n      }\n      return false; // Indicate failure\n    }\n  }\n\n  /**\n     * Parses a patch string into its component hunks.\n     * @param patch The unified diff patch string.\n     * @returns An array of parsed patch objects, or throws on error.\n     */\n  parsePatch(patch: string): diff.ParsedDiff[] {\n    logger.debug('Parsing patch string.');\n    try {\n      return diff.parsePatch(patch);\n    } catch (error) {\n      logger.error('Error parsing patch string:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Applies multiple patches sequentially to a string.\n     * Stops and returns the intermediate result if any patch fails.\n     * @param patches Array of patch strings.\n     * @param initialStr The starting string content.\n     * @param options Optional patch application options.\n     * @returns The final string content after applying all successful patches, or false if any patch failed.\n     */\n  applyMultiplePatches(patches: string[], initialStr: string, options?: ApplyPatchOptions): string | false {\n    logger.debug(`Applying ${patches.length} patches sequentially.`);\n    let currentContent: string | false = initialStr;\n\n    for (let i = 0; i < patches.length; i++) {\n      const patch = patches[i];\n      // Use typeof check to avoid TypeScript comparison error\n      if (typeof currentContent === 'boolean') {\n        logger.error(`Cannot apply patch ${i + 1}, previous patch failed.`);\n        return false; // Stop if a previous patch failed\n      }\n      logger.debug(`Applying patch ${i + 1}...`);\n      currentContent = this.applyPatch(patch, currentContent, options);\n\n      if (currentContent === false) {\n        logger.error(`Failed to apply patch ${i + 1}.`);\n        return false;\n      }\n    }\n\n    logger.info(`Successfully applied ${patches.length} patches.`);\n    return currentContent;\n  }\n}\n\nexport const diffEngine = new DiffEngine();\n"]}