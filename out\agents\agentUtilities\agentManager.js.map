{"version": 3, "file": "agentManager.js", "sourceRoot": "", "sources": ["../../../src/agents/agentUtilities/agentManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,mCAAgC;AAChC,yCAAsC;AAiCtC,MAAa,YAAY;IACf,MAAM,CAAC,SAAS,CAAe;IAC/B,OAAO,GAAuB,IAAI,GAAG,EAAE,CAAC;IACxC,gBAAgB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAoB,CAAC;IAC/D,cAAc,CAAS;IACvB,gBAAgB,CAAS;IAEjC;;SAEK;IACW,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;IAE9D;QACE,gCAAgC;QAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,aAAK,CAAC;YACtB,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,eAAe;YACrB,gBAAgB,EAAE,SAAS;YAC3B,IAAI,EAAE,WAAW;YACjB,YAAY,EAAE,CAAC,MAAM,CAAC;YACtB,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAC5B,YAAY,CAAC,SAAS,GAAG,IAAI,YAAY,EAAE,CAAC;QAC9C,CAAC;QACD,OAAO,YAAY,CAAC,SAAS,CAAC;IAChC,CAAC;IAED;;SAEK;IACE,YAAY;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;SAEK;IACE,WAAW;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;SAEK;IACE,QAAQ,CAAC,EAAU;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED;;SAEK;IACE,eAAe;QACpB,+CAA+C;QAC/C,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE/C,4EAA4E;QAC5E,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CACnD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAChD,CAAC;QACJ,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC3C,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;SAEK;IACE,QAAQ,CAAC,KAAY;QAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAClC,eAAM,CAAC,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QAE/D,6DAA6D;QAC7D,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC9B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC9B,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YACvC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;SAEK;IACE,WAAW,CAAC,OAAoB;QACrC,MAAM,KAAK,GAAG,IAAI,aAAK,CAAC;YACtB,GAAG,OAAO;YACV,IAAI,EAAE,WAAW;YACjB,YAAY,EAAE,CAAC,MAAM,CAAC;YACtB,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACE,WAAW,CAAC,EAAU,EAAE,YAAmB;QAChD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;YACnC,eAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;YAC5D,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACE,WAAW,CAAC,EAAU;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACvC,IAAI,MAAM,EAAE,CAAC;YACX,eAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;YAE5D,qDAAqD;YACrD,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;gBACjD,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAClC,CAAC;YACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnD,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YACpC,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,gBAAgB,CAAC,KAAY;QAClC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,eAAM,CAAC,IAAI,CAAC,uBAAuB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACnD,CAAC;IAEM,kBAAkB;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEM,kBAAkB,CAAC,KAAY;QACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,eAAM,CAAC,IAAI,CAAC,yBAAyB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACxC,KAAK,KAAK,IAAI,CAAC,cAAc;YAC7B,KAAK,KAAK,IAAI,CAAC,gBAAgB,CAChC,CAAC;IACJ,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAChC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;CACF;AA9KD,oCA8KC;AAED,8BAA8B;AACjB,QAAA,YAAY,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Agent } from './agent';\nimport { logger } from '../../logger';\nimport { AgentRole } from '../../types/agent';\nimport { LLMConfig } from '../../llm/types';\n\n\n/**\n * Event emitted when agents are changed\n */\nexport interface AgentChangeEvent {\n  type: 'add' | 'update' | 'delete';\n  agentId: string;\n}\n\n/**\n * Manages agents in the system\n */\n\nexport interface AgentConfig {\n    id: string;\n    name: string;\n    description?: string;\n    role: AgentRole;\n    capabilities: string[];\n    llmProvider: string;\n    llmModel: string;\n    temperature?: number;\n    maxTokens?: number;\n    systemPromptName: string;\n    llm?: LLMConfig;\n    tools?: string[];\n    isSupervisor?: boolean;\n}\n\nexport class AgentManager {\n  private static _instance: AgentManager;\n  private _agents: Map<string, Agent> = new Map();\n  private _onAgentsChanged = new vscode.EventEmitter<AgentChangeEvent>();\n  private _receiverAgent?: Agent;\n  private _supervisorAgent?: Agent;\n\n  /**\n     * Event that fires when agents are changed\n     */\n  public readonly onAgentsChanged = this._onAgentsChanged.event;\n\n  private constructor() {\n    // Initialize with default agent\n    this.addAgent(new Agent({\n      id: 'default',\n      name: 'Default Agent',\n      systemPromptName: 'default',\n      role: 'assistant',\n      capabilities: ['chat'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4'\n    }));\n  }\n\n  /**\n     * Get the singleton instance of AgentManager\n     */\n  public static getInstance(): AgentManager {\n    if (!AgentManager._instance) {\n      AgentManager._instance = new AgentManager();\n    }\n    return AgentManager._instance;\n  }\n\n  /**\n     * Get all agents\n     */\n  public getAllAgents(): Agent[] {\n    return Array.from(this._agents.values());\n  }\n\n  /**\n     * Get all agent IDs\n     */\n  public getAgentIds(): string[] {\n    return Array.from(this._agents.keys());\n  }\n\n  /**\n     * Get an agent by ID\n     */\n  public getAgent(id: string): Agent | undefined {\n    return this._agents.get(id);\n  }\n\n  /**\n     * Get the default agent\n     */\n  public getDefaultAgent(): Agent | undefined {\n    // First try to find an agent with ID 'default'\n    let defaultAgent = this._agents.get('default');\n\n    // If not found, try to find an agent with name 'default' (case insensitive)\n    if (!defaultAgent) {\n      defaultAgent = Array.from(this._agents.values()).find(\n        agent => agent.name.toLowerCase() === 'default'\n      );\n    }\n\n    // If still not found, return the first agent\n    if (!defaultAgent && this._agents.size > 0) {\n      defaultAgent = Array.from(this._agents.values())[0];\n    }\n\n    return defaultAgent;\n  }\n\n  /**\n     * Add a new agent\n     */\n  public addAgent(agent: Agent): void {\n    this._agents.set(agent.id, agent);\n    logger.info(`Added agent ${agent.id}`);\n    this._onAgentsChanged.fire({ type: 'add', agentId: agent.id });\n\n    // Set receiver and supervisor agents if they match the roles\n    if (agent.role === 'receiver') {\n      this._receiverAgent = agent;\n    } else if (agent.role === 'supervisor') {\n      this._supervisorAgent = agent;\n    }\n  }\n\n  /**\n     * Create a new agent from config\n     */\n  public createAgent(_config: AgentConfig): Agent {\n    const agent = new Agent({\n      ..._config,\n      role: 'assistant',\n      capabilities: ['chat'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4'\n    });\n    this.addAgent(agent);\n    return agent;\n  }\n\n  /**\n     * Update an existing agent\n     */\n  public updateAgent(id: string, updatedAgent: Agent): boolean {\n    if (this._agents.has(id)) {\n      this._agents.set(id, updatedAgent);\n      logger.info(`Updated agent ${id}`);\n      this._onAgentsChanged.fire({ type: 'update', agentId: id });\n      return true;\n    }\n    return false;\n  }\n\n  /**\n     * Delete an agent by ID\n     */\n  public deleteAgent(id: string): boolean {\n    const result = this._agents.delete(id);\n    if (result) {\n      logger.info(`Deleted agent ${id}`);\n      this._onAgentsChanged.fire({ type: 'delete', agentId: id });\n\n      // Clear receiver or supervisor if this agent was one\n      if (this._receiverAgent === this._agents.get(id)) {\n        this._receiverAgent = undefined;\n      }\n      if (this._supervisorAgent === this._agents.get(id)) {\n        this._supervisorAgent = undefined;\n      }\n    }\n    return result;\n  }\n\n  public getReceiverAgent(): Agent | undefined {\n    return this._receiverAgent;\n  }\n\n  public setReceiverAgent(agent: Agent): void {\n    this._receiverAgent = agent;\n    this.addAgent(agent);\n    logger.info(`Receiver agent set: ${agent.name}`);\n  }\n\n  public getSupervisorAgent(): Agent | undefined {\n    return this._supervisorAgent;\n  }\n\n  public setSupervisorAgent(agent: Agent): void {\n    this._supervisorAgent = agent;\n    this.addAgent(agent);\n    logger.info(`Supervisor agent set: ${agent.name}`);\n  }\n\n  public getAdditionalAgents(): Agent[] {\n    return this.getAllAgents().filter(agent =>\n      agent !== this._receiverAgent &&\n      agent !== this._supervisorAgent\n    );\n  }\n\n  public dispose(): void {\n    this._agents.clear();\n    this._receiverAgent = undefined;\n    this._supervisorAgent = undefined;\n  }\n}\n\n// Export a singleton instance\nexport const agentManager = AgentManager.getInstance();\n"]}