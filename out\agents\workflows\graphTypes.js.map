{"version": 3, "file": "graphTypes.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/graphTypes.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Agent } from '../agentUtilities/agent';\nimport { ITool, StructuredTool } from '../../tools/tool.ts.backup';\nimport type { OperationMode, Methodology } from './types';\n\nexport type NodeType = 'input' | 'output' | 'agent' | 'tool' | 'decision' | 'process';\nexport type EdgeType =\n    // Core flow types\n    | 'success'        // Successful execution path\n    | 'failure'        // Failure/error path\n    | 'default'        // Default pathway\n\n    // Structural relationships\n    | 'composition'    // Whole-part relationship\n    | 'dependency'     // Dependency relationship\n    | 'association'    // General association\n\n    // Special workflow types\n    | 'feedback'       // Feedback loop\n    | 'validation'     // Validation pathway\n    | 'optimization'   // Optimization path\n    | 'integration'    // Integration point\n\n    // Control flow\n    | 'conditional'    // Conditional branching\n    | 'parallel'       // Parallel execution\n    | 'synchronization' // Synchronization point\n\n    // Operation modes\n    | 'memory'         // Memory-enhanced operations\n    | 'document-qa'    // Document Q&A\n    | 'refactor'       // Code refactoring\n    | 'debug'          // Code debugging\n    | 'chat'           // Conversational chat\n    | 'ask'            // Question answering with context\n    | 'edit'           // Code editing\n    | 'codegen'        // Code generation\n    | 'multi-agent'    // Multi-agent collaboration\n\n    // Methodologies\n    | 'agile'          // Agile methodology\n    | 'xp'             // Extreme Programming\n    | 'scrum'          // Scrum framework\n\n    // New workflow types\n    | 'pr-creation'        // PR creation workflow\n    | 'pr-review'          // PR review workflow\n    | 'checkpoint'         // Checkpoint system\n    | 'mcp'                // Model Context Protocol\n    | 'pattern-refactoring' // Pattern-based refactoring\n    | 'technical-debt';    // Technical debt reduction\n\n\nexport interface GraphNode {\n    id: string;\n    label: string;\n    type: NodeType;\n    description?: string;\n    agent?: Agent;\n    tool?: ITool;\n    data?: any;\n}\n\nexport interface GraphEdge {\n    name: string;\n    source: string;\n    target: string;\n    type: EdgeType;\n    condition?: (_state: any) => boolean | Promise<boolean>;\n    data?: any;\n}\n\nexport interface GraphDefinition {\n    id: string;\n    name: string;\n    description: string;\n    version: string;\n    nodes: GraphNode[];\n    edges: GraphEdge[];\n    startNodeId: string;\n    operationMode: OperationMode;\n    methodology?: Methodology;\n    tags?: string[];\n    agentNodes?: GraphNode[];\n    toolNodes?: GraphNode[];\n}"]}