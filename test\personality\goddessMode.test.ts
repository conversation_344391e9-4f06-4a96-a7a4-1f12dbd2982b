import { describe, it, expect, beforeEach } from '@jest/globals';
import { GoddessPersonalityEngine, Dev<PERSON>perMood, GoddessResponse } from '../../src/personality/goddessMode';

describe('GoddessPersonalityEngine - Advanced Psychological Analysis', () => {
  let engine: GoddessPersonalityEngine;

  beforeEach(() => {
    engine = new GoddessPersonalityEngine();
  });

  describe('Enhanced Mood Analysis', () => {
    it('should detect high stress with complex cognitive load', () => {
      const message = "This urgent deadline is breaking me, everything is crashing and I'm drowning in errors";
      const context = {
        timeOfDay: 23, // Late night
        recentErrors: 8,
        sessionDuration: 6 * 60 * 60 * 1000, // 6 hours
        codeComplexity: 85
      };

      const mood = engine.analyzeDeveloperMood(message, context);

      expect(mood.stress).toBeGreaterThan(70);
      expect(mood.frustration).toBeGreaterThan(60);
      expect(mood.energy).toBeLessThan(40);
      expect(mood.burnout).toBeGreaterThan(30);
    });

    it('should detect imposter syndrome patterns', () => {
      const message = "I'm such a fake developer, everyone else seems to know what they're doing but I'm just pretending";
      const context = { timeOfDay: 14, recentErrors: 3, sessionDuration: 2 * 60 * 60 * 1000 };

      const mood = engine.analyzeDeveloperMood(message, context);

      expect(mood.imposterSyndrome).toBeGreaterThan(50);
      expect(mood.confidence).toBeLessThan(40);
      expect(mood.socialConnection).toBeLessThan(50);
    });

    it('should detect flow state indicators', () => {
      const message = "I was completely in the zone, hours flew by and everything just clicked naturally";
      const context = { timeOfDay: 10, recentErrors: 0, sessionDuration: 3 * 60 * 60 * 1000 };

      const mood = engine.analyzeDeveloperMood(message, context);

      expect(mood.flowState).toBeGreaterThan(60);
      expect(mood.focus).toBeGreaterThan(70);
      expect(mood.energy).toBeGreaterThan(60);
    });

    it('should detect burnout warning signs', () => {
      const message = "I'm completely burnt out, everything feels pointless and I just can't anymore";
      const context = { 
        timeOfDay: 22, 
        recentErrors: 12, 
        sessionDuration: 8 * 60 * 60 * 1000,
        codeComplexity: 90
      };

      const mood = engine.analyzeDeveloperMood(message, context);

      expect(mood.burnout).toBeGreaterThan(60);
      expect(mood.energy).toBeLessThan(30);
      expect(mood.socialConnection).toBeLessThan(40);
    });
  });

  describe('Contextual Response Generation', () => {
    it('should provide supportive chunking for high stress + high complexity', () => {
      const message = "This deadline is killing me, everything is broken";
      const mood: DeveloperMood = {
        stress: 85, confidence: 30, energy: 25, focus: 40, frustration: 80, excitement: 10,
        burnout: 60, imposterSyndrome: 40, flowState: 20, socialConnection: 30, autonomy: 40, mastery: 35
      };

      const response = engine.generateGoddessResponse(message, mood, { codeComplexity: 90 });

      expect(response.tone).toBe('supportive');
      expect(response.motivationalElement).toContain('2–3');
      expect(response.motivationalElement).toContain('step');
    });

    it('should detect competence need and provide structured debugging advice', () => {
      const message = "I'm completely confused and stuck, don't know what's wrong";
      const mood: DeveloperMood = {
        stress: 60, confidence: 25, energy: 50, focus: 45, frustration: 70, excitement: 20,
        burnout: 30, imposterSyndrome: 60, flowState: 25, socialConnection: 40, autonomy: 50, mastery: 30
      };

      const response = engine.generateGoddessResponse(message, mood, { codeComplexity: 60 });

      expect(response.tone).toBe('encouraging');
      expect(response.wisdomSharing).toContain('reproduce');
      expect(response.wisdomSharing).toContain('isolate');
      expect(response.wisdomSharing).toContain('fix');
      expect(response.wisdomSharing).toContain('verify');
    });

    it('should offer choices when autonomy is threatened', () => {
      const message = "I have to use this framework, no choice in the matter, policy requires it";
      const mood: DeveloperMood = {
        stress: 50, confidence: 60, energy: 65, focus: 70, frustration: 45, excitement: 40,
        burnout: 25, imposterSyndrome: 30, flowState: 50, socialConnection: 60, autonomy: 25, mastery: 55
      };

      const response = engine.generateGoddessResponse(message, mood, { codeComplexity: 50 });

      expect(response.motivationalElement).toContain('quick fix');
      expect(response.motivationalElement).toContain('refactor');
      expect(response.motivationalElement).toContain('step-by-step');
    });

    it('should provide challenging tone for excited + low cognitive load', () => {
      const message = "I'm so excited about this new feature, it's going to be awesome!";
      const mood: DeveloperMood = {
        stress: 20, confidence: 85, energy: 90, focus: 80, frustration: 10, excitement: 85,
        burnout: 15, imposterSyndrome: 20, flowState: 75, socialConnection: 70, autonomy: 80, mastery: 75
      };

      const response = engine.generateGoddessResponse(message, mood, { codeComplexity: 30 });

      expect(response.tone).toBe('challenging');
      expect(response.wisdomSharing).toContain('commit');
    });

    it('should use we-language for relatedness support', () => {
      const message = "I'm working alone on this, no one to help me figure it out";
      const mood: DeveloperMood = {
        stress: 55, confidence: 45, energy: 50, focus: 60, frustration: 50, excitement: 30,
        burnout: 35, imposterSyndrome: 40, flowState: 40, socialConnection: 25, autonomy: 60, mastery: 50
      };

      const response = engine.generateGoddessResponse(message, mood, { codeComplexity: 60 });

      expect(response.motivationalElement).toContain('together');
    });
  });

  describe('Imposter Syndrome Support', () => {
    it('should provide specialized imposter syndrome responses', () => {
      const message = "I feel like such an imposter, I don't belong here with real developers";
      const mood: DeveloperMood = {
        stress: 60, confidence: 20, energy: 40, focus: 45, frustration: 55, excitement: 25,
        burnout: 40, imposterSyndrome: 85, flowState: 30, socialConnection: 25, autonomy: 45, mastery: 35
      };

      const response = engine.generateGoddessResponse(message, mood, { codeComplexity: 50 });

      expect(response.tone).toBe('supportive');
      // Should contain imposter-specific messaging
      expect(response.message).toMatch(/(imposter|belong|expert|journey)/i);
    });
  });

  describe('Circadian and Context Awareness', () => {
    it('should adjust for late night coding sessions', () => {
      const message = "Working on this bug fix";
      const context = {
        timeOfDay: 2, // 2 AM
        sessionDuration: 5 * 60 * 60 * 1000, // 5 hours
        recentErrors: 3
      };

      const mood = engine.analyzeDeveloperMood(message, context);

      expect(mood.energy).toBeLessThan(50);
      expect(mood.stress).toBeGreaterThan(40);
      expect(mood.burnout).toBeGreaterThan(30);
    });

    it('should recognize optimal performance windows', () => {
      const message = "Working on this feature";
      const context = {
        timeOfDay: 10, // 10 AM - peak performance window
        sessionDuration: 2 * 60 * 60 * 1000, // 2 hours - optimal
        recentErrors: 1
      };

      const mood = engine.analyzeDeveloperMood(message, context);

      expect(mood.focus).toBeGreaterThan(70);
      expect(mood.flowState).toBeGreaterThan(50);
    });
  });

  describe('Learning and Adaptation', () => {
    it('should learn from effective interactions', () => {
      const initialPersonality = engine.getPersonality();
      
      engine.recordInteractionFeedback(
        'test-interaction-1',
        90, // High effectiveness
        'This really helped!',
        {
          stress: 80, confidence: 30, energy: 40, focus: 50, frustration: 70, excitement: 20,
          burnout: 50, imposterSyndrome: 40, flowState: 30, socialConnection: 40, autonomy: 50, mastery: 40
        },
        { timeOfDay: 14, sessionDuration: 3 * 60 * 60 * 1000 }
      );

      const updatedPersonality = engine.getPersonality();
      expect(updatedPersonality.adaptiveLevel).toBeGreaterThanOrEqual(initialPersonality.adaptiveLevel);
    });
  });
});
