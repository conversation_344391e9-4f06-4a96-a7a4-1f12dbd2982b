{"_type": "UMLClass", "_id": "AAAAAAGH1IDatabase=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "IDatabase", "stereotype": "interface", "visibility": "public", "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1IDatabaseOp1=", "_parent": {"$ref": "AAAAAAGH1IDatabase="}, "name": "initialize", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp1P1=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp1="}, "type": "Promise<void>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IDatabaseOp2=", "_parent": {"$ref": "AAAAAAGH1IDatabase="}, "name": "addRecord", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp2P1=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp2="}, "name": "collection", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp2P2=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp2="}, "name": "record", "type": "Record<string, any>"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp2P3=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp2="}, "type": "Promise<string>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IDatabaseOp3=", "_parent": {"$ref": "AAAAAAGH1IDatabase="}, "name": "getRecord", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp3P1=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp3="}, "name": "collection", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp3P2=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp3="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp3P3=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp3="}, "type": "Promise<Record<string, any> | undefined>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IDatabaseOp4=", "_parent": {"$ref": "AAAAAAGH1IDatabase="}, "name": "updateRecord", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp4P1=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp4="}, "name": "collection", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp4P2=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp4="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp4P3=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp4="}, "name": "record", "type": "Record<string, any>"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp4P4=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp4="}, "type": "Promise<boolean>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IDatabaseOp5=", "_parent": {"$ref": "AAAAAAGH1IDatabase="}, "name": "deleteRecord", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp5P1=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp5="}, "name": "collection", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp5P2=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp5="}, "name": "id", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp5P3=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp5="}, "type": "Promise<boolean>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IDatabaseOp6=", "_parent": {"$ref": "AAAAAAGH1IDatabase="}, "name": "queryRecords", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp6P1=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp6="}, "name": "collection", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp6P2=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp6="}, "name": "query", "type": "Record<string, any>"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp6P3=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp6="}, "name": "limit", "type": "number", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp6P4=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp6="}, "name": "sort", "type": "Record<string, 1 | -1>", "defaultValue": "undefined"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp6P5=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp6="}, "type": "Promise<Record<string, any>[]>", "direction": "return"}]}, {"_type": "UMLOperation", "_id": "AAAAAAGH1IDatabaseOp7=", "_parent": {"$ref": "AAAAAAGH1IDatabase="}, "name": "clearCollection", "visibility": "public", "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp7P1=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp7="}, "name": "collection", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1IDatabaseOp7P2=", "_parent": {"$ref": "AAAAAAGH1IDatabaseOp7="}, "type": "Promise<void>", "direction": "return"}]}], "isAbstract": true}