"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.directoryListTool = exports.DirectoryListTool = void 0;
const vscode = __importStar(require("vscode"));
const zod_1 = require("zod");
class DirectoryListTool {
    id = 'listDir';
    name = 'List Directory';
    description = 'Lists the contents of a directory (files and subdirectories).';
    type = 'single-action';
    schema = zod_1.z.object({
        dirPath: zod_1.z.string().describe('Path to the directory (relative to workspace root or absolute).')
    });
    actions = {};
    async execute(input, _context) {
        const dirPath = input.dirPath;
        if (!dirPath) {
            return { success: false, error: '\'dirPath\' is required.', toolId: this.id };
        }
        let dirUri;
        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
            const workspaceRoot = vscode.workspace.workspaceFolders[0].uri;
            try {
                const uri = vscode.Uri.parse(dirPath);
                if (uri.scheme)
                    dirUri = uri;
                else
                    dirUri = vscode.Uri.joinPath(workspaceRoot, dirPath);
            }
            catch {
                dirUri = vscode.Uri.joinPath(workspaceRoot, dirPath);
            }
        }
        else {
            try {
                const uri = vscode.Uri.parse(dirPath);
                if (uri.scheme)
                    dirUri = uri;
            }
            catch {
                // Handle empty catch block by doing nothing
            }
        }
        if (!dirUri) {
            return { success: false, error: `Could not resolve directory path: ${dirPath}.`, toolId: this.id };
        }
        try {
            const entries = await vscode.workspace.fs.readDirectory(dirUri);
            const result = entries.map(([name, type]) => ({
                name,
                type: type === vscode.FileType.Directory ? 'directory' : 'file'
            }));
            return { success: true, output: result, toolId: this.id };
        }
        catch (error) {
            return { success: false, error: `Failed to list directory: ${error.message || error}`, toolId: this.id };
        }
    }
}
exports.DirectoryListTool = DirectoryListTool;
exports.directoryListTool = new DirectoryListTool();
//# sourceMappingURL=directoryListTool.js.map