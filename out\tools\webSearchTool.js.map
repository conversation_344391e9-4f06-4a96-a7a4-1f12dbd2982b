{"version": 3, "file": "webSearchTool.js", "sourceRoot": "", "sources": ["../../src/tools/webSearchTool.ts"], "names": [], "mappings": ";;;;;;AAEA,kDAA0B;AAC1B,6BAAwB;AAExB,MAAa,aAAa;IACf,EAAE,GAAG,WAAW,CAAC;IACjB,IAAI,GAAG,uBAAuB,CAAC;IAC/B,WAAW,GAAG,oEAAoE,CAAC;IACnF,IAAI,GAAG,cAAc,CAAC,CAAC,oBAAoB;IAC3C,OAAO,GAAyC;QACvD,YAAY,EAAE;YACZ,WAAW,EAAE,iDAAiD;YAC9D,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;aAChD,CAAC;SACH;QACD,OAAO,EAAE;YACP,WAAW,EAAE,6CAA6C;YAC1D,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBAC/C,OAAO,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;aAC5F,CAAC;SACH;KACF,CAAC;IAEF,qDAAqD;IAC7C,KAAK,CAAC,iBAAiB,CAAC,KAAgB,EAAE,QAAuB;QACvE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;YACpC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB;oBAC/B,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU,EAAE,YAAY;iBACzB,CAAC;YACJ,CAAC;YAED,wBAAwB;YACxB,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;gBACxB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uDAAuD;oBAC9D,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU,EAAE,YAAY;iBACzB,CAAC;YACJ,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,iCAAiC,kBAAkB,CAAC,KAAK,CAAC,sCAAsC,CAAC;gBAC7G,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC;oBAC3B,MAAM,EAAE,KAAK;oBACb,GAAG;oBACH,OAAO,EAAE,KAAK,EAAE,oBAAoB;oBACpC,OAAO,EAAE;wBACP,YAAY,EAAE,uBAAuB;qBACtC;iBACF,CAAC,CAAC;gBAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAE3B,8CAA8C;gBAC9C,MAAM,gBAAgB,GAAG;oBACvB,KAAK;oBACL,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE;oBACrC,cAAc,EAAE,IAAI,CAAC,cAAc,IAAI,EAAE;oBACzC,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;oBACnC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE;oBACzB,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,EAAE;oBACjC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,IAAI,EAAE;oBAC7C,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;oBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;oBACvB,aAAa,EAAE,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAA2C,EAAE,EAAE,CAAC,CAAC;wBAC1G,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;wBACtB,GAAG,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE;qBAC1B,CAAC,CAAC;iBACJ,CAAC;gBAEF,kCAAkC;gBAClC,IAAI,OAAO,GAAG,EAAE,CAAC;gBAEjB,IAAI,gBAAgB,CAAC,YAAY,EAAE,CAAC;oBAClC,OAAO,IAAI,GAAG,gBAAgB,CAAC,YAAY,IAAI,CAAC;oBAChD,IAAI,gBAAgB,CAAC,cAAc,EAAE,CAAC;wBACpC,OAAO,IAAI,WAAW,gBAAgB,CAAC,cAAc,IAAI,CAAC;oBAC5D,CAAC;gBACH,CAAC;qBAAM,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;oBACnC,OAAO,IAAI,GAAG,gBAAgB,CAAC,MAAM,IAAI,CAAC;gBAC5C,CAAC;qBAAM,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;oBACvC,OAAO,IAAI,eAAe,gBAAgB,CAAC,UAAU,IAAI,CAAC;oBAC1D,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;wBACtC,OAAO,IAAI,WAAW,gBAAgB,CAAC,gBAAgB,IAAI,CAAC;oBAC9D,CAAC;gBACH,CAAC;qBAAM,IAAI,gBAAgB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrD,OAAO,IAAI,mBAAmB,CAAC;oBAC/B,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,KAAa,EAAE,EAAE;wBACnE,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC;oBAC7C,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,0BAA0B,CAAC;gBACvC,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE;wBACN,OAAO;wBACP,IAAI,EAAE,gBAAgB;qBACvB;oBACD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU,EAAE,YAAY;oBACxB,QAAQ,EAAE;wBACR,WAAW,EAAE,CAAC,CAAC,gBAAgB,CAAC,YAAY;wBAC5C,SAAS,EAAE,CAAC,CAAC,gBAAgB,CAAC,MAAM;wBACpC,aAAa,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU;wBAC5C,UAAU,EAAE,gBAAgB,CAAC,aAAa,CAAC,MAAM;qBAClD;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,8BAA8B;gBAC9B,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBAClC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mDAAmD;wBAC1D,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU,EAAE,YAAY;qBACzB,CAAC;gBACJ,CAAC;qBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;oBAC1B,mEAAmE;oBACnE,qCAAqC;oBACrC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,6BAA6B,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,OAAO,EAAE;wBAC7E,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU,EAAE,YAAY;qBACzB,CAAC;gBACJ,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;oBACzB,oDAAoD;oBACpD,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mFAAmF;wBAC1F,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU,EAAE,YAAY;qBACzB,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,uEAAuE;oBACvE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sBAAsB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;wBACrD,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU,EAAE,YAAY;qBACzB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,sBAAsB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACrD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,YAAY;aACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAgB,EAAE,QAAuB;QACxE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;YACpC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB;oBAC/B,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU,EAAE,OAAO;iBACpB,CAAC;YACJ,CAAC;YAED,qDAAqD;YACrD,MAAM,OAAO,GAAI,KAAK,CAAC,OAAoB,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEhF,mBAAmB;YACnB,MAAM,YAAY,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YACzE,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAEpF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;oBAC7G,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU,EAAE,OAAO;iBACpB,CAAC;YACJ,CAAC;YAED,4BAA4B;YAC5B,MAAM,OAAO,GAAwB,EAAE,CAAC;YACxC,MAAM,MAAM,GAA2B,EAAE,CAAC;YAE1C,sCAAsC;YACtC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC;oBACH,MAAM,GAAG,GAAG,iCAAiC,kBAAkB,CAAC,KAAK,CAAC,sCAAsC,CAAC;oBAC7G,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;oBACrD,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;oBAE3B,OAAO,CAAC,UAAU,GAAG;wBACnB,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE;wBACrC,cAAc,EAAE,IAAI,CAAC,cAAc,IAAI,EAAE;wBACzC,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;wBACnC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE;wBACzB,aAAa,EAAE,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAA2C,EAAE,EAAE,CAAC,CAAC;4BAC1G,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;4BACtB,GAAG,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE;yBAC1B,CAAC,CAAC;qBACJ,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,MAAM,CAAC,UAAU,GAAG,6BAA6B,KAAK,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;gBACtF,CAAC;YACH,CAAC;YAED,4CAA4C;YAC5C,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC;gBAClD,IAAI,CAAC;oBACH,+DAA+D;oBAC/D,kCAAkC;oBAClC,OAAO,CAAC,IAAI,GAAG;wBACb,SAAS,EAAE,iCAAiC,kBAAkB,CAAC,KAAK,CAAC,EAAE;wBACvE,OAAO,EAAE,iEAAiE;qBAC3E,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,MAAM,CAAC,IAAI,GAAG,uBAAuB,KAAK,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;gBAC1E,CAAC;YACH,CAAC;YAED,8CAA8C;YAC9C,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;gBACpD,IAAI,CAAC;oBACH,wEAAwE;oBACxE,kCAAkC;oBAClC,OAAO,CAAC,MAAM,GAAG;wBACf,SAAS,EAAE,mCAAmC,kBAAkB,CAAC,KAAK,CAAC,EAAE;wBACzE,OAAO,EAAE,mEAAmE;qBAC7E,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,MAAM,CAAC,MAAM,GAAG,yBAAyB,KAAK,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;gBAC9E,CAAC;YACH,CAAC;YAED,6CAA6C;YAC7C,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC;gBACnD,IAAI,CAAC;oBACH,OAAO,CAAC,KAAK,GAAG;wBACd,SAAS,EAAE,qCAAqC,kBAAkB,CAAC,KAAK,CAAC,EAAE;wBAC3E,OAAO,EAAE,kEAAkE;qBAC5E,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,MAAM,CAAC,KAAK,GAAG,wBAAwB,KAAK,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;gBAC5E,CAAC;YACH,CAAC;YAED,8CAA8C;YAC9C,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;gBACpD,IAAI,CAAC;oBACH,OAAO,CAAC,MAAM,GAAG;wBACf,SAAS,EAAE,mCAAmC,kBAAkB,CAAC,KAAK,CAAC,EAAE;wBACzE,OAAO,EAAE,mEAAmE;qBAC7E,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,MAAM,CAAC,MAAM,GAAG,yBAAyB,KAAK,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;gBAC9E,CAAC;YACH,CAAC;YAED,0CAA0C;YAC1C,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAE1C,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,8BAA8B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACvE,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU,EAAE,OAAO;iBACpB,CAAC;YACJ,CAAC;YAED,oBAAoB;YACpB,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,KAAK;oBACL,OAAO,EAAE,iBAAiB;oBAC1B,OAAO;oBACP,MAAM,EAAE,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;iBACtD;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,OAAO;gBACnB,QAAQ,EAAE;oBACR,iBAAiB;oBACjB,aAAa;iBACd;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACvD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,OAAO;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,IAAI,CAAC;YACH,4FAA4F;YAC5F,MAAM,QAAQ,GAAG,UAAU,IAAI,KAAK,CAAC,MAAgB,CAAC;YACtD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;YAEpC,iBAAiB;YACjB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB;oBAC/B,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,+DAA+D;YAC/D,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;YACjC,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;gBAC5B,OAAO,WAAW,CAAC,MAAM,CAAC;YAC5B,CAAC;YAED,2DAA2D;YAC3D,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;YAED,iDAAiD;YACjD,QAAQ,QAAQ,EAAE,CAAC;gBACjB,KAAK,YAAY;oBACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBACtD,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBACvD;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,QAAQ,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBAChG,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;wBACV,QAAQ,EAAE;4BACR,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;yBAC5C;qBACF,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,oDAAoD;YACpD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wCAAwC,KAAK,CAAC,OAAO,EAAE;gBAC9D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,UAAU,EAAE,KAAK,CAAC,KAAK;iBACxB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAzWD,sCAyWC;AAEY,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC", "sourcesContent": ["import { ITool, ToolInput, ToolResult, ToolActionDefinition } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport axios from 'axios';\nimport { z } from 'zod';\n\nexport class WebSearchTool implements ITool {\n  readonly id = 'webSearch';\n  readonly name = 'Web Search (Advanced)';\n  readonly description = 'Performs web search using multiple providers and advanced options.';\n  readonly type = 'multi-action'; // Required by ITool\n  readonly actions: Record<string, ToolActionDefinition> = {\n    'duckduckgo': {\n      description: 'Web search using DuckDuckGo Instant Answer API.',\n      inputSchema: z.object({\n        query: z.string().describe('The search query.')\n      })\n    },\n    'multi': {\n      description: 'Search multiple web engines simultaneously.',\n      inputSchema: z.object({\n        query: z.string().describe('The search query.'),\n        engines: z.array(z.string()).optional().describe('Optional list of search engines to use.')\n      })\n    }\n  };\n\n  // Store the implementation of each action separately\n  private async executeDuckDuckGo(input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      const query = input.query as string;\n      if (!query) {\n        return {\n          success: false,\n          error: '\\'query\\' is required.',\n          toolId: this.id,\n          actionName: 'duckduckgo'\n        };\n      }\n\n      // Validate query length\n      if (query.length > 1000) {\n        return {\n          success: false,\n          error: 'Query is too long. Maximum length is 1000 characters.',\n          toolId: this.id,\n          actionName: 'duckduckgo'\n        };\n      }\n\n      try {\n        const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1`;\n        const response = await axios({\n          method: 'get',\n          url,\n          timeout: 10000, // 10 second timeout\n          headers: {\n            'User-Agent': 'Codessa-Extension/1.0'\n          }\n        });\n\n        const data = response.data;\n\n        // Format the results in a more structured way\n        const formattedResults = {\n          query,\n          abstractText: data.AbstractText || '',\n          abstractSource: data.AbstractSource || '',\n          abstractURL: data.AbstractURL || '',\n          answer: data.Answer || '',\n          definition: data.Definition || '',\n          definitionSource: data.DefinitionSource || '',\n          heading: data.Heading || '',\n          image: data.Image || '',\n          relatedTopics: (data.RelatedTopics || []).slice(0, 5).map((topic: { Text?: string; FirstURL?: string }) => ({\n            text: topic.Text || '',\n            url: topic.FirstURL || ''\n          }))\n        };\n\n        // Create a human-readable summary\n        let summary = '';\n\n        if (formattedResults.abstractText) {\n          summary += `${formattedResults.abstractText}\\n`;\n          if (formattedResults.abstractSource) {\n            summary += `Source: ${formattedResults.abstractSource}\\n`;\n          }\n        } else if (formattedResults.answer) {\n          summary += `${formattedResults.answer}\\n`;\n        } else if (formattedResults.definition) {\n          summary += `Definition: ${formattedResults.definition}\\n`;\n          if (formattedResults.definitionSource) {\n            summary += `Source: ${formattedResults.definitionSource}\\n`;\n          }\n        } else if (formattedResults.relatedTopics.length > 0) {\n          summary += 'Related Topics:\\n';\n          formattedResults.relatedTopics.forEach((topic: any, index: number) => {\n            summary += `${index + 1}. ${topic.text}\\n`;\n          });\n        } else {\n          summary = 'No instant answer found.';\n        }\n\n        return {\n          success: true,\n          output: {\n            summary,\n            data: formattedResults\n          },\n          toolId: this.id,\n          actionName: 'duckduckgo',\n          metadata: {\n            hasAbstract: !!formattedResults.abstractText,\n            hasAnswer: !!formattedResults.answer,\n            hasDefinition: !!formattedResults.definition,\n            topicCount: formattedResults.relatedTopics.length\n          }\n        };\n      } catch (error: any) {\n        // Handle specific error types\n        if (error.code === 'ECONNABORTED') {\n          return {\n            success: false,\n            error: 'Search request timed out. Please try again later.',\n            toolId: this.id,\n            actionName: 'duckduckgo'\n          };\n        } else if (error.response) {\n          // The request was made and the server responded with a status code\n          // that falls out of the range of 2xx\n          return {\n            success: false,\n            error: `Search failed with status ${error.response.status}: ${error.message}`,\n            toolId: this.id,\n            actionName: 'duckduckgo'\n          };\n        } else if (error.request) {\n          // The request was made but no response was received\n          return {\n            success: false,\n            error: 'No response received from search provider. Please check your internet connection.',\n            toolId: this.id,\n            actionName: 'duckduckgo'\n          };\n        } else {\n          // Something happened in setting up the request that triggered an Error\n          return {\n            success: false,\n            error: `Web search failed: ${error.message || error}`,\n            toolId: this.id,\n            actionName: 'duckduckgo'\n          };\n        }\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Web search failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName: 'duckduckgo'\n      };\n    }\n  }\n\n  private async executeMultiSearch(input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      const query = input.query as string;\n      if (!query) {\n        return {\n          success: false,\n          error: '\\'query\\' is required.',\n          toolId: this.id,\n          actionName: 'multi'\n        };\n      }\n\n      // Get the list of engines to search, or use defaults\n      const engines = (input.engines as string[]) || ['duckduckgo', 'bing', 'google'];\n\n      // Validate engines\n      const validEngines = ['duckduckgo', 'bing', 'google', 'yahoo', 'yandex'];\n      const invalidEngines = engines.filter(e => !validEngines.includes(e.toLowerCase()));\n\n      if (invalidEngines.length > 0) {\n        return {\n          success: false,\n          error: `Invalid search engines: ${invalidEngines.join(', ')}. Valid options are: ${validEngines.join(', ')}.`,\n          toolId: this.id,\n          actionName: 'multi'\n        };\n      }\n\n      // Initialize results object\n      const results: Record<string, any> = {};\n      const errors: Record<string, string> = {};\n\n      // Search with DuckDuckGo if requested\n      if (engines.some(e => e.toLowerCase() === 'duckduckgo')) {\n        try {\n          const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1`;\n          const response = await axios({ method: 'get', url });\n          const data = response.data;\n\n          results.duckduckgo = {\n            abstractText: data.AbstractText || '',\n            abstractSource: data.AbstractSource || '',\n            abstractURL: data.AbstractURL || '',\n            answer: data.Answer || '',\n            relatedTopics: (data.RelatedTopics || []).slice(0, 5).map((topic: { Text?: string; FirstURL?: string }) => ({\n              text: topic.Text || '',\n              url: topic.FirstURL || ''\n            }))\n          };\n        } catch (error: any) {\n          errors.duckduckgo = `DuckDuckGo search failed: ${error.message || 'Unknown error'}`;\n        }\n      }\n\n      // Search with Bing if requested (simulated)\n      if (engines.some(e => e.toLowerCase() === 'bing')) {\n        try {\n          // In a real implementation, this would use the Bing Search API\n          // For now, we'll simulate results\n          results.bing = {\n            searchUrl: `https://www.bing.com/search?q=${encodeURIComponent(query)}`,\n            message: 'Bing search results would appear here in a real implementation.'\n          };\n        } catch (error: any) {\n          errors.bing = `Bing search failed: ${error.message || 'Unknown error'}`;\n        }\n      }\n\n      // Search with Google if requested (simulated)\n      if (engines.some(e => e.toLowerCase() === 'google')) {\n        try {\n          // In a real implementation, this would use the Google Custom Search API\n          // For now, we'll simulate results\n          results.google = {\n            searchUrl: `https://www.google.com/search?q=${encodeURIComponent(query)}`,\n            message: 'Google search results would appear here in a real implementation.'\n          };\n        } catch (error: any) {\n          errors.google = `Google search failed: ${error.message || 'Unknown error'}`;\n        }\n      }\n\n      // Search with Yahoo if requested (simulated)\n      if (engines.some(e => e.toLowerCase() === 'yahoo')) {\n        try {\n          results.yahoo = {\n            searchUrl: `https://search.yahoo.com/search?p=${encodeURIComponent(query)}`,\n            message: 'Yahoo search results would appear here in a real implementation.'\n          };\n        } catch (error: any) {\n          errors.yahoo = `Yahoo search failed: ${error.message || 'Unknown error'}`;\n        }\n      }\n\n      // Search with Yandex if requested (simulated)\n      if (engines.some(e => e.toLowerCase() === 'yandex')) {\n        try {\n          results.yandex = {\n            searchUrl: `https://yandex.com/search/?text=${encodeURIComponent(query)}`,\n            message: 'Yandex search results would appear here in a real implementation.'\n          };\n        } catch (error: any) {\n          errors.yandex = `Yandex search failed: ${error.message || 'Unknown error'}`;\n        }\n      }\n\n      // Check if we have any successful results\n      const successfulEngines = Object.keys(results);\n      const failedEngines = Object.keys(errors);\n\n      if (successfulEngines.length === 0) {\n        return {\n          success: false,\n          error: `All search engines failed: ${Object.values(errors).join('; ')}`,\n          toolId: this.id,\n          actionName: 'multi'\n        };\n      }\n\n      // Format the output\n      return {\n        success: true,\n        output: {\n          query,\n          engines: successfulEngines,\n          results,\n          errors: failedEngines.length > 0 ? errors : undefined\n        },\n        toolId: this.id,\n        actionName: 'multi',\n        metadata: {\n          successfulEngines,\n          failedEngines\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Multi-search failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName: 'multi'\n      };\n    }\n  }\n\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    try {\n      // For backward compatibility, check if actionName is undefined and try to get it from input\n      const actionId = actionName || input.action as string;\n      const query = input.query as string;\n\n      // Validate query\n      if (!query) {\n        return {\n          success: false,\n          error: '\\'query\\' is required.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Create a clean copy of the input without the action property\n      const actionInput = { ...input };\n      if ('action' in actionInput) {\n        delete actionInput.action;\n      }\n\n      // If no specific action is provided, default to duckduckgo\n      if (!actionId || !this.actions[actionId]) {\n        return this.executeDuckDuckGo(actionInput, context);\n      }\n\n      // Route to the appropriate action implementation\n      switch (actionId) {\n        case 'duckduckgo':\n          return this.executeDuckDuckGo(actionInput, context);\n        case 'multi':\n          return this.executeMultiSearch(actionInput, context);\n        default:\n          return {\n            success: false,\n            error: `Unknown action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`,\n            toolId: this.id,\n            actionName,\n            metadata: {\n              availableActions: Object.keys(this.actions)\n            }\n          };\n      }\n    } catch (error: any) {\n      // Catch any unexpected errors in the execution flow\n      return {\n        success: false,\n        error: `Unexpected error in Web Search tool: ${error.message}`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          errorStack: error.stack\n        }\n      };\n    }\n  }\n}\n\nexport const webSearchTool = new WebSearchTool();\n"]}