{"version": 3, "file": "agentWorker.js", "sourceRoot": "", "sources": ["../../../src/agents/agentUtilities/agentWorker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mDAA8E;AAC9E,mCAAsC;AAetC,MAAa,WAAY,SAAQ,qBAAY;IACnC,MAAM,CAAS;IACvB,YAAY,IAAgB;QAC1B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,IAAI,uBAAM,CAAC,UAAU,EAAE;YACnC,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAwB,EAAE,EAAE;YACrD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;YAC5B,IAAI,IAAI,KAAK,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC;IACD,SAAS;QACP,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;CACF;AAlBD,kCAkBC;AAED,qBAAqB;AACrB,IAAI,6BAAY,KAAK,KAAK,EAAE,CAAC;IAC3B,CAAC,KAAK,IAAI,EAAE;QACV,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,WAAW,GAAG,yBAAa,2BAAU,CAAC,SAAS,uCAAC,CAAC;YACvD,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,2BAAU,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,MAAM,GAAG,KAAK,CAAC,2BAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,GAAG,2BAAU,CAAC,IAAI,EAAE;gBAC9C,QAAQ,EAAE,CAAC,IAAa,EAAE,EAAE;oBAC1B,2BAAU,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClD,CAAC;aACF,CAAC,CAAC;YACH,2BAAU,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,QAAQ,GAAG,EAAE,CAAC;YAClB,IAAI,GAAG,YAAY,KAAK,EAAE,CAAC;gBACzB,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC;YACzB,CAAC;iBAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,SAAS,IAAI,GAAG,EAAE,CAAC;gBACvE,QAAQ,GAAI,GAA2B,CAAC,OAAO,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;YACD,2BAAU,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC,CAAC,EAAE,CAAC;AACP,CAAC", "sourcesContent": ["import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';\nimport { EventEmitter } from 'events';\n\nexport interface WorkerTask {\n    agentPath: string;\n    agentConfig: Record<string, unknown>;\n    method: string;\n    args: unknown[];\n}\n\nexport interface WorkerStreamEvent {\n    type: 'data' | 'end' | 'error';\n    data?: unknown;\n    error?: Error | string;\n}\n\nexport class AgentWorker extends EventEmitter {\n  private worker: Worker;\n  constructor(task: WorkerTask) {\n    super();\n    this.worker = new Worker(__filename, {\n      workerData: task\n    });\n    this.worker.on('message', (event: WorkerStreamEvent) => {\n      this.emit(event.type, event.data || event.error);\n    });\n    this.worker.on('error', err => this.emit('error', err));\n    this.worker.on('exit', code => {\n      if (code !== 0) this.emit('error', new Error(`Worker stopped with exit code ${code}`));\n    });\n  }\n  terminate(): void {\n    this.worker.terminate();\n  }\n}\n\n// Worker thread code\nif (isMainThread === false) {\n  (async () => {\n    try {\n      // Dynamic import for the agent class\n      const agentModule = await import(workerData.agentPath);\n      const AgentClass = agentModule.default || agentModule;\n      const agent = new AgentClass(workerData.agentConfig);\n      const method = agent[workerData.method].bind(agent);\n      const result = await method(...workerData.args, {\n        onStream: (data: unknown) => {\n          parentPort?.postMessage({ type: 'data', data });\n        }\n      });\n      parentPort?.postMessage({ type: 'end', data: result });\n    } catch (err) {\n      let errorMsg = '';\n      if (err instanceof Error) {\n        errorMsg = err.message;\n      } else if (typeof err === 'object' && err !== null && 'message' in err) {\n        errorMsg = (err as { message: string }).message;\n      } else {\n        errorMsg = String(err);\n      }\n      parentPort?.postMessage({ type: 'error', error: errorMsg });\n    }\n  })();\n}\n"]}