{"version": 3, "file": "specializedTemplates.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/specializedTemplates.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AA8HH,oEAoBC;AAED,4DAyDC;AAED,sEA0EC;AAED,0DAoFC;AAED,gDAgBC;AAED,8CA0CC;AAED,gDA2CC;AAED,sDAkEC;AAED,sDA0CC;AAED,4DA0BC;AAED,wDA0FC;AAED,wDAuFC;AAED,oDAkCC;AAED,kDAgCC;AAED,4CA0EC;AAED,kDAiCC;AA78BD,mDAAkE;AAClE,mCAAkC;AAElC,yDAAsD;AACtD,+DAA4D;AAC5D,yCAAsC;AACtC,iDAWwB;AAqBxB,kEAAkE;AAElE,MAAM,eAAe;IACX,MAAM,CAAiB;IACvB,UAAU,CAAqB;IAEvC,YAAY,OAAY;QACtB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG;YAChB,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;SACV,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,IAAe;QACrB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,IAAe;QACrB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK;QACH,MAAM,QAAQ,GAAoB;YAChC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;YAClB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO;YACvC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,IAAqB,IAAI,SAAS;YAC7D,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;YAC5B,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;YAC5B,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,OAAO;YACnD,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE;YACpC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,SAAS;YACnC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;SACrC,CAAC;QAEF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;QAChG,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC5C,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED,yEAAyE;AAEzE,SAAS,eAAe;IACtB,OAAO,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,gBAAgB;IACvB,OAAO,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,eAAe,CAAC,KAAwB,EAAE,IAAI,GAAG,OAAO;IAC/D,IAAI,CAAC,KAAK;QAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,EAAE,CAAC,CAAC;IACzE,OAAO,eAAO,CAAC,eAAe,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC7E,CAAC;AAED,SAAS,cAAc,CAAC,IAAW,EAAE,IAAY,EAAE,KAAc;IAC/D,OAAO;QACL,EAAE,EAAE,IAAI,CAAC,EAAE;QACX,IAAI,EAAE,MAAM;QACZ,IAAI;QACJ,KAAK,EAAE,KAAK,IAAI,IAAI;QACpB,IAAI;QACJ,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,0DAA0D;YAC1D,mFAAmF;YACnF,MAAM,UAAU,GAAI,KAAa,CAAC,UAAU,CAAC;YAC7C,MAAM,KAAK,GAAI,KAAa,CAAC,KAAK,CAAC;YACnC,MAAM,OAAO,GAAI,KAAa,CAAC,OAAO,CAAC;YACvC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAClE,qFAAqF;YACrF,OAAO;gBACL,GAAG,KAAK;gBACR,UAAU;aACX,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,8DAA8D;AAE9D,SAAgB,4BAA4B,CAAC,OAAY;IACvD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;IAC/E,CAAC;IAED,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,cAAc,CAAC,IAAA,wCAAyB,GAAE,EAAE,kBAAkB,CAAC,CAAC;SACxE,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,8CAA8C;SACtF,OAAO,CAAC,cAAc,CAAC,IAAA,mCAAoB,GAAE,EAAE,aAAa,CAAC,CAAC;SAC9D,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAgB,IAAI,SAAS,EAAE,CAAC,CAAC,YAAY;SAChJ,OAAO,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,IAAgB,IAAI,SAAS,EAAE,CAAC,CAAC,YAAY;SACtJ,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAgB,IAAI,SAAS,EAAE,CAAC,CAAC,YAAY;SAC/I,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,IAAgB,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,YAAY;IAE/I,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,wBAAwB,CAAC,OAAY;IACnD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;IAC3E,CAAC;IAED,MAAM,YAAY,GAAG,cAAc,CAAC,IAAA,0CAA2B,GAAE,EAAE,oBAAoB,CAAC,CAAC;IACzF,MAAM,cAAc,GAAc;QAChC,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,wBAAwB;QAC9B,KAAK,EAAE,wBAAwB;QAC/B,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;YAChG,CAAC;YACD,MAAM,MAAM,GAAG,kEAAkE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,YAAY,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAC5L,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACtD,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,MAAM,SAAS,GAAc;QAC3B,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,mBAAmB;QACzB,KAAK,EAAE,mBAAmB;QAC1B,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;YAClG,CAAC;YACD,MAAM,MAAM,GAAG,yDAAyD,KAAK,CAAC,aAAa,YAAY,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAC9I,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAClD,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,YAAY,CAAC;SACrB,OAAO,CAAC,cAAc,CAAC;SACvB,OAAO,CAAC,SAAS,CAAC;SAClB,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,yBAAyB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAgB,IAAI,SAAS,EAAE,CAAC,CAAC,YAAY;SACzJ,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAgB,IAAI,SAAS,EAAE,CAAC,CAAC,YAAY;SAC9J,OAAO,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAgB,IAAI,SAAS,EAAE,CAAC,CAAC,YAAY;SAC3J,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,IAAgB,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,YAAY;IAElJ,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,6BAA6B,CAAC,OAAY;IACxD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;IAChF,CAAC;IAED,MAAM,YAAY,GAAc;QAC9B,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,eAAe;QACtB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;YAC9F,CAAC;YACD,MAAM,MAAM,GAAG,4DAA4D,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAClH,KAAK,CAAC,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,MAAM,YAAY,GAAc;QAC9B,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,kBAAkB;QACxB,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;YAC9F,CAAC;YACD,MAAM,MAAM,GAAG,6CAA6C,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC7E,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,MAAM,YAAY,GAAc;QAC9B,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,4BAA4B;QAClC,KAAK,EAAE,4BAA4B;QACnC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;YAC9F,CAAC;YACD,MAAM,MAAM,GAAG,uCAAuC,KAAK,CAAC,IAAI,oBAAoB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAC3H,KAAK,CAAC,cAAc,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,YAAY,CAAC;SACrB,OAAO,CAAC,YAAY,CAAC;SACrB,OAAO,CAAC,YAAY,CAAC;SACrB,OAAO,CAAC,cAAc,CAAC,IAAA,gCAAiB,GAAE,EAAE,mBAAmB,CAAC,CAAC;SACjE,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;SAC3H,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;SAClI,OAAO,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;SAC9H,OAAO,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;SACvI,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;KAClI;IAEH,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,uBAAuB,CAAC,OAAY;IAClD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACzE,CAAC;IAED,MAAM,aAAa,GAAc;QAC/B,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAE,gBAAgB;QACvB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;YAC/F,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;SACZ,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,KAAK,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,MAAM,WAAW,GAAc;QAC7B,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,qBAAqB;QAC3B,KAAK,EAAE,qBAAqB;QAC5B,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;YAC7F,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;gBACL,KAAK,CAAC,WAAW,EAAE,CAAC;YAE9B,KAAK,CAAC,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,MAAM,WAAW,GAAc;QAC7B,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,sBAAsB;QAC5B,KAAK,EAAE,sBAAsB;QAC7B,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;YAC5F,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;YACT,KAAK,CAAC,QAAQ;gBACV,KAAK,CAAC,WAAW,EAAE,CAAC;YAE9B,KAAK,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,aAAa,CAAC;SACtB,OAAO,CAAC,WAAW,CAAC;SACpB,OAAO,CAAC,WAAW,CAAC;SACpB,OAAO,CAAC,cAAc,CAAC,IAAA,gCAAiB,GAAE,EAAE,qBAAqB,CAAC,CAAC;SACnE,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SACtH,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SAChI,OAAO,CAAC,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SACnI,OAAO,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,0BAA0B,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SACjJ,OAAO,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,0BAA0B,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;KACvI;IAEH,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,kBAAkB,CAAC,OAAY;IAC7C,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;IACpE,CAAC;IAED,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,8CAA8C;SACpG,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAkB,EAAE,CAAC,CAAC,YAAY;SACvH,OAAO,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAkB,EAAE,CAAC,CAAC,CAAC,YAAY;IAE7H,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,iBAAiB,CAAC,OAAY;IAC5C,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;IACnE,CAAC;IAED,MAAM,gBAAgB,GAAG,cAAc,CAAC,IAAA,0CAA2B,GAAE,EAAE,mBAAmB,CAAC,CAAC;IAC5F,MAAM,eAAe,GAAc;QACjC,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,kBAAkB;QACxB,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;YACjG,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;WACV,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;SACzD,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YACvE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAGF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,gBAAgB,CAAC;SACzB,OAAO,CAAC,eAAe,CAAC;SACxB,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,KAAiB,EAAE,CAAC,CAAC,YAAY;SAC9H,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,KAAiB,EAAE,CAAC,CAAC,YAAY;SAC1I,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAiB,EAAE,CAAC,CAAC,CAAC,YAAY;IAE7H,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,kBAAkB,CAAC,OAAY;IAC7C,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,MAAM,YAAY,GAAG,cAAc,CAAC,IAAA,qCAAsB,GAAE,EAAE,eAAe,CAAC,CAAC;IAC/E,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;IACpE,CAAC;IACD,MAAM,MAAM,GAAc;QACxB,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,aAAa;QACnB,KAAK,EAAE,aAAa;QACpB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;YAC5F,CAAC;YACD,gDAAgD;YAChD,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,qBAAqB,EAAE;gBAC/D,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC;gBAC7C,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE;aACzD,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC3E,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAGF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,YAAY,CAAC;SACrB,OAAO,CAAC,MAAM,CAAC;SACf,OAAO,CAAC,cAAc,CAAC,IAAA,gCAAiB,GAAE,EAAE,gBAAgB,CAAC,CAAC;SAC9D,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,MAAkB,EAAE,CAAC,CAAC,YAAY;SAC5H,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,MAAkB,EAAE,CAAC,CAAC,YAAY;SACjI,OAAO,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,MAAkB,EAAE,CAAC,CAAC,YAAY;SACpI,OAAO,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAkB,EAAE,CAAC,CAAC,CAAC,YAAY;IAErI,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,qBAAqB,CAAC,OAAY;IAChD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,MAAM,aAAa,GAAG,cAAc,CAAC,IAAA,sCAAuB,GAAE,EAAE,gBAAgB,CAAC,CAAC;IAClF,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACvE,CAAC;IACD,MAAM,aAAa,GAAc;QAC/B,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAE,gBAAgB;QACvB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;YAC/F,CAAC;YACD,gDAAgD;YAChD,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,wBAAwB,EAAE;gBAClE,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC;gBACnD,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE;aACzD,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC9E,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;YACpC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,YAAY,GAAc;QAC9B,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,eAAe;QACtB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;YAC9F,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;QACb,KAAK,CAAC,aAAa;iBACV,KAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAElD,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC/E,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAGF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,aAAa,CAAC;SACtB,OAAO,CAAC,aAAa,CAAC;SACtB,OAAO,CAAC,YAAY,CAAC;SACrB,OAAO,CAAC,cAAc,CAAC,IAAA,gCAAiB,GAAE,EAAE,sBAAsB,CAAC,CAAC;SACpE,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAqB,EAAE,CAAC,CAAC,YAAY;SAC5H,OAAO,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAqB,EAAE,CAAC,CAAC,YAAY;SACpI,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAqB,EAAE,CAAC,CAAC,YAAY;SAChI,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,2BAA2B,EAAE,IAAI,EAAE,SAAqB,EAAE,CAAC,CAAC,YAAY;SAC3I,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,2BAA2B,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAqB,EAAE,CAAC,CAAC,CAAC,YAAY;IAExI,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,qBAAqB,CAAC,OAAY;IAChD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,WAAW,GAAG,cAAc,CAAC,IAAA,sCAAuB,GAAE,EAAE,cAAc,CAAC,CAAC;IAC9E,MAAM,aAAa,GAAc;QAC/B,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAE,gBAAgB;QACvB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;YAC/F,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;mBACF,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC;SAC5C,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAChF,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAGF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,WAAW,CAAC;SACpB,OAAO,CAAC,aAAa,CAAC;SACtB,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAqB,EAAE,CAAC,CAAC,YAAY;SAC1H,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAqB,EAAE,CAAC,CAAC,YAAY;SACvI,OAAO,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAqB,EAAE,CAAC,CAAC,CAAC,YAAY;IAElI,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,wBAAwB,CAAC,OAAY;IACnD,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,wBAAgB,CAAC,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;IAC/E,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;IAC3E,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IAC9E,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;IAC3E,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAErE,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,UAAU,CAAC;SACnB,OAAO,CAAC,WAAW,CAAC;SACpB,OAAO,CAAC,UAAU,CAAC;SACnB,OAAO,CAAC,QAAQ,CAAC;SACjB,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;SAC1G,OAAO,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;SAC5H,OAAO,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;SAC5H,OAAO,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;SACtH,OAAO,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAE5G,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,sBAAsB,CAAC,OAAY;IACjD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACzE,CAAC;IAED,MAAM,YAAY,GAAG,cAAc,CAAC,IAAA,sCAAuB,GAAE,EAAE,uBAAuB,EAAE,uBAAuB,CAAC,CAAC;IACjH,MAAM,SAAS,GAAc;QAC3B,EAAE,EAAE,oBAAoB;QACxB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,oBAAoB;QAC1B,KAAK,EAAE,oBAAoB;QAC3B,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,+EAA+E,CAAC,CAAC;YACnG,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;gBACL,KAAK,CAAC,OAAO,CAAC,4BAA4B,CAAC;SAClD,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC3E,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,SAAS,GAAc;QAC3B,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,mBAAmB;QACzB,KAAK,EAAE,mBAAmB;QAC1B,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;YAClG,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;cACP,KAAK,CAAC,UAAU;SACrB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAChF,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,cAAc,GAAc;QAChC,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAE,gBAAgB;QACvB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;YAC/F,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;aACR,KAAK,CAAC,SAAS;SACnB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC9E,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;YACpC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,MAAM,GAAG,cAAc,CAAC,IAAA,gCAAiB,GAAE,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;IAE7E,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,YAAY,CAAC;SACrB,OAAO,CAAC,SAAS,CAAC;SAClB,OAAO,CAAC,SAAS,CAAC;SAClB,OAAO,CAAC,cAAc,CAAC;SACvB,OAAO,CAAC,MAAM,CAAC;SACf,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,4BAA4B,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SACtI,OAAO,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,mBAAmB;SACzI,OAAO,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SAC3I,OAAO,CAAC,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SAC5I,OAAO,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SACpI,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,CAAC,YAAY;IAExH,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,sBAAsB,CAAC,OAAY;IACjD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IACxE,CAAC;IAED,MAAM,gBAAgB,GAAc;QAClC,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,mBAAmB;QACzB,KAAK,EAAE,mBAAmB;QAC1B,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;YAClG,CAAC;YACD,gDAAgD;YAChD,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,2BAA2B,EAAE;gBACrE,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE;aACrD,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YACjF,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;YAC1C,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,cAAc,GAAG,cAAc,CAAC,IAAA,0CAA2B,GAAE,EAAE,iBAAiB,CAAC,CAAC;IACxF,MAAM,QAAQ,GAAc;QAC1B,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,kBAAkB;QACxB,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;YACjG,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;oBACD,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC;qBACpC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAExC,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC/E,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,eAAe,GAAc;QACjC,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,kBAAkB;QACxB,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;YACjG,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;oBACD,KAAK,CAAC,cAAc;SAC/B,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YACvE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IAEF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,gBAAgB,CAAC;SACzB,OAAO,CAAC,cAAc,CAAC;SACvB,OAAO,CAAC,QAAQ,CAAC;SACjB,OAAO,CAAC,eAAe,CAAC;SACxB,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;SAC/H,OAAO,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;SACjJ,OAAO,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;SAChJ,OAAO,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,YAAY;SAC1I,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,CAAC,YAAY;IAElI,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,oBAAoB,CAAC,OAAY;IAC/C,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAE7C,4IAA4I;IAC5I,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,2GAA2G;QAC3G,+FAA+F;QAC/F,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,wBAAgB,CAAC,EAAE,CAAC;YACjD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAED,MAAM,UAAU,GAAG,cAAc,CAAC,IAAA,sCAAuB,GAAE,EAAE,aAAa,CAAC,CAAC;IAC5E,MAAM,KAAK,GAAG,cAAc,CAAC,IAAA,8BAAe,GAAE,EAAE,cAAc,CAAC,CAAC;IAChE,MAAM,IAAI,GAAG,cAAc,CAAC,IAAA,gCAAiB,GAAE,EAAE,YAAY,CAAC,CAAC;IAC/D,MAAM,MAAM,GAAG,cAAc,CAAC,IAAA,mCAAoB,GAAE,EAAE,YAAY,CAAC,CAAC;IACpE,MAAM,OAAO,GAAG,cAAc,CAAC,IAAA,mCAAoB,GAAE,EAAE,YAAY,CAAC,CAAC;IAErE,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,UAAU,CAAC;SACnB,OAAO,CAAC,KAAK,CAAC;SACd,OAAO,CAAC,IAAI,CAAC;SACb,OAAO,CAAC,MAAM,CAAC;SACf,OAAO,CAAC,OAAO,CAAC;SAChB,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,QAAoB,EAAE,CAAC,CAAC,YAAY;SAC1H,OAAO,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,QAAoB,EAAE,CAAC,CAAC,YAAY;SACtI,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,QAAoB,EAAE,CAAC,CAAC,YAAY;SACnI,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,QAAoB,EAAE,CAAC,CAAC,YAAY;SAClI,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,QAAoB,EAAE,CAAC,CAAC,YAAY;SACrI,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAoB,EAAE,CAAC,CAAC,CAAC,YAAY;IAEhI,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,mBAAmB,CAAC,OAAY;IAC9C,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC;QAClC,GAAG,OAAO;QACV,WAAW,EAAE,OAAO;KACrB,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,cAAc,CAAC,IAAA,sCAAuB,GAAE,EAAE,iBAAiB,CAAC,CAAC;IAC7E,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;IACrE,CAAC;IACD,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;IACnE,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IAClE,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IAC/D,MAAM,aAAa,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IAEtE,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,OAAO,CAAC;SAChB,OAAO,CAAC,QAAQ,CAAC;SACjB,OAAO,CAAC,WAAW,CAAC;SACpB,OAAO,CAAC,MAAM,CAAC;SACf,OAAO,CAAC,aAAa,CAAC;SACtB,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;SACrG,OAAO,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;SACxH,OAAO,CAAC,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;SACzH,OAAO,CAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;SACrH,OAAO,CAAC,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;SACzH,OAAO,CAAC,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;SAC5G,OAAO,CAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAElI,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,gBAAgB,CAAC,OAAY;IAC3C,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC;QAClC,GAAG,OAAO;QACV,WAAW,EAAE,IAAI;KAClB,CAAC,CAAC;IAEH,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IAClE,CAAC;IAED,MAAM,SAAS,GAAG,cAAc,CAAC,IAAA,sCAAuB,GAAE,EAAE,YAAY,CAAC,CAAC;IAC1E,MAAM,eAAe,GAAc;QACjC,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,kBAAkB;QACxB,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;YACjG,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;cACP,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;SACrC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YAC9E,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;YACpC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,GAAG,GAAc;QACrB,EAAE,EAAE,oBAAoB;QACxB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,oBAAoB;QAC1B,KAAK,EAAE,oBAAoB;QAC3B,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,8CAA8C;QACpE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvB,mDAAmD;YACnD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,+EAA+E,CAAC,CAAC;YACnG,CAAC;YACD,4BAA4B;YAC5B,MAAM,MAAM,GAAG;kBACH,KAAK,CAAC,aAAa;SAC5B,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;YAE1C,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;YACxE,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,qBAAqB,GAAG,cAAc,CAAC,IAAA,8BAAe,GAAE,EAAE,aAAa,CAAC,CAAC;IAC/E,MAAM,gBAAgB,GAAG,cAAc,CAAC,IAAA,sCAAuB,GAAE,EAAE,mBAAmB,CAAC,CAAC;IAExF,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,SAAS,CAAC;SAClB,OAAO,CAAC,eAAe,CAAC;SACxB,OAAO,CAAC,GAAG,CAAC;SACZ,OAAO,CAAC,qBAAqB,CAAC;SAC9B,OAAO,CAAC,gBAAgB,CAAC;SACzB,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SACpH,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SACjI,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SAClI,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SAC7H,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SACtI,OAAO,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAgB,EAAE,CAAC,CAAC,YAAY;SAChI,OAAO,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,UAAsB,EAAE,CAAC,CAAC,CAAC,YAAY;IAElJ,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,SAAgB,mBAAmB,CAAC,OAAY;IAC9C,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC;QAClC,GAAG,OAAO;QACV,WAAW,EAAE,OAAO;KACrB,CAAC,CAAC;IAEH,6DAA6D;IAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,YAAY,aAAK,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;IACrE,CAAC;IAED,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,8CAA8C;IACpH,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC,8CAA8C;IAClH,MAAM,UAAU,GAAG,cAAc,CAAC,IAAA,sCAAuB,GAAE,EAAE,aAAa,CAAC,CAAC;IAC5E,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC,CAAC,8CAA8C;IACjH,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,8CAA8C;IAE9G,OAAO;SACJ,OAAO,CAAC,eAAe,EAAE,CAAC;SAC1B,OAAO,CAAC,YAAY,CAAC;SACrB,OAAO,CAAC,WAAW,CAAC;SACpB,OAAO,CAAC,UAAU,CAAC;SACnB,OAAO,CAAC,MAAM,CAAC;SACf,OAAO,CAAC,MAAM,CAAC;SACf,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC3B,OAAO,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SACxH,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SAClI,OAAO,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SAClI,OAAO,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SAC1I,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,YAAY;SAC9I,OAAO,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAmB,EAAE,CAAC,CAAC,CAAC,YAAY;IAElI,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,6DAA6D;AAE7D,MAAa,eAAe;IAC1B,MAAM,CAAC,MAAM,CAAC,OAAY;QACxB,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO,4BAA4B,CAAC,OAAO,CAAC,CAAC;YAC/C,KAAK,aAAa;gBAChB,OAAO,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAC3C,KAAK,aAAa;gBAChB,OAAO,6BAA6B,CAAC,OAAO,CAAC,CAAC;YAChD,KAAK,WAAW;gBACd,OAAO,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAC1C,KAAK,MAAM;gBACT,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACrC,KAAK,KAAK;gBACR,OAAO,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACpC,KAAK,MAAM;gBACT,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACrC,KAAK,SAAS;gBACZ,OAAO,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACxC,KAAK,SAAS;gBACZ,OAAO,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACxC,KAAK,aAAa;gBAChB,OAAO,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAC3C,KAAK,OAAO;gBACV,OAAO,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACzC,KAAK,UAAU;gBACb,OAAO,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACzC,KAAK,QAAQ;gBACX,OAAO,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACvC,KAAK,OAAO;gBACV,OAAO,mBAAmB,CAAC,OAAO,CAAC,CAAC;YACtC,KAAK,IAAI;gBACP,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACnC,KAAK,OAAO;gBACV,OAAO,mBAAmB,CAAC,OAAO,CAAC,CAAC;YACtC,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,oDAAoD;gBACpD,MAAM,EAAE,wBAAwB,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;gBAC9D,OAAO,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAC3C,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,oDAAoD;gBACpD,MAAM,EAAE,sBAAsB,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;gBAC5D,OAAO,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACzC,CAAC;YACD,KAAK,YAAY,CAAC,CAAC,CAAC;gBAClB,oDAAoD;gBACpD,MAAM,EAAE,wBAAwB,EAAE,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;gBACrE,OAAO,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAC3C,CAAC;YACD,KAAK,KAAK,CAAC,CAAC,CAAC;gBACX,oDAAoD;gBACpD,MAAM,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;gBACvD,OAAO,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACnD,CAAC;YACD,KAAK,qBAAqB,CAAC,CAAC,CAAC;gBAC3B,oDAAoD;gBACpD,MAAM,EAAE,gCAAgC,EAAE,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;gBAC9E,OAAO,gCAAgC,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClE,CAAC;YACD,KAAK,gBAAgB,CAAC,CAAC,CAAC;gBACtB,oDAAoD;gBACpD,MAAM,EAAE,2BAA2B,EAAE,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;gBACzE,OAAO,2BAA2B,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;YACD;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;CACF;AArED,0CAqEC", "sourcesContent": ["/**\n * Ultimate Codessa Workflow System\n * Supports all VSCode operation modes, SDLC methodologies, and advanced AI workflows\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent, MultiAgentSystem } from '../agentUtilities/agent';\nimport { Codessa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, WorkflowType, Methodology, EdgeType, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { promptManager } from '../../prompts/promptManager';\nimport { Logger } from '../../logger';\nimport {\n  createMemoryRetrievalTool,\n  createMemorySaveTool,\n  createDocumentRetrievalTool,\n  createCodeAnalysisTool,\n  createTestingTool,\n  createDocumentationTool,\n  createDeploymentTool,\n  createMonitoringTool,\n  createCI_CDTool,\n  // Removed duplicate imports\n} from './vectorStores';\n\n// ==================== TYPES AND INTERFACES ====================\n\ninterface WorkflowConfig {\n  id: string;\n  name: string;\n  description: string;\n  agent: Agent | MultiAgentSystem;\n  type?: WorkflowType;\n  methodology?: Methodology;\n  version?: string;\n  metadata?: Record<string, any>;\n}\n\ninterface WorkflowComponents {\n  nodes: GraphNode[];\n  edges: GraphEdge[];\n  startNodeId?: string;\n}\n\n// ==================== CORE WORKFLOW BUILDER ====================\n\nclass WorkflowBuilder {\n  private config: WorkflowConfig;\n  private components: WorkflowComponents;\n\n  constructor(_config: any) {\n    this.config = _config;\n    this.components = {\n      nodes: [],\n      edges: []\n    };\n  }\n\n  addNode(node: GraphNode): WorkflowBuilder {\n    this.components.nodes.push(node);\n    return this;\n  }\n\n  addEdge(edge: GraphEdge): WorkflowBuilder {\n    this.components.edges.push(edge);\n    return this;\n  }\n\n  build(): GraphDefinition {\n    const workflow: GraphDefinition = {\n      id: this.config.id,\n      name: this.config.name,\n      description: this.config.description,\n      version: this.config.version || '1.0.0',\n      operationMode: this.config.type as OperationMode || 'agentic',\n      nodes: this.components.nodes,\n      edges: this.components.edges,\n      startNodeId: this.components.startNodeId || 'input',\n      metadata: this.config.metadata || {},\n      type: this.config.type || 'default',\n      methodology: this.config.methodology\n    };\n\n    Logger.instance.info('Registering workflow: ' + this.config.name + ' (' + this.config.id + ')');\n    workflowRegistry.registerWorkflow(workflow);\n    return workflow;\n  }\n}\n\n// ==================== WORKFLOW COMPONENT FACTORIES ====================\n\nfunction createInputNode(): GraphNode {\n  return Codessa.createInputNode('input', 'Input');\n}\n\nfunction createOutputNode(): GraphNode {\n  return Codessa.createOutputNode('output', 'Output');\n}\n\nfunction createAgentNode(agent: Agent | undefined, name = 'Agent'): GraphNode {\n  if (!agent) throw new Error(`Agent instance required for node: ${name}`);\n  return Codessa.createAgentNode(`agent-${name.toLowerCase()}`, name, agent);\n}\n\nfunction createToolNode(tool: ITool, name: string, label?: string): GraphNode {\n  return {\n    id: tool.id,\n    type: 'tool',\n    name,\n    label: label || name,\n    tool,\n    execute: async (state) => {\n      // Map GraphState to ToolInput and call the tool's execute\n      // You may need to adjust how actionName, input, and context are derived from state\n      const actionName = (state as any).actionName;\n      const input = (state as any).input;\n      const context = (state as any).context;\n      const toolResult = await tool.execute(actionName, input, context);\n      // You may want to merge toolResult.output into the state.outputs or return as needed\n      return {\n        ...state,\n        toolResult\n      };\n    }\n  };\n}\n\n// ==================== WORKFLOW CREATORS ====================\n\nexport function createMemoryEnhancedWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Memory Enhanced workflow requires a single Agent instance');\n  }\n\n  builder\n    .addNode(createInputNode())\n    .addNode(createToolNode(createMemoryRetrievalTool(), 'Memory Retrieval'))\n    .addNode(createAgentNode(_config.agent)) // Now _config.agent is guaranteed to be Agent\n    .addNode(createToolNode(createMemorySaveTool(), 'Memory Save'))\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-memory', source: 'input', target: 'tool-memory-retrieval', type: _config.type as EdgeType || 'default' }) // Cast type\n    .addEdge({ name: 'memory-to-agent', source: 'tool-memory-retrieval', target: 'agent-agent', type: _config.type as EdgeType || 'default' }) // Cast type\n    .addEdge({ name: 'agent-to-save', source: 'agent-agent', target: 'tool-memory-save', type: _config.type as EdgeType || 'default' }) // Cast type\n    .addEdge({ name: 'save-to-output', source: 'tool-memory-save', target: 'output', type: _config.type as EdgeType || 'default' }); // Cast type\n\n  return builder.build();\n}\n\nexport function createDocumentQAWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Document QA workflow requires a single Agent instance');\n  }\n\n  const docRetrieval = createToolNode(createDocumentRetrievalTool(), 'Document Retrieval');\n  const infoExtraction: GraphNode = {\n    id: 'info-extraction',\n    type: 'agent',\n    name: 'Information Extraction',\n    label: 'Information Extraction',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in info-extraction node');\n      }\n      const prompt = `Task: Extract relevant information from documents.\\nDocuments: ${JSON.stringify(state.outputs['tool-document-retrieval'])}\\nQuery: ${state.messages.slice(-1)[0]?.content}`;\n      const extraction = await state.agent.generate(prompt);\n      state.extractedInfo = extraction;\n      return state;\n    }\n  };\n\n  const answerGen: GraphNode = {\n    id: 'answer-generation',\n    type: 'agent',\n    name: 'Answer Generation',\n    label: 'Answer Generation',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in answer-generation node');\n      }\n      const prompt = `Task: Generate comprehensive answer.\\nExtracted Info: ${state.extractedInfo}\\nQuery: ${state.messages.slice(-1)[0]?.content}`;\n      const answer = await state.agent.generate(prompt);\n      state.answer = answer;\n      return state;\n    }\n  };\n\n  builder\n    .addNode(createInputNode())\n    .addNode(docRetrieval)\n    .addNode(infoExtraction)\n    .addNode(answerGen)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-doc-retrieval', source: 'input', target: 'tool-document-retrieval', type: _config.type as EdgeType || 'default' }) // Cast type\n    .addEdge({ name: 'doc-to-extraction', source: 'tool-document-retrieval', target: 'info-extraction', type: _config.type as EdgeType || 'default' }) // Cast type\n    .addEdge({ name: 'extraction-to-answer', source: 'info-extraction', target: 'answer-generation', type: _config.type as EdgeType || 'default' }) // Cast type\n    .addEdge({ name: 'answer-to-output', source: 'answer-generation', target: 'output', type: _config.type as EdgeType || 'default' }); // Cast type\n\n  return builder.build();\n}\n\nexport function createCodeRefactoringWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Code Refactoring workflow requires a single Agent instance');\n  }\n\n  const codeAnalysis: GraphNode = {\n    id: 'code-analysis',\n    type: 'agent',\n    name: 'Code Analysis',\n    label: 'Code Analysis',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in code-analysis node');\n      }\n      const prompt = `Task: Analyze code for refactoring opportunities.\\nCode: ${state.messages.slice(-1)[0]?.content}`;\n      state.analysis = await state.agent.generate(prompt);\n      return state;\n    }\n  };\n\n  const refactorPlan: GraphNode = {\n    id: 'refactor-plan',\n    type: 'agent',\n    name: 'Refactoring Plan',\n    label: 'Refactoring Plan',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in refactor-plan node');\n      }\n      const prompt = `Task: Create refactoring plan.\\nAnalysis: ${state.analysis}`;\n      state.plan = await state.agent.generate(prompt);\n      return state;\n    }\n  };\n\n  const refactorImpl: GraphNode = {\n    id: 'refactor-impl',\n    type: 'agent',\n    name: 'Refactoring Implementation',\n    label: 'Refactoring Implementation',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in refactor-impl node');\n      }\n      const prompt = `Task: Implement refactoring.\\nPlan: ${state.plan}\\nOriginal Code: ${state.messages.slice(-1)[0]?.content}`;\n      state.refactoredCode = await state.agent.generate(prompt);\n      return state;\n    }\n  };\n\n  builder\n    .addNode(createInputNode())\n    .addNode(codeAnalysis)\n    .addNode(refactorPlan)\n    .addNode(refactorImpl)\n    .addNode(createToolNode(createTestingTool(), 'Refactoring Tests'))\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'refactor' as EdgeType }) // Cast type\n    .addEdge({ name: 'analysis-to-plan', source: 'code-analysis', target: 'refactor-plan', type: 'refactor' as EdgeType }) // Cast type\n    .addEdge({ name: 'plan-to-impl', source: 'refactor-plan', target: 'refactor-impl', type: 'refactor' as EdgeType }) // Cast type\n    .addEdge({ name: 'impl-to-test', source: 'refactor-impl', target: 'tool-refactoring-tests', type: 'refactor' as EdgeType }) // Cast type\n    .addEdge({ name: 'test-to-output', source: 'tool-refactoring-tests', target: 'output', type: 'refactor' as EdgeType }) // Cast type\n    ;\n\n  return builder.build();\n}\n\nexport function createDebuggingWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Debugging workflow requires a single Agent instance');\n  }\n\n  const errorAnalysis: GraphNode = {\n    id: 'error-analysis',\n    type: 'agent',\n    name: 'Error Analysis',\n    label: 'Error Analysis',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in error-analysis node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Analyze error and identify root cause.\nError: ${state.messages.slice(-1)[0]?.content}`;\n\n      state.errorReport = await state.agent.generate(prompt); // Use string prompt\n      return state;\n    }\n  };\n\n  const solutionGen: GraphNode = {\n    id: 'solution-gen',\n    type: 'agent',\n    name: 'Solution Generation',\n    label: 'Solution Generation',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in solution-gen node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Generate solution for error.\nError Report: ${state.errorReport}`;\n\n      state.solution = await state.agent.generate(prompt); // Use string prompt\n      return state;\n    }\n  };\n\n  const explanation: GraphNode = {\n    id: 'explanation',\n    type: 'agent',\n    name: 'Solution Explanation',\n    label: 'Solution Explanation',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in explanation node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Explain solution and root cause.\nSolution: ${state.solution}\nError Report: ${state.errorReport}`;\n\n      state.explanation = await state.agent.generate(prompt); // Use string prompt\n      return state;\n    }\n  };\n\n  builder\n    .addNode(createInputNode())\n    .addNode(errorAnalysis)\n    .addNode(solutionGen)\n    .addNode(explanation)\n    .addNode(createToolNode(createTestingTool(), 'Solution Validation'))\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-error', source: 'input', target: 'error-analysis', type: 'debug' as EdgeType }) // Cast type\n    .addEdge({ name: 'error-to-solution', source: 'error-analysis', target: 'solution-gen', type: 'debug' as EdgeType }) // Cast type\n    .addEdge({ name: 'solution-to-explanation', source: 'solution-gen', target: 'explanation', type: 'debug' as EdgeType }) // Cast type\n    .addEdge({ name: 'explanation-to-validation', source: 'explanation', target: 'tool-solution-validation', type: 'debug' as EdgeType }) // Cast type\n    .addEdge({ name: 'validation-to-output', source: 'tool-solution-validation', target: 'output', type: 'debug' as EdgeType }) // Cast type\n    ;\n\n  return builder.build();\n}\n\nexport function createChatWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Chat workflow requires a single Agent instance');\n  }\n\n  builder\n    .addNode(createInputNode())\n    .addNode(createAgentNode(_config.agent, 'Chat Agent')) // Now _config.agent is guaranteed to be Agent\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-agent', source: 'input', target: 'agent-chat-agent', type: 'chat' as EdgeType }) // Cast type\n    .addEdge({ name: 'agent-to-output', source: 'agent-chat-agent', target: 'output', type: 'chat' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createAskWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Ask workflow requires a single Agent instance');\n  }\n\n  const contextRetriever = createToolNode(createDocumentRetrievalTool(), 'Context Retriever');\n  const answerGenerator: GraphNode = {\n    id: 'answer-generator',\n    type: 'agent',\n    name: 'Answer Generator',\n    label: 'Answer Generator',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in answer-generator node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Generate answer based on context.\nContext: ${JSON.stringify(state.outputs['tool-context-retriever'])}\nQuery: ${state.messages.slice(-1)[0]?.content}`;\n\n      const answer = await state.agent.generate(prompt); // Use string prompt\n      state.answer = answer;\n      return state;\n    }\n  };\n\n\n  builder\n    .addNode(createInputNode())\n    .addNode(contextRetriever)\n    .addNode(answerGenerator)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-context', source: 'input', target: 'tool-context-retriever', type: 'ask' as EdgeType }) // Cast type\n    .addEdge({ name: 'context-to-answer', source: 'tool-context-retriever', target: 'answer-generator', type: 'ask' as EdgeType }) // Cast type\n    .addEdge({ name: 'answer-to-output', source: 'answer-generator', target: 'output', type: 'ask' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createEditWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  const codeAnalyzer = createToolNode(createCodeAnalysisTool(), 'Code Analyzer');\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Edit workflow requires a single Agent instance');\n  }\n  const editor: GraphNode = {\n    id: 'code-editor',\n    type: 'agent',\n    name: 'Code Editor',\n    label: 'Code Editor',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in code-editor node');\n      }\n      // Construct a string prompt using promptManager\n      const prompt = promptManager.renderPrompt('workflow.codeEditor', {\n        analysis: state.outputs['tool-code-analyzer'],\n        originalCode: state.messages.slice(-1)[0]?.content || ''\n      });\n\n      const editedCode = await state.agent.generate(prompt); // Use string prompt\n      state.editedCode = editedCode;\n      return state;\n    }\n  };\n\n\n  builder\n    .addNode(createInputNode())\n    .addNode(codeAnalyzer)\n    .addNode(editor)\n    .addNode(createToolNode(createTestingTool(), 'Edit Validator'))\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-analysis', source: 'input', target: 'tool-code-analyzer', type: 'edit' as EdgeType }) // Cast type\n    .addEdge({ name: 'analysis-to-edit', source: 'tool-code-analyzer', target: 'code-editor', type: 'edit' as EdgeType }) // Cast type\n    .addEdge({ name: 'edit-to-validation', source: 'code-editor', target: 'tool-edit-validator', type: 'edit' as EdgeType }) // Cast type\n    .addEdge({ name: 'validation-to-output', source: 'tool-edit-validator', target: 'output', type: 'edit' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createCodeGenWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  const specGenerator = createToolNode(createDocumentationTool(), 'Spec Generator');\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('CodeGen workflow requires a single Agent instance');\n  }\n  const codeGenerator: GraphNode = {\n    id: 'code-generator',\n    type: 'agent',\n    name: 'Code Generator',\n    label: 'Code Generator',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in code-generator node');\n      }\n      // Construct a string prompt using promptManager\n      const prompt = promptManager.renderPrompt('workflow.codeGenerator', {\n        specification: state.outputs['tool-spec-generator'],\n        requirements: state.messages.slice(-1)[0]?.content || ''\n      });\n\n      const generatedCode = await state.agent.generate(prompt); // Use string prompt\n      state.generatedCode = generatedCode;\n      return state;\n    }\n  };\n  const codeReviewer: GraphNode = {\n    id: 'code-reviewer',\n    type: 'agent',\n    name: 'Code Reviewer',\n    label: 'Code Reviewer',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in code-reviewer node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Review generated code.\nCode: ${state.generatedCode}\nSpecification: ${state.outputs['tool-spec-generator']}`;\n\n      const reviewFeedback = await state.agent.generate(prompt); // Use string prompt\n      state.reviewFeedback = reviewFeedback;\n      return state;\n    }\n  };\n\n\n  builder\n    .addNode(createInputNode())\n    .addNode(specGenerator)\n    .addNode(codeGenerator)\n    .addNode(codeReviewer)\n    .addNode(createToolNode(createTestingTool(), 'Generated Code Tests'))\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-spec', source: 'input', target: 'tool-spec-generator', type: 'codegen' as EdgeType }) // Cast type\n    .addEdge({ name: 'spec-to-code', source: 'tool-spec-generator', target: 'code-generator', type: 'codegen' as EdgeType }) // Cast type\n    .addEdge({ name: 'code-to-review', source: 'code-generator', target: 'code-reviewer', type: 'codegen' as EdgeType }) // Cast type\n    .addEdge({ name: 'review-to-test', source: 'code-reviewer', target: 'tool-generated-code-tests', type: 'codegen' as EdgeType }) // Cast type\n    .addEdge({ name: 'test-to-output', source: 'tool-generated-code-tests', target: 'output', type: 'codegen' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createAgenticWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Agentic workflow requires a single Agent instance');\n  }\n\n  const taskDefiner = createToolNode(createDocumentationTool(), 'Task Definer');\n  const agentExecutor: GraphNode = {\n    id: 'agent-executor',\n    type: 'agent',\n    name: 'Agent Executor',\n    label: 'Agent Executor',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in agent-executor node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Execute the defined task.\nTask Definition: ${state.outputs['tool-task-definer']}\nInput: ${state.messages.slice(-1)[0]?.content}`;\n\n      const executionResult = await state.agent.generate(prompt); // Use string prompt\n      state.executionResult = executionResult;\n      return state;\n    }\n  };\n\n\n  builder\n    .addNode(createInputNode())\n    .addNode(taskDefiner)\n    .addNode(agentExecutor)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-task', source: 'input', target: 'tool-task-definer', type: 'agentic' as EdgeType }) // Cast type\n    .addEdge({ name: 'task-to-execution', source: 'tool-task-definer', target: 'agent-executor', type: 'agentic' as EdgeType }) // Cast type\n    .addEdge({ name: 'execution-to-output', source: 'agent-executor', target: 'output', type: 'agentic' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createMultiAgentWorkflow(_config: any): GraphDefinition {\n  if (!(_config.agent instanceof MultiAgentSystem)) {\n    throw new Error('Multi-agent workflow requires a MultiAgentSystem instance');\n  }\n\n  const builder = new WorkflowBuilder(_config);\n\n  const supervisor = createAgentNode(_config.agent.supervisor, 'Supervisor');\n  const coordinator = createAgentNode(_config.agent.coordinator, 'Coordinator');\n  const specialist = createAgentNode(_config.agent.specialist, 'Specialist');\n  const executor = createAgentNode(_config.agent.executor, 'Executor');\n\n  builder\n    .addNode(createInputNode())\n    .addNode(supervisor)\n    .addNode(coordinator)\n    .addNode(specialist)\n    .addNode(executor)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-supervisor', source: 'input', target: 'agent-supervisor', type: 'multi-agent' })\n    .addEdge({ name: 'supervisor-to-coordinator', source: 'agent-supervisor', target: 'agent-coordinator', type: 'multi-agent' })\n    .addEdge({ name: 'coordinator-to-specialist', source: 'agent-coordinator', target: 'agent-specialist', type: 'multi-agent' })\n    .addEdge({ name: 'specialist-to-executor', source: 'agent-specialist', target: 'agent-executor', type: 'multi-agent' })\n    .addEdge({ name: 'executor-to-output', source: 'agent-executor', target: 'output', type: 'multi-agent' });\n\n  return builder.build();\n}\n\nexport function createUXDesignWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('UX Design workflow requires a single Agent instance');\n  }\n\n  const requirements = createToolNode(createDocumentationTool(), 'Requirements Gatherer', 'Requirements Gatherer');\n  const wireframe: GraphNode = {\n    id: 'wireframe-designer',\n    type: 'agent',\n    name: 'Wireframe Designer',\n    label: 'Wireframe Designer',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in wireframe-designer node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Design wireframes based on requirements.\nRequirements: ${state.outputs['tool-requirements-gatherer']}\nInput: ${state.messages.slice(-1)[0]?.content}`;\n\n      const wireframes = await state.agent.generate(prompt); // Use string prompt\n      state.wireframes = wireframes;\n      return state;\n    }\n  };\n  const prototype: GraphNode = {\n    id: 'prototype-builder',\n    type: 'agent',\n    name: 'Prototype Builder',\n    label: 'Prototype Builder',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in prototype-builder node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Build a prototype based on wireframes.\nWireframes: ${state.wireframes}\nInput: ${state.messages.slice(-1)[0]?.content}`;\n\n      const prototypeResult = await state.agent.generate(prompt); // Use string prompt\n      state.prototype = prototypeResult;\n      return state;\n    }\n  };\n  const implementation: GraphNode = {\n    id: 'ui-implementer',\n    type: 'agent',\n    name: 'UI Implementer',\n    label: 'UI Implementer',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in ui-implementer node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Implement UI based on prototype.\nPrototype: ${state.prototype}\nInput: ${state.messages.slice(-1)[0]?.content}`;\n\n      const implementedUI = await state.agent.generate(prompt); // Use string prompt\n      state.implementedUI = implementedUI;\n      return state;\n    }\n  };\n  const tester = createToolNode(createTestingTool(), 'UX Tester', 'UX Tester');\n\n  builder\n    .addNode(createInputNode())\n    .addNode(requirements)\n    .addNode(wireframe)\n    .addNode(prototype)\n    .addNode(implementation)\n    .addNode(tester)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-requirements', source: 'input', target: 'tool-requirements-gatherer', type: 'ux' as EdgeType }) // Cast type\n    .addEdge({ name: 'requirements-to-wireframe', source: 'input', target: 'wireframe-designer', type: 'ux' as EdgeType }) // Corrected source\n    .addEdge({ name: 'wireframe-to-prototype', source: 'wireframe-designer', target: 'prototype-builder', type: 'ux' as EdgeType }) // Cast type\n    .addEdge({ name: 'prototype-to-implementation', source: 'prototype-builder', target: 'ui-implementer', type: 'ux' as EdgeType }) // Cast type\n    .addEdge({ name: 'implementation-to-test', source: 'ui-implementer', target: 'tool-ux-tester', type: 'ux' as EdgeType }) // Cast type\n    .addEdge({ name: 'test-to-output', source: 'tool-ux-tester', target: 'output', type: 'ux' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createResearchWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Research workflow requires a single Agent instance');\n  }\n\n  const questionAnalyzer: GraphNode = {\n    id: 'question-analyzer',\n    type: 'agent',\n    name: 'Question Analyzer',\n    label: 'Question Analyzer',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in question-analyzer node');\n      }\n      // Construct a string prompt using promptManager\n      const prompt = promptManager.renderPrompt('workflow.questionAnalyzer', {\n        question: state.messages.slice(-1)[0]?.content || ''\n      });\n\n      const analyzedQuestion = await state.agent.generate(prompt); // Use string prompt\n      state.analyzedQuestion = analyzedQuestion;\n      return state;\n    }\n  };\n  const researchEngine = createToolNode(createDocumentRetrievalTool(), 'Research Engine');\n  const analysis: GraphNode = {\n    id: 'research-analyst',\n    type: 'agent',\n    name: 'Research Analyst',\n    label: 'Research Analyst',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in research-analyst node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Analyze research results.\nResearch Results: ${state.outputs['tool-research-engine']}\nAnalyzed Question: ${state.analyzedQuestion}`;\n\n      const analysisResult = await state.agent.generate(prompt); // Use string prompt\n      state.analysisResult = analysisResult;\n      return state;\n    }\n  };\n  const reportGenerator: GraphNode = {\n    id: 'report-generator',\n    type: 'agent',\n    name: 'Report Generator',\n    label: 'Report Generator',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in report-generator node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Generate a research report.\nAnalysis Results: ${state.analysisResult}\nInput: ${state.messages.slice(-1)[0]?.content}`;\n\n      const report = await state.agent.generate(prompt); // Use string prompt\n      state.report = report;\n      return state;\n    }\n  };\n\n  builder\n    .addNode(createInputNode())\n    .addNode(questionAnalyzer)\n    .addNode(researchEngine)\n    .addNode(analysis)\n    .addNode(reportGenerator)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-question', source: 'input', target: 'question-analyzer', type: 'research' as EdgeType }) // Cast type\n    .addEdge({ name: 'question-to-research', source: 'question-analyzer', target: 'tool-research-engine', type: 'research' as EdgeType }) // Cast type\n    .addEdge({ name: 'research-to-analysis', source: 'tool-research-engine', target: 'research-analyst', type: 'research' as EdgeType }) // Cast type\n    .addEdge({ name: 'analysis-to-report', source: 'research-analyst', target: 'report-generator', type: 'research' as EdgeType }) // Cast type\n    .addEdge({ name: 'report-to-output', source: 'report-generator', target: 'output', type: 'research' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createDevOpsWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder(_config);\n\n  // Check if _config.agent is a single Agent for this workflow (DevOps might not strictly require an Agent, but adding check for consistency)\n  if (!(_config.agent instanceof Agent)) {\n    // This workflow primarily uses tools, but if an agent node were added later, this check would be relevant.\n    // For now, we'll allow MultiAgentSystem as well, but log a warning if it's not a single Agent.\n    if (!(_config.agent instanceof MultiAgentSystem)) {\n      Logger.instance.warn('DevOps workflow configured with neither a single Agent nor a MultiAgentSystem.');\n    }\n  }\n\n  const codeCommit = createToolNode(createDocumentationTool(), 'Code Commit');\n  const build = createToolNode(createCI_CDTool(), 'Build System');\n  const test = createToolNode(createTestingTool(), 'Test Suite');\n  const deploy = createToolNode(createDeploymentTool(), 'Deployment');\n  const monitor = createToolNode(createMonitoringTool(), 'Monitoring');\n\n  builder\n    .addNode(createInputNode())\n    .addNode(codeCommit)\n    .addNode(build)\n    .addNode(test)\n    .addNode(deploy)\n    .addNode(monitor)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-commit', source: 'input', target: 'tool-code-commit', type: 'devops' as EdgeType }) // Cast type\n    .addEdge({ name: 'commit-to-build', source: 'tool-code-commit', target: 'tool-build-system', type: 'devops' as EdgeType }) // Cast type\n    .addEdge({ name: 'build-to-test', source: 'tool-build-system', target: 'tool-test-suite', type: 'devops' as EdgeType }) // Cast type\n    .addEdge({ name: 'test-to-deploy', source: 'tool-test-suite', target: 'tool-deployment', type: 'devops' as EdgeType }) // Cast type\n    .addEdge({ name: 'deploy-to-monitor', source: 'tool-deployment', target: 'tool-monitoring', type: 'devops' as EdgeType }) // Cast type\n    .addEdge({ name: 'monitor-to-output', source: 'tool-monitoring', target: 'output', type: 'devops' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createAgileWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder({\n    ..._config,\n    methodology: 'agile'\n  });\n\n  const backlog = createToolNode(createDocumentationTool(), 'Product Backlog');\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Agile workflow requires a single Agent instance');\n  }\n  const planning = createAgentNode(_config.agent, 'Sprint Planning');\n  const development = createAgentNode(_config.agent, 'Development');\n  const review = createAgentNode(_config.agent, 'Sprint Review');\n  const retrospective = createAgentNode(_config.agent, 'Retrospective');\n\n  builder\n    .addNode(createInputNode())\n    .addNode(backlog)\n    .addNode(planning)\n    .addNode(development)\n    .addNode(review)\n    .addNode(retrospective)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-backlog', source: 'input', target: 'tool-product-backlog', type: 'agile' })\n    .addEdge({ name: 'backlog-to-planning', source: 'tool-product-backlog', target: 'agent-sprint-planning', type: 'agile' })\n    .addEdge({ name: 'planning-to-development', source: 'agent-sprint-planning', target: 'agent-development', type: 'agile' })\n    .addEdge({ name: 'development-to-review', source: 'agent-development', target: 'agent-sprint-review', type: 'agile' })\n    .addEdge({ name: 'review-to-retrospective', source: 'agent-sprint-review', target: 'agent-retrospective', type: 'agile' })\n    .addEdge({ name: 'retrospective-to-output', source: 'agent-retrospective', target: 'output', type: 'agile' })\n    .addEdge({ name: 'retrospective-to-backlog', source: 'agent-retrospective', target: 'tool-product-backlog', type: 'feedback' });\n\n  return builder.build();\n}\n\nexport function createXPWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder({\n    ..._config,\n    methodology: 'xp'\n  });\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('XP workflow requires a single Agent instance');\n  }\n\n  const userStory = createToolNode(createDocumentationTool(), 'User Story');\n  const pairProgramming: GraphNode = {\n    id: 'pair-programming',\n    type: 'agent',\n    name: 'Pair Programming',\n    label: 'Pair Programming',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in pair-programming node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Perform pair programming on the user story.\nUser Story: ${state.outputs['tool-user-story']}\nInput: ${state.messages.slice(-1)[0]?.content}`;\n\n      const developedCode = await state.agent.generate(prompt); // Use string prompt\n      state.developedCode = developedCode;\n      return state;\n    }\n  };\n  const tdd: GraphNode = {\n    id: 'tdd-implementation',\n    type: 'agent',\n    name: 'TDD Implementation',\n    label: 'TDD Implementation',\n    agent: _config.agent, // Now _config.agent is guaranteed to be Agent\n    execute: async (state) => {\n      // Ensure agent is an Agent before calling generate\n      if (!state.agent || !(state.agent instanceof Agent)) {\n        throw new Error('Agent not available or not a single Agent instance in tdd-implementation node');\n      }\n      // Construct a string prompt\n      const prompt = `Task: Implement code using Test-Driven Development.\nDeveloped Code: ${state.developedCode}\nInput: ${state.messages.slice(-1)[0]?.content}`;\n\n      const tddCode = await state.agent.generate(prompt); // Use string prompt\n      state.tddCode = tddCode;\n      return state;\n    }\n  };\n  const continuousIntegration = createToolNode(createCI_CDTool(), 'CI Pipeline');\n  const customerFeedback = createToolNode(createDocumentationTool(), 'Customer Feedback');\n\n  builder\n    .addNode(createInputNode())\n    .addNode(userStory)\n    .addNode(pairProgramming)\n    .addNode(tdd)\n    .addNode(continuousIntegration)\n    .addNode(customerFeedback)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-story', source: 'input', target: 'tool-user-story', type: 'xp' as EdgeType }) // Cast type\n    .addEdge({ name: 'story-to-pairing', source: 'tool-user-story', target: 'pair-programming', type: 'xp' as EdgeType }) // Cast type\n    .addEdge({ name: 'pairing-to-tdd', source: 'pair-programming', target: 'tdd-implementation', type: 'xp' as EdgeType }) // Cast type\n    .addEdge({ name: 'tdd-to-ci', source: 'tdd-implementation', target: 'tool-ci-pipeline', type: 'xp' as EdgeType }) // Cast type\n    .addEdge({ name: 'ci-to-feedback', source: 'tool-ci-pipeline', target: 'tool-customer-feedback', type: 'xp' as EdgeType }) // Cast type\n    .addEdge({ name: 'feedback-to-output', source: 'tool-customer-feedback', target: 'output', type: 'xp' as EdgeType }) // Cast type\n    .addEdge({ name: 'feedback-to-story', source: 'tool-customer-feedback', target: 'tool-user-story', type: 'feedback' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\nexport function createScrumWorkflow(_config: any): GraphDefinition {\n  const builder = new WorkflowBuilder({\n    ..._config,\n    methodology: 'scrum'\n  });\n\n  // Check if _config.agent is a single Agent for this workflow\n  if (!(_config.agent instanceof Agent)) {\n    throw new Error('Scrum workflow requires a single Agent instance');\n  }\n\n  const productOwner = createAgentNode(_config.agent, 'Product Owner'); // Now _config.agent is guaranteed to be Agent\n  const scrumMaster = createAgentNode(_config.agent, 'Scrum Master'); // Now _config.agent is guaranteed to be Agent\n  const dailyScrum = createToolNode(createDocumentationTool(), 'Daily Scrum');\n  const sprint = createAgentNode(_config.agent, 'Sprint Execution'); // Now _config.agent is guaranteed to be Agent\n  const review = createAgentNode(_config.agent, 'Sprint Review'); // Now _config.agent is guaranteed to be Agent\n\n  builder\n    .addNode(createInputNode())\n    .addNode(productOwner)\n    .addNode(scrumMaster)\n    .addNode(dailyScrum)\n    .addNode(sprint)\n    .addNode(review)\n    .addNode(createOutputNode())\n    .addEdge({ name: 'input-to-po', source: 'input', target: 'agent-product-owner', type: 'scrum' as EdgeType }) // Cast type\n    .addEdge({ name: 'po-to-sm', source: 'agent-product-owner', target: 'agent-scrum-master', type: 'scrum' as EdgeType }) // Cast type\n    .addEdge({ name: 'sm-to-daily', source: 'agent-scrum-master', target: 'tool-daily-scrum', type: 'scrum' as EdgeType }) // Cast type\n    .addEdge({ name: 'daily-to-sprint', source: 'tool-daily-scrum', target: 'agent-sprint-execution', type: 'scrum' as EdgeType }) // Cast type\n    .addEdge({ name: 'sprint-to-review', source: 'agent-sprint-execution', target: 'agent-sprint-review', type: 'scrum' as EdgeType }) // Cast type\n    .addEdge({ name: 'review-to-output', source: 'agent-sprint-review', target: 'output', type: 'scrum' as EdgeType }); // Cast type\n\n  return builder.build();\n}\n\n// ==================== WORKFLOW FACTORY ====================\n\nexport class WorkflowFactory {\n  static create(_config: any): GraphDefinition {\n    switch (_config.type) {\n      case 'memory':\n        return createMemoryEnhancedWorkflow(_config);\n      case 'document-qa':\n        return createDocumentQAWorkflow(_config);\n      case 'refactoring':\n        return createCodeRefactoringWorkflow(_config);\n      case 'debugging':\n        return createDebuggingWorkflow(_config);\n      case 'chat':\n        return createChatWorkflow(_config);\n      case 'ask':\n        return createAskWorkflow(_config);\n      case 'edit':\n        return createEditWorkflow(_config);\n      case 'codegen':\n        return createCodeGenWorkflow(_config);\n      case 'agentic':\n        return createAgenticWorkflow(_config);\n      case 'multi-agent':\n        return createMultiAgentWorkflow(_config);\n      case 'ui-ux':\n        return createUXDesignWorkflow(_config);\n      case 'research':\n        return createResearchWorkflow(_config);\n      case 'devops':\n        return createDevOpsWorkflow(_config);\n      case 'agile':\n        return createAgileWorkflow(_config);\n      case 'xp':\n        return createXPWorkflow(_config);\n      case 'scrum':\n        return createScrumWorkflow(_config);\n      case 'pr-creation': {\n        // Import dynamically to avoid circular dependencies\n        const { createPRCreationWorkflow } = require('./prWorkflows');\n        return createPRCreationWorkflow(_config);\n      }\n      case 'pr-review': {\n        // Import dynamically to avoid circular dependencies\n        const { createPRReviewWorkflow } = require('./prWorkflows');\n        return createPRReviewWorkflow(_config);\n      }\n      case 'checkpoint': {\n        // Import dynamically to avoid circular dependencies\n        const { createCheckpointWorkflow } = require('./checkpointWorkflow');\n        return createCheckpointWorkflow(_config);\n      }\n      case 'mcp': {\n        // Import dynamically to avoid circular dependencies\n        const { createMCPWorkflow } = require('./mcpWorkflow');\n        return createMCPWorkflow(_config.agent, _config);\n      }\n      case 'pattern-refactoring': {\n        // Import dynamically to avoid circular dependencies\n        const { createPatternRefactoringWorkflow } = require('./advancedRefactoring');\n        return createPatternRefactoringWorkflow(_config.agent, _config);\n      }\n      case 'technical-debt': {\n        // Import dynamically to avoid circular dependencies\n        const { createTechnicalDebtWorkflow } = require('./advancedRefactoring');\n        return createTechnicalDebtWorkflow(_config.agent, _config);\n      }\n      default:\n        throw new Error(`Unknown workflow type: ${_config.type}`);\n    }\n  }\n}\n"]}