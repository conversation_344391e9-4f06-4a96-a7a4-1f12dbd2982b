"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedTerminalActions = void 0;
const vscode = __importStar(require("vscode"));
const codeActionProvider_1 = require("./codeActionProvider");
class EnhancedTerminalActions {
    _session;
    _terminalProvider;
    _actionHistory = [];
    _activeActions = new Map();
    _isEnabled = true;
    constructor(session) {
        this._session = session;
        this._terminalProvider = codeActionProvider_1.codeActionManager.getProvider('terminal')
            || new codeActionProvider_1.TerminalCodeActionProvider();
        this._initialize();
    }
    _initialize() {
        // Register command handlers for terminal actions
        const commands = [
            vscode.commands.registerCommand('codessa.installCommand', this._installCommand.bind(this)),
            vscode.commands.registerCommand('codessa.fixPackageError', this._fixPackageError.bind(this)),
            vscode.commands.registerCommand('codessa.fixPermissionError', this._fixPermissionError.bind(this)),
            vscode.commands.registerCommand('codessa.fixConnectionError', this._fixConnectionError.bind(this)),
            vscode.commands.registerCommand('codessa.runTerminalQuickFix', this._runTerminalQuickFix.bind(this)),
            vscode.commands.registerCommand('codessa.showTerminalActions', this._showTerminalActions.bind(this))
        ];
        // Store disposables for cleanup
        this._session['disposables'].push(...commands);
        // Set up event listeners
        this._setupEventListeners();
    }
    _setupEventListeners() {
        // Listen for terminal output to detect errors
        const outputListener = this._session.onData((output) => {
            if (this._isEnabled) {
                this._analyzeTerminalOutput(output);
            }
        });
        const errorListener = this._session.onError((error) => {
            this._handleTerminalError(error);
        });
        this._session['disposables'].push(outputListener, errorListener);
    }
    async _analyzeTerminalOutput(output) {
        try {
            const actions = this._terminalProvider.analyzeTerminalOutput(output);
            if (actions.length > 0) {
                // Register actions for later use
                actions.forEach((action, index) => {
                    const actionId = `terminal-action-${Date.now()}-${index}`;
                    this._activeActions.set(actionId, action);
                });
                // Show notification with quick fix option
                const showActions = await vscode.window.showInformationMessage(`Found ${actions.length} potential fix(es) for terminal output`, 'Show Quick Fixes', 'Apply Best Fix', 'Ignore');
                switch (showActions) {
                    case 'Show Quick Fixes':
                        await this._showTerminalActions();
                        break;
                    case 'Apply Best Fix':
                        await this._applyBestAction(actions);
                        break;
                }
            }
        }
        catch (error) {
            console.error('Error analyzing terminal output:', error);
        }
    }
    _handleTerminalError(error) {
        console.error('Terminal error detected:', error);
        // Create a generic error fix action
        const action = new vscode.CodeAction(`$(error) Fix terminal error: ${error.message}`, vscode.CodeActionKind.QuickFix);
        action.command = {
            command: 'codessa.runTerminalQuickFix',
            title: 'Run terminal error fix',
            arguments: [error.message]
        };
        const actionId = `terminal-error-${Date.now()}`;
        this._activeActions.set(actionId, action);
        vscode.window.showErrorMessage(`Terminal error detected: ${error.message}`, 'Show Fix').then(selection => {
            if (selection === 'Show Fix') {
                this._showTerminalActions();
            }
        });
    }
    async _installCommand(command, source) {
        try {
            let installCommand = '';
            // Determine the appropriate install command based on the system
            const platform = process.platform;
            if (platform === 'win32') {
                // Windows - try Chocolatey first, then scoop
                installCommand = `choco install ${command} || scoop install ${command}`;
            }
            else if (platform === 'darwin') {
                // macOS - try Homebrew
                installCommand = `brew install ${command}`;
            }
            else {
                // Linux - try apt, then yum, then snap
                installCommand = `sudo apt update && sudo apt install -y ${command} || sudo yum install -y ${command} || sudo snap install ${command}`;
            }
            await this._session.writeLn(installCommand);
            const result = {
                action: new vscode.CodeAction(`Install ${command}`),
                applied: true,
                timestamp: new Date(),
                output: `Installing ${command}...`
            };
            this._actionHistory.push(result);
            vscode.window.showInformationMessage(`Installing ${command}...`);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to install ${command}: ${error}`);
        }
    }
    async _fixPackageError(errorOutput) {
        try {
            // Common package error fixes
            if (errorOutput.includes('EACCES') || errorOutput.includes('permission')) {
                // Permission error - try with sudo or fix permissions
                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm');
                await this._session.writeLn('sudo chmod -R 755 ~/.npm');
            }
            else if (errorOutput.includes('ENOTFOUND')) {
                // Network error - check connection
                await this._session.writeLn('ping -c 4 registry.npmjs.org');
            }
            else if (errorOutput.includes('ETIMEDOUT')) {
                // Timeout - retry with different registry
                await this._session.writeLn('npm config set registry https://registry.npmjs.org/');
            }
            else {
                // Generic fix - clear cache and retry
                await this._session.writeLn('npm cache clean --force');
                await this._session.writeLn('rm -rf node_modules package-lock.json');
                await this._session.writeLn('npm install');
            }
            const result = {
                action: new vscode.CodeAction('Fix package error'),
                applied: true,
                timestamp: new Date(),
                output: 'Applied package error fix'
            };
            this._actionHistory.push(result);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to fix package error: ${error}`);
        }
    }
    async _fixPermissionError(errorOutput) {
        try {
            // Fix permission issues
            await this._session.writeLn('chmod +x $(pwd)/*');
            await this._session.writeLn('find . -type f -name "*.sh" -exec chmod +x {} \\;');
            // If it's a Node.js permission issue
            if (errorOutput.includes('node') || errorOutput.includes('npm')) {
                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm ~/.node-gyp');
            }
            const result = {
                action: new vscode.CodeAction('Fix permission error'),
                applied: true,
                timestamp: new Date(),
                output: 'Fixed permission issues'
            };
            this._actionHistory.push(result);
            vscode.window.showInformationMessage('Fixed permission issues');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to fix permissions: ${error}`);
        }
    }
    async _fixConnectionError(errorOutput) {
        try {
            // Diagnose and fix connection issues
            await this._session.writeLn('ping -c 4 8.8.8.8'); // Test basic connectivity
            await this._session.writeLn('nslookup google.com'); // Test DNS
            // Check proxy settings
            await this._session.writeLn('env | grep -i proxy');
            // If it's a specific service, try alternative endpoints
            if (errorOutput.includes('registry.npmjs.org')) {
                await this._session.writeLn('npm config set registry https://registry.npmmirror.com');
            }
            const result = {
                action: new vscode.CodeAction('Fix connection error'),
                applied: true,
                timestamp: new Date(),
                output: 'Diagnosed and attempted to fix connection issues'
            };
            this._actionHistory.push(result);
            vscode.window.showInformationMessage('Diagnosed connection issues');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to fix connection: ${error}`);
        }
    }
    async _runTerminalQuickFix(errorMessage) {
        try {
            // Generic terminal error fixes
            if (errorMessage.includes('command not found')) {
                const command = errorMessage.match(/command not found:?\s+(.+?)(?:\s|$)/i)?.[1];
                if (command) {
                    await this._installCommand(command, 'terminal');
                    return;
                }
            }
            // Run a generic diagnostic
            await this._session.writeLn('echo "Terminal diagnostic:"');
            await this._session.writeLn('pwd && ls -la');
            await this._session.writeLn('echo $PATH');
            await this._session.writeLn('which bash');
            vscode.window.showInformationMessage('Ran terminal diagnostic');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to run terminal fix: ${error}`);
        }
    }
    async _showTerminalActions() {
        const actions = Array.from(this._activeActions.values());
        if (actions.length === 0) {
            vscode.window.showInformationMessage('No terminal actions available');
            return;
        }
        const items = actions.map(action => ({
            label: action.title,
            description: action.kind?.value || 'Quick Fix',
            action: action
        }));
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select a terminal quick fix to apply'
        });
        if (selected) {
            await this._applyAction(selected.action);
        }
    }
    async _applyBestAction(actions) {
        if (actions.length === 0)
            return;
        // Apply the first (highest priority) action
        await this._applyAction(actions[0]);
    }
    async _applyAction(action) {
        try {
            const success = await this._terminalProvider.applyQuickFix(action, null);
            const result = {
                action,
                applied: success,
                timestamp: new Date(),
                output: success ? 'Action applied successfully' : 'Failed to apply action'
            };
            this._actionHistory.push(result);
            if (success) {
                vscode.window.showInformationMessage('Quick fix applied successfully');
            }
            else {
                vscode.window.showErrorMessage('Failed to apply quick fix');
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Error applying action: ${error}`);
        }
    }
    getActionHistory() {
        return [...this._actionHistory];
    }
    clearActionHistory() {
        this._actionHistory.length = 0;
    }
    enable() {
        this._isEnabled = true;
    }
    disable() {
        this._isEnabled = false;
    }
    isEnabled() {
        return this._isEnabled;
    }
    getActiveActions() {
        return Array.from(this._activeActions.values());
    }
    dispose() {
        this._activeActions.clear();
        this._actionHistory.length = 0;
    }
}
exports.EnhancedTerminalActions = EnhancedTerminalActions;
// Extend the EnhancedInteractiveSession class
const originalConstructor = global.EnhancedInteractiveSession;
if (originalConstructor) {
    const OriginalClass = originalConstructor;
    // Monkey patch to add Code Action support
    OriginalClass.prototype._terminalActions = null;
    OriginalClass.prototype.getTerminalActions = function () {
        if (!this._terminalActions) {
            this._terminalActions = new EnhancedTerminalActions(this);
        }
        return this._terminalActions;
    };
    OriginalClass.prototype.enableCodeActions = function () {
        const actions = this.getTerminalActions();
        actions.enable();
    };
    OriginalClass.prototype.disableCodeActions = function () {
        const actions = this.getTerminalActions();
        actions.disable();
    };
}
//# sourceMappingURL=enhancedTerminalActions.js.map