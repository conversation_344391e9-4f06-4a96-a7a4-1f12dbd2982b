{"version": 3, "file": "ttsSettingsSection.js", "sourceRoot": "", "sources": ["../../../src/ui/settings/ttsSettingsSection.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAA0E;AAE1E,MAAa,eAAe;IAKW;IAJ7B,MAAM,CAAC,QAAQ,CAAkB;IACjC,MAAM,CAAkC;IACxC,gBAAgB,CAAqB;IAE7C,YAAqC,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QACnE,IAAI,CAAC,gBAAgB,GAAG,gCAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,OAAgC;QACxD,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAEM,IAAI;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,aAAa,EACb,cAAc,EACd,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAC9B,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxD,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACvB,KAAK,gBAAgB;oBACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC7D,MAAM;gBACR,KAAK,eAAe;oBAClB,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACzD,MAAM;gBACR,KAAK,eAAe;oBAClB,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC5B,MAAM;YACR,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB;QACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;QACrD,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE3C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDA6E2C,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;wDAM/B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;kBAWzE,MAAM,CAAC,MAAM,CAAC,yBAAW,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;qCACxB,QAAQ,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;0BACxE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;iBAE7D,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;;;gFAMqD,QAAQ,CAAC,IAAI;mCAC1D,QAAQ,CAAC,IAAI;;;;;iFAKiC,QAAQ,CAAC,KAAK;oCAC3D,QAAQ,CAAC,KAAK;;;;;gFAK8B,QAAQ,CAAC,MAAM;qCAC1D,QAAQ,CAAC,MAAM,GAAG,GAAG;;;;;;;;;kBASxC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC;;;;;cAK5D,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;;;;;;;+BAOT,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA8C/C,CAAC;IACP,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,GAAG,EAA2B,CAAC;QAClD,MAAM,CAAC,MAAM,CAAC,yBAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5C,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,gBAAgB,CAAC,QAAqB,EAAE,aAAqB;QACnE,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;6BACF,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;kBACpE,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,QAAQ;;SAEtC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC;IAEO,aAAa,CAAC,MAAoC;QACxD,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE;YAC1C,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,IAAI,IAAI;;8BAEc,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;0BACxD,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;;;8CAGR,KAAK,CAAC,IAAI;4CACZ,KAAK,CAAC,QAAQ;sCACpB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,KAAK,CAAC,MAAM,UAAU,CAAC,CAAC,CAAC,EAAE;;8EAEZ,KAAK,CAAC,EAAE;;;;yBAI7D,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;iBAElB,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACvD,CAAC;IACH,CAAC;CACF;AAnSD,0CAmSC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { TTSSettings<PERSON>anager, TTS<PERSON>rovider, TTSVoice } from './ttsSettings';\n\nexport class TTSSettingsView {\n  private static instance: TTSSettingsView;\n  private _panel: vscode.WebviewPanel | undefined;\n  private _settingsManager: TTSSettingsManager;\n\n  private constructor(private readonly context: vscode.ExtensionContext) {\n    this._settingsManager = TTSSettingsManager.getInstance(context);\n  }\n\n  public static getInstance(context: vscode.ExtensionContext): TTSSettingsView {\n    if (!TTSSettingsView.instance) {\n      TTSSettingsView.instance = new TTSSettingsView(context);\n    }\n    return TTSSettingsView.instance;\n  }\n\n  public show() {\n    if (this._panel) {\n      this._panel.reveal();\n      return;\n    }\n\n    this._panel = vscode.window.createWebviewPanel(\n      'ttsSettings',\n      'TTS Settings',\n      vscode.ViewColumn.One,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true\n      }\n    );\n\n    this._panel.webview.html = this._getWebviewContent();\n    this._panel.onDidDispose(() => {\n      this._panel = undefined;\n    });\n\n    this._panel.webview.onDidReceiveMessage(async (message) => {\n      switch (message.type) {\n      case 'updateSettings':\n        await this._settingsManager.updateSettings(message.settings);\n        break;\n      case 'downloadVoice':\n        await this._settingsManager.downloadVoice(message.voice);\n        break;\n      case 'refreshVoices':\n        await this._refreshVoices();\n        break;\n      }\n    });\n  }\n\n  private _getWebviewContent(): string {\n    const settings = this._settingsManager.getSettings();\n    const voices = this._getVoicesByProvider();\n\n    return `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>TTS Settings</title>\n    <style>\n        body {\n            padding: 20px;\n            color: var(--vscode-foreground);\n            font-family: var(--vscode-font-family);\n        }\n        .section {\n            margin-bottom: 20px;\n            padding: 15px;\n            background: var(--vscode-editor-background);\n            border: 1px solid var(--vscode-panel-border);\n            border-radius: 4px;\n        }\n        .section-title {\n            margin: 0 0 10px 0;\n            font-size: 1.2em;\n            color: var(--vscode-titleBar-activeForeground);\n        }\n        .form-group {\n            margin-bottom: 15px;\n        }\n        label {\n            display: block;\n            margin-bottom: 5px;\n        }\n        select, input[type=\"range\"] {\n            width: 100%;\n            padding: 5px;\n            background: var(--vscode-input-background);\n            color: var(--vscode-input-foreground);\n            border: 1px solid var(--vscode-input-border);\n        }\n        .voice-list {\n            max-height: 200px;\n            overflow-y: auto;\n            border: 1px solid var(--vscode-panel-border);\n            padding: 10px;\n        }\n        .voice-item {\n            padding: 8px;\n            border-bottom: 1px solid var(--vscode-panel-border);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        .voice-item:last-child {\n            border-bottom: none;\n        }\n        .download-btn {\n            padding: 4px 8px;\n            background: var(--vscode-button-background);\n            color: var(--vscode-button-foreground);\n            border: none;\n            cursor: pointer;\n        }\n        .download-btn:hover {\n            background: var(--vscode-button-hoverBackground);\n        }\n        .provider-section {\n            margin-top: 10px;\n            padding: 10px;\n            background: var(--vscode-editor-inactiveSelectionBackground);\n            border-radius: 4px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"section\">\n        <h2 class=\"section-title\">General Settings</h2>\n        <div class=\"form-group\">\n            <label>\n                <input type=\"checkbox\" id=\"enabled\" ${settings.enabled ? 'checked' : ''}>\n                Enable TTS\n            </label>\n        </div>\n        <div class=\"form-group\">\n            <label>\n                <input type=\"checkbox\" id=\"autoSpeak\" ${settings.autoSpeak ? 'checked' : ''}>\n                Auto-speak assistant messages\n            </label>\n        </div>\n    </div>\n\n    <div class=\"section\">\n        <h2 class=\"section-title\">Voice Settings</h2>\n        <div class=\"form-group\">\n            <label for=\"provider\">TTS Provider</label>\n            <select id=\"provider\">\n                ${Object.values(TTSProvider).map(provider => `\n                    <option value=\"${provider}\" ${settings.provider === provider ? 'selected' : ''}>\n                        ${provider.charAt(0).toUpperCase() + provider.slice(1)}\n                    </option>\n                `).join('')}\n            </select>\n        </div>\n\n        <div class=\"form-group\">\n            <label for=\"rate\">Speech Rate</label>\n            <input type=\"range\" id=\"rate\" min=\"0.5\" max=\"2\" step=\"0.1\" value=\"${settings.rate}\">\n            <span id=\"rateValue\">${settings.rate}x</span>\n        </div>\n\n        <div class=\"form-group\">\n            <label for=\"pitch\">Pitch</label>\n            <input type=\"range\" id=\"pitch\" min=\"0.5\" max=\"2\" step=\"0.1\" value=\"${settings.pitch}\">\n            <span id=\"pitchValue\">${settings.pitch}</span>\n        </div>\n\n        <div class=\"form-group\">\n            <label for=\"volume\">Volume</label>\n            <input type=\"range\" id=\"volume\" min=\"0\" max=\"1\" step=\"0.1\" value=\"${settings.volume}\">\n            <span id=\"volumeValue\">${settings.volume * 100}%</span>\n        </div>\n    </div>\n\n    <div class=\"section\">\n        <h2 class=\"section-title\">Available Voices</h2>\n        <div class=\"form-group\">\n            <label for=\"voice\">Selected Voice</label>\n            <select id=\"voice\">\n                ${this._getVoiceOptions(settings.provider, settings.voice)}\n            </select>\n        </div>\n\n        <div class=\"voice-list\">\n            ${this._getVoiceList(voices)}\n        </div>\n    </div>\n\n    <script>\n        (function() {\n            const vscode = acquireVsCodeApi();\n            const settings = ${JSON.stringify(settings)};\n\n            function updateSettings() {\n                const newSettings = {\n                    enabled: document.getElementById('enabled').checked,\n                    provider: document.getElementById('provider').value,\n                    voice: document.getElementById('voice').value,\n                    rate: parseFloat(document.getElementById('rate').value),\n                    pitch: parseFloat(document.getElementById('pitch').value),\n                    volume: parseFloat(document.getElementById('volume').value),\n                    autoSpeak: document.getElementById('autoSpeak').checked\n                };\n                vscode.postMessage({ type: 'updateSettings', settings: newSettings });\n            }\n\n            // Event listeners\n            document.getElementById('enabled').addEventListener('change', updateSettings);\n            document.getElementById('autoSpeak').addEventListener('change', updateSettings);\n            document.getElementById('provider').addEventListener('change', () => {\n                vscode.postMessage({ type: 'refreshVoices' });\n                updateSettings();\n            });\n            document.getElementById('voice').addEventListener('change', updateSettings);\n            document.getElementById('rate').addEventListener('input', (e) => {\n                document.getElementById('rateValue').textContent = e.target.value + 'x';\n                updateSettings();\n            });\n            document.getElementById('pitch').addEventListener('input', (e) => {\n                document.getElementById('pitchValue').textContent = e.target.value;\n                updateSettings();\n            });\n            document.getElementById('volume').addEventListener('input', (e) => {\n                document.getElementById('volumeValue').textContent = (e.target.value * 100) + '%';\n                updateSettings();\n            });\n\n            // Download voice buttons\n            document.querySelectorAll('.download-btn').forEach(btn => {\n                btn.addEventListener('click', (e) => {\n                    const voiceId = e.target.dataset.voiceId;\n                    vscode.postMessage({ type: 'downloadVoice', voice: voiceId });\n                });\n            });\n        })();\n    </script>\n</body>\n</html>`;\n  }\n\n  private _getVoicesByProvider(): Map<TTSProvider, TTSVoice[]> {\n    const voices = new Map<TTSProvider, TTSVoice[]>();\n    Object.values(TTSProvider).forEach(provider => {\n      voices.set(provider, this._settingsManager.getVoices(provider));\n    });\n    return voices;\n  }\n\n  private _getVoiceOptions(provider: TTSProvider, selectedVoice: string): string {\n    const voices = this._settingsManager.getVoices(provider);\n    return voices.map(voice => `\n            <option value=\"${voice.id}\" ${voice.id === selectedVoice ? 'selected' : ''}>\n                ${voice.name} (${voice.language})\n            </option>\n        `).join('');\n  }\n\n  private _getVoiceList(voices: Map<TTSProvider, TTSVoice[]>): string {\n    let html = '';\n    voices.forEach((providerVoices, provider) => {\n      if (providerVoices.length > 0) {\n        html += `\n                    <div class=\"provider-section\">\n                        <h3>${provider.charAt(0).toUpperCase() + provider.slice(1)} Voices</h3>\n                        ${providerVoices.map(voice => `\n                            <div class=\"voice-item\">\n                                <div>\n                                    <strong>${voice.name}</strong>\n                                    <span>${voice.language}</span>\n                                    ${voice.gender ? `<span>(${voice.gender})</span>` : ''}\n                                </div>\n                                <button class=\"download-btn\" data-voice-id=\"${voice.id}\">\n                                    Download\n                                </button>\n                            </div>\n                        `).join('')}\n                    </div>\n                `;\n      }\n    });\n    return html;\n  }\n\n  private async _refreshVoices(): Promise<void> {\n    if (this._panel) {\n      this._panel.webview.html = this._getWebviewContent();\n    }\n  }\n} "]}