const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const SRC_DIR = path.join(__dirname, 'src');
const IGNORE_PATTERNS = [
  /\/node_modules\//,
  /\.test\.tsx?$/,
  /\.d\.ts$/,
  /\/__tests__\//,
  /\/__mocks__\//,
  /\/fixtures\//,
  /\/types\//,
  /\/interfaces\//
];

// Get all TypeScript/JavaScript files
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList);
    } else if (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js') || file.endsWith('.jsx')) {
      // <PERSON><PERSON> ignored patterns
      if (!IGNORE_PATTERNS.some(pattern => pattern.test(filePath))) {
        fileList.push(path.relative(process.cwd(), filePath));
      }
    }
  });
  
  return fileList;
}

// Get all imports in a file
function getImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    // Match import/require statements
    const importRegex = /(?:import|require\s*\(\s*['\"`]([^'\"`]+)['\"`]|from\s+['\"`]([^'\"`]+)['\"`])|import\s*['\"`]([^'\"`]+)['\"`])/g;
    const imports = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1] || match[2] || match[3];
      if (importPath && !importPath.startsWith('.')) continue; // Skip node_modules imports
      if (importPath) imports.push(importPath);
    }
    
    return imports;
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return [];
  }
}

// Resolve import path to actual file
function resolveImportPath(importPath, baseDir) {
  const extensions = ['', '.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx', '/index.js', '/index.jsx'];
  
  // Handle relative paths
  if (importPath.startsWith('.')) {
    const absPath = path.resolve(path.dirname(baseDir), importPath);
    
    for (const ext of extensions) {
      const fullPath = absPath + ext;
      if (fs.existsSync(fullPath)) {
        return path.relative(process.cwd(), fullPath);
      }
    }
    
    // Check for directory with index file
    for (const ext of extensions) {
      const fullPath = path.join(absPath, 'index' + ext);
      if (fs.existsSync(fullPath)) {
        return path.relative(process.cwd(), fullPath);
      }
    }
  }
  
  return null;
}

// Main function
async function findUnusedFiles() {
  console.log('Analyzing codebase for unused files...\n');
  
  // Get all TypeScript/JavaScript files
  const allFiles = getAllFiles(SRC_DIR);
  const fileUsage = new Map();
  
  // Initialize file usage map
  allFiles.forEach(file => {
    fileUsage.set(file, { references: [], isUsed: false });
  });
  
  // Analyze each file's imports
  allFiles.forEach(file => {
    const imports = getImports(file);
    
    imports.forEach(importPath => {
      const resolvedPath = resolveImportPath(importPath, file);
      
      if (resolvedPath && fileUsage.has(resolvedPath)) {
        const fileData = fileUsage.get(resolvedPath);
        fileData.references.push(file);
        fileData.isUsed = true;
      }
    });
  });
  
  // Mark entry points as used
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const entryPoints = [
    packageJson.main,
    ...(packageJson.bin ? Object.values(packageJson.bin) : [])
  ].filter(Boolean);
  
  entryPoints.forEach(entryPoint => {
    const entryPath = path.relative(process.cwd(), path.join('src', entryPoint));
    if (fileUsage.has(entryPath)) {
      fileUsage.get(entryPath).isUsed = true;
    }
  });
  
  // Find unused files
  const unusedFiles = [];
  const usedFiles = [];
  
  fileUsage.forEach((data, file) => {
    if (data.isUsed) {
      usedFiles.push({
        file,
        references: data.references
      });
    } else {
      unusedFiles.push(file);
    }
  });
  
  // Output results
  console.log(`\nFound ${unusedFiles.length} potentially unused files:\n`);
  unusedFiles.forEach(file => {
    console.log(`- ${file}`);
  });
  
  console.log('\nAnalysis complete.');
  
  return {
    unusedFiles,
    usedFiles: usedFiles.sort((a, b) => b.references.length - a.references.length)
  };
}

// Run the analysis
findUnusedFiles()
  .then(({ unusedFiles, usedFiles }) => {
    // Save results to files
    fs.writeFileSync(
      'unused-files.json',
      JSON.stringify(unusedFiles, null, 2)
    );
    
    fs.writeFileSync(
      'file-usage-stats.json',
      JSON.stringify(
        usedFiles.map(({ file, references }) => ({
          file,
          referenceCount: references.length,
          references: references.slice(0, 5) // Only include first 5 references to keep the output manageable
        })),
        null,
        2
      )
    );
    
    console.log('\nResults saved to unused-files.json and file-usage-stats.json');
  })
  .catch(error => {
    console.error('Error during analysis:', error);
    process.exit(1);
  });
