{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/types.ts"], "names": [], "mappings": ";AAAA;;;GAGG", "sourcesContent": ["/**\n * Comprehensive Types for Codessa Workflows\n * Properly separates operation modes from methodologies\n */\n\nimport { BaseMessage } from './corePolyfill';\nimport { Agent, AgentContext } from '../agentUtilities/agent';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n/**\n * Core Node Types\n */\nexport type NodeType =\n    | 'input'\n    | 'output'\n    | 'agent'\n    | 'tool'\n    | 'decision'\n    | 'process';\n\n/**\n * Node definition (extended for compatibility with graphTypes)\n */\nexport interface GraphNode {\n    id: string;\n    label: string; // Added for compatibility\n    type: NodeType;\n    name: string;\n    description?: string;\n    agent?: Agent;\n    tool?: ITool;\n    condition?: (_state: any) => Promise<string>;\n    config?: Record<string, any>;\n    metadata?: Record<string, any>;\n    execute?: (_state: any) => Promise<Partial<GraphState>> | Partial<GraphState>;\n}\n\n/**\n * Operation Modes - What the workflow does\n */\nexport type OperationMode =\n    | 'memory'             // Memory-enhanced operations\n    | 'document-qa'        // Document Q&A\n    | 'refactoring'        // Code refactoring\n    | 'debugging'          // Code debugging\n    | 'chat'               // Conversational chat\n    | 'ask'                // Question answering with context\n    | 'edit'               // Code editing\n    | 'codegen'            // Code generation\n    | 'agentic'            // Single agent tasks\n    | 'multi-agent'        // Multi-agent collaboration\n    | 'research'           // Research tasks\n    | 'ui-ux'              // UI/UX workflows\n    | 'documentation'      // Documentation generation\n    | 'pr-creation'        // PR creation workflow\n    | 'pr-review'          // PR review workflow\n    | 'checkpoint'         // Checkpoint system\n    | 'mcp'                // Model Context Protocol\n    | 'pattern-refactoring' // Pattern-based refactoring\n    | 'technical-debt'     // Technical debt reduction\n\n/**\n * SDLC Tasks - Development Lifecycle Tasks\n */\nexport type SDLCTask =\n    | 'planning'\n    | 'requirements'\n    | 'design'\n    | 'implementation'\n    | 'testing'\n    | 'deployment'\n    | 'maintenance';\n\n\n/**\n * Development Methodologies - How the workflow operates\n */\nexport type Methodology =\n    | 'agile'          // Agile methodology\n    | 'scrum'          // Scrum framework\n    | 'xp'             // Extreme Programming\n    | 'waterfall'      // Traditional sequential\n    | 'devops';        // DevOps practices\n\n/**\n * Workflow Type - High-level workflow categories\n */\nexport type WorkflowType = OperationMode | SDLCTask | Methodology | 'default';\n\n/**\n * Edge Relationship Types\n */\nexport type EdgeType =\n    // Core flow types\n    | 'success'        // Successful execution path\n    | 'failure'        // Failure/error path\n    | 'default'        // Default pathway\n\n    // Structural relationships\n    | 'composition'    // Whole-part relationship\n    | 'dependency'     // Dependency relationship\n    | 'association'    // General association\n\n    // Special workflow types\n    | 'feedback'       // Feedback loop\n    | 'validation'     // Validation pathway\n    | 'optimization'   // Optimization path\n    | 'integration'    // Integration point\n\n    // Control flow\n    | 'conditional'    // Conditional branching\n    | 'parallel'       // Parallel execution\n    | 'synchronization' // Synchronization point\n\n    // Operation modes\n    | 'memory'         // Memory-enhanced operations\n    | 'document-qa'    // Document Q&A\n    | 'refactor'       // Code refactoring\n    | 'debug'          // Code debugging\n    | 'chat'           // Conversational chat\n    | 'ask'            // Question answering with context\n    | 'edit'           // Code editing\n    | 'codegen'        // Code generation\n    | 'multi-agent'    // Multi-agent collaboration\n\n    // Methodologies\n    | 'agile'          // Agile methodology\n    | 'xp'             // Extreme Programming\n    | 'scrum'          // Scrum framework\n\n    // New workflow types\n    | 'pr-creation'        // PR creation workflow\n    | 'pr-review'          // PR review workflow\n    | 'checkpoint'         // Checkpoint system\n    | 'mcp'                // Model Context Protocol\n    | 'pattern-refactoring' // Pattern-based refactoring\n    | 'technical-debt';    // Technical debt reduction\n\n/**\n * Node definition\n */\nexport interface GraphNode {\n    id: string;\n    type: NodeType;\n    name: string;\n    description?: string;\n    agent?: Agent;                // For agent nodes\n    tool?: ITool;                 // For tool nodes\n    condition?: (_state: any) => Promise<string>; // Conditional branching\n    config?: Record<string, any>; // Node-specific configuration\n    metadata?: Record<string, any>; // Additional metadata\n    execute?: (_state: any) => Promise<Partial<GraphState>> | Partial<GraphState>;\n}\n\n/**\n * Edge definition\n */\nexport interface GraphEdge {\n    source: string;\n    target: string;\n    type: EdgeType;\n    name: string;\n    description?: string;\n    weight?: number;\n    priority?: number;\n    metadata?: Record<string, any>;\n    condition?: (_state: any) => boolean | Promise<boolean>;\n    conditionType?: 'routing_decision' | 'custom' | 'boolean';\n    traversalPolicy?: 'auto' | 'manual' | 'conditional';\n}\n\n/**\n * Workflow state definition\n */\nexport interface GraphState {\n    // Core state properties\n    messages: BaseMessage[];\n    inputs: Record<string, any>;\n    outputs: Record<string, any>;\n\n    // Execution context\n    currentNode: string;\n    previousNode?: string;\n    nextNodes?: string[];\n\n    // History tracking\n    history: Array<{\n        nodeId: string;\n        startTime: Date;\n        endTime?: Date;\n        duration?: number;\n        status?: 'success' | 'failure' | 'pending';\n        result?: any;\n        metrics?: Record<string, any>;\n    }>;\n\n    // Custom state extensions\n    [key: string]: any;\n}\n\n/**\n * Comprehensive workflow definition\n */\nexport interface GraphDefinition {\n    // Core identification\n    id: string;\n    name: string;\n    description: string;\n    version: string;\n\n    // Workflow characteristics\n    operationMode: OperationMode;\n    methodology?: Methodology;\n    domain?: string; // e.g. 'software-development', 'data-science'\n    type?: WorkflowType; // Workflow type for factory creation\n\n    // Graph structure\n    nodes: GraphNode[];\n    edges: GraphEdge[];\n    startNodeId: string;\n\n    // Codessa agent and tool nodes (optional for advanced workflows)\n    agentNodes?: GraphNode[];\n    toolNodes?: GraphNode[];\n\n    // Execution configuration\n    maxConcurrency?: number;\n    retryPolicy?: {\n        maxRetries: number;\n        backoffFactor: number;\n    };\n\n    // Metadata\n    tags?: string[];\n    metadata?: {\n        revolutionaryFeatures?: {\n            goddessMode?: boolean;\n            quantumAnalysis?: boolean;\n            neuralSynthesis?: boolean;\n            timeTravelDebugging?: boolean;\n            adaptivePersonality?: boolean;\n            parallelUniverseTesting?: boolean;\n            quantumEntanglement?: boolean;\n            consciousnessAnalysis?: boolean;\n            synapticLearning?: boolean;\n        };\n        enhancementLevel?: string;\n        aiCapabilities?: string[];\n        goddessBlessing?: string;\n        [key: string]: any;\n    };\n\n    // Lifecycle hooks\n    onStart?: (_state: any) => Promise<void>;\n    onComplete?: (_state: any) => Promise<void>;\n    onError?: (_error: Error, _state: any) => Promise<void>;\n}\n\n/**\n * Workflow execution options\n */\nexport interface GraphExecutionOptions {\n    // Execution limits\n    maxSteps?: number;\n    timeout?: number;\n\n    // Resource management\n    resourceLimits?: {\n        memoryMB?: number;\n        cpuUsage?: number;\n    };\n\n    // Monitoring and control\n    onProgress?: (_state: any) => void;\n    onNodeStart?: (_nodeId: string, _state: any) => void;\n    onNodeEnd?: (_nodeId: string, _state: any, _result: any) => void;\n    onEdgeTraversed?: (_edge: GraphEdge, _state: any) => void;\n\n    // Context and environment\n    context?: AgentContext;\n    environment?: 'planning' | 'design' | 'development' | 'testing' | 'staging' | 'production' | 'deployment' | 'maintenance';\n}\n\n/**\n * Workflow execution result\n */\nexport interface GraphExecutionResult {\n    success: boolean;\n    state: GraphState;\n    error?: Error;\n\n    // Performance metrics\n    metrics?: {\n        totalTime: number;\n        nodesExecuted: number;\n        averageNodeTime: number;\n        resourceUsage?: Record<string, any>;\n    };\n\n    // Validation information\n    validation?: {\n        status: 'valid' | 'invalid' | 'partial';\n        issues?: Array<{\n            nodeId?: string;\n            edgeId?: string;\n            message: string;\n            severity: 'warning' | 'error';\n        }>;\n    };\n}\n"]}