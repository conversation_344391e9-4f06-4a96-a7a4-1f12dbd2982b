{"version": 3, "file": "contextualInlineChat.js", "sourceRoot": "", "sources": ["../../../src/ui/inlineEditing/contextualInlineChat.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AAIjC,yCAAsC;AA+BtC,MAAa,oBAAoB;IACvB,eAAe,CAAkB;IACjC,aAAa,CAAgB;IAC7B,WAAW,CAAqB;IAChC,WAAW,GAAwB,EAAE,CAAC;IAE9C,qBAAqB;IACb,cAAc,GAAmC,IAAI,GAAG,EAAE,CAAC;IAC3D,gBAAgB,GAAkB,IAAI,CAAC;IAE/C,cAAc;IACN,gBAAgB,GAA+B,IAAI,CAAC;IACpD,aAAa,CAAuB;IAE5C,YACE,eAAgC,EAChC,aAA4B,EAC5B,WAA+B;QAE/B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC7F,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,oCAAoC,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,0BAA0B,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,+BAA+B,CAAC;QAC7D,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAE1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,gBAAgB;QACtB,gCAAgC;QAChC,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACrF,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAErC,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,IAAI,EAAE,EAAE;YAC3F,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExC,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,CAAC,OAAe,EAAE,EAAE;YAC9G,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;IAEO,mBAAmB;QACzB,uDAAuD;QACvD,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC,KAAK,CAAC,EAAE;YAC7E,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEzC,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;YACxE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExC,4BAA4B;QAC5B,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;YACxE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED;;SAEK;IACE,gBAAgB;QACrB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,eAAe,CAAC,QAA0B,EAAE,SAA4B;QACnF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,kCAAkC,CAAC,CAAC;YACrE,OAAO;QACT,CAAC;QAED,6DAA6D;QAC7D,MAAM,YAAY,GAAG,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;QACzD,MAAM,aAAa,GAAG,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC;QAEpD,qCAAqC;QACrC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QAE1F,qBAAqB;QACrB,MAAM,SAAS,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAsB;YACjC,EAAE,EAAE,SAAS;YACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO;YACP,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,uCAAuC;QACvC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE3B,eAAM,CAAC,IAAI,CAAC,+BAA+B,SAAS,gBAAgB,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;IACrH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,WAAW,CAAC,OAAe;QACtC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAiB,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,mBAAmB;QACnB,MAAM,WAAW,GAAsB;YACrC,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,EAAE,MAAM;YACZ,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE;gBACP,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;gBAClC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS;gBACpC,WAAW,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;aAC/F;SACF,CAAC;QAEF,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEnC,uBAAuB;QACvB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QACpF,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExC,YAAY;QACZ,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE3B,+BAA+B;QAC/B,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,yBAAyB,CACrC,OAA0B,EAC1B,WAA8B;QAE9B,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAEhE,4CAA4C;YAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBAC5C,MAAM;gBACN,IAAI;aACL,EAAE;gBACD,SAAS,EAAE;oBACT,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACpD,SAAS,EAAE;wBACT,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;wBACjE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;qBAC5B;oBACD,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBAC3G;aACF,CAAC,CAAC;YAEH,IAAI,eAAe,GAAG,MAAM,CAAC,MAAM,IAAI,kEAAkE,CAAC;YAE1G,2CAA2C;YAC3C,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,eAAe,EAAE;oBACjE,cAAc,EAAE,UAAU;oBAC1B,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;gBACH,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC;YACrC,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;gBACvB,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,eAAe;gBACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,OAAO,EAAE;oBACP,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;oBAClC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS;oBACpC,WAAW,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW;iBAC7C;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,KAAK,EAAE,CAAC,CAAC;YAChE,OAAO;gBACL,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;gBACvB,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,0FAA0F;gBACnG,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,OAAO,EAAE;oBACP,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;oBAClC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS;oBACpC,WAAW,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW;iBAC7C;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,qBAAqB,CAAC,OAA0B,EAAE,WAA8B;QACtF,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,MAAM,mBAAmB,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;QAEtF,OAAO;;;;UAID,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM;cACvB,OAAO,CAAC,QAAQ,CAAC,UAAU;mBACtB,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC;eACvE,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe;;;QAG3D,OAAO,CAAC,QAAQ,CAAC,UAAU;EACjC,OAAO,CAAC,eAAe;;;;EAIvB,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGxE,WAAW,CAAC,OAAO;;;;;;;;;;SAUZ,CAAC;IACR,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,OAAe;QAC9C,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACrG,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxG,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACtG,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/G,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACxE,OAAO,OAAO,CAAC,CAAC,qCAAqC;QACvD,CAAC;QAED,OAAO,MAAM,CAAC,CAAC,uBAAuB;IACxC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAC5B,QAA6B,EAC7B,QAAyB,EACzB,SAA2B;QAE3B,mEAAmE;QACnE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACrE,MAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvG,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAE3D,oCAAoC;QACpC,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACvD,KAAK,EAAE,QAAQ,QAAQ,CAAC,GAAG,CAAC,MAAM,SAAS,QAAQ,CAAC,IAAI,EAAE;gBAC1D,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE5E,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAE9D,YAAY,GAAG;gBACb,eAAe,EAAE,QAAQ;gBACzB,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB,EAAE,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;aACrF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,eAAe;YACf,YAAY;YACZ,mBAAmB,EAAE,EAAE;SACxB,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,QAA6B,EAAE,QAAyB;QACxF,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAClD,sCAAsC,EACtC,QAAQ,CAAC,GAAG,CACb,CAAC;YAEF,wBAAwB;YACxB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CACjD,6BAA6B,EAC7B,QAAQ,CAAC,GAAG,EACZ,QAAQ,CACT,CAAC;YAEF,6BAA6B;YAC7B,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CACtD,kCAAkC,EAClC,QAAQ,CAAC,GAAG,EACZ,QAAQ,CACT,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,2BAA2B;gBAChE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE;gBACjC,WAAW,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE;gBAC3C,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;aAClE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,QAA6B;QAC3D,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1E,IAAI,CAAC,eAAe;gBAAE,OAAO,EAAE,CAAC;YAEhC,oBAAoB;YACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAE3D,iCAAiC;YACjC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;YAE7E,OAAO;gBACL,eAAe,EAAE,eAAe,CAAC,IAAI;gBACrC,YAAY;gBACZ,gBAAgB;gBAChB,QAAQ,EAAE,QAAQ,CAAC,UAAU;gBAC7B,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;aAC1C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,wBAAwB,CACpC,QAA6B,EAC7B,QAAyB,EACzB,SAA2B;QAE3B,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;YACxD,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE1E,4BAA4B;YAC5B,IAAI,YAAY,EAAE,CAAC;gBACjB,WAAW,CAAC,IAAI,CAAC,uBAAuB,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC7E,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACvC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACvC,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACN,WAAW,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;gBACpD,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACtC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC1C,CAAC;YAED,gCAAgC;YAChC,IAAI,QAAQ,CAAC,UAAU,KAAK,YAAY,IAAI,QAAQ,CAAC,UAAU,KAAK,YAAY,EAAE,CAAC;gBACjF,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzC,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC7C,CAAC;iBAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;gBAC5C,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,CAAC;YAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yBAAyB;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;SAEK;IACG,cAAc;QACpB,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACtD,mBAAmB,EACnB,qBAAqB,EACrB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SAC9F,CACF,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE9D,+BAA+B;QAC/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;YAC1D,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,eAAe;QACrB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;SAEK;IACG,YAAY,CAAC,OAA0B;QAC7C,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO;QAEnC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC;YACxC,OAAO,EAAE,YAAY;YACrB,OAAO,EAAE;gBACP,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,OAAO,EAAE;oBACP,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBAC3C,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;oBAClC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU;iBAC9C;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,OAAY;QACvC,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,aAAa;gBAChB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAClC,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM;QACV,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iBAAiB;QACvB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA+IF,CAAC;IACR,CAAC;IAED,iBAAiB;IACT,wBAAwB,CAAC,QAA6B,EAAE,QAAyB;QACvF,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,OAA0B;QAChE,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClE,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;gBACjC,OAAO,EAAE,gBAAgB,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC;oBAClF,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM;oBAC7C,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;oBAClC,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB;iBAC5D,CAAC,EAAE;gBACJ,QAAQ,EAAE;oBACR,MAAM,EAAE,cAAqB;oBAC7B,IAAI,EAAE,cAAqB;oBAC3B,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;iBAC3E;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,2CAA2C,KAAK,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/D,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,KAA4C;QACpE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/D,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACtE,OAAO,CAAC,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC7D,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC;YACzD,CAAC;QACH,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,KAAqC;QAC7D,qCAAqC;QACrC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;YACnD,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAChD,+DAA+D;YACjE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,MAAqC;QACjE,IAAI,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpC,oEAAoE;QACtE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,OAAgC,EAAE,QAAyB;QACtF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,gDAAgD;gBAChD,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAC/E,OAAO,WAAW,IAAI,MAAM,CAAC;YAC/B,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,QAA6B;QAC1D,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1E,IAAI,CAAC,eAAe;gBAAE,OAAO,EAAE,CAAC;YAEhC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;YACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAEnD,8CAA8C;YAC9C,MAAM,QAAQ,GAAG;gBACf,GAAG,QAAQ,SAAS;gBACpB,GAAG,QAAQ,SAAS;gBACpB,GAAG,QAAQ,OAAO;gBAClB,GAAG,QAAQ,UAAU;aACtB,CAAC;YAEF,MAAM,YAAY,GAAa,EAAE,CAAC;YAClC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBACjE,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACjD,CAAC;YAED,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB,CAAC,eAAuC;QAC3E,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,YAAY,GAAG,CAAC,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YACjG,MAAM,UAAU,GAAa,EAAE,CAAC;YAEhC,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC5C,IAAI,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,IAAI,CAAC,EACjD,IAAI,EACJ,CAAC,CACF,CAAC;gBACF,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;YAED,OAAO;gBACL,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACtD,OAAO,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;aACtD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,eAAe,CAAC,QAA6B;QACnD,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAEnC,6BAA6B;QAC7B,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;YAAE,OAAO,OAAO,CAAC;QACzF,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,KAAK,CAAC;QACnF,IAAI,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,SAAS,CAAC;QAC/F,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YAAE,OAAO,SAAS,CAAC;QAC/F,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;YAAE,OAAO,OAAO,CAAC;QACvF,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;YAAE,OAAO,QAAQ,CAAC;QAE1F,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,eAAuC;QACpE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC/C,IAAI,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,6BAA6B,CAAC,EAC1E,IAAI,EACJ,CAAC,CACF,CAAC;YACF,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7B,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,eAAuC;QACpE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC/C,IAAI,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,0BAA0B,CAAC,EACvE,IAAI,EACJ,CAAC,CACF,CAAC;YACF,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7B,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;CACF;AAt2BD,oDAs2BC", "sourcesContent": ["/**\n * Contextual Inline Chat - Advanced inline chat that understands cursor context\n * \n * Provides intelligent inline chat that integrates with existing Codessa agents\n * and maintains context awareness throughout the conversation.\n */\n\nimport * as vscode from 'vscode';\nimport { SupervisorAgent } from '../../agents/agentTypes/supervisorAgent';\nimport { MemoryManager } from '../../memory/memoryManager';\nimport { GoddessModeManager } from '../../goddess/goddessMode';\nimport { logger } from '../../logger';\n\nexport interface InlineChatContext {\n  document: vscode.TextDocument;\n  position: vscode.Position;\n  selection: vscode.Selection;\n  surroundingCode: string;\n  semanticInfo: any;\n  conversationHistory: InlineChatMessage[];\n}\n\nexport interface InlineChatMessage {\n  id: string;\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: number;\n  context: {\n    position: vscode.Position;\n    selection: vscode.Selection;\n    codeSnippet: string;\n  };\n}\n\nexport interface InlineChatSession {\n  id: string;\n  startTime: number;\n  context: InlineChatContext;\n  messages: InlineChatMessage[];\n  isActive: boolean;\n}\n\nexport class ContextualInlineChat {\n  private supervisorAgent: SupervisorAgent;\n  private memoryManager: MemoryManager;\n  private goddessMode: GoddessModeManager;\n  private disposables: vscode.Disposable[] = [];\n\n  // Session management\n  private activeSessions: Map<string, InlineChatSession> = new Map();\n  private currentSessionId: string | null = null;\n\n  // UI elements\n  private inlineChatWidget: vscode.WebviewPanel | null = null;\n  private statusBarItem: vscode.StatusBarItem;\n\n  constructor(\n    supervisorAgent: SupervisorAgent,\n    memoryManager: MemoryManager,\n    goddessMode: GoddessModeManager\n  ) {\n    this.supervisorAgent = supervisorAgent;\n    this.memoryManager = memoryManager;\n    this.goddessMode = goddessMode;\n\n    this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);\n    this.statusBarItem.text = '$(comment-discussion) Codessa Chat';\n    this.statusBarItem.command = 'codessa.toggleInlineChat';\n    this.statusBarItem.tooltip = 'Toggle Contextual Inline Chat';\n    this.statusBarItem.show();\n\n    this.registerCommands();\n    this.setupEventListeners();\n  }\n\n  private registerCommands(): void {\n    // Register inline chat commands\n    const toggleCommand = vscode.commands.registerCommand('codessa.toggleInlineChat', () => {\n      this.toggleInlineChat();\n    });\n    this.disposables.push(toggleCommand);\n\n    const startChatCommand = vscode.commands.registerCommand('codessa.startInlineChat', (args) => {\n      this.startInlineChat(args?.position, args?.selection);\n    });\n    this.disposables.push(startChatCommand);\n\n    const sendMessageCommand = vscode.commands.registerCommand('codessa.sendInlineChatMessage', (message: string) => {\n      this.sendMessage(message);\n    });\n    this.disposables.push(sendMessageCommand);\n  }\n\n  private setupEventListeners(): void {\n    // Listen for cursor position changes to update context\n    const selectionListener = vscode.window.onDidChangeTextEditorSelection(event => {\n      this.updateChatContext(event);\n    });\n    this.disposables.push(selectionListener);\n\n    // Listen for document changes\n    const documentListener = vscode.workspace.onDidChangeTextDocument(event => {\n      this.onDocumentChanged(event);\n    });\n    this.disposables.push(documentListener);\n\n    // Listen for editor changes\n    const editorListener = vscode.window.onDidChangeActiveTextEditor(editor => {\n      this.onActiveEditorChanged(editor);\n    });\n    this.disposables.push(editorListener);\n  }\n\n  /**\n     * Toggle inline chat visibility\n     */\n  public toggleInlineChat(): void {\n    if (this.inlineChatWidget) {\n      this.closeInlineChat();\n    } else {\n      this.openInlineChat();\n    }\n  }\n\n  /**\n     * Start a new inline chat session\n     */\n  public async startInlineChat(position?: vscode.Position, selection?: vscode.Selection): Promise<void> {\n    const editor = vscode.window.activeTextEditor;\n    if (!editor) {\n      vscode.window.showWarningMessage('No active editor for inline chat');\n      return;\n    }\n\n    // Use provided position/selection or current cursor position\n    const chatPosition = position || editor.selection.active;\n    const chatSelection = selection || editor.selection;\n\n    // Build context for the chat session\n    const context = await this.buildChatContext(editor.document, chatPosition, chatSelection);\n\n    // Create new session\n    const sessionId = `session_${Date.now()}`;\n    const session: InlineChatSession = {\n      id: sessionId,\n      startTime: Date.now(),\n      context,\n      messages: [],\n      isActive: true\n    };\n\n    this.activeSessions.set(sessionId, session);\n    this.currentSessionId = sessionId;\n\n    // Open chat widget if not already open\n    if (!this.inlineChatWidget) {\n      this.openInlineChat();\n    }\n\n    // Update UI with new session\n    this.updateChatUI(session);\n\n    logger.info(`Started inline chat session ${sessionId} at position ${chatPosition.line}:${chatPosition.character}`);\n  }\n\n  /**\n     * Send a message in the current chat session\n     */\n  public async sendMessage(content: string): Promise<void> {\n    if (!this.currentSessionId) {\n      await this.startInlineChat();\n    }\n\n    const session = this.activeSessions.get(this.currentSessionId!);\n    if (!session) {\n      logger.error('No active chat session found');\n      return;\n    }\n\n    // Add user message\n    const userMessage: InlineChatMessage = {\n      id: `msg_${Date.now()}`,\n      role: 'user',\n      content,\n      timestamp: Date.now(),\n      context: {\n        position: session.context.position,\n        selection: session.context.selection,\n        codeSnippet: this.getCodeSnippetAtPosition(session.context.document, session.context.position)\n      }\n    };\n\n    session.messages.push(userMessage);\n\n    // Generate AI response\n    const assistantMessage = await this.generateAssistantResponse(session, userMessage);\n    session.messages.push(assistantMessage);\n\n    // Update UI\n    this.updateChatUI(session);\n\n    // Store conversation in memory\n    await this.storeConversationInMemory(session);\n  }\n\n  /**\n     * Generate AI response using existing Codessa agents\n     */\n  private async generateAssistantResponse(\n    session: InlineChatSession,\n    userMessage: InlineChatMessage\n  ): Promise<InlineChatMessage> {\n    try {\n      // Build comprehensive prompt with context\n      const prompt = this.buildContextualPrompt(session, userMessage);\n\n      // Use SupervisorAgent with appropriate mode\n      const mode = this.determineAppropriateMode(userMessage.content);\n\n      const result = await this.supervisorAgent.run({\n        prompt,\n        mode\n      }, {\n        workspace: {\n          currentFile: session.context.document.uri.toString(),\n          selection: {\n            text: session.context.document.getText(session.context.selection),\n            range: { start: 0, end: 0 }\n          },\n          workspaceFolders: Array.from(vscode.workspace.workspaceFolders ?? []).map(folder => folder.uri.toString())\n        }\n      });\n\n      let responseContent = result.output || 'I apologize, but I encountered an issue processing your request.';\n\n      // Apply Goddess Mode enhancement if active\n      if (this.goddessMode.isGoddessMode()) {\n        const enhanced = this.goddessMode.enhanceResponse(responseContent, {\n          taskComplexity: 'moderate',\n          userMood: 'focused'\n        });\n        responseContent = enhanced.content;\n      }\n\n      return {\n        id: `msg_${Date.now()}`,\n        role: 'assistant',\n        content: responseContent,\n        timestamp: Date.now(),\n        context: {\n          position: session.context.position,\n          selection: session.context.selection,\n          codeSnippet: userMessage.context.codeSnippet\n        }\n      };\n\n    } catch (error) {\n      logger.error(`Failed to generate assistant response: ${error}`);\n      return {\n        id: `msg_${Date.now()}`,\n        role: 'assistant',\n        content: 'I apologize, but I encountered an error while processing your request. Please try again.',\n        timestamp: Date.now(),\n        context: {\n          position: session.context.position,\n          selection: session.context.selection,\n          codeSnippet: userMessage.context.codeSnippet\n        }\n      };\n    }\n  }\n\n  /**\n     * Build contextual prompt for AI\n     */\n  private buildContextualPrompt(session: InlineChatSession, userMessage: InlineChatMessage): string {\n    const context = session.context;\n    const conversationHistory = session.messages.slice(-5); // Last 5 messages for context\n\n    return `\n# Contextual Inline Chat Request\n\n## Current Context:\n- File: ${context.document.uri.fsPath}\n- Language: ${context.document.languageId}\n- Position: Line ${context.position.line + 1}, Column ${context.position.character + 1}\n- Selection: ${context.selection.isEmpty ? 'None' : 'Text selected'}\n\n## Code Context:\n\\`\\`\\`${context.document.languageId}\n${context.surroundingCode}\n\\`\\`\\`\n\n## Conversation History:\n${conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\\n')}\n\n## Current User Message:\n${userMessage.content}\n\n## Instructions:\nYou are Codessa, an advanced AI coding assistant. Respond to the user's message with:\n1. Context awareness of the current code and cursor position\n2. Specific, actionable advice related to the code\n3. Code examples when appropriate\n4. Clear explanations that help the user understand\n\nKeep responses concise but helpful, focusing on the immediate context and user needs.\n        `;\n  }\n\n  /**\n     * Determine appropriate agent mode based on user message\n     */\n  private determineAppropriateMode(message: string): 'task' | 'chat' | 'inline' | 'ask' | 'debug' | 'edit' | 'agent' | 'multi-agent' | 'research' | 'documentation' | 'technical-debt' | 'ui-ux' | 'refactor' {\n    const messageLower = message.toLowerCase();\n\n    if (messageLower.includes('debug') || messageLower.includes('error') || messageLower.includes('bug')) {\n      return 'debug';\n    }\n    if (messageLower.includes('edit') || messageLower.includes('change') || messageLower.includes('modify')) {\n      return 'edit';\n    }\n    if (messageLower.includes('explain') || messageLower.includes('what') || messageLower.includes('how')) {\n      return 'ask';\n    }\n    if (messageLower.includes('refactor') || messageLower.includes('improve') || messageLower.includes('optimize')) {\n      return 'refactor';\n    }\n    if (messageLower.includes('test') || messageLower.includes('unit test')) {\n      return 'agent'; // Use agent mode for test generation\n    }\n\n    return 'chat'; // Default to chat mode\n  }\n\n  /**\n     * Build comprehensive chat context with enhanced Phase 3 features\n     */\n  private async buildChatContext(\n    document: vscode.TextDocument,\n    position: vscode.Position,\n    selection: vscode.Selection\n  ): Promise<InlineChatContext> {\n    // Enhanced surrounding code analysis (20 lines for better context)\n    const startLine = Math.max(0, position.line - 20);\n    const endLine = Math.min(document.lineCount - 1, position.line + 20);\n    const surroundingRange = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);\n    const surroundingCode = document.getText(surroundingRange);\n\n    // Get enhanced semantic information\n    let semanticInfo = {};\n    try {\n      // Use existing memory system to get relevant context\n      const memories = await this.memoryManager.searchMemories({\n        query: `file:${document.uri.fsPath} line:${position.line}`,\n        limit: 10\n      });\n\n      // Add code intelligence\n      const codeIntelligence = await this.getCodeIntelligence(document, position);\n\n      // Add project context\n      const projectContext = await this.getProjectContext(document);\n\n      semanticInfo = {\n        relatedMemories: memories,\n        codeIntelligence,\n        projectContext,\n        smartSuggestions: await this.generateSmartSuggestions(document, position, selection)\n      };\n    } catch (error) {\n      logger.warn(`Failed to get semantic info: ${error}`);\n    }\n\n    return {\n      document,\n      position,\n      selection,\n      surroundingCode,\n      semanticInfo,\n      conversationHistory: []\n    };\n  }\n\n  /**\n     * Get code intelligence information (Phase 3 enhancement)\n     */\n  private async getCodeIntelligence(document: vscode.TextDocument, position: vscode.Position): Promise<any> {\n    try {\n      // Get symbol information at cursor\n      const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(\n        'vscode.executeDocumentSymbolProvider',\n        document.uri\n      );\n\n      // Get hover information\n      const hovers = await vscode.commands.executeCommand<vscode.Hover[]>(\n        'vscode.executeHoverProvider',\n        document.uri,\n        position\n      );\n\n      // Get definition information\n      const definitions = await vscode.commands.executeCommand<vscode.Location[]>(\n        'vscode.executeDefinitionProvider',\n        document.uri,\n        position\n      );\n\n      return {\n        symbols: symbols?.slice(0, 5) || [], // Limit to 5 most relevant\n        hovers: hovers?.slice(0, 3) || [],\n        definitions: definitions?.slice(0, 3) || [],\n        currentSymbol: this.findSymbolAtPosition(symbols || [], position)\n      };\n    } catch (error) {\n      logger.warn('Failed to get code intelligence:', error);\n      return {};\n    }\n  }\n\n  /**\n     * Get project-wide context (Phase 3 enhancement)\n     */\n  private async getProjectContext(document: vscode.TextDocument): Promise<any> {\n    try {\n      const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);\n      if (!workspaceFolder) return {};\n\n      // Get related files\n      const relatedFiles = await this.findRelatedFiles(document);\n\n      // Get project structure insights\n      const projectStructure = await this.analyzeProjectStructure(workspaceFolder);\n\n      return {\n        workspaceFolder: workspaceFolder.name,\n        relatedFiles,\n        projectStructure,\n        language: document.languageId,\n        framework: this.detectFramework(document)\n      };\n    } catch (error) {\n      logger.warn('Failed to get project context:', error);\n      return {};\n    }\n  }\n\n  /**\n     * Generate smart suggestions based on context (Phase 3 feature)\n     */\n  private async generateSmartSuggestions(\n    document: vscode.TextDocument,\n    position: vscode.Position,\n    selection: vscode.Selection\n  ): Promise<string[]> {\n    const suggestions: string[] = [];\n\n    try {\n      const currentLine = document.lineAt(position.line).text;\n      const selectedText = selection.isEmpty ? '' : document.getText(selection);\n\n      // Context-aware suggestions\n      if (selectedText) {\n        suggestions.push(`Explain this code: \"${selectedText.substring(0, 50)}...\"`);\n        suggestions.push('Refactor this code');\n        suggestions.push('Add error handling');\n        suggestions.push('Generate tests for this');\n      } else {\n        suggestions.push('Explain what this function does');\n        suggestions.push('Add documentation');\n        suggestions.push('Suggest improvements');\n        suggestions.push('Find potential bugs');\n      }\n\n      // Language-specific suggestions\n      if (document.languageId === 'typescript' || document.languageId === 'javascript') {\n        suggestions.push('Add TypeScript types');\n        suggestions.push('Convert to async/await');\n      } else if (document.languageId === 'python') {\n        suggestions.push('Add type hints');\n        suggestions.push('Add docstring');\n      }\n\n      return suggestions.slice(0, 6); // Limit to 6 suggestions\n    } catch (error) {\n      logger.warn('Failed to generate smart suggestions:', error);\n      return ['Ask me anything about this code'];\n    }\n  }\n\n  /**\n     * Open inline chat widget\n     */\n  private openInlineChat(): void {\n    this.inlineChatWidget = vscode.window.createWebviewPanel(\n      'codessaInlineChat',\n      'Codessa Inline Chat',\n      vscode.ViewColumn.Beside,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true,\n        localResourceRoots: [vscode.Uri.joinPath(vscode.workspace.workspaceFolders![0].uri, 'media')]\n      }\n    );\n\n    this.inlineChatWidget.webview.html = this.getInlineChatHTML();\n\n    // Handle messages from webview\n    this.inlineChatWidget.webview.onDidReceiveMessage(message => {\n      this.handleWebviewMessage(message);\n    });\n\n    // Handle panel disposal\n    this.inlineChatWidget.onDidDispose(() => {\n      this.inlineChatWidget = null;\n    });\n  }\n\n  /**\n     * Close inline chat widget\n     */\n  private closeInlineChat(): void {\n    if (this.inlineChatWidget) {\n      this.inlineChatWidget.dispose();\n      this.inlineChatWidget = null;\n    }\n  }\n\n  /**\n     * Update chat UI with session data\n     */\n  private updateChatUI(session: InlineChatSession): void {\n    if (!this.inlineChatWidget) return;\n\n    this.inlineChatWidget.webview.postMessage({\n      command: 'updateChat',\n      session: {\n        id: session.id,\n        messages: session.messages,\n        context: {\n          fileName: session.context.document.fileName,\n          position: session.context.position,\n          language: session.context.document.languageId\n        }\n      }\n    });\n  }\n\n  /**\n     * Handle messages from webview\n     */\n  private handleWebviewMessage(message: any): void {\n    switch (message.command) {\n      case 'sendMessage':\n        this.sendMessage(message.content);\n        break;\n      case 'clearChat':\n        this.clearCurrentSession();\n        break;\n      case 'newSession':\n        this.startInlineChat();\n        break;\n    }\n  }\n\n  /**\n     * Get HTML for inline chat webview\n     */\n  private getInlineChatHTML(): string {\n    return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n            <meta charset=\"UTF-8\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n            <title>Codessa Inline Chat</title>\n            <style>\n                body { \n                    font-family: var(--vscode-font-family);\n                    background: var(--vscode-editor-background);\n                    color: var(--vscode-editor-foreground);\n                    margin: 0;\n                    padding: 10px;\n                }\n                .chat-container {\n                    display: flex;\n                    flex-direction: column;\n                    height: 100vh;\n                }\n                .chat-header {\n                    padding: 10px;\n                    border-bottom: 1px solid var(--vscode-panel-border);\n                    background: var(--vscode-panel-background);\n                }\n                .chat-messages {\n                    flex: 1;\n                    overflow-y: auto;\n                    padding: 10px;\n                }\n                .message {\n                    margin-bottom: 15px;\n                    padding: 8px 12px;\n                    border-radius: 8px;\n                }\n                .user-message {\n                    background: var(--vscode-button-background);\n                    color: var(--vscode-button-foreground);\n                    margin-left: 20px;\n                }\n                .assistant-message {\n                    background: var(--vscode-input-background);\n                    border: 1px solid var(--vscode-input-border);\n                    margin-right: 20px;\n                }\n                .chat-input {\n                    display: flex;\n                    padding: 10px;\n                    border-top: 1px solid var(--vscode-panel-border);\n                    background: var(--vscode-panel-background);\n                }\n                .chat-input input {\n                    flex: 1;\n                    padding: 8px;\n                    background: var(--vscode-input-background);\n                    color: var(--vscode-input-foreground);\n                    border: 1px solid var(--vscode-input-border);\n                    border-radius: 4px;\n                }\n                .chat-input button {\n                    margin-left: 8px;\n                    padding: 8px 16px;\n                    background: var(--vscode-button-background);\n                    color: var(--vscode-button-foreground);\n                    border: none;\n                    border-radius: 4px;\n                    cursor: pointer;\n                }\n                .context-info {\n                    font-size: 0.9em;\n                    color: var(--vscode-descriptionForeground);\n                    margin-bottom: 10px;\n                }\n            </style>\n        </head>\n        <body>\n            <div class=\"chat-container\">\n                <div class=\"chat-header\">\n                    <h3>Codessa Inline Chat</h3>\n                    <div class=\"context-info\" id=\"contextInfo\">Ready to help with your code</div>\n                </div>\n                <div class=\"chat-messages\" id=\"chatMessages\"></div>\n                <div class=\"chat-input\">\n                    <input type=\"text\" id=\"messageInput\" placeholder=\"Ask about your code...\" />\n                    <button onclick=\"sendMessage()\">Send</button>\n                    <button onclick=\"clearChat()\">Clear</button>\n                </div>\n            </div>\n            \n            <script>\n                const vscode = acquireVsCodeApi();\n                \n                function sendMessage() {\n                    const input = document.getElementById('messageInput');\n                    const message = input.value.trim();\n                    if (message) {\n                        vscode.postMessage({\n                            command: 'sendMessage',\n                            content: message\n                        });\n                        input.value = '';\n                    }\n                }\n                \n                function clearChat() {\n                    vscode.postMessage({ command: 'clearChat' });\n                }\n                \n                document.getElementById('messageInput').addEventListener('keypress', function(e) {\n                    if (e.key === 'Enter') {\n                        sendMessage();\n                    }\n                });\n                \n                window.addEventListener('message', event => {\n                    const message = event.data;\n                    if (message.command === 'updateChat') {\n                        updateChatDisplay(message.session);\n                    }\n                });\n                \n                function updateChatDisplay(session) {\n                    const messagesContainer = document.getElementById('chatMessages');\n                    const contextInfo = document.getElementById('contextInfo');\n                    \n                    // Update context info\n                    contextInfo.textContent = \\`\\${session.context.fileName} - Line \\${session.context.position.line + 1}\\`;\n                    \n                    // Update messages\n                    messagesContainer.innerHTML = '';\n                    session.messages.forEach(msg => {\n                        const messageDiv = document.createElement('div');\n                        messageDiv.className = \\`message \\${msg.role}-message\\`;\n                        messageDiv.textContent = msg.content;\n                        messagesContainer.appendChild(messageDiv);\n                    });\n                    \n                    // Scroll to bottom\n                    messagesContainer.scrollTop = messagesContainer.scrollHeight;\n                }\n            </script>\n        </body>\n        </html>\n        `;\n  }\n\n  // Helper methods\n  private getCodeSnippetAtPosition(document: vscode.TextDocument, position: vscode.Position): string {\n    const line = document.lineAt(position.line);\n    return line.text;\n  }\n\n  private async storeConversationInMemory(session: InlineChatSession): Promise<void> {\n    try {\n      const lastMessage = session.messages[session.messages.length - 1];\n      await this.memoryManager.addMemory({\n        content: `Inline chat: ${lastMessage.content.substring(0, 100)}\\n\\n${JSON.stringify({\n          sessionId: session.id,\n          filePath: session.context.document.uri.fsPath,\n          position: session.context.position,\n          conversation: session.messages.slice(-2) // Last 2 messages\n        })}`,\n        metadata: {\n          source: 'conversation' as any,\n          type: 'conversation' as any,\n          tags: ['inline-chat', 'conversation', session.context.document.languageId]\n        }\n      });\n    } catch (error) {\n      logger.warn(`Failed to store conversation in memory: ${error}`);\n    }\n  }\n\n  private clearCurrentSession(): void {\n    if (this.currentSessionId) {\n      const session = this.activeSessions.get(this.currentSessionId);\n      if (session) {\n        session.messages = [];\n        this.updateChatUI(session);\n      }\n    }\n  }\n\n  private updateChatContext(event: vscode.TextEditorSelectionChangeEvent): void {\n    if (this.currentSessionId) {\n      const session = this.activeSessions.get(this.currentSessionId);\n      if (session && session.context.document === event.textEditor.document) {\n        session.context.position = event.textEditor.selection.active;\n        session.context.selection = event.textEditor.selection;\n      }\n    }\n  }\n\n  private onDocumentChanged(event: vscode.TextDocumentChangeEvent): void {\n    // Update context for active sessions\n    for (const session of this.activeSessions.values()) {\n      if (session.context.document === event.document) {\n        // Context may have changed, could update surrounding code here\n      }\n    }\n  }\n\n  private onActiveEditorChanged(editor: vscode.TextEditor | undefined): void {\n    if (editor && this.currentSessionId) {\n      // Could start new session for new editor or update existing session\n    }\n  }\n\n  /**\n     * Find symbol at specific position\n     */\n  private findSymbolAtPosition(symbols: vscode.DocumentSymbol[], position: vscode.Position): vscode.DocumentSymbol | undefined {\n    for (const symbol of symbols) {\n      if (symbol.range.contains(position)) {\n        // Check children first for more specific symbol\n        const childSymbol = this.findSymbolAtPosition(symbol.children || [], position);\n        return childSymbol || symbol;\n      }\n    }\n    return undefined;\n  }\n\n  /**\n     * Find files related to current document\n     */\n  private async findRelatedFiles(document: vscode.TextDocument): Promise<string[]> {\n    try {\n      const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);\n      if (!workspaceFolder) return [];\n\n      const fileName = document.fileName;\n      const baseName = fileName.replace(/\\.[^/.]+$/, '');\n\n      // Look for related files (tests, types, etc.)\n      const patterns = [\n        `${baseName}.test.*`,\n        `${baseName}.spec.*`,\n        `${baseName}.d.ts`,\n        `${baseName}.types.*`\n      ];\n\n      const relatedFiles: string[] = [];\n      for (const pattern of patterns) {\n        const files = await vscode.workspace.findFiles(pattern, null, 5);\n        relatedFiles.push(...files.map(f => f.fsPath));\n      }\n\n      return relatedFiles.slice(0, 5);\n    } catch (error) {\n      logger.warn('Failed to find related files:', error);\n      return [];\n    }\n  }\n\n  /**\n     * Analyze project structure for context\n     */\n  private async analyzeProjectStructure(workspaceFolder: vscode.WorkspaceFolder): Promise<any> {\n    try {\n      // Look for common project files\n      const projectFiles = ['package.json', 'tsconfig.json', 'pyproject.toml', 'Cargo.toml', 'go.mod'];\n      const foundFiles: string[] = [];\n\n      for (const file of projectFiles) {\n        const files = await vscode.workspace.findFiles(\n          new vscode.RelativePattern(workspaceFolder, file),\n          null,\n          1\n        );\n        if (files.length > 0) {\n          foundFiles.push(file);\n        }\n      }\n\n      return {\n        projectFiles: foundFiles,\n        hasTests: await this.hasTestDirectory(workspaceFolder),\n        hasDocs: await this.hasDocsDirectory(workspaceFolder)\n      };\n    } catch (error) {\n      logger.warn('Failed to analyze project structure:', error);\n      return {};\n    }\n  }\n\n  /**\n     * Detect framework being used\n     */\n  private detectFramework(document: vscode.TextDocument): string | undefined {\n    const content = document.getText();\n\n    // Simple framework detection\n    if (content.includes('import React') || content.includes('from \"react\"')) return 'React';\n    if (content.includes('import Vue') || content.includes('from \"vue\"')) return 'Vue';\n    if (content.includes('import { Component }') && content.includes('@angular')) return 'Angular';\n    if (content.includes('import express') || content.includes('from \"express\"')) return 'Express';\n    if (content.includes('from flask') || content.includes('import flask')) return 'Flask';\n    if (content.includes('from django') || content.includes('import django')) return 'Django';\n\n    return undefined;\n  }\n\n  /**\n     * Check if workspace has test directory\n     */\n  private async hasTestDirectory(workspaceFolder: vscode.WorkspaceFolder): Promise<boolean> {\n    try {\n      const testDirs = await vscode.workspace.findFiles(\n        new vscode.RelativePattern(workspaceFolder, '{test,tests,__tests__,spec}'),\n        null,\n        1\n      );\n      return testDirs.length > 0;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n     * Check if workspace has docs directory\n     */\n  private async hasDocsDirectory(workspaceFolder: vscode.WorkspaceFolder): Promise<boolean> {\n    try {\n      const docsDirs = await vscode.workspace.findFiles(\n        new vscode.RelativePattern(workspaceFolder, '{docs,documentation,doc}'),\n        null,\n        1\n      );\n      return docsDirs.length > 0;\n    } catch {\n      return false;\n    }\n  }\n\n  dispose(): void {\n    this.disposables.forEach(d => d.dispose());\n    this.statusBarItem.dispose();\n    if (this.inlineChatWidget) {\n      this.inlineChatWidget.dispose();\n    }\n    this.activeSessions.clear();\n  }\n}\n"]}