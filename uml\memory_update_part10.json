{
  "_type": "UMLRealization",
  "_id": "AAAAAAGH1Rel1=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1MemoryVectorStore="
  },
  "target": {
    "$ref": "AAAAAAGH1IVectorStore="
  }
}

{
  "_type": "UMLRealization",
  "_id": "AAAAAAGH1Rel2=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1ChromaVectorStore="
  },
  "target": {
    "$ref": "AAAAAAGH1IVectorStore="
  }
}

{
  "_type": "UMLRealization",
  "_id": "AAAAAAGH1Rel3=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1PineconeVectorStore="
  },
  "target": {
    "$ref": "AAAAAAGH1IVectorStore="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel4=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1VectorStoreFactory="
  },
  "target": {
    "$ref": "AAAAAAGH1MemoryVectorStore="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel5=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1VectorStoreFactory="
  },
  "target": {
    "$ref": "AAAAAAGH1ChromaVectorStore="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel6=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1VectorStoreFactory="
  },
  "target": {
    "$ref": "AAAAAAGH1PineconeVectorStore="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel7=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1CodessaMemoryProvider="
  },
  "target": {
    "$ref": "AAAAAAGH1IVectorStore="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel8=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1CodessaMemoryProvider="
  },
  "target": {
    "$ref": "AAAAAAGH1IDatabase="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel9=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1CodessaMemoryProvider="
  },
  "target": {
    "$ref": "AAAAAAGH1VectorStoreFactory="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel10=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1CodessaMemoryProvider="
  },
  "target": {
    "$ref": "AAAAAAGH1DatabaseFactory="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel11=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1CodessaMemoryProvider="
  },
  "target": {
    "$ref": "AAAAAAGH1FileChunkingService="
  }
}

{
  "_type": "UMLDependency",
  "_id": "AAAAAAGH1Rel12=",
  "_parent": {
    "$ref": "AAAAAAFF+qBWK6M3Z8Y="
  },
  "source": {
    "$ref": "AAAAAAGH1CodessaGraphMemory="
  },
  "target": {
    "$ref": "AAAAAAGH1CodessaMemoryProvider="
  }
}