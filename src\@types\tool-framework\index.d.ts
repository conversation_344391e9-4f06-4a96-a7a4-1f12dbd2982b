// Type definitions for tool framework interfaces

declare module 'tool-framework' {
  export interface ITerminalToolEvents {
    on(event: 'operationStart', listener: (operationId: string, operation: string) => void): this;
    on(event: 'operationProgress', listener: (operationId: string, progress: number) => void): this;
    on(event: 'operationComplete', listener: (operationId: string, result: any) => void): this;
    on(event: 'operationError', listener: (operationId: string, error: Error) => void): this;
    
    emit(event: 'operationStart', operationId: string, operation: string): boolean;
    emit(event: 'operationProgress', operationId: string, progress: number): boolean;
    emit(event: 'operationComplete', operationId: string, result: any): boolean;
    emit(event: 'operationError', operationId: string, error: Error): boolean;
  }

  export interface IToolMemorySchema {
    type: string;
    source: string;
    metadata: {
      toolName?: string;
      version?: string;
      categories?: string[];
      [key: string]: any;
    };
  }

  export interface ILogger {
    info(message: string, ...meta: any[]): void;
    error(message: string, ...meta: any[]): void;
    warn(message: string, ...meta: any[]): void;
    debug(message: string, ...meta: any[]): void;
  }

  export interface IFileSystemManager {
    readFile(filePath: string, encoding?: string): Promise<string>;
    writeFile(filePath: string, content: string, options?: { encoding?: string; createParent?: boolean }): Promise<void>;
    exists(filePath: string): Promise<boolean>;
    mkdir(dirPath: string, options?: { recursive?: boolean }): Promise<void>;
    readdir(dirPath: string): Promise<string[]>;
    stat(filePath: string): Promise<{ isFile(): boolean; isDirectory(): boolean }>;
  }

  export interface IWorkspaceKnowledge {
    // Add any workspace knowledge related methods here
  }

  export interface IMemoryOperations {
    // Add memory operations methods here
  }

  export interface IAIContext {
    // Add AI context methods here
  }

  export interface IToolTerminalContext {
    // Add terminal context methods here
  }

  export interface InteractiveSession {
    // Add interactive session methods here
  }

  export enum MemoryType {
    CodeEdit = 'code_edit',
    // Add other memory types as needed
  }

  export enum MemorySource {
    Tool = 'tool',
    // Add other sources as needed
  }
}

// Global declarations
declare const AITerminalTool: any; // Or provide proper type definition if available
declare class AITerminalTool {
  constructor(options: { terminalSession: any; aiContext: any; toolContext: any });
  // Add other methods as needed
}
