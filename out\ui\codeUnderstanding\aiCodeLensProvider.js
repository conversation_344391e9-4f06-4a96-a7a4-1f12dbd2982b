"use strict";
/**
 * AI-Powered CodeLens Provider - Immersive code understanding
 *
 * Provides intelligent CodeLens insights directly in the editor using existing
 * Codessa agents and tools for deep code analysis.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AICodeLensProvider = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
const promptManager_1 = require("../../prompts/promptManager");
class AICodeLensProvider {
    supervisorAgent;
    toolRegistry;
    memoryManager;
    disposables = [];
    // Performance optimization
    analysisCache = new Map();
    cacheExpiry = 60000; // 1 minute
    maxCacheSize = 50;
    // Analysis state
    isAnalyzing = new Map();
    constructor(supervisorAgent, toolRegistry, memoryManager) {
        this.supervisorAgent = supervisorAgent;
        this.toolRegistry = toolRegistry;
        this.memoryManager = memoryManager;
        this.registerProvider();
        this.registerCommands();
    }
    registerProvider() {
        // Register for multiple languages
        const languages = ['typescript', 'javascript', 'python', 'java', 'csharp', 'cpp', 'go', 'rust'];
        for (const language of languages) {
            const provider = vscode.languages.registerCodeLensProvider({ language, scheme: 'file' }, this);
            this.disposables.push(provider);
        }
        logger_1.logger.info(`AI CodeLens provider registered for ${languages.length} languages`);
    }
    registerCommands() {
        // Register commands for CodeLens actions
        const explainCommand = vscode.commands.registerCommand('codessa.explainCode', (range, document) => {
            this.explainCode(range, document);
        });
        this.disposables.push(explainCommand);
        const optimizeCommand = vscode.commands.registerCommand('codessa.optimizeCode', (range, document) => {
            this.optimizeCode(range, document);
        });
        this.disposables.push(optimizeCommand);
        const generateTestsCommand = vscode.commands.registerCommand('codessa.generateTests', (range, document) => {
            this.generateTests(range, document);
        });
        this.disposables.push(generateTestsCommand);
        const refactorCommand = vscode.commands.registerCommand('codessa.refactorCode', (range, document) => {
            this.refactorCode(range, document);
        });
        this.disposables.push(refactorCommand);
        const documentCommand = vscode.commands.registerCommand('codessa.documentCode', (range, document) => {
            this.documentCode(range, document);
        });
        this.disposables.push(documentCommand);
        // Additional commands referenced by CodeLenses
        const showInsightCommand = vscode.commands.registerCommand('codessa.showInsight', async (insight, range, document) => {
            // Display a quick detail and offer to run the associated action
            const actionLabel = insight.actionCommand ? 'Run Action' : undefined;
            const selection = await vscode.window.showInformationMessage(`${insight.title}\n\n${insight.description}`, ...(actionLabel ? [actionLabel] : []));
            if (selection === 'Run Action' && insight.actionCommand) {
                await vscode.commands.executeCommand(insight.actionCommand, range, document);
            }
        });
        this.disposables.push(showInsightCommand);
        const showCodeAnalysisCommand = vscode.commands.registerCommand('codessa.showCodeAnalysis', async (analysis) => {
            // Open a virtual document with analysis details
            const detailLines = [];
            detailLines.push('# Code Analysis Summary');
            detailLines.push(`- Insights: ${analysis.insights.length}`);
            detailLines.push(`- Complexity: ${analysis.metrics.complexity}`);
            detailLines.push(`- Maintainability: ${analysis.metrics.maintainability}`);
            detailLines.push(`- Test Coverage: ${analysis.metrics.testCoverage}`);
            detailLines.push(`- Performance: ${analysis.metrics.performance}`);
            detailLines.push('\n## Insights');
            for (const ins of analysis.insights) {
                detailLines.push(`- [${ins.severity}] (${ins.type}) ${ins.title} - ${ins.description}`);
            }
            const doc = await vscode.workspace.openTextDocument({ language: 'markdown', content: detailLines.join('\n') });
            await vscode.window.showTextDocument(doc, { preview: true });
        });
        this.disposables.push(showCodeAnalysisCommand);
        const showSecurityIssuesCommand = vscode.commands.registerCommand('codessa.showSecurityIssues', async (range, document) => {
            const activeDoc = document ?? vscode.window.activeTextEditor?.document;
            if (!activeDoc)
                return;
            const securityTool = this.toolRegistry.getTool('securityScan');
            if (!securityTool) {
                vscode.window.showWarningMessage('Security scan tool is not available.');
                return;
            }
            const result = await securityTool.execute('scanFile', { filePath: activeDoc.uri.fsPath });
            const issuesArray = result.output.issues;
            if (result.success && result.output && Array.isArray(issuesArray)) {
                const issues = issuesArray;
                const lines = ['# Security Issues', ...issues.map((i, idx) => `${idx + 1}. ${i.message}`)];
                const doc = await vscode.workspace.openTextDocument({ language: 'markdown', content: lines.join('\n') });
                await vscode.window.showTextDocument(doc, { preview: true });
            }
            else {
                vscode.window.showInformationMessage('No security issues found.');
            }
        });
        this.disposables.push(showSecurityIssuesCommand);
        const fixSecurityCommand = vscode.commands.registerCommand('codessa.fixSecurity', async (range, document) => {
            const activeDoc = document ?? vscode.window.activeTextEditor?.document;
            if (!activeDoc)
                return;
            const code = range ? activeDoc.getText(range) : activeDoc.getText();
            const result = await this.supervisorAgent.run({
                prompt: `Identify and fix security vulnerabilities in this code. Provide fixed code only.\n\n\`\`\`${activeDoc.languageId}\n${code}\n\`\`\``,
                mode: 'refactor'
            });
            if (result.success && result.output) {
                const edit = new vscode.WorkspaceEdit();
                const targetRange = range ?? new vscode.Range(0, 0, activeDoc.lineCount, 0);
                edit.replace(activeDoc.uri, targetRange, result.output);
                await vscode.workspace.applyEdit(edit);
                vscode.window.showInformationMessage('Applied security fixes.');
            }
        });
        this.disposables.push(fixSecurityCommand);
    }
    /**
       * Main CodeLens provider method
       */
    async provideCodeLenses(document, token) {
        try {
            // Check if we should provide CodeLenses for this document
            if (!this.shouldProvideCodeLenses(document)) {
                return [];
            }
            // Get or perform code analysis
            const analysisResult = await this.getCodeAnalysis(document, token);
            if (!analysisResult) {
                return [];
            }
            // Convert insights to CodeLenses
            const codeLenses = this.convertInsightsToCodeLenses(analysisResult, document);
            logger_1.logger.info(`Provided ${codeLenses.length} CodeLenses for ${document.fileName}`);
            return codeLenses;
        }
        catch (error) {
            logger_1.logger.error(`CodeLens provider error: ${error}`);
            return [];
        }
    }
    /**
       * Resolve CodeLens with additional information
       */
    async resolveCodeLens(codeLens, token) {
        if (token.isCancellationRequested) {
            return codeLens;
        }
        const aiCodeLens = codeLens;
        // Add confidence indicator to title
        const confidenceIndicator = this.getConfidenceIndicator(aiCodeLens.confidence);
        aiCodeLens.command = {
            title: `${confidenceIndicator} ${aiCodeLens.insight.title}`,
            command: aiCodeLens.insight.actionCommand || 'codessa.showInsight',
            arguments: [aiCodeLens.insight, aiCodeLens.range]
        };
        return aiCodeLens;
    }
    /**
       * Determine if we should provide CodeLenses for this document
       */
    shouldProvideCodeLenses(document) {
        // Skip for very large files (performance)
        if (document.lineCount > 1000) {
            return false;
        }
        // Skip for certain file types
        const skipExtensions = ['.md', '.txt', '.json', '.xml'];
        const fileExtension = document.fileName.toLowerCase().substring(document.fileName.lastIndexOf('.'));
        if (skipExtensions.includes(fileExtension)) {
            return false;
        }
        return true;
    }
    /**
       * Get or perform code analysis
       */
    async getCodeAnalysis(document, token) {
        const cacheKey = `${document.uri.fsPath}_${document.version}`;
        // Check cache first
        const cached = this.getCachedAnalysis(cacheKey);
        if (cached) {
            return cached;
        }
        // Check if already analyzing
        if (this.isAnalyzing.get(cacheKey)) {
            return null;
        }
        this.isAnalyzing.set(cacheKey, true);
        try {
            if (token.isCancellationRequested) {
                return null;
            }
            const analysisResult = await this.performCodeAnalysis(document, token);
            // Cache the result
            this.setCachedAnalysis(cacheKey, analysisResult);
            return analysisResult;
        }
        finally {
            this.isAnalyzing.delete(cacheKey);
        }
    }
    /**
       * Perform comprehensive code analysis using existing tools
       */
    async performCodeAnalysis(document, _token) {
        const insights = [];
        const metrics = {
            complexity: 0,
            maintainability: 0,
            testCoverage: 0,
            performance: 0
        };
        const suggestions = [];
        try {
            // Use existing code analysis tools
            const complexityTool = this.toolRegistry.getTool('codeComplexity');
            if (complexityTool) {
                const complexityResult = await complexityTool.execute('analyzeFile', {
                    filePath: document.uri.fsPath,
                    content: document.getText()
                });
                if (complexityResult.success && complexityResult.output) {
                    const complexity = complexityResult.output;
                    metrics.complexity = complexity.cyclomaticComplexity || 0;
                    if (typeof complexity.maintainabilityIndex === 'number') {
                        metrics.maintainability = complexity.maintainabilityIndex;
                    }
                    else {
                        // Heuristic maintainability if not provided: inverse of complexity
                        metrics.maintainability = Math.max(0, 100 - metrics.complexity * 5);
                    }
                    // Add complexity insights
                    if (metrics.complexity > 10) {
                        insights.push({
                            type: 'complexity',
                            title: `High Complexity (${metrics.complexity})`,
                            description: 'This function has high cyclomatic complexity and may be difficult to maintain',
                            severity: 'warning',
                            actionCommand: 'codessa.refactorCode'
                        });
                        suggestions.push('Consider breaking complex functions into smaller, testable units.');
                    }
                }
            }
            // Security analysis
            const securityTool = this.toolRegistry.getTool('securityScan');
            if (securityTool) {
                const securityResult = await securityTool.execute('scanFile', {
                    filePath: document.uri.fsPath
                });
                if (securityResult.success && securityResult.output) {
                    const vulnerabilities = securityResult.output;
                    if (vulnerabilities.issues && vulnerabilities.issues.length > 0) {
                        insights.push({
                            type: 'security',
                            title: `${vulnerabilities.issues.length} Security Issue(s)`,
                            description: 'Potential security vulnerabilities detected',
                            severity: 'error',
                            actionCommand: 'codessa.showSecurityIssues'
                        });
                        suggestions.push('Review security issues and apply recommended fixes.');
                    }
                }
            }
            // Performance analysis using AI
            if (!(_token && _token.isCancellationRequested)) {
                const performanceInsights = await this.analyzePerformance(document);
                insights.push(...performanceInsights);
                // Simple performance metric: number of performance insights
                metrics.performance = performanceInsights.length;
            }
            // Documentation analysis
            if (!(_token && _token.isCancellationRequested)) {
                const documentationInsights = await this.analyzeDocumentation(document);
                insights.push(...documentationInsights);
            }
            // Testing analysis
            if (!(_token && _token.isCancellationRequested)) {
                const testingInsights = await this.analyzeTestCoverage(document);
                insights.push(...testingInsights);
                // Heuristic test coverage metric
                metrics.testCoverage = testingInsights.length === 0 ? 80 : 40;
            }
        }
        catch (error) {
            logger_1.logger.error(`Code analysis failed: ${error}`);
        }
        const result = { insights, metrics, suggestions };
        // Persist a concise record to memory for later retrieval/search
        try {
            await this.memoryManager.addMemory({
                content: `Code analysis for ${document.fileName}: ${insights.length} insights. Complexity ${metrics.complexity}, Maintainability ${metrics.maintainability}, Performance ${metrics.performance}, TestCoverage ${metrics.testCoverage}.`,
                metadata: {
                    source: 'code',
                    type: 'insight',
                    tags: ['code', 'analysis', document.languageId]
                }
            });
        }
        catch (e) {
            logger_1.logger.debug('Failed to persist analysis memory (non-critical):', e);
        }
        return result;
    }
    /**
       * Analyze performance using AI
       */
    async analyzePerformance(document) {
        const insights = [];
        try {
            const prompt = promptManager_1.promptManager.renderPrompt('analysis.performance', {
                codeContent: document.getText(),
                languageId: document.languageId
            });
            const result = await this.supervisorAgent.run({
                prompt,
                mode: 'ask'
            });
            if (result.success && typeof result.output === 'string') {
                const parsed = this.parseAIInsights(result.output, 'performance');
                insights.push(...parsed);
            }
        }
        catch (error) {
            logger_1.logger.warn(`Performance analysis failed: ${error}`);
        }
        return insights;
    }
    /**
       * Analyze documentation coverage
       */
    async analyzeDocumentation(document) {
        const insights = [];
        const text = document.getText();
        // Simple heuristic: check for functions without documentation
        const functionRegex = /(?:function|def|class|interface|type)\s+(\w+)/g;
        const docRegex = /\/\*\*[\s\S]*?\*\/|#\s*.*|"""[\s\S]*?"""/g;
        const functions = Array.from(text.matchAll(functionRegex));
        const docs = Array.from(text.matchAll(docRegex));
        if (functions.length > 0 && docs.length < functions.length * 0.5) {
            insights.push({
                type: 'documentation',
                title: 'Missing Documentation',
                description: `${functions.length - docs.length} functions/classes lack documentation`,
                severity: 'suggestion',
                actionCommand: 'codessa.documentCode'
            });
        }
        return insights;
    }
    /**
       * Analyze test coverage
       */
    async analyzeTestCoverage(document) {
        const insights = [];
        // Check if this is a test file
        const isTestFile = document.fileName.includes('.test.') ||
            document.fileName.includes('.spec.') ||
            document.fileName.includes('test/') ||
            document.fileName.includes('tests/');
        if (!isTestFile) {
            // Check if corresponding test file exists
            const testFileExists = await this.checkTestFileExists(document.uri.fsPath);
            if (!testFileExists) {
                insights.push({
                    type: 'testing',
                    title: 'No Tests Found',
                    description: 'No corresponding test file found for this module',
                    severity: 'suggestion',
                    actionCommand: 'codessa.generateTests'
                });
            }
        }
        return insights;
    }
    /**
       * Convert insights to CodeLenses
       */
    convertInsightsToCodeLenses(analysisResult, document) {
        const codeLenses = [];
        // Add insights as CodeLenses
        for (const insight of analysisResult.insights) {
            // Determine position for CodeLens
            let range = new vscode.Range(0, 0, 0, 0); // Default to top of file
            // Try to find relevant line for the insight
            if (insight.type === 'complexity' || insight.type === 'performance') {
                // Find function definitions
                const functionMatch = document.getText().match(/(?:function|def|class)\s+\w+/);
                if (functionMatch) {
                    const position = document.positionAt(document.getText().indexOf(functionMatch[0]));
                    range = new vscode.Range(position, position);
                }
            }
            const codeLens = {
                range,
                insight,
                confidence: this.calculateInsightConfidence(insight),
                actionable: !!insight.actionCommand,
                isResolved: true,
                command: {
                    title: insight.title,
                    command: insight.actionCommand || 'codessa.showInsight',
                    arguments: [insight, range, document]
                }
            };
            codeLenses.push(codeLens);
        }
        // Add summary CodeLens at the top
        if (analysisResult.insights.length > 0) {
            const summaryCodeLens = {
                range: new vscode.Range(0, 0, 0, 0),
                insight: {
                    type: 'explanation',
                    title: `📊 Code Analysis: ${analysisResult.insights.length} insights`,
                    description: 'Click to view detailed code analysis',
                    severity: 'info',
                    actionCommand: 'codessa.showCodeAnalysis'
                },
                confidence: 1.0,
                actionable: true,
                isResolved: true,
                command: {
                    title: `📊 Code Analysis: ${analysisResult.insights.length} insights`,
                    command: 'codessa.showCodeAnalysis',
                    arguments: [analysisResult, document]
                }
            };
            codeLenses.unshift(summaryCodeLens);
        }
        return codeLenses;
    }
    /**
       * Parse AI insights from response
       */
    parseAIInsights(response, type) {
        const insights = [];
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                if (parsed.insights && Array.isArray(parsed.insights)) {
                    for (const insight of parsed.insights) {
                        insights.push({
                            type: type,
                            title: insight.title || 'AI Insight',
                            description: insight.description || '',
                            severity: insight.severity || 'info',
                            actionCommand: this.getActionCommandForType(type)
                        });
                    }
                }
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to parse AI insights: ${error}`);
        }
        return insights;
    }
    /**
       * Get action command for insight type
       */
    getActionCommandForType(type) {
        switch (type) {
            case 'performance': return 'codessa.optimizeCode';
            case 'complexity': return 'codessa.refactorCode';
            case 'documentation': return 'codessa.documentCode';
            case 'testing': return 'codessa.generateTests';
            case 'security': return 'codessa.fixSecurity';
            default: return 'codessa.explainCode';
        }
    }
    /**
       * Calculate confidence for insight
       */
    calculateInsightConfidence(insight) {
        // Base confidence on insight type and severity
        let confidence = 0.5;
        if (insight.severity === 'error')
            confidence = 0.9;
        else if (insight.severity === 'warning')
            confidence = 0.7;
        else if (insight.severity === 'suggestion')
            confidence = 0.6;
        return confidence;
    }
    /**
       * Get confidence indicator emoji
       */
    getConfidenceIndicator(confidence) {
        if (confidence >= 0.8)
            return '🎯';
        if (confidence >= 0.6)
            return '💡';
        return '💭';
    }
    // Command implementations
    async explainCode(range, document) {
        const code = document.getText(range);
        const result = await this.supervisorAgent.run({
            prompt: `Explain this code:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
            mode: 'ask'
        });
        if (result.success && result.output) {
            vscode.window.showInformationMessage(result.output);
        }
    }
    async optimizeCode(range, document) {
        const code = document.getText(range);
        const result = await this.supervisorAgent.run({
            prompt: `Optimize this code for performance:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
            mode: 'edit'
        });
        if (result.success && result.output) {
            const edit = new vscode.WorkspaceEdit();
            edit.replace(document.uri, range, result.output);
            vscode.workspace.applyEdit(edit);
        }
    }
    async generateTests(range, document) {
        const code = document.getText(range);
        const result = await this.supervisorAgent.run({
            prompt: `Generate unit tests for this code:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
            mode: 'agent'
        });
        if (result.success && result.output) {
            // Create new test file or show in new editor
            const testContent = result.output;
            const newDoc = await vscode.workspace.openTextDocument({
                content: testContent,
                language: document.languageId
            });
            vscode.window.showTextDocument(newDoc);
        }
    }
    async refactorCode(range, document) {
        const code = document.getText(range);
        const result = await this.supervisorAgent.run({
            prompt: `Refactor this code to reduce complexity:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
            mode: 'refactor'
        });
        if (result.success && result.output) {
            const edit = new vscode.WorkspaceEdit();
            edit.replace(document.uri, range, result.output);
            vscode.workspace.applyEdit(edit);
        }
    }
    async documentCode(range, document) {
        const code = document.getText(range);
        const result = await this.supervisorAgent.run({
            prompt: `Add comprehensive documentation to this code:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
            mode: 'documentation'
        });
        if (result.success && result.output) {
            const edit = new vscode.WorkspaceEdit();
            edit.replace(document.uri, range, result.output);
            vscode.workspace.applyEdit(edit);
        }
    }
    // Helper methods
    getCachedAnalysis(key) {
        const cached = this.analysisCache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
            return cached.result;
        }
        if (cached) {
            this.analysisCache.delete(key);
        }
        return null;
    }
    setCachedAnalysis(key, result) {
        if (this.analysisCache.size >= this.maxCacheSize) {
            const firstKey = this.analysisCache.keys().next().value;
            if (firstKey) {
                this.analysisCache.delete(firstKey);
            }
        }
        this.analysisCache.set(key, { result, timestamp: Date.now() });
    }
    async checkTestFileExists(filePath) {
        // Simple check for test file existence
        const testPaths = [
            filePath.replace(/\.([^.]+)$/, '.test.$1'),
            filePath.replace(/\.([^.]+)$/, '.spec.$1'),
            filePath.replace(/src\//, 'test/').replace(/\.([^.]+)$/, '.test.$1')
        ];
        for (const testPath of testPaths) {
            try {
                await vscode.workspace.fs.stat(vscode.Uri.file(testPath));
                return true;
            }
            catch {
                // File doesn't exist, continue
            }
        }
        return false;
    }
    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.analysisCache.clear();
        this.isAnalyzing.clear();
    }
}
exports.AICodeLensProvider = AICodeLensProvider;
//# sourceMappingURL=aiCodeLensProvider.js.map