"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.promptManager = exports.PromptManager = exports.PromptCategory = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const logger_1 = require("../logger");
/**
 * Prompt category
 */
var PromptCategory;
(function (PromptCategory) {
    PromptCategory["SYSTEM"] = "system";
    PromptCategory["AGENT"] = "agent";
    PromptCategory["MODE"] = "mode";
    PromptCategory["WORKFLOW"] = "workflow";
    PromptCategory["CUSTOM"] = "custom";
})(PromptCategory || (exports.PromptCategory = PromptCategory = {}));
class PromptManager {
    static instance;
    prompts = new Map();
    context;
    promptsDir;
    constructor() { }
    /**
       * Get the singleton instance
       */
    static getInstance() {
        if (!PromptManager.instance) {
            PromptManager.instance = new PromptManager();
        }
        return PromptManager.instance;
    }
    /**
       * Initialize the prompt manager
       */
    async initialize(context) {
        this.context = context;
        // Create prompts directory if it doesn't exist
        const extensionPath = context.extensionPath;
        this.promptsDir = path.join(extensionPath, 'prompts');
        if (!fs.existsSync(this.promptsDir)) {
            fs.mkdirSync(this.promptsDir, { recursive: true });
        }
        // Load prompts
        await this.loadPrompts();
        // Register default prompts if none exist
        if (this.prompts.size === 0) {
            await this.registerDefaultPrompts();
        }
    }
    /**
       * Load prompts from the prompts directory
       */
    async loadPrompts() {
        if (!this.promptsDir) {
            return;
        }
        try {
            // Read prompt files
            const files = await fs.promises.readdir(this.promptsDir);
            for (const file of files) {
                if (file.endsWith('.json')) {
                    try {
                        const filePath = path.join(this.promptsDir, file);
                        const content = await fs.promises.readFile(filePath, 'utf-8');
                        const prompt = JSON.parse(content);
                        // Validate prompt
                        if (this.validatePrompt(prompt)) {
                            this.prompts.set(prompt.id, prompt);
                            logger_1.Logger.instance.debug(`Loaded prompt: ${prompt.name} (${prompt.id})`);
                        }
                        else {
                            logger_1.Logger.instance.warn(`Invalid prompt in file: ${file}`);
                        }
                    }
                    catch (error) {
                        logger_1.Logger.instance.error(`Error loading prompt file ${file}:`, error);
                    }
                }
            }
            logger_1.Logger.instance.info(`Loaded ${this.prompts.size} prompts`);
        }
        catch (error) {
            logger_1.Logger.instance.error('Error loading prompts:', error);
        }
    }
    /**
       * Register default prompts
       */
    async registerDefaultPrompts() {
        // System prompts
        await this.registerPrompt({
            id: 'system.default',
            name: 'Default System Prompt',
            description: 'Default system prompt for the AI assistant',
            category: PromptCategory.SYSTEM,
            content: `
You are Codessa, an AI coding assistant.
You help users with programming tasks, answer questions about code, and provide guidance on software development.
You are knowledgeable about various programming languages, frameworks, and best practices.
When asked about code, provide clear, concise explanations and examples.
If you don't know the answer to a question, say so rather than making something up.
`,
            variables: [],
            tags: ['system', 'default'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.defaultCoder',
            name: 'Default Coder System Prompt',
            description: 'System prompt for default coding assistant',
            category: PromptCategory.SYSTEM,
            content: `
You are an expert AI programming assistant.
- Follow the user's requirements carefully.
- Ensure code is high quality, well-documented, and adheres to best practices.
- Think step-by-step before writing code.
- If you need to modify files or research documentation, use the provided tools.
- If you need clarification, ask questions.
- Use markdown code blocks for code, unless the mode is 'edit' (use tools) or 'inline' (raw code).

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'coder'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.debugFix',
            name: 'Debug Fix System Prompt',
            description: 'System prompt for debugging and fixing code',
            category: PromptCategory.SYSTEM,
            content: `
You are an AI debugging assistant.
- Analyze the provided code ({{CODE_SNIPPET}}), file path ({{FILE_PATH}}), error message ({{ERROR_MESSAGE}}), and diagnostics ({{DIAGNOSTICS}}).
- Identify the root cause of the error.
- Propose a fix. Use the 'file.applyDiff' or 'file.writeFile' tool to apply the fix. Do not output raw code for the fix, use the tools.
- Explain the fix clearly in your final answer.

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['CODE_SNIPPET', 'FILE_PATH', 'ERROR_MESSAGE', 'DIAGNOSTICS', 'TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'debug'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.generateCode',
            name: 'Generate Code System Prompt',
            description: 'System prompt for code generation',
            category: PromptCategory.SYSTEM,
            content: `
You are an AI code generation assistant.
- Generate code based on the user's request ({{USER_REQUEST}}).
- Consider the context ({{CURRENT_FILE_PATH}}, {{SELECTED_TEXT}}).
- Ensure the generated code is correct, efficient, and fits the surrounding code style.
- You can use tools like 'file.writeFile' if the request is to create a new file.
- Provide the final code in your final answer, usually within markdown blocks.

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['USER_REQUEST', 'CURRENT_FILE_PATH', 'SELECTED_TEXT', 'TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'generate'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.inlineCode',
            name: 'Inline Code System Prompt',
            description: 'System prompt for inline code generation',
            category: PromptCategory.SYSTEM,
            content: `
You are an AI assistant generating a short code snippet to be inserted inline.
- The user has selected the following text: {{SELECTED_TEXT}}
- The user's request is: {{USER_REQUEST}}
- Generate a concise code snippet that fulfills the request, suitable for replacing the selected text or inserting at the cursor.
- Output ONLY the raw code snippet in the 'final_answer' field of the JSON output. Do not use markdown. Do not use tools unless absolutely necessary (e.g., reading another file for context).

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['SELECTED_TEXT', 'USER_REQUEST', 'TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'inline'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.documentationResearcher',
            name: 'Documentation Researcher System Prompt',
            description: 'System prompt for documentation research',
            category: PromptCategory.SYSTEM,
            content: `
You are an AI assistant specialized in finding and summarizing technical documentation using the 'docs.search' tool.
- Research documentation related to the user's query: {{QUERY}}
- Use the 'docs.search' tool with the query.
- Summarize the findings from the tool result in your final answer.

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['QUERY', 'TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'documentation'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.xpTester',
            name: 'XP Tester System Prompt',
            description: 'System prompt for XP/TDD testing',
            category: PromptCategory.SYSTEM,
            content: `
You are an AI assistant following Extreme Programming (XP) principles, focusing on Test-Driven Development (TDD).
- The user wants to implement the following feature: {{FEATURE_DESCRIPTION}}
- Write comprehensive unit tests for this feature *before* writing the implementation code.
- Use the testing framework appropriate for the project context ({{TEST_FRAMEWORK}}).
- Ensure tests cover edge cases and main functionality.
- Output the test code using the 'file.writeFile' tool.

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['FEATURE_DESCRIPTION', 'TEST_FRAMEWORK', 'TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'xp', 'testing'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.xpImplementer',
            name: 'XP Implementer System Prompt',
            description: 'System prompt for XP implementation',
            category: PromptCategory.SYSTEM,
            content: `
You are an AI assistant following Extreme Programming (XP) principles.
- You are given the following unit tests: {{TEST_CODE}}
- Write the simplest possible implementation code that passes these tests.
- Refactor the code for clarity and efficiency after tests pass, if necessary.
- Adhere to coding standards and best practices.
- Output the implementation code using the 'file.writeFile' tool.

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['TEST_CODE', 'TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'xp', 'implementation'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.chatAgent',
            name: 'Chat Agent System Prompt',
            description: 'System prompt for chat agent',
            category: PromptCategory.SYSTEM,
            content: `
You are a helpful AI assistant engaging in a conversation.
- Respond clearly and concisely to the user's messages.
- Maintain the context of the conversation history.
- You can use tools if the user asks for information retrieval or file operations.

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'chat'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'system.editCode',
            name: 'Edit Code System Prompt',
            description: 'System prompt for code editing',
            category: PromptCategory.SYSTEM,
            content: `
You are an AI code editor assistant.
- The user wants you to modify existing code.
- Current file: {{CURRENT_FILE_PATH}}
- Selected code or context: {{SELECTED_TEXT}}
- User request: {{USER_REQUEST}}
- First analyze the code to understand its structure and purpose.
- Then, use the file.readFile tool if you need more context beyond what's selected.
- Create a diff patch using file.createDiff and apply it with file.applyDiff.
- Only modify what's needed for the task - be surgical and preserve the original code style.
- If your changes are complex, explain your modifications in the final answer.

{{TOOL_USAGE_INSTRUCTIONS}}
`,
            variables: ['CURRENT_FILE_PATH', 'SELECTED_TEXT', 'USER_REQUEST', 'TOOL_USAGE_INSTRUCTIONS'],
            tags: ['system', 'edit'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Add TOOL_USAGE_INSTRUCTIONS as a separate prompt
        await this.registerPrompt({
            id: 'system.toolUsageInstructions',
            name: 'Tool Usage Instructions',
            description: 'Instructions for using tools in AI prompts',
            category: PromptCategory.SYSTEM,
            content: `
You have access to the following tools:
{{AVAILABLE_TOOLS_LIST}}

To use a tool, output a JSON object EXACTLY in this format (no other text before or after):
{
  "tool_call": {
    "name": "tool_id.action_name", // e.g., "file.readFile", "docs.search"
    "arguments": { // Arguments specific to the tool action
      "arg1": "value1",
      "arg2": "value2"
      // ...
    }
  }
}

After the tool executes, I will provide you with the result, and you can continue your task or call another tool.

When you have the final answer and don't need to use any more tools, output a JSON object EXACTLY in this format:
{
  "final_answer": "Your complete final response here."
}

Think step-by-step. Analyze the request, decide if a tool is needed, call the tool if necessary, analyze the result, and repeat until you can provide the final answer.
`,
            variables: ['AVAILABLE_TOOLS_LIST'],
            tags: ['system', 'tools'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Agent prompts
        await this.registerPrompt({
            id: 'agent.developer',
            name: 'Developer Agent',
            description: 'Prompt for the developer agent',
            category: PromptCategory.AGENT,
            content: `
You are a skilled software developer with expertise in multiple programming languages and frameworks.
Your task is to write clean, efficient, and well-documented code that follows best practices.
Consider edge cases, error handling, and performance in your implementations.
Explain your code and the reasoning behind your design decisions.
`,
            variables: [],
            tags: ['agent', 'developer'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'agent.architect',
            name: 'Architect Agent',
            description: 'Prompt for the architect agent',
            category: PromptCategory.AGENT,
            content: `
You are a software architect with deep knowledge of system design, patterns, and architectural principles.
Your task is to design robust, scalable, and maintainable software systems.
Consider trade-offs between different approaches and justify your decisions.
Create clear diagrams and documentation to communicate your architectural vision.
`,
            variables: [],
            tags: ['agent', 'architect'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Mode prompts
        await this.registerPrompt({
            id: 'mode.ask',
            name: 'Ask Mode',
            description: 'Prompt for Ask mode',
            category: PromptCategory.MODE,
            content: `
You are an AI assistant specialized in answering questions about codebases.

## Context Information:
{{contextContent}}

{{memoryContext}}

## User Question:
{{message}}

## Instructions:
- Provide clear, concise, and accurate answers about the code
- Focus on explaining concepts, architecture, and implementation details
- Use proper code formatting and include line numbers when relevant
- If you don't know the answer, say so rather than making something up
- Reference specific files and functions when applicable
- Explain the reasoning behind your answers
`,
            variables: ['contextContent', 'memoryContext', 'message'],
            tags: ['mode', 'ask'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'mode.debug',
            name: 'Debug Mode',
            description: 'Prompt for Debug mode',
            category: PromptCategory.MODE,
            content: `
You are an AI debugging assistant specialized in identifying and fixing code issues.

## Code Context:
{{contextContent}}

{{memoryContext}}

## Issue Description:
{{message}}

{{errorMessage}}

{{stackTrace}}

## Instructions:
- Analyze the code problem and identify root causes
- Provide step-by-step debugging approaches
- Suggest complete, actionable code fixes
- Include line numbers and file names when referencing code
- Explain your reasoning and debugging methodology
- Consider error messages, stack traces, and code context
- Provide preventive measures to avoid similar issues
`,
            variables: ['contextContent', 'memoryContext', 'message', 'errorMessage', 'stackTrace'],
            tags: ['mode', 'debug'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Edit Mode
        await this.registerPrompt({
            id: 'mode.edit',
            name: 'Edit Mode',
            description: 'Prompt for Edit mode',
            category: PromptCategory.MODE,
            content: `
You are an AI code editing assistant specialized in making precise code modifications.

## Code Context:
{{contextContent}}

{{memoryContext}}

## Edit Request:
{{message}}

## Instructions:
- Analyze the existing code structure and understand its purpose
- Make precise, targeted modifications as requested
- Maintain code quality, style, and existing patterns
- Provide a detailed plan of changes before implementation
- Include before/after code snippets for clarity
- Explain the rationale behind each change
- Ensure changes don't break existing functionality
- Follow the project's coding standards and conventions
`,
            variables: ['contextContent', 'memoryContext', 'message'],
            tags: ['mode', 'edit'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Chat Mode
        await this.registerPrompt({
            id: 'mode.chat',
            name: 'Chat Mode',
            description: 'Prompt for Chat mode',
            category: PromptCategory.MODE,
            content: `
You are Codessa, a helpful AI coding assistant engaging in conversation.

## Context Information:
{{contextContent}}

{{memoryContext}}

## User Message:
{{message}}

## Instructions:
- Respond naturally and conversationally
- Maintain context from previous messages
- Provide helpful coding assistance when requested
- Ask clarifying questions when needed
- Be concise but thorough in explanations
- Use appropriate technical language for the user's level
- Reference code examples when helpful
`,
            variables: ['contextContent', 'memoryContext', 'message'],
            tags: ['mode', 'chat'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Agent Mode
        await this.registerPrompt({
            id: 'mode.agent',
            name: 'Agent Mode',
            description: 'Prompt for autonomous Agent mode',
            category: PromptCategory.MODE,
            content: `
You are an autonomous AI agent capable of working independently on complex tasks.

## Task Assignment:
{{task}}

## Context Information:
{{contextContent}}

{{memoryContext}}

## Instructions:
- Work autonomously to complete the assigned task
- Break down complex tasks into manageable steps
- Use available tools and resources effectively
- Provide regular progress updates
- Think step-by-step and explain your reasoning
- Handle errors gracefully and adapt your approach
- Deliver high-quality, production-ready results
- Document your work and decisions
`,
            variables: ['task', 'contextContent', 'memoryContext'],
            tags: ['mode', 'agent', 'autonomous'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Multi-Agent Mode
        await this.registerPrompt({
            id: 'mode.multiAgent',
            name: 'Multi-Agent Mode',
            description: 'Prompt for Multi-Agent coordination',
            category: PromptCategory.MODE,
            content: `
You are the supervisor of a multi-agent team working collaboratively on complex tasks.

## Task Assignment:
{{task}}

## Context Information:
{{contextContent}}

{{memoryContext}}

## Team Members:
{{teamMembers}}

## Instructions:
- Coordinate and delegate tasks to appropriate team members
- Monitor progress and provide guidance when needed
- Integrate work from different team members
- Resolve conflicts and ensure quality standards
- Maintain clear communication channels
- Ensure the final solution meets all requirements
- Provide comprehensive status updates
- Foster collaboration and knowledge sharing
`,
            variables: ['task', 'contextContent', 'memoryContext', 'teamMembers'],
            tags: ['mode', 'multi-agent', 'coordination'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Research Mode
        await this.registerPrompt({
            id: 'mode.research',
            name: 'Research Mode',
            description: 'Prompt for Research mode',
            category: PromptCategory.MODE,
            content: `
You are a research assistant conducting comprehensive technical analysis.

## Research Topic:
{{message}}

## Available Context:
{{contextContent}}

{{memoryContext}}

## Instructions:
- Conduct thorough analysis of the research topic
- Provide comprehensive findings and insights
- Identify relevant code patterns and implementations
- Suggest best practices and recommendations
- Include additional resources and references
- Structure your research logically and clearly
- Support findings with evidence and examples
- Consider multiple perspectives and approaches
`,
            variables: ['message', 'contextContent', 'memoryContext'],
            tags: ['mode', 'research'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Documentation Mode
        await this.registerPrompt({
            id: 'mode.documentation',
            name: 'Documentation Mode',
            description: 'Prompt for Documentation mode',
            category: PromptCategory.MODE,
            content: `
You are a technical documentation specialist creating comprehensive code documentation.

## Documentation Request:
{{message}}

## Code to Document:
{{contextContent}}

{{memoryContext}}

## Instructions:
- Create clear, professional documentation in markdown format
- Include overview and purpose of the code
- Provide API documentation with parameters and return values
- Add usage examples and code snippets
- Document implementation details and architecture
- Include best practices and considerations
- Reference related components and dependencies
- Use proper headings, formatting, and structure
`,
            variables: ['message', 'contextContent', 'memoryContext'],
            tags: ['mode', 'documentation'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Refactor Mode
        await this.registerPrompt({
            id: 'mode.refactor',
            name: 'Refactor Mode',
            description: 'Prompt for Refactor mode',
            category: PromptCategory.MODE,
            content: `
You are a code refactoring expert specializing in improving code quality and structure.

## Refactoring Request:
{{message}}

## Code to Refactor:
{{contextContent}}

{{memoryContext}}

## Instructions:
- Analyze current code structure and identify improvement opportunities
- Apply appropriate design patterns and best practices
- Improve code readability, maintainability, and performance
- Reduce complexity and eliminate code duplication
- Enhance error handling and robustness
- Provide before/after comparisons with explanations
- Include testing recommendations for refactored code
- Document benefits and potential risks of changes
`,
            variables: ['message', 'contextContent', 'memoryContext'],
            tags: ['mode', 'refactor'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Receiver Agent
        await this.registerPrompt({
            id: 'agent.receiver',
            name: 'Receiver Agent',
            description: 'Prompt for the receiver agent that processes user input',
            category: PromptCategory.AGENT,
            content: `
You are the Receiver Agent responsible for processing and enhancing user input before delegation.

## User Input:
{{userInput}}

## Multimodal Data:
{{multimodalData}}

## Attached Files:
{{attachedFiles}}

## Instructions:
- Correct spelling and grammar errors in user input
- Enhance clarity and completeness of user requests
- Process and describe any attached images or media
- Clean up and improve transcribed audio text
- Organize and structure file attachments and references
- Extract key requirements and intent from user input
- Prepare structured, comprehensive information for the supervisor
- Maintain the user's original intent while improving clarity
- Flag any ambiguities that need clarification
`,
            variables: ['userInput', 'multimodalData', 'attachedFiles'],
            tags: ['agent', 'receiver', 'input-processing'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Supervisor Agent
        await this.registerPrompt({
            id: 'agent.supervisor',
            name: 'Supervisor Agent',
            description: 'Prompt for the supervisor agent that coordinates all work',
            category: PromptCategory.AGENT,
            content: `
You are the Supervisor Agent responsible for coordinating all work and providing final responses to users.

## Processed User Request:
{{processedRequest}}

## Available Agents:
{{availableAgents}}

## Context Information:
{{contextContent}}

{{memoryContext}}

## Instructions:
- Analyze the processed user request and determine required actions
- Delegate appropriate tasks to specialized agents
- Monitor agent progress and provide guidance as needed
- Collect and integrate outputs from all working agents
- Ensure all aspects of the user request are addressed
- Generate comprehensive, well-structured responses
- Stream your thoughts and decision-making process to the user
- Coordinate async work to prevent UI blocking
- Maintain quality standards and completeness
- Provide final response only after all agents complete their work
`,
            variables: ['processedRequest', 'availableAgents', 'contextContent', 'memoryContext'],
            tags: ['agent', 'supervisor', 'coordination'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Multi-Agent Team Member Prompts
        await this.registerPrompt({
            id: 'agent.multiAgent.supervisor',
            name: 'Multi-Agent Supervisor',
            description: 'Prompt for supervisor in multi-agent team',
            category: PromptCategory.AGENT,
            content: `
You are a supervisor in a team of AI agents working on a software project.
Your role is to:
1. Coordinate the team and ensure tasks are completed effectively
2. Assign tasks to appropriate team members based on their expertise
3. Monitor progress and provide guidance when needed
4. Resolve conflicts and ensure quality standards are met
5. Communicate with stakeholders and provide status updates

Be decisive in your leadership while remaining collaborative and supportive of your team.
Focus on delivering high-quality results within the given constraints.
`,
            variables: [],
            tags: ['agent', 'multi-agent', 'supervisor'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'agent.multiAgent.architect',
            name: 'Multi-Agent Architect',
            description: 'Prompt for architect in multi-agent team',
            category: PromptCategory.AGENT,
            content: `
You are an architect in a team of AI agents working on a software project.
Your role is to:
1. Design the overall structure and architecture of the solution
2. Make high-level technical decisions and trade-offs
3. Create architectural diagrams and documentation
4. Ensure the solution is scalable, maintainable, and follows best practices
5. Guide the development team in implementing the architecture

Focus on creating robust, well-designed solutions that meet both current and future needs.
Consider performance, security, and maintainability in all your architectural decisions.
`,
            variables: [],
            tags: ['agent', 'multi-agent', 'architect'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'agent.multiAgent.developer',
            name: 'Multi-Agent Developer',
            description: 'Prompt for developer in multi-agent team',
            category: PromptCategory.AGENT,
            content: `
You are a developer in a team of AI agents working on a software project.
Your role is to:
1. Implement the solution based on the architect's design and requirements
2. Write clean, efficient, and well-documented code
3. Follow coding standards and best practices
4. Collaborate with other team members to integrate your work
5. Fix bugs and address issues identified during testing

Focus on writing code that is not only functional but also maintainable and readable.
Pay attention to edge cases and error handling in your implementation.
`,
            variables: [],
            tags: ['agent', 'multi-agent', 'developer'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'agent.multiAgent.tester',
            name: 'Multi-Agent Tester',
            description: 'Prompt for tester in multi-agent team',
            category: PromptCategory.AGENT,
            content: `
You are a tester in a team of AI agents working on a software project.
Your role is to:
1. Create test plans and test cases based on requirements
2. Execute tests to verify functionality
3. Identify and report bugs and issues
4. Verify fixes and perform regression testing
5. Ensure the solution meets quality standards

Be thorough in your testing approach, considering edge cases and potential failure points.
Provide clear, detailed bug reports that help developers understand and fix issues.
`,
            variables: [],
            tags: ['agent', 'multi-agent', 'tester'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'agent.multiAgent.reviewer',
            name: 'Multi-Agent Reviewer',
            description: 'Prompt for reviewer in multi-agent team',
            category: PromptCategory.AGENT,
            content: `
You are a code reviewer in a team of AI agents working on a software project.
Your role is to:
1. Review code for quality, correctness, and adherence to standards
2. Identify potential bugs, performance issues, and security vulnerabilities
3. Suggest improvements and best practices
4. Ensure code is maintainable and follows the project's style guide
5. Provide constructive feedback to developers

Be thorough but fair in your reviews, focusing on helping improve the code rather than criticizing.
Consider both technical correctness and readability in your feedback.
`,
            variables: [],
            tags: ['agent', 'multi-agent', 'reviewer'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Code Analysis Prompts
        await this.registerPrompt({
            id: 'analysis.performance',
            name: 'Performance Analysis',
            description: 'Prompt for analyzing code performance',
            category: PromptCategory.WORKFLOW,
            content: `
Analyze this code for performance issues and optimization opportunities:

## Code to Analyze:
{{codeContent}}

## Language:
{{languageId}}

## Focus Areas:
1. Algorithmic complexity
2. Memory usage patterns
3. I/O operations
4. Loop optimizations
5. Data structure choices

## Output Format:
Provide specific, actionable insights in JSON format:
{
  "insights": [
    {
      "type": "performance",
      "title": "Brief title",
      "description": "Detailed description",
      "severity": "info|warning|error",
      "lineNumber": 0
    }
  ]
}
`,
            variables: ['codeContent', 'languageId'],
            tags: ['analysis', 'performance'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'analysis.architecture',
            name: 'Architecture Analysis',
            description: 'Prompt for analyzing project architecture patterns',
            category: PromptCategory.WORKFLOW,
            content: `
# Architecture Pattern Analysis

## Project Structure:
{{fileStructure}}

## Languages:
{{languages}}

## Entry Points:
{{entryPoints}}

## Task:
Analyze the project structure and identify architecture patterns.

Provide analysis in JSON format:
{
  "patterns": [
    {
      "name": "Pattern Name",
      "confidence": 0.8,
      "description": "Description of the pattern",
      "evidence": ["Evidence 1", "Evidence 2"]
    }
  ],
  "recommendations": [
    {
      "type": "improvement",
      "description": "Recommendation description",
      "priority": "high|medium|low"
    }
  ]
}
`,
            variables: ['fileStructure', 'languages', 'entryPoints'],
            tags: ['analysis', 'architecture'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Workflow Template Prompts
        await this.registerPrompt({
            id: 'workflow.memoryAgent',
            name: 'Memory Agent Workflow',
            description: 'Prompt for memory-enhanced agent workflow',
            category: PromptCategory.WORKFLOW,
            content: `
You are an AI assistant that remembers past interactions.
{{enhancedContext}}

User Query: "{{userQuery}}"

Provide a relevant and helpful response, leveraging the insights from your memory if applicable.
`,
            variables: ['enhancedContext', 'userQuery'],
            tags: ['workflow', 'memory'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'workflow.questionAnalyzer',
            name: 'Question Analyzer Workflow',
            description: 'Prompt for analyzing research questions',
            category: PromptCategory.WORKFLOW,
            content: `
Task: Analyze the research question.
Question: {{question}}

Provide a detailed analysis of the question including:
1. Key concepts and topics
2. Scope and complexity
3. Required research areas
4. Potential challenges
5. Recommended approach
`,
            variables: ['question'],
            tags: ['workflow', 'analysis'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'workflow.codeEditor',
            name: 'Code Editor Workflow',
            description: 'Prompt for editing code based on analysis',
            category: PromptCategory.WORKFLOW,
            content: `
Task: Edit code based on analysis.
Analysis: {{analysis}}
Original Code: {{originalCode}}

Provide the edited code with improvements based on the analysis. Include:
1. Clear explanation of changes made
2. Reasoning for each modification
3. Any potential side effects or considerations
`,
            variables: ['analysis', 'originalCode'],
            tags: ['workflow', 'code-editing'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'workflow.topicAnalysis',
            name: 'Topic Analysis Workflow',
            description: 'Prompt for analyzing research topics',
            category: PromptCategory.WORKFLOW,
            content: `
You are a research assistant. Analyze the following topic and create a comprehensive research plan.

Topic: {{topic}}

Provide:
1. Key areas to research
2. Specific search queries to use
3. Expected information sources
4. Research methodology
5. Success criteria

Format your response clearly for an AI assistant to follow.
`,
            variables: ['topic'],
            tags: ['workflow', 'research'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'workflow.codeGenerator',
            name: 'Code Generator Workflow',
            description: 'Prompt for generating code based on specifications',
            category: PromptCategory.WORKFLOW,
            content: `
Task: Generate code based on specification.
Specification: {{specification}}
Requirements: {{requirements}}

Provide clean, well-documented code that meets the specification and requirements.
Include appropriate error handling and follow best practices for the target language.
`,
            variables: ['specification', 'requirements'],
            tags: ['workflow', 'code-generation'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        await this.registerPrompt({
            id: 'workflow.codeAnalysis',
            name: 'Code Analysis Workflow',
            description: 'Prompt for analyzing code files',
            category: PromptCategory.WORKFLOW,
            content: `
Analyze the following code file and provide insights:

File: {{filePath}}

{{fileContent}}

Provide your analysis in the following JSON format:
{
    "insights": ["list of key insights about the code"],
    "suggestions": ["list of specific improvement suggestions"],
    "dependencies": ["list of detected dependencies"]
}
`,
            variables: ['filePath', 'fileContent'],
            tags: ['workflow', 'analysis'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        // Workflow prompts
        await this.registerPrompt({
            id: 'workflow.codeGeneration',
            name: 'Code Generation Workflow',
            description: 'Prompt for code generation workflow',
            category: PromptCategory.WORKFLOW,
            content: `
Generate code based on the following requirements:

{{requirements}}

Please provide clean, well-documented code that meets these requirements.
Explain your implementation approach and any assumptions you made.
`,
            variables: ['requirements'],
            tags: ['workflow', 'code-generation'],
            author: 'Codessa',
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
    }
    /**
       * Validate a prompt
       */
    validatePrompt(prompt) {
        return (!!prompt.id &&
            !!prompt.name &&
            !!prompt.description &&
            !!prompt.category &&
            !!prompt.content);
    }
    /**
       * Register a prompt
       */
    async registerPrompt(prompt) {
        // Validate prompt
        if (!this.validatePrompt(prompt)) {
            throw new Error('Invalid prompt');
        }
        // Add to in-memory map
        this.prompts.set(prompt.id, prompt);
        // Save to file
        await this.savePrompt(prompt);
        logger_1.Logger.instance.info(`Registered prompt: ${prompt.name} (${prompt.id})`);
    }
    /**
       * Save a prompt to file
       */
    async savePrompt(prompt) {
        if (!this.promptsDir) {
            return;
        }
        try {
            const filePath = path.join(this.promptsDir, `${prompt.id}.json`);
            await fs.promises.writeFile(filePath, JSON.stringify(prompt, null, 2), 'utf-8');
        }
        catch (error) {
            logger_1.Logger.instance.error(`Error saving prompt ${prompt.id}:`, error);
            throw error;
        }
    }
    /**
       * Get a prompt by ID
       */
    getPrompt(id) {
        return this.prompts.get(id);
    }
    /**
       * Get all prompts
       */
    getAllPrompts() {
        return Array.from(this.prompts.values());
    }
    /**
       * Get prompts by category
       */
    getPromptsByCategory(category) {
        return Array.from(this.prompts.values()).filter(prompt => prompt.category === category);
    }
    /**
       * Get prompts by tag
       */
    getPromptsByTag(tag) {
        return Array.from(this.prompts.values()).filter(prompt => prompt.tags?.includes(tag));
    }
    /**
       * Delete a prompt
       */
    async deletePrompt(id) {
        if (!this.prompts.has(id)) {
            throw new Error(`Prompt with ID '${id}' not found`);
        }
        // Remove from in-memory map
        this.prompts.delete(id);
        // Delete file
        if (this.promptsDir) {
            try {
                const filePath = path.join(this.promptsDir, `${id}.json`);
                await fs.promises.unlink(filePath);
            }
            catch (error) {
                logger_1.Logger.instance.error(`Error deleting prompt file for ${id}:`, error);
                throw error;
            }
        }
        logger_1.Logger.instance.info(`Deleted prompt: ${id}`);
    }
    /**
       * Render a prompt with variables
       */
    renderPrompt(promptId, variables = {}) {
        const prompt = this.getPrompt(promptId);
        if (!prompt) {
            throw new Error(`Prompt with ID '${promptId}' not found`);
        }
        let content = prompt.content;
        // Replace variables
        for (const [key, value] of Object.entries(variables)) {
            content = content.replace(new RegExp(`{{${key}}}`, 'g'), value);
        }
        return content;
    }
    /**
     * Get a system prompt by name
     */
    getSystemPrompt(name, variables) {
        // Try to find a prompt with the given name in the system category
        const systemPrompts = this.getPromptsByCategory(PromptCategory.SYSTEM);
        // First, try to find an exact match by ID
        const exactMatch = systemPrompts.find(p => p.id === `system.${name}`);
        if (exactMatch) {
            return this.renderPrompt(exactMatch.id, variables || {});
        }
        // Next, try to find a prompt with a matching name
        const nameMatch = systemPrompts.find(p => p.name.toLowerCase().includes(name.toLowerCase()) ||
            p.id.toLowerCase().includes(name.toLowerCase()));
        if (nameMatch) {
            return this.renderPrompt(nameMatch.id, variables || {});
        }
        // If no match found, return the default system prompt
        const defaultPrompt = this.getPrompt('system.default');
        if (defaultPrompt) {
            return this.renderPrompt('system.default', variables || {});
        }
        // If no default prompt exists, return a simple fallback
        return `You are Codessa, an AI coding assistant. You are helping with: ${name}`;
    }
}
exports.PromptManager = PromptManager;
// Export singleton instance
exports.promptManager = PromptManager.getInstance();
//# sourceMappingURL=promptManager.js.map