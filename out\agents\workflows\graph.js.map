{"version": 3, "file": "graph.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/graph.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAKH,yCAAsC;AAEtC;;GAEG;AACH,MAAa,OAAO;IACV,UAAU,CAAkB;IAC5B,KAAK,CAAyB;IAC9B,KAAK,CAAyB;IAEtC;;SAEK;IACL,YAAY,UAA2B;QACrC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;QAEvB,YAAY;QACZ,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,YAAY;QACZ,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IACpE,CAAC;IAED;;SAEK;IACE,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;SAEK;IACE,OAAO,CAAC,EAAU;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;SAEK;IACE,QAAQ;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;SAEK;IACE,OAAO,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;SAEK;IACE,QAAQ;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;SAEK;IACE,gBAAgB,CAAC,MAAc;QACpC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;IAChE,CAAC;IAED;;SAEK;IACE,gBAAgB,CAAC,MAAc;QACpC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;IAChE,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,eAAe,CAAC,EAAU,EAAE,IAAY;QACpD,OAAO;YACL,EAAE;YACF,IAAI;YACJ,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,OAAO;SACd,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,eAAe,CAAC,EAAU,EAAE,IAAY,EAAE,KAAY;QAClE,OAAO;YACL,EAAE;YACF,IAAI;YACJ,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,OAAO;YACb,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,cAAc,CAAC,EAAU,EAAE,IAAY,EAAE,IAAW;QAChE,OAAO;YACL,EAAE;YACF,IAAI;YACJ,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,MAAM;YACZ,IAAI;SACL,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,gBAAgB,CAAC,EAAU,EAAE,IAAY;QACrD,OAAO;YACL,EAAE;YACF,IAAI;YACJ,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,QAAQ;SACf,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,OAAO,CAAC,KAAU;QAC7B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzE,kBAAkB;QAClB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;QAEtE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,iDAAiD;QACjD,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,KAAU;QAClD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAElC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,iBAAiB,MAAM,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAElE,IAAI,MAAW,CAAC;QAEhB,6BAA6B;QAC7B,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,OAAO;gBACV,MAAM,GAAG,KAAK,CAAC;gBACf,MAAM;YAER,KAAK,OAAO;gBACV,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAChB,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC3E,CAAC;gBACD,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;gBACnE,MAAM;YAER,KAAK,MAAM;gBACT,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC1E,CAAC;gBACD,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC3D,MAAM;YAER,KAAK,QAAQ;gBACX,OAAO,KAAK,CAAC;YAEf;gBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,qBAAqB;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEpD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,uCAAuC;YACvC,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,sCAAsC;QACtC,IAAI,QAA+B,CAAC;QAEpC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,iCAAiC;YACjC,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,wEAAwE;YACxE,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACnC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC5B,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBAClD,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAChC,CAAC;gBAED,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,0CAA0C,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACtF,CAAC;QAED,wBAAwB;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;CACF;AA3ND,0BA2NC", "sourcesContent": ["/**\n * Codessa graph implementation\n *\n * This file provides the core graph implementation for Codessa workflows.\n */\n\nimport { Agent } from '../agentUtilities/agent';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { GraphDefinition, GraphNode, GraphEdge } from './types';\nimport { Logger } from '../../logger';\n\n/**\n * Codessa graph implementation\n */\nexport class Codessa {\n  private definition: GraphDefinition;\n  private nodes: Map<string, GraphNode>;\n  private edges: Map<string, GraphEdge>;\n\n  /**\n     * Create a new Codessa graph\n     */\n  constructor(definition: GraphDefinition) {\n    this.definition = definition;\n    this.nodes = new Map();\n    this.edges = new Map();\n\n    // Add nodes\n    definition.nodes.forEach(node => {\n      this.nodes.set(node.id, node);\n    });\n\n    // Add edges\n    definition.edges.forEach(edge => {\n      this.edges.set(edge.name, edge);\n    });\n\n    Logger.instance.info(`Created Codessa graph: ${definition.name}`);\n  }\n\n  /**\n     * Get the graph definition\n     */\n  public getDefinition(): GraphDefinition {\n    return this.definition;\n  }\n\n  /**\n     * Get a node by ID\n     */\n  public getNode(id: string): GraphNode | undefined {\n    return this.nodes.get(id);\n  }\n\n  /**\n     * Get all nodes\n     */\n  public getNodes(): GraphNode[] {\n    return Array.from(this.nodes.values());\n  }\n\n  /**\n     * Get an edge by name\n     */\n  public getEdge(name: string): GraphEdge | undefined {\n    return this.edges.get(name);\n  }\n\n  /**\n     * Get all edges\n     */\n  public getEdges(): GraphEdge[] {\n    return Array.from(this.edges.values());\n  }\n\n  /**\n     * Get outgoing edges from a node\n     */\n  public getOutgoingEdges(nodeId: string): GraphEdge[] {\n    return this.getEdges().filter(edge => edge.source === nodeId);\n  }\n\n  /**\n     * Get incoming edges to a node\n     */\n  public getIncomingEdges(nodeId: string): GraphEdge[] {\n    return this.getEdges().filter(edge => edge.target === nodeId);\n  }\n\n  /**\n     * Create an input node\n     */\n  public static createInputNode(id: string, name: string): GraphNode {\n    return {\n      id,\n      name,\n      label: name,\n      type: 'input'\n    };\n  }\n\n  /**\n     * Create an agent node\n     */\n  public static createAgentNode(id: string, name: string, agent: Agent): GraphNode {\n    return {\n      id,\n      name,\n      label: name,\n      type: 'agent',\n      agent\n    };\n  }\n\n  /**\n     * Create a tool node\n     */\n  public static createToolNode(id: string, name: string, tool: ITool): GraphNode {\n    return {\n      id,\n      name,\n      label: name,\n      type: 'tool',\n      tool\n    };\n  }\n\n  /**\n     * Create an output node\n     */\n  public static createOutputNode(id: string, name: string): GraphNode {\n    return {\n      id,\n      name,\n      label: name,\n      type: 'output'\n    };\n  }\n\n  /**\n     * Execute the graph\n     */\n  public async execute(input: any): Promise<any> {\n    Logger.instance.info(`Executing Codessa graph: ${this.definition.name}`);\n\n    // Find input node\n    const inputNode = this.getNodes().find(node => node.type === 'input');\n\n    if (!inputNode) {\n      throw new Error('No input node found in the graph');\n    }\n\n    // Execute the graph starting from the input node\n    return this.executeNode(inputNode.id, input);\n  }\n\n  /**\n     * Execute a node\n     */\n  private async executeNode(nodeId: string, input: any): Promise<any> {\n    const node = this.getNode(nodeId);\n\n    if (!node) {\n      throw new Error(`Node with ID '${nodeId}' not found`);\n    }\n\n    Logger.instance.info(`Executing node: ${node.name} (${node.id})`);\n\n    let output: any;\n\n    // Process node based on type\n    switch (node.type) {\n      case 'input':\n        output = input;\n        break;\n\n      case 'agent':\n        if (!node.agent) {\n          throw new Error(`Agent not defined for node: ${node.name} (${node.id})`);\n        }\n        output = await node.agent.run({ prompt: input, mode: 'task' }, {});\n        break;\n\n      case 'tool':\n        if (!node.tool) {\n          throw new Error(`Tool not defined for node: ${node.name} (${node.id})`);\n        }\n        output = await node.tool.execute(undefined, { input }, {});\n        break;\n\n      case 'output':\n        return input;\n\n      default:\n        throw new Error(`Unknown node type: ${node.type}`);\n    }\n\n    // Get outgoing edges\n    const outgoingEdges = this.getOutgoingEdges(nodeId);\n\n    if (outgoingEdges.length === 0) {\n      // No outgoing edges, return the output\n      return output;\n    }\n\n    // Find the appropriate edge to follow\n    let nextEdge: GraphEdge | undefined;\n\n    if (outgoingEdges.length === 1) {\n      // Only one outgoing edge, use it\n      nextEdge = outgoingEdges[0];\n    } else {\n      // Multiple outgoing edges, find the appropriate one based on the output\n      nextEdge = outgoingEdges.find(edge => {\n        if (edge.type === 'default') {\n          return true;\n        }\n\n        if (edge.type === 'conditional' && edge.condition) {\n          return edge.condition(output);\n        }\n\n        return false;\n      });\n    }\n\n    if (!nextEdge) {\n      throw new Error(`No valid outgoing edge found for node: ${node.name} (${node.id})`);\n    }\n\n    // Execute the next node\n    return this.executeNode(nextEdge.target, output);\n  }\n}\n"]}