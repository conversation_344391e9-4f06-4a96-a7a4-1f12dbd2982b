{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../src/logger.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,qCAAuC;AAIvC,MAAM,kBAAkB,GAA6B;IACnD,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACT,CAAC;AAEF,MAAa,MAAM;IACT,MAAM,CAAC,SAAS,GAAkB,IAAI,CAAC;IACvC,aAAa,CAAuB;IACpC,eAAe,CAAW;IAE1B,sBAAsB,CAAgC;IAEvD,MAAM,CAAC,UAAU,CAAC,OAAgC;QACvD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,gFAAgF;QAChF,IAAI,QAAQ,CAAC,sBAAsB,EAAE,CAAC;YACpC,QAAQ,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;QAC5C,CAAC;QACD,QAAQ,CAAC,sBAAsB,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC9E,IAAI,CAAC,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC/C,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;IAC9D,CAAC;IAED;QACE,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAClE,IAAI,CAAC,eAAe,GAAG,IAAA,oBAAW,GAAc,CAAC;IACnD,CAAC;IAEM,MAAM,KAAK,QAAQ;QACxB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,EAAE,CAAC;QAClC,CAAC;QACD,OAAO,MAAM,CAAC,SAAS,CAAC;IAC1B,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,eAAe,GAAG,IAAA,oBAAW,GAAc,CAAC;QACjD,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;IAC1D,CAAC;IAEO,SAAS,CAAC,KAAe;QAC/B,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC/E,CAAC;IAEO,aAAa,CAAC,KAAe,EAAE,OAAe,EAAE,OAAiB;QACvE,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,gBAAgB,GAAG,IAAI,SAAS,KAAK,WAAW,IAAI,OAAO,EAAE,CAAC;QAElE,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,YAAY,KAAK,EAAE,CAAC;gBAC7B,gBAAgB,IAAI,cAAc,OAAO,CAAC,KAAK,IAAI,0BAA0B,EAAE,CAAC;YAClF,CAAC;iBAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBACvC,IAAI,CAAC;oBACH,gBAAgB,IAAI,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;gBACzE,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,gBAAgB,IAAI,6CAA6C,CAAC;gBACpE,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,gBAAgB,IAAI,gBAAgB,OAAO,EAAE,CAAC;YAChD,CAAC;QACH,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEO,GAAG,CAAC,KAAe,EAAE,OAAe,EAAE,OAAiB;QAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACrE,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAEjD,qCAAqC;YACrC,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;gBACtB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,OAAiB;QACtC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,OAAiB;QACrC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,OAAiB;QACrC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,OAAiB;QACtC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED,IAAI;QACF,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK;QACH,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC;IAC9B,CAAC;IAED,OAAO;QACL,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;IAChC,CAAC;;AAtGH,wBAuGC;AAED,uDAAuD;AAC1C,QAAA,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { getLogLevel } from './config';\n\nexport type LogLevel = 'debug' | 'info' | 'warn' | 'error';\n\nconst LOG_LEVEL_PRIORITY: Record<LogLevel, number> = {\n  debug: 0,\n  info: 1,\n  warn: 2,\n  error: 3\n};\n\nexport class Logger {\n  private static _instance: Logger | null = null;\n  private outputChannel: vscode.OutputChannel;\n  private currentLogLevel: LogLevel;\n\n  private configChangeDisposable: vscode.Disposable | undefined;\n\n  public static initialize(context: vscode.ExtensionContext): void {\n    const instance = Logger.instance;\n    // Store the config change subscription in the extension context's subscriptions\n    if (instance.configChangeDisposable) {\n      instance.configChangeDisposable.dispose();\n    }\n    instance.configChangeDisposable = vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.logLevel')) {\n        instance.updateLogLevel();\n      }\n    });\n    context.subscriptions.push(instance.configChangeDisposable);\n  }\n\n  private constructor() {\n    this.outputChannel = vscode.window.createOutputChannel('Codessa');\n    this.currentLogLevel = getLogLevel() as LogLevel;\n  }\n\n  public static get instance(): Logger {\n    if (!Logger._instance) {\n      Logger._instance = new Logger();\n    }\n    return Logger._instance;\n  }\n\n  private updateLogLevel(): void {\n    this.currentLogLevel = getLogLevel() as LogLevel;\n    this.debug(`Log level set to: ${this.currentLogLevel}`);\n  }\n\n  private shouldLog(level: LogLevel): boolean {\n    return LOG_LEVEL_PRIORITY[level] >= LOG_LEVEL_PRIORITY[this.currentLogLevel];\n  }\n\n  private formatMessage(level: LogLevel, message: string, details?: unknown): string {\n    const timestamp = new Date().toISOString();\n    const levelPadded = level.toUpperCase().padEnd(5);\n    let formattedMessage = `[${timestamp}] ${levelPadded} ${message}`;\n\n    if (details) {\n      if (details instanceof Error) {\n        formattedMessage += `\\n  Stack: ${details.stack || 'No stack trace available'}`;\n      } else if (typeof details === 'object') {\n        try {\n          formattedMessage += `\\n  Details: ${JSON.stringify(details, null, 2)}`;\n        } catch (e) {\n          formattedMessage += '\\n  Details: [Object cannot be stringified]';\n        }\n      } else {\n        formattedMessage += `\\n  Details: ${details}`;\n      }\n    }\n\n    return formattedMessage;\n  }\n\n  private log(level: LogLevel, message: string, details?: unknown): void {\n    if (this.shouldLog(level)) {\n      const formattedMessage = this.formatMessage(level, message, details);\n      this.outputChannel?.appendLine(formattedMessage);\n\n      // For errors, also show notification\n      if (level === 'error') {\n        vscode.window.showErrorMessage(message);\n      }\n    }\n  }\n\n  debug(message: string, details?: unknown): void {\n    this.log('debug', message, details);\n  }\n\n  info(message: string, details?: unknown): void {\n    this.log('info', message, details);\n  }\n\n  warn(message: string, details?: unknown): void {\n    this.log('warn', message, details);\n  }\n\n  error(message: string, details?: unknown): void {\n    this.log('error', message, details);\n  }\n\n  show(): void {\n    this.outputChannel?.show();\n  }\n\n  clear(): void {\n    this.outputChannel?.clear();\n  }\n\n  dispose(): void {\n    this.outputChannel?.dispose();\n  }\n}\n\n// Export logger instance after Logger class is defined\nexport const logger = Logger.instance;\n"]}