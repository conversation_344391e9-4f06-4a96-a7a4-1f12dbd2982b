{"version": 3, "file": "credentialsManager.js", "sourceRoot": "", "sources": ["../../src/credentials/credentialsManager.ts"], "names": [], "mappings": ";;;AACA,sCAAmC;AAEnC;;;GAGG;AACH,MAAa,kBAAkB;IACrB,MAAM,CAAC,QAAQ,CAAqB;IACpC,aAAa,CAAuB;IAC3B,SAAS,GAAG,sBAAsB,CAAC;IAEpD,YAAoB,OAAgC;QAClD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC;IACvC,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,WAAW,CAAC,OAAiC;QACzD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;YACjF,CAAC;YACD,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACrC,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,eAAe,CAAC,UAAkB,EAAE,GAAW,EAAE,KAAa;QACzE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACvD,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAClD,eAAM,CAAC,IAAI,CAAC,yBAAyB,UAAU,IAAI,GAAG,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,UAAU,IAAI,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,+BAA+B,YAAY,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;;;;SAKK;IACE,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,GAAW;QACxD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACvD,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,IAAI,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/E,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,gBAAgB,CAAC,UAAkB,EAAE,GAAW;QAC3D,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACvD,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC5C,eAAM,CAAC,IAAI,CAAC,0BAA0B,UAAU,IAAI,GAAG,EAAE,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,UAAU,IAAI,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,GAAW;QACxD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACxD,OAAO,KAAK,KAAK,SAAS,CAAC;IAC7B,CAAC;IAED;;SAEK;IACG,aAAa,CAAC,UAAkB,EAAE,GAAW;QACnD,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,IAAI,GAAG,EAAE,CAAC;IACjD,CAAC;CACF;AAzFD,gDAyFC;AAED,4BAA4B;AACf,QAAA,kBAAkB,GAAG;IAChC,WAAW,EAAE,kBAAkB,CAAC,WAAW;CAC5C,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { logger } from '../logger';\n\n/**\n * Manages secure storage and retrieval of API credentials\n * using VS Code's built-in SecretStorage API\n */\nexport class CredentialsManager {\n  private static instance: CredentialsManager;\n  private secretStorage: vscode.SecretStorage;\n  private readonly keyPrefix = 'codessa.credentials.';\n\n  private constructor(context: vscode.ExtensionContext) {\n    this.secretStorage = context.secrets;\n  }\n\n  /**\n     * Get the singleton instance of CredentialsManager\n     */\n  public static getInstance(context?: vscode.ExtensionContext): CredentialsManager {\n    if (!CredentialsManager.instance) {\n      if (!context) {\n        throw new Error('CredentialsManager must be initialized with a context first');\n      }\n      CredentialsManager.instance = new CredentialsManager(context);\n    }\n    return CredentialsManager.instance;\n  }\n\n  /**\n     * Store a credential securely\n     * @param providerId The ID of the provider (e.g., 'openai', 'anthropic')\n     * @param key The key name (e.g., 'api<PERSON>ey', 'organizationId')\n     * @param value The value to store\n     */\n  public async storeCredential(providerId: string, key: string, value: string): Promise<void> {\n    try {\n      const storageKey = this.getStorageKey(providerId, key);\n      await this.secretStorage.store(storageKey, value);\n      logger.info(`Stored credential for ${providerId}.${key}`);\n    } catch (error) {\n      logger.error(`Failed to store credential for ${providerId}.${key}:`, error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Failed to store credential: ${errorMessage}`);\n    }\n  }\n\n  /**\n     * Retrieve a credential\n     * @param providerId The ID of the provider\n     * @param key The key name\n     * @returns The stored value, or undefined if not found\n     */\n  public async getCredential(providerId: string, key: string): Promise<string | undefined> {\n    try {\n      const storageKey = this.getStorageKey(providerId, key);\n      return await this.secretStorage.get(storageKey);\n    } catch (error) {\n      logger.error(`Failed to retrieve credential for ${providerId}.${key}:`, error);\n      return undefined;\n    }\n  }\n\n  /**\n     * Delete a stored credential\n     * @param providerId The ID of the provider\n     * @param key The key name\n     */\n  public async deleteCredential(providerId: string, key: string): Promise<void> {\n    try {\n      const storageKey = this.getStorageKey(providerId, key);\n      await this.secretStorage.delete(storageKey);\n      logger.info(`Deleted credential for ${providerId}.${key}`);\n    } catch (error) {\n      logger.error(`Failed to delete credential for ${providerId}.${key}:`, error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Failed to delete credential: ${errorMessage}`);\n    }\n  }\n\n  /**\n     * Check if a credential exists\n     * @param providerId The ID of the provider\n     * @param key The key name\n     */\n  public async hasCredential(providerId: string, key: string): Promise<boolean> {\n    const value = await this.getCredential(providerId, key);\n    return value !== undefined;\n  }\n\n  /**\n     * Generate the storage key for a credential\n     */\n  private getStorageKey(providerId: string, key: string): string {\n    return `${this.keyPrefix}${providerId}.${key}`;\n  }\n}\n\n// Export singleton instance\nexport const credentialsManager = {\n  getInstance: CredentialsManager.getInstance\n};\n"]}