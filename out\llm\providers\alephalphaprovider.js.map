{"version": 3, "file": "alephalphaprovider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/alephalphaprovider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAGpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;GAEG;AACH,MAAa,kBAAmB,SAAQ,iCAAe;IAC5C,UAAU,GAAG,YAAY,CAAC;IAC1B,WAAW,GAAG,aAAa,CAAC;IAC5B,WAAW,GAAG,mDAAmD,CAAC;IAClE,OAAO,GAAG,8BAA8B,CAAC;IACzC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,gCAAgC,CAAC;IACnD,YAAY,GAAG,kBAAkB,CAAC;IAEnC,MAAM,GAAQ,IAAI,CAAC;IAE3B,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;gBAC1E,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,EAAE,qBAAqB;gBACrC,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,MAAM,EAAE;oBACnC,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,wDAAwD,EAAE,CAAC;QAC1F,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,yBAAyB;YACzB,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;YACzD,CAAC;YAED,oCAAoC;YACpC,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC5B,eAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,IAAI,kBAAkB,CAAC,CAAC;YACzE,CAAC;YAED,qBAAqB;YACrB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,MAAM,CAAC;YACzC,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,UAAU,OAAO,CAAC,OAAO,MAAM,CAAC;oBAC5C,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC;oBACzC,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,iDAAiD;oBACnD,CAAC;gBACH,CAAC;gBAED,8BAA8B;gBAC9B,MAAM,IAAI,MAAM,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;YAC1B,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;wBACrD,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBACvG,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnD,KAAK,EAAE,OAAO;gBACd,MAAM,EAAE,MAAM;gBACd,cAAc,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG;gBACvC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,KAAK,EAAE,IAAI;gBACX,cAAc,EAAE,MAAM,CAAC,aAAa,IAAI,EAAE;aAC3C,EAAE;gBACD,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,qBAAqB;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE;gBAC/C,YAAY,EAAE,MAAM;gBACpB,KAAK,EAAE;oBACL,YAAY,EAAE,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;oBAC1E,gBAAgB,EAAE,MAAM,CAAC,iBAAiB,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;iBAC7G;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,IAAI,YAAY,GAAG,iCAAiC,CAAC;YAErD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,0BAA0B,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACpH,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,6EAA6E;QAC7E,OAAO;YACL;gBACE,EAAE,EAAE,kBAAkB;gBACtB,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,sCAAsC;gBACnD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,2CAA2C;gBACxD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,mCAAmC;gBAChD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;YACD;gBACE,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,uCAAuC;gBACpD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,MAAM;aACpB;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gEAAgE;aAC1E,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnD,KAAK,EAAE,OAAO;gBACd,MAAM,EAAE,eAAe;gBACvB,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC/C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,+DAA+D,OAAO,IAAI;iBACpF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uDAAuD;iBACjE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,YAAY,GAAG,sCAAsC,CAAC;YAE1D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,qEAAqE;gBAClF,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,wEAAwE;gBACrF,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,sCAAsC;gBACnD,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,kBAAkB;oBAClB,mBAAmB;oBACnB,eAAe;oBACf,gBAAgB;iBACjB;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAjSD,gDAiSC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { BaseLL<PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for Aleph Alpha models\n */\nexport class AlephAlphaProvider extends BaseLLMProvider {\n  readonly providerId = 'alephalpha';\n  readonly displayName = 'Aleph Alpha';\n  readonly description = 'European AI models with multilingual capabilities';\n  readonly website = 'https://www.aleph-alpha.com/';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api.aleph-alpha.com/v1';\n  readonly defaultModel = 'luminous-supreme';\n\n  private client: any = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        logger.info('Aleph Alpha configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (!apiKey) {\n      logger.warn('API key not set for Aleph Alpha provider.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 60000, // 60 seconds timeout\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      logger.info('Aleph Alpha client initialized successfully.');\n    } catch (error) {\n      logger.error('Failed to initialize Aleph Alpha client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using Aleph Alpha models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'Aleph Alpha provider not configured (API key missing?)' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Check for cancellation\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request was cancelled' };\n      }\n\n      // Log available tools for debugging\n      if (tools && tools.size > 0) {\n        logger.debug(`Aleph Alpha provider has ${tools.size} tools available`);\n      }\n\n      // Prepare the prompt\n      let prompt = '';\n            \n      if (params.systemPrompt) {\n        prompt += `${params.systemPrompt}\\n\\n`;\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            prompt += `Human: ${message.content}\\n\\n`;\n          } else if (message.role === 'assistant') {\n            prompt += `AI: ${message.content}\\n\\n`;\n          } else if (message.role === 'system') {\n            // System messages already added at the beginning\n          }\n        }\n                \n        // Add the final prompt marker\n        prompt += 'AI: ';\n      } else {\n        // Just add the user prompt\n        prompt += params.prompt;\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            logger.info('Aleph Alpha request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          logger.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request\n      const response = await this.client.post('/complete', {\n        model: modelId,\n        prompt: prompt,\n        maximum_tokens: params.maxTokens || 256,\n        temperature: params.temperature || 0.7,\n        top_p: 0.95,\n        stop_sequences: params.stopSequences || []\n      }, {\n        signal: abortController?.signal\n      });\n\n      // Parse the response\n      const result = response.data;\n            \n      return {\n        content: result.completions[0].completion || '',\n        finishReason: 'stop',\n        usage: {\n          promptTokens: result.prompt_tokens || prompt.length / 4, // Rough estimate\n          completionTokens: result.completion_tokens || result.completions[0].completion.length / 4, // Rough estimate\n        }\n      };\n    } catch (error: any) {\n      logger.error('Aleph Alpha generate error:', error);\n      let errorMessage = 'Failed to call Aleph Alpha API.';\n\n      if (error.response) {\n        errorMessage = `Aleph Alpha API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available Aleph Alpha models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    // Aleph Alpha doesn't have a models endpoint, so we return a predefined list\n    return [\n      {\n        id: 'luminous-supreme',\n        name: 'Luminous Supreme',\n        description: 'Most capable model for complex tasks',\n        contextWindow: 2048,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'luminous-extended',\n        name: 'Luminous Extended',\n        description: 'Balanced model with larger context window',\n        contextWindow: 4096,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'luminous-base',\n        name: 'Luminous Base',\n        description: 'Balanced model for most use cases',\n        contextWindow: 2048,\n        pricingInfo: 'Paid'\n      },\n      {\n        id: 'luminous-small',\n        name: 'Luminous Small',\n        description: 'Fastest and most cost-effective model',\n        contextWindow: 2048,\n        pricingInfo: 'Paid'\n      }\n    ];\n  }\n\n  /**\n     * Test connection to Aleph Alpha\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'Aleph Alpha client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Simple test request to check if the API is working\n      const response = await this.client.post('/complete', {\n        model: modelId,\n        prompt: 'Hello, world!',\n        maximum_tokens: 5,\n        temperature: 0.7\n      });\n\n      if (response.data && response.data.completions) {\n        return {\n          success: true,\n          message: `Successfully connected to Aleph Alpha API and tested model '${modelId}'.`\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Connected to API but received an unexpected response.'\n        };\n      }\n    } catch (error: any) {\n      logger.error('Aleph Alpha connection test failed:', error);\n      let errorMessage = 'Failed to connect to Aleph Alpha API';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your Aleph Alpha API key (from https://app.aleph-alpha.com/profile)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The Aleph Alpha API endpoint (default: https://api.aleph-alpha.com/v1)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default Aleph Alpha model to use',\n        required: false,\n        type: 'select',\n        options: [\n          'luminous-supreme',\n          'luminous-extended',\n          'luminous-base',\n          'luminous-small'\n        ]\n      }\n    ];\n  }\n}\n"]}