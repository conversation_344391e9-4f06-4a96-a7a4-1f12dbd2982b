{"version": 3, "file": "advancedTemplates.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/advancedTemplates.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAwmCH,8CAkIC;AAeD,kEAkQC;AAcD,8EAqbC;AAeD,oEAqTC;AAgBD,wDAiUC;AAjlFD,6BAAwB;AAIxB,mCAAkC;AAGlC,yDAAsD;AACtD,+DAA4D;AAC5D,yCAAsC;AACtC,sEAAmG;AAOnG,oCAAoC;AACpC,MAAe,QAAQ;IAQrB,sCAAsC;IACtC,KAAK,CAAC,OAAO;QACX,sCAAsC;IACxC,CAAC;CACF;AA6CD;;;GAGG;AACH,MAAe,mBAAoB,SAAQ,QAAQ;IAC9B,QAAQ,CAAQ;IACzB,QAAQ,CAAgB;IACxB,SAAS,CAAS;IAClB,kBAAkB,CAAU;IAC5B,gBAAgB,CAAU;IAQpC,YACE,QAAe,EACf,OAAe,cAAc,EAC7B,cAAsB,0BAA0B,EAChD,cAA6B;QAE7B,KAAK,EAAE,CAAC,CAAC,4BAA4B;QAErC,sBAAsB;QACtB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,4BAA4B;QAC5B,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;QAE/B,uCAAuC;QACvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,+CAA+C;QAC/C,IAAI,CAAC,kBAAkB,GAAG,QAAQ,IAAI,IAAI,CAAC,QAAQ;YACjD,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,SAAS;YAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC;QAE3C,oCAAoC;QACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,uCAAuC;QACvC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;IAKD,gDAAgD;IACtC,gBAAgB,CACxB,OAAgB,EAChB,MAAe,EACf,KAAc;QAEd,OAAO;YACL,OAAO;YACP,MAAM;YACN,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED,qBAAqB;IACrB,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,iBAAiB;IACjB,aAAa,CAAC,OAAqB;QACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAM,CAAC,KAAc,EAAE,UAA8B,EAAE;QAC3D,IAAI,CAAC;YACH,wCAAwC;YACxC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpB,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;wBAC9B,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC1C,CAAC;oBACD,MAAM,IAAI,KAAK,CAAC,4BAA4B,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACtE,CAAC;gBACD,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;YACtB,CAAC;YAED,6CAA6C;YAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC;YACjD,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC1C,CAAC;YACD,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUD;;;OAGG;IACH,aAAa,CAAC,OAAqB;QACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED;;;;;;;OAOG;IACH;;;;OAIG;IACO,KAAK,CAAC,KAAK,CAAC,KAAc;QAClC,IAAI,CAAC;YACH,yDAAyD;YACzD,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAChD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,gCAAgC,CAAC,CAAC;YAE1E,uCAAuC;YACvC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,sBAAsB,EAAE;oBAC9D,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS;oBACvC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS;oBACvC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB;iBACtD,CAAC,CAAC;YACL,CAAC;YAED,kDAAkD;YAClD,IAAI,MAAe,CAAC;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAe,CAAC;YAEtC,6CAA6C;YAC7C,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC1C,wCAAwC;gBACxC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC;oBAClC,CAAC,CAAC,MAAM,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC;oBACtD,CAAC,CAAC,MAAM,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC5C,CAAC;iBACI,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC7C,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;oBAChC,CAAC,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC;oBACpD,CAAC,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC1C,CAAC;iBACI,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBAChD,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC;oBACnC,CAAC,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC;oBACvD,CAAC,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7C,CAAC;iBACI,CAAC;gBACJ,MAAM,IAAI,KAAK,CACb,iBAAiB,IAAI,CAAC,IAAI,4CAA4C;oBACtE,iDAAiD,CAClD,CAAC;YACJ,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,0CAA0C,CAAC,CAAC;YAEpF,yDAAyD;YACzD,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEvC,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YAEpE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,gCAAgC,EAAE;gBACxE,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,KAAK;aACb,CAAC,CAAC;YAEH,0DAA0D;YAC1D,IAAI,KAAK,YAAY,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC3C,KAAa,CAAC,OAAO,GAAG;oBACvB,QAAQ,EAAE,IAAI,CAAC,SAAS;oBACxB,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI;oBAC5C,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS;oBACvC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC;YACJ,CAAC;YAED,wDAAwD;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACO,iBAAiB,CACzB,MAAe,EACf,UAAmC,EAAE;QAErC,MAAM,SAAS,GAAwB;YACrC,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,SAAS;YACtB,OAAO,EAAE,EAAE;YACX,OAAO,EAAE,EAAE;SACZ,CAAC;QAEF,iCAAiC;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAC5C,SAAS,CAAC,OAAO,GAAG;gBAClB,GAAG,SAAS,CAAC,OAAO;gBACpB,CAAC,SAAS,CAAC,EAAE,MAAM;gBACnB,GAAG,OAAO;aACX,CAAC;QACJ,CAAC;QAED,4EAA4E;QAC5E,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,MAAM,cAAc,GAAG,MAAM,CAAC;YAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAEhC,qEAAqE;YACrE,MAAM,WAAW,GAAgB;gBAC/B,QAAQ,EAAE,GAAG,EAAE,CAAC,MAAM;gBACtB,OAAO,EAAE,cAAc;gBACvB,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACrC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;oBACb,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE;wBACJ,OAAO,EAAE,cAAc;wBACvB,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACtC;iBACF,CAAC;aACuB,CAAC;YAE5B,SAAS,CAAC,QAAQ,GAAG;gBACnB,GAAG,CAAC,SAAS,CAAC,QAAQ,IAAI,EAAE,CAAC;gBAC7B,WAAW;aACZ,CAAC;QACJ,CAAC;QAED,OAAO,SAAuB,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,MAAe;QACtC,6DAA6D;QAC7D,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;YAChE,MAAM,KAAK,GAAG,MAA6B,CAAC;YAC5C,OAAO;gBACL,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE;gBAC9B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;gBAC1B,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,SAAS;gBAC3C,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;gBAC5B,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;gBAC5B,GAAG,KAAK;aACT,CAAC;QACJ,CAAC;QAED,4CAA4C;QAC5C,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;gBACpC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM;aACzB,CAAC,CAAC;QACL,CAAC;QAED,6CAA6C;QAC7C,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;gBACpC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM;gBACxB,GAAI,MAAkC;aACvC,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YACpC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM;YACxB,KAAK,EAAE,MAAM;SACd,CAAC,CAAC;IACL,CAAC;CACF;AAqCD;;GAEG;AACH,MAAM,+BAAgC,SAAQ,mBAAmB;IAC/D,iEAAiE;IACxD,OAAO,GAAwB;QACtC,OAAO,EAAE;YACP,WAAW,EAAE,sCAAsC;YACnD,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBAC9C,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;oBAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;oBAC7C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;oBAC7C,eAAe,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;oBACvC,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;oBACrC,cAAc,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;iBACvC,CAAC,CAAC,QAAQ,EAAE;aACd,CAAC;SACH;KACF,CAAC;IAEF,sCAAsC;IACpB,EAAE,CAAS;IACX,IAAI,CAAS;IACb,WAAW,CAAS;IACpB,IAAI,GAAqC,eAAe,CAAC;IAEnE,eAAe,GAAqB;QAC1C,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,GAAG;QACb,eAAe,EAAE,IAAI;QACrB,cAAc,EAAE,IAAI;KACrB,CAAC;IAEF;;;;;OAKG;IACa,KAAK,CAAC,MAAM,CAC1B,KAAc,EACd,UAA8B,EAAE;QAEhC,IAAI,CAAC;YACH,wCAAwC;YACxC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAuB,CAAC,CAAC;YACtD,CAAC;YAED,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IACD;;;OAGG;IACM,MAAM,GAAG,OAAC,CAAC,KAAK,CAAC;QACxB,sBAAsB;QACtB,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2CAA2C,CAAC;QAEhE,sCAAsC;QACtC,OAAC,CAAC,MAAM,CAAC;YACP,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2CAA2C,CAAC;YACvE,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;gBAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;qBAC1C,QAAQ,CAAC,uCAAuC,CAAC;gBACpD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;qBAC1C,QAAQ,CAAC,+BAA+B,CAAC;gBAC5C,eAAe,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;qBACpC,QAAQ,CAAC,sCAAsC,CAAC;gBACnD,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;qBAClC,QAAQ,CAAC,2CAA2C,CAAC;gBACxD,cAAc,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;qBACnC,QAAQ,CAAC,iEAAiE,CAAC;aAC/E,CAAC,CAAC,QAAQ,EAAE;SACd,CAAC;KACH,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,kDAAkD;QAClD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QACvC,CAAC;QACD,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH;;;;;;;OAOG;IACH,YACE,aAAoB,EACpB,OAAe,iCAAiC,EAChD,cAAsB,kEAAkE,EACxF,iBAA2C,EAAE,EAC7C,OAAsB;QAEtB,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAEjD,wDAAwD;QACxD,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,cAAc,EAAE,CAAC;QAEtE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,wDAAwD;QACxD,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,cAAc,EAAE,CAAC;QAEtE,IAAI,CAAC,CAAC,QAAQ,IAAI,aAAa,IAAI,MAAM,IAAI,aAAa,IAAI,SAAS,IAAI,aAAa,CAAC,EAAE,CAAC;YAC1F,eAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,6DAA6D,IAAI,CAAC,IAAI,IAAI;gBAC1E,iEAAiE,CAClE,CAAC;QACJ,CAAC;QAED,iDAAiD;QACjD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IAED;;;;;OAKG;IACK,mBAAmB,CACzB,WAAqC,EACrC,OAAsB;QAEtB,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,oEAAoE;QACpE,MAAM,eAAe,GAA6B;YAChD,GAAG,WAAW;YACd,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;SAC/D,CAAC;QAEF,+BAA+B;QAC/B,eAAe,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,IAAI,EAAE,CAAC;QAExD,uCAAuC;QACvC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClC,eAAe,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC;QACpC,CAAC;QAED,qCAAqC;QACrC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,4CAA4C;YAC5C,IAAI,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;gBAClC,eAAe,CAAC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC;YACnE,CAAC;YAED,gCAAgC;YAChC,IAAI,OAAO,CAAC,SAAS,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;gBAC/C,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;gBACvD,eAAe,CAAC,OAAO,CAAC,IAAI,GAAG;oBAC7B,GAAG,WAAW;oBACd,WAAW;oBACX,GAAG,OAAO,CAAC,SAAS,CAAC,gBAAgB;iBACtC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC;YACxD,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;iBACnD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;iBAC1D,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;YAEhC,eAAe,CAAC,OAAO,CAAC,IAAI,GAAG;gBAC7B,GAAG,WAAW;gBACd,GAAG,YAAY;aAChB,CAAC;QACJ,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACgB,KAAK,CAAC,KAAK,CAAC,KAA2D;QACxF,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;YACjC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAE7B,uDAAuD;YACvD,MAAM,aAAa,GAA6B;gBAC9C,GAAG,IAAI,CAAC,eAAe;gBACvB,GAAG,OAAO;gBACV,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,EAAE;aACzE,CAAC;YAEF,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAEzE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yCAAyC,KAAK,GAAG,EAAE;gBACvE,OAAO,EAAE,eAAe;gBACxB,UAAU,EAAE,CAAC,CAAC,OAAO;gBACrB,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI;aACxC,CAAC,CAAC;YAEH,kDAAkD;YAClD,MAAM,SAAS,GAAG;gBAChB,KAAK;gBACL,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;aACjF,CAAC;YAEF,mEAAmE;YACnE,IAAI,MAAe,CAAC;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAe,CAAC;YAEtC,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC1C,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC;oBAClC,CAAC,CAAC,MAAM,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC;oBAC3C,CAAC,CAAC,MAAM,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC/C,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;oBAChC,CAAC,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC;oBACzC,CAAC,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;iBAAM,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBAClD,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC;oBACnC,CAAC,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;oBAC5C,CAAC,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;YAED,mCAAmC;YACnC,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;QAEtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,uBAAuB,CACnC,MAAe,EACf,aAAqB,EACrB,OAAiC;QAEjC,IAAI,CAAC;YACH,qCAAqC;YACrC,IAAI,SAAS,GAAmE,EAAE,CAAC;YAEnF,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1B,4BAA4B;gBAC5B,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;oBAC3B,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;wBAC5B,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;oBAC1B,CAAC;yBAAM,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,SAAS,IAAI,GAAG,EAAE,CAAC;wBAC9D,OAAO;4BACL,OAAO,EAAE,MAAM,CAAE,GAAW,CAAC,OAAO,CAAC;4BACrC,QAAQ,EAAG,GAAW,CAAC,QAAQ,IAAI,EAAE;yBACtC,CAAC;oBACJ,CAAC;oBACD,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1C,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAChD,0CAA0C;gBAC1C,MAAM,GAAG,GAAG,MAAiC,CAAC;gBAC9C,SAAS,GAAG,CAAC;wBACX,OAAO,EAAE,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;wBACrE,QAAQ,EAAE,UAAU,IAAI,GAAG,CAAC,CAAC,CAAE,GAAG,CAAC,QAAoC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;qBACnF,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACnD,0BAA0B;gBAC1B,SAAS,GAAG,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC5C,CAAC;YAED,yCAAyC;YACzC,IAAI,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;gBACtD,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAChD,CAAC;YAED,2CAA2C;YAC3C,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACnC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;oBACjC,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,CAAC;oBACvC,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;YACL,CAAC;YAED,mCAAmC;YACnC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC7B,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5D,CAAC;YAED,6BAA6B;YAC7B,MAAM,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBACtD,EAAE,EAAE,OAAO,KAAK,GAAG,CAAC,EAAE;gBACtB,OAAO,EAAE,GAAG,CAAC,OAAO;gBACpB,GAAG,CAAC,OAAO,CAAC,eAAe,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;aAC/E,CAAC,CAAC,CAAC;YAEJ,yDAAyD;YACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE;gBAC9C,SAAS,EAAE,gBAAgB;gBAC3B,KAAK,EAAE,gBAAgB,CAAC,MAAM;gBAC9B,KAAK,EAAE,aAAa;gBACpB,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE;oBACR,gBAAgB,EAAE,OAAO;oBACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAEnE,gEAAgE;YAChE,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;gBAClC,KAAK,EAAE,qCAAqC;gBAC5C,OAAO,EAAE,YAAY;gBACrB,QAAQ,EAAE;oBACR,KAAK,EAAE;wBACL,OAAO,EAAE,qCAAqC;wBAC9C,KAAK,EAAE,KAAK;wBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AAwBD;;GAEG;AACH,MAAa,oBAAqB,SAAQ,yBAAyB;IACjE,iEAAiE;IACxD,OAAO,GAAwB;QACtC,OAAO,EAAE;YACP,WAAW,EAAE,yBAAyB;YACtC,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBAC9C,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;oBAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;oBAC7C,eAAe,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;oBACvC,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;iBACtC,CAAC,CAAC,QAAQ,EAAE;aACd,CAAC;SACH;KACF,CAAC;IAEF,sCAAsC;IACpB,EAAE,CAAS;IACX,IAAI,CAAS;IACb,WAAW,CAAS;IACpB,IAAI,GAAqC,eAAe,CAAC;IAEnE,eAAe,CAAgB;IAEvC,2EAA2E;IAClE,MAAM,GAAG,OAAC,CAAC,KAAK,CAAC;QACxB,sBAAsB;QACtB,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;QAE/C,sCAAsC;QACtC,OAAC,CAAC,MAAM,CAAC;YACP,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;YACtD,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;gBAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;qBAC1C,QAAQ,CAAC,qCAAqC,CAAC;gBAClD,eAAe,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;qBACpC,QAAQ,CAAC,4CAA4C,CAAC;gBACzD,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;qBAClC,QAAQ,CAAC,mCAAmC,CAAC;aACjD,CAAC,CAAC,QAAQ,EAAE;SACd,CAAC;KACH,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,8DAA8D;QAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QACvC,CAAC;QACD,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH;;;;;;;OAOG;IACH,YACE,UAAiB,EACjB,OAAe,sBAAsB,EACrC,cAAsB,wEAAwE,EAC9F,iBAAgC,EAAE,EAClC,cAA6B;QAE7B,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,6BAA6B;QAC7B,IAAI,CAAC,eAAe,GAAG;YACrB,KAAK,EAAE,EAAE;YACT,eAAe,EAAE,IAAI;YACrB,GAAG,cAAc;SAClB,CAAC;QAEF,IAAI,CAAC,CAAC,QAAQ,IAAI,UAAU,IAAI,MAAM,IAAI,UAAU,IAAI,SAAS,IAAI,UAAU,CAAC,EAAE,CAAC;YACjF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,kDAAkD,IAAI,CAAC,IAAI,IAAI;gBAC/D,iEAAiE,CAClE,CAAC;QACJ,CAAC;QAED,iDAAiD;QACjD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IAED;;;;;OAKG;IACa,KAAK,CAAC,MAAM,CAC1B,KAAc,EACd,UAA8B,EAAE;QAEhC,IAAI,CAAC;YACH,wCAAwC;YACxC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAuB,CAAC,CAAC;YACtD,CAAC;YAED,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YACtE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,mBAAmB,CACzB,WAA0B,EAC1B,OAAsB;QAEtB,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,oEAAoE;QACpE,MAAM,eAAe,GAAkB;YACrC,GAAG,WAAW;YACd,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;SAC/D,CAAC;QAEF,+BAA+B;QAC/B,eAAe,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,IAAI,EAAE,CAAC;QAExD,qCAAqC;QACrC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,4CAA4C;YAC5C,IAAI,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;gBAClC,eAAe,CAAC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC;YACnE,CAAC;YAED,gCAAgC;YAChC,IAAI,OAAO,CAAC,SAAS,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;gBAC/C,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC;oBAC7D,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI;oBAC9B,CAAC,CAAC,EAAE,CAAC;gBAEP,eAAe,CAAC,OAAO,CAAC,IAAI,GAAG;oBAC7B,GAAG,WAAW;oBACd,WAAW;oBACX,GAAG,OAAO,CAAC,SAAS,CAAC,gBAAgB;iBACtC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC7D,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI;gBAC9B,CAAC,CAAC,EAAE,CAAC;YAEP,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;iBACnD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;iBAC1D,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;YAEhC,eAAe,CAAC,OAAO,CAAC,IAAI,GAAG;gBAC7B,GAAG,WAAW;gBACd,GAAG,YAAY;aAChB,CAAC;QACJ,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACgB,KAAK,CAAC,KAAK,CAAC,KAAgD;QAC7E,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;YACjC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAE7B,uDAAuD;YACvD,MAAM,aAAa,GAAkB;gBACnC,GAAG,IAAI,CAAC,eAAe;gBACvB,GAAG,OAAO;gBACV,OAAO,EAAE;oBACP,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI,EAAE,CAAC;oBACvC,GAAG,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;iBAC3B;aACF,CAAC;YAEF,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAEzE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,KAAK,GAAG,EAAE;gBAC5D,OAAO,EAAE,eAAe;gBACxB,UAAU,EAAE,CAAC,CAAC,OAAO;gBACrB,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI;aACxC,CAAC,CAAC;YAEH,kDAAkD;YAClD,MAAM,SAAS,GAAG;gBAChB,KAAK;gBACL,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;aACjF,CAAC;YAEF,mEAAmE;YACnE,IAAI,MAAe,CAAC;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAe,CAAC;YAEtC,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC1C,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC;oBAClC,CAAC,CAAC,MAAM,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC;oBAC3C,CAAC,CAAC,MAAM,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC/C,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;oBAChC,CAAC,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC;oBACzC,CAAC,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;iBAAM,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBAClD,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC;oBACnC,CAAC,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;oBAC5C,CAAC,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;YAED,mCAAmC;YACnC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;QAEnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,oBAAoB,CAChC,MAAe,EACf,aAAqB,EACrB,OAAsB;QAEtB,IAAI,CAAC;YACH,qCAAqC;YACrC,IAAI,KAAK,GAAmE,EAAE,CAAC;YAE/E,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1B,wBAAwB;gBACxB,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACxB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;oBAC3B,CAAC;yBAAM,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;wBACjE,OAAO;4BACL,OAAO,EAAE,MAAM,CAAE,IAAY,CAAC,OAAO,CAAC;4BACtC,QAAQ,EAAG,IAAY,CAAC,QAAQ,IAAI,EAAE;yBACvC,CAAC;oBACJ,CAAC;oBACD,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3C,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAChD,sCAAsC;gBACtC,MAAM,IAAI,GAAG,MAAiC,CAAC;gBAC/C,KAAK,GAAG,CAAC;wBACP,OAAO,EAAE,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;wBACxE,QAAQ,EAAE,UAAU,IAAI,IAAI,CAAC,CAAC,CAAE,IAAI,CAAC,QAAoC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;qBACrF,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACnD,0BAA0B;gBAC1B,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACxC,CAAC;YAED,yCAAyC;YACzC,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC;YAED,mCAAmC;YACnC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC7B,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YACpD,CAAC;YAED,6BAA6B;YAC7B,MAAM,gBAAgB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBACnD,EAAE,EAAE,UAAU,KAAK,GAAG,CAAC,EAAE;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,GAAG,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;aACjF,CAAC,CAAC,CAAC;YAEJ,yDAAyD;YACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE;gBAC9C,OAAO,EAAE,gBAAgB;gBACzB,KAAK,EAAE,gBAAgB,CAAC,MAAM;gBAC9B,KAAK,EAAE,aAAa;gBACpB,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE;oBACR,aAAa,EAAE,OAAO;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAEhE,gEAAgE;YAChE,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;gBAClC,KAAK,EAAE,kCAAkC;gBACzC,OAAO,EAAE,YAAY;gBACrB,QAAQ,EAAE;oBACR,KAAK,EAAE;wBACL,OAAO,EAAE,kCAAkC;wBAC3C,KAAK,EAAE,KAAK;wBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AApUD,oDAoUC;AAKD;;;;;;;;;GASG;AACH,SAAgB,iBAAiB,CAC/B,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,KAAY,EACZ,aAAoB;IAEpB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtE,CAAC;IACD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IAClE,CAAC;IACD,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IAClE,CAAC;IAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;IAE/D,4BAA4B;IAC5B,MAAM,YAAY,GAAc;QAC9B,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,oBAAoB;QAC3B,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,iDAAiD;QAC9D,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,IAAI,CAAC;gBACH,MAAM,EAAE,KAAK,EAAE,GAAG,KAA0B,CAAC;gBAC7C,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBAC9D,CAAC;gBAED,mEAAmE;gBACnE,IAAI,MAAM,CAAC;gBACX,IAAI,OAAQ,aAAqB,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;oBACxD,MAAM,GAAG,MAAO,aAAqB,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC1D,CAAC;qBAAM,IAAI,OAAQ,aAAqB,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC7D,MAAM,GAAG,MAAO,aAAqB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBACxD,CAAC;qBAAM,IAAI,OAAQ,aAAqB,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;oBAChE,MAAM,GAAG,MAAO,aAAqB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC3D,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;gBACvE,CAAC;gBAED,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBACvD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;KACF,CAAC;IAEF,MAAM,YAAY,GAAc;QAC9B,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,mBAAmB;QAC1B,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,oDAAoD;QACjE,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,IAAI,CAAC;gBACH,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,KAA8C,CAAC;gBAC5E,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;gBACtD,CAAC;gBAED,8CAA8C;gBAC9C,MAAM,MAAM,GAAG;oBACb,KAAK;oBACL,OAAO,EAAE,SAAS;iBACnB,CAAC;gBAEF,oEAAoE;gBACpE,IAAI,QAAQ,CAAC;gBACb,IAAI,OAAQ,KAAa,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;oBAChD,QAAQ,GAAG,MAAO,KAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACjD,CAAC;qBAAM,IAAI,OAAQ,KAAa,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBACrD,QAAQ,GAAG,MAAO,KAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/C,CAAC;qBAAM,IAAI,OAAQ,KAAa,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;oBACxD,QAAQ,GAAG,MAAO,KAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBAC9D,CAAC;gBAED,OAAO,EAAE,QAAQ,EAAE,CAAC;YACtB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBACvD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;KACF,CAAC;IAEF,0CAA0C;IAC1C,MAAM,KAAK,GAAgB;QACzB;YACE,MAAM,EAAE,UAAU;YAClB,MAAM,EAAE,UAAU;YAClB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,wCAAwC;YACrD,SAAS,EAAE,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS;SAC7C;QACD;YACE,MAAM,EAAE,UAAU;YAClB,MAAM,EAAE,UAAU;YAClB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,0BAA0B;YACvC,SAAS,EAAE,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC;SAChH;KACF,CAAC;IAEF,0CAA0C;IAC1C,OAAO;QACL,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;QACnC,KAAK;QACL,WAAW,EAAE,UAAU;QACvB,aAAa,EAAE,aAAa;QAC5B,IAAI,EAAE,CAAC,KAAK,EAAE,gCAAgC,EAAE,aAAa,CAAC;QAC9D,QAAQ,EAAE;YACR,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACjC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACvC;KACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,SAAgB,2BAA2B,CACzC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,gBAAiF,EACjF,eAAsB;IAEtB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW;QAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtG,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IACtH,IAAI,CAAC,eAAe;QAAE,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;IAElG,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;IAEzE,uBAAuB;IACvB,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAErE,MAAM,gBAAgB,GAAc;QAClC,EAAE,EAAE,eAAe,EAAE,UAAU;QAC/B,IAAI,EAAE,OAAO,EAAE,kBAAkB;QACjC,IAAI,EAAE,4BAA4B,EAAE,YAAY;QAChD,KAAK,EAAE,eAAe;QACtB,KAAK,EAAE,eAAe,EAAE,yCAAyC;QACjE,oDAAoD;QACpD,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACvE,sEAAsE;YACtE,kGAAkG;YAClG,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAClI,MAAM,SAAS,GAAG,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,SAAS,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAExI,8CAA8C;YAC9C,MAAM,MAAM,GAAG;;;6BAGQ,SAAS;;;cAGxB,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE,gBAAgB,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;aAO5G,CAAC;YAER,8BAA8B;YAC9B,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,sDAAsD;YAErH,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,cAAc,EAAE,CAAC,CAAC;YAEvE,qEAAqE;YACrE,OAAO;gBACL,oCAAoC;gBACpC,OAAO,EAAE,EAAE,eAAe,EAAE,cAAc,EAAE;gBAC5C,qDAAqD;gBACrD,gBAAgB,EAAE,cAAc,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,yBAAyB;gBAChF,yEAAyE;gBACzE,UAAU,EAAE,SAAS;aACtB,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,4CAA4C;IAC5C,MAAM,eAAe,GAAgB,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACrE,OAAO;YACL,EAAE,EAAE,cAAc,UAAU,CAAC,EAAE,EAAE,EAAE,oCAAoC;YACvE,IAAI,EAAE,OAAO,EAAE,kBAAkB;YACjC,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,mCAAmC;YAC1D,KAAK,EAAE,eAAe,UAAU,CAAC,IAAI,EAAE;YACvC,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,gCAAgC;YACzD,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAuB,EAAE;gBACrE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC;gBAChG,yDAAyD;gBACzD,MAAM,SAAS,GAAI,KAAK,CAAC,UAAqB,IAAK,KAAK,CAAC,MAAkC,EAAE,UAAoB,IAAI,qBAAqB,CAAC;gBAE3I,8CAA8C;gBAC9C,MAAM,MAAM,GAAG;2BACI,UAAU,CAAC,IAAI,qBAAqB,UAAU,CAAC,SAAS;6EACN,SAAS;;;;kBAIpE,CAAC;gBAEX,8BAA8B;gBAC9B,MAAM,kBAAkB,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,iCAAiC;gBAErG,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,cAAc,kBAAkB,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBAEjG,+DAA+D;gBAC/D,OAAO;oBACL,GAAG,KAAK,EAAE,wBAAwB;oBAClC,OAAO,EAAE;wBACP,GAAG,CAAC,KAAK,CAAC,OAAkC,IAAI,EAAE,CAAC;wBACnD,CAAC,cAAc,UAAU,CAAC,EAAE,EAAE,CAAC,EAAE,kBAAkB;qBACpD;oBACD,CAAC,uBAAuB,UAAU,CAAC,EAAE,EAAE,CAAC,EAAE,kBAAkB,EAAE,iCAAiC;oBAC/F,UAAU,EAAE,SAAS,EAAE,oCAAoC;oBAC3D,0EAA0E;iBAClD,CAAC,CAAC,gDAAgD;YAC9E,CAAC;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GAAc;QACjC,EAAE,EAAE,aAAa,EAAE,UAAU;QAC7B,IAAI,EAAE,OAAO,EAAE,oDAAoD;QACnE,IAAI,EAAE,kCAAkC,EAAE,YAAY;QACtD,KAAK,EAAE,qBAAqB;QAC5B,KAAK,EAAE,eAAe,EAAE,2CAA2C;QACnE,2CAA2C;QAC3C,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;YACrE,6EAA6E;YAC7E,MAAM,SAAS,GAAI,KAAK,CAAC,UAAqB,IAAI,qBAAqB,CAAC;YAExE,0DAA0D;YAC1D,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACtD,MAAM,QAAQ,GAAI,KAAK,CAAC,uBAAuB,IAAI,CAAC,EAAE,EAAE,CAAY,IAAI,sBAAsB,CAAC;gBAC/F,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC;YAClE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,KAAK,sBAAsB,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,6BAA6B;YAEvH,iEAAiE;YACjE,MAAM,MAAM,GAAG;;;6BAGQ,SAAS;;;cAGxB,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,SAAS,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;;;;aAInG,CAAC;YAER,kEAAkE;YAClE,MAAM,kBAAkB,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,iCAAiC;YAEpG,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YAExD,mDAAmD;YACnD,OAAO;gBACL,OAAO,EAAE,EAAE,aAAa,EAAE,kBAAkB,EAAE,EAAE,mBAAmB;gBACnE,cAAc,EAAE,kBAAkB,EAAE,8CAA8C;aACnF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,iBAAiB,GAAc;QACnC,EAAE,EAAE,sBAAsB,EAAE,4BAA4B;QACxD,IAAI,EAAE,OAAO,EAAE,iCAAiC;QAChD,IAAI,EAAE,sBAAsB,EAAE,YAAY;QAC1C,KAAK,EAAE,sBAAsB;QAC7B,kCAAkC;QAClC,OAAO,EAAE,KAAK,IAAkC,EAAE;YAChD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC9D,8EAA8E;YAC9E,kEAAkE;YAClE,MAAM,OAAO,GAAG,4FAA4F,CAAC;YAC7G,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACjD,OAAO;gBACL,OAAO,EAAE,EAAE,sBAAsB,EAAE,OAAO,EAAE;gBAC5C,cAAc,EAAE,OAAO,EAAE,kCAAkC;aAC5D,CAAC;QACJ,CAAC;KACF,CAAC;IAGF,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IAEzE,uBAAuB;IACvB,MAAM,KAAK,GAAgB;QACzB,wCAAwC;QACxC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,gBAAgB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,uBAAuB,EAAE;KACtG,CAAC;IAEF,iEAAiE;IACjE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC9B,KAAK,CAAC,IAAI,CAAC;YACT,MAAM,EAAE,gBAAgB,CAAC,EAAE;YAC3B,MAAM,EAAE,cAAc,IAAI,CAAC,EAAE,EAAE;YAC/B,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,8BAA8B,IAAI,CAAC,EAAE,EAAE;YAC7C,SAAS,EAAE,KAAK,EAAE,KAA8B,EAAE,EAAE;gBAClD,MAAM,eAAe,GAAI,KAAK,CAAC,gBAA2B,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;gBAChF,OAAO,eAAe,KAAK,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YACnD,CAAC;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,oEAAoE;IACpE,KAAK,CAAC,IAAI,CAAC;QACT,MAAM,EAAE,gBAAgB,CAAC,EAAE;QAC3B,MAAM,EAAE,eAAe,CAAC,EAAE;QAC1B,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,6BAA6B;QACjC,SAAS,EAAE,KAAK,EAAE,KAA8B,EAAE,EAAE;YAClD,MAAM,eAAe,GAAI,KAAK,CAAC,gBAA2B,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YAChF,OAAO,eAAe,KAAK,aAAa,CAAC;QAC3C,CAAC;KACJ,CAAC,CAAC;IAEH,2EAA2E;IAC3E,KAAK,CAAC,IAAI,CAAC;QACT,MAAM,EAAE,gBAAgB,CAAC,EAAE;QAC3B,MAAM,EAAE,iBAAiB,CAAC,EAAE;QAC5B,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,+BAA+B;QACnC,SAAS,EAAE,KAAK,EAAE,KAA8B,EAAE,EAAE;YAClD,MAAM,eAAe,GAAI,KAAK,CAAC,gBAA2B,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YAChF,OAAO,eAAe,KAAK,sBAAsB,CAAC;QACpD,CAAC;KACJ,CAAC,CAAC;IAGH,+DAA+D;IAC/D,6EAA6E;IAC7E,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC9B,KAAK,CAAC,IAAI,CAAC;YACT,MAAM,EAAE,cAAc,IAAI,CAAC,EAAE,EAAE;YAC/B,MAAM,EAAE,eAAe,CAAC,EAAE;YAC1B,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,cAAc,IAAI,CAAC,EAAE,iBAAiB;SAC7C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,qEAAqE;IACrE,KAAK,CAAC,IAAI,CACR,EAAE,MAAM,EAAE,eAAe,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,uBAAuB,EAAE,EACrG,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,yBAAyB,EAAE,CAC1G,CAAC;IAGF,qCAAqC;IACrC,MAAM,kBAAkB,GAAoB;QAC1C,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,aAAa,EAAE,2CAA2C;QACzE,KAAK,EAAE;YACL,SAAS;YACT,gBAAgB;YAChB,GAAG,eAAe,EAAE,mDAAmD;YACvE,eAAe;YACf,iBAAiB,EAAE,6BAA6B;YAChD,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,wCAAwC;KACnE,CAAC;IAEF,+BAA+B;IAC/B,mCAAgB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACtD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,IAAI,MAAM,EAAE,2BAA2B,CAAC,CAAC;IAEzF,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,iCAAiC,CAC/C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,KAAY;IAEZ,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW;QAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtG,IAAI,CAAC,KAAK;QAAE,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;IAExF,0CAA0C;IAC1C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4CAA4C,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;IAEjF,uBAAuB;IACvB,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAErE,sBAAsB;IACtB,MAAM,mBAAmB,GAAU;QACjC,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,iEAAiE;QAC9E,IAAI,EAAE,eAAe;QACrB,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;YAC3B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;SAChE,CAAC;QACF,OAAO,EAAE,KAAK,EAAE,WAA+B,EAAE,KAAgB,EAAE,QAAuB,EAAuB,EAAE;YACjH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,wDAAwD;gBACxD,MAAM,UAAU,GAAiB;oBAC/B,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,CAAC;oBACjD,IAAI,EAAE,EAAE;oBACR,QAAQ,EAAE,SAAS;oBACnB,UAAU,EAAE,SAAS;iBACtB,CAAC;gBAEF,2CAA2C;gBAC3C,MAAM,aAAa,GAAwB;oBACzC,KAAK;oBACL,KAAK,EAAE,EAAE;oBACT,SAAS,EAAE,GAAG;oBACd,eAAe,EAAE,IAAI;oBACrB,UAAU,EAAE,IAAI;oBAChB,MAAM,EAAE,UAAU;iBACnB,CAAC;gBAEF,0CAA0C;gBAC1C,MAAM,YAAY,GAAG,CAAC,OAA8B,EAAE,EAAE;oBACtD,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;wBAC1B,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC;oBACpC,CAAC;oBACD,aAAa,CAAC,MAAM,GAAG,EAAE,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC;gBACjE,CAAC,CAAC;gBAEF,gFAAgF;gBAChF,IAAI,QAAQ,EAAE,SAAS,EAAE,CAAC;oBACxB,uCAAuC;oBACvC,IAAI,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;wBACnC,YAAY,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;oBAC7D,CAAC;oBAED,kDAAkD;oBAClD,IAAI,QAAQ,CAAC,SAAS,CAAC,gBAAgB,IAAI,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC1F,YAAY,CAAC;4BACX,IAAI,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;yBACnG,CAAC,CAAC;oBACL,CAAC;oBAED,8CAA8C;oBAC9C,IAAI,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC;wBACvC,2CAA2C;wBAC3C,aAAa,CAAC,KAAK,GAAG,GAAG,KAAK,IAAI,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;oBACxE,CAAC;gBACH,CAAC;gBAED,qEAAqE;gBACrE,IAAI,QAAQ,EAAE,QAAQ,EAAE,CAAC;oBACvB,YAAY,CAAC;wBACX,QAAQ,EAAE,QAAQ,CAAC,QAAQ;wBAC3B,IAAI,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE,cAAc,CAAC;qBAC9D,CAAC,CAAC;gBACL,CAAC;gBAED,uDAAuD;gBACvD,IAAI,QAAQ,EAAE,SAAS,EAAE,CAAC;oBACxB,IAAI,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;wBACjC,oDAAoD;wBACpD,aAAa,CAAC,KAAK,GAAG,GAAG,KAAK,WAAW,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;wBAChF,YAAY,CAAC;4BACX,IAAI,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC;yBACxE,CAAC,CAAC;oBACL,CAAC;oBAED,gDAAgD;oBAChD,IAAI,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;wBACzC,YAAY,CAAC;4BACX,aAAa,EAAE,QAAQ,CAAC,SAAS,CAAC,iBAAiB,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,2BAA2B;4BACxG,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;yBACxB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,6DAA6D;gBAC7D,IAAI,QAAQ,EAAE,SAAS,EAAE,CAAC;oBACxB,MAAM,WAAW,GAAG,CAAC,mBAAmB,CAAC,CAAC;oBAE1C,kDAAkD;oBAClD,IAAI,QAAQ,CAAC,SAAS,CAAC,eAAe;wBAAE,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAC7E,IAAI,QAAQ,CAAC,SAAS,CAAC,UAAU;wBAAE,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBACnE,IAAI,QAAQ,CAAC,SAAS,CAAC,aAAa;wBAAE,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBACzE,IAAI,QAAQ,CAAC,SAAS,CAAC,oBAAoB;wBAAE,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBAEnF,+CAA+C;oBAC/C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;wBAC5C,IAAI,CAAC,CAAC,iBAAiB,EAAE,YAAY,EAAE,eAAe,EAAE,sBAAsB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BAC9F,WAAW,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;wBACjC,CAAC;oBACH,CAAC,CAAC,CAAC;oBAEH,YAAY,CAAC;wBACX,IAAI,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,WAAW,CAAC;qBAC9D,CAAC,CAAC;gBACL,CAAC;gBAED,qCAAqC;gBACrC,IAAI,QAAQ,EAAE,gBAAgB,EAAE,CAAC;oBAC/B,YAAY,CAAC;wBACX,IAAI,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE,mBAAmB,EAAE,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;qBACvG,CAAC,CAAC;oBAEH,8CAA8C;oBAC9C,IAAI,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;wBACzE,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,4BAA4B;4BACnC,MAAM,EAAE,kBAAkB;yBAC3B,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,4CAA4C;gBAC5C,IAAI,QAAQ,EAAE,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;oBAC/C,YAAY,CAAC;wBACX,IAAI,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE,eAAe,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;qBACrG,CAAC,CAAC;gBACL,CAAC;gBAED,sCAAsC;gBACtC,IAAI,QAAQ,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;oBACzD,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,4BAA4B;wBACnC,MAAM,EAAE,kBAAkB;qBAC3B,CAAC;gBACJ,CAAC;gBAED,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;gBAC1E,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBACrC,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;oBAC5B,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;gBACvL,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEhB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,aAAa;oBACrB,MAAM,EAAE,kBAAkB;oBAC1B,QAAQ,EAAE;wBACR,aAAa,EAAE,QAAQ,CAAC,MAAM;wBAC9B,WAAW,EAAE;4BACX,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,SAAS;4BAChC,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,SAAS;4BAChC,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,SAAS;4BAChC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,QAAQ;4BAC9B,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,gBAAgB;4BACvC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,KAAK;yBACzB;qBACF;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBAC3D,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,6BAA6B;oBACpC,MAAM,EAAE,kBAAkB;iBAC3B,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;IAEF,MAAM,cAAc,GAAU;QAC5B,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,yBAAyB;QACtC,IAAI,EAAE,eAAe;QACrB,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;YAC3B,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+BAA+B,CAAC;YAC7D,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,gDAAgD,CAAC;SAC9G,CAAC;QACF,OAAO,EAAE,KAAK,EAAE,WAA+B,EAAE,KAAgB,EAAE,QAAuB,EAAuB,EAAE;YACjH,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;YAEtC,+CAA+C;YAC/C,MAAM,gBAAgB,GAAG;gBACvB,MAAM,EAAE,QAAwB;gBAChC,IAAI,EAAE,MAAoB;gBAC1B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,uCAAuC;gBACvC,GAAG,CAAC,CAAC,CAAC,QAAQ,IAAI;oBAChB,UAAU,EAAE,IAAI;oBAChB,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE,IAAI,IAAI,cAAc;iBAC1D,CAAC;gBACF,GAAG,QAAQ;aACZ,CAAC;YAEF,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBAClD,OAAO;oBACP,QAAQ,EAAE,gBAAgB;iBAC3B,CAAC,CAAC;gBACH,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,sCAAsC,MAAM,CAAC,EAAE,EAAE;oBACzD,MAAM,EAAE,cAAc;iBACvB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBACrD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB;oBAC9B,MAAM,EAAE,cAAc;iBACvB,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;IAEF,oDAAoD;IACpD,MAAM,6BAA6B,GAAG,mBAAmB,YAAY,cAAc;QACjF,CAAC,CAAC,mBAAmB;QACrB,+DAA+D;QAC/D,CAAC,CAAC,IAAI,CAAC,KAAM,SAAQ,iBAAiB;YAC3B,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAChF,YAAY,QAAsB,IAAI,KAAK,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC;SAC7E,CAAC,CAAC,mBAAmB,CAAC,CAAC;IAE1B,MAAM,wBAAwB,GAAG,cAAc,YAAY,cAAc;QACvE,CAAC,CAAC,cAAc;QAChB,+DAA+D;QAC/D,CAAC,CAAC,IAAI,CAAC,KAAM,SAAQ,iBAAiB;YAC3B,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACxG,YAAY,QAAsB,IAAI,KAAK,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;SACzE,CAAC,CAAC,cAAc,CAAC,CAAC;IAGrB,MAAM,mBAAmB,GAAG,eAAO,CAAC,cAAc,CAChD,kBAAkB,EAAE,UAAU;IAC9B,kBAAkB,EAAE,YAAY;IAChC,6BAA6B,CAAC,4CAA4C;KAC3E,CAAC;IAEF,MAAM,sBAAsB,GAAc;QACxC,EAAE,EAAE,qBAAqB,EAAE,UAAU;QACrC,IAAI,EAAE,MAAM,EAAE,gCAAgC;QAC9C,IAAI,EAAE,+BAA+B,EAAE,YAAY;QACnD,KAAK,EAAE,iBAAiB;QACxB,mCAAmC;QACnC,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;YAC7D,4DAA4D;YAC5D,MAAM,iBAAiB,GAAI,KAAK,CAAC,OAAmC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,uBAAuB;YAEnH,gEAAgE;YAChE,IAAI,eAAe,GAAG,EAAE,CAAC;YACzB,IAAI,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1E,eAAe,GAAG;;;GAGvB,iBAAiB;;;EAGlB,CAAC;YACG,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5E,sFAAsF;gBACtF,eAAe,GAAG;;;GAGvB,iBAAiB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;oBAC9B,MAAM,OAAO,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAE,GAA+B,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACvL,OAAO,UAAU,KAAK,GAAG,CAAC,KAAK,OAAO,EAAE,CAAC;gBAC3C,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;;;EAGrB,CAAC;YACG,CAAC;iBAAM,CAAC;gBACN,eAAe,GAAG,EAAE,CAAC,CAAC,2CAA2C;gBACjE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;YAC/E,CAAC;YAED,6DAA6D;YAC7D,OAAO;gBACL,gBAAgB,EAAE,eAAe,EAAE,0BAA0B;gBAC7D,wEAAwE;gBACxE,OAAO,EAAE,eAAe;aACzB,CAAC;QACJ,CAAC;KACF,CAAC;IAGF,MAAM,SAAS,GAAc;QAC3B,EAAE,EAAE,OAAO,EAAE,UAAU;QACvB,IAAI,EAAE,OAAO,EAAE,kBAAkB;QACjC,IAAI,EAAE,cAAc,EAAE,YAAY;QAClC,KAAK,EAAE,cAAc;QACrB,KAAK,EAAE,KAAK,EAAE,qBAAqB;QACnC,8BAA8B;QAC9B,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC/D,uDAAuD;YACvD,MAAM,QAAQ,GAAI,KAAK,CAAC,QAAsB,IAAI,EAAE,CAAC;YACrD,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAA4B,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1G,MAAM,SAAS,GAAG,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,SAAS,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAExI,MAAM,eAAe,GAAI,KAAK,CAAC,gBAA2B,IAAK,KAAK,CAAC,OAAkB,IAAI,EAAE,CAAC,CAAC,yBAAyB;YAExH,yDAAyD;YACzD,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,sBAAsB,EAAE;gBAChE,eAAe;gBACf,SAAS;aACV,CAAC,CAAC;YAEH,uCAAuC;YACvC,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,iCAAiC;YAErF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;YAE1D,0DAA0D;YAC1D,OAAO;gBACL,OAAO,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE;gBACjC,cAAc,EAAE,aAAa;aAC9B,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,cAAc,GAAG,eAAO,CAAC,cAAc,CAC3C,aAAa,EAAE,UAAU;IACzB,aAAa,EAAE,YAAY;IAC3B,wBAAwB,CAAC,yCAAyC;KACnE,CAAC;IAEF,4FAA4F;IAC5F,MAAM,qBAAqB,GAAc;QACvC,EAAE,EAAE,qBAAqB;QACzB,IAAI,EAAE,MAAM,EAAE,gCAAgC;QAC9C,IAAI,EAAE,wBAAwB;QAC9B,KAAK,EAAE,wBAAwB;QAC/B,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;YAC7D,uDAAuD;YACvD,MAAM,QAAQ,GAAI,KAAK,CAAC,QAAsB,IAAI,EAAE,CAAC;YACrD,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAA4B,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1G,MAAM,SAAS,GAAG,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,SAAS,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAExI,MAAM,aAAa,GAAI,KAAK,CAAC,cAAyB,IAAM,KAAK,CAAC,OAAmC,EAAE,KAAgB,IAAI,EAAE,CAAC,CAAC,uBAAuB;YAEtJ,qDAAqD;YACrD,2EAA2E;YAC3E,MAAM,aAAa,GAAG,iCAAiC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,iBAAiB,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;YAEzI,wCAAwC;YACxC,MAAM,cAAc,GAAG;gBACrB,MAAM,EAAE,cAAc,EAAE,cAAc;gBACtC,IAAI,EAAE,kBAAkB,EAAE,gBAAgB;gBAC1C,gBAAgB,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,mCAAmC;gBACjF,oBAAoB,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACpD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,4EAA4E;gBAC5E,8BAA8B;gBAC9B,wBAAwB;aACzB,CAAC;YAEF,2DAA2D;YAC3D,OAAO;gBACL,OAAO,EAAE;oBACP,qBAAqB,EAAE;wBACrB,OAAO,EAAE,aAAa;wBACtB,QAAQ,EAAE,cAAc;qBACzB;iBACF;gBACD,iFAAiF;gBACjF,sBAAsB,EAAE,aAAa;gBACrC,uBAAuB,EAAE,cAAc;aACxC,CAAC;QACJ,CAAC;KACF,CAAC;IAGF,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IAGzE,uBAAuB;IACvB,MAAM,KAAK,GAAgB;QACzB,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,mBAAmB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC,EAAE,OAAO,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,qBAAqB;QACtJ,EAAE,MAAM,EAAE,mBAAmB,CAAC,EAAE,EAAE,MAAM,EAAE,sBAAsB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,mBAAmB,CAAC,EAAE,OAAO,sBAAsB,CAAC,EAAE,EAAE,EAAE,EAAE,2CAA2C;QACtM,EAAE,MAAM,EAAE,sBAAsB,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,sBAAsB,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,4BAA4B;QACnK,0CAA0C;QAC1C,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,qBAAqB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC,EAAE,OAAO,qBAAqB,CAAC,EAAE,EAAE,EAAE;QACnI,8CAA8C;QAC9C,EAAE,MAAM,EAAE,qBAAqB,CAAC,EAAE,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,qBAAqB,CAAC,EAAE,OAAO,cAAc,CAAC,EAAE,EAAE,EAAE;QAC7I,0CAA0C;QAC1C,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,cAAc,CAAC,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,EAAE;KACxH,CAAC;IAEF,qCAAqC;IACrC,MAAM,kBAAkB,GAAoB;QAC1C,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,QAAQ,EAAE,6CAA6C;QACtE,KAAK,EAAE;YACL,SAAS;YACT,mBAAmB;YACnB,sBAAsB;YACtB,SAAS;YACT,qBAAqB,EAAE,+BAA+B;YACtD,cAAc;YACd,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,wCAAwC;KACnE,CAAC;IAEF,+BAA+B;IAC/B,mCAAgB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACtD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,IAAI,MAAM,EAAE,2BAA2B,CAAC,CAAC;IAEjG,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAGD;;;;;;;;;;;GAWG;AACH,SAAgB,4BAA4B,CAC1C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,mBAA0B,EAC1B,eAAsB;IAEtB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW;QAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtG,IAAI,CAAC,mBAAmB;QAAE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAChF,IAAI,CAAC,eAAe;QAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IAExE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;IAE3E,uBAAuB;IACvB,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,+BAA+B,CAAC,CAAC;IAEpF,MAAM,wBAAwB,GAAc;QAC1C,EAAE,EAAE,uBAAuB;QAC3B,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,sBAAsB;QAC5B,KAAK,EAAE,sBAAsB;QAC7B,KAAK,EAAE,mBAAmB,EAAE,sDAAsD;QAClF,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC/D,MAAM,QAAQ,GAAI,KAAK,CAAC,QAAsB,IAAI,EAAE,CAAC;YACrD,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAA4B,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1G,MAAM,WAAW,GAAG,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,SAAS,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAG,KAAK,CAAC,MAAkC,EAAE,KAAgB,IAAI,EAAE,CAAC,CAAC,YAAY;YAEvN,MAAM,MAAM,GAAG;;;;6BAIQ,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;;;;;;;;;;;aAW3F,CAAC;YAER,MAAM,cAAc,GAAG,MAAM,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAElE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;YAC1D,OAAO;gBACL,OAAO,EAAE,EAAE,uBAAuB,EAAE,cAAc,EAAE;gBACpD,YAAY,EAAE,cAAc,EAAE,8BAA8B;gBAC5D,YAAY,EAAE,WAAW,EAAE,wBAAwB;aACpD,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,kBAAkB,GAAc;QACpC,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,eAAe;QACtB,KAAK,EAAE,mBAAmB;QAC1B,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACzD,MAAM,YAAY,GAAI,KAAK,CAAC,YAAuB,IAAI,2BAA2B,CAAC;YAEnF,MAAM,MAAM,GAAG;;;;;;cAMP,YAAY;;;;aAIb,CAAC;YAER,MAAM,aAAa,GAAG,MAAM,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEjE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACzC,OAAO;gBACL,OAAO,EAAE,EAAE,iBAAiB,EAAE,aAAa,EAAE;gBAC7C,cAAc,EAAE,aAAa,EAAE,uBAAuB;gBACtD,YAAY,EAAE,YAAY,EAAE,0BAA0B;gBACtD,YAAY,EAAE,KAAK,CAAC,YAAY,EAAE,0BAA0B;aAC7D,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,cAAc,GAAc;QAChC,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,aAAa;QACnB,KAAK,EAAE,aAAa;QACpB,KAAK,EAAE,eAAe;QACtB,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACrD,MAAM,aAAa,GAAI,KAAK,CAAC,cAAyB,IAAI,mBAAmB,CAAC;YAC9E,MAAM,YAAY,GAAI,KAAK,CAAC,YAAuB,IAAI,2BAA2B,CAAC;YAEnF,MAAM,MAAM,GAAG;;;;;cAKP,YAAY;;;;;cAKZ,aAAa;;;;;;;;;;;aAWd,CAAC;YAER,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE5D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAChD,OAAO;gBACL,OAAO,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE;gBACxC,WAAW,EAAE,YAAY,EAAE,0BAA0B;gBACrD,cAAc,EAAE,aAAa,EAAE,kBAAkB;gBACjD,YAAY,EAAE,YAAY,EAAE,0BAA0B;gBACtD,YAAY,EAAE,KAAK,CAAC,YAAY,EAAE,0BAA0B;aAC7D,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,oEAAoE;IACpE,MAAM,kBAAkB,GAAc;QACpC,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,SAAS,EAAE,0CAA0C;QAC3D,IAAI,EAAE,mBAAmB;QACzB,KAAK,EAAE,mBAAmB;QAC1B,sEAAsE;QACtE,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAmB,EAAE;YACjE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACzD,MAAM,UAAU,GAAI,KAAK,CAAC,WAAsB,IAAI,EAAE,CAAC;YAEvD,2DAA2D;YAC3D,mFAAmF;YACnF,kCAAkC;YAClC,+DAA+D;YAC/D,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;gBACrE,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC7C,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC5C,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC3C,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC7C,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE7C,IAAI,eAAe,EAAE,CAAC;gBACpB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;gBAChG,OAAO,iBAAiB,CAAC,CAAC,4BAA4B;YACxD,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;gBACjG,OAAO,QAAQ,CAAC,CAAC,4BAA4B;YAC/C,CAAC;QACH,CAAC;KACF,CAAC;IAGF,MAAM,kBAAkB,GAAc;QACpC,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,aAAa;QACnB,KAAK,EAAE,aAAa;QACpB,KAAK,EAAE,mBAAmB,EAAE,gDAAgD;QAC5E,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACzD,MAAM,aAAa,GAAI,KAAK,CAAC,cAAyB,IAAI,mBAAmB,CAAC;YAC9E,MAAM,UAAU,GAAI,KAAK,CAAC,WAAsB,IAAI,qBAAqB,CAAC;YAC1E,MAAM,YAAY,GAAI,KAAK,CAAC,YAAuB,IAAI,2BAA2B,CAAC,CAAC,yCAAyC;YAE7H,MAAM,MAAM,GAAG;;;;;cAKP,aAAa;;;;;cAKb,UAAU;;;;;cAKV,YAAY;;;;aAIb,CAAC;YAER,MAAM,aAAa,GAAG,MAAM,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,4CAA4C;YAE9G,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YACpD,OAAO;gBACL,OAAO,EAAE,EAAE,iBAAiB,EAAE,aAAa,EAAE;gBAC7C,iBAAiB,EAAE,aAAa,EAAE,0CAA0C;aAC7E,CAAC;QACJ,CAAC;KACF,CAAC;IAGF,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IAGzE,uBAAuB;IACvB,MAAM,KAAK,GAAgB;QACzB,gDAAgD;QAChD,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,wBAAwB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,+BAA+B,EAAE;QACrH,yBAAyB;QACzB,EAAE,MAAM,EAAE,wBAAwB,CAAC,EAAE,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,wCAAwC,EAAE;QACvI,uBAAuB;QACvB,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,8BAA8B,EAAE;QACnH,gDAAgD;QAChD,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,8BAA8B,EAAE;QAEnH,uCAAuC;QACvC,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,kCAAkC,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,iBAAiB,CAAC,EAAE;QAClN,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,0BAA0B,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,QAAQ,CAAC,EAAE;QAEzL,uBAAuB;QACvB,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,0BAA0B,EAAE;KAC5G,CAAC;IAEF,0FAA0F;IAC1F,uEAAuE;IACvE,+FAA+F;IAC/F,8FAA8F;IAE9F,6EAA6E;IAC7E,kBAAkB,CAAC,OAAO,GAAG,KAAK,EAAE,KAA8B,EAAmB,EAAE;QACrF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACzD,MAAM,UAAU,GAAI,KAAK,CAAC,WAAsB,IAAI,EAAE,CAAC;QAEvD,2DAA2D;QAC3D,+DAA+D;QAC/D,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;YACrE,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7C,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5C,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC3C,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7C,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAE7C,IAAI,eAAe,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;YAChG,OAAO,iBAAiB,CAAC,CAAC,4BAA4B;QACxD,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;YACjG,OAAO,QAAQ,CAAC,CAAC,4BAA4B;QAC/C,CAAC;IACH,CAAC,CAAC;IAEF,wEAAwE;IACxE,iGAAiG;IACjG,6FAA6F;IAC7F,8FAA8F;IAC9F,mGAAmG;IACnG,kEAAkE;IAElE,0FAA0F;IAC1F,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,kBAAkB,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAChH,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,kBAAkB,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAExG,wFAAwF;IACxF,mFAAmF;IACnF,wDAAwD;IACxD,KAAK,CAAC,IAAI,CACR,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,kBAAkB,CAAC,EAAE,KAAK,kBAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,gCAAgC;IAC/K,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,kBAAkB,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE,EAAE,CAC9H,CAAC;IAGF,qCAAqC;IACrC,MAAM,kBAAkB,GAAoB;QAC1C,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,SAAS,EAAE,6CAA6C;QACvE,KAAK,EAAE;YACL,SAAS;YACT,wBAAwB;YACxB,kBAAkB;YAClB,cAAc;YACd,kBAAkB,EAAE,4BAA4B;YAChD,kBAAkB;YAClB,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,wCAAwC;KACnE,CAAC;IAEF,+BAA+B;IAC/B,mCAAgB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACtD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,IAAI,MAAM,EAAE,2BAA2B,CAAC,CAAC;IAE3F,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAGD;;;;;;;;;;;;GAYG;AACH,SAAgB,sBAAsB,CACpC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,KAAY,EACZ,UAAwB;IAExB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW;QAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtG,IAAI,CAAC,KAAK;QAAE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACjF,IAAI,CAAC,UAAU;QAAE,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAEnF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;IAEpE,6CAA6C;IAC7C,MAAM,iBAAiB,GAAG,UAAU,YAAY,oBAAoB;QAClE,CAAC,CAAC,UAAU;QACZ,CAAC,CAAC,IAAI,oBAAoB,CAAC,UAAU,EAAE,YAAY,EAAE,uBAAuB,CAAC,CAAC,CAAC,8BAA8B;IAG/G,uBAAuB;IACvB,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,iCAAiC,CAAC,CAAC;IAEtF,MAAM,iBAAiB,GAAc;QACnC,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,eAAe;QACtB,KAAK,EAAE,KAAK,EAAE,0BAA0B;QACxC,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACxD,MAAM,QAAQ,GAAI,KAAK,CAAC,QAAsB,IAAI,EAAE,CAAC;YACrD,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAA4B,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1G,MAAM,SAAS,GAAG,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,SAAS,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAG,KAAK,CAAC,MAAkC,EAAE,KAAgB,IAAI,EAAE,CAAC,CAAC,YAAY;YAErN,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnE,MAAM,QAAQ,GAAG,2CAA2C,CAAC;gBAC7D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAChC,yDAAyD;gBACzD,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC,+BAA+B;YACrF,CAAC;YAGD,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,wBAAwB,EAAE;gBAClE,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,iCAAiC;YAEtF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAEnD,8DAA8D;YAC9D,6FAA6F;YAC7F,MAAM,gBAAgB,GAAa,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnB,kEAAkE;gBAClE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;gBACpE,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtB,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBACzC,CAAC;qBAAM,CAAC;oBACN,0EAA0E;oBAC1E,oFAAoF;oBACpF,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;wBAClE,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,uCAAuC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;wBACxF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,cAAc,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,qBAAqB;4BACnF,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBACxC,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,iDAAiD;YACjD,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;gBACzG,gBAAgB,CAAC,IAAI,CACnB,SAAS,EACT,sBAAsB,SAAS,EAAE,EACjC,GAAG,SAAS,2BAA2B,CACxC,CAAC;YACJ,CAAC;YAED,4DAA4D;YAC5D,MAAM,cAAc,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,qBAAqB;YAG1E,OAAO;gBACL,OAAO,EAAE,EAAE,gBAAgB,EAAE,cAAc,EAAE,EAAE,sBAAsB;gBACrE,aAAa,EAAE,cAAc,EAAE,sBAAsB;gBACrD,cAAc,EAAE,cAAc,EAAE,wCAAwC;gBACxE,UAAU,EAAE,SAAS,EAAE,sBAAsB;aAC9C,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,oCAAoC;IACpC,eAAO,CAAC,cAAc,CACpB,uBAAuB,EAAE,UAAU;IACnC,oBAAoB,EAAE,YAAY;IAClC,oBAAoB,CAAC,4CAA4C;KAClE,CAAC;IAEF,oDAAoD;IACpD,MAAM,kBAAkB,GAAc;QACpC,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,MAAM,EAAE,gCAAgC;QAC9C,IAAI,EAAE,wBAAwB;QAC9B,KAAK,EAAE,wBAAwB;QAC/B,sEAAsE;QACtE,wEAAwE;QACxE,2EAA2E;QAC3E,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACzD,MAAM,gBAAgB,GAAI,KAAK,CAAC,cAA2B,IAAI,EAAE,CAAC;YAClE,MAAM,gBAAgB,GAAI,KAAK,CAAC,kBAA0D,IAAI,EAAE,CAAC,CAAC,oBAAoB;YAEtH,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;gBAC5F,iDAAiD;gBACjD,OAAO,EAAE,YAAY,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,CAAC;YACxF,CAAC;YAED,oCAAoC;YACpC,MAAM,YAAY,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEnD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,YAAY,GAAG,CAAC,CAAC;YAE7D,IAAI,aAAqB,CAAC;YAC1B,IAAI,CAAC;gBACH,yDAAyD;gBACzD,MAAM,aAAa,GAAG,MAAM,iBAAiB,CAAC,MAAM,CAAC;oBACnD,KAAK,EAAE,YAAY;oBACnB,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;iBACvB,CAAC,CAAC,CAAC,2BAA2B;gBAC/B,aAAa,GAAG,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;gBAClG,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;YAE1H,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2CAA2C,YAAY,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC1F,aAAa,GAAG,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACrF,CAAC;YAED,2CAA2C;YAC3C,gBAAgB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;YAEtE,6DAA6D;YAC7D,uEAAuE;YACvE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,gBAAgB,CAAC,MAAM,qCAAqC,CAAC,CAAC;gBAC1G,OAAO;oBACL,cAAc,EAAE,gBAAgB,EAAE,gCAAgC;oBAClE,kBAAkB,EAAE,gBAAgB,EAAE,iCAAiC;oBACvE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,mBAAmB;oBACjD,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,kBAAkB;oBACtD,iGAAiG;oBACjG,yEAAyE;oBACzE,uEAAuE;iBACxE,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;gBACvF,mEAAmE;gBACnE,OAAO;oBACL,cAAc,EAAE,EAAE,EAAE,uBAAuB;oBAC3C,kBAAkB,EAAE,gBAAgB,EAAE,2BAA2B;oBACjE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,mBAAmB;oBACjD,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,kBAAkB;oBACtD,YAAY,EAAE,sBAAsB,CAAC,mBAAmB;iBACzD,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;IAGF,MAAM,uBAAuB,GAAc;QACzC,EAAE,EAAE,sBAAsB;QAC1B,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,qBAAqB;QAC3B,KAAK,EAAE,qBAAqB;QAC5B,KAAK,EAAE,KAAK,EAAE,0BAA0B;QACxC,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC9D,MAAM,aAAa,GAAI,KAAK,CAAC,kBAA0D,IAAI,EAAE,CAAC,CAAC,yBAAyB;YACxH,MAAM,SAAS,GAAI,KAAK,CAAC,UAAqB,IAAI,WAAW,CAAC,CAAC,qBAAqB;YACpF,MAAM,YAAY,GAAI,KAAK,CAAC,aAAwB,IAAI,EAAE,CAAC,CAAC,oBAAoB;YAEhF,4CAA4C;YAC5C,MAAM,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,EAAqC,EAAE,KAAa,EAAE,EAAE,CAClG,qBAAqB,KAAK,GAAG,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,CAAC,MAAM,OAAO,CACpG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG;;+DAE0C,SAAS;;;;cAI1D,gBAAgB;;;;;cAKhB,YAAY;;;;;;;;;;;aAWb,CAAC;YAER,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,iCAAiC;YAEtF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACzD,OAAO;gBACL,OAAO,EAAE,EAAE,sBAAsB,EAAE,cAAc,EAAE;gBACnD,oBAAoB,EAAE,cAAc,EAAE,8BAA8B;gBACpE,UAAU,EAAE,SAAS,EAAE,mBAAmB;gBAC1C,aAAa,EAAE,YAAY,EAAE,kBAAkB;aAChD,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,qBAAqB,GAAc;QACvC,EAAE,EAAE,oBAAoB;QACxB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,qBAAqB;QAC3B,KAAK,EAAE,qBAAqB;QAC5B,KAAK,EAAE,KAAK,EAAE,2BAA2B;QACzC,OAAO,EAAE,KAAK,EAAE,KAA8B,EAAgC,EAAE;YAC9E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC5D,MAAM,SAAS,GAAI,KAAK,CAAC,UAAqB,IAAI,oBAAoB,CAAC,CAAC,qBAAqB;YAC7F,MAAM,mBAAmB,GAAI,KAAK,CAAC,oBAA+B,IAAI,uBAAuB,CAAC;YAC9F,MAAM,YAAY,GAAI,KAAK,CAAC,aAAwB,IAAI,EAAE,CAAC,CAAC,kBAAkB;YAE9E,MAAM,MAAM,GAAG;;;wCAGmB,SAAS;;;;cAInC,YAAY;;;;;cAKZ,mBAAmB;;;;;;;;;;aAUpB,CAAC;YAER,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,iCAAiC;YAEvF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACvD,OAAO;gBACL,OAAO,EAAE,EAAE,oBAAoB,EAAE,eAAe,EAAE;gBAClD,qBAAqB,EAAE,eAAe,EAAE,yBAAyB;aAClE,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IAGzE,uBAAuB;IACvB,MAAM,KAAK,GAAgB;QACzB,yCAAyC;QACzC,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;QACxG,+DAA+D;QAC/D,EAAE,IAAI,EAAE,mCAAmC,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;QAE3H,wGAAwG;QACxG,kLAAkL;QAElL,oCAAoC;QACpC,EAAE,IAAI,EAAE,mCAAmC,EAAE,MAAM,EAAE,uBAAuB,CAAC,EAAE,EAAE,MAAM,EAAE,qBAAqB,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;QACpI,sBAAsB;QACtB,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,qBAAqB,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1G,CAAC;IAEF,gFAAgF;IAChF,4EAA4E;IAC5E,6EAA6E;IAC7E,8EAA8E;IAE9E,qCAAqC;IACrC,MAAM,kBAAkB,GAAoB;QAC1C,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,UAAU,EAAE,sCAAsC;QACjE,KAAK,EAAE;YACL,SAAS;YACT,iBAAiB;YACjB,kBAAkB,EAAE,wCAAwC;YAC5D,kKAAkK;YAClK,uBAAuB;YACvB,qBAAqB;YACrB,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,wCAAwC;KACnE,CAAC;IAEF,+BAA+B;IAC/B,mCAAgB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACtD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,IAAI,MAAM,EAAE,2BAA2B,CAAC,CAAC;IAEpF,OAAO,kBAAkB,CAAC;AAC5B,CAAC", "sourcesContent": ["/**\n * Advanced Codessa workflow templates.\n * Provides production-quality, complete, and robust workflow definitions\n * using various agents and tools.\n */\n\nimport { z } from 'zod';\nimport { ITool, Tool, ToolInput, ToolResult } from '../../tools/tool.ts.backup';\nimport { AgentContext } from '../agentUtilities/agent';\nimport { Agent } from '../agentUtilities/agent';\nimport { Codessa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, GraphState } from './types';\nimport { BaseMessage } from './corePolyfill';\nimport { workflowRegistry } from './workflowRegistry';\nimport { promptManager } from '../../prompts/promptManager';\nimport { Logger } from '../../logger';\nimport { codessaMemoryProvider as codessaMemoryManager } from '../../memory/codessa/codessaMemory';\nimport { MemorySource, MemoryType, MemorySearchOptions, MemoryFilter } from '../../memory/types';\n\n// Import tools from the tools directory with aliases to avoid naming conflicts\nimport { DocumentRetrievalTool as DocRetrievalTool } from '../../tools/documentRetrievalTool';\nimport { SearchTool as SearchToolImpl } from '../../tools/searchTool';\n\n// Base class for all enhanced tools\nabstract class BaseTool implements ITool {\n  abstract readonly id: string;\n  abstract readonly name: string;\n  abstract readonly description: string;\n  abstract readonly type: 'single-action' | 'multi-action';\n  \n  abstract execute(input: ToolInput, context?: AgentContext): Promise<ToolResult>;\n  \n  // Optional dispose method for cleanup\n  async dispose(): Promise<void> {\n    // Default implementation does nothing\n  }\n}\n\n// Local type aliases to avoid naming conflicts\ntype DocRetrievalOpts = {\n  limit?: number;\n  minScore?: number;\n  includeMetadata?: boolean;\n  filters?: MemoryFilter;\n  semanticSearch?: boolean;\n};\n\ntype SearchOpts = {\n  limit?: number;\n  includeMetadata?: boolean;\n  filters?: Record<string, unknown>;\n};\n\n/**\n * Interface for tool context options\n * @deprecated Use the one from the tools directory instead\n */\nexport interface ToolContextOptions {\n  /**\n   * Whether to validate the input against the schema\n   * @default true\n   */\n  validateInput?: boolean;\n  \n  /**\n   * Additional context to pass to the tool\n   */\n  context?: Record<string, unknown>;\n  \n  /**\n   * Callback for handling validation errors\n   */\n  onValidationError?: (error: z.ZodError) => unknown;\n}\n\n// Type for structured tools\ntype StructuredTool = ITool & {\n  schema: z.ZodType<unknown>;\n  _call(input: unknown): Promise<unknown>;\n};\n\n/**\n * Base class for enhanced tool wrappers that provide additional functionality\n * like input validation, context management, and error handling.\n */\nabstract class EnhancedToolWrapper extends BaseTool {\n  protected readonly baseTool: ITool;\n  protected _context?: AgentContext;\n  protected _toolName: string;\n  protected _baseToolHasSchema: boolean;\n  protected _schemaValidated: boolean;\n  \n  abstract readonly schema: z.ZodType<unknown>;\n  abstract readonly name: string;\n  abstract readonly description: string;\n  abstract readonly type: 'single-action' | 'multi-action';\n  abstract readonly actions: Record<string, any>;\n\n  constructor(\n    baseTool: ITool,\n    name: string = 'EnhancedTool',\n    description: string = 'An enhanced tool wrapper',\n    initialContext?: AgentContext\n  ) {\n    super(); // Call BaseTool constructor\n    \n    // Store the base tool\n    this.baseTool = baseTool;\n    \n    // Store context if provided\n    this._context = initialContext;\n    \n    // Initialize tool name and description\n    this._toolName = name;\n    \n    // Check if the base tool has a schema property\n    this._baseToolHasSchema = 'schema' in this.baseTool && \n      this.baseTool.schema !== undefined && \n      typeof this.baseTool.schema === 'object';\n      \n    // Initialize schema validation flag\n    this._schemaValidated = false;\n    \n    // Initialize required ITool properties\n    this.id = name.toLowerCase().replace(/\\s+/g, '-');\n    this.type = 'single-action';\n  }\n  \n  // Implement required ITool methods\n  abstract execute(input: ToolInput, context?: AgentContext): Promise<ToolResult>;\n  \n  // Helper method to create a standard ToolResult\n  protected createToolResult(\n    success: boolean,\n    output: unknown,\n    error?: string\n  ): ToolResult {\n    return {\n      success,\n      output,\n      error,\n      timestamp: new Date().toISOString()\n    };\n  }\n  \n  // Getter for context\n  get context(): AgentContext | undefined {\n    return this._context;\n  }\n  \n  // Update context\n  updateContext(context: AgentContext): void {\n    this._context = context;\n  }\n  \n  /**\n   * Invokes the tool with the given input and options\n   * @param input The input to the tool\n   * @param options Optional tool context options\n   * @returns The result of the tool execution\n   */\n  async invoke(input: unknown, options: ToolContextOptions = {}): Promise<unknown> {\n    try {\n      // Validate input if schema is available\n      if (this.schema) {\n        const result = this.schema.safeParse(input);\n        if (!result.success) {\n          if (options.onValidationError) {\n            options.onValidationError(result.error);\n          }\n          throw new Error(`Input validation failed: ${result.error.message}`);\n        }\n        input = result.data;\n      }\n      \n      // Execute the tool with context if available\n      const context = options.context || this._context;\n      if (context) {\n        return await this._call(input, context);\n      }\n      return await this._call(input);\n    } catch (error) {\n      Logger.instance.error(`Error in ${this._toolName}:`, error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Internal method to execute the tool with the given input\n   * @param input The input to the tool\n   * @param context Optional execution context\n   * @returns The result of the tool execution\n   */\n  protected abstract _call(input: unknown, context?: AgentContext): Promise<unknown>;\n  \n  /**\n   * Updates the context for the tool\n   * @param context The new context to set\n   */\n  updateContext(context: AgentContext): void {\n    this._context = context;\n  }\n\n  /**\n   * Internal call method to invoke the wrapped tool.\n   * Handles different potential method names (`invoke`, `call`, `execute`).\n   * Assumes the wrapped tool's method matches the schema's input type after transformation.\n   * @param input - The input to the wrapper (pre-schema transformation).\n   * @returns The result of the wrapped tool's execution.\n   * @throws {Error} If the wrapped tool does not have a valid execution method.\n   */\n  /**\n   * Internal method to execute the tool with the given input\n   * @param input The input to the tool\n   * @returns The result of the tool execution\n   */\n  protected async _call(input: unknown): Promise<GraphState> {\n    try {\n      // Validate and transform input using the concrete schema\n      const validatedInput = this.schema.parse(input);\n      Logger.instance.debug(`[${this._toolName}] Input validated/transformed.`);\n\n      // Log context information if available\n      if (this._context) {\n        Logger.instance.debug(`[${this._toolName}] Context available:`, {\n          hasWorkspace: !!this._context.workspace,\n          hasVariables: !!this._context.variables,\n          hasStreamingContext: !!this._context.streamingContext\n        });\n      }\n\n      // Determine which method to call on the base tool\n      let result: unknown;\n      const baseTool = this.baseTool as any;\n      \n      // Try to call the most specific method first\n      if (typeof baseTool.invoke === 'function') {\n        // Pass context if the method accepts it\n        result = baseTool.invoke.length >= 2 \n          ? await baseTool.invoke(validatedInput, this._context)\n          : await baseTool.invoke(validatedInput);\n      } \n      else if (typeof baseTool.call === 'function') {\n        result = baseTool.call.length >= 2\n          ? await baseTool.call(validatedInput, this._context)\n          : await baseTool.call(validatedInput);\n      } \n      else if (typeof baseTool.execute === 'function') {\n        result = baseTool.execute.length >= 2\n          ? await baseTool.execute(validatedInput, this._context)\n          : await baseTool.execute(validatedInput);\n      } \n      else {\n        throw new Error(\n          `Wrapped tool '${this.name}' does not have a valid execution method. ` +\n          `Expected 'invoke', 'call', or 'execute' method.`\n        );\n      }\n\n      Logger.instance.debug(`[${this._toolName}] Tool execution completed successfully.`);\n      \n      // Ensure the result is in the expected GraphState format\n      return this._normalizeResult(result);\n      \n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      const errorStack = error instanceof Error ? error.stack : undefined;\n      \n      Logger.instance.error(`[${this._toolName}] Error during tool execution:`, { \n        message: errorMessage, \n        stack: errorStack, \n        input: input \n      });\n      \n      // Enhance the error with context information if available\n      if (error instanceof Error && this._context) {\n        (error as any).context = {\n          toolName: this._toolName,\n          contextType: this._context.constructor?.name,\n          hasWorkspace: !!this._context.workspace,\n          timestamp: new Date().toISOString()\n        };\n      }\n      \n      // Re-throw the error to be handled by the workflow node\n      throw error;\n    }\n  }\n  \n  /**\n   * Creates a minimal valid GraphState with required properties\n   * @param result The result to include in the state\n   * @param outputs Additional outputs to include\n   * @returns A valid GraphState object\n   */\n  protected _createGraphState(\n    result: unknown,\n    outputs: Record<string, unknown> = {}\n  ): GraphState {\n    const baseState: Partial<GraphState> = {\n      messages: [],\n      inputs: {},\n      currentNode: 'unknown',\n      history: [],\n      outputs: {}\n    };\n\n    // Add the main result to outputs\n    const resultKey = this._toolName;\n    if (result !== undefined && result !== null) {\n      baseState.outputs = {\n        ...baseState.outputs,\n        [resultKey]: result,\n        ...outputs\n      };\n    }\n\n    // For string results, add to messages using a properly typed message object\n    if (typeof result === 'string') {\n      const messageContent = result;\n      const toolName = this._toolName;\n      \n      // Create a properly typed message object that implements BaseMessage\n      const toolMessage: BaseMessage = {\n        _getType: () => 'tool',\n        content: messageContent,\n        additional_kwargs: { name: toolName },\n        toDict: () => ({\n          type: 'tool',\n          data: {\n            content: messageContent,\n            additional_kwargs: { name: toolName }\n          }\n        })\n      } as unknown as BaseMessage;\n      \n      baseState.messages = [\n        ...(baseState.messages || []),\n        toolMessage\n      ];\n    }\n\n    return baseState as GraphState;\n  }\n\n  /**\n   * Normalizes the result from the tool to ensure it's a valid GraphState\n   * @param result The raw result from the tool\n   * @returns A normalized GraphState\n   */\n  private _normalizeResult(result: unknown): GraphState {\n    // If already a GraphState, ensure it has required properties\n    if (result && typeof result === 'object' && 'outputs' in result) {\n      const state = result as Partial<GraphState>;\n      return {\n        messages: state.messages || [],\n        inputs: state.inputs || {},\n        currentNode: state.currentNode || 'unknown',\n        history: state.history || [],\n        outputs: state.outputs || {},\n        ...state\n      };\n    }\n    \n    // For string results, create a simple state\n    if (typeof result === 'string') {\n      return this._createGraphState(result, {\n        [this._toolName]: result\n      });\n    }\n    \n    // For object results, include all properties\n    if (result && typeof result === 'object') {\n      return this._createGraphState(result, {\n        [this._toolName]: result,\n        ...(result as Record<string, unknown>)\n      });\n    }\n    \n    // Fallback for primitive values\n    return this._createGraphState(result, {\n      [this._toolName]: result,\n      value: result\n    });\n  }\n}\n\n\n/**\n * Interface for document retrieval options that can be passed via context\n */\ninterface DocumentRetrievalOptions {\n  /**\n   * Maximum number of documents to return\n   * @default 5\n   */\n  limit?: number;\n  \n  /**\n   * Minimum relevance score (0-1)\n   * @default 0.7\n   */\n  minScore?: number;\n  \n  /**\n   * Whether to include document metadata in results\n   * @default true\n   */\n  includeMetadata?: boolean;\n  \n  /**\n   * Additional filter criteria for document retrieval\n   */\n  filters?: MemoryFilter;\n  \n  /**\n   * Whether to use semantic search (true) or keyword search (false)\n   * @default true\n   */\n  semanticSearch?: boolean;\n}\n\n/**\n * Enhanced wrapper for document retrieval tools that leverages context for improved search\n */\nclass DocumentRetrievalStructuredTool extends EnhancedToolWrapper {\n  // Implement the abstract actions property from ToolSchemaWrapper\n  override actions: Record<string, any> = {\n    default: {\n      description: 'Retrieves documents based on a query',\n      inputSchema: z.object({\n        query: z.string().describe('The search query'),\n        options: z.object({\n          limit: z.number().int().positive().optional(),\n          minScore: z.number().min(0).max(1).optional(),\n          includeMetadata: z.boolean().optional(),\n          filters: z.record(z.any()).optional(),\n          semanticSearch: z.boolean().optional()\n        }).optional()\n      })\n    }\n  };\n  \n  // Implement required ITool properties\n  override readonly id: string;\n  override readonly name: string;\n  override readonly description: string;\n  override readonly type: 'single-action' | 'multi-action' = 'single-action';\n  \n  private _defaultOptions: DocRetrievalOpts = {\n    limit: 5,\n    minScore: 0.7,\n    includeMetadata: true,\n    semanticSearch: true\n  };\n  \n  /**\n   * Invokes the document retrieval with enhanced context support\n   * @param input The input query or parameters\n   * @param options Optional invocation options including context\n   * @returns The retrieval results in a GraphState format\n   */\n  public override async invoke(\n    input: unknown, \n    options: ToolContextOptions = {}\n  ): Promise<GraphState> {\n    try {\n      // Update context if provided in options\n      if (options.context) {\n        this.updateContext(options.context as AgentContext);\n      }\n      \n      return await super.invoke(input, options);\n    } catch (error) {\n      Logger.instance.error('Error in DocumentRetrievalStructuredTool.invoke:', error);\n      throw error;\n    }\n  }\n  /**\n   * Schema for document retrieval input\n   * Can be a simple query string or an object with additional parameters\n   */\n  override schema = z.union([\n    // Simple string query\n    z.string().describe('The query string to search for documents.'),\n    \n    // Or an object with query and options\n    z.object({\n      query: z.string().describe('The query string to search for documents.'),\n      options: z.object({\n        limit: z.number().int().positive().optional()\n          .describe('Maximum number of documents to return'),\n        minScore: z.number().min(0).max(1).optional()\n          .describe('Minimum relevance score (0-1)'),\n        includeMetadata: z.boolean().optional()\n          .describe('Whether to include document metadata'),\n        filters: z.record(z.any()).optional()\n          .describe('Additional filters for document retrieval'),\n        semanticSearch: z.boolean().optional()\n          .describe('Whether to use semantic search (true) or keyword search (false)')\n      }).optional()\n    })\n  ]).transform((input) => {\n    // Normalize input to always return a query string\n    if (typeof input === 'string') {\n      return { query: input, options: {} };\n    }\n    return { query: input.query, options: input.options || {} };\n  });\n\n  /**\n   * Creates a new DocumentRetrievalStructuredTool instance\n   * @param retrievalTool The underlying retrieval tool to wrap\n   * @param name Optional name for the tool (defaults to the base tool's name)\n   * @param description Optional description for the tool\n   * @param defaultOptions Default retrieval options\n   * @param context Optional initial context\n   */\n  constructor(\n    retrievalTool: ITool,\n    name: string = 'DocumentRetrievalStructuredTool',\n    description: string = 'Retrieves documents based on a query with contextual enhancement',\n    defaultOptions: DocumentRetrievalOptions = {},\n    context?: AgentContext\n  ) {\n    super(retrievalTool, name, description, context);\n    \n    // Merge provided default options with built-in defaults\n    this._defaultOptions = { ...this._defaultOptions, ...defaultOptions };\n    \n    this.id = name.toLowerCase().replace(/\\s+/g, '-');\n    this.name = name;\n    this.description = description;\n    \n    // Merge provided default options with built-in defaults\n    this._defaultOptions = { ...this._defaultOptions, ...defaultOptions };\n    \n    if (!('invoke' in retrievalTool || 'call' in retrievalTool || 'execute' in retrievalTool)) {\n      Logger.instance.warn(\n        `DocumentRetrievalStructuredTool wrapper created for tool '${this.name}' ` +\n        `which lacks standard execution methods (invoke, call, execute).`\n      );\n    }\n    \n    // Bind methods to maintain proper 'this' context\n    this._enhanceWithContext = this._enhanceWithContext.bind(this);\n  }\n\n  /**\n   * Internal method to enhance retrieval options with context information\n   * @param baseOptions Base retrieval options\n   * @param context Current execution context\n   * @returns Enhanced retrieval options with context\n   */\n  private _enhanceWithContext(\n    baseOptions: DocumentRetrievalOptions,\n    context?: AgentContext\n  ): DocumentRetrievalOptions {\n    if (!context) {\n      return baseOptions;\n    }\n    \n    // Create a deep copy of base options to avoid mutating the original\n    const enhancedOptions: DocumentRetrievalOptions = {\n      ...baseOptions,\n      filters: baseOptions.filters ? { ...baseOptions.filters } : {}\n    };\n    \n    // Ensure filters object exists\n    enhancedOptions.filters = enhancedOptions.filters || {};\n    \n    // Initialize tags array if not present\n    if (!enhancedOptions.filters.tags) {\n      enhancedOptions.filters.tags = [];\n    }\n    \n    // Add workspace context if available\n    if (context.workspace) {\n      // Add current file as a filter if available\n      if (context.workspace.currentFile) {\n        enhancedOptions.filters.filePath = context.workspace.currentFile;\n      }\n      \n      // Add workspace folders as tags\n      if (context.workspace.workspaceFolders?.length) {\n        const currentTags = enhancedOptions.filters.tags || [];\n        enhancedOptions.filters.tags = [\n          ...currentTags,\n          'workspace',\n          ...context.workspace.workspaceFolders\n        ];\n      }\n    }\n    \n    // Add variables context if available\n    if (context.variables) {\n      const currentTags = enhancedOptions.filters?.tags || [];\n      const variableTags = Object.entries(context.variables)\n        .filter(([_, value]) => value && typeof value === 'object')\n        .map(([key]) => `var_${key}`);\n      \n      enhancedOptions.filters.tags = [\n        ...currentTags,\n        ...variableTags\n      ];\n    }\n    \n    return enhancedOptions;\n  }\n  \n  /**\n   * Executes the document retrieval with enhanced context handling\n   * @param input The normalized input (from schema transformation)\n   * @returns The retrieval results in a GraphState format\n   */\n  protected override async _call(input: { query: string; options: DocumentRetrievalOptions }): Promise<GraphState> {\n    try {\n      const { query, options } = input;\n      const context = this.context;\n      \n      // Merge options with defaults and enhance with context\n      const mergedOptions: DocumentRetrievalOptions = {\n        ...this._defaultOptions,\n        ...options,\n        filters: { ...this._defaultOptions.filters, ...(options.filters || {}) }\n      };\n      \n      const enhancedOptions = this._enhanceWithContext(mergedOptions, context);\n      \n      Logger.instance.debug(`[DocumentRetrieval] Executing query: \"${query}\"`, {\n        options: enhancedOptions,\n        hasContext: !!context,\n        contextType: context?.constructor?.name\n      });\n      \n      // Execute the base tool with the enhanced options\n      const baseInput = { \n        query, \n        ...(Object.keys(enhancedOptions).length > 0 ? { options: enhancedOptions } : {}) \n      };\n      \n      // Call the base tool's invoke method with context if it accepts it\n      let result: unknown;\n      const baseTool = this.baseTool as any;\n      \n      if (typeof baseTool.invoke === 'function') {\n        result = baseTool.invoke.length >= 2 \n          ? await baseTool.invoke(baseInput, context)\n          : await baseTool.invoke(baseInput);\n      } else if (typeof baseTool.call === 'function') {\n        result = baseTool.call.length >= 2\n          ? await baseTool.call(baseInput, context)\n          : await baseTool.call(baseInput);\n      } else if (typeof baseTool.execute === 'function') {\n        result = baseTool.execute.length >= 2\n          ? await baseTool.execute(baseInput, context)\n          : await baseTool.execute(baseInput);\n      } else {\n        throw new Error('No valid execution method found on base tool');\n      }\n      \n      // Process and normalize the result\n      return this._processRetrievalResult(result, query, enhancedOptions);\n      \n    } catch (error) {\n      Logger.instance.error('Error in DocumentRetrievalStructuredTool._call:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Processes and normalizes the retrieval result into a valid GraphState\n   * @param result The raw result from the base tool\n   * @param originalQuery The original search query\n   * @param options The retrieval options used\n   * @returns A normalized GraphState with the retrieval results\n   */\n  private async _processRetrievalResult(\n    result: unknown,\n    originalQuery: string,\n    options: DocumentRetrievalOptions\n  ): Promise<GraphState> {\n    try {\n      // Normalize different result formats\n      let documents: Array<{ content: string; metadata?: Record<string, unknown> }> = [];\n      \n      if (Array.isArray(result)) {\n        // Handle array of documents\n        documents = result.map(doc => {\n          if (typeof doc === 'string') {\n            return { content: doc };\n          } else if (doc && typeof doc === 'object' && 'content' in doc) {\n            return {\n              content: String((doc as any).content),\n              metadata: (doc as any).metadata || {}\n            };\n          }\n          return { content: JSON.stringify(doc) };\n        });\n      } else if (result && typeof result === 'object') {\n        // Handle single document or object result\n        const doc = result as Record<string, unknown>;\n        documents = [{\n          content: 'content' in doc ? String(doc.content) : JSON.stringify(doc),\n          metadata: 'metadata' in doc ? (doc.metadata as Record<string, unknown>) || {} : {}\n        }];\n      } else if (result !== undefined && result !== null) {\n        // Handle primitive values\n        documents = [{ content: String(result) }];\n      }\n      \n      // Apply post-processing based on options\n      if (options.limit && documents.length > options.limit) {\n        documents = documents.slice(0, options.limit);\n      }\n      \n      // Filter by score if minScore is specified\n      if (options.minScore !== undefined) {\n        documents = documents.filter(doc => {\n          const score = doc.metadata?.score ?? 1;\n          return Number(score) >= (options.minScore || 0);\n        });\n      }\n      \n      // Remove metadata if not requested\n      if (!options.includeMetadata) {\n        documents = documents.map(({ content }) => ({ content }));\n      }\n      \n      // Create a structured result\n      const formattedResults = documents.map((doc, index) => ({\n        id: `doc_${index + 1}`,\n        content: doc.content,\n        ...(options.includeMetadata && doc.metadata ? { metadata: doc.metadata } : {})\n      }));\n      \n      // Create a valid GraphState with all required properties\n      return this._createGraphState(formattedResults, {\n        documents: formattedResults,\n        count: formattedResults.length,\n        query: originalQuery,\n        options: options,\n        metadata: {\n          retrievalOptions: options,\n          timestamp: new Date().toISOString()\n        }\n      });\n      \n    } catch (error) {\n      Logger.instance.error('Error processing retrieval result:', error);\n      \n      // Create an error state with all required GraphState properties\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      return this._createGraphState(null, {\n        error: 'Failed to process retrieval results',\n        details: errorMessage,\n        metadata: {\n          error: {\n            message: 'Failed to process retrieval results',\n            cause: error,\n            timestamp: new Date().toISOString()\n          }\n        }\n      });\n    }\n  }\n}\n\n/**\n * Interface for search options that can be passed via context\n */\ninterface SearchOptions {\n  /**\n   * Maximum number of search results to return\n   * @default 10\n   */\n  limit?: number;\n  \n  /**\n   * Whether to include metadata in search results\n   * @default true\n   */\n  includeMetadata?: boolean;\n  \n  /**\n   * Additional filter criteria for the search\n   */\n  filters?: Record<string, unknown>;\n}\n\n/**\n * Enhanced wrapper for search tools that leverages context for improved search\n */\nexport class SearchStructuredTool extends EnhancedToolSchemaWrapper {\n  // Implement the abstract actions property from ToolSchemaWrapper\n  override actions: Record<string, any> = {\n    default: {\n      description: 'Performs a search query',\n      inputSchema: z.object({\n        query: z.string().describe('The search query'),\n        options: z.object({\n          limit: z.number().int().positive().optional(),\n          includeMetadata: z.boolean().optional(),\n          filters: z.record(z.any()).optional()\n        }).optional()\n      })\n    }\n  };\n  \n  // Implement required ITool properties\n  override readonly id: string;\n  override readonly name: string;\n  override readonly description: string;\n  override readonly type: 'single-action' | 'multi-action' = 'single-action';\n  \n  private _defaultOptions: SearchOptions;\n  \n  // Schema for input validation and transformation with proper type handling\n  override schema = z.union([\n    // Simple string query\n    z.string().describe('The search query string.'),\n    \n    // Or an object with query and options\n    z.object({\n      query: z.string().describe('The search query string.'),\n      options: z.object({\n        limit: z.number().int().positive().optional()\n          .describe('Maximum number of results to return'),\n        includeMetadata: z.boolean().optional()\n          .describe('Whether to include metadata in the results'),\n        filters: z.record(z.any()).optional()\n          .describe('Additional filters for the search')\n      }).optional()\n    })\n  ]).transform((input) => {\n    // Normalize input to always return a query string and options\n    if (typeof input === 'string') {\n      return { query: input, options: {} };\n    }\n    return { query: input.query, options: input.options || {} };\n  });\n  \n  /**\n   * Creates a new SearchStructuredTool instance\n   * @param searchTool The underlying search tool to wrap\n   * @param name Optional name for the tool (defaults to the base tool's name)\n   * @param description Optional description for the tool\n   * @param defaultOptions Default search options\n   * @param initialContext Optional initial context\n   */\n  constructor(\n    searchTool: ITool, \n    name: string = 'SearchStructuredTool',\n    description: string = 'Performs a general search based on a query with contextual enhancement',\n    defaultOptions: SearchOptions = {},\n    initialContext?: AgentContext\n  ) {\n    super(searchTool, name, description, initialContext);\n    this.id = name.toLowerCase().replace(/\\s+/g, '-');\n    this.name = name;\n    this.description = description;\n    \n    // Initialize default options\n    this._defaultOptions = {\n      limit: 10,\n      includeMetadata: true,\n      ...defaultOptions\n    };\n    \n    if (!('invoke' in searchTool || 'call' in searchTool || 'execute' in searchTool)) {\n      Logger.instance.warn(\n        `SearchStructuredTool wrapper created for tool '${this.name}' ` +\n        `which lacks standard execution methods (invoke, call, execute).`\n      );\n    }\n    \n    // Bind methods to maintain proper 'this' context\n    this._enhanceWithContext = this._enhanceWithContext.bind(this);\n  }\n  \n  /**\n   * Invokes the search with enhanced context support\n   * @param input The input query or parameters\n   * @param options Optional invocation options including context\n   * @returns The search results in a GraphState format\n   */\n  public override async invoke(\n    input: unknown, \n    options: ToolContextOptions = {}\n  ): Promise<GraphState> {\n    try {\n      // Update context if provided in options\n      if (options.context) {\n        this.updateContext(options.context as AgentContext);\n      }\n      \n      return await super.invoke(input, options);\n    } catch (error) {\n      Logger.instance.error('Error in SearchStructuredTool.invoke:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Internal method to enhance search options with context information\n   * @param baseOptions Base search options\n   * @param context Current execution context\n   * @returns Enhanced search options with context\n   */\n  private _enhanceWithContext(\n    baseOptions: SearchOptions,\n    context?: AgentContext\n  ): SearchOptions {\n    if (!context) {\n      return baseOptions;\n    }\n    \n    // Create a deep copy of base options to avoid mutating the original\n    const enhancedOptions: SearchOptions = {\n      ...baseOptions,\n      filters: baseOptions.filters ? { ...baseOptions.filters } : {}\n    };\n    \n    // Ensure filters object exists\n    enhancedOptions.filters = enhancedOptions.filters || {};\n    \n    // Add workspace context if available\n    if (context.workspace) {\n      // Add current file as a filter if available\n      if (context.workspace.currentFile) {\n        enhancedOptions.filters.filePath = context.workspace.currentFile;\n      }\n      \n      // Add workspace folders as tags\n      if (context.workspace.workspaceFolders?.length) {\n        const currentTags = Array.isArray(enhancedOptions.filters.tags)\n          ? enhancedOptions.filters.tags\n          : [];\n          \n        enhancedOptions.filters.tags = [\n          ...currentTags,\n          'workspace',\n          ...context.workspace.workspaceFolders\n        ];\n      }\n    }\n    \n    // Add variables context if available\n    if (context.variables) {\n      const currentTags = Array.isArray(enhancedOptions.filters.tags)\n        ? enhancedOptions.filters.tags\n        : [];\n        \n      const variableTags = Object.entries(context.variables)\n        .filter(([_, value]) => value && typeof value === 'object')\n        .map(([key]) => `var_${key}`);\n      \n      enhancedOptions.filters.tags = [\n        ...currentTags,\n        ...variableTags\n      ];\n    }\n    \n    return enhancedOptions;\n  }\n  \n  /**\n   * Executes the search with enhanced context handling\n   * @param input The normalized input (from schema transformation)\n   * @returns The search results in a GraphState format\n   */\n  protected override async _call(input: { query: string; options: SearchOptions }): Promise<GraphState> {\n    try {\n      const { query, options } = input;\n      const context = this.context;\n      \n      // Merge options with defaults and enhance with context\n      const mergedOptions: SearchOptions = {\n        ...this._defaultOptions,\n        ...options,\n        filters: { \n          ...(this._defaultOptions.filters || {}), \n          ...(options.filters || {}) \n        }\n      };\n      \n      const enhancedOptions = this._enhanceWithContext(mergedOptions, context);\n      \n      Logger.instance.debug(`[Search] Executing query: \"${query}\"`, {\n        options: enhancedOptions,\n        hasContext: !!context,\n        contextType: context?.constructor?.name\n      });\n      \n      // Execute the base tool with the enhanced options\n      const baseInput = { \n        query, \n        ...(Object.keys(enhancedOptions).length > 0 ? { options: enhancedOptions } : {}) \n      };\n      \n      // Call the base tool's invoke method with context if it accepts it\n      let result: unknown;\n      const baseTool = this.baseTool as any;\n      \n      if (typeof baseTool.invoke === 'function') {\n        result = baseTool.invoke.length >= 2 \n          ? await baseTool.invoke(baseInput, context)\n          : await baseTool.invoke(baseInput);\n      } else if (typeof baseTool.call === 'function') {\n        result = baseTool.call.length >= 2\n          ? await baseTool.call(baseInput, context)\n          : await baseTool.call(baseInput);\n      } else if (typeof baseTool.execute === 'function') {\n        result = baseTool.execute.length >= 2\n          ? await baseTool.execute(baseInput, context)\n          : await baseTool.execute(baseInput);\n      } else {\n        throw new Error('No valid execution method found on base tool');\n      }\n      \n      // Process and normalize the result\n      return this._processSearchResult(result, query, enhancedOptions);\n      \n    } catch (error) {\n      Logger.instance.error('Error in SearchStructuredTool._call:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Processes and normalizes the search result\n   * @param result - The raw result from the base tool\n   * @param originalQuery - The original search query\n   * @param options - The search options used\n   * @returns A normalized GraphState with the search results\n   */\n  private async _processSearchResult(\n    result: unknown,\n    originalQuery: string,\n    options: SearchOptions\n  ): Promise<GraphState> {\n    try {\n      // Normalize different result formats\n      let items: Array<{ content: string; metadata?: Record<string, unknown> }> = [];\n      \n      if (Array.isArray(result)) {\n        // Handle array of items\n        items = result.map(item => {\n          if (typeof item === 'string') {\n            return { content: item };\n          } else if (item && typeof item === 'object' && 'content' in item) {\n            return {\n              content: String((item as any).content),\n              metadata: (item as any).metadata || {}\n            };\n          }\n          return { content: JSON.stringify(item) };\n        });\n      } else if (result && typeof result === 'object') {\n        // Handle single item or object result\n        const item = result as Record<string, unknown>;\n        items = [{\n          content: 'content' in item ? String(item.content) : JSON.stringify(item),\n          metadata: 'metadata' in item ? (item.metadata as Record<string, unknown>) || {} : {}\n        }];\n      } else if (result !== undefined && result !== null) {\n        // Handle primitive values\n        items = [{ content: String(result) }];\n      }\n      \n      // Apply post-processing based on options\n      if (options.limit && items.length > options.limit) {\n        items = items.slice(0, options.limit);\n      }\n      \n      // Remove metadata if not requested\n      if (!options.includeMetadata) {\n        items = items.map(({ content }) => ({ content }));\n      }\n      \n      // Create a structured result\n      const formattedResults = items.map((item, index) => ({\n        id: `result_${index + 1}`,\n        content: item.content,\n        ...(options.includeMetadata && item.metadata ? { metadata: item.metadata } : {})\n      }));\n      \n      // Create a valid GraphState with all required properties\n      return this._createGraphState(formattedResults, {\n        results: formattedResults,\n        count: formattedResults.length,\n        query: originalQuery,\n        options: options,\n        metadata: {\n          searchOptions: options,\n          timestamp: new Date().toISOString()\n        }\n      });\n      \n    } catch (error) {\n      Logger.instance.error('Error processing search result:', error);\n      \n      // Create an error state with all required GraphState properties\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      return this._createGraphState(null, {\n        error: 'Failed to process search results',\n        details: errorMessage,\n        metadata: {\n          error: {\n            message: 'Failed to process search results',\n            cause: error,\n            timestamp: new Date().toISOString()\n          }\n        }\n      });\n    }\n  }\n}\n\n// Re-export the core workflow types for consistency\nexport type { GraphNode, GraphEdge, GraphDefinition } from './types';\n\n/**\n * Creates a Retrieval-Augmented Generation workflow\n * @param id - Unique identifier for the workflow\n * @param name - Display name of the workflow\n * @param description - Description of the workflow's purpose\n * @param agent - The agent instance to use for generation\n * @param retrievalTool - The retrieval tool to use for document retrieval\n * @returns A GraphDefinition representing the RAG workflow\n * @throws {Error} If required parameters are missing or invalid\n */\nexport function createRAGWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  agent: Agent,\n  retrievalTool: ITool\n): GraphDefinition {\n  if (!id || !name || !description) {\n    throw new Error('Workflow ID, name, and description are required.');\n  }\n  if (!agent) {\n    throw new Error('Agent instance is required for RAG workflow.');\n  }\n  if (!retrievalTool) {\n    throw new Error('Retrieval tool is required for RAG workflow.');\n  }\n\n  Logger.instance.info(`Creating RAG workflow: ${name} (${id})`);\n\n  // Define the workflow nodes\n  const retrieveNode: GraphNode = {\n    id: 'retrieve',\n    label: 'Retrieve Documents',\n    name: 'retrieve',\n    type: 'tool',\n    description: 'Retrieves relevant documents based on the query',\n    tool: retrievalTool,\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      try {\n        const { query } = state as { query: string };\n        if (!query) {\n          throw new Error('No query provided for document retrieval');\n        }\n        \n        // Use the appropriate method based on what's available on the tool\n        let result;\n        if (typeof (retrievalTool as any).invoke === 'function') {\n          result = await (retrievalTool as any).invoke({ query });\n        } else if (typeof (retrievalTool as any).call === 'function') {\n          result = await (retrievalTool as any).call({ query });\n        } else if (typeof (retrievalTool as any).execute === 'function') {\n          result = await (retrievalTool as any).execute({ query });\n        } else {\n          throw new Error('No valid execution method found on retrieval tool');\n        }\n        \n        return { documents: result };\n      } catch (error) {\n        Logger.instance.error('Error in retrieveNode:', error);\n        throw error;\n      }\n    },\n  };\n\n  const generateNode: GraphNode = {\n    id: 'generate',\n    label: 'Generate Response',\n    name: 'generate',\n    type: 'agent',\n    description: 'Generates a response using the retrieved documents',\n    agent: agent,\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      try {\n        const { query, documents } = state as { query: string; documents: unknown };\n        if (!query) {\n          throw new Error('No query provided for generation');\n        }\n        \n        // Prepare the prompt with retrieved documents\n        const prompt = {\n          query,\n          context: documents,\n        };\n        \n        // Use the appropriate method based on what's available on the agent\n        let response;\n        if (typeof (agent as any).invoke === 'function') {\n          response = await (agent as any).invoke(prompt);\n        } else if (typeof (agent as any).call === 'function') {\n          response = await (agent as any).call(prompt);\n        } else if (typeof (agent as any).execute === 'function') {\n          response = await (agent as any).execute(prompt);\n        } else {\n          throw new Error('No valid execution method found on agent');\n        }\n        \n        return { response };\n      } catch (error) {\n        Logger.instance.error('Error in generateNode:', error);\n        throw error;\n      }\n    },\n  };\n\n  // Define the edges (workflow connections)\n  const edges: GraphEdge[] = [\n    {\n      source: 'retrieve',\n      target: 'generate',\n      type: 'success',\n      name: 'retrieve-to-generate',\n      description: 'Pass retrieved documents to generation',\n      condition: (state: any) => !!state.documents\n    },\n    {\n      source: 'retrieve',\n      target: 'generate',\n      type: 'failure',\n      name: 'retrieve-failure',\n      description: 'Handle retrieval failure',\n      condition: (state: any) => !state.documents || (Array.isArray(state.documents) && state.documents.length === 0)\n    }\n  ];\n\n  // Return the complete workflow definition\n  return {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [retrieveNode, generateNode],\n    edges,\n    startNodeId: 'retrieve',\n    operationMode: 'document-qa',\n    tags: ['rag', 'retrieval-augmented-generation', 'document-qa'],\n    metadata: {\n      created: new Date().toISOString(),\n      lastModified: new Date().toISOString()\n    }\n  };\n}\n\n/**\n * Create a multi-agent collaborative workflow definition.\n * This workflow coordinates multiple specialized agents to solve complex tasks,\n * potentially routing tasks based on analysis.\n *\n * @param id - Unique ID for the workflow.\n * @param name - Human-readable name.\n * @param description - Description of the workflow's purpose.\n * @param specialistAgents - Array of specialist agents with their expertise.\n * @param supervisorAgent - The supervisor agent responsible for task analysis and integration.\n * @returns The defined GraphDefinition object.\n * @throws {Error} If required inputs are missing or invalid.\n */\nexport function createCollaborativeWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  specialistAgents: { id: string; name: string; agent: Agent; expertise: string }[],\n  supervisorAgent: Agent\n): GraphDefinition {\n  if (!id || !name || !description) throw new Error('Workflow ID, name, and description are required.');\n  if (!specialistAgents || specialistAgents.length === 0) throw new Error('At least one specialist agent is required.');\n  if (!supervisorAgent) throw new Error('Supervisor agent is required for collaborative workflow.');\n\n  Logger.instance.info(`Creating Collaborative workflow: ${name} (${id})`);\n\n  // --- Define Nodes ---\n  const inputNode = Codessa.createInputNode('input', 'Workflow Input');\n\n  const taskAnalysisNode: GraphNode = {\n    id: 'task-analysis', // Node ID\n    type: 'agent', // Agent node type\n    name: 'Task Analysis (Supervisor)', // Node name\n    label: 'Task Analysis',\n    agent: supervisorAgent, // The supervisor agent performs analysis\n    // execute method defines the logic run by this node\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing task-analysis node with supervisor.');\n      // Get the latest relevant input/message for the supervisor to analyze\n      // This might be the original input or the output of a previous node, depending on graph structure\n      const lastMessage = Array.isArray(state.messages) && state.messages.length > 0 ? state.messages[state.messages.length - 1] : null;\n      const userQuery = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : '';\n\n      // Create a detailed prompt for the supervisor\n      const prompt = `\n            You are the supervisor for a team of specialist AI agents. Your current task is to analyze a user request and route it to the appropriate specialist agent or determine if collaboration is needed.\n\n            User Request: \"${userQuery}\"\n\n            Your team consists of the following specialists:\n            ${specialistAgents.map(spec => `- **${spec.name}** (ID: ${spec.id}): Expert in ${spec.expertise}`).join('\\n')}\n\n            Based on the user's request, determine the *most suitable specialist agent ID* to handle this task.\n            If the task requires input or expertise from *multiple* specialists, respond with the special ID \"COLLABORATE\".\n            If the task is unclear or cannot be routed to any specialist, respond with the special ID \"CLARIFICATION_NEEDED\".\n\n            Respond ONLY with one of the specialist agent IDs, \"COLLABORATE\", or \"CLARIFICATION_NEEDED\".\n            `;\n\n      // Invoke the supervisor agent\n      const analysisResult = await supervisorAgent.generate(prompt); // Assuming generate returns the agent's text response\n\n      Logger.instance.debug(`Supervisor analysis result: ${analysisResult}`);\n\n      // Return the analysis result as state update for conditional routing\n      return {\n        // Store the supervisor's raw output\n        outputs: { 'task-analysis': analysisResult },\n        // Store the routing decision in a specific state key\n        routing_decision: analysisResult.trim().toUpperCase(), // Normalize the decision\n        // Pass the original query or last relevant message for specialist agents\n        user_query: userQuery,\n      };\n    }\n  };\n\n  // Create specialist agent nodes dynamically\n  const specialistNodes: GraphNode[] = specialistAgents.map(specialist => {\n    return {\n      id: `specialist_${specialist.id}`, // Unique ID for the specialist node\n      type: 'agent', // Agent node type\n      name: specialist.name, // Node name from specialist config\n      label: `Specialist: ${specialist.name}`,\n      agent: specialist.agent, // The specialist agent instance\n      execute: async (state: Record<string, unknown>): Promise<GraphState> => {\n        Logger.instance.debug(`Executing specialist agent node: ${specialist.name} (${specialist.id})`);\n        // Access the user query stored by the task-analysis node\n        const userQuery = (state.user_query as string) || (state.inputs as Record<string, unknown>)?.user_query as string || 'No query available.';\n\n        // Create a prompt tailored for the specialist\n        const prompt = `\n                 You are ${specialist.name}, a specialist in ${specialist.expertise}.\n                 The user requires your expertise for the following task: \"${userQuery}\"\n\n                 Please provide a detailed response, analysis, or solution based on your specialization.\n                 If you need assistance from other specialists (in a collaborative workflow), indicate this in your response.\n                 `;\n\n        // Invoke the specialist agent\n        const specialistResponse = await specialist.agent.generate(prompt); // Assuming generate returns text\n\n        Logger.instance.debug(`${specialist.name} response: ${specialistResponse.substring(0, 100)}...`);\n\n        // Store the specialist's response and return the updated state\n        return {\n          ...state, // Spread existing state\n          outputs: { // Update outputs, preserving other outputs if any\n            ...(state.outputs as Record<string, unknown> || {}),\n            [`specialist_${specialist.id}`]: specialistResponse\n          },\n          [`specialist_response_${specialist.id}`]: specialistResponse, // Add/update specialist response\n          user_query: userQuery, // Ensure user_query is passed along\n          // Other state properties from the original state are included by ...state\n        } as unknown as GraphState; // Cast to GraphState to satisfy the return type\n      }\n    };\n  });\n\n  const integrationNode: GraphNode = {\n    id: 'integration', // Node ID\n    type: 'agent', // Agent node type (supervisor performs integration)\n    name: 'Integrate Responses (Supervisor)', // Node name\n    label: 'Integrate Responses',\n    agent: supervisorAgent, // The supervisor agent handles integration\n    // execute method for the integration logic\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing integration node with supervisor.');\n      // Access the original user query and all specialist responses from the state\n      const userQuery = (state.user_query as string) || 'No query available.';\n\n      // Gather specialist responses dynamically from state keys\n      const specialistResponses = specialistAgents.map(spec => {\n        const response = (state[`specialist_response_${spec.id}`] as string) || 'No response provided';\n        return { name: spec.name, expertise: spec.expertise, response };\n      }).filter(sr => sr.response !== 'No response provided' || sr.response.trim().length > 0); // Filter out empty responses\n\n      // Create a prompt for the supervisor to synthesize the responses\n      const prompt = `\n            You are the supervisor. Your task is to synthesize the responses from the specialist agents into a single, coherent, and comprehensive answer for the user.\n\n            User Request: \"${userQuery}\"\n\n            Specialist Responses:\n            ${specialistResponses.map(sr => `--- ${sr.name} (${sr.expertise}) ---\\n${sr.response}`).join('\\n\\n')}\n            ---\n\n            Please integrate these responses. If specialists provided conflicting information or identified gaps, note this and synthesize the best possible answer. Ensure the final response directly addresses the user's original request.\n            `;\n\n      // Invoke the supervisor agent to generate the integrated response\n      const integratedResponse = await supervisorAgent.generate(prompt); // Assuming generate returns text\n\n      Logger.instance.debug('Integrated response generated.');\n\n      // Store the final integrated response in the state\n      return {\n        outputs: { 'integration': integratedResponse }, // Store raw output\n        final_response: integratedResponse, // Store the final response in a dedicated key\n      };\n    }\n  };\n\n  const clarificationNode: GraphNode = {\n    id: 'clarification_needed', // Node ID for clarification\n    type: 'input', // Changed from 'node' to 'input'\n    name: 'Clarification Needed', // Node name\n    label: 'Clarification Needed',\n    // execute logic for clarification\n    execute: async (): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing clarification_needed node.');\n      // This node signifies that the supervisor determined clarification is needed.\n      // We'll generate a simple response asking the user for more info.\n      const message = 'I need more information to understand your request. Could you please provide more details?';\n      Logger.instance.info('Clarification requested.');\n      return {\n        outputs: { 'clarification_needed': message },\n        final_response: message, // Store the clarification message\n      };\n    }\n  };\n\n\n  const outputNode = Codessa.createOutputNode('output', 'Workflow Output');\n\n  // --- Define Edges ---\n  const edges: GraphEdge[] = [\n    // Start from input, go to task analysis\n    { source: inputNode.id, target: taskAnalysisNode.id, type: 'default', name: 'input_to_taskAnalysis' }\n  ];\n\n  // Conditional edges from task analysis based on routing decision\n  specialistAgents.forEach(spec => {\n    edges.push({\n      source: taskAnalysisNode.id,\n      target: `specialist_${spec.id}`,\n      type: 'default',\n      name: `taskAnalysis_to_specialist_${spec.id}`,\n      condition: async (state: Record<string, unknown>) => {\n        const routingDecision = (state.routing_decision as string)?.toUpperCase() || '';\n        return routingDecision === spec.id.toUpperCase();\n      }\n    });\n  });\n\n  // Edge from task analysis to integration if collaboration is needed\n  edges.push({\n    source: taskAnalysisNode.id,\n    target: integrationNode.id,\n    type: 'default',\n    name: 'taskAnalysis_to_integration',\n      condition: async (state: Record<string, unknown>) => {\n        const routingDecision = (state.routing_decision as string)?.toUpperCase() || '';\n        return routingDecision === 'COLLABORATE';\n      }\n  });\n\n  // Edge from task analysis to clarification node if clarification is needed\n  edges.push({\n    source: taskAnalysisNode.id,\n    target: clarificationNode.id,\n    type: 'default',\n    name: 'taskAnalysis_to_clarification',\n      condition: async (state: Record<string, unknown>) => {\n        const routingDecision = (state.routing_decision as string)?.toUpperCase() || '';\n        return routingDecision === 'CLARIFICATION_NEEDED';\n      }\n  });\n\n\n  // Edges from each specialist node back to the integration node\n  // If a specialist finishes, they pass their response to the integration step\n  specialistAgents.forEach(spec => {\n    edges.push({\n      source: `specialist_${spec.id}`,\n      target: integrationNode.id,\n      type: 'default',\n      name: `specialist_${spec.id}_to_integration`\n    });\n  });\n\n  // Edges from integration and clarification nodes to the final output\n  edges.push(\n    { source: integrationNode.id, target: outputNode.id, type: 'default', name: 'integration_to_output' },\n    { source: clarificationNode.id, target: outputNode.id, type: 'default', name: 'clarification_to_output' }\n  );\n\n\n  // --- Create Workflow Definition ---\n  const workflowDefinition: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'multi-agent', // OperationMode for collaborative workflow\n    nodes: [\n      inputNode,\n      taskAnalysisNode,\n      ...specialistNodes, // Include all dynamically created specialist nodes\n      integrationNode,\n      clarificationNode, // Include clarification node\n      outputNode\n    ],\n    edges,\n    startNodeId: inputNode.id // The workflow starts at the input node\n  };\n\n  // Register workflow definition\n  workflowRegistry.registerWorkflow(workflowDefinition);\n  Logger.instance.info(`Collaborative workflow '${name}' (${id}) created and registered.`);\n\n  return workflowDefinition;\n}\n\n/**\n * Create a memory-enhanced agent workflow definition with long-term recall and saving.\n * This workflow integrates memory retrieval *before* the agent runs\n * and memory saving *after* the agent responds.\n *\n * @param id - Unique ID for the workflow.\n * @param name - Human-readable name.\n * @param description - Description of the workflow's purpose.\n * @param agent - The agent instance to be enhanced with memory.\n * @returns The defined GraphDefinition object.\n * @throws {Error} If required inputs are missing or invalid.\n */\nexport function createMemoryEnhancedAgentWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  agent: Agent\n): GraphDefinition {\n  if (!id || !name || !description) throw new Error('Workflow ID, name, and description are required.');\n  if (!agent) throw new Error('Agent instance is required for memory-enhanced workflow.');\n\n  // ... (rest of the code remains the same)\n  Logger.instance.info(`Creating Memory-Enhanced Agent workflow: ${name} (${id})`);\n\n  // --- Define Nodes ---\n  const inputNode = Codessa.createInputNode('input', 'Workflow Input');\n\n  // Create memory tools\n  const memoryRetrievalTool: ITool = {\n    id: 'memory_retriever',\n    name: 'memory_retriever',\n    description: 'Retrieves memories based on a query with contextual enhancement',\n    type: 'single-action',\n    singleActionSchema: z.object({\n      query: z.string().describe('The query to search for in memory')\n    }),\n    execute: async (_actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> => {\n      const query = input.query || '';\n      try {\n        // Build comprehensive search options using full context\n        const baseFilter: MemoryFilter = {\n          source: undefined,\n          type: ['text', 'code', 'conversation', 'insight'],\n          tags: [],\n          filePath: undefined,\n          changeType: undefined\n        };\n\n        // Create search options with proper typing\n        const searchOptions: MemorySearchOptions = {\n          query,\n          limit: 20,\n          threshold: 0.7,\n          includeMetadata: true,\n          contextual: true,\n          filter: baseFilter\n        };\n\n        // Helper function to safely update filter\n        const updateFilter = (updates: Partial<MemoryFilter>) => {\n          if (!searchOptions.filter) {\n            searchOptions.filter = baseFilter;\n          }\n          searchOptions.filter = { ...searchOptions.filter, ...updates };\n        };\n\n        // Use workspace context for file-specific and project-specific memory retrieval\n        if (_context?.workspace) {\n          // Use current file for precise context\n          if (_context.workspace.currentFile) {\n            updateFilter({ filePath: _context.workspace.currentFile });\n          }\n\n          // Use workspace folders for project-level context\n          if (_context.workspace.workspaceFolders && _context.workspace.workspaceFolders.length > 0) {\n            updateFilter({\n              tags: [...(searchOptions.filter?.tags || []), 'workspace', _context.workspace.workspaceFolders[0]]\n            });\n          }\n\n          // Use text selection for more targeted search\n          if (_context.workspace.selection?.text) {\n            // Enhance query with selected text context\n            searchOptions.query = `${query} ${_context.workspace.selection.text}`;\n          }\n        }\n\n        // Use file path context if available (more general than currentFile)\n        if (_context?.filePath) {\n          updateFilter({\n            filePath: _context.filePath,\n            tags: [...(searchOptions.filter?.tags || []), 'file_context']\n          });\n        }\n\n        // Use debug context for error-related memory retrieval\n        if (_context?.debugData) {\n          if (_context.debugData.lastError) {\n            // Include error message in query for better context\n            searchOptions.query = `${query} error: ${_context.debugData.lastError.message}`;\n            updateFilter({\n              tags: [...(searchOptions.filter?.tags || []), 'error_context', 'debug']\n            });\n          }\n\n          // Use analysis timestamp for temporal filtering\n          if (_context.debugData.analysisTimestamp) {\n            updateFilter({\n              fromTimestamp: _context.debugData.analysisTimestamp - (24 * 60 * 60 * 1000), // 24 hours before analysis\n              toTimestamp: Date.now()\n            });\n          }\n        }\n\n        // Use variables from context for enhanced metadata filtering\n        if (_context?.variables) {\n          const contextTags = ['context_variables'];\n\n          // Extract meaningful tags from available managers\n          if (_context.variables.workflowManager) contextTags.push('workflow_context');\n          if (_context.variables.mcpManager) contextTags.push('mcp_context');\n          if (_context.variables.promptManager) contextTags.push('prompt_context');\n          if (_context.variables.knowledgebaseManager) contextTags.push('knowledge_context');\n\n          // Add any additional context variables as tags\n          Object.keys(_context.variables).forEach(key => {\n            if (!['workflowManager', 'mcpManager', 'promptManager', 'knowledgebaseManager'].includes(key)) {\n              contextTags.push(`var_${key}`);\n            }\n          });\n\n          updateFilter({\n            tags: [...(searchOptions.filter?.tags || []), ...contextTags]\n          });\n        }\n\n        // Use streaming context if available\n        if (_context?.streamingContext) {\n          updateFilter({\n            tags: [...(searchOptions.filter?.tags || []), 'streaming_context', _context.streamingContext.streamId]\n          });\n\n          // Check for cancellation during memory search\n          if (_context.streamingContext.cancellationToken?.isCancellationRequested) {\n            return {\n              success: false,\n              error: 'Memory retrieval cancelled',\n              toolId: 'memory_retriever'\n            };\n          }\n        }\n\n        // Use tools context for tool-related memory\n        if (_context?.tools && _context.tools.size > 0) {\n          updateFilter({\n            tags: [...(searchOptions.filter?.tags || []), 'tools_context', ...Array.from(_context.tools.keys())]\n          });\n        }\n\n        // Use cancellation token if available\n        if (_context?.cancellationToken?.isCancellationRequested) {\n          return {\n            success: false,\n            error: 'Memory retrieval cancelled',\n            toolId: 'memory_retriever'\n          };\n        }\n\n        const memories = await codessaMemoryManager.searchMemories(searchOptions);\n        const memoryContent = memories.map(m => {\n          const metadata = m.metadata;\n          return `[${new Date(m.timestamp).toISOString()}] ${metadata.filePath ? `File: ${metadata.filePath}` : ''} ${metadata.tags ? `Tags: ${metadata.tags.join(', ')}` : ''}\\n${m.content}`;\n        }).join('\\n\\n');\n\n        return {\n          success: true,\n          output: memoryContent,\n          toolId: 'memory_retriever',\n          metadata: {\n            memoriesFound: memories.length,\n            contextUsed: {\n              workspace: !!_context?.workspace,\n              debugData: !!_context?.debugData,\n              variables: !!_context?.variables,\n              filePath: !!_context?.filePath,\n              streaming: !!_context?.streamingContext,\n              tools: !!_context?.tools\n            }\n          }\n        };\n      } catch (error) {\n        Logger.instance.error('Error retrieving memories:', error);\n        return {\n          success: false,\n          error: 'Failed to retrieve memories',\n          toolId: 'memory_retriever'\n        };\n      }\n    }\n  };\n\n  const memorySaveTool: ITool = {\n    id: 'memory_saver',\n    name: 'memory_saver',\n    description: 'Saves content to memory',\n    type: 'single-action',\n    singleActionSchema: z.object({\n      content: z.string().describe('The content to save to memory'),\n      metadata: z.record(z.string(), z.any()).optional().describe('Optional metadata to associate with the memory')\n    }),\n    execute: async (_actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> => {\n      const content = input.content || '';\n      const metadata = input.metadata || {};\n\n      // Use context to enhance metadata if available\n      const enhancedMetadata = {\n        source: 'system' as MemorySource,\n        type: 'text' as MemoryType,\n        timestamp: Date.now(),\n        // Add context information if available\n        ...(!!_context && {\n          hasContext: true,\n          contextType: _context.constructor?.name || 'AgentContext'\n        }),\n        ...metadata\n      };\n\n      try {\n        const memory = await codessaMemoryManager.addMemory({\n          content,\n          metadata: enhancedMetadata\n        });\n        return {\n          success: true,\n          output: `Memory saved successfully with ID: ${memory.id}`,\n          toolId: 'memory_saver'\n        };\n      } catch (error) {\n        Logger.instance.error('Error saving memory:', error);\n        return {\n          success: false,\n          error: 'Failed to save memory',\n          toolId: 'memory_saver'\n        };\n      }\n    }\n  };\n\n  // Wrap tools if they aren't already StructuredTools\n  const memoryRetrievalStructuredTool = memoryRetrievalTool instanceof StructuredTool\n    ? memoryRetrievalTool\n    // Create a minimal schema wrapper if the tool isn't structured\n    : new (class extends ToolSchemaWrapper {\n      override schema = z.object({ query: z.string() }).transform((obj) => obj.query);\n      constructor(baseTool: Tool | ITool) { super(baseTool, 'memory_retriever'); }\n    })(memoryRetrievalTool);\n\n  const memorySaveStructuredTool = memorySaveTool instanceof StructuredTool\n    ? memorySaveTool\n    // Create a minimal schema wrapper if the tool isn't structured\n    : new (class extends ToolSchemaWrapper {\n      override schema = z.object({ content: z.string(), metadata: z.record(z.string(), z.any()).optional() });\n      constructor(baseTool: Tool | ITool) { super(baseTool, 'memory_saver'); }\n    })(memorySaveTool);\n\n\n  const memoryRetrievalNode = Codessa.createToolNode(\n    'memory-retrieval', // Node ID\n    'Memory Retrieval', // Node name\n    memoryRetrievalStructuredTool // The StructuredTool instance for retrieval\n  );\n\n  const contextEnhancementNode: GraphNode = {\n    id: 'context-enhancement', // Node ID\n    type: 'tool', // Changed from 'node' to 'tool'\n    name: 'Enhance Context with Memories', // Node name\n    label: 'Enhance Context',\n    // execute method defines the logic\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing context-enhancement node.');\n      // Get the results from the 'memory-retrieval' node's output\n      const retrievedMemories = (state.outputs as Record<string, unknown>)?.['memory-retrieval']; // Access using node ID\n\n      // Format retrieved memories into a string context for the agent\n      let enhancedContext = '';\n      if (typeof retrievedMemories === 'string' && retrievedMemories.length > 0) {\n        enhancedContext = `\n Relevant insights from past interactions:\n ---\n ${retrievedMemories}\n ---\n Use these insights to inform your response and maintain continuity.\n `;\n      } else if (Array.isArray(retrievedMemories) && retrievedMemories.length > 0) {\n        // Assume retrievedMemories is an array of formatted strings or objects with 'content'\n        enhancedContext = `\n Relevant insights from past interactions:\n ---\n ${retrievedMemories.map((mem, index) => {\n          const content = typeof mem === 'string' ? mem : (typeof mem === 'object' && mem !== null && 'content' in mem) ? String((mem as Record<string, unknown>).content) : JSON.stringify(mem);\n          return `Memory ${index + 1}: ${content}`;\n        }).join('\\n\\n')}\n ---\n Use these insights to inform your response and maintain continuity.\n `;\n      } else {\n        enhancedContext = ''; // No memories found, provide empty context\n        Logger.instance.debug('Context enhancement received no retrieved memories.');\n      }\n\n      // Store the enhanced context in the state for the agent node\n      return {\n        enhanced_context: enhancedContext, // Store formatted context\n        // Optional: Directly add to a 'context' field if agent expects it there\n        context: enhancedContext,\n      };\n    }\n  };\n\n\n  const agentNode: GraphNode = {\n    id: 'agent', // Node ID\n    type: 'agent', // Agent node type\n    name: 'Memory Agent', // Node name\n    label: 'Memory Agent',\n    agent: agent, // The agent instance\n    // execute logic for the agent\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing memory-enhanced agent node.');\n      // Get the original user query and the enhanced context\n      const messages = (state.messages as unknown[]) || [];\n      const lastMessage = messages.length > 0 ? messages[messages.length - 1] as Record<string, unknown> : null;\n      const userQuery = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : '';\n\n      const enhancedContext = (state.enhanced_context as string) || (state.context as string) || ''; // Get context from state\n\n      // Construct the prompt for the agent using promptManager\n      const prompt = promptManager.renderPrompt('workflow.memoryAgent', {\n        enhancedContext,\n        userQuery\n      });\n\n      // Invoke the agent's generation method\n      const agentResponse = await agent.generate(prompt); // Assuming generate returns text\n\n      Logger.instance.debug('Memory agent generated response.');\n\n      // Return the agent's response as node output and in state\n      return {\n        outputs: { agent: agentResponse },\n        agent_response: agentResponse,\n      };\n    }\n  };\n\n  const memorySaveNode = Codessa.createToolNode(\n    'memory-save', // Node ID\n    'Memory Save', // Node name\n    memorySaveStructuredTool // The StructuredTool instance for saving\n  );\n\n  // A custom node might be needed *before* memory-save to format the content/metadata to save\n  const prepareMemorySaveNode: GraphNode = {\n    id: 'prepare-memory-save',\n    type: 'tool', // Changed from 'node' to 'tool'\n    name: 'Prepare Memory Content',\n    label: 'Prepare Memory Content',\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing prepare-memory-save node.');\n      // Get the original user query and the agent's response\n      const messages = (state.messages as unknown[]) || [];\n      const lastMessage = messages.length > 0 ? messages[messages.length - 1] as Record<string, unknown> : null;\n      const userQuery = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : '';\n\n      const agentResponse = (state.agent_response as string) || ((state.outputs as Record<string, unknown>)?.agent as string) || ''; // Get agent's response\n\n      // Decide what content and metadata to save to memory\n      // This is a crucial step - extract key information or conversation summary\n      const memoryContent = `Conversation Summary:\\nUser: \"${userQuery.substring(0, 200)}...\"\\nAgent: \"${agentResponse.substring(0, 200)}...\"`;\n\n      // Prepare metadata for the memory entry\n      const memoryMetadata = {\n        source: 'conversation', // Source type\n        type: 'dialogue_summary', // Specific type\n        userQuerySnippet: userQuery.substring(0, 50), // Snippet for quick identification\n        agentResponseSnippet: agentResponse.substring(0, 50),\n        timestamp: Date.now(),\n        // Add other relevant context like session ID, user ID if available in state\n        // sessionId: state.sessionId,\n        // userId: state.userId,\n      };\n\n      // Store the prepared input for the 'memory-save' tool node\n      return {\n        outputs: { // Output structure expected by the next node (tool node)\n          'prepare-memory-save': { // Key matches node ID\n            content: memoryContent,\n            metadata: memoryMetadata\n          }\n        },\n        // Store the prepared content/metadata in state for potential later use/debugging\n        memory_to_save_content: memoryContent,\n        memory_to_save_metadata: memoryMetadata,\n      };\n    }\n  };\n\n\n  const outputNode = Codessa.createOutputNode('output', 'Workflow Output');\n\n\n  // --- Define Edges ---\n  const edges: GraphEdge[] = [\n    { source: inputNode.id, target: memoryRetrievalNode.id, type: 'default', name: `${inputNode.id}-to-${memoryRetrievalNode.id}` }, // Input -> Retrieval\n    { source: memoryRetrievalNode.id, target: contextEnhancementNode.id, type: 'default', name: `${memoryRetrievalNode.id}-to-${contextEnhancementNode.id}` }, // Retrieval Results -> Context Enhancement\n    { source: contextEnhancementNode.id, target: agentNode.id, type: 'default', name: `${contextEnhancementNode.id}-to-${agentNode.id}` }, // Enhanced Context -> Agent\n    // Agent output goes to prepare for saving\n    { source: agentNode.id, target: prepareMemorySaveNode.id, type: 'default', name: `${agentNode.id}-to-${prepareMemorySaveNode.id}` },\n    // Prepared content goes to memory saving tool\n    { source: prepareMemorySaveNode.id, target: memorySaveNode.id, type: 'default', name: `${prepareMemorySaveNode.id}-to-${memorySaveNode.id}` },\n    // Memory save result goes to final output\n    { source: memorySaveNode.id, target: outputNode.id, type: 'default', name: `${memorySaveNode.id}-to-${outputNode.id}` }\n  ];\n\n  // --- Create Workflow Definition ---\n  const workflowDefinition: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'memory', // OperationMode for memory-enhanced workflow\n    nodes: [\n      inputNode,\n      memoryRetrievalNode,\n      contextEnhancementNode,\n      agentNode,\n      prepareMemorySaveNode, // Include the preparation node\n      memorySaveNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: inputNode.id // The workflow starts at the input node\n  };\n\n  // Register workflow definition\n  workflowRegistry.registerWorkflow(workflowDefinition);\n  Logger.instance.info(`Memory-Enhanced Agent workflow '${name}' (${id}) created and registered.`);\n\n  return workflowDefinition;\n}\n\n\n/**\n * Create a code generation and review workflow definition.\n * This workflow orchestrates specialized agents for iterative code creation and review.\n *\n * @param id - Unique ID for the workflow.\n * @param name - Human-readable name.\n * @param description - Description of the workflow's purpose.\n * @param codeGenerationAgent - The agent instance specialized in code generation.\n * @param codeReviewAgent - The agent instance specialized in code review.\n * @returns The defined GraphDefinition object.\n * @throws {Error} If required inputs are missing or invalid.\n */\nexport function createCodeGenerationWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  codeGenerationAgent: Agent,\n  codeReviewAgent: Agent\n): GraphDefinition {\n  if (!id || !name || !description) throw new Error('Workflow ID, name, and description are required.');\n  if (!codeGenerationAgent) throw new Error('Code generation agent is required.');\n  if (!codeReviewAgent) throw new Error('Code review agent is required.');\n\n  Logger.instance.info(`Creating Code Generation workflow: ${name} (${id})`);\n\n  // --- Define Nodes ---\n  const inputNode = Codessa.createInputNode('input', 'Workflow Input (Requirements)');\n\n  const requirementsAnalysisNode: GraphNode = {\n    id: 'requirements-analysis',\n    type: 'agent',\n    name: 'Analyze Requirements',\n    label: 'Analyze Requirements',\n    agent: codeGenerationAgent, // Code generation agent can also analyze requirements\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing requirements-analysis node.');\n      const messages = (state.messages as unknown[]) || [];\n      const lastMessage = messages.length > 0 ? messages[messages.length - 1] as Record<string, unknown> : null;\n      const userRequest = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : ((state.inputs as Record<string, unknown>)?.input as string) || ''; // Get input\n\n      const prompt = `\n            You are an AI assistant specializing in analyzing software requirements.\n            Analyze the following user request and break it down into a clear, structured set of requirements for a code generation agent.\n\n            User Request: \"${typeof userRequest === 'string' ? userRequest : JSON.stringify(userRequest)}\"\n\n            Provide the requirements in a structured format, including:\n            - Programming Language: (e.g., Python, TypeScript, Java)\n            - Core Functionality: Detailed description of what the code should do.\n            - Inputs: What data does the code take?\n            - Outputs: What data does the code produce?\n            - Constraints & Edge Cases: Any specific limitations, performance needs, security considerations, or unusual scenarios to handle.\n            - Desired Output Format: (e.g., just the code, code with explanations, specific file structure)\n\n            Format your response clearly so it can be directly used by a code generator.\n            `;\n\n      const analysisResult = await codeGenerationAgent.generate(prompt);\n\n      Logger.instance.debug('Requirements analysis generated.');\n      return {\n        outputs: { 'requirements-analysis': analysisResult },\n        requirements: analysisResult, // Store the analysis in state\n        user_request: userRequest, // Pass original request\n      };\n    }\n  };\n\n  const codeGenerationNode: GraphNode = {\n    id: 'code-generation',\n    type: 'agent',\n    name: 'Generate Code',\n    label: 'Generate Code',\n    agent: codeGenerationAgent,\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing code-generation node.');\n      const requirements = (state.requirements as string) || 'No requirements provided.';\n\n      const prompt = `\n            You are an AI code generation expert. Write code based on the following requirements.\n            Aim for clean, readable, and efficient code. Include comments where necessary.\n\n            Requirements:\n            ---\n            ${requirements}\n            ---\n\n            Provide the generated code.\n            `;\n\n      const generatedCode = await codeGenerationAgent.generate(prompt);\n\n      Logger.instance.debug('Code generated.');\n      return {\n        outputs: { 'code-generation': generatedCode },\n        generated_code: generatedCode, // Store generated code\n        requirements: requirements, // Pass requirements along\n        user_request: state.user_request, // Pass user request along\n      };\n    }\n  };\n\n  const codeReviewNode: GraphNode = {\n    id: 'code-review',\n    type: 'agent',\n    name: 'Review Code',\n    label: 'Review Code',\n    agent: codeReviewAgent,\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing code-review node.');\n      const generatedCode = (state.generated_code as string) || 'No code provided.';\n      const requirements = (state.requirements as string) || 'No requirements provided.';\n\n      const prompt = `\n            You are an AI code reviewer. Review the following code against the provided requirements.\n\n            Requirements:\n            ---\n            ${requirements}\n            ---\n\n            Generated Code:\n            ---\n            ${generatedCode}\n            ---\n\n            Critique the code based on:\n            1. Correctness (Does it meet requirements?)\n            2. Efficiency & Performance\n            3. Readability & Documentation\n            4. Adherence to Best Practices & Idioms (for the specified language)\n            5. Potential Security Vulnerabilities\n\n            Provide constructive feedback and clear suggestions for improvement. If the code looks good, state that clearly.\n            `;\n\n      const reviewResult = await codeReviewAgent.generate(prompt);\n\n      Logger.instance.debug('Code review completed.');\n      return {\n        outputs: { 'code-review': reviewResult },\n        code_review: reviewResult, // Store the review result\n        generated_code: generatedCode, // Pass code along\n        requirements: requirements, // Pass requirements along\n        user_request: state.user_request, // Pass user request along\n      };\n    }\n  };\n\n  // Custom node to decide if refinement is needed based on the review\n  const reviewDecisionNode: GraphNode = {\n    id: 'review-decision',\n    type: 'process', // Changed from 'conditional' to 'process'\n    name: 'Decide Refinement',\n    label: 'Decide Refinement',\n    // execute determines the next step based on state (the review result)\n    execute: async (state: Record<string, unknown>): Promise<string> => {\n      Logger.instance.debug('Executing review-decision node.');\n      const codeReview = (state.code_review as string) || '';\n\n      // Analyze the review to decide if refinement is necessary.\n      // This is a simplified heuristic. A real implementation might use an LLM call here\n      // or a more sophisticated parser.\n      // Look for keywords indicating issues or requests for changes.\n      const needsRefinement = codeReview.toLowerCase().includes('suggestion') ||\n        codeReview.toLowerCase().includes('consider') ||\n        codeReview.toLowerCase().includes('improve') ||\n        codeReview.toLowerCase().includes('change') ||\n        codeReview.toLowerCase().includes('feedback') ||\n        codeReview.toLowerCase().includes('issue');\n\n      if (needsRefinement) {\n        Logger.instance.info('Code review indicates refinement is needed. Routing to code-refinement.');\n        return 'code-refinement'; // Return the target node ID\n      } else {\n        Logger.instance.info('Code review indicates no significant issues. Routing directly to output.');\n        return 'output'; // Return the target node ID\n      }\n    }\n  };\n\n\n  const codeRefinementNode: GraphNode = {\n    id: 'code-refinement',\n    type: 'agent',\n    name: 'Refine Code',\n    label: 'Refine Code',\n    agent: codeGenerationAgent, // Code generation agent refines based on review\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing code-refinement node.');\n      const generatedCode = (state.generated_code as string) || 'No code provided.';\n      const codeReview = (state.code_review as string) || 'No review provided.';\n      const requirements = (state.requirements as string) || 'No requirements provided.'; // Pass requirements to refinement prompt\n\n      const prompt = `\n            You are an AI code expert tasked with refining code based on feedback.\n\n            Original Code:\n            ---\n            ${generatedCode}\n            ---\n\n            Code Review Feedback:\n            ---\n            ${codeReview}\n            ---\n\n            Requirements:\n            ---\n            ${requirements}\n            ---\n\n            Carefully address the feedback from the code review. Provide the improved code, and include a brief summary of the changes you made.\n            `;\n\n      const refinedResult = await codeGenerationAgent.generate(prompt); // Assuming generates refined code + summary\n\n      Logger.instance.debug('Code refinement completed.');\n      return {\n        outputs: { 'code-refinement': refinedResult },\n        final_code_result: refinedResult, // Store the final result (code + summary)\n      };\n    }\n  };\n\n\n  const outputNode = Codessa.createOutputNode('output', 'Workflow Output');\n\n\n  // --- Define Edges ---\n  const edges: GraphEdge[] = [\n    // Start from input, go to requirements analysis\n    { source: inputNode.id, target: requirementsAnalysisNode.id, type: 'default', name: 'input-to-requirementsAnalysis' },\n    // Analysis -> Generation\n    { source: requirementsAnalysisNode.id, target: codeGenerationNode.id, type: 'default', name: 'requirementsAnalysis-to-codeGeneration' },\n    // Generation -> Review\n    { source: codeGenerationNode.id, target: codeReviewNode.id, type: 'default', name: 'codeGeneration-to-codeReview' },\n    // Review -> Decision node (conditional routing)\n    { source: codeReviewNode.id, target: reviewDecisionNode.id, type: 'default', name: 'codeReview-to-reviewDecision' },\n\n    // Conditional edges from Decision node\n    { source: reviewDecisionNode.id, target: codeRefinementNode.id, type: 'default', name: 'reviewDecision-to-codeRefinement', condition: async (state) => Promise.resolve(state.next_node_id === 'code-refinement') },\n    { source: reviewDecisionNode.id, target: outputNode.id, type: 'default', name: 'reviewDecision-to-output', condition: async (state) => Promise.resolve(state.next_node_id === 'output') },\n\n    // Refinement -> Output\n    { source: codeRefinementNode.id, target: outputNode.id, type: 'default', name: 'codeRefinement-to-output' },\n  ];\n\n  // The reviewDecisionNode execute method should explicitly return the ID of the next node.\n  // Update the execute method of reviewDecisionNode to return string ID:\n  // execute: async (state: any): Promise<string> => { ... return 'code-refinement' | 'output'; }\n  // And the edge conditions should check state.outputs['review-decision'] or state.next_node_id\n\n  // Let's refactor reviewDecisionNode execute to return the string ID directly\n  reviewDecisionNode.execute = async (state: Record<string, unknown>): Promise<string> => {\n    Logger.instance.debug('Executing review-decision node.');\n    const codeReview = (state.code_review as string) || '';\n\n    // Analyze the review to decide if refinement is necessary.\n    // Look for keywords indicating issues or requests for changes.\n    const needsRefinement = codeReview.toLowerCase().includes('suggestion') ||\n      codeReview.toLowerCase().includes('consider') ||\n      codeReview.toLowerCase().includes('improve') ||\n      codeReview.toLowerCase().includes('change') ||\n      codeReview.toLowerCase().includes('feedback') ||\n      codeReview.toLowerCase().includes('issue');\n\n    if (needsRefinement) {\n      Logger.instance.info('Code review indicates refinement is needed. Routing to code-refinement.');\n      return 'code-refinement'; // Return the target node ID\n    } else {\n      Logger.instance.info('Code review indicates no significant issues. Routing directly to output.');\n      return 'output'; // Return the target node ID\n    }\n  };\n\n  // Adjust the conditional edges to use the execute method's return value\n  // Codessa handles this transition based on the string returned by execute for conditional nodes.\n  // The 'condition' property is not needed for the node that itself determines the next state.\n  // Remove the conditional edges above and rely on the reviewDecisionNode.execute return value.\n  // This means we need to remove the `type: 'conditional'` from edges leaving the reviewDecisionNode\n  // and update the execute method return type to `Promise<string>`.\n\n  // Remove redundant conditional edges and redefine based on node.execute returning string:\n  edges.splice(edges.findIndex(e => e.source === reviewDecisionNode.id && e.target === codeRefinementNode.id), 1);\n  edges.splice(edges.findIndex(e => e.source === reviewDecisionNode.id && e.target === outputNode.id), 1);\n\n  // Add new edges where the decision node has no defined target type, relying on execute.\n  // Codessa interprets a non-default, non-conditional edge from a node whose execute\n  // returns a string as a transition to that string's ID.\n  edges.push(\n    { source: reviewDecisionNode.id, target: codeRefinementNode.id, type: 'default', name: `${reviewDecisionNode.id}->${codeRefinementNode.id}` }, // Added type 'default' and name\n    { source: reviewDecisionNode.id, target: outputNode.id, type: 'default', name: `${reviewDecisionNode.id}->${outputNode.id}` }, // Added type 'default' and name\n  );\n\n\n  // --- Create Workflow Definition ---\n  const workflowDefinition: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'codegen', // OperationMode for code generation workflow\n    nodes: [\n      inputNode,\n      requirementsAnalysisNode,\n      codeGenerationNode,\n      codeReviewNode,\n      reviewDecisionNode, // Include the decision node\n      codeRefinementNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: inputNode.id // The workflow starts at the input node\n  };\n\n  // Register workflow definition\n  workflowRegistry.registerWorkflow(workflowDefinition);\n  Logger.instance.info(`Code Generation workflow '${name}' (${id}) created and registered.`);\n\n  return workflowDefinition;\n}\n\n\n/**\n * Create an autonomous research agent workflow definition.\n * This workflow coordinates an agent with a search tool to research a topic,\n * gather, analyze, and synthesize information.\n *\n * @param id - Unique ID for the workflow.\n * @param name - Human-readable name.\n * @param description - Description of the workflow's purpose.\n * @param agent - The agent instance responsible for analysis and synthesis.\n * @param searchTool - The tool to use for searching information.\n * @returns The defined GraphDefinition object.\n * @throws {Error} If required inputs are missing or invalid.\n */\nexport function createResearchWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  agent: Agent,\n  searchTool: Tool | ITool\n): GraphDefinition {\n  if (!id || !name || !description) throw new Error('Workflow ID, name, and description are required.');\n  if (!agent) throw new Error('Agent instance is required for research workflow.');\n  if (!searchTool) throw new Error('Search tool is required for research workflow.');\n\n  Logger.instance.info(`Creating Research workflow: ${name} (${id})`);\n\n  // Ensure the search tool is properly wrapped\n  const wrappedSearchTool = searchTool instanceof SearchStructuredTool \n    ? searchTool \n    : new SearchStructuredTool(searchTool, 'SearchTool', 'Performs web searches'); // Wrap if not already wrapped\n\n\n  // --- Define Nodes ---\n  const inputNode = Codessa.createInputNode('input', 'Workflow Input (Research Topic)');\n\n  const topicAnalysisNode: GraphNode = {\n    id: 'topic-analysis',\n    type: 'agent',\n    name: 'Analyze Topic',\n    label: 'Analyze Topic',\n    agent: agent, // Agent performs analysis\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing topic-analysis node.');\n      const messages = (state.messages as unknown[]) || [];\n      const lastMessage = messages.length > 0 ? messages[messages.length - 1] as Record<string, unknown> : null;\n      const userTopic = (lastMessage && typeof lastMessage === 'object' && 'content' in lastMessage) ? String(lastMessage.content) || '' : ((state.inputs as Record<string, unknown>)?.input as string) || ''; // Get input\n\n      if (typeof userTopic !== 'string' || userTopic.trim().length === 0) {\n        const errorMsg = 'Research topic input is missing or empty.';\n        Logger.instance.error(errorMsg);\n        // Route to a failure or output node indicating the error\n        return { error: errorMsg, next_node_id: 'output' }; // Use next_node_id for routing\n      }\n\n\n      const prompt = promptManager.renderPrompt('workflow.topicAnalysis', {\n        topic: userTopic\n      });\n\n      const analysisResult = await agent.generate(prompt); // Assuming generate returns text\n\n      Logger.instance.debug('Topic analysis generated.');\n\n      // Attempt to extract search queries from the analysis result.\n      // This is a simple heuristic; a more robust solution would use an LLM to parse the analysis.\n      const extractedQueries: string[] = [];\n      const lines = analysisResult.split('\\n');\n      lines.forEach(line => {\n        // Simple pattern matching for lines that look like search queries\n        const match = line.match(/Search Queries?:?\\s*[\"']?([^\"']+)[\"']?/i);\n        if (match && match[1]) {\n          extractedQueries.push(match[1].trim());\n        } else {\n          // Fallback: if analysis didn't yield structured queries, use generic ones\n          // This fallback might overwrite carefully extracted ones, improve extraction logic.\n          if (line.includes('search query') || line.includes('investigate')) {\n            const potentialQuery = line.replace(/.*(search query|investigate)[:-]?\\s*/i, '').trim();\n            if (potentialQuery.length > 5 && potentialQuery.length < 100) { // Basic length check\n              extractedQueries.push(potentialQuery);\n            }\n          }\n        }\n      });\n\n      // Ensure at least one query if extraction failed\n      if (extractedQueries.length === 0) {\n        Logger.instance.warn('Failed to extract specific search queries from analysis. Using fallback queries.');\n        extractedQueries.push(\n          userTopic,\n          `latest research on ${userTopic}`,\n          `${userTopic} challenges and solutions`\n        );\n      }\n\n      // Limit the number of queries to avoid excessive tool calls\n      const limitedQueries = extractedQueries.slice(0, 5); // Limit to 5 queries\n\n\n      return {\n        outputs: { 'topic-analysis': analysisResult }, // Store analysis text\n        research_plan: analysisResult, // Store plan in state\n        search_queries: limitedQueries, // Store extracted queries for next step\n        user_topic: userTopic, // Pass original topic\n      };\n    }\n  };\n\n  // Create information gathering node\n  Codessa.createToolNode(\n    'information-gathering', // Node ID\n    'Gather Information', // Node name\n    searchStructuredTool // The StructuredTool instance for searching\n  );\n\n  // Custom node to process search queries iteratively\n  const processQueriesNode: GraphNode = {\n    id: 'process-queries',\n    type: 'tool', // Changed from 'node' to 'tool'\n    name: 'Process Search Queries',\n    label: 'Process Search Queries',\n    // This node will iterate through the `search_queries` state variable.\n    // execute will trigger the 'information-gathering' tool for each query.\n    // It needs to manage the list of queries to process and aggregate results.\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing process-queries node.');\n      const queriesToProcess = (state.search_queries as string[]) || [];\n      const allSearchResults = (state.all_search_results as { query: string; result: string }[]) || []; // Aggregate results\n\n      if (queriesToProcess.length === 0) {\n        Logger.instance.info('No search queries left to process. Routing to information-analysis.');\n        // No more queries, signal transition to analysis\n        return { next_node_id: 'information-analysis', all_search_results: allSearchResults };\n      }\n\n      // Take the next query from the list\n      const currentQuery = queriesToProcess[0];\n      const remainingQueries = queriesToProcess.slice(1);\n\n      Logger.instance.debug(`Processing query: \"${currentQuery}\"`);\n\n      let currentResult: string;\n      try {\n        // Search for information (using the wrapped search tool)\n        const searchResults = await wrappedSearchTool.invoke({\n          query: currentQuery,\n          options: { limit: 10 }\n        }); // Use the wrapper's invoke\n        currentResult = typeof searchResults === 'string' ? searchResults : JSON.stringify(searchResults);\n        Logger.instance.debug(`Search result for \"${currentQuery.substring(0, 50)}...\": ${currentResult.substring(0, 100)}...`);\n\n      } catch (error: unknown) {\n        Logger.instance.error(`Error during search tool execution for \"${currentQuery}\":`, error);\n        currentResult = `Error: ${error instanceof Error ? error.message : String(error)}`;\n      }\n\n      // Add the result to the aggregated results\n      allSearchResults.push({ query: currentQuery, result: currentResult });\n\n      // Update state with remaining queries and aggregated results\n      // Signal self-loop back to process-queries node if more queries remain\n      if (remainingQueries.length > 0) {\n        Logger.instance.debug(`Queries remaining (${remainingQueries.length}). Looping back to process-queries.`);\n        return {\n          search_queries: remainingQueries, // Update with remaining queries\n          all_search_results: allSearchResults, // Update with aggregated results\n          user_topic: state.user_topic, // Pass topic along\n          research_plan: state.research_plan, // Pass plan along\n          // next_node_id is implicitly this node ('process-queries') if execute doesn't return a string ID\n          // or we can explicitly set it for clarity if allowed by Codessa polyfill\n          // next_node_id: 'process-queries' // Uncomment if needed and supported\n        };\n      } else {\n        Logger.instance.info('All search queries processed. Routing to information-analysis.');\n        // All queries processed, signal transition to information-analysis\n        return {\n          search_queries: [], // Empty the query list\n          all_search_results: allSearchResults, // Final aggregated results\n          user_topic: state.user_topic, // Pass topic along\n          research_plan: state.research_plan, // Pass plan along\n          next_node_id: 'information-analysis' // Explicitly route\n        };\n      }\n    }\n  };\n\n\n  const informationAnalysisNode: GraphNode = {\n    id: 'information-analysis',\n    type: 'agent',\n    name: 'Analyze Information',\n    label: 'Analyze Information',\n    agent: agent, // Agent performs analysis\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing information-analysis node.');\n      const searchResults = (state.all_search_results as { query: string; result: string }[]) || []; // Get aggregated results\n      const userTopic = (state.user_topic as string) || 'the topic'; // Get original topic\n      const researchPlan = (state.research_plan as string) || ''; // Get research plan\n\n      // Format search results for analysis prompt\n      const formattedResults = searchResults.map((sr: { query: string; result: string }, index: number) =>\n        `--- Search Result ${index + 1} (Query: \"${sr.query.substring(0, 100)}...\") ---\\n${sr.result}\\n---`\n      ).join('\\n\\n');\n\n      const prompt = `\n            You are an AI assistant specializing in synthesizing research findings.\n            Analyze the following search results related to \"${userTopic}\".\n\n            Search Results:\n            ---\n            ${formattedResults}\n            ---\n\n            Research Plan/Goals:\n            ---\n            ${researchPlan}\n            ---\n\n            Based on the search results and the research plan:\n            1. Extract the most important facts, insights, and key takeaways.\n            2. Identify any recurring themes, common viewpoints, or significant findings.\n            3. Note any contradictions, inconsistencies, or differing perspectives found across the sources.\n            4. Identify any obvious gaps in the information where the search results were insufficient or unclear.\n            5. Briefly comment on the apparent relevance or reliability of the sources if possible from the text.\n\n            Provide your analysis in a clear, structured format.\n            `;\n\n      const analysisResult = await agent.generate(prompt); // Assuming generate returns text\n\n      Logger.instance.debug('Information analysis generated.');\n      return {\n        outputs: { 'information-analysis': analysisResult },\n        information_analysis: analysisResult, // Store the analysis in state\n        user_topic: userTopic, // Pass topic along\n        research_plan: researchPlan, // Pass plan along\n      };\n    }\n  };\n\n  const findingsSynthesisNode: GraphNode = {\n    id: 'findings-synthesis',\n    type: 'agent',\n    name: 'Synthesize Findings',\n    label: 'Synthesize Findings',\n    agent: agent, // Agent performs synthesis\n    execute: async (state: Record<string, unknown>): Promise<Partial<GraphState>> => {\n      Logger.instance.debug('Executing findings-synthesis node.');\n      const userTopic = (state.user_topic as string) || 'the research topic'; // Get original topic\n      const informationAnalysis = (state.information_analysis as string) || 'No analysis provided.';\n      const researchPlan = (state.research_plan as string) || ''; // Pass plan along\n\n      const prompt = `\n            You are an AI research reporter. Your task is to synthesize the conducted research and analysis into a comprehensive report.\n\n            Original Research Topic: \"${userTopic}\"\n\n            Research Plan/Goals:\n            ---\n            ${researchPlan}\n            ---\n\n            Information Analysis:\n            ---\n            ${informationAnalysis}\n            ---\n\n            Create a well-structured research report that:\n            1. Provides an executive summary of the key findings.\n            2. Presents the main insights, organized logically (e.g., by sub-question from the plan or by theme).\n            3. Discusses any limitations, conflicting information, or remaining gaps identified during analysis.\n            4. Concludes with implications or suggestions for further research if appropriate.\n\n            Format your report with clear headings and sections.\n            `;\n\n      const synthesisResult = await agent.generate(prompt); // Assuming generate returns text\n\n      Logger.instance.debug('Findings synthesis completed.');\n      return {\n        outputs: { 'findings-synthesis': synthesisResult },\n        final_research_report: synthesisResult, // Store the final report\n      };\n    }\n  };\n\n  const outputNode = Codessa.createOutputNode('output', 'Workflow Output');\n\n\n  // --- Define Edges ---\n  const edges: GraphEdge[] = [\n    // Start from input, go to topic analysis\n    { name: 'input-to-topic-analysis', source: inputNode.id, target: topicAnalysisNode.id, type: 'default' },\n    // Analysis -> Process Queries node (initiates the search loop)\n    { name: 'topic-analysis-to-process-queries', source: topicAnalysisNode.id, target: processQueriesNode.id, type: 'default' },\n\n    // Process Queries -> Information Analysis (conditional exit from loop, handled by execute returning ID)\n    // { name: 'process-queries-to-information-analysis', source: processQueriesNode.id, target: informationAnalysisNode.id, type: 'conditional' } // Not needed if execute returns ID\n\n    // Information Analysis -> Synthesis\n    { name: 'information-analysis-to-synthesis', source: informationAnalysisNode.id, target: findingsSynthesisNode.id, type: 'default' },\n    // Synthesis -> Output\n    { name: 'synthesis-to-output', source: findingsSynthesisNode.id, target: outputNode.id, type: 'default' }\n  ];\n\n  // Note: The self-loop within 'process-queries' is handled by its execute method\n  // returning no explicit next_node_id when more queries remain, or returning\n  // 'information-analysis' when done. The 'information-gathering' tool node is\n  // called *within* the 'process-queries' node's execute method in this design.\n\n  // --- Create Workflow Definition ---\n  const workflowDefinition: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'research', // OperationMode for research workflow\n    nodes: [\n      inputNode,\n      topicAnalysisNode,\n      processQueriesNode, // Include the iterative processing node\n      // informationGatheringNode, // Note: informationGatheringNode is called *from* processQueriesNode's execute, not a separate graph node transition in this design.\n      informationAnalysisNode,\n      findingsSynthesisNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: inputNode.id // The workflow starts at the input node\n  };\n\n  // Register workflow definition\n  workflowRegistry.registerWorkflow(workflowDefinition);\n  Logger.instance.info(`Research workflow '${name}' (${id}) created and registered.`);\n\n  return workflowDefinition;\n}\n"]}