{"version": 3, "file": "memoryTool.js", "sourceRoot": "", "sources": ["../../src/tools/memoryTool.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AA8bH,4CAEC;AA1bD,6BAAwB;AACxB,sCAAmC,CAAC,eAAe;AAEnD,6CAA6C;AAC7C,MAAM,qBAAqB,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,yEAAyE;IACzE,OAAO,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,yDAAyD,CAAC;IACpF,8FAA8F;IAC9F,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;QACjB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,iCAAiC,CAAC;QAC9D,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+BAA+B,CAAC;QAC1D,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;KAC5F,CAAC,CAAC,QAAQ,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,yFAAyF,CAAC,EAAE,yCAAyC;IACnK,6FAA6F;IAC7F,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6DAA6D,CAAC;CAClG,CAAC,CAAC,QAAQ,CAAC,kCAAkC,CAAC,CAAC;AAEhD,MAAM,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,qDAAqD;IACrD,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gDAAgD,CAAC;CACjF,CAAC,CAAC,QAAQ,CAAC,4CAA4C,CAAC,CAAC;AAE1D,MAAM,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,mDAAmD;IACnD,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,8CAA8C,CAAC;CAC/E,CAAC,CAAC,QAAQ,CAAC,0CAA0C,CAAC,CAAC;AAExD,MAAM,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;AACvC,iEAAiE;CAClE,CAAC,CAAC,QAAQ,CAAC,mCAAmC,CAAC,CAAC;AAEjD,iFAAiF;AACjF,MAAM,yBAAyB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,2DAA2D;IAC3D,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,0BAA0B,CAAC;IAC7D,+CAA+C;IAC/C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;IACpG,uDAAuD;IACvD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kDAAkD,CAAC;IAC7F,uDAAuD;IACvD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iEAAiE,CAAC;IAC5H,+DAA+D;CAChE,CAAC,CAAC,QAAQ,CAAC,qCAAqC,CAAC,CAAC;AAEnD,MAAM,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,uHAAuH;IACvH,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6EAA6E,CAAC;IACxI,2GAA2G;IAC3G,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wEAAwE,CAAC;CACnH,CAAC,CAAC,QAAQ,CAAC,oCAAoC,CAAC,CAAC;AAElD,oCAAoC;AACpC,MAAM,4BAA4B,GAAG,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,uCAAuC,CAAC,CAAC;AAEpG,uCAAuC;AACvC,MAAM,+BAA+B,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/C,8EAA8E;IAC9E,iFAAiF;IACjF,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IAC/B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IACnD,kBAAkB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACvD,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IACzD,uBAAuB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IAC/D,WAAW,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uFAAuF,CAAC,EAAE,aAAa;IACxM,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+EAA+E,CAAC,EAAE,aAAa;IACzL,oDAAoD;CACrD,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC,CAAC;AAG5D;;;GAGG;AACH,MAAa,UAAU;IACZ,EAAE,GAAG,mBAAmB,CAAC,CAAC,yBAAyB;IACnD,IAAI,GAAG,gBAAgB,CAAC,CAAC,oCAAoC;IAC7D,WAAW,GAAG,oPAAoP,CAAC;IACnQ,QAAQ,GAAG,QAAQ,CAAC,CAAC,gBAAgB;IACrC,IAAI,GAAG,cAAc,CAAC,CAAC,8BAA8B;IAE9D,0DAA0D;IACjD,OAAO,CAAuC;IAE/C,MAAM,CAAoB,CAAC,kCAAkC;IAErE;;;;SAIK;IACL,YAAY,MAAyB;QACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAEhD,2CAA2C;QAC3C,IAAI,CAAC,OAAO,GAAG;YACb,MAAM,EAAE;gBACN,WAAW,EAAE,2EAA2E;gBACxF,WAAW,EAAE,qBAAqB;aACnC;YACD,KAAK,EAAE;gBACL,WAAW,EAAE,qDAAqD;gBAClE,WAAW,EAAE,oBAAoB;aAClC;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE,0CAA0C;gBACvD,WAAW,EAAE,uBAAuB;aACrC;YACD,MAAM,EAAE;gBACN,WAAW,EAAE,0EAA0E;gBACvF,WAAW,EAAE,uBAAuB;aACrC;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE,sJAAsJ;gBACnK,WAAW,EAAE,yBAAyB;aACvC;YACD,gEAAgE;YAChE,0DAA0D;YAC1D,OAAO,EAAE;gBACP,WAAW,EAAE,gGAAgG;gBAC7G,WAAW,EAAE,wBAAwB;aACtC;YACD,wEAAwE;YACxE,cAAc,EAAE;gBACd,WAAW,EAAE,qEAAqE;gBAClF,WAAW,EAAE,4BAA4B;aAC1C;YACD,iBAAiB,EAAE;gBACjB,WAAW,EAAE,iKAAiK;gBAC9K,WAAW,EAAE,+BAA+B;aAC7C;SACF,CAAC;IACJ,CAAC;IAED;;;;;SAKK;IACL,qBAAqB;QACnB,+FAA+F;QAC/F,0EAA0E;QAC1E,gDAAgD;QAChD,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,EAAE;YAClE,0EAA0E;YAC1E,8DAA8D;YAC9D,MAAM,YAAY,GAAI,SAAS,CAAC,WAAmB,CAAC,MAAM,CAAC,CAAC,CAAE,SAAS,CAAC,WAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAEjH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gFAAgF,UAAU,IAAI,CAAC,CAAC;gBACtH,yDAAyD;gBACzD,OAAO;oBACL,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;oBACnC,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,gBAAgB,UAAU,UAAU;oBAC1E,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,+BAA+B;iBAChF,CAAC;YACJ,CAAC;YAED,kEAAkE;YAClE,IAAI,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC;gBACtC,YAAY,CAAC,UAAU,CAAC,QAAQ,GAAG;oBACjC,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,IAAI,yCAAyC;oBACtG,oBAAoB,EAAE,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,0CAA0C;iBACvI,CAAC;YACJ,CAAC;YACD,IAAI,YAAY,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;gBACpC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG;oBAC/B,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,IAAI,kBAAkB;oBAC7E,oBAAoB,EAAE,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE;iBAC3F,CAAC;YACJ,CAAC;YAGD,OAAO;gBACL,iCAAiC;gBACjC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;gBACnC,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,UAAU,EAAE,YAAY,EAAE,4BAA4B;aACvD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAGD;;;;;;;;;;;SAWK;IACL,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,QAAQ,GAAG,+DAA+D,CAAC;YACjF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1E,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,2CAA2C,UAAU,GAAG,CAAC;YAC1E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1E,CAAC;QAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,UAAU,GAAG,CAAC,CAAC;QACrE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qBAAqB,UAAU,IAAI,EAAE,KAAK,CAAC,CAAC;QAElE,IAAI,CAAC;YACH,6DAA6D;YAC7D,+DAA+D;YAC/D,oEAAoE;YAEpE,IAAI,YAAiB,CAAC,CAAC,gCAAgC;YACvD,IAAI,aAAa,GAAG,IAAI,CAAC,CAAC,kBAAkB;YAE5C,4CAA4C;YAC5C,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,MAAM;oBACT,mDAAmD;oBACnD,MAAM,SAAS,GAAG,KAA8C,CAAC;oBACjE,8EAA8E;oBAC9E,sDAAsD;oBACtD,8CAA8C;oBAC9C,MAAM,gBAAgB,GAAG;wBACvB,GAAG,SAAS,CAAC,QAAQ;wBACrB,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;wBACrE,6DAA6D;wBAC7D,MAAM,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM,IAAI,MAAsB;wBAC3D,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,IAAI,IAAI,MAAoB;qBACtD,CAAC;oBACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;wBACzC,OAAO,EAAE,SAAS,CAAC,OAAO;wBAC1B,QAAQ,EAAE;4BACR,MAAM,EAAE,gBAAgB,CAAC,MAAsB;4BAC/C,IAAI,EAAE,gBAAgB,CAAC,IAAkB;4BACzC,IAAI,EAAE,gBAAgB,CAAC,IAAI;yBAC5B;qBACF,CAAC,CAAC;oBACH,YAAY,GAAG,MAAM,CAAC,CAAC,iCAAiC;oBACxD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;oBACzE,MAAM;gBAER,KAAK,KAAK;oBACR,MAAM,QAAQ,GAAG,KAA6C,CAAC;oBAC/D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACpD,YAAY,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,2BAA2B;oBACzD,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,kCAAkC;oBAC3D,IAAI,aAAa;wBAAE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;;wBACrF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC5E,MAAM;gBAER,KAAK,QAAQ;oBACX,MAAM,WAAW,GAAG,KAAgD,CAAC;oBACrE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACpD,YAAY,GAAG,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,4BAA4B;oBAChF,aAAa,GAAG,EAAE,CAAC,CAAC,oCAAoC;oBACxD,IAAI,aAAa;wBAAE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;;wBACtF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gDAAgD,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC5F,MAAM;gBAER,KAAK,MAAM;oBACT,wDAAwD;oBACxD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,yBAAyB;oBAC1E,YAAY,GAAG,OAAO,CAAC,CAAC,0BAA0B;oBAClD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,OAAO,CAAC,MAAM,kBAAkB,CAAC,CAAC;oBACjE,MAAM;gBAER,KAAK,QAAQ;oBACX,MAAM,WAAW,GAAG,KAAkD,CAAC;oBACvE,0CAA0C;oBAC1C,MAAM,aAAa,GAAwB;wBACzC,KAAK,EAAE,WAAW,CAAC,KAAK;wBACxB,KAAK,EAAE,WAAW,CAAC,KAAK;wBACxB,MAAM,EAAE,WAAW,CAAC,MAAM;wBAC1B,4BAA4B;wBAC5B,gEAAgE;qBACjE,CAAC;oBACF,4DAA4D;oBAC5D,0EAA0E;oBAC1E,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,8BAA8B;oBACrG,YAAY,GAAG,aAAa,CAAC,CAAC,iDAAiD;oBAC/E,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,aAAa,CAAC,MAAM,mCAAmC,CAAC,CAAC;oBACvF,MAAM;gBAER,0BAA0B;gBAC1B,uBAAuB;gBAEvB,KAAK,OAAO;oBACV,MAAM,UAAU,GAAG,KAAiD,CAAC;oBACrE,sCAAsC;oBACtC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;wBAC9C,MAAM,YAAY,GAAG,yDAAyD,CAAC;wBAC/E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;wBACpC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;oBAC9E,CAAC;oBAED,IAAI,CAAC;wBACH,mFAAmF;wBACnF,wDAAwD;wBACxD,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;4BACtB,yBAAyB;4BACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;gCAChD,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,EAAE;gCACxB,MAAM,EAAE,KAAK,CAAC,MAAM;gCACpB,MAAM,EAAE,KAAK,CAAC,MAAM;gCACpB,SAAS,EAAE,KAAK,CAAC,SAAS;6BAC3B,CAAC,CAAC;4BAEH,oCAAoC;4BACpC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gCAChD,gDAAgD;gCAChD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,MAAO,CAAC,EAAE,CAAC;oCAC9D,6BAA6B;oCAC7B,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;wCAChC,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wCACrC,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE,CAAC;4CAC3C,OAAO,KAAK,CAAC;wCACf,CAAC;oCACH,CAAC;oCACD,kCAAkC;yCAC7B,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;wCAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;4CACrC,OAAO,KAAK,CAAC;wCACf,CAAC;oCACH,CAAC;yCACI,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;wCACxB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;4CACnC,OAAO,KAAK,CAAC;wCACf,CAAC;oCACH,CAAC;oCACD,+BAA+B;yCAC1B,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;wCACxB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;wCAC1D,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;4CAC3F,OAAO,KAAK,CAAC;wCACf,CAAC;oCACH,CAAC;oCACD,2BAA2B;yCACtB,IAAK,MAAc,CAAC,GAAG,CAAC,KAAK,KAAK,EAAE,CAAC;wCACxC,OAAO,KAAK,CAAC;oCACf,CAAC;gCACH,CAAC;gCACD,OAAO,IAAI,CAAC;4BACd,CAAC,CAAC,CAAC;4BAEH,8BAA8B;4BAC9B,IAAI,WAAW,GAAG,CAAC,CAAC;4BACpB,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;gCACtC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gCACzD,IAAI,OAAO;oCAAE,WAAW,EAAE,CAAC;4BAC7B,CAAC;4BAED,YAAY,GAAG;gCACb,KAAK,EAAE,WAAW;gCAClB,aAAa,EAAE,gBAAgB,CAAC,MAAM;gCACtC,MAAM,EAAE,UAAU,CAAC,MAAM;6BAC1B,CAAC;4BACF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,WAAW,kCAAkC,CAAC,CAAC;wBACjF,CAAC;6BAAM,CAAC;4BACN,qBAAqB;4BACrB,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC;4BAC7D,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;4BAClC,YAAY,GAAG;gCACb,KAAK,EAAE,WAAW;gCAClB,MAAM,EAAE,KAAK;6BACd,CAAC;4BACF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,WAAW,IAAI,CAAC,CAAC;wBACrF,CAAC;wBAED,aAAa,GAAG,IAAI,CAAC,CAAC,0CAA0C;oBAClE,CAAC;oBAAC,OAAO,KAAU,EAAE,CAAC;wBACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;wBACtF,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE;4BACnD,MAAM,EAAE,IAAI,CAAC,EAAE;4BACf,UAAU;yBACX,CAAC;oBACJ,CAAC;oBACD,MAAM;gBAER,6BAA6B;gBAE7B,qCAAqC;gBACrC,KAAK,cAAc;oBACjB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;oBACjE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;oBACvD,YAAY,GAAG,QAAQ,CAAC;oBACxB,aAAa,GAAG,IAAI,CAAC;oBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;oBACnD,MAAM;gBAER,KAAK,iBAAiB;oBACpB,MAAM,mBAAmB,GAAG,KAAwD,CAAC;oBACrF,0DAA0D;oBAC1D,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,wDAAwD;oBAChH,YAAY,GAAG,EAAE,MAAM,EAAE,kBAAkB,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBACzF,aAAa,GAAG,IAAI,CAAC,CAAC,wCAAwC;oBAC9D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;oBACjD,MAAM;gBAER;oBACE,yEAAyE;oBACzE,0BAA0B;oBAC1B,MAAM,kBAAkB,GAAG,mBAAmB,UAAU,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvH,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;oBAC1C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YACtF,CAAC;YAED,sDAAsD;YACtD,OAAO;gBACL,OAAO,EAAE,aAAa;gBACtB,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,UAAU;gBACtB,kCAAkC;aACnC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,qDAAqD;YACrD,MAAM,YAAY,GAAG,mCAAmC,UAAU,MAAM,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;YACjG,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC9E,CAAC;IACH,CAAC;CACF;AA5WD,gCA4WC;AAED,oEAAoE;AACpE,SAAgB,gBAAgB,CAAC,MAAyB;IACxD,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED,8BAA8B;AAC9B,mFAAmF;AACnF,sDAAsD;AACtD,mHAAmH;AACnH,2BAA2B", "sourcesContent": ["/**\n * AI Agent Tool for comprehensive memory management.\n * Provides actions for saving, retrieving, updating, deleting, searching,\n * tagging, and visualizing memory entries via a MemoryManager.\n * Implemented as a multi-action tool.\n */\n\nimport { ITool, ToolInput, ToolResult, ToolActionDefinition } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { MemorySearchOptions, MemorySource, MemoryType } from '../memory/types'; // Import types from types.ts\nimport { IMemoryOperations } from '../memory/types'; // Import MemoryManager from memoryManager.ts\nimport { z } from 'zod';\nimport { Logger } from '../logger'; // Correct path\n\n// Define Zod schemas for each action's input\nconst SaveMemoryInputSchema = z.object({\n  /** The content of the memory entry. Can be string or structured data. */\n  content: z.any().describe('The content of the memory entry (string, object, etc.).'),\n  /** Metadata for the memory entry, requiring source and type and allowing other properties. */\n  metadata: z.object({\n    source: z.string().describe('The source of the memory entry.'),\n    type: z.string().describe('The type of the memory entry.'),\n    tags: z.string().array().optional().describe('Optional list of tags for the memory entry.'),\n  }).catchall(z.any()).describe('Metadata for the memory entry, requiring source and type and allowing other properties.'), // Use catchall for additional properties\n  /** Optional specific ID to assign to the memory entry. If not provided, one is generated. */\n  id: z.string().optional().describe('Optional specific ID to assign. Defaults to generated UUID.'),\n}).describe('Input for saving a memory entry.');\n\nconst GetMemoryInputSchema = z.object({\n  /** The unique ID of the memory entry to retrieve. */\n  id: z.string().min(1).describe('The unique ID of the memory entry to retrieve.'),\n}).describe('Input for retrieving a memory entry by ID.');\n\nconst DeleteMemoryInputSchema = z.object({\n  /** The unique ID of the memory entry to delete. */\n  id: z.string().min(1).describe('The unique ID of the memory entry to delete.'),\n}).describe('Input for deleting a memory entry by ID.');\n\nconst ListMemoriesInputSchema = z.object({\n  // No input parameters needed as getMemories expects no arguments\n}).describe('Input for listing memory entries.');\n\n// Assuming MemorySearchOptions is defined elsewhere and has filter, limit, query\nconst SearchMemoriesInputSchema = z.object({\n  /** The search query string for semantic or text search. */\n  query: z.string().min(1).describe('The search query string.'),\n  /** Optional limit on the number of results. */\n  limit: z.number().int().positive().optional().describe('Optional maximum number of search results.'),\n  /** Optional relevance threshold for search results. */\n  threshold: z.number().optional().describe('Optional relevance threshold for search results.'),\n  /** Optional filter criteria for searching memories. */\n  filter: z.record(z.string(), z.any()).optional().describe('Optional filter criteria for search (e.g., { source: \"user\" }).'),\n  // Removed semantic property as it's not in MemorySearchOptions\n}).describe('Input for searching memory entries.');\n\nconst ClearMemoriesInputSchema = z.object({\n  /** Optional filter to clear only memories matching the criteria. If omitted, clears all memories. Use with caution. */\n  filter: z.record(z.string(), z.any()).optional().describe('Optional filter to clear only memories matching criteria. Use with caution.'),\n  /** Confirmation flag to prevent accidental clearing of all memories. Required if no filter is provided. */\n  confirm: z.boolean().optional().describe('Required confirmation to clear all memories if no filter is specified.'),\n}).describe('Input for clearing memory entries.');\n\n// Action to get tool's own settings\nconst GetMemorySettingsInputSchema = z.object({}).describe('Input to get current memory settings.');\n\n// Action to update tool's own settings\nconst UpdateMemorySettingsInputSchema = z.object({\n  // Define the structure for settings updates based on your MemorySettings type\n  // Example: enabled, maxMemories, relevanceThreshold, vectorStore, database, etc.\n  enabled: z.boolean().optional(),\n  maxMemories: z.number().int().positive().optional(),\n  relevanceThreshold: z.number().min(0).max(1).optional(),\n  contextWindowSize: z.number().int().positive().optional(),\n  conversationHistorySize: z.number().int().positive().optional(),\n  vectorStore: z.enum(['memory', 'chroma', 'pinecone', 'weaviate', 'hnswlib']).optional().describe('Optional vector store to use (\"memory\", \"chroma\", \"pinecone\", \"weaviate\", \"hnswlib\").'), // Use z.enum\n  database: z.enum(['sqlite', 'mysql', 'postgres', 'mongodb', 'redis']).optional().describe('Optional database to use (\"sqlite\", \"mysql\", \"postgres\", \"mongodb\", \"redis\").'), // Use z.enum\n  // Add other settings properties that can be updated\n}).partial().describe('Partial memory settings to update.');\n\n\n/**\n * Implements the ITool interface for comprehensive memory management actions.\n * This is a multi-action tool coordinating operations via a MemoryManager.\n */\nexport class MemoryTool implements ITool {\n  readonly id = 'memory_management'; // Unique ID for the tool\n  readonly name = 'memory_manager'; // Machine-friendly name for the LLM\n  readonly description = 'Provides comprehensive capabilities to save, retrieve, update, delete, search, tag, list, clear, and visualize memory entries used by the AI. Memory entries store diverse information like code snippets, documents, conversation summaries, etc.';\n  readonly category = 'Memory'; // Tool category\n  readonly type = 'multi-action'; // This is a multi-action tool\n\n  // Define the available actions using ToolActionDefinition\n  readonly actions: Record<string, ToolActionDefinition>;\n\n  private memory: IMemoryOperations; // Dependency on memory operations\n\n  /**\n     * Constructs a new MemoryTool instance.\n     * @param memory - The memory operations instance to use.\n     * @throws {Error} If the memory operations instance is not provided.\n     */\n  constructor(memory: IMemoryOperations) {\n    if (!memory) {\n      throw new Error('MemoryTool requires a memory operations instance.');\n    }\n    this.memory = memory;\n    Logger.instance.info('MemoryTool initialized.');\n\n    // Define the actions using the Zod schemas\n    this.actions = {\n      'save': {\n        description: 'Saves a new memory entry or updates an existing one if an ID is provided.',\n        inputSchema: SaveMemoryInputSchema,\n      },\n      'get': {\n        description: 'Retrieves a specific memory entry by its unique ID.',\n        inputSchema: GetMemoryInputSchema,\n      },\n      'delete': {\n        description: 'Deletes a memory entry by its unique ID.',\n        inputSchema: DeleteMemoryInputSchema,\n      },\n      'list': {\n        description: 'Lists existing memory entries, optionally filtered, limited, and sorted.',\n        inputSchema: ListMemoriesInputSchema,\n      },\n      'search': {\n        description: 'Searches for memory entries semantically or by keyword query, optionally with metadata filters. Returns relevant entries ordered by relevance/match.',\n        inputSchema: SearchMemoriesInputSchema,\n      },\n      // Removed 'update' action as updateMemory method does not exist\n      // Removed 'tag' action as tagMemory method does not exist\n      'clear': {\n        description: 'Clears all memory entries or only those matching an optional filter. USE WITH EXTREME CAUTION.',\n        inputSchema: ClearMemoriesInputSchema,\n      },\n      // Removed 'visualize' action as visualizeMemories method does not exist\n      'get_settings': {\n        description: 'Retrieves the current configuration settings for the memory system.',\n        inputSchema: GetMemorySettingsInputSchema,\n      },\n      'update_settings': {\n        description: 'Updates the configuration settings for the memory system. Requires specific input parameters matching setting keys. Some updates may require re-initialization.',\n        inputSchema: UpdateMemorySettingsInputSchema,\n      },\n    };\n  }\n\n  /**\n     * Provides the structured definition for the LLM.\n     * Generates JSON schema definitions for each action.\n     * Conforms to ITool.getDefinitionForModel.\n     * @returns An array of JSON schema definitions suitable for LLM tool calling (e.g., OpenAI function definitions).\n     */\n  getDefinitionForModel(): any {\n    // For a multi-action tool, the LLM typically sees each action as a distinct callable function.\n    // The names should be unique across all tools/actions exposed to the LLM.\n    // A common pattern is \"tool_name__action_name\".\n    return Object.entries(this.actions).map(([actionName, actionDef]) => {\n      // Generate JSON schema for the action's input schema using Zod's toJSON()\n      // Use casting for robustness against potential Zod variations\n      const schemaObject = (actionDef.inputSchema as any).toJSON ? (actionDef.inputSchema as any).toJSON() : undefined;\n\n      if (!schemaObject) {\n        Logger.instance.error(`MemoryTool getDefinitionForModel: Failed to generate JSON schema for action '${actionName}'.`);\n        // Return a minimal definition if schema generation fails\n        return {\n          name: `${this.name}__${actionName}`,\n          description: actionDef.description || `Performs the ${actionName} action.`,\n          parameters: { type: 'object', properties: {} }, // Empty params if schema fails\n        };\n      }\n\n      // Potential schema adjustments for complex types (e.g., metadata)\n      if (schemaObject.properties?.metadata) {\n        schemaObject.properties.metadata = {\n          type: 'object',\n          description: schemaObject.properties.metadata.description || 'Optional metadata for the memory entry.',\n          additionalProperties: { type: ['string', 'number', 'boolean', 'array', 'object', 'null'] }, // Allow various types for metadata values\n        };\n      }\n      if (schemaObject.properties?.filter) {\n        schemaObject.properties.filter = {\n          type: 'object',\n          description: schemaObject.properties.filter.description || 'Filter criteria.',\n          additionalProperties: { type: ['string', 'number', 'boolean', 'array', 'object', 'null'] },\n        };\n      }\n\n\n      return {\n        // Action name format for the LLM\n        name: `${this.name}__${actionName}`,\n        description: actionDef.description,\n        parameters: schemaObject, // The generated JSON schema\n      };\n    });\n  }\n\n\n  /**\n     * Executes the specified memory action.\n     * The calling framework is responsible for parsing the LLM's tool call,\n     * extracting the action name and parameters, and validating parameters\n     * against the specific action's schema before calling this method.\n     *\n     * @param actionName - The name of the action to execute (e.g., 'save', 'get').\n     * @param input - The validated input parameters for the action.\n     * @param context - Optional agent context (provides access to memoryManager if not dependency injected).\n     * @returns A promise resolving to the tool's result.\n     * @throws {Error} If the action execution fails.\n     */\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    if (!actionName) {\n      const errorMsg = 'MemoryTool requires an actionName for multi-action execution.';\n      Logger.instance.error(errorMsg, { input });\n      return { success: false, error: errorMsg, toolId: this.id, actionName };\n    }\n\n    const actionDefinition = this.actions[actionName];\n    if (!actionDefinition) {\n      const errorMsg = `MemoryTool received unknown actionName: ${actionName}.`;\n      Logger.instance.error(errorMsg, { input });\n      return { success: false, error: errorMsg, toolId: this.id, actionName };\n    }\n\n    Logger.instance.info(`Executing MemoryTool action: \"${actionName}\"`);\n    Logger.instance.debug(`Input for action \"${actionName}\":`, input);\n\n    try {\n      // Input is assumed to be validated by the framework already,\n      // but a final parse could be added here if paranoia is needed.\n      // const validatedInput = actionDefinition.inputSchema.parse(input);\n\n      let resultOutput: any; // Output of the specific action\n      let successStatus = true; // Default success\n\n      // --- Execute the specific action logic ---\n      switch (actionName) {\n        case 'save':\n          // Use SaveMemoryInputSchema type for input clarity\n          const saveInput = input as z.infer<typeof SaveMemoryInputSchema>;\n          // memoryManager.addMemory should ideally take { content, metadata, id, tags }\n          // Merge tags from input into metadata.tags if present\n          // Ensure required metadata fields are present\n          const metadataWithTags = {\n            ...saveInput.metadata,\n            ...(saveInput.metadata.tags ? { tags: saveInput.metadata.tags } : {}),\n            // Ensure required fields have default values if not provided\n            source: saveInput.metadata.source || 'user' as MemorySource,\n            type: saveInput.metadata.type || 'text' as MemoryType\n          };\n          const memory = await this.memory.addMemory({\n            content: saveInput.content,\n            metadata: {\n              source: metadataWithTags.source as MemorySource,\n              type: metadataWithTags.type as MemoryType,\n              tags: metadataWithTags.tags\n            },\n          });\n          resultOutput = memory; // Return the saved entry details\n          Logger.instance.info(`Memory entry saved/updated with ID: ${memory.id}`);\n          break;\n\n        case 'get':\n          const getInput = input as z.infer<typeof GetMemoryInputSchema>;\n          const entry = await this.memory.getMemory(input.id);\n          resultOutput = entry || null; // Return the entry or null\n          successStatus = !!entry; // Success only if entry was found\n          if (successStatus) Logger.instance.info(`Memory entry retrieved with ID: ${getInput.id}`);\n          else Logger.instance.warn(`Memory entry not found with ID: ${getInput.id}`);\n          break;\n\n        case 'delete':\n          const deleteInput = input as z.infer<typeof DeleteMemoryInputSchema>;\n          const ok = await this.memory.deleteMemory(input.id);\n          resultOutput = { id: deleteInput.id, deleted: ok }; // Return status of deletion\n          successStatus = ok; // Success only if deletion occurred\n          if (successStatus) Logger.instance.info(`Memory entry deleted with ID: ${deleteInput.id}`);\n          else Logger.instance.warn(`Memory entry not found for deletion with ID: ${deleteInput.id}`);\n          break;\n\n        case 'list':\n          // Removed listOptions as getMemories takes no arguments\n          const entries = await this.memory.getMemories(); // Call without arguments\n          resultOutput = entries; // Return array of entries\n          Logger.instance.info(`Listed ${entries.length} memory entries.`);\n          break;\n\n        case 'search':\n          const searchInput = input as z.infer<typeof SearchMemoriesInputSchema>;\n          // Map schema input to MemorySearchOptions\n          const searchOptions: MemorySearchOptions = {\n            query: searchInput.query,\n            limit: searchInput.limit,\n            filter: searchInput.filter,\n            // Removed semantic property\n            // Add other search options if MemorySearchOptions includes them\n          };\n          // MemoryManager should expose both search and searchSimilar\n          // Assuming a unified search method that handles both based on query/flags\n          const searchResults = await this.memory.searchMemories(searchOptions); // Assuming this method exists\n          resultOutput = searchResults; // Return array of search results (MemoryEntry[])\n          Logger.instance.info(`Found ${searchResults.length} memory entries for search query.`);\n          break;\n\n        // Removed 'update' action\n        // Removed 'tag' action\n\n        case 'clear':\n          const clearInput = input as z.infer<typeof ClearMemoriesInputSchema>;\n          // Check confirmation for clearing all\n          if (!clearInput.filter && !clearInput.confirm) {\n            const confirmError = 'Clearing all memories requires \\'confirm: true\\' input.';\n            Logger.instance.error(confirmError);\n            return { success: false, error: confirmError, toolId: this.id, actionName };\n          }\n\n          try {\n            // MemoryManager's clearMemories doesn't return a count and doesn't accept a filter\n            // We'll need to implement filtering ourselves if needed\n            if (clearInput.filter) {\n              // Get all memories first\n              const memories = await this.memory.searchMemories({\n                query: input.query,\n                limit: input.limit || 10,\n                filter: input.filter,\n                sortBy: input.sortBy,\n                sortOrder: input.sortOrder\n              });\n\n              // Filter memories based on criteria\n              const memoriesToDelete = memories.filter(memory => {\n                // Check each filter property against the memory\n                for (const [key, value] of Object.entries(clearInput.filter!)) {\n                  // Handle metadata properties\n                  if (key.startsWith('metadata.')) {\n                    const metadataKey = key.substring(9);\n                    if (memory.metadata[metadataKey] !== value) {\n                      return false;\n                    }\n                  }\n                  // Handle source and type directly\n                  else if (key === 'source') {\n                    if (memory.metadata.source !== value) {\n                      return false;\n                    }\n                  }\n                  else if (key === 'type') {\n                    if (memory.metadata.type !== value) {\n                      return false;\n                    }\n                  }\n                  // Handle tags (array contains)\n                  else if (key === 'tags') {\n                    const filterTags = Array.isArray(value) ? value : [value];\n                    if (!memory.metadata.tags || !filterTags.every(tag => memory.metadata.tags!.includes(tag))) {\n                      return false;\n                    }\n                  }\n                  // Handle direct properties\n                  else if ((memory as any)[key] !== value) {\n                    return false;\n                  }\n                }\n                return true;\n              });\n\n              // Delete each filtered memory\n              let deleteCount = 0;\n              for (const memory of memoriesToDelete) {\n                const success = await this.memory.deleteMemory(input.id);\n                if (success) deleteCount++;\n              }\n\n              resultOutput = {\n                count: deleteCount,\n                totalFiltered: memoriesToDelete.length,\n                filter: clearInput.filter\n              };\n              Logger.instance.info(`Cleared ${deleteCount} memory entries matching filter.`);\n            } else {\n              // Clear all memories\n              const beforeCount = (await this.memory.getMemories()).length;\n              await this.memory.clearMemories();\n              resultOutput = {\n                count: beforeCount,\n                filter: 'all'\n              };\n              Logger.instance.info(`Cleared all memory entries (approximately ${beforeCount}).`);\n            }\n\n            successStatus = true; // Clearing operation itself is successful\n          } catch (error: any) {\n            Logger.instance.error(`Error during memory clear operation: ${error.message}`, error);\n            return {\n              success: false,\n              error: `Failed to clear memories: ${error.message}`,\n              toolId: this.id,\n              actionName\n            };\n          }\n          break;\n\n        // Removed 'visualize' action\n\n        // Action for getting memory settings\n        case 'get_settings':\n          Logger.instance.info('Executing MemoryTool get_settings action');\n          const settings = await this.memory.getMemorySettings();\n          resultOutput = settings;\n          successStatus = true;\n          Logger.instance.info('Retrieved memory settings.');\n          break;\n\n        case 'update_settings':\n          const updateSettingsInput = input as z.infer<typeof UpdateMemorySettingsInputSchema>;\n          // MemoryManager should expose a method to update settings\n          await this.memory.updateMemorySettings(input.settings); // Assuming updateMemorySettings handles partial updates\n          resultOutput = { status: 'settings updated', updated: Object.keys(updateSettingsInput) };\n          successStatus = true; // Update operation itself is successful\n          Logger.instance.info('Updated memory settings.');\n          break;\n\n        default:\n          // This case should ideally not be reached if parsing/routing is correct,\n          // but handle defensively.\n          const unknownActionError = `Unknown action: ${actionName}. Available actions: ${Object.keys(this.actions).join(', ')}`;\n          Logger.instance.error(unknownActionError);\n          return { success: false, error: unknownActionError, toolId: this.id, actionName };\n      }\n\n      // Return the result in the standard ToolResult format\n      return {\n        success: successStatus,\n        output: resultOutput,\n        toolId: this.id,\n        actionName: actionName,\n        // Add usage/metadata if available\n      };\n\n    } catch (error: any) {\n      // Catch any unhandled errors during action execution\n      const errorMessage = `Error during MemoryTool action \"${actionName}\": ${error.message || error}`;\n      Logger.instance.error(errorMessage, error);\n      return { success: false, error: errorMessage, toolId: this.id, actionName };\n    }\n  }\n}\n\n// Factory function to create the MemoryTool, injecting dependencies\nexport function createMemoryTool(memory: IMemoryOperations): MemoryTool {\n  return new MemoryTool(memory);\n}\n\n// Example usage (conceptual):\n// const memoryManager = getMemoryManagerInstance(); // Assume this function exists\n// const memoryTool = createMemoryTool(memoryManager);\n// const toolResult = await memoryTool.execute('save', { content: 'User likes blue.', metadata: { user: 'abc' } });\n// console.log(toolResult);"]}