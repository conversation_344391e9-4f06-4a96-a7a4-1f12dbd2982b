"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryVectorStore = void 0;
const corePolyfill_1 = require("../../../agents/workflows/corePolyfill");
const logger_1 = require("../../../logger");
/**
 * In-memory vector store implementation
 */
class MemoryVectorStore {
    documents = [];
    embeddings;
    constructor(embeddings) {
        this.embeddings = embeddings;
    }
    /**
       * Initialize the vector store
       */
    async initialize() {
        try {
            // Test embeddings to ensure they're working
            await this.embeddings.embedQuery('test');
            logger_1.logger.info('Memory vector store initialized successfully with embeddings');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize memory vector store:', error);
            throw error;
        }
    }
    /**
       * Add documents
       */
    addDocuments(documents) {
        try {
            this.documents.push(...documents);
            logger_1.logger.debug(`Added ${documents.length} documents to memory vector store`);
        }
        catch (error) {
            logger_1.logger.error('Failed to add documents:', error);
            throw error;
        }
    }
    /**
       * Add a vector
       */
    async addVector(id, vector, metadata = {}) {
        try {
            // Simulate async operation
            await Promise.resolve();
            // Create document with vector information
            const document = new corePolyfill_1.Document({
                pageContent: metadata.content || '',
                metadata: {
                    ...metadata,
                    id,
                    vectorDimensions: vector.length,
                    vectorNorm: Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0))
                }
            });
            // Add document
            this.addDocuments([document]); // Document[] expected
            logger_1.logger.debug(`Added vector with ID ${id} (${vector.length} dimensions) to memory vector store`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to add vector with ID ${id}:`, error);
            throw error;
        }
    }
    /**
       * Get a vector by ID
       */
    async getVector(id) {
        try {
            // Simulate async operation
            await Promise.resolve();
            logger_1.logger.warn(`Getting vectors by ID (${id}) is not supported by MemoryVectorStore`);
            return undefined;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get vector with ID ${id}:`, error);
            return undefined;
        }
    }
    /**
       * Delete a vector by ID
       */
    async deleteVector(id) {
        try {
            // Simulate async operation and attempt to find and remove document
            await Promise.resolve();
            const initialLength = this.documents.length;
            this.documents = this.documents.filter(doc => doc.metadata.id !== id);
            const removed = this.documents.length < initialLength;
            logger_1.logger.warn(`Delete vector by ID (${id}): ${removed ? 'found and removed' : 'not found'}`);
            return removed;
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete vector with ID ${id}:`, error);
            return false;
        }
    }
    /**
       * Clear all vectors
       */
    async clearVectors() {
        try {
            // Simulate async operation
            await Promise.resolve();
            const documentCount = this.documents.length;
            this.documents = [];
            logger_1.logger.info(`Memory vector store cleared successfully (removed ${documentCount} documents)`);
        }
        catch (error) {
            logger_1.logger.error('Failed to clear memory vector store:', error);
            throw error;
        }
    }
    /**
       * Search for similar vectors
       */
    async searchSimilarVectors(queryVector, maxResults = 5, searchFilter) {
        try {
            // Simulate async operation
            await Promise.resolve();
            // Basic implementation: return empty results with logging of parameters
            logger_1.logger.warn(`Vector search not fully implemented. Query vector length: ${queryVector.length}, max results: ${maxResults}, filter: ${searchFilter ? 'provided' : 'none'}`);
            return [];
        }
        catch (error) {
            logger_1.logger.error('Failed to search similar vectors:', error);
            return [];
        }
    }
    /**
       * Search for similar documents
       */
    async similaritySearch(query, k = 4) {
        try {
            // Use embeddings to generate query vector
            await this.embeddings.embedQuery(query);
            // Simple implementation that returns all documents
            // In a real implementation, this would use embeddings and vector similarity
            logger_1.logger.debug(`Performing similarity search for query: "${query}" with k=${k}`);
            return this.documents.slice(0, k).map(doc => ({
                pageContent: doc.pageContent,
                metadata: doc.metadata
            }));
        }
        catch (error) {
            logger_1.logger.error('Failed to perform similarity search:', error);
            return [];
        }
    }
}
exports.MemoryVectorStore = MemoryVectorStore;
//# sourceMappingURL=memoryVectorStore.js.map