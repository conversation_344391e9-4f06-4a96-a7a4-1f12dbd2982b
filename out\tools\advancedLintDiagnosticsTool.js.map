{"version": 3, "file": "advancedLintDiagnosticsTool.js", "sourceRoot": "", "sources": ["../../src/tools/advancedLintDiagnosticsTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,6BAAwB;AAExB,MAAa,aAAa;IACf,EAAE,GAAG,WAAW,CAAC;IACjB,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,qDAAqD,CAAC;IACpE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;QACpF,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,gDAAgD,CAAC;KACzF,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,WAAW,EAAE,oCAAoC,EAAE;YACtG,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gDAAgD,EAAE;SAC1F;QACD,QAAQ,EAAE,EAAE;KACb,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,iBAAiB;YACjB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACzB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,kBAAkB,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE;oBACpD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,MAAM,KAAK,GAAG,KAAK,CAAC,KAA6B,CAAC;YAClD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,IAAI,QAAQ,CAAC;YAElD,oDAAoD;YACpD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0FAA0F;oBACjG,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,kCAAkC;YAClC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzF,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,+DAA+D;oBACtE,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,8BAA8B;YAC9B,MAAM,OAAO,GAA4D,EAAE,CAAC;YAE5E,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,sBAAsB;gBACtB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,CAAC;wBACH,qBAAqB;wBACrB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAI,MAAM,GAAG,KAAK,CAAC;wBACnB,IAAI,CAAC;4BACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BACpC,MAAM,GAAG,IAAI,CAAC;wBAChB,CAAC;wBAAC,MAAM,CAAC;4BACP,MAAM,GAAG,KAAK,CAAC;wBACjB,CAAC;wBAED,IAAI,CAAC,MAAM,EAAE,CAAC;4BACZ,OAAO,CAAC,IAAI,CAAC;gCACX,IAAI;gCACJ,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,qBAAqB;6BAC7B,CAAC,CAAC;4BACH,SAAS;wBACX,CAAC;wBAED,oBAAoB;wBACpB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,EAAE,GAAG,MAAM,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;wBAEnG,OAAO,CAAC,IAAI,CAAC;4BACX,IAAI;4BACJ,OAAO,EAAE,IAAI;yBACd,CAAC,CAAC;oBACL,CAAC;oBAAC,OAAO,GAAQ,EAAE,CAAC;wBAClB,OAAO,CAAC,IAAI,CAAC;4BACX,IAAI;4BACJ,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,GAAG,CAAC,OAAO,IAAI,eAAe;yBACtC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;gBAE3D,OAAO;oBACL,OAAO,EAAE,YAAY,GAAG,CAAC;oBACzB,MAAM,EAAE;wBACN,OAAO,EAAE,UAAU,YAAY,IAAI,KAAK,CAAC,MAAM,eAAe,MAAM,GAAG;wBACvE,OAAO;qBACR;oBACD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;oBACV,QAAQ,EAAE;wBACR,MAAM;wBACN,SAAS,EAAE,KAAK,CAAC,MAAM;wBACvB,YAAY;wBACZ,YAAY,EAAE,KAAK,CAAC,MAAM,GAAG,YAAY;qBAC1C;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,wBAAwB;gBACxB,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,EAAE,GAAG,MAAM,OAAO,CAAC,CAAC;oBAEzF,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,gCAAgC,MAAM,GAAG;wBACjD,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;wBACV,QAAQ,EAAE;4BACR,MAAM;4BACN,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM;yBAC3D;qBACF,CAAC;gBACJ,CAAC;gBAAC,OAAO,GAAQ,EAAE,CAAC;oBAClB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,6BAA6B,GAAG,CAAC,OAAO,IAAI,eAAe,EAAE;wBACpE,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,sBAAsB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACrD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAlJD,sCAkJC;AAED,MAAa,cAAc;IAChB,EAAE,GAAG,YAAY,CAAC;IAClB,IAAI,GAAG,cAAc,CAAC;IACtB,WAAW,GAAG,wEAAwE,CAAC;IACvF,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kCAAkC,CAAC;QAClF,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,gDAAgD,CAAC;KACzF,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,WAAW,EAAE,kCAAkC,EAAE;YACpG,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gDAAgD,EAAE;SAC1F;QACD,QAAQ,EAAE,EAAE;KACb,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,iBAAiB;YACjB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACzB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,kBAAkB,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE;oBACpD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,MAAM,KAAK,GAAG,KAAK,CAAC,KAA6B,CAAC;YAClD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,IAAI,QAAQ,CAAC;YAElD,oDAAoD;YACpD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0FAA0F;oBACjG,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,kCAAkC;YAClC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzF,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mEAAmE;oBAC1E,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,8BAA8B;YAC9B,MAAM,OAAO,GAA4D,EAAE,CAAC;YAE5E,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,0BAA0B;gBAC1B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,CAAC;wBACH,qBAAqB;wBACrB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAI,MAAM,GAAG,KAAK,CAAC;wBACnB,IAAI,CAAC;4BACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BACpC,MAAM,GAAG,IAAI,CAAC;wBAChB,CAAC;wBAAC,MAAM,CAAC;4BACP,MAAM,GAAG,KAAK,CAAC;wBACjB,CAAC;wBAED,IAAI,CAAC,MAAM,EAAE,CAAC;4BACZ,OAAO,CAAC,IAAI,CAAC;gCACX,IAAI;gCACJ,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,qBAAqB;6BAC7B,CAAC,CAAC;4BACH,SAAS;wBACX,CAAC;wBAED,mBAAmB;wBACnB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,EAAE,GAAG,MAAM,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;wBAElG,OAAO,CAAC,IAAI,CAAC;4BACX,IAAI;4BACJ,OAAO,EAAE,IAAI;yBACd,CAAC,CAAC;oBACL,CAAC;oBAAC,OAAO,GAAQ,EAAE,CAAC;wBAClB,OAAO,CAAC,IAAI,CAAC;4BACX,IAAI;4BACJ,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,GAAG,CAAC,OAAO,IAAI,eAAe;yBACtC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;gBAE3D,OAAO;oBACL,OAAO,EAAE,YAAY,GAAG,CAAC;oBACzB,MAAM,EAAE;wBACN,OAAO,EAAE,cAAc,YAAY,IAAI,KAAK,CAAC,MAAM,eAAe,MAAM,GAAG;wBAC3E,OAAO;qBACR;oBACD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;oBACV,QAAQ,EAAE;wBACR,MAAM;wBACN,SAAS,EAAE,KAAK,CAAC,MAAM;wBACvB,YAAY;wBACZ,YAAY,EAAE,KAAK,CAAC,MAAM,GAAG,YAAY;qBAC1C;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,4BAA4B;gBAC5B,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,EAAE,GAAG,MAAM,MAAM,CAAC,CAAC;oBAExF,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,oCAAoC,MAAM,GAAG;wBACrD,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;wBACV,QAAQ,EAAE;4BACR,MAAM;4BACN,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM;yBAC3D;qBACF,CAAC;gBACJ,CAAC;gBAAC,OAAO,GAAQ,EAAE,CAAC;oBAClB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,iCAAiC,GAAG,CAAC,OAAO,IAAI,eAAe,EAAE;wBACxE,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACvD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAlJD,wCAkJC;AAED,MAAa,eAAe;IACjB,EAAE,GAAG,aAAa,CAAC;IACnB,IAAI,GAAG,cAAc,CAAC;IACtB,WAAW,GAAG,6DAA6D,CAAC;IAC5E,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAClC,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE;KACb,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,MAAiB,EAAE,QAAuB;QACtF,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YAEtD,sBAAsB;YACtB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,yCAAyC;YACzC,MAAM,UAAU,GAOL,EAAE,CAAC;YAEd,kCAAkC;YAClC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,WAAW,EAAE,CAAC;gBACvC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;gBAEjC,UAAU,EAAE,CAAC;gBACb,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;gBAE5B,kCAAkC;gBAClC,IAAI,UAAU,GAAG,CAAC,CAAC;gBACnB,IAAI,YAAY,GAAG,CAAC,CAAC;gBACrB,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAI,SAAS,GAAG,CAAC,CAAC;gBAElB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACxB,KAAK,MAAM,CAAC,kBAAkB,CAAC,KAAK;4BAClC,UAAU,EAAE,CAAC;4BACb,UAAU,EAAE,CAAC;4BACb,MAAM;wBACR,KAAK,MAAM,CAAC,kBAAkB,CAAC,OAAO;4BACpC,YAAY,EAAE,CAAC;4BACf,YAAY,EAAE,CAAC;4BACf,MAAM;wBACR,KAAK,MAAM,CAAC,kBAAkB,CAAC,WAAW;4BACxC,SAAS,EAAE,CAAC;4BACZ,SAAS,EAAE,CAAC;4BACZ,MAAM;wBACR,KAAK,MAAM,CAAC,kBAAkB,CAAC,IAAI;4BACjC,SAAS,EAAE,CAAC;4BACZ,SAAS,EAAE,CAAC;4BACZ,MAAM;oBACR,CAAC;gBACH,CAAC;gBAED,8BAA8B;gBAC9B,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,GAAG,CAAC,MAAM;oBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,MAAM,EAAE,UAAU;oBAClB,QAAQ,EAAE,YAAY;oBACtB,KAAK,EAAE,SAAS;oBAChB,KAAK,EAAE,SAAS;iBACjB,CAAC,CAAC;YACL,CAAC;YAED,iDAAiD;YACjD,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YAE/C,8BAA8B;YAC9B,MAAM,aAAa,GAAG;gBACpB,QAAQ,EAAE;oBACR,UAAU;oBACV,WAAW;oBACX,MAAM,EAAE;wBACN,MAAM,EAAE,UAAU;wBAClB,QAAQ,EAAE,YAAY;wBACtB,WAAW,EAAE,SAAS;wBACtB,KAAK,EAAE,SAAS;qBACjB;iBACF;gBACD,WAAW,EAAE,UAAU;gBACvB,aAAa,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gCAAgC;aACxE,CAAC;YAEF,uCAAuC;YACvC,MAAM,WAAW,GAAG;gBAClB,eAAe;gBACf,eAAe;gBACf,4BAA4B,UAAU,EAAE;gBACxC,iBAAiB,WAAW,EAAE;gBAC9B,eAAe,UAAU,EAAE;gBAC3B,iBAAiB,YAAY,EAAE;gBAC/B,oBAAoB,SAAS,EAAE;gBAC/B,cAAc,SAAS,EAAE;gBACzB,EAAE;gBACF,+BAA+B;gBAC/B,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAChC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,MAAM,YAAY,CAAC,CAAC,MAAM,YAAY,CAAC,CAAC,QAAQ,cAAc,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,KAAK,SAAS,CAClH;aACF,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEb,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,aAAa;iBACpB;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,WAAW;oBACX,UAAU;oBACV,YAAY;iBACb;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACvD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AA1ID,0CA0IC;AAED,MAAa,qBAAqB;IACvB,EAAE,GAAG,mBAAmB,CAAC;IACzB,IAAI,GAAG,oBAAoB,CAAC;IAC5B,WAAW,GAAG,mDAAmD,CAAC;IAClE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;QAC3E,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;KACrG,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oCAAoC,EAAE;YAC5E,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0DAA0D,EAAE;SACtG;QACD,QAAQ,EAAE,EAAE;KACb,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,KAAK,GAAG,KAAK,CAAC,KAA2B,CAAC;QAChD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAA8B,CAAC;QACtD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YACtD,MAAM,OAAO,GAAU,EAAE,CAAC;YAC1B,IAAI,gBAAgB,GAAG,CAAC,CAAC;YACzB,IAAI,kBAAkB,GAAG,CAAC,CAAC;YAE3B,+CAA+C;YAC/C,MAAM,aAAa,GAAG;gBACpB,CAAC,EAAE,OAAO;gBACV,CAAC,EAAE,SAAS;gBACZ,CAAC,EAAE,aAAa;gBAChB,CAAC,EAAE,MAAM;aACV,CAAC;YAEF,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,WAAW,EAAE,CAAC;gBACvC,gBAAgB,IAAI,KAAK,CAAC,MAAM,CAAC;gBACjC,MAAM,WAAW,GAAU,EAAE,CAAC;gBAE9B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,gBAAgB;oBAChB,MAAM,YAAY,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;oBACxF,MAAM,eAAe,GAAG,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC;oBAE7E,IAAI,YAAY,IAAI,eAAe,EAAE,CAAC;wBACpC,kBAAkB,EAAE,CAAC;wBAErB,mDAAmD;wBACnD,IAAI,WAAW,GAAG,EAAE,CAAC;wBACrB,IAAI,CAAC;4BACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;4BAC9D,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BACpD,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;wBACjC,CAAC;wBAAC,OAAO,IAAI,EAAE,CAAC;4BACd,WAAW,GAAG,2BAA2B,CAAC;wBAC5C,CAAC;wBAED,WAAW,CAAC,IAAI,CAAC;4BACf,OAAO,EAAE,IAAI,CAAC,OAAO;4BACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;4BACvB,YAAY,EAAE,aAAa,CAAC,IAAI,CAAC,QAAsC,CAAC,IAAI,SAAS;4BACrF,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,4CAA4C;4BAC7E,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,wBAAwB;4BAChE,IAAI,EAAE,WAAW;4BACjB,KAAK,EAAE;gCACL,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE;gCAC7E,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE;6BACxE;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI,EAAE,GAAG,CAAC,MAAM;wBAChB,WAAW,EAAE,WAAW;qBACzB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,4CAA4C;YAC5C,MAAM,gBAAgB,GAAG;gBACvB,OAAO,EAAE;oBACP,UAAU,EAAE,WAAW,CAAC,MAAM,EAAE,uCAAuC;oBACvE,gBAAgB;oBAChB,kBAAkB;oBAClB,OAAO,EAAE;wBACP,KAAK,EAAE,KAAK,IAAI,MAAM;wBACtB,QAAQ,EAAE,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,QAAsC,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,CAAC,MAAM;qBACrH;iBACF;gBACD,OAAO;aACR,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,UAAU,EAAE,kBAAkB;oBAC9B,SAAS,EAAE,OAAO,CAAC,MAAM;iBAC1B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC7D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAjHD,sDAiHC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../types/agent';\nimport { z } from 'zod';\n\nexport class BatchLintTool implements ITool {\n  readonly id = 'batchLint';\n  readonly name = 'Batch Lint';\n  readonly description = 'Run lint on multiple files or the entire workspace.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    files: z.array(z.string()).optional().describe('Files to lint. If empty, lint all.'),\n    linter: z.string().optional().describe('Linter to use (e.g., eslint, stylelint, etc.).')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      files: { type: 'array', items: { type: 'string' }, description: 'Files to lint. If empty, lint all.' },\n      linter: { type: 'string', description: '<PERSON><PERSON> to use (e.g., eslint, stylelint, etc.).' }\n    },\n    required: []\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      // Validate input\n      const parsedInput = this.singleActionSchema.safeParse(input);\n      if (!parsedInput.success) {\n        return {\n          success: false,\n          error: `Invalid input: ${parsedInput.error.message}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      const files = input.files as string[] | undefined;\n      const linter = input.linter as string || 'eslint';\n\n      // Validate linter name to prevent command injection\n      if (!/^[a-zA-Z0-9_\\-]+$/.test(linter)) {\n        return {\n          success: false,\n          error: 'Invalid linter name. Only alphanumeric characters, hyphens, and underscores are allowed.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Check if workspace is available\n      if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {\n        return {\n          success: false,\n          error: 'No workspace folder open. Linting requires an open workspace.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Track results for each file\n      const results: Array<{file: string, success: boolean, error?: string}> = [];\n\n      if (files && files.length > 0) {\n        // Lint specific files\n        for (const file of files) {\n          try {\n            // Validate file path\n            const uri = vscode.Uri.file(file);\n            let exists = false;\n            try {\n              await vscode.workspace.fs.stat(uri);\n              exists = true;\n            } catch {\n              exists = false;\n            }\n\n            if (!exists) {\n              results.push({\n                file,\n                success: false,\n                error: 'File does not exist'\n              });\n              continue;\n            }\n\n            // Run the lint task\n            await vscode.commands.executeCommand('workbench.action.tasks.runTask', `${linter}:lint`, { file });\n\n            results.push({\n              file,\n              success: true\n            });\n          } catch (err: any) {\n            results.push({\n              file,\n              success: false,\n              error: err.message || 'Unknown error'\n            });\n          }\n        }\n\n        const successCount = results.filter(r => r.success).length;\n\n        return {\n          success: successCount > 0,\n          output: {\n            message: `Linted ${successCount}/${files.length} files with ${linter}.`,\n            results\n          },\n          toolId: this.id,\n          actionName,\n          metadata: {\n            linter,\n            fileCount: files.length,\n            successCount,\n            failureCount: files.length - successCount\n          }\n        };\n      } else {\n        // Lint entire workspace\n        try {\n          await vscode.commands.executeCommand('workbench.action.tasks.runTask', `${linter}:lint`);\n\n          return {\n            success: true,\n            output: `Linted entire workspace with ${linter}.`,\n            toolId: this.id,\n            actionName,\n            metadata: {\n              linter,\n              workspace: vscode.workspace.workspaceFolders[0].uri.fsPath\n            }\n          };\n        } catch (err: any) {\n          return {\n            success: false,\n            error: `Failed to lint workspace: ${err.message || 'Unknown error'}`,\n            toolId: this.id,\n            actionName\n          };\n        }\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Batch lint failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n}\n\nexport class AutoFixAllTool implements ITool {\n  readonly id = 'autoFixAll';\n  readonly name = 'Auto Fix All';\n  readonly description = 'Automatically fix all fixable lint errors in given files or workspace.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    files: z.array(z.string()).optional().describe('Files to fix. If empty, fix all.'),\n    linter: z.string().optional().describe('Linter to use (e.g., eslint, stylelint, etc.).')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      files: { type: 'array', items: { type: 'string' }, description: 'Files to fix. If empty, fix all.' },\n      linter: { type: 'string', description: 'Linter to use (e.g., eslint, stylelint, etc.).' }\n    },\n    required: []\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      // Validate input\n      const parsedInput = this.singleActionSchema.safeParse(input);\n      if (!parsedInput.success) {\n        return {\n          success: false,\n          error: `Invalid input: ${parsedInput.error.message}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      const files = input.files as string[] | undefined;\n      const linter = input.linter as string || 'eslint';\n\n      // Validate linter name to prevent command injection\n      if (!/^[a-zA-Z0-9_\\-]+$/.test(linter)) {\n        return {\n          success: false,\n          error: 'Invalid linter name. Only alphanumeric characters, hyphens, and underscores are allowed.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Check if workspace is available\n      if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {\n        return {\n          success: false,\n          error: 'No workspace folder open. Auto-fixing requires an open workspace.',\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Track results for each file\n      const results: Array<{file: string, success: boolean, error?: string}> = [];\n\n      if (files && files.length > 0) {\n        // Auto-fix specific files\n        for (const file of files) {\n          try {\n            // Validate file path\n            const uri = vscode.Uri.file(file);\n            let exists = false;\n            try {\n              await vscode.workspace.fs.stat(uri);\n              exists = true;\n            } catch {\n              exists = false;\n            }\n\n            if (!exists) {\n              results.push({\n                file,\n                success: false,\n                error: 'File does not exist'\n              });\n              continue;\n            }\n\n            // Run the fix task\n            await vscode.commands.executeCommand('workbench.action.tasks.runTask', `${linter}:fix`, { file });\n\n            results.push({\n              file,\n              success: true\n            });\n          } catch (err: any) {\n            results.push({\n              file,\n              success: false,\n              error: err.message || 'Unknown error'\n            });\n          }\n        }\n\n        const successCount = results.filter(r => r.success).length;\n\n        return {\n          success: successCount > 0,\n          output: {\n            message: `Auto-fixed ${successCount}/${files.length} files with ${linter}.`,\n            results\n          },\n          toolId: this.id,\n          actionName,\n          metadata: {\n            linter,\n            fileCount: files.length,\n            successCount,\n            failureCount: files.length - successCount\n          }\n        };\n      } else {\n        // Auto-fix entire workspace\n        try {\n          await vscode.commands.executeCommand('workbench.action.tasks.runTask', `${linter}:fix`);\n\n          return {\n            success: true,\n            output: `Auto-fixed entire workspace with ${linter}.`,\n            toolId: this.id,\n            actionName,\n            metadata: {\n              linter,\n              workspace: vscode.workspace.workspaceFolders[0].uri.fsPath\n            }\n          };\n        } catch (err: any) {\n          return {\n            success: false,\n            error: `Failed to auto-fix workspace: ${err.message || 'Unknown error'}`,\n            toolId: this.id,\n            actionName\n          };\n        }\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Auto-fix all failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n}\n\nexport class LintSummaryTool implements ITool {\n  readonly id = 'lintSummary';\n  readonly name = 'Lint Summary';\n  readonly description = 'Generate a summary report of lint errors for the workspace.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({});\n  readonly inputSchema = {\n    type: 'object',\n    properties: {},\n    required: []\n  };\n  async execute(actionName: string | undefined, _input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      const diagnostics = vscode.languages.getDiagnostics();\n\n      // Initialize counters\n      let totalIssues = 0;\n      let totalFiles = 0;\n      let errorCount = 0;\n      let warningCount = 0;\n      let infoCount = 0;\n      let hintCount = 0;\n\n      // Map to store file-specific diagnostics\n      const fileIssues: Array<{\n                file: string;\n                issues: number;\n                errors: number;\n                warnings: number;\n                infos: number;\n                hints: number;\n            }> = [];\n\n      // Process each file's diagnostics\n      for (const [uri, diags] of diagnostics) {\n        if (diags.length === 0) continue;\n\n        totalFiles++;\n        totalIssues += diags.length;\n\n        // Count by severity for this file\n        let fileErrors = 0;\n        let fileWarnings = 0;\n        let fileInfos = 0;\n        let fileHints = 0;\n\n        for (const diag of diags) {\n          switch (diag.severity) {\n          case vscode.DiagnosticSeverity.Error:\n            errorCount++;\n            fileErrors++;\n            break;\n          case vscode.DiagnosticSeverity.Warning:\n            warningCount++;\n            fileWarnings++;\n            break;\n          case vscode.DiagnosticSeverity.Information:\n            infoCount++;\n            fileInfos++;\n            break;\n          case vscode.DiagnosticSeverity.Hint:\n            hintCount++;\n            fileHints++;\n            break;\n          }\n        }\n\n        // Add file details to the map\n        fileIssues.push({\n          file: uri.fsPath,\n          issues: diags.length,\n          errors: fileErrors,\n          warnings: fileWarnings,\n          infos: fileInfos,\n          hints: fileHints\n        });\n      }\n\n      // Sort files by total issues (most issues first)\n      fileIssues.sort((a, b) => b.issues - a.issues);\n\n      // Create a structured summary\n      const summaryObject = {\n        overview: {\n          totalFiles,\n          totalIssues,\n          byType: {\n            errors: errorCount,\n            warnings: warningCount,\n            information: infoCount,\n            hints: hintCount\n          }\n        },\n        fileDetails: fileIssues,\n        topIssueFiles: fileIssues.slice(0, 10) // Top 10 files with most issues\n      };\n\n      // Create a human-readable summary text\n      const summaryText = [\n        'Lint Summary:',\n        '-------------',\n        `Total Files with Issues: ${totalFiles}`,\n        `Total Issues: ${totalIssues}`,\n        `  - Errors: ${errorCount}`,\n        `  - Warnings: ${warningCount}`,\n        `  - Information: ${infoCount}`,\n        `  - Hints: ${hintCount}`,\n        '',\n        'Top 5 Files with Most Issues:',\n        ...fileIssues.slice(0, 5).map(f =>\n          `  ${f.file}: ${f.issues} issues (${f.errors} errors, ${f.warnings} warnings, ${f.infos} info, ${f.hints} hints)`\n        )\n      ].join('\\n');\n\n      return {\n        success: true,\n        output: {\n          text: summaryText,\n          data: summaryObject\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          totalIssues,\n          errorCount,\n          warningCount\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Lint summary failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n}\n\nexport class DiagnosticsSearchTool implements ITool {\n  readonly id = 'diagnosticsSearch';\n  readonly name = 'Diagnostics Search';\n  readonly description = 'Search/filter diagnostics by message or severity.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    query: z.string().optional().describe('Text to search for in diagnostics.'),\n    severity: z.number().optional().describe('Severity to filter (0=Error, 1=Warning, 2=Info, 3=Hint).')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      query: { type: 'string', description: 'Text to search for in diagnostics.' },\n      severity: { type: 'number', description: 'Severity to filter (0=Error, 1=Warning, 2=Info, 3=Hint).' }\n    },\n    required: []\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const query = input.query as string | undefined;\n    const severity = input.severity as number | undefined;\n    try {\n      const diagnostics = vscode.languages.getDiagnostics();\n      const results: any[] = [];\n      let totalDiagnostics = 0;\n      let matchedDiagnostics = 0;\n\n      // Map severity numbers to human-readable names\n      const severityNames = {\n        0: 'Error',\n        1: 'Warning',\n        2: 'Information',\n        3: 'Hint'\n      };\n\n      for (const [uri, diags] of diagnostics) {\n        totalDiagnostics += diags.length;\n        const fileMatches: any[] = [];\n\n        for (const diag of diags) {\n          // Apply filters\n          const queryMatches = !query || diag.message.toLowerCase().includes(query.toLowerCase());\n          const severityMatches = severity === undefined || diag.severity === severity;\n\n          if (queryMatches && severityMatches) {\n            matchedDiagnostics++;\n\n            // Get the line of code where the diagnostic occurs\n            let codeSnippet = '';\n            try {\n              const document = await vscode.workspace.openTextDocument(uri);\n              const line = document.lineAt(diag.range.start.line);\n              codeSnippet = line.text.trim();\n            } catch (_err) {\n              codeSnippet = '<Could not retrieve code>';\n            }\n\n            fileMatches.push({\n              message: diag.message,\n              severity: diag.severity,\n              severityName: severityNames[diag.severity as keyof typeof severityNames] || 'Unknown',\n              line: diag.range.start.line + 1, // 1-based line number for human readability\n              column: diag.range.start.character + 1, // 1-based column number\n              code: codeSnippet,\n              range: {\n                start: { line: diag.range.start.line, character: diag.range.start.character },\n                end: { line: diag.range.end.line, character: diag.range.end.character }\n              }\n            });\n          }\n        }\n\n        if (fileMatches.length > 0) {\n          results.push({\n            file: uri.fsPath,\n            diagnostics: fileMatches\n          });\n        }\n      }\n\n      // Format the results in a more readable way\n      const formattedResults = {\n        summary: {\n          totalFiles: diagnostics.length, // Use length instead of size for array\n          totalDiagnostics,\n          matchedDiagnostics,\n          filters: {\n            query: query || 'none',\n            severity: severity !== undefined ? `${severityNames[severity as keyof typeof severityNames]} (${severity})` : 'none'\n          }\n        },\n        results\n      };\n\n      return {\n        success: true,\n        output: formattedResults,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          matchCount: matchedDiagnostics,\n          fileCount: results.length\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Diagnostics search failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n}\n"]}