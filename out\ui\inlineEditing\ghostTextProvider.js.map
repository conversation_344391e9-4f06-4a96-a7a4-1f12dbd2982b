{"version": 3, "file": "ghostTextProvider.js", "sourceRoot": "", "sources": ["../../../src/ui/inlineEditing/ghostTextProvider.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AAIjC,yCAAsC;AAqBtC,MAAa,iBAAiB;IACpB,eAAe,CAAkB;IACjC,aAAa,CAAgB;IAC7B,YAAY,CAAe;IAC3B,WAAW,GAAwB,EAAE,CAAC;IAE9C,2BAA2B;IACnB,eAAe,GAAuC,IAAI,GAAG,EAAE,CAAC;IACvD,WAAW,GAAG,KAAK,CAAC,CAAC,aAAa;IAClC,YAAY,GAAG,GAAG,CAAC;IAEpC,mBAAmB;IACX,YAAY,CAA8B;IAC1C,YAAY,CAAkC;IAC9C,cAAc,GAAwB,EAAE,CAAC;IAEjD,YACE,eAAgC,EAChC,aAA4B,EAC5B,YAA0B;QAE1B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,gBAAgB;QACtB,yCAAyC;QACzC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,oCAAoC,CACpE,EAAE,MAAM,EAAE,MAAM,EAAE,EAClB,IAAI,CACL,CAAC;QACF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhC,eAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;IACvE,CAAC;IAEO,mBAAmB;QACzB,qCAAqC;QACrC,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC,KAAK,CAAC,EAAE;YAC1E,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEtC,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;YACxE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,4BAA4B,CAChC,QAA6B,EAC7B,QAAyB,EACzB,OAAuC,EACvC,KAA+B;QAE/B,IAAI,CAAC;YACH,yCAAyC;YACzC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC;gBAChE,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,gCAAgC;YAChC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE1E,oBAAoB;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,8BAA8B,CAAC,iBAAiB,CAAC,CAAC;YAChE,CAAC;YAED,0BAA0B;YAC1B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAEzE,wBAAwB;YACxB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAE7C,4BAA4B;YAC5B,OAAO,IAAI,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAC;QAE1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;YACpD,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;SAEK;IACG,wBAAwB,CAC9B,QAA6B,EAC7B,QAAyB,EACzB,OAAuC;QAEvC,4DAA4D;QAC5D,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QACrD,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE/D,iCAAiC;QACjC,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gCAAgC;QAChC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;QACnG,MAAM,cAAc,GAAG,kBAAkB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACtE,MAAM,aAAa,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAEvE,IAAI,YAAY,GAAG,aAAa,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mDAAmD;QACnD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,sBAAsB,CAClC,QAA6B,EAC7B,QAAyB;QAEzB,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,SAAS,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAExG,kDAAkD;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACpE,MAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvG,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAE3D,iDAAiD;QACjD,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnD,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QACvE,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACnE,MAAM,aAAa,GAAG,GAAG,YAAY,IAAI,WAAW,EAAE,CAAC;QAEvD,4CAA4C;QAC5C,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC7D,IAAI,aAAa,EAAE,CAAC;gBAClB,MAAM,cAAc,GAAG,MAAM,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE;oBAC/D,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM;oBAC7B,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE;iBACjE,CAAC,CAAC;gBACH,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;oBAC3B,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC;gBAC1C,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,eAAe;YACf,aAAa;YACb,eAAe;SAChB,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CACjC,OAA0B,EAC1B,KAA+B;QAE/B,MAAM,WAAW,GAA0B,EAAE,CAAC;QAE9C,IAAI,CAAC;YACH,iDAAiD;YACjD,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAEnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBAC5C,MAAM;gBACN,IAAI,EAAE,MAAM;aACb,EAAE;gBACD,SAAS,EAAE;oBACT,WAAW,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;oBAC5C,SAAS,EAAE;wBACT,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;wBACjD,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;qBAC5B;oBACD,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBAC3G;aACF,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,qCAAqC;gBACrC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC1E,WAAW,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;YACzC,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,gDAAgD;QAChD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;SAEK;IACG,qBAAqB,CAAC,OAA0B;QACtD,OAAO;;;;UAID,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM;cACvB,OAAO,CAAC,QAAQ,CAAC,UAAU;mBACtB,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC;;;QAG9E,OAAO,CAAC,QAAQ,CAAC,UAAU;EACjC,OAAO,CAAC,eAAe;;;;EAIvB,OAAO,CAAC,aAAa;;;EAGrB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;SAuBzC,CAAC;IACR,CAAC;IAED;;SAEK;IACG,kBAAkB,CAAC,UAAkB,EAAE,OAA0B;QACvE,MAAM,WAAW,GAA0B,EAAE,CAAC;QAE9C,IAAI,CAAC;YACH,6BAA6B;YAC7B,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAClD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,MAAM,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBACnD,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;wBACzC,WAAW,CAAC,IAAI,CAAC;4BACf,EAAE,EAAE,MAAM,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;4BAC3B,IAAI,EAAE,UAAU,CAAC,IAAI,IAAI,EAAE;4BAC3B,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC;4BAC3D,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,GAAG;4BACxC,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,yBAAyB;4BAC5D,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,KAAK;4BACxC,YAAY,EAAE,IAAI;yBACnB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YACxD,yDAAyD;YACzD,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,WAAW,CAAC,IAAI,CAAC;oBACf,EAAE,EAAE,eAAe,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC/B,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;oBACvB,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC;oBAC3D,UAAU,EAAE,GAAG;oBACf,SAAS,EAAE,wBAAwB;oBACnC,SAAS,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACpC,YAAY,EAAE,IAAI;iBACnB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;SAEK;IACG,2BAA2B,CAAC,OAA0B;QAC5D,MAAM,WAAW,GAA0B,EAAE,CAAC;QAC9C,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnE,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE/E,mCAAmC;QACnC,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACtC,WAAW,CAAC,IAAI,CAAC;gBACf,EAAE,EAAE,oBAAoB,IAAI,CAAC,GAAG,EAAE,EAAE;gBACpC,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC;gBAC3D,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,uBAAuB;gBAClC,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,KAAK;aACpB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,WAAW,CAAC,IAAI,CAAC;gBACf,EAAE,EAAE,eAAe,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC/B,IAAI,EAAE,uBAAuB;gBAC7B,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC;gBAC3D,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,yBAAyB;gBACpC,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,KAAK;aACpB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,6CAA6C;IACrC,gBAAgB,CAAC,OAA0B;QACjD,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;IAC1H,CAAC;IAEO,oBAAoB,CAAC,GAAW;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB,CAAC,GAAW,EAAE,WAAkC;QACtE,sBAAsB;QACtB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YAC1D,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAC7C,CAAC;IAEO,8BAA8B,CAAC,WAAkC;QACvE,OAAO,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACpC,UAAU,EAAE,UAAU,CAAC,IAAI;YAC3B,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,OAAO,EAAE;gBACP,OAAO,EAAE,0BAA0B;gBACnC,KAAK,EAAE,mBAAmB;gBAC1B,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,aAAa,CAAC,QAAyB;QAC7C,gCAAgC;QAChC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAI,IAAI,CAAC,YAAoB,CAAC,SAAS,CAAC;YACnE,OAAO,QAAQ,GAAG,GAAG,CAAC,CAAC,6CAA6C;QACtE,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,uBAAuB,CAAC,KAA4C;QAC1E,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC;QACtD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,YAAoB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACpD,CAAC;IAEO,iBAAiB,CAAC,KAAqC;QAC7D,mCAAmC;QACnC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QAC1C,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzC,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF;AA/ZD,8CA+ZC", "sourcesContent": ["/**\n * Ghost Text Provider - Advanced inline editing with AI suggestions\n * \n * Provides ghost text previews, multi-line suggestions, and contextual inline chat\n * that integrates seamlessly with existing Codessa functionality.\n */\n\nimport * as vscode from 'vscode';\nimport { SupervisorAgent } from '../../agents/agentTypes/supervisorAgent';\nimport { MemoryManager } from '../../memory/memoryManager';\nimport { ToolRegistry } from '../../tools/toolRegistry';\nimport { logger } from '../../logger';\n\nexport interface GhostTextSuggestion {\n  id: string;\n  text: string;\n  range: vscode.Range;\n  confidence: number;\n  reasoning: string;\n  multiLine: boolean;\n  contextAware: boolean;\n}\n\nexport interface InlineEditContext {\n  document: vscode.TextDocument;\n  position: vscode.Position;\n  selection: vscode.Selection;\n  surroundingText: string;\n  cursorContext: string;\n  semanticContext: any;\n}\n\nexport class GhostTextProvider implements vscode.InlineCompletionItemProvider {\n  private supervisorAgent: SupervisorAgent;\n  private memoryManager: MemoryManager;\n  private toolRegistry: ToolRegistry;\n  private disposables: vscode.Disposable[] = [];\n\n  // Performance optimization\n  private suggestionCache: Map<string, GhostTextSuggestion[]> = new Map();\n  private readonly cacheExpiry = 30000; // 30 seconds\n  private readonly maxCacheSize = 100;\n\n  // Context tracking\n  private lastPosition: vscode.Position | undefined;\n  private lastDocument: vscode.TextDocument | undefined;\n  private contextHistory: InlineEditContext[] = [];\n\n  constructor(\n    supervisorAgent: SupervisorAgent,\n    memoryManager: MemoryManager,\n    toolRegistry: ToolRegistry\n  ) {\n    this.supervisorAgent = supervisorAgent;\n    this.memoryManager = memoryManager;\n    this.toolRegistry = toolRegistry;\n\n    this.registerProvider();\n    this.setupEventListeners();\n  }\n\n  private registerProvider(): void {\n    // Register as inline completion provider\n    const provider = vscode.languages.registerInlineCompletionItemProvider(\n      { scheme: 'file' },\n      this\n    );\n    this.disposables.push(provider);\n\n    logger.info('Ghost Text Provider registered for inline completions');\n  }\n\n  private setupEventListeners(): void {\n    // Listen for cursor position changes\n    const cursorListener = vscode.window.onDidChangeTextEditorSelection(event => {\n      this.onCursorPositionChanged(event);\n    });\n    this.disposables.push(cursorListener);\n\n    // Listen for document changes\n    const documentListener = vscode.workspace.onDidChangeTextDocument(event => {\n      this.onDocumentChanged(event);\n    });\n    this.disposables.push(documentListener);\n  }\n\n  /**\n     * Main inline completion provider method\n     */\n  async provideInlineCompletionItems(\n    document: vscode.TextDocument,\n    position: vscode.Position,\n    context: vscode.InlineCompletionContext,\n    token: vscode.CancellationToken\n  ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | undefined> {\n    try {\n      // Check if we should provide suggestions\n      if (!this.shouldProvideSuggestions(document, position, context)) {\n        return undefined;\n      }\n\n      // Build context for AI analysis\n      const editContext = await this.buildInlineEditContext(document, position);\n\n      // Check cache first\n      const cacheKey = this.generateCacheKey(editContext);\n      const cachedSuggestions = this.getCachedSuggestions(cacheKey);\n      if (cachedSuggestions) {\n        return this.convertToInlineCompletionItems(cachedSuggestions);\n      }\n\n      // Generate AI suggestions\n      const suggestions = await this.generateAISuggestions(editContext, token);\n\n      // Cache the suggestions\n      this.cacheSuggestions(cacheKey, suggestions);\n\n      // Convert to VS Code format\n      return this.convertToInlineCompletionItems(suggestions);\n\n    } catch (error) {\n      logger.error(`Ghost text provider error: ${error}`);\n      return undefined;\n    }\n  }\n\n  /**\n     * Determine if we should provide suggestions\n     */\n  private shouldProvideSuggestions(\n    document: vscode.TextDocument,\n    position: vscode.Position,\n    context: vscode.InlineCompletionContext\n  ): boolean {\n    // Don't suggest in comments (unless specifically requested)\n    const lineText = document.lineAt(position.line).text;\n    const beforeCursor = lineText.substring(0, position.character);\n\n    // Skip if in single-line comment\n    if (beforeCursor.trim().startsWith('//')) {\n      return false;\n    }\n\n    // Skip if in multi-line comment\n    const textBeforePosition = document.getText(new vscode.Range(new vscode.Position(0, 0), position));\n    const commentMatches = textBeforePosition.match(/\\/\\*[\\s\\S]*?\\*\\//g);\n    const openComments = (textBeforePosition.match(/\\/\\*/g) || []).length;\n    const closeComments = (textBeforePosition.match(/\\*\\//g) || []).length;\n\n    if (openComments > closeComments) {\n      return false;\n    }\n\n    // Don't suggest if user is actively typing rapidly\n    if (this.isRapidTyping(position)) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n     * Build comprehensive context for inline editing\n     */\n  private async buildInlineEditContext(\n    document: vscode.TextDocument,\n    position: vscode.Position\n  ): Promise<InlineEditContext> {\n    const selection = vscode.window.activeTextEditor?.selection || new vscode.Selection(position, position);\n\n    // Get surrounding text (5 lines before and after)\n    const startLine = Math.max(0, position.line - 5);\n    const endLine = Math.min(document.lineCount - 1, position.line + 5);\n    const surroundingRange = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);\n    const surroundingText = document.getText(surroundingRange);\n\n    // Get cursor context (current line and position)\n    const currentLine = document.lineAt(position.line);\n    const beforeCursor = currentLine.text.substring(0, position.character);\n    const afterCursor = currentLine.text.substring(position.character);\n    const cursorContext = `${beforeCursor}|${afterCursor}`;\n\n    // Get semantic context using existing tools\n    let semanticContext = {};\n    try {\n      const codeIntelTool = this.toolRegistry.getTool('codeIntel');\n      if (codeIntelTool) {\n        const semanticResult = await codeIntelTool.execute('getContext', {\n          filePath: document.uri.fsPath,\n          position: { line: position.line, character: position.character }\n        });\n        if (semanticResult.success) {\n          semanticContext = semanticResult.output;\n        }\n      }\n    } catch (error) {\n      logger.warn(`Failed to get semantic context: ${error}`);\n    }\n\n    return {\n      document,\n      position,\n      selection,\n      surroundingText,\n      cursorContext,\n      semanticContext\n    };\n  }\n\n  /**\n     * Generate AI-powered suggestions\n     */\n  private async generateAISuggestions(\n    context: InlineEditContext,\n    token: vscode.CancellationToken\n  ): Promise<GhostTextSuggestion[]> {\n    const suggestions: GhostTextSuggestion[] = [];\n\n    try {\n      // Use existing SupervisorAgent for AI generation\n      const prompt = this.buildSuggestionPrompt(context);\n\n      const result = await this.supervisorAgent.run({\n        prompt,\n        mode: 'edit'\n      }, {\n        workspace: {\n          currentFile: context.document.uri.toString(),\n          selection: {\n            text: context.document.getText(context.selection),\n            range: { start: 0, end: 0 }\n          },\n          workspaceFolders: Array.from(vscode.workspace.workspaceFolders ?? []).map(folder => folder.uri.toString())\n        }\n      });\n\n      if (result.success && result.output) {\n        // Parse AI response into suggestions\n        const parsedSuggestions = this.parseAISuggestions(result.output, context);\n        suggestions.push(...parsedSuggestions);\n      }\n\n    } catch (error) {\n      logger.error(`AI suggestion generation failed: ${error}`);\n    }\n\n    // Add fallback suggestions if no AI suggestions\n    if (suggestions.length === 0) {\n      suggestions.push(...this.generateFallbackSuggestions(context));\n    }\n\n    return suggestions;\n  }\n\n  /**\n     * Build prompt for AI suggestion generation\n     */\n  private buildSuggestionPrompt(context: InlineEditContext): string {\n    return `\n# Inline Code Completion Request\n\n## Context:\n- File: ${context.document.uri.fsPath}\n- Language: ${context.document.languageId}\n- Position: Line ${context.position.line + 1}, Column ${context.position.character + 1}\n\n## Current Code Context:\n\\`\\`\\`${context.document.languageId}\n${context.surroundingText}\n\\`\\`\\`\n\n## Cursor Position:\n${context.cursorContext}\n\n## Semantic Context:\n${JSON.stringify(context.semanticContext, null, 2)}\n\n## Task:\nProvide intelligent code completion suggestions for the cursor position marked with |.\nConsider:\n1. Code context and patterns\n2. Language-specific best practices\n3. Variable scope and types\n4. Function signatures and return types\n5. Common coding patterns\n\n## Response Format:\nProvide 1-3 completion suggestions in JSON format:\n{\n  \"suggestions\": [\n    {\n      \"text\": \"completion text\",\n      \"confidence\": 0.9,\n      \"reasoning\": \"why this suggestion makes sense\",\n      \"multiLine\": false\n    }\n  ]\n}\n        `;\n  }\n\n  /**\n     * Parse AI response into structured suggestions\n     */\n  private parseAISuggestions(aiResponse: string, context: InlineEditContext): GhostTextSuggestion[] {\n    const suggestions: GhostTextSuggestion[] = [];\n\n    try {\n      // Try to parse JSON response\n      const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0]);\n        if (parsed.suggestions && Array.isArray(parsed.suggestions)) {\n          for (let i = 0; i < parsed.suggestions.length; i++) {\n            const suggestion = parsed.suggestions[i];\n            suggestions.push({\n              id: `ai_${Date.now()}_${i}`,\n              text: suggestion.text || '',\n              range: new vscode.Range(context.position, context.position),\n              confidence: suggestion.confidence || 0.5,\n              reasoning: suggestion.reasoning || 'AI generated suggestion',\n              multiLine: suggestion.multiLine || false,\n              contextAware: true\n            });\n          }\n        }\n      }\n    } catch (error) {\n      logger.warn(`Failed to parse AI suggestions: ${error}`);\n      // Fallback: treat entire response as a single suggestion\n      if (aiResponse.trim().length > 0) {\n        suggestions.push({\n          id: `ai_fallback_${Date.now()}`,\n          text: aiResponse.trim(),\n          range: new vscode.Range(context.position, context.position),\n          confidence: 0.3,\n          reasoning: 'Fallback AI suggestion',\n          multiLine: aiResponse.includes('\\n'),\n          contextAware: true\n        });\n      }\n    }\n\n    return suggestions;\n  }\n\n  /**\n     * Generate fallback suggestions when AI fails\n     */\n  private generateFallbackSuggestions(context: InlineEditContext): GhostTextSuggestion[] {\n    const suggestions: GhostTextSuggestion[] = [];\n    const currentLine = context.document.lineAt(context.position.line);\n    const beforeCursor = currentLine.text.substring(0, context.position.character);\n\n    // Simple pattern-based suggestions\n    if (beforeCursor.endsWith('console.')) {\n      suggestions.push({\n        id: `fallback_console_${Date.now()}`,\n        text: 'log()',\n        range: new vscode.Range(context.position, context.position),\n        confidence: 0.8,\n        reasoning: 'Common console method',\n        multiLine: false,\n        contextAware: false\n      });\n    }\n\n    if (beforeCursor.endsWith('if (')) {\n      suggestions.push({\n        id: `fallback_if_${Date.now()}`,\n        text: 'condition) {\\n    \\n}',\n        range: new vscode.Range(context.position, context.position),\n        confidence: 0.7,\n        reasoning: 'If statement completion',\n        multiLine: true,\n        contextAware: false\n      });\n    }\n\n    return suggestions;\n  }\n\n  // Helper methods for caching and performance\n  private generateCacheKey(context: InlineEditContext): string {\n    return `${context.document.uri.fsPath}_${context.position.line}_${context.position.character}_${context.cursorContext}`;\n  }\n\n  private getCachedSuggestions(key: string): GhostTextSuggestion[] | null {\n    const cached = this.suggestionCache.get(key);\n    if (cached) {\n      return cached;\n    }\n    return null;\n  }\n\n  private cacheSuggestions(key: string, suggestions: GhostTextSuggestion[]): void {\n    // Implement LRU cache\n    if (this.suggestionCache.size >= this.maxCacheSize) {\n      const firstKey = this.suggestionCache.keys().next().value;\n      if (firstKey) {\n        this.suggestionCache.delete(firstKey);\n      }\n    }\n    this.suggestionCache.set(key, suggestions);\n  }\n\n  private convertToInlineCompletionItems(suggestions: GhostTextSuggestion[]): vscode.InlineCompletionItem[] {\n    return suggestions.map(suggestion => ({\n      insertText: suggestion.text,\n      range: suggestion.range,\n      command: {\n        command: 'codessa.acceptSuggestion',\n        title: 'Accept Suggestion',\n        arguments: [suggestion]\n      }\n    }));\n  }\n\n  private isRapidTyping(position: vscode.Position): boolean {\n    // Simple rapid typing detection\n    if (this.lastPosition && this.lastDocument) {\n      const timeDiff = Date.now() - (this.lastPosition as any).timestamp;\n      return timeDiff < 100; // Less than 100ms since last position change\n    }\n    return false;\n  }\n\n  private onCursorPositionChanged(event: vscode.TextEditorSelectionChangeEvent): void {\n    this.lastPosition = event.textEditor.selection.active;\n    this.lastDocument = event.textEditor.document;\n    (this.lastPosition as any).timestamp = Date.now();\n  }\n\n  private onDocumentChanged(event: vscode.TextDocumentChangeEvent): void {\n    // Clear cache for changed document\n    const docPath = event.document.uri.fsPath;\n    for (const [key] of this.suggestionCache) {\n      if (key.startsWith(docPath)) {\n        this.suggestionCache.delete(key);\n      }\n    }\n  }\n\n  dispose(): void {\n    this.disposables.forEach(d => d.dispose());\n    this.suggestionCache.clear();\n  }\n}\n"]}