import * as vscode from 'vscode';
import { Logger, logger } from './logger';
import { llmService } from './llm/llmService';
import { AgentManager } from './agents/agentUtilities/agentManager';
import { ToolRegistry } from './tools/toolRegistry';
import { workflowRegistry } from './agents/workflows/workflowRegistry';
import { MemoryManager } from './memory/memoryManager';
import { workflowManager } from './agents/workflows/workflowManager';
import { knowledgebaseManager } from './knowledge/knowledgebaseManager';
import { mcpManager } from './mcp/mcpManager';
import { promptManager } from './prompts/promptManager';
import { ChatViewProvider } from './ui/chat/chatViewProvider';
import receiverAgent from './agents/agentTypes/receiverAgent';
import { SupervisorAgent } from './agents/agentTypes/supervisorAgent';
import { createModeAgent } from './agents/agentUtilities/agentFactory';
import { openModeSelector } from './commands/openModeSelector';
import { registerModes } from './commands/registerModes';

import { operationModeRegistry } from './agents/agentModes/operationMode';
import { OperationMode } from './agents/workflows/types';
import { ITool } from './tools/tool.ts.backup';

import { DocumentationTool } from './tools/docsTool';
import { DirectoryListTool } from './tools/directoryListTool';
import { CodeSearchTool } from './tools/codeSearchTool';
import { TerminalCommandTool } from './tools/terminalCommandTool';
import { WebSearchTool } from './tools/webSearchTool';
import { WebReadTool } from './tools/webReadTool';
import { MemoryTool } from './tools/memoryTool';
import { BrowserPreviewTool } from './tools/browserPreviewTool';
import { DeployWebAppTool } from './tools/deployWebAppTool';
import { GitTool } from './tools/gitTool';
import { EditorActionsTool } from './tools/editorActionsTool';
import { CodeIntelligenceTool } from './tools/codeIntelligenceTool';
import { CodeGenerationTool } from './tools/codeGenerationTool';
import { LintDiagnosticsTool } from './tools/lintDiagnosticsTool';
import { EnhancedFileSystemTool } from './tools/enhancedFileTools';
import { MCPManager } from './mcp/mcp';
import { GoddessModeManager } from './goddess/goddessMode';
import { AllSettingsPanel } from './ui/settings/allSettingsPanel';
import { providerManager } from './llm/providerManager';
import { SettingsManager } from './ui/settings/settingsManager';
import { FileChangeTracker } from './ui/chat/fileChangeTracker';
import { FileChangePanel } from './ui/chat/fileChangePanel';
import { ProjectWideIntelligence } from './intelligence/projectWideIntelligence';
import { CrossRepositoryContext } from './intelligence/crossRepositoryContext';
import { AgentTreeDataProvider } from './ui/agents/agentTree';
import { TerminalView } from './ui/components/TerminalView';

import { Agent } from './agents/agentUtilities/agent';
import { getDefaultModelConfig } from './config/modelConfig';
import { WorkflowDefinition } from './agents/workflows/workflowEngine';

// Code Action Integration
import { codeActionManager } from './tools/codeActionProvider';

interface WorkflowMetadata {
  advancedFeatures?: {
    goddessMode?: boolean;
    quantumAnalysis?: boolean;
    neuralSynthesis?: boolean;
    timeTravelDebugging?: boolean;
    [key: string]: boolean | undefined;
  };
  revolutionaryFeatures?: Record<string, boolean>;
  [key: string]: unknown;
}

interface Workflow extends WorkflowDefinition {
  metadata?: WorkflowMetadata;
}

interface WorkflowQuickPickItem extends vscode.QuickPickItem {
  workflowId: string;
}
import { ToolDetailsPanel } from './ui/tools/toolDetailsPanel';

// This method is called when your extension is activated
export async function activate(context: vscode.ExtensionContext): Promise<void> {
  Logger.initialize(context);
  logger.info('Codessa extension activating...');

  let toolRegistry: ToolRegistry;

  // Initialize settings manager first
  try {
    const settingsManager = SettingsManager.getInstance();
    await settingsManager.initialize();
    logger.info('Settings manager initialized');
  } catch (e) {
    const errorMessage = 'Codessa: SettingsManager initialization failed: ' + (e instanceof Error ? e.message : String(e));
    logger.error(errorMessage);
    // Defer showing error message until after activation
    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
  }

  // Initialize LLM service
  try {
    llmService.initialize(context);
    logger.info('LLM service initialized');
  } catch (e) {
    const errorMessage = 'Codessa: LLM service initialization failed: ' + (e instanceof Error ? e.message : String(e));
    logger.error(errorMessage);
    // Defer showing error message until after activation
    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
  }

  // Initialize memory manager
  const memoryManager = new MemoryManager();
  try {
    await memoryManager.initialize(context);
    logger.info('Memory manager initialized');
  } catch (e) {
    const errorMessage = 'Codessa: Memory manager initialization failed: ' + (e instanceof Error ? e.message : String(e));
    logger.error(errorMessage);
    // Defer showing error message until after activation
    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
  }

  // Initialize prompt manager
  try {
    await promptManager.initialize(context);
    logger.info('Prompt manager initialized');
  } catch (e) {
    const errorMessage = 'Codessa: Prompt manager initialization failed: ' + (e instanceof Error ? e.message : String(e));
    logger.error(errorMessage);
    // Defer showing error message until after activation
    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
  }

  // Initialize knowledgebase manager
  try {
    await knowledgebaseManager.addKnowledge('Codessa is a powerful, agentic AI coding assistant for Visual Studio Code.');
    logger.info('Knowledgebase manager initialized');
  } catch (e) {
    const errorMessage = 'Codessa: Knowledgebase manager initialization failed: ' + (e instanceof Error ? e.message : String(e));
    logger.error(errorMessage);
    // Defer showing error message until after activation
    setTimeout(() => vscode.window.showErrorMessage(errorMessage), 1000);
  }

  // Initialize tool registry first since other components depend on it
  try {
    toolRegistry = await ToolRegistry.initialize(context);
    logger.info('Tool registry initialized');
  } catch (e) {
    const errorMessage = 'Codessa: Tool registry initialization failed: ' + (e instanceof Error ? e.message : String(e));
    logger.error(errorMessage);
    vscode.window.showErrorMessage(errorMessage);
    return;
  }

  // Register tools with standardized IDs - these will be updated to include schema properties
  try {
    toolRegistry.registerToolWithId(new DocumentationTool(), 'docs');
    toolRegistry.registerToolWithId(new DirectoryListTool(), 'listDir');
    toolRegistry.registerToolWithId(new CodeSearchTool(), 'codeSearch');
    toolRegistry.registerToolWithId(new TerminalCommandTool(), 'terminal_command');
    toolRegistry.registerToolWithId(new WebSearchTool(), 'webSearch');
    toolRegistry.registerToolWithId(new WebReadTool(), 'webRead');
    toolRegistry.registerToolWithId(new MemoryTool(memoryManager), 'memory_management');
    toolRegistry.registerToolWithId(new BrowserPreviewTool(), 'browserPreview');
    toolRegistry.registerToolWithId(new DeployWebAppTool(), 'deployWebApp');
    toolRegistry.registerToolWithId(new GitTool(), 'git');
    toolRegistry.registerToolWithId(new EditorActionsTool(), 'editor');
    toolRegistry.registerToolWithId(new CodeIntelligenceTool(), 'codeIntel');
    toolRegistry.registerToolWithId(new CodeGenerationTool(), 'codeGen');
    toolRegistry.registerToolWithId(new LintDiagnosticsTool(), 'lint_diagnostics');
    toolRegistry.registerToolWithId(new EnhancedFileSystemTool(), 'enhancedFile');
  } catch (error) {
    logger.warn('Some tools may not have proper schema definitions:', error);
  }

  // Verify all tools were registered successfully
  const registeredTools = ToolRegistry.instance.getAllTools();
  if (registeredTools.length === 0) {
    logger.error('No tools were registered in the tool registry');
  } else {
    logger.info(`Successfully registered ${registeredTools.length} tools`);
    // Log each registered tool
    registeredTools.forEach((tool: ITool) => {
      logger.info(`Registered tool: ${tool.id} (${tool.name})`);
    });
  }

  // Store the tool registry instance in the extension context
  context.subscriptions.push({
    dispose: () => {
      try {
        // Clean up tools
        const tools = toolRegistry.getAllTools();
        tools.forEach(tool => {
          if (tool.dispose) {
            tool.dispose();
          }
        });
      } catch (error) {
        logger.error('Error cleaning up tools:', error);
      }
    }
  });

  // Initialize core services that other components depend on (after tool registration)
  // Workflow registry
  try {
    await workflowRegistry.initialize();
    logger.info('Workflow registry initialized');
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Workflow registry initialization failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Register operation modes
  try {
    await registerModes(context);
    logger.info('Operation modes registered');
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Operation modes registration failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Initialize operation modes
  try {
    await operationModeRegistry.initializeModes(context);
    logger.info('Operation modes initialized');
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Operation modes initialization failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Create receiver agent
  let receiverAgentInstance;
  try {
    receiverAgentInstance = new receiverAgent({
      id: 'receiver',
      name: 'Receiver Agent',
      systemPromptName: 'receiver',
      capabilities: ['receive'],
      llmProvider: 'openai',
      llmModel: 'gpt-4'
    });
    logger.info('Receiver agent created');
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Receiver agent creation failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Set up agent context
  let agentContext;
  try {
    const mcpManagerInstance = mcpManager;
    agentContext = {
      variables: {
        workflowManager,
        mcpManager: mcpManagerInstance,
        promptManager,
        knowledgebaseManager
      }
    };
    if (receiverAgentInstance) {
      receiverAgentInstance.setContext(agentContext);
      const agentManager = AgentManager.getInstance();
      agentManager.setReceiverAgent(receiverAgentInstance);
      logger.info('Receiver agent initialized');
    }
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Agent context setup failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Create supervisor agent
  let supervisorAgent;
  try {
    supervisorAgent = new SupervisorAgent({
      id: 'supervisor-agent',
      name: 'Supervisor Agent',
      description: 'Orchestrates specialized agents and workflows',
      systemPromptName: 'supervisorAgent',
      tools: ['enhancedFileSystem', 'codeSearch', 'memory', 'workflow']
    }, receiverAgentInstance);
    if (agentContext && supervisorAgent) {
      supervisorAgent.setContext(agentContext);
      const agentManager = AgentManager.getInstance();
      agentManager.setSupervisorAgent(supervisorAgent);
      logger.info('Supervisor agent initialized');
    }
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Supervisor agent creation failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Create default operation mode agent
  try {
    const defaultMode = operationModeRegistry.getDefaultMode();
    if (defaultMode) {
      const modeAgent = createModeAgent(defaultMode.id, {
        id: `mode_${defaultMode.id}`,
        name: defaultMode.displayName,
        systemPromptName: defaultMode.id
      });
      if (modeAgent) {
        const agentManager = AgentManager.getInstance();
        agentManager.addAgent(modeAgent);
        logger.info(`Default mode agent (${defaultMode.id}) initialized`);
      }
    }
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Default mode agent creation failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Store agents in global state
  try {
    if (context && receiverAgentInstance && supervisorAgent) {
      context.globalState.update('receiverAgent', receiverAgentInstance);
      context.globalState.update('supervisorAgent', supervisorAgent);
      logger.info('Multi-agent system initialized successfully');
    }
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Storing agents in global state failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // VS Code integrations
  try {
    logger.info('Initializing VS Code integrations...');
    logger.info('✅ Language Model integration ready');
    // Initialize MCP Manager
    const mcpManagerInstance = MCPManager.getInstance();
    if (mcpManagerInstance && typeof mcpManagerInstance.dispose === 'function') {
      context.subscriptions.push(mcpManagerInstance);
    }
    logger.info('✅ Enhanced MCP 2025 initialized');

    // Initialize Agent Tree View
    const agentTreeDataProvider = new AgentTreeDataProvider();
    context.subscriptions.push(
      vscode.window.registerTreeDataProvider('codessaAgentView', agentTreeDataProvider)
    );
    logger.info('✅ Agent tree view initialized');
    const goddessMode = GoddessModeManager.getInstance();
    const goddessDisposable = {
      dispose: () => {
        const manager = goddessMode as { dispose?: () => void };
        if (manager.dispose) {
          manager.dispose();
        }
      }
    };
    context.subscriptions.push(goddessDisposable);
    const config = vscode.workspace.getConfiguration('codessa');
    const autoActivateGoddess = config.get<boolean>('goddess.autoActivate', false);
    if (autoActivateGoddess) {
      goddessMode.activate();
      logger.info('✅ Goddess Mode auto-activated');
    } else {
      logger.info('✅ Goddess Mode initialized (inactive)');
    }
    context.globalState.update('mcpManager', mcpManager);
    context.globalState.update('goddessMode', goddessMode);
    logger.info('🚀 VS Code integrations initialized successfully!');
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: VS Code integrations initialization failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // UI enhancements will be registered later to avoid duplication
  logger.info('UI enhancements will be registered with tree view providers');

  // Advanced memory and intelligence systems
  let quantumMemory, projectIntelligence, crossRepoContext;
  try {
    logger.info('Initializing advanced memory systems...');
    await memoryManager.initializeQuantumMemory(supervisorAgent);
    quantumMemory = memoryManager.getQuantumMemorySystem();
    if (!quantumMemory) {
      throw new Error('Quantum memory system is required for project intelligence');
    }
    projectIntelligence = new ProjectWideIntelligence(
      supervisorAgent,
      ToolRegistry.instance,
      memoryManager,
      quantumMemory
    );
    crossRepoContext = new CrossRepositoryContext(
      supervisorAgent,
      ToolRegistry.instance,
      quantumMemory,
      projectIntelligence
    );
    await crossRepoContext.initialize();
    context.globalState.update('projectIntelligence', projectIntelligence);
    context.globalState.update('crossRepoContext', crossRepoContext);
    context.subscriptions.push({
      dispose: () => {
        projectIntelligence.dispose();
        crossRepoContext.dispose();
      }
    });
    logger.info('🧠 Advanced memory systems initialized successfully!');
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Advanced memory/intelligence systems initialization failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Advanced AI features
  let goddessPersonalityEngine, quantumTool, neuralTool;
  try {
    logger.info('Initializing advanced AI features...');
    const goddessModule = await import('./personality/goddessMode');
    goddessPersonalityEngine = goddessModule.goddessPersonalityEngine;
    logger.info('✅ Goddess Personality Engine initialized');
    const quantumModule = await import('./tools/quantumAnalysisTool');
    quantumTool = new quantumModule.QuantumAnalysisTool();
    ToolRegistry.instance.registerTool(quantumTool);
    logger.info('✅ Quantum Analysis Tool registered');
    const neuralModule = await import('./tools/neuralCodeSynthesisTool');
    neuralTool = new neuralModule.NeuralCodeSynthesisTool();
    ToolRegistry.instance.registerTool(neuralTool);
    logger.info('✅ Neural Code Synthesis Tool registered');
  } catch (e) {
    vscode.window.showErrorMessage('Codessa: Advanced AI features initialization failed: ' + (e instanceof Error ? e.message : String(e)));
  }

  // Register codessa.addAgent command
  context.subscriptions.push(
    vscode.commands.registerCommand('codessa.addAgent', async () => {
      const agentName = await vscode.window.showInputBox({ prompt: 'Enter a name for the new agent' });
      if (agentName) {
        const defaultConfig = await getDefaultModelConfig();
        const newAgentConfig = {
          id: agentName.toLowerCase().replace(/\s+/g, '-'),
          name: agentName,
          description: 'A new agent created by the user.',
          role: 'specialist',
          capabilities: [],
          llmProvider: defaultConfig.provider,
          llmModel: defaultConfig.modelId,
          systemPromptName: 'default',
          tools: [],
        };
        const agentInstance = new Agent(newAgentConfig);
        const agentManager = AgentManager.getInstance();
        agentManager.addAgent(agentInstance);
        vscode.window.showInformationMessage(`Agent '${agentName}' created successfully.`);
      }
    }),

    // Register codessa.openToolDetails command
    vscode.commands.registerCommand('codessa.openToolDetails', (toolId: string) => {
      try {
        // Forward to openToolDetailsPanel which already has the implementation
        vscode.commands.executeCommand('codessa.openToolDetailsPanel', toolId);
      } catch (error) {
        const errorMessage = `Failed to open tool details: ${error instanceof Error ? error.message : String(error)}`;
        logger.error(errorMessage);
        vscode.window.showErrorMessage(errorMessage);
      }
    }),

    // Register codessa.openToolDetailsPanel command
    vscode.commands.registerCommand('codessa.openToolDetailsPanel', (toolId: string) => {
      try {
        const tool = ToolRegistry.getTool(toolId);
        if (!tool) {
          vscode.window.showErrorMessage(`Tool with ID '${toolId}' not found.`);
          return;
        }
        ToolDetailsPanel.createOrShow(context.extensionUri, tool);
      } catch (error) {
        const errorMessage = `Failed to open tool details: ${error instanceof Error ? error.message : String(error)}`;
        logger.error(errorMessage);
        vscode.window.showErrorMessage(errorMessage);
      }
    }),

    // Chat view provider will be registered later to avoid duplication
  );
  const advancedCommands = [
    vscode.commands.registerCommand('codessa.activateGoddessMode', async () => {
      try {
        // Update goddess personality to be more active
        goddessPersonalityEngine.updatePersonality({
          adaptiveLevel: 90,
          emotionalIntelligence: 95,
          motivationalStyle: 'adaptive',
          wisdomLevel: 95,
          creativityLevel: 90
        });

        vscode.window.showInformationMessage(
          '✨ Goddess Mode Activated! I am now Codessa, your divine coding companion with enhanced emotional intelligence and adaptive personality! 👑',
          'Chat with Goddess', 'View Personality'
        ).then(selection => {
          if (selection === 'Chat with Goddess') {
            vscode.commands.executeCommand('codessa.openChatView');
          } else if (selection === 'View Personality') {
            const personality = goddessPersonalityEngine.getPersonality();
            vscode.window.showInformationMessage(
              `Goddess Personality: Adaptive Level ${personality.adaptiveLevel}%, EQ ${personality.emotionalIntelligence}%, Wisdom ${personality.wisdomLevel}%`
            );
          }
        });
      } catch (error) {
        logger.error('Error activating Goddess Mode:', error);
        vscode.window.showErrorMessage('Failed to activate Goddess Mode');
      }
    }),

    vscode.commands.registerCommand('codessa.quantumAnalysis', async () => {
      try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
          vscode.window.showWarningMessage('Please open a file to perform quantum analysis');
          return;
        }

        const code = editor.document.getText();
        const result = await quantumTool.execute('quantum_pattern_recognition', {
          action: 'quantum_pattern_recognition',
          code
        });

        if (result.success && result.output) {
          const output = result.output;
          vscode.window.showInformationMessage(
            `🔬 Quantum Analysis Complete: Found ${output.patterns?.length || 0} quantum patterns with ${(output.confidence * 100).toFixed(1)}% confidence`,
            'View Details', 'Run Parallel Test'
          ).then(selection => {
            if (selection === 'View Details') {
              logger.info('Quantum Analysis Results:', result);
              vscode.window.showInformationMessage('Quantum analysis details logged to output');
            } else if (selection === 'Run Parallel Test') {
              vscode.commands.executeCommand('codessa.parallelUniverseTest');
            }
          });
        } else {
          vscode.window.showErrorMessage(`Quantum analysis failed: ${result.error || 'Unknown error'}`);
        }
      } catch (error) {
        logger.error('Error in quantum analysis:', error);
        vscode.window.showErrorMessage('Quantum analysis failed');
      }
    }),

    vscode.commands.registerCommand('codessa.parallelUniverseTest', async () => {
      try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
          vscode.window.showWarningMessage('Please open a file to test in parallel universes');
          return;
        }

        const code = editor.document.getText();
        const result = await quantumTool.execute('parallel_universe_testing', {
          action: 'parallel_universe_testing',
          code
        });

        if (result.success && result.output) {
          const output = result.output;
          vscode.window.showInformationMessage(
            `🌌 Parallel Universe Testing: Tested in ${output.universes?.length || 0} universes. Quantum interference: ${output.quantumInterference ? 'Detected' : 'None'}`,
            'View Timeline', 'See Predictions'
          ).then(selection => {
            if (selection === 'View Timeline') {
              logger.info('Parallel Universe Test Results:', result);
              vscode.window.showInformationMessage('Timeline analysis logged to output');
            } else if (selection === 'See Predictions') {
              const mitigation = output.mitigationStrategies?.slice(0, 3).join('\n• ') || 'No strategies available';
              vscode.window.showInformationMessage(`Mitigation Strategies:\n• ${mitigation}`);
            }
          });
        }
      } catch (error) {
        logger.error('Error in parallel universe testing:', error);
        vscode.window.showErrorMessage('Parallel universe testing failed');
      }
    }),

    vscode.commands.registerCommand('codessa.neuralCodeSynthesis', async () => {
      try {
        const prompt = await vscode.window.showInputBox({
          prompt: 'Enter your code generation request',
          placeHolder: 'e.g., Create a function to sort an array using neural networks'
        });

        if (!prompt) return;

        const result = await neuralTool.execute('generate_code', {
          action: 'generate_code',
          prompt
        });

        const editor = vscode.window.activeTextEditor;
        if (editor && result.success && result.output) {
          const output = result.output;
          if (output.generatedCode) {
            const position = editor.selection.active;
            await editor.edit(editBuilder => {
              editBuilder.insert(position, `\n// Neural-generated code (Creativity: ${output.creativityScore}%)\n${output.generatedCode}\n`);
            });

            vscode.window.showInformationMessage(
              `🧠 Neural Code Generated! Creativity Score: ${output.creativityScore}%, Neural Path: ${output.neuralPath?.length || 0} layers`,
              'View Insights'
            ).then(selection => {
              if (selection === 'View Insights') {
                const insights = output.consciousnessInsights?.join('\n• ') || 'No insights available';
                vscode.window.showInformationMessage(`Neural Insights:\n• ${insights}`);
              }
            });
          }
        } else {
          vscode.window.showErrorMessage(`Neural code synthesis failed: ${result.error || 'Unknown error'}`);
        }
      } catch (error) {
        logger.error('Error in neural code synthesis:', error);
        vscode.window.showErrorMessage('Neural code synthesis failed');
      }
    }),

    vscode.commands.registerCommand('codessa.timeTravelDebug', async () => {
      try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
          vscode.window.showWarningMessage('Please open a file to perform time-travel debugging');
          return;
        }

        const timeTravelTool = ToolRegistry.instance.getTool('timeTravelDebug');
        if (!timeTravelTool) {
          vscode.window.showErrorMessage('Time-travel debugging tool not available');
          return;
        }

        const result = await timeTravelTool.execute('predictFutureIssues', {
          filePath: editor.document.uri.fsPath,
          timeHorizon: '3-months'
        });

        if (result.success) {
          const predictions = result.output;
          vscode.window.showInformationMessage(
            `⏰ Time-Travel Analysis: Risk Score ${predictions.combinedRiskScore}%, Found ${predictions.traditionalPredictions?.length || 0} traditional + ${predictions.quantumPredictions?.length || 0} quantum predictions`,
            'View Timeline', 'See Predictions'
          ).then(selection => {
            if (selection === 'View Timeline') {
              logger.info('Time-travel predictions:', predictions);
              vscode.window.showInformationMessage('Timeline analysis logged to output');
            } else if (selection === 'See Predictions') {
              const mitigation = predictions.mitigationStrategies?.slice(0, 3).join('\n• ') || 'No strategies available';
              vscode.window.showInformationMessage(`Mitigation Strategies:\n• ${mitigation}`);
            }
          });
        }
      } catch (error) {
        logger.error('Error in time-travel debugging:', error);
        vscode.window.showErrorMessage('Time-travel debugging failed');
      }
    }),

    // Advanced Workflow Commands
    vscode.commands.registerCommand('codessa.createAdvancedWorkflow', async () => {
      try {
        const workflowType = await vscode.window.showQuickPick([
          {
            label: '✨ Goddess-Guided Workflow',
            description: 'Enhanced with divine coding wisdom and emotional intelligence',
            detail: 'Perfect for complex development tasks requiring guidance and support'
          },
          {
            label: '🔬 Quantum-Enhanced Workflow',
            description: 'Leverages quantum-inspired algorithms for superior analysis',
            detail: 'Ideal for code analysis and pattern recognition tasks'
          },
          {
            label: '🧠 Neural Synthesis Workflow',
            description: 'Brain-inspired code generation and optimization',
            detail: 'Best for code generation and creative problem-solving'
          },
          {
            label: '⏰ Time-Travel Debugging Workflow',
            description: 'Predicts and prevents future issues',
            detail: 'Essential for maintaining code quality and preventing bugs'
          },
          {
            label: '🚀 Ultimate Advanced Workflow',
            description: 'Combines all advanced features for maximum power',
            detail: 'The most advanced workflow with all AI capabilities enabled'
          }
        ], {
          placeHolder: 'Select the type of advanced workflow to create',
          title: '🚀 Create Advanced Workflow'
        });

        if (!workflowType) return;

        const workflowName = await vscode.window.showInputBox({
          prompt: 'Enter a name for your advanced workflow',
          placeHolder: 'My Advanced Workflow',
          title: '✨ Name Your Advanced Workflow'
        });

        if (!workflowName) return;

        // Create advanced workflow based on selection
        const { workflowRegistry } = await import('./agents/workflows/workflowRegistry');
        const { Workflow } = await import('./agents/workflows/workflowEngine');

        // Create an advanced workflow definition
        const advancedWorkflow = {
          id: `advanced-${Date.now()}`,
          name: workflowName,
          description: `Advanced workflow: ${workflowType.label}`,
          version: '1.0.0',
          steps: [],
          inputs: [],
          outputs: [],
          startStepId: 'advanced-start'
        };

        // Create a graph definition for registration
        const graphDefinition = {
          id: advancedWorkflow.id,
          name: advancedWorkflow.name,
          description: advancedWorkflow.description,
          version: advancedWorkflow.version,
          nodes: [],
          edges: [],
          startNodeId: 'advanced-start',
          operationMode: workflowType.label.toLowerCase().replace(/\s+/g, '-') as OperationMode,
          tags: ['advanced', workflowType.label.toLowerCase()]
        };

        // Create and configure the workflow
        const workflow = new Workflow(advancedWorkflow);

        // Enable advanced features based on selection
        if (workflowType.label.includes('Goddess')) {
          workflow.enableAdvancedFeatures({ goddessMode: true, adaptivePersonality: true });
        } else if (workflowType.label.includes('Quantum')) {
          workflow.enableAdvancedFeatures({ quantumAnalysis: true });
        } else if (workflowType.label.includes('Neural')) {
          workflow.enableAdvancedFeatures({ neuralSynthesis: true });
        } else if (workflowType.label.includes('Time-Travel')) {
          workflow.enableAdvancedFeatures({ timeTravelDebugging: true });
        } else if (workflowType.label.includes('Ultimate')) {
          workflow.enableAdvancedFeatures({
            goddessMode: true,
            quantumAnalysis: true,
            neuralSynthesis: true,
            timeTravelDebugging: true,
            adaptivePersonality: true
          });
        }

        // Register the workflow
        workflowRegistry.registerWorkflow(graphDefinition);

        vscode.window.showInformationMessage(
          `🚀 Advanced workflow "${workflowName}" created successfully with ${workflowType.label} capabilities!`,
          'Open Workflow Panel', 'Test Workflow'
        ).then(selection => {
          if (selection === 'Open Workflow Panel') {
            vscode.commands.executeCommand('codessa.openWorkflowPanel');
          } else if (selection === 'Test Workflow') {
            vscode.commands.executeCommand('codessa.runAdvancedWorkflow', advancedWorkflow.id);
          }
        });
      } catch (error) {
        logger.error('Error creating advanced workflow:', error);
        vscode.window.showErrorMessage('Failed to create advanced workflow');
      }
    }),
    vscode.commands.registerCommand('codessa.runAdvancedWorkflow', async (workflowId?: string) => {
      try {
        if (!workflowId) {
          // Show workflow picker
          const { workflowRegistry } = await import('./agents/workflows/workflowRegistry');
          const advancedWorkflows = workflowRegistry.getAdvancedWorkflows();
          if (advancedWorkflows.length === 0) {
            vscode.window.showInformationMessage(
              'No advanced workflows found. Create one first!',
              'Create Advanced Workflow'
            ).then(selection => {
              if (selection) {
                vscode.commands.executeCommand('codessa.createAdvancedWorkflow');
              }
            });
            return;
          }
          // Define workflow interface to match the expected structure
          interface WorkflowWithMetadata {
            id: string;
            name: string;
            description: string;
            metadata?: {
              advancedFeatures?: Record<string, boolean>;
              revolutionaryFeatures?: Record<string, boolean>;
              [key: string]: any;
            };
          }

          const selectedWorkflow = await vscode.window.showQuickPick(
            (advancedWorkflows as unknown as WorkflowWithMetadata[]).map((wf) => {
              const metadata = wf.metadata || {};
              const features = metadata.advancedFeatures || metadata.revolutionaryFeatures || {};
              return {
                label: `${features.goddessMode ? '✨' : ''}${features.quantumAnalysis ? '🔬' : ''}${features.neuralSynthesis ? '🧠' : ''}${features.timeTravelDebugging ? '⏰' : ''} ${wf.name}`,
                description: wf.description || '',
                detail: `Features: ${Object.entries(features)
                  .filter(([, enabled]) => Boolean(enabled))
                  .map(([feature]) => feature)
                  .join(', ')}`,
                workflowId: wf.id
              };
            }),
            {
              placeHolder: 'Select an advanced workflow to run',
              title: '🚀 Run Advanced Workflow'
            }
          );
          if (!selectedWorkflow) return;
          workflowId = selectedWorkflow.workflowId;
        }
        vscode.window.showInformationMessage(
          '🚀 Running advanced workflow with enhanced AI capabilities...',
          'View Progress'
        ).then(selection => {
          if (selection === 'View Progress') {
            vscode.commands.executeCommand('codessa.openWorkflowPanel');
          }
        });
      } catch (error) {
        logger.error('Error running advanced workflow:', error);
        vscode.window.showErrorMessage('Failed to run advanced workflow');
      }
    })
  ];
  
  // Register all advanced commands
  if (context) {
    advancedCommands.forEach(command => context.subscriptions.push(command));
  }

  // Register ChatViewProvider for the chat sidebar
  let chatViewProvider: ChatViewProvider | undefined;
  try {
    chatViewProvider = new ChatViewProvider(context.extensionUri, context);
    const chatViewProviderDisposable = vscode.window.registerWebviewViewProvider(
      'codessa.chatView',
      chatViewProvider,
      {
        webviewOptions: { retainContextWhenHidden: true }
      }
    );

    context.subscriptions.push(chatViewProviderDisposable);
    logger.info('✅ Chat view provider registered successfully');
  } catch (error) {
    logger.error('❌ Failed to register chat view provider:', error);
    vscode.window.showErrorMessage(`Failed to register chat view: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  // Refresh agent references in ChatViewProvider after agents are set up
  if (chatViewProvider && receiverAgentInstance && supervisorAgent) {
    try {
      chatViewProvider.refreshAgentReferences();
      logger.info('✅ ChatViewProvider agent references refreshed');
    } catch (error) {
      logger.error('❌ Failed to refresh ChatViewProvider agent references:', error);
    }
  }

  // Register tree view data providers
  const { registerAgentTreeView } = await import('./ui/agents/agentTree');
  const { registerToolsTreeView } = await import('./ui/tools/toolsView');

  registerAgentTreeView(context);
  registerToolsTreeView(context);
  logger.info('Tree view data providers registered successfully');

  // Register terminal view
  const terminalView = new TerminalView(context.extensionUri);
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(
      TerminalView.viewType,
      terminalView
    )
  );

  // Initialize file change tracking
  const fileChangeTracker = FileChangeTracker.getInstance(context);
  context.subscriptions.push(fileChangeTracker);

  // Register file change tracking commands
  context.subscriptions.push(
    vscode.commands.registerCommand('codessa.showFileChanges', async () => {
      try {
        await fileChangeTracker.showFileChanges();
      } catch (error) {
        logger.error('Failed to show file changes:', error);
        vscode.window.showErrorMessage('Failed to show file changes');
      }
    }),

    vscode.commands.registerCommand('codessa.openFileChangePanel', () => {
      try {
        FileChangePanel.createOrShow(context.extensionUri, context);
      } catch (error) {
        logger.error('Failed to open file change panel:', error);
        vscode.window.showErrorMessage('Failed to open file change panel');
      }
    }),

    vscode.commands.registerCommand('codessa.refreshFileChanges', async () => {
      try {
        await fileChangeTracker.refreshChanges();
        vscode.window.showInformationMessage('File changes refreshed');
      } catch (error) {
        logger.error('Failed to refresh file changes:', error);
        vscode.window.showErrorMessage('Failed to refresh file changes');
      }
    })
  );

  // Register settings commands
  context.subscriptions.push(
    vscode.commands.registerCommand('codessa.openSettings', async () => {
      try {
        await AllSettingsPanel.createOrShow(
          context,
          context.extensionUri,
          memoryManager,
          providerManager.getInstance()
        );
      } catch (error) {
        logger.error('Failed to open settings panel:', error);
        vscode.window.showErrorMessage('Failed to open settings panel');
      }
    }),

    // Debug command to check provider status
    vscode.commands.registerCommand('codessa.debugProviderStatus', async () => {
      try {
        const config = vscode.workspace.getConfiguration('codessa.llm');
        const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];
        const suppressErrors = vscode.workspace.getConfiguration('codessa').get<boolean>('suppressProviderErrors', true);

        const message = `Provider Status Debug:
- Enabled Providers: ${enabledProviders.join(', ')}
- Suppress Provider Errors: ${suppressErrors}
- LM Studio Enabled: ${enabledProviders.includes('lmstudio')}`;

        vscode.window.showInformationMessage(message);
        logger.info(message);
      } catch (error) {
        logger.error('Failed to get provider status:', error);
        vscode.window.showErrorMessage('Failed to get provider status');
      }
    }),
    vscode.commands.registerCommand('codessa.openSettingsView', async () => {
      try {
        await AllSettingsPanel.createOrShow(
          context,
          context.extensionUri,
          memoryManager,
          providerManager.getInstance()
        );
      } catch (error) {
        logger.error('Failed to open settings view:', error);
        vscode.window.showErrorMessage('Failed to open settings view');
      }
    })
  );
  logger.info('Settings commands registered successfully');

  // Register mode selector command
  context.subscriptions.push(
    vscode.commands.registerCommand('codessa.openModeSelector', () => {
      try {
        openModeSelector(context);
      } catch (error) {
        logger.error('Failed to open mode selector:', error);
        vscode.window.showErrorMessage('Failed to open mode selector');
      }
    })
  );
  logger.info('Mode selector command registered successfully');

  context.globalState.update('goddessPersonalityEngine', goddessPersonalityEngine);
  context.globalState.update('quantumAnalysisTool', quantumTool);
  context.globalState.update('neuralCodeSynthesisTool', neuralTool);
  logger.info('🚀 Advanced AI features initialized successfully!');
}
