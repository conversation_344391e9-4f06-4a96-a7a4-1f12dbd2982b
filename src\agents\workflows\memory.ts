/**
 * Memory-Enhanced Workflow and interfaces for Codessa workflows
 *
 * This module provides workflows for memory-enhanced operations and re-exports memory types and interfaces.
 * - Storing and retrieving information from memory
 * - Using memory to enhance responses
 * - Managing short-term and long-term memory
 */

import { ITool } from '../../tools/tool.ts.backup';
import { Agent } from '../agentUtilities/agent';
import { Codessa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';
import { workflowRegistry } from './workflowRegistry';
import { logger } from '../../logger';
import { StructuredTool } from './corePolyfill';
// Import the memoryManager singleton for workflow memory operations
import { memoryManager } from '../../memory/memoryManager';
// Import CodessaMemoryProvider for advanced memory functionality
import { CodessaMemoryProvider } from '../../memory/codessa/codessaMemory';


// Re-export memory types from the memory module
export type { MemoryEntry, MemorySearchOptions } from '../../memory/types';

// Re-export the CodessaMemoryProvider from its new location
export { codessaMemoryProvider as codessaMemoryManager, CodessaMemoryProvider } from '../../memory/codessa/codessaMemory';

/**
 * Create a Memory-Enhanced workflow
 */
export function createMemoryEnhancedWorkflow(
  id: string,
  name: string,
  description: string,
  memoryAgent: Agent,
  tools: ITool[] = []
): GraphDefinition {
  logger.info(`Creating Memory-Enhanced workflow: ${name}`);

  // Initialize memory provider for this workflow
  const memoryProvider = new CodessaMemoryProvider();

  // Create nodes with memory integration
  const inputNode = Codessa.createInputNode('input', 'Input');

  // Create agent nodes with memory functionality embedded in the agent
  const queryAnalysisNode = Codessa.createAgentNode('query-analysis', 'Query Analysis', memoryAgent);
  const memoryRetrievalNode = Codessa.createAgentNode('memory-retrieval', 'Memory Retrieval', memoryAgent);
  const contextEnhancementNode = Codessa.createAgentNode('context-enhancement', 'Context Enhancement', memoryAgent);
  const responseGenerationNode = Codessa.createAgentNode('response-generation', 'Response Generation', memoryAgent);
  const memorySaveNode = Codessa.createAgentNode('memory-save', 'Memory Save', memoryAgent);

  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Initialize memory functionality for this workflow
  initializeWorkflowMemory(id, memoryProvider);

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },
    { name: 'query-to-memory', source: 'query-analysis', target: 'memory-retrieval', type: 'default' },
    { name: 'memory-to-context', source: 'memory-retrieval', target: 'context-enhancement', type: 'default' },
    { name: 'context-to-response', source: 'context-enhancement', target: 'response-generation', type: 'default' },
    { name: 'response-to-memory', source: 'response-generation', target: 'memory-save', type: 'default' },
    { name: 'memory-to-output', source: 'memory-save', target: 'output', type: 'default' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect context enhancement to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `context-to-tool-${index}`,
        source: 'context-enhancement',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to response generation
      edges.push({
        name: `tool-${index}-to-response`,
        source: `tool-${index}`,
        target: 'response-generation',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      queryAnalysisNode,
      memoryRetrievalNode,
      contextEnhancementNode,
      responseGenerationNode,
      memorySaveNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'memory' as OperationMode,
    tags: ['memory', 'context', 'retrieval']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a specialized Conversation Memory workflow
 */
export function createConversationMemoryWorkflow(
  id: string,
  name: string,
  description: string,
  memoryAgent: Agent
): GraphDefinition {
  logger.info(`Creating Conversation Memory workflow: ${name}`);

  // Initialize conversation memory provider
  const conversationMemoryProvider = new CodessaMemoryProvider();

  // Create nodes with conversation memory integration
  const inputNode = Codessa.createInputNode('input', 'Input');
  const messageAnalysisNode = Codessa.createAgentNode('message-analysis', 'Message Analysis', memoryAgent);
  const conversationHistoryNode = Codessa.createAgentNode('conversation-history', 'Conversation History', memoryAgent);
  const relevanceFilteringNode = Codessa.createAgentNode('relevance-filtering', 'Relevance Filtering', memoryAgent);
  const contextIntegrationNode = Codessa.createAgentNode('context-integration', 'Context Integration', memoryAgent);
  const responseGenerationNode = Codessa.createAgentNode('response-generation', 'Response Generation', memoryAgent);
  const conversationUpdateNode = Codessa.createAgentNode('conversation-update', 'Conversation Update', memoryAgent);

  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Initialize conversation memory for this workflow
  initializeConversationMemory(id, conversationMemoryProvider);

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-message', source: 'input', target: 'message-analysis', type: 'default' },
    { name: 'message-to-history', source: 'message-analysis', target: 'conversation-history', type: 'default' },
    { name: 'history-to-filtering', source: 'conversation-history', target: 'relevance-filtering', type: 'default' },
    { name: 'filtering-to-integration', source: 'relevance-filtering', target: 'context-integration', type: 'default' },
    { name: 'integration-to-response', source: 'context-integration', target: 'response-generation', type: 'default' },
    { name: 'response-to-update', source: 'response-generation', target: 'conversation-update', type: 'default' },
    { name: 'update-to-output', source: 'conversation-update', target: 'output', type: 'default' }
  ];

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      messageAnalysisNode,
      conversationHistoryNode,
      relevanceFilteringNode,
      contextIntegrationNode,
      responseGenerationNode,
      conversationUpdateNode,
      outputNode
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'memory' as OperationMode,
    tags: ['memory', 'conversation', 'context']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Initialize workflow memory using both memory systems
 */
function initializeWorkflowMemory(workflowId: string, memoryProvider: CodessaMemoryProvider): void {
  logger.info(`Initializing memory for workflow: ${workflowId}`);

  // Set up memory context for the workflow
  memoryManager.addMemory({
    content: `Workflow initialized: ${workflowId}`,
    metadata: {
      workflowId,
      timestamp: Date.now(),
      source: 'system',
      type: 'conversation'
    }
  }).catch(error => {
    logger.error('Error initializing workflow memory:', error);
  });

  // Initialize memory provider context
  memoryProvider.addMemory({
    content: `Workflow ${workflowId} started`,
    metadata: {
      workflowId,
      source: 'system',
      type: 'conversation'
    }
  }).catch(error => {
    logger.error('Error initializing memory provider:', error);
  });
}

/**
 * Initialize conversation memory using both memory systems
 */
function initializeConversationMemory(workflowId: string, memoryProvider: CodessaMemoryProvider): void {
  logger.info(`Initializing conversation memory for workflow: ${workflowId}`);

  // Set up conversation context
  memoryManager.addMemory({
    content: `Conversation workflow initialized: ${workflowId}`,
    metadata: {
      workflowId,
      timestamp: Date.now(),
      source: 'system',
      type: 'conversation'
    }
  }).catch(error => {
    logger.error('Error initializing conversation memory:', error);
  });

  // Initialize conversation provider
  memoryProvider.addMemory({
    content: `Conversation workflow ${workflowId} ready`,
    metadata: {
      workflowId,
      source: 'system',
      type: 'conversation'
    }
  }).catch(error => {
    logger.error('Error initializing conversation memory provider:', error);
  });
}

/**
 * Enhanced memory operations using both memoryManager and CodessaMemoryProvider
 */
export class WorkflowMemoryManager {
  private memoryProvider: CodessaMemoryProvider;

  constructor() {
    this.memoryProvider = new CodessaMemoryProvider();
  }

  /**
   * Initialize memory for a workflow session
   */
  async initializeWorkflowMemory(workflowId: string, sessionId: string): Promise<void> {
    logger.info(`Initializing memory for workflow ${workflowId}, session ${sessionId}`);

    // Initialize session in memoryManager
    await memoryManager.addMemory({
      content: `Workflow session started: ${workflowId}`,
      metadata: {
        workflowId,
        sessionId,
        timestamp: Date.now(),
        source: 'system',
        type: 'conversation'
      }
    });

    // Initialize session in CodessaMemoryProvider
    await this.memoryProvider.addMemory({
      content: `Session ${sessionId} started for workflow ${workflowId}`,
      metadata: {
        workflowId,
        sessionId,
        source: 'system',
        type: 'conversation'
      }
    });
  }

  /**
   * Store workflow step results with enhanced memory features
   */
  async storeStepResult(
    workflowId: string,
    stepId: string,
    input: any,
    output: any,
    metadata: any = {}
  ): Promise<void> {
    // Store in memoryManager with structured data
    await memoryManager.addMemory({
      content: JSON.stringify({ input, output }),
      metadata: {
        workflowId,
        stepId,
        timestamp: Date.now(),
        source: 'system',
        type: 'conversation',
        ...metadata
      }
    });

    // Store in CodessaMemoryProvider with enhanced features
    await this.memoryProvider.addMemory({
      content: `Step ${stepId}: ${JSON.stringify(output)}`,
      metadata: {
        workflowId,
        stepId,
        source: 'system',
        type: 'conversation',
        ...metadata
      }
    });
  }

  /**
   * Retrieve workflow context using both memory systems
   */
  async getWorkflowContext(workflowId: string, query: string): Promise<any> {
    // Get context from memoryManager
    const memoryContext = await memoryManager.searchMemories({
      query,
      limit: 20,
      filter: { type: 'conversation' }
    });

    // Get enhanced context from CodessaMemoryProvider
    const enhancedContext = await this.memoryProvider.searchMemories({
      query,
      limit: 10,
      filter: { type: 'conversation' }
    });

    return {
      memoryContext,
      enhancedContext,
      combinedInsights: this.combineMemoryInsights(memoryContext, enhancedContext)
    };
  }

  /**
   * Combine insights from both memory systems
   */
  private combineMemoryInsights(memoryContext: any[], enhancedContext: any): any {
    return {
      totalMemories: memoryContext.length,
      enhancedFeatures: enhancedContext,
      patterns: this.extractPatterns(memoryContext),
      recommendations: this.generateRecommendations(memoryContext, enhancedContext)
    };
  }

  /**
   * Extract patterns from memory data
   */
  private extractPatterns(memories: any[]): any {
    // Analyze patterns in workflow execution
    const stepFrequency = memories.reduce((acc, memory) => {
      const stepId = memory.metadata?.stepId;
      if (stepId) {
        acc[stepId] = (acc[stepId] || 0) + 1;
      }
      return acc;
    }, {});

    return { stepFrequency };
  }

  /**
   * Generate recommendations based on memory analysis
   */
  private generateRecommendations(memoryContext: any[], enhancedContext: any): string[] {
    const recommendations: string[] = [];

    if (memoryContext.length > 100) {
      recommendations.push('Consider archiving older workflow memories');
    }

    if (enhancedContext && enhancedContext.patterns) {
      recommendations.push('Enhanced patterns detected - optimize workflow based on insights');
    }

    return recommendations;
  }
}

// Export singleton instance for use in workflows
export const workflowMemoryManager = new WorkflowMemoryManager();
