import * as vscode from 'vscode';
import * as path from 'path';

export class BaseTreeItem extends vscode.TreeItem {
    constructor(
        label: string,
        collapsibleState: vscode.TreeItemCollapsibleState
    ) {
        super(label, collapsibleState);
    }

    public init(options: {
        id?: string;
        tooltip?: string;
        description?: string;
        contextValue?: string;
        command?: vscode.Command;
    }): void {
        if (options.id) this.id = options.id;
        if (options.tooltip) this.tooltip = options.tooltip;
        if (options.description) this.description = options.description;
        if (options.contextValue) this.contextValue = options.contextValue;
        if (options.command) this.command = options.command;
    }

    protected setIcon(iconName: string, context: string): void {
        const resourcesPath = path.join(context, '..', 'resources');
        this.iconPath = {
            light: vscode.Uri.file(path.join(resourcesPath, 'light', iconName)),
            dark: vscode.Uri.file(path.join(resourcesPath, 'dark', iconName))
        };
    }
}
