"use strict";
/**
 * Quantum Memory System - Revolutionary memory architecture
 *
 * Implements temporal memory, contextual retrieval, collaborative memory,
 * predictive memory, and memory analytics while integrating with existing
 * Codessa memory infrastructure.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuantumMemorySystem = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
class QuantumMemorySystem {
    memoryManager;
    vectorMemory;
    supervisorAgent;
    // Quantum memory stores
    temporalStore = new Map();
    contextualIndex = new Map();
    collaborativeShares = new Map();
    predictiveInsights = new Map();
    // Performance optimization
    queryCache = new Map();
    cacheExpiry = 300000; // 5 minutes
    maxCacheSize = 1000;
    // Analytics tracking
    analytics;
    queryHistory = [];
    constructor(memoryManager, vectorMemory, supervisorAgent) {
        this.memoryManager = memoryManager;
        this.vectorMemory = vectorMemory;
        this.supervisorAgent = supervisorAgent;
        this.analytics = this.initializeAnalytics();
        this.setupEventListeners();
        this.startPredictiveEngine();
    }
    /**
       * Initialize the quantum memory system
       */
    async initialize() {
        try {
            logger_1.logger.info('Initializing Quantum Memory System...');
            // Load existing temporal memories
            await this.loadTemporalMemories();
            // Load collaborative shares
            await this.loadCollaborativeShares();
            // Initialize predictive engine
            await this.initializePredictiveEngine();
            // Build contextual index
            await this.buildContextualIndex();
            logger_1.logger.info('Quantum Memory System initialized successfully');
        }
        catch (error) {
            logger_1.logger.error(`Failed to initialize Quantum Memory System: ${error}`);
            throw error;
        }
    }
    /**
       * Store temporal memory with version tracking
       */
    async storeTemporalMemory(content, context, changeType = 'create', metadata = {}) {
        const id = `temporal_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        // Find previous version if updating
        let previousVersionId;
        const existingVersions = this.temporalStore.get(context.filePath) || [];
        if (changeType === 'update' && existingVersions.length > 0) {
            const lastVersion = existingVersions[existingVersions.length - 1];
            previousVersionId = lastVersion.id;
            lastVersion.nextVersionId = id;
        }
        const temporalEntry = {
            id,
            content,
            timestamp: Date.now(),
            version: existingVersions.length + 1,
            previousVersionId,
            changeType,
            context,
            metadata: {
                tags: [],
                ...metadata
            }
        };
        // Store in temporal store
        if (!this.temporalStore.has(context.filePath)) {
            this.temporalStore.set(context.filePath, []);
        }
        const temporalEntries = this.temporalStore.get(context.filePath);
        if (temporalEntries) {
            temporalEntries.push(temporalEntry);
        }
        // Store in regular memory system for compatibility
        await this.memoryManager.addMemory({
            content: JSON.stringify(temporalEntry),
            metadata: {
                source: 'temporal',
                type: 'semantic',
                tags: ['temporal', changeType, ...metadata.tags || []]
            }
        });
        // Update contextual index
        this.updateContextualIndex(temporalEntry);
        // Update analytics
        this.analytics.temporalMemories++;
        this.analytics.totalMemories++;
        logger_1.logger.info(`Stored temporal memory: ${id} (${changeType})`);
        return id;
    }
    /**
       * Advanced contextual memory retrieval
       */
    async retrieveContextualMemories(query) {
        const cacheKey = this.generateQueryCacheKey(query);
        // Check cache first
        const cached = this.getFromCache(cacheKey);
        if (cached && Array.isArray(cached)) {
            return cached;
        }
        const startTime = Date.now();
        const results = [];
        try {
            // 1. Get base memories from existing system
            const baseMemories = await this.memoryManager.searchMemories({
                query: query.query,
                limit: query.maxResults || 20
            });
            // 1.5. Enhance with vector memory search for semantic similarity
            try {
                const vectorResults = await this.vectorMemory.searchSimilarMemories(query.query, query.maxResults || 20);
                logger_1.logger.debug(`Vector memory found ${vectorResults.length} additional semantic matches`);
                // Vector results would be merged with base memories in a full implementation
            }
            catch (error) {
                logger_1.logger.debug(`Vector memory search failed: ${error}`);
            }
            // 2. Convert to temporal entries and apply contextual filtering
            const temporalMemories = baseMemories.map(memory => this.convertToTemporalEntry(memory));
            const contextualResults = await this.applyContextualFiltering(temporalMemories, query);
            // 3. Apply temporal constraints
            const temporalResults = this.applyTemporalConstraints(contextualResults, query.temporalConstraints);
            // 4. Enhance with quantum insights
            const enhancedResults = await this.enhanceWithQuantumInsights(temporalResults, query);
            // 5. Sort by relevance and context
            const sortedResults = this.sortByRelevanceAndContext(enhancedResults, query);
            results.push(...sortedResults.slice(0, query.maxResults || 10));
            // Cache the results
            this.setCache(cacheKey, results);
            // Update analytics
            const retrievalTime = Date.now() - startTime;
            this.updateRetrievalAnalytics(query.query, results.length, retrievalTime);
            logger_1.logger.info(`Retrieved ${results.length} contextual memories in ${retrievalTime}ms`);
            return results;
        }
        catch (error) {
            logger_1.logger.error(`Contextual memory retrieval failed: ${error}`);
            return [];
        }
    }
    /**
       * Generate predictive memory insights
       */
    async generatePredictiveInsights(context) {
        try {
            // Analyze recent memory patterns
            const recentMemories = await this.getRecentMemories(24 * 60 * 60 * 1000); // Last 24 hours
            // Use AI to identify patterns and predict needs
            const prompt = this.buildPredictivePrompt(recentMemories, context);
            const result = await this.supervisorAgent.run({
                prompt,
                mode: 'ask'
            });
            if (result.success && result.output) {
                const insights = this.parsePredictiveInsights(result.output);
                // Store insights
                for (const insight of insights) {
                    this.predictiveInsights.set(insight.id, insight);
                }
                this.analytics.predictiveInsights += insights.length;
                logger_1.logger.info(`Generated ${insights.length} predictive insights`);
                return insights;
            }
            return [];
        }
        catch (error) {
            logger_1.logger.error(`Failed to generate predictive insights: ${error}`);
            return [];
        }
    }
    /**
       * Share memory collaboratively
       */
    async shareMemory(memoryId, sharedWith, permissions = 'read', context = '', expiresIn) {
        const shareId = `share_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const share = {
            id: shareId,
            memoryId,
            sharedBy: 'current-user', // In real implementation, get from user context
            sharedWith,
            permissions,
            sharedAt: Date.now(),
            expiresAt: expiresIn ? Date.now() + expiresIn : undefined,
            context
        };
        this.collaborativeShares.set(shareId, share);
        this.analytics.collaborativeShares++;
        logger_1.logger.info(`Shared memory ${memoryId} with ${sharedWith.length} users`);
        return shareId;
    }
    /**
       * Get memory analytics
       */
    getAnalytics() {
        // Update real-time analytics
        this.updateAnalytics();
        return { ...this.analytics };
    }
    /**
       * Visualize memory connections (Enhanced for Phase 4)
       */
    async generateMemoryVisualization(centerMemoryId) {
        try {
            const memories = centerMemoryId
                ? await this.getRelatedMemories(centerMemoryId, 50)
                : Array.from(this.temporalStore.values()).flat().slice(0, 100);
            const nodes = memories.map(memory => ({
                id: memory.id,
                label: memory.content.substring(0, 50) + '...',
                type: memory.changeType,
                timestamp: memory.timestamp,
                context: memory.context.filePath,
                size: this.calculateNodeSize(memory),
                color: this.getNodeColor(memory.changeType),
                importance: this.calculateMemoryImportance(memory)
            }));
            const edges = this.generateMemoryConnections(memories);
            // Add clustering information
            const clusters = this.identifyMemoryClusters(memories);
            // Add temporal flow information
            const temporalFlow = this.analyzeTemporalFlow(memories);
            return {
                nodes,
                edges,
                clusters,
                temporalFlow,
                metadata: {
                    totalNodes: nodes.length,
                    totalEdges: edges.length,
                    centerNode: centerMemoryId,
                    generatedAt: Date.now(),
                    timespan: this.calculateTimespan(memories),
                    complexity: this.calculateVisualizationComplexity(nodes, edges)
                }
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to generate memory visualization: ${error}`);
            return { nodes: [], edges: [], clusters: [], temporalFlow: [], metadata: {} };
        }
    }
    /**
       * Advanced Memory Analytics (Enhanced for Phase 4)
       */
    async generateAdvancedAnalytics() {
        try {
            const allMemories = Array.from(this.temporalStore.values()).flat();
            return {
                ...this.analytics,
                advancedMetrics: {
                    memoryDensity: this.calculateMemoryDensity(allMemories),
                    contextualCoverage: this.calculateContextualCoverage(allMemories),
                    temporalDistribution: this.analyzeTemporalDistribution(allMemories),
                    collaborationPatterns: this.analyzeCollaborationPatterns(),
                    predictiveAccuracy: this.calculatePredictiveAccuracy(),
                    memoryEfficiency: this.calculateMemoryEfficiency(),
                    knowledgeGaps: this.identifyKnowledgeGaps(allMemories),
                    learningVelocity: this.calculateLearningVelocity(allMemories)
                },
                visualizations: {
                    memoryMap: await this.generateMemoryVisualization(),
                    temporalTimeline: this.generateTemporalTimeline(allMemories),
                    contextualHeatmap: this.generateContextualHeatmap(allMemories),
                    collaborationNetwork: this.generateCollaborationNetwork()
                }
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to generate advanced analytics: ${error}`);
            return { ...this.analytics };
        }
    }
    // Private helper methods
    initializeAnalytics() {
        return {
            totalMemories: 0,
            temporalMemories: 0,
            collaborativeShares: 0,
            predictiveInsights: 0,
            memoryGrowthRate: 0,
            averageRetrievalTime: 0,
            mostAccessedMemories: [],
            memoryPatterns: {
                commonTags: [],
                frequentContexts: [],
                peakUsageTimes: []
            },
            userBehaviorInsights: {
                preferredMemoryTypes: [],
                searchPatterns: [],
                collaborationFrequency: 0
            }
        };
    }
    setupEventListeners() {
        // Listen for file changes to update temporal memory
        vscode.workspace.onDidChangeTextDocument(event => {
            this.onDocumentChanged(event);
        });
        // Listen for file saves to create temporal snapshots
        vscode.workspace.onDidSaveTextDocument(document => {
            this.onDocumentSaved(document);
        });
    }
    async onDocumentChanged(event) {
        if (event.contentChanges.length > 0) {
            // Store temporal memory for significant changes
            const change = event.contentChanges[0];
            if (change.text.length > 10 || change.rangeLength > 10) {
                await this.storeTemporalMemory(`Document change: ${change.text}`, {
                    filePath: event.document.uri.fsPath,
                    lineNumber: change.range.start.line,
                    projectContext: vscode.workspace.name || 'unknown'
                }, 'update', { tags: ['auto-save', 'document-change'] });
            }
        }
    }
    async onDocumentSaved(document) {
        // Create temporal snapshot on save
        await this.storeTemporalMemory(`File saved: ${document.fileName}`, {
            filePath: document.uri.fsPath,
            projectContext: vscode.workspace.name || 'unknown'
        }, 'update', { tags: ['save', 'snapshot'] });
    }
    startPredictiveEngine() {
        // Run predictive analysis every 30 minutes
        setInterval(async () => {
            try {
                const context = vscode.window.activeTextEditor?.document.uri.fsPath || 'general';
                await this.generatePredictiveInsights(context);
            }
            catch (error) {
                logger_1.logger.warn(`Predictive engine error: ${error}`);
            }
        }, 30 * 60 * 1000);
    }
    async loadTemporalMemories() {
        try {
            const memories = await this.memoryManager.searchMemories({
                query: 'temporal',
                limit: 1000,
                filter: {
                    tags: ['temporal']
                }
            });
            for (const memory of memories) {
                try {
                    const temporalEntry = JSON.parse(memory.content);
                    if (!this.temporalStore.has(temporalEntry.context.filePath)) {
                        this.temporalStore.set(temporalEntry.context.filePath, []);
                    }
                    const temporalEntries = this.temporalStore.get(temporalEntry.context.filePath);
                    if (temporalEntries) {
                        temporalEntries.push(temporalEntry);
                    }
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to parse temporal memory: ${error}`);
                }
            }
            logger_1.logger.info(`Loaded ${this.temporalStore.size} temporal memory files`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to load temporal memories: ${error}`);
        }
    }
    async loadCollaborativeShares() {
        // In real implementation, load from persistent storage
        logger_1.logger.info('Collaborative shares loaded');
    }
    async initializePredictiveEngine() {
        // Initialize predictive models and patterns
        logger_1.logger.info('Predictive engine initialized');
    }
    async buildContextualIndex() {
        // Build index for fast contextual lookups
        for (const [, memories] of this.temporalStore) {
            for (const memory of memories) {
                this.updateContextualIndex(memory);
            }
        }
        logger_1.logger.info(`Built contextual index with ${this.contextualIndex.size} entries`);
    }
    updateContextualIndex(memory) {
        const contexts = [
            memory.context.filePath,
            memory.context.functionName,
            memory.context.className,
            memory.context.projectContext,
            ...memory.metadata.tags
        ].filter(Boolean);
        for (const context of contexts) {
            if (!this.contextualIndex.has(context)) {
                this.contextualIndex.set(context, new Set());
            }
            const contextualSet = this.contextualIndex.get(context);
            if (contextualSet) {
                contextualSet.add(memory.id);
            }
        }
    }
    generateQueryCacheKey(query) {
        return JSON.stringify(query);
    }
    getFromCache(key) {
        const cached = this.queryCache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
            return cached.result;
        }
        if (cached) {
            this.queryCache.delete(key);
        }
        return null;
    }
    setCache(key, result) {
        if (this.queryCache.size >= this.maxCacheSize) {
            const firstKey = this.queryCache.keys().next().value;
            if (firstKey) {
                this.queryCache.delete(firstKey);
            }
        }
        this.queryCache.set(key, { result, timestamp: Date.now() });
    }
    async applyContextualFiltering(memories, query) {
        // Filter memories based on context
        const filtered = [];
        for (const memory of memories) {
            try {
                const temporalEntry = JSON.parse(memory.content);
                // Apply context filters
                if (query.context.currentFile &&
                    !temporalEntry.context.filePath.includes(query.context.currentFile)) {
                    continue;
                }
                if (query.context.currentFunction &&
                    temporalEntry.context.functionName !== query.context.currentFunction) {
                    continue;
                }
                filtered.push(temporalEntry);
            }
            catch (error) {
                // Skip invalid entries
                continue;
            }
        }
        return filtered;
    }
    applyTemporalConstraints(memories, constraints) {
        if (!constraints)
            return memories;
        return memories.filter(memory => {
            if (constraints.startTime && memory.timestamp < constraints.startTime)
                return false;
            if (constraints.endTime && memory.timestamp > constraints.endTime)
                return false;
            if (constraints.maxAge && Date.now() - memory.timestamp > constraints.maxAge)
                return false;
            return true;
        });
    }
    convertToTemporalEntry(memory) {
        return {
            ...memory,
            version: 1,
            changeType: 'create',
            context: {
                filePath: memory.metadata.filePath || '',
                functionName: memory.metadata.functionName,
                className: memory.metadata.className,
                lineNumber: memory.metadata.lineNumber,
                projectContext: memory.metadata.projectContext || ''
            },
            metadata: {
                author: memory.metadata.author,
                commitHash: memory.metadata.commitHash,
                branchName: memory.metadata.branchName,
                tags: memory.metadata.tags || []
            },
            previousVersionId: undefined
        };
    }
    async enhanceWithQuantumInsights(memories, query) {
        // Add quantum insights to memories based on query context
        logger_1.logger.debug(`Enhancing ${memories.length} memories with quantum insights for context: ${query.context}`);
        // For now, return memories as-is but log the enhancement attempt
        return memories;
    }
    sortByRelevanceAndContext(memories, query) {
        return memories.sort((a, b) => {
            // Sort by timestamp (most recent first) and relevance
            const timeScore = b.timestamp - a.timestamp;
            const contextScore = this.calculateContextScore(a, query) - this.calculateContextScore(b, query);
            return contextScore !== 0 ? contextScore : timeScore;
        });
    }
    calculateContextScore(memory, query) {
        let score = 0;
        if (query.context.currentFile && memory.context.filePath.includes(query.context.currentFile)) {
            score += 10;
        }
        if (query.context.currentFunction && memory.context.functionName === query.context.currentFunction) {
            score += 5;
        }
        if (query.context.currentClass && memory.context.className === query.context.currentClass) {
            score += 5;
        }
        return score;
    }
    async getRecentMemories(timeWindow) {
        const cutoff = Date.now() - timeWindow;
        const recent = [];
        for (const memories of this.temporalStore.values()) {
            for (const memory of memories) {
                if (memory.timestamp >= cutoff) {
                    recent.push(memory);
                }
            }
        }
        return recent.sort((a, b) => b.timestamp - a.timestamp);
    }
    buildPredictivePrompt(memories, context) {
        const recentChanges = memories.slice(0, 10).map(m => `${m.changeType}: ${m.content} (${m.context.filePath})`).join('\n');
        return `
# Predictive Memory Analysis

## Recent Memory Patterns:
${recentChanges}

## Current Context:
${context}

## Task:
Analyze the recent memory patterns and predict:
1. What the developer might need next
2. Potential issues or opportunities
3. Suggested actions or improvements

Provide insights in JSON format:
{
  "insights": [
    {
      "type": "pattern|suggestion|warning|opportunity",
      "confidence": 0.8,
      "prediction": "Brief prediction",
      "reasoning": "Why this prediction makes sense",
      "suggestedActions": ["action1", "action2"]
    }
  ]
}
        `;
    }
    parsePredictiveInsights(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed.insights.map((insight, index) => ({
                    id: `insight_${Date.now()}_${index}`,
                    type: insight.type || 'suggestion',
                    confidence: insight.confidence || 0.5,
                    prediction: insight.prediction || '',
                    reasoning: insight.reasoning || '',
                    suggestedActions: insight.suggestedActions || [],
                    relatedMemories: [],
                    expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
                }));
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to parse predictive insights: ${error}`);
        }
        return [];
    }
    async getRelatedMemories(memoryId, limit) {
        // Find memories related to the given memory
        const related = [];
        for (const memories of this.temporalStore.values()) {
            for (const memory of memories) {
                if (memory.id === memoryId || memory.previousVersionId === memoryId || memory.nextVersionId === memoryId) {
                    related.push(memory);
                }
            }
        }
        return related.slice(0, limit);
    }
    generateMemoryConnections(memories) {
        const edges = [];
        for (const memory of memories) {
            if (memory.previousVersionId) {
                edges.push({
                    from: memory.previousVersionId,
                    to: memory.id,
                    type: 'version',
                    weight: 1.0,
                    label: 'evolves to'
                });
            }
            // Add context-based connections
            const relatedMemories = memories.filter(m => m.id !== memory.id &&
                m.context.filePath === memory.context.filePath);
            for (const related of relatedMemories.slice(0, 3)) {
                edges.push({
                    from: memory.id,
                    to: related.id,
                    type: 'context',
                    weight: 0.8,
                    label: 'related'
                });
            }
        }
        return edges;
    }
    updateRetrievalAnalytics(query, resultCount, retrievalTime) {
        this.queryHistory.push({ query, timestamp: Date.now(), resultCount });
        // Update average retrieval time
        const totalTime = this.analytics.averageRetrievalTime * (this.queryHistory.length - 1) + retrievalTime;
        this.analytics.averageRetrievalTime = totalTime / this.queryHistory.length;
        // Keep only recent query history
        if (this.queryHistory.length > 1000) {
            this.queryHistory = this.queryHistory.slice(-500);
        }
    }
    updateAnalytics() {
        // Update real-time analytics
        this.analytics.totalMemories = Array.from(this.temporalStore.values()).reduce((sum, memories) => sum + memories.length, 0);
        this.analytics.temporalMemories = this.analytics.totalMemories;
        this.analytics.collaborativeShares = this.collaborativeShares.size;
        this.analytics.predictiveInsights = this.predictiveInsights.size;
        // Calculate growth rate (simplified)
        const recentMemories = this.queryHistory.filter(q => Date.now() - q.timestamp < 24 * 60 * 60 * 1000);
        this.analytics.memoryGrowthRate = recentMemories.length;
    }
    // Enhanced visualization helper methods for Phase 4
    calculateNodeSize(memory) {
        // Calculate node size based on memory importance and content length
        let size = 10; // Base size
        size += Math.min(memory.content.length / 100, 20); // Content length factor
        size += memory.metadata.tags.length * 2; // Tag factor
        return Math.min(size, 50); // Max size limit
    }
    getNodeColor(changeType) {
        const colors = {
            'create': '#4CAF50', // Green
            'update': '#2196F3', // Blue
            'delete': '#F44336', // Red
            'refactor': '#FF9800' // Orange
        };
        return colors[changeType] || '#9E9E9E';
    }
    calculateMemoryImportance(memory) {
        let importance = 0.5; // Base importance
        // Recent memories are more important
        const age = Date.now() - memory.timestamp;
        const dayInMs = 24 * 60 * 60 * 1000;
        if (age < dayInMs)
            importance += 0.3;
        else if (age < 7 * dayInMs)
            importance += 0.2;
        else if (age < 30 * dayInMs)
            importance += 0.1;
        // More tags indicate more context
        importance += Math.min(memory.metadata.tags.length * 0.1, 0.3);
        return Math.min(importance, 1.0);
    }
    identifyMemoryClusters(memories) {
        const clusters = [];
        const fileGroups = new Map();
        // Group by file path
        for (const memory of memories) {
            const filePath = memory.context.filePath;
            if (!fileGroups.has(filePath)) {
                fileGroups.set(filePath, []);
            }
            const fileGroup = fileGroups.get(filePath);
            if (fileGroup) {
                fileGroup.push(memory);
            }
        }
        // Create clusters for files with multiple memories
        for (const [filePath, fileMemories] of fileGroups) {
            if (fileMemories.length > 1) {
                clusters.push({
                    id: `cluster_${filePath.replace(/[^a-zA-Z0-9]/g, '_')}`,
                    name: `File: ${filePath.split('/').pop()}`,
                    members: fileMemories.map(m => m.id),
                    type: 'file-based',
                    strength: fileMemories.length / memories.length
                });
            }
        }
        return clusters;
    }
    analyzeTemporalFlow(memories) {
        const flow = [];
        const sortedMemories = memories.sort((a, b) => a.timestamp - b.timestamp);
        for (let i = 0; i < sortedMemories.length - 1; i++) {
            const current = sortedMemories[i];
            const next = sortedMemories[i + 1];
            // Check if memories are related (same file or linked versions)
            if (current.context.filePath === next.context.filePath ||
                current.nextVersionId === next.id) {
                flow.push({
                    from: current.id,
                    to: next.id,
                    timeDelta: next.timestamp - current.timestamp,
                    type: 'temporal',
                    strength: this.calculateTemporalStrength(current, next)
                });
            }
        }
        return flow;
    }
    calculateTemporalStrength(memory1, memory2) {
        const timeDelta = Math.abs(memory2.timestamp - memory1.timestamp);
        const hourInMs = 60 * 60 * 1000;
        // Closer in time = stronger connection
        if (timeDelta < hourInMs)
            return 1.0;
        if (timeDelta < 24 * hourInMs)
            return 0.7;
        if (timeDelta < 7 * 24 * hourInMs)
            return 0.4;
        return 0.1;
    }
    calculateTimespan(memories) {
        if (memories.length === 0) {
            return { start: 0, end: 0, duration: 0 };
        }
        const timestamps = memories.map(m => m.timestamp);
        const start = Math.min(...timestamps);
        const end = Math.max(...timestamps);
        return { start, end, duration: end - start };
    }
    calculateVisualizationComplexity(nodes, edges) {
        // Simple complexity metric based on nodes and edges
        return Math.min((nodes.length + edges.length) / 100, 1.0);
    }
    // Advanced analytics helper methods
    calculateMemoryDensity(memories) {
        if (memories.length === 0)
            return 0;
        const timespan = this.calculateTimespan(memories);
        if (timespan.duration === 0)
            return 1;
        const dayInMs = 24 * 60 * 60 * 1000;
        return memories.length / (timespan.duration / dayInMs);
    }
    calculateContextualCoverage(memories) {
        const uniqueFiles = new Set(memories.map(m => m.context.filePath));
        const uniqueFunctions = new Set(memories.map(m => m.context.functionName).filter(Boolean));
        const uniqueClasses = new Set(memories.map(m => m.context.className).filter(Boolean));
        // Simple coverage metric
        return Math.min((uniqueFiles.size + uniqueFunctions.size + uniqueClasses.size) / 100, 1.0);
    }
    analyzeTemporalDistribution(memories) {
        const distribution = {
            hourly: new Array(24).fill(0),
            daily: new Array(7).fill(0),
            monthly: new Array(12).fill(0)
        };
        for (const memory of memories) {
            const date = new Date(memory.timestamp);
            distribution.hourly[date.getHours()]++;
            distribution.daily[date.getDay()]++;
            distribution.monthly[date.getMonth()]++;
        }
        return distribution;
    }
    analyzeCollaborationPatterns() {
        // Analyze collaboration patterns from shared memories
        return {
            totalShares: this.collaborativeShares.size,
            averageShareDuration: 0,
            mostActiveCollaborators: [],
            collaborationFrequency: 0
        };
    }
    calculatePredictiveAccuracy() {
        // Calculate accuracy of past predictions
        const expiredInsights = Array.from(this.predictiveInsights.values())
            .filter(insight => Date.now() > insight.expiresAt);
        if (expiredInsights.length === 0)
            return 0.5; // Default
        // Simple accuracy calculation (would need actual validation in real implementation)
        return 0.75; // Placeholder
    }
    calculateMemoryEfficiency() {
        // Calculate memory system efficiency
        const totalMemories = Array.from(this.temporalStore.values()).reduce((sum, memories) => sum + memories.length, 0);
        const cacheHitRate = this.queryCache.size > 0 ? 0.8 : 0; // Simplified
        return Math.min((totalMemories / 1000) * cacheHitRate, 1.0);
    }
    identifyKnowledgeGaps(memories) {
        const gaps = [];
        // Identify files with no recent memories
        const recentCutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days
        const recentFiles = new Set(memories
            .filter(m => m.timestamp > recentCutoff)
            .map(m => m.context.filePath));
        const allFiles = new Set(memories.map(m => m.context.filePath));
        for (const file of allFiles) {
            if (!recentFiles.has(file)) {
                gaps.push(`No recent activity in ${file}`);
            }
        }
        return gaps.slice(0, 10); // Limit to top 10
    }
    calculateLearningVelocity(memories) {
        // Calculate how quickly new knowledge is being acquired
        const recentCutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days
        const recentMemories = memories.filter(m => m.timestamp > recentCutoff);
        return recentMemories.length / 7; // Memories per day
    }
    generateTemporalTimeline(memories) {
        const timeline = memories
            .sort((a, b) => a.timestamp - b.timestamp)
            .map(memory => ({
            timestamp: memory.timestamp,
            event: memory.changeType,
            file: memory.context.filePath,
            content: memory.content.substring(0, 100)
        }));
        return timeline;
    }
    generateContextualHeatmap(memories) {
        const heatmap = {};
        for (const memory of memories) {
            const filePath = memory.context.filePath;
            heatmap[filePath] = (heatmap[filePath] || 0) + 1;
        }
        return heatmap;
    }
    generateCollaborationNetwork() {
        const network = {
            nodes: [],
            edges: []
        };
        // Generate collaboration network from shared memories
        for (const share of this.collaborativeShares.values()) {
            network.nodes.push({
                id: share.sharedBy,
                type: 'user',
                label: share.sharedBy
            });
            for (const recipient of share.sharedWith) {
                network.nodes.push({
                    id: recipient,
                    type: 'user',
                    label: recipient
                });
                network.edges.push({
                    from: share.sharedBy,
                    to: recipient,
                    type: 'collaboration',
                    weight: 1
                });
            }
        }
        return network;
    }
    /**
       * Dispose quantum memory system
       */
    dispose() {
        this.queryCache.clear();
        this.temporalStore.clear();
        this.contextualIndex.clear();
        this.collaborativeShares.clear();
        this.predictiveInsights.clear();
        this.queryHistory = [];
        logger_1.logger.info('Quantum Memory System disposed');
    }
}
exports.QuantumMemorySystem = QuantumMemorySystem;
//# sourceMappingURL=quantumMemorySystem.js.map