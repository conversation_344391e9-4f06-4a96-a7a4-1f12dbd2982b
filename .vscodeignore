# Development and build files
.vscode/**
.github/**
.git/**
.git_disabled/**
.amazonq/**
.codessa/**
# Source files - they'll be compiled to out/
src/**
# Development and build files
webpack.config.js
tsconfig.json
tsconfig.dev.json
.eslintrc.js
.eslintrc.json
test/**
scripts/**
docs/**
uml/**
.gitignore
.gitattributes
.npmrc
tsconfig.tsbuildinfo
*.vsix
*.log
*.tgz
appmap.log
CLEANUP.md
SETUP.md
plan.md
CONTRIBUTING.md
CHANGELOG.md
PHASE1_README.md
PHASE2_README.md
competitor-features.md
srccompetitor-features.md
Augment-Memories.md
errors.txt
minimal.vscodeignore
run-extension-vscode.bat
tsc_trace.log

# Include only the necessary files from node_modules
!node_modules/axios/**
!node_modules/@mistralai/**
!node_modules/@anthropic-ai/**
!node_modules/@pinecone-database/**
!node_modules/diff/**
!node_modules/mongodb/**
!node_modules/mysql2/**
!node_modules/openai/**
!node_modules/pg/**
!node_modules/redis/**
!node_modules/sqlite3/**
!node_modules/uuid/**
!node_modules/zod/**
!node_modules/zod-to-json-schema/**

# Exclude development and test files from node_modules
node_modules/**/test/**
node_modules/**/tests/**
node_modules/**/__tests__/**
node_modules/**/*.d.ts
node_modules/**/*.map
node_modules/**/*.md
node_modules/**/LICENSE
node_modules/**/CHANGELOG*
node_modules/**/README*
!node_modules/highlight.js/lib/**
!node_modules/marked/lib/**
!node_modules/openai/dist/**
!node_modules/uuid/dist/**
!node_modules/zod/lib/**
!node_modules/zod-to-json-schema/dist/**
!node_modules/pg/**
!node_modules/mongodb/**
!node_modules/redis/**
!node_modules/uuid/**
!node_modules/mysql2/**
!node_modules/sqlite3/**
!node_modules/sqlite/**

# Include only necessary files from node_modules
!node_modules/@anthropic-ai/sdk/dist/**
!node_modules/@mistralai/mistralai/dist/**
!node_modules/axios/dist/**
!node_modules/diff/dist/**
!node_modules/highlight.js/lib/**
!node_modules/marked/lib/**
!node_modules/openai/dist/**
!node_modules/uuid/dist/**
!node_modules/zod/lib/**
!node_modules/zod-to-json-schema/dist/**