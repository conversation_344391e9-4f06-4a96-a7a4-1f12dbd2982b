{"version": 3, "file": "receiverAgent.js", "sourceRoot": "", "sources": ["../../../src/agents/agentTypes/receiverAgent.ts"], "names": [], "mappings": ";;AAAA,mDAA6D;AAE7D,yCAAsC;AACtC,+DAA4D;AAE5D,MAAM,aAAc,SAAQ,aAAK;IAC/B,YAAY,OAA6B;QACvC,KAAK,CAAC;YACJ,GAAG,OAAO;YACV,IAAI,EAAE,WAAW;YACjB,YAAY,EAAE,CAAC,SAAS,CAAC;YACzB,WAAW,EAAE,QAAQ;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,UAAmC,EAAE;QACrE,eAAM,CAAC,IAAI,CAAC,oCAAoC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;QAE9E,4CAA4C;QAC5C,IAAI,OAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;YACvC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAChC,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;gBACxC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,qDAAqD;QACrD,IAAI,QAAQ,GAAG,SAAS,CAAC;QACzB,IAAI,OAAO,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC;YACvC,QAAQ,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,CAAC;QACtF,CAAC;QAED,kDAAkD;QAClD,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACnE,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;gBACrB,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,IAAI,OAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;YACvC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAChC,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QACD,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,OAAO,CAAC,SAAS,EAAE,oBAAoB,EAAE,CAAC;YAC5C,SAAS,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACvF,CAAC;QAED,6BAA6B;QAC7B,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;YAClC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QACxF,CAAC;QAED,uDAAuD;QACvD,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAE1D,qDAAqD;QACrD,MAAM,iBAAiB,GAAG,6BAAa,CAAC,YAAY,CAAC,gBAAgB,EAAE;YACrE,SAAS,EAAE,KAAK;YAChB,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;YAC9C,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;YAC5C,aAAa;YACb,SAAS;YACT,UAAU;YACV,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM;SACvD,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,OAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;YACvC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAChC,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE;gBACxC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QAE7D,wBAAwB;QACxB,IAAI,OAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;YACvC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAChC,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,EAAE;gBACzD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,kCAAkC,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QACtF,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,cAAc,CAAC,OAAY;QACjC,IAAI,gBAAgB,GAAG,EAAE,CAAC;QAE1B,2BAA2B;QAC3B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,gBAAgB,IAAI,yBAAyB,CAAC;YAC9C,IAAI,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;gBAClC,gBAAgB,IAAI,iBAAiB,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC;gBAChF,IAAI,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;oBAChC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;oBAC9C,gBAAgB,IAAI,4BAA4B,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC;gBAC/G,CAAC;YACH,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9D,gBAAgB,IAAI,sBAAsB,CAAC;YAC3C,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;gBAC1C,gBAAgB,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC;gBAC3C,gBAAgB,IAAI,sBAAsB,IAAI,CAAC,OAAO,YAAY,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,0CAA0C;QAC1C,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC/B,gBAAgB,IAAI,2BAA2B,CAAC;YAChD,gBAAgB,IAAI,GAAG,OAAO,CAAC,kBAAkB,IAAI,CAAC;QACxD,CAAC;QAED,OAAO,gBAAgB,IAAI,iCAAiC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAAgC;QAC7D,MAAM,UAAU,GAA4B,EAAE,CAAC;QAE/C,iBAAiB;QACjB,IAAI,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAY,EAAE,EAAE;gBACtD,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;oBAC5C,MAAM,QAAQ,GAAG,GAA8B,CAAC;oBAChD,OAAO;wBACL,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;wBACzC,QAAQ,EAAE,QAAQ;qBACnB,CAAC;gBACJ,CAAC;gBACD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;YAC3E,CAAC,CAAC,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvD,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAgC,CAAC;YAC1D,UAAU,CAAC,KAAK,GAAG;gBACjB,IAAI,EAAE,OAAO;gBACb,aAAa,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,aAAuB,CAAC;gBACzE,QAAQ,EAAE,QAAQ;aACnB,CAAC;QACJ,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAgC;QAC5D,MAAM,KAAK,GAA8B,EAAE,CAAC;QAE5C,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAClD,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBACjC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;oBAC9C,MAAM,OAAO,GAAG,IAA+B,CAAC;oBAChD,KAAK,CAAC,IAAI,CAAC;wBACT,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,cAAc;wBACpC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;wBACxB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,SAAS;wBAC/B,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC;wBACvB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;wBAC9B,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;qBACtC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAiC;QACrD,0CAA0C;QAC1C,OAAO,qBAAqB,QAAQ,CAAC,IAAI,IAAI,SAAS,KAAK,QAAQ,CAAC,IAAI,IAAI,cAAc,GAAG,CAAC;IAChG,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,aAAqB;QAC/C,IAAI,CAAC,aAAa,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACxD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,sCAAsC;QACtC,OAAO,aAAa;aACjB,IAAI,EAAE;aACN,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,uBAAuB;aAC5C,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC,sBAAsB;aACzD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,iDAAiD;aACtE,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAgC;QACrD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAc,IAAI,cAAc,CAAC;QACtD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAc,IAAI,SAAS,CAAC;QACjD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAc,IAAI,CAAC,CAAC;QAEzC,IAAI,OAAO,GAAG,SAAS,IAAI,KAAK,IAAI,GAAG,CAAC;QACxC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,OAAO,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,CAAC;QAED,qCAAqC;QACrC,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC3D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAiB,CAAC;YAC1C,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;gBACzB,OAAO,IAAI,cAAc,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;YAC1D,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,cAAc,OAAO,EAAE,CAAC;YACrC,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAa;QACnC,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAClC,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAC,KAAoB,EAAE,eAA6B,EAAE;QAC7D,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,uCAAuC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAEjE,qCAAqC;YACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAEnE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE;oBACR,OAAO,EAAE,IAAI,CAAC,EAAE;oBAChB,SAAS,EAAE,IAAI,CAAC,IAAI;oBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,QAAQ,EAAE;oBACR,OAAO,EAAE,IAAI,CAAC,EAAE;oBAChB,SAAS,EAAE,IAAI,CAAC,IAAI;oBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAED,kBAAe,aAAa,CAAC", "sourcesContent": ["import { Agent, AgentConfig } from '../agentUtilities/agent';\nimport { AgentRunInput, AgentContext, AgentRunR<PERSON>ult } from '../../types/agent';\nimport { logger } from '../../logger';\nimport { promptManager } from '../../prompts/promptManager';\n\nclass receiverAgent extends Agent {\n  constructor(_config: Partial<AgentConfig>) {\n    super({\n      ..._config,\n      role: 'assistant',\n      capabilities: ['receive'],\n      llmProvider: 'openai',\n      llmModel: 'gpt-4'\n    });\n  }\n\n  async processInput(input: string, context: Record<string, unknown> = {}): Promise<string> {\n    logger.info(`receiverAgent processing input: \"${input.substring(0, 50)}...\"`);\n\n    // Emit streaming event if context available\n    if (context.streamingContext?.onStream) {\n      context.streamingContext.onStream({\n        type: 'start',\n        agentId: 'receiver',\n        data: { input: input.substring(0, 100) },\n        timestamp: Date.now()\n      });\n    }\n\n    // --- Gather relevant workflow for receiver mode ---\n    let workflow = undefined;\n    if (context.variables?.workflowManager) {\n      workflow = await context.variables.workflowManager.getWorkflowForMode?.('receiver');\n    }\n\n    // --- Gather memory and knowledgebase context ---\n    let memoryContext = '';\n    if (this.getMemory) {\n      const memories = await this.getMemory().getRelevantMemories(input);\n      if (memories?.length) {\n        memoryContext = this.getMemory().formatMemoriesForPrompt(memories);\n      }\n    }\n\n    // Emit progress event\n    if (context.streamingContext?.onStream) {\n      context.streamingContext.onStream({\n        type: 'progress',\n        agentId: 'receiver',\n        data: { step: 'context_gathering' },\n        timestamp: Date.now()\n      });\n    }\n    let kbContext = '';\n    if (context.variables?.knowledgebaseManager) {\n      kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input);\n    }\n\n    // --- Gather MCP context ---\n    let mcpContext = '';\n    if (context.variables?.mcpManager) {\n      mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext?.() || {});\n    }\n\n    // Extract multimodal data and attachments from context\n    const multimodalData = this._extractMultimodalData(context);\n    const attachedFiles = this._extractAttachedFiles(context);\n\n    // Use promptManager for consistent prompt generation\n    const enhancementPrompt = promptManager.renderPrompt('agent.receiver', {\n      userInput: input,\n      multimodalData: JSON.stringify(multimodalData),\n      attachedFiles: JSON.stringify(attachedFiles),\n      memoryContext,\n      kbContext,\n      mcpContext,\n      workflow: workflow ? JSON.stringify(workflow) : 'None'\n    });\n    \n    // Emit processing event\n    if (context.streamingContext?.onStream) {\n      context.streamingContext.onStream({\n        type: 'progress',\n        agentId: 'receiver',\n        data: { step: 'generating_enhancement' },\n        timestamp: Date.now()\n      });\n    }\n\n    const enhancedInput = await this.generate(enhancementPrompt);\n    \n    // Emit completion event\n    if (context.streamingContext?.onStream) {\n      context.streamingContext.onStream({\n        type: 'complete',\n        agentId: 'receiver',\n        data: { result: enhancedInput.substring(0, 100) + '...' },\n        timestamp: Date.now()\n      });\n    }\n    \n    logger.debug(`receiverAgent enhanced input: \"${enhancedInput.substring(0, 100)}...\"`);\n    return enhancedInput;\n  }\n    \n  private _formatContext(context: any): string {\n    let formattedContext = '';\n        \n    // Format workspace context\n    if (context.workspace) {\n      formattedContext += '### Workspace Context\\n';\n      if (context.workspace.currentFile) {\n        formattedContext += `Current File: ${context.workspace.currentFile.fileName}\\n`;\n        if (context.workspace.selection) {\n          const selection = context.workspace.selection;\n          formattedContext += `Selected Text: \\n\\`\\`\\`\\n${context.workspace.currentFile.getText(selection)}\\n\\`\\`\\`\\n`;\n        }\n      }\n    }\n        \n    // Format attached files\n    if (context.attachedFiles && context.attachedFiles.length > 0) {\n      formattedContext += '### Attached Files\\n';\n      context.attachedFiles.forEach((file: any) => {\n        formattedContext += `File: ${file.name}\\n`;\n        formattedContext += `Content: \\n\\`\\`\\`\\n${file.content}\\n\\`\\`\\`\\n`;\n      });\n    }\n        \n    // Format voice transcription if available\n    if (context.voiceTranscription) {\n      formattedContext += '### Voice Transcription\\n';\n      formattedContext += `${context.voiceTranscription}\\n`;\n    }\n        \n    return formattedContext || 'No additional context available';\n  }\n\n  /**\n   * Extract multimodal data from context (images, audio, etc.)\n   */\n  private _extractMultimodalData(context: Record<string, unknown>): Record<string, unknown> {\n    const multimodal: Record<string, unknown> = {};\n\n    // Extract images\n    if (context.images && Array.isArray(context.images)) {\n      multimodal.images = context.images.map((img: unknown) => {\n        if (typeof img === 'object' && img !== null) {\n          const imageObj = img as Record<string, unknown>;\n          return {\n            type: 'image',\n            description: this._analyzeImage(imageObj),\n            metadata: imageObj\n          };\n        }\n        return { type: 'image', description: 'Image attachment', metadata: img };\n      });\n    }\n\n    // Extract audio/transcribed text\n    if (context.audio && typeof context.audio === 'object') {\n      const audioObj = context.audio as Record<string, unknown>;\n      multimodal.audio = {\n        type: 'audio',\n        transcription: this._cleanTranscription(audioObj.transcription as string),\n        metadata: audioObj\n      };\n    }\n\n    return multimodal;\n  }\n\n  /**\n   * Extract attached files from context\n   */\n  private _extractAttachedFiles(context: Record<string, unknown>): Record<string, unknown>[] {\n    const files: Record<string, unknown>[] = [];\n\n    if (context.files && Array.isArray(context.files)) {\n      for (const file of context.files) {\n        if (typeof file === 'object' && file !== null) {\n          const fileObj = file as Record<string, unknown>;\n          files.push({\n            name: fileObj.name || 'Unknown file',\n            path: fileObj.path || '',\n            type: fileObj.type || 'unknown',\n            size: fileObj.size || 0,\n            content: fileObj.content || '',\n            summary: this._summarizeFile(fileObj)\n          });\n        }\n      }\n    }\n\n    return files;\n  }\n\n  /**\n   * Analyze image content (placeholder for future image analysis)\n   */\n  private _analyzeImage(imageObj: Record<string, unknown>): string {\n    // Future: Implement actual image analysis\n    return `Image attachment: ${imageObj.name || 'unnamed'} (${imageObj.type || 'unknown type'})`;\n  }\n\n  /**\n   * Clean and improve transcribed audio text\n   */\n  private _cleanTranscription(transcription: string): string {\n    if (!transcription || typeof transcription !== 'string') {\n      return '';\n    }\n\n    // Basic text cleaning and improvement\n    return transcription\n      .trim()\n      .replace(/\\s+/g, ' ') // Normalize whitespace\n      .replace(/\\b(um|uh|er|ah)\\b/gi, '') // Remove filler words\n      .replace(/\\s+/g, ' ') // Clean up extra spaces from filler word removal\n      .trim();\n  }\n\n  /**\n   * Summarize file content\n   */\n  private _summarizeFile(fileObj: Record<string, unknown>): string {\n    const name = fileObj.name as string || 'Unknown file';\n    const type = fileObj.type as string || 'unknown';\n    const size = fileObj.size as number || 0;\n\n    let summary = `File: ${name} (${type})`;\n    if (size > 0) {\n      summary += ` - ${this._formatFileSize(size)}`;\n    }\n\n    // Add content preview for text files\n    if (fileObj.content && typeof fileObj.content === 'string') {\n      const content = fileObj.content as string;\n      if (content.length > 200) {\n        summary += `\\nPreview: ${content.substring(0, 200)}...`;\n      } else {\n        summary += `\\nContent: ${content}`;\n      }\n    }\n\n    return summary;\n  }\n\n  /**\n   * Format file size in human-readable format\n   */\n  private _formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  /**\n   * Override the run method to use our custom processInput logic\n   */\n  async run(input: AgentRunInput, agentContext: AgentContext = {}): Promise<AgentRunResult> {\n    try {\n      logger.info(`receiverAgent run called with mode: ${input.mode}`);\n\n      // Use our custom processInput method\n      const result = await this.processInput(input.prompt, agentContext);\n\n      return {\n        success: true,\n        output: result,\n        metadata: {\n          agentId: this.id,\n          agentName: this.name,\n          mode: input.mode,\n          timestamp: Date.now()\n        }\n      };\n    } catch (error) {\n      logger.error(`receiverAgent run error:`, error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : String(error),\n        metadata: {\n          agentId: this.id,\n          agentName: this.name,\n          mode: input.mode,\n          timestamp: Date.now()\n        }\n      };\n    }\n  }\n}\n\nexport default receiverAgent;\n"]}