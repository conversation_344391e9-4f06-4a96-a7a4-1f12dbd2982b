{"version": 3, "file": "providerSettings.js", "sourceRoot": "", "sources": ["../../src/llm/providerSettings.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,sCAAmC;AAEnC,0EAAuE;AAEvE;;;GAGG;AACH,MAAa,uBAAuB;IAC1B,MAAM,CAAC,QAAQ,CAA0B;IACzC,OAAO,CAA0B;IACxB,aAAa,GAAG,aAAa,CAAC;IAE/C,YAAoB,OAAgC;QAClD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,WAAW,CAAC,OAAiC;QACzD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC;YACtC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;YACtF,CAAC;YACD,uBAAuB,CAAC,QAAQ,GAAG,IAAI,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO,uBAAuB,CAAC,QAAQ,CAAC;IAC1C,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QAC/C,IAAI,CAAC;YACH,wDAAwD;YACxD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAChE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAoC,WAAW,CAAC,IAAI,EAAE,CAAC;YACnF,MAAM,cAAc,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAEnD,6CAA6C;YAC7C,MAAM,MAAM,GAAG,MAAM,uCAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEtG,OAAO;gBACL,GAAG,cAAc;gBACjB,MAAM,EAAE,MAAM,IAAI,cAAc,CAAC,MAAM;aACxC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,oBAAoB,CAAC,UAAkB,EAAE,OAAY;QAChE,IAAI,CAAC;YACH,kCAAkC;YAClC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,uCAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAChE,UAAU,EACV,QAAQ,EACR,OAAO,CAAC,MAAM,CACf,CAAC;gBAEF,wEAAwE;gBACxE,MAAM,EAAE,MAAM,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO,CAAC;gBAClD,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;gBACpD,OAAO,GAAG,kBAAkB,CAAC;YAC/B,CAAC;YAED,yDAAyD;YACzD,IAAI,CAAC;gBACH,gCAAgC;gBAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAElE,mCAAmC;gBACnC,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBAElD,uDAAuD;gBACvD,MAAM,gBAAgB,GAAG;oBACvB,GAAG,SAAS;oBACZ,CAAC,UAAU,CAAC,EAAE,OAAO;iBACtB,CAAC;gBAEF,gEAAgE;gBAChE,eAAM,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAC9E,eAAM,CAAC,KAAK,CAAC,oBAAoB,GAAG,UAAU,GAAG,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC7F,eAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;gBAE/E,qCAAqC;gBACrC,IAAI,CAAC;oBACH,uDAAuD;oBACvD,IAAI,CAAC;wBACH,MAAM,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;wBAC3D,eAAM,CAAC,IAAI,CAAC,2CAA2C,UAAU,EAAE,CAAC,CAAC;wBACrE,OAAO,CAAC,qBAAqB;oBAC/B,CAAC;oBAAC,OAAO,SAAS,EAAE,CAAC;wBACnB,eAAM,CAAC,IAAI,CAAC,wCAAwC,SAAS,8BAA8B,CAAC,CAAC;oBAC/F,CAAC;oBAED,qCAAqC;oBACrC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACtF,MAAM,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;wBAC3F,eAAM,CAAC,IAAI,CAAC,gDAAgD,UAAU,EAAE,CAAC,CAAC;oBAC5E,CAAC;yBAAM,CAAC;wBACN,sDAAsD;wBACtD,MAAM,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;wBACxF,eAAM,CAAC,IAAI,CAAC,6CAA6C,UAAU,EAAE,CAAC,CAAC;oBACzE,CAAC;gBACH,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,eAAM,CAAC,KAAK,CAAC,mCAAmC,WAAW,EAAE,CAAC,CAAC;oBAC/D,MAAM,IAAI,KAAK,CAAC,mCAAmC,WAAW,EAAE,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,+CAA+C,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;gBAClF,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5E,MAAM,IAAI,KAAK,CAAC,4CAA4C,YAAY,mDAAmD,CAAC,CAAC;YAC/H,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,4CAA4C,YAAY,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;;;SAIK;IACE,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QAClD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,MAAM,uCAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEzG,+DAA+D;YAC/D,OAAO,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,UAAU,iBAAiB,EAAE,KAAK,CAAC,CAAC;YAChF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;SAGK;IACE,oBAAoB;QACzB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAChE,OAAO,MAAM,CAAC,GAAG,CAAS,iBAAiB,CAAC,IAAI,QAAQ,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;;SAGK;IACE,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QAClD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEhE,+CAA+C;YAC/C,IAAI,CAAC;gBACH,mEAAmE;gBACnE,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtF,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,UAAU,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;oBACzF,eAAM,CAAC,IAAI,CAAC,2BAA2B,UAAU,qBAAqB,CAAC,CAAC;gBAC1E,CAAC;qBAAM,CAAC;oBACN,sDAAsD;oBACtD,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,UAAU,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;oBACtF,eAAM,CAAC,IAAI,CAAC,2BAA2B,UAAU,kBAAkB,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,IAAI,CAAC,6DAA6D,WAAW,EAAE,CAAC,CAAC;gBAExF,kCAAkC;gBAClC,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;oBACzD,eAAM,CAAC,IAAI,CAAC,2BAA2B,UAAU,gBAAgB,CAAC,CAAC;gBACrE,CAAC;gBAAC,OAAO,SAAS,EAAE,CAAC;oBACnB,eAAM,CAAC,KAAK,CAAC,iDAAiD,SAAS,EAAE,CAAC,CAAC;oBAC3E,MAAM,IAAI,KAAK,CAAC,mCAAmC,SAAS,EAAE,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,mCAAmC,YAAY,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF;AAhMD,0DAgMC;AAED,4BAA4B;AACf,QAAA,uBAAuB,GAAG;IACrC,WAAW,EAAE,uBAAuB,CAAC,WAAW;CACjD,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { logger } from '../logger';\nimport { LLMProviderConfig } from './llmProvider';\nimport { credentialsManager } from '../credentials/credentialsManager';\n\n/**\n * Manages provider settings, combining secure credentials with\n * non-sensitive configuration stored in VS Code settings\n */\nexport class ProviderSettingsManager {\n  private static instance: ProviderSettingsManager;\n  private context: vscode.ExtensionContext;\n  private readonly configSection = 'codessa.llm';\n\n  private constructor(context: vscode.ExtensionContext) {\n    this.context = context;\n  }\n\n  /**\n     * Get the singleton instance of ProviderSettingsManager\n     */\n  public static getInstance(context?: vscode.ExtensionContext): ProviderSettingsManager {\n    if (!ProviderSettingsManager.instance) {\n      if (!context) {\n        throw new Error('ProviderSettingsManager must be initialized with a context first');\n      }\n      ProviderSettingsManager.instance = new ProviderSettingsManager(context);\n    }\n    return ProviderSettingsManager.instance;\n  }\n\n  /**\n     * Get the configuration for a provider\n     * @param providerId The provider ID\n     * @returns The provider configuration\n     */\n  public async getProviderConfig(providerId: string): Promise<LLMProviderConfig> {\n    try {\n      // Get non-sensitive settings from VS Code configuration\n      const config = vscode.workspace.getConfiguration('codessa.llm');\n      const providers = config.get<Record<string, LLMProviderConfig>>('providers') || {};\n      const providerConfig = providers[providerId] || {};\n\n      // Get sensitive settings from secure storage\n      const apiKey = await credentialsManager.getInstance(this.context).getCredential(providerId, 'apiKey');\n\n      return {\n        ...providerConfig,\n        apiKey: apiKey || providerConfig.apiKey\n      };\n    } catch (error) {\n      logger.error(`Failed to get provider config for ${providerId}:`, error);\n      return {};\n    }\n  }\n\n  /**\n     * Update the configuration for a provider\n     * @param providerId The provider ID\n     * @param config The new configuration\n     */\n  public async updateProviderConfig(providerId: string, _config: any): Promise<void> {\n    try {\n      // Store API key in secure storage\n      if (_config.apiKey) {\n        await credentialsManager.getInstance(this.context).storeCredential(\n          providerId,\n          'apiKey',\n          _config.apiKey\n        );\n\n        // Remove API key from the config object that will be stored in settings\n        const { apiKey, ...nonSensitiveConfig } = _config;\n        logger.debug('Storing API key securely:', !!apiKey);\n        _config = nonSensitiveConfig;\n      }\n\n      // Update non-sensitive settings in VS Code configuration\n      try {\n        // Get the current configuration\n        const vsConfig = vscode.workspace.getConfiguration('codessa.llm');\n\n        // Get the current providers object\n        const providers = vsConfig.get('providers') || {};\n\n        // Update the specific provider in the providers object\n        const updatedProviders = {\n          ...providers,\n          [providerId]: _config\n        };\n\n        // Log the current configuration and what we're trying to update\n        logger.debug(`Current providers configuration: ${JSON.stringify(providers)}`);\n        logger.debug('Updating provider ' + providerId + ' with config: ' + JSON.stringify(_config));\n        logger.debug(`Updated providers will be: ${JSON.stringify(updatedProviders)}`);\n\n        // Update the entire providers object\n        try {\n          // First try updating at the User level (most reliable)\n          try {\n            await vsConfig.update('providers', updatedProviders, true);\n            logger.info(`Updated user configuration for provider ${providerId}`);\n            return; // Exit if successful\n          } catch (userError) {\n            logger.warn(`Failed to update user configuration: ${userError}. Trying workspace/global...`);\n          }\n\n          // Then try Workspace or Global level\n          if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n            await vsConfig.update('providers', updatedProviders, vscode.ConfigurationTarget.Workspace);\n            logger.info(`Updated workspace configuration for provider ${providerId}`);\n          } else {\n            // If no workspace is open, update at the Global level\n            await vsConfig.update('providers', updatedProviders, vscode.ConfigurationTarget.Global);\n            logger.info(`Updated global configuration for provider ${providerId}`);\n          }\n        } catch (updateError) {\n          logger.error(`Failed to update configuration: ${updateError}`);\n          throw new Error(`Failed to update configuration: ${updateError}`);\n        }\n      } catch (error) {\n        logger.error(`Failed to update configuration for provider ${providerId}:`, error);\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        throw new Error(`Failed to update provider configuration: ${errorMessage}. Please check your VS Code settings permissions.`);\n      }\n    } catch (error) {\n      logger.error(`Failed to update provider config for ${providerId}:`, error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Failed to update provider configuration: ${errorMessage}`);\n    }\n  }\n\n  /**\n     * Check if a provider has been configured\n     * @param providerId The provider ID\n     * @returns True if the provider has been configured\n     */\n  public async isProviderConfigured(providerId: string): Promise<boolean> {\n    try {\n      const config = await this.getProviderConfig(providerId);\n      const hasApiKey = await credentialsManager.getInstance(this.context).hasCredential(providerId, 'apiKey');\n\n      // Check if the provider has the minimum required configuration\n      return hasApiKey || (config.apiKey !== undefined && config.apiKey !== '');\n    } catch (error) {\n      logger.error(`Failed to check if provider ${providerId} is configured:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Get the default provider ID from settings\n     * @returns The default provider ID, or 'ollama' if not set\n     */\n  public getDefaultProviderId(): string {\n    try {\n      const config = vscode.workspace.getConfiguration('codessa.llm');\n      return config.get<string>('defaultProvider') || 'ollama';\n    } catch (error) {\n      logger.error('Failed to get default provider ID:', error);\n      return 'ollama';\n    }\n  }\n\n  /**\n     * Set the default provider ID\n     * @param providerId The provider ID to set as default\n     */\n  public async setDefaultProviderId(providerId: string): Promise<void> {\n    try {\n      const config = vscode.workspace.getConfiguration('codessa.llm');\n\n      // Try different configuration targets in order\n      try {\n        // First try updating at the Workspace level if we have a workspace\n        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n          await config.update('defaultProvider', providerId, vscode.ConfigurationTarget.Workspace);\n          logger.info(`Set default provider to ${providerId} at workspace level`);\n        } else {\n          // If no workspace is open, update at the Global level\n          await config.update('defaultProvider', providerId, vscode.ConfigurationTarget.Global);\n          logger.info(`Set default provider to ${providerId} at global level`);\n        }\n      } catch (updateError) {\n        logger.warn(`Failed to set default provider at workspace/global level: ${updateError}`);\n\n        // Try User settings as a fallback\n        try {\n          await config.update('defaultProvider', providerId, true);\n          logger.info(`Set default provider to ${providerId} at user level`);\n        } catch (userError) {\n          logger.error(`Failed to set default provider at user level: ${userError}`);\n          throw new Error(`Failed to set default provider: ${userError}`);\n        }\n      }\n    } catch (error) {\n      logger.error(`Failed to set default provider to ${providerId}:`, error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`Failed to set default provider: ${errorMessage}`);\n    }\n  }\n}\n\n// Export singleton instance\nexport const providerSettingsManager = {\n  getInstance: ProviderSettingsManager.getInstance\n};\n"]}