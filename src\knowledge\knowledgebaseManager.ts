import { IKnowledgebaseManager } from '../managers';
import { logger } from '../logger';

/**
 * Knowledgebase Manager for handling knowledge operations
 */
export class KnowledgebaseManager implements IKnowledgebaseManager {
  private static instance: KnowledgebaseManager;
  private knowledgebase: Array<{
    content: string;
    metadata?: Record<string, any>;
    timestamp: number;
  }> = [];
  
  private constructor() {}
  
  /**
   * Get the singleton instance
   */
  public static getInstance(): KnowledgebaseManager {
    if (!KnowledgebaseManager.instance) {
      KnowledgebaseManager.instance = new KnowledgebaseManager();
    }
    return KnowledgebaseManager.instance;
  }
  
  /**
   * Get relevant knowledge for a query
   */
  public async getRelevantKnowledge(query: string): Promise<string> {
    logger.info(`Getting relevant knowledge for query: ${query.substring(0, 50)}...`);
    
    if (this.knowledgebase.length === 0) {
      return '';
    }
    
    // Simple implementation - in a real system, this would use embeddings and similarity search
    const relevantItems = this.knowledgebase
      .filter(item => this.isRelevant(item.content, query))
      .slice(0, 3); // Limit to top 3 results
    
    if (relevantItems.length === 0) {
      return '';
    }
    
    // Format the knowledge for inclusion in prompts
    return relevantItems
      .map(item => `--- Knowledge Item ---\n${item.content}\n-------------------`)
      .join('\n\n');
  }
  
  /**
   * Add knowledge to the knowledgebase
   */
  public async addKnowledge(content: string, metadata?: Record<string, any>): Promise<void> {
    this.knowledgebase.push({
      content,
      metadata,
      timestamp: Date.now()
    });
    
    logger.info(`Added knowledge to knowledgebase: ${content.substring(0, 50)}...`);
  }
  
  /**
   * Search the knowledgebase
   */
  public async searchKnowledge(query: string): Promise<any[]> {
    return this.knowledgebase
      .filter(item => this.isRelevant(item.content, query))
      .map(item => ({
        content: item.content,
        metadata: item.metadata,
        timestamp: item.timestamp,
        relevance: this.calculateRelevance(item.content, query)
      }))
      .sort((a, b) => b.relevance - a.relevance);
  }
  
  /**
   * Simple relevance check - in a real system, this would use embeddings and similarity search
   */
  private isRelevant(content: string, query: string): boolean {
    const normalizedContent = content.toLowerCase();
    const normalizedQuery = query.toLowerCase();
    
    // Check if any words from the query appear in the content
    const queryWords = normalizedQuery.split(/\s+/);
    return queryWords.some(word => normalizedContent.includes(word));
  }
  
  /**
   * Calculate relevance score - in a real system, this would use embeddings and similarity search
   */
  private calculateRelevance(content: string, query: string): number {
    const normalizedContent = content.toLowerCase();
    const normalizedQuery = query.toLowerCase();
    
    // Count how many query words appear in the content
    const queryWords = normalizedQuery.split(/\s+/);
    const matchingWords = queryWords.filter(word => normalizedContent.includes(word));
    
    return matchingWords.length / queryWords.length;
  }
}

// Export singleton instance
export const knowledgebaseManager = KnowledgebaseManager.getInstance();