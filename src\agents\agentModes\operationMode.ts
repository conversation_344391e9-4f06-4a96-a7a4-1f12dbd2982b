import * as vscode from 'vscode';
import { LLMGenerateParams } from '../../llm/types';
import { Agent } from '../agentUtilities/agent';

/**
 * Context type for operation modes
 */
export enum ContextType {
  NONE = 'none',
  ENTIRE_CODEBASE = 'entire_codebase',
  SELECTED_FILES = 'selected_files',
  CURRENT_FILE = 'current_file',
  CUSTOM = 'custom'
}

/**
 * Context source for operation modes
 */
export interface ContextSource {
  type: ContextType;
  files?: string[];
  folders?: string[];
  externalResources?: string[];
  customContent?: string;
}

/**
 * Operation mode interface
 */
export interface IOperationMode {
  /**
   * Unique identifier for the mode
   */
  readonly id: string;

  /**
   * Display name for the mode
   */
  readonly displayName: string;

  /**
   * Description of the mode
   */
  readonly description: string;

  /**
   * Icon for the mode
   */
  readonly icon: string;

  /**
   * Default context type for this mode
   */
  readonly defaultContextType: ContextType;

  /**
   * Process a message in this mode
   */
  processMessage(message: string, agent: Agent, context: ContextSource): Promise<string>;

  /**
   * Get LLM parameters for this mode
   */
  getLLMParams(): LLMGenerateParams;
}

/**
 * Base class for operation modes
 */
export abstract class OperationMode implements IOperationMode {
  /** Unique identifier for the mode */
  public readonly id: string;
  /** Display name for the mode */
  public readonly displayName: string;
  /** Description of the mode */
  public readonly description: string;
  /** Icon for the mode */
  public readonly icon: string;
  /** Default context type for this mode */
  public readonly defaultContextType: ContextType;
  /** Status bar item for the mode (optional, can be used by subclasses) */
  protected statusBarItem?: vscode.StatusBarItem;
  /** Logger instance for subclasses */
  protected logger = (typeof (globalThis as any).Logger !== 'undefined' ? (globalThis as any).Logger.instance : undefined) || require('../../logger').Logger.instance;

  constructor(
    id: string,
    displayName: string,
    description: string,
    icon: string,
    defaultContextType: ContextType = ContextType.CURRENT_FILE
  ) {
    this.id = id;
    this.displayName = displayName;
    this.description = description;
    this.icon = icon;
    this.defaultContextType = defaultContextType;
  }

  /**
     * Initialize the mode. Subclasses can override to provide custom setup.
     * @param context Extension context
     */
  async initialize(context: vscode.ExtensionContext): Promise<void> {
    // Default: set up a status bar item if not already present
    if (!this.statusBarItem) {
      this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
      this.statusBarItem.text = `${this.icon} ${this.displayName}: Idle`;
      this.statusBarItem.tooltip = `Codessa ${this.displayName} Mode`;
      context.subscriptions.push(this.statusBarItem);
    }
  }

  /**
     * Dispose of any resources held by this mode. Subclasses can override.
     */
  async dispose(): Promise<void> {
    if (this.statusBarItem) {
      this.statusBarItem.dispose();
      this.statusBarItem = undefined;
    }
  }

  /**
   * Process a message in this mode (must be implemented by subclass)
   */
  abstract processMessage(message: string, agent: Agent, context: ContextSource): Promise<string>;

  /**
   * Get LLM parameters for this mode
   */
  getLLMParams(): LLMGenerateParams {
    return {
      prompt: '',
      modelId: '',
      temperature: 0.7,
      maxTokens: 2000,
      stopSequences: [],
      mode: 'task'
    };
  }
}


/**
 * Registry for operation modes
 */
class OperationModeRegistry {
  private modes: Map<string, IOperationMode> = new Map();
  private defaultModeId: string | undefined;

  /**
     * Register a mode
     */
  registerMode(mode: IOperationMode): void {
    this.modes.set(mode.id, mode);
  }

  /**
     * Get a mode by ID
     */
  getMode(id: string): IOperationMode | undefined {
    return this.modes.get(id);
  }

  /**
     * Get all registered modes
     */
  getAllModes(): IOperationMode[] {
    return Array.from(this.modes.values());
  }

  /**
     * Set the default mode by ID
     */
  setDefaultMode(id: string): void {
    if (this.modes.has(id)) {
      this.defaultModeId = id;
    } else {
      throw new Error(`Mode with id '${id}' not registered.`);
    }
  }

  /**
     * Get the default mode (if any)
     */
  getDefaultMode(): IOperationMode | undefined {
    return this.defaultModeId ? this.modes.get(this.defaultModeId) : undefined;
  }

  /**
     * Initialize all registered modes
     */
  async initializeModes(context: vscode.ExtensionContext): Promise<void> {
    for (const mode of this.modes.values()) {
      if (typeof (mode as any).initialize === 'function') {
        await (mode as any).initialize(context);
      }
    }
  }
}

// Export a singleton instance
export const operationModeRegistry = new OperationModeRegistry();