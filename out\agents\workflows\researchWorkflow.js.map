{"version": 3, "file": "researchWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/researchWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;AAeH,wDAwJC;AAKD,wEAwLC;AAhWD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAEtC,sEAAmG;AAGnG;;GAEG;AACH,SAAgB,sBAAsB,CACpC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,aAAoB,EACpB,YAAmB,EACnB,QAAiB,EAAE;IAEnB,eAAM,CAAC,IAAI,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;IAEnD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACrG,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,aAAa,CAAC,CAAC;IAE1H,oDAAoD;IACpD,MAAM,qBAAqB,GAAG;QAC5B,KAAK,CAAC,kBAAkB,CAAC,KAAa;YACpC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;oBAC9B,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAsB;wBAC9B,IAAI,EAAE,MAAoB;wBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;qBAC5B;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,2BAA2B,CAAC,KAAa;YAC7C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBACnC,IAAI,CAAC;wBACH,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,UAAU,CAAC,CAAC;wBACpE,OAAO,CAAC,CAAC,OAAO,CAAC;oBACnB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,eAAM,CAAC,IAAI,CAAC,aAAa,WAAW,CAAC,MAAM,yCAAyC,CAAC,CAAC;gBACtF,OAAO,WAAW,CAAC;YACrB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;gBAC5E,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,KAAK,CAAC,qBAAqB,CAAC,QAAa;YACvC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;oBACjC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,SAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;qBAC/B;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;KACF,CAAC;IAEF,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC9G,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAClG,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAGtF,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;IACzF,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtF,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAClH,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;QAChG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/F,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1F,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,yCAAyC;QACzC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,qBAAqB,KAAK,EAAE;gBAClC,MAAM,EAAE,uBAAuB;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,sCAAsC;YACtC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,cAAc;gBACjC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,eAAe;gBACvB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,iBAAiB;YACjB,wBAAwB;YACxB,oBAAoB;YACpB,gBAAgB;YAChB,aAAa;YACb,cAAc;YACd,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,UAA2B;QAC1C,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,uBAAuB,CAAC;QACvD,QAAQ,EAAE;YACR,aAAa,EAAE,qBAAqB;SACrC;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,8BAA8B,CAC5C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,aAAoB,EACpB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,wCAAwC,IAAI,EAAE,CAAC,CAAC;IAE5D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC9G,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,aAAa,CAAC,CAAC;IAC7H,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;IACjH,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;IACxG,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAClG,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,aAAa,CAAC,CAAC;IAC7H,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;IAC1F,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,MAAM,6BAA6B,GAAG;QACpC,KAAK,CAAC,qBAAqB,CAAC,UAAe;YACzC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBACnC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,YAAY,CAAC;qBAC7C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,eAAe,CAAC,UAAe;YACnC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBACnC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,SAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,YAAY,CAAC;qBAC7C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,KAAK,CAAC,gBAAgB,CAAC,WAAgB;YACrC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;oBACpC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,YAA0B;wBAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC;qBAC9C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC7C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,KAAK,CAAC,YAAY,CAAC,OAAY;YAC7B,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;oBAChC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,SAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC;qBAC1C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,uBAAuB,CAAC,KAAa;YACzC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBAChC,IAAI,CAAC;wBACH,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,UAAU,CAAC,CAAC;wBACrE,OAAO,CAAC,CAAC,OAAO,CAAC;oBACnB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,eAAM,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,qCAAqC,CAAC,CAAC;gBAC/E,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;gBACxE,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;KACF,CAAC;IAEF,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9F,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpH,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtH,EAAE,IAAI,EAAE,gCAAgC,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpH,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QAC5G,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QAClH,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QACjH,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1F,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,mCAAmC;QACnC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,2BAA2B,KAAK,EAAE;gBACxC,MAAM,EAAE,iBAAiB;gBACzB,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,sCAAsC;YACtC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,cAAc;gBACjC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,eAAe;gBACvB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,oBAAoB;YACpB,yBAAyB;YACzB,qBAAqB;YACrB,kBAAkB;YAClB,gBAAgB;YAChB,yBAAyB;YACzB,cAAc;YACd,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,UAA2B;QAC1C,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;QAC1C,QAAQ,EAAE;YACR,aAAa,EAAE,6BAA6B;SAC7C;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa Research Workflow\n *\n * This module provides workflow templates for research-focused tasks:\n * - Information gathering\n * - Source evaluation\n * - Data analysis\n * - Synthesis and reporting\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\nimport { codessaMemoryProvider as codessaMemoryManager } from '../../memory/codessa/codessaMemory';\nimport { MemorySource, MemoryType } from '../../memory/types';\n\n/**\n * Create a Research workflow for in-depth research tasks\n */\nexport function createResearchWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  researchAgent: Agent,\n  analysisAgent: Agent,\n  summaryAgent: Agent,\n  tools: ITool[] = []\n): GraphDefinition {\n  logger.info(`Creating Research workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const queryAnalysisNode = Codessa.createAgentNode('query-analysis', 'Query Analysis', researchAgent);\n  const informationRetrievalNode = Codessa.createAgentNode('information-retrieval', 'Information Retrieval', researchAgent);\n\n  // Create a memory manager for the research workflow\n  const researchMemoryManager = {\n    async storeResearchQuery(query: string): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(query),\n          metadata: {\n            source: 'user' as MemorySource,\n            type: 'text' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['research', 'query']\n          }\n        });\n        logger.info('Saved research query to memory');\n      } catch (error) {\n        logger.error('Failed to save research query to memory:', error);\n      }\n    },\n\n    async retrieveRelevantInformation(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5\n        });\n\n        const information = memories.map(m => {\n          try {\n            return JSON.parse(m.content);\n          } catch (parseError) {\n            logger.debug('Failed to parse memory content as JSON:', parseError);\n            return m.content;\n          }\n        });\n\n        logger.info(`Retrieved ${information.length} relevant information items from memory`);\n        return information;\n      } catch (error) {\n        logger.error('Failed to retrieve relevant information from memory:', error);\n        return [];\n      }\n    },\n\n    async storeResearchFindings(findings: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(findings),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'insight' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['research', 'findings']\n          }\n        });\n        logger.info('Saved research findings to memory');\n      } catch (error) {\n        logger.error('Failed to save research findings to memory:', error);\n      }\n    }\n  };\n\n  const sourceEvaluationNode = Codessa.createAgentNode('source-evaluation', 'Source Evaluation', analysisAgent);\n  const dataAnalysisNode = Codessa.createAgentNode('data-analysis', 'Data Analysis', analysisAgent);\n  const synthesisNode = Codessa.createAgentNode('synthesis', 'Synthesis', summaryAgent);\n\n\n  const conclusionNode = Codessa.createAgentNode('conclusion', 'Conclusion', summaryAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },\n    { name: 'query-to-retrieval', source: 'query-analysis', target: 'information-retrieval', type: 'default' },\n    { name: 'retrieval-to-evaluation', source: 'information-retrieval', target: 'source-evaluation', type: 'default' },\n    { name: 'evaluation-to-analysis', source: 'source-evaluation', target: 'data-analysis', type: 'default' },\n    { name: 'analysis-to-synthesis', source: 'data-analysis', target: 'synthesis', type: 'default' },\n    { name: 'synthesis-to-conclusion', source: 'synthesis', target: 'conclusion', type: 'default' },\n    { name: 'conclusion-to-output', source: 'conclusion', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect information retrieval to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `retrieval-to-tool-${index}`,\n        source: 'information-retrieval',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to data analysis\n      edges.push({\n        name: `tool-${index}-to-analysis`,\n        source: `tool-${index}`,\n        target: 'data-analysis',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      queryAnalysisNode,\n      informationRetrievalNode,\n      sourceEvaluationNode,\n      dataAnalysisNode,\n      synthesisNode,\n      conclusionNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'research' as OperationMode,\n    tags: ['research', 'analysis', 'information-retrieval'],\n    metadata: {\n      memoryManager: researchMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized academic research workflow\n */\nexport function createAcademicResearchWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  researchAgent: Agent,\n  analysisAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Academic Research workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const literatureReviewNode = Codessa.createAgentNode('literature-review', 'Literature Review', researchAgent);\n  const hypothesisFormulationNode = Codessa.createAgentNode('hypothesis-formulation', 'Hypothesis Formulation', researchAgent);\n  const methodologyDesignNode = Codessa.createAgentNode('methodology-design', 'Methodology Design', researchAgent);\n  const dataCollectionNode = Codessa.createAgentNode('data-collection', 'Data Collection', researchAgent);\n  const dataAnalysisNode = Codessa.createAgentNode('data-analysis', 'Data Analysis', analysisAgent);\n  const resultsInterpretationNode = Codessa.createAgentNode('results-interpretation', 'Results Interpretation', analysisAgent);\n  const conclusionNode = Codessa.createAgentNode('conclusion', 'Conclusion', analysisAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  const academicResearchMemoryManager = {\n    async storeLiteratureReview(literature: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(literature),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['academic', 'research', 'literature']\n          }\n        });\n        logger.info('Saved literature review to memory');\n      } catch (error) {\n        logger.error('Failed to save literature review to memory:', error);\n      }\n    },\n\n    async storeHypothesis(hypothesis: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(hypothesis),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'insight' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['academic', 'research', 'hypothesis']\n          }\n        });\n        logger.info('Saved hypothesis to memory');\n      } catch (error) {\n        logger.error('Failed to save hypothesis to memory:', error);\n      }\n    },\n\n    async storeMethodology(methodology: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(methodology),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'procedural' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['academic', 'research', 'methodology']\n          }\n        });\n        logger.info('Saved methodology to memory');\n      } catch (error) {\n        logger.error('Failed to save methodology to memory:', error);\n      }\n    },\n\n    async storeResults(results: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(results),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'insight' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['academic', 'research', 'results']\n          }\n        });\n        logger.info('Saved research results to memory');\n      } catch (error) {\n        logger.error('Failed to save research results to memory:', error);\n      }\n    },\n\n    async retrieveRelatedResearch(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5\n        });\n\n        const research = memories.map(m => {\n          try {\n            return JSON.parse(m.content);\n          } catch (parseError) {\n            logger.debug('Failed to parse research memory as JSON:', parseError);\n            return m.content;\n          }\n        });\n\n        logger.info(`Retrieved ${research.length} related research items from memory`);\n        return research;\n      } catch (error) {\n        logger.error('Failed to retrieve related research from memory:', error);\n        return [];\n      }\n    }\n  };\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-literature', source: 'input', target: 'literature-review', type: 'default' },\n    { name: 'literature-to-hypothesis', source: 'literature-review', target: 'hypothesis-formulation', type: 'default' },\n    { name: 'hypothesis-to-methodology', source: 'hypothesis-formulation', target: 'methodology-design', type: 'default' },\n    { name: 'methodology-to-data-collection', source: 'methodology-design', target: 'data-collection', type: 'default' },\n    { name: 'data-collection-to-analysis', source: 'data-collection', target: 'data-analysis', type: 'default' },\n    { name: 'analysis-to-interpretation', source: 'data-analysis', target: 'results-interpretation', type: 'default' },\n    { name: 'interpretation-to-conclusion', source: 'results-interpretation', target: 'conclusion', type: 'default' },\n    { name: 'conclusion-to-output', source: 'conclusion', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect data collection to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `data-collection-to-tool-${index}`,\n        source: 'data-collection',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to data analysis\n      edges.push({\n        name: `tool-${index}-to-analysis`,\n        source: `tool-${index}`,\n        target: 'data-analysis',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      literatureReviewNode,\n      hypothesisFormulationNode,\n      methodologyDesignNode,\n      dataCollectionNode,\n      dataAnalysisNode,\n      resultsInterpretationNode,\n      conclusionNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'research' as OperationMode,\n    tags: ['research', 'academic', 'analysis'],\n    metadata: {\n      memoryManager: academicResearchMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}