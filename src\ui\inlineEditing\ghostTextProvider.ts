/**
 * Ghost Text Provider - Advanced inline editing with AI suggestions
 * 
 * Provides ghost text previews, multi-line suggestions, and contextual inline chat
 * that integrates seamlessly with existing Codessa functionality.
 */

import * as vscode from 'vscode';
import { SupervisorAgent } from '../../agents/agentTypes/supervisorAgent';
import { MemoryManager } from '../../memory/memoryManager';
import { ToolRegistry } from '../../tools/toolRegistry';
import { logger } from '../../logger';

export interface GhostTextSuggestion {
  id: string;
  text: string;
  range: vscode.Range;
  confidence: number;
  reasoning: string;
  multiLine: boolean;
  contextAware: boolean;
}

export interface InlineEditContext {
  document: vscode.TextDocument;
  position: vscode.Position;
  selection: vscode.Selection;
  surroundingText: string;
  cursorContext: string;
  semanticContext: any;
}

export class GhostTextProvider implements vscode.InlineCompletionItemProvider {
  private supervisorAgent: SupervisorAgent;
  private memoryManager: MemoryManager;
  private toolRegistry: ToolRegistry;
  private disposables: vscode.Disposable[] = [];

  // Performance optimization
  private suggestionCache: Map<string, GhostTextSuggestion[]> = new Map();
  private readonly cacheExpiry = 30000; // 30 seconds
  private readonly maxCacheSize = 100;

  // Context tracking
  private lastPosition: vscode.Position | undefined;
  private lastDocument: vscode.TextDocument | undefined;
  private contextHistory: InlineEditContext[] = [];

  constructor(
    supervisorAgent: SupervisorAgent,
    memoryManager: MemoryManager,
    toolRegistry: ToolRegistry
  ) {
    this.supervisorAgent = supervisorAgent;
    this.memoryManager = memoryManager;
    this.toolRegistry = toolRegistry;

    this.registerProvider();
    this.setupEventListeners();
  }

  private registerProvider(): void {
    // Register as inline completion provider
    const provider = vscode.languages.registerInlineCompletionItemProvider(
      { scheme: 'file' },
      this
    );
    this.disposables.push(provider);

    logger.info('Ghost Text Provider registered for inline completions');
  }

  private setupEventListeners(): void {
    // Listen for cursor position changes
    const cursorListener = vscode.window.onDidChangeTextEditorSelection(event => {
      this.onCursorPositionChanged(event);
    });
    this.disposables.push(cursorListener);

    // Listen for document changes
    const documentListener = vscode.workspace.onDidChangeTextDocument(event => {
      this.onDocumentChanged(event);
    });
    this.disposables.push(documentListener);
  }

  /**
     * Main inline completion provider method
     */
  async provideInlineCompletionItems(
    document: vscode.TextDocument,
    position: vscode.Position,
    context: vscode.InlineCompletionContext,
    token: vscode.CancellationToken
  ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | undefined> {
    try {
      // Check if we should provide suggestions
      if (!this.shouldProvideSuggestions(document, position, context)) {
        return undefined;
      }

      // Build context for AI analysis
      const editContext = await this.buildInlineEditContext(document, position);

      // Check cache first
      const cacheKey = this.generateCacheKey(editContext);
      const cachedSuggestions = this.getCachedSuggestions(cacheKey);
      if (cachedSuggestions) {
        return this.convertToInlineCompletionItems(cachedSuggestions);
      }

      // Generate AI suggestions
      const suggestions = await this.generateAISuggestions(editContext, token);

      // Cache the suggestions
      this.cacheSuggestions(cacheKey, suggestions);

      // Convert to VS Code format
      return this.convertToInlineCompletionItems(suggestions);

    } catch (error) {
      logger.error(`Ghost text provider error: ${error}`);
      return undefined;
    }
  }

  /**
     * Determine if we should provide suggestions
     */
  private shouldProvideSuggestions(
    document: vscode.TextDocument,
    position: vscode.Position,
    context: vscode.InlineCompletionContext
  ): boolean {
    // Don't suggest in comments (unless specifically requested)
    const lineText = document.lineAt(position.line).text;
    const beforeCursor = lineText.substring(0, position.character);

    // Skip if in single-line comment
    if (beforeCursor.trim().startsWith('//')) {
      return false;
    }

    // Skip if in multi-line comment
    const textBeforePosition = document.getText(new vscode.Range(new vscode.Position(0, 0), position));
    const commentMatches = textBeforePosition.match(/\/\*[\s\S]*?\*\//g);
    const openComments = (textBeforePosition.match(/\/\*/g) || []).length;
    const closeComments = (textBeforePosition.match(/\*\//g) || []).length;

    if (openComments > closeComments) {
      return false;
    }

    // Don't suggest if user is actively typing rapidly
    if (this.isRapidTyping(position)) {
      return false;
    }

    return true;
  }

  /**
     * Build comprehensive context for inline editing
     */
  private async buildInlineEditContext(
    document: vscode.TextDocument,
    position: vscode.Position
  ): Promise<InlineEditContext> {
    const selection = vscode.window.activeTextEditor?.selection || new vscode.Selection(position, position);

    // Get surrounding text (5 lines before and after)
    const startLine = Math.max(0, position.line - 5);
    const endLine = Math.min(document.lineCount - 1, position.line + 5);
    const surroundingRange = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);
    const surroundingText = document.getText(surroundingRange);

    // Get cursor context (current line and position)
    const currentLine = document.lineAt(position.line);
    const beforeCursor = currentLine.text.substring(0, position.character);
    const afterCursor = currentLine.text.substring(position.character);
    const cursorContext = `${beforeCursor}|${afterCursor}`;

    // Get semantic context using existing tools
    let semanticContext = {};
    try {
      const codeIntelTool = this.toolRegistry.getTool('codeIntel');
      if (codeIntelTool) {
        const semanticResult = await codeIntelTool.execute('getContext', {
          filePath: document.uri.fsPath,
          position: { line: position.line, character: position.character }
        });
        if (semanticResult.success) {
          semanticContext = semanticResult.output;
        }
      }
    } catch (error) {
      logger.warn(`Failed to get semantic context: ${error}`);
    }

    return {
      document,
      position,
      selection,
      surroundingText,
      cursorContext,
      semanticContext
    };
  }

  /**
     * Generate AI-powered suggestions
     */
  private async generateAISuggestions(
    context: InlineEditContext,
    token: vscode.CancellationToken
  ): Promise<GhostTextSuggestion[]> {
    const suggestions: GhostTextSuggestion[] = [];

    try {
      // Use existing SupervisorAgent for AI generation
      const prompt = this.buildSuggestionPrompt(context);

      const result = await this.supervisorAgent.run({
        prompt,
        mode: 'edit'
      }, {
        workspace: {
          currentFile: context.document.uri.toString(),
          selection: {
            text: context.document.getText(context.selection),
            range: { start: 0, end: 0 }
          },
          workspaceFolders: Array.from(vscode.workspace.workspaceFolders ?? []).map(folder => folder.uri.toString())
        }
      });

      if (result.success && result.output) {
        // Parse AI response into suggestions
        const parsedSuggestions = this.parseAISuggestions(result.output, context);
        suggestions.push(...parsedSuggestions);
      }

    } catch (error) {
      logger.error(`AI suggestion generation failed: ${error}`);
    }

    // Add fallback suggestions if no AI suggestions
    if (suggestions.length === 0) {
      suggestions.push(...this.generateFallbackSuggestions(context));
    }

    return suggestions;
  }

  /**
     * Build prompt for AI suggestion generation
     */
  private buildSuggestionPrompt(context: InlineEditContext): string {
    return `
# Inline Code Completion Request

## Context:
- File: ${context.document.uri.fsPath}
- Language: ${context.document.languageId}
- Position: Line ${context.position.line + 1}, Column ${context.position.character + 1}

## Current Code Context:
\`\`\`${context.document.languageId}
${context.surroundingText}
\`\`\`

## Cursor Position:
${context.cursorContext}

## Semantic Context:
${JSON.stringify(context.semanticContext, null, 2)}

## Task:
Provide intelligent code completion suggestions for the cursor position marked with |.
Consider:
1. Code context and patterns
2. Language-specific best practices
3. Variable scope and types
4. Function signatures and return types
5. Common coding patterns

## Response Format:
Provide 1-3 completion suggestions in JSON format:
{
  "suggestions": [
    {
      "text": "completion text",
      "confidence": 0.9,
      "reasoning": "why this suggestion makes sense",
      "multiLine": false
    }
  ]
}
        `;
  }

  /**
     * Parse AI response into structured suggestions
     */
  private parseAISuggestions(aiResponse: string, context: InlineEditContext): GhostTextSuggestion[] {
    const suggestions: GhostTextSuggestion[] = [];

    try {
      // Try to parse JSON response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (parsed.suggestions && Array.isArray(parsed.suggestions)) {
          for (let i = 0; i < parsed.suggestions.length; i++) {
            const suggestion = parsed.suggestions[i];
            suggestions.push({
              id: `ai_${Date.now()}_${i}`,
              text: suggestion.text || '',
              range: new vscode.Range(context.position, context.position),
              confidence: suggestion.confidence || 0.5,
              reasoning: suggestion.reasoning || 'AI generated suggestion',
              multiLine: suggestion.multiLine || false,
              contextAware: true
            });
          }
        }
      }
    } catch (error) {
      logger.warn(`Failed to parse AI suggestions: ${error}`);
      // Fallback: treat entire response as a single suggestion
      if (aiResponse.trim().length > 0) {
        suggestions.push({
          id: `ai_fallback_${Date.now()}`,
          text: aiResponse.trim(),
          range: new vscode.Range(context.position, context.position),
          confidence: 0.3,
          reasoning: 'Fallback AI suggestion',
          multiLine: aiResponse.includes('\n'),
          contextAware: true
        });
      }
    }

    return suggestions;
  }

  /**
     * Generate fallback suggestions when AI fails
     */
  private generateFallbackSuggestions(context: InlineEditContext): GhostTextSuggestion[] {
    const suggestions: GhostTextSuggestion[] = [];
    const currentLine = context.document.lineAt(context.position.line);
    const beforeCursor = currentLine.text.substring(0, context.position.character);

    // Simple pattern-based suggestions
    if (beforeCursor.endsWith('console.')) {
      suggestions.push({
        id: `fallback_console_${Date.now()}`,
        text: 'log()',
        range: new vscode.Range(context.position, context.position),
        confidence: 0.8,
        reasoning: 'Common console method',
        multiLine: false,
        contextAware: false
      });
    }

    if (beforeCursor.endsWith('if (')) {
      suggestions.push({
        id: `fallback_if_${Date.now()}`,
        text: 'condition) {\n    \n}',
        range: new vscode.Range(context.position, context.position),
        confidence: 0.7,
        reasoning: 'If statement completion',
        multiLine: true,
        contextAware: false
      });
    }

    return suggestions;
  }

  // Helper methods for caching and performance
  private generateCacheKey(context: InlineEditContext): string {
    return `${context.document.uri.fsPath}_${context.position.line}_${context.position.character}_${context.cursorContext}`;
  }

  private getCachedSuggestions(key: string): GhostTextSuggestion[] | null {
    const cached = this.suggestionCache.get(key);
    if (cached) {
      return cached;
    }
    return null;
  }

  private cacheSuggestions(key: string, suggestions: GhostTextSuggestion[]): void {
    // Implement LRU cache
    if (this.suggestionCache.size >= this.maxCacheSize) {
      const firstKey = this.suggestionCache.keys().next().value;
      if (firstKey) {
        this.suggestionCache.delete(firstKey);
      }
    }
    this.suggestionCache.set(key, suggestions);
  }

  private convertToInlineCompletionItems(suggestions: GhostTextSuggestion[]): vscode.InlineCompletionItem[] {
    return suggestions.map(suggestion => ({
      insertText: suggestion.text,
      range: suggestion.range,
      command: {
        command: 'codessa.acceptSuggestion',
        title: 'Accept Suggestion',
        arguments: [suggestion]
      }
    }));
  }

  private isRapidTyping(position: vscode.Position): boolean {
    // Simple rapid typing detection
    if (this.lastPosition && this.lastDocument) {
      const timeDiff = Date.now() - (this.lastPosition as any).timestamp;
      return timeDiff < 100; // Less than 100ms since last position change
    }
    return false;
  }

  private onCursorPositionChanged(event: vscode.TextEditorSelectionChangeEvent): void {
    this.lastPosition = event.textEditor.selection.active;
    this.lastDocument = event.textEditor.document;
    (this.lastPosition as any).timestamp = Date.now();
  }

  private onDocumentChanged(event: vscode.TextDocumentChangeEvent): void {
    // Clear cache for changed document
    const docPath = event.document.uri.fsPath;
    for (const [key] of this.suggestionCache) {
      if (key.startsWith(docPath)) {
        this.suggestionCache.delete(key);
      }
    }
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.suggestionCache.clear();
  }
}
