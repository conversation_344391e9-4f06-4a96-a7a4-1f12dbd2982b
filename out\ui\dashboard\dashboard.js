"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardPanel = void 0;
const vscode = __importStar(require("vscode"));
const utils_1 = require("../utilities/utils");
const logger_1 = require("../../logger");
const llmService_1 = require("../../llm/llmService");
const agentManager_1 = require("../../agents/agentUtilities/agentManager");
const toolRegistry_1 = require("../../tools/toolRegistry");
const providerSettingsSection_1 = require("../settings/sections/providerSettingsSection");
/**
 * A dashboard webview that shows activity, analytics, and health status
 */
class DashboardPanel {
    static currentPanel;
    static viewType = 'codessaDashboard';
    _panel;
    _extensionUri;
    _context;
    _disposables = [];
    _refreshInterval;
    /**
       * Create or show a dashboard panel
       */
    static createOrShow(extensionUri, context) {
        logger_1.Logger.instance.info('Creating or showing dashboard panel...');
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;
        // If we already have a panel, show it
        if (DashboardPanel.currentPanel) {
            DashboardPanel.currentPanel._panel.reveal(column);
            return DashboardPanel.currentPanel;
        }
        // Otherwise, create a new panel
        logger_1.Logger.instance.info('Creating new dashboard panel...');
        try {
            const panel = vscode.window.createWebviewPanel(DashboardPanel.viewType, 'Codessa Dashboard', column || vscode.ViewColumn.One, {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'media'),
                    vscode.Uri.joinPath(extensionUri, 'resources')
                ]
            });
            logger_1.Logger.instance.info('Dashboard panel created successfully.');
            const dashboardPanel = new DashboardPanel(panel, extensionUri, context);
            // Register panel with extension context
            context.subscriptions.push(panel);
            return dashboardPanel;
        }
        catch (error) {
            logger_1.Logger.instance.error('Error creating dashboard panel:', error);
            vscode.window.showErrorMessage('Failed to create dashboard panel. Check logs for details.');
            throw error;
        }
    }
    constructor(panel, extensionUri, context) {
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._context = context;
        // Set initial HTML content
        this._update();
        // Set up auto-refresh (every 30 seconds)
        this._refreshInterval = setInterval(() => {
            this._update();
        }, 30000);
        // Handle panel disposal
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
        // Handle messages from the webview
        this._panel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'refresh':
                    this._update();
                    break;
                case 'openSettings':
                    vscode.commands.executeCommand('codessa.openSettings');
                    break;
                case 'openProviderSettings':
                    if (message.providerId) {
                        // Open settings for a specific provider
                        const provider = await llmService_1.llmService.getProvider(message.providerId);
                        if (provider) {
                            providerSettingsSection_1.ProviderSettingsPanel.createOrShow(this._extensionUri, message.providerId, this._context);
                        }
                        else {
                            vscode.window.showErrorMessage(`Provider ${message.providerId} not found.`);
                        }
                    }
                    else {
                        // Open provider selection dialog
                        vscode.commands.executeCommand('codessa.openProviderSettings');
                    }
                    break;
                case 'openAgent':
                    vscode.commands.executeCommand('codessa.openAgentDetailsPanel', message.agentId);
                    break;
                case 'createAgent':
                    vscode.commands.executeCommand('codessa.addAgent');
                    break;
                case 'showLogs':
                    vscode.commands.executeCommand('codessa.showLogs');
                    break;
            }
        }, null, this._disposables);
        // Cache the current panel
        DashboardPanel.currentPanel = this;
    }
    /**
       * Update dashboard content
       */
    async _update() {
        logger_1.Logger.instance.info('Updating dashboard content...');
        try {
            const webview = this._panel.webview;
            this._panel.title = 'Codessa Dashboard';
            // Gather data for the dashboard
            logger_1.Logger.instance.info('Gathering dashboard data...');
            const dashboardData = await this._getDashboardData();
            logger_1.Logger.instance.info('Dashboard data gathered successfully.');
            // Update HTML content
            logger_1.Logger.instance.info('Generating webview HTML content...');
            webview.html = this._getWebviewContent(webview, dashboardData);
            logger_1.Logger.instance.info('Dashboard content updated successfully.');
        }
        catch (error) {
            logger_1.Logger.instance.error('Error updating dashboard content:', error);
            vscode.window.showErrorMessage('Failed to update dashboard content. Check logs for details.');
        }
    }
    /**
       * Gather data for the dashboard
       */
    async _getDashboardData() {
        try {
            // Get provider status
            const providers = llmService_1.llmService.listProviderIds();
            const configuredProviders = llmService_1.llmService.getConfiguredProviders();
            const providerStatus = await Promise.all(providers.map(async (id) => {
                const provider = await llmService_1.llmService.getProvider(id);
                return {
                    id,
                    configured: provider ? provider.isConfigured() : false,
                    isDefault: false, // Will be set below if applicable
                };
            }));
            // Get default provider
            const defaultProvider = await llmService_1.llmService.getDefaultProvider();
            if (defaultProvider) {
                const defaultProviderStatus = providerStatus.find(p => p.id === defaultProvider.providerId);
                if (defaultProviderStatus) {
                    defaultProviderStatus.isDefault = true;
                }
            }
            // Get agents
            const agents = agentManager_1.AgentManager.getInstance().getAllAgents().map((agent) => ({
                id: agent.id,
                name: agent.name,
                description: agent.description || '',
                toolCount: agent.tools.size,
                provider: agent.llmConfig?.provider || 'unknown',
                model: agent.llmConfig?.modelId || 'unknown'
            }));
            // Get tools
            const tools = toolRegistry_1.ToolRegistry.instance.getAllTools().map(tool => ({
                id: tool.id,
                name: tool.name || tool.id,
                description: tool.description || ''
            }));
            // Return combined data
            return {
                providers: {
                    total: providers.length,
                    configured: configuredProviders.length,
                    list: providerStatus
                },
                agents: {
                    total: agents.length,
                    list: agents
                },
                tools: {
                    total: tools.length,
                    list: tools
                },
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            logger_1.Logger.instance.error('Error gathering dashboard data:', error);
            // Return minimal data
            return {
                error: `Failed to gather data: ${error}`,
                timestamp: new Date().toISOString()
            };
        }
    }
    /**
       * Generate the webview HTML content
       */
    _getWebviewContent(webview, data) {
        logger_1.Logger.instance.info('Generating webview HTML content...');
        try {
            // Get resource URIs
            logger_1.Logger.instance.info('Getting resource URIs...');
            const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'dashboard.js'));
            logger_1.Logger.instance.info(`Script URI: ${scriptUri}`);
            const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'dashboard.css'));
            logger_1.Logger.instance.info(`Style URI: ${styleUri}`);
            const logoUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'resources', 'codessa-logo.png'));
            logger_1.Logger.instance.info(`Logo URI: ${logoUri}`);
            const nonce = (0, utils_1.getNonce)();
            logger_1.Logger.instance.info('Resource URIs generated successfully.');
            return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Codessa Dashboard</title>
                <link rel="stylesheet" href="${styleUri}">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src ${webview.cspSource} https:; script-src 'nonce-${nonce}'; style-src ${webview.cspSource} 'unsafe-inline'; font-src ${webview.cspSource};">
            </head>
            <body>
                <div class="dashboard-container">
                    <header class="dashboard-header">
                        <div class="logo-container">
                            <img src="${logoUri}" alt="Codessa Logo" class="logo" />
                            <h1>Codessa Dashboard</h1>
                        </div>
                        <div class="actions">
                            <button id="btn-refresh" title="Refresh dashboard" class="icon-button">
                                <span style="font-family: 'codicon'; font-size: 16px;">&#xeb37;</span> Refresh
                            </button>
                            <button id="btn-settings" title="Open settings" class="icon-button">
                                <span style="font-family: 'codicon'; font-size: 16px;">&#xeb52;</span> Settings
                            </button>
                            <button id="btn-logs" title="Show logs" class="icon-button">
                                <span style="font-family: 'codicon'; font-size: 16px;">&#xea7f;</span> Logs
                            </button>
                        </div>
                    </header>

                    <div class="dashboard-content">
                        <div class="dashboard-row">
                            <div class="dashboard-card status-card">
                                <h2>System Status</h2>
                                <div class="status-indicators">
                                    <div class="status-item ${data.providers?.configured > 0 ? 'status-ok' : 'status-warning'}">
                                        <span class="status-icon"></span>
                                        <span class="status-label">LLM Providers</span>
                                        <span class="status-value">${data.providers?.configured || 0}/${data.providers?.total || 0} configured</span>
                                    </div>
                                    <div class="status-item ${data.agents?.total > 0 ? 'status-ok' : 'status-warning'}">
                                        <span class="status-icon"></span>
                                        <span class="status-label">Agents</span>
                                        <span class="status-value">${data.agents?.total || 0} available</span>
                                    </div>
                                    <div class="status-item status-ok">
                                        <span class="status-icon"></span>
                                        <span class="status-label">Tools</span>
                                        <span class="status-value">${data.tools?.total || 0} available</span>
                                    </div>
                                </div>
                                <p class="last-updated">Last updated: <span id="timestamp"></span></p>
                            </div>
                        </div>

                        <div class="dashboard-row">
                            <div class="dashboard-card">
                                <h2>Agents</h2>
                                <div class="card-actions">
                                    <button id="btn-create-agent" class="btn primary"><span style="font-family: 'codicon'; font-size: 14px;">&#xea60;</span> Create Agent</button>
                                </div>
                                <div class="agents-list" id="agents-list">
                                    <!-- Agents will be inserted here by JavaScript -->
                                    ${data.error ? `<div class="error-message">${data.error}</div>` : ''}
                                    ${!data.error && (!data.agents || data.agents.total === 0) ?
                '<div class="empty-message">No agents configured yet. Create your first agent to get started.</div>' : ''}
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-row">
                            <div class="dashboard-card">
                                <h2>LLM Providers</h2>
                                <div class="provider-list" id="provider-list">
                                    <!-- Providers will be inserted here by JavaScript -->
                                </div>
                            </div>

                            <div class="dashboard-card">
                                <h2>Available Tools</h2>
                                <div class="tools-list" id="tools-list">
                                    <!-- Tools will be inserted here by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script nonce="${nonce}">
                    // Dashboard data
                    const dashboardData = ${JSON.stringify(data)};
                </script>
                <script nonce="${nonce}" src="${scriptUri}"></script>
            </body>
            </html>`;
        }
        catch (error) {
            logger_1.Logger.instance.error('Error generating webview HTML content:', error);
            return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <title>Error</title>
            </head>
            <body>
                <h1>Error</h1>
                <p>Failed to generate dashboard content. Check logs for details.</p>
                <pre>${error}</pre>
            </body>
            </html>`;
        }
    }
    /**
       * Clean up resources
       */
    dispose() {
        DashboardPanel.currentPanel = undefined;
        if (this._refreshInterval) {
            clearInterval(this._refreshInterval);
        }
        this._panel.dispose();
        while (this._disposables.length) {
            const disposable = this._disposables.pop();
            if (disposable) {
                disposable.dispose();
            }
        }
    }
}
exports.DashboardPanel = DashboardPanel;
//# sourceMappingURL=dashboard.js.map