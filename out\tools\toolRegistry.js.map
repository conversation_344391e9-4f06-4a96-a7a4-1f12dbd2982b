{"version": 3, "file": "toolRegistry.js", "sourceRoot": "", "sources": ["../../src/tools/toolRegistry.ts"], "names": [], "mappings": ";;;AAEA,yCAA+C;AAC/C,2DAAwD;AACxD,qDAAkD;AAClD,+DAA4D;AAC5D,mDAAgD;AAChD,+CAA4C;AAC5C,6CAAgD;AAChD,2DAAwD;AACxD,6DAA0D;AAC1D,yDAAsD;AACtD,uCAAoC;AACpC,2DAAwD;AACxD,iEAA8D;AAC9D,6DAA0D;AAC1D,+DAA4D;AAC5D,sCAAmC;AACnC,2DAA6D;AAE7D,wBAAwB;AACxB,6EAA0H;AAC1H,qEAAkG;AAClG,yDAA6I;AAC7I,2EAA6G;AAC7G,uDAAkI;AAClI,+EAAsH;AACtH,6DAAkH;AAClH,yDAAqH;AAErH,qCAAqC;AACrC,yDAAwD;AACxD,yDAA+D;AAC/D,qEAAkE;AAClE,+DAAsF;AAEtF,mCAAmC;AACnC,0BAA0B;AAC1B,uDAAoD;AAEpD,gCAAgC;AAChC,uEAA8D;AAC9D,yDAA8C;AAC9C,2DAAwD;AAExD,gBAAgB;AAChB,uEAAoE;AACpE,6DAA0D;AAE1D;;;GAGG;AACH,+DAA+D;AAC/D,MAAa,YAAY;IACf,MAAM,CAAC,SAAS,CAA2B;IAC3C,KAAK,GAAG,IAAI,GAAG,EAAiB,CAAC;IACjC,WAAW,GAAG,KAAK,CAAC;IAE5B;QACE,sDAAsD;IACxD,CAAC;IAED;;SAEK;IACE,MAAM,KAAK,QAAQ;QACxB,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAC5B,gDAAgD;YAChD,YAAY,CAAC,SAAS,GAAG,IAAI,YAAY,EAAE,CAAC;YAC5C,4DAA4D;QAC9D,CAAC;QACD,OAAO,YAAY,CAAC,SAAS,CAAC;IAChC,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,OAAgC;QAC7D,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,oDAAoD;QAC5F,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6FAA6F,CAAC,CAAC;YACpH,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,MAAM,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,OAAO,CAAC,MAAc;QAClC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;QAChG,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;QAChG,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,YAAY,CAAC,IAAW;QACpC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;QAChG,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,EAAE,uCAAuC,CAAC,CAAC;QACxF,CAAC;QACD,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC/C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,UAAU,CAAC,OAAgC;QACvD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;YAClF,OAAO;QACT,CAAC;QAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAEjE,kCAAkC;QAClC,MAAM,6BAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAExC,0BAA0B;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,gDAAgD;QAChD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAExD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC;QAChF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED;;SAEK;IACG,oBAAoB;QAC1B,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAEtD,6CAA6C;YAC7C,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAiB,EAAE,EAAE,SAAS,CAAC,CAAC;YAC5D,IAAI,CAAC,kBAAkB,CAAC,IAAI,+BAAc,EAAE,EAAE,YAAY,CAAC,CAAC;YAC5D,IAAI,CAAC,kBAAkB,CAAC,IAAI,yCAAmB,EAAE,EAAE,kBAAkB,CAAC,CAAC;YACvE,IAAI,CAAC,kBAAkB,CAAC,IAAI,6BAAa,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,yBAAW,EAAE,EAAE,SAAS,CAAC,CAAC;YACtD,IAAI,CAAC,kBAAkB,CAAC,IAAA,6BAAgB,EAAC,6BAAa,CAAC,EAAE,mBAAmB,CAAC,CAAC;YAC9E,IAAI,CAAC,kBAAkB,CAAC,IAAI,uCAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,mCAAgB,EAAE,EAAE,cAAc,CAAC,CAAC;YAChE,IAAI,CAAC,kBAAkB,CAAC,IAAI,iBAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAiB,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC3D,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAoB,EAAE,EAAE,WAAW,CAAC,CAAC;YACjE,IAAI,CAAC,kBAAkB,CAAC,IAAI,uCAAkB,EAAE,EAAE,SAAS,CAAC,CAAC;YAC7D,IAAI,CAAC,kBAAkB,CAAC,IAAI,yCAAmB,EAAE,EAAE,kBAAkB,CAAC,CAAC;YACvE,IAAI,CAAC,kBAAkB,CAAC,IAAI,iCAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,0CAAsB,EAAE,EAAE,cAAc,CAAC,CAAC;YACtE,IAAI,CAAC,kBAAkB,CAAC,IAAI,4BAAiB,EAAE,EAAE,MAAM,CAAC,CAAC;YAEzD,0CAA0C;YAC1C,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAe,EAAE,EAAE,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,6CAAgB,EAAE,EAAE,UAAU,CAAC,CAAC;YAC5D,IAAI,CAAC,kBAAkB,CAAC,IAAI,8CAAiB,EAAE,EAAE,eAAe,CAAC,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,IAAI,iDAAoB,EAAE,EAAE,cAAc,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,uCAAoB,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,0CAAuB,EAAE,EAAE,WAAW,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAwB,EAAE,EAAE,YAAY,CAAC,CAAC;YACtE,IAAI,CAAC,kBAAkB,CAAC,IAAI,iDAA8B,EAAE,EAAE,QAAQ,CAAC,CAAC;YACxE,IAAI,CAAC,kBAAkB,CAAC,IAAI,wCAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,0CAAiB,EAAE,EAAE,eAAe,CAAC,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,yCAAa,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,yCAAa,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,8BAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,kBAAkB,CAAC,IAAI,+BAAa,EAAE,EAAE,QAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,IAAI,mCAAiB,EAAE,EAAE,YAAY,CAAC,CAAC;YAC/D,IAAI,CAAC,kBAAkB,CAAC,IAAI,+BAAa,EAAE,EAAE,QAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,IAAI,4BAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,oCAAkB,EAAE,EAAE,aAAa,CAAC,CAAC;YACjE,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAa,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAc,EAAE,EAAE,YAAY,CAAC,CAAC;YAC5D,IAAI,CAAC,kBAAkB,CAAC,IAAI,6CAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,mDAAqB,EAAE,EAAE,mBAAmB,CAAC,CAAC;YAC1E,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAgB,CAAC,6BAAa,CAAC,EAAE,cAAc,CAAC,CAAC;YAC7E,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAgB,CAAC,6BAAa,CAAC,EAAE,cAAc,CAAC,CAAC;YAC7E,IAAI,CAAC,kBAAkB,CAAC,IAAI,kCAAa,CAAC,6BAAa,CAAC,EAAE,WAAW,CAAC,CAAC;YACvE,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAuB,CAAC,6BAAa,CAAC,EAAE,WAAW,CAAC,CAAC;YACjF,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,wCAAqB,EAAE,EAAE,YAAY,CAAC,CAAC;YACnE,IAAI,CAAC,kBAAkB,CAAC,IAAI,kCAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,sCAAmB,EAAE,EAAE,cAAc,CAAC,CAAC;YACnE,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAyB,EAAE,EAAE,uBAAuB,CAAC,CAAC;YAClF,IAAI,CAAC,kBAAkB,CAAC,IAAI,+CAAsB,EAAE,EAAE,oBAAoB,CAAC,CAAC;YAC5E,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAqB,EAAE,EAAE,mBAAmB,CAAC,CAAC;YAC1E,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAsB,EAAE,EAAE,oBAAoB,CAAC,CAAC;YAC5E,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAiB,EAAE,EAAE,eAAe,CAAC,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,IAAI,2BAAQ,EAAE,EAAE,UAAU,CAAC,CAAC;YACpD,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAiB,EAAE,EAAE,eAAe,CAAC,CAAC;YAElE,sCAAsC;YACtC,IAAI,CAAC,kBAAkB,CAAC,IAAI,wCAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,0CAAiB,EAAE,EAAE,eAAe,CAAC,CAAC;YAElE,wCAAwC;YACxC,IAAI,CAAC,kBAAkB,CAAC,IAAI,uCAAoB,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,0CAAuB,EAAE,EAAE,WAAW,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAwB,EAAE,EAAE,YAAY,CAAC,CAAC;YACtE,IAAI,CAAC,kBAAkB,CAAC,IAAI,iDAA8B,EAAE,EAAE,QAAQ,CAAC,CAAC;YAExE,wCAAwC;YACxC,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,yCAAa,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,yCAAa,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAE9D,8BAA8B;YAC9B,IAAI,CAAC,kBAAkB,CAAC,IAAI,8BAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,kBAAkB,CAAC,IAAI,+BAAa,EAAE,EAAE,QAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,IAAI,mCAAiB,EAAE,EAAE,YAAY,CAAC,CAAC;YAC/D,IAAI,CAAC,kBAAkB,CAAC,IAAI,+BAAa,EAAE,EAAE,QAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,IAAI,4BAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,oCAAkB,EAAE,EAAE,aAAa,CAAC,CAAC;YAEjE,0CAA0C;YAC1C,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAa,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAc,EAAE,EAAE,YAAY,CAAC,CAAC;YAC5D,IAAI,CAAC,kBAAkB,CAAC,IAAI,6CAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,mDAAqB,EAAE,EAAE,mBAAmB,CAAC,CAAC;YAE1E,iCAAiC;YACjC,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAgB,CAAC,6BAAa,CAAC,EAAE,cAAc,CAAC,CAAC;YAC7E,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAgB,CAAC,6BAAa,CAAC,EAAE,cAAc,CAAC,CAAC;YAC7E,IAAI,CAAC,kBAAkB,CAAC,IAAI,kCAAa,CAAC,6BAAa,CAAC,EAAE,WAAW,CAAC,CAAC;YACvE,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAuB,CAAC,6BAAa,CAAC,EAAE,WAAW,CAAC,CAAC;YAEjF,8BAA8B;YAC9B,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,wCAAqB,EAAE,EAAE,YAAY,CAAC,CAAC;YACnE,IAAI,CAAC,kBAAkB,CAAC,IAAI,kCAAe,EAAE,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,CAAC,kBAAkB,CAAC,IAAI,sCAAmB,EAAE,EAAE,cAAc,CAAC,CAAC;YAEnE,wCAAwC;YACxC,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;YACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAyB,EAAE,EAAE,uBAAuB,CAAC,CAAC;YAClF,IAAI,CAAC,kBAAkB,CAAC,IAAI,+CAAsB,EAAE,EAAE,oBAAoB,CAAC,CAAC;YAE5E,2CAA2C;YAC3C,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAqB,EAAE,EAAE,mBAAmB,CAAC,CAAC;YAC1E,IAAI,CAAC,kBAAkB,CAAC,IAAI,4CAAsB,EAAE,EAAE,oBAAoB,CAAC,CAAC;YAE5E,uCAAuC;YACvC,IAAI,CAAC,kBAAkB,CAAC,IAAI,2CAAiB,EAAE,EAAE,eAAe,CAAC,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,IAAI,2BAAQ,EAAE,EAAE,UAAU,CAAC,CAAC;YACpD,IAAI,CAAC,kBAAkB,CAAC,IAAI,qCAAiB,EAAE,EAAE,eAAe,CAAC,CAAC;YAElE,wCAAwC;YACxC,IAAI,CAAC,kBAAkB,CAAC,IAAI,iDAAuB,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAC1E,IAAI,CAAC,kBAAkB,CAAC,IAAI,uCAAkB,EAAE,EAAE,aAAa,CAAC,CAAC;YAEjE,wCAAwC;YACxC,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAExC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,IAAI,6DAA6D,CAAC,CAAC;QACpH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,gCAAgC;QACtC,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;YACjE,MAAM,EAAE,uBAAuB,EAAE,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;YAEzE,iCAAiC;YACjC,IAAI,CAAC,kBAAkB,CAAC,IAAI,mBAAmB,EAAE,EAAE,kBAAkB,CAAC,CAAC;YAEvE,sCAAsC;YACtC,IAAI,CAAC,kBAAkB,CAAC,IAAI,uBAAuB,EAAE,EAAE,uBAAuB,CAAC,CAAC;YAEhF,wDAAwD;YACxD,IAAI,CAAC,qCAAqC,EAAE,CAAC;YAE7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;SAEK;IACG,qCAAqC;QAC3C,iEAAiE;QACjE,MAAM,oBAAoB,GAAG;YAC3B,kBAAkB;YAClB,uBAAuB;YACvB,iBAAiB;SAClB,CAAC;QAEF,4EAA4E;QAC5E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7F,CAAC;IAED;;SAEK;IACE,kBAAkB,CAAC,IAAW,EAAE,EAAU;QAC/C,kDAAkD;QAClD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE;YAChC,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,KAAK;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,YAAY,CAAC,IAAW;QACtB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YAC5B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,EAAE,uCAAuC,CAAC,CAAC;QACxF,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC9B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAEzF,qDAAqD;QACrD,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,EAAE;gBAC5D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,UAAU,MAAM,MAAM,CAAC,WAAW,IAAI,gBAAgB,EAAE,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACL,OAAO,CAAC,MAAc;QACpB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,MAAM,iCAAiC,cAAc,EAAE,CAAC,CAAC;QACjG,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;SAEK;IACL,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;SAEK;IACL,aAAa,CAAC,OAAiB;QAC7B,MAAM,MAAM,GAAG,IAAI,GAAG,EAAiB,CAAC;QAExC,KAAK,MAAM,EAAE,IAAI,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9B,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACL,iBAAiB;QACf,MAAM,gBAAgB,GAAG;YACvB,SAAS;YACT,YAAY;YACZ,mBAAmB;YACnB,QAAQ;YACR,kBAAkB;YAClB,WAAW;YACX,KAAK;YACL,WAAW;YACX,cAAc;YACd,cAAc;SACf,CAAC;QAEF,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAC9C,CAAC;IAED;;SAEK;IACL,kBAAkB,CAAC,QAAgB;QACjC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACnD,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAC3B,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,iBAAiB;QACf,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;CACF;AAxXD,oCAwXC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool } from './tool.ts.backup';\nimport { DocumentationTool } from './docsTool';\nimport { DirectoryListTool } from './directoryListTool';\nimport { CodeSearchTool } from './codeSearchTool';\nimport { TerminalCommandTool } from './terminalCommandTool';\nimport { WebSearchTool } from './webSearchTool';\nimport { WebReadTool } from './webReadTool';\nimport { createMemoryTool } from './memoryTool';\nimport { memoryManager } from '../memory/memoryManager';\nimport { BrowserPreviewTool } from './browserPreviewTool';\nimport { DeployWebAppTool } from './deployWebAppTool';\nimport { GitTool } from './gitTool';\nimport { EditorActionsTool } from './editorActionsTool';\nimport { CodeIntelligenceTool } from './codeIntelligenceTool';\nimport { CodeGenerationTool } from './codeGenerationTool';\nimport { LintDiagnosticsTool } from './lintDiagnosticsTool';\nimport { Logger } from '../logger';\nimport { EnhancedFileSystemTool } from './enhancedFileTools';\n\n// Import advanced tools\nimport { ExplainCodeTool, DocumentCodeTool, GenerateTestsTool, MultiFileCodeGenTool } from './advancedCodeGenerationTool';\nimport { FuzzySearchTool, SemanticSearchTool, SearchPreviewTool } from './advancedCodeSearchTool';\nimport { DocumentationGenTool, DocumentationSearchTool, DocumentationSummaryTool, DocumentationVisualizationTool } from './advancedDocsTool';\nimport { MultiCursorTool, ClipboardTool, BatchEditTool, FindReplaceTool } from './advancedEditorActionsTool';\nimport { GitStashTool, GitRevertTool, GitCherryPickTool, GitRebaseTool, GitTagTool, GitBranchGraphTool } from './advancedGitTool';\nimport { BatchLintTool, AutoFixAllTool, LintSummaryTool, DiagnosticsSearchTool } from './advancedLintDiagnosticsTool';\nimport { MemorySearchTool, MemoryUpdateTool, MemoryTagTool, MemoryVisualizationTool } from './advancedMemoryTool';\nimport { WebMultiSearchTool, WebContentExtractTool, WebSnapshotTool, WebDeployStatusTool } from './advancedWebTools';\n\n// Import new advanced analysis tools\nimport { CodeComplexityTool } from './codeAnalysisTool';\nimport { SecurityVulnerabilityTool } from './codeAnalysisTool';\nimport { DependencyAnalysisTool } from './dependencyAnalysisTool';\nimport { CodeSmellDetectorTool, RefactoringAdvisorTool } from './codeRefactoringTool';\n\n// Import enhanced file system tool\n// Import diagnostics tool\nimport { DiagnosticsTool } from './diagnosticsTool';\n\n// Import advanced editing tools\nimport { PrecisionEditTool } from './advancedCodeEditingTool';\nimport { DiffTool } from './advancedDiffTool';\nimport { LargeFileEditTool } from './largeFileEditTool';\n\n// Phase 2 tools\nimport { TimeTravelDebuggingTool } from './timeTravelDebuggingTool';\nimport { CascadeEditingTool } from './cascadeEditingTool';\n\n/**\n * Registry for all available tools in the extension.\n * Acts as a central access point for tool instances.\n */\n// Singleton export removed; use ToolRegistry.instance instead.\nexport class ToolRegistry {\n  private static _instance: ToolRegistry | undefined;\n  private tools = new Map<string, ITool>();\n  private initialized = false;\n\n  private constructor() {\n    // Private constructor to prevent direct instantiation\n  }\n\n  /**\n     * Get the singleton instance of ToolRegistry\n     */\n  public static get instance(): ToolRegistry {\n    if (!ToolRegistry._instance) {\n      // Create a new instance if it doesn't exist yet\n      ToolRegistry._instance = new ToolRegistry();\n      // Note: The context will be set when initialize() is called\n    }\n    return ToolRegistry._instance;\n  }\n\n  /**\n     * Initialize the ToolRegistry\n     */\n  public static async initialize(context: vscode.ExtensionContext): Promise<ToolRegistry> {\n    const instance = ToolRegistry.instance; // This will create the instance if it doesn't exist\n    if (instance.initialized) {\n      Logger.instance.warn('ToolRegistry.initialize() called multiple times. This is allowed but may indicate an issue.');\n      return instance;\n    }\n    await instance.initialize(context);\n    return instance;\n  }\n\n  /**\n     * Get a tool by ID\n     */\n  public static getTool(toolId: string): ITool | undefined {\n    if (!ToolRegistry.instance) {\n      throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');\n    }\n    return ToolRegistry.instance.tools.get(toolId);\n  }\n\n  /**\n     * Get all registered tools\n     */\n  public static getAllTools(): ITool[] {\n    if (!ToolRegistry.instance) {\n      throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');\n    }\n    return Array.from(ToolRegistry.instance.tools.values());\n  }\n\n  /**\n     * Register a tool\n     */\n  public static registerTool(tool: ITool): void {\n    if (!ToolRegistry.instance) {\n      throw new Error('ToolRegistry not initialized. Call ToolRegistry.initialize(context) first.');\n    }\n    if (ToolRegistry.instance.tools.has(tool.id)) {\n      Logger.instance.warn(`Tool with ID '${tool.id}' is already registered. Overwriting.`);\n    }\n    ToolRegistry.instance.tools.set(tool.id, tool);\n    Logger.instance.info(`Registered tool: ${tool.id} (${tool.name}) - ${tool.description}`);\n  }\n\n  /**\n     * Initialize the registry\n     */\n  private async initialize(context: vscode.ExtensionContext): Promise<void> {\n    if (this.initialized) {\n      Logger.instance.warn('ToolRegistry.initialize() called when already initialized');\n      return;\n    }\n\n    Logger.instance.info('Starting tool registry initialization...');\n\n    // Initialize memory manager first\n    await memoryManager.initialize(context);\n\n    // Register built-in tools\n    this.registerBuiltInTools();\n\n    // Wait a bit to ensure all tools are registered\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    Logger.instance.info(`Tool registry initialized with ${this.tools.size} tools`);\n    this.initialized = true;\n  }\n\n  /**\n     * Register built-in tools with the registry\n     */\n  private registerBuiltInTools() {\n    try {\n      Logger.instance.info('Starting tool registration...');\n\n      // Register basic tools with standardized IDs\n      this.registerToolWithId(new DirectoryListTool(), 'listDir');\n      this.registerToolWithId(new CodeSearchTool(), 'codeSearch');\n      this.registerToolWithId(new TerminalCommandTool(), 'terminal_command');\n      this.registerToolWithId(new WebSearchTool(), 'webSearch');\n      this.registerToolWithId(new WebReadTool(), 'webRead');\n      this.registerToolWithId(createMemoryTool(memoryManager), 'memory_management');\n      this.registerToolWithId(new BrowserPreviewTool(), 'browserPreview');\n      this.registerToolWithId(new DeployWebAppTool(), 'deployWebApp');\n      this.registerToolWithId(new GitTool(), 'git');\n      this.registerToolWithId(new EditorActionsTool(), 'editor');\n      this.registerToolWithId(new CodeIntelligenceTool(), 'codeIntel');\n      this.registerToolWithId(new CodeGenerationTool(), 'codeGen');\n      this.registerToolWithId(new LintDiagnosticsTool(), 'lint_diagnostics');\n      this.registerToolWithId(new DiagnosticsTool(), 'diagnostics');\n      this.registerToolWithId(new EnhancedFileSystemTool(), 'enhancedFile');\n      this.registerToolWithId(new DocumentationTool(), 'docs');\n\n      // Register advanced code generation tools\n      this.registerToolWithId(new ExplainCodeTool(), 'explain');\n      this.registerToolWithId(new DocumentCodeTool(), 'document');\n      this.registerToolWithId(new GenerateTestsTool(), 'generateTests');\n      this.registerToolWithId(new MultiFileCodeGenTool(), 'multiFileGen');\n      this.registerToolWithId(new DocumentationGenTool(), 'docGen');\n      this.registerToolWithId(new DocumentationSearchTool(), 'docSearch');\n      this.registerToolWithId(new DocumentationSummaryTool(), 'docSummary');\n      this.registerToolWithId(new DocumentationVisualizationTool(), 'docViz');\n      this.registerToolWithId(new FuzzySearchTool(), 'fuzzySearch');\n      this.registerToolWithId(new SemanticSearchTool(), 'semanticSearch');\n      this.registerToolWithId(new SearchPreviewTool(), 'searchPreview');\n      this.registerToolWithId(new MultiCursorTool(), 'multiCursor');\n      this.registerToolWithId(new ClipboardTool(), 'clipboard');\n      this.registerToolWithId(new BatchEditTool(), 'batchEdit');\n      this.registerToolWithId(new FindReplaceTool(), 'findReplace');\n      this.registerToolWithId(new GitStashTool(), 'stash');\n      this.registerToolWithId(new GitRevertTool(), 'revert');\n      this.registerToolWithId(new GitCherryPickTool(), 'cherryPick');\n      this.registerToolWithId(new GitRebaseTool(), 'rebase');\n      this.registerToolWithId(new GitTagTool(), 'tag');\n      this.registerToolWithId(new GitBranchGraphTool(), 'branchGraph');\n      this.registerToolWithId(new BatchLintTool(), 'batchLint');\n      this.registerToolWithId(new AutoFixAllTool(), 'autoFixAll');\n      this.registerToolWithId(new LintSummaryTool(), 'lintSummary');\n      this.registerToolWithId(new DiagnosticsSearchTool(), 'diagnosticsSearch');\n      this.registerToolWithId(new MemorySearchTool(memoryManager), 'memorySearch');\n      this.registerToolWithId(new MemoryUpdateTool(memoryManager), 'memoryUpdate');\n      this.registerToolWithId(new MemoryTagTool(memoryManager), 'memoryTag');\n      this.registerToolWithId(new MemoryVisualizationTool(memoryManager), 'memoryViz');\n      this.registerToolWithId(new WebMultiSearchTool(), 'webMultiSearch');\n      this.registerToolWithId(new WebContentExtractTool(), 'webExtract');\n      this.registerToolWithId(new WebSnapshotTool(), 'webSnapshot');\n      this.registerToolWithId(new WebDeployStatusTool(), 'deployStatus');\n      this.registerToolWithId(new CodeComplexityTool(), 'codeComplexity');\n      this.registerToolWithId(new SecurityVulnerabilityTool(), 'securityVulnerability');\n      this.registerToolWithId(new DependencyAnalysisTool(), 'dependencyAnalysis');\n      this.registerToolWithId(new CodeSmellDetectorTool(), 'codeSmellDetector');\n      this.registerToolWithId(new RefactoringAdvisorTool(), 'refactoringAdvisor');\n      this.registerToolWithId(new PrecisionEditTool(), 'precisionEdit');\n      this.registerToolWithId(new DiffTool(), 'diffTool');\n      this.registerToolWithId(new LargeFileEditTool(), 'largeFileEdit');\n\n      // Register advanced code search tools\n      this.registerToolWithId(new FuzzySearchTool(), 'fuzzySearch');\n      this.registerToolWithId(new SemanticSearchTool(), 'semanticSearch');\n      this.registerToolWithId(new SearchPreviewTool(), 'searchPreview');\n\n      // Register advanced documentation tools\n      this.registerToolWithId(new DocumentationGenTool(), 'docGen');\n      this.registerToolWithId(new DocumentationSearchTool(), 'docSearch');\n      this.registerToolWithId(new DocumentationSummaryTool(), 'docSummary');\n      this.registerToolWithId(new DocumentationVisualizationTool(), 'docViz');\n\n      // Register advanced editor action tools\n      this.registerToolWithId(new MultiCursorTool(), 'multiCursor');\n      this.registerToolWithId(new ClipboardTool(), 'clipboard');\n      this.registerToolWithId(new BatchEditTool(), 'batchEdit');\n      this.registerToolWithId(new FindReplaceTool(), 'findReplace');\n\n      // Register advanced git tools\n      this.registerToolWithId(new GitStashTool(), 'stash');\n      this.registerToolWithId(new GitRevertTool(), 'revert');\n      this.registerToolWithId(new GitCherryPickTool(), 'cherryPick');\n      this.registerToolWithId(new GitRebaseTool(), 'rebase');\n      this.registerToolWithId(new GitTagTool(), 'tag');\n      this.registerToolWithId(new GitBranchGraphTool(), 'branchGraph');\n\n      // Register advanced lint diagnostic tools\n      this.registerToolWithId(new BatchLintTool(), 'batchLint');\n      this.registerToolWithId(new AutoFixAllTool(), 'autoFixAll');\n      this.registerToolWithId(new LintSummaryTool(), 'lintSummary');\n      this.registerToolWithId(new DiagnosticsSearchTool(), 'diagnosticsSearch');\n\n      // Register advanced memory tools\n      this.registerToolWithId(new MemorySearchTool(memoryManager), 'memorySearch');\n      this.registerToolWithId(new MemoryUpdateTool(memoryManager), 'memoryUpdate');\n      this.registerToolWithId(new MemoryTagTool(memoryManager), 'memoryTag');\n      this.registerToolWithId(new MemoryVisualizationTool(memoryManager), 'memoryViz');\n\n      // Register advanced web tools\n      this.registerToolWithId(new WebMultiSearchTool(), 'webMultiSearch');\n      this.registerToolWithId(new WebContentExtractTool(), 'webExtract');\n      this.registerToolWithId(new WebSnapshotTool(), 'webSnapshot');\n      this.registerToolWithId(new WebDeployStatusTool(), 'deployStatus');\n\n      // Register advanced code analysis tools\n      this.registerToolWithId(new CodeComplexityTool(), 'codeComplexity');\n      this.registerToolWithId(new SecurityVulnerabilityTool(), 'securityVulnerability');\n      this.registerToolWithId(new DependencyAnalysisTool(), 'dependencyAnalysis');\n\n      // Register advanced code refactoring tools\n      this.registerToolWithId(new CodeSmellDetectorTool(), 'codeSmellDetector');\n      this.registerToolWithId(new RefactoringAdvisorTool(), 'refactoringAdvisor');\n\n      // Register advanced code editing tools\n      this.registerToolWithId(new PrecisionEditTool(), 'precisionEdit');\n      this.registerToolWithId(new DiffTool(), 'diffTool');\n      this.registerToolWithId(new LargeFileEditTool(), 'largeFileEdit');\n\n      // Phase 2: Register revolutionary tools\n      this.registerToolWithId(new TimeTravelDebuggingTool(), 'timeTravelDebug');\n      this.registerToolWithId(new CascadeEditingTool(), 'cascadeEdit');\n\n      // Phase 5: Register revolutionary tools\n      this.registerRevolutionaryPhase5Tools();\n\n      Logger.instance.info(`Initialized ${this.tools.size} tools in registry (including Phase 5 revolutionary tools).`);\n    } catch (error) {\n      Logger.instance.error('Error registering built-in tools:', error);\n    }\n  }\n\n  /**\n     * Register Phase 5 Revolutionary Tools\n     */\n  private registerRevolutionaryPhase5Tools(): void {\n    try {\n      // Import and register revolutionary tools\n      const { QuantumAnalysisTool } = require('./quantumAnalysisTool');\n      const { NeuralCodeSynthesisTool } = require('./neuralCodeSynthesisTool');\n\n      // Register Quantum Analysis Tool\n      this.registerToolWithId(new QuantumAnalysisTool(), 'quantum_analysis');\n\n      // Register Neural Code Synthesis Tool\n      this.registerToolWithId(new NeuralCodeSynthesisTool(), 'neural_code_synthesis');\n\n      // Update essential tools to include revolutionary tools\n      this.updateEssentialToolsWithRevolutionary();\n\n      Logger.instance.info('Phase 5 revolutionary tools registered successfully');\n    } catch (error) {\n      Logger.instance.error('Error registering Phase 5 revolutionary tools:', error);\n    }\n  }\n\n  /**\n     * Update essential tools to include revolutionary capabilities\n     */\n  private updateEssentialToolsWithRevolutionary(): void {\n    // Add revolutionary tools to essential tools for advanced agents\n    const revolutionaryToolIds = [\n      'quantum_analysis',\n      'neural_code_synthesis',\n      'timeTravelDebug'\n    ];\n\n    // These will be available to agents that request revolutionary capabilities\n    Logger.instance.debug(`Revolutionary tools available: ${revolutionaryToolIds.join(', ')}`);\n  }\n\n  /**\n     * Registers a tool with the registry\n     */\n  public registerToolWithId(tool: ITool, id: string): void {\n    // Override the tool's ID with our standardized ID\n    Object.defineProperty(tool, 'id', {\n      value: id,\n      writable: false,\n      configurable: false\n    });\n\n    this.registerTool(tool);\n  }\n\n  registerTool(tool: ITool): void {\n    if (this.tools.has(tool.id)) {\n      Logger.instance.warn(`Tool with ID '${tool.id}' is already registered. Overwriting.`);\n    }\n    this.tools.set(tool.id, tool);\n    Logger.instance.info(`Registered tool: ${tool.id} (${tool.name}) - ${tool.description}`);\n\n    // Log the tool's actions if it's a multi-action tool\n    if (tool.type === 'multi-action' && tool.actions) {\n      Object.entries(tool.actions).forEach(([actionName, action]) => {\n        Logger.instance.debug(`  Action: ${actionName} - ${action.description || 'No description'}`);\n      });\n    }\n  }\n\n  /**\n     * Gets a tool by ID\n     */\n  getTool(toolId: string): ITool | undefined {\n    const tool = this.tools.get(toolId);\n    if (!tool) {\n      const availableTools = Array.from(this.tools.keys()).join(', ');\n      Logger.instance.warn(`Tool with ID '${toolId}' not found. Available tools: ${availableTools}`);\n    }\n    return tool;\n  }\n\n  /**\n     * Gets all registered tools\n     */\n  getAllTools(): ITool[] {\n    return Array.from(this.tools.values());\n  }\n\n  /**\n     * Converts array of tool IDs to a map of tool instances\n     */\n  getToolsByIds(toolIds: string[]): Map<string, ITool> {\n    const result = new Map<string, ITool>();\n\n    for (const id of toolIds) {\n      const tool = this.getTool(id);\n      if (tool) {\n        result.set(id, tool);\n      } else {\n        Logger.instance.warn(`Tool with ID '${id}' not found.`);\n      }\n    }\n\n    return result;\n  }\n\n  /**\n     * Get all essential tools for agents\n     */\n  getEssentialTools(): Map<string, ITool> {\n    const essentialToolIds = [\n      'listDir',\n      'codeSearch',\n      'memory_management',\n      'editor',\n      'terminal_command',\n      'webSearch',\n      'git',\n      'codeIntel',\n      'memorySearch',\n      'memoryUpdate'\n    ];\n\n    return this.getToolsByIds(essentialToolIds);\n  }\n\n  /**\n     * Get tools by category\n     */\n  getToolsByCategory(category: string): ITool[] {\n    return Array.from(this.tools.values()).filter(tool =>\n      tool.category === category\n    );\n  }\n\n  /**\n     * Get all available tool categories\n     */\n  getToolCategories(): string[] {\n    const categories = new Set<string>();\n    for (const tool of this.tools.values()) {\n      if (tool.category) {\n        categories.add(tool.category);\n      }\n    }\n    return Array.from(categories);\n  }\n}\n"]}