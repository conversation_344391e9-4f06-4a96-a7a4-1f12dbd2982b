// TTS section logic and rendering

// Utility to ensure parseInt always gets a string
function safeGetString(val: string | null | undefined): string {
  return typeof val === 'string' ? val : '';
}

export type TTSVoice = {
    name: string;
    provider: string;
    language: string;
    voiceId: string;
    enabled: boolean;
};

import { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';

let ttsVoices: TTSVoice[] = [];
let editingTtsIdx: number | null = null;
const sectionTheme: UIThemeConfig['section'] = defaultUIThemeConfig.section;

export function renderTTSSettingsSection(container: HTMLElement, settings: any) {
  // Sync from settings
  ttsVoices = Array.isArray(settings.ttsVoices) ? settings.ttsVoices : [];

  // Render comprehensive TTS settings
  renderTTSSettings(container, settings);

  // Add button listeners
  const addBtn = document.getElementById('addTTSBtn');
  if (addBtn) addBtn.onclick = () => showTTSModal(container, {}, null);
  // Modal buttons
  const cancelBtn = document.getElementById('cancelTTSBtn');
  if (cancelBtn) cancelBtn.onclick = () => hideTTSModal(container);
  const saveBtn = document.getElementById('saveTTSBtn');
  if (saveBtn) saveBtn.onclick = () => saveTTS(container, settings);

  // Provider change listeners
  const providerSelect = document.getElementById('ttsProvider') as HTMLSelectElement;
  if (providerSelect) {
    providerSelect.onchange = () => updateVoiceOptions(container, settings);
  }

  // Test TTS button
  const testBtn = document.getElementById('testTTSBtn');
  if (testBtn) {
    testBtn.onclick = () => testTTS(settings);
  }
}

function renderTTSTable(container: HTMLElement) {
  const section = container.querySelector('#ttsSection') as HTMLElement;
  if (!ttsVoices || ttsVoices.length === 0) {
    section.innerHTML = '<div style="color:#aaa;">No TTS voices defined.</div>';
    return;
  }
  let htmlContent = `<style>
        .crud-table th, .crud-table td { padding: 6px 10px; }
        .crud-table th {
            background: ${sectionTheme.headerBg};
            color: ${sectionTheme.headerColor};
            font-weight: 600;
        }
        .crud-table tbody tr:nth-child(even) { background: #fafbfc; }
        .crud-table tbody tr:hover { background: #e8f0fe; }
        .btn-tts {
            background:${sectionTheme.button.background};
            color:${sectionTheme.button.color};
            border:${sectionTheme.button.border};
            border-radius:${sectionTheme.button.borderRadius};
            padding:3px 10px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s;
        }
        .btn-tts:hover { background:#1d4ed8; }
    </style>`;
  htmlContent += '<table class="crud-table"><thead><tr>' +
        '<th>Name</th><th>Provider</th><th>Language</th><th>Voice ID</th><th>Enabled</th><th>Actions</th>' +
        '</tr></thead><tbody>';
  ttsVoices.forEach((tts, idx) => {
    htmlContent += `<tr>
            <td>${tts.name || ''}</td>
            <td>${tts.provider || ''}</td>
            <td>${tts.language || ''}</td>
            <td>${tts.voiceId || ''}</td>
            <td>${tts.enabled ? 'Yes' : 'No'}</td>
            <td>
                <button type="button" data-edit="${idx}">Edit</button>
                <button type="button" data-delete="${idx}">Delete</button>
            </td>
        </tr>`;
  });
  htmlContent += '</tbody></table>';
  section.innerHTML = htmlContent;
  // Attach edit/delete event listeners
  section.querySelectorAll('button[data-edit]').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const idxAttr = (e.target as HTMLElement).getAttribute('data-edit');
      const idx = parseInt(safeGetString(idxAttr));
      showTTSModal(container, ttsVoices[idx], idx);
    });
  });
  section.querySelectorAll('button[data-delete]').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const idxAttr = (e.target as HTMLElement).getAttribute('data-delete');
      const idx = parseInt(safeGetString(idxAttr));
      deleteTTS(container, idx);
    });
  });
}

function showTTSModal(container: HTMLElement, tts: Partial<TTSVoice>, idx: number | null) {
  const modal = document.getElementById('ttsModal');
  const title = document.getElementById('ttsModalTitle');
  const nameInput = document.getElementById('ttsName') as HTMLInputElement | null;
  const providerInput = document.getElementById('ttsProvider') as HTMLInputElement | null;
  const languageInput = document.getElementById('ttsLanguage') as HTMLInputElement | null;
  const voiceIdInput = document.getElementById('ttsVoiceId') as HTMLInputElement | null;
  const enabledInput = document.getElementById('ttsEnabled') as HTMLInputElement | null;
  if (modal) modal.style.display = 'flex';
  if (title) title.innerText = idx == null ? 'Add TTS Voice' : 'Edit TTS Voice';
  if (nameInput) nameInput.value = tts?.name || '';
  if (providerInput) providerInput.value = tts?.provider || '';
  if (languageInput) languageInput.value = tts?.language || '';
  if (voiceIdInput) voiceIdInput.value = tts?.voiceId || '';
  if (enabledInput) enabledInput.checked = !!tts?.enabled;
  editingTtsIdx = idx;
}

function hideTTSModal(container: HTMLElement) {
  const modal = document.getElementById('ttsModal');
  if (modal) modal.style.display = 'none';
  // Clear validation errors in container
  const errors = container.querySelectorAll('.validation-error');
  errors.forEach(error => error.remove());
  editingTtsIdx = null;
}

function saveTTS(container: HTMLElement, settings: any) {
  const nameInput = document.getElementById('ttsName') as HTMLInputElement | null;
  const providerInput = document.getElementById('ttsProvider') as HTMLInputElement | null;
  const languageInput = document.getElementById('ttsLanguage') as HTMLInputElement | null;
  const voiceIdInput = document.getElementById('ttsVoiceId') as HTMLInputElement | null;
  const enabledInput = document.getElementById('ttsEnabled') as HTMLInputElement | null;
  const tts: TTSVoice = {
    name: nameInput?.value || '',
    provider: providerInput?.value || '',
    language: languageInput?.value || '',
    voiceId: voiceIdInput?.value || '',
    enabled: enabledInput?.checked || false,
  };
  if (editingTtsIdx == null) {
    ttsVoices.push(tts);
  } else {
    ttsVoices[editingTtsIdx] = tts;
  }
  settings.ttsVoices = ttsVoices;
  hideTTSModal(container);
  renderTTSTable(container);
}

import { showModal } from '../components/modal';

function deleteTTS(container: HTMLElement, idx: number) {
  const ttsName = ttsVoices[idx]?.name || 'this TTS voice';
  showModal({
    title: 'Delete TTS Voice',
    content: `Are you sure you want to delete the TTS voice "${ttsName}"? This cannot be undone.`,
    onConfirm: () => {
      ttsVoices.splice(idx, 1);
      const settings = (window as any).settings || {};
      settings.ttsVoices = ttsVoices;
      renderTTSTable(container);
    }
  });
}

function renderTTSSettings(container: HTMLElement, settings: any) {
  const ttsSettings = settings.tts || {};

  const htmlContent = `
    <style>
      .tts-settings { padding: 20px; }
      .tts-section { margin-bottom: 30px; padding: 15px; border: 1px solid var(--vscode-panel-border); border-radius: 8px; }
      .tts-section h3 { margin-top: 0; color: var(--vscode-foreground); }
      .form-group { margin-bottom: 15px; }
      .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
      .form-control { width: 100%; padding: 8px; border: 1px solid var(--vscode-input-border); background: var(--vscode-input-background); color: var(--vscode-input-foreground); border-radius: 4px; }
      .form-row { display: flex; gap: 15px; }
      .form-row .form-group { flex: 1; }
      .checkbox-group { display: flex; align-items: center; gap: 8px; }
      .btn-primary { background: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
      .btn-primary:hover { background: var(--vscode-button-hoverBackground); }
      .btn-secondary { background: var(--vscode-button-secondaryBackground); color: var(--vscode-button-secondaryForeground); border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
      .api-key-input { font-family: monospace; }
      .provider-config { display: none; padding: 10px; background: var(--vscode-editor-inactiveSelectionBackground); border-radius: 4px; margin-top: 10px; }
      .provider-config.active { display: block; }
      .voice-preview { display: flex; align-items: center; gap: 10px; }
      .quality-indicator { padding: 2px 6px; border-radius: 3px; font-size: 12px; }
      .quality-high { background: #22c55e; color: white; }
      .quality-medium { background: #f59e0b; color: white; }
      .quality-low { background: #ef4444; color: white; }
    </style>

    <div class="tts-settings">
      <!-- General TTS Settings -->
      <div class="tts-section">
        <h3>🔊 General Settings</h3>
        <div class="form-group">
          <div class="checkbox-group">
            <input type="checkbox" id="ttsEnabled" ${ttsSettings.enabled ? 'checked' : ''}>
            <label for="ttsEnabled">Enable Text-to-Speech</label>
          </div>
        </div>
        <div class="form-group">
          <div class="checkbox-group">
            <input type="checkbox" id="ttsAutoSpeak" ${ttsSettings.autoSpeak ? 'checked' : ''}>
            <label for="ttsAutoSpeak">Auto-speak assistant responses</label>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="ttsProvider">Provider</label>
            <select id="ttsProvider" class="form-control">
              <option value="system" ${ttsSettings.provider === 'system' ? 'selected' : ''}>System TTS</option>
              <option value="edge" ${ttsSettings.provider === 'edge' ? 'selected' : ''}>Microsoft Edge</option>
              <option value="google" ${ttsSettings.provider === 'google' ? 'selected' : ''}>Google Cloud TTS</option>
              <option value="openai" ${ttsSettings.provider === 'openai' ? 'selected' : ''}>OpenAI TTS</option>
              <option value="elevenlabs" ${ttsSettings.provider === 'elevenlabs' ? 'selected' : ''}>ElevenLabs</option>
            </select>
          </div>
          <div class="form-group">
            <label for="ttsVoice">Voice</label>
            <select id="ttsVoice" class="form-control">
              <option value="">Select a voice...</option>
            </select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="ttsRate">Speech Rate: <span id="rateValue">${ttsSettings.rate || 1.0}</span></label>
            <input type="range" id="ttsRate" min="0.5" max="2.0" step="0.1" value="${ttsSettings.rate || 1.0}" class="form-control">
          </div>
          <div class="form-group">
            <label for="ttsPitch">Pitch: <span id="pitchValue">${ttsSettings.pitch || 1.0}</span></label>
            <input type="range" id="ttsPitch" min="0.5" max="2.0" step="0.1" value="${ttsSettings.pitch || 1.0}" class="form-control">
          </div>
          <div class="form-group">
            <label for="ttsVolume">Volume: <span id="volumeValue">${ttsSettings.volume || 1.0}</span></label>
            <input type="range" id="ttsVolume" min="0.0" max="1.0" step="0.1" value="${ttsSettings.volume || 1.0}" class="form-control">
          </div>
        </div>
        <div class="voice-preview">
          <button type="button" id="testTTSBtn" class="btn-secondary">🎵 Test Voice</button>
          <span class="quality-indicator quality-high">High Quality</span>
        </div>
      </div>

      <!-- Provider-specific configurations -->
      <div class="tts-section">
        <h3>🔑 Provider Configuration</h3>

        <!-- Google Cloud TTS -->
        <div id="googleConfig" class="provider-config">
          <h4>Google Cloud TTS</h4>
          <div class="form-group">
            <label for="googleApiKey">API Key</label>
            <input type="password" id="googleApiKey" class="form-control api-key-input"
                   value="${ttsSettings.apiKeys?.google || ''}"
                   placeholder="Enter Google Cloud TTS API key">
          </div>
        </div>

        <!-- OpenAI TTS -->
        <div id="openaiConfig" class="provider-config">
          <h4>OpenAI TTS</h4>
          <div class="form-group">
            <label for="openaiApiKey">API Key</label>
            <input type="password" id="openaiApiKey" class="form-control api-key-input"
                   value="${ttsSettings.apiKeys?.openai || ''}"
                   placeholder="Enter OpenAI API key">
          </div>
        </div>

        <!-- ElevenLabs -->
        <div id="elevenlabsConfig" class="provider-config">
          <h4>ElevenLabs</h4>
          <div class="form-group">
            <label for="elevenlabsApiKey">API Key</label>
            <input type="password" id="elevenlabsApiKey" class="form-control api-key-input"
                   value="${ttsSettings.apiKeys?.elevenlabs || ''}"
                   placeholder="Enter ElevenLabs API key">
          </div>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div class="tts-section">
        <h3>⚙️ Advanced Settings</h3>
        <div class="form-row">
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="ssmlEnabled" ${ttsSettings.advanced?.ssmlEnabled ? 'checked' : ''}>
              <label for="ssmlEnabled">Enable SSML (Speech Synthesis Markup Language)</label>
            </div>
          </div>
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="emotionControl" ${ttsSettings.advanced?.emotionControl ? 'checked' : ''}>
              <label for="emotionControl">Emotion Control</label>
            </div>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="qualityMode">Quality Mode</label>
            <select id="qualityMode" class="form-control">
              <option value="fast" ${ttsSettings.advanced?.qualityMode === 'fast' ? 'selected' : ''}>Fast</option>
              <option value="balanced" ${ttsSettings.advanced?.qualityMode === 'balanced' ? 'selected' : ''}>Balanced</option>
              <option value="high" ${ttsSettings.advanced?.qualityMode === 'high' ? 'selected' : ''}>High Quality</option>
            </select>
          </div>
          <div class="form-group">
            <div class="checkbox-group">
              <input type="checkbox" id="cacheEnabled" ${ttsSettings.advanced?.cacheEnabled ? 'checked' : ''}>
              <label for="cacheEnabled">Enable Audio Caching</label>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  container.innerHTML = htmlContent;

  // Add event listeners for real-time updates
  setupTTSEventListeners(container, settings);
}

function setupTTSEventListeners(container: HTMLElement, settings: any) {
  // Range input listeners for real-time updates
  const rateSlider = document.getElementById('ttsRate') as HTMLInputElement;
  const pitchSlider = document.getElementById('ttsPitch') as HTMLInputElement;
  const volumeSlider = document.getElementById('ttsVolume') as HTMLInputElement;

  if (rateSlider) {
    rateSlider.addEventListener('input', () => {
      const rateValue = document.getElementById('rateValue');
      if (rateValue) rateValue.textContent = rateSlider.value;
    });
  }

  if (pitchSlider) {
    pitchSlider.addEventListener('input', () => {
      const pitchValue = document.getElementById('pitchValue');
      if (pitchValue) pitchValue.textContent = pitchSlider.value;
    });
  }

  if (volumeSlider) {
    volumeSlider.addEventListener('input', () => {
      const volumeValue = document.getElementById('volumeValue');
      if (volumeValue) volumeValue.textContent = volumeSlider.value;
    });
  }

  // Provider change listener
  const providerSelect = document.getElementById('ttsProvider') as HTMLSelectElement;
  if (providerSelect) {
    providerSelect.addEventListener('change', () => {
      updateProviderConfig(providerSelect.value);
      updateVoiceOptions(container, settings);
    });
    // Initialize provider config
    updateProviderConfig(providerSelect.value);
  }
}

function updateProviderConfig(provider: string) {
  // Hide all provider configs
  const configs = document.querySelectorAll('.provider-config');
  configs.forEach(config => config.classList.remove('active'));

  // Show selected provider config
  const selectedConfig = document.getElementById(`${provider}Config`);
  if (selectedConfig) {
    selectedConfig.classList.add('active');
  }
}

function updateVoiceOptions(container: HTMLElement, settings: any) {
  const providerSelect = document.getElementById('ttsProvider') as HTMLSelectElement;
  const voiceSelect = document.getElementById('ttsVoice') as HTMLSelectElement;

  if (!providerSelect || !voiceSelect) return;

  const provider = providerSelect.value;

  // Clear existing options
  voiceSelect.innerHTML = '<option value="">Loading voices...</option>';

  // Request voices from backend
  if ((window as any).vscode) {
    (window as any).vscode.postMessage({
      command: 'getTTSVoices',
      provider: provider
    });
  }
}

function testTTS(settings: any) {
  const testText = 'Hello! This is a test of the text-to-speech system. How does it sound?';

  if ((window as any).vscode) {
    (window as any).vscode.postMessage({
      command: 'testTTS',
      text: testText,
      settings: getCurrentTTSSettings()
    });
  }
}

function getCurrentTTSSettings() {
  const enabled = (document.getElementById('ttsEnabled') as HTMLInputElement)?.checked || false;
  const autoSpeak = (document.getElementById('ttsAutoSpeak') as HTMLInputElement)?.checked || false;
  const provider = (document.getElementById('ttsProvider') as HTMLSelectElement)?.value || 'system';
  const voice = (document.getElementById('ttsVoice') as HTMLSelectElement)?.value || '';
  const rate = parseFloat((document.getElementById('ttsRate') as HTMLInputElement)?.value || '1.0');
  const pitch = parseFloat((document.getElementById('ttsPitch') as HTMLInputElement)?.value || '1.0');
  const volume = parseFloat((document.getElementById('ttsVolume') as HTMLInputElement)?.value || '1.0');

  const apiKeys = {
    google: (document.getElementById('googleApiKey') as HTMLInputElement)?.value || '',
    openai: (document.getElementById('openaiApiKey') as HTMLInputElement)?.value || '',
    elevenlabs: (document.getElementById('elevenlabsApiKey') as HTMLInputElement)?.value || ''
  };

  const advanced = {
    ssmlEnabled: (document.getElementById('ssmlEnabled') as HTMLInputElement)?.checked || false,
    emotionControl: (document.getElementById('emotionControl') as HTMLInputElement)?.checked || false,
    qualityMode: (document.getElementById('qualityMode') as HTMLSelectElement)?.value || 'balanced',
    cacheEnabled: (document.getElementById('cacheEnabled') as HTMLInputElement)?.checked || true,
    streamingEnabled: true
  };

  return {
    enabled,
    autoSpeak,
    provider,
    voice,
    rate,
    pitch,
    volume,
    apiKeys,
    advanced
  };
}
