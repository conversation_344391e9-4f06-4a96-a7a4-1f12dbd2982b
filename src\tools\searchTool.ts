/**
 * Enhanced search tool with context support and schema validation.
 * Wraps a base search tool to provide consistent behavior and type safety.
 */

import { z } from 'zod';
import { Tool } from '../../managers';
import { ITool } from './tool.ts.backup';
import { ToolSchemaWrapper, ToolContextOptions } from './toolSchemaWrapper';
import { Logger } from '../../logger';
import { AgentContext } from '../agents/agentUtilities/agent';
import { GraphState } from '../agents/workflows/types';

/**
 * Interface for search options
 */
export interface SearchToolOptions {
  /** Maximum number of results to return */
  limit?: number;
  /** Whether to include metadata in results */
  includeMetadata?: boolean;
  /** Additional filters for the search */
  filters?: Record<string, unknown>;
}

/**
 * Enhanced wrapper for search tools with context awareness
 */
export class SearchTool extends ToolSchemaWrapper {
  private _defaultOptions: SearchToolOptions;

  /**
   * Schema for input validation and transformation
   */
  override schema = z.union([
    // Simple string query
    z.string().describe('The search query string.'),
    
    // Or an object with query and options
    z.object({
      query: z.string().describe('The search query string.'),
      options: z.object({
        limit: z.number().int().positive().optional()
          .describe('Maximum number of results to return'),
        includeMetadata: z.boolean().optional()
          .describe('Whether to include metadata in results'),
        filters: z.record(z.any()).optional()
          .describe('Additional filters for the search')
      }).optional()
    })
  ]).transform((input) => {
    // Normalize input to always return a query string and options
    if (typeof input === 'string') {
      return { query: input, options: {} };
    }
    return { query: input.query, options: input.options || {} };
  });

  constructor(
    searchTool: Tool | ITool, 
    name?: string, 
    description?: string,
    defaultOptions: SearchToolOptions = {},
    initialContext?: AgentContext
  ) {
    super(
      searchTool, 
      name || searchTool.name || 'search',
      description || 'Performs a general search based on a query with contextual enhancement.',
      initialContext
    );
    
    // Initialize default options
    this._defaultOptions = {
      limit: 10,
      includeMetadata: true,
      ...defaultOptions
    };
    
    if (!('invoke' in searchTool || 'call' in searchTool || 'execute' in searchTool)) {
      Logger.instance.warn(
        `SearchTool wrapper created for tool '${this.name}' ` +
        `which lacks standard execution methods (invoke, call, execute).`
      );
    }
    
    // Bind methods to maintain proper 'this' context
    this._enhanceWithContext = this._enhanceWithContext.bind(this);
    this._processSearchResult = this._processSearchResult.bind(this);
  }

  /**
   * Internal method to enhance search options with context information
   */
  private _enhanceWithContext(
    baseOptions: SearchToolOptions,
    context?: AgentContext
  ): SearchToolOptions {
    const enhancedOptions = { ...baseOptions };
    
    // If we have a context, we can enhance the options
    if (context) {
      // Example: Add user-specific filters from context
      if (context.userId) {
        enhancedOptions.filters = {
          ...enhancedOptions.filters,
          userId: context.userId
        };
      }
      
      // Example: Adjust limit based on context
      if (context.isMobile) {
        enhancedOptions.limit = Math.min(enhancedOptions.limit || 10, 5);
      }
    }
    
    return enhancedOptions;
  }

  /**
   * Process and normalize the search result into a valid GraphState
   */
  private async _processSearchResult(
    result: unknown,
    originalQuery: string,
    options: SearchToolOptions
  ): Promise<GraphState> {
    try {
      // Default state with query and empty results
      const baseState: Partial<GraphState> = {
        query: originalQuery,
        results: [],
        metadata: {
          searchOptions: options,
          timestamp: new Date().toISOString()
        }
      };

      // Handle different result formats
      if (Array.isArray(result)) {
        // If it's an array, assume it's a list of search results
        baseState.results = result.map((item, index) => ({
          content: typeof item === 'string' ? item : JSON.stringify(item),
          metadata: {},
          score: 1.0 - (index * 0.01) // Simple fallback scoring
        }));
      } else if (result && typeof result === 'object' && 'results' in result) {
        // Handle result objects with a results property
        const resultObj = result as { results: unknown[]; [key: string]: unknown };
        baseState.results = Array.isArray(resultObj.results)
          ? resultObj.results.map((item, index) => ({
              content: typeof item === 'string' ? item : JSON.stringify(item),
              metadata: {},
              score: 1.0 - (index * 0.01)
            }))
          : [];
        
        // Copy other properties to metadata
        Object.entries(resultObj).forEach(([key, value]) => {
          if (key !== 'results') {
            baseState.metadata = baseState.metadata || {};
            baseState.metadata[key] = value;
          }
        });
      } else if (result !== undefined && result !== null) {
        // Handle single result or other result types
        baseState.results = [{
          content: typeof result === 'string' ? result : JSON.stringify(result),
          metadata: {},
          score: 1.0
        }];
      }

      // Apply limit
      if (options.limit) {
        baseState.results = baseState.results.slice(0, options.limit);
      }

      return baseState as GraphState;
    } catch (error) {
      Logger.instance.error('Error processing search result:', error);
      throw new Error(`Failed to process search results: ${error.message}`);
    }
  }

  /**
   * Execute the search with the given input
   */
  protected async _call(input: { query: string; options: SearchToolOptions }): Promise<GraphState> {
    const { query, options: userOptions } = input;
    
    try {
      if (!query) {
        throw new Error('Query is required for search');
      }

      // Merge default options with user-provided options
      const baseOptions: SearchToolOptions = {
        ...this._defaultOptions,
        ...userOptions
      };

      // Enhance options with context if available
      const enhancedOptions = this._enhanceWithContext(baseOptions, this._context);
      
      Logger.instance.debug(`Executing search with query: "${query}"`);
      
      // Execute the underlying tool
      let result;
      if ('invoke' in this._baseTool) {
        result = await this._baseTool.invoke({ 
          query, 
          options: enhancedOptions 
        });
      } else if ('call' in this._baseTool) {
        result = await this._baseTool.call({ 
          query, 
          options: enhancedOptions 
        });
      } else if ('execute' in this._baseTool) {
        result = await this._baseTool.execute({ 
          query, 
          options: enhancedOptions 
        });
      } else {
        throw new Error('No valid execution method found on base tool');
      }

      // Process and normalize the result
      return this._processSearchResult(result, query, enhancedOptions);
    } catch (error) {
      Logger.instance.error('Error in SearchTool._call:', error);
      return {
        query,
        results: [],
        error: error.message,
        metadata: {
          error: true,
          errorMessage: error.message,
          timestamp: new Date().toISOString()
        }
      };
    }
  }
}

export default SearchTool;
