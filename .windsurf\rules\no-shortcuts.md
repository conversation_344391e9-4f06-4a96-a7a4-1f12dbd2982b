---
trigger: always_on
---

Implement in full, all todos and remove the associated todo comments once implemented. Don't simplify the code or delete imports and unused variables, instead create missing logic and files (fully, no todos or simulations or examples). If changes or additions require additions to another file, edit and add the necessary logic in those files too, but don't leave any file with linting errors and dart errors and syntax errors or logic errors. Don't cut corners just to make the linter happy. The ultimate goal is to ensure the highest standards of code (OOP, proper data structures, all variables declared before being used, correct syntax and logic that works correctly in context of other related files and logic...). Don't plan forever. Don't work on one file the entire day. But that doesn't mean half jobs and crappy code, it means be methodical and practical, everything must be thorough and complete and error free. Don't ask too many or unnecessary questions. Don't divert from my explicit instructions and chase rabit holes and own goals, I won't have that. After you think your work is done on a file, save it via command line, then run linter checks and fix any and all issues that may still remain. Be absolutely sure of that. Do batch edits when possible and when it makes sense. Always auto save and run linter checks and fix any and all issues that may still remain. always check for lint and other errors before you report anything