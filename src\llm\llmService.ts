import * as vscode from 'vscode';
import { ILLMProvider, LLMProviderConfig } from './llmProvider';
import { logger } from '../logger';
import { LLMConfig, getDefaultModelConfig } from '../config';
import { providerManager } from './providerManager';
import { shouldSuppressError } from '../ui/feedback/notifications';

// Import all providers
// Standard API providers
import { OpenAIProvider } from './providers/openaiProvider';
import { AnthropicProvider } from './providers/anthropicProvider';
import { GoogleAIProvider } from './providers/googleAIProvider';
import { MistralAIProvider } from './providers/mistralAIProvider';
import { CohereProvider } from './providers/cohereProvider';
import { DeepSeekProvider } from './providers/deepseekProvider';

// Local and self-hosted providers
import { OllamaProvider } from './providers/ollamaProvider';
import { LMStudioProvider } from './providers/lmstudioProvider';

// Aggregator providers
import { OpenRouterProvider } from './providers/openrouterProvider';
import { HuggingFaceProvider } from './providers/huggingfaceProvider';

// Code-specific model providers
import { StarCoderProvider } from './providers/starcoderProvider';
import { CodeLlamaProvider } from './providers/codeLlamaProvider';
import { ReplitProvider } from './providers/replitProvider';
import { WizardCoderProvider } from './providers/wizardCoderProvider';
import { XwinCoderProvider } from './providers/xwinCoderProvider';
import { PhiProvider } from './providers/phiProvider';
import { YiCodeProvider } from './providers/yiCodeProvider';
import { CodeGemmaProvider } from './providers/codeGemmaProvider';
import { SantaCoderProvider } from './providers/santaCoderProvider';
import { StableCodeProvider } from './providers/stableCodeProvider';

// Additional API providers
import { PerplexityAIProvider } from './providers/perplexityProvider';

/**
 * Service that manages LLM providers and model selection
 */
class LLMService {
  private providers = new Map<string, ILLMProvider>();
  private _onProvidersChanged = new vscode.EventEmitter<void>();
  readonly onProvidersChanged = this._onProvidersChanged.event;
  private context: vscode.ExtensionContext | undefined;
  
  // Helper function to check if a dependency is available
  private isDependencyAvailable(packageName: string): boolean {
    try {
      require.resolve(packageName);
      return true;
    } catch (error) {
      return false;
    }
  }

  constructor() {
    // Listen for configuration changes that might affect providers
    vscode.workspace.onDidChangeConfiguration(e => {
      if (e.affectsConfiguration('codessa.llm')) {
        logger.info('LLM configuration changed, re-initializing providers.');
        this.reinitializeProviders();
      }
    });
  }

  /**
     * Initialize the service with the extension context
     * This must be called before using any provider functionality
     */
  public initialize(context: vscode.ExtensionContext): void {
    this.context = context;
    logger.info('Initializing LLM service...');

    // Initialize the provider manager
    providerManager.getInstance(context);

    this.initializeProviders();
  }

  private async initializeProviders() {
    if (!this.context) {
      logger.error('LLMService not initialized with context');
      return;
    }

    // Clear existing providers
    this.providers.clear();

    // Get enabled providers from settings
    const config = vscode.workspace.getConfiguration('codessa.llm');
    let enabledProviders: string[] = config.get('enabledProviders') || ['ollama'];
    if (!Array.isArray(enabledProviders) || enabledProviders.length === 0) {
      logger.warn('No providers enabled in settings. All LLM functionality will be disabled.');
      enabledProviders = [];
    }

    logger.info(`Initializing LLM providers. Enabled providers: ${enabledProviders.length > 0 ? enabledProviders.join(', ') : 'none'}`);

    // Define provider factories
    const providerFactories = [
      // Standard API providers
      { id: 'openai', factory: () => new OpenAIProvider(this.context!) },
      { id: 'anthropic', factory: () => new AnthropicProvider(this.context!) },
      { id: 'googleai', factory: () => new GoogleAIProvider(this.context!) },
      { id: 'mistralai', factory: () => new MistralAIProvider(this.context!) },
      { id: 'cohere', factory: () => new CohereProvider(this.context!) },
      { id: 'deepseek', factory: () => new DeepSeekProvider(this.context!) },

      // Local and self-hosted providers
      { id: 'ollama', factory: () => new OllamaProvider(this.context!) },
      { id: 'lmstudio', factory: () => new LMStudioProvider(this.context!) },

      // Aggregator providers
      { id: 'openrouter', factory: () => new OpenRouterProvider(this.context!) },
      { id: 'huggingface', factory: () => new HuggingFaceProvider(this.context!) },

      // Code-specific model providers
      { id: 'starcoder', factory: () => new StarCoderProvider(this.context!) },
      { id: 'codellama', factory: () => new CodeLlamaProvider(this.context!) },
      { id: 'replit', factory: () => new ReplitProvider(this.context!) },
      { id: 'wizardcoder', factory: () => new WizardCoderProvider(this.context!) },
      { id: 'xwincoder', factory: () => new XwinCoderProvider(this.context!) },
      { id: 'phi', factory: () => new PhiProvider(this.context!) },
      { id: 'yicode', factory: () => new YiCodeProvider(this.context!) },
      { id: 'codegemma', factory: () => new CodeGemmaProvider(this.context!) },
      { id: 'santacoder', factory: () => new SantaCoderProvider(this.context!) },
      { id: 'stablecode', factory: () => new StableCodeProvider(this.context!) },

      // Additional API providers
      { id: 'perplexity', factory: () => new PerplexityAIProvider(this.context!) }
    ];

    // Track initialization errors
    const initializationErrors: string[] = [];
    const skippedProviders: string[] = [];

    // Register only enabled providers
    for (const { id, factory } of providerFactories) {
      if (!enabledProviders.includes(id)) {
        logger.debug(`Skipping provider ${id} (not enabled in settings)`);
        skippedProviders.push(id);
        continue;
      }

      logger.info(`Initializing provider: ${id}`);
      try {
        // Double-check that the provider is still enabled before creating it
        // This prevents race conditions where settings might change during initialization
        const currentConfig = vscode.workspace.getConfiguration('codessa.llm');
        const currentEnabledProviders = currentConfig.get<string[]>('enabledProviders') || ['ollama'];

        if (!currentEnabledProviders.includes(id)) {
          logger.debug(`Provider ${id} was disabled during initialization, skipping`);
          skippedProviders.push(id);
          continue;
        }
        
        // Check for required dependencies
        let missingDependency = false;
        if (id === 'anthropic' && !this.isDependencyAvailable('@anthropic-ai/sdk')) {
          logger.warn(`Provider ${id} requires @anthropic-ai/sdk which is not installed. Skipping.`);
          missingDependency = true;
        } else if (id === 'openai' && !this.isDependencyAvailable('openai')) {
          logger.warn(`Provider ${id} requires openai which is not installed. Skipping.`);
          missingDependency = true;
        }
        
        if (missingDependency) {
          initializationErrors.push(`Provider ${id}: Required dependency not installed`);
          continue;
        }

        const provider = factory();
        if (provider) {
          const typedProvider = provider as ILLMProvider;
          this.registerProvider(typedProvider);
          logger.info(`Successfully registered provider: ${id}`);

          // Only test connection if provider is configured
          if (provider.isConfigured()) {
            try {
              // For local providers, use silent connection test to avoid error popups
              if (id === 'lmstudio' || id === 'ollama') {
                const result = await typedProvider.testConnection('silent-check');
                if (result.success) {
                  logger.info(`Local provider ${id} is running and configured`);
                  // Only try to list models if connection is successful
                  try {
                    const models = await typedProvider.listModels();
                    logger.info(`Provider ${id} has ${models.length} models available`);
                  } catch (modelError) {
                    logger.debug(`Failed to list models for ${id}: ${modelError instanceof Error ? modelError.message : String(modelError)}`);
                  }
                } else {
                  logger.debug(`Local provider ${id} connection test failed: ${result.message}`);
                }
              } else {
                // For API providers, try to get a model ID first
                try {
                  const models = await typedProvider.listModels();
                  const modelId = typedProvider.defaultModel || models[0]?.id;
                  if (!modelId) throw new Error('No model ID available');

                  const result = await typedProvider.testConnection(modelId);
                  if (result.success) {
                    logger.info(`API provider ${id} is configured with ${models.length} models`);
                  } else {
                    throw new Error(result.message);
                  }
                } catch (modelError) {
                  // If we can't list models, try with a default model
                  logger.debug('Failed to list models, trying default:', modelError);
                  const modelId = typedProvider.defaultModel || 'default';
                  const result = await typedProvider.testConnection(modelId);
                  if (result.success) {
                    logger.info(`API provider ${id} is configured`);
                  } else {
                    throw new Error(result.message);
                  }
                }
              }
            } catch (error: unknown) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              logger.warn(`Failed to initialize provider ${id}: ${errorMessage}`);
              initializationErrors.push(`Provider ${id}: ${errorMessage}`);
            }
          } else {
            logger.info(`Provider ${id} registered but not configured`);
          }
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(`Failed to register provider ${id}: ${errorMessage}`);
        initializationErrors.push(`Provider ${id}: ${errorMessage}`);
      }
    }

    // Log summary of initialization
    const initializedCount = this.providers.size;
    const configuredCount = this.getConfiguredProviders().length;
    logger.info(`Initialized ${initializedCount} LLM providers (${configuredCount} configured)`);
    
    if (skippedProviders.length > 0) {
      logger.info(`Skipped ${skippedProviders.length} disabled providers: ${skippedProviders.join(', ')}`);
    }

    // If there were any initialization errors, log them
    if (initializationErrors.length > 0) {
      logger.warn('Some providers failed to initialize:');
      initializationErrors.forEach(error => logger.warn(`  - ${error}`));
    }

    // Notify listeners that providers have changed
    this._onProvidersChanged.fire();

    // If no providers were successfully initialized, log a warning but don't throw an error
    if (initializedCount === 0) {
      logger.warn('No LLM providers were initialized. Users can enable providers in settings.');
      // Don't throw an error - this allows the extension to work without any LLM providers
    }
  }

  private async reinitializeProviders() {
    await this.initializeProviders();
  }

  /**
     * Attempt to register a provider with error handling
     */
  private async tryRegisterProvider(id: string, providerFactory: () => ILLMProvider): Promise<void> {
    try {
      const provider = providerFactory();

      // Only register if the provider initializes successfully
      if (provider) {
        this.registerProvider(provider);
        logger.info(`Successfully registered provider: ${id}`);

        // Try to initialize models if provider is configured
        if (provider.isConfigured()) {
          try {
            // For local providers like LM Studio and Ollama, use a silent check
            // to avoid showing error messages if they're not running
            if (id === 'lmstudio' || id === 'ollama') {
              // First try a silent connection test
              const testResult = await provider.testConnection('silent-check');
              if (testResult.success) {
                const models = await provider.listModels();
                logger.info(`Provider ${id} has ${models.length} models available`);
              } else {
                // Just log the error without showing notifications
                logger.debug(`Provider ${id} is not available: ${testResult.message}`);
              }
            } else {
              // For cloud providers, try to list models normally
              const models = await provider.listModels();
              logger.info(`Provider ${id} has ${models.length} models available`);
            }
          } catch (error) {
            // Log the error but don't let it prevent the provider from being registered
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            logger.warn(`Failed to list models for provider ${id}: ${errorMessage}`);

            // Check if we should suppress this error message
            const warningMessage = `Failed to connect to ${id} provider. The provider is registered but may not work until the connection issue is resolved.`;
                        
            // Only show warnings for cloud providers that are explicitly configured
            // Local providers (ollama, lmstudio) should never show errors during initialization
            if (!shouldSuppressError(warningMessage) &&
                            id !== 'ollama' &&
                            id !== 'lmstudio' &&
                            provider.isConfigured()) {
              // Only show warnings for cloud providers that should be available
              // and only if error suppression is disabled and the provider is configured
              // Import showWarningMessage to use error suppression
              const { showWarningMessage } = require('../ui/notifications');
              showWarningMessage(warningMessage);
            }
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.warn(`Failed to initialize ${id} provider: ${errorMessage}`);

      // Always register the provider even if initialization fails
      // This ensures the UI shows all available providers
      try {
        const provider = providerFactory();
        if (provider) {
          this.registerProvider(provider);
          logger.info(`Registered provider ${id} despite initialization failure`);
        }
      } catch (e) {
        // If we can't even create the provider, just log and continue
        logger.error(`Could not create provider ${id}: ${e instanceof Error ? e.message : String(e)}`);
      }
    }
  }

  /**
     * Registers a new LLM provider
     */
  registerProvider(provider: ILLMProvider): void {
    if (this.providers.has(provider.providerId)) {
      logger.warn(`Provider with ID '${provider.providerId}' is already registered. Overwriting.`);
    }
    this.providers.set(provider.providerId, provider);

    // Also register with the provider manager
    if (this.context) {
      providerManager.getInstance(this.context).registerProvider(provider);
    }
  }

  /**
     * Gets a provider by ID
     */
  getProvider(providerId: string): ILLMProvider | undefined {
    return this.providers.get(providerId);
  }

  /**
     * Gets the appropriate LLM provider based on the config
     */
  async getProviderForConfig(_config: any): Promise<ILLMProvider | undefined> {
    const provider = this.providers.get(_config.provider);

    if (!provider) {
      logger.warn('LLM provider \'' + _config.provider + '\' not found.');
      return undefined;
    }

    if (!this.context) {
      logger.error('LLMService not initialized with context');
      return undefined;
    }

    // Check if the provider is enabled (configured and not disabled)
    const manager = providerManager.getInstance(this.context);
    if (!await manager.isProviderEnabled(provider.providerId)) {
      logger.warn('LLM provider \'' + _config.provider + '\' is not enabled (not configured or explicitly disabled).');
      return undefined;
    }

    return provider;
  }

  /**
     * Gets the default provider based on settings
     */
  async getDefaultProvider(): Promise<ILLMProvider | undefined> {
    if (!this.context) {
      logger.error('LLMService not initialized with context');
      return undefined;
    }

    // Get default provider ID from settings
    const defaultProviderId = providerManager.getInstance(this.context).getDefaultProviderId();
    const provider = this.providers.get(defaultProviderId);
    const manager = providerManager.getInstance(this.context);

    // Check if the default provider is enabled (configured and not disabled)
    if (provider && await manager.isProviderEnabled(provider.providerId)) {
      return provider;
    }
        
    // If the default provider is not available, not configured, or disabled
    logger.warn(`Default provider '${defaultProviderId}' not found, not configured, or disabled. Trying to find another provider.`);
        
    // Try to find an enabled provider
    const enabledProviders = await this.getEnabledProviders();
    if (enabledProviders.length > 0) {
      // Prioritize local providers (Ollama, LM Studio) if they're enabled
      const localProvider = enabledProviders.find(p => 
        p.providerId === 'ollama' || p.providerId === 'lmstudio'
      );
            
      if (localProvider) {
        return localProvider;
      }
            
      // Otherwise return the first enabled provider
      return enabledProviders[0];
    }
        
    // If no enabled providers, try any configured provider
    const configuredProviders = this.getConfiguredProviders();
    if (configuredProviders.length > 0) {
      return configuredProviders[0];
    }
        
    // If no providers are available at all, return undefined
    logger.warn('No LLM providers are available. Users can enable providers in settings.');
    return undefined;
  }

  /**
     * Sets the default provider
     */
  async setDefaultProvider(providerId: string): Promise<boolean> {
    if (!this.context) {
      logger.error('LLMService not initialized with context');
      return false;
    }

    const provider = this.providers.get(providerId);
    if (!provider) {
      logger.warn(`Cannot set default provider: Provider '${providerId}' not found.`);
      return false;
    }

    try {
      const success = await providerManager.getInstance(this.context).setDefaultProviderId(providerId);
      if (success) {
        logger.info(`Set default provider to ${providerId}`);
        return true;
      } else {
        logger.error(`Failed to set default provider to ${providerId}`);
        return false;
      }
    } catch (error) {
      logger.error(`Failed to set default provider to ${providerId}:`, error);
      return false;
    }
  }

  /**
     * Gets all available LLM providers
     */
  getAllProviders(): ILLMProvider[] {
    return Array.from(this.providers.values());
  }

  /**
     * Gets all available configured providers
     */
  getConfiguredProviders(): ILLMProvider[] {
    return Array.from(this.providers.values())
      .filter(provider => provider.isConfigured());
  }
     
  /**
     * Gets all available enabled providers (configured and not disabled)
     */
  async getEnabledProviders(): Promise<ILLMProvider[]> {
    if (!this.context) {
      logger.error('LLMService not initialized with context');
      return [];
    }
        
    const enabledProviders: ILLMProvider[] = [];
    const manager = providerManager.getInstance(this.context);
        
    for (const provider of this.providers.values()) {
      if (await manager.isProviderEnabled(provider.providerId)) {
        enabledProviders.push(provider);
      }
    }
        
    return enabledProviders;
  }

  /**
     * Lists all provider IDs
     */
  listProviderIds(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
     * Gets the default model configuration from settings
     */
  async getDefaultModelConfig(): Promise<LLMConfig> {
    return getDefaultModelConfig();
  }

  /**
     * Updates the configuration for a provider
     */
  async updateProviderConfig(providerId: string, _config: any): Promise<boolean> {
    if (!this.context) {
      logger.error('LLMService not initialized with context');
      return false;
    }

    const provider = this.providers.get(providerId);
    if (!provider) {
      logger.warn('Cannot update provider config: Provider ' + providerId + ' not found.');
      return false;
    }

    try {
      await provider.updateConfig(_config);
      logger.info('Updated configuration for provider ' + providerId);
      return true;
    } catch (error) {
      logger.error('Failed to update configuration for provider ' + providerId + ':', error);
      return false;
    }
  }

  /**
     * Gets the configuration for a provider
     */
  getProviderConfig(providerId: string): LLMProviderConfig | undefined {
    const provider = this.providers.get(providerId);
    if (!provider) {
      return undefined;
    }

    return provider.getConfig();
  }
}

export const llmService = new LLMService();