{"version": 3, "file": "advancedSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/advancedSettingsSection.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,sEA+IC;AAhJD,uCAAuC;AACvC,SAAgB,6BAA6B,CAAC,SAAsB,EAAE,QAAa;IACjF,IAAI,IAAI,GAAG;;;;;;;;;;aAUA,CAAC;IACZ,IAAI,IAAI,2DAA2D,CAAC;IAEpE,6BAA6B;IAC7B,IAAI,IAAI;;;2CAGiC,QAAQ,CAAC,YAAY,IAAI,EAAE;;WAE3D,CAAC;IAEV,0BAA0B;IAC1B,IAAI,IAAI;;;;oBAIU,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU;;;WAGhE,CAAC;IAEV,+BAA+B;IAC/B,IAAI,IAAI;;;2CAGiC,OAAO,QAAQ,CAAC,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;;WAEjH,CAAC;IAEV,sBAAsB;IACtB,IAAI,IAAI;;;;WAIC,CAAC;IAEV,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;IAG3B,gDAAgD;IAChD,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;QAC7E,kDAAO,qBAAqB,IAAE,IAAI,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;YACnD,SAAS,CAAC;gBACR,KAAK,EAAE,yBAAyB;gBAChC,OAAO,EAAE,wGAAwG,QAAQ,CAAC,YAAY,IAAI,EAAE,gCAAgC;gBAC5K,SAAS,EAAE,GAAG,EAAE;oBACd,MAAM,GAAG,GAAG,QAAQ,CAAE,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAsB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBACnG,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;wBACvC,KAAK,CAAC,0CAA0C,CAAC,CAAC;wBAClD,OAAO;oBACT,CAAC;oBACD,QAAQ,CAAC,YAAY,GAAG,GAAG,CAAC;oBAC5B,6BAA6B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,kDAAkD;IAClD,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;QAC/E,kDAAO,qBAAqB,IAAE,IAAI,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;YACnD,SAAS,CAAC;gBACR,KAAK,EAAE,sBAAsB;gBAC7B,OAAO,EAAE,2GAA2G,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,mCAAmC;gBAC/L,SAAS,EAAE,GAAG,EAAE;oBACd,MAAM,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAqB,CAAC;oBAC9E,QAAQ,CAAC,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC;oBACxC,6BAA6B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,uDAAuD;IACvD,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;QACnF,kDAAO,qBAAqB,IAAE,IAAI,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;YACnD,SAAS,CAAC;gBACR,KAAK,EAAE,2BAA2B;gBAClC,OAAO,EAAE,0GAA0G,OAAO,QAAQ,CAAC,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,gCAAgC;gBACpO,SAAS,EAAE,GAAG,EAAE;oBACd,MAAM,GAAG,GAAG,QAAQ,CAAE,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAsB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBACzG,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,EAAE,CAAC;wBACtC,KAAK,CAAC,gDAAgD,CAAC,CAAC;wBACxD,OAAO;oBACT,CAAC;oBACD,QAAQ,CAAC,kBAAkB,GAAG,GAAG,CAAC;oBAClC,6BAA6B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,sCAAsC;IACtC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;QAC/E,MAAM,MAAM,GAAG,gBAAgB,EAAE,CAAC;QAElC,+CAA+C;QAC/C,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC;YACzC,OAAO,EAAE,mBAAmB;YAC5B,KAAK,EAAE,4BAA4B;YACnC,OAAO,EAAE,uHAAuH;YAChI,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QAEH,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC;gBACH,0BAA0B;gBAC1B,MAAM,MAAM,CAAC,WAAW,CAAC;oBACvB,OAAO,EAAE,cAAc;iBACxB,CAAC,CAAC;gBAEH,iBAAiB;gBACjB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE5D,yCAAyC;gBACzC,MAAM,CAAC,WAAW,CAAC;oBACjB,OAAO,EAAE,wBAAwB;oBACjC,OAAO,EAAE,wCAAwC;iBAClD,CAAC,CAAC;gBAEH,oBAAoB;gBACpB,MAAM,CAAC,WAAW,CAAC;oBACjB,OAAO,EAAE,cAAc;iBACxB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,WAAW,CAAC;oBACjB,OAAO,EAAE,kBAAkB;oBAC3B,OAAO,EAAE,qCAAqC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBACvG,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Declare the acquireVsCodeApi function for TypeScript\ndeclare function acquireVsCodeApi(): {\n  postMessage(message: any): Promise<any>;\n};\n\n// Message handler type\ntype MessageHandler = (message: any) => void;\n\n// Advanced section logic and rendering\nexport function renderAdvancedSettingsSection(container: HTMLElement, settings: any) {\n  let html = `<style>\n        .adv-section-title { font-size:1.18em; font-weight:600; margin-bottom:10px; color:#222; letter-spacing:0.01em; }\n        .adv-group { margin-bottom:18px; padding:12px 14px; background:#f8fafc; border-radius:8px; }\n        .adv-label { font-weight:500; margin-bottom:3px; display:block; color:#374151; }\n        .adv-desc { color:#6b7280; font-size:0.97em; margin-bottom:6px; }\n        .adv-btn { background:#2563eb; color:#fff; border:none; border-radius:5px; padding:4px 12px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s; }\n        .adv-btn:hover { background:#1d4ed8; }\n        .adv-btn[disabled] { background:#e5e7eb; color:#888; cursor:not-allowed; }\n        .adv-danger { background:#fee2e2; color:#b91c1c; border:none; border-radius:5px; padding:4px 12px; font-size:1em; cursor:pointer; transition:background 0.15s; margin-top:8px; }\n        .adv-danger:hover { background:#fecaca; color:#dc2626; }\n    </style>`;\n  html += '<div class=\"adv-section-title\">⚙️ Advanced Settings</div>';\n\n  // --- Data Sync Interval ---\n  html += `<div class=\"adv-group\">\n        <span class=\"adv-label\">Data Sync Interval</span>\n        <span class=\"adv-desc\">How often (in minutes) should your data be synced?</span>\n        <span style=\"margin-right:12px;\">${settings.syncInterval || 10} min</span>\n        <button class=\"adv-btn\" id=\"editSyncIntervalBtn\">Edit</button>\n    </div>`;\n\n  // --- Verbose Logging ---\n  html += `<div class=\"adv-group\">\n        <span class=\"adv-label\">Verbose Logging</span>\n        <span class=\"adv-desc\">Enable detailed log output for debugging and diagnostics.</span>\n        <div style=\"display:flex;align-items:center;gap:10px;\">\n            <span>${settings.verboseLogging ? '<b>Enabled</b>' : 'Disabled'}</span>\n            <button class=\"adv-btn\" id=\"editVerboseLoggingBtn\">Edit</button>\n        </div>\n    </div>`;\n\n  // --- Max Concurrent Tasks ---\n  html += `<div class=\"adv-group\">\n        <span class=\"adv-label\">Max Concurrent Tasks</span>\n        <span class=\"adv-desc\">Limit the number of tasks that can run at the same time.</span>\n        <span style=\"margin-right:12px;\">${typeof settings.maxConcurrentTasks === 'number' ? settings.maxConcurrentTasks : 4}</span>\n        <button class=\"adv-btn\" id=\"editMaxConcurrentTasksBtn\">Edit</button>\n    </div>`;\n\n  // --- Danger Zone ---\n  html += `<div class=\"adv-group\" style=\"background:#fff0f0;border:1px solid #fecaca;\">\n        <span class=\"adv-label\" style=\"color:#b91c1c;\">Danger Zone</span>\n        <span class=\"adv-desc\">Clear all application data. This action cannot be undone!</span>\n        <button class=\"adv-danger\" id=\"clearAppDataBtn\">Clear All Data</button>\n    </div>`;\n\n  container.innerHTML = html;\n\n\n  // --- Modal logic for editing Sync Interval ---\n  document.getElementById('editSyncIntervalBtn')?.addEventListener('click', () => {\n    import('../components/modal').then(({ showModal }) => {\n      showModal({\n        title: 'Edit Data Sync Interval',\n        content: `<label>Sync Interval (minutes): <input type='number' id='modalSyncInterval' min='1' max='120' value='${settings.syncInterval || 10}' style='width:60px;'></label>`,\n        onConfirm: () => {\n          const val = parseInt((document.getElementById('modalSyncInterval') as HTMLInputElement).value, 10);\n          if (isNaN(val) || val < 1 || val > 120) {\n            alert('Sync interval must be between 1 and 120.');\n            return;\n          }\n          settings.syncInterval = val;\n          renderAdvancedSettingsSection(container, settings);\n        }\n      });\n    });\n  });\n\n  // --- Modal logic for editing Verbose Logging ---\n  document.getElementById('editVerboseLoggingBtn')?.addEventListener('click', () => {\n    import('../components/modal').then(({ showModal }) => {\n      showModal({\n        title: 'Edit Verbose Logging',\n        content: `<label style='display:flex;align-items:center;gap:8px;'><input type='checkbox' id='modalVerboseLogging' ${settings.verboseLogging ? 'checked' : ''}/> Enable Verbose Logging</label>`,\n        onConfirm: () => {\n          const cb = document.getElementById('modalVerboseLogging') as HTMLInputElement;\n          settings.verboseLogging = !!cb?.checked;\n          renderAdvancedSettingsSection(container, settings);\n        }\n      });\n    });\n  });\n\n  // --- Modal logic for editing Max Concurrent Tasks ---\n  document.getElementById('editMaxConcurrentTasksBtn')?.addEventListener('click', () => {\n    import('../components/modal').then(({ showModal }) => {\n      showModal({\n        title: 'Edit Max Concurrent Tasks',\n        content: `<label>Max Concurrent Tasks: <input type='number' id='modalMaxConcurrentTasks' min='1' max='32' value='${typeof settings.maxConcurrentTasks === 'number' ? settings.maxConcurrentTasks : 4}' style='width:60px;'></label>`,\n        onConfirm: () => {\n          const val = parseInt((document.getElementById('modalMaxConcurrentTasks') as HTMLInputElement).value, 10);\n          if (isNaN(val) || val < 1 || val > 32) {\n            alert('Max concurrent tasks must be between 1 and 32.');\n            return;\n          }\n          settings.maxConcurrentTasks = val;\n          renderAdvancedSettingsSection(container, settings);\n        }\n      });\n    });\n  });\n\n  // --- Danger Zone: Clear All Data ---\n  document.getElementById('clearAppDataBtn')?.addEventListener('click', async () => {\n    const vscode = acquireVsCodeApi();\n    \n    // Show confirmation dialog using VS Code's API\n    const confirmed = await vscode.postMessage({\n      command: 'showConfirmDialog',\n      title: 'Clear All Application Data',\n      message: 'This action will permanently delete all application data. This cannot be undone.\\n\\nAre you sure you want to proceed?',\n      modal: true\n    });\n\n    if (confirmed) {\n      try {\n        // Clear extension storage\n        await vscode.postMessage({\n          command: 'clearStorage'\n        });\n        \n        // Reset settings\n        Object.keys(settings).forEach(k => { delete settings[k]; });\n        \n        // Notify user that data has been cleared\n        vscode.postMessage({\n          command: 'showInformationMessage',\n          message: 'All application data has been cleared.'\n        });\n        \n        // Reload the window\n        vscode.postMessage({\n          command: 'reloadWindow'\n        });\n      } catch (error) {\n        vscode.postMessage({\n          command: 'showErrorMessage',\n          message: `Failed to clear application data: ${error instanceof Error ? error.message : String(error)}`\n        });\n      }\n    }\n  });\n}\n\n"]}