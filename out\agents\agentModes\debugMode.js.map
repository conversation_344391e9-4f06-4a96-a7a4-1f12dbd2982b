{"version": 3, "file": "debugMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/debugMode.ts"], "names": [], "mappings": ";;;AACA,mDAA4E;AAG5E,yCAAsC;AACtC,qDAAkD;AAClD,+DAA4D;AAE5D;;GAEG;AACH,MAAa,SAAU,SAAQ,6BAAa;IACjC,EAAE,GAAG,OAAO,CAAC;IACb,WAAW,GAAG,OAAO,CAAC;IACtB,WAAW,GAAG,6BAA6B,CAAC;IAC5C,IAAI,GAAG,QAAQ,CAAC;IAChB,kBAAkB,GAAG,2BAAW,CAAC,cAAc,CAAC;IAChD,yBAAyB,GAAG,KAAK,CAAC;IAClC,sBAAsB,GAAG,KAAK,CAAC;IAExC;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY,EACZ,aAA4B,EAC5B,gBAAsC;QAEtC,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,OAAO,EAAE,CAAC,CAAC;YAErE,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE7E,kCAAkC;YAClC,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;wBACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,qCAAqC,CAAC,CAAC;oBAC/F,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8CAA8C,EAAE,WAAW,CAAC,CAAC;gBAClF,kCAAkC;YACpC,CAAC;YAED,yCAAyC;YACzC,MAAM,YAAY,GAAG,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC,kBAAkB,gBAAgB,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7G,MAAM,UAAU,GAAG,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAC,gBAAgB,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAErG,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,YAAY,EAAE;gBACtD,cAAc;gBACd,aAAa;gBACb,OAAO;gBACP,YAAY;gBACZ,UAAU;aACX,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAEnE,wCAAwC;YACxC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,gBAAgB,OAAO,EAAE,CAAC,CAAC;oBAChE,MAAM,WAAW,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,EAAE,WAAW,CAAC,CAAC;gBAC9E,qCAAqC;YACvC,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,wCAAwC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1G,CAAC;IACH,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG,EAAE,+CAA+C;YACjE,SAAS,EAAE,IAAI,EAAI,yCAAyC;YAC5D,IAAI,EAAE,MAAM;SACb,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,eAAe;IACnB,mFAAmF;IACnF,KAAY;IACZ,mFAAmF;IACnF,aAA4B;QAE5B,OAAO;;;;;;;;;;;;;CAaV,CAAC;IACA,CAAC;IAED;;SAEK;IACL,eAAe;QAKb,OAAO;YACL,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;CAwBnB;YACK,YAAY,EAAE;;;;;CAKnB;SACI,CAAC;IACJ,CAAC;CACF;AA1JD,8BA0JC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { contextManager } from './contextManager';\nimport { promptManager } from '../../prompts/promptManager';\n\n/**\n * Debug Mode - Uses specified files as context for debugging\n */\nexport class DebugMode extends OperationMode {\n  readonly id = 'debug';\n  readonly displayName = 'Debug';\n  readonly description = 'Debug issues with your code';\n  readonly icon = '$(bug)';\n  readonly defaultContextType = ContextType.SELECTED_FILES;\n  readonly requiresHumanVerification = false;\n  readonly supportsMultipleAgents = false;\n\n  /**\n     * Process a user message in Debug mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    contextSource: ContextSource,\n    additionalParams?: Record<string, any>\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Debug mode: ${message}`);\n\n      // Get context content\n      const contextContent = await contextManager.getContextContent(contextSource);\n\n      // Add memory context if available\n      let memoryContext = '';\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          const relevantMemories = await agentMemory.getRelevantMemories(message);\n          if (relevantMemories && relevantMemories.length > 0) {\n            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to debug context`);\n          }\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to retrieve memory context for debug:', memoryError);\n        // Continue without memory context\n      }\n\n      // Prepare the prompt using promptManager\n      const errorMessage = additionalParams?.errorMessage ? `Error message: ${additionalParams.errorMessage}` : '';\n      const stackTrace = additionalParams?.stackTrace ? `Stack trace: ${additionalParams.stackTrace}` : '';\n\n      const prompt = promptManager.renderPrompt('mode.debug', {\n        contextContent,\n        memoryContext,\n        message,\n        errorMessage,\n        stackTrace\n      });\n\n      // Generate response using the agent\n      const response = await agent.generate(prompt, this.getLLMParams());\n\n      // Store the debugging session in memory\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          await agentMemory.addMessage('user', `Debug issue: ${message}`);\n          await agentMemory.addMessage('assistant', response);\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to store debug session in memory:', memoryError);\n        // Continue without storing in memory\n      }\n\n      return response;\n    } catch (error) {\n      Logger.instance.error('Error processing message in Debug mode:', error);\n      return `Error processing your debug request: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Get LLM parameters specific to Debug mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.3, // Lower temperature for more precise debugging\n      maxTokens: 2000,   // Longer responses for detailed analysis\n      mode: 'task'\n    };\n  }\n\n  /**\n     * Get the system prompt for Debug mode\n     */\n  async getSystemPrompt(\n    // @ts-ignore - Parameter required by interface but not used in this implementation\n    agent: Agent,\n    // @ts-ignore - Parameter required by interface but not used in this implementation\n    contextSource: ContextSource\n  ): Promise<string> {\n    return `\nYou are an AI assistant specialized in debugging code.\nYour task is to help the user identify and fix issues in their code.\nAnalyze the code carefully, identify potential problems, and suggest solutions.\nBe methodical in your approach:\n1. Understand the error or issue description\n2. Analyze the relevant code\n3. Identify potential causes\n4. Suggest specific fixes with code examples\n5. Explain why the issue occurred and how the fix resolves it\n\nWhen suggesting fixes, provide complete code snippets that the user can directly implement.\nInclude line numbers and file names when referencing specific parts of the code.\n`;\n  }\n\n  /**\n     * Get UI components specific to Debug mode\n     */\n  getUIComponents(): {\n    controlPanel?: string;\n    contextPanel?: string;\n    messageInput?: string;\n  } {\n    return {\n      contextPanel: `\n<div class=\"context-panel\">\n    <div class=\"context-header\">\n        <h3>Debug Context</h3>\n        <div class=\"context-controls\">\n            <button id=\"btn-refresh-context\" title=\"Refresh Context\"><i class=\"codicon codicon-refresh\"></i></button>\n            <button id=\"btn-select-files\" title=\"Select Files\"><i class=\"codicon codicon-file-code\"></i></button>\n            <button id=\"btn-select-folders\" title=\"Select Folders\"><i class=\"codicon codicon-folder\"></i></button>\n        </div>\n    </div>\n    <div class=\"context-type\">\n        <select id=\"context-type-selector\">\n            <option value=\"selected_files\">Selected Files</option>\n            <option value=\"current_file\">Current File</option>\n            <option value=\"custom\">Custom</option>\n        </select>\n    </div>\n    <div id=\"context-files-list\" class=\"context-files-list\"></div>\n    <div class=\"error-info\">\n        <h4>Error Information</h4>\n        <textarea id=\"error-message\" placeholder=\"Paste error message here...\"></textarea>\n        <textarea id=\"stack-trace\" placeholder=\"Paste stack trace here...\"></textarea>\n    </div>\n</div>\n`,\n      messageInput: `\n<div class=\"message-input-container\">\n    <textarea id=\"message-input\" placeholder=\"Describe the issue you're experiencing...\"></textarea>\n    <button id=\"btn-send\" title=\"Send\"><i class=\"codicon codicon-send\"></i></button>\n</div>\n`\n    };\n  }\n}\n"]}