"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.operationModeRegistry = exports.OperationMode = exports.ContextType = void 0;
const vscode = __importStar(require("vscode"));
/**
 * Context type for operation modes
 */
var ContextType;
(function (ContextType) {
    ContextType["NONE"] = "none";
    ContextType["ENTIRE_CODEBASE"] = "entire_codebase";
    ContextType["SELECTED_FILES"] = "selected_files";
    ContextType["CURRENT_FILE"] = "current_file";
    ContextType["CUSTOM"] = "custom";
})(ContextType || (exports.ContextType = ContextType = {}));
/**
 * Base class for operation modes
 */
class OperationMode {
    /** Unique identifier for the mode */
    id;
    /** Display name for the mode */
    displayName;
    /** Description of the mode */
    description;
    /** Icon for the mode */
    icon;
    /** Default context type for this mode */
    defaultContextType;
    /** Status bar item for the mode (optional, can be used by subclasses) */
    statusBarItem;
    /** Logger instance for subclasses */
    logger = (typeof globalThis.Logger !== 'undefined' ? globalThis.Logger.instance : undefined) || require('../../logger').Logger.instance;
    constructor(id, displayName, description, icon, defaultContextType = ContextType.CURRENT_FILE) {
        this.id = id;
        this.displayName = displayName;
        this.description = description;
        this.icon = icon;
        this.defaultContextType = defaultContextType;
    }
    /**
       * Initialize the mode. Subclasses can override to provide custom setup.
       * @param context Extension context
       */
    async initialize(context) {
        // Default: set up a status bar item if not already present
        if (!this.statusBarItem) {
            this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
            this.statusBarItem.text = `${this.icon} ${this.displayName}: Idle`;
            this.statusBarItem.tooltip = `Codessa ${this.displayName} Mode`;
            context.subscriptions.push(this.statusBarItem);
        }
    }
    /**
       * Dispose of any resources held by this mode. Subclasses can override.
       */
    async dispose() {
        if (this.statusBarItem) {
            this.statusBarItem.dispose();
            this.statusBarItem = undefined;
        }
    }
    /**
     * Get LLM parameters for this mode
     */
    getLLMParams() {
        return {
            prompt: '',
            modelId: '',
            temperature: 0.7,
            maxTokens: 2000,
            stopSequences: [],
            mode: 'task'
        };
    }
}
exports.OperationMode = OperationMode;
/**
 * Registry for operation modes
 */
class OperationModeRegistry {
    modes = new Map();
    defaultModeId;
    /**
       * Register a mode
       */
    registerMode(mode) {
        this.modes.set(mode.id, mode);
    }
    /**
       * Get a mode by ID
       */
    getMode(id) {
        return this.modes.get(id);
    }
    /**
       * Get all registered modes
       */
    getAllModes() {
        return Array.from(this.modes.values());
    }
    /**
       * Set the default mode by ID
       */
    setDefaultMode(id) {
        if (this.modes.has(id)) {
            this.defaultModeId = id;
        }
        else {
            throw new Error(`Mode with id '${id}' not registered.`);
        }
    }
    /**
       * Get the default mode (if any)
       */
    getDefaultMode() {
        return this.defaultModeId ? this.modes.get(this.defaultModeId) : undefined;
    }
    /**
       * Initialize all registered modes
       */
    async initializeModes(context) {
        for (const mode of this.modes.values()) {
            if (typeof mode.initialize === 'function') {
                await mode.initialize(context);
            }
        }
    }
}
// Export a singleton instance
exports.operationModeRegistry = new OperationModeRegistry();
//# sourceMappingURL=operationMode.js.map