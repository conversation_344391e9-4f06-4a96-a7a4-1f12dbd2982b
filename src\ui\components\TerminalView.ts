import * as vscode from 'vscode';
import { PseudoTerminal } from '../../tools/pseudoTerminal';
import { terminalSettings, TerminalViewType } from '../settings/sections/terminalSettingsSection';

export class TerminalView implements vscode.WebviewViewProvider {
    public static readonly viewType = 'codessa.terminalView';
    private _view?: vscode.WebviewView;
    private _terminal?: PseudoTerminal;
    private _disposables: vscode.Disposable[] = [];
    private _outputBuffer: string = '';

    constructor(private readonly _extensionUri: vscode.Uri) {
        // Handle configuration changes
        this._disposables.push(
            vscode.workspace.onDidChangeConfiguration(e => {
                if (e.affectsConfiguration('codessa.terminal')) {
                    this.updateView();
                }
            })
        );
    }

    public resolveWebviewView(webviewView: vscode.WebviewView) {
        this._view = webviewView;
        
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        // Set up message handler for the webview
        this._disposables.push(
            webviewView.webview.onDidReceiveMessage(
                this.handleWebviewMessage.bind(this)
            )
        );

        this.updateView();
    }

    private async updateView() {
        if (!this._view) return;
        
        const settings = terminalSettings.getSettings();
        const shouldShow = settings.showTerminal && 
                         (settings.viewType === 'sideView' || settings.viewType === 'both');

        if (shouldShow) {
            await this.initializeTerminal();
            this._view.webview.html = this.getTerminalHtml();
            
            // Send any buffered output to the webview
            if (this._outputBuffer) {
                this._view.webview.postMessage({
                    command: 'terminalOutput',
                    text: this._outputBuffer
                });
                this._outputBuffer = '';
            }
        } else {
            this.disposeTerminal();
            this._view.webview.html = this.getEmptyStateHtml();
        }
    }

    private async initializeTerminal() {
        if (this._terminal) return;
        
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        const shell = vscode.env.shell || (process.platform === 'win32' ? 'powershell.exe' : 'bash');
        
        this._terminal = new PseudoTerminal(shell, cwd);
        
        // Handle terminal output
        this._terminal.onOutput(data => {
            this.sendOutputToWebview(data);
        });
        
        // Handle terminal close
        this._terminal.onExit(() => {
            this.disposeTerminal();
        });
    }

    private sendOutputToWebview(text: string) {
        if (this._view) {
            this._view.webview.postMessage({
                command: 'terminalOutput',
                text: text
            });
        } else {
            // Buffer the output until the webview is ready
            this._outputBuffer += text;
        }
    }

    private handleWebviewMessage(message: any) {
        switch (message.command) {
            case 'terminalInput':
                this._terminal?.write(message.text);
                break;
                
            case 'toggleTerminalView':
                this.toggleTerminalView();
                break;
        }
    }
    
    private async toggleTerminalView() {
        const settings = terminalSettings.getSettings();
        let newViewType: TerminalViewType;
        
        switch (settings.viewType) {
            case TerminalViewType.SideView:
                newViewType = TerminalViewType.DefaultTerminal;
                break;
            case TerminalViewType.DefaultTerminal:
                newViewType = TerminalViewType.Both;
                break;
            case TerminalViewType.Both:
            default:
                newViewType = TerminalViewType.SideView;
        }
        
        await terminalSettings.updateSettings({ viewType: newViewType });
        this.updateView();
    }

    private disposeTerminal() {
        if (this._terminal) {
            this._terminal.dispose();
            this._terminal = undefined;
        }
        this._outputBuffer = '';
    }
    
    public dispose() {
        this.disposeTerminal();
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];
    }

    private getTerminalHtml() {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body { 
                        margin: 0; 
                        padding: 0;
                        font-family: var(--vscode-font-family);
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                    }
                    .terminal-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 8px 12px;
                        border-bottom: 1px solid var(--vscode-panel-border);
                    }
                    .terminal-title {
                        font-weight: 600;
                    }
                    .terminal-toggle {
                        background: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        padding: 4px 12px;
                        border-radius: 2px;
                        cursor: pointer;
                        font-size: 12px;
                    }
                    .terminal-toggle:hover {
                        background: var(--vscode-button-hoverBackground);
                    }
                    #terminal { 
                        background: var(--vscode-terminal-background);
                        color: var(--vscode-terminal-foreground);
                        padding: 8px;
                        height: calc(100vh - 60px);
                        overflow: auto;
                        font-family: var(--vscode-editor-font-family);
                        font-size: var(--vscode-editor-font-size);
                        white-space: pre-wrap;
                        line-height: 1.4;
                    }
                    #terminal:focus {
                        outline: none;
                    }
                </style>
            </head>
            <body>
                <div class="terminal-header">
                    <div class="terminal-title">Terminal</div>
                    <button class="terminal-toggle" id="toggleTerminal">Show in Default Terminal</button>
                </div>
                <div id="terminal" tabindex="0"></div>
                <script>
                    const terminal = document.getElementById('terminal');
                    const toggleBtn = document.getElementById('toggleTerminal');
                    
                    // Focus the terminal div for keyboard input
                    terminal.focus();
                    
                    // Handle terminal toggle
                    toggleBtn.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'toggleTerminalView'
                        });
                    });
                    
                    // Handle keyboard input
                    terminal.addEventListener('keydown', (e) => {
                        // Don't handle special keys
                        if (e.ctrlKey || e.altKey || e.metaKey) {
                            return;
                        }
                        
                        // Send the key to the extension
                        vscode.postMessage({
                            command: 'terminalInput',
                            text: e.key
                        });
                        
                        // Prevent default to avoid double input
                        e.preventDefault();
                    });
                    
                    // Handle messages from the extension
                    window.addEventListener('message', event => {
                        const message = event.data;
                        switch (message.command) {
                            case 'terminalOutput':
                                terminal.textContent += message.text;
                                terminal.scrollTop = terminal.scrollHeight;
                                break;
                            case 'terminalClear':
                                terminal.textContent = '';
                                break;
                        }
                    });
                </script>
            </body>
            </html>`;
    }

    private getEmptyStateHtml() {
        return `
            <!DOCTYPE html>
            <html>
            <body style="padding: 20px; font-family: var(--vscode-font-family);">
                <p>Terminal is disabled or set to show in default terminal view.</p>
            </body>
            </html>`;
    }

    dispose() {
        this.disposeTerminal();
    }
}
