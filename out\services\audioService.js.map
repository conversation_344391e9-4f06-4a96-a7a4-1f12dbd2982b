{"version": 3, "file": "audioService.js", "sourceRoot": "", "sources": ["../../src/services/audioService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,sCAAmC;AACnC,4DAAuF;AACvF,2DAAkE;AAClE,wDAAqD;AAErD,MAAa,YAAY;IASc;IAR7B,MAAM,CAAC,QAAQ,CAAe;IAC9B,cAAc,CAA4B;IAC1C,YAAY,GAAW,EAAE,CAAC;IAC1B,gBAAgB,CAAqB;IACrC,aAAa,GAAgC,IAAI,CAAC;IAClD,YAAY,GAAG,KAAK,CAAC;IACrB,cAAc,GAAsB,IAAI,CAAC;IAEjD,YAAqC,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QACnE,IAAI,CAAC,gBAAgB,GAAG,gCAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACpC,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,OAAgC;QACxD,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAEO,0BAA0B;QAChC,IAAI,iBAAiB,IAAI,MAAM,EAAE,CAAC;YAChC,yCAAyC;YACzC,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC;YACD,eAAe,CAAC,eAAe,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,MAAM,MAAM,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,yBAAW,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC5E,EAAE,EAAE,KAAK,CAAC,QAAQ;YAClB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,QAAQ,EAAE,KAAK,CAAC,IAAI;YACpB,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;YACzD,QAAQ,EAAE,yBAAW,CAAC,IAAI;SAC3B,CAAC,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC;YACH,kDAAkD;YAClD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;gBACvD,KAAK,EAAE;oBACL,gBAAgB,EAAE,IAAI;oBACtB,gBAAgB,EAAE,IAAI;oBACtB,eAAe,EAAE,IAAI;oBACrB,UAAU,EAAE,KAAK;oBACjB,YAAY,EAAE,CAAC;iBAChB;aACF,CAAC,CAAC;YAEH,qCAAqC;YACrC,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,wBAAwB;gBAClC,kBAAkB,EAAE,MAAM;aAC3B,CAAC;YAEF,gDAAgD;YAChD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrD,OAAO,CAAC,QAAQ,GAAG,WAAW,CAAC;YACjC,CAAC;YAED,IAAI,CAAC,cAAc,GAAG,IAAI,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACzD,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YAEvB,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;gBAC9C,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;oBACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAK,IAAI,EAAE;gBACtC,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,WAAW,EAAE,CAAC,CAAC;oBAEtG,yCAAyC;oBACzC,MAAM,WAAW,GAAG,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC;oBAClD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;oBAE/C,gCAAgC;oBAChC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;oBAEjC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,UAAU,CAAC,MAAM,QAAQ,CAAC,CAAC;gBAC1E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACnE,CAAC;wBAAS,CAAC;oBACT,kBAAkB;oBAClB,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;gBACtC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBACrD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC5B,CAAC,CAAC;YAEF,2DAA2D;YAC3D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YACnD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAEzB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC7C,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAK,IAAI,EAAE;oBACtC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;oBACrE,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;oBAChC,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE;wBACtB,MAAM,UAAU,GAAG,MAAM,CAAC,MAAgB,CAAC;wBAC3C,OAAO,CAAC,UAAU,CAAC,CAAC;oBACtB,CAAC,CAAC;oBACF,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAClC,CAAC,CAAC;gBACF,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEM,KAAK,CAAC,KAAK,CAAC,IAAY;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;QACrD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,QAAQ,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC5B,KAAK,yBAAW,CAAC,MAAM;oBACrB,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC5C,MAAM;gBACR,KAAK,yBAAW,CAAC,IAAI;oBACnB,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC1C,MAAM;gBACR,KAAK,yBAAW,CAAC,SAAS;oBACxB,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC/C,MAAM;gBACR,KAAK,yBAAW,CAAC,MAAM;oBACrB,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC5C,MAAM;gBACR,KAAK,yBAAW,CAAC,MAAM;oBACrB,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC5C,MAAM;gBACR,KAAK,yBAAW,CAAC,UAAU;oBACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAChD,MAAM;gBACR;oBACE,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAY,EAAE,QAAa;QAC3D,4BAA4B;QAC5B,MAAM,qBAAS,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC9F,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,IAAY,EAAE,QAAa;QACtD,uEAAuE;QACvE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,iBAAiB,IAAI,MAAM,EAAE,CAAC;YACjE,MAAM,SAAS,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,MAAM,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;YAC3C,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAC;YACnG,IAAI,aAAa;gBAAE,SAAS,CAAC,KAAK,GAAG,aAAa,CAAC;YACnD,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC/B,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YACjC,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;YACnC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,SAAS,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;gBAClC,SAAS,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC7C,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,qBAAS,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,QAAa;QACxD,2BAA2B;QAC3B,MAAM,qBAAS,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC9F,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,QAAa;QACxD,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC9B,KAAK,EAAE,EAAE,IAAI,EAAE;gBACf,KAAK,EAAE;oBACL,YAAY,EAAE,OAAO;oBACrB,IAAI,EAAE,QAAQ,CAAC,KAAK,IAAI,kBAAkB;iBAC3C;gBACD,WAAW,EAAE;oBACX,aAAa,EAAE,KAAK;oBACpB,YAAY,EAAE,QAAQ,CAAC,IAAI,IAAI,GAAG;oBAClC,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,GAAG;oBAC5B,YAAY,EAAE,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;iBAChD;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,6BAA6B;gBACvC,IAAI,EAAE,qBAAqB;gBAC3B,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;oBACpD,cAAc,EAAE,kBAAkB;oBAClC,gBAAgB,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;iBAC9C;aACF,CAAC;YAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAQ,EAAE,EAAE;oBAC9C,IAAI,IAAI,GAAG,EAAE,CAAC;oBACd,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAU,EAAE,EAAE,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;oBAC9C,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;wBACjB,IAAI,CAAC;4BACH,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAClC,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;gCAC1B,yBAAyB;gCACzB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gCAC9C,OAAO,EAAE,CAAC;4BACZ,CAAC;iCAAM,CAAC;gCACN,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;4BACjD,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACxB,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACpB,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,QAAa;QACxD,gCAAgC;QAChC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC9B,KAAK,EAAE,OAAO;gBACd,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,OAAO;gBAChC,eAAe,EAAE,KAAK;gBACtB,KAAK,EAAE,QAAQ,CAAC,IAAI,IAAI,GAAG;aAC5B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,gBAAgB;gBAC1B,IAAI,EAAE,kBAAkB;gBACxB,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;oBACpD,cAAc,EAAE,kBAAkB;oBAClC,gBAAgB,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;iBAC9C;aACF,CAAC;YAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAQ,EAAE,EAAE;oBAC9C,MAAM,MAAM,GAAU,EAAE,CAAC;oBACzB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAU,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBACnD,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;wBACjB,IAAI,CAAC;4BACH,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;4BAC1C,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;4BACnC,OAAO,EAAE,CAAC;wBACZ,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACxB,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACpB,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAAY,EAAE,QAAa;QAC5D,oCAAoC;QACpC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC;YAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC9B,IAAI;gBACJ,QAAQ,EAAE,uBAAuB;gBACjC,cAAc,EAAE;oBACd,SAAS,EAAE,GAAG;oBACd,gBAAgB,EAAE,GAAG;oBACrB,KAAK,EAAE,GAAG;oBACV,iBAAiB,EAAE,IAAI;iBACxB;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,mBAAmB;gBAC7B,IAAI,EAAE,sBAAsB,OAAO,EAAE;gBACrC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU;oBACzC,cAAc,EAAE,kBAAkB;oBAClC,gBAAgB,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;iBAC9C;aACF,CAAC;YAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAQ,EAAE,EAAE;oBAC9C,MAAM,MAAM,GAAU,EAAE,CAAC;oBACzB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAU,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBACnD,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;wBACjB,IAAI,CAAC;4BACH,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;4BAC1C,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;4BACnC,OAAO,EAAE,CAAC;wBACZ,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACxB,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACpB,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,WAAmB;QAC3C,oCAAoC;QACpC,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QACvD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;IAEO,gBAAgB,CAAC,WAAmB;QAC1C,6CAA6C;QAC7C,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7B,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,eAAe,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACzE,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAExC,4BAA4B;QAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAElC,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,OAAO,GAAG,iDAAiD,QAAQ,gBAAgB,CAAC;QACtF,CAAC;aAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,GAAG,WAAW,QAAQ,GAAG,CAAC;QACnC,CAAC;aAAM,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YAChC,OAAO,GAAG,UAAU,QAAQ,gBAAgB,QAAQ,gBAAgB,QAAQ,GAAG,CAAC;QAClF,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE;gBAC3B,qBAAqB;gBACrB,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;gBAC9B,IAAI,KAAK,EAAE,CAAC;oBACV,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEM,aAAa,CAAC,OAAgB;QACnC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACpD,CAAC;IAEM,YAAY;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC;IACrD,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,SAAsB;QAC9C,4CAA4C;QAC5C,MAAM,gBAAgB,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC;QAC1D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,8CAA8C;QAC9C,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAS,cAAc,CAAC,CAAC;QACtF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,kCAAkC;YAClC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAS,cAAc,CAAC,IAAI,EAAE,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;QAClG,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,+CAA2B,EAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAC3E,0DAA0D;YAC1D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,kBAAkB;QACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC7B,CAAC;CACF;AAzcD,oCAycC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from '../logger';\nimport { TTSSettings<PERSON>anager, TTS<PERSON>rovider, TTSVoice } from '../ui/settings/ttsSettings';\nimport { transcribeWithOpenAIWhisper } from './whisperTranscribe';\nimport { SystemTTS } from '../ui/settings/systemTTS';\n\nexport class AudioService {\n  private static instance: AudioService;\n  private _mediaRecorder: MediaRecorder | undefined;\n  private _audioChunks: Blob[] = [];\n  private _settingsManager: TTSSettingsManager;\n  private _currentVoice: SpeechSynthesisVoice | null = null;\n  private _isRecording = false;\n  private _lastRecording: Uint8Array | null = null;\n\n  private constructor(private readonly context: vscode.ExtensionContext) {\n    this._settingsManager = TTSSettingsManager.getInstance(context);\n    this._initializeSpeechSynthesis();\n  }\n\n  public static getInstance(context: vscode.ExtensionContext): AudioService {\n    if (!AudioService.instance) {\n      AudioService.instance = new AudioService(context);\n    }\n    return AudioService.instance;\n  }\n\n  private _initializeSpeechSynthesis() {\n    if ('speechSynthesis' in window) {\n      // Load voices when they become available\n      if (speechSynthesis.getVoices().length > 0) {\n        this._loadVoices();\n      }\n      speechSynthesis.onvoiceschanged = () => this._loadVoices();\n    }\n  }\n\n  private _loadVoices() {\n    const voices = speechSynthesis.getVoices();\n    const edgeVoices = voices.filter(voice => voice.name.includes('Microsoft'));\n    this._settingsManager.updateVoices(TTSProvider.EDGE, edgeVoices.map(voice => ({\n      id: voice.voiceURI,\n      name: voice.name,\n      language: voice.lang,\n      gender: voice.name.includes('Female') ? 'Female' : 'Male',\n      provider: TTSProvider.EDGE\n    })));\n  }\n\n  public async startRecording(): Promise<void> {\n    if (this._isRecording) {\n      throw new Error('Recording is already in progress');\n    }\n\n    try {\n      // Request microphone access with enhanced options\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n          sampleRate: 44100,\n          channelCount: 1\n        }\n      });\n\n      // Use high-quality recording options\n      const options = {\n        mimeType: 'audio/webm;codecs=opus',\n        audioBitsPerSecond: 128000\n      };\n\n      // Fallback for browsers that don't support webm\n      if (!MediaRecorder.isTypeSupported(options.mimeType)) {\n        options.mimeType = 'audio/wav';\n      }\n\n      this._mediaRecorder = new MediaRecorder(stream, options);\n      this._audioChunks = [];\n\n      this._mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          this._audioChunks.push(event.data);\n        }\n      };\n\n      this._mediaRecorder.onstop = async () => {\n        try {\n          const audioBlob = new Blob(this._audioChunks, { type: this._mediaRecorder?.mimeType || 'audio/wav' });\n\n          // Convert to array buffer for processing\n          const arrayBuffer = await audioBlob.arrayBuffer();\n          const uint8Array = new Uint8Array(arrayBuffer);\n\n          // Store for later transcription\n          this._lastRecording = uint8Array;\n\n          Logger.instance.info(`Recording completed: ${uint8Array.length} bytes`);\n        } catch (error) {\n          Logger.instance.error('Error processing recorded audio:', error);\n        } finally {\n          // Stop all tracks\n          stream.getTracks().forEach(track => track.stop());\n        }\n      };\n\n      this._mediaRecorder.onerror = (event) => {\n        Logger.instance.error('MediaRecorder error:', event);\n        this._isRecording = false;\n      };\n\n      // Start recording with time slicing for better performance\n      this._mediaRecorder.start(1000); // 1 second chunks\n      this._isRecording = true;\n\n      Logger.instance.info('Recording started successfully');\n    } catch (error) {\n      Logger.instance.error('Error starting recording:', error);\n      this._isRecording = false;\n      throw error;\n    }\n  }\n\n  public stopRecording(): Promise<string | null> {\n    return new Promise((resolve) => {\n      if (this._mediaRecorder && this._isRecording) {\n        this._mediaRecorder.onstop = async () => {\n          const audioBlob = new Blob(this._audioChunks, { type: 'audio/wav' });\n          const reader = new FileReader();\n          reader.onloadend = () => {\n            const base64data = reader.result as string;\n            resolve(base64data);\n          };\n          reader.readAsDataURL(audioBlob);\n        };\n        this._mediaRecorder.stop();\n        this._isRecording = false;\n      } else {\n        resolve(null);\n      }\n    });\n  }\n\n  public isRecording(): boolean {\n    return this._isRecording;\n  }\n\n  public async speak(text: string): Promise<void> {\n    const settings = this._settingsManager.getSettings();\n    if (!settings.enabled) {\n      return;\n    }\n\n    try {\n      switch (settings.provider) {\n      case TTSProvider.SYSTEM:\n        await this._speakWithSystem(text, settings);\n        break;\n      case TTSProvider.EDGE:\n        await this._speakWithEdge(text, settings);\n        break;\n      case TTSProvider.MICROSOFT:\n        await this._speakWithMicrosoft(text, settings);\n        break;\n      case TTSProvider.GOOGLE:\n        await this._speakWithGoogle(text, settings);\n        break;\n      case TTSProvider.OPENAI:\n        await this._speakWithOpenAI(text, settings);\n        break;\n      case TTSProvider.ELEVENLABS:\n        await this._speakWithElevenLabs(text, settings);\n        break;\n      default:\n        await this._speakWithSystem(text, settings);\n      }\n    } catch (error) {\n      Logger.instance.error('Error speaking text:', error);\n      throw error;\n    }\n  }\n\n  private async _speakWithMicrosoft(text: string, settings: any): Promise<void> {\n    // Use SystemTTS for Windows\n    await SystemTTS.speak(text, settings.voice, settings.rate, settings.pitch, settings.volume);\n  }\n\n  private async _speakWithEdge(text: string, settings: any): Promise<void> {\n    // Use browser speechSynthesis if available, else fallback to SystemTTS\n    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {\n      const utterance = new SpeechSynthesisUtterance(text);\n      const voices = speechSynthesis.getVoices();\n      const selectedVoice = voices.find(v => v.voiceURI === settings.voice || v.name === settings.voice);\n      if (selectedVoice) utterance.voice = selectedVoice;\n      utterance.rate = settings.rate;\n      utterance.pitch = settings.pitch;\n      utterance.volume = settings.volume;\n      return new Promise((resolve, reject) => {\n        utterance.onend = () => resolve();\n        utterance.onerror = (error) => reject(error);\n        speechSynthesis.speak(utterance);\n      });\n    } else {\n      await SystemTTS.speak(text, settings.voice, settings.rate, settings.pitch, settings.volume);\n    }\n  }\n\n  private async _speakWithSystem(text: string, settings: any): Promise<void> {\n    // Use system TTS (default)\n    await SystemTTS.speak(text, settings.voice, settings.rate, settings.pitch, settings.volume);\n  }\n\n  private async _speakWithGoogle(text: string, settings: any): Promise<void> {\n    // Google Cloud TTS API implementation\n    if (!settings.apiKeys?.google) {\n      throw new Error('Google TTS API key not configured');\n    }\n\n    try {\n      const https = require('https');\n      const postData = JSON.stringify({\n        input: { text },\n        voice: {\n          languageCode: 'en-US',\n          name: settings.voice || 'en-US-Standard-A'\n        },\n        audioConfig: {\n          audioEncoding: 'MP3',\n          speakingRate: settings.rate || 1.0,\n          pitch: settings.pitch || 0.0,\n          volumeGainDb: (settings.volume - 1) * 20 || 0.0\n        }\n      });\n\n      const options = {\n        hostname: 'texttospeech.googleapis.com',\n        path: '/v1/text:synthesize',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${settings.apiKeys.google}`,\n          'Content-Type': 'application/json',\n          'Content-Length': Buffer.byteLength(postData)\n        }\n      };\n\n      return new Promise((resolve, reject) => {\n        const req = https.request(options, (res: any) => {\n          let data = '';\n          res.on('data', (chunk: any) => data += chunk);\n          res.on('end', () => {\n            try {\n              const response = JSON.parse(data);\n              if (response.audioContent) {\n                // Play the audio content\n                this._playAudioContent(response.audioContent);\n                resolve();\n              } else {\n                reject(new Error('No audio content received'));\n              }\n            } catch (error) {\n              reject(error);\n            }\n          });\n        });\n        req.on('error', reject);\n        req.write(postData);\n        req.end();\n      });\n    } catch (error) {\n      Logger.instance.error('Google TTS error:', error);\n      throw error;\n    }\n  }\n\n  private async _speakWithOpenAI(text: string, settings: any): Promise<void> {\n    // OpenAI TTS API implementation\n    if (!settings.apiKeys?.openai) {\n      throw new Error('OpenAI TTS API key not configured');\n    }\n\n    try {\n      const https = require('https');\n      const postData = JSON.stringify({\n        model: 'tts-1',\n        input: text,\n        voice: settings.voice || 'alloy',\n        response_format: 'mp3',\n        speed: settings.rate || 1.0\n      });\n\n      const options = {\n        hostname: 'api.openai.com',\n        path: '/v1/audio/speech',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${settings.apiKeys.openai}`,\n          'Content-Type': 'application/json',\n          'Content-Length': Buffer.byteLength(postData)\n        }\n      };\n\n      return new Promise((resolve, reject) => {\n        const req = https.request(options, (res: any) => {\n          const chunks: any[] = [];\n          res.on('data', (chunk: any) => chunks.push(chunk));\n          res.on('end', () => {\n            try {\n              const audioBuffer = Buffer.concat(chunks);\n              this._playAudioBuffer(audioBuffer);\n              resolve();\n            } catch (error) {\n              reject(error);\n            }\n          });\n        });\n        req.on('error', reject);\n        req.write(postData);\n        req.end();\n      });\n    } catch (error) {\n      Logger.instance.error('OpenAI TTS error:', error);\n      throw error;\n    }\n  }\n\n  private async _speakWithElevenLabs(text: string, settings: any): Promise<void> {\n    // ElevenLabs TTS API implementation\n    if (!settings.apiKeys?.elevenlabs) {\n      throw new Error('ElevenLabs TTS API key not configured');\n    }\n\n    try {\n      const https = require('https');\n      const voiceId = settings.voice || 'rachel';\n      const postData = JSON.stringify({\n        text,\n        model_id: 'eleven_monolingual_v1',\n        voice_settings: {\n          stability: 0.5,\n          similarity_boost: 0.5,\n          style: 0.0,\n          use_speaker_boost: true\n        }\n      });\n\n      const options = {\n        hostname: 'api.elevenlabs.io',\n        path: `/v1/text-to-speech/${voiceId}`,\n        method: 'POST',\n        headers: {\n          'xi-api-key': settings.apiKeys.elevenlabs,\n          'Content-Type': 'application/json',\n          'Content-Length': Buffer.byteLength(postData)\n        }\n      };\n\n      return new Promise((resolve, reject) => {\n        const req = https.request(options, (res: any) => {\n          const chunks: any[] = [];\n          res.on('data', (chunk: any) => chunks.push(chunk));\n          res.on('end', () => {\n            try {\n              const audioBuffer = Buffer.concat(chunks);\n              this._playAudioBuffer(audioBuffer);\n              resolve();\n            } catch (error) {\n              reject(error);\n            }\n          });\n        });\n        req.on('error', reject);\n        req.write(postData);\n        req.end();\n      });\n    } catch (error) {\n      Logger.instance.error('ElevenLabs TTS error:', error);\n      throw error;\n    }\n  }\n\n  private _playAudioContent(base64Audio: string): void {\n    // Convert base64 to buffer and play\n    const audioBuffer = Buffer.from(base64Audio, 'base64');\n    this._playAudioBuffer(audioBuffer);\n  }\n\n  private _playAudioBuffer(audioBuffer: Buffer): void {\n    // Use system audio player to play the buffer\n    const fs = require('fs');\n    const path = require('path');\n    const os = require('os');\n\n    const tempFile = path.join(os.tmpdir(), `codessa_tts_${Date.now()}.mp3`);\n    fs.writeFileSync(tempFile, audioBuffer);\n\n    // Play using system command\n    const { exec } = require('child_process');\n    const platform = process.platform;\n\n    let command = '';\n    if (platform === 'win32') {\n      command = `powershell -c \"(New-Object Media.SoundPlayer '${tempFile}').PlaySync()\"`;\n    } else if (platform === 'darwin') {\n      command = `afplay \"${tempFile}\"`;\n    } else if (platform === 'linux') {\n      command = `aplay \"${tempFile}\" || paplay \"${tempFile}\" || mpg123 \"${tempFile}\"`;\n    }\n\n    if (command) {\n      exec(command, (error: any) => {\n        // Clean up temp file\n        fs.unlink(tempFile, () => {});\n        if (error) {\n          Logger.instance.error('Error playing audio:', error);\n        }\n      });\n    }\n  }\n\n  public setTTSEnabled(enabled: boolean): void {\n    this._settingsManager.updateSettings({ enabled });\n  }\n\n  public isTTSEnabled(): boolean {\n    return this._settingsManager.getSettings().enabled;\n  }\n\n  public async speechToText(audioData?: Uint8Array): Promise<string> {\n    // Use provided audio data or last recording\n    const dataToTranscribe = audioData || this._lastRecording;\n    if (!dataToTranscribe) {\n      throw new Error('No audio data available for transcription');\n    }\n\n    // Get OpenAI API key from settings or context\n    let apiKey = vscode.workspace.getConfiguration('codessa').get<string>('openAIApiKey');\n    if (!apiKey) {\n      // Try to get from context storage\n      apiKey = this.context.globalState.get<string>('openAIApiKey') || '';\n    }\n\n    if (!apiKey) {\n      throw new Error('OpenAI API key is not set. Please set codessa.openAIApiKey in your settings.');\n    }\n\n    try {\n      const result = await transcribeWithOpenAIWhisper(dataToTranscribe, apiKey);\n      // Clear the last recording after successful transcription\n      this._lastRecording = null;\n      return result;\n    } catch (err: any) {\n      Logger.instance.error('Whisper transcription failed:', err);\n      throw new Error('Speech-to-text failed: ' + (err?.message || err));\n    }\n  }\n\n  public getLastRecording(): Uint8Array | null {\n    return this._lastRecording;\n  }\n\n  public clearLastRecording(): void {\n    this._lastRecording = null;\n  }\n}"]}