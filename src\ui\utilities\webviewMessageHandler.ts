import * as vscode from 'vscode';
import { logger } from '../../logger';

type WebviewLike = vscode.WebviewPanel | vscode.WebviewView | { webview: vscode.Webview };

export class WebviewMessageHandler {
  private context: vscode.ExtensionContext;

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
  }

  private getWebview(webview: WebviewLike): vscode.Webview {
    if ('webview' in webview) {
      return webview.webview;
    }
    return (webview as any).webview || webview;
  }

  public async handleMessage(message: any, webview: WebviewLike): Promise<Record<string, any> | boolean | null> {
    try {
      switch (message.command) {
        case 'showConfirmDialog':
          return this.handleConfirmDialog(message);

        case 'clearStorage':
          return this.handleClearStorage();

        case 'showInformationMessage':
          vscode.window.showInformationMessage(message.message);
          return true;

        case 'showErrorMessage':
          vscode.window.showErrorMessage(message.message);
          return true;

        case 'reloadWindow':
          vscode.commands.executeCommand('workbench.action.reloadWindow');
          return true;

        default:
          logger.warn(`Unknown command: ${message.command}`);
          return null;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Error handling message: ${errorMessage}`);
      return { error: errorMessage };
    }
  }

  private async handleConfirmDialog(message: any): Promise<{ confirmed: boolean }> {
    const result = await vscode.window.showInformationMessage(
      message.message,
      { modal: true },
      'Yes', 'No'
    );
    return { confirmed: result === 'Yes' };
  }

  private async handleClearStorage(): Promise<{ success: boolean; error?: string }> {
    try {
      // Clear global state
      await this.context.globalState.update('codessa', undefined);

      // Clear workspace state
      await this.context.workspaceState.update('codessa', undefined);

      // Clear all secrets
      const secrets = await this.context.secrets.get('codessa.secrets');
      if (secrets) {
        const secretsObj = JSON.parse(secrets);
        for (const key in secretsObj) {
          await this.context.secrets.delete(`codessa.secrets.${key}`);
        }
      }

      return { success: true };
    } catch (error) {
      logger.error('Failed to clear storage:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
