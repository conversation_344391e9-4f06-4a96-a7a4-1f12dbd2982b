import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import axios from 'axios';
import { WebContentExtractTool, WebSnapshotTool } from './advancedWebTools';
import { z } from 'zod';

export class WebReadTool implements ITool {
  readonly id = 'webRead';
  readonly name = 'Web Read (Advanced)';
  readonly description = 'Reads and extracts content, snapshot, and more from web pages.';
  readonly type = 'multi-action'; // Required by ITool
  readonly actions: Record<string, any> = {
    'read': {
      id: 'read',
      name: 'Read Web Page',
      description: 'Read the content of a web page (HTML/text).',
      type: 'single-action', // Required by ITool
      actions: {}, // Required by ITool
      singleActionSchema: z.object({
        url: z.string().describe('The URL to read.')
      }),
      inputSchema: {
        type: 'object',
        properties: {
          url: { type: 'string', description: 'The URL to read.' }
        },
        required: ['url']
      },
      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
        const url = input.url as string;
        if (!url) {
          return { success: false, error: '\'url\' is required.', toolId: 'read', actionName };
        }
        try {
          const response = await axios({ method: 'get', url });
          const content = response.data;
          return {
            success: true,
            output: typeof content === 'string' ? content : JSON.stringify(content),
            toolId: 'read',
            actionName
          };
        } catch (error: any) {
          return {
            success: false,
            error: `Failed to read URL: ${error.message || error}`,
            toolId: 'read',
            actionName
          };
        }
      }
    },
    'extract': {
      ...new WebContentExtractTool(),
      type: 'single-action',
      actions: {},
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const url = input.url as string;
        if (!url) {
          return { success: false, error: '\'url\' is required.', toolId: 'webExtract', actionName };
        }
        // Placeholder: In real implementation, use a library like mercury-parser or readability
        return { success: true, output: `Extracted content from: ${url}`, toolId: 'webExtract', actionName };
      }
    },
    'snapshot': {
      ...new WebSnapshotTool(),
      type: 'single-action',
      actions: {},
      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
        const url = input.url as string;
        if (!url) {
          return { success: false, error: '\'url\' is required.', toolId: 'webSnapshot', actionName };
        }
        // Placeholder: Actual screenshot would require headless browser integration
        return { success: true, output: `Snapshot data for: ${url}`, toolId: 'webSnapshot', actionName };
      }
    },
  };

  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    // If an action is specified, delegate to the appropriate action tool
    if (actionName && this.actions[actionName]) {
      const actionTool = this.actions[actionName];
      // Check if the action tool has the new execute method signature
      if (actionTool.execute.length >= 2) {
        // Pass the actionName parameter to the nested tool
        return actionTool.execute(actionName, input, context);
      } else {
        // Fallback for older tools that don't have the new signature
        const result = await (actionTool as any).execute(input, context);
        // Add the actionName to the result if it's not already there
        if (result && !result.actionName) {
          result.actionName = actionName;
        }
        return result;
      }
    }

    // Default behavior (backward compatibility) - treat as a web read
    const url = input.url as string;
    if (!url) {
      return { success: false, error: '\'url\' is required.', toolId: this.id, actionName };
    }
    try {
      const response = await axios({ method: 'get', url });
      const content = response.data;
      return {
        success: true,
        output: typeof content === 'string' ? content : JSON.stringify(content),
        toolId: this.id,
        actionName
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Failed to read URL: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }
}

export const webReadTool = new WebReadTool();
