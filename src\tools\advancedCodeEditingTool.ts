import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { randomUUID } from 'crypto';
import { EventEmitter } from 'events';
// Define the missing interfaces locally since they're not exported from toolFramework
declare module 'tool-framework' {
  export interface ITerminalToolEvents {
    on(event: string, listener: (...args: any[]) => void): this;
    emit(event: string, ...args: any[]): boolean;
  }

  export interface IToolMemorySchema {
    type: string;
    source: string;
    metadata: Record<string, unknown>;
  }
}

// Import the interfaces we just declared
import type { ITerminalToolEvents, IToolMemorySchema } from 'tool-framework';
import { performance } from 'perf_hooks';
import { z } from 'zod';

// Core framework imports
import {
  IFileSystemManager,
  DiffFormat,
  DiffOptions,
  FileEditOptions,
  ILogger,
  AITerminalTool,
  InteractiveSession,
  IAIContext,
  IToolTerminalContext,
  QuickFixPattern,
  IWorkspaceKnowledge,
  DirectoryTree
} from './toolFramework';

// Import memory types from the correct location
import type { IMemoryOperations, MemorySource, MemoryType } from '../memory/types';

// Define missing interfaces locally since they're not exported from toolFramework
interface IToolOperationMemory {
  id: string;
  toolName: string;
  operation: string;
  timestamp: number;
  input: Record<string, unknown>;
  output?: unknown;
  error?: {
    message: string;
    code?: string;
    stack?: string;
  };
  metadata: {
    source: MemorySource;
    type: string;
    tags?: string[];
    workspaceContext?: string;
    affectedFiles?: string[];
    success: boolean;
    importance?: number;
    [key: string]: unknown;
  };
  duration: number;
  executionId: string;
  state: {
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    startTime: number;
    endTime?: number;
    progress?: number;
  };
  dependencies?: {
    requiredOperations?: string[];
    dependentOperations?: string[];
  };
  metrics?: {
    cpuUsage?: number;
    memoryUsage?: number;
    apiCalls?: number;
    fileOperations?: number;
  };
}

// Workflow engine integration
import { Workflow, WorkflowContext, WorkflowStep, WorkflowStepResult } from '../agents/workflows/workflowEngine';
import { logger } from '../logger';

// Type definitions for AI operations
type AIAnalysisResult = {
  suggestions: Array<{
    type: 'optimization' | 'refactor' | 'bug' | 'security';
    description: string;
    codeSnippet: string;
    recommendedChange: string;
    confidence: number;
  }>;
  metrics: {
    complexity: number;
    maintainability: number;
    performance: number;
    security: number;
  };
};

type RefactoringResult = {
  originalCode: string;
  refactoredCode: string;
  changes: Array<{
    type: 'add' | 'remove' | 'modify';
    line: number;
    oldContent?: string;
    newContent?: string;
    reason: string;
  }>;
  summary: string;
};

type CodeEditOperation = {
  type: 'replace' | 'insert' | 'delete';
  filePath: string;
  range: [number, number]; // [startLine, endLine] (1-based)
  content?: string;
  options?: FileEditOptions;
};
/**
 * Input types for code editing actions using framework interfaces
 */
interface ReplaceLinesInput extends Partial<FileEditOptions> {
  filePath: string;
  startLine: number;
  endLine: number;
  newContent: string;
  createBackup: boolean;
  createIfNotExists?: boolean;
  encoding?: BufferEncoding;
  dryRun?: boolean;
}

interface InsertLinesInput extends Partial<FileEditOptions> {
  filePath: string;
  line: number;
  newContent: string;
  createBackup: boolean;
  createIfNotExists?: boolean;
  encoding?: BufferEncoding;
  dryRun?: boolean;
}

interface DeleteLinesInput extends Partial<FileEditOptions> {
  filePath: string;
  startLine: number;
  endLine: number;
  createBackup: boolean;
  dryRun?: boolean;
}

interface GenerateDiffInput extends Partial<DiffOptions> {
  filePath: string;
  comparedTo?: string;
  format: DiffFormat;
  contextLines?: number;
  ignoreWhitespace?: boolean;
  ignoreCase?: boolean;
  caseSensitive?: boolean;
}

interface ValidateFileInput {
  filePath: string;
  checkSyntax?: boolean;
}

/**
 * Advanced code editing tool with AI integration and workflow support
 * Extends AITerminalTool for terminal integration and AI capabilities
 */
// Define the operation history type for better type safety
type OperationHistoryEntry = {
  operation: string;
  filePath: string;
  timestamp: number;
  success: boolean;
  error?: string;
  metadata?: Record<string, unknown>;
  duration?: number;
  changes?: Array<{
    type: 'add' | 'remove' | 'modify';
    line: number;
    content: string;
  }>;
};

// Define active operation type for better type safety
type ActiveOperation = {
  id: string;
  operation: string;
  filePath: string;
  startTime: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  error?: string;
  changes?: Array<{
    type: 'add' | 'remove' | 'modify';
    line: number;
    content: string;
  }>;
};

export class AdvancedCodeEditingTool extends AITerminalTool {
  // Public readonly properties
  public readonly name = 'AdvancedCodeEditing';
  public readonly description = 'Advanced file editing tool for precise code modifications with automatic backups, integrity checks, and full framework capabilities';
  public readonly version = '1.0.0';
  public readonly category = 'Development';

  // Private properties
  private toolEvents: EventEmitter;
  private memorySchema: IToolOperationMemory;
  private activeOperations = new Map<string, ActiveOperation>();
  private operationHistory: OperationHistoryEntry[] = [];

  // Service dependencies
  private logger: ILogger;
  private fileSystem: IFileSystemManager;
  private workspaceKnowledge: IWorkspaceKnowledge | null;
  private memoryOperations: IMemoryOperations;
  private aiContext: IAIContext;

  constructor(
    terminalSession: InteractiveSession,
    aiContext: IAIContext,
    toolContext: IToolTerminalContext,
    services: {
      memoryManager: IMemoryOperations;
      fileSystem: IFileSystemManager;
      workspace?: IWorkspaceKnowledge;
      logger?: ILogger;
    }
  ) {
    super({ terminalSession, aiContext, toolContext });

    // Initialize service dependencies
    this.logger = services.logger || console as unknown as ILogger;
    this.fileSystem = services.fileSystem;
    this.workspaceKnowledge = services.workspace || null;
    this.memoryOperations = services.memoryManager;
    this.aiContext = aiContext;

    // Initialize event emitter and memory schema
    this.toolEvents = new EventEmitter();
    this.memorySchema = this.createMemorySchema();

    // Initialize file system watchers and AI patterns
    this.initializeFileSystemWatchers();
    this.initializeAIPatterns();
  }

  // Implement required abstract method from AITerminalTool
  public async executeAIOperation(context: IAIContext, input: unknown): Promise<unknown> {
    try {
      this.logger.info('Executing AI operation', { input });
      // Implement actual AI operation logic here
      return { success: true, result: 'Operation completed' };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Error in executeAIOperation', { error: errorMessage });
      throw error;
    }
  }

  /**
   * Analyze a code snippet and provide optimization suggestions
   * @param codeSnippet The code to analyze
   * @param language The programming language of the code
   */
  private async analyzeCodeSnippet(
    codeSnippet: string,
    language: string = 'typescript',
    context?: WorkflowContext
  ): Promise<{
    originalCode: string;
    suggestedCode: string;
    description: string;
    complexity: number;
    improvements: string[];
    memoryId?: string;
    workflowId?: string;
  }> {
    const startTime = performance.now();

    try {
      // Use the AI context to analyze the code
      const analysis = await this.aiContext.analyzeCode({
        code: codeSnippet,
        language,
        features: ['complexity', 'optimization', 'security']
      });

      const duration = performance.now() - startTime;
      const metadata = {
        duration,
        language,
        complexity: analysis.complexity || 0,
        suggestions: analysis.suggestions?.length || 0
      };

      // Log the successful analysis
      await this.logOperation('analyzeCodeSnippet', 'snippet', true, undefined);

      // Store detailed analysis in memory
      await this.memoryManager.storeMemory({
        id: `analysis:${Date.now()}`,
        content: JSON.stringify({
          codeSnippet,
          analysis,
          metadata
        }),
        metadata: {
          type: 'code_analysis',
          source: 'ai_analysis',
          timestamp: Date.now(),
          ...metadata
        }
      });

      return {
        originalCode: codeSnippet,
        suggestedCode: analysis.suggestedCode || codeSnippet,
        description: analysis.summary || 'No specific issues found',
        complexity: analysis.complexity || 0,
        improvements: analysis.suggestions?.map(s => s.description) || []
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await this.logOperation('analyzeCodeSnippet', 'snippet', false, errorMessage);

      // Return a basic analysis result in case of errors
      return {
        originalCode: codeSnippet,
        suggestedCode: codeSnippet,
        description: 'Error during analysis: ' + errorMessage,
        complexity: 0,
        improvements: []
      };
    }
  }

  private activeOperations: Map<string, {
    id: string;
    operation: string;
    filePath: string;
    startTime: number;
    changes: Array<{
      type: 'add' | 'remove' | 'modify';
      line: number;
      content: string;
    }>;
  }> = new Map();

  private terminalEvents: ITerminalToolEvents;
  private memorySchema: IToolMemorySchema;
  private toolEvents: EventEmitter;

  constructor(
    terminalSession: InteractiveSession,
    aiContext: IAIContext,
    toolContext: IToolTerminalContext,
    services: {
      memoryManager: IMemoryOperations;
      fileSystem: IFileSystemManager;
      workspace: IWorkspaceKnowledge;
      logger?: ILogger;
    }
  ) {
    super({ terminalSession, aiContext, toolContext });

    this.logger = services.logger || console as unknown as ILogger;
    this.fileSystem = services.fileSystem;
    this.workspaceKnowledge = services.workspace;
    this.memoryOperations = services.memoryManager;
    this.toolEvents = new EventEmitter();

    // Initialize memory schema
    this.memorySchema = this.createMemorySchema();

    // Initialize file system watchers
    this.initializeFileSystemWatchers();

    // Initialize AI patterns
    this.initializeAIPatterns();

    // Initialize memory schema
    this.memorySchema = {
      type: MemoryType.CodeEdit,
      source: MemorySource.Tool,
      metadata: {
        toolName: this.name,
        version: this.version,
        categories: ['code', 'refactoring', 'optimization']
      }
    };

    // Initialize AI patterns and capabilities
    this.initializeAIPatterns();
    this.initializeFileSystemWatchers();

    // Set up terminal event listeners
    this.setupTerminalEventListeners();

    this.logger.info('AdvancedCodeEditingTool initialized with full framework capabilities');
  }

  /**
   * Initialize file system watchers for relevant file changes
   */
  private initializeFileSystemWatchers(): void {
    // Watch for file changes to update workspace knowledge
    const watcher = vscode.workspace.createFileSystemWatcher('**/*.{ts,js,jsx,tsx,json}');

    watcher.onDidChange(async (uri) => {
      this.logger.debug(`File changed: ${uri.fsPath}`);
      await this.updateWorkspaceKnowledge(uri.fsPath);
    });

    watcher.onDidCreate(async (uri) => {
      this.logger.debug(`File created: ${uri.fsPath}`);
      await this.updateWorkspaceKnowledge(uri.fsPath);
    });

    watcher.onDidDelete((uri) => {
      this.logger.debug(`File deleted: ${uri.fsPath}`);
      this.removeFromWorkspaceKnowledge(uri.fsPath);
    });
  }

  /**
   * Update workspace knowledge with file changes
   */
  private async updateWorkspaceKnowledge(filePath: string): Promise<void> {
    try {
      const content = await this.fileSystem.readFile(filePath);
      // Store file content in memory for quick access
      await this.memoryManager.storeMemory({
        id: `file:${filePath}`,
        content,
        metadata: {
          type: 'file_content',
          path: filePath,
          lastModified: Date.now(),
          source: 'file_system_watcher'
        }
      });
      this.logger.debug(`Updated workspace knowledge for: ${filePath}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to update workspace knowledge for ${filePath}: ${errorMessage}`, { error: errorMessage });
    }
  }

  /**
   * Remove file from workspace knowledge
   */
  private async removeFromWorkspaceKnowledge(filePath: string): Promise<void> {
    try {
      await this.memoryManager.deleteMemory(`file:${filePath}`);
      this.logger.debug(`Removed from workspace knowledge: ${filePath}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to remove ${filePath} from workspace knowledge: ${errorMessage}`, { error: errorMessage });
    }
  }

  /**
   * Initialize AI patterns for code editing
   */
  private initializeAIPatterns(): void {
    try {
      if (!this.workspaceKnowledge) {
        this.logger.warn('Workspace knowledge not available, skipping AI patterns initialization');
        return;
      }

      // Register quick fix patterns for common issues
      const quickFixPatterns: QuickFixPattern[] = [
        {
          id: 'missing-import',
          pattern: /Cannot find name '([^']+)'/,
          title: 'Add missing import',
          description: 'Add missing import statement for $1',
          action: async (match: RegExpMatchArray) => {
            // Implementation for adding missing imports
            return { success: true };
          }
        },
        {
          id: 'unused-variable',
          pattern: /'([^']+)' is declared but its value is never read/,
          title: 'Remove unused variable',
          description: 'Remove unused variable $1',
          action: async (match: RegExpMatchArray) => {
            // Implementation for removing unused variables
            return { success: true };
          }
        }
      ];

      if (typeof this.workspaceKnowledge.registerPatterns === 'function') {
        this.workspaceKnowledge.registerPatterns(quickFixPatterns);
        this.logger.info('Registered AI patterns for code editing');
      }
    } catch (error) {
      this.logger.error('Failed to initialize AI patterns', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Log an operation to history and memory
   */
  private async logOperation(operation: string, filePath: string, success: boolean, error?: string): Promise<void> {
    try {
      const logEntry = {
        operation,
        filePath,
        timestamp: Date.now(),
        success,
        error
      };

      // Here you would typically use the memory manager to store the log
      // For now, we'll just log to the console
      if (success) {
        this.logger.info(`Operation completed: ${operation}`, { filePath });
      } else {
        this.logger.error(`Operation failed: ${operation}`, { filePath, error });
      }

      // If you have a memory manager with a store method, you could do:
      // await this.memoryManager.store('operation-logs', logEntry);

    } catch (error) {
      this.logger.error('Failed to log operation', {
        operation,
        filePath,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

      // Ensure metadata has the correct types and required fields
      const { metadata: existingMetadata = {} } = updatedMemory;
const metadata: IToolMemorySchema['metadata'] = {
  source: 'conversation',
  type: 'text',
  success: true,
  ...existingMetadata,
  // Ensure these required fields are not overridden
  toolName: this.name,
  operation: updatedMemory.operation,
  executionId: updatedMemory.executionId || `exec_${Date.now()}`
};

// Use the memory manager to update the memory
await this.memoryManager.addMemory({
  content: JSON.stringify(updatedMemory.input),
  metadata
});
    }
  }

  protected async _execute(actionName: string, validatedInput: unknown): Promise < { output: unknown, memoriesCreated?: string[] } > {
  const memory = this.createOperationMemory(
    actionName,
    validatedInput as Record<string, unknown>
  );

  try {
    let result: unknown;

    switch(actionName) {
        case 'replaceLines':
    result = await this.executeReplaceLines(validatedInput as ReplaceLinesInput);
    break;
    case 'insertLines':
    result = await this.executeInsertLines(validatedInput as InsertLinesInput);
    break;
    case 'deleteLines':
    result = await this.executeDeleteLines(validatedInput as DeleteLinesInput);
    break;
    default:
          throw new Error(`Unsupported action: ${actionName}`);
  }

      // Update memory with successful result
      await this.updateOperationMemory(memory, {
    output: result,
    state: {
      ...memory.state,
      status: 'completed',
      endTime: Date.now(),
      progress: 100
    },
    duration: Date.now() - memory.state.startTime
  });

  return { output: result };

} catch (error) {
  // Update memory with error result for framework tracking
  const errorMsg = error instanceof Error ? error.message : String(error);
  await this.updateOperationMemory(memory, {
    state: {
      ...memory.state,
      status: 'failed',
      endTime: Date.now()
    },
    error: {
      message: errorMsg,
      stack: error instanceof Error ? error.stack : undefined
    },
    duration: Date.now() - memory.state.startTime
  });

  throw error;
}
  }

  /**
   * Execute a code formatting operation
   */
  private async executeFormatCode(input: { filePath: string; options?: any }): Promise < { success: boolean } > {
  try {
    this.logger.info(`Formatting code in file: ${input.filePath}`);
    // Format the code using the file system directly since workspace.formatDocument is not available
    const content = await this.fileSystem.readFile(input.filePath);
    // Basic formatting - trim whitespace and ensure consistent line endings
    const formattedContent = content
      .split('\n')
      .map(line => line.trimEnd())
      .join('\n')
      .replace(/\r\n/g, '\n');

    await this.fileSystem.writeFile(input.filePath, formattedContent);
    return { success: true };
  } catch(error) {
    this.logger.error(`Failed to format code: ${error}`);
    throw error;
  }
}

  /**
   * Apply a code action to a file
   */
  private async executeApplyCodeAction(input: { filePath: string; action: string; range?: any }): Promise < { success: boolean } > {
  try {
    this.logger.info(`Applying code action to file: ${input.filePath}, action: ${input.action}`);

    // Read the current file content
    const content = await this.fileSystem.readFile(input.filePath);
    let newContent = content;

    // Handle common code actions
    switch(input.action.toLowerCase()) {
        case 'trimwhitespace':
  newContent = content.split('\n').map(line => line.trimEnd()).join('\n');
  break;
        case 'removetrailingnewline':
  newContent = content.replace(/\n+$/, '');
  break;
        case 'ensurenonewline':
  newContent = content.trimEnd() + '\n';
  break;
  // Add more code actions as needed
}

// Only write if content changed
if (newContent !== content) {
  await this.fileSystem.writeFile(input.filePath, newContent);
}

return { success: true };
    } catch (error) {
  this.logger.error(`Failed to apply code action: ${error}`);
  throw error;
}
  }

  /**
   * Apply a diff to a file
   */
  private async executeApplyDiff(input: { filePath: string; diff: string; options?: any }): Promise < { success: boolean } > {
  try {
    this.logger.info(`Applying diff to file: ${input.filePath}`);
    // Apply the diff using the file system's diff application if available
    if(this.fileSystem && typeof this.fileSystem.applyDiff === 'function') {
  await this.fileSystem.applyDiff(input.filePath, input.diff, input.options);
} else {
  // Fallback implementation
  const content = await this.fileSystem.readFile(input.filePath);
  const newContent = this.applyPatch(content, input.diff);
  await this.fileSystem.writeFile(input.filePath, newContent);
}
return { success: true };
    } catch (error) {
  this.logger.error(`Failed to apply diff: ${error}`);
  throw error;
}
  }

  /**
   * Helper method to apply a unified diff/patch to content
   */
  private applyPatch(originalContent: string, patch: string): string {
  // This is a simplified implementation
  // In a real-world scenario, you'd want to use a proper diff/patch library
  const lines = originalContent.split('\n');
  const patchLines = patch.split('\n');
  const result: string[] = [];
  let i = 0;

  while (i < patchLines.length) {
    const line = patchLines[i];
    if (line.startsWith('@@')) {
      // Parse the hunk header
      const hunkMatch = line.match(/^@@ -\d+(?:,\d+)? \+\d+(?:,\d+)? @@/);
      if (hunkMatch) {
        i++;
        // Skip the hunk header and process the hunk
        while (i < patchLines.length && !patchLines[i].startsWith('@@')) {
          const hunkLine = patchLines[i];
          if (hunkLine.startsWith('+') && !hunkLine.startsWith('+++')) {
            result.push(hunkLine.substring(1));
          } else if (!hunkLine.startsWith('-') && !hunkLine.startsWith('---')) {
            result.push(hunkLine);
          }
          i++;
        }
      }
    } else {
      i++;
    }
  }

  return result.join('\n');
}

/**
 * Verify a code change using checksum
 */
private async executeVerifyCodeChange(input: { filePath: string; expectedChecksum: string }): Promise < { success: boolean; matches: boolean } > {
  try {
    if(!this.fileSystem) throw new Error('File system not available');

    const content = await this.fileSystem.readFile(input.filePath);
    const checksum = await this.calculateChecksum(content);
    const matches = checksum === input.expectedChecksum;
    return { success: true, matches };
  } catch(error) {
    this.logger.error(`Failed to verify code change: ${error}`);
    return { success: false, matches: false };
  }
}

  /**
   * Calculate a secure SHA-256 checksum for the given content
   * @param content The content to generate checksum for
   * @returns A hexadecimal string representing the SHA-256 hash
   */
  private async calculateChecksum(content: string): Promise < string > {
  const crypto = await import('crypto');
  return crypto
    .createHash('sha256')
    .update(content, 'utf8')
    .digest('hex');
}

  /**
   * Execute a replace lines operation
   */
  private async executeReplaceLines(input: ReplaceLinesInput): Promise < { success: boolean; message: string; memoryId?: string } > {
  try {
    if(!this.fileSystem) throw new Error('File system not available');

    const { filePath, startLine, endLine, newContent, createBackup } = input;

    // Read original content
    const originalContent = await this.fileSystem.readFile(filePath);
    const lines = originalContent ? originalContent.split('\n') : [];

    // Validate line numbers
    if(startLine < 1 || endLine > lines.length || startLine > endLine) {
  throw new Error(`Invalid line range: ${startLine}-${endLine}`);
}

// Create backup if requested
if (createBackup) {
  const backupPath = `${filePath}.bak`;
  await this.fileSystem.writeFile(backupPath, originalContent);
}

// Replace the lines
const deletedLines = lines.splice(startLine - 1, endLine - startLine + 1, ...newContent.split('\n'));

// Write the modified content back to the file
await this.fileSystem.writeFile(filePath, lines.join('\n'));

// Create operation memory with proper typing and correct memory source/type
const memory = this.createOperationMemory('replaceLines', {
  filePath,
  startLine,
  endLine,
  linesReplaced: deletedLines.length,
  newLineCount: newContent.split('\n').length
}, {
  source: 'conversation',
  type: 'text',
  success: true,
  affectedFiles: [filePath]
});

// Update memory with success status and proper state
await this.updateOperationMemory(memory, {
  state: {
    status: 'completed',
    startTime: Date.now(),
    endTime: Date.now()
  }
});

return {
  success: true,
  message: `Successfully replaced lines ${startLine}-${endLine}`,
  memoryId: memory.id
};
  } catch (error) {
  this.logger.error(`Error in executeReplaceLines: ${error}`);
  throw error;
}
}

/**
 * Execute an insert lines operation
 */
private async executeInsertLines(input: InsertLinesInput): Promise < { success: boolean; message: string; memoryId?: string } > {
  try {
    if(!this.fileSystem) throw new Error('File system not available');

    const { filePath, line, newContent, createBackup } = input;

    // Read original content
    const originalContent = await this.fileSystem.readFile(filePath);
    const lines = originalContent ? originalContent.split('\n') : [];

    // Validate line number
    if(line < 0 || line > lines.length) {
  throw new Error(`Invalid line number: ${line}`);
}

// Create backup if requested
if (createBackup) {
  const backupPath = `${filePath}.bak`;
  await this.fileSystem.writeFile(backupPath, originalContent);
}

// Insert the new content at the specified line
const insertPosition = line === 0 ? 0 : line;
const newLines = newContent.split('\n');
lines.splice(insertPosition, 0, ...newLines);

// Write the modified content back to the file
await this.fileSystem.writeFile(filePath, lines.join('\n'));

// Create operation memory with proper typing and correct memory source/type
const memory = this.createOperationMemory('insertLines', {
  filePath,
  line,
  linesInserted: newLines.length,
  contentPreview: newContent.length > 50 ? newContent.substring(0, 50) + '...' : newContent
}, {
  source: 'conversation',
  type: 'text',
  success: true,
  affectedFiles: [filePath]
});

// Update memory with success status and proper state
await this.updateOperationMemory(memory, {
  state: {
    status: 'completed',
    startTime: Date.now(),
    endTime: Date.now()
  }
});

return {
  success: true,
  message: `Successfully inserted ${newLines.length} lines at position ${line}`,
  memoryId: memory.id
};
  } catch (error) {
  this.logger.error(`Failed to insert lines: ${error}`);
  throw error;
}
}

/**
 * Execute a delete lines operation
 */
private async executeDeleteLines(input: DeleteLinesInput): Promise < { success: boolean; message: string; memoryId?: string } > {
  try {
    if(!this.fileSystem) throw new Error('File system not available');

    const { filePath, startLine, endLine, createBackup } = input;

    // Read original content
    const originalContent = await this.fileSystem.readFile(filePath);
    const lines = originalContent ? originalContent.split('\n') : [];

    // Validate line numbers
    if(startLine < 1 || endLine > lines.length || startLine > endLine) {
  throw new Error(`Invalid line range: ${startLine}-${endLine}`);
}

// Create backup if requested
if (createBackup) {
  const backupPath = `${filePath}.bak`;
  await this.fileSystem.writeFile(backupPath, originalContent);
}

// Remove the specified lines and capture deleted content
const deletedLines = lines.splice(startLine - 1, endLine - startLine + 1);
const deletedContent = deletedLines.join('\n');

// Write the modified content back to the file
await this.fileSystem.writeFile(filePath, lines.join('\n'));

// Create operation memory with proper typing
const memory = this.createOperationMemory('deleteLines', {
  filePath,
  startLine,
  endLine,
  linesDeleted: endLine - startLine + 1,
  deletedContentPreview: deletedContent.length > 100
    ? deletedContent.substring(0, 100) + '...'
    : deletedContent
}, {
  source: 'conversation',
  type: 'text',
  success: true,
  affectedFiles: [filePath]
});

// Update memory with success status and proper state
await this.updateOperationMemory(memory, {
  state: {
    status: 'completed',
    startTime: Date.now(),
    endTime: Date.now()
  }
});

return {
  success: true,
  message: `Successfully deleted ${endLine - startLine + 1} lines (${startLine}-${endLine})`,
  memoryId: memory.id
};
  } catch (error) {
  this.logger.error(`Failed to delete lines: ${error}`);
  throw error;
}
}

/**
 * Generate a diff between two versions of a file or between two files
 */
private async executeGenerateDiff(input: GenerateDiffInput): Promise < {
  diff: string;
  format: DiffFormat;
  summary: { additions: number; deletions: number; changes: number };
} > {
  try {
    if(!this.fileSystem) throw new Error('File system not available');

    const {
      filePath,
      comparedTo,
      format = DiffFormat.Unified,
      contextLines = 3,
      ignoreWhitespace = false,
      ignoreCase = false,
      caseSensitive = true
    } = input;

    // Get the current file content
    const currentContent = await this.fileSystem.readFile(filePath);

    // Get the content to compare against
    let comparedContent = '';
    if(comparedTo) {
      // If a comparison file is provided, read it
      comparedContent = await this.fileSystem.readFile(comparedTo);
    }

    // Generate diff using the file system's diff utility
    const diff = await this.fileSystem.diffContent(
      comparedContent,
      currentContent,
      {
        format,
        contextLines,
        ignoreWhitespace,
        ignoreCase,
        fileHeader: true,
        hunkHeaders: true,
        lineNumbers: true
      }
    );

    // For JSON format, parse the diff to get the change counts
    let summary = { additions: 0, deletions: 0, changes: 0 };

    if(format === DiffFormat.Json) {
  try {
    const diffData = JSON.parse(diff);
    if (diffData.changes && Array.isArray(diffData.changes)) {
      summary = {
        additions: diffData.changes.filter((c: any) => c.type === 'add').length,
        deletions: diffData.changes.filter((c: any) => c.type === 'remove').length,
        changes: diffData.changes.length
      };
    }
  } catch (e) {
    const error = e as Error;
    this.logger.warn('Failed to parse JSON diff for summary', { error: error.message });
    // Fallback to analyzing the diff text for other formats
    summary = this.analyzeDiff(diff);
  }
} else {
  // For non-JSON formats, analyze the diff text
  summary = this.analyzeDiff(diff);
}

return {
  diff,
  format,
  summary
};
  } catch (error) {
  this.logger.error(`Failed to generate diff: ${error}`);
  throw error;
}
}

/**
 * Generate a JSON diff between two strings
 */
private generateJsonDiff(newContent: string, oldContent: string): Array < {
  type: 'add' | 'remove' | 'change';
  oldLine?: string;
  newLine?: string;
  lineNumber: number;
} > {
  const newLines = newContent.split('\n');
  const oldLines = oldContent.split('\n');
  const changes: Array<{
    type: 'add' | 'remove' | 'change';
    oldLine ?: string;
    newLine ?: string;
    lineNumber: number;
  }> =[];

for (let i = 0; i < Math.max(newLines.length, oldLines.length); i++) {
  const newLine = newLines[i];
  const oldLine = oldLines[i];

  if (newLine !== oldLine) {
    if (newLine === undefined) {
      changes.push({ type: 'remove', oldLine, lineNumber: i + 1 });
    } else if (oldLine === undefined) {
      changes.push({ type: 'add', newLine, lineNumber: i + 1 });
    } else {
      changes.push({
        type: 'change',
        oldLine,
        newLine,
        lineNumber: i + 1
      });
    }
  }
}

return changes;
}

/**
 * Analyze a unified diff and return statistics
 */
private analyzeDiff(diff: string): { additions: number; deletions: number; changes: number } {
  const lines = diff.split('\n');
  let additions = 0;
  let deletions = 0;

  for (const line of lines) {
    if (line.startsWith('+') && !line.startsWith('+++')) {
      additions++;
    } else if (line.startsWith('-') && !line.startsWith('---')) {
      deletions++;
    }
  }

  return {
    additions,
    deletions,
    changes: additions + deletions
  };
}

/**
 * Validate file structure and content
 */
private async executeValidateFile(input: ValidateFileInput): Promise < {
  isValid: boolean;
  lineCount: number;
  size: number;
  checksum: string;
  issues?: string[];
} > {
  try {
    if(!this.fileSystem) {
  throw new Error('File system service not available');
}

const { filePath, checkSyntax = false } = input;
let content: string;

// Check if file exists by attempting to read it
try {
  content = await this.fileSystem.readFile(filePath);
} catch (error: any) {
  if (error.code === 'ENOENT') {
    throw new Error(`File not found: ${filePath}`);
  } else {
    throw new Error(`File cannot be read: ${filePath}`);
  }
}

// Basic validation
const lines = content.split('\n');
const lineCount = lines.length;
const size = Buffer.byteLength(content, 'utf8');
const issues: string[] = [];

// Generate checksum
const crypto = await import('crypto');
const checksum = crypto.createHash('sha256').update(content).digest('hex');

// Check if file is empty
if (size === 0) {
  issues.push('File is empty');
}

// Check line endings (CRLF vs LF)
if (content.includes('\r\n')) {
  issues.push('File contains Windows-style line endings (CRLF)');
}

// Check for trailing whitespace
lines.forEach((line, index) => {
  if (line.endsWith(' ') || line.endsWith('\t')) {
    issues.push(`Trailing whitespace found on line ${index + 1}`);
  }
});

// Check for mixed indentation
const hasTabs = content.includes('\t');
const hasSpaces = content.match(/^ +/m) !== null;
if (hasTabs && hasSpaces) {
  issues.push('File contains mixed indentation (tabs and spaces)');
}

// Check for BOM
if (content.length > 0 && content.charCodeAt(0) === 0xFEFF) {
  issues.push('File contains a BOM (Byte Order Mark)');
}

// Note: Removed workspace-specific syntax checking since it's not in the interface
// If syntax checking is needed, it should be implemented in a different way

return {
  isValid: issues.length === 0,
  lineCount,
  size,
  checksum,
  issues: issues.length > 0 ? issues : undefined
};
  } catch (error) {
  this.logger.error(`Error validating file ${input.filePath}: ${error}`);
  throw error;
}
}
}
