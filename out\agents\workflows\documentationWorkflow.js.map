{"version": 3, "file": "documentationWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/documentationWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;AAaH,kEA4FC;AAKD,wEAuFC;AAjMD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,2BAA2B,CACzC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,eAAsB,EACtB,cAAqB,EACrB,aAAoB,EACpB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,oCAAoC,IAAI,EAAE,CAAC,CAAC;IAExD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACnG,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,eAAe,CAAC,CAAC;IACnH,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,eAAe,CAAC,CAAC;IACnH,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACnG,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,cAAc,CAAC,CAAC;IAC5G,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;IAC3G,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;IAC5F,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACxF,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEzF,iBAAiB;QACjB,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,EAAE;QAC9G,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,EAAE;KAC7G,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,sCAAsC;QACtC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,mBAAmB,KAAK,EAAE;gBAChC,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,sCAAsC;YACtC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,cAAc;gBACjC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,eAAe;gBACvB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,qBAAqB;YACrB,gBAAgB;YAChB,mBAAmB;YACnB,mBAAmB;YACnB,cAAc;YACd,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,eAAgC;QAC/C,IAAI,EAAE,CAAC,eAAe,EAAE,mBAAmB,EAAE,WAAW,CAAC;KAC1D,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,8BAA8B,CAC5C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,eAAsB,EACtB,cAAqB,EACrB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,wCAAwC,IAAI,EAAE,CAAC,CAAC;IAE5D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAChG,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,eAAe,CAAC,CAAC;IAC/H,MAAM,0BAA0B,GAAG,eAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,eAAe,CAAC,CAAC;IAClI,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,eAAe,CAAC,CAAC;IAC/H,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAClH,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,8BAA8B,EAAE,eAAe,CAAC,CAAC;IAC5H,MAAM,2BAA2B,GAAG,eAAO,CAAC,eAAe,CAAC,2BAA2B,EAAE,2BAA2B,EAAE,eAAe,CAAC,CAAC;IACvI,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QACvF,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,yBAAyB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvH,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvH,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjH,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1G,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,2BAA2B,EAAE,IAAI,EAAE,SAAS,EAAE;QACpH,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,2BAA2B,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1G,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,sCAAsC;QACtC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,mBAAmB,KAAK,EAAE;gBAChC,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,uCAAuC;YACvC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,WAAW;gBAC9B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,oBAAoB;gBAC5B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,eAAe;YACf,yBAAyB;YACzB,0BAA0B;YAC1B,yBAAyB;YACzB,qBAAqB;YACrB,oBAAoB;YACpB,2BAA2B;YAC3B,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,eAAgC;QAC/C,IAAI,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,WAAW,CAAC;KAC5C,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa Documentation Workflow\n *\n * This module provides workflow templates for documentation tasks:\n * - Code documentation\n * - API documentation\n * - User guides\n * - Technical specifications\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n\n/**\n * Create a Documentation workflow for generating comprehensive documentation\n */\nexport function createDocumentationWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  techWriterAgent: Agent,\n  developerAgent: Agent,\n  reviewerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Documentation workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const codeAnalysisNode = Codessa.createAgentNode('code-analysis', 'Code Analysis', developerAgent);\n  const structurePlanningNode = Codessa.createAgentNode('structure-planning', 'Structure Planning', techWriterAgent);\n  const contentGenerationNode = Codessa.createAgentNode('content-generation', 'Content Generation', techWriterAgent);\n  const codeExamplesNode = Codessa.createAgentNode('code-examples', 'Code Examples', developerAgent);\n  const technicalReviewNode = Codessa.createAgentNode('technical-review', 'Technical Review', developerAgent);\n  const editorialReviewNode = Codessa.createAgentNode('editorial-review', 'Editorial Review', reviewerAgent);\n  const formattingNode = Codessa.createAgentNode('formatting', 'Formatting', techWriterAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'default' },\n    { name: 'analysis-to-structure', source: 'code-analysis', target: 'structure-planning', type: 'default' },\n    { name: 'structure-to-content', source: 'structure-planning', target: 'content-generation', type: 'default' },\n    { name: 'content-to-examples', source: 'content-generation', target: 'code-examples', type: 'default' },\n    { name: 'examples-to-tech-review', source: 'code-examples', target: 'technical-review', type: 'default' },\n    { name: 'tech-review-to-editorial', source: 'technical-review', target: 'editorial-review', type: 'default' },\n    { name: 'editorial-to-formatting', source: 'editorial-review', target: 'formatting', type: 'default' },\n    { name: 'formatting-to-output', source: 'formatting', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'tech-review-to-content', source: 'technical-review', target: 'content-generation', type: 'feedback' },\n    { name: 'editorial-to-content', source: 'editorial-review', target: 'content-generation', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect content generation to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `content-to-tool-${index}`,\n        source: 'content-generation',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to code examples\n      edges.push({\n        name: `tool-${index}-to-examples`,\n        source: `tool-${index}`,\n        target: 'code-examples',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      codeAnalysisNode,\n      structurePlanningNode,\n      contentGenerationNode,\n      codeExamplesNode,\n      technicalReviewNode,\n      editorialReviewNode,\n      formattingNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'documentation' as OperationMode,\n    tags: ['documentation', 'technical-writing', 'code-docs']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create an API Documentation workflow\n */\nexport function createAPIDocumentationWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  techWriterAgent: Agent,\n  apiExpertAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating API Documentation workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const apiAnalysisNode = Codessa.createAgentNode('api-analysis', 'API Analysis', apiExpertAgent);\n  const endpointDocumentationNode = Codessa.createAgentNode('endpoint-documentation', 'Endpoint Documentation', techWriterAgent);\n  const parameterDocumentationNode = Codessa.createAgentNode('parameter-documentation', 'Parameter Documentation', techWriterAgent);\n  const responseDocumentationNode = Codessa.createAgentNode('response-documentation', 'Response Documentation', techWriterAgent);\n  const exampleGenerationNode = Codessa.createAgentNode('example-generation', 'Example Generation', apiExpertAgent);\n  const errorHandlingDocNode = Codessa.createAgentNode('error-handling-doc', 'Error Handling Documentation', techWriterAgent);\n  const apiReferenceCompilationNode = Codessa.createAgentNode('api-reference-compilation', 'API Reference Compilation', techWriterAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-analysis', source: 'input', target: 'api-analysis', type: 'default' },\n    { name: 'analysis-to-endpoint', source: 'api-analysis', target: 'endpoint-documentation', type: 'default' },\n    { name: 'endpoint-to-parameter', source: 'endpoint-documentation', target: 'parameter-documentation', type: 'default' },\n    { name: 'parameter-to-response', source: 'parameter-documentation', target: 'response-documentation', type: 'default' },\n    { name: 'response-to-examples', source: 'response-documentation', target: 'example-generation', type: 'default' },\n    { name: 'examples-to-error', source: 'example-generation', target: 'error-handling-doc', type: 'default' },\n    { name: 'error-to-compilation', source: 'error-handling-doc', target: 'api-reference-compilation', type: 'default' },\n    { name: 'compilation-to-output', source: 'api-reference-compilation', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect example generation to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `example-to-tool-${index}`,\n        source: 'example-generation',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to error handling\n      edges.push({\n        name: `tool-${index}-to-error`,\n        source: `tool-${index}`,\n        target: 'error-handling-doc',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      apiAnalysisNode,\n      endpointDocumentationNode,\n      parameterDocumentationNode,\n      responseDocumentationNode,\n      exampleGenerationNode,\n      errorHandlingDocNode,\n      apiReferenceCompilationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'documentation' as OperationMode,\n    tags: ['api', 'documentation', 'reference']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}