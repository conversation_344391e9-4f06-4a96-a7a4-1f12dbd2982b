fixed some lint errors and warnings
# Please enter the commit message for your changes. Lines starting
# with '#' will be ignored, and an empty message aborts the commit.
#
# On branch main
# Your branch is up to date with 'origin/main'.
#
# Changes to be committed:
#	modified:   .vscodeignore
#	modified:   appmap.log
#	modified:   codessa-1.0.0.vsix
#	modified:   eslint.config.js
#	deleted:    fix-imports.js
#	modified:   fix-providers.js
#	modified:   media/agentConfig.js
#	modified:   media/chat.js
#	modified:   media/dashboard.js
#	new file:   media/embeddedDiff.css
#	new file:   media/fileChanges.css
#	modified:   media/memoryView.js
#	modified:   media/modeSelector.js
#	modified:   media/providerSettings.js
#	modified:   media/settings.js
#	modified:   media/styles/chat.css
#	modified:   media/styles/settings.css
#	modified:   media/toolPicker.js
#	modified:   media/workflow.js
#	modified:   out/agentManager.js.map
#	modified:   out/agents/ReceiverAgent.js
#	modified:   out/agents/ReceiverAgent.js.map
#	modified:   out/agents/agent.js
#	modified:   out/agents/agent.js.map
#	modified:   out/agents/agentManager.js
#	modified:   out/agents/agentManager.js.map
#	modified:   out/agents/agentWorker.js.map
#	modified:   out/agents/defaultPrompts.js.map
#	modified:   out/agents/modes/agentFactory.js
#	modified:   out/agents/modes/agentFactory.js.map
#	modified:   out/agents/modes/chatAgent.js
#	modified:   out/agents/modes/chatAgent.js.map
#	modified:   out/agents/modes/debugAgent.js
#	modified:   out/agents/modes/debugAgent.js.map
#	modified:   out/agents/modes/documentAgent.js
#	modified:   out/agents/modes/documentAgent.js.map
#	modified:   out/agents/modes/editAgent.js
#	modified:   out/agents/modes/editAgent.js.map
#	modified:   out/agents/modes/refactorAgent.js
#	modified:   out/agents/modes/refactorAgent.js.map
#	modified:   out/agents/promptManager.js
#	modified:   out/agents/promptManager.js.map
#	deleted:    out/agents/specializedAgents.js
#	deleted:    out/agents/specializedAgents.js.map
#	modified:   out/agents/supervisorAgent.js
#	modified:   out/agents/supervisorAgent.js.map
#	modified:   out/checkpoint/checkpointManager.js
#	modified:   out/checkpoint/checkpointManager.js.map
#	modified:   out/commands/openModeSelector.js
#	modified:   out/commands/openModeSelector.js.map
#	modified:   out/commands/registerModes.js.map
#	modified:   out/config.js
#	modified:   out/config.js.map
#	modified:   out/config/modelConfig.js
#	modified:   out/config/modelConfig.js.map
#	modified:   out/credentials/credentialsManager.js
#	modified:   out/credentials/credentialsManager.js.map
#	modified:   out/diff/diffEngine.js
#	modified:   out/diff/diffEngine.js.map
#	modified:   out/extension.js
#	modified:   out/extension.js.map
#	modified:   out/goddess/goddessMode.js
#	modified:   out/goddess/goddessMode.js.map
#	modified:   out/intelligence/crossRepositoryContext.js
#	modified:   out/intelligence/crossRepositoryContext.js.map
#	modified:   out/intelligence/projectWideIntelligence.js
#	modified:   out/intelligence/projectWideIntelligence.js.map
#	modified:   out/llm/llmProvider.js
#	modified:   out/llm/llmProvider.js.map
#	modified:   out/llm/llmService.js
#	modified:   out/llm/llmService.js.map
#	modified:   out/llm/providerManager.js
#	modified:   out/llm/providerManager.js.map
#	modified:   out/llm/providerSettings.js
#	modified:   out/llm/providerSettings.js.map
#	modified:   out/llm/providers/ai21Provider.js
#	modified:   out/llm/providers/ai21Provider.js.map
#	modified:   out/llm/providers/alephalphaprovider.js
#	modified:   out/llm/providers/alephalphaprovider.js.map
#	modified:   out/llm/providers/anthropicProvider.js
#	modified:   out/llm/providers/anthropicProvider.js.map
#	modified:   out/llm/providers/baseLLMProvider.js
#	modified:   out/llm/providers/baseLLMProvider.js.map
#	modified:   out/llm/providers/codeGemmaProvider.js
#	modified:   out/llm/providers/codeGemmaProvider.js.map
#	modified:   out/llm/providers/codeLlamaProvider.js
#	modified:   out/llm/providers/codeLlamaProvider.js.map
#	modified:   out/llm/providers/codeParrotProvider.js
#	modified:   out/llm/providers/codeParrotProvider.js.map
#	modified:   out/llm/providers/cohereProvider.js
#	modified:   out/llm/providers/cohereProvider.js.map
#	modified:   out/llm/providers/deepseekProvider.js
#	modified:   out/llm/providers/deepseekProvider.js.map
#	modified:   out/llm/providers/ggufProvider.js
#	modified:   out/llm/providers/ggufProvider.js.map
#	modified:   out/llm/providers/googleAIProvider.js
#	modified:   out/llm/providers/googleAIProvider.js.map
#	modified:   out/llm/providers/huggingfaceProvider.js
#	modified:   out/llm/providers/huggingfaceProvider.js.map
#	modified:   out/llm/providers/lmstudioProvider.js
#	modified:   out/llm/providers/lmstudioProvider.js.map
#	modified:   out/llm/providers/mistralAIProvider.js
#	modified:   out/llm/providers/mistralAIProvider.js.map
#	modified:   out/llm/providers/nousHermesProvider.js
#	modified:   out/llm/providers/nousHermesProvider.js.map
#	modified:   out/llm/providers/ollamaProvider.js
#	modified:   out/llm/providers/ollamaProvider.js.map
#	modified:   out/llm/providers/openaiProvider.js
#	modified:   out/llm/providers/openaiProvider.js.map
#	modified:   out/llm/providers/openrouterProvider.js
#	modified:   out/llm/providers/openrouterProvider.js.map
#	modified:   out/llm/providers/perplexityProvider.js
#	modified:   out/llm/providers/perplexityProvider.js.map
#	modified:   out/llm/providers/phiProvider.js
#	modified:   out/llm/providers/phiProvider.js.map
#	modified:   out/llm/providers/replitProvider.js
#	modified:   out/llm/providers/replitProvider.js.map
#	modified:   out/llm/providers/santaCoderProvider.js
#	modified:   out/llm/providers/santaCoderProvider.js.map
#	modified:   out/llm/providers/stableCodeProvider.js
#	modified:   out/llm/providers/stableCodeProvider.js.map
#	modified:   out/llm/providers/starcoderProvider.js
#	modified:   out/llm/providers/starcoderProvider.js.map
#	modified:   out/llm/providers/togetheraiProvider.js
#	modified:   out/llm/providers/togetheraiProvider.js.map
#	modified:   out/llm/providers/wizardCoderProvider.js
#	modified:   out/llm/providers/wizardCoderProvider.js.map
#	modified:   out/llm/providers/xwinCoderProvider.js
#	modified:   out/llm/providers/xwinCoderProvider.js.map
#	modified:   out/llm/providers/yiCodeProvider.js
#	modified:   out/llm/providers/yiCodeProvider.js.map
#	modified:   out/llm/types.js.map
#	modified:   out/logger.js
#	modified:   out/logger.js.map
#	modified:   out/mcp/mcp2025.js
#	modified:   out/mcp/mcp2025.js.map
#	modified:   out/mcp/mcpManager.js
#	modified:   out/mcp/mcpManager.js.map
#	modified:   out/memory/agentMemory.js
#	modified:   out/memory/agentMemory.js.map
#	modified:   out/memory/codessa/codessaGraphMemory.js
#	modified:   out/memory/codessa/codessaGraphMemory.js.map
#	modified:   out/memory/codessa/codessaMemory.js
#	modified:   out/memory/codessa/codessaMemory.js.map
#	modified:   out/memory/codessa/databases/databaseFactory.js
#	modified:   out/memory/codessa/databases/databaseFactory.js.map
#	modified:   out/memory/codessa/databases/mongodbDatabase.js
#	modified:   out/memory/codessa/databases/mongodbDatabase.js.map
#	modified:   out/memory/codessa/databases/mysqlDatabase.js
#	modified:   out/memory/codessa/databases/mysqlDatabase.js.map
#	modified:   out/memory/codessa/databases/postgresDatabase.js
#	modified:   out/memory/codessa/databases/postgresDatabase.js.map
#	modified:   out/memory/codessa/databases/redisDatabase.js
#	modified:   out/memory/codessa/databases/redisDatabase.js.map
#	modified:   out/memory/codessa/databases/sqliteDatabase.js
#	modified:   out/memory/codessa/databases/sqliteDatabase.js.map
#	modified:   out/memory/codessa/fileChunking.js
#	modified:   out/memory/codessa/fileChunking.js.map
#	modified:   out/memory/codessa/vectorStores/chromaVectorStore.js
#	modified:   out/memory/codessa/vectorStores/chromaVectorStore.js.map
#	modified:   out/memory/codessa/vectorStores/memoryVectorStore.js
#	modified:   out/memory/codessa/vectorStores/memoryVectorStore.js.map
#	modified:   out/memory/codessa/vectorStores/pineconeVectorStore.js
#	modified:   out/memory/codessa/vectorStores/pineconeVectorStore.js.map
#	modified:   out/memory/codessa/vectorStores/vectorStoreFactory.js
#	modified:   out/memory/codessa/vectorStores/vectorStoreFactory.js.map
#	deleted:    out/memory/codessaMemory.js
#	deleted:    out/memory/codessaMemory.js.map
#	deleted:    out/memory/langGraphMemory.js
#	deleted:    out/memory/langGraphMemory.js.map
#	deleted:    out/memory/memory.js
#	deleted:    out/memory/memory.js.map
#	modified:   out/memory/memoryConfig.js.map
#	modified:   out/memory/memoryManager.js
#	modified:   out/memory/memoryManager.js.map
#	modified:   out/memory/quantum/quantumMemorySystem.js
#	modified:   out/memory/quantum/quantumMemorySystem.js.map
#	modified:   out/memory/types.js.map
#	modified:   out/memory/vectorMemory.js
#	modified:   out/memory/vectorMemory.js.map
#	modified:   out/modes/agentMode.js
#	modified:   out/modes/agentMode.js.map
#	modified:   out/modes/askMode.js
#	modified:   out/modes/askMode.js.map
#	modified:   out/modes/chatMode.js
#	modified:   out/modes/chatMode.js.map
#	modified:   out/modes/contextManager.js
#	modified:   out/modes/contextManager.js.map
#	modified:   out/modes/debugMode.js
#	modified:   out/modes/debugMode.js.map
#	modified:   out/modes/documentationMode.js
#	modified:   out/modes/documentationMode.js.map
#	modified:   out/modes/editMode.js
#	modified:   out/modes/editMode.js.map
#	modified:   out/modes/multiAgentMode.js
#	modified:   out/modes/multiAgentMode.js.map
#	modified:   out/modes/operationMode.js
#	modified:   out/modes/operationMode.js.map
#	modified:   out/modes/refactorMode.js
#	modified:   out/modes/refactorMode.js.map
#	modified:   out/modes/researchMode.js
#	modified:   out/modes/researchMode.js.map
#	deleted:    out/package.json
#	modified:   out/personality/goddessMode.js
#	modified:   out/personality/goddessMode.js.map
#	modified:   out/prompts/promptManager.js
#	modified:   out/prompts/promptManager.js.map
#	modified:   out/services/audioService.js
#	modified:   out/services/audioService.js.map
#	modified:   out/services/whisperTranscribe.js
#	modified:   out/services/whisperTranscribe.js.map
#	modified:   out/test/phase1Integration.test.js
#	modified:   out/test/phase1Integration.test.js.map
#	modified:   out/test/phase2Integration.test.js
#	modified:   out/test/phase2Integration.test.js.map
#	modified:   out/tools/advancedCodeEditingTool.js
#	modified:   out/tools/advancedCodeEditingTool.js.map
#	modified:   out/tools/advancedCodeGenerationTool.js.map
#	modified:   out/tools/advancedCodeSearchTool.js
#	modified:   out/tools/advancedCodeSearchTool.js.map
#	modified:   out/tools/advancedDiffTool.js
#	modified:   out/tools/advancedDiffTool.js.map
#	modified:   out/tools/advancedDocsTool.js.map
#	modified:   out/tools/advancedEditorActionsTool.js.map
#	modified:   out/tools/advancedGitTool.js.map
#	modified:   out/tools/advancedLintDiagnosticsTool.js
#	modified:   out/tools/advancedLintDiagnosticsTool.js.map
#	modified:   out/tools/advancedMemoryTool.js
#	modified:   out/tools/advancedMemoryTool.js.map
#	modified:   out/tools/advancedWebTools.js
#	modified:   out/tools/advancedWebTools.js.map
#	modified:   out/tools/browserPreviewTool.js
#	modified:   out/tools/browserPreviewTool.js.map
#	modified:   out/tools/cascadeEditingTool.js
#	modified:   out/tools/cascadeEditingTool.js.map
#	modified:   out/tools/codeAnalysisTool.js
#	modified:   out/tools/codeAnalysisTool.js.map
#	modified:   out/tools/codeGenerationTool.js.map
#	modified:   out/tools/codeIntelligenceTool.js
#	modified:   out/tools/codeIntelligenceTool.js.map
#	modified:   out/tools/codeRefactoringTool.js
#	modified:   out/tools/codeRefactoringTool.js.map
#	modified:   out/tools/codeSearchTool.js
#	modified:   out/tools/codeSearchTool.js.map
#	modified:   out/tools/dependencyAnalysisTool.js
#	modified:   out/tools/dependencyAnalysisTool.js.map
#	modified:   out/tools/deployWebAppTool.js
#	modified:   out/tools/deployWebAppTool.js.map
#	modified:   out/tools/diagnosticsTool.js
#	modified:   out/tools/diagnosticsTool.js.map
#	modified:   out/tools/directoryListTool.js
#	modified:   out/tools/directoryListTool.js.map
#	modified:   out/tools/docsTool.js
#	modified:   out/tools/docsTool.js.map
#	modified:   out/tools/editorActionsTool.js.map
#	modified:   out/tools/enhancedFileTools.js
#	modified:   out/tools/enhancedFileTools.js.map
#	modified:   out/tools/gitTool.js
#	modified:   out/tools/gitTool.js.map
#	modified:   out/tools/largeFileEditTool.js
#	modified:   out/tools/largeFileEditTool.js.map
#	modified:   out/tools/lintDiagnosticsTool.js
#	modified:   out/tools/lintDiagnosticsTool.js.map
#	modified:   out/tools/memoryTool.js
#	modified:   out/tools/memoryTool.js.map
#	modified:   out/tools/memoryTools.js.map
#	modified:   out/tools/neuralCodeSynthesisTool.js
#	modified:   out/tools/neuralCodeSynthesisTool.js.map
#	modified:   out/tools/quantumAnalysisTool.js
#	modified:   out/tools/quantumAnalysisTool.js.map
#	modified:   out/tools/terminalCommandTool.js
#	modified:   out/tools/terminalCommandTool.js.map
#	modified:   out/tools/timeTravelDebuggingTool.js
#	modified:   out/tools/timeTravelDebuggingTool.js.map
#	modified:   out/tools/toolRegistry.js
#	modified:   out/tools/toolRegistry.js.map
#	modified:   out/tools/types.js.map
#	modified:   out/tools/webReadTool.js
#	modified:   out/tools/webReadTool.js.map
#	modified:   out/tools/webSearchTool.js
#	modified:   out/tools/webSearchTool.js.map
#	modified:   out/tools/workflowTools.js
#	modified:   out/tools/workflowTools.js.map
#	modified:   out/types/agent.js.map
#	modified:   out/ui/agentConfigPanel.js
#	modified:   out/ui/agentConfigPanel.js.map
#	modified:   out/ui/agentTree.js
#	modified:   out/ui/agentTree.js.map
#	modified:   out/ui/allSettingsPanel.js
#	modified:   out/ui/allSettingsPanel.js.map
#	modified:   out/ui/chat/revolutionaryChatInterface.js
#	modified:   out/ui/chat/revolutionaryChatInterface.js.map
#	modified:   out/ui/chatView.js
#	modified:   out/ui/chatView.js.map
#	modified:   out/ui/chatViewProvider.js
#	modified:   out/ui/chatViewProvider.js.map
#	modified:   out/ui/codeUnderstanding/aiCodeLensProvider.js
#	modified:   out/ui/codeUnderstanding/aiCodeLensProvider.js.map
#	modified:   out/ui/dashboard.js
#	modified:   out/ui/dashboard.js.map
#	new file:   out/ui/fileChangePanel.js
#	new file:   out/ui/fileChangePanel.js.map
#	new file:   out/ui/fileChangeTracker.js
#	new file:   out/ui/fileChangeTracker.js.map
#	modified:   out/ui/inlineEditing/contextualInlineChat.js
#	modified:   out/ui/inlineEditing/contextualInlineChat.js.map
#	modified:   out/ui/inlineEditing/ghostTextProvider.js
#	modified:   out/ui/inlineEditing/ghostTextProvider.js.map
#	modified:   out/ui/memoryView.js
#	modified:   out/ui/memoryView.js.map
#	modified:   out/ui/modeSelectorView.js
#	modified:   out/ui/modeSelectorView.js.map
#	modified:   out/ui/notifications.js
#	modified:   out/ui/notifications.js.map
#	deleted:    out/ui/phase3Manager.js
#	deleted:    out/ui/phase3Manager.js.map
#	modified:   out/ui/providerSettingsPanel.js
#	modified:   out/ui/providerSettingsPanel.js.map
#	modified:   out/ui/settings/components/modal.js.map
#	modified:   out/ui/settings/handlers/agentsHandler.js.map
#	modified:   out/ui/settings/handlers/databaseHandler.js.map
#	modified:   out/ui/settings/handlers/knowledgebaseHandler.js.map
#	modified:   out/ui/settings/handlers/memorySettingsHandler.js.map
#	modified:   out/ui/settings/handlers/promptsHandler.js.map
#	modified:   out/ui/settings/handlers/providersModelsHandler.js.map
#	modified:   out/ui/settings/handlers/ttsHandler.js.map
#	modified:   out/ui/settings/handlers/workflowsHandler.js.map
#	modified:   out/ui/settings/handlers/workspaceHandler.js.map
#	deleted:    out/ui/settings/sections/advancedSection.js
#	deleted:    out/ui/settings/sections/advancedSection.js.map
#	modified:   out/ui/settings/sections/advancedSettingsSection.js
#	modified:   out/ui/settings/sections/advancedSettingsSection.js.map
#	deleted:    out/ui/settings/sections/agentsSection.js
#	deleted:    out/ui/settings/sections/agentsSection.js.map
#	modified:   out/ui/settings/sections/agentsSettingsSection.js.map
#	deleted:    out/ui/settings/sections/databaseSection.js
#	deleted:    out/ui/settings/sections/databaseSection.js.map
#	modified:   out/ui/settings/sections/databaseSettingsSection.js
#	modified:   out/ui/settings/sections/databaseSettingsSection.js.map
#	deleted:    out/ui/settings/sections/generalSection.js
#	deleted:    out/ui/settings/sections/generalSection.js.map
#	modified:   out/ui/settings/sections/generalSettingsSection.js
#	modified:   out/ui/settings/sections/generalSettingsSection.js.map
#	deleted:    out/ui/settings/sections/knowledgebaseSection.js
#	deleted:    out/ui/settings/sections/knowledgebaseSection.js.map
#	modified:   out/ui/settings/sections/knowledgebaseSettingsSection.js
#	modified:   out/ui/settings/sections/knowledgebaseSettingsSection.js.map
#	modified:   out/ui/settings/sections/mcpSettingsSection.js.map
#	deleted:    out/ui/settings/sections/memorySection.js
#	deleted:    out/ui/settings/sections/memorySection.js.map
#	modified:   out/ui/settings/sections/memorySettingsSection.js.map
#	deleted:    out/ui/settings/sections/promptsSection.js
#	deleted:    out/ui/settings/sections/promptsSection.js.map
#	modified:   out/ui/settings/sections/promptsSettingsSection.js.map
#	deleted:    out/ui/settings/sections/providersModelsSection.js
#	deleted:    out/ui/settings/sections/providersModelsSection.js.map
#	modified:   out/ui/settings/sections/providersModelsSettingsSection.js
#	modified:   out/ui/settings/sections/providersModelsSettingsSection.js.map
#	modified:   out/ui/settings/sections/revolutionaryFeaturesSection.js.map
#	deleted:    out/ui/settings/sections/ttsSection.js
#	deleted:    out/ui/settings/sections/ttsSection.js.map
#	modified:   out/ui/settings/sections/ttsSettingsSection.js
#	modified:   out/ui/settings/sections/ttsSettingsSection.js.map
#	deleted:    out/ui/settings/sections/uiThemeSection.js
#	deleted:    out/ui/settings/sections/uiThemeSection.js.map
#	modified:   out/ui/settings/sections/uiThemeSettingsSection.js
#	modified:   out/ui/settings/sections/uiThemeSettingsSection.js.map
#	deleted:    out/ui/settings/sections/workflowsSection.js
#	deleted:    out/ui/settings/sections/workflowsSection.js.map
#	modified:   out/ui/settings/sections/workflowsSettingsSection.js.map
#	deleted:    out/ui/settings/sections/workspaceSection.js
#	deleted:    out/ui/settings/sections/workspaceSection.js.map
#	modified:   out/ui/settings/sections/workspaceSettingsSection.js
#	modified:   out/ui/settings/sections/workspaceSettingsSection.js.map
#	modified:   out/ui/settings/settingsManager.js
#	modified:   out/ui/settings/settingsManager.js.map
#	modified:   out/ui/settings/systemTTS.js
#	modified:   out/ui/settings/systemTTS.js.map
#	modified:   out/ui/settings/themeConfig.js
#	modified:   out/ui/settings/themeConfig.js.map
#	modified:   out/ui/settings/ttsSettings.js
#	modified:   out/ui/settings/ttsSettings.js.map
#	modified:   out/ui/settings/ttsSettingsView.js.map
#	modified:   out/ui/statusBar.js
#	modified:   out/ui/statusBar.js.map
#	deleted:    out/ui/statusBarManager.js
#	deleted:    out/ui/statusBarManager.js.map
#	modified:   out/ui/toolDetailsPanel.js.map
#	modified:   out/ui/toolsView.js
#	modified:   out/ui/toolsView.js.map
#	modified:   out/ui/types.js.map
#	modified:   out/ui/utils.js.map
#	modified:   out/ui/workflowPanel.js
#	modified:   out/ui/workflowPanel.js.map
#	modified:   out/ui/workflowStorage.js.map
#	modified:   out/utils.js
#	modified:   out/utils.js.map
#	modified:   out/vscode/chatParticipant.js
#	modified:   out/vscode/chatParticipant.js.map
#	deleted:    out/vscode/languageModelProvider.js
#	deleted:    out/vscode/languageModelProvider.js.map
#	modified:   out/workflows/advancedRefactoring.js
#	modified:   out/workflows/advancedRefactoring.js.map
#	modified:   out/workflows/advancedTemplates.js
#	modified:   out/workflows/advancedTemplates.js.map
#	modified:   out/workflows/askModeWorkflow.js.map
#	modified:   out/workflows/checkpointWorkflow.js.map
#	modified:   out/workflows/collaborativeCodingWorkflow.js.map
#	modified:   out/workflows/corePolyfill.js
#	modified:   out/workflows/corePolyfill.js.map
#	modified:   out/workflows/documentQAWorkflow.js.map
#	modified:   out/workflows/documentationWorkflow.js.map
#	modified:   out/workflows/graph.js
#	modified:   out/workflows/graph.js.map
#	modified:   out/workflows/graphTypes.js.map
#	modified:   out/workflows/knowledgeBaseWorkflow.js.map
#	modified:   out/workflows/mcpWorkflow.js.map
#	modified:   out/workflows/memory.js
#	modified:   out/workflows/memory.js.map
#	modified:   out/workflows/memoryEnhancedWorkflow.js.map
#	modified:   out/workflows/methodologyWorkflows.js.map
#	modified:   out/workflows/multiRepoWorkflow.js.map
#	modified:   out/workflows/operationModeWorkflows.js.map
#	modified:   out/workflows/patternRefactoringWorkflow.js.map
#	modified:   out/workflows/prWorkflows.js.map
#	deleted:    out/workflows/registry.js
#	deleted:    out/workflows/registry.js.map
#	modified:   out/workflows/researchWorkflow.js
#	modified:   out/workflows/researchWorkflow.js.map
#	modified:   out/workflows/sdlcWorkflows.js
#	modified:   out/workflows/sdlcWorkflows.js.map
#	modified:   out/workflows/specializedTemplates.js
#	modified:   out/workflows/specializedTemplates.js.map
#	modified:   out/workflows/technicalDebtWorkflow.js.map
#	modified:   out/workflows/templates.js
#	modified:   out/workflows/templates.js.map
#	modified:   out/workflows/types.js.map
#	modified:   out/workflows/uiUxWorkflow.js.map
#	modified:   out/workflows/vectorStores.js.map
#	modified:   out/workflows/workflowEngine.js
#	modified:   out/workflows/workflowEngine.js.map
#	modified:   out/workflows/workflowRegistry.js
#	modified:   out/workflows/workflowRegistry.js.map
#	modified:   package.json
#	new file:   scripts/post-compile.cjs
#	modified:   scripts/testWorkflowStorage.js
#	modified:   src/agentManager.ts
#	modified:   src/agents/ReceiverAgent.ts
#	deleted:    src/agents/agent.js
#	modified:   src/agents/agent.ts
#	modified:   src/agents/agentManager.ts
#	modified:   src/agents/agentWorker.ts
#	deleted:    src/agents/defaultPrompts.js
#	modified:   src/agents/defaultPrompts.ts
#	modified:   src/agents/modes/agentFactory.ts
#	modified:   src/agents/modes/chatAgent.ts
#	modified:   src/agents/modes/debugAgent.ts
#	modified:   src/agents/modes/documentAgent.ts
#	modified:   src/agents/modes/editAgent.ts
#	modified:   src/agents/modes/refactorAgent.ts
#	deleted:    src/agents/promptManager.js
#	modified:   src/agents/promptManager.ts
#	modified:   src/agents/supervisorAgent.ts
#	modified:   src/checkpoint/checkpointManager.ts
#	modified:   src/commands/openModeSelector.ts
#	modified:   src/commands/registerModes.ts
#	deleted:    src/config.js
#	modified:   src/config.ts
#	modified:   src/config/modelConfig.ts
#	deleted:    src/credentials/credentialsManager.js
#	modified:   src/credentials/credentialsManager.ts
#	deleted:    src/diff/diffEngine.js
#	modified:   src/diff/diffEngine.ts
#	modified:   src/extension.ts
#	modified:   src/goddess/goddessMode.ts
#	modified:   src/intelligence/crossRepositoryContext.ts
#	modified:   src/intelligence/projectWideIntelligence.ts
#	deleted:    src/llm/llmProvider.js
#	modified:   src/llm/llmProvider.ts
#	deleted:    src/llm/llmService.js
#	modified:   src/llm/llmService.ts
#	modified:   src/llm/providerManager.ts
#	deleted:    src/llm/providerSettings.js
#	modified:   src/llm/providerSettings.ts
#	modified:   src/llm/providers/ai21Provider.ts
#	modified:   src/llm/providers/alephalphaprovider.ts
#	deleted:    src/llm/providers/anthropicProvider.js
#	modified:   src/llm/providers/anthropicProvider.ts
#	deleted:    src/llm/providers/baseLLMProvider.js
#	modified:   src/llm/providers/baseLLMProvider.ts
#	modified:   src/llm/providers/codeGemmaProvider.ts
#	modified:   src/llm/providers/codeLlamaProvider.ts
#	modified:   src/llm/providers/codeParrotProvider.ts
#	deleted:    src/llm/providers/cohereProvider.js
#	modified:   src/llm/providers/cohereProvider.ts
#	deleted:    src/llm/providers/deepseekProvider.js
#	modified:   src/llm/providers/deepseekProvider.ts
#	modified:   src/llm/providers/ggufProvider.ts
#	deleted:    src/llm/providers/googleAIProvider.js
#	modified:   src/llm/providers/googleAIProvider.ts
#	deleted:    src/llm/providers/huggingfaceProvider.js
#	modified:   src/llm/providers/huggingfaceProvider.ts
#	deleted:    src/llm/providers/lmstudioProvider.js
#	modified:   src/llm/providers/lmstudioProvider.ts
#	deleted:    src/llm/providers/mistralAIProvider.js
#	modified:   src/llm/providers/mistralAIProvider.ts
#	modified:   src/llm/providers/nousHermesProvider.ts
#	deleted:    src/llm/providers/ollamaProvider.js
#	modified:   src/llm/providers/ollamaProvider.ts
#	deleted:    src/llm/providers/openaiProvider.js
#	modified:   src/llm/providers/openaiProvider.ts
#	deleted:    src/llm/providers/openrouterProvider.js
#	modified:   src/llm/providers/openrouterProvider.ts
#	modified:   src/llm/providers/perplexityProvider.ts
#	modified:   src/llm/providers/phiProvider.ts
#	modified:   src/llm/providers/replitProvider.ts
#	modified:   src/llm/providers/santaCoderProvider.ts
#	modified:   src/llm/providers/stableCodeProvider.ts
#	modified:   src/llm/providers/starcoderProvider.ts
#	modified:   src/llm/providers/togetheraiProvider.ts
#	modified:   src/llm/providers/wizardCoderProvider.ts
#	modified:   src/llm/providers/xwinCoderProvider.ts
#	modified:   src/llm/providers/yiCodeProvider.ts
#	modified:   src/llm/types.ts
#	deleted:    src/logger.js
#	modified:   src/logger.ts
#	modified:   src/mcp/mcp2025.ts
#	modified:   src/mcp/mcpManager.ts
#	modified:   src/memory/agentMemory.ts
#	modified:   src/memory/codessa/codessaGraphMemory.ts
#	modified:   src/memory/codessa/codessaMemory.ts
#	modified:   src/memory/codessa/databases/databaseFactory.ts
#	modified:   src/memory/codessa/databases/mongodbDatabase.ts
#	modified:   src/memory/codessa/databases/mysqlDatabase.ts
#	modified:   src/memory/codessa/databases/postgresDatabase.ts
#	modified:   src/memory/codessa/databases/redisDatabase.ts
#	modified:   src/memory/codessa/databases/sqliteDatabase.ts
#	modified:   src/memory/codessa/fileChunking.ts
#	modified:   src/memory/codessa/vectorStores/chromaVectorStore.ts
#	modified:   src/memory/codessa/vectorStores/memoryVectorStore.ts
#	modified:   src/memory/codessa/vectorStores/pineconeVectorStore.ts
#	modified:   src/memory/codessa/vectorStores/vectorStoreFactory.ts
#	modified:   src/memory/memoryConfig.ts
#	modified:   src/memory/memoryManager.ts
#	modified:   src/memory/quantum/quantumMemorySystem.ts
#	modified:   src/memory/types.ts
#	modified:   src/memory/vectorMemory.ts
#	modified:   src/modes/agentMode.ts
#	modified:   src/modes/askMode.ts
#	modified:   src/modes/chatMode.ts
#	modified:   src/modes/contextManager.ts
#	modified:   src/modes/debugMode.ts
#	modified:   src/modes/documentationMode.ts
#	modified:   src/modes/editMode.ts
#	modified:   src/modes/multiAgentMode.ts
#	modified:   src/modes/operationMode.ts
#	modified:   src/modes/refactorMode.ts
#	modified:   src/modes/researchMode.ts
#	modified:   src/personality/goddessMode.ts
#	modified:   src/prompts/promptManager.ts
#	modified:   src/services/audioService.ts
#	modified:   src/services/whisperTranscribe.ts
#	modified:   src/test/phase1Integration.test.ts
#	modified:   src/test/phase2Integration.test.ts
#	modified:   src/tools/advancedCodeEditingTool.ts
#	modified:   src/tools/advancedCodeGenerationTool.ts
#	modified:   src/tools/advancedCodeSearchTool.ts
#	modified:   src/tools/advancedDiffTool.ts
#	modified:   src/tools/advancedDocsTool.ts
#	modified:   src/tools/advancedEditorActionsTool.ts
#	modified:   src/tools/advancedGitTool.ts
#	modified:   src/tools/advancedLintDiagnosticsTool.ts
#	modified:   src/tools/advancedMemoryTool.ts
#	modified:   src/tools/advancedWebTools.ts
#	modified:   src/tools/browserPreviewTool.ts
#	modified:   src/tools/cascadeEditingTool.ts
#	modified:   src/tools/codeAnalysisTool.ts
#	modified:   src/tools/codeGenerationTool.ts
#	modified:   src/tools/codeIntelligenceTool.ts
#	modified:   src/tools/codeRefactoringTool.ts
#	modified:   src/tools/codeSearchTool.ts
#	modified:   src/tools/dependencyAnalysisTool.ts
#	modified:   src/tools/deployWebAppTool.ts
#	deleted:    src/tools/diagnosticsTool.js
#	modified:   src/tools/diagnosticsTool.ts
#	modified:   src/tools/directoryListTool.ts
#	deleted:    src/tools/docsTool.js
#	modified:   src/tools/docsTool.ts
#	modified:   src/tools/editorActionsTool.ts
#	deleted:    src/tools/enhancedFileTools.js
#	modified:   src/tools/enhancedFileTools.ts
#	modified:   src/tools/gitTool.ts
#	modified:   src/tools/largeFileEditTool.ts
#	modified:   src/tools/lintDiagnosticsTool.ts
#	modified:   src/tools/memoryTool.ts
#	modified:   src/tools/memoryTools.ts
#	modified:   src/tools/neuralCodeSynthesisTool.ts
#	modified:   src/tools/quantumAnalysisTool.ts
#	modified:   src/tools/terminalCommandTool.ts
#	modified:   src/tools/timeTravelDebuggingTool.ts
#	deleted:    src/tools/tool.js
#	deleted:    src/tools/toolRegistry.js
#	modified:   src/tools/toolRegistry.ts
#	modified:   src/tools/webReadTool.ts
#	modified:   src/tools/webSearchTool.ts
#	modified:   src/tools/workflowTools.ts
#	modified:   src/types/agent.ts
#	modified:   src/types/anthropic.d.ts
#	modified:   src/types/axios.d.ts
#	modified:   src/types/codessa-modules.d.ts
#	modified:   src/types/codessa.d.ts
#	modified:   src/types/langchain-modules.d.ts
#	modified:   src/types/mongodb.d.ts
#	modified:   src/types/openai.d.ts
#	modified:   src/types/pg.d.ts
#	modified:   src/types/sqlite.d.ts
#	modified:   src/ui/agentConfigPanel.ts
#	modified:   src/ui/agentTree.ts
#	modified:   src/ui/allSettingsPanel.ts
#	modified:   src/ui/chat/revolutionaryChatInterface.ts
#	modified:   src/ui/chatView.ts
#	modified:   src/ui/chatViewProvider.ts
#	modified:   src/ui/codeUnderstanding/aiCodeLensProvider.ts
#	modified:   src/ui/dashboard.ts
#	new file:   src/ui/fileChangePanel.ts
#	new file:   src/ui/fileChangeTracker.ts
#	modified:   src/ui/inlineEditing/contextualInlineChat.ts
#	modified:   src/ui/inlineEditing/ghostTextProvider.ts
#	modified:   src/ui/memoryView.ts
#	modified:   src/ui/modeSelectorView.ts
#	deleted:    src/ui/notifications.js
#	modified:   src/ui/notifications.ts
#	modified:   src/ui/providerSettingsPanel.ts
#	modified:   src/ui/settings/components/modal.ts
#	modified:   src/ui/settings/handlers/agentsHandler.ts
#	modified:   src/ui/settings/handlers/databaseHandler.ts
#	modified:   src/ui/settings/handlers/knowledgebaseHandler.ts
#	modified:   src/ui/settings/handlers/memorySettingsHandler.ts
#	modified:   src/ui/settings/handlers/promptsHandler.ts
#	modified:   src/ui/settings/handlers/providersModelsHandler.ts
#	modified:   src/ui/settings/handlers/ttsHandler.ts
#	modified:   src/ui/settings/handlers/workflowsHandler.ts
#	modified:   src/ui/settings/handlers/workspaceHandler.ts
#	modified:   src/ui/settings/sections/advancedSettingsSection.ts
#	modified:   src/ui/settings/sections/agentsSettingsSection.ts
#	modified:   src/ui/settings/sections/databaseSettingsSection.ts
#	modified:   src/ui/settings/sections/generalSettingsSection.ts
#	modified:   src/ui/settings/sections/knowledgebaseSettingsSection.ts
#	modified:   src/ui/settings/sections/mcpSettingsSection.ts
#	modified:   src/ui/settings/sections/memorySettingsSection.ts
#	modified:   src/ui/settings/sections/promptsSettingsSection.ts
#	modified:   src/ui/settings/sections/providersModelsSettingsSection.ts
#	modified:   src/ui/settings/sections/revolutionaryFeaturesSection.ts
#	modified:   src/ui/settings/sections/ttsSettingsSection.ts
#	modified:   src/ui/settings/sections/uiThemeSettingsSection.ts
#	modified:   src/ui/settings/sections/workflowsSettingsSection.ts
#	modified:   src/ui/settings/sections/workspaceSettingsSection.ts
#	modified:   src/ui/settings/settingsManager.ts
#	modified:   src/ui/settings/systemTTS.ts
#	modified:   src/ui/settings/themeConfig.ts
#	modified:   src/ui/settings/ttsSettings.ts
#	modified:   src/ui/settings/ttsSettingsView.ts
#	modified:   src/ui/statusBar.ts
#	modified:   src/ui/toolDetailsPanel.ts
#	modified:   src/ui/toolsView.ts
#	modified:   src/ui/types.ts
#	modified:   src/ui/utils.ts
#	modified:   src/ui/workflowPanel.ts
#	modified:   src/ui/workflowStorage.ts
#	modified:   src/utils.ts
#	modified:   src/vscode/chatParticipant.ts
#	modified:   src/workflows/advancedRefactoring.ts
#	modified:   src/workflows/advancedTemplates.ts
#	modified:   src/workflows/askModeWorkflow.ts
#	modified:   src/workflows/checkpointWorkflow.ts
#	modified:   src/workflows/collaborativeCodingWorkflow.ts
#	modified:   src/workflows/corePolyfill.ts
#	modified:   src/workflows/documentQAWorkflow.ts
#	modified:   src/workflows/documentationWorkflow.ts
#	modified:   src/workflows/graph.ts
#	modified:   src/workflows/graphTypes.ts
#	modified:   src/workflows/knowledgeBaseWorkflow.ts
#	modified:   src/workflows/mcpWorkflow.ts
#	modified:   src/workflows/memory.ts
#	modified:   src/workflows/memoryEnhancedWorkflow.ts
#	modified:   src/workflows/methodologyWorkflows.ts
#	modified:   src/workflows/multiRepoWorkflow.ts
#	modified:   src/workflows/operationModeWorkflows.ts
#	modified:   src/workflows/patternRefactoringWorkflow.ts
#	modified:   src/workflows/prWorkflows.ts
#	modified:   src/workflows/researchWorkflow.ts
#	modified:   src/workflows/sdlcWorkflows.ts
#	modified:   src/workflows/specializedTemplates.ts
#	modified:   src/workflows/technicalDebtWorkflow.ts
#	modified:   src/workflows/templates.ts
#	modified:   src/workflows/types.ts
#	modified:   src/workflows/uiUxWorkflow.ts
#	modified:   src/workflows/vectorStores.ts
#	modified:   src/workflows/workflowEngine.ts
#	modified:   src/workflows/workflowRegistry.ts
#	deleted:    tsconfig.tsbuildinfo
#
