{"version": 3, "file": "workflowsHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/workflowsHandler.ts"], "names": [], "mappings": ";;AACA,wDAuEC;AAxED,gDAAgD;AAChD,SAAgB,sBAAsB,CAAC,OAAY,EAAE,KAAU;IAC7D,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IACjF,MAAM,QAAQ,GAAwE;QACpF,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,IAAI;KACZ,CAAC;IACF,IAAI,CAAC;QACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC;gBACnC,MAAM;YACR,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAChD,QAAQ,CAAC,KAAK,GAAG,mCAAmC,CAAC;oBACrD,MAAM;gBACR,CAAC;gBACD,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzE,IAAI,MAAM,EAAE,CAAC;oBACX,QAAQ,CAAC,KAAK,GAAG,uCAAuC,CAAC;oBACzD,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACzB,MAAM;YACR,CAAC;YACD,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAC9B,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;oBACzC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC3E,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC;gBACtE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACxC,MAAM;YACR,CAAC;YACD,KAAK,gBAAgB,CAAC,CAAC,CAAC;gBACtB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;oBACzC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAClE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAClC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC3C,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for Workflows CRUD messages and logic\nexport function handleWorkflowsMessage(message: any, panel: any) {\n  const settings = (window as any).settings || {};\n  settings.workflows = Array.isArray(settings.workflows) ? settings.workflows : [];\n  const response: { type: string; success: boolean; data: any; error: string | null } = {\n    type: message.type,\n    success: false,\n    data: null,\n    error: null\n  };\n  try {\n    switch (message.type) {\n      case 'getWorkflows': {\n        response.success = true;\n        response.data = settings.workflows;\n        break;\n      }\n      case 'addWorkflow': {\n        const workflow = message.workflow;\n        if (!workflow || !workflow.id || !workflow.name) {\n          response.error = 'Missing required workflow fields.';\n          break;\n        }\n        const exists = settings.workflows.find((w: any) => w.id === workflow.id);\n        if (exists) {\n          response.error = 'Workflow with this id already exists.';\n          break;\n        }\n        settings.workflows.push(workflow);\n        response.success = true;\n        response.data = workflow;\n        break;\n      }\n      case 'editWorkflow': {\n        const workflow = message.workflow;\n        if (!workflow || !workflow.id) {\n          response.error = 'Workflow id required.';\n          break;\n        }\n        const idx = settings.workflows.findIndex((w: any) => w.id === workflow.id);\n        if (idx === -1) {\n          response.error = 'Workflow not found.';\n          break;\n        }\n        settings.workflows[idx] = { ...settings.workflows[idx], ...workflow };\n        response.success = true;\n        response.data = settings.workflows[idx];\n        break;\n      }\n      case 'deleteWorkflow': {\n        const id = message.id;\n        if (!id) {\n          response.error = 'Workflow id required.';\n          break;\n        }\n        const idx = settings.workflows.findIndex((w: any) => w.id === id);\n        if (idx === -1) {\n          response.error = 'Workflow not found.';\n          break;\n        }\n        settings.workflows.splice(idx, 1);\n        response.success = true;\n        break;\n      }\n      default: {\n        response.error = 'Unknown message type.';\n      }\n    }\n  } catch (err: any) {\n    response.error = err?.message || String(err);\n  }\n  panel.postMessage(response);\n}\n"]}