/**
 * Enhanced Model Context Protocol (MCP) 2025 Implementation
 * 
 * This module implements the latest MCP 2025 specification with enhanced
 * capabilities for tool integration, resource management, and protocol extensions.
 */

import * as vscode from 'vscode';
import { Logger } from '../logger';
import { ToolRegistry } from '../tools/toolRegistry';
import { ITool } from '../tools/tool.ts.backup';
import { IMCPManager } from '../managers';
import { MCPContext } from './mcpManager';

const logger = Logger.instance;

/**
 * MCP 2025 Protocol Version
 */
export const MCP_VERSION = '2025.1';

/**
 * MCP Message Types
 */
export enum MCPMessageType {
    REQUEST = 'request',
    RESPONSE = 'response',
    NOTIFICATION = 'notification',
    ERROR = 'error'
}

/**
 * MCP Resource Types
 */
export enum MCPResourceType {
    FILE = 'file',
    DIRECTORY = 'directory',
    URL = 'url',
    MEMORY = 'memory',
    TOOL = 'tool',
    AGENT = 'agent',
    WORKFLOW = 'workflow'
}

/**
 * Base Message Parameters Interface
 */
export interface MCPBaseParams {
    metadata?: Record<string, unknown>;
}

/**
 * Resource Message Parameters
 */
export interface MCPResourceParams extends MCPBaseParams {
    uri: string;
}

/**
 * Tool Message Parameters
 */
export interface MCPToolParams extends MCPBaseParams {
    name: string;
    arguments?: Record<string, unknown>;
}

/**
 * Agent Message Parameters
 */
export interface MCPAgentParams extends MCPBaseParams {
    agentId: string;
    task: string;
    parameters?: Record<string, unknown>;
}

/**
 * Workflow Message Parameters
 */
export interface MCPWorkflowParams extends MCPBaseParams {
    workflowId: string;
    inputs?: Record<string, unknown>;
}

/**
 * Memory Message Parameters
 */
export interface MCPMemoryParams extends MCPBaseParams {
    query: string;
    limit?: number;
}

/**
 * Chat Message Parameters
 */
export interface MCPChatParams extends MCPBaseParams {
    action: string;
    data: Record<string, unknown>;
}

/**
 * MCP Message Parameters
 */
export type MCPMessageParams = 
    | MCPBaseParams 
    | MCPResourceParams
    | MCPToolParams
    | MCPAgentParams 
    | MCPWorkflowParams
    | MCPMemoryParams
    | MCPChatParams;

export interface MCPMessage {
    id: string;
    type: MCPMessageType;
    method?: string;
    params?: MCPMessageParams;
    result?: Record<string, unknown>;
    error?: MCPError;
    timestamp: number;
    version: string;
}

/**
 * MCP Error Data Type
 */
export type MCPErrorData = 
    | string 
    | number 
    | boolean 
    | null 
    | { [key: string]: MCPErrorData }
    | MCPErrorData[];

/**
 * MCP Error Interface
 */
export interface MCPError {
    code: number;
    message: string;
    data?: MCPErrorData;
}

/**
 * MCP Resource Content Types
 */
export type MCPResourceContent =
    | string
    | Buffer
    | Uint8Array
    | Record<string, unknown>
    | Array<unknown>;

/**
 * MCP Resource Metadata
 */
export interface MCPResourceMetadata {
    creator?: string;
    createdAt?: number;
    modifiedAt?: number;
    tags?: string[];
    version?: string;
    encoding?: string;
    [key: string]: unknown;
}

/**
 * MCP Resource Interface
 */
export interface MCPResource {
    uri: string;
    type: MCPResourceType;
    name: string;
    description?: string;
    metadata?: MCPResourceMetadata;
    content?: MCPResourceContent;
    mimeType?: string;
    size?: number;
    lastModified?: number;
}

/**
 * MCP Schema Type
 */
export interface MCPSchema {
    type: string;
    properties?: Record<string, {
        type: string;
        description?: string;
        required?: boolean;
        items?: MCPSchema;
        enum?: Array<string | number | boolean>;
        [key: string]: unknown;
    }>;
    required?: string[];
    additionalProperties?: boolean;
    [key: string]: unknown;
}

/**
 * MCP Tool Metadata
 */
export interface MCPToolMetadata {
    version?: string;
    author?: string;
    category?: string;
    tags?: string[];
    documentation?: string;
    [key: string]: unknown;
}

/**
 * MCP Tool Interface
 */
export interface MCPTool {
    name: string;
    description: string;
    inputSchema: MCPSchema;
    outputSchema?: MCPSchema;
    metadata?: MCPToolMetadata;
}

/**
 * MCP Server Interface
 */
export interface MCPServer {
    name: string;
    version: string;
    capabilities: MCPCapabilities;
    tools: MCPTool[];
    resources: MCPResource[];
}

/**
 * MCP Capabilities Interface
 */
export interface MCPCapabilities {
    tools?: {
        listChanged?: boolean;
        call?: boolean;
    };
    resources?: {
        subscribe?: boolean;
        listChanged?: boolean;
    };
    prompts?: {
        listChanged?: boolean;
    };
    logging?: {
        level?: string;
    };
    experimental?: {
        agents?: boolean;
        workflows?: boolean;
        memory?: boolean;
        goddess_mode?: boolean;
        [key: string]: boolean | undefined;
    };
}

/**
 * Enhanced MCP 2025 Manager
 */
export class MCPManager implements IMCPManager {
  private static instance: MCPManager;
  private servers: Map<string, MCPServer> = new Map();
  private messageHandlers: Map<string, (message: MCPMessage) => Promise<Record<string, unknown>>> = new Map();
  private resourceSubscriptions: Map<string, Set<string>> = new Map();
  private toolRegistry: ToolRegistry;

  private tools: ITool[] = [];
  private context: MCPContext;

  private constructor() {
    this.toolRegistry = ToolRegistry.instance;
    this.setupMessageHandlers();
    
    // Use vscode import to register configuration change handler
    vscode.workspace.onDidChangeConfiguration(() => {
      this.handleConfigurationChange();
    });

    // Initialize tools using ITool interface
    this.tools = this.toolRegistry.getAllTools();

    // Initialize context using MCPContext type
    this.context = {
      version: MCP_VERSION,
      metadata: {
        source: 'codessa-mcp2025',
        timestamp: new Date().toISOString(),
        sessionId: Date.now().toString()
      },
      content: {}
    };
    
    logger.info('MCP 2025 Manager initialized');
  }

  private handleConfigurationChange(): void {
    const config = vscode.workspace.getConfiguration('codessa.mcp');
    // Update MCP-specific configuration
    const logLevel = config.get<string>('logLevel', 'info');
    logger.info(`MCP configuration updated - Log Level: ${logLevel}`);
    
    // Update active connections timeout and cleanup
    const connectionTimeout = config.get<number>('connectionTimeout', 3600000);
    const now = Date.now();
    for (const [clientId, data] of this.activeConnections.entries()) {
      if (now - data.lastActive > connectionTimeout) {
        this.activeConnections.delete(clientId);
        logger.info(`Cleaned up inactive connection: ${clientId}`);
      }
    }
    
    // Apply configuration to the MCP context with preserved content
    this.context = {
      version: MCP_VERSION,
      metadata: {
        source: 'codessa-mcp2025',
        timestamp: new Date().toISOString(),
        sessionId: Date.now().toString(),
        logLevel: logLevel
      },
      content: this.context.content
    };
  }

  public static getInstance(): MCPManager {
    if (!MCPManager.instance) {
      MCPManager.instance = new MCPManager();
    }
    return MCPManager.instance;
  }

  /**
     * Setup message handlers
     */
  private setupMessageHandlers(): void {
    this.messageHandlers.set('initialize', this.handleInitialize.bind(this));
    this.messageHandlers.set('tools/list', this.handleToolsList.bind(this));
    this.messageHandlers.set('tools/call', this.handleToolCall.bind(this));
    this.messageHandlers.set('resources/list', this.handleResourcesList.bind(this));
    this.messageHandlers.set('resources/read', this.handleResourceRead.bind(this));
    this.messageHandlers.set('resources/subscribe', this.handleResourceSubscribe.bind(this));
    this.messageHandlers.set('resources/unsubscribe', this.handleResourceUnsubscribe.bind(this));
    this.messageHandlers.set('prompts/list', this.handlePromptsList.bind(this));
    this.messageHandlers.set('prompts/get', this.handlePromptGet.bind(this));
    this.messageHandlers.set('logging/setLevel', this.handleLoggingSetLevel.bind(this));
        
    // Enhanced 2025 methods
    this.messageHandlers.set('agents/list', this.handleAgentsList.bind(this));
    this.messageHandlers.set('agents/invoke', this.handleAgentInvoke.bind(this));
    this.messageHandlers.set('workflows/list', this.handleWorkflowsList.bind(this));
    this.messageHandlers.set('workflows/execute', this.handleWorkflowExecute.bind(this));
    this.messageHandlers.set('memory/search', this.handleMemorySearch.bind(this));
    this.messageHandlers.set('memory/store', this.handleMemoryStore.bind(this));

    // VS Code specific handlers for enhanced integration
    this.messageHandlers.set('vscode/workspace', this.handleVSCodeWorkspace.bind(this));
    this.messageHandlers.set('vscode/editor', this.handleVSCodeEditor.bind(this));
    this.messageHandlers.set('vscode/chat', this.handleVSCodeChat.bind(this));
    this.messageHandlers.set('vscode/diagnostics', this.handleVSCodeDiagnostics.bind(this));
  }

  /**
     * Register an MCP server
     */
  public registerServer(server: MCPServer): void {
    this.servers.set(server.name, server);
    logger.info(`MCP server registered: ${server.name} v${server.version}`);
  }

  /**
     * Process an MCP message
     */
  public async processMessage(message: MCPMessage): Promise<MCPMessage> {
    try {
      logger.debug(`Processing MCP message: ${message.method}`);

      if (!message.method) {
        throw new Error('Message method is required');
      }

      // Update client's last active timestamp
      const clientConnection = this.activeConnections.get(message.id);
      if (clientConnection) {
        clientConnection.lastActive = Date.now();
      }

      const handler = this.messageHandlers.get(message.method);
      if (!handler) {
        throw new Error(`Unknown method: ${message.method}`);
      }

      const result = await handler(message);

      // Track successful message processing in context
      this.updateContext({
        lastProcessedMessage: {
          id: message.id,
          method: message.method,
          timestamp: Date.now()
        }
      });

      return {
        id: message.id,
        type: MCPMessageType.RESPONSE,
        result: result,
        timestamp: Date.now(),
        version: MCP_VERSION
      };

    } catch (error) {
      logger.error('Error processing MCP message:', error);
            
      return {
        id: message.id,
        type: MCPMessageType.ERROR,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : String(error)
        },
        timestamp: Date.now(),
        version: MCP_VERSION
      };
    }
  }

  /**
     * Handle initialize request
     */
  private activeConnections = new Map<string, { lastActive: number }>();

  private async handleInitialize(message: MCPMessage): Promise<{
    protocolVersion: string;
    capabilities: MCPCapabilities;
    serverInfo: { name: string; version: string; };
  }> {
    logger.debug(`Initializing MCP connection for client ${message.id}`);
    // Track active connections for session management
    this.activeConnections.set(message.id, { lastActive: Date.now() });
    return {
      protocolVersion: MCP_VERSION,
      capabilities: {
        tools: {
          listChanged: true,
          call: true
        },
        resources: {
          subscribe: true,
          listChanged: true
        },
        prompts: {
          listChanged: true
        },
        logging: {
          level: 'info'
        },
        experimental: {
          agents: true,
          workflows: true,
          memory: true,
          goddess_mode: true
        }
      },
      serverInfo: {
        name: 'Codessa MCP Server',
        version: '1.0.0'
      }
    };
  }

  /**
     * Type guards for parameter types
     */
  private isToolParams(params: MCPMessageParams): params is MCPToolParams {
    return 'name' in params;
  }

  private isResourceParams(params: MCPMessageParams): params is MCPResourceParams {
    return 'uri' in params;
  }

  private isAgentParams(params: MCPMessageParams): params is MCPAgentParams {
    return 'agentId' in params && 'task' in params;
  }

  private isWorkflowParams(params: MCPMessageParams): params is MCPWorkflowParams {
    return 'workflowId' in params;
  }

  private isMemoryParams(params: MCPMessageParams): params is MCPMemoryParams {
    return 'query' in params;
  }

  private isChatParams(params: MCPMessageParams): params is MCPChatParams {
    return 'action' in params && 'data' in params;
  }

  /**
     * Handle tools list request
     */
  private async handleToolsList(message: MCPMessage): Promise<{
    tools: Array<{
      name: string;
      description: string;
      inputSchema: MCPSchema;
    }>;
  }> {
    logger.debug(`Handling tools list request [${message.id}]`);
    const tools = this.toolRegistry.getAllTools();
        
    return {
      tools: tools.map(tool => {
        // Create basic schema that matches the interface
        const mcpSchema: MCPSchema = {
          type: 'object',
          properties: {},
          required: []
        };

        // If the tool has a schema, try to extract its structure
        if (tool.schema && typeof tool.schema === 'object') {
          const rawSchema = tool.schema as { _def?: { shape?: Record<string, unknown> } };
          const properties = rawSchema._def?.shape || {};
          
          // Process each property in the schema
          for (const [key, value] of Object.entries(properties)) {
            const prop = value as { _def?: { typeName?: string; isOptional?: boolean }; description?: string };
            
            if (prop?._def) {
              if (!mcpSchema.properties) {
                mcpSchema.properties = {};
              }
              
              mcpSchema.properties[key] = {
                type: prop._def.typeName || 'unknown',
                description: prop.description
              };
              
              if (!prop._def.isOptional) {
                if (!mcpSchema.required) {
                  mcpSchema.required = [];
                }
                mcpSchema.required.push(key);
              }
            }
          }
        }

        return {
          name: tool.id,
          description: tool.description || `Tool: ${tool.id}`,
          inputSchema: mcpSchema
        };
      })
    };
  }

  /**
     * Handle tool call request
     */
  private async handleToolCall(message: MCPMessage): Promise<{ content: Array<{ type: string; text: string; }> }> {
    if (!message.params || !this.isToolParams(message.params)) {
      throw new Error('Tool call requires name parameter');
    }
        
    const tool = this.toolRegistry.getTool(message.params.name);
    if (!tool) {
      throw new Error(`Tool not found: ${message.params.name}`);
    }

    const result = await tool.execute(undefined, message.params.arguments || {});
        
    return {
      content: [
        {
          type: 'text',
          text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
        }
      ]
    };
  }

  /**
     * Handle resources list request
     */
  private async handleResourcesList(message: MCPMessage): Promise<{ resources: MCPResource[] }> {
    logger.debug(`Handling resources list request [${message.id}]`);
    const resources: MCPResource[] = [
      {
        uri: 'file://workspace',
        type: MCPResourceType.DIRECTORY,
        name: 'Workspace',
        description: 'Current VS Code workspace'
      },
      {
        uri: 'memory://codessa',
        type: MCPResourceType.MEMORY,
        name: 'Codessa Memory',
        description: 'Codessa memory system'
      }
    ];

    return { resources };
  }

  /**
     * Handle resource read request
     */
  private async handleResourceRead(message: MCPMessage): Promise<{
    contents: Array<{ uri: string; mimeType: string; text: string }>
  }> {
    if (!message.params || !this.isResourceParams(message.params)) {
      throw new Error('URI parameter is required');
    }
        
    // Implementation would depend on the resource type
    return {
      contents: [
        {
          uri: message.params.uri,
          mimeType: 'text/plain',
          text: `Content of resource: ${message.params.uri}`
        }
      ]
    };
  }

  /**
     * Handle resource subscribe request
     */
  private async handleResourceSubscribe(message: MCPMessage): Promise<{ success: boolean }> {
    if (!message.params || !this.isResourceParams(message.params)) {
      throw new Error('URI parameter is required');
    }
        
    if (!this.resourceSubscriptions.has(message.params.uri)) {
      this.resourceSubscriptions.set(message.params.uri, new Set());
    }
        
    const subscriptions = this.resourceSubscriptions.get(message.params.uri);
    if (subscriptions) {
      subscriptions.add(message.id);
    }
        
    return { success: true };
  }

  /**
     * Handle resource unsubscribe request
     */
  private async handleResourceUnsubscribe(message: MCPMessage): Promise<{ success: boolean }> {
    if (!message.params || !this.isResourceParams(message.params)) {
      throw new Error('URI parameter is required');
    }
        
    const subscriptions = this.resourceSubscriptions.get(message.params.uri);
    if (subscriptions) {
      subscriptions.delete(message.id);
    }
        
    return { success: true };
  }

  /**
     * Handle prompts list request
     */
  private async handlePromptsList(message: MCPMessage): Promise<{
    prompts: Array<{
      name: string;
      description: string;
      arguments: Array<{
        name: string;
        description: string;
        required: boolean;
      }>;
    }>;
  }> {
    logger.debug(`Handling prompts list request [${message.id}]`);
    return {
      prompts: [
        {
          name: 'code_analysis',
          description: 'Analyze code for issues and improvements',
          arguments: [
            {
              name: 'code',
              description: 'Code to analyze',
              required: true
            }
          ]
        },
        {
          name: 'goddess_mode',
          description: 'Activate Goddess Mode for enhanced AI interaction',
          arguments: [
            {
              name: 'personality',
              description: 'Personality type to activate',
              required: false
            }
          ]
        }
      ]
    };
  }

  /**
     * Handle prompt get request
     */
  private async handlePromptGet(message: MCPMessage): Promise<{ description: string; messages: Array<{ role: string; content: { type: string; text: string } }> }> {
    if (!message.params || !this.isToolParams(message.params)) {
      throw new Error('Name parameter is required');
    }

    return {
      description: `Prompt: ${message.params.name}`,
      messages: [
        {
          role: 'user',
          content: {
            type: 'text',
            text: `Execute ${message.params.name} with arguments: ${JSON.stringify(message.params.arguments || {})}`
          }
        }
      ]
    };
  }

  /**
     * Handle logging set level request
     */
  private async handleLoggingSetLevel(message: MCPMessage): Promise<{ success: boolean }> {
    if (!message.params || !('level' in message.params)) {
      throw new Error('Level parameter is required');
    }
    logger.info(`Setting log level to: ${message.params.level}`);
    return { success: true };
  }

  /**
     * Handle agents list request (Enhanced 2025)
     */
  private async handleAgentsList(message: MCPMessage): Promise<{ agents: Array<{ id: string; name: string; description: string; capabilities: string[] }> }> {
    logger.debug(`Handling agents list request [${message.id}]`);
    // Implementation would list available agents
    return {
      agents: [
        {
          id: 'supervisor',
          name: 'Supervisor Agent',
          description: 'Coordinates multiple agents',
          capabilities: ['delegation', 'coordination', 'workflow_management']
        }
      ]
    };
  }

  /**
     * Handle agent invoke request (Enhanced 2025)
     */
  private async handleAgentInvoke(message: MCPMessage): Promise<{ result: string; status: string }> {
    if (!message.params || !this.isAgentParams(message.params)) {
      throw new Error('AgentId and task parameters are required');
    }
        
    // Implementation would invoke the specified agent
    return {
      result: `Agent ${message.params.agentId} executed task: ${message.params.task}`,
      status: 'completed'
    };
  }

  /**
     * Handle workflows list request (Enhanced 2025)
     */
  private async handleWorkflowsList(message: MCPMessage): Promise<{ 
    workflows: Array<{ id: string; name: string; description: string }> 
  }> {
    logger.debug(`Handling workflows list request [${message.id}]`);
    return {
      workflows: [
        {
          id: 'code_analysis',
          name: 'Code Analysis Workflow',
          description: 'Comprehensive code analysis and improvement suggestions'
        },
        {
          id: 'refactoring',
          name: 'Refactoring Workflow',
          description: 'Automated code refactoring and optimization'
        }
      ]
    };
  }

  /**
     * Handle workflow execute request (Enhanced 2025)
     */
  private async handleWorkflowExecute(message: MCPMessage): Promise<{
    result: string;
    outputs: Record<string, unknown>;
    status: string;
  }> {
    if (!message.params || !this.isWorkflowParams(message.params)) {
      throw new Error('WorkflowId parameter is required');
    }
        
    // Implementation would execute the specified workflow
    return {
      result: `Workflow ${message.params.workflowId} executed successfully`,
      outputs: message.params.inputs || {},
      status: 'completed'
    };
  }

  /**
     * Handle memory search request (Enhanced 2025)
     */
  private async handleMemorySearch(message: MCPMessage): Promise<{
    results: Array<{ id: string; content: string; relevance: number; timestamp: number }>;
    total: number;
  }> {
    if (!message.params || !this.isMemoryParams(message.params)) {
      throw new Error('Query parameter is required');
    }

    const limit = message.params.limit ?? 10;
        
    // Implementation would search memory system
    return {
      results: [
        {
          id: '1',
          content: `Memory result for query: ${message.params.query}`,
          relevance: 0.95,
          timestamp: Date.now()
        }
      ].slice(0, limit),
      total: 1
    };
  }

  /**
     * Handle memory store request (Enhanced 2025)
     */
  private async handleMemoryStore(message: MCPMessage): Promise<{
    id: string;
    stored: boolean;
    timestamp: number;
  }> {
    if (!message.params || !('content' in message.params)) {
      throw new Error('Content parameter is required');
    }

    // Implementation would store content in memory system
    return {
      id: Date.now().toString(),
      stored: true,
      timestamp: Date.now()
    };
  }

  /**
     * Handle VS Code workspace request
     */
  private async handleVSCodeWorkspace(message: MCPMessage): Promise<{
    workspace: {
      folders: Array<{ name: string; uri: string }>;
      name: string;
      hasWorkspace: boolean;
    };
    capabilities: string[];
  }> {
    logger.debug(`Handling VS Code workspace request [${message.id}]`);
    try {
      // Get workspace information if available
      const workspaceInfo = {
        folders: [] as Array<{ name: string; uri: string }>,
        name: 'Unknown',
        hasWorkspace: false
      };

      // Get workspace folders from real VS Code API
      const folders = vscode.workspace.workspaceFolders;
      if (folders) {
        workspaceInfo.folders = folders.map(folder => ({
          name: folder.name,
          uri: folder.uri.toString()
        }));
        workspaceInfo.hasWorkspace = true;
        workspaceInfo.name = vscode.workspace.name || 'Workspace';
      }

      return {
        workspace: workspaceInfo,
        capabilities: ['file_access', 'editor_integration', 'diagnostics']
      };
    } catch (error) {
      logger.error('Error handling VS Code workspace request:', error);
      return {
        workspace: { folders: [], name: 'Unknown', hasWorkspace: false },
        capabilities: []
      };
    }
  }

  /**
     * Handle VS Code editor request
     */
  private async handleVSCodeEditor(message: MCPMessage): Promise<{
    editor: {
      activeEditor: null | {
        fileName: string;
        languageId: string;
        lineCount: number;
      };
      visibleEditors: unknown[];
      selection: null | {
        start: { line: number; character: number };
        end: { line: number; character: number };
        text: string;
      };
    };
    capabilities: string[];
  }> {
    try {
      logger.debug(`Processing editor request: ${message.id} (${message.method})`);
      const editorInfo = {
        activeEditor: null as null | {
          fileName: string;
          languageId: string;
          lineCount: number;
        },
        visibleEditors: [],
        selection: null as null | {
          start: { line: number; character: number };
          end: { line: number; character: number };
          text: string;
        }
      };

      // Get editor info from real VS Code API
      const activeEditor = vscode.window.activeTextEditor;
      if (activeEditor) {
        editorInfo.activeEditor = {
          fileName: activeEditor.document.fileName,
          languageId: activeEditor.document.languageId,
          lineCount: activeEditor.document.lineCount
        };

        if (!activeEditor.selection.isEmpty) {
          editorInfo.selection = {
            start: { 
              line: activeEditor.selection.start.line, 
              character: activeEditor.selection.start.character 
            },
            end: { 
              line: activeEditor.selection.end.line, 
              character: activeEditor.selection.end.character 
            },
            text: activeEditor.document.getText(activeEditor.selection)
          };
        }
      }

      return {
        editor: editorInfo,
        capabilities: ['text_manipulation', 'selection_access', 'cursor_control']
      };
    } catch (error) {
      logger.error('Error handling VS Code editor request:', error);
      return {
        editor: { activeEditor: null, visibleEditors: [], selection: null },
        capabilities: []
      };
    }
  }

  /**
     * Handle VS Code chat request
     */
  private async handleVSCodeChat(message: MCPMessage): Promise<{
    success: boolean;
    messageId?: string;
    response?: string;
    messages?: unknown[];
    total?: number;
    cleared?: number;
    error?: string;
  }> {
    try {
      if (!message.params || !this.isChatParams(message.params)) {
        throw new Error('Action and data parameters are required');
      }

      switch (message.params.action) {
      case 'send_message': {
        const msg = message.params.data.message;
        if (typeof msg !== 'string') {
          throw new Error('Message must be a string');
        }
        return {
          success: true,
          messageId: Date.now().toString(),
          response: `Processed chat message: ${msg}`
        };
      }

      case 'get_history':
        return {
          success: true,
          messages: [],
          total: 0
        };

      case 'clear_history':
        return {
          success: true,
          cleared: 0
        };

      default:
        throw new Error(`Unknown chat action: ${message.params.action}`);
      }
    } catch (error) {
      logger.error('Error handling VS Code chat request:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
     * Handle VS Code diagnostics request
     */
  private async handleVSCodeDiagnostics(message: MCPMessage): Promise<{
    diagnostics: {
      uri: string;
      problems: unknown[];
      summary: { errors: number; warnings: number; info: number };
    };
    capabilities: string[];
  }> {
    try {
      if (!message.params || !this.isResourceParams(message.params)) {
        throw new Error('URI parameter is required');
      }

      const diagnostics = {
        uri: message.params.uri,
        problems: [],
        summary: {
          errors: 0,
          warnings: 0,
          info: 0
        }
      };

      return {
        diagnostics,
        capabilities: ['problem_detection', 'code_analysis', 'fix_suggestions']
      };
    } catch (error) {
      logger.error('Error handling VS Code diagnostics request:', error);
      return {
        diagnostics: { uri: 'unknown', problems: [], summary: { errors: 0, warnings: 0, info: 0 } },
        capabilities: []
      };
    }
  }

  /**
     * Create an MCP message
     */
  public createMessage(
    type: MCPMessageType,
    method?: string,
    params?: MCPMessageParams,
    result?: Record<string, unknown>,
    error?: MCPError
  ): MCPMessage {
    return {
      id: Date.now().toString(),
      type,
      method,
      params,
      result,
      error,
      timestamp: Date.now(),
      version: MCP_VERSION
    };
  }

  /**
     * Dispose of the MCP manager
     */
  public dispose(): void {
    this.servers.clear();
    this.messageHandlers.clear();
    this.resourceSubscriptions.clear();
    logger.info('MCP 2025 Manager disposed');
  }
  
  /**
   * Get the current context (IMCPManager interface implementation)
   */
  public getCurrentContext(): MCPContext {
    return {
      version: MCP_VERSION,
      metadata: {
        source: 'codessa-mcp2025',
        timestamp: new Date().toISOString(),
        sessionId: Date.now().toString()
      },
      content: {}
    };
  }
  
  /**
   * Update the context with new content (IMCPManager interface implementation)
   */
  public updateContext(content: Record<string, unknown>): void {
    logger.info('MCP2025Manager: updateContext called with content');
    // Update the context with the new content
    this.context.content = {
      ...this.context.content,
      ...content
    };
  }
  
  /**
   * Add code to the context (IMCPManager interface implementation)
   */
  public addCode(language: string, content: string, path?: string): void {
    logger.info(`MCP2025Manager: addCode called for ${path || 'unnamed'}`);
  }
  
  /**
   * Add a file to the context (IMCPManager interface implementation)
   */
  public addFile(path: string, content: string, language?: string): void {
    logger.info(`MCP2025Manager: addFile called for ${path}`);
    
    // Add file content to the context
    this.context.content[path] = {
      content,
      language: language || 'text',
      timestamp: new Date().toISOString()
    };
  }
}
