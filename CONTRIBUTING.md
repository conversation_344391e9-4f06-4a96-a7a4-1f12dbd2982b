# Contributing to <PERSON><PERSON>

Thank you for your interest in contributing to Codessa! Codessa is an open-source project (GPL-3.0 for non-commercial use) and welcomes contributions from the community. Please read the guidelines below to participate in development.

## How to Contribute

1. **Fork the Repository**
   - Click the "Fork" button on GitHub to create your own copy.
2. **Clone Your Fork**
   - `git clone https://github.com/YOUR_USERNAME/codessa.git`
3. **Create a Branch**
   - Use a descriptive branch name, e.g., `feature/agent-framework` or `fix/typo-readme`.
4. **Make Your Changes**
   - Follow the coding style and best practices used in the project.
   - Write clear commit messages.
   - Add tests if applicable.
5. **Test Your Changes**
   - Run `npm install` and `npm run compile` to ensure everything builds.
   - Run and test the extension in VS Code (press F5).
6. **Submit a Pull Request**
   - Push your branch to GitHub and open a Pull Request (PR) against the `main` branch.
   - Describe your changes clearly in the PR description.

## Code of Conduct

Be respectful and constructive. Disagreements are fine, but always be courteous and open to feedback.

## Reporting Issues

If you find a bug or have a feature request, please open an issue on GitHub. Provide as much detail as possible to help us address the problem quickly.

## License & Commercial Use

- Codessa is **open source for non-commercial use** under the GPL-3.0 license.
- **Commercial use** requires a separate license. Contact the <NAME_EMAIL> for commercial arrangements and written permission.

## Contact

For questions, commercial licensing, or support, contact:
- Email: <EMAIL>
- GitHub: https://github.com/djlaserman

Thank you for helping make Codessa better!
