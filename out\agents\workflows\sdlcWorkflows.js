"use strict";
/**
 * Codessa SDLC Workflows
 *
 * This module provides workflow templates for Software Development Life Cycle (SDLC) phases:
 * - Planning
 * - Requirements
 * - Design
 * - Implementation
 * - Testing
 * - Deployment
 * - Maintenance
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPlanningWorkflow = createPlanningWorkflow;
exports.createRequirementsWorkflow = createRequirementsWorkflow;
exports.createDesignWorkflow = createDesignWorkflow;
exports.createImplementationWorkflow = createImplementationWorkflow;
exports.createTestingWorkflow = createTestingWorkflow;
exports.createDeploymentWorkflow = createDeploymentWorkflow;
exports.createMaintenanceWorkflow = createMaintenanceWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
const codessaMemory_1 = require("../../memory/codessa/codessaMemory");
const vectorStores_1 = require("./vectorStores");
/**
 * Create a Planning workflow for project planning and roadmap creation
 */
function createPlanningWorkflow(id, name, description, productOwnerAgent, techLeadAgent, projectManagerAgent, tools = []) {
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const requirementsAnalysisNode = graph_1.Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', productOwnerAgent);
    const scopeDefinitionNode = graph_1.Codessa.createAgentNode('scope-definition', 'Scope Definition', projectManagerAgent);
    const resourcePlanningNode = graph_1.Codessa.createAgentNode('resource-planning', 'Resource Planning', projectManagerAgent);
    const technicalFeasibilityNode = graph_1.Codessa.createAgentNode('technical-feasibility', 'Technical Feasibility', techLeadAgent);
    const roadmapCreationNode = graph_1.Codessa.createAgentNode('roadmap-creation', 'Roadmap Creation', projectManagerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    const planningMemoryManager = {
        async storeFeasibilityAnalysis(feasibilityData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(feasibilityData),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['planning', 'technical-feasibility', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved technical feasibility analysis to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save technical feasibility analysis to memory:', error);
            }
        },
        async retrievePreviousRoadmaps() {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query: 'project roadmap planning',
                    limit: 3
                });
                const roadmaps = memories.map(m => {
                    try {
                        return JSON.parse(m.content);
                    }
                    catch (_e) {
                        return m.content;
                    }
                });
                logger_1.Logger.instance.info(`Retrieved ${roadmaps.length} previous roadmaps from memory`);
                return roadmaps;
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to retrieve previous roadmaps from memory:', error);
                return [];
            }
        },
        async storeRoadmap(roadmap) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(roadmap),
                    metadata: {
                        source: 'system',
                        type: 'project',
                        timestamp: new Date().toISOString(),
                        tags: ['planning', 'roadmap', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved project roadmap to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save project roadmap to memory:', error);
            }
        }
    };
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },
        { name: 'requirements-to-scope', source: 'requirements-analysis', target: 'scope-definition', type: 'default' },
        { name: 'scope-to-resources', source: 'scope-definition', target: 'resource-planning', type: 'default' },
        { name: 'resources-to-feasibility', source: 'resource-planning', target: 'technical-feasibility', type: 'default' },
        { name: 'feasibility-to-roadmap', source: 'technical-feasibility', target: 'roadmap-creation', type: 'default' },
        { name: 'roadmap-to-output', source: 'roadmap-creation', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect technical feasibility to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `feasibility-to-tool-${index}`,
                source: 'technical-feasibility',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to roadmap creation
            edges.push({
                name: `tool-${index}-to-roadmap`,
                source: `tool-${index}`,
                target: 'roadmap-creation',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            requirementsAnalysisNode,
            scopeDefinitionNode,
            resourcePlanningNode,
            technicalFeasibilityNode,
            roadmapCreationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        type: 'planning',
        metadata: {
            memoryManager: planningMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a Requirements workflow for gathering and analyzing requirements
 */
function createRequirementsWorkflow(id, name, description, businessAnalystAgent, techLeadAgent, productOwnerAgent, tools = []) {
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const stakeholderInterviewNode = graph_1.Codessa.createAgentNode('stakeholder-interview', 'Stakeholder Interview', businessAnalystAgent);
    const requirementsGatheringNode = graph_1.Codessa.createAgentNode('requirements-gathering', 'Requirements Gathering', businessAnalystAgent);
    const requirementsAnalysisNode = graph_1.Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', techLeadAgent);
    const requirementsPrioritizationNode = graph_1.Codessa.createAgentNode('requirements-prioritization', 'Requirements Prioritization', productOwnerAgent);
    const requirementsDocumentationNode = graph_1.Codessa.createAgentNode('requirements-documentation', 'Requirements Documentation', businessAnalystAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    const requirementsMemoryManager = {
        async storeStakeholderInterviews(interviewData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(interviewData),
                    metadata: {
                        source: 'user',
                        type: 'conversation',
                        timestamp: new Date().toISOString(),
                        tags: ['requirements', 'interview', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved stakeholder interview data to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save stakeholder interview data to memory:', error);
            }
        },
        async storeRequirements(requirements) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(requirements),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['requirements', 'documentation', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved requirements to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save requirements to memory:', error);
            }
        },
        async retrieveSimilarRequirements(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5
                });
                const requirements = memories.map(m => {
                    try {
                        return JSON.parse(m.content);
                    }
                    catch (_e) {
                        return m.content;
                    }
                });
                logger_1.Logger.instance.info(`Retrieved ${requirements.length} similar requirements from memory`);
                return requirements;
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to retrieve similar requirements from memory:', error);
                return [];
            }
        }
    };
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-interview', source: 'input', target: 'stakeholder-interview', type: 'default' },
        { name: 'interview-to-gathering', source: 'stakeholder-interview', target: 'requirements-gathering', type: 'default' },
        { name: 'gathering-to-analysis', source: 'requirements-gathering', target: 'requirements-analysis', type: 'default' },
        { name: 'analysis-to-prioritization', source: 'requirements-analysis', target: 'requirements-prioritization', type: 'default' },
        { name: 'prioritization-to-documentation', source: 'requirements-prioritization', target: 'requirements-documentation', type: 'default' },
        { name: 'documentation-to-output', source: 'requirements-documentation', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect requirements analysis to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `analysis-to-tool-${index}`,
                source: 'requirements-analysis',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to requirements prioritization
            edges.push({
                name: `tool-${index}-to-prioritization`,
                source: `tool-${index}`,
                target: 'requirements-prioritization',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            stakeholderInterviewNode,
            requirementsGatheringNode,
            requirementsAnalysisNode,
            requirementsPrioritizationNode,
            requirementsDocumentationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        type: 'requirements',
        metadata: {
            memoryManager: requirementsMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a Design workflow for system architecture and design
 */
function createDesignWorkflow(id, name, description, architectAgent, uiDesignerAgent, databaseDesignerAgent, tools = []) {
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const architectureDesignNode = graph_1.Codessa.createAgentNode('architecture-design', 'Architecture Design', architectAgent);
    const databaseDesignNode = graph_1.Codessa.createAgentNode('database-design', 'Database Design', databaseDesignerAgent);
    const uiDesignNode = graph_1.Codessa.createAgentNode('ui-design', 'UI Design', uiDesignerAgent);
    const apiDesignNode = graph_1.Codessa.createAgentNode('api-design', 'API Design', architectAgent);
    const designReviewNode = graph_1.Codessa.createAgentNode('design-review', 'Design Review', architectAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    const designMemoryManager = {
        async storeArchitectureDesign(designData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(designData),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['design', 'architecture', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved architecture design to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save architecture design to memory:', error);
            }
        },
        async storeDatabaseDesign(designData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(designData),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['design', 'database', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved database design to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save database design to memory:', error);
            }
        },
        async storeUIDesign(designData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(designData),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['design', 'ui', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved UI design to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save UI design to memory:', error);
            }
        },
        async storeAPIDesign(designData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(designData),
                    metadata: {
                        source: 'system',
                        type: 'document',
                        timestamp: new Date().toISOString(),
                        tags: ['design', 'api', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved API design to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save API design to memory:', error);
            }
        },
        async retrieveDesignPatterns(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5
                });
                const patterns = memories.map(m => {
                    try {
                        return JSON.parse(m.content);
                    }
                    catch (_e) {
                        return m.content;
                    }
                });
                logger_1.Logger.instance.info(`Retrieved ${patterns.length} design patterns from memory`);
                return patterns;
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to retrieve design patterns from memory:', error);
                return [];
            }
        }
    };
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-architecture', source: 'input', target: 'architecture-design', type: 'default' },
        { name: 'architecture-to-database', source: 'architecture-design', target: 'database-design', type: 'default' },
        { name: 'architecture-to-ui', source: 'architecture-design', target: 'ui-design', type: 'default' },
        { name: 'architecture-to-api', source: 'architecture-design', target: 'api-design', type: 'default' },
        { name: 'database-to-review', source: 'database-design', target: 'design-review', type: 'default' },
        { name: 'ui-to-review', source: 'ui-design', target: 'design-review', type: 'default' },
        { name: 'api-to-review', source: 'api-design', target: 'design-review', type: 'default' },
        { name: 'review-to-output', source: 'design-review', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect architecture design to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `architecture-to-tool-${index}`,
                source: 'architecture-design',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to design review
            edges.push({
                name: `tool-${index}-to-review`,
                source: `tool-${index}`,
                target: 'design-review',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            architectureDesignNode,
            databaseDesignNode,
            uiDesignNode,
            apiDesignNode,
            designReviewNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        type: 'design',
        metadata: {
            memoryManager: designMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create an Implementation workflow for code development
 */
function createImplementationWorkflow(id, name, description, developerAgent, reviewerAgent, qaAgent, tools = []) {
    logger_1.Logger.instance.info(`Creating Implementation workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const taskBreakdownNode = graph_1.Codessa.createAgentNode('task-breakdown', 'Task Breakdown', developerAgent);
    const codeImplementationNode = graph_1.Codessa.createAgentNode('code-implementation', 'Code Implementation', developerAgent);
    const unitTestingNode = graph_1.Codessa.createAgentNode('unit-testing', 'Unit Testing', developerAgent);
    const codeReviewNode = graph_1.Codessa.createAgentNode('code-review', 'Code Review', reviewerAgent);
    const integrationNode = graph_1.Codessa.createAgentNode('integration', 'Integration', developerAgent);
    const qaTestingNode = graph_1.Codessa.createAgentNode('qa-testing', 'QA Testing', qaAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create tool nodes
    const codeAnalysisTool = (0, vectorStores_1.createCodeAnalysisTool)();
    const testingTool = (0, vectorStores_1.createTestingTool)();
    const codeAnalysisNode = graph_1.Codessa.createToolNode('code-analysis-tool', 'Code Analysis Tool', codeAnalysisTool);
    const testingToolNode = graph_1.Codessa.createToolNode('testing-tool', 'Testing Tool', testingTool);
    // Add additional tool nodes if tools are provided
    const additionalToolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`additional-tool-${index}`, `Tool: ${tool.name}`, tool));
    const implementationMemoryManager = {
        async storeCodeImplementation(codeData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(codeData),
                    metadata: {
                        source: 'system',
                        type: 'code',
                        timestamp: new Date().toISOString(),
                        tags: ['implementation', 'code', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved code implementation to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save code implementation to memory:', error);
            }
        },
        async storeTestResults(testResults) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(testResults),
                    metadata: {
                        source: 'system',
                        type: 'insight',
                        timestamp: new Date().toISOString(),
                        tags: ['implementation', 'testing', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved test results to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save test results to memory:', error);
            }
        },
        async storeCodeReview(reviewData) {
            try {
                await codessaMemory_1.codessaMemoryProvider.addMemory({
                    content: JSON.stringify(reviewData),
                    metadata: {
                        source: 'system',
                        type: 'insight',
                        timestamp: new Date().toISOString(),
                        tags: ['implementation', 'review', 'sdlc']
                    }
                });
                logger_1.Logger.instance.info('Saved code review to memory');
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to save code review to memory:', error);
            }
        },
        async retrieveCodeExamples(query) {
            try {
                const memories = await codessaMemory_1.codessaMemoryProvider.searchMemories({
                    query,
                    limit: 5
                });
                const examples = memories.map(m => {
                    try {
                        return JSON.parse(m.content);
                    }
                    catch (_e) {
                        return m.content;
                    }
                });
                logger_1.Logger.instance.info(`Retrieved ${examples.length} code examples from memory`);
                return examples;
            }
            catch (error) {
                logger_1.Logger.instance.error('Failed to retrieve code examples from memory:', error);
                return [];
            }
        }
    };
    // Create edges
    const edges = [
        { name: 'input-to-breakdown', source: 'input', target: 'task-breakdown', type: 'default' },
        { name: 'breakdown-to-implementation', source: 'task-breakdown', target: 'code-implementation', type: 'default' },
        { name: 'implementation-to-analysis', source: 'code-implementation', target: 'code-analysis-tool', type: 'default' },
        { name: 'analysis-to-unit-testing', source: 'code-analysis-tool', target: 'unit-testing', type: 'default' },
        { name: 'unit-testing-to-testing-tool', source: 'unit-testing', target: 'testing-tool', type: 'default' },
        { name: 'testing-tool-to-review', source: 'testing-tool', target: 'code-review', type: 'default' },
        { name: 'review-to-integration', source: 'code-review', target: 'integration', type: 'default' },
        { name: 'integration-to-qa', source: 'integration', target: 'qa-testing', type: 'default' },
        { name: 'qa-to-output', source: 'qa-testing', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'review-to-implementation', source: 'code-review', target: 'code-implementation', type: 'feedback' },
        { name: 'qa-to-implementation', source: 'qa-testing', target: 'code-implementation', type: 'feedback' }
    ];
    // Add additional tool edges if tools are provided
    if (additionalToolNodes.length > 0) {
        // Connect code implementation to additional tools
        additionalToolNodes.forEach((_, index) => {
            edges.push({
                name: `implementation-to-additional-tool-${index}`,
                source: 'code-implementation',
                target: `additional-tool-${index}`,
                type: 'conditional'
            });
            // Connect additional tools back to unit testing
            edges.push({
                name: `additional-tool-${index}-to-unit-testing`,
                source: `additional-tool-${index}`,
                target: 'unit-testing',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            taskBreakdownNode,
            codeImplementationNode,
            codeAnalysisNode,
            unitTestingNode,
            testingToolNode,
            codeReviewNode,
            integrationNode,
            qaTestingNode,
            outputNode,
            ...additionalToolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        type: 'implementation',
        metadata: {
            memoryManager: implementationMemoryManager
        }
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a Testing workflow for comprehensive testing
 */
function createTestingWorkflow(id, name, description, qaAgent, developerAgent, securityAgent) {
    logger_1.Logger.instance.info(`Creating Testing workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const testPlanningNode = graph_1.Codessa.createAgentNode('test-planning', 'Test Planning', qaAgent);
    const testCaseDesignNode = graph_1.Codessa.createAgentNode('test-case-design', 'Test Case Design', qaAgent);
    const unitTestingNode = graph_1.Codessa.createAgentNode('unit-testing', 'Unit Testing', developerAgent);
    const integrationTestingNode = graph_1.Codessa.createAgentNode('integration-testing', 'Integration Testing', qaAgent);
    const systemTestingNode = graph_1.Codessa.createAgentNode('system-testing', 'System Testing', qaAgent);
    const performanceTestingNode = graph_1.Codessa.createAgentNode('performance-testing', 'Performance Testing', qaAgent);
    const securityTestingNode = graph_1.Codessa.createAgentNode('security-testing', 'Security Testing', securityAgent);
    const bugFixingNode = graph_1.Codessa.createAgentNode('bug-fixing', 'Bug Fixing', developerAgent);
    const regressionTestingNode = graph_1.Codessa.createAgentNode('regression-testing', 'Regression Testing', qaAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create tool nodes
    const testingTool = (0, vectorStores_1.createTestingTool)();
    const testingToolNode = graph_1.Codessa.createToolNode('testing-tool', 'Testing Tool', testingTool);
    // Create edges
    const edges = [
        { name: 'input-to-planning', source: 'input', target: 'test-planning', type: 'default' },
        { name: 'planning-to-design', source: 'test-planning', target: 'test-case-design', type: 'default' },
        { name: 'design-to-unit', source: 'test-case-design', target: 'unit-testing', type: 'default' },
        { name: 'unit-to-integration', source: 'unit-testing', target: 'integration-testing', type: 'default' },
        { name: 'integration-to-system', source: 'integration-testing', target: 'system-testing', type: 'default' },
        { name: 'system-to-performance', source: 'system-testing', target: 'performance-testing', type: 'default' },
        { name: 'performance-to-security', source: 'performance-testing', target: 'security-testing', type: 'default' },
        { name: 'security-to-tool', source: 'security-testing', target: 'testing-tool', type: 'default' },
        { name: 'tool-to-bug-fixing', source: 'testing-tool', target: 'bug-fixing', type: 'default' },
        { name: 'bug-fixing-to-regression', source: 'bug-fixing', target: 'regression-testing', type: 'default' },
        { name: 'regression-to-output', source: 'regression-testing', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'regression-to-bug-fixing', source: 'regression-testing', target: 'bug-fixing', type: 'feedback' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            testPlanningNode,
            testCaseDesignNode,
            unitTestingNode,
            integrationTestingNode,
            systemTestingNode,
            performanceTestingNode,
            securityTestingNode,
            testingToolNode,
            bugFixingNode,
            regressionTestingNode,
            outputNode
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        type: 'testing'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a Deployment workflow for releasing software
 */
function createDeploymentWorkflow(id, name, description, devOpsAgent, qaAgent, supportAgent) {
    logger_1.Logger.instance.info(`Creating Deployment workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const releasePreparationNode = graph_1.Codessa.createAgentNode('release-preparation', 'Release Preparation', devOpsAgent);
    const environmentSetupNode = graph_1.Codessa.createAgentNode('environment-setup', 'Environment Setup', devOpsAgent);
    const deploymentPlanningNode = graph_1.Codessa.createAgentNode('deployment-planning', 'Deployment Planning', devOpsAgent);
    const stagingDeploymentNode = graph_1.Codessa.createAgentNode('staging-deployment', 'Staging Deployment', devOpsAgent);
    const stagingTestingNode = graph_1.Codessa.createAgentNode('staging-testing', 'Staging Testing', qaAgent);
    const productionDeploymentNode = graph_1.Codessa.createAgentNode('production-deployment', 'Production Deployment', devOpsAgent);
    const postDeploymentTestingNode = graph_1.Codessa.createAgentNode('post-deployment-testing', 'Post-Deployment Testing', qaAgent);
    const monitoringSetupNode = graph_1.Codessa.createAgentNode('monitoring-setup', 'Monitoring Setup', devOpsAgent);
    const documentationUpdateNode = graph_1.Codessa.createAgentNode('documentation-update', 'Documentation Update', supportAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create tool nodes
    const deploymentTool = (0, vectorStores_1.createDeploymentTool)();
    const monitoringTool = (0, vectorStores_1.createMonitoringTool)();
    const documentationTool = (0, vectorStores_1.createDocumentationTool)();
    const cicdTool = (0, vectorStores_1.createCI_CDTool)();
    const deploymentToolNode = graph_1.Codessa.createToolNode('deployment-tool', 'Deployment Tool', deploymentTool);
    const monitoringToolNode = graph_1.Codessa.createToolNode('monitoring-tool', 'Monitoring Tool', monitoringTool);
    const documentationToolNode = graph_1.Codessa.createToolNode('documentation-tool', 'Documentation Tool', documentationTool);
    const cicdToolNode = graph_1.Codessa.createToolNode('cicd-tool', 'CI/CD Tool', cicdTool);
    // Create edges
    const edges = [
        { name: 'input-to-preparation', source: 'input', target: 'release-preparation', type: 'default' },
        { name: 'preparation-to-environment', source: 'release-preparation', target: 'environment-setup', type: 'default' },
        { name: 'environment-to-planning', source: 'environment-setup', target: 'deployment-planning', type: 'default' },
        { name: 'planning-to-cicd', source: 'deployment-planning', target: 'cicd-tool', type: 'default' },
        { name: 'cicd-to-staging', source: 'cicd-tool', target: 'staging-deployment', type: 'default' },
        { name: 'staging-to-testing', source: 'staging-deployment', target: 'staging-testing', type: 'default' },
        { name: 'testing-to-deployment-tool', source: 'staging-testing', target: 'deployment-tool', type: 'default' },
        { name: 'deployment-tool-to-production', source: 'deployment-tool', target: 'production-deployment', type: 'default' },
        { name: 'production-to-post-testing', source: 'production-deployment', target: 'post-deployment-testing', type: 'default' },
        { name: 'post-testing-to-monitoring', source: 'post-deployment-testing', target: 'monitoring-setup', type: 'default' },
        { name: 'monitoring-to-tool', source: 'monitoring-setup', target: 'monitoring-tool', type: 'default' },
        { name: 'monitoring-tool-to-documentation-tool', source: 'monitoring-tool', target: 'documentation-tool', type: 'default' },
        { name: 'documentation-tool-to-documentation', source: 'documentation-tool', target: 'documentation-update', type: 'default' },
        { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'staging-testing-to-staging', source: 'staging-testing', target: 'staging-deployment', type: 'feedback' },
        { name: 'post-testing-to-production', source: 'post-deployment-testing', target: 'production-deployment', type: 'feedback' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            releasePreparationNode,
            environmentSetupNode,
            deploymentPlanningNode,
            cicdToolNode,
            stagingDeploymentNode,
            stagingTestingNode,
            deploymentToolNode,
            productionDeploymentNode,
            postDeploymentTestingNode,
            monitoringSetupNode,
            monitoringToolNode,
            documentationToolNode,
            documentationUpdateNode,
            outputNode
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        type: 'deployment'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a Maintenance workflow for ongoing support and updates
 */
function createMaintenanceWorkflow(id, name, description, supportAgent, developerAgent, devOpsAgent) {
    logger_1.Logger.instance.info(`Creating Maintenance workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const monitoringNode = graph_1.Codessa.createAgentNode('monitoring', 'Monitoring', devOpsAgent);
    const issueTriageNode = graph_1.Codessa.createAgentNode('issue-triage', 'Issue Triage', supportAgent);
    const bugAnalysisNode = graph_1.Codessa.createAgentNode('bug-analysis', 'Bug Analysis', developerAgent);
    const hotfixNode = graph_1.Codessa.createAgentNode('hotfix', 'Hotfix', developerAgent);
    const performanceOptimizationNode = graph_1.Codessa.createAgentNode('performance-optimization', 'Performance Optimization', developerAgent);
    const securityPatchingNode = graph_1.Codessa.createAgentNode('security-patching', 'Security Patching', developerAgent);
    const updateDeploymentNode = graph_1.Codessa.createAgentNode('update-deployment', 'Update Deployment', devOpsAgent);
    const documentationUpdateNode = graph_1.Codessa.createAgentNode('documentation-update', 'Documentation Update', supportAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create tool nodes
    const monitoringTool = (0, vectorStores_1.createMonitoringTool)();
    const deploymentTool = (0, vectorStores_1.createDeploymentTool)();
    const monitoringToolNode = graph_1.Codessa.createToolNode('monitoring-tool', 'Monitoring Tool', monitoringTool);
    const deploymentToolNode = graph_1.Codessa.createToolNode('deployment-tool', 'Deployment Tool', deploymentTool);
    // Create edges
    const edges = [
        { name: 'input-to-monitoring', source: 'input', target: 'monitoring', type: 'default' },
        { name: 'monitoring-to-tool', source: 'monitoring', target: 'monitoring-tool', type: 'default' },
        { name: 'monitoring-tool-to-triage', source: 'monitoring-tool', target: 'issue-triage', type: 'default' },
        { name: 'triage-to-analysis', source: 'issue-triage', target: 'bug-analysis', type: 'default' },
        { name: 'analysis-to-hotfix', source: 'bug-analysis', target: 'hotfix', type: 'conditional' },
        { name: 'analysis-to-performance', source: 'bug-analysis', target: 'performance-optimization', type: 'conditional' },
        { name: 'analysis-to-security', source: 'bug-analysis', target: 'security-patching', type: 'conditional' },
        { name: 'hotfix-to-deployment', source: 'hotfix', target: 'update-deployment', type: 'default' },
        { name: 'performance-to-deployment', source: 'performance-optimization', target: 'update-deployment', type: 'default' },
        { name: 'security-to-deployment', source: 'security-patching', target: 'update-deployment', type: 'default' },
        { name: 'deployment-to-tool', source: 'update-deployment', target: 'deployment-tool', type: 'default' },
        { name: 'deployment-tool-to-documentation', source: 'deployment-tool', target: 'documentation-update', type: 'default' },
        { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'deployment-to-monitoring', source: 'update-deployment', target: 'monitoring', type: 'feedback' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            monitoringNode,
            monitoringToolNode,
            issueTriageNode,
            bugAnalysisNode,
            hotfixNode,
            performanceOptimizationNode,
            securityPatchingNode,
            updateDeploymentNode,
            deploymentToolNode,
            documentationUpdateNode,
            outputNode
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        type: 'maintenance'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=sdlcWorkflows.js.map