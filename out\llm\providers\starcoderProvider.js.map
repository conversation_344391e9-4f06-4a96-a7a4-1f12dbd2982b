{"version": 3, "file": "starcoderProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/starcoderProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAGpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;;;;GAKG;AACH,MAAa,iBAAkB,SAAQ,iCAAe;IAC3C,UAAU,GAAG,WAAW,CAAC;IACzB,WAAW,GAAG,WAAW,CAAC;IAC1B,WAAW,GAAG,mDAAmD,CAAC;IAClE,OAAO,GAAG,gCAAgC,CAAC;IAC3C,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,6CAA6C,CAAC;IAChE,YAAY,GAAG,wBAAwB,CAAC;IAEzC,MAAM,GAAQ,IAAI,CAAC;IAE3B,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBACjF,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;YAC7E,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,MAAM,EAAE,sCAAsC;gBACvD,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,MAAM,EAAE;oBACnC,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACvE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,sDAAsD,EAAE,CAAC;QACxF,CAAC;QAED,IAAI,CAAC;YACH,qFAAqF;YACrF,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAChF,MAAM,QAAQ,GAAG,GAAG,OAAO,EAAE,CAAC;YAE9B,qBAAqB;YACrB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,gCAAgC;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,eAAe,MAAM,CAAC,YAAY,MAAM,CAAC;YACrD,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,aAAa,OAAO,CAAC,OAAO,MAAM,CAAC;oBAC/C,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,kBAAkB,OAAO,CAAC,OAAO,MAAM,CAAC;oBACpD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,MAAM,IAAI,eAAe,OAAO,CAAC,OAAO,MAAM,CAAC;oBACjD,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,aAAa,MAAM,CAAC,MAAM,MAAM,CAAC;YAC7C,CAAC;YAED,gFAAgF;YAChF,MAAM,IAAI,iBAAiB,CAAC;YAE5B,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,uBAAuB;YACvB,MAAM,WAAW,GAAG;gBAClB,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE;oBACV,cAAc,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;oBACxC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,KAAK,EAAE,IAAI;oBACX,SAAS,EAAE,IAAI;oBACf,gBAAgB,EAAE,KAAK;oBACvB,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,CAAC;iBACpE;aACF,CAAC;YAEF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,OAAO,EAAE,CAAC,CAAC;YAEvE,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;wBAC5D,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBAChH,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE;gBAC7D,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,8CAA8C;YAC9C,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;YACvE,CAAC;YAED,qBAAqB;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,iEAAiE;YACjE,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/C,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;oBAC7B,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;gBACrC,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,MAAM;gBACpB,KAAK,EAAE;oBACL,YAAY,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;oBAClD,gBAAgB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;iBACxD;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,YAAY,GAAG,+BAA+B,CAAC;YAEnD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,wBAAwB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClH,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,6DAA6D;QAC7D,OAAO;YACL;gBACE,EAAE,EAAE,wBAAwB;gBAC5B,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,0CAA0C;gBACvD,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,4BAA4B;aAC1C;YACD;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,0CAA0C;gBACvD,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,4BAA4B;aAC1C;YACD;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,2CAA2C;gBACxD,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,4BAA4B;aAC1C;YACD;gBACE,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,2CAA2C;gBACxD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,4BAA4B;aAC1C;YACD;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,uCAAuC;gBACpD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,4BAA4B;aAC1C;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,8DAA8D;aACxE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,QAAQ,GAAG,OAAO,CAAC;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChD,MAAM,EAAE,oBAAoB;gBAC5B,UAAU,EAAE;oBACV,cAAc,EAAE,EAAE;oBAClB,gBAAgB,EAAE,KAAK;iBACxB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAClB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,6DAA6D,OAAO,IAAI;iBAClF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uDAAuD;iBACjE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAClE,IAAI,YAAY,GAAG,oCAAoC,CAAC;YAExD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,wBAAwB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClH,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,yEAAyE;gBACtF,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,gGAAgG;gBAC7G,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,oCAAoC;gBACjD,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,wBAAwB;oBACxB,uBAAuB;oBACvB,uBAAuB;oBACvB,mBAAmB;oBACnB,uBAAuB;iBACxB;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAzTD,8CAyTC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Base<PERSON><PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { Logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for StarCoder and StarCoder2 models via Hugging Face\n * \n * StarCoder and StarCoder2 are open-source code models developed by the BigCode project\n * They are MIT licensed and available on Hugging Face\n */\nexport class StarCoderProvider extends BaseLLMProvider {\n  readonly providerId = 'starcoder';\n  readonly displayName = 'StarCoder';\n  readonly description = 'Open-source code models by BigCode (MIT licensed)';\n  readonly website = 'https://huggingface.co/bigcode';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api-inference.huggingface.co/models';\n  readonly defaultModel = 'bigcode/starcoder2-15b';\n\n  private client: any = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        Logger.instance.info('StarCoder configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (!apiKey) {\n      Logger.instance.warn('Hugging Face API key not set for StarCoder provider.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 120000, // 2 minutes timeout for model loading\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      Logger.instance.info('StarCoder client initialized successfully.');\n    } catch (error) {\n      Logger.instance.error('Failed to initialize StarCoder client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using StarCoder models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'StarCoder provider not configured (API key missing?)' };\n    }\n\n    try {\n      // Prepare the model endpoint - for StarCoder we need to specify the model in the URL\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n      const endpoint = `${modelId}`;\n\n      // Prepare the prompt\n      let prompt = '';\n            \n      // Add system prompt if provided\n      if (params.systemPrompt) {\n        prompt += `<|system|>\\n${params.systemPrompt}\\n\\n`;\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            prompt += `<|user|>\\n${message.content}\\n\\n`;\n          } else if (message.role === 'assistant') {\n            prompt += `<|assistant|>\\n${message.content}\\n\\n`;\n          } else if (message.role === 'system') {\n            prompt += `<|system|>\\n${message.content}\\n\\n`;\n          }\n        }\n      } else {\n        // Just add the user prompt\n        prompt += `<|user|>\\n${params.prompt}\\n\\n`;\n      }\n            \n      // Add the assistant prefix to indicate we want the model to generate a response\n      prompt += '<|assistant|>\\n';\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Prepare request data\n      const requestData = {\n        inputs: prompt,\n        parameters: {\n          max_new_tokens: params.maxTokens || 1024,\n          temperature: params.temperature || 0.7,\n          top_p: 0.95,\n          do_sample: true,\n          return_full_text: false,\n          stop: params.stopSequences || ['<|user|>', '<|system|>', '<|end|>']\n        }\n      };\n\n      Logger.instance.debug(`Sending request to StarCoder model ${modelId}`);\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            Logger.instance.info('StarCoder request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          Logger.instance.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request\n      const response = await this.client.post(endpoint, requestData, {\n        signal: abortController?.signal\n      });\n\n      // Check for cancellation again after API call\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled during processing' };\n      }\n\n      // Parse the response\n      const result = response.data;\n            \n      // Hugging Face Inference API returns an array of generated texts\n      let content = '';\n      if (Array.isArray(result) && result.length > 0) {\n        if (result[0].generated_text) {\n          content = result[0].generated_text;\n        }\n      }\n\n      return {\n        content,\n        finishReason: 'stop',\n        usage: {\n          promptTokens: prompt.length / 4, // Rough estimate\n          completionTokens: content.length / 4, // Rough estimate\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error('StarCoder generate error:', error);\n      let errorMessage = 'Failed to call StarCoder API.';\n\n      if (error.response) {\n        errorMessage = `StarCoder API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available StarCoder models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    // StarCoder models are fixed, so we return a predefined list\n    return [\n      {\n        id: 'bigcode/starcoder2-15b',\n        name: 'StarCoder2 15B',\n        description: 'Latest StarCoder2 model (15B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free with Hugging Face API'\n      },\n      {\n        id: 'bigcode/starcoder2-7b',\n        name: 'StarCoder2 7B',\n        description: 'Smaller StarCoder2 model (7B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free with Hugging Face API'\n      },\n      {\n        id: 'bigcode/starcoder2-3b',\n        name: 'StarCoder2 3B',\n        description: 'Smallest StarCoder2 model (3B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free with Hugging Face API'\n      },\n      {\n        id: 'bigcode/starcoder',\n        name: 'StarCoder',\n        description: 'Original StarCoder model (15B parameters)',\n        contextWindow: 8192,\n        pricingInfo: 'Free with Hugging Face API'\n      },\n      {\n        id: 'bigcode/starcoderbase',\n        name: 'StarCoderBase',\n        description: 'Base model without instruction tuning',\n        contextWindow: 8192,\n        pricingInfo: 'Free with Hugging Face API'\n      }\n    ];\n  }\n\n  /**\n     * Test connection to StarCoder\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'StarCoder client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Simple test request to check if the API is working\n      const endpoint = modelId;\n      const response = await this.client.post(endpoint, {\n        inputs: 'def hello_world():',\n        parameters: {\n          max_new_tokens: 10,\n          return_full_text: false\n        }\n      });\n\n      if (response.data) {\n        return {\n          success: true,\n          message: `Successfully connected to StarCoder API and tested model '${modelId}'.`\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Connected to API but received an unexpected response.'\n        };\n      }\n    } catch (error: any) {\n      Logger.instance.error('StarCoder connection test failed:', error);\n      let errorMessage = 'Failed to connect to StarCoder API';\n\n      if (error.response) {\n        errorMessage = `StarCoder API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'Hugging Face API Key',\n        description: 'Your Hugging Face API key (from https://huggingface.co/settings/tokens)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The Hugging Face Inference API endpoint (default: https://api-inference.huggingface.co/models)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default StarCoder model to use',\n        required: false,\n        type: 'select',\n        options: [\n          'bigcode/starcoder2-15b',\n          'bigcode/starcoder2-7b',\n          'bigcode/starcoder2-3b',\n          'bigcode/starcoder',\n          'bigcode/starcoderbase'\n        ]\n      }\n    ];\n  }\n}\n"]}