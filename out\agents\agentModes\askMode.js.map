{"version": 3, "file": "askMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/askMode.ts"], "names": [], "mappings": ";;;AAAA,8BAA8B;AAC9B,mDAA4E;AAG5E,yCAAsC;AACtC,qDAAkD;AAClD,+DAA4D;AAE5D;;GAEG;AACH,MAAa,OAAQ,SAAQ,6BAAa;IAC/B,EAAE,GAAG,KAAK,CAAC;IACX,WAAW,GAAG,KAAK,CAAC;IACpB,WAAW,GAAG,mCAAmC,CAAC;IAClD,IAAI,GAAG,aAAa,CAAC;IACrB,kBAAkB,GAAG,2BAAW,CAAC,eAAe,CAAC;IACjD,yBAAyB,GAAG,KAAK,CAAC;IAClC,sBAAsB,GAAG,KAAK,CAAC;IAExC;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY,EACZ,aAA4B,EAC5B,iBAA2C;QAE3C,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,OAAO,EAAE,CAAC,CAAC;YAEnE,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAE7E,kCAAkC;YAClC,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;wBACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,mCAAmC,CAAC,CAAC;oBAC7F,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4CAA4C,EAAE,WAAW,CAAC,CAAC;gBAChF,kCAAkC;YACpC,CAAC;YAED,yCAAyC;YACzC,MAAM,MAAM,GAAG,6BAAa,CAAC,YAAY,CAAC,UAAU,EAAE;gBACpD,cAAc;gBACd,aAAa;gBACb,OAAO;aACR,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAEnE,mCAAmC;YACnC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBAC9C,MAAM,WAAW,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,EAAE,WAAW,CAAC,CAAC;gBAC7E,qCAAqC;YACvC,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YACtE,OAAO,mCAAmC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QACrG,CAAC;IACH,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;YACf,IAAI,EAAE,MAAM;SACb,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,eAAe,CACnB,MAAa,EACb,cAA6B;QAE7B,OAAO,6BAAa,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;SAEK;IACL,eAAe;QAKb,OAAO;YACL,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;CAoBnB;YACK,YAAY,EAAE;;;;;CAKnB;SACI,CAAC;IACJ,CAAC;CACF;AAlID,0BAkIC", "sourcesContent": ["// Remove unused vscode import\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { contextManager } from './contextManager';\nimport { promptManager } from '../../prompts/promptManager';\n\n/**\n * Ask Mode - Default mode that uses the entire codebase as context\n */\nexport class AskMode extends OperationMode {\n  readonly id = 'ask';\n  readonly displayName = 'Ask';\n  readonly description = 'Ask questions about your codebase';\n  readonly icon = '$(question)';\n  readonly defaultContextType = ContextType.ENTIRE_CODEBASE;\n  readonly requiresHumanVerification = false;\n  readonly supportsMultipleAgents = false;\n\n  /**\n     * Process a user message in Ask mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    contextSource: ContextSource,\n    _additionalParams?: Record<string, unknown>\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Ask mode: ${message}`);\n\n      // Get context content\n      const contextContent = await contextManager.getContextContent(contextSource);\n\n      // Add memory context if available\n      let memoryContext = '';\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          const relevantMemories = await agentMemory.getRelevantMemories(message);\n          if (relevantMemories && relevantMemories.length > 0) {\n            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to ask context`);\n          }\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to retrieve memory context for ask:', memoryError);\n        // Continue without memory context\n      }\n\n      // Prepare the prompt using promptManager\n      const prompt = promptManager.renderPrompt('mode.ask', {\n        contextContent,\n        memoryContext,\n        message\n      });\n\n      // Generate response using the agent\n      const response = await agent.generate(prompt, this.getLLMParams());\n\n      // Store the conversation in memory\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          await agentMemory.addMessage('user', message);\n          await agentMemory.addMessage('assistant', response);\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to store conversation in memory:', memoryError);\n        // Continue without storing in memory\n      }\n\n      return response;\n    } catch (error) {\n      Logger.instance.error('Error processing message in Ask mode:', error);\n      return `Error processing your question: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Get LLM parameters specific to Ask mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.7,\n      maxTokens: 2000,\n      mode: 'task'\n    };\n  }\n\n  /**\n     * Get the system prompt for Ask mode\n     */\n  async getSystemPrompt(\n    _agent: Agent,\n    _contextSource: ContextSource\n  ): Promise<string> {\n    return promptManager.renderPrompt('mode.ask', {});\n  }\n\n  /**\n     * Get UI components specific to Ask mode\n     */\n  getUIComponents(): {\n    controlPanel?: string;\n    contextPanel?: string;\n    messageInput?: string;\n  } {\n    return {\n      contextPanel: `\n<div class=\"context-panel\">\n    <div class=\"context-header\">\n        <h3>Context</h3>\n        <div class=\"context-controls\">\n            <button id=\"btn-refresh-context\" title=\"Refresh Context\"><i class=\"codicon codicon-refresh\"></i></button>\n            <button id=\"btn-select-files\" title=\"Select Files\"><i class=\"codicon codicon-file-code\"></i></button>\n            <button id=\"btn-select-folders\" title=\"Select Folders\"><i class=\"codicon codicon-folder\"></i></button>\n        </div>\n    </div>\n    <div class=\"context-type\">\n        <select id=\"context-type-selector\">\n            <option value=\"entire_codebase\">Entire Codebase</option>\n            <option value=\"selected_files\">Selected Files</option>\n            <option value=\"current_file\">Current File</option>\n            <option value=\"custom\">Custom</option>\n        </select>\n    </div>\n    <div id=\"context-files-list\" class=\"context-files-list\"></div>\n</div>\n`,\n      messageInput: `\n<div class=\"message-input-container\">\n    <textarea id=\"message-input\" placeholder=\"Ask a question about your code...\"></textarea>\n    <button id=\"btn-send\" title=\"Send\"><i class=\"codicon codicon-send\"></i></button>\n</div>\n`\n    };\n  }\n}\n"]}