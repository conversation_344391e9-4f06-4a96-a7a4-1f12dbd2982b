import type { IDatabase } from '../../types';
import { logger } from '../../../logger';
import { getConfig } from '../../../config';
import { createClient } from 'redis';
import type { RedisClientType, RedisZAddMember } from 'redis';

/**
 * Redis database implementation
 */
export class RedisDatabase implements IDatabase {
  private client: RedisClientType | undefined;
  private url: string;
  private keyPrefix: string;
  private initialized = false;

  constructor() {
    this.url = getConfig<string>('memory.database.redis.url', '');
    this.keyPrefix = getConfig<string>('memory.database.redis.keyPrefix', 'codessa:');
  }

  /**
     * Initialize the database
     */
  public async initialize(): Promise<void> {
    try {
      if (!this.url) {
        throw new Error('Redis URL not configured');
      }
            
      // Create Redis client
      this.client = createClient({
        url: this.url
      });
            
      // Connect to Redis
      await this.client.connect();
            
      this.initialized = true;
      logger.info('Redis database initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Redis database:', error);
      throw error;
    }
  }

  /**
     * Get full key with prefix
     */
  private getFullKey(collection: string, id: string): string {
    return `${this.keyPrefix}${collection}:${id}`;
  }

  /**
     * Add a record
     */
  public async addRecord(collection: string, record: Record<string, unknown>): Promise<string> {
    if (!this.client || !this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      // Extract and type the record fields
      const id = record.id as string;
      const timestamp = record.timestamp as number | undefined;
      const metadata = record.metadata as Record<string, unknown> | undefined;

      const key = this.getFullKey(collection, id);

      // Store record
      await this.client.set(key, JSON.stringify(record));

      // Add to collection index
      await this.client.sAdd(`${this.keyPrefix}${collection}:ids`, id);

      // Add to timestamp index
      if (timestamp) {
        const member: RedisZAddMember = {
          score: timestamp,
          value: id
        };
        await this.client.zAdd(`${this.keyPrefix}${collection}:timestamps`, member);
      }

      // Add to metadata indexes
      if (metadata) {
        // Add to source index
        const source = metadata.source as string | undefined;
        if (source) {
          await this.client.sAdd(`${this.keyPrefix}${collection}:source:${source}`, id);
        }
                
        // Add to type index
        const type = metadata.type as string | undefined;
        if (type) {
          await this.client.sAdd(`${this.keyPrefix}${collection}:type:${type}`, id);
        }

        // Add to tag indexes
        const tags = metadata.tags as string[] | undefined;
        if (tags && Array.isArray(tags)) {
          for (const tag of tags) {
            await this.client.sAdd(`${this.keyPrefix}${collection}:tag:${String(tag)}`, id);
          }
        }
      }
            
      logger.debug(`Added record with ID ${id} to collection ${collection}`);
      return id;
    } catch (error) {
      logger.error(`Failed to add record to collection ${collection}:`, error);
      throw error;
    }
  }

  /**
     * Get a record by ID
     */
  public async getRecord(collection: string, id: string): Promise<Record<string, unknown> | undefined> {
    if (!this.client || !this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      const key = this.getFullKey(collection, id);
            
      // Get record
      const data = await this.client.get(key);
            
      if (!data) {
        return undefined;
      }
            
      return JSON.parse(data);
    } catch (error) {
      logger.error(`Failed to get record with ID ${id} from collection ${collection}:`, error);
      return undefined;
    }
  }

  /**
     * Update a record
     */
  public async updateRecord(collection: string, id: string, record: Record<string, unknown>): Promise<boolean> {
    if (!this.client || !this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      // Extract and type the record fields
      const timestamp = record.timestamp as number | undefined;
      const metadata = record.metadata as Record<string, unknown> | undefined;

      const key = this.getFullKey(collection, id);

      // Check if record exists
      const exists = await this.client.exists(key);

      if (!exists) {
        return false;
      }

      // Get existing record
      const existingData = await this.client.get(key);
      const existingRecord = existingData ? JSON.parse(existingData) as Record<string, unknown> : {};
            
      // Delete existing metadata indexes
      const existingMetadata = existingRecord.metadata as Record<string, unknown> | undefined;
      if (existingMetadata) {
        // Delete from source index
        const existingSource = existingMetadata.source as string | undefined;
        if (existingSource) {
          await this.client.sRem(`${this.keyPrefix}${collection}:source:${existingSource}`, id);
        }

        // Delete from type index
        const existingType = existingMetadata.type as string | undefined;
        if (existingType) {
          await this.client.sRem(`${this.keyPrefix}${collection}:type:${existingType}`, id);
        }

        // Delete from tag indexes
        const existingTags = existingMetadata.tags as string[] | undefined;
        if (existingTags && Array.isArray(existingTags)) {
          for (const tag of existingTags) {
            await this.client.sRem(`${this.keyPrefix}${collection}:tag:${String(tag)}`, id);
          }
        }
      }
            
      // Update timestamp index
      const existingTimestamp = existingRecord.timestamp as number | undefined;
      if (existingTimestamp && timestamp && existingTimestamp !== timestamp) {
        await this.client.zRem(`${this.keyPrefix}${collection}:timestamps`, id);
        const member: RedisZAddMember = {
          score: timestamp,
          value: id
        };
        await this.client.zAdd(`${this.keyPrefix}${collection}:timestamps`, member);
      }
            
      // Store updated record
      await this.client.set(key, JSON.stringify(record));
            
      // Add to metadata indexes
      if (metadata) {
        // Add to source index
        const source = metadata.source as string | undefined;
        if (source) {
          await this.client.sAdd(`${this.keyPrefix}${collection}:source:${source}`, id);
        }

        // Add to type index
        const type = metadata.type as string | undefined;
        if (type) {
          await this.client.sAdd(`${this.keyPrefix}${collection}:type:${type}`, id);
        }

        // Add to tag indexes
        const tags = metadata.tags as string[] | undefined;
        if (tags && Array.isArray(tags)) {
          for (const tag of tags) {
            await this.client.sAdd(`${this.keyPrefix}${collection}:tag:${String(tag)}`, id);
          }
        }
      }
            
      logger.debug(`Updated record with ID ${id} in collection ${collection}`);
      return true;
    } catch (error) {
      logger.error(`Failed to update record with ID ${id} in collection ${collection}:`, error);
      return false;
    }
  }

  /**
     * Delete a record
     */
  public async deleteRecord(collection: string, id: string): Promise<boolean> {
    if (!this.client || !this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      const key = this.getFullKey(collection, id);
            
      // Get existing record
      const existingData = await this.client.get(key);
            
      if (!existingData) {
        return false;
      }
            
      const existingRecord = JSON.parse(existingData);
            
      // Delete from collection index
      await this.client.sRem(`${this.keyPrefix}${collection}:ids`, id);
            
      // Delete from timestamp index
      await this.client.zRem(`${this.keyPrefix}${collection}:timestamps`, id);
            
      // Delete from metadata indexes
      if (existingRecord.metadata) {
        // Delete from source index
        if (existingRecord.metadata.source) {
          await this.client.sRem(`${this.keyPrefix}${collection}:source:${existingRecord.metadata.source}`, id);
        }
                
        // Delete from type index
        if (existingRecord.metadata.type) {
          await this.client.sRem(`${this.keyPrefix}${collection}:type:${existingRecord.metadata.type}`, id);
        }
                
        // Delete from tag indexes
        if (existingRecord.metadata.tags && Array.isArray(existingRecord.metadata.tags)) {
          for (const tag of existingRecord.metadata.tags) {
            await this.client.sRem(`${this.keyPrefix}${collection}:tag:${tag}`, id);
          }
        }
      }
            
      // Delete record
      await this.client.del(key);
            
      logger.debug(`Deleted record with ID ${id} from collection ${collection}`);
      return true;
    } catch (error) {
      logger.error(`Failed to delete record with ID ${id} from collection ${collection}:`, error);
      return false;
    }
  }

  /**
     * Query records
     */
  public async queryRecords(collection: string, query: Record<string, unknown>, limit?: number): Promise<Record<string, unknown>[]> {
    if (!this.client || !this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      // Get all IDs in the collection
      let ids: string[] = [];
            
      // Handle timestamp range queries
      const timestampQuery = query.timestamp as { $gte?: number; $lte?: number } | undefined;
      if (timestampQuery) {
        const min = timestampQuery.$gte || '-inf';
        const max = timestampQuery.$lte || '+inf';
                
        // Get IDs in timestamp range
        const rangeIds = await this.client.zRange(`${this.keyPrefix}${collection}:timestamps`, min, max, { BY: 'SCORE' });
                
        if (rangeIds.length === 0) {
          return [];
        }
                
        ids = rangeIds;
      } else {
        // Get all IDs
        ids = await this.client.sMembers(`${this.keyPrefix}${collection}:ids`);
                
        if (ids.length === 0) {
          return [];
        }
      }
            
      // Handle metadata filters
      for (const key in query) {
        if (key === 'timestamp' || key === '$text') {
          continue;
        }
                
        if (key.startsWith('metadata.')) {
          const metadataKey = key.substring(9);
                    
          if (metadataKey === 'source') {
            // Filter by source
            const sourceIds = await this.client.sMembers(`${this.keyPrefix}${collection}:source:${query[key]}`);
            ids = ids.filter(id => sourceIds.includes(id));
          } else if (metadataKey === 'type') {
            // Filter by type
            const typeIds = await this.client.sMembers(`${this.keyPrefix}${collection}:type:${query[key]}`);
            ids = ids.filter(id => typeIds.includes(id));
          } else if (metadataKey === 'tags') {
            // Filter by tags
            const tagsQuery = query[key] as { $all?: string[] } | undefined;
            if (tagsQuery?.$all) {
              for (const tag of tagsQuery.$all) {
                const tagIds = await this.client.sMembers(`${this.keyPrefix}${collection}:tag:${String(tag)}`);
                ids = ids.filter(id => tagIds.includes(id));
              }
            }
          }
        }
      }
            
      if (ids.length === 0) {
        return [];
      }
            
      // Get records
      const records: Record<string, unknown>[] = [];
            
      // Apply limit
      if (limit && ids.length > limit) {
        ids = ids.slice(0, limit);
      }
            
      // Get records
      for (const id of ids) {
        const record = await this.getRecord(collection, id);
                
        if (record) {
          // Handle text search
          const textQuery = query.$text as { $search: string } | undefined;
          if (textQuery?.$search) {
            const recordContent = (record as Record<string, unknown>).content as string;
            if (!recordContent.toLowerCase().includes(textQuery.$search.toLowerCase())) {
              continue;
            }
          }
                    
          records.push(record);
        }
      }
            
      // Sort by timestamp
      records.sort((a, b) => {
        const aTimestamp = (a.timestamp as number) || 0;
        const bTimestamp = (b.timestamp as number) || 0;
        return bTimestamp - aTimestamp;
      });
            
      return records;
    } catch (error) {
      logger.error(`Failed to query records from collection ${collection}:`, error);
      return [];
    }
  }

  /**
     * Clear all records in a collection
     */
  public async clearCollection(collection: string): Promise<void> {
    if (!this.client || !this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      // Get all IDs in the collection
      const ids = await this.client.sMembers(`${this.keyPrefix}${collection}:ids`);
            
      // Delete all records
      for (const id of ids) {
        await this.deleteRecord(collection, id);
      }
            
      // Delete collection indexes
      await this.client.del(`${this.keyPrefix}${collection}:ids`);
      await this.client.del(`${this.keyPrefix}${collection}:timestamps`);
            
      // Find and delete all metadata indexes
      const keys = await this.client.keys(`${this.keyPrefix}${collection}:*`);
            
      if (keys.length > 0 && this.client) {
        // Delete each key one by one since del() only accepts a single key
        const client = this.client;
        await Promise.all(keys.map(key => client.del(key)));
      }
            
      logger.info(`Cleared collection ${collection}`);
    } catch (error) {
      logger.error(`Failed to clear collection ${collection}:`, error);
      throw error;
    }
  }
}
