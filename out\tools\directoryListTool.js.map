{"version": 3, "file": "directoryListTool.js", "sourceRoot": "", "sources": ["../../src/tools/directoryListTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,6BAAwB;AAExB,MAAa,iBAAiB;IACnB,EAAE,GAAG,SAAS,CAAC;IACf,IAAI,GAAG,gBAAgB,CAAC;IACxB,WAAW,GAAG,+DAA+D,CAAC;IAC9E,IAAI,GAAG,eAAe,CAAC;IACvB,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QACzB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,iEAAiE,CAAC;KAChG,CAAC,CAAC;IACM,OAAO,GAAwB,EAAE,CAAC;IAE3C,KAAK,CAAC,OAAO,CAAC,KAAgB,EAAE,QAAuB;QACrD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QACxC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAChF,CAAC;QACD,IAAI,MAA8B,CAAC;QACnC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAC/D,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACtC,IAAI,GAAG,CAAC,MAAM;oBAAE,MAAM,GAAG,GAAG,CAAC;;oBACxB,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAC5D,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACtC,IAAI,GAAG,CAAC,MAAM;oBAAE,MAAM,GAAG,GAAG,CAAC;YAC/B,CAAC;YAAC,MAAM,CAAC;gBACP,4CAA4C;YAC9C,CAAC;QACH,CAAC;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qCAAqC,OAAO,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QACrG,CAAC;QACD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAChE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC5C,IAAI;gBACJ,IAAI,EAAE,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;aAChE,CAAC,CAAC,CAAC;YACJ,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC3G,CAAC;IACH,CAAC;CACF;AA/CD,8CA+CC;AAEY,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { z } from 'zod';\n\nexport class DirectoryListTool implements ITool {\n  readonly id = 'listDir';\n  readonly name = 'List Directory';\n  readonly description = 'Lists the contents of a directory (files and subdirectories).';\n  readonly type = 'single-action';\n  readonly schema = z.object({\n    dirPath: z.string().describe('Path to the directory (relative to workspace root or absolute).')\n  });\n  readonly actions: Record<string, any> = {};\n\n  async execute(input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const dirPath = input.dirPath as string;\n    if (!dirPath) {\n      return { success: false, error: '\\'dirPath\\' is required.', toolId: this.id };\n    }\n    let dirUri: vscode.Uri | undefined;\n    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n      const workspaceRoot = vscode.workspace.workspaceFolders[0].uri;\n      try {\n        const uri = vscode.Uri.parse(dirPath);\n        if (uri.scheme) dirUri = uri;\n        else dirUri = vscode.Uri.joinPath(workspaceRoot, dirPath);\n      } catch {\n        dirUri = vscode.Uri.joinPath(workspaceRoot, dirPath);\n      }\n    } else {\n      try {\n        const uri = vscode.Uri.parse(dirPath);\n        if (uri.scheme) dirUri = uri;\n      } catch {\n        // Handle empty catch block by doing nothing\n      }\n    }\n    if (!dirUri) {\n      return { success: false, error: `Could not resolve directory path: ${dirPath}.`, toolId: this.id };\n    }\n    try {\n      const entries = await vscode.workspace.fs.readDirectory(dirUri);\n      const result = entries.map(([name, type]) => ({\n        name,\n        type: type === vscode.FileType.Directory ? 'directory' : 'file'\n      }));\n      return { success: true, output: result, toolId: this.id };\n    } catch (error: any) {\n      return { success: false, error: `Failed to list directory: ${error.message || error}`, toolId: this.id };\n    }\n  }\n}\n\nexport const directoryListTool = new DirectoryListTool();\n"]}