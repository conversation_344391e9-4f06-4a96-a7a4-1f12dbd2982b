/**
 * Checkpoint Workflow
 *
 * This module provides a workflow for managing code checkpoints:
 * - Creating checkpoints before making changes
 * - Rolling back to previous checkpoints
 */

import { ITool } from '../../tools/tool.ts.backup';
import { ToolMessage, StructuredTool } from 'src/managers';
import { GraphDefinition, GraphState, GraphNode } from './types';
import { Logger } from '../../logger';

// Import the CheckpointManager from its new location
import { checkpointManager } from '../../checkpoint';

/**
 * Creates a checkpoint workflow
 */
export function createCheckpointWorkflow(options: {
  name?: string;
  description?: string;
  tools?: (ITool | StructuredTool)[];
}): GraphDefinition {
  const name = options.name || 'Checkpoint Workflow';
  const description = options.description || 'Workflow for managing code checkpoints';

  // Create a simple workflow with input and output nodes
  const workflow: GraphDefinition = {
    id: `checkpoint-workflow-${Date.now()}`,
    name,
    description,
    version: '1.0.0',
    operationMode: 'checkpoint',
    type: 'checkpoint',
    nodes: [
      {
        id: 'input',
        type: 'input',
        name: 'Input',
        label: 'Input'
      },
      {
        id: 'output',
        type: 'output',
        name: 'Output',
        label: 'Output'
      }
    ],
    edges: [
      {
        source: 'input',
        target: 'output',
        type: 'checkpoint',
        name: 'Default Flow'
      }
    ],
    startNodeId: 'input'
  };

  // Add specialized nodes for checkpoint management
  const createCheckpointNode: GraphNode = {
    id: 'create_checkpoint',
    type: 'tool',
    name: 'Create Checkpoint',
    label: 'Create Checkpoint',
    execute: async (state: any): Promise<Partial<GraphState>> => {
      Logger.instance.info('Creating checkpoint');

      const description = state.inputs?.description || 'Automatic checkpoint';
      const files = state.inputs?.files || [];

      try {
        const checkpoint = await checkpointManager.createCheckpoint(
          description,
          Array.isArray(files) ? files : [files],
          state.inputs?.metadata || {}
        );

        return {
          outputs: {
            create_checkpoint: checkpoint
          },
          messages: [
            new ToolMessage(
              'create_checkpoint',
              {},
              `Checkpoint created: ${checkpoint.id} - ${description}`
            )
          ]
        };
      } catch (error: any) {
        return {
          outputs: {
            create_checkpoint: { error: error.message }
          },
          messages: [
            new ToolMessage(
              'create_checkpoint',
              {},
              `Error creating checkpoint: ${error.message}`
            )
          ]
        };
      }
    }
  };

  const rollbackCheckpointNode: GraphNode = {
    id: 'rollback_checkpoint',
    type: 'tool',
    name: 'Rollback to Checkpoint',
    label: 'Rollback to Checkpoint',
    execute: async (state: any): Promise<Partial<GraphState>> => {
      Logger.instance.info('Rolling back to checkpoint');

      const checkpointId = state.inputs?.checkpointId;

      if (!checkpointId) {
        return {
          outputs: {
            rollback_checkpoint: { error: 'No checkpoint ID provided' }
          },
          messages: [
            new ToolMessage(
              'rollback_checkpoint',
              {},
              'Error: No checkpoint ID provided'
            )
          ]
        };
      }

      try {
        const success = await checkpointManager.rollbackToCheckpoint(checkpointId);

        if (success) {
          return {
            outputs: {
              rollback_checkpoint: { success: true }
            },
            messages: [
              new ToolMessage(
                'rollback_checkpoint',
                {},
                `Successfully rolled back to checkpoint: ${checkpointId}`
              )
            ]
          };
        } else {
          return {
            outputs: {
              rollback_checkpoint: { error: 'Rollback failed' }
            },
            messages: [
              new ToolMessage(
                'rollback_checkpoint',
                {},
                'Error: Rollback failed'
              )
            ]
          };
        }
      } catch (error: any) {
        return {
          outputs: {
            rollback_checkpoint: { error: error.message }
          },
          messages: [
            new ToolMessage(
              'rollback_checkpoint',
              {},
              `Error rolling back to checkpoint: ${error.message}`
            )
          ]
        };
      }
    }
  };

  // Add nodes to workflow
  workflow.nodes.push(createCheckpointNode);
  workflow.nodes.push(rollbackCheckpointNode);

  // Add edges to connect the nodes
  workflow.edges = [
    {
      source: 'input',
      target: 'output',
      type: 'checkpoint',
      name: 'Default Flow'
    },
    {
      source: 'input',
      target: 'create_checkpoint',
      type: 'conditional',
      name: 'To Create Checkpoint',
      condition: async (state: any) => Promise.resolve(state.inputs?.action === 'create_checkpoint'),
      conditionType: 'custom'
    },
    {
      source: 'input',
      target: 'rollback_checkpoint',
      type: 'conditional',
      name: 'To Rollback Checkpoint',
      condition: async (state: any) => Promise.resolve(state.inputs?.action === 'rollback_checkpoint'),
      conditionType: 'custom'
    },
    {
      source: 'create_checkpoint',
      target: 'output',
      type: 'checkpoint',
      name: 'From Create Checkpoint To Output'
    },
    {
      source: 'rollback_checkpoint',
      target: 'output',
      type: 'checkpoint',
      name: 'From Rollback Checkpoint To Output'
    }
  ];

  return workflow;
}
