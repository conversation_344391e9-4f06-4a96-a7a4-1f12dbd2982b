{"version": 3, "file": "googleAIProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/googleAIProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uDAAoD;AAEpD,yCAAiD;AACjD,yCAAsC;AACtC,+CAAiC;AACjC,qEAAqE;AAErE,MAAa,gBAAiB,SAAQ,iCAAe;IAC1C,UAAU,GAAG,UAAU,CAAC;IACxB,WAAW,GAAG,WAAW,CAAC;IAC1B,WAAW,GAAG,yBAAyB,CAAC;IACxC,OAAO,GAAG,wBAAwB,CAAC;IACnC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,KAAK,CAAC;IACtC,eAAe,GAAG,2CAA2C,CAAC;IAC9D,YAAY,GAAG,YAAY,CAAC;IAE7B,MAAM,GAAkB,IAAI,CAAC;IAErC,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,4BAA4B,CAAC,EAAE,CAAC;gBACzD,eAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBACxE,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,MAAM,GAAG,IAAA,0BAAiB,GAAE,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,OAA0B,EAC1B,kBAA6C;QAE7C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,0FAA0F;aAClG,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,eAAe,kBAAkB,IAAI,CAAC,YAAY,kBAAkB,EAAE;gBACzG,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;iBACzC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAE,OAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAE,OAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBACnF,GAAG,OAAO;iBACX,CAAC;aACH,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBAC/L,OAAO;oBACL,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;oBACjD,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,MAAM;oBACvD,KAAK,EAAE,SAAS;iBACjB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,qCAAqC;iBACrG,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,oCAAoC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aAC/F,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,wBAAwB;QACxB,OAAO;YACL,YAAY;YACZ,mBAAmB;SACpB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,wBAAwB;QACxB,MAAM,MAAM,GAAG;YACb,YAAY;YACZ,mBAAmB;SACpB,CAAC;QACF,eAAM,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;QACvE,OAAO,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,sDAAsD;aAChE,CAAC;QACJ,CAAC;QAED,wFAAwF;QACxF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,UAAU,OAAO,kCAAkC;aAC7D,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,gDAAgD,OAAO,gDAAgD;SACjH,CAAC;IACJ,CAAC;IAED,qEAAqE;IAErE;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,wBAAwB;gBACrC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,6CAA6C;gBAC1D,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AA7JD,4CA6JC", "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './baseLL<PERSON>rovider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { getGoogleAIApiKey } from '../../config';\nimport { logger } from '../../logger';\nimport * as vscode from 'vscode';\n// Gemini AI SDK import removed. Using direct REST API calls instead.\n\nexport class GoogleAIProvider extends BaseLL<PERSON>rovider {\n  readonly providerId = 'googleai';\n  readonly displayName = 'Google AI';\n  readonly description = 'Google Gemini AI models';\n  readonly website = 'https://ai.google.dev/';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = false;\n  readonly defaultEndpoint = 'https://generativelanguage.googleapis.com';\n  readonly defaultModel = 'gemini-pro';\n\n  private apiKey: string | null = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.providers.googleai')) {\n        logger.info('Google AI configuration changed, re-initializing client.');\n        this.initializeClient();\n      }\n    });\n  }\n\n  private initializeClient() {\n    this.apiKey = getGoogleAIApiKey();\n    if (!this.apiKey) {\n      logger.warn('Google AI API key not set.');\n    } else {\n      logger.info('Google AI credentials initialized.');\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.apiKey;\n  }\n\n  async generate(\n    _params: LLMGenerateParams,\n    _cancellationToken?: vscode.CancellationToken\n  ): Promise<LLMGenerateResult> {\n    if (!this.isConfigured()) {\n      return {\n        content: '',\n        error: 'Google AI provider not configured (API key missing). Please set the API key in settings.'\n      };\n    }\n\n    try {\n      // Example REST API call to Google Gemini AI\n      const fetch = require('node-fetch');\n      const response = await fetch(`${this.defaultEndpoint}/v1beta/models/${this.defaultModel}:generateContent`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`\n        },\n        body: JSON.stringify({\n          contents: Array.isArray((_params as any).contents) ? (_params as any).contents : [],\n          ..._params\n        })\n      });\n      const data = await response.json();\n      if (response.ok && data.candidates && data.candidates.length > 0 && data.candidates[0].content && Array.isArray(data.candidates[0].content.parts) && data.candidates[0].content.parts[0]?.text) {\n        return {\n          content: data.candidates[0].content.parts[0].text,\n          finishReason: data.candidates[0].finishReason || 'stop',\n          error: undefined\n        };\n      } else {\n        return {\n          content: '',\n          error: data.error && data.error.message ? data.error.message : 'Unknown error from Google Gemini AI'\n        };\n      }\n    } catch (err: any) {\n      return {\n        content: '',\n        error: 'Google Gemini AI API call failed: ' + (err && err.message ? err.message : String(err))\n      };\n    }\n  }\n\n  async getAvailableModels(): Promise<string[]> {\n    if (!this.isConfigured()) {\n      return [];\n    }\n    // Return default models\n    return [\n      'gemini-pro',\n      'gemini-pro-vision'\n    ];\n  }\n\n  async listModels(): Promise<{id: string}[]> {\n    if (!this.isConfigured()) {\n      return [];\n    }\n    // Return default models\n    const models = [\n      'gemini-pro',\n      'gemini-pro-vision'\n    ];\n    logger.info(`Provider googleai has ${models.length} models available`);\n    return models.map(id => ({ id }));\n  }\n\n  /**\n     * Test connection to Google AI\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.isConfigured()) {\n      return {\n        success: false,\n        message: 'Google AI not configured. Please check your API key.'\n      };\n    }\n\n    // Since we don't have a real implementation yet, just check if the model is in our list\n    const availableModels = await this.getAvailableModels();\n    if (!availableModels.includes(modelId)) {\n      return {\n        success: false,\n        message: `Model '${modelId}' not found in available models.`\n      };\n    }\n\n    return {\n      success: true,\n      message: `Google AI provider is configured with model '${modelId}'. Note: This is a placeholder implementation.`\n    };\n  }\n\n  // Use the parent class implementation for getConfig and updateConfig\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your Google AI API key',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use (e.g., gemini-pro)',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n\n\n"]}