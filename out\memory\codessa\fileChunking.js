"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileChunkingService = exports.FixedSizeBinaryChunker = exports.RecursiveTextChunker = exports.BinaryExtractor = exports.TextExtractor = exports.LocalFileSource = exports.UniversalChunkingService = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const uuid_1 = require("uuid");
const p_limit_1 = __importDefault(require("p-limit")); // For concurrent processing
const glob_1 = require("glob");
// Core Polyfills (Using relative paths)
const corePolyfill_1 = require("../../agents/workflows/corePolyfill");
// Document might be used by specific chunkers or extractors later
// import { Document } from '../../workflows/corePolyfill';
// Local project imports (Ensure these paths are correct)
const logger_1 = require("../../logger");
const config_1 = require("../../config");
// === Core Service ===
class UniversalChunkingService {
    config;
    extractors;
    chunkingStrategies;
    memoryStorer;
    constructor(config, extractors, chunkingStrategies, memoryStorer) {
        this.config = this.validateConfig(config);
        this.extractors = extractors;
        this.chunkingStrategies = chunkingStrategies;
        this.memoryStorer = memoryStorer;
        if (this.extractors.length === 0) {
            throw new Error('UniversalChunkingService requires at least one IContentExtractor.');
        }
        if (this.chunkingStrategies.length === 0) {
            throw new Error('UniversalChunkingService requires at least one IChunkingStrategy.');
        }
        logger_1.logger.info('UniversalChunkingService initialized.');
    }
    validateConfig(config) {
        // Add thorough validation for all config options here
        const validatedConfig = { ...config };
        validatedConfig.defaultChunkSize = Math.max(50, config.defaultChunkSize ?? 1000);
        validatedConfig.defaultChunkOverlap = Math.max(0, config.defaultChunkOverlap ?? 200);
        validatedConfig.maxChunksPerSource = Math.max(1, config.maxChunksPerSource ?? 500);
        validatedConfig.concurrencyLimit = Math.max(1, config.concurrencyLimit ?? 5);
        validatedConfig.fixedBinaryChunkSize = Math.max(128, config.fixedBinaryChunkSize ?? 4096);
        validatedConfig.localFile = config.localFile ?? { maxFileSizeMB: 100, allowedExtensions: [], excludedPatterns: [] };
        validatedConfig.localFile.maxFileSizeMB = Math.max(0.1, validatedConfig.localFile.maxFileSizeMB ?? 100);
        // Set default values for missing properties
        validatedConfig.textChunkingStrategy = config.textChunkingStrategy ?? 'recursive';
        validatedConfig.recursiveSeparators = config.recursiveSeparators ?? {};
        validatedConfig.defaultTextSeparators = config.defaultTextSeparators ?? ['\n\n', '\n', '. ', '? ', '! ', ' ', ''];
        validatedConfig.binaryChunkingStrategy = config.binaryChunkingStrategy ?? 'fixed';
        // Ensure overlap is less than chunk size
        validatedConfig.defaultChunkOverlap = Math.min(validatedConfig.defaultChunkOverlap, validatedConfig.defaultChunkSize - 10);
        return validatedConfig;
    }
    /**
       * Processes a batch of data sources concurrently.
       * @param dataSources An array of IDataSource instances.
       * @param progressCallback Optional callback for progress updates.
       * @returns A promise resolving to a BatchProcessResult.
       */
    async processBatch(dataSources, progressCallback) {
        const totalSources = dataSources.length;
        logger_1.logger.info(`Starting batch processing for ${totalSources} data sources with concurrency ${this.config.concurrencyLimit}.`);
        const limit = (0, p_limit_1.default)(this.config.concurrencyLimit);
        const results = [];
        let processedCount = 0;
        const processingPromises = dataSources.map((source, index) => limit(async () => {
            const uri = source.getUri();
            logger_1.logger.debug(`[Batch ${index + 1}/${totalSources}] Processing source: ${uri}`);
            if (progressCallback) {
                progressCallback(processedCount, totalSources, uri);
            }
            const result = await this.processDataSource(source);
            results.push(result);
            processedCount++;
            if (progressCallback) {
                // Update progress after completion
                progressCallback(processedCount, totalSources);
            }
            logger_1.logger.debug(`[Batch ${index + 1}/${totalSources}] Finished source: ${uri} - Status: ${result.status}`);
            return result; // p-limit expects a return value
        }));
        // Wait for all promises to settle
        await Promise.allSettled(processingPromises);
        // Aggregate results
        const batchResult = {
            totalSources,
            processedSources: results.filter(r => r.status === 'processed').length,
            skippedSources: results.filter(r => r.status === 'skipped').length,
            failedSources: results.filter(r => r.status === 'error').length,
            totalChunksAdded: results.reduce((sum, r) => sum + r.chunkCount, 0),
            results: results,
            errors: results.filter(r => r.status === 'error').map(r => ({ uri: r.sourceUri, error: r.message ?? 'Unknown error' })),
        };
        logger_1.logger.info(`Batch processing completed. Processed: ${batchResult.processedSources}, Skipped: ${batchResult.skippedSources}, Failed: ${batchResult.failedSources}, Chunks Added: ${batchResult.totalChunksAdded}`);
        if (batchResult.failedSources > 0) {
            logger_1.logger.warn(`Batch processing encountered ${batchResult.failedSources} errors.`);
        }
        return batchResult;
    }
    /**
       * Processes a single data source: gets metadata, extracts content, chunks, and stores memory entries.
       * @param dataSource The IDataSource instance to process.
       * @returns A promise resolving to a ProcessResult.
       */
    async processDataSource(dataSource) {
        const sourceUri = dataSource.getUri();
        let metadata;
        let stream;
        let result = {
            sourceUri,
            success: false,
            status: 'error', // Default to error
            chunkCount: 0,
            addedEntries: [],
            message: 'Processing did not complete.',
        };
        try {
            // 1. Get Metadata
            logger_1.logger.debug(`[${sourceUri}] Getting metadata...`);
            metadata = await dataSource.getMetadata();
            result.metadata = metadata; // Store metadata in result
            // 2. Check Preconditions (e.g., size limits for specific source types)
            if (metadata.sourceType === 'local_file' && metadata.size) {
                const fileSizeMB = metadata.size / (1024 * 1024);
                if (fileSizeMB > this.config.localFile.maxFileSizeMB) {
                    logger_1.logger.warn(`[${sourceUri}] Skipping: File size ${fileSizeMB.toFixed(2)}MB exceeds limit ${this.config.localFile.maxFileSizeMB}MB.`);
                    return { ...result, success: false, status: 'skipped', message: `File size exceeds limit (${this.config.localFile.maxFileSizeMB}MB)` };
                }
                if (metadata.size === 0) {
                    logger_1.logger.info(`[${sourceUri}] Skipping: File is empty.`);
                    return { ...result, success: false, status: 'skipped', message: 'Source is empty.' };
                }
            }
            // Add more source-specific precondition checks here
            // 3. Find Suitable Extractor
            logger_1.logger.debug(`[${sourceUri}] Finding content extractor...`);
            const extractor = this.findExtractor(metadata);
            if (!extractor) {
                logger_1.logger.warn(`[${sourceUri}] Skipping: No suitable content extractor found for metadata:`, metadata);
                return { ...result, success: false, status: 'skipped', message: 'No suitable content extractor found.' };
            }
            logger_1.logger.debug(`[${sourceUri}] Using extractor: ${extractor.getName()}`);
            // 4. Get Content Stream
            logger_1.logger.debug(`[${sourceUri}] Getting content stream...`);
            stream = await dataSource.getContentStream();
            // 5. Extract Content
            logger_1.logger.debug(`[${sourceUri}] Extracting content...`);
            const extractedContent = await extractor.extract(stream, metadata);
            result.extractedContentType = extractedContent.contentType; // Store content type
            // Handle empty extracted content
            if ((typeof extractedContent.content === 'string' && extractedContent.content.length === 0) ||
                (Buffer.isBuffer(extractedContent.content) && extractedContent.content.length === 0)) {
                logger_1.logger.info(`[${sourceUri}] Skipping: Extracted content is empty.`);
                // Consider this success, but skipped chunking
                return { ...result, success: true, status: 'skipped', message: 'Extracted content is empty.', chunkCount: 0 };
            }
            // 6. Find Suitable Chunking Strategy
            logger_1.logger.debug(`[${sourceUri}] Finding chunking strategy for content type: ${extractedContent.contentType}...`);
            const chunker = this.findChunkingStrategy(extractedContent.contentType);
            if (!chunker) {
                logger_1.logger.warn(`[${sourceUri}] Skipping: No suitable chunking strategy found for content type: ${extractedContent.contentType}`);
                return { ...result, success: false, status: 'skipped', message: `No chunking strategy for ${extractedContent.contentType}` };
            }
            logger_1.logger.debug(`[${sourceUri}] Using chunking strategy: ${chunker.getName()}`);
            result.chunkingStrategyUsed = chunker.getName(); // Store strategy used
            // 7. Chunk Content and Store Memory Entries
            logger_1.logger.debug(`[${sourceUri}] Chunking content and storing entries...`);
            let chunkIndex = 0;
            const addedEntries = [];
            for await (const chunk of chunker.chunk(extractedContent, this.config, metadata)) {
                if (chunkIndex >= this.config.maxChunksPerSource) {
                    logger_1.logger.warn(`[${sourceUri}] Reached maximum chunk limit (${this.config.maxChunksPerSource}). Stopping chunking for this source.`);
                    break;
                }
                // Simple check for effectively empty chunks (whitespace string or empty buffer)
                const isEmptyChunk = (typeof chunk.content === 'string' && chunk.content.trim().length === 0) ||
                    (Buffer.isBuffer(chunk.content) && chunk.content.length === 0);
                if (isEmptyChunk) {
                    logger_1.logger.debug(`[${sourceUri}] Skipping empty chunk at index ${chunkIndex}.`);
                    continue; // Don't increment chunkIndex for skipped empty chunks
                }
                const entryMetadata = this.prepareMemoryEntryMetadata(metadata, extractedContent, chunk.metadata, chunkIndex, sourceUri);
                try {
                    const addedEntry = await this.memoryStorer.storeMemory({
                        content: chunk.content, // Pass string or Buffer directly
                        metadata: entryMetadata,
                    });
                    // Ensure the returned entry has an ID and merged metadata
                    if (!addedEntry.id) {
                        logger_1.logger.warn(`[${sourceUri}] Memory storer did not return an ID for chunk ${chunkIndex}. Assigning fallback.`);
                        const chunkId = entryMetadata.chunkId;
                        addedEntry.id = (typeof chunkId === 'string' ? chunkId : `chunk_${(0, uuid_1.v4)()}`); // Use generated chunkId or UUID
                    }
                    addedEntry.metadata = { ...entryMetadata, ...addedEntry.metadata }; // Merge metadata
                    addedEntries.push(addedEntry);
                    chunkIndex++; // Increment only for successfully added non-empty chunks
                }
                catch (storeError) {
                    logger_1.logger.error(`[${sourceUri}] Failed to store chunk ${chunkIndex}:`, storeError);
                    // Decide on error strategy: stop processing this source or continue?
                    // For now, stop processing this source on storage error.
                    const errorMessage = storeError instanceof Error ? storeError.message : String(storeError);
                    throw new Error(`Failed to store chunk ${chunkIndex}: ${errorMessage}`);
                }
            }
            logger_1.logger.info(`[${sourceUri}] Successfully processed and stored ${chunkIndex} chunks.`);
            result = {
                ...result,
                success: true,
                status: 'processed',
                chunkCount: chunkIndex,
                addedEntries: addedEntries,
                message: `Processed successfully with ${chunkIndex} chunks.`,
            };
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.logger.error(`[${sourceUri}] Failed to process source:`, { message: errorMessage, stack: errorStack });
            // Ensure result reflects the error state
            result = {
                ...result, // Keep any previously set fields like metadata if available
                success: false,
                status: 'error',
                message: errorMessage || 'Unknown processing error.',
            };
            return result;
        }
        finally {
            // 8. Cleanup Data Source (if applicable)
            if (dataSource.cleanup) {
                try {
                    logger_1.logger.debug(`[${sourceUri}] Cleaning up data source...`);
                    await dataSource.cleanup();
                }
                catch (cleanupError) {
                    logger_1.logger.warn(`[${sourceUri}] Error during data source cleanup:`, cleanupError);
                }
            }
            // Ensure stream is destroyed if it exists and wasn't fully consumed or errored
            if (stream && !stream.destroyed) {
                stream.destroy();
            }
        }
    }
    findExtractor(metadata) {
        // Find the first extractor that supports the metadata
        return this.extractors.find(e => e.supports(metadata));
    }
    findChunkingStrategy(contentType) {
        // Find the first strategy that supports the content type
        return this.chunkingStrategies.find(cs => cs.supports(contentType));
    }
    prepareMemoryEntryMetadata(sourceMetadata, extractedContent, chunkMetadata, chunkIndex, sourceUri) {
        // Combine metadata from all stages
        const combined = {
            // Source Info
            sourceUri: sourceUri,
            sourceType: sourceMetadata.sourceType,
            fileName: sourceMetadata.fileName,
            fileExtension: sourceMetadata.fileName ? path.extname(sourceMetadata.fileName).toLowerCase() : undefined,
            fileSize: sourceMetadata.size,
            fileMtime: sourceMetadata.lastModified?.toISOString(),
            // Extraction Info
            extractedContentType: extractedContent.contentType,
            ...extractedContent.metadata, // Metadata from the extractor (e.g., page number)
            // Chunking Info
            chunkIndex: chunkIndex,
            chunkId: `${sourceUri}::chunk_${chunkIndex}`, // Generate a predictable chunk ID
            ...chunkMetadata, // Metadata from the chunker itself
            // Standard Memory Entry Fields (Map from source/chunk info)
            id: `mem_${(0, uuid_1.v4)()}`, // Let storer override this
            timestamp: Date.now(),
            // Determine MemorySource and MemoryType based on sourceType/contentType
            source: this.mapToMemorySource(sourceMetadata.sourceType),
            type: this.mapToMemoryType(extractedContent.contentType, sourceMetadata.sourceType),
            tags: this.generateTags(sourceMetadata, extractedContent),
        };
        // Remove undefined fields for cleaner metadata
        Object.keys(combined).forEach(key => {
            if (combined[key] === undefined) {
                delete combined[key];
            }
        });
        return combined;
    }
    // Helper methods to map to your specific MemorySource/MemoryType enums
    mapToMemorySource(sourceType) {
        // Example mapping
        if (sourceType === 'local_file' || sourceType === 'http_url' || sourceType === 's3_object')
            return 'file';
        if (sourceType.includes('db') || sourceType.includes('database'))
            return 'database';
        // Add other mappings (WEB, USER_INPUT, etc.)
        return 'workspace'; // Default
    }
    mapToMemoryType(contentType, sourceType) {
        // Example mapping - refine based on your needs
        if (contentType.startsWith('text/'))
            return 'text';
        if (contentType.includes('pdf'))
            return 'document';
        if (contentType.includes('word') || contentType.includes('opendocument.text'))
            return 'document';
        if (contentType.startsWith('image/'))
            return 'image';
        if (contentType.startsWith('audio/'))
            return 'audio';
        if (contentType.startsWith('video/'))
            return 'video';
        if (contentType === 'application/octet-stream' || contentType.includes('binary'))
            return 'binary';
        // Fallback based on source if content type is generic
        if (sourceType === 'local_file')
            return 'code'; // Generic file
        return 'text'; // Default
    }
    generateTags(sourceMetadata, extractedContent) {
        const tags = new Set();
        tags.add(this.mapToMemorySource(sourceMetadata.sourceType).toLowerCase());
        tags.add(this.mapToMemoryType(extractedContent.contentType, sourceMetadata.sourceType).toLowerCase());
        if (sourceMetadata.fileName) {
            const ext = path.extname(sourceMetadata.fileName).toLowerCase().replace('.', '');
            if (ext) {
                tags.add(`ext:${ext}`);
            }
        }
        if (extractedContent.contentType) {
            tags.add(`mime:${extractedContent.contentType.replace('/', '_')}`);
        }
        // Add tags from extracted metadata if available (e.g., language)
        if (extractedContent.metadata?.language) {
            tags.add(`lang:${extractedContent.metadata.language}`);
        }
        return Array.from(tags);
    }
}
exports.UniversalChunkingService = UniversalChunkingService;
// === Default Implementations (Examples) ===
// --- Data Sources ---
class LocalFileSource {
    filePath;
    uri;
    constructor(filePath) {
        if (!path.isAbsolute(filePath)) {
            throw new Error(`LocalFileSource requires an absolute path. Received: ${filePath}`);
        }
        this.filePath = filePath;
        this.uri = `file://${this.filePath}`; // Use file URI scheme
    }
    getUri() {
        return this.uri;
    }
    async getMetadata() {
        try {
            const stats = await fs.promises.stat(this.filePath);
            if (!stats.isFile()) {
                throw new Error(`Path is not a file: ${this.filePath}`);
            }
            // Basic MIME type detection (can be improved with libraries like 'mime-types' or 'file-type')
            const mimeType = this.detectMimeType(this.filePath);
            return {
                uri: this.uri,
                sourceType: 'local_file',
                size: stats.size,
                lastModified: stats.mtime,
                fileName: path.basename(this.filePath),
                mimeType: mimeType,
                filePath: this.filePath, // Include original path if needed downstream
            };
        }
        catch (error) {
            logger_1.logger.error(`[${this.uri}] Error getting metadata:`, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`Failed to get metadata for ${this.filePath}: ${errorMessage}`);
        }
    }
    async getContentStream() {
        try {
            // Create stream only when requested
            const stream = fs.createReadStream(this.filePath);
            stream.on('error', (err) => {
                logger_1.logger.error(`[${this.uri}] Error reading file stream:`, err);
                // Error handling is crucial for streams
            });
            // Add minimal async operation to justify async
            await Promise.resolve();
            return stream;
        }
        catch (error) {
            logger_1.logger.error(`[${this.uri}] Error creating read stream:`, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`Failed to create read stream for ${this.filePath}: ${errorMessage}`);
        }
    }
    // Basic MIME detection based on extension - replace with a robust library
    detectMimeType(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const mimeMap = {
            '.txt': 'text/plain', '.md': 'text/markdown', '.html': 'text/html', '.css': 'text/css', '.js': 'text/javascript',
            '.json': 'application/json', '.xml': 'application/xml', '.pdf': 'application/pdf',
            '.png': 'image/png', '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg', '.gif': 'image/gif', '.svg': 'image/svg+xml',
            '.wav': 'audio/wav', '.mp3': 'audio/mpeg', '.ogg': 'audio/ogg',
            '.mp4': 'video/mp4', '.webm': 'video/webm',
            '.zip': 'application/zip', '.gz': 'application/gzip',
            // Add many more...
        };
        return mimeMap[ext] || 'application/octet-stream'; // Default binary
    }
}
exports.LocalFileSource = LocalFileSource;
// --- Content Extractors ---
class TextExtractor {
    supportedMimeTypes;
    defaultEncoding;
    constructor(supportedMimeTypes = ['text/plain', 'text/markdown', 'text/html', 'text/css', 'text/javascript', 'application/json', 'application/xml'], defaultEncoding = 'utf-8') {
        this.supportedMimeTypes = new Set(supportedMimeTypes);
        this.defaultEncoding = defaultEncoding;
    }
    getName() { return 'TextExtractor'; }
    supports(metadata) {
        // Support if MIME type matches OR if it's a file with a known text extension and no specific binary MIME
        const isSupportedMime = metadata.mimeType ? this.supportedMimeTypes.has(metadata.mimeType) : false;
        // Check if it's a text file based on extension
        let isLikelyTextFile = false;
        if (metadata.sourceType === 'local_file' && metadata.fileName) {
            const fileExtension = path.extname(metadata.fileName).toLowerCase();
            const textExtensions = ['.txt', '.md', '.log', '.csv', '.tsv', '.html', '.css', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp', '.h', '.hpp', '.cs', '.go', '.rb', '.php', '.sh', '.bash', '.ps1', '.xml', '.json', '.yaml', '.yml', '.sql', '.graphql', '.gql', '.dockerfile', '.tf', '.hcl', '.swift', '.kt', '.kts', '.groovy', '.scala', '.rs', '.lua', '.pl', '.pm', '.r', '.dart', '.vue', '.svelte'];
            isLikelyTextFile = textExtensions.includes(fileExtension) &&
                (!metadata.mimeType || metadata.mimeType === 'application/octet-stream');
        }
        return isSupportedMime || isLikelyTextFile;
    }
    async extract(stream, metadata) {
        try {
            // Read the entire stream into a buffer, then decode
            // For *very* large text files, stream processing line-by-line might be better,
            // but requires changes in chunking strategies too. This is simpler for now.
            const buffer = await this.streamToBuffer(stream);
            const content = buffer.toString(this.defaultEncoding); // Assume UTF-8, could try detecting encoding
            // TODO: Add language detection using a library (e.g., 'franc')
            // const language = detectLanguage(content);
            return {
                contentType: metadata.mimeType ?? 'text/plain', // Use detected MIME or default
                content: content,
                metadata: {
                    encoding: this.defaultEncoding,
                    // language: language // Add detected language if available
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`[${metadata.uri}] Error extracting text content:`, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`Failed to extract text content: ${errorMessage}`);
        }
    }
    async streamToBuffer(stream) {
        const chunks = [];
        for await (const chunk of stream) {
            chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
        }
        return Buffer.concat(chunks);
    }
}
exports.TextExtractor = TextExtractor;
class BinaryExtractor {
    getName() { return 'BinaryExtractor'; }
    supports(metadata) {
        // Acts as a fallback for anything not handled by other extractors
        // Log the metadata for debugging purposes
        logger_1.logger.debug(`BinaryExtractor supporting fallback extraction for: ${metadata.uri}`);
        return true;
    }
    async extract(stream, metadata) {
        try {
            // Read the entire stream into a buffer
            const buffer = await this.streamToBuffer(stream);
            return {
                contentType: metadata.mimeType ?? 'application/octet-stream',
                content: buffer,
                metadata: {}, // No specific metadata extracted from raw bytes
            };
        }
        catch (error) {
            logger_1.logger.error(`[${metadata.uri}] Error extracting binary content:`, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`Failed to extract binary content: ${errorMessage}`);
        }
    }
    async streamToBuffer(stream) {
        const chunks = [];
        for await (const chunk of stream) {
            chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
        }
        return Buffer.concat(chunks);
    }
}
exports.BinaryExtractor = BinaryExtractor;
// --- Chunking Strategies ---
class RecursiveTextChunker {
    getName() { return 'RecursiveTextChunker'; }
    supports(contentType) {
        return contentType.startsWith('text/');
    }
    async *chunk(extracted, config, sourceMetadata) {
        if (typeof extracted.content !== 'string') {
            logger_1.logger.warn(`[${sourceMetadata.uri}] RecursiveTextChunker received non-string content type ${extracted.contentType}. Skipping.`);
            return;
        }
        const content = extracted.content;
        const chunkSize = config.defaultChunkSize;
        const chunkOverlap = config.defaultChunkOverlap;
        const extension = sourceMetadata.fileName ? path.extname(sourceMetadata.fileName).toLowerCase() : '.txt'; // Default extension
        // Get separators based on config or default
        const separators = config.recursiveSeparators?.[extension] ?? config.defaultTextSeparators ?? this.getDefaultSeparators();
        try {
            const splitter = new corePolyfill_1.RecursiveCharacterTextSplitter({
                chunkSize,
                chunkOverlap,
                separators,
                keepSeparator: false,
                lengthFunction: (text) => text.length,
            });
            const textChunks = await splitter.splitText(content);
            for (let i = 0; i < textChunks.length; i++) {
                const chunkContent = textChunks[i];
                // Skip whitespace-only chunks
                if (chunkContent.trim().length > 0) {
                    yield {
                        content: chunkContent,
                        metadata: {
                            chunker: this.getName(),
                            originalLength: chunkContent.length,
                            // Add line numbers if feasible/needed (requires more complex tracking)
                        },
                    };
                }
                else {
                    logger_1.logger.debug(`[${sourceMetadata.uri}] Skipping whitespace-only text chunk at index ${i}.`);
                }
            }
        }
        catch (error) {
            logger_1.logger.error(`[${sourceMetadata.uri}] Error during recursive text chunking:`, error);
            // Re-throw or handle as needed; maybe yield an error chunk?
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`Recursive text chunking failed: ${errorMessage}`);
        }
    }
    getDefaultSeparators() {
        return ['\n\n', '\n', '. ', '? ', '! ', ' ', ''];
    }
}
exports.RecursiveTextChunker = RecursiveTextChunker;
class FixedSizeBinaryChunker {
    getName() { return 'FixedSizeBinaryChunker'; }
    supports(contentType) {
        // Apply to generic binary or any type not handled by text chunkers
        return contentType === 'application/octet-stream' || !contentType.startsWith('text/');
    }
    async *chunk(extracted, config, sourceMetadata) {
        if (!Buffer.isBuffer(extracted.content)) {
            logger_1.logger.warn(`[${sourceMetadata.uri}] FixedSizeBinaryChunker received non-buffer content type ${extracted.contentType}. Skipping.`);
            return;
        }
        const buffer = extracted.content;
        const chunkSize = config.fixedBinaryChunkSize; // Use binary-specific chunk size
        const totalSize = buffer.length;
        if (totalSize === 0) {
            return; // No chunks for empty buffer
        }
        logger_1.logger.debug(`[${sourceMetadata.uri}] Chunking binary data (${totalSize} bytes) into fixed sizes of ${chunkSize} bytes.`);
        // Add minimal async operation to justify async generator
        await Promise.resolve();
        for (let i = 0; i < totalSize; i += chunkSize) {
            const end = Math.min(i + chunkSize, totalSize);
            const chunkContent = Buffer.from(buffer.subarray(i, end));
            if (chunkContent.length > 0) {
                yield {
                    content: chunkContent, // Yield Buffer directly
                    metadata: {
                        chunker: this.getName(),
                        startByte: i,
                        endByte: end,
                        chunkSize: chunkContent.length,
                    },
                };
            }
        }
    }
}
exports.FixedSizeBinaryChunker = FixedSizeBinaryChunker;
/**
 * FileChunkingService - A service for chunking files and workspace folders
 * This is a facade over the UniversalChunkingService that provides a simpler API
 * for common file chunking operations.
 */
class FileChunkingService {
    static instance = null;
    static memoryStorer = null;
    /**
       * Initialize the FileChunkingService with a memory storer
       */
    static initialize(memoryStorer) {
        // Create extractors
        const extractors = [
            new TextExtractor(),
            new BinaryExtractor() // Fallback extractor
        ];
        // Create chunking strategies
        const chunkingStrategies = [
            new RecursiveTextChunker(),
            new FixedSizeBinaryChunker()
        ];
        // Create default config
        const config = {
            defaultChunkSize: (0, config_1.getConfig)('memory.fileChunking.chunkSize', 1000),
            defaultChunkOverlap: (0, config_1.getConfig)('memory.fileChunking.chunkOverlap', 200),
            maxChunksPerSource: (0, config_1.getConfig)('memory.fileChunking.maxChunksPerFile', 100),
            concurrencyLimit: 5,
            textChunkingStrategy: 'recursive',
            recursiveSeparators: {
                '.py': ['\n\n', '\n', '. ', '? ', '! ', ' ', ''],
                '.js': ['\n\n', '\n', '. ', '? ', '! ', ' ', ''],
                '.ts': ['\n\n', '\n', '. ', '? ', '! ', ' ', ''],
                '.md': ['\n\n', '\n', '## ', '### ', '. ', '? ', '! ', ' ', ''],
                '.txt': ['\n\n', '\n', '. ', '? ', '! ', ' ', '']
            },
            defaultTextSeparators: ['\n\n', '\n', '. ', '? ', '! ', ' ', ''],
            binaryChunkingStrategy: 'fixed',
            fixedBinaryChunkSize: 4096,
            localFile: {
                maxFileSizeMB: 100,
                allowedExtensions: [],
                excludedPatterns: ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.git/**']
            }
        };
        // Create the UniversalChunkingService
        this.instance = new UniversalChunkingService(config, extractors, chunkingStrategies, memoryStorer);
        this.memoryStorer = memoryStorer;
        logger_1.logger.info('FileChunkingService initialized');
    }
    /**
       * Chunk a file and store in memory
       * @param filePath Path to the file to chunk
       * @returns Array of memory entries created from the file
       */
    static async chunkFile(filePath) {
        if (!this.instance || !this.memoryStorer) {
            throw new Error('FileChunkingService not initialized');
        }
        // Create a local file data source
        const fileSource = new LocalFileDataSource(filePath);
        // Process the file
        const result = await this.instance.processDataSource(fileSource);
        if (result.status === 'error') {
            throw new Error(`Failed to chunk file: ${result.message}`);
        }
        return result.addedEntries;
    }
    /**
       * Chunk a workspace folder and store in memory
       * @param folderPath Path to the folder to chunk
       * @param includePatterns Glob patterns to include
       * @param excludePatterns Glob patterns to exclude
       * @returns Array of memory entries created from the workspace
       */
    static async chunkWorkspace(folderPath, includePatterns = [
        // === Programming Languages ===
        // JavaScript/TypeScript ecosystem
        '**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,svelte,astro}',
        // Python ecosystem
        '**/*.{py,pyx,pyi,pyw,py3,ipynb,pth}',
        // Java ecosystem
        '**/*.{java,kt,kts,scala,sc,groovy,gradle,kts}',
        // C/C++ ecosystem
        '**/*.{c,cc,cpp,cxx,c++,h,hh,hpp,hxx,h++,inc,inl}',
        // C# ecosystem
        '**/*.{cs,csx,vb,vbx,fs,fsx,fsi}',
        // Web technologies
        '**/*.{html,htm,xhtml,xml,xsl,xslt,svg,mathml}',
        '**/*.{css,scss,sass,less,styl,stylus,pcss,postcss}',
        // Mobile development
        '**/*.{swift,m,mm,dart,flutter}',
        '**/*.{java,kt,xml}', // Android
        // Other popular languages
        '**/*.{go,rs,php,rb,pl,pm,lua,r,R,jl,elm,hs,lhs,ml,mli,ocaml,clj,cljs,cljc,edn}',
        '**/*.{erl,hrl,ex,exs,elixir,nim,cr,zig,odin,v,vlang}',
        // === Configuration & Data Files ===
        // Package managers & build tools
        '**/*.{json,json5,jsonc,yaml,yml,toml,ini,cfg,conf,config,properties}',
        '**/package.json', '**/package-lock.json', '**/yarn.lock', '**/pnpm-lock.yaml',
        '**/Cargo.toml', '**/Cargo.lock', '**/go.mod', '**/go.sum',
        '**/requirements.txt', '**/Pipfile', '**/Pipfile.lock', '**/pyproject.toml',
        '**/composer.json', '**/composer.lock', '**/Gemfile', '**/Gemfile.lock',
        '**/build.gradle', '**/pom.xml', '**/CMakeLists.txt', '**/Makefile',
        // Environment & deployment
        '**/.env*', '**/docker-compose*.{yml,yaml}', '**/Dockerfile*', '**/Containerfile*',
        '**/k8s*.{yml,yaml}', '**/kustomization.{yml,yaml}',
        // CI/CD
        '**/.github/**/*.{yml,yaml}', '**/.gitlab-ci.yml', '**/azure-pipelines.yml',
        '**/Jenkinsfile', '**/bitbucket-pipelines.yml', '**/circle.yml', '**/.circleci/**/*.yml',
        // === Documentation & Markup ===
        '**/*.{md,mdx,markdown,rst,txt,rtf,adoc,asciidoc,org,tex,latex}',
        '**/*.{wiki,textile,creole,pod,rdoc}',
        // === Database & Query Languages ===
        '**/*.{sql,mysql,pgsql,sqlite,nosql,cql,cypher,sparql,graphql,gql}',
        '**/*.{hql,pig,hive}', // Big data
        // === Shell & Scripting ===
        '**/*.{sh,bash,zsh,fish,csh,tcsh,ksh,dash}',
        '**/*.{bat,cmd,ps1,psm1,psd1}', // Windows
        '**/*.{awk,sed,perl,tcl,expect}',
        // === Microsoft Technologies ===
        '**/*.{xaml,xml,resx,settings,config,manifest,targets,props}',
        '**/*.{aspx,ascx,master,ashx,asmx,svc,cshtml,vbhtml,razor}',
        '**/*.{csproj,vbproj,fsproj,vcxproj,vcproj,sln,slnx}',
        // === Apple Technologies ===
        '**/*.{plist,strings,storyboard,xib,xcconfig,xcscheme,xcworkspacedata}',
        '**/*.{pbxproj,xcodeproj,xcworkspace}/**/*',
        // === Game Development ===
        '**/*.{unity,prefab,scene,asset,meta,shader,hlsl,glsl,cg,fx}',
        '**/*.{ue4,uasset,umap,blueprint}',
        '**/*.{gdscript,gd,tres,tscn,godot}',
        // === Data Science & Analytics ===
        '**/*.{ipynb,rmd,qmd,jmd}', // Notebooks
        '**/*.{sas,spss,stata,dta,sav}',
        '**/*.{weka,arff,libsvm}',
        // === Infrastructure as Code ===
        '**/*.{tf,tfvars,hcl}', // Terraform
        '**/*.{bicep,arm}', // Azure
        '**/*.{cfn,template}', // CloudFormation
        '**/serverless.{yml,yaml}', // Serverless
        // === API & Protocol Definitions ===
        '**/*.{proto,avro,thrift,swagger,openapi}',
        '**/*.{wsdl,xsd,dtd,rng,rnc}',
        // === Specialized Formats ===
        '**/*.{log,trace,dump}', // Logs and debugging
        '**/*.{patch,diff}', // Version control
        '**/*.{license,copyright,notice,authors,contributors,changelog,news}',
        '**/{README,CHANGELOG,LICENSE,NOTICE,AUTHORS,CONTRIBUTORS,INSTALL,TODO,FIXME}*',
        // === Legacy & Specialized Languages ===
        '**/*.{cobol,cob,cbl,fortran,f90,f95,ada,pas,dpr,asm,s,nasm,masm}',
        '**/*.{vhdl,vhd,verilog,v,sv,systemverilog}', // Hardware description
        '**/*.{matlab,m,octave}', // Scientific computing
        // === Template & Generator Files ===
        '**/*.{mustache,handlebars,hbs,twig,jinja,j2,erb,ejs,pug,jade}',
        '**/*.{liquid,smarty,velocity,freemarker,thymeleaf}',
        // === Catch remaining text files ===
        '**/*.txt', '**/*README*', '**/*CHANGELOG*', '**/*LICENSE*'
    ], excludePatterns = [
        // Build outputs and dependencies
        '**/node_modules/**', '**/dist/**', '**/build/**', '**/out/**', '**/target/**',
        '**/bin/**', '**/obj/**', '**/.next/**', '**/.nuxt/**', '**/coverage/**',
        // Version control
        '**/.git/**', '**/.svn/**', '**/.hg/**', '**/.bzr/**',
        // IDE and editor files
        '**/.vscode/**', '**/.idea/**', '**/.*project', '**/*.swp', '**/*.swo', '**/*~',
        // OS files
        '**/.DS_Store', '**/Thumbs.db', '**/desktop.ini',
        // Temporary and cache files
        '**/tmp/**', '**/temp/**', '**/.cache/**', '**/.temp/**', '**/logs/**',
        // Package manager caches
        '**/.npm/**', '**/.yarn/**', '**/.pnpm/**', '**/bower_components/**',
        // Language-specific build artifacts
        '**/__pycache__/**', '**/*.pyc', '**/*.pyo', '**/*.pyd',
        '**/vendor/**', // PHP/Go vendor directories
        '**/.gradle/**', '**/gradle/**',
        // Large binary or generated files
        '**/*.min.js', '**/*.min.css', '**/*.bundle.js', '**/*.chunk.js',
        // Database files
        '**/*.db', '**/*.sqlite', '**/*.sqlite3',
        // Media files (usually not useful for code analysis)
        '**/*.{jpg,jpeg,png,gif,bmp,ico,svg,webp,tiff,tif}',
        '**/*.{mp3,mp4,avi,mov,wmv,flv,webm,ogg,wav,flac}',
        '**/*.{pdf,doc,docx,xls,xlsx,ppt,pptx}',
        // Archive files
        '**/*.{zip,tar,gz,bz2,xz,7z,rar,dmg,iso}'
    ]) {
        if (!this.instance || !this.memoryStorer) {
            throw new Error('FileChunkingService not initialized');
        }
        // Find all files in the workspace that match the patterns
        const allFiles = [];
        for (const pattern of includePatterns) {
            const files = await (0, glob_1.glob)(pattern, {
                cwd: folderPath,
                ignore: excludePatterns,
                absolute: true
            });
            allFiles.push(...files);
        }
        logger_1.logger.info(`Found ${allFiles.length} files in workspace ${folderPath}`);
        if (allFiles.length === 0) {
            return [];
        }
        // Create data sources for each file
        const dataSources = allFiles.map(file => new LocalFileDataSource(file));
        // Process all files
        const batchResult = await this.instance.processBatch(dataSources);
        if (batchResult.failedSources > 0) {
            logger_1.logger.warn(`Failed to chunk ${batchResult.failedSources} files in workspace ${folderPath}`);
        }
        // Flatten all added entries
        const allEntries = [];
        for (const result of batchResult.results) {
            allEntries.push(...result.addedEntries);
        }
        return allEntries;
    }
}
exports.FileChunkingService = FileChunkingService;
/**
 * LocalFileDataSource - A data source for local files
 */
class LocalFileDataSource {
    filePath;
    constructor(filePath) {
        this.filePath = filePath;
    }
    getUri() {
        return `file://${this.filePath}`;
    }
    async getMetadata() {
        const stat = await fs.promises.stat(this.filePath);
        return {
            uri: this.getUri(),
            sourceType: 'local_file',
            size: stat.size,
            lastModified: stat.mtime,
            fileName: path.basename(this.filePath),
            mimeType: this.getMimeType(this.filePath)
        };
    }
    async getContentStream() {
        // Add minimal async operation to justify async
        await Promise.resolve();
        return fs.createReadStream(this.filePath);
    }
    getMimeType(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        // Comprehensive MIME type mapping for all supported file types
        const mimeTypes = {
            // === Text and Markup ===
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.markdown': 'text/markdown',
            '.mdx': 'text/markdown',
            '.rst': 'text/x-rst',
            '.rtf': 'application/rtf',
            '.adoc': 'text/asciidoc',
            '.asciidoc': 'text/asciidoc',
            '.org': 'text/org',
            '.tex': 'text/x-tex',
            '.latex': 'text/x-latex',
            '.wiki': 'text/x-wiki',
            '.textile': 'text/x-textile',
            '.creole': 'text/x-creole',
            '.pod': 'text/x-pod',
            '.rdoc': 'text/x-rdoc',
            // === Web Technologies ===
            '.html': 'text/html',
            '.htm': 'text/html',
            '.xhtml': 'application/xhtml+xml',
            '.xml': 'application/xml',
            '.xsl': 'application/xslt+xml',
            '.xslt': 'application/xslt+xml',
            '.svg': 'image/svg+xml',
            '.mathml': 'application/mathml+xml',
            '.css': 'text/css',
            '.scss': 'text/x-scss',
            '.sass': 'text/x-sass',
            '.less': 'text/x-less',
            '.styl': 'text/x-stylus',
            '.stylus': 'text/x-stylus',
            '.pcss': 'text/x-postcss',
            '.postcss': 'text/x-postcss',
            // === JavaScript/TypeScript Ecosystem ===
            '.js': 'text/javascript',
            '.mjs': 'text/javascript',
            '.cjs': 'text/javascript',
            '.jsx': 'text/jsx',
            '.ts': 'text/typescript',
            '.mts': 'text/typescript',
            '.cts': 'text/typescript',
            '.tsx': 'text/tsx',
            '.vue': 'text/x-vue',
            '.svelte': 'text/x-svelte',
            '.astro': 'text/x-astro',
            // === Python Ecosystem ===
            '.py': 'text/x-python',
            '.pyx': 'text/x-cython',
            '.pyi': 'text/x-python',
            '.pyw': 'text/x-python',
            '.py3': 'text/x-python',
            '.ipynb': 'application/x-ipynb+json',
            '.pth': 'text/plain',
            // === Java Ecosystem ===
            '.java': 'text/x-java-source',
            '.kt': 'text/x-kotlin',
            '.kts': 'text/x-kotlin',
            '.scala': 'text/x-scala',
            '.sc': 'text/x-scala',
            '.groovy': 'text/x-groovy',
            '.gradle': 'text/x-gradle',
            // === C/C++ Ecosystem ===
            '.c': 'text/x-c',
            '.cc': 'text/x-c++',
            '.cpp': 'text/x-c++',
            '.cxx': 'text/x-c++',
            '.c++': 'text/x-c++',
            '.h': 'text/x-c',
            '.hh': 'text/x-c++',
            '.hpp': 'text/x-c++',
            '.hxx': 'text/x-c++',
            '.h++': 'text/x-c++',
            '.inc': 'text/x-c',
            '.inl': 'text/x-c++',
            // === C# and .NET ===
            '.cs': 'text/x-csharp',
            '.csx': 'text/x-csharp',
            '.vb': 'text/x-vb',
            '.vbx': 'text/x-vb',
            '.fs': 'text/x-fsharp',
            '.fsx': 'text/x-fsharp',
            '.fsi': 'text/x-fsharp',
            '.xaml': 'application/xaml+xml',
            '.resx': 'application/x-resx',
            '.config': 'application/xml',
            '.manifest': 'application/xml',
            '.targets': 'application/xml',
            '.props': 'application/xml',
            '.csproj': 'application/xml',
            '.vbproj': 'application/xml',
            '.fsproj': 'application/xml',
            '.vcxproj': 'application/xml',
            '.vcproj': 'application/xml',
            '.sln': 'text/plain',
            '.slnx': 'application/xml',
            // === Mobile Development ===
            '.swift': 'text/x-swift',
            '.m': 'text/x-objectivec',
            '.mm': 'text/x-objectivec++',
            '.dart': 'text/x-dart',
            '.flutter': 'text/x-dart',
            // === Other Popular Languages ===
            '.go': 'text/x-go',
            '.rs': 'text/x-rust',
            '.php': 'text/x-php',
            '.rb': 'text/x-ruby',
            '.pl': 'text/x-perl',
            '.pm': 'text/x-perl',
            '.lua': 'text/x-lua',
            '.r': 'text/x-r',
            '.R': 'text/x-r',
            '.jl': 'text/x-julia',
            '.elm': 'text/x-elm',
            '.hs': 'text/x-haskell',
            '.lhs': 'text/x-literate-haskell',
            '.ml': 'text/x-ocaml',
            '.mli': 'text/x-ocaml',
            '.ocaml': 'text/x-ocaml',
            '.clj': 'text/x-clojure',
            '.cljs': 'text/x-clojure',
            '.cljc': 'text/x-clojure',
            '.edn': 'application/edn',
            '.erl': 'text/x-erlang',
            '.hrl': 'text/x-erlang',
            '.ex': 'text/x-elixir',
            '.exs': 'text/x-elixir',
            '.elixir': 'text/x-elixir',
            '.nim': 'text/x-nim',
            '.cr': 'text/x-crystal',
            '.zig': 'text/x-zig',
            '.odin': 'text/x-odin',
            '.v': 'text/x-vlang',
            '.vlang': 'text/x-vlang',
            // === Configuration and Data ===
            '.json': 'application/json',
            '.json5': 'application/json5',
            '.jsonc': 'application/json',
            '.yaml': 'text/yaml',
            '.yml': 'text/yaml',
            '.toml': 'application/toml',
            '.ini': 'text/plain',
            '.cfg': 'text/plain',
            '.conf': 'text/plain',
            '.properties': 'text/x-java-properties',
            // === Shell and Scripting ===
            '.sh': 'text/x-shellscript',
            '.bash': 'text/x-shellscript',
            '.zsh': 'text/x-shellscript',
            '.fish': 'text/x-fish',
            '.csh': 'text/x-csh',
            '.tcsh': 'text/x-tcsh',
            '.ksh': 'text/x-ksh',
            '.dash': 'text/x-shellscript',
            '.bat': 'text/x-msdos-batch',
            '.cmd': 'text/x-msdos-batch',
            '.ps1': 'text/x-powershell',
            '.psm1': 'text/x-powershell',
            '.psd1': 'text/x-powershell',
            '.awk': 'text/x-awk',
            '.sed': 'text/x-sed',
            '.perl': 'text/x-perl',
            '.tcl': 'text/x-tcl',
            '.expect': 'text/x-expect',
            // === Database and Query Languages ===
            '.sql': 'text/x-sql',
            '.mysql': 'text/x-mysql',
            '.pgsql': 'text/x-pgsql',
            '.sqlite': 'text/x-sqlite',
            '.nosql': 'text/x-nosql',
            '.cql': 'text/x-cassandra',
            '.cypher': 'text/x-cypher',
            '.sparql': 'text/x-sparql',
            '.graphql': 'application/graphql',
            '.gql': 'application/graphql',
            '.hql': 'text/x-hive',
            '.pig': 'text/x-pig',
            '.hive': 'text/x-hive',
            // === Apple Technologies ===
            '.plist': 'application/x-plist',
            '.strings': 'text/plain',
            '.storyboard': 'application/xml',
            '.xib': 'application/xml',
            '.xcconfig': 'text/plain',
            '.xcscheme': 'application/xml',
            '.xcworkspacedata': 'application/xml',
            '.pbxproj': 'text/plain',
            // === Game Development ===
            '.unity': 'text/x-unity',
            '.prefab': 'text/yaml',
            '.scene': 'text/yaml',
            '.asset': 'text/yaml',
            '.meta': 'text/yaml',
            '.shader': 'text/x-glsl',
            '.hlsl': 'text/x-hlsl',
            '.glsl': 'text/x-glsl',
            '.cg': 'text/x-cg',
            '.fx': 'text/x-fx',
            '.gdscript': 'text/x-gdscript',
            '.gd': 'text/x-gdscript',
            '.tres': 'text/plain',
            '.tscn': 'text/plain',
            // === Infrastructure as Code ===
            '.tf': 'text/x-terraform',
            '.tfvars': 'text/x-terraform',
            '.hcl': 'text/x-hcl',
            '.bicep': 'text/x-bicep',
            '.arm': 'application/json',
            // === API and Protocol Definitions ===
            '.proto': 'text/x-protobuf',
            '.avro': 'application/json',
            '.thrift': 'text/x-thrift',
            '.swagger': 'application/json',
            '.openapi': 'application/json',
            '.wsdl': 'application/xml',
            '.xsd': 'application/xml',
            '.dtd': 'application/xml-dtd',
            '.rng': 'application/xml',
            '.rnc': 'text/x-rnc',
            // === Template and Generator Files ===
            '.mustache': 'text/x-mustache',
            '.handlebars': 'text/x-handlebars',
            '.hbs': 'text/x-handlebars',
            '.twig': 'text/x-twig',
            '.jinja': 'text/x-jinja',
            '.j2': 'text/x-jinja',
            '.erb': 'text/x-ruby-template',
            '.ejs': 'text/x-ejs',
            '.pug': 'text/x-pug',
            '.jade': 'text/x-jade',
            '.liquid': 'text/x-liquid',
            '.smarty': 'text/x-smarty',
            '.velocity': 'text/x-velocity',
            '.freemarker': 'text/x-freemarker',
            '.thymeleaf': 'text/x-thymeleaf',
            // === Legacy and Specialized Languages ===
            '.cobol': 'text/x-cobol',
            '.cob': 'text/x-cobol',
            '.cbl': 'text/x-cobol',
            '.fortran': 'text/x-fortran',
            '.f90': 'text/x-fortran',
            '.f95': 'text/x-fortran',
            '.ada': 'text/x-ada',
            '.pas': 'text/x-pascal',
            '.dpr': 'text/x-pascal',
            '.asm': 'text/x-asm',
            '.s': 'text/x-asm',
            '.nasm': 'text/x-nasm',
            '.masm': 'text/x-masm',
            '.vhdl': 'text/x-vhdl',
            '.vhd': 'text/x-vhdl',
            '.verilog': 'text/x-verilog',
            '.sv': 'text/x-systemverilog',
            '.systemverilog': 'text/x-systemverilog',
            '.matlab': 'text/x-matlab',
            '.octave': 'text/x-octave',
            // === Data Science and Analytics ===
            '.rmd': 'text/x-r-markdown',
            '.qmd': 'text/x-quarto',
            '.jmd': 'text/x-julia-markdown',
            '.sas': 'text/x-sas',
            '.spss': 'text/x-spss',
            '.stata': 'text/x-stata',
            '.dta': 'application/x-stata',
            '.sav': 'application/x-spss',
            '.weka': 'text/x-weka',
            '.arff': 'text/x-arff',
            '.libsvm': 'text/x-libsvm',
            // === Specialized Formats ===
            '.log': 'text/plain',
            '.trace': 'text/plain',
            '.dump': 'text/plain',
            '.patch': 'text/x-patch',
            '.diff': 'text/x-diff',
            '.license': 'text/plain',
            '.copyright': 'text/plain',
            '.notice': 'text/plain',
            '.authors': 'text/plain',
            '.contributors': 'text/plain',
            '.changelog': 'text/plain',
            '.news': 'text/plain',
            // === Microsoft Office and Documents ===
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.pdf': 'application/pdf',
            // === Media Files (for completeness) ===
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.ico': 'image/x-icon',
            '.webp': 'image/webp',
            '.tiff': 'image/tiff',
            '.tif': 'image/tiff',
            '.mp3': 'audio/mpeg',
            '.mp4': 'video/mp4',
            '.avi': 'video/x-msvideo',
            '.mov': 'video/quicktime',
            '.wmv': 'video/x-ms-wmv',
            '.flv': 'video/x-flv',
            '.webm': 'video/webm',
            '.ogg': 'audio/ogg',
            '.wav': 'audio/wav',
            '.flac': 'audio/flac'
        };
        return mimeTypes[ext] ?? 'application/octet-stream';
    }
}
//# sourceMappingURL=fileChunking.js.map