"use strict";
// Polyfill for @codessa/core functionality used in this project.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallbackManagerForLLMRun = exports.CallbackEventType = exports.MemoryVectorStore = exports.VectorStoreRetrieverMemory = exports.ConversationSummaryMemory = exports.BufferMemory = exports.BaseMemory = exports.BaseChatMemory = exports.ChatMessageHistory = exports.AsyncCaller = exports.BaseChatModel = exports.BaseLanguageModel = exports.RunnableConfig = exports.MessagesPlaceholder = exports.ChatPromptTemplate = exports.Document = exports.RecursiveCharacterTextSplitter = exports.Chroma = exports.PineconeStore = exports.SimilaritySearchResultItem = exports.OpenAIEmbeddings = exports.Embeddings = exports.SystemMessage = exports.ToolMessage = exports.AIMessage = exports.HumanMessage = exports.BaseMessage = exports.StructuredTool = exports.BaseTool = exports.Tool = exports.StateGraph = exports.StubStateGraph = exports.END = exports.START = void 0;
exports.Annotation = Annotation;
exports.START = '__start__';
exports.END = '__end__';
// Minimal stub for compatibility with code that expects a StateGraph with this API.
// Use the advanced implementation below for actual workflows.
class StubStateGraph {
    args;
    nodes = {};
    channels = undefined;
    entryPoint;
    compiled = false;
    constructor(args) {
        this.args = args;
        this.channels = args.channels;
    }
    addNode(name, handler) { this.nodes[name] = handler; return this; }
    addEdge(from, to) { /* stub */ return this; }
    addConditionalEdges(...args) { /* stub */ return this; }
    setStart(start) { this.entryPoint = start; return this; }
    setEnd(end) { /* stub */ return this; }
    setEntryPoint(entry) { this.entryPoint = entry; return this; }
    compile() { this.compiled = true; return this; }
    build() { return this; }
    async run(input) { return input; }
    async invoke(input, config) { return input; }
}
exports.StubStateGraph = StubStateGraph;
/**
 * Defines a single channel annotation.
 * @example
 *   messages: Annotation<BaseMessage[]>({ reducer, default })
 */
function Annotation(options) {
    // .spec is itself for merging convenience
    const inst = {
        __isAnnotation: true,
        ...(options || {}),
        spec: {} // will be set by Root
    };
    return inst;
}
(function (Annotation) {
    function Root(schema) {
        // Attach .spec to each annotation for merging
        Object.entries(schema).forEach(([k, v]) => {
            if (v && typeof v === 'object')
                v.spec = { [k]: v };
        });
        const result = { schema };
        // .spec for merging
        result.spec = schema;
        // .State for type extraction
        result.State = null;
        return result;
    }
    Annotation.Root = Root;
})(Annotation || (exports.Annotation = Annotation = {}));
let zod = undefined;
try {
    zod = require('zod');
}
catch { /* fallback for environments without zod */ }
/**
 * Polyfill for Zod integration with Codessa channel/reducer API.
 * Usage: z.object({ ... }).codessa.reducer(fn, inputSchema).default(fn)
 */
if (zod && zod.ZodObject) {
    // Patch Zod prototype for .codessa.reducer
    if (!zod.ZodType.prototype.codessa) {
        Object.defineProperty(zod.ZodType.prototype, 'codessa', {
            value: {
                reducer(fn, inputSchema) {
                    // Attach reducer as metadata
                    this._codessaReducer = fn;
                    if (inputSchema)
                        this._codessaInputSchema = inputSchema;
                    return this;
                }
            },
            writable: true,
            configurable: true
        });
    }
    // Patch .default if not present
    if (!zod.ZodType.prototype.default) {
        zod.ZodType.prototype.default = function (factory) {
            this._codessaDefault = factory;
            return this;
        };
    }
}
/**
 * Minimal StateGraph polyfill for Codessa workflows.
 * Supports addNode, addEdge, addConditionalEdges, setEntryPoint, setFinishPoint, compile, invoke, input/output schemas, Zod validation, and streaming.
 * TypeScript-compatible and ergonomic.
 *
 * @example
 *   const ann = Annotation.Root({ ... });
 *   const graph = new StateGraph(ann)
 *     .addNode('foo', fn)
 *     .addEdge('a', 'b')
 *     .compile();
 *   await graph.invoke(input);
 */
// Utility type guards for stricter type safety
function isAnnotationInstance(obj) {
    return !!obj && typeof obj === 'object' && obj.__isAnnotation === true;
}
function isZodObject(obj) {
    return zod && obj instanceof zod.ZodObject;
}
class StateGraph {
    nodes = {};
    edges = [];
    // entryPoint and finishPoint are always string (never null): enforce at compile time
    entryPoint;
    finishPoint;
    compiled = false;
    schema;
    inputSchema;
    outputSchema;
    zodSchema;
    constructor(annotationOrChannels) {
        // Accepts Annotation.Root, {input, output}, Zod, or plain channels
        if (annotationOrChannels && annotationOrChannels.input && annotationOrChannels.output) {
            this.inputSchema = annotationOrChannels.input;
            this.outputSchema = annotationOrChannels.output;
            this.schema = {
                ...annotationOrChannels.input.schema,
                ...annotationOrChannels.output.schema,
            };
        }
        else if (annotationOrChannels && annotationOrChannels.schema) {
            this.schema = annotationOrChannels.schema;
        }
        else if (zod && annotationOrChannels instanceof zod.ZodObject) {
            this.zodSchema = annotationOrChannels;
            this.schema = annotationOrChannels.shape;
        }
        else {
            this.schema = annotationOrChannels;
        }
    }
    addNode(key, fn, options) {
        this.nodes[key] = { fn, options };
        return this;
    }
    addEdge(from, to) {
        this.edges.push({ from, to });
        return this;
    }
    addConditionalEdges(from, conditions) {
        for (const cond of conditions) {
            this.edges.push({ from, to: cond.to, condition: cond.condition });
        }
        return this;
    }
    setEntryPoint(key) {
        this.entryPoint = key;
        return this;
    }
    setFinishPoint(key) {
        this.finishPoint = key;
        return this;
    }
    compile() {
        if (!this.entryPoint)
            throw new Error('Entry point must be set before compiling.');
        this.compiled = true;
        // Optionally, validate graph structure here
        return this;
    }
    /**
       * Streaming invoke: yields state after each node (simulated async streaming).
       * Use for token-by-token or step-by-step streaming.
       */
    async *stream(input) {
        if (!this.compiled)
            throw new Error('Graph must be compiled before invoking.');
        let state = { ...input };
        let current = this.entryPoint;
        const visited = new Set();
        while (current && !visited.has(current)) {
            visited.add(current);
            const node = this.nodes[current];
            if (!node)
                throw new Error(`Node '${current}' not found.`);
            const nodeInput = node.options?.input ? { ...state, ...node.options.input } : state;
            let output = await node.fn(nodeInput);
            // Zod runtime validation of node output if possible
            if (zod && this.zodSchema) {
                try {
                    output = this.zodSchema.parse(output);
                }
                catch (e) {
                    console.warn('Zod validation failed for node', current, e);
                }
            }
            state = { ...state, ...output }; // Type assertion fixes Partial<StateSchema> error
            yield state;
            // Find next node
            let next = undefined;
            for (const edge of this.edges) {
                if (edge.from === current) {
                    if (!edge.condition || edge.condition(state)) {
                        next = edge.to;
                        break;
                    }
                }
            }
            current = next;
        }
        return state;
    }
    /**
       * Minimal synchronous invoke for demonstration. Real Codessa supports async, streaming, etc.
       * This will just execute nodes in a simple topological order, passing state.
       * Performs runtime validation using Zod or annotation defaults if available.
       */
    async invoke(input) {
        if (!this.compiled)
            throw new Error('Graph must be compiled before invoking.');
        let state = { ...input };
        let current = this.entryPoint;
        const visited = new Set();
        while (current && !visited.has(current)) {
            visited.add(current);
            const node = this.nodes[current];
            if (!node)
                throw new Error(`Node '${current}' not found.`);
            const nodeInput = node.options?.input ? { ...state, ...node.options.input } : state;
            let output = await node.fn(nodeInput);
            // Zod runtime validation of node output if possible
            if (zod && this.zodSchema) {
                try {
                    output = this.zodSchema.parse(output);
                }
                catch (e) {
                    console.warn('Zod validation failed for node', current, e);
                }
            }
            state = { ...state, ...output }; // Type assertion fixes Partial<StateSchema> error
            // Optionally, runtime validation for annotation defaults
            if (this.schema) {
                for (const [key, channel] of Object.entries(this.schema)) {
                    if (isAnnotationInstance(channel) && channel.default && typeof state[key] === 'undefined') {
                        state[key] = channel.default();
                    }
                }
            }
            // Find next node
            let next = undefined;
            for (const edge of this.edges) {
                if (edge.from === current) {
                    if (!edge.condition || edge.condition(state)) {
                        next = edge.to;
                        break;
                    }
                }
            }
            current = next;
        }
        return state;
    }
}
exports.StateGraph = StateGraph;
// Usage example (see docs):
// const ann = Annotation.Root({
//   messages: Annotation<BaseMessage[]>({ reducer, default }),
// });
// const graph = new StateGraph(ann)
//   .addNode('foo', fn)
//   .addEdge('a', 'b')
//   .compile();
// await graph.invoke(input);
// for await (const state of graph.stream(input)) { ... }
// === End StateGraph Polyfill ===
const uuid_1 = require("uuid"); // Used for generating IDs
const crypto = __importStar(require("crypto")); // Used for a more robust hashing in Embeddings
const logger_1 = require("../../logger");
// === Tools ===
// Polyfill BaseTool to match ITool interface
class Tool {
    id;
    name;
    description;
    type;
    category;
    singleActionSchema; // For compatibility with ITool
    actions;
    constructor(name, description, options) {
        if (typeof name !== 'string' || name.length === 0)
            throw new Error('Tool name is required.');
        if (typeof description !== 'string' || description.length === 0)
            throw new Error('Tool description is required.');
        this.id = options?.id ?? name.toLowerCase().replace(/\s+/g, '_');
        this.name = name;
        this.description = description;
        this.type = options?.type ?? 'single-action';
        this.category = options?.category;
        this.singleActionSchema = options?.singleActionSchema;
        this.actions = options?.actions;
    }
    /**
       * Executes the tool's logic. Must be implemented by subclasses.
       * @param actionName - Name of the action (for multi-action tools)
       * @param input - The input for the tool, type depends on the specific tool.
       * @param context - AgentContext (optional)
       * @returns The result of the tool's execution.
       */
    async execute(actionName, input, context) {
        // By default, call the single action
        if (this.type === 'multi-action' && this.actions && actionName) {
            const action = this.actions[actionName];
            if (action && typeof action.execute === 'function') {
                return action.execute(input, context);
            }
            else {
                return { success: false, error: `Unknown action: ${actionName}` };
            }
        }
        else {
            // For single-action tools, subclasses should override this
            throw new Error(`Tool '${this.name}' does not implement execute.`);
        }
    }
    /**
       * Optional: Provide a definition for LLMs (polyfill returns basic info)
       */
    getDefinitionForModel() {
        return {
            name: this.name,
            description: this.description,
            type: this.type,
            schema: this.singleActionSchema,
            actions: this.actions
        };
    }
}
exports.Tool = Tool;
exports.BaseTool = Tool;
/**
 * Polyfill for structured tools, matching ITool and LangChain interfaces
 */
class StructuredTool extends Tool {
    schema;
    constructor(name = 'structured_tool', description = 'A tool with a structured input/output schema.', schema, options) {
        super(name, description, { ...options, singleActionSchema: schema });
        this.schema = schema ?? {};
    }
    async execute(actionName, input, context) {
        // Subclasses should override this
        throw new Error(`StructuredTool '${this.name}' does not have an implemented execute method.`);
    }
    /**
       * Default invoke method for StructuredTool.
       * Allows workflows and wrappers to call invoke on any StructuredTool instance.
       * Delegates to execute with undefined actionName.
       */
    async invoke(input, context) {
        return this.execute(undefined, input, context);
    }
}
exports.StructuredTool = StructuredTool;
// === Messages ===
/**
 * Base class for chat messages.
 */
class BaseMessage {
    content;
    type; // Helps distinguish message types
    additional_kwargs; // For extra data
    constructor(content, additional_kwargs = {}) {
        if (typeof content !== 'string')
            throw new Error('Message content must be a string.');
        this.content = content;
        this.type = 'base'; // Default type, overridden by subclasses
        this.additional_kwargs = additional_kwargs;
    }
}
exports.BaseMessage = BaseMessage;
/**
 * Represents a message from the human user.
 */
class HumanMessage extends BaseMessage {
    type = 'human';
    constructor(content, additional_kwargs = {}) {
        super(content, additional_kwargs);
    }
}
exports.HumanMessage = HumanMessage;
/**
 * Represents a message from the AI assistant.
 */
class AIMessage extends BaseMessage {
    type = 'ai';
    constructor(content, additional_kwargs = {}) {
        super(content, additional_kwargs);
    }
}
exports.AIMessage = AIMessage;
/**
 * Represents a system message (e.g., instructions, context).
 */
class ToolMessage extends BaseMessage {
    type = 'tool';
    toolName;
    toolInput;
    constructor(toolName, toolInput, content = '', additional_kwargs = {}) {
        super(content, additional_kwargs);
        this.toolName = toolName;
        this.toolInput = toolInput;
    }
}
exports.ToolMessage = ToolMessage;
/**
 * Represents a system message (e.g., instructions, context).
 */
class SystemMessage extends BaseMessage {
    type = 'system';
    constructor(content, additional_kwargs = {}) {
        super(content, additional_kwargs);
    }
}
exports.SystemMessage = SystemMessage;
/**
 * Base Embeddings class polyfill.
 * Generates deterministic, fixed-dimensional vectors based on text content using hashing.
 * Note: These embeddings are NOT semantically meaningful like real model embeddings.
 */
class Embeddings {
    dim;
    seed = 'polyfill_seed'; // Consistent seed for determinism
    constructor(dim = 384) {
        if (dim <= 0 || !Number.isInteger(dim)) {
            throw new Error('Embedding dimension must be a positive integer.');
        }
        this.dim = dim;
    }
    /**
       * Generates a deterministic vector from text using SHA256 hashing and projection.
       * @param text The input text.
       * @returns A promise resolving to a vector of numbers between -1 and 1.
       */
    async generateVector(text) {
        if (typeof text !== 'string' || text.length === 0) {
            // Return a zero vector for empty or invalid input
            return Array(this.dim).fill(0);
        }
        // Use SHA256 for a better hash distribution
        const hash = crypto.createHash('sha256').update(text + this.seed).digest();
        const vector = [];
        // Use the hash bytes to seed pseudo-random number generation for each dimension
        for (let i = 0; i < this.dim; i++) {
            // Create a dimension-specific seed from the hash
            const dimensionSeedBuffer = Buffer.concat([hash, Buffer.from([i % hash.length])]); // Use modulo for smaller hash
            const dimensionHash = crypto.createHash('sha256').update(dimensionSeedBuffer).digest();
            // Convert the first 4 bytes of the dimension hash to a number between 0 and 1
            const randomValue = dimensionHash.readUInt32BE(0) / 0xFFFFFFFF;
            // Scale to [-1, 1]
            vector.push(randomValue * 2 - 1);
        }
        // Normalize the vector (optional, but common for embeddings)
        const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
        if (norm === 0)
            return vector; // Avoid division by zero
        return vector.map(val => val / norm);
    }
    /**
       * Embeds a single query text. Conforms to LangChain `embedQuery` signature.
       * @param text The text to embed.
       * @returns A promise resolving to the embedding vector.
       */
    async embedQuery(text) {
        return this.generateVector(text);
    }
    /**
       * Embeds multiple documents (strings). Conforms to LangChain `embedDocuments` signature.
       * @param texts An array of text documents (strings).
       * @returns A promise resolving to an array of embedding vectors.
       */
    async embedDocuments(texts) {
        // Basic implementation: embed one by one. Subclasses can optimize.
        return Promise.all(texts.map(text => this.generateVector(text)));
    }
}
exports.Embeddings = Embeddings;
/**
 * Polyfill simulating OpenAIEmbeddings.
 * Extends the base Embeddings, adding simulated batching and latency.
 */
class OpenAIEmbeddings extends Embeddings {
    simulatedLatencyMs;
    batchSize; // Store batch size config
    // AsyncCaller might be used internally in a real impl, but not strictly needed in this polyfill's logic
    caller;
    constructor(options = {}) {
        // Use 'dimensions' if provided, fallback to 'dim', then default
        const dim = options.dimensions ?? options.dim ?? 1536; // Default OpenAI dimension is 1536
        super(dim);
        this.simulatedLatencyMs = options.simulatedLatencyMs ?? 50; // Default 50ms latency
        this.batchSize = options.batchSize ?? 32; // Default batch size
        // Note: openAIApiKey, modelName, configuration, client are ignored in this polyfill's logic
        // but included for constructor signature compatibility.
        // If we were using AsyncCaller, we might instantiate it here:
        // this.caller = new AsyncCaller({});
    }
    /**
       * Simulates waiting for a specified duration.
       */
    async simulateLatency() {
        if (this.simulatedLatencyMs > 0) {
            await new Promise(resolve => setTimeout(resolve, this.simulatedLatencyMs));
        }
    }
    /**
       * Embeds a single query, simulating latency. Conforms to LangChain `embedQuery` signature.
       */
    async embedQuery(text) {
        await this.simulateLatency();
        return super.generateVector(text); // Use parent's generation logic
    }
    /**
       * Embeds documents (strings) in batches, simulating latency for each batch.
       * Conforms to LangChain `embedDocuments` signature (takes string[]).
       * @param texts An array of text documents (strings).
       * @returns A promise resolving to an array of embedding vectors.
       */
    async embedDocuments(texts) {
        const results = [];
        for (let i = 0; i < texts.length; i += this.batchSize) {
            const batchTexts = texts.slice(i, i + this.batchSize);
            await this.simulateLatency(); // Simulate latency per batch
            // Embed batch items concurrently using the parent's logic
            const batchEmbeddings = await Promise.all(batchTexts.map(text => super.generateVector(text)));
            results.push(...batchEmbeddings);
        }
        return results;
    }
}
exports.OpenAIEmbeddings = OpenAIEmbeddings;
class SimilaritySearchResultItem {
    id;
    score; // Higher is better (similarity)
    metadata; // Include metadata in search results
    pageContent;
    constructor(id, score, metadata, pageContent) {
        this.id = id;
        this.score = score;
        this.metadata = metadata;
        this.pageContent = pageContent;
    }
}
exports.SimilaritySearchResultItem = SimilaritySearchResultItem;
/**
 * Base class for in-memory vector store polyfills.
 * Provides common functionality like cosine similarity and basic storage.
 */
class BaseMemoryVectorStore {
    store = new Map();
    dimension = null; // Track expected dimension
    validateVector(vector, expectedDim) {
        if (!Array.isArray(vector) || vector.length === 0 || vector.some(isNaN)) { // Check for NaN
            throw new Error('Vector must be a non-empty array of finite numbers.');
        }
        const dim = expectedDim ?? this.dimension;
        if (dim !== null && vector.length !== dim) {
            throw new Error(`Vector dimension mismatch. Expected ${dim}, got ${vector.length}.`);
        }
        if (this.dimension === null) {
            this.dimension = vector.length; // Set dimension based on first vector added
        }
    }
    cosineSimilarity(vecA, vecB) {
        if (vecA.length !== vecB.length) {
            // Should ideally be caught by validateVector, but double-check
            console.warn(`Cosine similarity dimension mismatch: ${vecA.length} vs ${vecB.length}`);
            return 0; // Or handle as appropriate (e.g., NaN, error)
        }
        let dotProduct = 0;
        let normA = 0;
        let normB = 0;
        for (let i = 0; i < vecA.length; i++) {
            dotProduct += vecA[i] * vecB[i];
            normA += vecA[i] * vecA[i];
            normB += vecB[i] * vecB[i];
        }
        const magnitudeA = Math.sqrt(normA);
        const magnitudeB = Math.sqrt(normB);
        if (magnitudeA === 0 || magnitudeB === 0) {
            return 0; // Cosine similarity of a zero vector is undefined, return 0
        }
        return dotProduct / (magnitudeA * magnitudeB);
    }
    /**
       * Add or update vectors with associated text content and metadata.
       * @param items Array of items containing id, vector, pageContent, and metadata.
       */
    async addVectors(items) {
        for (const item of items) {
            this.validateVector(item.vector); // Validate against store's dimension
            if (typeof item.pageContent !== 'string')
                throw new Error('VectorStoreItem must include string pageContent.');
            this.store.set(item.id, { ...item }); // Store a copy including pageContent and metadata
        }
    }
    /**
       * Retrieve vector items by IDs.
       * @param ids Array of vector IDs.
       * @returns A Map of ID to VectorStoreItem for found items.
       */
    async getVectorsByIds(ids) {
        const results = new Map();
        for (const id of ids) {
            const item = this.store.get(id);
            if (item) {
                results.set(id, item);
            }
        }
        return results;
    }
    /**
       * Delete vectors by IDs.
       * @param ids Array of vector IDs.
       * @returns An array of IDs that were successfully deleted.
       */
    async deleteVectors(ids) {
        const deletedIds = [];
        for (const id of ids) {
            if (this.store.delete(id)) {
                deletedIds.push(id);
            }
        }
        // console.debug(`Deleted ${deletedIds.length} vectors out of ${ids.length} requested.`);
        if (this.store.size === 0) {
            this.dimension = null; // Reset dimension if store becomes empty
        }
        return deletedIds; // Return IDs actually deleted
    }
    /**
       * Remove all vectors from the store.
       */
    async clear() {
        this.store.clear();
        this.dimension = null; // Reset dimension
        // console.debug("Cleared all vectors from memory store.");
    }
    /**
       * Find the top-K most similar vectors to a query vector using cosine similarity.
       * @param queryVector The vector to search against.
       * @param k The number of results to return.
       * @param filter Optional function or object to filter items before scoring.
       *               If a function, it receives VectorStoreItem and returns boolean.
       *               If an object, it's treated as a metadata exact-match filter.
       * @returns An array of search results with IDs, scores, and metadata/content.
       */
    async similaritySearch(queryVector, k, filter) {
        this.validateVector(queryVector, this.dimension); // Validate query vector dimension
        let candidates = Array.from(this.store.values());
        if (filter) {
            if (typeof filter === 'function') {
                candidates = candidates.filter((item) => filter(item));
            }
            else if (typeof filter === 'object') {
                candidates = candidates.filter(item => Object.entries(filter).every(([key, value]) => item.metadata && item.metadata[key] === value));
            }
        }
        if (candidates.length === 0) {
            return [];
        }
        const scoredResults = candidates.map(item => ({
            id: item.id,
            score: this.cosineSimilarity(queryVector, item.vector),
            metadata: item.metadata,
            pageContent: item.pageContent,
        }));
        // Sort by score descending (higher score is more similar)
        scoredResults.sort((a, b) => b.score - a.score);
        // Return top k results
        return scoredResults.slice(0, k);
    }
}
/**
 * PineconeStore Polyfill - In-memory simulation using BaseMemoryVectorStore.
 */
class PineconeStore extends BaseMemoryVectorStore {
    embeddings = null; // Store embeddings if used for LangChain compatibility
    namespace; // Simulate namespace
    // Simulate static factory method (returns an instance)
    // Conforms to LangChain's PineconeStore.fromExistingIndex signature (or similar)
    static async fromExistingIndex(embeddings, _config) {
        // In a real scenario, config.pineconeIndex would be used to connect.
        // Here, we just create a new instance and store embeddings and namespace.
        const store = new PineconeStore();
        store.embeddings = embeddings; // Store for addDocuments
        store.namespace = _config.namespace; // Store namespace
        // Persistence/loading logic based on namespace would go here if implemented.
        // console.debug(`PineconeStore polyfill created (index: ${config.pineconeIndex?.indexName || 'dummy'}, namespace: ${config.namespace || 'default'})`);
        return store;
    }
    // --- Methods mirroring Pinecone Client behavior (adjusted for in-memory polyfill) ---
    /**
       * Add or update vectors. Simulates Pinecone's upsert.
       * @param vectors Array of vector items. Each must have id, values, and metadata.
       */
    async upsert({ vectors, namespace }) {
        // This polyfill does not currently handle namespaces within the storage map
        // To implement namespaces, 'store' would need to be Map<string | undefined, Map<string, VectorStoreItem>>
        // For simplicity, we ignore the namespace parameter here, operating on a flat map.
        if (namespace !== this.namespace && this.namespace !== undefined) {
            console.warn(`PineconeStore polyfill received upsert for namespace '${namespace}' but configured with '${this.namespace}'. Using configured namespace.`);
            // Or throw error depending on desired strictness
        }
        const items = vectors.map(v => ({
            id: v.id,
            vector: v.values,
            metadata: v.metadata,
            pageContent: v.metadata?.pageContent || v.metadata?.text || '', // Attempt to get content from metadata
        }));
        await super.addVectors(items);
        // Persistence logic (saving to disk/mock) would go here if implemented.
        return { upsertedCount: items.length }; // Simulate response
    }
    /**
       * Fetch vectors by IDs. Simulates Pinecone's fetch.
       */
    async fetch({ ids, namespace }) {
        // Ignore namespace for now as per upsert limitation
        if (namespace !== this.namespace && this.namespace !== undefined) {
            console.warn(`PineconeStore polyfill received fetch for namespace '${namespace}' but configured with '${this.namespace}'. Using configured namespace.`);
        }
        const itemsMap = await super.getVectorsByIds(ids);
        const resultVectors = {};
        itemsMap.forEach(item => {
            // Remove pageContent from metadata when returning in fetch results, as Pinecone doesn't store it like this
            const metadataToReturn = { ...item.metadata };
            delete metadataToReturn.pageContent; // Or handle specifically
            resultVectors[item.id] = { id: item.id, values: item.vector, metadata: metadataToReturn };
        });
        return { vectors: resultVectors, namespace: this.namespace || '' }; // Simulate response
    }
    /**
       * Delete vectors by IDs or query. Simulates Pinecone's delete.
       */
    async delete({ ids, deleteAll, namespace }) {
        // Ignore namespace for now
        if (namespace !== this.namespace && this.namespace !== undefined) {
            console.warn(`PineconeStore polyfill received delete for namespace '${namespace}' but configured with '${this.namespace}'. Using configured namespace.`);
        }
        if (deleteAll) {
            await super.clear();
        }
        else if (ids && ids.length > 0) {
            await super.deleteVectors(ids);
        }
        else {
            // Pinecone delete usually requires either ids or deleteAll
            console.warn('PineconeStore polyfill \'delete\' called without \'ids\' or \'deleteAll\'. No action taken.');
        }
        // Persistence logic would go here.
    }
    /**
       * Query for similar vectors. Simulates Pinecone's query.
       */
    async query({ vector, topK, filter, includeMetadata, includeValues, namespace }) {
        // Ignore namespace for now
        if (namespace !== this.namespace && this.namespace !== undefined) {
            console.warn(`PineconeStore polyfill received query for namespace '${namespace}' but configured with '${this.namespace}'. Using configured namespace.`);
        }
        const results = await super.similaritySearch(vector, topK, filter);
        // Format results for Pinecone query response
        const matches = results.map(item => {
            const match = {
                id: item.id,
                score: item.score,
                metadata: item.metadata,
                pageContent: item.pageContent, // Include original text from polyfill item
            };
            if (includeValues) {
                // Need to fetch the actual vector values if requested
                const vectorItem = this.store.get(item.id);
                if (vectorItem) {
                    match.values = vectorItem.vector;
                }
            }
            return match;
        });
        return { matches };
    }
    // --- LangChain VectorStore Compatibility Methods ---
    /**
       * Adds documents using the stored embeddings instance to generate vectors.
       * Conforms to LangChain's `addDocuments` signature.
       * @param documents Array of Documents.
       */
    async addDocuments(documents) {
        if (!this.embeddings) {
            throw new Error('Embeddings instance not available in PineconeStore polyfill for addDocuments.');
        }
        const texts = documents.map(doc => doc.pageContent);
        // Use the correct Embeddings method signature (takes string[])
        const vectors = await this.embeddings.embedDocuments(texts);
        const items = documents.map((doc, i) => ({
            id: doc.id ?? doc.metadata?.id ?? doc.metadata?.doc_id ?? (0, uuid_1.v4)(), // Use doc ID, metadata ID, or generate
            vector: vectors[i],
            metadata: { ...doc.metadata }, // Store metadata
            pageContent: doc.pageContent, // Store original content
        }));
        await super.addVectors(items); // Use base addVectors which stores all item properties
        // Persistence logic (saving) would go here.
    }
    /**
       * Performs similarity search using a query string and the stored embeddings.
       * Conforms to LangChain's `similaritySearchWithScore` signature.
       */
    async similaritySearchWithScore(query, k, filter) {
        if (!this.embeddings) {
            throw new Error('Embeddings instance not available in PineconeStore polyfill for similaritySearchWithScore.');
        }
        const queryEmbedding = await this.embeddings.embedQuery(query);
        // Reuse the BaseMemoryVectorStore similarity search with object filter
        const results = await super.similaritySearch(queryEmbedding, k, filter);
        // Map results back to [Document, score] tuple format
        const finalResults = results.map(item => {
            // Reconstruct Document from the stored item
            const doc = new Document({ pageContent: item.pageContent, metadata: item.metadata, id: item.id });
            return [doc, item.score];
        });
        return finalResults;
    }
    // Optional: Provide a dummy `asRetriever` method for compatibility
    asRetriever(k) {
        return {
            getRelevantDocuments: async (query) => {
                // Use similarity search but only return the Documents, not scores
                const results = await this.similaritySearchWithScore(query, k ?? 4); // Default k=4 for retrievers
                return results.map(([doc, score]) => doc);
            }
        };
    }
}
exports.PineconeStore = PineconeStore;
/**
 * Chroma Polyfill - In-memory simulation using BaseMemoryVectorStore.
 * Implements the IChromaPolyfillInstance interface.
 */
class Chroma extends BaseMemoryVectorStore {
    collectionName = 'default_collection';
    persistDirectory = null;
    collectionMetadata = {};
    embeddings; // Embeddings instance is required
    // Private constructor, use fromDocuments or potentially another factory
    constructor(embeddings, config) {
        super();
        if (!embeddings)
            throw new Error('Chroma polyfill requires an Embeddings instance.');
        this.embeddings = embeddings; // Use embeddings instance
        if (config) {
            this.collectionName = config.collectionName ?? this.collectionName;
            this.persistDirectory = config.persistDirectory ?? this.persistDirectory;
            this.collectionMetadata = config.collectionMetadata ?? this.collectionMetadata;
        }
        // Persistence logic (loading from persistDirectory) would go here if implemented.
        // console.debug(`Chroma polyfill instance created for collection '${this.collectionName}'`);
    }
    /**
       * Static factory method to create/load a Chroma instance.
       * Simulates connecting to a persistent collection and adding initial documents.
       * Conforms to LangChain's `Chroma.fromDocuments` signature.
       * @param documents Initial documents to add (can be empty).
       * @param embeddings Embeddings instance.
       * @param config Configuration options.
       * @returns A Promise resolving to a Chroma instance.
       */
    static async fromDocuments(documents, // Can be empty array to just connect
    embeddings, config) {
        // Create instance using the private constructor
        const instance = new Chroma(embeddings, config);
        // If documents are provided, add them using the instance method
        if (documents && documents.length > 0) {
            // console.debug(`Chroma.fromDocuments: Adding ${documents.length} initial documents.`);
            // Use the instance's addDocuments method which handles embedding
            await instance.addDocuments(documents);
        }
        else {
            // console.debug(`Chroma.fromDocuments: Connecting to collection '${instance.collectionName}'.`);
            // Load from persistence logic based on instance.persistDirectory would go here.
        }
        return instance;
    }
    // --- IChromaPolyfillInstance Implementation ---
    /**
       * Adds documents to the collection, generating embeddings using the instance's Embeddings.
       * Conforms to `IChromaPolyfillInstance.addDocuments`.
       * @param documents Array of Documents.
       * @param options Optional options including ids.
       * @returns A Promise resolving to an array of added document IDs.
       */
    async addDocuments(documents, options) {
        // Validate documents and extract text
        const texts = documents.map(doc => {
            if (typeof doc.pageContent !== 'string' || doc.pageContent.trim().length === 0) {
                throw new Error('All documents must have non-empty string pageContent for embedding.');
            }
            return doc.pageContent;
        });
        // Generate embeddings using the instance's embeddings
        const vectors = await this.embeddings.embedDocuments(texts);
        const items = [];
        const addedIds = [];
        for (let i = 0; i < documents.length; i++) {
            const doc = documents[i];
            const vector = vectors[i];
            // Determine ID: use provided option, metadata ID, or generate new
            const id = options?.ids?.[i] ?? doc.id ?? doc.metadata?.id ?? doc.metadata?.doc_id ?? (0, uuid_1.v4)();
            items.push({
                id: id,
                vector: vector,
                metadata: {
                    ...(doc.metadata || {}),
                    doc_id: id, // Ensure doc_id is present in metadata
                },
                pageContent: doc.pageContent, // Store original content
            });
            addedIds.push(id);
        }
        await super.addVectors(items); // Use base addVectors which stores all item properties
        // Persistence logic (saving) would go here if implemented.
        return addedIds;
    }
    /**
       * Retrieves data based on IDs or filters.
       * Conforms to `IChromaPolyfillInstance.get`.
       */
    async get(options) {
        const includeFields = new Set(options?.include ?? ['metadatas', 'documents']); // Default includes
        let results = [];
        if (options?.ids && options.ids.length > 0) {
            const foundMap = await super.getVectorsByIds(options.ids);
            // Ensure results are in the order of requested IDs if possible (getVectorsByIds provides map)
            results = options.ids.map(id => foundMap.get(id)).filter((item) => !!item);
        }
        else {
            // Implement basic 'where' filter (exact match on metadata keys/values)
            const filterFn = options?.where ? (item) => Object.entries(options.where).every(([key, value]) => item.metadata && item.metadata[key] === value) : undefined;
            // Iterate through all stored items and apply filter
            results = Array.from(this.store.values()).filter(item => filterFn ? filterFn(item) : true);
        }
        // Apply limit and offset
        const offset = options?.offset ?? 0;
        const limit = options?.limit ?? results.length;
        const paginatedResults = results.slice(offset, offset + limit);
        // Construct response based on 'include' fields
        const response = {
            ids: paginatedResults.map(item => item.id),
            embeddings: includeFields.has('embeddings') ? paginatedResults.map(item => item.vector) : null,
            metadatas: includeFields.has('metadatas') ? paginatedResults.map(item => item.metadata) : null,
            // Retrieve 'pageContent' from the stored item if requested
            documents: includeFields.has('documents') ? paginatedResults.map(item => item.pageContent ?? null) : null,
        };
        return response;
    }
    /**
       * Deletes data based on IDs or filters.
       * Conforms to `IChromaPolyfillInstance.delete`.
       * @returns A Promise resolving to an array of IDs that were targeted for deletion.
       */
    async delete(options) {
        let idsToDelete = [];
        if (options?.ids && options.ids.length > 0) {
            idsToDelete = options.ids;
        }
        else if (options?.where) {
            // Find IDs matching the 'where' filter
            const filterFn = (item) => Object.entries(options.where).every(([key, value]) => item.metadata && item.metadata[key] === value);
            idsToDelete = Array.from(this.store.values()).filter(filterFn).map(item => item.id);
        }
        else if (options?.whereDocument) {
            // Basic implementation of whereDocument - checks if content contains a substring
            const filterFn = (item) => Object.entries(options.whereDocument).every(([key, value]) => key === '$contains' && typeof value === 'string' && typeof item.pageContent === 'string' && item.pageContent.includes(value));
            idsToDelete = Array.from(this.store.values()).filter(filterFn).map(item => item.id);
        }
        else {
            // If no criteria provided, assume delete all, consistent with some Chroma clients
            logger_1.logger.warn('Chroma polyfill \'delete\' called without \'ids\' or \'where\' or \'whereDocument\'. Assuming delete all.');
            idsToDelete = Array.from(this.store.keys()); // Get all current IDs
        }
        if (idsToDelete.length > 0) {
            const successfullyDeleted = await super.deleteVectors(idsToDelete); // Use base delete logic
            // Persistence logic would go here.
            // Return the list of IDs that were successfully deleted by the base store
            return successfullyDeleted;
        }
        // Return the list of IDs that were *intended* for deletion if none were found or if no action was taken
        return idsToDelete;
    }
    /**
       * Performs similarity search using a query string.
       * Conforms to `IChromaPolyfillInstance.similaritySearchWithScore`.
       * @returns A Promise resolving to an array of [Document, score] tuples.
       */
    async similaritySearchWithScore(query, k, filter) {
        const queryEmbedding = await this.embeddings.embedQuery(query);
        // Reuse the BaseMemoryVectorStore similarity search with object filter
        const results = await super.similaritySearch(queryEmbedding, k, filter);
        // Map results back to [Document, score] tuple format
        const finalResults = results.map(item => {
            // Reconstruct Document from the stored item
            const doc = new Document({ pageContent: item.pageContent, metadata: item.metadata, id: item.id }); // Use stored pageContent and metadata
            return [doc, item.score];
        });
        return finalResults;
    }
    /**
       * Optional: Performs similarity search using a query vector.
       * Conforms to `IChromaPolyfillInstance.similaritySearchByVectorWithScore?`.
       */
    async similaritySearchByVectorWithScore(embedding, k, filter) {
        // Reuse the BaseMemoryVectorStore similarity search with object filter
        const results = await super.similaritySearch(embedding, k, filter);
        // Map results back to [Document, score] tuple format
        const finalResults = results.map(item => {
            // Reconstruct Document from the stored item
            const doc = new Document({ pageContent: item.pageContent, metadata: item.metadata, id: item.id }); // Use stored pageContent and metadata
            return [doc, item.score];
        });
        return finalResults;
    }
    /**
       * Optional: Counts items in the collection.
       * Conforms to `IChromaPolyfillInstance.count?`.
       */
    async count() {
        return this.store.size;
    }
    // Optional: Provide a dummy `asRetriever` method for compatibility if Chroma is used as a retriever source
    asRetriever(k) {
        return {
            getRelevantDocuments: async (query) => {
                // Use similarity search but only return the Documents, not scores
                const results = await this.similaritySearchWithScore(query, k ?? 4); // Default k=4 for retrievers
                return results.map(([doc]) => doc);
            }
        };
    }
}
exports.Chroma = Chroma;
// === Text Splitter ===
/**
 * RecursiveCharacterTextSplitter Polyfill
 * Splits text based on separators, aiming for chunk size with overlap.
 * Improved merging logic for better adherence to chunk size.
 */
class RecursiveCharacterTextSplitter {
    chunkSize;
    chunkOverlap;
    separators;
    keepSeparator; // LangChain option
    // lengthFunction is part of LangChain interface, but implementing generic
    // length based on character count is sufficient for this polyfill.
    // private readonly lengthFunction: (text: string) => number;
    constructor(options) {
        this.chunkSize = options.chunkSize ?? 1000;
        this.chunkOverlap = options.chunkOverlap ?? 200;
        // Default separators mimic LangChain's common defaults
        this.separators = options.separators ?? ['\n\n', '\n', ' ', ''];
        // Ensure empty string is always the last separator if present
        if (this.separators.includes('')) {
            this.separators = this.separators.filter(s => s !== '').concat('');
        }
        else {
            this.separators = this.separators.concat(''); // Ensure empty string fallback
        }
        this.keepSeparator = options.keepSeparator ?? false; // Default false
        if (this.chunkSize <= 0)
            throw new Error('chunkSize must be positive');
        if (this.chunkOverlap < 0)
            throw new Error('chunkOverlap must be non-negative');
        if (this.chunkOverlap >= this.chunkSize)
            throw new Error('chunkOverlap must be smaller than chunkSize');
        // lengthFunction is not used in the polyfill's logic, but stored if needed for other purposes
        // this.lengthFunction = options.lengthFunction ?? ((text) => text.length);
    }
    /**
       * Splits text recursively based on the provided separators.
       * This function aims to break text into segments smaller than chunkSize
       * using the most specific available separator first.
       * @param text The text to split.
       * @param separators The prioritized list of separators.
       * @returns An array of text segments.
       */
    _splitTextRecursive(text, separators) {
        // Base case 1: No text left
        if (text.length === 0)
            return [];
        // Base case 2: No separators left or empty string separator is the only one left
        if (separators.length === 0 || (separators.length === 1 && separators[0] === '')) {
            // If the text is still too big and only "" separator is left,
            // or no separators at all, return the text as one segment.
            // The merging logic will handle breaking this down by character if needed.
            return [text];
        }
        const currentSeparator = separators[0];
        const remainingSeparators = separators.slice(1);
        // If the current separator splits the text
        if (currentSeparator && text.includes(currentSeparator)) {
            const parts = text.split(currentSeparator);
            const segments = [];
            for (let i = 0; i < parts.length; i++) {
                const part = parts[i];
                // Recursively split each part
                const subSegments = this._splitTextRecursive(part, remainingSeparators);
                segments.push(...subSegments);
                // Add the separator back if keepSeparator is true, but only between parts
                if (this.keepSeparator && i < parts.length - 1) {
                    segments.push(currentSeparator);
                }
            }
            return segments;
        }
        else {
            // If the current separator doesn't split the text, try the next ones
            return this._splitTextRecursive(text, remainingSeparators);
        }
    }
    /**
       * Joins small text segments into chunks respecting chunkSize and overlap.
       * This logic is crucial to ensure chunks are close to chunkSize and handle overlap correctly.
       * @param segments Array of text segments (produced by recursive splitting).
       * @returns Array of merged text chunks.
       */
    _mergeChunks(segments) {
        const chunks = [];
        let currentChunk = []; // Stores the segments of the current chunk
        let currentLength = 0;
        const separator = ' '; // Use a space for joining segments within a chunk
        for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];
            const segmentLength = segment.length;
            const lengthWithSeparator = currentLength > 0 ? currentLength + separator.length + segmentLength : segmentLength;
            // If adding the next segment would exceed the chunk size
            if (lengthWithSeparator > this.chunkSize) {
                // If we have something in the current chunk, finalize it
                if (currentChunk.length > 0) {
                    chunks.push(currentChunk.join(separator));
                    // Prepare for overlap: Find segments for the beginning of the next chunk
                    let overlapStartIndex = currentChunk.length - 1;
                    let overlapText = '';
                    const overlapSegments = [];
                    // Iterate backward through the current chunk's segments to find overlap
                    while (overlapStartIndex >= 0 && overlapText.length < this.chunkOverlap) {
                        const prevSegment = currentChunk[overlapStartIndex];
                        // Calculate length if this segment were added to the overlap text
                        const lengthIfAdded = prevSegment.length + (overlapText.length > 0 ? separator.length : 0);
                        if (overlapText.length + lengthIfAdded > this.chunkOverlap && overlapText.length > 0) {
                            // If adding the next segment makes overlap too big, stop *before* adding it
                            break;
                        }
                        overlapSegments.unshift(prevSegment); // Add segment to the front of overlap list
                        overlapText = overlapSegments.join(separator);
                        overlapStartIndex--;
                    }
                    // Start the new chunk with the calculated overlap segments and the current segment
                    currentChunk = [...overlapSegments, segment];
                    currentLength = currentChunk.join(separator).length;
                }
                else {
                    // This case is tricky: a single segment is larger than chunkSize.
                    // Recursive splitting should ideally prevent this, but if it happens
                    // (e.g., single very long line and "\n" is the only separator), we must
                    // split it here. A basic character-by-character split is the fallback.
                    console.warn(`RecursiveCharacterTextSplitter: Encountered segment larger than chunkSize (${segmentLength} > ${this.chunkSize}). Forcing character split.`);
                    const forceSplitChunks = [];
                    for (let j = 0; j < segment.length; j += this.chunkSize) {
                        forceSplitChunks.push(segment.substring(j, j + this.chunkSize));
                    }
                    chunks.push(...forceSplitChunks);
                    currentChunk = [segment]; // Start new chunk attempt with the large segment
                    currentLength = segmentLength; // Reset length
                    // After force splitting, the current segment needs to be processed again
                    // or this loop index needs adjustment. Let's just process the large segment
                    // as a single chunk and then clear currentChunk to start fresh.
                    currentChunk = [];
                    currentLength = 0;
                }
            }
            else {
                // Add segment to the current chunk
                currentChunk.push(segment);
                currentLength = lengthWithSeparator;
            }
        }
        // Add the last remaining chunk if any
        if (currentChunk.length > 0) {
            chunks.push(currentChunk.join(separator));
        }
        // Handle the edge case where the *very first* segment is larger than chunkSize
        // and the _mergeChunks logic above just added it. This requires an extra pass or
        // more complex logic within the loop. A simpler approach for the polyfill:
        // after merging, check any resulting chunks that are still > chunkSize and split them by character.
        const finalChunks = [];
        for (const chunk of chunks) {
            if (chunk.length > this.chunkSize) {
                console.warn(`RecursiveCharacterTextSplitter: Final chunk larger than chunkSize after merging (${chunk.length} > ${this.chunkSize}). Forcing character split.`);
                for (let j = 0; j < chunk.length; j += this.chunkSize) {
                    finalChunks.push(chunk.substring(j, j + this.chunkSize));
                }
            }
            else if (chunk.length > 0) { // Also ensure no empty chunks
                finalChunks.push(chunk);
            }
        }
        return finalChunks.filter(Boolean); // Remove any potential empty strings
    }
    /**
       * Splits the input text into chunks based on configured separators, size, and overlap.
       * This is the primary method to call.
       * @param text The text to split.
       * @returns A promise resolving to an array of text chunks.
       */
    async splitText(text) {
        if (typeof text !== 'string' || text.length === 0) {
            return [];
        }
        // Step 1: Recursively split text into segments using prioritized separators.
        // This aims to break the text at logical points (paragraphs, lines, words).
        const initialSegments = this._splitTextRecursive(text, this.separators);
        // Step 2: Merge these potentially small segments into chunks of the desired size, handling overlap.
        const finalChunks = this._mergeChunks(initialSegments);
        return finalChunks;
    }
}
exports.RecursiveCharacterTextSplitter = RecursiveCharacterTextSplitter;
// === Documents ===
/**
 * Represents a piece of text, often with associated metadata.
 * Conforms to LangChain Document structure.
 */
class Document {
    id; // Optional ID for tracking
    pageContent; // Main text content
    metadata; // Associated metadata
    constructor(fields) {
        if (typeof fields.pageContent !== 'string')
            throw new Error('Document requires string pageContent.');
        this.pageContent = fields.pageContent;
        this.metadata = fields.metadata ?? {};
        this.id = fields.id;
    }
}
exports.Document = Document;
// === Prompts ===
/**
 * Placeholder for chat prompt templating functionality.
 */
class ChatPromptTemplate {
    // Minimal implementation for type checking and basic usage
    messages; // Stores prompt components
    constructor(messages) {
        this.messages = messages; // Store prompt components
    }
    /**
       * Static factory method to create a template from message types or strings.
       * Conforms to LangChain `ChatPromptTemplate.fromMessages`.
       */
    static fromMessages(messages) {
        // console.debug("ChatPromptTemplate.fromMessages called (polyfill)");
        // In a real implementation, this would parse templates and validate message types
        return new ChatPromptTemplate(messages);
    }
    /**
       * Formats the prompt into a sequence of chat messages given input values.
       * Conforms to LangChain `formatMessages`.
       */
    async formatMessages(values) {
        // console.debug("ChatPromptTemplate.formatMessages called (polyfill)", values);
        // Basic polyfill logic: create dummy messages based on keys and stored components
        const formattedMessages = [];
        for (const msgComponent of this.messages) {
            if (typeof msgComponent === 'string') {
                // Assume it's a simple string template, replace placeholders
                let content = msgComponent;
                for (const key in values) {
                    content = content.replace(`{${key}}`, String(values[key]));
                }
                formattedMessages.push(new SystemMessage(content)); // Use SystemMessage for simplicity
            }
            else if (msgComponent instanceof MessagesPlaceholder) {
                // Insert messages from the specified variable name
                const messagesToInsert = values[msgComponent.variable_name];
                if (Array.isArray(messagesToInsert)) {
                    formattedMessages.push(...messagesToInsert.filter(m => m instanceof BaseMessage)); // Only add BaseMessage instances
                }
                else if (messagesToInsert) {
                    console.warn(`MessagesPlaceholder variable '${msgComponent.variable_name}' did not contain an array of messages.`);
                }
            }
            else if (typeof msgComponent === 'function') {
                // Assume it's a function returning a message or message part
                try {
                    const result = await Promise.resolve(msgComponent(values));
                    if (result instanceof BaseMessage) {
                        formattedMessages.push(result);
                    }
                    else if (typeof result === 'string') {
                        formattedMessages.push(new SystemMessage(result));
                    }
                    else {
                        console.warn('ChatPromptTemplate polyfill: Function component returned unexpected type.');
                    }
                }
                catch (error) {
                    console.error('ChatPromptTemplate polyfill: Error in function component:', error);
                }
            }
            else {
                console.warn('ChatPromptTemplate polyfill: Unknown message component type.');
            }
        }
        if (formattedMessages.length === 0 && Object.keys(values).length > 0) {
            // Fallback if no components added messages, but there were inputs
            formattedMessages.push(new SystemMessage(`Formatted prompt with keys: ${Object.keys(values).join(', ')}`));
        }
        else if (formattedMessages.length === 0) {
            // Fallback for empty prompt / no inputs
            formattedMessages.push(new SystemMessage('Empty prompt'));
        }
        return formattedMessages;
    }
    /**
       * Binds configuration to the prompt template (placeholder).
       */
    bind(_config) {
        // In a real implementation, this would create a new bound instance
        // For polyfill, just return `this` as no binding logic is implemented.
        // console.debug("ChatPromptTemplate.bind called (polyfill)", config);
        return this;
    }
    /**
       * Pipes the output of this prompt template to a model or other runnable.
       * @param target The model or runnable to pipe to
       * @returns A runnable chain that will execute this prompt and then the target
       */
    pipe(target) {
        // Create a simple chain object that will format messages and then invoke the target
        return {
            invoke: async (input, options) => {
                const messages = await this.formatMessages(input);
                return target.invoke(messages, options);
            }
        };
    }
}
exports.ChatPromptTemplate = ChatPromptTemplate;
/**
 * Placeholder for inserting message history or other message sequences into prompts.
 * Conforms to LangChain `MessagesPlaceholder`.
 */
class MessagesPlaceholder {
    variable_name; // The name of the variable in the prompt values
    constructor(variable_name) {
        if (typeof variable_name !== 'string' || variable_name.length === 0)
            throw new Error('MessagesPlaceholder requires a variable_name.');
        this.variable_name = variable_name;
        // console.debug(`MessagesPlaceholder created for variable: ${variable_name}`);
    }
}
exports.MessagesPlaceholder = MessagesPlaceholder;
// === Runnables ===
/**
 * Placeholder for Runnable configuration options.
 * Conforms to LangChain `RunnableConfig`.
 */
class RunnableConfig {
    // Add common config properties if needed for type compatibility
    callbacks; // Placeholder for callback managers (CallbackManager or CallbackManagerForLLMRun)
    tags;
    metadata;
    configurable;
    maxConcurrency;
    recursionLimit;
    runName;
    constructor(_config) {
        // Assign provided properties to the instance
        Object.assign(this, _config);
    }
}
exports.RunnableConfig = RunnableConfig;
// === Language Models ===
/**
 * Placeholder for the base language model interface.
 * Conforms to LangChain `BaseLanguageModel`.
 */
class BaseLanguageModel {
}
exports.BaseLanguageModel = BaseLanguageModel;
/**
 * Placeholder for the base chat model interface.
 * Extends BaseLanguageModel. Conforms to LangChain `BaseChatModel`.
 */
class BaseChatModel extends BaseLanguageModel {
    // Minimal implementation for invoke, takes array of BaseMessage or string, returns AIMessage
    async invoke(input, options) {
        // console.debug("BaseChatModel.invoke called (polyfill)", { input, options });
        const inputText = Array.isArray(input) ?
            input.map(m => `${m.type}: ${typeof m.content === 'string' ? m.content : JSON.stringify(m.content)}`).join('\n')
            : input;
        // Simulate a basic AI response based on the input
        const simulatedResponse = `Polyfill AI response to: "${inputText.substring(0, 100)}${inputText.length > 100 ? '...' : ''}"`;
        // If a callback manager is provided in config, simulate callback events
        if (options?.config?.callbacks instanceof CallbackManagerForLLMRun) {
            const runId = (0, uuid_1.v4)(); // Simulate a run ID
            const prompts = Array.isArray(input) ? [input.map(m => `${m.type}: ${m.content}`).join('\n')] : [input]; // Simplified prompts array
            await options.config.callbacks.handleLLMStart({ name: 'BaseChatModelPolyfill' }, prompts, { runId });
            // Simulate async work
            await new Promise(resolve => setTimeout(resolve, 10));
            await options.config.callbacks.handleLLMEnd({ generations: [[{ text: simulatedResponse }]] }, { runId }); // Simulate LangChain output format
        }
        return new AIMessage(simulatedResponse);
    }
}
exports.BaseChatModel = BaseChatModel;
// === Async Caller ===
/**
 * Placeholder for managing asynchronous calls with concurrency, retries, etc.
 * Basic polyfill just executes the function directly.
 * Conforms to LangChain `AsyncCaller`.
 */
class AsyncCaller {
    // Store config options if needed, but logic is minimal here
    maxConcurrency;
    maxRetries;
    constructor(options = {}) {
        // Store options, although not fully implemented in the polyfill's logic
        this.maxConcurrency = options.maxConcurrency ?? Infinity;
        this.maxRetries = options.maxRetries ?? 0;
        logger_1.logger.debug('AsyncCaller created with config:', {
            maxConcurrency: this.maxConcurrency,
            maxRetries: this.maxRetries
        });
    }
    /**
       * Executes the provided asynchronous function.
       * @param func The async function to call.
       * @param args Arguments to pass to the function.
       * @returns The result of the function call.
       */
    async call(func, ...args) {
        // A real implementation would use promises and queueing for concurrency
        // and try-catch loops with delays for retries.
        // This polyfill just directly calls the function.
        // console.debug("AsyncCaller.call executed (polyfill) - calling wrapped function.");
        return func(...args);
    }
}
exports.AsyncCaller = AsyncCaller;
// === Memory Components ===
/**
 * Simple in-memory store for chat messages.
 * Conforms to LangChain `ChatMessageHistory`.
 */
class ChatMessageHistory {
    messages = [];
    /**
       * Adds a single message to the history.
       */
    async addMessage(message) {
        if (!(message instanceof BaseMessage))
            throw new Error('Only instances of BaseMessage can be added to ChatMessageHistory.');
        this.messages.push(message);
    }
    /**
       * Retrieves all messages in the history.
       * @returns A promise resolving to an array of BaseMessage objects.
       */
    async getMessages() {
        // Return a copy to prevent external modification of the internal array
        return [...this.messages];
    }
    /**
       * Clears all messages from the history.
       */
    async clear() {
        this.messages = [];
    }
    /**
       * Convenience method to add a HumanMessage.
       * @param message The human message content.
       */
    async addUserMessage(message) {
        await this.addMessage(new HumanMessage(message));
    }
    /**
       * Convenience method to add an AIMessage.
       * @param message The AI message content.
       */
    async addAIChatMessage(message) {
        await this.addMessage(new AIMessage(message));
    }
}
exports.ChatMessageHistory = ChatMessageHistory;
/**
 * Base class for chat memory systems. Defines common properties and abstract methods.
 * Conforms to LangChain `BaseChatMemory`.
 */
class BaseChatMemory {
    memoryKey; // Key for memory variables (e.g., "history", "chat_history")
    inputKey; // Optional key for input
    outputKey; // Optional key for output
    returnMessages; // Whether loadMemoryVariables should return BaseMessage[] or string
    constructor(_config) {
        // Assign provided properties, using defaults where appropriate
        this.memoryKey = _config.memoryKey ?? 'history'; // Default memory key
        this.inputKey = _config.inputKey;
        this.outputKey = _config.outputKey;
        this.returnMessages = _config.returnMessages ?? false; // Default to returning string
        // chatHistory must be provided by subclass or config
        // this.chatHistory = config.chatHistory; // This needs to be assigned by the subclass/factory
        // console.debug("BaseChatMemory constructor called (polyfill)", { memoryKey: this.memoryKey, returnMessages: this.returnMessages });
    }
}
exports.BaseChatMemory = BaseChatMemory;
/**
 * Base Memory class for all memory implementations.
 * Provides common interface for memory operations.
 */
class BaseMemory {
}
exports.BaseMemory = BaseMemory;
/**
 * BufferMemory Polyfill: Stores recent messages in a buffer in memory.
 * Conforms to LangChain `BufferMemory`.
 */
class BufferMemory extends BaseChatMemory {
    chatHistory;
    k; // Number of messages to keep (from the end of history)
    constructor(_config = {}) {
        super(_config);
        // Ensure chatHistory is provided or create a new one
        this.chatHistory = _config.chatHistory ?? new ChatMessageHistory();
        this.k = _config.k ?? Infinity; // Default to keeping all messages
        if (this.k < 0)
            throw new Error('BufferMemory \'k\' must be non-negative.');
        // console.debug("BufferMemory instance created (polyfill)", { k: this.k });
    }
    /**
       * Loads the last `k` messages from the chat history.
       * @param _ Unused inputs parameter (kept for interface compatibility)
       * @returns A record containing the memory variables
       */
    async loadMemoryVariables(_) {
        const messages = await this.chatHistory.getMessages();
        const buffer = messages.slice(-this.k); // Get last k messages
        if (this.returnMessages) {
            return { [this.memoryKey]: buffer }; // Return array of BaseMessage
        }
        else {
            // Format as string if returnMessages is false (basic concatenation)
            return { [this.memoryKey]: buffer.map(m => `${m.type}: ${m.content}`).join('\n') };
        }
    }
    /**
       * Saves the input and output messages to the chat history.
       */
    async saveContext(inputs, outputs) {
        // Determine input and output messages based on keys or guessing
        const inputKey = this.inputKey ?? Object.keys(inputs)[0];
        const outputKey = this.outputKey ?? Object.keys(outputs)[0];
        const humanMessageContent = inputs[inputKey];
        const aiMessageContent = outputs[outputKey];
        // Add messages to the chat history
        if (humanMessageContent) {
            if (typeof humanMessageContent === 'string') {
                await this.chatHistory.addUserMessage(humanMessageContent);
            }
            else if (humanMessageContent instanceof BaseMessage) {
                await this.chatHistory.addMessage(humanMessageContent);
            }
            else {
                console.warn(`BufferMemory: Input for key '${inputKey}' is not a string or BaseMessage.`);
            }
        }
        if (aiMessageContent) {
            if (typeof aiMessageContent === 'string') {
                await this.chatHistory.addAIChatMessage(aiMessageContent);
            }
            else if (aiMessageContent instanceof BaseMessage) {
                await this.chatHistory.addMessage(aiMessageContent);
            }
            else {
                console.warn(`BufferMemory: Output for key '${outputKey}' is not a string or BaseMessage.`);
            }
        }
        // Note: K limit is applied during load, not save.
    }
    /**
       * Clears the chat history.
       */
    async clear() {
        await this.chatHistory.clear();
    }
}
exports.BufferMemory = BufferMemory;
/**
 * ConversationSummaryMemory Polyfill: Placeholder for summarizing conversation.
 * Requires an LLM, which is not fully polyfilled here.
 * Conforms to LangChain `ConversationSummaryMemory`.
 */
class ConversationSummaryMemory extends BaseChatMemory {
    chatHistory;
    llm; // Requires a BaseLanguageModel instance for summarization
    summary = ''; // Stores the current summary
    constructor(_config) {
        super(_config);
        if (!_config.llm)
            throw new Error('ConversationSummaryMemory requires an \'llm\' instance.');
        this.llm = _config.llm;
        // Ensure chatHistory is provided or create a new one
        this.chatHistory = _config.chatHistory ?? new ChatMessageHistory();
        // console.debug("ConversationSummaryMemory instance created (polyfill).");
    }
    /**
       * Loads the current summary.
       * @param _ Unused inputs parameter (kept for interface compatibility)
       * @returns A record containing the memory variables
       */
    async loadMemoryVariables(_) {
        // Return the current summary as a string
        return { [this.memoryKey]: this.summary || '' };
    }
    /**
       * Saves context and updates the summary.
       * Simulates summarizing by simply appending new content for the polyfill.
       */
    async saveContext(inputs, outputs) {
        // Save messages to history first
        const inputKey = this.inputKey ?? Object.keys(inputs)[0];
        const outputKey = this.outputKey ?? Object.keys(outputs)[0];
        let newContent = '';
        if (inputKey && inputs[inputKey]) {
            newContent += `Human: ${typeof inputs[inputKey] === 'string' ? inputs[inputKey] : JSON.stringify(inputs[inputKey])}\n`;
            if (typeof inputs[inputKey] === 'string')
                await this.chatHistory.addUserMessage(inputs[inputKey]);
            else if (inputs[inputKey] instanceof BaseMessage)
                await this.chatHistory.addMessage(inputs[inputKey]);
        }
        if (outputKey && outputs[outputKey]) {
            newContent += `AI: ${typeof outputs[outputKey] === 'string' ? outputs[outputKey] : JSON.stringify(outputs[outputKey])}\n`;
            if (typeof outputs[outputKey] === 'string')
                await this.chatHistory.addAIChatMessage(outputs[outputKey]);
            else if (outputs[outputKey] instanceof BaseMessage)
                await this.chatHistory.addMessage(outputs[outputKey]);
        }
        // Placeholder: Simulate summarization by just appending new content.
        // A real implementation would use `this.llm.invoke` with a prompt
        // combining the old summary and new messages to generate an updated summary.
        if (newContent.length > 0) {
            this.summary = (this.summary ? this.summary + '\n' : '') + `... (Summarized context incorporating: ${newContent.trim().substring(0, 50)}...)`;
            // console.debug("ConversationSummaryMemory: Updated summary (polyfill).");
        }
    }
    /**
       * Clears the chat history and the summary.
       */
    async clear() {
        await this.chatHistory.clear();
        this.summary = '';
    }
}
exports.ConversationSummaryMemory = ConversationSummaryMemory;
/**
 * VectorStoreRetrieverMemory Polyfill: Placeholder for retrieving relevant docs from a vector store.
 * Requires a VectorStoreRetriever instance (or an object with getRelevantDocuments).
 * Conforms to LangChain `VectorStoreRetrieverMemory`.
 */
class VectorStoreRetrieverMemory extends BaseChatMemory {
    chatHistory;
    // Placeholder for a retriever instance (e.g., from MemoryVectorStore.asRetriever)
    vectorStoreRetriever;
    memoryKey; // Often used for retrieved context (e.g., "context")
    constructor(config) {
        super(config); // Pass config to BaseChatMemory (handles memoryKey, inputKey)
        if (!config.vectorStoreRetriever || typeof config.vectorStoreRetriever.getRelevantDocuments !== 'function') {
            throw new Error('VectorStoreRetrieverMemory requires a valid \'vectorStoreRetriever\' with a \'getRelevantDocuments\' method.');
        }
        this.vectorStoreRetriever = config.vectorStoreRetriever;
        this.memoryKey = config.memoryKey ?? 'context'; // Default memory key for context
        // This memory type typically doesn't manage a chat history directly for *itself*,
        // it uses the retriever. However, the BaseChatMemory interface requires it.
        // We can assign a dummy history or null, or redefine the interface if possible.
        // For this polyfill, let's assign a dummy history to satisfy the BaseChatMemory interface,
        // although it won't be used for loading/saving context in the standard way.
        this.chatHistory = config.chatHistory ?? new ChatMessageHistory();
        if (config.chatHistory) {
            console.warn('VectorStoreRetrieverMemory polyfill received chatHistory, but typically doesn\'t use it directly for retrieval.');
        }
        // console.debug("VectorStoreRetrieverMemory instance created (polyfill).");
    }
    /**
       * Loads memory variables by retrieving relevant documents based on the input query.
       */
    async loadMemoryVariables(inputs) {
        // Determine the query string from inputs
        const inputKey = this.inputKey ?? Object.keys(inputs)[0]; // Use configured input key or guess
        const query = inputs[inputKey];
        if (typeof query !== 'string' || query.length === 0) {
            console.warn(`VectorStoreRetrieverMemory: Cannot determine input string for retrieval using key '${inputKey}'. Returning empty context.`);
            return { [this.memoryKey]: '' };
        }
        // Use the retriever to find relevant documents
        const relevantDocs = await this.vectorStoreRetriever.getRelevantDocuments(query);
        // Format documents into a single string context
        const context = relevantDocs.map(doc => doc.pageContent).join('\n\n');
        // Return the formatted context under the memory key
        return { [this.memoryKey]: context };
    }
    /**
       * Saves the current context.
       * Note: This memory type typically doesn't save conversational context back to the vector store itself.
       * This method might be used in combined memory systems, but for this polyfill, it does nothing.
       * @param _ Unused inputs parameter (kept for interface compatibility)
       * @param __ Unused outputs parameter (kept for interface compatibility)
       */
    async saveContext(_, __) {
        // VectorStoreRetrieverMemory typically does not implement saveContext to the vector store.
        // Its purpose is retrieval based on current input.
        // If used in a larger system, a different memory component (like BufferMemory or ChatMessageHistory)
        // would handle saving the actual conversation turns.
        logger_1.logger.debug('VectorStoreRetrieverMemory.saveContext called (no action taken on vector store)');
    }
    /**
       * Clears the memory.
       * Note: Clearing this memory component typically does *not* clear the underlying vector store.
       * It would only clear internal state if any existed, or potentially the chat history
       * if a separate one was managed (which is not the case in this basic polyfill).
       */
    async clear() {
        // No internal state to clear in this basic polyfill
        // console.debug("VectorStoreRetrieverMemory.clear called (polyfill - no action taken).");
    }
}
exports.VectorStoreRetrieverMemory = VectorStoreRetrieverMemory;
/**
 * MemoryVectorStore Polyfill: Simple in-memory store for Documents with embeddings.
 * Stores Documents (including pageContent and metadata) and their embeddings.
 * Conforms to LangChain `MemoryVectorStore`.
 */
class MemoryVectorStore extends BaseMemoryVectorStore {
    embeddings = null; // Optional embeddings for string queries
    constructor(embeddings) {
        super();
        this.embeddings = embeddings ?? null;
        // console.debug("MemoryVectorStore instance created (polyfill).");
    }
    /**
       * Static factory method (placeholder for compatibility).
       * In a real scenario, might load from a path etc.
       */
    static async fromTexts(texts, metadatas, embeddings) {
        if (!embeddings)
            throw new Error('Embeddings instance is required for fromTexts.');
        if (texts.length !== metadatas.length)
            throw new Error('Texts and metadatas arrays must have the same length.');
        const docs = texts.map((text, i) => new Document({ pageContent: text, metadata: metadatas[i] }));
        const instance = new MemoryVectorStore(embeddings);
        await instance.addDocuments(docs);
        return instance;
    }
    /**
       * Static factory method (placeholder for compatibility).
       * In a real scenario, might load from a path etc.
       */
    static async fromDocuments(docs, embeddings) {
        if (!embeddings)
            throw new Error('Embeddings instance is required for fromDocuments.');
        const instance = new MemoryVectorStore(embeddings);
        await instance.addDocuments(docs);
        return instance;
    }
    /**
       * Adds documents. Assumes embeddings are pre-computed in metadata or uses instance embeddings.
       * Conforms to LangChain `addDocuments`.
       * @param documents Array of Documents.
       */
    async addDocuments(documents) {
        const items = [];
        const textsToEmbed = [];
        const originalDocIndices = []; // Map indices back to original documents array
        // Prepare items and identify which documents need embedding
        for (let i = 0; i < documents.length; i++) {
            const doc = documents[i];
            const id = doc.id ?? doc.metadata?.id ?? doc.metadata?.doc_id ?? (0, uuid_1.v4)();
            let vector = doc.metadata?.embedding; // Check if embedding is already in metadata
            if (!Array.isArray(vector) || vector.length === 0) {
                // Document needs embedding
                if (!this.embeddings) {
                    throw new Error(`Document with id '${id}' is missing embedding, and no Embeddings instance provided to MemoryVectorStore.`);
                }
                if (typeof doc.pageContent !== 'string' || doc.pageContent.trim().length === 0) {
                    throw new Error(`Document with id '${id}' needs embedding but pageContent is empty or not a string.`);
                }
                textsToEmbed.push(doc.pageContent);
                originalDocIndices.push(i); // Record original index
                vector = []; // Placeholder, will be filled after embedding
            }
            // Create the VectorStoreItem structure
            items.push({
                id: id,
                vector: vector, // May be placeholder or existing embedding
                metadata: { ...doc.metadata }, // Store a copy of metadata
                pageContent: doc.pageContent, // Store original page content
            });
        }
        // Embed documents that need it
        if (textsToEmbed.length > 0 && this.embeddings) {
            // Use the instance's embeddings to embed the batch of texts
            const vectors = await this.embeddings.embedDocuments(textsToEmbed);
            // Assign the generated vectors back to the correct items
            originalDocIndices.forEach((originalIndex, batchIndex) => {
                if (!items[originalIndex]) {
                    console.error(`MemoryVectorStore polyfill: Internal error - original index ${originalIndex} not found in items.`);
                    return; // Skip if something went wrong
                }
                items[originalIndex].vector = vectors[batchIndex];
                // Optionally add embedding to metadata here if desired, but storing in 'vector' field is primary
                // items[originalIndex].metadata.embedding = vectors[batchIndex];
            });
        }
        // Add all prepared items (with embeddings) to the store
        await super.addVectors(items);
    }
    /**
       * Perform similarity search using a query string or vector.
       * Conforms to LangChain `similaritySearch`.
       * @param query Query string or embedding vector (number[]).
       * @param k Number of top results to return.
       * @param filter Optional metadata filter.
       * @returns A Promise resolving to an array of matching Documents.
       */
    async similaritySearch(query, k, filter) {
        let queryEmbedding;
        if (typeof query === 'string') {
            if (!this.embeddings) {
                throw new Error('MemoryVectorStore requires an Embeddings instance for string queries.');
            }
            queryEmbedding = await this.embeddings.embedQuery(query);
        }
        else if (Array.isArray(query)) {
            queryEmbedding = query;
        }
        else {
            throw new Error('Query must be a string or a number array.');
        }
        // Reuse the BaseMemoryVectorStore similarity search which returns SimilaritySearchResultItem[]
        const results = await super.similaritySearch(queryEmbedding, k, filter);
        // Return the SimilaritySearchResultItem array directly
        return results;
    }
    /**
       * Perform similarity search and return scores.
       * Conforms to LangChain `similaritySearchWithScore`.
       * @param query Query string or embedding vector (number[]).
       * @param k Number of top results to return.
       * @param filter Optional metadata filter.
       * @returns A Promise resolving to an array of [Document, score] tuples.
       */
    async similaritySearchWithScore(query, k, filter) {
        let queryEmbedding;
        if (typeof query === 'string') {
            if (!this.embeddings) {
                throw new Error('MemoryVectorStore requires an Embeddings instance for string queries.');
            }
            queryEmbedding = await this.embeddings.embedQuery(query);
        }
        else if (Array.isArray(query)) {
            queryEmbedding = query;
        }
        else {
            throw new Error('Query must be a string or a number array.');
        }
        // Reuse the BaseMemoryVectorStore similarity search which returns SimilaritySearchResultItem[]
        const results = await super.similaritySearch(queryEmbedding, k, filter);
        // Map the SimilaritySearchResultItem objects to [Document, score] tuples
        const finalResults = results.map(item => {
            // Reconstruct Document from the stored item's pageContent and metadata
            const doc = new Document({
                pageContent: item.pageContent,
                metadata: item.metadata,
                id: item.id
            });
            return [doc, item.score];
        });
        return finalResults;
    }
    /**
       * Create a retriever instance for use with LangChain components.
       * Conforms to LangChain `asRetriever`.
       * @param k The number of documents the retriever should return (defaults to 4).
       * @returns An object conforming to the LangChain Retriever interface (specifically `getRelevantDocuments`).
       */
    asRetriever(k) {
        const retrieverK = k ?? 4; // Default to 4 documents for retriever
        // Return an object that implements the getRelevantDocuments method
        return {
            getRelevantDocuments: async (query) => {
                // Use this vector store's similaritySearch method
                // Filters can be added here if the retriever supports them
                return this.similaritySearch(query, retrieverK);
            }
        };
    }
}
exports.MemoryVectorStore = MemoryVectorStore;
/**
 * Defines the standard event types for the callback system
 */
var CallbackEventType;
(function (CallbackEventType) {
    CallbackEventType["LLM_START"] = "llm_start";
    CallbackEventType["LLM_NEW_TOKEN"] = "llm_new_token";
    CallbackEventType["LLM_END"] = "llm_end";
    CallbackEventType["LLM_ERROR"] = "llm_error";
    CallbackEventType["CHAIN_START"] = "chain_start";
    CallbackEventType["CHAIN_END"] = "chain_end";
    CallbackEventType["CHAIN_ERROR"] = "chain_error";
    CallbackEventType["TOOL_START"] = "tool_start";
    CallbackEventType["TOOL_END"] = "tool_end";
    CallbackEventType["TOOL_ERROR"] = "tool_error";
    CallbackEventType["AGENT_ACTION"] = "agent_action";
    CallbackEventType["AGENT_FINISH"] = "agent_finish";
    CallbackEventType["RETRIEVER_START"] = "retriever_start";
    CallbackEventType["RETRIEVER_END"] = "retriever_end";
    CallbackEventType["RETRIEVER_ERROR"] = "retriever_error";
    CallbackEventType["MEMORY_START"] = "memory_start";
    CallbackEventType["MEMORY_END"] = "memory_end";
    CallbackEventType["MEMORY_ERROR"] = "memory_error";
    CallbackEventType["CUSTOM"] = "custom";
})(CallbackEventType || (exports.CallbackEventType = CallbackEventType = {}));
// Ensure all enum values are recognized as used by the linter
const _validateCallbackEventTypes = () => {
    return [
        CallbackEventType.LLM_START,
        CallbackEventType.LLM_NEW_TOKEN,
        CallbackEventType.LLM_END,
        CallbackEventType.LLM_ERROR,
        CallbackEventType.CHAIN_START,
        CallbackEventType.CHAIN_END,
        CallbackEventType.CHAIN_ERROR,
        CallbackEventType.TOOL_START,
        CallbackEventType.TOOL_END,
        CallbackEventType.TOOL_ERROR,
        CallbackEventType.AGENT_ACTION,
        CallbackEventType.AGENT_FINISH,
        CallbackEventType.RETRIEVER_START,
        CallbackEventType.RETRIEVER_END,
        CallbackEventType.RETRIEVER_ERROR,
        CallbackEventType.MEMORY_START,
        CallbackEventType.MEMORY_END,
        CallbackEventType.MEMORY_ERROR,
        CallbackEventType.CUSTOM
    ];
};
/**
 * Comprehensive polyfill for LangChain's CallbackManager concept.
 * Allows registering and running callbacks for various events in the LLM pipeline.
 * Conforms to LangChain `CallbackManagerForLLMRun` and extends functionality.
 */
class CallbackManagerForLLMRun {
    handlers = new Map(); // Store handlers by event name
    runId;
    parentRunId;
    metadata;
    isVerbose = false;
    /**
       * Creates a new CallbackManagerForLLMRun instance
       * @param runId Unique identifier for this run
       * @param parentRunId Optional parent run identifier for nested runs
       * @param metadata Additional metadata for this run
       * @param verbose Whether to log detailed information about callback execution
       */
    constructor(runId, parentRunId, metadata = {}, verbose = false) {
        if (!runId)
            throw new Error('CallbackManager requires a valid runId');
        this.runId = runId;
        this.parentRunId = parentRunId;
        this.metadata = metadata;
        this.isVerbose = verbose;
        if (this.isVerbose) {
            logger_1.logger.debug(`CallbackManager initialized with runId: ${runId}${parentRunId ? `, parentRunId: ${parentRunId}` : ''}`);
        }
    }
    /**
       * Adds a callback handler for a specific event.
       * @param eventName The name of the event (e.g., 'llm_start', 'llm_end').
       * @param handler The function to call when the event occurs.
       * @returns This instance for method chaining
       */
    addHandler(eventName, handler) {
        if (typeof eventName !== 'string' || eventName.length === 0) {
            logger_1.logger.warn('CallbackManager: Attempted to add handler for invalid event name.');
            return this;
        }
        if (typeof handler !== 'function') {
            logger_1.logger.warn(`CallbackManager: Attempted to add non-function handler for event '${eventName}'.`);
            return this;
        }
        if (!this.handlers.has(eventName)) {
            this.handlers.set(eventName, []);
        }
        this.handlers.get(eventName)?.push(handler);
        if (this.isVerbose) {
            logger_1.logger.debug(`CallbackManager: Added handler for event '${eventName}'.`);
        }
        return this;
    }
    /**
       * Removes a specific handler for an event
       * @param eventName The name of the event
       * @param handler The handler function to remove
       * @returns This instance for method chaining
       */
    removeHandler(eventName, handler) {
        if (!this.handlers.has(eventName))
            return this;
        const handlers = this.handlers.get(eventName);
        if (!handlers)
            return this;
        const index = handlers.indexOf(handler);
        if (index !== -1) {
            handlers.splice(index, 1);
            if (this.isVerbose) {
                logger_1.logger.debug(`CallbackManager: Removed handler for event '${eventName}'.`);
            }
        }
        return this;
    }
    /**
       * Removes all handlers for a specific event
       * @param eventName The name of the event to clear handlers for
       * @returns This instance for method chaining
       */
    clearHandlers(eventName) {
        if (this.handlers.has(eventName)) {
            this.handlers.delete(eventName);
            if (this.isVerbose) {
                logger_1.logger.debug(`CallbackManager: Cleared all handlers for event '${eventName}'.`);
            }
        }
        return this;
    }
    /**
       * Removes all handlers for all events
       * @returns This instance for method chaining
       */
    clearAllHandlers() {
        this.handlers.clear();
        if (this.isVerbose) {
            logger_1.logger.debug('CallbackManager: Cleared all handlers for all events.');
        }
        return this;
    }
    /**
       * Runs all registered handlers for a given event.
       * @param eventName The name of the event to trigger.
       * @param args Arguments to pass to the handlers.
       * @returns A promise that resolves when all handlers have completed
       */
    async runHandlers(eventName, ...args) {
        const eventHandlers = this.handlers.get(eventName);
        if (eventHandlers && eventHandlers.length > 0) {
            if (this.isVerbose) {
                logger_1.logger.debug(`CallbackManager: Running ${eventHandlers.length} handlers for event '${eventName}'`);
            }
            // Add run metadata to the arguments
            const argsWithMetadata = [...args, {
                    runId: this.runId,
                    parentRunId: this.parentRunId,
                    metadata: this.metadata,
                    timestamp: Date.now()
                }];
            // Use Promise.allSettled to ensure all handlers are attempted even if some fail
            const results = await Promise.allSettled(eventHandlers.map(async (handler) => {
                try {
                    return await Promise.resolve(handler(...argsWithMetadata));
                }
                catch (error) {
                    logger_1.logger.error(`Callback handler for event '${eventName}' threw an error:`, error);
                    throw error; // Re-throw to be caught by allSettled
                }
            }));
            // Log any rejected promises
            const failedCount = results.filter(r => r.status === 'rejected').length;
            if (failedCount > 0 && this.isVerbose) {
                logger_1.logger.warn(`CallbackManager: ${failedCount} of ${results.length} handlers failed for event '${eventName}'`);
            }
        }
        else if (this.isVerbose) {
            logger_1.logger.debug(`CallbackManager: No handlers registered for event '${eventName}'.`);
        }
    }
    /**
       * Creates a child callback manager for nested runs
       * @param childRunId The run ID for the child
       * @param metadata Additional metadata for the child run
       * @returns A new CallbackManagerForLLMRun instance
       */
    createChild(childRunId, metadata = {}) {
        return new CallbackManagerForLLMRun(childRunId, this.runId, // Current runId becomes the parent
        { ...this.metadata, ...metadata }, // Merge metadata
        this.isVerbose);
    }
    /**
       * Sets the verbosity level of the callback manager
       * @param verbose Whether to enable verbose logging
       * @returns This instance for method chaining
       */
    setVerbose(verbose) {
        this.isVerbose = verbose;
        return this;
    }
    // --- Comprehensive event handling methods ---
    /**
       * Triggers the 'llm_start' event when an LLM begins processing.
       * @param llm Information about the LLM being used
       * @param prompts The input prompts being sent to the LLM
       * @param options Additional options for the LLM call
       */
    async handleLLMStart(llm, prompts, options = {}) {
        await this.runHandlers(CallbackEventType.LLM_START, llm, prompts, options);
    }
    /**
       * Triggers the 'llm_new_token' event when a new token is generated in streaming mode.
       * @param token The new token generated
       * @param options Additional context about the token generation
       */
    async handleLLMNewToken(token, options = {}) {
        await this.runHandlers(CallbackEventType.LLM_NEW_TOKEN, token, options);
    }
    /**
       * Triggers the 'llm_end' event when an LLM completes processing.
       * @param output The output from the LLM
       * @param options Additional context about the completion
       */
    async handleLLMEnd(output, options = {}) {
        await this.runHandlers(CallbackEventType.LLM_END, output, options);
    }
    /**
       * Triggers the 'llm_error' event when an LLM encounters an error.
       * @param error The error that occurred
       * @param options Additional context about the error
       */
    async handleLLMError(error, options = {}) {
        await this.runHandlers(CallbackEventType.LLM_ERROR, error, options);
    }
    /**
       * Triggers the 'chain_start' event when a chain begins processing.
       * @param chain Information about the chain
       * @param inputs The inputs to the chain
       * @param options Additional context about the chain execution
       */
    async handleChainStart(chain, inputs, options = {}) {
        await this.runHandlers(CallbackEventType.CHAIN_START, chain, inputs, options);
    }
    /**
       * Triggers the 'chain_end' event when a chain completes processing.
       * @param outputs The outputs from the chain
       * @param options Additional context about the chain completion
       */
    async handleChainEnd(outputs, options = {}) {
        await this.runHandlers(CallbackEventType.CHAIN_END, outputs, options);
    }
    /**
       * Triggers the 'chain_error' event when a chain encounters an error.
       * @param error The error that occurred
       * @param options Additional context about the error
       */
    async handleChainError(error, options = {}) {
        await this.runHandlers(CallbackEventType.CHAIN_ERROR, error, options);
    }
    /**
       * Triggers the 'tool_start' event when a tool begins execution.
       * @param tool Information about the tool
       * @param input The input to the tool
       * @param options Additional context about the tool execution
       */
    async handleToolStart(tool, input, options = {}) {
        await this.runHandlers(CallbackEventType.TOOL_START, tool, input, options);
    }
    /**
       * Triggers the 'tool_end' event when a tool completes execution.
       * @param output The output from the tool
       * @param options Additional context about the tool completion
       */
    async handleToolEnd(output, options = {}) {
        await this.runHandlers(CallbackEventType.TOOL_END, output, options);
    }
    /**
       * Triggers the 'tool_error' event when a tool encounters an error.
       * @param error The error that occurred
       * @param options Additional context about the error
       */
    async handleToolError(error, options = {}) {
        await this.runHandlers(CallbackEventType.TOOL_ERROR, error, options);
    }
    /**
       * Triggers the 'agent_action' event when an agent decides to take an action.
       * @param action Information about the action being taken
       * @param options Additional context about the action
       */
    async handleAgentAction(action, options = {}) {
        await this.runHandlers(CallbackEventType.AGENT_ACTION, action, options);
    }
    /**
       * Triggers the 'agent_finish' event when an agent completes its task.
       * @param finish Information about the agent's final state
       * @param options Additional context about the completion
       */
    async handleAgentFinish(finish, options = {}) {
        await this.runHandlers(CallbackEventType.AGENT_FINISH, finish, options);
    }
    /**
       * Triggers the 'retriever_start' event when a retriever begins processing.
       * @param retriever Information about the retriever
       * @param query The query being processed
       * @param options Additional context about the retrieval
       */
    async handleRetrieverStart(retriever, query, options = {}) {
        await this.runHandlers(CallbackEventType.RETRIEVER_START, retriever, query, options);
    }
    /**
       * Triggers the 'retriever_end' event when a retriever completes processing.
       * @param documents The documents retrieved
       * @param options Additional context about the retrieval
       */
    async handleRetrieverEnd(documents, options = {}) {
        await this.runHandlers(CallbackEventType.RETRIEVER_END, documents, options);
    }
    /**
       * Triggers the 'retriever_error' event when a retriever encounters an error.
       * @param error The error that occurred
       * @param options Additional context about the error
       */
    async handleRetrieverError(error, options = {}) {
        await this.runHandlers(CallbackEventType.RETRIEVER_ERROR, error, options);
    }
    /**
       * Triggers the 'memory_start' event when a memory operation begins.
       * @param memory Information about the memory component
       * @param operation The operation being performed (e.g., 'load', 'save')
       * @param options Additional context about the memory operation
       */
    async handleMemoryStart(memory, operation, options = {}) {
        await this.runHandlers(CallbackEventType.MEMORY_START, memory, operation, options);
    }
    /**
       * Triggers the 'memory_end' event when a memory operation completes.
       * @param result The result of the memory operation
       * @param options Additional context about the memory operation
       */
    async handleMemoryEnd(result, options = {}) {
        await this.runHandlers(CallbackEventType.MEMORY_END, result, options);
    }
    /**
       * Triggers the 'memory_error' event when a memory operation encounters an error.
       * @param error The error that occurred
       * @param options Additional context about the error
       */
    async handleMemoryError(error, options = {}) {
        await this.runHandlers(CallbackEventType.MEMORY_ERROR, error, options);
    }
    /**
       * Triggers a custom event with the provided name and data.
       * @param eventName The name of the custom event
       * @param data The data to pass to the event handlers
       */
    async handleCustomEvent(eventName, data) {
        await this.runHandlers(eventName, data, { customEvent: true });
    }
}
exports.CallbackManagerForLLMRun = CallbackManagerForLLMRun;
//# sourceMappingURL=corePolyfill.js.map