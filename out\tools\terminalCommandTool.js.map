{"version": 3, "file": "terminalCommandTool.js", "sourceRoot": "", "sources": ["../../src/tools/terminalCommandTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA,0CAsBC;AAjED,2DAAqD;AACrD;;;GAGG;AAEH,+CAAiC;AAGjC,kDAAoC,CAAC,+BAA+B;AAEpE,+BAAiC,CAAC,mCAAmC;AACrE,6BAAwB,CAAC,4BAA4B;AACrD,kDAAkD;AAClD,sCAAmC,CAAC,wCAAwC;AAC5E,sCAAsC,CAAC,8CAA8C;AACrF,uCAAyB,CAAC,oBAAoB;AAC9C,+CAAiC,CAAC,0EAA0E;AAE5G,mEAAmE;AACnE,MAAM,KAAK,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAmBjC;;;GAGG;AACH,SAAgB,eAAe;IAC7B,MAAM,WAAW,GAAG,IAAA,kBAAS,EAAqB,oCAAoC,EAAE,SAAS,CAAC,CAAC;IACnG,IAAI,WAAW,EAAE,CAAC;QAChB,iGAAiG;QACjG,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC/B,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;QACzB,gEAAgE;QAChE,0EAA0E;QAC1E,6FAA6F;QAC7F,kCAAkC;QAClC,qEAAqE;QACrE,mEAAmE;QACnE,wDAAwD;QACxD,kEAAkE;QAClE,OAAO,SAAS,CAAC;IACnB,CAAC;SAAM,CAAC;QACN,wCAAwC;QACxC,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC;IAC1C,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,gBAAgB,CAAC,OAAe,EAAE,OAAY;IACrD,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAE3C,gEAAgE;IAChE,IAAI,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAClE,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,KAAK,MAAM,cAAc,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YACrD,IAAI,CAAC;gBACH,2EAA2E;gBAC3E,IAAI,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACnE,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtD,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;wBAAC,aAAa,GAAG,IAAI,CAAC;wBAAC,MAAM;oBAAC,CAAC;gBAC3D,CAAC;qBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,sBAAsB;oBACrE,aAAa,GAAG,IAAI,CAAC;oBACrB,MAAM;gBACR,CAAC;qBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,sCAAsC;oBACnF,aAAa,GAAG,IAAI,CAAC;oBACrB,MAAM;gBACR,CAAC;YACH,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0CAA0C,EAAE,CAAC,CAAC,CAAC;gBACrE,gEAAgE;gBAChE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wDAAwD,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;YAC/F,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,iEAAiE;IACjE,IAAI,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtE,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACzD,IAAI,CAAC;gBACH,2EAA2E;gBAC3E,IAAI,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvE,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;wBACxB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+CAA+C,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;wBACtF,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;qBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,sBAAsB;oBACvE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;oBAC/E,OAAO,KAAK,CAAC;gBACf,CAAC;qBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,wBAAwB;oBACvE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;oBAClF,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4CAA4C,EAAE,CAAC,CAAC,CAAC;gBACvE,6DAA6D;gBAC7D,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC,CAAC,gBAAgB;AAC/B,CAAC;AAGD;;;;GAIG;AACH,MAAa,mBAAmB;IACrB,EAAE,GAAG,kBAAkB,CAAC;IACxB,IAAI,GAAG,kBAAkB,CAAC,CAAC,8BAA8B;IACzD,WAAW,GAAG,2KAA2K,CAAC;IAC1L,QAAQ,GAAG,QAAQ,CAAC;IACpB,IAAI,GAAG,eAAe,CAAC,CAAC,+BAA+B;IACvD,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QACzB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,8FAA8F,CAAC;QACnI,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;QAC1F,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yHAAyH,CAAC;QAC9J,GAAG,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oKAAoK,CAAC;QACrP,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;QACpH,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sEAAsE,CAAC;QAC7G,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;QAC/F,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oEAAoE,CAAC;KACxI,CAAC,CAAC;IACM,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IAEhE,wEAAwE;IAC/D,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,uDAAuD;QACvD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,8FAA8F,CAAC;QACnI,0DAA0D;QAC1D,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;QAC1F,kGAAkG;QAClG,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yHAAyH,CAAC;QAC9J,kKAAkK;QAClK,GAAG,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oKAAoK,CAAC;QACrP,uFAAuF;QACvF,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;QACpH,oJAAoJ;QACpJ,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sEAAsE,CAAC;QAC7G,4DAA4D;QAC5D,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;QAC/F,sGAAsG;QACtG,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oEAAoE,CAAC;KACxI,CAAC,CAAC,QAAQ,CAAC,oDAAoD,CAAC,CAAC;IAE3D,MAAM,CAAqB;IAElC;QACE,0CAA0C;QAC1C,MAAM,eAAe,GAAG,IAAA,kBAAS,EAAuB,uCAAuC,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;QAClH,MAAM,iBAAiB,GAAG,IAAA,kBAAS,EAAuB,yCAAyC,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtH,IAAI,CAAC,MAAM,GAAG;YACZ,oBAAoB,EAAE,IAAA,kBAAS,EAAS,4CAA4C,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,eAAe;YACxH,gBAAgB,EAAE,IAAA,kBAAS,EAAS,wCAAwC,EAAE,KAAK,CAAC,EAAE,qBAAqB;YAC3G,YAAY,EAAE,IAAA,kBAAS,EAAqB,oCAAoC,EAAE,SAAS,CAAC,IAAI,eAAe,EAAE;YACjH,eAAe,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS;YACzE,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;YAC/E,wBAAwB,EAAE,IAAA,kBAAS,EAAU,gDAAgD,EAAE,KAAK,CAAC;SACtG,CAAC;QAEF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8CAA8C,EAAE;YACnE,GAAG,IAAI,CAAC,MAAM;YACd,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK;YACvG,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,MAAM;YAC9G,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,wBAAwB;SAC9C,CAAC,CAAC;IACL,CAAC;IAED;;;;SAIK;IACL,qBAAqB;QACnB,8DAA8D;QAC9D,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7D,MAAM,YAAY,GAAG,IAAA,oCAAe,EAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAE9D,uDAAuD;YACvD,IACE,YAAY;gBACZ,OAAO,YAAY,KAAK,QAAQ;gBAChC,YAAY,IAAI,YAAY;gBAC5B,YAAY,CAAC,UAAU;gBACvB,KAAK,IAAI,YAAY,CAAC,UAAU;gBAChC,YAAY,CAAC,UAAU,CAAC,GAAG,EAC3B,CAAC;gBACD,YAAY,CAAC,UAAU,CAAC,GAAG,GAAG;oBAC5B,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW;oBACpD,oBAAoB,EAAE,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;iBACnD,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,YAAY;aACzB,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,kFAAkF;YAClF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6GAA6G,CAAC,CAAC;YACrI,yDAAyD;YACzD,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE;oBACV,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,kCAAkC,EAAE;qBAC7E;oBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;iBACtB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAGD;;;;;;SAMK;IACL,KAAK,CAAC,OAAO,CAAC,KAAgB,EAAE,OAAsB;QACpD,kEAAkE;QAClE,0DAA0D;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClD,MAAM,QAAQ,GAAG,2BAA2B,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM;gBAC/D,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;qBAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC;qBACpC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAM,KAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;qBACrE,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1E,CAAC;QACD,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC;QAExC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QACvC,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,IAAI,EAAE,CAAC;QACvC,6EAA6E;QAC7E,IAAI,GAAG,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QACtL,sEAAsE;QACtE,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5B,+GAA+G;QAC/G,MAAM,UAAU,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QACtC,MAAM,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACnF,IAAI,cAAc,CAAC,GAAI,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,cAAc,CAAC,GAAI,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;gBAChF,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,0BAA0B;YAC7C,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAI,CAAC,GAAG,CAAW,CAAC,CAAC,mBAAmB;YACpE,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAC5B,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACzE,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC/D,MAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC;QACxC,MAAM,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAE7F,wDAAwD;QACxD,MAAM,iBAAiB,GAAG,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,QAAQ,GAAG,wBAAwB,GAAG,iBAAiB,GAAG,yCAAyC,CAAC;YAC1G,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAChC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC9D,CAAC;QAGD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,GAAG,iBAAiB,GAAG,GAAG,CAAC,CAAC;QACvE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,GAAG,YAAY,GAAG,KAAK,GAAG,aAAa,GAAG,SAAS,GAAG,kBAAkB,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QAE5P,8FAA8F;QAC9F,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;QAEzF,IAAI,OAAO,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAC3D,IAAI,CAAC;gBACH,uFAAuF;gBACvF,wCAAwC;gBACxC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,iBAAiB,EAAE;oBACxD,GAAG;oBACH,GAAG;oBACH,KAAK,EAAE,oBAAoB;oBAC3B,OAAO,EAAE,SAAS;oBAClB,SAAS,EAAE,gBAAgB,EAAE,sBAAsB;iBACpD,CAAC,CAAC;gBAEH,8FAA8F;gBAC9F,+FAA+F;gBAC/F,+EAA+E;gBAC/E,+CAA+C;gBAC/C,IAAI,MAAM,EAAE,CAAC;oBACX,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,iBAAiB,GAAG,2BAA2B,GAAG,MAAM,CAAC,CAAC;gBAC/F,CAAC;gBAGD,OAAO;oBACL,OAAO,EAAE,IAAI,EAAE,mEAAmE;oBAClF,MAAM,EAAE;wBACN,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,MAAM;wBACd,qEAAqE;wBACrE,QAAQ,EAAE,IAAI;wBACd,MAAM,EAAE,IAAI;wBACZ,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,oCAAoC;qBAClF;oBACD,KAAK,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B,CAAC,EAAE,wDAAwD;oBAC9H,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,QAAQ,EAAE;wBACR,OAAO,EAAE,iBAAiB;wBAC1B,GAAG,EAAE,KAAK,EAAE,SAAS;wBACrB,eAAe,EAAE,MAAM;qBACxB;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,+DAA+D;gBAC/D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,GAAG,iBAAiB,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;gBACxF,IAAI,YAAY,GAAG,KAAK,CAAC,OAAO,IAAI,2BAA2B,CAAC;gBAChE,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC;gBAChC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC;gBAEhC,gEAAgE;gBAChE,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;oBACjC,YAAY,GAAG,qBAAqB,GAAG,SAAS,GAAG,qCAAqC,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;gBAChH,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACpC,YAAY,GAAG,CAAC,oCAAoC,GAAG,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC3G,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;oBACzE,YAAY,GAAG,iCAAiC,GAAG,gBAAgB,GAAG,UAAU,CAAC;oBACjF,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,2BAA2B;oBAC3E,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;gBACjD,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,YAAY;oBACnB,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,QAAQ,EAAE;wBACR,OAAO,EAAE,iBAAiB;wBAC1B,GAAG,EAAE,KAAK,EAAE,SAAS;wBACrB,eAAe,EAAE,MAAM;wBACvB,aAAa,EAAE,KAAK,CAAC,IAAI;wBACzB,UAAU,EAAE,KAAK,CAAC,MAAM;wBACxB,UAAU,EAAE,KAAK,CAAC,MAAM;qBACzB;oBACD,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,KAAK,gBAAgB,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,KAAK,gBAAgB,EAAE,EAAE;iBAC9K,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YACtE,+DAA+D;YAC/D,MAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE;gBACtC,GAAG;gBACH,GAAG;gBACH,KAAK,EAAE,KAAK,EAAE,sCAAsC;gBACpD,OAAO,EAAE,SAAS,EAAE,0CAA0C;gBAC9D,kEAAkE;aACnE,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,eAAe,GAAG,KAAK,CAAC;YAC9B,MAAM,eAAe,GAAG,KAAK,CAAC;YAE9B,MAAM,aAAa,GAAG,CAAC,MAAgB,EAAE,MAAc,EAAE,SAAkB,EAAE,EAAE;gBAC7E,OAAO,IAAI,OAAO,CAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACxD,IAAI,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACxC,IAAI,gBAAgB,GAAG,SAAS,CAAC;oBAEjC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;wBACjC,IAAI,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,gBAAgB,EAAE,CAAC;4BAC3D,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;wBACvD,CAAC;6BAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BAC7B,qDAAqD;4BACrD,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;4BACvG,gBAAgB,GAAG,IAAI,CAAC;4BACxB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,0BAA0B,GAAG,OAAO,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,GAAG,CAAC,CAAC;4BAC5J,yDAAyD;4BACzD,0CAA0C;wBAC5C,CAAC;wBACD,oCAAoC;oBACtC,CAAC,CAAC,CAAC;oBACH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;oBACnE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,uBAAuB;gBACrD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAGF,IAAI,CAAC;gBACH,4DAA4D;gBAC5D,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBAChJ,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBAEhJ,8DAA8D;gBAC9D,MAAM,CAAC,UAAU,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;oBAC3H,IAAI,OAAO,CAAgE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBAC7F,IAAI,KAAwB,CAAC;wBAC7B,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE;4BACjC,0EAA0E;4BAC1E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,GAAG,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;4BACnF,KAAK,GAAG,GAAG,CAAC,CAAC,sBAAsB;4BACnC,iDAAiD;4BACjD,MAAM,CAAC,GAAG,CAAC,CAAC;wBACd,CAAC,CAAC,CAAC;wBACH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAmB,EAAE,MAAqB,EAAE,EAAE;4BACjE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,GAAG,qBAAqB,GAAG,IAAI,GAAG,cAAc,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC;4BAC5G,4CAA4C;4BAC5C,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,gCAAgC;wBACpE,CAAC,CAAC,CAAC;wBACH,qCAAqC;wBACrC,IAAI,eAAe,GAAG,KAAK,CAAC;wBAC5B,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;4BACpC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gCACpB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,sBAAsB,GAAG,SAAS,GAAG,yBAAyB,CAAC,CAAC;gCAC7G,eAAe,GAAG,IAAI,CAAC;gCACvB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,oCAAoC;4BAC/D,CAAC;wBACH,CAAC,EAAE,SAAS,CAAC,CAAC;wBACd,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;4BACvB,YAAY,CAAC,aAAa,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC;oBACF,aAAa;oBACb,aAAa;iBACd,CAAC,CAAC;gBAEH,MAAM,MAAM,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAClD,MAAM,MAAM,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAElD,uDAAuD;gBACvD,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;oBACrB,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC,uBAAuB;gBACjD,CAAC;gBAED,uCAAuC;gBACvC,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,2CAA2C;gBAElF,uCAAuC;gBACvC,OAAO;oBACL,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE;wBACN,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,UAAU,CAAC,IAAI;wBACzB,MAAM,EAAE,UAAU,CAAC,MAAM;wBACzB,SAAS,EAAE;4BACT,MAAM,EAAE,oBAAoB;4BAC5B,MAAM,EAAE,oBAAoB;yBAC7B;qBACF;oBACD,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,2BAA2B,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,UAAU,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3N,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,QAAQ,EAAE;wBACR,OAAO,EAAE,iBAAiB;wBAC1B,GAAG,EAAE,KAAK,EAAE,SAAS;wBACrB,eAAe,EAAE,OAAO;qBACzB;iBACF,CAAC;YAEJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,mEAAmE;gBACnE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+CAA+C,GAAG,iBAAiB,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;gBACzG,yDAAyD;gBACzD,MAAM,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACpD,MAAM,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAEpD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,0CAA0C;oBAClE,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,QAAQ,EAAE;wBACR,OAAO,EAAE,iBAAiB;wBAC1B,GAAG,EAAE,KAAK,EAAE,SAAS;wBACrB,eAAe,EAAE,OAAO;wBACxB,YAAY,EAAE,IAAI,EAAE,8CAA8C;qBACnE;oBACD,MAAM,EAAE;wBACN,MAAM,EAAE,aAAa;wBACrB,MAAM,EAAE,aAAa;wBACrB,QAAQ,EAAE,IAAI;wBACd,MAAM,EAAE,IAAI;wBACZ,SAAS,EAAE;4BACT,MAAM,EAAE,eAAe,EAAE,gCAAgC;4BACzD,MAAM,EAAE,eAAe;yBACxB;qBACF;iBACF,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAnYD,kDAmYC;AAED,6CAA6C;AAC7C,MAAM,4BAA4B;IACxB,MAAM,CAAC,QAAQ,GAA+B,IAAI,CAAC;IAE3D,gBAAwB,CAAC;IAElB,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE,CAAC;YAC3C,4BAA4B,CAAC,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACpE,CAAC;QACD,OAAO,4BAA4B,CAAC,QAAQ,CAAC;IAC/C,CAAC;;AAGH,gCAAgC;AACnB,QAAA,mBAAmB,GAAG,4BAA4B,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import { zodToJsonSchema } from 'zod-to-json-schema';\n/**\n * AI Agent Tool to execute terminal commands across different operating systems.\n * Provides robust execution and output capture for non-interactive commands.\n */\n\nimport * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup'; // Assuming tool.ts is in the same directory\nimport { AgentContext } from '../agents/agentUtilities/agent'; // Assuming AgentContext is defined here\nimport * as cp from 'child_process'; // Node.js child_process module\nimport { Readable } from 'stream'; // Node.js stream module\nimport { promisify } from 'util'; // Node.js util module for promises\nimport { z } from 'zod'; // Zod for schema validation\n// Assume these are available paths in the project\nimport { Logger } from '../logger'; // Assuming logging utility is available\nimport { getConfig } from '../config'; // Assuming configuration utility is available\nimport * as os from 'os'; // Node.js os module\nimport * as nodePath from 'path'; // Node.js path module - Renamed to avoid clash if 'path' variable is used\n\n// Promisify cp.exec for fallback or alternative execution strategy\nconst execP = promisify(cp.exec);\n\n/**\n * Configuration interface for the TerminalCommandTool.\n */\ninterface TerminalToolConfig {\n  /** Maximum buffer size in bytes for captured stdout/stderr. */\n  maxOutputBufferBytes: number;\n  /** Default timeout for commands in milliseconds. */\n  defaultTimeoutMs: number;\n  /** Default shell to use if not specified by the agent. */\n  defaultShell: string;\n  /** Optional: List of allowed commands (regex or string prefixes). If empty, all commands are allowed (use with caution). */\n  allowedCommands?: string[];\n  /** Optional: List of forbidden commands (regex or string prefixes). Applied after allowedCommands. */\n  forbiddenCommands?: string[];\n  /** Use `exec` instead of `spawn` for simpler command execution (less control over streams/timeout). */\n  useExecForSimpleCommands?: boolean;\n}\n/**\n * Utility to get the default shell based on the operating system and configuration.\n * @returns The path to the default shell executable string.\n */\nexport function getDefaultShell(): string {\n  const configShell = getConfig<string | undefined>('tools.terminalCommand.defaultShell', undefined);\n  if (configShell) {\n    // Validate if configured shell exists and is executable? Too complex for polyfill. Assume valid.\n    return configShell;\n  }\n\n  const platform = os.platform();\n  if (platform === 'win32') {\n    // On Windows, prefer PowerShell if available, otherwise Cmd.exe\n    // Checking for existence is complex asyncly. Just return potential paths.\n    // PowerShell path can vary (System32, SysWOW64). Using just \"powershell.exe\" relies on PATH.\n    // Cmd.exe is usually in System32.\n    // Let's prioritize PowerShell if it's the OS default, otherwise Cmd.\n    // Relying on spawn's shell option interpretation is best practice.\n    // 'powershell' or 'cmd' might work directly with spawn.\n    // Use 'cmd' as a safer default fallback for Windows if no config.\n    return 'cmd.exe';\n  } else {\n    // On Unix-like systems, default to bash\n    return process.env.SHELL || '/bin/bash';\n  }\n}\n\n/**\n * Validates if a command string is allowed based on configured allow/forbidden lists.\n * Note: This is a basic check and not a full security sandbox. Shell commands\n * are inherently powerful and complex.\n * @param command The full command string including args.\n * @param config The tool's configuration with allowed/forbidden lists.\n * @returns True if the command is allowed, false otherwise.\n */\nfunction isCommandAllowed(command: string, _config: any): boolean {\n  const commandLower = command.toLowerCase();\n\n  // If allow-list exists, command must match at least one pattern\n  if (_config.allowedCommands && _config.allowedCommands.length > 0) {\n    let allowedByList = false;\n    for (const allowedPattern of _config.allowedCommands) {\n      try {\n        // Test as regex if it looks like one, otherwise as simple substring/prefix\n        if (allowedPattern.startsWith('/') && allowedPattern.endsWith('/')) {\n          const regex = new RegExp(allowedPattern.slice(1, -1));\n          if (regex.test(command)) { allowedByList = true; break; }\n        } else if (command.startsWith(allowedPattern)) { // Simple prefix match\n          allowedByList = true;\n          break;\n        } else if (command.includes(allowedPattern)) { // Simple includes match (less secure)\n          allowedByList = true;\n          break;\n        }\n      } catch (e) {\n        Logger.instance.error('Invalid regex in allowedCommands config:', e);\n        // Invalid regex in allow list - treat as not allowed for safety\n        return false;\n      }\n    }\n    if (!allowedByList) {\n      Logger.instance.warn('Command blocked: Not matched by any allowed pattern: \"' + command + '\"');\n      return false;\n    }\n  }\n\n  // If forbidden-list exists, command must *not* match any pattern\n  if (_config.forbiddenCommands && _config.forbiddenCommands.length > 0) {\n    for (const forbiddenPattern of _config.forbiddenCommands) {\n      try {\n        // Test as regex if it looks like one, otherwise as simple substring/prefix\n        if (forbiddenPattern.startsWith('/') && forbiddenPattern.endsWith('/')) {\n          const regex = new RegExp(forbiddenPattern.slice(1, -1));\n          if (regex.test(command)) {\n            Logger.instance.warn('Command blocked by forbidden regex pattern: \"' + command + '\"');\n            return false;\n          }\n        } else if (command.startsWith(forbiddenPattern)) { // Simple prefix match\n          Logger.instance.warn('Command blocked by forbidden prefix: \"' + command + '\"');\n          return false;\n        } else if (command.includes(forbiddenPattern)) { // Simple includes match\n          Logger.instance.warn('Command blocked by forbidden substring: \"' + command + '\"');\n          return false;\n        }\n      } catch (e) {\n        Logger.instance.error('Invalid regex in forbiddenCommands config:', e);\n        // Invalid regex in forbidden list - block command for safety\n        return false;\n      }\n    }\n  }\n\n  return true; // Passed checks\n}\n\n\n/**\n * Implements the ITool interface for executing terminal commands.\n * Supports structured input for command, arguments, cwd, env, timeout, shell, stdin, and output limits.\n * Uses child_process.spawn for better control and stream handling, with an option to use exec.\n */\nexport class TerminalCommandTool implements ITool {\n  readonly id = 'terminal_command';\n  readonly name = 'terminal_command'; // Use a machine-friendly name\n  readonly description = 'Executes a shell command or script in a terminal environment and returns output. Supports specifying working directory, environment variables, timeout, shell, and input.';\n  readonly category = 'System';\n  readonly type = 'single-action'; // Define as single-action tool\n  readonly schema = z.object({\n    command: z.string().min(1).describe('The command string or executable path (e.g., \"ls\", \"dir\", \"git status\", \"python script.py\").'),\n    args: z.string().array().optional().describe('Optional arguments to pass to the command.'),\n    cwd: z.string().optional().describe('The current working directory for the command. Defaults to the VS Code workspace root or the current process directory.'),\n    env: z.record(z.string(), z.string().nullable().optional()).optional().describe('Environment variables to set for the command process (e.g., { \"NODE_ENV\": \"development\", \"PATH\": \"/usr/local/bin\" }). Null/undefined values remove inherited vars.'),\n    timeout: z.number().int().positive().optional().describe('Maximum duration in milliseconds for the command to run.'),\n    shell: z.string().optional().describe('The shell executable to use (e.g., \"bash\", \"powershell\", \"cmd.exe\").'),\n    stdin: z.string().optional().describe('Optional input string to pipe to the command\\'s stdin.'),\n    outputLimitBytes: z.number().int().positive().optional().describe('Maximum bytes to capture from stdout and stderr before truncating.')\n  });\n  readonly actions: Record<string, any> = {}; // Required by ITool\n\n  // Define the input schema using Zod for validation and structured input\n  readonly singleActionSchema = z.object({\n    /** The command string or executable path. Required. */\n    command: z.string().min(1).describe('The command string or executable path (e.g., \"ls\", \"dir\", \"git status\", \"python script.py\").'),\n    /** Optional array of arguments to pass to the command. */\n    args: z.string().array().optional().describe('Optional arguments to pass to the command.'),\n    /** The current working directory for the command. Defaults to the workspace root if available. */\n    cwd: z.string().optional().describe('The current working directory for the command. Defaults to the VS Code workspace root or the current process directory.'),\n    /** Environment variables to set for the command process. Merges with parent process env by default. Use null or undefined values to remove inherited env vars. */\n    env: z.record(z.string(), z.string().nullable().optional()).optional().describe('Environment variables to set for the command process (e.g., { \"NODE_ENV\": \"development\", \"PATH\": \"/usr/local/bin\" }). Null/undefined values remove inherited vars.'),\n    /** Maximum duration in milliseconds for the command to run before being terminated. */\n    timeout: z.number().int().positive().optional().describe('Maximum duration in milliseconds for the command to run.'),\n    /** The shell executable to use to run the command (e.g., \"bash\", \"powershell\", \"cmd.exe\"). Defaults to the system default or configured default. */\n    shell: z.string().optional().describe('The shell executable to use (e.g., \"bash\", \"powershell\", \"cmd.exe\").'),\n    /** Optional input string to pipe to the command's stdin. */\n    stdin: z.string().optional().describe('Optional input string to pipe to the command\\'s stdin.'),\n    /** Maximum bytes to capture from stdout and stderr. Output exceeding this limit will be truncated. */\n    outputLimitBytes: z.number().int().positive().optional().describe('Maximum bytes to capture from stdout and stderr before truncating.'),\n  }).describe('Input parameters for executing a terminal command.');\n\n  public config: TerminalToolConfig;\n\n  constructor() {\n    // Load configuration during instantiation\n    const allowedCommands = getConfig<string[] | undefined>('tools.terminalCommand.allowedCommands', undefined) || [];\n    const forbiddenCommands = getConfig<string[] | undefined>('tools.terminalCommand.forbiddenCommands', undefined) || [];\n\n    this.config = {\n      maxOutputBufferBytes: getConfig<number>('tools.terminalCommand.maxOutputBufferBytes', 10 * 1024 * 1024), // Default 10MB\n      defaultTimeoutMs: getConfig<number>('tools.terminalCommand.defaultTimeoutMs', 60000), // Default 60 seconds\n      defaultShell: getConfig<string | undefined>('tools.terminalCommand.defaultShell', undefined) || getDefaultShell(),\n      allowedCommands: allowedCommands.length > 0 ? allowedCommands : undefined,\n      forbiddenCommands: forbiddenCommands.length > 0 ? forbiddenCommands : undefined,\n      useExecForSimpleCommands: getConfig<boolean>('tools.terminalCommand.useExecForSimpleCommands', false),\n    };\n\n    Logger.instance.info('TerminalCommandTool initialized with config:', {\n      ...this.config,\n      allowedCommands: this.config.allowedCommands ? this.config.allowedCommands.length + ' patterns' : 'all',\n      forbiddenCommands: this.config.forbiddenCommands ? this.config.forbiddenCommands.length + ' patterns' : 'none',\n      defaultShell: this.config.defaultShell,\n      useExec: this.config.useExecForSimpleCommands\n    });\n  }\n\n  /**\n     * Provides the structured definition for the LLM.\n     * Conforms to ITool.getDefinitionForModel.\n     * @returns The JSON schema definition suitable for LLM tool calling.\n     */\n  getDefinitionForModel(): any {\n    // Use zod-to-json-schema to convert Zod schema to JSON Schema\n    if (this.type === 'single-action' && this.singleActionSchema) {\n      const schemaObject = zodToJsonSchema(this.singleActionSchema);\n\n      // Attempt to fix potential Zod toJSON issues or enrich\n      if (\n        schemaObject &&\n        typeof schemaObject === 'object' &&\n        'properties' in schemaObject &&\n        schemaObject.properties &&\n        'env' in schemaObject.properties &&\n        schemaObject.properties.env\n      ) {\n        schemaObject.properties.env = {\n          type: 'object',\n          description: schemaObject.properties.env.description,\n          additionalProperties: { type: ['string', 'null'] }\n        };\n      }\n\n      return {\n        name: this.name,\n        description: this.description,\n        parameters: schemaObject,\n      };\n    } else {\n      // This case should not happen for this tool's definition, but included for safety\n      Logger.instance.error('TerminalCommandTool getDefinitionForModel: Tool type is not single-action or singleActionSchema is missing.');\n      // Return a minimal definition if schema generation fails\n      return {\n        name: this.name,\n        description: this.description,\n        parameters: {\n          type: 'object',\n          properties: {\n            command: { type: 'string', description: 'Shell command string to execute.' }\n          },\n          required: ['command']\n        },\n      };\n    }\n  }\n\n\n  /**\n     * Executes a terminal command. Uses child_process.spawn by default,\n     * with an option for cp.exec for simple cases.\n     * @param input - The validated input conforming to singleActionSchema.\n     * @param context - Optional agent context.\n     * @returns A promise resolving to the tool's result.\n     */\n  async execute(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // Validate input strictly against the Zod schema here to be safe,\n    // although the calling framework *should* have done this.\n    const parsedInput = this.singleActionSchema.safeParse(input);\n    if (!parsedInput.success) {\n      const formattedError = parsedInput.error.format();\n      const errorMsg = 'Invalid input for tool \\'' + this.name + '\\': ' +\n        Object.entries(formattedError)\n          .filter(([key]) => key !== '_errors')\n          .map(([key, value]) => `${key}: ${(value as any)._errors.join(', ')}`)\n          .join('; ');\n      Logger.instance.error(errorMsg, parsedInput.error);\n      return { success: false, error: errorMsg, toolId: this.id, actionName };\n    }\n    const validatedInput = parsedInput.data;\n\n    const command = validatedInput.command;\n    const args = validatedInput.args || [];\n    // Determine CWD: Use provided, then workspace root, then current process CWD\n    let cwd = validatedInput.cwd ?? (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0 ? vscode.workspace.workspaceFolders[0].uri.fsPath : process.cwd());\n    // Normalize cwd using nodePath to ensure cross-platform compatibility\n    cwd = nodePath.resolve(cwd);\n    // Prepare environment variables: Merge provided env with current process env. Handle null/undefined to remove.\n    const currentEnv = { ...process.env };\n    const env = validatedInput.env ? Object.keys(validatedInput.env).reduce((acc, key) => {\n      if (validatedInput.env![key] === null || validatedInput.env![key] === undefined) {\n        delete acc[key]; // Remove from environment\n      } else {\n        acc[key] = validatedInput.env![key] as string; // Add or overwrite\n      }\n      return acc;\n    }, currentEnv) : currentEnv;\n    const timeoutMs = validatedInput.timeout ?? this.config.defaultTimeoutMs;\n    const shell = validatedInput.shell ?? this.config.defaultShell;\n    const stdinInput = validatedInput.stdin;\n    const outputLimitBytes = validatedInput.outputLimitBytes ?? this.config.maxOutputBufferBytes;\n\n    // Command Security Check on the combined command string\n    const fullCommandString = command + (args.length > 0 ? ' ' + args.join(' ') : '');\n    if (!isCommandAllowed(fullCommandString, this.config)) {\n      const errorMsg = 'Execution of command \"' + fullCommandString + '\" is blocked by security configuration.';\n      Logger.instance.error(errorMsg);\n      return { success: false, error: errorMsg, toolId: this.id };\n    }\n\n\n    Logger.instance.info('Executing command: \"' + fullCommandString + '\"');\n    Logger.instance.debug('Options: cwd=\"' + cwd + '\", shell=\"' + shell + '\", timeout=' + timeoutMs + 'ms, outputLimit=' + outputLimitBytes + ' bytes, stdin=' + (stdinInput ? 'present' : 'absent') + ', envKeys=[' + Object.keys(env || {}).join(', ') + ']');\n\n    // Decide between spawn and exec based on config or input complexity (e.g., presence of stdin)\n    const useExec = this.config.useExecForSimpleCommands && !stdinInput && args.length === 0;\n\n    if (useExec) {\n      Logger.instance.debug('Using cp.exec for simple command.');\n      try {\n        // cp.exec combines stdout/stderr unless options.maxBuffer is exceeded, then it errors.\n        // It also has a default timeout option.\n        const { stdout, stderr } = await execP(fullCommandString, {\n          cwd,\n          env,\n          shell, // Pass shell option\n          timeout: timeoutMs,\n          maxBuffer: outputLimitBytes, // Limit output buffer\n        });\n\n        // cp.exec resolves even on non-zero exit codes, unless stdio exceeds maxBuffer or spawn fails\n        // We need to manually check for errors captured in stderr or non-zero exit code if using exec.\n        // Promisified exec throws on non-zero exit code by default if stdout is empty.\n        // If stderr is non-empty, it's often an error.\n        if (stderr) {\n          Logger.instance.warn('Command \"' + fullCommandString + '\" executed with stderr:\\n' + stderr);\n        }\n\n\n        return {\n          success: true, // Assume success if execP didn't throw and stderr check is handled\n          output: {\n            stdout: stdout,\n            stderr: stderr,\n            // exec doesn't provide explicit exitCode/signal in successful result\n            exitCode: null,\n            signal: null,\n            truncated: { stdout: false, stderr: false }, // exec throws on maxBuffer exceeded\n          },\n          error: stderr || (stdout ? undefined : 'Command produced no output'), // Treat non-empty stderr as a potential error indicator\n          toolId: this.id,\n          metadata: {\n            command: fullCommandString,\n            cwd, shell, timeoutMs,\n            executionMethod: 'exec',\n          },\n        };\n      } catch (error: any) {\n        // exec errors on spawn failure, timeout, or maxBuffer exceeded\n        Logger.instance.error('cp.exec failed for command \"' + fullCommandString + '\":', error);\n        let errorMessage = error.message || 'Command execution failed.';\n        let stdout = error.stdout || '';\n        let stderr = error.stderr || '';\n\n        // Improve error message for timeouts or buffer limits from exec\n        if (error.killed && error.signal) {\n          errorMessage = 'Command timed out (' + timeoutMs + 'ms) and was terminated with signal ' + error.signal + '.';\n        } else if (error.code !== undefined) {\n          errorMessage = ('Command exited with non-zero code ' + error.code + '. ' + (error.message || '')).trim();\n        } else if (error.message && error.message.includes('maxBuffer exceeded')) {\n          errorMessage = 'Command output exceeded limit (' + outputLimitBytes + ' bytes).';\n          stdout = stdout.substring(0, outputLimitBytes); // Truncate captured output\n          stderr = stderr.substring(0, outputLimitBytes);\n        }\n\n        return {\n          success: false,\n          error: errorMessage,\n          toolId: this.id,\n          metadata: {\n            command: fullCommandString,\n            cwd, shell, timeoutMs,\n            executionMethod: 'exec',\n            execErrorCode: error.code,\n            execSignal: error.signal,\n            execKilled: error.killed,\n          },\n          output: { stdout, stderr, exitCode: error.code, signal: error.signal, truncated: { stdout: stdout.length === outputLimitBytes, stderr: stderr.length === outputLimitBytes } }\n        };\n      }\n    } else {\n      Logger.instance.debug('Using cp.spawn for robust command execution.');\n      // Use spawn for better control over streams and larger outputs\n      const process = cp.spawn(command, args, {\n        cwd,\n        env,\n        shell: shell, // Pass the specified shell executable\n        timeout: timeoutMs, // Use spawn's built-in timeout (Node 10+)\n        // stdio: 'pipe' is default, handles stdin, stdout, stderr streams\n      });\n\n      const stdoutBuffer = Buffer.from('');\n      const stderrBuffer = Buffer.from('');\n      const stdoutTruncated = false;\n      const stderrTruncated = false;\n\n      const collectOutput = (stream: Readable, buffer: Buffer, truncated: boolean) => {\n        return new Promise<[Buffer, boolean]>((resolve, reject) => {\n          let currentBuffer = Buffer.from(buffer);\n          let currentTruncated = truncated;\n\n          stream.on('data', (data: Buffer) => {\n            if (currentBuffer.length + data.length <= outputLimitBytes) {\n              currentBuffer = Buffer.concat([currentBuffer, data]);\n            } else if (!currentTruncated) {\n              // Only capture up to the limit and mark as truncated\n              currentBuffer = Buffer.concat([currentBuffer, data.slice(0, outputLimitBytes - currentBuffer.length)]);\n              currentTruncated = true;\n              Logger.instance.warn((stream === process.stdout ? 'Stdout' : 'Stderr') + ' truncated for command \"' + command + '\" due to limit ' + outputLimitBytes + '.');\n              // Optionally pause/destroy stream to stop receiving data\n              // stream.pause(); // Or stream.destroy();\n            }\n            // If truncated, ignore further data\n          });\n          stream.on('end', () => resolve([currentBuffer, currentTruncated]));\n          stream.on('error', reject); // Handle stream errors\n        });\n      };\n\n\n      try {\n        // Use Promise.all to capture both stdout and stderr streams\n        const stdoutPromise = process.stdout ? collectOutput(process.stdout, stdoutBuffer, stdoutTruncated) : Promise.resolve([Buffer.from(''), false]);\n        const stderrPromise = process.stderr ? collectOutput(process.stderr, stderrBuffer, stderrTruncated) : Promise.resolve([Buffer.from(''), false]);\n\n        // Wait for the process to close AND for output streams to end\n        const [exitResult, [finalStdoutBuffer, finalStdoutTruncated], [finalStderrBuffer, finalStderrTruncated]] = await Promise.all([\n          new Promise<{ code: number | null; signal: string | null; error?: Error }>((resolve, reject) => {\n            let error: Error | undefined;\n            process.on('error', (err: Error) => {\n              // Handles errors like command not found, permission issues *during spawn*\n              Logger.instance.error('Failed to spawn command \"' + command + '\": ' + err.message);\n              error = err; // Capture spawn error\n              // Reject the main process promise on spawn error\n              reject(err);\n            });\n            process.on('close', (code: number | null, signal: string | null) => {\n              Logger.instance.debug('Command \"' + command + '\" closed with code ' + code + ' and signal ' + signal + '.');\n              // Resolve the main process promise on close\n              resolve({ code, signal, error }); // Include potential spawn error\n            });\n            // Implement manual timeout for spawn\n            let killedByTimeout = false;\n            const timeoutHandle = setTimeout(() => {\n              if (!process.killed) {\n                Logger.instance.warn('Command \"' + command + '\" exceeded timeout (' + timeoutMs + 'ms) and will be killed.');\n                killedByTimeout = true;\n                process.kill('SIGTERM'); // or 'SIGKILL' if you want to force\n              }\n            }, timeoutMs);\n            process.on('close', () => {\n              clearTimeout(timeoutHandle);\n            });\n          }),\n          stdoutPromise,\n          stderrPromise,\n        ]);\n\n        const stdout = finalStdoutBuffer.toString('utf8');\n        const stderr = finalStderrBuffer.toString('utf8');\n\n        // Check for spawn errors captured by the 'error' event\n        if (exitResult.error) {\n          throw exitResult.error; // Re-throw spawn error\n        }\n\n        // Determine success based on exit code\n        const success = exitResult.code === 0; // Zero exit code usually indicates success\n\n        // Return the detailed result structure\n        return {\n          success: success,\n          output: {\n            stdout: stdout,\n            stderr: stderr,\n            exitCode: exitResult.code,\n            signal: exitResult.signal,\n            truncated: {\n              stdout: finalStdoutTruncated,\n              stderr: finalStderrTruncated,\n            },\n          },\n          error: success ? undefined : 'Command exited with code ' + (exitResult.code || 'null') + (exitResult.signal ? ' (signal ' + exitResult.signal + ')' : '') + (stderr ? '\\nStderr: ' + stderr.substring(0, 200) + '...' : ''),\n          toolId: this.id,\n          metadata: {\n            command: fullCommandString,\n            cwd, shell, timeoutMs,\n            executionMethod: 'spawn',\n          },\n        };\n\n      } catch (error: any) {\n        // This catch block handles errors during spawning or stream errors\n        Logger.instance.error('Error during cp.spawn or stream for command \"' + fullCommandString + '\":', error);\n        // Capture whatever output was collected before the error\n        const partialStdout = stdoutBuffer.toString('utf8');\n        const partialStderr = stderrBuffer.toString('utf8');\n\n        return {\n          success: false,\n          error: error.message || 'Failed to execute command (spawn error).',\n          toolId: this.id,\n          metadata: {\n            command: fullCommandString,\n            cwd, shell, timeoutMs,\n            executionMethod: 'spawn',\n            processError: true, // Indicate an error before command completion\n          },\n          output: {\n            stdout: partialStdout,\n            stderr: partialStderr,\n            exitCode: null,\n            signal: null,\n            truncated: {\n              stdout: stdoutTruncated, // Use truncation flags captured\n              stderr: stderrTruncated,\n            },\n          }\n        };\n      }\n    }\n  }\n}\n\n// Singleton pattern with lazy initialization\nclass TerminalCommandToolSingleton {\n  private static instance: TerminalCommandTool | null = null;\n\n  private constructor() { }\n\n  public static getInstance(): TerminalCommandTool {\n    if (!TerminalCommandToolSingleton.instance) {\n      TerminalCommandToolSingleton.instance = new TerminalCommandTool();\n    }\n    return TerminalCommandToolSingleton.instance;\n  }\n}\n\n// Export the singleton instance\nexport const terminalCommandTool = TerminalCommandToolSingleton.getInstance();\n"]}