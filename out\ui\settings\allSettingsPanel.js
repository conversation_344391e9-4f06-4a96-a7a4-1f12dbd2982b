"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllSettingsPanel = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
const settingsManager_1 = require("./settingsManager");
const webviewMessageHandler_1 = require("../utilities/webviewMessageHandler");
class AllSettingsPanel {
    context;
    memoryManager;
    providerManager;
    static viewType = 'codessa.settings';
    static currentPanel;
    panel;
    extensionUri;
    disposables = [];
    settingsManager;
    messageHandler;
    constructor(context, extensionUri, memoryManager, providerManager) {
        this.context = context;
        this.memoryManager = memoryManager;
        this.providerManager = providerManager;
        this.extensionUri = extensionUri;
        this.settingsManager = settingsManager_1.SettingsManager.getInstance();
        // Create and configure the webview panel
        this.panel = vscode.window.createWebviewPanel(AllSettingsPanel.viewType, 'Codessa Settings', vscode.ViewColumn.One, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'media')]
        });
        // Initialize the message handler after panel is created
        this.messageHandler = new webviewMessageHandler_1.WebviewMessageHandler(this.context);
        this.panel.webview.html = this.getWebviewContent();
        this.setupMessageHandlers();
        this.setupPanelEvents();
    }
    static async createOrShow(context, extensionUri, memoryManager, providerManager) {
        if (AllSettingsPanel.currentPanel) {
            AllSettingsPanel.currentPanel.panel.reveal(vscode.ViewColumn.One);
            return;
        }
        AllSettingsPanel.currentPanel = new AllSettingsPanel(context, extensionUri, memoryManager, providerManager);
        // Initialize settings manager if not already done
        if (!AllSettingsPanel.currentPanel.settingsManager.isReady()) {
            await AllSettingsPanel.currentPanel.settingsManager.initialize();
        }
    }
    setupMessageHandlers() {
        this.messageHandler = new webviewMessageHandler_1.WebviewMessageHandler(this.context);
        this.panel.webview.onDidReceiveMessage(async (message) => {
            try {
                // First try the settings-specific handlers
                switch (message.command) {
                    case 'updateSetting':
                        await this.handleSettingUpdate(message);
                        return;
                    case 'requestInitialSettings':
                        await this.sendInitialSettings();
                        return;
                    case 'requestProviderSettings':
                        await this.sendProviderSettings();
                        return;
                    case 'resetSetting':
                        await this.handleResetSetting(message);
                        return;
                    case 'exportSettings':
                        await this.handleExportSettings();
                        return;
                    case 'importSettings':
                        await this.handleImportSettings(message);
                        return;
                }
                // Then try the generic message handler
                const response = await this.messageHandler.handleMessage(message, this.panel);
                if (response !== null && response !== undefined) {
                    const responseMessage = {
                        command: `${message.command}Response`,
                        ...(typeof response === 'object' && !Array.isArray(response) ? response : { result: response })
                    };
                    this.panel.webview.postMessage(responseMessage);
                }
            }
            catch (error) {
                logger_1.logger.error('Error handling webview message:', error);
                this.panel.webview.postMessage({
                    command: 'error',
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }, null, this.disposables);
    }
    async handleSettingUpdate(message) {
        const { key, value } = message;
        if (!key) {
            logger_1.logger.error('No key provided for setting update');
            return;
        }
        const fullKey = `${message.section}.${key}`;
        logger_1.logger.debug(`Updating setting: ${fullKey} = ${value}`);
        try {
            // Use the settings manager to update the setting
            const validation = await this.settingsManager.setSetting(fullKey, value, true);
            if (validation.isValid) {
                // Notify webview of successful update
                this.panel.webview.postMessage({
                    command: 'settingUpdated',
                    section: message.section,
                    key,
                    value,
                    success: true,
                    warnings: validation.warnings
                });
                // Handle special cases
                if (message.section === 'general') {
                    switch (key) {
                        case 'theme':
                            // Handle theme change if needed
                            break;
                        case 'language':
                            // Handle language change if needed
                            break;
                    }
                }
            }
            else {
                // Notify webview of validation failure
                this.panel.webview.postMessage({
                    command: 'settingUpdated',
                    section: message.section,
                    key,
                    value,
                    success: false,
                    error: validation.errors.join(', '),
                    warnings: validation.warnings
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to update setting:', error);
            this.panel.webview.postMessage({
                command: 'settingUpdated',
                section: message.section,
                key,
                value,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async handleResetSetting(message) {
        const { key } = message;
        if (!key) {
            logger_1.logger.error('No key provided for reset setting');
            return;
        }
        const fullKey = `${message.section}.${key}`;
        try {
            await this.settingsManager.resetSetting(fullKey);
            this.panel.webview.postMessage({
                command: 'settingReset',
                section: message.section,
                key,
                success: true
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to reset setting:', error);
            this.panel.webview.postMessage({
                command: 'settingReset',
                section: message.section,
                key,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async handleExportSettings() {
        try {
            const settingsJson = this.settingsManager.exportSettings();
            this.panel.webview.postMessage({
                command: 'settingsExported',
                settings: settingsJson,
                success: true
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to export settings:', error);
            this.panel.webview.postMessage({
                command: 'settingsExported',
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async handleImportSettings(message) {
        const { settings } = message;
        if (!settings) {
            logger_1.logger.error('No settings provided for import');
            return;
        }
        try {
            const result = await this.settingsManager.importSettings(settings);
            if (result.success) {
                this.panel.webview.postMessage({
                    command: 'settingsImported',
                    success: true
                });
                // Reload settings in the webview
                await this.sendInitialSettings();
            }
            else {
                this.panel.webview.postMessage({
                    command: 'settingsImported',
                    success: false,
                    errors: result.errors
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to import settings:', error);
            this.panel.webview.postMessage({
                command: 'settingsImported',
                success: false,
                error: error instanceof Error ? error.message : 'Failed to load settings'
            });
        }
    }
    async sendInitialSettings() {
        try {
            // Get settings as unknown first, then cast to Settings
            const settings = this.settingsManager.getAllSettings();
            const sections = this.settingsManager.getSettingSections();
            await this.panel.webview.postMessage({
                type: 'initialSettings',
                settings,
                sections
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to load initial settings:', error);
            this.panel.webview.postMessage({
                command: 'updateSettings',
                settings: {},
                error: error instanceof Error ? error.message : 'Failed to load settings'
            });
        }
    }
    async sendProviderSettings() {
        try {
            const providers = this.providerManager.getAllProviders();
            const activeProvider = this.providerManager.getDefaultProviderId();
            this.panel.webview.postMessage({
                command: 'updateProviderSettings',
                providers,
                activeProvider
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to load provider settings:', error);
            this.panel.webview.postMessage({
                command: 'updateProviderSettings',
                providers: [],
                activeProvider: null,
                error: error instanceof Error ? error.message : 'Failed to load provider settings'
            });
        }
    }
    setupPanelEvents() {
        this.panel.onDidDispose(() => {
            AllSettingsPanel.currentPanel = undefined;
            this.dispose();
        }, null, this.disposables);
    }
    dispose() {
        if (this.panel) {
            this.panel.dispose();
        }
        while (this.disposables.length) {
            const disposable = this.disposables.pop();
            if (disposable) {
                disposable.dispose();
            }
        }
    }
    getWebviewContent() {
        const scriptUri = this.panel.webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'settings.js'));
        const stylesUri = this.panel.webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'styles', 'settings.css'));
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Codessa Settings</title>
            <link href="${stylesUri}" rel="stylesheet">
        </head>
        <body>
            <div id="codessa-settings-root"></div>
            <script src="${scriptUri}"></script>
            <script>
                // Ensure initial settings are always requested after script loads
                window.addEventListener('DOMContentLoaded', function() {
                    if (window.acquireVsCodeApi) {
                        const vscode = window.acquireVsCodeApi();
                        vscode.postMessage({ command: 'requestInitialSettings' });
                    } else if (window.parent) {
                        // fallback for some webview environments
                        window.parent.postMessage({ command: 'requestInitialSettings' }, '*');
                    }
                });
            </script>
        </body>
        </html>`;
    }
}
exports.AllSettingsPanel = AllSettingsPanel;
//# sourceMappingURL=allSettingsPanel.js.map