{"version": 3, "file": "codessaWorkflows.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/codessaWorkflows.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;AAEH,eAAe;AACf,0CAAwB;AAExB,8BAA8B;AAC9B,0CAAwB;AAExB,kBAAkB;AAClB,qDAAmC;AAEnC,+BAA+B;AAC/B,2CAAyB;AAEzB,+EAA+E;AAC/E,yCAMqB;AALnB,+GAAA,kBAAkB,OAAA;AAClB,gHAAA,mBAAmB,OAAA;AACnB,qHAAA,wBAAwB,OAAA;AACxB,8GAAA,iBAAiB,OAAA;AACjB,yHAAA,4BAA4B,OAAA;AAG9B,4BAA4B;AAC5B,sDAAoC;AAEpC,+BAA+B;AAC/B,oDAAoD;AACpD,+DAcgC;AAb9B,sEAAsE;AACtE,qIAAA,6BAA6B,OAAA;AAC7B,+HAA<PERSON>,uBAAuB,OAAA;AACvB,yHAAA,iBAAiB,OAAA;AACjB,0HAAA,kBAAkB,OAAA;AAClB,6HAAA,qBAAqB,OAAA;AACrB,6HAAA,qBAAqB,OAAA;AACrB,8HAAA,sBAAsB,OAAA;AACtB,4HAAA,oBAAoB,OAAA;AACpB,2HAAA,mBAAmB,OAAA;AACnB,wHAAA,gBAAgB,OAAA;AAChB,2HAAA,mBAAmB,OAAA;AACnB,uHAAA,eAAe,OAAA;AAGjB,sBAAsB;AACtB,6CAGuB;AAFrB,uHAAA,wBAAwB,OAAA;AACxB,qHAAA,sBAAsB,OAAA;AAGxB,6BAA6B;AAC7B,2DAAgE;AAAvD,8HAAA,wBAAwB,OAAA;AAEjC,qDAAqD;AACrD,+CAAqD;AAA5C,+GAAA,iBAAiB,OAAA;AAG1B,sBAAsB;AACtB,6CAAkD;AAAzC,gHAAA,iBAAiB,OAAA;AAE1B,8CAA8C;AAC9C,iCAAuC;AAA9B,iGAAA,UAAU,OAAA;AAGnB,wCAAwC;AACxC,6DAG+B;AAF7B,uIAAA,gCAAgC,OAAA;AAChC,kIAAA,2BAA2B,OAAA;AAG7B,uBAAuB;AACvB,iDAA+B;AAE/B,mBAAmB;AACnB,iDAA+B", "sourcesContent": ["/**\n * Codessa workflow implementation\n *\n * Exports types, graph implementation, registry, memory implementation, and\n * various templates for creating Codessa workflows.\n */\n\n// Export types\nexport * from './types';\n\n// Export graph implementation\nexport * from './graph';\n\n// Export registry\nexport * from './workflowRegistry';\n\n// Export memory implementation\nexport * from './memory';\n\n// Export templates (excluding createMemoryEnhancedWorkflow to avoid ambiguity)\nexport {\n  createChatWorkflow,\n  createReActWorkflow,\n  createMultiAgentWorkflow,\n  createTDDWorkflow,\n  createSprintPlanningWorkflow\n} from './templates';\n\n// Export advanced templates\nexport * from './advancedTemplates';\n\n// Export specialized templates\n// Use explicit re-exports to avoid naming conflicts\nexport {\n  // Explicitly re-export functions with unique names to avoid conflicts\n  createCodeRefactoringWorkflow,\n  createDebuggingWorkflow,\n  createAskWorkflow,\n  createEditWorkflow,\n  createCodeGenWorkflow,\n  createAgenticWorkflow,\n  createUXDesignWorkflow,\n  createDevOpsWorkflow,\n  createAgileWorkflow,\n  createXPWorkflow,\n  createScrumWorkflow,\n  WorkflowFactory\n} from './specializedTemplates';\n\n// Export PR workflows\nexport {\n  createPRCreationWorkflow,\n  createPRReviewWorkflow\n} from './prWorkflows';\n\n// Export checkpoint workflow\nexport { createCheckpointWorkflow } from './checkpointWorkflow';\n\n// Re-export checkpoint manager from its new location\nexport { checkpointManager } from '../../checkpoint';\nexport type { CheckpointManager, Checkpoint } from '../../checkpoint';\n\n// Export MCP workflow\nexport { createMCPWorkflow } from './mcpWorkflow';\n\n// Re-export MCP manager from its new location\nexport { mcpManager } from '../../mcp';\nexport type { MCPManager, MCPContext } from '../../mcp';\n\n// Export advanced refactoring workflows\nexport {\n  createPatternRefactoringWorkflow,\n  createTechnicalDebtWorkflow\n} from './advancedRefactoring';\n\n// Export vector stores\nexport * from './vectorStores';\n\n// Export polyfills\nexport * from './corePolyfill';\n"]}