/**
 * Model Context Protocol (MCP) Manager
 * 
 * This module provides integration with the Model Context Protocol (MCP),
 * which is a standard for exchanging context between different AI models
 * and tools. It allows:
 * - Sharing context between different models
 * - Standardizing context format
 * - Integrating with external services
 */

import { logger } from '../logger';

/**
 * MCP Context structure
 */
export interface MCPContext {
    version: string;
    metadata: {
        source: string;
        timestamp: string;
        sessionId?: string;
        logLevel?: string;
    };
    content: {
        text?: string;
        code?: {
            language: string;
            content: string;
            path?: string;
        }[];
        files?: {
            path: string;
            content: string;
            language?: string;
        }[];
        links?: {
            url: string;
            title?: string;
            description?: string;
        }[];
        images?: {
            url: string;
            alt?: string;
            caption?: string;
        }[];
    };
    tools?: Array<{
        name: string;
        description: string;
        parameters?: Record<string, unknown>;
        results?: Record<string, unknown> | Array<unknown> | string | number | boolean | null;
    }>;
}

/**
 * MCP Manager for handling Model Context Protocol integration
 */
import { IMCPManager } from '../managers';

export class MCPManager implements IMCPManager {
  private currentContext: MCPContext;
  private sessionId: string;
    
  constructor() {
    this.sessionId = `session-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    this.currentContext = this.createEmptyContext();
  }
    
  /**
     * Creates an empty MCP context
     */
  private createEmptyContext(): MCPContext {
    return {
      version: '1.0',
      metadata: {
        source: 'codessa',
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId
      },
      content: {}
    };
  }
    
  /**
     * Updates the current context with new content
     */
  updateContext(content: Partial<MCPContext['content']>): void {
    this.currentContext.content = {
      ...this.currentContext.content,
      ...content
    };
    this.currentContext.metadata.timestamp = new Date().toISOString();
        
    logger.info('Updated MCP context');
  }
    
  /**
     * Adds code to the context
     */
  addCode(language: string, content: string, path?: string): void {
    if (!this.currentContext.content.code) {
      this.currentContext.content.code = [];
    }
        
    this.currentContext.content.code.push({
      language,
      content,
      path
    });
        
    this.currentContext.metadata.timestamp = new Date().toISOString();
        
    logger.info(`Added code to MCP context: ${path || 'unnamed'}`);
  }
    
  /**
     * Adds a file to the context
     */
  addFile(path: string, content: string, language?: string): void {
    if (!this.currentContext.content.files) {
      this.currentContext.content.files = [];
    }
        
    this.currentContext.content.files.push({
      path,
      content,
      language
    });
        
    this.currentContext.metadata.timestamp = new Date().toISOString();
        
    logger.info(`Added file to MCP context: ${path}`);
  }
    
  /**
     * Adds a link to the context
     */
  addLink(url: string, title?: string, description?: string): void {
    if (!this.currentContext.content.links) {
      this.currentContext.content.links = [];
    }
        
    this.currentContext.content.links.push({
      url,
      title,
      description
    });
        
    this.currentContext.metadata.timestamp = new Date().toISOString();
        
    logger.info(`Added link to MCP context: ${url}`);
  }
    
  /**
     * Adds a tool result to the context
     */
  addToolResult(
    name: string,
    description: string,
    parameters?: Record<string, unknown>,
    results?: Record<string, unknown> | Array<unknown> | string | number | boolean | null
  ): void {
    if (!this.currentContext.tools) {
      this.currentContext.tools = [];
    }
        
    this.currentContext.tools.push({
      name,
      description,
      parameters,
      results
    });
        
    this.currentContext.metadata.timestamp = new Date().toISOString();
        
    logger.info(`Added tool result to MCP context: ${name}`);
  }
    
  /**
     * Gets the current context
     */
  getContext(): MCPContext {
    return { ...this.currentContext };
  }
  
  /**
   * Gets the current context (alias for getContext for IMCPManager interface)
   */
  getCurrentContext(): MCPContext {
    return this.getContext();
  }
    
  /**
     * Exports the context to a JSON string
     */
  exportContext(): string {
    return JSON.stringify(this.currentContext, null, 2);
  }
    
  /**
     * Imports a context from a JSON string
     */
  importContext(json: string): void {
    try {
      const context = JSON.parse(json) as MCPContext;
            
      // Validate the context
      if (!context.version || !context.metadata || !context.metadata.source) {
        throw new Error('Invalid MCP context format');
      }
            
      this.currentContext = context;
      logger.info('Imported MCP context');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Error importing MCP context: ${errorMessage}`);
      throw error;
    }
  }
    
  /**
     * Clears the current context
     */
  clearContext(): void {
    this.currentContext = this.createEmptyContext();
    logger.info('Cleared MCP context');
  }
}

// Create a singleton instance
export const mcpManager = new MCPManager();
