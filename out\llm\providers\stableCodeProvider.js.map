{"version": 3, "file": "stableCodeProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/stableCodeProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAGpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;;;;GAKG;AACH,MAAa,kBAAmB,SAAQ,iCAAe;IAC5C,UAAU,GAAG,YAAY,CAAC;IAC1B,WAAW,GAAG,YAAY,CAAC;IAC3B,WAAW,GAAG,yCAAyC,CAAC;IACxD,OAAO,GAAG,iEAAiE,CAAC;IAC5E,cAAc,GAAG,IAAI,CAAC,CAAC,gCAAgC;IACvD,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,6CAA6C,CAAC;IAChE,YAAY,GAAG,0CAA0C,CAAC;IAE3D,MAAM,GAAQ,IAAI,CAAC;IAE3B,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBACzE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACxD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,EAAE,qBAAqB;gBACrC,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,MAAM,EAAE;oBACnC,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,MAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,uDAAuD,EAAE,CAAC;QACzF,CAAC;QAED,IAAI,CAAC;YACH,wFAAwF;YACxF,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAChF,MAAM,QAAQ,GAAG,GAAG,OAAO,EAAE,CAAC;YAE9B,qBAAqB;YACrB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,6CAA6C;YAC7C,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjC,iDAAiD;gBACjD,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACxB,MAAM,IAAI,eAAe,MAAM,CAAC,YAAY,MAAM,CAAC;gBACrD,CAAC;gBAED,0BAA0B;gBAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;4BAC5B,MAAM,IAAI,aAAa,OAAO,CAAC,OAAO,MAAM,CAAC;wBAC/C,CAAC;6BAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;4BACxC,MAAM,IAAI,kBAAkB,OAAO,CAAC,OAAO,MAAM,CAAC;wBACpD,CAAC;6BAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;4BACrC,MAAM,IAAI,eAAe,OAAO,CAAC,OAAO,MAAM,CAAC;wBACjD,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,2BAA2B;oBAC3B,MAAM,IAAI,aAAa,MAAM,CAAC,MAAM,MAAM,CAAC;gBAC7C,CAAC;gBAED,gFAAgF;gBAChF,MAAM,IAAI,iBAAiB,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,2CAA2C;gBAC3C,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACxB,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,MAAM,CAAC;gBACzC,CAAC;gBAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;wBACrC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,MAAM,CAAC;oBACrC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;gBAC1B,CAAC;YACH,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,uBAAuB;YACvB,MAAM,WAAW,GAAG;gBAClB,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE;oBACV,cAAc,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG;oBACvC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,KAAK,EAAE,IAAI;oBACX,SAAS,EAAE,IAAI;oBACf,gBAAgB,EAAE,KAAK;oBACvB,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;iBACzD;aACF,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,uCAAuC,OAAO,EAAE,CAAC,CAAC;YAE/D,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;wBACpD,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBACvG,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE;gBAC7D,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,8CAA8C;YAC9C,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;YACvE,CAAC;YAED,qBAAqB;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,iEAAiE;YACjE,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/C,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;oBAC7B,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;gBACrC,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,MAAM;gBACpB,KAAK,EAAE;oBACL,YAAY,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;oBAClD,gBAAgB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;iBACxD;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,YAAY,GAAG,gCAAgC,CAAC;YAEpD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,yBAAyB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACnH,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,8DAA8D;QAC9D,OAAO;YACL;gBACE,EAAE,EAAE,0CAA0C;gBAC9C,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,oDAAoD;gBACjE,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,4BAA4B;aAC1C;YACD;gBACE,EAAE,EAAE,4CAA4C;gBAChD,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,qDAAqD;gBAClE,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,4BAA4B;aAC1C;YACD;gBACE,EAAE,EAAE,+CAA+C;gBACnD,IAAI,EAAE,6BAA6B;gBACnC,WAAW,EAAE,qEAAqE;gBAClF,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,4BAA4B;aAC1C;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,+DAA+D;aACzE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,QAAQ,GAAG,OAAO,CAAC;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChD,MAAM,EAAE,oBAAoB;gBAC5B,UAAU,EAAE;oBACV,cAAc,EAAE,EAAE;oBAClB,gBAAgB,EAAE,KAAK;iBACxB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAClB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,gEAAgE,OAAO,IAAI;iBACrF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uDAAuD;iBACjE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,YAAY,GAAG,uCAAuC,CAAC;YAE3D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,yEAAyE;gBACtF,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,gGAAgG;gBAC7G,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,qCAAqC;gBAClD,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,0CAA0C;oBAC1C,4CAA4C;oBAC5C,+CAA+C;iBAChD;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAzTD,gDAyTC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { <PERSON><PERSON><PERSON><PERSON>ider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for StableCode models\n * \n * StableCode is a family of code-specialized LLMs from Stability AI\n * It can be used via Hugging Face or self-hosted\n */\nexport class StableCodeProvider extends BaseLLMProvider {\n  readonly providerId = 'stablecode';\n  readonly displayName = 'StableCode';\n  readonly description = 'Code-specialized LLMs from Stability AI';\n  readonly website = 'https://huggingface.co/stabilityai/stablecode-instruct-alpha-3b';\n  readonly requiresApiKey = true; // Required for Hugging Face API\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api-inference.huggingface.co/models';\n  readonly defaultModel = 'stabilityai/stablecode-instruct-alpha-3b';\n\n  private client: any = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        logger.info('StableCode configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (!apiKey) {\n      logger.warn('API key not set for StableCode provider.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 60000, // 60 seconds timeout\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      logger.info('StableCode client initialized successfully.');\n    } catch (error) {\n      logger.error('Failed to initialize StableCode client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using StableCode models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    _tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'StableCode provider not configured (API key missing?)' };\n    }\n\n    try {\n      // Prepare the model endpoint - for Hugging Face we need to specify the model in the URL\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n      const endpoint = `${modelId}`;\n\n      // Prepare the prompt\n      let prompt = '';\n            \n      // StableCode uses a specific format for chat\n      if (modelId.includes('instruct')) {\n        // Instruction-tuned models use a specific format\n        if (params.systemPrompt) {\n          prompt += `<|system|>\\n${params.systemPrompt}\\n\\n`;\n        }\n                \n        // Add history if provided\n        if (params.history && params.history.length > 0) {\n          for (const message of params.history) {\n            if (message.role === 'user') {\n              prompt += `<|user|>\\n${message.content}\\n\\n`;\n            } else if (message.role === 'assistant') {\n              prompt += `<|assistant|>\\n${message.content}\\n\\n`;\n            } else if (message.role === 'system') {\n              prompt += `<|system|>\\n${message.content}\\n\\n`;\n            }\n          }\n        } else {\n          // Just add the user prompt\n          prompt += `<|user|>\\n${params.prompt}\\n\\n`;\n        }\n                \n        // Add the assistant prefix to indicate we want the model to generate a response\n        prompt += '<|assistant|>\\n';\n      } else {\n        // Base models just use the prompt directly\n        if (params.systemPrompt) {\n          prompt += `${params.systemPrompt}\\n\\n`;\n        }\n                \n        if (params.history && params.history.length > 0) {\n          for (const message of params.history) {\n            prompt += `${message.content}\\n\\n`;\n          }\n        } else {\n          prompt += params.prompt;\n        }\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Prepare request data\n      const requestData = {\n        inputs: prompt,\n        parameters: {\n          max_new_tokens: params.maxTokens || 512,\n          temperature: params.temperature || 0.7,\n          top_p: 0.95,\n          do_sample: true,\n          return_full_text: false,\n          stop: params.stopSequences || ['<|user|>', '<|system|>']\n        }\n      };\n\n      logger.debug(`Sending request to StableCode model ${modelId}`);\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            logger.info('StableCode request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          logger.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request\n      const response = await this.client.post(endpoint, requestData, {\n        signal: abortController?.signal\n      });\n\n      // Check for cancellation again after API call\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled during processing' };\n      }\n\n      // Parse the response\n      const result = response.data;\n            \n      // Hugging Face Inference API returns an array of generated texts\n      let content = '';\n      if (Array.isArray(result) && result.length > 0) {\n        if (result[0].generated_text) {\n          content = result[0].generated_text;\n        }\n      }\n\n      return {\n        content,\n        finishReason: 'stop',\n        usage: {\n          promptTokens: prompt.length / 4, // Rough estimate\n          completionTokens: content.length / 4, // Rough estimate\n        }\n      };\n    } catch (error: any) {\n      logger.error('StableCode generate error:', error);\n      let errorMessage = 'Failed to call StableCode API.';\n\n      if (error.response) {\n        errorMessage = `StableCode API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available StableCode models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    // StableCode models are fixed, so we return a predefined list\n    return [\n      {\n        id: 'stabilityai/stablecode-instruct-alpha-3b',\n        name: 'StableCode Instruct 3B',\n        description: 'Instruction-tuned StableCode model (3B parameters)',\n        contextWindow: 4096,\n        pricingInfo: 'Free with Hugging Face API'\n      },\n      {\n        id: 'stabilityai/stablecode-completion-alpha-3b',\n        name: 'StableCode Completion 3B',\n        description: 'Completion-focused StableCode model (3B parameters)',\n        contextWindow: 4096,\n        pricingInfo: 'Free with Hugging Face API'\n      },\n      {\n        id: 'stabilityai/stablecode-completion-alpha-3b-4k',\n        name: 'StableCode Completion 3B 4K',\n        description: 'Completion-focused StableCode model with 4K context (3B parameters)',\n        contextWindow: 4096,\n        pricingInfo: 'Free with Hugging Face API'\n      }\n    ];\n  }\n\n  /**\n     * Test connection to StableCode\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'StableCode client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Simple test request to check if the API is working\n      const endpoint = modelId;\n      const response = await this.client.post(endpoint, {\n        inputs: 'def hello_world():',\n        parameters: {\n          max_new_tokens: 10,\n          return_full_text: false\n        }\n      });\n\n      if (response.data) {\n        return {\n          success: true,\n          message: `Successfully connected to Hugging Face API and tested model '${modelId}'.`\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Connected to API but received an unexpected response.'\n        };\n      }\n    } catch (error: any) {\n      logger.error('StableCode connection test failed:', error);\n      let errorMessage = 'Failed to connect to Hugging Face API';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'Hugging Face API Key',\n        description: 'Your Hugging Face API key (from https://huggingface.co/settings/tokens)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The Hugging Face Inference API endpoint (default: https://api-inference.huggingface.co/models)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default StableCode model to use',\n        required: false,\n        type: 'select',\n        options: [\n          'stabilityai/stablecode-instruct-alpha-3b',\n          'stabilityai/stablecode-completion-alpha-3b',\n          'stabilityai/stablecode-completion-alpha-3b-4k'\n        ]\n      }\n    ];\n  }\n}\n"]}