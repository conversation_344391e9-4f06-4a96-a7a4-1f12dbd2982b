{"version": 3, "file": "ttsSettings.js", "sourceRoot": "", "sources": ["../../../src/ui/settings/ttsSettings.ts"], "names": [], "mappings": ";;;AAyBA,kDAEC;AA1BD,yCAAsC;AACtC,2CAAwC;AAUxC,IAAY,WAOX;AAPD,WAAY,WAAW;IACnB,gCAAiB,CAAA;IACjB,4BAAa,CAAA;IACb,sCAAuB,CAAA;IACvB,gCAAiB,CAAA;IACjB,gCAAiB,CAAA;IACjB,wCAAyB,CAAA;AAC7B,CAAC,EAPW,WAAW,2BAAX,WAAW,QAOtB;AAED,gDAAgD;AACjC,cAAM,GAAkD,WAAW,SAA3D,YAAI,GAA4C,WAAW,OAArD,iBAAS,GAAiC,WAAW,YAA1C,cAAM,GAAyB,WAAW,SAAlC,cAAM,GAAiB,WAAW,SAA1B,kBAAU,GAAK,WAAW,YAAC;AAEnF,6CAA6C;AAC7C,SAAgB,mBAAmB;IACjC,OAAO,CAAC,cAAM,EAAE,YAAI,EAAE,iBAAS,EAAE,cAAM,EAAE,cAAM,EAAE,kBAAU,CAAC,CAAC;AAC/D,CAAC;AA2BD,MAAa,kBAAkB;IAMQ;IAL7B,MAAM,CAAC,QAAQ,CAAqB;IACpC,SAAS,CAAc;IACvB,OAAO,CAA+B;IACtC,YAAY,GAAwB,EAAE,CAAC;IAE/C,YAAqC,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QACnE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,OAAgC;QACxD,OAAO,CAAC,GAAG,CAAC,oDAAoD,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QACzF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACrC,CAAC;IAEO,YAAY;QAClB,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC;YAC1D,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,MAAM,CAAC;YACzE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;YACnD,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC;YAClD,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;YACpD,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC;YACtD,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;YAC7D,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;YACjE,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBAC3D,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBAC3D,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,qBAAqB,EAAE,EAAE,CAAC;gBACnE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC;aAC1D;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC;gBAClE,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC;gBACxE,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;gBAChE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC;gBACvE,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC;gBACnE,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC;aAC5E;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,yCAAyC;YACzC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAEnD,6BAA6B;YAC7B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAE/C,8BAA8B;YAC9B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YAEzD,2BAA2B;YAC3B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAEnD,2BAA2B;YAC3B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAEnD,+BAA+B;YAC/B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC5D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,gDAAgD;QAChD,OAAO,MAAM,qBAAS,CAAC,SAAS,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,sCAAsC;QACtC,OAAO,MAAM,qBAAS,CAAC,SAAS,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,6DAA6D;QAC7D,OAAO,MAAM,qBAAS,CAAC,SAAS,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,0BAA0B;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC7B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,kCAAkC;QAClC,OAAO;YACL,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,2BAA2B,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;YAChI,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,2BAA2B,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;YAC9H,EAAE,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;YAC9H,EAAE,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;SAC7H,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,oBAAoB;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC7B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO;YACL,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;YAC/E,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;YAC7E,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;YAC/E,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;YAC7E,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;YAC7E,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE;SACpF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,oBAAoB;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACjC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,mCAAmC;QACnC,OAAO;YACL,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,UAAU,EAAE;YACvG,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,UAAU,EAAE;YACnG,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,UAAU,EAAE;YACrG,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,UAAU,EAAE;YACrG,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,UAAU,EAAE;YACnG,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,UAAU,EAAE;SAClG,CAAC;IACJ,CAAC;IAEM,WAAW;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,QAA8B;QACxD,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,EAAE,CAAC;QACpD,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5E,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC9E,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxE,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACtE,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxE,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1E,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAChF,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEtF,gBAAgB;QAChB,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxF,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxF,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAChG,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAEtF,yBAAyB;QACzB,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC7F,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QACnG,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC3F,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC7F,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC/F,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IACzG,CAAC;IAEM,SAAS,CAAC,QAAqB;QACpC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IAEM,YAAY,CAAC,QAAqB,EAAE,MAAkB;QAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,MAAgB;QACzC,oCAAoC;QACpC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;IAC3E,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9C,CAAC;CACF;AAjMD,gDAiMC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from '../../logger';\nimport { SystemTTS } from './systemTTS';\n\nexport interface TTSVoice {\n    id: string;\n    name: string;\n    language: string;\n    gender?: string;\n    provider: TTSProvider;\n}\n\nexport enum TTSProvider {\n    SYSTEM = 'system',\n    EDGE = 'edge',\n    MICROSOFT = 'microsoft',\n    GOOGLE = 'google',\n    OPENAI = 'openai',\n    ELEVENLABS = 'elevenlabs'\n}\n\n// Export the enum values for use in other files\nexport const { SYSTEM, EDGE, MICROSOFT, GOOGLE, OPENAI, ELEVENLABS } = TTSProvider;\n\n// Utility function to get all provider types\nexport function getAllProviderTypes(): TTSProvider[] {\n  return [SYSTEM, EDGE, MICROSOFT, GOOGLE, OPENAI, ELEVENLABS];\n}\n\nexport interface TTSSettings {\n    enabled: boolean;\n    provider: TTSProvider;\n    voice: string;\n    rate: number;\n    pitch: number;\n    volume: number;\n    autoSpeak: boolean;\n    downloadPath: string;\n    apiKeys: {\n        google?: string;\n        openai?: string;\n        elevenlabs?: string;\n        azure?: string;\n    };\n    advanced: {\n        ssmlEnabled: boolean;\n        emotionControl: boolean;\n        speedBoost: boolean;\n        qualityMode: 'fast' | 'balanced' | 'high';\n        cacheEnabled: boolean;\n        streamingEnabled: boolean;\n    };\n}\n\nexport class TTSSettingsManager {\n  private static instance: TTSSettingsManager;\n  private _settings: TTSSettings;\n  private _voices: Map<TTSProvider, TTSVoice[]>;\n  private _disposables: vscode.Disposable[] = [];\n\n  private constructor(private readonly context: vscode.ExtensionContext) {\n    this._settings = this.loadSettings();\n    this._voices = new Map();\n    this.initializeVoices();\n  }\n\n  public static getInstance(context: vscode.ExtensionContext): TTSSettingsManager {\n    console.log('Getting TTS settings manager instance for context:', context.extensionPath);\n    if (!TTSSettingsManager.instance) {\n      TTSSettingsManager.instance = new TTSSettingsManager(context);\n    }\n    return TTSSettingsManager.instance;\n  }\n\n  private loadSettings(): TTSSettings {\n    return {\n      enabled: this.context.globalState.get('ttsEnabled', false),\n      provider: this.context.globalState.get('ttsProvider', TTSProvider.SYSTEM),\n      voice: this.context.globalState.get('ttsVoice', ''),\n      rate: this.context.globalState.get('ttsRate', 1.0),\n      pitch: this.context.globalState.get('ttsPitch', 1.0),\n      volume: this.context.globalState.get('ttsVolume', 1.0),\n      autoSpeak: this.context.globalState.get('ttsAutoSpeak', true),\n      downloadPath: this.context.globalState.get('ttsDownloadPath', ''),\n      apiKeys: {\n        google: this.context.globalState.get('ttsGoogleApiKey', ''),\n        openai: this.context.globalState.get('ttsOpenAIApiKey', ''),\n        elevenlabs: this.context.globalState.get('ttsElevenLabsApiKey', ''),\n        azure: this.context.globalState.get('ttsAzureApiKey', '')\n      },\n      advanced: {\n        ssmlEnabled: this.context.globalState.get('ttsSSMLEnabled', false),\n        emotionControl: this.context.globalState.get('ttsEmotionControl', false),\n        speedBoost: this.context.globalState.get('ttsSpeedBoost', false),\n        qualityMode: this.context.globalState.get('ttsQualityMode', 'balanced'),\n        cacheEnabled: this.context.globalState.get('ttsCacheEnabled', true),\n        streamingEnabled: this.context.globalState.get('ttsStreamingEnabled', true)\n      }\n    };\n  }\n\n  private async initializeVoices() {\n    try {\n      // Initialize System TTS voices (default)\n      const systemVoices = await this.fetchSystemVoices();\n      this._voices.set(TTSProvider.SYSTEM, systemVoices);\n\n      // Initialize Edge TTS voices\n      const edgeVoices = await this.fetchEdgeVoices();\n      this._voices.set(TTSProvider.EDGE, edgeVoices);\n\n      // Initialize Microsoft voices\n      const microsoftVoices = await this.fetchMicrosoftVoices();\n      this._voices.set(TTSProvider.MICROSOFT, microsoftVoices);\n\n      // Initialize Google voices\n      const googleVoices = await this.fetchGoogleVoices();\n      this._voices.set(TTSProvider.GOOGLE, googleVoices);\n\n      // Initialize OpenAI voices\n      const openaiVoices = await this.fetchOpenAIVoices();\n      this._voices.set(TTSProvider.OPENAI, openaiVoices);\n\n      // Initialize ElevenLabs voices\n      const elevenlabsVoices = await this.fetchElevenLabsVoices();\n      this._voices.set(TTSProvider.ELEVENLABS, elevenlabsVoices);\n    } catch (error) {\n      Logger.instance.error('Failed to initialize TTS voices:', error);\n    }\n  }\n\n  private async fetchSystemVoices(): Promise<TTSVoice[]> {\n    // Use SystemTTS to get platform-specific voices\n    return await SystemTTS.getVoices();\n  }\n\n  private async fetchMicrosoftVoices(): Promise<TTSVoice[]> {\n    // Use SystemTTS to get Windows voices\n    return await SystemTTS.getVoices();\n  }\n\n  private async fetchEdgeVoices(): Promise<TTSVoice[]> {\n    // Use SystemTTS to get Edge/browser voices (macOS/Linux/Win)\n    return await SystemTTS.getVoices();\n  }\n\n  private async fetchGoogleVoices(): Promise<TTSVoice[]> {\n    // Google Cloud TTS voices\n    const settings = this.getSettings();\n    if (!settings.apiKeys.google) {\n      return [];\n    }\n\n    // Return common Google TTS voices\n    return [\n      { id: 'en-US-Standard-A', name: 'English (US) - Standard A', language: 'en-US', gender: 'female', provider: TTSProvider.GOOGLE },\n      { id: 'en-US-Standard-B', name: 'English (US) - Standard B', language: 'en-US', gender: 'male', provider: TTSProvider.GOOGLE },\n      { id: 'en-US-Wavenet-A', name: 'English (US) - WaveNet A', language: 'en-US', gender: 'female', provider: TTSProvider.GOOGLE },\n      { id: 'en-US-Wavenet-B', name: 'English (US) - WaveNet B', language: 'en-US', gender: 'male', provider: TTSProvider.GOOGLE }\n    ];\n  }\n\n  private async fetchOpenAIVoices(): Promise<TTSVoice[]> {\n    // OpenAI TTS voices\n    const settings = this.getSettings();\n    if (!settings.apiKeys.openai) {\n      return [];\n    }\n\n    return [\n      { id: 'alloy', name: 'Alloy', language: 'en-US', provider: TTSProvider.OPENAI },\n      { id: 'echo', name: 'Echo', language: 'en-US', provider: TTSProvider.OPENAI },\n      { id: 'fable', name: 'Fable', language: 'en-US', provider: TTSProvider.OPENAI },\n      { id: 'onyx', name: 'Onyx', language: 'en-US', provider: TTSProvider.OPENAI },\n      { id: 'nova', name: 'Nova', language: 'en-US', provider: TTSProvider.OPENAI },\n      { id: 'shimmer', name: 'Shimmer', language: 'en-US', provider: TTSProvider.OPENAI }\n    ];\n  }\n\n  private async fetchElevenLabsVoices(): Promise<TTSVoice[]> {\n    // ElevenLabs voices\n    const settings = this.getSettings();\n    if (!settings.apiKeys.elevenlabs) {\n      return [];\n    }\n\n    // Return default ElevenLabs voices\n    return [\n      { id: 'rachel', name: 'Rachel', language: 'en-US', gender: 'female', provider: TTSProvider.ELEVENLABS },\n      { id: 'domi', name: 'Domi', language: 'en-US', gender: 'female', provider: TTSProvider.ELEVENLABS },\n      { id: 'bella', name: 'Bella', language: 'en-US', gender: 'female', provider: TTSProvider.ELEVENLABS },\n      { id: 'antoni', name: 'Antoni', language: 'en-US', gender: 'male', provider: TTSProvider.ELEVENLABS },\n      { id: 'elli', name: 'Elli', language: 'en-US', gender: 'female', provider: TTSProvider.ELEVENLABS },\n      { id: 'josh', name: 'Josh', language: 'en-US', gender: 'male', provider: TTSProvider.ELEVENLABS }\n    ];\n  }\n\n  public getSettings(): TTSSettings {\n    return { ...this._settings };\n  }\n\n  public async updateSettings(settings: Partial<TTSSettings>): Promise<void> {\n    this._settings = { ...this._settings, ...settings };\n    await this.saveSettings();\n  }\n\n  private async saveSettings(): Promise<void> {\n    await this.context.globalState.update('ttsEnabled', this._settings.enabled);\n    await this.context.globalState.update('ttsProvider', this._settings.provider);\n    await this.context.globalState.update('ttsVoice', this._settings.voice);\n    await this.context.globalState.update('ttsRate', this._settings.rate);\n    await this.context.globalState.update('ttsPitch', this._settings.pitch);\n    await this.context.globalState.update('ttsVolume', this._settings.volume);\n    await this.context.globalState.update('ttsAutoSpeak', this._settings.autoSpeak);\n    await this.context.globalState.update('ttsDownloadPath', this._settings.downloadPath);\n\n    // Save API keys\n    await this.context.globalState.update('ttsGoogleApiKey', this._settings.apiKeys.google);\n    await this.context.globalState.update('ttsOpenAIApiKey', this._settings.apiKeys.openai);\n    await this.context.globalState.update('ttsElevenLabsApiKey', this._settings.apiKeys.elevenlabs);\n    await this.context.globalState.update('ttsAzureApiKey', this._settings.apiKeys.azure);\n\n    // Save advanced settings\n    await this.context.globalState.update('ttsSSMLEnabled', this._settings.advanced.ssmlEnabled);\n    await this.context.globalState.update('ttsEmotionControl', this._settings.advanced.emotionControl);\n    await this.context.globalState.update('ttsSpeedBoost', this._settings.advanced.speedBoost);\n    await this.context.globalState.update('ttsQualityMode', this._settings.advanced.qualityMode);\n    await this.context.globalState.update('ttsCacheEnabled', this._settings.advanced.cacheEnabled);\n    await this.context.globalState.update('ttsStreamingEnabled', this._settings.advanced.streamingEnabled);\n  }\n\n  public getVoices(provider: TTSProvider): TTSVoice[] {\n    return this._voices.get(provider) || [];\n  }\n\n  public updateVoices(provider: TTSProvider, voices: TTSVoice[]): void {\n    this._voices.set(provider, voices);\n  }\n\n  public async downloadVoice(_voice: TTSVoice): Promise<void> {\n    // Implement voice downloading logic\n    Logger.instance.info('Voice download functionality not yet implemented');\n  }\n\n  public dispose(): void {\n    this._disposables.forEach(d => d.dispose());\n  }\n}"]}