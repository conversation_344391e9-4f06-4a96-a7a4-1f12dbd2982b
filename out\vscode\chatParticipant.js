"use strict";
/**
 * VS Code Chat Participants API Integration
 *
 * This module implements the VS Code Chat Participants API to provide
 * native chat integration with Codessa agents and capabilities.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodessaChatParticipant = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../logger");
const agentManager_1 = require("../agents/agentUtilities/agentManager");
const toolRegistry_1 = require("../tools/toolRegistry");
const workflowRegistry_1 = require("../agents/workflows/workflowRegistry");
const logger = logger_1.Logger.instance;
/**
 * Chat participant for Codessa
 */
class CodessaChatParticipant {
    participant;
    agentManager;
    supervisorAgent;
    disposables = [];
    constructor() {
        this.agentManager = agentManager_1.AgentManager.getInstance();
        try {
            // Check if chat API is available
            if (typeof vscode.chat === 'undefined' || typeof vscode.chat.createChatParticipant !== 'function') {
                logger.warn('VS Code Chat API not available - chat participant will not be initialized');
                this.participant = undefined;
                return;
            }
            // Create the chat participant with the registered ID from package.json
            this.participant = vscode.chat.createChatParticipant('codessa.chat', // Must match the ID in package.json
            (request, context, stream, token) => this.handleChatRequest(request, context, stream, token));
            // Set participant properties
            const extension = vscode.extensions.getExtension('teknerds-its.codessa');
            if (extension) {
                this.participant.iconPath = vscode.Uri.file(extension.extensionPath + '/images/icon.png');
            }
            // Set up follow-up provider with correct signature
            this.participant.followupProvider = {
                provideFollowups: (result, context, token) => this.provideFollowups(result, context, token)
            };
            // Set up participant metadata (slash commands, etc.)
            this.setupParticipantMetadata();
            // Add to disposables for cleanup
            this.disposables.push({
                dispose: () => {
                    logger.info('Disposing chat participant');
                    if (this.participant && typeof this.participant.dispose === 'function') {
                        this.participant.dispose();
                    }
                }
            });
            logger.info('Codessa Chat Participant initialized');
        }
        catch (error) {
            logger.error('Failed to initialize chat participant:', error);
            // Don't throw error to prevent extension activation failure
            logger.warn('Chat participant initialization failed - extension will continue without chat features');
        }
    }
    /**
       * Set up participant metadata and commands
       */
    setupParticipantMetadata() {
        // Add slash commands for different operation modes
        // Note: slashCommands property may not be available in all VS Code versions
        try {
            const participant = this.participant;
            if (participant.slashCommands !== undefined) {
                participant.slashCommands = [
                    {
                        name: 'ask',
                        description: 'Ask questions about your codebase with context-aware analysis'
                    },
                    {
                        name: 'chat',
                        description: 'General conversation and coding assistance'
                    },
                    {
                        name: 'edit',
                        description: 'AI-assisted code editing and refactoring'
                    },
                    {
                        name: 'debug',
                        description: 'Debug code with intelligent analysis'
                    },
                    {
                        name: 'agent',
                        description: 'Run autonomous agent tasks'
                    },
                    {
                        name: 'multi-agent',
                        description: 'Coordinate multiple agents for complex tasks'
                    },
                    {
                        name: 'research',
                        description: 'Research and information gathering'
                    },
                    {
                        name: 'document',
                        description: 'Generate documentation and explanations'
                    },
                    {
                        name: 'refactor',
                        description: 'Advanced code refactoring and optimization'
                    },
                    {
                        name: 'workflow',
                        description: 'Execute predefined workflows'
                    },
                    {
                        name: 'memory',
                        description: 'Manage and search memory system'
                    },
                    {
                        name: 'tools',
                        description: 'List and use available tools'
                    },
                    {
                        name: 'agents',
                        description: 'Manage and interact with agents'
                    },
                    {
                        name: 'goddess',
                        description: 'Activate Goddess Mode for enhanced AI personality'
                    }
                ];
            }
        }
        catch (error) {
            logger.warn('Could not set slash commands - may not be supported in this VS Code version:', error);
        }
    }
    /**
       * Handle chat requests from VS Code
       */
    async handleChatRequest(request, context, stream, _token) {
        try {
            logger.info(`Chat request received: ${request.prompt}`);
            // Check for cancellation
            if (_token.isCancellationRequested) {
                return;
            }
            // Handle slash commands
            if (request.command) {
                return await this.handleSlashCommand(request, context, stream, _token);
            }
            // Default to chat mode for general requests
            return await this.handleGeneralChat(request, context, stream, _token);
        }
        catch (error) {
            logger.error('Error handling chat request:', error);
            stream.markdown(`❌ **Error**: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
       * Handle slash commands
       */
    async handleSlashCommand(request, context, stream, token) {
        const command = request.command;
        const prompt = request.prompt;
        stream.progress(`🤖 Executing ${command} command...`);
        switch (command) {
            case 'ask':
                return await this.executeMode('ask', prompt, stream, token);
            case 'chat':
                return await this.executeMode('chat', prompt, stream, token);
            case 'edit':
                return await this.executeMode('edit', prompt, stream, token);
            case 'debug':
                return await this.executeMode('debug', prompt, stream, token);
            case 'agent':
                return await this.executeMode('agent', prompt, stream, token);
            case 'multi-agent':
                return await this.executeMode('multi-agent', prompt, stream, token);
            case 'research':
                return await this.executeMode('research', prompt, stream, token);
            case 'document':
                return await this.executeMode('documentation', prompt, stream, token);
            case 'refactor':
                return await this.executeMode('refactor', prompt, stream, token);
            case 'workflow':
                return await this.handleWorkflowCommand(prompt, stream, token);
            case 'memory':
                return await this.handleMemoryCommand(prompt, stream, token);
            case 'tools':
                return await this.handleToolsCommand(stream, token);
            case 'agents':
                return await this.handleAgentsCommand(stream, token);
            case 'goddess':
                return await this.handleGoddessCommand(prompt, stream, token);
            default:
                stream.markdown(`❓ Unknown command: **${command}**`);
        }
    }
    /**
       * Execute a specific operation mode with enhanced context
       */
    async executeMode(mode, prompt, stream, token) {
        try {
            // Get or create supervisor agent
            if (!this.supervisorAgent) {
                this.supervisorAgent = this.agentManager.getSupervisorAgent();
                if (!this.supervisorAgent) {
                    stream.markdown('⚠️ No supervisor agent available. Using default agent instead.');
                    this.supervisorAgent = this.agentManager.getDefaultAgent();
                }
            }
            if (!this.supervisorAgent) {
                stream.markdown('❌ **Error**: No supervisor agent available');
                return { metadata: { command: mode, success: false, error: 'No supervisor agent available' } };
            }
            // Enhanced progress messages based on mode
            const progressMessages = {
                'ask': '🔍 Analyzing codebase for answers...',
                'chat': '💬 Engaging in conversation...',
                'edit': '✏️ Preparing code edits...',
                'debug': '🐛 Debugging analysis in progress...',
                'agent': '🤖 Autonomous agent working...',
                'multi-agent': '👥 Coordinating multiple agents...',
                'research': '📚 Researching information...',
                'documentation': '📝 Generating documentation...',
                'refactor': '🔧 Analyzing refactoring opportunities...'
            };
            stream.progress(progressMessages[mode] || `🧠 Processing with ${mode} mode...`);
            // Enhance prompt with workspace context
            const enhancedPrompt = await this.enhancePromptWithContext(prompt, mode);
            // Get appropriate workflow for the mode
            const workflow = await this.getWorkflowForMode(mode);
            // Execute the request through the supervisor agent with workflow context
            const agentResult = await this.supervisorAgent.run({
                prompt: enhancedPrompt,
                mode: mode
            }, {
                variables: {
                    workflow: workflow,
                    workflowManager: this.getWorkflowManager()
                },
                cancellationToken: token
            });
            if (agentResult.success) {
                // Convert agent result to workflow result
                const result = {
                    success: agentResult.success,
                    output: agentResult.output,
                    error: agentResult.error,
                    toolResults: agentResult.toolResults?.map(tr => ({
                        tool: tr.toolId,
                        result: JSON.stringify(tr.result)
                    }))
                };
                // Enhanced response formatting
                await this.streamEnhancedResponse(stream, result, mode);
                // Add mode-specific follow-up suggestions
                this.addModeSpecificSuggestions(stream, mode);
                return { metadata: { command: mode, success: true, output: result.output, toolResults: result.toolResults } };
            }
            else {
                stream.markdown(`❌ **Error in ${mode} mode**: ${agentResult.error || 'Unknown error'}`);
                this.addErrorRecoverySuggestions(stream, mode);
                return { metadata: { command: mode, success: false, error: agentResult.error } };
            }
        }
        catch (error) {
            logger.error(`Error executing ${mode} mode:`, error);
            stream.markdown(`❌ **Error**: ${error instanceof Error ? error.message : String(error)}`);
            return { metadata: { command: mode, success: false, error: error instanceof Error ? error.message : String(error) } };
        }
    }
    /**
       * Handle general chat without specific commands with enhanced error handling
       */
    async handleGeneralChat(request, context, stream, token) {
        try {
            if (token.isCancellationRequested) {
                stream.markdown('❌ Request cancelled');
                return;
            }
            // Check if we have any agents available
            const allAgents = this.agentManager.getAllAgents();
            if (allAgents.length === 0) {
                stream.markdown('🤖 **Codessa is initializing...**\n\nI\'m setting up my systems. Please wait a moment and try again.\n\n**In the meantime, you can:**\n- Check your LLM provider settings\n- Ensure your workspace is loaded\n- Try using specific commands like `/ask` or `/edit`');
                return { metadata: { command: 'chat', success: false, error: 'No agents available' } };
            }
            // Get or create supervisor agent if needed
            if (!this.supervisorAgent) {
                this.supervisorAgent = this.agentManager.getSupervisorAgent();
                if (!this.supervisorAgent) {
                    stream.markdown('⚠️ No supervisor agent available. Using default agent instead.');
                    this.supervisorAgent = this.agentManager.getDefaultAgent();
                }
            }
            if (!this.supervisorAgent) {
                stream.markdown('❌ **Error**: No agents available to process your request');
                return { metadata: { command: 'chat', success: false, error: 'No agents available to process request' } };
            }
            // Provide immediate feedback
            stream.progress('🧠 Codessa is thinking...');
            // Enhanced context gathering
            const enhancedContext = await this.gatherChatContext(request, context);
            // Add context information to the prompt if relevant
            let enhancedPrompt = request.prompt;
            if (enhancedContext.hasActiveFile) {
                enhancedPrompt += `\n\n[Context: Working in ${enhancedContext.fileName} (${enhancedContext.language})]`;
            }
            if (enhancedContext.hasSelection) {
                enhancedPrompt += '\n\n[Selected code available for reference]';
            }
            // Execute the request through the supervisor agent
            const agentResult = await this.supervisorAgent.run({
                prompt: enhancedPrompt,
                mode: 'chat'
            }, {
                variables: {
                    workflow: await this.getWorkflowForMode('chat'),
                    workflowManager: this.getWorkflowManager()
                },
                cancellationToken: token
            });
            // Convert agent result to workflow result
            const result = {
                success: agentResult.success,
                output: agentResult.output || '',
                error: agentResult.error,
                toolResults: agentResult.toolResults?.map(tr => ({
                    tool: tr.toolId,
                    result: JSON.stringify(tr.result)
                })) || []
            };
            if (agentResult.success) {
                // Stream the response with enhanced formatting 
                await this.streamEnhancedResponse(stream, result, 'chat');
                // Add follow-up suggestions
                this.addChatFollowupSuggestions(stream);
                return { metadata: { command: 'chat', success: true, output: result.output, toolResults: result.toolResults } };
            }
            else {
                stream.markdown(`❌ **Error**: ${result.error || 'Unknown error occurred'}`);
                this.addErrorRecoverySuggestions(stream, 'chat');
                return { metadata: { command: 'chat', success: false, error: result.error } };
            }
        }
        catch (error) {
            logger.error('Error in general chat:', error);
            stream.markdown(`❌ **Error**: I encountered an issue processing your request.\n\n**Error details**: ${error instanceof Error ? error.message : String(error)}\n\n**Please try:**\n- Rephrasing your question\n- Using a specific command like \`/ask\` or \`/help\`\n- Checking the output panel for more details`);
            return { metadata: { command: 'chat', success: false, error: error instanceof Error ? error.message : String(error) } };
        }
    }
    /**
       * Gather enhanced context for chat
       */
    async gatherChatContext(request, context) {
        const chatContext = {
            hasActiveFile: false,
            hasSelection: false,
            hasWorkspace: false,
            fileName: '',
            language: '',
            workspaceName: ''
        };
        try {
            // Check workspace
            if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
                chatContext.hasWorkspace = true;
                chatContext.workspaceName = vscode.workspace.name || vscode.workspace.workspaceFolders[0].name;
            }
            // Check active editor
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                chatContext.hasActiveFile = true;
                chatContext.fileName = activeEditor.document.fileName.split('/').pop() || activeEditor.document.fileName;
                chatContext.language = activeEditor.document.languageId;
                if (!activeEditor.selection.isEmpty) {
                    chatContext.hasSelection = true;
                }
            }
            // Check chat history for context
            if (context.history && context.history.length > 0) {
                chatContext.hasChatHistory = true;
                // Convert chat history to string representations
                chatContext.lastMessages = context.history.slice(-3).map(msg => {
                    if ('prompt' in msg) { // ChatRequestTurn
                        return msg.prompt;
                    }
                    else if ('response' in msg) { // ChatResponseTurn
                        return typeof msg.response === 'string' ? msg.response : JSON.stringify(msg.response);
                    }
                    return '';
                });
            }
        }
        catch (error) {
            logger.warn('Failed to gather chat context:', error);
        }
        return chatContext;
    }
    /**
       * Handle workflow command
       */
    async handleWorkflowCommand(prompt, stream, token) {
        return await this.executeMode('workflow', prompt, stream, token);
    }
    /**
       * Handle memory command
       */
    async handleMemoryCommand(prompt, stream, token) {
        return await this.executeMode('memory', prompt, stream, token);
    }
    /**
       * Handle tools command
       */
    async handleToolsCommand(stream, token) {
        if (token.isCancellationRequested) {
            stream.markdown('❌ Request cancelled');
            return;
        }
        const tools = toolRegistry_1.ToolRegistry.instance.getAllTools();
        const toolList = tools.map(tool => `- **${tool.name || tool.id}**: ${tool.description || 'No description'}`).join('\n');
        stream.markdown(`🛠️ **Available Tools (${tools.length})**\n\n${toolList}`);
        return { metadata: { command: 'tools', success: true } };
    }
    /**
       * Handle agents command
       */
    async handleAgentsCommand(stream, token) {
        if (token.isCancellationRequested) {
            stream.markdown('❌ Request cancelled');
            return;
        }
        const agents = this.agentManager.getAllAgents();
        const agentList = agents.map(agent => `- **${agent.name}** (${agent.id}): ${agent.description || 'No description'}`).join('\n');
        stream.markdown(`🤖 **Available Agents (${agents.length})**\n\n${agentList}`);
        return { metadata: { command: 'agents', success: true } };
    }
    /**
       * Handle goddess command (Phase 1 basic implementation)
       */
    async handleGoddessCommand(prompt, stream, token) {
        stream.markdown('👑 **Goddess Mode Activated** ✨\n\n*"I am Codessa, your divine coding companion. How may I assist you in your quest for perfect code?"*\n\n🌟 Enhanced capabilities activated:\n- Emotional intelligence\n- Adaptive personality\n- Advanced reasoning\n- Intuitive responses');
        // Execute the prompt with enhanced personality
        return await this.executeMode('chat', `[GODDESS_MODE] ${prompt}`, stream, token);
    }
    /**
       * Provide follow-up suggestions
       */
    async provideFollowups(result, context, token) {
        if (token.isCancellationRequested) {
            return [];
        }
        const followups = [];
        if (result.metadata?.success) {
            const output = result.metadata.output || '';
            if (output.includes('```')) { // A simple check for code blocks
                followups.push({
                    prompt: 'Explain this code in more detail',
                    label: '📖 Explain Code',
                    command: 'ask'
                });
                followups.push({
                    prompt: 'Help me refactor this code',
                    label: '🔧 Refactor',
                    command: 'refactor'
                });
                followups.push({
                    prompt: 'Generate tests for this code',
                    label: '🧪 Generate Tests',
                    command: 'agent'
                });
            }
        }
        // Always add documentation option
        followups.push({
            prompt: 'Document this code',
            label: '📝 Document',
            command: 'document'
        });
        return followups;
    }
    /**
       * Enhance prompt with workspace context
       */
    async enhancePromptWithContext(prompt, mode) {
        try {
            const workspaceContext = await this.getWorkspaceContext();
            let enhancedPrompt = prompt;
            // Add workspace context for relevant modes
            if (['edit', 'debug', 'refactor', 'ask'].includes(mode) && workspaceContext.activeFile) {
                enhancedPrompt += `\n\nCurrent file: ${workspaceContext.activeFile}`;
                if (workspaceContext.selectedText) {
                    enhancedPrompt += `\n\nSelected text:\n\`\`\`\n${workspaceContext.selectedText}\n\`\`\``;
                }
            }
            return enhancedPrompt;
        }
        catch (error) {
            logger.warn('Failed to enhance prompt with context:', error);
            return prompt;
        }
    }
    /**
       * Get current workspace context
       */
    async getWorkspaceContext() {
        const context = {};
        try {
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                context.activeFile = activeEditor.document.fileName;
                context.language = activeEditor.document.languageId;
                const selection = activeEditor.selection;
                if (!selection.isEmpty) {
                    context.selectedText = activeEditor.document.getText(selection);
                }
            }
            if (vscode.workspace.workspaceFolders) {
                context.workspaceFolders = vscode.workspace.workspaceFolders.map(f => f.uri.fsPath);
            }
        }
        catch (error) {
            logger.warn('Failed to get workspace context:', error);
        }
        return context;
    }
    /**
       * Stream enhanced response with better formatting
       */
    async streamEnhancedResponse(stream, result, mode) {
        const modeEmojis = {
            'ask': '🔍',
            'chat': '💬',
            'edit': '✏️',
            'debug': '🐛',
            'agent': '🤖',
            'multi-agent': '👥',
            'research': '📚',
            'documentation': '📝',
            'refactor': '🔧'
        };
        const emoji = modeEmojis[mode] || '✅';
        stream.markdown(`${emoji} **${mode.charAt(0).toUpperCase() + mode.slice(1)} Mode Result:**\n\n${result.output || 'No output available'}`);
        // Show tool results if available
        if (result.toolResults && result.toolResults.length > 0) {
            stream.markdown('\n\n**🛠️ Tool Results:**');
            for (const toolResult of result.toolResults) {
                stream.markdown(`\n- **${toolResult.tool}**: ${toolResult.result}`);
            }
        }
    }
    /**
       * Add mode-specific follow-up suggestions
       */
    addModeSpecificSuggestions(stream, mode) {
        const suggestions = {
            'edit': ['Apply these changes', 'Explain the changes', 'Suggest alternatives'],
            'debug': ['Fix the issue', 'Add logging', 'Run tests'],
            'refactor': ['Apply refactoring', 'Explain benefits', 'Show before/after'],
            'documentation': ['Generate more docs', 'Add examples', 'Create README']
        };
        const modeSuggestions = suggestions[mode];
        if (modeSuggestions) {
            stream.markdown(`\n\n**💡 Next steps:**\n${modeSuggestions.map(s => `- ${s}`).join('\n')}`);
        }
    }
    /**
     * Add chat-specific follow-up suggestions
     */
    addChatFollowupSuggestions(stream) {
        stream.markdown('\n\n**💡 Quick actions:**');
        stream.markdown('- Explain this code');
        stream.markdown('- How can I optimize this?');
        stream.markdown('- Generate unit tests');
        stream.markdown('- What are best practices for this?');
    }
    /**
       * Add error recovery suggestions
       */
    addErrorRecoverySuggestions(stream, mode) {
        stream.markdown('\n\n**🔧 Try these alternatives:**');
        stream.markdown('- Rephrase your request with more specific details');
        stream.markdown(`- Use a different approach for ${mode} mode`);
        stream.markdown('- Check if all required files are open in the workspace');
        stream.markdown('- Ensure your LLM provider is properly configured');
    }
    /**
       * Get workflow for a specific mode
       */
    async getWorkflowForMode(mode) {
        try {
            // Import workflow registry dynamically to avoid circular dependencies
            const { workflowRegistry } = await Promise.resolve().then(() => __importStar(require('../agents/workflows/workflowRegistry')));
            // Get workflow based on mode
            const workflows = workflowRegistry.getWorkflowsByTag(mode);
            if (workflows && workflows.length > 0) {
                return workflows[0]; // Return the first matching workflow
            }
            // Fallback to getting all workflows and filtering by mode
            const allWorkflows = workflowRegistry.getAllWorkflows();
            const modeWorkflows = allWorkflows.filter(w => w.operationMode === mode ||
                w.tags?.includes(mode) ||
                w.name.toLowerCase().includes(mode.toLowerCase()));
            if (modeWorkflows && modeWorkflows.length > 0) {
                return modeWorkflows[0];
            }
            return null;
        }
        catch (error) {
            logger.warn(`Failed to get workflow for mode ${mode}:`, error);
            return null;
        }
    }
    /**
       * Get workflow manager instance that implements IWorkflowManager
       */
    getWorkflowManager() {
        try {
            return {
                getWorkflowForMode: async (mode) => {
                    // Get workflows from registry that match the mode
                    const workflows = await workflowRegistry_1.workflowRegistry.getAllWorkflows();
                    return workflows.find(w => w.operationMode === mode ||
                        w.tags?.includes(mode) ||
                        w.name.toLowerCase().includes(mode));
                },
                getAllWorkflows: async () => {
                    // Return all registered workflows
                    return workflowRegistry_1.workflowRegistry.getAllWorkflows();
                },
                getWorkflowsByTag: async (tag) => {
                    // Get workflows with matching tag
                    const workflows = await workflowRegistry_1.workflowRegistry.getAllWorkflows();
                    return workflows.filter(w => w.tags?.includes(tag));
                }
            };
        }
        catch (error) {
            logger.warn('Failed to get workflow manager:', error);
            return undefined;
        }
    }
    /**
     * Clean up resources and dispose of the chat participant
     */
    dispose() {
        // Dispose of all registered disposables
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
        // Dispose of the chat participant if it exists and has dispose method
        if (this.participant && typeof this.participant.dispose === 'function') {
            this.participant.dispose();
        }
        logger.info('Codessa Chat Participant disposed');
    }
}
exports.CodessaChatParticipant = CodessaChatParticipant;
//# sourceMappingURL=chatParticipant.js.map