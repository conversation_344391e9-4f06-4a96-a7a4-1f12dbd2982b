import { Agent, AgentContext, AgentRunInput, AgentRunResult } from '../agentUtilities/agent';
import { ITool } from '../../tools/types';
import { logger } from '../../logger';

export class DocumentAgent extends Agent {
  constructor(_config: any) {
    super({
      ..._config,
      role: 'assistant',
      capabilities: ['document'],
      llmProvider: 'openai',
      llmModel: 'gpt-4'
    });
  }

  override async run(input: AgentRunInput, context: AgentContext = {}): Promise<AgentRunResult> {
    logger.info(`DocumentAgent processing: "${input.prompt.substring(0, 50)}..."`);

    // --- Gather relevant workflow for document mode ---
    let workflow: any = undefined;
    if (context.variables?.workflowManager) {
      workflow = await context.variables.workflowManager.getWorkflowForMode('document');
    }

    // --- Gather all relevant tools ---
    let tools = this.tools;
    if (context.tools) {
      tools = context.tools as Map<string, ITool>;
    }

    // --- Gather memory and knowledgebase context ---
    let memoryContext = '';
    if (this.getMemory) {
      const memories = await this.getMemory().getRelevantMemories(input.prompt);
      if (memories?.length) {
        memoryContext = this.getMemory().formatMemoriesForPrompt(memories);
      }
    }
    let kbContext = '';
    if (context.variables?.knowledgebaseManager) {
      kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input.prompt);
    }

    // --- Gather MCP context ---
    let mcpContext = '';
    if (context.variables?.mcpManager) {
      mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext() || {});
    }

    // --- Use external prompt if available ---
    let externalPrompt = '';
    if (context.variables?.promptManager) {
      externalPrompt = context.variables.promptManager.getSystemPrompt('documentAgent', context.variables) || '';
    }

    // --- Compose final prompt ---
    const documentPrompt = `
# Document Mode Processing

${externalPrompt}

## User Request:
${input.prompt}

## Memory Context:
${memoryContext}

## Knowledgebase Context:
${kbContext}

## MCP Context:
${mcpContext}

## Workflow:
${workflow ? JSON.stringify(workflow) : 'None'}

## Code to Document:
${context.workspace?.currentFile ? (typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile) : 'No code context available'}

## Your Task:
1. Create appropriate documentation for the code
2. Follow best practices for the language/framework
3. Include examples where helpful
Use all available workflows, tools, memory, knowledgebase, and context. Stream output if possible.
`;
    input.prompt = documentPrompt;
    // Pass all gathered context and tools to super.run
    return super.run(input, { ...context, tools });
  }
}