{"version": 3, "file": "codeLlamaProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/codeLlamaProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAGpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;;;;;;;GAQG;AACH,MAAa,iBAAkB,SAAQ,iCAAe;IAC3C,UAAU,GAAG,WAAW,CAAC;IACzB,WAAW,GAAG,YAAY,CAAC;IAC3B,WAAW,GAAG,oCAAoC,CAAC;IACnD,OAAO,GAAG,kEAAkE,CAAC;IAC7E,cAAc,GAAG,KAAK,CAAC,CAAC,wDAAwD;IAChF,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,4BAA4B,CAAC,CAAC,oBAAoB;IACpE,YAAY,GAAG,cAAc,CAAC;IAE/B,MAAM,GAAQ,IAAI,CAAC;IACnB,YAAY,GAAwC,QAAQ,CAAC;IAErE,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBACzE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,0BAA0B;QAC1B,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtE,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC/B,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,MAAM,OAAO,GAA2B;gBACtC,cAAc,EAAE,kBAAkB;aACnC,CAAC;YAEF,iDAAiD;YACjD,IAAI,MAAM,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,EAAE,CAAC;gBAClD,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,MAAM,EAAE,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,MAAM,EAAE,oBAAoB;gBACrC,OAAO;aACR,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,kEAAkE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QACrG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,6EAA6E;QAC7E,4CAA4C;QAC5C,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,EAAE,CAAC;YACxC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAC/C,CAAC;QACD,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,MAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,oCAAoC,EAAE,CAAC;QACtE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,qBAAqB;YACrB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,gCAAgC;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,sBAAsB,MAAM,CAAC,YAAY,gBAAgB,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,YAAY,CAAC;YACzB,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,6CAA6C;gBAC7C,IAAI,eAAe,GAAG,EAAE,CAAC;gBAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAElC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;4BACV,MAAM,IAAI,cAAc,eAAe,aAAa,CAAC;wBACvD,CAAC;wBACD,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC;oBACpC,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,MAAM,CAAC;oBACrC,CAAC;gBACH,CAAC;gBAED,6BAA6B;gBAC7B,MAAM,IAAI,GAAG,eAAe,cAAc,CAAC;YAC7C,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,cAAc,CAAC;YAC3C,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;wBACpD,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBACvG,CAAC;YACH,CAAC;YAED,8CAA8C;YAC9C,IAAI,QAAQ,CAAC;YAEb,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACnC,oBAAoB;gBACpB,MAAM,WAAW,GAAG;oBAClB,KAAK,EAAE,OAAO;oBACd,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,KAAK;oBACb,OAAO,EAAE;wBACP,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;wBACtC,WAAW,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;wBACrC,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC;qBACzC;iBACF,CAAC;gBAEF,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE;oBAC1D,MAAM,EAAE,eAAe,EAAE,MAAM;iBAChC,CAAC,CAAC;gBAEH,wBAAwB;gBACxB,OAAO;oBACL,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE;oBACrC,YAAY,EAAE,MAAM;oBACpB,KAAK,EAAE;wBACL,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB;wBAC7C,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,UAAU;qBAC3C;iBACF,CAAC;YACJ,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,EAAE,CAAC;gBAC/C,oCAAoC;gBACpC,MAAM,WAAW,GAAG;oBAClB,MAAM,EAAE,MAAM;oBACd,UAAU,EAAE;wBACV,cAAc,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;wBACxC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;wBACtC,KAAK,EAAE,IAAI;wBACX,SAAS,EAAE,IAAI;wBACf,gBAAgB,EAAE,KAAK;wBACvB,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC;qBACzC;iBACF,CAAC;gBAEF,kDAAkD;gBAClD,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE;oBACjD,MAAM,EAAE,eAAe,EAAE,MAAM;iBAChC,CAAC,CAAC;gBAEH,oBAAoB;gBACpB,IAAI,OAAO,GAAG,EAAE,CAAC;gBACjB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7D,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;wBACpC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;oBAC5C,CAAC;gBACH,CAAC;gBAED,OAAO;oBACL,OAAO;oBACP,YAAY,EAAE,MAAM;oBACpB,KAAK,EAAE;wBACL,YAAY,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;wBAClD,gBAAgB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;qBACxD;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,+CAA+C;gBAC/C,MAAM,QAAQ,GAA8E,EAAE,CAAC;gBAE/F,gCAAgC;gBAChC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACxB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,MAAM,CAAC,YAAY;qBACpB,CAAC,CAAC;gBACd,CAAC;gBAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;qBACd,CAAC,CAAC;gBACd,CAAC;gBAED,MAAM,WAAW,GAAG;oBAClB,KAAK,EAAE,OAAO;oBACd,QAAQ,EAAE,QAAQ;oBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,UAAU,EAAE,MAAM,CAAC,SAAS;oBAC5B,IAAI,EAAE,MAAM,CAAC,aAAa;iBAC3B,CAAC;gBAEF,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE;oBAClE,MAAM,EAAE,eAAe,EAAE,MAAM;iBAChC,CAAC,CAAC;gBAEH,mCAAmC;gBACnC,OAAO;oBACL,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;oBACvD,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,IAAI,MAAM;oBAC9D,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;iBAC3B,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE5E,uDAAuD;YACvD,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAC/B,YAAY,CAAC,QAAQ,CAAC,sBAAsB,CAAC;gBAC7C,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3C,eAAM,CAAC,KAAK,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,gBAAgB,GAAG,gCAAgC,CAAC;YAExD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,gBAAgB,GAAG,yBAAyB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACvH,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,gBAAgB,GAAG,2BAA2B,CAAC;YACjD,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC;YACnC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,gBAAgB;gBACvB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACnC,gDAAgD;gBAChD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAEpD,+BAA+B;gBAC/B,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM;qBAChC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CACjB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;oBAC9B,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;oBAC3C,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CACxD;qBACA,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;oBAChB,EAAE,EAAE,CAAC,CAAC,IAAI;oBACV,IAAI,EAAE,CAAC,CAAC,IAAI;oBACZ,WAAW,EAAE,SAAS,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;oBACpD,aAAa,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC;oBACpD,WAAW,EAAE,cAAc;iBAC5B,CAAC,CAAC,CAAC;gBAEN,OAAO,MAAM,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,gDAAgD;gBAChD,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,eAAM,CAAC,KAAK,CAAC,sCAAsC,YAAY,EAAE,CAAC,CAAC;YAEnE,uDAAuD;YACvD,qEAAqE;YACrE,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ;gBACxB,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;oBACrC,YAAY,CAAC,QAAQ,CAAC,sBAAsB,CAAC;oBAC7C,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBAC7C,eAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;YAC9E,CAAC;iBAAM,CAAC;gBACN,sEAAsE;gBACtE,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;YAED,uCAAuC;YACvC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;SAEK;IACG,mBAAmB;QACzB,OAAO;YACL;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,uCAAuC;gBACpD,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,0CAA0C;gBACvD,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,yCAAyC;gBACtD,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,8CAA8C;gBAC3D,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,wBAAwB;gBAC5B,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,+CAA+C;gBAC5D,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,wBAAwB;gBAC5B,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,+CAA+C;gBAC5D,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,qBAAqB;gBACzB,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,+CAA+C;gBAC5D,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,sBAAsB;gBAC1B,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,gDAAgD;gBAC7D,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,sBAAsB;gBAC1B,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,gDAAgD;gBAC7D,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,qBAAqB;aACnC;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,QAAgB;QAC/C,wDAAwD;QACxD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACG,UAAU,CAAC,KAAa;QAC9B,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;YACjB,OAAO,GAAG,KAAK,IAAI,CAAC;QACtB,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YAC/B,OAAO,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3C,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YACtC,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,oCAAoC;aAC9C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACnC,wCAAwC;gBACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACpD,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;gBAE9E,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,UAAU,OAAO,gEAAgE,OAAO,IAAI;qBACtG,CAAC;gBACJ,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,wDAAwD,OAAO,IAAI;iBAC7E,CAAC;YACJ,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,EAAE,CAAC;gBAC/C,+CAA+C;gBAC/C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;oBAC1C,MAAM,EAAE,oBAAoB;oBAC5B,UAAU,EAAE;wBACV,cAAc,EAAE,EAAE;wBAClB,gBAAgB,EAAE,KAAK;qBACxB;iBACF,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAClB,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,8DAA8D;qBACxE,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,qDAAqD;gBACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBAC3D,KAAK,EAAE,OAAO;oBACd,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;oBAC9C,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;oBAC3C,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,mDAAmD,OAAO,IAAI;qBACxE,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uDAAuD;aACjE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE5E,4EAA4E;YAC5E,IAAI,OAAO,KAAK,cAAc,EAAE,CAAC;gBAC/B,eAAM,CAAC,KAAK,CAAC,+CAA+C,YAAY,EAAE,CAAC,CAAC;YAC9E,CAAC;iBAAM,CAAC;gBACN,kDAAkD;gBAClD,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,gBAAgB,GAAG,qCAAqC,CAAC;YAE7D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,gBAAgB,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAC5G,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC;YACnC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gBAAgB;aAC1B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,mEAAmE;gBAChF,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,6BAA6B;gBAC1C,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,QAAQ,CAAC;aAC7C;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,oEAAoE;gBACjF,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,qCAAqC;gBAClD,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AAliBD,8CAkiBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { BaseLLMProvider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for Code Llama models\n *\n * Code Llama is a family of code-specialized LLMs from Meta AI\n * This provider supports using Code Llama via:\n * 1. Ollama (local)\n * 2. Hugging Face Inference API\n * 3. Custom API endpoints (self-hosted)\n */\nexport class CodeLlamaProvider extends BaseLLMProvider {\n  readonly providerId = 'codellama';\n  readonly displayName = 'Code Llama';\n  readonly description = 'Code-specialized LLMs from Meta AI';\n  readonly website = 'https://ai.meta.com/blog/code-llama-large-language-model-coding/';\n  readonly requiresApiKey = false; // Not required for local Ollama, but can be used for HF\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'http://localhost:11434/api'; // Default to Ollama\n  readonly defaultModel = 'codellama:7b';\n\n  private client: any = null;\n  private endpointType: 'ollama' | 'huggingface' | 'custom' = 'ollama';\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        logger.info('Code Llama configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    // Determine endpoint type\n    if (baseUrl.includes('localhost:11434') || baseUrl.includes('ollama')) {\n      this.endpointType = 'ollama';\n    } else if (baseUrl.includes('huggingface.co')) {\n      this.endpointType = 'huggingface';\n    } else {\n      this.endpointType = 'custom';\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      const headers: Record<string, string> = {\n        'Content-Type': 'application/json'\n      };\n\n      // Add API key if provided and using Hugging Face\n      if (apiKey && this.endpointType === 'huggingface') {\n        headers['Authorization'] = `Bearer ${apiKey}`;\n      }\n\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 120000, // 2 minutes timeout\n        headers\n      });\n\n      logger.info(`Code Llama client initialized successfully with endpoint type: ${this.endpointType}`);\n    } catch (error) {\n      logger.error('Failed to initialize Code Llama client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    // For Ollama and custom endpoints, we just need the client to be initialized\n    // For Hugging Face, we also need an API key\n    if (this.endpointType === 'huggingface') {\n      return !!this.client && !!this.config.apiKey;\n    }\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using Code Llama models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    _tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'Code Llama provider not configured' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare the prompt\n      let prompt = '';\n\n      // Add system prompt if provided\n      if (params.systemPrompt) {\n        prompt += `<s>[INST] <<SYS>>\\n${params.systemPrompt}\\n<</SYS>>\\n\\n`;\n      } else {\n        prompt += '<s>[INST] ';\n      }\n\n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        // Code Llama uses a specific format for chat\n        let lastUserMessage = '';\n\n        for (let i = 0; i < params.history.length; i++) {\n          const message = params.history[i];\n\n          if (message.role === 'user') {\n            if (i > 0) {\n              prompt += `[/INST]\\n\\n${lastUserMessage}\\n\\n[INST] `;\n            }\n            lastUserMessage = message.content;\n          } else if (message.role === 'assistant') {\n            prompt += `${message.content}\\n\\n`;\n          }\n        }\n\n        // Add the final user message\n        prompt += `${lastUserMessage} [/INST]\\n\\n`;\n      } else {\n        // Just add the user prompt\n        prompt += `${params.prompt} [/INST]\\n\\n`;\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            logger.info('Code Llama request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          logger.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request based on endpoint type\n      let response;\n\n      if (this.endpointType === 'ollama') {\n        // Ollama API format\n        const requestData = {\n          model: modelId,\n          prompt: prompt,\n          stream: false,\n          options: {\n            temperature: params.temperature ?? 0.7,\n            num_predict: params.maxTokens ?? 2048,\n            stop: params.stopSequences ?? ['[INST]']\n          }\n        };\n\n        response = await this.client.post('/generate', requestData, {\n          signal: abortController?.signal\n        });\n\n        // Parse Ollama response\n        return {\n          content: response.data.response || '',\n          finishReason: 'stop',\n          usage: {\n            promptTokens: response.data.prompt_eval_count,\n            completionTokens: response.data.eval_count,\n          }\n        };\n      } else if (this.endpointType === 'huggingface') {\n        // Hugging Face Inference API format\n        const requestData = {\n          inputs: prompt,\n          parameters: {\n            max_new_tokens: params.maxTokens || 1024,\n            temperature: params.temperature || 0.7,\n            top_p: 0.95,\n            do_sample: true,\n            return_full_text: false,\n            stop: params.stopSequences || ['[INST]']\n          }\n        };\n\n        // For HF, we need to specify the model in the URL\n        response = await this.client.post('', requestData, {\n          signal: abortController?.signal\n        });\n\n        // Parse HF response\n        let content = '';\n        if (Array.isArray(response.data) && response.data.length > 0) {\n          if (response.data[0].generated_text) {\n            content = response.data[0].generated_text;\n          }\n        }\n\n        return {\n          content,\n          finishReason: 'stop',\n          usage: {\n            promptTokens: prompt.length / 4, // Rough estimate\n            completionTokens: content.length / 4, // Rough estimate\n          }\n        };\n      } else {\n        // Custom API - assume OpenAI-compatible format\n        const messages: { role: string; content: string; name?: string; tool_call_id?: string }[] = [];\n\n        // Add system prompt if provided\n        if (params.systemPrompt) {\n          messages.push({\n            role: 'system',\n            content: params.systemPrompt\n          } as const);\n        }\n\n        if (params.history && params.history.length > 0) {\n          messages.push(...params.history);\n        } else {\n          messages.push({\n            role: 'user',\n            content: params.prompt\n          } as const);\n        }\n\n        const requestData = {\n          model: modelId,\n          messages: messages,\n          temperature: params.temperature ?? 0.7,\n          max_tokens: params.maxTokens,\n          stop: params.stopSequences\n        };\n\n        response = await this.client.post('/chat/completions', requestData, {\n          signal: abortController?.signal\n        });\n\n        // Parse OpenAI-compatible response\n        return {\n          content: response.data.choices[0].message.content || '',\n          finishReason: response.data.choices[0].finish_reason || 'stop',\n          usage: response.data.usage\n        };\n      }\n    } catch (error: any) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n\n      // Don't show error notifications for connection issues\n      if (errorMessage.includes('ECONNREFUSED') ||\n                errorMessage.includes('connect ECONNREFUSED') ||\n                errorMessage.includes('timeout')) {\n        logger.debug(`Code Llama connection error: ${errorMessage}`);\n      } else {\n        logger.error('Code Llama generate error:', error);\n      }\n\n      let userErrorMessage = 'Failed to call Code Llama API.';\n\n      if (error.response) {\n        userErrorMessage = `Code Llama API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        userErrorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        userErrorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: userErrorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available Code Llama models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    try {\n      if (this.endpointType === 'ollama') {\n        // For Ollama, we can fetch the available models\n        const response = await this.client.get('/api/tags');\n\n        // Filter for Code Llama models\n        const models = response.data.models\n          .filter((m: any) =>\n            m.name.toLowerCase().includes('codellama') ||\n                        m.name.toLowerCase().includes('code-llama') ||\n                        m.name.toLowerCase().includes('llama-code')\n          )\n          .map((m: any) => ({\n            id: m.name,\n            name: m.name,\n            description: `Size: ${this.formatSize(m.size || 0)}`,\n            contextWindow: this.getContextWindowForModel(m.name),\n            pricingInfo: 'Free (local)'\n          }));\n\n        return models;\n      } else {\n        // For other endpoints, return predefined models\n        return this.getPredefinedModels();\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      logger.debug(`Failed to fetch Code Llama models: ${errorMessage}`);\n\n      // Don't show error notifications for connection issues\n      // This prevents error popups for users who don't have Ollama running\n      if (this.endpointType === 'ollama' &&\n                (errorMessage.includes('ECONNREFUSED') ||\n                 errorMessage.includes('connect ECONNREFUSED') ||\n                 errorMessage.includes('timeout'))) {\n        logger.info('Ollama connection failed, using predefined Code Llama models');\n      } else {\n        // Only log as error for non-connection issues or non-Ollama endpoints\n        logger.error('Failed to fetch Code Llama models:', error);\n      }\n\n      // Return predefined models as fallback\n      return this.getPredefinedModels();\n    }\n  }\n\n  /**\n     * Get predefined Code Llama models\n     */\n  private getPredefinedModels(): LLMModelInfo[] {\n    return [\n      {\n        id: 'codellama:7b',\n        name: 'Code Llama 7B',\n        description: 'Base Code Llama model (7B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'codellama:13b',\n        name: 'Code Llama 13B',\n        description: 'Medium Code Llama model (13B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'codellama:34b',\n        name: 'Code Llama 34B',\n        description: 'Large Code Llama model (34B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'codellama:7b-instruct',\n        name: 'Code Llama 7B Instruct',\n        description: 'Instruction-tuned Code Llama (7B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'codellama:13b-instruct',\n        name: 'Code Llama 13B Instruct',\n        description: 'Instruction-tuned Code Llama (13B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'codellama:34b-instruct',\n        name: 'Code Llama 34B Instruct',\n        description: 'Instruction-tuned Code Llama (34B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'codellama:7b-python',\n        name: 'Code Llama 7B Python',\n        description: 'Python-specialized Code Llama (7B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'codellama:13b-python',\n        name: 'Code Llama 13B Python',\n        description: 'Python-specialized Code Llama (13B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'codellama:34b-python',\n        name: 'Code Llama 34B Python',\n        description: 'Python-specialized Code Llama (34B parameters)',\n        contextWindow: 16384,\n        pricingInfo: 'Free (open weights)'\n      }\n    ];\n  }\n\n  /**\n     * Get the context window size for a specific model\n     */\n  private getContextWindowForModel(_modelId: string): number {\n    // Code Llama models generally have a 16K context window\n    return 16384;\n  }\n\n  /**\n     * Format file size in bytes to a human-readable string\n     */\n  private formatSize(bytes: number): string {\n    if (bytes < 1024) {\n      return `${bytes} B`;\n    } else if (bytes < 1024 * 1024) {\n      return `${(bytes / 1024).toFixed(2)} KB`;\n    } else if (bytes < 1024 * 1024 * 1024) {\n      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;\n    } else {\n      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;\n    }\n  }\n\n  /**\n     * Test connection to Code Llama\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'Code Llama client not initialized.'\n      };\n    }\n\n    try {\n      if (this.endpointType === 'ollama') {\n        // For Ollama, check if the model exists\n        const response = await this.client.get('/api/tags');\n        const modelExists = response.data.models.some((m: any) => m.name === modelId);\n\n        if (!modelExists) {\n          return {\n            success: false,\n            message: `Model '${modelId}' not found. You may need to pull it first with 'ollama pull ${modelId}'.`\n          };\n        }\n\n        return {\n          success: true,\n          message: `Successfully connected to Ollama and verified model '${modelId}'.`\n        };\n      } else if (this.endpointType === 'huggingface') {\n        // For Hugging Face, make a simple test request\n        const response = await this.client.post('', {\n          inputs: 'def hello_world():',\n          parameters: {\n            max_new_tokens: 10,\n            return_full_text: false\n          }\n        });\n\n        if (response.data) {\n          return {\n            success: true,\n            message: 'Successfully connected to Hugging Face API and tested model.'\n          };\n        }\n      } else {\n        // For custom endpoints, assume OpenAI-compatible API\n        const response = await this.client.post('/chat/completions', {\n          model: modelId,\n          messages: [{ role: 'user', content: 'Hello' }],\n          max_tokens: 10\n        });\n\n        if (response.data && response.data.choices) {\n          return {\n            success: true,\n            message: `Successfully connected to API and tested model '${modelId}'.`\n          };\n        }\n      }\n\n      return {\n        success: false,\n        message: 'Connected to API but received an unexpected response.'\n      };\n    } catch (error: any) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n\n      // Don't show error notifications for connection issues during silent checks\n      if (modelId === 'silent-check') {\n        logger.debug(`Code Llama connection test failed silently: ${errorMessage}`);\n      } else {\n        // Only log as error for explicit connection tests\n        logger.error('Code Llama connection test failed:', error);\n      }\n\n      let userErrorMessage = 'Failed to connect to Code Llama API';\n\n      if (error.response) {\n        userErrorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        userErrorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: userErrorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The API endpoint (default: http://localhost:11434/api for Ollama)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'endpointType',\n        name: 'Endpoint Type',\n        description: 'The type of endpoint to use',\n        required: true,\n        type: 'select',\n        options: ['ollama', 'huggingface', 'custom']\n      },\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'API key (required for Hugging Face, optional for custom endpoints)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default Code Llama model to use',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n"]}