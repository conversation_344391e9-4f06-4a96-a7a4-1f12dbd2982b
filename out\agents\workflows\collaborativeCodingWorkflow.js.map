{"version": 3, "file": "collaborativeCodingWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/collaborativeCodingWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAaH,8EA+FC;AAKD,sEA+FC;AA5MD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,iCAAiC,CAC/C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,gBAAuB,EACvB,cAAqB,EACrB,aAAoB,EACpB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,2CAA2C,IAAI,EAAE,CAAC,CAAC;IAE/D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,gBAAgB,CAAC,CAAC;IAChI,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;IACjH,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAC3G,MAAM,UAAU,GAAG,eAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;IAC/E,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IAC5F,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;IACvH,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC9F,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAC3G,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,gCAAgC,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1H,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC5G,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3F,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACtF,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QAC5G,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAE3F,iBAAiB;QACjB,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE;QACvF,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE;KACzG,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,0BAA0B;QAC1B,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,kBAAkB,KAAK,EAAE;gBAC/B,MAAM,EAAE,QAAQ;gBAChB,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,oCAAoC;YACpC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,YAAY;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,yBAAyB;YACzB,oBAAoB;YACpB,kBAAkB;YAClB,UAAU;YACV,cAAc;YACd,sBAAsB;YACtB,eAAe;YACf,kBAAkB;YAClB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,aAAa;QAC5B,IAAI,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,gBAAgB,CAAC;KAClD,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,6BAA6B,CAC3C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,WAAkB,EAClB,cAAqB,EACrB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,uCAAuC,IAAI,EAAE,CAAC,CAAC;IAE3D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACnG,MAAM,YAAY,GAAG,eAAO,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACrF,MAAM,UAAU,GAAG,eAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC5E,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC7F,MAAM,WAAW,GAAG,eAAO,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IAC/E,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;IAC3F,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACtG,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;IACzG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACxF,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9F,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QACrF,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACtF,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QACxF,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7F,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAE3F,iBAAiB;QACjB,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE;QACvF,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE;QACpF,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE;KAClG,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,0BAA0B;QAC1B,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,kBAAkB,KAAK,EAAE;gBAC/B,MAAM,EAAE,QAAQ;gBAChB,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,oCAAoC;YACpC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,YAAY;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,gBAAgB;YAChB,YAAY;YACZ,UAAU;YACV,cAAc;YACd,WAAW;YACX,eAAe;YACf,iBAAiB;YACjB,kBAAkB;YAClB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,aAAa;QAC5B,IAAI,EAAE,CAAC,kBAAkB,EAAE,IAAI,EAAE,eAAe,CAAC;KAClD,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Collaborative Coding Workflow\n *\n * This module provides a workflow for collaborative coding:\n * - Coordinating multiple developers\n * - Sharing context between team members\n * - Managing collaborative coding sessions\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n\n/**\n * Create a Collaborative Coding workflow\n */\nexport function createCollaborativeCodingWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  coordinatorAgent: Agent,\n  developerAgent: Agent,\n  reviewerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Collaborative Coding workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const sessionInitializationNode = Codessa.createAgentNode('session-initialization', 'Session Initialization', coordinatorAgent);\n  const taskDistributionNode = Codessa.createAgentNode('task-distribution', 'Task Distribution', coordinatorAgent);\n  const contextSharingNode = Codessa.createAgentNode('context-sharing', 'Context Sharing', coordinatorAgent);\n  const codingNode = Codessa.createAgentNode('coding', 'Coding', developerAgent);\n  const codeReviewNode = Codessa.createAgentNode('code-review', 'Code Review', reviewerAgent);\n  const conflictResolutionNode = Codessa.createAgentNode('conflict-resolution', 'Conflict Resolution', coordinatorAgent);\n  const integrationNode = Codessa.createAgentNode('integration', 'Integration', developerAgent);\n  const sessionSummaryNode = Codessa.createAgentNode('session-summary', 'Session Summary', coordinatorAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-initialization', source: 'input', target: 'session-initialization', type: 'default' },\n    { name: 'initialization-to-distribution', source: 'session-initialization', target: 'task-distribution', type: 'default' },\n    { name: 'distribution-to-sharing', source: 'task-distribution', target: 'context-sharing', type: 'default' },\n    { name: 'sharing-to-coding', source: 'context-sharing', target: 'coding', type: 'default' },\n    { name: 'coding-to-review', source: 'coding', target: 'code-review', type: 'default' },\n    { name: 'review-to-resolution', source: 'code-review', target: 'conflict-resolution', type: 'default' },\n    { name: 'resolution-to-integration', source: 'conflict-resolution', target: 'integration', type: 'default' },\n    { name: 'integration-to-summary', source: 'integration', target: 'session-summary', type: 'default' },\n    { name: 'summary-to-output', source: 'session-summary', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'review-to-coding', source: 'code-review', target: 'coding', type: 'feedback' },\n    { name: 'resolution-to-review', source: 'conflict-resolution', target: 'code-review', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect coding to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `coding-to-tool-${index}`,\n        source: 'coding',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to code review\n      edges.push({\n        name: `tool-${index}-to-review`,\n        source: `tool-${index}`,\n        target: 'code-review',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      sessionInitializationNode,\n      taskDistributionNode,\n      contextSharingNode,\n      codingNode,\n      codeReviewNode,\n      conflictResolutionNode,\n      integrationNode,\n      sessionSummaryNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'multi-agent',\n    tags: ['collaborative', 'team', 'coding-session']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized Pair Programming workflow\n */\nexport function createPairProgrammingWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  driverAgent: Agent,\n  navigatorAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Pair Programming workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const taskAnalysisNode = Codessa.createAgentNode('task-analysis', 'Task Analysis', navigatorAgent);\n  const planningNode = Codessa.createAgentNode('planning', 'Planning', navigatorAgent);\n  const codingNode = Codessa.createAgentNode('coding', 'Coding', driverAgent);\n  const codeReviewNode = Codessa.createAgentNode('code-review', 'Code Review', navigatorAgent);\n  const testingNode = Codessa.createAgentNode('testing', 'Testing', driverAgent);\n  const refactoringNode = Codessa.createAgentNode('refactoring', 'Refactoring', driverAgent);\n  const roleSwitchingNode = Codessa.createAgentNode('role-switching', 'Role Switching', navigatorAgent);\n  const sessionSummaryNode = Codessa.createAgentNode('session-summary', 'Session Summary', navigatorAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-analysis', source: 'input', target: 'task-analysis', type: 'default' },\n    { name: 'analysis-to-planning', source: 'task-analysis', target: 'planning', type: 'default' },\n    { name: 'planning-to-coding', source: 'planning', target: 'coding', type: 'default' },\n    { name: 'coding-to-review', source: 'coding', target: 'code-review', type: 'default' },\n    { name: 'review-to-testing', source: 'code-review', target: 'testing', type: 'default' },\n    { name: 'testing-to-refactoring', source: 'testing', target: 'refactoring', type: 'default' },\n    { name: 'refactoring-to-switching', source: 'refactoring', target: 'role-switching', type: 'default' },\n    { name: 'switching-to-summary', source: 'role-switching', target: 'session-summary', type: 'default' },\n    { name: 'summary-to-output', source: 'session-summary', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'review-to-coding', source: 'code-review', target: 'coding', type: 'feedback' },\n    { name: 'testing-to-coding', source: 'testing', target: 'coding', type: 'feedback' },\n    { name: 'switching-to-planning', source: 'role-switching', target: 'planning', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect coding to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `coding-to-tool-${index}`,\n        source: 'coding',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to code review\n      edges.push({\n        name: `tool-${index}-to-review`,\n        source: `tool-${index}`,\n        target: 'code-review',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      taskAnalysisNode,\n      planningNode,\n      codingNode,\n      codeReviewNode,\n      testingNode,\n      refactoringNode,\n      roleSwitchingNode,\n      sessionSummaryNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'multi-agent',\n    tags: ['pair-programming', 'xp', 'collaborative']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}