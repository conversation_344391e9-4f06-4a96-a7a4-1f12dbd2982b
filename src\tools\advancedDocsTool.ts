import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../types/agent';
import { llmService } from '../llm/llmService';
import { LLMConfig } from '../config';
import { z } from 'zod';

export class DocumentationGenTool implements ITool {
  readonly id = 'docGen';
  readonly name = 'Documentation Generation';
  readonly description = 'Generate documentation for code or APIs.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    code: z.string().describe('Code to document.'),
    type: z.enum(['function', 'class', 'module', 'api']).describe('Type of documentation.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      code: { type: 'string', description: 'Code to document.' },
      type: { type: 'string', enum: ['function', 'class', 'module', 'api'], description: 'Type of documentation.' }
    },
    required: ['code', 'type']
  };
  private llmConfig: LLMConfig = {
    provider: 'openai',
    modelId: 'gpt-3.5-turbo',
    options: { temperature: 0.25 }
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const code = input.code as string;
    const type = input.type as string;
    const provider = await llmService.getProviderForConfig(this.llmConfig);
    if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: this.id, actionName };
    const prompt = `Generate ${type} documentation for the following code.\n\n${code}`;
    const result = await provider.generate({ prompt, systemPrompt: 'You are an expert technical writer.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });
    if (result.error) return { success: false, error: result.error, toolId: this.id, actionName };
    return { success: true, output: result.content, toolId: this.id, actionName };
  }
}

export class DocumentationSearchTool implements ITool {
  readonly id = 'docSearch';
  readonly name = 'Documentation Search (Advanced)';
  readonly description = 'Search documentation using web or local sources.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    query: z.string().describe('Documentation search query.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      query: { type: 'string', description: 'Documentation search query.' }
    },
    required: ['query']
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    // Placeholder: In real implementation, this would use web search or offline docs
    return { success: true, output: `Searched documentation for: ${input.query}`, toolId: this.id, actionName };
  }
}

export class DocumentationSummaryTool implements ITool {
  readonly id = 'docSummary';
  readonly name = 'Documentation Summary';
  readonly description = 'Summarize documentation or technical articles.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    text: z.string().describe('Documentation or article to summarize.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      text: { type: 'string', description: 'Documentation or article to summarize.' }
    },
    required: ['text']
  };
  private llmConfig: LLMConfig = {
    provider: 'openai',
    modelId: 'gpt-3.5-turbo',
    options: { temperature: 0.2 }
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const text = input.text as string;
    const provider = await llmService.getProviderForConfig(this.llmConfig);
    if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: this.id, actionName };
    const prompt = `Summarize the following documentation or article for a developer audience.\n\n${text}`;
    const result = await provider.generate({ prompt, systemPrompt: 'You are an expert technical summarizer.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });
    if (result.error) return { success: false, error: result.error, toolId: this.id, actionName };
    return { success: true, output: result.content, toolId: this.id, actionName };
  }
}

export class DocumentationVisualizationTool implements ITool {
  readonly id = 'docViz';
  readonly name = 'Documentation Visualization';
  readonly description = 'Visualize documentation structure or API relationships.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    source: z.string().describe('Source code or API spec to visualize.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      source: { type: 'string', description: 'Source code or API spec to visualize.' }
    },
    required: ['source']
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    // Placeholder: actual visualization would require UI integration
    return { success: true, output: `Visualization data for: ${input.source}`, toolId: this.id, actionName };
  }
}
