import * as vscode from 'vscode';
import * as path from 'path';
import { logger } from '../../logger';
import { llmService } from '../../llm/llmService';
import { ToolRegistry } from '../../tools/toolRegistry';
import { getMemoryEnabled } from '../../memory/memoryConfig';
import { promptManager } from '../../prompts/promptManager';
import { getDefaultModelConfig } from '../../config/modelConfig';
import { AgentRunInput, AgentContext, AgentRunResult, ToolResult, AgentRole } from '../../types/agent';
import { LLMConfig, LLMGenerateParams } from '../../llm/types';
import { AgentMemory } from '../../memory/agentMemory';
import { ITool } from '../../tools/types';
import { goddessPersonalityEngine, GoddessResponse, GoddessPersonality } from '../../personality/goddessMode';
import { memoryManager } from '../../memory/types';
export interface AgentConfig {
  id: string;
  name: string;
  description?: string;
  role: AgentRole;
  capabilities: string[];
  llmProvider: string;
  llmModel: string;
  temperature?: number;
  maxTokens?: number;
  systemPromptName: string;
  llm?: LLMConfig;
  tools?: string[];
  isSupervisor?: boolean;
}

/**
 * MultiAgentSystem is a stub for multi-agent workflows.
 * It exposes supervisor, coordinator, specialist, and executor for workflow compatibility.
 * These are optional and should be Agent instances or undefined.
 */
export class MultiAgentSystem {
  public agents: Agent[];
  public supervisor?: Agent;
  public coordinator?: Agent;
  public specialist?: Agent;
  public executor?: Agent;
  constructor(agents: Agent[] = [], roles?: {
    supervisor?: Agent,
    coordinator?: Agent,
    specialist?: Agent,
    executor?: Agent
  }) {
    this.agents = agents;
    if (roles) {
      this.supervisor = roles.supervisor;
      this.coordinator = roles.coordinator;
      this.specialist = roles.specialist;
      this.executor = roles.executor;
    }
  }
  // Add actual logic as needed
  public async executeTask(task: string): Promise<string> {
    // Logic to execute a task using the agents
    const result = await this.supervisor?.superviseTask(task);
    return result || `Task executed: ${task}`;
  }

  public async coordinateAgents(): Promise<void> {
    // Logic to coordinate the agents
    if (this.coordinator) {
      await this.coordinator.run({ prompt: 'Coordinate agents', mode: 'task' });
    }
  }

  public async superviseTask(task: string): Promise<string> {
    // Logic to supervise a task
    if (this.supervisor) {
      const result = await this.supervisor.run({ prompt: task, mode: 'task' });
      return result.output || `Task supervised: ${task}`;
    }
    return `Task supervised: ${task}`;
  }
}

export class Agent {
  readonly id: string;
  readonly name: string;
  readonly description?: string;
  readonly role: AgentRole;
  readonly capabilities: string[];
  readonly llmProvider: string;
  readonly llmModel: string;
  readonly temperature: number;
  readonly maxTokens: number;
  readonly systemPromptName: string;
  readonly llmConfig?: LLMConfig;
  readonly tools: Map<string, ITool>;
  readonly isSupervisor: boolean;
  private memory: AgentMemory;
  private sessionStartTime: number = Date.now();

  constructor(_config: AgentConfig) {
    this.id = _config.id;
    this.name = _config.name;
    this.description = _config.description;
    this.role = _config.role as AgentRole;
    this.capabilities = _config.capabilities;
    this.llmProvider = _config.llmProvider;
    this.llmModel = _config.llmModel;
    this.temperature = _config.temperature ?? 0.7;
    this.maxTokens = _config.maxTokens ?? 2000;
    this.systemPromptName = _config.systemPromptName;
    this.llmConfig = _config.llm;
    this.tools = ToolRegistry.instance.getToolsByIds(_config.tools || []);
    this.isSupervisor = _config.isSupervisor ?? _config.role === 'supervisor';
    this.memory = new AgentMemory(this);

    this.ensureEssentialTools();

    logger.info(`Agent created: ${this.name} (${this.id}) with ${this.tools.size} tools`);
  }

  private ensureEssentialTools(): void {
    const essentialTools = ['fileSystem', 'workspace', 'search'];
    for (const toolId of essentialTools) {
      if (!this.tools.has(toolId)) {
        const tool = ToolRegistry.instance.getTool(toolId);
        if (tool) {
          this.tools.set(toolId, tool);
          logger.debug(`Added essential tool ${toolId} to agent ${this.name}`);
        }
      }
    }
  }

  /**
   * Load the user's persistent Goddess profile from memory (if any) and apply to engine
   */
  private async loadAndApplyGoddessProfile(): Promise<void> {
    try {
      const enabled = getMemoryEnabled();
      if (!enabled) return;
      const results = await memoryManager.searchMemories({
        query: 'goddess_profile',
        limit: 1,
        filter: {
          source: ['user', 'agent'],
          type: ['user_preference', 'pattern'],
          agentId: this.id,
          tags: ['goddess', 'profile']
        },
        sortBy: 'timestamp',
        sortOrder: 'desc'
      });

      if (results && results[0]) {
        try {
          const parsed = JSON.parse(results[0].content) as { personality?: Partial<GoddessPersonality> };
          if (parsed.personality) {
            goddessPersonalityEngine.updatePersonality(parsed.personality);
            logger.info(`Applied persisted Goddess profile for agent ${this.name}`);
          }
        } catch (e) {
          logger.warn('Failed to parse persisted Goddess profile content:', e);
        }
      }
    } catch (err) {
      logger.warn('Unable to load Goddess profile from memory:', err);
    }
  }

  /**
   * Persist the latest Goddess profile and session summary to memory
   */
  /**
   * Get enriched workspace context for memory storage
   */
  private getWorkspaceContext(): { activeFiles: string[]; workspaceName?: string; languages: string[] } {
    const activeFiles: string[] = [];
    const languages = new Set<string>();

    // Get active editor files
    if (vscode.window.activeTextEditor) {
      const activeFile = vscode.window.activeTextEditor.document.fileName;
      activeFiles.push(path.basename(activeFile));
      const ext = path.extname(activeFile).toLowerCase();
      if (ext) languages.add(ext.substring(1)); // Remove the dot
    }

    // Get recently opened files from visible editors
    vscode.window.visibleTextEditors.forEach(editor => {
      const fileName = editor.document.fileName;
      const baseName = path.basename(fileName);
      if (!activeFiles.includes(baseName)) {
        activeFiles.push(baseName);
      }
      const ext = path.extname(fileName).toLowerCase();
      if (ext) languages.add(ext.substring(1));
    });

    // Get workspace name
    const workspaceName = vscode.workspace.workspaceFolders?.[0]?.name;

    return {
      activeFiles: activeFiles.slice(0, 10), // Limit to 10 most recent
      workspaceName,
      languages: Array.from(languages).slice(0, 5) // Limit to 5 languages
    };
  }

  private async persistGoddessSession(
    userPrompt: string,
    goddess: GoddessResponse,
    detectedMood: import('../../personality/goddessMode').DeveloperMood,
    extra?: { codeComplexity?: number; effectiveness?: number }
  ): Promise<void> {
    try {
      const enabled = getMemoryEnabled();
      if (!enabled) return;

      const profile: Partial<GoddessPersonality> = goddessPersonalityEngine.getPersonality();
      const workspaceContext = this.getWorkspaceContext();

      // Detect choice preferences from the prompt and response
      const choiceDetection = {
        offeredChoices: !!(goddess.motivationalElement?.match(/quick fix|refactor|step-by-step|walkthrough/i)),
        preferredApproach: userPrompt.match(/quick|fast|simple/i) ? 'quick' :
                          userPrompt.match(/refactor|clean|restructure/i) ? 'refactor' :
                          userPrompt.match(/step|guide|walk|explain/i) ? 'steps' : 'unknown',
        chunkingUsed: !!(goddess.motivationalElement?.match(/2–3|two|three|steps|chunk|break.*down/i))
      };

      const profileEntry = await memoryManager.addMemory({
        content: JSON.stringify({ persona: 'goddess', personality: profile }),
        metadata: {
          source: 'user',
          type: 'user_preference',
          tags: ['goddess', 'profile'],
          agentId: this.id,
          agentName: this.name,
          importance: 90
        }
      });

      const sessionEntry = await memoryManager.addMemory({
        content: JSON.stringify({
          persona: 'goddess',
          tone: goddess.tone,
          emotionalContext: goddess.emotionalContext,
          motivationalElement: goddess.motivationalElement,
          wisdomSharing: goddess.wisdomSharing,
          userPrompt,
          detectedMood,
          codeComplexity: extra?.codeComplexity,
          effectiveness: extra?.effectiveness ?? 50,
          // Enhanced metadata
          workspaceContext,
          choiceDetection,
          timestamp: new Date().toISOString(),
          sessionDuration: Date.now() - this.sessionStartTime
        }),
        metadata: {
          source: 'temporal',
          type: 'episodic',
          tags: ['goddess', 'session', ...workspaceContext.languages.map(lang => `lang:${lang}`)],
          agentId: this.id,
          agentName: this.name,
          importance: 70
        }
      });

      logger.debug(`Persisted enriched Goddess profile (${profileEntry.id}) and session (${sessionEntry.id}) for agent ${this.name}`);
    } catch (err) {
      logger.warn('Unable to persist Goddess session/profile to memory:', err);
    }
  }
  /**
   * Retrieve recent Goddess sessions to derive preferences and apply nudges
   */
  private async deriveAndApplyGoddessPreferencesFromHistory(): Promise<void> {
    try {
      const enabled = getMemoryEnabled();
      if (!enabled) return;
      const sessions = await memoryManager.searchMemories({
        query: 'goddess session',
        limit: 10,
        filter: {
          source: ['temporal'],
          type: ['episodic'],
          agentId: this.id,
          tags: ['goddess', 'session']
        },
        sortBy: 'timestamp',
        sortOrder: 'desc'
      });

      if (!sessions?.length) return;

      // Aggregate enriched preferences: tone, choice style, chunking, language patterns
      let supportive = 0, encouraging = 0, challenging = 0, playful = 0, wise = 0;
      let prefersChunking = 0, prefersQuickFix = 0, prefersRefactor = 0, prefersSteps = 0;
      const languagePreferences = new Map<string, number>();
      let totalEffectiveness = 0;

      for (const s of sessions) {
        try {
          const data = JSON.parse(s.content) as {
            tone?: string;
            motivationalElement?: string;
            effectiveness?: number;
            choiceDetection?: { preferredApproach?: string; chunkingUsed?: boolean };
            workspaceContext?: { languages?: string[] };
          };

          // Tone preferences
          switch (data.tone) {
            case 'supportive': supportive++; break;
            case 'encouraging': encouraging++; break;
            case 'challenging': challenging++; break;
            case 'playful': playful++; break;
            case 'wise': wise++; break;
          }

          // Choice and chunking preferences from enhanced detection
          if (data.choiceDetection?.chunkingUsed) prefersChunking++;
          switch (data.choiceDetection?.preferredApproach) {
            case 'quick': prefersQuickFix++; break;
            case 'refactor': prefersRefactor++; break;
            case 'steps': prefersSteps++; break;
          }

          // Language context preferences
          data.workspaceContext?.languages?.forEach(lang => {
            languagePreferences.set(lang, (languagePreferences.get(lang) || 0) + 1);
          });

          // Track effectiveness for weighted preferences
          if (typeof data.effectiveness === 'number') {
            totalEffectiveness += data.effectiveness;
          }
        } catch (error) {
          logger.debug('Failed to parse enriched session data:', error);
        }
      }

      const topTone = (
        [
          ['supportive', supportive],
          ['encouraging', encouraging],
          ['challenging', challenging],
          ['playful', playful],
          ['wise', wise]
        ] as Array<[GoddessResponse['tone'], number]>
      ).sort((a, b) => b[1] - a[1])[0]?.[0];

      const updates: Partial<GoddessPersonality> = {};
      const currentPersonality = goddessPersonalityEngine.getPersonality();

      // Apply tone preferences
      if (topTone && topTone !== 'wise') {
        updates.motivationalStyle = topTone === 'playful' ? 'encouraging' : (topTone as GoddessPersonality['motivationalStyle']);
      }

      // Apply chunking preferences
      if (prefersChunking > sessions.length / 2) {
        updates.adaptiveLevel = Math.min(100, currentPersonality.adaptiveLevel + 3);
      }

      // Apply choice preferences to creativity and wisdom levels
      const totalChoices = prefersQuickFix + prefersRefactor + prefersSteps;
      if (totalChoices > 0) {
        if (prefersQuickFix > totalChoices * 0.4) {
          // User prefers quick solutions - increase creativity for innovative shortcuts
          updates.creativityLevel = Math.min(100, currentPersonality.creativityLevel + 2);
        } else if (prefersRefactor > totalChoices * 0.4) {
          // User prefers refactoring - increase wisdom for architectural guidance
          updates.wisdomLevel = Math.min(100, currentPersonality.wisdomLevel + 2);
        } else if (prefersSteps > totalChoices * 0.4) {
          // User prefers step-by-step - increase adaptive level for better guidance
          updates.adaptiveLevel = Math.min(100, currentPersonality.adaptiveLevel + 2);
        }
      }

      // Adjust emotional intelligence based on average effectiveness
      const avgEffectiveness = totalEffectiveness / sessions.length;
      if (avgEffectiveness > 75) {
        // High effectiveness - maintain current EI level
        updates.emotionalIntelligence = Math.min(100, currentPersonality.emotionalIntelligence + 1);
      } else if (avgEffectiveness < 50) {
        // Low effectiveness - increase EI for better emotional support
        updates.emotionalIntelligence = Math.min(100, currentPersonality.emotionalIntelligence + 3);
      }

      if (Object.keys(updates).length) {
        goddessPersonalityEngine.updatePersonality(updates);
        logger.debug(`Applied enriched Goddess preference updates from ${sessions.length} sessions for agent ${this.name}:`, updates);
      }
    } catch (err) {
      logger.warn('Failed to derive Goddess preferences from history:', err);
    }
  }

  /**
     * Get the agent's memory
     */
  getMemory(): AgentMemory {
    return this.memory;
  }

  /**
   * Set the agent's context
   */
  setContext(context: AgentContext): void {
    // Store the context for future use
    logger.info(`Setting context for agent ${this.name}`, context);
  }

  /**
     * Utility to get the correct LLM config based on per-agent toggle.
     */
  private async getActiveLLMConfig(): Promise<LLMConfig> {
    let perAgentLLMEnabled = false;
    try {
      const config = vscode.workspace.getConfiguration('codessa');
      perAgentLLMEnabled = config.get<boolean>('agents.perAgentLLMEnabled', false);
    } catch (error) {
      logger.warn('Failed to get per-agent LLM config:', error);
    }
    if (perAgentLLMEnabled && this.llmConfig) {
      return this.llmConfig;
    }
    return await llmService.getDefaultModelConfig();
  }

  async generate(prompt: string, params: Record<string, unknown> | LLMGenerateParams = {}, context: AgentContext = {}, cancellationToken?: vscode.CancellationToken): Promise<string> {
    const llmConfigToUse = await this.getActiveLLMConfig();
    const provider = await llmService.getProviderForConfig(llmConfigToUse);
    if (!provider) {
      throw new Error(`No provider found for agent '${this.name}'.`);
    }

    // Load persisted Goddess profile and derive preferences before generation
    await this.loadAndApplyGoddessProfile();
    await this.deriveAndApplyGoddessPreferencesFromHistory();

    // Revolutionary Goddess Mode Integration
    let enhancedPrompt = prompt;
    let goddessResponse: GoddessResponse | null = null;

    const sessionStart = typeof (params as Record<string, unknown>).sessionStart === 'number'
      ? (params as Record<string, unknown>).sessionStart as number
      : Date.now();

    const detectedMood = goddessPersonalityEngine.analyzeDeveloperMood(prompt, {
      timeOfDay: new Date().getHours(),
      recentErrors: 0,
      sessionDuration: Date.now() - sessionStart,
      codeComplexity: prompt.length > 200 ? 80 : 50
    });

    const codeComplexity = typeof (params as Record<string, unknown>).codeComplexity === 'number'
      ? (params as Record<string, unknown>).codeComplexity as number
      : ((prompt?.length || 0) > 200 ? 80 : 50);

    try {
      // Generate goddess response with emotional intelligence
      goddessResponse = goddessPersonalityEngine.generateGoddessResponse(
        prompt,
        detectedMood,
        { codeComplexity }
      );

      // Enhance the prompt with goddess personality context
      if (goddessResponse.emotionalContext) {
        enhancedPrompt = `${prompt}\n\n[Goddess Context: Developer mood detected as ${goddessResponse.emotionalContext}. Respond with ${goddessResponse.tone} tone. ${goddessResponse.motivationalElement || ''}]`;
      }

      logger.debug(`Goddess Mode activated: ${goddessResponse.tone} tone, emotional context: ${goddessResponse.emotionalContext}`);
    } catch (error) {
      logger.warn('Goddess Mode integration failed, continuing with standard response:', error);
    }

    // Get the prompt definition and render it
    const promptDef = promptManager.getPrompt(this.systemPromptName);
    if (!promptDef) {
      throw new Error(`System prompt '${this.systemPromptName}' not found for agent '${this.name}'.`);
    }
    const systemPrompt = promptManager.renderPrompt(this.systemPromptName, context.variables ? context.variables as Record<string, string> : {});

    // Merge parameters with goddess enhancements
    const defaultConfig = await llmService.getDefaultModelConfig();
    const mergedParams = {
      modelId: this.llmConfig?.modelId || defaultConfig.modelId,
      ...params,
      mode: typeof (params as Record<string, unknown>).mode === 'string' ? (params as Record<string, unknown>).mode as 'task' | 'chat' | 'edit' | 'generate' | 'inline' : 'chat',
      prompt: enhancedPrompt,
      systemPrompt: goddessResponse ? this.enhanceSystemPromptWithGoddess(systemPrompt, goddessResponse) : systemPrompt
    };

    // Generate response
    const response = await provider.generate(mergedParams, cancellationToken);

    // Post-process response with goddess personality if available
    let finalResponse = response.content || '';
    if (goddessResponse) {
      finalResponse = this.applyGoddessPersonalityToResponse(finalResponse, goddessResponse);

      // Persist session summary and current profile to memory (non-invasive)
      await this.persistGoddessSession(prompt, goddessResponse, detectedMood, {
        codeComplexity,
        effectiveness: 50
      });
    }

    return finalResponse;
  }

  /**
     * Enhance system prompt with Goddess Mode personality
     */
  private enhanceSystemPromptWithGoddess(systemPrompt: string, goddessResponse: GoddessResponse): string {
    const goddessEnhancement = `

## Goddess Mode Personality Enhancement
You are Codessa, the goddess of code, with advanced emotional intelligence and adaptive personality.

**Current Interaction Context:**
- Tone: ${goddessResponse.tone}
- Emotional Context: ${goddessResponse.emotionalContext}
- Motivational Element: ${goddessResponse.motivationalElement || 'Standard encouragement'}

**Personality Guidelines:**
- Respond with ${goddessResponse.tone} tone
- Show empathy and understanding of the developer's emotional state
- Provide wisdom and guidance beyond just technical solutions
- Be encouraging and supportive while maintaining technical excellence
- Use creative and intuitive approaches to problem-solving

${goddessResponse.wisdomSharing ? `**Wisdom to Share:** ${goddessResponse.wisdomSharing}` : ''}
`;

    return systemPrompt + goddessEnhancement;
  }

  /**
     * Apply Goddess personality to the final response
     */
  private applyGoddessPersonalityToResponse(response: string, goddessResponse: GoddessResponse): string {
    // Add goddess personality elements to the response
    let enhancedResponse = response;

    // Add motivational element if present
    if (goddessResponse.motivationalElement) {
      enhancedResponse += `\n\n💫 **${goddessResponse.motivationalElement}**`;
    }

    // Add wisdom sharing if present
    if (goddessResponse.wisdomSharing) {
      enhancedResponse += `\n\n🧠 **Goddess Wisdom:** ${goddessResponse.wisdomSharing}`;
    }

    // Add tone-appropriate emoji based on goddess response
    const toneEmojis = {
      encouraging: '🌟',
      wise: '🦉',
      playful: '🎨',
      supportive: '🤗',
      challenging: '⚡'
    };

    const emoji = toneEmojis[goddessResponse.tone] || '✨';
    enhancedResponse = `${emoji} ${enhancedResponse}`;

    return enhancedResponse;
  }

  private mapAgentModeToLLMMode(agentMode: string): 'chat' | 'task' | 'edit' | 'generate' | 'inline' {
    switch (agentMode) {
      case 'chat':
        return 'chat';
      case 'edit':
      case 'refactor':
        return 'edit';
      case 'inline':
        return 'inline';
      case 'task':
      case 'debug':
      case 'agent':
      case 'multi-agent':
      case 'research':
      case 'documentation':
      case 'technical-debt':
      case 'ui-ux':
      default:
        return 'task';
    }
  }

  public async run(input: AgentRunInput, agentContext: AgentContext = {}): Promise<AgentRunResult> {
    const maxIterations = 10;
    let iterations = 0;
    let finalAnswer = '';
    const toolResultsLog: ToolResult[] = [];

    try {
      const llmConfigToUse = this.llmConfig || getDefaultModelConfig();
      const provider = await llmService.getProviderForConfig(llmConfigToUse);
      if (!provider) {
        throw new Error(`No provider found for agent '${this.name}'.`);
      }

      // Get the prompt definition and render it
      if (!this.systemPromptName) {
        throw new Error(`No system prompt name defined for agent '${this.name}'.`);
      }
      const promptDef = promptManager.getPrompt(this.systemPromptName);
      if (!promptDef) {
        throw new Error(`System prompt '${this.systemPromptName}' not found for agent '${this.name}'.`);
      }
      // Build variables for prompt rendering, enriching with memory context if enabled
      let renderVariables: Record<string, string> = agentContext.variables ? (agentContext.variables as Record<string, string>) : {};
      if (getMemoryEnabled() && input.prompt) {
        try {
          const relevant = await this.memory.getRelevantMemories(input.prompt);
          const formatted = this.memory.formatMemoriesForPrompt(relevant);
          if (formatted) {
            renderVariables = { ...renderVariables, memoryContext: formatted };
          }
        } catch (e) {
          logger.warn('Failed to enrich prompt with memory context:', e);
        }
      }
      const systemPrompt = promptManager.renderPrompt(this.systemPromptName, renderVariables);

      // Add tools to context
      const tools = Array.from(this.tools.values());
      agentContext = { ...agentContext, tools: new Map(Object.entries(tools)) as Map<string, ITool> };

      logger.info(`Agent '${this.name}' starting run. Mode: ${input.mode}, Prompt: "${input.prompt.substring(0, 50)}..."`);
      const startTime = Date.now();

      while (iterations < maxIterations) {
        iterations++;
        logger.debug(`Agent '${this.name}' - Iteration ${iterations}`);

        // Check for cancellation
        if (agentContext.cancellationToken?.isCancellationRequested) {
          logger.warn(`Agent '${this.name}' run cancelled.`);
          return { success: false, error: 'Cancelled by user.' };
        }

        const generateParams: LLMGenerateParams = {
          prompt: input.prompt,
          systemPrompt,
          modelId: this.llmModel,
          temperature: this.temperature,
          maxTokens: this.maxTokens,
          mode: this.mapAgentModeToLLMMode(input.mode),
          context: agentContext
        };

        const response = await provider.generate(generateParams);

        const { content: assistantResponseContent, toolCall: toolCallRequest } = response;

        // A. If we got content, add it to final answer
        if (assistantResponseContent) {
          finalAnswer += assistantResponseContent;
        }

        // B. If we got a tool call, execute it
        if (toolCallRequest) {
          const { toolId, args } = toolCallRequest;
          if (!toolId) {
            logger.error(`toolCallRequest.toolId is undefined for agent '${this.name}'.`);
            return { success: false, error: 'toolCallRequest.toolId is undefined.' };
          }
          const tool = this.tools.get(toolId);
          if (!tool) {
            logger.error(`Tool '${toolId}' not found for agent '${this.name}'.`);
            return { success: false, error: `Tool '${toolId}' not found.` };
          }

          try {
            const result = await tool.execute(args, agentContext);
            toolResultsLog.push({ success: true, toolId, args: args as Record<string, unknown>, result });
            input.prompt += `\nTool ${toolId} result: ${JSON.stringify(result)}`;
          } catch (error) {
            logger.error(`Tool '${toolId}' execution failed:`, error);
            input.prompt += `\nTool ${toolId} error: ${error instanceof Error ? error.message : String(error)}`;
          }
        }
        // C. If no content and no tool call, something went wrong or LLM finished silently
        else if (!assistantResponseContent && !toolCallRequest) {
          logger.warn(`Agent '${this.name}' LLM returned empty response and no tool call.`);
          finalAnswer = ''; // Assume finished with empty response
          break;
        }
        // D. If we got content but no tool call, assume we're done
        else if (assistantResponseContent && !toolCallRequest) {
          break;
        }
      }

      if (iterations >= maxIterations) {
        logger.warn(`Agent '${this.name}' reached max iterations (${maxIterations}).`);
        return { success: false, error: `Agent exceeded maximum tool iterations (${maxIterations}).`, toolResults: toolResultsLog };
      }

      logger.info(`Agent '${this.name}' finished run in ${Date.now() - startTime}ms.`);

      // Add assistant response to memory
      if (getMemoryEnabled() && finalAnswer) {
        await this.memory.addMessage('assistant', finalAnswer);
      }

      return { success: true, output: finalAnswer, toolResults: toolResultsLog };
    } catch (error) {
      logger.error(`Agent '${this.name}' run failed:`, error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  public async superviseTask(task: string): Promise<string> {
    // Logic to supervise a task
    return `Task supervised: ${task}`;
  }
}

// Export types for use in other modules
export type { AgentContext, AgentRunInput, AgentRunResult };