{"version": 3, "file": "agent.js", "sourceRoot": "", "sources": ["../../src/types/agent.ts"], "names": [], "mappings": "", "sourcesContent": ["import { LLMConfig } from '../llm/types';\n\nexport type AgentRole = 'receiver' | 'supervisor' | 'assistant' | 'specialist';\n\nexport interface AgentConfig {\n    id: string;\n    name: string;\n    description?: string;\n    systemPromptName: string;\n    llm?: LLMConfig;\n    tools?: string[];\n    chainedAgentIds?: string[];\n    role: AgentRole;\n    capabilities: string[];\n    llmProvider: string;\n    llmModel: string;\n    temperature?: number;\n    maxTokens?: number;\n    isSupervisor?: boolean;\n}\n\nimport { IWorkflowManager, IMCPManager, IPromptManager, IKnowledgebaseManager } from '../managers';\n\nexport interface AgentContext {\n    workspace?: {\n        currentFile?: string;\n        selection?: { text: string; range: { start: number; end: number } };\n        workspaceFolders?: string[];\n    };\n    debugData?: {\n        bugOrigin?: unknown;\n        analysisTimestamp?: number;\n        lastError?: {\n            message: string;\n            timestamp: number;\n            details?: string;\n        };\n    };\n    variables?: {\n        workflowManager?: IWorkflowManager;\n        mcpManager?: IMCPManager;\n        promptManager?: IPromptManager;\n        knowledgebaseManager?: IKnowledgebaseManager;\n        [key: string]: unknown;\n    };\n    cancellationToken?: { isCancellationRequested: boolean };\n    tools?: Map<string, unknown>;\n    filePath?: string;\n    streamingContext?: {\n        streamId: string;\n        parentStreamId?: string;\n        onStream?: (event: AgentStreamEvent) => void;\n        cancellationToken?: { isCancellationRequested: boolean };\n    };\n}\n\nexport interface AgentStreamEvent {\n    type: 'start' | 'progress' | 'data' | 'tool_call' | 'delegation' | 'complete' | 'error';\n    agentId: string;\n    timestamp: number;\n    data?: any;\n    content?: string;\n    metadata?: Record<string, unknown>;\n    error?: Error;\n}\n\nexport interface AgentRunInput {\n    prompt: string;\n    mode: string;\n    context?: AgentContext;\n}\n\nexport interface AgentRunResult {\n    success: boolean;\n    output?: string;\n    error?: string;\n    toolResults?: ToolResult[];\n}\n\nexport interface ToolResult {\n    /** Indicates if the tool executed successfully. */\n    success: boolean;\n    /** The output payload of the tool execution on success. Can be any serializable data. */\n    output?: unknown;\n    /** An error message or object if the tool execution failed. */\n    error?: string | Error;\n    /** Optional usage metrics (e.g., tokens consumed, cost). */\n    usage?: { [key: string]: number };\n    /** The ID of the tool that was executed. */\n    toolId: string;\n    /** The name of the specific action executed if the tool has multiple actions. */\n    actionName?: string;\n    /** Additional metadata about the execution or result. */\n    metadata?: Record<string, unknown>;\n    /** The arguments passed to the tool. */\n    args: Record<string, unknown>;\n    /** The result of the tool execution. */\n    result?: unknown;\n}\n"]}