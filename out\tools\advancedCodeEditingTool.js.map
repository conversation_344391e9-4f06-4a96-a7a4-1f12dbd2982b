{"version": 3, "file": "advancedCodeEditingTool.js", "sourceRoot": "", "sources": ["../../src/tools/advancedCodeEditingTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,mCAAsC;AAiBtC,2CAAyC;AAGzC,yBAAyB;AACzB,mDAayB;AA4KzB,MAAa,uBAAwB,SAAQ,8BAAc;IACzD,6BAA6B;IACb,IAAI,GAAG,qBAAqB,CAAC;IAC7B,WAAW,GAAG,qIAAqI,CAAC;IACpJ,OAAO,GAAG,OAAO,CAAC;IAClB,QAAQ,GAAG,aAAa,CAAC;IAEzC,qBAAqB;IACb,UAAU,CAAe;IACzB,YAAY,CAAuB;IACnC,gBAAgB,GAAG,IAAI,GAAG,EAA2B,CAAC;IACtD,gBAAgB,GAA4B,EAAE,CAAC;IAEvD,uBAAuB;IACf,MAAM,CAAU;IAChB,UAAU,CAAqB;IAC/B,kBAAkB,CAA6B;IAC/C,gBAAgB,CAAoB;IACpC,SAAS,CAAa;IAE9B,YACE,eAAmC,EACnC,SAAqB,EACrB,WAAiC,EACjC,QAKC;QAED,KAAK,CAAC,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC;QAEnD,kCAAkC;QAClC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,OAA6B,CAAC;QAC/D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC;QACrD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,6CAA6C;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,qBAAY,EAAE,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE9C,kDAAkD;QAClD,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED,yDAAyD;IAClD,KAAK,CAAC,kBAAkB,CAAC,OAAmB,EAAE,KAAc;QACjE,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACtD,2CAA2C;YAC3C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,qBAAqB,EAAE,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,kBAAkB,CAC9B,WAAmB,EACnB,WAAmB,YAAY,EAC/B,OAAyB;QAUzB,MAAM,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,yCAAyC;YACzC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBAChD,IAAI,EAAE,WAAW;gBACjB,QAAQ;gBACR,QAAQ,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,UAAU,CAAC;aACrD,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,wBAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC/C,MAAM,QAAQ,GAAG;gBACf,QAAQ;gBACR,QAAQ;gBACR,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,CAAC;gBACpC,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC;aAC/C,CAAC;YAEF,8BAA8B;YAC9B,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAE1E,oCAAoC;YACpC,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;gBACnC,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC5B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;oBACtB,WAAW;oBACX,QAAQ;oBACR,QAAQ;iBACT,CAAC;gBACF,QAAQ,EAAE;oBACR,IAAI,EAAE,eAAe;oBACrB,MAAM,EAAE,aAAa;oBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,GAAG,QAAQ;iBACZ;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,YAAY,EAAE,WAAW;gBACzB,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,WAAW;gBACpD,WAAW,EAAE,QAAQ,CAAC,OAAO,IAAI,0BAA0B;gBAC3D,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,CAAC;gBACpC,YAAY,EAAE,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE;aAClE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;YAE9E,mDAAmD;YACnD,OAAO;gBACL,YAAY,EAAE,WAAW;gBACzB,aAAa,EAAE,WAAW;gBAC1B,WAAW,EAAE,yBAAyB,GAAG,YAAY;gBACrD,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,EAAE;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,gBAAgB,GAUnB,IAAI,GAAG,EAAE,CAAC;IAEP,cAAc,CAAsB;IACpC,YAAY,CAAoB;IAChC,UAAU,CAAe;IAEjC,YACE,eAAmC,EACnC,SAAqB,EACrB,WAAiC,EACjC,QAKC;QAED,KAAK,CAAC,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC;QAEnD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,OAA6B,CAAC;QAC/D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,SAAS,CAAC;QAC7C,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC;QAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,qBAAY,EAAE,CAAC;QAErC,2BAA2B;QAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE9C,kCAAkC;QAClC,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,yBAAyB;QACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,2BAA2B;QAC3B,IAAI,CAAC,YAAY,GAAG;YAClB,IAAI,EAAE,kBAAU,CAAC,QAAQ;YACzB,MAAM,EAAE,oBAAY,CAAC,IAAI;YACzB,QAAQ,EAAE;gBACR,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,cAAc,CAAC;aACpD;SACF,CAAC;QAEF,0CAA0C;QAC1C,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,kCAAkC;QAClC,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACK,4BAA4B;QAClC,uDAAuD;QACvD,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,2BAA2B,CAAC,CAAC;QAEtF,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACjD,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,QAAgB;QACrD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzD,gDAAgD;YAChD,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;gBACnC,EAAE,EAAE,QAAQ,QAAQ,EAAE;gBACtB,OAAO;gBACP,QAAQ,EAAE;oBACR,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,QAAQ;oBACd,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;oBACxB,MAAM,EAAE,qBAAqB;iBAC9B;aACF,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,QAAQ,KAAK,YAAY,EAAE,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;QACtH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,4BAA4B,CAAC,QAAgB;QACzD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,QAAQ,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,QAAQ,EAAE,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,QAAQ,8BAA8B,YAAY,EAAE,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;QACvH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;gBAC3F,OAAO;YACT,CAAC;YAED,gDAAgD;YAChD,MAAM,gBAAgB,GAAsB;gBAC1C;oBACE,EAAE,EAAE,gBAAgB;oBACpB,OAAO,EAAE,4BAA4B;oBACrC,KAAK,EAAE,oBAAoB;oBAC3B,WAAW,EAAE,qCAAqC;oBAClD,MAAM,EAAE,KAAK,EAAE,KAAuB,EAAE,EAAE;wBACxC,4CAA4C;wBAC5C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;oBAC3B,CAAC;iBACF;gBACD;oBACE,EAAE,EAAE,iBAAiB;oBACrB,OAAO,EAAE,mDAAmD;oBAC5D,KAAK,EAAE,wBAAwB;oBAC/B,WAAW,EAAE,2BAA2B;oBACxC,MAAM,EAAE,KAAK,EAAE,KAAuB,EAAE,EAAE;wBACxC,+CAA+C;wBAC/C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;oBAC3B,CAAC;iBACF;aACF,CAAC;YAEF,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,KAAK,UAAU,EAAE,CAAC;gBACnE,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;gBAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,SAAiB,EAAE,QAAgB,EAAE,OAAgB,EAAE,KAAc;QAC9F,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG;gBACf,SAAS;gBACT,QAAQ;gBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,OAAO;gBACP,KAAK;aACN,CAAC;YAEF,mEAAmE;YACnE,yCAAyC;YACzC,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3E,CAAC;YAED,kEAAkE;YAClE,8DAA8D;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,SAAS;gBACT,QAAQ;gBACR,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CAAA;AApVH,0DAoVG;AAEG,4DAA4D;AAC5D,MAAM,EAAE,QAAQ,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,aAAa,CAAC;AAChE,MAAM,QAAQ,GAAkC;IAC9C,MAAM,EAAE,cAAc;IACtB,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,IAAI;IACb,GAAG,gBAAgB;IACnB,kDAAkD;IAClD,QAAQ,EAAE,IAAI,CAAC,IAAI;IACnB,SAAS,EAAE,aAAa,CAAC,SAAS;IAClC,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;CAC/D,CAAC;AAEF,8CAA8C;AAC9C,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;IACjC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;IAC5C,QAAQ;CACT,CAAC,CAAC;AAIS,KAAK,CAAA;AAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAA;AAAE,OAAO,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAG,MAAM,CAAC,CAAC,EAAE,GAAG;IACnI,KAAK,EAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,CACvC,UAAU,EACV,cAAyC,CAC1C;IAED,GAAG,EAAC;QACF,GAAG,EAAC,MAAM,EAAE,OAAO;QAEnB,MAAM,CAAC,UAAU;QACb,CAAC,AADe;QAChB,IAAI,EAAC,cAAc;QACvB,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAmC,CAAC;QAC5E,KAAK,EAAA;QACL,IAAI,EAAC,aAAa;QAClB,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAkC,CAAC;QAC1E,KAAK,EAAA;QACL,IAAI,EAAC,aAAa;QAClB,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAkC,CAAC;QAC1E,KAAK,EAAA;QACL,OAAO,EACD,AADE;QACF,KAAK,EAAC,IAAI,KAAK,CAAC,uBAAuB,UAAU,EAAE,CAAC;KAC3D;IAEG,uCAAuC;;IAAvC,uCAAuC;IACvC,KAAK,EAAC,IAAI,EAAA,CAAC,qBAAqB,CAAC,MAAM,EAAE;QAC3C,MAAM,EAAE,MAAM;QACd,KAAK,EAAE;YACL,GAAG,MAAM,CAAC,KAAK;YACf,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;YACnB,QAAQ,EAAE,GAAG;SACd;QACD,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS;KAC9C,CAAC;IAEF,MAAM,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE;CAE1B,CAAA;AAAC,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACf,yDAAyD;IACzD,MAAM,QAAQ,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACxE,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;QACvC,KAAK,EAAE;YACL,GAAG,MAAM,CAAC,KAAK;YACf,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;SACpB;QACD,KAAK,EAAE;YACL,OAAO,EAAE,QAAQ;YACjB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACxD;QACD,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS;KAC9C,CAAC,CAAC;IAEH,MAAM,KAAK,CAAC;AACd,CAAC;AAMS,KAAK,CAAA;AAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAG,GAAG,EAAE,CAAC,CAAA;AAAE,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAC9G,GAAG,EAAC;QACF,IAAI,EAAA,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC9D,iGAAiG;QACjG,KAAK,EAAC,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QAC9D,wEAAwE;QACxE,KAAK,EAAC,gBAAgB,GAAG,OAAO;aAC7B,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;aAC3B,IAAI,CAAC,IAAI,CAAC;aACV,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;QAEzB,KAAK,EAAC,IAAI,EAAA,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC;QACjE,MAAM,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE;KACzB,EAAC,KAAK,CAAC,KAAK;QACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACrD,MAAM,KAAK,CAAC;IACd,CAAC;CACF,CAAA;AAKS,KAAK,CAAA;AAAC,sBAAsB,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAG,GAAG,EAAE,CAAC,CAAA;AAAE,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IACjI,GAAG,EAAC;QACF,IAAI,EAAA,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,KAAK,CAAC,QAAQ,aAAa,KAAK,CAAC,MAAM,EAAE,CAAC;QAE5F,gCAAgC;QAChC,KAAK,EAAC,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QAC9D,GAAG,EAAC,UAAU,GAAG,OAAO;QAExB,6BAA6B;QAC7B,MAAM,CAAC,KAAK,IAAA,CAAC,AAAD,EAAA,EAAA,CAAC,MAAM,CAAC,WAAW,EAAE;KAAC;CAAA,CAAA;AAAC,CAAC;IAC3B,gBAAgB,CAAA;IAC3B,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxE,MAAM;IACK,uBAAuB,CAAA;IAClC,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACzC,MAAM;IACK,iBAAiB,CAAA;IAC5B,UAAU,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;IACtC,MAAM;IACN,kCAAkC;AACpC,CAAC;AAED,gCAAgC;AAChC,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;IAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC9D,CAAC;AAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACnB,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;IAC3D,MAAM,KAAK,CAAC;AACd,CAAC;AAMS,KAAK,CAAA;AAAC,gBAAgB,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAG,GAAG,EAAE,CAAC,CAAA;AAAE,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAC3H,GAAG,EAAC;QACF,IAAI,EAAA,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,QAAQ,EAAE,CAAC;QAErD,EAAA,CAAC,UAAU,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK,UAAU;KAAC;CAAA,CAAA;AAAC,CAAC;IAC1E,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAC7E,CAAC;AAAM,CAAC;IACN,0BAA0B;IAC1B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IACxD,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC9D,CAAC;AACD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACnB,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;IACpD,MAAM,KAAK,CAAC;AACd,CAAC;AAMS,UAAU,CAAC,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;AAAE,MAAM,CAAA;AAAC,CAAC;IACpE,sCAAsC;IACtC,0EAA0E;IAC1E,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QAC7B,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,wBAAwB;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACpE,IAAI,SAAS,EAAE,CAAC;gBACd,CAAC,EAAE,CAAC;gBACJ,4CAA4C;gBAC5C,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChE,MAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;oBAC/B,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC5D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,CAAC;yBAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBACpE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACxB,CAAC;oBACD,CAAC,EAAE,CAAC;gBACN,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,CAAC,EAAE,CAAC;QACN,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAKO,KAAK,CAAA;AAAC,uBAAuB,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAA;AAAE,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAC/I,GAAG,EAAC;QACM,EAAA,CAAC,UAAU,EAAE,KAAK,EAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC;QAEjE,KAAK,EAAC,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QAC9D,KAAK,EAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;QACtD,KAAK,EAAC,OAAO,GAAG,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACnD,MAAM,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;KAClC,EAAC,KAAK,CAAC,KAAK;QACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC5C,CAAC;CACF,CAAA;AAOS,KAAK,CAAA;AAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;AAAE,OAAO,GAAG,MAAM,GAAG;IACrE,KAAK,EAAC,MAAM,GAAG,wDAAa,QAAQ,GAAC;IACrC,MAAM,EAAC,MAAM;SACV,UAAU,CAAC,QAAQ,CAAC;SACpB,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC;SACvB,MAAM,CAAC,KAAK,CAAC;CACjB,CAAA;AAKS,KAAK,CAAA;AAAC,mBAAmB,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAA;AAAE,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAG,MAAM,EAAE,GAAG;IAClI,GAAG,EAAC;QACM,EAAA,CAAC,UAAU,EAAE,KAAK,EAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC;QAEjE,KAAK,EAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,KAAK;QAExE,wBAAwB;QACxB,KAAK,EAAC,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAChE,KAAK,EAAC,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAEhE,wBAAwB;QACxB,EAAE,CAAC,SAAS,EAAC,AAAD,EAAG,AAAD,IAAG,CAAC,AAAF;KAAA,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,IAAI,SAAS,GAAG,OAAO;CAAC,CAAA;AAAC,CAAC;IACtE,MAAM,IAAI,KAAK,CAAC,uBAAuB,SAAS,IAAI,OAAO,EAAE,CAAC,CAAC;AACjE,CAAC;AAED,6BAA6B;AAC7B,IAAI,YAAY,EAAE,CAAC;IACjB,MAAM,UAAU,GAAG,GAAG,QAAQ,MAAM,CAAC;IACrC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;AAC/D,CAAC;AAED,oBAAoB;AACpB,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAErG,8CAA8C;AAC9C,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAE5D,4EAA4E;AAC5E,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE;IACxD,QAAQ;IACR,SAAS;IACT,OAAO;IACP,aAAa,EAAE,YAAY,CAAC,MAAM;IAClC,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;CAC5C,EAAE;IACD,MAAM,EAAE,cAAc;IACtB,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,IAAI;IACb,aAAa,EAAE,CAAC,QAAQ,CAAC;CAC1B,CAAC,CAAC;AAEH,qDAAqD;AACrD,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;IACvC,KAAK,EAAE;QACL,MAAM,EAAE,WAAW;QACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;KACpB;CACF,CAAC,CAAC;AAEH,OAAO;IACL,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,+BAA+B,SAAS,IAAI,OAAO,EAAE;IAC9D,QAAQ,EAAE,MAAM,CAAC,EAAE;CACpB,CAAC;AACE,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;IAC5D,MAAM,KAAK,CAAC;AACd,CAAC;AAMO,KAAK,CAAA;AAAC,kBAAkB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;AAAE,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAG,MAAM,EAAE,GAAG;IAC9H,GAAG,EAAC;QACM,EAAA,CAAC,UAAU,EAAE,KAAK,EAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC;QAEjE,KAAK,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,KAAK;QAE1D,wBAAwB;QACxB,KAAK,EAAC,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAChE,KAAK,EAAC,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAEhE,uBAAuB;QACvB,EAAE,CAAC,IAAI,EAAC,AAAD,EAAG,AAAD,IAAG,CAAC,AAAF;KAAA,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM;CAAC,CAAA;AAAC,CAAC;IACvC,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAC;AAClD,CAAC;AAED,6BAA6B;AAC7B,IAAI,YAAY,EAAE,CAAC;IACjB,MAAM,UAAU,GAAG,GAAG,QAAQ,MAAM,CAAC;IACrC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;AAC/D,CAAC;AAED,+CAA+C;AAC/C,MAAM,cAAc,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC7C,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACxC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC;AAE7C,8CAA8C;AAC9C,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAE5D,4EAA4E;AAC5E,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;IACvD,QAAQ;IACR,IAAI;IACJ,aAAa,EAAE,QAAQ,CAAC,MAAM;IAC9B,cAAc,EAAE,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,UAAU;CAC1F,EAAE;IACD,MAAM,EAAE,cAAc;IACtB,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,IAAI;IACb,aAAa,EAAE,CAAC,QAAQ,CAAC;CAC1B,CAAC,CAAC;AAEH,qDAAqD;AACrD,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;IACvC,KAAK,EAAE;QACL,MAAM,EAAE,WAAW;QACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;KACpB;CACF,CAAC,CAAC;AAEH,OAAO;IACL,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,yBAAyB,QAAQ,CAAC,MAAM,sBAAsB,IAAI,EAAE;IAC7E,QAAQ,EAAE,MAAM,CAAC,EAAE;CACpB,CAAC;AACE,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;IACtD,MAAM,KAAK,CAAC;AACd,CAAC;AAMO,KAAK,CAAA;AAAC,kBAAkB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;AAAE,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAG,MAAM,EAAE,GAAG;IAC9H,GAAG,EAAC;QACM,EAAA,CAAC,UAAU,EAAE,KAAK,EAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC;QAEjE,KAAK,EAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,KAAK;QAE5D,wBAAwB;QACxB,KAAK,EAAC,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAChE,KAAK,EAAC,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAEhE,wBAAwB;QACxB,EAAE,CAAC,SAAS,EAAC,AAAD,EAAG,AAAD,IAAG,CAAC,AAAF;KAAA,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,IAAI,SAAS,GAAG,OAAO;CAAC,CAAA;AAAC,CAAC;IACtE,MAAM,IAAI,KAAK,CAAC,uBAAuB,SAAS,IAAI,OAAO,EAAE,CAAC,CAAC;AACjE,CAAC;AAED,6BAA6B;AAC7B,IAAI,YAAY,EAAE,CAAC;IACjB,MAAM,UAAU,GAAG,GAAG,QAAQ,MAAM,CAAC;IACrC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;AAC/D,CAAC;AAED,yDAAyD;AACzD,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;AAC1E,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE/C,8CAA8C;AAC9C,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAE5D,6CAA6C;AAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;IACvD,QAAQ;IACR,SAAS;IACT,OAAO;IACP,YAAY,EAAE,OAAO,GAAG,SAAS,GAAG,CAAC;IACrC,qBAAqB,EAAE,cAAc,CAAC,MAAM,GAAG,GAAG;QAChD,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QAC1C,CAAC,CAAC,cAAc;CACnB,EAAE;IACD,MAAM,EAAE,cAAc;IACtB,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,IAAI;IACb,aAAa,EAAE,CAAC,QAAQ,CAAC;CAC1B,CAAC,CAAC;AAEH,qDAAqD;AACrD,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;IACvC,KAAK,EAAE;QACL,MAAM,EAAE,WAAW;QACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;KACpB;CACF,CAAC,CAAC;AAEH,OAAO;IACL,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,wBAAwB,OAAO,GAAG,SAAS,GAAG,CAAC,WAAW,SAAS,IAAI,OAAO,GAAG;IAC1F,QAAQ,EAAE,MAAM,CAAC,EAAE;CACpB,CAAC;AACE,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;IACtD,MAAM,KAAK,CAAC;AACd,CAAC;AAMO,KAAK,CAAA;AAAC,mBAAmB,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAA;AAAE,OAAO,GAAG;IACrE,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,0BAAU;IAClB,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE;CACnE,GAAG;IACF,GAAG,EAAC;QACM,EAAA,CAAC,UAAU,EAAE,KAAK,EAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC;QAEjE,KAAK,EAAC;YACJ,QAAQ;YACR,UAAU;YACV,MAAM,GAAG,0BAAU,CAAC,OAAO;YAC3B,YAAY,GAAG,CAAC;YAChB,gBAAgB,GAAG,KAAK;YACxB,UAAU,GAAG,KAAK;YAClB,aAAa,GAAG,IAAI;SACrB,GAAG,KAAK;QAET,+BAA+B;QAC/B,KAAK,EAAC,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAE/D,qCAAqC;QACrC,GAAG,EAAC,eAAe,GAAG,EAAE;QACxB,EAAE,CAAC,UAAU;YACX,4CAA4C;YAC5C,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,CAAC;QAED,qDAAqD;;QAArD,qDAAqD;QACrD,KAAK,EAAC,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAC5C,eAAe,EACf,cAAc,EACd;YACE,MAAM;YACN,YAAY;YACZ,gBAAgB;YAChB,UAAU;YACV,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;SAClB,CACF;QAED,2DAA2D;QAC3D,GAAG,EAAC,OAAO,GAAG,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;QAExD,EAAE,CAAC,MAAM,IAAC,CAAC,AAAF;KAAA,KAAK,0BAAU,CAAC,IAAI;CAAC,CAAA;AAAC,CAAC;IAClC,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACxD,OAAO,GAAG;gBACR,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM;gBACvE,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,MAAM;gBAC1E,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM;aACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,KAAK,GAAG,CAAU,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACpF,wDAAwD;QACxD,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;AACH,CAAC;AAAM,CAAC;IACN,8CAA8C;IAC9C,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC;AAED,OAAO;IACL,IAAI;IACJ,MAAM;IACN,OAAO;CACR,CAAC;AACE,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;IACvD,MAAM,KAAK,CAAC;AACd,CAAC;AAMO,gBAAgB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAA;AAAE,KAAK,GAAG;IACxE,IAAI,EAAE,KAAK,GAAG,QAAQ,GAAG,QAAQ;IACjC,OAAO,EAAG,MAAM;IAChB,OAAO,EAAG,MAAM;IAChB,UAAU,EAAE,MAAM;CACnB,GAAG;IACF,KAAK,EAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;IACvC,KAAK,EAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;IACvC,KAAK,EAAC,OAAO,EAAE,KAAK;QAClB,IAAI,EAAE,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAClC,OAAO,CAAC,CAAC,CAAA,CAAA,CAAC,CAAC,MAAM,CAAC;QAClB,OAAO,CAAC,CAAC,CAAA,CAAA,CAAC,CAAC,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;IACrB,CAAC;CAAA,GAAE,AAAD,CAAA;AAAE,EAAE,CAAC;AAET,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACpE,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE5B,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;QACxB,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC;aAAM,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,QAAQ;gBACd,OAAO;gBACP,OAAO;gBACP,UAAU,EAAE,CAAC,GAAG,CAAC;aAClB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;AACH,CAAC;AAED,OAAO,OAAO,CAAC;AAMP,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;AAAE,CAAC;IAAC,SAAS,EAAE,MAAM,CAAC;IAAC,SAAS,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,MAAM,CAAA;AAAC,CAAC;AAAC,CAAC;IAC5F,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACpD,SAAS,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3D,SAAS,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO;QACL,SAAS;QACT,SAAS;QACT,OAAO,EAAE,SAAS,GAAG,SAAS;KAC/B,CAAC;AACJ,CAAC;AAKO,KAAK,CAAA;AAAC,mBAAmB,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAA;AAAE,OAAO,GAAG;IACrE,OAAO,EAAE,OAAO;IAChB,SAAS,EAAE,MAAM;IACjB,IAAI,EAAE,MAAM;IACZ,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAG,MAAM,CAAC,CAAC;CAClB,GAAG;IACF,GAAG,EAAC;QACM,EAAA,CAAC,UAAU;KAAC;CAAA,CAAA;AAAC,CAAC;IACxB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,CAAC;AAED,MAAM,EAAE,QAAQ,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC;AAChD,IAAI,OAAe,CAAC;AAEpB,gDAAgD;AAChD,IAAI,CAAC;IACH,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACrD,CAAC;AAAC,OAAO,KAAU,EAAE,CAAC;IACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;AAED,mBAAmB;AACnB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAClC,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;AAC/B,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAChD,MAAM,MAAM,GAAa,EAAE,CAAC;AAE5B,oBAAoB;AACpB,MAAM,MAAM,GAAG,wDAAa,QAAQ,GAAC,CAAC;AACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAE3E,yBAAyB;AACzB,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;IACf,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAC/B,CAAC;AAED,kCAAkC;AAClC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;IAC7B,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;AACjE,CAAC;AAED,gCAAgC;AAChC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,MAAM,CAAC,IAAI,CAAC,qCAAqC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,8BAA8B;AAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvC,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;AACjD,IAAI,OAAO,IAAI,SAAS,EAAE,CAAC;IACzB,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;AACnE,CAAC;AAED,gBAAgB;AAChB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;IAC3D,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;AACvD,CAAC;AAED,mFAAmF;AACnF,4EAA4E;AAE5E,OAAO;IACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;IAC5B,SAAS;IACT,IAAI;IACJ,QAAQ;IACR,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;CAC/C,CAAC;AACE,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,KAAK,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;IACvE,MAAM,KAAK,CAAC;AACd,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as path from 'path';\nimport * as fs from 'fs-extra';\nimport { randomUUID } from 'crypto';\nimport { EventEmitter } from 'events';\n// Define the missing interfaces locally since they're not exported from toolFramework\ndeclare module 'tool-framework' {\n  export interface ITerminalToolEvents {\n    on(event: string, listener: (...args: any[]) => void): this;\n    emit(event: string, ...args: any[]): boolean;\n  }\n\n  export interface IToolMemorySchema {\n    type: string;\n    source: string;\n    metadata: Record<string, unknown>;\n  }\n}\n\n// Import the interfaces we just declared\nimport type { ITerminalToolEvents, IToolMemorySchema } from 'tool-framework';\nimport { performance } from 'perf_hooks';\nimport { z } from 'zod';\n\n// Core framework imports\nimport {\n  IFileSystemManager,\n  DiffFormat,\n  DiffOptions,\n  FileEditOptions,\n  ILogger,\n  AITerminalTool,\n  InteractiveSession,\n  IAIContext,\n  IToolTerminalContext,\n  QuickFixPattern,\n  IWorkspaceKnowledge,\n  DirectoryTree\n} from './toolFramework';\n\n// Import memory types from the correct location\nimport type { IMemoryOperations, MemorySource, MemoryType } from '../memory/types';\n\n// Define missing interfaces locally since they're not exported from toolFramework\ninterface IToolOperationMemory {\n  id: string;\n  toolName: string;\n  operation: string;\n  timestamp: number;\n  input: Record<string, unknown>;\n  output?: unknown;\n  error?: {\n    message: string;\n    code?: string;\n    stack?: string;\n  };\n  metadata: {\n    source: MemorySource;\n    type: string;\n    tags?: string[];\n    workspaceContext?: string;\n    affectedFiles?: string[];\n    success: boolean;\n    importance?: number;\n    [key: string]: unknown;\n  };\n  duration: number;\n  executionId: string;\n  state: {\n    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';\n    startTime: number;\n    endTime?: number;\n    progress?: number;\n  };\n  dependencies?: {\n    requiredOperations?: string[];\n    dependentOperations?: string[];\n  };\n  metrics?: {\n    cpuUsage?: number;\n    memoryUsage?: number;\n    apiCalls?: number;\n    fileOperations?: number;\n  };\n}\n\n// Workflow engine integration\nimport { Workflow, WorkflowContext, WorkflowStep, WorkflowStepResult } from '../agents/workflows/workflowEngine';\nimport { logger } from '../logger';\n\n// Type definitions for AI operations\ntype AIAnalysisResult = {\n  suggestions: Array<{\n    type: 'optimization' | 'refactor' | 'bug' | 'security';\n    description: string;\n    codeSnippet: string;\n    recommendedChange: string;\n    confidence: number;\n  }>;\n  metrics: {\n    complexity: number;\n    maintainability: number;\n    performance: number;\n    security: number;\n  };\n};\n\ntype RefactoringResult = {\n  originalCode: string;\n  refactoredCode: string;\n  changes: Array<{\n    type: 'add' | 'remove' | 'modify';\n    line: number;\n    oldContent?: string;\n    newContent?: string;\n    reason: string;\n  }>;\n  summary: string;\n};\n\ntype CodeEditOperation = {\n  type: 'replace' | 'insert' | 'delete';\n  filePath: string;\n  range: [number, number]; // [startLine, endLine] (1-based)\n  content?: string;\n  options?: FileEditOptions;\n};\n/**\n * Input types for code editing actions using framework interfaces\n */\ninterface ReplaceLinesInput extends Partial<FileEditOptions> {\n  filePath: string;\n  startLine: number;\n  endLine: number;\n  newContent: string;\n  createBackup: boolean;\n  createIfNotExists?: boolean;\n  encoding?: BufferEncoding;\n  dryRun?: boolean;\n}\n\ninterface InsertLinesInput extends Partial<FileEditOptions> {\n  filePath: string;\n  line: number;\n  newContent: string;\n  createBackup: boolean;\n  createIfNotExists?: boolean;\n  encoding?: BufferEncoding;\n  dryRun?: boolean;\n}\n\ninterface DeleteLinesInput extends Partial<FileEditOptions> {\n  filePath: string;\n  startLine: number;\n  endLine: number;\n  createBackup: boolean;\n  dryRun?: boolean;\n}\n\ninterface GenerateDiffInput extends Partial<DiffOptions> {\n  filePath: string;\n  comparedTo?: string;\n  format: DiffFormat;\n  contextLines?: number;\n  ignoreWhitespace?: boolean;\n  ignoreCase?: boolean;\n  caseSensitive?: boolean;\n}\n\ninterface ValidateFileInput {\n  filePath: string;\n  checkSyntax?: boolean;\n}\n\n/**\n * Advanced code editing tool with AI integration and workflow support\n * Extends AITerminalTool for terminal integration and AI capabilities\n */\n// Define the operation history type for better type safety\ntype OperationHistoryEntry = {\n  operation: string;\n  filePath: string;\n  timestamp: number;\n  success: boolean;\n  error?: string;\n  metadata?: Record<string, unknown>;\n  duration?: number;\n  changes?: Array<{\n    type: 'add' | 'remove' | 'modify';\n    line: number;\n    content: string;\n  }>;\n};\n\n// Define active operation type for better type safety\ntype ActiveOperation = {\n  id: string;\n  operation: string;\n  filePath: string;\n  startTime: number;\n  status: 'pending' | 'running' | 'completed' | 'failed';\n  progress?: number;\n  error?: string;\n  changes?: Array<{\n    type: 'add' | 'remove' | 'modify';\n    line: number;\n    content: string;\n  }>;\n};\n\nexport class AdvancedCodeEditingTool extends AITerminalTool {\n  // Public readonly properties\n  public readonly name = 'AdvancedCodeEditing';\n  public readonly description = 'Advanced file editing tool for precise code modifications with automatic backups, integrity checks, and full framework capabilities';\n  public readonly version = '1.0.0';\n  public readonly category = 'Development';\n\n  // Private properties\n  private toolEvents: EventEmitter;\n  private memorySchema: IToolOperationMemory;\n  private activeOperations = new Map<string, ActiveOperation>();\n  private operationHistory: OperationHistoryEntry[] = [];\n\n  // Service dependencies\n  private logger: ILogger;\n  private fileSystem: IFileSystemManager;\n  private workspaceKnowledge: IWorkspaceKnowledge | null;\n  private memoryOperations: IMemoryOperations;\n  private aiContext: IAIContext;\n\n  constructor(\n    terminalSession: InteractiveSession,\n    aiContext: IAIContext,\n    toolContext: IToolTerminalContext,\n    services: {\n      memoryManager: IMemoryOperations;\n      fileSystem: IFileSystemManager;\n      workspace?: IWorkspaceKnowledge;\n      logger?: ILogger;\n    }\n  ) {\n    super({ terminalSession, aiContext, toolContext });\n\n    // Initialize service dependencies\n    this.logger = services.logger || console as unknown as ILogger;\n    this.fileSystem = services.fileSystem;\n    this.workspaceKnowledge = services.workspace || null;\n    this.memoryOperations = services.memoryManager;\n    this.aiContext = aiContext;\n\n    // Initialize event emitter and memory schema\n    this.toolEvents = new EventEmitter();\n    this.memorySchema = this.createMemorySchema();\n\n    // Initialize file system watchers and AI patterns\n    this.initializeFileSystemWatchers();\n    this.initializeAIPatterns();\n  }\n\n  // Implement required abstract method from AITerminalTool\n  public async executeAIOperation(context: IAIContext, input: unknown): Promise<unknown> {\n    try {\n      this.logger.info('Executing AI operation', { input });\n      // Implement actual AI operation logic here\n      return { success: true, result: 'Operation completed' };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      this.logger.error('Error in executeAIOperation', { error: errorMessage });\n      throw error;\n    }\n  }\n\n  /**\n   * Analyze a code snippet and provide optimization suggestions\n   * @param codeSnippet The code to analyze\n   * @param language The programming language of the code\n   */\n  private async analyzeCodeSnippet(\n    codeSnippet: string,\n    language: string = 'typescript',\n    context?: WorkflowContext\n  ): Promise<{\n    originalCode: string;\n    suggestedCode: string;\n    description: string;\n    complexity: number;\n    improvements: string[];\n    memoryId?: string;\n    workflowId?: string;\n  }> {\n    const startTime = performance.now();\n\n    try {\n      // Use the AI context to analyze the code\n      const analysis = await this.aiContext.analyzeCode({\n        code: codeSnippet,\n        language,\n        features: ['complexity', 'optimization', 'security']\n      });\n\n      const duration = performance.now() - startTime;\n      const metadata = {\n        duration,\n        language,\n        complexity: analysis.complexity || 0,\n        suggestions: analysis.suggestions?.length || 0\n      };\n\n      // Log the successful analysis\n      await this.logOperation('analyzeCodeSnippet', 'snippet', true, undefined);\n\n      // Store detailed analysis in memory\n      await this.memoryManager.storeMemory({\n        id: `analysis:${Date.now()}`,\n        content: JSON.stringify({\n          codeSnippet,\n          analysis,\n          metadata\n        }),\n        metadata: {\n          type: 'code_analysis',\n          source: 'ai_analysis',\n          timestamp: Date.now(),\n          ...metadata\n        }\n      });\n\n      return {\n        originalCode: codeSnippet,\n        suggestedCode: analysis.suggestedCode || codeSnippet,\n        description: analysis.summary || 'No specific issues found',\n        complexity: analysis.complexity || 0,\n        improvements: analysis.suggestions?.map(s => s.description) || []\n      };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      await this.logOperation('analyzeCodeSnippet', 'snippet', false, errorMessage);\n\n      // Return a basic analysis result in case of errors\n      return {\n        originalCode: codeSnippet,\n        suggestedCode: codeSnippet,\n        description: 'Error during analysis: ' + errorMessage,\n        complexity: 0,\n        improvements: []\n      };\n    }\n  }\n\n  private activeOperations: Map<string, {\n    id: string;\n    operation: string;\n    filePath: string;\n    startTime: number;\n    changes: Array<{\n      type: 'add' | 'remove' | 'modify';\n      line: number;\n      content: string;\n    }>;\n  }> = new Map();\n\n  private terminalEvents: ITerminalToolEvents;\n  private memorySchema: IToolMemorySchema;\n  private toolEvents: EventEmitter;\n\n  constructor(\n    terminalSession: InteractiveSession,\n    aiContext: IAIContext,\n    toolContext: IToolTerminalContext,\n    services: {\n      memoryManager: IMemoryOperations;\n      fileSystem: IFileSystemManager;\n      workspace: IWorkspaceKnowledge;\n      logger?: ILogger;\n    }\n  ) {\n    super({ terminalSession, aiContext, toolContext });\n\n    this.logger = services.logger || console as unknown as ILogger;\n    this.fileSystem = services.fileSystem;\n    this.workspaceKnowledge = services.workspace;\n    this.memoryOperations = services.memoryManager;\n    this.toolEvents = new EventEmitter();\n\n    // Initialize memory schema\n    this.memorySchema = this.createMemorySchema();\n\n    // Initialize file system watchers\n    this.initializeFileSystemWatchers();\n\n    // Initialize AI patterns\n    this.initializeAIPatterns();\n\n    // Initialize memory schema\n    this.memorySchema = {\n      type: MemoryType.CodeEdit,\n      source: MemorySource.Tool,\n      metadata: {\n        toolName: this.name,\n        version: this.version,\n        categories: ['code', 'refactoring', 'optimization']\n      }\n    };\n\n    // Initialize AI patterns and capabilities\n    this.initializeAIPatterns();\n    this.initializeFileSystemWatchers();\n\n    // Set up terminal event listeners\n    this.setupTerminalEventListeners();\n\n    this.logger.info('AdvancedCodeEditingTool initialized with full framework capabilities');\n  }\n\n  /**\n   * Initialize file system watchers for relevant file changes\n   */\n  private initializeFileSystemWatchers(): void {\n    // Watch for file changes to update workspace knowledge\n    const watcher = vscode.workspace.createFileSystemWatcher('**/*.{ts,js,jsx,tsx,json}');\n\n    watcher.onDidChange(async (uri) => {\n      this.logger.debug(`File changed: ${uri.fsPath}`);\n      await this.updateWorkspaceKnowledge(uri.fsPath);\n    });\n\n    watcher.onDidCreate(async (uri) => {\n      this.logger.debug(`File created: ${uri.fsPath}`);\n      await this.updateWorkspaceKnowledge(uri.fsPath);\n    });\n\n    watcher.onDidDelete((uri) => {\n      this.logger.debug(`File deleted: ${uri.fsPath}`);\n      this.removeFromWorkspaceKnowledge(uri.fsPath);\n    });\n  }\n\n  /**\n   * Update workspace knowledge with file changes\n   */\n  private async updateWorkspaceKnowledge(filePath: string): Promise<void> {\n    try {\n      const content = await this.fileSystem.readFile(filePath);\n      // Store file content in memory for quick access\n      await this.memoryManager.storeMemory({\n        id: `file:${filePath}`,\n        content,\n        metadata: {\n          type: 'file_content',\n          path: filePath,\n          lastModified: Date.now(),\n          source: 'file_system_watcher'\n        }\n      });\n      this.logger.debug(`Updated workspace knowledge for: ${filePath}`);\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      this.logger.error(`Failed to update workspace knowledge for ${filePath}: ${errorMessage}`, { error: errorMessage });\n    }\n  }\n\n  /**\n   * Remove file from workspace knowledge\n   */\n  private async removeFromWorkspaceKnowledge(filePath: string): Promise<void> {\n    try {\n      await this.memoryManager.deleteMemory(`file:${filePath}`);\n      this.logger.debug(`Removed from workspace knowledge: ${filePath}`);\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      this.logger.error(`Failed to remove ${filePath} from workspace knowledge: ${errorMessage}`, { error: errorMessage });\n    }\n  }\n\n  /**\n   * Initialize AI patterns for code editing\n   */\n  private initializeAIPatterns(): void {\n    try {\n      if (!this.workspaceKnowledge) {\n        this.logger.warn('Workspace knowledge not available, skipping AI patterns initialization');\n        return;\n      }\n\n      // Register quick fix patterns for common issues\n      const quickFixPatterns: QuickFixPattern[] = [\n        {\n          id: 'missing-import',\n          pattern: /Cannot find name '([^']+)'/,\n          title: 'Add missing import',\n          description: 'Add missing import statement for $1',\n          action: async (match: RegExpMatchArray) => {\n            // Implementation for adding missing imports\n            return { success: true };\n          }\n        },\n        {\n          id: 'unused-variable',\n          pattern: /'([^']+)' is declared but its value is never read/,\n          title: 'Remove unused variable',\n          description: 'Remove unused variable $1',\n          action: async (match: RegExpMatchArray) => {\n            // Implementation for removing unused variables\n            return { success: true };\n          }\n        }\n      ];\n\n      if (typeof this.workspaceKnowledge.registerPatterns === 'function') {\n        this.workspaceKnowledge.registerPatterns(quickFixPatterns);\n        this.logger.info('Registered AI patterns for code editing');\n      }\n    } catch (error) {\n      this.logger.error('Failed to initialize AI patterns', {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n  }\n\n  /**\n   * Log an operation to history and memory\n   */\n  private async logOperation(operation: string, filePath: string, success: boolean, error?: string): Promise<void> {\n    try {\n      const logEntry = {\n        operation,\n        filePath,\n        timestamp: Date.now(),\n        success,\n        error\n      };\n\n      // Here you would typically use the memory manager to store the log\n      // For now, we'll just log to the console\n      if (success) {\n        this.logger.info(`Operation completed: ${operation}`, { filePath });\n      } else {\n        this.logger.error(`Operation failed: ${operation}`, { filePath, error });\n      }\n\n      // If you have a memory manager with a store method, you could do:\n      // await this.memoryManager.store('operation-logs', logEntry);\n\n    } catch (error) {\n      this.logger.error('Failed to log operation', {\n        operation,\n        filePath,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n  }\n\n      // Ensure metadata has the correct types and required fields\n      const { metadata: existingMetadata = {} } = updatedMemory;\nconst metadata: IToolMemorySchema['metadata'] = {\n  source: 'conversation',\n  type: 'text',\n  success: true,\n  ...existingMetadata,\n  // Ensure these required fields are not overridden\n  toolName: this.name,\n  operation: updatedMemory.operation,\n  executionId: updatedMemory.executionId || `exec_${Date.now()}`\n};\n\n// Use the memory manager to update the memory\nawait this.memoryManager.addMemory({\n  content: JSON.stringify(updatedMemory.input),\n  metadata\n});\n    }\n  }\n\n  protected async _execute(actionName: string, validatedInput: unknown): Promise < { output: unknown, memoriesCreated?: string[] } > {\n  const memory = this.createOperationMemory(\n    actionName,\n    validatedInput as Record<string, unknown>\n  );\n\n  try {\n    let result: unknown;\n\n    switch(actionName) {\n        case 'replaceLines':\n    result = await this.executeReplaceLines(validatedInput as ReplaceLinesInput);\n    break;\n    case 'insertLines':\n    result = await this.executeInsertLines(validatedInput as InsertLinesInput);\n    break;\n    case 'deleteLines':\n    result = await this.executeDeleteLines(validatedInput as DeleteLinesInput);\n    break;\n    default:\n          throw new Error(`Unsupported action: ${actionName}`);\n  }\n\n      // Update memory with successful result\n      await this.updateOperationMemory(memory, {\n    output: result,\n    state: {\n      ...memory.state,\n      status: 'completed',\n      endTime: Date.now(),\n      progress: 100\n    },\n    duration: Date.now() - memory.state.startTime\n  });\n\n  return { output: result };\n\n} catch (error) {\n  // Update memory with error result for framework tracking\n  const errorMsg = error instanceof Error ? error.message : String(error);\n  await this.updateOperationMemory(memory, {\n    state: {\n      ...memory.state,\n      status: 'failed',\n      endTime: Date.now()\n    },\n    error: {\n      message: errorMsg,\n      stack: error instanceof Error ? error.stack : undefined\n    },\n    duration: Date.now() - memory.state.startTime\n  });\n\n  throw error;\n}\n  }\n\n  /**\n   * Execute a code formatting operation\n   */\n  private async executeFormatCode(input: { filePath: string; options?: any }): Promise < { success: boolean } > {\n  try {\n    this.logger.info(`Formatting code in file: ${input.filePath}`);\n    // Format the code using the file system directly since workspace.formatDocument is not available\n    const content = await this.fileSystem.readFile(input.filePath);\n    // Basic formatting - trim whitespace and ensure consistent line endings\n    const formattedContent = content\n      .split('\\n')\n      .map(line => line.trimEnd())\n      .join('\\n')\n      .replace(/\\r\\n/g, '\\n');\n\n    await this.fileSystem.writeFile(input.filePath, formattedContent);\n    return { success: true };\n  } catch(error) {\n    this.logger.error(`Failed to format code: ${error}`);\n    throw error;\n  }\n}\n\n  /**\n   * Apply a code action to a file\n   */\n  private async executeApplyCodeAction(input: { filePath: string; action: string; range?: any }): Promise < { success: boolean } > {\n  try {\n    this.logger.info(`Applying code action to file: ${input.filePath}, action: ${input.action}`);\n\n    // Read the current file content\n    const content = await this.fileSystem.readFile(input.filePath);\n    let newContent = content;\n\n    // Handle common code actions\n    switch(input.action.toLowerCase()) {\n        case 'trimwhitespace':\n  newContent = content.split('\\n').map(line => line.trimEnd()).join('\\n');\n  break;\n        case 'removetrailingnewline':\n  newContent = content.replace(/\\n+$/, '');\n  break;\n        case 'ensurenonewline':\n  newContent = content.trimEnd() + '\\n';\n  break;\n  // Add more code actions as needed\n}\n\n// Only write if content changed\nif (newContent !== content) {\n  await this.fileSystem.writeFile(input.filePath, newContent);\n}\n\nreturn { success: true };\n    } catch (error) {\n  this.logger.error(`Failed to apply code action: ${error}`);\n  throw error;\n}\n  }\n\n  /**\n   * Apply a diff to a file\n   */\n  private async executeApplyDiff(input: { filePath: string; diff: string; options?: any }): Promise < { success: boolean } > {\n  try {\n    this.logger.info(`Applying diff to file: ${input.filePath}`);\n    // Apply the diff using the file system's diff application if available\n    if(this.fileSystem && typeof this.fileSystem.applyDiff === 'function') {\n  await this.fileSystem.applyDiff(input.filePath, input.diff, input.options);\n} else {\n  // Fallback implementation\n  const content = await this.fileSystem.readFile(input.filePath);\n  const newContent = this.applyPatch(content, input.diff);\n  await this.fileSystem.writeFile(input.filePath, newContent);\n}\nreturn { success: true };\n    } catch (error) {\n  this.logger.error(`Failed to apply diff: ${error}`);\n  throw error;\n}\n  }\n\n  /**\n   * Helper method to apply a unified diff/patch to content\n   */\n  private applyPatch(originalContent: string, patch: string): string {\n  // This is a simplified implementation\n  // In a real-world scenario, you'd want to use a proper diff/patch library\n  const lines = originalContent.split('\\n');\n  const patchLines = patch.split('\\n');\n  const result: string[] = [];\n  let i = 0;\n\n  while (i < patchLines.length) {\n    const line = patchLines[i];\n    if (line.startsWith('@@')) {\n      // Parse the hunk header\n      const hunkMatch = line.match(/^@@ -\\d+(?:,\\d+)? \\+\\d+(?:,\\d+)? @@/);\n      if (hunkMatch) {\n        i++;\n        // Skip the hunk header and process the hunk\n        while (i < patchLines.length && !patchLines[i].startsWith('@@')) {\n          const hunkLine = patchLines[i];\n          if (hunkLine.startsWith('+') && !hunkLine.startsWith('+++')) {\n            result.push(hunkLine.substring(1));\n          } else if (!hunkLine.startsWith('-') && !hunkLine.startsWith('---')) {\n            result.push(hunkLine);\n          }\n          i++;\n        }\n      }\n    } else {\n      i++;\n    }\n  }\n\n  return result.join('\\n');\n}\n\n/**\n * Verify a code change using checksum\n */\nprivate async executeVerifyCodeChange(input: { filePath: string; expectedChecksum: string }): Promise < { success: boolean; matches: boolean } > {\n  try {\n    if(!this.fileSystem) throw new Error('File system not available');\n\n    const content = await this.fileSystem.readFile(input.filePath);\n    const checksum = await this.calculateChecksum(content);\n    const matches = checksum === input.expectedChecksum;\n    return { success: true, matches };\n  } catch(error) {\n    this.logger.error(`Failed to verify code change: ${error}`);\n    return { success: false, matches: false };\n  }\n}\n\n  /**\n   * Calculate a secure SHA-256 checksum for the given content\n   * @param content The content to generate checksum for\n   * @returns A hexadecimal string representing the SHA-256 hash\n   */\n  private async calculateChecksum(content: string): Promise < string > {\n  const crypto = await import('crypto');\n  return crypto\n    .createHash('sha256')\n    .update(content, 'utf8')\n    .digest('hex');\n}\n\n  /**\n   * Execute a replace lines operation\n   */\n  private async executeReplaceLines(input: ReplaceLinesInput): Promise < { success: boolean; message: string; memoryId?: string } > {\n  try {\n    if(!this.fileSystem) throw new Error('File system not available');\n\n    const { filePath, startLine, endLine, newContent, createBackup } = input;\n\n    // Read original content\n    const originalContent = await this.fileSystem.readFile(filePath);\n    const lines = originalContent ? originalContent.split('\\n') : [];\n\n    // Validate line numbers\n    if(startLine < 1 || endLine > lines.length || startLine > endLine) {\n  throw new Error(`Invalid line range: ${startLine}-${endLine}`);\n}\n\n// Create backup if requested\nif (createBackup) {\n  const backupPath = `${filePath}.bak`;\n  await this.fileSystem.writeFile(backupPath, originalContent);\n}\n\n// Replace the lines\nconst deletedLines = lines.splice(startLine - 1, endLine - startLine + 1, ...newContent.split('\\n'));\n\n// Write the modified content back to the file\nawait this.fileSystem.writeFile(filePath, lines.join('\\n'));\n\n// Create operation memory with proper typing and correct memory source/type\nconst memory = this.createOperationMemory('replaceLines', {\n  filePath,\n  startLine,\n  endLine,\n  linesReplaced: deletedLines.length,\n  newLineCount: newContent.split('\\n').length\n}, {\n  source: 'conversation',\n  type: 'text',\n  success: true,\n  affectedFiles: [filePath]\n});\n\n// Update memory with success status and proper state\nawait this.updateOperationMemory(memory, {\n  state: {\n    status: 'completed',\n    startTime: Date.now(),\n    endTime: Date.now()\n  }\n});\n\nreturn {\n  success: true,\n  message: `Successfully replaced lines ${startLine}-${endLine}`,\n  memoryId: memory.id\n};\n  } catch (error) {\n  this.logger.error(`Error in executeReplaceLines: ${error}`);\n  throw error;\n}\n}\n\n/**\n * Execute an insert lines operation\n */\nprivate async executeInsertLines(input: InsertLinesInput): Promise < { success: boolean; message: string; memoryId?: string } > {\n  try {\n    if(!this.fileSystem) throw new Error('File system not available');\n\n    const { filePath, line, newContent, createBackup } = input;\n\n    // Read original content\n    const originalContent = await this.fileSystem.readFile(filePath);\n    const lines = originalContent ? originalContent.split('\\n') : [];\n\n    // Validate line number\n    if(line < 0 || line > lines.length) {\n  throw new Error(`Invalid line number: ${line}`);\n}\n\n// Create backup if requested\nif (createBackup) {\n  const backupPath = `${filePath}.bak`;\n  await this.fileSystem.writeFile(backupPath, originalContent);\n}\n\n// Insert the new content at the specified line\nconst insertPosition = line === 0 ? 0 : line;\nconst newLines = newContent.split('\\n');\nlines.splice(insertPosition, 0, ...newLines);\n\n// Write the modified content back to the file\nawait this.fileSystem.writeFile(filePath, lines.join('\\n'));\n\n// Create operation memory with proper typing and correct memory source/type\nconst memory = this.createOperationMemory('insertLines', {\n  filePath,\n  line,\n  linesInserted: newLines.length,\n  contentPreview: newContent.length > 50 ? newContent.substring(0, 50) + '...' : newContent\n}, {\n  source: 'conversation',\n  type: 'text',\n  success: true,\n  affectedFiles: [filePath]\n});\n\n// Update memory with success status and proper state\nawait this.updateOperationMemory(memory, {\n  state: {\n    status: 'completed',\n    startTime: Date.now(),\n    endTime: Date.now()\n  }\n});\n\nreturn {\n  success: true,\n  message: `Successfully inserted ${newLines.length} lines at position ${line}`,\n  memoryId: memory.id\n};\n  } catch (error) {\n  this.logger.error(`Failed to insert lines: ${error}`);\n  throw error;\n}\n}\n\n/**\n * Execute a delete lines operation\n */\nprivate async executeDeleteLines(input: DeleteLinesInput): Promise < { success: boolean; message: string; memoryId?: string } > {\n  try {\n    if(!this.fileSystem) throw new Error('File system not available');\n\n    const { filePath, startLine, endLine, createBackup } = input;\n\n    // Read original content\n    const originalContent = await this.fileSystem.readFile(filePath);\n    const lines = originalContent ? originalContent.split('\\n') : [];\n\n    // Validate line numbers\n    if(startLine < 1 || endLine > lines.length || startLine > endLine) {\n  throw new Error(`Invalid line range: ${startLine}-${endLine}`);\n}\n\n// Create backup if requested\nif (createBackup) {\n  const backupPath = `${filePath}.bak`;\n  await this.fileSystem.writeFile(backupPath, originalContent);\n}\n\n// Remove the specified lines and capture deleted content\nconst deletedLines = lines.splice(startLine - 1, endLine - startLine + 1);\nconst deletedContent = deletedLines.join('\\n');\n\n// Write the modified content back to the file\nawait this.fileSystem.writeFile(filePath, lines.join('\\n'));\n\n// Create operation memory with proper typing\nconst memory = this.createOperationMemory('deleteLines', {\n  filePath,\n  startLine,\n  endLine,\n  linesDeleted: endLine - startLine + 1,\n  deletedContentPreview: deletedContent.length > 100\n    ? deletedContent.substring(0, 100) + '...'\n    : deletedContent\n}, {\n  source: 'conversation',\n  type: 'text',\n  success: true,\n  affectedFiles: [filePath]\n});\n\n// Update memory with success status and proper state\nawait this.updateOperationMemory(memory, {\n  state: {\n    status: 'completed',\n    startTime: Date.now(),\n    endTime: Date.now()\n  }\n});\n\nreturn {\n  success: true,\n  message: `Successfully deleted ${endLine - startLine + 1} lines (${startLine}-${endLine})`,\n  memoryId: memory.id\n};\n  } catch (error) {\n  this.logger.error(`Failed to delete lines: ${error}`);\n  throw error;\n}\n}\n\n/**\n * Generate a diff between two versions of a file or between two files\n */\nprivate async executeGenerateDiff(input: GenerateDiffInput): Promise < {\n  diff: string;\n  format: DiffFormat;\n  summary: { additions: number; deletions: number; changes: number };\n} > {\n  try {\n    if(!this.fileSystem) throw new Error('File system not available');\n\n    const {\n      filePath,\n      comparedTo,\n      format = DiffFormat.Unified,\n      contextLines = 3,\n      ignoreWhitespace = false,\n      ignoreCase = false,\n      caseSensitive = true\n    } = input;\n\n    // Get the current file content\n    const currentContent = await this.fileSystem.readFile(filePath);\n\n    // Get the content to compare against\n    let comparedContent = '';\n    if(comparedTo) {\n      // If a comparison file is provided, read it\n      comparedContent = await this.fileSystem.readFile(comparedTo);\n    }\n\n    // Generate diff using the file system's diff utility\n    const diff = await this.fileSystem.diffContent(\n      comparedContent,\n      currentContent,\n      {\n        format,\n        contextLines,\n        ignoreWhitespace,\n        ignoreCase,\n        fileHeader: true,\n        hunkHeaders: true,\n        lineNumbers: true\n      }\n    );\n\n    // For JSON format, parse the diff to get the change counts\n    let summary = { additions: 0, deletions: 0, changes: 0 };\n\n    if(format === DiffFormat.Json) {\n  try {\n    const diffData = JSON.parse(diff);\n    if (diffData.changes && Array.isArray(diffData.changes)) {\n      summary = {\n        additions: diffData.changes.filter((c: any) => c.type === 'add').length,\n        deletions: diffData.changes.filter((c: any) => c.type === 'remove').length,\n        changes: diffData.changes.length\n      };\n    }\n  } catch (e) {\n    const error = e as Error;\n    this.logger.warn('Failed to parse JSON diff for summary', { error: error.message });\n    // Fallback to analyzing the diff text for other formats\n    summary = this.analyzeDiff(diff);\n  }\n} else {\n  // For non-JSON formats, analyze the diff text\n  summary = this.analyzeDiff(diff);\n}\n\nreturn {\n  diff,\n  format,\n  summary\n};\n  } catch (error) {\n  this.logger.error(`Failed to generate diff: ${error}`);\n  throw error;\n}\n}\n\n/**\n * Generate a JSON diff between two strings\n */\nprivate generateJsonDiff(newContent: string, oldContent: string): Array < {\n  type: 'add' | 'remove' | 'change';\n  oldLine?: string;\n  newLine?: string;\n  lineNumber: number;\n} > {\n  const newLines = newContent.split('\\n');\n  const oldLines = oldContent.split('\\n');\n  const changes: Array<{\n    type: 'add' | 'remove' | 'change';\n    oldLine ?: string;\n    newLine ?: string;\n    lineNumber: number;\n  }> =[];\n\nfor (let i = 0; i < Math.max(newLines.length, oldLines.length); i++) {\n  const newLine = newLines[i];\n  const oldLine = oldLines[i];\n\n  if (newLine !== oldLine) {\n    if (newLine === undefined) {\n      changes.push({ type: 'remove', oldLine, lineNumber: i + 1 });\n    } else if (oldLine === undefined) {\n      changes.push({ type: 'add', newLine, lineNumber: i + 1 });\n    } else {\n      changes.push({\n        type: 'change',\n        oldLine,\n        newLine,\n        lineNumber: i + 1\n      });\n    }\n  }\n}\n\nreturn changes;\n}\n\n/**\n * Analyze a unified diff and return statistics\n */\nprivate analyzeDiff(diff: string): { additions: number; deletions: number; changes: number } {\n  const lines = diff.split('\\n');\n  let additions = 0;\n  let deletions = 0;\n\n  for (const line of lines) {\n    if (line.startsWith('+') && !line.startsWith('+++')) {\n      additions++;\n    } else if (line.startsWith('-') && !line.startsWith('---')) {\n      deletions++;\n    }\n  }\n\n  return {\n    additions,\n    deletions,\n    changes: additions + deletions\n  };\n}\n\n/**\n * Validate file structure and content\n */\nprivate async executeValidateFile(input: ValidateFileInput): Promise < {\n  isValid: boolean;\n  lineCount: number;\n  size: number;\n  checksum: string;\n  issues?: string[];\n} > {\n  try {\n    if(!this.fileSystem) {\n  throw new Error('File system service not available');\n}\n\nconst { filePath, checkSyntax = false } = input;\nlet content: string;\n\n// Check if file exists by attempting to read it\ntry {\n  content = await this.fileSystem.readFile(filePath);\n} catch (error: any) {\n  if (error.code === 'ENOENT') {\n    throw new Error(`File not found: ${filePath}`);\n  } else {\n    throw new Error(`File cannot be read: ${filePath}`);\n  }\n}\n\n// Basic validation\nconst lines = content.split('\\n');\nconst lineCount = lines.length;\nconst size = Buffer.byteLength(content, 'utf8');\nconst issues: string[] = [];\n\n// Generate checksum\nconst crypto = await import('crypto');\nconst checksum = crypto.createHash('sha256').update(content).digest('hex');\n\n// Check if file is empty\nif (size === 0) {\n  issues.push('File is empty');\n}\n\n// Check line endings (CRLF vs LF)\nif (content.includes('\\r\\n')) {\n  issues.push('File contains Windows-style line endings (CRLF)');\n}\n\n// Check for trailing whitespace\nlines.forEach((line, index) => {\n  if (line.endsWith(' ') || line.endsWith('\\t')) {\n    issues.push(`Trailing whitespace found on line ${index + 1}`);\n  }\n});\n\n// Check for mixed indentation\nconst hasTabs = content.includes('\\t');\nconst hasSpaces = content.match(/^ +/m) !== null;\nif (hasTabs && hasSpaces) {\n  issues.push('File contains mixed indentation (tabs and spaces)');\n}\n\n// Check for BOM\nif (content.length > 0 && content.charCodeAt(0) === 0xFEFF) {\n  issues.push('File contains a BOM (Byte Order Mark)');\n}\n\n// Note: Removed workspace-specific syntax checking since it's not in the interface\n// If syntax checking is needed, it should be implemented in a different way\n\nreturn {\n  isValid: issues.length === 0,\n  lineCount,\n  size,\n  checksum,\n  issues: issues.length > 0 ? issues : undefined\n};\n  } catch (error) {\n  this.logger.error(`Error validating file ${input.filePath}: ${error}`);\n  throw error;\n}\n}\n}\n"]}