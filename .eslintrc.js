module.exports = {
  root: true,
  env: {
    node: true,
    es2022: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint'],
  rules: {
    // Base rules
    'indent': 'off', // Disabled as it's causing too many errors
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'no-console': 'off', // Temporarily disabled
    'no-unused-vars': 'off', // Handled by @typescript-eslint/no-unused-vars
    
    // TypeScript rules
    '@typescript-eslint/explicit-function-return-type': 'off', // Temporarily disabled
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-empty-interface': 'warn',
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-var-requires': 'warn',
    '@typescript-eslint/no-non-null-assertion': 'warn',
  },
  ignorePatterns: ['**/*.js', 'node_modules', 'out', 'dist'],
};
