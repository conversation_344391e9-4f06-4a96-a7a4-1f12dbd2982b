{"version": 3, "file": "sqliteDatabase.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/databases/sqliteDatabase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,4CAAyC;AACzC,4CAA4C;AAC5C,2CAA6B;AAC7B,uCAAyB;AACzB,iDAAmC;AACnC,mCAAwC;AAExC;;GAEG;AACH,MAAa,cAAc;IACjB,EAAE,CAAuB;IACzB,QAAQ,CAAS;IAEzB;QACE,wDAAwD;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;QACtE,IAAI,CAAC,QAAQ,GAAG,IAAA,kBAAS,EAAS,iCAAiC,EAAE,WAAW,CAAC,CAAC;IACpF,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,sDAAsD;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC;gBACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC9B,eAAM,CAAC,IAAI,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAC;oBACzD,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC7C,eAAM,CAAC,IAAI,CAAC,mCAAmC,SAAS,EAAE,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,SAAS,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC3E,MAAM,IAAI,KAAK,CAAC,wCAAwC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACpH,CAAC;YAED,gBAAgB;YAChB,IAAI,CAAC,EAAE,GAAG,MAAM,IAAA,aAAI,EAAC;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,OAAO,CAAC,QAAQ;aACzB,CAAC,CAAC;YAEH,yCAAyC;YACzC,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAEjD,gBAAgB;YAChB,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;aAiBZ,CAAC,CAAC;YAET,eAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,MAA+B;QACxE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;YAC5D,CAAC;YAED,iBAAiB;YACjB,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YAEpD,oBAAoB;YACpB,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,gBAAgB;YAChB,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACf,wFAAwF,EACxF,CAAC,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CACnD,CAAC;YAEF,yBAAyB;YACzB,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAClE,MAAM,WAAW,GAAG,QAAmC,CAAC;gBACxD,IAAI,WAAW,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBACxD,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,IAAgB,EAAE,CAAC;wBAC/C,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACf,mEAAmE,EACnE,CAAC,EAAE,EAAE,GAAG,CAAC,CACV,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACvE,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,EAAU;QACnD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;YAC5D,CAAC;YAED,aAAa;YACb,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,qCAAqC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE9E,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,WAAW;YACX,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,iDAAiD,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAExF,iBAAiB;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAE7C,uBAAuB;YACvB,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAErC,OAAO;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,QAAQ;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzF,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU,EAAE,MAA+B;QACvF,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;YAC5D,CAAC;YAED,iBAAiB;YACjB,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YAEhD,gBAAgB;YAChB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAC9B,2EAA2E,EAC3E,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CACnD,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,uBAAuB;YACvB,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,6CAA6C,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAEvE,6BAA6B;YAC7B,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAClE,MAAM,WAAW,GAAG,QAAmC,CAAC;gBACxD,IAAI,WAAW,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBACxD,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,IAAgB,EAAE,CAAC;wBAC/C,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACf,wDAAwD,EACxD,CAAC,EAAE,EAAE,GAAG,CAAC,CACV,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACzE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,kBAAkB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1F,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU;QACtD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;YAC5D,CAAC;YAED,6DAA6D;YAC7D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,mCAAmC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE5E,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,oBAAoB,UAAU,EAAE,CAAC,CAAC;YAC3E,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5F,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,KAA8B,EAAE,KAAc;QAC1F,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;YAC5D,CAAC;YAED,cAAc;YACd,IAAI,GAAG,GAAG,qCAAqC,CAAC;YAChD,MAAM,MAAM,GAAwB,EAAE,CAAC;YAEvC,sDAAsD;YACtD,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;YAC5C,IAAI,YAAY,IAAI,OAAO,YAAY,KAAK,QAAQ,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;gBAC9E,MAAM,OAAO,GAAG,YAAuC,CAAC;gBACxD,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAgB,CAAC;oBACtC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpB,GAAG,IAAI;gDAC6B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;;+DAEvB,CAAC;wBACpD,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;wBACpE,OAAO,KAAK,CAAC,eAAe,CAAC,CAAC;oBAChC,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,GAAG,IAAI,YAAY,CAAC;YACtB,CAAC;YAED,8DAA8D;YAC9D,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjD,IAAI,CAAC,KAAK;oBAAE,SAAS;gBAErB,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;oBACpB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;wBAChD,MAAM,OAAO,GAAG,KAAgC,CAAC;wBACjD,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;4BACxC,GAAG,IAAI,uBAAuB,CAAC;4BAC/B,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;oBACvC,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;oBACnE,IAAI,WAAW,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC;wBAC5E,GAAG,IAAI,oCAAoC,WAAW,QAAQ,CAAC;wBAC/D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACrB,CAAC;gBACH,CAAC;qBAAM,IAAI,GAAG,KAAK,WAAW,EAAE,CAAC;oBAC/B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;wBAChD,MAAM,YAAY,GAAG,KAAgC,CAAC;wBACtD,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;4BAC1C,GAAG,IAAI,uBAAuB,CAAC;4BAC/B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;wBACjC,CAAC;wBACD,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;4BAC1C,GAAG,IAAI,uBAAuB,CAAC;4BAC/B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;wBACjC,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC;oBACvG,GAAG,IAAI,UAAU,GAAG,MAAM,CAAC;oBAC3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,eAAe;YACf,GAAG,IAAI,4BAA4B,CAAC;YAEpC,iCAAiC;YACjC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBAC3C,GAAG,IAAI,UAAU,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,8CAA8C;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEhD,yCAAyC;YACzC,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACnD,8DACE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAC5C,GAAG,EACH,GAAG,SAAS,CACb,CAAC,CAAC,CAAC,EAAE,CAAC;YAEP,2DAA2D;YAC3D,MAAM,cAAc,GAAG,IAAI,GAAG,EAAoB,CAAC;YACnD,KAAK,MAAM,GAAG,IAAI,IAAiD,EAAE,CAAC;gBACpE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBACvC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBACxC,CAAC;gBACD,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACrD,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,2DAA2D;YAC3D,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC3B,IAAI,QAAiC,CAAC;gBACtC,IAAI,CAAC;oBACH,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACzC,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,eAAM,CAAC,IAAI,CAAC,uCAAuC,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;oBACpE,QAAQ,GAAG,EAAE,CAAC;gBAChB,CAAC;gBACD,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;gBAEpD,OAAO;oBACL,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,QAAQ;iBACT,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC,CAAC,mDAAmD;QAClE,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,eAAe,CAAC,UAAkB;QAC7C,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;YAC5D,CAAC;YAED,mEAAmE;YACnE,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAE1C,eAAM,CAAC,IAAI,CAAC,sBAAsB,UAAU,EAAE,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA1XD,wCA0XC", "sourcesContent": ["import { IDatabase } from '../../types';\nimport { logger } from '../../../logger';\nimport { getConfig } from '../../../config';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport * as sqlite3 from 'sqlite3';\nimport { open, Database } from 'sqlite';\n\n/**\n * SQLite database implementation\n */\nexport class SQLiteDatabase implements IDatabase {\n  private db: Database | undefined;\n  private filename: string;\n\n  constructor() {\n    // Use absolute path to avoid any path resolution issues\n    const defaultPath = path.join(process.cwd(), '.codessa', 'memory.db');\n    this.filename = getConfig<string>('memory.database.sqlite.filename', defaultPath);\n  }\n\n  /**\n     * Initialize the database\n     */\n  public async initialize(): Promise<void> {\n    try {\n      // Ensure directory exists with verbose error handling\n      const directory = path.dirname(this.filename);\n      try {\n        if (!fs.existsSync(directory)) {\n          logger.info(`Creating database directory: ${directory}`);\n          fs.mkdirSync(directory, { recursive: true });\n          logger.info(`Successfully created directory: ${directory}`);\n        }\n      } catch (error) {\n        logger.error(`Failed to create database directory '${directory}':`, error);\n        throw new Error(`Failed to create database directory: ${error instanceof Error ? error.message : String(error)}`);\n      }\n            \n      // Open database\n      this.db = await open({\n        filename: this.filename,\n        driver: sqlite3.Database\n      });\n            \n      // Enable WAL mode for better performance\n      await this.db.exec('PRAGMA journal_mode = WAL;');\n            \n      // Create tables\n      await this.db.exec(`\n                CREATE TABLE IF NOT EXISTS memories (\n                    id TEXT PRIMARY KEY,\n                    content TEXT NOT NULL,\n                    timestamp INTEGER NOT NULL,\n                    metadata TEXT NOT NULL\n                );\n                \n                CREATE TABLE IF NOT EXISTS memory_tags (\n                    memory_id TEXT NOT NULL,\n                    tag TEXT NOT NULL,\n                    PRIMARY KEY (memory_id, tag),\n                    FOREIGN KEY (memory_id) REFERENCES memories(id) ON DELETE CASCADE\n                );\n                \n                CREATE INDEX IF NOT EXISTS idx_memories_timestamp ON memories(timestamp);\n                CREATE INDEX IF NOT EXISTS idx_memory_tags_tag ON memory_tags(tag);\n            `);\n            \n      logger.info(`SQLite database initialized successfully at ${this.filename}`);\n    } catch (error) {\n      logger.error('Failed to initialize SQLite database:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Add a record\n     */\n  public async addRecord(collection: string, record: Record<string, unknown>): Promise<string> {\n    if (!this.db) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      if (collection !== 'memories') {\n        throw new Error(`Collection ${collection} not supported`);\n      }\n            \n      // Extract fields\n      const { id, content, timestamp, metadata } = record;\n\n      // Type guard for id\n      if (typeof id !== 'string') {\n        throw new Error('Record ID must be a string');\n      }\n\n      // Insert memory\n      await this.db.run(\n        'INSERT OR REPLACE INTO memories (id, content, timestamp, metadata) VALUES (?, ?, ?, ?)',\n        [id, content, timestamp, JSON.stringify(metadata)]\n      );\n\n      // Insert tags if present\n      if (metadata && typeof metadata === 'object' && metadata !== null) {\n        const metadataObj = metadata as Record<string, unknown>;\n        if (metadataObj.tags && Array.isArray(metadataObj.tags)) {\n          for (const tag of metadataObj.tags as string[]) {\n            await this.db.run(\n              'INSERT OR REPLACE INTO memory_tags (memory_id, tag) VALUES (?, ?)',\n              [id, tag]\n            );\n          }\n        }\n      }\n\n      logger.debug(`Added record with ID ${id} to collection ${collection}`);\n      return id;\n    } catch (error) {\n      logger.error(`Failed to add record to collection ${collection}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n     * Get a record by ID\n     */\n  public async getRecord(collection: string, id: string): Promise<Record<string, unknown> | undefined> {\n    if (!this.db) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      if (collection !== 'memories') {\n        throw new Error(`Collection ${collection} not supported`);\n      }\n            \n      // Get memory\n      const memory = await this.db.get('SELECT * FROM memories WHERE id = ?', [id]);\n            \n      if (!memory) {\n        return undefined;\n      }\n            \n      // Get tags\n      const tags = await this.db.all('SELECT tag FROM memory_tags WHERE memory_id = ?', [id]);\n            \n      // Parse metadata\n      const metadata = JSON.parse(memory.metadata);\n            \n      // Add tags to metadata\n      metadata.tags = tags.map(t => t.tag);\n            \n      return {\n        id: memory.id,\n        content: memory.content,\n        timestamp: memory.timestamp,\n        metadata\n      };\n    } catch (error) {\n      logger.error(`Failed to get record with ID ${id} from collection ${collection}:`, error);\n      return undefined;\n    }\n  }\n\n  /**\n     * Update a record\n     */\n  public async updateRecord(collection: string, id: string, record: Record<string, unknown>): Promise<boolean> {\n    if (!this.db) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      if (collection !== 'memories') {\n        throw new Error(`Collection ${collection} not supported`);\n      }\n            \n      // Extract fields\n      const { content, timestamp, metadata } = record;\n            \n      // Update memory\n      const result = await this.db.run(\n        'UPDATE memories SET content = ?, timestamp = ?, metadata = ? WHERE id = ?',\n        [content, timestamp, JSON.stringify(metadata), id]\n      );\n            \n      if (result.changes === 0) {\n        return false;\n      }\n            \n      // Delete existing tags\n      await this.db.run('DELETE FROM memory_tags WHERE memory_id = ?', [id]);\n\n      // Insert new tags if present\n      if (metadata && typeof metadata === 'object' && metadata !== null) {\n        const metadataObj = metadata as Record<string, unknown>;\n        if (metadataObj.tags && Array.isArray(metadataObj.tags)) {\n          for (const tag of metadataObj.tags as string[]) {\n            await this.db.run(\n              'INSERT INTO memory_tags (memory_id, tag) VALUES (?, ?)',\n              [id, tag]\n            );\n          }\n        }\n      }\n            \n      logger.debug(`Updated record with ID ${id} in collection ${collection}`);\n      return true;\n    } catch (error) {\n      logger.error(`Failed to update record with ID ${id} in collection ${collection}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Delete a record\n     */\n  public async deleteRecord(collection: string, id: string): Promise<boolean> {\n    if (!this.db) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      if (collection !== 'memories') {\n        throw new Error(`Collection ${collection} not supported`);\n      }\n            \n      // Delete memory (tags will be deleted via ON DELETE CASCADE)\n      const result = await this.db.run('DELETE FROM memories WHERE id = ?', [id]);\n            \n      logger.debug(`Deleted record with ID ${id} from collection ${collection}`);\n      return result.changes > 0;\n    } catch (error) {\n      logger.error(`Failed to delete record with ID ${id} from collection ${collection}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Query records\n     */\n  public async queryRecords(collection: string, query: Record<string, unknown>, limit?: number): Promise<Record<string, unknown>[]> {\n    if (!this.db) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      if (collection !== 'memories') {\n        throw new Error(`Collection ${collection} not supported`);\n      }\n            \n      // Build query\n      let sql = 'SELECT DISTINCT m.* FROM memories m';\n      const params: (string | number)[] = [];\n            \n      // Handle tag filters - using proper parameter binding\n      const metadataTags = query['metadata.tags'];\n      if (metadataTags && typeof metadataTags === 'object' && metadataTags !== null) {\n        const tagsObj = metadataTags as Record<string, unknown>;\n        if (tagsObj.$all && Array.isArray(tagsObj.$all)) {\n          const tags = tagsObj.$all as string[];\n          if (tags.length > 0) {\n            sql += ` JOIN memory_tags t ON m.id = t.memory_id\n                              WHERE t.tag IN (${Array(tags.length).fill('?').join(',')})\n                              GROUP BY m.id\n                              HAVING COUNT(DISTINCT t.tag) = ?`;\n            params.push(...tags.map((tag: string) => String(tag)), tags.length);\n            delete query['metadata.tags'];\n          }\n        }\n      } else {\n        sql += ' WHERE 1=1';\n      }\n            \n      // Handle other filters with proper type checking and escaping\n      for (const [key, value] of Object.entries(query)) {\n        if (!value) continue;\n\n        if (key === '$text') {\n          if (typeof value === 'object' && value !== null) {\n            const textObj = value as Record<string, unknown>;\n            if (typeof textObj.$search === 'string') {\n              sql += ' AND m.content LIKE ?';\n              params.push(`%${textObj.$search}%`);\n            }\n          }\n        } else if (key.startsWith('metadata.')) {\n          const metadataKey = key.substring(9).replace(/[^a-zA-Z0-9_]/g, '');\n          if (metadataKey && (typeof value === 'string' || typeof value === 'number')) {\n            sql += ` AND json_extract(m.metadata, '$.${metadataKey}') = ?`;\n            params.push(value);\n          }\n        } else if (key === 'timestamp') {\n          if (typeof value === 'object' && value !== null) {\n            const timestampObj = value as Record<string, unknown>;\n            if (typeof timestampObj.$gte === 'number') {\n              sql += ' AND m.timestamp >= ?';\n              params.push(timestampObj.$gte);\n            }\n            if (typeof timestampObj.$lte === 'number') {\n              sql += ' AND m.timestamp <= ?';\n              params.push(timestampObj.$lte);\n            }\n          }\n        } else if (['id', 'content'].includes(key) && (typeof value === 'string' || typeof value === 'number')) {\n          sql += ` AND m.${key} = ?`;\n          params.push(value);\n        }\n      }\n            \n      // Add order by\n      sql += ' ORDER BY m.timestamp DESC';\n            \n      // Add limit with type validation\n      if (typeof limit === 'number' && limit > 0) {\n        sql += ' LIMIT ?';\n        params.push(limit);\n      }\n            \n      // Execute query with proper parameter binding\n      const memories = await this.db.all(sql, params);\n            \n      // Get tags for memories more efficiently\n      const memoryIds = memories.map(m => m.id);\n      const tags = memoryIds.length > 0 ? await this.db.all(\n        `SELECT memory_id, tag FROM memory_tags WHERE memory_id IN (${\n          Array(memoryIds.length).fill('?').join(',')\n        })`,\n        ...memoryIds\n      ) : [];\n            \n      // Group tags by memory ID using Map for better performance\n      const tagsByMemoryId = new Map<string, string[]>();\n      for (const tag of tags as Array<{ memory_id: string; tag: string }>) {\n        if (!tagsByMemoryId.has(tag.memory_id)) {\n          tagsByMemoryId.set(tag.memory_id, []);\n        }\n        const memoryTags = tagsByMemoryId.get(tag.memory_id);\n        if (memoryTags) {\n          memoryTags.push(tag.tag);\n        }\n      }\n            \n      // Build result with proper error handling for JSON parsing\n      return memories.map(memory => {\n        let metadata: Record<string, unknown>;\n        try {\n          metadata = JSON.parse(memory.metadata);\n        } catch (e) {\n          logger.warn(`Invalid JSON in metadata for memory ${memory.id}:`, e);\n          metadata = {};\n        }\n        metadata.tags = tagsByMemoryId.get(memory.id) || [];\n                \n        return {\n          id: memory.id,\n          content: memory.content,\n          timestamp: memory.timestamp,\n          metadata\n        };\n      });\n    } catch (error) {\n      logger.error(`Failed to query records from collection ${collection}:`, error);\n      throw error; // Re-throw to allow proper error handling upstream\n    }\n  }\n\n  /**\n     * Clear all records in a collection\n     */\n  public async clearCollection(collection: string): Promise<void> {\n    if (!this.db) {\n      throw new Error('Database not initialized');\n    }\n\n    try {\n      if (collection !== 'memories') {\n        throw new Error(`Collection ${collection} not supported`);\n      }\n            \n      // Delete all memories (tags will be deleted via ON DELETE CASCADE)\n      await this.db.run('DELETE FROM memories');\n            \n      logger.info(`Cleared collection ${collection}`);\n    } catch (error) {\n      logger.error(`Failed to clear collection ${collection}:`, error);\n      throw error;\n    }\n  }\n}\n"]}