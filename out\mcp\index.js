"use strict";
/**
 * Model Context Protocol (MCP) Integration
 *
 * This module provides integration with the Model Context Protocol (MCP),
 * which is a standard for exchanging context between different AI models
 * and tools.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.mcpManager = exports.MCPManager = void 0;
var mcpManager_1 = require("./mcpManager");
Object.defineProperty(exports, "MCPManager", { enumerable: true, get: function () { return mcpManager_1.MCPManager; } });
Object.defineProperty(exports, "mcpManager", { enumerable: true, get: function () { return mcpManager_1.mcpManager; } });
//# sourceMappingURL=index.js.map