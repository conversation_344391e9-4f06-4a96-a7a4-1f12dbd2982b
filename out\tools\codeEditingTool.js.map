{"version": 3, "file": "codeEditingTool.js", "sourceRoot": "", "sources": ["../../src/tools/codeEditingTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,8BAA8B;AAC9B,+CAAiC;AACjC,2CAA6B;AAC7B,mCAAoC;AAGpC,6BAAwB;AAExB,kFAAkF;AAClF,+EAA+E;AAC/E,uCAAuM;AAEvM,gFAAgF;AAChF,mDAoByB;AAKzB,kEAAkE;AAElE,MAAM,cAAc,GAAG,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC,CAAC;AACvF,MAAM,gBAAgB,GAAG,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;AAC1F,MAAM,kBAAkB,GAAG,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,mDAAmD,CAAC,CAAC;AAE/H,MAAM,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,QAAQ,EAAE,cAAc;IACxB,SAAS,EAAE,gBAAgB;IAC3B,OAAO,EAAE,gBAAgB;IACzB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;IACzF,YAAY,EAAE,kBAAkB;CACnC,CAAC,CAAC;AAEH,MAAM,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,QAAQ,EAAE,cAAc;IACxB,IAAI,EAAE,gBAAgB;IACtB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;IAC9D,YAAY,EAAE,kBAAkB;CACnC,CAAC,CAAC;AAEH,MAAM,yBAAyB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sEAAsE,CAAC;IACxG,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC;IAC5E,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;IAC7E,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,uDAAuD,CAAC;CACnH,CAAC,CAAC;AAEH,MAAM,0BAA0B,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,uEAAuE,CAAC;IACxG,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4DAA4D,CAAC;CACrG,CAAC,CAAC;AAEH,MAAM,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,QAAQ,EAAE,cAAc;IACxB,IAAI,EAAE,gBAAgB;IACtB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;IACtG,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,8DAA8D,CAAC;CACtG,CAAC,CAAC;AAEH,MAAM,4BAA4B,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,QAAQ,EAAE,cAAc;IACxB,SAAS,EAAE,gBAAgB;IAC3B,OAAO,EAAE,gBAAgB;IACzB,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,6CAA6C,CAAC;CAC7F,CAAC,CAAC;AAEH,MAAM,2BAA2B,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,QAAQ,EAAE,cAAc;IACxB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;IAC7E,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8CAA8C,CAAC;CACpF,CAAC,CAAC;AAEH,MAAM,qCAAqC,GAAG,OAAC,CAAC,MAAM,CAAC;IACnD,QAAQ,EAAE,cAAc;IACxB,aAAa,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,sFAAsF,CAAC;IACrJ,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,sEAAsE,CAAC;CACtI,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,OAAC,CAAC,MAAM,CAAC;IACnC,QAAQ,EAAE,cAAc;IACxB,mBAAmB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,uEAAuE,CAAC;IACjH,SAAS,EAAE,gBAAgB,CAAC,QAAQ,EAAE;IACtC,OAAO,EAAE,gBAAgB,CAAC,QAAQ,EAAE;CACvC,CAAC,CAAC;AAEH,MAAM,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,QAAQ,EAAE,cAAc;IACxB,qBAAqB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wEAAwE,CAAC;IACpH,IAAI,EAAE,gBAAgB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qEAAqE,CAAC;CACpH,CAAC,CAAC;AAEH,MAAM,gCAAgC,GAAG,OAAC,CAAC,MAAM,CAAC;IAC9C,kBAAkB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,gFAAgF,CAAC;CACtJ,CAAC,CAAC;AAEH,MAAM,8BAA8B,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+CAA+C,CAAC;IACjF,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,0CAA0C,CAAC;IACtF,eAAe,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,2CAA2C,CAAC;CAC/G,CAAC,CAAC;AAaH,MAAM,kBAAkB;IACA;IAA0B;IAA9C,YAAoB,OAAgB,EAAU,MAAe;QAAzC,YAAO,GAAP,OAAO,CAAS;QAAU,WAAM,GAAN,MAAM,CAAS;IAAI,CAAC;IAE1D,oBAAoB,CAAC,QAAgB;QACzC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,uDAAuD,QAAQ,EAAE,CAAC,CAAC;QACpG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,IAAY,EAAE,MAAc,EAAE,OAAe;QACrF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,QAAQ,IAAI,IAAI,IAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,CAAC;QACnG,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,6BAA6B,CAAC,IAAI,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7F,MAAM,UAAU,GAAG,UAAU,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;QAEtF,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3B,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC1C,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,SAAiB,EAAE,OAAe,EAAE,eAAuB;QACxG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,SAAS,IAAI,OAAO,OAAO,QAAQ,iBAAiB,eAAe,GAAG,CAAC,CAAC;QAC3H,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,UAAU,GAAG,UAAU,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACrE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAEjG,MAAM,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,mBAAmB,EAAE,IAAI,UAAU,CAAC;QACzE,MAAM,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QAErD,MAAM,MAAM,GAAG,kBAAO,CAAC,KAAK,CACxB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC9D,CAAC;QAEF,MAAM,WAAW,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACjJ,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;QACzH,IAAI,CAAC,aAAa;YAAE,MAAM,IAAI,KAAK,CAAC,qFAAqF,CAAC,CAAC;QAE3H,MAAM,KAAK,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;QAC1L,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAE/E,KAAK,CAAC,YAAY,EAAE,CAAC;QACrB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;QAE7F,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAClD,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAAgB;QACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,QAAQ,EAAE,CAAC,CAAC;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACvD,UAAU,CAAC,eAAe,EAAE,CAAC;QAC7B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,SAAiB,EAAE,OAAe,EAAE,UAAkB;QACjG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,UAAU,QAAQ,QAAQ,EAAE,CAAC,CAAC;QACtF,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,6BAA6B,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACzF,MAAM,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,6BAA6B,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACjF,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAE1D,MAAM,SAAS,GAAG,eAAe,CAAC,sBAAsB,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QACjH,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,UAAU,CAAC,CAAC;QAC7E,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,UAAU,+CAA+C,CAAC,CAAC;QAE3G,SAAS,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAC3G,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAEO,WAAW,CAAC,YAAoB;QACpC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE,YAAY,CAAC,CAAC;IAChG,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC;QAC/E,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC1B,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IACjF,CAAC;CACJ;AAED,MAAM,iBAAiB;IACC;IAApB,YAAoB,MAAe;QAAf,WAAM,GAAN,MAAM,CAAS;IAAI,CAAC;IAEhC,KAAK,CAAC,MAAM,CAAC,QAAgB;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3C,OAAO,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,IAAY,EAAE,MAAc,EAAE,OAAe;QACrF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,IAAI,IAAI,IAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,CAAC;QACpG,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3D,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAuB,sCAAsC,EAAE,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACrJ,IAAI,CAAC,aAAa;YAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,CAAC,UAAU,8BAA8B,CAAC,CAAC;QACxH,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAChD,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACjC,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IACnG,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,SAAiB,EAAE,OAAe,EAAE,eAAuB;QACxG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,SAAS,IAAI,OAAO,OAAO,QAAQ,EAAE,CAAC,CAAC;QACtG,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;QACvG,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAsB,kCAAkC,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAClI,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAClG,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,CAAC,UAAU,4EAA4E,CAAC,CAAC;QACtL,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACjC,OAAO,EAAE,eAAe,EAAE,yCAAyC,QAAQ,mCAAmC,EAAE,CAAC;IACrH,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAAgB;QACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,QAAQ,EAAE,CAAC,CAAC;QACtE,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAuB,4BAA4B,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,wBAAwB,CAAC,CAAC;QACvK,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,CAAC,UAAU,wCAAwC,CAAC,CAAC;QAClH,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,SAAiB,EAAE,OAAe,EAAE,UAAkB;QACjG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,UAAU,QAAQ,QAAQ,EAAE,CAAC,CAAC;QAClF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;QACvG,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAsB,kCAAkC,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAClI,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,UAAU,CAAC,CAAC;QACnG,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,UAAU,0CAA0C,CAAC,CAAC;QAC/H,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IACrC,CAAC;IAEO,WAAW,CAAC,YAAoB;QACpC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE,YAAY,CAAC,CAAC;IAChG,CAAC;CACJ;AAED,MAAM,sBAAsB;IAChB,UAAU,CAAqB;IAC/B,WAAW,CAAoB;IAEvC,YAAY,OAAgB,EAAE,MAAe;QACzC,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1D,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QAC5C,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9G,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACzD,QAAQ,GAAG,CAAC,UAAU,EAAE,CAAC;YACrB,KAAK,YAAY,CAAC;YAClB,KAAK,iBAAiB,CAAC;YACvB,KAAK,YAAY,CAAC;YAClB,KAAK,iBAAiB;gBAClB,OAAO,IAAI,CAAC,UAAU,CAAC;YAC3B;gBACI,OAAO,IAAI,CAAC,WAAW,CAAC;QAChC,CAAC;IACL,CAAC;CACJ;AAED,aAAa;AAEb,MAAa,uBAAwB,SAAQ,8BAAc;IACvC,IAAI,GAAG,qBAAqB,CAAC;IAC7B,WAAW,GAAG,0KAA0K,CAAC;IACzL,OAAO,GAAG,OAAO,CAAC;IAClB,QAAQ,GAAG,aAAa,CAAC;IAEzB,OAAO,GAAmD;QACtE,0BAA0B,EAAE;YACxB,WAAW,EAAE,6EAA6E;YAC1F,WAAW,EAAE,qCAAqC;YAClD,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SAC/H;QACD,YAAY,EAAE;YACV,WAAW,EAAE,2FAA2F;YACxG,WAAW,EAAE,uBAAuB;YACpC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SAC9F;QACD,WAAW,EAAE;YACT,WAAW,EAAE,yFAAyF;YACtG,WAAW,EAAE,sBAAsB;YACnC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SAC9F;QACD,cAAc,EAAE;YACZ,WAAW,EAAE,4EAA4E;YACzF,WAAW,EAAE,yBAAyB;YACtC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SACnI;QACD,YAAY,EAAE;YACV,WAAW,EAAE,+HAA+H;YAC5I,WAAW,EAAE,uBAAuB;YACpC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SAC7G;QACD,iBAAiB,EAAE;YACf,WAAW,EAAE,iIAAiI;YAC9I,WAAW,EAAE,4BAA4B;YACzC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SAC9F;QACD,gBAAgB,EAAE;YACd,WAAW,EAAE,+GAA+G;YAC5H,WAAW,EAAE,2BAA2B;YACxC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SAC7G;QACD,UAAU,EAAE;YACR,WAAW,EAAE,4FAA4F;YACzG,WAAW,EAAE,qBAAqB;YAClC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SAChH;QACD,YAAY,EAAE;YACV,WAAW,EAAE,0GAA0G;YACvH,WAAW,EAAE,uBAAuB;YACpC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SACpG;QACD,sBAAsB,EAAE;YACpB,WAAW,EAAE,uIAAuI;YACpJ,WAAW,EAAE,0BAA0B;YACvC,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SACnG;QACD,qBAAqB,EAAE;YACnB,WAAW,EAAE,uFAAuF;YACpG,WAAW,EAAE,gCAAgC;YAC7C,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SAC9E;QACD,mBAAmB,EAAE;YACjB,WAAW,EAAE,2FAA2F;YACxG,WAAW,EAAE,8BAA8B;YAC3C,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC;SACpG;KACJ,CAAC;IAEe,MAAM,CAAU;IAChB,UAAU,CAAqB;IAC/B,kBAAkB,CAAsB;IACxC,gBAAgB,CAAoB;IACpC,sBAAsB,CAAyB;IAEhE,YACI,QAQC;QAED,KAAK,CAAC,EAAE,eAAe,EAAE,QAAQ,CAAC,eAAe,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;QACvH,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,SAAS,CAAC;QAC7C,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC;QAC/C,MAAM,SAAS,GAAG,IAAI,kBAAO,CAAC,EAAE,qBAAqB,EAAE,KAAK,EAAE,CAAC,CAAC;QAChE,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC;QAChF,SAAS,CAAC,qBAAqB,CAAC,GAAG,aAAa,uBAAuB,CAAC,CAAC;QACzE,IAAI,CAAC,sBAAsB,GAAG,IAAI,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEjF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,gEAAgE,CAAC,CAAC;IACrH,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,UAAkB,EAAE,KAAc,EAAE,OAA0B;QAC9E,MAAM,WAAW,GAAG,IAAA,mBAAU,GAAE,CAAC;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE,WAAW,UAAU,cAAc,EAAE,CAAC;YAC/D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1D,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;QACtF,CAAC;QAED,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,KAAgC,CAAC,CAAC;QAElG,IAAI,CAAC;YACD,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtG,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACpD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,WAAW,EAAE,WAAW,UAAU,GAAG,CAAC,CAAC;gBACnH,IAAI,CAAC,OAAO;oBAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnF,CAAC;YAED,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAC7F,MAAM,eAAe,GAAG,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAE7D,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC;YAClG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;YAE5D,OAAO;gBACH,WAAW;gBACX,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,UAAU;gBACV,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,eAAe;gBACvB,eAAe,EAAE,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC;aAChE,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1H,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,UAAU,WAAW,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;YAC1F,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;YAC/F,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;YAC5D,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;QACpG,CAAC;IACL,CAAC;IAES,KAAK,CAAC,QAAQ,CAAC,UAAkB,EAAE,cAAuB,EAAE,OAA0B;QAC5F,uEAAuE;QACvE,QAAQ,UAAU,EAAE,CAAC;YACjB,KAAK,4BAA4B;gBAC7B,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,iCAAiC,CAAC,cAAuE,EAAE,OAAO,CAAC,EAAE,CAAC;YACtJ,KAAK,cAAc;gBACf,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAyD,CAAC,EAAE,CAAC;YACjH,KAAK,aAAa;gBACd,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAwD,CAAC,EAAE,CAAC;YAC/G,KAAK,gBAAgB;gBACjB,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAA2D,CAAC,EAAE,CAAC;YACrH,KAAK,cAAc;gBACf,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAyD,CAAC,EAAE,CAAC;YACjH,KAAK,mBAAmB;gBACpB,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,wBAAwB,CAAC,cAA8D,CAAC,EAAE,CAAC;YAC3H,KAAK,kBAAkB;gBACnB,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAA6D,CAAC,EAAE,CAAC;YACzH,KAAK,YAAY;gBACb,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAuD,EAAE,OAAO,CAAC,EAAE,CAAC;YACtH,KAAK,cAAc;gBACf,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAyD,EAAE,OAAO,CAAC,EAAE,CAAC;YAC1H,KAAK,wBAAwB;gBACzB,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,sBAAsB,CAAC,cAA4D,CAAC,EAAE,CAAC;YACvH,KAAK,uBAAuB;gBACxB,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,4BAA4B,CAAC,cAAkE,CAAC,EAAE,CAAC;YACnI,KAAK,qBAAqB;gBACtB,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,0BAA0B,CAAC,cAAgE,CAAC,EAAE,CAAC;YAC/H;gBACI,MAAM,IAAI,KAAK,CAAC,WAAW,UAAU,0BAA0B,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,UAAkB;QACpC,4EAA4E;QAC5E,IAAI,CAAC,uBAAuB,EAAE,qBAAqB,EAAE,4BAA4B,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACtG,OAAO,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAClE,CAAC;QACD,IAAI,CAAC,cAAc,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,YAAY,EAAE,cAAc,EAAE,wBAAwB,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1L,OAAO,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC9B,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,OAAmB,EAAE,KAAyB;QAC1E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,EAAE,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QACnG,MAAM,MAAM,GAA8B,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACjF,OAAO,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE;YACxC,iBAAiB,EAAE,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC,KAAK;YAC7D,UAAU,EAAE,KAAK;SACpB,CAAC,CAAC;IACP,CAAC;IAEM,gBAAgB;QACnB,OAAO,WAAW,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO;mBACjC,IAAI,CAAC,WAAW;;;;EAIjC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACrG,CAAC;IAED,oEAAoE;IAE5D,KAAK,CAAC,iCAAiC,CAAC,KAA4D,EAAE,OAA0B;QACpI,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,4BAA4B,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACnK,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,IAAI,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpE,MAAM,eAAe,GAAG,cAAc,CAAC;QAEvC,sEAAsE;QACtE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7E,IAAI,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QACnE,IAAI,gBAAgB,KAAK,cAAc,EAAE,CAAC;YACtC,UAAU,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACzD,cAAc,GAAG,gBAAgB,CAAC;QACtC,CAAC;QAED,6GAA6G;QAC7G,IAAI,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wDAAwD,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3F,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,kCAAkC;YACnG,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACtF,MAAM,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC9K,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAChE,UAAU,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QACvF,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,GAAG,GAAG,CAAU,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,KAAK,CAAC,QAAQ,gCAAgC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACpH,UAAU,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QACpF,CAAC;QAED,0DAA0D;QAC1D,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACrB,IAAI,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACrF,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,6GAA6G;gBAC7G,MAAM,QAAQ,CAAC,cAAc,CAAC,cAAc,KAAK,CAAC,QAAQ,QAAQ,CAAC,CAAC;gBACpE,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAChE,UAAU,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,GAAG,GAAG,CAAU,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAChF,UAAU,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC;QAED,sDAAsD;QACtD,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;YACzF,MAAM,iBAAiB,GAAG;;;;;;;;;;;cAWxB,cAAc;gBACZ,CAAC;YACL,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAC;YAC1G,UAAU,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEhE,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;QACjG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,2CAA2C,KAAK,CAAC,QAAQ,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IAC1I,CAAC;IAGO,KAAK,CAAC,mBAAmB,CAAC,KAA8C;QAC5E,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC;QACrH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACtF,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAE3G,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;QACpG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IACtE,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAmD;QACtF,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAC3J,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACtF,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QAEpI,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC;QACtG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,sCAAsC,KAAK,CAAC,QAAQ,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IACzH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAkD;QACpF,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;QACtJ,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACtF,IAAI,CAAC,CAAC,QAAQ,YAAY,kBAAkB,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;QACtG,CAAC;QAED,MAAM,UAAU,GAAI,QAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC,QAAQ,CAAe,CAAC;QACxF,MAAM,SAAS,GAAG,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAM,IAAI,GAAG,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,gBAAK,CAAC,OAAO,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,aAAa,KAAK,CAAC,YAAY,uBAAuB,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,gBAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC;YACpC,IAAI,EAAE,KAAK,CAAC,YAAY;YACxB,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE;YACpC,UAAU,EAAE,eAAe,KAAK,CAAC,YAAY,GAAG;SACnD,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC;YACpC,IAAI,EAAE,KAAK,CAAC,YAAY;YACxB,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/D,UAAU,EAAE,QAAQ,KAAK,CAAC,YAAY,WAAW;SACpD,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAChD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;YAC/B,IAAI,eAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,IAAI,eAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC,OAAO,EAAE,KAAK,qBAAU,CAAC,WAAW,EAAE,CAAC;gBACvK,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,QAAQ,MAAM,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAC/G,CAAC;iBAAM,CAAC;gBACJ,GAAG,CAAC,eAAe,CAAC,QAAQ,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACpD,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAG,MAAO,QAAgB,CAAC,iBAAiB,EAAE,CAAC;QAClE,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;QACpG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IACtE,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAiD;QAClF,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,wBAAwB,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;QACxI,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACxF,IAAI,CAAC,CAAC,QAAQ,YAAY,kBAAkB,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,2FAA2F,CAAC,CAAC;QACjH,CAAC;QAED,MAAM,UAAU,GAAI,QAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC,UAAU,CAAe,CAAC;QAC1F,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACvC,MAAM,aAAa,GAAG,MAAO,QAAgB,CAAC,iBAAiB,EAAE,CAAC;QAElE,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC;QAC1C,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;QACnG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAA8C;QAC5E,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAChH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC/D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,KAAK,CAAC,YAAY;YAAE,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1F,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QACxG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAElE,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACzE,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,kCAAkC,KAAK,CAAC,QAAQ,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IACrH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAA6C;QAC1E,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC/G,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC/D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,KAAK,CAAC,YAAY;YAAE,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1F,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAElE,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACzE,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAoC,KAAK,CAAC,QAAQ,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IACvH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAAgD;QAChF,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;QAC9G,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAClF,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CACpD,QAAQ,EACR,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EACtE,KAAK,CAAC,cAAc,CACvB,CAAC;YACF,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACnB,aAAa,EAAE,CAAC;gBAChB,iBAAiB,IAAI,YAAY,CAAC;YACtC,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GAAG,EAAE,aAAa,EAAE,iBAAiB,EAAE,CAAC;QACpD,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;QACjF,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IAClE,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAA4C,EAAE,OAA0B;QACpG,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;QACxI,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACvE,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAE9F,MAAM,MAAM,GAAG;qBACF,KAAK,CAAC,QAAQ;wBACX,KAAK,CAAC,mBAAmB;;;;;;UAMvC,cAAc;YACZ,CAAC;QAEL,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAChG,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,iBAAiB,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAE7G,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9E,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,0BAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAE5G,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAC3F,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,0BAA0B,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IAClG,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAA8C,EAAE,OAA0B;QACxG,MAAM,MAAM,GAAG,IAAA,yCAAyB,EAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;QAE5I,MAAM,MAAM,GAAG;wBACC,KAAK,CAAC,qBAAqB;;;SAG1C,CAAC;QAEF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC5F,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;YAC3F,UAAU,EAAE,aAAa;YACzB,YAAY,EAAE,KAAK,EAAE,wCAAwC;SAChE,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,IAAA,yCAAyB,EAAC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;QACpG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAkB,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC;IACtE,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,KAAuD;QAC9F,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,MAAM,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;QAEhE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,uBAAuB,EAAE,CAAC;QAEnF,MAAM,MAAM,GAAG;UACb,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,kFAAkF,CAAC,CAAC,CAAC,EAAE;iBAC3G,IAAI,EAAE,CAAC;QAChB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACrF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,KAAqD;QAC1F,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,MAAM,GAAG,GAAG,OAAO,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACxI,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAElD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,0BAA0B,GAAG,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;IAC/F,CAAC;IACD,aAAa;IAEb,yCAAyC;IAEjC,sBAAsB,CAAC,IAAY,EAAE,GAAyB;QAClE,MAAM,WAAW,GAAG,GAAG,CAAC,WAA+B,CAAC;QACxD,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE;YAC/D,MAAM,MAAM,GAAG,GAAmB,CAAC;YACnC,OAAO,eAAe,GAAG,SAAU,MAAM,CAAC,IAAY,CAAC,QAAQ,QAAQ,MAAM,CAAC,WAAW,IAAI,iBAAiB,EAAE,CAAC;QACrH,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,OAAO,WAAW,IAAI,wBAAwB,GAAG,CAAC,WAAW,kBAAkB,KAAK,EAAE,CAAC;IAC3F,CAAC;IAEO,gBAAgB,CAAC,OAAe,EAAE,SAAkB,EAAE,OAAgB;QAC1E,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO;YAAE,OAAO,OAAO,CAAC;QAC3C,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxE,CAAC;IAEO,gBAAgB,CAAC,eAAuB,EAAE,UAAkB,EAAE,SAAkB,EAAE,OAAgB;QACtG,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO;YAAE,OAAO,UAAU,CAAC,CAAC,iCAAiC;QAChF,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1C,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAChF,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAEO,sBAAsB,CAAC,IAAY;QACvC,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBACzC,IAAI,CAAC,SAAS,EAAE,CAAC;oBACb,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBAC9B,SAAS,GAAG,IAAI,CAAC;oBACjB,KAAK,GAAG,CAAC,CAAC;oBACV,CAAC,EAAE,CAAC;gBACR,CAAC;qBAAM,CAAC;oBACJ,KAAK,EAAE,CAAC;oBACR,iEAAiE;oBACjE,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBAChC,CAAC,EAAE,CAAC;gBACR,CAAC;YACL,CAAC;iBAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAChD,IAAI,SAAS,EAAE,CAAC;oBACZ,KAAK,EAAE,CAAC;oBACR,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;wBACd,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBAC9B,SAAS,GAAG,KAAK,CAAC;oBACtB,CAAC;yBAAM,CAAC;wBACJ,oCAAoC;wBACpC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBACpC,CAAC;oBACD,CAAC,EAAE,CAAC;gBACR,CAAC;qBAAM,CAAC;oBACJ,iDAAiD;oBACjD,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBAChC,CAAC,EAAE,CAAC;gBACR,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QACD,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC;CAGJ;AA1iBD,0DA0iBC", "sourcesContent": ["// Import core Node.js modules\r\nimport * as vscode from 'vscode';\r\nimport * as path from 'path';\r\nimport { randomUUID } from 'crypto';\r\nimport { EventEmitter } from 'events';\r\nimport { performance } from 'perf_hooks';\r\nimport { z } from 'zod';\r\n\r\n// Import the ts-morph library for deep AST manipulation in TypeScript/JavaScript.\r\n// This is a production dependency for any serious structural refactoring tool.\r\nimport { Project, SourceFile, SyntaxKind, Node, FunctionDeclaration, ClassDeclaration, Scope, Writers, WriterFunction, PropertyDeclaration, Identifier, VariableStatement, Statement } from 'ts-morph';\r\n\r\n// Import core framework contracts, classes, and functions from toolFramework.ts\r\nimport {\r\n    AITerminalTool,\r\n    IFileSystemManager,\r\n    DiffFormat,\r\n    DiffOptions,\r\n    FileEditOptions,\r\n    ILogger,\r\n    IWorkspaceKnowledge,\r\n    ITool,\r\n    ToolActionDefinition,\r\n    ToolInvokeOptions,\r\n    ToolResult,\r\n    ToolPermission,\r\n    InteractiveSession,\r\n    IAIContext,\r\n    IToolTerminalContext,\r\n    IToolOperationMemory,\r\n    createToolOperationMemory,\r\n    updateToolOperationMemory,\r\n    LanguageModelPromptPart\r\n} from './toolFramework';\r\n\r\n// Import memory types from the correct location\r\nimport type { IMemoryOperations, MemorySource, MemoryType } from '../memory/types';\r\n\r\n// #region --- Zod Schemas for Rigorous Tool Action Validation ---\r\n\r\nconst FilePathSchema = z.string().describe(\"The workspace-relative path to the file.\");\r\nconst LineNumberSchema = z.number().int().positive().describe(\"The 1-based line number.\");\r\nconst CreateBackupSchema = z.boolean().optional().default(false).describe(\"If true, creates a .bak file before modification.\");\r\n\r\nconst ReplaceLinesInputSchema = z.object({\r\n    filePath: FilePathSchema,\r\n    startLine: LineNumberSchema,\r\n    endLine: LineNumberSchema,\r\n    newContent: z.string().describe(\"The new content to substitute for the specified lines.\"),\r\n    createBackup: CreateBackupSchema,\r\n});\r\n\r\nconst InsertLinesInputSchema = z.object({\r\n    filePath: FilePathSchema,\r\n    line: LineNumberSchema,\r\n    newContent: z.string().describe(\"The content to be inserted.\"),\r\n    createBackup: CreateBackupSchema,\r\n});\r\n\r\nconst FindAndReplaceInputSchema = z.object({\r\n    globPattern: z.string().describe(\"A glob pattern to find files to search within (e.g., 'src/**/*.ts').\"),\r\n    findPattern: z.string().describe(\"The text or regex pattern to search for.\"),\r\n    replacePattern: z.string().describe(\"The text or regex replacement pattern.\"),\r\n    isRegex: z.boolean().optional().default(false).describe(\"Indicates if the findPattern is a regular expression.\"),\r\n});\r\n\r\nconst MoveAndRefactorInputSchema = z.object({\r\n    sourcePath: z.string().describe(\"The current workspace-relative path of the file or directory to move.\"),\r\n    destinationPath: z.string().describe(\"The new workspace-relative path for the file or directory.\"),\r\n});\r\n\r\nconst RenameSymbolInputSchema = z.object({\r\n    filePath: FilePathSchema,\r\n    line: LineNumberSchema,\r\n    column: z.number().int().positive().describe(\"The 1-based column number where the symbol is located.\"),\r\n    newName: z.string().min(1).describe(\"The new name for the symbol. All references will be updated.\"),\r\n});\r\n\r\nconst ExtractToFunctionInputSchema = z.object({\r\n    filePath: FilePathSchema,\r\n    startLine: LineNumberSchema,\r\n    endLine: LineNumberSchema,\r\n    newFunctionName: z.string().min(1).describe(\"The name of the new function to be created.\"),\r\n});\r\n\r\nconst EncapsulateFieldInputSchema = z.object({\r\n    filePath: FilePathSchema,\r\n    className: z.string().describe(\"The name of the class containing the field.\"),\r\n    propertyName: z.string().describe(\"The name of the public field to encapsulate.\"),\r\n});\r\n\r\nconst UntangleAndRestructureFileInputSchema = z.object({\r\n    filePath: FilePathSchema,\r\n    aiRestructure: z.boolean().optional().default(false).describe(\"If true, after cleaning syntax, an AI will attempt to logically reorder code blocks.\"),\r\n    runLinterFix: z.boolean().optional().default(true).describe(\"If true, attempts to run the project's configured linter with --fix.\"),\r\n});\r\n\r\nconst AIRefactorInputSchema = z.object({\r\n    filePath: FilePathSchema,\r\n    refactorInstruction: z.string().describe(\"A detailed, natural language instruction for the desired refactoring.\"),\r\n    startLine: LineNumberSchema.optional(),\r\n    endLine: LineNumberSchema.optional(),\r\n});\r\n\r\nconst GenerateCodeInputSchema = z.object({\r\n    filePath: FilePathSchema,\r\n    generationInstruction: z.string().describe(\"A detailed, natural language instruction for the code to be generated.\"),\r\n    line: LineNumberSchema.optional().describe(\"Optional line number for insertion. If omitted, appends to the end.\"),\r\n});\r\n\r\nconst GenerateCommitMessageInputSchema = z.object({\r\n    conventionalCommit: z.boolean().optional().default(true).describe(\"If true, generates a message following the Conventional Commits specification.\"),\r\n});\r\n\r\nconst ManageNpmDependencyInputSchema = z.object({\r\n    packageName: z.string().describe(\"The name of the npm package to add or remove.\"),\r\n    action: z.enum(['add', 'remove']).describe(\"Whether to add or remove the dependency.\"),\r\n    isDevDependency: z.boolean().optional().default(false).describe(\"True if this is a development dependency.\"),\r\n});\r\n\r\n// #endregion\r\n\r\n// #region --- Language-Agnostic Strategy Abstractions ---\r\n\r\ninterface ILanguageStrategy {\r\n    renameSymbol(filePath: string, line: number, column: number, newName: string): Promise<string[]>;\r\n    extractToFunction(filePath: string, startLine: number, endLine: number, newFunctionName: string): Promise<{ newFunctionCode: string }>;\r\n    organizeImports(filePath: string): Promise<void>;\r\n    applyCodeAction(filePath: string, startLine: number, endLine: number, actionName: string): Promise<void>;\r\n}\r\n\r\nclass TypeScriptStrategy implements ILanguageStrategy {\r\n    constructor(private project: Project, private logger: ILogger) { }\r\n\r\n    private getSourceFileOrThrow(filePath: string): SourceFile {\r\n        const absolutePath = this.resolvePath(filePath);\r\n        const sourceFile = this.project.getSourceFile(absolutePath);\r\n        if (!sourceFile) throw new Error(`TypeScript strategy could not find file in project: ${filePath}`);\r\n        return sourceFile;\r\n    }\r\n\r\n    public async renameSymbol(filePath: string, line: number, column: number, newName: string): Promise<string[]> {\r\n        this.logger.debug(`TS Strategy: Renaming symbol at ${filePath}:${line}:${column} to '${newName}'`);\r\n        const sourceFile = this.getSourceFileOrThrow(filePath);\r\n        const position = sourceFile.compilerNode.getPositionOfLineAndCharacter(line - 1, column - 1);\r\n        const renameNode = sourceFile.getDescendantAtPos(position);\r\n        if (!renameNode) throw new Error(`No symbol found at ${filePath}:${line}:${column}.`);\r\n\r\n        renameNode.rename(newName);\r\n        return await this.saveModifiedFiles();\r\n    }\r\n\r\n    public async extractToFunction(filePath: string, startLine: number, endLine: number, newFunctionName: string): Promise<{ newFunctionCode: string }> {\r\n        this.logger.debug(`TS Strategy: Extracting lines ${startLine}-${endLine} of ${filePath} to function '${newFunctionName}'`);\r\n        const sourceFile = this.getSourceFileOrThrow(filePath);\r\n        const statements = sourceFile.getStatementsInRan(startLine, endLine);\r\n        if (statements.length === 0) throw new Error(\"No code statements found in the specified range.\");\r\n\r\n        const insertionPoint = statements[0].getParentSyntaxList() || sourceFile;\r\n        const insertionIndex = statements[0].getChildIndex();\r\n\r\n        const writer = Writers.union(\r\n            (w) => w.write(statements.map(s => s.getText()).join('\\n')),\r\n        );\r\n\r\n        const refactoring = sourceFile.getLanguageService().getApplicableRefactors(statements[0].getStart(), statements[statements.length - 1].getEnd());\r\n        const extractAction = refactoring.find(r => r.name === \"Extract Symbol\")?.actions.find(a => a.name.includes(\"function\"));\r\n        if (!extractAction) throw new Error(\"Extraction to function is not an available refactoring for the selected code range.\");\r\n\r\n        const edits = sourceFile.getLanguageService().getEditsForRefactor(statements[0].getStart(), statements[statements.length - 1].getEnd(), extractAction.name, { newName: newFunctionName });\r\n        if (!edits) throw new Error(\"Could not get edits for extraction refactoring.\");\r\n\r\n        edits.applyChanges();\r\n        await this.saveModifiedFiles();\r\n\r\n        const newFunc = sourceFile.getFunction(newFunctionName);\r\n        if (!newFunc) throw new Error(\"Failed to find the newly created function after extraction.\");\r\n\r\n        return { newFunctionCode: newFunc.getText() };\r\n    }\r\n\r\n    public async organizeImports(filePath: string): Promise<void> {\r\n        this.logger.debug(`TS Strategy: Organizing imports for ${filePath}`);\r\n        const sourceFile = this.getSourceFileOrThrow(filePath);\r\n        sourceFile.organizeImports();\r\n        await this.saveModifiedFiles();\r\n    }\r\n\r\n    public async applyCodeAction(filePath: string, startLine: number, endLine: number, actionName: string): Promise<void> {\r\n        this.logger.debug(`TS Strategy: Applying code action '${actionName}' to ${filePath}`);\r\n        const sourceFile = this.getSourceFileOrThrow(filePath);\r\n        const startPos = sourceFile.compilerNode.getPositionOfLineAndCharacter(startLine - 1, 0);\r\n        const endPos = sourceFile.compilerNode.getPositionOfLineAndCharacter(endLine, 0);\r\n        const languageService = this.project.getLanguageService();\r\n\r\n        const codeFixes = languageService.getCodeFixesAtPosition(sourceFile.getFilePath(), startPos, endPos, [], {}, {});\r\n        const targetFix = codeFixes.find(fix => fix.getDescription() === actionName);\r\n        if (!targetFix) throw new Error(`Code action '${actionName}' could not be found for the specified range.`);\r\n\r\n        targetFix.getChanges().forEach(change => change.getSourceFile().applyTextChanges(change.getTextChanges()));\r\n        await this.saveModifiedFiles();\r\n    }\r\n\r\n    private resolvePath(relativePath: string): string {\r\n        return path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', relativePath);\r\n    }\r\n\r\n    private async saveModifiedFiles(): Promise<string[]> {\r\n        const dirtyFiles = this.project.getSourceFiles().filter(sf => sf.isModified());\r\n        await this.project.save();\r\n        return dirtyFiles.map(f => vscode.workspace.asRelativePath(f.getFilePath()));\r\n    }\r\n}\r\n\r\nclass VSCodeLSPStrategy implements ILanguageStrategy {\r\n    constructor(private logger: ILogger) { }\r\n\r\n    private async getDoc(filePath: string): Promise<vscode.TextDocument> {\r\n        const absPath = this.resolvePath(filePath);\r\n        return await vscode.workspace.openTextDocument(absPath);\r\n    }\r\n\r\n    public async renameSymbol(filePath: string, line: number, column: number, newName: string): Promise<string[]> {\r\n        this.logger.debug(`LSP Strategy: Renaming symbol at ${filePath}:${line}:${column} to '${newName}'`);\r\n        const doc = await this.getDoc(filePath);\r\n        const position = new vscode.Position(line - 1, column - 1);\r\n        const workspaceEdit = await vscode.commands.executeCommand<vscode.WorkspaceEdit>('vscode.executeDocumentRenameProvider', doc.uri, position, newName);\r\n        if (!workspaceEdit) throw new Error(`The active language extension for '${doc.languageId}' does not support renaming.`);\r\n        await vscode.workspace.applyEdit(workspaceEdit);\r\n        await vscode.workspace.saveAll();\r\n        return Array.from(workspaceEdit.entries(), ([uri, _]) => vscode.workspace.asRelativePath(uri));\r\n    }\r\n\r\n    public async extractToFunction(filePath: string, startLine: number, endLine: number, newFunctionName: string): Promise<{ newFunctionCode: string }> {\r\n        this.logger.debug(`LSP Strategy: Attempting to extract lines ${startLine}-${endLine} of ${filePath}`);\r\n        const doc = await this.getDoc(filePath);\r\n        const range = new vscode.Range(new vscode.Position(startLine - 1, 0), new vscode.Position(endLine, 0));\r\n        const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>('vscode.executeCodeActionProvider', doc.uri, range);\r\n        const extractAction = codeActions.find(action => action.kind?.value.includes('refactor.extract'));\r\n        if (!extractAction || !extractAction.edit) throw new Error(`The language extension for '${doc.languageId}' does not provide an 'Extract to function' action for the selected range.`);\r\n        await vscode.workspace.applyEdit(extractAction.edit);\r\n        await vscode.workspace.saveAll();\r\n        return { newFunctionCode: `// Function extracted successfully in ${filePath}. Review the file for the result.` };\r\n    }\r\n\r\n    public async organizeImports(filePath: string): Promise<void> {\r\n        this.logger.debug(`LSP Strategy: Organizing imports for ${filePath}`);\r\n        const doc = await this.getDoc(filePath);\r\n        const edit = await vscode.commands.executeCommand<vscode.WorkspaceEdit>('_executeCodeActionProvider', doc.uri, new vscode.Range(0, 0, 0, 0), 'source.organizeImports');\r\n        if (!edit) throw new Error(`The language extension for '${doc.languageId}' does not support organizing imports.`);\r\n        await vscode.workspace.applyEdit(edit);\r\n        await vscode.workspace.saveAll();\r\n    }\r\n\r\n    public async applyCodeAction(filePath: string, startLine: number, endLine: number, actionName: string): Promise<void> {\r\n        this.logger.debug(`LSP Strategy: Applying action '${actionName}' to ${filePath}`);\r\n        const doc = await this.getDoc(filePath);\r\n        const range = new vscode.Range(new vscode.Position(startLine - 1, 0), new vscode.Position(endLine, 0));\r\n        const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>('vscode.executeCodeActionProvider', doc.uri, range);\r\n        const targetAction = codeActions.find(a => a.title === actionName || a.kind?.value === actionName);\r\n        if (!targetAction || !targetAction.edit) throw new Error(`Code action '${actionName}' not found or does not provide an edit.`);\r\n        await vscode.workspace.applyEdit(targetAction.edit);\r\n        await vscode.workspace.saveAll();\r\n    }\r\n\r\n    private resolvePath(relativePath: string): string {\r\n        return path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', relativePath);\r\n    }\r\n}\r\n\r\nclass LanguageServiceFactory {\r\n    private tsStrategy: TypeScriptStrategy;\r\n    private lspStrategy: VSCodeLSPStrategy;\r\n\r\n    constructor(project: Project, logger: ILogger) {\r\n        this.tsStrategy = new TypeScriptStrategy(project, logger);\r\n        this.lspStrategy = new VSCodeLSPStrategy(logger);\r\n    }\r\n\r\n    public async getStrategyForFile(filePath: string): Promise<ILanguageStrategy> {\r\n        const uri = vscode.Uri.file(path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath));\r\n        const doc = await vscode.workspace.openTextDocument(uri);\r\n        switch (doc.languageId) {\r\n            case 'typescript':\r\n            case 'typescriptreact':\r\n            case 'javascript':\r\n            case 'javascriptreact':\r\n                return this.tsStrategy;\r\n            default:\r\n                return this.lspStrategy;\r\n        }\r\n    }\r\n}\r\n\r\n// #endregion\r\n\r\nexport class AdvancedCodeEditingTool extends AITerminalTool implements ITool {\r\n    public readonly name = 'AdvancedCodeEditing';\r\n    public readonly description = 'Performs advanced, structural code editing, refactoring, and analysis using Abstract Syntax Trees (AST), AI, and workspace-wide operations for any programming language.';\r\n    public readonly version = '3.0.0';\r\n    public readonly category = 'Development';\r\n\r\n    public readonly actions: Readonly<Record<string, ToolActionDefinition>> = {\r\n        untangleAndRestructureFile: {\r\n            description: \"Fixes syntax, cleans up, and logically restructures a convoluted code file.\",\r\n            inputSchema: UntangleAndRestructureFileInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), message: z.string(), stepsTaken: z.array(z.string()), memoryId: z.string() })\r\n        },\r\n        replaceLines: {\r\n            description: 'Replaces a range of lines in a single file with new content. Use for simple text changes.',\r\n            inputSchema: ReplaceLinesInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), message: z.string(), memoryId: z.string() })\r\n        },\r\n        insertLines: {\r\n            description: 'Inserts new content at a specific line in a single file. Use for simple text additions.',\r\n            inputSchema: InsertLinesInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), message: z.string(), memoryId: z.string() })\r\n        },\r\n        findAndReplace: {\r\n            description: 'Performs a workspace-wide search and replace using text or regex patterns.',\r\n            inputSchema: FindAndReplaceInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), filesModified: z.number(), totalReplacements: z.number(), memoryId: z.string() })\r\n        },\r\n        renameSymbol: {\r\n            description: 'Safely renames a variable, function, class, or interface and all its references across the entire workspace for any language.',\r\n            inputSchema: RenameSymbolInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), filesModified: z.array(z.string()), memoryId: z.string() })\r\n        },\r\n        extractToFunction: {\r\n            description: 'Extracts a block of code into a new function, automatically determining parameters and return values (for supported languages).',\r\n            inputSchema: ExtractToFunctionInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), message: z.string(), memoryId: z.string() })\r\n        },\r\n        encapsulateField: {\r\n            description: 'Makes a public class field private and creates getter and setter methods for it (TypeScript/JavaScript only).',\r\n            inputSchema: EncapsulateFieldInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), filesModified: z.array(z.string()), memoryId: z.string() })\r\n        },\r\n        aiRefactor: {\r\n            description: 'Uses an AI model to perform a complex refactoring based on a natural language instruction.',\r\n            inputSchema: AIRefactorInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), message: z.string(), diff: z.string(), memoryId: z.string() })\r\n        },\r\n        generateCode: {\r\n            description: 'Uses an AI model to generate a new piece of code (e.g., class, function, test case) from an instruction.',\r\n            inputSchema: GenerateCodeInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), generatedCode: z.string(), memoryId: z.string() })\r\n        },\r\n        moveAndRefactorImports: {\r\n            description: 'Moves a file or directory and automatically updates all relative import statements across the workspace (TypeScript/JavaScript only).',\r\n            inputSchema: MoveAndRefactorInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), filesUpdated: z.number(), memoryId: z.string() })\r\n        },\r\n        generateCommitMessage: {\r\n            description: 'Analyzes staged git changes and generates a descriptive, conventional commit message.',\r\n            inputSchema: GenerateCommitMessageInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), commitMessage: z.string() })\r\n        },\r\n        manageNpmDependency: {\r\n            description: 'Adds or removes an npm dependency, updating package.json and running the install command.',\r\n            inputSchema: ManageNpmDependencyInputSchema,\r\n            outputSchema: z.object({ success: z.boolean(), message: z.string(), terminalOutput: z.string() })\r\n        },\r\n    };\r\n\r\n    private readonly logger: ILogger;\r\n    private readonly fileSystem: IFileSystemManager;\r\n    private readonly workspaceKnowledge: IWorkspaceKnowledge;\r\n    private readonly memoryOperations: IMemoryOperations;\r\n    private readonly languageServiceFactory: LanguageServiceFactory;\r\n\r\n    constructor(\r\n        services: {\r\n            terminalSession: InteractiveSession;\r\n            aiContext: IAIContext;\r\n            toolContext: IToolTerminalContext;\r\n            memoryManager: IMemoryOperations;\r\n            fileSystem: IFileSystemManager;\r\n            workspace: IWorkspaceKnowledge;\r\n            logger: ILogger;\r\n        }\r\n    ) {\r\n        super({ terminalSession: services.terminalSession, aiContext: services.aiContext, toolContext: services.toolContext });\r\n        this.logger = services.logger;\r\n        this.fileSystem = services.fileSystem;\r\n        this.workspaceKnowledge = services.workspace;\r\n        this.memoryOperations = services.memoryManager;\r\n        const tsProject = new Project({ useInMemoryFileSystem: false });\r\n        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '.';\r\n        tsProject.addSourceFilesAtPaths(`${workspaceRoot}/**/*.{ts,tsx,js,jsx}`);\r\n        this.languageServiceFactory = new LanguageServiceFactory(tsProject, this.logger);\r\n\r\n        this.logger.info(`[${this.name} v${this.version}] Tool initialized. Language-agnostic capabilities are active.`);\r\n    }\r\n\r\n    public async invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult> {\r\n        const executionId = randomUUID();\r\n        const actionDef = this.actions[actionName];\r\n\r\n        if (!actionDef) {\r\n            const error = { message: `Action '${actionName}' not found.` };\r\n            this.logger.error(error.message, { toolName: this.name });\r\n            return { executionId, toolName: this.name, actionName, status: 'failure', error };\r\n        }\r\n\r\n        const memory = createToolOperationMemory(this.name, actionName, input as Record<string, unknown>);\r\n\r\n        try {\r\n            options.cancellationToken.onCancellationRequested(() => { throw new Error('Operation cancelled.'); });\r\n\r\n            const permissions = this.getPermissions(actionName);\r\n            if (permissions.length > 0) {\r\n                const granted = await options.services.permissionManager.requestPermissions(permissions, `Action '${actionName}'`);\r\n                if (!granted) throw new Error(`Permissions denied: ${permissions.join(', ')}`);\r\n            }\r\n\r\n            const validatedInput = actionDef.inputSchema.parse(input);\r\n            const { output, memoriesCreated } = await this._execute(actionName, validatedInput, options);\r\n            const validatedOutput = actionDef.outputSchema.parse(output);\r\n\r\n            const finalMemory = updateToolOperationMemory(memory, { success: true, output: validatedOutput });\r\n            await this.memoryOperations.storeMemory(finalMemory as any);\r\n\r\n            return {\r\n                executionId,\r\n                toolName: this.name,\r\n                actionName,\r\n                status: 'success',\r\n                output: validatedOutput,\r\n                memoriesCreated: [...(memoriesCreated || []), finalMemory.id]\r\n            };\r\n        } catch (error) {\r\n            const errorDetails = error instanceof Error ? { message: error.message, stack: error.stack } : { message: String(error) };\r\n            this.logger.error(`Action '${actionName}' failed.`, { executionId, error: errorDetails });\r\n            const finalMemory = updateToolOperationMemory(memory, { success: false, error: errorDetails });\r\n            await this.memoryOperations.storeMemory(finalMemory as any);\r\n            return { executionId, toolName: this.name, actionName, status: 'failure', error: errorDetails };\r\n        }\r\n    }\r\n\r\n    protected async _execute(actionName: string, validatedInput: unknown, options: ToolInvokeOptions): Promise<{ output: unknown; memoriesCreated?: string[] }> {\r\n        // The core logic now delegates to specific, fully-implemented methods.\r\n        switch (actionName) {\r\n            case 'untangleAndRestructureFile':\r\n                return { output: await this.executeUntangleAndRestructureFile(validatedInput as z.infer<typeof UntangleAndRestructureFileInputSchema>, options) };\r\n            case 'replaceLines':\r\n                return { output: await this.executeReplaceLines(validatedInput as z.infer<typeof ReplaceLinesInputSchema>) };\r\n            case 'insertLines':\r\n                return { output: await this.executeInsertLines(validatedInput as z.infer<typeof InsertLinesInputSchema>) };\r\n            case 'findAndReplace':\r\n                return { output: await this.executeFindAndReplace(validatedInput as z.infer<typeof FindAndReplaceInputSchema>) };\r\n            case 'renameSymbol':\r\n                return { output: await this.executeRenameSymbol(validatedInput as z.infer<typeof RenameSymbolInputSchema>) };\r\n            case 'extractToFunction':\r\n                return { output: await this.executeExtractToFunction(validatedInput as z.infer<typeof ExtractToFunctionInputSchema>) };\r\n            case 'encapsulateField':\r\n                return { output: await this.executeEncapsulateField(validatedInput as z.infer<typeof EncapsulateFieldInputSchema>) };\r\n            case 'aiRefactor':\r\n                return { output: await this.executeAiRefactor(validatedInput as z.infer<typeof AIRefactorInputSchema>, options) };\r\n            case 'generateCode':\r\n                return { output: await this.executeGenerateCode(validatedInput as z.infer<typeof GenerateCodeInputSchema>, options) };\r\n            case 'moveAndRefactorImports':\r\n                return { output: await this.executeMoveAndRefactor(validatedInput as z.infer<typeof MoveAndRefactorInputSchema>) };\r\n            case 'generateCommitMessage':\r\n                return { output: await this.executeGenerateCommitMessage(validatedInput as z.infer<typeof GenerateCommitMessageInputSchema>) };\r\n            case 'manageNpmDependency':\r\n                return { output: await this.executeManageNpmDependency(validatedInput as z.infer<typeof ManageNpmDependencyInputSchema>) };\r\n            default:\r\n                throw new Error(`Action '${actionName}' has no implementation.`);\r\n        }\r\n    }\r\n\r\n    public getPermissions(actionName: string): readonly ToolPermission[] {\r\n        // Permissions are defined based on the action's requirements on the system.\r\n        if (['generateCommitMessage', 'manageNpmDependency', 'untangleAndRestructureFile'].includes(actionName)) {\r\n            return ['workspace:read', 'workspace:write', 'shell:execute'];\r\n        }\r\n        if (['replaceLines', 'insertLines', 'findAndReplace', 'renameSymbol', 'extractToFunction', 'encapsulateField', 'aiRefactor', 'generateCode', 'moveAndRefactorImports'].includes(actionName)) {\r\n            return ['workspace:read', 'workspace:write'];\r\n        }\r\n        return ['workspace:read'];\r\n    }\r\n\r\n    public async executeAIOperation(context: IAIContext, input: { prompt: string }): Promise<string> {\r\n        this.logger.info(`Executing generic AI operation for tool`, { promptLength: input.prompt.length });\r\n        const prompt: LanguageModelPromptPart[] = [{ type: 'text', text: input.prompt }];\r\n        return context.llm.generateResponse(prompt, {\r\n            cancellationToken: new vscode.CancellationTokenSource().token,\r\n            jsonOutput: false\r\n        });\r\n    }\r\n\r\n    public getDocumentation(): string {\r\n        return `# Tool: ${this.name} v${this.version}\r\n**Description:** ${this.description}\r\nThis tool performs advanced, language-aware code modifications. It automatically detects the programming language and uses the best available method (AST for TypeScript/JavaScript, VS Code's LSP for others) to ensure refactorings are safe and accurate.\r\n\r\n## Actions\r\n${Object.entries(this.actions).map(([name, def]) => this.getActionDocumentation(name, def)).join('\\n')}`;\r\n    }\r\n\r\n    // #region --- Action Implementations (Delegating to Strategies) ---\r\n\r\n    private async executeUntangleAndRestructureFile(input: z.infer<typeof UntangleAndRestructureFileInputSchema>, options: ToolInvokeOptions): Promise<{ success: boolean; message: string; stepsTaken: string[]; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'untangleAndRestructureFile', input, { affectedFiles: [input.filePath], tags: ['cleanup', 'refactor', 'ai'] });\r\n        const stepsTaken: string[] = [];\r\n\r\n        let currentContent = await this.fileSystem.readFile(input.filePath);\r\n        const originalContent = currentContent;\r\n\r\n        // Step 1: Pre-emptive syntax cleaning for common \"messy\" code issues.\r\n        this.logger.info(`Untangle Step 1: Sanitizing syntax for ${input.filePath}`);\r\n        let sanitizedContent = this.sanitizeBrokenComments(currentContent);\r\n        if (sanitizedContent !== currentContent) {\r\n            stepsTaken.push(\"Sanitized broken multi-line comments.\");\r\n            currentContent = sanitizedContent;\r\n        }\r\n\r\n        // Step 2: Attempt to format using the language-specific strategy. This often fixes many small syntax issues.\r\n        try {\r\n            this.logger.info(`Untangle Step 2: Programmatic formatting via LSP for ${input.filePath}`);\r\n            await this.fileSystem.writeFile(input.filePath, currentContent); // Write sanitized content to disk\r\n            const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);\r\n            await strategy.organizeImports(input.filePath);\r\n            await vscode.commands.executeCommand('editor.action.formatDocument', vscode.Uri.file(path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', input.filePath)));\r\n            currentContent = await this.fileSystem.readFile(input.filePath);\r\n            stepsTaken.push(\"Formatted document and organized imports using language server.\");\r\n        } catch (e) {\r\n            const err = e as Error;\r\n            this.logger.warn(`Programmatic formatting failed for ${input.filePath}, proceeding with AI. Error: ${err.message}`);\r\n            stepsTaken.push(\"Programmatic formatting failed, will rely on AI for cleanup.\");\r\n        }\r\n\r\n        // Step 3 (Optional): Run an external linter if requested.\r\n        if (input.runLinterFix) {\r\n            try {\r\n                this.logger.info(`Untangle Step 3: Running linter with --fix for ${input.filePath}`);\r\n                const terminal = this.getTerminalSession();\r\n                // This assumes standard linters are in package.json scripts. A more robust solution would detect the linter.\r\n                await terminal.executeCommand(`npx eslint ${input.filePath} --fix`);\r\n                currentContent = await this.fileSystem.readFile(input.filePath);\r\n                stepsTaken.push(\"Executed linter with auto-fix.\");\r\n            } catch (e) {\r\n                const err = e as Error;\r\n                this.logger.warn(`Linter command failed for ${input.filePath}: ${err.message}`);\r\n                stepsTaken.push(\"Linter auto-fix command failed or was not configured.\");\r\n            }\r\n        }\r\n\r\n        // Step 4 (Optional): AI-driven logical restructuring.\r\n        if (input.aiRestructure) {\r\n            this.logger.info(`Untangle Step 4: Using AI to logically restructure ${input.filePath}`);\r\n            const restructurePrompt = `You are an expert software architect. Your task is to logically restructure the following code file. Do not change the logic, but improve the organization.\r\n            Common tasks include:\r\n            - Grouping related functions or methods.\r\n            - Ordering members within a class (e.g., public fields, private fields, constructor, public methods, private methods).\r\n            - Moving helper functions closer to where they are used.\r\n            - Ensuring a consistent and logical top-to-bottom flow in the file.\r\n            \r\n            IMPORTANT: Output only the complete, restructured code for the entire file. Do not add any explanations or markdown.\r\n            \r\n            Original File Content:\r\n            ---\r\n            ${currentContent}\r\n            ---`;\r\n            currentContent = await this.executeAIOperation(options.services.aiContext, { prompt: restructurePrompt });\r\n            stepsTaken.push(\"Applied AI-driven logical code restructuring.\");\r\n        }\r\n\r\n        await this.fileSystem.writeFile(input.filePath, currentContent);\r\n\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { stepsTaken } });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, message: `Successfully untangled and restructured ${input.filePath}.`, stepsTaken, memoryId: finalMemory.id };\r\n    }\r\n\r\n\r\n    private async executeRenameSymbol(input: z.infer<typeof RenameSymbolInputSchema>): Promise<{ success: boolean; filesModified: string[]; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'renameSymbol', input, { tags: ['refactor', 'language-aware'] });\r\n        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);\r\n        const filesModified = await strategy.renameSymbol(input.filePath, input.line, input.column, input.newName);\r\n\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { filesModified } });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, filesModified, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeExtractToFunction(input: z.infer<typeof ExtractToFunctionInputSchema>): Promise<{ success: boolean; message: string; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'extractToFunction', input, { affectedFiles: [input.filePath], tags: ['refactor', 'language-aware'] });\r\n        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);\r\n        const { newFunctionCode } = await strategy.extractToFunction(input.filePath, input.startLine, input.endLine, input.newFunctionName);\r\n\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { newFunctionCode } });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, message: `Successfully extracted function in ${input.filePath}.`, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeEncapsulateField(input: z.infer<typeof EncapsulateFieldInputSchema>): Promise<{ success: boolean; filesModified: string[]; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'encapsulateField', input, { affectedFiles: [input.filePath], tags: ['refactor', 'typescript'] });\r\n        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);\r\n        if (!(strategy instanceof TypeScriptStrategy)) {\r\n            throw new Error(\"Encapsulate Field is currently only supported for TypeScript/JavaScript files.\");\r\n        }\r\n\r\n        const sourceFile = (strategy as any).getSourceFileOrThrow(input.filePath) as SourceFile;\r\n        const classNode = sourceFile.getClassOrThrow(input.className);\r\n        const prop = classNode.getPropertyOrThrow(input.propertyName);\r\n\r\n        if (prop.getScope() === Scope.Private) {\r\n            throw new Error(`Property '${input.propertyName}' is already private.`);\r\n        }\r\n\r\n        prop.set({ scope: Scope.Private });\r\n        const getter = classNode.addGetAccessor({\r\n            name: input.propertyName,\r\n            returnType: prop.getType().getText(),\r\n            statements: `return this.${input.propertyName};`\r\n        });\r\n        const setter = classNode.addSetAccessor({\r\n            name: input.propertyName,\r\n            parameters: [{ name: 'value', type: prop.getType().getText() }],\r\n            statements: `this.${input.propertyName} = value;`\r\n        });\r\n\r\n        const references = prop.findReferencesAsNodes();\r\n        for (const ref of references) {\r\n            const parent = ref.getParent();\r\n            if (Node.isPropertyAccessExpression(parent) && Node.isBinaryExpression(parent.getParent()) && parent.getParent().getOperatorToken().getKind() === SyntaxKind.EqualsToken) {\r\n                parent.getParent().replaceWithText(`this.${setter.getName()}(${parent.getParent().getRight().getText()})`);\r\n            } else {\r\n                ref.replaceWithText(`this.${getter.getName()}`);\r\n            }\r\n        }\r\n\r\n        const filesModified = await (strategy as any).saveModifiedFiles();\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { filesModified } });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, filesModified, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeMoveAndRefactor(input: z.infer<typeof MoveAndRefactorInputSchema>): Promise<{ success: boolean; filesUpdated: number; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'moveAndRefactorImports', input, { tags: ['refactor', 'workspace', 'typescript'] });\r\n        const strategy = await this.languageServiceFactory.getStrategyForFile(input.sourcePath);\r\n        if (!(strategy instanceof TypeScriptStrategy)) {\r\n            throw new Error(\"Move and Refactor Imports is currently only supported for TypeScript/JavaScript projects.\");\r\n        }\r\n\r\n        const sourceFile = (strategy as any).getSourceFileOrThrow(input.sourcePath) as SourceFile;\r\n        sourceFile.move(input.destinationPath);\r\n        const modifiedFiles = await (strategy as any).saveModifiedFiles();\r\n\r\n        const filesUpdated = modifiedFiles.length;\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { filesUpdated } });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, filesUpdated, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeReplaceLines(input: z.infer<typeof ReplaceLinesInputSchema>): Promise<{ success: boolean; message: string; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'replaceLines', input, { affectedFiles: [input.filePath] });\r\n        const content = await this.fileSystem.readFile(input.filePath);\r\n        const lines = content.split('\\n');\r\n        if (input.startLine > input.endLine || input.endLine > lines.length) {\r\n            throw new Error('Invalid line range provided.');\r\n        }\r\n        if (input.createBackup) await this.fileSystem.writeFile(`${input.filePath}.bak`, content);\r\n        lines.splice(input.startLine - 1, input.endLine - input.startLine + 1, ...input.newContent.split('\\n'));\r\n        await this.fileSystem.writeFile(input.filePath, lines.join('\\n'));\r\n\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, message: `Successfully replaced lines in ${input.filePath}.`, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeInsertLines(input: z.infer<typeof InsertLinesInputSchema>): Promise<{ success: boolean; message: string; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'insertLines', input, { affectedFiles: [input.filePath] });\r\n        const content = await this.fileSystem.readFile(input.filePath);\r\n        const lines = content.split('\\n');\r\n        if (input.line > lines.length + 1 || input.line < 1) {\r\n            throw new Error('Invalid line number for insertion.');\r\n        }\r\n        if (input.createBackup) await this.fileSystem.writeFile(`${input.filePath}.bak`, content);\r\n        lines.splice(input.line - 1, 0, ...input.newContent.split('\\n'));\r\n        await this.fileSystem.writeFile(input.filePath, lines.join('\\n'));\r\n\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, message: `Successfully inserted content in ${input.filePath}.`, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeFindAndReplace(input: z.infer<typeof FindAndReplaceInputSchema>): Promise<{ success: boolean; filesModified: number; totalReplacements: number; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'findAndReplace', input, { tags: ['bulk', 'workspace'] });\r\n        const files = await this.workspaceKnowledge.findFilesByPattern(input.globPattern);\r\n        let filesModified = 0;\r\n        let totalReplacements = 0;\r\n\r\n        for (const file of files) {\r\n            const filePath = vscode.workspace.asRelativePath(file);\r\n            const replacements = await this.fileSystem.replaceInFile(\r\n                filePath,\r\n                input.isRegex ? new RegExp(input.findPattern, 'g') : input.findPattern,\r\n                input.replacePattern\r\n            );\r\n            if (replacements > 0) {\r\n                filesModified++;\r\n                totalReplacements += replacements;\r\n            }\r\n        }\r\n\r\n        const output = { filesModified, totalReplacements };\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true, output });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, ...output, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeAiRefactor(input: z.infer<typeof AIRefactorInputSchema>, options: ToolInvokeOptions): Promise<{ success: boolean; message: string; diff: string; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'aiRefactor', input, { affectedFiles: [input.filePath], tags: ['ai', 'refactor'] });\r\n        const originalContent = await this.fileSystem.readFile(input.filePath);\r\n        const codeToRefactor = this.extractCodeBlock(originalContent, input.startLine, input.endLine);\r\n\r\n        const prompt = `You are an expert AI software engineer. Your task is to refactor a piece of code based on a specific instruction.\r\n        File Path: ${input.filePath}\r\n        Instruction: \"${input.refactorInstruction}\"\r\n\r\n        IMPORTANT: You must only output the new, refactored code for the provided snippet. Do NOT include explanations, markdown, or any other text.\r\n\r\n        Original Code Snippet:\r\n        ---\r\n        ${codeToRefactor}\r\n        ---`;\r\n\r\n        const refactoredSnippet = await this.executeAIOperation(options.services.aiContext, { prompt });\r\n        const newContent = this.replaceCodeBlock(originalContent, refactoredSnippet, input.startLine, input.endLine);\r\n\r\n        await this.fileSystem.writeFile(input.filePath, newContent, { backup: true });\r\n        const diff = await this.fileSystem.diffContent(originalContent, newContent, { format: DiffFormat.Unified });\r\n\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { diff } });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, message: 'AI refactoring complete.', diff, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeGenerateCode(input: z.infer<typeof GenerateCodeInputSchema>, options: ToolInvokeOptions): Promise<{ success: boolean; generatedCode: string; memoryId: string }> {\r\n        const memory = createToolOperationMemory(this.name, 'generateCode', input, { affectedFiles: [input.filePath], tags: ['ai', 'generation'] });\r\n\r\n        const prompt = `You are an expert AI software engineer. Generate a piece of code based on the following instruction.\r\n        Instruction: \"${input.generationInstruction}\"\r\n\r\n        IMPORTANT: You must only output the raw code. Do NOT include explanations, markdown, or any other text.\r\n        `;\r\n\r\n        const generatedCode = await this.executeAIOperation(options.services.aiContext, { prompt });\r\n        await this.executeInsertLines({\r\n            filePath: input.filePath,\r\n            line: input.line || (await this.fileSystem.readFile(input.filePath)).split('\\n').length + 1,\r\n            newContent: generatedCode,\r\n            createBackup: false, // Don't backup for a new code insertion\r\n        });\r\n\r\n        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { generatedCode } });\r\n        await this.memoryOperations.storeMemory(finalMemory as any);\r\n        return { success: true, generatedCode, memoryId: finalMemory.id };\r\n    }\r\n\r\n    private async executeGenerateCommitMessage(input: z.infer<typeof GenerateCommitMessageInputSchema>): Promise<{ success: boolean; commitMessage: string }> {\r\n        const terminal = this.getTerminalSession();\r\n        await terminal.executeCommand('git add -A');\r\n        const diff = await terminal.executeCommand('git diff --staged');\r\n\r\n        if (!diff.trim()) return { success: true, commitMessage: 'No changes to commit.' };\r\n\r\n        const prompt = `Based on the following git diff, generate a concise commit message.\r\n        ${input.conventionalCommit ? 'Use the Conventional Commits specification (e.g., feat(api): add new endpoint). ' : ''}\r\n        Diff:\\n${diff}`;\r\n        const commitMessage = await this.executeAIOperation(this.getAIContext(), { prompt });\r\n        return { success: true, commitMessage };\r\n    }\r\n\r\n    private async executeManageNpmDependency(input: z.infer<typeof ManageNpmDependencyInputSchema>): Promise<{ success: boolean; message: string; terminalOutput: string }> {\r\n        const terminal = this.getTerminalSession();\r\n        const cmd = `npm ${input.action === 'add' ? 'install' : 'uninstall'} ${input.packageName}${input.isDevDependency ? ' --save-dev' : ''}`;\r\n        const output = await terminal.executeCommand(cmd);\r\n\r\n        return { success: true, message: `Successfully executed: ${cmd}`, terminalOutput: output };\r\n    }\r\n    // #endregion\r\n\r\n    // #region --- Private Helper Methods ---\r\n\r\n    private getActionDocumentation(name: string, def: ToolActionDefinition): string {\r\n        const inputSchema = def.inputSchema as z.ZodObject<any>;\r\n        const props = Object.entries(inputSchema.shape).map(([key, val]) => {\r\n            const schema = val as z.ZodTypeAny;\r\n            return `        - \\`${key}\\`: \\`${(schema._def as any).typeName}\\` - ${schema.description || 'No description.'}`;\r\n        }).join('\\n');\r\n        return `\\n### \\`${name}\\`\\n**Description:** ${def.description}\\n**Inputs:**\\n${props}`;\r\n    }\r\n\r\n    private extractCodeBlock(content: string, startLine?: number, endLine?: number): string {\r\n        if (!startLine || !endLine) return content;\r\n        return content.split('\\n').slice(startLine - 1, endLine).join('\\n');\r\n    }\r\n\r\n    private replaceCodeBlock(originalContent: string, newSnippet: string, startLine?: number, endLine?: number): string {\r\n        if (!startLine || !endLine) return newSnippet; // Replace whole file if no range\r\n        const lines = originalContent.split('\\n');\r\n        lines.splice(startLine - 1, endLine - startLine + 1, ...newSnippet.split('\\n'));\r\n        return lines.join('\\n');\r\n    }\r\n\r\n    private sanitizeBrokenComments(code: string): string {\r\n        let inComment = false;\r\n        let depth = 0;\r\n        const sanitizedChars = [];\r\n        for (let i = 0; i < code.length; i++) {\r\n            if (code[i] === '/' && code[i + 1] === '*') {\r\n                if (!inComment) {\r\n                    sanitizedChars.push('/', '*');\r\n                    inComment = true;\r\n                    depth = 1;\r\n                    i++;\r\n                } else {\r\n                    depth++;\r\n                    // This is a nested /*, we will escape it to prevent syntax break\r\n                    sanitizedChars.push('/', '\\\\*');\r\n                    i++;\r\n                }\r\n            } else if (code[i] === '*' && code[i + 1] === '/') {\r\n                if (inComment) {\r\n                    depth--;\r\n                    if (depth === 0) {\r\n                        sanitizedChars.push('*', '/');\r\n                        inComment = false;\r\n                    } else {\r\n                        // This is a premature */, escape it\r\n                        sanitizedChars.push('\\\\*', '/');\r\n                    }\r\n                    i++;\r\n                } else {\r\n                    // This is a */ without a preceding /*, escape it\r\n                    sanitizedChars.push('\\\\*', '/');\r\n                    i++;\r\n                }\r\n            } else {\r\n                sanitizedChars.push(code[i]);\r\n            }\r\n        }\r\n        return sanitizedChars.join('');\r\n    }\r\n\r\n    // #endregion\r\n}"]}