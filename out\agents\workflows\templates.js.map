{"version": 3, "file": "templates.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/templates.ts"], "names": [], "mappings": ";AAAA;;GAEG;;AAqDH,gDAiCC;AAKD,kDAuEC;AAKD,4DA2EC;AAKD,oEAgDC;AAKD,8CAwEC;AAKD,oEAmDC;AA1aD,iDAAsC;AACtC,6BAAwB;AAExB,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC,8BAA8B;AAC9B,MAAM,mBAAoB,SAAQ,mBAAI;IAOhB;IANpB,IAAI,GAAG,kBAAkB,CAAC;IAC1B,WAAW,GAAG,+DAA+D,CAAC;IAC9E,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uDAAuD,CAAC;KAC/F,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,GAAuB,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;IAEnE,YAAoB,YAA+B;QACjD,KAAK,CAAC,kBAAkB,EAAE,+DAA+D,CAAC,CAAC;QADzE,iBAAY,GAAZ,YAAY,CAAmB;IAEnD,CAAC;IAES,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,EAAsB;QACjD,MAAM,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;QAC1B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,OAAO,GAAU,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1E,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3E,CAAC;CACF;AAED,MAAM,cAAe,SAAQ,mBAAI;IAOX;IANpB,IAAI,GAAG,aAAa,CAAC;IACrB,WAAW,GAAG,6DAA6D,CAAC;IAC5E,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;KACzF,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,GAAuB,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;IAEnE,YAAoB,YAA+B;QACjD,KAAK,CAAC,aAAa,EAAE,6DAA6D,CAAC,CAAC;QADlE,iBAAY,GAAZ,YAAY,CAAmB;IAEnD,CAAC;IAES,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,EAAsB;QACjD,MAAM,OAAO,GAAG,KAAK,IAAI,EAAE,CAAC;QAC5B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAChD,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC/E,OAAO,2BAA2B,CAAC;IACrC,CAAC;CACF;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAChC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,KAAY;IAEZ,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACnE,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7E,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAChF,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;QACzC,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,KAAK;KACrB,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CACjC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,KAAY,EACZ,KAAa;IAEb,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACnE,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,oBAAoB;IACpB,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QACvD,OAAO,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;KAC9E,CAAC;IAEF,yCAAyC;IACzC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;QACpC,KAAK,CAAC,IAAI,CAAC;YACT,IAAI,EAAE,iBAAiB,KAAK,EAAE;YAC9B,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,QAAQ,CAAC,EAAE;YACnB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC1C,OAAO,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,eAAe,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YAC/E,CAAC;SACF,CAAC,CAAC;QAEH,KAAK,CAAC,IAAI,CAAC;YACT,IAAI,EAAE,QAAQ,KAAK,WAAW;YAC9B,MAAM,EAAE,QAAQ,CAAC,EAAE;YACnB,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,KAAK,CAAC,IAAI,CAAC;QACT,IAAI,EAAE,iBAAiB;QACvB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACzB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC1C,OAAO,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAC5D,CAAC;KACF,CAAC,CAAC;IAEH,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,UAAU,CAAC;QACvD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,MAAM;KACtB,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CACtC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,MAAe,EACf,eAAsB;IAEtB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;IAED,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;IAC5F,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,qBAAqB;IACrB,MAAM,UAAU,GAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QAC1D,OAAO,eAAO,CAAC,eAAe,CAAC,SAAS,KAAK,EAAE,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;KACxF,CAAC;IAEF,sCAAsC;IACtC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;QACtC,KAAK,CAAC,IAAI,CAAC;YACT,IAAI,EAAE,uBAAuB,KAAK,EAAE;YACpC,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,SAAS,CAAC,EAAE;YACpB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAC/C,OAAO,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,kBAAkB,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACnF,CAAC;SACF,CAAC,CAAC;QAEH,KAAK,CAAC,IAAI,CAAC;YACT,IAAI,EAAE,SAAS,KAAK,gBAAgB;YACpC,MAAM,EAAE,SAAS,CAAC,EAAE;YACpB,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,qCAAqC;IACrC,KAAK,CAAC,IAAI,CAAC;QACT,IAAI,EAAE,sBAAsB;QAC5B,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACzB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC/C,OAAO,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAC/D,CAAC;KACF,CAAC,CAAC;IAEH,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,GAAG,UAAU,EAAE,UAAU,CAAC;QAC7D,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,aAAa;KAC7B,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,4BAA4B,CAC1C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,KAAY,EACZ,WAA8B;IAE9B,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACjE,MAAM,mBAAmB,GAAG,eAAO,CAAC,cAAc,CAChD,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,CACpB,CAAC;IACF,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACnE,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,WAAW,CAAC,CAAC;IACvD,MAAM,cAAc,GAAG,eAAO,CAAC,cAAc,CAC3C,aAAa,EACb,aAAa,EACb,cAAc,CACf,CAAC;IACF,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACzF,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC5F,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,CAAC,SAAS,EAAE,mBAAmB,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,CAAC;QAC9E,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,MAAM;KACtB,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAC/B,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,mBAA0B,EAC1B,YAAmB,EACnB,gBAAuB;IAEvB,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;IAC3F,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;IACtG,MAAM,UAAU,GAAG,eAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;IAClF,MAAM,YAAY,GAAG,eAAO,CAAC,eAAe,CAAC,UAAU,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAC1F,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,6BAA6B;IAC7B,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACzF,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3F;YACE,IAAI,EAAE,oBAAoB;YAC1B,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,UAAU;YAClB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC3C,OAAO,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAC3D,CAAC;SACF;QACD;YACE,IAAI,EAAE,uBAAuB;YAC7B,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,aAAa;YACrB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC3C,OAAO,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAC3D,CAAC;SACF;QACD,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QACrF;YACE,IAAI,EAAE,kBAAkB;YACxB,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC3C,OAAO,UAAU;oBACf,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC;oBACnC,UAAU,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC;YAC1D,CAAC;SACF;KACF,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC;QACzF,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,WAAW;KAC3B,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,4BAA4B,CAC1C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,iBAAwB,EACxB,aAAoB,EACpB,cAAqB;IAErB,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;IAC/G,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;IAC5G,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,kBAAkB,EAAE,cAAc,CAAC,CAAC;IACjG,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;IACjH,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC9G,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,wCAAwC;IACxC,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,mCAAmC,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACnH,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,qCAAqC,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvH,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KACxG,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,mBAAmB;YACnB,qBAAqB;YACrB,cAAc;YACd,kBAAkB;YAClB,oBAAoB;YACpB,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;KACzB,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa workflow templates\n */\n\nimport { Tool } from './corePolyfill';\nimport { z } from 'zod';\nimport { Agent } from '../agentUtilities/agent';\nimport { Codessa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { Logger } from '../../logger';\nimport { MemoryVectorStore } from '../../memory/codessa/vectorStores/memoryVectorStore';\n\n// Custom tool implementations\nclass MemoryRetrievalTool extends Tool {\n  name = 'memory-retrieval';\n  description = 'Retrieves relevant memories based on the current conversation';\n  schema = z.object({\n    input: z.string().optional().describe('The query to search for in memories (optional string)')\n  }).strip().transform((obj: { input?: string }) => obj.input ?? '');\n\n  constructor(private _vectorStore: MemoryVectorStore) {\n    super('memory-retrieval', 'Retrieves relevant memories based on the current conversation');\n  }\n\n  protected async _call({ input }: { input?: string }): Promise<string> {\n    const query = input || '';\n    Logger.instance.info('Retrieving memories for:', query);\n    const results: any[] = await this._vectorStore.similaritySearch(query, 5);\n    return results.map(r => (r.content ?? r.pageContent ?? '')).join('\\n\\n');\n  }\n}\n\nclass MemorySaveTool extends Tool {\n  name = 'memory-save';\n  description = 'Saves important information from the conversation to memory';\n  schema = z.object({\n    input: z.string().optional().describe('The content to save to memory (optional string)')\n  }).strip().transform((obj: { input?: string }) => obj.input ?? '');\n\n  constructor(private _vectorStore: MemoryVectorStore) {\n    super('memory-save', 'Saves important information from the conversation to memory');\n  }\n\n  protected async _call({ input }: { input?: string }): Promise<string> {\n    const content = input || '';\n    Logger.instance.info('Saving memory:', content);\n    await this._vectorStore.addDocuments([{ pageContent: content, metadata: {} }]);\n    return 'Memory saved successfully';\n  }\n}\n\n/**\n * Create a simple chat workflow\n */\nexport function createChatWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  agent: Agent\n): GraphDefinition {\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const agentNode = Codessa.createAgentNode('agent', 'Agent', agent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-agent', source: 'input', target: 'agent', type: 'default' },\n    { name: 'agent-to-output', source: 'agent', target: 'output', type: 'default' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [inputNode, agentNode, outputNode],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'ask'\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a ReAct agent workflow with tool integration\n */\nexport function createReActWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  agent: Agent,\n  tools: Tool[]\n): GraphDefinition {\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const agentNode = Codessa.createAgentNode('agent', 'Agent', agent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create tool nodes\n  const toolNodes: GraphNode[] = tools.map((tool, index) => {\n    return Codessa.createToolNode(`tool-${index}`, tool.name, tool);\n  });\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-agent', source: 'input', target: 'agent', type: 'default' }\n  ];\n\n  // Add edges from agent to tools and back\n  toolNodes.forEach((toolNode, index) => {\n    edges.push({\n      name: `agent-to-tool-${index}`,\n      source: 'agent',\n      target: toolNode.id,\n      type: 'default',\n      condition: async (state) => {\n        const lastOutput = state.outputs['agent'];\n        return lastOutput && lastOutput.includes(`Using tool: ${tools[index].name}`);\n      }\n    });\n\n    edges.push({\n      name: `tool-${index}-to-agent`,\n      source: toolNode.id,\n      target: 'agent',\n      type: 'default'\n    });\n  });\n\n  // Add edge from agent to output\n  edges.push({\n    name: 'agent-to-output',\n    source: 'agent',\n    target: 'output',\n    type: 'default',\n    condition: async (state) => {\n      const lastOutput = state.outputs['agent'];\n      return lastOutput && lastOutput.includes('Final Answer:');\n    }\n  });\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [inputNode, agentNode, ...toolNodes, outputNode],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'chat'\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a multi-agent workflow for collaborative problem solving\n */\nexport function createMultiAgentWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  agents: Agent[],\n  supervisorAgent: Agent\n): GraphDefinition {\n  if (agents.length === 0) {\n    throw new Error('At least one agent is required');\n  }\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const supervisorNode = Codessa.createAgentNode('supervisor', 'Supervisor', supervisorAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create agent nodes\n  const agentNodes: GraphNode[] = agents.map((agent, index) => {\n    return Codessa.createAgentNode(`agent-${index}`, agent.name, agent);\n  });\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-supervisor', source: 'input', target: 'supervisor', type: 'default' }\n  ];\n\n  // Add edges from supervisor to agents\n  agentNodes.forEach((agentNode, index) => {\n    edges.push({\n      name: `supervisor-to-agent-${index}`,\n      source: 'supervisor',\n      target: agentNode.id,\n      type: 'default',\n      condition: async (state) => {\n        const lastOutput = state.outputs['supervisor'];\n        return lastOutput && lastOutput.includes(`Delegating to: ${agents[index].name}`);\n      }\n    });\n\n    edges.push({\n      name: `agent-${index}-to-supervisor`,\n      source: agentNode.id,\n      target: 'supervisor',\n      type: 'default'\n    });\n  });\n\n  // Add edge from supervisor to output\n  edges.push({\n    name: 'supervisor-to-output',\n    source: 'supervisor',\n    target: 'output',\n    type: 'default',\n    condition: async (state) => {\n      const lastOutput = state.outputs['supervisor'];\n      return lastOutput && lastOutput.includes('Final Consensus:');\n    }\n  });\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [inputNode, supervisorNode, ...agentNodes, outputNode],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'multi-agent'\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a memory-enhanced workflow with vector store integration\n */\nexport function createMemoryEnhancedWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  agent: Agent,\n  vectorStore: MemoryVectorStore\n): GraphDefinition {\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const memoryRetrievalTool = new MemoryRetrievalTool(vectorStore);\n  const memoryRetrievalNode = Codessa.createToolNode(\n    'memory-retrieval',\n    'Memory Retrieval',\n    memoryRetrievalTool\n  );\n  const agentNode = Codessa.createAgentNode('agent', 'Agent', agent);\n  const memorySaveTool = new MemorySaveTool(vectorStore);\n  const memorySaveNode = Codessa.createToolNode(\n    'memory-save',\n    'Memory Save',\n    memorySaveTool\n  );\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-memory-retrieval', source: 'input', target: 'memory-retrieval', type: 'default' },\n    { name: 'memory-retrieval-to-agent', source: 'memory-retrieval', target: 'agent', type: 'default' },\n    { name: 'agent-to-memory-save', source: 'agent', target: 'memory-save', type: 'default' },\n    { name: 'memory-save-to-output', source: 'memory-save', target: 'output', type: 'default' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [inputNode, memoryRetrievalNode, agentNode, memorySaveNode, outputNode],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'chat'\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a workflow for Test-Driven Development (TDD)\n */\nexport function createTDDWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  implementationAgent: Agent,\n  testingAgent: Agent,\n  refactoringAgent: Agent\n): GraphDefinition {\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const testWriterNode = Codessa.createAgentNode('test-writer', 'Test Writer', testingAgent);\n  const implementerNode = Codessa.createAgentNode('implementer', 'Implementation', implementationAgent);\n  const testerNode = Codessa.createAgentNode('tester', 'Test Runner', testingAgent);\n  const refactorNode = Codessa.createAgentNode('refactor', 'Refactoring', refactoringAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create edges for TDD cycle\n  const edges: GraphEdge[] = [\n    { name: 'input-to-test-writer', source: 'input', target: 'test-writer', type: 'default' },\n    { name: 'test-writer-to-implementer', source: 'test-writer', target: 'implementer', type: 'default' },\n    { name: 'implementer-to-tester', source: 'implementer', target: 'tester', type: 'default' },\n    {\n      name: 'tester-to-refactor',\n      source: 'tester',\n      target: 'refactor',\n      type: 'default',\n      condition: async (state) => {\n        const testOutput = state.outputs['tester'];\n        return testOutput && testOutput.includes('Tests passed');\n      }\n    },\n    {\n      name: 'tester-to-implementer',\n      source: 'tester',\n      target: 'implementer',\n      type: 'default',\n      condition: async (state) => {\n        const testOutput = state.outputs['tester'];\n        return testOutput && testOutput.includes('Tests failed');\n      }\n    },\n    { name: 'refactor-to-tester', source: 'refactor', target: 'tester', type: 'default' },\n    {\n      name: 'tester-to-output',\n      source: 'tester',\n      target: 'output',\n      type: 'default',\n      condition: async (state) => {\n        const testOutput = state.outputs['tester'];\n        return testOutput &&\n          testOutput.includes('Tests passed') &&\n          testOutput.includes('Code quality metrics satisfied');\n      }\n    }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [inputNode, testWriterNode, implementerNode, testerNode, refactorNode, outputNode],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'debugging'\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a workflow for Agile Sprint Planning\n */\nexport function createSprintPlanningWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  productOwnerAgent: Agent,\n  techLeadAgent: Agent,\n  estimatorAgent: Agent\n): GraphDefinition {\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const backlogGroomingNode = Codessa.createAgentNode('backlog-grooming', 'Backlog Grooming', productOwnerAgent);\n  const technicalAnalysisNode = Codessa.createAgentNode('tech-analysis', 'Technical Analysis', techLeadAgent);\n  const estimationNode = Codessa.createAgentNode('estimation', 'Story Estimation', estimatorAgent);\n  const prioritizationNode = Codessa.createAgentNode('prioritization', 'Sprint Prioritization', productOwnerAgent);\n  const capacityPlanningNode = Codessa.createAgentNode('capacity-planning', 'Capacity Planning', techLeadAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create edges for sprint planning flow\n  const edges: GraphEdge[] = [\n    { name: 'input-to-backlog-grooming', source: 'input', target: 'backlog-grooming', type: 'default' },\n    { name: 'backlog-grooming-to-tech-analysis', source: 'backlog-grooming', target: 'tech-analysis', type: 'default' },\n    { name: 'tech-analysis-to-estimation', source: 'tech-analysis', target: 'estimation', type: 'default' },\n    { name: 'estimation-to-prioritization', source: 'estimation', target: 'prioritization', type: 'default' },\n    { name: 'prioritization-to-capacity-planning', source: 'prioritization', target: 'capacity-planning', type: 'default' },\n    { name: 'capacity-planning-to-output', source: 'capacity-planning', target: 'output', type: 'default' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      backlogGroomingNode,\n      technicalAnalysisNode,\n      estimationNode,\n      prioritizationNode,\n      capacityPlanningNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic'\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}