"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractiveSession = exports.TerminalActions = exports.PseudoTerminal = exports.TerminalProcess = exports.TerminalActionProvider = void 0;
const vscode = __importStar(require("vscode"));
const os = __importStar(require("os"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
const events_1 = require("events");
const logger_1 = require("../logger");
// Import code action provider integration
const codeActionProvider_1 = require("./codeActionProvider");
/**
 * Terminal action provider that combines code actions and terminal capabilities.
 */
class TerminalActionProvider extends codeActionProvider_1.TerminalCodeActionProvider {
    _quickFixHistory = new Set();
    _outputBuffer = '';
    _outputFlushTimer = null;
    _terminalDiagnostics;
    _aiPatterns = new Map();
    _toolContext = new Map();
    _onAIAction = new vscode.EventEmitter();
    _onPatternMatch = new vscode.EventEmitter();
    onAIAction = this._onAIAction.event;
    onPatternMatch = this._onPatternMatch.event;
    registerToolContext(toolId, context) {
        this._toolContext.set(toolId, context);
    }
    getToolContext(toolId) {
        return this._toolContext.get(toolId);
    }
    updateToolContext(toolId, context) {
        const existing = this._toolContext.get(toolId);
        if (existing) {
            this._toolContext.set(toolId, { ...existing, ...context });
        }
    }
    constructor() {
        super();
        this._terminalDiagnostics = vscode.languages.createDiagnosticCollection('terminal');
        this.initializeTerminalPatterns();
        // Register terminal-specific icons
        this._registerIcon({
            name: 'terminal-error',
            icon: new vscode.ThemeIcon('error', new vscode.ThemeColor('terminalError.foreground')),
            category: 'terminal-error',
            description: 'Terminal error indicators'
        });
        this._registerIcon({
            name: 'terminal-warning',
            icon: new vscode.ThemeIcon('warning', new vscode.ThemeColor('terminalWarning.foreground')),
            category: 'terminal-warning',
            description: 'Terminal warning indicators'
        });
        this._registerIcon({
            name: 'terminal-success',
            icon: new vscode.ThemeIcon('check', new vscode.ThemeColor('terminalSuccess.foreground')),
            category: 'terminal-success',
            description: 'Terminal success indicators'
        });
    }
    initializeTerminalPatterns() {
        // Register standard terminal patterns
        this._registerStandardPatterns();
        // Register enhanced patterns
        this._registerEnhancedPatterns();
    }
    _registerStandardPatterns() {
        // Command not found
        this.registerPattern({
            pattern: /command not found:\s+(.+)$/m,
            action: 'install-missing-command',
            priority: 100,
            category: 'terminal-error',
            fix: (match, doc, range) => {
                const cmd = match[1];
                const action = new vscode.CodeAction(`${this._getIconForCategory('error', 'terminal')} Install missing command: ${cmd}`, vscode.CodeActionKind.QuickFix);
                action.command = {
                    command: 'codessa.terminal.installCommand',
                    title: `Install ${cmd}`,
                    arguments: [cmd]
                };
                action.diagnostics = [{
                        message: `Command '${cmd}' not found`,
                        range,
                        severity: vscode.DiagnosticSeverity.Error,
                        source: 'terminal'
                    }];
                return action;
            }
        });
        // Permission denied
        this.registerPattern({
            pattern: /permission denied|EACCES/i,
            action: 'fix-permissions',
            priority: 90,
            category: 'security',
            fix: (match, doc, range) => {
                const action = new vscode.CodeAction(`${this._getIconForCategory('security', 'terminal')} Fix permission issues`, vscode.CodeActionKind.QuickFix.append('security'));
                action.command = {
                    command: 'codessa.terminal.fixPermissions',
                    title: 'Fix permissions',
                    arguments: [match[0], doc.uri.toString()]
                };
                action.diagnostics = [{
                        message: `Permission error: ${match[0]}`,
                        range,
                        severity: vscode.DiagnosticSeverity.Error,
                        source: 'terminal'
                    }];
                return action;
            }
        });
        // Network issues
        this.registerPattern({
            pattern: /ECONNREFUSED|ETIMEDOUT|network error/i,
            action: 'fix-network',
            priority: 85,
            category: 'terminal-warning',
            fix: (match, doc, range) => {
                const action = new vscode.CodeAction(`${this._getIconForCategory('warning', 'terminal')} Fix network connection`, vscode.CodeActionKind.QuickFix);
                action.command = {
                    command: 'codessa.terminal.fixNetwork',
                    title: 'Fix network',
                    arguments: [match[0], doc.uri.toString()]
                };
                action.diagnostics = [{
                        message: `Network error: ${match[0]}`,
                        range,
                        severity: vscode.DiagnosticSeverity.Warning,
                        source: 'terminal'
                    }];
                return action;
            }
        });
    }
    _registerEnhancedPatterns() {
        // Build errors
        this.registerPattern({
            pattern: /error TS\d+:|error C\d+:|compilation failed/i,
            action: 'fix-build-error',
            priority: 95,
            category: 'debug',
            fix: (match, doc, range) => {
                const action = new vscode.CodeAction(`${this._getIconForCategory('error', 'terminal')} Fix build error`, vscode.CodeActionKind.QuickFix.append('debug'));
                action.command = {
                    command: 'codessa.terminal.fixBuildError',
                    title: 'Fix build error',
                    arguments: [match[0], doc.uri.toString()]
                };
                action.diagnostics = [{
                        message: `Build error: ${match[0]}`,
                        range,
                        severity: vscode.DiagnosticSeverity.Error,
                        source: 'terminal',
                        relatedInformation: [{
                                location: new vscode.Location(doc.uri, range),
                                message: 'Build process failed'
                            }]
                    }];
                return action;
            }
        });
        // Missing dependencies
        this.registerPattern({
            pattern: /cannot find module|module not found|package .* is not installed/i,
            action: 'install-dependency',
            priority: 95,
            category: 'dependency',
            fix: (match, doc, range) => {
                const moduleName = match[0].match(/['"]([^'"]+)['"]/)?.[1] || 'package';
                const action = new vscode.CodeAction(`${this._getIconForCategory('package', 'terminal')} Install missing dependency: ${moduleName}`, vscode.CodeActionKind.QuickFix.append('dependency'));
                action.command = {
                    command: 'codessa.terminal.installDependency',
                    title: 'Install dependency',
                    arguments: [match[0], doc.uri.toString()]
                };
                action.diagnostics = [{
                        message: `Missing dependency: ${moduleName}`,
                        range,
                        severity: vscode.DiagnosticSeverity.Warning,
                        source: 'terminal',
                        tags: [vscode.DiagnosticTag.Unnecessary],
                        relatedInformation: [{
                                location: new vscode.Location(doc.uri, range),
                                message: `Run 'npm install ${moduleName}' to fix this issue`
                            }]
                    }];
                return action;
            }
        });
    }
    async analyzeOutput(output) {
        this._appendOutput(output);
        class TerminalDocument {
            uri = vscode.Uri.parse('terminal://output');
            languageId = 'terminal';
            version = 1;
            lineCount = output.split('\n').length;
            getText(range) {
                if (!range)
                    return output;
                const lines = output.split('\n');
                if (range.start.line === range.end.line) {
                    return lines[range.start.line].substring(range.start.character, range.end.character);
                }
                const selectedLines = lines.slice(range.start.line, range.end.line + 1);
                selectedLines[0] = selectedLines[0].substring(range.start.character);
                const lastIndex = selectedLines.length - 1;
                selectedLines[lastIndex] = selectedLines[lastIndex].substring(0, range.end.character);
                return selectedLines.join('\n');
            }
            lineAt(lineOrPosition) {
                const line = typeof lineOrPosition === 'number' ? lineOrPosition : lineOrPosition.line;
                const lines = output.split('\n');
                const text = lines[line] || '';
                return {
                    lineNumber: line,
                    text,
                    range: new vscode.Range(line, 0, line, text.length),
                    rangeIncludingLineBreak: new vscode.Range(line, 0, line, text.length + 1),
                    firstNonWhitespaceCharacterIndex: text.search(/\S/),
                    isEmptyOrWhitespace: text.trim().length === 0
                };
            }
        }
        const range = new vscode.Range(0, 0, output.split('\n').length - 1, 0);
        const context = {
            document: new TerminalDocument(),
            range,
            diagnostics: [],
            trigger: 'auto',
            source: 'terminal'
        };
        // Get standard quick fixes
        const quickFixes = await this._getQuickFixes();
        // Get pattern-based actions from base class
        const patternActions = await this._getPatternBasedActions(context);
        // Get context-aware actions
        const contextActions = this._getContextAwareActions(context);
        // Combine all actions with proper icons and metadata
        const allActions = [...quickFixes, ...patternActions, ...contextActions]
            .map(action => {
            const category = action.kind?.value?.includes('terminal') ? 'terminal' : 'command';
            action.title = `${this._getIconForCategory(category)} ${action.title}`;
            return action;
        });
        // Add diagnostics for relevant actions
        this._updateDiagnostics(allActions);
        return allActions;
    }
    _appendOutput(output) {
        // Process output for visual indicators
        const processedOutput = this._processOutputWithIndicators(output);
        this._outputBuffer += processedOutput;
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
        }
        this._outputFlushTimer = setTimeout(() => this._flushOutputBuffer(), 100);
    }
    _processOutputWithIndicators(output) {
        // Add visual indicators based on content
        const lines = output.split('\n');
        return lines.map(line => {
            if (line.match(/error|failed|exception/i)) {
                return `${this._getIconForCategory('error', 'terminal')} ${line}`;
            }
            if (line.match(/warning|warn/i)) {
                return `${this._getIconForCategory('warning', 'terminal')} ${line}`;
            }
            if (line.match(/success|completed|done/i)) {
                return `${this._getIconForCategory('success', 'terminal')} ${line}`;
            }
            if (line.match(/info|note/i)) {
                return `${this._getIconForCategory('info', 'terminal')} ${line}`;
            }
            return line;
        }).join('\n');
    }
    _flushOutputBuffer() {
        this._outputBuffer = '';
        this._outputFlushTimer = null;
    }
    async _getQuickFixes() {
        const actions = [];
        const uniqueActions = new Set();
        const processPatterns = [];
        for (const pattern of this._quickFixPatterns) {
            const matches = this._outputBuffer.match(pattern.pattern);
            if (matches && !this._quickFixHistory.has(pattern.action)) {
                try {
                    if (this.isAIPattern(pattern)) {
                        // Process AI patterns asynchronously
                        processPatterns.push(this.processAIPattern(pattern, matches).then(aiActions => {
                            for (const action of aiActions) {
                                const actionKey = `${action.title}-${action.kind?.value}`;
                                if (!uniqueActions.has(actionKey)) {
                                    actions.push(action);
                                    uniqueActions.add(actionKey);
                                    this._quickFixHistory.add(pattern.action);
                                    this._addDiagnostic(pattern, matches);
                                }
                            }
                        }));
                    }
                    else {
                        // Process standard patterns immediately
                        const action = this._createQuickFix(pattern, matches);
                        const actionKey = `${action.title}-${action.kind?.value}`;
                        if (!uniqueActions.has(actionKey)) {
                            actions.push(action);
                            uniqueActions.add(actionKey);
                            this._quickFixHistory.add(pattern.action);
                            this._addDiagnostic(pattern, matches);
                        }
                    }
                }
                catch (error) {
                    console.error(`Error creating quick fix for pattern ${pattern.action}:`, error);
                }
            }
        }
        // Wait for all AI pattern processing to complete
        if (processPatterns.length > 0) {
            await Promise.all(processPatterns);
        }
        return actions;
    }
    _addDiagnostic(pattern, matches) {
        const diagnostic = new vscode.Diagnostic(new vscode.Range(0, 0, 0, 0), matches[0], this._getDiagnosticSeverity(pattern.category));
        diagnostic.source = 'terminal';
        diagnostic.code = pattern.action;
        this._terminalDiagnostics.set(vscode.Uri.parse('terminal://output'), [diagnostic]);
    }
    _getDiagnosticSeverity(category) {
        switch (category.toLowerCase()) {
            case 'error':
            case 'security':
                return vscode.DiagnosticSeverity.Error;
            case 'warning':
            case 'dependency':
                return vscode.DiagnosticSeverity.Warning;
            case 'info':
            case 'terminal':
                return vscode.DiagnosticSeverity.Information;
            default:
                return vscode.DiagnosticSeverity.Hint;
        }
    }
    async provideCodeActions(document, range, context, token) {
        // Check for cancellation
        if (token.isCancellationRequested) {
            return [];
        }
        // First check if this is a terminal document and has diagnostic context
        if (document.uri.scheme === 'terminal' && context.diagnostics.length > 0) {
            // Focus our analysis on the specific range where the code action was requested
            // Use the relevant text from the specific range for more targeted analysis
            const relevantText = document.getText(range);
            this._appendOutput(relevantText);
            const actions = await this._getQuickFixes();
            // Filter and sort actions based on diagnostics
            return actions.filter(action => {
                // Only include actions that are relevant to the current diagnostics
                return context.diagnostics.some(diag => {
                    return diag.range.intersection(range) &&
                        action.diagnostics?.some(actionDiag => actionDiag.message === diag.message);
                });
            });
        }
        // For non-terminal documents, let the parent handle it
        return [];
    }
    _createQuickFix(pattern, matches) {
        const dummyDocument = { uri: vscode.Uri.parse('terminal://output') };
        const dummyRange = new vscode.Range(0, 0, 0, 0);
        const action = pattern.fix(matches, dummyDocument, dummyRange);
        action.title = `${this._getIconForCategory(pattern.category)} ${action.title}`;
        action.kind = this._getCodeActionKindForCategory(pattern.category);
        return action;
    }
    _updateDiagnostics(actions) {
        const diagnostics = [];
        // Remove unused lines variable
        actions.forEach(action => {
            if (action.diagnostics?.length) {
                action.diagnostics.forEach(diag => {
                    // Convert line-based range to position in full output
                    const startPos = diag.range.start;
                    const endPos = diag.range.end;
                    const diagnostic = new vscode.Diagnostic(new vscode.Range(startPos, endPos), diag.message, diag.severity);
                    diagnostic.source = 'terminal';
                    diagnostic.code = action.kind?.value;
                    diagnostics.push(diagnostic);
                });
            }
        });
        this._terminalDiagnostics.set(vscode.Uri.parse('terminal://output'), diagnostics);
    }
    _getCodeActionKindForCategory(category) {
        switch (category.toLowerCase()) {
            case 'terminal':
                return vscode.CodeActionKind.QuickFix;
            case 'security':
                return vscode.CodeActionKind.QuickFix.append('security');
            case 'debug':
                return vscode.CodeActionKind.QuickFix.append('debug');
            case 'dependency':
                return vscode.CodeActionKind.QuickFix.append('dependency');
            case 'refactor':
                return vscode.CodeActionKind.RefactorExtract;
            default:
                return vscode.CodeActionKind.QuickFix;
        }
    }
    registerPattern(pattern) {
        if (this.isAIPattern(pattern)) {
            this._aiPatterns.set(pattern.action, pattern);
        }
        this._quickFixPatterns.push(pattern);
    }
    isAIPattern(pattern) {
        return 'aiContext' in pattern || 'contextProvider' in pattern;
    }
    markPatternUsed(patternId) {
        this._quickFixHistory.add(patternId);
    }
    clearHistory() {
        this._quickFixHistory.clear();
        this._terminalDiagnostics.clear();
    }
    async processAIPattern(pattern, matches) {
        this._onPatternMatch.fire({ pattern, matches });
        let context;
        if (pattern.contextProvider) {
            context = await pattern.contextProvider(matches);
        }
        if (!pattern.aiContext) {
            return [];
        }
        // Here you would integrate with your AI service
        // This is just a placeholder for the structure
        const aiResponse = await this.processWithAI(pattern.aiContext, context);
        if (pattern.resultProcessor) {
            const actions = pattern.resultProcessor(aiResponse);
            if (Array.isArray(actions)) {
                actions.forEach(action => {
                    this._onAIAction.fire({ action, context, aiResponse });
                });
                return actions;
            }
            else {
                this._onAIAction.fire({ action: actions, context, aiResponse });
                return [actions];
            }
        }
        return [];
    }
    async processWithAI(aiContext, context) {
        // Implement your AI processing logic here
        // This is where you would integrate with your AI service
        const prompt = this.buildPrompt(aiContext.promptTemplate, context);
        return Promise.resolve({
            suggestion: `${prompt} - AI response`,
            confidence: 0.95,
            modelId: aiContext.modelId,
            context
        });
    }
    buildPrompt(template, context) {
        // Convert context to string representation
        const contextStr = JSON.stringify(context, null, 2);
        return template.replace('{{context}}', contextStr);
    }
    /**
     * Enhanced terminal integration methods
     */
    analyzeTerminalOutput(output) {
        // Use the terminal code action provider to analyze output
        this._appendOutput(output);
        // Return synchronous quick fixes
        const actions = [];
        this._quickFixPatterns.forEach(pattern => {
            const matches = output.match(pattern.pattern);
            if (matches) {
                try {
                    const action = this._createQuickFix(pattern, matches);
                    actions.push(action);
                }
                catch (error) {
                    logger_1.Logger.instance.error(`Error creating quick fix for pattern ${pattern.action}:`, error);
                }
            }
        });
        return actions;
    }
    async applyQuickFix(action) {
        try {
            if (action.edit) {
                const success = await vscode.workspace.applyEdit(action.edit);
                if (success) {
                    this.markPatternUsed(action.kind?.value || 'unknown');
                }
                return success;
            }
            else if (action.command) {
                await vscode.commands.executeCommand(action.command.command, ...action.command.arguments || []);
                this.markPatternUsed(action.kind?.value || 'unknown');
                return true;
            }
            return false;
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to apply quick fix:', error);
            return false;
        }
    }
    getCodeActionManager() {
        return codeActionProvider_1.codeActionManager;
    }
    dispose() {
        this._terminalDiagnostics.dispose();
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
        }
    }
}
exports.TerminalActionProvider = TerminalActionProvider;
/**
 * Terminal process with advanced features extending EventEmitter
 */
class TerminalProcess extends events_1.EventEmitter {
    id;
    config;
    state;
    stats;
    events;
    _terminal = null;
    _isDisposed = false;
    _isInitialized = false;
    _commandHistory = [];
    _outputBuffer = '';
    _logPath;
    _logStream;
    _startupTime = Date.now();
    _lastCommandTime = 0;
    _responseTimes = [];
    _terminalCodeActionProvider;
    // Event emitters
    _onData = new vscode.EventEmitter();
    _onExit = new vscode.EventEmitter();
    _onReady = new vscode.EventEmitter();
    _onResize = new vscode.EventEmitter();
    _onCommand = new vscode.EventEmitter();
    _onError = new vscode.EventEmitter();
    _onStateChange = new vscode.EventEmitter();
    // Private emitter references for internal use
    _dataEmitter;
    _exitEmitter;
    _readyEmitter;
    _resizeEmitter;
    _commandEmitter;
    _errorEmitter;
    _stateChangeEmitter;
    // Resource management
    _disposables = [];
    _writeQueue = [];
    _isWriting = false;
    _outputFlushTimer = null;
    constructor(config = {}) {
        super(); // Call EventEmitter constructor first
        this.id = `terminal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        // Initialize terminal code action provider
        this._terminalCodeActionProvider = new codeActionProvider_1.TerminalCodeActionProvider();
        // Merge with defaults
        this.config = {
            name: 'Enhanced Terminal',
            shell: this._resolveShellPath(config.shell || this._detectShell()),
            args: config.args || this._getShellArgs(),
            cwd: process.cwd(),
            env: {},
            dimensions: { columns: 120, rows: 30 },
            enableColorSupport: true,
            enableUnicodeSupport: true,
            enableBracketedPaste: true,
            scrollback: 1000,
            enableCommandHistory: true,
            maxCommandHistory: 1000,
            enableAutoComplete: false,
            workingDirectoryTracking: true,
            ...config
        };
        // Initialize state
        this.state = {
            isConnected: false,
            isReady: false,
            currentDirectory: this.config.cwd || process.cwd(),
            shellType: this.config.shell || this._detectShell(),
            dimensions: this.config.dimensions || { columns: 120, rows: 30 },
            lastCommand: '',
            commandHistory: [],
            isBusy: false
        };
        // Initialize stats
        this.stats = {
            totalCommands: 0,
            totalOutputBytes: 0,
            averageResponseTime: 0,
            uptimeSeconds: 0,
            errorCount: 0,
            resizeCount: 0
        };
        // Initialize events
        this.events = {
            onData: this._onData.event,
            onExit: this._onExit.event,
            onReady: this._onReady.event,
            onResize: this._onResize.event,
            onCommand: this._onCommand.event,
            onError: this._onError.event,
            onStateChange: this._onStateChange.event
        };
        // Store emitter references for internal use
        this._dataEmitter = this._onData;
        this._exitEmitter = this._onExit;
        this._readyEmitter = this._onReady;
        this._resizeEmitter = this._onResize;
        this._commandEmitter = this._onCommand;
        this._errorEmitter = this._onError;
        this._stateChangeEmitter = this._onStateChange;
        this._disposables.push(this._onData, this._onExit, this._onReady, this._onResize, this._onCommand, this._onError, this._onStateChange);
    }
    _detectShell() {
        const platform = os.platform();
        if (platform === 'win32') {
            return process.env.ComSpec || path.join(process.env.SystemRoot || 'C:\\Windows', 'System32', 'cmd.exe');
        }
        else if (platform === 'darwin') {
            return process.env.SHELL || '/bin/zsh';
        }
        else {
            return process.env.SHELL || '/bin/bash';
        }
    }
    _getShellArgs() {
        const platform = os.platform();
        const shell = this.config.shell || this._detectShell();
        if (platform === 'win32') {
            if (shell.includes('powershell') || shell.includes('pwsh')) {
                return ['-NoExit', '-Command', ''];
            }
            else if (shell.includes('cmd')) {
                return ['/k'];
            }
        }
        else {
            // Unix-like systems
            return ['-i']; // Interactive mode
        }
        return [];
    }
    _resolveShellPath(shell) {
        if (path.isAbsolute(shell)) {
            return shell;
        }
        // Try to resolve shell from PATH
        const pathEnv = process.env.PATH || '';
        const pathSeparator = os.platform() === 'win32' ? ';' : ':';
        const paths = pathEnv.split(pathSeparator);
        for (const dir of paths) {
            const fullPath = path.join(dir, shell);
            try {
                // In a real implementation, you'd check if file exists and is executable
                return fullPath;
            }
            catch {
                continue;
            }
        }
        return shell; // Fallback to original
    }
    async initialize() {
        if (this._isInitialized || this._isDisposed) {
            return;
        }
        try {
            // Create VS Code terminal with enhanced options
            this._terminal = vscode.window.createTerminal({
                name: this.config.name || 'Enhanced Terminal',
                shellPath: this.config.shell || this._detectShell(),
                cwd: this.config.cwd || process.cwd(),
                env: {
                    ...process.env,
                    ...this.config.env,
                    TERM: 'xterm-256color',
                    COLORTERM: this.config.enableColorSupport ? 'truecolor' : undefined,
                    LANG: this.config.enableUnicodeSupport ? 'en_US.UTF-8' : undefined
                },
                isTransient: this.config.isTransient ?? true,
                hideFromUser: this.config.hideFromUser ?? false,
                iconPath: new vscode.ThemeIcon('terminal')
            });
            // Set up event listeners
            const closeListener = vscode.window.onDidCloseTerminal(terminal => {
                if (terminal === this._terminal) {
                    this._handleExit(0);
                }
            });
            this._disposables.push(closeListener);
            // Show terminal if not hidden
            if (!this.config.hideFromUser) {
                this._terminal.show(true);
            }
            // Initialize terminal with enhanced settings
            await this._initializeTerminal();
            this._isInitialized = true;
            this.state.isConnected = true;
            this.state.isReady = true;
            this._onReady.fire();
            this._onStateChange.fire({ ...this.state });
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this._onError.fire(err);
            this.stats.errorCount++;
            throw err;
        }
    }
    async _initializeTerminal() {
        if (!this._terminal)
            return;
        const initCommands = [
            'clear', // Clear screen
            '\x1b[2J\x1b[0f', // Alternative clear
            '\x1b[?25h', // Show cursor
            '\x1b[?2004h', // Enable bracketed paste if supported
            '\x1b[?1049h', // Alternate screen buffer
            'echo "Enhanced Terminal Ready"', // Ready indicator
            'pwd', // Get current directory
            'echo ""' // New line
        ];
        for (const cmd of initCommands) {
            await this._sendTextToTerminal(cmd, false);
            await this._delay(50); // Small delay between commands
        }
        // Set initial dimensions
        if (this.config.dimensions) {
            await this.resize(this.config.dimensions.columns, this.config.dimensions.rows);
        }
    }
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    async write(data) {
        if (!this._isInitialized || this._isDisposed || !this._terminal) {
            throw new Error('Terminal not initialized or disposed');
        }
        // Process input for special characters and commands
        const processedData = this._processInput(data);
        this._writeQueue.push(processedData);
        await this._processWriteQueue();
        if (this.config.enableCommandHistory && data.trim()) {
            this._addToCommandHistory(data.trim());
        }
    }
    async writeLn(data) {
        await this.write(data + '\n');
    }
    async executeCommand(command, options) {
        const result = await this.executeCommandAdvanced(command, options);
        return result.output;
    }
    async executeCommandAdvanced(command, options) {
        const startTime = Date.now();
        this._lastCommandTime = startTime;
        this.state.isBusy = true;
        this.state.lastCommand = command;
        this._onStateChange.fire({ ...this.state });
        const config = {
            timeout: options?.timeout ?? 30000, // 30 second default timeout
            expectPrompt: options?.expectPrompt ?? true,
            background: options?.background ?? false,
            workingDirectory: options?.workingDirectory
        };
        try {
            // Change working directory if specified
            if (config.workingDirectory && config.workingDirectory !== this.state.currentDirectory) {
                await this.setWorkingDirectory(config.workingDirectory);
            }
            // Send command to terminal
            await this.writeLn(command);
            this._onCommand.fire(command);
            this.stats.totalCommands++;
            // Use sophisticated command completion detection
            const result = await this._waitForCommandCompletion(command, config);
            const responseTime = Date.now() - startTime;
            this._responseTimes.push(responseTime);
            this._updateAverageResponseTime();
            this.stats.averageResponseTime = this._calculateAverageResponseTime(responseTime);
            return result;
        }
        finally {
            this.state.isBusy = false;
            this._onStateChange.fire({ ...this.state });
        }
    }
    async _waitForCommandCompletion(command, config) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            let outputBuffer = '';
            let commandCompleted = false;
            // Sophisticated shell prompt detection patterns
            const shellPrompts = [
                // Bash/Zsh prompts
                /^\$ /, /^bash-\d+\.\d+\$ /, /^zsh-\d+\.\d+\$ /,
                /^[a-zA-Z0-9_]+@[a-zA-Z0-9_]+:[^$]*\$ /,
                /^➜ /, // Oh My Zsh
                /^❯ /, // Starship
                /^→ /, // Pure prompt
                // Windows prompts
                /^[A-Z]:\\.*>/,
                /^PS [A-Z]:\\.*>/,
                // Fish shell
                /^[a-zA-Z0-9_]+@[a-zA-Z0-9_]+ [^$]*\$ /,
                // Generic prompts
                /^> /, /^# /, /^\[.*\]\$ /,
            ];
            // Error pattern detection
            const errorPatterns = [
                /^bash: .*: command not found/,
                /^zsh: command not found: .*/,
                /'.*': command not found/,
                /^Error: /, /^error: /, /^ERROR: /,
                /^Command failed: /,
            ];
            // Background job detection
            const backgroundPatterns = [
                /^\[\d+\]\s+\d+$/, // [1] 1234
                /^\[\d+\]\s+\+/, // [1] + running
                /^\[\d+\]\s+-/, // [1] - stopped
            ];
            const checkCompletion = (data) => {
                outputBuffer += data;
                // Check for background job indicators
                if (config.background) {
                    for (const pattern of backgroundPatterns) {
                        if (pattern.test(data.trim())) {
                            commandCompleted = true;
                            clearTimeout(timeoutId);
                            resolve({
                                command,
                                output: outputBuffer,
                                exitCode: 0,
                                duration: Date.now() - startTime,
                                success: true,
                                background: true,
                                completedAt: new Date()
                            });
                            return;
                        }
                    }
                }
                // Check for error patterns
                for (const pattern of errorPatterns) {
                    if (pattern.test(data.trim())) {
                        commandCompleted = true;
                        clearTimeout(timeoutId);
                        resolve({
                            command,
                            output: outputBuffer,
                            exitCode: 1,
                            duration: Date.now() - startTime,
                            success: false,
                            error: data.trim(),
                            completedAt: new Date()
                        });
                        return;
                    }
                }
                // Check for shell prompt if expected
                if (config.expectPrompt) {
                    for (const pattern of shellPrompts) {
                        if (pattern.test(data.trim())) {
                            commandCompleted = true;
                            clearTimeout(timeoutId);
                            resolve({
                                command,
                                output: outputBuffer,
                                exitCode: 0,
                                duration: Date.now() - startTime,
                                success: true,
                                completedAt: new Date()
                            });
                            return;
                        }
                    }
                }
            };
            // Set up timeout
            const timeoutId = setTimeout(() => {
                if (!commandCompleted) {
                    resolve({
                        command,
                        output: outputBuffer,
                        exitCode: -1,
                        duration: Date.now() - startTime,
                        success: false,
                        error: `Command timed out after ${config.timeout}ms`,
                        completedAt: new Date()
                    });
                }
            }, config.timeout);
            // Listen for output data
            const dataListener = this.events.onData(checkCompletion);
            // Clean up listener on completion
            const cleanup = () => {
                dataListener.dispose();
                clearTimeout(timeoutId);
            };
            // Override resolve to include cleanup
            const originalResolve = resolve;
            resolve = ((result) => {
                cleanup();
                originalResolve(result);
            });
            // Override reject to include cleanup
            const originalReject = reject;
            reject = ((error) => {
                cleanup();
                originalReject(error);
            });
        });
    }
    _calculateAverageResponseTime(currentResponseTime) {
        if (this._responseTimes.length === 0) {
            return currentResponseTime;
        }
        // Use exponential moving average for better performance tracking
        const alpha = 0.1; // Smoothing factor
        const previousAverage = this.stats.averageResponseTime || 0;
        return previousAverage * (1 - alpha) + currentResponseTime * alpha;
    }
    _processInput(data) {
        let processed = data;
        // Handle backspace - using character code to avoid ESLint issues
        if (data.includes(String.fromCharCode(8))) {
            processed = data.replace(new RegExp(String.fromCharCode(8), 'g'), '\x1b[D \x1b[D');
        }
        // Handle special control sequences
        if (this.config.enableBracketedPaste && data.length > 1) {
            processed = `\x1b[200~${processed}\x1b[201~`;
        }
        return processed;
    }
    async _processWriteQueue() {
        if (this._isWriting || this._writeQueue.length === 0)
            return;
        this._isWriting = true;
        try {
            while (this._writeQueue.length > 0 && !this._isDisposed) {
                const chunk = this._writeQueue.shift();
                if (chunk && this._terminal) {
                    await this._sendTextToTerminal(chunk, false);
                    this.stats.totalOutputBytes += chunk.length;
                }
            }
        }
        finally {
            this._isWriting = false;
        }
    }
    async _sendTextToTerminal(text, addNewLine) {
        if (!this._terminal)
            return;
        try {
            this._terminal.sendText(text, addNewLine);
        }
        catch (error) {
            console.error('Error sending text to terminal:', error);
            this.stats.errorCount++;
        }
    }
    async resize(cols, rows) {
        if (this._isDisposed)
            return;
        this.state.dimensions = { columns: cols, rows };
        this.stats.resizeCount++;
        this._onResize.fire({ columns: cols, rows });
        // VS Code handles terminal resizing automatically
        // but we can track the intended dimensions
    }
    async clear() {
        await this._sendTextToTerminal('\x1b[2J\x1b[0f', false);
    }
    async reset() {
        await this._sendTextToTerminal('\x1b[c', false); // Reset terminal
        await this._delay(100);
        await this._initializeTerminal();
    }
    async sendSignal(signal) {
        // VS Code doesn't support sending signals directly
        // This is a placeholder for potential future enhancement
        console.log(`Signal ${signal} requested but not supported in VS Code terminals`);
    }
    async getWorkingDirectory() {
        if (this.config.workingDirectoryTracking) {
            return this.state.currentDirectory;
        }
        return this.config.cwd || process.cwd();
    }
    async setWorkingDirectory(path) {
        await this.executeCommand(`cd "${path}"`);
        this.state.currentDirectory = path;
        this._onStateChange.fire({ ...this.state });
    }
    getCommandHistory() {
        return [...this._commandHistory];
    }
    clearCommandHistory() {
        this._commandHistory = [];
        this.state.commandHistory = [];
        this._onStateChange.fire({ ...this.state });
    }
    _addToCommandHistory(command) {
        if (!this.config.enableCommandHistory)
            return;
        this._commandHistory.push(command);
        if (this._commandHistory.length > (this.config.maxCommandHistory || 1000)) {
            this._commandHistory.shift();
        }
        this.state.commandHistory = [...this._commandHistory];
    }
    enableLogging(logPath) {
        if (logPath) {
            this._logPath = logPath;
        }
        if (!this._logStream) {
            this._logStream = vscode.window.createOutputChannel('Terminal Logs');
        }
    }
    disableLogging() {
        if (this._logStream) {
            this._logStream.dispose();
            this._logStream = undefined;
        }
        this._logPath = undefined;
    }
    _updateAverageResponseTime() {
        if (this._responseTimes.length > 0) {
            this.stats.averageResponseTime = this._responseTimes.reduce((a, b) => a + b, 0) / this._responseTimes.length;
        }
    }
    _handleExit(exitCode) {
        this.state.exitCode = exitCode;
        this.state.isConnected = false;
        this.state.isReady = false;
        this._onExit.fire(exitCode);
        this._onStateChange.fire({ ...this.state });
        this.dispose();
    }
    //Method removed - moved to InteractiveSession
    dispose() {
        if (this._isDisposed)
            return;
        this._isDisposed = true;
        // Clear timers
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
            this._outputFlushTimer = null;
        }
        // Dispose terminal
        if (this._terminal) {
            try {
                this._terminal.dispose();
            }
            catch (error) {
                console.error('Error disposing terminal:', error);
            }
            this._terminal = null;
        }
        // Dispose all resources
        this._disposables.forEach(d => {
            try {
                d.dispose();
            }
            catch (error) {
                console.error('Error disposing resource:', error);
            }
        });
        this._disposables = [];
        // Disable logging
        this.disableLogging();
        // Clear buffers
        this._writeQueue = [];
        this._outputBuffer = '';
    }
}
exports.TerminalProcess = TerminalProcess;
/**
 * Pseudoterminal with advanced features using actual child processes
 */
class PseudoTerminal {
    config;
    exitCallback;
    _onDidWriteEmitter = new vscode.EventEmitter();
    _onDidCloseEmitter = new vscode.EventEmitter();
    onDidWrite = this._onDidWriteEmitter.event;
    onDidClose = this._onDidCloseEmitter.event;
    _childProcess = null;
    _isOpen = false;
    _isDisposed = false;
    _outputBuffer = '';
    _outputFlushTimer = null;
    _dimensions;
    _cwd;
    _shell;
    _shellArgs;
    constructor(config = {}, exitCallback) {
        this.config = config;
        this.exitCallback = exitCallback;
        this._dimensions = config.dimensions || { columns: 120, rows: 30 };
        this._cwd = config.cwd || process.cwd();
        this._shell = this._resolveShellPath(config.shell || this._detectShell());
        this._shellArgs = config.args || this._getShellArgs();
    }
    open(initialDimensions) {
        if (this._isOpen || this._isDisposed)
            return;
        this._isOpen = true;
        this._dimensions = initialDimensions || this._dimensions;
        try {
            // Spawn the actual shell process
            this._childProcess = (0, child_process_1.spawn)(this._shell, this._shellArgs, {
                cwd: this._cwd,
                env: {
                    ...process.env,
                    ...this.config.env,
                    TERM: 'xterm-256color',
                    COLUMNS: this._dimensions?.columns?.toString() || '120',
                    LINES: this._dimensions?.rows?.toString() || '30'
                },
                stdio: ['pipe', 'pipe', 'pipe']
            });
            // Set up event handlers for the child process
            this._childProcess.stdout?.on('data', (data) => {
                this._handleOutput(data.toString());
            });
            this._childProcess.stderr?.on('data', (data) => {
                this._handleOutput(data.toString());
            });
            this._childProcess.on('exit', (code, signal) => {
                const exitCode = code !== null ? code : (signal ? -1 : 0);
                this._cleanupAndClose(exitCode);
            });
            this._childProcess.on('error', (error) => {
                logger_1.Logger.instance.error('Terminal process error:', error);
                this._handleOutput(`\r\n${error.message}\r\n`);
                this._cleanupAndClose(-1);
            });
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to spawn terminal process:', error);
            this._cleanupAndClose(-1);
        }
    }
    _detectShell() {
        const platform = os.platform();
        if (platform === 'win32') {
            return process.env.ComSpec || path.join(process.env.SystemRoot || 'C:\\Windows', 'System32', 'cmd.exe');
        }
        else if (platform === 'darwin') {
            return process.env.SHELL || '/bin/zsh';
        }
        else {
            return process.env.SHELL || '/bin/bash';
        }
    }
    _getShellArgs() {
        const platform = os.platform();
        const shell = this.config.shell || this._detectShell();
        if (platform === 'win32') {
            if (shell.includes('powershell') || shell.includes('pwsh')) {
                return ['-NoExit', '-Command', ''];
            }
            else if (shell.includes('cmd')) {
                return ['/k'];
            }
        }
        else {
            // Unix-like systems
            return ['-i']; // Interactive mode
        }
        return [];
    }
    _resolveShellPath(shell) {
        if (path.isAbsolute(shell)) {
            return shell;
        }
        // Try to resolve shell from PATH
        const pathEnv = process.env.PATH || '';
        const pathSeparator = os.platform() === 'win32' ? ';' : ':';
        const paths = pathEnv.split(pathSeparator);
        for (const dir of paths) {
            const fullPath = path.join(dir, shell);
            try {
                // In a real implementation, you'd check if file exists and is executable
                return fullPath;
            }
            catch {
                continue;
            }
        }
        return shell; // Fallback to original
    }
    close() {
        this._cleanupAndClose(0);
    }
    handleInput(data) {
        if (this._childProcess && this._childProcess.stdin && !this._isDisposed) {
            this._childProcess.stdin.write(data);
        }
    }
    _handleOutput(data) {
        if (!data || this._isDisposed)
            return;
        this._outputBuffer += data;
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
        }
        this._outputFlushTimer = setTimeout(() => {
            this._flushOutputBuffer();
        }, 16); // ~60fps for smooth output
    }
    _flushOutputBuffer() {
        if (this._isDisposed || !this._outputBuffer) {
            this._outputFlushTimer = null;
            return;
        }
        try {
            this._onDidWriteEmitter.fire(this._outputBuffer);
            this._outputBuffer = '';
        }
        catch (error) {
            console.error('Error flushing terminal output:', error);
        }
        finally {
            this._outputFlushTimer = null;
        }
    }
    setDimensions(dimensions) {
        this._dimensions = dimensions;
        if (this._childProcess && !this._childProcess.killed) {
            // Send resize signal to child process
            try {
                this._childProcess.kill('SIGWINCH');
            }
            catch (error) {
                logger_1.Logger.instance.error('Error resizing terminal:', error);
            }
        }
    }
    _cleanupAndClose(exitCode) {
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
            this._outputFlushTimer = null;
            this._flushOutputBuffer();
        }
        if (this._childProcess && !this._childProcess.killed) {
            this._childProcess.kill();
            this._childProcess = null;
        }
        this._isOpen = false;
        this._onDidCloseEmitter.fire(exitCode);
        this.exitCallback?.(exitCode);
    }
    dispose() {
        if (this._isDisposed)
            return;
        this._isDisposed = true;
        this._cleanupAndClose(0);
    }
    // Additional methods
    getChildProcess() {
        return this._childProcess;
    }
    async executeCommand(command) {
        if (this._childProcess && this._childProcess.stdin && !this._childProcess.killed) {
            return new Promise((resolve, reject) => {
                let output = '';
                const timeout = setTimeout(() => {
                    reject(new Error('Command execution timeout'));
                }, 30000);
                const dataHandler = (data) => {
                    output += data.toString();
                };
                if (this._childProcess && this._childProcess.stdout) {
                    this._childProcess.stdout.on('data', dataHandler);
                }
                if (this._childProcess && this._childProcess.stderr) {
                    this._childProcess.stderr.on('data', dataHandler);
                }
                if (this._childProcess && this._childProcess.stdin) {
                    this._childProcess.stdin.write(command + '\n');
                }
                // Simple command completion detection (this could be enhanced)
                setTimeout(() => {
                    clearTimeout(timeout);
                    if (this._childProcess && this._childProcess.stdout) {
                        this._childProcess.stdout.off('data', dataHandler);
                    }
                    if (this._childProcess && this._childProcess.stderr) {
                        this._childProcess.stderr.off('data', dataHandler);
                    }
                    resolve(output);
                }, 1000);
            });
        }
        throw new Error('Terminal not initialized');
    }
    getStats() {
        // Return basic stats for the child process
        if (this._childProcess) {
            return {
                totalCommands: 0,
                totalOutputBytes: 0,
                averageResponseTime: 0,
                uptimeSeconds: Math.floor((Date.now() - this._childProcess.spawnargs.length) / 1000),
                errorCount: 0,
                resizeCount: 0
            };
        }
        return null;
    }
    getState() {
        if (this._childProcess) {
            return {
                isConnected: !this._childProcess.killed,
                isReady: !this._childProcess.killed,
                currentDirectory: this._cwd,
                shellType: this._shell,
                dimensions: this._dimensions || { columns: 120, rows: 30 },
                lastCommand: '',
                commandHistory: [],
                isBusy: false,
                exitCode: this._childProcess.exitCode || undefined
            };
        }
        return null;
    }
}
exports.PseudoTerminal = PseudoTerminal;
/**
 * Terminal Actions integration class
 */
class TerminalActions {
    _session;
    _terminalProvider;
    _actionHistory = [];
    _activeActions = new Map();
    _isEnabled = true;
    constructor(session) {
        this._session = session;
        this._terminalProvider = codeActionProvider_1.codeActionManager.getProvider('terminal')
            || new codeActionProvider_1.TerminalCodeActionProvider();
        this._initialize();
    }
    _initialize() {
        // Register command handlers for terminal actions
        const commands = [
            vscode.commands.registerCommand('codessa.installCommand', this._installCommand.bind(this)),
            vscode.commands.registerCommand('codessa.fixPackageError', this._fixPackageError.bind(this)),
            vscode.commands.registerCommand('codessa.fixPermissionError', this._fixPermissionError.bind(this)),
            vscode.commands.registerCommand('codessa.fixConnectionError', this._fixConnectionError.bind(this)),
            vscode.commands.registerCommand('codessa.runTerminalQuickFix', this._runTerminalQuickFix.bind(this)),
            vscode.commands.registerCommand('codessa.showTerminalActions', this._showTerminalActions.bind(this))
        ];
        // Store disposables for cleanup
        const sessionWithDisposables = this._session;
        sessionWithDisposables.disposables = sessionWithDisposables.disposables || [];
        sessionWithDisposables.disposables.push(...commands);
        // Set up event listeners
        this._setupEventListeners();
    }
    _setupEventListeners() {
        // Listen for terminal output to detect errors
        const outputListener = this._session.onData((output) => {
            if (this._isEnabled) {
                this._analyzeTerminalOutput(output);
            }
        });
        const errorListener = this._session.onError((error) => {
            this._handleTerminalError(error);
        });
        const sessionWithDisposables = this._session;
        sessionWithDisposables.disposables = sessionWithDisposables.disposables || [];
        sessionWithDisposables.disposables.push(outputListener, errorListener);
    }
    async _analyzeTerminalOutput(output) {
        try {
            const actions = this._terminalProvider.analyzeTerminalOutput(output);
            if (actions.length > 0) {
                // Register actions for later use
                actions.forEach((action, index) => {
                    const actionId = `terminal-action-${Date.now()}-${index}`;
                    this._activeActions.set(actionId, action);
                });
                // Show notification with quick fix option
                const showActions = await vscode.window.showInformationMessage(`Found ${actions.length} potential fix(es) for terminal output`, 'Show Quick Fixes', 'Apply Best Fix', 'Ignore');
                switch (showActions) {
                    case 'Show Quick Fixes':
                        await this._showTerminalActions();
                        break;
                    case 'Apply Best Fix':
                        await this._applyBestAction(actions);
                        break;
                }
            }
        }
        catch (error) {
            console.error('Error analyzing terminal output:', error);
        }
    }
    _handleTerminalError(error) {
        console.error('Terminal error detected:', error);
        // Create a generic error fix action
        const action = new vscode.CodeAction(`$(error) Fix terminal error: ${error.message}`, vscode.CodeActionKind.QuickFix);
        action.command = {
            command: 'codessa.runTerminalQuickFix',
            title: 'Run terminal error fix',
            arguments: [error.message]
        };
        const actionId = `terminal-error-${Date.now()}`;
        this._activeActions.set(actionId, action);
        vscode.window.showErrorMessage(`Terminal error detected: ${error.message}`, 'Show Fix').then(selection => {
            if (selection === 'Show Fix') {
                this._showTerminalActions();
            }
        });
    }
    async _installCommand(command) {
        try {
            let installCommand = '';
            // Determine the appropriate install command based on the system
            const platform = process.platform;
            if (platform === 'win32') {
                // Windows - try Chocolatey first, then scoop
                installCommand = `choco install ${command} || scoop install ${command}`;
            }
            else if (platform === 'darwin') {
                // macOS - try Homebrew
                installCommand = `brew install ${command}`;
            }
            else {
                // Linux - try apt, then yum, then snap
                installCommand = `sudo apt update && sudo apt install -y ${command} || sudo yum install -y ${command} || sudo snap install ${command}`;
            }
            await this._session.writeLn(installCommand);
            const result = {
                action: new vscode.CodeAction(`Install ${command}`),
                applied: true,
                timestamp: new Date(),
                output: `Installing ${command}...`
            };
            this._actionHistory.push(result);
            vscode.window.showInformationMessage(`Installing ${command}...`);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to install ${command}: ${error}`);
        }
    }
    async _fixPackageError(errorOutput) {
        try {
            // Common package error fixes
            if (errorOutput.includes('EACCES') || errorOutput.includes('permission')) {
                // Permission error - try with sudo or fix permissions
                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm');
                await this._session.writeLn('sudo chmod -R 755 ~/.npm');
            }
            else if (errorOutput.includes('ENOTFOUND')) {
                // Network error - check connection
                await this._session.writeLn('ping -c 4 registry.npmjs.org');
            }
            else if (errorOutput.includes('ETIMEDOUT')) {
                // Timeout - retry with different registry
                await this._session.writeLn('npm config set registry https://registry.npmjs.org/');
            }
            else {
                // Generic fix - clear cache and retry
                await this._session.writeLn('npm cache clean --force');
                await this._session.writeLn('rm -rf node_modules package-lock.json');
                await this._session.writeLn('npm install');
            }
            const result = {
                action: new vscode.CodeAction('Fix package error'),
                applied: true,
                timestamp: new Date(),
                output: 'Applied package error fix'
            };
            this._actionHistory.push(result);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to fix package error: ${error}`);
        }
    }
    async _fixPermissionError(errorOutput) {
        try {
            // Fix permission issues
            await this._session.writeLn('chmod +x $(pwd)/*');
            await this._session.writeLn('find . -type f -name "*.sh" -exec chmod +x {} \\;');
            // If it's a Node.js permission issue
            if (errorOutput.includes('node') || errorOutput.includes('npm')) {
                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm ~/.node-gyp');
            }
            const result = {
                action: new vscode.CodeAction('Fix permission error'),
                applied: true,
                timestamp: new Date(),
                output: 'Fixed permission issues'
            };
            this._actionHistory.push(result);
            vscode.window.showInformationMessage('Fixed permission issues');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to fix permissions: ${error}`);
        }
    }
    async _fixConnectionError(errorOutput) {
        try {
            // Diagnose and fix connection issues
            await this._session.writeLn('ping -c 4 8.8.8.8'); // Test basic connectivity
            await this._session.writeLn('nslookup google.com'); // Test DNS
            // Check proxy settings
            await this._session.writeLn('env | grep -i proxy');
            // If it's a specific service, try alternative endpoints
            if (errorOutput.includes('registry.npmjs.org')) {
                await this._session.writeLn('npm config set registry https://registry.npmmirror.com');
            }
            const result = {
                action: new vscode.CodeAction('Fix connection error'),
                applied: true,
                timestamp: new Date(),
                output: 'Diagnosed and attempted to fix connection issues'
            };
            this._actionHistory.push(result);
            vscode.window.showInformationMessage('Diagnosed connection issues');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to fix connection: ${error}`);
        }
    }
    async _runTerminalQuickFix(errorMessage) {
        try {
            // Generic terminal error fixes
            if (errorMessage.includes('command not found')) {
                const command = errorMessage.match(/command not found:?\s+(.+?)(?:\s|$)/i)?.[1];
                if (command) {
                    await this._installCommand(command);
                    return;
                }
            }
            // Run a generic diagnostic
            await this._session.writeLn('echo "Terminal diagnostic:"');
            await this._session.writeLn('pwd && ls -la');
            await this._session.writeLn('echo $PATH');
            await this._session.writeLn('which bash');
            vscode.window.showInformationMessage('Ran terminal diagnostic');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to run terminal fix: ${error}`);
        }
    }
    async _showTerminalActions() {
        const actions = Array.from(this._activeActions.values());
        if (actions.length === 0) {
            vscode.window.showInformationMessage('No terminal actions available');
            return;
        }
        const items = actions.map(action => ({
            label: action.title,
            description: action.kind?.value || 'Quick Fix',
            action: action
        }));
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select a terminal quick fix to apply'
        });
        if (selected) {
            await this._applyAction(selected.action);
        }
    }
    async _applyBestAction(actions) {
        if (actions.length === 0)
            return;
        // Apply the first (highest priority) action
        await this._applyAction(actions[0]);
    }
    async _applyAction(action) {
        try {
            const dummyDoc = {
                uri: vscode.Uri.parse('terminal://output'),
                getText: () => '',
                getWordRangeAtPosition: () => undefined,
                lineAt: () => ({ text: '', range: new vscode.Range(0, 0, 0, 0) }),
                offsetAt: () => 0,
                positionAt: () => new vscode.Position(0, 0),
                validateRange: (range) => range,
                validatePosition: (position) => position
            };
            const success = await this._terminalProvider.applyQuickFix(action, dummyDoc);
            const result = {
                action,
                applied: success,
                timestamp: new Date(),
                output: success ? 'Action applied successfully' : 'Failed to apply action'
            };
            this._actionHistory.push(result);
            if (success) {
                vscode.window.showInformationMessage('Quick fix applied successfully');
            }
            else {
                vscode.window.showErrorMessage('Failed to apply quick fix');
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Error applying action: ${error}`);
        }
    }
    getActionHistory() {
        return [...this._actionHistory];
    }
    clearActionHistory() {
        this._actionHistory.length = 0;
    }
    enable() {
        this._isEnabled = true;
    }
    disable() {
        this._isEnabled = false;
    }
    isEnabled() {
        return this._isEnabled;
    }
    getActiveActions() {
        return Array.from(this._activeActions.values());
    }
    dispose() {
        this._activeActions.clear();
        this._actionHistory.length = 0;
    }
}
exports.TerminalActions = TerminalActions;
/**
 * Interactive session with Code Action integration
 */
class InteractiveSession {
    id;
    config;
    state;
    stats;
    events;
    // Code Action patterns for terminal error detection
    _quickFixPatterns = [];
    _iconRegistry = new Map();
    _actionHistory = [];
    _activeActions = new Map();
    _isCodeActionsEnabled = true;
    // Terminal Actions integration
    _terminalActions = null;
    _actionProvider;
    _pty;
    _terminal = null;
    _isDisposed = false;
    _pendingActions = [];
    // Event forwarding
    _dataEmitter = new vscode.EventEmitter();
    _exitEmitter = new vscode.EventEmitter();
    _readyEmitter = new vscode.EventEmitter();
    _resizeEmitter = new vscode.EventEmitter();
    _commandEmitter = new vscode.EventEmitter();
    _errorEmitter = new vscode.EventEmitter();
    _stateChangeEmitter = new vscode.EventEmitter();
    onData = this._dataEmitter.event;
    onExit = this._exitEmitter.event;
    onReady = this._readyEmitter.event;
    onResize = this._resizeEmitter.event;
    onCommand = this._commandEmitter.event;
    onError = this._errorEmitter.event;
    onStateChange = this._stateChangeEmitter.event;
    constructor(config = {}) {
        this.id = `interactive-session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        this.config = {
            name: 'Interactive Terminal Session',
            ...config
        };
        // Initialize providers and actions
        this._actionProvider = new TerminalActionProvider();
        this._terminalActions = new TerminalActions(this);
        this._pty = new PseudoTerminal({
            shell: this.config.shell || 'bash',
            cwd: this.config.cwd || process.cwd()
        }, (exitCode) => {
            this._exitEmitter.fire(exitCode || 0);
        });
        // Forward events from the pseudoterminal and analyze output
        this._pty.onDidWrite((data) => {
            this._dataEmitter.fire(data);
            this._analyzeTerminalOutput(data);
        });
        this._pty.onDidClose((exitCode) => {
            this._exitEmitter.fire(exitCode);
            this.dispose();
        });
        // Initialize state and stats
        this.state = {
            isConnected: false,
            isReady: false,
            currentDirectory: this.config.cwd || process.cwd(),
            shellType: this.config.shell || 'bash',
            dimensions: this.config.dimensions || { columns: 120, rows: 30 },
            lastCommand: '',
            commandHistory: [],
            isBusy: false
        };
        this.stats = {
            totalCommands: 0,
            totalOutputBytes: 0,
            averageResponseTime: 0,
            uptimeSeconds: 0,
            errorCount: 0,
            resizeCount: 0
        };
        this.events = {
            onData: this.onData,
            onExit: this.onExit,
            onReady: this.onReady,
            onResize: this.onResize,
            onCommand: this.onCommand,
            onError: this.onError,
            onStateChange: this.onStateChange
        };
        // Initialize Code Action patterns
        this._initializeQuickFixPatterns();
        this._initializeIconRegistry();
        // Initialize Terminal Actions
        this._terminalActions = new TerminalActions(this);
        this._createTerminal();
    }
    _createTerminal() {
        this._terminal = vscode.window.createTerminal({
            name: this.config.name || 'Interactive Terminal Session',
            pty: this._pty,
            iconPath: new vscode.ThemeIcon('terminal')
        });
        if (!this.config.hideFromUser) {
            this._terminal.show();
        }
    }
    _initializeQuickFixPatterns() {
        // Terminal Error Patterns
        this._addPattern({
            pattern: /bash:\s+(.+?):\s+command not found/i,
            action: 'install-missing-command',
            priority: 100,
            category: 'terminal',
            fix: (match, doc, range) => {
                const command = match[1];
                const action = new vscode.CodeAction(`Install missing command: ${command}`, vscode.CodeActionKind.QuickFix);
                action.command = {
                    command: 'codessa.installCommand',
                    title: `Install ${command}`,
                    arguments: [command, 'terminal']
                };
                action.diagnostics = [{
                        range,
                        message: `Command '${command}' not found`,
                        severity: vscode.DiagnosticSeverity.Error,
                        source: 'terminal'
                    }];
                return action;
            }
        });
        // Import Error Patterns
        this._addPattern({
            pattern: /Cannot find module\s+['"](.+?)['"]/i,
            action: 'fix-import',
            priority: 95,
            category: 'imports',
            fix: (match, _doc, _range) => {
                const property = match[1];
                const action = new vscode.CodeAction(`Add property '${property}' to interface`, vscode.CodeActionKind.QuickFix);
                action.command = {
                    command: 'codessa.addProperty',
                    title: `Add property ${property}`,
                    arguments: [property, _doc, _range]
                };
                return action;
            }
        });
        // TypeScript Error Patterns
        this._addPattern({
            pattern: /Property\s+['"](.+?)['"]\s+does not exist on type/i,
            action: 'add-property',
            priority: 90,
            category: 'typescript',
            fix: (match, _doc, _range) => {
                const property = match[1];
                const action = new vscode.CodeAction(`Add property '${property}' to interface`, vscode.CodeActionKind.QuickFix);
                action.command = {
                    command: 'codessa.addProperty',
                    title: `Add property ${property}`,
                    arguments: [property, _doc, _range]
                };
                return action;
            }
        });
        // ESLint Error Patterns
        this._addPattern({
            pattern: /(.+?)\s+is not defined/i,
            action: 'define-variable',
            priority: 85,
            category: 'eslint',
            fix: (match, doc, range) => {
                const variable = match[1];
                const action = new vscode.CodeAction(`Define variable: ${variable}`, vscode.CodeActionKind.QuickFix);
                action.edit = new vscode.WorkspaceEdit();
                action.edit.insert(doc.uri, new vscode.Position(range.start.line, 0), `const ${variable} = ;\n`);
                return action;
            }
        });
        // Security Vulnerability Patterns
        this._addPattern({
            pattern: /Potential security vulnerability:\s+(.+?)/i,
            action: 'fix-security',
            priority: 100,
            category: 'security',
            fix: (match, doc, range) => {
                const issue = match[1];
                const action = new vscode.CodeAction(`Fix security issue: ${issue}`, vscode.CodeActionKind.QuickFix);
                action.command = {
                    command: 'codessa.fixSecurity',
                    title: `Fix ${issue}`,
                    arguments: [issue, doc, range]
                };
                return action;
            }
        });
    }
    _initializeIconRegistry() {
        this._registerIcon({
            name: 'lightbulb',
            icon: new vscode.ThemeIcon('lightbulb'),
            category: 'suggestion',
            description: 'General suggestions and improvements'
        });
        this._registerIcon({
            name: 'error',
            icon: new vscode.ThemeIcon('error'),
            category: 'error',
            description: 'Error conditions requiring immediate attention'
        });
        this._registerIcon({
            name: 'warning',
            icon: new vscode.ThemeIcon('warning'),
            category: 'warning',
            description: 'Warning conditions that should be addressed'
        });
        this._registerIcon({
            name: 'info',
            icon: new vscode.ThemeIcon('info'),
            category: 'info',
            description: 'Informational suggestions and hints'
        });
        this._registerIcon({
            name: 'tools',
            icon: new vscode.ThemeIcon('tools'),
            category: 'refactor',
            description: 'Refactoring and code improvement actions'
        });
        this._registerIcon({
            name: 'package',
            icon: new vscode.ThemeIcon('package'),
            category: 'dependency',
            description: 'Package and dependency management'
        });
        this._registerIcon({
            name: 'terminal',
            icon: new vscode.ThemeIcon('terminal'),
            category: 'terminal',
            description: 'Terminal and command-line related actions'
        });
        this._registerIcon({
            name: 'debug',
            icon: new vscode.ThemeIcon('debug'),
            category: 'debug',
            description: 'Debugging and diagnostic actions'
        });
        this._registerIcon({
            name: 'security',
            icon: new vscode.ThemeIcon('shield'),
            category: 'security',
            description: 'Security-related fixes and improvements'
        });
    }
    _addPattern(pattern) {
        this._quickFixPatterns.push(pattern);
    }
    _registerIcon(icon) {
        this._iconRegistry.set(icon.name, icon);
    }
    _getIconForCategory(category) {
        const icon = this._iconRegistry.get(category);
        return icon ? `$(${icon.icon.id})` : '💡';
    }
    // Code Action analysis methods
    async _analyzeTerminalOutput(output) {
        if (!this._isCodeActionsEnabled)
            return;
        try {
            const actions = this._analyzeOutputForActions(output);
            if (actions.length > 0) {
                // Register actions for later use
                actions.forEach((action, index) => {
                    const actionId = `terminal-action-${Date.now()}-${index}`;
                    this._activeActions.set(actionId, action);
                });
                // Show notification with quick fix option
                const showActions = await vscode.window.showInformationMessage(`Found ${actions.length} potential fix(es) for terminal output`, 'Show Quick Fixes', 'Apply Best Fix', 'Ignore');
                switch (showActions) {
                    case 'Show Quick Fixes':
                        await this._showTerminalActions();
                        break;
                    case 'Apply Best Fix':
                        await this._applyBestAction(actions);
                        break;
                }
            }
        }
        catch (error) {
            console.error('Error analyzing terminal output:', error);
        }
    }
    _analyzeOutputForActions(output) {
        const actions = [];
        for (const pattern of this._quickFixPatterns) {
            const matches = output.match(pattern.pattern);
            if (matches) {
                try {
                    // Create a dummy document and range for the pattern fix
                    const dummyDoc = {
                        uri: vscode.Uri.parse('terminal://output'),
                        getText: () => output
                    };
                    const dummyRange = new vscode.Range(0, 0, 0, 0);
                    const action = pattern.fix(matches, dummyDoc, dummyRange);
                    action.title = `${this._getIconForCategory(pattern.category)} ${action.title}`;
                    actions.push(action);
                }
                catch (error) {
                    console.error('Error creating code action:', error);
                }
            }
        }
        // Store actions in pending actions for later processing
        this._pendingActions.push(...actions);
        return actions;
    }
    async _showTerminalActions() {
        const actions = Array.from(this._activeActions.values());
        if (actions.length === 0) {
            vscode.window.showInformationMessage('No terminal actions available');
            return;
        }
        const items = actions.map(action => ({
            label: action.title,
            description: action.kind?.value || 'Quick Fix',
            action: action
        }));
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select a terminal quick fix to apply'
        });
        if (selected) {
            await this._applyAction(selected.action);
        }
    }
    async _applyBestAction(actions) {
        if (actions.length === 0)
            return;
        // Apply the first (highest priority) action
        await this._applyAction(actions[0]);
    }
    async _applyAction(action) {
        try {
            if (action.edit) {
                const success = await vscode.workspace.applyEdit(action.edit);
                if (success) {
                    vscode.window.showInformationMessage('Quick fix applied successfully');
                }
                return success;
            }
            else if (action.command) {
                await vscode.commands.executeCommand(action.command.command, ...action.command.arguments || []);
                vscode.window.showInformationMessage('Quick fix applied successfully');
                return true;
            }
            else {
                return false;
            }
        }
        catch (error) {
            console.error('Error applying quick fix:', error);
            vscode.window.showErrorMessage('Failed to apply quick fix');
            return false;
        }
    }
    // Public Code Action methods
    getActionHistory() {
        return [...this._actionHistory];
    }
    clearActionHistory() {
        this._actionHistory.length = 0;
    }
    enableCodeActions() {
        this._isCodeActionsEnabled = true;
    }
    disableCodeActions() {
        this._isCodeActionsEnabled = false;
    }
    isCodeActionsEnabled() {
        return this._isCodeActionsEnabled;
    }
    getActiveActions() {
        return Array.from(this._activeActions.values());
    }
    // Terminal Actions integration methods
    getTerminalActions() {
        return this._terminalActions;
    }
    enableTerminalActions() {
        if (this._terminalActions) {
            this._terminalActions.enable();
        }
    }
    disableTerminalActions() {
        if (this._terminalActions) {
            this._terminalActions.disable();
        }
    }
    isTerminalActionsEnabled() {
        return this._terminalActions ? this._terminalActions.isEnabled() : false;
    }
    getTerminalActionHistory() {
        return this._terminalActions ? this._terminalActions.getActionHistory() : [];
    }
    clearTerminalActionHistory() {
        if (this._terminalActions) {
            this._terminalActions.clearActionHistory();
        }
    }
    // ITerminalProcess implementation
    async initialize() {
        // The terminal is already created and will initialize when opened
        this.state.isConnected = true;
        this.state.isReady = true;
        this._readyEmitter.fire();
        this._stateChangeEmitter.fire({ ...this.state });
    }
    async write(data) {
        if (this._isDisposed)
            return;
        this._pty.handleInput(data);
        // Analyze output for Code Actions if enabled
        this._analyzeTerminalOutput(data);
    }
    async writeLn(data) {
        await this.write(data + '\n');
    }
    async executeCommand(command, options) {
        // Use options parameter for timeout handling
        const timeout = options?.timeout || 30000;
        const workingDirectory = options?.workingDirectory;
        const childProcess = this._pty.getChildProcess();
        if (childProcess) {
            // Log execution details if options provided
            if (options && (options.background || options.expectPrompt || workingDirectory)) {
                logger_1.Logger.instance.info(`Executing command with options: background=${options.background}, expectPrompt=${options.expectPrompt}, workingDirectory=${workingDirectory}, timeout=${timeout}`);
            }
            return await this._pty.executeCommand(command);
        }
        throw new Error('Terminal not initialized');
    }
    async executeCommandAdvanced(command, options) {
        const childProcess = this._pty.getChildProcess();
        if (childProcess && !childProcess.killed) {
            // Create a basic CommandResult using the child process
            const startTime = Date.now();
            try {
                const output = await this._pty.executeCommand(command);
                // Log command execution details if options were provided
                if (options && (options.timeout !== undefined || options.workingDirectory || options.background)) {
                    logger_1.Logger.instance.info('Executing command with options:', {
                        command,
                        timeout: options.timeout,
                        workingDirectory: options.workingDirectory,
                        background: options.background,
                        expectPrompt: options.expectPrompt
                    });
                }
                return {
                    exitCode: 0,
                    output,
                    command,
                    duration: Date.now() - startTime,
                    success: true,
                    completedAt: new Date()
                };
            }
            catch (error) {
                return {
                    exitCode: -1,
                    output: error instanceof Error ? error.message : String(error),
                    command,
                    duration: Date.now() - startTime,
                    success: false,
                    completedAt: new Date()
                };
            }
        }
        throw new Error('Terminal not initialized');
    }
    async resize(cols, rows) {
        if (this._isDisposed)
            return;
        this._pty.setDimensions({ columns: cols, rows });
        this.state.dimensions = { columns: cols, rows };
        this._resizeEmitter.fire({ columns: cols, rows });
        this._stateChangeEmitter.fire({ ...this.state });
    }
    async clear() {
        await this.write('\x1b[2J\x1b[0f');
    }
    async reset() {
        await this.write('\x1b[c');
    }
    async sendSignal(signal) {
        // Not supported in VS Code terminals
        console.log(`Signal ${signal} not supported in VS Code terminals`);
    }
    async getWorkingDirectory() {
        return this.state.currentDirectory;
    }
    async setWorkingDirectory(path) {
        await this.executeCommand(`cd "${path}"`);
        this.state.currentDirectory = path;
        this._stateChangeEmitter.fire({ ...this.state });
    }
    getCommandHistory() {
        // Return command history from the child process state
        const state = this._pty.getState();
        return state?.commandHistory || [];
    }
    clearCommandHistory() {
        // Clear command history - this would need to be implemented in the child process
        logger_1.Logger.instance.info('Command history cleared');
    }
    enableLogging(logPath) {
        // Enable logging - store the path for future use
        logger_1.Logger.instance.info(`Logging enabled${logPath ? ` to ${logPath}` : ''}`);
    }
    disableLogging() {
        // Disable logging
        logger_1.Logger.instance.info('Logging disabled');
    }
    dispose() {
        if (this._isDisposed)
            return;
        this._isDisposed = true;
        if (this._terminal) {
            this._terminal.dispose();
            this._terminal = null;
        }
        this._pty.dispose();
        // Dispose emitters
        this._dataEmitter.dispose();
        this._exitEmitter.dispose();
        this._readyEmitter.dispose();
        this._resizeEmitter.dispose();
        this._commandEmitter.dispose();
        this._errorEmitter.dispose();
        this._stateChangeEmitter.dispose();
    }
}
exports.InteractiveSession = InteractiveSession;
//# sourceMappingURL=pseudoTerminal.js.map