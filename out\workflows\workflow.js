"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Workflow = void 0;
const events_1 = require("events");
class Workflow {
    _id;
    _name;
    _steps = new Map();
    _transitions = new Map();
    _initialStepId = null;
    _emitter = new events_1.EventEmitter();
    constructor(id, name) {
        this._id = id;
        this._name = name;
    }
    /**
     * Get the workflow ID
     */
    get id() {
        return this._id;
    }
    /**
     * Get the workflow name
     */
    get name() {
        return this._name;
    }
    /**
     * Add a step to the workflow
     */
    addStep(step) {
        if (this._steps.has(step.id)) {
            throw new Error(`Step with ID '${step.id}' already exists in workflow '${this._id}'`);
        }
        this._steps.set(step.id, step);
        this._transitions.set(step.id, new Set());
    }
    /**
     * Set the initial step of the workflow
     */
    setInitialStep(stepId) {
        if (!this._steps.has(stepId)) {
            throw new Error(`Step with ID '${stepId}' not found in workflow '${this._id}'`);
        }
        this._initialStepId = stepId;
    }
    /**
     * Add a transition between two steps
     */
    addTransition(fromStepId, toStepId) {
        if (!this._steps.has(fromStepId)) {
            throw new Error(`Source step '${fromStepId}' not found in workflow '${this._id}'`);
        }
        if (!this._steps.has(toStepId)) {
            throw new Error(`Target step '${toStepId}' not found in workflow '${this._id}'`);
        }
        const transitions = this._transitions.get(fromStepId);
        if (transitions) {
            transitions.add(toStepId);
        }
        else {
            this._transitions.set(fromStepId, new Set([toStepId]));
        }
    }
    /**
     * Get the next step based on the current step and result
     */
    getNextStep(currentStepId, result) {
        // If nextStepId is explicitly set in the result, use that
        if (result.nextStepId !== undefined && result.nextStepId !== null) {
            return result.nextStepId; // This is now properly typed as string | null
        }
        // Otherwise, use the first available transition
        const transitions = this._transitions.get(currentStepId);
        if (transitions && transitions.size > 0) {
            const nextStep = transitions.values().next().value;
            return nextStep || null;
        }
        // No more steps
        return null;
    }
    /**
     * Execute the workflow with the given context
     */
    async execute(context) {
        if (!this._initialStepId) {
            throw new Error('No initial step set for workflow');
        }
        // Initialize context with proper types
        context.history = context.history || [];
        context.startTime = context.startTime || new Date();
        context.endTime = context.endTime || null;
        context.status = context.status || 'pending';
        context.data = context.data || {};
        context.metadata = context.metadata || {};
        context.data = context.data || {};
        context.startTime = context.startTime || new Date();
        context.status = 'running';
        let currentStepId = this._initialStepId;
        let lastResult = null;
        // Execute steps in sequence
        while (currentStepId) {
            const step = this._steps.get(currentStepId);
            if (!step) {
                throw new Error(`Step '${currentStepId}' not found in workflow '${this._id}'`);
            }
            // Execute the step
            const startTime = new Date();
            try {
                // Emit step start event
                this._emitter.emit('stepStart', { stepId: currentStepId, startTime });
                // Execute the step
                const result = await step.execute(context);
                const endTime = new Date();
                // Record the step execution
                const historyEntry = {
                    stepId: currentStepId,
                    startTime,
                    endTime,
                    result,
                    quantumAnalysis: result.metadata?.quantumAnalysis,
                    neuralInsights: result.metadata?.neuralInsights,
                    goddessGuidance: result.metadata?.goddessGuidance
                };
                context.history.push(historyEntry);
                lastResult = result;
                // Emit step complete event
                this._emitter.emit('stepComplete', {
                    stepId: currentStepId,
                    result,
                    duration: endTime.getTime() - startTime.getTime()
                });
                // If step failed, stop the workflow
                if (!result.success) {
                    context.status = 'failed';
                    context.endTime = new Date();
                    return result;
                }
                // Get the next step
                currentStepId = this.getNextStep(currentStepId, result);
            }
            catch (error) {
                const endTime = new Date();
                const errorResult = {
                    success: false,
                    error: error instanceof Error ? error.message : String(error),
                    output: undefined,
                    metadata: {
                        error: {
                            name: error instanceof Error ? error.name : 'Error',
                            message: error instanceof Error ? error.message : String(error),
                            stack: error instanceof Error ? error.stack : undefined,
                            timestamp: new Date().toISOString()
                        }
                    }
                };
                // Record the failed step
                const historyEntry = {
                    stepId: currentStepId ?? 'unknown',
                    startTime,
                    endTime: endTime || new Date(), // Ensure endTime is always a Date
                    result: {
                        success: errorResult.success,
                        output: errorResult.output,
                        error: errorResult.error,
                        nextStepId: errorResult.nextStepId,
                        metadata: errorResult.metadata || {}
                    }
                };
                context.history.push(historyEntry);
                context.status = 'failed';
                context.endTime = endTime;
                // Emit step error event
                this._emitter.emit('stepError', {
                    stepId: currentStepId,
                    error: error,
                    duration: endTime.getTime() - startTime.getTime()
                });
                return errorResult;
            }
        }
        // Workflow completed successfully
        context.status = 'completed';
        context.endTime = new Date();
        // Emit workflow complete event
        this._emitter.emit('complete', {
            context,
            result: lastResult || { success: true, output: 'Workflow completed' }
        });
        return lastResult || { success: true, output: 'Workflow completed' };
    }
    /**
     * Add an event listener
     */
    on(event, listener) {
        this._emitter.on(event, listener);
    }
    /**
     * Remove an event listener
     */
    off(event, listener) {
        this._emitter.off(event, listener);
    }
    /**
     * Create a shallow copy of the workflow
     */
    clone() {
        const workflow = new Workflow(this._id, this._name);
        // Clone steps
        for (const step of this._steps.values()) {
            workflow.addStep(step);
        }
        // Clone transitions
        for (const [fromStepId, toStepIds] of this._transitions.entries()) {
            for (const toStepId of toStepIds) {
                workflow.addTransition(fromStepId, toStepId);
            }
        }
        // Set initial step
        if (this._initialStepId) {
            workflow.setInitialStep(this._initialStepId);
        }
        return workflow;
    }
}
exports.Workflow = Workflow;
//# sourceMappingURL=workflow.js.map