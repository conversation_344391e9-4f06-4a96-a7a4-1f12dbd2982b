{"version": 3, "file": "openModeSelector.js", "sourceRoot": "", "sources": ["../../src/commands/openModeSelector.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA,4CA0BC;AA1HD,+CAAiC;AACjC,kEAA+D;AAC/D,kDAAgD;AAChD,wEAAqE;AACrE,sCAAmC;AACnC,sEAA2F;AAC3F,uFAA+D;AAC/D,0EAAuE;AACvE,wEAAwE;AAExE;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAC,OAAgC;IACnE,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAElD,qDAAqD;QACrD,MAAM,qBAAqB,GAAG,IAAI,uBAAa,CAAC;YAC9C,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,uDAAuD;YACpE,gBAAgB,EAAE,eAAe;YACjC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;SACpC,CAAC,CAAC;QAEH,uDAAuD;QACvD,MAAM,eAAe,GAAG,IAAI,iCAAe,CAAC;YAC1C,EAAE,EAAE,kBAAkB;YACtB,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,+CAA+C;YAC5D,gBAAgB,EAAE,iBAAiB;YACnC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;YAC/C,YAAY,EAAE,IAAI;SACnB,EAAE,qBAAqB,CAAC,CAAC;QAE1B,2BAA2B;QAC3B,2BAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;QAC7C,2BAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAEvC,0CAA0C;QAC1C,MAAM,KAAK,GAAG,qCAAqB,CAAC,WAAW,EAAE,CAAC;QAClD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG;gBAClB,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,QAAQ;gBACtB,IAAI,EAAE,GAAG,IAAI,CAAC,WAAW,QAAQ;gBACjC,WAAW,EAAE,yBAAyB,IAAI,CAAC,WAAW,OAAO;gBAC7D,gBAAgB,EAAE,GAAG,IAAI,CAAC,EAAE,OAAO;gBACnC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;aAChD,CAAC;YAEF,2CAA2C;YAC3C,MAAM,SAAS,GAAG,IAAA,8BAAe,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;YACxD,2BAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAEjC,2BAA2B;YAC3B,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YACtD,eAAM,CAAC,IAAI,CAAC,gDAAgD,IAAI,CAAC,WAAW,OAAO,CAAC,CAAC;QACvF,CAAC;QAED,qEAAqE;QACrE,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,EAAE,qBAAqB,CAAC,CAAC;QACnE,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAE/D,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC3D,OAAO,eAAe,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC9D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAAC,OAAgC;IAChE,mDAAmD;IACnD,IAAI,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAoB,CAAC;IAEpF,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,IAAI,CAAC;YACH,8BAA8B;YAC9B,eAAe,GAAG,MAAM,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yCAAyC,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,gBAAgB,CAAC,OAAgC;IACrE,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAExC,gCAAgC;QAChC,MAAM,gBAAgB,GAAG,mCAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAEtF,wBAAwB;QACxB,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,IAAoB,EAAE,EAAE;YAC7D,eAAM,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YAE/D,2BAA2B;YAC3B,MAAM,eAAe,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YAED,8DAA8D;YAC9D,oBAAS,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,eAAe,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACnD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5H,CAAC;AACH,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ModeSelectorView } from '../ui/chat/modeSelectorView';\nimport { ChatPanel } from '../ui/chat/chatView';\nimport { agentManager } from '../agents/agentUtilities/agentManager';\nimport { logger } from '../logger';\nimport { IOperationMode, operationModeRegistry } from '../agents/agentModes/operationMode';\nimport receiverAgent from '../agents/agentTypes/receiverAgent';\nimport { SupervisorAgent } from '../agents/agentTypes/supervisorAgent';\nimport { createModeAgent } from '../agents/agentUtilities/agentFactory';\n\n/**\n * Initialize the multi-agent system with specialized agents for each mode\n */\nasync function initializeAgentSystem(context: vscode.ExtensionContext): Promise<SupervisorAgent> {\n  try {\n    logger.info('Initializing multi-agent system...');\n\n    // Create receiver agent for preprocessing user input\n    const receiverAgentInstance = new receiverAgent({\n      id: 'receiver-agent',\n      name: 'Receiver Agent',\n      description: 'Preprocesses user input to enhance clarity and detail',\n      systemPromptName: 'receiverAgent',\n      tools: ['file', 'search', 'memory']\n    });\n\n    // Create supervisor agent for orchestrating everything\n    const supervisorAgent = new SupervisorAgent({\n      id: 'supervisor-agent',\n      name: 'Supervisor Agent',\n      description: 'Orchestrates specialized agents and workflows',\n      systemPromptName: 'supervisorAgent',\n      tools: ['file', 'search', 'memory', 'workflow'],\n      isSupervisor: true\n    }, receiverAgentInstance);\n\n    // Register the core agents\n    agentManager.addAgent(receiverAgentInstance);\n    agentManager.addAgent(supervisorAgent);\n\n    // Create specialized agents for each mode\n    const modes = operationModeRegistry.getAllModes();\n    for (const mode of modes) {\n      const agentConfig = {\n        id: `${mode.id}-agent`,\n        name: `${mode.displayName} Agent`,\n        description: `Specialized agent for ${mode.displayName} mode`,\n        systemPromptName: `${mode.id}Agent`,\n        tools: ['file', 'search', 'memory', 'workflow']\n      };\n\n      // Create a specialized agent for this mode\n      const modeAgent = createModeAgent(mode.id, agentConfig);\n      agentManager.addAgent(modeAgent);\n\n      // Register with supervisor\n      supervisorAgent.registerModeAgent(mode.id, modeAgent);\n      logger.info(`Created and registered specialized agent for ${mode.displayName} mode`);\n    }\n\n    // Store in global state for access from other parts of the extension\n    context.globalState.update('receiverAgent', receiverAgentInstance);\n    context.globalState.update('supervisorAgent', supervisorAgent);\n\n    logger.info('Multi-agent system initialized successfully');\n    return supervisorAgent;\n  } catch (error) {\n    logger.error('Error initializing multi-agent system:', error);\n    throw error;\n  }\n}\n\n/**\n * Get the supervisor agent or initialize the agent system if needed\n */\nasync function getSupervisorAgent(context: vscode.ExtensionContext): Promise<SupervisorAgent | null> {\n  // Check if supervisor agent exists in global state\n  let supervisorAgent = context.globalState.get('supervisorAgent') as SupervisorAgent;\n\n  if (!supervisorAgent) {\n    try {\n      // Initialize the agent system\n      supervisorAgent = await initializeAgentSystem(context);\n    } catch (error) {\n      logger.error('Failed to initialize multi-agent system:', error);\n      vscode.window.showErrorMessage('Failed to initialize multi-agent system');\n      return null;\n    }\n  }\n\n  return supervisorAgent;\n}\n\n/**\n * Open the mode selector\n */\nexport async function openModeSelector(context: vscode.ExtensionContext): Promise<void> {\n  try {\n    logger.info('Opening mode selector...');\n\n    // Create the mode selector view\n    const modeSelectorView = ModeSelectorView.createOrShow(context.extensionUri, context);\n\n    // Handle mode selection\n    modeSelectorView.onModeSelected(async (mode: IOperationMode) => {\n      logger.info(`Mode selected: ${mode.displayName} (${mode.id})`);\n\n      // Get the supervisor agent\n      const supervisorAgent = await getSupervisorAgent(context);\n      if (!supervisorAgent) {\n        return;\n      }\n\n      // Open chat panel with the selected mode and supervisor agent\n      ChatPanel.createOrShow(context.extensionUri, supervisorAgent, context, mode);\n    });\n\n    logger.info('Mode selector opened successfully');\n  } catch (error) {\n    logger.error('Error opening mode selector:', error);\n    vscode.window.showErrorMessage(`Failed to open mode selector: ${error instanceof Error ? error.message : String(error)}`);\n  }\n}"]}