{"version": 3, "file": "settingsManager.js", "sourceRoot": "", "sources": ["../../../src/ui/settings/settingsManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,yCAAkE;AAClE,yCAAsC;AAStC,MAAa,eAAe;IAClB,MAAM,CAAC,QAAQ,CAAkB;IACjC,QAAQ,GAAyB,IAAI,GAAG,EAAE,CAAC;IAC3C,UAAU,GAA8D,IAAI,GAAG,EAAE,CAAC;IAClF,eAAe,GAA8C,IAAI,GAAG,EAAE,CAAC;IACvE,WAAW,GAA0B,IAAI,CAAC;IAC1C,aAAa,GAAG,KAAK,CAAC;IAEvB,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;QACE,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACG,oBAAoB;QAC1B,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,iDAAiD,EAAE,CAAC,KAAa,EAAE,EAAE;YACrF,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;gBAC1D,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACnE,CAAC;iBAAM,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBACtB,QAAQ,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;YAC5E,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,2DAA2D,EAAE,CAAC,KAAa,EAAE,EAAE;YAC/F,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACxD,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACvE,CAAC;iBAAM,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;gBACvB,QAAQ,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YAC3E,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,oDAAoD,EAAE,CAAC,KAAa,EAAE,EAAE;YACxF,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;YACtE,CAAC;iBAAM,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC,KAAa,EAAE,EAAE;YACxD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3D,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACrC,CAAC;iBAAM,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gBAC7B,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC,KAAa,EAAE,EAAE;YACxD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,KAAK,EAAE,CAAC;gBAC7D,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACpE,CAAC;iBAAM,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,KAAa,EAAE,EAAE;YACnD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAEhD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,KAAa,EAAE,EAAE;YACtD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAEtD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjE,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC7D,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACE,YAAY,CAAc,GAAW,EAAE,SAAiD;QAC7F,gDAAgD;QAChD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,SAAyD,CAAC,CAAC;IACtF,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAE5D,qEAAqE;YACrE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YACxD,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1C,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC1B,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAChC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,wBAAwB,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,UAAU,CAAI,GAAW,EAAE,YAAe;QAC/C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;YAClF,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrC,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAE,KAAW,CAAC,CAAC,CAAC,YAAY,CAAC;IAC3D,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU,CAAc,GAAW,EAAE,KAAQ,EAAE,SAAS,GAAG,KAAK;QAC3E,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;YACnF,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,qBAAqB;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAEpD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,GAAG,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;YAC7E,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAE9B,mBAAmB;QACnB,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAEvC,gCAAgC;QAChC,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;SAEK;IACG,eAAe,CAAC,GAAW,EAAE,KAAc;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,SAAS,EAAE,CAAC;YACd,mFAAmF;YACnF,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QAED,gCAAgC;QAChC,KAAK,MAAM,CAAC,YAAY,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;YACpE,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBAChD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC;gBACzC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACpB,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC;QACH,CAAC;QAED,4DAA4D;QAC5D,OAAO;YACL,OAAO,EAAE,KAAK,KAAK,SAAS;YAC5B,MAAM,EAAE,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,EAAE;YAChE,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,GAAW,EAAE,KAAc;QAC5D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAA,kBAAS,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,EAAE,CAAC,CAAC;YACnD,CAAC;YACD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,GAAG,EAAE,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACG,aAAa,CAAC,GAAW,EAAE,KAAc;QAC/C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;YACvC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,iBAAiB;IAC5B,CAAC;IAED;;SAEK;IACE,iBAAiB,CAAc,GAAW,EAAE,QAA4B;QAC7E,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACpC,CAAC;QACD,gDAAgD;QAC/C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAkC,CAAC,IAAI,CAAC,QAAoC,CAAC,CAAC;IAC7G,CAAC;IAED;;SAEK;IACE,oBAAoB,CAAc,GAAW,EAAE,QAA4B;QAChF,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAsE,CAAC;QACrH,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAoC,CAAC,CAAC;YACtE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,qBAAqB,CAAC,GAAW,EAAE,KAAc;QACvD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,IAAI,CAAC;oBACH,kFAAkF;oBAClF,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACE,cAAc;QACnB,MAAM,MAAM,GAA4B,EAAE,CAAC;QAC3C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YACnD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACtB,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,GAAW;QACnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEvC,IAAI,UAAU,EAAE,YAAY,KAAK,SAAS,EAAE,CAAC;gBAC3C,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACN,4CAA4C;gBAC5C,MAAM,IAAA,kBAAS,EAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YACjE,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAEjD,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,cAAc;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,UAAkB;QAC5C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAA4B,CAAC;YAE3E,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC5D,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;oBAC3D,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;wBACxB,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACzD,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC5E,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,wBAAwB,YAAY,EAAE,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC;YACpF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;SAEK;IACE,kBAAkB;QACvB,OAAO;YACL;gBACE,EAAE,EAAE,eAAe;gBACnB,KAAK,EAAE,wBAAwB;gBAC/B,IAAI,EAAE,GAAG;gBACT,WAAW,EAAE,mGAAmG;gBAChH,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,8BAA8B;aAC1C;YACD;gBACE,EAAE,EAAE,SAAS;gBACb,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,0CAA0C;gBACvD,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,wBAAwB;aACpC;YACD;gBACE,EAAE,EAAE,WAAW;gBACf,KAAK,EAAE,cAAc;gBACrB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,+CAA+C;gBAC5D,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,gCAAgC;aAC5C;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,2CAA2C;gBACxD,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,uBAAuB;aACnC;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,6CAA6C;gBAC1D,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,uBAAuB;aACnC;YACD;gBACE,EAAE,EAAE,WAAW;gBACf,KAAK,EAAE,WAAW;gBAClB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,0CAA0C;gBACvD,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,0BAA0B;aACtC;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,WAAW;gBAClB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,wCAAwC;gBACrD,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,wBAAwB;aACpC;YACD;gBACE,EAAE,EAAE,KAAK;gBACT,KAAK,EAAE,gBAAgB;gBACvB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,6CAA6C;gBAC1D,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,oBAAoB;aAChC;YACD;gBACE,EAAE,EAAE,UAAU;gBACd,KAAK,EAAE,UAAU;gBACjB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,yCAAyC;gBACtD,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,yBAAyB;aACrC;SACF,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;CACF;AA7fD,0CA6fC", "sourcesContent": ["/**\n * Professional Settings Manager\n * Handles all settings operations with proper validation, persistence, and error handling\n */\n\nimport * as vscode from 'vscode';\nimport { getConfig as _getConfig, setConfig } from '../../config';\nimport { Logger } from '../../logger';\nimport { Settings, SettingsSection } from './types';\n\nexport interface SettingsValidationResult {\n    isValid: boolean;\n    errors: string[];\n    warnings: string[];\n}\n\nexport class SettingsManager {\n  private static instance: SettingsManager;\n  private settings: Map<string, unknown> = new Map();\n  private validators: Map<string, (value: unknown) => SettingsValidationResult> = new Map();\n  private changeListeners: Map<string, ((value: unknown) => void)[]> = new Map();\n  private saveTimeout: NodeJS.Timeout | null = null;\n  private isInitialized = false;\n\n  public static getInstance(): SettingsManager {\n    if (!SettingsManager.instance) {\n      SettingsManager.instance = new SettingsManager();\n    }\n    return SettingsManager.instance;\n  }\n\n  private constructor() {\n    this.initializeValidators();\n  }\n\n  /**\n     * Initialize the settings manager (should be called after VS Code is ready)\n     */\n  public async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      return;\n    }\n        \n    try {\n      await this.loadAllSettings();\n      this.isInitialized = true;\n      Logger.instance.info('Settings manager initialized successfully');\n    } catch (error) {\n      Logger.instance.error('Failed to initialize settings manager:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Initialize validation rules for all settings\n     */\n  private initializeValidators(): void {\n    // Revolutionary Features Validators\n    this.addValidator('revolutionaryFeatures.goddessMode.adaptiveLevel', (value: number) => {\n      const errors: string[] = [];\n      const warnings: string[] = [];\n            \n      if (typeof value !== 'number' || value < 0 || value > 100) {\n        errors.push('Adaptive level must be a number between 0 and 100');\n      } else if (value < 50) {\n        warnings.push('Low adaptive level may reduce Goddess Mode effectiveness');\n      }\n            \n      return { isValid: errors.length === 0, errors, warnings };\n    });\n\n    this.addValidator('revolutionaryFeatures.quantumAnalysis.confidenceThreshold', (value: number) => {\n      const errors: string[] = [];\n      const warnings: string[] = [];\n            \n      if (typeof value !== 'number' || value < 0 || value > 1) {\n        errors.push('Confidence threshold must be a number between 0 and 1');\n      } else if (value < 0.5) {\n        warnings.push('Low confidence threshold may produce unreliable results');\n      }\n            \n      return { isValid: errors.length === 0, errors, warnings };\n    });\n\n    this.addValidator('revolutionaryFeatures.neuralSynthesis.learningRate', (value: number) => {\n      const errors: string[] = [];\n      const warnings: string[] = [];\n            \n      if (typeof value !== 'number' || value < 0.001 || value > 0.1) {\n        errors.push('Learning rate must be a number between 0.001 and 0.1');\n      } else if (value > 0.05) {\n        warnings.push('High learning rate may cause instability');\n      }\n            \n      return { isValid: errors.length === 0, errors, warnings };\n    });\n\n    // Provider Settings Validators\n    this.addValidator('providers.*.apiKey', (value: string) => {\n      const errors: string[] = [];\n      const warnings: string[] = [];\n            \n      if (typeof value !== 'string' || value.trim().length === 0) {\n        errors.push('API key is required');\n      } else if (value.length < 10) {\n        warnings.push('API key seems too short');\n      }\n            \n      return { isValid: errors.length === 0, errors, warnings };\n    });\n\n    // Memory Settings Validators\n    this.addValidator('memory.maxMemories', (value: number) => {\n      const errors: string[] = [];\n      const warnings: string[] = [];\n            \n      if (typeof value !== 'number' || value < 10 || value > 10000) {\n        errors.push('Max memories must be a number between 10 and 10000');\n      } else if (value > 5000) {\n        warnings.push('High memory count may impact performance');\n      }\n            \n      return { isValid: errors.length === 0, errors, warnings };\n    });\n\n    // General Settings Validators\n    this.addValidator('general.theme', (value: string) => {\n      const errors: string[] = [];\n      const validThemes = ['system', 'light', 'dark'];\n            \n      if (typeof value !== 'string' || !validThemes.includes(value)) {\n        errors.push('Theme must be one of: system, light, dark');\n      }\n            \n      return { isValid: errors.length === 0, errors, warnings: [] };\n    });\n\n    this.addValidator('general.language', (value: string) => {\n      const errors: string[] = [];\n      const validLanguages = ['en', 'de', 'fr', 'es', 'zh'];\n            \n      if (typeof value !== 'string' || !validLanguages.includes(value)) {\n        errors.push('Language must be one of: en, de, fr, es, zh');\n      }\n            \n      return { isValid: errors.length === 0, errors, warnings: [] };\n    });\n  }\n\n  /**\n     * Add a validator for a specific setting key\n     */\n  public addValidator<T = unknown>(key: string, validator: (value: T) => SettingsValidationResult): void {\n    // Use type assertion to handle the generic type\n    this.validators.set(key, validator as (value: unknown) => SettingsValidationResult);\n  }\n\n  /**\n     * Load all settings from VS Code configuration\n     */\n  private async loadAllSettings(): Promise<void> {\n    try {\n      const config = vscode.workspace.getConfiguration('codessa');\n            \n      // Load all configuration keys by inspecting the configuration object\n      const configValues = JSON.parse(JSON.stringify(config));\n      const allKeys = Object.keys(configValues);\n            \n      for (const key of allKeys) {\n        try {\n          const value = config.get(key);\n          this.settings.set(key, value);\n        } catch (error) {\n          Logger.instance.warn(`Failed to load setting ${key}:`, error);\n        }\n      }\n            \n      Logger.instance.info(`Loaded ${this.settings.size} settings successfully`);\n    } catch (error) {\n      Logger.instance.error('Failed to load settings:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Get a setting value with type safety\n     */\n  public getSetting<T>(key: string, defaultValue: T): T {\n    if (!this.isInitialized) {\n      Logger.instance.warn('Settings manager not initialized, returning default value');\n      return defaultValue;\n    }\n        \n    const value = this.settings.get(key);\n    return value !== undefined ? (value as T) : defaultValue;\n  }\n\n  /**\n     * Set a setting value with validation\n     */\n  public async setSetting<T = unknown>(key: string, value: T, immediate = false): Promise<SettingsValidationResult> {\n    if (!this.isInitialized) {\n      Logger.instance.warn('Settings manager not initialized, attempting to initialize');\n      await this.initialize();\n    }\n\n    // Validate the value\n    const validation = this.validateSetting(key, value);\n        \n    if (!validation.isValid) {\n      Logger.instance.warn(`Invalid setting value for ${key}:`, validation.errors);\n      return validation;\n    }\n\n    // Update local cache\n    this.settings.set(key, value);\n\n    // Notify listeners\n    this.notifyChangeListeners(key, value);\n\n    // Save to VS Code configuration\n    if (immediate) {\n      await this.saveSettingImmediate(key, value);\n    } else {\n      this.debouncedSave(key, value);\n    }\n\n    return validation;\n  }\n\n  /**\n     * Validate a setting value\n     */\n  private validateSetting(key: string, value: unknown): SettingsValidationResult {\n    const validator = this.validators.get(key);\n    if (validator) {\n      // Safe to call with any value since validators are added with proper type checking\n      return validator(value);\n    }\n\n    // Check for wildcard validators\n    for (const [validatorKey, validatorFn] of this.validators.entries()) {\n      if (validatorKey.includes('*')) {\n        const pattern = validatorKey.replace('*', '.*');\n        const regex = new RegExp(`^${pattern}$`);\n        if (regex.test(key)) {\n          return validatorFn(value);\n        }\n      }\n    }\n\n    // Default validation - just check if value is not undefined\n    return {\n      isValid: value !== undefined,\n      errors: value === undefined ? ['Value cannot be undefined'] : [],\n      warnings: []\n    };\n  }\n\n  /**\n     * Save setting immediately to VS Code configuration\n     */\n  private async saveSettingImmediate(key: string, value: unknown): Promise<void> {\n    try {\n      const success = await setConfig(key, value);\n      if (!success) {\n        throw new Error(`Failed to save setting ${key}`);\n      }\n      Logger.instance.debug(`Setting saved: ${key}`);\n    } catch (error) {\n      Logger.instance.error(`Failed to save setting ${key}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n     * Debounced save to avoid excessive writes\n     */\n  private debouncedSave(key: string, value: unknown): void {\n    if (this.saveTimeout) {\n      clearTimeout(this.saveTimeout);\n    }\n\n    this.saveTimeout = setTimeout(async () => {\n      try {\n        await this.saveSettingImmediate(key, value);\n      } catch (error) {\n        console.error('Debounced save error:', error);\n      }\n    }, 500); // 500ms debounce\n  }\n\n  /**\n     * Add a change listener for a specific setting\n     */\n  public addChangeListener<T = unknown>(key: string, listener: (value: T) => void): void {\n    console.log('Adding change listener for key:', key);\n    if (!this.changeListeners.has(key)) {\n      this.changeListeners.set(key, []);\n    }\n    // Use type assertion to handle the generic type\n    (this.changeListeners.get(key) as ((value: unknown) => void)[]).push(listener as (value: unknown) => void);\n  }\n\n  /**\n     * Remove a change listener\n     */\n  public removeChangeListener<T = unknown>(key: string, listener: (value: T) => void): void {\n    console.log('Removing change listener for key:', key);\n    const listeners = this.changeListeners.get(key) as (((value: T) => void) & ((value: unknown) => void))[] | undefined;\n    if (listeners) {\n      const index = listeners.indexOf(listener as (value: unknown) => void);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    }\n  }\n\n  /**\n     * Notify all change listeners for a setting\n     */\n  private notifyChangeListeners(key: string, value: unknown): void {\n    const listeners = this.changeListeners.get(key);\n    if (listeners) {\n      listeners.forEach(listener => {\n        try {\n          // Safe to call with any value since listeners are added with proper type checking\n          listener(value);\n        } catch (error) {\n          Logger.instance.error(`Error in change listener for ${key}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n     * Get all settings as a plain object\n     */\n  public getAllSettings(): Record<string, unknown> {\n    const result: Record<string, unknown> = {};\n    for (const [key, value] of this.settings.entries()) {\n      result[key] = value;\n    }\n    return result;\n  }\n\n  /**\n     * Reset a setting to its default value\n     */\n  public async resetSetting(key: string): Promise<void> {\n    try {\n      const config = vscode.workspace.getConfiguration('codessa');\n      const inspection = config.inspect(key);\n            \n      if (inspection?.defaultValue !== undefined) {\n        await this.setSetting(key, inspection.defaultValue, true);\n      } else {\n        // Remove the setting to use VS Code default\n        await setConfig(key, undefined);\n        this.settings.delete(key);\n      }\n            \n      Logger.instance.info(`Setting reset: ${key}`);\n    } catch (error) {\n      Logger.instance.error(`Failed to reset setting ${key}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n     * Reset all settings to defaults\n     */\n  public async resetAllSettings(): Promise<void> {\n    try {\n      const config = vscode.workspace.getConfiguration('codessa');\n      console.log('Resetting all settings for config:', config.keys());\n      const allKeys = Array.from(this.settings.keys());\n            \n      for (const key of allKeys) {\n        await this.resetSetting(key);\n      }\n            \n      Logger.instance.info('All settings reset to defaults');\n    } catch (error) {\n      Logger.instance.error('Failed to reset all settings:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Export settings to JSON\n     */\n  public exportSettings(): string {\n    return JSON.stringify(this.getAllSettings(), null, 2);\n  }\n\n  /**\n     * Import settings from JSON\n     */\n  public async importSettings(jsonString: string): Promise<{ success: boolean; errors: string[] }> {\n    const errors: string[] = [];\n        \n    try {\n      const importedSettings = JSON.parse(jsonString) as Record<string, unknown>;\n            \n      for (const [key, value] of Object.entries(importedSettings)) {\n        try {\n          const validation = await this.setSetting(key, value, true);\n          if (!validation.isValid) {\n            errors.push(`${key}: ${validation.errors.join(', ')}`);\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : String(error);\n          errors.push(`${key}: Failed to import - ${errorMessage}`);\n        }\n      }\n            \n      return { success: errors.length === 0, errors };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Invalid JSON format';\n      return { success: false, errors: [errorMessage] };\n    }\n  }\n\n  /**\n     * Get setting sections configuration\n     */\n  public getSettingSections(): SettingsSection[] {\n    return [\n      {\n        id: 'revolutionary',\n        title: 'Revolutionary Features',\n        icon: '✨',\n        description: 'Configure advanced AI capabilities including Goddess Mode, Quantum Analysis, and Neural Synthesis',\n        order: 1,\n        component: 'revolutionaryFeaturesSection'\n      },\n      {\n        id: 'general',\n        title: 'General',\n        icon: '⚙️',\n        description: 'Basic extension settings and preferences',\n        order: 2,\n        component: 'generalSettingsSection'\n      },\n      {\n        id: 'providers',\n        title: 'AI Providers',\n        icon: '🤖',\n        description: 'Configure AI model providers and API settings',\n        order: 3,\n        component: 'providersModelsSettingsSection'\n      },\n      {\n        id: 'agents',\n        title: 'Agents',\n        icon: '👥',\n        description: 'Manage AI agents and their configurations',\n        order: 4,\n        component: 'agentsSettingsSection'\n      },\n      {\n        id: 'memory',\n        title: 'Memory',\n        icon: '🧠',\n        description: 'Configure memory system and storage options',\n        order: 5,\n        component: 'memorySettingsSection'\n      },\n      {\n        id: 'workflows',\n        title: 'Workflows',\n        icon: '🔄',\n        description: 'Manage automated workflows and templates',\n        order: 6,\n        component: 'workflowsSettingsSection'\n      },\n      {\n        id: 'ui',\n        title: 'Interface',\n        icon: '🎨',\n        description: 'User interface and theme customization',\n        order: 7,\n        component: 'uiThemeSettingsSection'\n      },\n      {\n        id: 'tts',\n        title: 'Text-to-Speech',\n        icon: '🔊',\n        description: 'Configure text-to-speech and voice settings',\n        order: 8,\n        component: 'ttsSettingsSection'\n      },\n      {\n        id: 'advanced',\n        title: 'Advanced',\n        icon: '🔧',\n        description: 'Advanced settings and developer options',\n        order: 9,\n        component: 'advancedSettingsSection'\n      }\n    ].sort((a, b) => a.order - b.order);\n  }\n\n  /**\n     * Check if settings manager is initialized\n     */\n  public isReady(): boolean {\n    return this.isInitialized;\n  }\n\n  /**\n     * Dispose of the settings manager\n     */\n  public dispose(): void {\n    if (this.saveTimeout) {\n      clearTimeout(this.saveTimeout);\n      this.saveTimeout = null;\n    }\n        \n    this.changeListeners.clear();\n    this.settings.clear();\n    this.validators.clear();\n    this.isInitialized = false;\n  }\n}\n"]}