{"version": 3, "file": "memory.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/memory.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;;AAwBH,oEAyFC;AAKD,4EA8DC;AAhLD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAEtC,oEAAoE;AACpE,8DAA2D;AAC3D,iEAAiE;AACjE,sEAA2E;AAM3E,4DAA4D;AAC5D,oEAA0H;AAAjH,qHAAA,qBAAqB,OAAwB;AAAE,sHAAA,qBAAqB,OAAA;AAE7E;;GAEG;AACH,SAAgB,4BAA4B,CAC1C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,WAAkB,EAClB,QAAiB,EAAE;IAEnB,eAAM,CAAC,IAAI,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;IAE1D,+CAA+C;IAC/C,MAAM,cAAc,GAAG,IAAI,qCAAqB,EAAE,CAAC;IAEnD,uCAAuC;IACvC,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAE5D,qEAAqE;IACrE,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;IACnG,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;IACzG,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAClH,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAClH,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;IAE1F,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,oDAAoD;IACpD,wBAAwB,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;IAE7C,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtF,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAClG,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9G,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KACvF,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,uCAAuC;QACvC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,mBAAmB,KAAK,EAAE;gBAChC,MAAM,EAAE,qBAAqB;gBAC7B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,4CAA4C;YAC5C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,cAAc;gBACjC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,qBAAqB;gBAC7B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,iBAAiB;YACjB,mBAAmB;YACnB,sBAAsB;YACtB,sBAAsB;YACtB,cAAc;YACd,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,QAAyB;QACxC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;KACzC,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,gCAAgC,CAC9C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,WAAkB;IAElB,eAAM,CAAC,IAAI,CAAC,0CAA0C,IAAI,EAAE,CAAC,CAAC;IAE9D,0CAA0C;IAC1C,MAAM,0BAA0B,GAAG,IAAI,qCAAqB,EAAE,CAAC;IAE/D,oDAAoD;IACpD,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;IACzG,MAAM,uBAAuB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,WAAW,CAAC,CAAC;IACrH,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAClH,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAClH,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAClH,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAElH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,mDAAmD;IACnD,4BAA4B,CAAC,EAAE,EAAE,0BAA0B,CAAC,CAAC;IAE7D,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1F,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChH,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnH,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAClH,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC/F,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,mBAAmB;YACnB,uBAAuB;YACvB,sBAAsB;YACtB,sBAAsB;YACtB,sBAAsB;YACtB,sBAAsB;YACtB,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,QAAyB;QACxC,IAAI,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,SAAS,CAAC;KAC5C,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,wBAAwB,CAAC,UAAkB,EAAE,cAAqC;IACzF,eAAM,CAAC,IAAI,CAAC,qCAAqC,UAAU,EAAE,CAAC,CAAC;IAE/D,yCAAyC;IACzC,6BAAa,CAAC,SAAS,CAAC;QACtB,OAAO,EAAE,yBAAyB,UAAU,EAAE;QAC9C,QAAQ,EAAE;YACR,UAAU;YACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,cAAc;SACrB;KACF,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,qCAAqC;IACrC,cAAc,CAAC,SAAS,CAAC;QACvB,OAAO,EAAE,YAAY,UAAU,UAAU;QACzC,QAAQ,EAAE;YACR,UAAU;YACV,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,cAAc;SACrB;KACF,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,4BAA4B,CAAC,UAAkB,EAAE,cAAqC;IAC7F,eAAM,CAAC,IAAI,CAAC,kDAAkD,UAAU,EAAE,CAAC,CAAC;IAE5E,8BAA8B;IAC9B,6BAAa,CAAC,SAAS,CAAC;QACtB,OAAO,EAAE,sCAAsC,UAAU,EAAE;QAC3D,QAAQ,EAAE;YACR,UAAU;YACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,cAAc;SACrB;KACF,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,cAAc,CAAC,SAAS,CAAC;QACvB,OAAO,EAAE,yBAAyB,UAAU,QAAQ;QACpD,QAAQ,EAAE;YACR,UAAU;YACV,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,cAAc;SACrB;KACF,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACf,eAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAa,qBAAqB;IACxB,cAAc,CAAwB;IAE9C;QACE,IAAI,CAAC,cAAc,GAAG,IAAI,qCAAqB,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,UAAkB,EAAE,SAAiB;QAClE,eAAM,CAAC,IAAI,CAAC,oCAAoC,UAAU,aAAa,SAAS,EAAE,CAAC,CAAC;QAEpF,sCAAsC;QACtC,MAAM,6BAAa,CAAC,SAAS,CAAC;YAC5B,OAAO,EAAE,6BAA6B,UAAU,EAAE;YAClD,QAAQ,EAAE;gBACR,UAAU;gBACV,SAAS;gBACT,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,cAAc;aACrB;SACF,CAAC,CAAC;QAEH,8CAA8C;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAClC,OAAO,EAAE,WAAW,SAAS,yBAAyB,UAAU,EAAE;YAClE,QAAQ,EAAE;gBACR,UAAU;gBACV,SAAS;gBACT,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,cAAc;aACrB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,UAAkB,EAClB,MAAc,EACd,KAAU,EACV,MAAW,EACX,WAAgB,EAAE;QAElB,8CAA8C;QAC9C,MAAM,6BAAa,CAAC,SAAS,CAAC;YAC5B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;YAC1C,QAAQ,EAAE;gBACR,UAAU;gBACV,MAAM;gBACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,cAAc;gBACpB,GAAG,QAAQ;aACZ;SACF,CAAC,CAAC;QAEH,wDAAwD;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAClC,OAAO,EAAE,QAAQ,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACpD,QAAQ,EAAE;gBACR,UAAU;gBACV,MAAM;gBACN,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,cAAc;gBACpB,GAAG,QAAQ;aACZ;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,UAAkB,EAAE,KAAa;QACxD,iCAAiC;QACjC,MAAM,aAAa,GAAG,MAAM,6BAAa,CAAC,cAAc,CAAC;YACvD,KAAK;YACL,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;SACjC,CAAC,CAAC;QAEH,kDAAkD;QAClD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC;YAC/D,KAAK;YACL,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;SACjC,CAAC,CAAC;QAEH,OAAO;YACL,aAAa;YACb,eAAe;YACf,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,eAAe,CAAC;SAC7E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,aAAoB,EAAE,eAAoB;QACtE,OAAO;YACL,aAAa,EAAE,aAAa,CAAC,MAAM;YACnC,gBAAgB,EAAE,eAAe;YACjC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;YAC7C,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,eAAe,CAAC;SAC9E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAe;QACrC,yCAAyC;QACzC,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACpD,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;YACvC,IAAI,MAAM,EAAE,CAAC;gBACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvC,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,OAAO,EAAE,aAAa,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,aAAoB,EAAE,eAAoB;QACxE,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,eAAe,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC;YAChD,eAAe,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QAC3F,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;CACF;AA9ID,sDA8IC;AAED,iDAAiD;AACpC,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC", "sourcesContent": ["/**\n * Memory-Enhanced Workflow and interfaces for Codessa workflows\n *\n * This module provides workflows for memory-enhanced operations and re-exports memory types and interfaces.\n * - Storing and retrieving information from memory\n * - Using memory to enhance responses\n * - Managing short-term and long-term memory\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { Codessa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n// Import the memoryManager singleton for workflow memory operations\nimport { memoryManager } from '../../memory/memoryManager';\n// Import CodessaMemoryProvider for advanced memory functionality\nimport { CodessaMemoryProvider } from '../../memory/codessa/codessaMemory';\n\n\n// Re-export memory types from the memory module\nexport type { MemoryEntry, MemorySearchOptions } from '../../memory/types';\n\n// Re-export the CodessaMemoryProvider from its new location\nexport { codessaMemoryProvider as codessaMemoryManager, CodessaMemoryProvider } from '../../memory/codessa/codessaMemory';\n\n/**\n * Create a Memory-Enhanced workflow\n */\nexport function createMemoryEnhancedWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  memoryAgent: Agent,\n  tools: ITool[] = []\n): GraphDefinition {\n  logger.info(`Creating Memory-Enhanced workflow: ${name}`);\n\n  // Initialize memory provider for this workflow\n  const memoryProvider = new CodessaMemoryProvider();\n\n  // Create nodes with memory integration\n  const inputNode = Codessa.createInputNode('input', 'Input');\n\n  // Create agent nodes with memory functionality embedded in the agent\n  const queryAnalysisNode = Codessa.createAgentNode('query-analysis', 'Query Analysis', memoryAgent);\n  const memoryRetrievalNode = Codessa.createAgentNode('memory-retrieval', 'Memory Retrieval', memoryAgent);\n  const contextEnhancementNode = Codessa.createAgentNode('context-enhancement', 'Context Enhancement', memoryAgent);\n  const responseGenerationNode = Codessa.createAgentNode('response-generation', 'Response Generation', memoryAgent);\n  const memorySaveNode = Codessa.createAgentNode('memory-save', 'Memory Save', memoryAgent);\n\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Initialize memory functionality for this workflow\n  initializeWorkflowMemory(id, memoryProvider);\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },\n    { name: 'query-to-memory', source: 'query-analysis', target: 'memory-retrieval', type: 'default' },\n    { name: 'memory-to-context', source: 'memory-retrieval', target: 'context-enhancement', type: 'default' },\n    { name: 'context-to-response', source: 'context-enhancement', target: 'response-generation', type: 'default' },\n    { name: 'response-to-memory', source: 'response-generation', target: 'memory-save', type: 'default' },\n    { name: 'memory-to-output', source: 'memory-save', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect context enhancement to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `context-to-tool-${index}`,\n        source: 'context-enhancement',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to response generation\n      edges.push({\n        name: `tool-${index}-to-response`,\n        source: `tool-${index}`,\n        target: 'response-generation',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      queryAnalysisNode,\n      memoryRetrievalNode,\n      contextEnhancementNode,\n      responseGenerationNode,\n      memorySaveNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'memory' as OperationMode,\n    tags: ['memory', 'context', 'retrieval']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized Conversation Memory workflow\n */\nexport function createConversationMemoryWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  memoryAgent: Agent\n): GraphDefinition {\n  logger.info(`Creating Conversation Memory workflow: ${name}`);\n\n  // Initialize conversation memory provider\n  const conversationMemoryProvider = new CodessaMemoryProvider();\n\n  // Create nodes with conversation memory integration\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const messageAnalysisNode = Codessa.createAgentNode('message-analysis', 'Message Analysis', memoryAgent);\n  const conversationHistoryNode = Codessa.createAgentNode('conversation-history', 'Conversation History', memoryAgent);\n  const relevanceFilteringNode = Codessa.createAgentNode('relevance-filtering', 'Relevance Filtering', memoryAgent);\n  const contextIntegrationNode = Codessa.createAgentNode('context-integration', 'Context Integration', memoryAgent);\n  const responseGenerationNode = Codessa.createAgentNode('response-generation', 'Response Generation', memoryAgent);\n  const conversationUpdateNode = Codessa.createAgentNode('conversation-update', 'Conversation Update', memoryAgent);\n\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Initialize conversation memory for this workflow\n  initializeConversationMemory(id, conversationMemoryProvider);\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-message', source: 'input', target: 'message-analysis', type: 'default' },\n    { name: 'message-to-history', source: 'message-analysis', target: 'conversation-history', type: 'default' },\n    { name: 'history-to-filtering', source: 'conversation-history', target: 'relevance-filtering', type: 'default' },\n    { name: 'filtering-to-integration', source: 'relevance-filtering', target: 'context-integration', type: 'default' },\n    { name: 'integration-to-response', source: 'context-integration', target: 'response-generation', type: 'default' },\n    { name: 'response-to-update', source: 'response-generation', target: 'conversation-update', type: 'default' },\n    { name: 'update-to-output', source: 'conversation-update', target: 'output', type: 'default' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      messageAnalysisNode,\n      conversationHistoryNode,\n      relevanceFilteringNode,\n      contextIntegrationNode,\n      responseGenerationNode,\n      conversationUpdateNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'memory' as OperationMode,\n    tags: ['memory', 'conversation', 'context']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Initialize workflow memory using both memory systems\n */\nfunction initializeWorkflowMemory(workflowId: string, memoryProvider: CodessaMemoryProvider): void {\n  logger.info(`Initializing memory for workflow: ${workflowId}`);\n\n  // Set up memory context for the workflow\n  memoryManager.addMemory({\n    content: `Workflow initialized: ${workflowId}`,\n    metadata: {\n      workflowId,\n      timestamp: Date.now(),\n      source: 'system',\n      type: 'conversation'\n    }\n  }).catch(error => {\n    logger.error('Error initializing workflow memory:', error);\n  });\n\n  // Initialize memory provider context\n  memoryProvider.addMemory({\n    content: `Workflow ${workflowId} started`,\n    metadata: {\n      workflowId,\n      source: 'system',\n      type: 'conversation'\n    }\n  }).catch(error => {\n    logger.error('Error initializing memory provider:', error);\n  });\n}\n\n/**\n * Initialize conversation memory using both memory systems\n */\nfunction initializeConversationMemory(workflowId: string, memoryProvider: CodessaMemoryProvider): void {\n  logger.info(`Initializing conversation memory for workflow: ${workflowId}`);\n\n  // Set up conversation context\n  memoryManager.addMemory({\n    content: `Conversation workflow initialized: ${workflowId}`,\n    metadata: {\n      workflowId,\n      timestamp: Date.now(),\n      source: 'system',\n      type: 'conversation'\n    }\n  }).catch(error => {\n    logger.error('Error initializing conversation memory:', error);\n  });\n\n  // Initialize conversation provider\n  memoryProvider.addMemory({\n    content: `Conversation workflow ${workflowId} ready`,\n    metadata: {\n      workflowId,\n      source: 'system',\n      type: 'conversation'\n    }\n  }).catch(error => {\n    logger.error('Error initializing conversation memory provider:', error);\n  });\n}\n\n/**\n * Enhanced memory operations using both memoryManager and CodessaMemoryProvider\n */\nexport class WorkflowMemoryManager {\n  private memoryProvider: CodessaMemoryProvider;\n\n  constructor() {\n    this.memoryProvider = new CodessaMemoryProvider();\n  }\n\n  /**\n   * Initialize memory for a workflow session\n   */\n  async initializeWorkflowMemory(workflowId: string, sessionId: string): Promise<void> {\n    logger.info(`Initializing memory for workflow ${workflowId}, session ${sessionId}`);\n\n    // Initialize session in memoryManager\n    await memoryManager.addMemory({\n      content: `Workflow session started: ${workflowId}`,\n      metadata: {\n        workflowId,\n        sessionId,\n        timestamp: Date.now(),\n        source: 'system',\n        type: 'conversation'\n      }\n    });\n\n    // Initialize session in CodessaMemoryProvider\n    await this.memoryProvider.addMemory({\n      content: `Session ${sessionId} started for workflow ${workflowId}`,\n      metadata: {\n        workflowId,\n        sessionId,\n        source: 'system',\n        type: 'conversation'\n      }\n    });\n  }\n\n  /**\n   * Store workflow step results with enhanced memory features\n   */\n  async storeStepResult(\n    workflowId: string,\n    stepId: string,\n    input: any,\n    output: any,\n    metadata: any = {}\n  ): Promise<void> {\n    // Store in memoryManager with structured data\n    await memoryManager.addMemory({\n      content: JSON.stringify({ input, output }),\n      metadata: {\n        workflowId,\n        stepId,\n        timestamp: Date.now(),\n        source: 'system',\n        type: 'conversation',\n        ...metadata\n      }\n    });\n\n    // Store in CodessaMemoryProvider with enhanced features\n    await this.memoryProvider.addMemory({\n      content: `Step ${stepId}: ${JSON.stringify(output)}`,\n      metadata: {\n        workflowId,\n        stepId,\n        source: 'system',\n        type: 'conversation',\n        ...metadata\n      }\n    });\n  }\n\n  /**\n   * Retrieve workflow context using both memory systems\n   */\n  async getWorkflowContext(workflowId: string, query: string): Promise<any> {\n    // Get context from memoryManager\n    const memoryContext = await memoryManager.searchMemories({\n      query,\n      limit: 20,\n      filter: { type: 'conversation' }\n    });\n\n    // Get enhanced context from CodessaMemoryProvider\n    const enhancedContext = await this.memoryProvider.searchMemories({\n      query,\n      limit: 10,\n      filter: { type: 'conversation' }\n    });\n\n    return {\n      memoryContext,\n      enhancedContext,\n      combinedInsights: this.combineMemoryInsights(memoryContext, enhancedContext)\n    };\n  }\n\n  /**\n   * Combine insights from both memory systems\n   */\n  private combineMemoryInsights(memoryContext: any[], enhancedContext: any): any {\n    return {\n      totalMemories: memoryContext.length,\n      enhancedFeatures: enhancedContext,\n      patterns: this.extractPatterns(memoryContext),\n      recommendations: this.generateRecommendations(memoryContext, enhancedContext)\n    };\n  }\n\n  /**\n   * Extract patterns from memory data\n   */\n  private extractPatterns(memories: any[]): any {\n    // Analyze patterns in workflow execution\n    const stepFrequency = memories.reduce((acc, memory) => {\n      const stepId = memory.metadata?.stepId;\n      if (stepId) {\n        acc[stepId] = (acc[stepId] || 0) + 1;\n      }\n      return acc;\n    }, {});\n\n    return { stepFrequency };\n  }\n\n  /**\n   * Generate recommendations based on memory analysis\n   */\n  private generateRecommendations(memoryContext: any[], enhancedContext: any): string[] {\n    const recommendations: string[] = [];\n\n    if (memoryContext.length > 100) {\n      recommendations.push('Consider archiving older workflow memories');\n    }\n\n    if (enhancedContext && enhancedContext.patterns) {\n      recommendations.push('Enhanced patterns detected - optimize workflow based on insights');\n    }\n\n    return recommendations;\n  }\n}\n\n// Export singleton instance for use in workflows\nexport const workflowMemoryManager = new WorkflowMemoryManager();\n"]}