{"version": 3, "file": "whisperTranscribe.js", "sourceRoot": "", "sources": ["../../src/services/whisperTranscribe.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,kEAgDC;AApDD,6CAA+B;AAIxB,KAAK,UAAU,2BAA2B,CAAC,SAAqB,EAAE,MAAc;IACrF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,QAAQ,GAAG,wBAAwB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAChF,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,QAAQ,GAAG,WAAW,CAAC;QAC7B,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAC/B,KAAK,QAAQ,MAAM;YACb,0DAA0D,QAAQ,OAAO;YACzE,iCAAiC,CACxC,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,QAAQ,CAAC,CAAC;QAC3D,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;QAEtE,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,0BAA0B;YAChC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,MAAM,EAAE;gBACnC,cAAc,EAAE,iCAAiC,QAAQ,EAAE;gBAC3D,gBAAgB,EAAE,IAAI,CAAC,MAAM;aAC9B;SACF,CAAC;QAEF,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACzC,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;gBACvB,IAAI,IAAI,KAAK,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACjB,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC9B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;wBACd,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACrB,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,yBAAyB,CAAC,CAAC;oBAC3D,CAAC;gBACH,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,MAAM,CAAC,wCAAwC,GAAG,GAAG,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACtB,MAAM,CAAC,8BAA8B,GAAG,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QACH,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChB,GAAG,CAAC,GAAG,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as https from 'https';\nimport * as path from 'path';\nimport * as fs from 'fs';\n\nexport async function transcribeWithOpenAIWhisper(audioData: Uint8Array, apiKey: string): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const boundary = '----WebKitFormBoundary' + Math.random().toString(16).slice(2);\n    const audioBuffer = Buffer.from(audioData);\n    const filename = 'audio.wav';\n    const formDataStart = Buffer.from(\n      `--${boundary}\\r\\n` +\n            `Content-Disposition: form-data; name=\"file\"; filename=\"${filename}\"\\r\\n` +\n            'Content-Type: audio/wav\\r\\n\\r\\n'\n    );\n    const formDataEnd = Buffer.from(`\\r\\n--${boundary}--\\r\\n`);\n    const body = Buffer.concat([formDataStart, audioBuffer, formDataEnd]);\n\n    const options = {\n      hostname: 'api.openai.com',\n      path: '/v1/audio/transcriptions',\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${apiKey}`,\n        'Content-Type': `multipart/form-data; boundary=${boundary}`,\n        'Content-Length': body.length\n      }\n    };\n\n    const req = https.request(options, (res) => {\n      let data = '';\n      res.on('data', (chunk) => {\n        data += chunk;\n      });\n      res.on('end', () => {\n        try {\n          const json = JSON.parse(data);\n          if (json.text) {\n            resolve(json.text);\n          } else {\n            reject(json.error?.message || 'No transcription result');\n          }\n        } catch (err) {\n          reject('Failed to parse Whisper API response: ' + err);\n        }\n      });\n    });\n    req.on('error', (err) => {\n      reject('Whisper API request failed: ' + err);\n    });\n    req.write(body);\n    req.end();\n  });\n}\n"]}