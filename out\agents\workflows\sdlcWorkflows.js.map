{"version": 3, "file": "sdlcWorkflows.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/sdlcWorkflows.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;GAWG;;AAuBH,wDA8IC;AAKD,gEA8IC;AAKD,oDAkLC;AAKD,oEAkLC;AAKD,sDA2EC;AAKD,4DAwFC;AAKD,8DA8EC;AAl6BD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAEtC,sEAAmG;AAEnG,iDAOwB;AAExB;;GAEG;AACH,SAAgB,sBAAsB,CACpC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,iBAAwB,EACxB,aAAoB,EACpB,mBAA0B,EAC1B,QAAiB,EAAE;IAEnB,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;IAC9H,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;IACjH,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;IACpH,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,aAAa,CAAC,CAAC;IAC1H,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;IACjH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,MAAM,qBAAqB,GAAG;QAC5B,KAAK,CAAC,wBAAwB,CAAC,eAAoB;YACjD,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;oBACxC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,uBAAuB,EAAE,MAAM,CAAC;qBACpD;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;YAC3F,CAAC;QACH,CAAC;QAED,KAAK,CAAC,wBAAwB;YAC5B,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK,EAAE,0BAA0B;oBACjC,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBAChC,IAAI,CAAC;wBACH,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC;oBAAC,OAAO,EAAE,EAAE,CAAC;wBACZ,OAAO,CAAC,CAAC,OAAO,CAAC;oBACnB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,gCAAgC,CAAC,CAAC;gBACnF,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;gBAClF,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,KAAK,CAAC,YAAY,CAAC,OAAY;YAC7B,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;oBAChC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,SAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC;qBACtC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;KACF,CAAC;IAEF,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACxG,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnH,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChH,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC7F,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,yCAAyC;QACzC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,uBAAuB,KAAK,EAAE;gBACpC,MAAM,EAAE,uBAAuB;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,yCAAyC;YACzC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,aAAa;gBAChC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,kBAAkB;gBAC1B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,wBAAwB;YACxB,mBAAmB;YACnB,oBAAoB;YACpB,wBAAwB;YACxB,mBAAmB;YACnB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,IAAI,EAAE,UAAsB;QAC5B,QAAQ,EAAE;YACR,aAAa,EAAE,qBAAqB;SACrC;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CACxC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,oBAA2B,EAC3B,aAAoB,EACpB,iBAAwB,EACxB,QAAoC,EAAE;IAEtC,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,oBAAoB,CAAC,CAAC;IACjI,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAAC;IACpI,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,aAAa,CAAC,CAAC;IAC1H,MAAM,8BAA8B,GAAG,eAAO,CAAC,eAAe,CAAC,6BAA6B,EAAE,6BAA6B,EAAE,iBAAiB,CAAC,CAAC;IAChJ,MAAM,6BAA6B,GAAG,eAAO,CAAC,eAAe,CAAC,4BAA4B,EAAE,4BAA4B,EAAE,oBAAoB,CAAC,CAAC;IAChJ,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,MAAM,yBAAyB,GAAG;QAChC,KAAK,CAAC,0BAA0B,CAAC,aAAkB;YACjD,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;oBACtC,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAsB;wBAC9B,IAAI,EAAE,cAA4B;wBAClC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,MAAM,CAAC;qBAC5C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YACrE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;YACvF,CAAC;QACH,CAAC;QAED,KAAK,CAAC,iBAAiB,CAAC,YAAiB;YACvC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;oBACrC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC;qBAChD;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,2BAA2B,CAAC,KAAa;YAC7C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBACpC,IAAI,CAAC;wBACH,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC;oBAAC,OAAO,EAAE,EAAE,CAAC;wBACZ,OAAO,CAAC,CAAC,OAAO,CAAC;oBACnB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,YAAY,CAAC,MAAM,mCAAmC,CAAC,CAAC;gBAC1F,OAAO,YAAY,CAAC;YACtB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;gBACrF,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;KACF,CAAC;IAEF,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjG,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtH,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrH,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,6BAA6B,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/H,EAAE,IAAI,EAAE,iCAAiC,EAAE,MAAM,EAAE,6BAA6B,EAAE,MAAM,EAAE,4BAA4B,EAAE,IAAI,EAAE,SAAS,EAAE;QACzI,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,4BAA4B,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC7G,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,yCAAyC;QACzC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,oBAAoB,KAAK,EAAE;gBACjC,MAAM,EAAE,uBAAuB;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,oDAAoD;YACpD,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,oBAAoB;gBACvC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,6BAA6B;gBACrC,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,wBAAwB;YACxB,yBAAyB;YACzB,wBAAwB;YACxB,8BAA8B;YAC9B,6BAA6B;YAC7B,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,IAAI,EAAE,cAA0B;QAChC,QAAQ,EAAE;YACR,aAAa,EAAE,yBAAyB;SACzC;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,cAAqB,EACrB,eAAsB,EACtB,qBAA4B,EAC5B,QAAoC,EAAE;IAEtC,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,CAAC,CAAC;IACrH,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;IAChH,MAAM,YAAY,GAAG,eAAO,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;IACxF,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;IAC1F,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACnG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,MAAM,mBAAmB,GAAG;QAC1B,KAAK,CAAC,uBAAuB,CAAC,UAAe;YAC3C,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBACnC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,CAAC;qBACzC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAC9D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YAChF,CAAC;QACH,CAAC;QAED,KAAK,CAAC,mBAAmB,CAAC,UAAe;YACvC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBACnC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC;qBACrC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,KAAK,CAAC,aAAa,CAAC,UAAe;YACjC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBACnC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;qBAC/B;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACpD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,cAAc,CAAC,UAAe;YAClC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBACnC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC;qBAChC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,sBAAsB,CAAC,KAAa;YACxC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBAChC,IAAI,CAAC;wBACH,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC;oBAAC,OAAO,EAAE,EAAE,CAAC;wBACZ,OAAO,CAAC,CAAC,OAAO,CAAC;oBACnB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,8BAA8B,CAAC,CAAC;gBACjF,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;gBAChF,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;KACF,CAAC;IAEF,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAClG,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACvF,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACzF,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KACzF,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,uCAAuC;QACvC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,wBAAwB,KAAK,EAAE;gBACrC,MAAM,EAAE,qBAAqB;gBAC7B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,sCAAsC;YACtC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,YAAY;gBAC/B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,eAAe;gBACvB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,sBAAsB;YACtB,kBAAkB;YAClB,YAAY;YACZ,aAAa;YACb,gBAAgB;YAChB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,IAAI,EAAE,QAAoB;QAC1B,QAAQ,EAAE;YACR,aAAa,EAAE,mBAAmB;SACnC;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,4BAA4B,CAC1C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,cAAqB,EACrB,aAAoB,EACpB,OAAc,EACd,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,IAAI,EAAE,CAAC,CAAC;IAElE,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACtG,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,CAAC,CAAC;IACrH,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAChG,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IAC5F,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC9F,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IACnF,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,oBAAoB;IACpB,MAAM,gBAAgB,GAAG,IAAA,qCAAsB,GAAE,CAAC;IAClD,MAAM,WAAW,GAAG,IAAA,gCAAiB,GAAE,CAAC;IACxC,MAAM,gBAAgB,GAAG,eAAO,CAAC,cAAc,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;IAC9G,MAAM,eAAe,GAAG,eAAO,CAAC,cAAc,CAAC,cAAc,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;IAE5F,kDAAkD;IAClD,MAAM,mBAAmB,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACjE,eAAO,CAAC,cAAc,CAAC,mBAAmB,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CAC/E,CAAC;IAEF,MAAM,2BAA2B,GAAG;QAClC,KAAK,CAAC,uBAAuB,CAAC,QAAa;YACzC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;oBACjC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,MAAoB;wBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,gBAAgB,EAAE,MAAM,EAAE,MAAM,CAAC;qBACzC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAC9D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YAChF,CAAC;QACH,CAAC;QAED,KAAK,CAAC,gBAAgB,CAAC,WAAgB;YACrC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;oBACpC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,SAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,MAAM,CAAC;qBAC5C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,eAAe,CAAC,UAAe;YACnC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBACnC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,SAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC;qBAC3C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,oBAAoB,CAAC,KAAa;YACtC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBAChC,IAAI,CAAC;wBACH,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC;oBAAC,OAAO,EAAE,EAAE,CAAC;wBACZ,OAAO,CAAC,CAAC,OAAO,CAAC;oBACnB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,4BAA4B,CAAC,CAAC;gBAC/E,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;gBAC9E,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;KACF,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1F,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjH,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpH,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QAClG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QAChG,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3F,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEjF,iBAAiB;QACjB,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,UAAU,EAAE;QAC5G,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,UAAU,EAAE;KACxG,CAAC;IAEF,kDAAkD;IAClD,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACnC,kDAAkD;QAClD,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YACvC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,qCAAqC,KAAK,EAAE;gBAClD,MAAM,EAAE,qBAAqB;gBAC7B,MAAM,EAAE,mBAAmB,KAAK,EAAE;gBAClC,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,gDAAgD;YAChD,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,mBAAmB,KAAK,kBAAkB;gBAChD,MAAM,EAAE,mBAAmB,KAAK,EAAE;gBAClC,MAAM,EAAE,cAAc;gBACtB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,iBAAiB;YACjB,sBAAsB;YACtB,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,cAAc;YACd,eAAe;YACf,aAAa;YACb,UAAU;YACV,GAAG,mBAAmB;SACvB;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,IAAI,EAAE,gBAA4B;QAClC,QAAQ,EAAE;YACR,aAAa,EAAE,2BAA2B;SAC3C;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CACnC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,OAAc,EACd,cAAqB,EACrB,aAAoB;IAEpB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;IAE3D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;IAC5F,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACpG,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAChG,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAC9G,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAC/F,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAC9G,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;IAC3G,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;IAC1F,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC;IAC3G,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,oBAAoB;IACpB,MAAM,WAAW,GAAG,IAAA,gCAAiB,GAAE,CAAC;IACxC,MAAM,eAAe,GAAG,eAAO,CAAC,cAAc,CAAC,cAAc,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;IAE5F,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACxF,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpG,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/F,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QACjG,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7F,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEjG,iBAAiB;QACjB,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE;KAC3G,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,gBAAgB;YAChB,kBAAkB;YAClB,eAAe;YACf,sBAAsB;YACtB,iBAAiB;YACjB,sBAAsB;YACtB,mBAAmB;YACnB,eAAe;YACf,aAAa;YACb,qBAAqB;YACrB,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,IAAI,EAAE,SAAqB;KAC5B,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CACtC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,WAAkB,EAClB,OAAc,EACd,YAAmB;IAEnB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC;IAE9D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAClH,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,WAAW,CAAC,CAAC;IAC5G,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAClH,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,WAAW,CAAC,CAAC;IAC/G,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;IAClG,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC;IACxH,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,OAAO,CAAC,CAAC;IACzH,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;IACzG,MAAM,uBAAuB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,YAAY,CAAC,CAAC;IACtH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,oBAAoB;IACpB,MAAM,cAAc,GAAG,IAAA,mCAAoB,GAAE,CAAC;IAC9C,MAAM,cAAc,GAAG,IAAA,mCAAoB,GAAE,CAAC;IAC9C,MAAM,iBAAiB,GAAG,IAAA,sCAAuB,GAAE,CAAC;IACpD,MAAM,QAAQ,GAAG,IAAA,8BAAe,GAAE,CAAC;IACnC,MAAM,kBAAkB,GAAG,eAAO,CAAC,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;IACxG,MAAM,kBAAkB,GAAG,eAAO,CAAC,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;IACxG,MAAM,qBAAqB,GAAG,eAAO,CAAC,cAAc,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;IACpH,MAAM,YAAY,GAAG,eAAO,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IAEjF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjG,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnH,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChH,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;QACjG,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/F,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACxG,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,+BAA+B,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtH,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,yBAAyB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3H,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtH,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,uCAAuC,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3H,EAAE,IAAI,EAAE,qCAAqC,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9H,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEtG,iBAAiB;QACjB,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,EAAE;QACjH,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,UAAU,EAAE;KAC7H,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,sBAAsB;YACtB,oBAAoB;YACpB,sBAAsB;YACtB,YAAY;YACZ,qBAAqB;YACrB,kBAAkB;YAClB,kBAAkB;YAClB,wBAAwB;YACxB,yBAAyB;YACzB,mBAAmB;YACnB,kBAAkB;YAClB,qBAAqB;YACrB,uBAAuB;YACvB,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,IAAI,EAAE,YAAwB;KAC/B,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CACvC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,YAAmB,EACnB,cAAqB,EACrB,WAAkB;IAElB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,IAAI,EAAE,CAAC,CAAC;IAE/D,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;IACxF,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,cAAc,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;IAC9F,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAChG,MAAM,UAAU,GAAG,eAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;IAC/E,MAAM,2BAA2B,GAAG,eAAO,CAAC,eAAe,CAAC,0BAA0B,EAAE,0BAA0B,EAAE,cAAc,CAAC,CAAC;IACpI,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,cAAc,CAAC,CAAC;IAC/G,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,WAAW,CAAC,CAAC;IAC5G,MAAM,uBAAuB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,YAAY,CAAC,CAAC;IACtH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,oBAAoB;IACpB,MAAM,cAAc,GAAG,IAAA,mCAAoB,GAAE,CAAC;IAC9C,MAAM,cAAc,GAAG,IAAA,mCAAoB,GAAE,CAAC;IAC9C,MAAM,kBAAkB,GAAG,eAAO,CAAC,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;IACxG,MAAM,kBAAkB,GAAG,eAAO,CAAC,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;IAExG,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QACvF,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/F,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE;QAC7F,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,0BAA0B,EAAE,IAAI,EAAE,aAAa,EAAE;QACpH,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,aAAa,EAAE;QAC1G,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,0BAA0B,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvH,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,kCAAkC,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QACxH,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEtG,iBAAiB;QACjB,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE;KAC1G,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,cAAc;YACd,kBAAkB;YAClB,eAAe;YACf,eAAe;YACf,UAAU;YACV,2BAA2B;YAC3B,oBAAoB;YACpB,oBAAoB;YACpB,kBAAkB;YAClB,uBAAuB;YACvB,UAAU;SACX;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,IAAI,EAAE,aAAyB;KAChC,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa SDLC Workflows\n *\n * This module provides workflow templates for Software Development Life Cycle (SDLC) phases:\n * - Planning\n * - Requirements\n * - Design\n * - Implementation\n * - Testing\n * - Deployment\n * - Maintenance\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, SDLCTask } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { Logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\nimport { codessaMemoryProvider as codessaMemoryManager } from '../../memory/codessa/codessaMemory';\nimport { MemorySource, MemoryType } from '../../memory/types';\nimport {\n  createCodeAnalysisTool,\n  createTestingTool,\n  createDocumentationTool,\n  createDeploymentTool,\n  createMonitoringTool,\n  createCI_CDTool\n} from './vectorStores';\n\n/**\n * Create a Planning workflow for project planning and roadmap creation\n */\nexport function createPlanningWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  productOwnerAgent: Agent,\n  techLeadAgent: Agent,\n  projectManagerAgent: Agent,\n  tools: ITool[] = []\n): GraphDefinition {\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const requirementsAnalysisNode = Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', productOwnerAgent);\n  const scopeDefinitionNode = Codessa.createAgentNode('scope-definition', 'Scope Definition', projectManagerAgent);\n  const resourcePlanningNode = Codessa.createAgentNode('resource-planning', 'Resource Planning', projectManagerAgent);\n  const technicalFeasibilityNode = Codessa.createAgentNode('technical-feasibility', 'Technical Feasibility', techLeadAgent);\n  const roadmapCreationNode = Codessa.createAgentNode('roadmap-creation', 'Roadmap Creation', projectManagerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  const planningMemoryManager = {\n    async storeFeasibilityAnalysis(feasibilityData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(feasibilityData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['planning', 'technical-feasibility', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved technical feasibility analysis to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save technical feasibility analysis to memory:', error);\n      }\n    },\n\n    async retrievePreviousRoadmaps(): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query: 'project roadmap planning',\n          limit: 3\n        });\n\n        const roadmaps = memories.map(m => {\n          try {\n            return JSON.parse(m.content);\n          } catch (_e) {\n            return m.content;\n          }\n        });\n\n        Logger.instance.info(`Retrieved ${roadmaps.length} previous roadmaps from memory`);\n        return roadmaps;\n      } catch (error) {\n        Logger.instance.error('Failed to retrieve previous roadmaps from memory:', error);\n        return [];\n      }\n    },\n\n    async storeRoadmap(roadmap: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(roadmap),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'project' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['planning', 'roadmap', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved project roadmap to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save project roadmap to memory:', error);\n      }\n    }\n  };\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },\n    { name: 'requirements-to-scope', source: 'requirements-analysis', target: 'scope-definition', type: 'default' },\n    { name: 'scope-to-resources', source: 'scope-definition', target: 'resource-planning', type: 'default' },\n    { name: 'resources-to-feasibility', source: 'resource-planning', target: 'technical-feasibility', type: 'default' },\n    { name: 'feasibility-to-roadmap', source: 'technical-feasibility', target: 'roadmap-creation', type: 'default' },\n    { name: 'roadmap-to-output', source: 'roadmap-creation', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect technical feasibility to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `feasibility-to-tool-${index}`,\n        source: 'technical-feasibility',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to roadmap creation\n      edges.push({\n        name: `tool-${index}-to-roadmap`,\n        source: `tool-${index}`,\n        target: 'roadmap-creation',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      requirementsAnalysisNode,\n      scopeDefinitionNode,\n      resourcePlanningNode,\n      technicalFeasibilityNode,\n      roadmapCreationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    type: 'planning' as SDLCTask,\n    metadata: {\n      memoryManager: planningMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a Requirements workflow for gathering and analyzing requirements\n */\nexport function createRequirementsWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  businessAnalystAgent: Agent,\n  techLeadAgent: Agent,\n  productOwnerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const stakeholderInterviewNode = Codessa.createAgentNode('stakeholder-interview', 'Stakeholder Interview', businessAnalystAgent);\n  const requirementsGatheringNode = Codessa.createAgentNode('requirements-gathering', 'Requirements Gathering', businessAnalystAgent);\n  const requirementsAnalysisNode = Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', techLeadAgent);\n  const requirementsPrioritizationNode = Codessa.createAgentNode('requirements-prioritization', 'Requirements Prioritization', productOwnerAgent);\n  const requirementsDocumentationNode = Codessa.createAgentNode('requirements-documentation', 'Requirements Documentation', businessAnalystAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  const requirementsMemoryManager = {\n    async storeStakeholderInterviews(interviewData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(interviewData),\n          metadata: {\n            source: 'user' as MemorySource,\n            type: 'conversation' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['requirements', 'interview', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved stakeholder interview data to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save stakeholder interview data to memory:', error);\n      }\n    },\n\n    async storeRequirements(requirements: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(requirements),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['requirements', 'documentation', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved requirements to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save requirements to memory:', error);\n      }\n    },\n\n    async retrieveSimilarRequirements(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5\n        });\n\n        const requirements = memories.map(m => {\n          try {\n            return JSON.parse(m.content);\n          } catch (_e) {\n            return m.content;\n          }\n        });\n\n        Logger.instance.info(`Retrieved ${requirements.length} similar requirements from memory`);\n        return requirements;\n      } catch (error) {\n        Logger.instance.error('Failed to retrieve similar requirements from memory:', error);\n        return [];\n      }\n    }\n  };\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-interview', source: 'input', target: 'stakeholder-interview', type: 'default' },\n    { name: 'interview-to-gathering', source: 'stakeholder-interview', target: 'requirements-gathering', type: 'default' },\n    { name: 'gathering-to-analysis', source: 'requirements-gathering', target: 'requirements-analysis', type: 'default' },\n    { name: 'analysis-to-prioritization', source: 'requirements-analysis', target: 'requirements-prioritization', type: 'default' },\n    { name: 'prioritization-to-documentation', source: 'requirements-prioritization', target: 'requirements-documentation', type: 'default' },\n    { name: 'documentation-to-output', source: 'requirements-documentation', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect requirements analysis to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `analysis-to-tool-${index}`,\n        source: 'requirements-analysis',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to requirements prioritization\n      edges.push({\n        name: `tool-${index}-to-prioritization`,\n        source: `tool-${index}`,\n        target: 'requirements-prioritization',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      stakeholderInterviewNode,\n      requirementsGatheringNode,\n      requirementsAnalysisNode,\n      requirementsPrioritizationNode,\n      requirementsDocumentationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    type: 'requirements' as SDLCTask,\n    metadata: {\n      memoryManager: requirementsMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a Design workflow for system architecture and design\n */\nexport function createDesignWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  architectAgent: Agent,\n  uiDesignerAgent: Agent,\n  databaseDesignerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const architectureDesignNode = Codessa.createAgentNode('architecture-design', 'Architecture Design', architectAgent);\n  const databaseDesignNode = Codessa.createAgentNode('database-design', 'Database Design', databaseDesignerAgent);\n  const uiDesignNode = Codessa.createAgentNode('ui-design', 'UI Design', uiDesignerAgent);\n  const apiDesignNode = Codessa.createAgentNode('api-design', 'API Design', architectAgent);\n  const designReviewNode = Codessa.createAgentNode('design-review', 'Design Review', architectAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  const designMemoryManager = {\n    async storeArchitectureDesign(designData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(designData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['design', 'architecture', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved architecture design to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save architecture design to memory:', error);\n      }\n    },\n\n    async storeDatabaseDesign(designData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(designData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['design', 'database', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved database design to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save database design to memory:', error);\n      }\n    },\n\n    async storeUIDesign(designData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(designData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['design', 'ui', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved UI design to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save UI design to memory:', error);\n      }\n    },\n\n    async storeAPIDesign(designData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(designData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['design', 'api', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved API design to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save API design to memory:', error);\n      }\n    },\n\n    async retrieveDesignPatterns(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5\n        });\n\n        const patterns = memories.map(m => {\n          try {\n            return JSON.parse(m.content);\n          } catch (_e) {\n            return m.content;\n          }\n        });\n\n        Logger.instance.info(`Retrieved ${patterns.length} design patterns from memory`);\n        return patterns;\n      } catch (error) {\n        Logger.instance.error('Failed to retrieve design patterns from memory:', error);\n        return [];\n      }\n    }\n  };\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-architecture', source: 'input', target: 'architecture-design', type: 'default' },\n    { name: 'architecture-to-database', source: 'architecture-design', target: 'database-design', type: 'default' },\n    { name: 'architecture-to-ui', source: 'architecture-design', target: 'ui-design', type: 'default' },\n    { name: 'architecture-to-api', source: 'architecture-design', target: 'api-design', type: 'default' },\n    { name: 'database-to-review', source: 'database-design', target: 'design-review', type: 'default' },\n    { name: 'ui-to-review', source: 'ui-design', target: 'design-review', type: 'default' },\n    { name: 'api-to-review', source: 'api-design', target: 'design-review', type: 'default' },\n    { name: 'review-to-output', source: 'design-review', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect architecture design to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `architecture-to-tool-${index}`,\n        source: 'architecture-design',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to design review\n      edges.push({\n        name: `tool-${index}-to-review`,\n        source: `tool-${index}`,\n        target: 'design-review',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      architectureDesignNode,\n      databaseDesignNode,\n      uiDesignNode,\n      apiDesignNode,\n      designReviewNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    type: 'design' as SDLCTask,\n    metadata: {\n      memoryManager: designMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create an Implementation workflow for code development\n */\nexport function createImplementationWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  developerAgent: Agent,\n  reviewerAgent: Agent,\n  qaAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating Implementation workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const taskBreakdownNode = Codessa.createAgentNode('task-breakdown', 'Task Breakdown', developerAgent);\n  const codeImplementationNode = Codessa.createAgentNode('code-implementation', 'Code Implementation', developerAgent);\n  const unitTestingNode = Codessa.createAgentNode('unit-testing', 'Unit Testing', developerAgent);\n  const codeReviewNode = Codessa.createAgentNode('code-review', 'Code Review', reviewerAgent);\n  const integrationNode = Codessa.createAgentNode('integration', 'Integration', developerAgent);\n  const qaTestingNode = Codessa.createAgentNode('qa-testing', 'QA Testing', qaAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create tool nodes\n  const codeAnalysisTool = createCodeAnalysisTool();\n  const testingTool = createTestingTool();\n  const codeAnalysisNode = Codessa.createToolNode('code-analysis-tool', 'Code Analysis Tool', codeAnalysisTool);\n  const testingToolNode = Codessa.createToolNode('testing-tool', 'Testing Tool', testingTool);\n\n  // Add additional tool nodes if tools are provided\n  const additionalToolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`additional-tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  const implementationMemoryManager = {\n    async storeCodeImplementation(codeData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(codeData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'code' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['implementation', 'code', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved code implementation to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save code implementation to memory:', error);\n      }\n    },\n\n    async storeTestResults(testResults: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(testResults),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'insight' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['implementation', 'testing', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved test results to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save test results to memory:', error);\n      }\n    },\n\n    async storeCodeReview(reviewData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(reviewData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'insight' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['implementation', 'review', 'sdlc']\n          }\n        });\n        Logger.instance.info('Saved code review to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save code review to memory:', error);\n      }\n    },\n\n    async retrieveCodeExamples(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5\n        });\n\n        const examples = memories.map(m => {\n          try {\n            return JSON.parse(m.content);\n          } catch (_e) {\n            return m.content;\n          }\n        });\n\n        Logger.instance.info(`Retrieved ${examples.length} code examples from memory`);\n        return examples;\n      } catch (error) {\n        Logger.instance.error('Failed to retrieve code examples from memory:', error);\n        return [];\n      }\n    }\n  };\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-breakdown', source: 'input', target: 'task-breakdown', type: 'default' },\n    { name: 'breakdown-to-implementation', source: 'task-breakdown', target: 'code-implementation', type: 'default' },\n    { name: 'implementation-to-analysis', source: 'code-implementation', target: 'code-analysis-tool', type: 'default' },\n    { name: 'analysis-to-unit-testing', source: 'code-analysis-tool', target: 'unit-testing', type: 'default' },\n    { name: 'unit-testing-to-testing-tool', source: 'unit-testing', target: 'testing-tool', type: 'default' },\n    { name: 'testing-tool-to-review', source: 'testing-tool', target: 'code-review', type: 'default' },\n    { name: 'review-to-integration', source: 'code-review', target: 'integration', type: 'default' },\n    { name: 'integration-to-qa', source: 'integration', target: 'qa-testing', type: 'default' },\n    { name: 'qa-to-output', source: 'qa-testing', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'review-to-implementation', source: 'code-review', target: 'code-implementation', type: 'feedback' },\n    { name: 'qa-to-implementation', source: 'qa-testing', target: 'code-implementation', type: 'feedback' }\n  ];\n\n  // Add additional tool edges if tools are provided\n  if (additionalToolNodes.length > 0) {\n    // Connect code implementation to additional tools\n    additionalToolNodes.forEach((_, index) => {\n      edges.push({\n        name: `implementation-to-additional-tool-${index}`,\n        source: 'code-implementation',\n        target: `additional-tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect additional tools back to unit testing\n      edges.push({\n        name: `additional-tool-${index}-to-unit-testing`,\n        source: `additional-tool-${index}`,\n        target: 'unit-testing',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      taskBreakdownNode,\n      codeImplementationNode,\n      codeAnalysisNode,\n      unitTestingNode,\n      testingToolNode,\n      codeReviewNode,\n      integrationNode,\n      qaTestingNode,\n      outputNode,\n      ...additionalToolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    type: 'implementation' as SDLCTask,\n    metadata: {\n      memoryManager: implementationMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a Testing workflow for comprehensive testing\n */\nexport function createTestingWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  qaAgent: Agent,\n  developerAgent: Agent,\n  securityAgent: Agent\n): GraphDefinition {\n  Logger.instance.info(`Creating Testing workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const testPlanningNode = Codessa.createAgentNode('test-planning', 'Test Planning', qaAgent);\n  const testCaseDesignNode = Codessa.createAgentNode('test-case-design', 'Test Case Design', qaAgent);\n  const unitTestingNode = Codessa.createAgentNode('unit-testing', 'Unit Testing', developerAgent);\n  const integrationTestingNode = Codessa.createAgentNode('integration-testing', 'Integration Testing', qaAgent);\n  const systemTestingNode = Codessa.createAgentNode('system-testing', 'System Testing', qaAgent);\n  const performanceTestingNode = Codessa.createAgentNode('performance-testing', 'Performance Testing', qaAgent);\n  const securityTestingNode = Codessa.createAgentNode('security-testing', 'Security Testing', securityAgent);\n  const bugFixingNode = Codessa.createAgentNode('bug-fixing', 'Bug Fixing', developerAgent);\n  const regressionTestingNode = Codessa.createAgentNode('regression-testing', 'Regression Testing', qaAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create tool nodes\n  const testingTool = createTestingTool();\n  const testingToolNode = Codessa.createToolNode('testing-tool', 'Testing Tool', testingTool);\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-planning', source: 'input', target: 'test-planning', type: 'default' },\n    { name: 'planning-to-design', source: 'test-planning', target: 'test-case-design', type: 'default' },\n    { name: 'design-to-unit', source: 'test-case-design', target: 'unit-testing', type: 'default' },\n    { name: 'unit-to-integration', source: 'unit-testing', target: 'integration-testing', type: 'default' },\n    { name: 'integration-to-system', source: 'integration-testing', target: 'system-testing', type: 'default' },\n    { name: 'system-to-performance', source: 'system-testing', target: 'performance-testing', type: 'default' },\n    { name: 'performance-to-security', source: 'performance-testing', target: 'security-testing', type: 'default' },\n    { name: 'security-to-tool', source: 'security-testing', target: 'testing-tool', type: 'default' },\n    { name: 'tool-to-bug-fixing', source: 'testing-tool', target: 'bug-fixing', type: 'default' },\n    { name: 'bug-fixing-to-regression', source: 'bug-fixing', target: 'regression-testing', type: 'default' },\n    { name: 'regression-to-output', source: 'regression-testing', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'regression-to-bug-fixing', source: 'regression-testing', target: 'bug-fixing', type: 'feedback' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      testPlanningNode,\n      testCaseDesignNode,\n      unitTestingNode,\n      integrationTestingNode,\n      systemTestingNode,\n      performanceTestingNode,\n      securityTestingNode,\n      testingToolNode,\n      bugFixingNode,\n      regressionTestingNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    type: 'testing' as SDLCTask\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a Deployment workflow for releasing software\n */\nexport function createDeploymentWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  devOpsAgent: Agent,\n  qaAgent: Agent,\n  supportAgent: Agent\n): GraphDefinition {\n  Logger.instance.info(`Creating Deployment workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const releasePreparationNode = Codessa.createAgentNode('release-preparation', 'Release Preparation', devOpsAgent);\n  const environmentSetupNode = Codessa.createAgentNode('environment-setup', 'Environment Setup', devOpsAgent);\n  const deploymentPlanningNode = Codessa.createAgentNode('deployment-planning', 'Deployment Planning', devOpsAgent);\n  const stagingDeploymentNode = Codessa.createAgentNode('staging-deployment', 'Staging Deployment', devOpsAgent);\n  const stagingTestingNode = Codessa.createAgentNode('staging-testing', 'Staging Testing', qaAgent);\n  const productionDeploymentNode = Codessa.createAgentNode('production-deployment', 'Production Deployment', devOpsAgent);\n  const postDeploymentTestingNode = Codessa.createAgentNode('post-deployment-testing', 'Post-Deployment Testing', qaAgent);\n  const monitoringSetupNode = Codessa.createAgentNode('monitoring-setup', 'Monitoring Setup', devOpsAgent);\n  const documentationUpdateNode = Codessa.createAgentNode('documentation-update', 'Documentation Update', supportAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create tool nodes\n  const deploymentTool = createDeploymentTool();\n  const monitoringTool = createMonitoringTool();\n  const documentationTool = createDocumentationTool();\n  const cicdTool = createCI_CDTool();\n  const deploymentToolNode = Codessa.createToolNode('deployment-tool', 'Deployment Tool', deploymentTool);\n  const monitoringToolNode = Codessa.createToolNode('monitoring-tool', 'Monitoring Tool', monitoringTool);\n  const documentationToolNode = Codessa.createToolNode('documentation-tool', 'Documentation Tool', documentationTool);\n  const cicdToolNode = Codessa.createToolNode('cicd-tool', 'CI/CD Tool', cicdTool);\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-preparation', source: 'input', target: 'release-preparation', type: 'default' },\n    { name: 'preparation-to-environment', source: 'release-preparation', target: 'environment-setup', type: 'default' },\n    { name: 'environment-to-planning', source: 'environment-setup', target: 'deployment-planning', type: 'default' },\n    { name: 'planning-to-cicd', source: 'deployment-planning', target: 'cicd-tool', type: 'default' },\n    { name: 'cicd-to-staging', source: 'cicd-tool', target: 'staging-deployment', type: 'default' },\n    { name: 'staging-to-testing', source: 'staging-deployment', target: 'staging-testing', type: 'default' },\n    { name: 'testing-to-deployment-tool', source: 'staging-testing', target: 'deployment-tool', type: 'default' },\n    { name: 'deployment-tool-to-production', source: 'deployment-tool', target: 'production-deployment', type: 'default' },\n    { name: 'production-to-post-testing', source: 'production-deployment', target: 'post-deployment-testing', type: 'default' },\n    { name: 'post-testing-to-monitoring', source: 'post-deployment-testing', target: 'monitoring-setup', type: 'default' },\n    { name: 'monitoring-to-tool', source: 'monitoring-setup', target: 'monitoring-tool', type: 'default' },\n    { name: 'monitoring-tool-to-documentation-tool', source: 'monitoring-tool', target: 'documentation-tool', type: 'default' },\n    { name: 'documentation-tool-to-documentation', source: 'documentation-tool', target: 'documentation-update', type: 'default' },\n    { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'staging-testing-to-staging', source: 'staging-testing', target: 'staging-deployment', type: 'feedback' },\n    { name: 'post-testing-to-production', source: 'post-deployment-testing', target: 'production-deployment', type: 'feedback' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      releasePreparationNode,\n      environmentSetupNode,\n      deploymentPlanningNode,\n      cicdToolNode,\n      stagingDeploymentNode,\n      stagingTestingNode,\n      deploymentToolNode,\n      productionDeploymentNode,\n      postDeploymentTestingNode,\n      monitoringSetupNode,\n      monitoringToolNode,\n      documentationToolNode,\n      documentationUpdateNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    type: 'deployment' as SDLCTask\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a Maintenance workflow for ongoing support and updates\n */\nexport function createMaintenanceWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  supportAgent: Agent,\n  developerAgent: Agent,\n  devOpsAgent: Agent\n): GraphDefinition {\n  Logger.instance.info(`Creating Maintenance workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const monitoringNode = Codessa.createAgentNode('monitoring', 'Monitoring', devOpsAgent);\n  const issueTriageNode = Codessa.createAgentNode('issue-triage', 'Issue Triage', supportAgent);\n  const bugAnalysisNode = Codessa.createAgentNode('bug-analysis', 'Bug Analysis', developerAgent);\n  const hotfixNode = Codessa.createAgentNode('hotfix', 'Hotfix', developerAgent);\n  const performanceOptimizationNode = Codessa.createAgentNode('performance-optimization', 'Performance Optimization', developerAgent);\n  const securityPatchingNode = Codessa.createAgentNode('security-patching', 'Security Patching', developerAgent);\n  const updateDeploymentNode = Codessa.createAgentNode('update-deployment', 'Update Deployment', devOpsAgent);\n  const documentationUpdateNode = Codessa.createAgentNode('documentation-update', 'Documentation Update', supportAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Create tool nodes\n  const monitoringTool = createMonitoringTool();\n  const deploymentTool = createDeploymentTool();\n  const monitoringToolNode = Codessa.createToolNode('monitoring-tool', 'Monitoring Tool', monitoringTool);\n  const deploymentToolNode = Codessa.createToolNode('deployment-tool', 'Deployment Tool', deploymentTool);\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-monitoring', source: 'input', target: 'monitoring', type: 'default' },\n    { name: 'monitoring-to-tool', source: 'monitoring', target: 'monitoring-tool', type: 'default' },\n    { name: 'monitoring-tool-to-triage', source: 'monitoring-tool', target: 'issue-triage', type: 'default' },\n    { name: 'triage-to-analysis', source: 'issue-triage', target: 'bug-analysis', type: 'default' },\n    { name: 'analysis-to-hotfix', source: 'bug-analysis', target: 'hotfix', type: 'conditional' },\n    { name: 'analysis-to-performance', source: 'bug-analysis', target: 'performance-optimization', type: 'conditional' },\n    { name: 'analysis-to-security', source: 'bug-analysis', target: 'security-patching', type: 'conditional' },\n    { name: 'hotfix-to-deployment', source: 'hotfix', target: 'update-deployment', type: 'default' },\n    { name: 'performance-to-deployment', source: 'performance-optimization', target: 'update-deployment', type: 'default' },\n    { name: 'security-to-deployment', source: 'security-patching', target: 'update-deployment', type: 'default' },\n    { name: 'deployment-to-tool', source: 'update-deployment', target: 'deployment-tool', type: 'default' },\n    { name: 'deployment-tool-to-documentation', source: 'deployment-tool', target: 'documentation-update', type: 'default' },\n    { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'deployment-to-monitoring', source: 'update-deployment', target: 'monitoring', type: 'feedback' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      monitoringNode,\n      monitoringToolNode,\n      issueTriageNode,\n      bugAnalysisNode,\n      hotfixNode,\n      performanceOptimizationNode,\n      securityPatchingNode,\n      updateDeploymentNode,\n      deploymentToolNode,\n      documentationUpdateNode,\n      outputNode\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    type: 'maintenance' as SDLCTask\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}"]}