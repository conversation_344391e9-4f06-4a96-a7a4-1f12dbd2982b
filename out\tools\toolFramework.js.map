{"version": 3, "file": "toolFramework.js", "sourceRoot": "", "sources": ["../../src/tools/toolFramework.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2wFA,4DAwCC;AAED,8DAoCC;AAUD,8DAiCC;AAp4FD,8BAA8B;AAC9B,uCAAyB;AACzB,2CAA6B;AAC7B,2BAAoC;AACpC,mCAAsC;AAEtC,wDAAwD;AACxD,+CAAiC;AAgBjC,qCAAqC;AACrC,qDAc0B;AAC1B,sCAAsC;AAGtC,qCAAqC;AACrC,2DAAwD;AAKxD,4CAA4C;AAC5C,uEAAqI;AACrI,yEAAsE;AACtE,2EAAwE;AAExE,mCAAmC;AACnC,6BAAkC;AAgElC;;;;GAIG;AACH,MAAsB,cAAe,SAAQ,uCAAsB;IAC9C,gBAAgB,CAAqB;IACrC,UAAU,CAAa;IACvB,eAAe,GAAsC,IAAI,GAAG,EAAE,CAAC;IAC/D,gBAAgB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IACrD,aAAa,GAAG,IAAI,MAAM,CAAC,YAAY,EAIpD,CAAC;IAEL,2CAA2C;IAC1B,cAAc,CAAgB;IAC9B,gBAAgB,CAAyB;IACzC,iBAAiB,CAA0B;IAC3C,gBAAgB,GAAsC,IAAI,GAAG,EAAE,CAAC;IAChE,gBAAgB,GAA0B,IAAI,GAAG,EAAE,CAAC;IAErE,oDAAoD;IACnC,kBAAkB,GAAG,IAAI,MAAM,CAAC,YAAY,EAIzD,CAAC;IACY,oBAAoB,GAAG,IAAI,MAAM,CAAC,YAAY,EAI3D,CAAC;IAEW,YAAY,GAIvB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IACd,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;IAC9C,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;IAClD,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAEtE,YACI,QAMC;QAED,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;QAEhF,0CAA0C;QAC1C,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,aAAa,IAAI,6BAAa,CAAC,WAAW,EAAE,CAAC;QAC5E,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,IAAI,iCAAe,CAAC;QACpE,IAAI,CAAC,iBAAiB,GAAG,mCAAgB,CAAC;QAE1C,uCAAuC;QACvC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,6BAA6B,EAAE,CAAC;QAErC,wBAAwB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;YACtB,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACnC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAA0B,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;YACzE,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,cAAc,CAAC,SAAiB;QACnC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC/C,CAAC;IAES,YAAY;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAIS,kBAAkB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACO,kBAAkB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACO,mBAAmB;QACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,2BAA2B;QAC/B,+BAA+B;QAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACnC,iCAAiC;YACjC,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAC5B,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE;gBACpC,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,QAAQ,EAAE;oBACN,MAAM,EAAE,kBAAkC;oBAC1C,IAAI,EAAE,YAA0B;oBAChC,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC;oBACjC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;iBACxC;aACJ,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,6BAA6B;QACjC,iCAAiC;QACjC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACrC,0CAA0C;YAC1C,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAC5B,SAAS,EAAE,oBAAoB;gBAC/B,KAAK,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;gBACzD,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO;gBAC7B,QAAQ,EAAE;oBACN,MAAM,EAAE,UAA0B;oBAClC,IAAI,EAAE,YAA0B;oBAChC,IAAI,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC;oBAChD,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;iBAC/C;aACJ,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,oBAAoB,CAAC,SAapC;QACG,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,SAAS,CAAC,SAAS,EACnB,SAAS,CAAC,KAAK,EACf;YACI,GAAG,SAAS,CAAC,QAAQ;YACrB,OAAO,EAAE,SAAS,CAAC,OAAO;SAC7B,CACJ,CAAC;QAEF,eAAe,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAC1C,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC1E,eAAe,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3C,eAAe,CAAC,QAAQ,GAAG,eAAe,CAAC,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC;QAE3F,kCAAkC;QAClC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;QAE/D,gCAAgC;QAChC,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;gBAChC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;oBACpB,QAAQ,EAAE,eAAe,CAAC,QAAQ;oBAClC,SAAS,EAAE,eAAe,CAAC,SAAS;oBACpC,KAAK,EAAE,eAAe,CAAC,KAAK;oBAC5B,MAAM,EAAE,eAAe,CAAC,MAAM;oBAC9B,QAAQ,EAAE,eAAe,CAAC,QAAQ;oBAClC,OAAO,EAAE,SAAS,CAAC,OAAO;iBAC7B,CAAC;gBACF,QAAQ,EAAE,eAAe,CAAC,QAAQ;aACrC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;OAEG;IACO,qBAAqB,CAC3B,QAAgB,EAChB,SAAiB,EACjB,KAA8B,EAC9B,WAQK,EAAE;QAEP,OAAO;YACH,EAAE,EAAE,GAAG,QAAQ,IAAI,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YAC5C,QAAQ;YACR,SAAS;YACT,KAAK;YACL,QAAQ,EAAE;gBACN,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,MAAsB;gBACjD,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,YAA0B;gBACjD,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC;gBAC1D,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,GAAG;gBACtC,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;gBAC3C,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB;YACD,KAAK,EAAE;gBACH,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IAEH;;OAEG;IACO,KAAK,CAAC,WAAW,CACvB,OAAe,EACf,WAOK,EAAE;QAEP,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YACpD,OAAO;YACP,QAAQ,EAAE;gBACN,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,MAAsB;gBACjD,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,YAA0B;gBACjD,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC5D,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,GAAG;gBACtC,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;gBAC3C,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;gBAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACzB,SAAS,EAAE,OAAO;YAClB,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAC1B,KAAa,EACb,UAAwC,EAAE;QAE1C,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,KAAK,EAAE;gBACpE,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;gBAC1B,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnH,GAAG,OAAO;aACb,CAAC,CAAC;YAEH,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBACzB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAsB,EAAE,IAAI,EAAE,OAAqB,EAAE,EAAiB;gBACzI,OAAO,EAAE,IAAI;aAChB,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBACzB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAsB,EAAE,IAAI,EAAE,OAAqB,EAAE,EAAiB;gBACxI,OAAO,EAAE,KAAK;aACjB,CAAC,CAAC;YACH,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;OAEG;IAEH;;OAEG;IACO,KAAK,CAAC,eAAe,CAC3B,UAAkB,EAClB,SAA8B,EAAE,EAChC,OAAkC;QAElC,IAAI,CAAC;YACD,6BAA6B;YAC7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAC1E,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,IAAI,yBAAQ,CAAC,kBAAkB,CAAC,CAAC;YAElD,wBAAwB;YACxB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEhD,2BAA2B;YAC3B,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;gBACrC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;oBAC3B,UAAU;oBACV,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE;wBACJ,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,EAAE,QAAQ,EAAE;wBACpB,QAAQ,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;qBACjC;iBACJ,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEvD,qCAAqC;YACrC,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAC5B,SAAS,EAAE,oBAAoB;gBAC/B,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;gBAC7B,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,QAAQ,EAAE;oBACN,MAAM,EAAE,UAA0B;oBAClC,IAAI,EAAE,YAA0B;oBAChC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;oBAC9B,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;oBACtC,gBAAgB,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC,IAAI;iBAC5D;aACJ,CAAC,CAAC;YAEH,2BAA2B;YAC3B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEzC,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAE1E,2BAA2B;YAC3B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEzC,MAAM,WAAW,GAAuB;gBACpC,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,QAAQ,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;aACxC,CAAC;YAEF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAC3B,UAAU;gBACV,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,WAAW;aACtB,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACvB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CACpB,UAA8B;QAE9B,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACpD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,GAAW;QACnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAAC,UAAkB;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC;gBACD,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACxB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBAEzC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;oBAC3B,UAAU;oBACV,IAAI,EAAE,WAAW;oBACjB,MAAM,EAAE;wBACJ,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,oBAAoB;wBAC3B,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;qBAChC;iBACJ,CAAC,CAAC;gBAEH,OAAO,IAAI,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;gBACzE,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACO,iBAAiB;QACvB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,YAAY,EAGxC,CAAC;QAEL,IAAI,WAAW,EAAE,CAAC;YACd,2CAA2C;YAC3C,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;gBACpC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;QACP,CAAC;QAED,OAAO;YACH,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM;YACnC,UAAU,EAAE,IAAI,CAAC,YAAY;YAC7B,cAAc,EAAE,YAAY,CAAC,KAAK;SACrC,CAAC;IACN,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,OAAwB,EAAE,OAAyB;QAChF,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM;YAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAEtC,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnF,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,MAAM;gBACN,OAAO,EAAE;oBACL,OAAO;oBACP,OAAO;oBACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACxB;aACJ,CAAC,CAAC;QACP,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACO,wBAAwB;QAC9B,wBAAwB;QACxB,IAAI,CAAC,eAAe,CAAC;YACjB,OAAO,EAAE,yBAAyB;YAClC,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,OAAO;YACjB,GAAG,EAAE,CAAC,KAAuB,EAAE,QAA6B,EAAE,EAAE;gBAC5D,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,eAAe,EACf,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,sBAAsB;oBAC/B,KAAK,EAAE,eAAe;oBACtB,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBACjD,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,eAAe,CAAC;YACjB,OAAO,EAAE,eAAe;YACxB,MAAM,EAAE,iBAAiB;YACzB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,SAAS;YACnB,GAAG,EAAE,CAAC,KAAuB,EAAE,QAA6B,EAAE,EAAE;gBAC5D,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,iBAAiB,EACjB,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,wBAAwB;oBACjC,KAAK,EAAE,iBAAiB;oBACxB,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBACjD,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,eAAe,CAAC;YACjB,OAAO,EAAE,yBAAyB;YAClC,MAAM,EAAE,iBAAiB;YACzB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,SAAS;YACnB,GAAG,EAAE,CAAC,KAAuB,EAAE,QAA6B,EAAE,EAAE;gBAC5D,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,iBAAiB,EACjB,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,wBAAwB;oBACjC,KAAK,EAAE,iBAAiB;oBACxB,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;iBACjD,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,aAAa,CACzB,OAAe,EACf,OAKC;QAED,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC5E,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBACzC,OAAO;oBACH,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,MAAM;oBACd,OAAO;oBACP,QAAQ,EAAE,CAAC;oBACX,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC;YACN,CAAC;YACD,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,mDAAmD;YACnD,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC5B,mBAAmB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAC3E,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAClC,CAAC;YACF,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC;YAE/B,MAAM,WAAW,GAAG,CAAC,UAAU,CAAC,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,WAAW,CAAC,CAAC;YAExE,wBAAwB;YACxB,MAAM,IAAI,CAAC,kBAAkB,CACzB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EACtD,EAAE,CACL,CAAC;YACF,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,GAAe,EAAE,WAAgC;QACtE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QAC3E,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,OAAuB;QAC/C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IAEH;;OAEG;IACO,KAAK,CAAC,qCAAqC,CACjD,OAAe,EACf,OAOC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG;YACX,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,KAAK;YAClC,YAAY,EAAE,OAAO,EAAE,YAAY,IAAI,IAAI;YAC3C,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,KAAK;YACxC,gBAAgB,EAAE,OAAO,EAAE,gBAAgB;YAC3C,iBAAiB,EAAE,OAAO,EAAE,iBAAiB,IAAI,IAAI;YACrD,aAAa,EAAE,OAAO,EAAE,aAAa,IAAI,IAAI;SAChD,CAAC;QAEF,IAAI,CAAC;YACD,yCAAyC;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,OAAO,EAAE;gBACvE,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;aAC5C,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,cAAc,GAAkB;gBAClC,GAAG,MAAM;gBACT,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,EAAE;gBACf,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,SAAS;aACxB,CAAC;YAEF,6CAA6C;YAC7C,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACrF,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC/E,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC7F,CAAC;YAED,kCAAkC;YAClC,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1C,cAAc,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC5E,CAAC;YAED,6BAA6B;YAC7B,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,oBAAoB,CAAC;oBAC5B,SAAS,EAAE,kBAAkB;oBAC7B,KAAK,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;oBACnC,MAAM,EAAE,cAAc;oBACtB,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,QAAQ,EAAE;wBACN,MAAM,EAAE,UAA0B;wBAClC,IAAI,EAAE,YAA0B;wBAChC,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;wBACnE,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;wBACtC,aAAa,EAAE,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,MAAM,CAAC;qBAChE;iBACJ,CAAC,CAAC;YACP,CAAC;YAED,OAAO,cAAc,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,WAAW,GAAkB;gBAC/B,QAAQ,EAAE,CAAC,CAAC;gBACZ,MAAM,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC9D,OAAO;gBACP,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBAChC,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,CAAC;wBACV,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnC,OAAO,EAAE,mBAAmB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;wBACpF,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,UAAU;qBACrB,CAAC;gBACF,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE;oBACR,UAAU,EAAE,GAAG;oBACf,WAAW,EAAE,CAAC,sBAAsB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC;oBAC/E,QAAQ,EAAE,OAAO;iBACpB;aACJ,CAAC;YAEF,wBAAwB;YACxB,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,oBAAoB,CAAC;oBAC5B,SAAS,EAAE,wBAAwB;oBACnC,KAAK,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;oBACnC,MAAM,EAAE,WAAW;oBACnB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE;wBACN,MAAM,EAAE,UAA0B;wBAClC,IAAI,EAAE,YAA0B;wBAChC,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC;wBACtC,UAAU,EAAE,GAAG;qBAClB;iBACJ,CAAC,CAAC;YACP,CAAC;YAED,OAAO,WAAW,CAAC;QACvB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,mCAAmC,CAAC,MAAc;QACxD,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,mCAAmC;QACnC,IAAI,mCAAmC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACnD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACxG,IAAI,KAAK,EAAE,CAAC;gBACR,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,4BAA4B,KAAK,CAAC,CAAC,CAAC,EAAE,EACtC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,iCAAiC;oBAC1C,KAAK,EAAE,WAAW,KAAK,CAAC,CAAC,CAAC,EAAE;oBAC5B,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBACxB,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,IAAI,yCAAyC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACzD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,uBAAuB,EACvB,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;YACF,MAAM,CAAC,OAAO,GAAG;gBACb,OAAO,EAAE,iCAAiC;gBAC1C,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,CAAC,MAAM,CAAC;aACtB,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;QAED,wBAAwB;QACxB,IAAI,uDAAuD,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACvE,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACtG,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,wBAAwB,SAAS,CAAC,CAAC,CAAC,EAAE,EACtC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,6BAA6B;oBACtC,KAAK,EAAE,UAAU,SAAS,CAAC,CAAC,CAAC,EAAE;oBAC/B,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;iBAC5B,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACO,6BAA6B,CAAC,MAAc;QAClD,MAAM,WAAW,GAAwB,EAAE,CAAC;QAC5C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC1B,iBAAiB;YACjB,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,WAAW,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU,CAClC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,EAC9C,IAAI,CAAC,IAAI,EAAE,EACX,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAClC,CAAC,CAAC;YACP,CAAC;YACD,mBAAmB;iBACd,IAAI,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU,CAClC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,EAC9C,IAAI,CAAC,IAAI,EAAE,EACX,MAAM,CAAC,kBAAkB,CAAC,OAAO,CACpC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACO,2BAA2B,CAAC,OAA4B;QAC9D,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM;YACN,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YACxC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YACxC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC5C,QAAQ,EAAE;gBACN,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,MAAM;gBACf,OAAO,EAAE;oBACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;iBAClC;aACJ;SACJ,CAAC,CAAC,CAAC;IACR,CAAC;IAED;;OAEG;IACO,mBAAmB,CAAC,MAAc;QACxC,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC;YACxB,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC1B,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACzB,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC;IAES,KAAK,CAAC,iBAAiB,CAAC,MAAc;QAK5C,6DAA6D;QAC7D,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,QAAQ,GAAG,SAAS,CAAC;QACzB,IAAI,UAAU,GAAG,GAAG,CAAC;QAErB,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3B,QAAQ,GAAG,OAAO,CAAC;YACnB,UAAU,GAAG,GAAG,CAAC;YACjB,WAAW,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC7D,WAAW,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAChC,WAAW,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACrD,WAAW,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YAC3D,UAAU,GAAG,GAAG,CAAC;QACrB,CAAC;QAED,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACjD,CAAC;IAES,0BAA0B,CAAC,MAAc;QAC/C,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,MAAM,SAAS,GAAG,2BAA2B,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAExC,IAAI,OAAO,EAAE,CAAC;YACV,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAClE,CAAC,CAAC;QACP,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,iBAAiB,CAAC,MAAyB;QACjD,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,GAAG,CAAC;QACxD,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,EAAE,CAAC;QACvD,OAAO,EAAE,CAAC;IACd,CAAC;IAES,iBAAiB,CAAC,MAAyB;QACjD,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QAC/D,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QAC/D,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,mBAAmB,CAAC,MAAyB;QACnD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzC,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,CAAC;QACtE,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,GAAG,CAAC;QACnE,OAAO,GAAG,CAAC;IACf,CAAC;IAEM,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;IACpC,CAAC;CACJ;AA55BD,wCA45BC;AAED,sCAAsC;AACtC,4DAA+B;AAC/B,mCAAwD;AAExD,6DAA6D;AAC7D,MAAM,UAAU,GAAG,GAAW,EAAE;IAC5B,OAAO,IAAA,mBAAgB,GAAE,CAAC;AAC9B,CAAC,CAAC;AA4TF;;GAEG;AACH,IAAY,UAmBX;AAnBD,WAAY,UAAU;IAClB,oCAAoC;IACpC,iCAAmB,CAAA;IACnB,0BAA0B;IAC1B,iCAAmB,CAAA;IACnB,qBAAqB;IACrB,yBAAW,CAAA;IACX,2CAA2C;IAC3C,2BAAa,CAAA;IACb,wBAAwB;IACxB,2BAAa,CAAA;IACb,sBAAsB;IACtB,2BAAa,CAAA;IACb,2BAA2B;IAC3B,2BAAa,CAAA;IACb,wBAAwB;IACxB,yCAA2B,CAAA;IAC3B,kBAAkB;IAClB,+BAAiB,CAAA;AACrB,CAAC,EAnBW,UAAU,0BAAV,UAAU,QAmBrB;AAklCD;;;;;;;;;;;;;;;;;;;;GAoBG;AACU,QAAA,gBAAgB,GAAG;IAC5B,gBAAgB,EAAY,iDAAiD;IAC7E,iBAAiB,EAAW,kDAAkD;IAC9E,eAAe,EAAa,0DAA0D;IACtF,8BAA8B,EAAE,iEAAiE;IACjG,sBAAsB,EAAM,qCAAqC;IACjE,gBAAgB,EAAY,6BAA6B;CACnD,CAAC;AAUX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CG;AACH;;;;;;GAMG;AACH;;GAEG;AACH,MAAa,mBAAoB,SAAQ,mCAAkB;IACtC,cAAc,CAAgB;IAC9B,cAAc,CAAgB;IACvC,eAAe,CAAW;IAC1B,WAAW,CAAoC;IAC/C,YAAY,GAAG,KAAK,CAAC;IACrB,QAAQ,GAAG,KAAK,CAAC;IACjB,OAAO,GAAG,KAAK,CAAC;IAChB,UAAU,CAAS;IACnB,IAAI,CAAS;IAErB,gBAAgB;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,YAAY,MAAsB;QAC9B,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC,cAAc,GAAG;YAClB,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,KAAK;YACd,gBAAgB,EAAE,IAAI,CAAC,IAAI;YAC3B,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI,YAAY;YACvC,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,WAAW,EAAE,EAAE;YACf,cAAc,EAAE,EAAE;YAClB,MAAM,EAAE,KAAK;SAChB,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG;YAClB,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,CAAC;YACnB,mBAAmB,EAAE,CAAC;YACtB,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;SACjB,CAAC;IACN,CAAC;IAES,QAAQ;QACd,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5C,IAAI,CAAC,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9F,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC1C,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IAES,QAAQ;QACd,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;QACtF,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IAED,qBAAqB;QACjB,MAAM,OAAO,GAAqB;YAC9B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,KAAK,IAAI,EAAE;gBACnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACzB,CAAC;YACD,aAAa,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE;YACtC,cAAc,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE;YACvC,KAAK,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACzC,OAAO,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAC7C,cAAc,EAAE,CAAC,GAAW,EAAE,IAK7B,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;YACpC,sBAAsB,EAAE,KAAK,EAAE,GAAW,EAAE,IAK3C,EAAE,EAAE;gBACD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,IAAI,OAAO,GAAG,IAAI,CAAC;gBACnB,IAAI,KAAyB,CAAC;gBAE9B,IAAI,CAAC;oBACD,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBAClD,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACT,OAAO,GAAG,KAAK,CAAC;oBAChB,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvD,CAAC;gBAED,OAAO;oBACH,OAAO,EAAE,GAAG;oBACZ,MAAM;oBACN,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAChC,OAAO;oBACP,KAAK;oBACL,UAAU,EAAE,IAAI,EAAE,UAAU,IAAI,KAAK;oBACrC,WAAW,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC;YACN,CAAC;YACD,MAAM,EAAE,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE;gBACnC,IAAI,CAAC,WAAW,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;gBAC3C,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;gBAClC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;YAC7B,CAAC;YACD,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE;YACzB,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE;YACzB,UAAU,EAAE,CAAC,MAAc,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACvD,mBAAmB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YACrD,mBAAmB,EAAE,CAAC,IAAY,EAAE,EAAE;gBAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;YAC7B,CAAC;YACD,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;YAClD,mBAAmB,EAAE,GAAG,EAAE;gBACtB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;YAC9B,CAAC;YACD,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE;SAChC,CAAC;QACF,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,oBAAoB;QAChB,OAAO,IAAI,gCAAe,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAEQ,KAAK,CAAC,cAAc,CACzB,OAAe,EACf,OAKC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACD,IAAI,OAAO,EAAE,gBAAgB,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,gBAAgB,CAAC;YACzC,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,CAAC;YACtD,IAAI,CAAC,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,KAAK,CAAC;gBACnF,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,mBAAmB,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC/D,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YAErB,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,KAAK,CAAC;gBACnF,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,mBAAmB,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC/D,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YAErB,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;CACJ;AA/KD,kDA+KC;AAkBD;;GAEG;AACH,MAAa,mBAAmB;IACpB,MAAM,CAAC,SAAS,CAAsB;IACtC,OAAO,CAAiB;IAEhC;QACI,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG;YACX,KAAK,EAAE,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW;YAC5D,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;YAClB,GAAG,EAAE,OAAO,CAAC,GAAG;SACnB,CAAC;IACN,CAAC;IAEM,MAAM,KAAK,QAAQ;QACtB,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,CAAC;YACjC,mBAAmB,CAAC,SAAS,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC9D,CAAC;QACD,OAAO,mBAAmB,CAAC,SAAS,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,MAAc;QACvC,MAAM,MAAM,GAAmB;YAC3B,GAAG,IAAI,CAAC,OAAO;YACf,IAAI,EAAE,QAAQ,MAAM,EAAE;SACzB,CAAC;QACF,OAAO,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;CACJ;AA9BD,kDA8BC;AAED;;;GAGG;AACH,8DAA8D;AAC9D,SAAgB,wBAAwB,CAAuD,IAAW;IACtG,MAAM,uBAAwB,SAAQ,IAAI;QAC9B,QAAQ,CAAsB;QAC9B,QAAQ,CAAmB;QAC3B,QAAQ,CAAkB;QAElC,8DAA8D;QAC9D,YAAY,GAAG,IAAW;YACtB,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;YACf,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QACzD,CAAC;QAED,IAAI,OAAO;YACP,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QAED,IAAI,aAAa;YACb,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,OAAO;YACP,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QAED,IAAI,MAAM;YACN,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAChC,CAAC;QAED,IAAI,OAAO;YACP,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QAES,KAAK,CAAC,OAAO;YACnB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;KACJ;IAED,OAAO,uBAAuB,CAAC;AACnC,CAAC;AAED,SAAgB,yBAAyB,CACrC,MAA4B,EAC5B,MAIC,EACD,OAKC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE3B,OAAO;QACH,GAAG,MAAM;QACT,QAAQ,EAAE,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS;QAC1C,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,KAAK,EAAE;YACH,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ;YAC/C,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS;YACjC,OAAO;YACP,QAAQ,EAAE,GAAG;SAChB;QACD,QAAQ,EAAE;YACN,GAAG,MAAM,CAAC,QAAQ;YAClB,OAAO,EAAE,MAAM,CAAC,OAAO;SAC1B;QACD,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YACf,GAAG,MAAM,CAAC,OAAO;YACjB,GAAG,OAAO;SACb,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO;KACrB,CAAC;AACN,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,yBAAyB,CACrC,QAAgB,EAChB,SAAiB,EACjB,KAA8B,EAC9B,OAKC;IAED,OAAO;QACH,EAAE,EAAE,UAAU,EAAE;QAChB,QAAQ;QACR,SAAS;QACT,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,KAAK;QACL,QAAQ,EAAE,CAAC;QACX,WAAW,EAAE,UAAU,EAAE;QACzB,KAAK,EAAE;YACH,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB;QACD,QAAQ,EAAE;YACN,MAAM,EAAE,MAAsB;YAC9B,IAAI,EAAE,YAA0B;YAChC,OAAO,EAAE,KAAK;YACd,gBAAgB,EAAE,OAAO,EAAE,gBAAgB;YAC3C,aAAa,EAAE,OAAO,EAAE,aAAa;YACrC,UAAU,EAAE,OAAO,EAAE,UAAU;YAC/B,IAAI,EAAE,OAAO,EAAE,IAAI;SACtB;KACJ,CAAC;AACN,CAAC;AA6ID;;GAEG;AAEH;;;;;;GAMG;AACH,MAAM,uBAAwB,SAAQ,KAAK;IACvC;;;OAGG;IACH,YAAY,CAAC,GAAG,sBAAsB;QAClC,KAAK,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;IAC1C,CAAC;CACJ;AAED;;;;;;GAMG;AACH,MAAM,qBAAsB,SAAQ,KAAK;IACrC;;;OAGG;IACH,YAAY,CAAS;QACjB,KAAK,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;IACxC,CAAC;CACJ;AAED;;;;;;GAMG;AACH,MAAM,mBAAoB,SAAQ,KAAK;IACnC;;;OAGG;IACH,YAAY,CAAS;QACjB,KAAK,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACtC,CAAC;CACJ;AAED;;;;;;;GAOG;AACH,MAAM,aAAc,SAAQ,KAAK;IAC7B;;;OAGG;IACH,YAAY,CAAS;QACjB,KAAK,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAChC,CAAC;CACJ;AAED;;;;;;GAMG;AACH,MAAM,aAAc,SAAQ,KAAK;IAC7B;;;OAGG;IACH,YAAY,CAAS;QACjB,KAAK,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAChC,CAAC;CACJ;AAED;;;;;;GAMG;AACH,MAAM,mBAAoB,SAAQ,KAAK;IACnC;;;OAGG;IACH,YAAY,CAAS;QACjB,KAAK,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACtC,CAAC;CACJ;AACD,aAAa;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiDG;AACH,MAAsB,QAAQ;IAa1B;;;OAGG;IACa,OAAO,GAAG,OAAO,CAAC;IA2BlC;;;;;;;;;;;;OAYG;IACI,KAAK,CAAC,MAAM,CAAC,UAAkB,EAAE,KAAc,EAAE,OAA0B;QAC9E,MAAM,WAAW,GAAG,UAAU,EAAE,CAAC;QACjC,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE3C,IAAI,CAAC;YACD,yBAAyB;YACzB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,MAAM,IAAI,mBAAmB,CAAC,WAAW,UAAU,wBAAwB,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YAC9F,CAAC;YAED,yCAAyC;YACzC,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC5D,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,SAAS,IAAI,CAAC,IAAI,kDAAkD,UAAU,IAAI,CAAC;gBACrG,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;gBAC1G,IAAI,CAAC,aAAa,EAAE,CAAC;oBACjB,MAAM,IAAI,qBAAqB,CAAC,qCAAqC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC3G,CAAC;YACL,CAAC;YAED,0CAA0C;YAC1C,IAAI,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;gBAC5C,MAAM,IAAI,uBAAuB,EAAE,CAAC;YACxC,CAAC;YAED,gCAAgC;YAChC,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1D,oCAAoC;YACpC,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAE7F,iCAAiC;YACjC,MAAM,eAAe,GAAG,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAE7D,2BAA2B;YAC3B,OAAO;gBACH,WAAW;gBACX,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,UAAU;gBACV,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,eAAe;gBACvB,eAAe;aAClB,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,iFAAiF;YACjF,IAAI,eAAsG,CAAC;YAE3G,IAAI,KAAK,YAAY,cAAQ,EAAE,CAAC;gBAC5B,2BAA2B;gBAC3B,eAAe,GAAG;oBACd,OAAO,EAAE,wBAAwB;oBACjC,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE;iBAC3B,CAAC;YACN,CAAC;iBAAM,IAAI,KAAK,YAAY,qBAAqB,EAAE,CAAC;gBAChD,4BAA4B;gBAC5B,eAAe,GAAG;oBACd,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,qBAAqB;iBAC9B,CAAC;YACN,CAAC;iBAAM,IAAI,KAAK,YAAY,uBAAuB,EAAE,CAAC;gBAClD,2DAA2D;gBAC3D,OAAO;oBACH,WAAW;oBACX,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,UAAU;oBACV,MAAM,EAAE,WAAW;oBACnB,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE;iBACzD,CAAC;YACN,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAChC,2BAA2B;gBAC3B,eAAe,GAAG;oBACd,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,IAAI,EAAE,mBAAmB;iBAC5B,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,sBAAsB;gBACtB,eAAe,GAAG;oBACd,OAAO,EAAE,2BAA2B;oBACpC,IAAI,EAAE,WAAW;iBACpB,CAAC;YACN,CAAC;YAED,6CAA6C;YAC7C,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE;gBAC3C,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,UAAU;gBAClB,KAAK,EAAE,eAAe;aACzB,CAAC,CAAC;YAEH,wBAAwB;YACxB,OAAO;gBACH,WAAW;gBACX,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,UAAU;gBACV,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,eAAe;aACzB,CAAC;QACN,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,cAAc,CAAC,UAAkB;QACpC,uDAAuD;QACvD,OAAO,CAAC,GAAG,CAAC,mCAAmC,UAAU,EAAE,CAAC,CAAC;QAC7D,OAAO,EAAE,CAAC;IACd,CAAC;CAUJ;AApMD,4BAoMC;AACD,aAAa;AAEb,0DAA0D;AAE1D;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,gBAAgB;IACzB,+CAA+C;IAC/B,EAAE,CAAS;IAE3B,wCAAwC;IACxB,IAAI,CAAS;IAE7B,kDAAkD;IAClC,WAAW,CAAS;IAEpC,2DAA2D;IAC3C,OAAO,CAA4D;IAEnF,wDAAwD;IACxC,SAAS,CAAW;IAEpC,8CAA8C;IAC9B,aAAa,CAAW;IAExC,sCAAsC;IACrB,QAAQ,CAAS;IAElC,oDAAoD;IACnC,UAAU,CAAS;IAEpC,8EAA8E;IAC7D,KAAK,CAA0B;IAEhD,yDAAyD;IACxC,QAAQ,CAAoB;IAE7C,sDAAsD;IACrC,KAAK,CAAoB;IAE1C;;;;;;;;;OASG;IACH,YACI,EAAU,EACV,QAAgB,EAChB,UAAkB,EAClB,KAA8B,EAC9B,QAA2B,EAC3B,KAAwB;QAExB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,SAAS,QAAQ,IAAI,UAAU,EAAE,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,WAAW,QAAQ,IAAI,UAAU,EAAE,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,gCAAgC;QAChC,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE,OAAwB,EAA+B,EAAE;YAC3E,sCAAsC;YACtC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM,QAAQ,GAAG,SAAS,IAAI,CAAC,QAAQ,cAAc,CAAC;gBACtD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;YAC/C,CAAC;YAED,IAAI,CAAC;gBACD,kDAAkD;gBAClD,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAE7D,0BAA0B;gBAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE;oBAC7D,eAAe,EAAE,OAAO;oBACxB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,iBAAiB,EAAE,IAAI,CAAC,KAAK;iBAChC,CAAC,CAAC;gBAEH,oBAAoB;gBACpB,OAAO;oBACH,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,SAAS;oBACpC,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;iBACzD,CAAC;YACN,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACtB,8BAA8B;gBAC9B,MAAM,QAAQ,GAAG,yBAAyB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;YAC/C,CAAC;QACL,CAAC,CAAC;IACN,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACK,YAAY,CAAC,KAA8B,EAAE,OAAwB;QACzE,MAAM,QAAQ,GAA4B,EAAE,CAAC;QAE7C,+BAA+B;QAC/B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9E,sDAAsD;gBACtD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAClD,MAAM,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,GAAG,IAAI,CAAC;gBACvC,IAAI,aAAsB,CAAC;gBAE3B,mCAAmC;gBACnC,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;oBACtB,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;gBACnC,CAAC;qBAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC9B,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC;gBACpC,CAAC;qBAAM,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;oBAChC,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACJ,oCAAoC;oBACpC,aAAa,GAAI,OAA8C,CAAC,MAAM,CAAC,CAAC;gBAC5E,CAAC;gBAED,2BAA2B;gBAC3B,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,IAAI,aAAa,CAAC,6BAA6B,MAAM,yBAAyB,CAAC,CAAC;gBAC1F,CAAC;gBAED,qCAAqC;gBACrC,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;oBAC9B,aAAa,GAAI,aAAyC,EAAE,CAAC,IAAI,CAAC,CAAC;gBACvE,CAAC;gBAED,4CAA4C;gBAC5C,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,IAAI,aAAa,CAAC,2BAA2B,KAAK,0BAA0B,CAAC,CAAC;gBACxF,CAAC;gBAED,QAAQ,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACJ,6CAA6C;gBAC7C,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAC1B,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;AApKD,4CAoKC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AACH,MAAa,mBAAmB;IAC5B,mEAAmE;IAClD,QAAQ,CAAoB;IAE7C;;;;OAIG;IACH,YAAY,QAA2B;QACnC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsDG;IACI,sBAAsB,CAAC,IAiB7B;QACG,gEAAgE;QAChE,MAAM,KAAK,GAAmB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAChD,IAAI,gBAAgB,CAChB,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,EACb,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC,KAAK,CAC7C,CACJ,CAAC;QAEF,iCAAiC;QACjC,MAAM,kBAAkB,GAAuB;YAC3C,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,wBAAwB,IAAI,CAAC,IAAI,EAAE;YAChD,OAAO,EAAE,OAAO;YAChB,KAAK;YACL,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE;YACzB,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE;SACvC,CAAC;QAEF,OAAO,IAAI,yBAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACI,wBAAwB,CAC3B,QAAgB,EAChB,UAAkB,EAClB,KAA8B,EAC9B,aAAqB,QAAQ,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;QAErD,MAAM,MAAM,GAAG,cAAc,CAAC;QAE9B,qCAAqC;QACrC,MAAM,IAAI,GAAG,IAAI,gBAAgB,CAC7B,MAAM,EACN,QAAQ,EACR,UAAU,EACV,KAAK,EACL,IAAI,CAAC,QAAQ,EACb,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC,KAAK,CAC7C,CAAC;QAEF,qDAAqD;QACrD,MAAM,kBAAkB,GAAuB;YAC3C,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,WAAW,QAAQ,IAAI,UAAU,EAAE;YACzC,WAAW,EAAE,8BAA8B,QAAQ,IAAI,UAAU,EAAE;YACnE,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,CAAC,IAAI,CAAC;YACb,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,MAAM;SACtB,CAAC;QAEF,OAAO,IAAI,yBAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;CACJ;AAhLD,kDAgLC;AAED,MAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;IAClF,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QACpB,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sEAAsE,CAAC;QAC/F,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;QACpE,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC;QAC5E,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,oFAAoF,CAAC;KAC9H,CAAC,CAAC,CAAC,QAAQ,CAAC,iDAAiD,CAAC;CAClE,CAAC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,MAAa,YAAY;IAeQ;IAd7B,yDAAyD;IACxC,GAAG,CAAiB;IAErC,+DAA+D;IAC9C,eAAe,CAAsB;IAEtD,kDAAkD;IACjC,QAAQ,GAAG,IAAI,qBAAY,EAAE,CAAC;IAE/C;;;;OAIG;IACH,YAA6B,QAA2B;QAA3B,aAAQ,GAAR,QAAQ,CAAmB;QACpD,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;QACjD,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,EAAE,CAAC,KAAuD,EAAE,QAAsC;QACrG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACI,KAAK,CAAC,GAAG,CAAC,IAAY,EAAE,OAAuE;QAClG,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;QACzC,MAAM,UAAU,GAAG,UAAU,EAAE,CAAC;QAEhC,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,aAAa,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;YACzE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1D,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YAE/D,+CAA+C;YAC/C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC;gBACtE,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,EAAE;aACW,CAAC,CAAC;YAE1B,sDAAsD;YACtD,MAAM,WAAW,GAAgB;gBAC7B,EAAE,EAAE,UAAU,EAAE;gBAChB,OAAO,EAAE,SAAS,IAAI,sBAAsB,gBAAgB,CAAC,MAAM,EAAE;gBACrE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,QAAQ,EAAE;oBACN,MAAM,EAAE,cAA8B,EAAE,mCAAmC;oBAC3E,IAAI,EAAE,eAA6B;oBACnC,IAAI,EAAE,CAAC,eAAe,EAAE,oBAAoB,CAAC;oBAC7C,IAAI;oBACJ,WAAW,EAAE,gBAAgB,CAAC,MAAM;oBACpC,UAAU;iBACb;aACJ,CAAC;YAEF,mEAAmE;YACnE,IAAI,CAAC;gBACD,mCAAmC;gBACnC,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAA4F,CAAC;gBACjI,MAAM,aAAa,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACnB,wCAAwC;gBACxC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;YACzD,CAAC;YAED,4EAA4E;YAC5E,MAAM,MAAM,GAAG,IAAA,kBAAS,EAAC,SAAS,EAAE,EAAE,CAAyC,CAAC;YAChF,MAAM,sBAAsB,GAAG,MAAM,CAAC,sBAAsB,IAAI,IAAI,CAAC;YAErE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAE3E,2DAA2D;YAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC;gBACzD,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,iBAAiB,IAAI,EAAE;gBAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;aACpB,CAAC,CAAC;YAEH,4DAA4D;YAC5D,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;gBACpE,MAAM,iBAAiB,GAAG,QAAkD,CAAC;gBAC7E,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACnC,CAAC;YAED,yCAAyC;YACzC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,QAAQ,CAAC,sBAAsB,CAAC;oBAC5B,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,IAAI;oBACrB,eAAe,EAAE,KAAK;oBACtB,mBAAmB,EAAE,KAAK;oBAC1B,mBAAmB,EAAE,IAAI;iBAC5B,CAAC,CAAC;YACP,CAAC;YAED,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAEtD,mDAAmD;YACnD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAE/D,uDAAuD;YACvD,MAAM,QAAQ,GAAG,mCAAmF,CAAC;YACrG,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC;YAC9C,QAAQ,CAAC,gBAAgB,EAAE,CAAC,UAAU,CAAC,CAAC;YAExC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE;gBAC9B,UAAU;gBACV,YAAY,EAAE;oBACV,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ;oBACpD,OAAO,EAAE,IAAI,IAAI,EAAE;iBACtB;aACJ,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC;YACzF,OAAO,WAAW,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,aAAa,CACtB,QAAgB,EAChB,UAAkB,EAClB,KAA8B,EAC9B,KAAwB;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QAC5F,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAiE,CAAC;QACrG,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC,CAAC,sCAAsC;QACzE,OAAO,MAAO,QAA+F,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;IACtM,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAAE,aAA2C,EAAE,KAAwB;QAChH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QACrG,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzG,MAAM,WAAW,GAA8B,EAAE,CAAC;QAClD,MAAM,aAAa,GAAG,4HAA4H,IAAI,0CAA0C,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAE,gBAA8B,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAM,CAAyB,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,yBAAyB,QAAQ,6iBAA6iB,CAAC;QACp5B,IAAI,aAAa,EAAE,MAAM,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;YAChE,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7J,CAAC;QACD,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAAE,iBAAiB,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QAClH,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,QAAkB;QAClD,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;QAetC,MAAM,EAAE,GAAG,iCAAkC,CAAC;QAC9C,IAAI,EAAE,CAAC,2BAA2B,EAAE,CAAC;YACjC,IAAI,CAAC;gBACD,kDAAkD;gBAClD,MAAM,cAAc,GAAG,MAAM,EAAE,CAAC,2BAA2B,CACvD,UAAU,EACV,OAAO,EACP;oBACI,uBAAuB,EAAE,KAAK;oBAC9B,mBAAmB,EAAE,IAAI;oBACzB,sBAAsB,EAAE,IAAI;iBAC/B,CACJ,CAAC;gBAEF,IAAI,cAAc,EAAE,CAAC;oBACjB,OAAO;wBACH,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,MAAM,EAAE,cAAc,CAAC,MAAM;wBAC7B,KAAK,EAAE,cAAc,CAAC,KAAK;qBAC9B,CAAC;gBACN,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,0DAA0D;gBAC1D,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,sEAAsE,EAAE,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACzK,CAAC;QACL,CAAC;QAED,6BAA6B;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,+BAA+B;YAC/B,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;gBACjB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;gBAC9B,MAAM,EAAE,SAAS;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC;gBACD,mBAAmB;gBACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAE3C,yBAAyB;gBACzB,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjE,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAClC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;gBAE7B,wCAAwC;gBACxC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAChD,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC7C,CAAC;gBAED,wCAAwC;gBACxC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBAClB,OAAO;wBACH,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,SAAS,IAAI,CAAC,IAAI,UAAU;qBACtD,CAAC;gBACN,CAAC;gBAED,0CAA0C;gBAC1C,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;oBACpB,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,UAAU,CAAC,CAAC;oBAC9E,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;wBACnB,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,8BAA8B;oBACrD,CAAC;gBACL,CAAC;YACL,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACtB,0BAA0B;gBAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjE,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAClC,YAAY,CAAC,MAAM,GAAG;oBAClB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE,CAAC;gBAEF,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE,CAAC;YACN,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,OAAO;YACH,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,OAAO,CAAC,OAAO;SAC1B,CAAC;IACN,CAAC;CACJ;AAjTD,oCAiTC;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAa,uBAAuB;IAChC,2DAA2D;IACnD,OAAO,GAA4B,IAAI,CAAC;IAEhD,oEAAoE;IACnD,WAAW,GAAG,6BAA6B,CAAC;IAE7D;;;;;;;;;;;;;OAaG;IACI,KAAK,CAAC,UAAU,CAAC,OAAyB;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,yCAAyC;IACrE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACI,KAAK,CAAC,kBAAkB,CAAC,WAAsC,EAAE,SAAiB;QACrF,+CAA+C;QAC/C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QAC3D,CAAC;QAED,4DAA4D;QAC5D,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAmB,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;QAE9F,kDAAkD;QAClD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAExD,gEAAgE;QAChE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,4CAA4C;QAC5C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACnD,GAAG,SAAS,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EACrD,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,OAAO,CACV,CAAC;QAEF,wDAAwD;QACxD,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACvB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,8BAA8B;QAC9B,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AA3FD,0DA2FC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,MAAa,kBAAkB;IAC3B,4CAA4C;IACpC,KAAK,GAAG,IAAI,GAAG,EAAiB,CAAC;IAEzC,iEAAiE;IACzD,QAAQ,GAA6B,IAAI,CAAC;IAElD;;;;;;;;;;;;;;;OAeG;IACI,KAAK,CAAC,UAAU,CAAC,QAA2B;QAC/C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,0CAA0C;IACxE,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,OAAO,CAAC,QAAgB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,WAAW;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;CACJ;AAtED,gDAsEC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAsB,iBAAiB;IACnC,2EAA2E;IACxD,MAAM,CAAU;IAEnC;;;;OAIG;IACH,YAAY,QAA2B;QACnC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAClC,CAAC;CA2BJ;AAtCD,8CAsCC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAa,WAAY,SAAQ,iBAAiB;IAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACI,KAAK,CAAC,gBAAgB,CACzB,MAA2B,EAC3B,OAAuE;QAEvE,sDAAsD;QACtD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,MAAM,CAAC,GAAG,CAAS,QAAQ,CAAC,CAAC;QAE1E,iCAAiC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACrD,CAAC;QAED,4CAA4C;QAC5C,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAS,SAAS,EAAE,2BAA2B,CAAC,CAAC;QAE3E,0CAA0C;QAC1C,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;QAE5D,yCAAyC;QACzC,IAAI,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;YACpD,MAAM,IAAI,uBAAuB,EAAE,CAAC;QACxC,CAAC;QAED,IAAI,CAAC;YACD,qDAAqD;YACrD,MAAM,QAAQ,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACjE,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACxB,OAAO,EAAE,IAAI,EAAE,MAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;gBACjD,CAAC;qBAAM,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;oBACrB,OAAO,EAAE,IAAI,EAAE,MAAe,EAAE,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBACtD,CAAC;qBAAM,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;oBACtB,yEAAyE;oBACzE,OAAO,EAAE,IAAI,EAAE,MAAe,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;gBAC9D,CAAC;gBACD,OAAO,EAAE,IAAI,EAAE,MAAe,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,0CAA0C;YAC1C,MAAM,WAAW,GAAG;gBAChB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAS,OAAO,EAAE,aAAa,CAAC;gBACjD,QAAQ;gBACR,WAAW,EAAE,GAAG;gBAChB,6CAA6C;gBAC7C,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,eAAe,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,CAAC;aAC1E,CAAC;YAEF,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,OAAO,mBAAmB,EAAE;gBACxD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACL,cAAc,EAAE,kBAAkB;oBAClC,eAAe,EAAE,UAAU,MAAM,EAAE;iBACtC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBACjC,MAAM,EAAE,UAAU,CAAC,MAAM;aAC5B,CAAC,CAAC;YAEH,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACf,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,aAAa,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC,CAAC;YAClE,CAAC;YAOD,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAoB,CAAC;YAErD,0CAA0C;YAC1C,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;QAEnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,0BAA0B;YAC1B,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;YAEvE,yDAAyD;YACzD,MAAM,KAAK,CAAC;QAEhB,CAAC;gBAAS,CAAC;YACP,yCAAyC;YACzC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;CACJ;AArHD,kCAqHC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,MAAa,WAAY,SAAQ,iBAAiB;IAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACI,KAAK,CAAC,gBAAgB,CACzB,MAA2B,EAC3B,OAAuE;QAEvE,sDAAsD;QACtD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QACnE,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAS,OAAO,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAS,SAAS,EAAE,wBAAwB,CAAC,CAAC;QAExE,2DAA2D;QAC3D,IAAI,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;YACpD,MAAM,IAAI,uBAAuB,EAAE,CAAC;QACxC,CAAC;QAED,+BAA+B;QAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACpD,CAAC;QAED,gEAAgE;QAChE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9B,OAAO;YACP,KAAK;YACL,YAAY,EAAE,MAAM,CAAC,MAAM;YAC3B,UAAU,EAAE,OAAO,CAAC,UAAU;SACjC,CAAC,CAAC;QAEH,+DAA+D;QAC/D,IAAI,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;YACpD,MAAM,IAAI,uBAAuB,EAAE,CAAC;QACxC,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iGAAiG,CAAC,CAAC;QAEpH,yEAAyE;QACzE,MAAM,IAAI,mBAAmB,CAAC,gCAAgC,CAAC,CAAC;IACpE,CAAC;CACJ;AA3ED,kCA2EC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AACH,MAAa,aAAc,SAAQ,iBAAiB;IAChD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsCG;IACI,KAAK,CAAC,gBAAgB,CACzB,MAA2B,EAC3B,OAAuE;QAEvE,yDAAyD;QACzD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QACrE,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAS,SAAS,EAAE,0BAA0B,CAAC,CAAC;QAE1E,gEAAgE;QAChE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACjC,OAAO;YACP,YAAY,EAAE,MAAM,CAAC,MAAM;YAC3B,UAAU,EAAE,OAAO,CAAC,UAAU;SACjC,CAAC,CAAC;QAEH,2CAA2C;QAC3C,IAAI,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;YACpD,MAAM,IAAI,uBAAuB,EAAE,CAAC;QACxC,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oGAAoG,CAAC,CAAC;QAEvH,yEAAyE;QACzE,MAAM,IAAI,mBAAmB,CAAC,mCAAmC,CAAC,CAAC;IACvE,CAAC;CACJ;AAlED,sCAkEC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAa,qBAAqB;IAC9B,mDAAmD;IAC3C,WAAW,CAAkB;IAErC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACI,UAAU,CAAC,QAA2B;QACzC,oDAAoD;QACpD,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAS,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEpG,oEAAoE;QACpE,QAAQ,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;YACjC,KAAK,QAAQ;gBACT,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAC7C,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAC7C,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,CAAC,WAAW,GAAG,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAC/C,MAAM;YACV;gBACI,MAAM,IAAI,KAAK,CAAC,0BAA0B,YAAY,uDAAuD,CAAC,CAAC;QACvH,CAAC;QAED,yDAAyD;QACzD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACI,cAAc;QACjB,kEAAkE;QAClE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;CACJ;AArFD,sDAqFC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,MAAa,gBAAgB;IACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACI,KAAK,CAAC,YAAY,CACrB,KAAa,EACb,IAAuG;QAEvG,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAC7B;YACI,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;YAC9C,KAAK;YACL,WAAW,EAAE,IAAI;SACpB,EACD,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAC7C,CAAC;IACN,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,sBAAsB,CAAC,OAAe;QACzC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,kBAAkB,CAAC,OAAe;QACrC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,gBAAgB,CAAC,OAAuB;QAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACzF,CAAC;CACJ;AAvGD,4CAuGC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,MAAa,yBAAyB;IAClC,kDAAkD;IAC1C,OAAO,CAAgB;IAE/B;;;;;;;;;;;;;OAaG;IACH,YAAY,IAAY;QACpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACI,KAAK,CAAC,OAAe,EAAE,QAAiB;QAC3C,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,IAAI,CAAC,OAAe,EAAE,QAAiB;QAC1C,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,IAAI,CAAC,OAAe,EAAE,QAAiB;QAC1C,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACI,KAAK,CAAC,OAAuB,EAAE,QAAiB;QACnD,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAClE,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACrF,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACK,GAAG,CAAC,KAAa,EAAE,OAAe,EAAE,QAAiB;QACzD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,KAAK,KAAK,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxG,CAAC;CACJ;AArID,8DAqIC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuCG;AACH,MAAa,uBAAuB;IAChC;;;;;;;;OAQG;IACH,KAAK,CAAC,eAAe,CACjB,YAAoB,EACpB,UAA2B,EAC3B,UAA2B,EAC3B,UAAkB,EAClB,OAAqB;QAErB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAC/B,UAAU,EACV,UAAU,EACV,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,OAAO,EAAE,CAC7C,CAAC;QAEF,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc,CAChB,YAAoB,EACpB,SAAiB,EACjB,OAAyB;QAEzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAED,wDAAwD;IACvC,aAAa,CAAS;IAEvC;;;;;;;;;;;;;;;;;OAiBG;IACH;QACI,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;IACzC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACK,sBAAsB,CAAC,YAAoB;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;QAEpE,iEAAiE;QACjE,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,KAAK,CAAC,QAAQ,CAAC,YAAoB;QACtC,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1C,IAAI,CAAC;YACD,mEAAmE;YACnE,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAChD,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,CACzC,CAAC;YAEF,IAAI,QAAQ,EAAE,CAAC;gBACX,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC;YAED,0CAA0C;YAC1C,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAClE,OAAO,YAAY,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,mEAAmE;YACnE,OAAO,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,KAAK,CAAC,SAAS,CAAC,YAAoB,EAAE,OAAwB;QACjE,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1C,gDAAgD;QAChD,MAAM,aAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEhE,qCAAqC;QACrC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAElF,wCAAwC;QACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAChD,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,CACzC,CAAC;QAEF,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACjC,uDAAuD;YACvD,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACxC,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAC9B,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EACtB,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CACjD,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YACzC,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAEvC,kCAAkC;YAClC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACnB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,wCAAwC;YACxC,MAAM,aAAE,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAE1C,0DAA0D;YAC1D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAChD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,CAC9C,CAAC;YACF,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC/F,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACI,KAAK,CAAC,UAAU,CAAC,YAAoB;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1C,8CAA8C;QAC9C,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAChD,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,CACzC,CAAC;QAEF,IAAI,GAAG,EAAE,CAAC;YACN,iCAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,gCAAgC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,sCAAsC;QACtC,IAAI,QAAQ,EAAE,CAAC;YACX,sCAAsC;YACtC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CACnD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,CACxD,CAAC;YAEF,0CAA0C;YAC1C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC3B,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC1E,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;YAC/E,CAAC;QACL,CAAC;QAED,sBAAsB;QACtB,MAAM,aAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAE9B,2CAA2C;QAC3C,2FAA2F;QAC3F,2EAA2E;IAC/E,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACI,KAAK,CAAC,eAAe,CAAC,YAAoB;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,aAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACtD,CAAC;IAEM,KAAK,CAAC,YAAY,CACrB,YAAoB,EACpB,OAAwB,EACxB,UAA6E,EAAE;QAE/E,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAElF,+BAA+B;QAC/B,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,UAAU,GAAG,GAAG,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,MAAM,EAAE,CAAC;YACtE,MAAM,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAChD,CAAC;QAED,wCAAwC;QACxC,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAC9C,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,CACzC,CAAC;QAEF,IAAI,cAAc,GAAG,EAAE,CAAC;QAExB,IAAI,QAAQ,EAAE,CAAC;YACX,iCAAiC;YACjC,cAAc,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QACxC,CAAC;aAAM,CAAC;YACJ,oCAAoC;YACpC,IAAI,CAAC;gBACD,cAAc,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAC9D,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACtB,MAAM,SAAS,GAAG,KAA8B,CAAC;gBACjD,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;oBAC5D,MAAM,KAAK,CAAC;gBAChB,CAAC;YACL,CAAC;YAED,0CAA0C;YAC1C,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,MAAM,WAAW,GAA4B;oBACzC,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,OAAO,EAAE,OAAO,CAAC,OAAO;iBAC3B,CAAC;gBAEF,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBACxD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAE3E,oDAAoD;gBACpD,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;gBACxC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpF,MAAM,CAAC,WAAW,CACd,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,EAChC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CACtC,CAAC;YACN,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC;QAC5C,MAAM,UAAU,GAAG,cAAc;YAC7B,CAAC,CAAC,cAAc,GAAG,SAAS,GAAG,UAAU;YACzC,CAAC,CAAC,UAAU,CAAC;QAEjB,gEAAgE;QAChE,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAE/C,kEAAkE;QAClE,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAChD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,CAC9C,CAAC;YAEF,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;gBAC/C,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAE3F,sCAAsC;gBACtC,MAAM,CAAC,WAAW,CACd,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,EAChC,MAAM,CAAC,oBAAoB,CAAC,yBAAyB,CACxD,CAAC;gBAEF,iDAAiD;gBACjD,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,UAAU,CACnB,YAAoB,EACpB,aAA8B,EAC9B,UAA6B,EAAE;QAE/B,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QACzD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,OAAO,GAAmB,EAAE,CAAC;QACnC,MAAM,KAAK,GAAG,aAAa,YAAY,MAAM;YACzC,CAAC,CAAC,aAAa;YACf,CAAC,CAAC,IAAI,MAAM,CACR,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,EACvF,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CACrC,CAAC;QAEN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,KAAK,CAAC;YACV,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBACzC,OAAO,CAAC,IAAI,CAAC;oBACT,UAAU,EAAE,CAAC,GAAG,CAAC;oBACjB,WAAW,EAAE,IAAI;oBACjB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACf,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;iBAC7D,CAAC,CAAC;gBAEH,iDAAiD;gBACjD,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,EAAE,CAAC;oBAClC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,aAAa,CACtB,YAAoB,EACpB,aAA8B,EAC9B,WAAsE,EACtE,UAA+C,EAAE;QAEjD,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAEzD,+BAA+B;QAC/B,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,UAAU,GAAG,GAAG,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,MAAM,EAAE,CAAC;YACtE,MAAM,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,KAAK,GAAG,aAAa,YAAY,MAAM;YACzC,CAAC,CAAC,aAAa;YACf,CAAC,CAAC,IAAI,MAAM,CACR,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,EACvF,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CACrC,CAAC;QAEN,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE;YAClD,YAAY,EAAE,CAAC;YACf,OAAO,OAAO,WAAW,KAAK,UAAU;gBACpC,CAAC,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;gBACtB,CAAC,CAAC,WAAW,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,YAAY,CACrB,YAAoB,EACpB,UAAkB,EAClB,OAAe,EACf,UAA2B,EAAE;QAE7B,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,IAAI,CAAC;YACD,WAAW,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACtB,MAAM,SAAS,GAAG,KAA8B,CAAC;YACjD,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5D,MAAM,KAAK,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACrE,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;QAEnC,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzD,CAAC;IAEM,KAAK,CAAC,eAAe,CACxB,YAAoB,EACpB,SAA0E,EAC1E,UAA2B,EAAE;QAE7B,IAAI,OAAO,EAAE,CAAC;YACV,iCAAe,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,qCAAqC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACnG,CAAC;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QACzD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAEtC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACvB,6BAA6B;gBAC7B,aAAa,EAAE,CAAC;YACpB,CAAC;iBAAM,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACzB,mBAAmB;gBACnB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACtB,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;gBACpB,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,sBAAsB;gBACtB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;QACL,CAAC;QAED,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,WAAW,CACpB,YAAoB,EACpB,UAA2B,EAC3B,UAAuB,EAAE;QAEzB,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,iCAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,+BAA+B,YAAY,EAAE,CAAC,CAAC;QAC/E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAErD,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,WAAW,CACpB,UAA2B,EAC3B,UAA2B,EAC3B,UAAuB,EAAE;QAEzB,qCAAqC;QACrC,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAC9F,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAE9F,iEAAiE;QACjE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClD,IAAI,CAAC;gBACD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,iBAAiB,CAAC;gBACjD,MAAM,MAAM,GAAG,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;gBAE5C,mDAAmD;gBACnD,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC;oBACrE,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,GAAG,KAAK,aAAa;iBAC9B,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC;oBACtE,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,GAAG,KAAK,aAAa;iBAC9B,CAAC,CAAC;gBAEH,kCAAkC;gBAClC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAChC,aAAa,EACb,OAAO,EACP,QAAQ,EACR,KAAK,EACL;oBACI,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM;oBAC1D,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,eAAe,EAAE,IAAI;iBACxB,EACD;oBACI,UAAU,EAAE,IAAI;oBAChB,KAAK,EAAE,KAAK;oBACZ,WAAW,EAAE,qBAAqB;oBAClC,MAAM,EAAE,0CAA0C;iBACrD,CACJ,CAAC;gBAEF,yDAAyD;gBACzD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBACpD,IAAI,YAAY,EAAE,CAAC;oBACf,wDAAwD;oBACxD,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;oBAExC,6BAA6B;oBAC7B,MAAM,SAAS,GAAG,CAAC,GAAwB,EAAE,EAAE,CAC3C,IAAI,MAAM,CAAC,KAAK,CACZ,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EACjB,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CACvC,CAAC;oBAEN,iDAAiD;oBACjD,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;wBAC1C,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC;wBAC1C,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC;qBAC9C,CAAC,CAAC;oBAEH,0CAA0C;oBAC1C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC,EAAE,aAAa,CAAC,CAAC;oBACzD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,EAAE,aAAa,CAAC,CAAC;oBAE3D,kBAAkB;oBAClB,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAEvC,qDAAqD;oBACrD,MAAM,OAAO,CAAC,GAAG,CAAC;wBACd,OAAO,CAAC,IAAI,EAAE;wBACd,QAAQ,CAAC,IAAI,EAAE;qBAClB,CAAC,CAAC;oBAEH,iCAAiC;oBACjC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;wBACrB,MAAM,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;wBAC5E,MAAM,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACjF,CAAC;gBACL,CAAC;gBAED,iEAAiE;gBACjE,OAAO,EAAE,CAAC;YACd,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;gBACtD,2CAA2C;YAC/C,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,MAAM,UAAU,GAAG,wDAAa,MAAM,GAAC,CAAC;QAExC,yCAAyC;QACzC,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,CAC7C,UAAU,EAAE,oBAAoB;QAChC,UAAU,EAAE,oBAAoB;QAChC,aAAa,EACb,aAAa,CAChB,CAAC;QAEF,uDAAuD;QACvD,QAAQ,OAAO,CAAC,MAAM,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YAC3C,KAAK,UAAU,CAAC,IAAI;gBAChB,OAAO,IAAI,CAAC,SAAS,CAAC;oBAClB,UAAU,EAAE,aAAa;oBACzB,UAAU,EAAE,aAAa;oBACzB,KAAK,EAAE,UAAU;iBACpB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAChB,KAAK,UAAU,CAAC,OAAO,CAAC;YACxB;gBACI,OAAO,UAAU,CAAC;QAC1B,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,SAAS,CAClB,YAAoB,EACpB,IAAY,EACZ,UAAmD,EAAE;QAErD,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,2BAA2B;QAC3B,IAAI,cAAsB,CAAC;QAC3B,IAAI,CAAC;YACD,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,uBAAuB,YAAY,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACtH,CAAC;QAED,qBAAqB;QACrB,MAAM,SAAS,GAAG,wDAAa,MAAM,GAAC,CAAC;QAEvC,wCAAwC;QACxC,IAAI,OAAuB,CAAC;QAC5B,IAAI,CAAC;YACD,iCAAiC;YACjC,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YAErD,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAClE,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,SAAS,CAAC,IAAI,CAAC,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAClG,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;QACzC,CAAC;QAED,yDAAyD;QACzD,IAAI,OAAO,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACnC,IAAI,CAAC;gBACD,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAClD,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;gBAEhF,0DAA0D;gBAC1D,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CACpD,WAAW,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAC3C,CAAC;gBAEF,oCAAoC;gBACpC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACxC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAC7D,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAEvC,8CAA8C;gBAC9C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAI,CACxE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,EACrB,GAAG,EAAE,CAAC,SAAS,CAClB,CAAC;gBAEF,IAAI,UAAU,EAAE,CAAC;oBACb,MAAM,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACzE,CAAC;gBAED,gBAAgB;gBAChB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAChC,aAAa,EACb,WAAW,EACX,WAAW,EACX,oBAAoB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EACjD,EAAE,OAAO,EAAE,KAAK,EAAE,CACrB,CAAC;gBAEF,uCAAuC;gBACvC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACtD,qCAAqC,EACrC;oBACI,KAAK,EAAE,IAAI;oBACX,MAAM,EAAE,oBAAoB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG;iBAC7D,EACD,eAAe,EACf,QAAQ,CACX,CAAC;gBAEF,IAAI,OAAO,KAAK,eAAe,EAAE,CAAC;oBAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,8BAA8B,CAAC,EAAE,CAAC;gBAC3E,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,wDAAwD;gBACxD,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC;YACD,0CAA0C;YAC1C,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAE5C,2DAA2D;YAC3D,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC1C,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;gBAE9D,+CAA+C;gBAC/C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBAClB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,wBAAwB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EACrD,WAAW,CACd,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;wBACZ,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;4BACzB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;wBACxC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,YAAY,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACvH,CAAC;IACL,CAAC;CACJ;AA/xBD,0DA+xBC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,MAAa,wBAAwB;IACjC,uEAAuE;IACtD,aAAa,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC;IAE9G,qCAAqC;IACpB,aAAa,CAAS;IAEvC;QACI,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;IACzC,CAAC;IAED;;;;;;;;OAQG;IACK,uBAAuB,CAAC,YAAoB;QAChD,iDAAiD;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEpD,2BAA2B;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QAEtE,0CAA0C;QAC1C,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,SAAS,YAAY,sCAAsC,CAAC,CAAC;QACjF,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,SAAS,CAAC,YAAoB,EAAE,OAAwB;QACjE,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;QAEhE,8BAA8B;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,aAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEzC,iBAAiB;QACjB,MAAM,aAAE,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,QAAQ,CAAC,YAAoB;QACtC,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACI,KAAK,CAAC,gBAAgB,CAAC,OAAuC;QACjE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,+DAA+D;QAC/D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,OAAO,EAAE,cAAc,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAEvF;;;;;;;;;;;WAWG;QACH,MAAM,SAAS,GAAG,KAAK,EAAE,OAAe,EAA0B,EAAE;YAChE,oDAAoD;YACpD,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAqE,EAAE,CAAC;YAEtF,sCAAsC;YACtC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC1B,uBAAuB;gBACvB,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,SAAS;gBACb,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAE/D,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;oBACtB,4CAA4C;oBAC5C,QAAQ,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACJ,sCAAsC;oBACtC,QAAQ,CAAC,IAAI,CAAC;wBACV,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,IAAI,EAAE,MAAM;qBACf,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YAED,iCAAiC;YACjC,OAAO;gBACH,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;gBACnD,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC5B,QAAQ;gBACR,IAAI,EAAE,WAAW;aACpB,CAAC;QACN,CAAC,CAAC;QAEF,kDAAkD;QAClD,OAAO,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACI,KAAK,CAAC,kBAAkB,CAAC,WAAmB;QAC/C,OAAO,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,KAAK,CAAC,cAAc,CAAC,YAAoB;QAC5C,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH;;;;;;;;;;;;;;OAcG;IACI,KAAK,CAAC,YAAY,CACrB,YAAoB,EACpB,UAOI,EAAE;QAEN,qCAAqC;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;QAChE,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1C,IAAI,CAAC;YACD,iDAAiD;YACjD,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACxD,CAAC;YAED,uDAAuD;YACvD,MAAM,QAAQ,GAAiB,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAE5E,iEAAiE;YACjE,MAAM,WAAW,GAA4B;gBACzC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM;gBAC1D,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,KAAK;gBAC7C,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI;aACnC,CAAC;YAEF,+DAA+D;YAC/D,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,WAAW,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;YAC9C,CAAC;YAED,kEAAkE;YAClE,MAAM,MAAM,GAAe,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAEvF,sFAAsF;YACtF,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC1C,sEAAsE;gBACtE,qBAAqB,CAAC,GAAG,EAAE;oBACvB,0BAA0B;oBAC1B,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;wBAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC;wBACzC,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;wBAEpD,2DAA2D;wBAC3D,MAAM,CAAC,WAAW,CACd,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,UAAU,CACrB,CAAC;oBACN,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;iBAAM,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC3B,wDAAwD;gBACxD,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC;gBACzC,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAEpD,sEAAsE;gBACtE,MAAM,CAAC,WAAW,CACd,OAAO,CAAC,SAAS,EACjB,MAAM,CAAC,oBAAoB,CAAC,OAAO,CACtC,CAAC;YACN,CAAC;iBAAM,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC5B,gEAAgE;gBAChE,MAAM,CAAC,WAAW,CACd,MAAM,CAAC,SAAS,EAChB,OAAO,CAAC,UAAU,CACrB,CAAC;YACN,CAAC;YAED,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,kCAAkC,YAAY,EAAE,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;CACJ;AA/VD,4DA+VC;AAED;;;;;;;;;;GAUG;AACH,MAAa,eAAe;IACP,MAAM,CAAU;IACzB,OAAO,GAA8B,IAAI,CAAC;IACjC,aAAa,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IAClD,aAAa,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IAClD,cAAc,GAAG,IAAI,MAAM,CAAC,YAAY,EAAS,CAAC;IAClD,cAAc,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;IACjD,eAAe,GAAG,IAAI,MAAM,CAAC,YAAY,EAAqC,CAAC;IAC/E,gBAAgB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAU,CAAC;IACrD,oBAAoB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAiB,CAAC;IAChE,KAAK,CAAS;IAE/B,YAAY,MAAe;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC;IACjG,CAAC;IAEO,UAAU,CAAC,GAAY;QAC3B,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,GAAY;QACpC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,GAAG,IAAI,mCAAkB,CAAC;gBAClC,GAAG,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;gBACzB,KAAK,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,MAAM,CAAC;gBACtF,UAAU,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;gBACtC,oBAAoB,EAAE,IAAI;gBAC1B,iBAAiB,EAAE,IAAI;gBACvB,kBAAkB,EAAE,IAAI;gBACxB,wBAAwB,EAAE,IAAI;gBAC9B,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,oCAAoC;gBAChD,KAAK,EAAE,QAAQ;gBACf,YAAY,EAAE,KAAK;gBACnB,WAAW,EAAE,KAAK;gBAClB,oBAAoB,EAAE,IAAI;gBAC1B,oBAAoB,EAAE,IAAI;gBAC1B,kBAAkB,EAAE,IAAI;aAC3B,CAAC,CAAC;YAEH,0BAA0B;YAC1B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACrE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE;gBACjC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,4BAA4B;YACrD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAY,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YAC3E,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAE3E,yBAAyB;YACzB,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAClE,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACtB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,YAAY,EAAE,CAAC,CAAC;gBAC5E,MAAM,KAAK,CAAC;YAChB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,OAAO,CACT,OAAe,EACf,IAAc,EACd,UAMI,EAAE;QAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAClF,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,aAAa,EAAE;gBAC/D,gBAAgB,EAAE,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,gBAAgB;gBACzD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK,EAAE,4BAA4B;gBAC/D,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,KAAK;aAC1C,CAAC,CAAC;YAEH,OAAO;gBACH,OAAO,EAAE,aAAa;gBACtB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,WAAW,EAAE,IAAI,IAAI,EAAE;aAC1B,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,YAAY,EAAE,CAAC,CAAC;YAChE,MAAM,IAAI,aAAa,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,aAAa,CACf,OAAe,EACf,UAKI,EAAE;QAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACtD,IAAI,CAAC;YACD,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE;gBACzC,gBAAgB,EAAE,OAAO,CAAC,GAAG;gBAC7B,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,UAAU,EAAE,OAAO,CAAC,UAAU;aACjC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;YACvE,MAAM,IAAI,aAAa,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAGD,KAAK,CAAC,aAAa,CAAC,UAA4B,EAAE;QAC9C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEtD,uDAAuD;QACvD,MAAM,OAAO,GAAwB;YACjC,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK;YAChC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK;YAChC,KAAK,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YAC5C,MAAM,EAAE,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;YAClE,OAAO,EAAE,GAAG,EAAE;gBACV,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACxB,CAAC;SACJ,CAAC;QAEF,8CAA8C;QAC9C,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;YACnB,OAAO,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;YAChD,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE;YAC5B,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE;YAC5B,UAAU,EAAE,CAAC,MAAc,EAAE,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;YAC1D,mBAAmB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,mBAAmB,EAAE;YACxD,mBAAmB,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACxE,iBAAiB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,iBAAiB,EAAE;YACpD,mBAAmB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,mBAAmB,EAAE;YACxD,aAAa,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC;YACnE,cAAc,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,EAAE;YAC9C,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE;gBACJ,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK;gBAChC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK;gBAChC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK;gBAClC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK;gBACpC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK;gBACtC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK;gBAClC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK;aACjD;SACJ,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG;IACH,OAAO;QACH,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED,yBAAyB;IACzB,qBAAqB;IACrB,yBAAyB;IAEzB;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,mBAAmB,EAAE,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,mBAAmB,CAAC,IAAY;QAClC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO,EAAE,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,OAAgB;QAC1B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACJ,wDAAwD;YACxD,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAChC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc;QACV,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK;QACP,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,MAAc;QAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,QAAQ;QACJ,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,QAAQ;QACJ,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO,CAAC,IAAY;QACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK,CAAC,IAAY;QACpB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACP,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED,yBAAyB;IACzB,qBAAqB;IACrB,yBAAyB;IAEzB;;OAEG;IACH,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,MAAM,CACR,OAAe,EACf,IAAc,EACd,OAAuG;QAEvG,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEnD,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,EAAE;YACjD,MAAM,WAAW,GAAwB,EAAE,CAAC;YAE5C,sBAAsB;YACtB,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,MAAM,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;oBAC5E,OAAO,CAAC,OAAO,EAAE,CAAC;oBAClB,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC9B,CAAC,CAAC,CAAC;gBACH,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACvC,CAAC;YAED,sBAAsB;YACtB,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,QAAgB,EAAE,EAAE;gBACvD,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;gBACtC,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;YACH,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAEjC,0CAA0C;YAC1C,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE;gBACnD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;YACH,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAEjC,oBAAoB;YACpB,OAAO,CAAC,UAAU,EAAE;iBACf,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;iBAC1C,KAAK,CAAC,KAAK,CAAC,EAAE;gBACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;gBAC9E,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAxbD,0CAwbC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CG;AACH,MAAa,sBAAsB;IAC/B,mEAAmE;IAC3D,gBAAgB,CAAqB;IAE7C;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,YAAY,MAAgB;QACxB,kDAAO,oBAAoB,IAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YACxC,IAAI,CAAC,gBAAgB,GAAG,IAAI,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACI,KAAK,CAAC,QAAQ;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgDG;IACI,KAAK,CAAC,SAAS;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACI,KAAK,CAAC,SAAS,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAhKD,wDAgKC", "sourcesContent": ["// Import core Node.js modules\nimport * as os from 'os';\nimport * as path from 'path';\nimport { promises as fs } from 'fs';\nimport { EventEmitter } from 'events';\n\n// Import VSCode API with a namespace to avoid conflicts\nimport * as vscode from 'vscode';\n\n// Re-export commonly used VSCode types for internal use\ntype CancellationToken = vscode.CancellationToken;\ntype ExtensionContext = vscode.ExtensionContext;\ntype Progress<T> = vscode.Progress<T>;\ntype OutputChannel = vscode.OutputChannel;\ntype Event<T> = vscode.Event<T>;\ntype Uri = vscode.Uri;\ntype TextEditor = vscode.TextEditor;\ntype TextDocument = vscode.TextDocument;\ntype ViewColumn = vscode.ViewColumn;\ntype Range = vscode.Range;\ntype TextDocumentShowOptions = vscode.TextDocumentShowOptions;\ntype TextEditorRevealType = vscode.TextEditorRevealType;\n\n// Import core terminal functionality\nimport {\n    InteractiveSession,\n    ITerminalProcess,\n    TerminalActions,\n    TerminalConfig,\n    CommandResult,\n    TerminalState,\n    TerminalStats,\n    TerminalActionProvider,\n    IAIContext,\n    IAIToolPattern,\n    IToolTerminalContext,\n    ITerminalToolEvents,\n    QuickFixPattern\n} from './pseudoTerminal';\nimport { getConfig } from '../config';\nimport type { IMemoryOperations, MemoryEntry, MemorySearchOptions, MemorySource, MemoryType } from '../memory/types';\n\n// Import comprehensive memory system\nimport { MemoryManager } from '../memory/memoryManager';\nimport { CodessaMemoryProvider } from '../memory/codessa/codessaMemory';\nimport { CodessaGraphMemory } from '../memory/codessa/codessaGraphMemory';\nimport { FileChunkingService } from '../memory/codessa/fileChunking';\n\n// Import workflow types and implementations\nimport { Workflow, WorkflowContext, WorkflowStepResult, WorkflowStep, WorkflowDefinition } from '../agents/workflows/workflowEngine';\nimport { workflowManager } from '../agents/workflows/workflowManager';\nimport { workflowRegistry } from '../agents/workflows/workflowRegistry';\n\n// Import Zod for schema validation\nimport { z, ZodError } from 'zod';\n\n/**\n * Enhanced interfaces for comprehensive tool integration\n */\n\n// Enhanced command result interface\nexport interface CommandResult {\n    exitCode: number;\n    output: string;\n    command: string;\n    duration: number;\n    success: boolean;\n    completedAt: Date;\n\n    // Enhanced result information\n    codeActions?: vscode.CodeAction[];\n    diagnostics?: vscode.Diagnostic[];\n    quickFixes?: CodeActionResult[];\n    aiAnalysis?: {\n        confidence: number;\n        suggestions: string[];\n        category: string;\n    };\n}\n\n// Code action result with metadata\nexport interface CodeActionResult {\n    action: vscode.CodeAction;\n    priority: number;\n    category: string;\n    confidence: number;\n    metadata: {\n        source: string;\n        trigger: string;\n        context: Record<string, unknown>;\n    };\n}\n\n// Tool operation memory interface\nexport interface IToolOperationMemory {\n    id: string;\n    toolName: string;\n    operation: string;\n    input: Record<string, unknown>;\n    output?: unknown;\n    metadata: {\n        source: MemorySource;\n        type: MemoryType;\n        tags: string[];\n        importance: number;\n        workspaceContext?: string;\n        affectedFiles?: string[];\n        success?: boolean;\n        [key: string]: unknown;\n    };\n    state: {\n        status: 'pending' | 'running' | 'completed' | 'failed';\n        startTime: number;\n        endTime?: number;\n    };\n    duration?: number;\n}\n\n/**\n * Enhanced base class for all AI-enhanced tools with comprehensive memory and workflow integration.\n * This class provides a unified interface for terminal operations, code actions, memory management,\n * workflow execution, and AI-powered features.\n */\nexport abstract class AITerminalTool extends TerminalActionProvider {\n    private readonly _terminalSession: InteractiveSession;\n    private readonly _aiContext: IAIContext;\n    private readonly _toolContextMap: Map<string, IToolTerminalContext> = new Map();\n    private readonly _onProcessOutput = new vscode.EventEmitter<string>();\n    private readonly _onAIAnalysis = new vscode.EventEmitter<{\n        action: vscode.CodeAction;\n        context: unknown;\n        aiResponse?: unknown;\n    }>();\n\n    // Enhanced memory and workflow integration\n    private readonly _memoryManager: MemoryManager;\n    private readonly _workflowManager: typeof workflowManager;\n    private readonly _workflowRegistry: typeof workflowRegistry;\n    private readonly _operationMemory: Map<string, IToolOperationMemory> = new Map();\n    private readonly _activeWorkflows: Map<string, Workflow> = new Map();\n\n    // Event emitters for memory and workflow operations\n    private readonly _onMemoryOperation = new vscode.EventEmitter<{\n        operation: string;\n        memory: MemoryEntry;\n        success: boolean;\n    }>();\n    private readonly _onWorkflowExecution = new vscode.EventEmitter<{\n        workflowId: string;\n        step: string;\n        result: WorkflowStepResult;\n    }>();\n\n    public readonly onAIAnalysis: vscode.Event<{\n        action: vscode.CodeAction;\n        context: unknown;\n        aiResponse?: unknown;\n    }> = this._onAIAnalysis.event;\n    public readonly onProcessOutput = this._onProcessOutput.event;\n    public readonly onMemoryOperation = this._onMemoryOperation.event;\n    public readonly onWorkflowExecution = this._onWorkflowExecution.event;\n\n    constructor(\n        services: {\n            terminalSession: InteractiveSession;\n            aiContext: IAIContext;\n            toolContext: IToolTerminalContext;\n            memoryManager?: MemoryManager;\n            workflowManager?: typeof workflowManager;\n        }\n    ) {\n        super();\n        this._terminalSession = services.terminalSession;\n        this._aiContext = services.aiContext;\n        this._toolContextMap.set(services.toolContext.session.id, services.toolContext);\n\n        // Initialize memory and workflow managers\n        this._memoryManager = services.memoryManager || MemoryManager.getInstance();\n        this._workflowManager = services.workflowManager || workflowManager;\n        this._workflowRegistry = workflowRegistry;\n\n        // Initialize patterns and integrations\n        this.initializeCommonPatterns();\n        this.initializeMemoryIntegration();\n        this.initializeWorkflowIntegration();\n\n        // Set up event handlers\n        const events = this.getTerminalEvents();\n        events.onPatternMatch(e => {\n            if (e.pattern.action === 'quick-fix') {\n                this._handleQuickFixPattern(e.pattern as QuickFixPattern, e.matches);\n            }\n        });\n    }\n\n    public getToolContext(sessionId: string): IToolTerminalContext | undefined {\n        return this._toolContextMap.get(sessionId);\n    }\n\n    protected getAIContext(): IAIContext {\n        return this._aiContext;\n    }\n\n    protected abstract executeAIOperation(context: IAIContext, input: unknown): Promise<unknown>;\n\n    protected getTerminalSession(): InteractiveSession {\n        return this._terminalSession;\n    }\n\n    /**\n     * Get the memory manager instance\n     */\n    protected getMemoryManager(): MemoryManager {\n        return this._memoryManager;\n    }\n\n    /**\n     * Get the workflow manager instance\n     */\n    protected getWorkflowManager(): typeof workflowManager {\n        return this._workflowManager;\n    }\n\n    /**\n     * Get the workflow registry instance\n     */\n    protected getWorkflowRegistry(): typeof workflowRegistry {\n        return this._workflowRegistry;\n    }\n\n    /**\n     * Initialize memory integration for the tool\n     */\n    private initializeMemoryIntegration(): void {\n        // Set up memory event handlers\n        this.onMemoryOperation(async (event) => {\n            // Store operation in tool memory\n            await this.storeOperationMemory({\n                operation: event.operation,\n                input: { memoryId: event.memory.id },\n                output: event.memory,\n                success: event.success,\n                metadata: {\n                    source: 'memory-operation' as MemorySource,\n                    type: 'procedural' as MemoryType,\n                    tags: ['memory', event.operation],\n                    importance: event.success ? 0.8 : 0.3\n                }\n            });\n        });\n    }\n\n    /**\n     * Initialize workflow integration for the tool\n     */\n    private initializeWorkflowIntegration(): void {\n        // Set up workflow event handlers\n        this.onWorkflowExecution(async (event) => {\n            // Store workflow execution in tool memory\n            await this.storeOperationMemory({\n                operation: 'workflow-execution',\n                input: { workflowId: event.workflowId, step: event.step },\n                output: event.result,\n                success: event.result.success,\n                metadata: {\n                    source: 'workflow' as MemorySource,\n                    type: 'procedural' as MemoryType,\n                    tags: ['workflow', event.workflowId, event.step],\n                    importance: event.result.success ? 0.9 : 0.4\n                }\n            });\n        });\n    }\n\n    /**\n     * Store operation memory with enhanced metadata\n     */\n    protected async storeOperationMemory(operation: {\n        operation: string;\n        input: Record<string, unknown>;\n        output?: unknown;\n        success: boolean;\n        metadata?: Partial<{\n            source: MemorySource;\n            type: MemoryType;\n            tags: string[];\n            importance: number;\n            workspaceContext: string;\n            affectedFiles: string[];\n        }>;\n    }): Promise<IToolOperationMemory> {\n        const operationMemory = this.createOperationMemory(\n            this.constructor.name,\n            operation.operation,\n            operation.input,\n            {\n                ...operation.metadata,\n                success: operation.success\n            }\n        );\n\n        operationMemory.output = operation.output;\n        operationMemory.state.status = operation.success ? 'completed' : 'failed';\n        operationMemory.state.endTime = Date.now();\n        operationMemory.duration = operationMemory.state.endTime - operationMemory.state.startTime;\n\n        // Store in local operation memory\n        this._operationMemory.set(operationMemory.id, operationMemory);\n\n        // Store in global memory system\n        try {\n            await this._memoryManager.addMemory({\n                content: JSON.stringify({\n                    toolName: operationMemory.toolName,\n                    operation: operationMemory.operation,\n                    input: operationMemory.input,\n                    output: operationMemory.output,\n                    duration: operationMemory.duration,\n                    success: operation.success\n                }),\n                metadata: operationMemory.metadata\n            });\n        } catch (error) {\n            Logger.instance.error('Failed to store operation in memory:', error);\n        }\n\n        return operationMemory;\n    }\n\n    /**\n     * Create operation memory entry\n     */\n    protected createOperationMemory(\n        toolName: string,\n        operation: string,\n        input: Record<string, unknown>,\n        metadata: Partial<{\n            source: MemorySource;\n            type: MemoryType;\n            tags: string[];\n            importance: number;\n            workspaceContext: string;\n            affectedFiles: string[];\n            success: boolean;\n        }> = {}\n    ): IToolOperationMemory {\n        return {\n            id: `${toolName}-${operation}-${Date.now()}`,\n            toolName,\n            operation,\n            input,\n            metadata: {\n                source: metadata.source || 'tool' as MemorySource,\n                type: metadata.type || 'procedural' as MemoryType,\n                tags: metadata.tags || [toolName.toLowerCase(), operation],\n                importance: metadata.importance || 0.5,\n                workspaceContext: metadata.workspaceContext,\n                affectedFiles: metadata.affectedFiles,\n                success: metadata.success,\n                timestamp: Date.now()\n            },\n            state: {\n                status: 'pending',\n                startTime: Date.now()\n            }\n        };\n    }\n\n    /**\n     * Comprehensive memory operations for tools\n     */\n\n    /**\n     * Store content in memory with enhanced metadata\n     */\n    protected async storeMemory(\n        content: string,\n        metadata: Partial<{\n            source: MemorySource;\n            type: MemoryType;\n            tags: string[];\n            importance: number;\n            workspaceContext: string;\n            affectedFiles: string[];\n        }> = {}\n    ): Promise<MemoryEntry> {\n        const memoryEntry = await this._memoryManager.addMemory({\n            content,\n            metadata: {\n                source: metadata.source || 'tool' as MemorySource,\n                type: metadata.type || 'procedural' as MemoryType,\n                tags: metadata.tags || [this.constructor.name.toLowerCase()],\n                importance: metadata.importance || 0.5,\n                workspaceContext: metadata.workspaceContext,\n                affectedFiles: metadata.affectedFiles,\n                toolName: this.constructor.name,\n                timestamp: Date.now()\n            }\n        });\n\n        this._onMemoryOperation.fire({\n            operation: 'store',\n            memory: memoryEntry,\n            success: true\n        });\n\n        return memoryEntry;\n    }\n\n    /**\n     * Retrieve memories based on search criteria\n     */\n    protected async searchMemories(\n        query: string,\n        options: Partial<MemorySearchOptions> = {}\n    ): Promise<MemoryEntry[]> {\n        try {\n            const memories = await this._memoryManager.searchSimilarMemories(query, {\n                limit: options.limit || 10,\n                threshold: options.threshold || 0.7,\n                source: options.source,\n                type: options.type,\n                tags: options.tags ? [...options.tags, this.constructor.name.toLowerCase()] : [this.constructor.name.toLowerCase()],\n                ...options\n            });\n\n            this._onMemoryOperation.fire({\n                operation: 'search',\n                memory: { id: 'search-result', content: query, metadata: { source: 'tool' as MemorySource, type: 'query' as MemoryType } } as MemoryEntry,\n                success: true\n            });\n\n            return memories;\n        } catch (error) {\n            Logger.instance.error('Failed to search memories:', error);\n            this._onMemoryOperation.fire({\n                operation: 'search',\n                memory: { id: 'search-error', content: query, metadata: { source: 'tool' as MemorySource, type: 'query' as MemoryType } } as MemoryEntry,\n                success: false\n            });\n            return [];\n        }\n    }\n\n    /**\n     * Comprehensive workflow operations for tools\n     */\n\n    /**\n     * Execute a workflow with the given inputs\n     */\n    protected async executeWorkflow(\n        workflowId: string,\n        inputs: Record<string, any> = {},\n        context?: Partial<WorkflowContext>\n    ): Promise<WorkflowStepResult> {\n        try {\n            // Get workflow from registry\n            const workflowDefinition = this._workflowRegistry.getWorkflow(workflowId);\n            if (!workflowDefinition) {\n                throw new Error(`Workflow not found: ${workflowId}`);\n            }\n\n            // Create workflow instance\n            const workflow = new Workflow(workflowDefinition);\n\n            // Store active workflow\n            this._activeWorkflows.set(workflowId, workflow);\n\n            // Set up progress tracking\n            workflow.onProgress((stepId, progress) => {\n                this._onWorkflowExecution.fire({\n                    workflowId,\n                    step: stepId,\n                    result: {\n                        success: true,\n                        output: { progress },\n                        metadata: { stepId, progress }\n                    }\n                });\n            });\n\n            // Execute workflow\n            const result = await workflow.execute(inputs, context);\n\n            // Store workflow execution in memory\n            await this.storeOperationMemory({\n                operation: 'workflow-execution',\n                input: { workflowId, inputs },\n                output: result,\n                success: result.success,\n                metadata: {\n                    source: 'workflow' as MemorySource,\n                    type: 'procedural' as MemoryType,\n                    tags: ['workflow', workflowId],\n                    importance: result.success ? 0.9 : 0.4,\n                    workspaceContext: context?.workflow?.getDefinition().name\n                }\n            });\n\n            // Clean up active workflow\n            this._activeWorkflows.delete(workflowId);\n\n            return result;\n        } catch (error) {\n            Logger.instance.error(`Failed to execute workflow ${workflowId}:`, error);\n\n            // Clean up active workflow\n            this._activeWorkflows.delete(workflowId);\n\n            const errorResult: WorkflowStepResult = {\n                success: false,\n                error: error instanceof Error ? error.message : String(error),\n                metadata: { workflowId, error: true }\n            };\n\n            this._onWorkflowExecution.fire({\n                workflowId,\n                step: 'error',\n                result: errorResult\n            });\n\n            return errorResult;\n        }\n    }\n\n    /**\n     * Create and register a new workflow\n     */\n    protected createWorkflow(\n        definition: WorkflowDefinition\n    ): void {\n        this._workflowRegistry.registerWorkflow(definition);\n        Logger.instance.info(`Workflow registered: ${definition.id}`);\n    }\n\n    /**\n     * Get available workflows by tag\n     */\n    protected getWorkflowsByTag(tag: string): WorkflowDefinition[] {\n        return this._workflowRegistry.getWorkflowsByTag(tag);\n    }\n\n    /**\n     * Cancel an active workflow\n     */\n    protected async cancelWorkflow(workflowId: string): Promise<boolean> {\n        const workflow = this._activeWorkflows.get(workflowId);\n        if (workflow) {\n            try {\n                await workflow.cancel();\n                this._activeWorkflows.delete(workflowId);\n\n                this._onWorkflowExecution.fire({\n                    workflowId,\n                    step: 'cancelled',\n                    result: {\n                        success: false,\n                        error: 'Workflow cancelled',\n                        metadata: { cancelled: true }\n                    }\n                });\n\n                return true;\n            } catch (error) {\n                Logger.instance.error(`Failed to cancel workflow ${workflowId}:`, error);\n                return false;\n            }\n        }\n        return false;\n    }\n\n    /**\n     * Get the terminal events for the current tool context\n     */\n    protected getTerminalEvents(): ITerminalToolEvents {\n        const toolContext = Array.from(this._toolContextMap.values())[0];\n        const eventEmitter = new vscode.EventEmitter<{\n            pattern: IAIToolPattern;\n            matches: RegExpMatchArray;\n        }>();\n\n        if (toolContext) {\n            // Bind terminal events to the tool context\n            toolContext.provider.onPatternMatch(e => {\n                eventEmitter.fire(e);\n            });\n        }\n\n        return {\n            ...this.getTerminalSession().events,\n            onAIAction: this.onAIAnalysis,\n            onPatternMatch: eventEmitter.event\n        };\n    }\n\n    /**\n     * Handle quick fix patterns in terminal output\n     */\n    protected _handleQuickFixPattern(pattern: QuickFixPattern, matches: RegExpMatchArray): Promise<void> {\n        if (!pattern || !matches) {\n            return Promise.resolve();\n        }\n\n        const editor = vscode.window.activeTextEditor;\n        if (!editor) return Promise.resolve();\n\n        const action = pattern.fix(matches, editor.document, new vscode.Range(0, 0, 0, 0));\n        if (action) {\n            this._onAIAnalysis.fire({\n                action,\n                context: {\n                    pattern,\n                    matches,\n                    timestamp: Date.now()\n                }\n            });\n        }\n\n        return Promise.resolve();\n    }\n\n    /**\n     * Initialize common patterns that all tools might need\n     */\n    protected initializeCommonPatterns(): void {\n        // Common error patterns\n        this.registerPattern({\n            pattern: /error|exception|failed/i,\n            action: 'analyze-error',\n            priority: 100,\n            category: 'error',\n            fix: (match: RegExpMatchArray, document: vscode.TextDocument) => {\n                const action = new vscode.CodeAction(\n                    'Analyze Error',\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.analyzeError',\n                    title: 'Analyze Error',\n                    arguments: [match[0], document.uri.toString()]\n                };\n                return action;\n            }\n        });\n\n        // Common warning patterns\n        this.registerPattern({\n            pattern: /warning|warn/i,\n            action: 'analyze-warning',\n            priority: 90,\n            category: 'warning',\n            fix: (match: RegExpMatchArray, document: vscode.TextDocument) => {\n                const action = new vscode.CodeAction(\n                    'Analyze Warning',\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.analyzeWarning',\n                    title: 'Analyze Warning',\n                    arguments: [match[0], document.uri.toString()]\n                };\n                return action;\n            }\n        });\n\n        // Common success patterns\n        this.registerPattern({\n            pattern: /success|completed|done/i,\n            action: 'process-success',\n            priority: 80,\n            category: 'success',\n            fix: (match: RegExpMatchArray, document: vscode.TextDocument) => {\n                const action = new vscode.CodeAction(\n                    'Process Success',\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.processSuccess',\n                    title: 'Process Success',\n                    arguments: [match[0], document.uri.toString()]\n                };\n                return action;\n            }\n        });\n    }\n\n    /**\n     * Execute a command with AI-enhanced error handling and suggestions\n     */\n    protected async executeWithAI(\n        command: string,\n        options?: {\n            timeout?: number;\n            expectPrompt?: boolean;\n            background?: boolean;\n            workingDirectory?: string;\n        }\n    ): Promise<CommandResult> {\n        try {\n            const result = await this._terminalSession.executeCommand(command, options);\n            if (typeof result === 'string') {\n                await this.processTerminalOutput(result);\n                return {\n                    exitCode: 0,\n                    output: result,\n                    command,\n                    duration: 0,\n                    success: true,\n                    completedAt: new Date()\n                };\n            }\n            return result;\n        } catch (error) {\n            // Create error diagnostics and trigger AI analysis\n            const diagnostic = new vscode.Diagnostic(\n                new vscode.Range(0, 0, 0, 0),\n                `Command failed: ${error instanceof Error ? error.message : String(error)}`,\n                vscode.DiagnosticSeverity.Error\n            );\n            diagnostic.source = 'terminal';\n            \n            const diagnostics = [diagnostic];\n            this.addDiagnostics(vscode.Uri.parse('terminal://output'), diagnostics);\n\n            // Process error with AI\n            await this.handleAIProcessing(\n                error instanceof Error ? error.message : String(error),\n                []\n            );\n            throw error;\n        }\n    }\n\n    /**\n     * Add diagnostics to the terminal\n     */\n    protected addDiagnostics(uri: vscode.Uri, diagnostics: vscode.Diagnostic[]): void {\n        const collection = vscode.languages.createDiagnosticCollection('terminal');\n        collection.set(uri, diagnostics);\n    }\n\n    /**\n     * Register a new AI pattern for terminal output analysis\n     */\n    protected registerAIPattern(pattern: IAIToolPattern): void {\n        this.registerPattern(pattern);\n    }\n\n    /**\n     * Comprehensive terminal capabilities integration with code actions\n     */\n\n    /**\n     * Execute command with enhanced code action analysis\n     */\n    protected async executeTerminalCommandWithCodeActions(\n        command: string,\n        options?: {\n            timeout?: number;\n            expectPrompt?: boolean;\n            background?: boolean;\n            workingDirectory?: string;\n            enableCodeActions?: boolean;\n            storeInMemory?: boolean;\n        }\n    ): Promise<CommandResult> {\n        const startTime = Date.now();\n        const config = {\n            timeout: options?.timeout ?? 30000,\n            expectPrompt: options?.expectPrompt ?? true,\n            background: options?.background ?? false,\n            workingDirectory: options?.workingDirectory,\n            enableCodeActions: options?.enableCodeActions ?? true,\n            storeInMemory: options?.storeInMemory ?? true\n        };\n\n        try {\n            // Execute command using terminal session\n            const result = await this._terminalSession.executeCommandAdvanced(command, {\n                timeout: config.timeout,\n                expectPrompt: config.expectPrompt,\n                background: config.background,\n                workingDirectory: config.workingDirectory\n            });\n\n            // Enhanced result with code actions\n            const enhancedResult: CommandResult = {\n                ...result,\n                codeActions: [],\n                diagnostics: [],\n                quickFixes: [],\n                aiAnalysis: undefined\n            };\n\n            // Analyze output for code actions if enabled\n            if (config.enableCodeActions) {\n                enhancedResult.codeActions = this.analyzeTerminalOutputForCodeActions(result.output);\n                enhancedResult.diagnostics = this.generateDiagnosticsFromOutput(result.output);\n                enhancedResult.quickFixes = this.createQuickFixesFromActions(enhancedResult.codeActions);\n            }\n\n            // AI analysis for complex outputs\n            if (this.shouldProcessWithAI(result.output)) {\n                enhancedResult.aiAnalysis = await this.performAIAnalysis(result.output);\n            }\n\n            // Store in memory if enabled\n            if (config.storeInMemory) {\n                await this.storeOperationMemory({\n                    operation: 'terminal-command',\n                    input: { command, options: config },\n                    output: enhancedResult,\n                    success: result.success,\n                    metadata: {\n                        source: 'terminal' as MemorySource,\n                        type: 'procedural' as MemoryType,\n                        tags: ['terminal', 'command', result.success ? 'success' : 'error'],\n                        importance: result.success ? 0.6 : 0.8,\n                        affectedFiles: this.extractFilePathsFromOutput(result.output)\n                    }\n                });\n            }\n\n            return enhancedResult;\n        } catch (error) {\n            const errorResult: CommandResult = {\n                exitCode: -1,\n                output: error instanceof Error ? error.message : String(error),\n                command,\n                duration: Date.now() - startTime,\n                success: false,\n                completedAt: new Date(),\n                codeActions: [],\n                diagnostics: [{\n                    range: new vscode.Range(0, 0, 0, 0),\n                    message: `Command failed: ${error instanceof Error ? error.message : String(error)}`,\n                    severity: vscode.DiagnosticSeverity.Error,\n                    source: 'terminal'\n                }],\n                quickFixes: [],\n                aiAnalysis: {\n                    confidence: 0.9,\n                    suggestions: ['Check command syntax', 'Verify permissions', 'Check file paths'],\n                    category: 'error'\n                }\n            };\n\n            // Store error in memory\n            if (config.storeInMemory) {\n                await this.storeOperationMemory({\n                    operation: 'terminal-command-error',\n                    input: { command, options: config },\n                    output: errorResult,\n                    success: false,\n                    metadata: {\n                        source: 'terminal' as MemorySource,\n                        type: 'procedural' as MemoryType,\n                        tags: ['terminal', 'command', 'error'],\n                        importance: 0.9\n                    }\n                });\n            }\n\n            return errorResult;\n        }\n    }\n\n    /**\n     * Analyze terminal output for code actions using integrated providers\n     */\n    protected analyzeTerminalOutputForCodeActions(output: string): vscode.CodeAction[] {\n        const actions: vscode.CodeAction[] = [];\n\n        // Error patterns with code actions\n        if (/command not found|not recognized/i.test(output)) {\n            const match = output.match(/command not found:\\s*(.+?)$/m) || output.match(/'(.+?)' is not recognized/);\n            if (match) {\n                const action = new vscode.CodeAction(\n                    `Install missing command: ${match[1]}`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.terminal.installCommand',\n                    title: `Install ${match[1]}`,\n                    arguments: [match[1]]\n                };\n                actions.push(action);\n            }\n        }\n\n        // Permission errors\n        if (/permission denied|access denied|EACCES/i.test(output)) {\n            const action = new vscode.CodeAction(\n                'Fix permission issues',\n                vscode.CodeActionKind.QuickFix\n            );\n            action.command = {\n                command: 'codessa.terminal.fixPermissions',\n                title: 'Fix permissions',\n                arguments: [output]\n            };\n            actions.push(action);\n        }\n\n        // File not found errors\n        if (/no such file or directory|cannot find|file not found/i.test(output)) {\n            const fileMatch = output.match(/(?:no such file or directory|cannot find|file not found):\\s*(.+?)$/m);\n            if (fileMatch) {\n                const action = new vscode.CodeAction(\n                    `Create missing file: ${fileMatch[1]}`,\n                    vscode.CodeActionKind.QuickFix\n                );\n                action.command = {\n                    command: 'codessa.terminal.createFile',\n                    title: `Create ${fileMatch[1]}`,\n                    arguments: [fileMatch[1]]\n                };\n                actions.push(action);\n            }\n        }\n\n        return actions;\n    }\n\n    /**\n     * Generate diagnostics from terminal output\n     */\n    protected generateDiagnosticsFromOutput(output: string): vscode.Diagnostic[] {\n        const diagnostics: vscode.Diagnostic[] = [];\n        const lines = output.split('\\n');\n\n        lines.forEach((line, index) => {\n            // Error patterns\n            if (/error:|ERROR:|failed/i.test(line)) {\n                diagnostics.push(new vscode.Diagnostic(\n                    new vscode.Range(index, 0, index, line.length),\n                    line.trim(),\n                    vscode.DiagnosticSeverity.Error\n                ));\n            }\n            // Warning patterns\n            else if (/warning:|WARN:|deprecated/i.test(line)) {\n                diagnostics.push(new vscode.Diagnostic(\n                    new vscode.Range(index, 0, index, line.length),\n                    line.trim(),\n                    vscode.DiagnosticSeverity.Warning\n                ));\n            }\n        });\n\n        return diagnostics;\n    }\n\n    /**\n     * Create quick fixes from code actions\n     */\n    protected createQuickFixesFromActions(actions: vscode.CodeAction[]): CodeActionResult[] {\n        return actions.map(action => ({\n            action,\n            priority: this.getActionPriority(action),\n            category: this.getActionCategory(action),\n            confidence: this.getActionConfidence(action),\n            metadata: {\n                source: 'terminal',\n                trigger: 'auto',\n                context: {\n                    timestamp: Date.now(),\n                    toolName: this.constructor.name\n                }\n            }\n        }));\n    }\n\n    /**\n     * Utility methods for AI analysis and file path extraction\n     */\n    protected shouldProcessWithAI(output: string): boolean {\n        return output.includes('error') ||\n               output.includes('warning') ||\n               output.includes('failed') ||\n               output.includes('exception');\n    }\n\n    protected async performAIAnalysis(output: string): Promise<{\n        confidence: number;\n        suggestions: string[];\n        category: string;\n    }> {\n        // Basic AI analysis - can be enhanced with actual AI service\n        const suggestions: string[] = [];\n        let category = 'general';\n        let confidence = 0.5;\n\n        if (output.includes('error')) {\n            category = 'error';\n            confidence = 0.8;\n            suggestions.push('Check error message for specific details');\n            suggestions.push('Verify input parameters and file paths');\n        }\n\n        if (output.includes('permission')) {\n            suggestions.push('Check file/directory permissions');\n            suggestions.push('Run with elevated privileges if needed');\n            confidence = 0.9;\n        }\n\n        return { confidence, suggestions, category };\n    }\n\n    protected extractFilePathsFromOutput(output: string): string[] {\n        const paths: string[] = [];\n        const pathRegex = /(?:[a-zA-Z]:\\\\|\\/)[^\\s]+/g;\n        const matches = output.match(pathRegex);\n\n        if (matches) {\n            paths.push(...matches.filter(path =>\n                path.includes('.') || path.endsWith('/') || path.endsWith('\\\\')\n            ));\n        }\n\n        return paths;\n    }\n\n    protected getActionPriority(action: vscode.CodeAction): number {\n        if (action.kind?.value.includes('quickfix')) return 100;\n        if (action.kind?.value.includes('refactor')) return 80;\n        return 50;\n    }\n\n    protected getActionCategory(action: vscode.CodeAction): string {\n        if (action.kind?.value.includes('quickfix')) return 'quickfix';\n        if (action.kind?.value.includes('refactor')) return 'refactor';\n        return 'general';\n    }\n\n    protected getActionConfidence(action: vscode.CodeAction): number {\n        const title = action.title.toLowerCase();\n        if (title.includes('install') || title.includes('create')) return 0.9;\n        if (title.includes('fix') || title.includes('analyze')) return 0.8;\n        return 0.7;\n    }\n\n    public dispose(): void {\n        super.dispose();\n        this._terminalSession.dispose();\n    }\n}\n\n// Import node-fetch for HTTP requests\nimport fetch from 'node-fetch';\nimport { randomUUID as cryptoRandomUUID } from 'crypto';\n\n// Use Node.js crypto.randomUUID() for secure UUID generation\nconst randomUUID = (): string => {\n    return cryptoRandomUUID();\n};\n\n\n// #region PART 1: Core Framework Contracts & Interfaces\n\n// ===================================================================================\n//                        CORE INTERFACES & DATA STRUCTURES\n// ===================================================================================\n\n/**\n * Represents multimodal data that can be processed by the AI system.\n * This includes various types of media such as images, audio, or other binary data.\n * Used for clipboard operations and file-based multimodal content processing.\n *\n * @interface MultimodalData\n * @property {'image/jpeg' | 'image/png' | 'image/webp'} mimeType - The MIME type of the data (e.g., 'image/jpeg', 'image/png')\n * @property {string} base64Data - The base64-encoded content of the data\n * @property {'clipboard' | 'file'} source - The source of the multimodal data\n */\nexport interface MultimodalData {\n    mimeType: 'image/jpeg' | 'image/png' | 'image/webp';\n    base64Data: string;\n    source: 'clipboard' | 'file';\n}\n\n/**\n * Represents a single content part of a prompt for a language model.\n * This can be either text or image content that will be sent to the language model.\n * \n * @property {'text' | 'image'} type - The type of the content part\n * @property {string} [text] - The text content (for type 'text')\n * @property {object} [source] - The image source (for type 'image')\n * @property {'base64'} source.type - The type of image source (currently only base64 is supported)\n * @property {string} source.media_type - The MIME type of the image\n * @property {string} source.data - The base64-encoded image data\n */\nexport type LanguageModelPromptPart =\n    | { type: 'text'; text: string; }\n    | { type: 'image'; source: { type: 'base64'; media_type: MultimodalData['mimeType']; data: string; }; };\n\n/**\n * Represents a complete prompt that can be sent to a language model.\n * A prompt consists of an array of content parts, which can include both text and images.\n * This allows for multimodal inputs to the language model.\n * \n * @type {Array<LanguageModelPromptPart>}\n */\nexport type LanguageModelPrompt = LanguageModelPromptPart[];\n\n/**\n * Represents a hierarchical directory structure with files and subdirectories.\n * This is used to model the file system structure in a way that can be easily\n * traversed and manipulated by tools and services.\n * \n * @property {string} path - The full path to the directory\n * @property {string} name - The name of the directory\n * @property {Array<DirectoryTree | FileEntry>} children - Array of child entries (files or subdirectories)\n * @property {'directory'} type - Type discriminator, always 'directory' for directories\n */\nexport interface DirectoryTree {\n    path: string;\n    name: string;\n    children: (DirectoryTree | FileEntry)[];\n    type: 'directory';\n}\n\n/**\n * Represents a file entry in a directory structure.\n * \n * @property {string} path - The full path to the file\n * @property {string} name - The name of the file\n * @property {'file'} type - Type discriminator, always 'file' for files\n */\ntype FileEntry = {\n    path: string;\n    name: string;\n    type: 'file';\n};\n\n/**\n * Represents the memory schema for a tool's operations.\n * This interface defines the structure for storing and retrieving\n * tool-specific memory entries including operation history and context.\n */\nexport interface IToolMemorySchema {\n    /** Unique identifier for this memory record */\n    id: string;\n\n    /** The name of the tool that generated this memory */\n    toolName: string;\n\n    /** The specific operation or action performed by the tool */\n    operation: string;\n\n    /** Timestamp when this memory was created */\n    timestamp: number;\n\n    /** The input parameters used for the operation */\n    input: Record<string, unknown>;\n\n    /** The result or output of the operation */\n    output?: unknown;\n\n    /** Any errors that occurred during the operation */\n    error?: {\n        message: string;\n        code?: string;\n        stack?: string;\n    };\n\n    /** Additional contextual metadata about the operation */\n    metadata: {\n        /** The source that triggered this tool operation */\n        source: MemorySource;\n        /** The type of memory entry */\n        type: MemoryType;\n        /** Optional tags for categorizing the memory */\n        tags?: string[];\n        /** The workspace context where the operation occurred */\n        workspaceContext?: string;\n        /** The file(s) affected by this operation, if any */\n        affectedFiles?: string[];\n        /** Whether this operation was successful */\n        success: boolean;\n        /** How important this memory is (0-1) */\n        importance?: number;\n        /** Additional tool-specific metadata */\n        [key: string]: unknown;\n    };\n}\n\n/**\n * Represents a single tool operation's memory record.\n * This interface extends IToolMemorySchema with additional\n * execution and tracking details specific to a tool operation.\n */\nexport interface IToolOperationMemory extends IToolMemorySchema {\n    /** Duration of the operation in milliseconds */\n    duration: number;\n\n    /** The execution context identifier */\n    executionId: string;\n\n    /** Current state of the operation */\n    state: {\n        /** The status of this operation */\n        status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';\n        /** When the operation started */\n        startTime: number;\n        /** When the operation ended (if completed) */\n        endTime?: number;\n        /** Progress information (0-100) */\n        progress?: number;\n    };\n\n    /** Dependencies between operations */\n    dependencies?: {\n        /** IDs of operations that must complete before this one */\n        requiredOperations?: string[];\n        /** IDs of operations that depend on this one */\n        dependentOperations?: string[];\n    };\n\n    /** Resource usage metrics */\n    metrics?: {\n        /** CPU usage during operation (0-100) */\n        cpuUsage?: number;\n        /** Memory usage in bytes */\n        memoryUsage?: number;\n        /** Number of API calls made */\n        apiCalls?: number;\n        /** Number of file operations */\n        fileOperations?: number;\n    };\n}\n\n/**\n * Standardized logging interface for consistent logging across the application.\n * This interface is injected into all services and tools to provide a consistent\n * way to log messages with different severity levels.\n * \n * @method debug - Log a debug-level message (for detailed diagnostic information)\n * @method info - Log an informational message (for general operational messages)\n * @method warn - Log a warning message (for potentially harmful situations)\n * @method error - Log an error message (for error conditions that need attention)\n */\nexport interface ILogger {\n    /**\n     * Log a debug-level message with optional metadata.\n     * @param {string} message - The message to log\n     * @param {object} [metadata] - Optional additional data to include with the log\n     */\n    debug(message: string, metadata?: object): void;\n\n    /**\n     * Log an informational message with optional metadata.\n     * @param {string} message - The message to log\n     * @param {object} [metadata] - Optional additional data to include with the log\n     */\n    info(message: string, metadata?: object): void;\n\n    /**\n     * Log a warning message with optional metadata.\n     * @param {string} message - The message to log\n     * @param {object} [metadata] - Optional additional data to include with the log\n     */\n    warn(message: string, metadata?: object): void;\n\n    /**\n     * Log an error message or Error object with optional metadata.\n     * @param {string | Error} message - The error message or Error object to log\n     * @param {object} [metadata] - Optional additional data to include with the log\n     */\n    error(message: string | Error, metadata?: object): void;\n}\n\n/**\n * Provides tools with a high-level understanding of the workspace structure.\n * This interface abstracts workspace operations to provide a consistent way to\n * interact with the file system and workspace contents across different environments.\n */\nexport interface IWorkspaceKnowledge {\n    /**\n     * Retrieves a hierarchical representation of the workspace directory structure.\n     * @param {Object} [options] - Configuration options for directory tree generation\n     * @param {string[]} [options.ignorePatterns] - Array of glob patterns to exclude from the tree\n     * @returns {Promise<DirectoryTree | null>} A promise that resolves to the directory tree or null if no workspace is open\n     * @throws {Error} If there's an error accessing the file system\n     * \n     * @example\n     * ```typescript\n     * const tree = await workspaceKnowledge.getDirectoryTree({\n     *   ignorePatterns: ['**\\/node_modules/**', '**\\/.git/**']\n     * });\n     * ```\n     */\n    getDirectoryTree(options?: { ignorePatterns?: string[] }): Promise<DirectoryTree | null>;\n\n    /**\n     * Finds files in the workspace matching the given glob pattern.\n     * @param {string} globPattern - The glob pattern to match files against\n     * @returns {Promise<Uri[]>} A promise that resolves to an array of file URIs\n     * @throws {Error} If the pattern is invalid or if there's an error searching\n     * \n     * @example\n     * ```typescript\n     * const tsFiles = await workspaceKnowledge.findFilesByPattern('**\\\\*.ts');\n     * ```\n     */\n    findFilesByPattern(globPattern: string): Promise<Uri[]>;\n\n    /**\n     * Reads the content of a file in the workspace.\n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @returns {Promise<string>} A promise that resolves to the file content as a string\n     * @throws {Error} If the file doesn't exist or cannot be read\n     * \n     * @example\n     * ```typescript\n     * const content = await workspaceKnowledge.getFileContent('src/utils/helpers.ts');\n     * ```\n     */\n    getFileContent(relativePath: string): Promise<string>;\n}\n\n/**\n * Options for file append operations\n */\nexport interface FileAppendOptions {\n    /**\n     * Whether to add a newline before appending content\n     * @default true\n     */\n    newLine?: boolean;\n\n    /**\n     * Separator to add between existing content and new content\n     * @default '\\n' (newline)\n     */\n    separator?: string;\n}\n\n/**\n * Options for file content search operations\n */\nexport interface FileSearchOptions {\n    /**\n     * Whether the search should be case-sensitive\n     * @default false\n     */\n    caseSensitive?: boolean;\n\n    /**\n     * Whether to use regular expressions for pattern matching\n     * @default false\n     */\n    useRegex?: boolean;\n\n    /**\n     * Maximum number of matches to return (0 for unlimited)\n     * @default 0\n     */\n    maxMatches?: number;\n}\n\n/**\n * Represents a match found during a file content search\n */\nexport interface ContentMatch {\n    lineNumber: number;\n    lineContent: string;\n    match: string | string[];\n    /** Character positions of the match within the line (start, end) */\n    position: [number, number];\n}\n\n/**\n * Supported diff formats for file comparison and patching\n */\nexport enum DiffFormat {\n    /** Unified diff format (default) */\n    Unified = 'unified',\n    /** Context diff format */\n    Context = 'context',\n    /** Git-style diff */\n    Git = 'git',\n    /** JSON diff format for structured data */\n    Json = 'json',\n    /** Line-by-line diff */\n    Line = 'line',\n    /** Word-level diff */\n    Word = 'word',\n    /** Character-level diff */\n    Char = 'char',\n    /** Side-by-side diff */\n    SideBySide = 'side-by-side',\n    /** Inline diff */\n    Inline = 'inline'\n}\n\n// VSCode types are already imported at the top of the file\n\n/**\n * Options for diff generation and application\n */\nexport interface DiffOptions {\n    // Diff Formatting Options\n    /**\n     * The format of the diff to generate/apply\n     * @default DiffFormat.Unified\n     */\n    format?: DiffFormat;\n\n    /**\n     * Number of context lines to include around changes\n     * @default 3\n     */\n    contextLines?: number;\n\n    // Diff Generation Options\n    /**\n     * Whether to ignore whitespace changes\n     * @default false\n     */\n    ignoreWhitespace?: boolean;\n\n    /**\n     * Whether to ignore case differences\n     * @default false\n     */\n    ignoreCase?: boolean;\n\n    /**\n     * Whether to ignore line endings (CRLF vs LF)\n     * @default true\n     */\n    ignoreLineEndings?: boolean;\n\n    /**\n     * Whether to ignore changes in indentation\n     * @default false\n     */\n    ignoreIndentation?: boolean;\n\n    // Advanced Diff Options\n    /**\n     * Whether to detect and handle moved/renamed content\n     * @default true\n     */\n    detectRenames?: boolean;\n\n    /**\n     * Whether to detect and handle copied content\n     * @default false\n     */\n    detectCopies?: boolean;\n\n    /**\n     * Whether to detect and handle moved/renamed files\n     * @default true\n     */\n    detectRenamedFiles?: boolean;\n\n    // Binary File Handling\n    /**\n     * Whether to detect and handle binary files\n     * @default true\n     */\n    detectBinary?: boolean;\n\n    /**\n     * Whether to generate/apply diffs for binary files\n     * @default false\n     */\n    binaryDiff?: boolean;\n\n    // Output Formatting\n    /**\n     * Whether to normalize line endings before diffing\n     * @default true\n     */\n    normalizeLineEndings?: boolean;\n\n    /**\n     * Whether to include line numbers in the diff output\n     * @default true\n     */\n    lineNumbers?: boolean;\n\n    /**\n     * Whether to include the file header in the diff output\n     * @default true\n     */\n    fileHeader?: boolean;\n\n    /**\n     * Whether to include the hunk headers in the diff output\n     * @default true\n     */\n    hunkHeaders?: boolean;\n\n    // VSCode Editor Integration\n    /**\n     * Whether to show the diff in VSCode's diff editor\n     * @default true when running in VSCode\n     */\n    showInEditor?: boolean;\n\n    /**\n     * The title to show in the diff editor\n     */\n    title?: string;\n\n    /**\n     * The view column to open the diff editor in\n     * @default ViewColumn.Beside\n     */\n    viewColumn?: typeof vscode.ViewColumn[keyof typeof vscode.ViewColumn];\n\n    /**\n     * Whether to preserve focus on the original editor\n     * @default true\n     */\n    preserveFocus?: boolean;\n\n    /**\n     * Selection to highlight in the diff editor\n     */\n    selection?: vscode.Range;\n\n    /**\n     * Reveal type for the diff editor\n     * @default vscode.TextEditorRevealType.InCenterIfOutsideViewport\n     */\n    revealType?: vscode.TextEditorRevealType;\n\n    /**\n     * Additional options for the diff editor\n     */\n    editorOptions?: vscode.TextDocumentShowOptions;\n\n    /**\n     * Language ID for syntax highlighting in the diff\n     * @default 'plaintext'\n     */\n    languageId?: string;\n}\n\n/**\n * Options for file editing operations\n */\n/**\n * Options for appending content to a file\n */\nexport interface FileAppendOptions {\n    /**\n     * Separator to use between existing content and new content\n     * @default '\\n'\n     */\n    separator?: string;\n}\n\n/**\n * Represents a match found in a file\n */\n/** Represents a match found in a search operation */\nexport interface FileContentMatch {\n    /** 1-based line number of the match */\n    line: number;\n    /** 1-based column number of the match */\n    column: number;\n    /** The actual text that matched */\n    match: string;\n    /** Any capture groups from regex matches */\n    groups: string[];\n    /** The file path relative to workspace root */\n    filePath: string;\n    /** The file's URI */\n    uri: Uri;\n    /** Preview of the surrounding content */\n    preview: string;\n    /** Additional search context */\n    context?: {\n        /** Number of lines before the match */\n        linesBefore?: string[];\n        /** Number of lines after the match */\n        linesAfter?: string[];\n        /** The matched line */\n        matchedLine: string;\n        /** Start position within the matched line */\n        matchStart: number;\n        /** End position within the matched line */\n        matchEnd: number;\n    };\n}\n\n/**\n * Options for searching within a file\n */\nexport interface FileSearchOptions {\n    /**\n     * Whether to use regular expression for searching\n     * @default false\n     */\n    useRegex?: boolean;\n\n    /**\n     * Whether the search is case-sensitive\n     * @default false\n     */\n    caseSensitive?: boolean;\n}\n\nexport interface FileEditOptions extends DiffOptions {\n    /**\n     * Whether to create the file if it doesn't exist\n     * @default false\n     */\n    createIfNotExists?: boolean;\n\n    /**\n     * Whether to preserve the file's line endings\n     * @default true\n     */\n    preserveLineEndings?: boolean;\n\n    /**\n     * Encoding to use when reading/writing the file\n     * @default 'utf8'\n     */\n    encoding?: BufferEncoding;\n\n    /**\n     * Whether to create a backup of the file before making changes\n     * @default false\n     */\n    backup?: boolean;\n\n    /**\n     * Whether to show a preview of the changes before applying them\n     * @default true\n     */\n    preview?: boolean;\n\n    /**\n     * Suffix to use for backup files\n     * @default '.bak'\n     */\n    backupSuffix?: string;\n\n    /**\n     * Whether to suppress success messages\n     * @default false\n     */\n    silent?: boolean;\n}\n\n/**\n * A secure, sandboxed service for all file system I/O operations.\n * This interface provides a controlled way to interact with the file system,\n * ensuring that operations are performed safely within the workspace boundaries.\n */\nexport interface IFileSystemManager {\n    // ========== Basic File Operations ==========\n\n    /**\n     * Reads the contents of a file as a UTF-8 encoded string.\n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @param {BufferEncoding} [encoding='utf8'] - The encoding to use when reading the file\n     * @returns {Promise<string>} A promise that resolves with the file contents\n     * @throws {Error} If the file doesn't exist, cannot be read, or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * const content = await fsManager.readFile('src/config/settings.json');\n     * ```\n     */\n    readFile(relativePath: string, encoding?: BufferEncoding): Promise<string>;\n\n    /**\n     * Writes content to a file, creating it if it doesn't exist or overwriting if it does.\n     * Parent directories will be created if they don't exist.\n     * \n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @param {string | Buffer} content - The content to write to the file\n     * @param {FileEditOptions} [options] - Additional options for the write operation\n     * @returns {Promise<void>} A promise that resolves when the write operation is complete\n     * @throws {Error} If the file cannot be written or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * await fsManager.writeFile('src/generated/config.js', 'const config = {};');\n     * ```\n     */\n    writeFile(relativePath: string, content: string | Buffer, options?: FileEditOptions): Promise<void>;\n\n    /**\n     * Appends content to the end of a file.\n     * \n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @param {string | Buffer} content - The content to append to the file\n     * @param {FileAppendOptions & FileEditOptions} [options] - Options for the append operation\n     * @returns {Promise<void>} A promise that resolves when the append operation is complete\n     * @throws {Error} If the file cannot be written to or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * // Basic append\n     * await fsManager.appendToFile('log.txt', 'New log entry');\n     * \n     * // Append with custom separator\n     * await fsManager.appendToFile('config.json', '\\n{\"newSetting\": true}', { separator: ',\\n' });\n     * ```\n     */\n    appendToFile(\n        relativePath: string,\n        content: string | Buffer,\n        options?: FileAppendOptions & FileEditOptions\n    ): Promise<void>;\n\n    /**\n     * Deletes a file from the workspace.\n     * \n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @returns {Promise<void>} A promise that resolves when the file is deleted\n     * @throws {Error} If the file doesn't exist, cannot be deleted, or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * await fsManager.deleteFile('temp/old-config.json');\n     * ```\n     */\n    deleteFile(relativePath: string): Promise<void>;\n\n    /**\n     * Creates a directory and any necessary parent directories.\n     * \n     * @param {string} relativePath - The path to the directory relative to the workspace root\n     * @returns {Promise<void>} A promise that resolves when the directory is created\n     * @throws {Error} If the directory cannot be created or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * await fsManager.createDirectory('src/generated/types');\n     * ```\n     */\n    createDirectory(relativePath: string): Promise<void>;\n\n    // ========== Advanced File Operations ==========\n\n    /**\n     * Finds all occurrences of a search pattern in a file.\n     * \n     * @param {string} relativePath - The path to the file to search in\n     * @param {string | RegExp} searchPattern - The pattern to search for (string or RegExp)\n     * @param {FileSearchOptions} [options] - Options for the search operation\n     * @returns {Promise<ContentMatch[]>} A promise that resolves with an array of matches\n     * @throws {Error} If the file cannot be read or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * // Simple text search\n     * const matches = await fsManager.findInFile('src/utils/helpers.ts', 'function calculate');\n     * \n     * // Case-insensitive regex search\n     * const regexMatches = await fsManager.findInFile(\n     *   'src/config.json', \n     *   /api[_-]?key\\s*[:=]\\s*['\"]([^'\"]+)['\"]/i,\n     *   { useRegex: true }\n     * );\n     * ```\n     */\n    findInFile(\n        relativePath: string,\n        searchPattern: string | RegExp,\n        options?: FileSearchOptions\n    ): Promise<ContentMatch[]>;\n\n    /**\n     * Replaces content in a file based on a search pattern.\n     * \n     * @param {string} relativePath - The path to the file to modify\n     * @param {string | RegExp} searchPattern - The pattern to search for\n     * @param {string | ((match: string, ...groups: string[]) => string)} replacement - The replacement string or function\n     * @param {FileSearchOptions & FileEditOptions} [options] - Options for the replace operation\n     * @returns {Promise<number>} A promise that resolves with the number of replacements made\n     * @throws {Error} If the file cannot be read, written, or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * // Simple text replacement\n     * const count = await fsManager.replaceInFile(\n     *   'config.json',\n     *   'old-api.example.com',\n     *   'new-api.example.com'\n     * );\n     * \n     * // Using a replacement function\n     * const updated = await fsManager.replaceInFile(\n     *   'package.json',\n     *   /\"version\":\\s*\"([\\d.]+)\"/,\n     *   (match, version) => `\"version\": \"${require('semver').inc(version, 'patch')}\"`,\n     *   { useRegex: true }\n     * );\n     * ```\n     */\n    replaceInFile(\n        relativePath: string,\n        searchPattern: string | RegExp,\n        replacement: string | ((match: string, ...groups: string[]) => string),\n        options?: FileSearchOptions & FileEditOptions\n    ): Promise<number>;\n\n    /**\n     * Inserts content at a specific line in a file.\n     * \n     * @param {string} relativePath - The path to the file to modify\n     * @param {number} lineNumber - The 1-based line number to insert at\n     * @param {string} content - The content to insert\n     * @param {FileEditOptions} [options] - Options for the insert operation\n     * @returns {Promise<void>} A promise that resolves when the insert is complete\n     * @throws {Error} If the file cannot be modified or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * // Insert an import at the top of a file\n     * await fsManager.insertAtLine('src/app.ts', 1, 'import * as path from \\'path\\';\\n');\n     * \n     * // Insert a route at a specific position\n     * await fsManager.insertAtLine('src/routes.ts', 10, '  { path: \\'/new-route\\', component: NewComponent },\\n');\n     * ```\n     */\n    insertAtLine(\n        relativePath: string,\n        lineNumber: number,\n        content: string,\n        options?: FileEditOptions\n    ): Promise<void>;\n\n    /**\n     * Updates or replaces lines in a file that match a predicate function.\n     * \n     * @param {string} relativePath - The path to the file to modify\n     * @param {(line: string, lineNumber: number) => string | null} predicate - Function that returns:\n     *   - A new string to replace the line with\n     *   - null to leave the line unchanged\n     *   - undefined to remove the line\n     * @param {FileEditOptions} [options] - Options for the update operation\n     * @returns {Promise<number>} A promise that resolves with the number of lines modified\n     * @throws {Error} If the file cannot be modified or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * // Update version numbers in a package.json\n     * const updated = await fsManager.updateFileLines('package.json', (line) => {\n     *   if (line.includes('\"version\":')) {\n     *     return line.replace(/\"version\":\\s*\"[\\d.]+/g, '\"version\": \"1.2.3\"');\n     *   }\n     *   return line; // Leave other lines unchanged\n     * });\n     * \n     * // Remove all console.log statements\n     * const cleaned = await fsManager.updateFileLines('src/utils/logger.ts', (line) => {\n     *   return line.trim().startsWith('console.log(') ? undefined : line;\n     * });\n     * ```\n     */\n    updateFileLines(\n        relativePath: string,\n        predicate: (line: string, lineNumber: number) => string | null | undefined,\n        options?: FileEditOptions\n    ): Promise<number>;\n\n    /**\n     * Gets a diff between the current file content and a new version.\n     * \n     * @param {string} relativePath - The path to the file\n     * @param {string | Buffer} newContent - The new content to compare against\n     * @param {DiffOptions} [options] - Options for the diff generation\n     * @returns {Promise<string>} A promise that resolves with a diff string in the specified format\n     * @throws {Error} If the file cannot be read or is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * // Get unified diff\n     * const diff = await fsManager.getFileDiff('src/utils/helpers.ts', newContent);\n     * \n     * // Get JSON diff\n     * const jsonDiff = await fsManager.getFileDiff('config.json', newConfig, {\n     *   format: DiffFormat.Json,\n     *   ignoreWhitespace: true\n     * });\n     * \n     * // Show diff in VSCode editor\n     * await fsManager.getFileDiff('file.txt', newContent, {\n     *   showInEditor: true,\n     *   title: 'Preview Changes',\n     *   viewColumn: ViewColumn.Beside\n     * });\n     * ```\n     */\n    getFileDiff(\n        relativePath: string,\n        newContent: string | Buffer,\n        options?: DiffOptions\n    ): Promise<string>;\n\n    /**\n     * Gets a diff between two versions of a file.\n     * \n     * @param {string | Buffer} oldContent - The old content to compare from\n     * @param {string | Buffer} newContent - The new content to compare to\n     * @param {DiffOptions} [options] - Options for the diff generation\n     * @returns {Promise<string>} A promise that resolves with a diff string in the specified format\n     * \n     * @example\n     * ```typescript\n     * // Get diff as string\n     * const diff = await fsManager.diffContent(oldContent, newContent, {\n     *   format: DiffFormat.SideBySide,\n     *   contextLines: 5\n     * });\n     * \n     * // Show diff in VSCode editor\n     * await fsManager.diffContent(oldContent, newContent, {\n     *   showInEditor: true,\n     *   title: 'Compare Versions',\n     *   viewColumn: ViewColumn.Beside\n     * });\n     * ```\n     */\n    diffContent(\n        oldContent: string | Buffer,\n        newContent: string | Buffer,\n        options?: DiffOptions\n    ): Promise<string>;\n\n    /**\n     * Applies a diff to a file.\n     * \n     * @param {string} relativePath - The path to the file to patch\n     * @param {string} diff - The diff to apply (format is auto-detected)\n     * @param {FileEditOptions} [options] - Options for the patch operation\n     * @returns {Promise<{success: boolean, conflicts: string[]}>} A promise that resolves with the result of the patch operation\n     * @throws {Error} If the patch cannot be applied or the file is outside the workspace\n     * \n     * @example\n     * ```typescript\n     * // Apply diff directly\n     * const result = await fsManager.applyDiff('src/utils/helpers.ts', diffString, {\n     *   backup: true,\n     *   ignoreWhitespace: true\n     * });\n     * \n     * // Show preview before applying\n     * const preview = await fsManager.getFileDiff('file.txt', newContent, {\n     *   showInEditor: true,\n     *   title: 'Review Changes Before Applying'\n     * });\n     * \n     * // After user confirms, apply the changes\n     * const applyResult = await fsManager.applyDiff('file.txt', diffString, {\n     *   backup: true\n     * });\n     * \n     * if (!applyResult.success) {\n     *   console.warn('Conflicts found:', applyResult.conflicts);\n     * }\n     * ```\n     */\n    applyDiff(\n        relativePath: string,\n        diff: string,\n        options?: FileEditOptions\n    ): Promise<{ success: boolean; conflicts: string[] }>;\n\n    /**\n     * Creates a patch file from changes between two versions of a file.\n     * \n     * @param {string} relativePath - The path to the file\n     * @param {string | Buffer} oldContent - The old content\n     * @param {string | Buffer} newContent - The new content\n     * @param {string} outputPath - The path to save the patch file\n     * @param {DiffOptions} [options] - Options for the patch generation\n     * @returns {Promise<void>} A promise that resolves when the patch file is created\n     * \n     * @example\n     * ```typescript\n     * await fsManager.createPatchFile(\n     *   'src/utils/helpers.ts',\n     *   oldContent,\n     *   newContent,\n     *   'patches/helpers-fix.patch',\n     *   { format: DiffFormat.Git }\n     * );\n     * ```\n     */\n    createPatchFile(\n        relativePath: string,\n        oldContent: string | Buffer,\n        newContent: string | Buffer,\n        outputPath: string,\n        options?: DiffOptions\n    ): Promise<void>;\n\n    /**\n     * Applies a patch file to a file.\n     * \n     * @param {string} relativePath - The path to the file to patch\n     * @param {string} patchPath - The path to the patch file\n     * @param {FileEditOptions} [options] - Options for the patch application\n     * @returns {Promise<{success: boolean, conflicts: string[]}>} A promise that resolves with the result of the patch operation\n     * \n     * @example\n     * ```typescript\n     * const result = await fsManager.applyPatchFile(\n     *   'src/utils/helpers.ts',\n     *   'patches/helpers-fix.patch',\n     *   { backup: true }\n     * );\n     * ```\n     */\n    applyPatchFile(\n        relativePath: string,\n        patchPath: string,\n        options?: FileEditOptions\n    ): Promise<{ success: boolean; conflicts: string[] }>;\n}\n\n/**\n * @deprecated Use InteractiveSession from './pseudoTerminal' instead\n */\nexport interface IInteractiveSession extends vscode.Disposable {\n    /**\n     * A unique identifier for this terminal session.\n     */\n    readonly id: string;\n\n    /**\n     * An event that fires when data is received from the terminal.\n     */\n    readonly onData: Event<string>;\n\n    /**\n     * An event that fires when the terminal process exits.\n     * The event parameter is the exit code of the process.\n     */\n    readonly onExit: Event<number>;\n\n    /**\n     * Writes data to the terminal's input stream.\n     * \n     * @param {string} data - The data to write to the terminal\n     * @returns {Promise<void>} A promise that resolves when the data has been written\n     * @throws {Error} If the terminal is not available or the write operation fails\n     * \n     * @example\n     * ```typescript\n     * await terminal.write('ls -la\\n');\n     * ```\n     */\n    write(data: string): Promise<void>;\n\n    /**\n     * Resizes the terminal to the specified dimensions.\n     * \n     * @param {number} cols - The number of columns (width) in characters\n     * @param {number} rows - The number of rows (height) in characters\n     * @returns {Promise<void>} A promise that resolves when the resize is complete\n     * @throws {Error} If the terminal doesn't support resizing or the operation fails\n     * \n     * @example\n     * ```typescript\n     * await terminal.resize(120, 40);\n     * ```\n     */\n    resize(cols: number, rows: number): Promise<void>;\n}\n\n/**\n * The single, unified service for all terminal operations within the extension.\n * This service provides a high-level API for executing commands, streaming output,\n * and managing interactive terminal sessions in a cross-platform manner.\n * \n * @deprecated Use InteractiveSession and PseudoTerminal from './pseudoTerminal' directly for new code\n */\nexport interface ITerminalService {\n    /**\n     * Executes a command in a terminal and returns the result.\n     * \n     * @param {string} command - The command to execute\n     * @param {string[]} args - Array of command-line arguments\n     * @param {Object} options - Execution options\n     * @param {string} [options.cwd] - The current working directory for the command\n     * @param {CancellationToken} options.cancellationToken - Token to cancel the operation\n     * @returns {Promise<CommandResult>} A promise that resolves with the command result\n     * @throws {TerminalError} If the command execution fails\n     * \n     * @example\n     * ```typescript\n     * const result = await terminalService.execute('git', ['status'], {\n     *   cwd: '/path/to/repo',\n     *   cancellationToken: cancellationToken\n     * });\n     * ```\n     */\n    execute(\n        command: string,\n        args: string[],\n        options: {\n            cwd?: string;\n            cancellationToken: CancellationToken\n        }\n    ): Promise<CommandResult>;\n\n    /**\n     * Streams the output of a command in real-time.\n     * \n     * @param {string} command - The command to execute\n     * @param {string[]} args - Array of command-line arguments\n     * @param {Object} options - Stream options\n     * @param {string} [options.cwd] - The current working directory for the command\n     * @param {(data: string) => void} options.onData - Callback for receiving output data\n     * @param {CancellationToken} options.cancellationToken - Token to cancel the operation\n     * @returns {Promise<{ exitCode: number }>} A promise that resolves with the exit code when complete\n     * \n     * @example\n     * ```typescript\n     * await terminalService.stream('npm', ['install'], {\n     *   cwd: projectPath,\n     *   onData: data => console.log(data),\n     *   cancellationToken\n     * });\n     * ```\n     */\n    stream(\n        command: string,\n        args: string[],\n        options: {\n            cwd?: string;\n            onData: (data: string) => void;\n            cancellationToken: CancellationToken\n        }\n    ): Promise<{ exitCode: number }>;\n\n    /**\n     * Creates a new interactive terminal session.\n     * \n     * @param {Object} [options] - Session options\n     * @param {string} [options.cwd] - The initial working directory for the terminal\n     * @returns {Promise<IInteractiveSession>} A promise that resolves with the new terminal session\n     * \n     * @example\n     * ```typescript\n     * const session = await terminalService.createSession({ cwd: '/path/to/start' });\n     * session.onData(data => console.log('Terminal output:', data));\n     * await session.write('echo Hello World\\n');\n     * ```\n     */\n    createSession(options?: { cwd?: string }): Promise<IInteractiveSession>;\n}\n\n/**\n * A secure service for interacting with the system clipboard in a cross-platform manner.\n * This interface provides methods for reading and writing both text and image data\n * to/from the system clipboard, with proper error handling and type safety.\n */\nexport interface IClipboardService {\n    /**\n     * Reads plain text from the system clipboard.\n     * \n     * @returns {Promise<string>} A promise that resolves with the text content\n     * @throws {Error} If reading from the clipboard fails or if the clipboard doesn't contain text\n     * \n     * @example\n     * ```typescript\n     * try {\n     *   const text = await clipboardService.readText();\n     *   console.log('Clipboard text:', text);\n     * } catch (error) {\n     *   console.error('Failed to read from clipboard:', error);\n     * }\n     * ```\n     */\n    readText(): Promise<string>;\n\n    /**\n     * Reads an image from the clipboard if available.\n     * \n     * @returns {Promise<MultimodalData | null>} A promise that resolves with the image data\n     *         or null if the clipboard doesn't contain an image\n     * @throws {Error} If reading from the clipboard fails or if the image format is unsupported\n     * \n     * @example\n     * ```typescript\n     * try {\n     *   const imageData = await clipboardService.readImage();\n     *   if (imageData) {\n     *     console.log(`Image data (${imageData.mimeType}):`, imageData.base64Data.substring(0, 50) + '...');\n     *   } else {\n     *     console.log('No image in clipboard');\n     *   }\n     * } catch (error) {\n     *   console.error('Failed to read image from clipboard:', error);\n     * }\n     * ```\n     */\n    readImage(): Promise<MultimodalData | null>;\n\n    /**\n     * Writes text to the system clipboard.\n     * \n     * @param {string} text - The text to write to the clipboard\n     * @returns {Promise<void>} A promise that resolves when the operation is complete\n     * @throws {Error} If writing to the clipboard fails\n     * \n     * @example\n     * ```typescript\n     * try {\n     *   await clipboardService.writeText('Text to copy');\n     *   console.log('Text copied to clipboard');\n     * } catch (error) {\n     *   console.error('Failed to write to clipboard:', error);\n     * }\n     * ```\n     */\n    writeText(text: string): Promise<void>;\n}\n\n/**\n * The core interface for interacting with Large Language Models (LLMs).\n * This defines a standard contract for generating text responses from language models,\n * supporting both streaming and non-streaming use cases.\n */\nexport interface ILanguageModel {\n    /**\n     * Generates a response from the language model based on the provided prompt.\n     * \n     * @param {LanguageModelPrompt} prompt - The input prompt for the language model\n     * @param {Object} options - Generation options\n     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation\n     * @param {boolean} options.jsonOutput - Whether to force JSON output format\n     * @returns {Promise<string>} A promise that resolves with the generated text\n     * \n     * @example\n     * ```typescript\n     * const prompt = [\n     *   { role: 'system', content: 'You are a helpful assistant.' },\n     *   { role: 'user', content: 'Tell me about TypeScript' }\n     * ];\n     * \n     * const response = await languageModel.generateResponse(prompt, {\n     *   cancellationToken,\n     *   jsonOutput: false\n     * });\n     * ```\n     * \n     * @throws {Error} If the generation fails or is cancelled\n     */\n    generateResponse(\n        prompt: LanguageModelPrompt,\n        options: {\n            cancellationToken: CancellationToken;\n            jsonOutput: boolean\n        }\n    ): Promise<string>;\n}\n\n/**\n * A service that manages and provides access to the currently active language model.\n * This acts as a facade that can be used to switch between different LLM providers\n * or configurations at runtime, providing a single point of access to the active model.\n */\nexport interface ILanguageModelProvider {\n    /**\n     * Initializes the language model provider with required services.\n     * \n     * @param {IServiceContainer} services - The service container providing access to other services\n     * @returns {void}\n     * \n     * @example\n     * ```typescript\n     * // During extension activation\n     * languageModelProvider.initialize(serviceContainer);\n     * ```\n     */\n    initialize(services: IServiceContainer): void;\n\n    /**\n     * Retrieves the currently active language model instance.\n     * \n     * @returns {ILanguageModel} The active language model instance\n     * @throws {Error} If no language model is available or initialized\n     * \n     * @example\n     * ```typescript\n     * const model = languageModelProvider.getActiveModel();\n     * const response = await model.generateResponse(prompt, { options });\n     * '''\n     */\n    getActiveModel(): ILanguageModel;\n}\n\n/**\n * Manages checking and requesting user consent for sensitive operations.\n * This service handles permission requests in a user-friendly way, ensuring\n * that users understand and approve potentially sensitive actions.\n */\nexport interface IPermissionManager {\n    /**\n     * Initializes the permission manager with the extension context.\n     * \n     * @param {ExtensionContext} context - The extension context\n     * @returns {void}\n     * \n     * @example\n     * ```typescript\n     * // During extension activation\n     * permissionManager.initialize(context);\n     * ```\n     */\n    initialize(context: ExtensionContext): void;\n\n    /**\n     * Requests one or more permissions from the user with a clear rationale.\n     * \n     * @param {readonly ToolPermission[]} permissions - Array of permissions to request\n     * @param {string} rationale - Explanation of why these permissions are needed\n     * @returns {Promise<boolean>} A promise that resolves to true if all permissions are granted\n     * \n     * @example\n     * ```typescript\n     * const granted = await permissionManager.requestPermissions(\n     *   [ToolPermission.FileSystemRead, ToolPermission.ClipboardWrite],\n     *   'We need these permissions to save the generated code to your project and copy it to the clipboard.'\n     * );\n     * \n     * if (!granted) {\n     *   throw new Error('Operation cancelled: Required permissions not granted');\n     * }\n     * ```\n     */\n    requestPermissions(permissions: readonly ToolPermission[], rationale: string): Promise<boolean>;\n}\n\n/**\n * A centralized registry for discovering, managing, and accessing tools within the extension.\n * This registry maintains a collection of available tools and provides methods to look them up\n * by name or retrieve all registered tools.\n */\nexport interface IToolRegistry {\n    /**\n     * Initializes the tool registry with the provided service container.\n     * This method should be called during extension activation to set up the registry.\n     * \n     * @param {IServiceContainer} services - The service container providing access to other services\n     * @returns {Promise<void>} A promise that resolves when initialization is complete\n     * \n     * @example\n     * ```typescript\n     * // During extension activation\n     * await toolRegistry.initialize(serviceContainer);\n     * ```\n     */\n    initialize(services: IServiceContainer): Promise<void>;\n\n    /**\n     * Retrieves a tool by its name.\n     * \n     * @param {string} toolName - The name of the tool to retrieve\n     * @returns {ITool | undefined} The tool instance if found, otherwise undefined\n     * \n     * @example\n     * ```typescript\n     * const tool = toolRegistry.getTool('fileSystem');\n     * if (tool) {\n     *   // Use the tool\n     * }\n     * ```\n     */\n    getTool(toolName: string): ITool | undefined;\n\n    /**\n     * Retrieves all registered tools.\n     * \n     * @returns {readonly ITool[]} An array of all registered tools\n     * \n     * @example\n     * ```typescript\n     * const allTools = toolRegistry.getAllTools();\n     * for (const tool of allTools) {\n     *   console.log(`Found tool: ${tool.name}`);\n     * }\n     * ```\n     */\n    getAllTools(): readonly ITool[];\n}\n\n/**\n * Provides user interface interaction capabilities for tools within the VS Code environment.\n * This interface abstracts common UI operations, allowing tools to present information,\n * request input, and show progress without directly depending on VS Code's UI APIs.\n */\nexport interface IToolUIProvider {\n    /**\n     * Shows a progress indicator in the VS Code UI while executing the provided work.\n     * \n     * @template T - The type of the result returned by the work function\n     * @param {string} title - The title to display in the progress notification\n     * @param {(progress: Progress<{ message?: string }>, token: CancellationToken) => Promise<T>} work - The async function to execute with progress tracking\n     * @returns {Promise<T>} A promise that resolves with the result of the work function\n     * \n     * @example\n     * ```typescript\n     * // Show progress while performing a long-running operation\n     * const result = await uiProvider.showProgress('Processing files...', async (progress, token) => {\n     *   progress.report({ message: 'Starting processing...' });\n     *   // Perform work...\n     *   progress.report({ message: 'Almost done...' });\n     *   return await someAsyncOperation();\n     * });\n     * ```\n     */\n    showProgress<T>(\n        title: string,\n        work: (progress: Progress<{ message?: string }>, token: CancellationToken) => Promise<T>\n    ): Promise<T>;\n\n    /**\n     * Shows an informational message to the user.\n     * \n     * @param {string} message - The message to display\n     * \n     * @example\n     * ```typescript\n     * uiProvider.showInformationMessage('Operation completed successfully');\n     * ```\n     */\n    showInformationMessage(message: string): void;\n\n    /**\n     * Shows a warning message to the user.\n     * \n     * @param {string} message - The warning message to display\n     * \n     * @example\n     * ```typescript\n     * uiProvider.showWarningMessage('This operation may take longer than expected');\n     * ```\n     */\n    showWarningMessage(message: string): void;\n\n    /**\n     * Shows an error message to the user.\n     * \n     * @param {string | Error} message - The error message or Error object to display\n     * \n     * @example\n     * ```typescript\n     * try {\n     *   // Some operation that might fail\n     * } catch (error) {\n     *   uiProvider.showErrorMessage(error);\n     * }\n     * ```\n     */\n    showErrorMessage(message: string | Error): void;\n}\n\n/** Metadata for logging operations */\nexport interface LogMetadata {\n    [key: string]: unknown;\n}\n\n/**\n * A centralized container for all singleton services used throughout the extension.\n * This container facilitates clean dependency injection, making services easily accessible\n * while maintaining loose coupling between components.\n * \n * @example\n * ```typescript\n * // Get a service from the container\n * const fs = services.get<IFileSystemManager>('fileSystem');\n * \n * // Check if a service is available\n * if (services.has('languageModel')) {\n *   const model = services.get<ILanguageModel>('languageModel');\n *   // Use the model...\n * }\n * ```\n */\nexport interface IServiceContainer {\n    readonly logger: ILogger;\n    readonly ui: IToolUIProvider;\n    readonly permissionManager: IPermissionManager;\n    readonly toolRegistry: IToolRegistry;\n    readonly memoryManager: IMemoryOperations;\n    readonly workspace: IWorkspaceKnowledge;\n    readonly fileSystem: IFileSystemManager;\n    readonly terminalService: ITerminalService;\n    readonly clipboard: IClipboardService;\n    readonly llmProvider: ILanguageModelProvider;\n}\n\n/**\n * Permission constants defining the specific capabilities that tools may request access to.\n *\n * These permissions control access to sensitive system resources and operations.\n * Tools must declare their required permissions, and users are prompted to grant them\n * before the tool can perform restricted operations.\n *\n * @constant\n * @type {readonly string[]}\n *\n * @example\n * ```typescript\n * // A tool requesting file system write access\n * public getPermissions(actionName: string): readonly ToolPermission[] {\n *   if (actionName === 'writeFile') {\n *     return ['workspace:write'];\n *   }\n *   return [];\n * }\n * ```\n */\nexport const TOOL_PERMISSIONS = [\n    'workspace:read',           // Read access to workspace files and directories\n    'workspace:write',          // Write access to workspace files and directories\n    'shell:execute',            // Execute shell commands (governs execute() and stream())\n    'terminal:interactive_session', // Create interactive terminal sessions (governs createSession())\n    'network:unrestricted',     // Make unrestricted network requests\n    'clipboard:read',           // Read from system clipboard\n] as const;\n\n/**\n * A specific tool permission type derived from the TOOL_PERMISSIONS constant.\n * This ensures type safety when working with permissions throughout the framework.\n *\n * @typedef {typeof TOOL_PERMISSIONS[number]} ToolPermission\n */\nexport type ToolPermission = typeof TOOL_PERMISSIONS[number];\n\n/**\n * The detailed result of an individual tool action execution.\n *\n * This interface captures all aspects of a tool execution, including success/failure status,\n * output data, error information, and metadata about the execution for auditing and debugging.\n *\n * @interface ToolResult\n *\n * @property {string} executionId - Unique identifier for this specific tool execution\n * @property {string} toolName - Name of the tool that was executed\n * @property {string} actionName - Name of the action that was performed\n * @property {'success' | 'failure' | 'cancelled'} status - Final status of the execution\n * @property {unknown} [output] - Output data returned by the tool action (only present on success)\n * @property {object} [error] - Error information (only present on failure/cancellation)\n * @property {string} error.message - Human-readable error message\n * @property {string} [error.code] - Machine-readable error code for programmatic handling\n * @property {string} [error.stack] - Error stack trace for debugging\n * @property {Record<string, unknown>} [error.details] - Additional error context and metadata\n * @property {string[]} [memoriesCreated] - IDs of memories created during execution for learning\n *\n * @example\n * ```typescript\n * // Successful execution result\n * {\n *   executionId: \"exec-123\",\n *   toolName: \"fileSystem\",\n *   actionName: \"readFile\",\n *   status: \"success\",\n *   output: { content: \"file contents...\" },\n *   memoriesCreated: [\"mem-456\"]\n * }\n *\n * // Failed execution result\n * {\n *   executionId: \"exec-789\",\n *   toolName: \"terminal\",\n *   actionName: \"execute\",\n *   status: \"failure\",\n *   error: {\n *     message: \"Command failed with exit code 1\",\n *     code: \"E_COMMAND_FAILED\",\n *     details: { exitCode: 1, command: \"invalid-command\" }\n *   }\n * }\n * ```\n */\n/**\n * Updates a tool operation memory with execution results\n * @param memory The memory record to update\n * @param result The execution result\n * @param metrics Optional performance metrics\n * @returns The updated memory record\n */\n/**\n * Enhanced terminal session that integrates with tool memory management\n */\nexport class ToolTerminalSession extends InteractiveSession {\n    private readonly _terminalState: TerminalState;\n    private readonly _terminalStats: TerminalStats;\n    private _commandHistory: string[];\n    private _dimensions: { columns: number; rows: number };\n    private _isConnected = false;\n    private _isReady = false;\n    private _isBusy = false;\n    private _startTime: number;\n    private _cwd: string;\n\n    getTerminalState(): TerminalState {\n        return this._terminalState;\n    }\n\n    constructor(config: TerminalConfig) {\n        super(config);\n        this._cwd = config.cwd || process.cwd();\n        this._commandHistory = [];\n        this._dimensions = { columns: 120, rows: 30 };\n        this._startTime = Date.now();\n        \n        this._terminalState = {\n            isConnected: false,\n            isReady: false,\n            currentDirectory: this._cwd,\n            shellType: config.shell || 'powershell',\n            dimensions: this._dimensions,\n            lastCommand: '',\n            commandHistory: [],\n            isBusy: false\n        };\n\n        this._terminalStats = {\n            totalCommands: 0,\n            totalOutputBytes: 0,\n            averageResponseTime: 0,\n            uptimeSeconds: 0,\n            errorCount: 0,\n            resizeCount: 0\n        };\n    }\n\n    protected getState(): TerminalState {\n        this._terminalState.isConnected = this._isConnected;\n        this._terminalState.isReady = this._isReady;\n        this._terminalState.currentDirectory = this._cwd;\n        this._terminalState.lastCommand = this._commandHistory[this._commandHistory.length - 1] || '';\n        this._terminalState.commandHistory = [...this._commandHistory];\n        this._terminalState.isBusy = this._isBusy;\n        return { ...this._terminalState };\n    }\n\n    protected getStats(): TerminalStats {\n        this._terminalStats.uptimeSeconds = Math.floor((Date.now() - this._startTime) / 1000);\n        return { ...this._terminalStats };\n    }\n\n    createTerminalProcess(): ITerminalProcess {\n        const process: ITerminalProcess = {\n            id: this.id,\n            config: this.config,\n            state: this.getState(),\n            stats: this.getStats(),\n            events: this.events,\n            initialize: async () => {\n                this._isConnected = true;\n                this._isReady = true;\n            },\n            enableLogging: () => Promise.resolve(),\n            disableLogging: () => Promise.resolve(),\n            write: (data: string) => this.write(data),\n            writeLn: (data: string) => this.writeLn(data),\n            executeCommand: (cmd: string, opts?: { \n                timeout?: number;\n                expectPrompt?: boolean;\n                background?: boolean;\n                workingDirectory?: string;\n            }) => this.executeCommand(cmd, opts),\n            executeCommandAdvanced: async (cmd: string, opts?: {\n                timeout?: number;\n                expectPrompt?: boolean;\n                background?: boolean;\n                workingDirectory?: string;\n            }) => {\n                const startTime = Date.now();\n                let output = '';\n                let success = true;\n                let error: string | undefined;\n                \n                try {\n                    output = await this.executeCommand(cmd, opts);\n                } catch (e) {\n                    success = false;\n                    error = e instanceof Error ? e.message : String(e);\n                }\n                \n                return {\n                    command: cmd,\n                    output,\n                    exitCode: success ? 0 : 1,\n                    duration: Date.now() - startTime,\n                    success,\n                    error,\n                    background: opts?.background || false,\n                    completedAt: new Date()\n                };\n            },\n            resize: (cols: number, rows: number) => {\n                this._dimensions = { columns: cols, rows };\n                this._terminalStats.resizeCount++;\n                return Promise.resolve();\n            },\n            clear: () => this.clear(),\n            reset: () => this.reset(),\n            sendSignal: (signal: string) => this.sendSignal(signal),\n            getWorkingDirectory: () => Promise.resolve(this._cwd),\n            setWorkingDirectory: (path: string) => {\n                this._cwd = path;\n                return Promise.resolve();\n            },\n            getCommandHistory: () => [...this._commandHistory],\n            clearCommandHistory: () => {\n                this._commandHistory = [];\n            },\n            dispose: () => this.dispose()\n        };\n        return process;\n    }\n\n    createActionRegistry(): TerminalActions {\n        return new TerminalActions(this);\n    }\n\n    override async executeCommand(\n        command: string,\n        options?: { \n            timeout?: number;\n            expectPrompt?: boolean;\n            background?: boolean;\n            workingDirectory?: string;\n        }\n    ): Promise<string> {\n        this._isBusy = true;\n        this._commandHistory.push(command);\n        const startTime = Date.now();\n\n        try {\n            if (options?.workingDirectory) {\n                this._cwd = options.workingDirectory;\n            }\n\n            const output = await super.executeCommand(command, options);\n            const duration = Date.now() - startTime;\n            \n            this._terminalStats.totalCommands++;\n            this._terminalStats.totalOutputBytes += output.length;\n            this._terminalStats.averageResponseTime = this._terminalStats.averageResponseTime === 0 \n                ? duration \n                : (this._terminalStats.averageResponseTime + duration) / 2;\n            this._isBusy = false;\n\n            return output;\n        } catch (error) {\n            const duration = Date.now() - startTime;\n            this._terminalStats.errorCount++;\n            this._terminalStats.totalCommands++;\n            this._terminalStats.averageResponseTime = this._terminalStats.averageResponseTime === 0\n                ? duration\n                : (this._terminalStats.averageResponseTime + duration) / 2;\n            this._isBusy = false;\n\n            throw error;\n        }\n    }\n}\n\n/**\n * Base interface for tools that need terminal interaction capabilities\n */\nexport interface ITerminalTool {\n    /** The terminal session */\n    readonly session: ToolTerminalSession;\n    /** Terminal state */\n    readonly terminalState: TerminalState;\n    /** Terminal process interface */\n    readonly process: ITerminalProcess;\n    /** Terminal configuration */\n    readonly config: TerminalConfig;\n    /** Available terminal actions */\n    readonly actions: TerminalActions;\n}\n\n/**\n * Factory for creating tool terminal sessions\n */\nexport class ToolTerminalFactory {\n    private static _instance: ToolTerminalFactory;\n    private _config: TerminalConfig;\n\n    private constructor() {\n        const platform = os.platform();\n        this._config = {\n            shell: platform === 'win32' ? 'powershell.exe' : '/bin/bash',\n            cwd: process.cwd(),\n            env: process.env\n        };\n    }\n\n    public static get instance(): ToolTerminalFactory {\n        if (!ToolTerminalFactory._instance) {\n            ToolTerminalFactory._instance = new ToolTerminalFactory();\n        }\n        return ToolTerminalFactory._instance;\n    }\n\n    /**\n     * Creates a new terminal session for a tool\n     */\n    public createTerminalSession(toolId: string): ToolTerminalSession {\n        const config: TerminalConfig = {\n            ...this._config,\n            name: `Tool-${toolId}`\n        };\n        return new ToolTerminalSession(config);\n    }\n}\n\n/**\n * Mixin class to add terminal capabilities to a tool\n * @template TBase - Type of the base class\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withTerminalCapabilities<TBase extends new (...args: any[]) => { id: string }>(Base: TBase) {\n    class TerminalCapabilityClass extends Base implements ITerminalTool {\n        private _session: ToolTerminalSession;\n        private _process: ITerminalProcess;\n        private _actions: TerminalActions;\n\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        constructor(...args: any[]) {\n            super(...args);\n            this._session = ToolTerminalFactory.instance.createTerminalSession(this.id);\n            this._process = this._session.createTerminalProcess();\n            this._actions = this._session.createActionRegistry();\n        }\n\n        get session(): ToolTerminalSession {\n            return this._session;\n        }\n\n        get terminalState(): TerminalState {\n            return this._session.getTerminalState();\n        }\n\n        get process(): ITerminalProcess {\n            return this._process;\n        }\n\n        get config(): TerminalConfig {\n            return this._session.config;\n        }\n\n        get actions(): TerminalActions {\n            return this._actions;\n        }\n\n        protected async dispose(): Promise<void> {\n            await this._session.dispose();\n        }\n    }\n\n    return TerminalCapabilityClass;\n}\n\nexport function updateToolOperationMemory(\n    memory: IToolOperationMemory,\n    result: {\n        success: boolean;\n        output?: unknown;\n        error?: { message: string; code?: string; stack?: string; };\n    },\n    metrics?: {\n        cpuUsage?: number;\n        memoryUsage?: number;\n        apiCalls?: number;\n        fileOperations?: number;\n    }\n): IToolOperationMemory {\n    const endTime = Date.now();\n    \n    return {\n        ...memory,\n        duration: endTime - memory.state.startTime,\n        output: result.output,\n        error: result.error,\n        state: {\n            status: result.success ? 'completed' : 'failed',\n            startTime: memory.state.startTime,\n            endTime,\n            progress: 100\n        },\n        metadata: {\n            ...memory.metadata,\n            success: result.success\n        },\n        metrics: metrics ? {\n            ...memory.metrics,\n            ...metrics\n        } : memory.metrics\n    };\n}\n\n/**\n * Creates a new tool operation memory record\n * @param toolName Name of the tool\n * @param operation Name of the operation/action\n * @param input Input parameters used\n * @param context Optional workspace context\n * @returns A new tool operation memory record\n */\nexport function createToolOperationMemory(\n    toolName: string,\n    operation: string,\n    input: Record<string, unknown>,\n    context?: {\n        workspaceContext?: string;\n        affectedFiles?: string[];\n        importance?: number;\n        tags?: string[];\n    }\n): IToolOperationMemory {\n    return {\n        id: randomUUID(),\n        toolName,\n        operation,\n        timestamp: Date.now(),\n        input,\n        duration: 0,\n        executionId: randomUUID(),\n        state: {\n            status: 'pending',\n            startTime: Date.now()\n        },\n        metadata: {\n            source: 'tool' as MemorySource,\n            type: 'procedural' as MemoryType,\n            success: false,\n            workspaceContext: context?.workspaceContext,\n            affectedFiles: context?.affectedFiles,\n            importance: context?.importance,\n            tags: context?.tags\n        }\n    };\n}\n\nexport interface ToolResult {\n    readonly executionId: string;\n    readonly toolName: string;\n    readonly actionName: string;\n    readonly status: 'success' | 'failure' | 'cancelled';\n    readonly output?: unknown;\n    readonly error?: { readonly message: string; readonly code?: string; readonly stack?: string; readonly details?: Record<string, unknown> };\n    readonly memoriesCreated?: string[];\n    /** Memory record for this tool operation */\n    readonly memory?: IToolOperationMemory;\n    /** Whether this operation's memory should be preserved long-term */\n    readonly preserveMemory?: boolean;\n    /** Importance score for memory retention (0-1) */\n    readonly memoryImportance?: number;\n}\n\n/**\n * The master interface for defining AI Agent Tools.\n *\n * This interface defines the contract that all tools must implement to be usable\n * within the framework. It provides the essential methods for tool discovery,\n * execution, permission management, and documentation.\n *\n * @interface ITool\n *\n * @property {string} name - Unique identifier for the tool\n * @property {string} description - Human-readable description of the tool's purpose\n * @property {string} version - Semantic version string (e.g., \"1.0.0\")\n * @property {string} category - Category for tool organization and discovery\n * @property {Readonly<Record<string, ToolActionDefinition>>} actions - Registry of available actions\n * @method invoke - Execute a specific action with input parameters and options\n * @method getPermissions - Return required permissions for a specific action\n * @method getDocumentation - Return comprehensive documentation in markdown format\n *\n * @example\n * ```typescript\n * class MyCustomTool implements ITool {\n *   public readonly name = 'myTool';\n *   public readonly description = 'A custom tool for specific operations';\n *   public readonly version = '1.0.0';\n *   public readonly category = 'Custom';\n *\n *   public readonly actions = {\n *     performAction: {\n *       description: 'Perform a specific action',\n *       inputSchema: z.object({ input: z.string() }),\n *       outputSchema: z.object({ result: z.string() })\n *     }\n *   } as const;\n *\n *   public async invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult> {\n *     // Implementation here\n *   }\n *\n *   public getPermissions(actionName: string): readonly ToolPermission[] {\n *     return ['workspace:read'];\n *   }\n *\n *   public getDocumentation(): string {\n *     return `# My Custom Tool\\n\\nProvides custom functionality...`;\n *   }\n * }\n * ```\n */\nexport interface ITool {\n    readonly name: string;\n    readonly description: string;\n    readonly version: string;\n    readonly category: string;\n    readonly actions: Readonly<Record<string, ToolActionDefinition>>;\n    invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult>;\n    getPermissions(actionName: string): readonly ToolPermission[];\n    getDocumentation(): string;\n}\n\n/**\n * Defines a single, named action within a tool.\n *\n * Each tool action has a clear definition including its purpose, input/output schemas,\n * and validation rules. This enables the framework to validate inputs, provide\n * documentation, and ensure type safety.\n *\n * @interface ToolActionDefinition\n *\n * @property {string} description - Human-readable description of what this action does\n * @property {z.ZodType<unknown>} inputSchema - Zod schema for validating input parameters\n * @property {z.ZodType<unknown>} outputSchema - Zod schema for validating output data\n *\n * @example\n * ```typescript\n * const readFileAction: ToolActionDefinition = {\n *   description: 'Read content from a file at the specified path',\n *   inputSchema: z.object({\n *     path: z.string().min(1, 'Path cannot be empty'),\n *     encoding: z.enum(['utf-8', 'ascii']).optional().default('utf-8')\n *   }),\n *   outputSchema: z.object({\n *     content: z.string(),\n *     size: z.number(),\n *     lastModified: z.date()\n *   })\n * };\n * ```\n */\nexport interface ToolActionDefinition {\n    readonly description: string;\n    readonly inputSchema: z.ZodType<unknown>;\n    readonly outputSchema: z.ZodType<unknown>;\n}\n\n/**\n * The set of options passed to a tool's `invoke` method.\n *\n * These options provide the execution context, services, and control parameters\n * that tools need to function properly within the framework.\n *\n * @interface ToolInvokeOptions\n *\n * @property {WorkflowContext} workflowContext - The current workflow execution context\n * @property {IServiceContainer} services - Container providing access to all framework services\n * @property {CancellationToken} cancellationToken - Token for cancelling the operation\n *\n * @example\n * ```typescript\n * const options: ToolInvokeOptions = {\n *   workflowContext: currentWorkflowContext,\n *   services: serviceContainer,\n *   cancellationToken: cancellationTokenSource.token\n * };\n *\n * const result = await tool.invoke('readFile', input, options);\n * ```\n */\nexport interface ToolInvokeOptions {\n    readonly workflowContext: WorkflowContext;\n    readonly services: IServiceContainer;\n    readonly cancellationToken: CancellationToken;\n}\n\n/**\n * Custom Error classes for the tool framework\n */\n\n/**\n * Error thrown when an operation is cancelled by the user or due to a cancellation token.\n * This error indicates that the operation was intentionally stopped before completion.\n *\n * @class OperationCancelledError\n * @extends Error\n */\nclass OperationCancelledError extends Error {\n    /**\n     * Creates a new OperationCancelledError instance.\n     * @param {string} [message='Operation cancelled.'] - The error message to display\n     */\n    constructor(m = 'Operation cancelled.') {\n        super(m);\n        this.name = 'OperationCancelledError';\n    }\n}\n\n/**\n * Error thrown when a user denies the required permissions for a tool operation.\n * This prevents unauthorized access to sensitive resources or operations.\n *\n * @class PermissionDeniedError\n * @extends Error\n */\nclass PermissionDeniedError extends Error {\n    /**\n     * Creates a new PermissionDeniedError instance.\n     * @param {string} message - The error message explaining which permissions were denied\n     */\n    constructor(m: string) {\n        super(m);\n        this.name = 'PermissionDeniedError';\n    }\n}\n\n/**\n * Error thrown when a tool action is not found or improperly defined in the tool's actions registry.\n * This indicates a configuration or implementation error in the tool definition.\n *\n * @class ToolDefinitionError\n * @extends Error\n */\nclass ToolDefinitionError extends Error {\n    /**\n     * Creates a new ToolDefinitionError instance.\n     * @param {string} message - The error message explaining the tool definition issue\n     */\n    constructor(m: string) {\n        super(m);\n        this.name = 'ToolDefinitionError';\n    }\n}\n\n/**\n * Error thrown when terminal operations fail due to system-level issues or configuration problems.\n * This includes failures in command execution, session creation, or terminal process management.\n *\n * @class TerminalError\n * @extends Error\n * @deprecated Use the error classes from './pseudoTerminal' instead\n */\nclass TerminalError extends Error {\n    /**\n     * Creates a new TerminalError instance.\n     * @param {string} message - The error message explaining the terminal operation failure\n     */\n    constructor(m: string) {\n        super(m);\n        this.name = 'TerminalError';\n    }\n}\n\n/**\n * Error thrown when workflow operations fail due to execution issues, invalid configurations,\n * or problems with step execution and orchestration.\n *\n * @class WorkflowError\n * @extends Error\n */\nclass WorkflowError extends Error {\n    /**\n     * Creates a new WorkflowError instance.\n     * @param {string} message - The error message explaining the workflow execution failure\n     */\n    constructor(m: string) {\n        super(m);\n        this.name = 'WorkflowError';\n    }\n}\n\n/**\n * Error thrown when attempting to use a feature or method that has not been implemented yet.\n * This serves as a placeholder for planned functionality that is not yet available.\n *\n * @class NotImplementedError\n * @extends Error\n */\nclass NotImplementedError extends Error {\n    /**\n     * Creates a new NotImplementedError instance.\n     * @param {string} message - The error message explaining what functionality is not yet implemented\n     */\n    constructor(m: string) {\n        super(m);\n        this.name = 'NotImplementedError';\n    }\n}\n// #endregion\n\n/**\n * Abstract base class for all AI Agent Tools.\n *\n * This class provides the foundation for implementing tools within the framework.\n * It handles common functionality such as permission management, input validation,\n * error handling, and execution orchestration. All concrete tool implementations\n * must extend this class and provide their specific business logic.\n *\n * @abstract\n * @class BaseTool\n * @implements {ITool}\n *\n * @example\n * ```typescript\n * export class FileSystemTool extends BaseTool {\n *   public readonly name = 'fileSystem';\n *   public readonly description = 'File system operations';\n *   public readonly category = 'System';\n *\n *   public readonly actions = {\n *     readFile: {\n *       description: 'Read content from a file',\n *       inputSchema: z.object({ path: z.string() }),\n *       outputSchema: z.object({ content: z.string() })\n *     }\n *   } as const;\n *\n *   protected async _execute(actionName: string, input: unknown, options: ToolInvokeOptions) {\n *     switch (actionName) {\n *       case 'readFile':\n *         const { path } = input as { path: string };\n *         const content = await fs.readFile(path, 'utf-8');\n *         return { output: { content } };\n *       default:\n *         throw new Error(`Unknown action: ${actionName}`);\n *     }\n *   }\n *\n *   public getDocumentation() {\n *     return `# File System Tool\n *\n * Provides basic file system operations for reading, writing, and managing files.\n *\n * ## Actions\n * - **readFile**: Read content from a file path\n * `;\n *   }\n * }\n * ```\n */\nexport abstract class BaseTool implements ITool {\n    /**\n     * The unique name identifier for this tool.\n     * Used for tool discovery and invocation.\n     */\n    public abstract readonly name: string;\n\n    /**\n     * Human-readable description of what this tool does.\n     * Should be concise but informative.\n     */\n    public abstract readonly description: string;\n\n    /**\n     * Version string for this tool implementation.\n     * Follows semantic versioning (e.g., \"1.0.0\").\n     */\n    public readonly version = '1.0.0';\n\n    /**\n     * Category this tool belongs to for organization and discovery.\n     * Examples: 'System', 'Development', 'AI', 'Utilities'\n     */\n    public abstract readonly category: string;\n\n    /**\n     * Registry of all actions supported by this tool.\n     * Each action defines its input/output schemas and description.\n     */\n    public abstract readonly actions: Readonly<Record<string, ToolActionDefinition>>;\n\n    /**\n     * Executes the specific business logic for a tool action.\n     * This method must be implemented by concrete tool classes.\n     *\n     * @protected\n     * @abstract\n     * @param {string} actionName - The name of the action to execute\n     * @param {unknown} validatedInput - The validated input data for the action\n     * @param {ToolInvokeOptions} options - Execution options including services and context\n     * @returns {Promise<{ output: unknown, memoriesCreated?: string[] }>} The execution result\n     */\n    protected abstract _execute(actionName: string, validatedInput: unknown, options: ToolInvokeOptions): Promise<{ output: unknown, memoriesCreated?: string[] }>;\n\n    /**\n     * Main entry point for tool invocation.\n     * Handles permission checking, input validation, execution, and error handling.\n     *\n     * @param {string} actionName - The name of the action to invoke\n     * @param {unknown} input - The input data for the action (will be validated)\n     * @param {ToolInvokeOptions} options - Execution options and context\n     * @returns {Promise<ToolResult>} The result of the tool execution\n     *\n     * @throws {ToolDefinitionError} If the requested action doesn't exist\n     * @throws {PermissionDeniedError} If required permissions are not granted\n     * @throws {OperationCancelledError} If the operation is cancelled\n     */\n    public async invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult> {\n        const executionId = randomUUID();\n        const { services, cancellationToken } = options;\n        const actionDef = this.actions[actionName];\n\n        try {\n            // Validate action exists\n            if (!actionDef) {\n                throw new ToolDefinitionError(`Action '${actionName}' not found in tool '${this.name}'.`);\n            }\n\n            // Check and request required permissions\n            const requiredPermissions = this.getPermissions(actionName);\n            if (requiredPermissions.length > 0) {\n                const rationale = `Tool '${this.name}' is requesting permissions to perform action '${actionName}'.`;\n                const hasPermission = await services.permissionManager.requestPermissions(requiredPermissions, rationale);\n                if (!hasPermission) {\n                    throw new PermissionDeniedError(`User denied required permissions: ${requiredPermissions.join(', ')}`);\n                }\n            }\n\n            // Check for cancellation before execution\n            if (cancellationToken.isCancellationRequested) {\n                throw new OperationCancelledError();\n            }\n\n            // Validate input against schema\n            const validatedInput = actionDef.inputSchema.parse(input);\n\n            // Execute the tool's business logic\n            const { output, memoriesCreated } = await this._execute(actionName, validatedInput, options);\n\n            // Validate output against schema\n            const validatedOutput = actionDef.outputSchema.parse(output);\n\n            // Return successful result\n            return {\n                executionId,\n                toolName: this.name,\n                actionName,\n                status: 'success',\n                output: validatedOutput,\n                memoriesCreated\n            };\n\n        } catch (error) {\n            // Handle different types of errors with appropriate structured error information\n            let structuredError: { message: string, code?: string, stack?: string, details?: Record<string, unknown> };\n\n            if (error instanceof ZodError) {\n                // Schema validation errors\n                structuredError = {\n                    message: 'Data validation failed',\n                    code: 'E_VALIDATION',\n                    details: error.flatten()\n                };\n            } else if (error instanceof PermissionDeniedError) {\n                // Permission-related errors\n                structuredError = {\n                    message: error.message,\n                    code: 'E_PERMISSION_DENIED'\n                };\n            } else if (error instanceof OperationCancelledError) {\n                // Cancellation returns cancelled status instead of failure\n                return {\n                    executionId,\n                    toolName: this.name,\n                    actionName,\n                    status: 'cancelled',\n                    error: { message: error.message, code: 'E_CANCELLED' }\n                };\n            } else if (error instanceof Error) {\n                // General execution errors\n                structuredError = {\n                    message: error.message,\n                    stack: error.stack,\n                    code: 'E_EXECUTION_ERROR'\n                };\n            } else {\n                // Unknown error types\n                structuredError = {\n                    message: 'An unknown error occurred',\n                    code: 'E_UNKNOWN'\n                };\n            }\n\n            // Log the error for debugging and monitoring\n            services.logger.error(structuredError.message, {\n                tool: this.name,\n                action: actionName,\n                error: structuredError\n            });\n\n            // Return failure result\n            return {\n                executionId,\n                toolName: this.name,\n                actionName,\n                status: 'failure',\n                error: structuredError\n            };\n        }\n    }\n\n    /**\n     * Returns the permissions required for a specific action.\n     * Override this method in concrete tool implementations to specify required permissions.\n     *\n     * @param {string} actionName - The name of the action to check permissions for\n     * @returns {readonly ToolPermission[]} Array of required permissions (empty by default)\n     *\n     * @example\n     * ```typescript\n     * public getPermissions(actionName: string): readonly ToolPermission[] {\n     *   switch (actionName) {\n     *     case 'writeFile':\n     *       return ['workspace:write'];\n     *     case 'runCommand':\n     *       return ['shell:execute'];\n     *     default:\n     *       return [];\n     *   }\n     * }\n     * ```\n     */\n    public getPermissions(actionName: string): readonly ToolPermission[] {\n        // Log the action name for debugging and audit purposes\n        console.log(`Getting permissions for action: ${actionName}`);\n        return [];\n    }\n\n    /**\n     * Returns comprehensive documentation for this tool.\n     * This should include usage examples, action descriptions, and any important notes.\n     *\n     * @abstract\n     * @returns {string} Markdown-formatted documentation string\n     */\n    public abstract getDocumentation(): string;\n}\n// #endregion\n\n// #region PART 3: Advanced Workflow Integration for Tools\n\n/**\n * A workflow step that executes a specific tool action.\n *\n * This class integrates tools into the advanced workflow system by implementing\n * the WorkflowStep interface. It handles input hydration using workflow context\n * and executes the specified tool action with proper error handling.\n *\n * @class ToolWorkflowStep\n * @implements {WorkflowStep}\n *\n * @example\n * ```typescript\n * const step = new ToolWorkflowStep(\n *   'read-file-step',\n *   'fileSystem',\n *   'readFile',\n *   { path: '{{inputs.filePath}}' },\n *   services,\n *   cancellationToken\n * );\n * ```\n */\nexport class ToolWorkflowStep implements WorkflowStep {\n    /** Unique identifier for this workflow step */\n    public readonly id: string;\n\n    /** Human-readable name for this step */\n    public readonly name: string;\n\n    /** Detailed description of what this step does */\n    public readonly description: string;\n\n    /** The execution function that performs the tool action */\n    public readonly execute: (context: WorkflowContext) => Promise<WorkflowStepResult>;\n\n    /** Array of next step IDs to execute after this step */\n    public readonly nextSteps: string[];\n\n    /** Whether this step has conditional logic */\n    public readonly isConditional?: boolean;\n\n    /** The name of the tool to execute */\n    private readonly toolName: string;\n\n    /** The name of the action to execute on the tool */\n    private readonly actionName: string;\n\n    /** The input parameters for the tool action (may contain template strings) */\n    private readonly input: Record<string, unknown>;\n\n    /** Service container providing access to all services */\n    private readonly services: IServiceContainer;\n\n    /** Cancellation token for cancelling the operation */\n    private readonly token: CancellationToken;\n\n    /**\n     * Creates a new ToolWorkflowStep instance.\n     *\n     * @param {string} id - Unique identifier for this step\n     * @param {string} toolName - Name of the tool to execute\n     * @param {string} actionName - Name of the action to execute\n     * @param {Record<string, unknown>} input - Input parameters (may contain template strings like {{variableName}})\n     * @param {IServiceContainer} services - Service container with access to all services\n     * @param {CancellationToken} token - Cancellation token for the operation\n     */\n    constructor(\n        id: string,\n        toolName: string,\n        actionName: string,\n        input: Record<string, unknown>,\n        services: IServiceContainer,\n        token: CancellationToken\n    ) {\n        this.id = id;\n        this.name = `Tool: ${toolName}.${actionName}`;\n        this.description = `Execute ${toolName}.${actionName}`;\n        this.nextSteps = [];\n        this.toolName = toolName;\n        this.actionName = actionName;\n        this.input = input;\n        this.services = services;\n        this.token = token;\n\n        // Define the execution function\n        this.execute = async (context: WorkflowContext): Promise<WorkflowStepResult> => {\n            // Retrieve the tool from the registry\n            const tool = this.services.toolRegistry.getTool(this.toolName);\n            if (!tool) {\n                const errorMsg = `Tool '${this.toolName}' not found.`;\n                return { success: false, error: errorMsg };\n            }\n\n            try {\n                // Hydrate input parameters using workflow context\n                const hydratedInput = this.hydrateInput(this.input, context);\n\n                // Execute the tool action\n                const result = await tool.invoke(this.actionName, hydratedInput, {\n                    workflowContext: context,\n                    services: this.services,\n                    cancellationToken: this.token\n                });\n\n                // Return the result\n                return {\n                    success: result.status === 'success',\n                    output: result.output,\n                    error: result.error ? result.error.message : undefined\n                };\n            } catch (error: unknown) {\n                // Handle any execution errors\n                const errorMsg = `Error executing tool '${this.toolName}.${this.actionName}': ${error instanceof Error ? error.message : String(error)}`;\n                return { success: false, error: errorMsg };\n            }\n        };\n    }\n\n    /**\n     * Hydrates input parameters by replacing template strings with actual values from the workflow context.\n     *\n     * Template strings use the format {{source.property}} where:\n     * - inputs: References workflow input values\n     * - outputs: References output values from previous steps\n     * - variables: References workflow variables\n     *\n     * @private\n     * @param {Record<string, unknown>} input - The input parameters with potential template strings\n     * @param {WorkflowContext} context - The workflow context containing values for hydration\n     * @returns {Record<string, unknown>} The hydrated input parameters\n     * @throws {WorkflowError} If a template string cannot be resolved\n     *\n     * @example\n     * ```typescript\n     * // Input: { path: '{{inputs.filePath}}', content: '{{outputs.readStep.content}}' }\n     * // Context: { inputs: { filePath: '/test.txt' }, outputs: { readStep: { content: 'Hello' } } }\n     * // Result: { path: '/test.txt', content: 'Hello' }\n     * ```\n     */\n    private hydrateInput(input: Record<string, unknown>, context: WorkflowContext): Record<string, unknown> {\n        const hydrated: Record<string, unknown> = {};\n\n        // Process each input parameter\n        for (const [key, value] of Object.entries(input)) {\n            if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {\n                // This is a template string that needs to be resolved\n                const path = value.slice(2, -2).trim().split('.');\n                const [source, ...propertyPath] = path;\n                let resolvedValue: unknown;\n\n                // Resolve based on the source type\n                if (source === 'inputs') {\n                    resolvedValue = context.inputs;\n                } else if (source === 'outputs') {\n                    resolvedValue = context.outputs;\n                } else if (source === 'variables') {\n                    resolvedValue = context.variables;\n                } else {\n                    // Try to find in context properties\n                    resolvedValue = (context as unknown as Record<string, unknown>)[source];\n                }\n\n                // Ensure the source exists\n                if (resolvedValue === undefined) {\n                    throw new WorkflowError(`Hydration failed: Source '${source}' not found in context.`);\n                }\n\n                // Navigate through the property path\n                for (const prop of propertyPath) {\n                    resolvedValue = (resolvedValue as Record<string, unknown>)?.[prop];\n                }\n\n                // Ensure the final path resolves to a value\n                if (resolvedValue === undefined) {\n                    throw new WorkflowError(`Hydration failed: Path '${value}' could not be resolved.`);\n                }\n\n                hydrated[key] = resolvedValue;\n            } else {\n                // Not a template string, use the value as-is\n                hydrated[key] = value;\n            }\n        }\n\n        return hydrated;\n    }\n}\n\n/**\n * Factory class for creating workflows that integrate tools into the advanced workflow system.\n *\n * This factory provides methods to create both complex multi-step workflows from execution plans\n * and simple single-tool workflows. It serves as the primary interface for converting tool\n * execution specifications into executable workflow instances.\n *\n * @class ToolWorkflowFactory\n *\n * @example\n * ```typescript\n * const factory = new ToolWorkflowFactory(services);\n *\n * // Create a simple single-tool workflow\n * const simpleWorkflow = factory.createSimpleToolWorkflow(\n *   'fileSystem',\n *   'readFile',\n *   { path: '/example.txt' }\n * );\n *\n * // Create a complex workflow from a plan\n * const complexWorkflow = factory.createWorkflowFromPlan({\n *   id: 'complex-workflow',\n *   name: 'Process Files',\n *   steps: [\n *     {\n *       id: 'read',\n *       tool_name: 'fileSystem',\n *       action_name: 'readFile',\n *       input: { path: '{{inputs.sourcePath}}' }\n *     },\n *     {\n *       id: 'process',\n *       tool_name: 'textProcessor',\n *       action_name: 'transform',\n *       input: { content: '{{outputs.read.content}}' }\n *     }\n *   ]\n * });\n * ```\n */\nexport class ToolWorkflowFactory {\n    /** Service container providing access to all framework services */\n    private readonly services: IServiceContainer;\n\n    /**\n     * Creates a new ToolWorkflowFactory instance.\n     *\n     * @param {IServiceContainer} services - The service container with all required services\n     */\n    constructor(services: IServiceContainer) {\n        this.services = services;\n    }\n\n    /**\n     * Creates a complex workflow from a detailed execution plan.\n     *\n     * This method takes a structured plan containing multiple steps and converts it into\n     * an executable workflow. Each step in the plan is transformed into a ToolWorkflowStep\n     * that can be executed as part of the workflow.\n     *\n     * @param {Object} plan - The execution plan for the workflow\n     * @param {string} plan.id - Unique identifier for the workflow\n     * @param {string} plan.name - Human-readable name for the workflow\n     * @param {Array} plan.steps - Array of step definitions\n     * @param {string} plan.steps[].id - Unique identifier for the step\n     * @param {string} plan.steps[].tool_name - Name of the tool to execute\n     * @param {string} plan.steps[].action_name - Name of the action to execute\n     * @param {Record<string, unknown>} plan.steps[].input - Input parameters (may contain template strings)\n     * @param {Array} [plan.inputs] - Optional workflow input definitions\n     * @param {string} plan.inputs[].id - Input identifier\n     * @param {string} plan.inputs[].name - Input display name\n     * @param {string} plan.inputs[].description - Input description\n     * @param {string} plan.inputs[].type - Input data type\n     * @param {boolean} plan.inputs[].required - Whether the input is required\n     * @param {unknown} [plan.inputs[].default] - Default value for the input\n     * @returns {Workflow} An executable workflow instance\n     *\n     * @example\n     * ```typescript\n     * const workflow = factory.createWorkflowFromPlan({\n     *   id: 'file-processing-workflow',\n     *   name: 'Process Source Files',\n     *   steps: [\n     *     {\n     *       id: 'read-source',\n     *       tool_name: 'fileSystem',\n     *       action_name: 'readFile',\n     *       input: { path: '{{inputs.sourcePath}}' }\n     *     },\n     *     {\n     *       id: 'validate-content',\n     *       tool_name: 'contentValidator',\n     *       action_name: 'validate',\n     *       input: { content: '{{outputs.read-source.content}}' }\n     *     }\n     *   ],\n     *   inputs: [\n     *     {\n     *       id: 'sourcePath',\n     *       name: 'Source File Path',\n     *       description: 'Path to the source file to process',\n     *       type: 'string',\n     *       required: true\n     *     }\n     *   ]\n     * });\n     * ```\n     */\n    public createWorkflowFromPlan(plan: {\n        id: string;\n        name: string;\n        steps: Array<{\n            id: string;\n            tool_name: string;\n            action_name: string;\n            input: Record<string, unknown>;\n        }>;\n        inputs?: Array<{\n            id: string;\n            name: string;\n            description: string;\n            type: 'string' | 'number' | 'boolean' | 'object' | 'array';\n            required: boolean;\n            default?: unknown;\n        }>;\n    }): Workflow {\n        // Convert plan steps into executable ToolWorkflowStep instances\n        const steps: WorkflowStep[] = plan.steps.map(step =>\n            new ToolWorkflowStep(\n                step.id,\n                step.tool_name,\n                step.action_name,\n                step.input,\n                this.services,\n                new vscode.CancellationTokenSource().token\n            )\n        );\n\n        // Create the workflow definition\n        const workflowDefinition: WorkflowDefinition = {\n            id: plan.id,\n            name: plan.name,\n            description: `Tool-based workflow: ${plan.name}`,\n            version: '1.0.0',\n            steps,\n            inputs: plan.inputs || [],\n            outputs: [],\n            startStepId: plan.steps[0]?.id || ''\n        };\n\n        return new Workflow(workflowDefinition);\n    }\n\n    /**\n     * Creates a simple workflow that executes a single tool action.\n     *\n     * This is a convenience method for creating straightforward workflows that only\n     * need to execute one tool action. It's useful for simple operations or as\n     * building blocks for more complex workflows.\n     *\n     * @param {string} toolName - Name of the tool to execute\n     * @param {string} actionName - Name of the action to execute on the tool\n     * @param {Record<string, unknown>} input - Input parameters for the action\n     * @param {string} [workflowId] - Optional custom ID for the workflow (auto-generated if not provided)\n     * @returns {Workflow} An executable workflow instance containing a single tool step\n     *\n     * @example\n     * ```typescript\n     * // Create a simple workflow to read a file\n     * const readFileWorkflow = factory.createSimpleToolWorkflow(\n     *   'fileSystem',\n     *   'readFile',\n     *   { path: '/path/to/file.txt' }\n     * );\n     *\n     * // Create with custom workflow ID\n     * const customWorkflow = factory.createSimpleToolWorkflow(\n     *   'terminal',\n     *   'execute',\n     *   { command: 'ls', args: ['-la'] },\n     *   'list-directory-workflow'\n     * );\n     * ```\n     */\n    public createSimpleToolWorkflow(\n        toolName: string,\n        actionName: string,\n        input: Record<string, unknown>,\n        workflowId: string = `tool-${toolName}-${Date.now()}`\n    ): Workflow {\n        const stepId = 'execute-tool';\n\n        // Create a single tool workflow step\n        const step = new ToolWorkflowStep(\n            stepId,\n            toolName,\n            actionName,\n            input,\n            this.services,\n            new vscode.CancellationTokenSource().token\n        );\n\n        // Create the workflow definition for the single step\n        const workflowDefinition: WorkflowDefinition = {\n            id: workflowId,\n            name: `Execute ${toolName}.${actionName}`,\n            description: `Simple workflow to execute ${toolName}.${actionName}`,\n            version: '1.0.0',\n            steps: [step],\n            inputs: [],\n            outputs: [],\n            startStepId: stepId\n        };\n\n        return new Workflow(workflowDefinition);\n    }\n}\n\nconst WorkflowPlanSchema = z.object({\n    thought: z.string().describe('The reasoning and strategy for the generated plan.'),\n    steps: z.array(z.object({\n        id: z.string().describe('A unique camelCase identifier for this step, e.g., \\'readFileStep\\'.'),\n        tool_name: z.string().describe('The exact name of the tool to use.'),\n        action_name: z.string().describe('The exact name of the action to execute.'),\n        input: z.record(z.unknown()).describe('Input parameters. Use \\'$stepId.output\\' to reference outputs from previous steps.'),\n    })).describe('The sequence of tool calls to achieve the goal.'),\n});\n\n/**\n * The central orchestrator for AI agent operations within the tool framework.\n *\n * This class serves as the primary coordinator for executing complex goals by leveraging\n * available tools and workflows. It handles goal analysis, workflow planning, execution,\n * and result processing. The orchestrator uses AI language models to create intelligent\n * execution plans and manages the entire lifecycle of AI agent operations.\n *\n * Key responsibilities:\n * - Goal analysis and context gathering using memory systems\n * - Dynamic workflow planning using language models\n * - Tool orchestration and execution coordination\n * - Progress tracking and result reporting\n * - Memory management for learning and context preservation\n *\n * @class Orchestrator\n *\n * @example\n * ```typescript\n * const orchestrator = new Orchestrator(services);\n *\n * // Execute a complex goal\n * const result = await orchestrator.run(\n *   'Analyze the codebase and generate documentation',\n *   {\n *     visualContext: [imageData],\n *     token: cancellationToken\n *   }\n * );\n *\n * // Execute a simple tool directly\n * const simpleResult = await orchestrator.runSimpleTool(\n *   'fileSystem',\n *   'readFile',\n *   { path: '/path/to/file.txt' },\n *   cancellationToken\n * );\n * ```\n */\nexport class Orchestrator {\n    /** The language model used for planning and reasoning */\n    private readonly llm: ILanguageModel;\n\n    /** Factory for creating workflows from tool execution plans */\n    private readonly workflowFactory: ToolWorkflowFactory;\n\n    /** Event emitter for workflow lifecycle events */\n    private readonly _emitter = new EventEmitter();\n\n    /**\n     * Creates a new Orchestrator instance.\n     *\n     * @param {IServiceContainer} services - The service container providing access to all framework services\n     */\n    constructor(private readonly services: IServiceContainer) {\n        this.llm = services.llmProvider.getActiveModel();\n        this.workflowFactory = new ToolWorkflowFactory(services);\n    }\n\n    /**\n     * Registers an event listener for workflow lifecycle events.\n     *\n     * @param {'workflowStart' | 'stepComplete' | 'workflowEnd'} event - The event type to listen for\n     * @param {(...args: unknown[]) => void} listener - The event handler function\n     *\n     * @example\n     * ```typescript\n     * orchestrator.on('workflowStart', ({ workflowId, goal }) => {\n     *   console.log(`Started workflow ${workflowId} for goal: ${goal}`);\n     * });\n     *\n     * orchestrator.on('workflowEnd', ({ workflowId, finalContext }) => {\n     *   console.log(`Completed workflow ${workflowId}: ${finalContext.status}`);\n     * });\n     * ```\n     */\n    public on(event: 'workflowStart' | 'stepComplete' | 'workflowEnd', listener: (...args: unknown[]) => void): void {\n        this._emitter.on(event, listener);\n    }\n\n    /**\n     * Executes a complex goal by analyzing requirements, planning workflow, and orchestrating tool execution.\n     *\n     * This is the main entry point for AI agent operations. It performs several key steps:\n     * 1. Analyzes the goal and retrieves relevant context from memory\n     * 2. Uses AI to generate a structured workflow plan\n     * 3. Creates and executes a workflow based on the plan\n     * 4. Tracks progress and reports results\n     * 5. Stores execution results in memory for future learning\n     *\n     * @param {string} goal - The goal to achieve (e.g., \"analyze codebase and generate documentation\")\n     * @param {Object} options - Execution options\n     * @param {MultimodalData[]} [options.visualContext] - Optional visual context (images) for analysis\n     * @param {CancellationToken} options.token - Cancellation token for the operation\n     * @returns {Promise<WorkflowStepResult>} The result of the workflow execution\n     *\n     * @throws {Error} If workflow planning or execution fails\n     *\n     * @example\n     * ```typescript\n     * const result = await orchestrator.run(\n     *   'Create a summary of all TypeScript files in the project',\n     *   {\n     *     visualContext: [], // No images provided\n     *     token: cancellationToken\n     *   }\n     * );\n     *\n     * if (result.success) {\n     *   console.log('Execution successful:', result.output);\n     * } else {\n     *   console.error('Execution failed:', result.error);\n     * }\n     * ```\n     */\n    public async run(goal: string, options: { visualContext?: MultimodalData[], token: CancellationToken }): Promise<WorkflowStepResult> {\n        const { visualContext, token } = options;\n        const workflowId = randomUUID();\n\n        return this.services.ui.showProgress(`AI Agent: ${goal}`, async (progress) => {\n            this._emitter.emit('workflowStart', { workflowId, goal });\n            progress.report({ message: 'Analyzing goal and memories...' });\n\n            // Utilize memory manager for context retrieval\n            const relevantMemories = await this.services.memoryManager.searchMemories({\n                query: goal,\n                limit: 10\n            } as MemorySearchOptions);\n\n            // Store search results in memory for future reference\n            const memoryEntry: MemoryEntry = {\n                id: randomUUID(),\n                content: `Goal: ${goal} | Memories found: ${relevantMemories.length}`,\n                timestamp: Date.now(),\n                metadata: {\n                    source: 'orchestrator' as MemorySource, // Type assertion for compatibility\n                    type: 'goal_analysis' as MemoryType,\n                    tags: ['goal_analysis', 'workflow_execution'],\n                    goal,\n                    memoryCount: relevantMemories.length,\n                    workflowId\n                }\n            };\n\n            // Use memoryManager instance with full MemoryManager functionality\n            try {\n                // Get memory manager from services\n                const memoryManager = this.services.memoryManager as IMemoryOperations & { storeMemory?: (entry: MemoryEntry) => Promise<void> };\n                await memoryManager.storeMemory?.(memoryEntry);\n            } catch (memoryError) {\n                // Silently handle memory storage errors\n                console.warn('Failed to store memory:', memoryError);\n            }\n\n            // Get configuration for enhanced workflow features using getConfig directly\n            const config = getConfig('aiAgent', {}) as { enableAdvancedFeatures?: boolean };\n            const enableAdvancedFeatures = config.enableAdvancedFeatures ?? true;\n\n            const plan = await this.createWorkflowPlan(goal, visualContext, token);\n            this.services.logger.info('Generated workflow plan', { workflowId, plan });\n\n            // Create workflow using the factory with enhanced features\n            const workflow = this.workflowFactory.createWorkflowFromPlan({\n                id: workflowId,\n                name: `Workflow for: ${goal}`,\n                steps: plan.steps\n            });\n\n            // Set up agent for the workflow if the workflow supports it\n            if ('setAgent' in workflow && typeof workflow.setAgent === 'function') {\n                const workflowWithAgent = workflow as { setAgent: (agent: unknown) => void };\n                workflowWithAgent.setAgent({});\n            }\n\n            // Enable advanced features if configured\n            if (enableAdvancedFeatures) {\n                workflow.enableAdvancedFeatures({\n                    goddessMode: true,\n                    quantumAnalysis: true,\n                    neuralSynthesis: false,\n                    timeTravelDebugging: false,\n                    adaptivePersonality: true\n                });\n            }\n\n            progress.report({ message: 'Executing workflow...' });\n\n            // Execute the workflow - simplified implementation\n            const finalResult = await this.executeWorkflowSimple(workflow);\n\n            // Register workflow with registry for future reference\n            const registry = workflowRegistry as unknown as { registerWorkflow?: (definition: unknown) => void };\n            const definition = workflow.getDefinition?.();\n            registry.registerWorkflow?.(definition);\n\n            this._emitter.emit('workflowEnd', {\n                workflowId,\n                finalContext: {\n                    status: finalResult.success ? 'completed' : 'failed',\n                    endTime: new Date()\n                }\n            });\n\n            this.services.logger.info('Workflow finished with result:', { workflowId, finalResult });\n            return finalResult;\n        });\n    }\n\n    public async runSimpleTool(\n        toolName: string,\n        actionName: string,\n        input: Record<string, unknown>,\n        token: CancellationToken\n    ): Promise<WorkflowStepResult> {\n        const workflow = this.workflowFactory.createSimpleToolWorkflow(toolName, actionName, input);\n        const context = workflow.getContext() as WorkflowContext & { cancellationToken?: CancellationToken };\n        context.cancellationToken = token; // Use the provided cancellation token\n        return await (workflow as unknown as { execute?: (context: WorkflowContext) => Promise<WorkflowStepResult> }).execute?.(context) || { success: false, error: 'Workflow execution not supported' };\n    }\n\n    private async createWorkflowPlan(goal: string, visualContext: MultimodalData[] | undefined, token: CancellationToken): Promise<z.infer<typeof WorkflowPlanSchema>> {\n        const relevantMemories = await this.services.memoryManager.searchMemories({ query: goal, limit: 5 });\n        const toolDocs = this.services.toolRegistry.getAllTools().map(t => t.getDocumentation()).join('\\n---\\n');\n        const promptParts: LanguageModelPromptPart[] = [];\n        const textualPrompt = `You are a world-class AI orchestrator. Your task is to create a JSON workflow plan to achieve the user's GOAL.\\n\\nGOAL: \"${goal}\"\\n\\nRELEVANT MEMORIES (for context):\\n${relevantMemories.length > 0 ? (relevantMemories as unknown[]).map(m => `- ${(m as { content: string }).content}`).join('\\n') : 'None'}\\n\\nAVAILABLE TOOLS:\\n${toolDocs}\\n\\nINSTRUCTIONS:\\n1. Think step-by-step to decompose the goal into a sequence of tool calls.\\n2. Define the workflow as a JSON object strictly matching this schema: { \"thought\": \"string\", \"steps\": [{ \"id\": \"string\", \"tool_name\": \"string\", \"action_name\": \"string\", \"input\": \"object\" }] }\\n3. To use an output from a previous step, use the syntax \"$stepId.output\".\\n4. If images are provided, incorporate your analysis of them into the plan.\\n5. Ensure the plan is logical and directly works towards solving the user's GOAL.\\n\\nRESPONSE (JSON object only):`;\n        if (visualContext?.length) {\n            promptParts.push({ type: 'text', text: 'Analyze image(s)...' });\n            visualContext.forEach(image => { promptParts.push({ type: 'image', source: { type: 'base64', media_type: image.mimeType, data: image.base64Data } }); });\n        }\n        promptParts.push({ type: 'text', text: textualPrompt });\n        const responseJson = await this.llm.generateResponse(promptParts, { cancellationToken: token, jsonOutput: true });\n        return WorkflowPlanSchema.parse(JSON.parse(responseJson));\n    }\n\n    /**\n     * Simplified workflow execution for when the full workflow engine is not available\n     */\n    private async executeWorkflowSimple(workflow: Workflow): Promise<WorkflowStepResult> {\n        const definition = workflow.getDefinition();\n        const context = workflow.getContext();\n\n        // Use workflowManager for advanced workflow coordination if available\n        interface WorkflowManager {\n            coordinateWorkflowExecution?: (\n                definition: unknown,\n                context: WorkflowContext,\n                options: {\n                    enableParallelExecution: boolean;\n                    enableErrorRecovery: boolean;\n                    enableProgressTracking: boolean;\n                }\n            ) => Promise<WorkflowStepResult>;\n        }\n\n        const wm = workflowManager as WorkflowManager;\n        if (wm.coordinateWorkflowExecution) {\n            try {\n                // Leverage workflowManager for enhanced execution\n                const enhancedResult = await wm.coordinateWorkflowExecution(\n                    definition,\n                    context,\n                    {\n                        enableParallelExecution: false,\n                        enableErrorRecovery: true,\n                        enableProgressTracking: true\n                    }\n                );\n\n                if (enhancedResult) {\n                    return {\n                        success: enhancedResult.success,\n                        output: enhancedResult.output,\n                        error: enhancedResult.error\n                    };\n                }\n            } catch (error) {\n                // Fall back to simple execution if advanced features fail\n                this.services.logger.warn('Advanced workflow execution failed, falling back to simple execution', { error: error instanceof Error ? error.message : String(error) });\n            }\n        }\n\n        // Execute steps sequentially\n        for (let i = 0; i < definition.steps.length; i++) {\n            const step = definition.steps[i];\n            const startTime = Date.now();\n\n            // Record step start in history\n            context.history.push({\n                stepId: step.id,\n                startTime: new Date(startTime),\n                result: undefined\n            });\n\n            try {\n                // Execute the step\n                const result = await step.execute(context);\n\n                // Record step completion\n                const historyEntry = context.history[context.history.length - 1];\n                historyEntry.endTime = new Date();\n                historyEntry.result = result;\n\n                // Store output in context if successful\n                if (result.success && result.output !== undefined) {\n                    context.outputs[step.id] = result.output;\n                }\n\n                // If step failed, return failure result\n                if (!result.success) {\n                    return {\n                        success: false,\n                        error: result.error || `Step '${step.name}' failed`\n                    };\n                }\n\n                // If step specifies next step, jump to it\n                if (result.nextStepId) {\n                    const nextIndex = definition.steps.findIndex(s => s.id === result.nextStepId);\n                    if (nextIndex !== -1) {\n                        i = nextIndex - 1; // Will be incremented by loop\n                    }\n                }\n            } catch (error: unknown) {\n                // Record error in history\n                const historyEntry = context.history[context.history.length - 1];\n                historyEntry.endTime = new Date();\n                historyEntry.result = {\n                    success: false,\n                    error: error instanceof Error ? error.message : String(error)\n                };\n\n                return {\n                    success: false,\n                    error: error instanceof Error ? error.message : String(error)\n                };\n            }\n        }\n\n        // All steps completed successfully\n        return {\n            success: true,\n            output: context.outputs\n        };\n    }\n}\n\n/**\n * VS Code implementation of the IPermissionManager interface.\n *\n * This service handles permission requests within the VS Code environment by:\n * - Storing granted permissions in VS Code's global state\n * - Presenting permission requests to users via modal dialogs\n * - Managing the lifecycle of permission grants\n *\n * The service persists permission grants across VS Code sessions using the\n * extension's global state storage.\n *\n * @class VSCodePermissionManager\n * @implements {IPermissionManager}\n *\n * @example\n * ```typescript\n * const permissionManager = new VSCodePermissionManager();\n * await permissionManager.initialize(context);\n *\n * const granted = await permissionManager.requestPermissions(\n *   ['workspace:write', 'shell:execute'],\n *   'This tool needs to modify files and run commands.'\n * );\n * ```\n */\nexport class VSCodePermissionManager implements IPermissionManager {\n    /** VS Code extension context for accessing global state */\n    private context: ExtensionContext | null = null;\n\n    /** Key used to store granted permissions in VS Code global state */\n    private readonly GRANTED_KEY = 'ai-agent.grantedPermissions';\n\n    /**\n     * Initializes the permission manager with the VS Code extension context.\n     *\n     * This method must be called before using the permission manager to request permissions.\n     * The context provides access to VS Code's global state for persisting permission grants.\n     *\n     * @param {ExtensionContext} context - The VS Code extension context\n     *\n     * @example\n     * ```typescript\n     * // During extension activation\n     * await permissionManager.initialize(context);\n     * ```\n     */\n    public async initialize(context: ExtensionContext): Promise<void> {\n        this.context = context; // Store context for potential future use\n    }\n\n    /**\n     * Requests one or more permissions from the user with a clear rationale.\n     *\n     * This method:\n     * 1. Checks if the requested permissions are already granted\n     * 2. Filters out already-granted permissions\n     * 3. Presents a modal dialog to the user explaining why permissions are needed\n     * 4. Updates the persistent permission store if the user grants permissions\n     *\n     * @param {readonly ToolPermission[]} permissions - Array of permissions to request\n     * @param {string} rationale - Clear explanation of why these permissions are needed\n     * @returns {Promise<boolean>} Promise that resolves to true if all permissions are granted\n     *\n     * @throws {Error} If the permission manager has not been initialized\n     *\n     * @example\n     * ```typescript\n     * const granted = await permissionManager.requestPermissions(\n     *   ['workspace:write', 'terminal:interactive_session'],\n     *   'This tool needs to create files and run interactive terminal sessions.'\n     * );\n     *\n     * if (!granted) {\n     *   throw new Error('Required permissions were not granted by the user.');\n     * }\n     * ```\n     */\n    public async requestPermissions(permissions: readonly ToolPermission[], rationale: string): Promise<boolean> {\n        // Return early if no permissions are requested\n        if (!permissions.length) {\n            return true;\n        }\n\n        // Ensure the manager has been initialized\n        if (!this.context) {\n            throw new Error('Permission manager not initialized.');\n        }\n\n        // Get currently granted permissions from persistent storage\n        const granted = new Set(this.context.globalState.get<ToolPermission[]>(this.GRANTED_KEY, []));\n\n        // Filter out permissions that are already granted\n        const needed = permissions.filter(p => !granted.has(p));\n\n        // Return early if all requested permissions are already granted\n        if (!needed.length) {\n            return true;\n        }\n\n        // Present permission request dialog to user\n        const decision = await vscode.window.showWarningMessage(\n            `${rationale} Requires: ${needed.join(', ')}. Grant?`,\n            { modal: true },\n            'Grant'\n        );\n\n        // Update persistent storage if user granted permissions\n        if (decision === 'Grant') {\n            needed.forEach(p => granted.add(p));\n            await this.context.globalState.update(this.GRANTED_KEY, Array.from(granted));\n            return true;\n        }\n\n        // User denied the permissions\n        return false;\n    }\n}\n\n/**\n * A simple, static implementation of the IToolRegistry interface.\n *\n * This registry maintains a collection of tools in memory and provides basic\n * methods for tool discovery and management. It's suitable for scenarios where\n * the set of available tools is known at initialization time and doesn't change\n * dynamically during runtime.\n *\n * Key features:\n * - In-memory storage of tool instances\n * - Fast lookup by tool name\n * - Immutable view of all registered tools\n * - Service container integration for tool initialization\n *\n * @class StaticToolRegistry\n * @implements {IToolRegistry}\n *\n * @example\n * ```typescript\n * const registry = new StaticToolRegistry();\n * await registry.initialize(serviceContainer);\n *\n * // Register tools (implementation would need to be added)\n * // registry.registerTool(new FileSystemTool());\n * // registry.registerTool(new TerminalTool());\n *\n * // Retrieve tools\n * const fileTool = registry.getTool('fileSystem');\n * const allTools = registry.getAllTools();\n * ```\n */\nexport class StaticToolRegistry implements IToolRegistry {\n    /** Internal storage for registered tools */\n    private tools = new Map<string, ITool>();\n\n    /** Reference to the service container for tool initialization */\n    private services: IServiceContainer | null = null;\n\n    /**\n     * Initializes the tool registry with the provided service container.\n     *\n     * This method stores a reference to the service container which can be used\n     * by tools during their initialization or operation. The container provides\n     * access to all framework services that tools might need.\n     *\n     * @param {IServiceContainer} services - The service container with all required services\n     * @returns {Promise<void>} A promise that resolves when initialization is complete\n     *\n     * @example\n     * ```typescript\n     * // During extension activation\n     * await toolRegistry.initialize(serviceContainer);\n     * ```\n     */\n    public async initialize(services: IServiceContainer): Promise<void> {\n        this.services = services; // Store services for potential future use\n    }\n\n    /**\n     * Retrieves a tool by its name.\n     *\n     * This method performs a fast lookup in the internal tool registry and returns\n     * the tool instance if found, or undefined if no tool with the specified name exists.\n     *\n     * @param {string} toolName - The name of the tool to retrieve\n     * @returns {ITool | undefined} The tool instance if found, otherwise undefined\n     *\n     * @example\n     * ```typescript\n     * const tool = toolRegistry.getTool('fileSystem');\n     * if (tool) {\n     *   // Use the tool\n     *   const result = await tool.invoke('readFile', { path: '/example.txt' }, options);\n     * }\n     * ```\n     */\n    public getTool(toolName: string): ITool | undefined {\n        return this.tools.get(toolName);\n    }\n\n    /**\n     * Retrieves all registered tools as an immutable array.\n     *\n     * This method returns a read-only view of all tools currently registered in the\n     * registry. The returned array cannot be modified, ensuring the integrity of\n     * the internal tool collection.\n     *\n     * @returns {readonly ITool[]} An immutable array of all registered tools\n     *\n     * @example\n     * ```typescript\n     * const allTools = toolRegistry.getAllTools();\n     * console.log(`Found ${allTools.length} tools:`);\n     * for (const tool of allTools) {\n     *   console.log(`- ${tool.name}: ${tool.description}`);\n     * }\n     * ```\n     */\n    public getAllTools(): readonly ITool[] {\n        return Array.from(this.tools.values());\n    }\n}\n\n/**\n * Abstract base class for language model implementations.\n *\n * This class provides common functionality and structure for all language model\n * implementations within the framework. It handles logging infrastructure and\n * provides a consistent constructor pattern for all language model services.\n *\n * Key features:\n * - Integrated logging support for all language model operations\n * - Consistent service container integration\n * - Abstract interface for response generation\n *\n * @abstract\n * @class BaseLanguageModel\n * @implements {ILanguageModel}\n *\n * @example\n * ```typescript\n * export class CustomLanguageModel extends BaseLanguageModel {\n *   public async generateResponse(\n *     prompt: LanguageModelPrompt,\n *     options: { cancellationToken: CancellationToken; jsonOutput: boolean; }\n *   ): Promise<string> {\n *     // Custom implementation here\n *     this.logger.info('Generating response with custom model');\n *     return 'Custom model response';\n *   }\n * }\n * ```\n */\nexport abstract class BaseLanguageModel implements ILanguageModel {\n    /** Logger instance for tracking language model operations and debugging */\n    protected readonly logger: ILogger;\n\n    /**\n     * Creates a new BaseLanguageModel instance.\n     *\n     * @param {IServiceContainer} services - The service container providing access to framework services\n     */\n    constructor(services: IServiceContainer) {\n        this.logger = services.logger;\n    }\n\n    /**\n     * Generates a response from the language model based on the provided prompt.\n     *\n     * This abstract method must be implemented by concrete language model classes\n     * to provide the actual model interaction logic.\n     *\n     * @abstract\n     * @param {LanguageModelPrompt} prompt - The input prompt for the language model\n     * @param {Object} options - Generation options\n     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation\n     * @param {boolean} options.jsonOutput - Whether to force JSON output format\n     * @returns {Promise<string>} A promise that resolves with the generated text response\n     *\n     * @example\n     * ```typescript\n     * const response = await languageModel.generateResponse(prompt, {\n     *   cancellationToken,\n     *   jsonOutput: true\n     * });\n     * ```\n     */\n    abstract generateResponse(\n        prompt: LanguageModelPrompt,\n        options: { cancellationToken: CancellationToken; jsonOutput: boolean; }\n    ): Promise<string>;\n}\n\n/**\n * OpenAI implementation of the ILanguageModel interface.\n *\n * This class provides integration with OpenAI's GPT models through their REST API.\n * It supports both text-only and multimodal prompts (with image support), and handles\n * cancellation tokens, timeout management, and proper error handling.\n *\n * Key features:\n * - Full OpenAI Chat Completions API support\n * - Multimodal prompt handling (text + images)\n * - Configurable model selection and parameters\n * - Automatic timeout and cancellation handling\n * - JSON output format support for structured responses\n * - Comprehensive error handling and logging\n *\n * @class OpenAIModel\n * @extends {BaseLanguageModel}\n * @implements {ILanguageModel}\n *\n * @example\n * ```typescript\n * const openAIModel = new OpenAIModel(services);\n *\n * const response = await openAIModel.generateResponse(\n *   [\n *     { type: 'text', text: 'Describe this image:' },\n *     { type: 'image', source: { type: 'base64', media_type: 'image/jpeg', data: '...' } }\n *   ],\n *   {\n *     cancellationToken,\n *     jsonOutput: false\n *   }\n * );\n * ```\n */\nexport class OpenAIModel extends BaseLanguageModel {\n    /**\n     * Generates a response from OpenAI's language model based on the provided prompt.\n     *\n     * This method handles the complete flow of preparing the request, sending it to\n     * OpenAI's API, processing the response, and handling any errors that occur.\n     *\n     * @param {LanguageModelPrompt} prompt - The input prompt containing text and/or images\n     * @param {Object} options - Generation options\n     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation\n     * @param {boolean} options.jsonOutput - Whether to force JSON output format\n     * @returns {Promise<string>} A promise that resolves with the generated text response\n     *\n     * @throws {OperationCancelledError} If the operation is cancelled via cancellation token\n     * @throws {Error} If the API request fails or configuration is invalid\n     *\n     * @example\n     * ```typescript\n     * const prompt = [\n     *   { type: 'text', text: 'Explain the following code:' },\n     *   { type: 'text', text: 'function add(a, b) { return a + b; }' }\n     * ];\n     *\n     * const response = await openAIModel.generateResponse(prompt, {\n     *   cancellationToken,\n     *   jsonOutput: true\n     * });\n     * ```\n     */\n    public async generateResponse(\n        prompt: LanguageModelPrompt,\n        options: { cancellationToken: CancellationToken; jsonOutput: boolean; }\n    ): Promise<string> {\n        // Retrieve OpenAI configuration from VS Code settings\n        const config = vscode.workspace.getConfiguration('aiAgent.openai');\n        const apiKey = process.env.OPENAI_API_KEY || config.get<string>('apiKey');\n\n        // Validate API key configuration\n        if (!apiKey) {\n            throw new Error('OpenAI API key not configured');\n        }\n\n        // Get API base URL with fallback to default\n        const baseUrl = config.get<string>('baseUrl', 'https://api.openai.com/v1');\n\n        // Set up request cancellation and timeout\n        const controller = new AbortController();\n        const timeout = setTimeout(() => controller.abort(), 30000);\n\n        // Check for cancellation before starting\n        if (options.cancellationToken.isCancellationRequested) {\n            throw new OperationCancelledError();\n        }\n\n        try {\n            // Convert multimodal prompt to OpenAI message format\n            const messages = (Array.isArray(prompt) ? prompt : [prompt]).map(p => {\n                if (typeof p === 'string') {\n                    return { role: 'user' as const, content: p };\n                } else if ('text' in p) {\n                    return { role: 'user' as const, content: p.text };\n                } else if ('image' in p) {\n                    // Note: OpenAI supports images, but this implementation uses placeholder\n                    return { role: 'user' as const, content: '[Image data]' };\n                }\n                return { role: 'user' as const, content: String(p) };\n            });\n\n            // Prepare request body with configuration\n            const requestBody = {\n                model: config.get<string>('model', 'gpt-4-turbo'),\n                messages,\n                temperature: 0.1,\n                // Add JSON format specification if requested\n                ...(options.jsonOutput && { response_format: { type: 'json_object' } })\n            };\n\n            // Make API request to OpenAI\n            const response = await fetch(`${baseUrl}/chat/completions`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKey}`\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n\n            // Handle API errors\n            if (!response.ok) {\n                const errorBody = await response.text();\n                throw new Error(`API Error ${response.status}: ${errorBody}`);\n            }\n\n            // Parse and validate response\n            interface OpenAIResponse {\n                choices: Array<{ message: { content: string } }>;\n                [key: string]: unknown;\n            }\n            const data = await response.json() as OpenAIResponse;\n\n            // Extract and return the response content\n            return data.choices[0]?.message?.content || '';\n\n        } catch (error) {\n            // Log error for debugging\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            this.logger.error(errorMessage, { message: 'OpenAI request failed.' });\n\n            // Re-throw the error for proper handling by calling code\n            throw error;\n\n        } finally {\n            // Clean up timeout regardless of outcome\n            clearTimeout(timeout);\n        }\n    }\n}\n\n/**\n * Ollama implementation of the ILanguageModel interface.\n *\n * This class provides integration with Ollama, a local LLM server that runs\n * various open-source language models. It's designed for scenarios where users\n * want to run models locally without relying on external API services.\n *\n * Key features:\n * - Local LLM execution via Ollama server\n * - Support for various open-source models\n * - Cancellation token support for operation control\n * - Comprehensive logging and error handling\n * - Placeholder implementation with proper scaffolding\n *\n * Note: This implementation is currently a placeholder and requires the actual\n * Ollama client integration to be completed.\n *\n * @class OllamaModel\n * @extends {BaseLanguageModel}\n * @implements {ILanguageModel}\n *\n * @example\n * ```typescript\n * const ollamaModel = new OllamaModel(services);\n *\n * try {\n *   const response = await ollamaModel.generateResponse(\n *     [{ type: 'text', text: 'Explain quantum computing' }],\n *     {\n *       cancellationToken,\n *       jsonOutput: false\n *     }\n *   );\n * } catch (error) {\n *   // Will throw NotImplementedError until implementation is completed\n *   console.error('Ollama integration not yet implemented');\n * }\n * ```\n */\nexport class OllamaModel extends BaseLanguageModel {\n    /**\n     * Generates a response using the Ollama local LLM server.\n     *\n     * This method is currently a placeholder implementation that validates\n     * configuration and logs the request parameters but does not actually\n     * communicate with an Ollama server. The implementation serves as a\n     * foundation for the eventual Ollama client integration.\n     *\n     * @param {LanguageModelPrompt} prompt - The input prompt for the language model\n     * @param {Object} options - Generation options\n     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation\n     * @param {boolean} options.jsonOutput - Whether to force JSON output format\n     * @returns {Promise<string>} A promise that resolves with the generated text response\n     *\n     * @throws {OperationCancelledError} If the operation is cancelled via cancellation token\n     * @throws {Error} If the Ollama model is not configured\n     * @throws {NotImplementedError} Always thrown until the actual implementation is completed\n     *\n     * @example\n     * ```typescript\n     * const prompt = [\n     *   { type: 'text', text: 'What is the capital of France?' }\n     * ];\n     *\n     * try {\n     *   const response = await ollamaModel.generateResponse(prompt, {\n     *     cancellationToken,\n     *     jsonOutput: false\n     *   });\n     * } catch (error) {\n     *   if (error instanceof NotImplementedError) {\n     *     console.log('Ollama integration is not yet implemented');\n     *   }\n     * }\n     * ```\n     */\n    public async generateResponse(\n        prompt: LanguageModelPrompt,\n        options: { cancellationToken: CancellationToken; jsonOutput: boolean; }\n    ): Promise<string> {\n        // Retrieve Ollama configuration from VS Code settings\n        const config = vscode.workspace.getConfiguration('aiAgent.ollama');\n        const model = config.get<string>('model');\n        const baseUrl = config.get<string>('baseUrl', 'http://localhost:11434');\n\n        // Check for cancellation before proceeding with validation\n        if (options.cancellationToken.isCancellationRequested) {\n            throw new OperationCancelledError();\n        }\n\n        // Validate model configuration\n        if (!model) {\n            throw new Error('Ollama model not configured.');\n        }\n\n        // Log the configuration details and prompt length for debugging\n        this.logger.info('Using Ollama.', {\n            baseUrl,\n            model,\n            promptLength: prompt.length,\n            jsonOutput: options.jsonOutput\n        });\n\n        // Check for cancellation before proceeding with implementation\n        if (options.cancellationToken.isCancellationRequested) {\n            throw new OperationCancelledError();\n        }\n\n        // Log that implementation is pending\n        this.logger.warn('Ollama client not implemented yet, but parameters are being utilized for logging and validation');\n\n        // Throw NotImplementedError until the actual implementation is completed\n        throw new NotImplementedError('Ollama client not implemented.');\n    }\n}\n\n/**\n * LM Studio implementation of the ILanguageModel interface.\n *\n * This class provides integration with LM Studio, a user-friendly desktop application\n * for running LLMs locally. LM Studio provides an OpenAI-compatible API server that\n * can run various quantized models locally, offering privacy and control benefits.\n *\n * Key features:\n * - Local LLM execution via LM Studio's OpenAI-compatible server\n * - Support for various quantized models (GGUF format)\n * - OpenAI-compatible API for easy integration\n * - Cancellation token support for operation control\n * - Comprehensive logging and error handling\n * - Placeholder implementation with proper scaffolding\n *\n * Note: This implementation is currently a placeholder and requires the actual\n * LM Studio client integration to be completed. LM Studio typically runs on\n * localhost:1234 and provides an OpenAI-compatible API.\n *\n * @class LMStudioModel\n * @extends {BaseLanguageModel}\n * @implements {ILanguageModel}\n *\n * @example\n * ```typescript\n * const lmStudioModel = new LMStudioModel(services);\n *\n * try {\n *   const response = await lmStudioModel.generateResponse(\n *     [{ type: 'text', text: 'Explain machine learning' }],\n *     {\n *       cancellationToken,\n *       jsonOutput: false\n *     }\n *   );\n * } catch (error) {\n *   // Will throw NotImplementedError until implementation is completed\n *   console.error('LM Studio integration not yet implemented');\n * }\n * ```\n */\nexport class LMStudioModel extends BaseLanguageModel {\n    /**\n     * Generates a response using the LM Studio local LLM server.\n     *\n     * This method is currently a placeholder implementation that validates\n     * configuration and logs the request parameters but does not actually\n     * communicate with an LM Studio server. The implementation serves as a\n     * foundation for the eventual LM Studio client integration.\n     *\n     * LM Studio typically provides an OpenAI-compatible API, so the eventual\n     * implementation will be similar to the OpenAI implementation but with\n     * different configuration defaults.\n     *\n     * @param {LanguageModelPrompt} prompt - The input prompt for the language model\n     * @param {Object} options - Generation options\n     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation\n     * @param {boolean} options.jsonOutput - Whether to force JSON output format\n     * @returns {Promise<string>} A promise that resolves with the generated text response\n     *\n     * @throws {OperationCancelledError} If the operation is cancelled via cancellation token\n     * @throws {NotImplementedError} Always thrown until the actual implementation is completed\n     *\n     * @example\n     * ```typescript\n     * const prompt = [\n     *   { type: 'text', text: 'What are the benefits of local LLMs?' }\n     * ];\n     *\n     * try {\n     *   const response = await lmStudioModel.generateResponse(prompt, {\n     *     cancellationToken,\n     *     jsonOutput: false\n     *   });\n     * } catch (error) {\n     *   if (error instanceof NotImplementedError) {\n     *     console.log('LM Studio integration is not yet implemented');\n     *   }\n     * }\n     * ```\n     */\n    public async generateResponse(\n        prompt: LanguageModelPrompt,\n        options: { cancellationToken: CancellationToken; jsonOutput: boolean; }\n    ): Promise<string> {\n        // Retrieve LM Studio configuration from VS Code settings\n        const config = vscode.workspace.getConfiguration('aiAgent.lmstudio');\n        const baseUrl = config.get<string>('baseUrl', 'http://localhost:1234/v1');\n\n        // Log the configuration details and prompt length for debugging\n        this.logger.info('Using LM Studio.', {\n            baseUrl,\n            promptLength: prompt.length,\n            jsonOutput: options.jsonOutput\n        });\n\n        // Check for cancellation before proceeding\n        if (options.cancellationToken.isCancellationRequested) {\n            throw new OperationCancelledError();\n        }\n\n        // Log that implementation is pending\n        this.logger.warn('LM Studio client not implemented yet, but parameters are being utilized for logging and validation');\n\n        // Throw NotImplementedError until the actual implementation is completed\n        throw new NotImplementedError('LM Studio client not implemented.');\n    }\n}\n\n/**\n * VS Code implementation of the ILanguageModelProvider interface.\n *\n * This service manages the lifecycle of language model providers within the VS Code\n * environment. It acts as a factory and registry for different LLM implementations,\n * allowing users to switch between providers (OpenAI, Ollama, LM Studio) via\n * configuration settings.\n *\n * Key features:\n * - Dynamic provider switching based on VS Code configuration\n * - Lazy initialization of language model instances\n * - Comprehensive provider support (OpenAI, Ollama, LM Studio)\n * - Configuration-driven provider selection\n * - Proper error handling for unsupported providers\n * - Integration with VS Code's settings system\n *\n * The provider reads from the 'aiAgent.provider' configuration setting to determine\n * which language model implementation to instantiate. This allows users to switch\n * between different LLM backends without code changes.\n *\n * @class LanguageModelProvider\n * @implements {ILanguageModelProvider}\n *\n * @example\n * ```typescript\n * const provider = new LanguageModelProvider();\n * await provider.initialize(serviceContainer);\n *\n * const model = provider.getActiveModel();\n * const response = await model.generateResponse(prompt, options);\n *\n * // User can switch providers via VS Code settings:\n * // \"aiAgent.provider\": \"ollama\" | \"openai\" | \"lmstudio\"\n * ```\n */\nexport class LanguageModelProvider implements ILanguageModelProvider {\n    /** The currently active language model instance */\n    private activeModel!: ILanguageModel;\n\n    /**\n     * Initializes the language model provider with the required service container.\n     *\n     * This method reads the VS Code configuration to determine which language model\n     * provider to instantiate. It supports multiple providers including OpenAI,\n     * Ollama, and LM Studio, allowing users to choose their preferred LLM backend.\n     *\n     * The initialization process:\n     * 1. Reads 'aiAgent.provider' configuration setting\n     * 2. Instantiates the appropriate language model class\n     * 3. Logs the selected provider for debugging and monitoring\n     * 4. Stores the instance for later retrieval\n     *\n     * @param {IServiceContainer} services - The service container providing access to framework services\n     * @returns {void}\n     *\n     * @throws {Error} If an unsupported provider is configured\n     *\n     * @example\n     * ```typescript\n     * // During extension activation\n     * const provider = new LanguageModelProvider();\n     * provider.initialize(serviceContainer);\n     *\n     * // Configuration in VS Code settings:\n     * // {\n     * //   \"aiAgent.provider\": \"openai\"\n     * // }\n     * ```\n     */\n    public initialize(services: IServiceContainer): void {\n        // Read provider configuration from VS Code settings\n        const providerType = vscode.workspace.getConfiguration('aiAgent').get<string>('provider', 'openai');\n\n        // Instantiate the appropriate language model based on configuration\n        switch (providerType.toLowerCase()) {\n            case 'openai':\n                this.activeModel = new OpenAIModel(services);\n                break;\n            case 'ollama':\n                this.activeModel = new OllamaModel(services);\n                break;\n            case 'lmstudio':\n                this.activeModel = new LMStudioModel(services);\n                break;\n            default:\n                throw new Error(`Unsupported provider: '${providerType}'. Supported providers are: openai, ollama, lmstudio.`);\n        }\n\n        // Log the selected provider for debugging and monitoring\n        services.logger.info(`Active AI provider: ${providerType}`);\n    }\n\n    /**\n     * Retrieves the currently active language model instance.\n     *\n     * This method returns the language model instance that was created during\n     * initialization. It ensures that the provider has been properly initialized\n     * before allowing access to the model.\n     *\n     * @returns {ILanguageModel} The active language model instance\n     *\n     * @throws {Error} If the provider has not been initialized\n     *\n     * @example\n     * ```typescript\n     * const model = provider.getActiveModel();\n     * const response = await model.generateResponse(prompt, {\n     *   cancellationToken,\n     *   jsonOutput: false\n     * });\n     * ```\n     */\n    public getActiveModel(): ILanguageModel {\n        // Ensure the provider has been initialized before allowing access\n        if (!this.activeModel) {\n            throw new Error('LLM Provider not initialized. Call initialize() first.');\n        }\n\n        return this.activeModel;\n    }\n}\n\n/**\n * VS Code implementation of the IToolUIProvider interface.\n *\n * This service provides user interface interaction capabilities within the VS Code\n * environment, serving as a bridge between tools and VS Code's UI components.\n * It abstracts common UI operations to allow tools to present information,\n * request input, and show progress without directly depending on VS Code APIs.\n *\n * Key features:\n * - Progress indicator support with cancellation capability\n * - Information, warning, and error message display\n * - Integration with VS Code's notification system\n * - Proper error message extraction from Error objects\n * - Non-blocking UI operations for tool execution\n *\n * The provider uses VS Code's built-in UI primitives to ensure consistent\n * user experience and proper integration with the editor environment.\n *\n * @class VSCodeUIProvider\n * @implements {IToolUIProvider}\n *\n * @example\n * ```typescript\n * const uiProvider = new VSCodeUIProvider();\n *\n * // Show progress for long-running operation\n * const result = await uiProvider.showProgress('Processing files...', async (progress, token) => {\n *   progress.report({ message: 'Starting...' });\n *   // Perform work...\n *   return await someAsyncOperation();\n * });\n *\n * // Show various message types\n * uiProvider.showInformationMessage('Operation completed successfully');\n * uiProvider.showWarningMessage('This may take a while');\n * uiProvider.showErrorMessage(new Error('Something went wrong'));\n * ```\n */\nexport class VSCodeUIProvider implements IToolUIProvider {\n    /**\n     * Shows a progress indicator in the VS Code UI while executing the provided work.\n     *\n     * This method displays a cancellable progress notification in VS Code's status\n     * bar area and executes the provided work function with progress reporting\n     * capabilities. The progress indicator automatically disappears when the\n     * work completes or is cancelled.\n     *\n     * @template T - The type of the result returned by the work function\n     * @param {string} title - The title to display in the progress notification\n     * @param {(progress: vscode.Progress<{ message?: string }>, token: vscode.CancellationToken) => Thenable<T>} work - The async function to execute with progress tracking\n     * @returns {Promise<T>} A promise that resolves with the result of the work function\n     *\n     * @example\n     * ```typescript\n     * const files = await uiProvider.showProgress('Scanning workspace...', async (progress, token) => {\n     *   progress.report({ message: 'Finding files...' });\n     *\n     *   if (token.isCancellationRequested) {\n     *     throw new OperationCancelledError();\n     *   }\n     *\n     *   const result = await scanWorkspace();\n     *   progress.report({ message: 'Complete!' });\n     *   return result;\n     * });\n     * ```\n     */\n    public async showProgress<T>(\n        title: string,\n        work: (progress: vscode.Progress<{ message?: string }>, token: vscode.CancellationToken) => Thenable<T>\n    ): Promise<T> {\n        return vscode.window.withProgress(\n            {\n                location: vscode.ProgressLocation.Notification,\n                title,\n                cancellable: true\n            },\n            (progress, token) => work(progress, token)\n        );\n    }\n\n    /**\n     * Shows an informational message to the user.\n     *\n     * This method displays a non-intrusive information message to the user\n     * through VS Code's notification system. The message appears temporarily\n     * and doesn't require user interaction to dismiss.\n     *\n     * @param {string} message - The message to display\n     *\n     * @example\n     * ```typescript\n     * uiProvider.showInformationMessage('File saved successfully');\n     * ```\n     */\n    public showInformationMessage(message: string): void {\n        vscode.window.showInformationMessage(message);\n    }\n\n    /**\n     * Shows a warning message to the user.\n     *\n     * This method displays a warning message that draws the user's attention\n     * to potentially important information. The message is styled to indicate\n     * a warning condition.\n     *\n     * @param {string} message - The warning message to display\n     *\n     * @example\n     * ```typescript\n     * uiProvider.showWarningMessage('Large file detected - processing may be slow');\n     * ```\n     */\n    public showWarningMessage(message: string): void {\n        vscode.window.showWarningMessage(message);\n    }\n\n    /**\n     * Shows an error message to the user.\n     *\n     * This method displays an error message that indicates a problem or failure.\n     * If an Error object is provided, it extracts and displays the error message.\n     * The message is styled to indicate an error condition.\n     *\n     * @param {string | Error} message - The error message or Error object to display\n     *\n     * @example\n     * ```typescript\n     * try {\n     *   await riskyOperation();\n     * } catch (error) {\n     *   uiProvider.showErrorMessage(error);\n     * }\n     *\n     * // Or with a custom message\n     * uiProvider.showErrorMessage('Failed to save file');\n     * ```\n     */\n    public showErrorMessage(message: string | Error): void {\n        vscode.window.showErrorMessage(message instanceof Error ? message.message : message);\n    }\n}\n\n/**\n * VS Code implementation of the ILogger interface using output channels.\n *\n * This logger implementation integrates with VS Code's output channel system,\n * providing a dedicated output window for logging messages. It's designed for\n * development and debugging purposes, allowing users to view detailed logs\n * in a structured format within the VS Code interface.\n *\n * Key features:\n * - Integration with VS Code's output channel system\n * - Structured logging with metadata support\n * - Automatic error stack trace inclusion\n * - Consistent log formatting with level prefixes\n * - Persistent log visibility across sessions\n *\n * The logger creates a named output channel that can be viewed by users through\n * VS Code's \"Output\" panel, making it ideal for debugging and monitoring tool\n * execution and framework operations.\n *\n * @class VSCodeOutputChannelLogger\n * @implements {ILogger}\n *\n * @example\n * ```typescript\n * const logger = new VSCodeOutputChannelLogger('My Tool');\n *\n * logger.info('Tool initialized successfully');\n * logger.debug('Processing file', { fileName: 'example.txt', size: 1024 });\n * logger.error(new Error('File processing failed'), { fileName: 'example.txt' });\n *\n * // Users can view logs in VS Code's Output panel under \"My Tool\" channel\n * ```\n */\nexport class VSCodeOutputChannelLogger implements ILogger {\n    /** The VS Code output channel used for logging */\n    private channel: OutputChannel;\n\n    /**\n     * Creates a new VS Code output channel logger instance.\n     *\n     * This constructor creates a dedicated output channel in VS Code where all\n     * log messages will be displayed. The channel name is used to identify the\n     * log source in the Output panel.\n     *\n     * @param {string} name - The name of the output channel (displayed in VS Code's Output panel)\n     *\n     * @example\n     * ```typescript\n     * const logger = new VSCodeOutputChannelLogger('AI Agent Tools');\n     * ```\n     */\n    constructor(name: string) {\n        this.channel = vscode.window.createOutputChannel(name);\n    }\n\n    /**\n     * Logs a debug-level message with optional metadata.\n     *\n     * Debug messages are intended for detailed diagnostic information during\n     * development and troubleshooting. They provide insights into the internal\n     * workings of tools and framework components.\n     *\n     * @param {string} message - The debug message to log\n     * @param {object} [metadata] - Optional additional data to include with the log\n     *\n     * @example\n     * ```typescript\n     * logger.debug('Processing started', {\n     *   fileName: 'config.json',\n     *   operation: 'validation',\n     *   timestamp: Date.now()\n     * });\n     * ```\n     */\n    public debug(message: string, metadata?: object): void {\n        this.log('DEBUG', message, metadata);\n    }\n\n    /**\n     * Logs an informational message with optional metadata.\n     *\n     * Info messages provide general operational information about tool execution,\n     * successful operations, and important state changes.\n     *\n     * @param {string} message - The informational message to log\n     * @param {object} [metadata] - Optional additional data to include with the log\n     *\n     * @example\n     * ```typescript\n     * logger.info('File system tool initialized successfully');\n     * logger.info('Processing completed', { filesProcessed: 15, duration: 250 });\n     * ```\n     */\n    public info(message: string, metadata?: object): void {\n        this.log('INFO', message, metadata);\n    }\n\n    /**\n     * Logs a warning message with optional metadata.\n     *\n     * Warning messages indicate potentially harmful situations or important\n     * conditions that should be reviewed but don't prevent operation.\n     *\n     * @param {string} message - The warning message to log\n     * @param {object} [metadata] - Optional additional data to include with the log\n     *\n     * @example\n     * ```typescript\n     * logger.warn('Large file detected - processing may be slow', {\n     *   fileSize: '2.5GB',\n     *   recommendedAction: 'Consider chunking'\n     * });\n     * ```\n     */\n    public warn(message: string, metadata?: object): void {\n        this.log('WARN', message, metadata);\n    }\n\n    /**\n     * Logs an error message or Error object with optional metadata.\n     *\n     * Error messages indicate error conditions that need attention. If an Error\n     * object is provided, the logger automatically includes the stack trace\n     * for debugging purposes.\n     *\n     * @param {string | Error} message - The error message or Error object to log\n     * @param {object} [metadata] - Optional additional data to include with the log\n     *\n     * @example\n     * ```typescript\n     * try {\n     *   await riskyOperation();\n     * } catch (error) {\n     *   logger.error(error, { operation: 'fileProcessing', fileName: 'data.txt' });\n     * }\n     *\n     * // Or with a custom message\n     * logger.error('Failed to connect to external service');\n     * ```\n     */\n    public error(message: string | Error, metadata?: object): void {\n        this.log('ERROR', message instanceof Error ? message.message : message,\n            message instanceof Error ? { ...metadata, stack: message.stack } : metadata);\n    }\n\n    /**\n     * Internal method that formats and writes log messages to the output channel.\n     *\n     * This private method handles the common formatting logic for all log levels,\n     * ensuring consistent output format and proper metadata serialization.\n     *\n     * @private\n     * @param {string} level - The log level (DEBUG, INFO, WARN, ERROR)\n     * @param {string} message - The log message\n     * @param {object} [metadata] - Optional metadata to include\n     *\n     * @example\n     * ```typescript\n     * // Internal usage\n     * this.log('INFO', 'Operation completed', { duration: 150 });\n     * // Output: [INFO] Operation completed | {\"duration\":150}\n     * ```\n     */\n    private log(level: string, message: string, metadata?: object): void {\n        this.channel.appendLine(`[${level}] ${message}${metadata ? ` | ${JSON.stringify(metadata)}` : ''}`);\n    }\n}\n\n/**\n * VS Code implementation of the IFileSystemManager interface.\n *\n * This service provides secure, sandboxed file system operations within the VS Code\n * workspace environment. It ensures that all file operations are confined to the\n * workspace boundaries and provides automatic path resolution and validation.\n *\n * Key features:\n * - Workspace-bound file operations (cannot access files outside workspace)\n * - Automatic path resolution relative to workspace root\n * - Path traversal attack prevention\n * - Support for both string and Buffer content\n * - Automatic directory creation for file writes\n * - UTF-8 encoding for text file operations\n * - Comprehensive error handling and validation\n *\n * The service automatically resolves relative paths against the workspace root\n * and validates that all operations stay within the workspace boundaries.\n * This prevents path traversal attacks and ensures data integrity.\n *\n * @class VSCodeFileSystemManager\n * @implements {IFileSystemManager}\n *\n * @example\n * ```typescript\n * const fsManager = new VSCodeFileSystemManager();\n *\n * // Read a configuration file\n * const config = await fsManager.readFile('config/settings.json');\n *\n * // Write generated content\n * await fsManager.writeFile('dist/output.js', generatedCode);\n *\n * // Create nested directories automatically\n * await fsManager.createDirectory('src/components/utils');\n *\n * // Delete temporary files\n * await fsManager.deleteFile('temp/cache.json');\n * ```\n */\nexport class VSCodeFileSystemManager implements IFileSystemManager {\n    /**\n     * Creates a patch file showing differences between old and new content\n     * @param relativePath The path to the file being patched (for context in the patch file)\n     * @param oldContent The original content\n     * @param newContent The new content\n     * @param outputPath Where to save the patch file\n     * @param options Diff generation options\n     * @returns A promise that resolves to the path of the created patch file\n     */\n    async createPatchFile(\n        relativePath: string,\n        oldContent: string | Buffer,\n        newContent: string | Buffer,\n        outputPath: string,\n        options?: DiffOptions\n    ): Promise<void> {\n        const diff = await this.diffContent(\n            oldContent,\n            newContent,\n            { ...options, format: DiffFormat.Unified }\n        );\n\n        await this.writeFile(outputPath, diff);\n    }\n\n    /**\n     * Applies a patch file to the workspace\n     * @param relativePath The path to the file to patch (relative to workspace root)\n     * @param patchPath The path to the patch file to apply\n     * @param options Options for applying the patch\n     * @returns The result of the patch application\n     */\n    async applyPatchFile(\n        relativePath: string,\n        patchPath: string,\n        options?: FileEditOptions\n    ): Promise<{ success: boolean; conflicts: string[] }> {\n        const patchContent = await this.readFile(patchPath);\n        return this.applyDiff(relativePath, patchContent, options);\n    }\n\n    /** The absolute path to the workspace root directory */\n    private readonly workspaceRoot: string;\n\n    /**\n     * Creates a new VS Code file system manager instance.\n     *\n     * This constructor initializes the workspace root path and validates that\n     * a workspace is currently open. All file operations will be relative to\n     * this workspace root.\n     *\n     * @throws {Error} If no workspace folder is currently open in VS Code\n     *\n     * @example\n     * ```typescript\n     * // Will succeed if a workspace is open\n     * const fsManager = new VSCodeFileSystemManager();\n     *\n     * // Will throw if no workspace is open\n     * // const fsManager = new VSCodeFileSystemManager(); // Error!\n     * ```\n     */\n    constructor() {\n        const root = vscode.workspace.workspaceFolders?.[0];\n        if (!root) {\n            throw new Error('No workspace folder open.');\n        }\n        this.workspaceRoot = root.uri.fsPath;\n    }\n\n    /**\n     * Resolves and validates a relative path against the workspace root.\n     *\n     * This private method performs critical security validation by ensuring\n     * that the resolved absolute path is within the workspace boundaries.\n     * It prevents path traversal attacks and ensures data integrity.\n     *\n     * @private\n     * @param {string} relativePath - The relative path to resolve and validate\n     * @returns {string} The validated absolute path\n     * @throws {Error} If the resolved path would be outside the workspace\n     *\n     * @example\n     * ```typescript\n     * // Safe path resolution\n     * const safePath = this._resolveAndValidatePath('src/utils/helpers.ts');\n     * // Result: '/workspace/src/utils/helpers.ts'\n     *\n     * // Path traversal attempt (will throw)\n     * // const blockedPath = this._resolveAndValidatePath('../../../etc/passwd');\n     * // Error: Path traversal denied.\n     * ```\n     */\n    private resolveAndValidatePath(relativePath: string): string {\n        const absolutePath = path.resolve(this.workspaceRoot, relativePath);\n\n        // Security check: ensure the path is within workspace boundaries\n        if (!absolutePath.startsWith(this.workspaceRoot)) {\n            throw new Error('Path traversal denied.');\n        }\n\n        return absolutePath;\n    }\n\n    /**\n     * Reads the contents of a file as a UTF-8 encoded string.\n     *\n     * This method reads the complete contents of a file and returns it as a string.\n     * The file path is resolved relative to the workspace root and validated\n     * for security.\n     *\n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @returns {Promise<string>} A promise that resolves with the file contents as a UTF-8 string\n     * @throws {Error} If the file doesn't exist, cannot be read, or is outside the workspace\n     *\n     * @example\n     * ```typescript\n     * // Read a TypeScript configuration file\n     * const tsConfig = await fsManager.readFile('tsconfig.json');\n     * console.log('TypeScript config:', tsConfig);\n     *\n     * // Read a source code file\n     * const sourceCode = await fsManager.readFile('src/main.ts');\n     * ```\n     */\n    public async readFile(relativePath: string): Promise<string> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        const uri = vscode.Uri.file(absolutePath);\n\n        try {\n            // First try to get the document if it's already open in the editor\n            const document = vscode.workspace.textDocuments.find(\n                doc => doc.uri.fsPath === absolutePath\n            );\n\n            if (document) {\n                return document.getText();\n            }\n\n            // If not open, open it as a text document\n            const textDocument = await vscode.workspace.openTextDocument(uri);\n            return textDocument.getText();\n        } catch (error) {\n            // Fall back to direct file system read if document can't be opened\n            return fs.readFile(absolutePath, 'utf-8');\n        }\n    }\n\n    /**\n     * Writes content to a file, creating it if it doesn't exist or overwriting if it does.\n     *\n     * This method writes the provided content to a file. If the file doesn't exist,\n     * it will be created. If the parent directories don't exist, they will be\n     * created automatically. The content can be either a string or Buffer.\n     *\n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @param {string | Buffer} content - The content to write to the file\n     * @returns {Promise<void>} A promise that resolves when the write operation is complete\n     * @throws {Error} If the file cannot be written or is outside the workspace\n     *\n     * @example\n     * ```typescript\n     * // Write a configuration file\n     * await fsManager.writeFile('config/app.json', JSON.stringify(config, null, 2));\n     *\n     * // Write binary content\n     * const buffer = Buffer.from('binary data');\n     * await fsManager.writeFile('assets/image.png', buffer);\n     *\n     * // Create nested directories automatically\n     * await fsManager.writeFile('deep/nested/path/file.txt', 'content');\n     * ```\n     */\n    public async writeFile(relativePath: string, content: string | Buffer): Promise<void> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        const uri = vscode.Uri.file(absolutePath);\n\n        // Create parent directories if they don't exist\n        await fs.mkdir(path.dirname(absolutePath), { recursive: true });\n\n        // Convert Buffer to string if needed\n        const contentStr = Buffer.isBuffer(content) ? content.toString('utf-8') : content;\n\n        // Check if the document is already open\n        const document = vscode.workspace.textDocuments.find(\n            doc => doc.uri.fsPath === absolutePath\n        );\n\n        if (document && !document.isClosed) {\n            // If document is open, use workspace edit to update it\n            const edit = new vscode.WorkspaceEdit();\n            const fullRange = new vscode.Range(\n                document.positionAt(0),\n                document.positionAt(document.getText().length)\n            );\n            edit.replace(uri, fullRange, contentStr);\n            await vscode.workspace.applyEdit(edit);\n\n            // Save the document if it's dirty\n            if (document.isDirty) {\n                await document.save();\n            }\n        } else {\n            // Fall back to direct file system write\n            await fs.writeFile(absolutePath, content);\n\n            // If the file is open in any editor, ensure it's reloaded\n            const editor = vscode.window.visibleTextEditors.find(\n                e => e.document.uri.fsPath === absolutePath\n            );\n            if (editor) {\n                await vscode.commands.executeCommand('workbench.action.files.revert', editor.document.uri);\n            }\n        }\n    }\n\n    /**\n     * Deletes a file from the workspace.\n     *\n     * This method removes a file from the file system. The file path is resolved\n     * relative to the workspace root and validated for security before deletion.\n     *\n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @returns {Promise<void>} A promise that resolves when the file is deleted\n     * @throws {Error} If the file doesn't exist, cannot be deleted, or is outside the workspace\n     *\n     * @example\n     * ```typescript\n     * // Delete a temporary file\n     * await fsManager.deleteFile('temp/cache.json');\n     *\n     * // Delete a generated file\n     * await fsManager.deleteFile('dist/bundle.js');\n     * ```\n     */\n    public async deleteFile(relativePath: string): Promise<void> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        const uri = vscode.Uri.file(absolutePath);\n\n        // Check if the document is open in any editor\n        const document = vscode.workspace.textDocuments.find(\n            doc => doc.uri.fsPath === absolutePath\n        );\n\n        if (uri) {\n            workflowManager.logger?.debug?.(`Processing file deletion for ${uri.fsPath}`);\n        }\n\n        // If document is open, close it first\n        if (document) {\n            // Find all editors with this document\n            const editors = vscode.window.visibleTextEditors.filter(\n                editor => editor.document.uri.fsPath === absolutePath\n            );\n\n            // Close all editors showing this document\n            for (const editor of editors) {\n                await vscode.window.showTextDocument(editor.document, { preview: false });\n                await vscode.commands.executeCommand('workbench.action.closeActiveEditor');\n            }\n        }\n\n        // Now delete the file\n        await fs.unlink(absolutePath);\n\n        // Notify VS Code that the file was deleted\n        // VS Code automatically handles file system events, so we don't need to manually fire them\n        // The file system watcher will pick up the change and notify any listeners\n    }\n\n    /**\n     * Creates a directory and any necessary parent directories.\n     *\n     * This method creates a directory at the specified path. If parent directories\n     * don't exist, they will be created automatically. The path is resolved\n     * relative to the workspace root and validated for security.\n     *\n     * @param {string} relativePath - The path to the directory relative to the workspace root\n     * @returns {Promise<void>} A promise that resolves when the directory is created\n     * @throws {Error} If the directory cannot be created or is outside the workspace\n     *\n     * @example\n     * ```typescript\n     * // Create a simple directory\n     * await fsManager.createDirectory('logs');\n     *\n     * // Create nested directories\n     * await fsManager.createDirectory('src/components/buttons');\n     *\n     * // Create deep directory structure\n     * await fsManager.createDirectory('app/modules/user/authentication');\n     * ```\n     */\n    public async createDirectory(relativePath: string): Promise<void> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        await fs.mkdir(absolutePath, { recursive: true });\n    }\n\n    public async appendToFile(\n        relativePath: string,\n        content: string | Buffer,\n        options: FileAppendOptions & FileEditOptions & { viewColumn?: ViewColumn } = {}\n    ): Promise<void> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        const uri = vscode.Uri.file(absolutePath);\n        const contentStr = Buffer.isBuffer(content) ? content.toString('utf-8') : content;\n\n        // Create a backup if requested\n        if (options.backup) {\n            const backupPath = `${absolutePath}${options.backupSuffix || '.bak'}`;\n            await fs.copyFile(absolutePath, backupPath);\n        }\n\n        // Check if the document is already open\n        let document = vscode.workspace.textDocuments.find(\n            doc => doc.uri.fsPath === absolutePath\n        );\n\n        let currentContent = '';\n\n        if (document) {\n            // Get content from open document\n            currentContent = document.getText();\n        } else {\n            // Read from file system if not open\n            try {\n                currentContent = await fs.readFile(absolutePath, 'utf-8');\n            } catch (error: unknown) {\n                const nodeError = error as NodeJS.ErrnoException;\n                if (nodeError.code !== 'ENOENT' || !options.createIfNotExists) {\n                    throw error;\n                }\n            }\n\n            // Open the document if we need to show it\n            if (options.viewColumn) {\n                const showOptions: TextDocumentShowOptions = {\n                    viewColumn: options.viewColumn,\n                    preserveFocus: options.preserveFocus,\n                    preview: options.preview\n                };\n\n                document = await vscode.workspace.openTextDocument(uri);\n                const editor = await vscode.window.showTextDocument(document, showOptions);\n\n                // Reveal the end of the document where we'll append\n                const lastLine = document.lineCount - 1;\n                const endPos = new vscode.Position(lastLine, document.lineAt(lastLine).text.length);\n                editor.revealRange(\n                    new vscode.Range(endPos, endPos),\n                    vscode.TextEditorRevealType.Default\n                );\n            }\n        }\n\n        // Handle separator for appending\n        const separator = options.separator || '\\n';\n        const newContent = currentContent\n            ? currentContent + separator + contentStr\n            : contentStr;\n\n        // Write the updated content using the existing writeFile method\n        await this.writeFile(relativePath, newContent);\n\n        // If we have an open document and it's visible, scroll to the end\n        if (document) {\n            const editor = vscode.window.visibleTextEditors.find(\n                e => e.document.uri.fsPath === absolutePath\n            );\n\n            if (editor) {\n                const lastLine = editor.document.lineCount - 1;\n                const endPos = new vscode.Position(lastLine, editor.document.lineAt(lastLine).text.length);\n\n                // Scroll to show the appended content\n                editor.revealRange(\n                    new vscode.Range(endPos, endPos),\n                    vscode.TextEditorRevealType.InCenterIfOutsideViewport\n                );\n\n                // Move cursor to the end of the appended content\n                editor.selection = new vscode.Selection(endPos, endPos);\n            }\n        }\n    }\n\n    public async findInFile(\n        relativePath: string,\n        searchPattern: string | RegExp,\n        options: FileSearchOptions = {}\n    ): Promise<ContentMatch[]> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        const content = await fs.readFile(absolutePath, 'utf-8');\n        const lines = content.split('\\n');\n        const matches: ContentMatch[] = [];\n        const regex = searchPattern instanceof RegExp\n            ? searchPattern\n            : new RegExp(\n                options.useRegex ? searchPattern : searchPattern.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'),\n                options.caseSensitive ? 'g' : 'gi'\n            );\n\n        for (let i = 0; i < lines.length; i++) {\n            const line = lines[i];\n            let match;\n            while ((match = regex.exec(line)) !== null) {\n                matches.push({\n                    lineNumber: i + 1,\n                    lineContent: line,\n                    match: match[0],\n                    position: [match.index + 1, match.index + match[0].length]\n                });\n\n                // Prevent infinite loops for zero-length matches\n                if (match.index === regex.lastIndex) {\n                    regex.lastIndex++;\n                }\n            }\n        }\n\n        return matches;\n    }\n\n    public async replaceInFile(\n        relativePath: string,\n        searchPattern: string | RegExp,\n        replacement: string | ((match: string, ...groups: string[]) => string),\n        options: FileSearchOptions & FileEditOptions = {}\n    ): Promise<number> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        const content = await fs.readFile(absolutePath, 'utf-8');\n\n        // Create a backup if requested\n        if (options.backup) {\n            const backupPath = `${absolutePath}${options.backupSuffix || '.bak'}`;\n            await fs.copyFile(absolutePath, backupPath);\n        }\n\n        const regex = searchPattern instanceof RegExp\n            ? searchPattern\n            : new RegExp(\n                options.useRegex ? searchPattern : searchPattern.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'),\n                options.caseSensitive ? 'g' : 'gi'\n            );\n\n        let replaceCount = 0;\n        const newContent = content.replace(regex, (...args) => {\n            replaceCount++;\n            return typeof replacement === 'function'\n                ? replacement(...args)\n                : replacement;\n        });\n\n        if (replaceCount > 0) {\n            await this.writeFile(relativePath, newContent);\n        }\n\n        return replaceCount;\n    }\n\n    public async insertAtLine(\n        relativePath: string,\n        lineNumber: number,\n        content: string,\n        options: FileEditOptions = {}\n    ): Promise<void> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        let fileContent = '';\n\n        try {\n            fileContent = await fs.readFile(absolutePath, 'utf-8');\n        } catch (error: unknown) {\n            const nodeError = error as NodeJS.ErrnoException;\n            if (nodeError.code !== 'ENOENT' || !options.createIfNotExists) {\n                throw error;\n            }\n        }\n\n        const lines = fileContent.split('\\n');\n        const insertAt = Math.max(0, Math.min(lineNumber - 1, lines.length));\n        lines.splice(insertAt, 0, content);\n\n        await this.writeFile(relativePath, lines.join('\\n'));\n    }\n\n    public async updateFileLines(\n        relativePath: string,\n        predicate: (line: string, lineNumber: number) => string | null | undefined,\n        options: FileEditOptions = {}\n    ): Promise<number> {\n        if (options) {\n            workflowManager.logger?.info?.(`Updating file lines with options: ${JSON.stringify(options)}`);\n        }\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        const content = await fs.readFile(absolutePath, 'utf-8');\n        const lines = content.split('\\n');\n        let modifiedCount = 0;\n        const newLines: string[] = [];\n\n        for (let i = 0; i < lines.length; i++) {\n            const line = lines[i];\n            const result = predicate(line, i + 1);\n\n            if (result === undefined) {\n                // Skip this line (remove it)\n                modifiedCount++;\n            } else if (result !== null) {\n                // Replace the line\n                newLines.push(result);\n                if (result !== line) {\n                    modifiedCount++;\n                }\n            } else {\n                // Keep the line as is\n                newLines.push(line);\n            }\n        }\n\n        if (modifiedCount > 0) {\n            await this.writeFile(relativePath, newLines.join('\\n'));\n        }\n\n        return modifiedCount;\n    }\n\n    /**\n     * Gets a diff between the current file content and a new version.\n     * \n     * @param {string} relativePath - The path to the file\n     * @param {string | Buffer} newContent - The new content to compare against\n     * @param {DiffOptions} [options] - Options for the diff generation\n     * @returns {Promise<string>} A promise that resolves with a diff string in the specified format\n     * @throws {Error} If the file cannot be read or is outside the workspace\n     */\n    public async getFileDiff(\n        relativePath: string,\n        newContent: string | Buffer,\n        options: DiffOptions = {}\n    ): Promise<string> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        workflowManager.logger?.debug?.(`Generating diff for file at ${absolutePath}`);\n        const oldContent = await this.readFile(relativePath);\n\n        return this.diffContent(oldContent, newContent, options);\n    }\n\n    /**\n     * Gets a diff between two versions of content.\n     * \n     * @param {string | Buffer} oldContent - The old content to compare from\n     * @param {string | Buffer} newContent - The new content to compare to\n     * @param {DiffOptions} [options] - Options for the diff generation\n     * @returns {Promise<string>} A promise that resolves with a diff string in the specified format\n     */\n    public async diffContent(\n        oldContent: string | Buffer,\n        newContent: string | Buffer,\n        options: DiffOptions = {}\n    ): Promise<string> {\n        // Convert Buffer to string if needed\n        const oldContentStr = Buffer.isBuffer(oldContent) ? oldContent.toString('utf-8') : oldContent;\n        const newContentStr = Buffer.isBuffer(newContent) ? newContent.toString('utf-8') : newContent;\n\n        // If showInEditor is true, show the diff in VSCode's diff editor\n        if (options.showInEditor !== false && vscode.window) {\n            try {\n                const title = options.title || 'Compare Changes';\n                const diffId = `codessa-diff-${Date.now()}`;\n\n                // Create a unique identifier for this diff session\n                const leftUri = vscode.Uri.parse(`codessa-diff:${diffId}/original`).with({\n                    scheme: 'untitled',\n                    path: `${title} (Original)`\n                });\n\n                const rightUri = vscode.Uri.parse(`codessa-diff:${diffId}/modified`).with({\n                    scheme: 'untitled',\n                    path: `${title} (Modified)`\n                });\n\n                // Create and show the diff editor\n                await vscode.commands.executeCommand(\n                    'vscode.diff',\n                    leftUri,\n                    rightUri,\n                    title,\n                    {\n                        preview: true,\n                        viewColumn: options.viewColumn || vscode.ViewColumn.Beside,\n                        preserveFocus: options.preserveFocus,\n                        selection: options.selection,\n                        revealIfVisible: true\n                    },\n                    {\n                        isReadOnly: true,\n                        label: title,\n                        description: 'Codessa Diff Viewer',\n                        detail: 'Interactive diff viewer for code changes'\n                    }\n                );\n\n                // Get the active text editor (should be the diff editor)\n                const activeEditor = vscode.window.activeTextEditor;\n                if (activeEditor) {\n                    // Apply content to the left and right sides of the diff\n                    const edit = new vscode.WorkspaceEdit();\n\n                    // Clear any existing content\n                    const fullRange = (doc: vscode.TextDocument) =>\n                        new vscode.Range(\n                            doc.positionAt(0),\n                            doc.positionAt(doc.getText().length)\n                        );\n\n                    // Open the documents if they're not already open\n                    const [leftDoc, rightDoc] = await Promise.all([\n                        vscode.workspace.openTextDocument(leftUri),\n                        vscode.workspace.openTextDocument(rightUri)\n                    ]);\n\n                    // Replace entire content in a single edit\n                    edit.replace(leftUri, fullRange(leftDoc), oldContentStr);\n                    edit.replace(rightUri, fullRange(rightDoc), newContentStr);\n\n                    // Apply the edits\n                    await vscode.workspace.applyEdit(edit);\n\n                    // Save the documents to ensure changes are preserved\n                    await Promise.all([\n                        leftDoc.save(),\n                        rightDoc.save()\n                    ]);\n\n                    // Set language mode if available\n                    if (options.languageId) {\n                        await vscode.languages.setTextDocumentLanguage(leftDoc, options.languageId);\n                        await vscode.languages.setTextDocumentLanguage(rightDoc, options.languageId);\n                    }\n                }\n\n                // Return empty string since we're showing the diff in the editor\n                return '';\n            } catch (error) {\n                console.error('Error showing diff in editor:', error);\n                // Fall through to text-based diff on error\n            }\n        }\n\n        // Import the 'diff' module\n        const diffModule = await import('diff');\n        \n        // Use create patch with standard options\n        const diffResult = diffModule.default.createPatch(\n            'original', // original filename\n            'modified', // modified filename\n            oldContentStr,\n            newContentStr\n        );\n\n        // Return the diff result based on the requested format\n        switch (options.format || DiffFormat.Unified) {\n            case DiffFormat.Json:\n                return JSON.stringify({\n                    oldContent: oldContentStr,\n                    newContent: newContentStr,\n                    patch: diffResult\n                }, null, 2);\n            case DiffFormat.Unified:\n            default:\n                return diffResult;\n        }\n    }\n\n    /**\n     * Applies a diff to a file.\n     * \n     * @param {string} relativePath - The path to the file to patch\n     * @param {string} diff - The diff to apply (format is auto-detected)\n     * @param {FileEditOptions} [options] - Options for the patch operation\n     * @returns {Promise<{success: boolean, conflicts: string[]}>} A promise that resolves with the result of the patch operation\n     * @throws {Error} If the patch cannot be applied or the file is outside the workspace\n     */\n    public async applyDiff(\n        relativePath: string,\n        diff: string,\n        options: FileEditOptions & { preview?: boolean } = {}\n    ): Promise<{ success: boolean; conflicts: string[] }> {\n        const absolutePath = this.resolveAndValidatePath(relativePath);\n        const conflicts: string[] = [];\n\n        // Get current file content\n        let currentContent: string;\n        try {\n            currentContent = await this.readFile(relativePath);\n        } catch (error) {\n            throw new Error(`Failed to read file ${relativePath}: ${error instanceof Error ? error.message : String(error)}`);\n        }\n\n        // Import diff module\n        const diffUtils = await import('diff');\n\n        // Apply the diff to get the new content\n        let patched: string | false;\n        try {\n            // Apply patch with basic options\n            patched = diffUtils.applyPatch(currentContent, diff);\n\n            if (patched === false) {\n                throw new Error('Failed to apply diff: patch not applicable');\n            }\n        } catch (error) {\n            conflicts.push(`Failed to apply diff: ${error instanceof Error ? error.message : String(error)}`);\n            return { success: false, conflicts };\n        }\n\n        // If preview is requested, show the diff before applying\n        if (options.preview && vscode.window) {\n            try {\n                const originalUri = vscode.Uri.file(absolutePath);\n                const modifiedUri = originalUri.with({ scheme: 'untitled', query: 'modified' });\n\n                // Create a new untitled document for the modified content\n                const document = await vscode.workspace.openTextDocument(\n                    modifiedUri.with({ scheme: 'untitled' })\n                );\n\n                // Set the content and language mode\n                const edit = new vscode.WorkspaceEdit();\n                edit.insert(modifiedUri, new vscode.Position(0, 0), patched);\n                await vscode.workspace.applyEdit(edit);\n\n                // Get the language ID for syntax highlighting\n                const languageId = await vscode.workspace.openTextDocument(originalUri).then(\n                    doc => doc.languageId,\n                    () => undefined\n                );\n\n                if (languageId) {\n                    await vscode.languages.setTextDocumentLanguage(document, languageId);\n                }\n\n                // Show the diff\n                await vscode.commands.executeCommand(\n                    'vscode.diff',\n                    originalUri,\n                    modifiedUri,\n                    `Preview changes: ${path.basename(relativePath)}`,\n                    { preview: false }\n                );\n\n                // Ask for confirmation before applying\n                const confirm = await vscode.window.showInformationMessage(\n                    'Do you want to apply these changes?',\n                    {\n                        modal: true,\n                        detail: `This will modify ${path.basename(relativePath)}.`\n                    },\n                    'Apply Changes',\n                    'Cancel'\n                );\n\n                if (confirm !== 'Apply Changes') {\n                    return { success: false, conflicts: ['User cancelled the operation'] };\n                }\n            } catch (error) {\n                // If preview fails, log but continue with the operation\n                console.warn('Failed to show diff preview:', error);\n            }\n        }\n\n        // Apply the changes\n        try {\n            // Write the file with the patched content\n            await this.writeFile(relativePath, patched);\n\n            // Reveal the file in the explorer and show success message\n            if (vscode.window) {\n                const uri = vscode.Uri.file(absolutePath);\n                await vscode.commands.executeCommand('revealInExplorer', uri);\n\n                // Show a success message if not in silent mode\n                if (!options.silent) {\n                    vscode.window.showInformationMessage(\n                        `Successfully updated ${path.basename(relativePath)}`,\n                        'Open File'\n                    ).then(choice => {\n                        if (choice === 'Open File') {\n                            vscode.window.showTextDocument(uri);\n                        }\n                    });\n                }\n            }\n\n            return { success: true, conflicts: [] };\n        } catch (error) {\n            throw new Error(`Failed to write file ${relativePath}: ${error instanceof Error ? error.message : String(error)}`);\n        }\n    }\n}\n\n/**\n * VS Code implementation of the IWorkspaceKnowledge interface.\n *\n * This service provides tools with a high-level understanding of the\n * workspace structure, enabling intelligent file discovery and content analysis.\n *\n * Key features:\n * - Hierarchical directory tree generation with customizable ignore patterns\n * - File pattern matching using VS Code's powerful search capabilities\n * - Efficient file content retrieval with workspace-relative paths\n * - Built-in ignore patterns for common directories (.git, node_modules, dist, etc.)\n * - Recursive directory traversal with proper path handling\n * - Integration with VS Code workspace APIs for optimal performance\n *\n * @class VSCodeWorkspaceKnowledge\n * @implements {IWorkspaceKnowledge}\n *\n * @example\n * ```typescript\n * const workspaceKnowledge = new VSCodeWorkspaceKnowledge();\n *\n * // Get the complete workspace structure\n * const tree = await workspaceKnowledge.getDirectoryTree({\n *   ignorePatterns: ['**\\/node_modules/**', '**\\/.git/**']\n * });\n *\n * // Find all TypeScript files\n * const tsFiles = await workspaceKnowledge.findFilesByPattern('**\\/*.ts');\n *\n * // Read specific file content\n * const configContent = await workspaceKnowledge.getFileContent('tsconfig.json');\n * ```\n */\nexport class VSCodeWorkspaceKnowledge implements IWorkspaceKnowledge {\n    /** Default set of directory patterns to ignore during tree building */\n    private readonly defaultIgnore = new Set(['.git', 'node_modules', 'dist', 'build', '.vscode', '__pycache__']);\n\n    /** The root path of the workspace */\n    private readonly workspaceRoot: string;\n\n    constructor() {\n        const root = vscode.workspace.workspaceFolders?.[0];\n        if (!root) {\n            throw new Error('No workspace folder open.');\n        }\n        this.workspaceRoot = root.uri.fsPath;\n    }\n\n    /**\n     * Resolves and validates a path relative to the workspace root.\n     * Ensures the resolved path is within the workspace boundaries to prevent path traversal attacks.\n     * \n     * @private\n     * @param {string} relativePath - The path to resolve and validate\n     * @returns {string} The resolved absolute path\n     * @throws {Error} If the path is outside the workspace boundaries\n     */\n    private _resolveAndValidatePath(relativePath: string): string {\n        // Normalize the path to handle . and .. segments\n        const normalizedPath = path.normalize(relativePath);\n\n        // Resolve to absolute path\n        const absolutePath = path.resolve(this.workspaceRoot, normalizedPath);\n\n        // Verify the path is within the workspace\n        if (!absolutePath.startsWith(this.workspaceRoot)) {\n            throw new Error(`Path \"${relativePath}\" resolves to outside the workspace.`);\n        }\n\n        return absolutePath;\n    }\n\n    /**\n     * Writes content to a file in the workspace.\n     * @param relativePath The path to the file relative to the workspace root\n     * @param content The content to write to the file\n     * @returns A promise that resolves when the file is written\n     * @throws {Error} If the file cannot be written or is outside the workspace\n     */\n    public async writeFile(relativePath: string, content: string | Buffer): Promise<void> {\n        const absolutePath = this._resolveAndValidatePath(relativePath);\n\n        // Ensure the directory exists\n        const dir = path.dirname(absolutePath);\n        await fs.mkdir(dir, { recursive: true });\n\n        // Write the file\n        await fs.writeFile(absolutePath, content);\n    }\n\n    /**\n     * Reads the content of a file in the workspace.\n     * @param relativePath The path to the file relative to the workspace root\n     * @returns A promise that resolves with the file content as a string\n     */\n    public async readFile(relativePath: string): Promise<string> {\n        const absolutePath = this._resolveAndValidatePath(relativePath);\n        return fs.readFile(absolutePath, 'utf8');\n    }\n\n    /**\n     * Retrieves a hierarchical representation of the workspace directory structure.\n     *\n     * This method builds a complete tree structure of the workspace, starting from\n     * the root folder. It recursively traverses all directories and files, while\n     * respecting ignore patterns to avoid including irrelevant or large directories.\n     *\n     * The tree structure provides:\n     * - Relative paths from workspace root\n     * - Proper type discrimination between files and directories\n     * - Hierarchical organization reflecting the actual file system structure\n     * - Efficient traversal capabilities for tools and services\n     *\n     * @param {Object} [options] - Configuration options for directory tree generation\n     * @param {string[]} [options.ignorePatterns] - Additional glob patterns to exclude from the tree\n     * @returns {Promise<DirectoryTree | null>} A promise that resolves to the directory tree or null if no workspace is open\n     * @throws {Error} If there's an error accessing the file system during traversal\n     *\n     * @example\n     * ```typescript\n     * // Get workspace tree ignoring common directories\n     * const tree = await workspaceKnowledge.getDirectoryTree({\n     *   ignorePatterns: ['**\\/node_modules/**', '**\\/.git/**', '**\\/dist/**']\n     * });\n     *\n     * if (tree) {\n     *   console.log('Workspace root:', tree.name);\n     *   console.log('Total items:', tree.children.length);\n     * }\n     *\n     * // Get complete tree including all files\n     * const fullTree = await workspaceKnowledge.getDirectoryTree();\n     * ```\n     */\n    public async getDirectoryTree(options?: { ignorePatterns?: string[] }): Promise<DirectoryTree | null> {\n        const rootFolder = vscode.workspace.workspaceFolders?.[0];\n        if (!rootFolder) {\n            return null;\n        }\n\n        // Combine default ignore patterns with user-specified patterns\n        const ignoreSet = new Set([...this.defaultIgnore, ...(options?.ignorePatterns ?? [])]);\n\n        /**\n         * Recursively builds the directory tree structure.\n         *\n         * This internal function performs the actual tree building by:\n         * 1. Reading directory entries with file type information\n         * 2. Filtering out ignored directories and files\n         * 3. Recursively processing subdirectories\n         * 4. Constructing proper tree nodes with relative paths\n         *\n         * @param {string} dirPath - The absolute path of the directory to process\n         * @returns {Promise<DirectoryTree>} A promise that resolves to the directory tree node\n         */\n        const buildTree = async (dirPath: string): Promise<DirectoryTree> => {\n            // Read directory entries with file type information\n            const entries = await fs.readdir(dirPath, { withFileTypes: true });\n            const children: (DirectoryTree | { path: string; name: string; type: 'file' })[] = [];\n\n            // Process each entry in the directory\n            for (const entry of entries) {\n                // Skip ignored entries\n                if (ignoreSet.has(entry.name)) {\n                    continue;\n                }\n\n                const fullPath = path.join(dirPath, entry.name);\n                const relPath = path.relative(rootFolder.uri.fsPath, fullPath);\n\n                if (entry.isDirectory()) {\n                    // Recursively build subtree for directories\n                    children.push(await buildTree(fullPath));\n                } else {\n                    // Create file entry for regular files\n                    children.push({\n                        path: relPath,\n                        name: entry.name,\n                        type: 'file'\n                    });\n                }\n            }\n\n            // Return the directory tree node\n            return {\n                path: path.relative(rootFolder.uri.fsPath, dirPath),\n                name: path.basename(dirPath),\n                children,\n                type: 'directory'\n            };\n        };\n\n        // Start building the tree from the workspace root\n        return buildTree(rootFolder.uri.fsPath);\n    }\n\n    /**\n     * Finds files in the workspace matching the given glob pattern.\n     *\n     * This method leverages VS Code's powerful file search capabilities to find\n     * files that match the specified glob pattern. It supports all standard glob\n     * patterns and provides efficient searching across the entire workspace.\n     *\n     * Common pattern examples:\n     * - `**\\/*.ts` - All TypeScript files in any subdirectory\n     * - `src/**` - All files in the src directory and subdirectories\n     * - `**\\/*.{js,ts}` - All JavaScript and TypeScript files\n     * - `!**\\/node_modules/**` - Exclude node_modules (when supported)\n     *\n     * @param {string} globPattern - The glob pattern to match files against\n     * @returns {Promise<Uri[]>} A promise that resolves to an array of file URIs that match the pattern\n     * @throws {Error} If the pattern is invalid or if there's an error searching\n     *\n     * @example\n     * ```typescript\n     * // Find all TypeScript files\n     * const tsFiles = await workspaceKnowledge.findFilesByPattern('**\\/*.ts');\n     * console.log(`Found ${tsFiles.length} TypeScript files`);\n     *\n     * // Find configuration files\n     * const configFiles = await workspaceKnowledge.findFilesByPattern('**\\/*.{json,yml,yaml}');\n     *\n     * // Find files in specific directory\n     * const srcFiles = await workspaceKnowledge.findFilesByPattern('src/**');\n     * ```\n     */\n    public async findFilesByPattern(globPattern: string): Promise<Uri[]> {\n        return vscode.workspace.findFiles(globPattern);\n    }\n\n    /**\n     * Reads the content of a file in the workspace.\n     *\n     * This method provides convenient access to file contents using workspace-relative\n     * paths. It internally uses the VSCodeFileSystemManager to handle path resolution,\n     * validation, and secure file access.\n     *\n     * @param {string} relativePath - The path to the file relative to the workspace root\n     * @returns {Promise<string>} A promise that resolves to the file content as a UTF-8 string\n     * @throws {Error} If the file doesn't exist, cannot be read, or is outside the workspace\n     *\n     * @example\n     * ```typescript\n     * // Read package.json\n     * const packageJson = await workspaceKnowledge.getFileContent('package.json');\n     * const packageData = JSON.parse(packageJson);\n     *\n     * // Read source code files\n     * const mainFile = await workspaceKnowledge.getFileContent('src/main.ts');\n     *\n     * // Read configuration files\n     * const config = await workspaceKnowledge.getFileContent('.vscode/settings.json');\n     * ```\n     */\n    public async getFileContent(relativePath: string): Promise<string> {\n        return this.readFile(relativePath);\n    }\n\n    /**\n     * Opens a document in the VS Code editor with precise control over its display and selection.\n     * This method provides a comprehensive way to interact with the editor programmatically.\n     * \n     * @param {string} relativePath - Path to the file relative to workspace root\n     * @param {Object} [options] - Configuration options for the editor\n     * @param {ViewColumn} [options.viewColumn=ViewColumn.Active] - The editor column to open in\n     * @param {boolean} [options.preserveFocus=false] - Whether to keep focus on the current editor\n     * @param {boolean} [options.preview=true] - Open in preview mode (single tab)\n     * @param {Range} [options.selection] - Text selection range to apply\n     * @param {TextEditorRevealType} [options.revealType=TextEditorRevealType.Default] - How to reveal the selection\n     * @param {string} [options.content] - Optional content to write before opening\n     * @returns {Promise<{ editor: TextEditor; document: TextDocument } | undefined>} The editor and document instances\n     * @throws {Error} If the file cannot be accessed or edited\n     * \n     * @example\n     * ```typescript\n     * // Open a file in a new editor group\n     * await fsManager.openInEditor('src/main.ts', {\n     *   viewColumn: ViewColumn.Beside,\n     *   preview: false,\n     *   selection: new Range(new Position(10, 0), new Position(15, 0)),\n     *   revealType: TextEditorRevealType.InCenterIfOutsideViewport\n     * });\n     * \n     * // Open and edit a file with new content\n     * await fsManager.openInEditor('config.json', {\n     *   content: JSON.stringify(config, null, 2),\n     *   viewColumn: ViewColumn.One\n     * });\n     * ```\n     */\n    /**\n     * Opens a document in the VS Code editor with precise control over its display and selection.\n     * This method provides a comprehensive way to interact with the editor programmatically.\n     * \n     * @param {string} relativePath - Path to the file relative to workspace root\n     * @param {Object} [options] - Configuration options for the editor\n     * @param {ViewColumn} [options.viewColumn=ViewColumn.Active] - The editor column to open in\n     * @param {boolean} [options.preserveFocus=false] - Whether to keep focus on the current editor\n     * @param {boolean} [options.preview=true] - Open in preview mode (single tab)\n     * @param {Range} [options.selection] - Text selection range to apply\n     * @param {TextEditorRevealType} [options.revealType=TextEditorRevealType.Default] - How to reveal the selection\n     * @param {string} [options.content] - Optional content to write before opening\n     * @returns {Promise<{ editor: TextEditor; document: TextDocument } | undefined>} The editor and document instances\n     * @throws {Error} If the file cannot be accessed or edited\n     */\n    public async openInEditor(\n        relativePath: string,\n        options: {\n            viewColumn?: ViewColumn;\n            preserveFocus?: boolean;\n            preview?: boolean;\n            selection?: Range;\n            revealType?: TextEditorRevealType;\n            content?: string;\n        } = {}\n    ): Promise<{ editor: TextEditor; document: TextDocument } | undefined> {\n        // Resolve and validate the file path\n        const absolutePath = this._resolveAndValidatePath(relativePath);\n        const uri = vscode.Uri.file(absolutePath);\n\n        try {\n            // Update file content if new content is provided\n            if (options.content !== undefined) {\n                await this.writeFile(relativePath, options.content);\n            }\n\n            // Open the document first to get TextDocument instance\n            const document: TextDocument = await vscode.workspace.openTextDocument(uri);\n\n            // Configure TextDocumentShowOptions with all relevant properties\n            const showOptions: TextDocumentShowOptions = {\n                viewColumn: options.viewColumn ?? vscode.ViewColumn.Active,\n                preserveFocus: options.preserveFocus ?? false,\n                preview: options.preview ?? true\n            };\n\n            // If a selection is provided, configure it in the show options\n            if (options.selection) {\n                showOptions.selection = options.selection;\n            }\n\n            // Show the document in the editor and get the TextEditor instance\n            const editor: TextEditor = await vscode.window.showTextDocument(document, showOptions);\n\n            // If we have both a selection and a reveal type, apply them after the editor is ready\n            if (options.selection && options.revealType) {\n                // Use requestAnimationFrame to ensure the editor is fully initialized\n                requestAnimationFrame(() => {\n                    // Set the selection first\n                    if (options.selection && options.revealType) {\n                        const { start, end } = options.selection;\n                        editor.selection = new vscode.Selection(start, end);\n\n                        // Then reveal the selection with the specified reveal type\n                        editor.revealRange(\n                            options.selection,\n                            options.revealType\n                        );\n                    }\n                });\n            } else if (options.selection) {\n                // If only selection is provided, just set the selection\n                const { start, end } = options.selection;\n                editor.selection = new vscode.Selection(start, end);\n\n                // Reveal with default behavior if no specific reveal type is provided\n                editor.revealRange(\n                    options.selection,\n                    vscode.TextEditorRevealType.Default\n                );\n            } else if (options.revealType) {\n                // If only reveal type is provided, reveal the current selection\n                editor.revealRange(\n                    editor.selection,\n                    options.revealType\n                );\n            }\n\n            return { editor, document };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            throw new Error(`Failed to open file in editor: ${errorMessage}`);\n        }\n    }\n}\n\n/**\n * A lightweight wrapper around the InteractiveSession from pseudoTerminal.ts\n * that provides a simplified interface for terminal operations.\n *\n * This class is provided for backward compatibility. New code should use\n * the InteractiveSession class directly from './pseudoTerminal'.\n *\n * @class TerminalService\n * @implements {ITerminalService}\n * @deprecated Use InteractiveSession from './pseudoTerminal' directly in new code\n */\nexport class TerminalService implements ITerminalService {\n    private readonly logger: ILogger;\n    private session: InteractiveSession | null = null;\n    private readonly onDataEmitter = new vscode.EventEmitter<string>();\n    private readonly onExitEmitter = new vscode.EventEmitter<number>();\n    private readonly onErrorEmitter = new vscode.EventEmitter<Error>();\n    private readonly onReadyEmitter = new vscode.EventEmitter<void>();\n    private readonly onResizeEmitter = new vscode.EventEmitter<{ columns: number; rows: number }>();\n    private readonly onCommandEmitter = new vscode.EventEmitter<string>();\n    private readonly onStateChangeEmitter = new vscode.EventEmitter<TerminalState>();\n    private readonly shell: string;\n\n    constructor(logger: ILogger) {\n        this.logger = logger;\n        this.shell = process.platform === 'win32' ? 'powershell.exe' : (process.env.SHELL || 'bash');\n    }\n\n    private resolveCwd(cwd?: string): string {\n        return cwd || process.cwd();\n    }\n\n    private async ensureSession(cwd?: string): Promise<InteractiveSession> {\n        if (!this.session) {\n            this.session = new InteractiveSession({\n                cwd: cwd || process.cwd(),\n                shell: process.platform === 'win32' ? 'powershell.exe' : (process.env.SHELL || 'bash'),\n                dimensions: { columns: 120, rows: 30 },\n                enableCommandHistory: true,\n                maxCommandHistory: 1000,\n                enableAutoComplete: true,\n                workingDirectoryTracking: true,\n                scrollback: 10000,\n                fontSize: 14,\n                fontFamily: 'Consolas, \"Courier New\", monospace',\n                theme: 'system',\n                hideFromUser: false,\n                isTransient: false,\n                enableBracketedPaste: true,\n                enableUnicodeSupport: true,\n                enableColorSupport: true\n            });\n\n            // Set up event forwarding\n            this.session.onData((data: string) => this.onDataEmitter.fire(data));\n            this.session.onExit((code: number) => {\n                this.onExitEmitter.fire(code);\n                this.session = null; // Clear the session on exit\n            });\n            this.session.onError((error: Error) => this.onErrorEmitter.fire(error));\n            this.session.onReady(() => this.onReadyEmitter.fire());\n            this.session.onResize(dimensions => this.onResizeEmitter.fire(dimensions));\n            this.session.onCommand(cmd => this.onCommandEmitter.fire(cmd));\n            this.session.onStateChange(state => this.onStateChangeEmitter.fire(state));\n\n            // Initialize the session\n            try {\n                await this.session.initialize();\n                this.logger.info('Terminal session initialized successfully');\n            } catch (error: unknown) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                this.logger.error(`Failed to initialize terminal session: ${errorMessage}`);\n                throw error;\n            }\n        }\n        return this.session;\n    }\n\n    async execute(\n        command: string,\n        args: string[],\n        options: {\n            cwd?: string;\n            cancellationToken?: vscode.CancellationToken;\n            timeout?: number;\n            background?: boolean;\n            workingDirectory?: string;\n        } = {}\n    ): Promise<CommandResult> {\n        const session = await this.ensureSession(options.cwd || options.workingDirectory);\n        try {\n            const commandString = [command, ...args].join(' ');\n            const result = await session.executeCommandAdvanced(commandString, {\n                workingDirectory: options.cwd || options.workingDirectory,\n                timeout: options.timeout || 30000, // 30 second default timeout\n                background: options.background || false\n            });\n\n            return {\n                command: commandString,\n                output: result.output,\n                exitCode: result.exitCode,\n                duration: result.duration,\n                success: result.success,\n                error: result.error,\n                completedAt: new Date()\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            this.logger.error(`Failed to execute command: ${errorMessage}`);\n            throw new TerminalError(`Command execution failed: ${errorMessage}`);\n        }\n    }\n\n    /**\n     * Executes a shell command and returns the output.\n     * \n     * @param command The command to execute\n     * @param options Command execution options\n     * @returns The command output as a string\n     */\n    async executeSimple(\n        command: string,\n        options: {\n            cwd?: string;\n            timeout?: number;\n            expectPrompt?: boolean;\n            background?: boolean;\n        } = {}\n    ): Promise<string> {\n        const session = await this.ensureSession(options.cwd);\n        try {\n            return await session.executeCommand(command, {\n                workingDirectory: options.cwd,\n                timeout: options.timeout,\n                expectPrompt: options.expectPrompt,\n                background: options.background\n            });\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            this.logger.error(`Failed to execute simple command: ${errorMessage}`);\n            throw new TerminalError(`Command execution failed: ${errorMessage}`);\n        }\n    }\n\n\n    async createSession(options: { cwd?: string } = {}): Promise<IInteractiveSession> {\n        const session = await this.ensureSession(options.cwd);\n\n        // Create a wrapper that implements IInteractiveSession\n        const wrapper: IInteractiveSession = {\n            id: session.id,\n            onData: this.onDataEmitter.event,\n            onExit: this.onExitEmitter.event,\n            write: (data: string) => session.write(data),\n            resize: (cols: number, rows: number) => session.resize(cols, rows),\n            dispose: () => {\n                session.dispose();\n                this.session = null;\n            }\n        };\n\n        // Add additional methods with type assertions\n        Object.assign(wrapper, {\n            writeLn: (data: string) => session.writeLn(data),\n            clear: () => session.clear(),\n            reset: () => session.reset(),\n            sendSignal: (signal: string) => session.sendSignal(signal),\n            getWorkingDirectory: () => session.getWorkingDirectory(),\n            setWorkingDirectory: (path: string) => session.setWorkingDirectory(path),\n            getCommandHistory: () => session.getCommandHistory(),\n            clearCommandHistory: () => session.clearCommandHistory(),\n            enableLogging: (logPath?: string) => session.enableLogging(logPath),\n            disableLogging: () => session.disableLogging(),\n            state: session.state,\n            stats: session.stats,\n            events: {\n                onData: this.onDataEmitter.event,\n                onExit: this.onExitEmitter.event,\n                onReady: this.onReadyEmitter.event,\n                onResize: this.onResizeEmitter.event,\n                onCommand: this.onCommandEmitter.event,\n                onError: this.onErrorEmitter.event,\n                onStateChange: this.onStateChangeEmitter.event\n            }\n        });\n        \n        return wrapper;\n    }\n\n    /**\n     * Disposes of the terminal service and all associated resources.\n     * This includes the terminal session, all event emitters, and any other resources.\n     */\n    dispose() {\n        if (this.session) {\n            this.session.dispose();\n            this.session = null;\n        }\n\n        // Dispose all event emitters\n        this.onDataEmitter.dispose();\n        this.onExitEmitter.dispose();\n        this.onErrorEmitter.dispose();\n        this.onReadyEmitter.dispose();\n        this.onResizeEmitter.dispose();\n        this.onCommandEmitter.dispose();\n        this.onStateChangeEmitter.dispose();\n    }\n\n    // ======================\n    // Additional Methods\n    // ======================\n\n    /**\n     * Gets the current working directory of the terminal.\n     * @returns A promise that resolves to the current working directory\n     */\n    async getWorkingDirectory(): Promise<string> {\n        const session = await this.ensureSession();\n        return session.getWorkingDirectory();\n    }\n\n    /**\n     * Changes the working directory of the terminal.\n     * @param path The new working directory path\n     * @returns A promise that resolves when the directory is changed\n     */\n    async setWorkingDirectory(path: string): Promise<void> {\n        const session = await this.ensureSession();\n        return session.setWorkingDirectory(path);\n    }\n\n    /**\n     * Gets the command history for the current session.\n     * @returns An array of previously executed commands\n     */\n    getCommandHistory(): string[] {\n        if (!this.session) {\n            return [];\n        }\n        return this.session.getCommandHistory();\n    }\n\n    /**\n     * Clears the command history for the current session.\n     */\n    clearCommandHistory(): void {\n        if (this.session) {\n            this.session.clearCommandHistory();\n        }\n    }\n\n    /**\n     * Enables logging of terminal output to a file.\n     * @param logPath Optional path to the log file. If not provided, a default path is used.\n     */\n    enableLogging(logPath?: string): void {\n        if (this.session) {\n            this.session.enableLogging(logPath);\n        } else {\n            // Store the log path to be used when session is created\n            this.ensureSession().then(session => {\n                session.enableLogging(logPath);\n            });\n        }\n    }\n\n    /**\n     * Disables logging of terminal output.\n     */\n    disableLogging(): void {\n        if (this.session) {\n            this.session.disableLogging();\n        }\n    }\n\n    /**\n     * Resets the terminal to its initial state.\n     * This clears the screen and resets the terminal settings.\n     */\n    async reset(): Promise<void> {\n        const session = await this.ensureSession();\n        return session.reset();\n    }\n\n    /**\n     * Sends a signal to the terminal process.\n     * @param signal The signal to send (e.g., 'SIGINT', 'SIGTERM')\n     */\n    async sendSignal(signal: string): Promise<void> {\n        const session = await this.ensureSession();\n        return session.sendSignal(signal);\n    }\n\n    /**\n     * Gets the current terminal state.\n     * @returns The current terminal state or null if no session exists\n     */\n    getState(): TerminalState | null {\n        return this.session?.state || null;\n    }\n\n    /**\n     * Gets terminal statistics.\n     * @returns Terminal statistics or null if no session exists\n     */\n    getStats(): TerminalStats | null {\n        return this.session?.stats || null;\n    }\n\n    /**\n     * Writes data to the terminal followed by a newline.\n     * @param data The data to write\n     */\n    async writeLn(data: string): Promise<void> {\n        const session = await this.ensureSession();\n        return session.writeLn(data);\n    }\n\n    /**\n     * Writes data to the terminal.\n     * @param data The data to write\n     */\n    async write(data: string): Promise<void> {\n        const session = await this.ensureSession();\n        return session.write(data);\n    }\n\n    /**\n     * Clears the terminal screen.\n     */\n    async clear(): Promise<void> {\n        const session = await this.ensureSession();\n        return session.clear();\n    }\n\n    // ======================\n    // Event Registration\n    // ======================\n\n    /**\n     * Event that fires when data is received from the terminal.\n     */\n    get onData() {\n        return this.onDataEmitter.event;\n    }\n\n    /**\n     * Event that fires when the terminal exits.\n     */\n    get onExit() {\n        return this.onExitEmitter.event;\n    }\n\n    /**\n     * Event that fires when an error occurs.\n     */\n    get onError() {\n        return this.onErrorEmitter.event;\n    }\n\n    /**\n     * Event that fires when the terminal is ready for input.\n     */\n    get onReady() {\n        return this.onReadyEmitter.event;\n    }\n\n    /**\n     * Event that fires when the terminal is resized.\n     */\n    get onResize() {\n        return this.onResizeEmitter.event;\n    }\n\n    /**\n     * Event that fires when a command is executed.\n     */\n    get onCommand() {\n        return this.onCommandEmitter.event;\n    }\n\n    /**\n     * Event that fires when the terminal state changes.\n     */\n    get onStateChange() {\n        return this.onStateChangeEmitter.event;\n    }\n\n    /**\n     * Streams the output of a command in real-time using a callback function.\n     *\n     * This method executes a command and provides real-time output streaming through\n     * a callback function. It's ideal for long-running commands where you need to\n     * process output as it's generated, such as build processes or file watching.\n     *\n     * The streaming supports:\n     * - Real-time data callback for processing output chunks\n     * - Cancellation support for stopping long-running commands\n     * - Cross-platform command execution\n     * - Proper resource cleanup when streaming completes\n     * - Error handling for initialization failures\n     *\n     * @param command The command to execute\n     * @param args Array of command-line arguments\n     * @param options Streaming options\n     * @returns A promise that resolves with the exit code when the command completes\n     */\n    async stream(\n        command: string,\n        args: string[],\n        options: { cwd?: string; onData: (data: string) => void; cancellationToken?: vscode.CancellationToken }\n    ): Promise<{ exitCode: number }> {\n        const session = await this.ensureSession(options.cwd);\n        const commandString = [command, ...args].join(' ');\n\n        return new Promise<{ exitCode: number }>((resolve) => {\n            const disposables: vscode.Disposable[] = [];\n\n            // Handle cancellation\n            if (options.cancellationToken) {\n                const cancelDisposable = options.cancellationToken.onCancellationRequested(() => {\n                    session.dispose();\n                    resolve({ exitCode: -1 });\n                });\n                disposables.push(cancelDisposable);\n            }\n\n            // Handle process exit\n            const exitDisposable = session.onExit((exitCode: number) => {\n                disposables.forEach(d => d.dispose());\n                resolve({ exitCode });\n            });\n            disposables.push(exitDisposable);\n\n            // Forward output to the provided callback\n            const dataDisposable = session.onData((data: string) => {\n                options.onData(data);\n            });\n            disposables.push(dataDisposable);\n\n            // Start the command\n            session.initialize()\n                .then(() => session.writeLn(commandString))\n                .catch(error => {\n                    this.logger.error('Failed to initialize or write to terminal stream:', error);\n                    resolve({ exitCode: -1 });\n                });\n        });\n    }\n}\n\n/**\n * VS Code implementation of the IClipboardService interface.\n *\n * This service provides secure, cross-platform clipboard operations within the VS Code\n * environment, supporting both text and image data with proper error handling and\n * type safety. It serves as a bridge between the tool framework and VS Code's\n * clipboard APIs, ensuring consistent behavior across different platforms.\n *\n * Key features:\n * - Cross-platform clipboard access (Windows, macOS, Linux)\n * - Support for both text and image clipboard content\n * - Multimodal data handling with proper MIME type support\n * - Base64 encoding for image data transport\n * - Comprehensive error handling and validation\n * - Integration with VS Code's clipboard security model\n * - Support for JPEG, PNG, and WebP image formats\n *\n * The service delegates to a specialized clipboard service implementation that\n * handles the platform-specific details of clipboard interaction while providing\n * a clean, consistent API for tools to use.\n *\n * @class VSCodeClipboardService\n * @implements {IClipboardService}\n *\n * @example\n * ```typescript\n * const clipboardService = new VSCodeClipboardService(logger);\n *\n * // Read text from clipboard\n * try {\n *   const text = await clipboardService.readText();\n *   console.log('Clipboard text:', text);\n * } catch (error) {\n *   console.error('Failed to read clipboard:', error);\n * }\n *\n * // Write text to clipboard\n * await clipboardService.writeText('Hello, World!');\n *\n * // Read image from clipboard\n * const imageData = await clipboardService.readImage();\n * if (imageData) {\n *   console.log(`Image (${imageData.mimeType}):`, imageData.base64Data.substring(0, 50) + '...');\n * }\n * ```\n */\nexport class VSCodeClipboardService implements IClipboardService {\n    /** Reference to the underlying clipboard service implementation */\n    private clipboardService!: IClipboardService;\n\n    /**\n     * Creates a new VS Code clipboard service instance.\n     *\n     * This constructor initializes the clipboard service by dynamically loading\n     * and instantiating the specialized clipboard service implementation. It\n     * accepts an optional logger for tracking clipboard operations and debugging.\n     *\n     * The service uses dynamic require to load the clipboard service implementation,\n     * allowing for modular architecture and easier testing. The logger is passed\n     * to the underlying service for consistent logging across the framework.\n     *\n     * @param {ILogger} [logger] - Optional logger instance for tracking clipboard operations\n     *\n     * @example\n     * ```typescript\n     * // Create with logger for debugging\n     * const logger = new VSCodeOutputChannelLogger('Clipboard');\n     * const clipboardService = new VSCodeClipboardService(logger);\n     *\n     * // Create without logger\n     * const clipboardService = new VSCodeClipboardService();\n     * ```\n     */\n    constructor(logger?: ILogger) {\n        import('./clipboardService').then(service => {\n            this.clipboardService = new service.VSCodeClipboardService(logger);\n        });\n    }\n\n    /**\n     * Reads plain text from the system clipboard.\n     *\n     * This method retrieves text content from the system clipboard, if available.\n     * It handles various text encodings and provides proper error handling for\n     * cases where the clipboard doesn't contain text or access is denied.\n     *\n     * The method supports:\n     * - Plain text clipboard content\n     * - Unicode text with proper encoding handling\n     * - Cross-platform clipboard access\n     * - Comprehensive error reporting for access issues\n     *\n     * @returns {Promise<string>} A promise that resolves with the text content of the clipboard\n     * @throws {Error} If reading from the clipboard fails or if the clipboard doesn't contain text\n     *\n     * @example\n     * ```typescript\n     * try {\n     *   const text = await clipboardService.readText();\n     *   console.log('Clipboard contains:', text);\n     *\n     *   // Process the text (e.g., for code generation)\n     *   const generatedCode = await generateCodeFromDescription(text);\n     *   await fileSystem.writeFile('generated.ts', generatedCode);\n     * } catch (error) {\n     *   if (error.message.includes('No text')) {\n     *     console.log('No text in clipboard');\n     *   } else {\n     *     console.error('Clipboard access failed:', error);\n     *   }\n     * }\n     * ```\n     */\n    public async readText(): Promise<string> {\n        return this.clipboardService.readText();\n    }\n\n    /**\n     * Reads an image from the clipboard if available.\n     *\n     * This method attempts to retrieve image data from the system clipboard.\n     * If an image is present, it returns the image data encoded as base64 with\n     * proper MIME type information. If no image is available, it returns null.\n     *\n     * The method supports:\n     * - JPEG, PNG, and WebP image formats\n     * - Base64 encoding for safe transport\n     * - Proper MIME type detection and reporting\n     * - Graceful handling when no image is present\n     * - Cross-platform image clipboard access\n     *\n     * @returns {Promise<MultimodalData | null>} A promise that resolves with the image data\n     *         or null if the clipboard doesn't contain an image\n     * @throws {Error} If reading from the clipboard fails or if the image format is unsupported\n     *\n     * @example\n     * ```typescript\n     * try {\n     *   const imageData = await clipboardService.readImage();\n     *\n     *   if (imageData) {\n     *     console.log(`Found image (${imageData.mimeType})`);\n     *\n     *     // Use with multimodal AI processing\n     *     const prompt: LanguageModelPrompt = [\n     *       { type: 'text', text: 'Describe this image:' },\n     *       {\n     *         type: 'image',\n     *         source: {\n     *           type: 'base64',\n     *           media_type: imageData.mimeType,\n     *           data: imageData.base64Data\n     *         }\n     *       }\n     *     ];\n     *\n     *     const description = await llm.generateResponse(prompt, options);\n     *     console.log('Image description:', description);\n     *   } else {\n     *     console.log('No image in clipboard');\n     *   }\n     * } catch (error) {\n     *   console.error('Failed to read image from clipboard:', error);\n     * }\n     * ```\n     */\n    public async readImage(): Promise<MultimodalData | null> {\n        return this.clipboardService.readImage();\n    }\n\n    /**\n     * Writes text to the system clipboard.\n     *\n     * This method writes the specified text content to the system clipboard,\n     * making it available for pasting in other applications. It handles text\n     * encoding properly and provides error handling for clipboard access issues.\n     *\n     * The method supports:\n     * - Plain text content of any length\n     * - Unicode text with proper encoding\n     * - Cross-platform clipboard writing\n     * - Error handling for access denied scenarios\n     *\n     * @param {string} text - The text to write to the clipboard\n     * @returns {Promise<void>} A promise that resolves when the operation is complete\n     * @throws {Error} If writing to the clipboard fails\n     *\n     * @example\n     * ```typescript\n     * try {\n     *   // Copy generated code to clipboard\n     *   const generatedCode = await generateTypeScriptCode();\n     *   await clipboardService.writeText(generatedCode);\n     *\n     *   console.log('Code copied to clipboard successfully');\n     * } catch (error) {\n     *   console.error('Failed to copy to clipboard:', error);\n     * }\n     *\n     * // Copy user-friendly messages\n     * await clipboardService.writeText('Operation completed successfully!');\n     * ```\n     */\n    public async writeText(text: string): Promise<void> {\n        return this.clipboardService.writeText(text);\n    }\n}\n"]}