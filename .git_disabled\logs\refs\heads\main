0000000000000000000000000000000000000000 0d6bd0bf2899340d7337af06c648a057451<PERSON>ee<PERSON> <PERSON><PERSON> <<EMAIL>> 1745029682 +0200	clone: from https://github.com/djlaserman/Codessa.git
0d6bd0bf2899340d7337af06c648a057451afeeb 1975a9c506236f000989b06359df878b437d9b63 <PERSON><PERSON> <dj<PERSON><EMAIL>> 1745061005 +0200	commit: bring in the blueprints
1975a9c506236f000989b06359df878b437d9b63 2ee3b4b70fdba384a6948eff35bd9d20b61b10a7 Isaki <PERSON>e <<EMAIL>> 1745070891 +0200	commit: more work on the initial functionality
2ee3b4b70fdba384a6948eff35bd9d20b61b10a7 680366487f648a093a34e872a909b34d05ffd9ee <PERSON><PERSON> <dj<PERSON><PERSON>@gmail.com> 1745070909 +0200	pull --tags origin main: Merge made by the 'ort' strategy.
680366487f648a093a34e872a909b34d05ffd9ee 3d42e9e64b434679755f26450d140393c37a7e31 Isaki Dube <<EMAIL>> 1745075895 +0200	pull --tags origin main: Fast-forward
3d42e9e64b434679755f26450d140393c37a7e31 5af336a574c5c4242ac63e0548498711d00ba5ec Isaki Dube <<EMAIL>> 1745199370 +0200	commit: work on chatview and other logic wip not done
5af336a574c5c4242ac63e0548498711d00ba5ec 339194e578709be445a61db0b8c8bb3bdd8c4f4e Isaki Dube <<EMAIL>> 1745234155 +0200	commit: fixed the chat view ui, needs little changes
339194e578709be445a61db0b8c8bb3bdd8c4f4e 0967e5d317a51da99dae242568a43fdf3ffe783c Isaki Dube <<EMAIL>> ********** +0200	commit: more UI work | chat bubbles and some interaction logic
0967e5d317a51da99dae242568a43fdf3ffe783c 3af717b95c91506dc6568ad864a93061151a2383 Isaki Dube <<EMAIL>> ********** +0200	pull --tags origin main: Fast-forward
3af717b95c91506dc6568ad864a93061151a2383 9a3f47ef27a2b9b910e0979f969ae2d6b3e2c77e Isaki Dube <<EMAIL>> ********** +0200	commit: fixed provider errors on startup
9a3f47ef27a2b9b910e0979f969ae2d6b3e2c77e e307b8dac1e7e85b4ca433edd49911ef886682b0 Isaki Dube <<EMAIL>> ********** +0200	commit: fixed agents and notifications
e307b8dac1e7e85b4ca433edd49911ef886682b0 0f4ce4dc0377962481fe869d6c5140a7f8d5ff22 Isaki Dube <<EMAIL>> ********** +0200	commit: agents, settings, memory, provider settings etc.
0f4ce4dc0377962481fe869d6c5140a7f8d5ff22 137db5721a11f09ad1fd3083be54d546096b0481 Isaki Dube <<EMAIL>> ********** +0200	commit: ui, modes and settings upgrades and fixes
137db5721a11f09ad1fd3083be54d546096b0481 d093e8201df437cd9ecf3b80b728ee78a964d7f2 Isaki Dube <<EMAIL>> ********** +0200	commit: general fixes
d093e8201df437cd9ecf3b80b728ee78a964d7f2 0f678525472282f17c39050a659058eb3983c199 Isaki Dube <<EMAIL>> ********** +0200	commit: workflows and workflows registry updated
0f678525472282f17c39050a659058eb3983c199 6b1c138744cc1124b941012ff82b760c4fadf59a Isaki Dube <<EMAIL>> ********** +0200	commit: workflows more fixes
6b1c138744cc1124b941012ff82b760c4fadf59a bfd37355f75dd7ab9e86d5a8ca2e2ffa0e7a2b30 Isaki Dube <<EMAIL>> ********** +0200	commit: workflows updated and compiled packaged okay
bfd37355f75dd7ab9e86d5a8ca2e2ffa0e7a2b30 e36c64b90192eb6aeb0dfac550afb2ed77f9e59f Isaki Dube <<EMAIL>> ********** +0200	commit: upgrade plans to level up
e36c64b90192eb6aeb0dfac550afb2ed77f9e59f 0d1b5e2fb07d82d56ac50db0204881c5b16afe22 Isaki Dube <<EMAIL>> 1749875198 +0200	commit: update and upgrade all features
0d1b5e2fb07d82d56ac50db0204881c5b16afe22 4eb0b06fda82db8fe8bdaddef70592ab338f6332 Isaki Dube <<EMAIL>> 1749945078 +0200	commit: multiple fixes
4eb0b06fda82db8fe8bdaddef70592ab338f6332 062063191c2d9a1b559da1ad6d4cd1ff78b70e22 Isaki Dube <<EMAIL>> 1751124542 +0200	commit: Fixes here an there
062063191c2d9a1b559da1ad6d4cd1ff78b70e22 e5f5caf65cd63f80fa14fbaf25064cca41007ca2 Isaki Dube <<EMAIL>> 1751871598 +0200	commit: fixed some lint errors and warnings
