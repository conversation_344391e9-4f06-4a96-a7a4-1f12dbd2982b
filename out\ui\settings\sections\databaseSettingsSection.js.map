{"version": 3, "file": "databaseSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/databaseSettingsSection.ts"], "names": [], "mappings": ";;AAsBA,sEAYC;AAlCD,uCAAuC;AACvC,kDAAkD;AAClD,SAAS,aAAa,CAAC,GAA8B;IACnD,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,CAAC;AAED,+CAAgD;AAahD,IAAI,mBAAmB,GAAyB,EAAE,CAAC;AACnD,IAAI,YAAY,GAAkB,IAAI,CAAC;AAEvC,SAAgB,6BAA6B,CAAC,SAAsB,EAAE,QAAa;IACjF,qBAAqB;IACrB,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAC;IACtG,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAC/B,uBAAuB;IACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IACzD,IAAI,MAAM;QAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,iBAAiB,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IAC1E,gBAAgB;IAChB,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;IAC/D,IAAI,SAAS;QAAE,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IACtE,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;IAC3D,IAAI,OAAO;QAAE,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACzE,CAAC;AAED,SAAS,mBAAmB,CAAC,SAAsB;IACjD,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,kBAAkB,CAAgB,CAAC;IAC3E,IAAI,CAAC,mBAAmB,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7D,OAAO,CAAC,SAAS,GAAG,iEAAiE,CAAC;QACtF,OAAO;IACT,CAAC;IACD,IAAI,IAAI,GAAG,uCAAuC;QAC5C,wHAAwH;QACxH,sBAAsB,CAAC;IAC7B,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;QACtC,IAAI,IAAI;kBACM,EAAE,CAAC,IAAI,IAAI,EAAE;kBACb,EAAE,CAAC,IAAI,IAAI,EAAE;kBACb,EAAE,CAAC,IAAI,IAAI,EAAE;kBACb,EAAE,CAAC,IAAI,IAAI,EAAE;kBACb,EAAE,CAAC,QAAQ,IAAI,EAAE;kBACjB,EAAE,CAAC,QAAQ,IAAI,EAAE;kBACjB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;;mDAEQ,GAAG;qDACD,GAAG;;cAE1C,CAAC;IACb,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,kBAAkB,CAAC;IAC3B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IACzB,qCAAqC;IACrC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACtE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,cAAc,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAGD,SAAS,iBAAiB,CAAC,SAAsB,EAAE,EAA+B,EAAE,GAAkB;IACpG,YAAY,GAAG,GAAG,CAAC;IACnB,IAAA,iBAAS,EAAC;QACR,KAAK,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,0BAA0B;QAC3E,OAAO,EAAE;;8EAEiE,EAAE,EAAE,IAAI,IAAI,EAAE;8EACd,EAAE,EAAE,IAAI,IAAI,EAAE;8EACd,EAAE,EAAE,IAAI,IAAI,EAAE;gFACZ,EAAE,EAAE,IAAI,IAAI,EAAE;sFACR,EAAE,EAAE,QAAQ,IAAI,EAAE;0FACd,EAAE,EAAE,QAAQ,IAAI,EAAE;yFACnB,EAAE,EAAE,QAAQ,IAAI,EAAE;qEACtC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;SAExF;QACL,SAAS,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,EAAG,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;QACxE,QAAQ,EAAE,GAAG,EAAE,GAAG,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC;KACzC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAsB;IAC/C,+CAA+C;IAC/C,MAAM,aAAa,GAAG,SAAS,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;IACtE,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,YAAY,GAAG,IAAI,CAAC;AACtB,CAAC;AAED,SAAS,YAAY,CAAC,SAAsB,EAAE,QAAa;IACzD,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA4B,CAAC;IACrF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA4B,CAAC;IACrF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA4B,CAAC;IACrF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA4B,CAAC;IACrF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAA4B,CAAC;IAC7F,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAA4B,CAAC;IAC7F,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAA4B,CAAC;IACzF,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAA4B,CAAC;IAC3F,MAAM,EAAE,GAAuB;QAC7B,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;QACnC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;QACnC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;QACnC,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAE,KAAK,IAAI,GAAG,EAAE,EAAE,CAAC;QAC3C,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;QAC3C,QAAQ,EAAE,aAAa,EAAE,KAAK,IAAI,EAAE;QACpC,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE;QACzC,OAAO,EAAE,YAAY,EAAE,OAAO,IAAI,KAAK;KACxC,CAAC;IACF,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QACjD,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAClD,OAAO;IACT,CAAC;IACD,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QACzB,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;SAAM,CAAC;QACN,mBAAmB,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;IACzC,CAAC;IACD,QAAQ,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACnD,mBAAmB,CAAC,SAAS,CAAC,CAAC;AACjC,CAAC;AAGD,SAAS,cAAc,CAAC,SAAsB,EAAE,GAAW;IACzD,IAAA,iBAAS,EAAC;QACR,KAAK,EAAE,4BAA4B;QACnC,OAAO,EAAE,sEAAsE;QAC/E,SAAS,EAAE,GAAG,EAAE;YACd,mBAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;YACnD,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC;QACD,QAAQ,EAAE,GAAG,EAAE,GAAE,CAAC;KACnB,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Database section logic and rendering\n// Utility to ensure parseInt always gets a string\nfunction safeGetString(val: string | null | undefined): string {\n  return typeof val === 'string' ? val : '';\n}\n\nimport { showModal } from '../components/modal';\n\nexport type DatabaseConnection = {\n    name: string;\n    type: string;\n    host: string;\n    port: number;\n    username: string;\n    password: string;\n    database: string;\n    enabled: boolean;\n};\n\nlet databaseConnections: DatabaseConnection[] = [];\nlet editingDbIdx: number | null = null;\n\nexport function renderDatabaseSettingsSection(container: HTMLElement, settings: any) {\n  // Sync from settings\n  databaseConnections = Array.isArray(settings.databaseConnections) ? settings.databaseConnections : [];\n  renderDatabaseTable(container);\n  // Add button listeners\n  const addBtn = document.getElementById('addDatabaseBtn');\n  if (addBtn) addBtn.onclick = () => showDatabaseModal(container, {}, null);\n  // Modal buttons\n  const cancelBtn = document.getElementById('cancelDatabaseBtn');\n  if (cancelBtn) cancelBtn.onclick = () => hideDatabaseModal(container);\n  const saveBtn = document.getElementById('saveDatabaseBtn');\n  if (saveBtn) saveBtn.onclick = () => saveDatabase(container, settings);\n}\n\nfunction renderDatabaseTable(container: HTMLElement) {\n  const section = container.querySelector('#databaseSection') as HTMLElement;\n  if (!databaseConnections || databaseConnections.length === 0) {\n    section.innerHTML = '<div style=\"color:#aaa;\">No database connections defined.</div>';\n    return;\n  }\n  let html = '<table class=\"crud-table\"><thead><tr>' +\n        '<th>Name</th><th>Type</th><th>Host</th><th>Port</th><th>Username</th><th>Database</th><th>Enabled</th><th>Actions</th>' +\n        '</tr></thead><tbody>';\n  databaseConnections.forEach((db, idx) => {\n    html += `<tr>\n            <td>${db.name || ''}</td>\n            <td>${db.type || ''}</td>\n            <td>${db.host || ''}</td>\n            <td>${db.port || ''}</td>\n            <td>${db.username || ''}</td>\n            <td>${db.database || ''}</td>\n            <td>${db.enabled ? 'Yes' : 'No'}</td>\n            <td>\n                <button type=\"button\" data-edit=\"${idx}\">Edit</button>\n                <button type=\"button\" data-delete=\"${idx}\">Delete</button>\n            </td>\n        </tr>`;\n  });\n  html += '</tbody></table>';\n  section.innerHTML = html;\n  // Attach edit/delete event listeners\n  section.querySelectorAll('button[data-edit]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-edit');\n      const idx = parseInt(safeGetString(idxAttr));\n      showDatabaseModal(container, databaseConnections[idx], idx);\n    });\n  });\n  section.querySelectorAll('button[data-delete]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-delete');\n      const idx = parseInt(safeGetString(idxAttr));\n      deleteDatabase(container, idx);\n    });\n  });\n}\n\n\nfunction showDatabaseModal(container: HTMLElement, db: Partial<DatabaseConnection>, idx: number | null) {\n  editingDbIdx = idx;\n  showModal({\n    title: idx == null ? 'Add Database Connection' : 'Edit Database Connection',\n    content: `\n            <form id=\"databaseModalForm\" autocomplete=\"off\" style=\"display:flex;flex-direction:column;gap:12px;min-width:320px;\">\n                <label>Name:<br><input type=\"text\" id=\"databaseName\" value=\"${db?.name || ''}\" required style=\"width:100%\"></label>\n                <label>Type:<br><input type=\"text\" id=\"databaseType\" value=\"${db?.type || ''}\" required style=\"width:100%\"></label>\n                <label>Host:<br><input type=\"text\" id=\"databaseHost\" value=\"${db?.host || ''}\" required style=\"width:100%\"></label>\n                <label>Port:<br><input type=\"number\" id=\"databasePort\" value=\"${db?.port ?? ''}\" required style=\"width:100%\"></label>\n                <label>Username:<br><input type=\"text\" id=\"databaseUsername\" value=\"${db?.username || ''}\" style=\"width:100%\"></label>\n                <label>Password:<br><input type=\"password\" id=\"databasePassword\" value=\"${db?.password || ''}\" style=\"width:100%\"></label>\n                <label>Database Name:<br><input type=\"text\" id=\"databaseDbName\" value=\"${db?.database || ''}\" style=\"width:100%\"></label>\n                <label><input type=\"checkbox\" id=\"databaseEnabled\" ${db?.enabled ? 'checked' : ''}/> Enabled</label>\n            </form>\n        `,\n    onConfirm: () => saveDatabase(container, (window as any).settings || {}),\n    onCancel: () => { editingDbIdx = null; }\n  });\n}\n\nfunction hideDatabaseModal(container: HTMLElement) {\n  // Clear any validation errors in the container\n  const errorElements = container.querySelectorAll('.validation-error');\n  errorElements.forEach(el => el.remove());\n  editingDbIdx = null;\n}\n\nfunction saveDatabase(container: HTMLElement, settings: any) {\n  const nameInput = document.getElementById('databaseName') as HTMLInputElement | null;\n  const typeInput = document.getElementById('databaseType') as HTMLInputElement | null;\n  const hostInput = document.getElementById('databaseHost') as HTMLInputElement | null;\n  const portInput = document.getElementById('databasePort') as HTMLInputElement | null;\n  const usernameInput = document.getElementById('databaseUsername') as HTMLInputElement | null;\n  const passwordInput = document.getElementById('databasePassword') as HTMLInputElement | null;\n  const dbNameInput = document.getElementById('databaseDbName') as HTMLInputElement | null;\n  const enabledInput = document.getElementById('databaseEnabled') as HTMLInputElement | null;\n  const db: DatabaseConnection = {\n    name: nameInput?.value.trim() || '',\n    type: typeInput?.value.trim() || '',\n    host: hostInput?.value.trim() || '',\n    port: parseInt(portInput?.value || '0', 10),\n    username: usernameInput?.value.trim() || '',\n    password: passwordInput?.value || '',\n    database: dbNameInput?.value.trim() || '',\n    enabled: enabledInput?.checked || false,\n  };\n  if (!db.name || !db.type || !db.host || !db.port) {\n    alert('Name, Type, Host, and Port are required.');\n    return;\n  }\n  if (editingDbIdx == null) {\n    databaseConnections.push(db);\n  } else {\n    databaseConnections[editingDbIdx] = db;\n  }\n  settings.databaseConnections = databaseConnections;\n  renderDatabaseTable(container);\n}\n\n\nfunction deleteDatabase(container: HTMLElement, idx: number) {\n  showModal({\n    title: 'Delete Database Connection',\n    content: '<div>Are you sure you want to delete this database connection?</div>',\n    onConfirm: () => {\n      databaseConnections.splice(idx, 1);\n      const settings = (window as any).settings || {};\n      settings.databaseConnections = databaseConnections;\n      renderDatabaseTable(container);\n    },\n    onCancel: () => {}\n  });\n}\n"]}