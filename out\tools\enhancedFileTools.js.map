{"version": 3, "file": "enhancedFileTools.js", "sourceRoot": "", "sources": ["../../src/tools/enhancedFileTools.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,sCAAmC;AAGnC,mDAAgD;AAChD,+BAAgD;AAChD,6BAAwB;AAGxB,+CAAiC;AAGjC,MAAM,OAAO,GAAG,IAAI,kBAAW,CAAC,OAAO,CAAC,CAAC;AACzC,MAAM,OAAO,GAAG,IAAI,kBAAW,EAAE,CAAC;AAElC;;GAEG;AACH,MAAa,sBAAsB;IACxB,EAAE,GAAG,MAAM,CAAC;IACZ,IAAI,GAAG,iCAAiC,CAAC;IACzC,WAAW,GAAG,iLAAiL,CAAC;IAChM,IAAI,GAAG,cAAc,CAAC;IAE/B,oBAAoB;IACZ,MAAM,CAAC,OAAO,GAAG,IAAI,GAAG,EAAgE,CAAC;IACzF,MAAM,CAAU,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;IAElE,OAAO,GAAwB;QACtC,UAAU,EAAE;YACV,WAAW,EAAE,wCAAwC;YACrD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4DAA4D,CAAC;aAC5F,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4DAA4D,CAAC;aAC5F,CAAC;SACH;QACD,WAAW,EAAE;YACX,WAAW,EAAE,0GAA0G;YACvH,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4DAA4D,CAAC;gBAC3F,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;gBACjE,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6DAA6D,CAAC;aAC7G,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4DAA4D,CAAC;gBAC3F,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;gBACjE,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6DAA6D,CAAC;aAC7G,CAAC;SACH;QACD,YAAY,EAAE;YACZ,WAAW,EAAE,sFAAsF;YACnG,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;gBAClF,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;aAC1E,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;gBAClF,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;aAC1E,CAAC;SACH;QACD,WAAW,EAAE;YACX,WAAW,EAAE,qIAAqI;YAClJ,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;gBAClF,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gCAAgC,CAAC;gBAC5D,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;aAC9G,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;gBAClF,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gCAAgC,CAAC;gBAC5D,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;aAC9G,CAAC;SACH;QACD,YAAY,EAAE;YACZ,WAAW,EAAE,2CAA2C;YACxD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8CAA8C,CAAC;gBAC7E,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;aAC3F,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8CAA8C,CAAC;gBAC7E,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;aAC3F,CAAC;SACH;QACD,YAAY,EAAE;YACZ,WAAW,EAAE,6BAA6B;YAC1C,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;gBACnF,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;aAC9G,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;gBACnF,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;aAC9G,CAAC;SACH;QACD,YAAY,EAAE;YACZ,WAAW,EAAE,yCAAyC;YACtD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oBAAoB,CAAC;gBAClD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gBAC9C,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;aAC9G,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oBAAoB,CAAC;gBAClD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gBAC9C,YAAY,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;aAC9G,CAAC;SACH;QACD,UAAU,EAAE;YACV,WAAW,EAAE,4CAA4C;YACzD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBACpD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC;aACxD,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBACpD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC;aACxD,CAAC;SACH;QACD,WAAW,EAAE;YACX,WAAW,EAAE,gDAAgD;YAC7D,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;aAClF,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;aAClF,CAAC;SACH;QACD,WAAW,EAAE;YACX,WAAW,EAAE,mDAAmD;YAChE,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yDAAyD,CAAC;aACxF,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yDAAyD,CAAC;aACxF,CAAC;SACH;QACD,eAAe,EAAE;YACf,WAAW,EAAE,2CAA2C;YACxD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;aAC9D,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;aAC9D,CAAC;SACH;QACD,aAAa,EAAE;YACb,WAAW,EAAE,mCAAmC;YAChD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC;YACpB,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC;SAC1B;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,4FAA4F;QAC5F,MAAM,QAAQ,GAAG,UAAU,IAAI,KAAK,CAAC,MAAgB,CAAC;QAEtD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,oDAAoD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACjG,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B,QAAQ,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC5G,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,4DAA4D;QAC5D,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;QACjC,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC5B,OAAO,WAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC;YACH,QAAQ,QAAQ,EAAE,CAAC;gBACjB,KAAK,UAAU;oBACb,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC3D,KAAK,WAAW;oBACd,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC5D,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC7D,KAAK,WAAW;oBACd,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC5D,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC7D,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC7D,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC7D,KAAK,UAAU;oBACb,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC3D,KAAK,WAAW;oBACd,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC5D,KAAK,WAAW;oBACd,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC5D,KAAK,eAAe;oBAClB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAChE,KAAK,aAAa;oBAChB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC9D;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;wBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU,EAAE,QAAQ;qBACrB,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qBAAqB,QAAQ,KAAK,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACjE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,QAAQ;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,wBAAwB;YACxB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACjE,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACzC,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBAC7E,yCAAyC;oBACzC,OAAO,GAAG,EAAE,CAAC;gBACf,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YAED,iCAAiC;YACjC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEvE,mBAAmB;YACnB,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC3C,OAAO;gBACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,IAAI;aACL,CAAC,CAAC;YAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,8BAA8B,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,4CAA4C;YAC5C,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAE3D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,QAAQ,cAAc,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qBAAqB,QAAQ,eAAe,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,iBAAiB,KAAK,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,iBAAiB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,uDAAuD;QACvD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACrE,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;YACzC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,sBAAsB,CAAC,gBAAgB,EAAE,CAAC;gBACrE,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAChD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,QAAgB;QAC3C,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAE/D,iCAAiC;YACjC,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACvC,IAAI,GAAG,CAAC,MAAM;oBAAE,OAAO,GAAG,CAAC,CAAC,mBAAmB;YACjD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,2CAA2C;YAC7C,CAAC;YAED,wCAAwC;YACxC,OAAO,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;YAC7C,kCAAkC;YAClC,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QAED,sCAAsC;QACtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,QAAQ,qCAAqC,CAAC,CAAC;QACrG,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAAC,KAAgB,EAAE,UAAkB;QAChE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAE1C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7F,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,gCAAgC,QAAQ,6DAA6D;gBAC5G,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACzD,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACrD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,WAAW,CAAC,MAAM,oBAAoB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAClG,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAEtE,IAAI,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC7E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YAC/F,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAClH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,KAAgB,EAAE,UAAkB;QACjE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QACxC,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB;QAErE,IAAI,CAAC,QAAQ,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,4CAA4C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC9G,CAAC;QAED,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACjG,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,gCAAgC,QAAQ,6DAA6D;gBAC5G,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACpC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,QAAQ,oCAAoC,EAAE,KAAK,CAAC,CAAC;gBAC3G,CAAC;YACH,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oBAAoB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC5D,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC3D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,OAAO,CAAC,MAAM,kBAAkB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7F,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,QAAQ,QAAQ,wBAAwB;gBAChD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,IAAI,EAAE,OAAO,CAAC,MAAM;oBACpB,aAAa,EAAE,YAAY;iBAC5B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YACtE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACnH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,KAAgB,EAAE,UAAkB;QAClE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,UAAoB,CAAC;QAE9C,IAAI,CAAC,QAAQ,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC1C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+CAA+C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACjH,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,QAAQ,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7G,CAAC;QAED,IAAI,CAAC;YACH,iCAAiC;YACjC,IAAI,eAAe,GAAG,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACzE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBAC7E,2EAA2E;oBAC3E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,QAAQ,kDAAkD,CAAC,CAAC;oBAC1F,eAAe,GAAG,EAAE,CAAC;gBACvB,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC,CAAC,6BAA6B;gBAC5C,CAAC;YACH,CAAC;YAED,MAAM,KAAK,GAAG,uBAAU,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;YACtF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,QAAQ,EAAE,CAAC,CAAC;YACxE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,YAAY,EAAE,eAAe,CAAC,MAAM;oBACpC,OAAO,EAAE,UAAU,CAAC,MAAM;iBAC3B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACpH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,KAAgB,EAAE,UAAkB;QACjE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;QACpC,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB;QAErE,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACxB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0CAA0C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC5G,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,QAAQ,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7G,CAAC;QAED,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACpC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,QAAQ,oCAAoC,EAAE,KAAK,CAAC,CAAC;gBAC3G,CAAC;YACH,CAAC;YAED,0CAA0C;YAC1C,IAAI,cAAc,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC;gBACH,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACxE,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YACvD,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBAC7E,6DAA6D;oBAC7D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,QAAQ,yDAAyD,CAAC,CAAC;oBAChG,cAAc,GAAG,EAAE,CAAC;gBACtB,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC,CAAC,6BAA6B;gBAC5C,CAAC;YACH,CAAC;YAED,qBAAqB;YACrB,MAAM,cAAc,GAAG,uBAAU,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;YAEpE,IAAI,cAAc,KAAK,KAAK,EAAE,CAAC;gBAC7B,wBAAwB;gBACxB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,yCAAyC,QAAQ,yEAAyE;oBACjI,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,gDAAgD;YAChD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC5E,MAAM,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC3D,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;YAElE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,QAAQ,EAAE,CAAC,CAAC;YAClE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,iCAAiC,QAAQ,GAAG;gBACpD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,aAAa,EAAE,YAAY;iBAC5B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAErE,gDAAgD;YAChD,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBACvC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,QAAQ,2CAA2C,CAAC,CAAC;gBACxF,CAAC;gBAAC,OAAO,YAAY,EAAE,CAAC;oBACtB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qBAAqB,QAAQ,eAAe,EAAE,YAAY,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACpH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,KAAgB,EAAE,UAAkB;QAClE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,OAAO,GAAI,KAAK,CAAC,OAAkB,IAAI,EAAE,CAAC;QAEhD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7F,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,gCAAgC,QAAQ,6DAA6D;gBAC5G,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,+BAA+B;YAC/B,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACxC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB,QAAQ,wCAAwC;oBAC/E,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,4CAA4C;gBAC5C,IAAI,CAAC,CAAC,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC;oBAChF,MAAM,KAAK,CAAC,CAAC,6BAA6B;gBAC5C,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC1D,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAE3D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;YAC9D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,iBAAiB,QAAQ,EAAE;gBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,IAAI,EAAE,OAAO,CAAC,MAAM;iBACrB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACpH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,KAAgB,EAAE,UAAkB;QAClE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB;QAErE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7F,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,gCAAgC,QAAQ,6DAA6D;gBAC5G,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACpC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,QAAQ,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBAC5G,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC1D,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE1C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;YAC9D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,iBAAiB,QAAQ,EAAE;gBACnC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,aAAa,EAAE,YAAY;iBAC5B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAEvE,IAAI,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC7E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YAC/F,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACpH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,KAAgB,EAAE,UAAkB;QAClE,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QACxC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QACxC,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB;QAErE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2CAA2C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7G,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YACvB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,6FAA6F;gBACpG,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBACnC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,OAAO,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBAC3G,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7E,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAEjD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,OAAO,OAAO,OAAO,EAAE,CAAC,CAAC;YAC3E,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,iBAAiB,OAAO,OAAO,OAAO,EAAE;gBAChD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,OAAO;oBACP,OAAO;oBACP,aAAa,EAAE,YAAY;iBAC5B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAE1F,IAAI,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC7E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YACrG,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACpH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAAC,KAAgB,EAAE,UAAkB;QAChE,MAAM,UAAU,GAAG,KAAK,CAAC,UAAoB,CAAC;QAC9C,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAE1C,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC7B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+CAA+C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACjH,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAEpD,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,6FAA6F;gBACpG,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,gBAAgB;YAChB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,SAAS,CAAC,MAAM,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAChF,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEnD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,UAAU,OAAO,QAAQ,EAAE,CAAC,CAAC;YAC9E,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,gBAAgB,UAAU,OAAO,QAAQ,EAAE;gBACnD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,UAAU;oBACV,QAAQ;iBACT;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,SAAS,CAAC,MAAM,OAAO,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAE7F,IAAI,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC7E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,UAAU,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YACxG,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAClH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,KAAgB,EAAE,UAAkB;QACjE,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QAExC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC5F,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qCAAqC,OAAO,6DAA6D;gBAChH,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,uBAAuB;YACvB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9D,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAElD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;YAClE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,sBAAsB,OAAO,EAAE;gBACvC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,OAAO;iBACR;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACzH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,KAAgB,EAAE,UAAkB;QACjE,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,CAAC;QAExC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC5F,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qCAAqC,OAAO,6DAA6D;gBAChH,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,uBAAuB;YACvB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9D,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE9D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;YAClE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,sBAAsB,OAAO,EAAE;gBACvC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,OAAO;iBACR;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAE3E,IAAI,KAAK,YAAY,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC7E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YACnG,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACzH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,KAAgB,EAAE,UAAkB;QACrE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAE1C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7F,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAExD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,QAAQ,EAAE;oBACxC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,yBAAyB,QAAQ,cAAc;gBACvD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;iBACT;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACvH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,KAAgB,EAAE,UAAkB;QACnE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBAChG,QAAQ;gBACR,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;gBACnD,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,UAAU;gBAC3E,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO,CAAC,MAAM;iBACtB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACrH,CAAC;IACH,CAAC;;AAx6BH,wDAy6BC;AAED,+DAA+D;AAClD,QAAA,sBAAsB,GAAG,IAAI,sBAAsB,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from '../logger';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { diffEngine } from '../diff/diffEngine';\nimport { TextDecoder, TextEncoder } from 'util';\nimport { z } from 'zod';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport * as crypto from 'crypto';\nimport * as os from 'os';\n\nconst decoder = new TextDecoder('utf-8');\nconst encoder = new TextEncoder();\n\n/**\n * Enhanced file system tool that combines basic file operations with advanced capabilities\n */\nexport class EnhancedFileSystemTool implements ITool {\n  readonly id = 'file';\n  readonly name = 'Enhanced File System Operations';\n  readonly description = 'Comprehensive file system operations with advanced capabilities including reading, writing, diffing, patching, and file management with automatic backups and integrity checks.';\n  readonly type = 'multi-action';\n\n  // Backup management\n  private static backups = new Map<string, { content: string, timestamp: number, hash: string }>();\n  private static readonly BACKUP_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours\n\n  readonly actions: Record<string, any> = {\n    'readFile': {\n      description: 'Reads the content of a specified file.',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file (relative to workspace root or absolute).')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file (relative to workspace root or absolute).')\n      })\n    },\n    'writeFile': {\n      description: 'Writes content to a specified file, overwriting existing content. Creates the file if it does not exist.',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file (relative to workspace root or absolute).'),\n        content: z.string().describe('The content to write to the file.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before writing. Default is true.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file (relative to workspace root or absolute).'),\n        content: z.string().describe('The content to write to the file.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before writing. Default is true.')\n      })\n    },\n    'createDiff': {\n      description: 'Creates a unified diff patch between the content of a file and new provided content.',\n      schema: z.object({\n        filePath: z.string().describe('Path to the original file (relative or absolute).'),\n        newContent: z.string().describe('The proposed new content for the file.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the original file (relative or absolute).'),\n        newContent: z.string().describe('The proposed new content for the file.')\n      })\n    },\n    'applyDiff': {\n      description: 'Applies a unified diff patch to a specified file. IMPORTANT: The file content should match the state the patch was created against.',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to patch (relative or absolute).'),\n        patch: z.string().describe('The unified diff patch string.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before patching. Default is true.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to patch (relative or absolute).'),\n        patch: z.string().describe('The unified diff patch string.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before patching. Default is true.')\n      })\n    },\n    'createFile': {\n      description: 'Creates a new file at the specified path.',\n      schema: z.object({\n        filePath: z.string().describe('Path to the new file (relative or absolute).'),\n        content: z.string().optional().describe('Initial content for the file. Default is empty.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the new file (relative or absolute).'),\n        content: z.string().optional().describe('Initial content for the file. Default is empty.')\n      })\n    },\n    'deleteFile': {\n      description: 'Deletes the specified file.',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to delete (relative or absolute).'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before deleting. Default is true.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to delete (relative or absolute).'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before deleting. Default is true.')\n      })\n    },\n    'renameFile': {\n      description: 'Renames a file from oldPath to newPath.',\n      schema: z.object({\n        oldPath: z.string().describe('Current file path.'),\n        newPath: z.string().describe('New file path.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before renaming. Default is true.')\n      }),\n      inputSchema: z.object({\n        oldPath: z.string().describe('Current file path.'),\n        newPath: z.string().describe('New file path.'),\n        createBackup: z.boolean().optional().describe('Whether to create a backup before renaming. Default is true.')\n      })\n    },\n    'copyFile': {\n      description: 'Copies a file from sourcePath to destPath.',\n      schema: z.object({\n        sourcePath: z.string().describe('Source file path.'),\n        destPath: z.string().describe('Destination file path.')\n      }),\n      inputSchema: z.object({\n        sourcePath: z.string().describe('Source file path.'),\n        destPath: z.string().describe('Destination file path.')\n      })\n    },\n    'createDir': {\n      description: 'Creates a new directory at the specified path.',\n      schema: z.object({\n        dirPath: z.string().describe('Path to the new directory (relative or absolute).')\n      }),\n      inputSchema: z.object({\n        dirPath: z.string().describe('Path to the new directory (relative or absolute).')\n      })\n    },\n    'deleteDir': {\n      description: 'Deletes the specified directory and its contents.',\n      schema: z.object({\n        dirPath: z.string().describe('Path to the directory to delete (relative or absolute).')\n      }),\n      inputSchema: z.object({\n        dirPath: z.string().describe('Path to the directory to delete (relative or absolute).')\n      })\n    },\n    'restoreBackup': {\n      description: 'Restores a file from backup if available.',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to restore.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to restore.')\n      })\n    },\n    'listBackups': {\n      description: 'Lists all available file backups.',\n      schema: z.object({}),\n      inputSchema: z.object({})\n    }\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // For backward compatibility, check if actionName is undefined and try to get it from input\n    const actionId = actionName || input.action as string;\n\n    if (!actionId) {\n      return {\n        success: false,\n        error: `Action parameter is required. Available actions: ${Object.keys(this.actions).join(', ')}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    const actionInfo = this.actions[actionId];\n\n    if (!actionInfo) {\n      return {\n        success: false,\n        error: `Unknown file system action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    // Pass the rest of the input to the specific action handler\n    const actionInput = { ...input };\n    if ('action' in actionInput) {\n      delete actionInput.action;\n    }\n\n    try {\n      switch (actionId) {\n        case 'readFile':\n          return await this.executeReadFile(actionInput, actionId);\n        case 'writeFile':\n          return await this.executeWriteFile(actionInput, actionId);\n        case 'createDiff':\n          return await this.executeCreateDiff(actionInput, actionId);\n        case 'applyDiff':\n          return await this.executeApplyDiff(actionInput, actionId);\n        case 'createFile':\n          return await this.executeCreateFile(actionInput, actionId);\n        case 'deleteFile':\n          return await this.executeDeleteFile(actionInput, actionId);\n        case 'renameFile':\n          return await this.executeRenameFile(actionInput, actionId);\n        case 'copyFile':\n          return await this.executeCopyFile(actionInput, actionId);\n        case 'createDir':\n          return await this.executeCreateDir(actionInput, actionId);\n        case 'deleteDir':\n          return await this.executeDeleteDir(actionInput, actionId);\n        case 'restoreBackup':\n          return await this.executeRestoreBackup(actionInput, actionId);\n        case 'listBackups':\n          return await this.executeListBackups(actionInput, actionId);\n        default:\n          return {\n            success: false,\n            error: `Unknown action: ${actionId}`,\n            toolId: this.id,\n            actionName: actionId\n          };\n      }\n    } catch (error: any) {\n      Logger.instance.error(`Error executing file action ${actionId}:`, error);\n      return {\n        success: false,\n        error: `Failed to execute ${actionId}: ${error.message || error}`,\n        toolId: this.id,\n        actionName: actionId\n      };\n    }\n  }\n\n  /**\n     * Create a backup of a file before editing\n     */\n  private async createBackup(filePath: string): Promise<string> {\n    try {\n      // Clean up old backups\n      this.cleanupOldBackups();\n\n      // Read the file content\n      const fileUri = this.resolveWorkspacePath(filePath);\n      if (!fileUri) {\n        throw new Error(`Could not resolve file path: ${filePath}`);\n      }\n\n      let content = '';\n      try {\n        const contentUint8 = await vscode.workspace.fs.readFile(fileUri);\n        content = decoder.decode(contentUint8);\n      } catch (error: any) {\n        if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {\n          // If file doesn't exist, backup is empty\n          content = '';\n        } else {\n          throw error;\n        }\n      }\n\n      // Generate a hash of the content\n      const hash = crypto.createHash('sha256').update(content).digest('hex');\n\n      // Store the backup\n      EnhancedFileSystemTool.backups.set(filePath, {\n        content,\n        timestamp: Date.now(),\n        hash\n      });\n\n      Logger.instance.info(`Created backup for ${filePath}`);\n      return hash;\n    } catch (error) {\n      Logger.instance.error(`Failed to create backup of ${filePath}:`, error);\n      throw new Error(`Failed to create backup of ${filePath}: ${error}`);\n    }\n  }\n\n  /**\n     * Restore a file from backup\n     */\n  private async restoreFromBackup(filePath: string): Promise<boolean> {\n    try {\n      const backup = EnhancedFileSystemTool.backups.get(filePath);\n      if (!backup) {\n        return false;\n      }\n\n      const fileUri = this.resolveWorkspacePath(filePath);\n      if (!fileUri) {\n        throw new Error(`Could not resolve file path: ${filePath}`);\n      }\n\n      // Write the backup content back to the file\n      const contentUint8 = encoder.encode(backup.content);\n      await vscode.workspace.fs.writeFile(fileUri, contentUint8);\n\n      Logger.instance.info(`Restored ${filePath} from backup`);\n      return true;\n    } catch (error) {\n      Logger.instance.error(`Failed to restore ${filePath} from backup:`, error);\n      throw new Error(`Failed to restore ${filePath} from backup: ${error}`);\n    }\n  }\n\n  /**\n     * Clean up old backups\n     */\n  private cleanupOldBackups(): void {\n    const now = Date.now();\n    // Convert entries() iterator to array before iterating\n    const entries = Array.from(EnhancedFileSystemTool.backups.entries());\n    for (const [filePath, backup] of entries) {\n      if (now - backup.timestamp > EnhancedFileSystemTool.BACKUP_EXPIRY_MS) {\n        EnhancedFileSystemTool.backups.delete(filePath);\n        Logger.instance.debug(`Cleaned up old backup for ${filePath}`);\n      }\n    }\n  }\n\n  /**\n     * Resolve a workspace path to a vscode.Uri\n     */\n  private resolveWorkspacePath(filePath: string): vscode.Uri | undefined {\n    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n      const workspaceRoot = vscode.workspace.workspaceFolders[0].uri;\n\n      // Check if it's already absolute\n      try {\n        const uri = vscode.Uri.parse(filePath);\n        if (uri.scheme) return uri; // Already absolute\n      } catch (e) {\n        // Ignore parsing errors, treat as relative\n      }\n\n      // If relative, join with workspace root\n      return vscode.Uri.joinPath(workspaceRoot, filePath);\n    } else if (vscode.Uri.parse(filePath).scheme) {\n      // Absolute path outside workspace\n      return vscode.Uri.parse(filePath);\n    }\n\n    // Relative path but no workspace open\n    Logger.instance.warn(`Cannot resolve relative path \"${filePath}\" without an open workspace folder.`);\n    return undefined;\n  }\n\n  /**\n     * Execute read file action\n     */\n  private async executeReadFile(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n\n    if (!filePath) {\n      return { success: false, error: '\\'filePath\\' is required.', toolId: this.id, actionName };\n    }\n\n    const fileUri = this.resolveWorkspacePath(filePath);\n    if (!fileUri) {\n      return {\n        success: false,\n        error: `Could not resolve file path: ${filePath}. Make sure it's relative to an open workspace or absolute.`,\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      Logger.instance.debug(`Reading file: ${fileUri.fsPath}`);\n      const fileContentUint8 = await vscode.workspace.fs.readFile(fileUri);\n      const fileContent = decoder.decode(fileContentUint8);\n      Logger.instance.info(`Successfully read ${fileContent.length} characters from ${fileUri.fsPath}`);\n      return { success: true, output: fileContent, toolId: this.id, actionName };\n    } catch (error: any) {\n      Logger.instance.error(`Error reading file ${fileUri.fsPath}:`, error);\n\n      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {\n        return { success: false, error: `File not found: ${filePath}`, toolId: this.id, actionName };\n      }\n\n      return { success: false, error: `Failed to read file: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute write file action\n     */\n  private async executeWriteFile(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const content = input.content as string;\n    const createBackup = input.createBackup !== false; // Default to true\n\n    if (!filePath || content === undefined) {\n      return { success: false, error: '\\'filePath\\' and \\'content\\' are required.', toolId: this.id, actionName };\n    }\n\n    if (typeof content !== 'string') {\n      return { success: false, error: '\\'content\\' must be a string.', toolId: this.id, actionName };\n    }\n\n    const fileUri = this.resolveWorkspacePath(filePath);\n    if (!fileUri) {\n      return {\n        success: false,\n        error: `Could not resolve file path: ${filePath}. Make sure it's relative to an open workspace or absolute.`,\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Create backup if requested\n      if (createBackup) {\n        try {\n          await this.createBackup(filePath);\n        } catch (error) {\n          Logger.instance.warn(`Failed to create backup for ${filePath}, continuing with write operation:`, error);\n        }\n      }\n\n      Logger.instance.debug(`Writing to file: ${fileUri.fsPath}`);\n      const contentUint8 = encoder.encode(content);\n      await vscode.workspace.fs.writeFile(fileUri, contentUint8);\n      Logger.instance.info(`Successfully wrote ${content.length} characters to ${fileUri.fsPath}`);\n      return {\n        success: true,\n        output: `File ${filePath} written successfully.`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          size: content.length,\n          backupCreated: createBackup\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error writing file ${fileUri.fsPath}:`, error);\n      return { success: false, error: `Failed to write file: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute create diff action\n     */\n  private async executeCreateDiff(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const newContent = input.newContent as string;\n\n    if (!filePath || newContent === undefined) {\n      return { success: false, error: '\\'filePath\\' and \\'newContent\\' are required.', toolId: this.id, actionName };\n    }\n\n    const fileUri = this.resolveWorkspacePath(filePath);\n    if (!fileUri) {\n      return { success: false, error: `Could not resolve file path: ${filePath}.`, toolId: this.id, actionName };\n    }\n\n    try {\n      // Read the original file content\n      let originalContent = '';\n      try {\n        const originalContentUint8 = await vscode.workspace.fs.readFile(fileUri);\n        originalContent = decoder.decode(originalContentUint8);\n      } catch (error: any) {\n        if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {\n          // If file doesn't exist, treat original content as empty for diff creation\n          Logger.instance.debug(`File ${filePath} not found, creating diff against empty content.`);\n          originalContent = '';\n        } else {\n          throw error; // Re-throw other read errors\n        }\n      }\n\n      const patch = diffEngine.createPatch(filePath, filePath, originalContent, newContent);\n      Logger.instance.info(`Successfully created diff patch for ${filePath}`);\n      return {\n        success: true,\n        output: patch,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          originalSize: originalContent.length,\n          newSize: newContent.length\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error creating diff for ${filePath}:`, error);\n      return { success: false, error: `Failed to create diff: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute apply diff action\n     */\n  private async executeApplyDiff(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const patch = input.patch as string;\n    const createBackup = input.createBackup !== false; // Default to true\n\n    if (!filePath || !patch) {\n      return { success: false, error: '\\'filePath\\' and \\'patch\\' are required.', toolId: this.id, actionName };\n    }\n\n    const fileUri = this.resolveWorkspacePath(filePath);\n    if (!fileUri) {\n      return { success: false, error: `Could not resolve file path: ${filePath}.`, toolId: this.id, actionName };\n    }\n\n    try {\n      // Create backup if requested\n      if (createBackup) {\n        try {\n          await this.createBackup(filePath);\n        } catch (error) {\n          Logger.instance.warn(`Failed to create backup for ${filePath}, continuing with patch operation:`, error);\n        }\n      }\n\n      // 1. Read the current content of the file\n      let currentContent = '';\n      try {\n        const currentContentUint8 = await vscode.workspace.fs.readFile(fileUri);\n        currentContent = decoder.decode(currentContentUint8);\n      } catch (error: any) {\n        if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {\n          // If the file doesn't exist, maybe the patch is creating it?\n          Logger.instance.warn(`File ${filePath} not found. Attempting to apply patch to empty content.`);\n          currentContent = '';\n        } else {\n          throw error; // Re-throw other read errors\n        }\n      }\n\n      // 2. Apply the patch\n      const patchedContent = diffEngine.applyPatch(patch, currentContent);\n\n      if (patchedContent === false) {\n        // Patch failed to apply\n        return {\n          success: false,\n          error: `Patch could not be applied cleanly to ${filePath}. The file content may have changed, or the patch is invalid/malformed.`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // 3. Write the patched content back to the file\n      Logger.instance.debug(`Writing patched content back to: ${fileUri.fsPath}`);\n      const patchedContentUint8 = encoder.encode(patchedContent);\n      await vscode.workspace.fs.writeFile(fileUri, patchedContentUint8);\n\n      Logger.instance.info(`Successfully applied patch to ${filePath}`);\n      return {\n        success: true,\n        output: `Patch applied successfully to ${filePath}.`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          backupCreated: createBackup\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error applying patch to ${filePath}:`, error);\n\n      // Try to restore from backup if patching failed\n      if (createBackup) {\n        try {\n          await this.restoreFromBackup(filePath);\n          Logger.instance.info(`Restored ${filePath} from backup after failed patch operation`);\n        } catch (restoreError) {\n          Logger.instance.error(`Failed to restore ${filePath} from backup:`, restoreError);\n        }\n      }\n\n      return { success: false, error: `Failed to apply patch: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute create file action\n     */\n  private async executeCreateFile(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const content = (input.content as string) || '';\n\n    if (!filePath) {\n      return { success: false, error: '\\'filePath\\' is required.', toolId: this.id, actionName };\n    }\n\n    const fileUri = this.resolveWorkspacePath(filePath);\n    if (!fileUri) {\n      return {\n        success: false,\n        error: `Could not resolve file path: ${filePath}. Make sure it's relative to an open workspace or absolute.`,\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Check if file already exists\n      try {\n        await vscode.workspace.fs.stat(fileUri);\n        return {\n          success: false,\n          error: `File already exists: ${filePath}. Use 'writeFile' action to overwrite.`,\n          toolId: this.id,\n          actionName\n        };\n      } catch (error: any) {\n        // File doesn't exist, which is what we want\n        if (!(error instanceof vscode.FileSystemError && error.code === 'FileNotFound')) {\n          throw error; // Re-throw unexpected errors\n        }\n      }\n\n      // Create the file\n      Logger.instance.debug(`Creating file: ${fileUri.fsPath}`);\n      const contentUint8 = encoder.encode(content);\n      await vscode.workspace.fs.writeFile(fileUri, contentUint8);\n\n      Logger.instance.info(`Successfully created file ${filePath}`);\n      return {\n        success: true,\n        output: `File created: ${filePath}`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          size: content.length\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error creating file ${fileUri.fsPath}:`, error);\n      return { success: false, error: `Failed to create file: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute delete file action\n     */\n  private async executeDeleteFile(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const createBackup = input.createBackup !== false; // Default to true\n\n    if (!filePath) {\n      return { success: false, error: '\\'filePath\\' is required.', toolId: this.id, actionName };\n    }\n\n    const fileUri = this.resolveWorkspacePath(filePath);\n    if (!fileUri) {\n      return {\n        success: false,\n        error: `Could not resolve file path: ${filePath}. Make sure it's relative to an open workspace or absolute.`,\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Create backup if requested\n      if (createBackup) {\n        try {\n          await this.createBackup(filePath);\n        } catch (error) {\n          Logger.instance.warn(`Failed to create backup for ${filePath}, continuing with delete operation:`, error);\n        }\n      }\n\n      // Delete the file\n      Logger.instance.debug(`Deleting file: ${fileUri.fsPath}`);\n      await vscode.workspace.fs.delete(fileUri);\n\n      Logger.instance.info(`Successfully deleted file ${filePath}`);\n      return {\n        success: true,\n        output: `File deleted: ${filePath}`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          backupCreated: createBackup\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error deleting file ${fileUri.fsPath}:`, error);\n\n      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {\n        return { success: false, error: `File not found: ${filePath}`, toolId: this.id, actionName };\n      }\n\n      return { success: false, error: `Failed to delete file: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute rename file action\n     */\n  private async executeRenameFile(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const oldPath = input.oldPath as string;\n    const newPath = input.newPath as string;\n    const createBackup = input.createBackup !== false; // Default to true\n\n    if (!oldPath || !newPath) {\n      return { success: false, error: '\\'oldPath\\' and \\'newPath\\' are required.', toolId: this.id, actionName };\n    }\n\n    const oldUri = this.resolveWorkspacePath(oldPath);\n    const newUri = this.resolveWorkspacePath(newPath);\n\n    if (!oldUri || !newUri) {\n      return {\n        success: false,\n        error: 'Could not resolve file paths. Make sure they\\'re relative to an open workspace or absolute.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Create backup if requested\n      if (createBackup) {\n        try {\n          await this.createBackup(oldPath);\n        } catch (error) {\n          Logger.instance.warn(`Failed to create backup for ${oldPath}, continuing with rename operation:`, error);\n        }\n      }\n\n      // Rename the file\n      Logger.instance.debug(`Renaming file: ${oldUri.fsPath} -> ${newUri.fsPath}`);\n      await vscode.workspace.fs.rename(oldUri, newUri);\n\n      Logger.instance.info(`Successfully renamed file ${oldPath} -> ${newPath}`);\n      return {\n        success: true,\n        output: `Renamed file: ${oldPath} -> ${newPath}`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          oldPath,\n          newPath,\n          backupCreated: createBackup\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error renaming file ${oldUri.fsPath} -> ${newUri.fsPath}:`, error);\n\n      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {\n        return { success: false, error: `Source file not found: ${oldPath}`, toolId: this.id, actionName };\n      }\n\n      return { success: false, error: `Failed to rename file: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute copy file action\n     */\n  private async executeCopyFile(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const sourcePath = input.sourcePath as string;\n    const destPath = input.destPath as string;\n\n    if (!sourcePath || !destPath) {\n      return { success: false, error: '\\'sourcePath\\' and \\'destPath\\' are required.', toolId: this.id, actionName };\n    }\n\n    const sourceUri = this.resolveWorkspacePath(sourcePath);\n    const destUri = this.resolveWorkspacePath(destPath);\n\n    if (!sourceUri || !destUri) {\n      return {\n        success: false,\n        error: 'Could not resolve file paths. Make sure they\\'re relative to an open workspace or absolute.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Copy the file\n      Logger.instance.debug(`Copying file: ${sourceUri.fsPath} -> ${destUri.fsPath}`);\n      await vscode.workspace.fs.copy(sourceUri, destUri);\n\n      Logger.instance.info(`Successfully copied file ${sourcePath} -> ${destPath}`);\n      return {\n        success: true,\n        output: `Copied file: ${sourcePath} -> ${destPath}`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          sourcePath,\n          destPath\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error copying file ${sourceUri.fsPath} -> ${destUri.fsPath}:`, error);\n\n      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {\n        return { success: false, error: `Source file not found: ${sourcePath}`, toolId: this.id, actionName };\n      }\n\n      return { success: false, error: `Failed to copy file: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute create directory action\n     */\n  private async executeCreateDir(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const dirPath = input.dirPath as string;\n\n    if (!dirPath) {\n      return { success: false, error: '\\'dirPath\\' is required.', toolId: this.id, actionName };\n    }\n\n    const dirUri = this.resolveWorkspacePath(dirPath);\n    if (!dirUri) {\n      return {\n        success: false,\n        error: `Could not resolve directory path: ${dirPath}. Make sure it's relative to an open workspace or absolute.`,\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Create the directory\n      Logger.instance.debug(`Creating directory: ${dirUri.fsPath}`);\n      await vscode.workspace.fs.createDirectory(dirUri);\n\n      Logger.instance.info(`Successfully created directory ${dirPath}`);\n      return {\n        success: true,\n        output: `Directory created: ${dirPath}`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          dirPath\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error creating directory ${dirUri.fsPath}:`, error);\n      return { success: false, error: `Failed to create directory: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute delete directory action\n     */\n  private async executeDeleteDir(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const dirPath = input.dirPath as string;\n\n    if (!dirPath) {\n      return { success: false, error: '\\'dirPath\\' is required.', toolId: this.id, actionName };\n    }\n\n    const dirUri = this.resolveWorkspacePath(dirPath);\n    if (!dirUri) {\n      return {\n        success: false,\n        error: `Could not resolve directory path: ${dirPath}. Make sure it's relative to an open workspace or absolute.`,\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Delete the directory\n      Logger.instance.debug(`Deleting directory: ${dirUri.fsPath}`);\n      await vscode.workspace.fs.delete(dirUri, { recursive: true });\n\n      Logger.instance.info(`Successfully deleted directory ${dirPath}`);\n      return {\n        success: true,\n        output: `Directory deleted: ${dirPath}`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          dirPath\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error deleting directory ${dirUri.fsPath}:`, error);\n\n      if (error instanceof vscode.FileSystemError && error.code === 'FileNotFound') {\n        return { success: false, error: `Directory not found: ${dirPath}`, toolId: this.id, actionName };\n      }\n\n      return { success: false, error: `Failed to delete directory: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute restore backup action\n     */\n  private async executeRestoreBackup(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n\n    if (!filePath) {\n      return { success: false, error: '\\'filePath\\' is required.', toolId: this.id, actionName };\n    }\n\n    try {\n      const restored = await this.restoreFromBackup(filePath);\n\n      if (!restored) {\n        return {\n          success: false,\n          error: `No backup found for ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      return {\n        success: true,\n        output: `Successfully restored ${filePath} from backup`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error(`Error restoring backup for ${filePath}:`, error);\n      return { success: false, error: `Failed to restore backup: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n\n  /**\n     * Execute list backups action\n     */\n  private async executeListBackups(input: ToolInput, actionName: string): Promise<ToolResult> {\n    try {\n      const backups = Array.from(EnhancedFileSystemTool.backups.entries()).map(([filePath, backup]) => ({\n        filePath,\n        timestamp: new Date(backup.timestamp).toISOString(),\n        age: Math.round((Date.now() - backup.timestamp) / (60 * 1000)) + ' minutes',\n        hash: backup.hash\n      }));\n\n      return {\n        success: true,\n        output: backups,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          count: backups.length\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error('Error listing backups:', error);\n      return { success: false, error: `Failed to list backups: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n\n// Export a singleton instance of the enhanced file system tool\nexport const enhancedFileSystemTool = new EnhancedFileSystemTool();\n"]}