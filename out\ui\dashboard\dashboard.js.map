{"version": 3, "file": "dashboard.js", "sourceRoot": "", "sources": ["../../../src/ui/dashboard/dashboard.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,8CAA8C;AAC9C,yCAAsC;AACtC,qDAAkD;AAClD,2EAAwE;AAExE,2DAAwD;AACxD,0FAAqF;AAErF;;GAEG;AACH,MAAa,cAAc;IAClB,MAAM,CAAC,YAAY,CAA6B;IAC/C,MAAM,CAAU,QAAQ,GAAG,kBAAkB,CAAC;IAErC,MAAM,CAAsB;IAC5B,aAAa,CAAa;IAC1B,QAAQ,CAA0B;IAC3C,YAAY,GAAwB,EAAE,CAAC;IACvC,gBAAgB,CAA2B;IAEnD;;SAEK;IACE,MAAM,CAAC,YAAY,CACxB,YAAwB,EACxB,OAAgC;QAEhC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YAC3C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEd,sCAAsC;QACtC,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;YAChC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAClD,OAAO,cAAc,CAAC,YAAY,CAAC;QACrC,CAAC;QAED,gCAAgC;QAChC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACxD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,cAAc,CAAC,QAAQ,EACvB,mBAAmB,EACnB,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;gBACE,aAAa,EAAE,IAAI;gBACnB,uBAAuB,EAAE,IAAI;gBAC7B,kBAAkB,EAAE;oBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;oBAC1C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC;iBAC/C;aACF,CACF,CAAC;YACF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAC9D,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAExE,wCAAwC;YACxC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAElC,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2DAA2D,CAAC,CAAC;YAC5F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,YACE,KAA0B,EAC1B,YAAwB,EACxB,OAAgC;QAEhC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,2BAA2B;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,yCAAyC;QACzC,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACrC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChB,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxB,KAAK,SAAS;oBACZ,IAAI,CAAC,OAAO,EAAE,CAAC;oBACf,MAAM;gBACR,KAAK,cAAc;oBACjB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;oBACvD,MAAM;gBACR,KAAK,sBAAsB;oBACzB,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;wBACvB,wCAAwC;wBACxC,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;wBAClE,IAAI,QAAQ,EAAE,CAAC;4BACb,+CAAqB,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAC5F,CAAC;6BAAM,CAAC;4BACN,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,OAAO,CAAC,UAAU,aAAa,CAAC,CAAC;wBAC9E,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,iCAAiC;wBACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;oBACjE,CAAC;oBACD,MAAM;gBACR,KAAK,WAAW;oBACd,MAAM,CAAC,QAAQ,CAAC,cAAc,CAC5B,+BAA+B,EAC/B,OAAO,CAAC,OAAO,CAChB,CAAC;oBACF,MAAM;gBACR,KAAK,aAAa;oBAChB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;oBACnD,MAAM;gBACR,KAAK,UAAU;oBACb,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;oBACnD,MAAM;YACV,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CAClB,CAAC;QAEF,0BAA0B;QAC1B,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC;IACrC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,OAAO;QACnB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACtD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,mBAAmB,CAAC;YAExC,gCAAgC;YAChC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACpD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAE9D,sBAAsB;YACtB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAC3D,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAC/D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6DAA6D,CAAC,CAAC;QAChG,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,SAAS,GAAG,uBAAU,CAAC,eAAe,EAAE,CAAC;YAC/C,MAAM,mBAAmB,GAAG,uBAAU,CAAC,sBAAsB,EAAE,CAAC;YAChE,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;gBAClE,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBAClD,OAAO;oBACL,EAAE;oBACF,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK;oBACtD,SAAS,EAAE,KAAK,EAAE,kCAAkC;iBACrD,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC;YAEJ,uBAAuB;YACvB,MAAM,eAAe,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;YAC9D,IAAI,eAAe,EAAE,CAAC;gBACpB,MAAM,qBAAqB,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,UAAU,CAAC,CAAC;gBAC5F,IAAI,qBAAqB,EAAE,CAAC;oBAC1B,qBAAqB,CAAC,SAAS,GAAG,IAAI,CAAC;gBACzC,CAAC;YACH,CAAC;YAED,aAAa;YACb,MAAM,MAAM,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC,KAAY,EAAE,EAAE,CAAC,CAAC;gBAC9E,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;gBACpC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;gBAC3B,QAAQ,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,IAAI,SAAS;gBAChD,KAAK,EAAE,KAAK,CAAC,SAAS,EAAE,OAAO,IAAI,SAAS;aAC7C,CAAC,CAAC,CAAC;YAEJ,YAAY;YACZ,MAAM,KAAK,GAAG,2BAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC7D,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE;gBAC1B,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;aACpC,CAAC,CAAC,CAAC;YAEJ,uBAAuB;YACvB,OAAO;gBACL,SAAS,EAAE;oBACT,KAAK,EAAE,SAAS,CAAC,MAAM;oBACvB,UAAU,EAAE,mBAAmB,CAAC,MAAM;oBACtC,IAAI,EAAE,cAAc;iBACrB;gBACD,MAAM,EAAE;oBACN,KAAK,EAAE,MAAM,CAAC,MAAM;oBACpB,IAAI,EAAE,MAAM;iBACb;gBACD,KAAK,EAAE;oBACL,KAAK,EAAE,KAAK,CAAC,MAAM;oBACnB,IAAI,EAAE,KAAK;iBACZ;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAChE,sBAAsB;YACtB,OAAO;gBACL,KAAK,EAAE,0BAA0B,KAAK,EAAE;gBACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,kBAAkB,CAAC,OAAuB,EAAE,IAAS;QAC3D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAC3D,IAAI,CAAC;YACH,oBAAoB;YACpB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CACpC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,CAAC,CACjE,CAAC;YACF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,SAAS,EAAE,CAAC,CAAC;YAEjD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CACnC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,eAAe,CAAC,CAClE,CAAC;YACF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,QAAQ,EAAE,CAAC,CAAC;YAE/C,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,kBAAkB,CAAC,CACzE,CAAC;YACF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,OAAO,EAAE,CAAC,CAAC;YAE7C,MAAM,KAAK,GAAG,IAAA,gBAAQ,GAAE,CAAC;YACzB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAE9D,OAAO;;;;;;+CAMkC,QAAQ;kGAC2C,OAAO,CAAC,SAAS,8BAA8B,KAAK,gBAAgB,OAAO,CAAC,SAAS,8BAA8B,OAAO,CAAC,SAAS;;;;;;wCAM9L,OAAO;;;;;;;;;;;;;;;;;;;;;8DAqBe,IAAI,CAAC,SAAS,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB;;;qEAGxD,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC;;8DAEpE,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB;;;qEAGhD,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC;;;;;qEAKvB,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC;;;;;;;;;;;;;;;sCAerD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,8BAA8B,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE;sCAClE,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtF,oGAAoG,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;iCAuBlF,KAAK;;4CAEM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;iCAE/B,KAAK,UAAU,SAAS;;oBAErC,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO;;;;;;;;;uBASU,KAAK;;oBAER,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,cAAc,CAAC,YAAY,GAAG,SAAS,CAAC;QACxC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,gBAAqC,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAC3C,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;;AAlXH,wCAmXC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { getNonce } from '../utilities/utils';\nimport { Logger } from '../../logger';\nimport { llmService } from '../../llm/llmService';\nimport { AgentManager } from '../../agents/agentUtilities/agentManager';\nimport { Agent } from '../../agents/agentUtilities/agent';\nimport { ToolRegistry } from '../../tools/toolRegistry';\nimport { ProviderSettingsPanel } from '../settings/sections/providerSettingsSection';\n\n/**\n * A dashboard webview that shows activity, analytics, and health status\n */\nexport class DashboardPanel {\n  public static currentPanel: DashboardPanel | undefined;\n  private static readonly viewType = 'codessaDashboard';\n\n  private readonly _panel: vscode.WebviewPanel;\n  private readonly _extensionUri: vscode.Uri;\n  private readonly _context: vscode.ExtensionContext;\n  private _disposables: vscode.Disposable[] = [];\n  private _refreshInterval: NodeJS.Timer | undefined;\n\n  /**\n     * Create or show a dashboard panel\n     */\n  public static createOrShow(\n    extensionUri: vscode.Uri,\n    context: vscode.ExtensionContext\n  ): DashboardPanel {\n    Logger.instance.info('Creating or showing dashboard panel...');\n    const column = vscode.window.activeTextEditor\n      ? vscode.window.activeTextEditor.viewColumn\n      : undefined;\n\n    // If we already have a panel, show it\n    if (DashboardPanel.currentPanel) {\n      DashboardPanel.currentPanel._panel.reveal(column);\n      return DashboardPanel.currentPanel;\n    }\n\n    // Otherwise, create a new panel\n    Logger.instance.info('Creating new dashboard panel...');\n    try {\n      const panel = vscode.window.createWebviewPanel(\n        DashboardPanel.viewType,\n        'Codessa Dashboard',\n        column || vscode.ViewColumn.One,\n        {\n          enableScripts: true,\n          retainContextWhenHidden: true,\n          localResourceRoots: [\n            vscode.Uri.joinPath(extensionUri, 'media'),\n            vscode.Uri.joinPath(extensionUri, 'resources')\n          ]\n        }\n      );\n      Logger.instance.info('Dashboard panel created successfully.');\n      const dashboardPanel = new DashboardPanel(panel, extensionUri, context);\n\n      // Register panel with extension context\n      context.subscriptions.push(panel);\n\n      return dashboardPanel;\n    } catch (error) {\n      Logger.instance.error('Error creating dashboard panel:', error);\n      vscode.window.showErrorMessage('Failed to create dashboard panel. Check logs for details.');\n      throw error;\n    }\n  }\n\n  private constructor(\n    panel: vscode.WebviewPanel,\n    extensionUri: vscode.Uri,\n    context: vscode.ExtensionContext\n  ) {\n    this._panel = panel;\n    this._extensionUri = extensionUri;\n    this._context = context;\n\n    // Set initial HTML content\n    this._update();\n\n    // Set up auto-refresh (every 30 seconds)\n    this._refreshInterval = setInterval(() => {\n      this._update();\n    }, 30000);\n\n    // Handle panel disposal\n    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);\n\n    // Handle messages from the webview\n    this._panel.webview.onDidReceiveMessage(\n      async (message) => {\n        switch (message.command) {\n          case 'refresh':\n            this._update();\n            break;\n          case 'openSettings':\n            vscode.commands.executeCommand('codessa.openSettings');\n            break;\n          case 'openProviderSettings':\n            if (message.providerId) {\n              // Open settings for a specific provider\n              const provider = await llmService.getProvider(message.providerId);\n              if (provider) {\n                ProviderSettingsPanel.createOrShow(this._extensionUri, message.providerId, this._context);\n              } else {\n                vscode.window.showErrorMessage(`Provider ${message.providerId} not found.`);\n              }\n            } else {\n              // Open provider selection dialog\n              vscode.commands.executeCommand('codessa.openProviderSettings');\n            }\n            break;\n          case 'openAgent':\n            vscode.commands.executeCommand(\n              'codessa.openAgentDetailsPanel',\n              message.agentId\n            );\n            break;\n          case 'createAgent':\n            vscode.commands.executeCommand('codessa.addAgent');\n            break;\n          case 'showLogs':\n            vscode.commands.executeCommand('codessa.showLogs');\n            break;\n        }\n      },\n      null,\n      this._disposables\n    );\n\n    // Cache the current panel\n    DashboardPanel.currentPanel = this;\n  }\n\n  /**\n     * Update dashboard content\n     */\n  private async _update() {\n    Logger.instance.info('Updating dashboard content...');\n    try {\n      const webview = this._panel.webview;\n      this._panel.title = 'Codessa Dashboard';\n\n      // Gather data for the dashboard\n      Logger.instance.info('Gathering dashboard data...');\n      const dashboardData = await this._getDashboardData();\n      Logger.instance.info('Dashboard data gathered successfully.');\n\n      // Update HTML content\n      Logger.instance.info('Generating webview HTML content...');\n      webview.html = this._getWebviewContent(webview, dashboardData);\n      Logger.instance.info('Dashboard content updated successfully.');\n    } catch (error) {\n      Logger.instance.error('Error updating dashboard content:', error);\n      vscode.window.showErrorMessage('Failed to update dashboard content. Check logs for details.');\n    }\n  }\n\n  /**\n     * Gather data for the dashboard\n     */\n  private async _getDashboardData(): Promise<any> {\n    try {\n      // Get provider status\n      const providers = llmService.listProviderIds();\n      const configuredProviders = llmService.getConfiguredProviders();\n      const providerStatus = await Promise.all(providers.map(async (id) => {\n        const provider = await llmService.getProvider(id);\n        return {\n          id,\n          configured: provider ? provider.isConfigured() : false,\n          isDefault: false, // Will be set below if applicable\n        };\n      }));\n\n      // Get default provider\n      const defaultProvider = await llmService.getDefaultProvider();\n      if (defaultProvider) {\n        const defaultProviderStatus = providerStatus.find(p => p.id === defaultProvider.providerId);\n        if (defaultProviderStatus) {\n          defaultProviderStatus.isDefault = true;\n        }\n      }\n\n      // Get agents\n      const agents = AgentManager.getInstance().getAllAgents().map((agent: Agent) => ({\n        id: agent.id,\n        name: agent.name,\n        description: agent.description || '',\n        toolCount: agent.tools.size,\n        provider: agent.llmConfig?.provider || 'unknown',\n        model: agent.llmConfig?.modelId || 'unknown'\n      }));\n\n      // Get tools\n      const tools = ToolRegistry.instance.getAllTools().map(tool => ({\n        id: tool.id,\n        name: tool.name || tool.id,\n        description: tool.description || ''\n      }));\n\n      // Return combined data\n      return {\n        providers: {\n          total: providers.length,\n          configured: configuredProviders.length,\n          list: providerStatus\n        },\n        agents: {\n          total: agents.length,\n          list: agents\n        },\n        tools: {\n          total: tools.length,\n          list: tools\n        },\n        timestamp: new Date().toISOString()\n      };\n    } catch (error) {\n      Logger.instance.error('Error gathering dashboard data:', error);\n      // Return minimal data\n      return {\n        error: `Failed to gather data: ${error}`,\n        timestamp: new Date().toISOString()\n      };\n    }\n  }\n\n  /**\n     * Generate the webview HTML content\n     */\n  private _getWebviewContent(webview: vscode.Webview, data: any): string {\n    Logger.instance.info('Generating webview HTML content...');\n    try {\n      // Get resource URIs\n      Logger.instance.info('Getting resource URIs...');\n      const scriptUri = webview.asWebviewUri(\n        vscode.Uri.joinPath(this._extensionUri, 'media', 'dashboard.js')\n      );\n      Logger.instance.info(`Script URI: ${scriptUri}`);\n\n      const styleUri = webview.asWebviewUri(\n        vscode.Uri.joinPath(this._extensionUri, 'media', 'dashboard.css')\n      );\n      Logger.instance.info(`Style URI: ${styleUri}`);\n\n      const logoUri = webview.asWebviewUri(\n        vscode.Uri.joinPath(this._extensionUri, 'resources', 'codessa-logo.png')\n      );\n      Logger.instance.info(`Logo URI: ${logoUri}`);\n\n      const nonce = getNonce();\n      Logger.instance.info('Resource URIs generated successfully.');\n\n      return `<!DOCTYPE html>\n            <html lang=\"en\">\n            <head>\n                <meta charset=\"UTF-8\">\n                <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n                <title>Codessa Dashboard</title>\n                <link rel=\"stylesheet\" href=\"${styleUri}\">\n                <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; img-src ${webview.cspSource} https:; script-src 'nonce-${nonce}'; style-src ${webview.cspSource} 'unsafe-inline'; font-src ${webview.cspSource};\">\n            </head>\n            <body>\n                <div class=\"dashboard-container\">\n                    <header class=\"dashboard-header\">\n                        <div class=\"logo-container\">\n                            <img src=\"${logoUri}\" alt=\"Codessa Logo\" class=\"logo\" />\n                            <h1>Codessa Dashboard</h1>\n                        </div>\n                        <div class=\"actions\">\n                            <button id=\"btn-refresh\" title=\"Refresh dashboard\" class=\"icon-button\">\n                                <span style=\"font-family: 'codicon'; font-size: 16px;\">&#xeb37;</span> Refresh\n                            </button>\n                            <button id=\"btn-settings\" title=\"Open settings\" class=\"icon-button\">\n                                <span style=\"font-family: 'codicon'; font-size: 16px;\">&#xeb52;</span> Settings\n                            </button>\n                            <button id=\"btn-logs\" title=\"Show logs\" class=\"icon-button\">\n                                <span style=\"font-family: 'codicon'; font-size: 16px;\">&#xea7f;</span> Logs\n                            </button>\n                        </div>\n                    </header>\n\n                    <div class=\"dashboard-content\">\n                        <div class=\"dashboard-row\">\n                            <div class=\"dashboard-card status-card\">\n                                <h2>System Status</h2>\n                                <div class=\"status-indicators\">\n                                    <div class=\"status-item ${data.providers?.configured > 0 ? 'status-ok' : 'status-warning'}\">\n                                        <span class=\"status-icon\"></span>\n                                        <span class=\"status-label\">LLM Providers</span>\n                                        <span class=\"status-value\">${data.providers?.configured || 0}/${data.providers?.total || 0} configured</span>\n                                    </div>\n                                    <div class=\"status-item ${data.agents?.total > 0 ? 'status-ok' : 'status-warning'}\">\n                                        <span class=\"status-icon\"></span>\n                                        <span class=\"status-label\">Agents</span>\n                                        <span class=\"status-value\">${data.agents?.total || 0} available</span>\n                                    </div>\n                                    <div class=\"status-item status-ok\">\n                                        <span class=\"status-icon\"></span>\n                                        <span class=\"status-label\">Tools</span>\n                                        <span class=\"status-value\">${data.tools?.total || 0} available</span>\n                                    </div>\n                                </div>\n                                <p class=\"last-updated\">Last updated: <span id=\"timestamp\"></span></p>\n                            </div>\n                        </div>\n\n                        <div class=\"dashboard-row\">\n                            <div class=\"dashboard-card\">\n                                <h2>Agents</h2>\n                                <div class=\"card-actions\">\n                                    <button id=\"btn-create-agent\" class=\"btn primary\"><span style=\"font-family: 'codicon'; font-size: 14px;\">&#xea60;</span> Create Agent</button>\n                                </div>\n                                <div class=\"agents-list\" id=\"agents-list\">\n                                    <!-- Agents will be inserted here by JavaScript -->\n                                    ${data.error ? `<div class=\"error-message\">${data.error}</div>` : ''}\n                                    ${!data.error && (!data.agents || data.agents.total === 0) ?\n          '<div class=\"empty-message\">No agents configured yet. Create your first agent to get started.</div>' : ''}\n                                </div>\n                            </div>\n                        </div>\n\n                        <div class=\"dashboard-row\">\n                            <div class=\"dashboard-card\">\n                                <h2>LLM Providers</h2>\n                                <div class=\"provider-list\" id=\"provider-list\">\n                                    <!-- Providers will be inserted here by JavaScript -->\n                                </div>\n                            </div>\n\n                            <div class=\"dashboard-card\">\n                                <h2>Available Tools</h2>\n                                <div class=\"tools-list\" id=\"tools-list\">\n                                    <!-- Tools will be inserted here by JavaScript -->\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <script nonce=\"${nonce}\">\n                    // Dashboard data\n                    const dashboardData = ${JSON.stringify(data)};\n                </script>\n                <script nonce=\"${nonce}\" src=\"${scriptUri}\"></script>\n            </body>\n            </html>`;\n    } catch (error) {\n      Logger.instance.error('Error generating webview HTML content:', error);\n      return `<!DOCTYPE html>\n            <html lang=\"en\">\n            <head>\n                <meta charset=\"UTF-8\">\n                <title>Error</title>\n            </head>\n            <body>\n                <h1>Error</h1>\n                <p>Failed to generate dashboard content. Check logs for details.</p>\n                <pre>${error}</pre>\n            </body>\n            </html>`;\n    }\n  }\n\n  /**\n     * Clean up resources\n     */\n  public dispose() {\n    DashboardPanel.currentPanel = undefined;\n    if (this._refreshInterval) {\n      clearInterval(this._refreshInterval as unknown as number);\n    }\n    this._panel.dispose();\n    while (this._disposables.length) {\n      const disposable = this._disposables.pop();\n      if (disposable) {\n        disposable.dispose();\n      }\n    }\n  }\n}"]}