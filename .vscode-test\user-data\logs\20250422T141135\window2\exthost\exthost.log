2025-04-22 20:56:50.288 [info] Extension host with pid 58452 started
2025-04-22 20:56:50.309 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-04-22 20:56:50.355 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-04-22 20:56:50.361 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-04-22 20:56:50.412 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-04-22 20:56:50.513 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-04-22 20:56:50.513 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-04-22 20:56:50.532 [info] Eager extensions activated
2025-04-22 20:56:50.540 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-04-22 20:56:50.544 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-04-22 20:56:50.564 [info] ExtensionService#_doActivateExtension TekNerds-ITS.codessa, startup: false, activationEvent: 'onStartupFinished'
2025-04-22 20:57:07.349 [error] Activating extension TekNerds-ITS.codessa failed due to an error:
2025-04-22 20:57:07.349 [error] Error: Cannot find module '@langchain/chroma'
Require stack:
- e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\vectorStores\chromaVectorStore.js
- e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\vectorStores\vectorStoreFactory.js
- e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\langchainMemory.js
- e:\_2025_Coding_Projects\AI\Codessa\out\memory\memoryManager.js
- e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\vectorStores.js
- e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\advancedTemplates.js
- e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\index.js
- e:\_2025_Coding_Projects\AI\Codessa\out\extension.js
- c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\out\vs\workbench\api\node\extensionHostProcess.js
	at Module._resolveFilename (node:internal/modules/cjs/loader:1232:15)
	at n._resolveFilename (node:electron/js2c/utility_init:2:16068)
	at t._resolveFilename (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22672)
	at Module._load (node:internal/modules/cjs/loader:1058:27)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\vectorStores\chromaVectorStore.js:37:18)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\vectorStores\vectorStoreFactory.js:5:29)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\langchainMemory.js:42:30)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\memory\memoryManager.js:42:27)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\vectorStores.js:12:25)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\advancedTemplates.js:16:24)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\index.js:31:14)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\extension.js:55:21)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at vV.xb (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:23710)
	at async Promise.all (index 0)
2025-04-22 20:57:49.116 [info] Extension host terminating: renderer closed the MessagePort
2025-04-22 20:57:49.166 [info] Extension host with pid 58452 exiting with code 0
2025-04-22 20:57:50.262 [info] Extension host with pid 46840 started
2025-04-22 20:57:50.300 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-04-22 20:57:50.334 [info] ExtensionService#_doActivateExtension TekNerds-ITS.codessa, startup: false, activationEvent: 'onView:codessa.chatViewSidebar'
2025-04-22 20:57:50.885 [error] Activating extension TekNerds-ITS.codessa failed due to an error:
2025-04-22 20:57:50.885 [error] Error: Cannot find module '@langchain/chroma'
Require stack:
- e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\vectorStores\chromaVectorStore.js
- e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\vectorStores\vectorStoreFactory.js
- e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\langchainMemory.js
- e:\_2025_Coding_Projects\AI\Codessa\out\memory\memoryManager.js
- e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\vectorStores.js
- e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\advancedTemplates.js
- e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\index.js
- e:\_2025_Coding_Projects\AI\Codessa\out\extension.js
- c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\out\vs\workbench\api\node\extensionHostProcess.js
	at Module._resolveFilename (node:internal/modules/cjs/loader:1232:15)
	at n._resolveFilename (node:electron/js2c/utility_init:2:16068)
	at t._resolveFilename (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22672)
	at Module._load (node:internal/modules/cjs/loader:1058:27)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\vectorStores\chromaVectorStore.js:37:18)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\vectorStores\vectorStoreFactory.js:5:29)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\memory\langchain\langchainMemory.js:42:30)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\memory\memoryManager.js:42:27)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\vectorStores.js:12:25)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\advancedTemplates.js:16:24)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\workflows\langgraph\index.js:31:14)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at Object.<anonymous> (e:\_2025_Coding_Projects\AI\Codessa\out\extension.js:55:21)
	at Module._compile (node:internal/modules/cjs/loader:1484:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
	at Module.load (node:internal/modules/cjs/loader:1295:32)
	at Module._load (node:internal/modules/cjs/loader:1111:12)
	at c._load (node:electron/js2c/node_init:2:16955)
	at e._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:197:1509)
	at t._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:22390)
	at i._load (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:175:24781)
	at Module.require (node:internal/modules/cjs/loader:1318:19)
	at require (node:internal/modules/helpers:179:18)
	at vV.xb (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:183:23710)
	at async Promise.all (index 0)
2025-04-22 20:57:50.909 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-04-22 20:57:50.917 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-04-22 20:57:50.954 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-04-22 20:57:51.065 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-04-22 20:57:51.065 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-04-22 20:57:51.083 [info] Eager extensions activated
2025-04-22 20:57:51.090 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-04-22 20:57:51.095 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-04-22 20:58:23.388 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-04-22 20:59:05.116 [info] Extension host terminating: renderer closed the MessagePort
2025-04-22 20:59:05.133 [info] Extension host with pid 46840 exiting with code 0
