import * as vscode from 'vscode';
import { ILogger } from './toolFramework';
import sharp from 'sharp';
import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';
import { Readable, PassThrough } from 'stream';
import * as streamPromises from 'stream/promises';
import { SecurityUtils, SecurityError } from '../security/securityUtils';

export interface ClipboardMetrics {
    successCount: number;
    errorCount: number;
    averageDuration: number;
    maxDuration: number;
    bytesProcessed: number;
    lastError?: {
        name: string;
        message: string;
        stack?: string;
    };
}

class ClipboardMetricsService extends EventEmitter {
    private _successCount: number = 0;
    private _errorCount: number = 0;
    private _totalDuration: number = 0;
    private _maxDuration: number = 0;
    private _totalBytes: number = 0;
    private readonly operations: number[] = [];
    private readonly maxOperations: number = 1000;

    constructor() {
        super();
    }

    public startOperation(): number {
        return performance.now();
    }

    public recordSuccess(startTime: number, bytes: number): void {
        const duration = performance.now() - startTime;
        this.recordOperation(duration, bytes, true);
    }

    public recordError(startTime: number, error: Error): void {
        const duration = performance.now() - startTime;
        this.recordOperation(duration, 0, false, error);
    }

    private recordOperation(duration: number, bytes: number, success: boolean, error?: Error): void {
        this.operations.push(duration);
        this._totalDuration += duration;
        this._maxDuration = Math.max(this._maxDuration, duration);

        if (success) {
            this._successCount++;
            this._totalBytes += bytes;
        } else {
            this._errorCount++;
        }

        // Maintain a rolling window of operations
        if (this.operations.length > this.maxOperations) {
            const removedDuration = this.operations.shift();
            if (removedDuration !== undefined) {
                this._totalDuration -= removedDuration;
            }
        }

        this.emit('metricsUpdated', {
            ...this.getMetrics(),
            lastError: error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : undefined
        });
    }

    public getMetrics(): ClipboardMetrics {
        return {
            successCount: this._successCount,
            errorCount: this._errorCount,
            averageDuration: this.operations.length > 0 
                ? this._totalDuration / this.operations.length 
                : 0,
            maxDuration: this._maxDuration,
            bytesProcessed: this._totalBytes
        };
    }

    public reset(): void {
        this._successCount = 0;
        this._errorCount = 0;
        this._totalDuration = 0;
        this._maxDuration = 0;
        this._totalBytes = 0;
        this.operations.length = 0;
        this.emit('metricsReset');
    }
}

/**
 * Base error class for clipboard-related errors
 */
export class ClipboardError extends Error {
    constructor(message: string, public readonly cause?: Error) {
        super(message);
        this.name = 'ClipboardError';
    }
}

/**
 * Error thrown when clipboard access is denied
 */
export class ClipboardAccessError extends ClipboardError {
    constructor(message: string, cause?: Error) {
        super(message, cause);
        this.name = 'ClipboardAccessError';
    }
}

/**
 * Error thrown when unsupported content is encountered
 */
export class UnsupportedContentError extends ClipboardError {
    constructor(message: string, cause?: Error) {
        super(message, cause);
        this.name = 'UnsupportedContentError';
    }
}

/**
 * Error thrown when image processing fails
 */
export class ImageProcessingError extends ClipboardError {
    constructor(message: string, cause?: Error) {
        super(message, cause);
        this.name = 'ImageProcessingError';
    }
}

/**
 * Error thrown when content size exceeds limits
 */
export class ContentSizeError extends ClipboardError {
    constructor(message: string, public readonly size: number, public readonly limit: number) {
        super(`${message} (size: ${size}, limit: ${limit})`);
        this.name = 'ContentSizeError';
    }
}

/**
 * Configuration options for the clipboard service
 */
export interface ClipboardServiceOptions {
    /** Maximum retries for clipboard operations */
    maxRetries?: number;
    /** Delay between retries in milliseconds */
    retryDelay?: number;
    /** Maximum size in bytes for clipboard content */
    maxContentSize?: number;
    /** Maximum size of a data chunk for processing */
    chunkSize?: number;
}

/**
 * Default configuration values
 */
const DEFAULT_OPTIONS: Required<ClipboardServiceOptions> = {
    maxRetries: 3,
    retryDelay: 100,
    maxContentSize: 50 * 1024 * 1024, // 50MB
    chunkSize: 1024 * 1024 // 1MB
};
/** Event data for clipboard changes */
export type ClipboardChangeEvent = {
    type: 'text' | 'image' | 'html';
    source: string;
};

/** Event data for clipboard errors */
export type ClipboardErrorEvent = {
    error: Error;
    operation: string;
};

/** Clipboard operation metrics */
export interface ClipboardMetrics {
    /** Total number of successful operations */
    successCount: number;
    /** Total number of failed operations */
    errorCount: number;
    /** Average operation duration in milliseconds */
    averageDuration: number;
    /** Maximum operation duration in milliseconds */
    maxDuration: number;
    /** Total bytes processed */
    bytesProcessed: number;
}

export interface IClipboardService {
    readText(): Promise<string>;
    readImage(): Promise<MultimodalData | null>;
    readHtml(): Promise<HtmlContent | null>;
    writeText(text: string): Promise<void>;
    writeImage(image: MultimodalData): Promise<void>;
    writeHtml(content: HtmlContent): Promise<void>;
    on(event: 'change', listener: (data: ClipboardChangeEvent) => void): void;
    on(event: 'error', listener: (data: ClipboardErrorEvent) => void): void;
    getMetrics(): ClipboardMetrics;
}

/**
 * Represents image data that can be stored in the clipboard or read from a file.
 */
export interface MultimodalData {
    /** The MIME type of the image data */
    mimeType: 'image/jpeg' | 'image/png' | 'image/webp';
    /** The base64-encoded image data */
    base64Data: string;
    /** The source of the image data */
    source: 'clipboard' | 'file';
    /** Optional metadata */
    metadata?: {
        width?: number;
        height?: number;
        format?: string;
        size?: number;
        securityHash?: string;
        entropy?: number;
    };
}

/**
 * HTML content with optional metadata
 */
export interface HtmlContent {
    /** The HTML content */
    html: string;
    /** Optional plain text fallback */
    plainText?: string;
    /** Optional metadata */
    metadata?: {
        charset?: string;
        title?: string;
    };
}

/**
 * A VS Code-specific implementation of the IClipboardService interface.
 * Provides clipboard functionality within the VS Code environment, including support for
 * both text and image data with automatic format conversion.
 * 
 * @implements {IClipboardService}
 * @example
 * ```typescript
 * // Create a new instance with a custom logger
 * const clipboardService = new VSCodeClipboardService(customLogger);
 * 
 * // Use the service
 * await clipboardService.writeText('Copied text');
 * const text = await clipboardService.readText();
 * ```
 */
export class VSCodeClipboardService extends EventEmitter implements IClipboardService {
    private readonly logger: ILogger;
    private readonly options: Required<ClipboardServiceOptions>;
    private readonly metricsService: ClipboardMetricsService;
    private readonly supportedImageTypes = ['image/png', 'image/jpeg', 'image/webp'] as const;
    private isDisposed = false;

    constructor(logger?: ILogger, options: ClipboardServiceOptions = {}) {
        super();
        this.logger = logger || console;
        this.options = { ...DEFAULT_OPTIONS, ...options };
        this.metricsService = new ClipboardMetricsService();

        // Monitor clipboard changes by watching text document changes
        vscode.workspace.onDidChangeTextDocument(e => {
            if (e.contentChanges.length > 0) {
                this.emit('change', { type: 'text', source: e.document.uri });
            }
        });
    }

    public async readText(): Promise<string> {
        const startTime = this.metricsService.startOperation();

        try {
            const text = await this.retryOperation(async () => {
                const result = await vscode.env.clipboard.readText();
                
                // Size validation
                if (result.length > this.options.maxContentSize) {
                    throw new ContentSizeError(
                        'Clipboard text content exceeds size limit',
                        result.length,
                        this.options.maxContentSize
                    );
                }

                // Content security checks
                if (/[<>|&;$()]/.test(result)) {
                    throw new SecurityError('Potential unsafe characters detected in clipboard content');
                }

                // Check for potentially dangerous content
                const entropy = SecurityUtils.calculateEntropy(result);
                if (entropy > 6.5) { // High entropy could indicate encrypted/compressed malicious content
                    this.logger.warn('High entropy content detected in clipboard', { entropy });
                }

                // Check for null bytes and control characters
                if (result.includes('\0')) {
                    throw new SecurityError('Null bytes detected in clipboard content');
                }

                return result;
            });

            this.metricsService.recordSuccess(startTime, Buffer.byteLength(text));
            this.logger.debug('Successfully read text from clipboard', { 
                length: text.length,
                hash: SecurityUtils.generateContentHash(text)
            });
            return text;
        } catch (error) {
            this.metricsService.recordError(startTime, error as Error);
            throw this.wrapError('Failed to read clipboard text', error);
        }
    }

    public async readHtml(): Promise<HtmlContent | null> {
        const startTime = this.metricsService.startOperation();

        try {
            const text = await this.readText();
            
            if (!text.trim().startsWith('<')) {
                return null;
            }

            const result: HtmlContent = {
                html: text,
                plainText: text.replace(/<[^>]+>/g, ''),
                metadata: {
                    charset: this.detectCharset(text)
                }
            };

            this.metricsService.recordSuccess(startTime, Buffer.byteLength(text));
            return result;
        } catch (error) {
            this.metricsService.recordError(startTime, error as Error);
            throw this.wrapError('Failed to read HTML content', error);
        }
    }

    public async writeHtml(content: HtmlContent): Promise<void> {
        const startTime = this.metricsService.startOperation();

        try {
            if (Buffer.byteLength(content.html) > this.options.maxContentSize) {
                throw new ContentSizeError(
                    'HTML content exceeds size limit',
                    Buffer.byteLength(content.html),
                    this.options.maxContentSize
                );
            }

            await this.retryOperation(async () => {
                await vscode.env.clipboard.writeText(content.html);
            });

            this.metricsService.recordSuccess(startTime, Buffer.byteLength(content.html));
        } catch (error) {
            this.metricsService.recordError(startTime, error as Error);
            throw this.wrapError('Failed to write HTML content', error);
        }
    }

    public async readImage(): Promise<MultimodalData | null> {
        const startTime = this.metricsService.startOperation();

        try {
            const clipboardy = await import('clipboardy');
            const clipboard = clipboardy.default || clipboardy;
            const clipboardContent = await this.retryOperation(() => clipboard.read());

            const imageMatch = clipboardContent.match(/^data:(image\/[a-z]+);base64,([\s\S]+)$/i);
            if (!imageMatch) {
                this.logger.debug('No image data found in clipboard');
                return null;
            }

            const detectedMimeType = imageMatch[1];
            let base64Data = imageMatch[2];

            // Validate MIME type
            if (!SecurityUtils.isSafeMimeType(detectedMimeType)) {
                throw new SecurityError(`Unsafe MIME type detected: ${detectedMimeType}`);
            }

            // Validate base64 data
            if (!SecurityUtils.validateBase64(base64Data)) {
                throw new SecurityError('Invalid base64 data detected');
            }

            // Size validation
            const rawSize = Buffer.byteLength(base64Data, 'base64');
            if (rawSize > this.options.maxContentSize) {
                throw new ContentSizeError(
                    'Image data exceeds size limit',
                    rawSize,
                    this.options.maxContentSize
                );
            }

            // Decode and validate image data
            const imageBuffer = Buffer.from(base64Data, 'base64');

            // Check for potential shellcode
            if (SecurityUtils.detectShellcode(imageBuffer)) {
                throw new SecurityError('Potential malicious code detected in image data');
            }

            // Validate image format signature
            if (!SecurityUtils.validateImageData(imageBuffer)) {
                throw new SecurityError('Invalid image format signature');
            }

            let finalMimeType: typeof this.supportedImageTypes[number];
            let metadata: MultimodalData['metadata'] = {};

            if (detectedMimeType === 'image/gif') {
                this.logger.debug('Converting GIF to PNG format');
                [finalMimeType, base64Data, metadata] = await this.convertGifToPng(base64Data);
            } else if (this.isSupportedImageType(detectedMimeType)) {
                finalMimeType = detectedMimeType;
                metadata = await this.getImageMetadata(imageBuffer);
            } else {
                throw new UnsupportedContentError(
                    `Unsupported image type: ${detectedMimeType}. Supported types: ${this.supportedImageTypes.join(', ')}`
                );
            }

            // Calculate and verify entropy
            const entropy = SecurityUtils.calculateEntropy(imageBuffer);
            if (entropy > 7.99) { // Extremely high entropy might indicate steganography or encrypted content
                this.logger.warn('Suspicious image entropy detected', { entropy });
                throw new SecurityError('Suspicious image content detected');
            }

            const contentHash = SecurityUtils.generateContentHash(imageBuffer);
            const result: MultimodalData = {
                mimeType: finalMimeType,
                base64Data,
                source: 'clipboard',
                metadata: {
                    ...metadata,
                    securityHash: contentHash
                }
            };

            this.metricsService.recordSuccess(startTime, rawSize);
            this.logger.debug(`Successfully read ${finalMimeType} image from clipboard`, { 
                metadata,
                hash: contentHash,
                entropy
            });
            return result;
        } catch (error) {
            this.metricsService.recordError(startTime, error as Error);
            throw this.wrapError('Failed to read image from clipboard', error);
        }
    }

    public async writeImage(image: MultimodalData): Promise<void> {
        const startTime = this.metricsService.startOperation();

        try {
            // Validate MIME type
            if (!this.isSupportedImageType(image.mimeType)) {
                throw new UnsupportedContentError(
                    `Unsupported image type: ${image.mimeType}. Supported types: ${this.supportedImageTypes.join(', ')}`
                );
            }

            if (!SecurityUtils.isSafeMimeType(image.mimeType)) {
                throw new SecurityError(`Unsafe MIME type: ${image.mimeType}`);
            }

            // Validate base64 data
            if (!SecurityUtils.validateBase64(image.base64Data)) {
                throw new SecurityError('Invalid base64 data detected');
            }

            const imageBuffer = Buffer.from(image.base64Data, 'base64');
            
            // Size validation
            const rawSize = imageBuffer.length;
            if (rawSize > this.options.maxContentSize) {
                throw new ContentSizeError(
                    'Image data exceeds size limit',
                    rawSize,
                    this.options.maxContentSize
                );
            }

            // Validate image format signature
            if (!SecurityUtils.validateImageData(imageBuffer)) {
                throw new SecurityError('Invalid image format signature');
            }

            // Check for potential shellcode
            if (SecurityUtils.detectShellcode(imageBuffer)) {
                throw new SecurityError('Potential malicious code detected in image data');
            }

            // Calculate and verify entropy
            const entropy = SecurityUtils.calculateEntropy(imageBuffer);
            if (entropy > 7.99) {
                throw new SecurityError('Suspicious image content detected');
            }

            // Generate content hash for verification
            const originalHash = SecurityUtils.generateContentHash(imageBuffer);

            // Write image with content verification
            const dataUrl = `data:${image.mimeType};base64,${image.base64Data}`;
            await this.retryOperation(async () => {
                await Promise.resolve(vscode.env.clipboard.writeText(dataUrl));
                
                // Verify the written content
                const verificationContent = await this.readImage();
                if (!verificationContent) {
                    throw new SecurityError('Failed to verify written image content');
                }

                const verificationBuffer = Buffer.from(verificationContent.base64Data, 'base64');
                const verificationHash = SecurityUtils.generateContentHash(verificationBuffer);

                if (verificationHash !== originalHash) {
                    throw new SecurityError('Image content verification failed');
                }
            });

            this.metricsService.recordSuccess(startTime, rawSize);
            this.logger.debug(`Successfully wrote ${image.mimeType} image to clipboard`, {
                size: rawSize,
                hash: originalHash,
                entropy,
                metadata: image.metadata
            });
        } catch (error) {
            this.metricsService.recordError(startTime, error as Error);
            throw this.wrapError('Failed to write image to clipboard', error);
        }
    }

    public async writeText(text: string): Promise<void> {
        const startTime = this.metricsService.startOperation();
        const originalHash = SecurityUtils.generateContentHash(text);

        try {
            // Size validation
            const size = Buffer.byteLength(text);
            if (size > this.options.maxContentSize) {
                throw new ContentSizeError(
                    'Text content exceeds size limit',
                    size,
                    this.options.maxContentSize
                );
            }

            // Check for unsafe content
            if (text.includes('\0')) {
                throw new SecurityError('Null bytes detected in content');
            }

            // Check for potential script injection
            if (/<script[\s>]|javascript:/i.test(text)) {
                throw new SecurityError('Potential script injection detected');
            }

            // Check for command injection
            if (/[<>|&;$()]/.test(text)) {
                throw new SecurityError('Potential command injection detected');
            }

            // Check for high entropy (possible encrypted/compressed malicious content)
            const entropy = SecurityUtils.calculateEntropy(text);
            if (entropy > 6.5) {
                this.logger.warn('High entropy content detected', { entropy });
            }

            await this.retryOperation(async () => {
                await Promise.resolve(vscode.env.clipboard.writeText(text));
                
                // Verify content was written correctly
                const verificationText = await vscode.env.clipboard.readText();
                const verificationHash = SecurityUtils.generateContentHash(verificationText);
                
                if (verificationHash !== originalHash) {
                    throw new SecurityError('Clipboard content verification failed');
                }
            });
            
            this.metricsService.recordSuccess(startTime, size);
            this.logger.debug('Successfully wrote text to clipboard', { 
                length: text.length,
                hash: originalHash,
                entropy
            });
        } catch (error) {
            this.metricsService.recordError(startTime, error as Error);
            throw this.wrapError('Failed to write to clipboard', error);
        }
    }

    public getMetrics() {
        return this.metricsService.getMetrics();
    }

    public dispose(): void {
        if (this.isDisposed) return;
        this.isDisposed = true;
        this.removeAllListeners();
    }

    private async convertGifToPng(gifData: string): Promise<['image/png', string, MultimodalData['metadata']]> {
        try {
            const buffer = Buffer.from(gifData, 'base64');
            const chunks: Buffer[] = [];
            const stream = Readable.from(buffer);
            
            await streamPromises.pipeline(
                stream,
                new PassThrough({
                    highWaterMark: this.options.chunkSize,
                    async transform(chunk, encoding, callback) {
                        try {
                            const processed = await sharp(chunk)
                                .toFormat('png')
                                .toBuffer();
                            chunks.push(processed);
                            callback(null);
                        } catch (err) {
                            callback(err as Error);
                        }
                    }
                })
            );

            const processedBuffer = Buffer.concat(chunks);
            const metadata = await this.getImageMetadata(processedBuffer);
            return ['image/png', processedBuffer.toString('base64'), metadata];
        } catch (error) {
            throw new ImageProcessingError('Failed to convert GIF to PNG', error as Error);
        }
    }

    private async getImageMetadata(buffer: Buffer): Promise<MultimodalData['metadata']> {
        try {
            const metadata = await sharp(buffer).metadata();
            return {
                width: metadata.width,
                height: metadata.height,
                format: metadata.format,
                size: buffer.length
            };
        } catch (error) {
            this.logger.warn('Failed to extract image metadata', { error });
            return {};
        }
    }

    private isSupportedImageType(mimeType: string): mimeType is typeof this.supportedImageTypes[number] {
        return this.supportedImageTypes.includes(mimeType as typeof this.supportedImageTypes[number]);
    }

    private detectCharset(html: string): string {
        const match = html.match(/<meta\s+charset=["']?([\w-]+)/i);
        return match?.[1] || 'utf-8';
    }

    private async retryOperation<T>(operation: () => Promise<T>, maxAttempts: number = this.options.maxRetries): Promise<T> {
        let lastError: Error | undefined;

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error as Error;
                if (attempt < maxAttempts) {
                    const delay = this.options.retryDelay * Math.pow(2, attempt - 1);
                    this.logger.debug(`Retry attempt ${attempt}/${maxAttempts} after ${delay}ms`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError;
    }

    private wrapError(message: string, error: unknown): Error {
        if (error instanceof ClipboardError) {
            return error;
        }

        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('access') || errorMessage.includes('permission')) {
            return new ClipboardAccessError(`${message}: ${errorMessage}`, error as Error);
        }

        return new ClipboardError(`${message}: ${errorMessage}`, error as Error);
    }
}
