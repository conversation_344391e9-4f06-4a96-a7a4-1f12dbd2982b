"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemTTS = void 0;
const child_process_1 = require("child_process");
const ttsSettings_1 = require("./ttsSettings");
class SystemTTS {
    static async speak(text, voice, rate, pitch, volume) {
        const platform = process.platform;
        if (platform === 'win32') {
            // Windows: Use PowerShell SAPI.SpVoice
            const command = `PowerShell -Command "$speak = New-Object -ComObject SAPI.SpVoice; try { $speak.Voice = ($speak.GetVoices() | Where-Object { $_.GetDescription() -eq '${voice.replace(/'/g, "''")}' })[0] } catch {}; $speak.Rate = ${Math.round((rate - 1) * 10)}; $speak.Volume = ${Math.round(volume * 100)}; $speak.Speak('${text.replace(/'/g, "''")}')"`;
            return new Promise((resolve, reject) => {
                (0, child_process_1.exec)(command, (error) => {
                    if (error)
                        reject(error);
                    else
                        resolve();
                });
            });
        }
        else if (platform === 'darwin') {
            // macOS: Use 'say' command
            const command = `say -v '${voice}' -r ${Math.round(rate * 200)} -- '${text.replace(/'/g, "\\'")}'`;
            return new Promise((resolve, reject) => {
                (0, child_process_1.exec)(command, (error) => {
                    if (error)
                        reject(error);
                    else
                        resolve();
                });
            });
        }
        else if (platform === 'linux') {
            // Linux: Use espeak or festival
            const command = `espeak -v '${voice}' -s ${Math.round(rate * 100)} '${text.replace(/'/g, "\\'")}'`;
            return new Promise((resolve, reject) => {
                (0, child_process_1.exec)(command, (error) => {
                    if (error)
                        reject(error);
                    else
                        resolve();
                });
            });
        }
        else {
            throw new Error('System TTS not supported on this platform.');
        }
    }
    static async getVoices() {
        const platform = process.platform;
        if (platform === 'win32') {
            // Windows: Use PowerShell to list SAPI voices
            return new Promise((resolve) => {
                (0, child_process_1.exec)('PowerShell -Command "Add-Type -AssemblyName System.Speech; (New-Object System.Speech.Synthesis.SpeechSynthesizer).GetInstalledVoices() | ForEach-Object { $_.VoiceInfo.Name }"', (err, stdout) => {
                    if (err)
                        return resolve([]);
                    const voices = stdout.split(/\r?\n/).filter(Boolean).map(name => ({
                        id: name,
                        name,
                        language: '',
                        provider: ttsSettings_1.TTSProvider.MICROSOFT
                    }));
                    resolve(voices);
                });
            });
        }
        else if (platform === 'darwin') {
            // macOS: Use 'say -v ?' to list voices
            return new Promise((resolve) => {
                (0, child_process_1.exec)('say -v ?', (err, stdout) => {
                    if (err)
                        return resolve([]);
                    const voices = stdout.split(/\r?\n/).filter(Boolean).map(line => {
                        const [name] = line.split(/\s+/);
                        return { id: name, name, language: '', provider: ttsSettings_1.TTSProvider.EDGE };
                    });
                    resolve(voices);
                });
            });
        }
        else if (platform === 'linux') {
            // Linux: Use espeak --voices
            return new Promise((resolve) => {
                (0, child_process_1.exec)('espeak --voices', (err, stdout) => {
                    if (err)
                        return resolve([]);
                    const lines = stdout.split(/\r?\n/).slice(1);
                    const voices = lines.map(line => {
                        const parts = line.trim().split(/\s+/);
                        if (parts.length < 4)
                            return null;
                        return { id: parts[3], name: parts[3], language: parts[1], provider: ttsSettings_1.TTSProvider.EDGE };
                    }).filter(Boolean);
                    resolve(voices);
                });
            });
        }
        else {
            return [];
        }
    }
}
exports.SystemTTS = SystemTTS;
//# sourceMappingURL=systemTTS.js.map