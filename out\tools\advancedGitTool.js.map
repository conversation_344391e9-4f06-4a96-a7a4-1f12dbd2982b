{"version": 3, "file": "advancedGitTool.js", "sourceRoot": "", "sources": ["../../src/tools/advancedGitTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,kDAAoC;AACpC,6BAAwB;AAExB,MAAa,YAAY;IACd,EAAE,GAAG,OAAO,CAAC;IACb,IAAI,GAAG,WAAW,CAAC;IACnB,WAAW,GAAG,0CAA0C,CAAC;IACzD,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;KAC5E,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,qCAAqC,EAAE;SAC7E;QACD,QAAQ,EAAE,EAAE;KACb,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;QACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3D,EAAE,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;oBAC5D,IAAI,GAAG,IAAI,CAAC,MAAM;wBAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;oBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC/G,CAAC;IACH,CAAC;CACF;AA/BD,oCA+BC;AAED,MAAa,aAAa;IACf,EAAE,GAAG,QAAQ,CAAC;IACd,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,2CAA2C,CAAC;IAC1D,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+CAA+C,CAAC;KAC3E,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,+CAA+C,EAAE;SACvF;QACD,QAAQ,EAAE,CAAC,MAAM,CAAC;KACnB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;QAClC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3D,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;oBAC7D,IAAI,GAAG,IAAI,CAAC,MAAM;wBAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;oBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAChH,CAAC;IACH,CAAC;CACF;AA/BD,sCA+BC;AAED,MAAa,iBAAiB;IACnB,EAAE,GAAG,YAAY,CAAC;IAClB,IAAI,GAAG,iBAAiB,CAAC;IACzB,WAAW,GAAG,gDAAgD,CAAC;IAC/D,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;KAChF,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oDAAoD,EAAE;SAC5F;QACD,QAAQ,EAAE,CAAC,MAAM,CAAC;KACnB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;QAClC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3D,EAAE,CAAC,IAAI,CAAC,mBAAmB,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;oBAClE,IAAI,GAAG,IAAI,CAAC,MAAM;wBAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;oBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACrH,CAAC;IACH,CAAC;CACF;AA/BD,8CA+BC;AAED,MAAa,aAAa;IACf,EAAE,GAAG,QAAQ,CAAC;IACd,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,uDAAuD,CAAC;IACtE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+CAA+C,CAAC;KAC3E,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,+CAA+C,EAAE;SACvF;QACD,QAAQ,EAAE,CAAC,MAAM,CAAC;KACnB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;QAClC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3D,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;oBAC7D,IAAI,GAAG,IAAI,CAAC,MAAM;wBAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;oBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAChH,CAAC;IACH,CAAC;CACF;AA/BD,sCA+BC;AAED,MAAa,UAAU;IACZ,EAAE,GAAG,KAAK,CAAC;IACX,IAAI,GAAG,SAAS,CAAC;IACjB,WAAW,GAAG,yDAAyD,CAAC;IACxE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;KAC/F,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wDAAwD,EAAE;SAChG;QACD,QAAQ,EAAE,EAAE;KACb,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;QACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3D,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;oBAC1D,IAAI,GAAG,IAAI,CAAC,MAAM;wBAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;oBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC7G,CAAC;IACH,CAAC;CACF;AA/BD,gCA+BC;AAED,MAAa,kBAAkB;IACpB,EAAE,GAAG,aAAa,CAAC;IACnB,IAAI,GAAG,kBAAkB,CAAC;IAC1B,WAAW,GAAG,mCAAmC,CAAC;IAClD,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAClC,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE;KACb,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,MAAiB,EAAE,QAAuB;QACtF,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3D,EAAE,CAAC,IAAI,CAAC,4CAA4C,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;oBACrF,IAAI,GAAG,IAAI,CAAC,MAAM;wBAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;oBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,4BAA4B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACtH,CAAC;IACH,CAAC;CACF;AA1BD,gDA0BC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../types/agent';\nimport * as cp from 'child_process';\nimport { z } from 'zod';\n\nexport class GitStashTool implements ITool {\n  readonly id = 'stash';\n  readonly name = 'Git Stash';\n  readonly description = 'Stash changes in the current repository.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    args: z.string().optional().describe('Additional arguments for git stash.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      args: { type: 'string', description: 'Additional arguments for git stash.' }\n    },\n    required: []\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const args = input.args as string || '';\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    try {\n      const result = await new Promise<string>((resolve, reject) => {\n        cp.exec(`git stash ${args}`, { cwd }, (err, stdout, stderr) => {\n          if (err && !stdout) return reject(stderr || err.message);\n          resolve(stdout || stderr);\n        });\n      });\n      return { success: true, output: result.trim(), toolId: this.id, actionName };\n    } catch (error: any) {\n      return { success: false, error: `Git stash failed: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n\nexport class GitRevertTool implements ITool {\n  readonly id = 'revert';\n  readonly name = 'Git Revert';\n  readonly description = 'Revert commits in the current repository.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    args: z.string().describe('Arguments for git revert (e.g., commit hash).')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      args: { type: 'string', description: 'Arguments for git revert (e.g., commit hash).' }\n    },\n    required: ['args']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const args = input.args as string;\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    try {\n      const result = await new Promise<string>((resolve, reject) => {\n        cp.exec(`git revert ${args}`, { cwd }, (err, stdout, stderr) => {\n          if (err && !stdout) return reject(stderr || err.message);\n          resolve(stdout || stderr);\n        });\n      });\n      return { success: true, output: result.trim(), toolId: this.id, actionName };\n    } catch (error: any) {\n      return { success: false, error: `Git revert failed: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n\nexport class GitCherryPickTool implements ITool {\n  readonly id = 'cherryPick';\n  readonly name = 'Git Cherry-Pick';\n  readonly description = 'Cherry-pick commits in the current repository.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    args: z.string().describe('Arguments for git cherry-pick (e.g., commit hash).')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      args: { type: 'string', description: 'Arguments for git cherry-pick (e.g., commit hash).' }\n    },\n    required: ['args']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const args = input.args as string;\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    try {\n      const result = await new Promise<string>((resolve, reject) => {\n        cp.exec(`git cherry-pick ${args}`, { cwd }, (err, stdout, stderr) => {\n          if (err && !stdout) return reject(stderr || err.message);\n          resolve(stdout || stderr);\n        });\n      });\n      return { success: true, output: result.trim(), toolId: this.id, actionName };\n    } catch (error: any) {\n      return { success: false, error: `Git cherry-pick failed: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n\nexport class GitRebaseTool implements ITool {\n  readonly id = 'rebase';\n  readonly name = 'Git Rebase';\n  readonly description = 'Rebase branches or commits in the current repository.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    args: z.string().describe('Arguments for git rebase (e.g., branch name).')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      args: { type: 'string', description: 'Arguments for git rebase (e.g., branch name).' }\n    },\n    required: ['args']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const args = input.args as string;\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    try {\n      const result = await new Promise<string>((resolve, reject) => {\n        cp.exec(`git rebase ${args}`, { cwd }, (err, stdout, stderr) => {\n          if (err && !stdout) return reject(stderr || err.message);\n          resolve(stdout || stderr);\n        });\n      });\n      return { success: true, output: result.trim(), toolId: this.id, actionName };\n    } catch (error: any) {\n      return { success: false, error: `Git rebase failed: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n\nexport class GitTagTool implements ITool {\n  readonly id = 'tag';\n  readonly name = 'Git Tag';\n  readonly description = 'Create, list, or delete tags in the current repository.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    args: z.string().optional().describe('Arguments for git tag (e.g., tag name, -d for delete).')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      args: { type: 'string', description: 'Arguments for git tag (e.g., tag name, -d for delete).' }\n    },\n    required: []\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const args = input.args as string || '';\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    try {\n      const result = await new Promise<string>((resolve, reject) => {\n        cp.exec(`git tag ${args}`, { cwd }, (err, stdout, stderr) => {\n          if (err && !stdout) return reject(stderr || err.message);\n          resolve(stdout || stderr);\n        });\n      });\n      return { success: true, output: result.trim(), toolId: this.id, actionName };\n    } catch (error: any) {\n      return { success: false, error: `Git tag failed: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n\nexport class GitBranchGraphTool implements ITool {\n  readonly id = 'branchGraph';\n  readonly name = 'Git Branch Graph';\n  readonly description = 'Show a graphical log of branches.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({});\n  readonly inputSchema = {\n    type: 'object',\n    properties: {},\n    required: []\n  };\n  async execute(actionName: string | undefined, _input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    try {\n      const result = await new Promise<string>((resolve, reject) => {\n        cp.exec('git log --oneline --graph --all --decorate', { cwd }, (err, stdout, stderr) => {\n          if (err && !stdout) return reject(stderr || err.message);\n          resolve(stdout || stderr);\n        });\n      });\n      return { success: true, output: result.trim(), toolId: this.id, actionName };\n    } catch (error: any) {\n      return { success: false, error: `Git branch graph failed: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n"]}