{"version": 3, "file": "postgresDatabase.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/databases/postgresDatabase.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AACzC,4CAA4C;AAC5C,2BAA0B;AAG1B;;GAEG;AACH,MAAa,gBAAgB;IACnB,IAAI,CAAmB;IACvB,gBAAgB,CAAS;IACzB,MAAM,CAAS;IACf,WAAW,GAAG,KAAK,CAAC;IAE5B;QACE,IAAI,CAAC,gBAAgB,GAAG,IAAA,kBAAS,EAAS,2CAA2C,EAAE,EAAE,CAAC,CAAC;QAC3F,IAAI,CAAC,MAAM,GAAG,IAAA,kBAAS,EAAS,iCAAiC,EAAE,SAAS,CAAC,CAAC;IAChF,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;YACjE,CAAC;YAED,yBAAyB;YACzB,IAAI,CAAC,IAAI,GAAG,IAAI,SAAI,CAAC;gBACnB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;gBACvC,GAAG,EAAE,EAAE;gBACP,iBAAiB,EAAE,KAAK;gBACxB,uBAAuB,EAAE,IAAI;aAC9B,CAAC,CAAC;YAEH,kBAAkB;YAClB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEzC,IAAI,CAAC;gBACH,oCAAoC;gBACpC,MAAM,MAAM,CAAC,KAAK,CAAC,+BAA+B,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;gBAEjE,gBAAgB;gBAChB,MAAM,MAAM,CAAC,KAAK,CAAC;iDACsB,IAAI,CAAC,MAAM;;;;;;iBAM3C,CAAC,CAAC;gBAEX,iBAAiB;gBACjB,MAAM,MAAM,CAAC,KAAK,CAAC,wDAAwD,IAAI,CAAC,MAAM,2BAA2B,CAAC,CAAC;gBACnH,MAAM,MAAM,CAAC,KAAK,CAAC,uDAAuD,IAAI,CAAC,MAAM,gCAAgC,CAAC,CAAC;gBACvH,MAAM,MAAM,CAAC,KAAK,CAAC,sDAAsD,IAAI,CAAC,MAAM,uDAAuD,CAAC,CAAC;gBAE7I,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,4DAA4D,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACzF,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,MAA+B;QACxE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,MAA8B,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEnC,oCAAoC;YACpC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAY,CAAC;YAC/B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAiB,CAAC;YACzC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAmB,CAAC;YAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAmC,CAAC;YAE5D,gBAAgB;YAChB,MAAM,MAAM,CAAC,KAAK,CAChB,eAAe,IAAI,CAAC,MAAM;;;gEAG8B,EACxD,CAAC,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CACnD,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACvE,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,EAAU;QACnD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,MAA8B,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEnC,aAAa;YACb,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAC/B,iBAAiB,IAAI,CAAC,MAAM,yBAAyB,EACrD,CAAC,EAAE,CAAC,CACL,CAAC;YAEF,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAA4B,CAAC;YAEzD,OAAO;gBACL,EAAE,EAAE,MAAM,CAAC,EAAY;gBACvB,OAAO,EAAE,MAAM,CAAC,OAAiB;gBACjC,SAAS,EAAE,MAAM,CAAC,SAAmB;gBACrC,QAAQ,EAAE,MAAM,CAAC,QAAmC;aACrD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzF,OAAO,SAAS,CAAC;QACnB,CAAC;gBAAS,CAAC;YACT,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU,EAAE,MAA+B;QACvF,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,MAA8B,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEnC,iBAAiB;YACjB,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YAEhD,gBAAgB;YAChB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAC/B,UAAU,IAAI,CAAC,MAAM;;8BAEC,EACtB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CACnD,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACzE,OAAO,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,kBAAkB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1F,OAAO,KAAK,CAAC;QACf,CAAC;gBAAS,CAAC;YACT,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU;QACtD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,MAA8B,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEnC,gBAAgB;YAChB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAC/B,eAAe,IAAI,CAAC,MAAM,yBAAyB,EACnD,CAAC,EAAE,CAAC,CACL,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,oBAAoB,UAAU,EAAE,CAAC,CAAC;YAC3E,OAAO,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5F,OAAO,KAAK,CAAC;QACf,CAAC;gBAAS,CAAC;YACT,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,KAA8B,EAAE,KAAc;QAC1F,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,MAA8B,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEnC,cAAc;YACd,IAAI,GAAG,GAAG,iBAAiB,IAAI,CAAC,MAAM,WAAW,CAAC;YAClD,MAAM,MAAM,GAAc,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAa,EAAE,CAAC;YAChC,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,iBAAiB;YACjB,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;gBACxB,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;oBACpB,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAwB,CAAC;oBACpD,UAAU,CAAC,IAAI,CAAC,kEAAkE,UAAU,GAAG,CAAC,CAAC;oBACjG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC/B,UAAU,EAAE,CAAC;gBACf,CAAC;qBAAM,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;oBACvC,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBACrC,UAAU,CAAC,IAAI,CAAC,eAAe,WAAW,QAAQ,UAAU,EAAE,CAAC,CAAC;oBAChE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBACxB,UAAU,EAAE,CAAC;gBACf,CAAC;qBAAM,IAAI,GAAG,KAAK,WAAW,EAAE,CAAC;oBAC/B,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAqC,CAAC;oBACtE,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;wBACxB,UAAU,CAAC,IAAI,CAAC,iBAAiB,UAAU,EAAE,CAAC,CAAC;wBAC/C,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBACjC,UAAU,EAAE,CAAC;oBACf,CAAC;oBACD,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;wBACxB,UAAU,CAAC,IAAI,CAAC,iBAAiB,UAAU,EAAE,CAAC,CAAC;wBAC/C,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBACjC,UAAU,EAAE,CAAC;oBACf,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,UAAU,EAAE,CAAC,CAAC;oBAC3C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBACxB,UAAU,EAAE,CAAC;gBACf,CAAC;YACH,CAAC;YAED,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,GAAG,IAAI,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,CAAC;YAED,eAAe;YACf,GAAG,IAAI,0BAA0B,CAAC;YAElC,YAAY;YACZ,IAAI,KAAK,EAAE,CAAC;gBACV,GAAG,IAAI,WAAW,UAAU,EAAE,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAE/C,6CAA6C;YAC7C,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBAC3B,MAAM,QAAQ,GAAG,GAA8B,CAAC;gBAChD,OAAO;oBACL,EAAE,EAAE,QAAQ,CAAC,EAAY;oBACzB,OAAO,EAAE,QAAQ,CAAC,OAAiB;oBACnC,SAAS,EAAE,QAAQ,CAAC,SAAmB;oBACvC,QAAQ,EAAE,QAAQ,CAAC,QAAmC;iBACvD,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,OAAO,EAAE,CAAC;QACZ,CAAC;gBAAS,CAAC;YACT,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,eAAe,CAAC,UAAkB;QAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,MAA8B,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEnC,qBAAqB;YACrB,MAAM,MAAM,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;YAE1D,eAAM,CAAC,IAAI,CAAC,sBAAsB,UAAU,EAAE,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAzVD,4CAyVC", "sourcesContent": ["import type { IDatabase } from '../../types';\nimport { logger } from '../../../logger';\nimport { getConfig } from '../../../config';\nimport { Pool } from 'pg';\nimport type { PoolClient } from 'pg';\n\n/**\n * PostgreSQL database implementation\n */\nexport class PostgresDatabase implements IDatabase {\n  private pool: Pool | undefined;\n  private connectionString: string;\n  private schema: string;\n  private initialized = false;\n\n  constructor() {\n    this.connectionString = getConfig<string>('memory.database.postgres.connectionString', '');\n    this.schema = getConfig<string>('memory.database.postgres.schema', 'codessa');\n  }\n\n  /**\n     * Initialize the database\n     */\n  public async initialize(): Promise<void> {\n    try {\n      if (!this.connectionString) {\n        throw new Error('PostgreSQL connection string not configured');\n      }\n            \n      // Create connection pool\n      this.pool = new Pool({\n        connectionString: this.connectionString,\n        max: 20,\n        idleTimeoutMillis: 30000,\n        connectionTimeoutMillis: 2000\n      });\n            \n      // Test connection\n      const client = await this.pool.connect();\n            \n      try {\n        // Create schema if it doesn't exist\n        await client.query(`CREATE SCHEMA IF NOT EXISTS ${this.schema}`);\n                \n        // Create tables\n        await client.query(`\n                    CREATE TABLE IF NOT EXISTS ${this.schema}.memories (\n                        id TEXT PRIMARY KEY,\n                        content TEXT NOT NULL,\n                        timestamp BIGINT NOT NULL,\n                        metadata JSONB NOT NULL\n                    )\n                `);\n                \n        // Create indexes\n        await client.query(`CREATE INDEX IF NOT EXISTS idx_memories_timestamp ON ${this.schema}.memories(timestamp DESC)`);\n        await client.query(`CREATE INDEX IF NOT EXISTS idx_memories_metadata ON ${this.schema}.memories USING GIN (metadata)`);\n        await client.query(`CREATE INDEX IF NOT EXISTS idx_memories_content ON ${this.schema}.memories USING GIN (to_tsvector('english', content))`);\n                \n        this.initialized = true;\n        logger.info(`PostgreSQL database initialized successfully with schema ${this.schema}`);\n      } finally {\n        client.release();\n      }\n    } catch (error) {\n      logger.error('Failed to initialize PostgreSQL database:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Add a record\n     */\n  public async addRecord(collection: string, record: Record<string, unknown>): Promise<string> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    let client: PoolClient | undefined;\n        \n    try {\n      client = await this.pool.connect();\n            \n      // Extract fields with proper typing\n      const id = record.id as string;\n      const content = record.content as string;\n      const timestamp = record.timestamp as number;\n      const metadata = record.metadata as Record<string, unknown>;\n\n      // Insert record\n      await client.query(\n        `INSERT INTO ${this.schema}.memories (id, content, timestamp, metadata)\n                VALUES ($1, $2, $3, $4)\n                ON CONFLICT (id) DO UPDATE\n                SET content = $2, timestamp = $3, metadata = $4`,\n        [id, content, timestamp, JSON.stringify(metadata)]\n      );\n\n      logger.debug(`Added record with ID ${id} to collection ${collection}`);\n      return id;\n    } catch (error) {\n      logger.error(`Failed to add record to collection ${collection}:`, error);\n      throw error;\n    } finally {\n      if (client) {\n        client.release();\n      }\n    }\n  }\n\n  /**\n     * Get a record by ID\n     */\n  public async getRecord(collection: string, id: string): Promise<Record<string, unknown> | undefined> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    let client: PoolClient | undefined;\n        \n    try {\n      client = await this.pool.connect();\n            \n      // Get record\n      const result = await client.query(\n        `SELECT * FROM ${this.schema}.memories WHERE id = $1`,\n        [id]\n      );\n            \n      if (result.rows.length === 0) {\n        return undefined;\n      }\n            \n      const record = result.rows[0] as Record<string, unknown>;\n\n      return {\n        id: record.id as string,\n        content: record.content as string,\n        timestamp: record.timestamp as number,\n        metadata: record.metadata as Record<string, unknown>\n      };\n    } catch (error) {\n      logger.error(`Failed to get record with ID ${id} from collection ${collection}:`, error);\n      return undefined;\n    } finally {\n      if (client) {\n        client.release();\n      }\n    }\n  }\n\n  /**\n     * Update a record\n     */\n  public async updateRecord(collection: string, id: string, record: Record<string, unknown>): Promise<boolean> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    let client: PoolClient | undefined;\n        \n    try {\n      client = await this.pool.connect();\n            \n      // Extract fields\n      const { content, timestamp, metadata } = record;\n            \n      // Update record\n      const result = await client.query(\n        `UPDATE ${this.schema}.memories\n                SET content = $1, timestamp = $2, metadata = $3\n                WHERE id = $4`,\n        [content, timestamp, JSON.stringify(metadata), id]\n      );\n            \n      logger.debug(`Updated record with ID ${id} in collection ${collection}`);\n      return result.rowCount > 0;\n    } catch (error) {\n      logger.error(`Failed to update record with ID ${id} in collection ${collection}:`, error);\n      return false;\n    } finally {\n      if (client) {\n        client.release();\n      }\n    }\n  }\n\n  /**\n     * Delete a record\n     */\n  public async deleteRecord(collection: string, id: string): Promise<boolean> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    let client: PoolClient | undefined;\n        \n    try {\n      client = await this.pool.connect();\n            \n      // Delete record\n      const result = await client.query(\n        `DELETE FROM ${this.schema}.memories WHERE id = $1`,\n        [id]\n      );\n            \n      logger.debug(`Deleted record with ID ${id} from collection ${collection}`);\n      return result.rowCount > 0;\n    } catch (error) {\n      logger.error(`Failed to delete record with ID ${id} from collection ${collection}:`, error);\n      return false;\n    } finally {\n      if (client) {\n        client.release();\n      }\n    }\n  }\n\n  /**\n     * Query records\n     */\n  public async queryRecords(collection: string, query: Record<string, unknown>, limit?: number): Promise<Record<string, unknown>[]> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    let client: PoolClient | undefined;\n        \n    try {\n      client = await this.pool.connect();\n            \n      // Build query\n      let sql = `SELECT * FROM ${this.schema}.memories`;\n      const params: unknown[] = [];\n      const conditions: string[] = [];\n      let paramIndex = 1;\n            \n      // Handle filters\n      for (const key in query) {\n        if (key === '$text') {\n          const textQuery = query[key] as { $search: string };\n          conditions.push(`to_tsvector('english', content) @@ plainto_tsquery('english', $${paramIndex})`);\n          params.push(textQuery.$search);\n          paramIndex++;\n        } else if (key.startsWith('metadata.')) {\n          const metadataKey = key.substring(9);\n          conditions.push(`metadata->>'${metadataKey}' = $${paramIndex}`);\n          params.push(query[key]);\n          paramIndex++;\n        } else if (key === 'timestamp') {\n          const timestampQuery = query[key] as { $gte?: number; $lte?: number };\n          if (timestampQuery.$gte) {\n            conditions.push(`timestamp >= $${paramIndex}`);\n            params.push(timestampQuery.$gte);\n            paramIndex++;\n          }\n          if (timestampQuery.$lte) {\n            conditions.push(`timestamp <= $${paramIndex}`);\n            params.push(timestampQuery.$lte);\n            paramIndex++;\n          }\n        } else {\n          conditions.push(`${key} = $${paramIndex}`);\n          params.push(query[key]);\n          paramIndex++;\n        }\n      }\n            \n      if (conditions.length > 0) {\n        sql += ' WHERE ' + conditions.join(' AND ');\n      }\n            \n      // Add order by\n      sql += ' ORDER BY timestamp DESC';\n            \n      // Add limit\n      if (limit) {\n        sql += ` LIMIT $${paramIndex}`;\n        params.push(limit);\n      }\n            \n      // Execute query\n      const result = await client.query(sql, params);\n            \n      // Convert rows to records with proper typing\n      return result.rows.map(row => {\n        const typedRow = row as Record<string, unknown>;\n        return {\n          id: typedRow.id as string,\n          content: typedRow.content as string,\n          timestamp: typedRow.timestamp as number,\n          metadata: typedRow.metadata as Record<string, unknown>\n        };\n      });\n    } catch (error) {\n      logger.error(`Failed to query records from collection ${collection}:`, error);\n      return [];\n    } finally {\n      if (client) {\n        client.release();\n      }\n    }\n  }\n\n  /**\n     * Clear all records in a collection\n     */\n  public async clearCollection(collection: string): Promise<void> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    let client: PoolClient | undefined;\n        \n    try {\n      client = await this.pool.connect();\n            \n      // Delete all records\n      await client.query(`DELETE FROM ${this.schema}.memories`);\n            \n      logger.info(`Cleared collection ${collection}`);\n    } catch (error) {\n      logger.error(`Failed to clear collection ${collection}:`, error);\n      throw error;\n    } finally {\n      if (client) {\n        client.release();\n      }\n    }\n  }\n}\n"]}