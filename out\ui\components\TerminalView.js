"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalView = void 0;
const vscode = __importStar(require("vscode"));
const pseudoTerminal_1 = require("../../tools/pseudoTerminal");
const terminalSettingsSection_1 = require("../settings/sections/terminalSettingsSection");
class TerminalView {
    _extensionUri;
    static viewType = 'codessa.terminalView';
    _view;
    _terminal;
    _disposables = [];
    _outputBuffer = '';
    constructor(_extensionUri) {
        this._extensionUri = _extensionUri;
        // Handle configuration changes
        this._disposables.push(vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('codessa.terminal')) {
                this.updateView();
            }
        }));
    }
    resolveWebviewView(webviewView) {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };
        // Set up message handler for the webview
        this._disposables.push(webviewView.webview.onDidReceiveMessage(this.handleWebviewMessage.bind(this)));
        this.updateView();
    }
    async updateView() {
        if (!this._view)
            return;
        const settings = terminalSettingsSection_1.terminalSettings.getSettings();
        const shouldShow = settings.showTerminal &&
            (settings.viewType === 'sideView' || settings.viewType === 'both');
        if (shouldShow) {
            await this.initializeTerminal();
            this._view.webview.html = this.getTerminalHtml();
            // Send any buffered output to the webview
            if (this._outputBuffer) {
                this._view.webview.postMessage({
                    command: 'terminalOutput',
                    text: this._outputBuffer
                });
                this._outputBuffer = '';
            }
        }
        else {
            this.disposeTerminal();
            this._view.webview.html = this.getEmptyStateHtml();
        }
    }
    async initializeTerminal() {
        if (this._terminal)
            return;
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        const shell = vscode.env.shell || (process.platform === 'win32' ? 'powershell.exe' : 'bash');
        this._terminal = new pseudoTerminal_1.PseudoTerminal(shell, cwd);
        // Handle terminal output
        this._terminal.onOutput(data => {
            this.sendOutputToWebview(data);
        });
        // Handle terminal close
        this._terminal.onExit(() => {
            this.disposeTerminal();
        });
    }
    sendOutputToWebview(text) {
        if (this._view) {
            this._view.webview.postMessage({
                command: 'terminalOutput',
                text: text
            });
        }
        else {
            // Buffer the output until the webview is ready
            this._outputBuffer += text;
        }
    }
    handleWebviewMessage(message) {
        switch (message.command) {
            case 'terminalInput':
                this._terminal?.write(message.text);
                break;
            case 'toggleTerminalView':
                this.toggleTerminalView();
                break;
        }
    }
    async toggleTerminalView() {
        const settings = terminalSettingsSection_1.terminalSettings.getSettings();
        let newViewType;
        switch (settings.viewType) {
            case terminalSettingsSection_1.TerminalViewType.SideView:
                newViewType = terminalSettingsSection_1.TerminalViewType.DefaultTerminal;
                break;
            case terminalSettingsSection_1.TerminalViewType.DefaultTerminal:
                newViewType = terminalSettingsSection_1.TerminalViewType.Both;
                break;
            case terminalSettingsSection_1.TerminalViewType.Both:
            default:
                newViewType = terminalSettingsSection_1.TerminalViewType.SideView;
        }
        await terminalSettingsSection_1.terminalSettings.updateSettings({ viewType: newViewType });
        this.updateView();
    }
    disposeTerminal() {
        if (this._terminal) {
            this._terminal.dispose();
            this._terminal = undefined;
        }
        this._outputBuffer = '';
    }
    dispose() {
        this.disposeTerminal();
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];
    }
    getTerminalHtml() {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body { 
                        margin: 0; 
                        padding: 0;
                        font-family: var(--vscode-font-family);
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                    }
                    .terminal-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 8px 12px;
                        border-bottom: 1px solid var(--vscode-panel-border);
                    }
                    .terminal-title {
                        font-weight: 600;
                    }
                    .terminal-toggle {
                        background: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        padding: 4px 12px;
                        border-radius: 2px;
                        cursor: pointer;
                        font-size: 12px;
                    }
                    .terminal-toggle:hover {
                        background: var(--vscode-button-hoverBackground);
                    }
                    #terminal { 
                        background: var(--vscode-terminal-background);
                        color: var(--vscode-terminal-foreground);
                        padding: 8px;
                        height: calc(100vh - 60px);
                        overflow: auto;
                        font-family: var(--vscode-editor-font-family);
                        font-size: var(--vscode-editor-font-size);
                        white-space: pre-wrap;
                        line-height: 1.4;
                    }
                    #terminal:focus {
                        outline: none;
                    }
                </style>
            </head>
            <body>
                <div class="terminal-header">
                    <div class="terminal-title">Terminal</div>
                    <button class="terminal-toggle" id="toggleTerminal">Show in Default Terminal</button>
                </div>
                <div id="terminal" tabindex="0"></div>
                <script>
                    const terminal = document.getElementById('terminal');
                    const toggleBtn = document.getElementById('toggleTerminal');
                    
                    // Focus the terminal div for keyboard input
                    terminal.focus();
                    
                    // Handle terminal toggle
                    toggleBtn.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'toggleTerminalView'
                        });
                    });
                    
                    // Handle keyboard input
                    terminal.addEventListener('keydown', (e) => {
                        // Don't handle special keys
                        if (e.ctrlKey || e.altKey || e.metaKey) {
                            return;
                        }
                        
                        // Send the key to the extension
                        vscode.postMessage({
                            command: 'terminalInput',
                            text: e.key
                        });
                        
                        // Prevent default to avoid double input
                        e.preventDefault();
                    });
                    
                    // Handle messages from the extension
                    window.addEventListener('message', event => {
                        const message = event.data;
                        switch (message.command) {
                            case 'terminalOutput':
                                terminal.textContent += message.text;
                                terminal.scrollTop = terminal.scrollHeight;
                                break;
                            case 'terminalClear':
                                terminal.textContent = '';
                                break;
                        }
                    });
                </script>
            </body>
            </html>`;
    }
    getEmptyStateHtml() {
        return `
            <!DOCTYPE html>
            <html>
            <body style="padding: 20px; font-family: var(--vscode-font-family);">
                <p>Terminal is disabled or set to show in default terminal view.</p>
            </body>
            </html>`;
    }
    dispose() {
        this.disposeTerminal();
    }
}
exports.TerminalView = TerminalView;
//# sourceMappingURL=TerminalView.js.map