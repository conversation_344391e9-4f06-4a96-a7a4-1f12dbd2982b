// Shared types/interfaces for settings panel

// Base types
export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'trace';
export type Theme = 'light' | 'dark' | 'system' | 'high-contrast';
export type ChatLayout = 'default' | 'compact' | 'detailed';
export type VectorStoreType = 'memory' | 'pinecone' | 'weaviate' | 'chroma' | 'redis';

// Provider and Model types
export type ProviderType = 
  | 'openai' 
  | 'googleai' 
  | 'anthropic' 
  | 'mistral' 
  | 'ollama' 
  | 'ai21'
  | 'cohere'
  | 'togetherai'
  | 'openrouter'
  | 'huggingface'
  | 'codellama'
  | 'santacoder'
  | 'starcoder'
  | 'xwincoder'
  | 'wizardcoder'
  | 'stablestudio'
  | 'noushermes'
  | 'custom';

export interface ProviderConfig {
    id: string;
    name: string;
    type: ProviderType;
    apiKey?: string;
    baseUrl?: string;
    isActive: boolean;
    defaultModel?: string;
    maxTokens?: number;
    temperature?: number;
}

export interface ModelConfig {
    id: string;
    name: string;
    provider: string;
    contextWindow: number;
    maxTokens: number;
    trainingData: string;
    capabilities: string[];
}

// Agent and Workflow types
export interface AgentConfig {
    id: string;
    name: string;
    description: string;
    role: string;
    model?: string;
    provider?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    isActive: boolean;
    tools?: string[];
    memoryConfig?: {
        enabled: boolean;
        maxMemories: number;
        relevanceThreshold: number;
    };
}

export interface WorkflowStep {
    id: string;
    type: 'agent' | 'tool' | 'condition' | 'loop';
    config: Record<string, unknown>;
    nextStepId?: string;
}

export interface WorkflowConfig {
    id: string;
    name: string;
    description: string;
    steps: WorkflowStep[];
    isActive: boolean;
    triggerEvents: string[];
}

// TTS and Voice types
export type TTSProvider = 'system' | 'elevenlabs' | 'google' | 'amazon' | 'microsoft';

export interface TTSConfig {
    enabled: boolean;
    provider: TTSProvider;
    voice: string;
    rate: number;
    pitch: number;
    volume: number;
    autoSpeak: boolean;
    apiKeys: {
        elevenlabs?: string;
        google?: string;
        amazon?: string;
        microsoft?: string;
    };
    advanced: {
        ssmlEnabled: boolean;
        emotionControl: boolean;
        qualityMode: 'fast' | 'balanced' | 'high';
        cacheEnabled: boolean;
        streamingEnabled: boolean;
    };
}

// Database types
export type DatabaseType = 'sqlite' | 'postgres' | 'mongodb' | 'mysql';

export interface DatabaseConfig {
    type: DatabaseType;
    connectionString?: string;
    host?: string;
    port?: number;
    database?: string;
    username?: string;
    password?: string;
    ssl?: boolean;
    synchronize: boolean;
    logging: boolean;
}

// Knowledgebase types
export interface KnowledgebaseSource {
    id: string;
    type: 'file' | 'folder' | 'url' | 'git' | 'database';
    path: string;
    name: string;
    description?: string;
    isActive: boolean;
    lastSynced?: number;
    syncStatus: 'idle' | 'syncing' | 'error' | 'success';
    error?: string;
    metadata?: Record<string, unknown>;
}

export interface KnowledgebaseConfig {
    enabled: boolean;
    sources: KnowledgebaseSource[];
    autoSync: boolean;
    syncInterval: number;
    chunkSize: number;
    chunkOverlap: number;
    vectorStore: VectorStoreType;
}

// Workspace types
export type FileType = 'file' | 'directory' | 'symlink' | 'unknown';

export interface WorkspaceFile {
    path: string;
    name: string;
    type: FileType;
    size?: number;
    lastModified?: number;
    tags: string[];
    metadata: Record<string, unknown>;
    isExcluded: boolean;
    isBinary: boolean;
    encoding: string;
    languageId?: string;
}

export type TeamRole = 'owner' | 'admin' | 'developer' | 'viewer' | 'guest';

export interface WorkspaceTeamMember {
    id: string;
    name: string;
    email: string;
    role: TeamRole;
    avatarUrl?: string;
    lastActive?: number;
    permissions: string[];
    isActive: boolean;
    joinedAt: number;
}

export type DocumentationItemType = 'file' | 'link' | 'note' | 'code' | 'image' | 'diagram' | 'api';

export interface WorkspaceDocumentationItem {
    id: string;
    type: DocumentationItemType;
    title: string;
    content: string;
    path?: string;
    url?: string;
    tags: string[];
    createdAt: number;
    updatedAt: number;
    createdBy: string;
    updatedBy: string;
    metadata: Record<string, unknown>;
    isPinned: boolean;
    isArchived: boolean;
}

export interface WorkspaceKnowledgebaseConfig {
    sources: KnowledgebaseSource[];
    shared: boolean;
    autoSync: boolean;
    syncInterval: number;
    lastSynced?: number;
    syncStatus: 'idle' | 'syncing' | 'error' | 'success';
    lastError?: string;
    chunkSize: number;
    chunkOverlap: number;
    includePatterns: string[];
    excludePatterns: string[];
}

export interface Workspace {
    id: string;
    name: string;
    path: string;
    description: string;
    tags: string[];
    files: WorkspaceFile[];
    team: WorkspaceTeamMember[];
    memory: string;
    documentation: WorkspaceDocumentationItem[];
    knowledgebase: WorkspaceKnowledgebaseConfig;
    createdAt: number;
    updatedAt: number;
    isActive: boolean;
    settings: {
        autoSave: boolean;
        autoFormat: boolean;
        lintOnSave: boolean;
        formatOnSave: boolean;
        experimentalFeatures: boolean;
    };
}

// Settings Section Type
export interface SettingsSection {
    id: string;
    title: string;
    icon: string;
    description: string;
    order: number;
    component: string;
}

// Main Settings interface
export interface Settings {
    // General Settings
    logLevel: LogLevel;
    theme: Theme;
    fontSize: number;
    chatLayout: ChatLayout;
    autoSave: boolean;
    autoSaveDelay: number;
    showLineNumbers: boolean;
    showMinimap: boolean;
    
    // Provider & Model Settings
    defaultProvider: string;
    defaultModel: string;
    providers: ProviderConfig[];
    models: ModelConfig[];
    
    // Agent Settings
    agents: AgentConfig[];
    defaultAgent: string;
    
    // Workflow Settings
    workflows: WorkflowConfig[];
    
    // Memory Settings
    memoryEnabled: boolean;
    maxMemories: number;
    relevanceThreshold: number;
    contextWindowSize: number;
    conversationHistorySize: number;
    vectorStore: VectorStoreType;
    
    // TTS Settings
    tts: TTSConfig;
    
    // Database Settings
    database: DatabaseConfig;
    
    // Knowledgebase Settings
    knowledgebase: KnowledgebaseConfig;
    
    // Workspace Settings
    workspace?: Workspace;
    workspaces?: Workspace[];
    activeWorkspace?: string;
    
    // UI/UX Settings
    showTimestamps: boolean;
    showCodeLens: boolean;
    showInlineSuggestions: boolean;
    showStatusBar: boolean;
    
    // Advanced Settings
    experimentalFeatures: boolean;
    developerMode: boolean;
    telemetryEnabled: boolean;
    crashReporting: boolean;
}
