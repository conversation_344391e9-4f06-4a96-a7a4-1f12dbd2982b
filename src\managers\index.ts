/**
 * Export all manager interfaces, workflows, and shared utilities for easy access
 */

// ===== Manager Interfaces =====
export { IWorkflowManager, IMCPManager, IPromptManager, IKnowledgebaseManager } from '../types/managers';

// ===== Workflow Types and Core =====
// Export types and core workflow components
export * from '../agents/workflows/types';
export * from '../agents/workflows/graph';
export * from '../agents/workflows/workflowRegistry';
export * from '../agents/workflows/memory';

// ===== Workflow Templates =====
// Basic and advanced templates
export * from '../agents/workflows/templates';
export * from '../agents/workflows/advancedTemplates';

// Specialized templates with explicit exports to avoid naming conflicts
export {
  createCodeRefactoringWorkflow,
  createDebuggingWorkflow,
  createAskWorkflow,
  createEditWorkflow,
  createCodeGenWorkflow,
  createAgenticWorkflow,
  createUXDesignWorkflow,
  createDevOpsWorkflow,
  createAgileWorkflow as createAgileTemplateWorkflow,
  createXPWorkflow as createXPTemplateWorkflow,
  createScrumWorkflow as createScrumTemplateWorkflow,
  WorkflowFactory
} from '../agents/workflows/specializedTemplates';

// ===== Workflow Categories =====
// PR Workflows
export {
  createPRCreationWorkflow,
  createPRReviewWorkflow
} from '../agents/workflows/prWorkflows';

// Checkpoint Workflow
export { createCheckpointWorkflow } from '../agents/workflows/checkpointWorkflow';
export { checkpointManager } from '../checkpoint';
export type { CheckpointManager, Checkpoint } from '../checkpoint';

// MCP Workflow
export { createMCPWorkflow } from '../agents/workflows/mcpWorkflow';
export { mcpManager } from '../mcp';
export type { MCPManager, MCPContext } from '../mcp';

// Advanced Refactoring Workflows
export {
  createPatternRefactoringWorkflow,
  createTechnicalDebtWorkflow
} from '../agents/workflows/advancedRefactoring';

// SDLC Workflows
export {
  createPlanningWorkflow,
  createRequirementsWorkflow,
  createDesignWorkflow,
  createImplementationWorkflow,
  createTestingWorkflow,
  createDeploymentWorkflow,
  createMaintenanceWorkflow
} from '../agents/workflows/sdlcWorkflows';

// Methodology Workflows
export {
  createAgileWorkflow,
  createScrumWorkflow,
  createXPWorkflow,
  createWaterfallWorkflow,
  createDevOpsWorkflow as createDevOpsMethodologyWorkflow
} from '../agents/workflows/methodologyWorkflows';

// Research Workflows
export {
  createResearchWorkflow,
  createAcademicResearchWorkflow
} from '../agents/workflows/researchWorkflow';

// UI/UX Workflows
export {
  createUIUXWorkflow,
  createUIComponentWorkflow
} from '../agents/workflows/uiUxWorkflow';

// Documentation Workflows
export {
  createDocumentationWorkflow,
  createAPIDocumentationWorkflow
} from '../agents/workflows/documentationWorkflow';

// Technical Debt Workflows
export {
  createTechnicalDebtWorkflow as createTechnicalDebtReductionWorkflow,
  createLegacyCodeModernizationWorkflow
} from '../agents/workflows/technicalDebtWorkflow';

// Vector Stores
export * from '../agents/workflows/vectorStores';

// Document QA Workflows
export {
  createDocumentQAWorkflow,
  createPDFQAWorkflow
} from '../agents/workflows/documentQAWorkflow';

// Memory-Enhanced Workflows
export {
  createMemoryEnhancedWorkflow
} from '../agents/workflows/memoryEnhancedWorkflow';

// Pattern-Refactoring Workflows
export {
  createPatternRefactoringWorkflow as createPatternBasedRefactoringWorkflow,
  createDesignPatternImplementationWorkflow
} from '../agents/workflows/patternRefactoringWorkflow';

// Knowledge Base Workflows
export {
  createKnowledgeBaseWorkflow,
  createKnowledgeRetrievalWorkflow
} from '../agents/workflows/knowledgeBaseWorkflow';

// Multi-Repository Workflows
export {
  createMultiRepoWorkflow,
  createCrossRepoDependencyWorkflow
} from '../agents/workflows/multiRepoWorkflow';

// Collaborative Coding Workflows
export {
  createCollaborativeCodingWorkflow,
  createPairProgrammingWorkflow
} from '../agents/workflows/collaborativeCodingWorkflow';

// ===== Core Polyfills and Utilities =====
// Re-export core polyfills for langchain
// This provides compatibility shims for various langchain components
export * from '../agents/workflows/corePolyfill';

// Re-export codessa graph implementation
export * from '../agents/workflows/codessaGraph';

// ===== Helper Functions =====
import { workflowRegistry } from '../agents/workflows/workflowRegistry';

/**
 * Get all registered workflows
 */
export function getAllWorkflows() {
  return workflowRegistry.getAllWorkflows();
}

/**
 * Get a workflow by ID
 */
export function getWorkflowById(id: string) {
  return workflowRegistry.getWorkflow(id);
}

/**
 * Create a workflow instance
 */
export function createWorkflowInstance(id: string) {
  return workflowRegistry.createWorkflowInstance(id);
}