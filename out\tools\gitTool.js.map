{"version": 3, "file": "gitTool.js", "sourceRoot": "", "sources": ["../../src/tools/gitTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,2CAA6B;AAC7B,kDAAoC;AACpC,uDAAkI;AAElI,6BAAwB;AACxB,2CAA6B;AAC7B,sCAAmC;AAEnC,MAAa,OAAO;IACD,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAE9C,EAAE,GAAG,KAAK,CAAC;IACX,IAAI,GAAG,2BAA2B,CAAC;IACnC,WAAW,GAAG,+GAA+G,CAAC;IAC9H,IAAI,GAAG,cAAc,CAAC,CAAC,oBAAoB;IAC3C,OAAO,GAAwB;QACtC,QAAQ,EAAE;YACR,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,kBAAkB;YAC/B,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;aACpF,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,6CAA6C,EAAE;iBACrF;gBACD,QAAQ,EAAE,EAAE;aACb;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,IAAI,CAAC;oBACH,gCAAgC;oBAChC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAC7D,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;wBACzB,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,kBAAkB,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE;4BACpD,MAAM,EAAE,QAAQ;4BAChB,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,mCAAmC;oBACnC,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;oBAEjD,mCAAmC;oBACnC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACzF,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,mEAAmE;4BAC1E,MAAM,EAAE,QAAQ;4BAChB,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;oBAE5D,iDAAiD;oBACjD,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAkD,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBACpG,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;4BAC7D,IAAI,GAAG,EAAE,CAAC;gCACR,OAAO,CAAC;oCACN,MAAM,EAAE,MAAM,IAAI,EAAE;oCACpB,MAAM,EAAE,MAAM,IAAI,GAAG,CAAC,OAAO;oCAC7B,KAAK,EAAE,GAAG;iCACX,CAAC,CAAC;4BACL,CAAC;iCAAM,CAAC;gCACN,OAAO,CAAC;oCACN,MAAM,EAAE,MAAM,IAAI,EAAE;oCACpB,MAAM,EAAE,MAAM,IAAI,EAAE;iCACrB,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;oBAEH,oBAAoB;oBACpB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;wBACjB,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,sBAAsB,MAAM,CAAC,MAAM,EAAE;4BAC5C,MAAM,EAAE,QAAQ;4BAChB,UAAU;4BACV,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,oDAAoD;yBAClF,CAAC;oBACJ,CAAC;oBAED,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;wBACpD,MAAM,EAAE,QAAQ;wBAChB,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,0CAA0C,KAAK,CAAC,OAAO,EAAE;wBAChE,MAAM,EAAE,QAAQ;wBAChB,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;SACF;QACD,KAAK,EAAE;YACL,EAAE,EAAE,KAAK;YACT,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,yBAAyB;YACtC,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;aAC/D,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mCAAmC,EAAE;iBAC3E;gBACD,QAAQ,EAAE,CAAC,MAAM,CAAC;aACnB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,IAAI,CAAC;oBACH,gCAAgC;oBAChC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAC7D,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;wBACzB,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,kBAAkB,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE;4BACpD,MAAM,EAAE,KAAK;4BACb,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,iDAAiD;oBACjD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;wBAChB,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,yEAAyE;4BAChF,MAAM,EAAE,KAAK;4BACb,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,mCAAmC;oBACnC,MAAM,IAAI,GAAI,KAAK,CAAC,IAAe,CAAC,IAAI,EAAE,CAAC;oBAE3C,mCAAmC;oBACnC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACzF,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,mEAAmE;4BAC1E,MAAM,EAAE,KAAK;4BACb,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;oBAE5D,iDAAiD;oBACjD,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAkD,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBACpG,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;4BAC1D,IAAI,GAAG,EAAE,CAAC;gCACR,OAAO,CAAC;oCACN,MAAM,EAAE,MAAM,IAAI,EAAE;oCACpB,MAAM,EAAE,MAAM,IAAI,GAAG,CAAC,OAAO;oCAC7B,KAAK,EAAE,GAAG;iCACX,CAAC,CAAC;4BACL,CAAC;iCAAM,CAAC;gCACN,OAAO,CAAC;oCACN,MAAM,EAAE,MAAM,IAAI,EAAE;oCACpB,MAAM,EAAE,MAAM,IAAI,EAAE;iCACrB,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;oBAEH,oBAAoB;oBACpB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;wBACjB,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,mBAAmB,MAAM,CAAC,MAAM,EAAE;4BACzC,MAAM,EAAE,KAAK;4BACb,UAAU;4BACV,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,oDAAoD;yBAClF,CAAC;oBACJ,CAAC;oBAED,6EAA6E;oBAC7E,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,wBAAwB,IAAI,EAAE,CAAC;oBAE9F,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM;wBACN,MAAM,EAAE,KAAK;wBACb,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,uCAAuC,KAAK,CAAC,OAAO,EAAE;wBAC7D,MAAM,EAAE,KAAK;wBACb,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;SACF;QACD,QAAQ,EAAE;YACR,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,wBAAwB;YACrC,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;aACxD,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,4BAA4B,EAAE;iBACpE;gBACD,QAAQ,EAAE,CAAC,MAAM,CAAC;aACnB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;gBAClC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC7D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YAChF,CAAC;SACF;QACD,MAAM,EAAE;YACN,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,yBAAyB;YACtC,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;aACrD,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE;iBACjE;gBACD,QAAQ,EAAE,CAAC,MAAM,CAAC;aACnB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;gBAClC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC3D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;YAC9E,CAAC;SACF;QACD,MAAM,EAAE;YACN,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,mBAAmB;YAChC,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;aACrD,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE;iBACjE;gBACD,QAAQ,EAAE,CAAC,MAAM,CAAC;aACnB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;gBAClC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC3D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;YAC9E,CAAC;SACF;QACD,QAAQ,EAAE;YACR,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,kBAAkB;YAC/B,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;aACjE,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;iBAClE;gBACD,QAAQ,EAAE,EAAE;aACb;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC7D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YAChF,CAAC;SACF;QACD,MAAM,EAAE;YACN,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,YAAY;YACzB,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;aACjE,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;iBAClE;gBACD,QAAQ,EAAE,EAAE;aACb;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC3D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;YAC9E,CAAC;SACF;QACD,KAAK,EAAE;YACL,EAAE,EAAE,KAAK;YACT,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,WAAW;YACxB,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;aAChE,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE;iBACjE;gBACD,QAAQ,EAAE,EAAE;aACb;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC1D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;YAC7E,CAAC;SACF;QACD,OAAO,EAAE;YACP,GAAG,IAAI,8BAAY,EAAE;YACrB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC5D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;YAC/E,CAAC;SACF;QACD,QAAQ,EAAE;YACR,GAAG,IAAI,+BAAa,EAAE;YACtB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC7D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YAChF,CAAC;SACF;QACD,YAAY,EAAE;YACZ,GAAG,IAAI,mCAAiB,EAAE;YAC1B,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,mBAAmB,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAClE,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC;YACpF,CAAC;SACF;QACD,QAAQ,EAAE;YACR,GAAG,IAAI,+BAAa,EAAE;YACtB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC7D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YAChF,CAAC;SACF;QACD,KAAK,EAAE;YACL,GAAG,IAAI,4BAAU,EAAE;YACnB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBAC1D,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;YAC7E,CAAC;SACF;QACD,aAAa,EAAE;YACb,GAAG,IAAI,oCAAkB,EAAE;YAC3B,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,6BAA6B;gBAC7B,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;gBAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3D,EAAE,CAAC,IAAI,CAAC,4CAA4C,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;wBACrF,IAAI,GAAG,IAAI,CAAC,MAAM;4BAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;YACrF,CAAC;SACF;KACF,CAAA;IAEM,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,WAAoB,EAAE,SAAkB;QACtF,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,KAAK,GAAG,WAAW,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,WAAW,KAAK,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7E,MAAM,OAAO,GAAG,2CAA2C,KAAK,QAAQ,QAAQ,GAAG,CAAC;QAEpF,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC1C,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtD,MAAM,MAAM,GAAc;oBACxB,IAAI;oBACJ,MAAM;oBACN,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;oBACvD,OAAO;oBACP,OAAO,EAAE,EAAE;iBACZ,CAAC;gBACF,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,QAAgB;QAC1D,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,OAAO,GAAG,wCAAwC,UAAU,QAAQ,QAAQ,GAAG,CAAC;QAEtF,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC1C,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACtD,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;gBAClD,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;gBAClD,IAAI,UAAU,GAAqC,UAAU,CAAC;gBAC9D,IAAI,YAAY,GAAG,CAAC,IAAI,YAAY,KAAK,CAAC;oBAAE,UAAU,GAAG,OAAO,CAAC;gBACjE,IAAI,YAAY,GAAG,CAAC,IAAI,YAAY,KAAK,CAAC;oBAAE,UAAU,GAAG,SAAS,CAAC;gBAEnE,MAAM,MAAM,GAAe;oBACzB,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,QAAQ,IAAI,QAAQ,UAAU,GAAG;oBAC9C,WAAW,EAAE,CAAC,EAAE,SAAS;oBACzB,QAAQ,EAAE;wBACR,QAAQ,EAAE,IAAI;wBACd,SAAS,EAAE,YAAY;wBACvB,SAAS,EAAE,YAAY;qBACxB;iBACF,CAAC;gBACF,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,UAAkB,EAAE,QAAgB;QACtE,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;QACpF,MAAM,OAAO,GAAG,YAAY,UAAU,KAAK,iBAAiB,GAAG,CAAC;QAEhE,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YAC5D,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,4BAA4B,QAAQ,cAAc,UAAU,uCAAuC,KAAK,EAAE,CAAC,CAAC;YACxH,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QAC3F,IAAI,CAAC;YACH,4FAA4F;YAC5F,MAAM,MAAM,GAAG,UAAU,IAAI,KAAK,CAAC,MAAgB,CAAC;YAEpD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9D,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,oDAAoD,gBAAgB,EAAE;oBAC7E,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;oBACV,QAAQ,EAAE;wBACR,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;qBAC5C;iBACF,CAAC;YACJ,CAAC;YAED,kCAAkC;YAClC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9D,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB,MAAM,yBAAyB,gBAAgB,EAAE;oBAChF,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;oBACV,QAAQ,EAAE;wBACR,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;wBAC3C,eAAe,EAAE,MAAM;qBACxB;iBACF,CAAC;YACJ,CAAC;YAED,+DAA+D;YAC/D,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;YACjC,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;gBAC5B,OAAO,WAAW,CAAC,MAAM,CAAC;YAC5B,CAAC;YAED,gEAAgE;YAChE,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACnC,iEAAiE;gBACjE,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,6DAA6D;gBAC7D,MAAM,MAAM,GAAG,MAAO,UAAkB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBAEvE,gDAAgD;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;wBACnB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;oBACzB,CAAC;oBACD,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;wBACvB,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC;oBAC7B,CAAC;gBACH,CAAC;gBAED,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,oDAAoD;YACpD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,iCAAiC,KAAK,CAAC,OAAO,EAAE;gBACvD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,UAAU,EAAE,KAAK,CAAC,KAAK;iBACxB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAxnBD,0BAwnBC;AAEY,QAAA,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport * as util from 'util';\nimport * as cp from 'child_process';\nimport { GitStashTool, GitRevertTool, GitCherryPickTool, GitRebaseTool, GitTagTool, GitBranchGraphTool } from './advancedGitTool';\nimport { GitCommit, CodeChange } from './timeTravelDebuggingTool';\nimport { z } from 'zod';\nimport * as path from 'path';\nimport { logger } from '../logger';\n\nexport class GitTool implements ITool {\n  private readonly execPromise = util.promisify(cp.exec);\n\n  readonly id = 'git';\n  readonly name = 'Git Operations (Advanced)';\n  readonly description = 'Stage, commit, push, pull, branch, diff, log, stash, revert, cherry-pick, rebase, tag, and show branch graph.';\n  readonly type = 'multi-action'; // Required by ITool\n  readonly actions: Record<string, any> = {\n    'status': {\n      id: 'status',\n      name: 'Git Status',\n      description: 'Show git status.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        args: z.string().optional().describe('Additional arguments for git status command')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          args: { type: 'string', description: 'Additional arguments for git status command' }\n        },\n        required: []\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        try {\n          // Validate input against schema\n          const parsedInput = this.singleActionSchema.safeParse(input);\n          if (!parsedInput.success) {\n            return {\n              success: false,\n              error: `Invalid input: ${parsedInput.error.message}`,\n              toolId: 'status',\n              actionName\n            };\n          }\n\n          // Safely extract and sanitize args\n          const args = (input.args as string || '').trim();\n\n          // Check if workspace folder exists\n          if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {\n            return {\n              success: false,\n              error: 'No workspace folder open. Git commands require an open workspace.',\n              toolId: 'status',\n              actionName\n            };\n          }\n\n          const cwd = vscode.workspace.workspaceFolders[0].uri.fsPath;\n\n          // Execute git command with proper error handling\n          const result = await new Promise<{ stdout: string, stderr: string, error?: any }>((resolve, reject) => {\n            cp.exec(`git status ${args}`, { cwd }, (err, stdout, stderr) => {\n              if (err) {\n                resolve({\n                  stdout: stdout || '',\n                  stderr: stderr || err.message,\n                  error: err\n                });\n              } else {\n                resolve({\n                  stdout: stdout || '',\n                  stderr: stderr || ''\n                });\n              }\n            });\n          });\n\n          // Handle the result\n          if (result.error) {\n            return {\n              success: false,\n              error: `Git status failed: ${result.stderr}`,\n              toolId: 'status',\n              actionName,\n              output: result.stdout.trim() // Include any stdout that might have been generated\n            };\n          }\n\n          return {\n            success: true,\n            output: result.stdout.trim() || result.stderr.trim(),\n            toolId: 'status',\n            actionName\n          };\n        } catch (error: any) {\n          return {\n            success: false,\n            error: `Unexpected error executing git status: ${error.message}`,\n            toolId: 'status',\n            actionName\n          };\n        }\n      }\n    },\n    'add': {\n      id: 'add',\n      name: 'Git Add',\n      description: 'Stage files for commit.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        args: z.string().describe('Files to stage or staging options')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          args: { type: 'string', description: 'Files to stage or staging options' }\n        },\n        required: ['args']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        try {\n          // Validate input against schema\n          const parsedInput = this.singleActionSchema.safeParse(input);\n          if (!parsedInput.success) {\n            return {\n              success: false,\n              error: `Invalid input: ${parsedInput.error.message}`,\n              toolId: 'add',\n              actionName\n            };\n          }\n\n          // Check if args is provided (required parameter)\n          if (!input.args) {\n            return {\n              success: false,\n              error: 'The \\'args\\' parameter is required for git add. Specify files to stage.',\n              toolId: 'add',\n              actionName\n            };\n          }\n\n          // Safely extract and sanitize args\n          const args = (input.args as string).trim();\n\n          // Check if workspace folder exists\n          if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {\n            return {\n              success: false,\n              error: 'No workspace folder open. Git commands require an open workspace.',\n              toolId: 'add',\n              actionName\n            };\n          }\n\n          const cwd = vscode.workspace.workspaceFolders[0].uri.fsPath;\n\n          // Execute git command with proper error handling\n          const result = await new Promise<{ stdout: string, stderr: string, error?: any }>((resolve, reject) => {\n            cp.exec(`git add ${args}`, { cwd }, (err, stdout, stderr) => {\n              if (err) {\n                resolve({\n                  stdout: stdout || '',\n                  stderr: stderr || err.message,\n                  error: err\n                });\n              } else {\n                resolve({\n                  stdout: stdout || '',\n                  stderr: stderr || ''\n                });\n              }\n            });\n          });\n\n          // Handle the result\n          if (result.error) {\n            return {\n              success: false,\n              error: `Git add failed: ${result.stderr}`,\n              toolId: 'add',\n              actionName,\n              output: result.stdout.trim() // Include any stdout that might have been generated\n            };\n          }\n\n          // If no output was generated (common for git add), provide a helpful message\n          const output = result.stdout.trim() || result.stderr.trim() || `Successfully staged: ${args}`;\n\n          return {\n            success: true,\n            output,\n            toolId: 'add',\n            actionName\n          };\n        } catch (error: any) {\n          return {\n            success: false,\n            error: `Unexpected error executing git add: ${error.message}`,\n            toolId: 'add',\n            actionName\n          };\n        }\n      }\n    },\n    'commit': {\n      id: 'commit',\n      name: 'Git Commit',\n      description: 'Commit staged changes.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        args: z.string().describe('Commit message and options')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          args: { type: 'string', description: 'Commit message and options' }\n        },\n        required: ['args']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const args = input.args as string;\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git commit ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'commit', actionName };\n      }\n    },\n    'push': {\n      id: 'push',\n      name: 'Git Push',\n      description: 'Push commits to remote.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        args: z.string().describe('Push options and target')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          args: { type: 'string', description: 'Push options and target' }\n        },\n        required: ['args']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const args = input.args as string;\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git push ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'push', actionName };\n      }\n    },\n    'pull': {\n      id: 'pull',\n      name: 'Git Pull',\n      description: 'Pull from remote.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        args: z.string().describe('Pull options and source')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          args: { type: 'string', description: 'Pull options and source' }\n        },\n        required: ['args']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const args = input.args as string;\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git pull ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'pull', actionName };\n      }\n    },\n    'branch': {\n      id: 'branch',\n      name: 'Git Branch',\n      description: 'Manage branches.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        args: z.string().optional().describe('Branch options and names')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          args: { type: 'string', description: 'Branch options and names' }\n        },\n        required: []\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const args = input.args as string || '';\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git branch ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'branch', actionName };\n      }\n    },\n    'diff': {\n      id: 'diff',\n      name: 'Git Diff',\n      description: 'Show diff.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        args: z.string().optional().describe('Diff options and targets')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          args: { type: 'string', description: 'Diff options and targets' }\n        },\n        required: []\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const args = input.args as string || '';\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git diff ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'diff', actionName };\n      }\n    },\n    'log': {\n      id: 'log',\n      name: 'Git Log',\n      description: 'Show log.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        args: z.string().optional().describe('Log options and filters')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          args: { type: 'string', description: 'Log options and filters' }\n        },\n        required: []\n      },\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const args = input.args as string || '';\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git log ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'log', actionName };\n      }\n    },\n    'stash': {\n      ...new GitStashTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        const args = input.args as string || '';\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git stash ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'stash', actionName };\n      }\n    },\n    'revert': {\n      ...new GitRevertTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        const args = input.args as string || '';\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git revert ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'revert', actionName };\n      }\n    },\n    'cherryPick': {\n      ...new GitCherryPickTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        const args = input.args as string || '';\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git cherry-pick ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'cherryPick', actionName };\n      }\n    },\n    'rebase': {\n      ...new GitRebaseTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        const args = input.args as string || '';\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git rebase ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'rebase', actionName };\n      }\n    },\n    'tag': {\n      ...new GitTagTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        const args = input.args as string || '';\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec(`git tag ${args}`, { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'tag', actionName };\n      }\n    },\n    'branchGraph': {\n      ...new GitBranchGraphTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        // Placeholder implementation\n        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n        const result = await new Promise<string>((resolve, reject) => {\n          cp.exec('git log --graph --oneline --all --decorate', { cwd }, (err, stdout, stderr) => {\n            if (err && !stdout) return reject(stderr || err.message);\n            resolve(stdout || stderr);\n          });\n        });\n        return { success: true, output: result.trim(), toolId: 'branchGraph', actionName };\n      }\n    },\n  }\n\n  public async getCommitHistory(filePath: string, startCommit?: string, endCommit?: string): Promise<GitCommit[]> {\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    if (!cwd) {\n      throw new Error('No workspace folder open.');\n    }\n\n    const range = startCommit && endCommit ? `${startCommit}..${endCommit}` : '';\n    const command = `git log --pretty=format:'%H|%at|%an|%s' ${range} -- \"${filePath}\"`;\n\n    try {\n      const { stdout } = await this.execPromise(command, { cwd });\n      if (!stdout) {\n        return [];\n      }\n      return stdout.trim().split('\\n').map(line => {\n        const [hash, date, author, message] = line.split('|');\n        const commit: GitCommit = {\n          hash,\n          author,\n          date: new Date(parseInt(date, 10) * 1000).toISOString(),\n          message,\n          changes: [],\n        };\n        return commit;\n      });\n    } catch (error) {\n      console.error('Error getting commit history:', error);\n      return [];\n    }\n  }\n\n  public async getChanges(commitHash: string, filePath: string): Promise<CodeChange[]> {\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    if (!cwd) {\n      throw new Error('No workspace folder open.');\n    }\n\n    const command = `git show --pretty=tformat: --numstat ${commitHash} -- \"${filePath}\"`;\n\n    try {\n      const { stdout } = await this.execPromise(command, { cwd });\n      if (!stdout) {\n        return [];\n      }\n\n      return stdout.trim().split('\\n').map(line => {\n        const [additions, deletions, path] = line.split('\\t');\n        const numAdditions = parseInt(additions, 10) || 0;\n        const numDeletions = parseInt(deletions, 10) || 0;\n        let changeType: 'added' | 'modified' | 'deleted' = 'modified';\n        if (numAdditions > 0 && numDeletions === 0) changeType = 'added';\n        if (numDeletions > 0 && numAdditions === 0) changeType = 'deleted';\n\n        const change: CodeChange = {\n          type: changeType,\n          description: `File ${path} was ${changeType}.`,\n          impactLevel: 2, // medium\n          metadata: {\n            filePath: path,\n            additions: numAdditions,\n            deletions: numDeletions,\n          },\n        };\n        return change;\n      });\n    } catch (error) {\n      console.error(`Error getting changes for commit ${commitHash}:`, error);\n      return [];\n    }\n  }\n\n  public async getFileContentAtCommit(commitHash: string, filePath: string): Promise<string> {\n    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;\n    if (!cwd) {\n      throw new Error('No workspace folder open.');\n    }\n\n    const sanitizedFilePath = path.normalize(filePath).replace(/^(\\.\\.(\\/|\\\\|$))+/, '');\n    const command = `git show ${commitHash}:\"${sanitizedFilePath}\"`;\n\n    try {\n      const { stdout } = await this.execPromise(command, { cwd });\n      return stdout;\n    } catch (error) {\n      logger.warn(`Could not get content of ${filePath} at commit ${commitHash}. It might not have existed. Error: ${error}`);\n      return '';\n    }\n  }\n\n  public async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    try {\n      // For backward compatibility, check if actionName is undefined and try to get it from input\n      const action = actionName || input.action as string;\n\n      if (!action) {\n        const availableActions = Object.keys(this.actions).join(', ');\n        return {\n          success: false,\n          error: `Action parameter is required. Available actions: ${availableActions}`,\n          toolId: this.id,\n          actionName,\n          metadata: {\n            availableActions: Object.keys(this.actions)\n          }\n        };\n      }\n\n      // Validate that the action exists\n      const actionTool = this.actions[action];\n      if (!actionTool) {\n        const availableActions = Object.keys(this.actions).join(', ');\n        return {\n          success: false,\n          error: `Unknown git action: '${action}'. Available actions: ${availableActions}`,\n          toolId: this.id,\n          actionName,\n          metadata: {\n            availableActions: Object.keys(this.actions),\n            requestedAction: action\n          }\n        };\n      }\n\n      // Create a clean copy of the input without the action property\n      const actionInput = { ...input };\n      if ('action' in actionInput) {\n        delete actionInput.action;\n      }\n\n      // Check if the action tool has the new execute method signature\n      if (actionTool.execute.length >= 2) {\n        // Pass the action as the actionName parameter to the nested tool\n        return await actionTool.execute(action, actionInput, context);\n      } else {\n        // Fallback for older tools that don't have the new signature\n        const result = await (actionTool as any).execute(actionInput, context);\n\n        // Ensure the result has the required properties\n        if (result) {\n          if (!result.toolId) {\n            result.toolId = action;\n          }\n          if (!result.actionName) {\n            result.actionName = action;\n          }\n        }\n\n        return result;\n      }\n    } catch (error: any) {\n      // Catch any unexpected errors in the execution flow\n      return {\n        success: false,\n        error: `Unexpected error in Git tool: ${error.message}`,\n        toolId: this.id,\n        actionName,\n        metadata: {\n          errorStack: error.stack\n        }\n      };\n    }\n  }\n}\n\nexport const gitTool = new GitTool();\n"]}