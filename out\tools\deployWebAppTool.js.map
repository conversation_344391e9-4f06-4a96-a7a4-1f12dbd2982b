{"version": 3, "file": "deployWebAppTool.js", "sourceRoot": "", "sources": ["../../src/tools/deployWebAppTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,+CAAiC;AACjC,kDAAoC;AACpC,yDAAyD;AACzD,6BAAwB;AAExB,MAAa,gBAAgB;IAClB,EAAE,GAAG,cAAc,CAAC;IACpB,IAAI,GAAG,2BAA2B,CAAC;IACnC,WAAW,GAAG,uEAAuE,CAAC;IACtF,IAAI,GAAG,cAAc,CAAC,CAAC,oBAAoB;IAC3C,OAAO,GAAwB;QACtC,QAAQ,EAAE;YACR,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,kEAAkE;YAC/E,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+BAA+B,CAAC;gBACjE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC;gBACtE,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;aACxF,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,+BAA+B,EAAE;oBAC7E,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,uCAAuC,EAAE;oBAClF,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,iDAAiD,EAAE;iBACzF;gBACD,QAAQ,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;aACtC;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;gBACrF,MAAM,WAAW,GAAG,KAAK,CAAC,WAAqB,CAAC;gBAChD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;gBAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;gBACxC,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gDAAgD,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;gBACnH,CAAC;gBACD,IAAI,GAAG,GAAG,WAAW,CAAC;gBACtB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrJ,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC;gBAC5E,CAAC;gBACD,IAAI,OAAO,GAAG,EAAE,CAAC;gBACjB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAC3B,OAAO,GAAG,8BAA8B,IAAI,EAAE,CAAC;gBACjD,CAAC;qBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBACjC,OAAO,GAAG,qBAAqB,IAAI,EAAE,CAAC;gBACxC,CAAC;qBAAM,CAAC;oBACN,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,QAAQ,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;gBACtG,CAAC;gBACD,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBAC3D,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;4BAChD,IAAI,GAAG,IAAI,CAAC,MAAM;gCAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;4BACzD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;wBAC5B,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;oBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;gBAChF,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;gBACjH,CAAC;YACH,CAAC;SACF;QACD,QAAQ,EAAE;YACR,GAAG,IAAI,sCAAmB,EAAE;YAC5B,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,MAAM,WAAW,GAAG,KAAK,CAAC,WAAqB,CAAC;gBAChD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;gBAC1C,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gDAAgD,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;gBACnH,CAAC;gBACD,6BAA6B;gBAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,yBAAyB,WAAW,UAAU,QAAQ,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACnI,CAAC;SACF;KACF,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,4FAA4F;QAC5F,MAAM,QAAQ,GAAG,UAAU,IAAI,KAAK,CAAC,MAAgB,IAAI,QAAQ,CAAC;QAClE,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,QAAQ,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAClK,CAAC;QACD,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;QACjC,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC5B,OAAO,WAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAED,gEAAgE;QAChE,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnC,mEAAmE;YACnE,OAAO,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,6DAA6D;YAC7D,MAAM,MAAM,GAAG,MAAO,UAAkB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACvE,6DAA6D;YAC7D,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACjC,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;YAC/B,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;CACF;AAnGD,4CAmGC;AAEY,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC", "sourcesContent": ["import { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport * as vscode from 'vscode';\nimport * as cp from 'child_process';\nimport { WebDeployStatusTool } from './advancedWebTools';\nimport { z } from 'zod';\n\nexport class DeployWebAppTool implements ITool {\n  readonly id = 'deployWebApp';\n  readonly name = 'Deploy Web App (Advanced)';\n  readonly description = 'Deploys web apps and manages deployment status with advanced options.';\n  readonly type = 'multi-action'; // Required by ITool\n  readonly actions: Record<string, any> = {\n    'deploy': {\n      id: 'deploy',\n      name: 'Deploy Web App',\n      description: 'Deploy a JavaScript web application using a deployment provider.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        projectPath: z.string().describe('Path to the project to deploy'),\n        provider: z.string().describe('Deployment provider (netlify, vercel)'),\n        args: z.string().optional().describe('Additional arguments for the deployment command')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          projectPath: { type: 'string', description: 'Path to the project to deploy' },\n          provider: { type: 'string', description: 'Deployment provider (netlify, vercel)' },\n          args: { type: 'string', description: 'Additional arguments for the deployment command' }\n        },\n        required: ['projectPath', 'provider']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n        const projectPath = input.projectPath as string;\n        const provider = input.provider as string;\n        const args = input.args as string || '';\n        if (!projectPath || !provider) {\n          return { success: false, error: '\\'projectPath\\' and \\'provider\\' are required.', toolId: 'deploy', actionName };\n        }\n        let cwd = projectPath;\n        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0 && !projectPath.match(/^.:\\\\/) && !projectPath.startsWith('/')) {\n          cwd = vscode.workspace.workspaceFolders[0].uri.fsPath + '/' + projectPath;\n        }\n        let command = '';\n        if (provider === 'netlify') {\n          command = `npx netlify deploy --dir . ${args}`;\n        } else if (provider === 'vercel') {\n          command = `npx vercel --prod ${args}`;\n        } else {\n          return { success: false, error: `Unsupported provider: ${provider}`, toolId: 'deploy', actionName };\n        }\n        try {\n          const result = await new Promise<string>((resolve, reject) => {\n            cp.exec(command, { cwd }, (err, stdout, stderr) => {\n              if (err && !stdout) return reject(stderr || err.message);\n              resolve(stdout || stderr);\n            });\n          });\n          return { success: true, output: result.trim(), toolId: 'deploy', actionName };\n        } catch (error: any) {\n          return { success: false, error: `Deployment failed: ${error.message || error}`, toolId: 'deploy', actionName };\n        }\n      }\n    },\n    'status': {\n      ...new WebDeployStatusTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        const projectPath = input.projectPath as string;\n        const provider = input.provider as string;\n        if (!projectPath || !provider) {\n          return { success: false, error: '\\'projectPath\\' and \\'provider\\' are required.', toolId: 'status', actionName };\n        }\n        // Placeholder implementation\n        return { success: true, output: `Deployment status for ${projectPath} using ${provider}: Active`, toolId: 'status', actionName };\n      }\n    },\n  };\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // For backward compatibility, check if actionName is undefined and try to get it from input\n    const actionId = actionName || input.action as string || 'deploy';\n    const actionTool = this.actions[actionId];\n    if (!actionTool) {\n      return { success: false, error: `Unknown deploy action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id, actionName };\n    }\n    const actionInput = { ...input };\n    if ('action' in actionInput) {\n      delete actionInput.action;\n    }\n\n    // Check if the action tool has the new execute method signature\n    if (actionTool.execute.length >= 2) {\n      // Pass the actionId as the actionName parameter to the nested tool\n      return actionTool.execute(actionId, actionInput, context);\n    } else {\n      // Fallback for older tools that don't have the new signature\n      const result = await (actionTool as any).execute(actionInput, context);\n      // Add the actionName to the result if it's not already there\n      if (result && !result.actionName) {\n        result.actionName = actionId;\n      }\n      return result;\n    }\n  }\n}\n\nexport const deployWebAppTool = new DeployWebAppTool();\n"]}