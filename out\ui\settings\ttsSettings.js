"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TTSSettingsManager = exports.ELEVENLABS = exports.OPENAI = exports.GOOGLE = exports.MICROSOFT = exports.EDGE = exports.SYSTEM = exports.TTSProvider = void 0;
exports.getAllProviderTypes = getAllProviderTypes;
const logger_1 = require("../../logger");
const systemTTS_1 = require("./systemTTS");
var TTSProvider;
(function (TTSProvider) {
    TTSProvider["SYSTEM"] = "system";
    TTSProvider["EDGE"] = "edge";
    TTSProvider["MICROSOFT"] = "microsoft";
    TTSProvider["GOOGLE"] = "google";
    TTSProvider["OPENAI"] = "openai";
    TTSProvider["ELEVENLABS"] = "elevenlabs";
})(TTSProvider || (exports.TTSProvider = TTSProvider = {}));
// Export the enum values for use in other files
exports.SYSTEM = TTSProvider.SYSTEM, exports.EDGE = TTSProvider.EDGE, exports.MICROSOFT = TTSProvider.MICROSOFT, exports.GOOGLE = TTSProvider.GOOGLE, exports.OPENAI = TTSProvider.OPENAI, exports.ELEVENLABS = TTSProvider.ELEVENLABS;
// Utility function to get all provider types
function getAllProviderTypes() {
    return [exports.SYSTEM, exports.EDGE, exports.MICROSOFT, exports.GOOGLE, exports.OPENAI, exports.ELEVENLABS];
}
class TTSSettingsManager {
    context;
    static instance;
    _settings;
    _voices;
    _disposables = [];
    constructor(context) {
        this.context = context;
        this._settings = this.loadSettings();
        this._voices = new Map();
        this.initializeVoices();
    }
    static getInstance(context) {
        console.log('Getting TTS settings manager instance for context:', context.extensionPath);
        if (!TTSSettingsManager.instance) {
            TTSSettingsManager.instance = new TTSSettingsManager(context);
        }
        return TTSSettingsManager.instance;
    }
    loadSettings() {
        return {
            enabled: this.context.globalState.get('ttsEnabled', false),
            provider: this.context.globalState.get('ttsProvider', TTSProvider.SYSTEM),
            voice: this.context.globalState.get('ttsVoice', ''),
            rate: this.context.globalState.get('ttsRate', 1.0),
            pitch: this.context.globalState.get('ttsPitch', 1.0),
            volume: this.context.globalState.get('ttsVolume', 1.0),
            autoSpeak: this.context.globalState.get('ttsAutoSpeak', true),
            downloadPath: this.context.globalState.get('ttsDownloadPath', ''),
            apiKeys: {
                google: this.context.globalState.get('ttsGoogleApiKey', ''),
                openai: this.context.globalState.get('ttsOpenAIApiKey', ''),
                elevenlabs: this.context.globalState.get('ttsElevenLabsApiKey', ''),
                azure: this.context.globalState.get('ttsAzureApiKey', '')
            },
            advanced: {
                ssmlEnabled: this.context.globalState.get('ttsSSMLEnabled', false),
                emotionControl: this.context.globalState.get('ttsEmotionControl', false),
                speedBoost: this.context.globalState.get('ttsSpeedBoost', false),
                qualityMode: this.context.globalState.get('ttsQualityMode', 'balanced'),
                cacheEnabled: this.context.globalState.get('ttsCacheEnabled', true),
                streamingEnabled: this.context.globalState.get('ttsStreamingEnabled', true)
            }
        };
    }
    async initializeVoices() {
        try {
            // Initialize System TTS voices (default)
            const systemVoices = await this.fetchSystemVoices();
            this._voices.set(TTSProvider.SYSTEM, systemVoices);
            // Initialize Edge TTS voices
            const edgeVoices = await this.fetchEdgeVoices();
            this._voices.set(TTSProvider.EDGE, edgeVoices);
            // Initialize Microsoft voices
            const microsoftVoices = await this.fetchMicrosoftVoices();
            this._voices.set(TTSProvider.MICROSOFT, microsoftVoices);
            // Initialize Google voices
            const googleVoices = await this.fetchGoogleVoices();
            this._voices.set(TTSProvider.GOOGLE, googleVoices);
            // Initialize OpenAI voices
            const openaiVoices = await this.fetchOpenAIVoices();
            this._voices.set(TTSProvider.OPENAI, openaiVoices);
            // Initialize ElevenLabs voices
            const elevenlabsVoices = await this.fetchElevenLabsVoices();
            this._voices.set(TTSProvider.ELEVENLABS, elevenlabsVoices);
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to initialize TTS voices:', error);
        }
    }
    async fetchSystemVoices() {
        // Use SystemTTS to get platform-specific voices
        return await systemTTS_1.SystemTTS.getVoices();
    }
    async fetchMicrosoftVoices() {
        // Use SystemTTS to get Windows voices
        return await systemTTS_1.SystemTTS.getVoices();
    }
    async fetchEdgeVoices() {
        // Use SystemTTS to get Edge/browser voices (macOS/Linux/Win)
        return await systemTTS_1.SystemTTS.getVoices();
    }
    async fetchGoogleVoices() {
        // Google Cloud TTS voices
        const settings = this.getSettings();
        if (!settings.apiKeys.google) {
            return [];
        }
        // Return common Google TTS voices
        return [
            { id: 'en-US-Standard-A', name: 'English (US) - Standard A', language: 'en-US', gender: 'female', provider: TTSProvider.GOOGLE },
            { id: 'en-US-Standard-B', name: 'English (US) - Standard B', language: 'en-US', gender: 'male', provider: TTSProvider.GOOGLE },
            { id: 'en-US-Wavenet-A', name: 'English (US) - WaveNet A', language: 'en-US', gender: 'female', provider: TTSProvider.GOOGLE },
            { id: 'en-US-Wavenet-B', name: 'English (US) - WaveNet B', language: 'en-US', gender: 'male', provider: TTSProvider.GOOGLE }
        ];
    }
    async fetchOpenAIVoices() {
        // OpenAI TTS voices
        const settings = this.getSettings();
        if (!settings.apiKeys.openai) {
            return [];
        }
        return [
            { id: 'alloy', name: 'Alloy', language: 'en-US', provider: TTSProvider.OPENAI },
            { id: 'echo', name: 'Echo', language: 'en-US', provider: TTSProvider.OPENAI },
            { id: 'fable', name: 'Fable', language: 'en-US', provider: TTSProvider.OPENAI },
            { id: 'onyx', name: 'Onyx', language: 'en-US', provider: TTSProvider.OPENAI },
            { id: 'nova', name: 'Nova', language: 'en-US', provider: TTSProvider.OPENAI },
            { id: 'shimmer', name: 'Shimmer', language: 'en-US', provider: TTSProvider.OPENAI }
        ];
    }
    async fetchElevenLabsVoices() {
        // ElevenLabs voices
        const settings = this.getSettings();
        if (!settings.apiKeys.elevenlabs) {
            return [];
        }
        // Return default ElevenLabs voices
        return [
            { id: 'rachel', name: 'Rachel', language: 'en-US', gender: 'female', provider: TTSProvider.ELEVENLABS },
            { id: 'domi', name: 'Domi', language: 'en-US', gender: 'female', provider: TTSProvider.ELEVENLABS },
            { id: 'bella', name: 'Bella', language: 'en-US', gender: 'female', provider: TTSProvider.ELEVENLABS },
            { id: 'antoni', name: 'Antoni', language: 'en-US', gender: 'male', provider: TTSProvider.ELEVENLABS },
            { id: 'elli', name: 'Elli', language: 'en-US', gender: 'female', provider: TTSProvider.ELEVENLABS },
            { id: 'josh', name: 'Josh', language: 'en-US', gender: 'male', provider: TTSProvider.ELEVENLABS }
        ];
    }
    getSettings() {
        return { ...this._settings };
    }
    async updateSettings(settings) {
        this._settings = { ...this._settings, ...settings };
        await this.saveSettings();
    }
    async saveSettings() {
        await this.context.globalState.update('ttsEnabled', this._settings.enabled);
        await this.context.globalState.update('ttsProvider', this._settings.provider);
        await this.context.globalState.update('ttsVoice', this._settings.voice);
        await this.context.globalState.update('ttsRate', this._settings.rate);
        await this.context.globalState.update('ttsPitch', this._settings.pitch);
        await this.context.globalState.update('ttsVolume', this._settings.volume);
        await this.context.globalState.update('ttsAutoSpeak', this._settings.autoSpeak);
        await this.context.globalState.update('ttsDownloadPath', this._settings.downloadPath);
        // Save API keys
        await this.context.globalState.update('ttsGoogleApiKey', this._settings.apiKeys.google);
        await this.context.globalState.update('ttsOpenAIApiKey', this._settings.apiKeys.openai);
        await this.context.globalState.update('ttsElevenLabsApiKey', this._settings.apiKeys.elevenlabs);
        await this.context.globalState.update('ttsAzureApiKey', this._settings.apiKeys.azure);
        // Save advanced settings
        await this.context.globalState.update('ttsSSMLEnabled', this._settings.advanced.ssmlEnabled);
        await this.context.globalState.update('ttsEmotionControl', this._settings.advanced.emotionControl);
        await this.context.globalState.update('ttsSpeedBoost', this._settings.advanced.speedBoost);
        await this.context.globalState.update('ttsQualityMode', this._settings.advanced.qualityMode);
        await this.context.globalState.update('ttsCacheEnabled', this._settings.advanced.cacheEnabled);
        await this.context.globalState.update('ttsStreamingEnabled', this._settings.advanced.streamingEnabled);
    }
    getVoices(provider) {
        return this._voices.get(provider) || [];
    }
    updateVoices(provider, voices) {
        this._voices.set(provider, voices);
    }
    async downloadVoice(_voice) {
        // Implement voice downloading logic
        logger_1.Logger.instance.info('Voice download functionality not yet implemented');
    }
    dispose() {
        this._disposables.forEach(d => d.dispose());
    }
}
exports.TTSSettingsManager = TTSSettingsManager;
//# sourceMappingURL=ttsSettings.js.map