interface CacheEntry<T> {
  value: T;
  expiresAt: number;
  metadata?: {
    hits: number;
    lastAccessed: number;
    size?: number;
  };
}

export class Cache<T = any> {
  private cache: Map<string, CacheEntry<T>> = new Map();
  private maxSize: number;
  private defaultTTL: number; // in milliseconds

  constructor(maxSize: number = 1000, defaultTTL: number = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
  }

  /**
   * Set a value in the cache
   */
  public set(
    key: string, 
    value: T, 
    ttl: number = this.defaultTTL
  ): void {
    // If cache is full, remove the least recently used items
    if (this.cache.size >= this.maxSize) {
      this.evictLRU(this.maxSize * 0.1); // Evict 10% of the cache
    }

    this.cache.set(key, {
      value,
      expiresAt: Date.now() + ttl,
      metadata: {
        hits: 0,
        lastAccessed: Date.now(),
      },
    });
  }

  /**
   * Get a value from the cache
   */
  public get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check if entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    // Update metadata
    if (entry.metadata) {
      entry.metadata.hits++;
      entry.metadata.lastAccessed = Date.now();
    }
    
    return entry.value;
  }

  /**
   * Delete a value from the cache
   */
  public delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear the entire cache
   */
  public clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  public getStats() {
    let hits = 0;
    let size = 0;
    
    this.cache.forEach(entry => {
      if (entry.metadata) {
        hits += entry.metadata.hits;
      }
      size++;
    });
    
    return {
      size: this.cache.size,
      hits,
    };
  }

  /**
   * Evict least recently used items
   */
  private evictLRU(count: number): void {
    const entries = Array.from(this.cache.entries())
      .map(([key, entry]) => ({
        key,
        lastAccessed: entry.metadata?.lastAccessed || 0,
      }))
      .sort((a, b) => a.lastAccessed - b.lastAccessed);
    
    // Remove the least recently used items
    entries.slice(0, Math.min(count, entries.length)).forEach(({ key }) => {
      this.cache.delete(key);
    });
  }

  /**
   * Clean up expired entries
   */
  public cleanup(): number {
    let count = 0;
    const now = Date.now();
    
    this.cache.forEach((value, key) => {
      if (now > value.expiresAt) {
        this.cache.delete(key);
        count++;
      }
    });
    
    return count;
  }
}

// Global cache instance
export const globalCache = new Cache<any>();

// Cache decorator for methods
export function cacheResult(
  ttl: number = 5 * 60 * 1000, // 5 minutes default
  keyFn?: (...args: any[]) => string
) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const cache = new Cache(1000, ttl);
    
    descriptor.value = function(...args: any[]) {
      const cacheKey = keyFn 
        ? keyFn.apply(this, args)
        : `${propertyKey}:${JSON.stringify(args)}`;
      
      const cached = cache.get(cacheKey);
      if (cached !== null) {
        return cached;
      }
      
      const result = originalMethod.apply(this, args);
      
      // Handle both sync and async functions
      if (result instanceof Promise) {
        return result.then(res => {
          cache.set(cacheKey, res);
          return res;
        });
      } else {
        cache.set(cacheKey, result);
        return result;
      }
    };
    
    return descriptor;
  };
}
