import { EventEmitter } from 'events';

export interface WorkflowStepResult {
  success: boolean;
  output?: any;
  error?: Error | string;
  nextStepId?: string | null;
  metadata?: Record<string, any>;
}

export interface WorkflowHistoryEntry {
  stepId: string;
  startTime: Date;
  endTime: Date;
  result: WorkflowStepResult;
  quantumAnalysis?: any;
  neuralInsights?: any;
  goddessGuidance?: any;
}

export interface WorkflowContext {
  history: WorkflowHistoryEntry[];
  data: Record<string, any>;
  startTime: Date;
  endTime: Date | null;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  metadata: Record<string, any>;
  emitter?: EventEmitter;
  
  // Add any additional context properties needed by your workflow steps
  [key: string]: any;
}

export interface WorkflowStepOptions {
  id: string;
  name: string;
  description?: string;
  execute: (context: WorkflowContext) => Promise<WorkflowStepResult>;
  retryCount?: number;
  retryDelay?: number;
  timeout?: number;
  parallel?: boolean;
  condition?: (context: WorkflowContext) => boolean | Promise<boolean>;
  onSuccess?: (context: WorkflowContext, result: WorkflowStepResult) => void | Promise<void>;
  onError?: (context: WorkflowContext, error: Error) => void | Promise<void>;
  onComplete?: (context: WorkflowContext, result: WorkflowStepResult) => void | Promise<void>;
}
