"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.agentManager = exports.AgentManager = void 0;
const vscode = __importStar(require("vscode"));
const agent_1 = require("./agent");
const logger_1 = require("../../logger");
class AgentManager {
    static _instance;
    _agents = new Map();
    _onAgentsChanged = new vscode.EventEmitter();
    _receiverAgent;
    _supervisorAgent;
    /**
       * Event that fires when agents are changed
       */
    onAgentsChanged = this._onAgentsChanged.event;
    constructor() {
        // Initialize with default agent
        this.addAgent(new agent_1.Agent({
            id: 'default',
            name: 'Default Agent',
            systemPromptName: 'default',
            role: 'assistant',
            capabilities: ['chat'],
            llmProvider: 'openai',
            llmModel: 'gpt-4'
        }));
    }
    /**
       * Get the singleton instance of AgentManager
       */
    static getInstance() {
        if (!AgentManager._instance) {
            AgentManager._instance = new AgentManager();
        }
        return AgentManager._instance;
    }
    /**
       * Get all agents
       */
    getAllAgents() {
        return Array.from(this._agents.values());
    }
    /**
       * Get all agent IDs
       */
    getAgentIds() {
        return Array.from(this._agents.keys());
    }
    /**
       * Get an agent by ID
       */
    getAgent(id) {
        return this._agents.get(id);
    }
    /**
       * Get the default agent
       */
    getDefaultAgent() {
        // First try to find an agent with ID 'default'
        let defaultAgent = this._agents.get('default');
        // If not found, try to find an agent with name 'default' (case insensitive)
        if (!defaultAgent) {
            defaultAgent = Array.from(this._agents.values()).find(agent => agent.name.toLowerCase() === 'default');
        }
        // If still not found, return the first agent
        if (!defaultAgent && this._agents.size > 0) {
            defaultAgent = Array.from(this._agents.values())[0];
        }
        return defaultAgent;
    }
    /**
       * Add a new agent
       */
    addAgent(agent) {
        this._agents.set(agent.id, agent);
        logger_1.logger.info(`Added agent ${agent.id}`);
        this._onAgentsChanged.fire({ type: 'add', agentId: agent.id });
        // Set receiver and supervisor agents if they match the roles
        if (agent.role === 'receiver') {
            this._receiverAgent = agent;
        }
        else if (agent.role === 'supervisor') {
            this._supervisorAgent = agent;
        }
    }
    /**
       * Create a new agent from config
       */
    createAgent(_config) {
        const agent = new agent_1.Agent({
            ..._config,
            role: 'assistant',
            capabilities: ['chat'],
            llmProvider: 'openai',
            llmModel: 'gpt-4'
        });
        this.addAgent(agent);
        return agent;
    }
    /**
       * Update an existing agent
       */
    updateAgent(id, updatedAgent) {
        if (this._agents.has(id)) {
            this._agents.set(id, updatedAgent);
            logger_1.logger.info(`Updated agent ${id}`);
            this._onAgentsChanged.fire({ type: 'update', agentId: id });
            return true;
        }
        return false;
    }
    /**
       * Delete an agent by ID
       */
    deleteAgent(id) {
        const result = this._agents.delete(id);
        if (result) {
            logger_1.logger.info(`Deleted agent ${id}`);
            this._onAgentsChanged.fire({ type: 'delete', agentId: id });
            // Clear receiver or supervisor if this agent was one
            if (this._receiverAgent === this._agents.get(id)) {
                this._receiverAgent = undefined;
            }
            if (this._supervisorAgent === this._agents.get(id)) {
                this._supervisorAgent = undefined;
            }
        }
        return result;
    }
    getReceiverAgent() {
        return this._receiverAgent;
    }
    setReceiverAgent(agent) {
        this._receiverAgent = agent;
        this.addAgent(agent);
        logger_1.logger.info(`Receiver agent set: ${agent.name}`);
    }
    getSupervisorAgent() {
        return this._supervisorAgent;
    }
    setSupervisorAgent(agent) {
        this._supervisorAgent = agent;
        this.addAgent(agent);
        logger_1.logger.info(`Supervisor agent set: ${agent.name}`);
    }
    getAdditionalAgents() {
        return this.getAllAgents().filter(agent => agent !== this._receiverAgent &&
            agent !== this._supervisorAgent);
    }
    dispose() {
        this._agents.clear();
        this._receiverAgent = undefined;
        this._supervisorAgent = undefined;
    }
}
exports.AgentManager = AgentManager;
// Export a singleton instance
exports.agentManager = AgentManager.getInstance();
//# sourceMappingURL=agentManager.js.map