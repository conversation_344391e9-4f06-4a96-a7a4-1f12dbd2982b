{"version": 3, "file": "cohereProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/cohereProvider.ts"], "names": [], "mappings": ";;;AACA,uDAAoD;AAGpD,yDAAyD;AACzD,yCAAsC;AAEtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;GAEG;AACH,MAAa,cAAe,SAAQ,iCAAe;IACxC,UAAU,GAAG,QAAQ,CAAC;IACtB,WAAW,GAAG,QAAQ,CAAC;IACvB,WAAW,GAAG,yBAAyB,CAAC;IACxC,OAAO,GAAG,mBAAmB,CAAC;IAC9B,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,KAAK,CAAC;IACtC,eAAe,GAAG,0BAA0B,CAAC;IAC7C,YAAY,GAAG,SAAS,CAAC;IAE1B,MAAM,GAAQ,IAAI,CAAC;IACnB,OAAO,CAAS;IAExB,YAAY,OAAgC;QAC1C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACG,gBAAgB;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAC/C,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,YAAY;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC/C,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,QAAQ,CACnB,MAAyB;QAEzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;QACjE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,2BAA2B;YAC3B,MAAM,QAAQ,GAA6C,EAAE,CAAC;YAE9D,mCAAmC;YACnC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,OAAO,CAAC,OAAO;yBAChB,CAAC,CAAC;oBACd,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,SAAS;4BACf,OAAO,EAAE,OAAO,CAAC,OAAO;yBAChB,CAAC,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;gBAC/C,KAAK,EAAE,OAAO;gBACd,OAAO,EAAE,MAAM,CAAC,MAAM;gBACtB,YAAY,EAAE,QAAQ;gBACtB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACpC,QAAQ,EAAE,MAAM,CAAC,YAAY,IAAI,SAAS;aAC3C,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YAEzC,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,IAAI,MAAM;gBACnD,KAAK,EAAE;oBACL,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,IAAI,CAAC;oBACpD,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,IAAI,CAAC;oBAC1D,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,IAAI,CAAC,CAAC;iBACnG;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC9F,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAClE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,iFAAiF;QACjF,MAAM,MAAM,GAAG;YACb;gBACE,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,wCAAwC;gBACrD,aAAa,EAAE,IAAI;aACpB;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,qDAAqD;gBAClE,aAAa,EAAE,IAAI;aACpB;YACD;gBACE,EAAE,EAAE,WAAW;gBACf,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,4CAA4C;gBACzD,aAAa,EAAE,MAAM;aACtB;YACD;gBACE,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,qDAAqD;gBAClE,aAAa,EAAE,MAAM;aACtB;SACF,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,uBAAuB,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;QACrE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2DAA2D;aACrE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YACvE,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;gBAC9B,KAAK,EAAE,KAAK;gBACZ,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,yDAAyD,KAAK,GAAG;aAC3E,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aACxG,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,OAAY;QACpC,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,qBAAqB;gBAClC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,qDAAqD;gBAClE,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AA3ND,wCA2NC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Base<PERSON><PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\n// ITool import removed as it's not used in this provider\nimport { logger } from '../../logger';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for Cohere API\n */\nexport class CohereProvider extends BaseLLMProvider {\n  readonly providerId = 'cohere';\n  readonly displayName = 'Cohere';\n  readonly description = 'Access Cohere AI models';\n  readonly website = 'https://cohere.ai';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = false;\n  readonly defaultEndpoint = 'https://api.cohere.ai/v1';\n  readonly defaultModel = 'command';\n\n  private client: any = null;\n  private baseUrl: string;\n\n  constructor(context: vscode.ExtensionContext) {\n    super(context);\n    this.baseUrl = this.defaultEndpoint;\n    this.initializeClient();\n  }\n\n  /**\n     * Initialize the Axios client for API requests\n     */\n  private initializeClient(): void {\n    try {\n      if (!this.config.apiKey) {\n        logger.warn('Cohere API key not configured');\n        this.client = null;\n        return;\n      }\n\n      this.client = axios.create({\n        baseURL: this.baseUrl,\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      logger.info('Cohere client initialized');\n    } catch (error) {\n      logger.error('Failed to initialize Cohere client:', error);\n      this.client = null;\n    }\n  }\n\n  /**\n     * Check if the provider is configured\n     */\n  public isConfigured(): boolean {\n    return !!this.client && !!this.config.apiKey;\n  }\n\n  /**\n     * Generate text using Cohere\n     */\n  public async generate(\n    params: LLMGenerateParams\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'Cohere client not initialized' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare the chat history\n      const messages: Array<{ role: string; message: string }> = [];\n            \n      // Add history messages if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            messages.push({\n              role: 'USER',\n              message: message.content\n            } as const);\n          } else if (message.role === 'assistant') {\n            messages.push({\n              role: 'CHATBOT',\n              message: message.content\n            } as const);\n          }\n        }\n      }\n\n      // Make the API request\n      const response = await this.client.post('/chat', {\n        model: modelId,\n        message: params.prompt,\n        chat_history: messages,\n        temperature: params.temperature || 0.7,\n        max_tokens: params.maxTokens || 1024,\n        preamble: params.systemPrompt || undefined\n      });\n\n      // Extract the response content\n      const content = response.data.text || '';\n\n      return {\n        content,\n        finishReason: response.data.finish_reason || 'stop',\n        usage: {\n          promptTokens: response.data.meta?.prompt_tokens || 0,\n          completionTokens: response.data.meta?.response_tokens || 0,\n          totalTokens: (response.data.meta?.prompt_tokens || 0) + (response.data.meta?.response_tokens || 0)\n        }\n      };\n    } catch (error) {\n      logger.error('Error generating text with Cohere:', error);\n      return {\n        content: '',\n        error: `Cohere generation error: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * List available models from Cohere\n     */\n  public async listModels(): Promise<LLMModelInfo[]> {\n    if (!this.client) {\n      logger.warn('Cannot fetch Cohere models, client not configured.');\n      return [];\n    }\n\n    // Cohere doesn't have a public API to list models, so we'll return a static list\n    const models = [\n      {\n        id: 'command',\n        name: 'Command',\n        description: 'Cohere Command model - general purpose',\n        contextWindow: 4096\n      },\n      {\n        id: 'command-light',\n        name: 'Command Light',\n        description: 'Cohere Command Light model - faster, more efficient',\n        contextWindow: 4096\n      },\n      {\n        id: 'command-r',\n        name: 'Command-R',\n        description: 'Cohere Command-R model - latest generation',\n        contextWindow: 128000\n      },\n      {\n        id: 'command-r-plus',\n        name: 'Command-R Plus',\n        description: 'Cohere Command-R Plus model - enhanced capabilities',\n        contextWindow: 128000\n      }\n    ];\n\n    logger.info(`Provider cohere has ${models.length} models available`);\n    return models;\n  }\n\n  /**\n     * Test connection to Cohere\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'Cohere client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Try a simple chat request\n      const model = modelId || this.config.defaultModel || this.defaultModel;\n      await this.client.post('/chat', {\n        model: model,\n        message: 'Hello',\n        max_tokens: 5\n      });\n\n      return {\n        success: true,\n        message: `Successfully connected to Cohere API and tested model ${model}.`\n      };\n    } catch (error) {\n      logger.error('Cohere connection test failed:', error);\n      return {\n        success: false,\n        message: `Failed to connect to Cohere API: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * Update the provider configuration\n     */\n  public async updateConfig(_config: any): Promise<void> {\n    await super.updateConfig(_config);\n    this.initializeClient();\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your Cohere API key',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use (e.g., command, command-r)',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n\n\n"]}