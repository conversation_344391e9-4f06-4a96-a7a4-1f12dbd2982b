:root {
    --font-size: 13.5px;
    --line-height: 1.6;
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 12px;
    --space-lg: 16px;
    --space-xl: 24px;
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --transition-fast: 0.15s ease-out;
    --transition-std: 0.25s ease-out;

    /* --- Core Colors (Derived from VSCode Theme with Fallbacks) --- */
    --bg-primary: var(--vscode-sideBar-background, #ffffff);
    --bg-secondary: var(--vscode-editor-background, #f3f3f3);
    --bg-tertiary: var(--vscode-input-background, #ffffff);
    --text-primary: var(--vscode-editor-foreground, #1f1f1f);
    --text-secondary: var(--vscode-descriptionForeground, #717171);
    --text-link: var(--vscode-textLink-foreground, #007acc);
    --text-link-active: var(--vscode-textLink-activeForeground, #005a9e);
    --text-error: var(--vscode-errorForeground, #d10000);
    --accent-primary: var(--vscode-button-background, #007acc);
    --accent-primary-rgb: 0, 122, 204; /* RGB values for accent-primary */
    --accent-primary-hover: var(--vscode-button-hoverBackground, #005a9e);
    --accent-primary-text: var(--vscode-button-foreground, #ffffff);
    --accent-secondary: var(--vscode-button-secondaryBackground, #e0e0e0);
    --accent-secondary-hover: var(--vscode-button-secondaryHoverBackground, #cccccc);
    --accent-secondary-text: var(--vscode-button-secondaryForeground, #1f1f1f);
    --border-color: var(--vscode-panel-border, #d1d1d1);
    --border-color-subtle: color-mix(in srgb, var(--border-color) 50%, transparent);
    --focus-border: var(--vscode-focusBorder, #007acc);
    --code-bg: var(--vscode-textCodeBlock-background, #f5f5f5);
    --error-bg: var(--vscode-inputValidation-errorBackground, rgba(209, 0, 0, 0.1));

    /* --- Message Specific Colors --- */
    --message-bg-assistant: var(--vscode-editorWidget-background, #e8e8e8);
    --message-text-assistant: var(--vscode-editorWidget-foreground, #222222);
    --message-bg-user: var(--accent-primary);
    --message-text-user: var(--accent-primary-text);

    /* --- Shadows --- */
    --shadow-color-base: rgba(0, 0, 0, 0.08);
    --shadow-sm: 0 1px 2px 0 var(--shadow-color-base);
    --shadow-md: 0 3px 6px -1px var(--shadow-color-base), 0 2px 4px -1px var(--shadow-color-base);
    --shadow-lg: 0 10px 15px -3px var(--shadow-color-base), 0 4px 6px -2px var(--shadow-color-base);
}

body.vscode-dark {
    --bg-primary: var(--vscode-sideBar-background, #252526);
    --bg-secondary: var(--vscode-editor-background, #1e1e1e);
    --bg-tertiary: var(--vscode-input-background, #3c3c3c);
    --text-primary: var(--vscode-editor-foreground, #cccccc);
    --text-secondary: var(--vscode-descriptionForeground, #9b9b9b);
    --text-link: var(--vscode-textLink-foreground, #3794ff);
    --text-link-active: var(--vscode-textLink-activeForeground, #69b4ff);
    --text-error: var(--vscode-errorForeground, #f48771);
    --accent-primary: var(--vscode-button-background, #0e639c);
    --accent-primary-rgb: 14, 99, 156; /* RGB values for accent-primary in dark mode */
    --accent-primary-hover: var(--vscode-button-hoverBackground, #1177bb);
    --accent-primary-text: var(--vscode-button-foreground, #ffffff);
    --accent-secondary: var(--vscode-button-secondaryBackground, #3a3d41);
    --accent-secondary-hover: var(--vscode-button-secondaryHoverBackground, #4a4d51);
    --accent-secondary-text: var(--vscode-button-secondaryForeground, #cccccc);
    --border-color: var(--vscode-panel-border, #414141);
    --border-color-subtle: color-mix(in srgb, var(--border-color) 50%, transparent);
    --focus-border: var(--vscode-focusBorder, #007fd4);
    --code-bg: var(--vscode-textCodeBlock-background, #2d2d2d);
    --error-bg: var(--vscode-inputValidation-errorBackground, rgba(244, 135, 113, 0.15));

    --message-bg-assistant: var(--vscode-editorWidget-background, #2c2c2d);
    --message-text-assistant: var(--vscode-editorWidget-foreground, #d4d4d4);
    --message-bg-user: var(--accent-primary);
    --message-text-user: var(--accent-primary-text);

    --shadow-color-base: rgba(0, 0, 0, 0.25);
}

html, body {
    height: 100%; margin: 0; padding: 0;
    font-family: var(--vscode-font-family, sans-serif);
    font-size: var(--font-size); line-height: var(--line-height);
    background-color: var(--bg-primary); color: var(--text-primary);
    overflow: hidden;
}
* { box-sizing: border-box; }

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
}

/* Header */
.chat-header {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: var(--vscode-titleBar-activeBackground);
    border-bottom: 1px solid var(--vscode-panel-border);
}

.chat-header .logo {
    width: 32px;
    height: 32px;
    margin-right: 10px;
}

.chat-header h1 {
    margin: 0;
    font-size: 1.2em;
    flex-grow: 1;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.provider-select,
.model-select {
    padding: 4px 8px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 2px;
    font-size: 12px;
    min-width: 120px;
}

.provider-select:disabled,
.model-select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.provider-select option:disabled {
    color: var(--vscode-disabledForeground);
}

/* Messages Area */
.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message {
    max-width: 80%;
    padding: 12px;
    border-radius: 8px;
    position: relative;
}

.message.user {
    align-self: flex-end;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.message.assistant {
    align-self: flex-start;
    background-color: var(--vscode-editor-inactiveSelectionBackground);
}

.message.error {
    align-self: center;
    background-color: var(--vscode-errorForeground);
    color: var(--vscode-editor-background);
}

.message-content {
    margin-bottom: 4px;
    white-space: pre-wrap;
    word-break: break-word;
}

.message-timestamp {
    font-size: 0.8em;
    opacity: 0.7;
    text-align: right;
}

/* Input Area */
.chat-input-container {
    padding: 10px;
    background-color: var(--vscode-editor-background);
    border-top: 1px solid var(--vscode-panel-border);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-actions {
    display: flex;
    gap: 8px;
    padding: 8px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.icon-button {
    background: none;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.icon-button:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
}

.icon-button.active {
    color: var(--vscode-button-background);
}

.icon-button.recording {
    color: var(--vscode-errorForeground);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.icon-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.icon-button i.codicon {
    font-family: codicon;
    font-size: 16px;
    line-height: 16px;
    display: inline-block;
    vertical-align: middle;
}

.icon-button i.codicon:before {
    font-family: codicon;
    font-style: normal;
    font-weight: normal;
    display: inline-block;
    text-align: center;
    text-decoration: none;
    vertical-align: text-bottom;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Map codicon class names to their specific content */
.codicon-settings-gear:before { content: '\ea6f'; }
.codicon-clear-all:before { content: '\ea76'; }
.codicon-export:before { content: '\ea42'; }
.codicon-file:before { content: '\ea7b'; }
.codicon-folder:before { content: '\ea83'; }
.codicon-image:before { content: '\ea7c'; }
.codicon-mic:before { content: '\ea8c'; }
.codicon-unmute:before { content: '\eaa0'; }
.codicon-settings:before { content: '\eb52'; }

#chat-input {
    width: 100%;
    height: 60px; /* Start with minimum height */
    min-height: 60px;
    max-height: 200px; /* Maximum height before scrolling */
    padding: 8px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 4px;
    resize: none;
    font-family: var(--vscode-font-family);
    overflow-y: hidden; /* Start with no scrollbar */
    line-height: 1.4; /* Consistent line height for calculations */
    box-sizing: border-box; /* Include padding in height calculations */
    vertical-align: top; /* Prevent baseline alignment issues */
    display: block; /* Ensure proper block display */
}

#chat-input:focus {
    outline: 1px solid var(--vscode-focusBorder);
}

.send-button {
    align-self: flex-end;
    padding: 8px 16px;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.send-button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-hoverBackground);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-activeBackground);
}

/* --- Header & Top Toolbar --- */
.global-toolbar {
    display: flex; align-items: center; flex-wrap: wrap; justify-content: space-between;
    gap: var(--space-sm) var(--space-md);
    padding: var(--space-sm) var(--space-lg); border-bottom: 1px solid var(--border-color-subtle);
    background-color: var(--vscode-sideBar-background);
    flex-shrink: 0; z-index: 9;
}
.toolbar-group-left {
    display: flex; align-items: center; flex-wrap: wrap;
    gap: var(--space-sm) var(--space-md);
}
.toolbar-group-right {
    display: flex; align-items: center; flex-wrap: wrap;
    gap: var(--space-xs);
}

/* --- Dropdowns (Top Toolbar) --- */
.dropdown-container {
    position: relative; display: inline-flex; align-items: center;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: 0 var(--space-sm);
    min-width: 120px;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
    margin-right: var(--space-sm);
    height: 28px;
}

.dropdown-container:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 1px var(--accent-primary-hover);
}

.dropdown-container select {
    flex-grow: 1;
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    border: none;
    padding: var(--space-xs) var(--space-md) var(--space-xs) var(--space-xs);
    font-size: 0.9em;
    color: var(--text-primary);
    cursor: pointer;
    outline: none;
    width: 100%;
    min-width: 0;
    line-height: 1.5;
}

.dropdown-container::after {
    content: '\ea43';
    font-family: 'codicon';
    font-size: 14px;
    color: var(--text-secondary);
    position: absolute;
    right: var(--space-sm);
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

.dropdown-container:focus-within {
    border-color: var(--focus-border);
    box-shadow: 0 0 0 1px var(--focus-border);
}

/* Make dropdown options more visible */
.dropdown-container select option {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: var(--space-sm);
}

/* Chat Head Component */
.chat-head {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-md);
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0;
}

.chat-head-circle {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--accent-primary);
    background-color: transparent;
    flex-shrink: 0;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    z-index: 2;
}

.chat-head-circle:hover {
    background-color: rgba(var(--accent-primary-rgb), 0.1);
}

.chat-head-circle.active {
    background-color: var(--accent-primary);
}

.chat-head-line {
    height: 1px;
    background-color: var(--accent-primary);
    opacity: 0.5;
    flex-basis: 0;
    flex-grow: 1;
    margin: 0 var(--space-md);
}

/* Ensure equal width for both lines */
.chat-head-line:first-child,
.chat-head-line:last-child {
    flex-grow: 1;
    flex-basis: 0;
    min-width: 0;
}

.chat-head-username {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1em;
    padding: 0 var(--space-sm);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 50%;
    text-align: center;
}

/* Message Headers */
.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-xs) var(--space-md);
    border-bottom: 1px solid rgba(0,0,0,0.1);
    margin-bottom: var(--space-sm);
    font-size: 0.9em;
}

.message-context {
    display: inline-block;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.85em;
    font-family: var(--vscode-editor-font-family);
    margin-bottom: var(--space-sm);
}

.message.user .message-header {
    color: var(--text-secondary);
    border-bottom: 1px solid rgba(0, 122, 204, 0.15);
}
.message.user .message-context {
    background-color: rgba(0, 122, 204, 0.15);
    color: var(--text-primary);
}

.message.assistant .message-header {
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color-subtle);
}
.message.assistant .message-context {
    background-color: rgba(255, 180, 0, 0.15);
    color: var(--text-primary);
}

.message.system { align-self: center; max-width: 90%; text-align: center; font-style: italic; color: var(--text-secondary); opacity: 0.8; animation: none; }
.message.system .message-bubble { background: none; padding: var(--space-xs) var(--space-md); border: none; box-shadow: none; }
.message.system .message-bubble:hover { transform: none; box-shadow: none; }
.message.system .message-header { display: none; }

.message.error .message-header { color: var(--text-error); border-bottom: 1px solid var(--text-error); }

.timestamp { font-size: 0.8em; color: var(--text-secondary); margin-top: var(--space-xs); text-align: right; opacity: 0.6; }
.message.user .timestamp { text-align: right; color: var(--text-secondary); }
.message.assistant .timestamp { text-align: right; }
.message.system .timestamp, .message.error .timestamp { text-align: center; width: 100%; }

.message-id {
    font-size: 0.8em;
    color: var(--text-secondary);
    opacity: 0.7;
}

.message-username {
    font-weight: 500;
}

/* --- Markdown Content --- */
.message-content > *:first-child { margin-top: 0; } .message-content > *:last-child { margin-bottom: 0; }
.message-content h1, .message-content h2, .message-content h3, .message-content h4, .message-content h5, .message-content h6 { margin: var(--space-md) 0 var(--space-sm) 0; font-weight: 600; line-height: 1.3; }
.message-content h1 { font-size: 1.4em; } .message-content h2 { font-size: 1.3em; } .message-content h3 { font-size: 1.2em; }
.message-content p { margin: var(--space-sm) 0; }
.message-content ul, .message-content ol { margin: var(--space-sm) 0; padding-left: var(--space-lg); }
.message-content li { margin-bottom: var(--space-xs); }
.message-content a { color: var(--text-link); text-decoration: none; font-weight: 500; transition: all var(--transition-fast); border-bottom: 1px solid transparent; }
.message-content a:hover { color: var(--text-link-active); text-decoration: none; border-bottom: 1px solid var(--text-link-active); }
.message-content blockquote { margin: var(--space-sm) 0; padding: var(--space-xs) var(--space-md); border-left: 4px solid var(--accent-primary); color: var(--text-secondary); opacity: 0.9; background-color: var(--vscode-textBlockQuote-background); border-radius: 0 var(--radius-sm) var(--radius-sm) 0; }
.message-content hr { border: none; border-top: 1px solid var(--border-color); margin: var(--space-md) 0; }
.message-content table { border-collapse: collapse; margin: var(--space-md) 0; width: 100%; font-size: 0.95em; border: 1px solid var(--border-color); border-radius: var(--radius-md); overflow: hidden; box-shadow: var(--shadow-sm); }
.message-content th, .message-content td { border: 1px solid var(--border-color-subtle); padding: var(--space-sm) var(--space-md); text-align: left; }
.message-content th { background-color: var(--vscode-keybindingTable-headerBackground); font-weight: 600; }
.message-content tr:nth-child(even) td { background-color: var(--vscode-keybindingTable-rowsBackground); }

/* --- Code Blocks --- */
.message-content pre { margin: var(--space-md) 0; position: relative; background-color: var(--code-bg); border-radius: var(--radius-md); overflow: hidden; border: 1px solid var(--border-color); box-shadow: inset 0 1px 3px rgba(0,0,0,0.1); }
.code-block-header { display: flex; justify-content: space-between; align-items: center; padding: var(--space-xs) var(--space-md); background-color: var(--vscode-textCodeBlock-background); font-size: 0.85em; color: var(--text-secondary); border-bottom: 1px solid var(--border-color); }
body.vscode-dark .code-block-header { background-color: var(--vscode-textCodeBlock-background); }
.code-block-language { text-transform: lowercase; font-weight: 500; opacity: 0.8; }
.copy-code-button { background: none; border: none; color: var(--text-secondary); cursor: pointer; padding: 2px 4px; border-radius: var(--radius-sm); font-size: 14px; display: inline-flex; align-items: center; gap: 4px; transition: all var(--transition-fast); opacity: 0.7; }
.copy-code-button:hover { background-color: var(--accent-secondary-hover); color: var(--accent-secondary-text); opacity: 1; transform: scale(1.05); }
.copy-code-button .codicon { font-size: 14px; } .copy-code-button .copy-status { font-size: 0.9em; }
.message-content pre code { display: block; padding: var(--space-md) var(--space-lg); overflow-x: auto; font-family: var(--vscode-editor-font-family); font-size: var(--vscode-editor-font-size); line-height: var(--vscode-editor-line-height, 1.5); color: var(--text-primary); background: none; }
.message-content :not(pre) > code { font-family: var(--vscode-editor-font-family); font-size: 0.9em; background-color: var(--code-bg); padding: 0.15em 0.4em; border-radius: var(--radius-sm); border: 1px solid var(--border-color-subtle); color: var(--text-primary); opacity: 0.9; }
.hljs { background: transparent !important; }

/* --- Input Area --- */
.chat-input-area {
    padding: var(--space-md) var(--space-lg); border-top: 1px solid var(--border-color);
    background-color: var(--bg-primary); flex-shrink: 0; position: relative;
    box-shadow: 0 -3px 8px rgba(0,0,0,0.05); z-index: 5;
    overflow: hidden; /* Ensure the background logo doesn't overflow */
}
body.vscode-dark .chat-input-area { box-shadow: 0 -3px 8px rgba(0,0,0,0.15); }

/* Background Logo with Water Effect */
.background-logo-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none; /* Allow clicks to pass through */
    z-index: 0; /* Place behind other elements */
    overflow: hidden;
}

.chat-messages .background-logo-container {
    position: absolute; /* Absolute position in the chat area */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    z-index: 0;
}

.background-logo {
    width: 250px;
    height: 250px;
    opacity: 0.04; /* Very faint */
    filter: blur(1px);
    transform-origin: center;
    animation: waterFloat 8s ease-in-out infinite, waterWobble 6s ease-in-out infinite;
    position: relative;
    margin-top: 50px; /* Push it down a bit */
}

/* Water ripple effect */
.background-logo::before,
.background-logo::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: transparent;
    border: 2px solid rgba(var(--accent-primary-rgb, 0, 122, 204), 0.1);
    transform: translate(-50%, -50%);
    animation: waterRipple 10s linear infinite;
}

.background-logo::after {
    animation-delay: -5s;
    width: 80%;
    height: 80%;
}

@keyframes waterRipple {
    0% {
        width: 0%;
        height: 0%;
        opacity: 0.5;
    }
    100% {
        width: 200%;
        height: 200%;
        opacity: 0;
    }
}

/* Water floating animation */
@keyframes waterFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Water wobble animation */
@keyframes waterWobble {
    0%, 100% { transform: rotate(-2deg) scale(1); }
    25% { transform: rotate(0deg) scale(1.02); }
    50% { transform: rotate(2deg) scale(1); }
    75% { transform: rotate(0deg) scale(0.98); }
}

body.vscode-dark .background-logo {
    opacity: 0.03; /* Even more faint in dark mode */
    filter: blur(1.5px) brightness(1.5);
}

.typing-indicator {
    position: absolute; bottom: 100%;
    left: var(--space-lg); display: flex; align-items: center;
    gap: var(--space-sm); font-size: 0.9em; color: var(--text-secondary); padding: var(--space-xs) var(--space-sm);
    background-color: var(--bg-primary); border: 1px solid var(--border-color); border-bottom: none;
    border-radius: var(--radius-sm) var(--radius-sm) 0 0; opacity: 0; transform: translateY(5px);
    transition: opacity var(--transition-std), transform var(--transition-std); pointer-events: none; z-index: 6;
}
.typing-indicator.visible { opacity: 1; transform: translateY(0); }
.typing-indicator .codicon-spin { margin-right: var(--space-xs); color: var(--accent-primary); }

/* Input Toolbar (Attach, Media etc. - ABOVE main input) */
.input-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
    padding: 0 var(--space-xs);
}

.input-toolbar-left {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-sm);
}

.input-toolbar-right {
    display: flex;
    justify-content: flex-end;
    margin-left: auto;
}

.input-toolbar .icon-button {
    font-size: 16px;
    padding: 3px;
}

/* TTS Button in toolbar */
#btn-toggle-tts {
    font-size: 18px;
    padding: 4px;
    color: var(--text-secondary);
}

#btn-toggle-tts:hover {
    color: var(--text-primary);
    background-color: var(--accent-secondary-hover);
}

#btn-toggle-tts.active {
    color: var(--accent-primary);
    background-color: var(--vscode-inputValidation-infoBackground);
    border-color: var(--vscode-inputValidation-infoBorder);
}

/* Input Actions Secondary (Now in Global Toolbar - styling remains similar) */
.input-actions-secondary .icon-button {
    font-size: 15px; padding: 2px;
    color: var(--text-secondary); opacity: 0.6;
}
.input-actions-secondary .icon-button:hover {
    opacity: 1; color: var(--text-primary);
    background-color: var(--accent-secondary-hover);
}

/* Container for Input Wrapper and TTS Button */
.input-area-bottom {
    display: flex;
    align-items: flex-end;
    gap: var(--space-sm);
}

/* Input Wrapper (Textarea + Record/Send/Cancel) */
.input-wrapper {
    flex-grow: 1;
    display: flex; align-items: flex-end; gap: var(--space-sm);
    background-color: var(--bg-tertiary); border: 1px solid var(--border-color);
    border-radius: var(--radius-md); padding: var(--space-xs) var(--space-xs) var(--space-xs) var(--space-sm);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast); position: relative;
    width: 100%; /* Ensure the wrapper takes full width */
}
.input-wrapper:focus-within { border-color: var(--focus-border); box-shadow: 0 0 0 1.5px var(--focus-border); }

#message-input {
    flex-grow: 1; border: none; padding: var(--space-sm); background: transparent;
    color: var(--text-primary); font-family: inherit; font-size: inherit; resize: none; outline: none;
    min-height: calc(1.6em + var(--space-sm) * 2); max-height: 250px; line-height: var(--line-height);
    overflow-y: auto;
    width: 100%; /* Ensure the textarea takes full width */
    box-sizing: border-box; /* Include padding in width calculation */
}
#message-input::placeholder { color: var(--text-secondary); opacity: 0.7; }

.input-actions-main {
    display: flex;
    align-items: flex-end;
    padding-bottom: var(--space-xs);
    position: absolute; /* Position absolutely */
    right: var(--space-xs); /* Align to the right */
    bottom: var(--space-xs); /* Align to the bottom */
    background-color: var(--bg-tertiary); /* Match background */
    z-index: 2; /* Ensure buttons stay above textarea */
}
.input-actions-main .icon-button {
    padding: var(--space-sm);
    font-size: 20px;
    margin-left: var(--space-xs);
    border-radius: var(--radius-md);
}

#btn-record-audio {
    color: var(--text-secondary);
}
#btn-record-audio:hover {
     background-color: var(--accent-secondary-hover);
     color: var(--accent-secondary-text);
}
#btn-record-audio.recording {
    color: var(--text-error);
    background-color: var(--vscode-inputValidation-errorBackground);
    border-color: var(--vscode-inputValidation-errorBorder);
}
#btn-record-audio.recording:hover {
     background-color: var(--vscode-inputValidation-errorBackground);
}

#btn-send {
    background-color: var(--accent-primary); color: var(--accent-primary-text);
    box-shadow: var(--shadow-sm); border: none;
}
#btn-send:hover:not(:disabled) { background-color: var(--accent-primary-hover); box-shadow: var(--shadow-md); transform: translateY(-1px); }
#btn-send:disabled { background-color: var(--accent-secondary); color: var(--accent-secondary-text); opacity: 0.5; box-shadow: none; transform: none; cursor: not-allowed; }

#btn-cancel {
    background-color: rgba(255, 0, 0, 0.1);
    color: var(--text-error);
    border: 1px solid rgba(255, 0, 0, 0.3);
    display: none;
}
#btn-cancel:hover:not(:disabled) {
    background-color: rgba(255, 0, 0, 0.2);
    border-color: var(--text-error);
    color: var(--text-error);
}
#btn-cancel.visible {
    display: inline-flex;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
    100% { opacity: 0.7; transform: scale(1); }
}

/* TTS Button - Positioned via Flexbox */
.tts-button-side {
    flex-shrink: 0;
    margin-bottom: var(--space-xs);
    font-size: 18px;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
}

/* --- Scrollbar --- */
::-webkit-scrollbar { width: 9px; height: 9px; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: var(--vscode-scrollbarSlider-background); border-radius: 5px; border: 2px solid transparent; background-clip: content-box; }
::-webkit-scrollbar-thumb:hover { background: var(--vscode-scrollbarSlider-hoverBackground); background-clip: content-box; }
::-webkit-scrollbar-thumb:active { background: var(--vscode-scrollbarSlider-activeBackground); background-clip: content-box; }
::-webkit-scrollbar-corner { background: transparent; }

/* --- Responsive --- */
@media (max-width: 700px) {
    .global-toolbar { flex-direction: column; align-items: stretch; }
    .toolbar-group-left, .toolbar-group-right { justify-content: center; }
    .dropdown-container { flex-grow: 1; min-width: 150px; }
}
@media (max-width: 600px) {
    .global-toolbar { gap: var(--space-xs) var(--space-sm); padding: var(--space-xs) var(--space-md); }
    .chat-header h1 { font-size: 1.1em; }
}
@media (max-width: 450px) {
    .message { max-width: 95%; }
    .chat-header { padding: var(--space-sm) var(--space-md); }
    .chat-messages { padding: var(--space-md); }
    .chat-input-area { padding: var(--space-sm) var(--space-md); }
    .message-avatar { width: 28px; height: 28px; font-size: 16px; }
    .input-toolbar { flex-wrap: wrap; }
    .input-toolbar-left { flex-wrap: wrap; justify-content: flex-start; }
    .input-toolbar-right { margin-top: var(--space-xs); }
    .toolbar-group-right.input-actions-secondary { justify-content: space-around; }
    .input-actions-main .icon-button { font-size: 18px; padding: var(--space-xs); }
    .tts-button-side { font-size: 16px; padding: var(--space-xs); }
    .input-area-bottom { gap: var(--space-xs); }
}
