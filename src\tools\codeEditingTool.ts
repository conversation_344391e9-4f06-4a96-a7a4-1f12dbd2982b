// Import core Node.js modules
import * as vscode from 'vscode';
import * as path from 'path';
import { randomUUID } from 'crypto';
import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';
import { z } from 'zod';

// Import the ts-morph library for deep AST manipulation in TypeScript/JavaScript.
// This is a production dependency for any serious structural refactoring tool.
import { Project, SourceFile, SyntaxKind, Node, FunctionDeclaration, ClassDeclaration, Scope, Writers, WriterFunction, PropertyDeclaration, Identifier, VariableStatement, Statement } from 'ts-morph';

// Import core framework contracts, classes, and functions from toolFramework.ts
import {
    AITerminalTool,
    IFileSystemManager,
    DiffFormat,
    DiffOptions,
    FileEditOptions,
    ILogger,
    IWorkspaceKnowledge,
    ITool,
    ToolActionDefinition,
    ToolInvokeOptions,
    ToolResult,
    ToolPermission,
    InteractiveSession,
    IAIContext,
    IToolTerminalContext,
    IToolOperationMemory,
    createToolOperationMemory,
    updateToolOperationMemory,
    LanguageModelPromptPart
} from './toolFramework';

// Import memory types from the correct location
import type { IMemoryOperations, MemorySource, MemoryType } from '../memory/types';

// #region --- Zod Schemas for Rigorous Tool Action Validation ---

const FilePathSchema = z.string().describe("The workspace-relative path to the file.");
const LineNumberSchema = z.number().int().positive().describe("The 1-based line number.");
const CreateBackupSchema = z.boolean().optional().default(false).describe("If true, creates a .bak file before modification.");

const ReplaceLinesInputSchema = z.object({
    filePath: FilePathSchema,
    startLine: LineNumberSchema,
    endLine: LineNumberSchema,
    newContent: z.string().describe("The new content to substitute for the specified lines."),
    createBackup: CreateBackupSchema,
});

const InsertLinesInputSchema = z.object({
    filePath: FilePathSchema,
    line: LineNumberSchema,
    newContent: z.string().describe("The content to be inserted."),
    createBackup: CreateBackupSchema,
});

const FindAndReplaceInputSchema = z.object({
    globPattern: z.string().describe("A glob pattern to find files to search within (e.g., 'src/**/*.ts')."),
    findPattern: z.string().describe("The text or regex pattern to search for."),
    replacePattern: z.string().describe("The text or regex replacement pattern."),
    isRegex: z.boolean().optional().default(false).describe("Indicates if the findPattern is a regular expression."),
});

const MoveAndRefactorInputSchema = z.object({
    sourcePath: z.string().describe("The current workspace-relative path of the file or directory to move."),
    destinationPath: z.string().describe("The new workspace-relative path for the file or directory."),
});

const RenameSymbolInputSchema = z.object({
    filePath: FilePathSchema,
    line: LineNumberSchema,
    column: z.number().int().positive().describe("The 1-based column number where the symbol is located."),
    newName: z.string().min(1).describe("The new name for the symbol. All references will be updated."),
});

const ExtractToFunctionInputSchema = z.object({
    filePath: FilePathSchema,
    startLine: LineNumberSchema,
    endLine: LineNumberSchema,
    newFunctionName: z.string().min(1).describe("The name of the new function to be created."),
});

const EncapsulateFieldInputSchema = z.object({
    filePath: FilePathSchema,
    className: z.string().describe("The name of the class containing the field."),
    propertyName: z.string().describe("The name of the public field to encapsulate."),
});

const UntangleAndRestructureFileInputSchema = z.object({
    filePath: FilePathSchema,
    aiRestructure: z.boolean().optional().default(false).describe("If true, after cleaning syntax, an AI will attempt to logically reorder code blocks."),
    runLinterFix: z.boolean().optional().default(true).describe("If true, attempts to run the project's configured linter with --fix."),
});

const AIRefactorInputSchema = z.object({
    filePath: FilePathSchema,
    refactorInstruction: z.string().describe("A detailed, natural language instruction for the desired refactoring."),
    startLine: LineNumberSchema.optional(),
    endLine: LineNumberSchema.optional(),
});

const GenerateCodeInputSchema = z.object({
    filePath: FilePathSchema,
    generationInstruction: z.string().describe("A detailed, natural language instruction for the code to be generated."),
    line: LineNumberSchema.optional().describe("Optional line number for insertion. If omitted, appends to the end."),
});

const GenerateCommitMessageInputSchema = z.object({
    conventionalCommit: z.boolean().optional().default(true).describe("If true, generates a message following the Conventional Commits specification."),
});

const ManageNpmDependencyInputSchema = z.object({
    packageName: z.string().describe("The name of the npm package to add or remove."),
    action: z.enum(['add', 'remove']).describe("Whether to add or remove the dependency."),
    isDevDependency: z.boolean().optional().default(false).describe("True if this is a development dependency."),
});

// #endregion

// #region --- Language-Agnostic Strategy Abstractions ---

interface ILanguageStrategy {
    renameSymbol(filePath: string, line: number, column: number, newName: string): Promise<string[]>;
    extractToFunction(filePath: string, startLine: number, endLine: number, newFunctionName: string): Promise<{ newFunctionCode: string }>;
    organizeImports(filePath: string): Promise<void>;
    applyCodeAction(filePath: string, startLine: number, endLine: number, actionName: string): Promise<void>;
}

class TypeScriptStrategy implements ILanguageStrategy {
    constructor(private project: Project, private logger: ILogger) { }

    private getSourceFileOrThrow(filePath: string): SourceFile {
        const absolutePath = this.resolvePath(filePath);
        const sourceFile = this.project.getSourceFile(absolutePath);
        if (!sourceFile) throw new Error(`TypeScript strategy could not find file in project: ${filePath}`);
        return sourceFile;
    }

    public async renameSymbol(filePath: string, line: number, column: number, newName: string): Promise<string[]> {
        this.logger.debug(`TS Strategy: Renaming symbol at ${filePath}:${line}:${column} to '${newName}'`);
        const sourceFile = this.getSourceFileOrThrow(filePath);
        const position = sourceFile.compilerNode.getPositionOfLineAndCharacter(line - 1, column - 1);
        const renameNode = sourceFile.getDescendantAtPos(position);
        if (!renameNode) throw new Error(`No symbol found at ${filePath}:${line}:${column}.`);

        renameNode.rename(newName);
        return await this.saveModifiedFiles();
    }

    public async extractToFunction(filePath: string, startLine: number, endLine: number, newFunctionName: string): Promise<{ newFunctionCode: string }> {
        this.logger.debug(`TS Strategy: Extracting lines ${startLine}-${endLine} of ${filePath} to function '${newFunctionName}'`);
        const sourceFile = this.getSourceFileOrThrow(filePath);
        const statements = sourceFile.getStatementsInRan(startLine, endLine);
        if (statements.length === 0) throw new Error("No code statements found in the specified range.");

        const insertionPoint = statements[0].getParentSyntaxList() || sourceFile;
        const insertionIndex = statements[0].getChildIndex();

        const writer = Writers.union(
            (w) => w.write(statements.map(s => s.getText()).join('\n')),
        );

        const refactoring = sourceFile.getLanguageService().getApplicableRefactors(statements[0].getStart(), statements[statements.length - 1].getEnd());
        const extractAction = refactoring.find(r => r.name === "Extract Symbol")?.actions.find(a => a.name.includes("function"));
        if (!extractAction) throw new Error("Extraction to function is not an available refactoring for the selected code range.");

        const edits = sourceFile.getLanguageService().getEditsForRefactor(statements[0].getStart(), statements[statements.length - 1].getEnd(), extractAction.name, { newName: newFunctionName });
        if (!edits) throw new Error("Could not get edits for extraction refactoring.");

        edits.applyChanges();
        await this.saveModifiedFiles();

        const newFunc = sourceFile.getFunction(newFunctionName);
        if (!newFunc) throw new Error("Failed to find the newly created function after extraction.");

        return { newFunctionCode: newFunc.getText() };
    }

    public async organizeImports(filePath: string): Promise<void> {
        this.logger.debug(`TS Strategy: Organizing imports for ${filePath}`);
        const sourceFile = this.getSourceFileOrThrow(filePath);
        sourceFile.organizeImports();
        await this.saveModifiedFiles();
    }

    public async applyCodeAction(filePath: string, startLine: number, endLine: number, actionName: string): Promise<void> {
        this.logger.debug(`TS Strategy: Applying code action '${actionName}' to ${filePath}`);
        const sourceFile = this.getSourceFileOrThrow(filePath);
        const startPos = sourceFile.compilerNode.getPositionOfLineAndCharacter(startLine - 1, 0);
        const endPos = sourceFile.compilerNode.getPositionOfLineAndCharacter(endLine, 0);
        const languageService = this.project.getLanguageService();

        const codeFixes = languageService.getCodeFixesAtPosition(sourceFile.getFilePath(), startPos, endPos, [], {}, {});
        const targetFix = codeFixes.find(fix => fix.getDescription() === actionName);
        if (!targetFix) throw new Error(`Code action '${actionName}' could not be found for the specified range.`);

        targetFix.getChanges().forEach(change => change.getSourceFile().applyTextChanges(change.getTextChanges()));
        await this.saveModifiedFiles();
    }

    private resolvePath(relativePath: string): string {
        return path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', relativePath);
    }

    private async saveModifiedFiles(): Promise<string[]> {
        const dirtyFiles = this.project.getSourceFiles().filter(sf => sf.isModified());
        await this.project.save();
        return dirtyFiles.map(f => vscode.workspace.asRelativePath(f.getFilePath()));
    }
}

class VSCodeLSPStrategy implements ILanguageStrategy {
    constructor(private logger: ILogger) { }

    private async getDoc(filePath: string): Promise<vscode.TextDocument> {
        const absPath = this.resolvePath(filePath);
        return await vscode.workspace.openTextDocument(absPath);
    }

    public async renameSymbol(filePath: string, line: number, column: number, newName: string): Promise<string[]> {
        this.logger.debug(`LSP Strategy: Renaming symbol at ${filePath}:${line}:${column} to '${newName}'`);
        const doc = await this.getDoc(filePath);
        const position = new vscode.Position(line - 1, column - 1);
        const workspaceEdit = await vscode.commands.executeCommand<vscode.WorkspaceEdit>('vscode.executeDocumentRenameProvider', doc.uri, position, newName);
        if (!workspaceEdit) throw new Error(`The active language extension for '${doc.languageId}' does not support renaming.`);
        await vscode.workspace.applyEdit(workspaceEdit);
        await vscode.workspace.saveAll();
        return Array.from(workspaceEdit.entries(), ([uri, _]) => vscode.workspace.asRelativePath(uri));
    }

    public async extractToFunction(filePath: string, startLine: number, endLine: number, newFunctionName: string): Promise<{ newFunctionCode: string }> {
        this.logger.debug(`LSP Strategy: Attempting to extract lines ${startLine}-${endLine} of ${filePath}`);
        const doc = await this.getDoc(filePath);
        const range = new vscode.Range(new vscode.Position(startLine - 1, 0), new vscode.Position(endLine, 0));
        const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>('vscode.executeCodeActionProvider', doc.uri, range);
        const extractAction = codeActions.find(action => action.kind?.value.includes('refactor.extract'));
        if (!extractAction || !extractAction.edit) throw new Error(`The language extension for '${doc.languageId}' does not provide an 'Extract to function' action for the selected range.`);
        await vscode.workspace.applyEdit(extractAction.edit);
        await vscode.workspace.saveAll();
        return { newFunctionCode: `// Function extracted successfully in ${filePath}. Review the file for the result.` };
    }

    public async organizeImports(filePath: string): Promise<void> {
        this.logger.debug(`LSP Strategy: Organizing imports for ${filePath}`);
        const doc = await this.getDoc(filePath);
        const edit = await vscode.commands.executeCommand<vscode.WorkspaceEdit>('_executeCodeActionProvider', doc.uri, new vscode.Range(0, 0, 0, 0), 'source.organizeImports');
        if (!edit) throw new Error(`The language extension for '${doc.languageId}' does not support organizing imports.`);
        await vscode.workspace.applyEdit(edit);
        await vscode.workspace.saveAll();
    }

    public async applyCodeAction(filePath: string, startLine: number, endLine: number, actionName: string): Promise<void> {
        this.logger.debug(`LSP Strategy: Applying action '${actionName}' to ${filePath}`);
        const doc = await this.getDoc(filePath);
        const range = new vscode.Range(new vscode.Position(startLine - 1, 0), new vscode.Position(endLine, 0));
        const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>('vscode.executeCodeActionProvider', doc.uri, range);
        const targetAction = codeActions.find(a => a.title === actionName || a.kind?.value === actionName);
        if (!targetAction || !targetAction.edit) throw new Error(`Code action '${actionName}' not found or does not provide an edit.`);
        await vscode.workspace.applyEdit(targetAction.edit);
        await vscode.workspace.saveAll();
    }

    private resolvePath(relativePath: string): string {
        return path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', relativePath);
    }
}

class LanguageServiceFactory {
    private tsStrategy: TypeScriptStrategy;
    private lspStrategy: VSCodeLSPStrategy;

    constructor(project: Project, logger: ILogger) {
        this.tsStrategy = new TypeScriptStrategy(project, logger);
        this.lspStrategy = new VSCodeLSPStrategy(logger);
    }

    public async getStrategyForFile(filePath: string): Promise<ILanguageStrategy> {
        const uri = vscode.Uri.file(path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath));
        const doc = await vscode.workspace.openTextDocument(uri);
        switch (doc.languageId) {
            case 'typescript':
            case 'typescriptreact':
            case 'javascript':
            case 'javascriptreact':
                return this.tsStrategy;
            default:
                return this.lspStrategy;
        }
    }
}

// #endregion

export class AdvancedCodeEditingTool extends AITerminalTool implements ITool {
    public readonly name = 'AdvancedCodeEditing';
    public readonly description = 'Performs advanced, structural code editing, refactoring, and analysis using Abstract Syntax Trees (AST), AI, and workspace-wide operations for any programming language.';
    public readonly version = '3.0.0';
    public readonly category = 'Development';

    public readonly actions: Readonly<Record<string, ToolActionDefinition>> = {
        untangleAndRestructureFile: {
            description: "Fixes syntax, cleans up, and logically restructures a convoluted code file.",
            inputSchema: UntangleAndRestructureFileInputSchema,
            outputSchema: z.object({ success: z.boolean(), message: z.string(), stepsTaken: z.array(z.string()), memoryId: z.string() })
        },
        replaceLines: {
            description: 'Replaces a range of lines in a single file with new content. Use for simple text changes.',
            inputSchema: ReplaceLinesInputSchema,
            outputSchema: z.object({ success: z.boolean(), message: z.string(), memoryId: z.string() })
        },
        insertLines: {
            description: 'Inserts new content at a specific line in a single file. Use for simple text additions.',
            inputSchema: InsertLinesInputSchema,
            outputSchema: z.object({ success: z.boolean(), message: z.string(), memoryId: z.string() })
        },
        findAndReplace: {
            description: 'Performs a workspace-wide search and replace using text or regex patterns.',
            inputSchema: FindAndReplaceInputSchema,
            outputSchema: z.object({ success: z.boolean(), filesModified: z.number(), totalReplacements: z.number(), memoryId: z.string() })
        },
        renameSymbol: {
            description: 'Safely renames a variable, function, class, or interface and all its references across the entire workspace for any language.',
            inputSchema: RenameSymbolInputSchema,
            outputSchema: z.object({ success: z.boolean(), filesModified: z.array(z.string()), memoryId: z.string() })
        },
        extractToFunction: {
            description: 'Extracts a block of code into a new function, automatically determining parameters and return values (for supported languages).',
            inputSchema: ExtractToFunctionInputSchema,
            outputSchema: z.object({ success: z.boolean(), message: z.string(), memoryId: z.string() })
        },
        encapsulateField: {
            description: 'Makes a public class field private and creates getter and setter methods for it (TypeScript/JavaScript only).',
            inputSchema: EncapsulateFieldInputSchema,
            outputSchema: z.object({ success: z.boolean(), filesModified: z.array(z.string()), memoryId: z.string() })
        },
        aiRefactor: {
            description: 'Uses an AI model to perform a complex refactoring based on a natural language instruction.',
            inputSchema: AIRefactorInputSchema,
            outputSchema: z.object({ success: z.boolean(), message: z.string(), diff: z.string(), memoryId: z.string() })
        },
        generateCode: {
            description: 'Uses an AI model to generate a new piece of code (e.g., class, function, test case) from an instruction.',
            inputSchema: GenerateCodeInputSchema,
            outputSchema: z.object({ success: z.boolean(), generatedCode: z.string(), memoryId: z.string() })
        },
        moveAndRefactorImports: {
            description: 'Moves a file or directory and automatically updates all relative import statements across the workspace (TypeScript/JavaScript only).',
            inputSchema: MoveAndRefactorInputSchema,
            outputSchema: z.object({ success: z.boolean(), filesUpdated: z.number(), memoryId: z.string() })
        },
        generateCommitMessage: {
            description: 'Analyzes staged git changes and generates a descriptive, conventional commit message.',
            inputSchema: GenerateCommitMessageInputSchema,
            outputSchema: z.object({ success: z.boolean(), commitMessage: z.string() })
        },
        manageNpmDependency: {
            description: 'Adds or removes an npm dependency, updating package.json and running the install command.',
            inputSchema: ManageNpmDependencyInputSchema,
            outputSchema: z.object({ success: z.boolean(), message: z.string(), terminalOutput: z.string() })
        },
    };

    private readonly logger: ILogger;
    private readonly fileSystem: IFileSystemManager;
    private readonly workspaceKnowledge: IWorkspaceKnowledge;
    private readonly memoryOperations: IMemoryOperations;
    private readonly languageServiceFactory: LanguageServiceFactory;

    constructor(
        services: {
            terminalSession: InteractiveSession;
            aiContext: IAIContext;
            toolContext: IToolTerminalContext;
            memoryManager: IMemoryOperations;
            fileSystem: IFileSystemManager;
            workspace: IWorkspaceKnowledge;
            logger: ILogger;
        }
    ) {
        super({ terminalSession: services.terminalSession, aiContext: services.aiContext, toolContext: services.toolContext });
        this.logger = services.logger;
        this.fileSystem = services.fileSystem;
        this.workspaceKnowledge = services.workspace;
        this.memoryOperations = services.memoryManager;
        const tsProject = new Project({ useInMemoryFileSystem: false });
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '.';
        tsProject.addSourceFilesAtPaths(`${workspaceRoot}/**/*.{ts,tsx,js,jsx}`);
        this.languageServiceFactory = new LanguageServiceFactory(tsProject, this.logger);

        this.logger.info(`[${this.name} v${this.version}] Tool initialized. Language-agnostic capabilities are active.`);
    }

    public async invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult> {
        const executionId = randomUUID();
        const actionDef = this.actions[actionName];

        if (!actionDef) {
            const error = { message: `Action '${actionName}' not found.` };
            this.logger.error(error.message, { toolName: this.name });
            return { executionId, toolName: this.name, actionName, status: 'failure', error };
        }

        const memory = createToolOperationMemory(this.name, actionName, input as Record<string, unknown>);

        try {
            options.cancellationToken.onCancellationRequested(() => { throw new Error('Operation cancelled.'); });

            const permissions = this.getPermissions(actionName);
            if (permissions.length > 0) {
                const granted = await options.services.permissionManager.requestPermissions(permissions, `Action '${actionName}'`);
                if (!granted) throw new Error(`Permissions denied: ${permissions.join(', ')}`);
            }

            const validatedInput = actionDef.inputSchema.parse(input);
            const { output, memoriesCreated } = await this._execute(actionName, validatedInput, options);
            const validatedOutput = actionDef.outputSchema.parse(output);

            const finalMemory = updateToolOperationMemory(memory, { success: true, output: validatedOutput });
            await this.memoryOperations.storeMemory(finalMemory as any);

            return {
                executionId,
                toolName: this.name,
                actionName,
                status: 'success',
                output: validatedOutput,
                memoriesCreated: [...(memoriesCreated || []), finalMemory.id]
            };
        } catch (error) {
            const errorDetails = error instanceof Error ? { message: error.message, stack: error.stack } : { message: String(error) };
            this.logger.error(`Action '${actionName}' failed.`, { executionId, error: errorDetails });
            const finalMemory = updateToolOperationMemory(memory, { success: false, error: errorDetails });
            await this.memoryOperations.storeMemory(finalMemory as any);
            return { executionId, toolName: this.name, actionName, status: 'failure', error: errorDetails };
        }
    }

    protected async _execute(actionName: string, validatedInput: unknown, options: ToolInvokeOptions): Promise<{ output: unknown; memoriesCreated?: string[] }> {
        // The core logic now delegates to specific, fully-implemented methods.
        switch (actionName) {
            case 'untangleAndRestructureFile':
                return { output: await this.executeUntangleAndRestructureFile(validatedInput as z.infer<typeof UntangleAndRestructureFileInputSchema>, options) };
            case 'replaceLines':
                return { output: await this.executeReplaceLines(validatedInput as z.infer<typeof ReplaceLinesInputSchema>) };
            case 'insertLines':
                return { output: await this.executeInsertLines(validatedInput as z.infer<typeof InsertLinesInputSchema>) };
            case 'findAndReplace':
                return { output: await this.executeFindAndReplace(validatedInput as z.infer<typeof FindAndReplaceInputSchema>) };
            case 'renameSymbol':
                return { output: await this.executeRenameSymbol(validatedInput as z.infer<typeof RenameSymbolInputSchema>) };
            case 'extractToFunction':
                return { output: await this.executeExtractToFunction(validatedInput as z.infer<typeof ExtractToFunctionInputSchema>) };
            case 'encapsulateField':
                return { output: await this.executeEncapsulateField(validatedInput as z.infer<typeof EncapsulateFieldInputSchema>) };
            case 'aiRefactor':
                return { output: await this.executeAiRefactor(validatedInput as z.infer<typeof AIRefactorInputSchema>, options) };
            case 'generateCode':
                return { output: await this.executeGenerateCode(validatedInput as z.infer<typeof GenerateCodeInputSchema>, options) };
            case 'moveAndRefactorImports':
                return { output: await this.executeMoveAndRefactor(validatedInput as z.infer<typeof MoveAndRefactorInputSchema>) };
            case 'generateCommitMessage':
                return { output: await this.executeGenerateCommitMessage(validatedInput as z.infer<typeof GenerateCommitMessageInputSchema>) };
            case 'manageNpmDependency':
                return { output: await this.executeManageNpmDependency(validatedInput as z.infer<typeof ManageNpmDependencyInputSchema>) };
            default:
                throw new Error(`Action '${actionName}' has no implementation.`);
        }
    }

    public getPermissions(actionName: string): readonly ToolPermission[] {
        // Permissions are defined based on the action's requirements on the system.
        if (['generateCommitMessage', 'manageNpmDependency', 'untangleAndRestructureFile'].includes(actionName)) {
            return ['workspace:read', 'workspace:write', 'shell:execute'];
        }
        if (['replaceLines', 'insertLines', 'findAndReplace', 'renameSymbol', 'extractToFunction', 'encapsulateField', 'aiRefactor', 'generateCode', 'moveAndRefactorImports'].includes(actionName)) {
            return ['workspace:read', 'workspace:write'];
        }
        return ['workspace:read'];
    }

    public async executeAIOperation(context: IAIContext, input: { prompt: string }): Promise<string> {
        this.logger.info(`Executing generic AI operation for tool`, { promptLength: input.prompt.length });
        const prompt: LanguageModelPromptPart[] = [{ type: 'text', text: input.prompt }];
        return context.llm.generateResponse(prompt, {
            cancellationToken: new vscode.CancellationTokenSource().token,
            jsonOutput: false
        });
    }

    public getDocumentation(): string {
        return `# Tool: ${this.name} v${this.version}
**Description:** ${this.description}
This tool performs advanced, language-aware code modifications. It automatically detects the programming language and uses the best available method (AST for TypeScript/JavaScript, VS Code's LSP for others) to ensure refactorings are safe and accurate.

## Actions
${Object.entries(this.actions).map(([name, def]) => this.getActionDocumentation(name, def)).join('\n')}`;
    }

    // #region --- Action Implementations (Delegating to Strategies) ---

    private async executeUntangleAndRestructureFile(input: z.infer<typeof UntangleAndRestructureFileInputSchema>, options: ToolInvokeOptions): Promise<{ success: boolean; message: string; stepsTaken: string[]; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'untangleAndRestructureFile', input, { affectedFiles: [input.filePath], tags: ['cleanup', 'refactor', 'ai'] });
        const stepsTaken: string[] = [];

        let currentContent = await this.fileSystem.readFile(input.filePath);
        const originalContent = currentContent;

        // Step 1: Pre-emptive syntax cleaning for common "messy" code issues.
        this.logger.info(`Untangle Step 1: Sanitizing syntax for ${input.filePath}`);
        let sanitizedContent = this.sanitizeBrokenComments(currentContent);
        if (sanitizedContent !== currentContent) {
            stepsTaken.push("Sanitized broken multi-line comments.");
            currentContent = sanitizedContent;
        }

        // Step 2: Attempt to format using the language-specific strategy. This often fixes many small syntax issues.
        try {
            this.logger.info(`Untangle Step 2: Programmatic formatting via LSP for ${input.filePath}`);
            await this.fileSystem.writeFile(input.filePath, currentContent); // Write sanitized content to disk
            const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);
            await strategy.organizeImports(input.filePath);
            await vscode.commands.executeCommand('editor.action.formatDocument', vscode.Uri.file(path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', input.filePath)));
            currentContent = await this.fileSystem.readFile(input.filePath);
            stepsTaken.push("Formatted document and organized imports using language server.");
        } catch (e) {
            const err = e as Error;
            this.logger.warn(`Programmatic formatting failed for ${input.filePath}, proceeding with AI. Error: ${err.message}`);
            stepsTaken.push("Programmatic formatting failed, will rely on AI for cleanup.");
        }

        // Step 3 (Optional): Run an external linter if requested.
        if (input.runLinterFix) {
            try {
                this.logger.info(`Untangle Step 3: Running linter with --fix for ${input.filePath}`);
                const terminal = this.getTerminalSession();
                // This assumes standard linters are in package.json scripts. A more robust solution would detect the linter.
                await terminal.executeCommand(`npx eslint ${input.filePath} --fix`);
                currentContent = await this.fileSystem.readFile(input.filePath);
                stepsTaken.push("Executed linter with auto-fix.");
            } catch (e) {
                const err = e as Error;
                this.logger.warn(`Linter command failed for ${input.filePath}: ${err.message}`);
                stepsTaken.push("Linter auto-fix command failed or was not configured.");
            }
        }

        // Step 4 (Optional): AI-driven logical restructuring.
        if (input.aiRestructure) {
            this.logger.info(`Untangle Step 4: Using AI to logically restructure ${input.filePath}`);
            const restructurePrompt = `You are an expert software architect. Your task is to logically restructure the following code file. Do not change the logic, but improve the organization.
            Common tasks include:
            - Grouping related functions or methods.
            - Ordering members within a class (e.g., public fields, private fields, constructor, public methods, private methods).
            - Moving helper functions closer to where they are used.
            - Ensuring a consistent and logical top-to-bottom flow in the file.
            
            IMPORTANT: Output only the complete, restructured code for the entire file. Do not add any explanations or markdown.
            
            Original File Content:
            ---
            ${currentContent}
            ---`;
            currentContent = await this.executeAIOperation(options.services.aiContext, { prompt: restructurePrompt });
            stepsTaken.push("Applied AI-driven logical code restructuring.");
        }

        await this.fileSystem.writeFile(input.filePath, currentContent);

        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { stepsTaken } });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, message: `Successfully untangled and restructured ${input.filePath}.`, stepsTaken, memoryId: finalMemory.id };
    }


    private async executeRenameSymbol(input: z.infer<typeof RenameSymbolInputSchema>): Promise<{ success: boolean; filesModified: string[]; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'renameSymbol', input, { tags: ['refactor', 'language-aware'] });
        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);
        const filesModified = await strategy.renameSymbol(input.filePath, input.line, input.column, input.newName);

        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { filesModified } });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, filesModified, memoryId: finalMemory.id };
    }

    private async executeExtractToFunction(input: z.infer<typeof ExtractToFunctionInputSchema>): Promise<{ success: boolean; message: string; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'extractToFunction', input, { affectedFiles: [input.filePath], tags: ['refactor', 'language-aware'] });
        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);
        const { newFunctionCode } = await strategy.extractToFunction(input.filePath, input.startLine, input.endLine, input.newFunctionName);

        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { newFunctionCode } });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, message: `Successfully extracted function in ${input.filePath}.`, memoryId: finalMemory.id };
    }

    private async executeEncapsulateField(input: z.infer<typeof EncapsulateFieldInputSchema>): Promise<{ success: boolean; filesModified: string[]; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'encapsulateField', input, { affectedFiles: [input.filePath], tags: ['refactor', 'typescript'] });
        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);
        if (!(strategy instanceof TypeScriptStrategy)) {
            throw new Error("Encapsulate Field is currently only supported for TypeScript/JavaScript files.");
        }

        const sourceFile = (strategy as any).getSourceFileOrThrow(input.filePath) as SourceFile;
        const classNode = sourceFile.getClassOrThrow(input.className);
        const prop = classNode.getPropertyOrThrow(input.propertyName);

        if (prop.getScope() === Scope.Private) {
            throw new Error(`Property '${input.propertyName}' is already private.`);
        }

        prop.set({ scope: Scope.Private });
        const getter = classNode.addGetAccessor({
            name: input.propertyName,
            returnType: prop.getType().getText(),
            statements: `return this.${input.propertyName};`
        });
        const setter = classNode.addSetAccessor({
            name: input.propertyName,
            parameters: [{ name: 'value', type: prop.getType().getText() }],
            statements: `this.${input.propertyName} = value;`
        });

        const references = prop.findReferencesAsNodes();
        for (const ref of references) {
            const parent = ref.getParent();
            if (Node.isPropertyAccessExpression(parent) && Node.isBinaryExpression(parent.getParent()) && parent.getParent().getOperatorToken().getKind() === SyntaxKind.EqualsToken) {
                parent.getParent().replaceWithText(`this.${setter.getName()}(${parent.getParent().getRight().getText()})`);
            } else {
                ref.replaceWithText(`this.${getter.getName()}`);
            }
        }

        const filesModified = await (strategy as any).saveModifiedFiles();
        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { filesModified } });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, filesModified, memoryId: finalMemory.id };
    }

    private async executeMoveAndRefactor(input: z.infer<typeof MoveAndRefactorInputSchema>): Promise<{ success: boolean; filesUpdated: number; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'moveAndRefactorImports', input, { tags: ['refactor', 'workspace', 'typescript'] });
        const strategy = await this.languageServiceFactory.getStrategyForFile(input.sourcePath);
        if (!(strategy instanceof TypeScriptStrategy)) {
            throw new Error("Move and Refactor Imports is currently only supported for TypeScript/JavaScript projects.");
        }

        const sourceFile = (strategy as any).getSourceFileOrThrow(input.sourcePath) as SourceFile;
        sourceFile.move(input.destinationPath);
        const modifiedFiles = await (strategy as any).saveModifiedFiles();

        const filesUpdated = modifiedFiles.length;
        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { filesUpdated } });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, filesUpdated, memoryId: finalMemory.id };
    }

    private async executeReplaceLines(input: z.infer<typeof ReplaceLinesInputSchema>): Promise<{ success: boolean; message: string; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'replaceLines', input, { affectedFiles: [input.filePath] });
        const content = await this.fileSystem.readFile(input.filePath);
        const lines = content.split('\n');
        if (input.startLine > input.endLine || input.endLine > lines.length) {
            throw new Error('Invalid line range provided.');
        }
        if (input.createBackup) await this.fileSystem.writeFile(`${input.filePath}.bak`, content);
        lines.splice(input.startLine - 1, input.endLine - input.startLine + 1, ...input.newContent.split('\n'));
        await this.fileSystem.writeFile(input.filePath, lines.join('\n'));

        const finalMemory = updateToolOperationMemory(memory, { success: true });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, message: `Successfully replaced lines in ${input.filePath}.`, memoryId: finalMemory.id };
    }

    private async executeInsertLines(input: z.infer<typeof InsertLinesInputSchema>): Promise<{ success: boolean; message: string; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'insertLines', input, { affectedFiles: [input.filePath] });
        const content = await this.fileSystem.readFile(input.filePath);
        const lines = content.split('\n');
        if (input.line > lines.length + 1 || input.line < 1) {
            throw new Error('Invalid line number for insertion.');
        }
        if (input.createBackup) await this.fileSystem.writeFile(`${input.filePath}.bak`, content);
        lines.splice(input.line - 1, 0, ...input.newContent.split('\n'));
        await this.fileSystem.writeFile(input.filePath, lines.join('\n'));

        const finalMemory = updateToolOperationMemory(memory, { success: true });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, message: `Successfully inserted content in ${input.filePath}.`, memoryId: finalMemory.id };
    }

    private async executeFindAndReplace(input: z.infer<typeof FindAndReplaceInputSchema>): Promise<{ success: boolean; filesModified: number; totalReplacements: number; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'findAndReplace', input, { tags: ['bulk', 'workspace'] });
        const files = await this.workspaceKnowledge.findFilesByPattern(input.globPattern);
        let filesModified = 0;
        let totalReplacements = 0;

        for (const file of files) {
            const filePath = vscode.workspace.asRelativePath(file);
            const replacements = await this.fileSystem.replaceInFile(
                filePath,
                input.isRegex ? new RegExp(input.findPattern, 'g') : input.findPattern,
                input.replacePattern
            );
            if (replacements > 0) {
                filesModified++;
                totalReplacements += replacements;
            }
        }

        const output = { filesModified, totalReplacements };
        const finalMemory = updateToolOperationMemory(memory, { success: true, output });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, ...output, memoryId: finalMemory.id };
    }

    private async executeAiRefactor(input: z.infer<typeof AIRefactorInputSchema>, options: ToolInvokeOptions): Promise<{ success: boolean; message: string; diff: string; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'aiRefactor', input, { affectedFiles: [input.filePath], tags: ['ai', 'refactor'] });
        const originalContent = await this.fileSystem.readFile(input.filePath);
        const codeToRefactor = this.extractCodeBlock(originalContent, input.startLine, input.endLine);

        const prompt = `You are an expert AI software engineer. Your task is to refactor a piece of code based on a specific instruction.
        File Path: ${input.filePath}
        Instruction: "${input.refactorInstruction}"

        IMPORTANT: You must only output the new, refactored code for the provided snippet. Do NOT include explanations, markdown, or any other text.

        Original Code Snippet:
        ---
        ${codeToRefactor}
        ---`;

        const refactoredSnippet = await this.executeAIOperation(options.services.aiContext, { prompt });
        const newContent = this.replaceCodeBlock(originalContent, refactoredSnippet, input.startLine, input.endLine);

        await this.fileSystem.writeFile(input.filePath, newContent, { backup: true });
        const diff = await this.fileSystem.diffContent(originalContent, newContent, { format: DiffFormat.Unified });

        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { diff } });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, message: 'AI refactoring complete.', diff, memoryId: finalMemory.id };
    }

    private async executeGenerateCode(input: z.infer<typeof GenerateCodeInputSchema>, options: ToolInvokeOptions): Promise<{ success: boolean; generatedCode: string; memoryId: string }> {
        const memory = createToolOperationMemory(this.name, 'generateCode', input, { affectedFiles: [input.filePath], tags: ['ai', 'generation'] });

        const prompt = `You are an expert AI software engineer. Generate a piece of code based on the following instruction.
        Instruction: "${input.generationInstruction}"

        IMPORTANT: You must only output the raw code. Do NOT include explanations, markdown, or any other text.
        `;

        const generatedCode = await this.executeAIOperation(options.services.aiContext, { prompt });
        await this.executeInsertLines({
            filePath: input.filePath,
            line: input.line || (await this.fileSystem.readFile(input.filePath)).split('\n').length + 1,
            newContent: generatedCode,
            createBackup: false, // Don't backup for a new code insertion
        });

        const finalMemory = updateToolOperationMemory(memory, { success: true, output: { generatedCode } });
        await this.memoryOperations.storeMemory(finalMemory as any);
        return { success: true, generatedCode, memoryId: finalMemory.id };
    }

    private async executeGenerateCommitMessage(input: z.infer<typeof GenerateCommitMessageInputSchema>): Promise<{ success: boolean; commitMessage: string }> {
        const terminal = this.getTerminalSession();
        await terminal.executeCommand('git add -A');
        const diff = await terminal.executeCommand('git diff --staged');

        if (!diff.trim()) return { success: true, commitMessage: 'No changes to commit.' };

        const prompt = `Based on the following git diff, generate a concise commit message.
        ${input.conventionalCommit ? 'Use the Conventional Commits specification (e.g., feat(api): add new endpoint). ' : ''}
        Diff:\n${diff}`;
        const commitMessage = await this.executeAIOperation(this.getAIContext(), { prompt });
        return { success: true, commitMessage };
    }

    private async executeManageNpmDependency(input: z.infer<typeof ManageNpmDependencyInputSchema>): Promise<{ success: boolean; message: string; terminalOutput: string }> {
        const terminal = this.getTerminalSession();
        const cmd = `npm ${input.action === 'add' ? 'install' : 'uninstall'} ${input.packageName}${input.isDevDependency ? ' --save-dev' : ''}`;
        const output = await terminal.executeCommand(cmd);

        return { success: true, message: `Successfully executed: ${cmd}`, terminalOutput: output };
    }
    // #endregion

    // #region --- Private Helper Methods ---

    private getActionDocumentation(name: string, def: ToolActionDefinition): string {
        const inputSchema = def.inputSchema as z.ZodObject<any>;
        const props = Object.entries(inputSchema.shape).map(([key, val]) => {
            const schema = val as z.ZodTypeAny;
            return `        - \`${key}\`: \`${(schema._def as any).typeName}\` - ${schema.description || 'No description.'}`;
        }).join('\n');
        return `\n### \`${name}\`\n**Description:** ${def.description}\n**Inputs:**\n${props}`;
    }

    private extractCodeBlock(content: string, startLine?: number, endLine?: number): string {
        if (!startLine || !endLine) return content;
        return content.split('\n').slice(startLine - 1, endLine).join('\n');
    }

    private replaceCodeBlock(originalContent: string, newSnippet: string, startLine?: number, endLine?: number): string {
        if (!startLine || !endLine) return newSnippet; // Replace whole file if no range
        const lines = originalContent.split('\n');
        lines.splice(startLine - 1, endLine - startLine + 1, ...newSnippet.split('\n'));
        return lines.join('\n');
    }

    private sanitizeBrokenComments(code: string): string {
        let inComment = false;
        let depth = 0;
        const sanitizedChars = [];
        for (let i = 0; i < code.length; i++) {
            if (code[i] === '/' && code[i + 1] === '*') {
                if (!inComment) {
                    sanitizedChars.push('/', '*');
                    inComment = true;
                    depth = 1;
                    i++;
                } else {
                    depth++;
                    // This is a nested /*, we will escape it to prevent syntax break
                    sanitizedChars.push('/', '\\*');
                    i++;
                }
            } else if (code[i] === '*' && code[i + 1] === '/') {
                if (inComment) {
                    depth--;
                    if (depth === 0) {
                        sanitizedChars.push('*', '/');
                        inComment = false;
                    } else {
                        // This is a premature */, escape it
                        sanitizedChars.push('\\*', '/');
                    }
                    i++;
                } else {
                    // This is a */ without a preceding /*, escape it
                    sanitizedChars.push('\\*', '/');
                    i++;
                }
            } else {
                sanitizedChars.push(code[i]);
            }
        }
        return sanitizedChars.join('');
    }

    // #endregion
}