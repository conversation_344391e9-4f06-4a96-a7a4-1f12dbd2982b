{"version": 3, "file": "searchTool.js", "sourceRoot": "", "sources": ["../../src/tools/searchTool.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,6BAAwB;AAGxB,2DAA4E;AAC5E,yCAAsC;AAgBtC;;GAEG;AACH,MAAa,UAAW,SAAQ,qCAAiB;IACvC,eAAe,CAAoB;IAE3C;;OAEG;IACM,MAAM,GAAG,OAAC,CAAC,KAAK,CAAC;QACxB,sBAAsB;QACtB,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;QAE/C,sCAAsC;QACtC,OAAC,CAAC,MAAM,CAAC;YACP,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;YACtD,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;gBAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;qBAC1C,QAAQ,CAAC,qCAAqC,CAAC;gBAClD,eAAe,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;qBACpC,QAAQ,CAAC,wCAAwC,CAAC;gBACrD,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;qBAClC,QAAQ,CAAC,mCAAmC,CAAC;aACjD,CAAC,CAAC,QAAQ,EAAE;SACd,CAAC;KACH,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,8DAA8D;QAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QACvC,CAAC;QACD,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,YACE,UAAwB,EACxB,IAAa,EACb,WAAoB,EACpB,iBAAoC,EAAE,EACtC,cAA6B;QAE7B,KAAK,CACH,UAAU,EACV,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,QAAQ,EACnC,WAAW,IAAI,yEAAyE,EACxF,cAAc,CACf,CAAC;QAEF,6BAA6B;QAC7B,IAAI,CAAC,eAAe,GAAG;YACrB,KAAK,EAAE,EAAE;YACT,eAAe,EAAE,IAAI;YACrB,GAAG,cAAc;SAClB,CAAC;QAEF,IAAI,CAAC,CAAC,QAAQ,IAAI,UAAU,IAAI,MAAM,IAAI,UAAU,IAAI,SAAS,IAAI,UAAU,CAAC,EAAE,CAAC;YACjF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,wCAAwC,IAAI,CAAC,IAAI,IAAI;gBACrD,iEAAiE,CAClE,CAAC;QACJ,CAAC;QAED,iDAAiD;QACjD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,WAA8B,EAC9B,OAAsB;QAEtB,MAAM,eAAe,GAAG,EAAE,GAAG,WAAW,EAAE,CAAC;QAE3C,mDAAmD;QACnD,IAAI,OAAO,EAAE,CAAC;YACZ,kDAAkD;YAClD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,eAAe,CAAC,OAAO,GAAG;oBACxB,GAAG,eAAe,CAAC,OAAO;oBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;iBACvB,CAAC;YACJ,CAAC;YAED,yCAAyC;YACzC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAChC,MAAe,EACf,aAAqB,EACrB,OAA0B;QAE1B,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,SAAS,GAAwB;gBACrC,KAAK,EAAE,aAAa;gBACpB,OAAO,EAAE,EAAE;gBACX,QAAQ,EAAE;oBACR,aAAa,EAAE,OAAO;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;YAEF,kCAAkC;YAClC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1B,yDAAyD;gBACzD,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;oBAC/C,OAAO,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;oBAC/D,QAAQ,EAAE,EAAE;oBACZ,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,0BAA0B;iBACvD,CAAC,CAAC,CAAC;YACN,CAAC;iBAAM,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;gBACvE,gDAAgD;gBAChD,MAAM,SAAS,GAAG,MAAwD,CAAC;gBAC3E,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC;oBAClD,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;wBACtC,OAAO,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;wBAC/D,QAAQ,EAAE,EAAE;wBACZ,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC;qBAC5B,CAAC,CAAC;oBACL,CAAC,CAAC,EAAE,CAAC;gBAEP,oCAAoC;gBACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBACjD,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;wBACtB,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,IAAI,EAAE,CAAC;wBAC9C,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBAClC,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACnD,6CAA6C;gBAC7C,SAAS,CAAC,OAAO,GAAG,CAAC;wBACnB,OAAO,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;wBACrE,QAAQ,EAAE,EAAE;wBACZ,KAAK,EAAE,GAAG;qBACX,CAAC,CAAC;YACL,CAAC;YAED,cAAc;YACd,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClB,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,SAAuB,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,KAAK,CAAC,KAAoD;QACxE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAE9C,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,mDAAmD;YACnD,MAAM,WAAW,GAAsB;gBACrC,GAAG,IAAI,CAAC,eAAe;gBACvB,GAAG,WAAW;aACf,CAAC;YAEF,4CAA4C;YAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE7E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,KAAK,GAAG,CAAC,CAAC;YAEjE,8BAA8B;YAC9B,IAAI,MAAM,CAAC;YACX,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC/B,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;oBACnC,KAAK;oBACL,OAAO,EAAE,eAAe;iBACzB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpC,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;oBACjC,KAAK;oBACL,OAAO,EAAE,eAAe;iBACzB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACvC,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;oBACpC,KAAK;oBACL,OAAO,EAAE,eAAe;iBACzB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;YAED,mCAAmC;YACnC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO;gBACL,KAAK;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE;oBACR,KAAK,EAAE,IAAI;oBACX,YAAY,EAAE,KAAK,CAAC,OAAO;oBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAxND,gCAwNC;AAED,kBAAe,UAAU,CAAC", "sourcesContent": ["/**\n * Enhanced search tool with context support and schema validation.\n * Wraps a base search tool to provide consistent behavior and type safety.\n */\n\nimport { z } from 'zod';\nimport { Tool } from '../../managers';\nimport { ITool } from './tool.ts.backup';\nimport { ToolSchemaWrapper, ToolContextOptions } from './toolSchemaWrapper';\nimport { Logger } from '../../logger';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { GraphState } from '../agents/workflows/types';\n\n/**\n * Interface for search options\n */\nexport interface SearchToolOptions {\n  /** Maximum number of results to return */\n  limit?: number;\n  /** Whether to include metadata in results */\n  includeMetadata?: boolean;\n  /** Additional filters for the search */\n  filters?: Record<string, unknown>;\n}\n\n/**\n * Enhanced wrapper for search tools with context awareness\n */\nexport class SearchTool extends ToolSchemaWrapper {\n  private _defaultOptions: SearchToolOptions;\n\n  /**\n   * Schema for input validation and transformation\n   */\n  override schema = z.union([\n    // Simple string query\n    z.string().describe('The search query string.'),\n    \n    // Or an object with query and options\n    z.object({\n      query: z.string().describe('The search query string.'),\n      options: z.object({\n        limit: z.number().int().positive().optional()\n          .describe('Maximum number of results to return'),\n        includeMetadata: z.boolean().optional()\n          .describe('Whether to include metadata in results'),\n        filters: z.record(z.any()).optional()\n          .describe('Additional filters for the search')\n      }).optional()\n    })\n  ]).transform((input) => {\n    // Normalize input to always return a query string and options\n    if (typeof input === 'string') {\n      return { query: input, options: {} };\n    }\n    return { query: input.query, options: input.options || {} };\n  });\n\n  constructor(\n    searchTool: Tool | ITool, \n    name?: string, \n    description?: string,\n    defaultOptions: SearchToolOptions = {},\n    initialContext?: AgentContext\n  ) {\n    super(\n      searchTool, \n      name || searchTool.name || 'search',\n      description || 'Performs a general search based on a query with contextual enhancement.',\n      initialContext\n    );\n    \n    // Initialize default options\n    this._defaultOptions = {\n      limit: 10,\n      includeMetadata: true,\n      ...defaultOptions\n    };\n    \n    if (!('invoke' in searchTool || 'call' in searchTool || 'execute' in searchTool)) {\n      Logger.instance.warn(\n        `SearchTool wrapper created for tool '${this.name}' ` +\n        `which lacks standard execution methods (invoke, call, execute).`\n      );\n    }\n    \n    // Bind methods to maintain proper 'this' context\n    this._enhanceWithContext = this._enhanceWithContext.bind(this);\n    this._processSearchResult = this._processSearchResult.bind(this);\n  }\n\n  /**\n   * Internal method to enhance search options with context information\n   */\n  private _enhanceWithContext(\n    baseOptions: SearchToolOptions,\n    context?: AgentContext\n  ): SearchToolOptions {\n    const enhancedOptions = { ...baseOptions };\n    \n    // If we have a context, we can enhance the options\n    if (context) {\n      // Example: Add user-specific filters from context\n      if (context.userId) {\n        enhancedOptions.filters = {\n          ...enhancedOptions.filters,\n          userId: context.userId\n        };\n      }\n      \n      // Example: Adjust limit based on context\n      if (context.isMobile) {\n        enhancedOptions.limit = Math.min(enhancedOptions.limit || 10, 5);\n      }\n    }\n    \n    return enhancedOptions;\n  }\n\n  /**\n   * Process and normalize the search result into a valid GraphState\n   */\n  private async _processSearchResult(\n    result: unknown,\n    originalQuery: string,\n    options: SearchToolOptions\n  ): Promise<GraphState> {\n    try {\n      // Default state with query and empty results\n      const baseState: Partial<GraphState> = {\n        query: originalQuery,\n        results: [],\n        metadata: {\n          searchOptions: options,\n          timestamp: new Date().toISOString()\n        }\n      };\n\n      // Handle different result formats\n      if (Array.isArray(result)) {\n        // If it's an array, assume it's a list of search results\n        baseState.results = result.map((item, index) => ({\n          content: typeof item === 'string' ? item : JSON.stringify(item),\n          metadata: {},\n          score: 1.0 - (index * 0.01) // Simple fallback scoring\n        }));\n      } else if (result && typeof result === 'object' && 'results' in result) {\n        // Handle result objects with a results property\n        const resultObj = result as { results: unknown[]; [key: string]: unknown };\n        baseState.results = Array.isArray(resultObj.results)\n          ? resultObj.results.map((item, index) => ({\n              content: typeof item === 'string' ? item : JSON.stringify(item),\n              metadata: {},\n              score: 1.0 - (index * 0.01)\n            }))\n          : [];\n        \n        // Copy other properties to metadata\n        Object.entries(resultObj).forEach(([key, value]) => {\n          if (key !== 'results') {\n            baseState.metadata = baseState.metadata || {};\n            baseState.metadata[key] = value;\n          }\n        });\n      } else if (result !== undefined && result !== null) {\n        // Handle single result or other result types\n        baseState.results = [{\n          content: typeof result === 'string' ? result : JSON.stringify(result),\n          metadata: {},\n          score: 1.0\n        }];\n      }\n\n      // Apply limit\n      if (options.limit) {\n        baseState.results = baseState.results.slice(0, options.limit);\n      }\n\n      return baseState as GraphState;\n    } catch (error) {\n      Logger.instance.error('Error processing search result:', error);\n      throw new Error(`Failed to process search results: ${error.message}`);\n    }\n  }\n\n  /**\n   * Execute the search with the given input\n   */\n  protected async _call(input: { query: string; options: SearchToolOptions }): Promise<GraphState> {\n    const { query, options: userOptions } = input;\n    \n    try {\n      if (!query) {\n        throw new Error('Query is required for search');\n      }\n\n      // Merge default options with user-provided options\n      const baseOptions: SearchToolOptions = {\n        ...this._defaultOptions,\n        ...userOptions\n      };\n\n      // Enhance options with context if available\n      const enhancedOptions = this._enhanceWithContext(baseOptions, this._context);\n      \n      Logger.instance.debug(`Executing search with query: \"${query}\"`);\n      \n      // Execute the underlying tool\n      let result;\n      if ('invoke' in this._baseTool) {\n        result = await this._baseTool.invoke({ \n          query, \n          options: enhancedOptions \n        });\n      } else if ('call' in this._baseTool) {\n        result = await this._baseTool.call({ \n          query, \n          options: enhancedOptions \n        });\n      } else if ('execute' in this._baseTool) {\n        result = await this._baseTool.execute({ \n          query, \n          options: enhancedOptions \n        });\n      } else {\n        throw new Error('No valid execution method found on base tool');\n      }\n\n      // Process and normalize the result\n      return this._processSearchResult(result, query, enhancedOptions);\n    } catch (error) {\n      Logger.instance.error('Error in SearchTool._call:', error);\n      return {\n        query,\n        results: [],\n        error: error.message,\n        metadata: {\n          error: true,\n          errorMessage: error.message,\n          timestamp: new Date().toISOString()\n        }\n      };\n    }\n  }\n}\n\nexport default SearchTool;\n"]}