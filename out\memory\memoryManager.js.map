{"version": 3, "file": "memoryManager.js", "sourceRoot": "", "sources": ["../../src/memory/memoryManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,sCAAmC;AACnC,sCAAsC;AAEtC,2DAAgE;AAEhE,qEAAkE;AAClE,yDAA6D;AAC7D,+BAAoC;AAEpC,uEAAoE;AAoBpE;;GAEG;AACH,KAAK,UAAU,sBAAsB;IACnC,MAAM,EAAE,mBAAmB,EAAE,GAAG,wDAAa,gBAAgB,GAAC,CAAC;IAC/D,OAAO,mBAAmB,CAAC,WAAW,EAA0B,CAAC;AACnE,CAAC;AAwCD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,aAAa;IACxB,gDAAgD;IACxC,OAAO,CAAsC;IAErD,gEAAgE;IACxD,cAAc,CAA8B;IAEpD,6DAA6D;IACrD,mBAAmB,CAAmC;IAE9D,0DAA0D;IAClD,kBAAkB,CAAiC;IAE3D,2DAA2D;IACnD,mBAAmB,CAAkC;IAE7D,6DAA6D;IACrD,WAAW,GAAG,KAAK,CAAC;IAE5B,oDAAoD;IAC5C,kBAAkB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;IAE7D;;;OAGG;IACM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;IAE3D;;;OAGG;IACI,4BAA4B,CAAC,QAAoB;QACtD,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACK,kBAAkB,GAAG,IAAI,GAAG,EAA4B,CAAC;IAEjE;;;OAGG;IACK,eAAe,GAA2B,EAAE,CAAC;IAErD;;;OAGG;IACK,gBAAgB,GAAG,IAAI,CAAC;IAEhC;;;OAGG;IACK,iBAAiB,GAAG,IAAI,GAAG,EAA8D,CAAC;IAElG;;;;OAIG;IACc,iBAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;IAEhE;;;;OAIG;IACc,kBAAkB,GAAG,GAAG,CAAC;IAE1C;;;;OAIG;IACK,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC;IAErC;;;OAGG;IACI,MAAM,CAAC,gBAAgB;QAC5B,OAAO,aAAa,CAAC,aAAa,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,gBAAgB,CAAC,OAAgB;QAC7C,aAAa,CAAC,aAAa,GAAG,OAAO,CAAC;QACtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,KAAK,CAAC,UAAU,CAAC,OAAgC;QACtD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YAEvB,6BAA6B;YAC7B,MAAM,aAAa,GAAG,IAAA,kBAAS,EAAU,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAEjE,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBAClD,OAAO;YACT,CAAC;YAED,oDAAoD;YACpD,MAAM,YAAY,GAAG,IAAA,kBAAS,EAAS,eAAe,EAAE,SAAS,CAAC,CAAC;YAEnE,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC;oBACH,qCAAqC;oBACrC,IAAI,CAAC,cAAc,GAAG,qCAAqB,CAAC;oBAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAE9C,yCAAyC;oBACzC,IAAI,CAAC;wBACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,IAAI,CAAC,cAAuC,CAAC,CAAC;wBAC/F,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;wBAC3C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;oBAC3D,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,sDAAsD;wBACtD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mEAAmE,EAAE,UAAU,CAAC,CAAC;oBACxG,CAAC;oBAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBAC9D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,qEAAqE;oBACrE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oFAAoF,EAAE,KAAK,CAAC,CAAC;oBAElH,qDAAqD;oBACrD,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;oBAChC,IAAI,CAAC,mBAAmB,GAAG,MAAM,sBAAsB,EAAE,CAAC;oBAC1D,sFAAsF;oBACtF,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;oBACnD,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAEnD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,yCAAyC;gBACzC,IAAI,CAAC,mBAAmB,GAAG,MAAM,sBAAsB,EAAE,CAAC;gBAC1D,sFAAsF;gBACtF,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBACnD,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAEnD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YAClE,CAAC;YAED,mCAAmC;YACnC,IAAI,CAAC;gBACH,gFAAgF;gBAChF,2CAA2C;gBAC3C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;YACtG,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YAC7E,CAAC;YAED,0BAA0B;YAC1B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,IAAI,mBAAmB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC9C,IAAI,CAAC,cAAmF,CAAC,iBAAiB,CAAC,GAAG,EAAE;wBAC/G,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;oBACjC,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,GAAG,EAAE;oBAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBACjC,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACI,KAAK,CAAC,SAAS,CAAC,cAA8D;QACnF,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,gDAAgD;YAChD,MAAM,UAAU,GAAG,OAAO,cAAc,KAAK,QAAQ;gBACnD,CAAC,CAAC;oBACA,OAAO,EAAE,cAAc;oBACvB,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAsB;wBAC9B,IAAI,EAAE,UAAwB;qBAC/B;iBACF;gBACD,CAAC,CAAC,cAAc,CAAC;YAEnB,kDAAkD;YAClD,MAAM,WAAW,GAAgB;gBAC/B,GAAG,UAAU;gBACb,EAAE,EAAE,IAAA,SAAM,GAAE,EAAE,2CAA2C;gBACzD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,yBAAyB;YACzB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC1D,CAAC;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACpC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YACjD,CAAC;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACpC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,KAAK,CAAC,SAAS,CAAC,EAAU;QAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACjD,CAAC;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACpC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,OAAuD;QAC3F,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACpC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,YAAY,CAAC,EAAU;QAClC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACpD,CAAC;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACpC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,aAAa;QACxB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;YAC5C,CAAC;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACpC,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;YACjD,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACI,KAAK,CAAC,cAAc,CAAC,OAA4B;QACtD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,OAAO,GAAkB,EAAE,CAAC;YAEhC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC9D,CAAC;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACpC,kDAAkD;gBAClD,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACxF,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,sCAAsC;YACtC,IAAI,IAAA,kBAAS,EAAU,gCAAgC,EAAE,IAAI,CAAC,EAAE,CAAC;gBAC/D,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,OAAsB,EAAE,KAAa;QAClE,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAE3D,sDAAsD;YACtD,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC3C,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,KAAK,EAAE,gBAAgB,CAAC;aACtE,CAAC,CAAC,CAAC;YAEJ,mCAAmC;YACnC,OAAO,aAAa;iBACjB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;iBACjC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,KAAkB,EAAE,KAAa,EAAE,OAAoE;QACtI,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,8CAA8C;QAC9C,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAEjD,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,KAAK,IAAI,EAAE,CAAC;QAEnD,wBAAwB;QACxB,IAAI,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YACxF,KAAK,IAAI,EAAE,CAAC,CAAC,kCAAkC;QACjD,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxE,KAAK,IAAI,CAAC,CAAC,CAAC,qCAAqC;QACnD,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACnF,KAAK,IAAI,CAAC,CAAC,CAAC,iCAAiC;QAC/C,CAAC;QAED,kDAAkD;QAClD,MAAM,iBAAiB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACrG,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,qCAAqC;QAElF,qBAAqB;QACrB,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;YAAE,KAAK,IAAI,CAAC,CAAC;QAClD,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,MAAM;YAAE,KAAK,IAAI,CAAC,CAAC;QAE/C,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACG,0BAA0B;QAChC,IAAI,CAAC;YACH,MAAM,OAAO,GAAgE,EAAE,CAAC;YAEhF,iDAAiD;YACjD,IAAI,CAAC;gBACH,oDAAoD;gBACpD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBACpD,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO,CAAC,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBACrD,OAAO,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;oBAEpD,uBAAuB;oBACvB,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACvF,IAAI,eAAe,EAAE,CAAC;wBACpB,OAAO,CAAC,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC;oBACzC,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,kDAAkD;gBAClD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;YAC3E,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,qBAAqB,CAAC,KAAa,EAAE,UAAwC,EAAE;QAC1F,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACzE,CAAC;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACpC,kDAAkD;gBAClD,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACpF,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,6CAA6C;YAC7C,IAAI,IAAA,kBAAS,EAAS,eAAe,EAAE,SAAS,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3F,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;YACrF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,QAAgB;QACrC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,uCAAuC;YACvC,IAAI,IAAA,kBAAS,EAAS,eAAe,EAAE,SAAS,CAAC,KAAK,SAAS,EAAE,CAAC;gBAChE,OAAO,MAAM,kCAAmB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CACzB,UAAkB,EAClB,kBAA4B,CAAC,kEAAkE,CAAC,EAChG,kBAA4B,CAAC,oBAAoB,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;QAE7F,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,uCAAuC;YACvC,IAAI,IAAA,kBAAS,EAAS,eAAe,EAAE,SAAS,CAAC,KAAK,SAAS,EAAE,CAAC;gBAChE,OAAO,MAAM,kCAAmB,CAAC,cAAc,CAAC,UAAU,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;YAChG,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,iBAAiB;QACtB,IAAI,IAAI,CAAC,cAAc,IAAI,mBAAmB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtE,OAAQ,IAAI,CAAC,cAAyE,CAAC,iBAAiB,EAAE,CAAC;QAC7G,CAAC;aAAM,CAAC;YACN,wCAAwC;YACxC,OAAO;gBACL,gBAAgB;gBAChB,OAAO,EAAE,IAAA,kBAAS,EAAU,gBAAgB,EAAE,IAAI,CAAC;gBACnD,MAAM,EAAE,IAAA,kBAAS,EAAkC,eAAe,EAAE,SAAS,CAAC;gBAC9E,WAAW,EAAE,IAAA,kBAAS,EAAS,oBAAoB,EAAE,IAAI,CAAC;gBAC1D,kBAAkB,EAAE,IAAA,kBAAS,EAAS,2BAA2B,EAAE,GAAG,CAAC;gBACvE,mBAAmB,EAAE,IAAA,kBAAS,EAAS,4BAA4B,EAAE,EAAE,CAAC;gBACxE,kBAAkB,EAAE,IAAA,kBAAS,EAAS,2BAA2B,EAAE,GAAG,CAAC;gBACvE,iBAAiB,EAAE,IAAA,kBAAS,EAAS,0BAA0B,EAAE,CAAC,CAAC;gBACnE,uBAAuB,EAAE,IAAA,kBAAS,EAAS,gCAAgC,EAAE,GAAG,CAAC;gBAEjF,kCAAkC;gBAClC,mBAAmB,EAAE,IAAA,kBAAS,EAAU,4BAA4B,EAAE,IAAI,CAAC;gBAC3E,wBAAwB,EAAE,IAAA,kBAAS,EAAU,iCAAiC,EAAE,IAAI,CAAC;gBACrF,yBAAyB,EAAE,IAAA,kBAAS,EAAU,kCAAkC,EAAE,IAAI,CAAC;gBACvF,yBAAyB,EAAE,IAAA,kBAAS,EAAU,kCAAkC,EAAE,IAAI,CAAC;gBAEvF,wBAAwB;gBACxB,WAAW,EAAE,IAAA,kBAAS,EAA4D,oBAAoB,EAAE,QAAQ,CAAC;gBACjH,mBAAmB,EAAE;oBACnB,MAAM,EAAE;wBACN,SAAS,EAAE,IAAA,kBAAS,EAAS,qCAAqC,EAAE,mBAAmB,CAAC;wBACxF,cAAc,EAAE,IAAA,kBAAS,EAAS,0CAA0C,EAAE,kBAAkB,CAAC;qBAClG;oBACD,QAAQ,EAAE;wBACR,MAAM,EAAE,IAAA,kBAAS,EAAS,oCAAoC,EAAE,EAAE,CAAC;wBACnE,WAAW,EAAE,IAAA,kBAAS,EAAS,yCAAyC,EAAE,EAAE,CAAC;wBAC7E,SAAS,EAAE,IAAA,kBAAS,EAAS,uCAAuC,EAAE,kBAAkB,CAAC;qBAC1F;iBACF;gBAED,oBAAoB;gBACpB,QAAQ,EAAE,IAAA,kBAAS,EAAwD,iBAAiB,EAAE,QAAQ,CAAC;gBACvG,gBAAgB,EAAE;oBAChB,MAAM,EAAE;wBACN,QAAQ,EAAE,IAAA,kBAAS,EAAS,iCAAiC,EAAE,sBAAsB,CAAC;qBACvF;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,IAAA,kBAAS,EAAS,4BAA4B,EAAE,WAAW,CAAC;wBAClE,IAAI,EAAE,IAAA,kBAAS,EAAS,4BAA4B,EAAE,IAAI,CAAC;wBAC3D,IAAI,EAAE,IAAA,kBAAS,EAAS,4BAA4B,EAAE,MAAM,CAAC;wBAC7D,QAAQ,EAAE,IAAA,kBAAS,EAAS,gCAAgC,EAAE,EAAE,CAAC;wBACjE,QAAQ,EAAE,IAAA,kBAAS,EAAS,gCAAgC,EAAE,SAAS,CAAC;wBACxE,KAAK,EAAE,IAAA,kBAAS,EAAS,6BAA6B,EAAE,UAAU,CAAC;qBACpE;oBACD,QAAQ,EAAE;wBACR,gBAAgB,EAAE,IAAA,kBAAS,EAAS,2CAA2C,EAAE,EAAE,CAAC;wBACpF,MAAM,EAAE,IAAA,kBAAS,EAAS,iCAAiC,EAAE,SAAS,CAAC;qBACxE;oBACD,OAAO,EAAE;wBACP,gBAAgB,EAAE,IAAA,kBAAS,EAAS,0CAA0C,EAAE,EAAE,CAAC;wBACnF,QAAQ,EAAE,IAAA,kBAAS,EAAS,kCAAkC,EAAE,SAAS,CAAC;wBAC1E,UAAU,EAAE,IAAA,kBAAS,EAAS,oCAAoC,EAAE,UAAU,CAAC;qBAChF;oBACD,KAAK,EAAE;wBACL,GAAG,EAAE,IAAA,kBAAS,EAAS,2BAA2B,EAAE,EAAE,CAAC;wBACvD,SAAS,EAAE,IAAA,kBAAS,EAAS,iCAAiC,EAAE,UAAU,CAAC;qBAC5E;iBACF;gBAED,2BAA2B;gBAC3B,YAAY,EAAE;oBACZ,SAAS,EAAE,IAAA,kBAAS,EAAS,+BAA+B,EAAE,IAAI,CAAC;oBACnE,YAAY,EAAE,IAAA,kBAAS,EAAS,kCAAkC,EAAE,GAAG,CAAC;oBACxE,gBAAgB,EAAE,IAAA,kBAAS,EAAS,sCAAsC,EAAE,GAAG,CAAC;iBACjF;gBAED,uBAAuB;gBACvB,SAAS,EAAE,IAAA,kBAAS,EAAS,kBAAkB,EAAE,IAAI,CAAC;gBACtD,WAAW,EAAE,IAAA,kBAAS,EAAS,oBAAoB,EAAE,MAAM,CAAC,EAAE,YAAY;gBAC1E,SAAS,EAAE,IAAA,kBAAS,EAAS,kBAAkB,EAAE,EAAE,CAAC;gBACpD,uBAAuB,EAAE,IAAA,kBAAS,EAAS,gCAAgC,EAAE,EAAE,CAAC;aACjF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,oBAAoB,CAAC,QAAiC;QACjE,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,IAAI,sBAAsB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzE,MAAM,YAAY,GAAI,IAAI,CAAC,cAAuI,CAAC,oBAAoB,CAAC;gBACxL,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC7D,OAAO,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC,+CAA+C;YACjG,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;gBAC5E,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,uBAAuB,CAAC,eAAgC;QACnE,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC9B,sFAAsF;gBACtF,MAAM,oBAAoB,GAA0B;oBAClD,yBAAyB;oBACzB,SAAS,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;oBAC7D,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBACrC,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE;oBACrC,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC3C,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE;oBACzC,cAAc,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;oBACzD,qBAAqB,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC;oBAErF,oCAAoC;oBACpC,iBAAiB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBACjD,oBAAoB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;oBAEvE,6BAA6B;oBAC7B,oBAAoB,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,EAAE,CACjF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;oBACrF,yBAAyB,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;oBAC/E,kBAAkB,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,EAAE,CACvD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;oBAC3D,oBAAoB,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAC9C,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;oBAElD,2BAA2B;oBAC3B,kBAAkB,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC;oBAC/E,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE;oBAEvD,sBAAsB;oBACtB,cAAc,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;oBACzD,SAAS,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;oBACjD,cAAc,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,eAAe,EAAE,EAAE,CAC/D,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,eAAe,EAAE,eAAe,CAAC;oBAEnE,iBAAiB;oBACjB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;iBAC1C,CAAC;gBAEF,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,CAChD,oBAAoB,EACpB,IAAI,CAAC,mBAAmB,IAAI,MAAM,sBAAsB,EAAE,EAC1D,eAAe,CAChB,CAAC;gBAEF,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC;gBAC5C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,mBAAmB,CAC9B,OAAe,EACf,OAAqH,EAAE,gBAAgB;IACvI,aAA0D,QAAQ,EAClE,WAAoC,EAAE,CAAC,wCAAwC;;QAE/E,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACpG,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;YAC9F,6BAA6B;YAC7B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;gBACvC,OAAO;gBACP,QAAQ,EAAE;oBACR,MAAM,EAAE,UAA0B,EAAE,oBAAoB;oBACxD,IAAI,EAAE,UAAwB,EAAE,oBAAoB;oBACpD,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;oBACxD,GAAG,QAAQ;iBACZ;aACF,CAAC,CAAC;YACH,OAAO,WAAW,CAAC,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,0BAA0B,CAAC,KAAa;QACnD,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,gDAAgD;YAChD,MAAM,eAAe,GAAG;gBACtB,KAAK,EAAE,KAAK;gBACZ,OAAO,EAAE,EAAmE,EAAE,gBAAgB;gBAC9F,UAAU,EAAE,EAAE;aACf,CAAC;YACF,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,0BAA0B,CAAC,eAAe,CAAC,CAAC;QACpF,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;YAC9F,6BAA6B;YAC7B,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,0BAA0B,CAAC,OAAe;QACrD,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAC5E,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;YACnG,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,aAAa;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;SAEK;IACE,kBAAkB;QACvB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;YACzF,OAAO;gBACL,aAAa,EAAE,CAAC;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,EAAE,CAAC;gBACtB,kBAAkB,EAAE,CAAC;gBACrB,gBAAgB,EAAE,CAAC;gBACnB,oBAAoB,EAAE,CAAC;gBACvB,oBAAoB,EAAE,EAAE;gBACxB,cAAc,EAAE;oBACd,UAAU,EAAE,EAAE;oBACd,gBAAgB,EAAE,EAAE;oBACpB,cAAc,EAAE,EAAE;iBACnB;gBACD,oBAAoB,EAAE;oBACpB,oBAAoB,EAAE,EAAE;oBACxB,cAAc,EAAE,EAAE;oBAClB,sBAAsB,EAAE,CAAC;iBAC1B;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACI,KAAK,CAAC,oBAAoB,CAC/B,QAAgB,EAChB,WAAmB,EACnB,SAAiB,EACjB,cAA0C,MAAM,EAChD,SAAkB;QAElB,IAAI,CAAC;YACH,0BAA0B;YAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,4BAA4B;YAC5B,MAAM,YAAY,GAAqB;gBACrC,EAAE,EAAE,UAAU,QAAQ,IAAI,IAAA,SAAM,GAAE,EAAE;gBACpC,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,WAAW;gBACrB,UAAU,EAAE,CAAC,SAAS,CAAC;gBACvB,WAAW,EAAE,WAAW;gBACxB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;gBACpB,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS;gBACzD,OAAO,EAAE,eAAe,WAAW,OAAO,SAAS,EAAE;gBACrD,MAAM,EAAE,MAAM;aACf,CAAC;YAEF,oDAAoD;YACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE;oBACrC,OAAO,EAAE,SAAS;oBAClB,cAAc,EAAE,IAAI,GAAG,EAAE;oBACzB,SAAS,EAAE,EAAE;iBACd,CAAC,CAAC;YACL,CAAC;YAED,oCAAoC;YACpC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACjE,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACvB,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,sCAAsC,CAAC,CAAC;gBACpG,OAAO,KAAK,CAAC;YACf,CAAC;YACD,iBAAiB,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAE7D,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC3D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,QAAQ,gBAAgB,WAAW,OAAO,SAAS,SAAS,WAAW,SAAS,CAAC,CAAC;YAEjH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACpE,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;;;;;;;;OAWG;IACI,yBAAyB,CAAC,OAAe;QAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,8BAA8B;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,aAAa,GAAuB,EAAE,CAAC;QAE7C,KAAK,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;YAClE,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;gBAC5D,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,wBAAwB;gBACxB,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,WAAmB,EAAE,SAAiB;QACtF,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC3D,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,OAAO,GAAG,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5D,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC5D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,QAAQ,eAAe,SAAS,EAAE,CAAC,CAAC;YACpF,CAAC;YAED,qDAAqD;YACrD,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,KAAa,EAAE,KAAK,GAAG,EAAE;QAC1E,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,uCAAuC;YAChE,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;QAED,+CAA+C;QAC/C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QAExB,oBAAoB;QACpB,MAAM,QAAQ,GAAG,UAAU,OAAO,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAE/D,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,qDAAqD;QACrD,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAErE,MAAM,OAAO,GAAkD,EAAE,CAAC;QAElE,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,gCAAgC,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YACpF,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACd,OAAO,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;gBAEhC,2EAA2E;gBAC3E,IAAI,OAAO,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBAC7C,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAE1C,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAE3D,oBAAoB;QACpB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEvC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,kBAAkB,CAAC,OAAe,EAAE,KAAK,GAAG,GAAG;QACpD,OAAO,IAAI,CAAC,eAAe;aACxB,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,OAAO,CAAC;aACtC,KAAK,CAAC,CAAC,KAAK,CAAC;aACb,OAAO,EAAE,CAAC;IACf,CAAC;IAED;;SAEK;IACE,oBAAoB;QAMzB,MAAM,UAAU,GAAG,IAAI,GAAG,EAA8E,CAAC;QACzG,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvC,aAAa,EAAE,CAAC;YAChB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,cAAc,EAAE,CAAC;YACnB,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACjC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAC9E,CAAC;YAED,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1C,IAAI,KAAK,EAAE,CAAC;gBACV,QAAQ,GAAG,CAAC,MAAM,EAAE,CAAC;oBACnB,KAAK,MAAM;wBAAE,KAAK,CAAC,KAAK,EAAE,CAAC;wBAAC,MAAM;oBAClC,KAAK,OAAO;wBAAE,KAAK,CAAC,MAAM,EAAE,CAAC;wBAAC,MAAM;oBACpC,KAAK,OAAO;wBAAE,KAAK,CAAC,MAAM,EAAE,CAAC;wBAAC,MAAM;oBACpC,KAAK,QAAQ;wBAAE,KAAK,CAAC,OAAO,EAAE,CAAC;wBAAC,MAAM;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,aAAa;YACb,kBAAkB;YAClB,cAAc;YACd,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,eAAe,CAAC,OAAe,EAAE,QAAgB,EAAE,MAA6C,EAAE,OAAgB,EAAE,KAAc;QACxI,MAAM,QAAQ,GAAoB;YAChC,EAAE,EAAE,OAAO,IAAA,SAAM,GAAE,EAAE;YACrB,OAAO;YACP,QAAQ;YACR,MAAM;YACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO;YACP,KAAK;SACN,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEpC,0BAA0B;QAC1B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5E,CAAC;QAED,iCAAiC;QACjC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;gBACvC,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,gCAAgC,CAAC,MAAwB,EAAE,UAAoB,EAAE,UAAkB;QACzG,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACzD,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAElD,mEAAmE;QACnE,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,KAAK,IAAI,CAAC,CAAC;gBACX,kCAAkC;gBAClC,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC;oBACpD,KAAK,IAAI,CAAC,CAAC;gBACb,CAAC;YACH,CAAC;YAED,yBAAyB;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,KAAK,IAAI,CAAC,CAAC;YACb,CAAC;QACH,CAAC;QAED,yDAAyD;QACzD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;QAC/C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;YACnC,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC9B,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,KAAK,IAAI,CAAC,CAAC,CAAC,0BAA0B;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACtC,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,iEAAiE;QACjE,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACrE,IAAI,UAAU,GAAG,EAAE,EAAE,CAAC;YACpB,KAAK,IAAI,CAAC,CAAC,CAAC,oCAAoC;QAClD,CAAC;aAAM,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC,CAAC,SAAS;YACtC,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,0DAA0D;QAC1D,IAAI,MAAM,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;YACnC,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;aAAM,IAAI,MAAM,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACG,cAAc,CAAC,GAAW;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEzB,mCAAmC;QACnC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3D,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,MAAM,CAAC,OAAO,CAAC;IACxB,CAAC;IAEO,cAAc,CAAC,GAAW,EAAE,OAA2B;QAC7D,+BAA+B;QAC/B,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3D,sBAAsB;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YAC5D,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC9B,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,oBAAoB;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,qBAAqB;QACrB,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACpD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC;;AA3zCH,sCA4zCC;AAED,4BAA4B;AACf,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\r\nimport { Logger } from '../logger';\r\nimport { getConfig } from '../config';\r\nimport type { IMemoryProvider, MemoryEntry, MemorySearchOptions, MemorySettings, MemorySource, MemoryType, SharedMemoryItem, MemoryAccessLog as TypesMemoryAccessLog, MemoryMetadata } from './types';\r\nimport { codessaMemoryProvider } from './codessa/codessaMemory';\r\nimport type { CodessaMemoryProvider } from './codessa/codessaMemory';\r\nimport { CodessaGraphMemory } from './codessa/codessaGraphMemory';\r\nimport { FileChunkingService } from './codessa/fileChunking';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport type { IMemoryManagerAdapter, TemporalMemoryEntry } from './quantum/quantumMemorySystem';\r\nimport { QuantumMemorySystem } from './quantum/quantumMemorySystem';\r\nimport type { SupervisorAgent } from '../agents/agentTypes/supervisorAgent';\r\n\r\n/**\r\n * Vector Memory Manager interface for type safety\r\n */\r\ninterface IVectorMemoryManager {\r\n  setMemoryOperations(ops: unknown): void;\r\n  initialize(context?: vscode.ExtensionContext): Promise<void>;\r\n  addMemory(entry: MemoryEntry): Promise<MemoryEntry>;\r\n  getMemories(): Promise<MemoryEntry[]>;\r\n  getMemory(id: string): Promise<MemoryEntry | undefined>;\r\n  updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined>;\r\n  deleteMemory(id: string): Promise<boolean>;\r\n  clearMemories(): Promise<void>;\r\n  searchMemories(query: string, limit?: number): Promise<MemoryEntry[]>;\r\n  searchSimilarMemories(query: string, limit?: number): Promise<MemoryEntry[]>;\r\n  onMemoriesChanged(callback: () => void): void;\r\n}\r\n\r\n/**\r\n * Dynamic import helper to break circular dependency\r\n */\r\nasync function getVectorMemoryManager(): Promise<IVectorMemoryManager> {\r\n  const { VectorMemoryManager } = await import('./vectorMemory');\r\n  return VectorMemoryManager.getInstance() as IVectorMemoryManager;\r\n}\r\n\r\n/**\r\n * Represents the memory state for an agent, including shared memories and access logs.\r\n * This interface is used to manage and track memory sharing between different agents.\r\n * \r\n * @interface CrossAgentMemory\r\n * @property {string} agentId - The unique identifier of the agent\r\n * @property {Map<string, SharedMemoryItem>} sharedMemories - Map of memory IDs to shared memory items\r\n * @property {MemoryAccessLog[]} accessLog - Array of access log entries for auditing\r\n */\r\nexport interface CrossAgentMemory {\r\n  agentId: string;\r\n  sharedMemories: Map<string, SharedMemoryItem>;\r\n  accessLog: MemoryAccessLog[];\r\n}\r\n\r\n// Use MemoryAccessLog from types.ts\r\nexport type MemoryAccessLog = TypesMemoryAccessLog;\r\n\r\nexport interface MemoryAnalytics {\r\n  totalMemories: number;\r\n  temporalMemories: number;\r\n  collaborativeShares: number;\r\n  predictiveInsights: number;\r\n  memoryGrowthRate: number;\r\n  averageRetrievalTime: number;\r\n  mostAccessedMemories: string[];\r\n  memoryPatterns: {\r\n    commonTags: string[];\r\n    frequentContexts: string[];\r\n    peakUsageTimes: number[];\r\n  };\r\n  userBehaviorInsights: {\r\n    preferredMemoryTypes: string[];\r\n    searchPatterns: string[];\r\n    collaborationFrequency: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Memory Manager - Core class for managing agent memories and cross-agent memory sharing.\r\n * \r\n * @class MemoryManager\r\n * @implements {IMemoryManagerAdapter}\r\n * @description Centralized memory management system that handles storage, retrieval, and sharing of memories\r\n * across different agents. Supports multiple memory backends and provides advanced features like\r\n * cross-agent memory sharing, access control, and memory analytics.\r\n * \r\n * @example\r\n * ```typescript\r\n * const memoryManager = new MemoryManager();\r\n * await memoryManager.initialize(context);\r\n * \r\n * // Add a new memory\r\n * const memory = await memoryManager.addMemory('Important information');\r\n * \r\n * // Search memories\r\n * const results = await memoryManager.searchMemories({ query: 'important' });\r\n * ```\r\n * Central manager for all memory operations\r\n */\r\nexport class MemoryManager implements IMemoryManagerAdapter {\r\n  /** Extension context for VS Code integration */\r\n  private context: vscode.ExtensionContext | undefined;\r\n  \r\n  /** Active memory provider instance for storage and retrieval */\r\n  private memoryProvider: IMemoryProvider | undefined;\r\n  \r\n  /** Vector memory manager for semantic search capabilities */\r\n  private vectorMemoryManager: IVectorMemoryManager | undefined;\r\n  \r\n  /** Graph-based memory system for complex relationships */\r\n  private codessaGraphMemory: CodessaGraphMemory | undefined;\r\n  \r\n  /** Quantum memory system for advanced memory operations */\r\n  private quantumMemorySystem: QuantumMemorySystem | undefined;\r\n  \r\n  /** Tracks whether the memory manager has been initialized */\r\n  private initialized = false;\r\n  \r\n  /** Event emitter for memory change notifications */\r\n  private _onMemoriesChanged = new vscode.EventEmitter<void>();\r\n  \r\n  /**\r\n   * Event that fires when memories are added, updated, or removed\r\n   * @event\r\n   */\r\n  readonly onMemoriesChanged = this._onMemoriesChanged.event;\r\n\r\n  /**\r\n   * Register a listener for memory changes (for IMemoryOperations interface)\r\n   * This method is required by the IMemoryOperations interface\r\n   */\r\n  public registerMemoryChangeListener(listener: () => void): void {\r\n    this._onMemoriesChanged.event(listener);\r\n  }\r\n\r\n  /**\r\n   * Map of agent IDs to their cross-agent memory states\r\n   * @private\r\n   */\r\n  private crossAgentMemories = new Map<string, CrossAgentMemory>();\r\n  \r\n  /**\r\n   * Global access log for auditing all memory operations\r\n   * @private\r\n   */\r\n  private globalAccessLog: TypesMemoryAccessLog[] = [];\r\n  \r\n  /**\r\n   * Maximum number of entries to keep in the access log\r\n   * @private\r\n   */\r\n  private maxAccessLogSize = 1000;\r\n\r\n  /**\r\n   * Cache for search results to improve performance\r\n   * @private\r\n   */\r\n  private memorySearchCache = new Map<string, { results: SharedMemoryItem[]; timestamp: number }>();\r\n  \r\n  /**\r\n   * Time in milliseconds after which search cache entries expire\r\n   * @private\r\n   * @readonly\r\n   */\r\n  private readonly searchCacheExpiry = 2 * 60 * 1000; // 2 minutes\r\n  \r\n  /**\r\n   * Maximum number of search results to cache\r\n   * @private\r\n   * @readonly\r\n   */\r\n  private readonly maxSearchCacheSize = 100;\r\n\r\n  /**\r\n   * Global flag indicating if memory features are enabled\r\n   * @static\r\n   * @private\r\n   */\r\n  private static memoryEnabled = false;\r\n\r\n  /**\r\n   * Checks if the memory system is globally enabled\r\n   * @returns {boolean} True if memory features are enabled, false otherwise\r\n   */\r\n  public static getMemoryEnabled(): boolean {\r\n    return MemoryManager.memoryEnabled;\r\n  }\r\n\r\n  /**\r\n   * Enables or disables the memory system globally\r\n   * @param {boolean} enabled - Whether to enable or disable the memory system\r\n   */\r\n  public static setMemoryEnabled(enabled: boolean): void {\r\n    MemoryManager.memoryEnabled = enabled;\r\n    Logger.instance.info(`Memory ${enabled ? 'enabled' : 'disabled'}`);\r\n  }\r\n\r\n  /**\r\n   * Initializes the memory manager with the provided VS Code extension context.\r\n   * This method sets up the memory provider, vector memory manager, and other components.\r\n   * \r\n   * @param {vscode.ExtensionContext} context - The VS Code extension context\r\n   * @returns {Promise<void>} A promise that resolves when initialization is complete\r\n   * @throws {Error} If initialization fails\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * await memoryManager.initialize(vscodeContext);\r\n   * ```\r\n   */\r\n  public async initialize(context: vscode.ExtensionContext): Promise<void> {\r\n    if (this.initialized) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      this.context = context;\r\n\r\n      // Check if memory is enabled\r\n      const memoryEnabled = getConfig<boolean>('memory.enabled', true);\r\n\r\n      if (!memoryEnabled) {\r\n        Logger.instance.info('Memory system is disabled');\r\n        return;\r\n      }\r\n\r\n      // Initialize memory provider based on configuration\r\n      const memorySystem = getConfig<string>('memory.system', 'codessa');\r\n\r\n      if (memorySystem === 'codessa') {\r\n        try {\r\n          // Initialize Codessa memory provider\r\n          this.memoryProvider = codessaMemoryProvider;\r\n          await this.memoryProvider.initialize(context);\r\n\r\n          // Try to initialize Codessa Graph memory\r\n          try {\r\n            this.codessaGraphMemory = new CodessaGraphMemory(this.memoryProvider as CodessaMemoryProvider);\r\n            await this.codessaGraphMemory.initialize();\r\n            Logger.instance.info('Codessa Graph memory initialized');\r\n          } catch (graphError) {\r\n            // Log but don't fail if graph memory can't initialize\r\n            Logger.instance.warn('Failed to initialize Codessa Graph memory, continuing without it:', graphError);\r\n          }\r\n\r\n          Logger.instance.info('Codessa memory provider initialized');\r\n        } catch (error) {\r\n          // If Codessa memory provider fails, fall back to basic vector memory\r\n          Logger.instance.warn('Failed to initialize Codessa memory provider, falling back to basic vector memory:', error);\r\n\r\n          // Initialize basic vector memory manager as fallback\r\n          this.memoryProvider = undefined;\r\n          this.vectorMemoryManager = await getVectorMemoryManager();\r\n          // Set up the memory operations for vector memory manager to break circular dependency\r\n          this.vectorMemoryManager.setMemoryOperations(this);\r\n          await this.vectorMemoryManager.initialize(context);\r\n\r\n          Logger.instance.info('Basic vector memory manager initialized as fallback');\r\n        }\r\n      } else {\r\n        // Initialize basic vector memory manager\r\n        this.vectorMemoryManager = await getVectorMemoryManager();\r\n        // Set up the memory operations for vector memory manager to break circular dependency\r\n        this.vectorMemoryManager.setMemoryOperations(this);\r\n        await this.vectorMemoryManager.initialize(context);\r\n\r\n        Logger.instance.info('Basic vector memory manager initialized');\r\n      }\r\n\r\n      // Initialize Quantum Memory System\r\n      try {\r\n        // We need SupervisorAgent for quantum memory, but it might not be available yet\r\n        // So we'll initialize it later when needed\r\n        Logger.instance.info('Quantum Memory System will be initialized when SupervisorAgent is available');\r\n      } catch (error) {\r\n        Logger.instance.warn('Failed to initialize Quantum Memory System:', error);\r\n      }\r\n\r\n      // Register event handlers\r\n      if (this.memoryProvider) {\r\n        if ('onMemoriesChanged' in this.memoryProvider) {\r\n          (this.memoryProvider as unknown as { onMemoriesChanged: (listener: () => void) => void }).onMemoriesChanged(() => {\r\n            this._onMemoriesChanged.fire();\r\n          });\r\n        }\r\n      }\r\n\r\n      if (this.vectorMemoryManager) {\r\n        this.vectorMemoryManager.onMemoriesChanged(() => {\r\n          this._onMemoriesChanged.fire();\r\n        });\r\n      }\r\n\r\n      this.initialized = true;\r\n      Logger.instance.info('Memory manager initialized successfully');\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to initialize memory manager:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adds a new memory or updates an existing one if an ID is provided.\r\n   * \r\n   * @param {string | Omit<MemoryEntry, 'id' | 'timestamp'>} contentOrEntry - The content as a string or a partial memory entry\r\n   * @returns {Promise<MemoryEntry>} The created or updated memory entry\r\n   * @throws {Error} If the memory provider is not initialized or the operation fails\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * // Add a simple text memory\r\n   * const memory = await memoryManager.addMemory('Important note');\r\n   * \r\n   * // Add a memory with metadata\r\n   * const memoryWithMeta = await memoryManager.addMemory({\r\n   *   content: 'Project requirements',\r\n   *   metadata: {\r\n   *     type: 'requirements',\r\n   *     priority: 'high'\r\n   *   }\r\n   * });\r\n   * ```\r\n   */\r\n  public async addMemory(contentOrEntry: string | Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      // Create memory entry with proper type handling\r\n      const baseMemory = typeof contentOrEntry === 'string'\r\n        ? {\r\n          content: contentOrEntry,\r\n          metadata: {\r\n            source: 'user' as MemorySource,\r\n            type: 'semantic' as MemoryType\r\n          }\r\n        }\r\n        : contentOrEntry;\r\n      \r\n      // Create final memory entry with ID and timestamp\r\n      const memoryEntry: MemoryEntry = {\r\n        ...baseMemory,\r\n        id: uuidv4(), // Always generate a new ID for memoryEntry\r\n        timestamp: Date.now()\r\n      };\r\n\r\n      // Add to memory provider\r\n      if (this.memoryProvider) {\r\n        return await this.memoryProvider.addMemory(memoryEntry);\r\n      } else if (this.vectorMemoryManager) {\r\n        return await this.vectorMemoryManager.addMemory(memoryEntry);\r\n      } else {\r\n        throw new Error('No memory provider initialized');\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to add memory:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retrieves all stored memories.\r\n   * \r\n   * @returns {Promise<MemoryEntry[]>} An array of all memory entries\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * const allMemories = await memoryManager.getMemories();\r\n   * console.log(`Total memories: ${allMemories.length}`);\r\n   * ```\r\n   */\r\n  public async getMemories(): Promise<MemoryEntry[]> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      if (this.memoryProvider) {\r\n        return await this.memoryProvider.getMemories();\r\n      } else if (this.vectorMemoryManager) {\r\n        return await this.vectorMemoryManager.getMemories();\r\n      } else {\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to get memories:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retrieves a memory entry by its unique identifier.\r\n   * \r\n   * @param {string} id - The unique identifier of the memory to retrieve\r\n   * @returns {Promise<MemoryEntry | undefined>} The memory entry if found, undefined otherwise\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * const memory = await memoryManager.getMemory('memory-123');\r\n   * if (memory) {\r\n   *   console.log('Found memory:', memory.content);\r\n   * }\r\n   * ```\r\n   */\r\n  public async getMemory(id: string): Promise<MemoryEntry | undefined> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      if (this.memoryProvider) {\r\n        return await this.memoryProvider.getMemory(id);\r\n      } else if (this.vectorMemoryManager) {\r\n        return await this.vectorMemoryManager.getMemory(id);\r\n      } else {\r\n        return undefined;\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error(`Failed to get memory ${id}:`, error);\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates an existing memory with new content or metadata.\r\n   * \r\n   * @param {string} id - The ID of the memory to update\r\n   * @param {Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>} updates - The fields to update\r\n   * @returns {Promise<MemoryEntry | undefined>} The updated memory entry, or undefined if not found\r\n   * @throws {Error} If the update operation fails\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * // Update memory content\r\n   * const updated = await memoryManager.updateMemory('memory-123', {\r\n   *   content: 'Updated content',\r\n   *   metadata: { updatedAt: Date.now() }\r\n   * });\r\n   * ```\r\n   */\r\n  public async updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      if (this.memoryProvider) {\r\n        return await this.memoryProvider.updateMemory(id, updates);\r\n      } else if (this.vectorMemoryManager) {\r\n        return await this.vectorMemoryManager.updateMemory(id, updates);\r\n      } else {\r\n        return undefined;\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error(`Failed to update memory ${id}:`, error);\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deletes a memory by its ID.\r\n   * \r\n   * @param {string} id - The ID of the memory to delete\r\n   * @returns {Promise<boolean>} True if the memory was deleted, false if not found\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * const wasDeleted = await memoryManager.deleteMemory('memory-123');\r\n   * console.log(wasDeleted ? 'Memory deleted' : 'Memory not found');\r\n   * ```\r\n   */\r\n  public async deleteMemory(id: string): Promise<boolean> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      if (this.memoryProvider) {\r\n        return await this.memoryProvider.deleteMemory(id);\r\n      } else if (this.vectorMemoryManager) {\r\n        return await this.vectorMemoryManager.deleteMemory(id);\r\n      } else {\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error(`Failed to delete memory ${id}:`, error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes all stored memories.\r\n   * \r\n   * @returns {Promise<void>} A promise that resolves when all memories are cleared\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * // Clear all memories (use with caution)\r\n   * await memoryManager.clearMemories();\r\n   * ```\r\n   */\r\n  public async clearMemories(): Promise<void> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      if (this.memoryProvider) {\r\n        await this.memoryProvider.clearMemories();\r\n      } else if (this.vectorMemoryManager) {\r\n        await this.vectorMemoryManager.clearMemories();\r\n      }\r\n\r\n      Logger.instance.info('All memories cleared');\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to clear memories:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Searches through stored memories using the provided query and options.\r\n   * Supports contextual ranking and filtering of results.\r\n   * \r\n   * @param {MemorySearchOptions} options - Search configuration and query parameters\r\n   * @returns {Promise<MemoryEntry[]>} An array of matching memory entries, sorted by relevance\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * // Basic search\r\n   * const results = await memoryManager.searchMemories({\r\n   *   query: 'important note',\r\n   *   limit: 10,\r\n   *   minRelevance: 0.5\r\n   * });\r\n   * \r\n   * // Search with filters\r\n   * const filteredResults = await memoryManager.searchMemories({\r\n   *   query: 'error',\r\n   *   filters: {\r\n   *     type: 'error',\r\n   *     timestamp: { $gt: Date.now() - 86400000 } // Last 24 hours\r\n   *   }\r\n   * });\r\n   * ```\r\n   */\r\n  public async searchMemories(options: MemorySearchOptions): Promise<MemoryEntry[]> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      let results: MemoryEntry[] = [];\r\n\r\n      if (this.memoryProvider) {\r\n        results = await this.memoryProvider.searchMemories(options);\r\n      } else if (this.vectorMemoryManager) {\r\n        // Convert options to vector memory manager format\r\n        results = await this.vectorMemoryManager.searchMemories(options.query, options.limit);\r\n      } else {\r\n        return [];\r\n      }\r\n\r\n      // Apply contextual ranking if enabled\r\n      if (getConfig<boolean>('phase4.enableContextualRanking', true)) {\r\n        results = this.applyContextualRanking(results, options.query);\r\n      }\r\n\r\n      return results;\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to search memories:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Apply contextual ranking to search results\r\n     */\r\n  private applyContextualRanking(results: MemoryEntry[], query: string): MemoryEntry[] {\r\n    try {\r\n      // Get current workspace context\r\n      const workspaceContext = this.getCurrentWorkspaceContext();\r\n\r\n      // Score results based on relevance to current context\r\n      const scoredResults = results.map(result => ({\r\n        entry: result,\r\n        score: this.calculateContextualScore(result, query, workspaceContext)\r\n      }));\r\n\r\n      // Sort by score and return entries\r\n      return scoredResults\r\n        .sort((a, b) => b.score - a.score)\r\n        .map(item => item.entry);\r\n    } catch (error) {\r\n      Logger.instance.warn('Failed to apply contextual ranking:', error);\r\n      return results;\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Calculate contextual relevance score for search results\r\n     */\r\n  private calculateContextualScore(entry: MemoryEntry, query: string, context: {currentFile?: string; language?: string; project?: string}): number {\r\n    let score = 0;\r\n\r\n    // Base relevance score (simple text matching)\r\n    const queryLower = query.toLowerCase();\r\n    const contentLower = entry.content.toLowerCase();\r\n\r\n    if (contentLower.includes(queryLower)) score += 10;\r\n\r\n    // Context-based scoring\r\n    if (context.currentFile && entry.metadata.tags?.includes('file:' + context.currentFile)) {\r\n      score += 15; // High relevance for current file\r\n    }\r\n\r\n    if (context.language && entry.metadata.tags?.includes(context.language)) {\r\n      score += 8; // Medium relevance for same language\r\n    }\r\n\r\n    if (context.project && entry.metadata.tags?.includes('project:' + context.project)) {\r\n      score += 5; // Low relevance for same project\r\n    }\r\n\r\n    // Recency bonus (newer memories get slight boost)\r\n    const daysSinceCreation = (Date.now() - new Date(entry.timestamp).getTime()) / (1000 * 60 * 60 * 24);\r\n    score += Math.max(0, 5 - daysSinceCreation); // Up to 5 points for recent memories\r\n\r\n    // Type-based scoring\r\n    if (entry.metadata.type === 'insight') score += 3;\r\n    if (entry.metadata.type === 'code') score += 2;\r\n\r\n    return score;\r\n  }\r\n\r\n  /**\r\n     * Get current workspace context for memory operations\r\n     */\r\n  private getCurrentWorkspaceContext(): {currentFile?: string; language?: string; project?: string} {\r\n    try {\r\n      const context: {currentFile?: string; language?: string; project?: string} = {};\r\n\r\n      // Get active editor information from VS Code API\r\n      try {\r\n        // Use VS Code API directly instead of window object\r\n        const activeEditor = vscode.window.activeTextEditor;\r\n        if (activeEditor) {\r\n          context.currentFile = activeEditor.document.fileName;\r\n          context.language = activeEditor.document.languageId;\r\n\r\n          // Get workspace folder\r\n          const workspaceFolder = vscode.workspace.getWorkspaceFolder(activeEditor.document.uri);\r\n          if (workspaceFolder) {\r\n            context.project = workspaceFolder.name;\r\n          }\r\n        }\r\n      } catch (vscodeError) {\r\n        // VS Code API not available, return empty context\r\n        Logger.instance.debug('VS Code API not available for workspace context');\r\n      }\r\n\r\n      return context;\r\n    } catch (error) {\r\n      Logger.instance.warn('Failed to get workspace context:', error);\r\n      return {};\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Search memories by semantic similarity\r\n     */\r\n  public async searchSimilarMemories(query: string, options: Partial<MemorySearchOptions> = {}): Promise<MemoryEntry[]> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      if (this.memoryProvider) {\r\n        return await this.memoryProvider.searchSimilarMemories(query, options);\r\n      } else if (this.vectorMemoryManager) {\r\n        // Convert options to vector memory manager format\r\n        return await this.vectorMemoryManager.searchSimilarMemories(query, options.limit);\r\n      } else {\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to search similar memories:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Process a message with Codessa Graph memory\r\n     */\r\n  public async processMessage(message: string): Promise<string> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      // Check if Codessa Graph memory is available\r\n      if (getConfig<string>('memory.system', 'codessa') === 'codessa' && this.codessaGraphMemory) {\r\n        return await this.codessaGraphMemory.processMessage(message);\r\n      } else {\r\n        throw new Error('Codessa Graph memory not available');\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to process message with Codessa Graph memory:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Chunk a file and store in memory\r\n     */\r\n  public async chunkFile(filePath: string): Promise<MemoryEntry[]> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      // Check if Codessa memory is available\r\n      if (getConfig<string>('memory.system', 'codessa') === 'codessa') {\r\n        return await FileChunkingService.chunkFile(filePath);\r\n      } else {\r\n        throw new Error('Codessa memory not available for file chunking');\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error(`Failed to chunk file ${filePath}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Chunk a workspace folder and store in memory\r\n     */\r\n  public async chunkWorkspace(\r\n    folderPath: string,\r\n    includePatterns: string[] = ['**/*.{js,ts,jsx,tsx,py,java,c,cpp,cs,go,rb,php,html,css,md,json}'],\r\n    excludePatterns: string[] = ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.git/**']\r\n  ): Promise<MemoryEntry[]> {\r\n    if (!this.initialized && this.context) {\r\n      await this.initialize(this.context);\r\n    }\r\n\r\n    try {\r\n      // Check if Codessa memory is available\r\n      if (getConfig<string>('memory.system', 'codessa') === 'codessa') {\r\n        return await FileChunkingService.chunkWorkspace(folderPath, includePatterns, excludePatterns);\r\n      } else {\r\n        throw new Error('Codessa memory not available for workspace chunking');\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error(`Failed to chunk workspace ${folderPath}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Get comprehensive memory settings - OPTIMIZED\r\n     */\r\n  public getMemorySettings(): MemorySettings {\r\n    if (this.memoryProvider && 'getMemorySettings' in this.memoryProvider) {\r\n      return (this.memoryProvider as unknown as { getMemorySettings: () => MemorySettings }).getMemorySettings();\r\n    } else {\r\n      // Return comprehensive default settings\r\n      return {\r\n        // Core settings\r\n        enabled: getConfig<boolean>('memory.enabled', true),\r\n        system: getConfig<'basic' | 'codessa' | 'quantum'>('memory.system', 'codessa'),\r\n        maxMemories: getConfig<number>('memory.maxMemories', 1000),\r\n        maxMemoriesPerFile: getConfig<number>('phase4.maxMemoriesPerFile', 100),\r\n        memoryRetentionDays: getConfig<number>('phase4.memoryRetentionDays', 30),\r\n        relevanceThreshold: getConfig<number>('memory.relevanceThreshold', 0.7),\r\n        contextWindowSize: getConfig<number>('memory.contextWindowSize', 5),\r\n        conversationHistorySize: getConfig<number>('memory.conversationHistorySize', 100),\r\n\r\n        // Advanced memory system settings\r\n        enableQuantumMemory: getConfig<boolean>('phase4.enableQuantumMemory', true),\r\n        enablePredictiveInsights: getConfig<boolean>('phase4.enablePredictiveInsights', true),\r\n        enableMemoryVisualization: getConfig<boolean>('phase4.enableMemoryVisualization', true),\r\n        enableCollaborativeMemory: getConfig<boolean>('phase4.enableCollaborativeMemory', true),\r\n\r\n        // Vector store settings\r\n        vectorStore: getConfig<'memory' | 'chroma' | 'pinecone' | 'weaviate' | 'hnswlib'>('memory.vectorStore', 'chroma'),\r\n        vectorStoreSettings: {\r\n          chroma: {\r\n            directory: getConfig<string>('memory.vectorStore.chroma.directory', './.codessa/chroma'),\r\n            collectionName: getConfig<string>('memory.vectorStore.chroma.collectionName', 'codessa_memories')\r\n          },\r\n          pinecone: {\r\n            apiKey: getConfig<string>('memory.vectorStore.pinecone.apiKey', ''),\r\n            environment: getConfig<string>('memory.vectorStore.pinecone.environment', ''),\r\n            indexName: getConfig<string>('memory.vectorStore.pinecone.indexName', 'codessa-memories')\r\n          }\r\n        },\r\n\r\n        // Database settings\r\n        database: getConfig<'sqlite' | 'mysql' | 'postgres' | 'mongodb' | 'redis'>('memory.database', 'sqlite'),\r\n        databaseSettings: {\r\n          sqlite: {\r\n            filename: getConfig<string>('memory.database.sqlite.filename', './.codessa/memory.db')\r\n          },\r\n          mysql: {\r\n            host: getConfig<string>('memory.database.mysql.host', 'localhost'),\r\n            port: getConfig<number>('memory.database.mysql.port', 3306),\r\n            user: getConfig<string>('memory.database.mysql.user', 'root'),\r\n            password: getConfig<string>('memory.database.mysql.password', ''),\r\n            database: getConfig<string>('memory.database.mysql.database', 'codessa'),\r\n            table: getConfig<string>('memory.database.mysql.table', 'memories')\r\n          },\r\n          postgres: {\r\n            connectionString: getConfig<string>('memory.database.postgres.connectionString', ''),\r\n            schema: getConfig<string>('memory.database.postgres.schema', 'codessa')\r\n          },\r\n          mongodb: {\r\n            connectionString: getConfig<string>('memory.database.mongodb.connectionString', ''),\r\n            database: getConfig<string>('memory.database.mongodb.database', 'codessa'),\r\n            collection: getConfig<string>('memory.database.mongodb.collection', 'memories')\r\n          },\r\n          redis: {\r\n            url: getConfig<string>('memory.database.redis.url', ''),\r\n            keyPrefix: getConfig<string>('memory.database.redis.keyPrefix', 'codessa:')\r\n          }\r\n        },\r\n\r\n        // File processing settings\r\n        fileChunking: {\r\n          chunkSize: getConfig<number>('memory.fileChunking.chunkSize', 1000),\r\n          chunkOverlap: getConfig<number>('memory.fileChunking.chunkOverlap', 200),\r\n          maxChunksPerFile: getConfig<number>('memory.fileChunking.maxChunksPerFile', 100)\r\n        },\r\n\r\n        // Performance settings\r\n        cacheSize: getConfig<number>('memory.cacheSize', 1000),\r\n        cacheExpiry: getConfig<number>('memory.cacheExpiry', 300000), // 5 minutes\r\n        batchSize: getConfig<number>('memory.batchSize', 50),\r\n        maxConcurrentOperations: getConfig<number>('memory.maxConcurrentOperations', 10)\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Update memory settings\r\n     */\r\n  public async updateMemorySettings(settings: Partial<MemorySettings>): Promise<boolean> {\r\n    try {\r\n      if (this.memoryProvider && 'updateMemorySettings' in this.memoryProvider) {\r\n        const updateMethod = (this.memoryProvider as unknown as { updateMemorySettings: (settings: Partial<MemorySettings>) => boolean | void | Promise<boolean | void> }).updateMemorySettings;\r\n        const result = await Promise.resolve(updateMethod(settings));\r\n        return result === true || result === undefined; // Handle both boolean returns and void returns\r\n      } else {\r\n        Logger.instance.error('Memory provider does not support updating settings');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to update memory settings:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Initialize Quantum Memory System with SupervisorAgent\r\n     */\r\n  public async initializeQuantumMemory(supervisorAgent: SupervisorAgent): Promise<void> {\r\n    try {\r\n      if (!this.quantumMemorySystem) {\r\n        // Create an adapter that delegates all IMemoryManagerAdapter methods to this instance\r\n        const memoryManagerAdapter: IMemoryManagerAdapter = {\r\n          // Core memory operations\r\n          addMemory: (contentOrEntry) => this.addMemory(contentOrEntry),\r\n          getMemory: (id) => this.getMemory(id),\r\n          getMemories: () => this.getMemories(),\r\n          deleteMemory: (id) => this.deleteMemory(id),\r\n          clearMemories: () => this.clearMemories(),\r\n          searchMemories: (options) => this.searchMemories(options),\r\n          searchSimilarMemories: (query, options) => this.searchSimilarMemories(query, options),\r\n          \r\n          // Memory settings and configuration\r\n          getMemorySettings: () => this.getMemorySettings(),\r\n          updateMemorySettings: (settings) => this.updateMemorySettings(settings),\r\n          \r\n          // Cross-agent memory sharing\r\n          shareMemoryWithAgent: (memoryId, fromAgentId, toAgentId, accessLevel, expiresIn) => \r\n            this.shareMemoryWithAgent(memoryId, fromAgentId, toAgentId, accessLevel, expiresIn),\r\n          getSharedMemoriesForAgent: (agentId) => this.getSharedMemoriesForAgent(agentId),\r\n          revokeSharedMemory: (memoryId, fromAgentId, toAgentId) => \r\n            this.revokeSharedMemory(memoryId, fromAgentId, toAgentId),\r\n          searchSharedMemories: (agentId, query, limit) => \r\n            this.searchSharedMemories(agentId, query, limit),\r\n            \r\n          // Access logging and stats\r\n          getMemoryAccessLog: (agentId, limit) => this.getMemoryAccessLog(agentId, limit),\r\n          getMemoryAccessStats: () => this.getMemoryAccessStats(),\r\n          \r\n          // Advanced operations\r\n          processMessage: (message) => this.processMessage(message),\r\n          chunkFile: (filePath) => this.chunkFile(filePath),\r\n          chunkWorkspace: (folderPath, includePatterns, excludePatterns) => \r\n            this.chunkWorkspace(folderPath, includePatterns, excludePatterns),\r\n            \r\n          // Event handling\r\n          onMemoriesChanged: this.onMemoriesChanged\r\n        };\r\n\r\n        this.quantumMemorySystem = new QuantumMemorySystem(\r\n          memoryManagerAdapter,\r\n          this.vectorMemoryManager ?? await getVectorMemoryManager(),\r\n          supervisorAgent\r\n        );\r\n\r\n        await this.quantumMemorySystem.initialize();\r\n        Logger.instance.info('Quantum Memory System initialized successfully');\r\n      }\r\n    } catch (error) {\r\n      Logger.instance.error('Failed to initialize Quantum Memory System:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Get Quantum Memory System instance\r\n     */\r\n  public getQuantumMemorySystem(): QuantumMemorySystem | undefined {\r\n    return this.quantumMemorySystem;\r\n  }\r\n\r\n  /**\r\n     * Store temporal memory with versioning\r\n     */\r\n  public async storeTemporalMemory(\r\n    content: string,\r\n    context: { filePath: string; lineNumber?: number; functionName?: string; className?: string; projectContext: string }, // Typed context\r\n    changeType: 'create' | 'update' | 'delete' | 'refactor' = 'create',\r\n    metadata: Partial<MemoryMetadata> = {} // Use MemoryMetadata type from types.ts\r\n  ): Promise<string | undefined> {\r\n    if (this.quantumMemorySystem) {\r\n      return await this.quantumMemorySystem.storeTemporalMemory(content, context, changeType, metadata);\r\n    } else {\r\n      Logger.instance.warn('Quantum Memory System not initialized, falling back to regular memory');\r\n      // Fallback to regular memory\r\n      const memoryEntry = await this.addMemory({\r\n        content,\r\n        metadata: {\r\n          source: 'temporal' as MemorySource, // Use imported type\r\n          type: 'semantic' as MemoryType, // Use imported type\r\n          tags: ['temporal', changeType, ...(metadata.tags ?? [])],\r\n          ...metadata\r\n        }\r\n      });\r\n      return memoryEntry.id;\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Retrieve contextual memories based on query\r\n     */\r\n  public async retrieveContextualMemories(query: string): Promise<(MemoryEntry | TemporalMemoryEntry)[]> { // Allow both MemoryEntry and TemporalMemoryEntry\r\n    if (this.quantumMemorySystem) {\r\n      // Convert string query to ContextualMemoryQuery\r\n      const contextualQuery = {\r\n        query: query,\r\n        context: {} as { currentFile?: string; language?: string; project?: string }, // Typed context\r\n        maxResults: 10\r\n      };\r\n      return await this.quantumMemorySystem.retrieveContextualMemories(contextualQuery);\r\n    } else {\r\n      Logger.instance.warn('Quantum Memory System not initialized, falling back to regular search');\r\n      // Fallback to regular search\r\n      return await this.searchMemories({ query: query, limit: 10 });\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Generate predictive insights from memory\r\n     */\r\n  public async generatePredictiveInsights(context: string): Promise<unknown[]> { // Keeping 'any[]' as return type might be dynamic from QuantumMemorySystem\r\n    if (this.quantumMemorySystem) {\r\n      return await this.quantumMemorySystem.generatePredictiveInsights(context);\r\n    } else {\r\n      Logger.instance.warn('Quantum Memory System not initialized, cannot generate predictive insights');\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if the memory manager is initialized\r\n   */\r\n  public isInitialized(): boolean {\r\n    return this.initialized;\r\n  }\r\n\r\n  /**\r\n     * Get memory usage analytics and statistics\r\n     */\r\n  public getMemoryAnalytics(): MemoryAnalytics { // Keeping 'any' here as the return type is complex and might be dynamic from QuantumMemorySystem\r\n    if (this.quantumMemorySystem) {\r\n      return this.quantumMemorySystem.getAnalytics();\r\n    } else {\r\n      Logger.instance.warn('Quantum Memory System not initialized, returning basic analytics');\r\n      return {\r\n        totalMemories: 0,\r\n        temporalMemories: 0,\r\n        collaborativeShares: 0,\r\n        predictiveInsights: 0,\r\n        memoryGrowthRate: 0,\r\n        averageRetrievalTime: 0,\r\n        mostAccessedMemories: [],\r\n        memoryPatterns: {\r\n          commonTags: [],\r\n          frequentContexts: [],\r\n          peakUsageTimes: []\r\n        },\r\n        userBehaviorInsights: {\r\n          preferredMemoryTypes: [],\r\n          searchPatterns: [],\r\n          collaborationFrequency: 0\r\n        }\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Shares a memory with another agent, granting specified access permissions.\r\n   * \r\n   * @param {string} memoryId - The ID of the memory to share\r\n   * @param {string} fromAgentId - The ID of the agent sharing the memory\r\n   * @param {string} toAgentId - The ID of the agent to share with\r\n   * @param {'read' | 'write' | 'admin'} [accessLevel='read'] - The level of access to grant\r\n   * @param {number} [expiresIn] - Optional expiration time in milliseconds\r\n   * @returns {Promise<boolean>} True if sharing was successful, false otherwise\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * // Share a memory with read access\r\n   * await memoryManager.shareMemoryWithAgent(\r\n   *   'memory-123',\r\n   *   'agent-1',\r\n   *   'agent-2',\r\n   *   'read'\r\n   * );\r\n   * \r\n   * // Share with write access that expires in 1 hour\r\n   * await memoryManager.shareMemoryWithAgent(\r\n   *   'memory-123',\r\n   *   'agent-1',\r\n   *   'agent-2',\r\n   *   'write',\r\n   *   60 * 60 * 1000 // 1 hour in milliseconds\r\n   * );\r\n   * ```\r\n   */\r\n  public async shareMemoryWithAgent(\r\n    memoryId: string,\r\n    fromAgentId: string,\r\n    toAgentId: string,\r\n    accessLevel: 'read' | 'write' | 'admin' = 'read',\r\n    expiresIn?: number\r\n  ): Promise<boolean> {\r\n    try {\r\n      // Get the memory to share\r\n      const memory = await this.getMemory(memoryId);\r\n      if (!memory) {\r\n        this.logMemoryAccess(fromAgentId, memoryId, 'share', false);\r\n        return false;\r\n      }\r\n\r\n      // Create shared memory item\r\n      const sharedMemory: SharedMemoryItem = {\r\n        id: `shared_${memoryId}_${uuidv4()}`,\r\n        memoryId: memoryId,\r\n        sharedBy: fromAgentId,\r\n        sharedWith: [toAgentId],\r\n        permissions: accessLevel,\r\n        sharedAt: Date.now(),\r\n        expiresAt: expiresIn ? Date.now() + expiresIn : undefined,\r\n        context: `Shared from ${fromAgentId} to ${toAgentId}`,\r\n        memory: memory\r\n      };\r\n\r\n      // Ensure cross-agent memory exists for target agent\r\n      if (!this.crossAgentMemories.has(toAgentId)) {\r\n        this.crossAgentMemories.set(toAgentId, {\r\n          agentId: toAgentId,\r\n          sharedMemories: new Map(),\r\n          accessLog: []\r\n        });\r\n      }\r\n\r\n      // Add shared memory to target agent\r\n      const targetAgentMemory = this.crossAgentMemories.get(toAgentId);\r\n      if (!targetAgentMemory) {\r\n        this.logMemoryAccess(fromAgentId, memoryId, 'share', false, 'Failed to get or create agent memory');\r\n        return false;\r\n      }\r\n      targetAgentMemory.sharedMemories.set(memoryId, sharedMemory);\r\n\r\n      this.logMemoryAccess(fromAgentId, memoryId, 'share', true);\r\n      Logger.instance.info(`Memory ${memoryId} shared from ${fromAgentId} to ${toAgentId} with ${accessLevel} access`);\r\n\r\n      return true;\r\n    } catch (error) {\r\n      Logger.instance.error(`Failed to share memory ${memoryId}:`, error);\r\n      this.logMemoryAccess(fromAgentId, memoryId, 'share', false);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retrieves all memories that have been shared with a specific agent.\r\n   * \r\n   * @param {string} agentId - The ID of the agent\r\n   * @returns {SharedMemoryItem[]} An array of shared memory items\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * const sharedMemories = memoryManager.getSharedMemoriesForAgent('agent-2');\r\n   * console.log(`Agent has ${sharedMemories.length} shared memories`);\r\n   * ```\r\n   */\r\n  public getSharedMemoriesForAgent(agentId: string): SharedMemoryItem[] {\r\n    const agentMemory = this.crossAgentMemories.get(agentId);\r\n    if (!agentMemory) {\r\n      return [];\r\n    }\r\n\r\n    // Filter out expired memories\r\n    const now = Date.now();\r\n    const validMemories: SharedMemoryItem[] = [];\r\n\r\n    for (const [memoryId, sharedMemory] of agentMemory.sharedMemories) {\r\n      if (!sharedMemory.expiresAt || sharedMemory.expiresAt > now) {\r\n        validMemories.push(sharedMemory);\r\n      } else {\r\n        // Remove expired memory\r\n        agentMemory.sharedMemories.delete(memoryId);\r\n      }\r\n    }\r\n\r\n    return validMemories;\r\n  }\r\n\r\n  /**\r\n     * Revoke shared memory access between agents\r\n     */\r\n  public async revokeSharedMemory(memoryId: string, fromAgentId: string, toAgentId: string): Promise<boolean> {\r\n    try {\r\n      const agentMemory = this.crossAgentMemories.get(toAgentId);\r\n      if (!agentMemory) {\r\n        return false;\r\n      }\r\n\r\n      const removed = agentMemory.sharedMemories.delete(memoryId);\r\n      if (removed) {\r\n        this.logMemoryAccess(fromAgentId, memoryId, 'revoke', true);\r\n        Logger.instance.info(`Revoked shared memory ${memoryId} from agent ${toAgentId}`);\r\n      }\r\n\r\n      // Add a minimal async operation to justify the async\r\n      await Promise.resolve();\r\n      return removed;\r\n    } catch (error) {\r\n      Logger.instance.error(`Failed to revoke shared memory ${memoryId}:`, error);\r\n      this.logMemoryAccess(fromAgentId, memoryId, 'revoke', false);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Searches through memories shared with a specific agent.\r\n   * \r\n   * @param {string} agentId - The ID of the agent to search shared memories for\r\n   * @param {string} query - The search query\r\n   * @param {number} [limit=10] - Maximum number of results to return\r\n   * @returns {Promise<SharedMemoryItem[]>} An array of matching shared memory items\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * // Search through shared memories\r\n   * const results = await memoryManager.searchSharedMemories(\r\n   *   'agent-2',\r\n   *   'important project',\r\n   *   5 // Limit to 5 results\r\n   * );\r\n   * ```\r\n   */\r\n  public async searchSharedMemories(agentId: string, query: string, limit = 10): Promise<SharedMemoryItem[]> {\r\n    // Cleanup expired caches periodically\r\n    if (Math.random() < 0.1) { // 10% chance to cleanup on each search\r\n      this.cleanupExpiredCaches();\r\n    }\r\n\r\n    // Add minimal async operation to justify async\r\n    await Promise.resolve();\r\n\r\n    // Check cache first\r\n    const cacheKey = `search_${agentId}_${query}_${limit}`;\r\n    const cached = this.getSearchCache(cacheKey);\r\n    if (cached) {\r\n      return cached;\r\n    }\r\n\r\n    const sharedMemories = this.getSharedMemoriesForAgent(agentId);\r\n\r\n    if (sharedMemories.length === 0) {\r\n      return [];\r\n    }\r\n\r\n    // Optimized text-based search with early termination\r\n    const queryLower = query.toLowerCase();\r\n    const queryWords = queryLower.split(/\\s+/).filter(w => w.length > 2);\r\n\r\n    const matches: { memory: SharedMemoryItem; score: number }[] = [];\r\n\r\n    for (const memory of sharedMemories) {\r\n      const score = this.calculateOptimizedRelevanceScore(memory, queryWords, queryLower);\r\n      if (score > 0) {\r\n        matches.push({ memory, score });\r\n\r\n        // Early termination for performance if we have enough high-scoring matches\r\n        if (matches.length >= limit * 3 && score < 5) {\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Sort by relevance score (descending)\r\n    matches.sort((a, b) => b.score - a.score);\r\n\r\n    const results = matches.slice(0, limit).map(m => m.memory);\r\n\r\n    // Cache the results\r\n    this.setSearchCache(cacheKey, results);\r\n\r\n    return results;\r\n  }\r\n\r\n  /**\r\n   * Retrieves the access log for a specific agent, showing all memory operations.\r\n   * \r\n   * @param {string} agentId - The ID of the agent\r\n   * @param {number} [limit=100] - Maximum number of log entries to return\r\n   * @returns {MemoryAccessLog[]} An array of access log entries\r\n   * \r\n   * @example\r\n   * ```typescript\r\n   * // Get the 50 most recent access log entries\r\n   * const accessLog = memoryManager.getMemoryAccessLog('agent-1', 50);\r\n   * accessLog.forEach(entry => {\r\n   *   console.log(`${entry.action} ${entry.memoryId} - ${entry.success ? 'success' : 'failed'}`);\r\n   * });\r\n   * ```\r\n   */\r\n  public getMemoryAccessLog(agentId: string, limit = 100): MemoryAccessLog[] {\r\n    return this.globalAccessLog\r\n      .filter(log => log.agentId === agentId)\r\n      .slice(-limit)\r\n      .reverse();\r\n  }\r\n\r\n  /**\r\n     * Get global memory access statistics and metrics\r\n     */\r\n  public getMemoryAccessStats(): {\r\n    totalAccesses: number;\r\n    successfulAccesses: number;\r\n    failedAccesses: number;\r\n    agentStats: Map<string, { reads: number; writes: number; shares: number; revokes: number }>;\r\n    } {\r\n    const agentStats = new Map<string, { reads: number, writes: number, shares: number, revokes: number }>();\r\n    let totalAccesses = 0;\r\n    let successfulAccesses = 0;\r\n    let failedAccesses = 0;\r\n\r\n    for (const log of this.globalAccessLog) {\r\n      totalAccesses++;\r\n      if (log.success) {\r\n        successfulAccesses++;\r\n      } else {\r\n        failedAccesses++;\r\n      }\r\n\r\n      if (!agentStats.has(log.agentId)) {\r\n        agentStats.set(log.agentId, { reads: 0, writes: 0, shares: 0, revokes: 0 });\r\n      }\r\n\r\n      const stats = agentStats.get(log.agentId);\r\n      if (stats) {\r\n        switch (log.action) {\r\n          case 'read': stats.reads++; break;\r\n          case 'write': stats.writes++; break;\r\n          case 'share': stats.shares++; break;\r\n          case 'revoke': stats.revokes++; break;\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      totalAccesses,\r\n      successfulAccesses,\r\n      failedAccesses,\r\n      agentStats\r\n    };\r\n  }\r\n\r\n  /**\r\n     * Log memory access for security and auditing\r\n     */\r\n  private logMemoryAccess(agentId: string, memoryId: string, action: 'read' | 'write' | 'share' | 'revoke', success: boolean, error?: string): void {\r\n    const logEntry: MemoryAccessLog = {\r\n      id: `log_${uuidv4()}`,\r\n      agentId,\r\n      memoryId,\r\n      action,\r\n      timestamp: Date.now(),\r\n      success,\r\n      error\r\n    };\r\n\r\n    this.globalAccessLog.push(logEntry);\r\n\r\n    // Maintain log size limit\r\n    if (this.globalAccessLog.length > this.maxAccessLogSize) {\r\n      this.globalAccessLog = this.globalAccessLog.slice(-this.maxAccessLogSize);\r\n    }\r\n\r\n    // Also add to agent-specific log\r\n    const agentMemory = this.crossAgentMemories.get(agentId);\r\n    if (agentMemory) {\r\n      agentMemory.accessLog.push(logEntry);\r\n      if (agentMemory.accessLog.length > 100) {\r\n        agentMemory.accessLog = agentMemory.accessLog.slice(-100);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Calculate relevance score for shared memory search results\r\n     */\r\n  private calculateOptimizedRelevanceScore(memory: SharedMemoryItem, queryWords: string[], queryLower: string): number {\r\n    let score = 0;\r\n    const contentLower = memory.memory.content.toLowerCase();\r\n    const contextLower = memory.context.toLowerCase();\r\n\r\n    // Word-based matching in memory content (more accurate than regex)\r\n    for (const word of queryWords) {\r\n      if (contentLower.includes(word)) {\r\n        score += 2;\r\n        // Bonus for exact word boundaries\r\n        if (contentLower.match(new RegExp(`\\\\b${word}\\\\b`))) {\r\n          score += 1;\r\n        }\r\n      }\r\n\r\n      // Also search in context\r\n      if (contextLower.includes(word)) {\r\n        score += 1;\r\n      }\r\n    }\r\n\r\n    // Tag matches (high priority) - use memory.metadata.tags\r\n    const tags = memory.memory.metadata.tags ?? [];\r\n    for (const tag of tags) {\r\n      const tagLower = tag.toLowerCase();\r\n      for (const word of queryWords) {\r\n        if (tagLower.includes(word)) {\r\n          score += 5; // Tags are more important\r\n        }\r\n      }\r\n    }\r\n\r\n    // Exact phrase match bonus\r\n    if (contentLower.includes(queryLower)) {\r\n      score += 10;\r\n    }\r\n\r\n    // Recency bonus (optimized calculation) - use sharedAt timestamp\r\n    const ageInHours = (Date.now() - memory.sharedAt) / (1000 * 60 * 60);\r\n    if (ageInHours < 24) {\r\n      score += 5; // Recent memories are more relevant\r\n    } else if (ageInHours < 168) { // 1 week\r\n      score += 2;\r\n    }\r\n\r\n    // Permission level bonus (higher access = more important)\r\n    if (memory.permissions === 'admin') {\r\n      score += 3;\r\n    } else if (memory.permissions === 'write') {\r\n      score += 1;\r\n    }\r\n\r\n    return score;\r\n  }\r\n\r\n  /**\r\n     * Performance optimization: Search cache management\r\n     */\r\n  private getSearchCache(key: string): SharedMemoryItem[] | null {\r\n    const cached = this.memorySearchCache.get(key);\r\n    if (!cached) return null;\r\n\r\n    // Check if cache entry has expired\r\n    if (Date.now() - cached.timestamp > this.searchCacheExpiry) {\r\n      this.memorySearchCache.delete(key);\r\n      return null;\r\n    }\r\n\r\n    return cached.results;\r\n  }\r\n\r\n  private setSearchCache(key: string, results: SharedMemoryItem[]): void {\r\n    // Implement LRU cache behavior\r\n    if (this.memorySearchCache.size >= this.maxSearchCacheSize) {\r\n      // Remove oldest entry\r\n      const firstKey = this.memorySearchCache.keys().next().value;\r\n      if (firstKey) {\r\n        this.memorySearchCache.delete(firstKey);\r\n      }\r\n    }\r\n\r\n    this.memorySearchCache.set(key, {\r\n      results,\r\n      timestamp: Date.now()\r\n    });\r\n  }\r\n\r\n  /**\r\n     * Performance optimization: Cleanup expired caches\r\n     */\r\n  private cleanupExpiredCaches(): void {\r\n    const now = Date.now();\r\n\r\n    // Clean search cache\r\n    for (const [key, cached] of this.memorySearchCache.entries()) {\r\n      if (now - cached.timestamp > this.searchCacheExpiry) {\r\n        this.memorySearchCache.delete(key);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const memoryManager = new MemoryManager();\r\n"]}