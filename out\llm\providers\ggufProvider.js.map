{"version": 3, "file": "ggufProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/ggufProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AAC7B,uCAAyB;AACzB,uDAAoD;AAGpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;;;;;;GAOG;AACH,MAAa,YAAa,SAAQ,iCAAe;IACtC,UAAU,GAAG,MAAM,CAAC;IACpB,WAAW,GAAG,aAAa,CAAC;IAC5B,WAAW,GAAG,sCAAsC,CAAC;IACrD,OAAO,GAAG,wCAAwC,CAAC;IACnD,cAAc,GAAG,KAAK,CAAC;IACvB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,uBAAuB,CAAC;IAC1C,YAAY,GAAG,EAAE,CAAC;IAEnB,MAAM,GAAQ,IAAI,CAAC;IACnB,eAAe,CAAS;IACxB,YAAY,GAAG,EAAE,CAAC;IAClB,MAAM,GAA+B,IAAI,GAAG,EAAE,CAAC;IAEvD,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,0BAA0B;QAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAErE,iCAAiC;QACjC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBACxD,eAAM,CAAC,IAAI,CAAC,+BAA+B,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;YACrE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,KAAK,EAAE,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACnE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAChE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;QAEnD,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,MAAM,EAAE,8CAA8C;gBAC/D,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,sCAAsC;QACtC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAEpB,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAExC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBAC1C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;oBAEhD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE;wBACvB,EAAE,EAAE,OAAO;wBACX,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,YAAY,EAAE,KAAK,CAAC,KAAK;qBAC1B,CAAC,CAAC;oBAEH,eAAM,CAAC,IAAI,CAAC,qBAAqB,OAAO,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC/E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;SAEK;IACG,aAAa;QACnB,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,+CAA+C;QAC/C,IAAI,CAAC;YACH,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACxC,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACnD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC3B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC;oBACzD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,6DAA6D;QAC7D,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,0BAA0B,IAAI,EAAE,CAAC;QACpE,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvB,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;oBAClC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;4BAC3B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;wBACxC,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,GAAG,KAAK,KAAK,EAAE,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;SAEK;IACG,UAAU,CAAC,KAAa;QAC9B,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;YACjB,OAAO,GAAG,KAAK,IAAI,CAAC;QACtB,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YAC/B,OAAO,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3C,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YACtC,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,MAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,8BAA8B,EAAE,CAAC;QAChE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;YAEjE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC;YACtD,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,UAAU,OAAO,aAAa,EAAE,CAAC;YAChE,CAAC;YAED,qBAAqB;YACrB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,gCAAgC;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,sBAAsB,MAAM,CAAC,YAAY,gBAAgB,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,YAAY,CAAC;YACzB,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,kBAAkB;gBAClB,IAAI,eAAe,GAAG,EAAE,CAAC;gBAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAElC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;4BACV,MAAM,IAAI,cAAc,eAAe,aAAa,CAAC;wBACvD,CAAC;wBACD,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC;oBACpC,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,MAAM,CAAC;oBACrC,CAAC;gBACH,CAAC;gBAED,6BAA6B;gBAC7B,MAAM,IAAI,GAAG,eAAe,cAAc,CAAC;YAC7C,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,cAAc,CAAC;YAC3C,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;wBAC9C,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBACvG,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,SAAS,CAAC,IAAI;gBACrB,MAAM,EAAE,MAAM;gBACd,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACpC,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC;aACzC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE;gBAClE,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,iBAAiB;YACjB,OAAO;gBACL,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,EAAE,MAAM;gBACpB,KAAK,EAAE;oBACL,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;oBAC9D,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;iBACtF;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC5C,IAAI,YAAY,GAAG,0BAA0B,CAAC;YAE9C,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,mBAAmB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAC7G,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,yBAAyB;QACzB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAE3B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACpD,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,WAAW,EAAE,SAAS,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACnD,aAAa,EAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC;YACtD,WAAW,EAAE,cAAc;SAC5B,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,OAAe;QAC9C,8CAA8C;QAC9C,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7D,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtE,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,yBAAyB;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,eAAM,CAAC,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;gBAC5C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChC,eAAM,CAAC,KAAK,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;gBACtD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAE3D,wCAAwC;YACxC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACpC,eAAM,CAAC,IAAI,CAAC,oBAAoB,QAAQ,OAAO,QAAQ,EAAE,CAAC,CAAC;YAE3D,yBAAyB;YACzB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAE3B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,aAAa,CAAC,GAAW,EAAE,QAAgB;QACtD,IAAI,CAAC;YACH,6BAA6B;YAC7B,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC9C,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,qBAAqB,QAAQ,EAAE;gBACtC,WAAW,EAAE,IAAI;aAClB,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;gBAC3B,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;oBAE3D,wBAAwB;oBACxB,MAAM,MAAM,GAAG,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBAE9C,oBAAoB;oBACpB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC;wBAC3B,GAAG;wBACH,MAAM,EAAE,KAAK;wBACb,YAAY,EAAE,QAAQ;wBACtB,kBAAkB,EAAE,CAAC,aAAkB,EAAE,EAAE;4BACzC,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;4BACxF,QAAQ,CAAC,MAAM,CAAC;gCACd,OAAO,EAAE,GAAG,gBAAgB,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG;gCACrH,SAAS,EAAE,gBAAgB;6BAC5B,CAAC,CAAC;wBACL,CAAC;qBACF,CAAC,CAAC;oBAEH,gCAAgC;oBAChC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAE3B,sBAAsB;oBACtB,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBACjC,MAAM,CAAC,KAAK,EAAE,CAAC;wBACf,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;wBACxB,OAAO,KAAK,CAAC;oBACf,CAAC,CAAC,CAAC;oBAEH,oCAAoC;oBACpC,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBACpC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;wBAC9C,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;oBAEH,eAAM,CAAC,IAAI,CAAC,yBAAyB,GAAG,OAAO,QAAQ,EAAE,CAAC,CAAC;oBAE3D,yBAAyB;oBACzB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;oBAE3B,OAAO,IAAI,CAAC;gBACd,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;oBAClD,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,OAAO,MAAM,YAAY,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,WAAW,CAAC,OAAe;QACtC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,eAAM,CAAC,KAAK,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC;gBAC5C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,gDAAgD;YAChD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACjD,eAAM,CAAC,KAAK,CAAC,oDAAoD,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC/E,OAAO,KAAK,CAAC;YACf,CAAC;YAED,kBAAkB;YAClB,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,iBAAiB,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAE3D,yBAAyB;YACzB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAE3B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,8BAA8B;aACxC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,UAAU,OAAO,cAAc;iBACzC,CAAC;YACJ,CAAC;YAED,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrD,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,MAAM,EAAE,eAAe;gBACvB,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,2DAA2D,OAAO,IAAI;iBAChF,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0DAA0D;aACpE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,IAAI,YAAY,GAAG,kCAAkC,CAAC;YAEtD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,gEAAgE;gBAC7E,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,6CAA6C;gBAC1D,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,MAAM;aACb;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,+BAA+B;gBAC5C,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;aACxC;YACD;gBACE,EAAE,EAAE,4BAA4B;gBAChC,IAAI,EAAE,8BAA8B;gBACpC,WAAW,EAAE,kEAAkE;gBAC/E,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,WAAW;aAClB;SACF,CAAC;IACJ,CAAC;CACF;AAniBD,oCAmiBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport * as os from 'os';\nimport { BaseLLMProvider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for GGUF models using llama.cpp\n * \n * This provider allows users to:\n * 1. Use local GGUF models\n * 2. Download GGUF models from links\n * 3. Import GGUF models from other locations\n */\nexport class GGUFProvider extends BaseLLMProvider {\n  readonly providerId = 'gguf';\n  readonly displayName = 'GGUF Models';\n  readonly description = 'Use local GGUF models with llama.cpp';\n  readonly website = 'https://github.com/ggerganov/llama.cpp';\n  readonly requiresApiKey = false;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'http://localhost:8080';\n  readonly defaultModel = '';\n\n  private client: any = null;\n  private modelsDirectory: string;\n  private llamaCppPath = '';\n  private models: Map<string, GGUFModelInfo> = new Map();\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n        \n    // Set up models directory\n    this.modelsDirectory = path.join(os.homedir(), '.codessa', 'models');\n        \n    // Ensure models directory exists\n    if (!fs.existsSync(this.modelsDirectory)) {\n      try {\n        fs.mkdirSync(this.modelsDirectory, { recursive: true });\n        logger.info(`Created models directory at ${this.modelsDirectory}`);\n      } catch (error) {\n        logger.error(`Failed to create models directory: ${error}`);\n      }\n    }\n        \n    this.initializeClient();\n    this.scanForModels();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        logger.info('GGUF configuration changed, re-initializing client.');\n        this.loadConfig().then(() => {\n          this.initializeClient();\n          this.scanForModels();\n        });\n      }\n    });\n  }\n\n  private initializeClient() {\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n    this.llamaCppPath = this.config.llamaCppPath || '';\n\n    try {\n      // Initialize axios client with proper configuration\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 300000, // 5 minutes timeout for large model responses\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      logger.info('GGUF client initialized successfully.');\n    } catch (error) {\n      logger.error('Failed to initialize GGUF client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    // Check if we have at least one model\n    return this.models.size > 0;\n  }\n\n  /**\n     * Scan for GGUF models in the models directory\n     */\n  private async scanForModels() {\n    this.models.clear();\n        \n    try {\n      // Scan the models directory for .gguf files\n      const modelPaths = this.getModelPaths();\n            \n      for (const modelPath of modelPaths) {\n        try {\n          const stats = fs.statSync(modelPath);\n          const fileName = path.basename(modelPath);\n          const modelId = fileName.replace(/\\.gguf$/, '');\n                    \n          this.models.set(modelId, {\n            id: modelId,\n            name: modelId,\n            path: modelPath,\n            size: stats.size,\n            lastModified: stats.mtime\n          });\n                    \n          logger.info(`Found GGUF model: ${modelId} (${this.formatSize(stats.size)})`);\n        } catch (error) {\n          logger.error(`Error processing model file ${modelPath}: ${error}`);\n        }\n      }\n            \n      logger.info(`Found ${this.models.size} GGUF models`);\n    } catch (error) {\n      logger.error(`Error scanning for GGUF models: ${error}`);\n    }\n  }\n    \n  /**\n     * Get all GGUF model paths from configured locations\n     */\n  private getModelPaths(): string[] {\n    const modelPaths: string[] = [];\n        \n    // Add models from the default models directory\n    try {\n      if (fs.existsSync(this.modelsDirectory)) {\n        const files = fs.readdirSync(this.modelsDirectory);\n        for (const file of files) {\n          if (file.endsWith('.gguf')) {\n            modelPaths.push(path.join(this.modelsDirectory, file));\n          }\n        }\n      }\n    } catch (error) {\n      logger.error(`Error reading models directory: ${error}`);\n    }\n        \n    // Add models from additional directories specified in config\n    const additionalDirs = this.config.additionalModelDirectories || [];\n    for (const dir of additionalDirs) {\n      try {\n        if (fs.existsSync(dir)) {\n          const files = fs.readdirSync(dir);\n          for (const file of files) {\n            if (file.endsWith('.gguf')) {\n              modelPaths.push(path.join(dir, file));\n            }\n          }\n        }\n      } catch (error) {\n        logger.error(`Error reading additional model directory ${dir}: ${error}`);\n      }\n    }\n        \n    return modelPaths;\n  }\n\n  /**\n     * Format file size in bytes to a human-readable string\n     */\n  private formatSize(bytes: number): string {\n    if (bytes < 1024) {\n      return `${bytes} B`;\n    } else if (bytes < 1024 * 1024) {\n      return `${(bytes / 1024).toFixed(2)} KB`;\n    } else if (bytes < 1024 * 1024 * 1024) {\n      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;\n    } else {\n      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;\n    }\n  }\n\n  /**\n     * Generate text using a GGUF model\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    _tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'GGUF provider not configured' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || '';\n            \n      if (!modelId) {\n        return { content: '', error: 'No model specified' };\n      }\n            \n      const modelInfo = this.models.get(modelId);\n      if (!modelInfo) {\n        return { content: '', error: `Model '${modelId}' not found` };\n      }\n\n      // Prepare the prompt\n      let prompt = '';\n            \n      // Add system prompt if provided\n      if (params.systemPrompt) {\n        prompt += `<s>[INST] <<SYS>>\\n${params.systemPrompt}\\n<</SYS>>\\n\\n`;\n      } else {\n        prompt += '<s>[INST] ';\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        // Format for chat\n        let lastUserMessage = '';\n                \n        for (let i = 0; i < params.history.length; i++) {\n          const message = params.history[i];\n                    \n          if (message.role === 'user') {\n            if (i > 0) {\n              prompt += `[/INST]\\n\\n${lastUserMessage}\\n\\n[INST] `;\n            }\n            lastUserMessage = message.content;\n          } else if (message.role === 'assistant') {\n            prompt += `${message.content}\\n\\n`;\n          }\n        }\n                \n        // Add the final user message\n        prompt += `${lastUserMessage} [/INST]\\n\\n`;\n      } else {\n        // Just add the user prompt\n        prompt += `${params.prompt} [/INST]\\n\\n`;\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            logger.info('GGUF request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          logger.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request\n      const requestData = {\n        model: modelInfo.path,\n        prompt: prompt,\n        temperature: params.temperature ?? 0.7,\n        max_tokens: params.maxTokens ?? 2048,\n        stop: params.stopSequences ?? ['[INST]']\n      };\n            \n      const response = await this.client.post('/completion', requestData, {\n        signal: abortController?.signal\n      });\n            \n      // Parse response\n      return {\n        content: response.data.content || '',\n        finishReason: 'stop',\n        usage: {\n          promptTokens: response.data.prompt_tokens || prompt.length / 4,\n          completionTokens: response.data.completion_tokens || response.data.content.length / 4,\n        }\n      };\n    } catch (error: any) {\n      logger.error('GGUF generate error:', error);\n      let errorMessage = 'Failed to call GGUF API.';\n\n      if (error.response) {\n        errorMessage = `GGUF API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available GGUF models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    // Refresh the model list\n    await this.scanForModels();\n        \n    return Array.from(this.models.values()).map(model => ({\n      id: model.id,\n      name: model.name,\n      description: `Size: ${this.formatSize(model.size)}`,\n      contextWindow: this.getContextWindowForModel(model.id),\n      pricingInfo: 'Free (local)'\n    }));\n  }\n    \n  /**\n     * Get the context window size for a specific model\n     */\n  private getContextWindowForModel(modelId: string): number {\n    // Estimate context window based on model name\n    if (modelId.includes('70b')) {\n      return 4096;\n    } else if (modelId.includes('13b') || modelId.includes('7b')) {\n      return 8192;\n    } else if (modelId.includes('mistral') || modelId.includes('mixtral')) {\n      return 8192;\n    } else if (modelId.includes('llama-2')) {\n      return 4096;\n    } else if (modelId.includes('llama-3')) {\n      return 8192;\n    } else if (modelId.includes('codellama')) {\n      return 16384;\n    } else if (modelId.includes('phi-2')) {\n      return 2048;\n    } else if (modelId.includes('phi-3')) {\n      return 4096;\n    }\n        \n    // Default context window\n    return 4096;\n  }\n\n  /**\n     * Add a model from a local file\n     */\n  public async addModelFromFile(filePath: string): Promise<boolean> {\n    try {\n      if (!fs.existsSync(filePath)) {\n        logger.error(`File not found: ${filePath}`);\n        return false;\n      }\n            \n      if (!filePath.endsWith('.gguf')) {\n        logger.error(`File is not a GGUF model: ${filePath}`);\n        return false;\n      }\n            \n      const fileName = path.basename(filePath);\n      const destPath = path.join(this.modelsDirectory, fileName);\n            \n      // Copy the file to the models directory\n      fs.copyFileSync(filePath, destPath);\n      logger.info(`Added model from ${filePath} to ${destPath}`);\n            \n      // Refresh the model list\n      await this.scanForModels();\n            \n      return true;\n    } catch (error) {\n      logger.error(`Error adding model from file: ${error}`);\n      return false;\n    }\n  }\n    \n  /**\n     * Download a model from a URL\n     */\n  public async downloadModel(url: string, fileName: string): Promise<boolean> {\n    try {\n      // Show progress notification\n      const downloadTask = vscode.window.withProgress({\n        location: vscode.ProgressLocation.Notification,\n        title: `Downloading model ${fileName}`,\n        cancellable: true\n      }, async (progress, token) => {\n        try {\n          const destPath = path.join(this.modelsDirectory, fileName);\n                    \n          // Create a write stream\n          const writer = fs.createWriteStream(destPath);\n                    \n          // Download the file\n          const response = await axios({\n            url,\n            method: 'GET',\n            responseType: 'stream',\n            onDownloadProgress: (progressEvent: any) => {\n              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n              progress.report({ \n                message: `${percentCompleted}% (${this.formatSize(progressEvent.loaded)} of ${this.formatSize(progressEvent.total)})`,\n                increment: percentCompleted \n              });\n            }\n          });\n                    \n          // Pipe the response to the file\n          response.data.pipe(writer);\n                    \n          // Handle cancellation\n          token.onCancellationRequested(() => {\n            writer.close();\n            fs.unlinkSync(destPath);\n            return false;\n          });\n                    \n          // Wait for the download to complete\n          await new Promise((resolve, reject) => {\n            writer.on('finish', () => resolve(undefined));\n            writer.on('error', reject);\n          });\n                    \n          logger.info(`Downloaded model from ${url} to ${destPath}`);\n                    \n          // Refresh the model list\n          await this.scanForModels();\n                    \n          return true;\n        } catch (error) {\n          logger.error(`Error downloading model: ${error}`);\n          return false;\n        }\n      });\n            \n      return await downloadTask;\n    } catch (error) {\n      logger.error(`Error downloading model: ${error}`);\n      return false;\n    }\n  }\n    \n  /**\n     * Remove a model\n     */\n  public async removeModel(modelId: string): Promise<boolean> {\n    try {\n      const model = this.models.get(modelId);\n      if (!model) {\n        logger.error(`Model not found: ${modelId}`);\n        return false;\n      }\n            \n      // Check if the model is in our models directory\n      if (!model.path.startsWith(this.modelsDirectory)) {\n        logger.error(`Cannot remove model outside of models directory: ${model.path}`);\n        return false;\n      }\n            \n      // Remove the file\n      fs.unlinkSync(model.path);\n      logger.info(`Removed model ${modelId} from ${model.path}`);\n            \n      // Refresh the model list\n      await this.scanForModels();\n            \n      return true;\n    } catch (error) {\n      logger.error(`Error removing model: ${error}`);\n      return false;\n    }\n  }\n\n  /**\n     * Test connection to GGUF\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'GGUF client not initialized.'\n      };\n    }\n\n    try {\n      // Check if the model exists\n      const model = this.models.get(modelId);\n      if (!model) {\n        return {\n          success: false,\n          message: `Model '${modelId}' not found.`\n        };\n      }\n            \n      // Make a simple test request\n      const response = await this.client.post('/completion', {\n        model: model.path,\n        prompt: 'Hello, world!',\n        max_tokens: 10\n      });\n            \n      if (response.data && response.data.content) {\n        return {\n          success: true,\n          message: `Successfully connected to GGUF server and tested model '${modelId}'.`\n        };\n      }\n            \n      return {\n        success: false,\n        message: 'Connected to server but received an unexpected response.'\n      };\n    } catch (error: any) {\n      logger.error('GGUF connection test failed:', error);\n      let errorMessage = 'Failed to connect to GGUF server';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select' | 'directory' | 'file', options?: string[]}> {\n    return [\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The llama.cpp server endpoint (default: http://localhost:8080)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'llamaCppPath',\n        name: 'llama.cpp Path',\n        description: 'Path to the llama.cpp executable (optional)',\n        required: false,\n        type: 'file'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default GGUF model to use',\n        required: false,\n        type: 'select',\n        options: Array.from(this.models.keys())\n      },\n      {\n        id: 'additionalModelDirectories',\n        name: 'Additional Model Directories',\n        description: 'Additional directories to scan for GGUF models (comma-separated)',\n        required: false,\n        type: 'directory'\n      }\n    ];\n  }\n}\n\n/**\n * Interface for GGUF model information\n */\ninterface GGUFModelInfo {\n    id: string;\n    name: string;\n    path: string;\n    size: number;\n    lastModified: Date;\n}\n"]}