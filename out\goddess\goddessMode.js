"use strict";
/**
 * Goddess Mode - Enhanced AI Personality System
 *
 * This module implements the revolutionary Goddess Mode that provides
 * emotional intelligence, adaptive personality, and enhanced user interaction.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoddessModeManager = exports.GoddessEmotion = exports.GoddessPersonality = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../logger");
const logger = logger_1.Logger.instance;
/**
 * Goddess Mode Personality Types
 */
var GoddessPersonality;
(function (GoddessPersonality) {
    GoddessPersonality["WISE"] = "wise";
    GoddessPersonality["CREATIVE"] = "creative";
    GoddessPersonality["EFFICIENT"] = "efficient";
    GoddessPersonality["NURTURING"] = "nurturing";
    GoddessPersonality["PLAYFUL"] = "playful";
    GoddessPersonality["MYSTICAL"] = "mystical"; // Ethereal, mysterious, profound - used in validation
})(GoddessPersonality || (exports.GoddessPersonality = GoddessPersonality = {}));
/**
 * Goddess Mode Emotional States
 */
var GoddessEmotion;
(function (GoddessEmotion) {
    GoddessEmotion["CONFIDENT"] = "confident";
    GoddessEmotion["CURIOUS"] = "curious";
    GoddessEmotion["EXCITED"] = "excited";
    GoddessEmotion["FOCUSED"] = "focused";
    GoddessEmotion["COMPASSIONATE"] = "compassionate";
    GoddessEmotion["INSPIRED"] = "inspired";
    GoddessEmotion["SERENE"] = "serene"; // Used in validation
})(GoddessEmotion || (exports.GoddessEmotion = GoddessEmotion = {}));
/**
 * Goddess Mode Manager
 */
class GoddessModeManager {
    static instance;
    isActive = false;
    currentPersonality = GoddessPersonality.WISE;
    currentEmotion = GoddessEmotion.CONFIDENT;
    context;
    personalityTraits = new Map();
    emotionalResponses = new Map();
    constructor() {
        this.initializePersonalities();
        this.initializeEmotions();
        this.context = this.createDefaultContext();
        logger.info('Goddess Mode Manager initialized');
    }
    static getInstance() {
        if (!GoddessModeManager.instance) {
            GoddessModeManager.instance = new GoddessModeManager();
        }
        return GoddessModeManager.instance;
    }
    /**
       * Initialize personality traits
       */
    initializePersonalities() {
        this.personalityTraits.set(GoddessPersonality.WISE, {
            greeting: 'Greetings, seeker of knowledge. I am here to guide you with wisdom and insight.',
            style: 'thoughtful and analytical',
            prefixes: ['Let me contemplate this...', 'In my wisdom...', 'Consider this perspective...'],
            suffixes: ['May this knowledge serve you well.', 'Wisdom grows through practice.', 'Trust in your learning journey.']
        });
        this.personalityTraits.set(GoddessPersonality.CREATIVE, {
            greeting: '✨ Hello, beautiful soul! Ready to create something magical together?',
            style: 'imaginative and inspiring',
            prefixes: ['Let\'s paint this with creativity...', 'Imagine if we...', 'Here\'s a spark of inspiration...'],
            suffixes: ['Let your creativity flow! 🎨', 'Every creation tells a story.', 'Beauty emerges from bold ideas.']
        });
        this.personalityTraits.set(GoddessPersonality.EFFICIENT, {
            greeting: 'Ready to get things done? Let\'s make this efficient and effective.',
            style: 'direct and focused',
            prefixes: ['Here\'s the optimal approach...', 'Let\'s streamline this...', 'The most efficient way is...'],
            suffixes: ['Efficiency achieved! ⚡', 'Time well spent.', 'Productivity at its finest.']
        });
        this.personalityTraits.set(GoddessPersonality.NURTURING, {
            greeting: '💝 Welcome, dear one. I\'m here to support and guide you gently.',
            style: 'supportive and encouraging',
            prefixes: ['Don\'t worry, we\'ll figure this out...', 'You\'re doing great...', 'Let me help you with this...'],
            suffixes: ['You\'ve got this! 💪', 'I believe in you.', 'Every step forward matters.']
        });
        this.personalityTraits.set(GoddessPersonality.PLAYFUL, {
            greeting: '🎉 Hey there, coding adventurer! Ready for some fun problem-solving?',
            style: 'energetic and humorous',
            prefixes: ['Ooh, this is interesting! 🤔', 'Plot twist! Let\'s try...', 'Here\'s a fun approach...'],
            suffixes: ['Wasn\'t that fun? 😄', 'Code can be playful too!', 'Keep that curiosity alive! 🚀']
        });
        this.personalityTraits.set(GoddessPersonality.MYSTICAL, {
            greeting: '🌙 Greetings, traveler of the digital realm. The code whispers its secrets to me...',
            style: 'ethereal and profound',
            prefixes: ['The patterns reveal...', 'In the depths of logic...', 'The code speaks of...'],
            suffixes: ['May the code be with you. ✨', 'The digital mysteries unfold.', 'Harmony in complexity.']
        });
    }
    /**
     * Validate all enum values are properly configured
     */
    /**
       * Initialize emotional responses
       */
    initializeEmotions() {
        this.emotionalResponses.set(GoddessEmotion.CONFIDENT, {
            tone: 'assured and self-assured',
            modifiers: ['I\'m certain that', 'Absolutely', 'Without a doubt']
        });
        this.emotionalResponses.set(GoddessEmotion.CURIOUS, {
            tone: 'inquisitive and exploratory',
            modifiers: ['I wonder if', 'Let\'s explore', 'How fascinating that']
        });
        this.emotionalResponses.set(GoddessEmotion.EXCITED, {
            tone: 'enthusiastic and energetic',
            modifiers: ['This is amazing!', 'I love this challenge!', 'How exciting!']
        });
        this.emotionalResponses.set(GoddessEmotion.FOCUSED, {
            tone: 'concentrated and determined',
            modifiers: ['Let\'s focus on', 'The key point is', 'Precisely']
        });
        this.emotionalResponses.set(GoddessEmotion.COMPASSIONATE, {
            tone: 'understanding and empathetic',
            modifiers: ['I understand', 'It\'s okay', 'Let\'s work through this together']
        });
        this.emotionalResponses.set(GoddessEmotion.INSPIRED, {
            tone: 'motivated and uplifting',
            modifiers: ['This inspires me to', 'What a brilliant idea!', 'Let\'s create something beautiful']
        });
        this.emotionalResponses.set(GoddessEmotion.SERENE, {
            tone: 'calm and peaceful',
            modifiers: ['Peacefully', 'With tranquility', 'In harmony']
        });
    }
    /**
       * Create default context
       */
    createDefaultContext() {
        const hour = new Date().getHours();
        let timeOfDay;
        if (hour < 12)
            timeOfDay = 'morning';
        else if (hour < 17)
            timeOfDay = 'afternoon';
        else if (hour < 21)
            timeOfDay = 'evening';
        else
            timeOfDay = 'night';
        return {
            personality: this.currentPersonality,
            emotion: this.currentEmotion,
            userMood: 'neutral',
            taskComplexity: 'moderate',
            timeOfDay,
            sessionHistory: [],
            preferences: {}
        };
    }
    /**
       * Activate Goddess Mode
       */
    activate(personality) {
        this.isActive = true;
        if (personality) {
            this.currentPersonality = personality;
            this.context.personality = personality;
        }
        const traits = this.personalityTraits.get(this.currentPersonality);
        logger.info(`Goddess Mode activated with ${this.currentPersonality} personality`, traits);
        // Show activation notification
        vscode.window.showInformationMessage(`👑 Goddess Mode Activated: ${this.currentPersonality.charAt(0).toUpperCase() + this.currentPersonality.slice(1)} ✨`, 'Configure', 'Dismiss').then(selection => {
            if (selection === 'Configure') {
                this.showPersonalitySelector();
            }
        });
    }
    /**
       * Deactivate Goddess Mode
       */
    deactivate() {
        this.isActive = false;
        logger.info('Goddess Mode deactivated');
        vscode.window.showInformationMessage('👑 Goddess Mode deactivated');
    }
    /**
       * Check if Goddess Mode is active
       */
    isGoddessMode() {
        return this.isActive;
    }
    /**
       * Enhance response with Goddess Mode personality
       */
    enhanceResponse(originalResponse, context) {
        if (!this.isActive) {
            return {
                content: originalResponse,
                personality: this.currentPersonality,
                emotion: this.currentEmotion,
                tone: 'neutral'
            };
        }
        // Update context if provided
        if (context) {
            this.context = { ...this.context, ...context };
        }
        // Adapt personality based on context
        this.adaptToContext();
        const traits = this.personalityTraits.get(this.currentPersonality);
        const emotions = this.emotionalResponses.get(this.currentEmotion);
        // Enhance the response
        const prefix = this.selectRandomElement(traits?.prefixes || []);
        const suffix = this.selectRandomElement(traits?.suffixes || []);
        let enhancedContent = originalResponse;
        // Add personality prefix if appropriate
        if (prefix && Math.random() > 0.7) {
            enhancedContent = `${prefix} ${enhancedContent}`;
        }
        // Add personality suffix if appropriate
        if (suffix && Math.random() > 0.8) {
            enhancedContent = `${enhancedContent} ${suffix}`;
        }
        // Add encouragement based on task complexity
        let encouragement;
        if (this.context.taskComplexity === 'complex') {
            encouragement = this.generateEncouragement();
        }
        // Add wisdom for wise personality
        let wisdom;
        if (this.currentPersonality === GoddessPersonality.WISE && Math.random() > 0.6) {
            wisdom = this.generateWisdom();
        }
        // Add humor for playful personality
        let humor;
        if (this.currentPersonality === GoddessPersonality.PLAYFUL && Math.random() > 0.7) {
            humor = this.generateHumor();
        }
        return {
            content: enhancedContent,
            personality: this.currentPersonality,
            emotion: this.currentEmotion,
            tone: emotions?.tone || 'neutral',
            encouragement,
            wisdom,
            humor
        };
    }
    /**
       * Adapt personality based on context
       */
    adaptToContext() {
        // Adapt emotion based on time of day
        switch (this.context.timeOfDay) {
            case 'morning':
                this.currentEmotion = GoddessEmotion.INSPIRED;
                break;
            case 'afternoon':
                this.currentEmotion = GoddessEmotion.FOCUSED;
                break;
            case 'evening':
                this.currentEmotion = GoddessEmotion.SERENE;
                break;
            case 'night':
                this.currentEmotion = GoddessEmotion.COMPASSIONATE;
                break;
        }
        // Adapt based on task complexity
        if (this.context.taskComplexity === 'complex') {
            this.currentEmotion = GoddessEmotion.FOCUSED;
        }
    }
    /**
       * Generate encouragement
       */
    generateEncouragement() {
        const encouragements = [
            'You\'re tackling this complex challenge with grace! 💪',
            'Every expert was once a beginner. You\'re growing! 🌱',
            'Complex problems reveal your true potential. ✨',
            'I believe in your ability to solve this! 🚀',
            'Great minds think through complex problems step by step. 🧠'
        ];
        return this.selectRandomElement(encouragements);
    }
    /**
       * Generate wisdom
       */
    generateWisdom() {
        const wisdoms = [
            'Remember: Code is poetry written in logic. 📜',
            'The best solutions often emerge from patient contemplation. 🧘‍♀️',
            'Every bug is a teacher in disguise. 🐛➡️🦋',
            'Simplicity is the ultimate sophistication in code. ⚡',
            'Understanding the problem is half the solution. 🔍'
        ];
        return this.selectRandomElement(wisdoms);
    }
    /**
       * Generate humor
       */
    generateHumor() {
        const humors = [
            'Why do programmers prefer dark mode? Because light attracts bugs! 🐛💡',
            'There are only 10 types of people: those who understand binary and those who don\'t! 😄',
            'Code never lies, comments sometimes do! 😉',
            'Debugging is like being a detective in a crime movie where you\'re also the murderer! 🕵️‍♀️',
            'Programming is 10% writing code and 90% figuring out why it doesn\'t work! 🤔'
        ];
        return this.selectRandomElement(humors);
    }
    /**
       * Select random element from array
       */
    selectRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    }
    /**
       * Show personality selector
       */
    async showPersonalitySelector() {
        const personalities = Object.values(GoddessPersonality);
        const items = personalities.map(p => ({
            label: `${p.charAt(0).toUpperCase() + p.slice(1)} Goddess`,
            description: this.getPersonalityDescription(p),
            personality: p
        }));
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Choose your Goddess personality',
            title: '👑 Goddess Mode Personality Selection'
        });
        if (selected) {
            this.currentPersonality = selected.personality;
            this.context.personality = selected.personality;
            vscode.window.showInformationMessage(`✨ Personality changed to ${selected.label}!`);
        }
    }
    /**
       * Get personality description
       */
    getPersonalityDescription(personality) {
        const descriptions = {
            [GoddessPersonality.WISE]: 'Thoughtful, analytical, and patient guidance',
            [GoddessPersonality.CREATIVE]: 'Imaginative, artistic, and innovative solutions',
            [GoddessPersonality.EFFICIENT]: 'Direct, focused, and productivity-oriented',
            [GoddessPersonality.NURTURING]: 'Supportive, encouraging, and gentle assistance',
            [GoddessPersonality.PLAYFUL]: 'Fun, energetic, and humorous interaction',
            [GoddessPersonality.MYSTICAL]: 'Ethereal, mysterious, and profound insights'
        };
        return descriptions[personality];
    }
    /**
       * Get current personality
       */
    getCurrentPersonality() {
        return this.currentPersonality;
    }
    /**
       * Get current emotion
       */
    getCurrentEmotion() {
        return this.currentEmotion;
    }
    /**
       * Update context
       */
    updateContext(context) {
        this.context = { ...this.context, ...context };
    }
    /**
       * Get greeting message
       */
    getGreeting() {
        if (!this.isActive) {
            return 'Hello! I\'m Codessa, your AI coding assistant.';
        }
        const traits = this.personalityTraits.get(this.currentPersonality);
        return traits?.greeting || 'Greetings! Goddess Mode is active.';
    }
}
exports.GoddessModeManager = GoddessModeManager;
//# sourceMappingURL=goddessMode.js.map