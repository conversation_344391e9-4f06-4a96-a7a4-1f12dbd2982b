"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupervisorAgent = void 0;
const agent_1 = require("../agentUtilities/agent");
const logger_1 = require("../../logger");
const workflowRegistry_1 = require("../workflows/workflowRegistry");
const goddessMode_1 = require("../../goddess/goddessMode");
const toolRegistry_1 = require("../../tools/toolRegistry");
const memoryManager_1 = require("../../memory/memoryManager");
const events_1 = require("events");
const promptManager_1 = require("../../prompts/promptManager");
class SupervisorAgent extends agent_1.Agent {
    modeAgents = new Map();
    receiverAgent;
    goddessMode;
    toolRegistry;
    // Streaming and communication
    streamEmitter = new events_1.EventEmitter();
    activeStreams = new Map();
    agentCommunicationChannels = new Map();
    // Enhanced tool and workflow access
    allWorkflows = new Map();
    mcpConnections = new Map();
    // Phase 2: Autonomous capabilities
    currentIteration = 0;
    maxIterations = 5;
    taskHistory = [];
    learningMemory = new Map();
    constructor(_config, receiverAgent) {
        super({
            id: _config.id || 'supervisor-agent',
            name: _config.name || 'Supervisor Agent',
            systemPromptName: _config.systemPromptName || 'agent.supervisor',
            ..._config,
            role: 'supervisor',
            capabilities: ['supervise'],
            llmProvider: 'openai',
            llmModel: 'gpt-4'
        });
        this.receiverAgent = receiverAgent;
        this.goddessMode = goddessMode_1.GoddessModeManager.getInstance();
        this.toolRegistry = toolRegistry_1.ToolRegistry.instance;
        // Initialize enhanced capabilities
        this.initializeWorkflows();
        this.initializeMCPConnections();
        this.setupStreamingHandlers();
    }
    /**
     * Initialize all available workflows for supervisor access
     */
    initializeWorkflows() {
        try {
            // Get all registered workflows
            const workflows = workflowRegistry_1.workflowRegistry.getAllWorkflows();
            workflows.forEach((workflow) => {
                this.allWorkflows.set(workflow.id, workflow);
            });
            logger_1.logger.info(`SupervisorAgent initialized with ${this.allWorkflows.size} workflows`);
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize workflows:', error);
        }
    }
    /**
     * Initialize MCP connections for enhanced tool access
     */
    initializeMCPConnections() {
        try {
            // Initialize MCP connections here
            logger_1.logger.info('SupervisorAgent MCP connections initialized');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize MCP connections:', error);
        }
    }
    /**
     * Setup streaming event handlers for real-time communication
     */
    setupStreamingHandlers() {
        this.streamEmitter.on('agentStart', this.handleAgentStart.bind(this));
        this.streamEmitter.on('agentProgress', this.handleAgentProgress.bind(this));
        this.streamEmitter.on('agentComplete', this.handleAgentComplete.bind(this));
        this.streamEmitter.on('agentError', this.handleAgentError.bind(this));
    }
    /**
     * Streaming event handlers
     */
    handleAgentStart(event) {
        logger_1.logger.info(`Agent ${event.agentId} started`);
        this.broadcastToStreams(event);
    }
    handleAgentProgress(event) {
        logger_1.logger.debug(`Agent ${event.agentId} progress: ${event.data}`);
        this.broadcastToStreams(event);
    }
    handleAgentComplete(event) {
        logger_1.logger.info(`Agent ${event.agentId} completed`);
        this.broadcastToStreams(event);
        this.activeStreams.delete(event.agentId);
    }
    handleAgentError(event) {
        logger_1.logger.error(`Agent ${event.agentId} error:`, event.error);
        this.broadcastToStreams(event);
        this.activeStreams.delete(event.agentId);
    }
    broadcastToStreams(event) {
        this.activeStreams.forEach((context) => {
            if (context.onStream) {
                context.onStream(event);
            }
        });
    }
    registerModeAgent(mode, agent) {
        this.modeAgents.set(mode, agent);
        // Setup communication channel for this agent
        const channel = new events_1.EventEmitter();
        this.agentCommunicationChannels.set(agent.id, channel);
        logger_1.logger.info(`Registered agent for mode: ${mode} with communication channel`);
    }
    getModeAgent(mode) {
        return this.modeAgents.get(mode);
    }
    async createModeAgent(mode, name, description) {
        // Try to dynamically import the mode-specific agent
        try {
            const agentModule = await Promise.resolve(`${`./modes/${mode}Agent`}`).then(s => __importStar(require(s)));
            const AgentClass = agentModule[`${mode.charAt(0).toUpperCase() + mode.slice(1)}Agent`];
            if (AgentClass) {
                const agentConfig = {
                    id: `${mode}-${Date.now()}`,
                    name: name,
                    description: description,
                    systemPromptName: `${mode}Agent`,
                    tools: ['file', 'search', 'memory', 'workflow'],
                    role: 'specialist',
                    capabilities: [mode],
                    llmProvider: 'openai',
                    llmModel: 'gpt-4',
                    temperature: 0.7,
                    maxTokens: 2000
                };
                const agent = new AgentClass(agentConfig);
                this.registerModeAgent(mode, agent);
                logger_1.logger.info(`Dynamically created ${mode} agent`);
                return agent;
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to import specialized agent for mode ${mode}: ${error}`);
        }
        // Fallback to generic agent
        const agentConfig = {
            id: `${mode}-${Date.now()}`,
            name: name,
            description: description,
            systemPromptName: `${mode}Agent`,
            tools: ['file', 'search', 'memory', 'workflow'],
            role: 'supervisor',
            capabilities: ['supervise'],
            llmProvider: 'openai',
            llmModel: 'gpt-4',
            temperature: 0.7,
            maxTokens: 2000
        };
        const agent = new agent_1.Agent(agentConfig);
        this.registerModeAgent(mode, agent);
        logger_1.logger.info(`Dynamically created generic agent for mode: ${mode}`);
        return agent;
    }
    /**
       * Get appropriate workflow for a mode and task
       */
    getWorkflowForMode(mode, task) {
        // Get all workflows
        const workflows = workflowRegistry_1.workflowRegistry.getAllWorkflows();
        // First try to find a workflow specifically for this mode and task
        let workflow = workflows.find(w => w.tags?.includes(mode) &&
            w.tags?.includes(task));
        // If not found, try to find a workflow for this mode
        if (!workflow) {
            workflow = workflows.find(w => w.tags?.includes(mode) ||
                w.name.toLowerCase().includes(mode));
        }
        // If still not found, use a default workflow based on methodology
        if (!workflow) {
            if (mode === 'edit' || mode === 'refactor' || mode === 'debug') {
                workflow = workflows.find(w => w.methodology === 'devops');
            }
            else {
                workflow = workflows.find(w => w.methodology === 'waterfall');
            }
        }
        return workflow;
    }
    async processRequest(input, mode, context = {}) {
        logger_1.logger.info(`SupervisorAgent processing request in ${mode} mode: "${input.substring(0, 50)}..."`);
        // Create streaming context if not provided
        const streamingContext = context.streamingContext || {
            streamId: `stream_${Date.now()}`,
            onStream: context.onStream
        };
        this.activeStreams.set(streamingContext.streamId, streamingContext);
        try {
            // Emit start event
            this.emitStreamEvent('start', 'supervisor', { input, mode });
            // Step 1: Process input through the ReceiverAgent with streaming
            this.emitStreamEvent('progress', 'supervisor', { step: 'receiver_processing' });
            const enhancedInput = await this.receiverAgent.processInput(input, {
                ...context,
                streamingContext
            });
            // Step 2: Analyze the task to determine required agents and workflows
            this.emitStreamEvent('progress', 'supervisor', { step: 'task_analysis' });
            const taskAnalysis = await this.analyzeTask(enhancedInput, mode, context);
            // Step 3: Get or create the appropriate agents for this task
            this.emitStreamEvent('progress', 'supervisor', { step: 'agent_preparation' });
            const agents = await this.getOrCreateAgentsForTask(taskAnalysis, mode);
            // Step 4: Get the appropriate workflow for this task
            this.emitStreamEvent('progress', 'supervisor', { step: 'workflow_selection' });
            const workflow = this.getWorkflowForMode(mode, taskAnalysis.primaryTask);
            // Step 5: Execute the task using the workflow and agents with streaming
            this.emitStreamEvent('progress', 'supervisor', { step: 'execution' });
            const result = await this.executeWithWorkflow(enhancedInput, agents, workflow, mode, {
                ...context,
                streamingContext
            });
            // Step 6: Review and finalize the result
            this.emitStreamEvent('progress', 'supervisor', { step: 'finalization' });
            const finalResult = await this.reviewAndFinalize(result, enhancedInput, mode, context);
            // Emit completion event
            this.emitStreamEvent('complete', 'supervisor', { result: finalResult });
            return finalResult;
        }
        catch (error) {
            this.emitStreamEvent('error', 'supervisor', undefined, error);
            throw error;
        }
        finally {
            this.activeStreams.delete(streamingContext.streamId);
        }
    }
    /**
     * Emit streaming events for real-time updates
     */
    emitStreamEvent(type, agentId, data, error) {
        const event = {
            type,
            agentId,
            data,
            error,
            timestamp: Date.now()
        };
        this.streamEmitter.emit(`agent${type.charAt(0).toUpperCase() + type.slice(1)}`, event);
    }
    /**
     * Enable direct agent-to-agent communication
     */
    enableAgentCommunication(fromAgentId, toAgentId, message) {
        const toChannel = this.agentCommunicationChannels.get(toAgentId);
        if (toChannel) {
            toChannel.emit('message', {
                from: fromAgentId,
                to: toAgentId,
                message,
                timestamp: Date.now()
            });
            logger_1.logger.info(`Message sent from ${fromAgentId} to ${toAgentId}`);
        }
        else {
            logger_1.logger.warn(`No communication channel found for agent ${toAgentId}`);
        }
    }
    /**
     * Broadcast message to all agents
     */
    broadcastToAllAgents(fromAgentId, message) {
        this.agentCommunicationChannels.forEach((channel, agentId) => {
            if (agentId !== fromAgentId) {
                channel.emit('broadcast', {
                    from: fromAgentId,
                    message,
                    timestamp: Date.now()
                });
            }
        });
        logger_1.logger.info(`Broadcast message sent from ${fromAgentId} to all agents`);
    }
    /**
     * Get communication channel for an agent
     */
    getAgentCommunicationChannel(agentId) {
        return this.agentCommunicationChannels.get(agentId);
    }
    /**
     * Enhanced tool access - supervisor has access to ALL tools
     */
    getAllAvailableTools() {
        const allTools = this.toolRegistry.getAllTools();
        // Add MCP tools if available
        const mcpTools = this.getMCPTools();
        return [...allTools, ...mcpTools];
    }
    /**
     * Get MCP tools
     */
    getMCPTools() {
        // Implementation for MCP tool access
        const mcpTools = [];
        this.mcpConnections.forEach((connection, name) => {
            // Add MCP connection tools
            if (connection.tools) {
                const toolsWithSource = connection.tools.map((tool) => ({
                    ...tool,
                    source: `mcp:${name}`
                }));
                mcpTools.push(...toolsWithSource);
            }
        });
        return mcpTools;
    }
    /**
     * Enhanced workflow access - supervisor knows all workflows
     */
    getAllAvailableWorkflows() {
        return new Map(this.allWorkflows);
    }
    /**
     * Execute parallel agent tasks with streaming coordination
     */
    async executeParallelAgentTasks(tasks, coordinationContext) {
        const results = [];
        const streamId = `parallel_${Date.now()}`;
        this.emitStreamEvent('start', 'supervisor', {
            action: 'parallel_execution',
            taskCount: tasks.length,
            streamId
        });
        for (const task of tasks) {
            const taskPromise = this.delegateToAgent(task.agent, task.input, {
                ...task.context,
                parallelExecution: true,
                coordinationContext,
                streamingContext: {
                    streamId: `${streamId}_${task.agent.id}`,
                    parentStreamId: streamId,
                    onStream: coordinationContext.onStream
                }
            });
            results.push(taskPromise);
        }
        try {
            const completedResults = await Promise.all(results);
            this.emitStreamEvent('complete', 'supervisor', {
                action: 'parallel_execution',
                results: completedResults.map(r => r.substring(0, 50) + '...'),
                streamId
            });
            return completedResults;
        }
        catch (error) {
            this.emitStreamEvent('error', 'supervisor', {
                action: 'parallel_execution',
                streamId
            }, error);
            throw error;
        }
    }
    /**
       * Analyze the task to determine required agents and workflows
       */
    async analyzeTask(input, mode, context) {
        logger_1.logger.info(`Analyzing task for ${mode} mode`);
        // Get relevant memories for context using AgentMemory if available
        let memoryContext = '';
        try {
            if (this.getMemory) {
                const relevant = await this.getMemory().getRelevantMemories(input);
                memoryContext = this.getMemory().formatMemoriesForPrompt(relevant);
            }
            else {
                // Fallback to direct memoryManager usage
                const memories = await memoryManager_1.memoryManager.searchMemories({
                    query: input,
                    limit: 5,
                    filter: {
                        type: 'conversation',
                        source: 'user'
                    }
                });
                if (memories.length > 0) {
                    memoryContext = memories.map((m) => m.content).join('\n');
                }
            }
        }
        catch (error) {
            logger_1.logger.warn('Failed to retrieve memories for task analysis:', error);
        }
        // Get available agents for delegation
        const availableAgents = this._getAvailableAgents();
        // Use promptManager for consistent prompt generation
        const analysisPrompt = promptManager_1.promptManager.renderPrompt('agent.supervisor', {
            processedRequest: input,
            availableAgents: JSON.stringify(availableAgents),
            contextContent: context ? JSON.stringify(context, null, 2) : 'No additional context provided',
            memoryContext,
            mode
        });
        const analysisResult = await this.generate(analysisPrompt);
        try {
            return JSON.parse(analysisResult);
        }
        catch (error) {
            logger_1.logger.error(`Failed to parse task analysis result: ${error}`);
            // Return a default analysis
            return {
                primaryTask: 'process user request',
                requiredAgents: [mode],
                requiredTools: ['file', 'search', 'memory'],
                workflowType: 'waterfall',
                parallelExecution: false
            };
        }
    }
    /**
       * Get or create agents needed for a task
       */
    async getOrCreateAgentsForTask(taskAnalysis, mode) {
        const agents = [];
        // Always include the mode-specific agent
        let modeAgent = this.getModeAgent(mode);
        if (!modeAgent) {
            modeAgent = await this.createModeAgent(mode, `${mode.charAt(0).toUpperCase() + mode.slice(1)} Agent`, `Agent for ${mode} mode`);
        }
        agents.push(modeAgent);
        // Add any additional required agents
        if (taskAnalysis.requiredAgents) {
            for (const agentType of (taskAnalysis.requiredAgents || [])) {
                if (agentType !== mode) {
                    let agent = this.getModeAgent(agentType);
                    if (!agent) {
                        agent = await this.createModeAgent(agentType, `${agentType.charAt(0).toUpperCase() + agentType.slice(1)} Agent`, `Agent for ${agentType} tasks`);
                    }
                    agents.push(agent);
                }
            }
        }
        return agents;
    }
    /**
       * Execute a task using a workflow and agents
       */
    async executeWithWorkflow(input, agents, workflow, mode, context) {
        // If no workflow is available, use the primary agent directly
        if (!workflow || !workflow.nodes || workflow.nodes.length === 0) {
            logger_1.logger.info(`No workflow available for ${mode} mode. Using primary agent directly.`);
            return this.delegateToAgent(agents[0], input, context);
        }
        logger_1.logger.info(`Executing task with workflow: ${workflow.name}`);
        // Execute the workflow
        const results = [];
        const visitedNodes = new Set();
        let currentNodeId = workflow.startNodeId;
        while (currentNodeId && currentNodeId !== 'output') {
            // Prevent infinite loops
            if (visitedNodes.has(currentNodeId)) {
                logger_1.logger.warn(`Detected cycle in workflow at node ${currentNodeId}. Breaking.`);
                break;
            }
            visitedNodes.add(currentNodeId);
            // Find the current node
            const currentNode = workflow.nodes.find(node => node.id === currentNodeId);
            if (!currentNode) {
                logger_1.logger.error(`Node ${currentNodeId} not found in workflow.`);
                break;
            }
            // Process the node
            let nodeResult = '';
            if (currentNode.type === 'agent') {
                // Find the appropriate agent for this node
                const nodeAgent = agents.find(a => a.name.toLowerCase().includes(currentNode.label.toLowerCase()));
                if (nodeAgent) {
                    nodeResult = await this.delegateToAgent(nodeAgent, input, {
                        ...context,
                        currentNode: currentNode.id,
                        previousResults: results
                    });
                }
                else {
                    // Use the primary agent if no specific agent is found
                    nodeResult = await this.delegateToAgent(agents[0], input, {
                        ...context,
                        currentNode: currentNode.id,
                        previousResults: results
                    });
                }
            }
            else if (currentNode.type === 'tool' && currentNode.tool) {
                // Execute the tool
                try {
                    const toolResult = await currentNode.tool.execute('run', {
                        input,
                        context: {
                            ...context,
                            currentNode: currentNode.id,
                            previousResults: results
                        }
                    });
                    nodeResult = toolResult.output || '';
                }
                catch (error) {
                    logger_1.logger.error(`Error executing tool ${currentNode.label}: ${error}`);
                    nodeResult = `Error: ${error}`;
                }
            }
            // Add the result
            if (nodeResult) {
                results.push(nodeResult);
            }
            // Find the next node
            const outgoingEdges = workflow.edges.filter(edge => edge.source === currentNodeId);
            if (outgoingEdges.length === 0) {
                logger_1.logger.warn(`No outgoing edges from node ${currentNodeId}. Workflow execution stopped.`);
                break;
            }
            // For now, just take the first edge
            currentNodeId = outgoingEdges[0].target;
        }
        // Combine the results
        return results.join('\n\n');
    }
    /**
       * Phase 2: Autonomous task execution with self-correction (Optimized)
       */
    async executeAutonomousTask(requirements, mode = 'agent') {
        logger_1.logger.info(`Starting autonomous task execution: ${requirements.substring(0, 100)}...`);
        this.currentIteration = 0;
        const startTime = Date.now();
        try {
            // Early validation of requirements
            if (!requirements || requirements.trim().length < 10) {
                throw new Error('Requirements must be at least 10 characters long');
            }
            // Check if we have similar task in learning memory for optimization
            const similarTask = this.findSimilarTaskInMemory(requirements);
            let taskPlan;
            if (similarTask && similarTask.success) {
                logger_1.logger.info('Found similar successful task in memory, optimizing plan...');
                taskPlan = await this.optimizeTaskPlanFromMemory(requirements, mode, similarTask);
            }
            else {
                // Step 1: Create comprehensive task plan
                taskPlan = await this.createTaskPlan(requirements, mode);
            }
            logger_1.logger.info(`Created task plan with ${taskPlan.steps.length} steps (complexity: ${taskPlan.complexity})`);
            // Step 2: Execute the task plan with early termination on critical failures
            let result = await this.executeTaskPlan(taskPlan, requirements);
            // Step 3: Validate the result (optimized validation)
            let validation = await this.validateTaskResult(result, taskPlan);
            // Step 4: Self-correct if needed (with exponential backoff)
            let backoffDelay = 1000; // Start with 1 second
            while (!validation.success && this.currentIteration < this.maxIterations) {
                this.currentIteration++;
                logger_1.logger.info(`Task validation failed. Attempting self-correction (iteration ${this.currentIteration})`);
                // Add backoff delay to prevent rapid retries
                if (this.currentIteration > 1) {
                    await new Promise(resolve => setTimeout(resolve, backoffDelay));
                    backoffDelay = Math.min(backoffDelay * 2, 10000); // Max 10 seconds
                }
                result = await this.selfCorrectAndRetry(requirements, result, validation);
                validation = await this.validateTaskResult(result, taskPlan);
            }
            // Step 5: Learn from the experience (async to not block return)
            this.learnFromExecution(taskPlan, result, validation).catch(error => {
                logger_1.logger.warn(`Learning from execution failed: ${error}`);
            });
            const timeSpent = Date.now() - startTime;
            result.timeSpent = timeSpent;
            // Store in history (with size limit)
            this.taskHistory.push(result);
            if (this.taskHistory.length > 50) { // Limit history size
                this.taskHistory = this.taskHistory.slice(-50);
            }
            logger_1.logger.info(`Autonomous task completed in ${timeSpent}ms with ${this.currentIteration} iterations (success: ${result.success})`);
            return result;
        }
        catch (error) {
            logger_1.logger.error(`Autonomous task execution failed: ${error}`);
            const timeSpent = Date.now() - startTime;
            return {
                success: false,
                output: `Autonomous execution failed: ${error instanceof Error ? error.message : String(error)}`,
                artifacts: [],
                validationResults: [],
                timeSpent,
                errors: [error instanceof Error ? error.message : String(error)]
            };
        }
    }
    async run(input, context = {}) {
        // Extract mode from input or context
        const mode = input.mode || 'chat';
        try {
            // Check if this should be an autonomous task
            const isAutonomousRequest = this.shouldUseAutonomousMode(input.prompt, mode);
            if (isAutonomousRequest && mode === 'agent') {
                // Use autonomous execution for complex agent tasks
                const result = await this.executeAutonomousTask(input.prompt, mode);
                // Enhance with Goddess Mode if active
                let output = result.output;
                if (this.goddessMode.isGoddessMode()) {
                    const enhanced = this.goddessMode.enhanceResponse(output, {
                        taskComplexity: result.success ? 'moderate' : 'complex'
                    });
                    output = enhanced.content;
                    if (enhanced.encouragement) {
                        output += `\n\n${enhanced.encouragement}`;
                    }
                }
                return {
                    success: result.success,
                    output: output,
                    error: result.errors?.join('; ')
                };
            }
            else {
                // Use standard processing for simpler tasks with streaming context
                const result = await this.processRequest(input.prompt, mode, context);
                // Enhance with Goddess Mode if active
                let output = result;
                if (this.goddessMode.isGoddessMode()) {
                    const enhanced = this.goddessMode.enhanceResponse(output);
                    output = enhanced.content;
                }
                return {
                    success: true,
                    output: output
                };
            }
        }
        catch (error) {
            logger_1.logger.error(`Error in SupervisorAgent.run: ${error}`);
            return {
                success: false,
                error: `Error processing request: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
    async delegateToAgent(agent, input, context) {
        logger_1.logger.info(`Delegating to agent: ${agent.name}`);
        // Create streaming context for delegation
        const delegationStreamId = `delegation_${agent.id}_${Date.now()}`;
        const streamingContext = {
            streamId: delegationStreamId,
            parentStreamId: context.streamingContext?.streamId,
            onStream: (event) => {
                // Forward agent events to supervisor stream
                this.emitStreamEvent('delegation', 'supervisor', {
                    delegatedAgent: agent.name,
                    delegatedEvent: event
                });
                // Also forward to parent stream if available
                const streamingCtx = context.streamingContext;
                if (streamingCtx?.onStream) {
                    streamingCtx.onStream(event);
                }
            }
        };
        // Emit delegation start event
        this.emitStreamEvent('delegation', 'supervisor', {
            action: 'start',
            delegatedAgent: agent.name,
            input: input.substring(0, 100) + '...'
        });
        const agentInput = {
            prompt: input,
            mode: 'task'
        };
        const agentContext = {
            ...context,
            streamingContext,
            variables: {
                ...(context.variables || {}),
                delegatedBy: this.name,
                originalInput: input,
                supervisorId: this.id
            }
        };
        try {
            const result = await agent.run(agentInput, agentContext);
            if (!result.success) {
                logger_1.logger.error(`Agent ${agent.name} failed: ${result.error}`);
                this.emitStreamEvent('delegation', 'supervisor', {
                    action: 'error',
                    delegatedAgent: agent.name,
                    error: result.error
                });
                throw new Error(`Agent ${agent.name} failed: ${result.error}`);
            }
            // Emit delegation success event
            this.emitStreamEvent('delegation', 'supervisor', {
                action: 'complete',
                delegatedAgent: agent.name,
                result: result.output?.substring(0, 100) + '...'
            });
            return result.output || '';
        }
        catch (error) {
            this.emitStreamEvent('delegation', 'supervisor', {
                action: 'error',
                delegatedAgent: agent.name,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    async reviewAndFinalize(result, originalInput, mode, context) {
        logger_1.logger.info(`Reviewing and finalizing result for ${mode} mode`);
        // Use context for additional review information
        const contextInfo = context.variables ? JSON.stringify(context.variables) : 'No additional context';
        // Create a prompt for reviewing the result
        const reviewPrompt = `
        # Result Review Task

        You are the SupervisorAgent reviewing the result from the workflow execution.

        ## Original Enhanced Request:
        ${originalInput}

        ## Workflow Result:
        ${result}

        ## Context Information:
        ${contextInfo}

        ## Your Task:
        1. Verify that the result fully addresses the user's request
        2. Check for any errors or omissions
        3. Enhance the result if necessary
        4. Format the final response appropriately for ${mode} mode
        5. Consider the context information for additional insights

        ## Final Response:
        `;
        return this.generate(reviewPrompt);
    }
    /**
       * Phase 2: Create comprehensive task plan for autonomous execution
       */
    async createTaskPlan(requirements, mode) {
        const planningPrompt = `
        # Autonomous Task Planning

        You are the SupervisorAgent creating a comprehensive plan for autonomous task execution.

        ## Requirements:
        ${requirements}

        ## Mode:
        ${mode}

        ## Your Task:
        Create a detailed task plan that breaks down the requirements into executable steps.
        Consider dependencies, validation criteria, and potential failure points.

        ## Output Format (JSON):
        {
            "id": "unique_task_id",
            "description": "Brief description of the overall task",
            "complexity": "low|medium|high",
            "estimatedTime": 300000,
            "validationCriteria": ["criterion1", "criterion2"],
            "steps": [
                {
                    "id": "step_1",
                    "description": "What this step does",
                    "type": "analysis|implementation|testing|validation",
                    "agent": "agent_type",
                    "tools": ["tool1", "tool2"],
                    "expectedOutput": "What should be produced",
                    "dependencies": ["step_id"]
                }
            ],
            "dependencies": ["external_dependency"]
        }
        `;
        const planResult = await this.generate(planningPrompt);
        try {
            const plan = JSON.parse(planResult);
            return {
                id: plan.id || `task_${Date.now()}`,
                description: plan.description || requirements.substring(0, 100),
                steps: plan.steps || [],
                dependencies: plan.dependencies || [],
                estimatedTime: plan.estimatedTime || 300000,
                complexity: plan.complexity || 'medium',
                validationCriteria: plan.validationCriteria || ['Task completed successfully']
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to parse task plan: ${error}`);
            // Return a basic plan
            return {
                id: `task_${Date.now()}`,
                description: requirements.substring(0, 100),
                steps: [{
                        id: 'step_1',
                        description: 'Execute the task',
                        type: 'implementation',
                        agent: mode,
                        tools: ['file', 'search', 'memory'],
                        expectedOutput: 'Task completion',
                        dependencies: []
                    }],
                dependencies: [],
                estimatedTime: 300000,
                complexity: 'medium',
                validationCriteria: ['Task completed successfully']
            };
        }
    }
    /**
       * Phase 2: Execute task plan with step-by-step execution
       */
    async executeTaskPlan(taskPlan, originalRequirements) {
        logger_1.logger.info(`Executing task plan: ${taskPlan.description}`);
        const artifacts = [];
        const validationResults = [];
        const outputs = [];
        const errors = [];
        // Execute steps in dependency order
        const executedSteps = new Set();
        const stepResults = new Map();
        for (const step of taskPlan.steps) {
            try {
                // Check if dependencies are satisfied
                const unsatisfiedDeps = step.dependencies.filter(dep => !executedSteps.has(dep));
                if (unsatisfiedDeps.length > 0) {
                    logger_1.logger.warn(`Step ${step.id} has unsatisfied dependencies: ${unsatisfiedDeps.join(', ')}`);
                    // For now, continue anyway - in a full implementation, we'd handle this better
                }
                logger_1.logger.info(`Executing step: ${step.description}`);
                // Get the appropriate agent for this step
                const agent = await this.getAgentForStep(step);
                // Prepare context for the step
                const stepContext = {
                    originalRequirements,
                    taskPlan,
                    currentStep: step,
                    previousResults: stepResults,
                    executedSteps: Array.from(executedSteps)
                };
                // Execute the step
                const stepResult = await this.executeStep(step, agent, stepContext);
                stepResults.set(step.id, stepResult);
                outputs.push(`Step ${step.id}: ${stepResult}`);
                executedSteps.add(step.id);
                // Check if this step produced artifacts
                if (step.type === 'implementation') {
                    artifacts.push(`Step ${step.id} output`);
                }
            }
            catch (error) {
                const errorMsg = `Step ${step.id} failed: ${error instanceof Error ? error.message : String(error)}`;
                logger_1.logger.error(errorMsg);
                errors.push(errorMsg);
            }
        }
        return {
            success: errors.length === 0,
            output: outputs.join('\n\n'),
            artifacts,
            validationResults,
            timeSpent: 0, // Will be set by caller
            errors: errors.length > 0 ? errors : undefined
        };
    }
    /**
       * Phase 2: Get appropriate agent for a task step
       */
    async getAgentForStep(step) {
        // Try to get existing agent for the step type
        let agent = this.getModeAgent(step.agent);
        if (!agent) {
            // Create a new agent for this step type
            agent = await this.createModeAgent(step.agent, `${step.agent.charAt(0).toUpperCase() + step.agent.slice(1)} Agent`, `Agent for ${step.agent} tasks`);
        }
        return agent;
    }
    /**
       * Phase 2: Execute individual task step
       */
    async executeStep(step, agent, context) {
        const stepPrompt = `
        # Task Step Execution

        ## Step Details:
        - ID: ${step.id}
        - Description: ${step.description}
        - Type: ${step.type}
        - Expected Output: ${step.expectedOutput}

        ## Original Requirements:
        ${context.originalRequirements}

        ## Previous Results:
        ${Array.from(context.previousResults.entries()).map((entry) => `${entry[0]}: ${entry[1]}`).join('\n')}

        ## Available Tools:
        ${step.tools.join(', ')}

        ## Your Task:
        Execute this specific step according to its description and type.
        Use the available tools as needed.
        Provide clear output that matches the expected output format.
        `;
        const result = await this.delegateToAgent(agent, stepPrompt, context);
        return result;
    }
    /**
       * Phase 2: Validate task result against criteria
       */
    async validateTaskResult(result, taskPlan) {
        logger_1.logger.info('Validating task result...');
        const issues = [];
        // Basic validation
        if (!result.success) {
            issues.push('Task execution reported failure');
        }
        if (!result.output || result.output.trim().length === 0) {
            issues.push('No output produced');
        }
        if (result.errors && result.errors.length > 0) {
            issues.push(`Errors occurred: ${result.errors.join(', ')}`);
        }
        // Validate against criteria using LLM
        for (const criterion of taskPlan.validationCriteria) {
            try {
                const validationPrompt = `
                # Task Result Validation

                ## Validation Criterion:
                ${criterion}

                ## Task Output:
                ${result.output}

                ## Task Plan:
                ${JSON.stringify(taskPlan, null, 2)}

                ## Your Task:
                Determine if the task output satisfies the validation criterion.
                Respond with "PASS" if it satisfies the criterion, or "FAIL: reason" if it doesn't.
                `;
                const validationResult = await this.generate(validationPrompt);
                if (!validationResult.toLowerCase().includes('pass')) {
                    issues.push(`Validation failed for "${criterion}": ${validationResult}`);
                }
            }
            catch (error) {
                issues.push(`Validation error for "${criterion}": ${error}`);
            }
        }
        return {
            success: issues.length === 0,
            issues
        };
    }
    /**
       * Phase 2: Self-correct and retry failed task
       */
    async selfCorrectAndRetry(requirements, failedResult, validation) {
        this.currentIteration++;
        logger_1.logger.info(`Self-correction attempt ${this.currentIteration}`);
        // Analyze what went wrong and create corrective plan
        const correctionPrompt = `
        # Self-Correction Analysis

        ## Original Requirements:
        ${requirements}

        ## Failed Result:
        ${JSON.stringify(failedResult, null, 2)}

        ## Validation Issues:
        ${validation.issues.join('\n')}

        ## Your Task:
        1. Analyze what went wrong in the previous execution
        2. Identify the root causes of the failures
        3. Create a corrective action plan
        4. Re-execute the task with improvements

        Focus on addressing the specific validation issues identified.
        `;
        const correctionAnalysis = await this.generate(correctionPrompt);
        logger_1.logger.info(`Correction analysis: ${correctionAnalysis.substring(0, 200)}...`);
        // Create a new task plan based on the correction analysis
        const correctedPlan = await this.createTaskPlan(`${requirements}\n\nCorrection needed based on previous failure:\n${correctionAnalysis}`, 'agent');
        // Execute the corrected plan
        return await this.executeTaskPlan(correctedPlan, requirements);
    }
    /**
       * Phase 2: Learn from task execution for future improvements
       */
    async learnFromExecution(taskPlan, result, validation) {
        logger_1.logger.info('Learning from task execution...');
        const learningKey = `${taskPlan.complexity}_${taskPlan.description.substring(0, 50)}`;
        const learningData = {
            taskPlan,
            result,
            validation,
            iterations: this.currentIteration,
            timestamp: Date.now(),
            success: result.success && Boolean(validation.success)
        };
        this.learningMemory.set(learningKey, learningData);
        // Store learning in agent memory for persistence
        if (this.getMemory) {
            try {
                await this.getMemory().addMemory({
                    content: `Task execution learning: ${taskPlan.description}`,
                    metadata: {
                        source: 'agent',
                        type: 'insight',
                        data: JSON.stringify(learningData),
                        tags: ['learning', 'autonomous', taskPlan.complexity]
                    }
                });
            }
            catch (error) {
                logger_1.logger.warn(`Failed to store learning data: ${error}`);
            }
        }
    }
    /**
       * Phase 2: Determine if request should use autonomous mode
       */
    shouldUseAutonomousMode(prompt, mode) {
        // Use autonomous mode for agent mode and complex requests
        if (mode !== 'agent') {
            return false;
        }
        // Check for complexity indicators
        const complexityIndicators = [
            'implement', 'create', 'build', 'develop', 'refactor',
            'fix bug', 'debug', 'optimize', 'analyze', 'generate',
            'multiple files', 'entire project', 'complete solution'
        ];
        const promptLower = prompt.toLowerCase();
        const hasComplexityIndicators = complexityIndicators.some(indicator => promptLower.includes(indicator));
        // Use autonomous mode for complex requests or when explicitly requested
        return hasComplexityIndicators || promptLower.includes('autonomous');
    }
    /**
       * Phase 2: Find similar task in learning memory for optimization
       */
    findSimilarTaskInMemory(requirements) {
        const requirementsLower = requirements.toLowerCase();
        const threshold = 0.7; // Similarity threshold
        for (const [key, learningData] of Array.from(this.learningMemory.entries())) {
            const similarity = this.calculateTextSimilarity(requirementsLower, learningData.taskPlan.description.toLowerCase());
            if (similarity > threshold && learningData.success) {
                logger_1.logger.info(`Found similar task '${key}' with ${(similarity * 100).toFixed(1)}% similarity`);
                return learningData;
            }
        }
        return null;
    }
    /**
       * Phase 2: Optimize task plan based on memory
       */
    async optimizeTaskPlanFromMemory(requirements, mode, similarTask) {
        const baseTaskPlan = similarTask.taskPlan;
        // Create optimized plan based on successful patterns for the specific mode
        const optimizedPlan = {
            id: `optimized_task_${mode}_${Date.now()}`,
            description: requirements.substring(0, 100),
            steps: baseTaskPlan.steps.map((step) => ({
                ...step,
                id: `${step.id}_optimized_${mode}_${Date.now()}`,
                description: step.description // Keep successful step patterns
            })),
            dependencies: baseTaskPlan.dependencies || [],
            estimatedTime: Math.max(baseTaskPlan.estimatedTime * 0.8, 60000), // 20% faster estimate
            complexity: baseTaskPlan.complexity,
            validationCriteria: baseTaskPlan.validationCriteria || [`${mode} task completed successfully`]
        };
        logger_1.logger.info('Optimized task plan created from successful memory pattern');
        return optimizedPlan;
    }
    /**
       * Phase 2: Calculate text similarity for memory optimization
       */
    calculateTextSimilarity(text1, text2) {
        // Simple word-based similarity calculation
        const words1 = text1.split(/\s+/).filter(w => w.length > 2);
        const words2 = text2.split(/\s+/).filter(w => w.length > 2);
        if (words1.length === 0 || words2.length === 0)
            return 0;
        const commonWords = words1.filter(word => words2.includes(word));
        const totalWords = new Set([...words1, ...words2]).size;
        return commonWords.length / totalWords;
    }
    /**
     * Get list of available agents for delegation
     */
    _getAvailableAgents() {
        return [
            'developer',
            'architect',
            'tester',
            'documenter',
            'refactor',
            'debug',
            'research',
            'ui-ux'
        ];
    }
}
exports.SupervisorAgent = SupervisorAgent;
// Export the SupervisorAgent class as default
exports.default = SupervisorAgent;
//# sourceMappingURL=supervisorAgent.js.map