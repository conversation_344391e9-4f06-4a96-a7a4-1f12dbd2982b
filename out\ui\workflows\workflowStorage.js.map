{"version": 3, "file": "workflowStorage.js", "sourceRoot": "", "sources": ["../../../src/ui/workflows/workflowStorage.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,uCAAyB;AACzB,2CAA6B;AAE7B,MAAM,kBAAkB,GAAG,wBAAwB,CAAC;AAEpD,MAAa,eAAe;IAC1B,MAAM,CAAC,cAAc,CAAC,OAAgC;QACpD,iEAAiE;QACjE,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,EAAE,MAAM,IAAI,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAClF,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAgC;QACzD,MAAM,QAAQ,GAAG,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC;YACH,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,MAAM,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAgC,EAAE,SAAgB;QAC3E,MAAM,QAAQ,GAAG,eAAe,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC;YACH,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1D,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;CACF;AA7BD,0CA6BC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as fs from 'fs';\nimport * as path from 'path';\n\nconst WORKFLOWS_FILENAME = 'codessa_workflows.json';\n\nexport class WorkflowStorage {\n  static getStoragePath(context: vscode.ExtensionContext): string {\n    // Prefer workspace storage, fallback to extension global storage\n    const storagePath = context.storageUri?.fsPath || context.globalStorageUri.fsPath;\n    return path.join(storagePath, WORKFLOWS_FILENAME);\n  }\n\n  static async loadWorkflows(context: vscode.ExtensionContext): Promise<any[]> {\n    const filePath = WorkflowStorage.getStoragePath(context);\n    try {\n      if (fs.existsSync(filePath)) {\n        const raw = fs.readFileSync(filePath, 'utf8');\n        return JSON.parse(raw);\n      }\n    } catch (err) {\n      console.error('Failed to load workflows:', err);\n    }\n    return [];\n  }\n\n  static async saveWorkflows(context: vscode.ExtensionContext, workflows: any[]): Promise<void> {\n    const filePath = WorkflowStorage.getStoragePath(context);\n    try {\n      fs.mkdirSync(path.dirname(filePath), { recursive: true });\n      fs.writeFileSync(filePath, JSON.stringify(workflows, null, 2), 'utf8');\n    } catch (err) {\n      console.error('Failed to save workflows:', err);\n    }\n  }\n}\n"]}