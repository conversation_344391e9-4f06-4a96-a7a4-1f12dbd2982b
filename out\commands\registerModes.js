"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerModes = registerModes;
const operationMode_1 = require("../agents/agentModes/operationMode");
const askMode_1 = require("../agents/agentModes/askMode");
const chatMode_1 = require("../agents/agentModes/chatMode");
const debugMode_1 = require("../agents/agentModes/debugMode");
const editMode_1 = require("../agents/agentModes/editMode");
const agentMode_1 = require("../agents/agentModes/agentMode");
const multiAgentMode_1 = require("../agents/agentModes/multiAgentMode");
const researchMode_1 = require("../agents/agentModes/researchMode");
const documentationMode_1 = require("../agents/agentModes/documentationMode");
const refactorMode_1 = require("../agents/agentModes/refactorMode");
const logger_1 = require("../logger");
/**
 * Register all operation modes
 */
async function registerModes(context) {
    try {
        logger_1.logger.info('Registering operation modes...');
        // Register modes
        operationMode_1.operationModeRegistry.registerMode(new askMode_1.AskMode('ask', 'Ask', 'Ask questions about your codebase', '$(question)', operationMode_1.ContextType.ENTIRE_CODEBASE));
        operationMode_1.operationModeRegistry.registerMode(new chatMode_1.ChatMode('chat', 'Chat', 'General chat with the AI assistant', '$(comment)', operationMode_1.ContextType.NONE));
        operationMode_1.operationModeRegistry.registerMode(new debugMode_1.DebugMode('debug', 'Debug', 'Debug issues with your code', '$(bug)', operationMode_1.ContextType.SELECTED_FILES));
        operationMode_1.operationModeRegistry.registerMode(new editMode_1.EditMode('edit', 'Edit', 'AI-assisted code editing with human verification', '$(edit)', operationMode_1.ContextType.SELECTED_FILES));
        operationMode_1.operationModeRegistry.registerMode(new agentMode_1.AgentMode('agent', 'Agent', 'Autonomous AI agent that completes tasks with minimal user interaction', '$(robot)', operationMode_1.ContextType.ENTIRE_CODEBASE));
        operationMode_1.operationModeRegistry.registerMode(new multiAgentMode_1.MultiAgentMode('multi-agent', 'Multi-Agent', 'Team of AI agents working together on complex tasks', '$(organization)', operationMode_1.ContextType.ENTIRE_CODEBASE));
        operationMode_1.operationModeRegistry.registerMode(new researchMode_1.ResearchMode('research', 'Research', 'Comprehensive research and analysis of topics', '$(search)', operationMode_1.ContextType.ENTIRE_CODEBASE));
        operationMode_1.operationModeRegistry.registerMode(new documentationMode_1.DocumentationMode('documentation', 'Documentation', 'Generate comprehensive documentation for code', '$(book)', operationMode_1.ContextType.SELECTED_FILES));
        operationMode_1.operationModeRegistry.registerMode(new refactorMode_1.RefactorMode('refactor', 'Refactor', 'Refactor and optimize code structure', '$(tools)', operationMode_1.ContextType.SELECTED_FILES));
        // Set default mode
        operationMode_1.operationModeRegistry.setDefaultMode('chat');
        // Initialize modes
        await operationMode_1.operationModeRegistry.initializeModes(context);
        logger_1.logger.info('Operation modes registered successfully');
    }
    catch (error) {
        logger_1.logger.error('Error registering operation modes:', error);
        throw error;
    }
}
//# sourceMappingURL=registerModes.js.map