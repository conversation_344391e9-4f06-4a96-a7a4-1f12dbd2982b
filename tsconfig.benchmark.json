{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "outDir": "./out/benchmark", "rootDir": ".", "baseUrl": ".", "paths": {"vscode": ["test/mocks/vscode"], "src/*": ["src/*"], "test/*": ["test/*"]}}, "include": ["src/**/*.ts", "test/benchmark/**/*.ts", "test/mocks/**/*.ts"], "exclude": ["node_modules", "**/node_modules/*", "**/out", "**/.vscode-test"]}