import * as vscode from 'vscode';

/**
 * Helper functions for workspace context
 */
export class WorkspaceHelper {
  /**
   * Create workspace context with proper types
   */
  public static createWorkspaceContext() {
    const editor = vscode.window.activeTextEditor;
    
    return {
      currentFile: editor?.document.uri.toString(),
      selection: editor ? { 
        text: editor.document.getText(editor.selection) || '',
        range: { 
          start: editor.document.offsetAt(editor.selection.start), 
          end: editor.document.offsetAt(editor.selection.end) 
        }
      } : undefined,
      workspaceFolders: Array.from(vscode.workspace.workspaceFolders ?? [])
        .map(folder => folder.uri.toString())
    };
  }
  
  /**
   * Get the text content of the current file
   */
  public static getCurrentFileText(): string | undefined {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return undefined;
    }
    
    return editor.document.getText();
  }
  
  /**
   * Get the path of the current file
   */
  public static getCurrentFilePath(): string | undefined {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return undefined;
    }
    
    return editor.document.uri.fsPath;
  }
}