{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--disable-extensions", "--enable-proposed-api=codessa.codessa"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "npm: compile", "env": {"VSCODE_DEBUG_MODE": "true"}, "skipFiles": ["<node_internals>/**"], "sourceMaps": true}, {"name": "Extension Tests", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--extensionTestsPath=${workspaceFolder}/out/test/suite/index"], "outFiles": ["${workspaceFolder}/out/test/**/*.js"], "preLaunchTask": "npm: watch"}]}