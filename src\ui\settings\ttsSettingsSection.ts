import * as vscode from 'vscode';
import { TTSSettings<PERSON>anager, TTS<PERSON>rovider, TTSVoice } from './ttsSettings';

export class TTSSettingsView {
  private static instance: TTSSettingsView;
  private _panel: vscode.WebviewPanel | undefined;
  private _settingsManager: TTSSettingsManager;

  private constructor(private readonly context: vscode.ExtensionContext) {
    this._settingsManager = TTSSettingsManager.getInstance(context);
  }

  public static getInstance(context: vscode.ExtensionContext): TTSSettingsView {
    if (!TTSSettingsView.instance) {
      TTSSettingsView.instance = new TTSSettingsView(context);
    }
    return TTSSettingsView.instance;
  }

  public show() {
    if (this._panel) {
      this._panel.reveal();
      return;
    }

    this._panel = vscode.window.createWebviewPanel(
      'ttsSettings',
      'TTS Settings',
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true
      }
    );

    this._panel.webview.html = this._getWebviewContent();
    this._panel.onDidDispose(() => {
      this._panel = undefined;
    });

    this._panel.webview.onDidReceiveMessage(async (message) => {
      switch (message.type) {
      case 'updateSettings':
        await this._settingsManager.updateSettings(message.settings);
        break;
      case 'downloadVoice':
        await this._settingsManager.downloadVoice(message.voice);
        break;
      case 'refreshVoices':
        await this._refreshVoices();
        break;
      }
    });
  }

  private _getWebviewContent(): string {
    const settings = this._settingsManager.getSettings();
    const voices = this._getVoicesByProvider();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS Settings</title>
    <style>
        body {
            padding: 20px;
            color: var(--vscode-foreground);
            font-family: var(--vscode-font-family);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
        }
        .section-title {
            margin: 0 0 10px 0;
            font-size: 1.2em;
            color: var(--vscode-titleBar-activeForeground);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        select, input[type="range"] {
            width: 100%;
            padding: 5px;
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
        }
        .voice-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid var(--vscode-panel-border);
            padding: 10px;
        }
        .voice-item {
            padding: 8px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .voice-item:last-child {
            border-bottom: none;
        }
        .download-btn {
            padding: 4px 8px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            cursor: pointer;
        }
        .download-btn:hover {
            background: var(--vscode-button-hoverBackground);
        }
        .provider-section {
            margin-top: 10px;
            padding: 10px;
            background: var(--vscode-editor-inactiveSelectionBackground);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="section">
        <h2 class="section-title">General Settings</h2>
        <div class="form-group">
            <label>
                <input type="checkbox" id="enabled" ${settings.enabled ? 'checked' : ''}>
                Enable TTS
            </label>
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" id="autoSpeak" ${settings.autoSpeak ? 'checked' : ''}>
                Auto-speak assistant messages
            </label>
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Voice Settings</h2>
        <div class="form-group">
            <label for="provider">TTS Provider</label>
            <select id="provider">
                ${Object.values(TTSProvider).map(provider => `
                    <option value="${provider}" ${settings.provider === provider ? 'selected' : ''}>
                        ${provider.charAt(0).toUpperCase() + provider.slice(1)}
                    </option>
                `).join('')}
            </select>
        </div>

        <div class="form-group">
            <label for="rate">Speech Rate</label>
            <input type="range" id="rate" min="0.5" max="2" step="0.1" value="${settings.rate}">
            <span id="rateValue">${settings.rate}x</span>
        </div>

        <div class="form-group">
            <label for="pitch">Pitch</label>
            <input type="range" id="pitch" min="0.5" max="2" step="0.1" value="${settings.pitch}">
            <span id="pitchValue">${settings.pitch}</span>
        </div>

        <div class="form-group">
            <label for="volume">Volume</label>
            <input type="range" id="volume" min="0" max="1" step="0.1" value="${settings.volume}">
            <span id="volumeValue">${settings.volume * 100}%</span>
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Available Voices</h2>
        <div class="form-group">
            <label for="voice">Selected Voice</label>
            <select id="voice">
                ${this._getVoiceOptions(settings.provider, settings.voice)}
            </select>
        </div>

        <div class="voice-list">
            ${this._getVoiceList(voices)}
        </div>
    </div>

    <script>
        (function() {
            const vscode = acquireVsCodeApi();
            const settings = ${JSON.stringify(settings)};

            function updateSettings() {
                const newSettings = {
                    enabled: document.getElementById('enabled').checked,
                    provider: document.getElementById('provider').value,
                    voice: document.getElementById('voice').value,
                    rate: parseFloat(document.getElementById('rate').value),
                    pitch: parseFloat(document.getElementById('pitch').value),
                    volume: parseFloat(document.getElementById('volume').value),
                    autoSpeak: document.getElementById('autoSpeak').checked
                };
                vscode.postMessage({ type: 'updateSettings', settings: newSettings });
            }

            // Event listeners
            document.getElementById('enabled').addEventListener('change', updateSettings);
            document.getElementById('autoSpeak').addEventListener('change', updateSettings);
            document.getElementById('provider').addEventListener('change', () => {
                vscode.postMessage({ type: 'refreshVoices' });
                updateSettings();
            });
            document.getElementById('voice').addEventListener('change', updateSettings);
            document.getElementById('rate').addEventListener('input', (e) => {
                document.getElementById('rateValue').textContent = e.target.value + 'x';
                updateSettings();
            });
            document.getElementById('pitch').addEventListener('input', (e) => {
                document.getElementById('pitchValue').textContent = e.target.value;
                updateSettings();
            });
            document.getElementById('volume').addEventListener('input', (e) => {
                document.getElementById('volumeValue').textContent = (e.target.value * 100) + '%';
                updateSettings();
            });

            // Download voice buttons
            document.querySelectorAll('.download-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const voiceId = e.target.dataset.voiceId;
                    vscode.postMessage({ type: 'downloadVoice', voice: voiceId });
                });
            });
        })();
    </script>
</body>
</html>`;
  }

  private _getVoicesByProvider(): Map<TTSProvider, TTSVoice[]> {
    const voices = new Map<TTSProvider, TTSVoice[]>();
    Object.values(TTSProvider).forEach(provider => {
      voices.set(provider, this._settingsManager.getVoices(provider));
    });
    return voices;
  }

  private _getVoiceOptions(provider: TTSProvider, selectedVoice: string): string {
    const voices = this._settingsManager.getVoices(provider);
    return voices.map(voice => `
            <option value="${voice.id}" ${voice.id === selectedVoice ? 'selected' : ''}>
                ${voice.name} (${voice.language})
            </option>
        `).join('');
  }

  private _getVoiceList(voices: Map<TTSProvider, TTSVoice[]>): string {
    let html = '';
    voices.forEach((providerVoices, provider) => {
      if (providerVoices.length > 0) {
        html += `
                    <div class="provider-section">
                        <h3>${provider.charAt(0).toUpperCase() + provider.slice(1)} Voices</h3>
                        ${providerVoices.map(voice => `
                            <div class="voice-item">
                                <div>
                                    <strong>${voice.name}</strong>
                                    <span>${voice.language}</span>
                                    ${voice.gender ? `<span>(${voice.gender})</span>` : ''}
                                </div>
                                <button class="download-btn" data-voice-id="${voice.id}">
                                    Download
                                </button>
                            </div>
                        `).join('')}
                    </div>
                `;
      }
    });
    return html;
  }

  private async _refreshVoices(): Promise<void> {
    if (this._panel) {
      this._panel.webview.html = this._getWebviewContent();
    }
  }
} 