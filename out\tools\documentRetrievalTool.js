"use strict";
/**
 * Enhanced document retrieval tool with context support and schema validation.
 * Wraps a base retrieval tool to provide consistent behavior and type safety.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentRetrievalTool = void 0;
const zod_1 = require("zod");
const tool_ts_backup_1 = require("./tool.ts.backup");
/**
 * Local logger implementation for internal tool logging
 */
class Logger {
    static instance;
    /**
     * Get the singleton instance of the logger
     */
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    /**
     * Log an error message
     * @param message The error message
     * @param args Additional error data
     */
    error(message, ...args) {
        console.error(`[ERROR] ${message}`, ...args);
    }
    /**
     * Log a debug message
     * @param message The debug message
     * @param args Additional debug data
     */
    debug(message, ...args) {
        if (process.env.NODE_ENV === 'development') {
            console.debug(`[DEBUG] ${message}`, ...args);
        }
    }
}
/**
 * Enhanced wrapper for document retrieval tools with context awareness
 */
class DocumentRetrievalTool extends tool_ts_backup_1.BaseTool {
    _baseTool;
    _defaultOptions;
    type = 'single-action';
    category = 'retrieval';
    config = {};
    metadata = {};
    version = '1.0.0';
    schema = zod_1.z.union([
        // Simple string query
        zod_1.z.string()
            .min(1, 'Query cannot be empty')
            .describe('The query string to search for documents.'),
        // Or an object with query and options
        zod_1.z.object({
            query: zod_1.z.string()
                .min(1, 'Query cannot be empty')
                .describe('The query string to search for documents.'),
            options: zod_1.z.object({
                limit: zod_1.z.number()
                    .int()
                    .positive()
                    .max(100, 'Maximum limit is 100')
                    .optional()
                    .describe('Maximum number of documents to return'),
                minScore: zod_1.z.number()
                    .min(0)
                    .max(1)
                    .optional()
                    .describe('Minimum relevance score (0-1)'),
                includeMetadata: zod_1.z.boolean()
                    .optional()
                    .describe('Whether to include document metadata'),
                filters: zod_1.z.record(zod_1.z.unknown())
                    .optional()
                    .describe('Additional filters for document retrieval'),
                semanticSearch: zod_1.z.boolean()
                    .optional()
                    .describe('Whether to use semantic search (true) or keyword search (false)')
            }).strict().optional()
        }).strict()
    ]).transform((input) => {
        // Normalize input to always return a query string and options
        if (typeof input === 'string') {
            return { query: input, options: {} };
        }
        return { query: input.query, options: input.options || {} };
    });
    inputSchema = this.schema;
    outputSchema = zod_1.z.object({
        success: zod_1.z.boolean(),
        output: zod_1.z.any(),
        error: zod_1.z.string().optional(),
        toolId: zod_1.z.string(),
        actionName: zod_1.z.string().optional()
    });
    // Class property for description to satisfy BaseTool's abstract requirement
    description;
    /**
     * Creates a new DocumentRetrievalTool instance
     * @param baseTool - The underlying tool to wrap (must implement ITool)
     * @param options - Default options for document retrieval
     * @param name - Optional custom name for the tool (default: 'document-retrieval-tool')
     * @param description - Optional custom description
     * @throws {Error} If baseTool is not provided or invalid
     */
    constructor(baseTool, options = {}, name = 'document-retrieval-tool', description = 'Retrieves relevant documents based on a query with contextual enhancement.') {
        super(name);
        if (!baseTool || typeof baseTool.execute !== 'function') {
            throw new Error('A valid base tool implementing ITool is required');
        }
        this._baseTool = baseTool;
        this._defaultOptions = { ...options };
        this.description = description;
    }
    async execute(actionNameOrInput, inputOrContext, maybeContext) {
        // Handle both method signatures
        let actionName;
        let input;
        let context;
        if (typeof actionNameOrInput === 'string' || actionNameOrInput === undefined) {
            actionName = actionNameOrInput;
            input = inputOrContext;
            context = maybeContext;
        }
        else {
            input = actionNameOrInput;
            context = inputOrContext;
        }
        try {
            // Update context if provided
            if (context) {
                this.updateContext(context);
            }
            // Set default action name if not provided
            const resolvedActionName = actionName || 'retrieve';
            // Validate and normalize input
            const normalizedInput = await this.normalizeInput(input);
            // Execute the base tool with the validated input
            const result = await this.executeBaseTool(normalizedInput, input);
            // Create and return a success result
            return this.createSuccessResult(result, normalizedInput, resolvedActionName);
        }
        catch (error) {
            // Create and return an error result
            const resolvedActionName = actionName || input?.action || 'retrieve';
            return this.createErrorResult(this.getErrorMessage(error), input, resolvedActionName);
        }
    }
    /**
     * Validates the input against the schema
     * @param input - The input to validate
     * @returns Validation result with validity and optional error message
     */
    validateInput(input) {
        const validation = this.schema.safeParse(input);
        if (validation.success) {
            return { valid: true };
        }
        else {
            const errorMessage = validation.error.errors
                .map(e => `${e.path.join('.')}: ${e.message}`)
                .join('; ');
            Logger.getInstance().error('Input validation failed:', errorMessage);
            return { valid: false, error: errorMessage };
        }
    }
    /**
     * Validates and normalizes the input against the schema
     * @param input - The input to validate and normalize
     * @returns Normalized input with query and options
     * @throws {Error} If validation fails
     */
    async normalizeInput(input) {
        try {
            return await this.schema.parseAsync(input);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Invalid input format';
            throw new Error(`Invalid input: ${errorMessage}`);
        }
    }
    /**
     * Executes the base tool with the validated input
     * @param validated - The validated input
     * @param originalInput - The original input for context
     * @returns The result from the base tool
     */
    async executeBaseTool(validated, originalInput) {
        try {
            // Merge with default options
            const options = { ...this._defaultOptions, ...validated.options };
            // Prepare the input for the base tool, ensuring it matches ToolInput interface
            const baseInput = {
                ...originalInput,
                query: validated.query,
                options,
                // Ensure required fields are present
                action: 'retrieve',
                toolId: this._baseTool.id
            };
            // Execute the base tool with the proper input format
            if (this._baseTool.type === 'multi-action') {
                return await this._baseTool.execute('retrieve', baseInput);
            }
            else {
                return await this._baseTool.execute(baseInput);
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error executing base tool';
            Logger.getInstance().error('Error in executeBaseTool:', errorMessage);
            throw error;
        }
    }
    /**
     * Creates a success result object
     */
    createSuccessResult(result, validated, actionName) {
        return {
            success: true,
            output: result,
            toolId: this.id,
            actionName,
            metadata: {
                query: validated.query,
                options: validated.options
            }
        };
    }
    /**
     * Creates an error result object
     */
    createErrorResult(errorMessage, input, actionName) {
        return {
            success: false,
            error: errorMessage,
            toolId: this.id,
            actionName,
            metadata: {
                error: errorMessage,
                input
            }
        };
    }
    /**
     * Extracts error message from unknown error type
     */
    getErrorMessage(error) {
        if (error instanceof Error) {
            return error.message;
        }
        if (typeof error === 'string') {
            return error;
        }
        return 'An unknown error occurred';
    }
    /**
     * Gets the tool definition in a format suitable for consumption by LLMs
     * @returns The tool definition in a standardized format
     */
    getDefinitionForModel() {
        return {
            name: this.name,
            description: this.description,
            parameters: {
                type: 'object',
                properties: {
                    query: {
                        type: 'string',
                        description: 'The search query'
                    },
                    options: {
                        type: 'object',
                        properties: {
                            limit: {
                                type: 'number',
                                description: 'Maximum number of results to return (max: 100)'
                            },
                            minScore: {
                                type: 'number',
                                minimum: 0,
                                maximum: 1,
                                description: 'Minimum relevance score (0-1)'
                            },
                            includeMetadata: {
                                type: 'boolean',
                                description: 'Whether to include document metadata'
                            },
                            filters: {
                                type: 'object',
                                additionalProperties: true,
                                description: 'Additional filters for document retrieval'
                            },
                            semanticSearch: {
                                type: 'boolean',
                                description: 'Whether to use semantic search (true) or keyword search (false)'
                            }
                        }
                    }
                },
                required: ['query']
            }
        };
    }
    /**
     * Updates the context for the tool
     * @param context - The new agent context (optional)
     */
    updateContext(context) {
        if (context) {
            super.setContext(context);
        }
        else {
            // If context is undefined, clear the current context
            super.setContext(undefined);
        }
    }
    /**
     * Gets the current context
     * @returns The current agent context or undefined if not set
     */
    getContext() {
        return super.getContext();
    }
    /**
     * Cleans up any resources used by the tool
     * @returns A promise that resolves when cleanup is complete
     */
    async dispose() {
        try {
            if (typeof this._baseTool.dispose === 'function') {
                await this._baseTool.dispose();
            }
        }
        catch (error) {
            const errorMessage = this.getErrorMessage(error);
            Logger.getInstance().error('Error during tool disposal:', errorMessage);
            // Don't throw from dispose to prevent masking other errors
        }
    }
}
exports.DocumentRetrievalTool = DocumentRetrievalTool;
//# sourceMappingURL=documentRetrievalTool.js.map