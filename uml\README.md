# Codessa VS Code Extension - UML Architecture

This directory contains UML diagrams representing the architecture of the Codessa VS Code Extension. The diagrams are created using StarUML and stored in the `.mdj` format.

## Core Architecture

The `memory.mdj` file contains the core architecture diagram of the Codessa VS Code Extension. It shows the main components and their relationships.

### Key Components

#### Extension Core
- **Extension**: The main entry point for the VS Code extension. It initializes all the other components and registers the UI and commands.

#### Agent System
- **Agent**: Represents an AI agent that can execute tasks using LLM providers and tools.
- **AgentManager**: Singleton class that manages all agents in the system.
- **SupervisorAgent**: A specialized agent that can delegate tasks to other agents.

#### LLM Integration
- **LLMService**: Manages LLM providers and provides a unified interface for generating text.
- **ILLMProvider**: Interface for LLM providers (OpenAI, Anthropic, Ollama, etc.).
- **BaseLLMProvider**: Abstract base class for LLM providers.

#### Memory System
- **MemoryManager**: Manages different memory providers and provides a unified interface for storing and retrieving memories.
- **IMemoryProvider**: Interface for memory providers.
- **VectorMemoryManager**: Manages vector-based memory storage.
- **CodessaGraphMemory**: Graph-based memory system specific to Codessa.

#### Tools
- **ITool**: Interface for tools that agents can use.
- **ToolRegistry**: Singleton class that manages all tools in the system.

#### Workflows
- **WorkflowEngine**: Singleton class that manages workflows.
- **Workflow**: Represents a sequence of steps that can be executed.

#### UI Components
- **ChatViewProvider**: Provides the chat interface for interacting with agents.
- **AllSettingsPanel**: Provides a UI for configuring all settings.
- **AgentTreeDataProvider**: Provides data for the agent tree view.

### Relationships

The diagram shows the following key relationships:

1. **Extension** initializes and uses:
   - AgentManager
   - LLMService
   - MemoryManager
   - WorkflowEngine
   - ToolRegistry
   - ChatViewProvider
   - AgentTreeDataProvider

2. **Agent** uses:
   - LLMService for generating text
   - ToolRegistry for accessing tools
   - MemoryManager for storing and retrieving memories

3. **SupervisorAgent** extends **Agent** and can delegate tasks to other agents.

4. **Workflow** uses **Agent** to execute tasks.

5. **AgentTreeDataProvider** uses **AgentManager** to get information about agents.

## How to View and Edit the Diagrams

1. Install [StarUML](https://staruml.io/)
2. Open the `.mdj` files in StarUML
3. Edit the diagrams as needed

## Diagram Conventions

- **Classes** are represented as rectangles with the class name at the top.
- **Interfaces** are represented as rectangles with the interface name at the top and the «interface» stereotype.
- **Singletons** are represented with the «singleton» stereotype.
- **Associations** are represented as lines connecting classes.
- **Generalizations** (inheritance) are represented as lines with a hollow triangle pointing to the parent class.
- **Realizations** (implementation) are represented as dashed lines with a hollow triangle pointing to the interface.

## Code Structure

The codebase is organized into the following directories:

- **src/agents**: Contains the agent system components.
- **src/llm**: Contains the LLM integration components.
- **src/memory**: Contains the memory system components.
- **src/tools**: Contains the tools that agents can use.
- **src/workflows**: Contains the workflow system components.
- **src/ui**: Contains the UI components.
- **src/commands**: Contains the VS Code commands.
- **src/checkpoint**: Contains the checkpoint system components.
- **src/credentials**: Contains the credentials management components.
- **src/diff**: Contains the diff engine components.
- **src/mcp**: Contains the MCP (Master Control Program) components.
- **src/modes**: Contains the operation modes components.
- **src/prompts**: Contains the prompt management components.

## Future Improvements

- Add sequence diagrams for key operations
- Add activity diagrams for complex workflows
- Add component diagrams for high-level architecture
- Add deployment diagrams for production environments
