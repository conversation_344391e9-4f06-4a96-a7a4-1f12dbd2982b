{"version": 3, "file": "codeSearchTool.js", "sourceRoot": "", "sources": ["../../src/tools/codeSearchTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,kDAAoC;AACpC,qEAAkG;AAClG,6BAAwB;AAExB,MAAa,cAAc;IAChB,EAAE,GAAG,YAAY,CAAC;IAClB,IAAI,GAAG,kCAAkC,CAAC;IAC1C,WAAW,GAAG,oGAAoG,CAAC;IACnH,IAAI,GAAG,cAAc,CAAC;IACtB,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QACzB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;QAC3D,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QAC9C,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;QACtE,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QAC7E,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;QACxE,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,gCAAgC,CAAC;QACtE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kCAAkC,CAAC;KAC5E,CAAC,CAAC;IACM,OAAO,GAAwB;QACtC,OAAO,EAAE;YACP,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,mBAAmB;YACzB,WAAW,EAAE,+DAA+D;YAC5E,IAAI,EAAE,eAAe,EAAE,oBAAoB;YAC3C,OAAO,EAAE,EAAE,EAAE,oBAAoB;YACjC,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;gBAC3B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC1D,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0DAA0D,CAAC;aACpG,CAAC;YACF,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8BAA8B,EAAE;oBACtE,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0DAA0D,EAAE,OAAO,EAAE,GAAG,EAAE;iBACnH;gBACD,QAAQ,EAAE,CAAC,OAAO,CAAC;aACpB;YACD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;gBACrF,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;gBACpC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,IAAI,GAAG,CAAC;gBAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;gBAC1F,CAAC;gBACD,IAAI,GAAG,GAAG,OAAO,CAAC;gBAClB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;oBACtE,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,aAAa,IAAI,OAAO,EAAE,CAAC;gBACpG,CAAC;gBACD,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBAC3D,EAAE,CAAC,IAAI,CAAC,gDAAgD,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;4BACjG,IAAI,GAAG,IAAI,CAAC,MAAM;gCAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;4BACzD,OAAO,CAAC,MAAM,CAAC,CAAC;wBAClB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;oBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;gBAC/E,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;gBACjH,CAAC;YACH,CAAC;SACF;QACD,aAAa,EAAE;YACb,GAAG,IAAI,wCAAe,EAAE;YACxB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;gBACrF,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;oBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;wBACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;oBAChG,CAAC;oBAED,MAAM,OAAO,GAAG,KAAK,CAAC,OAAiB,IAAI,GAAG,CAAC;oBAC/C,IAAI,GAAG,GAAG,OAAO,CAAC;oBAElB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACtF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;wBACtE,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,aAAa,IAAI,OAAO,EAAE,CAAC;oBACpG,CAAC;yBAAM,CAAC;wBACN,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,oEAAoE;4BAC3E,MAAM,EAAE,aAAa;4BACrB,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,wCAAwC;oBACxC,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBAC3D,4CAA4C;wBAC5C,EAAE,CAAC,IAAI,CAAC,6DAA6D,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;4BAC9G,IAAI,GAAG,IAAI,CAAC,MAAM;gCAAE,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;4BACzD,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;oBAEH,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;wBACnB,OAAO;4BACL,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,6CAA6C,KAAK,GAAG;4BAC7D,MAAM,EAAE,aAAa;4BACrB,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;wBACrB,MAAM,EAAE,aAAa;wBACrB,UAAU;qBACX,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;wBACvD,MAAM,EAAE,aAAa;wBACrB,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;SACF;QACD,gBAAgB,EAAE;YAChB,GAAG,IAAI,2CAAkB,EAAE;YAC3B,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;gBACpF,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;oBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;wBACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC;oBACnG,CAAC;oBAED,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,IAAI,EAAE,CAAC;oBAQxC,MAAM,eAAe,GAAG,OAA+B,CAAC;oBAExD,IAAI,CAAC;wBACH,oCAAoC;wBACpC,IAAI,WAAW,CAAC;wBAEhB,IAAI,CAAC;4BACH,MAAM,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;4BAC/E,WAAW,GAAG,eAAe,EAAE,WAAW,IAAI,IAAI,iBAAiB,EAAE,CAAC;4BACtE,kEAAkE;4BAClE,IAAI,CAAC,eAAe,EAAE,WAAW,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,CAAC;gCAClE,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;gCACpC,kCAAkC;gCAClC,IAAI,eAAe,EAAE,CAAC;oCACpB,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;gCAC1C,CAAC;4BACH,CAAC;wBACH,CAAC;wBAAC,OAAO,IAAI,EAAE,CAAC;4BACd,2DAA2D;4BAC3D,IAAI,CAAC;gCACH,MAAM,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;gCACvE,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,6BAA6B,CAAC,CAAC;gCACpE,WAAW,GAAG,eAAe,EAAE,WAAW,IAAI,IAAI,iBAAiB,EAAE,CAAC;gCACtE,mDAAmD;gCACnD,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,CAAC;oCACjC,MAAM,mBAAmB,GAAG,IAAI,gBAAgB,EAAE,CAAC;oCACnD,IAAI,eAAe,EAAE,CAAC;wCACpB,eAAe,CAAC,UAAU,GAAG,mBAAmB,CAAC;oCACnD,CAAC;gCACH,CAAC;4BACH,CAAC;4BAAC,OAAO,IAAI,EAAE,CAAC;gCACd,OAAO;oCACL,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,0EAA0E;oCACjF,MAAM,EAAE,gBAAgB;oCACxB,UAAU;iCACX,CAAC;4BACJ,CAAC;wBACH,CAAC;wBAED,+DAA+D;wBAC/D,IAAI,CAAC,WAAW,CAAC,aAAa,IAAI,WAAW,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BACzE,OAAO;gCACL,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,yDAAyD;gCAChE,MAAM,EAAE,gBAAgB;gCACxB,UAAU;6BACX,CAAC;wBACJ,CAAC;wBAED,4BAA4B;wBAC5B,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;wBAE7D,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAC/B,OAAO;gCACL,OAAO,EAAE,IAAI;gCACb,MAAM,EAAE,iCAAiC;gCACzC,MAAM,EAAE,gBAAgB;gCACxB,UAAU;6BACX,CAAC;wBACJ,CAAC;wBAED,qBAAqB;wBACrB,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,KAAa,EAAE,EAAE;4BAC5D,OAAO,UAAU,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;wBAC5I,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAEd,OAAO;4BACL,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,gBAAgB;4BACxB,MAAM,EAAE,gBAAgB;4BACxB,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAAC,OAAO,GAAQ,EAAE,CAAC;wBAClB,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,2BAA2B,GAAG,CAAC,OAAO,IAAI,GAAG,EAAE;4BACtD,MAAM,EAAE,gBAAgB;4BACxB,UAAU;yBACX,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,2BAA2B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;wBAC1D,MAAM,EAAE,gBAAgB;wBACxB,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;SACF;QACD,eAAe,EAAE;YACf,GAAG,IAAI,0CAAiB,EAAE;YAC1B,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,EAAE;YACX,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;gBACrF,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;oBAC1C,qDAAqD;oBACrD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;oBAClC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAiB,IAAI,CAAC,CAAC;oBAElD,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACd,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,2BAA2B;4BAClC,MAAM,EAAE,eAAe;4BACvB,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;wBACxC,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,uBAAuB;4BAC9B,MAAM,EAAE,eAAe;4BACvB,UAAU;yBACX,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC;wBACH,oBAAoB;wBACpB,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;wBAE9D,uCAAuC;wBACvC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC,CAAC;wBAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC,CAAC;wBAE7D,gBAAgB;wBAChB,MAAM,KAAK,GAAa,EAAE,CAAC;wBAC3B,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;4BAClC,uDAAuD;4BACvD,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;4BACpC,MAAM,UAAU,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;4BAC5C,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC,CAAC;wBACnD,CAAC;wBAED,oBAAoB;wBACpB,MAAM,MAAM,GAAG;4BACb,SAAS,QAAQ,EAAE;4BACnB,SAAS,IAAI,GAAG,CAAC,EAAE;4BACnB,EAAE;4BACF,GAAG,KAAK;yBACT,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAEb,OAAO;4BACL,OAAO,EAAE,IAAI;4BACb,MAAM;4BACN,MAAM,EAAE,eAAe;4BACvB,UAAU;4BACV,QAAQ,EAAE;gCACR,QAAQ;gCACR,IAAI;gCACJ,YAAY;gCACZ,UAAU,EAAE,GAAG,GAAG,KAAK,GAAG,CAAC;6BAC5B;yBACF,CAAC;oBACJ,CAAC;oBAAC,OAAO,KAAU,EAAE,CAAC;wBACpB,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,EAAE;4BAC9C,MAAM,EAAE,eAAe;4BACvB,UAAU;yBACX,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,0BAA0B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;wBACzD,MAAM,EAAE,eAAe;wBACvB,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;SACF;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,KAAgB,EAAE,OAAsB;QACpD,4FAA4F;QAC5F,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAgB,IAAI,OAAO,CAAC;QACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oDAAoD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAChJ,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,QAAQ,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;QAC3J,CAAC;QACD,MAAM,WAAW,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;QACjC,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC5B,OAAO,WAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAED,gEAAgE;QAChE,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnC,mEAAmE;YACnE,OAAO,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,6DAA6D;YAC7D,MAAM,MAAM,GAAG,MAAO,UAAkB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACvE,6DAA6D;YAC7D,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACjC,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;YAC/B,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;CACF;AAtVD,wCAsVC;AAEY,QAAA,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport * as cp from 'child_process';\nimport { FuzzySearchTool, SemanticSearchTool, SearchPreviewTool } from './advancedCodeSearchTool';\nimport { z } from 'zod';\n\nexport class CodeSearchTool implements ITool {\n  readonly id = 'codeSearch';\n  readonly name = 'Code Search (ripgrep + advanced)';\n  readonly description = 'Searches codebase using ripgrep (rg), fuzzy search, semantic search, and provides result previews.';\n  readonly type = 'multi-action';\n  readonly schema = z.object({\n    action: z.string().describe('The search action to perform'),\n    query: z.string().describe('The search query'),\n    dirPath: z.string().optional().describe('Directory path to search in'),\n    topK: z.number().optional().describe('Number of results for semantic search'),\n    filePath: z.string().optional().describe('File path for search preview'),\n    line: z.number().optional().describe('Line number for search preview'),\n    context: z.number().optional().describe('Context lines for search preview')\n  });\n  readonly actions: Record<string, any> = {\n    'basic': {\n      id: 'basic',\n      name: 'Basic Code Search',\n      description: 'Searches codebase using ripgrep (rg). Returns matching lines.',\n      type: 'single-action', // Required by ITool\n      actions: {}, // Required by ITool\n      singleActionSchema: z.object({\n        query: z.string().describe('The search query for ripgrep'),\n        dirPath: z.string().optional().describe('Directory path to search in (default: current directory)')\n      }),\n      inputSchema: {\n        type: 'object',\n        properties: {\n          query: { type: 'string', description: 'The search query for ripgrep' },\n          dirPath: { type: 'string', description: 'Directory path to search in (default: current directory)', default: '.' }\n        },\n        required: ['query']\n      },\n      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n        const query = input.query as string;\n        const dirPath = input.dirPath as string || '.';\n        if (!query) {\n          return { success: false, error: '\\'query\\' is required.', toolId: 'basic', actionName };\n        }\n        let cwd = dirPath;\n        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n          const workspaceRoot = vscode.workspace.workspaceFolders[0].uri.fsPath;\n          cwd = dirPath.startsWith('/') || dirPath.match(/^.:\\\\/) ? dirPath : `${workspaceRoot}/${dirPath}`;\n        }\n        try {\n          const result = await new Promise<string>((resolve, reject) => {\n            cp.exec(`rg --no-heading --line-number --color never \"${query}\"`, { cwd }, (err, stdout, stderr) => {\n              if (err && !stdout) return reject(stderr || err.message);\n              resolve(stdout);\n            });\n          });\n          return { success: true, output: result.trim(), toolId: 'basic', actionName };\n        } catch (error: any) {\n          return { success: false, error: `Code search failed: ${error.message || error}`, toolId: 'basic', actionName };\n        }\n      }\n    },\n    'fuzzySearch': {\n      ...new FuzzySearchTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n        try {\n          const query = input.query as string;\n          if (!query) {\n            return { success: false, error: '\\'query\\' is required.', toolId: 'fuzzySearch', actionName };\n          }\n\n          const dirPath = input.dirPath as string || '.';\n          let cwd = dirPath;\n\n          if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n            const workspaceRoot = vscode.workspace.workspaceFolders[0].uri.fsPath;\n            cwd = dirPath.startsWith('/') || dirPath.match(/^.:\\\\/) ? dirPath : `${workspaceRoot}/${dirPath}`;\n          } else {\n            return {\n              success: false,\n              error: 'No workspace folder open. Fuzzy search requires an open workspace.',\n              toolId: 'fuzzySearch',\n              actionName\n            };\n          }\n\n          // Use ripgrep with fuzzy search options\n          const result = await new Promise<string>((resolve, reject) => {\n            // Use smart-case and fuzzy matching options\n            cp.exec(`rg --no-heading --line-number --color never --smart-case \"${query}\"`, { cwd }, (err, stdout, stderr) => {\n              if (err && !stdout) return reject(stderr || err.message);\n              resolve(stdout || '');\n            });\n          });\n\n          if (!result.trim()) {\n            return {\n              success: true,\n              output: `No fuzzy search results found for query: \"${query}\"`,\n              toolId: 'fuzzySearch',\n              actionName\n            };\n          }\n\n          return {\n            success: true,\n            output: result.trim(),\n            toolId: 'fuzzySearch',\n            actionName\n          };\n        } catch (error: any) {\n          return {\n            success: false,\n            error: `Fuzzy search failed: ${error.message || error}`,\n            toolId: 'fuzzySearch',\n            actionName\n          };\n        }\n      }\n    },\n    'semanticSearch': {\n      ...new SemanticSearchTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n        try {\n          const query = input.query as string;\n          if (!query) {\n            return { success: false, error: '\\'query\\' is required.', toolId: 'semanticSearch', actionName };\n          }\n\n          const topK = input.topK as number || 10;\n\n          // Extend AgentContext with optional vectorStore and embeddings for type safety\n          type SemanticAgentContext = AgentContext & {\n            vectorStore?: any;\n            embeddings?: any;\n          };\n\n          const semanticContext = context as SemanticAgentContext;\n\n          try {\n            // Import polyfill classes if needed\n            let vectorStore;\n\n            try {\n              const { MemoryVectorStore, Embeddings } = require('../workflows/corePolyfill');\n              vectorStore = semanticContext?.vectorStore || new MemoryVectorStore();\n              // Create embeddings only if needed for vectorStore initialization\n              if (!semanticContext?.vectorStore && !semanticContext?.embeddings) {\n                const embeddings = new Embeddings();\n                // Store in context for future use\n                if (semanticContext) {\n                  semanticContext.embeddings = embeddings;\n                }\n              }\n            } catch (_err) {\n              // If polyfill not available, try to use langchain directly\n              try {\n                const { MemoryVectorStore } = require('langchain/vectorstores/memory');\n                const { OpenAIEmbeddings } = require('langchain/embeddings/openai');\n                vectorStore = semanticContext?.vectorStore || new MemoryVectorStore();\n                // Create embeddings and store in context if needed\n                if (!semanticContext?.embeddings) {\n                  const langchainEmbeddings = new OpenAIEmbeddings();\n                  if (semanticContext) {\n                    semanticContext.embeddings = langchainEmbeddings;\n                  }\n                }\n              } catch (err2) {\n                return {\n                  success: false,\n                  error: 'Semantic search requires either codessa polyfill or langchain libraries.',\n                  toolId: 'semanticSearch',\n                  actionName\n                };\n              }\n            }\n\n            // If vectorStore is empty, we need to index the codebase first\n            if (!vectorStore.memoryVectors || vectorStore.memoryVectors.length === 0) {\n              return {\n                success: false,\n                error: 'Vector store is empty. Please index the codebase first.',\n                toolId: 'semanticSearch',\n                actionName\n              };\n            }\n\n            // Perform similarity search\n            const docs = await vectorStore.similaritySearch(query, topK);\n\n            if (!docs || docs.length === 0) {\n              return {\n                success: true,\n                output: 'No similar code snippets found.',\n                toolId: 'semanticSearch',\n                actionName\n              };\n            }\n\n            // Format the results\n            const formattedResults = docs.map((doc: any, index: number) => {\n              return `Result ${index + 1}:\\n${doc.pageContent || doc.content}\\n${doc.metadata ? `File: ${doc.metadata.source || 'unknown'}` : ''}\\n---`;\n            }).join('\\n');\n\n            return {\n              success: true,\n              output: formattedResults,\n              toolId: 'semanticSearch',\n              actionName\n            };\n          } catch (err: any) {\n            return {\n              success: false,\n              error: `Semantic search failed: ${err.message || err}`,\n              toolId: 'semanticSearch',\n              actionName\n            };\n          }\n        } catch (error: any) {\n          return {\n            success: false,\n            error: `Semantic search failed: ${error.message || error}`,\n            toolId: 'semanticSearch',\n            actionName\n          };\n        }\n      }\n    },\n    'searchPreview': {\n      ...new SearchPreviewTool(),\n      type: 'single-action',\n      actions: {},\n      async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n        try {\n          const filePath = input.filePath as string;\n          // Use line instead of lineNumber to match the schema\n          const line = input.line as number;\n          const contextLines = input.context as number || 3;\n\n          if (!filePath) {\n            return {\n              success: false,\n              error: '\\'filePath\\' is required.',\n              toolId: 'searchPreview',\n              actionName\n            };\n          }\n\n          if (line === undefined || line === null) {\n            return {\n              success: false,\n              error: '\\'line\\' is required.',\n              toolId: 'searchPreview',\n              actionName\n            };\n          }\n\n          try {\n            // Open the document\n            const doc = await vscode.workspace.openTextDocument(filePath);\n\n            // Calculate the range of lines to show\n            const start = Math.max(0, line - contextLines);\n            const end = Math.min(doc.lineCount - 1, line + contextLines);\n\n            // Get the lines\n            const lines: string[] = [];\n            for (let i = start; i <= end; i++) {\n              // Add line number prefix and highlight the target line\n              const lineText = doc.lineAt(i).text;\n              const linePrefix = i === line ? '> ' : '  ';\n              lines.push(`${linePrefix}${i + 1}: ${lineText}`);\n            }\n\n            // Format the output\n            const output = [\n              `File: ${filePath}`,\n              `Line: ${line + 1}`,\n              '',\n              ...lines\n            ].join('\\n');\n\n            return {\n              success: true,\n              output,\n              toolId: 'searchPreview',\n              actionName,\n              metadata: {\n                filePath,\n                line,\n                contextLines,\n                totalLines: end - start + 1\n              }\n            };\n          } catch (error: any) {\n            return {\n              success: false,\n              error: `Failed to open file: ${error.message}`,\n              toolId: 'searchPreview',\n              actionName\n            };\n          }\n        } catch (error: any) {\n          return {\n            success: false,\n            error: `Search preview failed: ${error.message || error}`,\n            toolId: 'searchPreview',\n            actionName\n          };\n        }\n      }\n    },\n  };\n\n  async execute(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // For backward compatibility, check if actionName is undefined and try to get it from input\n    const actionId = input.action as string || 'basic';\n    if (!actionId) {\n      return { success: false, error: `Action parameter is required. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id };\n    }\n    const actionTool = this.actions[actionId];\n    if (!actionTool) {\n      return { success: false, error: `Unknown code search action: ${actionId}. Available actions: ${Object.keys(this.actions).join(', ')}`, toolId: this.id };\n    }\n    const actionInput = { ...input };\n    if ('action' in actionInput) {\n      delete actionInput.action;\n    }\n\n    // Check if the action tool has the new execute method signature\n    if (actionTool.execute.length >= 2) {\n      // Pass the actionId as the actionName parameter to the nested tool\n      return actionTool.execute(actionId, actionInput, context);\n    } else {\n      // Fallback for older tools that don't have the new signature\n      const result = await (actionTool as any).execute(actionInput, context);\n      // Add the actionName to the result if it's not already there\n      if (result && !result.actionName) {\n        result.actionName = actionId;\n      }\n      return result;\n    }\n  }\n}\n\nexport const codeSearchTool = new CodeSearchTool();\n"]}