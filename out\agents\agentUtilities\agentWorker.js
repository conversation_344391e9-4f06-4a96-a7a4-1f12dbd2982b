"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentWorker = void 0;
const worker_threads_1 = require("worker_threads");
const events_1 = require("events");
class AgentWorker extends events_1.EventEmitter {
    worker;
    constructor(task) {
        super();
        this.worker = new worker_threads_1.Worker(__filename, {
            workerData: task
        });
        this.worker.on('message', (event) => {
            this.emit(event.type, event.data || event.error);
        });
        this.worker.on('error', err => this.emit('error', err));
        this.worker.on('exit', code => {
            if (code !== 0)
                this.emit('error', new Error(`Worker stopped with exit code ${code}`));
        });
    }
    terminate() {
        this.worker.terminate();
    }
}
exports.AgentWorker = AgentWorker;
// Worker thread code
if (worker_threads_1.isMainThread === false) {
    (async () => {
        try {
            // Dynamic import for the agent class
            const agentModule = await Promise.resolve(`${worker_threads_1.workerData.agentPath}`).then(s => __importStar(require(s)));
            const AgentClass = agentModule.default || agentModule;
            const agent = new AgentClass(worker_threads_1.workerData.agentConfig);
            const method = agent[worker_threads_1.workerData.method].bind(agent);
            const result = await method(...worker_threads_1.workerData.args, {
                onStream: (data) => {
                    worker_threads_1.parentPort?.postMessage({ type: 'data', data });
                }
            });
            worker_threads_1.parentPort?.postMessage({ type: 'end', data: result });
        }
        catch (err) {
            let errorMsg = '';
            if (err instanceof Error) {
                errorMsg = err.message;
            }
            else if (typeof err === 'object' && err !== null && 'message' in err) {
                errorMsg = err.message;
            }
            else {
                errorMsg = String(err);
            }
            worker_threads_1.parentPort?.postMessage({ type: 'error', error: errorMsg });
        }
    })();
}
//# sourceMappingURL=agentWorker.js.map