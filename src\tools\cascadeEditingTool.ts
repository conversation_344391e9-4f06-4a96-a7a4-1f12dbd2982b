/**
 * Cascade Editing Tool - Intelligent dependency-aware editing
 * 
 * This tool provides intelligent editing capabilities that automatically
 * propagate changes across dependent files and maintain code consistency.
 */

import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import { DependencyAnalysisTool } from './dependencyAnalysisTool';
import { CodeComplexityTool } from './codeAnalysisTool';
import { PrecisionEditTool } from './advancedCodeEditingTool';
import { logger } from '../logger';

export interface CascadeEdit {
  id: string;
  primaryFile: string;
  primaryChanges: EditChange[];
  dependentEdits: DependentEdit[];
  impactAnalysis: ImpactAnalysis;
  validationResults: ValidationResult[];
}

export interface EditChange {
  type: 'insert' | 'replace' | 'delete' | 'rename';
  location: EditLocation;
  content: string;
  reason: string;
}

export interface EditLocation {
  filePath: string;
  startLine: number;
  endLine: number;
  startColumn?: number;
  endColumn?: number;
}

export interface DependentEdit {
  filePath: string;
  changes: EditChange[];
  reason: string;
  confidence: number;
  dependencies: string[];
}

export interface ImpactAnalysis {
  totalFilesAffected: number;
  riskLevel: 'low' | 'medium' | 'high';
  breakingChanges: BreakingChange[];
  testFilesAffected: string[];
  documentationUpdatesNeeded: string[];
}

export interface BreakingChange {
  type: 'signature' | 'interface' | 'behavior' | 'dependency';
  description: string;
  affectedFiles: string[];
  severity: 'minor' | 'major' | 'critical';
}

export interface ValidationResult {
  filePath: string;
  passed: boolean;
  issues: string[];
  suggestions: string[];
}

export class CascadeEditingTool implements ITool {
  readonly id = 'cascade-edit';
  readonly name = 'Cascade Editing Tool';
  readonly description = 'Intelligent dependency-aware editing with automatic propagation';
  readonly type = 'multi-action' as const;
  readonly category = 'Editing';

  private dependencyTool: DependencyAnalysisTool;
  private codeAnalysisTool: CodeComplexityTool;
  private precisionEditTool: PrecisionEditTool;

  // Performance optimization: Cache and batch processing
  private dependencyCache: Map<string, any> = new Map();
  private editQueue: Map<string, EditChange[]> = new Map();
  private readonly cacheExpiry = 10 * 60 * 1000; // 10 minutes for dependency cache
  private readonly maxBatchSize = 10;

  constructor() {
    this.dependencyTool = new DependencyAnalysisTool();
    this.codeAnalysisTool = new CodeComplexityTool();
    this.precisionEditTool = new PrecisionEditTool();
  }

  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    try {
      switch (actionName) {
        case 'analyzeCascadeImpact':
          return await this.analyzeCascadeImpact(input, context);
        case 'executeCascadeEdit':
          return await this.executeCascadeEdit(input, context);
        case 'previewCascadeChanges':
          return await this.previewCascadeChanges(input, context);
        case 'validateCascadeEdit':
          return await this.validateCascadeEdit(input, context);
        case 'revertCascadeEdit':
          return await this.revertCascadeEdit(input, context);
        case 'optimizeDependencies':
          return await this.optimizeDependencies(input, context);
        default:
          return {
            success: false,
            error: `Unknown action: ${actionName}`,
            toolId: this.id,
            actionName
          };
      }
    } catch (error) {
      logger.error(`Cascade editing error: ${error}`);
      return {
        success: false,
        error: `Cascade editing failed: ${error instanceof Error ? error.message : String(error)}`,
        toolId: this.id,
        actionName
      };
    }
  }

  /**
     * Analyze the cascade impact of proposed changes (Optimized)
     */
  private async analyzeCascadeImpact(input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    const filePath = input.filePath as string;
    const proposedChanges = input.changes as EditChange[];

    if (!filePath || !proposedChanges) {
      return {
        success: false,
        error: 'File path and proposed changes are required',
        toolId: this.id,
        actionName: 'analyzeCascadeImpact'
      };
    }

    // Use context for additional workspace information if available
    const workspaceRoot = context?.workspace?.workspaceFolders?.[0];
    const currentFile = context?.workspace?.currentFile;

    logger.info(`Analyzing cascade impact for ${filePath} (workspace: ${workspaceRoot || 'unknown'}, current: ${currentFile || 'none'})`);

    try {
      // Check cache for recent dependency analysis
      const cacheKey = `deps_${filePath}`;
      let impactData = this.getCachedDependency(cacheKey);

      if (!impactData) {
        // Analyze dependencies with optimized depth
        const dependencyResult = await this.dependencyTool.execute('analyzeImpact', {
          filePath,
          depth: 2 // Reduced depth for better performance
        });

        if (!dependencyResult.success) {
          return {
            success: false,
            error: `Dependency analysis failed: ${dependencyResult.error}`,
            toolId: this.id,
            actionName: 'analyzeCascadeImpact'
          };
        }

        impactData = dependencyResult.output as any;
        this.setCachedDependency(cacheKey, impactData);
      }

      // Parallel processing for better performance
      const [dependentEdits, breakingChanges] = await Promise.all([
        this.analyzeDependentChanges(filePath, proposedChanges, impactData.impactedFiles),
        this.identifyBreakingChanges(proposedChanges, []) // Pass empty array initially for speed
      ]);

      // Create impact analysis
      const impactAnalysis: ImpactAnalysis = {
        totalFilesAffected: dependentEdits.length + 1,
        riskLevel: this.assessRiskLevel(breakingChanges, dependentEdits),
        breakingChanges,
        testFilesAffected: this.findTestFiles(impactData.impactedFiles || []),
        documentationUpdatesNeeded: this.findDocumentationFiles(impactData.impactedFiles || [])
      };

      const cascadeEdit: CascadeEdit = {
        id: `cascade_${Date.now()}`,
        primaryFile: filePath,
        primaryChanges: proposedChanges,
        dependentEdits,
        impactAnalysis,
        validationResults: []
      };

      return {
        success: true,
        output: cascadeEdit,
        toolId: this.id,
        actionName: 'analyzeCascadeImpact',
        metadata: {
          filePath,
          totalFilesAffected: impactAnalysis.totalFilesAffected,
          riskLevel: impactAnalysis.riskLevel,
          breakingChangesCount: breakingChanges.length,
          cached: impactData.cached || false
        }
      };
    } catch (error) {
      logger.error(`Cascade impact analysis failed for ${filePath}:`, error);
      return {
        success: false,
        error: `Cascade impact analysis failed: ${error instanceof Error ? error.message : String(error)}`,
        toolId: this.id,
        actionName: 'analyzeCascadeImpact'
      };
    }
  }

  /**
     * Execute cascade edit with dependency propagation
     */
  private async executeCascadeEdit(input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    const cascadeEdit = input.cascadeEdit as CascadeEdit;
    const dryRun = input.dryRun as boolean || false;

    if (!cascadeEdit) {
      return {
        success: false,
        error: 'Cascade edit configuration is required',
        toolId: this.id,
        actionName: 'executeCascadeEdit'
      };
    }

    // Log execution context for debugging
    const workspaceInfo = context?.workspace?.workspaceFolders?.[0] || 'unknown';
    logger.info(`Executing cascade edit ${cascadeEdit.id} (dryRun: ${dryRun}, workspace: ${workspaceInfo})`);

    const results: Array<{ filePath: string, success: boolean, error?: string }> = [];

    try {
      // Queue all edits for batch processing
      this.editQueue.set(cascadeEdit.primaryFile, cascadeEdit.primaryChanges);
      for (const dependentEdit of cascadeEdit.dependentEdits) {
        this.editQueue.set(dependentEdit.filePath, dependentEdit.changes);
      }

      // Execute primary file changes first
      if (!dryRun) {
        // Use batch processing for better performance
        const primaryEditResults = await this.processBatchEdits(cascadeEdit.primaryChanges);
        const primarySuccess = primaryEditResults.every(result => result.success);

        if (!primarySuccess) {
          const failedEdit = primaryEditResults.find(result => !result.success);
          results.push({
            filePath: cascadeEdit.primaryFile,
            success: false,
            error: failedEdit?.error instanceof Error ? failedEdit.error.message : failedEdit?.error
          });
          // Stop execution on primary file failure
          return {
            success: false,
            output: results,
            error: `Primary file edit failed: ${failedEdit?.error}`,
            toolId: this.id,
            actionName: 'executeCascadeEdit'
          };
        }
        results.push({ filePath: cascadeEdit.primaryFile, success: true });
      }

      // Execute dependent file changes
      for (const dependentEdit of cascadeEdit.dependentEdits) {
        if (dependentEdit.confidence < 0.7) {
          logger.warn(`Skipping low-confidence edit for ${dependentEdit.filePath} (confidence: ${dependentEdit.confidence})`);
          continue;
        }

        if (!dryRun) {
          let allChangesSucceeded = true;
          for (const change of dependentEdit.changes) {
            const editResult = await this.executeEditChange(change);
            if (!editResult.success) {
              results.push({
                filePath: dependentEdit.filePath,
                success: false,
                error: editResult.error instanceof Error ? editResult.error.message : editResult.error
              });
              allChangesSucceeded = false;
              break;
            }
          }
          if (allChangesSucceeded) {
            results.push({ filePath: dependentEdit.filePath, success: true });
          }
        } else {
          // Dry run - just validate
          results.push({ filePath: dependentEdit.filePath, success: true });
        }
      }

      // Validate all changes if not dry run
      if (!dryRun) {
        const validationResults = await this.validateAllChanges(cascadeEdit);
        cascadeEdit.validationResults = validationResults;
      }

      return {
        success: true,
        output: {
          cascadeEdit,
          executionResults: results,
          summary: {
            totalFiles: results.length,
            successfulFiles: results.filter(r => r.success).length,
            failedFiles: results.filter(r => !r.success).length,
            dryRun
          }
        },
        toolId: this.id,
        actionName: 'executeCascadeEdit',
        metadata: {
          cascadeEditId: cascadeEdit.id,
          filesModified: results.filter(r => r.success).length,
          dryRun
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `Cascade edit execution failed: ${error instanceof Error ? error.message : String(error)}`,
        output: results,
        toolId: this.id,
        actionName: 'executeCascadeEdit'
      };
    }
  }

  /**
     * Preview cascade changes without executing them
     */
  private async previewCascadeChanges(input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    // Set dry run to true and call executeCascadeEdit
    return await this.executeCascadeEdit({ ...input, dryRun: true }, context);
  }

  /**
     * Validate cascade edit results
     */
  private async validateCascadeEdit(input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    const cascadeEdit = input.cascadeEdit as CascadeEdit;

    if (!cascadeEdit) {
      return {
        success: false,
        error: 'Cascade edit configuration is required',
        toolId: this.id,
        actionName: 'validateCascadeEdit'
      };
    }

    // Use context for validation logging
    const workspaceInfo = context?.workspace?.workspaceFolders?.[0] || 'unknown';
    logger.info(`Validating cascade edit ${cascadeEdit.id} (workspace: ${workspaceInfo})`);

    const validationResults = await this.validateAllChanges(cascadeEdit);

    const overallSuccess = validationResults.every(result => result.passed);
    const totalIssues = validationResults.reduce((sum, result) => sum + result.issues.length, 0);

    return {
      success: true,
      output: {
        overallSuccess,
        validationResults,
        summary: {
          totalFiles: validationResults.length,
          passedFiles: validationResults.filter(r => r.passed).length,
          failedFiles: validationResults.filter(r => !r.passed).length,
          totalIssues
        }
      },
      toolId: this.id,
      actionName: 'validateCascadeEdit',
      metadata: {
        cascadeEditId: cascadeEdit.id,
        overallSuccess,
        totalIssues
      }
    };
  }

  // Helper methods
  private async analyzeDependentChanges(
    primaryFile: string,
    primaryChanges: EditChange[],
    impactedFiles: string[]
  ): Promise<DependentEdit[]> {
    const dependentEdits: DependentEdit[] = [];

    for (const impactedFile of impactedFiles) {
      if (impactedFile === primaryFile) continue;

      // Analyze what changes are needed in this dependent file
      const neededChanges = await this.determineNeededChanges(primaryFile, primaryChanges, impactedFile);

      if (neededChanges.length > 0) {
        dependentEdits.push({
          filePath: impactedFile,
          changes: neededChanges,
          reason: `Dependent on changes in ${primaryFile}`,
          confidence: 0.8, // Would be calculated based on analysis
          dependencies: [primaryFile]
        });
      }
    }

    return dependentEdits;
  }

  private async determineNeededChanges(primaryFile: string, primaryChanges: EditChange[], dependentFile: string): Promise<EditChange[]> {
    // Analyze the dependency relationship and determine what changes are needed
    const neededChanges: EditChange[] = [];

    try {
      // Use code analysis to understand the relationship
      const analysisResult = await this.codeAnalysisTool.execute('analyzeFile', {
        filePath: dependentFile
      });

      if (analysisResult.success) {
        // Analyze each primary change to see if it affects the dependent file
        for (const primaryChange of primaryChanges) {
          // Check if the dependent file imports or references the primary file
          const dependentContent = analysisResult.output?.content || '';
          const primaryFileName = primaryFile.split('/').pop()?.replace(/\.[^/.]+$/, '') || '';

          if (dependentContent.includes(primaryFileName) ||
            dependentContent.includes(primaryFile)) {

            // Determine what type of change is needed based on the primary change
            if (primaryChange.type === 'rename') {
              // Need to update import/reference statements
              neededChanges.push({
                type: 'replace',
                location: {
                  filePath: dependentFile,
                  startLine: 1,
                  endLine: 1
                },
                content: `// Update reference to renamed ${primaryFileName}`,
                reason: `Reference update due to rename in ${primaryFile}`
              });
            } else if (primaryChange.type === 'delete') {
              // Need to remove or replace the dependency
              neededChanges.push({
                type: 'replace',
                location: {
                  filePath: dependentFile,
                  startLine: 1,
                  endLine: 1
                },
                content: `// Remove or replace dependency on deleted ${primaryFileName}`,
                reason: `Dependency removal due to deletion in ${primaryFile}`
              });
            }
          }
        }
      }
    } catch (error) {
      logger.warn(`Failed to determine needed changes for ${dependentFile}: ${error}`);
    }

    return neededChanges;
  }

  private async identifyBreakingChanges(primaryChanges: EditChange[], dependentEdits: DependentEdit[]): Promise<BreakingChange[]> {
    // Analyze changes to identify breaking changes using code analysis
    const breakingChanges: BreakingChange[] = [];

    for (const change of primaryChanges) {
      // Use code analysis tool to assess complexity and impact
      try {
        const complexityResult = await this.codeAnalysisTool.execute('analyzeComplexity', {
          filePath: change.location.filePath,
          content: change.content
        });

        let severity: 'minor' | 'major' | 'critical' = 'minor';

        if (complexityResult.success && complexityResult.output) {
          const complexity = complexityResult.output as any;
          // Determine severity based on complexity and change type
          if (complexity.cyclomaticComplexity > 10 || change.type === 'delete') {
            severity = 'critical';
          } else if (complexity.cyclomaticComplexity > 5 || change.type === 'rename') {
            severity = 'major';
          }
        }

        if (change.type === 'delete' || change.type === 'rename') {
          breakingChanges.push({
            type: 'signature',
            description: `${change.type} operation may break dependent code (complexity-based analysis)`,
            affectedFiles: dependentEdits.map(edit => edit.filePath),
            severity
          });
        }

        // Check for interface changes
        if (change.content.includes('interface') || change.content.includes('export')) {
          breakingChanges.push({
            type: 'interface',
            description: 'Interface or export changes detected',
            affectedFiles: dependentEdits.map(edit => edit.filePath),
            severity: severity === 'minor' ? 'major' : severity
          });
        }
      } catch (error) {
        logger.warn(`Code analysis failed for breaking change detection: ${error}`);
        // Fallback to simple analysis
        if (change.type === 'delete' || change.type === 'rename') {
          breakingChanges.push({
            type: 'signature',
            description: `${change.type} operation may break dependent code`,
            affectedFiles: dependentEdits.map(edit => edit.filePath),
            severity: 'major'
          });
        }
      }
    }

    return breakingChanges;
  }

  private assessRiskLevel(breakingChanges: BreakingChange[], dependentEdits: DependentEdit[]): 'low' | 'medium' | 'high' {
    if (breakingChanges.length > 0) return 'high';
    if (dependentEdits.length > 5) return 'medium';
    return 'low';
  }

  private findTestFiles(impactedFiles: string[]): string[] {
    return impactedFiles.filter(file =>
      file.includes('test') || file.includes('spec') || file.endsWith('.test.ts') || file.endsWith('.spec.ts')
    );
  }

  private findDocumentationFiles(impactedFiles: string[]): string[] {
    return impactedFiles.filter(file =>
      file.endsWith('.md') || file.includes('doc') || file.includes('README')
    );
  }

  private async executeEditChange(change: EditChange): Promise<ToolResult> {
    // Use the precision edit tool to execute the change
    return await this.precisionEditTool.execute('replaceLines', {
      filePath: change.location.filePath,
      startLine: change.location.startLine,
      endLine: change.location.endLine,
      newContent: change.content
    });
  }

  private async validateAllChanges(cascadeEdit: CascadeEdit): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    // Validate primary file
    results.push(await this.validateFile(cascadeEdit.primaryFile));

    // Validate dependent files
    for (const dependentEdit of cascadeEdit.dependentEdits) {
      results.push(await this.validateFile(dependentEdit.filePath));
    }

    return results;
  }

  private async validateFile(filePath: string): Promise<ValidationResult> {
    // This would perform comprehensive validation of the file
    // For now, return a basic validation result
    return {
      filePath,
      passed: true,
      issues: [],
      suggestions: []
    };
  }

  // Additional methods for other actions...
  private async revertCascadeEdit(input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    const cascadeEditId = input.cascadeEditId as string;

    if (!cascadeEditId) {
      return {
        success: false,
        error: 'Cascade edit ID is required for revert operation',
        toolId: this.id,
        actionName: 'revertCascadeEdit'
      };
    }

    try {
      // Clear the edit queue for this cascade edit
      Array.from(this.editQueue.entries()).forEach(([filePath]) => {
        if (filePath.includes(cascadeEditId)) {
          this.editQueue.delete(filePath);
        }
      });

      // In a full implementation, this would:
      // 1. Retrieve the original state from version control
      // 2. Restore files to their previous state
      // 3. Validate the revert operation

      logger.info(`Reverted cascade edit ${cascadeEditId}`);

      return {
        success: true,
        output: `Cascade edit ${cascadeEditId} reverted successfully`,
        toolId: this.id,
        actionName: 'revertCascadeEdit',
        metadata: { cascadeEditId, context: context?.workspace?.currentFile }
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to revert cascade edit: ${error instanceof Error ? error.message : String(error)}`,
        toolId: this.id,
        actionName: 'revertCascadeEdit'
      };
    }
  }

  private async optimizeDependencies(input: ToolInput, context?: AgentContext): Promise<ToolResult> {
    const filePath = input.filePath as string;

    if (!filePath) {
      return {
        success: false,
        error: 'File path is required for dependency optimization',
        toolId: this.id,
        actionName: 'optimizeDependencies'
      };
    }

    try {
      // Use code analysis tool to analyze current dependencies
      const analysisResult = await this.codeAnalysisTool.execute('analyzeDependencies', {
        filePath
      });

      const optimizations: string[] = [];

      if (analysisResult.success) {
        // Analyze dependency patterns and suggest optimizations
        const dependencies = analysisResult.output?.dependencies || [];

        // Check for circular dependencies
        const circularDeps = dependencies.filter((dep: any) => dep.circular);
        if (circularDeps.length > 0) {
          optimizations.push(`Found ${circularDeps.length} circular dependencies that should be resolved`);
        }

        // Check for unused dependencies
        const unusedDeps = dependencies.filter((dep: any) => !dep.used);
        if (unusedDeps.length > 0) {
          optimizations.push(`Found ${unusedDeps.length} unused dependencies that can be removed`);
        }

        // Check for duplicate dependencies
        const duplicateDeps = dependencies.filter((dep: any) => dep.duplicate);
        if (duplicateDeps.length > 0) {
          optimizations.push(`Found ${duplicateDeps.length} duplicate dependencies that can be consolidated`);
        }
      }

      return {
        success: true,
        output: {
          filePath,
          optimizations,
          summary: `Analyzed dependencies for ${filePath}. Found ${optimizations.length} optimization opportunities.`,
          context: context?.workspace?.currentFile
        },
        toolId: this.id,
        actionName: 'optimizeDependencies',
        metadata: { filePath, optimizationCount: optimizations.length }
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to optimize dependencies: ${error instanceof Error ? error.message : String(error)}`,
        toolId: this.id,
        actionName: 'optimizeDependencies'
      };
    }
  }

  /**
     * Performance optimization: Dependency cache management
     */
  private getCachedDependency(key: string): any | null {
    const cached = this.dependencyCache.get(key);
    if (!cached) return null;

    // Check if cache entry has expired
    if (Date.now() - cached.timestamp > this.cacheExpiry) {
      this.dependencyCache.delete(key);
      return null;
    }

    return { ...cached.data, cached: true };
  }

  private setCachedDependency(key: string, data: any): void {
    // Implement simple LRU cache
    if (this.dependencyCache.size >= 20) { // Limit cache size
      const firstKey = this.dependencyCache.keys().next().value;
      if (firstKey) {
        this.dependencyCache.delete(firstKey);
      }
    }

    this.dependencyCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
     * Performance optimization: Batch edit processing
     */
  private async processBatchEdits(edits: EditChange[]): Promise<ToolResult[]> {
    const results: ToolResult[] = [];

    // Process edits in batches for better performance
    for (let i = 0; i < edits.length; i += this.maxBatchSize) {
      const batch = edits.slice(i, i + this.maxBatchSize);
      const batchPromises = batch.map(edit => this.executeEditChange(edit));

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      } catch (error) {
        logger.error('Batch edit processing failed:', error);
        // Continue with remaining batches
      }
    }

    return results;
  }
}
