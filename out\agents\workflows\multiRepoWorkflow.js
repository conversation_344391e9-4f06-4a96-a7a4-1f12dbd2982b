"use strict";
/**
 * Multi-Repository Context Workflow
 *
 * This module provides a workflow for working with multiple repositories:
 * - Analyzing code across repositories
 * - Maintaining context between different codebases
 * - Providing insights across project boundaries
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMultiRepoWorkflow = createMultiRepoWorkflow;
exports.createCrossRepoDependencyWorkflow = createCrossRepoDependencyWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
/**
 * Create a Multi-Repository Context workflow
 */
function createMultiRepoWorkflow(id, name, description, analyzerAgent, tools = []) {
    logger_1.logger.info(`Creating Multi-Repository Context workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const repoDiscoveryNode = graph_1.Codessa.createAgentNode('repo-discovery', 'Repository Discovery', analyzerAgent);
    const repoAnalysisNode = graph_1.Codessa.createAgentNode('repo-analysis', 'Repository Analysis', analyzerAgent);
    const dependencyMappingNode = graph_1.Codessa.createAgentNode('dependency-mapping', 'Dependency Mapping', analyzerAgent);
    const crossRepoSearchNode = graph_1.Codessa.createAgentNode('cross-repo-search', 'Cross-Repository Search', analyzerAgent);
    const contextIntegrationNode = graph_1.Codessa.createAgentNode('context-integration', 'Context Integration', analyzerAgent);
    const insightGenerationNode = graph_1.Codessa.createAgentNode('insight-generation', 'Insight Generation', analyzerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-discovery', source: 'input', target: 'repo-discovery', type: 'default' },
        { name: 'discovery-to-analysis', source: 'repo-discovery', target: 'repo-analysis', type: 'default' },
        { name: 'analysis-to-mapping', source: 'repo-analysis', target: 'dependency-mapping', type: 'default' },
        { name: 'mapping-to-search', source: 'dependency-mapping', target: 'cross-repo-search', type: 'default' },
        { name: 'search-to-integration', source: 'cross-repo-search', target: 'context-integration', type: 'default' },
        { name: 'integration-to-insight', source: 'context-integration', target: 'insight-generation', type: 'default' },
        { name: 'insight-to-output', source: 'insight-generation', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect cross-repo search to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `search-to-tool-${index}`,
                source: 'cross-repo-search',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to context integration
            edges.push({
                name: `tool-${index}-to-integration`,
                source: `tool-${index}`,
                target: 'context-integration',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            repoDiscoveryNode,
            repoAnalysisNode,
            dependencyMappingNode,
            crossRepoSearchNode,
            contextIntegrationNode,
            insightGenerationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'research',
        tags: ['multi-repo', 'cross-repository', 'code-analysis']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a specialized Cross-Repository Dependency Analysis workflow
 */
function createCrossRepoDependencyWorkflow(id, name, description, analyzerAgent, tools = []) {
    logger_1.logger.info(`Creating Cross-Repository Dependency Analysis workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const repoScanningNode = graph_1.Codessa.createAgentNode('repo-scanning', 'Repository Scanning', analyzerAgent);
    const packageAnalysisNode = graph_1.Codessa.createAgentNode('package-analysis', 'Package Analysis', analyzerAgent);
    const importAnalysisNode = graph_1.Codessa.createAgentNode('import-analysis', 'Import Analysis', analyzerAgent);
    const apiUsageAnalysisNode = graph_1.Codessa.createAgentNode('api-usage-analysis', 'API Usage Analysis', analyzerAgent);
    const dependencyGraphNode = graph_1.Codessa.createAgentNode('dependency-graph', 'Dependency Graph', analyzerAgent);
    const vulnerabilityAnalysisNode = graph_1.Codessa.createAgentNode('vulnerability-analysis', 'Vulnerability Analysis', analyzerAgent);
    const reportGenerationNode = graph_1.Codessa.createAgentNode('report-generation', 'Report Generation', analyzerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-scanning', source: 'input', target: 'repo-scanning', type: 'default' },
        { name: 'scanning-to-package', source: 'repo-scanning', target: 'package-analysis', type: 'default' },
        { name: 'scanning-to-import', source: 'repo-scanning', target: 'import-analysis', type: 'default' },
        { name: 'scanning-to-api', source: 'repo-scanning', target: 'api-usage-analysis', type: 'default' },
        { name: 'package-to-graph', source: 'package-analysis', target: 'dependency-graph', type: 'default' },
        { name: 'import-to-graph', source: 'import-analysis', target: 'dependency-graph', type: 'default' },
        { name: 'api-to-graph', source: 'api-usage-analysis', target: 'dependency-graph', type: 'default' },
        { name: 'graph-to-vulnerability', source: 'dependency-graph', target: 'vulnerability-analysis', type: 'default' },
        { name: 'vulnerability-to-report', source: 'vulnerability-analysis', target: 'report-generation', type: 'default' },
        { name: 'report-to-output', source: 'report-generation', target: 'output', type: 'default' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect dependency graph to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `graph-to-tool-${index}`,
                source: 'dependency-graph',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to vulnerability analysis
            edges.push({
                name: `tool-${index}-to-vulnerability`,
                source: `tool-${index}`,
                target: 'vulnerability-analysis',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            repoScanningNode,
            packageAnalysisNode,
            importAnalysisNode,
            apiUsageAnalysisNode,
            dependencyGraphNode,
            vulnerabilityAnalysisNode,
            reportGenerationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'research',
        tags: ['dependency-analysis', 'cross-repository', 'vulnerability']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=multiRepoWorkflow.js.map