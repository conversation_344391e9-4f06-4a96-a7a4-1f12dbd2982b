/**
 * Checkpoint Manager for Code Changes
 * 
 * This module provides a checkpoint system for tracking and managing code changes
 * during AI-assisted development. It allows:
 * - Creating checkpoints before making changes
 * - Rolling back to previous checkpoints
 * - Comparing changes between checkpoints
 * - Merging changes from different branches
 */

import * as vscode from 'vscode';
import * as crypto from 'crypto';
import * as path from 'path';
import { logger } from '../logger';

/**
 * Checkpoint data structure
 */
export interface Checkpoint {
    id: string;
    timestamp: Date;
    description: string;
    files: Array<{
        path: string;
        content: string;
        hash: string;
    }>;
    metadata: Record<string, unknown>;
}

/**
 * Checkpoint manager for tracking code changes
 */
export class CheckpointManager {
  private checkpoints: Checkpoint[] = [];
  private currentCheckpointIndex = -1;
  private storageKey = 'codessa.checkpoints';
    
  constructor() {
    this.loadCheckpoints();
  }
    
  /**
     * Loads checkpoints from storage
     */
  private async loadCheckpoints(): Promise<void> {
    try {
      const globalState = await this.getGlobalState();
      if (!globalState) return;
            
      const savedCheckpoints = globalState.get<Checkpoint[]>(this.storageKey);
      if (savedCheckpoints && Array.isArray(savedCheckpoints)) {
        // Convert string dates back to Date objects
        this.checkpoints = savedCheckpoints.map(cp => ({
          ...cp,
          timestamp: new Date(cp.timestamp)
        }));
        this.currentCheckpointIndex = this.checkpoints.length - 1;
        logger.info(`Loaded ${this.checkpoints.length} checkpoints from storage`);
      }
    } catch (error: unknown) {
      logger.error(`Error loading checkpoints: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
    
  /**
     * Saves checkpoints to storage
     */
  private async saveCheckpoints(): Promise<void> {
    try {
      const globalState = await this.getGlobalState();
      if (!globalState) return;
            
      await globalState.update(this.storageKey, this.checkpoints);
      logger.info(`Saved ${this.checkpoints.length} checkpoints to storage`);
    } catch (error: unknown) {
      logger.error(`Error saving checkpoints: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
    
  /**
     * Gets the VS Code extension context global state
     */
  private async getGlobalState(): Promise<vscode.Memento | null> {
    // Get the active VS Code extension
    const extension = vscode.extensions.getExtension('codessa.codessa');
    if (!extension) {
      logger.error('Codessa extension not found');
      return null;
    }
        
    // Ensure the extension is activated
    if (!extension.isActive) {
      await extension.activate();
    }
        
    // Access the extension's global state
    return extension.exports.getGlobalState();
  }
    
  /**
     * Creates a new checkpoint
     */
  async createCheckpoint(description: string, files: string[], metadata: Record<string, unknown> = {}): Promise<Checkpoint> {
    const checkpoint: Checkpoint = {
      id: `checkpoint-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      timestamp: new Date(),
      description,
      files: await this.captureFiles(files),
      metadata
    };
        
    this.checkpoints.push(checkpoint);
    this.currentCheckpointIndex = this.checkpoints.length - 1;
        
    // Save checkpoints to storage
    await this.saveCheckpoints();
        
    logger.info(`Created checkpoint: ${checkpoint.id} - ${description}`);
    return checkpoint;
  }
    
  /**
     * Captures the content of files for a checkpoint
     */
  private async captureFiles(filePaths: string[]): Promise<Checkpoint['files']> {
    const capturedFiles: Checkpoint['files'] = [];
        
    for (const filePath of filePaths) {
      try {
        // Get the full path
        const fullPath = this.getFullPath(filePath);
                
        // Read the file content
        const document = await vscode.workspace.openTextDocument(vscode.Uri.file(fullPath));
        const content = document.getText();
                
        // Calculate hash
        const hash = this.calculateHash(content);
                
        capturedFiles.push({
          path: filePath,
          content,
          hash
        });
      } catch (error: unknown) {
        logger.error(`Error capturing file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
        // Add the file with error information
        capturedFiles.push({
          path: filePath,
          content: `ERROR: ${error instanceof Error ? error.message : String(error)}`,
          hash: 'error'
        });
      }
    }
        
    return capturedFiles;
  }
    
  /**
     * Calculates a hash for file content
     */
  private calculateHash(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex');
  }
    
  /**
     * Gets the full path for a file
     */
  private getFullPath(filePath: string): string {
    // If the path is already absolute, return it
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
        
    // Get the workspace folder
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      throw new Error('No workspace folder open');
    }
        
    // Resolve the path relative to the workspace folder
    return path.join(workspaceFolders[0].uri.fsPath, filePath);
  }
    
  /**
     * Rolls back to a specific checkpoint
     */
  async rollbackToCheckpoint(checkpointId: string): Promise<boolean> {
    const checkpointIndex = this.checkpoints.findIndex(cp => cp.id === checkpointId);
        
    if (checkpointIndex === -1) {
      logger.error(`Checkpoint not found: ${checkpointId}`);
      return false;
    }
        
    const checkpoint = this.checkpoints[checkpointIndex];
        
    // Restore file contents
    for (const file of checkpoint.files) {
      try {
        // Get the full path
        const fullPath = this.getFullPath(file.path);
                
        // Create a TextEdit to replace the entire content
        const document = await vscode.workspace.openTextDocument(vscode.Uri.file(fullPath));
        const edit = new vscode.WorkspaceEdit();
                
        // Replace the entire content
        const fullRange = new vscode.Range(
          document.positionAt(0),
          document.positionAt(document.getText().length)
        );
        edit.replace(document.uri, fullRange, file.content);
                
        // Apply the edit
        const success = await vscode.workspace.applyEdit(edit);
        if (!success) {
          logger.error(`Failed to restore file: ${file.path}`);
        }
                
        // Save the document
        await document.save();
      } catch (error: unknown) {
        logger.error(`Error restoring file ${file.path}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
        
    this.currentCheckpointIndex = checkpointIndex;
        
    // Save the current checkpoint index
    await this.saveCheckpoints();
        
    logger.info(`Rolled back to checkpoint: ${checkpointId}`);
        
    return true;
  }
    
  /**
     * Gets all checkpoints
     */
  getCheckpoints(): Checkpoint[] {
    return [...this.checkpoints];
  }
    
  /**
     * Gets the current checkpoint
     */
  getCurrentCheckpoint(): Checkpoint | null {
    if (this.currentCheckpointIndex === -1) {
      return null;
    }
        
    return this.checkpoints[this.currentCheckpointIndex];
  }
    
  /**
     * Compares two checkpoints and returns the differences
     */
  compareCheckpoints(checkpointId1: string, checkpointId2: string): Record<string, unknown> {
    const checkpoint1 = this.checkpoints.find(cp => cp.id === checkpointId1);
    const checkpoint2 = this.checkpoints.find(cp => cp.id === checkpointId2);
        
    if (!checkpoint1 || !checkpoint2) {
      throw new Error('One or both checkpoints not found');
    }
        
    // Track changes
    let filesChanged = 0;
    let linesAdded = 0;
    let linesRemoved = 0;
    const fileDiffs: Record<string, unknown> = {};
        
    // Get all unique file paths from both checkpoints
    const allPaths = new Set([
      ...checkpoint1.files.map(f => f.path),
      ...checkpoint2.files.map(f => f.path)
    ]);
        
    // Compare each file
    for (const filePath of allPaths) {
      const file1 = checkpoint1.files.find(f => f.path === filePath);
      const file2 = checkpoint2.files.find(f => f.path === filePath);
            
      // If file exists in both checkpoints and content is different
      if (file1 && file2 && file1.hash !== file2.hash) {
        filesChanged++;
                
        // Calculate line differences
        const lines1 = file1.content.split('\n');
        const lines2 = file2.content.split('\n');
                
        // Simple diff calculation
        if (lines2.length > lines1.length) {
          linesAdded += lines2.length - lines1.length;
        } else {
          linesRemoved += lines1.length - lines2.length;
        }
                
        // Store diff details
        fileDiffs[filePath] = {
          changed: true,
          linesAdded: Math.max(0, lines2.length - lines1.length),
          linesRemoved: Math.max(0, lines1.length - lines2.length)
        };
      }
      // File added in checkpoint2
      else if (!file1 && file2) {
        filesChanged++;
        const lineCount = file2.content.split('\n').length;
        linesAdded += lineCount;
                
        fileDiffs[filePath] = {
          added: true,
          linesAdded: lineCount
        };
      }
      // File removed in checkpoint2
      else if (file1 && !file2) {
        filesChanged++;
        const lineCount = file1.content.split('\n').length;
        linesRemoved += lineCount;
                
        fileDiffs[filePath] = {
          removed: true,
          linesRemoved: lineCount
        };
      }
    }
        
    return {
      filesChanged,
      linesAdded,
      linesRemoved,
      fileDiffs
    };
  }
}

// Create a singleton instance
export const checkpointManager = new CheckpointManager();
