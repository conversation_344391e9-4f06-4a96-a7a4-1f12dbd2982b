/**
 * Comprehensive Types for Codessa Workflows
 * Properly separates operation modes from methodologies
 */

import { BaseMessage } from './corePolyfill';
import { Agent, AgentContext } from '../agentUtilities/agent';
import { ITool } from '../../tools/tool.ts.backup';

/**
 * Core Node Types
 */
export type NodeType =
    | 'input'
    | 'output'
    | 'agent'
    | 'tool'
    | 'decision'
    | 'process';

/**
 * Node definition (extended for compatibility with graphTypes)
 */
export interface GraphNode {
    id: string;
    label: string; // Added for compatibility
    type: NodeType;
    name: string;
    description?: string;
    agent?: Agent;
    tool?: ITool;
    condition?: (_state: any) => Promise<string>;
    config?: Record<string, any>;
    metadata?: Record<string, any>;
    execute?: (_state: any) => Promise<Partial<GraphState>> | Partial<GraphState>;
}

/**
 * Operation Modes - What the workflow does
 */
export type OperationMode =
    | 'memory'             // Memory-enhanced operations
    | 'document-qa'        // Document Q&A
    | 'refactoring'        // Code refactoring
    | 'debugging'          // Code debugging
    | 'chat'               // Conversational chat
    | 'ask'                // Question answering with context
    | 'edit'               // Code editing
    | 'codegen'            // Code generation
    | 'agentic'            // Single agent tasks
    | 'multi-agent'        // Multi-agent collaboration
    | 'research'           // Research tasks
    | 'ui-ux'              // UI/UX workflows
    | 'documentation'      // Documentation generation
    | 'pr-creation'        // PR creation workflow
    | 'pr-review'          // PR review workflow
    | 'checkpoint'         // Checkpoint system
    | 'mcp'                // Model Context Protocol
    | 'pattern-refactoring' // Pattern-based refactoring
    | 'technical-debt'     // Technical debt reduction

/**
 * SDLC Tasks - Development Lifecycle Tasks
 */
export type SDLCTask =
    | 'planning'
    | 'requirements'
    | 'design'
    | 'implementation'
    | 'testing'
    | 'deployment'
    | 'maintenance';


/**
 * Development Methodologies - How the workflow operates
 */
export type Methodology =
    | 'agile'          // Agile methodology
    | 'scrum'          // Scrum framework
    | 'xp'             // Extreme Programming
    | 'waterfall'      // Traditional sequential
    | 'devops';        // DevOps practices

/**
 * Workflow Type - High-level workflow categories
 */
export type WorkflowType = OperationMode | SDLCTask | Methodology | 'default';

/**
 * Edge Relationship Types
 */
export type EdgeType =
    // Core flow types
    | 'success'        // Successful execution path
    | 'failure'        // Failure/error path
    | 'default'        // Default pathway

    // Structural relationships
    | 'composition'    // Whole-part relationship
    | 'dependency'     // Dependency relationship
    | 'association'    // General association

    // Special workflow types
    | 'feedback'       // Feedback loop
    | 'validation'     // Validation pathway
    | 'optimization'   // Optimization path
    | 'integration'    // Integration point

    // Control flow
    | 'conditional'    // Conditional branching
    | 'parallel'       // Parallel execution
    | 'synchronization' // Synchronization point

    // Operation modes
    | 'memory'         // Memory-enhanced operations
    | 'document-qa'    // Document Q&A
    | 'refactor'       // Code refactoring
    | 'debug'          // Code debugging
    | 'chat'           // Conversational chat
    | 'ask'            // Question answering with context
    | 'edit'           // Code editing
    | 'codegen'        // Code generation
    | 'multi-agent'    // Multi-agent collaboration

    // Methodologies
    | 'agile'          // Agile methodology
    | 'xp'             // Extreme Programming
    | 'scrum'          // Scrum framework

    // New workflow types
    | 'pr-creation'        // PR creation workflow
    | 'pr-review'          // PR review workflow
    | 'checkpoint'         // Checkpoint system
    | 'mcp'                // Model Context Protocol
    | 'pattern-refactoring' // Pattern-based refactoring
    | 'technical-debt';    // Technical debt reduction

/**
 * Node definition
 */
export interface GraphNode {
    id: string;
    type: NodeType;
    name: string;
    description?: string;
    agent?: Agent;                // For agent nodes
    tool?: ITool;                 // For tool nodes
    condition?: (_state: any) => Promise<string>; // Conditional branching
    config?: Record<string, any>; // Node-specific configuration
    metadata?: Record<string, any>; // Additional metadata
    execute?: (_state: any) => Promise<Partial<GraphState>> | Partial<GraphState>;
}

/**
 * Edge definition
 */
export interface GraphEdge {
    source: string;
    target: string;
    type: EdgeType;
    name: string;
    description?: string;
    weight?: number;
    priority?: number;
    metadata?: Record<string, any>;
    condition?: (_state: any) => boolean | Promise<boolean>;
    conditionType?: 'routing_decision' | 'custom' | 'boolean';
    traversalPolicy?: 'auto' | 'manual' | 'conditional';
}

/**
 * Workflow state definition
 */
export interface GraphState {
    // Core state properties
    messages: BaseMessage[];
    inputs: Record<string, any>;
    outputs: Record<string, any>;

    // Execution context
    currentNode: string;
    previousNode?: string;
    nextNodes?: string[];

    // History tracking
    history: Array<{
        nodeId: string;
        startTime: Date;
        endTime?: Date;
        duration?: number;
        status?: 'success' | 'failure' | 'pending';
        result?: any;
        metrics?: Record<string, any>;
    }>;

    // Custom state extensions
    [key: string]: any;
}

/**
 * Comprehensive workflow definition
 */
export interface GraphDefinition {
    // Core identification
    id: string;
    name: string;
    description: string;
    version: string;

    // Workflow characteristics
    operationMode: OperationMode;
    methodology?: Methodology;
    domain?: string; // e.g. 'software-development', 'data-science'
    type?: WorkflowType; // Workflow type for factory creation

    // Graph structure
    nodes: GraphNode[];
    edges: GraphEdge[];
    startNodeId: string;

    // Codessa agent and tool nodes (optional for advanced workflows)
    agentNodes?: GraphNode[];
    toolNodes?: GraphNode[];

    // Execution configuration
    maxConcurrency?: number;
    retryPolicy?: {
        maxRetries: number;
        backoffFactor: number;
    };

    // Metadata
    tags?: string[];
    metadata?: {
        revolutionaryFeatures?: {
            goddessMode?: boolean;
            quantumAnalysis?: boolean;
            neuralSynthesis?: boolean;
            timeTravelDebugging?: boolean;
            adaptivePersonality?: boolean;
            parallelUniverseTesting?: boolean;
            quantumEntanglement?: boolean;
            consciousnessAnalysis?: boolean;
            synapticLearning?: boolean;
        };
        enhancementLevel?: string;
        aiCapabilities?: string[];
        goddessBlessing?: string;
        [key: string]: any;
    };

    // Lifecycle hooks
    onStart?: (_state: any) => Promise<void>;
    onComplete?: (_state: any) => Promise<void>;
    onError?: (_error: Error, _state: any) => Promise<void>;
}

/**
 * Workflow execution options
 */
export interface GraphExecutionOptions {
    // Execution limits
    maxSteps?: number;
    timeout?: number;

    // Resource management
    resourceLimits?: {
        memoryMB?: number;
        cpuUsage?: number;
    };

    // Monitoring and control
    onProgress?: (_state: any) => void;
    onNodeStart?: (_nodeId: string, _state: any) => void;
    onNodeEnd?: (_nodeId: string, _state: any, _result: any) => void;
    onEdgeTraversed?: (_edge: GraphEdge, _state: any) => void;

    // Context and environment
    context?: AgentContext;
    environment?: 'planning' | 'design' | 'development' | 'testing' | 'staging' | 'production' | 'deployment' | 'maintenance';
}

/**
 * Workflow execution result
 */
export interface GraphExecutionResult {
    success: boolean;
    state: GraphState;
    error?: Error;

    // Performance metrics
    metrics?: {
        totalTime: number;
        nodesExecuted: number;
        averageNodeTime: number;
        resourceUsage?: Record<string, any>;
    };

    // Validation information
    validation?: {
        status: 'valid' | 'invalid' | 'partial';
        issues?: Array<{
            nodeId?: string;
            edgeId?: string;
            message: string;
            severity: 'warning' | 'error';
        }>;
    };
}
