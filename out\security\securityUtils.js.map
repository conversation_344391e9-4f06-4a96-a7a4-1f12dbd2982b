{"version": 3, "file": "securityUtils.js", "sourceRoot": "", "sources": ["../../src/security/securityUtils.ts"], "names": [], "mappings": ";;;AAAA,mCAAoC;AACpC,+BAA+B;AAE/B;;GAEG;AACH,MAAa,aAAc,SAAQ,KAAK;IACS;IAA7C,YAAY,OAAe,EAAkB,OAAiB;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD0B,YAAO,GAAP,OAAO,CAAU;QAE1D,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAChC,CAAC;CACJ;AALD,sCAKC;AAED;;GAEG;AACH,MAAa,aAAa;IACtB,uCAAuC;IAC/B,MAAM,CAAU,eAAe,GAAG,GAAG,CAAC;IAE9C,yCAAyC;IACjC,MAAM,CAAU,wBAAwB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAE9F,qDAAqD;IAC7C,MAAM,CAAU,mBAAmB,GAAG;QAC1C,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;KACV,CAAC;IAEF,iCAAiC;IACzB,MAAM,CAAU,oBAAoB,GAAG;QAC3C,iBAAiB;QACjB,wBAAwB;QACxB,0BAA0B;QAC1B,wBAAwB;QACxB,iBAAiB;QACjB,yBAAyB;QACzB,kBAAkB;QAClB,mBAAmB;QACnB,eAAe;QACf,2BAA2B;KAC9B,CAAC;IAEF,wDAAwD;IAChD,MAAM,CAAU,yBAAyB,GAAG,YAAY,CAAC;IAEjE;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,IAAY;QACnC,wBAAwB;QACxB,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,KAAK,MAAM,GAAG,IAAI,aAAa,CAAC,mBAAmB,EAAE,CAAC;YAClD,SAAS,GAAG,SAAS,CAAC,OAAO,CACzB,IAAI,MAAM,CAAC,IAAI,GAAG,cAAc,GAAG,GAAG,EAAE,KAAK,CAAC,EAC9C,EAAE,CACL,CAAC;YACF,SAAS,GAAG,SAAS,CAAC,OAAO,CACzB,IAAI,MAAM,CAAC,IAAI,GAAG,SAAS,EAAE,IAAI,CAAC,EAClC,EAAE,CACL,CAAC;QACN,CAAC;QAED,4BAA4B;QAC5B,SAAS,GAAG,SAAS,CAAC,OAAO,CACzB,sCAAsC,EACtC,EAAE,CACL,CAAC;QAEF,0BAA0B;QAC1B,SAAS,GAAG,SAAS,CAAC,OAAO,CACzB,iDAAiD,EACjD,EAAE,CACL,CAAC;QAEF,gDAAgD;QAChD,SAAS,GAAG,SAAS,CAAC,OAAO,CACzB,yEAAyE,EACzE,EAAE,CACL,CAAC;QAEF,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,IAAY;QACnC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,eAAe,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,yCAAyC;QACzC,IAAI,aAAa,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,iBAAiB,CAAC,IAAY;QACxC,wCAAwC;QACxC,MAAM,UAAU,GAAG;YACf,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAClE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACrC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC7B,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC7B,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;SAC5B,CAAC;QAEF,uBAAuB;QACvB,OAAO,CACH,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;YACxC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC1C,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC1C,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,CAC/C,CAAC;IACN,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,QAAgB;QACzC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAAC,OAAwB;QACtD,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzE,OAAO,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,IAAY;QACrC,IAAI,CAAC;YACD,6BAA6B;YAC7B,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,sEAAsE;YACtE,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC5C,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACvB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3E,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,IAAqB;QAChD,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE3C,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAE7B,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC7B,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;gBACX,MAAM,WAAW,GAAG,IAAI,GAAG,MAAM,CAAC;gBAClC,OAAO,IAAI,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,IAAY;QACtC,iDAAiD;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,uCAAuC;QACvC,MAAM,UAAU,GAAG;YACf,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,WAAW;YACxD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAE,aAAa;SAC7D,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,uBAAuB,CAAC,QAAgB;QAClD,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,OAAO,aAAa,CAAC,wBAAwB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC;;AAlNL,sCAmNC", "sourcesContent": ["import { createHash } from 'crypto';\r\nimport { extname } from 'path';\r\n\r\n/**\r\n * Error thrown when a security violation is detected\r\n */\r\nexport class SecurityError extends Error {\r\n    constructor(message: string, public readonly details?: unknown) {\r\n        super(message);\r\n        this.name = 'SecurityError';\r\n    }\r\n}\r\n\r\n/**\r\n * Security utilities for clipboard operations\r\n */\r\nexport class SecurityUtils {\r\n    /** Maximum allowed file path length */\r\n    private static readonly MAX_PATH_LENGTH = 255;\r\n    \r\n    /** Allowed file extensions for images */\r\n    private static readonly ALLOWED_IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.webp', '.gif'];\r\n    \r\n    /** Dangerous HTML tags that could contain scripts */\r\n    private static readonly DANGEROUS_HTML_TAGS = [\r\n        'script',\r\n        'iframe',\r\n        'object',\r\n        'embed',\r\n        'form',\r\n        'input',\r\n        'applet',\r\n        'meta',\r\n        'base',\r\n        'link',\r\n        'style'\r\n    ];\r\n\r\n    /** Known dangerous mime types */\r\n    private static readonly DANGEROUS_MIME_TYPES = [\r\n        'text/javascript',\r\n        'application/javascript',\r\n        'application/x-javascript',\r\n        'application/ecmascript',\r\n        'text/ecmascript',\r\n        'application/x-httpd-php',\r\n        'application/x-sh',\r\n        'application/x-csh',\r\n        'text/x-script',\r\n        'application/x-shellscript'\r\n    ];\r\n\r\n    /** Pattern for detecting potential command injection */\r\n    private static readonly COMMAND_INJECTION_PATTERN = /[<>|&;$()]/;\r\n\r\n    /**\r\n     * Sanitize HTML content to prevent XSS attacks\r\n     */\r\n    public static sanitizeHtml(html: string): string {\r\n        // Remove dangerous tags\r\n        let sanitized = html;\r\n        for (const tag of SecurityUtils.DANGEROUS_HTML_TAGS) {\r\n            sanitized = sanitized.replace(\r\n                new RegExp(`<${tag}[^>]*>.*?</${tag}>`, 'gis'),\r\n                ''\r\n            );\r\n            sanitized = sanitized.replace(\r\n                new RegExp(`<${tag}[^>]*/>`, 'gi'),\r\n                ''\r\n            );\r\n        }\r\n\r\n        // Remove on* event handlers\r\n        sanitized = sanitized.replace(\r\n            /\\s+(on\\w+)\\s*=\\s*[\"']?[^\"'>]+[\"']?/gi,\r\n            ''\r\n        );\r\n\r\n        // Remove javascript: URLs\r\n        sanitized = sanitized.replace(\r\n            /\\s+href\\s*=\\s*[\"']?\\s*javascript:[^\"'>]+[\"']?/gi,\r\n            ''\r\n        );\r\n\r\n        // Remove data: URLs except for safe image types\r\n        sanitized = sanitized.replace(\r\n            /\\s+src\\s*=\\s*[\"']?\\s*data:(?!image\\/(jpeg|png|gif|webp);)[^\"'>]+[\"']?/gi,\r\n            ''\r\n        );\r\n\r\n        return sanitized;\r\n    }\r\n\r\n    /**\r\n     * Validate and sanitize a file path\r\n     */\r\n    public static validatePath(path: string): boolean {\r\n        if (!path || path.length > SecurityUtils.MAX_PATH_LENGTH) {\r\n            return false;\r\n        }\r\n\r\n        // Check for path traversal attempts\r\n        if (path.includes('..') || path.includes('\\\\\\\\')) {\r\n            return false;\r\n        }\r\n\r\n        // Check for null bytes\r\n        if (path.includes('\\0')) {\r\n            return false;\r\n        }\r\n\r\n        // Check for command injection characters\r\n        if (SecurityUtils.COMMAND_INJECTION_PATTERN.test(path)) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Validate image data for security\r\n     */\r\n    public static validateImageData(data: Buffer): boolean {\r\n        // Check for known image file signatures\r\n        const signatures = {\r\n            png: Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]),\r\n            jpeg: Buffer.from([0xFF, 0xD8, 0xFF]),\r\n            gif87a: Buffer.from('GIF87a'),\r\n            gif89a: Buffer.from('GIF89a'),\r\n            webp: Buffer.from('RIFF')\r\n        };\r\n\r\n        // Check file signature\r\n        return (\r\n            data.slice(0, 8).equals(signatures.png) ||\r\n            data.slice(0, 3).equals(signatures.jpeg) ||\r\n            data.slice(0, 6).equals(signatures.gif87a) ||\r\n            data.slice(0, 6).equals(signatures.gif89a) ||\r\n            (data.slice(0, 4).equals(signatures.webp) &&\r\n                data.slice(8, 12).toString() === 'WEBP')\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Check if a mime type is potentially dangerous\r\n     */\r\n    public static isSafeMimeType(mimeType: string): boolean {\r\n        return !SecurityUtils.DANGEROUS_MIME_TYPES.includes(mimeType.toLowerCase());\r\n    }\r\n\r\n    /**\r\n     * Generate a secure hash for content validation\r\n     */\r\n    public static generateContentHash(content: Buffer | string): string {\r\n        const buffer = Buffer.isBuffer(content) ? content : Buffer.from(content);\r\n        return createHash('sha256').update(buffer).digest('hex');\r\n    }\r\n\r\n    /**\r\n     * Validate base64 data\r\n     */\r\n    public static validateBase64(data: string): boolean {\r\n        try {\r\n            // Check if it's valid base64\r\n            if (!/^[A-Za-z0-9+/]*={0,2}$/.test(data)) {\r\n                return false;\r\n            }\r\n\r\n            // Check if decoded data contains any null bytes or control characters\r\n            const decoded = Buffer.from(data, 'base64');\r\n            return !decoded.includes(0x00) && \r\n                   !decoded.some(byte => byte < 32 && ![9, 10, 13].includes(byte));\r\n        } catch {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Calculate entropy of data to detect potential malicious content\r\n     */\r\n    public static calculateEntropy(data: Buffer | string): number {\r\n        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);\r\n        const frequencies = new Array(256).fill(0);\r\n        \r\n        for (const byte of buffer) {\r\n            frequencies[byte]++;\r\n        }\r\n\r\n        let entropy = 0;\r\n        const length = buffer.length;\r\n\r\n        for (const freq of frequencies) {\r\n            if (freq > 0) {\r\n                const probability = freq / length;\r\n                entropy -= probability * Math.log2(probability);\r\n            }\r\n        }\r\n\r\n        return entropy;\r\n    }\r\n\r\n    /**\r\n     * Detect potential shellcode in binary data\r\n     */\r\n    public static detectShellcode(data: Buffer): boolean {\r\n        // Check for repeated characters (like NOP sleds)\r\n        const hexData = data.toString('hex');\r\n        if (/(.)\\1{15,}/.test(hexData)) {\r\n            return true;\r\n        }\r\n\r\n        // Check for known shellcode signatures\r\n        const signatures = [\r\n            Buffer.from([0x90, 0x90, 0x90, 0x90, 0x90]), // NOP sled\r\n            Buffer.from([0x00, 0x00, 0x00, 0x00, 0x00])  // Null bytes\r\n        ];\r\n\r\n        return signatures.some(sig => data.includes(sig));\r\n    }\r\n\r\n    /**\r\n     * Validate file extension\r\n     */\r\n    public static isAllowedImageExtension(filename: string): boolean {\r\n        const ext = extname(filename).toLowerCase();\r\n        return SecurityUtils.ALLOWED_IMAGE_EXTENSIONS.includes(ext);\r\n    }\r\n}\r\n"]}