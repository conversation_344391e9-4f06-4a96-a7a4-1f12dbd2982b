// Centralized theme config for modals and settings sections
// Modern, accessible, appealing defaults for both light and dark mode

export type ModalTheme = {
    background: string;
    foreground: string;
    border: string;
    borderRadius: string;
    boxShadow: string;
    headerFontWeight: string;
    headerFontSize: string;
    contentFontSize: string;
    button: {
        background: string;
        color: string;
        border: string;
        borderRadius: string;
        shadow: string;
    };
    buttonDestructive: {
        background: string;
        color: string;
        border: string;
        borderRadius: string;
        shadow: string;
    };
    animation: {
        duration: string;
        scale: string;
    };
    warningColor: string;
    warningIcon: string;
};

export type SectionTheme = {
    headerColor: string;
    headerBg: string;
    border: string;
    borderRadius: string;
    accent: string;
    button: {
        background: string;
        color: string;
        border: string;
        borderRadius: string;
    };
};

export type UIThemeConfig = {
    mode: 'light' | 'dark' | 'system';
    accentColor: string;
    modal: ModalTheme;
    section: SectionTheme;
};

export const defaultUIThemeConfig: UIThemeConfig = {
  mode: 'system',
  accentColor: '#2563eb',
  modal: {
    background: '#fff',
    foreground: '#222',
    border: '#e5e7eb',
    borderRadius: '14px',
    boxShadow: '0 12px 36px rgba(0,0,0,0.20)',
    headerFontWeight: '700',
    headerFontSize: '1.35em',
    contentFontSize: '1.08em',
    button: {
      background: '#2563eb',
      color: '#fff',
      border: 'none',
      borderRadius: '5px',
      shadow: 'none',
    },
    buttonDestructive: {
      background: '#dc2626',
      color: '#fff',
      border: '1.5px solid #dc2626',
      borderRadius: '5px',
      shadow: '0 1px 6px 0 rgba(220,38,38,0.10)',
    },
    animation: {
      duration: '0.22s',
      scale: '0.96',
    },
    warningColor: '#b91c1c',
    warningIcon: '<svg width=\'28\' height=\'28\' viewBox=\'0 0 20 20\' fill=\'none\' aria-hidden=\'true\' focusable=\'false\'><circle cx=\'10\' cy=\'10\' r=\'9\' fill=\'#fff3f3\' stroke=\'#dc2626\' stroke-width=\'2\'/><path d=\'M10 6v5\' stroke=\'#dc2626\' stroke-width=\'1.7\' stroke-linecap=\'round\'/><circle cx=\'10\' cy=\'14.2\' r=\'1.2\' fill=\'#dc2626\'/></svg>',
  },
  section: {
    headerColor: '#222',
    headerBg: '#f8fafc',
    border: '#e5e7eb',
    borderRadius: '10px',
    accent: '#2563eb',
    button: {
      background: '#2563eb',
      color: '#fff',
      border: 'none',
      borderRadius: '5px',
    },
  },
};
