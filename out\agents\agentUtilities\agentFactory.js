"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createModeAgent = createModeAgent;
const agent_1 = require("./agent");
const logger_1 = require("../../logger");
const chatAgent_1 = require("../agentTypes/chatAgent");
const editAgent_1 = require("../agentTypes/editAgent");
const debugAgent_1 = require("../agentTypes/debugAgent");
const refactorAgent_1 = require("../agentTypes/refactorAgent");
const documentAgent_1 = require("../agentTypes/documentAgent");
/**
 * Factory function to create the appropriate specialized agent for a mode
 */
function createModeAgent(mode, _config) {
    switch (mode) {
        case 'chat':
            return new chatAgent_1.ChatAgent(_config);
        case 'edit':
            return new editAgent_1.EditAgent(_config);
        case 'debug':
            return new debugAgent_1.DebugAgent(_config);
        case 'refactor':
            return new refactorAgent_1.RefactorAgent(_config);
        case 'document':
            return new documentAgent_1.DocumentAgent(_config);
        default:
            // For other modes, create a generic agent
            logger_1.logger.info(`No specialized agent class found for mode: ${mode}. Using generic Agent.`);
            return new agent_1.Agent({
                id: _config.id || `agent-${Date.now()}`,
                name: _config.name || `Agent-${mode}`,
                systemPromptName: _config.systemPromptName || 'default',
                ..._config,
                role: 'assistant',
                capabilities: ['chat'],
                llmProvider: 'openai',
                llmModel: 'gpt-4'
            });
    }
}
//# sourceMappingURL=agentFactory.js.map