"use strict";
/**
 * Professional Settings Manager
 * Handles all settings operations with proper validation, persistence, and error handling
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsManager = void 0;
const vscode = __importStar(require("vscode"));
const config_1 = require("../../config");
const logger_1 = require("../../logger");
class SettingsManager {
    static instance;
    settings = new Map();
    validators = new Map();
    changeListeners = new Map();
    saveTimeout = null;
    isInitialized = false;
    static getInstance() {
        if (!SettingsManager.instance) {
            SettingsManager.instance = new SettingsManager();
        }
        return SettingsManager.instance;
    }
    constructor() {
        this.initializeValidators();
    }
    /**
       * Initialize the settings manager (should be called after VS Code is ready)
       */
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        try {
            await this.loadAllSettings();
            this.isInitialized = true;
            logger_1.Logger.instance.info('Settings manager initialized successfully');
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to initialize settings manager:', error);
            throw error;
        }
    }
    /**
       * Initialize validation rules for all settings
       */
    initializeValidators() {
        // Revolutionary Features Validators
        this.addValidator('revolutionaryFeatures.goddessMode.adaptiveLevel', (value) => {
            const errors = [];
            const warnings = [];
            if (typeof value !== 'number' || value < 0 || value > 100) {
                errors.push('Adaptive level must be a number between 0 and 100');
            }
            else if (value < 50) {
                warnings.push('Low adaptive level may reduce Goddess Mode effectiveness');
            }
            return { isValid: errors.length === 0, errors, warnings };
        });
        this.addValidator('revolutionaryFeatures.quantumAnalysis.confidenceThreshold', (value) => {
            const errors = [];
            const warnings = [];
            if (typeof value !== 'number' || value < 0 || value > 1) {
                errors.push('Confidence threshold must be a number between 0 and 1');
            }
            else if (value < 0.5) {
                warnings.push('Low confidence threshold may produce unreliable results');
            }
            return { isValid: errors.length === 0, errors, warnings };
        });
        this.addValidator('revolutionaryFeatures.neuralSynthesis.learningRate', (value) => {
            const errors = [];
            const warnings = [];
            if (typeof value !== 'number' || value < 0.001 || value > 0.1) {
                errors.push('Learning rate must be a number between 0.001 and 0.1');
            }
            else if (value > 0.05) {
                warnings.push('High learning rate may cause instability');
            }
            return { isValid: errors.length === 0, errors, warnings };
        });
        // Provider Settings Validators
        this.addValidator('providers.*.apiKey', (value) => {
            const errors = [];
            const warnings = [];
            if (typeof value !== 'string' || value.trim().length === 0) {
                errors.push('API key is required');
            }
            else if (value.length < 10) {
                warnings.push('API key seems too short');
            }
            return { isValid: errors.length === 0, errors, warnings };
        });
        // Memory Settings Validators
        this.addValidator('memory.maxMemories', (value) => {
            const errors = [];
            const warnings = [];
            if (typeof value !== 'number' || value < 10 || value > 10000) {
                errors.push('Max memories must be a number between 10 and 10000');
            }
            else if (value > 5000) {
                warnings.push('High memory count may impact performance');
            }
            return { isValid: errors.length === 0, errors, warnings };
        });
        // General Settings Validators
        this.addValidator('general.theme', (value) => {
            const errors = [];
            const validThemes = ['system', 'light', 'dark'];
            if (typeof value !== 'string' || !validThemes.includes(value)) {
                errors.push('Theme must be one of: system, light, dark');
            }
            return { isValid: errors.length === 0, errors, warnings: [] };
        });
        this.addValidator('general.language', (value) => {
            const errors = [];
            const validLanguages = ['en', 'de', 'fr', 'es', 'zh'];
            if (typeof value !== 'string' || !validLanguages.includes(value)) {
                errors.push('Language must be one of: en, de, fr, es, zh');
            }
            return { isValid: errors.length === 0, errors, warnings: [] };
        });
    }
    /**
       * Add a validator for a specific setting key
       */
    addValidator(key, validator) {
        // Use type assertion to handle the generic type
        this.validators.set(key, validator);
    }
    /**
       * Load all settings from VS Code configuration
       */
    async loadAllSettings() {
        try {
            const config = vscode.workspace.getConfiguration('codessa');
            // Load all configuration keys by inspecting the configuration object
            const configValues = JSON.parse(JSON.stringify(config));
            const allKeys = Object.keys(configValues);
            for (const key of allKeys) {
                try {
                    const value = config.get(key);
                    this.settings.set(key, value);
                }
                catch (error) {
                    logger_1.Logger.instance.warn(`Failed to load setting ${key}:`, error);
                }
            }
            logger_1.Logger.instance.info(`Loaded ${this.settings.size} settings successfully`);
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to load settings:', error);
            throw error;
        }
    }
    /**
       * Get a setting value with type safety
       */
    getSetting(key, defaultValue) {
        if (!this.isInitialized) {
            logger_1.Logger.instance.warn('Settings manager not initialized, returning default value');
            return defaultValue;
        }
        const value = this.settings.get(key);
        return value !== undefined ? value : defaultValue;
    }
    /**
       * Set a setting value with validation
       */
    async setSetting(key, value, immediate = false) {
        if (!this.isInitialized) {
            logger_1.Logger.instance.warn('Settings manager not initialized, attempting to initialize');
            await this.initialize();
        }
        // Validate the value
        const validation = this.validateSetting(key, value);
        if (!validation.isValid) {
            logger_1.Logger.instance.warn(`Invalid setting value for ${key}:`, validation.errors);
            return validation;
        }
        // Update local cache
        this.settings.set(key, value);
        // Notify listeners
        this.notifyChangeListeners(key, value);
        // Save to VS Code configuration
        if (immediate) {
            await this.saveSettingImmediate(key, value);
        }
        else {
            this.debouncedSave(key, value);
        }
        return validation;
    }
    /**
       * Validate a setting value
       */
    validateSetting(key, value) {
        const validator = this.validators.get(key);
        if (validator) {
            // Safe to call with any value since validators are added with proper type checking
            return validator(value);
        }
        // Check for wildcard validators
        for (const [validatorKey, validatorFn] of this.validators.entries()) {
            if (validatorKey.includes('*')) {
                const pattern = validatorKey.replace('*', '.*');
                const regex = new RegExp(`^${pattern}$`);
                if (regex.test(key)) {
                    return validatorFn(value);
                }
            }
        }
        // Default validation - just check if value is not undefined
        return {
            isValid: value !== undefined,
            errors: value === undefined ? ['Value cannot be undefined'] : [],
            warnings: []
        };
    }
    /**
       * Save setting immediately to VS Code configuration
       */
    async saveSettingImmediate(key, value) {
        try {
            const success = await (0, config_1.setConfig)(key, value);
            if (!success) {
                throw new Error(`Failed to save setting ${key}`);
            }
            logger_1.Logger.instance.debug(`Setting saved: ${key}`);
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to save setting ${key}:`, error);
            throw error;
        }
    }
    /**
       * Debounced save to avoid excessive writes
       */
    debouncedSave(key, value) {
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }
        this.saveTimeout = setTimeout(async () => {
            try {
                await this.saveSettingImmediate(key, value);
            }
            catch (error) {
                console.error('Debounced save error:', error);
            }
        }, 500); // 500ms debounce
    }
    /**
       * Add a change listener for a specific setting
       */
    addChangeListener(key, listener) {
        console.log('Adding change listener for key:', key);
        if (!this.changeListeners.has(key)) {
            this.changeListeners.set(key, []);
        }
        // Use type assertion to handle the generic type
        this.changeListeners.get(key).push(listener);
    }
    /**
       * Remove a change listener
       */
    removeChangeListener(key, listener) {
        console.log('Removing change listener for key:', key);
        const listeners = this.changeListeners.get(key);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }
    /**
       * Notify all change listeners for a setting
       */
    notifyChangeListeners(key, value) {
        const listeners = this.changeListeners.get(key);
        if (listeners) {
            listeners.forEach(listener => {
                try {
                    // Safe to call with any value since listeners are added with proper type checking
                    listener(value);
                }
                catch (error) {
                    logger_1.Logger.instance.error(`Error in change listener for ${key}:`, error);
                }
            });
        }
    }
    /**
       * Get all settings as a plain object
       */
    getAllSettings() {
        const result = {};
        for (const [key, value] of this.settings.entries()) {
            result[key] = value;
        }
        return result;
    }
    /**
       * Reset a setting to its default value
       */
    async resetSetting(key) {
        try {
            const config = vscode.workspace.getConfiguration('codessa');
            const inspection = config.inspect(key);
            if (inspection?.defaultValue !== undefined) {
                await this.setSetting(key, inspection.defaultValue, true);
            }
            else {
                // Remove the setting to use VS Code default
                await (0, config_1.setConfig)(key, undefined);
                this.settings.delete(key);
            }
            logger_1.Logger.instance.info(`Setting reset: ${key}`);
        }
        catch (error) {
            logger_1.Logger.instance.error(`Failed to reset setting ${key}:`, error);
            throw error;
        }
    }
    /**
       * Reset all settings to defaults
       */
    async resetAllSettings() {
        try {
            const config = vscode.workspace.getConfiguration('codessa');
            console.log('Resetting all settings for config:', config.keys());
            const allKeys = Array.from(this.settings.keys());
            for (const key of allKeys) {
                await this.resetSetting(key);
            }
            logger_1.Logger.instance.info('All settings reset to defaults');
        }
        catch (error) {
            logger_1.Logger.instance.error('Failed to reset all settings:', error);
            throw error;
        }
    }
    /**
       * Export settings to JSON
       */
    exportSettings() {
        return JSON.stringify(this.getAllSettings(), null, 2);
    }
    /**
       * Import settings from JSON
       */
    async importSettings(jsonString) {
        const errors = [];
        try {
            const importedSettings = JSON.parse(jsonString);
            for (const [key, value] of Object.entries(importedSettings)) {
                try {
                    const validation = await this.setSetting(key, value, true);
                    if (!validation.isValid) {
                        errors.push(`${key}: ${validation.errors.join(', ')}`);
                    }
                }
                catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    errors.push(`${key}: Failed to import - ${errorMessage}`);
                }
            }
            return { success: errors.length === 0, errors };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Invalid JSON format';
            return { success: false, errors: [errorMessage] };
        }
    }
    /**
       * Get setting sections configuration
       */
    getSettingSections() {
        return [
            {
                id: 'revolutionary',
                title: 'Revolutionary Features',
                icon: '✨',
                description: 'Configure advanced AI capabilities including Goddess Mode, Quantum Analysis, and Neural Synthesis',
                order: 1,
                component: 'revolutionaryFeaturesSection'
            },
            {
                id: 'general',
                title: 'General',
                icon: '⚙️',
                description: 'Basic extension settings and preferences',
                order: 2,
                component: 'generalSettingsSection'
            },
            {
                id: 'providers',
                title: 'AI Providers',
                icon: '🤖',
                description: 'Configure AI model providers and API settings',
                order: 3,
                component: 'providersModelsSettingsSection'
            },
            {
                id: 'agents',
                title: 'Agents',
                icon: '👥',
                description: 'Manage AI agents and their configurations',
                order: 4,
                component: 'agentsSettingsSection'
            },
            {
                id: 'memory',
                title: 'Memory',
                icon: '🧠',
                description: 'Configure memory system and storage options',
                order: 5,
                component: 'memorySettingsSection'
            },
            {
                id: 'workflows',
                title: 'Workflows',
                icon: '🔄',
                description: 'Manage automated workflows and templates',
                order: 6,
                component: 'workflowsSettingsSection'
            },
            {
                id: 'ui',
                title: 'Interface',
                icon: '🎨',
                description: 'User interface and theme customization',
                order: 7,
                component: 'uiThemeSettingsSection'
            },
            {
                id: 'tts',
                title: 'Text-to-Speech',
                icon: '🔊',
                description: 'Configure text-to-speech and voice settings',
                order: 8,
                component: 'ttsSettingsSection'
            },
            {
                id: 'advanced',
                title: 'Advanced',
                icon: '🔧',
                description: 'Advanced settings and developer options',
                order: 9,
                component: 'advancedSettingsSection'
            }
        ].sort((a, b) => a.order - b.order);
    }
    /**
       * Check if settings manager is initialized
       */
    isReady() {
        return this.isInitialized;
    }
    /**
       * Dispose of the settings manager
       */
    dispose() {
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
            this.saveTimeout = null;
        }
        this.changeListeners.clear();
        this.settings.clear();
        this.validators.clear();
        this.isInitialized = false;
    }
}
exports.SettingsManager = SettingsManager;
//# sourceMappingURL=settingsManager.js.map