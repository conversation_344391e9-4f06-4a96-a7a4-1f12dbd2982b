"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditAgent = void 0;
const agent_1 = require("../agentUtilities/agent");
const logger_1 = require("../../logger");
const cascadeEditingTool_1 = require("../../tools/cascadeEditingTool");
class EditAgent extends agent_1.Agent {
    cascadeEditTool;
    constructor(_config) {
        super({
            ..._config,
            role: 'assistant',
            capabilities: ['edit'],
            llmProvider: 'openai',
            llmModel: 'gpt-4'
        });
        this.cascadeEditTool = new cascadeEditingTool_1.CascadeEditingTool();
    }
    async run(input, context = {}) {
        logger_1.logger.info(`EditAgent processing: "${input.prompt.substring(0, 50)}..."`);
        // --- Gather relevant workflow for edit mode ---
        let workflow = undefined;
        if (context.variables?.workflowManager) {
            workflow = await context.variables.workflowManager.getWorkflowForMode('edit');
        }
        // --- Gather all relevant tools ---
        let tools = this.tools;
        if (context.tools) {
            tools = context.tools;
        }
        // --- Gather memory and knowledgebase context ---
        let memoryContext = '';
        if (this.getMemory) {
            const memories = await this.getMemory().getRelevantMemories(input.prompt);
            if (memories?.length) {
                memoryContext = this.getMemory().formatMemoriesForPrompt(memories);
            }
        }
        let kbContext = '';
        if (context.variables?.knowledgebaseManager) {
            kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input.prompt);
        }
        // --- Gather MCP context ---
        let mcpContext = '';
        if (context.variables?.mcpManager) {
            mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext() || {});
        }
        // --- Use external prompt if available ---
        let externalPrompt = '';
        if (context.variables?.promptManager) {
            externalPrompt = context.variables.promptManager.getSystemPrompt('editAgent', context.variables) || '';
        }
        // Phase 2: Enhanced editing with cascade capabilities (Optimized)
        const shouldUseCascadeEditing = this.shouldUseCascadeEditing(input.prompt);
        let cascadeContext = '';
        if (shouldUseCascadeEditing && context.workspace?.currentFile) {
            try {
                const filePath = typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile;
                // Validate file path
                if (!filePath || filePath.trim().length === 0) {
                    logger_1.logger.warn('Invalid file path for cascade editing');
                    cascadeContext = '\n## Cascade Edit Analysis: Invalid file path';
                }
                else {
                    // Analyze potential cascade impact with timeout
                    const impactPromise = this.cascadeEditTool.execute('analyzeCascadeImpact', {
                        filePath,
                        changes: [] // Would be populated with actual proposed changes
                    });
                    const impactResult = await Promise.race([
                        impactPromise,
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Cascade analysis timeout')), 15000))
                    ]);
                    if (impactResult.success && impactResult.output) {
                        // Limit the output size to prevent overwhelming the prompt
                        const analysis = impactResult.output;
                        const limitedAnalysis = {
                            ...analysis,
                            dependentEdits: analysis.dependentEdits?.slice(0, 5) || [], // Limit to 5 files
                            impactAnalysis: {
                                ...analysis.impactAnalysis,
                                testFilesAffected: analysis.impactAnalysis?.testFilesAffected?.slice(0, 3) || []
                            }
                        };
                        cascadeContext = `\n## Cascade Edit Analysis:\n${JSON.stringify(limitedAnalysis, null, 2)}`;
                    }
                    else {
                        cascadeContext = '\n## Cascade Edit Analysis: Analysis failed';
                    }
                }
            }
            catch (error) {
                logger_1.logger.warn(`Cascade editing analysis failed: ${error}`);
                cascadeContext = '\n## Cascade Edit Analysis: Not available due to error';
            }
        }
        // --- Compose final prompt ---
        const editPrompt = `
# Enhanced Edit Mode Processing with Cascade Editing

${externalPrompt}

## User Request:
${input.prompt}

## Memory Context:
${memoryContext}

## Knowledgebase Context:
${kbContext}

## MCP Context:
${mcpContext}

## Workflow:
${workflow ? JSON.stringify(workflow) : 'None'}

## Current File Context:
${context.workspace?.currentFile ? (typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile) : 'No file context available'}

${cascadeContext}

## Your Enhanced Editing Task:
1. Analyze the requested changes and their potential impact
2. Consider dependency relationships and cascade effects
3. Provide specific code edits that address the user's request
4. If cascade editing is needed, identify all affected files
5. Suggest validation steps to ensure changes don't break dependencies

## Available Advanced Tools:
- Cascade editing for dependency-aware changes
- Impact analysis for multi-file edits
- Dependency validation
- Automated propagation of changes

Use all available workflows, tools, memory, knowledgebase, and cascade context. Stream output if possible.
`;
        input.prompt = editPrompt;
        // Pass all gathered context and tools to super.run
        return super.run(input, { ...context, tools });
    }
    /**
       * Determine if cascade editing should be used
       */
    shouldUseCascadeEditing(prompt) {
        const cascadeIndicators = [
            'refactor',
            'rename',
            'change interface',
            'update signature',
            'modify api',
            'breaking change',
            'multiple files',
            'dependencies',
            'propagate',
            'update all',
            'global change'
        ];
        const promptLower = prompt.toLowerCase();
        return cascadeIndicators.some(indicator => promptLower.includes(indicator));
    }
}
exports.EditAgent = EditAgent;
//# sourceMappingURL=editAgent.js.map