{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/mcp/index.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,2CAAkE;AAAzD,wGAAA,UAAU,OAAA;AAAc,wGAAA,UAAU,OAAA", "sourcesContent": ["/**\n * Model Context Protocol (MCP) Integration\n * \n * This module provides integration with the Model Context Protocol (MCP),\n * which is a standard for exchanging context between different AI models\n * and tools.\n */\n\nexport { MCPManager, MCPContext, mcpManager } from './mcpManager';\n"]}