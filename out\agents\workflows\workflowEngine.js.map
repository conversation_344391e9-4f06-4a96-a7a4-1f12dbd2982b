{"version": 3, "file": "workflowEngine.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/workflowEngine.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AAG7B,yCAAsC;AAEtC,iEAA8D;AAC9D,+DAAyE;AACzE,yEAAsE;AACtE,iFAA8E;AAC9E,iFAA8E;AAG9E,+DAA4D;AA2I5D;;GAEG;AACH,MAAa,QAAQ;IACX,UAAU,CAAqB;IAC/B,OAAO,CAAkB;IACzB,SAAS,GAAG,KAAK,CAAC;IAClB,WAAW,GAAG,KAAK,CAAC;IACpB,kBAAkB,CAAgD;IAClE,mBAAmB,CAA8D;IAEzF,sBAAsB;IACd,WAAW,CAAuB;IAClC,UAAU,CAA2B;IACrC,cAAc,CAA2B;IACzC,4BAA4B,GAAG;QACrC,WAAW,EAAE,KAAK;QAClB,eAAe,EAAE,KAAK;QACtB,eAAe,EAAE,KAAK;QACtB,mBAAmB,EAAE,KAAK;QAC1B,mBAAmB,EAAE,KAAK;KAC3B,CAAC;IAEF,uCAAuC;IAC/B,SAAS,GAAG,IAAI,GAAG,EAAe,CAAC;IACnC,MAAM,CAAU,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;IAE/D,sBAAsB;IACd,gBAAgB,GAAG;QACzB,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,CAAC;QACd,aAAa,EAAE,CAAC;QAChB,KAAK,EAAE,IAAI,GAAG,EAAyE;KACxF,CAAC;IAEF;;SAEK;IACL,YAAY,UAA8B;QACxC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG;YACb,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,EAAW,EAAE,+BAA+B;YACnD,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,EAAE;YAEX,oCAAoC;YACpC,qBAAqB,EAAE;gBACrB,WAAW,EAAE,KAAK;gBAClB,eAAe,EAAE,KAAK;gBACtB,eAAe,EAAE,KAAK;gBACtB,mBAAmB,EAAE,KAAK;gBAC1B,mBAAmB,EAAE,KAAK;aAC3B;YAED,gBAAgB,EAAE;gBAChB,aAAa,EAAE,SAAS;gBACxB,iBAAiB,EAAE,EAAE;gBACrB,WAAW,EAAE,EAAE;gBACf,eAAe,EAAE,CAAC;aACnB;YAED,YAAY,EAAE;gBACZ,aAAa,EAAE,KAAK;gBACpB,SAAS,EAAE,KAAK;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,mBAAmB,EAAE,KAAK;aAC3B;YAED,YAAY,EAAE;gBACZ,mBAAmB,EAAE,IAAI,GAAG,EAAE;gBAC9B,eAAe,EAAE,EAAE;gBACnB,kBAAkB,EAAE,CAAC;aACtB;SACF,CAAC;QAEF,iCAAiC;QACjC,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACtC,CAAC;IAED;;SAEK;IACG,4BAA4B;QAClC,IAAI,CAAC;YACH,IAAI,CAAC,WAAW,GAAG,IAAI,yCAAmB,EAAE,CAAC;YAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,iDAAuB,EAAE,CAAC;YAChD,IAAI,CAAC,cAAc,GAAG,IAAI,iDAAuB,EAAE,CAAC;YAEpD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAChF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAED;;SAEK;IACE,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;SAEK;IACE,QAAQ,CAAC,KAAY;QAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED;;SAEK;IACE,SAAS,CAAC,MAA2B;QAC1C,kBAAkB;QAClB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAC9C,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,IAAI,QAAQ,CAAC,EAAE,CAAC;gBAC9E,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;QACzB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAC9C,IAAI,QAAQ,CAAC,EAAE,IAAI,MAAM,EAAE,CAAC;gBAC1B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACzD,CAAC;iBAAM,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;gBACjC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC;YACtD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACE,UAAU,CAAC,QAAsD;QACtE,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;IACrC,CAAC;IAED;;SAEK;IACE,WAAW,CAAC,QAAoE;QACrF,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;IACtC,CAAC;IAED;;SAEK;IACE,sBAAsB,CAAC,QAM7B;QACC,IAAI,CAAC,4BAA4B,GAAG;YAClC,GAAG,IAAI,CAAC,4BAA4B;YACpC,GAAG,QAAQ;SACZ,CAAC;QAEF,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,qBAAqB,GAAG;gBACnC,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB;gBACrC,GAAG,QAAQ;aACZ,CAAC;QACJ,CAAC;QAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8BAA8B,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC1J,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,KAAU;QACvD,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,IAAI,GAAG,sCAAwB,CAAC,oBAAoB,CACxD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EACrB;gBACE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE;gBAChC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM;gBACzE,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC1F,cAAc,EAAE,EAAE;aACnB,CACF,CAAC;YAEF,4BAA4B;YAC5B,MAAM,eAAe,GAAG,sCAAwB,CAAC,uBAAuB,CACtE,4BAA4B,MAAM,EAAE,EACpC,IAAI,EACJ,EAAE,cAAc,EAAE,EAAE,EAAE,CACvB,CAAC;YAEF,2BAA2B;YAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAClC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC3D,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,GAAG,EAAE,CAAC,CAAC;gBACtH,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,eAAe,GAAG,eAAe,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/F,CAAC;YAED,mCAAmC;YACnC,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,EAAE;oBACf,gBAAgB,EAAE,eAAe,CAAC,gBAAgB,IAAI,EAAE;oBACxD,aAAa,EAAE,eAAe,CAAC,aAAa,IAAI,EAAE;oBAClD,iBAAiB,EAAE,eAAe,CAAC,mBAAmB,IAAI,EAAE;iBAC7D;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,KAAU;QAC3D,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,6BAA6B,EAAE;gBAC3E,MAAM,EAAE,6BAA6B;gBACrC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;aAC5B,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,uBAAuB;gBACvB,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;oBAC9B,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC/C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC;gBACrF,CAAC;gBAED,OAAO;oBACL,GAAG,KAAK;oBACR,eAAe,EAAE,MAAM,CAAC,MAAM;iBAC/B,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,KAAU;QAC3D,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,wBAAwB,EAAE;gBACrE,MAAM,EAAE,wBAAwB;gBAChC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;gBAC3B,MAAM,EAAE,4BAA4B,MAAM,EAAE;aAC7C,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,uBAAuB;gBACvB,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;oBAC9B,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,SAAS,IAAI,CAAC,CAAC;oBAChG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC;wBAC7C,MAAM;wBACN,OAAO,EAAE,oBAAoB;wBAC7B,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,aAAa,IAAI,CAAC;qBAC/D,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO;oBACL,GAAG,KAAK;oBACR,cAAc,EAAE,MAAM,CAAC,MAAM;iBAC9B,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,wBAAwB,CAAC,MAAc,EAAE,KAAU;QAC/D,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACnF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBACtE,QAAQ,EAAE,oBAAoB;gBAC9B,WAAW,EAAE,SAAS;aACvB,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,OAAO;oBACL,GAAG,KAAK;oBACR,qBAAqB,EAAE,MAAM,CAAC,MAAM;iBACrC,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAwHK;IACE,MAAM;QACX,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;SAEK;IACE,iBAAiB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;SAEK;IACE,UAAU;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;;AAxcH,4BAycC;AAED;;GAEG;AACH,MAAa,gBAAgB;IACnB,MAAM,CAAC,QAAQ,CAAmB;IAClC,SAAS,GAAG,IAAI,GAAG,EAA8B,CAAC;IAE1D,gBAAwB,CAAC;IAEzB;;SAEK;IACE,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAC/B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACrD,CAAC;QACD,OAAO,gBAAgB,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED;;SAEK;IACE,gBAAgB,CAAC,QAA4B;QAClD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED;;SAEK;IACE,WAAW,CAAC,EAAU;QAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IAED;;SAEK;IACE,eAAe;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;SAEK;IACE,sBAAsB,CAAC,EAAU;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAExC,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;CACF;AAjDD,4CAiDC;AAED,4BAA4B;AACf,QAAA,gBAAgB,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC;AAE/D;;GAEG;AACU,QAAA,mBAAmB,GAAiC;IAC/D;;SAEK;IACL,SAAS,EAAE;QACT,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,8BAA8B;QAC3C,SAAS,EAAE,EAAE;QACb,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,uBAAuB,CAAC;gBAChE,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAClD,MAAM;oBACN,cAAc,EAAE,IAAI;iBACrB,CAAC,CAAC;gBAEH,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBAC7B,iBAAiB;oBACjB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sBAAsB;qBAC9B,CAAC;gBACJ,CAAC;gBAED,iCAAiC;gBACjC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,WAAW,CAAC;gBAC1D,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;gBAExC,OAAO;oBACL,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBACrD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBAC3F,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,iBAAiB,EAAE;QACjB,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,gBAAgB;QACtB,WAAW,EAAE,iCAAiC;QAC9C,SAAS,EAAE,EAAE;QACb,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa,KAAK,KAAK,CAAC;gBAC7D,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;oBAClD,cAAc,EAAE,IAAI;oBACpB,gBAAgB,EAAE,KAAK;oBACvB,aAAa;oBACb,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,cAAc;iBACtD,CAAC,CAAC;gBAEH,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACvC,4CAA4C;oBAC5C,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB;qBAC3B,CAAC;gBACJ,CAAC;gBAED,0CAA0C;gBAC1C,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,eAAe,CAAC;gBAC9D,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAE7D,OAAO;oBACL,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;gBAC9D,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBAC1F,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,mBAAmB,EAAE;QACnB,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,mCAAmC;QAChD,SAAS,EAAE,EAAE;QACb,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa,KAAK,KAAK,CAAC;gBAC7D,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;oBACpD,cAAc,EAAE,KAAK;oBACrB,gBAAgB,EAAE,IAAI;oBACtB,aAAa;oBACb,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,gBAAgB;iBACxD,CAAC,CAAC;gBAEH,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3C,8CAA8C;oBAC9C,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,qBAAqB;qBAC7B,CAAC;gBACJ,CAAC;gBAED,4CAA4C;gBAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,iBAAiB,CAAC;gBAChE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAE/D,OAAO;oBACL,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBAChE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBAC5F,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,gBAAgB,EAAE;QAChB,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,gCAAgC;QAC7C,SAAS,EAAE,EAAE;QACb,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;gBAErC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sCAAsC;qBAC9C,CAAC;gBACJ,CAAC;gBAED,2BAA2B;gBAC3B,IAAI,cAAc,GAAG,EAAE,CAAC;gBACxB,IAAI,OAAO,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;oBACjC,MAAM,aAAa,GAAkB,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC;oBAClE,cAAc,GAAG,MAAM,+BAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBACzE,CAAC;gBAED,0BAA0B;gBAC1B,MAAM,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,cAAc,OAAO,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;gBAE9E,mCAAmC;gBACnC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAE1D,6CAA6C;gBAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,kBAAkB,CAAC;gBACjE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC;gBAEtC,OAAO;oBACL,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;gBAC7D,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBAC7F,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,eAAe,EAAE;QACf,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,0BAA0B;QACvC,SAAS,EAAE,EAAE;QACb,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACzC,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;gBAEvC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,wCAAwC;qBAChD,CAAC;gBACJ,CAAC;gBAED,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;oBAC1B,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sCAAsC;qBAC9C,CAAC;gBACJ,CAAC;gBAED,8BAA8B;gBAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACzC,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAExD,iBAAiB;gBACjB,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBAExD,qCAAqC;gBACrC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,iBAAiB,CAAC;gBAChE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC;gBAEtC,OAAO;oBACL,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBAC5D,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBACvF,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,eAAe,EAAE;QACf,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,2BAA2B;QACxC,SAAS,EAAE,EAAE;QACb,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAEzC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,wCAAwC;qBAChD,CAAC;gBACJ,CAAC;gBAED,gBAAgB;gBAChB,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAE9D,mCAAmC;gBACnC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,aAAa,CAAC;gBAC5D,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;gBAErC,OAAO;oBACL,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBAC5D,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBACvF,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,eAAe,EAAE;QACf,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,+BAA+B;QAC5C,SAAS,EAAE,EAAE;QACb,aAAa,EAAE,IAAI;QACnB,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC;gBAC7C,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;gBAE/C,IAAI,CAAC,UAAU,IAAI,CAAC,WAAW,EAAE,CAAC;oBAChC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,yDAAyD;qBACjE,CAAC;gBACJ,CAAC;gBAED,yBAAyB;gBACzB,IAAI,eAAe,GAAG,KAAK,CAAC;gBAE5B,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE,CAAC;oBACpC,qBAAqB;oBACrB,eAAe,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC7C,CAAC;qBAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;oBACzC,2CAA2C;oBAC3C,wEAAwE;oBACxE,IAAI,CAAC;wBAEH,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;oBACpC,CAAC;oBAAC,OAAO,SAAS,EAAE,CAAC;wBACnB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC;wBAChE,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,+BAA+B,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;yBAC3G,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,oBAAoB;oBACpB,eAAe,GAAG,CAAC,CAAC,SAAS,CAAC;gBAChC,CAAC;gBAED,yCAAyC;gBACzC,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC;gBAE9D,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,UAAU;iBACX,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBAC3D,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBAC9F,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,oBAAoB,EAAE;QACpB,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,mBAAmB;QACzB,WAAW,EAAE,gCAAgC;QAC7C,SAAS,EAAE,EAAE;QACb,aAAa,EAAE,IAAI;QACnB,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,0BAA0B,CAAC;gBACrE,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,IAAI,KAAK,CAAC;gBAC1D,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC;gBACvD,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC;gBACnD,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;gBAEjD,IAAI,CAAC,aAAa,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mEAAmE;qBAC3E,CAAC;gBACJ,CAAC;gBAED,uBAAuB;gBACvB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACvD,OAAO,EACP,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,YAAY,EACZ,WAAW,CACZ,CAAC;gBAEF,6CAA6C;gBAC7C,MAAM,UAAU,GAAG,MAAM,KAAK,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC;gBAE1E,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,UAAU;iBACX,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACjE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBACpG,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,oBAAoB,EAAE;QACpB,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,mBAAmB;QACzB,WAAW,EAAE,4BAA4B;QACzC,SAAS,EAAE,EAAE;QACb,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;gBACvC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAEvC,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,gDAAgD;qBACxD,CAAC;gBACJ,CAAC;gBAED,sBAAsB;gBACtB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;gBAEtE,kCAAkC;gBAClC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,eAAe,CAAC;gBAC9D,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;gBAEpC,OAAO;oBACL,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACjE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBAC5F,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IAED;;SAEK;IACL,SAAS,EAAE;QACT,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,sCAAsC;QACnD,SAAS,EAAE,EAAE;QACb,KAAK,CAAC,OAAO,CAAC,OAAwB;YACpC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC;gBAE/C,8BAA8B;gBAC9B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;gBAE3D,OAAO;oBACL,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBACrD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBACxF,CAAC;YACJ,CAAC;QACH,CAAC;KACF;CACF,CAAC;AAEF,kCAAkC;AACrB,QAAA,oBAAoB,GAAuB;IACtD,EAAE,EAAE,cAAc;IAClB,IAAI,EAAE,eAAe;IACrB,WAAW,EAAE,0EAA0E;IACvF,OAAO,EAAE,OAAO;IAChB,MAAM,EAAE;QACN;YACE,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,6BAA6B;YAC1C,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,IAAI;SACf;KACF;IACD,OAAO,EAAE,EAAE;IACX,WAAW,EAAE,SAAS;IACtB,KAAK,EAAE;QACL;YACE,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE,gDAAgD;YAC7D,SAAS,EAAE,EAAE;YACb,KAAK,CAAC,OAAO,CAAC,OAAwB;gBACpC,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,oCAAoC;qBAC5C,CAAC;gBACJ,CAAC;gBAED,iCAAiC;gBACjC,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAE7F,IAAI,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;oBAC/C,oDAAoD;oBACpD,MAAM,YAAY,GAAG,UAAU,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC;oBACvG,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,YAAY,IAAI,qBAAqB;qBAC7C,CAAC;gBACJ,CAAC;gBAED,MAAM,cAAc,GAAG,6BAAa,CAAC,YAAY,CAAC,uBAAuB,EAAE;oBACzE,QAAQ;oBACR,WAAW,EAAE,UAAU,CAAC,MAAM;iBAC/B,CAAC,CAAC;gBAEH,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;oBAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACpC,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,MAAM;qBACf,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;oBACjE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,iCAAiC;qBACzC,CAAC;gBACJ,CAAC;YACH,CAAC;SACF;KACF;CACF,CAAC;AAEF,gCAAgC;AAChC,wBAAgB,CAAC,gBAAgB,CAAC,4BAAoB,CAAC,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { performance } from 'perf_hooks';\nimport { Agent } from '../agentUtilities/agent';\nimport { Logger } from '../../logger';\nimport { ContextSource } from '../agentModes/operationMode';\nimport { contextManager } from '../agentModes/contextManager';\nimport { goddessPersonalityEngine } from '../../personality/goddessMode';\nimport { QuantumAnalysisTool } from '../../tools/quantumAnalysisTool';\nimport { NeuralCodeSynthesisTool } from '../../tools/neuralCodeSynthesisTool';\nimport { TimeTravelDebuggingTool } from '../../tools/timeTravelDebuggingTool';\nimport { performanceMonitor } from '../../utils/performance';\nimport { globalCache as cache, cacheResult } from '../../utils/cache';\nimport { promptManager } from '../../prompts/promptManager';\n\n/**\n * Workflow step definition\n */\nexport interface WorkflowStep {\n  id: string;\n  name: string;\n  description: string;\n  execute: (_context: WorkflowContext) => Promise<WorkflowStepResult>;\n  nextSteps: string[];\n  isConditional?: boolean;\n}\n\n/**\n * Revolutionary Workflow Step Result with Enhanced Insights\n */\nexport interface WorkflowStepResult {\n  success: boolean;\n  nextStepId?: string;\n  output?: any;\n  error?: string;\n\n  // Revolutionary enhancements\n  quantumAnalysis?: {\n    patterns: any[];\n    confidence: number;\n    parallelOutcomes: any[];\n  };\n\n  neuralInsights?: {\n    synapticActivations: any[];\n    creativityScore: number;\n    consciousnessLevel: number;\n  };\n\n  goddessGuidance?: {\n    emotionalSupport: string;\n    wisdomSharing: string;\n    motivationalBoost: string;\n  };\n\n  timeTravelPredictions?: {\n    futureIssues: any[];\n    mitigationStrategies: string[];\n    alternativeTimelines: any[];\n  };\n\n  // Performance metrics\n  executionMetrics?: {\n    duration: number;\n    memoryUsage: number;\n    cpuUsage: number;\n    revolutionaryFeaturesUsed: string[];\n  };\n}\n\n/**\n * Revolutionary Workflow Context with Enhanced Capabilities\n */\nexport interface WorkflowContext {\n  workflow: Workflow;\n  agent: Agent;\n  inputs: Record<string, any>;\n  outputs: Record<string, any>;\n  variables: Record<string, any>;\n  history: {\n    stepId: string;\n    startTime: Date;\n    endTime?: Date;\n    result?: WorkflowStepResult;\n    quantumAnalysis?: any;\n    neuralInsights?: any;\n    goddessGuidance?: string;\n  }[];\n\n  // Revolutionary enhancements\n  revolutionaryFeatures?: {\n    goddessMode: boolean;\n    quantumAnalysis: boolean;\n    neuralSynthesis: boolean;\n    timeTravelDebugging: boolean;\n    adaptivePersonality: boolean;\n  };\n\n  // Emotional intelligence context\n  emotionalContext?: {\n    developerMood: string;\n    motivationalLevel: number;\n    stressLevel: number;\n    creativityBoost: number;\n  };\n\n  // Quantum workflow state\n  quantumState?: {\n    superposition: boolean;\n    entangled: boolean;\n    parallelExecutions: number;\n    quantumInterference: boolean;\n  };\n\n  // Neural workflow memory\n  neuralMemory?: {\n    synapticConnections: Map<string, number>;\n    learningHistory: Array<{\n      stepId: string;\n      pattern: string;\n      strength: number;\n    }>;\n    consciousnessLevel: number;\n  };\n}\n\n/**\n * Workflow definition\n */\nexport interface WorkflowDefinition {\n  id: string;\n  name: string;\n  description: string;\n  version: string;\n  steps: WorkflowStep[];\n  inputs: {\n    id: string;\n    name: string;\n    description: string;\n    type: 'string' | 'number' | 'boolean' | 'object' | 'array';\n    required: boolean;\n    default?: any;\n  }[];\n  outputs: {\n    id: string;\n    name: string;\n    description: string;\n    type: 'string' | 'number' | 'boolean' | 'object' | 'array';\n  }[];\n  startStepId: string;\n}\n\n/**\n * Revolutionary Workflow Class with Enhanced AI Capabilities\n */\nexport class Workflow {\n  private definition: WorkflowDefinition;\n  private context: WorkflowContext;\n  private isRunning = false;\n  private isCancelled = false;\n  private onProgressCallback?: (_stepId: string, _progress: number) => void;\n  private onCompletedCallback?: (_success: boolean, _outputs: Record<string, any>) => void;\n\n  // Revolutionary tools\n  private quantumTool?: QuantumAnalysisTool;\n  private neuralTool?: NeuralCodeSynthesisTool;\n  private timeTravelTool?: TimeTravelDebuggingTool;\n  private revolutionaryFeaturesEnabled = {\n    goddessMode: false,\n    quantumAnalysis: false,\n    neuralSynthesis: false,\n    timeTravelDebugging: false,\n    adaptivePersonality: false\n  };\n\n  // Cache for workflow steps and results\n  private stepCache = new Map<string, any>();\n  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes\n\n  // Performance metrics\n  private executionMetrics = {\n    totalSteps: 0,\n    cachedSteps: 0,\n    totalDuration: 0,\n    steps: new Map<string, { count: number; totalDuration: number; avgDuration: number }>()\n  };\n\n  /**\n     * Revolutionary Constructor with Enhanced Initialization\n     */\n  constructor(definition: WorkflowDefinition) {\n    this.definition = definition;\n    this.context = {\n      workflow: this,\n      agent: {} as Agent, // Will be set before execution\n      inputs: {},\n      outputs: {},\n      variables: {},\n      history: [],\n\n      // Initialize revolutionary features\n      revolutionaryFeatures: {\n        goddessMode: false,\n        quantumAnalysis: false,\n        neuralSynthesis: false,\n        timeTravelDebugging: false,\n        adaptivePersonality: false\n      },\n\n      emotionalContext: {\n        developerMood: 'neutral',\n        motivationalLevel: 50,\n        stressLevel: 30,\n        creativityBoost: 0\n      },\n\n      quantumState: {\n        superposition: false,\n        entangled: false,\n        parallelExecutions: 1,\n        quantumInterference: false\n      },\n\n      neuralMemory: {\n        synapticConnections: new Map(),\n        learningHistory: [],\n        consciousnessLevel: 0\n      }\n    };\n\n    // Initialize revolutionary tools\n    this.initializeRevolutionaryTools();\n  }\n\n  /**\n     * Initialize revolutionary AI tools\n     */\n  private initializeRevolutionaryTools(): void {\n    try {\n      this.quantumTool = new QuantumAnalysisTool();\n      this.neuralTool = new NeuralCodeSynthesisTool();\n      this.timeTravelTool = new TimeTravelDebuggingTool();\n\n      Logger.instance.info('Revolutionary workflow tools initialized successfully');\n    } catch (error) {\n      Logger.instance.warn('Some revolutionary tools could not be initialized:', error);\n    }\n  }\n\n  /**\n     * Get the workflow definition\n     */\n  public getDefinition(): WorkflowDefinition {\n    return this.definition;\n  }\n\n  /**\n     * Set the agent for this workflow\n     */\n  public setAgent(agent: Agent): void {\n    this.context.agent = agent;\n  }\n\n  /**\n     * Set inputs for this workflow\n     */\n  public setInputs(inputs: Record<string, any>): void {\n    // Validate inputs\n    for (const inputDef of this.definition.inputs) {\n      if (inputDef.required && !(inputDef.id in inputs) && !('default' in inputDef)) {\n        throw new Error(`Required input '${inputDef.id}' is missing`);\n      }\n    }\n\n    // Set inputs with defaults\n    this.context.inputs = {};\n    for (const inputDef of this.definition.inputs) {\n      if (inputDef.id in inputs) {\n        this.context.inputs[inputDef.id] = inputs[inputDef.id];\n      } else if ('default' in inputDef) {\n        this.context.inputs[inputDef.id] = inputDef.default;\n      }\n    }\n  }\n\n  /**\n     * Set a callback for progress updates\n     */\n  public onProgress(callback: (_stepId: string, _progress: number) => void): void {\n    this.onProgressCallback = callback;\n  }\n\n  /**\n     * Set a callback for workflow completion\n     */\n  public onCompleted(callback: (_success: boolean, _outputs: Record<string, any>) => void): void {\n    this.onCompletedCallback = callback;\n  }\n\n  /**\n     * Enable advanced features for this workflow\n     */\n  public enableAdvancedFeatures(features: {\n    goddessMode?: boolean;\n    quantumAnalysis?: boolean;\n    neuralSynthesis?: boolean;\n    timeTravelDebugging?: boolean;\n    adaptivePersonality?: boolean;\n  }): void {\n    this.revolutionaryFeaturesEnabled = {\n      ...this.revolutionaryFeaturesEnabled,\n      ...features\n    };\n\n    if (this.context.revolutionaryFeatures) {\n      this.context.revolutionaryFeatures = {\n        ...this.context.revolutionaryFeatures,\n        ...features\n      };\n    }\n\n    Logger.instance.info(`Advanced features enabled: ${Object.entries(features).filter(([_, enabled]) => enabled).map(([feature]) => feature).join(', ')}`);\n  }\n\n  /**\n     * Apply Goddess Mode enhancements to workflow execution\n     */\n  private async applyGoddessMode(stepId: string, input: any): Promise<any> {\n    if (!this.revolutionaryFeaturesEnabled.goddessMode) {\n      return input;\n    }\n\n    try {\n      // Analyze developer mood and context\n      const mood = goddessPersonalityEngine.analyzeDeveloperMood(\n        JSON.stringify(input),\n        {\n          timeOfDay: new Date().getHours(),\n          recentErrors: this.context.history.filter(h => !h.result?.success).length,\n          sessionDuration: Date.now() - (this.context.history[0]?.startTime.getTime() || Date.now()),\n          codeComplexity: 50\n        }\n      );\n\n      // Generate goddess response\n      const goddessResponse = goddessPersonalityEngine.generateGoddessResponse(\n        `Executing workflow step: ${stepId}`,\n        mood,\n        { codeComplexity: 50 }\n      );\n\n      // Update emotional context\n      if (this.context.emotionalContext) {\n        this.context.emotionalContext.developerMood = String(mood);\n        this.context.emotionalContext.motivationalLevel = Math.min(100, this.context.emotionalContext.motivationalLevel + 10);\n        this.context.emotionalContext.creativityBoost = goddessResponse.tone === 'playful' ? 20 : 10;\n      }\n\n      // Add goddess guidance to the step\n      return {\n        ...input,\n        goddessGuidance: {\n          emotionalSupport: goddessResponse.emotionalContext || '',\n          wisdomSharing: goddessResponse.wisdomSharing || '',\n          motivationalBoost: goddessResponse.motivationalElement || ''\n        }\n      };\n    } catch (error) {\n      Logger.instance.warn('Goddess Mode enhancement failed:', error);\n      return input;\n    }\n  }\n\n  /**\n     * Apply Quantum Analysis to workflow step\n     */\n  private async applyQuantumAnalysis(stepId: string, input: any): Promise<any> {\n    if (!this.revolutionaryFeaturesEnabled.quantumAnalysis || !this.quantumTool) {\n      return input;\n    }\n\n    try {\n      const result = await this.quantumTool.execute('quantum_pattern_recognition', {\n        action: 'quantum_pattern_recognition',\n        code: JSON.stringify(input)\n      });\n\n      if (result.success && result.output) {\n        // Update quantum state\n        if (this.context.quantumState) {\n          this.context.quantumState.superposition = true;\n          this.context.quantumState.parallelExecutions = result.output.patterns?.length || 1;\n        }\n\n        return {\n          ...input,\n          quantumAnalysis: result.output\n        };\n      }\n    } catch (error) {\n      Logger.instance.warn('Quantum Analysis enhancement failed:', error);\n    }\n\n    return input;\n  }\n\n  /**\n     * Apply Neural Synthesis to workflow step\n     */\n  private async applyNeuralSynthesis(stepId: string, input: any): Promise<any> {\n    if (!this.revolutionaryFeaturesEnabled.neuralSynthesis || !this.neuralTool) {\n      return input;\n    }\n\n    try {\n      const result = await this.neuralTool.execute('consciousness_analysis', {\n        action: 'consciousness_analysis',\n        code: JSON.stringify(input),\n        prompt: `Analyzing workflow step: ${stepId}`\n      });\n\n      if (result.success && result.output) {\n        // Update neural memory\n        if (this.context.neuralMemory) {\n          this.context.neuralMemory.consciousnessLevel = result.output.consciousnessLevel?.awareness || 0;\n          this.context.neuralMemory.learningHistory.push({\n            stepId,\n            pattern: 'workflow_execution',\n            strength: result.output.consciousnessLevel?.understanding || 0\n          });\n        }\n\n        return {\n          ...input,\n          neuralInsights: result.output\n        };\n      }\n    } catch (error) {\n      Logger.instance.warn('Neural Synthesis enhancement failed:', error);\n    }\n\n    return input;\n  }\n\n  /**\n     * Apply Time-Travel Debugging to predict future issues\n     */\n  private async applyTimeTravelDebugging(stepId: string, input: any): Promise<any> {\n    if (!this.revolutionaryFeaturesEnabled.timeTravelDebugging || !this.timeTravelTool) {\n      return input;\n    }\n\n    try {\n      const result = await this.timeTravelTool.execute('predictFutureIssues', {\n        filePath: 'workflow_execution',\n        timeHorizon: '1-month'\n      });\n\n      if (result.success && result.output) {\n        return {\n          ...input,\n          timeTravelPredictions: result.output\n        };\n      }\n    } catch (error) {\n      Logger.instance.warn('Time-Travel Debugging enhancement failed:', error);\n    }\n\n    return input;\n  }\n\n  /**\n          const quantumEnhancement = await this.applyQuantumAnalysis(step.id, enhancedContext.inputs);\n          enhancedContext = { ...enhancedContext, inputs: { ...enhancedContext.inputs, ...quantumEnhancement } };\n        }\n\n        // Apply Neural Synthesis\n        if (this.revolutionaryFeaturesEnabled.neuralSynthesis) {\n          const neuralEnhancement = await this.applyNeuralSynthesis(step.id, enhancedContext.inputs);\n          enhancedContext = { ...enhancedContext, inputs: { ...enhancedContext.inputs, ...neuralEnhancement } };\n        }\n\n        // Apply Time-Travel Debugging\n        if (this.revolutionaryFeaturesEnabled.timeTravelDebugging) {\n          const timeTravelEnhancement = await this.applyTimeTravelDebugging(step.id, enhancedContext.inputs);\n          enhancedContext = { ...enhancedContext, inputs: { ...enhancedContext.inputs, ...timeTravelEnhancement } };\n        }\n\n        // Execute step with enhanced context\n        let result: WorkflowStepResult;\n        try {\n          result = await step.execute(enhancedContext);\n\n          // Add revolutionary insights to result\n          if (enhancedContext.inputs.quantumAnalysis) {\n            result.quantumAnalysis = enhancedContext.inputs.quantumAnalysis;\n          }\n          if (enhancedContext.inputs.neuralInsights) {\n            result.neuralInsights = enhancedContext.inputs.neuralInsights;\n          }\n          if (enhancedContext.inputs.goddessGuidance) {\n            result.goddessGuidance = enhancedContext.inputs.goddessGuidance;\n          }\n          if (enhancedContext.inputs.timeTravelPredictions) {\n            result.timeTravelPredictions = enhancedContext.inputs.timeTravelPredictions;\n          }\n\n          // Add execution metrics\n          const endTime = Date.now();\n          const startTime = this.context.history[this.context.history.length - 1]?.startTime.getTime() || endTime;\n          result.executionMetrics = {\n            duration: endTime - startTime,\n            memoryUsage: process.memoryUsage().heapUsed,\n            cpuUsage: process.cpuUsage().user,\n            revolutionaryFeaturesUsed: Object.entries(this.revolutionaryFeaturesEnabled)\n              .filter(([_, enabled]) => enabled)\n              .map(([feature]) => feature)\n          };\n\n        } catch (error) {\n          Logger.instance.error(`Error executing workflow step '${step.name}':`, error);\n          result = {\n            success: false,\n            error: `Error: ${error instanceof Error ? error.message : String(error)}`\n          };\n        }\n\n        // Record end time and result\n        const historyEntry = this.context.history[this.context.history.length - 1];\n        historyEntry.endTime = new Date();\n        historyEntry.result = result;\n\n        // Update completed steps\n        completedSteps++;\n\n        // Check result\n        if (!result.success) {\n          Logger.instance.error(`Workflow step '${step.name}' failed: ${result.error}`);\n\n          // Call completion callback\n          if (this.onCompletedCallback) {\n            this.onCompletedCallback(false, this.context.outputs);\n          }\n\n          this.isRunning = false;\n          return this.context.outputs;\n        }\n\n        // Determine next step\n        if (result.nextStepId) {\n          // Explicit next step from result\n          currentStepId = result.nextStepId;\n        } else if (step.nextSteps.length === 1) {\n          // Single next step\n          currentStepId = step.nextSteps[0];\n        } else if (step.nextSteps.length > 1 && step.isConditional) {\n          // Multiple next steps for conditional step, but no explicit next step provided\n          throw new Error(`Conditional step '${step.name}' did not specify a next step`);\n        } else if (step.nextSteps.length === 0) {\n          // End of workflow\n          currentStepId = '';\n        } else {\n          // Default to first next step\n          currentStepId = step.nextSteps[0];\n        }\n      }\n\n      Logger.instance.info(`Workflow completed: ${this.definition.name}`);\n\n      // Call completion callback\n      if (this.onCompletedCallback) {\n        this.onCompletedCallback(true, this.context.outputs);\n      }\n\n      return this.context.outputs;\n    } catch (error) {\n      Logger.instance.error('Error executing workflow:', error);\n\n      // Call completion callback\n      if (this.onCompletedCallback) {\n        this.onCompletedCallback(false, this.context.outputs);\n      }\n\n      throw error;\n    } finally {\n      this.isRunning = false;\n    }\n  }\n\n  /**\n     * Cancel the workflow\n     */\n  public cancel(): void {\n    if (this.isRunning) {\n      this.isCancelled = true;\n      Logger.instance.info(`Cancelling workflow: ${this.definition.name}`);\n    }\n  }\n\n  /**\n     * Check if the workflow is running\n     */\n  public isWorkflowRunning(): boolean {\n    return this.isRunning;\n  }\n\n  /**\n     * Get the workflow context\n     */\n  public getContext(): WorkflowContext {\n    return this.context;\n  }\n}\n\n/**\n * Workflow registry\n */\nexport class WorkflowRegistry {\n  private static instance: WorkflowRegistry;\n  private workflows = new Map<string, WorkflowDefinition>();\n\n  private constructor() { }\n\n  /**\n     * Get the singleton instance\n     */\n  public static getInstance(): WorkflowRegistry {\n    if (!WorkflowRegistry.instance) {\n      WorkflowRegistry.instance = new WorkflowRegistry();\n    }\n    return WorkflowRegistry.instance;\n  }\n\n  /**\n     * Register a workflow\n     */\n  public registerWorkflow(workflow: WorkflowDefinition): void {\n    this.workflows.set(workflow.id, workflow);\n  }\n\n  /**\n     * Get a workflow by ID\n     */\n  public getWorkflow(id: string): WorkflowDefinition | undefined {\n    return this.workflows.get(id);\n  }\n\n  /**\n     * Get all registered workflows\n     */\n  public getAllWorkflows(): WorkflowDefinition[] {\n    return Array.from(this.workflows.values());\n  }\n\n  /**\n     * Create a workflow instance\n     */\n  public createWorkflowInstance(id: string): Workflow {\n    const definition = this.getWorkflow(id);\n\n    if (!definition) {\n      throw new Error(`Workflow with ID '${id}' not found`);\n    }\n\n    return new Workflow(definition);\n  }\n}\n\n// Export singleton instance\nexport const workflowRegistry = WorkflowRegistry.getInstance();\n\n/**\n * Common workflow steps\n */\nexport const commonWorkflowSteps: Record<string, WorkflowStep> = {\n  /**\n     * Input step - Collects input from the user\n     */\n  inputStep: {\n    id: 'input',\n    name: 'User Input',\n    description: 'Collects input from the user',\n    nextSteps: [],\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const prompt = context.inputs.prompt || 'Please provide input:';\n        const inputValue = await vscode.window.showInputBox({\n          prompt,\n          ignoreFocusOut: true\n        });\n\n        if (inputValue === undefined) {\n          // User cancelled\n          return {\n            success: false,\n            error: 'User cancelled input'\n          };\n        }\n\n        // Store the input in the context\n        const outputKey = context.inputs.outputKey || 'userInput';\n        context.outputs[outputKey] = inputValue;\n\n        return {\n          success: true\n        };\n      } catch (error) {\n        Logger.instance.error('Error in input step:', error);\n        return {\n          success: false,\n          error: `Error collecting input: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * File selection step - Allows the user to select files\n     */\n  fileSelectionStep: {\n    id: 'fileSelection',\n    name: 'File Selection',\n    description: 'Allows the user to select files',\n    nextSteps: [],\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const canSelectMany = context.inputs.canSelectMany !== false;\n        const fileUris = await vscode.window.showOpenDialog({\n          canSelectFiles: true,\n          canSelectFolders: false,\n          canSelectMany,\n          openLabel: context.inputs.openLabel || 'Select Files'\n        });\n\n        if (!fileUris || fileUris.length === 0) {\n          // User cancelled or didn't select any files\n          return {\n            success: false,\n            error: 'No files selected'\n          };\n        }\n\n        // Store the selected files in the context\n        const outputKey = context.inputs.outputKey || 'selectedFiles';\n        context.outputs[outputKey] = fileUris.map(uri => uri.fsPath);\n\n        return {\n          success: true\n        };\n      } catch (error) {\n        Logger.instance.error('Error in file selection step:', error);\n        return {\n          success: false,\n          error: `Error selecting files: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * Folder selection step - Allows the user to select folders\n     */\n  folderSelectionStep: {\n    id: 'folderSelection',\n    name: 'Folder Selection',\n    description: 'Allows the user to select folders',\n    nextSteps: [],\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const canSelectMany = context.inputs.canSelectMany !== false;\n        const folderUris = await vscode.window.showOpenDialog({\n          canSelectFiles: false,\n          canSelectFolders: true,\n          canSelectMany,\n          openLabel: context.inputs.openLabel || 'Select Folders'\n        });\n\n        if (!folderUris || folderUris.length === 0) {\n          // User cancelled or didn't select any folders\n          return {\n            success: false,\n            error: 'No folders selected'\n          };\n        }\n\n        // Store the selected folders in the context\n        const outputKey = context.inputs.outputKey || 'selectedFolders';\n        context.outputs[outputKey] = folderUris.map(uri => uri.fsPath);\n\n        return {\n          success: true\n        };\n      } catch (error) {\n        Logger.instance.error('Error in folder selection step:', error);\n        return {\n          success: false,\n          error: `Error selecting folders: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * AI generation step - Generates content using the AI\n     */\n  aiGenerationStep: {\n    id: 'aiGeneration',\n    name: 'AI Generation',\n    description: 'Generates content using the AI',\n    nextSteps: [],\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const prompt = context.inputs.prompt;\n\n        if (!prompt) {\n          return {\n            success: false,\n            error: 'No prompt provided for AI generation'\n          };\n        }\n\n        // Get context if specified\n        let contextContent = '';\n        if (context.inputs.contextSource) {\n          const contextSource: ContextSource = context.inputs.contextSource;\n          contextContent = await contextManager.getContextContent(contextSource);\n        }\n\n        // Prepare the full prompt\n        const fullPrompt = contextContent ? `${contextContent}\\n\\n${prompt}` : prompt;\n\n        // Generate content using the agent\n        const response = await context.agent.generate(fullPrompt);\n\n        // Store the generated content in the context\n        const outputKey = context.inputs.outputKey || 'generatedContent';\n        context.outputs[outputKey] = response;\n\n        return {\n          success: true\n        };\n      } catch (error) {\n        Logger.instance.error('Error in AI generation step:', error);\n        return {\n          success: false,\n          error: `Error generating content: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * File writing step - Writes content to a file\n     */\n  fileWritingStep: {\n    id: 'fileWriting',\n    name: 'File Writing',\n    description: 'Writes content to a file',\n    nextSteps: [],\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const filePath = context.inputs.filePath;\n        const content = context.inputs.content;\n\n        if (!filePath) {\n          return {\n            success: false,\n            error: 'No file path provided for file writing'\n          };\n        }\n\n        if (content === undefined) {\n          return {\n            success: false,\n            error: 'No content provided for file writing'\n          };\n        }\n\n        // Ensure the directory exists\n        const directory = path.dirname(filePath);\n        await fs.promises.mkdir(directory, { recursive: true });\n\n        // Write the file\n        await fs.promises.writeFile(filePath, content, 'utf-8');\n\n        // Store the file path in the context\n        const outputKey = context.inputs.outputKey || 'writtenFilePath';\n        context.outputs[outputKey] = filePath;\n\n        return {\n          success: true\n        };\n      } catch (error) {\n        Logger.instance.error('Error in file writing step:', error);\n        return {\n          success: false,\n          error: `Error writing file: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * File reading step - Reads content from a file\n     */\n  fileReadingStep: {\n    id: 'fileReading',\n    name: 'File Reading',\n    description: 'Reads content from a file',\n    nextSteps: [],\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const filePath = context.inputs.filePath;\n\n        if (!filePath) {\n          return {\n            success: false,\n            error: 'No file path provided for file reading'\n          };\n        }\n\n        // Read the file\n        const content = await fs.promises.readFile(filePath, 'utf-8');\n\n        // Store the content in the context\n        const outputKey = context.inputs.outputKey || 'fileContent';\n        context.outputs[outputKey] = content;\n\n        return {\n          success: true\n        };\n      } catch (error) {\n        Logger.instance.error('Error in file reading step:', error);\n        return {\n          success: false,\n          error: `Error reading file: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * Conditional step - Branches based on a condition\n     */\n  conditionalStep: {\n    id: 'conditional',\n    name: 'Conditional',\n    description: 'Branches based on a condition',\n    nextSteps: [],\n    isConditional: true,\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const condition = context.inputs.condition;\n        const trueStepId = context.inputs.trueStepId;\n        const falseStepId = context.inputs.falseStepId;\n\n        if (!trueStepId || !falseStepId) {\n          return {\n            success: false,\n            error: 'True or false step ID not provided for conditional step'\n          };\n        }\n\n        // Evaluate the condition\n        let conditionResult = false;\n\n        if (typeof condition === 'function') {\n          // Function condition\n          conditionResult = await condition(context);\n        } else if (typeof condition === 'string') {\n          // Expression condition (simple evaluation)\n          // In a real implementation, you would use a proper expression evaluator\n          try {\n\n            conditionResult = eval(condition);\n          } catch (evalError) {\n            Logger.instance.error('Error evaluating condition:', evalError);\n            return {\n              success: false,\n              error: `Error evaluating condition: ${evalError instanceof Error ? evalError.message : String(evalError)}`\n            };\n          }\n        } else {\n          // Boolean condition\n          conditionResult = !!condition;\n        }\n\n        // Determine next step based on condition\n        const nextStepId = conditionResult ? trueStepId : falseStepId;\n\n        return {\n          success: true,\n          nextStepId\n        };\n      } catch (error) {\n        Logger.instance.error('Error in conditional step:', error);\n        return {\n          success: false,\n          error: `Error in conditional step: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * User confirmation step - Asks the user for confirmation\n     */\n  userConfirmationStep: {\n    id: 'userConfirmation',\n    name: 'User Confirmation',\n    description: 'Asks the user for confirmation',\n    nextSteps: [],\n    isConditional: true,\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const message = context.inputs.message || 'Do you want to continue?';\n        const confirmLabel = context.inputs.confirmLabel || 'Yes';\n        const cancelLabel = context.inputs.cancelLabel || 'No';\n        const confirmStepId = context.inputs.confirmStepId;\n        const cancelStepId = context.inputs.cancelStepId;\n\n        if (!confirmStepId || !cancelStepId) {\n          return {\n            success: false,\n            error: 'Confirm or cancel step ID not provided for user confirmation step'\n          };\n        }\n\n        // Ask for confirmation\n        const result = await vscode.window.showInformationMessage(\n          message,\n          { modal: true },\n          confirmLabel,\n          cancelLabel\n        );\n\n        // Determine next step based on user response\n        const nextStepId = result === confirmLabel ? confirmStepId : cancelStepId;\n\n        return {\n          success: true,\n          nextStepId\n        };\n      } catch (error) {\n        Logger.instance.error('Error in user confirmation step:', error);\n        return {\n          success: false,\n          error: `Error in user confirmation step: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * Command execution step - Executes a VS Code command\n     */\n  commandExecutionStep: {\n    id: 'commandExecution',\n    name: 'Command Execution',\n    description: 'Executes a VS Code command',\n    nextSteps: [],\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const command = context.inputs.command;\n        const args = context.inputs.args || [];\n\n        if (!command) {\n          return {\n            success: false,\n            error: 'No command provided for command execution step'\n          };\n        }\n\n        // Execute the command\n        const result = await vscode.commands.executeCommand(command, ...args);\n\n        // Store the result in the context\n        const outputKey = context.inputs.outputKey || 'commandResult';\n        context.outputs[outputKey] = result;\n\n        return {\n          success: true\n        };\n      } catch (error) {\n        Logger.instance.error('Error in command execution step:', error);\n        return {\n          success: false,\n          error: `Error executing command: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  },\n\n  /**\n     * Delay step - Waits for a specified amount of time\n     */\n  delayStep: {\n    id: 'delay',\n    name: 'Delay',\n    description: 'Waits for a specified amount of time',\n    nextSteps: [],\n    async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n      try {\n        const delayMs = context.inputs.delayMs || 1000;\n\n        // Wait for the specified time\n        await new Promise(resolve => setTimeout(resolve, delayMs));\n\n        return {\n          success: true\n        };\n      } catch (error) {\n        Logger.instance.error('Error in delay step:', error);\n        return {\n          success: false,\n          error: `Error in delay step: ${error instanceof Error ? error.message : String(error)}`\n        };\n      }\n    }\n  }\n};\n\n// Example workflows for reference\nexport const codeAnalysisWorkflow: WorkflowDefinition = {\n  id: 'codeAnalysis',\n  name: 'Code Analysis',\n  description: 'Analyzes code quality, finds potential issues, and suggests improvements',\n  version: '1.0.0',\n  inputs: [\n    {\n      id: 'filePath',\n      name: 'File Path',\n      description: 'Path to the file to analyze',\n      type: 'string',\n      required: true\n    }\n  ],\n  outputs: [],\n  startStepId: 'analyze',\n  steps: [\n    {\n      id: 'analyze',\n      name: 'Analyze File',\n      description: 'Analyzes the specified file for quality issues',\n      nextSteps: [],\n      async execute(context: WorkflowContext): Promise<WorkflowStepResult> {\n        const filePath = context.inputs.filePath;\n        if (!filePath) {\n          return {\n            success: false,\n            error: 'No file path provided for analysis'\n          };\n        }\n\n        // Use file tool to read the file\n        const fileResult = await context.agent.tools?.get('file')?.execute('readFile', { filePath });\n\n        if (!fileResult?.success || !fileResult.output) {\n          // Handle potential Error object in fileResult.error\n          const errorMessage = fileResult?.error instanceof Error ? fileResult.error.message : fileResult?.error;\n          return {\n            success: false,\n            error: errorMessage || 'Failed to read file'\n          };\n        }\n\n        const analysisPrompt = promptManager.renderPrompt('workflow.codeAnalysis', {\n          filePath,\n          fileContent: fileResult.output\n        });\n\n        try {\n          const response = await context.agent.generate(analysisPrompt);\n          const result = JSON.parse(response);\n          return {\n            success: true,\n            output: result\n          };\n        } catch (error) {\n          Logger.instance.error('Failed to parse analysis result:', error);\n          return {\n            success: false,\n            error: 'Failed to parse analysis result'\n          };\n        }\n      }\n    }\n  ]\n};\n\n// Register the example workflow\nworkflowRegistry.registerWorkflow(codeAnalysisWorkflow);\n"]}