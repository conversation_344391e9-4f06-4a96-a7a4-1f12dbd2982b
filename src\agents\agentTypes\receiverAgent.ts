import { Agent, AgentConfig } from '../agentUtilities/agent';
import { AgentRunInput, AgentContext, AgentRunR<PERSON>ult } from '../../types/agent';
import { logger } from '../../logger';
import { promptManager } from '../../prompts/promptManager';

class receiverAgent extends Agent {
  constructor(_config: Partial<AgentConfig>) {
    super({
      ..._config,
      role: 'assistant',
      capabilities: ['receive'],
      llmProvider: 'openai',
      llmModel: 'gpt-4'
    });
  }

  async processInput(input: string, context: Record<string, unknown> = {}): Promise<string> {
    logger.info(`receiverAgent processing input: "${input.substring(0, 50)}..."`);

    // Emit streaming event if context available
    if (context.streamingContext?.onStream) {
      context.streamingContext.onStream({
        type: 'start',
        agentId: 'receiver',
        data: { input: input.substring(0, 100) },
        timestamp: Date.now()
      });
    }

    // --- Gather relevant workflow for receiver mode ---
    let workflow = undefined;
    if (context.variables?.workflowManager) {
      workflow = await context.variables.workflowManager.getWorkflowForMode?.('receiver');
    }

    // --- Gather memory and knowledgebase context ---
    let memoryContext = '';
    if (this.getMemory) {
      const memories = await this.getMemory().getRelevantMemories(input);
      if (memories?.length) {
        memoryContext = this.getMemory().formatMemoriesForPrompt(memories);
      }
    }

    // Emit progress event
    if (context.streamingContext?.onStream) {
      context.streamingContext.onStream({
        type: 'progress',
        agentId: 'receiver',
        data: { step: 'context_gathering' },
        timestamp: Date.now()
      });
    }
    let kbContext = '';
    if (context.variables?.knowledgebaseManager) {
      kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input);
    }

    // --- Gather MCP context ---
    let mcpContext = '';
    if (context.variables?.mcpManager) {
      mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext?.() || {});
    }

    // Extract multimodal data and attachments from context
    const multimodalData = this._extractMultimodalData(context);
    const attachedFiles = this._extractAttachedFiles(context);

    // Use promptManager for consistent prompt generation
    const enhancementPrompt = promptManager.renderPrompt('agent.receiver', {
      userInput: input,
      multimodalData: JSON.stringify(multimodalData),
      attachedFiles: JSON.stringify(attachedFiles),
      memoryContext,
      kbContext,
      mcpContext,
      workflow: workflow ? JSON.stringify(workflow) : 'None'
    });
    
    // Emit processing event
    if (context.streamingContext?.onStream) {
      context.streamingContext.onStream({
        type: 'progress',
        agentId: 'receiver',
        data: { step: 'generating_enhancement' },
        timestamp: Date.now()
      });
    }

    const enhancedInput = await this.generate(enhancementPrompt);
    
    // Emit completion event
    if (context.streamingContext?.onStream) {
      context.streamingContext.onStream({
        type: 'complete',
        agentId: 'receiver',
        data: { result: enhancedInput.substring(0, 100) + '...' },
        timestamp: Date.now()
      });
    }
    
    logger.debug(`receiverAgent enhanced input: "${enhancedInput.substring(0, 100)}..."`);
    return enhancedInput;
  }
    
  private _formatContext(context: any): string {
    let formattedContext = '';
        
    // Format workspace context
    if (context.workspace) {
      formattedContext += '### Workspace Context\n';
      if (context.workspace.currentFile) {
        formattedContext += `Current File: ${context.workspace.currentFile.fileName}\n`;
        if (context.workspace.selection) {
          const selection = context.workspace.selection;
          formattedContext += `Selected Text: \n\`\`\`\n${context.workspace.currentFile.getText(selection)}\n\`\`\`\n`;
        }
      }
    }
        
    // Format attached files
    if (context.attachedFiles && context.attachedFiles.length > 0) {
      formattedContext += '### Attached Files\n';
      context.attachedFiles.forEach((file: any) => {
        formattedContext += `File: ${file.name}\n`;
        formattedContext += `Content: \n\`\`\`\n${file.content}\n\`\`\`\n`;
      });
    }
        
    // Format voice transcription if available
    if (context.voiceTranscription) {
      formattedContext += '### Voice Transcription\n';
      formattedContext += `${context.voiceTranscription}\n`;
    }
        
    return formattedContext || 'No additional context available';
  }

  /**
   * Extract multimodal data from context (images, audio, etc.)
   */
  private _extractMultimodalData(context: Record<string, unknown>): Record<string, unknown> {
    const multimodal: Record<string, unknown> = {};

    // Extract images
    if (context.images && Array.isArray(context.images)) {
      multimodal.images = context.images.map((img: unknown) => {
        if (typeof img === 'object' && img !== null) {
          const imageObj = img as Record<string, unknown>;
          return {
            type: 'image',
            description: this._analyzeImage(imageObj),
            metadata: imageObj
          };
        }
        return { type: 'image', description: 'Image attachment', metadata: img };
      });
    }

    // Extract audio/transcribed text
    if (context.audio && typeof context.audio === 'object') {
      const audioObj = context.audio as Record<string, unknown>;
      multimodal.audio = {
        type: 'audio',
        transcription: this._cleanTranscription(audioObj.transcription as string),
        metadata: audioObj
      };
    }

    return multimodal;
  }

  /**
   * Extract attached files from context
   */
  private _extractAttachedFiles(context: Record<string, unknown>): Record<string, unknown>[] {
    const files: Record<string, unknown>[] = [];

    if (context.files && Array.isArray(context.files)) {
      for (const file of context.files) {
        if (typeof file === 'object' && file !== null) {
          const fileObj = file as Record<string, unknown>;
          files.push({
            name: fileObj.name || 'Unknown file',
            path: fileObj.path || '',
            type: fileObj.type || 'unknown',
            size: fileObj.size || 0,
            content: fileObj.content || '',
            summary: this._summarizeFile(fileObj)
          });
        }
      }
    }

    return files;
  }

  /**
   * Analyze image content (placeholder for future image analysis)
   */
  private _analyzeImage(imageObj: Record<string, unknown>): string {
    // Future: Implement actual image analysis
    return `Image attachment: ${imageObj.name || 'unnamed'} (${imageObj.type || 'unknown type'})`;
  }

  /**
   * Clean and improve transcribed audio text
   */
  private _cleanTranscription(transcription: string): string {
    if (!transcription || typeof transcription !== 'string') {
      return '';
    }

    // Basic text cleaning and improvement
    return transcription
      .trim()
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/\b(um|uh|er|ah)\b/gi, '') // Remove filler words
      .replace(/\s+/g, ' ') // Clean up extra spaces from filler word removal
      .trim();
  }

  /**
   * Summarize file content
   */
  private _summarizeFile(fileObj: Record<string, unknown>): string {
    const name = fileObj.name as string || 'Unknown file';
    const type = fileObj.type as string || 'unknown';
    const size = fileObj.size as number || 0;

    let summary = `File: ${name} (${type})`;
    if (size > 0) {
      summary += ` - ${this._formatFileSize(size)}`;
    }

    // Add content preview for text files
    if (fileObj.content && typeof fileObj.content === 'string') {
      const content = fileObj.content as string;
      if (content.length > 200) {
        summary += `\nPreview: ${content.substring(0, 200)}...`;
      } else {
        summary += `\nContent: ${content}`;
      }
    }

    return summary;
  }

  /**
   * Format file size in human-readable format
   */
  private _formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Override the run method to use our custom processInput logic
   */
  async run(input: AgentRunInput, agentContext: AgentContext = {}): Promise<AgentRunResult> {
    try {
      logger.info(`receiverAgent run called with mode: ${input.mode}`);

      // Use our custom processInput method
      const result = await this.processInput(input.prompt, agentContext);

      return {
        success: true,
        output: result,
        metadata: {
          agentId: this.id,
          agentName: this.name,
          mode: input.mode,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      logger.error(`receiverAgent run error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          agentId: this.id,
          agentName: this.name,
          mode: input.mode,
          timestamp: Date.now()
        }
      };
    }
  }
}

export default receiverAgent;
