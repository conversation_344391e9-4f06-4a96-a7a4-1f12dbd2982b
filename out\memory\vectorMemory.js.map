{"version": 3, "file": "vectorMemory.js", "sourceRoot": "", "sources": ["../../src/memory/vectorMemory.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAmC;AAEnC,kDAA+C;AAC/C,sCAAsC;AACtC,+CAAiC;AAUjC;;GAEG;AACH,MAAa,mBAAmB;IACtB,MAAM,CAAC,QAAQ,CAAsB;IACrC,OAAO,GAAmB,EAAE,CAAC;IAC7B,WAAW,GAAG,KAAK,CAAC;IACpB,gBAAgB,CAAgC;IAExD,gBAAwB,CAAC;IAEjB,kBAAkB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;IAE7D;;SAEK;IACE,iBAAiB,CAAC,QAAoB;QAC3C,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YAClC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC3D,CAAC;QACD,OAAO,mBAAmB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED;;SAEK;IACE,mBAAmB,CAAC,gBAAmC;QAC5D,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC3C,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU,CAAC,OAAiC;QACvD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,eAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;YACpF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,0FAA0F;YAC1F,IAAI,OAAO,EAAE,CAAC;gBACZ,eAAM,CAAC,KAAK,CAAC,kEAAkE,CAAC,CAAC;gBACjF,wEAAwE;gBACxE,gDAAgD;YAClD,CAAC;YAED,8CAA8C;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;YAC3D,eAAM,CAAC,IAAI,CAAC,mCAAmC,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;YAE3E,sEAAsE;YACtE,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBACpD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAC/C,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrE,eAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,GAAG,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;YACxG,CAAC;YAED,4BAA4B;YAC5B,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,GAAG,EAAE;gBACtD,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;YAC3D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEvE,sCAAsC;YACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YAE7E,+BAA+B;YAC/B,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,OAAO,CAAC,MAAM,gBAAgB,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;QACvG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe,CAAC,MAAmB;QAC/C,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBAClE,OAAO;YACT,CAAC;YAED,wCAAwC;YACxC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gBAChC,eAAM,CAAC,IAAI,CAAC,YAAY,QAAQ,CAAC,UAAU,8BAA8B,CAAC,CAAC;gBAC3E,OAAO;YACT,CAAC;YAED,qBAAqB;YACrB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAEnE,eAAe;YACf,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,2BAA2B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,gBAAgB,CAAC,CAAW,EAAE,CAAW;QAC/C,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QAED,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,CAAC;QACX,CAAC;QAED,OAAO,UAAU,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,cAA8D;QACnF,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,KAA4C,CAAC;YACjD,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;gBACvC,MAAM,aAAa,GAAG,IAAA,kBAAS,EAAS,6BAA6B,EAAE,SAAS,CAAC,CAAC;gBAClF,MAAM,WAAW,GAAG,IAAA,kBAAS,EAAS,2BAA2B,EAAE,SAAS,CAAC,CAAC;gBAC9E,KAAK,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,aAA6B,EAAE,IAAI,EAAE,WAAyB,EAAE,EAAE,CAAC;YAC5H,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,cAAc,CAAC;YACzB,CAAC;YACD,iDAAiD;YACjD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEjE,gCAAgC;YAChC,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAExC,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;IAC7C,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,EAAU;QAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,OAAuD;QAC3F,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YAC5E,IAAI,aAAa,EAAE,CAAC;gBAClB,oBAAoB;gBACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC;gBAC3D,wCAAwC;gBACxC,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;gBAC1C,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC;YACD,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,EAAU;QAClC,2BAA2B;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC;QAE3D,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,aAAa;QACxB,gBAAgB;QAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAElB,mCAAmC;QACnC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,KAAK,GAAG,EAAE;QACnD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;YAC1C,KAAK;YACL,KAAK;SACN,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,qBAAqB,CAAC,KAAa,EAAE,UAAiD,EAAE;QACnG,oDAAoD;QACpD,MAAM,IAAI,GAAiC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;QAEtG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1B,CAAC;YAED,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gBAC7C,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;gBAC5D,2BAA2B;gBAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;oBAC1C,KAAK;oBACL,KAAK,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK;oBAC5D,MAAM,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;iBACjE,CAAC,CAAC;YACL,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE5D,8BAA8B;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACzC,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC;aAC9D,CAAC,CAAC,CAAC;YAEJ,qBAAqB;YACrB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;YAEnD,4BAA4B;YAC5B,MAAM,kBAAkB,GAAG,IAAA,kBAAS,EAAS,2BAA2B,EAAE,GAAG,CAAC,CAAC;YAC/E,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,IAAI,kBAAkB,CAAC,CAAC;YAEtF,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;gBAClE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACtE,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YACzE,CAAC,CAAC,CAAC,CAAC;YACJ,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAA4C,CAAC;YAE1F,2BAA2B;YAC3B,IAAI,gBAAgB,GAAG,aAAa,CAAC;YACrC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClB,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CACxC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CACnD,CAAC;gBACJ,CAAC;gBAED,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;oBAChB,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CACxC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAC/C,CAAC;gBACJ,CAAC;gBAED,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC1C,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CACxC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAClF,CAAC;gBACJ,CAAC;gBAED,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;oBACvC,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;oBAC3C,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CACxC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,IAAI,aAAa,CAC5C,CAAC;gBACJ,CAAC;gBAED,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;oBACrC,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;oBACvC,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CACxC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,IAAI,WAAW,CAC1C,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,cAAc;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAC/B,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,2BAA2B;YAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;CACF;AAlYD,kDAkYC;AAED,4BAA4B;AACf,QAAA,mBAAmB,GAAG,mBAAmB,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import { logger } from '../logger';\r\nimport type { MemoryEntry, MemorySearchOptions, MemorySource, MemoryType, IMemoryOperations } from './types';\r\nimport { llmService } from '../llm/llmService';\r\nimport { getConfig } from '../config';\r\nimport * as vscode from 'vscode';\r\n\r\n/**\r\n * Vector representation of a memory\r\n */\r\ninterface MemoryVector {\r\n  memoryId: string;\r\n  vector: number[];\r\n}\r\n\r\n/**\r\n * Vector memory manager for semantic search\r\n */\r\nexport class VectorMemoryManager {\r\n  private static instance: VectorMemoryManager;\r\n  private vectors: MemoryVector[] = [];\r\n  private initialized = false;\r\n  private memoryOperations: IMemoryOperations | undefined;\r\n\r\n  private constructor() { }\r\n\r\n  private _onMemoriesChanged = new vscode.EventEmitter<void>();\r\n\r\n  /**\r\n     * Register a listener for memory changes\r\n     */\r\n  public onMemoriesChanged(listener: () => void): vscode.Disposable {\r\n    return this._onMemoriesChanged.event(listener);\r\n  }\r\n\r\n  /**\r\n     * Get the singleton instance\r\n     */\r\n  public static getInstance(): VectorMemoryManager {\r\n    if (!VectorMemoryManager.instance) {\r\n      VectorMemoryManager.instance = new VectorMemoryManager();\r\n    }\r\n    return VectorMemoryManager.instance;\r\n  }\r\n\r\n  /**\r\n     * Set the memory operations provider to break circular dependency\r\n     */\r\n  public setMemoryOperations(memoryOperations: IMemoryOperations): void {\r\n    this.memoryOperations = memoryOperations;\r\n  }\r\n\r\n  /**\r\n     * Initialize the vector memory manager\r\n     */\r\n  public async initialize(context?: vscode.ExtensionContext): Promise<void> {\r\n    if (this.initialized) {\r\n      return;\r\n    }\r\n\r\n    if (!this.memoryOperations) {\r\n      logger.warn('Vector memory manager initialized without memory operations provider');\r\n      this.initialized = true;\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Store context for potential future use (e.g., storing vector data in extension storage)\r\n      if (context) {\r\n        logger.debug('Vector memory manager initialized with VS Code extension context');\r\n        // Context could be used for persisting vector data to extension storage\r\n        // or for accessing extension-specific resources\r\n      }\r\n\r\n      // Load existing memories and generate vectors\r\n      const memories = await this.memoryOperations.getMemories();\r\n      logger.info(`Initializing vector memory with ${memories.length} memories`);\r\n\r\n      // Generate vectors for all memories (in batches to avoid overloading)\r\n      const batchSize = 10;\r\n      for (let i = 0; i < memories.length; i += batchSize) {\r\n        const batch = memories.slice(i, i + batchSize);\r\n        await Promise.all(batch.map(memory => this.addMemoryVector(memory)));\r\n        logger.debug(`Processed vector batch ${i / batchSize + 1}/${Math.ceil(memories.length / batchSize)}`);\r\n      }\r\n\r\n      // Listen for memory changes\r\n      this.memoryOperations.registerMemoryChangeListener(() => {\r\n        this.syncWithMemoryManager();\r\n        this._onMemoriesChanged.fire();\r\n      });\r\n\r\n      this.initialized = true;\r\n      logger.info('Vector memory initialized successfully');\r\n    } catch (error) {\r\n      logger.error('Failed to initialize vector memory:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Sync with memory manager\r\n     */\r\n  private async syncWithMemoryManager(): Promise<void> {\r\n    if (!this.memoryOperations) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const memories = await this.memoryOperations.getMemories();\r\n      const memoryIds = new Set(memories.map(memory => memory.id));\r\n      const vectorIds = new Set(this.vectors.map(vector => vector.memoryId));\r\n\r\n      // Remove vectors for deleted memories\r\n      this.vectors = this.vectors.filter(vector => memoryIds.has(vector.memoryId));\r\n\r\n      // Add vectors for new memories\r\n      const newMemories = memories.filter(memory => !vectorIds.has(memory.id));\r\n      for (const memory of newMemories) {\r\n        await this.addMemoryVector(memory);\r\n      }\r\n\r\n      logger.debug(`Synced vector memory: ${this.vectors.length} vectors for ${memories.length} memories`);\r\n    } catch (error) {\r\n      logger.error('Failed to sync vector memory:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Add a vector for a memory\r\n     */\r\n  private async addMemoryVector(memory: MemoryEntry): Promise<void> {\r\n    try {\r\n      // Generate vector using the default provider\r\n      const provider = await llmService.getDefaultProvider();\r\n      if (!provider) {\r\n        logger.warn('No default provider available for vector embedding');\r\n        return;\r\n      }\r\n\r\n      // Check if provider supports embeddings\r\n      if (!provider.generateEmbedding) {\r\n        logger.warn(`Provider ${provider.providerId} does not support embeddings`);\r\n        return;\r\n      }\r\n\r\n      // Generate embedding\r\n      const embedding = await provider.generateEmbedding(memory.content);\r\n\r\n      // Store vector\r\n      this.vectors.push({\r\n        memoryId: memory.id,\r\n        vector: embedding\r\n      });\r\n\r\n      logger.debug(`Added vector for memory ${memory.id}`);\r\n    } catch (error) {\r\n      logger.error(`Failed to add vector for memory ${memory.id}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Calculate cosine similarity between two vectors\r\n     */\r\n  private cosineSimilarity(a: number[], b: number[]): number {\r\n    if (a.length !== b.length) {\r\n      throw new Error('Vectors must have the same length');\r\n    }\r\n\r\n    let dotProduct = 0;\r\n    let normA = 0;\r\n    let normB = 0;\r\n\r\n    for (let i = 0; i < a.length; i++) {\r\n      dotProduct += a[i] * b[i];\r\n      normA += a[i] * a[i];\r\n      normB += b[i] * b[i];\r\n    }\r\n\r\n    normA = Math.sqrt(normA);\r\n    normB = Math.sqrt(normB);\r\n\r\n    if (normA === 0 || normB === 0) {\r\n      return 0;\r\n    }\r\n\r\n    return dotProduct / (normA * normB);\r\n  }\r\n\r\n  /**\r\n     * Add a memory with content and metadata\r\n     */\r\n  public async addMemory(contentOrEntry: string | Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry> {\r\n    if (!this.memoryOperations) {\r\n      throw new Error('Memory operations not available');\r\n    }\r\n\r\n    try {\r\n      // Convert string to memory entry if needed\r\n      let entry: Omit<MemoryEntry, 'id' | 'timestamp'>;\r\n      if (typeof contentOrEntry === 'string') {\r\n        const defaultSource = getConfig<string>('memory.vector.defaultSource', 'unknown');\r\n        const defaultType = getConfig<string>('memory.vector.defaultType', 'generic');\r\n        entry = { content: contentOrEntry, metadata: { source: defaultSource as MemorySource, type: defaultType as MemoryType } };\r\n      } else {\r\n        entry = contentOrEntry;\r\n      }\r\n      // Create a memory entry using the memory manager\r\n      const memoryEntry = await this.memoryOperations.addMemory(entry);\r\n\r\n      // Add vector for the new memory\r\n      await this.addMemoryVector(memoryEntry);\r\n\r\n      return memoryEntry;\r\n    } catch (error) {\r\n      logger.error('Failed to add memory:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Get all memories\r\n     */\r\n  public async getMemories(): Promise<MemoryEntry[]> {\r\n    if (!this.memoryOperations) {\r\n      return [];\r\n    }\r\n    return this.memoryOperations.getMemories();\r\n  }\r\n\r\n  /**\r\n     * Get a memory by ID\r\n     */\r\n  public async getMemory(id: string): Promise<MemoryEntry | undefined> {\r\n    if (!this.memoryOperations) {\r\n      return undefined;\r\n    }\r\n    return this.memoryOperations.getMemory(id);\r\n  }\r\n\r\n  /**\r\n   * Updates an existing memory with new content or metadata.\r\n   * @param id The ID of the memory to update\r\n   * @param updates The fields to update\r\n   * @returns The updated memory entry, or undefined if not found\r\n   */\r\n  public async updateMemory(id: string, updates: Partial<Omit<MemoryEntry, 'id' | 'timestamp'>>): Promise<MemoryEntry | undefined> {\r\n    if (!this.initialized) {\r\n      throw new Error('VectorMemoryManager not initialized');\r\n    }\r\n    if (!this.memoryOperations) {\r\n      return undefined;\r\n    }\r\n    try {\r\n      const updatedMemory = await this.memoryOperations.updateMemory(id, updates);\r\n      if (updatedMemory) {\r\n        // Remove old vector\r\n        this.vectors = this.vectors.filter(v => v.memoryId !== id);\r\n        // Add new vector for the updated memory\r\n        await this.addMemoryVector(updatedMemory);\r\n        this._onMemoriesChanged.fire();\r\n      }\r\n      return updatedMemory;\r\n    } catch (error) {\r\n      logger.error(`Failed to update memory ${id}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete a memory by ID\r\n   */\r\n  public async deleteMemory(id: string): Promise<boolean> {\r\n    // Remove from vector store\r\n    this.vectors = this.vectors.filter(v => v.memoryId !== id);\r\n\r\n    // Delete from memory manager\r\n    if (!this.memoryOperations) {\r\n      return false;\r\n    }\r\n    return await this.memoryOperations.deleteMemory(id);\r\n  }\r\n\r\n  /**\r\n     * Clear all memories\r\n     */\r\n  public async clearMemories(): Promise<void> {\r\n    // Clear vectors\r\n    this.vectors = [];\r\n\r\n    // Clear memories in memory manager\r\n    if (this.memoryOperations) {\r\n      await this.memoryOperations.clearMemories();\r\n    }\r\n  }\r\n\r\n  /**\r\n     * Search memories by text\r\n     */\r\n  public async searchMemories(query: string, limit = 10): Promise<MemoryEntry[]> {\r\n    if (!this.memoryOperations) {\r\n      return [];\r\n    }\r\n    return this.memoryOperations.searchMemories({\r\n      query,\r\n      limit\r\n    });\r\n  }\r\n\r\n  /**\r\n     * Search memories by semantic similarity\r\n     */\r\n  public async searchSimilarMemories(query: string, options: Partial<MemorySearchOptions> | number = {}): Promise<MemoryEntry[]> {\r\n    // Handle the case where options is a number (limit)\r\n    const opts: Partial<MemorySearchOptions> = typeof options === 'number' ? { limit: options } : options;\r\n\r\n    if (!this.memoryOperations) {\r\n      return [];\r\n    }\r\n\r\n    try {\r\n      if (!this.initialized) {\r\n        await this.initialize();\r\n      }\r\n\r\n      // Generate vector for query\r\n      const provider = await llmService.getDefaultProvider();\r\n      if (!provider || !provider.generateEmbedding) {\r\n        logger.warn('No provider with embedding support available');\r\n        // Fall back to text search\r\n        return this.memoryOperations.searchMemories({\r\n          query,\r\n          limit: typeof options === 'number' ? options : options.limit,\r\n          filter: typeof options === 'object' ? options.filter : undefined\r\n        });\r\n      }\r\n\r\n      const queryVector = await provider.generateEmbedding(query);\r\n\r\n      // Calculate similarity scores\r\n      const scores = this.vectors.map(vector => ({\r\n        memoryId: vector.memoryId,\r\n        similarity: this.cosineSimilarity(queryVector, vector.vector)\r\n      }));\r\n\r\n      // Sort by similarity\r\n      scores.sort((a, b) => b.similarity - a.similarity);\r\n\r\n      // Apply relevance threshold\r\n      const relevanceThreshold = getConfig<number>('memory.relevanceThreshold', 0.7);\r\n      const relevantScores = scores.filter(score => score.similarity >= relevanceThreshold);\r\n\r\n      // Get memories for relevant scores\r\n      const memories = await Promise.all(relevantScores.map(async score => {\r\n        const memory = await this.memoryOperations?.getMemory(score.memoryId);\r\n        return memory ? { ...memory, relevance: score.similarity } : undefined;\r\n      }));\r\n      const validMemories = memories.filter(Boolean) as (MemoryEntry & { relevance: number })[];\r\n\r\n      // Apply additional filters\r\n      let filteredMemories = validMemories;\r\n      if (opts.filter) {\r\n        const filter = opts.filter;\r\n        if (filter.source) {\r\n          filteredMemories = filteredMemories.filter(\r\n            memory => memory.metadata.source === filter.source\r\n          );\r\n        }\r\n\r\n        if (filter.type) {\r\n          filteredMemories = filteredMemories.filter(\r\n            memory => memory.metadata.type === filter.type\r\n          );\r\n        }\r\n\r\n        if (filter.tags && filter.tags.length > 0) {\r\n          filteredMemories = filteredMemories.filter(\r\n            memory => filter.tags?.every(tag => memory.metadata.tags?.includes(tag)) ?? false\r\n          );\r\n        }\r\n\r\n        if (filter.fromTimestamp !== undefined) {\r\n          const fromTimestamp = filter.fromTimestamp;\r\n          filteredMemories = filteredMemories.filter(\r\n            memory => memory.timestamp >= fromTimestamp\r\n          );\r\n        }\r\n\r\n        if (filter.toTimestamp !== undefined) {\r\n          const toTimestamp = filter.toTimestamp;\r\n          filteredMemories = filteredMemories.filter(\r\n            memory => memory.timestamp <= toTimestamp\r\n          );\r\n        }\r\n      }\r\n\r\n      // Apply limit\r\n      const limit = opts.limit || 10;\r\n      return filteredMemories.slice(0, limit);\r\n    } catch (error) {\r\n      logger.error('Error in semantic search:', error);\r\n      // Fall back to text search\r\n      return this.memoryOperations.searchMemories({ query, ...opts });\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const vectorMemoryManager = VectorMemoryManager.getInstance();\r\n"]}