/* File Changes Panel Styles */

.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-size: 13px;
  font-family: var(--vscode-font-family);
  cursor: pointer;
  transition: all 0.2s ease;
}

.button:hover {
  background: var(--vscode-button-hoverBackground);
}

.button.primary {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.button.primary:hover {
  background: var(--vscode-button-hoverBackground);
}

.button.secondary {
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.button.secondary:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

.button.danger {
  background: var(--vscode-inputValidation-errorBackground);
  color: var(--vscode-inputValidation-errorForeground);
  border-color: var(--vscode-inputValidation-errorBorder);
}

.button.danger:hover {
  opacity: 0.9;
}

.content {
  min-height: 400px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: var(--vscode-descriptionForeground);
}

.empty-state .codicon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--vscode-charts-green);
}

.empty-state h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

.changes-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  border-radius: 6px;
  margin-bottom: 16px;
  font-weight: 500;
}

.changes-summary .codicon {
  font-size: 16px;
}

.changes-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  overflow: hidden;
}

.change-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vscode-list-inactiveSelectionBackground);
  border-bottom: 1px solid var(--vscode-panel-border);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.change-item:last-child {
  border-bottom: none;
}

.change-item:hover {
  background: var(--vscode-list-hoverBackground);
}

.change-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.change-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.change-icon.codicon-edit {
  color: var(--vscode-charts-yellow);
}

.change-icon.codicon-add {
  color: var(--vscode-charts-green);
}

.change-icon.codicon-remove {
  color: var(--vscode-charts-red);
}

.change-details {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--vscode-foreground);
  margin-bottom: 2px;
}

.change-meta {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.change-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.change-item:hover .change-actions {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: var(--vscode-foreground);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--vscode-toolbar-hoverBackground);
}

.action-btn.view-btn:hover {
  color: var(--vscode-charts-blue);
}

.action-btn.open-btn:hover {
  color: var(--vscode-charts-purple);
}

.action-btn.discard-btn:hover {
  color: var(--vscode-charts-red);
  background: var(--vscode-inputValidation-errorBackground);
}

.action-btn.keep-btn:hover {
  color: var(--vscode-charts-green);
  background: var(--vscode-testing-iconPassed);
}

.action-btn .codicon {
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .change-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .change-actions {
    opacity: 1;
    justify-content: center;
  }
}

/* Dark theme adjustments */
.vscode-dark .change-item {
  background: var(--vscode-editor-background);
}

.vscode-dark .change-item:hover {
  background: var(--vscode-list-hoverBackground);
}

/* High contrast theme adjustments */
.vscode-high-contrast .change-item {
  border: 1px solid var(--vscode-contrastBorder);
}

.vscode-high-contrast .button {
  border: 1px solid var(--vscode-contrastBorder);
}

/* Animation for smooth transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.change-item {
  animation: fadeIn 0.3s ease-out;
}

/* Loading state */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--vscode-descriptionForeground);
}

.loading .codicon {
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
