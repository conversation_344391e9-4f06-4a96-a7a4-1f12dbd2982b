import * as vscode from 'vscode';
import { getNonce } from '../../utils';
import { WorkspaceHelper } from '../workspace/workspace';
import { AgentManager } from '../../agents/agentUtilities/agentManager';
import { SupervisorAgent, AgentStreamEvent, StreamingContext } from '../../agents/agentTypes/supervisorAgent';
import { ChatMessage, ChatSession } from './chatView';
import { Logger } from '../../logger';
import { llmService } from '../../llm/llmService';
import { LLMModelInfo } from '../../llm/llmProvider';
import { AudioService } from '../../services/audioService';
import { TTSSettingsView } from '../settings/ttsSettingsSection';
import { AllSettingsPanel } from '../settings/allSettingsPanel';
import { MemoryManager } from '../../memory/memoryManager';
import { ProviderManager } from '../../llm/providerManager';
import { EventEmitter } from 'events';

export class ChatViewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'codessa.chatView';
  private _view?: vscode.WebviewView;
  private _disposables: vscode.Disposable[] = [];
  private _chatSession: ChatSession;
  private _audioService: AudioService;
  private _ttsSettingsView: TTSSettingsView;
  private _supervisorAgent?: SupervisorAgent;

  // Enhanced streaming support
  private _streamingEmitter: EventEmitter = new EventEmitter();
  private _activeStreams: Map<string, StreamingContext> = new Map();
  private _agentStatusMap: Map<string, string> = new Map();

  private _currentCancelTokenSource?: vscode.CancellationTokenSource;

  constructor(
    private readonly _extensionUri: vscode.Uri,
    private readonly _context: vscode.ExtensionContext
  ) {
    this._chatSession = new ChatSession(_context);
    this._audioService = AudioService.getInstance(_context);
    this._ttsSettingsView = TTSSettingsView.getInstance(_context);
    // Initialize supervisor agent reference (will be set later)
    this._supervisorAgent = undefined;
    this.refreshAgentReferences();

    // Load any saved chat session
    this._chatSession.load().catch((error: Error) => {
      Logger.instance.error('Failed to load chat session:', error);
    });

    // Ensure the setting exists and defaults to ["ollama"] if not set
    const config = vscode.workspace.getConfiguration('codessa.llm');
    if (!config.get('enabledProviders')) {
      config.update('enabledProviders', ['ollama'], vscode.ConfigurationTarget.Global);
    }
  }

  /**
   * Refresh agent references from AgentManager
   */
  public refreshAgentReferences(): void {
    const agentManager = AgentManager.getInstance();
    const maybeSupervisor = agentManager.getSupervisorAgent();
    this._supervisorAgent = maybeSupervisor instanceof SupervisorAgent ? maybeSupervisor : undefined;

    if (this._supervisorAgent) {
      Logger.instance.info('ChatViewProvider: Supervisor agent reference updated');
    } else {
      Logger.instance.warn('ChatViewProvider: No supervisor agent available');
    }
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    _context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        this._extensionUri
      ]
    };

    webviewView.webview.html = this._getWebviewContent(webviewView.webview);

    // Use the provided resolve context and cancellation token to satisfy lints meaningfully
    void _context; // explicit usage to indicate we intentionally ignore additional details here
    _token.onCancellationRequested(() => {
      Logger.instance.info('Chat view resolve was cancelled');
    });

    // Initialize providers and models before sending initial state
    this._initializeProvidersAndModels(webviewView).then(() => {
      // Send initial state to webview
      webviewView.webview.postMessage({
        type: 'initialize',
        data: {
          messages: this._chatSession.getMessages(),
          isProcessing: this._chatSession.getProcessing()
        }
      });

      // Send provider info immediately after initialization
      this._sendProviderInfo(webviewView);
    }).catch(error => {
      Logger.instance.error('Failed to initialize providers and models:', error);
      // Send initial state anyway
      webviewView.webview.postMessage({
        type: 'initialize',
        data: {
          messages: this._chatSession.getMessages(),
          isProcessing: this._chatSession.getProcessing()
        }
      });

      // Still send provider info even if initialization fails
      this._sendProviderInfo(webviewView);
    });

    webviewView.webview.onDidReceiveMessage(async (data) => {
      // Support both 'type' and 'command' from frontend messages
      const messageType = data.type || data.command;
      Logger.instance.info(`Chat view received message: ${messageType}`, data);

      switch (messageType) {
        case 'providerChanged': {
          const providerId = data.provider;
          if (providerId) {
            Logger.instance.info(`Provider changed to: ${providerId}`);
            // Store the selected provider
            this._context.globalState.update('selectedProvider', providerId);

            // Get models for the selected provider
            const models = await this._getModelsForProvider(providerId);

            // Send models to webview
            webviewView.webview.postMessage({
              type: 'models',
              models,
              provider: providerId
            });

            // Set default model if available
            if (models.length > 0) {
              this._context.globalState.update('selectedModel', models[0].id);
              webviewView.webview.postMessage({
                type: 'currentModel',
                model: models[0].id
              });
            }
          }
          break;
        }
        case 'modelChanged': {
          const modelId = data.model;
          if (modelId) {
            // Store the selected model in the context
            this._context.globalState.update('selectedModel', modelId);
            Logger.instance.info(`Model changed to: ${modelId}`);
          }
          break;
        }
        case 'sendMessage': {
          const messageText = data.text || data.message?.text || data.content;
          if (!messageText) {
            Logger.instance.warn('Received sendMessage with no text content');
            break;
          }

          // Cancel any previous operation
          if (this._currentCancelTokenSource) {
            this._currentCancelTokenSource.cancel();
            this._currentCancelTokenSource.dispose();
          }
          this._currentCancelTokenSource = new vscode.CancellationTokenSource();
          const cancellationToken = this._currentCancelTokenSource.token;

          // Add user message
          const userMessage: ChatMessage = {
            id: `user_${Date.now()}`,
            role: 'user',
            content: messageText,
            timestamp: Date.now()
          };
          this._chatSession.addMessage(userMessage);
          this._chatSession.setProcessing(true);
          this._chatSession.save();

          // Send processing state to webview
          webviewView.webview.postMessage({
            type: 'processingState',
            isProcessing: true
          });

          try {
            // Get selected provider and model from context
            const selectedProvider = this._context.globalState.get('selectedProvider') || 'ollama';
            const selectedModel = this._context.globalState.get('selectedModel') || 'llama3';

            Logger.instance.info(`Using provider: ${selectedProvider}, model: ${selectedModel}`);

            // Update the default LLM config to use selected provider/model
            const config = vscode.workspace.getConfiguration('codessa');
            await config.update('selectedProvider', selectedProvider, vscode.ConfigurationTarget.Global);
            await config.update('selectedModel', selectedModel, vscode.ConfigurationTarget.Global);

            // Create streaming context for real-time updates
            const streamId = `chat_${Date.now()}`;
            const streamingContext: StreamingContext = {
              streamId,
              onStream: (event: AgentStreamEvent) => {
                this.handleAgentStreamEvent(event, webviewView);
              },
              cancellationToken
            };

            this._activeStreams.set(streamId, streamingContext);

            const agentManager = AgentManager.getInstance();
            const receiverAgent = agentManager.getReceiverAgent();
            const maybeSupervisor = agentManager.getSupervisorAgent();
            const supervisorAgent: SupervisorAgent | undefined =
              maybeSupervisor instanceof SupervisorAgent ? maybeSupervisor : undefined;

            if (!receiverAgent || !supervisorAgent) {
              const errorResponse: ChatMessage = {
                id: `error_${Date.now()}`,
                role: 'error',
                content: 'Required agents (receiver or supervisor) not available.',
                timestamp: Date.now()
              };

              this._chatSession.addMessage(errorResponse);
              this._chatSession.setProcessing(false);
              this._chatSession.save();

              webviewView.webview.postMessage({
                type: 'addMessage',
                message: errorResponse
              });

              webviewView.webview.postMessage({
                type: 'processingState',
                isProcessing: false
              });
              return;
            }

            // --- Receiver agent thinking ---
            const receiverThinkingMessage: ChatMessage = {
              id: `receiver_thinking_${Date.now()}`,
              role: 'assistant',
              content: 'Receiver agent is processing your input...',
              timestamp: Date.now(),
              isThinking: true
            };
            this._chatSession.addMessage(receiverThinkingMessage);
            this._chatSession.save();
            webviewView.webview.postMessage({
              type: 'addMessage',
              message: receiverThinkingMessage
            });

            // First, process through receiver agent
            const receiverResult = await receiverAgent.run({
              prompt: messageText,
              mode: 'chat'
            }, {
              workspace: WorkspaceHelper.createWorkspaceContext(),
              variables: {
                attachedFiles: this._chatSession.getAttachedFiles()
              },
              streamingContext,
              cancellationToken
            });

            // Update receiver thinking message with result
            receiverThinkingMessage.content = '✅ **Receiver Agent** completed analysis:\\n\\n*Enhanced request ready for supervisor*';
            receiverThinkingMessage.isThinking = false;
            this._chatSession.updateMessage(receiverThinkingMessage);
            this._chatSession.save();
            webviewView.webview.postMessage({
              type: 'updateMessage',
              message: receiverThinkingMessage
            });

            // Brief pause to show receiver completion
            await new Promise(resolve => setTimeout(resolve, 800));

            if (cancellationToken.isCancellationRequested) {
              const cancelledMsg: ChatMessage = {
                id: `cancelled_${Date.now()}`,
                role: 'assistant',
                content: 'Operation cancelled.',
                timestamp: Date.now()
              };
              this._chatSession.addMessage(cancelledMsg);
              this._chatSession.setProcessing(false);
              this._chatSession.save();
              webviewView.webview.postMessage({
                type: 'addMessage',
                message: cancelledMsg
              });
              webviewView.webview.postMessage({
                type: 'processingState',
                isProcessing: false
              });
              return;
            }

            if (!receiverResult.success) {
              throw new Error(`Receiver agent error: ${receiverResult.error}`);
            }

            // --- Supervisor agent thinking ---
            const supervisorThinkingMessage: ChatMessage = {
              id: `supervisor_thinking_${Date.now()}`,
              role: 'assistant',
              content: '🎯 **Supervisor Agent** analyzing and orchestrating your request...',
              timestamp: Date.now(),
              isThinking: true
            };
            this._chatSession.addMessage(supervisorThinkingMessage);
            this._chatSession.save();
            webviewView.webview.postMessage({
              type: 'addMessage',
              message: supervisorThinkingMessage
            });

            // Then, process through supervisor agent with streaming
            const supervisorResult = await supervisorAgent.run({
              prompt: receiverResult.output || '',
              mode: 'chat'
            }, {
              workspace: WorkspaceHelper.createWorkspaceContext(),
              variables: {
                attachedFiles: this._chatSession.getAttachedFiles(),
                receiverOutput: receiverResult.output
              },
              streamingContext,
              cancellationToken
            });

            if (cancellationToken.isCancellationRequested) {
              this._chatSession.removeMessage(supervisorThinkingMessage.id);
              this._chatSession.save();
              webviewView.webview.postMessage({
                type: 'removeMessage',
                messageId: supervisorThinkingMessage.id
              });
              const cancelledMsg: ChatMessage = {
                id: `cancelled_${Date.now()}`,
                role: 'assistant',
                content: 'Operation cancelled.',
                timestamp: Date.now()
              };
              this._chatSession.addMessage(cancelledMsg);
              this._chatSession.setProcessing(false);
              this._chatSession.save();
              webviewView.webview.postMessage({
                type: 'addMessage',
                message: cancelledMsg
              });
              webviewView.webview.postMessage({
                type: 'processingState',
                isProcessing: false
              });
              return;
            }

            if (!supervisorResult.success) {
              // Remove supervisor thinking message
              this._chatSession.removeMessage(supervisorThinkingMessage.id);
              this._chatSession.save();
              webviewView.webview.postMessage({
                type: 'removeMessage',
                messageId: supervisorThinkingMessage.id
              });
              throw new Error(`Supervisor agent error: ${supervisorResult.error}`);
            }

            // Update supervisor thinking message to show completion
            supervisorThinkingMessage.content = '✅ **Supervisor Agent** completed orchestration and coordination';
            supervisorThinkingMessage.isThinking = false;
            this._chatSession.updateMessage(supervisorThinkingMessage);
            this._chatSession.save();
            webviewView.webview.postMessage({
              type: 'updateMessage',
              message: supervisorThinkingMessage
            });

            // Brief pause to show supervisor completion
            await new Promise(resolve => setTimeout(resolve, 800));

            // --- Delegated agents processing (if any) ---
            const additionalAgents = agentManager.getAdditionalAgents();
            let finalOutput = supervisorResult.output;

            for (const agent of additionalAgents) {
              const delegatedThinkingMessage: ChatMessage = {
                id: `delegated_thinking_${agent.id}_${Date.now()}`,
                role: 'assistant',
                content: `🔄 **${agent.name || agent.id}** processing delegated task...`,
                timestamp: Date.now(),
                isThinking: true
              };
              this._chatSession.addMessage(delegatedThinkingMessage);
              this._chatSession.save();
              webviewView.webview.postMessage({
                type: 'addMessage',
                message: delegatedThinkingMessage
              });

              const result = await agent.run({
                prompt: finalOutput || '',
                mode: 'chat'
              }, {
                workspace: WorkspaceHelper.createWorkspaceContext(),
                variables: {
                  attachedFiles: this._chatSession.getAttachedFiles(),
                  receiverOutput: receiverResult.output,
                  supervisorOutput: supervisorResult.output
                },
                streamingContext,
                cancellationToken
              });

              // Update delegated thinking message with result
              delegatedThinkingMessage.content = `✅ **${agent.name || agent.id}** completed task`;
              delegatedThinkingMessage.isThinking = false;
              this._chatSession.updateMessage(delegatedThinkingMessage);
              this._chatSession.save();
              webviewView.webview.postMessage({
                type: 'updateMessage',
                message: delegatedThinkingMessage
              });

              if (cancellationToken.isCancellationRequested) {
                const cancelledMsg: ChatMessage = {
                  id: `cancelled_${Date.now()}`,
                  role: 'assistant',
                  content: 'Operation cancelled.',
                  timestamp: Date.now()
                };
                this._chatSession.addMessage(cancelledMsg);
                this._chatSession.setProcessing(false);
                this._chatSession.save();
                webviewView.webview.postMessage({
                  type: 'addMessage',
                  message: cancelledMsg
                });
                webviewView.webview.postMessage({
                  type: 'processingState',
                  isProcessing: false
                });
                return;
              }

              if (result.success && result.output) {
                finalOutput = result.output;
              }

              // Brief pause between agents
              await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Add final assistant message with complete response
            const assistantMessage: ChatMessage = {
              id: `assistant_${Date.now()}`,
              role: 'assistant',
              content: finalOutput || 'I apologize, but I was unable to generate a response to your request.',
              timestamp: Date.now()
            };

            this._chatSession.addMessage(assistantMessage);
            this._chatSession.setProcessing(false);
            this._chatSession.save();

            webviewView.webview.postMessage({
              type: 'addMessage',
              message: assistantMessage
            });

            webviewView.webview.postMessage({
              type: 'processingState',
              isProcessing: false
            });

            // Clean up streaming context
            this._activeStreams.delete(streamId);

            Logger.instance.info('Message processing completed successfully');

          } catch (error) {
            Logger.instance.error('Error processing message:', error);
            const errorMessage: ChatMessage = {
              id: `error_${Date.now()}`,
              role: 'error',
              content: `Error: ${error instanceof Error ? error.message : String(error)}`,
              timestamp: Date.now()
            };

            this._chatSession.addMessage(errorMessage);
            this._chatSession.setProcessing(false);
            this._chatSession.save();

            webviewView.webview.postMessage({
              type: 'addMessage',
              message: errorMessage
            });

            webviewView.webview.postMessage({
              type: 'processingState',
              isProcessing: false
            });
          }
          break;
        }
        case 'openSettings': {
          Logger.instance.info('Opening settings panel');
          try {
            // Get required managers
            const memoryManager = MemoryManager.getInstance();
            const providerManager = ProviderManager.getInstance();

            // Open the AllSettingsPanel
            await AllSettingsPanel.createOrShow(this._context, this._extensionUri, memoryManager, providerManager);
          } catch (error) {
            Logger.instance.error('Failed to open settings panel:', error);
            vscode.window.showErrorMessage('Failed to open settings panel');
          }
          break;
        }
        case 'toggleTTS': {
          const state = data.state;
          Logger.instance.info(`TTS toggled: ${state}`);
          try {
            // Toggle TTS state
            this._audioService.setTTSEnabled(state);
          } catch (error) {
            Logger.instance.error('Failed to toggle TTS:', error);
          }
          break;
        }
        case 'clearChat': {
          Logger.instance.info('Clearing chat history');
          try {
            this._chatSession.clearMessages();
            this._chatSession.save();
            webviewView.webview.postMessage({
              type: 'clearMessages'
            });
          } catch (error) {
            Logger.instance.error('Failed to clear chat:', error);
          }
          break;
        }
        case 'exportChat': {
          Logger.instance.info('Exporting chat');
          try {
            const messages = this._chatSession.getMessages();
            const exportData = JSON.stringify(messages, null, 2);

            // Show save dialog
            const uri = await vscode.window.showSaveDialog({
              defaultUri: vscode.Uri.file('codessa-chat-export.json'),
              filters: {
                'JSON Files': ['json'],
                'All Files': ['*']
              }
            });

            if (uri) {
              await vscode.workspace.fs.writeFile(uri, Buffer.from(exportData, 'utf8'));
              vscode.window.showInformationMessage('Chat exported successfully');
            }
          } catch (error) {
            Logger.instance.error('Failed to export chat:', error);
            vscode.window.showErrorMessage('Failed to export chat');
          }
          break;
        }
        // Other message handlers remain the same...
      }
    });

    webviewView.onDidDispose(() => this.dispose(), null, this._disposables);
  }

  private _getWebviewContent(webview: vscode.Webview): string {
    const nonce = getNonce();
    const csp = `default-src 'none'; img-src ${webview.cspSource} https: data:; script-src 'nonce-${nonce}'; style-src 'unsafe-inline' ${webview.cspSource};`;

    // Get resource URIs
    const chatCssUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chat.css'));
    const chatJsUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chat.js'));

    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="Content-Security-Policy" content="${csp}" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Codessa Chat</title>
  <link rel="stylesheet" href="${chatCssUri}">
</head>
<body>
  <div class="chat-container">
    <!-- Top Toolbar -->
    <div class="top-toolbar">
      <div class="toolbar-left">
        <select id="mode-selector" class="dropdown">
          <option value="chat">Chat</option>
          <option value="ask">Ask</option>
          <option value="debug">Debug</option>
          <option value="edit">Edit</option>
          <option value="agent">Agent</option>
          <option value="multiAgent">Multi-Agent</option>
          <option value="research">Research</option>
          <option value="documentation">Documentation</option>
        </select>
        <select id="provider-selector" class="dropdown">
          <option value="ollama">Ollama</option>
        </select>
        <select id="model-selector" class="dropdown">
          <option value="llama3">Llama 3</option>
        </select>
      </div>
      <div class="toolbar-right">
        <button id="btn-settings" class="btn-icon" title="Settings">⚙️</button>
        <button id="btn-export" class="btn-icon" title="Export Chat">📤</button>
        <button id="btn-clear" class="btn-icon" title="Clear Chat">🗑️</button>
      </div>
    </div>

    <!-- Messages Container -->
    <div id="chat-messages" class="messages-container">
      <div id="empty-chat-message" class="empty-message">
        Start a conversation with Codessa...
      </div>
    </div>

    <!-- Typing Indicator -->
    <div id="typing-indicator" class="typing-indicator" style="display: none;">
      <div class="typing-dots">
        <span></span><span></span><span></span>
      </div>
      <span class="typing-text">Codessa is thinking...</span>
    </div>

    <!-- Input Area -->
    <div class="input-container">
      <div class="input-toolbar">
        <button id="btn-add-context" class="btn-icon" title="Add Context">📎</button>
        <button id="btn-attach-file" class="btn-icon" title="Attach File">📄</button>
        <button id="btn-attach-folder" class="btn-icon" title="Attach Folder">📁</button>
        <button id="btn-upload-image" class="btn-icon" title="Upload Image">🖼️</button>
      </div>

      <div class="input-wrapper">
        <textarea id="message-input" placeholder="Type your message..." rows="1"></textarea>
        <div class="input-actions">
          <button id="btn-record-audio" class="btn-icon" title="Record Audio">🎤</button>
          <button id="btn-toggle-tts" class="btn-icon" title="Toggle TTS">🔊</button>
          <button id="btn-send" class="btn-primary" title="Send">Send</button>
          <button id="btn-cancel" class="btn-secondary" title="Cancel" style="display: none;">Cancel</button>
        </div>
      </div>

      <div class="input-secondary-actions">
        <button id="btn-input-copy" class="btn-icon" title="Copy">📋</button>
        <button id="btn-input-cut" class="btn-icon" title="Cut">✂️</button>
        <button id="btn-input-paste" class="btn-icon" title="Paste">📋</button>
        <button id="btn-input-clear" class="btn-icon" title="Clear Input">🗑️</button>
      </div>
    </div>
  </div>

  <script nonce="${nonce}">
    // Initialize state for chat.js
    const initialState = {
      isProcessing: false,
      messages: [],
      isTTSActive: false,
      currentMode: 'chat',
      currentProvider: 'ollama',
      currentModel: 'llama3',
      availableProviders: [],
      availableModels: []
    };
  </script>
  <script nonce="${nonce}" src="${chatJsUri}"></script>
</body>
</html>`;
  }

  private async _getProviderList(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    configured: boolean;
    isLocal: boolean;
  }>> {
    try {
      const providers = llmService.getAllProviders();
      return providers.map(p => ({
        id: p.providerId,
        name: p.displayName,
        description: p.description,
        configured: p.isConfigured(),
        isLocal: p.providerId === 'ollama' || p.providerId === 'lmstudio'
      }));
    } catch (error) {
      Logger.instance.warn('Failed to get provider list:', error);
      return [];
    }
  }

  private async _getModelsForProvider(providerId: string): Promise<LLMModelInfo[]> {
    try {
      const provider = llmService.getProvider(providerId);
      if (provider && provider.isConfigured()) {
        const models = await provider.listModels();
        return models;
      }
      return this._getDefaultModelsForProvider(providerId);
    } catch (error) {
      Logger.instance.warn(`Failed to list models for provider ${providerId}:`, error);
      return this._getDefaultModelsForProvider(providerId);
    }
  }

  private _getDefaultModelsForProvider(providerId: string): LLMModelInfo[] {
    const provider = llmService.getProvider(providerId);
    if (provider?.defaultModel) {
      return [{ id: provider.defaultModel }];
    }
    // Minimal sensible defaults per common providers
    if (providerId === 'ollama') {
      return [{ id: 'llama3' }];
    }
    if (providerId === 'openai') {
      return [{ id: 'gpt-4o' }];
    }
    if (providerId === 'lmstudio') {
      return [{ id: 'default' }];
    }
    return [];
  }

  private async _sendProviderInfo(webviewView: vscode.WebviewView): Promise<void> {
    try {
      const providers = await this._getProviderList();
      webviewView.webview.postMessage({
        type: 'providers',
        providers
      });
    } catch (error) {
      Logger.instance.warn('Failed to send provider info:', error);
    }
  }

  private async _sendModelsForProvider(webviewView: vscode.WebviewView, providerId?: string): Promise<void> {
    try {
      let targetProviderId = providerId || (this._context.globalState.get('selectedProvider') as string);
      if (!targetProviderId) {
        const def = await llmService.getDefaultProvider();
        targetProviderId = def?.providerId || 'ollama';
      }

      const models = await this._getModelsForProvider(targetProviderId);
      webviewView.webview.postMessage({
        type: 'models',
        models,
        provider: targetProviderId
      });

      // Determine current model
      const currentModel = (this._context.globalState.get('selectedModel') as string) || models[0]?.id;
      if (currentModel) {
        webviewView.webview.postMessage({
          type: 'currentModel',
          model: currentModel
        });
      }
    } catch (error) {
      Logger.instance.warn('Failed to send models for provider:', error);
    }
  }

  private async _initializeProvidersAndModels(webviewView: vscode.WebviewView): Promise<void> {
    try {
      await this._sendProviderInfo(webviewView);

      // Ensure a selected provider exists
      let selectedProvider = this._context.globalState.get('selectedProvider') as string | undefined;
      if (!selectedProvider) {
        const def = await llmService.getDefaultProvider();
        selectedProvider = def?.providerId || 'ollama';
        this._context.globalState.update('selectedProvider', selectedProvider);
      }

      await this._sendModelsForProvider(webviewView, selectedProvider);
    } catch (error) {
      Logger.instance.warn('Failed to initialize providers/models:', error);
    }
  }

  public dispose(): void {
    this._disposables.forEach(d => d.dispose());
  }

  private _getMimeType(filePath: string): string {
    const ext = filePath.toLowerCase().split('.').pop();
    const map: Record<string, string> = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'js': 'text/javascript',
      'ts': 'text/typescript',
      'json': 'application/json',
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'gif': 'image/gif',
      'svg': 'image/svg+xml',
      'wav': 'audio/wav',
      'mp3': 'audio/mpeg'
    };
    return map[ext || ''] || 'application/octet-stream';
  }

  private handleAgentStreamEvent(event: AgentStreamEvent, webviewView: vscode.WebviewView): void {
    try {
      // Track agent status map for quick status queries
      if (event.type === 'start') {
        this._agentStatusMap.set(event.agentId, 'running');
      } else if (event.type === 'complete') {
        this._agentStatusMap.set(event.agentId, 'complete');
      } else if (event.type === 'error') {
        this._agentStatusMap.set(event.agentId, 'error');
      }

      webviewView.webview.postMessage({
        type: 'agentStream',
        event
      });
    } catch (error) {
      Logger.instance.warn('Failed to forward agent stream event:', error);
    }
  }

  public getStreamingStatus(): { activeStreams: number; activeAgents: string[] } {
    return {
      activeStreams: this._activeStreams.size,
      activeAgents: Array.from(this._agentStatusMap.keys())
    };
  }

  public reveal(): void {
    if (this._view) {
      this._view.show?.(true);
    } else {
      vscode.commands.executeCommand('workbench.view.extension.codessa-sidebar');
    }
  }
}