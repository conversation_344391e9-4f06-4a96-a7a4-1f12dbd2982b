"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentationMode = void 0;
const operationMode_1 = require("./operationMode");
const logger_1 = require("../../logger");
const contextManager_1 = require("./contextManager");
/**
 * Documentation Mode - Generate comprehensive documentation
 */
class DocumentationMode extends operationMode_1.OperationMode {
    id = 'documentation';
    displayName = 'Documentation';
    description = 'Generate comprehensive documentation for code';
    icon = '$(book)';
    defaultContextType = operationMode_1.ContextType.SELECTED_FILES;
    requiresHumanVerification = false;
    supportsMultipleAgents = false;
    /**
       * Process a user message in Documentation mode
       */
    async processMessage(message, agent, contextSource, additionalParams) {
        try {
            logger_1.Logger.instance.info(`Processing message in Documentation mode: ${message}`);
            // Get context content
            const contextContent = await contextManager_1.contextManager.getContextContent(contextSource);
            // Add memory context if available
            let memoryContext = '';
            try {
                const agentMemory = agent.getMemory();
                if (agentMemory) {
                    const relevantMemories = await agentMemory.getRelevantMemories(message);
                    if (relevantMemories && relevantMemories.length > 0) {
                        memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
                        logger_1.Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to documentation context`);
                    }
                }
            }
            catch (memoryError) {
                logger_1.Logger.instance.warn('Failed to retrieve memory context for documentation:', memoryError);
                // Continue without memory context
            }
            // Prepare the documentation prompt
            const prompt = `
You are a technical documentation specialist. Generate comprehensive documentation for the following code.

Documentation Request: ${message}

Code to Document:
${contextContent}

${memoryContext}

Please provide:
1. Overview and purpose
2. API documentation with parameters and return values
3. Usage examples
4. Implementation details
5. Best practices and considerations
6. Related components or dependencies

Format the documentation in clear, professional markdown with proper headings and code examples.
`;
            // Generate response using the agent
            const response = await agent.generate(prompt, this.getLLMParams());
            // Store the documentation session in memory
            try {
                const agentMemory = agent.getMemory();
                if (agentMemory) {
                    await agentMemory.addMessage('user', `Documentation request: ${message}`);
                    await agentMemory.addMessage('assistant', response);
                }
            }
            catch (memoryError) {
                logger_1.Logger.instance.warn('Failed to store documentation session in memory:', memoryError);
                // Continue without storing in memory
            }
            return response;
        }
        catch (error) {
            logger_1.Logger.instance.error('Error processing message in Documentation mode:', error);
            return `Error processing your documentation request: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
    /**
       * Get LLM parameters specific to Documentation mode
       */
    getLLMParams() {
        return {
            prompt: '',
            modelId: '',
            temperature: 0.2, // Low temperature for consistent documentation
            maxTokens: 2500, // High token limit for comprehensive docs
            stopSequences: [],
            mode: 'generate'
        };
    }
}
exports.DocumentationMode = DocumentationMode;
//# sourceMappingURL=documentationMode.js.map