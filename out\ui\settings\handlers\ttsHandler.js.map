{"version": 3, "file": "ttsHandler.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/handlers/ttsHandler.ts"], "names": [], "mappings": ";;AACA,4CA6DC;AA9DD,8CAA8C;AAC9C,SAAgB,gBAAgB,CAAC,OAAY,EAAE,KAAU;IACvD,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAChD,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IACjF,MAAM,QAAQ,GAAwE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtJ,IAAI,CAAC;QACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC;gBACnC,MAAM;YACR,CAAC;YACD,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;gBACxB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;oBACjC,QAAQ,CAAC,KAAK,GAAG,8BAA8B,CAAC;oBAChD,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC7B,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC;gBACpB,MAAM;YACR,CAAC;YACD,KAAK,SAAS,CAAC,CAAC,CAAC;gBACf,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;gBACxB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;oBACpB,QAAQ,CAAC,KAAK,GAAG,kBAAkB,CAAC;oBACpC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;gBACtE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;oBACxC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC;gBACjE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACxC,MAAM;YACR,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,QAAQ,CAAC,KAAK,GAAG,kBAAkB,CAAC;oBACpC,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAClE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;oBACxC,MAAM;gBACR,CAAC;gBACD,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAClC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC3C,CAAC;QACD,CAAC;IACH,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC9B,CAAC", "sourcesContent": ["// Handler for TTS settings messages and logic\nexport function handleTTSMessage(message: any, panel: any) {\n  const settings = (window as any).settings || {};\n  settings.ttsVoices = Array.isArray(settings.ttsVoices) ? settings.ttsVoices : [];\n  const response: { type: string; success: boolean; data: any; error: string | null } = { type: message.type, success: false, data: null, error: null };\n  try {\n    switch (message.type) {\n    case 'getTTS': {\n      response.success = true;\n      response.data = settings.ttsVoices;\n      break;\n    }\n    case 'addTTS': {\n      const tts = message.tts;\n      if (!tts || !tts.name || !tts.id) {\n        response.error = 'Missing required TTS fields.';\n        break;\n      }\n      settings.ttsVoices.push(tts);\n      response.success = true;\n      response.data = tts;\n      break;\n    }\n    case 'editTTS': {\n      const tts = message.tts;\n      if (!tts || !tts.id) {\n        response.error = 'TTS id required.';\n        break;\n      }\n      const idx = settings.ttsVoices.findIndex((t: any) => t.id === tts.id);\n      if (idx === -1) {\n        response.error = 'TTS voice not found.';\n        break;\n      }\n      settings.ttsVoices[idx] = { ...settings.ttsVoices[idx], ...tts };\n      response.success = true;\n      response.data = settings.ttsVoices[idx];\n      break;\n    }\n    case 'deleteTTS': {\n      const id = message.id;\n      if (!id) {\n        response.error = 'TTS id required.';\n        break;\n      }\n      const idx = settings.ttsVoices.findIndex((t: any) => t.id === id);\n      if (idx === -1) {\n        response.error = 'TTS voice not found.';\n        break;\n      }\n      settings.ttsVoices.splice(idx, 1);\n      response.success = true;\n      break;\n    }\n    default: {\n      response.error = 'Unknown message type.';\n    }\n    }\n  } catch (err: any) {\n    response.error = err?.message || String(err);\n  }\n  panel.postMessage(response);\n}\n\n"]}