{"version": 3, "file": "agentsSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/agentsSettingsSection.ts"], "names": [], "mappings": ";;AA6BA,kEAuBC;AApDD,qCAAqC;AACrC,gDAAqE;AAmBrE,IAAI,MAAM,GAAY,EAAE,CAAC;AACzB,IAAI,eAAe,GAAkB,IAAI,CAAC;AAC1C,MAAM,YAAY,GAA6B,kCAAoB,CAAC,OAAO,CAAC;AAE5E;;;;GAIG;AACH,SAAgB,2BAA2B,CAAC,SAAsB,EAAE,QAAwB;IAC1F,qBAAqB;IACrB,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/D,MAAM,kBAAkB,GAAG,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC;IACzD,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC7B,uBAAuB;IACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;IACtD,IAAI,MAAM;QAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;IAC3F,gBAAgB;IAChB,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IACjE,IAAI,cAAc;QAAE,cAAc,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC7E,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;IAC7D,IAAI,YAAY;QAAE,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC9E,2CAA2C;IAC3C,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,gBAAgB,CAAgB,CAAC;IACzE,IAAI,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACnC,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,0FAA0F,CAAC;QAC5G,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,iBAAiB,CAAC,SAAsB;IAC/C,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,gBAAgB,CAAgB,CAAC;IACzE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO,CAAC,SAAS,GAAG,0GAA0G,CAAC;QAC/H,OAAO;IACT,CAAC;IACD,IAAI,IAAI,GAAG;;;0BAGa,YAAY,CAAC,QAAQ;qBAC1B,YAAY,CAAC,WAAW;;;;;;;;;;yBAUpB,YAAY,CAAC,MAAM,CAAC,UAAU;oBACnC,YAAY,CAAC,MAAM,CAAC,KAAK;qBACxB,YAAY,CAAC,MAAM,CAAC,MAAM;4BACnB,YAAY,CAAC,MAAM,CAAC,YAAY;;;;;;2CAMjB,YAAY,CAAC,MAAM;;;;aAIjD,CAAC;IACZ,IAAI,IAAI,uCAAuC;QACzC,uOAAuO;QACvO,sBAAsB,CAAC;IAC7B,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;QACxB,IAAI,IAAI;kBACM,CAAC,CAAC,IAAI,IAAI,EAAE;kBACZ,CAAC,CAAC,WAAW,IAAI,EAAE;kBACnB,CAAC,CAAC,CAAC,YAAY,IAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,IAAE,EAAE,CAAC,CAAC,MAAM,GAAC,EAAE,CAAA,CAAC,CAAA,KAAK,CAAA,CAAC,CAAA,EAAE;kBAC1E,CAAC,CAAC,QAAQ,IAAE,EAAE;kBACd,CAAC,CAAC,OAAO,IAAE,EAAE;kBACb,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;kBAC3B,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;kBACxB,CAAC,CAAC,CAAC,eAAe,IAAE,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;;mDAEA,GAAG;qDACD,GAAG;;cAE1C,CAAC;IACb,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,kBAAkB,CAAC;IAC3B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IACzB,qCAAqC;IACrC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACtE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAS,cAAc,CAAC,SAAsB,EAAE,QAAwB,EAAE,EAAE,MAAqB,IAAI,EAAE,kBAAkB,GAAG,KAAK;IAC/H,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IACpD,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;IACzD,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAA4B,CAAC;IAClF,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAA4B,CAAC;IAClF,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAA4B,CAAC;IACtF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAA4B,CAAC;IAC1F,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA4B,CAAC;IACxF,MAAM,eAAe,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAA4B,CAAC;IAC9F,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA4B,CAAC;IACxF,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA4B,CAAC;IACxF,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,IAAI,KAAK;QAAE,KAAK,CAAC,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC;IACtE,IAAI,SAAS;QAAE,SAAS,CAAC,KAAK,GAAG,KAAK,EAAE,IAAI,IAAI,EAAE,CAAC;IACnD,IAAI,SAAS;QAAE,SAAS,CAAC,KAAK,GAAG,KAAK,EAAE,WAAW,IAAI,EAAE,CAAC;IAC1D,IAAI,WAAW;QAAE,WAAW,CAAC,KAAK,GAAG,KAAK,EAAE,YAAY,IAAI,EAAE,CAAC;IAC/D,IAAI,aAAa,EAAE,CAAC;QAClB,aAAa,CAAC,KAAK,GAAG,KAAK,EAAE,QAAQ,IAAI,EAAE,CAAC;QAC5C,aAAa,CAAC,QAAQ,GAAG,kBAAkB,KAAK,KAAK,CAAC;IACxD,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,YAAY,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC;QACzD,YAAY,CAAC,QAAQ,GAAG,kBAAkB,KAAK,KAAK,CAAC;IACvD,CAAC;IACD,IAAI,eAAe;QAAE,eAAe,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC;IACnE,IAAI,YAAY;QAAE,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC;IAC1D,IAAI,YAAY;QAAE,YAAY,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,eAAe,IAAE,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9E,eAAe,GAAG,GAAG,CAAC;AACxB,CAAC;AAED;;;GAGG;AACH,SAAS,cAAc,CAAC,SAAsB;IAC5C,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IACpD,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,+CAA+C;IAC/C,MAAM,aAAa,GAAG,SAAS,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;IACtE,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;AAC3C,CAAC;AAED;;;;GAIG;AACH,SAAS,SAAS,CAAC,SAAsB,EAAE,QAAwB;IACjE,qDAAqD;IACrD,MAAM,SAAS,GAAG,SAAS,CAAC,aAAa,CAAmB,YAAY,CAAC,CAAC;IAC1E,MAAM,SAAS,GAAG,SAAS,CAAC,aAAa,CAAmB,YAAY,CAAC,CAAC;IAC1E,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAmB,cAAc,CAAC,CAAC;IAC9E,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAmB,gBAAgB,CAAC,CAAC;IAClF,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAmB,eAAe,CAAC,CAAC;IAChF,MAAM,eAAe,GAAG,SAAS,CAAC,aAAa,CAAmB,kBAAkB,CAAC,CAAC;IACtF,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAmB,eAAe,CAAC,CAAC;IAChF,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAmB,eAAe,CAAC,CAAC;IAEhF,2BAA2B;IAC3B,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;QACtB,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAE,wBAAwB,CAAC,CAAC;QACtE,OAAO;IACT,CAAC;IAED,gDAAgD;IAChD,MAAM,KAAK,GAAU;QACnB,IAAI,EAAE,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC;QACrC,WAAW,EAAE,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC;QAC5C,YAAY,EAAE,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC;QAC/C,QAAQ,EAAE,aAAa,CAAC,aAAa,EAAE,KAAK,CAAC;QAC7C,OAAO,EAAE,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC;QAC3C,UAAU,EAAE,eAAe,EAAE,OAAO,IAAI,KAAK;QAC7C,OAAO,EAAE,YAAY,EAAE,OAAO,IAAI,IAAI;QACtC,eAAe,EAAE,YAAY,EAAE,KAAK;aACjC,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aAClB,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;KACzB,CAAC;IAEF,2CAA2C;IAC3C,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;QAC7B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;SAAM,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QACnE,MAAM,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC;IAClC,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,eAAe,CAAC,CAAC;QACnE,OAAO;IACT,CAAC;IAED,yBAAyB;IACzB,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,yCAAyC;IACxE,cAAc,CAAC,SAAS,CAAC,CAAC;IAC1B,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAE7B,uBAAuB;IACvB,UAAU,CAAC,SAAS,EAAE,UAAU,KAAK,CAAC,IAAI,KAAK,eAAe,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,eAAe,EAAE,SAAS,CAAC,CAAC;AAC3H,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAAC,SAAsB,EAAE,OAAe,EAAE,OAAe;IACnF,2CAA2C;IAC3C,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,OAAO,QAAQ,CAAC,CAAC;IACnE,IAAI,aAAa,EAAE,CAAC;QAClB,aAAa,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;IAED,oCAAoC;IACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACnD,YAAY,CAAC,EAAE,GAAG,GAAG,OAAO,QAAQ,CAAC;IACrC,YAAY,CAAC,SAAS,GAAG,kBAAkB,CAAC;IAC5C,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;IACrC,YAAY,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACvC,YAAY,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC;IACxC,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC;IAEnC,yBAAyB;IACzB,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC;IACrD,IAAI,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;QAC9B,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;IACjE,CAAC;IAED,kBAAkB;IAClB,KAAK,EAAE,cAAc,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC9D,KAAqB,EAAE,KAAK,EAAE,CAAC;AAClC,CAAC;AAED;;;;;GAKG;AACH,SAAS,UAAU,CAAC,SAAsB,EAAE,OAAe,EAAE,OAAqC,MAAM;IACtG,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAc,iBAAiB,CAAC,IAAI,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/G,aAAa,CAAC,EAAE,GAAG,gBAAgB,CAAC;IACpC,aAAa,CAAC,WAAW,GAAG,OAAO,CAAC;IAEpC,qCAAqC;IACrC,MAAM,MAAM,GAAiC;QAC3C,OAAO,EAAE,OAAO;QAChB,OAAO,EAAE,QAAQ;QACjB,MAAM,EAAE,OAAO;QACf,YAAY,EAAE,KAAK;QACnB,eAAe,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAChC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;QACzD,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC9B,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;QACjD,MAAM,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC;YACzC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,mBAAmB;KACtE,CAAC;IAEF,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAE3C,8CAA8C;IAC9C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;QAC9B,MAAM,IAAI,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,EAAE,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED,4BAA4B;IAC5B,UAAU,CAAC,GAAG,EAAE;QACd,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;YACrC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QACpC,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC,EAAE,IAAI,CAAC,CAAC;AACX,CAAC;AAED,+CAAgD;AAEhD;;;;GAIG;AACH,SAAS,WAAW,CAAC,SAAsB,EAAE,GAAW;IACtD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,YAAY,CAAC;IACpD,IAAA,iBAAS,EAAC;QACR,KAAK,EAAE,cAAc;QACrB,OAAO,EAAE,0DAA0D,SAAS,kCAAkC;QAC9G,SAAS,EAAE,GAAG,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACtB,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;YACzB,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,aAAa,CAAC,GAA8B;IACnD,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,CAAC", "sourcesContent": ["// Agents section logic and rendering\nimport { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';\n\nexport interface Agent {\n    name: string;\n    description: string;\n    systemPrompt: string;\n    provider: string;\n    modelId: string;\n    supervisor: boolean;\n    enabled: boolean;\n    chainedAgentIds: string[];\n}\n\ninterface AgentsSettings {\n    agents: Agent[];\n    perAgentLLMEnabled?: boolean;\n    [key: string]: unknown; // Allow additional properties\n}\n\nlet agents: Agent[] = [];\nlet editingAgentIdx: number | null = null;\nconst sectionTheme: UIThemeConfig['section'] = defaultUIThemeConfig.section;\n\n/**\n * Renders the Agents settings section in the settings UI\n * @param container - The HTMLElement where the section should be rendered\n * @param settings - An object containing the current agents settings\n */\nexport function renderAgentsSettingsSection(container: HTMLElement, settings: AgentsSettings): void {\n  // Sync from settings\n  agents = Array.isArray(settings.agents) ? settings.agents : [];\n  const perAgentLLMEnabled = !!settings.perAgentLLMEnabled;\n  renderAgentsTable(container);\n  // Add button listeners\n  const addBtn = document.getElementById('addAgentBtn');\n  if (addBtn) addBtn.onclick = () => showAgentModal(container, {}, null, perAgentLLMEnabled);\n  // Modal buttons\n  const cancelAgentBtn = document.getElementById('cancelAgentBtn');\n  if (cancelAgentBtn) cancelAgentBtn.onclick = () => hideAgentModal(container);\n  const saveAgentBtn = document.getElementById('saveAgentBtn');\n  if (saveAgentBtn) saveAgentBtn.onclick = () => saveAgent(container, settings);\n  // Show a hint if per-agent LLM is disabled\n  const section = container.querySelector('#agentsSection') as HTMLElement;\n  if (section && !perAgentLLMEnabled) {\n    const hint = document.createElement('div');\n    hint.style.color = '#c2410c';\n    hint.style.fontSize = '1em';\n    hint.style.margin = '12px 0';\n    hint.innerText = 'Per-agent LLM/model configuration is disabled. All agents use the global provider/model.';\n    section.prepend(hint);\n  }\n}\n\n/**\n * Renders the agents table in the settings UI\n * @param container - The HTMLElement where the table should be rendered\n */\nfunction renderAgentsTable(container: HTMLElement): void {\n  const section = container.querySelector('#agentsSection') as HTMLElement;\n  if (!agents || agents.length === 0) {\n    section.innerHTML = '<div style=\"color:#aaa;font-size:1.1em;padding:24px 0;text-align:center;\">🧑‍💻 No agents defined.</div>';\n    return;\n  }\n  let html = `<style>\n        .crud-table th, .crud-table td { padding: 6px 10px; }\n        .crud-table th {\n            background: ${sectionTheme.headerBg};\n            color: ${sectionTheme.headerColor};\n            font-weight: 600;\n        }\n        .crud-table tbody tr:nth-child(even) { background: #fafbfc; }\n        .crud-table tbody tr:hover { background: #e8f0fe; }\n        .agent-badge { display:inline-block; padding:2px 8px; border-radius:8px; font-size:0.9em; margin-right:2px; }\n        .badge-enabled { background: #d1fae5; color: #059669; }\n        .badge-disabled { background: #fee2e2; color: #b91c1c; }\n        .badge-supervisor { background: #fef9c3; color: #b45309; }\n        .btn-agent {\n            background:${sectionTheme.button.background};\n            color:${sectionTheme.button.color};\n            border:${sectionTheme.button.border};\n            border-radius:${sectionTheme.button.borderRadius};\n            padding:3px 10px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s;\n        }\n        .btn-agent:hover { background:#1d4ed8; }\n        .btn-agent[disabled] { background:#e5e7eb; color:#888; cursor:not-allowed; }\n        .agent-action-icons { font-size:1.1em; cursor:pointer; margin:0 2px; }\n        .agent-action-icons.edit { color:${sectionTheme.accent}; }\n        .agent-action-icons.delete { color:#b91c1c; }\n        .agent-action-icons.edit:hover { color:#1d4ed8; }\n        .agent-action-icons.delete:hover { color:#dc2626; }\n    </style>`;\n  html += '<table class=\"crud-table\"><thead><tr>' +\n        '<th>👤 Name</th><th>📝 Description</th><th title=\"System Prompt\">💬 Prompt</th><th>🔗 Provider</th><th>🤖 Model ID</th><th title=\"Supervisor Agent\">🦸 Supervisor</th><th>⚡ Enabled</th><th>🔗 Chained Agents</th><th>⚙️ Actions</th>' +\n        '</tr></thead><tbody>';\n  agents.forEach((a, idx) => {\n    html += `<tr>\n            <td>${a.name || ''}</td>\n            <td>${a.description || ''}</td>\n            <td>${(a.systemPrompt||'').slice(0,40)}${(a.systemPrompt||'').length>40?'...':''}</td>\n            <td>${a.provider||''}</td>\n            <td>${a.modelId||''}</td>\n            <td>${a.supervisor ? 'Yes' : 'No'}</td>\n            <td>${a.enabled ? 'Yes' : 'No'}</td>\n            <td>${(a.chainedAgentIds||[]).join(',')}</td>\n            <td>\n                <button type=\"button\" data-edit=\"${idx}\">Edit</button>\n                <button type=\"button\" data-delete=\"${idx}\">Delete</button>\n            </td>\n        </tr>`;\n  });\n  html += '</tbody></table>';\n  section.innerHTML = html;\n  // Attach edit/delete event listeners\n  section.querySelectorAll('button[data-edit]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-edit');\n      const idx = parseInt(safeGetString(idxAttr));\n      showAgentModal(container, agents[idx], idx);\n    });\n  });\n  section.querySelectorAll('button[data-delete]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-delete');\n      const idx = parseInt(safeGetString(idxAttr));\n      deleteAgent(container, idx);\n    });\n  });\n}\n\n/**\n * Shows the agent modal for adding/editing an agent\n * @param container - The container element\n * @param agent - The agent data to edit, or an empty object for a new agent\n * @param idx - The index of the agent being edited, or null for a new agent\n * @param perAgentLLMEnabled - Whether per-agent LLM configuration is enabled\n */\nfunction showAgentModal(container: HTMLElement, agent: Partial<Agent> = {}, idx: number | null = null, perAgentLLMEnabled = false): void {\n  const modal = document.getElementById('agentModal');\n  const title = document.getElementById('agentModalTitle');\n  const nameInput = document.getElementById('agentName') as HTMLInputElement | null;\n  const descInput = document.getElementById('agentDesc') as HTMLInputElement | null;\n  const promptInput = document.getElementById('agentPrompt') as HTMLInputElement | null;\n  const providerInput = document.getElementById('agentProvider') as HTMLInputElement | null;\n  const modelIdInput = document.getElementById('agentModelId') as HTMLInputElement | null;\n  const supervisorInput = document.getElementById('agentSupervisor') as HTMLInputElement | null;\n  const enabledInput = document.getElementById('agentEnabled') as HTMLInputElement | null;\n  const chainedInput = document.getElementById('agentChained') as HTMLInputElement | null;\n  if (modal) modal.style.display = 'flex';\n  if (title) title.innerText = idx == null ? 'Add Agent' : 'Edit Agent';\n  if (nameInput) nameInput.value = agent?.name || '';\n  if (descInput) descInput.value = agent?.description || '';\n  if (promptInput) promptInput.value = agent?.systemPrompt || '';\n  if (providerInput) {\n    providerInput.value = agent?.provider || '';\n    providerInput.disabled = perAgentLLMEnabled === false;\n  }\n  if (modelIdInput) {\n    modelIdInput.value = safeGetString(agent?.modelId) || '';\n    modelIdInput.disabled = perAgentLLMEnabled === false;\n  }\n  if (supervisorInput) supervisorInput.checked = !!agent?.supervisor;\n  if (enabledInput) enabledInput.checked = !!agent?.enabled;\n  if (chainedInput) chainedInput.value = (agent?.chainedAgentIds||[]).join(',');\n  editingAgentIdx = idx;\n}\n\n/**\n * Hides the agent modal\n * @param container - The container element\n */\nfunction hideAgentModal(container: HTMLElement): void {\n  const modal = document.getElementById('agentModal');\n  if (modal) modal.style.display = 'none';\n  // Clear any validation errors in the container\n  const errorElements = container.querySelectorAll('.validation-error');\n  errorElements.forEach(el => el.remove());\n}\n\n/**\n * Saves the agent data from the modal\n * @param container - The container element\n * @param settings - The current settings object\n */\nfunction saveAgent(container: HTMLElement, settings: AgentsSettings): void {\n  // Get all input elements with proper type assertions\n  const nameInput = container.querySelector<HTMLInputElement>('#agentName');\n  const descInput = container.querySelector<HTMLInputElement>('#agentDesc');\n  const promptInput = container.querySelector<HTMLInputElement>('#agentPrompt');\n  const providerInput = container.querySelector<HTMLInputElement>('#agentProvider');\n  const modelIdInput = container.querySelector<HTMLInputElement>('#agentModelId');\n  const supervisorInput = container.querySelector<HTMLInputElement>('#agentSupervisor');\n  const enabledInput = container.querySelector<HTMLInputElement>('#agentEnabled');\n  const chainedInput = container.querySelector<HTMLInputElement>('#agentChained');\n  \n  // Validate required inputs\n  if (!nameInput?.value) {\n    showValidationError(container, 'agentName', 'Agent name is required');\n    return;\n  }\n  \n  // Create agent object with proper type checking\n  const agent: Agent = {\n    name: safeGetString(nameInput?.value),\n    description: safeGetString(descInput?.value),\n    systemPrompt: safeGetString(promptInput?.value),\n    provider: safeGetString(providerInput?.value),\n    modelId: safeGetString(modelIdInput?.value),\n    supervisor: supervisorInput?.checked ?? false,\n    enabled: enabledInput?.checked ?? true,\n    chainedAgentIds: chainedInput?.value\n      .split(',')\n      .map(s => s.trim())\n      .filter(Boolean) || []\n  };\n  \n  // Update agents array based on edit or add\n  if (editingAgentIdx === null) {\n    agents.push(agent);\n  } else if (editingAgentIdx >= 0 && editingAgentIdx < agents.length) {\n    agents[editingAgentIdx] = agent;\n  } else {\n    console.error('Invalid agent index for editing:', editingAgentIdx);\n    return;\n  }\n  \n  // Update settings and UI\n  settings.agents = [...agents]; // Create new array to trigger reactivity\n  hideAgentModal(container);\n  renderAgentsTable(container);\n  \n  // Show success message\n  showStatus(container, `Agent \"${agent.name}\" ${editingAgentIdx === null ? 'added' : 'updated'} successfully`, 'success');\n}\n\n/**\n * Shows a validation error message for a form field\n * @param container - The container element\n * @param fieldId - The ID of the field with the error\n * @param message - The error message to display\n */\nfunction showValidationError(container: HTMLElement, fieldId: string, message: string): void {\n  // Remove any existing error for this field\n  const existingError = container.querySelector(`#${fieldId}-error`);\n  if (existingError) {\n    existingError.remove();\n  }\n  \n  // Create and show new error message\n  const errorElement = document.createElement('div');\n  errorElement.id = `${fieldId}-error`;\n  errorElement.className = 'validation-error';\n  errorElement.style.color = '#dc3545';\n  errorElement.style.fontSize = '0.85em';\n  errorElement.style.marginTop = '0.25em';\n  errorElement.textContent = message;\n  \n  // Insert after the field\n  const field = container.querySelector(`#${fieldId}`);\n  if (field && field.parentNode) {\n    field.parentNode.insertBefore(errorElement, field.nextSibling);\n  }\n  \n  // Focus the field\n  field?.scrollIntoView({ behavior: 'smooth', block: 'center' });\n  (field as HTMLElement)?.focus();\n}\n\n/**\n * Shows a status message in the UI\n * @param container - The container element\n * @param message - The message to display\n * @param type - The type of message (success, error, or info)\n */\nfunction showStatus(container: HTMLElement, message: string, type: 'success' | 'error' | 'info' = 'info'): void {\n  const statusElement = container.querySelector<HTMLElement>('#status-message') || document.createElement('div');\n  statusElement.id = 'status-message';\n  statusElement.textContent = message;\n  \n  // Apply styles based on message type\n  const styles: Partial<CSSStyleDeclaration> = {\n    display: 'block',\n    padding: '0.75em',\n    margin: '1em 0',\n    borderRadius: '4px',\n    backgroundColor: type === 'error' ? '#f8d7da' : \n                   type === 'success' ? '#d4edda' : '#d1ecf1',\n    color: type === 'error' ? '#721c24' : \n           type === 'success' ? '#155724' : '#0c5460',\n    border: type === 'error' ? '1px solid #f5c6cb' : \n           type === 'success' ? '1px solid #c3e6cb' : '1px solid #bee5eb'\n  };\n  \n  Object.assign(statusElement.style, styles);\n  \n  // Insert status message if not already in DOM\n  if (!statusElement.parentNode) {\n    const form = container.querySelector('form');\n    form?.insertBefore(statusElement, form.firstChild);\n  }\n  \n  // Auto-hide after 5 seconds\n  setTimeout(() => {\n    statusElement.style.opacity = '0';\n    setTimeout(() => {\n      statusElement.style.display = 'none';\n      statusElement.style.opacity = '1';\n    }, 300);\n  }, 5000);\n}\n\nimport { showModal } from '../components/modal';\n\n/**\n * Deletes an agent after confirmation\n * @param container - The container element\n * @param idx - The index of the agent to delete\n */\nfunction deleteAgent(container: HTMLElement, idx: number): void {\n  const agentName = agents[idx]?.name || 'this agent';\n  showModal({\n    title: 'Delete Agent',\n    content: `Are you sure you want to permanently delete the agent \"${agentName}\"? This action cannot be undone.`,\n    onConfirm: () => {\n      agents.splice(idx, 1);\n      const settings = (window as any).settings || {};\n      settings.agents = agents;\n      renderAgentsTable(container);\n    }\n  });\n}\n\n/**\n * Safely gets a string value, returning an empty string if null or undefined\n * @param val - The value to check\n * @returns The string value or an empty string\n */\nfunction safeGetString(val: string | null | undefined): string {\n  return typeof val === 'string' ? val : '';\n}\n"]}