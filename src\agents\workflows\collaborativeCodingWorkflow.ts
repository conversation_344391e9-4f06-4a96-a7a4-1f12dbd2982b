/**
 * Collaborative Coding Workflow
 *
 * This module provides a workflow for collaborative coding:
 * - Coordinating multiple developers
 * - Sharing context between team members
 * - Managing collaborative coding sessions
 */

import { ITool } from '../../tools/tool.ts.backup';
import { Agent } from '../agentUtilities/agent';
import { <PERSON>ssa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge } from './types';
import { workflowRegistry } from './workflowRegistry';
import { logger } from '../../logger';
import { StructuredTool } from './corePolyfill';

/**
 * Create a Collaborative Coding workflow
 */
export function createCollaborativeCodingWorkflow(
  id: string,
  name: string,
  description: string,
  coordinatorAgent: Agent,
  developerAgent: Agent,
  reviewerAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  logger.info(`Creating Collaborative Coding workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const sessionInitializationNode = Codessa.createAgentNode('session-initialization', 'Session Initialization', coordinatorAgent);
  const taskDistributionNode = Codessa.createAgentNode('task-distribution', 'Task Distribution', coordinatorAgent);
  const contextSharingNode = Codessa.createAgentNode('context-sharing', 'Context Sharing', coordinatorAgent);
  const codingNode = Codessa.createAgentNode('coding', 'Coding', developerAgent);
  const codeReviewNode = Codessa.createAgentNode('code-review', 'Code Review', reviewerAgent);
  const conflictResolutionNode = Codessa.createAgentNode('conflict-resolution', 'Conflict Resolution', coordinatorAgent);
  const integrationNode = Codessa.createAgentNode('integration', 'Integration', developerAgent);
  const sessionSummaryNode = Codessa.createAgentNode('session-summary', 'Session Summary', coordinatorAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-initialization', source: 'input', target: 'session-initialization', type: 'default' },
    { name: 'initialization-to-distribution', source: 'session-initialization', target: 'task-distribution', type: 'default' },
    { name: 'distribution-to-sharing', source: 'task-distribution', target: 'context-sharing', type: 'default' },
    { name: 'sharing-to-coding', source: 'context-sharing', target: 'coding', type: 'default' },
    { name: 'coding-to-review', source: 'coding', target: 'code-review', type: 'default' },
    { name: 'review-to-resolution', source: 'code-review', target: 'conflict-resolution', type: 'default' },
    { name: 'resolution-to-integration', source: 'conflict-resolution', target: 'integration', type: 'default' },
    { name: 'integration-to-summary', source: 'integration', target: 'session-summary', type: 'default' },
    { name: 'summary-to-output', source: 'session-summary', target: 'output', type: 'default' },

    // Feedback loops
    { name: 'review-to-coding', source: 'code-review', target: 'coding', type: 'feedback' },
    { name: 'resolution-to-review', source: 'conflict-resolution', target: 'code-review', type: 'feedback' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect coding to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `coding-to-tool-${index}`,
        source: 'coding',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to code review
      edges.push({
        name: `tool-${index}-to-review`,
        source: `tool-${index}`,
        target: 'code-review',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      sessionInitializationNode,
      taskDistributionNode,
      contextSharingNode,
      codingNode,
      codeReviewNode,
      conflictResolutionNode,
      integrationNode,
      sessionSummaryNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'multi-agent',
    tags: ['collaborative', 'team', 'coding-session']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a specialized Pair Programming workflow
 */
export function createPairProgrammingWorkflow(
  id: string,
  name: string,
  description: string,
  driverAgent: Agent,
  navigatorAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  logger.info(`Creating Pair Programming workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const taskAnalysisNode = Codessa.createAgentNode('task-analysis', 'Task Analysis', navigatorAgent);
  const planningNode = Codessa.createAgentNode('planning', 'Planning', navigatorAgent);
  const codingNode = Codessa.createAgentNode('coding', 'Coding', driverAgent);
  const codeReviewNode = Codessa.createAgentNode('code-review', 'Code Review', navigatorAgent);
  const testingNode = Codessa.createAgentNode('testing', 'Testing', driverAgent);
  const refactoringNode = Codessa.createAgentNode('refactoring', 'Refactoring', driverAgent);
  const roleSwitchingNode = Codessa.createAgentNode('role-switching', 'Role Switching', navigatorAgent);
  const sessionSummaryNode = Codessa.createAgentNode('session-summary', 'Session Summary', navigatorAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-analysis', source: 'input', target: 'task-analysis', type: 'default' },
    { name: 'analysis-to-planning', source: 'task-analysis', target: 'planning', type: 'default' },
    { name: 'planning-to-coding', source: 'planning', target: 'coding', type: 'default' },
    { name: 'coding-to-review', source: 'coding', target: 'code-review', type: 'default' },
    { name: 'review-to-testing', source: 'code-review', target: 'testing', type: 'default' },
    { name: 'testing-to-refactoring', source: 'testing', target: 'refactoring', type: 'default' },
    { name: 'refactoring-to-switching', source: 'refactoring', target: 'role-switching', type: 'default' },
    { name: 'switching-to-summary', source: 'role-switching', target: 'session-summary', type: 'default' },
    { name: 'summary-to-output', source: 'session-summary', target: 'output', type: 'default' },

    // Feedback loops
    { name: 'review-to-coding', source: 'code-review', target: 'coding', type: 'feedback' },
    { name: 'testing-to-coding', source: 'testing', target: 'coding', type: 'feedback' },
    { name: 'switching-to-planning', source: 'role-switching', target: 'planning', type: 'feedback' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect coding to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `coding-to-tool-${index}`,
        source: 'coding',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to code review
      edges.push({
        name: `tool-${index}-to-review`,
        source: `tool-${index}`,
        target: 'code-review',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      taskAnalysisNode,
      planningNode,
      codingNode,
      codeReviewNode,
      testingNode,
      refactoringNode,
      roleSwitchingNode,
      sessionSummaryNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'multi-agent',
    tags: ['pair-programming', 'xp', 'collaborative']
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}
