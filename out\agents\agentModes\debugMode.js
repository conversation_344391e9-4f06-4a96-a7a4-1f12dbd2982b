"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebugMode = void 0;
const operationMode_1 = require("./operationMode");
const logger_1 = require("../../logger");
const contextManager_1 = require("./contextManager");
const promptManager_1 = require("../../prompts/promptManager");
/**
 * Debug Mode - Uses specified files as context for debugging
 */
class DebugMode extends operationMode_1.OperationMode {
    id = 'debug';
    displayName = 'Debug';
    description = 'Debug issues with your code';
    icon = '$(bug)';
    defaultContextType = operationMode_1.ContextType.SELECTED_FILES;
    requiresHumanVerification = false;
    supportsMultipleAgents = false;
    /**
       * Process a user message in Debug mode
       */
    async processMessage(message, agent, contextSource, additionalParams) {
        try {
            logger_1.Logger.instance.info(`Processing message in Debug mode: ${message}`);
            // Get context content
            const contextContent = await contextManager_1.contextManager.getContextContent(contextSource);
            // Add memory context if available
            let memoryContext = '';
            try {
                const agentMemory = agent.getMemory();
                if (agentMemory) {
                    const relevantMemories = await agentMemory.getRelevantMemories(message);
                    if (relevantMemories && relevantMemories.length > 0) {
                        memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
                        logger_1.Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to debug context`);
                    }
                }
            }
            catch (memoryError) {
                logger_1.Logger.instance.warn('Failed to retrieve memory context for debug:', memoryError);
                // Continue without memory context
            }
            // Prepare the prompt using promptManager
            const errorMessage = additionalParams?.errorMessage ? `Error message: ${additionalParams.errorMessage}` : '';
            const stackTrace = additionalParams?.stackTrace ? `Stack trace: ${additionalParams.stackTrace}` : '';
            const prompt = promptManager_1.promptManager.renderPrompt('mode.debug', {
                contextContent,
                memoryContext,
                message,
                errorMessage,
                stackTrace
            });
            // Generate response using the agent
            const response = await agent.generate(prompt, this.getLLMParams());
            // Store the debugging session in memory
            try {
                const agentMemory = agent.getMemory();
                if (agentMemory) {
                    await agentMemory.addMessage('user', `Debug issue: ${message}`);
                    await agentMemory.addMessage('assistant', response);
                }
            }
            catch (memoryError) {
                logger_1.Logger.instance.warn('Failed to store debug session in memory:', memoryError);
                // Continue without storing in memory
            }
            return response;
        }
        catch (error) {
            logger_1.Logger.instance.error('Error processing message in Debug mode:', error);
            return `Error processing your debug request: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
    /**
       * Get LLM parameters specific to Debug mode
       */
    getLLMParams() {
        return {
            prompt: '',
            modelId: '',
            temperature: 0.3, // Lower temperature for more precise debugging
            maxTokens: 2000, // Longer responses for detailed analysis
            mode: 'task'
        };
    }
    /**
       * Get the system prompt for Debug mode
       */
    async getSystemPrompt(
    // @ts-ignore - Parameter required by interface but not used in this implementation
    agent, 
    // @ts-ignore - Parameter required by interface but not used in this implementation
    contextSource) {
        return `
You are an AI assistant specialized in debugging code.
Your task is to help the user identify and fix issues in their code.
Analyze the code carefully, identify potential problems, and suggest solutions.
Be methodical in your approach:
1. Understand the error or issue description
2. Analyze the relevant code
3. Identify potential causes
4. Suggest specific fixes with code examples
5. Explain why the issue occurred and how the fix resolves it

When suggesting fixes, provide complete code snippets that the user can directly implement.
Include line numbers and file names when referencing specific parts of the code.
`;
    }
    /**
       * Get UI components specific to Debug mode
       */
    getUIComponents() {
        return {
            contextPanel: `
<div class="context-panel">
    <div class="context-header">
        <h3>Debug Context</h3>
        <div class="context-controls">
            <button id="btn-refresh-context" title="Refresh Context"><i class="codicon codicon-refresh"></i></button>
            <button id="btn-select-files" title="Select Files"><i class="codicon codicon-file-code"></i></button>
            <button id="btn-select-folders" title="Select Folders"><i class="codicon codicon-folder"></i></button>
        </div>
    </div>
    <div class="context-type">
        <select id="context-type-selector">
            <option value="selected_files">Selected Files</option>
            <option value="current_file">Current File</option>
            <option value="custom">Custom</option>
        </select>
    </div>
    <div id="context-files-list" class="context-files-list"></div>
    <div class="error-info">
        <h4>Error Information</h4>
        <textarea id="error-message" placeholder="Paste error message here..."></textarea>
        <textarea id="stack-trace" placeholder="Paste stack trace here..."></textarea>
    </div>
</div>
`,
            messageInput: `
<div class="message-input-container">
    <textarea id="message-input" placeholder="Describe the issue you're experiencing..."></textarea>
    <button id="btn-send" title="Send"><i class="codicon codicon-send"></i></button>
</div>
`
        };
    }
}
exports.DebugMode = DebugMode;
//# sourceMappingURL=debugMode.js.map