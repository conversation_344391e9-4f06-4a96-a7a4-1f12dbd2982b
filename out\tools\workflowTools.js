"use strict";
/**
 * Workflow Tools
 *
 * This module provides tools for use in workflows.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCodeAnalysisTool = createCodeAnalysisTool;
exports.createTestingTool = createTestingTool;
exports.createDocumentationTool = createDocumentationTool;
exports.createDeploymentTool = createDeploymentTool;
exports.createMonitoringTool = createMonitoringTool;
exports.createCI_CDTool = createCI_CDTool;
const corePolyfill_1 = require("../agents/workflows/corePolyfill");
// @ts-ignore
const zod_1 = require("zod");
const logger_1 = require("../logger");
const codeAnalysisTool_1 = require("./codeAnalysisTool");
const lintDiagnosticsTool_1 = require("./lintDiagnosticsTool");
const toolRegistry_1 = require("./toolRegistry");
const docsTool_1 = require("./docsTool");
/**
 * Wrapper class to adapt ITool to StructuredTool
 */
class ToolWrapper extends corePolyfill_1.StructuredTool {
    tool;
    actionName;
    constructor(tool, actionName) {
        super();
        this.tool = tool;
        this.actionName = actionName;
        // Set name and description
        Object.defineProperty(this, 'name', {
            value: actionName ? `${tool.name}-${actionName}` : tool.name
        });
        Object.defineProperty(this, 'description', {
            value: actionName && tool.actions && tool.actions[actionName]
                ? tool.actions[actionName].description
                : tool.description
        });
        // Set schema based on tool type and action
        if (tool.type === 'multi-action' && actionName && tool.actions && tool.actions[actionName]) {
            Object.defineProperty(this, 'schema', {
                value: tool.actions[actionName].inputSchema || zod_1.z.object({}).passthrough()
            });
        }
        else if (tool.type === 'single-action' && tool.singleActionSchema) {
            Object.defineProperty(this, 'schema', {
                value: tool.singleActionSchema
            });
        }
        else {
            Object.defineProperty(this, 'schema', {
                value: zod_1.z.object({}).passthrough()
            });
        }
    }
    async _call(input) {
        try {
            const result = await this.tool.execute(this.actionName, input);
            if (!result.success) {
                return `Error: ${result.error || 'Unknown error'}`;
            }
            // Format the output as a string
            if (typeof result.output === 'string') {
                return result.output;
            }
            else if (result.output === undefined || result.output === null) {
                return 'Operation completed successfully.';
            }
            else {
                return JSON.stringify(result.output, null, 2);
            }
        }
        catch (error) {
            return `Error executing tool: ${error.message || error}`;
        }
    }
}
/**
 * Create a code analysis tool that integrates with the existing CodeComplexityTool
 */
function createCodeAnalysisTool() {
    // Try to get from registry first
    const registryTool = toolRegistry_1.ToolRegistry.instance.getTool('codeComplexity');
    if (registryTool) {
        return new ToolWrapper(registryTool);
    }
    // Create a new instance if not in registry
    return new ToolWrapper(new codeAnalysisTool_1.CodeComplexityTool());
}
/**
 * Create a testing tool that integrates with the existing lintDiagnosticsTool
 */
function createTestingTool() {
    // Try to get from registry first
    const registryTool = toolRegistry_1.ToolRegistry.instance.getTool('lint_diagnostics');
    if (registryTool) {
        return new ToolWrapper(registryTool, 'runTests');
    }
    // Create a new instance if not in registry
    return new ToolWrapper(lintDiagnosticsTool_1.lintDiagnosticsTool, 'runTests');
}
/**
 * Create a documentation tool that integrates with the existing documentationTool
 */
function createDocumentationTool() {
    // Try to get from registry first
    const registryTool = toolRegistry_1.ToolRegistry.instance.getTool('documentation');
    if (registryTool) {
        return new ToolWrapper(registryTool);
    }
    // Create a new instance if not in registry
    return new ToolWrapper(docsTool_1.documentationTool);
}
/**
 * Create a deployment tool for web applications
 */
function createDeploymentTool() {
    // Try to get from registry first
    const registryTool = toolRegistry_1.ToolRegistry.instance.getTool('deployWebApp');
    if (registryTool) {
        return new ToolWrapper(registryTool);
    }
    // If not available, create a basic implementation
    class DeploymentTool extends corePolyfill_1.StructuredTool {
        name = 'deployment';
        description = 'Deploys applications to various environments';
        schema = zod_1.z.object({
            target: zod_1.z.string().describe('Deployment target (e.g., "dev", "staging", "production")'),
            path: zod_1.z.string().describe('Path to the application to deploy'),
            options: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional().describe('Additional deployment options')
        });
        async _call(input) {
            const { target, path } = input;
            // Log the deployment request
            logger_1.logger.info(`Deployment requested for ${path} to ${target}`);
            // Simulate deployment process
            await new Promise(resolve => setTimeout(resolve, 1000));
            return `Deployment of ${path} to ${target} environment simulated successfully.`;
        }
    }
    return new DeploymentTool();
}
/**
 * Create a monitoring tool for applications
 */
function createMonitoringTool() {
    class MonitoringTool extends corePolyfill_1.StructuredTool {
        name = 'monitoring';
        description = 'Monitors application health and performance';
        schema = zod_1.z.object({
            target: zod_1.z.string().describe('Monitoring target (e.g., "app", "server", "database")'),
            metrics: zod_1.z.array(zod_1.z.string()).optional().describe('Metrics to monitor'),
            duration: zod_1.z.number().optional().describe('Monitoring duration in seconds')
        });
        async _call(input) {
            const { target, metrics = ['cpu', 'memory', 'requests'], duration = 10 } = input;
            // Log the monitoring request
            logger_1.logger.info(`Monitoring requested for ${target}, metrics: ${metrics.join(', ')}, duration: ${duration}s`);
            // Simulate monitoring process
            await new Promise(resolve => setTimeout(resolve, Math.min(duration * 100, 2000)));
            // Generate simulated metrics
            const results = {};
            metrics.forEach((metric) => {
                switch (metric) {
                    case 'cpu':
                        results.cpu = { usage: Math.round(Math.random() * 100), unit: '%' };
                        break;
                    case 'memory':
                        results.memory = { usage: Math.round(Math.random() * 1024), unit: 'MB' };
                        break;
                    case 'requests':
                        results.requests = { count: Math.round(Math.random() * 1000), avgResponseTime: Math.round(Math.random() * 500) };
                        break;
                    default:
                        results[metric] = { value: Math.round(Math.random() * 100) };
                }
            });
            return `Monitoring results for ${target}:\n${JSON.stringify(results, null, 2)}`;
        }
    }
    return new MonitoringTool();
}
/**
 * Create a CI/CD tool for continuous integration and deployment
 */
function createCI_CDTool() {
    // Try to get git tool from registry first
    const gitToolInstance = toolRegistry_1.ToolRegistry.instance.getTool('git');
    class CICDTool extends corePolyfill_1.StructuredTool {
        name = 'ci-cd';
        description = 'Manages continuous integration and deployment pipelines';
        schema = zod_1.z.object({
            action: zod_1.z.enum(['build', 'test', 'deploy', 'status']).describe('CI/CD action to perform'),
            target: zod_1.z.string().optional().describe('Target environment or branch'),
            options: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional().describe('Additional options')
        });
        async _call(input) {
            const { action, target = 'main' } = input;
            // Log the CI/CD request
            logger_1.logger.info(`CI/CD ${action} requested for ${target}`);
            switch (action) {
                case 'build':
                    // Simulate build process
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    return `Build completed for ${target}. Build ID: ${Date.now()}`;
                case 'test':
                    // Simulate test process
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return `Tests passed for ${target}. Coverage: 87%`;
                case 'deploy':
                    // Simulate deployment process
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    return `Deployment to ${target} completed successfully. Deployment ID: ${Date.now()}`;
                case 'status':
                    // If git tool is available, try to get real status
                    if (gitToolInstance) {
                        try {
                            const result = await gitToolInstance.execute('status', {});
                            if (result.success) {
                                return `CI/CD Status for ${target}:\n${JSON.stringify(result.output, null, 2)}`;
                            }
                        }
                        catch (error) {
                            // Fall back to simulated status
                        }
                    }
                    // Simulated status
                    return `CI/CD Status for ${target}:\n` +
                        '- Last build: Successful (2 hours ago)\n' +
                        '- Last test run: Passed (2 hours ago)\n' +
                        '- Last deployment: Successful (1 hour ago)\n' +
                        '- Current status: Healthy';
                default:
                    return `Unknown CI/CD action: ${action}`;
            }
        }
    }
    return new CICDTool();
}
//# sourceMappingURL=workflowTools.js.map