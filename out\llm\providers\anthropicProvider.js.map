{"version": 3, "file": "anthropicProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/anthropicProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uDAAoD;AAGpD,8EAA8E;AAC9E,yCAAsC;AACtC,+CAAiC;AAEjC,4DAAkE;AAElE,MAAa,iBAAkB,SAAQ,iCAAe;IAC3C,UAAU,GAAG,WAAW,CAAC;IACzB,WAAW,GAAG,WAAW,CAAC;IAC1B,WAAW,GAAG,4BAA4B,CAAC;IAC3C,OAAO,GAAG,uBAAuB,CAAC;IAClC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,KAAK,CAAC;IACtC,eAAe,GAAG,2BAA2B,CAAC;IAC9C,YAAY,GAAG,wBAAwB,CAAC;IAEzC,MAAM,GAAe,IAAI,CAAC;IAElC,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,EAAE,CAAC;gBAC1D,eAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBACxE,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,sCAAsC;QACtC,IAAI,CAAC,aAAS,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;YAClG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAElC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,GAAG,IAAI,aAAS,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YACxC,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,0FAA0F;aAClG,CAAC;QACJ,CAAC;QAED,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;YAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,mBAAmB,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC;QAChF,CAAC;QAED,IAAI,CAAC;YACH,mBAAmB;YACnB,MAAM,QAAQ,GAAmB,EAAE,CAAC;YAEpC,oCAAoC;YACpC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC1B,SAAS,CAAC,uEAAuE;oBACnF,CAAC;oBAED,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACpD,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,GAAG,CAAC,IAA4B;4BACtC,OAAO,EAAE,GAAG,CAAC,OAAO;yBACrB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,wCAAwC;gBACxC,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;iBACvB,CAAC,CAAC;YACL,CAAC;YAED,kDAAkD;YAClD,MAAM,cAAc,GAAW,EAAE,CAAC;YAClC,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC5B,0EAA0E;gBAC1E,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAChD,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,WAAW,EAAE,CAAC;oBACzC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;wBACjB,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;4BAC9D,MAAM,WAAW,GAAG,MAAa,CAAC,CAAC,oCAAoC;4BACvE,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC;gCAC3B,cAAc,CAAC,IAAI,CAAC;oCAClB,QAAQ,EAAE;wCACR,IAAI,EAAE,GAAG,MAAM,IAAI,QAAQ,EAAE;wCAC7B,WAAW,EAAE,WAAW,CAAC,WAAW,IAAI,GAAG,MAAM,IAAI,QAAQ,SAAS;wCACtE,UAAU,EAAE,WAAW,CAAC,UAAU;qCACnC;iCACF,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,gCAAgC;YAChC,MAAM,iBAAiB,GAAG;gBACxB,KAAK,EAAE,MAAM,CAAC,OAAO,IAAI,wBAAwB;gBACjD,QAAQ;gBACR,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACpC,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,MAAM,EAAE,MAAM,CAAC,YAAY;aAC5B,CAAC;YAEF,yBAAyB;YACzB,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,iBAAyB,CAAC,KAAK,GAAG,cAAc,CAAC;YACpD,CAAC;YAED,oBAAoB;YACpB,eAAM,CAAC,KAAK,CAAC,4CAA4C,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC;YACpF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAEtE,uBAAuB;YACvB,IAAI,eAA4C,CAAC;YACjD,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC1B,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC;oBACxB,CAAC;yBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,EAAE,CAAC;wBAC9C,oBAAoB;wBACpB,IAAI,CAAC;4BACH,sCAAsC;4BACtC,MAAM,YAAY,GAAG,KAAY,CAAC;4BAClC,eAAe,GAAG;gCAChB,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,EAAE;gCAC7B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,CAAC;6BAClD,CAAC;4BAEF,gDAAgD;4BAChD,MAAM;wBACR,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;wBACjE,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,QAAQ,CAAC,WAAW,IAAI,MAAM;gBAC5C,QAAQ,EAAE,eAAe;aAC1B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,IAAI,YAAY,GAAG,8BAA8B,CAAC;YAElD,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;gBACzC,YAAY,GAAG,wBAAwB,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YAC7E,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzB,YAAY,GAAG,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC;YAC3C,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,+EAA+E;QAC/E,OAAO;YACL,wBAAwB;YACxB,0BAA0B;YAC1B,yBAAyB;YACzB,YAAY;YACZ,YAAY;YACZ,oBAAoB;SACrB,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,+EAA+E;QAC/E,MAAM,MAAM,GAAG;YACb,EAAE,EAAE,EAAE,wBAAwB,EAAE;YAChC,EAAE,EAAE,EAAE,0BAA0B,EAAE;YAClC,EAAE,EAAE,EAAE,yBAAyB,EAAE;YACjC,EAAE,EAAE,EAAE,YAAY,EAAE;YACpB,EAAE,EAAE,EAAE,YAAY,EAAE;YACpB,EAAE,EAAE,EAAE,oBAAoB,EAAE;SAC7B,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,0BAA0B,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;QACxE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,8DAA8D;aACxE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,uEAAuE;YACvE,mCAAmC;YACnC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAChC,KAAK,EAAE,OAAO;gBACd,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;aAC/C,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,uDAAuD,OAAO,IAAI;aAC5E,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,YAAY,GAAG,oCAAoC,CAAC;YAExD,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;gBACzC,YAAY,GAAG,wBAAwB,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YAC7E,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzB,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,qEAAqE;IAErE;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,wBAAwB;gBACrC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,yDAAyD;gBACtE,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AA9RD,8CA8RC", "sourcesContent": ["import { Base<PERSON><PERSON><PERSON>ider } from './baseLL<PERSON>rovider';\nimport { LLMGenerateP<PERSON><PERSON>, LLMGenerateResult } from '../types';\nimport { ToolCallRequest } from '../llmProvider';\n// Removed unused import getAnthropicApiKey - using credentialsManager instead\nimport { logger } from '../../logger';\nimport * as vscode from 'vscode';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport Anthropic, { MessageParam, Tool } from '@anthropic-ai/sdk';\n\nexport class AnthropicProvider extends BaseLLMProvider {\n  readonly providerId = 'anthropic';\n  readonly displayName = 'Anthropic';\n  readonly description = 'Anthropic Claude AI models';\n  readonly website = 'https://anthropic.com';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = false;\n  readonly defaultEndpoint = 'https://api.anthropic.com';\n  readonly defaultModel = 'claude-3-opus-20240229';\n\n  private client: any | null = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.providers.anthropic')) {\n        logger.info('Anthropic configuration changed, re-initializing client.');\n        this.initializeClient();\n      }\n    });\n  }\n\n  private initializeClient() {\n    // Check if Anthropic SDK is available\n    if (!Anthropic) {\n      logger.warn('Anthropic SDK not available. Please install it with: npm install @anthropic-ai/sdk');\n      this.client = null;\n      return;\n    }\n\n    const apiKey = this.config.apiKey;\n\n    if (!apiKey) {\n      logger.warn('Anthropic API key not set.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      this.client = new Anthropic({ apiKey });\n      logger.info('Anthropic client initialized.');\n    } catch (error) {\n      logger.error('Failed to initialize Anthropic client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return {\n        content: '',\n        error: 'Anthropic provider not configured (API key missing). Please set the API key in settings.'\n      };\n    }\n\n    if (cancellationToken?.isCancellationRequested) {\n      return { content: '', error: 'Request cancelled', finishReason: 'cancelled' };\n    }\n\n    try {\n      // Prepare messages\n      const messages: MessageParam[] = [];\n\n      // Add history messages if available\n      if (params.history && params.history.length > 0) {\n        for (const msg of params.history) {\n          if (msg.role === 'system') {\n            continue; // Skip system messages in history as Anthropic handles them separately\n          }\n\n          if (msg.role === 'user' || msg.role === 'assistant') {\n            messages.push({\n              role: msg.role as 'user' | 'assistant',\n              content: msg.content\n            });\n          }\n        }\n      } else {\n        // If no history, add the current prompt\n        messages.push({\n          role: 'user',\n          content: params.prompt\n        });\n      }\n\n      // Set up tool configuration if tools are provided\n      const anthropicTools: Tool[] = [];\n      if (tools && tools.size > 0) {\n        // Convert Map.entries() to Array for compatibility with older JS versions\n        const toolEntries = Array.from(tools.entries());\n        for (const [toolId, tool] of toolEntries) {\n          if (tool.actions) {\n            for (const [actionId, action] of Object.entries(tool.actions)) {\n              const typedAction = action as any; // Type assertion to avoid TS errors\n              if (typedAction.parameters) {\n                anthropicTools.push({\n                  function: {\n                    name: `${toolId}.${actionId}`,\n                    description: typedAction.description || `${toolId} ${actionId} action`,\n                    parameters: typedAction.parameters\n                  }\n                });\n              }\n            }\n          }\n        }\n      }\n\n      // Create the completion request\n      const completionRequest = {\n        model: params.modelId || 'claude-3-opus-20240229',\n        messages,\n        max_tokens: params.maxTokens || 1024,\n        temperature: params.temperature,\n        system: params.systemPrompt\n      };\n\n      // Add tools if available\n      if (anthropicTools.length > 0) {\n        (completionRequest as any).tools = anthropicTools;\n      }\n\n      // Make the API call\n      logger.debug(`Sending request to Anthropic with model: ${completionRequest.model}`);\n      const response = await this.client.messages.create(completionRequest);\n\n      // Process the response\n      let toolCallRequest: ToolCallRequest | undefined;\n      let content = '';\n\n      if (response.content && response.content.length > 0) {\n        for (const block of response.content) {\n          if (block.type === 'text') {\n            content += block.text;\n          } else if (block.type === 'tool_use' && tools) {\n            // Process tool call\n            try {\n              // Type assertion to access properties\n              const toolUseBlock = block as any;\n              toolCallRequest = {\n                name: toolUseBlock.name || '',\n                arguments: JSON.parse(toolUseBlock.input || '{}')\n              };\n\n              // Break early to let the agent execute the tool\n              break;\n            } catch (error) {\n              logger.error('Error parsing tool call from Anthropic:', error);\n            }\n          }\n        }\n      }\n\n      return {\n        content,\n        finishReason: response.stop_reason || 'stop',\n        toolCall: toolCallRequest\n      };\n\n    } catch (error: any) {\n      logger.error('Error calling Anthropic API:', error);\n      let errorMessage = 'Error calling Anthropic API.';\n\n      if (error.response?.data?.error?.message) {\n        errorMessage = `Anthropic API error: ${error.response.data.error.message}`;\n      } else if (error.message) {\n        errorMessage = `Error: ${error.message}`;\n      }\n\n      return { content: '', error: errorMessage };\n    }\n  }\n\n  async getAvailableModels(): Promise<string[]> {\n    if (!this.isConfigured()) {\n      return [];\n    }\n\n    // Return default Claude models - Anthropic doesn't provide a model listing API\n    return [\n      'claude-3-opus-20240229',\n      'claude-3-sonnet-20240229',\n      'claude-3-haiku-20240307',\n      'claude-2.1',\n      'claude-2.0',\n      'claude-instant-1.2'\n    ];\n  }\n\n  /**\n     * Lists available models with their details\n     */\n  async listModels(): Promise<{id: string}[]> {\n    if (!this.isConfigured()) {\n      return [];\n    }\n\n    // Return default Claude models - Anthropic doesn't provide a model listing API\n    const models = [\n      { id: 'claude-3-opus-20240229' },\n      { id: 'claude-3-sonnet-20240229' },\n      { id: 'claude-3-haiku-20240307' },\n      { id: 'claude-2.1' },\n      { id: 'claude-2.0' },\n      { id: 'claude-instant-1.2' }\n    ];\n\n    logger.info(`Provider anthropic has ${models.length} models available`);\n    return models;\n  }\n\n  /**\n     * Test connection to Anthropic\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'Anthropic client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Anthropic doesn't have a dedicated endpoint for testing connections,\n      // so we'll make a minimal API call\n      await this.client.messages.create({\n        model: modelId,\n        max_tokens: 10,\n        messages: [{ role: 'user', content: 'Hello' }]\n      });\n\n      return {\n        success: true,\n        message: `Successfully connected to Anthropic API with model '${modelId}'.`\n      };\n    } catch (error: any) {\n      logger.error('Anthropic connection test failed:', error);\n      let errorMessage = 'Failed to connect to Anthropic API';\n\n      if (error.response?.data?.error?.message) {\n        errorMessage = `Anthropic API error: ${error.response.data.error.message}`;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  // Use the parent class implementation for getConfig and updateConfig\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your Anthropic API key',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use (e.g., claude-3-opus-20240229)',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n\n"]}