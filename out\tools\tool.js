"use strict";
/**
 * Interfaces and types for AI Agent Tools, supporting modern features
 * like structured inputs, multiple actions, and multimodal data.
 * These definitions aim for production quality and completeness, leveraging Zod
 * for schema validation.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseTool = exports.ToolResultSchema = exports.ToolInputSchema = void 0;
// Zod for schema validation
const zod_1 = require("zod");
// Zod schema for common tool input
exports.ToolInputSchema = zod_1.z.object({
    action: zod_1.z.string(),
}).passthrough();
// Zod schema for tool result
exports.ToolResultSchema = zod_1.z.object({
    success: zod_1.z.boolean(),
    action: zod_1.z.string(),
    result: zod_1.z.any().optional(),
    error: zod_1.z.string().optional()
});
/**
 * Abstract base class providing common tool functionality
 */
class BaseTool {
    name;
    // Core Identification
    id;
    type = 'single-action';
    // Context Management
    _context;
    // Configuration
    config = {};
    metadata = {};
    version = '1.0.0';
    constructor(name, category) {
        this.name = name;
        this.id = name.toLowerCase().replace(/\s+/g, '-');
        if (category) {
            this.metadata.category = category;
        }
    }
    /**
     * Enhanced execution with additional options and context
     */
    async invoke(input, options = {}) {
        const { validateInput = true, context, onValidationError } = options;
        try {
            // Set context if provided
            if (context) {
                this.setContext?.(context);
            }
            // Validate input if enabled
            if (validateInput) {
                const validation = this.validateInput?.(input) || { valid: true };
                if (!validation.valid) {
                    const error = new Error(validation.error || 'Input validation failed');
                    onValidationError?.(error);
                    throw error;
                }
            }
            // Execute with current context
            const currentContext = this.getContext?.() || context;
            return await this.execute(typeof input === 'object' && input !== null && 'action' in input
                ? input.action
                : undefined, input, currentContext);
        }
        catch (error) {
            await this.onError?.(error, context);
            throw error;
        }
    }
    // Lifecycle Methods
    async initialize(context) {
        if (context) {
            this.setContext?.(context);
        }
    }
    async dispose() {
        // Default implementation does nothing
    }
    // Validation
    validateInput(input) {
        try {
            this.schema.parse(input);
            return { valid: true };
        }
        catch (error) {
            return {
                valid: false,
                error: error instanceof Error ? error.message : 'Validation failed'
            };
        }
    }
    // Context Management
    setContext(context) {
        this._context = context;
    }
    getContext() {
        return this._context;
    }
    // Error Handling
    async onError(error, context) {
        // Default error handling - can be overridden by subclasses
        console.error(`[${this.name}] Error:`, error);
    }
    // Utility Methods
    getSchemaForAction(actionName) {
        if (!actionName || !this.actions) {
            return this.schema;
        }
        return this.actions[actionName]?.inputSchema;
    }
    getDocumentation() {
        return this.description;
    }
    getExamples() {
        return [];
    }
    getMetrics() {
        return {};
    }
    resetMetrics() {
        // Default implementation does nothing
    }
    // Backward compatibility
    async run(input) {
        try {
            const result = await this.invoke(input);
            return {
                success: true,
                action: 'run',
                result: result,
                error: undefined
            };
        }
        catch (error) {
            // Return error result that matches schema
            const normalizedError = {
                success: false,
                action: input && typeof input === 'object' && 'action' in input ?
                    String(input.action) : 'unknown',
                error: error instanceof Error ? error.message : String(error)
            };
            return normalizedError;
        }
    }
}
exports.BaseTool = BaseTool;
//# sourceMappingURL=tool.js.map