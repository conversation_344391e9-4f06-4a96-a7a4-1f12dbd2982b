import { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';
import { setModalTheme } from '../components/modal';

// UI & Theme section logic and rendering
export function renderUIThemeSettingsSection(container: HTMLElement, settings: any) {
  // Default values
  // Use or initialize UIThemeConfig
  const themeConfig: UIThemeConfig = settings.themeConfig || { ...defaultUIThemeConfig };
  const theme = themeConfig.mode;
  const accentColor = themeConfig.accentColor;
  const onChange = typeof settings.onChange === 'function' ? settings.onChange : (key: string, value: any) => { settings[key] = value; };
  const onSave = typeof settings.onSave === 'function' ? settings.onSave : null;
  const onReset = typeof settings.onReset === 'function' ? settings.onReset : null;

  function updateThemeConfig(key: string, value: any) {
    // Only update top-level or modal/section keys
    if (key.startsWith('modal.')) {
      const sub = key.split('.').slice(1).join('.');
      setDeep(themeConfig.modal, sub, value);
    } else if (key.startsWith('section.')) {
      const sub = key.split('.').slice(1).join('.');
      setDeep(themeConfig.section, sub, value);
    } else {
      (themeConfig as any)[key] = value;
    }
    setModalTheme(themeConfig); // Live update modal
    onChange('themeConfig', themeConfig);
  }
  
  // Initialize theme config
  updateThemeConfig('mode', theme);

  function setDeep(obj: any, path: string, value: any) {
    const parts = path.split('.');
    let o = obj;
    for (let i = 0; i < parts.length - 1; i++) {
      if (!(parts[i] in o)) o[parts[i]] = {};
      o = o[parts[i]];
    }
    o[parts[parts.length - 1]] = value;
  }

  container.innerHTML = `
        <style>
            .ui-theme-section { max-width:480px; margin:0 auto; padding:22px 0; }
            .ui-theme-title { font-size:1.18em; font-weight:600; margin-bottom:14px; color:#222; }
            .ui-theme-group { margin-bottom:22px; }
            .ui-theme-label { font-weight:500; display:block; margin-bottom:7px; color:#374151; }
            .ui-theme-radio-group { display:flex; gap:22px; margin-bottom:8px; }
            .ui-theme-radio { display:flex; align-items:center; gap:7px; }
            .ui-theme-accent-row { display:flex; align-items:center; gap:12px; }
            .ui-theme-preview { margin-top:18px; padding:14px 18px; border-radius:8px; background:#f8fafc; border:1px solid #e5e7eb; }
            .ui-theme-btn-row { display:flex; gap:14px; margin-top:22px; }
            .ui-theme-btn { padding:7px 22px; border-radius:6px; border:none; font-weight:500; font-size:1em; cursor:pointer; }
            .ui-theme-btn.save { background:#2563eb; color:#fff; }
            .ui-theme-btn.reset { background:#f3f4f6; color:#222; border:1px solid #e5e7eb; }
            .ui-theme-accent-input { width:34px; height:34px; border-radius:50%; border:1.5px solid #d1d5db; background:transparent; cursor:pointer; }
        </style>
        <div class="ui-theme-section" role="region" aria-labelledby="ui-theme-title">
            <div class="ui-theme-title" id="ui-theme-title">UI & Theme Settings</div>
            <div class="ui-theme-group">
                <label class="ui-theme-label" for="theme-select">Theme</label>
                <div class="ui-theme-radio-group" id="theme-radio-group">
                    <label class="ui-theme-radio"><input type="radio" name="theme" value="light" id="theme-light"> Light</label>
                    <label class="ui-theme-radio"><input type="radio" name="theme" value="dark" id="theme-dark"> Dark</label>
                    <label class="ui-theme-radio"><input type="radio" name="theme" value="system" id="theme-system"> System</label>
                </div>
            </div>
            <div class="ui-theme-group">
                <label class="ui-theme-label" for="accent-color-input">Accent Color</label>
                <div class="ui-theme-accent-row">
                    <input type="color" id="accent-color-input" class="ui-theme-accent-input" aria-label="Accent Color" value="${accentColor}">
                    <span id="accent-color-value" style="font-family:monospace;font-size:0.98em;">${accentColor}</span>
                </div>
            </div>
            <div class="ui-theme-preview" id="ui-theme-preview" aria-live="polite">
                <div style="font-weight:500;">Live Preview:</div>
                <div style="margin-top:7px;">
                    <span style="color:${accentColor};font-weight:600;">Accent Color Example</span>
                    <span style="margin-left:18px;">Theme: <span id="theme-preview-label">${theme.charAt(0).toUpperCase() + theme.slice(1)}</span></span>
                </div>
                <div style="margin-top:14px;">
                    <button id="modal-preview-btn" style="background:${themeConfig.modal.button.background};color:${themeConfig.modal.button.color};border:${themeConfig.modal.button.border};border-radius:${themeConfig.modal.button.borderRadius};padding:7px 22px;font-size:1em;cursor:pointer;">Show Modal Preview</button>
                </div>
            </div>
            <div class="ui-theme-group">
                <label class="ui-theme-label">Modal Background</label>
                <input type="color" id="modal-bg-input" value="${themeConfig.modal.background}">
                <label class="ui-theme-label">Modal Border</label>
                <input type="color" id="modal-border-input" value="${themeConfig.modal.border}">
                <label class="ui-theme-label">Modal Border Radius</label>
                <input type="range" id="modal-radius-input" min="4" max="32" value="${parseInt(themeConfig.modal.borderRadius)}">
                <span id="modal-radius-value">${themeConfig.modal.borderRadius}</span>
                <label class="ui-theme-label">Modal Shadow</label>
                <input type="text" id="modal-shadow-input" value="${themeConfig.modal.boxShadow}" style="width:220px;">
                <label class="ui-theme-label">Modal Animation Duration (s)</label>
                <input type="number" id="modal-anim-input" min="0.05" max="2" step="0.01" value="${parseFloat(themeConfig.modal.animation.duration)}">
                <label class="ui-theme-label">Modal Icon (SVG allowed)</label>
                <input type="text" id="modal-icon-input" value="${themeConfig.modal.warningIcon.replace(/'/g, '&apos;') || ''}" style="width:220px;">
            </div>
            <div class="ui-theme-group">
                <label class="ui-theme-label">Section Header Background</label>
                <input type="color" id="section-header-bg-input" value="${themeConfig.section.headerBg}">
                <label class="ui-theme-label">Section Header Color</label>
                <input type="color" id="section-header-color-input" value="${themeConfig.section.headerColor}">
                <label class="ui-theme-label">Section Accent</label>
                <input type="color" id="section-accent-input" value="${themeConfig.section.accent}">
                <label class="ui-theme-label">Section Button Background</label>
                <input type="color" id="section-btn-bg-input" value="${themeConfig.section.button.background}">
                <label class="ui-theme-label">Section Button Color</label>
                <input type="color" id="section-btn-color-input" value="${themeConfig.section.button.color}">
                <label class="ui-theme-label">Section Button Border Radius</label>
                <input type="range" id="section-btn-radius-input" min="2" max="20" value="${parseInt(themeConfig.section.button.borderRadius)}">
                <span id="section-btn-radius-value">${themeConfig.section.button.borderRadius}</span>
            </div>
            <div class="ui-theme-btn-row">
                <button class="ui-theme-btn save" id="ui-theme-save-btn" type="button">Save</button>
                <button class="ui-theme-btn reset" id="ui-theme-reset-btn" type="button">Reset</button>
            </div>
        </div>
    `;

  // Set initial radio
  (container.querySelector(`#theme-${theme}`) as HTMLInputElement).checked = true;

  // --- Event handlers ---
  const themeRadios = container.querySelectorAll('input[name="theme"]') as NodeListOf<HTMLInputElement>;
  themeRadios.forEach(radio => {
    radio.addEventListener('change', (e) => {
      const target = e.target as HTMLInputElement;
      console.log('Theme changed to:', target.value);
      const newTheme = target.value;
      onChange('theme', newTheme);
      updatePreview(newTheme, (container.querySelector('#accent-color-input') as HTMLInputElement).value);
    });
  });

  const accentInput = container.querySelector('#accent-color-input') as HTMLInputElement;
  accentInput.addEventListener('input', e => {
    const newColor = accentInput.value;
    onChange('accentColor', newColor);
    (container.querySelector('#accent-color-value') as HTMLElement).textContent = newColor;
    updatePreview((container.querySelector('input[name="theme"]:checked') as HTMLInputElement).value, newColor);
  });

  // Save button
  const saveBtn = container.querySelector('#ui-theme-save-btn') as HTMLButtonElement;
  saveBtn.addEventListener('click', () => {
    if (onSave) onSave();
    else alert('Settings saved!');
  });

  // Reset button
  const resetBtn = container.querySelector('#ui-theme-reset-btn') as HTMLButtonElement;
  resetBtn.addEventListener('click', () => {
    if (onReset) onReset();
    // Reset UI to defaults
    (container.querySelector('#theme-system') as HTMLInputElement).checked = true;
    accentInput.value = '#2563eb';
    (container.querySelector('#accent-color-value') as HTMLElement).textContent = '#2563eb';
    updatePreview('system', '#2563eb');
    onChange('theme', 'system');
    onChange('accentColor', '#2563eb');
  });

  // --- Live preview helper ---
  function updatePreview(themeValue: string, accent: string) {
    const preview = container.querySelector('#ui-theme-preview') as HTMLElement;
        preview.querySelector('span')!.style.color = accent;
        const label = preview.querySelector('#theme-preview-label') as HTMLElement;
        label.textContent = themeValue.charAt(0).toUpperCase() + themeValue.slice(1);
  }

  // --- Accessibility: focus management ---
  (container.querySelector('.ui-theme-section') as HTMLElement).tabIndex = 0;
}

