{"version": 3, "file": "toolFrame.js", "sourceRoot": "", "sources": ["../../src/tools/toolFrame.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiiCA,8BA0HC;AA3pCD,6BAAkC;AAClC,mCAAoC;AAEpC,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AAC7B,2BAAoC;AACpC,4DAA+B;AAC/B,mCAAsC;AAEtC,mEAAmE;AACnE,2DAAuE;AACvE,uEAAqI;AACrI,yEAAsE;AACtE,2EAAwE;AACxE,qDAAsJ;AACtJ,sCAAsC;AAmItC,iFAAiF;AACpE,QAAA,gBAAgB,GAAG;IAC5B,gBAAgB;IAChB,iBAAiB;IACjB,eAAe,EAAE,iCAAiC;IAClD,8BAA8B,EAAE,uCAAuC;IACvE,sBAAsB;IACtB,gBAAgB;CACV,CAAC;AAwCX,uBAAuB;AACvB,MAAM,uBAAwB,SAAQ,KAAK;IAAG,YAAY,CAAC,GAAG,sBAAsB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC,CAAC,CAAC;CAAE;AAC5I,MAAM,qBAAsB,SAAQ,KAAK;IAAG,YAAY,CAAS,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC,CAAC,CAAC;CAAE;AACvH,MAAM,mBAAoB,SAAQ,KAAK;IAAG,YAAY,CAAS,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC,CAAC,CAAC;CAAE;AACnH,MAAM,aAAc,SAAQ,KAAK;IAAG,YAAY,CAAS,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC;CAAE;AACvG,MAAM,aAAc,SAAQ,KAAK;IAAG,YAAY,CAAS,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC;CAAE;AACvG,MAAM,mBAAoB,SAAQ,KAAK;IAAG,YAAY,CAAS,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC,CAAC,CAAC;CAAE;AACnH,aAAa;AAEb,gDAAgD;AAChD,MAAsB,QAAQ;IAGV,OAAO,GAAG,OAAO,CAAC;IAK3B,KAAK,CAAC,MAAM,CAAC,UAAkB,EAAE,KAAc,EAAE,OAA0B;QAC9E,MAAM,WAAW,GAAG,IAAA,mBAAU,GAAE,CAAC;QACjC,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC;YACD,IAAI,CAAC,SAAS;gBAAE,MAAM,IAAI,mBAAmB,CAAC,WAAW,UAAU,wBAAwB,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YAC1G,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC5D,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,SAAS,IAAI,CAAC,IAAI,kDAAkD,UAAU,IAAI,CAAC;gBACrG,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;gBAC1G,IAAI,CAAC,aAAa;oBAAE,MAAM,IAAI,qBAAqB,CAAC,qCAAqC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/H,CAAC;YACD,IAAI,iBAAiB,CAAC,uBAAuB;gBAAE,MAAM,IAAI,uBAAuB,EAAE,CAAC;YACnF,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAC7F,MAAM,eAAe,GAAG,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC7D,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,CAAC;QACzH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,eAAkG,CAAC;YACvG,IAAI,KAAK,YAAY,cAAQ;gBAAE,eAAe,GAAG,EAAE,OAAO,EAAE,wBAAwB,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;iBAClI,IAAI,KAAK,YAAY,qBAAqB;gBAAE,eAAe,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,qBAAqB,EAAE,CAAC;iBACtH,IAAI,KAAK,YAAY,uBAAuB;gBAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,CAAC;iBACnL,IAAI,KAAK,YAAY,KAAK;gBAAE,eAAe,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAG,KAAa,CAAC,IAAI,IAAI,mBAAmB,EAAE,CAAC;;gBAC/I,eAAe,GAAG,EAAE,OAAO,EAAE,2BAA2B,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;YACnF,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YAChH,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;QACvG,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,UAAkB;QACpC,uDAAuD;QACvD,OAAO,CAAC,GAAG,CAAC,mCAAmC,UAAU,EAAE,CAAC,CAAC;QAC7D,OAAO,EAAE,CAAC;IACd,CAAC;CAEJ;AA3CD,4BA2CC;AACD,aAAa;AAEb,0DAA0D;AAE1D;;GAEG;AACH,MAAa,gBAAgB;IACT,EAAE,CAAS;IACX,IAAI,CAAS;IACb,WAAW,CAAS;IACpB,OAAO,CAA4D;IACnE,SAAS,CAAW;IACpB,aAAa,CAAW;IAEvB,QAAQ,CAAS;IACjB,UAAU,CAAS;IACnB,KAAK,CAAsB;IAC3B,QAAQ,CAAoB;IAC5B,KAAK,CAAoB;IAE1C,YACI,EAAU,EACV,QAAgB,EAChB,UAAkB,EAClB,KAA0B,EAC1B,QAA2B,EAC3B,KAAwB;QAExB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,SAAS,QAAQ,IAAI,UAAU,EAAE,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,WAAW,QAAQ,IAAI,UAAU,EAAE,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE,OAAwB,EAA+B,EAAE;YAC3E,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM,QAAQ,GAAG,SAAS,IAAI,CAAC,QAAQ,cAAc,CAAC;gBACtD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;YAC/C,CAAC;YAED,IAAI,CAAC;gBACD,uCAAuC;gBACvC,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAE7D,mBAAmB;gBACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE;oBAC7D,eAAe,EAAE,OAAO;oBACxB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,iBAAiB,EAAE,IAAI,CAAC,KAAK;iBAChC,CAAC,CAAC;gBAEH,OAAO;oBACH,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,SAAS;oBACpC,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;iBACzD,CAAC;YACN,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACtB,MAAM,QAAQ,GAAG,yBAAyB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;YAC/C,CAAC;QACL,CAAC,CAAC;IACN,CAAC;IAEO,YAAY,CAAC,KAA0B,EAAE,OAAwB;QACrE,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAEzC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9E,sEAAsE;gBACtE,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAClD,MAAM,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,GAAG,IAAI,CAAC;gBACvC,IAAI,aAAkB,CAAC;gBAEvB,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;oBACtB,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;gBACnC,CAAC;qBAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC9B,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC;gBACpC,CAAC;qBAAM,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;oBAChC,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACJ,oCAAoC;oBACpC,aAAa,GAAI,OAAe,CAAC,MAAM,CAAC,CAAC;gBAC7C,CAAC;gBAED,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,IAAI,aAAa,CAAC,6BAA6B,MAAM,yBAAyB,CAAC,CAAC;gBAC1F,CAAC;gBAED,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;oBAC9B,aAAa,GAAG,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC;gBAC1C,CAAC;gBAED,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,IAAI,aAAa,CAAC,2BAA2B,KAAK,0BAA0B,CAAC,CAAC;gBACxF,CAAC;gBAED,QAAQ,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACJ,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAC1B,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;AAvGD,4CAuGC;AAED;;GAEG;AACH,MAAa,mBAAmB;IACX,QAAQ,CAAoB;IAE7C,YAAY,QAA2B;QACnC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,IAiB7B;QACG,MAAM,KAAK,GAAmB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAChD,IAAI,gBAAgB,CAChB,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,EACb,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC,KAAK,CAC7C,CACJ,CAAC;QAEF,MAAM,kBAAkB,GAAuB;YAC3C,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,wBAAwB,IAAI,CAAC,IAAI,EAAE;YAChD,OAAO,EAAE,OAAO;YAChB,KAAK;YACL,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE;YACzB,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE;SACvC,CAAC;QAEF,OAAO,IAAI,yBAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,wBAAwB,CAC3B,QAAgB,EAChB,UAAkB,EAClB,KAA0B,EAC1B,aAAqB,QAAQ,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;QAErD,MAAM,MAAM,GAAG,cAAc,CAAC;QAE9B,MAAM,IAAI,GAAG,IAAI,gBAAgB,CAC7B,MAAM,EACN,QAAQ,EACR,UAAU,EACV,KAAK,EACL,IAAI,CAAC,QAAQ,EACb,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC,KAAK,CAC7C,CAAC;QAEF,MAAM,kBAAkB,GAAuB;YAC3C,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,WAAW,QAAQ,IAAI,UAAU,EAAE;YACzC,WAAW,EAAE,8BAA8B,QAAQ,IAAI,UAAU,EAAE;YACnE,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,CAAC,IAAI,CAAC;YACb,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,MAAM;SACtB,CAAC;QAEF,OAAO,IAAI,yBAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;CACJ;AAtFD,kDAsFC;AAED,MAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;IAClF,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QACpB,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sEAAsE,CAAC;QAC/F,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;QACpE,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0CAA0C,CAAC;QAC5E,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,oFAAoF,CAAC;KAC1H,CAAC,CAAC,CAAC,QAAQ,CAAC,iDAAiD,CAAC;CAClE,CAAC,CAAC;AAEH,MAAa,YAAY;IAKQ;IAJZ,GAAG,CAAiB;IACpB,eAAe,CAAsB;IACrC,QAAQ,GAAG,IAAI,qBAAY,EAAE,CAAC;IAE/C,YAA6B,QAA2B;QAA3B,aAAQ,GAAR,QAAQ,CAAmB;QACpD,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;QACjD,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAEM,EAAE,CAAC,KAAuD,EAAE,QAAkC;QACjG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,GAAG,CAAC,IAAY,EAAE,OAAuE;QAClG,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;QACzC,MAAM,UAAU,GAAG,IAAA,mBAAU,GAAE,CAAC;QAEhC,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,aAAa,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;YACzE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1D,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YAE/D,+CAA+C;YAC/C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC;gBACtE,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,EAAE;aACW,CAAC,CAAC;YAE1B,sDAAsD;YACtD,MAAM,WAAW,GAAgB;gBAC7B,EAAE,EAAE,IAAA,mBAAU,GAAE;gBAChB,OAAO,EAAE,SAAS,IAAI,sBAAsB,gBAAgB,CAAC,MAAM,EAAE;gBACrE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,QAAQ,EAAE;oBACN,MAAM,EAAE,cAA8B,EAAE,mCAAmC;oBAC3E,IAAI,EAAE,eAA6B;oBACnC,IAAI,EAAE,CAAC,eAAe,EAAE,oBAAoB,CAAC;oBAC7C,IAAI;oBACJ,WAAW,EAAE,gBAAgB,CAAC,MAAM;oBACpC,UAAU;iBACb;aACJ,CAAC;YAEF,mEAAmE;YACnE,IAAI,CAAC;gBACD,MAAO,6BAAqB,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC,CAAC;YAC5D,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACnB,wCAAwC;gBACxC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;YACzD,CAAC;YAED,4EAA4E;YAC5E,MAAM,MAAM,GAAG,IAAA,kBAAS,EAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACxC,MAAM,sBAAsB,GAAI,MAAc,CAAC,sBAAsB,IAAI,IAAI,CAAC;YAE9E,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAE3E,2DAA2D;YAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC;gBACzD,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,iBAAiB,IAAI,EAAE;gBAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;aACpB,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,KAAK,GAAI,QAAgB,CAAC,OAAO,EAAE,KAAK,CAAC;YAC/C,IAAI,KAAK,EAAE,CAAC;gBACR,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;YAED,yCAAyC;YACzC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,QAAQ,CAAC,sBAAsB,CAAC;oBAC5B,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,IAAI;oBACrB,eAAe,EAAE,KAAK;oBACtB,mBAAmB,EAAE,KAAK;oBAC1B,mBAAmB,EAAE,IAAI;iBAC5B,CAAC,CAAC;YACP,CAAC;YAED,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAEtD,mDAAmD;YACnD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAE/D,uDAAuD;YACtD,mCAAwB,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAC;YAEvE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE;gBAC9B,UAAU;gBACV,YAAY,EAAE;oBACV,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ;oBACpD,OAAO,EAAE,IAAI,IAAI,EAAE;iBACtB;aACJ,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC;YACzF,OAAO,WAAW,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,aAAa,CACtB,QAAgB,EAChB,UAAkB,EAClB,KAA0B,EAC1B,KAAwB;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QAC5F,OAAO,MAAO,QAAgB,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;IACrI,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAAE,aAA2C,EAAE,KAAwB;QAChH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QACrG,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzG,MAAM,WAAW,GAA8B,EAAE,CAAC;QAClD,MAAM,aAAa,GAAG,4HAA4H,IAAI,0CAA0C,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAE,gBAA0B,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,yBAAyB,QAAQ,6iBAA6iB,CAAC;QACv3B,IAAI,aAAa,EAAE,MAAM,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;YAChE,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7J,CAAC;QACD,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAAE,iBAAiB,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QAClH,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,QAAkB;QAClD,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;QAEtC,sEAAsE;QACtE,MAAM,mBAAmB,GAAI,iCAAuB,EAAE,2BAA2B,CAAC;QAClF,IAAI,mBAAmB,EAAE,CAAC;YACtB,IAAI,CAAC;gBACD,kDAAkD;gBAClD,MAAM,cAAc,GAAG,MAAO,iCAAuB,CAAC,2BAA2B,CAC7E,UAAU,EACV,OAAO,EACP;oBACI,uBAAuB,EAAE,KAAK;oBAC9B,mBAAmB,EAAE,IAAI;oBACzB,sBAAsB,EAAE,IAAI;iBAC/B,CACJ,CAAC;gBAEF,IAAI,cAAc,EAAE,CAAC;oBACjB,OAAO;wBACH,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,MAAM,EAAE,cAAc,CAAC,MAAM;wBAC7B,KAAK,EAAE,cAAc,CAAC,KAAK;qBAC9B,CAAC;gBACN,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,0DAA0D;gBAC1D,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,sEAAsE,EAAE,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACzK,CAAC;QACL,CAAC;QAED,6BAA6B;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,+BAA+B;YAC/B,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;gBACjB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;gBAC9B,MAAM,EAAE,SAAS;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC;gBACD,mBAAmB;gBACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAE3C,yBAAyB;gBACzB,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjE,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAClC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;gBAE7B,wCAAwC;gBACxC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAChD,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC7C,CAAC;gBAED,wCAAwC;gBACxC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBAClB,OAAO;wBACH,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,SAAS,IAAI,CAAC,IAAI,UAAU;qBACtD,CAAC;gBACN,CAAC;gBAED,0CAA0C;gBAC1C,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;oBACpB,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,UAAU,CAAC,CAAC;oBAC9E,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;wBACnB,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,8BAA8B;oBACrD,CAAC;gBACL,CAAC;YACL,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACtB,0BAA0B;gBAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjE,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAClC,YAAY,CAAC,MAAM,GAAG;oBAClB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE,CAAC;gBAEF,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE,CAAC;YACN,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,OAAO;YACH,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,OAAO,CAAC,OAAO;SAC1B,CAAC;IACN,CAAC;CACJ;AAjOD,oCAiOC;AAED,aAAa;AAEb,qDAAqD;AAErD,MAAa,uBAAuB;IACxB,OAAO,CAAoB;IAAkB,WAAW,GAAG,6BAA6B,CAAC;IAC1F,UAAU,CAAC,OAAyB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC;IACjE,KAAK,CAAC,kBAAkB,CAAC,WAAsC,EAAE,SAAiB;QACrF,IAAI,CAAC,WAAW,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QACrC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAmB,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;QAC9F,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAChC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,SAAS,cAAc,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;QACzI,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAAC,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAAC,OAAO,IAAI,CAAC;QAAC,CAAC;QAC7J,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AAZD,0DAYC;AAED,MAAa,kBAAkB;IACnB,KAAK,GAAG,IAAI,GAAG,EAAiB,CAAC;IAClC,KAAK,CAAC,UAAU,CAAC,QAA2B,IAAmB,CAAC;IAChE,OAAO,CAAC,QAAgB,IAAuB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjF,WAAW,KAAuB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;CACrF;AALD,gDAKC;AAED,MAAsB,iBAAiB;IAChB,MAAM,CAAU;IAAC,YAAY,QAA2B,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;CAElH;AAHD,8CAGC;AAED,MAAa,WAAY,SAAQ,iBAAiB;IACvC,KAAK,CAAC,gBAAgB,CAAC,MAA2B,EAAE,OAAuE;QAC9H,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAAC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAS,QAAQ,CAAC,CAAC;QAAC,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAS,SAAS,EAAE,2BAA2B,CAAC,CAAC;QAC5L,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAClE,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QAAC,MAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;QAE3I,2DAA2D;QAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAErH,IAAI,CAAC;YACD,MAAM,IAAI,GAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,CAAS,OAAO,EAAE,aAAa,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC;YAC1I,IAAI,OAAO,CAAC,UAAU;gBAAE,IAAI,CAAC,eAAe,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;YACzE,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,OAAO,mBAAmB,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE,eAAe,EAAE,UAAU,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7N,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAAC,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAAC,MAAM,IAAI,KAAK,CAAC,aAAa,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC,CAAC;YAAC,CAAC;YAC7H,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAE1C,iDAAiD;YACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAE5G,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;gBAAE,MAAM,IAAI,uBAAuB,EAAE,CAAC;YAC/F,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QAChB,CAAC;gBAAS,CAAC;YACP,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;IACL,CAAC;CACJ;AA7BD,kCA6BC;AAED,MAAa,WAAY,SAAQ,iBAAiB;IACvC,KAAK,CAAC,gBAAgB,CAAC,MAA2B,EAAE,OAAuE;QAC9H,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAAC,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAS,OAAO,CAAC,CAAC;QAAC,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAC3K,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,GAAG,CAAS,SAAS,EAAE,wBAAwB,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/G,MAAM,IAAI,mBAAmB,CAAC,gCAAgC,CAAC,CAAC;IACpE,CAAC;CACJ;AAND,kCAMC;AAED,MAAa,aAAc,SAAQ,iBAAiB;IACzC,KAAK,CAAC,gBAAgB,CAAC,MAA2B,EAAE,OAAuE;QAC9H,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,GAAG,CAAS,SAAS,EAAE,0BAA0B,CAAC,EAAE,CAAC,CAAC;QACnL,MAAM,IAAI,mBAAmB,CAAC,mCAAmC,CAAC,CAAC;IACvE,CAAC;CACJ;AALD,sCAKC;AAED,MAAa,qBAAqB;IACtB,WAAW,CAAkB;IAC9B,UAAU,CAAC,QAA2B;QACzC,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAS,UAAU,EAAE,QAAQ,CAAC,CAAC;QACpG,QAAQ,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;YACjC,KAAK,QAAQ;gBAAE,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAAC,MAAM;YACnE,KAAK,QAAQ;gBAAE,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAAC,MAAM;YACnE,KAAK,UAAU;gBAAE,IAAI,CAAC,WAAW,GAAG,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAAC,MAAM;YACvE,OAAO,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,0BAA0B,YAAY,IAAI,CAAC,CAAC;QACzE,CAAC;QACD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC;IAChE,CAAC;IACM,cAAc,KAAqB,IAAI,CAAC,IAAI,CAAC,WAAW;QAAE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;CAChJ;AAbD,sDAaC;AAED,MAAa,gBAAgB;IAClB,KAAK,CAAC,YAAY,CACrB,KAAa,EACb,IAAuG;QAEvG,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAC7B;YACI,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;YAC9C,KAAK;YACL,WAAW,EAAE,IAAI;SACpB,EACD,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAC7C,CAAC;IACN,CAAC;IAEM,sBAAsB,CAAC,OAAe;QACzC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAEM,kBAAkB,CAAC,OAAe;QACrC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAEM,gBAAgB,CAAC,OAAuB;QAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACzF,CAAC;CACJ;AA1BD,4CA0BC;AAED,MAAa,yBAAyB;IAC1B,OAAO,CAAgB;IAAC,YAAY,IAAY,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7G,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,IAAa,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAChI,KAAK,CAAC,CAAS,EAAE,IAAa,IAAU,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI,CAAC,CAAS,EAAE,IAAa,IAAU,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACnE,IAAI,CAAC,CAAS,EAAE,IAAa,IAAU,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACnE,KAAK,CAAC,CAAiB,EAAE,IAAa,IAAU,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CAC3K;AAPD,8DAOC;AAED,MAAa,uBAAuB;IACf,aAAa,CAAS;IACvC,gBAAgB,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;QAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7J,uBAAuB,CAAC,CAAS,IAAY,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;IAChM,KAAK,CAAC,QAAQ,CAAC,CAAS,IAAqB,OAAO,aAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5G,KAAK,CAAC,SAAS,CAAC,CAAS,EAAE,CAAkB,IAAmB,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,aAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,aAAE,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrM,KAAK,CAAC,UAAU,CAAC,CAAS,IAAmB,MAAM,aAAE,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChG,KAAK,CAAC,eAAe,CAAC,CAAS,IAAmB,MAAM,aAAE,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;CACnI;AARD,0DAQC;AAED,MAAa,wBAAwB;IAChB,aAAa,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC;IACvG,KAAK,CAAC,gBAAgB,CAAC,OAAuC;QACjE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAAC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QACxF,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,OAAO,EAAE,cAAc,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACvF,MAAM,SAAS,GAAG,KAAK,EAAE,OAAe,EAA0B,EAAE;YAChE,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAAC,MAAM,QAAQ,GAAU,EAAE,CAAC;YAC/F,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC1B,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;oBAAE,SAAS;gBACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;gBAAC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAChH,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;oBAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAAC,CAAC;qBAAM,CAAC;oBAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;gBAAC,CAAC;YACrJ,CAAC;YACD,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;QAC9H,CAAC,CAAC;QACF,OAAO,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IACM,KAAK,CAAC,kBAAkB,CAAC,CAAS,IAAoB,OAAO,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7F,KAAK,CAAC,cAAc,CAAC,CAAS,IAAqB,OAAO,IAAI,uBAAuB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAChH;AAlBD,4DAkBC;AAED,MAAa,kBAAkB;IACV,MAAM,CAAU;IAChB,KAAK,CAAS;IACd,aAAa,CAAgB;IAC7B,cAAc,CAAiB;IACxC,cAAc,GAA4B,IAAI,CAAC;IAC/C,eAAe,GAA2B,IAAI,CAAC;IAC/C,YAAY,CAAgB;IAEpC,YAAY,MAAe;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC;QAE1F,6CAA6C;QAC7C,IAAI,CAAC,YAAY,GAAG;YAChB,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,KAAK;YACd,gBAAgB,EAAE,OAAO,CAAC,GAAG,EAAE;YAC/B,SAAS,EAAE,IAAI,CAAC,KAAK;YACrB,UAAU,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;YACrC,WAAW,EAAE,EAAE;YACf,cAAc,EAAE,EAAE;YAClB,MAAM,EAAE,KAAK;SAChB,CAAC;QAEF,+EAA+E;QAC/E,IAAI,CAAC,cAAc,GAAG;YAClB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;YAClB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,UAAU,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;SACxC,CAAC;QAEF,IAAI,CAAC,aAAa,GAAG;YACjB,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,CAAC;YACnB,mBAAmB,EAAE,CAAC;YACtB,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;SACjB,CAAC;QAEF,iEAAiE;IACrE,CAAC;IAEO,eAAe;QACnB,OAAO,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;IAC9E,CAAC;IAEO,UAAU,CAAC,GAAY;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC7C,MAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;QACxE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,aAAa,CAAC,+CAA+C,CAAC,CAAC;QAC7E,CAAC;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,OAAe,EAAE,IAAc,EAAE,OAAgE;QAClH,OAAO,IAAI,OAAO,CAAgB,CAAC,OAAO,EAAE,EAAE;YAC1C,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO;gBACtC,CAAC,CAAC,WAAW,WAAW,GAAG;gBAC3B,CAAC,CAAC,UAAU,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;YAEpD,MAAM,eAAe,GAAG,IAAI,mCAAkB,CAAC;gBAC3C,IAAI,EAAE,kBAAkB;gBACxB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,GAAG;aACN,CAAC,CAAC;YAEH,gBAAgB;YAChB,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACjD,MAAM,IAAI,IAAI,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,sBAAsB;YACtB,MAAM,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;gBAC5E,YAAY,GAAG,IAAI,CAAC;gBACpB,eAAe,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,sBAAsB;YACtB,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;gBACrD,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC3B,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,OAAO,CAAC;oBACJ,OAAO,EAAE,QAAQ;oBACjB,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,QAAQ;oBAClB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAChC,OAAO,EAAE,QAAQ,KAAK,CAAC;oBACvB,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS;oBACzD,UAAU,EAAE,KAAK;oBACjB,WAAW,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,oBAAoB;YACpB,eAAe,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACnC,OAAO,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;gBACvE,OAAO,CAAC;oBACJ,OAAO,EAAE,QAAQ;oBACjB,QAAQ,EAAE,CAAC,CAAC;oBACZ,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,CAAC;oBACX,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,+BAA+B;oBACtC,UAAU,EAAE,KAAK;oBACjB,WAAW,EAAE,IAAI,IAAI,EAAE;iBAC1B,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,OAAe,EAAE,IAAc,EAAE,OAAgG;QACjJ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO;gBACtC,CAAC,CAAC,WAAW,WAAW,GAAG;gBAC3B,CAAC,CAAC,UAAU,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;YAEpD,MAAM,eAAe,GAAG,IAAI,mCAAkB,CAAC;gBAC3C,IAAI,EAAE,iBAAiB;gBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,GAAG;aACN,CAAC,CAAC;YAEH,0CAA0C;YAC1C,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACjD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,sBAAsB;YACtB,MAAM,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;gBAC5E,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC1B,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,sBAAsB;YACtB,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;gBACrD,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC3B,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,OAAO,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,oBAAoB;YACpB,eAAe,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACnC,OAAO,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;gBAC9E,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,OAA0B;QACjD,IAAI,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAC1C,MAAM,OAAO,GAAG,IAAI,mCAAkB,CAAC;gBACnC,IAAI,EAAE,8BAA8B;gBACpC,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,GAAG;aACN,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;YACxE,OAAO,OAAO,CAAC;QACnB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACrH,MAAM,IAAI,aAAa,CAAC,yCAAyC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/H,CAAC;IACL,CAAC;CACJ;AAtLD,gDAsLC;AACD,aAAa;AAEb,6DAA6D;AAE7D,MAAa,sBAAsB;IACxB,KAAK,CAAC,QAAQ;QACjB,IAAI,CAAC;YACD,OAAO,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAChH,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,SAAS;QAClB,wDAAwD;QACxD,4DAA4D;QAC5D,MAAM,IAAI,mBAAmB,CAAC,qDAAqD,CAAC,CAAC;IACzF,CAAC;CACJ;AAdD,wDAcC;AAED,sFAAsF;AACtF,6EAA6E;AAC7E,MAAM,eAAe;IAAG,KAAK,CAAC,UAAU,CAAC,OAAyB,IAAI,CAAC;CAAE;AACzE,MAAM,aAAa;IAAG,KAAK,CAAC,UAAU,KAAK,CAAC;CAAE;AAC9C,MAAM,oBAAoB;IAAG,KAAK,CAAC,UAAU,KAAK,CAAC;CAAE;AAG9C,KAAK,UAAU,SAAS,CAAC,OAAgC;IAC5D,mDAAmD;IACnD,iDAAiD;IACjD,MAAM,MAAM,GAAG,IAAI,yBAAyB,CAAC,oBAAoB,CAAC,CAAC;IACnE,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAEtC,IAAI,CAAC;QAED,6EAA6E;QAC7E,2CAA2C;QAC3C,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,IAAI,CAAC;YACD,MAAM,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,0CAA0C,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QACnF,CAAC;QAED,kEAAkE;QAElE,oDAAoD;QACpD,MAAM,EAAE,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAClC,MAAM,iBAAiB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QACxD,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEtC,iDAAiD;QACjD,MAAM,aAAa,GAAG,IAAI,6BAAa,EAAkF,CAAC;QAC1H,MAAM,UAAU,GAAG,IAAI,uBAAuB,EAAE,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,wBAAwB,EAAE,CAAC;QACjD,MAAM,eAAe,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAC/C,MAAM,YAAY,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC9C,MAAM,WAAW,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAEhD,uDAAuD;QACvD,MAAM,QAAQ,GAAsB;YAChC,MAAM;YACN,EAAE;YACF,iBAAiB;YACjB,YAAY;YACZ,aAAa;YACb,SAAS;YACT,UAAU;YACV,eAAe;YACf,SAAS;YACT,WAAW;SACd,CAAC;QAEF,IAAI,CAAC;YACD,MAAM,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,IAAI,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,8DAA8D;QAC9D,IAAI,CAAC;YACD,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,gDAAgD,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QACzF,CAAC;QAED,IAAI,CAAC;YACD,MAAM,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxC,gDAAgD;QACpD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QAChF,CAAC;QAED,2FAA2F;QAC3F,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC7E,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;QACnF,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;QAEhD,6CAA6C;QAC7C,YAAY,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAAC;QACnF,YAAY,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,MAAM,cAAc,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC7H,YAAY,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAE9I,qDAAqD;QACrD,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;YACjF,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACzD,IAAI,CAAC;gBACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC,CAAC;gBACnG,IAAI,CAAC,IAAI;oBAAE,OAAO;gBAElB,MAAM,WAAW,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;gBAE/E,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;oBACtB,EAAE,CAAC,sBAAsB,CAAC,oDAAoD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACxH,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,aAAa,CAAC,wBAAwB,WAAW,CAAC,KAAK,IAAI,eAAe,EAAE,CAAC,CAAC;gBAC5F,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,CAAC,KAAK,YAAY,uBAAuB,CAAC,EAAE,CAAC;oBAC9C,MAAM,CAAC,KAAK,CAAC,KAAc,EAAE,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC,CAAC;oBAC9E,EAAE,CAAC,gBAAgB,CAAC,KAAc,CAAC,CAAC;gBACxC,CAAC;qBAAM,CAAC;oBACJ,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBACrD,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QACvF,OAAO,iBAAiB,CAAC;IAE7B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,MAAM,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5E,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,kDAAkD,EAAE,CAAC,CAAC;QACzF,6CAA6C;QAC7C,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4CAA4C,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QACxH,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;IAClC,CAAC;AACL,CAAC;AACD,aAAa", "sourcesContent": ["import { z, ZodError } from 'zod';\nimport { randomUUID } from 'crypto';\nimport type { CancellationToken, Progress, ExtensionContext, OutputChannel, Uri, Event } from 'vscode';\nimport * as vscode from 'vscode';\nimport * as os from 'os';\nimport * as path from 'path';\nimport { promises as fs } from 'fs';\nimport fetch from 'node-fetch';\nimport { EventEmitter } from 'events';\n\n// Import existing advanced functionality instead of reimplementing\nimport { MemoryManager, memoryManager } from '../memory/memoryManager';\nimport { Workflow, WorkflowContext, WorkflowStepResult, WorkflowStep, WorkflowDefinition } from '../agents/workflows/workflowEngine';\nimport { workflowManager } from '../agents/workflows/workflowManager';\nimport { workflowRegistry } from '../agents/workflows/workflowRegistry';\nimport { InteractiveSession, ITerminalProcess, TerminalActions, TerminalConfig, CommandResult, TerminalState, TerminalStats } from './pseudoTerminal';\nimport { getConfig } from '../config';\nimport type { IMemoryOperations, MemoryEntry, MemorySearchOptions, MemorySource, MemoryType } from '../memory/types';\n\n\n// #region PART 1: Core Framework Contracts & Interfaces\n\n// ===================================================================================\n//                        CORE INTERFACES & DATA STRUCTURES\n// ===================================================================================\n\n/** Represents multimodal data, such as an image. */\nexport interface MultimodalData {\n    mimeType: 'image/jpeg' | 'image/png' | 'image/webp';\n    base64Data: string;\n    source: 'clipboard' | 'file';\n}\n\n/** Represents a single content part of a prompt for a language model. */\nexport type LanguageModelPromptPart =\n    | { type: 'text'; text: string; }\n    | { type: 'image'; source: { type: 'base64'; media_type: MultimodalData['mimeType']; data: string; }; };\n\n/** Represents a complete prompt for a language model, which can include text and images. */\nexport type LanguageModelPrompt = LanguageModelPromptPart[];\n\n/** A structured representation of a directory and its contents. */\nexport interface DirectoryTree {\n    path: string;\n    name: string;\n    children: (DirectoryTree | { path: string; name: string; type: 'file' })[];\n    type: 'directory';\n}\n\n/** Standardized logging interface injected into all services and tools. */\nexport interface ILogger {\n    debug(message: string, metadata?: Record<string, any>): void;\n    info(message: string, metadata?: Record<string, any>): void;\n    warn(message: string, metadata?: Record<string, any>): void;\n    error(message: string | Error, metadata?: Record<string, any>): void;\n}\n\n/** Provides tools with a high-level understanding of the workspace structure. */\nexport interface IWorkspaceKnowledge {\n    getDirectoryTree(options?: { ignorePatterns?: string[] }): Promise<DirectoryTree | null>;\n    findFilesByPattern(globPattern: string): Promise<Uri[]>;\n    getFileContent(relativePath: string): Promise<string>;\n}\n\n/** A secure, sandboxed service for all file system I/O operations. */\nexport interface IFileSystemManager {\n    readFile(relativePath: string): Promise<string>;\n    writeFile(relativePath: string, content: string | Buffer): Promise<void>;\n    deleteFile(relativePath: string): Promise<void>;\n    createDirectory(relativePath: string): Promise<void>;\n}\n\n\n\n/** The contract for an interactive, stateful terminal session. */\nexport interface IInteractiveSession extends vscode.Disposable {\n    readonly id: string;\n    readonly onData: Event<string>;\n    readonly onExit: Event<number>;\n    write(data: string): Promise<void>;\n    resize(cols: number, rows: number): Promise<void>;\n}\n\n/** The single, unified service for all terminal operations. */\nexport interface ITerminalService {\n    execute(command: string, args: string[], options: { cwd?: string; cancellationToken: CancellationToken }): Promise<CommandResult>;\n    stream(command: string, args: string[], options: { cwd?: string; onData: (data: string) => void; cancellationToken: CancellationToken }): Promise<{ exitCode: number }>;\n    createSession(options?: { cwd?: string }): Promise<IInteractiveSession>;\n}\n\n/** A secure service for reading content from the user's clipboard. */\nexport interface IClipboardService {\n    readText(): Promise<string>;\n    readImage(): Promise<MultimodalData | null>;\n}\n\n/** The contract for any Large Language Model backend. */\nexport interface ILanguageModel {\n    generateResponse(prompt: LanguageModelPrompt, options: { cancellationToken: CancellationToken; jsonOutput: boolean }): Promise<string>;\n}\n\n/** A service that manages and provides the currently active language model. */\nexport interface ILanguageModelProvider {\n    initialize(services: IServiceContainer): void;\n    getActiveModel(): ILanguageModel;\n}\n\n/** Manages checking and requesting user consent for sensitive operations. */\nexport interface IPermissionManager {\n    initialize(context: ExtensionContext): void;\n    requestPermissions(permissions: readonly ToolPermission[], rationale: string): Promise<boolean>;\n}\n\n/** A registry capable of discovering and retrieving tools. */\nexport interface IToolRegistry {\n    initialize(services: IServiceContainer): Promise<void>;\n    getTool(toolName: string): ITool | undefined;\n    getAllTools(): readonly ITool[];\n}\n\n/** A service for interacting with the VS Code UI elements. */\nexport interface IToolUIProvider {\n    showProgress<T>(title: string, work: (progress: Progress<{ message?: string }>, token: CancellationToken) => Promise<T>): Promise<T>;\n    showInformationMessage(message: string): void;\n    showWarningMessage(message: string): void;\n    showErrorMessage(message: string | Error): void;\n}\n\n/** Metadata for logging operations */\nexport interface LogMetadata {\n    [key: string]: unknown;\n}\n\n/** A container for all singleton services, facilitating clean dependency injection. */\nexport interface IServiceContainer {\n    readonly logger: ILogger;\n    readonly ui: IToolUIProvider;\n    readonly permissionManager: IPermissionManager;\n    readonly toolRegistry: IToolRegistry;\n    readonly memoryManager: IMemoryOperations;\n    readonly workspace: IWorkspaceKnowledge;\n    readonly fileSystem: IFileSystemManager;\n    readonly terminalService: ITerminalService;\n    readonly clipboard: IClipboardService;\n    readonly llmProvider: ILanguageModelProvider;\n}\n\n/** An identifier for a resource or capability that a tool may need to access. */\nexport const TOOL_PERMISSIONS = [\n    'workspace:read',\n    'workspace:write',\n    'shell:execute', // Governs execute() and stream()\n    'terminal:interactive_session', // Governs the powerful createSession()\n    'network:unrestricted',\n    'clipboard:read',\n] as const;\nexport type ToolPermission = typeof TOOL_PERMISSIONS[number];\n\n/** The detailed result of an individual tool action execution. */\nexport interface ToolResult {\n    readonly executionId: string;\n    readonly toolName: string;\n    readonly actionName: string;\n    readonly status: 'success' | 'failure' | 'cancelled';\n    readonly output?: any;\n    readonly error?: { readonly message: string; readonly code?: string; readonly stack?: string; readonly details?: Record<string, any> };\n    readonly memoriesCreated?: string[];\n}\n\n/** The master interface for defining AI Agent Tools. */\nexport interface ITool {\n    readonly name: string;\n    readonly description: string;\n    readonly version: string;\n    readonly category: string;\n    readonly actions: Readonly<Record<string, ToolActionDefinition>>;\n    invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult>;\n    getPermissions(actionName: string): readonly ToolPermission[];\n    getDocumentation(): string;\n}\n\n/** Defines a single, named action within a tool. */\nexport interface ToolActionDefinition {\n    readonly description: string;\n    readonly inputSchema: z.ZodType<unknown>;\n    readonly outputSchema: z.ZodType<unknown>;\n}\n\n/** The set of options passed to a tool's `invoke` method. */\nexport interface ToolInvokeOptions {\n    readonly workflowContext: WorkflowContext;\n    readonly services: IServiceContainer;\n    readonly cancellationToken: CancellationToken;\n}\n\n// Custom Error classes\nclass OperationCancelledError extends Error { constructor(m = 'Operation cancelled.') { super(m); this.name = 'OperationCancelledError'; } }\nclass PermissionDeniedError extends Error { constructor(m: string) { super(m); this.name = 'PermissionDeniedError'; } }\nclass ToolDefinitionError extends Error { constructor(m: string) { super(m); this.name = 'ToolDefinitionError'; } }\nclass TerminalError extends Error { constructor(m: string) { super(m); this.name = 'TerminalError'; } }\nclass WorkflowError extends Error { constructor(m: string) { super(m); this.name = 'WorkflowError'; } }\nclass NotImplementedError extends Error { constructor(m: string) { super(m); this.name = 'NotImplementedError'; } }\n// #endregion\n\n// #region PART 2: Abstract Base Class for Tools\nexport abstract class BaseTool implements ITool {\n    public abstract readonly name: string;\n    public abstract readonly description: string;\n    public readonly version = '1.0.0';\n    public abstract readonly category: string;\n    public abstract readonly actions: Readonly<Record<string, ToolActionDefinition>>;\n    protected abstract _execute(actionName: string, validatedInput: unknown, options: ToolInvokeOptions): Promise<{ output: unknown, memoriesCreated?: string[] }>;\n\n    public async invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult> {\n        const executionId = randomUUID();\n        const { services, cancellationToken } = options;\n        const actionDef = this.actions[actionName];\n        try {\n            if (!actionDef) throw new ToolDefinitionError(`Action '${actionName}' not found in tool '${this.name}'.`);\n            const requiredPermissions = this.getPermissions(actionName);\n            if (requiredPermissions.length > 0) {\n                const rationale = `Tool '${this.name}' is requesting permissions to perform action '${actionName}'.`;\n                const hasPermission = await services.permissionManager.requestPermissions(requiredPermissions, rationale);\n                if (!hasPermission) throw new PermissionDeniedError(`User denied required permissions: ${requiredPermissions.join(', ')}`);\n            }\n            if (cancellationToken.isCancellationRequested) throw new OperationCancelledError();\n            const validatedInput = actionDef.inputSchema.parse(input);\n            const { output, memoriesCreated } = await this._execute(actionName, validatedInput, options);\n            const validatedOutput = actionDef.outputSchema.parse(output);\n            return { executionId, toolName: this.name, actionName, status: 'success', output: validatedOutput, memoriesCreated };\n        } catch (error) {\n            let structuredError: { message: string, code?: string, stack?: string, details?: Record<string, any> };\n            if (error instanceof ZodError) structuredError = { message: 'Data validation failed', code: 'E_VALIDATION', details: error.flatten() };\n            else if (error instanceof PermissionDeniedError) structuredError = { message: error.message, code: 'E_PERMISSION_DENIED' };\n            else if (error instanceof OperationCancelledError) return { executionId, toolName: this.name, actionName, status: 'cancelled', error: { message: error.message, code: 'E_CANCELLED' } };\n            else if (error instanceof Error) structuredError = { message: error.message, stack: error.stack, code: (error as any).code || 'E_EXECUTION_ERROR' };\n            else structuredError = { message: 'An unknown error occurred', code: 'E_UNKNOWN' };\n            services.logger.error(structuredError.message, { tool: this.name, action: actionName, error: structuredError });\n            return { executionId, toolName: this.name, actionName, status: 'failure', error: structuredError };\n        }\n    }\n\n    public getPermissions(actionName: string): readonly ToolPermission[] {\n        // Log the action name for debugging and audit purposes\n        console.log(`Getting permissions for action: ${actionName}`);\n        return [];\n    }\n    public abstract getDocumentation(): string;\n}\n// #endregion\n\n// #region PART 3: Advanced Workflow Integration for Tools\n\n/**\n * Tool Workflow Step - Integrates tools into the advanced workflow system\n */\nexport class ToolWorkflowStep implements WorkflowStep {\n    public readonly id: string;\n    public readonly name: string;\n    public readonly description: string;\n    public readonly execute: (context: WorkflowContext) => Promise<WorkflowStepResult>;\n    public readonly nextSteps: string[];\n    public readonly isConditional?: boolean;\n\n    private readonly toolName: string;\n    private readonly actionName: string;\n    private readonly input: Record<string, any>;\n    private readonly services: IServiceContainer;\n    private readonly token: CancellationToken;\n\n    constructor(\n        id: string,\n        toolName: string,\n        actionName: string,\n        input: Record<string, any>,\n        services: IServiceContainer,\n        token: CancellationToken\n    ) {\n        this.id = id;\n        this.name = `Tool: ${toolName}.${actionName}`;\n        this.description = `Execute ${toolName}.${actionName}`;\n        this.nextSteps = [];\n        this.toolName = toolName;\n        this.actionName = actionName;\n        this.input = input;\n        this.services = services;\n        this.token = token;\n\n        this.execute = async (context: WorkflowContext): Promise<WorkflowStepResult> => {\n            const tool = this.services.toolRegistry.getTool(this.toolName);\n            if (!tool) {\n                const errorMsg = `Tool '${this.toolName}' not found.`;\n                return { success: false, error: errorMsg };\n            }\n\n            try {\n                // Hydrate input using workflow context\n                const hydratedInput = this.hydrateInput(this.input, context);\n\n                // Execute the tool\n                const result = await tool.invoke(this.actionName, hydratedInput, {\n                    workflowContext: context,\n                    services: this.services,\n                    cancellationToken: this.token\n                });\n\n                return {\n                    success: result.status === 'success',\n                    output: result.output,\n                    error: result.error ? result.error.message : undefined\n                };\n            } catch (error: unknown) {\n                const errorMsg = `Error executing tool '${this.toolName}.${this.actionName}': ${error instanceof Error ? error.message : String(error)}`;\n                return { success: false, error: errorMsg };\n            }\n        };\n    }\n\n    private hydrateInput(input: Record<string, any>, context: WorkflowContext): Record<string, any> {\n        const hydrated: Record<string, any> = {};\n\n        for (const [key, value] of Object.entries(input)) {\n            if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {\n                // Handle template strings like {{variableName}} or {{outputs.stepId}}\n                const path = value.slice(2, -2).trim().split('.');\n                const [source, ...propertyPath] = path;\n                let resolvedValue: any;\n\n                if (source === 'inputs') {\n                    resolvedValue = context.inputs;\n                } else if (source === 'outputs') {\n                    resolvedValue = context.outputs;\n                } else if (source === 'variables') {\n                    resolvedValue = context.variables;\n                } else {\n                    // Try to find in context properties\n                    resolvedValue = (context as any)[source];\n                }\n\n                if (resolvedValue === undefined) {\n                    throw new WorkflowError(`Hydration failed: Source '${source}' not found in context.`);\n                }\n\n                for (const prop of propertyPath) {\n                    resolvedValue = resolvedValue?.[prop];\n                }\n\n                if (resolvedValue === undefined) {\n                    throw new WorkflowError(`Hydration failed: Path '${value}' could not be resolved.`);\n                }\n\n                hydrated[key] = resolvedValue;\n            } else {\n                hydrated[key] = value;\n            }\n        }\n\n        return hydrated;\n    }\n}\n\n/**\n * Tool Workflow Factory - Creates workflows that leverage tools\n */\nexport class ToolWorkflowFactory {\n    private readonly services: IServiceContainer;\n\n    constructor(services: IServiceContainer) {\n        this.services = services;\n    }\n\n    /**\n     * Create a workflow from tool execution plan\n     */\n    public createWorkflowFromPlan(plan: {\n        id: string;\n        name: string;\n        steps: Array<{\n            id: string;\n            tool_name: string;\n            action_name: string;\n            input: Record<string, any>;\n        }>;\n        inputs?: Array<{\n            id: string;\n            name: string;\n            description: string;\n            type: 'string' | 'number' | 'boolean' | 'object' | 'array';\n            required: boolean;\n            default?: any;\n        }>;\n    }): Workflow {\n        const steps: WorkflowStep[] = plan.steps.map(step =>\n            new ToolWorkflowStep(\n                step.id,\n                step.tool_name,\n                step.action_name,\n                step.input,\n                this.services,\n                new vscode.CancellationTokenSource().token\n            )\n        );\n\n        const workflowDefinition: WorkflowDefinition = {\n            id: plan.id,\n            name: plan.name,\n            description: `Tool-based workflow: ${plan.name}`,\n            version: '1.0.0',\n            steps,\n            inputs: plan.inputs || [],\n            outputs: [],\n            startStepId: plan.steps[0]?.id || ''\n        };\n\n        return new Workflow(workflowDefinition);\n    }\n\n    /**\n     * Create a simple single-tool workflow\n     */\n    public createSimpleToolWorkflow(\n        toolName: string,\n        actionName: string,\n        input: Record<string, any>,\n        workflowId: string = `tool-${toolName}-${Date.now()}`\n    ): Workflow {\n        const stepId = 'execute-tool';\n\n        const step = new ToolWorkflowStep(\n            stepId,\n            toolName,\n            actionName,\n            input,\n            this.services,\n            new vscode.CancellationTokenSource().token\n        );\n\n        const workflowDefinition: WorkflowDefinition = {\n            id: workflowId,\n            name: `Execute ${toolName}.${actionName}`,\n            description: `Simple workflow to execute ${toolName}.${actionName}`,\n            version: '1.0.0',\n            steps: [step],\n            inputs: [],\n            outputs: [],\n            startStepId: stepId\n        };\n\n        return new Workflow(workflowDefinition);\n    }\n}\n\nconst WorkflowPlanSchema = z.object({\n    thought: z.string().describe('The reasoning and strategy for the generated plan.'),\n    steps: z.array(z.object({\n        id: z.string().describe('A unique camelCase identifier for this step, e.g., \\'readFileStep\\'.'),\n        tool_name: z.string().describe('The exact name of the tool to use.'),\n        action_name: z.string().describe('The exact name of the action to execute.'),\n        input: z.record(z.any()).describe('Input parameters. Use \\'$stepId.output\\' to reference outputs from previous steps.'),\n    })).describe('The sequence of tool calls to achieve the goal.'),\n});\n\nexport class Orchestrator {\n    private readonly llm: ILanguageModel;\n    private readonly workflowFactory: ToolWorkflowFactory;\n    private readonly _emitter = new EventEmitter();\n\n    constructor(private readonly services: IServiceContainer) {\n        this.llm = services.llmProvider.getActiveModel();\n        this.workflowFactory = new ToolWorkflowFactory(services);\n    }\n\n    public on(event: 'workflowStart' | 'stepComplete' | 'workflowEnd', listener: (...args: any[]) => void): void {\n        this._emitter.on(event, listener);\n    }\n\n    public async run(goal: string, options: { visualContext?: MultimodalData[], token: CancellationToken }): Promise<WorkflowStepResult> {\n        const { visualContext, token } = options;\n        const workflowId = randomUUID();\n\n        return this.services.ui.showProgress(`AI Agent: ${goal}`, async (progress) => {\n            this._emitter.emit('workflowStart', { workflowId, goal });\n            progress.report({ message: 'Analyzing goal and memories...' });\n\n            // Utilize memory manager for context retrieval\n            const relevantMemories = await this.services.memoryManager.searchMemories({\n                query: goal,\n                limit: 10\n            } as MemorySearchOptions);\n\n            // Store search results in memory for future reference\n            const memoryEntry: MemoryEntry = {\n                id: randomUUID(),\n                content: `Goal: ${goal} | Memories found: ${relevantMemories.length}`,\n                timestamp: Date.now(),\n                metadata: {\n                    source: 'orchestrator' as MemorySource, // Type assertion for compatibility\n                    type: 'goal_analysis' as MemoryType,\n                    tags: ['goal_analysis', 'workflow_execution'],\n                    goal,\n                    memoryCount: relevantMemories.length,\n                    workflowId\n                }\n            };\n\n            // Use memoryManager instance with full MemoryManager functionality\n            try {\n                await (memoryManager as any).storeMemory?.(memoryEntry);\n            } catch (memoryError) {\n                // Silently handle memory storage errors\n                console.warn('Failed to store memory:', memoryError);\n            }\n\n            // Get configuration for enhanced workflow features using getConfig directly\n            const config = getConfig('aiAgent', {});\n            const enableAdvancedFeatures = (config as any).enableAdvancedFeatures ?? true;\n\n            const plan = await this.createWorkflowPlan(goal, visualContext, token);\n            this.services.logger.info('Generated workflow plan', { workflowId, plan });\n\n            // Create workflow using the factory with enhanced features\n            const workflow = this.workflowFactory.createWorkflowFromPlan({\n                id: workflowId,\n                name: `Workflow for: ${goal}`,\n                steps: plan.steps\n            });\n\n            // Set up agent for the workflow\n            const agent = (workflow as any).context?.agent;\n            if (agent) {\n                workflow.setAgent(agent);\n            }\n\n            // Enable advanced features if configured\n            if (enableAdvancedFeatures) {\n                workflow.enableAdvancedFeatures({\n                    goddessMode: true,\n                    quantumAnalysis: true,\n                    neuralSynthesis: false,\n                    timeTravelDebugging: false,\n                    adaptivePersonality: true\n                });\n            }\n\n            progress.report({ message: 'Executing workflow...' });\n\n            // Execute the workflow - simplified implementation\n            const finalResult = await this.executeWorkflowSimple(workflow);\n\n            // Register workflow with registry for future reference\n            (workflowRegistry as any).registerWorkflow?.(workflow.getDefinition());\n\n            this._emitter.emit('workflowEnd', {\n                workflowId,\n                finalContext: {\n                    status: finalResult.success ? 'completed' : 'failed',\n                    endTime: new Date()\n                }\n            });\n\n            this.services.logger.info('Workflow finished with result:', { workflowId, finalResult });\n            return finalResult;\n        });\n    }\n\n    public async runSimpleTool(\n        toolName: string,\n        actionName: string,\n        input: Record<string, any>,\n        token: CancellationToken\n    ): Promise<WorkflowStepResult> {\n        const workflow = this.workflowFactory.createSimpleToolWorkflow(toolName, actionName, input);\n        return await (workflow as any).execute?.(workflow.getContext()) || { success: false, error: 'Workflow execution not supported' };\n    }\n\n    private async createWorkflowPlan(goal: string, visualContext: MultimodalData[] | undefined, token: CancellationToken): Promise<z.infer<typeof WorkflowPlanSchema>> {\n        const relevantMemories = await this.services.memoryManager.searchMemories({ query: goal, limit: 5 });\n        const toolDocs = this.services.toolRegistry.getAllTools().map(t => t.getDocumentation()).join('\\n---\\n');\n        const promptParts: LanguageModelPromptPart[] = [];\n        const textualPrompt = `You are a world-class AI orchestrator. Your task is to create a JSON workflow plan to achieve the user's GOAL.\\n\\nGOAL: \"${goal}\"\\n\\nRELEVANT MEMORIES (for context):\\n${relevantMemories.length > 0 ? (relevantMemories as any[]).map(m => `- ${m.content}`).join('\\n') : 'None'}\\n\\nAVAILABLE TOOLS:\\n${toolDocs}\\n\\nINSTRUCTIONS:\\n1. Think step-by-step to decompose the goal into a sequence of tool calls.\\n2. Define the workflow as a JSON object strictly matching this schema: { \"thought\": \"string\", \"steps\": [{ \"id\": \"string\", \"tool_name\": \"string\", \"action_name\": \"string\", \"input\": \"object\" }] }\\n3. To use an output from a previous step, use the syntax \"$stepId.output\".\\n4. If images are provided, incorporate your analysis of them into the plan.\\n5. Ensure the plan is logical and directly works towards solving the user's GOAL.\\n\\nRESPONSE (JSON object only):`;\n        if (visualContext?.length) {\n            promptParts.push({ type: 'text', text: 'Analyze image(s)...' });\n            visualContext.forEach(image => { promptParts.push({ type: 'image', source: { type: 'base64', media_type: image.mimeType, data: image.base64Data } }); });\n        }\n        promptParts.push({ type: 'text', text: textualPrompt });\n        const responseJson = await this.llm.generateResponse(promptParts, { cancellationToken: token, jsonOutput: true });\n        return WorkflowPlanSchema.parse(JSON.parse(responseJson));\n    }\n\n    /**\n     * Simplified workflow execution for when the full workflow engine is not available\n     */\n    private async executeWorkflowSimple(workflow: Workflow): Promise<WorkflowStepResult> {\n        const definition = workflow.getDefinition();\n        const context = workflow.getContext();\n\n        // Use workflowManager for advanced workflow coordination if available\n        const useAdvancedWorkflow = (workflowManager as any)?.coordinateWorkflowExecution;\n        if (useAdvancedWorkflow) {\n            try {\n                // Leverage workflowManager for enhanced execution\n                const enhancedResult = await (workflowManager as any).coordinateWorkflowExecution(\n                    definition,\n                    context,\n                    {\n                        enableParallelExecution: false,\n                        enableErrorRecovery: true,\n                        enableProgressTracking: true\n                    }\n                );\n\n                if (enhancedResult) {\n                    return {\n                        success: enhancedResult.success,\n                        output: enhancedResult.output,\n                        error: enhancedResult.error\n                    };\n                }\n            } catch (error) {\n                // Fall back to simple execution if advanced features fail\n                this.services.logger.warn('Advanced workflow execution failed, falling back to simple execution', { error: error instanceof Error ? error.message : String(error) });\n            }\n        }\n\n        // Execute steps sequentially\n        for (let i = 0; i < definition.steps.length; i++) {\n            const step = definition.steps[i];\n            const startTime = Date.now();\n\n            // Record step start in history\n            context.history.push({\n                stepId: step.id,\n                startTime: new Date(startTime),\n                result: undefined\n            });\n\n            try {\n                // Execute the step\n                const result = await step.execute(context);\n\n                // Record step completion\n                const historyEntry = context.history[context.history.length - 1];\n                historyEntry.endTime = new Date();\n                historyEntry.result = result;\n\n                // Store output in context if successful\n                if (result.success && result.output !== undefined) {\n                    context.outputs[step.id] = result.output;\n                }\n\n                // If step failed, return failure result\n                if (!result.success) {\n                    return {\n                        success: false,\n                        error: result.error || `Step '${step.name}' failed`\n                    };\n                }\n\n                // If step specifies next step, jump to it\n                if (result.nextStepId) {\n                    const nextIndex = definition.steps.findIndex(s => s.id === result.nextStepId);\n                    if (nextIndex !== -1) {\n                        i = nextIndex - 1; // Will be incremented by loop\n                    }\n                }\n            } catch (error: unknown) {\n                // Record error in history\n                const historyEntry = context.history[context.history.length - 1];\n                historyEntry.endTime = new Date();\n                historyEntry.result = {\n                    success: false,\n                    error: error instanceof Error ? error.message : String(error)\n                };\n\n                return {\n                    success: false,\n                    error: error instanceof Error ? error.message : String(error)\n                };\n            }\n        }\n\n        // All steps completed successfully\n        return {\n            success: true,\n            output: context.outputs\n        };\n    }\n}\n\n// #endregion\n\n// #region PART 4: Production Service Implementations\n\nexport class VSCodePermissionManager implements IPermissionManager {\n    private context!: ExtensionContext; private readonly GRANTED_KEY = 'ai-agent.grantedPermissions';\n    public initialize(context: ExtensionContext) { this.context = context; }\n    public async requestPermissions(permissions: readonly ToolPermission[], rationale: string): Promise<boolean> {\n        if (!permissions.length) return true;\n        const granted = new Set(this.context.globalState.get<ToolPermission[]>(this.GRANTED_KEY, []));\n        const needed = permissions.filter(p => !granted.has(p));\n        if (!needed.length) return true;\n        const decision = await vscode.window.showWarningMessage(`${rationale} Requires: ${needed.join(', ')}. Grant?`, { modal: true }, 'Grant');\n        if (decision === 'Grant') { needed.forEach(p => granted.add(p)); await this.context.globalState.update(this.GRANTED_KEY, Array.from(granted)); return true; }\n        return false;\n    }\n}\n\nexport class StaticToolRegistry implements IToolRegistry {\n    private tools = new Map<string, ITool>();\n    public async initialize(services: IServiceContainer): Promise<void> { }\n    public getTool(toolName: string): ITool | undefined { return this.tools.get(toolName); }\n    public getAllTools(): readonly ITool[] { return Array.from(this.tools.values()); }\n}\n\nexport abstract class BaseLanguageModel implements ILanguageModel {\n    protected readonly logger: ILogger; constructor(services: IServiceContainer) { this.logger = services.logger; }\n    abstract generateResponse(prompt: LanguageModelPrompt, options: { cancellationToken: CancellationToken; jsonOutput: boolean; }): Promise<string>;\n}\n\nexport class OpenAIModel extends BaseLanguageModel {\n    public async generateResponse(prompt: LanguageModelPrompt, options: { cancellationToken: CancellationToken; jsonOutput: boolean; }): Promise<string> {\n        const config = vscode.workspace.getConfiguration('aiAgent.openAI'); const apiKey = config.get<string>('apiKey'); const baseUrl = config.get<string>('baseUrl', 'https://api.openai.com/v1');\n        if (!apiKey) throw new Error('OpenAI API key is not configured.');\n        const controller = new AbortController(); const registration = options.cancellationToken.onCancellationRequested(() => controller.abort());\n\n        // Log the request for debugging using the inherited logger\n        this.logger.info('Generating response with OpenAI', { jsonOutput: options.jsonOutput, promptLength: prompt.length });\n\n        try {\n            const body: any = { model: config.get<string>('model', 'gpt-4-turbo'), messages: [{ role: 'user', content: prompt }], temperature: 0.1, };\n            if (options.jsonOutput) body.response_format = { 'type': 'json_object' };\n            const response = await fetch(`${baseUrl}/chat/completions`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${apiKey}` }, body: JSON.stringify(body), signal: controller.signal });\n            if (!response.ok) { const errorBody = await response.text(); throw new Error(`API Error ${response.status}: ${errorBody}`); }\n            const data = await response.json() as any;\n\n            // Log successful response using inherited logger\n            this.logger.debug('OpenAI response received', { contentLength: data.choices[0]?.message?.content?.length });\n\n            return data.choices[0]?.message?.content ?? '';\n        } catch (error) {\n            if (error instanceof Error && error.name === 'AbortError') throw new OperationCancelledError();\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            this.logger.error(errorMessage, { message: 'OpenAI request failed.' });\n            throw error;\n        } finally {\n            registration.dispose();\n        }\n    }\n}\n\nexport class OllamaModel extends BaseLanguageModel {\n    public async generateResponse(prompt: LanguageModelPrompt, options: { cancellationToken: CancellationToken; jsonOutput: boolean; }): Promise<string> {\n        const config = vscode.workspace.getConfiguration('aiAgent.ollama'); const model = config.get<string>('model'); if (!model) throw new Error('Ollama model not configured.');\n        this.logger.info('Using Ollama.', { baseUrl: config.get<string>('baseUrl', 'http://localhost:11434'), model });\n        throw new NotImplementedError('Ollama client not implemented.');\n    }\n}\n\nexport class LMStudioModel extends BaseLanguageModel {\n    public async generateResponse(prompt: LanguageModelPrompt, options: { cancellationToken: CancellationToken; jsonOutput: boolean; }): Promise<string> {\n        const config = vscode.workspace.getConfiguration('aiAgent.lmstudio'); this.logger.info('Using LM Studio.', { baseUrl: config.get<string>('baseUrl', 'http://localhost:1234/v1') });\n        throw new NotImplementedError('LM Studio client not implemented.');\n    }\n}\n\nexport class LanguageModelProvider implements ILanguageModelProvider {\n    private activeModel!: ILanguageModel;\n    public initialize(services: IServiceContainer): void {\n        const providerType = vscode.workspace.getConfiguration('aiAgent').get<string>('provider', 'openai');\n        switch (providerType.toLowerCase()) {\n            case 'openai': this.activeModel = new OpenAIModel(services); break;\n            case 'ollama': this.activeModel = new OllamaModel(services); break;\n            case 'lmstudio': this.activeModel = new LMStudioModel(services); break;\n            default: throw new Error(`Unsupported provider: '${providerType}'.`);\n        }\n        services.logger.info(`Active AI provider: ${providerType}`);\n    }\n    public getActiveModel(): ILanguageModel { if (!this.activeModel) throw new Error('LLM Provider not initialized.'); return this.activeModel; }\n}\n\nexport class VSCodeUIProvider implements IToolUIProvider {\n    public async showProgress<T>(\n        title: string,\n        work: (progress: vscode.Progress<{ message?: string }>, token: vscode.CancellationToken) => Thenable<T>\n    ): Promise<T> {\n        return vscode.window.withProgress(\n            {\n                location: vscode.ProgressLocation.Notification,\n                title,\n                cancellable: true\n            },\n            (progress, token) => work(progress, token)\n        );\n    }\n\n    public showInformationMessage(message: string): void {\n        vscode.window.showInformationMessage(message);\n    }\n\n    public showWarningMessage(message: string): void {\n        vscode.window.showWarningMessage(message);\n    }\n\n    public showErrorMessage(message: string | Error): void {\n        vscode.window.showErrorMessage(message instanceof Error ? message.message : message);\n    }\n}\n\nexport class VSCodeOutputChannelLogger implements ILogger {\n    private channel: OutputChannel; constructor(name: string) { this.channel = vscode.window.createOutputChannel(name); }\n    private log(l: string, m: string, meta?: object) { this.channel.appendLine(`[${l}] ${m}${meta ? ` | ${JSON.stringify(meta)}` : ''}`); }\n    public debug(m: string, meta?: object): void { this.log('DEBUG', m, meta); }\n    public info(m: string, meta?: object): void { this.log('INFO', m, meta); }\n    public warn(m: string, meta?: object): void { this.log('WARN', m, meta); }\n    public error(m: string | Error, meta?: object): void { this.log('ERROR', m instanceof Error ? m.message : m, m instanceof Error ? { ...meta, stack: m.stack } : meta); }\n}\n\nexport class VSCodeFileSystemManager implements IFileSystemManager {\n    private readonly workspaceRoot: string;\n    constructor() { const root = vscode.workspace.workspaceFolders?.[0]; if (!root) throw new Error('No workspace folder open.'); this.workspaceRoot = root.uri.fsPath; }\n    private _resolveAndValidatePath(p: string): string { const abs = path.resolve(this.workspaceRoot, p); if (!abs.startsWith(this.workspaceRoot)) throw new Error('Path traversal denied.'); return abs; }\n    public async readFile(p: string): Promise<string> { return fs.readFile(this._resolveAndValidatePath(p), 'utf-8'); }\n    public async writeFile(p: string, c: string | Buffer): Promise<void> { const full = this._resolveAndValidatePath(p); await fs.mkdir(path.dirname(full), { recursive: true }); await fs.writeFile(full, c); }\n    public async deleteFile(p: string): Promise<void> { await fs.unlink(this._resolveAndValidatePath(p)); }\n    public async createDirectory(p: string): Promise<void> { await fs.mkdir(this._resolveAndValidatePath(p), { recursive: true }); }\n}\n\nexport class VSCodeWorkspaceKnowledge implements IWorkspaceKnowledge {\n    private readonly defaultIgnore = new Set(['.git', 'node_modules', 'dist', 'build', '.vscode', '__pycache__']);\n    public async getDirectoryTree(options?: { ignorePatterns?: string[] }): Promise<DirectoryTree | null> {\n        const rootFolder = vscode.workspace.workspaceFolders?.[0]; if (!rootFolder) return null;\n        const ignoreSet = new Set([...this.defaultIgnore, ...(options?.ignorePatterns ?? [])]);\n        const buildTree = async (dirPath: string): Promise<DirectoryTree> => {\n            const entries = await fs.readdir(dirPath, { withFileTypes: true }); const children: any[] = [];\n            for (const entry of entries) {\n                if (ignoreSet.has(entry.name)) continue;\n                const fullPath = path.join(dirPath, entry.name); const relPath = path.relative(rootFolder.uri.fsPath, fullPath);\n                if (entry.isDirectory()) { children.push(await buildTree(fullPath)); } else { children.push({ path: relPath, name: entry.name, type: 'file' }); }\n            }\n            return { path: path.relative(rootFolder.uri.fsPath, dirPath), name: path.basename(dirPath), children, type: 'directory' };\n        };\n        return buildTree(rootFolder.uri.fsPath);\n    }\n    public async findFilesByPattern(p: string): Promise<Uri[]> { return vscode.workspace.findFiles(p); }\n    public async getFileContent(p: string): Promise<string> { return new VSCodeFileSystemManager().readFile(p); }\n}\n\nexport class PtyTerminalService implements ITerminalService {\n    private readonly logger: ILogger;\n    private readonly shell: string;\n    private readonly terminalStats: TerminalStats;\n    private readonly terminalConfig: TerminalConfig;\n    private currentProcess: ITerminalProcess | null = null;\n    private terminalActions: TerminalActions | null = null;\n    private currentState: TerminalState;\n\n    constructor(logger: ILogger) {\n        this.logger = logger;\n        this.shell = os.platform() === 'win32' ? 'powershell.exe' : (process.env.SHELL || 'bash');\n\n        // Initialize currentState after shell is set\n        this.currentState = {\n            isConnected: false,\n            isReady: false,\n            currentDirectory: process.cwd(),\n            shellType: this.shell,\n            dimensions: { columns: 80, rows: 24 },\n            lastCommand: '',\n            commandHistory: [],\n            isBusy: false\n        };\n\n        // Initialize terminal configuration and stats for enhanced terminal management\n        this.terminalConfig = {\n            shell: this.shell,\n            cwd: process.cwd(),\n            env: process.env,\n            dimensions: { columns: 80, rows: 24 }\n        };\n\n        this.terminalStats = {\n            totalCommands: 0,\n            totalOutputBytes: 0,\n            averageResponseTime: 0,\n            uptimeSeconds: 0,\n            errorCount: 0,\n            resizeCount: 0\n        };\n\n        // Terminal actions will be initialized when a session is created\n    }\n\n    private getWorkspaceCwd(): string {\n        return vscode.workspace.workspaceFolders?.[0]?.uri.fsPath ?? os.homedir();\n    }\n\n    private resolveCwd(cwd?: string): string {\n        const workspaceRoot = this.getWorkspaceCwd();\n        const resolved = cwd ? path.resolve(workspaceRoot, cwd) : workspaceRoot;\n        if (!resolved.startsWith(workspaceRoot)) {\n            throw new TerminalError('Terminal CWD cannot be outside the workspace.');\n        }\n        return resolved;\n    }\n\n    public async execute(command: string, args: string[], options: { cwd?: string; cancellationToken: CancellationToken; }): Promise<CommandResult> {\n        return new Promise<CommandResult>((resolve) => {\n            let stdout = '';\n            const stderr = '';\n            let wasCancelled = false;\n            const startTime = Date.now();\n\n            const cwd = this.resolveCwd(options.cwd);\n            const fullCommand = [command, ...args].join(' ');\n            const shellCmd = os.platform() === 'win32'\n                ? `cmd /c \"${fullCommand}\"`\n                : `sh -c \"${fullCommand.replace(/\"/g, '\\\\\"')}\"`;\n\n            const terminalProcess = new InteractiveSession({\n                name: 'Terminal Command',\n                shell: this.shell,\n                cwd\n            });\n\n            // Handle output\n            const dataDisposable = terminalProcess.onData(data => {\n                stdout += data;\n            });\n\n            // Handle cancellation\n            const cancelDisposable = options.cancellationToken.onCancellationRequested(() => {\n                wasCancelled = true;\n                terminalProcess.dispose();\n            });\n\n            // Handle process exit\n            const exitDisposable = terminalProcess.onExit(exitCode => {\n                dataDisposable.dispose();\n                cancelDisposable.dispose();\n                exitDisposable.dispose();\n                resolve({\n                    command: shellCmd,\n                    output: stdout,\n                    exitCode: exitCode,\n                    duration: Date.now() - startTime,\n                    success: exitCode === 0,\n                    error: wasCancelled ? 'Command was cancelled' : undefined,\n                    background: false,\n                    completedAt: new Date()\n                });\n            });\n\n            // Start the process\n            terminalProcess.initialize().then(() => {\n                return terminalProcess.writeLn(shellCmd);\n            }).catch(error => {\n                this.logger.error('Failed to initialize or write to terminal:', error);\n                resolve({\n                    command: shellCmd,\n                    exitCode: -1,\n                    output: stdout,\n                    duration: 0,\n                    success: false,\n                    error: 'Failed to initialize terminal',\n                    background: false,\n                    completedAt: new Date()\n                });\n            });\n        });\n    }\n\n    public async stream(command: string, args: string[], options: { cwd?: string; onData: (data: string) => void; cancellationToken: CancellationToken; }): Promise<{ exitCode: number }> {\n        return new Promise((resolve) => {\n            const cwd = this.resolveCwd(options.cwd);\n            const fullCommand = [command, ...args].join(' ');\n            const shellCmd = os.platform() === 'win32'\n                ? `cmd /c \"${fullCommand}\"`\n                : `sh -c \"${fullCommand.replace(/\"/g, '\\\\\"')}\"`;\n\n            const terminalProcess = new InteractiveSession({\n                name: 'Terminal Stream',\n                shell: this.shell,\n                cwd\n            });\n\n            // Forward output to the provided callback\n            const dataDisposable = terminalProcess.onData(data => {\n                options.onData(data);\n            });\n\n            // Handle cancellation\n            const cancelDisposable = options.cancellationToken.onCancellationRequested(() => {\n                terminalProcess.dispose();\n                resolve({ exitCode: -1 });\n            });\n\n            // Handle process exit\n            const exitDisposable = terminalProcess.onExit(exitCode => {\n                dataDisposable.dispose();\n                cancelDisposable.dispose();\n                exitDisposable.dispose();\n                resolve({ exitCode: exitCode });\n            });\n\n            // Start the process\n            terminalProcess.initialize().then(() => {\n                return terminalProcess.writeLn(shellCmd);\n            }).catch(error => {\n                this.logger.error('Failed to initialize or write to terminal stream:', error);\n                resolve({ exitCode: -1 });\n            });\n        });\n    }\n\n    public async createSession(options?: { cwd?: string }): Promise<IInteractiveSession> {\n        try {\n            const cwd = this.resolveCwd(options?.cwd);\n            const session = new InteractiveSession({\n                name: 'Interactive Terminal Session',\n                shell: this.shell,\n                cwd\n            });\n            this.logger.info(`Created interactive terminal session ${session.id}.`);\n            return session;\n        } catch (error: unknown) {\n            this.logger.error(`Failed to create interactive session: ${error instanceof Error ? error.message : String(error)}`);\n            throw new TerminalError(`Failed to create interactive session: ${error instanceof Error ? error.message : String(error)}`);\n        }\n    }\n}\n// #endregion\n\n// #region PART 5: Main Extension Entrypoint & Service Wiring\n\nexport class VSCodeClipboardService implements IClipboardService {\n    public async readText(): Promise<string> {\n        try {\n            return await vscode.env.clipboard.readText();\n        } catch (error) {\n            throw new Error(`Failed to read clipboard text: ${error instanceof Error ? error.message : String(error)}`);\n        }\n    }\n\n    public async readImage(): Promise<MultimodalData | null> {\n        // VS Code doesn't provide direct clipboard image access\n        // This is a placeholder for potential future implementation\n        throw new NotImplementedError('Image clipboard reading not implemented for VS Code');\n    }\n}\n\n// These classes are placeholders representing the existing services in your codebase.\n// This bootstrap function correctly integrates with them as per your report.\nclass SettingsManager { async initialize(context: ExtensionContext) { } }\nclass PromptManager { async initialize() { } }\nclass KnowledgebaseManager { async initialize() { } }\n\n\nexport async function bootstrap(context: vscode.ExtensionContext): Promise<vscode.Disposable> {\n    // 1. Core Initialization Flow (as per your report)\n    // Logger is the first and most critical service.\n    const logger = new VSCodeOutputChannelLogger('AI Agent Framework');\n    logger.info('Activating AI Agent...');\n\n    try {\n\n        // Initialize your existing services. The `try...catch` pattern with graceful\n        // degradation is applied as you specified.\n        const settingsManager = new SettingsManager();\n        try {\n            await settingsManager.initialize(context);\n            logger.info('SettingsManager initialized');\n        } catch (e) {\n            const error = e instanceof Error ? e : new Error(String(e));\n            logger.error(`SettingsManager Initialization failed: ${error.message}`, error);\n        }\n\n        // --- This is where the framework's services are bootstrapped ---\n\n        // These framework services depend only on `logger`.\n        const ui = new VSCodeUIProvider();\n        const permissionManager = new VSCodePermissionManager();\n        permissionManager.initialize(context);\n\n        // Create memory manager with proper type casting\n        const memoryManager = new MemoryManager() as IMemoryOperations & { initialize(context: ExtensionContext): Promise<void> };\n        const fileSystem = new VSCodeFileSystemManager();\n        const workspace = new VSCodeWorkspaceKnowledge();\n        const terminalService = new PtyTerminalService(logger);\n        const clipboard = new VSCodeClipboardService();\n        const toolRegistry = new StaticToolRegistry();\n        const llmProvider = new LanguageModelProvider();\n\n        // Create service container with all properties at once\n        const services: IServiceContainer = {\n            logger,\n            ui,\n            permissionManager,\n            toolRegistry,\n            memoryManager,\n            workspace,\n            fileSystem,\n            terminalService,\n            clipboard,\n            llmProvider\n        };\n\n        try {\n            await memoryManager.initialize(context);\n            logger.info('MemoryManager initialized');\n        } catch (e) {\n            const error = e instanceof Error ? e : new Error(String(e));\n            logger.error(`MemoryManager Initialization failed: ${error.message}`, error);\n            throw new Error(`Failed to initialize MemoryManager: ${error.message}`);\n        }\n\n        // Initialize services that require the full service container\n        try {\n            llmProvider.initialize(services);\n            logger.info('LanguageModelProvider initialized');\n        } catch (e) {\n            const error = e instanceof Error ? e : new Error(String(e));\n            logger.error(`LanguageModelProvider Initialization failed: ${error.message}`, error);\n        }\n\n        try {\n            await toolRegistry.initialize(services);\n            logger.info('ToolRegistry initialized');\n            // Your concrete tools would be registered here.\n        } catch (e) {\n            const error = e instanceof Error ? e : new Error(String(e));\n            logger.error(`ToolRegistry Initialization failed: ${error.message}`, error);\n        }\n\n        // Check if all critical services initialized successfully before creating the orchestrator\n        if (!services.llmProvider || !services.toolRegistry || !services.memoryManager) {\n            throw new Error('Critical services failed to initialize. Agent cannot start.');\n        }\n\n        const orchestrator = new Orchestrator(services);\n\n        // Wire up orchestrator events to the logger.\n        orchestrator.on('workflowStart', (args) => logger.info('Workflow started.', args));\n        orchestrator.on('stepComplete', (args) => logger.info(`Workflow step '${args.stepId}' completed.`, { result: args.result }));\n        orchestrator.on('workflowEnd', (args) => logger.info(`Workflow finished with status: ${args.finalContext.status}.`, { id: args.workflowId }));\n\n        // Register the main command that triggers the agent.\n        const commandDisposable = vscode.commands.registerCommand('ai-agent.run', async () => {\n            const tokenSource = new vscode.CancellationTokenSource();\n            try {\n                const goal = await vscode.window.showInputBox({ title: 'AI Agent', prompt: 'What is your goal?' });\n                if (!goal) return;\n\n                const finalResult = await orchestrator.run(goal, { token: tokenSource.token });\n\n                if (finalResult.success) {\n                    ui.showInformationMessage(`Agent task completed successfully. Final output: ${JSON.stringify(finalResult.output)}`);\n                } else {\n                    throw new WorkflowError(`Task failed. Reason: ${finalResult.error ?? 'Unknown error'}`);\n                }\n            } catch (error) {\n                if (!(error instanceof OperationCancelledError)) {\n                    logger.error(error as Error, { message: 'Orchestrator failed to run goal.' });\n                    ui.showErrorMessage(error as Error);\n                } else {\n                    logger.info('Agent task was cancelled by user.');\n                }\n            }\n        });\n\n        logger.info('AI Agent Framework bootstrapped successfully and command is registered.');\n        return commandDisposable;\n\n    } catch (error) {\n        const safeError = error instanceof Error ? error : new Error(String(error));\n        logger.error(safeError, { message: 'Fatal error during AI Agent Framework bootstrap.' });\n        // Per your report, show error after a delay.\n        setTimeout(() => vscode.window.showErrorMessage(`AI Agent Framework failed to initialize: ${safeError.message}`), 1000);\n        return { dispose: () => { } };\n    }\n}\n// #endregion\n"]}