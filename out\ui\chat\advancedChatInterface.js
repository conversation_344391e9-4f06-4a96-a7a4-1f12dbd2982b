"use strict";
/**
 * Advanced Chat Interface - Next-generation multi-modal chat
 *
 * Provides advanced chat capabilities including voice, images, diagrams,
 * chat branching, and collaborative features while integrating with existing
 * Codessa infrastructure.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedChatInterface = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../../logger");
class AdvancedChatInterface {
    supervisorAgent;
    memoryManager;
    goddessMode;
    chatViewProvider;
    disposables = [];
    // Chat state management
    activeSessions = new Map();
    currentSessionId = null;
    chatHistory = new Map();
    // Multi-modal capabilities
    voiceSettings = {
        enabled: false,
        language: 'en-US',
        voice: 'default',
        speed: 1.0,
        pitch: 1.0
    };
    // Collaborative features
    collaborativeMode = false;
    onlineParticipants = new Map();
    // UI state
    chatPanel = null;
    statusBarItem;
    constructor(supervisorAgent, memoryManager, goddessMode, chatViewProvider) {
        this.supervisorAgent = supervisorAgent;
        this.memoryManager = memoryManager;
        this.goddessMode = goddessMode;
        this.chatViewProvider = chatViewProvider;
        this.setupStatusBar();
        this.registerCommands();
        this.loadChatHistory();
    }
    setupStatusBar() {
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 200);
        this.statusBarItem.text = '$(comment-discussion) Advanced Chat';
        this.statusBarItem.command = 'codessa.openAdvancedChat';
        this.statusBarItem.tooltip = 'Open Advanced Chat Interface';
        this.statusBarItem.show();
    }
    registerCommands() {
        // Main chat commands
        const openChatCommand = vscode.commands.registerCommand('codessa.openAdvancedChat', () => {
            this.openChatInterface();
        });
        this.disposables.push(openChatCommand);
        const newSessionCommand = vscode.commands.registerCommand('codessa.newChatSession', () => {
            this.createNewSession();
        });
        this.disposables.push(newSessionCommand);
        const branchChatCommand = vscode.commands.registerCommand('codessa.branchChat', (messageId) => {
            this.branchConversation(messageId);
        });
        this.disposables.push(branchChatCommand);
        // Multi-modal commands
        const attachFileCommand = vscode.commands.registerCommand('codessa.attachFile', () => {
            this.attachFile();
        });
        this.disposables.push(attachFileCommand);
        const attachImageCommand = vscode.commands.registerCommand('codessa.attachImage', () => {
            this.attachImage();
        });
        this.disposables.push(attachImageCommand);
        const toggleVoiceCommand = vscode.commands.registerCommand('codessa.toggleVoice', () => {
            this.toggleVoiceMode();
        });
        this.disposables.push(toggleVoiceCommand);
        // Collaborative commands
        const shareSessionCommand = vscode.commands.registerCommand('codessa.shareSession', () => {
            this.shareSession();
        });
        this.disposables.push(shareSessionCommand);
        const joinSessionCommand = vscode.commands.registerCommand('codessa.joinSession', (sessionId) => {
            this.joinCollaborativeSession(sessionId);
        });
        this.disposables.push(joinSessionCommand);
    }
    /**
       * Open the advanced chat interface
       */
    openChatInterface() {
        if (this.chatPanel) {
            this.chatPanel.reveal();
            return;
        }
        this.chatPanel = vscode.window.createWebviewPanel('advancedChat', 'Codessa Advanced Chat', vscode.ViewColumn.Beside, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [
                vscode.Uri.joinPath(vscode.workspace.workspaceFolders[0].uri, 'media'),
                vscode.Uri.joinPath(vscode.workspace.workspaceFolders[0].uri, 'src', 'ui', 'chat')
            ]
        });
        this.chatPanel.webview.html = this.getAdvancedChatHTML();
        // Handle messages from webview
        this.chatPanel.webview.onDidReceiveMessage(message => {
            this.handleWebviewMessage(message);
        });
        // Handle panel disposal
        this.chatPanel.onDidDispose(() => {
            this.chatPanel = null;
        });
        // Load current session if exists
        if (this.currentSessionId) {
            const session = this.activeSessions.get(this.currentSessionId);
            if (session) {
                this.updateChatUI(session);
            }
        }
        else {
            this.createNewSession();
        }
    }
    /**
       * Create a new chat session
       */
    createNewSession(name) {
        const sessionId = `session_${Date.now()}`;
        const session = {
            id: sessionId,
            name: name || `Chat Session ${this.activeSessions.size + 1}`,
            description: 'Advanced chat session with multi-modal capabilities',
            messages: [],
            branches: [],
            activeBranchId: 'main',
            participants: [{
                    id: 'user',
                    name: 'You',
                    role: 'owner',
                    isOnline: true
                }],
            isCollaborative: false,
            createdAt: Date.now(),
            updatedAt: Date.now()
        };
        // Create main branch
        const mainBranch = {
            id: 'main',
            name: 'Main Conversation',
            description: 'Primary conversation thread',
            parentMessageId: '',
            messages: [],
            isActive: true,
            createdAt: Date.now()
        };
        session.branches.push(mainBranch);
        this.activeSessions.set(sessionId, session);
        this.currentSessionId = sessionId;
        // Add welcome message
        this.addSystemMessage(session, this.getWelcomeMessage());
        if (this.chatPanel) {
            this.updateChatUI(session);
        }
        logger_1.logger.info(`Created new advanced chat session: ${sessionId}`);
        return sessionId;
    }
    /**
       * Send a message in the current session
       */
    async sendMessage(content, type = 'text', attachments) {
        if (!this.currentSessionId) {
            this.createNewSession();
        }
        const session = this.activeSessions.get(this.currentSessionId);
        if (!session) {
            logger_1.logger.error('No active chat session found');
            return;
        }
        // Add user message
        const userMessage = {
            id: `msg_${Date.now()}`,
            role: 'user',
            content,
            timestamp: Date.now(),
            type,
            branchId: session.activeBranchId,
            metadata: {
                attachments
            }
        };
        this.addMessageToSession(session, userMessage);
        // Generate AI response
        const assistantMessage = await this.generateAIResponse(session, userMessage);
        this.addMessageToSession(session, assistantMessage);
        // Update UI
        if (this.chatPanel) {
            this.updateChatUI(session);
        }
        // Save to memory
        await this.saveChatToMemory(session);
    }
    /**
       * Generate AI response using existing Codessa infrastructure
       */
    async generateAIResponse(session, userMessage) {
        try {
            // Build context from conversation history
            const conversationContext = this.buildConversationContext(session);
            // Determine appropriate mode based on message content
            const mode = this.determineResponseMode(userMessage.content);
            // Use SupervisorAgent for response generation
            const result = await this.supervisorAgent.run({
                prompt: this.buildAIPrompt(userMessage, conversationContext),
                mode
            }, {
                workspace: {
                    workspaceFolders: vscode.workspace.workspaceFolders,
                    currentFile: vscode.window.activeTextEditor?.document.uri.toString()
                }
            });
            let responseContent = result.output || 'I apologize, but I encountered an issue processing your request.';
            // Apply Goddess Mode enhancement if active
            if (this.goddessMode.isGoddessMode()) {
                const enhanced = this.goddessMode.enhanceResponse(responseContent, {
                    taskComplexity: session.messages.length > 10 ? 'complex' : 'moderate',
                    userMood: 'engaged'
                });
                responseContent = enhanced.content;
                if (enhanced.encouragement) {
                    responseContent += `\n\n${enhanced.encouragement}`;
                }
            }
            return {
                id: `msg_${Date.now()}`,
                role: 'assistant',
                content: responseContent,
                timestamp: Date.now(),
                type: 'text',
                branchId: session.activeBranchId
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to generate AI response: ${error}`);
            return {
                id: `msg_${Date.now()}`,
                role: 'assistant',
                content: 'I apologize, but I encountered an error while processing your request. Please try again.',
                timestamp: Date.now(),
                type: 'text',
                branchId: session.activeBranchId
            };
        }
    }
    /**
       * Branch conversation from a specific message
       */
    branchConversation(messageId, branchName) {
        const session = this.activeSessions.get(this.currentSessionId);
        if (!session) {
            logger_1.logger.error('No active session for branching');
            return '';
        }
        const branchId = `branch_${Date.now()}`;
        const branch = {
            id: branchId,
            name: branchName || `Branch ${session.branches.length + 1}`,
            description: `Branched from message ${messageId}`,
            parentMessageId: messageId,
            messages: [],
            isActive: false,
            createdAt: Date.now()
        };
        // Copy messages up to the branch point
        const parentMessage = this.findMessageInSession(session, messageId);
        if (parentMessage) {
            const messageIndex = session.messages.findIndex(m => m.id === messageId);
            branch.messages = session.messages.slice(0, messageIndex + 1).map(msg => ({
                ...msg,
                branchId
            }));
        }
        session.branches.push(branch);
        session.activeBranchId = branchId;
        if (this.chatPanel) {
            this.updateChatUI(session);
        }
        logger_1.logger.info(`Created conversation branch: ${branchId}`);
        return branchId;
    }
    /**
       * Attach file to current conversation
       */
    async attachFile() {
        const fileUri = await vscode.window.showOpenDialog({
            canSelectMany: false,
            openLabel: 'Attach File',
            filters: {
                'All Files': ['*']
            }
        });
        if (fileUri && fileUri[0]) {
            const file = fileUri[0];
            const content = await vscode.workspace.fs.readFile(file);
            const attachment = {
                id: `file_${Date.now()}`,
                type: 'file',
                name: file.path.split('/').pop() || 'unknown',
                content: content.toString(),
                mimeType: this.getMimeType(file.path),
                size: content.length
            };
            // Send message with attachment
            await this.sendMessage(`I've attached a file: ${attachment.name}`, 'text', [attachment]);
        }
    }
    /**
       * Attach image to current conversation
       */
    async attachImage() {
        const imageUri = await vscode.window.showOpenDialog({
            canSelectMany: false,
            openLabel: 'Attach Image',
            filters: {
                'Images': ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg']
            }
        });
        if (imageUri && imageUri[0]) {
            const image = imageUri[0];
            const content = await vscode.workspace.fs.readFile(image);
            const base64Content = Buffer.from(content).toString('base64');
            const attachment = {
                id: `image_${Date.now()}`,
                type: 'image',
                name: image.path.split('/').pop() || 'unknown',
                content: base64Content,
                mimeType: this.getMimeType(image.path),
                size: content.length
            };
            // Send message with image attachment
            await this.sendMessage(`I've attached an image: ${attachment.name}`, 'text', [attachment]);
        }
    }
    /**
       * Toggle voice mode
       */
    toggleVoiceMode() {
        this.voiceSettings.enabled = !this.voiceSettings.enabled;
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                command: 'updateVoiceSettings',
                settings: this.voiceSettings
            });
        }
        const status = this.voiceSettings.enabled ? 'enabled' : 'disabled';
        vscode.window.showInformationMessage(`Voice mode ${status}`);
    }
    /**
       * Share current session for collaboration
       */
    async shareSession() {
        const session = this.activeSessions.get(this.currentSessionId);
        if (!session) {
            vscode.window.showWarningMessage('No active session to share');
            return;
        }
        // Generate shareable link (in real implementation, this would involve a backend service)
        const shareLink = `codessa://chat/join/${session.id}`;
        await vscode.env.clipboard.writeText(shareLink);
        vscode.window.showInformationMessage('Session link copied to clipboard!');
        session.isCollaborative = true;
        logger_1.logger.info(`Shared chat session: ${session.id}`);
    }
    /**
       * Join collaborative session
       */
    async joinCollaborativeSession(sessionId) {
        // In real implementation, this would connect to a shared session
        vscode.window.showInformationMessage(`Joining collaborative session: ${sessionId}`);
        this.collaborativeMode = true;
    }
    // Helper methods
    addMessageToSession(session, message) {
        session.messages.push(message);
        // Add to active branch
        const activeBranch = session.branches.find(b => b.id === session.activeBranchId);
        if (activeBranch) {
            activeBranch.messages.push(message);
        }
        session.updatedAt = Date.now();
    }
    addSystemMessage(session, content) {
        const systemMessage = {
            id: `sys_${Date.now()}`,
            role: 'system',
            content,
            timestamp: Date.now(),
            type: 'text',
            branchId: session.activeBranchId
        };
        this.addMessageToSession(session, systemMessage);
    }
    buildConversationContext(session) {
        const recentMessages = session.messages.slice(-10); // Last 10 messages
        return recentMessages.map(msg => `${msg.role}: ${msg.content}`).join('\n');
    }
    buildAIPrompt(userMessage, context) {
        return `
# Advanced Chat Context

## Conversation History:
${context}

## Current User Message:
${userMessage.content}

## Message Type: ${userMessage.type}

## Attachments:
${userMessage.metadata?.attachments?.map(att => `- ${att.name} (${att.type})`).join('\n') || 'None'}

## Instructions:
You are Codessa, the ultimate AI coding goddess. Respond with intelligence, personality, and helpfulness.
Consider the conversation context and any attachments when formulating your response.
        `;
    }
    determineResponseMode(content) {
        const contentLower = content.toLowerCase();
        if (contentLower.includes('debug') || contentLower.includes('error'))
            return 'debug';
        if (contentLower.includes('edit') || contentLower.includes('change'))
            return 'edit';
        if (contentLower.includes('explain') || contentLower.includes('what'))
            return 'ask';
        if (contentLower.includes('generate') || contentLower.includes('create'))
            return 'agent';
        if (contentLower.includes('refactor') || contentLower.includes('improve'))
            return 'refactor';
        return 'chat';
    }
    findMessageInSession(session, messageId) {
        return session.messages.find(m => m.id === messageId);
    }
    getMimeType(filePath) {
        const extension = filePath.toLowerCase().split('.').pop();
        const mimeTypes = {
            'txt': 'text/plain',
            'js': 'text/javascript',
            'ts': 'text/typescript',
            'json': 'application/json',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'svg': 'image/svg+xml'
        };
        return mimeTypes[extension || ''] || 'application/octet-stream';
    }
    getWelcomeMessage() {
        const goddessMode = this.goddessMode.isGoddessMode();
        if (goddessMode) {
            return '✨ Welcome to the Advanced Chat Interface! I\'m Codessa, your coding goddess. I\'m here to help you with advanced multi-modal conversations, voice interactions, and collaborative coding. What magical code shall we create together? 🌟';
        }
        else {
            return 'Welcome to the Advanced Chat Interface! I\'m Codessa, your advanced AI coding assistant. This interface supports multi-modal conversations, file attachments, voice interactions, and collaborative features. How can I help you today?';
        }
    }
    async loadChatHistory() {
        try {
            // Load chat history from memory
            const memories = await this.memoryManager.searchMemories({
                query: 'advanced-chat',
                limit: 10,
                filter: {
                    tags: ['advanced-chat']
                }
            });
            for (const memory of memories) {
                try {
                    const sessionData = JSON.parse(memory.content);
                    if (sessionData.id && sessionData.messages) {
                        this.activeSessions.set(sessionData.id, sessionData);
                    }
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to parse chat history: ${error}`);
                }
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to load chat history: ${error}`);
        }
    }
    async saveChatToMemory(session) {
        try {
            await this.memoryManager.addMemory({
                content: `Advanced chat session: ${session.name}\n\n${JSON.stringify(session)}`,
                metadata: {
                    source: 'conversation',
                    type: 'conversation',
                    tags: ['advanced-chat', 'conversation', session.id]
                }
            });
        }
        catch (error) {
            logger_1.logger.warn(`Failed to save chat to memory: ${error}`);
        }
    }
    updateChatUI(session) {
        if (!this.chatPanel)
            return;
        this.chatPanel.webview.postMessage({
            command: 'updateSession',
            session: {
                id: session.id,
                name: session.name,
                messages: session.messages,
                branches: session.branches,
                activeBranchId: session.activeBranchId,
                isCollaborative: session.isCollaborative,
                participants: session.participants
            },
            voiceSettings: this.voiceSettings
        });
    }
    handleWebviewMessage(message) {
        switch (message.command) {
            case 'sendMessage':
                this.sendMessage(message.content, message.type, message.attachments);
                break;
            case 'branchConversation':
                this.branchConversation(message.messageId, message.branchName);
                break;
            case 'switchBranch':
                this.switchBranch(message.branchId);
                break;
            case 'attachFile':
                this.attachFile();
                break;
            case 'attachImage':
                this.attachImage();
                break;
            case 'toggleVoice':
                this.toggleVoiceMode();
                break;
            case 'shareSession':
                this.shareSession();
                break;
            case 'newSession':
                this.createNewSession(message.name);
                break;
        }
    }
    switchBranch(branchId) {
        const session = this.activeSessions.get(this.currentSessionId);
        if (session) {
            session.activeBranchId = branchId;
            this.updateChatUI(session);
        }
    }
    getAdvancedChatHTML() {
        // This would return comprehensive HTML for the advanced chat interface
        // For brevity, returning a placeholder that would be expanded
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Advanced Chat</title>
            <style>
                /* Advanced chat styles would go here */
                body { font-family: var(--vscode-font-family); }
                .chat-container { display: flex; flex-direction: column; height: 100vh; }
                /* More styles... */
            </style>
        </head>
        <body>
            <div class="chat-container">
                <div class="chat-header">
                    <h2>🚀 Advanced Chat Interface</h2>
                    <div class="chat-controls">
                        <button onclick="newSession()">New Session</button>
                        <button onclick="attachFile()">📎 File</button>
                        <button onclick="attachImage()">🖼️ Image</button>
                        <button onclick="toggleVoice()">🎤 Voice</button>
                        <button onclick="shareSession()">🔗 Share</button>
                    </div>
                </div>
                <div class="chat-messages" id="chatMessages"></div>
                <div class="chat-input">
                    <input type="text" id="messageInput" placeholder="Type your message..." />
                    <button onclick="sendMessage()">Send</button>
                </div>
            </div>
            <script>
                const vscode = acquireVsCodeApi();
                // Advanced chat JavaScript would go here
                function sendMessage() {
                    const input = document.getElementById('messageInput');
                    vscode.postMessage({
                        command: 'sendMessage',
                        content: input.value,
                        type: 'text'
                    });
                    input.value = '';
                }
                // More JavaScript functions...
            </script>
        </body>
        </html>
        `;
    }
    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.statusBarItem.dispose();
        if (this.chatPanel) {
            this.chatPanel.dispose();
        }
        this.activeSessions.clear();
        this.chatHistory.clear();
    }
}
exports.AdvancedChatInterface = AdvancedChatInterface;
//# sourceMappingURL=advancedChatInterface.js.map