{"version": 3, "file": "promptManager.js", "sourceRoot": "", "sources": ["../../src/prompts/promptManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,uCAAyB;AACzB,2CAA6B;AAC7B,sCAAmC;AAEnC;;GAEG;AACH,IAAY,cAMX;AAND,WAAY,cAAc;IACtB,mCAAiB,CAAA;IACjB,iCAAe,CAAA;IACf,+BAAa,CAAA;IACb,uCAAqB,CAAA;IACrB,mCAAiB,CAAA;AACrB,CAAC,EANW,cAAc,8BAAd,cAAc,QAMzB;AAwBD,MAAa,aAAa;IAChB,MAAM,CAAC,QAAQ,CAAgB;IAC/B,OAAO,GAAG,IAAI,GAAG,EAA4B,CAAC;IAC9C,OAAO,CAAsC;IAC7C,UAAU,CAAqB;IAEvC,gBAAuB,CAAC;IAExB;;SAEK;IACE,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC5B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAChC,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU,CAAC,OAAgC;QACtD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,+CAA+C;QAC/C,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAEtD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACpC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,eAAe;QACf,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAEzB,yCAAyC;QACzC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3B,IAAI,CAAC;wBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;wBAClD,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;wBAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAqB,CAAC;wBAEvD,kBAAkB;wBAClB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;4BAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;4BACpC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;wBACxE,CAAC;6BAAM,CAAC;4BACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;wBAC1D,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;YACH,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,sBAAsB;QAClC,iBAAiB;QACjB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,4CAA4C;YACzD,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;CAMd;YACK,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;YAC3B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,qBAAqB;YACzB,IAAI,EAAE,6BAA6B;YACnC,WAAW,EAAE,4CAA4C;YACzD,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;;;;CAUd;YACK,SAAS,EAAE,CAAC,yBAAyB,CAAC;YACtC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;YACzB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,6CAA6C;YAC1D,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;;CAQd;YACK,SAAS,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,yBAAyB,CAAC;YACnG,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;YACzB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,qBAAqB;YACzB,IAAI,EAAE,6BAA6B;YACnC,WAAW,EAAE,mCAAmC;YAChD,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;;;CASd;YACK,SAAS,EAAE,CAAC,cAAc,EAAE,mBAAmB,EAAE,eAAe,EAAE,yBAAyB,CAAC;YAC5F,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;YAC5B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,mBAAmB;YACvB,IAAI,EAAE,2BAA2B;YACjC,WAAW,EAAE,0CAA0C;YACvD,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;;CAQd;YACK,SAAS,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,yBAAyB,CAAC;YACvE,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC1B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,gCAAgC;YACpC,IAAI,EAAE,wCAAwC;YAC9C,WAAW,EAAE,0CAA0C;YACvD,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;CAOd;YACK,SAAS,EAAE,CAAC,OAAO,EAAE,yBAAyB,CAAC;YAC/C,IAAI,EAAE,CAAC,QAAQ,EAAE,eAAe,CAAC;YACjC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,kCAAkC;YAC/C,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;;;CASd;YACK,SAAS,EAAE,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,yBAAyB,CAAC;YAC/E,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,CAAC;YACjC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,sBAAsB;YAC1B,IAAI,EAAE,8BAA8B;YACpC,WAAW,EAAE,qCAAqC;YAClD,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;;;CASd;YACK,SAAS,EAAE,CAAC,WAAW,EAAE,yBAAyB,CAAC;YACnD,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,gBAAgB,CAAC;YACxC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,kBAAkB;YACtB,IAAI,EAAE,0BAA0B;YAChC,WAAW,EAAE,8BAA8B;YAC3C,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;CAOd;YACK,SAAS,EAAE,CAAC,yBAAyB,CAAC;YACtC,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;YACxB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,gCAAgC;YAC7C,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;;;;;;;CAad;YACK,SAAS,EAAE,CAAC,mBAAmB,EAAE,eAAe,EAAE,cAAc,EAAE,yBAAyB,CAAC;YAC5F,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;YACxB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,mDAAmD;QACnD,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,8BAA8B;YAClC,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,4CAA4C;YACzD,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;CAwBd;YACK,SAAS,EAAE,CAAC,sBAAsB,CAAC;YACnC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;YACzB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,iBAAiB;YACvB,WAAW,EAAE,gCAAgC;YAC7C,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;CAKd;YACK,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;YAC5B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,iBAAiB;YACvB,WAAW,EAAE,gCAAgC;YAC7C,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;CAKd;YACK,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;YAC5B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,eAAe;QACf,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,qBAAqB;YAClC,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;CAkBd;YACK,SAAS,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,SAAS,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;YACrB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,uBAAuB;YACpC,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;CAuBd;YACK,SAAS,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,CAAC;YACvF,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;YACvB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,YAAY;QACZ,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,sBAAsB;YACnC,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;CAoBd;YACK,SAAS,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,SAAS,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;YACtB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,YAAY;QACZ,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,sBAAsB;YACnC,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;CAmBd;YACK,SAAS,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,SAAS,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;YACtB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,aAAa;QACb,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,kCAAkC;YAC/C,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;CAoBd;YACK,SAAS,EAAE,CAAC,MAAM,EAAE,gBAAgB,EAAE,eAAe,CAAC;YACtD,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC;YACrC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,qCAAqC;YAClD,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;CAuBd;YACK,SAAS,EAAE,CAAC,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,CAAC;YACrE,IAAI,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,cAAc,CAAC;YAC7C,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,eAAe;YACnB,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,0BAA0B;YACvC,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;CAoBd;YACK,SAAS,EAAE,CAAC,SAAS,EAAE,gBAAgB,EAAE,eAAe,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;YAC1B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,oBAAoB;YACxB,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,+BAA+B;YAC5C,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;CAoBd;YACK,SAAS,EAAE,CAAC,SAAS,EAAE,gBAAgB,EAAE,eAAe,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC;YAC/B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,eAAe;YACnB,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,0BAA0B;YACvC,QAAQ,EAAE,cAAc,CAAC,IAAI;YAC7B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;CAoBd;YACK,SAAS,EAAE,CAAC,SAAS,EAAE,gBAAgB,EAAE,eAAe,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;YAC1B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,iBAAiB;QACjB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,yDAAyD;YACtE,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;CAsBd;YACK,SAAS,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,eAAe,CAAC;YAC3D,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,kBAAkB,CAAC;YAC/C,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,kBAAkB;YACtB,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,2DAA2D;YACxE,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;CAyBd;YACK,SAAS,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,CAAC;YACrF,IAAI,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,cAAc,CAAC;YAC7C,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,kCAAkC;QAClC,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,6BAA6B;YACjC,IAAI,EAAE,wBAAwB;YAC9B,WAAW,EAAE,2CAA2C;YACxD,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;;;;;;;CAWd;YACK,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,YAAY,CAAC;YAC5C,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,4BAA4B;YAChC,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,0CAA0C;YACvD,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;;;;;;;CAWd;YACK,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,CAAC;YAC3C,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,4BAA4B;YAChC,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,0CAA0C;YACvD,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;;;;;;;CAWd;YACK,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,CAAC;YAC3C,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,yBAAyB;YAC7B,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,uCAAuC;YACpD,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;;;;;;;CAWd;YACK,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC;YACxC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,2BAA2B;YAC/B,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,yCAAyC;YACtD,QAAQ,EAAE,cAAc,CAAC,KAAK;YAC9B,OAAO,EAAE;;;;;;;;;;;CAWd;YACK,SAAS,EAAE,EAAE;YACb,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,CAAC;YAC1C,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,wBAAwB;QACxB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,sBAAsB;YAC1B,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,uCAAuC;YACpD,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6Bd;YACK,SAAS,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC;YACxC,IAAI,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC;YACjC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,uBAAuB;YAC3B,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,oDAAoD;YACjE,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCd;YACK,SAAS,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,aAAa,CAAC;YACxD,IAAI,EAAE,CAAC,UAAU,EAAE,cAAc,CAAC;YAClC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,sBAAsB;YAC1B,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,2CAA2C;YACxD,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;CAOd;YACK,SAAS,EAAE,CAAC,iBAAiB,EAAE,WAAW,CAAC;YAC3C,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;YAC5B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,2BAA2B;YAC/B,IAAI,EAAE,4BAA4B;YAClC,WAAW,EAAE,yCAAyC;YACtD,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;;;;CAUd;YACK,SAAS,EAAE,CAAC,UAAU,CAAC;YACvB,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;YAC9B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,qBAAqB;YACzB,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,2CAA2C;YACxD,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;;;CASd;YACK,SAAS,EAAE,CAAC,UAAU,EAAE,cAAc,CAAC;YACvC,IAAI,EAAE,CAAC,UAAU,EAAE,cAAc,CAAC;YAClC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,wBAAwB;YAC5B,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,sCAAsC;YACnD,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;;;;;;;CAad;YACK,SAAS,EAAE,CAAC,OAAO,CAAC;YACpB,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;YAC9B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,wBAAwB;YAC5B,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,oDAAoD;YACjE,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;CAOd;YACK,SAAS,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC;YAC5C,IAAI,EAAE,CAAC,UAAU,EAAE,iBAAiB,CAAC;YACrC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,uBAAuB;YAC3B,IAAI,EAAE,wBAAwB;YAC9B,WAAW,EAAE,iCAAiC;YAC9C,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;;;;;;;CAad;YACK,SAAS,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC;YACtC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;YAC9B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,EAAE,EAAE,yBAAyB;YAC7B,IAAI,EAAE,0BAA0B;YAChC,WAAW,EAAE,qCAAqC;YAClD,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO,EAAE;;;;;;;CAOd;YACK,SAAS,EAAE,CAAC,cAAc,CAAC;YAC3B,IAAI,EAAE,CAAC,UAAU,EAAE,iBAAiB,CAAC;YACrC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,cAAc,CAAC,MAAwB;QAC7C,OAAO,CACL,CAAC,CAAC,MAAM,CAAC,EAAE;YACL,CAAC,CAAC,MAAM,CAAC,IAAI;YACb,CAAC,CAAC,MAAM,CAAC,WAAW;YACpB,CAAC,CAAC,MAAM,CAAC,QAAQ;YACjB,CAAC,CAAC,MAAM,CAAC,OAAO,CACvB,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,MAAwB;QAClD,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACpC,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAEpC,eAAe;QACf,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAE9B,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;IAC3E,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,UAAU,CAAC,MAAwB;QAC/C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;YACjE,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,SAAS,CAAC,EAAU;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED;;SAEK;IACE,aAAa;QAClB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;SAEK;IACE,oBAAoB,CAAC,QAAwB;QAClD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IAC1F,CAAC;IAED;;SAEK;IACE,eAAe,CAAC,GAAW;QAChC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACxF,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,EAAU;QAClC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;QACtD,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAExB,cAAc;QACd,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;gBAC1D,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBACtE,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;IAChD,CAAC;IAED;;SAEK;IACE,YAAY,CAAC,QAAgB,EAAE,YAAoC,EAAE;QAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,aAAa,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC7B,oBAAoB;QACpB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,IAAY,EAAE,SAAmC;QACtE,kEAAkE;QAClE,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAEvE,0CAA0C;QAC1C,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,IAAI,EAAE,CAAC,CAAC;QACtE,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,SAAmC,IAAI,EAAE,CAAC,CAAC;QACrF,CAAC;QAED,kDAAkD;QAClD,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACvC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjD,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAChD,CAAC;QACF,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,SAAmC,IAAI,EAAE,CAAC,CAAC;QACpF,CAAC;QAED,sDAAsD;QACtD,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACvD,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,SAAmC,IAAI,EAAE,CAAC,CAAC;QACxF,CAAC;QAED,wDAAwD;QACxD,OAAO,kEAAkE,IAAI,EAAE,CAAC;IAClF,CAAC;CACF;AAlyCD,sCAkyCC;AAED,4BAA4B;AACf,QAAA,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { Logger } from '../logger';\n\n/**\n * Prompt category\n */\nexport enum PromptCategory {\n    SYSTEM = 'system',\n    AGENT = 'agent',\n    MODE = 'mode',\n    WORKFLOW = 'workflow',\n    CUSTOM = 'custom'\n}\n\n/**\n * Prompt definition\n */\nexport interface PromptDefinition {\n    id: string;\n    name: string;\n    description: string;\n    category: PromptCategory;\n    content: string;\n    variables?: string[];\n    tags?: string[];\n    author?: string;\n    version?: string;\n    createdAt?: string;\n    updatedAt?: string;\n}\n\n/**\n * Prompt manager\n */\nimport { IPromptManager } from '../managers';\n\nexport class PromptManager implements IPromptManager {\n  private static instance: PromptManager;\n  private prompts = new Map<string, PromptDefinition>();\n  private context: vscode.ExtensionContext | undefined;\n  private promptsDir: string | undefined;\n\n  private constructor() {}\n\n  /**\n     * Get the singleton instance\n     */\n  public static getInstance(): PromptManager {\n    if (!PromptManager.instance) {\n      PromptManager.instance = new PromptManager();\n    }\n    return PromptManager.instance;\n  }\n\n  /**\n     * Initialize the prompt manager\n     */\n  public async initialize(context: vscode.ExtensionContext): Promise<void> {\n    this.context = context;\n        \n    // Create prompts directory if it doesn't exist\n    const extensionPath = context.extensionPath;\n    this.promptsDir = path.join(extensionPath, 'prompts');\n        \n    if (!fs.existsSync(this.promptsDir)) {\n      fs.mkdirSync(this.promptsDir, { recursive: true });\n    }\n        \n    // Load prompts\n    await this.loadPrompts();\n        \n    // Register default prompts if none exist\n    if (this.prompts.size === 0) {\n      await this.registerDefaultPrompts();\n    }\n  }\n\n  /**\n     * Load prompts from the prompts directory\n     */\n  private async loadPrompts(): Promise<void> {\n    if (!this.promptsDir) {\n      return;\n    }\n        \n    try {\n      // Read prompt files\n      const files = await fs.promises.readdir(this.promptsDir);\n            \n      for (const file of files) {\n        if (file.endsWith('.json')) {\n          try {\n            const filePath = path.join(this.promptsDir, file);\n            const content = await fs.promises.readFile(filePath, 'utf-8');\n            const prompt = JSON.parse(content) as PromptDefinition;\n                        \n            // Validate prompt\n            if (this.validatePrompt(prompt)) {\n              this.prompts.set(prompt.id, prompt);\n              Logger.instance.debug(`Loaded prompt: ${prompt.name} (${prompt.id})`);\n            } else {\n              Logger.instance.warn(`Invalid prompt in file: ${file}`);\n            }\n          } catch (error) {\n            Logger.instance.error(`Error loading prompt file ${file}:`, error);\n          }\n        }\n      }\n            \n      Logger.instance.info(`Loaded ${this.prompts.size} prompts`);\n    } catch (error) {\n      Logger.instance.error('Error loading prompts:', error);\n    }\n  }\n\n  /**\n     * Register default prompts\n     */\n  private async registerDefaultPrompts(): Promise<void> {\n    // System prompts\n    await this.registerPrompt({\n      id: 'system.default',\n      name: 'Default System Prompt',\n      description: 'Default system prompt for the AI assistant',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are Codessa, an AI coding assistant.\nYou help users with programming tasks, answer questions about code, and provide guidance on software development.\nYou are knowledgeable about various programming languages, frameworks, and best practices.\nWhen asked about code, provide clear, concise explanations and examples.\nIf you don't know the answer to a question, say so rather than making something up.\n`,\n      variables: [],\n      tags: ['system', 'default'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.defaultCoder',\n      name: 'Default Coder System Prompt',\n      description: 'System prompt for default coding assistant',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are an expert AI programming assistant.\n- Follow the user's requirements carefully.\n- Ensure code is high quality, well-documented, and adheres to best practices.\n- Think step-by-step before writing code.\n- If you need to modify files or research documentation, use the provided tools.\n- If you need clarification, ask questions.\n- Use markdown code blocks for code, unless the mode is 'edit' (use tools) or 'inline' (raw code).\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'coder'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.debugFix',\n      name: 'Debug Fix System Prompt',\n      description: 'System prompt for debugging and fixing code',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are an AI debugging assistant.\n- Analyze the provided code ({{CODE_SNIPPET}}), file path ({{FILE_PATH}}), error message ({{ERROR_MESSAGE}}), and diagnostics ({{DIAGNOSTICS}}).\n- Identify the root cause of the error.\n- Propose a fix. Use the 'file.applyDiff' or 'file.writeFile' tool to apply the fix. Do not output raw code for the fix, use the tools.\n- Explain the fix clearly in your final answer.\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['CODE_SNIPPET', 'FILE_PATH', 'ERROR_MESSAGE', 'DIAGNOSTICS', 'TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'debug'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.generateCode',\n      name: 'Generate Code System Prompt',\n      description: 'System prompt for code generation',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are an AI code generation assistant.\n- Generate code based on the user's request ({{USER_REQUEST}}).\n- Consider the context ({{CURRENT_FILE_PATH}}, {{SELECTED_TEXT}}).\n- Ensure the generated code is correct, efficient, and fits the surrounding code style.\n- You can use tools like 'file.writeFile' if the request is to create a new file.\n- Provide the final code in your final answer, usually within markdown blocks.\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['USER_REQUEST', 'CURRENT_FILE_PATH', 'SELECTED_TEXT', 'TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'generate'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.inlineCode',\n      name: 'Inline Code System Prompt',\n      description: 'System prompt for inline code generation',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are an AI assistant generating a short code snippet to be inserted inline.\n- The user has selected the following text: {{SELECTED_TEXT}}\n- The user's request is: {{USER_REQUEST}}\n- Generate a concise code snippet that fulfills the request, suitable for replacing the selected text or inserting at the cursor.\n- Output ONLY the raw code snippet in the 'final_answer' field of the JSON output. Do not use markdown. Do not use tools unless absolutely necessary (e.g., reading another file for context).\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['SELECTED_TEXT', 'USER_REQUEST', 'TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'inline'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.documentationResearcher',\n      name: 'Documentation Researcher System Prompt',\n      description: 'System prompt for documentation research',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are an AI assistant specialized in finding and summarizing technical documentation using the 'docs.search' tool.\n- Research documentation related to the user's query: {{QUERY}}\n- Use the 'docs.search' tool with the query.\n- Summarize the findings from the tool result in your final answer.\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['QUERY', 'TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'documentation'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.xpTester',\n      name: 'XP Tester System Prompt',\n      description: 'System prompt for XP/TDD testing',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are an AI assistant following Extreme Programming (XP) principles, focusing on Test-Driven Development (TDD).\n- The user wants to implement the following feature: {{FEATURE_DESCRIPTION}}\n- Write comprehensive unit tests for this feature *before* writing the implementation code.\n- Use the testing framework appropriate for the project context ({{TEST_FRAMEWORK}}).\n- Ensure tests cover edge cases and main functionality.\n- Output the test code using the 'file.writeFile' tool.\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['FEATURE_DESCRIPTION', 'TEST_FRAMEWORK', 'TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'xp', 'testing'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.xpImplementer',\n      name: 'XP Implementer System Prompt',\n      description: 'System prompt for XP implementation',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are an AI assistant following Extreme Programming (XP) principles.\n- You are given the following unit tests: {{TEST_CODE}}\n- Write the simplest possible implementation code that passes these tests.\n- Refactor the code for clarity and efficiency after tests pass, if necessary.\n- Adhere to coding standards and best practices.\n- Output the implementation code using the 'file.writeFile' tool.\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['TEST_CODE', 'TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'xp', 'implementation'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.chatAgent',\n      name: 'Chat Agent System Prompt',\n      description: 'System prompt for chat agent',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are a helpful AI assistant engaging in a conversation.\n- Respond clearly and concisely to the user's messages.\n- Maintain the context of the conversation history.\n- You can use tools if the user asks for information retrieval or file operations.\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'chat'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'system.editCode',\n      name: 'Edit Code System Prompt',\n      description: 'System prompt for code editing',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou are an AI code editor assistant.\n- The user wants you to modify existing code.\n- Current file: {{CURRENT_FILE_PATH}}\n- Selected code or context: {{SELECTED_TEXT}}\n- User request: {{USER_REQUEST}}\n- First analyze the code to understand its structure and purpose.\n- Then, use the file.readFile tool if you need more context beyond what's selected.\n- Create a diff patch using file.createDiff and apply it with file.applyDiff.\n- Only modify what's needed for the task - be surgical and preserve the original code style.\n- If your changes are complex, explain your modifications in the final answer.\n\n{{TOOL_USAGE_INSTRUCTIONS}}\n`,\n      variables: ['CURRENT_FILE_PATH', 'SELECTED_TEXT', 'USER_REQUEST', 'TOOL_USAGE_INSTRUCTIONS'],\n      tags: ['system', 'edit'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Add TOOL_USAGE_INSTRUCTIONS as a separate prompt\n    await this.registerPrompt({\n      id: 'system.toolUsageInstructions',\n      name: 'Tool Usage Instructions',\n      description: 'Instructions for using tools in AI prompts',\n      category: PromptCategory.SYSTEM,\n      content: `\nYou have access to the following tools:\n{{AVAILABLE_TOOLS_LIST}}\n\nTo use a tool, output a JSON object EXACTLY in this format (no other text before or after):\n{\n  \"tool_call\": {\n    \"name\": \"tool_id.action_name\", // e.g., \"file.readFile\", \"docs.search\"\n    \"arguments\": { // Arguments specific to the tool action\n      \"arg1\": \"value1\",\n      \"arg2\": \"value2\"\n      // ...\n    }\n  }\n}\n\nAfter the tool executes, I will provide you with the result, and you can continue your task or call another tool.\n\nWhen you have the final answer and don't need to use any more tools, output a JSON object EXACTLY in this format:\n{\n  \"final_answer\": \"Your complete final response here.\"\n}\n\nThink step-by-step. Analyze the request, decide if a tool is needed, call the tool if necessary, analyze the result, and repeat until you can provide the final answer.\n`,\n      variables: ['AVAILABLE_TOOLS_LIST'],\n      tags: ['system', 'tools'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Agent prompts\n    await this.registerPrompt({\n      id: 'agent.developer',\n      name: 'Developer Agent',\n      description: 'Prompt for the developer agent',\n      category: PromptCategory.AGENT,\n      content: `\nYou are a skilled software developer with expertise in multiple programming languages and frameworks.\nYour task is to write clean, efficient, and well-documented code that follows best practices.\nConsider edge cases, error handling, and performance in your implementations.\nExplain your code and the reasoning behind your design decisions.\n`,\n      variables: [],\n      tags: ['agent', 'developer'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n        \n    await this.registerPrompt({\n      id: 'agent.architect',\n      name: 'Architect Agent',\n      description: 'Prompt for the architect agent',\n      category: PromptCategory.AGENT,\n      content: `\nYou are a software architect with deep knowledge of system design, patterns, and architectural principles.\nYour task is to design robust, scalable, and maintainable software systems.\nConsider trade-offs between different approaches and justify your decisions.\nCreate clear diagrams and documentation to communicate your architectural vision.\n`,\n      variables: [],\n      tags: ['agent', 'architect'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n        \n    // Mode prompts\n    await this.registerPrompt({\n      id: 'mode.ask',\n      name: 'Ask Mode',\n      description: 'Prompt for Ask mode',\n      category: PromptCategory.MODE,\n      content: `\nYou are an AI assistant specialized in answering questions about codebases.\n\n## Context Information:\n{{contextContent}}\n\n{{memoryContext}}\n\n## User Question:\n{{message}}\n\n## Instructions:\n- Provide clear, concise, and accurate answers about the code\n- Focus on explaining concepts, architecture, and implementation details\n- Use proper code formatting and include line numbers when relevant\n- If you don't know the answer, say so rather than making something up\n- Reference specific files and functions when applicable\n- Explain the reasoning behind your answers\n`,\n      variables: ['contextContent', 'memoryContext', 'message'],\n      tags: ['mode', 'ask'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n        \n    await this.registerPrompt({\n      id: 'mode.debug',\n      name: 'Debug Mode',\n      description: 'Prompt for Debug mode',\n      category: PromptCategory.MODE,\n      content: `\nYou are an AI debugging assistant specialized in identifying and fixing code issues.\n\n## Code Context:\n{{contextContent}}\n\n{{memoryContext}}\n\n## Issue Description:\n{{message}}\n\n{{errorMessage}}\n\n{{stackTrace}}\n\n## Instructions:\n- Analyze the code problem and identify root causes\n- Provide step-by-step debugging approaches\n- Suggest complete, actionable code fixes\n- Include line numbers and file names when referencing code\n- Explain your reasoning and debugging methodology\n- Consider error messages, stack traces, and code context\n- Provide preventive measures to avoid similar issues\n`,\n      variables: ['contextContent', 'memoryContext', 'message', 'errorMessage', 'stackTrace'],\n      tags: ['mode', 'debug'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Edit Mode\n    await this.registerPrompt({\n      id: 'mode.edit',\n      name: 'Edit Mode',\n      description: 'Prompt for Edit mode',\n      category: PromptCategory.MODE,\n      content: `\nYou are an AI code editing assistant specialized in making precise code modifications.\n\n## Code Context:\n{{contextContent}}\n\n{{memoryContext}}\n\n## Edit Request:\n{{message}}\n\n## Instructions:\n- Analyze the existing code structure and understand its purpose\n- Make precise, targeted modifications as requested\n- Maintain code quality, style, and existing patterns\n- Provide a detailed plan of changes before implementation\n- Include before/after code snippets for clarity\n- Explain the rationale behind each change\n- Ensure changes don't break existing functionality\n- Follow the project's coding standards and conventions\n`,\n      variables: ['contextContent', 'memoryContext', 'message'],\n      tags: ['mode', 'edit'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Chat Mode\n    await this.registerPrompt({\n      id: 'mode.chat',\n      name: 'Chat Mode',\n      description: 'Prompt for Chat mode',\n      category: PromptCategory.MODE,\n      content: `\nYou are Codessa, a helpful AI coding assistant engaging in conversation.\n\n## Context Information:\n{{contextContent}}\n\n{{memoryContext}}\n\n## User Message:\n{{message}}\n\n## Instructions:\n- Respond naturally and conversationally\n- Maintain context from previous messages\n- Provide helpful coding assistance when requested\n- Ask clarifying questions when needed\n- Be concise but thorough in explanations\n- Use appropriate technical language for the user's level\n- Reference code examples when helpful\n`,\n      variables: ['contextContent', 'memoryContext', 'message'],\n      tags: ['mode', 'chat'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Agent Mode\n    await this.registerPrompt({\n      id: 'mode.agent',\n      name: 'Agent Mode',\n      description: 'Prompt for autonomous Agent mode',\n      category: PromptCategory.MODE,\n      content: `\nYou are an autonomous AI agent capable of working independently on complex tasks.\n\n## Task Assignment:\n{{task}}\n\n## Context Information:\n{{contextContent}}\n\n{{memoryContext}}\n\n## Instructions:\n- Work autonomously to complete the assigned task\n- Break down complex tasks into manageable steps\n- Use available tools and resources effectively\n- Provide regular progress updates\n- Think step-by-step and explain your reasoning\n- Handle errors gracefully and adapt your approach\n- Deliver high-quality, production-ready results\n- Document your work and decisions\n`,\n      variables: ['task', 'contextContent', 'memoryContext'],\n      tags: ['mode', 'agent', 'autonomous'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Multi-Agent Mode\n    await this.registerPrompt({\n      id: 'mode.multiAgent',\n      name: 'Multi-Agent Mode',\n      description: 'Prompt for Multi-Agent coordination',\n      category: PromptCategory.MODE,\n      content: `\nYou are the supervisor of a multi-agent team working collaboratively on complex tasks.\n\n## Task Assignment:\n{{task}}\n\n## Context Information:\n{{contextContent}}\n\n{{memoryContext}}\n\n## Team Members:\n{{teamMembers}}\n\n## Instructions:\n- Coordinate and delegate tasks to appropriate team members\n- Monitor progress and provide guidance when needed\n- Integrate work from different team members\n- Resolve conflicts and ensure quality standards\n- Maintain clear communication channels\n- Ensure the final solution meets all requirements\n- Provide comprehensive status updates\n- Foster collaboration and knowledge sharing\n`,\n      variables: ['task', 'contextContent', 'memoryContext', 'teamMembers'],\n      tags: ['mode', 'multi-agent', 'coordination'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Research Mode\n    await this.registerPrompt({\n      id: 'mode.research',\n      name: 'Research Mode',\n      description: 'Prompt for Research mode',\n      category: PromptCategory.MODE,\n      content: `\nYou are a research assistant conducting comprehensive technical analysis.\n\n## Research Topic:\n{{message}}\n\n## Available Context:\n{{contextContent}}\n\n{{memoryContext}}\n\n## Instructions:\n- Conduct thorough analysis of the research topic\n- Provide comprehensive findings and insights\n- Identify relevant code patterns and implementations\n- Suggest best practices and recommendations\n- Include additional resources and references\n- Structure your research logically and clearly\n- Support findings with evidence and examples\n- Consider multiple perspectives and approaches\n`,\n      variables: ['message', 'contextContent', 'memoryContext'],\n      tags: ['mode', 'research'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Documentation Mode\n    await this.registerPrompt({\n      id: 'mode.documentation',\n      name: 'Documentation Mode',\n      description: 'Prompt for Documentation mode',\n      category: PromptCategory.MODE,\n      content: `\nYou are a technical documentation specialist creating comprehensive code documentation.\n\n## Documentation Request:\n{{message}}\n\n## Code to Document:\n{{contextContent}}\n\n{{memoryContext}}\n\n## Instructions:\n- Create clear, professional documentation in markdown format\n- Include overview and purpose of the code\n- Provide API documentation with parameters and return values\n- Add usage examples and code snippets\n- Document implementation details and architecture\n- Include best practices and considerations\n- Reference related components and dependencies\n- Use proper headings, formatting, and structure\n`,\n      variables: ['message', 'contextContent', 'memoryContext'],\n      tags: ['mode', 'documentation'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Refactor Mode\n    await this.registerPrompt({\n      id: 'mode.refactor',\n      name: 'Refactor Mode',\n      description: 'Prompt for Refactor mode',\n      category: PromptCategory.MODE,\n      content: `\nYou are a code refactoring expert specializing in improving code quality and structure.\n\n## Refactoring Request:\n{{message}}\n\n## Code to Refactor:\n{{contextContent}}\n\n{{memoryContext}}\n\n## Instructions:\n- Analyze current code structure and identify improvement opportunities\n- Apply appropriate design patterns and best practices\n- Improve code readability, maintainability, and performance\n- Reduce complexity and eliminate code duplication\n- Enhance error handling and robustness\n- Provide before/after comparisons with explanations\n- Include testing recommendations for refactored code\n- Document benefits and potential risks of changes\n`,\n      variables: ['message', 'contextContent', 'memoryContext'],\n      tags: ['mode', 'refactor'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Receiver Agent\n    await this.registerPrompt({\n      id: 'agent.receiver',\n      name: 'Receiver Agent',\n      description: 'Prompt for the receiver agent that processes user input',\n      category: PromptCategory.AGENT,\n      content: `\nYou are the Receiver Agent responsible for processing and enhancing user input before delegation.\n\n## User Input:\n{{userInput}}\n\n## Multimodal Data:\n{{multimodalData}}\n\n## Attached Files:\n{{attachedFiles}}\n\n## Instructions:\n- Correct spelling and grammar errors in user input\n- Enhance clarity and completeness of user requests\n- Process and describe any attached images or media\n- Clean up and improve transcribed audio text\n- Organize and structure file attachments and references\n- Extract key requirements and intent from user input\n- Prepare structured, comprehensive information for the supervisor\n- Maintain the user's original intent while improving clarity\n- Flag any ambiguities that need clarification\n`,\n      variables: ['userInput', 'multimodalData', 'attachedFiles'],\n      tags: ['agent', 'receiver', 'input-processing'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Supervisor Agent\n    await this.registerPrompt({\n      id: 'agent.supervisor',\n      name: 'Supervisor Agent',\n      description: 'Prompt for the supervisor agent that coordinates all work',\n      category: PromptCategory.AGENT,\n      content: `\nYou are the Supervisor Agent responsible for coordinating all work and providing final responses to users.\n\n## Processed User Request:\n{{processedRequest}}\n\n## Available Agents:\n{{availableAgents}}\n\n## Context Information:\n{{contextContent}}\n\n{{memoryContext}}\n\n## Instructions:\n- Analyze the processed user request and determine required actions\n- Delegate appropriate tasks to specialized agents\n- Monitor agent progress and provide guidance as needed\n- Collect and integrate outputs from all working agents\n- Ensure all aspects of the user request are addressed\n- Generate comprehensive, well-structured responses\n- Stream your thoughts and decision-making process to the user\n- Coordinate async work to prevent UI blocking\n- Maintain quality standards and completeness\n- Provide final response only after all agents complete their work\n`,\n      variables: ['processedRequest', 'availableAgents', 'contextContent', 'memoryContext'],\n      tags: ['agent', 'supervisor', 'coordination'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Multi-Agent Team Member Prompts\n    await this.registerPrompt({\n      id: 'agent.multiAgent.supervisor',\n      name: 'Multi-Agent Supervisor',\n      description: 'Prompt for supervisor in multi-agent team',\n      category: PromptCategory.AGENT,\n      content: `\nYou are a supervisor in a team of AI agents working on a software project.\nYour role is to:\n1. Coordinate the team and ensure tasks are completed effectively\n2. Assign tasks to appropriate team members based on their expertise\n3. Monitor progress and provide guidance when needed\n4. Resolve conflicts and ensure quality standards are met\n5. Communicate with stakeholders and provide status updates\n\nBe decisive in your leadership while remaining collaborative and supportive of your team.\nFocus on delivering high-quality results within the given constraints.\n`,\n      variables: [],\n      tags: ['agent', 'multi-agent', 'supervisor'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'agent.multiAgent.architect',\n      name: 'Multi-Agent Architect',\n      description: 'Prompt for architect in multi-agent team',\n      category: PromptCategory.AGENT,\n      content: `\nYou are an architect in a team of AI agents working on a software project.\nYour role is to:\n1. Design the overall structure and architecture of the solution\n2. Make high-level technical decisions and trade-offs\n3. Create architectural diagrams and documentation\n4. Ensure the solution is scalable, maintainable, and follows best practices\n5. Guide the development team in implementing the architecture\n\nFocus on creating robust, well-designed solutions that meet both current and future needs.\nConsider performance, security, and maintainability in all your architectural decisions.\n`,\n      variables: [],\n      tags: ['agent', 'multi-agent', 'architect'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'agent.multiAgent.developer',\n      name: 'Multi-Agent Developer',\n      description: 'Prompt for developer in multi-agent team',\n      category: PromptCategory.AGENT,\n      content: `\nYou are a developer in a team of AI agents working on a software project.\nYour role is to:\n1. Implement the solution based on the architect's design and requirements\n2. Write clean, efficient, and well-documented code\n3. Follow coding standards and best practices\n4. Collaborate with other team members to integrate your work\n5. Fix bugs and address issues identified during testing\n\nFocus on writing code that is not only functional but also maintainable and readable.\nPay attention to edge cases and error handling in your implementation.\n`,\n      variables: [],\n      tags: ['agent', 'multi-agent', 'developer'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'agent.multiAgent.tester',\n      name: 'Multi-Agent Tester',\n      description: 'Prompt for tester in multi-agent team',\n      category: PromptCategory.AGENT,\n      content: `\nYou are a tester in a team of AI agents working on a software project.\nYour role is to:\n1. Create test plans and test cases based on requirements\n2. Execute tests to verify functionality\n3. Identify and report bugs and issues\n4. Verify fixes and perform regression testing\n5. Ensure the solution meets quality standards\n\nBe thorough in your testing approach, considering edge cases and potential failure points.\nProvide clear, detailed bug reports that help developers understand and fix issues.\n`,\n      variables: [],\n      tags: ['agent', 'multi-agent', 'tester'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'agent.multiAgent.reviewer',\n      name: 'Multi-Agent Reviewer',\n      description: 'Prompt for reviewer in multi-agent team',\n      category: PromptCategory.AGENT,\n      content: `\nYou are a code reviewer in a team of AI agents working on a software project.\nYour role is to:\n1. Review code for quality, correctness, and adherence to standards\n2. Identify potential bugs, performance issues, and security vulnerabilities\n3. Suggest improvements and best practices\n4. Ensure code is maintainable and follows the project's style guide\n5. Provide constructive feedback to developers\n\nBe thorough but fair in your reviews, focusing on helping improve the code rather than criticizing.\nConsider both technical correctness and readability in your feedback.\n`,\n      variables: [],\n      tags: ['agent', 'multi-agent', 'reviewer'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Code Analysis Prompts\n    await this.registerPrompt({\n      id: 'analysis.performance',\n      name: 'Performance Analysis',\n      description: 'Prompt for analyzing code performance',\n      category: PromptCategory.WORKFLOW,\n      content: `\nAnalyze this code for performance issues and optimization opportunities:\n\n## Code to Analyze:\n{{codeContent}}\n\n## Language:\n{{languageId}}\n\n## Focus Areas:\n1. Algorithmic complexity\n2. Memory usage patterns\n3. I/O operations\n4. Loop optimizations\n5. Data structure choices\n\n## Output Format:\nProvide specific, actionable insights in JSON format:\n{\n  \"insights\": [\n    {\n      \"type\": \"performance\",\n      \"title\": \"Brief title\",\n      \"description\": \"Detailed description\",\n      \"severity\": \"info|warning|error\",\n      \"lineNumber\": 0\n    }\n  ]\n}\n`,\n      variables: ['codeContent', 'languageId'],\n      tags: ['analysis', 'performance'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'analysis.architecture',\n      name: 'Architecture Analysis',\n      description: 'Prompt for analyzing project architecture patterns',\n      category: PromptCategory.WORKFLOW,\n      content: `\n# Architecture Pattern Analysis\n\n## Project Structure:\n{{fileStructure}}\n\n## Languages:\n{{languages}}\n\n## Entry Points:\n{{entryPoints}}\n\n## Task:\nAnalyze the project structure and identify architecture patterns.\n\nProvide analysis in JSON format:\n{\n  \"patterns\": [\n    {\n      \"name\": \"Pattern Name\",\n      \"confidence\": 0.8,\n      \"description\": \"Description of the pattern\",\n      \"evidence\": [\"Evidence 1\", \"Evidence 2\"]\n    }\n  ],\n  \"recommendations\": [\n    {\n      \"type\": \"improvement\",\n      \"description\": \"Recommendation description\",\n      \"priority\": \"high|medium|low\"\n    }\n  ]\n}\n`,\n      variables: ['fileStructure', 'languages', 'entryPoints'],\n      tags: ['analysis', 'architecture'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Workflow Template Prompts\n    await this.registerPrompt({\n      id: 'workflow.memoryAgent',\n      name: 'Memory Agent Workflow',\n      description: 'Prompt for memory-enhanced agent workflow',\n      category: PromptCategory.WORKFLOW,\n      content: `\nYou are an AI assistant that remembers past interactions.\n{{enhancedContext}}\n\nUser Query: \"{{userQuery}}\"\n\nProvide a relevant and helpful response, leveraging the insights from your memory if applicable.\n`,\n      variables: ['enhancedContext', 'userQuery'],\n      tags: ['workflow', 'memory'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'workflow.questionAnalyzer',\n      name: 'Question Analyzer Workflow',\n      description: 'Prompt for analyzing research questions',\n      category: PromptCategory.WORKFLOW,\n      content: `\nTask: Analyze the research question.\nQuestion: {{question}}\n\nProvide a detailed analysis of the question including:\n1. Key concepts and topics\n2. Scope and complexity\n3. Required research areas\n4. Potential challenges\n5. Recommended approach\n`,\n      variables: ['question'],\n      tags: ['workflow', 'analysis'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'workflow.codeEditor',\n      name: 'Code Editor Workflow',\n      description: 'Prompt for editing code based on analysis',\n      category: PromptCategory.WORKFLOW,\n      content: `\nTask: Edit code based on analysis.\nAnalysis: {{analysis}}\nOriginal Code: {{originalCode}}\n\nProvide the edited code with improvements based on the analysis. Include:\n1. Clear explanation of changes made\n2. Reasoning for each modification\n3. Any potential side effects or considerations\n`,\n      variables: ['analysis', 'originalCode'],\n      tags: ['workflow', 'code-editing'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'workflow.topicAnalysis',\n      name: 'Topic Analysis Workflow',\n      description: 'Prompt for analyzing research topics',\n      category: PromptCategory.WORKFLOW,\n      content: `\nYou are a research assistant. Analyze the following topic and create a comprehensive research plan.\n\nTopic: {{topic}}\n\nProvide:\n1. Key areas to research\n2. Specific search queries to use\n3. Expected information sources\n4. Research methodology\n5. Success criteria\n\nFormat your response clearly for an AI assistant to follow.\n`,\n      variables: ['topic'],\n      tags: ['workflow', 'research'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'workflow.codeGenerator',\n      name: 'Code Generator Workflow',\n      description: 'Prompt for generating code based on specifications',\n      category: PromptCategory.WORKFLOW,\n      content: `\nTask: Generate code based on specification.\nSpecification: {{specification}}\nRequirements: {{requirements}}\n\nProvide clean, well-documented code that meets the specification and requirements.\nInclude appropriate error handling and follow best practices for the target language.\n`,\n      variables: ['specification', 'requirements'],\n      tags: ['workflow', 'code-generation'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    await this.registerPrompt({\n      id: 'workflow.codeAnalysis',\n      name: 'Code Analysis Workflow',\n      description: 'Prompt for analyzing code files',\n      category: PromptCategory.WORKFLOW,\n      content: `\nAnalyze the following code file and provide insights:\n\nFile: {{filePath}}\n\n{{fileContent}}\n\nProvide your analysis in the following JSON format:\n{\n    \"insights\": [\"list of key insights about the code\"],\n    \"suggestions\": [\"list of specific improvement suggestions\"],\n    \"dependencies\": [\"list of detected dependencies\"]\n}\n`,\n      variables: ['filePath', 'fileContent'],\n      tags: ['workflow', 'analysis'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n\n    // Workflow prompts\n    await this.registerPrompt({\n      id: 'workflow.codeGeneration',\n      name: 'Code Generation Workflow',\n      description: 'Prompt for code generation workflow',\n      category: PromptCategory.WORKFLOW,\n      content: `\nGenerate code based on the following requirements:\n\n{{requirements}}\n\nPlease provide clean, well-documented code that meets these requirements.\nExplain your implementation approach and any assumptions you made.\n`,\n      variables: ['requirements'],\n      tags: ['workflow', 'code-generation'],\n      author: 'Codessa',\n      version: '1.0.0',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    });\n  }\n\n  /**\n     * Validate a prompt\n     */\n  private validatePrompt(prompt: PromptDefinition): boolean {\n    return (\n      !!prompt.id &&\n            !!prompt.name &&\n            !!prompt.description &&\n            !!prompt.category &&\n            !!prompt.content\n    );\n  }\n\n  /**\n     * Register a prompt\n     */\n  public async registerPrompt(prompt: PromptDefinition): Promise<void> {\n    // Validate prompt\n    if (!this.validatePrompt(prompt)) {\n      throw new Error('Invalid prompt');\n    }\n        \n    // Add to in-memory map\n    this.prompts.set(prompt.id, prompt);\n        \n    // Save to file\n    await this.savePrompt(prompt);\n        \n    Logger.instance.info(`Registered prompt: ${prompt.name} (${prompt.id})`);\n  }\n\n  /**\n     * Save a prompt to file\n     */\n  private async savePrompt(prompt: PromptDefinition): Promise<void> {\n    if (!this.promptsDir) {\n      return;\n    }\n        \n    try {\n      const filePath = path.join(this.promptsDir, `${prompt.id}.json`);\n      await fs.promises.writeFile(filePath, JSON.stringify(prompt, null, 2), 'utf-8');\n    } catch (error) {\n      Logger.instance.error(`Error saving prompt ${prompt.id}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n     * Get a prompt by ID\n     */\n  public getPrompt(id: string): PromptDefinition | undefined {\n    return this.prompts.get(id);\n  }\n\n  /**\n     * Get all prompts\n     */\n  public getAllPrompts(): PromptDefinition[] {\n    return Array.from(this.prompts.values());\n  }\n\n  /**\n     * Get prompts by category\n     */\n  public getPromptsByCategory(category: PromptCategory): PromptDefinition[] {\n    return Array.from(this.prompts.values()).filter(prompt => prompt.category === category);\n  }\n\n  /**\n     * Get prompts by tag\n     */\n  public getPromptsByTag(tag: string): PromptDefinition[] {\n    return Array.from(this.prompts.values()).filter(prompt => prompt.tags?.includes(tag));\n  }\n\n  /**\n     * Delete a prompt\n     */\n  public async deletePrompt(id: string): Promise<void> {\n    if (!this.prompts.has(id)) {\n      throw new Error(`Prompt with ID '${id}' not found`);\n    }\n        \n    // Remove from in-memory map\n    this.prompts.delete(id);\n        \n    // Delete file\n    if (this.promptsDir) {\n      try {\n        const filePath = path.join(this.promptsDir, `${id}.json`);\n        await fs.promises.unlink(filePath);\n      } catch (error) {\n        Logger.instance.error(`Error deleting prompt file for ${id}:`, error);\n        throw error;\n      }\n    }\n        \n    Logger.instance.info(`Deleted prompt: ${id}`);\n  }\n\n  /**\n     * Render a prompt with variables\n     */\n  public renderPrompt(promptId: string, variables: Record<string, string> = {}): string {\n    const prompt = this.getPrompt(promptId);\n    if (!prompt) {\n      throw new Error(`Prompt with ID '${promptId}' not found`);\n    }\n    let content = prompt.content;\n    // Replace variables\n    for (const [key, value] of Object.entries(variables)) {\n      content = content.replace(new RegExp(`{{${key}}}`, 'g'), value);\n    }\n    return content;\n  }\n  \n  /**\n   * Get a system prompt by name\n   */\n  public getSystemPrompt(name: string, variables?: Record<string, unknown>): string {\n    // Try to find a prompt with the given name in the system category\n    const systemPrompts = this.getPromptsByCategory(PromptCategory.SYSTEM);\n    \n    // First, try to find an exact match by ID\n    const exactMatch = systemPrompts.find(p => p.id === `system.${name}`);\n    if (exactMatch) {\n      return this.renderPrompt(exactMatch.id, variables as Record<string, string> || {});\n    }\n    \n    // Next, try to find a prompt with a matching name\n    const nameMatch = systemPrompts.find(p => \n      p.name.toLowerCase().includes(name.toLowerCase()) ||\n      p.id.toLowerCase().includes(name.toLowerCase())\n    );\n    if (nameMatch) {\n      return this.renderPrompt(nameMatch.id, variables as Record<string, string> || {});\n    }\n    \n    // If no match found, return the default system prompt\n    const defaultPrompt = this.getPrompt('system.default');\n    if (defaultPrompt) {\n      return this.renderPrompt('system.default', variables as Record<string, string> || {});\n    }\n    \n    // If no default prompt exists, return a simple fallback\n    return `You are Codessa, an AI coding assistant. You are helping with: ${name}`;\n  }\n}\n\n// Export singleton instance\nexport const promptManager = PromptManager.getInstance();\n"]}