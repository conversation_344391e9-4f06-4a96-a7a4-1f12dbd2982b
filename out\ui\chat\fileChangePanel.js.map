{"version": 3, "file": "fileChangePanel.js", "sourceRoot": "", "sources": ["../../../src/ui/chat/fileChangePanel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,yCAAsC;AACtC,2DAAoE;AAEpE,MAAM,MAAM,GAAG,eAAM,CAAC,QAAQ,CAAC;AAE/B;;;GAGG;AACH,MAAa,eAAe;IACnB,MAAM,CAAC,YAAY,CAA8B;IACvC,KAAK,CAAsB;IAC3B,YAAY,CAAa;IAClC,WAAW,GAAwB,EAAE,CAAC;IACtC,iBAAiB,CAAoB;IACrC,aAAa,GAAgB,IAAI,GAAG,EAAE,CAAC;IACvC,aAAa,GAAgB,IAAI,GAAG,EAAE,CAAC;IACvC,UAAU,GAA+C,KAAK,CAAC;IAC/D,QAAQ,GAA0C,UAAU,CAAC;IAC7D,QAAQ,GAAiC,MAAM,CAAC;IAChD,iBAAiB,GAAG,IAAI,CAAC;IACzB,mBAAmB,GAAG,KAAK,CAAC;IAC5B,kBAAkB,CAA8B;IAChD,cAAc,GAAqC,IAAI,GAAG,EAAE,CAAC;IAE9D,MAAM,CAAC,YAAY,CAAC,YAAwB,EAAE,OAAgC;QACnF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YAC3C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEd,sCAAsC;QACtC,IAAI,eAAe,CAAC,YAAY,EAAE,CAAC;YACjC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAClD,eAAe,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,gCAAgC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,oBAAoB,EACpB,cAAc,EACd,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;gBAC1C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC;aAClD;SACF,CACF,CAAC;QAEF,eAAe,CAAC,YAAY,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IACnF,CAAC;IAED,YAAoB,KAA0B,EAAE,YAAwB,EAAE,OAAgC;QACxG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,qCAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;QACzF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE/C,yCAAyC;QACzC,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,wCAAwC;QACxC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtE,+DAA+D;QAC/D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CACpC,OAAO,CAAC,EAAE;YACR,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;gBACxB,KAAK,YAAY,CAAC,CAAC,CAAC;oBAClB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,MAAM;gBACR,CAAC;gBACD,KAAK,SAAS,CAAC,CAAC,CAAC;oBACf,IAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,MAAM;gBACR,CAAC;gBACD,KAAK,aAAa,CAAC,CAAC,CAAC;oBACnB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACzC,MAAM;gBACR,CAAC;gBACD,KAAK,UAAU,CAAC,CAAC,CAAC;oBAChB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACtC,MAAM;gBACR,CAAC;gBACD,KAAK,UAAU,CAAC,CAAC,CAAC;oBAChB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACtC,MAAM;gBACR,CAAC;gBACD,KAAK,UAAU,CAAC,CAAC,CAAC;oBAChB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACtC,MAAM;gBACR,CAAC;gBACD,KAAK,YAAY,CAAC,CAAC,CAAC;oBAClB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACxC,MAAM;gBACR,CAAC;gBACD,KAAK,YAAY,CAAC,CAAC,CAAC;oBAClB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC1D,MAAM;gBACR,CAAC;gBACD,KAAK,YAAY,CAAC,CAAC,CAAC;oBAClB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACtC,MAAM;gBACR,CAAC;gBACD,KAAK,WAAW,CAAC,CAAC,CAAC;oBACjB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACrC,MAAM;gBACR,CAAC;gBACD,KAAK,SAAS,CAAC,CAAC,CAAC;oBACf,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACjC,MAAM;gBACR,CAAC;gBACD,KAAK,aAAa,CAAC,CAAC,CAAC;oBACnB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACzC,MAAM;gBACR,CAAC;gBACD,KAAK,WAAW,CAAC,CAAC,CAAC;oBACjB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;oBAC1D,MAAM;gBACR,CAAC;gBACD,KAAK,aAAa,CAAC,CAAC,CAAC;oBACnB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;oBAC5D,MAAM;gBACR,CAAC;gBACD,KAAK,mBAAmB,CAAC,CAAC,CAAC;oBACzB,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC/C,MAAM;gBACR,CAAC;gBACD,KAAK,YAAY,CAAC,CAAC,CAAC;oBAClB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;oBACxD,MAAM;gBACR,CAAC;gBACD,KAAK,YAAY,CAAC,CAAC,CAAC;oBAClB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;oBACvE,MAAM;gBACR,CAAC;gBACD,KAAK,eAAe,CAAC,CAAC,CAAC;oBACrB,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBACD,KAAK,eAAe,CAAC,CAAC,CAAC;oBACrB,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBACD,KAAK,uBAAuB,CAAC,CAAC,CAAC;oBAC7B,IAAI,CAAC,2BAA2B,EAAE,CAAC;oBACnC,MAAM;gBACR,CAAC;gBACD,KAAK,SAAS,CAAC,CAAC,CAAC;oBACf,IAAI,CAAC,OAAO,EAAE,CAAC;oBACf,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW,CACjB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,wCAAwC,OAAO,CAAC,MAAM,iBAAiB,EACvE,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,aAAa,CACd,CAAC;YAEF,IAAI,OAAO,KAAK,aAAa,EAAE,CAAC;gBAC9B,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAC9B;oBACE,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;oBAC9C,KAAK,EAAE,wBAAwB;oBAC/B,WAAW,EAAE,KAAK;iBACnB,EACD,KAAK,EAAE,QAAQ,EAAE,EAAE;oBACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBACxC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;wBAC1B,QAAQ,CAAC,MAAM,CAAC;4BACd,SAAS,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;4BACjC,OAAO,EAAE,cAAc,MAAM,CAAC,YAAY,KAAK;yBAChD,CAAC,CAAC;wBAEH,IAAI,CAAC;4BACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;wBACnE,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,MAAM,CAAC,KAAK,CAAC,oBAAoB,MAAM,CAAC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;wBAClE,CAAC;oBACH,CAAC;gBACH,CAAC,CACF,CAAC;gBAEF,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;gBAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACxD,YAAY,OAAO,CAAC,MAAM,iBAAiB,EAC3C,UAAU,CACX,CAAC;YAEF,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;gBAC3B,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;gBAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC;YAE5D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,sBAAsB,MAAM,CAAC,YAAY,GAAG,EAC5C,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,SAAS,CACV,CAAC;YAEF,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;gBACjE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YACvF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC;YAE5D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YAED,0CAA0C;YAC1C,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC;YAE5D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YAED,iBAAiB;YACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,EAChD,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,EAC/C,MAAM,CAAC,GAAG,EACV,GAAG,MAAM,CAAC,YAAY,iBAAiB,CACxC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB;QACvC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB,EAAE,QAAiB;QAC1D,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,MAAc;QAC3C,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;YACtD,OAAO;QACT,CAAC;QAED,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,SAAS,CAAC,CAAC,CAAC;gBACf,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAC3C,MAAM;YACR,CAAC;YACD,KAAK,MAAM,CAAC,CAAC,CAAC;gBACZ,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBACxC,MAAM;YACR,CAAC;YACD,KAAK,OAAO,CAAC,CAAC,CAAC;gBACb,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;gBACzC,MAAM;YACR,CAAC;YACD,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;gBAC1C,MAAM;YACR,CAAC;YACD;gBACE,oCAAoC;gBACpC,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,MAAkD;QACxE,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QACzB,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,IAA2C;QAC/D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAsC;QAC9D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,SAAiB;QAC/D,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3F,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;YACrE,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,SAAiB;QACjE,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,2BAA2B,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC7F,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,SAAS,GAAG,CAAC,WAAW,CAAC,CAAC;YACvE,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,QAAgB;QACpD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC;YAE5D,IAAI,MAAM,EAAE,YAAY,EAAE,CAAC;gBACzB,gEAAgE;gBAChE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0BAA0B,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;gBACtF,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,MAAc;QAC7D,IAAI,CAAC;YACH,sCAAsC;YACtC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,MAAM,EAAE,CAAC,CAAC;YACjE,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,IAAY,EAAE,OAAe;QAC5E,IAAI,CAAC;YACH,yEAAyE;YACzE,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;YACnE,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACtC,KAAK,EACL,YAAY,OAAO,EAAE,EACrB,MAAM,CAAC,kBAAkB,CAAC,WAAW,CACtC,CAAC;YACF,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC;YAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC3D,MAAM,OAAO,GAAG,CAAC,GAAG,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC1C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC7C,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAE1C,2CAA2C;YAC3C,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC7E,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;gBAClD,MAAM,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACvF,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,WAAW,CAAC,CAAC;YAC5E,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,IAAI,KAAK,OAAO,EAAE,CAAC,CAAC;YAClF,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,UAAU,GAAG;gBACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBAC9B,IAAI,EAAE,MAAM,CAAC,YAAY;oBACzB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,YAAY,EAAE,MAAM,CAAC,YAAY;oBACjC,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;iBAC1B,CAAC,CAAC;aACJ,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACvD,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC7C,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC;gBACnD,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE;aAC9B,CAAC,CAAC;YAEH,IAAI,GAAG,EAAE,CAAC;gBACR,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBAClE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC7C,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE;gBAC7B,aAAa,EAAE,KAAK;aACrB,CAAC,CAAC;YAEH,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBAEjE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,YAAY,UAAU,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,iBAAiB,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,CACtJ,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B;QACvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,2CAA2C,CAAC,CAAC;gBAC9E,OAAO;YACT,CAAC;YAED,iDAAiD;YACjD,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1E,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YACvG,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAClE,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAE3G,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1B,aAAa,GAAG,GAAG,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3D,CAAC;iBAAM,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxD,aAAa,GAAG,gBAAgB,OAAO,CAAC,MAAM,QAAQ,CAAC;YACzD,CAAC;iBAAM,IAAI,OAAO,EAAE,CAAC;gBACnB,aAAa,GAAG,4BAA4B,CAAC;YAC/C,CAAC;iBAAM,IAAI,SAAS,EAAE,CAAC;gBACrB,aAAa,GAAG,oCAAoC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,aAAa,GAAG,iBAAiB,OAAO,CAAC,MAAM,QAAQ,CAAC;YAC1D,CAAC;YAED,IAAI,QAAQ,EAAE,CAAC;gBACb,aAAa,IAAI,aAAa,CAAC;YACjC,CAAC;YAED,4BAA4B;YAC5B,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBACpD,MAAM,EAAE,8CAA8C;gBACtD,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,sBAAsB;aACpC,CAAC,CAAC;YAEH,IAAI,YAAY,EAAE,CAAC;gBACjB,0CAA0C;gBAC1C,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBACnD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mCAAmC,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,SAAmB;QAChD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,sBAAsB,SAAS,CAAC,MAAM,SAAS,EAC/C,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,aAAa,CACd,CAAC;QAEF,IAAI,OAAO,KAAK,aAAa,EAAE,CAAC;YAC9B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAClF,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,KAAK,CAAC,oBAAoB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,SAAmB;QAC7C,iDAAiD;QACjD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,mBAAmB,SAAS,CAAC,MAAM,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,SAAmB;QAC9C,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC/E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,iBAAiB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,UAAU,SAAS,CAAC,MAAM,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,SAAmB;QAC/C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;YAC/C,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,UAAU,EAAE;YAC7C,EAAE,KAAK,EAAE,yBAAyB,EAAE,KAAK,EAAE,cAAc,EAAE;YAC3D,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,UAAU,EAAE;SAC7C,EAAE,EAAE,WAAW,EAAE,6CAA6C,EAAE,CAAC,CAAC;QAEnE,IAAI,MAAM,EAAE,CAAC;YACX,mEAAmE;YACnE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,UAAU,SAAS,CAAC,MAAM,aAAa,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAqB;QAChD,IAAI,QAAQ,GAAG,OAAO,CAAC;QAEvB,gBAAgB;QAChB,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,KAAK,UAAU,CAAC,CAAC,CAAC;gBAChB,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC;gBAC1D,MAAM;YACR,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;gBAClD,MAAM;YACR,CAAC;YACD,KAAK,OAAO,CAAC,CAAC,CAAC;gBACb,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBACjE,MAAM;YACR,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,KAAK,MAAM,CAAC,CAAC,CAAC;gBACZ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;gBACtE,MAAM;YACR,CAAC;YACD,KAAK,UAAU,CAAC,CAAC,CAAC;gBAChB,MAAM,aAAa,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAClE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrH,MAAM;YACR,CAAC;YACD,KAAK,MAAM,CAAC,CAAC,CAAC;gBACZ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;gBACnF,MAAM;YACR,CAAC;YACD,KAAK,MAAM,CAAC,CAAC,CAAC;gBACZ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;gBACnD,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAqB;QAC7C,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAqB;QAC7C,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAqB;QAC5C,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;IAC/D,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAEO,MAAM;QACZ,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC;IAEO,iBAAiB,CAAC,OAAuB;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAEtD,sBAAsB;QACtB,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QACzG,MAAM,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;QAC3G,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;QAC9G,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC,CAAC;QAE/G,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,OAAO;;;;4FAIiF,OAAO,CAAC,SAAS,uBAAuB,KAAK;;sBAEnH,aAAa;sBACb,cAAc;sBACd,YAAY;sBACZ,YAAY;;;;;;;;;;;;;wCAaM,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;6CACtC,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;8CAC/C,IAAI,CAAC,UAAU,KAAK,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;0CACrD,IAAI,CAAC,UAAU,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;6CAG1C,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;yCAClD,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;yCAC1C,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;yCAC1C,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;4CAGvC,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;;;4CAGxC,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;;;4CAGxC,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;kBAkBtE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;mCAGJ,OAAO,CAAC,MAAM;;;;gCAIjB,OAAO,CAAC,MAAM;;iBAE7B,CAAC,CAAC,CAAC,EAAE;;;;;;cAMR,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;aAYxB,CAAC,CAAC,CAAC;;;;;+CAK+B,OAAO,CAAC,MAAM;;;;;+CAKd,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;;;;;+CAK/B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;;;;;+CAK/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;;;;kBAI3D,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;;+CAED,IAAI,CAAC,aAAa,CAAC,IAAI;;;;;;;;;;;;;;iBAcrD,CAAC,CAAC,CAAC,EAAE;;;yCAGmB,IAAI,CAAC,QAAQ;kBACpC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAElE;;;;yBAIY,KAAK;YAClB,IAAI,CAAC,gBAAgB,EAAE;;;cAGrB,CAAC;IACb,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAkB;QACzC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,YAAY,MAAM,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC;QAChE,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;QAEpE,OAAO;gCACqB,aAAa,IAAI,aAAa,qBAAqB,MAAM,CAAC,GAAG,CAAC,MAAM;;;2DAGzC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;+CAGvC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;;;0CAGpC,MAAM,CAAC,YAAY;;oBAEzC,MAAM,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,8CAA8C,CAAC,CAAC,CAAC,EAAE;oBACpF,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,8CAA8C,CAAC,CAAC,CAAC,EAAE;oBAC5E,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,2BAA2B,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE;;;;4CAIvD,MAAM,CAAC,IAAI;4CACX,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC;kBAChE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;;+CAEU,MAAM,CAAC,SAAS,CAAC,SAAS;+CAC1B,MAAM,CAAC,SAAS,CAAC,SAAS;;iBAExD,CAAC,CAAC,CAAC,EAAE;kBACJ,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,4BAA4B,MAAM,CAAC,SAAS,SAAS,CAAC,CAAC,CAAC,EAAE;;gBAE/E,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;;;kDAGY,MAAM,CAAC,YAAY;;;eAGtD,CAAC,CAAC,CAAC,EAAE;;;;wDAIoC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;6CACvC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UA8BhF,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;;KAEtD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,MAAkB;QAC3C,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpE,OAAO;;;;;;;OAON,CAAC;QACJ,CAAC;QAED,OAAO;;;;;;;;;4CASiC,MAAM,CAAC,SAAS,CAAC,SAAS;4CAC1B,MAAM,CAAC,SAAS,CAAC,SAAS;uCAC/B,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM;;;;;YAKxD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;sDACN,SAAS;;0CAErB,IAAI,CAAC,MAAM;;;;;;;;;;;kBAWnC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;0CACC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE;;sDAEnD,IAAI,CAAC,aAAa,IAAI,EAAE;sDACxB,IAAI,CAAC,aAAa,IAAI,EAAE;;;kDAG5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;gDAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;;sBAEvD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;0BAGhB,IAAI,CAAC,YAAY;;qBAEtB,CAAC,CAAC,CAAC,EAAE;;iBAET,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;WAGhB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;KAGhB,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,IAAY;QAChC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,UAAU,CAAC,CAAC,CAAC;gBAAC,OAAO,cAAc,CAAC;YAAC,CAAC;YAC3C,KAAK,SAAS,CAAC,CAAC,CAAC;gBAAC,OAAO,aAAa,CAAC;YAAC,CAAC;YACzC,KAAK,SAAS,CAAC,CAAC,CAAC;gBAAC,OAAO,gBAAgB,CAAC;YAAC,CAAC;YAC5C,KAAK,SAAS,CAAC,CAAC,CAAC;gBAAC,OAAO,qBAAqB,CAAC;YAAC,CAAC;YACjD,KAAK,YAAY,CAAC,CAAC,CAAC;gBAAC,OAAO,iBAAiB,CAAC;YAAC,CAAC;YAChD,OAAO,CAAC,CAAC,CAAC;gBAAC,OAAO,cAAc,CAAC;YAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,IAAY;QAChC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,UAAU,CAAC,CAAC,CAAC;gBAAC,OAAO,GAAG,CAAC;YAAC,CAAC;YAChC,KAAK,UAAU,CAAC,CAAC,CAAC;gBAAC,OAAO,GAAG,CAAC;YAAC,CAAC;YAChC,KAAK,SAAS,CAAC,CAAC,CAAC;gBAAC,OAAO,GAAG,CAAC;YAAC,CAAC;YAC/B,OAAO,CAAC,CAAC,CAAC;gBAAC,OAAO,GAAG,CAAC;YAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAEO,UAAU,CAAC,IAAY;QAC7B,OAAO,IAAI;aACR,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEO,eAAe,CAAC,SAAiB;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,GAAG,GAAG,SAAS,CAAC;QAE7B,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC;YACjB,OAAO,UAAU,CAAC;QACpB,CAAC;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;YACzC,OAAO,GAAG,OAAO,UAAU,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;YACzC,OAAO,GAAG,KAAK,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4CN,CAAC;IACJ,CAAC;IAEO,QAAQ;QACd,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,QAAQ,GAAG,gEAAgE,CAAC;QAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QACvE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,OAAO;QACZ,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC;QAEzC,yBAAyB;QACzB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QAErB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YACjC,IAAI,CAAC,EAAE,CAAC;gBACN,CAAC,CAAC,OAAO,EAAE,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAroCD,0CAqoCC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as path from 'path';\nimport { Logger } from '../../logger';\nimport { FileChangeTracker, FileChange } from './fileChangeTracker';\n\nconst logger = Logger.instance;\n\n/**\n * Advanced File Change Panel - Professional webview with embedded diffs\n * Features comprehensive change management and intelligent diff analysis\n */\nexport class FileChangePanel {\n  public static currentPanel: FileChangePanel | undefined;\n  private readonly panel: vscode.WebviewPanel;\n  private readonly extensionUri: vscode.Uri;\n  private disposables: vscode.Disposable[] = [];\n  private fileChangeTracker: FileChangeTracker;\n  private expandedDiffs: Set<string> = new Set();\n  private selectedFiles: Set<string> = new Set();\n  private filterMode: 'all' | 'critical' | 'conflicts' | 'large' = 'all';\n  private sortMode: 'name' | 'priority' | 'size' | 'time' = 'priority';\n  private viewMode: 'list' | 'grid' | 'timeline' = 'list';\n  private aiInsightsEnabled = true;\n  private collaborativeReview = false;\n  private commentDiagnostics: vscode.DiagnosticCollection;\n  private commentsByFile: Map<string, vscode.Diagnostic[]> = new Map();\n\n  public static createOrShow(extensionUri: vscode.Uri, context: vscode.ExtensionContext): void {\n    const column = vscode.window.activeTextEditor\n      ? vscode.window.activeTextEditor.viewColumn\n      : undefined;\n\n    // If we already have a panel, show it\n    if (FileChangePanel.currentPanel) {\n      FileChangePanel.currentPanel.panel.reveal(column);\n      FileChangePanel.currentPanel.refresh();\n      return;\n    }\n\n    // Otherwise, create a new panel\n    const panel = vscode.window.createWebviewPanel(\n      'codessaFileChanges',\n      'File Changes',\n      column || vscode.ViewColumn.One,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true,\n        localResourceRoots: [\n          vscode.Uri.joinPath(extensionUri, 'media'),\n          vscode.Uri.joinPath(extensionUri, 'out', 'media')\n        ]\n      }\n    );\n\n    FileChangePanel.currentPanel = new FileChangePanel(panel, extensionUri, context);\n  }\n\n  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, context: vscode.ExtensionContext) {\n    this.panel = panel;\n    this.extensionUri = extensionUri;\n    this.fileChangeTracker = FileChangeTracker.getInstance(context);\n    this.commentDiagnostics = vscode.languages.createDiagnosticCollection('codessaComments');\n    this.disposables.push(this.commentDiagnostics);\n\n    // Set the webview's initial html content\n    this.update();\n\n    // Listen for when the panel is disposed\n    this.panel.onDidDispose(() => this.dispose(), null, this.disposables);\n\n    // Handle messages from the webview with revolutionary features\n    this.panel.webview.onDidReceiveMessage(\n      message => {\n        switch (message.command) {\n          case 'discardAll': {\n            this.handleDiscardAll();\n            break;\n          }\n          case 'keepAll': {\n            this.handleKeepAll();\n            break;\n          }\n          case 'discardFile': {\n            this.handleDiscardFile(message.filePath);\n            break;\n          }\n          case 'keepFile': {\n            this.handleKeepFile(message.filePath);\n            break;\n          }\n          case 'viewFile': {\n            this.handleViewFile(message.filePath);\n            break;\n          }\n          case 'openFile': {\n            this.handleOpenFile(message.filePath);\n            break;\n          }\n          case 'toggleDiff': {\n            this.handleToggleDiff(message.filePath);\n            break;\n          }\n          case 'selectFile': {\n            this.handleSelectFile(message.filePath, message.selected);\n            break;\n          }\n          case 'bulkAction': {\n            this.handleBulkAction(message.action);\n            break;\n          }\n          case 'setFilter': {\n            this.handleSetFilter(message.filter);\n            break;\n          }\n          case 'setSort': {\n            this.handleSetSort(message.sort);\n            break;\n          }\n          case 'setViewMode': {\n            this.handleSetViewMode(message.viewMode);\n            break;\n          }\n          case 'stageHunk': {\n            this.handleStageHunk(message.filePath, message.hunkIndex);\n            break;\n          }\n          case 'unstageHunk': {\n            this.handleUnstageHunk(message.filePath, message.hunkIndex);\n            break;\n          }\n          case 'applyAISuggestion': {\n            this.handleApplyAISuggestion(message.filePath);\n            break;\n          }\n          case 'reviewFile': {\n            this.handleReviewFile(message.filePath, message.status);\n            break;\n          }\n          case 'addComment': {\n            this.handleAddComment(message.filePath, message.line, message.comment);\n            break;\n          }\n          case 'exportChanges': {\n            this.handleExportChanges();\n            break;\n          }\n          case 'importChanges': {\n            this.handleImportChanges();\n            break;\n          }\n          case 'generateCommitMessage': {\n            this.handleGenerateCommitMessage();\n            break;\n          }\n          case 'refresh': {\n            this.refresh();\n            break;\n          }\n        }\n      },\n      null,\n      this.disposables\n    );\n  }\n\n  private async handleDiscardAll(): Promise<void> {\n    try {\n      const changes = this.fileChangeTracker.getTrackedChanges();\n      if (changes.length === 0) {\n        return;\n      }\n\n      const confirm = await vscode.window.showWarningMessage(\n        `Are you sure you want to discard all ${changes.length} changed files?`,\n        { modal: true },\n        'Discard All'\n      );\n\n      if (confirm === 'Discard All') {\n        await vscode.window.withProgress(\n          {\n            location: vscode.ProgressLocation.Notification,\n            title: 'Discarding all changes',\n            cancellable: false\n          },\n          async (progress) => {\n            for (let i = 0; i < changes.length; i++) {\n              const change = changes[i];\n              progress.report({\n                increment: (100 / changes.length),\n                message: `Discarding ${change.relativePath}...`\n              });\n\n              try {\n                await vscode.commands.executeCommand('git.checkout', change.uri);\n              } catch (error) {\n                logger.error(`Error discarding ${change.relativePath}:`, error);\n              }\n            }\n          }\n        );\n\n        this.fileChangeTracker.clearTrackedChanges();\n        this.refresh();\n        vscode.window.showInformationMessage('All changes discarded');\n      }\n    } catch (error) {\n      logger.error('Error discarding all changes:', error);\n      vscode.window.showErrorMessage('Failed to discard all changes');\n    }\n  }\n\n  private async handleKeepAll(): Promise<void> {\n    try {\n      const changes = this.fileChangeTracker.getTrackedChanges();\n      if (changes.length === 0) {\n        return;\n      }\n\n      const confirm = await vscode.window.showInformationMessage(\n        `Keep all ${changes.length} changed files?`,\n        'Keep All'\n      );\n\n      if (confirm === 'Keep All') {\n        this.fileChangeTracker.clearTrackedChanges();\n        this.refresh();\n        vscode.window.showInformationMessage('All changes kept');\n      }\n    } catch (error) {\n      logger.error('Error keeping all changes:', error);\n      vscode.window.showErrorMessage('Failed to keep all changes');\n    }\n  }\n\n  private async handleDiscardFile(filePath: string): Promise<void> {\n    try {\n      const changes = this.fileChangeTracker.getTrackedChanges();\n      const change = changes.find(c => c.uri.fsPath === filePath);\n\n      if (!change) {\n        return;\n      }\n\n      const confirm = await vscode.window.showWarningMessage(\n        `Discard changes in ${change.relativePath}?`,\n        { modal: true },\n        'Discard'\n      );\n\n      if (confirm === 'Discard') {\n        await vscode.commands.executeCommand('git.checkout', change.uri);\n        this.refresh();\n        vscode.window.showInformationMessage(`Changes discarded for ${change.relativePath}`);\n      }\n    } catch (error) {\n      logger.error('Error discarding file:', error);\n      vscode.window.showErrorMessage('Failed to discard file changes');\n    }\n  }\n\n  private async handleKeepFile(filePath: string): Promise<void> {\n    try {\n      const changes = this.fileChangeTracker.getTrackedChanges();\n      const change = changes.find(c => c.uri.fsPath === filePath);\n\n      if (!change) {\n        return;\n      }\n\n      // Remove from tracking (changes are kept)\n      this.refresh();\n      vscode.window.showInformationMessage(`Changes kept for ${change.relativePath}`);\n    } catch (error) {\n      logger.error('Error keeping file:', error);\n      vscode.window.showErrorMessage('Failed to keep file changes');\n    }\n  }\n\n  private async handleViewFile(filePath: string): Promise<void> {\n    try {\n      const changes = this.fileChangeTracker.getTrackedChanges();\n      const change = changes.find(c => c.uri.fsPath === filePath);\n\n      if (!change) {\n        return;\n      }\n\n      // Open diff view\n      await vscode.commands.executeCommand('vscode.diff',\n        vscode.Uri.parse(`git:${change.uri.path}?HEAD`),\n        change.uri,\n        `${change.relativePath} (Working Tree)`\n      );\n    } catch (error) {\n      logger.error('Error viewing file:', error);\n      vscode.window.showErrorMessage('Failed to view file changes');\n    }\n  }\n\n  private async handleOpenFile(filePath: string): Promise<void> {\n    try {\n      const uri = vscode.Uri.file(filePath);\n      await vscode.window.showTextDocument(uri);\n    } catch (error) {\n      logger.error('Error opening file:', error);\n      vscode.window.showErrorMessage('Failed to open file');\n    }\n  }\n\n  /**\n   * Revolutionary embedded diff toggle\n   */\n  private handleToggleDiff(filePath: string): void {\n    if (this.expandedDiffs.has(filePath)) {\n      this.expandedDiffs.delete(filePath);\n    } else {\n      this.expandedDiffs.add(filePath);\n    }\n    this.refresh();\n  }\n\n  /**\n   * Advanced file selection for bulk operations\n   */\n  private handleSelectFile(filePath: string, selected: boolean): void {\n    if (selected) {\n      this.selectedFiles.add(filePath);\n    } else {\n      this.selectedFiles.delete(filePath);\n    }\n    this.refresh();\n  }\n\n  /**\n   * Bulk operations on selected files\n   */\n  private async handleBulkAction(action: string): Promise<void> {\n    const selectedFiles = Array.from(this.selectedFiles);\n    if (selectedFiles.length === 0) {\n      vscode.window.showWarningMessage('No files selected');\n      return;\n    }\n\n    switch (action) {\n      case 'discard': {\n        await this.bulkDiscardFiles(selectedFiles);\n        break;\n      }\n      case 'keep': {\n        await this.bulkKeepFiles(selectedFiles);\n        break;\n      }\n      case 'stage': {\n        await this.bulkStageFiles(selectedFiles);\n        break;\n      }\n      case 'review': {\n        await this.bulkReviewFiles(selectedFiles);\n        break;\n      }\n      default:\n        // No action for unknown action type\n        break;\n    }\n  }\n\n  /**\n   * Advanced filtering system\n   */\n  private handleSetFilter(filter: 'all' | 'critical' | 'conflicts' | 'large'): void {\n    this.filterMode = filter;\n    this.refresh();\n  }\n\n  /**\n   * Smart sorting options\n   */\n  private handleSetSort(sort: 'name' | 'priority' | 'size' | 'time'): void {\n    this.sortMode = sort;\n    this.refresh();\n  }\n\n  /**\n   * Multiple view modes\n   */\n  private handleSetViewMode(viewMode: 'list' | 'grid' | 'timeline'): void {\n    this.viewMode = viewMode;\n    this.refresh();\n  }\n\n  /**\n   * Git hunk staging\n   */\n  private async handleStageHunk(filePath: string, hunkIndex: number): Promise<void> {\n    try {\n      // Use git API to stage specific hunk\n      await vscode.commands.executeCommand('git.stageSelectedRanges', vscode.Uri.file(filePath));\n      vscode.window.showInformationMessage(`Hunk ${hunkIndex + 1} staged`);\n      this.refresh();\n    } catch (error) {\n      logger.error('Error staging hunk:', error);\n      vscode.window.showErrorMessage('Failed to stage hunk');\n    }\n  }\n\n  /**\n   * Git hunk unstaging\n   */\n  private async handleUnstageHunk(filePath: string, hunkIndex: number): Promise<void> {\n    try {\n      await vscode.commands.executeCommand('git.unstageSelectedRanges', vscode.Uri.file(filePath));\n      vscode.window.showInformationMessage(`Hunk ${hunkIndex + 1} unstaged`);\n      this.refresh();\n    } catch (error) {\n      logger.error('Error unstaging hunk:', error);\n      vscode.window.showErrorMessage('Failed to unstage hunk');\n    }\n  }\n\n  /**\n   * AI-powered suggestion application\n   */\n  private async handleApplyAISuggestion(filePath: string): Promise<void> {\n    try {\n      const changes = this.fileChangeTracker.getTrackedChanges();\n      const change = changes.find(c => c.uri.fsPath === filePath);\n\n      if (change?.aiSuggestion) {\n        // Apply AI suggestion (this would integrate with an AI service)\n        vscode.window.showInformationMessage(`AI suggestion applied: ${change.aiSuggestion}`);\n        this.refresh();\n      }\n    } catch (error) {\n      logger.error('Error applying AI suggestion:', error);\n      vscode.window.showErrorMessage('Failed to apply AI suggestion');\n    }\n  }\n\n  /**\n   * File review system\n   */\n  private async handleReviewFile(filePath: string, status: string): Promise<void> {\n    try {\n      // Update review status in the tracker\n      vscode.window.showInformationMessage(`File marked as ${status}`);\n      this.refresh();\n    } catch (error) {\n      logger.error('Error reviewing file:', error);\n      vscode.window.showErrorMessage('Failed to review file');\n    }\n  }\n\n  /**\n   * Collaborative commenting system\n   */\n  private async handleAddComment(filePath: string, line: number, comment: string): Promise<void> {\n    try {\n      // Add informational diagnostic to represent the comment in Problems pane\n      const uri = vscode.Uri.file(filePath);\n      const zeroBasedLine = Math.max(0, line - 1);\n      const range = new vscode.Range(zeroBasedLine, 0, zeroBasedLine, 0);\n      const diagnostic = new vscode.Diagnostic(\n        range,\n        `Comment: ${comment}`,\n        vscode.DiagnosticSeverity.Information\n      );\n      diagnostic.source = 'Codessa';\n\n      const existing = this.commentsByFile.get(uri.fsPath) || [];\n      const updated = [...existing, diagnostic];\n      this.commentsByFile.set(uri.fsPath, updated);\n      this.commentDiagnostics.set(uri, updated);\n\n      // Optionally reveal the line in the editor\n      try {\n        const doc = await vscode.workspace.openTextDocument(uri);\n        const editor = await vscode.window.showTextDocument(doc, { preview: false });\n        const pos = new vscode.Position(zeroBasedLine, 0);\n        editor.revealRange(new vscode.Range(pos, pos), vscode.TextEditorRevealType.InCenter);\n      } catch (revealError) {\n        logger.debug('Optional reveal failed for comment location:', revealError);\n      }\n\n      vscode.window.showInformationMessage(`Comment added to line ${line}: ${comment}`);\n      this.refresh();\n    } catch (error) {\n      logger.error('Error adding comment:', error);\n      vscode.window.showErrorMessage('Failed to add comment');\n    }\n  }\n\n  /**\n   * Export changes for sharing\n   */\n  private async handleExportChanges(): Promise<void> {\n    try {\n      const changes = this.fileChangeTracker.getTrackedChanges();\n      const exportData = {\n        timestamp: Date.now(),\n        changes: changes.map(change => ({\n          path: change.relativePath,\n          type: change.type,\n          diffStats: change.diffStats,\n          aiSuggestion: change.aiSuggestion,\n          tags: change.tags,\n          priority: change.priority\n        }))\n      };\n\n      const exportJson = JSON.stringify(exportData, null, 2);\n      const uri = await vscode.window.showSaveDialog({\n        defaultUri: vscode.Uri.file('codessa-changes.json'),\n        filters: { 'JSON': ['json'] }\n      });\n\n      if (uri) {\n        await vscode.workspace.fs.writeFile(uri, Buffer.from(exportJson));\n        vscode.window.showInformationMessage('Changes exported successfully');\n      }\n    } catch (error) {\n      logger.error('Error exporting changes:', error);\n      vscode.window.showErrorMessage('Failed to export changes');\n    }\n  }\n\n  /**\n   * Import changes from file\n   */\n  private async handleImportChanges(): Promise<void> {\n    try {\n      const uri = await vscode.window.showOpenDialog({\n        filters: { 'JSON': ['json'] },\n        canSelectMany: false\n      });\n\n      if (uri && uri[0]) {\n        const content = await vscode.workspace.fs.readFile(uri[0]);\n        const importData = JSON.parse(new TextDecoder().decode(content));\n\n        vscode.window.showInformationMessage(\n          `Imported ${importData.changes?.length || 0} changes from ${importData.timestamp ? new Date(importData.timestamp).toLocaleString() : 'unknown time'}`\n        );\n      }\n    } catch (error) {\n      logger.error('Error importing changes:', error);\n      vscode.window.showErrorMessage('Failed to import changes');\n    }\n  }\n\n  /**\n   * Generate AI commit message\n   */\n  private async handleGenerateCommitMessage(): Promise<void> {\n    try {\n      const changes = this.fileChangeTracker.getTrackedChanges();\n      if (changes.length === 0) {\n        vscode.window.showWarningMessage('No changes to generate commit message for');\n        return;\n      }\n\n      // Generate smart commit message based on changes\n      let commitMessage = '';\n      const fileTypes = new Set(changes.map(c => path.extname(c.relativePath)));\n      const hasTests = changes.some(c => c.relativePath.includes('test') || c.relativePath.includes('spec'));\n      const hasDocs = changes.some(c => c.relativePath.endsWith('.md'));\n      const hasConfig = changes.some(c => c.relativePath.includes('config') || c.relativePath.includes('.json'));\n\n      if (changes.length === 1) {\n        const change = changes[0];\n        commitMessage = `${change.type}: ${change.relativePath}`;\n      } else if (fileTypes.has('.ts') || fileTypes.has('.js')) {\n        commitMessage = `feat: update ${changes.length} files`;\n      } else if (hasDocs) {\n        commitMessage = 'docs: update documentation';\n      } else if (hasConfig) {\n        commitMessage = 'config: update configuration files';\n      } else {\n        commitMessage = `chore: update ${changes.length} files`;\n      }\n\n      if (hasTests) {\n        commitMessage += ' with tests';\n      }\n\n      // Show commit message input\n      const finalMessage = await vscode.window.showInputBox({\n        prompt: 'AI-generated commit message (edit if needed)',\n        value: commitMessage,\n        placeHolder: 'Enter commit message'\n      });\n\n      if (finalMessage) {\n        // Copy to clipboard or execute git commit\n        await vscode.env.clipboard.writeText(finalMessage);\n        vscode.window.showInformationMessage('Commit message copied to clipboard');\n      }\n    } catch (error) {\n      logger.error('Error generating commit message:', error);\n      vscode.window.showErrorMessage('Failed to generate commit message');\n    }\n  }\n\n  /**\n   * Bulk discard files\n   */\n  private async bulkDiscardFiles(filePaths: string[]): Promise<void> {\n    const confirm = await vscode.window.showWarningMessage(\n      `Discard changes in ${filePaths.length} files?`,\n      { modal: true },\n      'Discard All'\n    );\n\n    if (confirm === 'Discard All') {\n      for (const filePath of filePaths) {\n        try {\n          await vscode.commands.executeCommand('git.checkout', vscode.Uri.file(filePath));\n        } catch (error) {\n          logger.error(`Error discarding ${filePath}:`, error);\n        }\n      }\n      this.selectedFiles.clear();\n      this.refresh();\n    }\n  }\n\n  /**\n   * Bulk keep files\n   */\n  private async bulkKeepFiles(filePaths: string[]): Promise<void> {\n    // Simply remove from tracking (changes are kept)\n    this.selectedFiles.clear();\n    this.refresh();\n    vscode.window.showInformationMessage(`Kept changes in ${filePaths.length} files`);\n  }\n\n  /**\n   * Bulk stage files\n   */\n  private async bulkStageFiles(filePaths: string[]): Promise<void> {\n    for (const filePath of filePaths) {\n      try {\n        await vscode.commands.executeCommand('git.stage', vscode.Uri.file(filePath));\n      } catch (error) {\n        logger.error(`Error staging ${filePath}:`, error);\n      }\n    }\n    this.selectedFiles.clear();\n    this.refresh();\n    vscode.window.showInformationMessage(`Staged ${filePaths.length} files`);\n  }\n\n  /**\n   * Bulk review files\n   */\n  private async bulkReviewFiles(filePaths: string[]): Promise<void> {\n    const status = await vscode.window.showQuickPick([\n      { label: '✅ Approve All', value: 'approved' },\n      { label: '🔍 Mark as Needs Review', value: 'needs_review' },\n      { label: '❌ Reject All', value: 'rejected' }\n    ], { placeHolder: 'Select review status for all selected files' });\n\n    if (status) {\n      // Update review status (this would integrate with a review system)\n      this.selectedFiles.clear();\n      this.refresh();\n      vscode.window.showInformationMessage(`Marked ${filePaths.length} files as ${status.value}`);\n    }\n  }\n\n  /**\n   * Filter and sort changes\n   */\n  private filterAndSortChanges(changes: FileChange[]): FileChange[] {\n    let filtered = changes;\n\n    // Apply filters\n    switch (this.filterMode) {\n      case 'critical': {\n        filtered = changes.filter(c => c.priority === 'critical');\n        break;\n      }\n      case 'conflicts': {\n        filtered = changes.filter(c => c.conflictMarkers);\n        break;\n      }\n      case 'large': {\n        filtered = changes.filter(c => (c.diffStats?.changes || 0) > 50);\n        break;\n      }\n    }\n\n    // Apply sorting\n    switch (this.sortMode) {\n      case 'name': {\n        filtered.sort((a, b) => a.relativePath.localeCompare(b.relativePath));\n        break;\n      }\n      case 'priority': {\n        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };\n        filtered.sort((a, b) => (priorityOrder[a.priority || 'medium'] || 2) - (priorityOrder[b.priority || 'medium'] || 2));\n        break;\n      }\n      case 'size': {\n        filtered.sort((a, b) => (b.diffStats?.changes || 0) - (a.diffStats?.changes || 0));\n        break;\n      }\n      case 'time': {\n        filtered.sort((a, b) => b.timestamp - a.timestamp);\n        break;\n      }\n    }\n\n    return filtered;\n  }\n\n  /**\n   * Get total additions\n   */\n  private getTotalAdditions(changes: FileChange[]): number {\n    return changes.reduce((sum, c) => sum + (c.diffStats?.additions || 0), 0);\n  }\n\n  /**\n   * Get total deletions\n   */\n  private getTotalDeletions(changes: FileChange[]): number {\n    return changes.reduce((sum, c) => sum + (c.diffStats?.deletions || 0), 0);\n  }\n\n  /**\n   * Get critical count\n   */\n  private getCriticalCount(changes: FileChange[]): number {\n    return changes.filter(c => c.priority === 'critical').length;\n  }\n\n  public refresh(): void {\n    this.update();\n  }\n\n  private update(): void {\n    const webview = this.panel.webview;\n    this.panel.webview.html = this.getHtmlForWebview(webview);\n  }\n\n  private getHtmlForWebview(webview: vscode.Webview): string {\n    const allChanges = this.fileChangeTracker.getTrackedChanges();\n    const changes = this.filterAndSortChanges(allChanges);\n\n    // Get CSS and JS URIs\n    const styleResetUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'reset.css'));\n    const styleVSCodeUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'vscode.css'));\n    const styleMainUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'fileChanges.css'));\n    const styleDiffUri = webview.asWebviewUri(vscode.Uri.joinPath(this.extensionUri, 'media', 'embeddedDiff.css'));\n\n    const nonce = this.getNonce();\n\n    return `<!DOCTYPE html>\n      <html lang=\"en\">\n      <head>\n        <meta charset=\"UTF-8\">\n        <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <link href=\"${styleResetUri}\" rel=\"stylesheet\">\n        <link href=\"${styleVSCodeUri}\" rel=\"stylesheet\">\n        <link href=\"${styleMainUri}\" rel=\"stylesheet\">\n        <link href=\"${styleDiffUri}\" rel=\"stylesheet\">\n        <title>Codessa File Changes - Advanced Tracker</title>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"header-title\">\n              <h1>Codessa File Changes</h1>\n              <div class=\"header-subtitle\">Advanced change tracking with embedded diff analysis</div>\n            </div>\n            <div class=\"header-controls\">\n              <div class=\"filter-controls\">\n                <select id=\"filter-select\" class=\"control-select\">\n                  <option value=\"all\" ${this.filterMode === 'all' ? 'selected' : ''}>All Changes</option>\n                  <option value=\"critical\" ${this.filterMode === 'critical' ? 'selected' : ''}>Critical Only</option>\n                  <option value=\"conflicts\" ${this.filterMode === 'conflicts' ? 'selected' : ''}>Conflicts</option>\n                  <option value=\"large\" ${this.filterMode === 'large' ? 'selected' : ''}>Large Changes</option>\n                </select>\n                <select id=\"sort-select\" class=\"control-select\">\n                  <option value=\"priority\" ${this.sortMode === 'priority' ? 'selected' : ''}>By Priority</option>\n                  <option value=\"name\" ${this.sortMode === 'name' ? 'selected' : ''}>By Name</option>\n                  <option value=\"size\" ${this.sortMode === 'size' ? 'selected' : ''}>By Size</option>\n                  <option value=\"time\" ${this.sortMode === 'time' ? 'selected' : ''}>By Time</option>\n                </select>\n                <div class=\"view-mode-toggle\">\n                  <button class=\"view-btn ${this.viewMode === 'list' ? 'active' : ''}\" data-view=\"list\">\n                    <span class=\"codicon codicon-list-unordered\"></span>\n                  </button>\n                  <button class=\"view-btn ${this.viewMode === 'grid' ? 'active' : ''}\" data-view=\"grid\">\n                    <span class=\"codicon codicon-grid-view\"></span>\n                  </button>\n                  <button class=\"view-btn ${this.viewMode === 'timeline' ? 'active' : ''}\" data-view=\"timeline\">\n                    <span class=\"codicon codicon-timeline-view\"></span>\n                  </button>\n                </div>\n              </div>\n              <div class=\"header-actions\">\n                <button id=\"export-btn\" class=\"button secondary\" title=\"Export Changes\">\n                  <span class=\"codicon codicon-export\"></span>\n                  Export\n                </button>\n                <button id=\"generate-commit-btn\" class=\"button secondary\" title=\"Generate AI Commit Message\">\n                  <span class=\"codicon codicon-sparkle\"></span>\n                  AI Commit\n                </button>\n                <button id=\"refresh-btn\" class=\"button secondary\">\n                  <span class=\"codicon codicon-refresh\"></span>\n                  Refresh\n                </button>\n                ${changes.length > 0 ? `\n                  <button id=\"discard-all-btn\" class=\"button danger\">\n                    <span class=\"codicon codicon-discard\"></span>\n                    Discard All (${changes.length})\n                  </button>\n                  <button id=\"keep-all-btn\" class=\"button primary\">\n                    <span class=\"codicon codicon-check-all\"></span>\n                    Keep All (${changes.length})\n                  </button>\n                ` : ''}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"content\">\n            ${changes.length === 0 ? `\n              <div class=\"empty-state\">\n                <span class=\"codicon codicon-check\"></span>\n                <h2>🎉 No Changes Detected</h2>\n                <p>All files are synchronized and up to date</p>\n                <div class=\"empty-actions\">\n                  <button class=\"button secondary\" onclick=\"refreshChanges()\">\n                    <span class=\"codicon codicon-refresh\"></span>\n                    Scan for Changes\n                  </button>\n                </div>\n              </div>\n            ` : `\n              <div class=\"changes-summary\">\n                <div class=\"summary-stats\">\n                  <div class=\"stat-item\">\n                    <span class=\"codicon codicon-source-control\"></span>\n                    <span class=\"stat-value\">${changes.length}</span>\n                    <span class=\"stat-label\">Files</span>\n                  </div>\n                  <div class=\"stat-item\">\n                    <span class=\"codicon codicon-add\"></span>\n                    <span class=\"stat-value\">${this.getTotalAdditions(changes)}</span>\n                    <span class=\"stat-label\">Additions</span>\n                  </div>\n                  <div class=\"stat-item\">\n                    <span class=\"codicon codicon-remove\"></span>\n                    <span class=\"stat-value\">${this.getTotalDeletions(changes)}</span>\n                    <span class=\"stat-label\">Deletions</span>\n                  </div>\n                  <div class=\"stat-item\">\n                    <span class=\"codicon codicon-warning\"></span>\n                    <span class=\"stat-value\">${this.getCriticalCount(changes)}</span>\n                    <span class=\"stat-label\">Critical</span>\n                  </div>\n                </div>\n                ${this.selectedFiles.size > 0 ? `\n                  <div class=\"bulk-actions\">\n                    <span class=\"bulk-label\">${this.selectedFiles.size} selected:</span>\n                    <button class=\"bulk-btn\" data-action=\"discard\">\n                      <span class=\"codicon codicon-discard\"></span>\n                      Discard Selected\n                    </button>\n                    <button class=\"bulk-btn\" data-action=\"keep\">\n                      <span class=\"codicon codicon-check\"></span>\n                      Keep Selected\n                    </button>\n                    <button class=\"bulk-btn\" data-action=\"stage\">\n                      <span class=\"codicon codicon-add\"></span>\n                      Stage Selected\n                    </button>\n                  </div>\n                ` : ''}\n              </div>\n\n              <div class=\"changes-list ${this.viewMode}\">\n                ${changes.map(change => this.renderChangeItem(change)).join('')}\n              </div>\n            `}\n          </div>\n        </div>\n\n        <script nonce=\"${nonce}\">\n          ${this.getWebviewScript()}\n        </script>\n      </body>\n      </html>`;\n  }\n\n  /**\n   * Advanced change item renderer with embedded diff\n   */\n  private renderChangeItem(change: FileChange): string {\n    const isExpanded = this.expandedDiffs.has(change.uri.fsPath);\n    const isSelected = this.selectedFiles.has(change.uri.fsPath);\n    const priorityClass = `priority-${change.priority || 'medium'}`;\n    const conflictClass = change.conflictMarkers ? 'has-conflicts' : '';\n\n    return `\n      <div class=\"change-item ${priorityClass} ${conflictClass}\" data-file-path=\"${change.uri.fsPath}\">\n        <div class=\"change-header\">\n          <div class=\"change-selector\">\n            <input type=\"checkbox\" class=\"file-checkbox\" ${isSelected ? 'checked' : ''}>\n          </div>\n          <div class=\"change-info\">\n            <span class=\"change-icon codicon ${this.getChangeIcon(change.type)}\"></span>\n            <div class=\"change-details\">\n              <div class=\"file-name-row\">\n                <span class=\"file-name\">${change.relativePath}</span>\n                <div class=\"change-badges\">\n                  ${change.priority === 'critical' ? '<span class=\"badge critical\">CRITICAL</span>' : ''}\n                  ${change.conflictMarkers ? '<span class=\"badge conflict\">CONFLICT</span>' : ''}\n                  ${change.tags?.map(tag => `<span class=\"badge tag\">${tag}</span>`).join('') || ''}\n                </div>\n              </div>\n              <div class=\"change-meta\">\n                <span class=\"change-type\">${change.type}</span>\n                <span class=\"change-time\">${this.formatTimestamp(change.timestamp)}</span>\n                ${change.diffStats ? `\n                  <span class=\"diff-stats\">\n                    <span class=\"additions\">+${change.diffStats.additions}</span>\n                    <span class=\"deletions\">-${change.diffStats.deletions}</span>\n                  </span>\n                ` : ''}\n                ${change.gitStatus ? `<span class=\"git-status\">${change.gitStatus}</span>` : ''}\n              </div>\n              ${change.aiSuggestion ? `\n                <div class=\"ai-suggestion\">\n                  <span class=\"codicon codicon-sparkle\"></span>\n                  <span class=\"suggestion-text\">${change.aiSuggestion}</span>\n                  <button class=\"apply-suggestion-btn\" title=\"Apply AI Suggestion\">Apply</button>\n                </div>\n              ` : ''}\n            </div>\n          </div>\n          <div class=\"change-actions\">\n            <button class=\"action-btn diff-toggle-btn ${isExpanded ? 'expanded' : ''}\" title=\"Toggle Diff\">\n              <span class=\"codicon codicon-${isExpanded ? 'chevron-down' : 'chevron-right'}\"></span>\n            </button>\n            <button class=\"action-btn view-btn\" title=\"View in Editor\">\n              <span class=\"codicon codicon-eye\"></span>\n            </button>\n            <button class=\"action-btn open-btn\" title=\"Open File\">\n              <span class=\"codicon codicon-go-to-file\"></span>\n            </button>\n            <button class=\"action-btn stage-btn\" title=\"Stage Changes\">\n              <span class=\"codicon codicon-add\"></span>\n            </button>\n            <button class=\"action-btn discard-btn\" title=\"Discard Changes\">\n              <span class=\"codicon codicon-discard\"></span>\n            </button>\n            <button class=\"action-btn keep-btn\" title=\"Keep Changes\">\n              <span class=\"codicon codicon-check\"></span>\n            </button>\n            <div class=\"review-dropdown\">\n              <button class=\"action-btn review-btn\" title=\"Review Status\">\n                <span class=\"codicon codicon-comment\"></span>\n              </button>\n              <div class=\"review-menu\">\n                <button data-status=\"approved\">✅ Approve</button>\n                <button data-status=\"needs_review\">🔍 Needs Review</button>\n                <button data-status=\"rejected\">❌ Reject</button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        ${isExpanded ? this.renderEmbeddedDiff(change) : ''}\n      </div>\n    `;\n  }\n\n  /**\n   * Advanced embedded diff renderer\n   */\n  private renderEmbeddedDiff(change: FileChange): string {\n    if (!change.diffStats?.hunks || change.diffStats.hunks.length === 0) {\n      return `\n        <div class=\"embedded-diff\">\n          <div class=\"diff-loading\">\n            <span class=\"codicon codicon-loading codicon-modifier-spin\"></span>\n            Loading diff...\n          </div>\n        </div>\n      `;\n    }\n\n    return `\n      <div class=\"embedded-diff\">\n        <div class=\"diff-header\">\n          <div class=\"diff-controls\">\n            <button class=\"diff-control-btn\" data-action=\"expand-all\">Expand All</button>\n            <button class=\"diff-control-btn\" data-action=\"collapse-all\">Collapse All</button>\n            <button class=\"diff-control-btn\" data-action=\"stage-all\">Stage All Hunks</button>\n          </div>\n          <div class=\"diff-stats\">\n            <span class=\"stat-additions\">+${change.diffStats.additions}</span>\n            <span class=\"stat-deletions\">-${change.diffStats.deletions}</span>\n            <span class=\"stat-hunks\">${change.diffStats.hunks.length} hunks</span>\n          </div>\n        </div>\n\n        <div class=\"diff-content\">\n          ${change.diffStats.hunks.map((hunk, hunkIndex) => `\n            <div class=\"diff-hunk\" data-hunk-index=\"${hunkIndex}\">\n              <div class=\"hunk-header\">\n                <span class=\"hunk-info\">${hunk.header}</span>\n                <div class=\"hunk-actions\">\n                  <button class=\"hunk-action-btn stage-hunk-btn\" title=\"Stage Hunk\">\n                    <span class=\"codicon codicon-add\"></span>\n                  </button>\n                  <button class=\"hunk-action-btn discard-hunk-btn\" title=\"Discard Hunk\">\n                    <span class=\"codicon codicon-discard\"></span>\n                  </button>\n                </div>\n              </div>\n              <div class=\"hunk-lines\">\n                ${hunk.lines.map(line => `\n                  <div class=\"diff-line ${line.type}\" ${line.highlighted ? 'data-highlighted=\"true\"' : ''}>\n                    <div class=\"line-numbers\">\n                      <span class=\"old-line-number\">${line.oldLineNumber || ''}</span>\n                      <span class=\"new-line-number\">${line.newLineNumber || ''}</span>\n                    </div>\n                    <div class=\"line-content\">\n                      <span class=\"line-prefix\">${this.getLinePrefix(line.type)}</span>\n                      <span class=\"line-text\">${this.escapeHtml(line.content)}</span>\n                    </div>\n                    ${line.aiAnnotation ? `\n                      <div class=\"ai-annotation\">\n                        <span class=\"codicon codicon-sparkle\"></span>\n                        ${line.aiAnnotation}\n                      </div>\n                    ` : ''}\n                  </div>\n                `).join('')}\n              </div>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    `;\n  }\n\n  private getChangeIcon(type: string): string {\n    switch (type) {\n      case 'modified': { return 'codicon-edit'; }\n      case 'created': { return 'codicon-add'; }\n      case 'deleted': { return 'codicon-remove'; }\n      case 'renamed': { return 'codicon-arrow-right'; }\n      case 'conflicted': { return 'codicon-warning'; }\n      default: { return 'codicon-file'; }\n    }\n  }\n\n  private getLinePrefix(type: string): string {\n    switch (type) {\n      case 'addition': { return '+'; }\n      case 'deletion': { return '-'; }\n      case 'context': { return ' '; }\n      default: { return ' '; }\n    }\n  }\n\n  private escapeHtml(text: string): string {\n    return text\n      .replace(/&/g, '&amp;')\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;')\n      .replace(/\"/g, '&quot;')\n      .replace(/'/g, '&#039;');\n  }\n\n  private formatTimestamp(timestamp: number): string {\n    const now = Date.now();\n    const diff = now - timestamp;\n\n    if (diff < 60000) {\n      return 'just now';\n    } else if (diff < 3600000) {\n      const minutes = Math.floor(diff / 60000);\n      return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;\n    } else {\n      const hours = Math.floor(diff / 3600000);\n      return `${hours} hour${hours === 1 ? '' : 's'} ago`;\n    }\n  }\n\n  private getWebviewScript(): string {\n    return `\n      const vscode = acquireVsCodeApi();\n\n      // Header action buttons\n      document.getElementById('refresh-btn')?.addEventListener('click', () => {\n        vscode.postMessage({ command: 'refresh' });\n      });\n\n      document.getElementById('discard-all-btn')?.addEventListener('click', () => {\n        vscode.postMessage({ command: 'discardAll' });\n      });\n\n      document.getElementById('keep-all-btn')?.addEventListener('click', () => {\n        vscode.postMessage({ command: 'keepAll' });\n      });\n\n      // Individual file action buttons\n      document.querySelectorAll('.change-item').forEach(item => {\n        const filePath = item.getAttribute('data-file-path');\n\n        item.querySelector('.view-btn')?.addEventListener('click', () => {\n          vscode.postMessage({ command: 'viewFile', filePath });\n        });\n\n        item.querySelector('.open-btn')?.addEventListener('click', () => {\n          vscode.postMessage({ command: 'openFile', filePath });\n        });\n\n        item.querySelector('.discard-btn')?.addEventListener('click', () => {\n          vscode.postMessage({ command: 'discardFile', filePath });\n        });\n\n        item.querySelector('.keep-btn')?.addEventListener('click', () => {\n          vscode.postMessage({ command: 'keepFile', filePath });\n        });\n      });\n\n      // Double-click to open file\n      document.querySelectorAll('.change-item').forEach(item => {\n        item.addEventListener('dblclick', () => {\n          const filePath = item.getAttribute('data-file-path');\n          vscode.postMessage({ command: 'openFile', filePath });\n        });\n      });\n    `;\n  }\n\n  private getNonce(): string {\n    let text = '';\n    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    for (let i = 0; i < 32; i++) {\n      text += possible.charAt(Math.floor(Math.random() * possible.length));\n    }\n    return text;\n  }\n\n  public dispose(): void {\n    FileChangePanel.currentPanel = undefined;\n\n    // Clean up our resources\n    this.panel.dispose();\n\n    while (this.disposables.length) {\n      const x = this.disposables.pop();\n      if (x) {\n        x.dispose();\n      }\n    }\n  }\n}\n"]}