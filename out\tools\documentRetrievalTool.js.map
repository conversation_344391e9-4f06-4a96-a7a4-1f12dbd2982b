{"version": 3, "file": "documentRetrievalTool.js", "sourceRoot": "", "sources": ["../../src/tools/documentRetrievalTool.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,6BAAwB;AACxB,qDAA0E;AAG1E;;GAEG;AACH,MAAM,MAAM;IACF,MAAM,CAAC,QAAQ,CAAS;IAEhC;;OAEG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,QAAQ,GAAG,IAAI,MAAM,EAAE,CAAC;QACjC,CAAC;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,OAAe,EAAE,GAAG,IAAe;QAC9C,OAAO,CAAC,KAAK,CAAC,WAAW,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,OAAe,EAAE,GAAG,IAAe;QAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,CAAC,KAAK,CAAC,WAAW,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;CACF;AAkBD;;GAEG;AACH,MAAa,qBAAsB,SAAQ,yBAAQ;IAChC,SAAS,CAAQ;IACjB,eAAe,CAA2B;IAE3C,IAAI,GAAG,eAAwB,CAAC;IAChC,QAAQ,GAAG,WAAW,CAAC;IACvB,MAAM,GAA4B,EAAE,CAAC;IACrC,QAAQ,GAA4B,EAAE,CAAC;IACvC,OAAO,GAAG,OAAO,CAAC;IAElB,MAAM,GAAG,OAAC,CAAC,KAAK,CAAC;QAC/B,sBAAsB;QACtB,OAAC,CAAC,MAAM,EAAE;aACP,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;aAC/B,QAAQ,CAAC,2CAA2C,CAAC;QAExD,sCAAsC;QACtC,OAAC,CAAC,MAAM,CAAC;YACP,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;iBACd,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;iBAC/B,QAAQ,CAAC,2CAA2C,CAAC;YACxD,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC;gBAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;qBACd,GAAG,EAAE;qBACL,QAAQ,EAAE;qBACV,GAAG,CAAC,GAAG,EAAE,sBAAsB,CAAC;qBAChC,QAAQ,EAAE;qBACV,QAAQ,CAAC,uCAAuC,CAAC;gBACpD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;qBACjB,GAAG,CAAC,CAAC,CAAC;qBACN,GAAG,CAAC,CAAC,CAAC;qBACN,QAAQ,EAAE;qBACV,QAAQ,CAAC,+BAA+B,CAAC;gBAC5C,eAAe,EAAE,OAAC,CAAC,OAAO,EAAE;qBACzB,QAAQ,EAAE;qBACV,QAAQ,CAAC,sCAAsC,CAAC;gBACnD,OAAO,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,OAAO,EAAE,CAAC;qBAC3B,QAAQ,EAAE;qBACV,QAAQ,CAAC,2CAA2C,CAAC;gBACxD,cAAc,EAAE,OAAC,CAAC,OAAO,EAAE;qBACxB,QAAQ,EAAE;qBACV,QAAQ,CAAC,iEAAiE,CAAC;aAC/E,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;SACvB,CAAC,CAAC,MAAM,EAAE;KACZ,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,EAAwD,EAAE;QAC3E,8DAA8D;QAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QACvC,CAAC;QACD,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEa,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,YAAY,GAAG,OAAC,CAAC,MAAM,CAAC;QACtC,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE;QACpB,MAAM,EAAE,OAAC,CAAC,GAAG,EAAE;QACf,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC5B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;QAClB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAClC,CAAC,CAAC;IAEH,4EAA4E;IAC5D,WAAW,CAAS;IAEpC;;;;;;;OAOG;IACH,YACE,QAAe,EACf,UAAoC,EAAE,EACtC,IAAI,GAAG,yBAAyB,EAChC,WAAW,GAAG,4EAA4E;QAE1F,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAWM,KAAK,CAAC,OAAO,CAClB,iBAAiD,EACjD,cAAyC,EACzC,YAA2B;QAE3B,gCAAgC;QAChC,IAAI,UAA8B,CAAC;QACnC,IAAI,KAAgB,CAAC;QACrB,IAAI,OAAiC,CAAC;QAEtC,IAAI,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;YAC7E,UAAU,GAAG,iBAAiB,CAAC;YAC/B,KAAK,GAAG,cAA2B,CAAC;YACpC,OAAO,GAAG,YAAY,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,iBAA8B,CAAC;YACvC,OAAO,GAAG,cAA0C,CAAC;QACvD,CAAC;QAED,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC;YAED,0CAA0C;YAC1C,MAAM,kBAAkB,GAAG,UAAU,IAAI,UAAU,CAAC;YAEpD,+BAA+B;YAC/B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEzD,iDAAiD;YACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAElE,qCAAqC;YACrC,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,eAAe,EAAE,kBAAkB,CAAC,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,oCAAoC;YACpC,MAAM,kBAAkB,GAAG,UAAU,IAAK,KAAa,EAAE,MAAM,IAAI,UAAU,CAAC;YAC9E,OAAO,IAAI,CAAC,iBAAiB,CAC3B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAC3B,KAAK,EACL,kBAAkB,CACnB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,KAAc;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM;iBACzC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;iBAC7C,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC;YACrE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,cAAc,CAAC,KAAc;QACzC,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC;YACrF,MAAM,IAAI,KAAK,CAAC,kBAAkB,YAAY,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,eAAe,CAC3B,SAA+D,EAC/D,aAAwB;QAExB,IAAI,CAAC;YACH,6BAA6B;YAC7B,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YAElE,+EAA+E;YAC/E,MAAM,SAAS,GAAc;gBAC3B,GAAG,aAAa;gBAChB,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,OAAO;gBACP,qCAAqC;gBACrC,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;aAC1B,CAAC;YAEF,qDAAqD;YACrD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC3C,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAmC,CAAC;YAClG,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,2BAA2B,EAAE,YAAY,CAAC,CAAC;YACtE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,MAAe,EACf,SAA+D,EAC/D,UAAkB;QAElB,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,UAAU;YACV,QAAQ,EAAE;gBACR,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,iBAAiB,CACvB,YAAoB,EACpB,KAAgB,EAChB,UAAkB;QAElB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,UAAU;YACV,QAAQ,EAAE;gBACR,KAAK,EAAE,YAAY;gBACnB,KAAK;aACN;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAc;QACpC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC,OAAO,CAAC;QACvB,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,2BAA2B,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,qBAAqB;QAC1B,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,kBAAkB;qBAChC;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,KAAK,EAAE;gCACL,IAAI,EAAE,QAAQ;gCACd,WAAW,EAAE,gDAAgD;6BAC9D;4BACD,QAAQ,EAAE;gCACR,IAAI,EAAE,QAAQ;gCACd,OAAO,EAAE,CAAC;gCACV,OAAO,EAAE,CAAC;gCACV,WAAW,EAAE,+BAA+B;6BAC7C;4BACD,eAAe,EAAE;gCACf,IAAI,EAAE,SAAS;gCACf,WAAW,EAAE,sCAAsC;6BACpD;4BACD,OAAO,EAAE;gCACP,IAAI,EAAE,QAAQ;gCACd,oBAAoB,EAAE,IAAI;gCAC1B,WAAW,EAAE,2CAA2C;6BACzD;4BACD,cAAc,EAAE;gCACd,IAAI,EAAE,SAAS;gCACf,WAAW,EAAE,iEAAiE;6BAC/E;yBACF;qBACF;iBACF;gBACD,QAAQ,EAAE,CAAC,OAAO,CAAC;aACpB;SACF,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,OAAsB;QACzC,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,qDAAqD;YACrD,KAAK,CAAC,UAAU,CAAC,SAAoC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,OAAO;QAClB,IAAI,CAAC;YACH,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBACjD,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACjC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,6BAA6B,EAAE,YAAY,CAAC,CAAC;YACxE,2DAA2D;QAC7D,CAAC;IACH,CAAC;CACF;AAjWD,sDAiWC", "sourcesContent": ["/**\n * Enhanced document retrieval tool with context support and schema validation.\n * Wraps a base retrieval tool to provide consistent behavior and type safety.\n */\n\nimport { z } from 'zod';\nimport { BaseTool, ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\n\n/**\n * Local logger implementation for internal tool logging\n */\nclass Logger {\n  private static instance: Logger;\n  \n  /**\n   * Get the singleton instance of the logger\n   */\n  public static getInstance(): Logger {\n    if (!Logger.instance) {\n      Logger.instance = new Logger();\n    }\n    return Logger.instance;\n  }\n\n  /**\n   * Log an error message\n   * @param message The error message\n   * @param args Additional error data\n   */\n  public error(message: string, ...args: unknown[]): void {\n    console.error(`[ERROR] ${message}`, ...args);\n  }\n\n  /**\n   * Log a debug message\n   * @param message The debug message\n   * @param args Additional debug data\n   */\n  public debug(message: string, ...args: unknown[]): void {\n    if (process.env.NODE_ENV === 'development') {\n      console.debug(`[DEBUG] ${message}`, ...args);\n    }\n  }\n}\n\n/**\n * Options for document retrieval\n */\nexport interface DocumentRetrievalOptions {\n  /** Maximum number of documents to return */\n  limit?: number;\n  /** Minimum relevance score (0-1) */\n  minScore?: number;\n  /** Whether to include document metadata */\n  includeMetadata?: boolean;\n  /** Additional filters for document retrieval */\n  filters?: Record<string, unknown>;\n  /** Whether to use semantic search */\n  semanticSearch?: boolean;\n}\n\n/**\n * Enhanced wrapper for document retrieval tools with context awareness\n */\nexport class DocumentRetrievalTool extends BaseTool {\n  private readonly _baseTool: ITool;\n  private readonly _defaultOptions: DocumentRetrievalOptions;\n  \n  public readonly type = 'single-action' as const;\n  public readonly category = 'retrieval';\n  public readonly config: Record<string, unknown> = {};\n  public readonly metadata: Record<string, unknown> = {};\n  public readonly version = '1.0.0';\n  \n  public readonly schema = z.union([\n    // Simple string query\n    z.string()\n      .min(1, 'Query cannot be empty')\n      .describe('The query string to search for documents.'),\n    \n    // Or an object with query and options\n    z.object({\n      query: z.string()\n        .min(1, 'Query cannot be empty')\n        .describe('The query string to search for documents.'),\n      options: z.object({\n        limit: z.number()\n          .int()\n          .positive()\n          .max(100, 'Maximum limit is 100')\n          .optional()\n          .describe('Maximum number of documents to return'),\n        minScore: z.number()\n          .min(0)\n          .max(1)\n          .optional()\n          .describe('Minimum relevance score (0-1)'),\n        includeMetadata: z.boolean()\n          .optional()\n          .describe('Whether to include document metadata'),\n        filters: z.record(z.unknown())\n          .optional()\n          .describe('Additional filters for document retrieval'),\n        semanticSearch: z.boolean()\n          .optional()\n          .describe('Whether to use semantic search (true) or keyword search (false)')\n      }).strict().optional()\n    }).strict()\n  ]).transform((input): { query: string; options: DocumentRetrievalOptions } => {\n    // Normalize input to always return a query string and options\n    if (typeof input === 'string') {\n      return { query: input, options: {} };\n    }\n    return { query: input.query, options: input.options || {} };\n  });\n  \n  public readonly inputSchema = this.schema;\n  public readonly outputSchema = z.object({\n    success: z.boolean(),\n    output: z.any(),\n    error: z.string().optional(),\n    toolId: z.string(),\n    actionName: z.string().optional()\n  });\n\n  // Class property for description to satisfy BaseTool's abstract requirement\n  public readonly description: string;\n\n  /**\n   * Creates a new DocumentRetrievalTool instance\n   * @param baseTool - The underlying tool to wrap (must implement ITool)\n   * @param options - Default options for document retrieval\n   * @param name - Optional custom name for the tool (default: 'document-retrieval-tool')\n   * @param description - Optional custom description\n   * @throws {Error} If baseTool is not provided or invalid\n   */\n  constructor(\n    baseTool: ITool,\n    options: DocumentRetrievalOptions = {},\n    name = 'document-retrieval-tool',\n    description = 'Retrieves relevant documents based on a query with contextual enhancement.'\n  ) {\n    super(name);\n\n    if (!baseTool || typeof baseTool.execute !== 'function') {\n      throw new Error('A valid base tool implementing ITool is required');\n    }\n\n    this._baseTool = baseTool;\n    this._defaultOptions = { ...options };\n    this.description = description;\n  }\n\n  /**\n   * Executes the document retrieval with the given input\n   * @param actionName Optional action name (for multi-action tools)\n   * @param input The input parameters for the tool\n   * @param context The agent context (optional)\n   * @returns A promise resolving to the tool result\n   */\n  public async execute(input: ToolInput, context?: AgentContext): Promise<ToolResult>;\n  public async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult>;\n  public async execute(\n    actionNameOrInput: string | undefined | ToolInput,\n    inputOrContext?: ToolInput | AgentContext,\n    maybeContext?: AgentContext\n  ): Promise<ToolResult> {\n    // Handle both method signatures\n    let actionName: string | undefined;\n    let input: ToolInput;\n    let context: AgentContext | undefined;\n\n    if (typeof actionNameOrInput === 'string' || actionNameOrInput === undefined) {\n      actionName = actionNameOrInput;\n      input = inputOrContext as ToolInput;\n      context = maybeContext;\n    } else {\n      input = actionNameOrInput as ToolInput;\n      context = inputOrContext as AgentContext | undefined;\n    }\n\n    try {\n      // Update context if provided\n      if (context) {\n        this.updateContext(context);\n      }\n\n      // Set default action name if not provided\n      const resolvedActionName = actionName || 'retrieve';\n      \n      // Validate and normalize input\n      const normalizedInput = await this.normalizeInput(input);\n\n      // Execute the base tool with the validated input\n      const result = await this.executeBaseTool(normalizedInput, input);\n\n      // Create and return a success result\n      return this.createSuccessResult(result, normalizedInput, resolvedActionName);\n    } catch (error) {\n      // Create and return an error result\n      const resolvedActionName = actionName || (input as any)?.action || 'retrieve';\n      return this.createErrorResult(\n        this.getErrorMessage(error),\n        input,\n        resolvedActionName\n      );\n    }\n  }\n\n  /**\n   * Validates the input against the schema\n   * @param input - The input to validate\n   * @returns Validation result with validity and optional error message\n   */\n  public validateInput(input: unknown): { valid: boolean; error?: string } {\n    const validation = this.schema.safeParse(input);\n    if (validation.success) {\n      return { valid: true };\n    } else {\n      const errorMessage = validation.error.errors\n        .map(e => `${e.path.join('.')}: ${e.message}`)\n        .join('; ');\n      Logger.getInstance().error('Input validation failed:', errorMessage);\n      return { valid: false, error: errorMessage };\n    }\n  }\n\n  /**\n   * Validates and normalizes the input against the schema\n   * @param input - The input to validate and normalize\n   * @returns Normalized input with query and options\n   * @throws {Error} If validation fails\n   */\n  private async normalizeInput(input: unknown): Promise<{ query: string; options: DocumentRetrievalOptions }> {\n    try {\n      return await this.schema.parseAsync(input);\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Invalid input format';\n      throw new Error(`Invalid input: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * Executes the base tool with the validated input\n   * @param validated - The validated input\n   * @param originalInput - The original input for context\n   * @returns The result from the base tool\n   */\n  private async executeBaseTool(\n    validated: { query: string; options: DocumentRetrievalOptions },\n    originalInput: ToolInput\n  ): Promise<unknown> {\n    try {\n      // Merge with default options\n      const options = { ...this._defaultOptions, ...validated.options };\n\n      // Prepare the input for the base tool, ensuring it matches ToolInput interface\n      const baseInput: ToolInput = {\n        ...originalInput,\n        query: validated.query,\n        options,\n        // Ensure required fields are present\n        action: 'retrieve',\n        toolId: this._baseTool.id\n      };\n\n      // Execute the base tool with the proper input format\n      if (this._baseTool.type === 'multi-action') {\n        return await this._baseTool.execute('retrieve', baseInput);\n      } else {\n        return await this._baseTool.execute(baseInput);\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error executing base tool';\n      Logger.getInstance().error('Error in executeBaseTool:', errorMessage);\n      throw error;\n    }\n  }\n\n  /**\n   * Creates a success result object\n   */\n  private createSuccessResult(\n    result: unknown,\n    validated: { query: string; options: DocumentRetrievalOptions },\n    actionName: string\n  ): ToolResult {\n    return {\n      success: true,\n      output: result,\n      toolId: this.id,\n      actionName,\n      metadata: {\n        query: validated.query,\n        options: validated.options\n      }\n    };\n  }\n\n  /**\n   * Creates an error result object\n   */\n  private createErrorResult(\n    errorMessage: string,\n    input: ToolInput,\n    actionName: string\n  ): ToolResult {\n    return {\n      success: false,\n      error: errorMessage,\n      toolId: this.id,\n      actionName,\n      metadata: {\n        error: errorMessage,\n        input\n      }\n    };\n  }\n\n  /**\n   * Extracts error message from unknown error type\n   */\n  private getErrorMessage(error: unknown): string {\n    if (error instanceof Error) {\n      return error.message;\n    }\n    if (typeof error === 'string') {\n      return error;\n    }\n    return 'An unknown error occurred';\n  }\n\n  /**\n   * Gets the tool definition in a format suitable for consumption by LLMs\n   * @returns The tool definition in a standardized format\n   */\n  public getDefinitionForModel(): Record<string, unknown> {\n    return {\n      name: this.name,\n      description: this.description,\n      parameters: {\n        type: 'object',\n        properties: {\n          query: { \n            type: 'string', \n            description: 'The search query' \n          },\n          options: {\n            type: 'object',\n            properties: {\n              limit: { \n                type: 'number', \n                description: 'Maximum number of results to return (max: 100)' \n              },\n              minScore: { \n                type: 'number', \n                minimum: 0,\n                maximum: 1,\n                description: 'Minimum relevance score (0-1)' \n              },\n              includeMetadata: { \n                type: 'boolean', \n                description: 'Whether to include document metadata' \n              },\n              filters: { \n                type: 'object', \n                additionalProperties: true,\n                description: 'Additional filters for document retrieval' \n              },\n              semanticSearch: { \n                type: 'boolean', \n                description: 'Whether to use semantic search (true) or keyword search (false)' \n              }\n            }\n          }\n        },\n        required: ['query']\n      }\n    };\n  }\n\n  /**\n   * Updates the context for the tool\n   * @param context - The new agent context (optional)\n   */\n  public updateContext(context?: AgentContext): void {\n    if (context) {\n      super.setContext(context);\n    } else {\n      // If context is undefined, clear the current context\n      super.setContext(undefined as unknown as AgentContext);\n    }\n  }\n\n  /**\n   * Gets the current context\n   * @returns The current agent context or undefined if not set\n   */\n  public getContext(): AgentContext | undefined {\n    return super.getContext();\n  }\n\n  /**\n   * Cleans up any resources used by the tool\n   * @returns A promise that resolves when cleanup is complete\n   */\n  public async dispose(): Promise<void> {\n    try {\n      if (typeof this._baseTool.dispose === 'function') {\n        await this._baseTool.dispose();\n      }\n    } catch (error) {\n      const errorMessage = this.getErrorMessage(error);\n      Logger.getInstance().error('Error during tool disposal:', errorMessage);\n      // Don't throw from dispose to prevent masking other errors\n    }\n  }\n}\n"]}