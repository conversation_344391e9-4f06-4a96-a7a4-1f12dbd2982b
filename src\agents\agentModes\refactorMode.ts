import * as vscode from 'vscode';
import { OperationMode, ContextSource, ContextType } from './operationMode';
import { Agent } from '../agentUtilities/agent';
import { LLMGenerateParams } from '../../llm/types';
import { Logger } from '../../logger';
import { contextManager } from './contextManager';

/**
 * Refactor Mode - Code refactoring and optimization
 */
export class RefactorMode extends OperationMode {
  readonly id = 'refactor';
  readonly displayName = 'Refactor';
  readonly description = 'Refactor and optimize code structure';
  readonly icon = '$(tools)';
  readonly defaultContextType = ContextType.SELECTED_FILES;
  readonly requiresHumanVerification = true; // Refactoring should be verified
  readonly supportsMultipleAgents = false;

  /**
     * Process a user message in Refactor mode
     */
  async processMessage(
    message: string,
    agent: Agent,
    contextSource: ContextSource,
    additionalParams?: Record<string, any>
  ): Promise<string> {
    try {
      Logger.instance.info(`Processing message in Refactor mode: ${message}`);

      // Get context content
      const contextContent = await contextManager.getContextContent(contextSource);

      // Add memory context if available
      let memoryContext = '';
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          const relevantMemories = await agentMemory.getRelevantMemories(message);
          if (relevantMemories && relevantMemories.length > 0) {
            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to refactor context`);
          }
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to retrieve memory context for refactor:', memoryError);
        // Continue without memory context
      }

      // Prepare the refactoring prompt
      const prompt = `
You are a code refactoring expert. Analyze and refactor the following code based on the request.

Refactoring Request: ${message}

Code to Refactor:
${contextContent}

${memoryContext}

Please provide:
1. Analysis of current code structure and issues
2. Refactored code with improvements
3. Explanation of changes made
4. Benefits of the refactoring
5. Potential risks or considerations
6. Testing recommendations

Focus on:
- Code readability and maintainability
- Performance optimization
- Design patterns and best practices
- Reducing complexity and duplication
- Improving error handling

Provide the refactored code in proper code blocks with clear explanations.
`;

      // Generate response using the agent
      const response = await agent.generate(prompt, this.getLLMParams());

      // Store the refactoring session in memory
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          await agentMemory.addMessage('user', `Refactor request: ${message}`);
          await agentMemory.addMessage('assistant', response);
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to store refactor session in memory:', memoryError);
        // Continue without storing in memory
      }

      return response;
    } catch (error) {
      Logger.instance.error('Error processing message in Refactor mode:', error);
      return `Error processing your refactoring request: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
     * Get LLM parameters specific to Refactor mode
     */
  getLLMParams(): LLMGenerateParams {
    return {
      prompt: '',
      modelId: '',
      temperature: 0.1, // Very low temperature for consistent refactoring
      maxTokens: 3000,  // High token limit for detailed refactoring
      stopSequences: [],
      mode: 'edit'
    };
  }
}
