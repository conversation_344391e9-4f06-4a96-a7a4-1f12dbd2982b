{"version": 3, "file": "clipboardService.js", "sourceRoot": "", "sources": ["../../src/tools/clipboardService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,kDAA0B;AAC1B,mCAAsC;AACtC,2CAAyC;AACzC,mCAA+C;AAC/C,gEAAkD;AAClD,6DAAyE;AAezE,MAAM,uBAAwB,SAAQ,qBAAY;IACtC,aAAa,GAAW,CAAC,CAAC;IAC1B,WAAW,GAAW,CAAC,CAAC;IACxB,cAAc,GAAW,CAAC,CAAC;IAC3B,YAAY,GAAW,CAAC,CAAC;IACzB,WAAW,GAAW,CAAC,CAAC;IACf,UAAU,GAAa,EAAE,CAAC;IAC1B,aAAa,GAAW,IAAI,CAAC;IAE9C;QACI,KAAK,EAAE,CAAC;IACZ,CAAC;IAEM,cAAc;QACjB,OAAO,wBAAW,CAAC,GAAG,EAAE,CAAC;IAC7B,CAAC;IAEM,aAAa,CAAC,SAAiB,EAAE,KAAa;QACjD,MAAM,QAAQ,GAAG,wBAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC/C,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAEM,WAAW,CAAC,SAAiB,EAAE,KAAY;QAC9C,MAAM,QAAQ,GAAG,wBAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC/C,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAEO,eAAe,CAAC,QAAgB,EAAE,KAAa,EAAE,OAAgB,EAAE,KAAa;QACpF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,IAAI,QAAQ,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QAE1D,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC;QAC9B,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;QAED,0CAA0C;QAC1C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9C,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAChD,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;gBAChC,IAAI,CAAC,cAAc,IAAI,eAAe,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,GAAG,IAAI,CAAC,UAAU,EAAE;YACpB,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;gBACf,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACrB,CAAC,CAAC,CAAC,SAAS;SAChB,CAAC,CAAC;IACP,CAAC;IAEM,UAAU;QACb,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;gBACvC,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM;gBAC9C,CAAC,CAAC,CAAC;YACP,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,cAAc,EAAE,IAAI,CAAC,WAAW;SACnC,CAAC;IACN,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC9B,CAAC;CACJ;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,KAAK;IACQ;IAA7C,YAAY,OAAe,EAAkB,KAAa;QACtD,KAAK,CAAC,OAAO,CAAC,CAAC;QAD0B,UAAK,GAAL,KAAK,CAAQ;QAEtD,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IACjC,CAAC;CACJ;AALD,wCAKC;AAED;;GAEG;AACH,MAAa,oBAAqB,SAAQ,cAAc;IACpD,YAAY,OAAe,EAAE,KAAa;QACtC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;IACvC,CAAC;CACJ;AALD,oDAKC;AAED;;GAEG;AACH,MAAa,uBAAwB,SAAQ,cAAc;IACvD,YAAY,OAAe,EAAE,KAAa;QACtC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;IAC1C,CAAC;CACJ;AALD,0DAKC;AAED;;GAEG;AACH,MAAa,oBAAqB,SAAQ,cAAc;IACpD,YAAY,OAAe,EAAE,KAAa;QACtC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;IACvC,CAAC;CACJ;AALD,oDAKC;AAED;;GAEG;AACH,MAAa,gBAAiB,SAAQ,cAAc;IACH;IAA8B;IAA3E,YAAY,OAAe,EAAkB,IAAY,EAAkB,KAAa;QACpF,KAAK,CAAC,GAAG,OAAO,WAAW,IAAI,YAAY,KAAK,GAAG,CAAC,CAAC;QADZ,SAAI,GAAJ,IAAI,CAAQ;QAAkB,UAAK,GAAL,KAAK,CAAQ;QAEpF,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC;IACnC,CAAC;CACJ;AALD,4CAKC;AAgBD;;GAEG;AACH,MAAM,eAAe,GAAsC;IACvD,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,GAAG;IACf,cAAc,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;IACzC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM;CAChC,CAAC;AA2EF;;;;;;;;;;;;;;;GAeG;AACH,MAAa,sBAAuB,SAAQ,qBAAY;IACnC,MAAM,CAAU;IAChB,OAAO,CAAoC;IAC3C,cAAc,CAA0B;IACxC,mBAAmB,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,CAAU,CAAC;IAClF,UAAU,GAAG,KAAK,CAAC;IAE3B,YAAY,MAAgB,EAAE,UAAmC,EAAE;QAC/D,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,OAAO,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,EAAE,CAAC;QAClD,IAAI,CAAC,cAAc,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEpD,8DAA8D;QAC9D,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE;YACzC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;YAClE,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,QAAQ;QACjB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QAEvD,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBAC9C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAErD,kBAAkB;gBAClB,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;oBAC9C,MAAM,IAAI,gBAAgB,CACtB,2CAA2C,EAC3C,MAAM,CAAC,MAAM,EACb,IAAI,CAAC,OAAO,CAAC,cAAc,CAC9B,CAAC;gBACN,CAAC;gBAED,0BAA0B;gBAC1B,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC5B,MAAM,IAAI,6BAAa,CAAC,2DAA2D,CAAC,CAAC;gBACzF,CAAC;gBAED,0CAA0C;gBAC1C,MAAM,OAAO,GAAG,6BAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBACvD,IAAI,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,qEAAqE;oBACtF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;gBAChF,CAAC;gBAED,8CAA8C;gBAC9C,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACxB,MAAM,IAAI,6BAAa,CAAC,0CAA0C,CAAC,CAAC;gBACxE,CAAC;gBAED,OAAO,MAAM,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACvD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,IAAI,EAAE,6BAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC;aAChD,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,KAAc,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,SAAS,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ;QACjB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QAEvD,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEnC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,MAAM,MAAM,GAAgB;gBACxB,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;gBACvC,QAAQ,EAAE;oBACN,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;iBACpC;aACJ,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACtE,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,KAAc,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,SAAS,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,OAAoB;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QAEvD,IAAI,CAAC;YACD,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAChE,MAAM,IAAI,gBAAgB,CACtB,iCAAiC,EACjC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAC/B,IAAI,CAAC,OAAO,CAAC,cAAc,CAC9B,CAAC;YACN,CAAC;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACjC,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,KAAc,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,SAAS,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,SAAS;QAClB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QAEvD,IAAI,CAAC;YACD,MAAM,UAAU,GAAG,wDAAa,YAAY,GAAC,CAAC;YAC9C,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC;YACnD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;YAE3E,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YACtF,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;gBACtD,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,MAAM,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAE/B,qBAAqB;YACrB,IAAI,CAAC,6BAAa,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAClD,MAAM,IAAI,6BAAa,CAAC,8BAA8B,gBAAgB,EAAE,CAAC,CAAC;YAC9E,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,6BAAa,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,6BAAa,CAAC,8BAA8B,CAAC,CAAC;YAC5D,CAAC;YAED,kBAAkB;YAClB,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACxD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACxC,MAAM,IAAI,gBAAgB,CACtB,+BAA+B,EAC/B,OAAO,EACP,IAAI,CAAC,OAAO,CAAC,cAAc,CAC9B,CAAC;YACN,CAAC;YAED,iCAAiC;YACjC,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEtD,gCAAgC;YAChC,IAAI,6BAAa,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,6BAAa,CAAC,iDAAiD,CAAC,CAAC;YAC/E,CAAC;YAED,kCAAkC;YAClC,IAAI,CAAC,6BAAa,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,CAAC;gBAChD,MAAM,IAAI,6BAAa,CAAC,gCAAgC,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,aAAsD,CAAC;YAC3D,IAAI,QAAQ,GAA+B,EAAE,CAAC;YAE9C,IAAI,gBAAgB,KAAK,WAAW,EAAE,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;gBAClD,CAAC,aAAa,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YACnF,CAAC;iBAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACrD,aAAa,GAAG,gBAAgB,CAAC;gBACjC,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,uBAAuB,CAC7B,2BAA2B,gBAAgB,sBAAsB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACzG,CAAC;YACN,CAAC;YAED,+BAA+B;YAC/B,MAAM,OAAO,GAAG,6BAAa,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC,CAAC,2EAA2E;gBAC7F,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;gBACnE,MAAM,IAAI,6BAAa,CAAC,mCAAmC,CAAC,CAAC;YACjE,CAAC;YAED,MAAM,WAAW,GAAG,6BAAa,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACnE,MAAM,MAAM,GAAmB;gBAC3B,QAAQ,EAAE,aAAa;gBACvB,UAAU;gBACV,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE;oBACN,GAAG,QAAQ;oBACX,YAAY,EAAE,WAAW;iBAC5B;aACJ,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,aAAa,uBAAuB,EAAE;gBACzE,QAAQ;gBACR,IAAI,EAAE,WAAW;gBACjB,OAAO;aACV,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,KAAc,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,SAAS,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,KAAqB;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QAEvD,IAAI,CAAC;YACD,qBAAqB;YACrB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,uBAAuB,CAC7B,2BAA2B,KAAK,CAAC,QAAQ,sBAAsB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACvG,CAAC;YACN,CAAC;YAED,IAAI,CAAC,6BAAa,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,MAAM,IAAI,6BAAa,CAAC,qBAAqB,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnE,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,6BAAa,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClD,MAAM,IAAI,6BAAa,CAAC,8BAA8B,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE5D,kBAAkB;YAClB,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC;YACnC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACxC,MAAM,IAAI,gBAAgB,CACtB,+BAA+B,EAC/B,OAAO,EACP,IAAI,CAAC,OAAO,CAAC,cAAc,CAC9B,CAAC;YACN,CAAC;YAED,kCAAkC;YAClC,IAAI,CAAC,6BAAa,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,CAAC;gBAChD,MAAM,IAAI,6BAAa,CAAC,gCAAgC,CAAC,CAAC;YAC9D,CAAC;YAED,gCAAgC;YAChC,IAAI,6BAAa,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,6BAAa,CAAC,iDAAiD,CAAC,CAAC;YAC/E,CAAC;YAED,+BAA+B;YAC/B,MAAM,OAAO,GAAG,6BAAa,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC;gBACjB,MAAM,IAAI,6BAAa,CAAC,mCAAmC,CAAC,CAAC;YACjE,CAAC;YAED,yCAAyC;YACzC,MAAM,YAAY,GAAG,6BAAa,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAEpE,wCAAwC;YACxC,MAAM,OAAO,GAAG,QAAQ,KAAK,CAAC,QAAQ,WAAW,KAAK,CAAC,UAAU,EAAE,CAAC;YACpE,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACjC,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBAE/D,6BAA6B;gBAC7B,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnD,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACvB,MAAM,IAAI,6BAAa,CAAC,wCAAwC,CAAC,CAAC;gBACtE,CAAC;gBAED,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBACjF,MAAM,gBAAgB,GAAG,6BAAa,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;gBAE/E,IAAI,gBAAgB,KAAK,YAAY,EAAE,CAAC;oBACpC,MAAM,IAAI,6BAAa,CAAC,mCAAmC,CAAC,CAAC;gBACjE,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,KAAK,CAAC,QAAQ,qBAAqB,EAAE;gBACzE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,YAAY;gBAClB,OAAO;gBACP,QAAQ,EAAE,KAAK,CAAC,QAAQ;aAC3B,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,KAAc,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,SAAS,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,IAAY;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QACvD,MAAM,YAAY,GAAG,6BAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAE7D,IAAI,CAAC;YACD,kBAAkB;YAClB,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACrC,MAAM,IAAI,gBAAgB,CACtB,iCAAiC,EACjC,IAAI,EACJ,IAAI,CAAC,OAAO,CAAC,cAAc,CAC9B,CAAC;YACN,CAAC;YAED,2BAA2B;YAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,6BAAa,CAAC,gCAAgC,CAAC,CAAC;YAC9D,CAAC;YAED,uCAAuC;YACvC,IAAI,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzC,MAAM,IAAI,6BAAa,CAAC,qCAAqC,CAAC,CAAC;YACnE,CAAC;YAED,8BAA8B;YAC9B,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,6BAAa,CAAC,sCAAsC,CAAC,CAAC;YACpE,CAAC;YAED,2EAA2E;YAC3E,MAAM,OAAO,GAAG,6BAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACrD,IAAI,OAAO,GAAG,GAAG,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACjC,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;gBAE5D,uCAAuC;gBACvC,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC/D,MAAM,gBAAgB,GAAG,6BAAa,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;gBAE7E,IAAI,gBAAgB,KAAK,YAAY,EAAE,CAAC;oBACpC,MAAM,IAAI,6BAAa,CAAC,uCAAuC,CAAC,CAAC;gBACrE,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACtD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,IAAI,EAAE,YAAY;gBAClB,OAAO;aACV,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,KAAc,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,SAAS,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAEM,UAAU;QACb,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;IAC5C,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAe;QACzC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC9C,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAG,iBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErC,MAAM,cAAc,CAAC,QAAQ,CACzB,MAAM,EACN,IAAI,oBAAW,CAAC;gBACZ,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACrC,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ;oBACrC,IAAI,CAAC;wBACD,MAAM,SAAS,GAAG,MAAM,IAAA,eAAK,EAAC,KAAK,CAAC;6BAC/B,QAAQ,CAAC,KAAK,CAAC;6BACf,QAAQ,EAAE,CAAC;wBAChB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACvB,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACnB,CAAC;oBAAC,OAAO,GAAG,EAAE,CAAC;wBACX,QAAQ,CAAC,GAAY,CAAC,CAAC;oBAC3B,CAAC;gBACL,CAAC;aACJ,CAAC,CACL,CAAC;YAEF,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAC9D,OAAO,CAAC,WAAW,EAAE,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,oBAAoB,CAAC,8BAA8B,EAAE,KAAc,CAAC,CAAC;QACnF,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACzC,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAChD,OAAO;gBACH,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,IAAI,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAChE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,QAAgB;QACzC,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAmD,CAAC,CAAC;IAClG,CAAC;IAEO,aAAa,CAAC,IAAY;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QAC3D,OAAO,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,cAAc,CAAI,SAA2B,EAAE,cAAsB,IAAI,CAAC,OAAO,CAAC,UAAU;QACtG,IAAI,SAA4B,CAAC;QAEjC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC;gBACD,OAAO,MAAM,SAAS,EAAE,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,SAAS,GAAG,KAAc,CAAC;gBAC3B,IAAI,OAAO,GAAG,WAAW,EAAE,CAAC;oBACxB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;oBACjE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,OAAO,IAAI,WAAW,UAAU,KAAK,IAAI,CAAC,CAAC;oBAC9E,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,SAAS,CAAC;IACpB,CAAC;IAEO,SAAS,CAAC,OAAe,EAAE,KAAc;QAC7C,IAAI,KAAK,YAAY,cAAc,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACzE,OAAO,IAAI,oBAAoB,CAAC,GAAG,OAAO,KAAK,YAAY,EAAE,EAAE,KAAc,CAAC,CAAC;QACnF,CAAC;QAED,OAAO,IAAI,cAAc,CAAC,GAAG,OAAO,KAAK,YAAY,EAAE,EAAE,KAAc,CAAC,CAAC;IAC7E,CAAC;CACJ;AArcD,wDAqcC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ILogger } from './toolFramework';\nimport sharp from 'sharp';\nimport { EventEmitter } from 'events';\nimport { performance } from 'perf_hooks';\nimport { Readable, PassThrough } from 'stream';\nimport * as streamPromises from 'stream/promises';\nimport { SecurityUtils, SecurityError } from '../security/securityUtils';\n\nexport interface ClipboardMetrics {\n    successCount: number;\n    errorCount: number;\n    averageDuration: number;\n    maxDuration: number;\n    bytesProcessed: number;\n    lastError?: {\n        name: string;\n        message: string;\n        stack?: string;\n    };\n}\n\nclass ClipboardMetricsService extends EventEmitter {\n    private _successCount: number = 0;\n    private _errorCount: number = 0;\n    private _totalDuration: number = 0;\n    private _maxDuration: number = 0;\n    private _totalBytes: number = 0;\n    private readonly operations: number[] = [];\n    private readonly maxOperations: number = 1000;\n\n    constructor() {\n        super();\n    }\n\n    public startOperation(): number {\n        return performance.now();\n    }\n\n    public recordSuccess(startTime: number, bytes: number): void {\n        const duration = performance.now() - startTime;\n        this.recordOperation(duration, bytes, true);\n    }\n\n    public recordError(startTime: number, error: Error): void {\n        const duration = performance.now() - startTime;\n        this.recordOperation(duration, 0, false, error);\n    }\n\n    private recordOperation(duration: number, bytes: number, success: boolean, error?: Error): void {\n        this.operations.push(duration);\n        this._totalDuration += duration;\n        this._maxDuration = Math.max(this._maxDuration, duration);\n\n        if (success) {\n            this._successCount++;\n            this._totalBytes += bytes;\n        } else {\n            this._errorCount++;\n        }\n\n        // Maintain a rolling window of operations\n        if (this.operations.length > this.maxOperations) {\n            const removedDuration = this.operations.shift();\n            if (removedDuration !== undefined) {\n                this._totalDuration -= removedDuration;\n            }\n        }\n\n        this.emit('metricsUpdated', {\n            ...this.getMetrics(),\n            lastError: error ? {\n                name: error.name,\n                message: error.message,\n                stack: error.stack\n            } : undefined\n        });\n    }\n\n    public getMetrics(): ClipboardMetrics {\n        return {\n            successCount: this._successCount,\n            errorCount: this._errorCount,\n            averageDuration: this.operations.length > 0 \n                ? this._totalDuration / this.operations.length \n                : 0,\n            maxDuration: this._maxDuration,\n            bytesProcessed: this._totalBytes\n        };\n    }\n\n    public reset(): void {\n        this._successCount = 0;\n        this._errorCount = 0;\n        this._totalDuration = 0;\n        this._maxDuration = 0;\n        this._totalBytes = 0;\n        this.operations.length = 0;\n        this.emit('metricsReset');\n    }\n}\n\n/**\n * Base error class for clipboard-related errors\n */\nexport class ClipboardError extends Error {\n    constructor(message: string, public readonly cause?: Error) {\n        super(message);\n        this.name = 'ClipboardError';\n    }\n}\n\n/**\n * Error thrown when clipboard access is denied\n */\nexport class ClipboardAccessError extends ClipboardError {\n    constructor(message: string, cause?: Error) {\n        super(message, cause);\n        this.name = 'ClipboardAccessError';\n    }\n}\n\n/**\n * Error thrown when unsupported content is encountered\n */\nexport class UnsupportedContentError extends ClipboardError {\n    constructor(message: string, cause?: Error) {\n        super(message, cause);\n        this.name = 'UnsupportedContentError';\n    }\n}\n\n/**\n * Error thrown when image processing fails\n */\nexport class ImageProcessingError extends ClipboardError {\n    constructor(message: string, cause?: Error) {\n        super(message, cause);\n        this.name = 'ImageProcessingError';\n    }\n}\n\n/**\n * Error thrown when content size exceeds limits\n */\nexport class ContentSizeError extends ClipboardError {\n    constructor(message: string, public readonly size: number, public readonly limit: number) {\n        super(`${message} (size: ${size}, limit: ${limit})`);\n        this.name = 'ContentSizeError';\n    }\n}\n\n/**\n * Configuration options for the clipboard service\n */\nexport interface ClipboardServiceOptions {\n    /** Maximum retries for clipboard operations */\n    maxRetries?: number;\n    /** Delay between retries in milliseconds */\n    retryDelay?: number;\n    /** Maximum size in bytes for clipboard content */\n    maxContentSize?: number;\n    /** Maximum size of a data chunk for processing */\n    chunkSize?: number;\n}\n\n/**\n * Default configuration values\n */\nconst DEFAULT_OPTIONS: Required<ClipboardServiceOptions> = {\n    maxRetries: 3,\n    retryDelay: 100,\n    maxContentSize: 50 * 1024 * 1024, // 50MB\n    chunkSize: 1024 * 1024 // 1MB\n};\n/** Event data for clipboard changes */\nexport type ClipboardChangeEvent = {\n    type: 'text' | 'image' | 'html';\n    source: string;\n};\n\n/** Event data for clipboard errors */\nexport type ClipboardErrorEvent = {\n    error: Error;\n    operation: string;\n};\n\n/** Clipboard operation metrics */\nexport interface ClipboardMetrics {\n    /** Total number of successful operations */\n    successCount: number;\n    /** Total number of failed operations */\n    errorCount: number;\n    /** Average operation duration in milliseconds */\n    averageDuration: number;\n    /** Maximum operation duration in milliseconds */\n    maxDuration: number;\n    /** Total bytes processed */\n    bytesProcessed: number;\n}\n\nexport interface IClipboardService {\n    readText(): Promise<string>;\n    readImage(): Promise<MultimodalData | null>;\n    readHtml(): Promise<HtmlContent | null>;\n    writeText(text: string): Promise<void>;\n    writeImage(image: MultimodalData): Promise<void>;\n    writeHtml(content: HtmlContent): Promise<void>;\n    on(event: 'change', listener: (data: ClipboardChangeEvent) => void): void;\n    on(event: 'error', listener: (data: ClipboardErrorEvent) => void): void;\n    getMetrics(): ClipboardMetrics;\n}\n\n/**\n * Represents image data that can be stored in the clipboard or read from a file.\n */\nexport interface MultimodalData {\n    /** The MIME type of the image data */\n    mimeType: 'image/jpeg' | 'image/png' | 'image/webp';\n    /** The base64-encoded image data */\n    base64Data: string;\n    /** The source of the image data */\n    source: 'clipboard' | 'file';\n    /** Optional metadata */\n    metadata?: {\n        width?: number;\n        height?: number;\n        format?: string;\n        size?: number;\n        securityHash?: string;\n        entropy?: number;\n    };\n}\n\n/**\n * HTML content with optional metadata\n */\nexport interface HtmlContent {\n    /** The HTML content */\n    html: string;\n    /** Optional plain text fallback */\n    plainText?: string;\n    /** Optional metadata */\n    metadata?: {\n        charset?: string;\n        title?: string;\n    };\n}\n\n/**\n * A VS Code-specific implementation of the IClipboardService interface.\n * Provides clipboard functionality within the VS Code environment, including support for\n * both text and image data with automatic format conversion.\n * \n * @implements {IClipboardService}\n * @example\n * ```typescript\n * // Create a new instance with a custom logger\n * const clipboardService = new VSCodeClipboardService(customLogger);\n * \n * // Use the service\n * await clipboardService.writeText('Copied text');\n * const text = await clipboardService.readText();\n * ```\n */\nexport class VSCodeClipboardService extends EventEmitter implements IClipboardService {\n    private readonly logger: ILogger;\n    private readonly options: Required<ClipboardServiceOptions>;\n    private readonly metricsService: ClipboardMetricsService;\n    private readonly supportedImageTypes = ['image/png', 'image/jpeg', 'image/webp'] as const;\n    private isDisposed = false;\n\n    constructor(logger?: ILogger, options: ClipboardServiceOptions = {}) {\n        super();\n        this.logger = logger || console;\n        this.options = { ...DEFAULT_OPTIONS, ...options };\n        this.metricsService = new ClipboardMetricsService();\n\n        // Monitor clipboard changes by watching text document changes\n        vscode.workspace.onDidChangeTextDocument(e => {\n            if (e.contentChanges.length > 0) {\n                this.emit('change', { type: 'text', source: e.document.uri });\n            }\n        });\n    }\n\n    public async readText(): Promise<string> {\n        const startTime = this.metricsService.startOperation();\n\n        try {\n            const text = await this.retryOperation(async () => {\n                const result = await vscode.env.clipboard.readText();\n                \n                // Size validation\n                if (result.length > this.options.maxContentSize) {\n                    throw new ContentSizeError(\n                        'Clipboard text content exceeds size limit',\n                        result.length,\n                        this.options.maxContentSize\n                    );\n                }\n\n                // Content security checks\n                if (/[<>|&;$()]/.test(result)) {\n                    throw new SecurityError('Potential unsafe characters detected in clipboard content');\n                }\n\n                // Check for potentially dangerous content\n                const entropy = SecurityUtils.calculateEntropy(result);\n                if (entropy > 6.5) { // High entropy could indicate encrypted/compressed malicious content\n                    this.logger.warn('High entropy content detected in clipboard', { entropy });\n                }\n\n                // Check for null bytes and control characters\n                if (result.includes('\\0')) {\n                    throw new SecurityError('Null bytes detected in clipboard content');\n                }\n\n                return result;\n            });\n\n            this.metricsService.recordSuccess(startTime, Buffer.byteLength(text));\n            this.logger.debug('Successfully read text from clipboard', { \n                length: text.length,\n                hash: SecurityUtils.generateContentHash(text)\n            });\n            return text;\n        } catch (error) {\n            this.metricsService.recordError(startTime, error as Error);\n            throw this.wrapError('Failed to read clipboard text', error);\n        }\n    }\n\n    public async readHtml(): Promise<HtmlContent | null> {\n        const startTime = this.metricsService.startOperation();\n\n        try {\n            const text = await this.readText();\n            \n            if (!text.trim().startsWith('<')) {\n                return null;\n            }\n\n            const result: HtmlContent = {\n                html: text,\n                plainText: text.replace(/<[^>]+>/g, ''),\n                metadata: {\n                    charset: this.detectCharset(text)\n                }\n            };\n\n            this.metricsService.recordSuccess(startTime, Buffer.byteLength(text));\n            return result;\n        } catch (error) {\n            this.metricsService.recordError(startTime, error as Error);\n            throw this.wrapError('Failed to read HTML content', error);\n        }\n    }\n\n    public async writeHtml(content: HtmlContent): Promise<void> {\n        const startTime = this.metricsService.startOperation();\n\n        try {\n            if (Buffer.byteLength(content.html) > this.options.maxContentSize) {\n                throw new ContentSizeError(\n                    'HTML content exceeds size limit',\n                    Buffer.byteLength(content.html),\n                    this.options.maxContentSize\n                );\n            }\n\n            await this.retryOperation(async () => {\n                await vscode.env.clipboard.writeText(content.html);\n            });\n\n            this.metricsService.recordSuccess(startTime, Buffer.byteLength(content.html));\n        } catch (error) {\n            this.metricsService.recordError(startTime, error as Error);\n            throw this.wrapError('Failed to write HTML content', error);\n        }\n    }\n\n    public async readImage(): Promise<MultimodalData | null> {\n        const startTime = this.metricsService.startOperation();\n\n        try {\n            const clipboardy = await import('clipboardy');\n            const clipboard = clipboardy.default || clipboardy;\n            const clipboardContent = await this.retryOperation(() => clipboard.read());\n\n            const imageMatch = clipboardContent.match(/^data:(image\\/[a-z]+);base64,([\\s\\S]+)$/i);\n            if (!imageMatch) {\n                this.logger.debug('No image data found in clipboard');\n                return null;\n            }\n\n            const detectedMimeType = imageMatch[1];\n            let base64Data = imageMatch[2];\n\n            // Validate MIME type\n            if (!SecurityUtils.isSafeMimeType(detectedMimeType)) {\n                throw new SecurityError(`Unsafe MIME type detected: ${detectedMimeType}`);\n            }\n\n            // Validate base64 data\n            if (!SecurityUtils.validateBase64(base64Data)) {\n                throw new SecurityError('Invalid base64 data detected');\n            }\n\n            // Size validation\n            const rawSize = Buffer.byteLength(base64Data, 'base64');\n            if (rawSize > this.options.maxContentSize) {\n                throw new ContentSizeError(\n                    'Image data exceeds size limit',\n                    rawSize,\n                    this.options.maxContentSize\n                );\n            }\n\n            // Decode and validate image data\n            const imageBuffer = Buffer.from(base64Data, 'base64');\n\n            // Check for potential shellcode\n            if (SecurityUtils.detectShellcode(imageBuffer)) {\n                throw new SecurityError('Potential malicious code detected in image data');\n            }\n\n            // Validate image format signature\n            if (!SecurityUtils.validateImageData(imageBuffer)) {\n                throw new SecurityError('Invalid image format signature');\n            }\n\n            let finalMimeType: typeof this.supportedImageTypes[number];\n            let metadata: MultimodalData['metadata'] = {};\n\n            if (detectedMimeType === 'image/gif') {\n                this.logger.debug('Converting GIF to PNG format');\n                [finalMimeType, base64Data, metadata] = await this.convertGifToPng(base64Data);\n            } else if (this.isSupportedImageType(detectedMimeType)) {\n                finalMimeType = detectedMimeType;\n                metadata = await this.getImageMetadata(imageBuffer);\n            } else {\n                throw new UnsupportedContentError(\n                    `Unsupported image type: ${detectedMimeType}. Supported types: ${this.supportedImageTypes.join(', ')}`\n                );\n            }\n\n            // Calculate and verify entropy\n            const entropy = SecurityUtils.calculateEntropy(imageBuffer);\n            if (entropy > 7.99) { // Extremely high entropy might indicate steganography or encrypted content\n                this.logger.warn('Suspicious image entropy detected', { entropy });\n                throw new SecurityError('Suspicious image content detected');\n            }\n\n            const contentHash = SecurityUtils.generateContentHash(imageBuffer);\n            const result: MultimodalData = {\n                mimeType: finalMimeType,\n                base64Data,\n                source: 'clipboard',\n                metadata: {\n                    ...metadata,\n                    securityHash: contentHash\n                }\n            };\n\n            this.metricsService.recordSuccess(startTime, rawSize);\n            this.logger.debug(`Successfully read ${finalMimeType} image from clipboard`, { \n                metadata,\n                hash: contentHash,\n                entropy\n            });\n            return result;\n        } catch (error) {\n            this.metricsService.recordError(startTime, error as Error);\n            throw this.wrapError('Failed to read image from clipboard', error);\n        }\n    }\n\n    public async writeImage(image: MultimodalData): Promise<void> {\n        const startTime = this.metricsService.startOperation();\n\n        try {\n            // Validate MIME type\n            if (!this.isSupportedImageType(image.mimeType)) {\n                throw new UnsupportedContentError(\n                    `Unsupported image type: ${image.mimeType}. Supported types: ${this.supportedImageTypes.join(', ')}`\n                );\n            }\n\n            if (!SecurityUtils.isSafeMimeType(image.mimeType)) {\n                throw new SecurityError(`Unsafe MIME type: ${image.mimeType}`);\n            }\n\n            // Validate base64 data\n            if (!SecurityUtils.validateBase64(image.base64Data)) {\n                throw new SecurityError('Invalid base64 data detected');\n            }\n\n            const imageBuffer = Buffer.from(image.base64Data, 'base64');\n            \n            // Size validation\n            const rawSize = imageBuffer.length;\n            if (rawSize > this.options.maxContentSize) {\n                throw new ContentSizeError(\n                    'Image data exceeds size limit',\n                    rawSize,\n                    this.options.maxContentSize\n                );\n            }\n\n            // Validate image format signature\n            if (!SecurityUtils.validateImageData(imageBuffer)) {\n                throw new SecurityError('Invalid image format signature');\n            }\n\n            // Check for potential shellcode\n            if (SecurityUtils.detectShellcode(imageBuffer)) {\n                throw new SecurityError('Potential malicious code detected in image data');\n            }\n\n            // Calculate and verify entropy\n            const entropy = SecurityUtils.calculateEntropy(imageBuffer);\n            if (entropy > 7.99) {\n                throw new SecurityError('Suspicious image content detected');\n            }\n\n            // Generate content hash for verification\n            const originalHash = SecurityUtils.generateContentHash(imageBuffer);\n\n            // Write image with content verification\n            const dataUrl = `data:${image.mimeType};base64,${image.base64Data}`;\n            await this.retryOperation(async () => {\n                await Promise.resolve(vscode.env.clipboard.writeText(dataUrl));\n                \n                // Verify the written content\n                const verificationContent = await this.readImage();\n                if (!verificationContent) {\n                    throw new SecurityError('Failed to verify written image content');\n                }\n\n                const verificationBuffer = Buffer.from(verificationContent.base64Data, 'base64');\n                const verificationHash = SecurityUtils.generateContentHash(verificationBuffer);\n\n                if (verificationHash !== originalHash) {\n                    throw new SecurityError('Image content verification failed');\n                }\n            });\n\n            this.metricsService.recordSuccess(startTime, rawSize);\n            this.logger.debug(`Successfully wrote ${image.mimeType} image to clipboard`, {\n                size: rawSize,\n                hash: originalHash,\n                entropy,\n                metadata: image.metadata\n            });\n        } catch (error) {\n            this.metricsService.recordError(startTime, error as Error);\n            throw this.wrapError('Failed to write image to clipboard', error);\n        }\n    }\n\n    public async writeText(text: string): Promise<void> {\n        const startTime = this.metricsService.startOperation();\n        const originalHash = SecurityUtils.generateContentHash(text);\n\n        try {\n            // Size validation\n            const size = Buffer.byteLength(text);\n            if (size > this.options.maxContentSize) {\n                throw new ContentSizeError(\n                    'Text content exceeds size limit',\n                    size,\n                    this.options.maxContentSize\n                );\n            }\n\n            // Check for unsafe content\n            if (text.includes('\\0')) {\n                throw new SecurityError('Null bytes detected in content');\n            }\n\n            // Check for potential script injection\n            if (/<script[\\s>]|javascript:/i.test(text)) {\n                throw new SecurityError('Potential script injection detected');\n            }\n\n            // Check for command injection\n            if (/[<>|&;$()]/.test(text)) {\n                throw new SecurityError('Potential command injection detected');\n            }\n\n            // Check for high entropy (possible encrypted/compressed malicious content)\n            const entropy = SecurityUtils.calculateEntropy(text);\n            if (entropy > 6.5) {\n                this.logger.warn('High entropy content detected', { entropy });\n            }\n\n            await this.retryOperation(async () => {\n                await Promise.resolve(vscode.env.clipboard.writeText(text));\n                \n                // Verify content was written correctly\n                const verificationText = await vscode.env.clipboard.readText();\n                const verificationHash = SecurityUtils.generateContentHash(verificationText);\n                \n                if (verificationHash !== originalHash) {\n                    throw new SecurityError('Clipboard content verification failed');\n                }\n            });\n            \n            this.metricsService.recordSuccess(startTime, size);\n            this.logger.debug('Successfully wrote text to clipboard', { \n                length: text.length,\n                hash: originalHash,\n                entropy\n            });\n        } catch (error) {\n            this.metricsService.recordError(startTime, error as Error);\n            throw this.wrapError('Failed to write to clipboard', error);\n        }\n    }\n\n    public getMetrics() {\n        return this.metricsService.getMetrics();\n    }\n\n    public dispose(): void {\n        if (this.isDisposed) return;\n        this.isDisposed = true;\n        this.removeAllListeners();\n    }\n\n    private async convertGifToPng(gifData: string): Promise<['image/png', string, MultimodalData['metadata']]> {\n        try {\n            const buffer = Buffer.from(gifData, 'base64');\n            const chunks: Buffer[] = [];\n            const stream = Readable.from(buffer);\n            \n            await streamPromises.pipeline(\n                stream,\n                new PassThrough({\n                    highWaterMark: this.options.chunkSize,\n                    async transform(chunk, encoding, callback) {\n                        try {\n                            const processed = await sharp(chunk)\n                                .toFormat('png')\n                                .toBuffer();\n                            chunks.push(processed);\n                            callback(null);\n                        } catch (err) {\n                            callback(err as Error);\n                        }\n                    }\n                })\n            );\n\n            const processedBuffer = Buffer.concat(chunks);\n            const metadata = await this.getImageMetadata(processedBuffer);\n            return ['image/png', processedBuffer.toString('base64'), metadata];\n        } catch (error) {\n            throw new ImageProcessingError('Failed to convert GIF to PNG', error as Error);\n        }\n    }\n\n    private async getImageMetadata(buffer: Buffer): Promise<MultimodalData['metadata']> {\n        try {\n            const metadata = await sharp(buffer).metadata();\n            return {\n                width: metadata.width,\n                height: metadata.height,\n                format: metadata.format,\n                size: buffer.length\n            };\n        } catch (error) {\n            this.logger.warn('Failed to extract image metadata', { error });\n            return {};\n        }\n    }\n\n    private isSupportedImageType(mimeType: string): mimeType is typeof this.supportedImageTypes[number] {\n        return this.supportedImageTypes.includes(mimeType as typeof this.supportedImageTypes[number]);\n    }\n\n    private detectCharset(html: string): string {\n        const match = html.match(/<meta\\s+charset=[\"']?([\\w-]+)/i);\n        return match?.[1] || 'utf-8';\n    }\n\n    private async retryOperation<T>(operation: () => Promise<T>, maxAttempts: number = this.options.maxRetries): Promise<T> {\n        let lastError: Error | undefined;\n\n        for (let attempt = 1; attempt <= maxAttempts; attempt++) {\n            try {\n                return await operation();\n            } catch (error) {\n                lastError = error as Error;\n                if (attempt < maxAttempts) {\n                    const delay = this.options.retryDelay * Math.pow(2, attempt - 1);\n                    this.logger.debug(`Retry attempt ${attempt}/${maxAttempts} after ${delay}ms`);\n                    await new Promise(resolve => setTimeout(resolve, delay));\n                }\n            }\n        }\n\n        throw lastError;\n    }\n\n    private wrapError(message: string, error: unknown): Error {\n        if (error instanceof ClipboardError) {\n            return error;\n        }\n\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        if (errorMessage.includes('access') || errorMessage.includes('permission')) {\n            return new ClipboardAccessError(`${message}: ${errorMessage}`, error as Error);\n        }\n\n        return new ClipboardError(`${message}: ${errorMessage}`, error as Error);\n    }\n}\n"]}