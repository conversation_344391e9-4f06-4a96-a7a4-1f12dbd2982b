{"version": 3, "file": "notifications.js", "sourceRoot": "", "sources": ["../../../src/ui/feedback/notifications.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA,wDAEC;AASD,4CAYC;AAQD,gDASC;AAOD,kDA0FC;AASD,4DA+BC;AAvOD,+CAAiC;AACjC,yCAAsC;AACtC,yCAAyC;AAEzC,6CAA6C;AAC7C,MAAM,yBAAyB,GAAG;IAChC,mCAAmC;IACnC,mCAAmC;IACnC,kCAAkC;IAClC,8CAA8C;IAC9C,oBAAoB;IACpB,sBAAsB;IACtB,SAAS;IACT,WAAW;IACX,iBAAiB;IACjB,wCAAwC;IACxC,sCAAsC;IACtC,oCAAoC;IACpC,sBAAsB;IACtB,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,eAAe;IACf,iBAAiB;IACjB,wBAAwB;IACxB,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,kBAAkB;IAClB,mBAAmB;IACnB,eAAe;IACf,aAAa;IACb,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,YAAY;IACZ,sBAAsB;IACtB,aAAa;IACb,eAAe;IACf,kBAAkB;IAClB,kBAAkB;IAClB,oBAAoB;IACpB,uBAAuB;IACvB,0BAA0B;IAC1B,+BAA+B;IAC/B,iCAAiC;CAClC,CAAC;AAEF;;;;;GAKG;AACH,SAAgB,sBAAsB,CAAC,OAAe,EAAE,GAAG,KAAe;IACxE,OAAO,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;AACjE,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAC9B,OAAe,EACf,GAAG,KAAe;IAElB,iDAAiD;IACjD,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;QACjC,yDAAyD;QACzD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;QACnE,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;AAC3D,CAAC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,OAAe,EAAE,GAAG,KAAe;IACpE,mDAAmD;IACnD,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;QACjC,2DAA2D;QAC3D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC;QACrE,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED;;;;GAIG;AACH,SAAgB,mBAAmB,CAAC,OAAe;IACjD,8BAA8B;IAC9B,MAAM,sBAAsB,GAAG,IAAA,kBAAS,EAAU,wBAAwB,EAAE,IAAI,CAAC,CAAC;IAElF,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,sCAAsC;IACtC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;IAChE,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAW,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEhF,gDAAgD;IAChD,MAAM,YAAY,GAAG;QACnB,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU;QAC1F,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,aAAa;QAC9E,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY;KACpF,CAAC;IAEF,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEhG,iDAAiD;IACjD,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE,CAAC;QACzC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YAC3D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2CAA2C,QAAQ,KAAK,OAAO,EAAE,CAAC,CAAC;YACzF,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,yEAAyE;IACzE,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9F,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oDAAoD,OAAO,EAAE,CAAC,CAAC;YACrF,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,2EAA2E;IAC3E,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QAC3F,uEAAuE;QACvE,oFAAoF;QACpF,MAAM,mBAAmB,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YACnC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;YACvC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACxC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE1E,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,+DAA+D;YAC/D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kEAAkE,OAAO,EAAE,CAAC,CAAC;YACnG,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,yCAAyC;IACzC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;QACxC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC1C,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YAChD,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;QACtD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6CAA6C,OAAO,EAAE,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gEAAgE;IAChE,MAAM,uBAAuB,GAAG;QAC9B,oBAAoB,EAAE,sBAAsB,EAAE,mBAAmB;QACjE,cAAc,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe;QAC/D,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,eAAe;QAC7D,iBAAiB,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB;KAC9D,CAAC;IAEF,IAAI,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;QACrF,iDAAiD;QACjD,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE,CAAC;YACzC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBAC3D,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sDAAsD,QAAQ,KAAK,OAAO,EAAE,CAAC,CAAC;gBACpG,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED,+DAA+D;IAC/D,MAAM,cAAc,GAAG,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC9D,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CACtD,CAAC;IAEF,IAAI,cAAc,EAAE,CAAC;QACnB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,OAAO,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,wBAAwB,CAC5C,OAAe,EACf,OAAgB,EAChB,UAAoB,EAAE;IAEtB,iDAAiD;IACjD,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;QACjC,yDAAyD;QACzD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2CAA2C,OAAO,EAAE,CAAC,CAAC;QAC5E,IAAI,OAAO,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;IAC3B,IAAI,OAAO,EAAE,CAAC;QACZ,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC7B,CAAC;IAED,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;IAE1E,IAAI,SAAS,KAAK,cAAc,IAAI,OAAO,EAAE,CAAC;QAC5C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACzD,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;QACH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from '../../logger';\nimport { getConfig } from '../../config';\n\n// List of error message patterns to suppress\nconst SUPPRESSED_ERROR_PATTERNS = [\n  // Local provider connection errors\n  'Failed to fetch Code Llama models',\n  'LM Studio connection test failed',\n  'Failed to initialize Codessa memory provider',\n  'Connection refused',\n  'connect ECONNREFUSED',\n  'timeout',\n  'ETIMEDOUT',\n  'ESOCKETTIMEDOUT',\n  'Failed to connect to lmstudio provider',\n  'Failed to connect to ollama provider',\n  'Failed to list models for provider',\n  'Failed to initialize',\n  'ECONNRESET',\n  'socket hang up',\n  'ECONNABORTED',\n  'network error',\n  'network timeout',\n  'Failed to fetch models',\n  'Failed to load models',\n  'Could not connect to',\n  'Unable to connect to',\n  'Connection error',\n  'Connection failed',\n  'No connection',\n  'Not running',\n  'is not running',\n  'is not available',\n  'is not responding',\n  'is offline',\n  'could not be reached',\n  'unreachable',\n  'not reachable',\n  'Failed to import',\n  'Module not found',\n  'Cannot find module',\n  'Failed to load module',\n  'Failed to resolve module',\n  'Failed to open settings panel',\n  'Failed to open AllSettingsPanel'\n];\n\n/**\n * Show an information message to the user\n * @param message The message to show\n * @param items Optional items to include as buttons\n * @returns A promise that resolves to the selected item or undefined\n */\nexport function showInformationMessage(message: string, ...items: string[]): Thenable<string | undefined> {\n  return vscode.window.showInformationMessage(message, ...items);\n}\n\n/**\n * Show an error message to the user\n * @param message The message to show\n * @param items Optional items to include as buttons\n * @param options Additional options for showing the error\n * @returns A promise that resolves to the selected item or undefined\n */\nexport function showErrorMessage(\n  message: string,\n  ...items: string[]\n): Thenable<string | undefined> {\n  // Check if we should suppress this error message\n  if (shouldSuppressError(message)) {\n    // Log the suppressed error but don't show it to the user\n    Logger.instance.debug(`Suppressed error notification: ${message}`);\n    return Promise.resolve(undefined);\n  }\n\n  return vscode.window.showErrorMessage(message, ...items);\n}\n\n/**\n * Show a warning message to the user\n * @param message The message to show\n * @param items Optional items to include as buttons\n * @returns A promise that resolves to the selected item or undefined\n */\nexport function showWarningMessage(message: string, ...items: string[]): Thenable<string | undefined> {\n  // Check if we should suppress this warning message\n  if (shouldSuppressError(message)) {\n    // Log the suppressed warning but don't show it to the user\n    Logger.instance.debug(`Suppressed warning notification: ${message}`);\n    return Promise.resolve(undefined);\n  }\n\n  return vscode.window.showWarningMessage(message, ...items);\n}\n\n/**\n * Determines if an error message should be suppressed based on its content and provider configuration\n * @param message The error message to check\n * @returns True if the message should be suppressed, false otherwise\n */\nexport function shouldSuppressError(message: string): boolean {\n  // Get the suppression setting\n  const suppressProviderErrors = getConfig<boolean>('suppressProviderErrors', true);\n\n  if (!suppressProviderErrors) {\n    return false;\n  }\n\n  // Get enabled providers from settings\n  const config = vscode.workspace.getConfiguration('codessa.llm');\n  const enabledProviders = config.get<string[]>('enabledProviders') || ['ollama'];\n\n  // Always suppress errors for disabled providers\n  const allProviders = [\n    'ollama', 'lmstudio', 'openai', 'anthropic', 'googleai', 'mistralai', 'cohere', 'deepseek',\n    'openrouter', 'huggingface', 'starcoder', 'codellama', 'replit', 'wizardcoder',\n    'xwincoder', 'phi', 'yicode', 'codegemma', 'santacoder', 'stablecode', 'perplexity'\n  ];\n\n  const disabledProviders = allProviders.filter(provider => !enabledProviders.includes(provider));\n\n  // Check if the error is from a disabled provider\n  for (const provider of disabledProviders) {\n    if (message.toLowerCase().includes(provider.toLowerCase())) {\n      Logger.instance.debug(`Suppressing error for disabled provider ${provider}: ${message}`);\n      return true;\n    }\n  }\n\n  // Specifically suppress all LM Studio errors if LM Studio is not enabled\n  if (message.toLowerCase().includes('lm studio') || message.toLowerCase().includes('lmstudio')) {\n    if (!enabledProviders.includes('lmstudio')) {\n      Logger.instance.debug(`Suppressing LM Studio error (provider disabled): ${message}`);\n      return true;\n    }\n  }\n\n  // Always suppress errors for local providers during startup/initialization\n  if (message.toLowerCase().includes('ollama') || message.toLowerCase().includes('lmstudio')) {\n    // Check if this is an explicit user action or automatic initialization\n    // If the message contains \"test\" or \"check\", it's likely from a user-initiated test\n    const isUserInitiatedTest = message.toLowerCase().includes('test') ||\n                                   message.toLowerCase().includes('check') ||\n                                   message.toLowerCase().includes('manual') ||\n                                   message.toLowerCase().includes('explicit');\n\n    if (!isUserInitiatedTest) {\n      // Suppress automatic initialization errors for local providers\n      Logger.instance.debug(`Suppressing automatic initialization error for local provider: ${message}`);\n      return true;\n    }\n  }\n\n  // Suppress errors for disabled providers\n  if (message.toLowerCase().includes('provider') &&\n        (message.toLowerCase().includes('disabled') ||\n         message.toLowerCase().includes('not configured') ||\n         message.toLowerCase().includes('not enabled'))) {\n    Logger.instance.debug(`Suppressing provider configuration error: ${message}`);\n    return true;\n  }\n\n  // Suppress connection errors for providers that are not enabled\n  const connectionErrorPatterns = [\n    'connection refused', 'connection timed out', 'failed to connect',\n    'econnrefused', 'etimedout', 'esockettimedout', 'network error',\n    'connection error', 'timeout', 'unreachable', 'is it running',\n    'please check if', 'make sure', 'server at', 'localhost:1234'\n  ];\n\n  if (connectionErrorPatterns.some(pattern => message.toLowerCase().includes(pattern))) {\n    // Check if this error is for a disabled provider\n    for (const provider of disabledProviders) {\n      if (message.toLowerCase().includes(provider.toLowerCase())) {\n        Logger.instance.debug(`Suppressing connection error for disabled provider ${provider}: ${message}`);\n        return true;\n      }\n    }\n  }\n\n  // Check if the message contains any of the suppressed patterns\n  const shouldSuppress = SUPPRESSED_ERROR_PATTERNS.some(pattern =>\n    message.toLowerCase().includes(pattern.toLowerCase())\n  );\n\n  if (shouldSuppress) {\n    Logger.instance.debug(`Suppressing error matching pattern: ${message}`);\n  }\n\n  return shouldSuppress;\n}\n\n/**\n * Show a detailed error message with an option to view more details\n * @param message The main error message\n * @param details The detailed error information\n * @param buttons Additional buttons to show\n * @returns A promise that resolves to the selected item or undefined\n */\nexport async function showDetailedErrorMessage(\n  message: string,\n  details?: string,\n  buttons: string[] = []\n): Promise<string | undefined> {\n  // Check if we should suppress this error message\n  if (shouldSuppressError(message)) {\n    // Log the suppressed error but don't show it to the user\n    Logger.instance.debug(`Suppressed detailed error notification: ${message}`);\n    if (details) {\n      Logger.instance.debug(`Suppressed error details: ${details}`);\n    }\n    return Promise.resolve(undefined);\n  }\n\n  const items = [...buttons];\n  if (details) {\n    items.push('Show Details');\n  }\n\n  const selection = await vscode.window.showErrorMessage(message, ...items);\n\n  if (selection === 'Show Details' && details) {\n    const detailsDoc = await vscode.workspace.openTextDocument({\n      content: details,\n      language: 'text'\n    });\n    await vscode.window.showTextDocument(detailsDoc);\n  }\n\n  return selection;\n}\n"]}