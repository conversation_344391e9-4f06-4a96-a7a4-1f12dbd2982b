#!/usr/bin/env ts-node
/**
 * <PERSON><PERSON><PERSON> to run the workflow engine benchmark
 * 
 * Usage: 
 *   npm run benchmark
 *   
 * or directly with ts-node:
 *   npx ts-node scripts/runBenchmark.ts
 */

import * as path from 'path';
import { execSync } from 'child_process';
import { performance } from 'perf_hooks';
import { Logger } from '../src/logger';

// Configure logger
Logger.configure({
  level: 'info',
  transports: ['console'],
});

const logger = Logger.instance;

// Path to the benchmark script
const benchmarkScript = path.resolve(__dirname, '../benchmark/workflowBenchmark.ts');

// Function to run a command and log output
function runCommand(command: string, cwd: string = process.cwd()): string {
  try {
    return execSync(command, { cwd, stdio: 'pipe' }).toString();
  } catch (error: any) {
    logger.error(`Command failed: ${command}`, error.message);
    if (error.stderr) {
      logger.error(error.stderr.toString());
    }
    process.exit(1);
  }
}

// Function to run the benchmark
async function runBenchmark() {
  const startTime = performance.now();
  
  logger.info('🚀 Starting workflow engine benchmark suite\n');
  
  try {
    // Build the project first
    logger.info('🔨 Building project...');
    runCommand('npm run build');
    
    // Run the benchmark
    logger.info('\n🏃 Running benchmark...');
    const benchmarkOutput = runCommand(`npx ts-node --project benchmark/tsconfig.json ${benchmarkScript}`);
    
    // Log the benchmark output
    console.log('\n' + benchmarkOutput);
    
    // Generate a report
    const endTime = performance.now();
    const totalDuration = (endTime - startTime) / 1000; // in seconds
    
    logger.info(`\n✅ Benchmark completed in ${totalDuration.toFixed(2)} seconds`);
    logger.info('📊 Check the output above for detailed performance metrics');
    
  } catch (error) {
    logger.error('❌ Benchmark failed:', error);
    process.exit(1);
  }
}

// Run the benchmark
runBenchmark().catch(error => {
  logger.error('Unhandled error in benchmark:', error);
  process.exit(1);
});
