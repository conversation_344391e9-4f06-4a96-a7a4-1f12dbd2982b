import { LLMConfig } from '../llm/types';
import * as vscode from 'vscode';

export function getDefaultModelConfig(): LLMConfig {
  // Get selected provider and model from chat view settings
  const config = vscode.workspace.getConfiguration('codessa');
  const selectedProvider = config.get<string>('selectedProvider', 'ollama');
  const selectedModel = config.get<string>('selectedModel', 'llama3');

  return {
    provider: selectedProvider,
    modelId: selectedModel,
    temperature: 0.7,
    maxTokens: 2000
  };
}

export function getMaxToolIterations(): number {
  return 10;
} 