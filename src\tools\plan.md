# Tool Refactoring Plan

## Objective

Refactor all tool implementations (`*Tool.ts` files) to fully utilize the functionality provided by `toolFramework.ts` instead of using `tool.ts` or `tool.ts.backup`.

## Requirements

1. Each tool must leverage all available functionality from `toolFramework.ts`:
   - Advanced terminal functions via `InteractiveSession` and `AITerminalTool`
   - Memory operations through `IMemoryOperations` and `IToolMemorySchema`
   - Workflow capabilities using `Workflow` and `WorkflowContext`
   - File system operations via `IWorkspaceKnowledge` and `IFileSystem`
   - Logging and error handling through `ILogger`
   - Multimodal data processing with `MultimodalData` and `LanguageModelPrompt`
   - Directory and file operations with `DirectoryTree` and `FileEntry`

2. Guidelines:
   - No reimplementation of functionality that exists in `toolFramework.ts`
   - Maintain and upgrade tool capabilities without regression
   - Fix all errors and lint warnings before moving to the next tool
   - Only refactor files ending with `Tool.ts`
   - Preserve existing agent and workflow implementations
   - Use proper TypeScript types and interfaces
   - Implement proper error handling and logging
   - Follow the framework's patterns for memory management
   - Use the workflow system for complex operations
   - Leverage the terminal integration for command execution

## Refactoring Process

For each tool, follow these steps:

1. **Analysis Phase**
   - Review current implementation and identify functionality to be replaced
   - Map existing features to `toolFramework.ts` equivalents
   - Identify any missing functionality that needs to be implemented

2. **Implementation Phase**
   - Update imports to use `toolFramework.ts`
   - Refactor class to extend `AITerminalTool` where applicable
   - Replace direct file operations with `IWorkspaceKnowledge` and `IFileSystem`
   - Integrate memory operations using `IMemoryOperations`
   - Add workflow support where appropriate
   - Implement proper error handling and logging

3. **Testing Phase**
   - Verify all existing functionality works as expected
   - Test edge cases and error conditions
   - Run linter and fix all warnings
   - Ensure type safety throughout the implementation

4. **Documentation Phase**
   - Update JSDoc comments
   - Document any new features or changes
   - Update any relevant README files

## Tools to Refactor

**Total: 34 tools**

### Advanced Tools
- [ ] advancedCodeEditingTool.ts
- [ ] advancedCodeSearchTool.ts
- [ ] advancedCodeGenerationTool.ts
- [ ] advancedDiffTool.ts
- [ ] advancedDocsTool.ts
- [ ] advancedEditorActionsTool.ts
- [ ] advancedGitTool.ts
- [ ] advancedLintDiagnosticsTool.ts
- [ ] advancedMemoryTool.ts
- [ ] browserPreviewTool.ts

### Code Related Tools
- [ ] cascadeEditingTool.ts
- [ ] codeAnalysisTool.ts
- [ ] codeGenerationTool.ts
- [ ] codeIntelligenceTool.ts
- [ ] codeRefactoringTool.ts
- [ ] codeSearchTool.ts
- [ ] dependencyAnalysisTool.ts
- [ ] deployWebAppTool.ts

### Editor and Document Tools
- [ ] diagnosticsTool.ts
- [ ] directoryListTool.ts
- [ ] docsTool.ts
- [ ] documentRetrievalTool.ts
- [ ] editorActionsTool.ts
- [ ] gitTool.ts
- [ ] largeFileEditTool.ts
- [ ] lintDiagnosticsTool.ts
- [ ] memoryTool.ts
- [ ] neuralCodeSynthesisTool.ts

### System and Analysis Tools
- [ ] quantumAnalysisTool.ts
- [ ] searchTool.ts
- [ ] terminalCommandTool.ts
- [ ] timeTravelDebuggingTool.ts
- [ ] webReadTool.ts
- [ ] webSearchTool.ts

## Progress Tracking

- **Total tools to refactor**: 34
- **Tools completed**: 0
- **In progress**: 0
- **Remaining**: 34

## Implementation Notes

### Common Patterns to Implement

1. **Base Class Structure**
   ```typescript
   import { AITerminalTool, ILogger, IMemoryOperations, IWorkspaceKnowledge } from './toolFramework';
   
   export class MyTool extends AITerminalTool {
     constructor(
       private readonly logger: ILogger,
       private readonly memory: IMemoryOperations,
       private readonly workspace: IWorkspaceKnowledge
     ) {
       super();
     }
   }
   ```

2. **Memory Operations**
   ```typescript
   // Store operation in memory
   await this.memory.store({
     id: `operation-${Date.now()}`,
     toolName: 'MyTool',
     operation: 'processFile',
     timestamp: Date.now(),
     input: { filePath, options },
     metadata: {
       source: 'tool-execution',
       type: 'operation',
       success: true
     }
   });
   ```

3. **File Operations**
   ```typescript
   // Read file content
   const content = await this.workspace.getFileContent(relativePath);
   
   // Write file content
   await this.workspace.writeFile(relativePath, newContent);
   ```

4. **Terminal Integration**
   ```typescript
   // Execute command with AI-enhanced error handling
   const result = await this.executeWithAI('npm install', {
     timeout: 60000,
     workingDirectory: this.workspace.rootPath
   });
   ```

## Quality Assurance

- [ ] All tools must pass TypeScript type checking
- [ ] No lint warnings or errors
- [ ] All existing tests pass
- [ ] New tests added for new functionality
- [ ] Documentation updated
- [ ] Code follows project's coding standards

## Next Steps

1. Begin with `advancedCodeEditingTool.ts` as the first tool to refactor
2. Follow the refactoring process for each tool in the Advanced Tools section
3. Move to the Code Related Tools section after completing all Advanced Tools
4. Continue through each section until all tools are refactored
