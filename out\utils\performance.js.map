{"version": 3, "file": "performance.js", "sourceRoot": "", "sources": ["../../src/utils/performance.ts"], "names": [], "mappings": ";;;AAqFA,gDAeC;AAGD,oCAkCC;AAzID,2CAA8D;AAS9D,MAAM,kBAAkB;IACd,MAAM,CAAC,QAAQ,CAAqB;IACpC,OAAO,GAAwB,EAAE,CAAC;IAClC,OAAO,GAAY,IAAI,CAAC;IACxB,QAAQ,GAA+B,IAAI,CAAC;IAEpD;QACE,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACzD,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACrC,CAAC;IAEO,wBAAwB;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,gCAAmB,CAAC,CAAC,KAAK,EAAE,EAAE;YAChD,KAAK,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;oBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,SAAS,EAAE,wBAAW,CAAC,GAAG,EAAE;iBAC7B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IAEM,OAAO,CAAI,IAAY,EAAE,EAAW;QACzC,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,EAAE,CAAC;QAE/B,MAAM,SAAS,GAAG,GAAG,IAAI,QAAQ,CAAC;QAClC,MAAM,OAAO,GAAG,GAAG,IAAI,MAAM,CAAC;QAE9B,wBAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5B,MAAM,MAAM,GAAG,EAAE,EAAE,CAAC;QAEpB,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;YAC9B,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;gBACzB,wBAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC1B,wBAAW,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;gBAC9C,wBAAW,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBAClC,wBAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC,CAAiB,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,wBAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1B,wBAAW,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YAC9C,wBAAW,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAClC,wBAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAChC,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAEM,UAAU;QACf,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAEM,YAAY;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,CAAC;IAEM,MAAM;QACX,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;CACF;AAEY,QAAA,kBAAkB,GAAG,kBAAkB,CAAC,WAAW,EAAE,CAAC;AAEnE,gDAAgD;AAChD,SAAgB,kBAAkB,CAChC,MAAW,EACX,WAAmB,EACnB,UAA8B;IAE9B,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;IAExC,UAAU,CAAC,KAAK,GAAG,UAAS,GAAG,IAAW;QACxC,OAAO,0BAAkB,CAAC,OAAO,CAC/B,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,WAAW,EAAE,EAC3C,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CACvC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,+CAA+C;AACxC,KAAK,UAAU,YAAY,CAChC,IAAY,EACZ,SAA2B,EAC3B,QAA8B;IAE9B,MAAM,KAAK,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;IAEhC,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,wBAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;QAE3C,0BAAkB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;YACjC,IAAI;YACJ,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,QAAQ;SACT,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,QAAQ,GAAG,wBAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;QAE3C,0BAAkB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;YACjC,IAAI,EAAE,GAAG,IAAI,QAAQ;YACrB,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,QAAQ,EAAE;gBACR,GAAG,QAAQ;gBACX,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D;SACF,CAAC,CAAC;QAEH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC", "sourcesContent": ["import { performance, PerformanceObserver } from 'perf_hooks';\n\ninterface PerformanceMetric {\n  name: string;\n  duration: number;\n  timestamp: number;\n  metadata?: Record<string, any>;\n}\n\nclass PerformanceMonitor {\n  private static instance: PerformanceMonitor;\n  private metrics: PerformanceMetric[] = [];\n  private enabled: boolean = true;\n  private observer: PerformanceObserver | null = null;\n\n  private constructor() {\n    this.setupPerformanceObserver();\n  }\n\n  public static getInstance(): PerformanceMonitor {\n    if (!PerformanceMonitor.instance) {\n      PerformanceMonitor.instance = new PerformanceMonitor();\n    }\n    return PerformanceMonitor.instance;\n  }\n\n  private setupPerformanceObserver() {\n    this.observer = new PerformanceObserver((items) => {\n      items.getEntries().forEach((entry) => {\n        this.metrics.push({\n          name: entry.name,\n          duration: entry.duration,\n          timestamp: performance.now(),\n        });\n      });\n    });\n    \n    this.observer.observe({ entryTypes: ['measure'] });\n  }\n\n  public measure<T>(name: string, fn: () => T): T {\n    if (!this.enabled) return fn();\n\n    const startMark = `${name}-start`;\n    const endMark = `${name}-end`;\n    \n    performance.mark(startMark);\n    const result = fn();\n    \n    if (result instanceof Promise) {\n      return result.finally(() => {\n        performance.mark(endMark);\n        performance.measure(name, startMark, endMark);\n        performance.clearMarks(startMark);\n        performance.clearMarks(endMark);\n      }) as unknown as T;\n    } else {\n      performance.mark(endMark);\n      performance.measure(name, startMark, endMark);\n      performance.clearMarks(startMark);\n      performance.clearMarks(endMark);\n      return result;\n    }\n  }\n\n  public getMetrics(): PerformanceMetric[] {\n    return [...this.metrics];\n  }\n\n  public clearMetrics(): void {\n    this.metrics = [];\n  }\n\n  public enable(): void {\n    this.enabled = true;\n  }\n\n  public disable(): void {\n    this.enabled = false;\n  }\n}\n\nexport const performanceMonitor = PerformanceMonitor.getInstance();\n\n// Decorator for measuring method execution time\nexport function measurePerformance(\n  target: any,\n  propertyKey: string,\n  descriptor: PropertyDescriptor\n) {\n  const originalMethod = descriptor.value;\n  \n  descriptor.value = function(...args: any[]) {\n    return performanceMonitor.measure(\n      `${target.constructor.name}.${propertyKey}`,\n      () => originalMethod.apply(this, args)\n    );\n  };\n  \n  return descriptor;\n}\n\n// Utility function to measure async operations\nexport async function measureAsync<T>(\n  name: string,\n  operation: () => Promise<T>,\n  metadata?: Record<string, any>\n): Promise<T> {\n  const start = performance.now();\n  \n  try {\n    const result = await operation();\n    const duration = performance.now() - start;\n    \n    performanceMonitor['metrics'].push({\n      name,\n      duration,\n      timestamp: Date.now(),\n      metadata\n    });\n    \n    return result;\n  } catch (error) {\n    const duration = performance.now() - start;\n    \n    performanceMonitor['metrics'].push({\n      name: `${name}-error`,\n      duration,\n      timestamp: Date.now(),\n      metadata: {\n        ...metadata,\n        error: error instanceof Error ? error.message : String(error)\n      }\n    });\n    \n    throw error;\n  }\n}\n"]}