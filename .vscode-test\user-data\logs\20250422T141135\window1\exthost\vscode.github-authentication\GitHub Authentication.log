2025-04-22 14:11:39.423 [info] Reading sessions from keychain...
2025-04-22 14:11:39.423 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.423 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.423 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.423 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.423 [info] Got 0 sessions for ...
2025-04-22 14:11:39.423 [info] Got 0 sessions for ...
2025-04-22 14:11:39.423 [info] Got 0 sessions for ...
2025-04-22 14:11:39.423 [info] Got 0 sessions for ...
2025-04-22 14:11:39.423 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.672 [info] Got 0 sessions for ...
2025-04-22 14:11:39.672 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.672 [info] Got 0 sessions for ...
2025-04-22 14:11:39.672 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.672 [info] Got 0 sessions for ...
2025-04-22 14:11:39.672 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.672 [info] Got 0 sessions for ...
2025-04-22 14:11:39.672 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.672 [info] Got 0 sessions for ...
2025-04-22 14:11:39.839 [info] Getting sessions for all scopes...
2025-04-22 14:11:39.840 [info] Got 0 sessions for ...
2025-04-22 14:11:43.435 [info] Getting sessions for read:user,repo,user:email,workflow...
2025-04-22 14:11:43.435 [info] Got 0 sessions for read:user,repo,user:email,workflow...
2025-04-22 20:02:34.654 [info] Logging in with 'any' account...
2025-04-22 20:02:34.654 [info] Logging in for the following scopes: user:email
2025-04-22 20:02:34.692 [info] Trying without local server... (user:email)
2025-04-22 20:07:34.703 [error] Timed out
2025-04-22 22:38:12.219 [error] Canceled
2025-04-22 22:38:12.224 [error] Canceled
2025-04-22 22:38:12.227 [error] Error: No auth flow succeeded.
	at t.GitHubServer.login (c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\github-authentication\dist\extension.js:2:212185)
	at async t.GitHubAuthenticationProvider.createSession (c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\github-authentication\dist\extension.js:2:207189)
	at async Sw.$createSession (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:113:3533)
