{"version": 3, "file": "dependencyAnalysisTool.js", "sourceRoot": "", "sources": ["../../src/tools/dependencyAnalysisTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,kDAAoC;AACpC,6BAAwB;AACxB,2CAA6B;AAC7B,uCAAyB;AACzB,2CAA6B;AAE7B;;GAEG;AACH,MAAa,sBAAsB;IACxB,EAAE,GAAG,oBAAoB,CAAC;IAC1B,IAAI,GAAG,qBAAqB,CAAC;IAC7B,WAAW,GAAG,6DAA6D,CAAC;IAC5E,IAAI,GAAG,cAAc,CAAC;IACtB,OAAO,GAAG;QACjB,SAAS,EAAE;YACT,WAAW,EAAE,2CAA2C;YACxD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2CAA2C,CAAC;gBACxE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sDAAsD,CAAC;gBAC7F,kBAAkB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oEAAoE,CAAC;gBACzH,mBAAmB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4EAA4E,CAAC;aACnI,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2CAA2C,CAAC;gBACxE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sDAAsD,CAAC;gBAC7F,kBAAkB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oEAAoE,CAAC;gBACzH,mBAAmB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,4EAA4E,CAAC;aACnI,CAAC;SACH;QACD,cAAc,EAAE;YACd,WAAW,EAAE,yCAAyC;YACtD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;gBAChE,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mEAAmE,CAAC;aAChH,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;gBAChE,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mEAAmE,CAAC;aAChH,CAAC;SACH;QACD,WAAW,EAAE;YACX,WAAW,EAAE,0CAA0C;YACvD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;gBAC1E,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iCAAiC,CAAC;gBAC9F,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,2DAA2D,CAAC;aACnG,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;gBAC1E,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iCAAiC,CAAC;gBAC9F,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,2DAA2D,CAAC;aACnG,CAAC;SACH;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,uCAAuC;YACpD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;gBACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC7D,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kDAAkD,CAAC;aAC1F,CAAC;YACF,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;gBACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;gBAC7D,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kDAAkD,CAAC;aAC1F,CAAC;SACH;KACF,CAAC;IAEe,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAEvD,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,IAAI,CAAC;YACH,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAuC,CAAC,EAAE,CAAC;gBAC1E,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,UAAU,IAAI,WAAW,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACjH,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACrD,KAAK,cAAc;oBACjB,OAAO,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAC1D,KAAK,WAAW;oBACd,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACvD,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAC/C;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,UAAU,EAAE;wBACtC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC9D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,KAAgB,EAAE,UAAkB;QACnE,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;QACtC,MAAM,KAAK,GAAI,KAAK,CAAC,KAAgB,IAAI,CAAC,CAAC;QAC3C,MAAM,kBAAkB,GAAI,KAAK,CAAC,kBAA8B,IAAI,KAAK,CAAC;QAC1E,MAAM,mBAAmB,GAAI,KAAK,CAAC,mBAA+B,KAAK,KAAK,CAAC,CAAC,kBAAkB;QAEhG,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB;gBAChC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,0EAA0E;YAC1E,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpC,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,qBAAqB,MAAM,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,6CAA6C;YAC7C,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAE7D,uBAAuB;YACvB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;YAEzH,uBAAuB;YACvB,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAE3D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,YAAY;oBACZ,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,MAAM,CAAC;iBAC9D;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,MAAM;oBACN,WAAW;oBACX,KAAK;oBACL,kBAAkB;oBAClB,mBAAmB;oBACnB,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM;iBAClD;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC9D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAgB,EAAE,UAAkB;QACzE,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;QACtC,MAAM,UAAU,GAAI,KAAK,CAAC,UAAqB,IAAI,EAAE,CAAC;QAEtD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB;gBAChC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,0EAA0E;YAC1E,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpC,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,qBAAqB,MAAM,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC7C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,+BAA+B,MAAM,EAAE;oBAC9C,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,2BAA2B;YAC3B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAEnF,6BAA6B;YAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAEjE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,oBAAoB,EAAE,YAAY;oBAClC,KAAK,EAAE,YAAY,CAAC,MAAM;oBAC1B,OAAO,EAAE,IAAI,CAAC,iCAAiC,CAAC,YAAY,CAAC;iBAC9D;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,MAAM;oBACN,UAAU;oBACV,aAAa,EAAE,YAAY,CAAC,MAAM;iBACnC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yCAAyC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACxE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAAgB,EAAE,UAAkB;QACtE,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;QACtC,MAAM,MAAM,GAAI,KAAK,CAAC,MAAiB,IAAI,MAAM,CAAC;QAClD,MAAM,KAAK,GAAI,KAAK,CAAC,KAAgB,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB;gBAChC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,0EAA0E;YAC1E,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpC,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,qBAAqB,MAAM,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,6CAA6C;YAC7C,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAE7D,uBAAuB;YACvB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAE7F,yCAAyC;YACzC,IAAI,aAAa,CAAC;YAClB,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,MAAM;oBACT,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;oBACrE,MAAM;gBACR,KAAK,MAAM;oBACT,aAAa,GAAG,YAAY,CAAC;oBAC7B,MAAM;gBACR,KAAK,KAAK;oBACR,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;oBAC5D,MAAM;gBACR;oBACE,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,KAAK;iBACN;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,MAAM;oBACN,MAAM;oBACN,KAAK;oBACL,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM;iBAClD;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,oCAAoC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBACnE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAgB,EAAE,UAAkB;QAC9D,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,KAAK,GAAI,KAAK,CAAC,KAAgB,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B;gBAClC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,0EAA0E;YAC1E,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpC,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB,QAAQ,EAAE;oBACpC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,qBAAqB;YACrB,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YACjE,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,+BAA+B,QAAQ,EAAE;oBAChD,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,4CAA4C;YAC5C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAE3G,mEAAmE;YACnE,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAE/E,2BAA2B;YAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;YAElF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,aAAa;oBACb,OAAO,EAAE,aAAa;oBACtB,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,aAAa,CAAC;iBAClE;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,KAAK;oBACL,aAAa,EAAE,aAAa,CAAC,MAAM;iBACpC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE;gBAC1D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,WAAoB,EAAE,KAAa,EAAE,kBAA2B,EAAE,mBAA4B;QAC9I,MAAM,YAAY,GAA6B,EAAE,CAAC;QAElD,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACf,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,iDAAiD;YACjD,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;QACpG,CAAC;aAAM,CAAC;YACN,wBAAwB;YACxB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAAC,OAAe,EAAE,YAAsC,EAAE,kBAA2B,EAAE,mBAA4B,EAAE,KAAa;QAC9J,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAElF,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,EAAE,CAAC;gBACnC,oCAAoC;gBACpC,IAAI,CAAC,kBAAkB,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;oBACnD,SAAS;gBACX,CAAC;gBAED,oCAAoC;gBACpC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACzB,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAE1C,IAAI,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACvC,qCAAqC;oBACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;gBACtG,CAAC;qBAAM,IAAI,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACzC,0CAA0C;oBAC1C,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC7C,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;wBAC7G,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBACtE,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,YAAsC,EAAE,mBAA4B;QAC9G,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAEjD,kDAAkD;YAClD,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YAE5B,qCAAqC;YACrC,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,KAAK,CAAC;gBACX,KAAK,MAAM,CAAC;gBACZ,KAAK,KAAK,CAAC;gBACX,KAAK,MAAM;oBACT,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBACtF,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBAClF,MAAM;gBACR,KAAK,OAAO;oBACV,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBAChF,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBAClF,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBAC/E,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBAChF,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBAC9E,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBAChF,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;oBAC/E,MAAM;YACV,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;SAEK;IACG,6BAA6B,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QAC3I,oBAAoB;QACpB,MAAM,cAAc,GAAG,0EAA0E,CAAC;QAClG,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACvD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;QAED,0BAA0B;QAC1B,MAAM,YAAY,GAAG,mFAAmF,CAAC;QAEzG,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACrD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;QAED,wBAAwB;QACxB,MAAM,kBAAkB,GAAG,sCAAsC,CAAC;QAElE,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC3D,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACG,yBAAyB,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QACvI,0BAA0B;QAC1B,MAAM,WAAW,GAAG,qCAAqC,CAAC;QAC1D,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACvD,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE,CAAC;gBACjC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,MAAM,eAAe,GAAG,4DAA4D,CAAC;QAErF,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACxD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QACrI,0BAA0B;QAC1B,MAAM,WAAW,GAAG,yBAAyB,CAAC;QAC9C,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACG,yBAAyB,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QACvI,yBAAyB;QACzB,MAAM,UAAU,GAAG,wBAAwB,CAAC;QAC5C,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QACpI,mCAAmC;QACnC,MAAM,YAAY,GAAG,6EAA6E,CAAC;QACnG,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACrD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;QAED,uBAAuB;QACvB,MAAM,QAAQ,GAAG,sBAAsB,CAAC;QAExC,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QACrI,2BAA2B;QAC3B,MAAM,YAAY,GAAG,kCAAkC,CAAC;QACxD,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACrD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;QAED,oCAAoC;QACpC,MAAM,oBAAoB,GAAG,2CAA2C,CAAC;QAEzE,OAAO,CAAC,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC7D,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACG,qBAAqB,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QACnI,0BAA0B;QAC1B,MAAM,WAAW,GAAG,yCAAyC,CAAC;QAC9D,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAE5C,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;gBAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACjE,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,MAAM,iBAAiB,GAAG,uCAAuC,CAAC;QAElE,OAAO,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC1D,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACG,uBAAuB,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QACrI,uBAAuB;QACvB,MAAM,QAAQ,GAAG,sBAAsB,CAAC;QACxC,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;QAED,gCAAgC;QAChC,MAAM,gBAAgB,GAAG,iCAAiC,CAAC;QAE3D,OAAO,CAAC,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACzD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,QAAgB,EAAE,OAAe,EAAE,YAAsC,EAAE,mBAA4B;QACpI,uBAAuB;QACvB,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAEvE,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;SAEK;IACG,aAAa,CAAC,QAAgB,EAAE,UAAkB,EAAE,YAAsC,EAAE,mBAA4B;QAC9H,6CAA6C;QAC7C,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE,CAAC;YAClE,OAAO;QACT,CAAC;QAED,yBAAyB;QACzB,IAAI,YAAY,GAAG,UAAU,CAAC;QAE9B,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACnC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YAE7C,wCAAwC;YACxC,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAE9G,wDAAwD;YACxD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;oBAC7B,MAAM,WAAW,GAAG,GAAG,YAAY,GAAG,GAAG,EAAE,CAAC;oBAC5C,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC/B,YAAY,GAAG,WAAW,CAAC;wBAC3B,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACnD,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;SAEK;IACG,oBAAoB,CAAC,UAAkB;QAC7C,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACpE,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,YAAsC;QACrE,MAAM,KAAK,GAAG;YACZ,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM;YAC5C,iBAAiB,EAAE,CAAC;YACpB,0BAA0B,EAAE,CAAC;YAC7B,eAAe,EAAE,CAAC;YAClB,mBAAmB,EAAE,EAAE;YACvB,uBAAuB,EAAE,CAAC;YAC1B,yBAAyB,EAAE,EAAuC;SACnE,CAAC;QAEF,4CAA4C;QAC5C,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC;YAEvC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAClC,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC;gBACpC,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC;YACnC,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACzB,KAAK,CAAC,0BAA0B,GAAG,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAC;QAChF,CAAC;QAED,0CAA0C;QAC1C,KAAK,CAAC,yBAAyB,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;aAC3D,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;aACrD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEf,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;SAEK;IACG,yBAAyB,CAAC,YAAsC,EAAE,KAAU;QAClF,OAAO;;;eAGI,KAAK,CAAC,UAAU;sBACT,KAAK,CAAC,iBAAiB;iCACZ,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;8BAC9C,KAAK,CAAC,uBAAuB;+BAC5B,KAAK,CAAC,mBAAmB,KAAK,KAAK,CAAC,eAAe;;;EAGhF,KAAK,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;CAC5G,CAAC;IACA,CAAC;IAED;;SAEK;IACG,YAAY,CAAC,YAAsC,EAAE,UAAkB;QAC7E,MAAM,YAAY,GAAU,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAClC,MAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAC;QAEhC,MAAM,GAAG,GAAG,CAAC,IAAY,EAAE,OAAiB,EAAE,EAAQ,EAAE;YACtD,IAAI,YAAY,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;gBACtC,OAAO;YACT,CAAC;YAED,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpB,gBAAgB;gBAChB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;gBAEhD,YAAY,CAAC,IAAI,CAAC;oBAChB,KAAK;oBACL,MAAM,EAAE,KAAK,CAAC,MAAM;iBACrB,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAClB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhB,KAAK,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YACtB,CAAC;YAED,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC,CAAC;QAEF,2BAA2B;QAC3B,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7C,IAAI,YAAY,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;gBACtC,MAAM;YACR,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,GAAG,CAAC,IAAI,CAAC,CAAC;YACZ,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;SAEK;IACG,iCAAiC,CAAC,YAAmB;QAC3D,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,iCAAiC,CAAC;QAC3C,CAAC;QAED,OAAO;;;QAGH,YAAY,CAAC,MAAM;;EAEzB,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;uBACb,KAAK,GAAG,CAAC;YACpB,IAAI,CAAC,MAAM;UACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;CAChC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;CACZ,CAAC;IACA,CAAC;IAED;;SAEK;IACG,yBAAyB,CAAC,YAAsC,EAAE,QAAgB;QACxF,MAAM,KAAK,GAAa,CAAC,uBAAuB,QAAQ,GAAG,CAAC,CAAC;QAE7D,MAAM,iBAAiB,GAAG,CAAC,IAAY,EAAE,IAAc,EAAE,MAAM,GAAG,EAAE,EAAE,UAAuB,IAAI,GAAG,EAAE,EAAQ,EAAE;YAC9G,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,uBAAuB,CAAC,CAAC;gBACpD,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAElB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,oBAAoB,CAAC,CAAC;gBACjD,OAAO;YACT,CAAC;YAED,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC,CAAC;YAE/B,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;oBACtB,iBAAiB,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC7E,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,EAAE,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,uDAAuD;QACvD,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3B,iBAAiB,CAAC,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD,CAAC;aAAM,CAAC;YACN,8CAA8C;YAC9C,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;gBACxD,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,YAAsC;QACrE,MAAM,KAAK,GAAa,CAAC,wBAAwB,EAAE,eAAe,EAAE,wDAAwD,CAAC,CAAC;QAE9H,YAAY;QACZ,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,MAAM,QAAQ,aAAa,KAAK,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,YAAY;QACZ,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAE7C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,qCAAqC;oBAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;oBAC5C,KAAK,CAAC,IAAI,CAAC,MAAM,UAAU,SAAS,UAAU,IAAI,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEhB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;SAEK;IACG,cAAc,CAAC,GAAW;QAChC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;SAEK;IACG,iBAAiB,CAAC,YAAsC,EAAE,UAAkB,EAAE,KAAa;QACjG,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAElC,MAAM,cAAc,GAAG,CAAC,IAAY,EAAE,YAAY,GAAG,CAAC,EAAQ,EAAE;YAC9D,IAAI,YAAY,GAAG,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9C,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAElB,sCAAsC;YACtC,KAAK,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC3D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBACvD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvB,cAAc,CAAC,OAAO,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,cAAc,CAAC,UAAU,CAAC,CAAC;QAE3B,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACG,sBAAsB,CAAC,aAAuB,EAAE,eAAyC;QAC/F,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC;QACvD,MAAM,gBAAgB,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAExF,4BAA4B;QAC5B,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,gBAAgB,GAAG,EAAE,EAAE,CAAC;YAC1B,cAAc,GAAG,MAAM,CAAC;QAC1B,CAAC;aAAM,IAAI,gBAAgB,GAAG,EAAE,EAAE,CAAC;YACjC,cAAc,GAAG,QAAQ,CAAC;QAC5B,CAAC;QAED,OAAO;YACL,aAAa,EAAE,aAAa,CAAC,MAAM;YACnC,UAAU;YACV,gBAAgB;YAChB,cAAc;SACf,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,qBAAqB,CAAC,aAAuB,EAAE,OAAY;QACjE,OAAO;;;oBAGS,OAAO,CAAC,aAAa,WAAW,OAAO,CAAC,UAAU,WAAW,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;mBACjG,OAAO,CAAC,cAAc;;EAEvC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3B,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,6BAA6B;CACpF,CAAC;IACA,CAAC;CACF;AA1gCD,wDA0gCC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport * as cp from 'child_process';\nimport { z } from 'zod';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport * as util from 'util';\n\n/**\n * Analyzes code dependencies and generates dependency graphs\n */\nexport class DependencyAnalysisTool implements ITool {\n  readonly id = 'dependencyAnalysis';\n  readonly name = 'Dependency Analysis';\n  readonly description = 'Analyzes code dependencies and generates dependency graphs.';\n  readonly type = 'multi-action';\n  readonly actions = {\n    'analyze': {\n      description: 'Analyze dependencies in a file or project',\n      schema: z.object({\n        target: z.string().describe('Path to the file or directory to analyze.'),\n        depth: z.number().optional().describe('Maximum depth for dependency analysis. Default is 3.'),\n        includeNodeModules: z.boolean().optional().describe('Whether to include node_modules in the analysis. Default is false.'),\n        includeExternalDeps: z.boolean().optional().describe('Whether to include external dependencies in the analysis. Default is true.')\n      }),\n      inputSchema: z.object({\n        target: z.string().describe('Path to the file or directory to analyze.'),\n        depth: z.number().optional().describe('Maximum depth for dependency analysis. Default is 3.'),\n        includeNodeModules: z.boolean().optional().describe('Whether to include node_modules in the analysis. Default is false.'),\n        includeExternalDeps: z.boolean().optional().describe('Whether to include external dependencies in the analysis. Default is true.')\n      })\n    },\n    'findCircular': {\n      description: 'Find circular dependencies in a project',\n      schema: z.object({\n        target: z.string().describe('Path to the directory to analyze.'),\n        maxResults: z.number().optional().describe('Maximum number of circular dependencies to return. Default is 10.')\n      }),\n      inputSchema: z.object({\n        target: z.string().describe('Path to the directory to analyze.'),\n        maxResults: z.number().optional().describe('Maximum number of circular dependencies to return. Default is 10.')\n      })\n    },\n    'visualize': {\n      description: 'Generate a visualization of dependencies',\n      schema: z.object({\n        target: z.string().describe('Path to the file or directory to visualize.'),\n        format: z.enum(['text', 'json', 'dot']).optional().describe('Output format. Default is text.'),\n        depth: z.number().optional().describe('Maximum depth for dependency visualization. Default is 2.')\n      }),\n      inputSchema: z.object({\n        target: z.string().describe('Path to the file or directory to visualize.'),\n        format: z.enum(['text', 'json', 'dot']).optional().describe('Output format. Default is text.'),\n        depth: z.number().optional().describe('Maximum depth for dependency visualization. Default is 2.')\n      })\n    },\n    'impact': {\n      description: 'Analyze the impact of changing a file',\n      schema: z.object({\n        filePath: z.string().describe('Path to the file to analyze.'),\n        depth: z.number().optional().describe('Maximum depth for impact analysis. Default is 3.')\n      }),\n      inputSchema: z.object({\n        filePath: z.string().describe('Path to the file to analyze.'),\n        depth: z.number().optional().describe('Maximum depth for impact analysis. Default is 3.')\n      })\n    }\n  };\n\n  private readonly execPromise = util.promisify(cp.exec);\n\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    try {\n      if (!actionName || !this.actions[actionName as keyof typeof this.actions]) {\n        return {\n          success: false,\n          error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      switch (actionName) {\n        case 'analyze':\n          return this.analyzeDependencies(input, actionName);\n        case 'findCircular':\n          return this.findCircularDependencies(input, actionName);\n        case 'visualize':\n          return this.visualizeDependencies(input, actionName);\n        case 'impact':\n          return this.analyzeImpact(input, actionName);\n        default:\n          return {\n            success: false,\n            error: `Unknown action: ${actionName}`,\n            toolId: this.id,\n            actionName\n          };\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Dependency analysis failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  public async analyzeDependencies(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const target = input.target as string;\n    const depth = (input.depth as number) || 3;\n    const includeNodeModules = (input.includeNodeModules as boolean) || false;\n    const includeExternalDeps = (input.includeExternalDeps as boolean) !== false; // Default to true\n\n    if (!target) {\n      return {\n        success: false,\n        error: '\\'target\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Validate target exists\n      const uri = vscode.Uri.file(target);\n      // Replace Promise.then().catch() with try/catch for better error handling\n      let exists = false;\n      try {\n        await vscode.workspace.fs.stat(uri);\n        exists = true;\n      } catch {\n        exists = false;\n      }\n\n      if (!exists) {\n        return {\n          success: false,\n          error: `Target not found: ${target}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Determine if target is a file or directory\n      const stats = await vscode.workspace.fs.stat(uri);\n      const isDirectory = stats.type === vscode.FileType.Directory;\n\n      // Analyze dependencies\n      const dependencies = await this.extractDependencies(target, isDirectory, depth, includeNodeModules, includeExternalDeps);\n\n      // Calculate statistics\n      const stats2 = this.calculateDependencyStats(dependencies);\n\n      return {\n        success: true,\n        output: {\n          dependencies,\n          stats: stats2,\n          summary: this.generateDependencySummary(dependencies, stats2)\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          target,\n          isDirectory,\n          depth,\n          includeNodeModules,\n          includeExternalDeps,\n          dependencyCount: Object.keys(dependencies).length\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Dependency analysis failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async findCircularDependencies(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const target = input.target as string;\n    const maxResults = (input.maxResults as number) || 10;\n\n    if (!target) {\n      return {\n        success: false,\n        error: '\\'target\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Validate target exists and is a directory\n      const uri = vscode.Uri.file(target);\n      // Replace Promise.then().catch() with try/catch for better error handling\n      let exists = false;\n      try {\n        await vscode.workspace.fs.stat(uri);\n        exists = true;\n      } catch {\n        exists = false;\n      }\n\n      if (!exists) {\n        return {\n          success: false,\n          error: `Target not found: ${target}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      const stats = await vscode.workspace.fs.stat(uri);\n      if (stats.type !== vscode.FileType.Directory) {\n        return {\n          success: false,\n          error: `Target must be a directory: ${target}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Extract all dependencies\n      const dependencies = await this.extractDependencies(target, true, 10, false, true);\n\n      // Find circular dependencies\n      const circularDeps = this.findCircular(dependencies, maxResults);\n\n      return {\n        success: true,\n        output: {\n          circularDependencies: circularDeps,\n          count: circularDeps.length,\n          summary: this.generateCircularDependencySummary(circularDeps)\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          target,\n          maxResults,\n          circularCount: circularDeps.length\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Finding circular dependencies failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async visualizeDependencies(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const target = input.target as string;\n    const format = (input.format as string) || 'text';\n    const depth = (input.depth as number) || 2;\n\n    if (!target) {\n      return {\n        success: false,\n        error: '\\'target\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Validate target exists\n      const uri = vscode.Uri.file(target);\n      // Replace Promise.then().catch() with try/catch for better error handling\n      let exists = false;\n      try {\n        await vscode.workspace.fs.stat(uri);\n        exists = true;\n      } catch {\n        exists = false;\n      }\n\n      if (!exists) {\n        return {\n          success: false,\n          error: `Target not found: ${target}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Determine if target is a file or directory\n      const stats = await vscode.workspace.fs.stat(uri);\n      const isDirectory = stats.type === vscode.FileType.Directory;\n\n      // Extract dependencies\n      const dependencies = await this.extractDependencies(target, isDirectory, depth, false, true);\n\n      // Generate visualization based on format\n      let visualization;\n      switch (format) {\n        case 'text':\n          visualization = this.generateTextVisualization(dependencies, target);\n          break;\n        case 'json':\n          visualization = dependencies;\n          break;\n        case 'dot':\n          visualization = this.generateDotVisualization(dependencies);\n          break;\n        default:\n          visualization = this.generateTextVisualization(dependencies, target);\n      }\n\n      return {\n        success: true,\n        output: {\n          visualization,\n          format,\n          target,\n          depth\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          target,\n          format,\n          depth,\n          dependencyCount: Object.keys(dependencies).length\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Dependency visualization failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  private async analyzeImpact(input: ToolInput, actionName: string): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const depth = (input.depth as number) || 3;\n\n    if (!filePath) {\n      return {\n        success: false,\n        error: '\\'filePath\\' is required.',\n        toolId: this.id,\n        actionName\n      };\n    }\n\n    try {\n      // Validate file exists\n      const uri = vscode.Uri.file(filePath);\n      // Replace Promise.then().catch() with try/catch for better error handling\n      let exists = false;\n      try {\n        await vscode.workspace.fs.stat(uri);\n        exists = true;\n      } catch {\n        exists = false;\n      }\n\n      if (!exists) {\n        return {\n          success: false,\n          error: `File not found: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Get workspace root\n      const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);\n      if (!workspaceFolder) {\n        return {\n          success: false,\n          error: `File is not in a workspace: ${filePath}`,\n          toolId: this.id,\n          actionName\n        };\n      }\n\n      // Extract all dependencies in the workspace\n      const allDependencies = await this.extractDependencies(workspaceFolder.uri.fsPath, true, 10, false, false);\n\n      // Find files that depend on the target file (reverse dependencies)\n      const impactedFiles = this.findImpactedFiles(allDependencies, filePath, depth);\n\n      // Calculate impact metrics\n      const impactMetrics = this.calculateImpactMetrics(impactedFiles, allDependencies);\n\n      return {\n        success: true,\n        output: {\n          impactedFiles,\n          metrics: impactMetrics,\n          summary: this.generateImpactSummary(impactedFiles, impactMetrics)\n        },\n        toolId: this.id,\n        actionName,\n        metadata: {\n          filePath,\n          depth,\n          impactedCount: impactedFiles.length\n        }\n      };\n    } catch (error: any) {\n      return {\n        success: false,\n        error: `Impact analysis failed: ${error.message || error}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Extract dependencies from a file or directory\n     */\n  private async extractDependencies(target: string, isDirectory: boolean, depth: number, includeNodeModules: boolean, includeExternalDeps: boolean): Promise<Record<string, string[]>> {\n    const dependencies: Record<string, string[]> = {};\n\n    if (depth <= 0) {\n      return dependencies;\n    }\n\n    if (isDirectory) {\n      // Process all files in the directory recursively\n      await this.processDirectory(target, dependencies, includeNodeModules, includeExternalDeps, depth);\n    } else {\n      // Process a single file\n      await this.processFile(target, dependencies, includeExternalDeps);\n    }\n\n    return dependencies;\n  }\n\n  /**\n     * Process a directory recursively to extract dependencies\n     */\n  private async processDirectory(dirPath: string, dependencies: Record<string, string[]>, includeNodeModules: boolean, includeExternalDeps: boolean, depth: number): Promise<void> {\n    try {\n      const entries = await vscode.workspace.fs.readDirectory(vscode.Uri.file(dirPath));\n\n      for (const [name, type] of entries) {\n        // Skip node_modules if not included\n        if (!includeNodeModules && name === 'node_modules') {\n          continue;\n        }\n\n        // Skip hidden directories and files\n        if (name.startsWith('.')) {\n          continue;\n        }\n\n        const fullPath = path.join(dirPath, name);\n\n        if (type === vscode.FileType.Directory) {\n          // Recursively process subdirectories\n          await this.processDirectory(fullPath, dependencies, includeNodeModules, includeExternalDeps, depth);\n        } else if (type === vscode.FileType.File) {\n          // Process files with supported extensions\n          const ext = path.extname(name).toLowerCase();\n          if (['.js', '.jsx', '.ts', '.tsx', '.vue', '.py', '.java', '.cs', '.php', '.rb', '.go', '.rs'].includes(ext)) {\n            await this.processFile(fullPath, dependencies, includeExternalDeps);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(`Error processing directory ${dirPath}:`, error);\n    }\n  }\n\n  /**\n     * Process a single file to extract its dependencies\n     */\n  private async processFile(filePath: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): Promise<void> {\n    try {\n      const document = await vscode.workspace.openTextDocument(filePath);\n      const text = document.getText();\n      const ext = path.extname(filePath).toLowerCase();\n\n      // Initialize empty dependency array for this file\n      dependencies[filePath] = [];\n\n      // Extract imports based on file type\n      switch (ext) {\n        case '.js':\n        case '.jsx':\n        case '.ts':\n        case '.tsx':\n          this.extractJavaScriptDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n        case '.py':\n          this.extractPythonDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n        case '.java':\n          this.extractJavaDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n        case '.cs':\n          this.extractCSharpDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n        case '.php':\n          this.extractPHPDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n        case '.rb':\n          this.extractRubyDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n        case '.go':\n          this.extractGoDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n        case '.rs':\n          this.extractRustDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n        case '.vue':\n          this.extractVueDependencies(filePath, text, dependencies, includeExternalDeps);\n          break;\n      }\n    } catch (error) {\n      console.error(`Error processing file ${filePath}:`, error);\n    }\n  }\n\n  /**\n     * Extract dependencies from JavaScript/TypeScript files\n     */\n  private extractJavaScriptDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match ES6 imports\n    const es6ImportRegex = /import\\s+(?:(?:\\{[^}]*\\}|\\*\\s+as\\s+\\w+|\\w+)\\s+from\\s+)?['\"]([^'\"]+)['\"]/g;\n    let match;\n\n    while ((match = es6ImportRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n\n    // Match CommonJS requires\n    const requireRegex = /(?:const|let|var)\\s+(?:\\{[^}]*\\}|\\w+)\\s*=\\s*require\\s*\\(\\s*['\"]([^'\"]+)['\"]\\s*\\)/g;\n\n    while ((match = requireRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n\n    // Match dynamic imports\n    const dynamicImportRegex = /import\\s*\\(\\s*['\"]([^'\"]+)['\"]\\s*\\)/g;\n\n    while ((match = dynamicImportRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Extract dependencies from Python files\n     */\n  private extractPythonDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match import statements\n    const importRegex = /^\\s*import\\s+(\\w+(?:\\s*,\\s*\\w+)*)/gm;\n    let match;\n\n    while ((match = importRegex.exec(content)) !== null) {\n      const imports = match[1].split(',').map(i => i.trim());\n      for (const importName of imports) {\n        this.addDependency(filePath, importName, dependencies, includeExternalDeps);\n      }\n    }\n\n    // Match from ... import statements\n    const fromImportRegex = /^\\s*from\\s+([.\\w]+)\\s+import\\s+(?:\\(([^)]+)\\)|([^#\\n]+))/gm;\n\n    while ((match = fromImportRegex.exec(content)) !== null) {\n      const moduleName = match[1];\n      this.addDependency(filePath, moduleName, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Extract dependencies from Java files\n     */\n  private extractJavaDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match import statements\n    const importRegex = /^\\s*import\\s+([^;]+);/gm;\n    let match;\n\n    while ((match = importRegex.exec(content)) !== null) {\n      const importPath = match[1].trim();\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Extract dependencies from C# files\n     */\n  private extractCSharpDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match using statements\n    const usingRegex = /^\\s*using\\s+([^;]+);/gm;\n    let match;\n\n    while ((match = usingRegex.exec(content)) !== null) {\n      const importPath = match[1].trim();\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Extract dependencies from PHP files\n     */\n  private extractPHPDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match require/include statements\n    const requireRegex = /(?:require|include|require_once|include_once)\\s*\\(\\s*['\"]([^'\"]+)['\"]\\s*\\)/g;\n    let match;\n\n    while ((match = requireRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n\n    // Match use statements\n    const useRegex = /^\\s*use\\s+([^;]+);/gm;\n\n    while ((match = useRegex.exec(content)) !== null) {\n      const importPath = match[1].trim();\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Extract dependencies from Ruby files\n     */\n  private extractRubyDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match require statements\n    const requireRegex = /^\\s*require\\s+['\"]([^'\"]+)['\"]/gm;\n    let match;\n\n    while ((match = requireRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n\n    // Match require_relative statements\n    const requireRelativeRegex = /^\\s*require_relative\\s+['\"]([^'\"]+)['\"]/gm;\n\n    while ((match = requireRelativeRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Extract dependencies from Go files\n     */\n  private extractGoDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match import statements\n    const importRegex = /import\\s*\\(\\s*((?:[\\s\\S](?!\\)))*)\\s*\\)/g;\n    let match;\n\n    while ((match = importRegex.exec(content)) !== null) {\n      const importBlock = match[1];\n      const importLines = importBlock.split('\\n');\n\n      for (const line of importLines) {\n        const importMatch = line.match(/\\s*(?:\\w+\\s+)?[\"']([^\"']+)[\"']/);\n        if (importMatch) {\n          const importPath = importMatch[1];\n          this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n        }\n      }\n    }\n\n    // Match single import statements\n    const singleImportRegex = /import\\s+(?:\\w+\\s+)?[\"']([^\"']+)[\"']/g;\n\n    while ((match = singleImportRegex.exec(content)) !== null) {\n      const importPath = match[1];\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Extract dependencies from Rust files\n     */\n  private extractRustDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match use statements\n    const useRegex = /^\\s*use\\s+([^;]+);/gm;\n    let match;\n\n    while ((match = useRegex.exec(content)) !== null) {\n      const importPath = match[1].trim();\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n\n    // Match extern crate statements\n    const externCrateRegex = /^\\s*extern\\s+crate\\s+([^;]+);/gm;\n\n    while ((match = externCrateRegex.exec(content)) !== null) {\n      const importPath = match[1].trim();\n      this.addDependency(filePath, importPath, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Extract dependencies from Vue files\n     */\n  private extractVueDependencies(filePath: string, content: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Match script section\n    const scriptMatch = content.match(/<script[^>]*>([\\s\\S]*?)<\\/script>/);\n\n    if (scriptMatch) {\n      const scriptContent = scriptMatch[1];\n      this.extractJavaScriptDependencies(filePath, scriptContent, dependencies, includeExternalDeps);\n    }\n  }\n\n  /**\n     * Add a dependency to the dependencies map\n     */\n  private addDependency(filePath: string, importPath: string, dependencies: Record<string, string[]>, includeExternalDeps: boolean): void {\n    // Skip external dependencies if not included\n    if (!includeExternalDeps && this.isExternalDependency(importPath)) {\n      return;\n    }\n\n    // Resolve relative paths\n    let resolvedPath = importPath;\n\n    if (importPath.startsWith('.')) {\n      const dir = path.dirname(filePath);\n      resolvedPath = path.resolve(dir, importPath);\n\n      // Try to resolve with common extensions\n      const extensions = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.py', '.java', '.cs', '.php', '.rb', '.go', '.rs'];\n\n      // If the path doesn't have an extension, try to add one\n      if (!path.extname(resolvedPath)) {\n        for (const ext of extensions) {\n          const pathWithExt = `${resolvedPath}${ext}`;\n          if (fs.existsSync(pathWithExt)) {\n            resolvedPath = pathWithExt;\n            break;\n          }\n        }\n      }\n    }\n\n    // Add to dependencies\n    if (!dependencies[filePath].includes(resolvedPath)) {\n      dependencies[filePath].push(resolvedPath);\n    }\n  }\n\n  /**\n     * Check if a dependency is external (not a relative path)\n     */\n  private isExternalDependency(importPath: string): boolean {\n    return !importPath.startsWith('.') && !importPath.startsWith('/');\n  }\n\n  /**\n     * Calculate statistics for dependencies\n     */\n  private calculateDependencyStats(dependencies: Record<string, string[]>): any {\n    const stats = {\n      totalFiles: Object.keys(dependencies).length,\n      totalDependencies: 0,\n      averageDependenciesPerFile: 0,\n      maxDependencies: 0,\n      maxDependenciesFile: '',\n      filesWithNoDependencies: 0,\n      filesWithMostDependencies: [] as { file: string, count: number }[]\n    };\n\n    // Calculate total dependencies and find max\n    for (const [file, deps] of Object.entries(dependencies)) {\n      stats.totalDependencies += deps.length;\n\n      if (deps.length === 0) {\n        stats.filesWithNoDependencies++;\n      }\n\n      if (deps.length > stats.maxDependencies) {\n        stats.maxDependencies = deps.length;\n        stats.maxDependenciesFile = file;\n      }\n    }\n\n    // Calculate average\n    if (stats.totalFiles > 0) {\n      stats.averageDependenciesPerFile = stats.totalDependencies / stats.totalFiles;\n    }\n\n    // Find top 5 files with most dependencies\n    stats.filesWithMostDependencies = Object.entries(dependencies)\n      .map(([file, deps]) => ({ file, count: deps.length }))\n      .sort((a, b) => b.count - a.count)\n      .slice(0, 5);\n\n    return stats;\n  }\n\n  /**\n     * Generate a summary of dependencies\n     */\n  private generateDependencySummary(dependencies: Record<string, string[]>, stats: any): string {\n    return `\nDependency Analysis Summary:\n---------------------------\nTotal Files: ${stats.totalFiles}\nTotal Dependencies: ${stats.totalDependencies}\nAverage Dependencies Per File: ${stats.averageDependenciesPerFile.toFixed(2)}\nFiles With No Dependencies: ${stats.filesWithNoDependencies}\nFile With Most Dependencies: ${stats.maxDependenciesFile} (${stats.maxDependencies} dependencies)\n\nTop 5 Files With Most Dependencies:\n${stats.filesWithMostDependencies.map((item: any) => `  ${item.file}: ${item.count} dependencies`).join('\\n')}\n`;\n  }\n\n  /**\n     * Find circular dependencies in the dependency graph\n     */\n  private findCircular(dependencies: Record<string, string[]>, maxResults: number): any[] {\n    const circularDeps: any[] = [];\n    const visited = new Set<string>();\n    const stack = new Set<string>();\n\n    const dfs = (node: string, path: string[] = []): void => {\n      if (circularDeps.length >= maxResults) {\n        return;\n      }\n\n      if (stack.has(node)) {\n        // Found a cycle\n        const cycleStart = path.indexOf(node);\n        const cycle = [...path.slice(cycleStart), node];\n\n        circularDeps.push({\n          cycle,\n          length: cycle.length\n        });\n\n        return;\n      }\n\n      if (visited.has(node) || !dependencies[node]) {\n        return;\n      }\n\n      visited.add(node);\n      stack.add(node);\n      path.push(node);\n\n      for (const dep of dependencies[node]) {\n        dfs(dep, [...path]);\n      }\n\n      stack.delete(node);\n    };\n\n    // Start DFS from each node\n    for (const node of Object.keys(dependencies)) {\n      if (circularDeps.length >= maxResults) {\n        break;\n      }\n\n      if (!visited.has(node)) {\n        dfs(node);\n      }\n    }\n\n    return circularDeps;\n  }\n\n  /**\n     * Generate a summary of circular dependencies\n     */\n  private generateCircularDependencySummary(circularDeps: any[]): string {\n    if (circularDeps.length === 0) {\n      return 'No circular dependencies found.';\n    }\n\n    return `\nCircular Dependencies Summary:\n----------------------------\nFound ${circularDeps.length} circular dependencies.\n\n${circularDeps.map((item, index) => `\nCircular Dependency #${index + 1}:\n  Length: ${item.length}\n  Path: ${item.cycle.join(' -> ')}\n`).join('\\n')}\n`;\n  }\n\n  /**\n     * Generate a text visualization of dependencies\n     */\n  private generateTextVisualization(dependencies: Record<string, string[]>, rootPath: string): string {\n    const lines: string[] = [`Dependency Tree for ${rootPath}:`];\n\n    const printDependencies = (file: string, deps: string[], indent = '', visited: Set<string> = new Set()): void => {\n      if (visited.has(file)) {\n        lines.push(`${indent}${file} (circular reference)`);\n        return;\n      }\n\n      visited.add(file);\n\n      if (deps.length === 0) {\n        lines.push(`${indent}${file} (no dependencies)`);\n        return;\n      }\n\n      lines.push(`${indent}${file}`);\n\n      for (const dep of deps) {\n        if (dependencies[dep]) {\n          printDependencies(dep, dependencies[dep], `${indent}  `, new Set(visited));\n        } else {\n          lines.push(`${indent}  ${dep}`);\n        }\n      }\n    };\n\n    // Start with the root path if it's in the dependencies\n    if (dependencies[rootPath]) {\n      printDependencies(rootPath, dependencies[rootPath]);\n    } else {\n      // Otherwise, print all top-level dependencies\n      for (const [file, deps] of Object.entries(dependencies)) {\n        printDependencies(file, deps);\n      }\n    }\n\n    return lines.join('\\n');\n  }\n\n  /**\n     * Generate a DOT visualization of dependencies\n     */\n  private generateDotVisualization(dependencies: Record<string, string[]>): string {\n    const lines: string[] = ['digraph Dependencies {', '  rankdir=LR;', '  node [shape=box, style=filled, fillcolor=lightblue];'];\n\n    // Add nodes\n    for (const file of Object.keys(dependencies)) {\n      const nodeName = this.sanitizeForDot(file);\n      const label = path.basename(file);\n      lines.push(`  \"${nodeName}\" [label=\"${label}\"];`);\n    }\n\n    // Add edges\n    for (const [file, deps] of Object.entries(dependencies)) {\n      const sourceNode = this.sanitizeForDot(file);\n\n      for (const dep of deps) {\n        if (dependencies[dep]) { // Only include internal dependencies\n          const targetNode = this.sanitizeForDot(dep);\n          lines.push(`  \"${sourceNode}\" -> \"${targetNode}\";`);\n        }\n      }\n    }\n\n    lines.push('}');\n\n    return lines.join('\\n');\n  }\n\n  /**\n     * Sanitize a string for use in DOT format\n     */\n  private sanitizeForDot(str: string): string {\n    return str.replace(/\\\\/g, '/');\n  }\n\n  /**\n     * Find files that would be impacted by changing a file\n     */\n  private findImpactedFiles(dependencies: Record<string, string[]>, targetFile: string, depth: number): string[] {\n    const impacted: string[] = [];\n    const visited = new Set<string>();\n\n    const findDependents = (file: string, currentDepth = 0): void => {\n      if (currentDepth > depth || visited.has(file)) {\n        return;\n      }\n\n      visited.add(file);\n\n      // Find files that depend on this file\n      for (const [depFile, deps] of Object.entries(dependencies)) {\n        if (deps.includes(file) && !impacted.includes(depFile)) {\n          impacted.push(depFile);\n          findDependents(depFile, currentDepth + 1);\n        }\n      }\n    };\n\n    findDependents(targetFile);\n\n    return impacted;\n  }\n\n  /**\n     * Calculate impact metrics\n     */\n  private calculateImpactMetrics(impactedFiles: string[], allDependencies: Record<string, string[]>): any {\n    const totalFiles = Object.keys(allDependencies).length;\n    const impactPercentage = totalFiles > 0 ? (impactedFiles.length / totalFiles) * 100 : 0;\n\n    // Calculate impact severity\n    let impactSeverity = 'Low';\n    if (impactPercentage > 50) {\n      impactSeverity = 'High';\n    } else if (impactPercentage > 20) {\n      impactSeverity = 'Medium';\n    }\n\n    return {\n      impactedCount: impactedFiles.length,\n      totalFiles,\n      impactPercentage,\n      impactSeverity\n    };\n  }\n\n  /**\n     * Generate a summary of impact analysis\n     */\n  private generateImpactSummary(impactedFiles: string[], metrics: any): string {\n    return `\nImpact Analysis Summary:\n----------------------\nFile would impact ${metrics.impactedCount} out of ${metrics.totalFiles} files (${metrics.impactPercentage.toFixed(2)}%).\nImpact Severity: ${metrics.impactSeverity}\n\n${impactedFiles.length > 0 ? `Impacted Files:\n${impactedFiles.map(file => `  ${file}`).join('\\n')}` : 'No files would be impacted.'}\n`;\n  }\n}\n"]}