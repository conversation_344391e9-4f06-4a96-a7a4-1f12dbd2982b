{"version": 3, "file": "huggingfaceProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/huggingfaceProvider.ts"], "names": [], "mappings": ";;;AACA,uDAAoD;AAIpD,yCAAsC;AAEtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;GAEG;AACH,MAAa,mBAAoB,SAAQ,iCAAe;IAC7C,UAAU,GAAG,aAAa,CAAC;IAC3B,WAAW,GAAG,aAAa,CAAC;IAC5B,WAAW,GAAG,qDAAqD,CAAC;IACpE,OAAO,GAAG,sCAAsC,CAAC;IACjD,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,KAAK,CAAC;IACtC,eAAe,GAAG,6CAA6C,CAAC;IAChE,YAAY,GAAG,oCAAoC,CAAC;IAErD,MAAM,GAAQ,IAAI,CAAC;IACnB,OAAO,CAAS;IAExB,YAAY,OAAgC;QAC1C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACG,gBAAgB;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBAClD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAC/C,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,YAAY;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC/C,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,kBAA6C,EAC7C,MAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,oCAAoC,EAAE,CAAC;QACtE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,qBAAqB;YACrB,IAAI,UAAU,GAAG,EAAE,CAAC;YAEpB,gCAAgC;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,UAAU,IAAI,eAAe,MAAM,CAAC,YAAY,IAAI,CAAC;YACvD,CAAC;YAED,mCAAmC;YACnC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,UAAU,IAAI,aAAa,OAAO,CAAC,OAAO,IAAI,CAAC;oBACjD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,UAAU,IAAI,kBAAkB,OAAO,CAAC,OAAO,IAAI,CAAC;oBACtD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,UAAU,IAAI,eAAe,OAAO,CAAC,OAAO,IAAI,CAAC;oBACnD,CAAC;gBACH,CAAC;YACH,CAAC;YAED,yBAAyB;YACzB,UAAU,IAAI,aAAa,MAAM,CAAC,MAAM,mBAAmB,CAAC;YAE5D,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE,EAAE;gBACrD,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE;oBACV,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,cAAc,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;oBACxC,gBAAgB,EAAE,KAAK;iBACxB;aACF,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,IAAI,EAAE,CAAC;YAEvD,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,MAAM;gBACpB,KAAK,EAAE;oBACL,YAAY,EAAE,CAAC,EAAE,0CAA0C;oBAC3D,gBAAgB,EAAE,CAAC;oBACnB,WAAW,EAAE,CAAC;iBACf;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aACnG,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,eAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YACvE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,mFAAmF;QACnF,mDAAmD;QACnD,MAAM,aAAa,GAAG;YACpB;gBACE,EAAE,EAAE,oCAAoC;gBACxC,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,2BAA2B;gBACxC,aAAa,EAAE,IAAI;aACpB;YACD;gBACE,EAAE,EAAE,+BAA+B;gBACnC,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,4BAA4B;gBACzC,aAAa,EAAE,IAAI;aACpB;YACD;gBACE,EAAE,EAAE,gCAAgC;gBACpC,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,6BAA6B;gBAC1C,aAAa,EAAE,IAAI;aACpB;YACD;gBACE,EAAE,EAAE,2BAA2B;gBAC/B,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,0BAA0B;gBACvC,aAAa,EAAE,IAAI;aACpB;YACD;gBACE,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,uBAAuB;gBACpC,aAAa,EAAE,IAAI;aACpB;YACD;gBACE,EAAE,EAAE,oBAAoB;gBACxB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,gCAAgC;gBAC7C,aAAa,EAAE,IAAI;aACpB;SACF,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,4BAA4B,aAAa,CAAC,MAAM,mBAAmB,CAAC,CAAC;QACjF,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gEAAgE;aAC1E,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,kCAAkC;YAClC,MAAM,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YACvE,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE;gBAClC,MAAM,EAAE,OAAO;gBACf,UAAU,EAAE;oBACV,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,KAAK;iBACxB;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,8DAA8D,KAAK,GAAG;aAChF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yCAAyC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC7G,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,OAAY;QACpC,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,0BAA0B;gBACvC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,qEAAqE;gBAClF,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;SACF,CAAC;IACJ,CAAC;CACF;AAjPD,kDAiPC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { BaseLL<PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { logger } from '../../logger';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for HuggingFace Inference API\n */\nexport class HuggingFaceProvider extends BaseLLMProvider {\n  readonly providerId = 'huggingface';\n  readonly displayName = 'HuggingFace';\n  readonly description = 'Access HuggingFace models through the Inference API';\n  readonly website = 'https://huggingface.co/inference-api';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = false;\n  readonly defaultEndpoint = 'https://api-inference.huggingface.co/models';\n  readonly defaultModel = 'mistralai/Mistral-7B-Instruct-v0.2';\n\n  private client: any = null;\n  private baseUrl: string;\n\n  constructor(context: vscode.ExtensionContext) {\n    super(context);\n    this.baseUrl = this.defaultEndpoint;\n    this.initializeClient();\n  }\n\n  /**\n     * Initialize the Axios client for API requests\n     */\n  private initializeClient(): void {\n    try {\n      if (!this.config.apiKey) {\n        logger.warn('HuggingFace API key not configured');\n        this.client = null;\n        return;\n      }\n\n      this.client = axios.create({\n        baseURL: this.baseUrl,\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      logger.info('HuggingFace client initialized');\n    } catch (error) {\n      logger.error('Failed to initialize HuggingFace client:', error);\n      this.client = null;\n    }\n  }\n\n  /**\n     * Check if the provider is configured\n     */\n  public isConfigured(): boolean {\n    return !!this.client && !!this.config.apiKey;\n  }\n\n  /**\n     * Generate text using HuggingFace\n     */\n  public async generate(\n    params: LLMGenerateParams,\n    _cancellationToken?: vscode.CancellationToken,\n    _tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'HuggingFace client not initialized' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare the prompt\n      let fullPrompt = '';\n\n      // Add system prompt if provided\n      if (params.systemPrompt) {\n        fullPrompt += `<|system|>\\n${params.systemPrompt}\\n`;\n      }\n\n      // Add history messages if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            fullPrompt += `<|user|>\\n${message.content}\\n`;\n          } else if (message.role === 'assistant') {\n            fullPrompt += `<|assistant|>\\n${message.content}\\n`;\n          } else if (message.role === 'system') {\n            fullPrompt += `<|system|>\\n${message.content}\\n`;\n          }\n        }\n      }\n\n      // Add the current prompt\n      fullPrompt += `<|user|>\\n${params.prompt}\\n<|assistant|>\\n`;\n\n      // Make the API request\n      const response = await this.client.post(`/${modelId}`, {\n        mode: params.mode,\n        inputs: fullPrompt,\n        parameters: {\n          temperature: params.temperature || 0.7,\n          max_new_tokens: params.maxTokens || 1024,\n          return_full_text: false\n        }\n      });\n\n      // Extract the response content\n      const content = response.data[0]?.generated_text || '';\n\n      return {\n        content,\n        finishReason: 'stop',\n        usage: {\n          promptTokens: 0, // HuggingFace doesn't provide token usage\n          completionTokens: 0,\n          totalTokens: 0\n        }\n      };\n    } catch (error) {\n      logger.error('Error generating text with HuggingFace:', error);\n      return {\n        content: '',\n        error: `HuggingFace generation error: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * List available models from HuggingFace\n     */\n  public async listModels(): Promise<LLMModelInfo[]> {\n    if (!this.client) {\n      logger.warn('Cannot fetch HuggingFace models, client not configured.');\n      return [];\n    }\n\n    // HuggingFace doesn't have a simple API to list all available models for inference\n    // So we'll return a curated list of popular models\n    const popularModels = [\n      {\n        id: 'mistralai/Mistral-7B-Instruct-v0.2',\n        name: 'Mistral 7B Instruct',\n        description: 'Mistral 7B Instruct model',\n        contextWindow: 8192\n      },\n      {\n        id: 'meta-llama/Llama-2-7b-chat-hf',\n        name: 'Llama 2 7B Chat',\n        description: 'Meta Llama 2 7B Chat model',\n        contextWindow: 4096\n      },\n      {\n        id: 'meta-llama/Llama-2-13b-chat-hf',\n        name: 'Llama 2 13B Chat',\n        description: 'Meta Llama 2 13B Chat model',\n        contextWindow: 4096\n      },\n      {\n        id: 'tiiuae/falcon-7b-instruct',\n        name: 'Falcon 7B Instruct',\n        description: 'Falcon 7B Instruct model',\n        contextWindow: 2048\n      },\n      {\n        id: 'microsoft/phi-2',\n        name: 'Phi-2',\n        description: 'Microsoft Phi-2 model',\n        contextWindow: 2048\n      },\n      {\n        id: 'google/gemma-7b-it',\n        name: 'Gemma 7B Instruct',\n        description: 'Google Gemma 7B Instruct model',\n        contextWindow: 8192\n      }\n    ];\n\n    logger.info(`Provider huggingface has ${popularModels.length} models available`);\n    return popularModels;\n  }\n\n  /**\n     * Test connection to HuggingFace\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'HuggingFace client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Try a simple model info request\n      const model = modelId || this.config.defaultModel || this.defaultModel;\n      await this.client.post(`/${model}`, {\n        inputs: 'Hello',\n        parameters: {\n          max_new_tokens: 5,\n          return_full_text: false\n        }\n      });\n\n      return {\n        success: true,\n        message: `Successfully connected to HuggingFace API and tested model ${model}.`\n      };\n    } catch (error) {\n      logger.error('HuggingFace connection test failed:', error);\n      return {\n        success: false,\n        message: `Failed to connect to HuggingFace API: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * Update the provider configuration\n     */\n  public async updateConfig(_config: any): Promise<void> {\n    await super.updateConfig(_config);\n    this.initializeClient();\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your HuggingFace API key',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use (e.g., mistralai/Mistral-7B-Instruct-v0.2)',\n        required: false,\n        type: 'string'\n      }\n    ];\n  }\n}\n\n\n"]}