{"version": 3, "file": "codeAIEditingTool.js", "sourceRoot": "", "sources": ["../../src/tools/codeAIEditingTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA00BA,4CAyNC;AAniCD,8BAA8B;AAC9B,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AAC7B,+BAAuE;AACvE,mDAAqC;AACrC,wDAA0C;AAC1C,8DAAgD;AAmBhD,wBAAwB;AACxB,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC,KAAK,UAAU,uBAAuB;IACpC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACzB,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;QAC1B,mBAAmB,GAAG,IAAI,CAAC;IAC7B,CAAC;AACH,CAAC;AAED,yBAAyB;AACzB,IAAI,MAAM,GAAkB,IAAI,CAAC;AACjC,IAAI,QAAQ,GAAoB,IAAI,CAAC;AAarC,sCAAsC;AACtC,IAAI,SAAgC,CAAC;AACrC,IAAI,WAAoC,CAAC;AACzC,IAAI,QAA8B,CAAC;AAEnC,IAAI,CAAC;IACH,wDAAwD;IACxD,SAAS,GAAG,wDAAa,WAAW,GAAC,CAAC;AACxC,CAAC;AAAC,OAAO,CAAC,EAAE,CAAC;IACX,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;AACxE,CAAC;AAED,IAAI,CAAC;IACH,wDAAwD;IACxD,WAAW,GAAG,wDAAa,cAAc,GAAC,CAAC;AAC7C,CAAC;AAAC,OAAO,CAAC,EAAE,CAAC;IACX,OAAO,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;AAC7E,CAAC;AAED,IAAI,CAAC;IACH,wDAAwD;IACxD,QAAQ,GAAG,wDAAa,UAAU,GAAC,CAAC;AACtC,CAAC;AAAC,OAAO,CAAC,EAAE,CAAC;IACX,OAAO,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;AAC1E,CAAC;AA2BD;;;;;;GAMG;AACH,KAAK,UAAU,UAAU,CACvB,QAAgB,EAChB,OAAe,EACf,OAAe,YAAY;IAE3B,IAAI,CAAC;QACH,IAAI,SAAS,GAAG,OAAO,CAAC;QAExB,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC;gBACH,SAAS,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE;oBACzC,MAAM,EAAE,IAAI;oBACZ,WAAW,EAAE,IAAI;oBACjB,aAAa,EAAE,KAAK;iBACrB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,8DAA8D,EAAE,KAAK,CAAC,CAAC;YACtF,CAAC;QACH,CAAC;QAED,wDAAwD;QACxD,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YAC1B,SAAS,GAAG,OAAO;iBAChB,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAE,yBAAyB;iBACjD,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,6BAA6B;iBACrD,IAAI,EAAE,GAAG,IAAI,CAAC,CAAU,iCAAiC;QAC9D,CAAC;QAED,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,OAAO;YACf,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;SACzD,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,OAAO;YACf,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,+BAA+B;YAC/E,IAAI,EAAE,EAAE;SACT,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,mBAAmB,CAAC,OAAe;IAC1C,OAAO,OAAO;SACX,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAE,yBAAyB;SACjD,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAE,6BAA6B;SAC3D,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAE,8BAA8B;SAC1D,IAAI,EAAE,GAAG,IAAI,CAAC,CAAE,iCAAiC;AACtD,CAAC;AAQD,oBAAoB;AACpB,MAAa,SAAS;IACpB,IAAI,CAAS;IACb,eAAe,CAAW;IAC1B,eAAe,CAAW;IAE1B,YAAY,IAAY,EAAE,kBAA4B,CAAC,QAAQ,CAAC,EAAE,kBAA4B,CAAC,oBAAoB,EAAE,YAAY,CAAC;QAChI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,UAAoB,IAAI,CAAC,eAAe,EAAE,UAAoB,IAAI,CAAC,eAAe;QAChG,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EACxB,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CACzB,CAAC;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,UAAoB,IAAI,CAAC,eAAe,EAAE,UAAoB,IAAI,CAAC,eAAe;QAChG,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAC7B,OAAO,MAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,OAAe;QAC/C,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACpE,MAAM,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,QAAgB;QAC/B,MAAM,MAAM,GAAG,GAAG,QAAQ,MAAM,CAAC;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC9C,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAtCD,8BAsCC;AAQD,MAAa,QAAQ;IACnB,MAAM,CAAC,WAAW,CAAC,MAAc,EAAE,KAAa,EAAE,IAAY;QAC5D,OAAO,IAAA,kBAAW,EAChB,IAAI,EACJ,MAAM,EACN,KAAK,EACL,UAAU,EACV,UAAU,EACV,EAAE,EAAG,yCAAyC;QAC9C,EAAE,OAAO,EAAE,CAAC,EAAE,CACf,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAAe,EAAE,KAAa,EAAE,UAA6B,EAAE;QAC/E,MAAM,WAAW,GAAG,IAAA,iBAAU,EAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,MAAM,GAAG,OAAO,CAAC;QAErB,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE,CAAC;YAC5B,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAA,kBAAW,EAC1B,CAAC,CAAC,WAAW,IAAI,MAAM,EACvB,CAAC,CAAC,SAAS,IAAI,EAAE,EACjB,CAAC,CAAC,SAAS,IAAI,EAAE,EACjB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnB,OAAO,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK;gBACjE,GAAG,CAAC,CAAC,KAAK;aACX,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACd,CAAC;YAEF,sCAAsC;YACtC,MAAM,YAAY,GAAG;gBACnB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;gBACjG,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC;aACpC,CAAC;YAEF,MAAM,WAAW,GAAG,IAAA,iBAAU,EAAC,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;YAE/D,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;gBACpC,MAAM,GAAG,WAAW,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,MAAc,EAAE,KAAa;QAC5C,gDAAgD;QAChD,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAErC,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;QAElE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC;iBAAM,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBAClC,IAAI,CAAC,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACnC,CAAC;iBAAM,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;CACF;AAxED,4BAwEC;AAqBD,MAAa,UAAU;IACb,SAAS,CAAY;IACrB,YAAY,CAAW;IACvB,eAAe,CAAW;IAElC,YAAY,SAAoB;QAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,CAAC,8BAA8B,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,CAAC,oBAAoB,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;IAC5E,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,KAAa,EAAE,UAAyB,EAAE;QACrD,MAAM,OAAO,GAAmB,EAAE,CAAC;QACnC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAC1C,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EACzC,IAAI,CAAC,eAAe,CACrB,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE3D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACpD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBACtB,IAAI,KAA6B,CAAC;oBAElC,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;wBACjD,OAAO,CAAC,IAAI,CAAC;4BACX,IAAI;4BACJ,IAAI,EAAE,CAAC,GAAG,CAAC;4BACX,QAAQ,EAAE,IAAI;4BACd,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;4BACf,WAAW,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC;4BAC5B,SAAS,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;yBAC7C,CAAC,CAAC;wBAEH,gCAAgC;wBAChC,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;4BAC/D,OAAO,OAAO,CAAC;wBACjB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,wBAAwB,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,iBAAiB,CAAC,KAAa,EAAE,OAAsB;QAC7D,IAAI,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAE/D,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,OAAO,GAAG,MAAM,OAAO,KAAK,CAAC;QAC/B,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;QACjD,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACpC,CAAC;IAEO,YAAY,CAAC,MAAc;QACjC,OAAO,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IACvD,CAAC;CACF;AAnED,gCAmEC;AAUD,MAAa,OAAO;IAClB,MAAM,CAAC,QAAQ,CAAC,IAAS,IAAa,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACzO,MAAM,CAAC,SAAS,CAAC,IAAa,EAAE,IAAY,IAAe,MAAM,GAAG,GAAc,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI;QAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ;QAAE,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;CACpN;AAHD,0BAGC;AAED,kCAAkC;AAClC,MAAa,aAAa;IACxB,MAAM,CAAC,SAAS,CAAC,KAAe,EAAE,KAAa,EAAE,GAAW,EAAE,MAAc,IAAc,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IAC/O,MAAM,CAAC,cAAc,CAAC,KAAe,EAAE,KAAa,EAAE,GAAW,EAAE,MAAc,IAAc,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC;IAC3N,MAAM,CAAC,WAAW,CAAC,KAAe,EAAE,KAAa,EAAE,GAAW,IAAc,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC;CACjK;AAJD,sCAIC;AAED,+BAA+B;AAC/B,MAAa,aAAa;IACxB,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,QAAiE;QAC3G,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;YAClC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC;SACrC,CAAC,CAAC;QAEH,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,EAAE,EAAE,CAAC;YAC5B,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9B,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,YAAoB,IAAI,GAAG,IAAI;QACvE,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACzB,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACjD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEvC,IAAI,CAAC;YACH,OAAO,MAAM,GAAG,KAAK,EAAE,CAAC;gBACtB,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;gBAClE,IAAI,SAAS,KAAK,CAAC;oBAAE,MAAM;gBAC3B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;gBACnD,MAAM,IAAI,SAAS,CAAC;YACtB,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;gBAAS,CAAC;YACT,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;CACF;AAlCD,sCAkCC;AAeD,MAAa,aAAa;IAChB,KAAK,GAA+B,IAAI,GAAG,EAAE,CAAC;IAC9C,OAAO,GAA6B,IAAI,GAAG,EAAE,CAAC;IAC9C,cAAc,GAA0C;QAC9D,EAAE,KAAK,EAAE,iEAAiE,EAAE,IAAI,EAAE,UAAU,EAAE;QAC9F,EAAE,KAAK,EAAE,yDAAyD,EAAE,IAAI,EAAE,UAAU,EAAE;QACtF,EAAE,KAAK,EAAE,iDAAiD,EAAE,IAAI,EAAE,OAAO,EAAE;QAC3E,EAAE,KAAK,EAAE,sDAAsD,EAAE,IAAI,EAAE,OAAO,EAAE;QAChF,EAAE,KAAK,EAAE,oDAAoD,EAAE,IAAI,EAAE,QAAQ,EAAE;QAC/E,EAAE,KAAK,EAAE,qDAAqD,EAAE,IAAI,EAAE,WAAW,EAAE;QACnF,EAAE,KAAK,EAAE,kDAAkD,EAAE,IAAI,EAAE,MAAM,EAAE;QAC3E,EAAE,KAAK,EAAE,mDAAmD,EAAE,IAAI,EAAE,UAAU,EAAE;QAChF,EAAE,KAAK,EAAE,kCAAkC,EAAE,IAAI,EAAE,OAAO,EAAE;QAC5D,EAAE,KAAK,EAAE,gCAAgC,EAAE,IAAI,EAAE,KAAK,EAAE;QACxD,EAAE,KAAK,EAAE,gCAAgC,EAAE,IAAI,EAAE,UAAU,EAAE;KAC9D,CAAC;IAEF;QACE,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED,KAAK;QACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED,QAAQ;QACN,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,gBAAgB,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,IAAY,EAAE,OAAe;QAC3C,MAAM,OAAO,GAAkB,EAAE,CAAC;QAClC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,KAAK,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAChC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtB,MAAM,KAAK,GAAgB;wBACzB,IAAI;wBACJ,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,IAAI;wBACJ,IAAI,EAAE,CAAC,GAAG,CAAC;wBACX,SAAS,EAAE,CAAC,GAAG,CAAC;wBAChB,OAAO,EAAE,CAAC,GAAG,CAAC;qBACf,CAAC;oBACF,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACpB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,CAAC,MAAc,EAAE,aAAsB,KAAK;QAC9C,MAAM,OAAO,GAAkB,EAAE,CAAC;QAClC,MAAM,UAAU,GAAG,UAAU;YAC3B,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAEzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YAC1C,MAAM,KAAK,GAAG,UAAU;gBACtB,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU;gBAC3B,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAElD,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA/ED,sCA+EC;AAED,MAAa,QAAQ;IACX,SAAS,CAAY;IACrB,aAAa,CAAgB;IAC7B,QAAQ,CAAa;IACrB,MAAM,CAAM;IACZ,QAAQ,CAAM;IACd,QAAQ,GAAW,CAAC,CAAC;IACrB,QAAQ,GAAa,EAAE,CAAC;IACxB,KAAK,GAAW,EAAE,CAAC;IACnB,OAAO,CAKb;IAEF,YAAY,IAAY;QACtB,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,6BAA6B;QAC7B,IAAI,CAAC,OAAO,GAAG;YACb,YAAY,EAAE,KAAK,EAAE,QAAgB,EAAE,OAAe,EAAE,OAAe,EAAE,OAAe,YAAY,EAAqB,EAAE;gBACzH,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC7D,CAAC;YACD,eAAe,EAAE,KAAK,EAAE,QAAgB,EAAE,SAAiB,EAAE,OAAe,EAAE,QAAgB,EAAE,OAAe,YAAY,EAAqB,EAAE;gBAChJ,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC5E,CAAC;YACD,SAAS,EAAE,KAAK,EAAE,QAAgB,EAAE,SAAiB,EAAE,OAAe,EAAE,MAAc,EAAE,OAAe,YAAY,EAAqB,EAAE;gBACxI,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YACpE,CAAC;YACD,SAAS,EAAE,KAAK,EAAE,IAAU,EAAiE,EAAE;gBAC7F,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;SACF,CAAC;IACJ,CAAC;IAED,iCAAiC;IACjC,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAEO,SAAS;QACf,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,WAAW;QACjB,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,QAAgB,EAChB,OAAe,EACf,OAAe,EACf,OAAe,YAAY;QAE3B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAE5D,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC;YACH,iBAAiB;YACjB,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAEvC,qCAAqC;YACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,+BAA+B,OAAO,IAAI,CAAC,CAAC;YACzE,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE7C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,WAAW;oBACnB,KAAK,EAAE,WAAW;oBAClB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,WAAW,OAAO,qBAAqB;iBAC/C,CAAC;YACJ,CAAC;YAED,qBAAqB;YACrB,IAAI,UAAU,GAAG,WAAW,CAAC;YAC7B,IAAI,MAAM,GAAG,CAAC,CAAC;YAEf,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACrC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;oBAC9C,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC,CAAC;oBAC5D,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC;oBACtD,UAAU,GAAG,MAAM,GAAG,OAAO,GAAG,KAAK,CAAC;oBACtC,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,QAAQ,GAAG,UAAU,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,UAAU;gBACjB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC;aAC9D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,oCAAoC;aACrF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,QAAgB,EAChB,SAAiB,EACjB,OAAe,EACf,QAAgB,EAChB,OAAe,YAAY;QAE3B,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC5D,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEtC,uCAAuC;YACvC,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;YACvE,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YAC3D,MAAM,aAAa,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;YAEzE,0BAA0B;YAC1B,MAAM,WAAW,GAAG,YAAY,QAAQ;IAC1C,aAAa;EACf,CAAC;YAEG,kDAAkD;YAClD,MAAM,UAAU,GAAG;gBACjB,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC;gBACpC,GAAG,QAAQ,KAAK;gBAChB,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC;aAChC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEb,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,UAAU;gBACjB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC;aAC9D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;aAC7E,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CACrB,QAAgB,EAChB,SAAiB,EACjB,OAAe,EACf,MAAc,EACd,OAAe,YAAY;QAE3B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEtC,4BAA4B;QAC5B,IAAI,SAAS,GAAG,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvF,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B;aACvC,CAAC;QACJ,CAAC;QAED,8BAA8B;QAC9B,MAAM,KAAK,GAAG,SAAS,GAAG,CAAC,CAAC;QAC5B,MAAM,GAAG,GAAG,OAAO,CAAC;QACpB,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;QAE1B,iBAAiB;QACjB,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC5C,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QAC5B,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC;QAEpC,yDAAyD;QACzD,MAAM,SAAS,GAAG,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACnE,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,GAAG,WAAW,CAAC,CAAC;QAE9C,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEvC,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,WAAW;YACnB,KAAK,EAAE,UAAU;YACjB,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC;SAC9D,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,IAAU;QAChC,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAErB,iBAAiB;gBACjB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtD,OAAO,EAAE,CAAC;gBAEV,gBAAgB;gBAChB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBACd,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;gBACpB,CAAC;qBAAM,CAAC;oBACN,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC1E,IAAI,IAAI,QAAQ,CAAC;gBACnB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC9D,4CAA4C;YAC9C,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO;YACP,OAAO;YACP,IAAI;SACL,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,YAAoB;QAC3C,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,uBAAuB,EAAE,CAAC;YAEhC,+BAA+B;YAC/B,MAAM,GAAG,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;YAEnC,8BAA8B;YAC9B,MAAM,IAAI,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAErD,sCAAsC;YACtC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;YAC5D,MAAM,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEtC,IAAI,CAAC;gBACH,0DAA0D;gBAC1D,QAAQ,GAAG,MAAO,YAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAE1D,kCAAkC;gBAClC,IAAI,MAAM,IAAI,QAAQ,EAAE,CAAC;oBACvB,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;oBAAS,CAAC;gBACT,wBAAwB;gBACxB,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,OAAe;QACzC,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,CAAC;QACxB,OAAO,OAAO;aACX,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAE,uCAAuC;aAC/D,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAK,uCAAuC;aAChE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAE,4CAA4C;aACtE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,0CAA0C;aACrE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAK,gDAAgD;IAC9E,CAAC;CACF;AApSD,4BAoSC;AAWD;;;GAGG;AACI,KAAK,UAAU,gBAAgB,CACpC,IAAY,EACZ,MAAc,EACd,QAAgB,EAChB,SAAsD;IAOtD,MAAM,QAAQ,GAAG,uBAAuB,CAAA;IACxC,MAAM,SAAS,GAAG,UAAiB,CAAA;IAEnC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;QACzB,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;QAC1B,SAAS,CAAC,QAAQ,CAAC,GAAG;YACpB,MAAM,EAAE,IAAI,YAAY,CAAC,MAAM,EAAE;YACjC,QAAQ,EAAE,IAAI,GAAG,EAAiC;SACpC,CAAC;IACnB,CAAC;IAED,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAgB,CAAC;IAEhE,uDAAuD;IACvD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC;YACH,4CAA4C;YAC5C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,WAAW,EAAE,CAAC;gBACtC,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;gBAC1B,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YACtE,CAAC;YAEP,4BAA4B;YAC5B,MAAM,IAAI,GAAG,MAAO,YAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3D,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CACb,2CAA2C,MAAM,WAAW,QAAQ,SAAS,GAAG,EAAE,CACnF,CAAC;QACJ,CAAC;IACH,CAAC;IACD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,CAAA;IAEzC,uCAAuC;IACvC,IAAI,MAAc,CAAA;IAClB,IAAI,CAAC;QACH,MAAM,GAAG,MAAM,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;IACzC,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,SAAS,GAAG,EAAE,CAAC,CAAA;IAC7D,CAAC;IAED,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IACjC,MAAM,KAAK,GAA0D,EAAE,CAAA;IAEvE,6CAA6C;IAC7C,SAAS,UAAU,CAAC,CAAS,EAAE,CAAS,EAAE,SAAS,GAAG,GAAG;QACvD,MAAM,QAAQ,GAAG,CAAC,CAAS,EAAE,EAAE,CAC7B,IAAI,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAA;QACvD,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;QACtC,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;QAC1D,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACxC,OAAO,KAAK,GAAG,CAAC,IAAI,YAAY,GAAG,KAAK,IAAI,SAAS,CAAA;IACvD,CAAC;IAED,wDAAwD;IACxD,SAAS,YAAY,CAAC,OAAiB;QACrC,+BAA+B;QAC/B,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QACvB,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAA;QAC3C,IAAI,CAAC,CAAC;YAAE,OAAO,IAAI,CAAA;QAEnB,MAAM,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAA;QAC9B,MAAM,GAAG,GAAG,IAAI,GAAG,EAAyB,CAAA;QAE5C,2DAA2D;QAC3D,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;YACzC,IAAI,CAAC,EAAE;gBAAE,SAAQ;YACjB,EAAE,CAAC,CAAC,CAAC;iBACF,KAAK,CAAC,GAAG,CAAC;iBACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;iBACpB,MAAM,CAAC,OAAO,CAAC;iBACf,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACb,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;gBACrD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1C,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,CAAA;gBAC5B,CAAC;YACH,CAAC,CAAC,CAAA;QACN,CAAC;QAED,qCAAqC;QACrC,MAAM,KAAK,GAAG,QAAQ;aACnB,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aAClC,MAAM,CAAC,OAAO,CAAC,CAAA;QAElB,MAAM,YAAY,GAAG,KAAK;aACvB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACnD,IAAI,CAAC,IAAI,CAAC,CAAA;QAEb,OAAO,GAAG,MAAM,IAAI,YAAY,GAAG,CAAA;IACrC,CAAC;IAED;;;OAGG;IACH,SAAS,WAAW,CAAC,IAAgB;QACnC,6DAA6D;QAC7D,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvC,WAAW,CAAC,KAAK,CAAC,CAAA;QACpB,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAA;QACnC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC;YAAE,OAAM;QAI/B,MAAM,MAAM,GAAY,EAAE,CAAA;QAE1B,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;YACjE,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACpD,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YAE1B,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,CAAA;gBACnC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAClB,CAAC;YACD,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACrB,CAAC;QAED,yDAAyD;QACzD,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;gBAAE,SAAQ;YAElC,qBAAqB;YACrB,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAA;YACpE,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;YACxB,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YAC1C,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAA;YAEnE,wCAAwC;YACxC,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;YACtD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;YAC9C,MAAM,SAAS,GAAG,MAAM,GAAG,YAAY,CACrC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACf,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,CAC1C,CACF,CAAA;YAED,qDAAqD;YACrD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBACjC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAC7B,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,QAAQ,CAChB,CAAA;gBACD,KAAK,CAAC,IAAI,CAAC;oBACT,KAAK,EAAE,MAAM,CAAC,UAAU;oBACxB,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ;oBACvC,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;gBACF,SAAQ;YACV,CAAC;YAED,sDAAsD;YACtD,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAA;YACpC,MAAM,SAAS,GAAa,EAAE,CAAA;YAE9B,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;gBACzB,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;oBACpC,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAA;oBACtD,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAA;oBAC1B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC7B,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;wBACvB,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;wBAC9C,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA;oBACrC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,gDAAgD;YAChD,MAAM,SAAS,GAAG,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAA;YAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CACzB,SAAS,CAAC,QAAQ,EAClB,MAAM,CAAC,QAAQ,CAChB,CAAA;YAED,MAAM,WAAW,GACf,SAAS;gBACT,IAAI;gBACJ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;gBACpB,IAAI;gBACJ,MAAM,CAAA;YAER,KAAK,CAAC,IAAI,CAAC;gBACT,KAAK,EAAE,MAAM,CAAC,UAAU;gBACxB,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ;gBACvC,WAAW,EAAE,WAAW;aACzB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,2CAA2C;IAC3C,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAE1B,mEAAmE;IACnE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;IACvC,IAAI,KAAK,GAAG,MAAM,CAAA;IAClB,KAAK,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAE,IAAI,KAAK,EAAE,CAAC;QAChD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAChE,CAAC;IAED,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;AAChC,CAAC;AAED,yDAA0C;AAG1C,MAAa,SAAS;IAGS;IAFrB,WAAW,GAAG,IAAI,GAAG,EAA2B,CAAA;IAExD,YAA6B,mBAAiD,cAAc;QAA/D,qBAAgB,GAAhB,gBAAgB,CAA+C;IAAI,CAAC;IAEjG;;;OAGG;IACH,KAAK,CAAC,UAAU,CACd,QAAgB,EAChB,OAAe,EACf,IAAI,GAAG,YAAY,EACnB,kBAA4C,EAAE;QAE9C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACtC,MAAM,MAAM,GAAG,OAAO,CAAA;QACtB,IAAI,KAAK,GAAG,MAAM,CAAA;QAElB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAAA;QAC7C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YACnC,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;QAChD,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAC5C,IAAI,UAAU,GAAoB,MAAM,IAAI,EAAE,CAAA;QAE9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC;gBACX,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA;gBAC7E,UAAU,GAAG,QAAQ,IAAI,EAAE,CAAA;YAC7B,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,IAAI,CAAC,yCAAyC,QAAQ,GAAG,EAAE,UAAU,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;YACnI,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QAC3C,CAAC;QAED,MAAM,OAAO,GAAoB;YAC/B,MAAM;YACN,GAAG,UAAU;YACb,GAAG,eAAe;SACnB,CAAA;QAED,IAAI,CAAC;YACT,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACvD,CAAC;QAAC,OAAO,SAAS,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,CACV,kCAAkC,QAAQ,6CAA6C,EACvF,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAC3D,CAAA;YACD,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACrC,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;IAChD,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,OAAO,OAAO;aACX,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;aACtB,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;aACpB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;aACxB,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;aAC1B,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;IACxB,CAAC;IAEO,SAAS,CAAC,IAAY;QAC5B,MAAM,GAAG,GAA2B;YAClC,UAAU,EAAE,YAAY;YACxB,GAAG,EAAE,YAAY;YACjB,UAAU,EAAE,OAAO;YACnB,GAAG,EAAE,OAAO;YACZ,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,MAAM;YACb,OAAO,EAAE,SAAS;YAClB,QAAQ,EAAE,UAAU;YACpB,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,KAAK;YACV,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,MAAM;SACZ,CAAA;QACD,OAAO,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,OAAO,CAAA;IAC3C,CAAC;IAEO,SAAS,CAAC,IAAY,EAAE,MAAc,EAAE,KAAa;QAC3D,OAAO;YACL,IAAI;YACJ,MAAM;YACN,KAAK;YACL,OAAO,EAAE,MAAM,KAAK,KAAK;SAC1B,CAAA;IACH,CAAC;CACF;AApGD,8BAoGC;AAED,yBAAyB;AACzB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,CAAC,KAAK,IAAI,EAAE;QACV,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAC9C,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;QAEhD,iBAAiB;QACjB,mFAAmF;QACnF,+CAA+C;IACjD,CAAC,CAAC,EAAE,CAAC;AACP,CAAC", "sourcesContent": ["// ai-universal-code-editor.ts\nimport * as vscode from 'vscode';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { createPatch, applyPatch, parsePatch, ParsedDiff } from 'diff';\nimport * as readline from 'readline';\nimport * as fsPromises from 'fs/promises';\nimport * as ParserModule from 'web-tree-sitter';\nimport { string } from 'zod';\nimport type * as Prettier from 'prettier';\n\n\n// Re-export types from web-tree-sitter\ntype Parser = ParserModule.Parser;\ntype Language = ParserModule.Language;\ntype SyntaxNode = ParserModule.SyntaxNode;\ntype Tree = ParserModule.Tree;\ntype TreeCursor = ParserModule.TreeCursor;\ntype Point = ParserModule.Point;\ntype Range = ParserModule.Range;\ntype Edit = ParserModule.Edit;\ntype Query = ParserModule.Query;\ntype QueryMatch = ParserModule.QueryMatch;\ntype QueryCapture = ParserModule.QueryCapture;\ntype Logger = ParserModule.Logger;\n\n// Initialize the parser\nlet isParserInitialized = false;\n\nasync function ensureParserInitialized(): Promise<void> {\n  if (!isParserInitialized) {\n    await ParserModule.init();\n    isParserInitialized = true;\n  }\n}\n\n// Global parser instance\nlet parser: Parser | null = null;\nlet language: Language | null = null;\n\n// Type declarations for optional dependencies\ntype Escodegen = {\n  generate: (ast: any, options?: any) => string;\n};\ntype LezerPython = {\n  parse: (input: string) => any;\n};\ntype Prettier = {\n  format: (code: string, options?: any) => Promise<string>;\n};\n\n// Try to import optional dependencies\nlet escodegen: Escodegen | undefined;\nlet lezerPython: LezerPython | undefined;\nlet prettier: Prettier | undefined;\n\ntry {\n  // @ts-ignore - Dynamic import for optional dependencies\n  escodegen = await import('escodegen');\n} catch (e) {\n  console.warn('escodegen not available, some features may be limited');\n}\n\ntry {\n  // @ts-ignore - Dynamic import for optional dependencies\n  lezerPython = await import('lezer-python');\n} catch (e) {\n  console.warn('lezer-python not available, Python support will be limited');\n}\n\ntry {\n  // @ts-ignore - Dynamic import for optional dependencies\n  prettier = await import('prettier');\n} catch (e) {\n  console.warn('prettier not available, code formatting will be limited');\n}\n\n// === Core Types ===\n\n/**\n * Represents an edit of a source file.\n */\nexport interface FileEdit {\n  path: string;\n  before: string;\n  after: string;\n  applied?: boolean;\n  error?: string;\n  diff?: string;\n}\n\n/**\n * Text edit operation\n */\nexport interface TextEdit {\n  range: {\n    start: { line: number; character: number };\n    end: { line: number; character: number };\n  };\n  newText: string;\n}\n\n/**\n * Formats a file using Prettier if available, otherwise falls back to basic formatting\n * @param filePath Path to the file\n * @param content File content to format\n * @param lang Language of the file (default: 'typescript')\n * @returns Promise resolving to a FileEdit with the formatting changes\n */\nasync function formatFile(\n  filePath: string,\n  content: string,\n  lang: string = 'typescript'\n): Promise<FileEdit> {\n  try {\n    let formatted = content;\n\n    if (prettier) {\n      try {\n        formatted = await prettier.format(content, {\n          parser: lang,\n          singleQuote: true,\n          trailingComma: 'es5',\n        });\n      } catch (error) {\n        console.warn('Prettier formatting failed, falling back to basic formatting', error);\n      }\n    }\n\n    // Basic formatting if prettier fails or isn't available\n    if (formatted === content) {\n      formatted = content\n        .replace(/\\r\\n/g, '\\n')  // Normalize line endings\n        .replace(/\\s+\\n/g, '\\n') // Remove trailing whitespace\n        .trim() + '\\n';          // Ensure single trailing newline\n    }\n\n    return {\n      path: filePath,\n      before: content,\n      after: formatted,\n      applied: true,\n      diff: DiffUtil.unifiedDiff(content, formatted, filePath)\n    };\n  } catch (error) {\n    return {\n      path: filePath,\n      before: content,\n      after: content,\n      applied: false,\n      error: error instanceof Error ? error.message : 'Unknown error formatting file',\n      diff: ''\n    };\n  }\n}\n\n/**\n * Normalizes whitespace in code for consistent comparison\n * @param content The content to normalize\n * @returns Normalized content\n */\nfunction normalizeWhitespace(content: string): string {\n  return content\n    .replace(/\\r\\n/g, '\\n')  // Normalize line endings\n    .replace(/[^\\S\\n]+\\n/g, '\\n')  // Remove trailing whitespace\n    .replace(/\\n{3,}/g, '\\n\\n')  // Normalize multiple newlines\n    .trim() + '\\n';  // Ensure single trailing newline\n}\n\ninterface Plan {\n  id?: string;\n  timestamp?: string;\n  edits: FileEdit[];\n}\n\n// === Workspace ===\nexport class Workspace {\n  root: string;\n  includePatterns: string[];\n  excludePatterns: string[];\n\n  constructor(root: string, includePatterns: string[] = ['**/*.*'], excludePatterns: string[] = ['**/node_modules/**', '**/.git/**']) {\n    this.root = path.resolve(root);\n    this.includePatterns = includePatterns;\n    this.excludePatterns = excludePatterns;\n  }\n\n  async findFiles(include: string[] = this.includePatterns, exclude: string[] = this.excludePatterns): Promise<string[]> {\n    const uris = await vscode.workspace.findFiles(\n      `{${include.join(',')}}`,\n      `{${exclude.join(',')}}`\n    );\n    return uris.map(u => u.fsPath);\n  }\n\n  async listFiles(include: string[] = this.includePatterns, exclude: string[] = this.excludePatterns): Promise<string[]> {\n    return this.findFiles(include, exclude);\n  }\n\n  async readFile(filePath: string): Promise<string> {\n    return await fsPromises.readFile(filePath, 'utf-8');\n  }\n\n  async writeFile(filePath: string, content: string): Promise<void> {\n    await fsPromises.mkdir(path.dirname(filePath), { recursive: true });\n    await fsPromises.writeFile(filePath, content, 'utf-8');\n  }\n\n  async backupFile(filePath: string): Promise<string> {\n    const backup = `${filePath}.bak`;\n    const content = await this.readFile(filePath);\n    await this.writeFile(backup, content);\n    return backup;\n  }\n}\n\n// === Diff Utility ===\ninterface ApplyPatchOptions {\n  fuzzFactor?: number;\n  compareLine?: (line: string, hunk: string) => boolean;\n}\n\nexport class DiffUtil {\n  static unifiedDiff(before: string, after: string, file: string): string {\n    return createPatch(\n      file,\n      before,\n      after,\n      'Original',\n      'Modified',\n      '',  // Required empty string as 6th parameter\n      { context: 3 }\n    );\n  }\n\n  static applyPatch(content: string, patch: string, options: ApplyPatchOptions = {}): string {\n    const parsedPatch = parsePatch(patch);\n    if (!parsedPatch || !Array.isArray(parsedPatch)) {\n      throw new Error('Invalid patch format');\n    }\n\n    let result = content;\n\n    for (const p of parsedPatch) {\n      // Create a patch string from the parsed diff\n      const patchStr = createPatch(\n        p.oldFileName || 'file',\n        p.oldHeader || '',\n        p.newHeader || '',\n        p.hunks.flatMap(h => [\n          `@@ -${h.oldStart},${h.oldLines} +${h.newStart},${h.newLines} @@`,\n          ...h.lines\n        ]).join('\\n')\n      );\n\n      // Apply the patch with proper options\n      const patchOptions = {\n        compareLine: options.compareLine || ((line: string, hunk: string) => line.trim() === hunk.trim()),\n        fuzzFactor: options.fuzzFactor || 0\n      };\n\n      const patchResult = applyPatch(result, patchStr, patchOptions);\n\n      if (typeof patchResult === 'string') {\n        result = patchResult;\n      }\n    }\n\n    return result;\n  }\n\n  static diffLines(before: string, after: string): string {\n    // formatFile implementation moved below for now\n    const beforeLines = before.split('\\n');\n    const afterLines = after.split('\\n');\n\n    const diff: string[] = [];\n    const maxLength = Math.max(beforeLines.length, afterLines.length);\n\n    for (let i = 0; i < maxLength; i++) {\n      if (i >= beforeLines.length) {\n        diff.push(`+ ${afterLines[i]}`);\n      } else if (i >= afterLines.length) {\n        diff.push(`- ${beforeLines[i]}`);\n      } else if (beforeLines[i] !== afterLines[i]) {\n        diff.push(`- ${beforeLines[i]}`);\n        diff.push(`+ ${afterLines[i]}`);\n      } else {\n        diff.push(`  ${beforeLines[i]}`);\n      }\n    }\n\n    return diff.join('\\n');\n  }\n}\n\n// === Code Search ===\ninterface SearchOptions {\n  caseSensitive?: boolean;\n  wholeWord?: boolean;\n  regex?: boolean;\n  includeNodeModules?: boolean;\n  maxResults?: number;\n  filePatterns?: string[];\n}\n\ntype SearchResult = {\n  file: string;\n  line: number;\n  lineText: string;\n  match: string;\n  startColumn: number;\n  endColumn: number;\n};\n\nexport class CodeSearch {\n  private workspace: Workspace;\n  private filePatterns: string[];\n  private excludePatterns: string[];\n\n  constructor(workspace: Workspace) {\n    this.workspace = workspace;\n    this.filePatterns = ['**/*.{ts,tsx,js,jsx,json,md}'];\n    this.excludePatterns = ['**/node_modules/**', '**/.git/**', '**/dist/**'];\n  }\n\n  async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {\n    const results: SearchResult[] = [];\n    const files = await this.workspace.findFiles(\n      options.filePatterns || this.filePatterns,\n      this.excludePatterns\n    );\n\n    const searchRegex = this.createSearchRegex(query, options);\n\n    for (const file of files) {\n      try {\n        const content = await this.workspace.readFile(file);\n        const lines = content.split('\\n');\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i];\n          let match: RegExpExecArray | null;\n\n          while ((match = searchRegex.exec(line)) !== null) {\n            results.push({\n              file,\n              line: i + 1,\n              lineText: line,\n              match: match[0],\n              startColumn: match.index + 1,\n              endColumn: match.index + match[0].length + 1\n            });\n\n            // Apply maxResults if specified\n            if (options.maxResults && results.length >= options.maxResults) {\n              return results;\n            }\n          }\n        }\n      } catch (error) {\n        console.warn(`Error searching file ${file}:`, error);\n      }\n    }\n\n    return results;\n  }\n\n  private createSearchRegex(query: string, options: SearchOptions): RegExp {\n    let pattern = options.regex ? query : this.escapeRegExp(query);\n\n    if (options.wholeWord) {\n      pattern = `\\\\b${pattern}\\\\b`;\n    }\n\n    const flags = options.caseSensitive ? 'g' : 'gi';\n    return new RegExp(pattern, flags);\n  }\n\n  private escapeRegExp(string: string): string {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n  }\n}\n\n// === AST Utilities ===\nexport interface ASTNode {\n  type: string;\n  text: string;\n  startPosition: { row: number, column: number };\n  endPosition: { row: number, column: number };\n  children: ASTNode[];\n}\nexport class ASTUtil {\n  static traverse(node: any): ASTNode { return { type: node.type, text: node.text, startPosition: node.startPosition, endPosition: node.endPosition, children: node.children ? node.children.map((c: any) => ASTUtil.traverse(c)) : [] }; }\n  static findNodes(node: ASTNode, type: string): ASTNode[] { const out: ASTNode[] = []; if (node.type === type) out.push(node); for (const c of node.children) out.push(...ASTUtil.findNodes(c, type)); return out; }\n}\n\n// === Code Block Manipulation ===\nexport class CodeBlockUtil {\n  static moveBlock(lines: string[], start: number, end: number, target: number): string[] { const block = lines.slice(start, end); const rest = [...lines.slice(0, start), ...lines.slice(end)]; rest.splice(target, 0, ...block); return rest; }\n  static duplicateBlock(lines: string[], start: number, end: number, target: number): string[] { const block = lines.slice(start, end); const newLines = [...lines]; newLines.splice(target, 0, ...block); return newLines; }\n  static deleteBlock(lines: string[], start: number, end: number): string[] { const newLines = [...lines]; newLines.splice(start, end - start); return newLines; }\n}\n\n// === Large File Utilities ===\nexport class LargeFileUtil {\n  static async processLines(filePath: string, callback: (line: string, lineNum: number) => Promise<void> | void): Promise<void> {\n    const rl = readline.createInterface({\n      input: fs.createReadStream(filePath)\n    });\n\n    let lineNum = 0;\n    for await (const line of rl) {\n      await callback(line, lineNum);\n      lineNum++;\n    }\n  }\n\n  static async readChunks(filePath: string, chunkSize: number = 1024 * 1024): Promise<string[]> {\n    const stats = await fs.promises.stat(filePath);\n    const total = stats.size;\n    const chunks: string[] = [];\n    let offset = 0;\n\n    const fd = await fs.promises.open(filePath, 'r');\n    const buffer = Buffer.alloc(chunkSize);\n\n    try {\n      while (offset < total) {\n        const { bytesRead } = await fd.read(buffer, 0, chunkSize, offset);\n        if (bytesRead === 0) break;\n        chunks.push(buffer.toString('utf8', 0, bytesRead));\n        offset += bytesRead;\n      }\n      return chunks;\n    } finally {\n      await fd.close();\n    }\n  }\n}\n// === Symbol Indexing ===\nexport type SymbolType = 'function' | 'class' | 'variable' | 'import' | 'interface' | 'type' | 'const' | 'let';\n\nexport interface SymbolEntry {\n  file: string;\n  name: string;\n  type: SymbolType;\n  line: number;\n  startLine: number;\n  endLine: number;\n  language?: string;\n  exportName?: string;\n}\n\nexport class SymbolIndexer {\n  private index: Map<string, SymbolEntry[]> = new Map();\n  private symbols: Map<string, SymbolEntry> = new Map();\n  private symbolPatterns: { regex: RegExp, type: SymbolType }[] = [\n    { regex: /^(?:export\\s+)?(?:async\\s+)?function\\s+([a-zA-Z_$][\\w$]*)\\s*\\(/m, type: 'function' },\n    { regex: /^export\\s+default\\s+function\\s+([a-zA-Z_$][\\w$]*)\\s*\\(/m, type: 'function' },\n    { regex: /^(?:export\\s+)?class\\s+([A-Z][a-zA-Z0-9_$]*)\\b/m, type: 'class' },\n    { regex: /^export\\s+default\\s+class\\s+([A-Z][a-zA-Z0-9_$]*)\\b/m, type: 'class' },\n    { regex: /^import\\s+(?:[\\s\\S]*?\\s+from\\s+)?['\"]([^'\"]+)['\"]/m, type: 'import' },\n    { regex: /^(?:export\\s+)?interface\\s+([A-Z][a-zA-Z0-9_$]*)\\b/m, type: 'interface' },\n    { regex: /^(?:export\\s+)?type\\s+([A-Z][a-zA-Z0-9_$]*)\\s*=/m, type: 'type' },\n    { regex: /^export\\s+(?:const|let|var)\\s+([a-zA-Z_$][\\w$]*)/m, type: 'variable' },\n    { regex: /^const\\s+([a-zA-Z_$][\\w$]*)\\s*=/m, type: 'const' },\n    { regex: /^let\\s+([a-zA-Z_$][\\w$]*)\\s*=/m, type: 'let' },\n    { regex: /^var\\s+([a-zA-Z_$][\\w$]*)\\s*=/m, type: 'variable' }\n  ];\n\n  constructor() {\n    this.clear();\n  }\n\n  clear(): void {\n    this.index.clear();\n    this.symbols.clear();\n  }\n\n  getFiles(): string[] {\n    return Array.from(this.index.keys());\n  }\n\n  getSymbolsInFile(file: string): SymbolEntry[] {\n    return this.index.get(file) || [];\n  }\n\n  async indexFile(file: string, content: string): Promise<void> {\n    const entries: SymbolEntry[] = [];\n    const lines = content.split('\\n');\n\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i];\n\n      for (const { regex, type } of this.symbolPatterns) {\n        const match = line.match(regex);\n        if (match && match[1]) {\n          const entry: SymbolEntry = {\n            file,\n            name: match[1],\n            type,\n            line: i + 1,\n            startLine: i + 1,\n            endLine: i + 1\n          };\n          entries.push(entry);\n          this.symbols.set(entry.name, entry);\n        }\n      }\n    }\n\n    this.index.set(file, entries);\n  }\n\n  find(symbol: string, exactMatch: boolean = false): SymbolEntry[] {\n    const results: SymbolEntry[] = [];\n    const searchTerm = exactMatch\n      ? symbol\n      : symbol.toLowerCase();\n\n    for (const entry of this.symbols.values()) {\n      const match = exactMatch\n        ? entry.name === searchTerm\n        : entry.name.toLowerCase().includes(searchTerm);\n\n      if (match) {\n        results.push(entry);\n      }\n    }\n\n    return results;\n  }\n}\n\nexport class AIEditor {\n  private workspace: Workspace;\n  private symbolIndexer: SymbolIndexer;\n  private searcher: CodeSearch;\n  private bridge: any;\n  private prettier: any;\n  private _applied: number = 0;\n  private _backups: string[] = [];\n  private _diff: string = '';\n  private planner: {\n    renameSymbol: (path: string, oldName: string, newName: string, lang: string) => Promise<FileEdit>;\n    extractFunction: (path: string, start: number, end: number, name: string, lang: string) => Promise<FileEdit>;\n    moveLines: (path: string, start: number, end: number, to: number, lang: string) => Promise<FileEdit>;\n    applyPlan: (plan: Plan) => Promise<{ applied: number; backups: string[]; diff: string }>;\n  };\n\n  constructor(root: string) {\n    this.workspace = new Workspace(root);\n    this.symbolIndexer = new SymbolIndexer();\n    this.searcher = new CodeSearch(this.workspace);\n    this.bridge = {};\n    this.prettier = null;\n\n    // Initialize planner methods\n    this.planner = {\n      renameSymbol: async (filePath: string, oldName: string, newName: string, lang: string = 'typescript'): Promise<FileEdit> => {\n        return this.renameSymbol(filePath, oldName, newName, lang);\n      },\n      extractFunction: async (filePath: string, startLine: number, endLine: number, funcName: string, lang: string = 'typescript'): Promise<FileEdit> => {\n        return this.extractFunction(filePath, startLine, endLine, funcName, lang);\n      },\n      moveLines: async (filePath: string, startLine: number, endLine: number, toLine: number, lang: string = 'typescript'): Promise<FileEdit> => {\n        return this.moveLines(filePath, startLine, endLine, toLine, lang);\n      },\n      applyPlan: async (plan: Plan): Promise<{ applied: number; backups: string[]; diff: string }> => {\n        return this.applyPlan(plan);\n      }\n    };\n  }\n\n  // Getters for private properties\n  get applied(): number {\n    return this._applied;\n  }\n\n  get backups(): string[] {\n    return [...this._backups];\n  }\n\n  get diff(): string {\n    return this._diff;\n  }\n\n  private getParser(): Parser | null {\n    return parser;\n  }\n\n  private getLanguage(): Language | null {\n    return language;\n  }\n\n  async renameSymbol(\n    filePath: string,\n    oldName: string,\n    newName: string,\n    lang: string = 'typescript'\n  ): Promise<FileEdit> {\n    const fileContent = await this.workspace.readFile(filePath);\n\n    if (!parser || !language) {\n      throw new Error('Parser not initialized. Call initParser() first.');\n    }\n\n    try {\n      // Parse the file\n      const tree = parser.parse(fileContent);\n\n      // Find all occurrences of the symbol\n      const query = language.query(`(identifier) @id (#eq? @id \"${oldName}\")`);\n      const matches = query.matches(tree.rootNode);\n\n      if (matches.length === 0) {\n        return {\n          path: filePath,\n          before: fileContent,\n          after: fileContent,\n          applied: false,\n          error: `Symbol '${oldName}' not found in file`\n        };\n      }\n\n      // Apply replacements\n      let newContent = fileContent;\n      let offset = 0;\n\n      for (const match of matches) {\n        for (const capture of match.captures) {\n          const { startIndex, endIndex } = capture.node;\n          const before = newContent.substring(0, startIndex + offset);\n          const after = newContent.substring(endIndex + offset);\n          newContent = before + newName + after;\n          offset += newName.length - (endIndex - startIndex);\n        }\n      }\n\n      return {\n        path: filePath,\n        before: fileContent,\n        after: newContent,\n        applied: true,\n        diff: DiffUtil.unifiedDiff(fileContent, newContent, filePath)\n      };\n    } catch (error) {\n      return {\n        path: filePath,\n        before: fileContent,\n        after: fileContent,\n        applied: false,\n        error: error instanceof Error ? error.message : 'Unknown error during symbol rename'\n      };\n    }\n  }\n\n  private async extractFunction(\n    filePath: string,\n    startLine: number,\n    endLine: number,\n    funcName: string,\n    lang: string = 'typescript'\n  ): Promise<FileEdit> {\n    try {\n      const fileContent = await this.workspace.readFile(filePath);\n      const lines = fileContent.split('\\n');\n\n      // Get the range of the code to extract\n      const startIndex = lines.slice(0, startLine - 1).join('\\n').length + 1;\n      const endIndex = lines.slice(0, endLine).join('\\n').length;\n      const extractedCode = fileContent.substring(startIndex, endIndex).trim();\n\n      // Create the new function\n      const newFunction = `function ${funcName}() {\n  ${extractedCode}\n}`;\n\n      // Replace the extracted code with a function call\n      const newContent = [\n        fileContent.substring(0, startIndex),\n        `${funcName}();`,\n        fileContent.substring(endIndex)\n      ].join('\\n');\n\n      return {\n        path: filePath,\n        before: fileContent,\n        after: newContent,\n        applied: true,\n        diff: DiffUtil.unifiedDiff(fileContent, newContent, filePath)\n      };\n    } catch (error) {\n      return {\n        path: filePath,\n        before: '',\n        after: '',\n        applied: false,\n        error: error instanceof Error ? error.message : 'Failed to extract function'\n      };\n    }\n  }\n\n  private async moveLines(\n    filePath: string,\n    startLine: number,\n    endLine: number,\n    toLine: number,\n    lang: string = 'typescript'\n  ): Promise<FileEdit> {\n    const fileContent = await this.workspace.readFile(filePath);\n    const lines = fileContent.split('\\n');\n\n    // Ensure valid line numbers\n    if (startLine < 1 || endLine > lines.length || toLine < 1 || toLine > lines.length + 1) {\n      return {\n        path: filePath,\n        before: fileContent,\n        after: fileContent,\n        applied: false,\n        error: 'Invalid line numbers provided'\n      };\n    }\n\n    // Adjust for 0-based indexing\n    const start = startLine - 1;\n    const end = endLine;\n    const target = toLine - 1;\n\n    // Move the lines\n    const movingLines = lines.slice(start, end);\n    const newLines = [...lines];\n    newLines.splice(start, end - start);\n\n    // Calculate new target position after removing the lines\n    const newTarget = target > start ? target - (end - start) : target;\n    newLines.splice(newTarget, 0, ...movingLines);\n\n    const newContent = newLines.join('\\n');\n\n    return {\n      path: filePath,\n      before: fileContent,\n      after: newContent,\n      applied: true,\n      diff: DiffUtil.unifiedDiff(fileContent, newContent, filePath)\n    };\n  }\n\n  private async applyPlan(plan: Plan): Promise<{ applied: number; backups: string[]; diff: string }> {\n    let applied = 0;\n    const backups: string[] = [];\n    let diff = '';\n\n    for (const edit of plan.edits) {\n      try {\n        // Create backup\n        const backup = await this.workspace.backupFile(edit.path);\n        backups.push(backup);\n\n        // Apply the edit\n        await this.workspace.writeFile(edit.path, edit.after);\n        applied++;\n\n        // Generate diff\n        if (edit.diff) {\n          diff += edit.diff;\n        } else {\n          const fileDiff = DiffUtil.unifiedDiff(edit.before, edit.after, edit.path);\n          diff += fileDiff;\n        }\n      } catch (error) {\n        console.error(`Failed to apply edit to ${edit.path}:`, error);\n        // Continue with next edit even if one fails\n      }\n    }\n\n    return {\n      applied,\n      backups,\n      diff\n    };\n  }\n\n  private async initParser(langWasmPath: string): Promise<void> {\n    try {\n      // Initialize the parser if not already done\n      await ensureParserInitialized();\n\n      // Create a new parser instance\n      parser = new ParserModule.Parser();\n\n      // Load the language wasm file\n      const wasm = await fsPromises.readFile(langWasmPath);\n\n      // Create a blob URL for the wasm file\n      const blob = new Blob([wasm], { type: 'application/wasm' });\n      const url = URL.createObjectURL(blob);\n\n      try {\n        // Load the language using the Parser.Language.load method\n        language = await (ParserModule as any).Language.load(url);\n        \n        // Set the language for the parser\n        if (parser && language) {\n          parser.setLanguage(language);\n        }\n      } finally {\n        // Clean up the blob URL\n        URL.revokeObjectURL(url);\n      }\n    } catch (error) {\n      console.error('Failed to initialize parser:', error);\n      throw error;\n    }\n  }\n\n  private normalizeWhitespace(content: string): string {\n    if (!content) return '';\n    return content\n      .replace(/\\r\\n/g, '\\n')  // Convert Windows line endings to Unix\n      .replace(/\\r/g, '\\n')     // Convert old Mac line endings to Unix\n      .replace(/[ \\t]+$/gm, '')  // Remove trailing whitespace from each line\n      .replace(/\\n{3,}/g, '\\n\\n') // Normalize multiple empty lines to max 2\n      .replace(/\\s+$/, '');     // Remove trailing whitespace at the end of file\n  }\n}\n\n/**\n * Minimal AST-node interface capturing only the properties we use.\n */\ninterface Node {\n  namedChildren: Node[];\n  startIndex: number;\n  endIndex: number;\n}\n\n/**\n * Reads, parses, deduplicates, and merges near-duplicate definitions\n * in *any* programming language (via a Tree-sitter .wasm grammar).\n */\nexport async function removeDuplicates(\n  path: string,\n  langId: string,\n  wasmPath: string,\n  workspace: { readFile(path: string): Promise<string> }\n): Promise<FileEdit> {\n  // --- Parser & Grammar Caching (module-scope singletons) ---\n  type ParserCache = {\n    parser: Parser\n    grammars: Map<string, Language>\n  }\n  const cacheKey = '__TREE_SITTER_CACHE__'\n  const globalAny = globalThis as any\n\n  if (!globalAny[cacheKey]) {\n    await ParserModule.init();\n    globalAny[cacheKey] = {\n      parser: new ParserModule.Parser(),\n      grammars: new Map<string, ParserModule.Language>()\n    } as ParserCache;\n  }\n\n  const { parser, grammars } = globalAny[cacheKey] as ParserCache;\n\n  // --- Load or reuse the requested language grammar ---\n  if (!grammars.has(langId)) {\n    try {\n      // Initialize the parser if not already done\n      if (!globalAny[cacheKey]?.initialized) {\n        await ParserModule.init();\n        globalAny[cacheKey] = { ...globalAny[cacheKey], initialized: true };\n      }\n      \n// Load the language grammar\nconst lang = await (ParserModule as any).Language.load(wasmPath);\n      grammars.set(langId, lang);\n    } catch (err) {\n      throw new Error(\n        `Failed to load Tree-sitter grammar for \"${langId}\" from \"${wasmPath}\":\\n  ${err}`\n      );\n    }\n  }\n  parser.setLanguage(grammars.get(langId)!)\n\n  // --- Read & parse the source file ---\n  let before: string\n  try {\n    before = await workspace.readFile(path)\n  } catch (err) {\n    throw new Error(`Unable to read file \"${path}\":\\n  ${err}`)\n  }\n\n  const tree = parser.parse(before)\n  const edits: { start: number; end: number; replacement: string }[] = []\n\n  // --- Jaccard-based fuzzy header matcher ---\n  function fuzzyMatch(a: string, b: string, threshold = 0.6): boolean {\n    const tokenize = (s: string) =>\n      new Set(s.toLowerCase().split(/\\W+/).filter(Boolean))\n    const A = tokenize(a), B = tokenize(b)\n    const intersection = [...A].filter((w) => B.has(w)).length\n    const union = new Set([...A, ...B]).size\n    return union > 0 && intersection / union >= threshold\n  }\n\n  // --- Merge default parameter values across headers ---\n  function mergeHeaders(headers: string[]): string {\n    // Use first header as template\n    const tmpl = headers[0]\n    const m = tmpl.match(/^([^(]+)\\(([^)]*)\\)/)\n    if (!m) return tmpl\n\n    const [, fnName, paramStr] = m\n    const map = new Map<string, string | null>()\n\n    // Build name→default map, preferring any explicit defaults\n    for (const hdr of headers) {\n      const pm = hdr.match(/^[^(]+\\(([^)]*)\\)/)\n      if (!pm) continue\n      pm[1]\n        .split(',')\n        .map((p) => p.trim())\n        .filter(Boolean)\n        .forEach((p) => {\n          const [name, def] = p.split('=').map((s) => s.trim())\n          if (!map.has(name) || (def && def.length)) {\n            map.set(name, def ?? null)\n          }\n        })\n    }\n\n    // Re-serialize in the original order\n    const order = paramStr\n      .split(',')\n      .map((p) => p.trim().split('=')[0])\n      .filter(Boolean)\n\n    const mergedParams = order\n      .map((n) => (map.get(n) ? `${n}=${map.get(n)}` : n))\n      .join(', ')\n\n    return `${fnName}(${mergedParams})`\n  }\n\n  /**\n   * Recursively traverses `node` to find sibling groups of\n   * (near-)duplicates, merges them, and records text-replacements.\n   */\n  function processNode(node: SyntaxNode) {\n    // Recurse into all children first (handle nested duplicates)\n    for (const child of node.namedChildren) {\n      processNode(child)\n    }\n\n    const siblings = node.namedChildren\n    if (siblings.length < 2) return\n\n    // Group siblings by fuzzy header signature\n    type Group = { signature: string; nodes: SyntaxNode[] }\n    const groups: Group[] = []\n\n    for (const sib of siblings) {\n      const firstChild = sib.firstNamedChild;\n      const hdrEnd = firstChild ? firstChild.startIndex : sib.endIndex;\n      const rawHdr = before.slice(sib.startIndex, hdrEnd);\n      const sig = rawHdr.trim();\n\n      let grp = groups.find((g) => fuzzyMatch(g.signature, sig));\n      if (!grp) {\n        grp = { signature: sig, nodes: [] }\n        groups.push(grp)\n      }\n      grp.nodes.push(sib)\n    }\n\n    // For each duplicate group, compose a merged replacement\n    for (const grp of groups) {\n      if (grp.nodes.length < 2) continue\n\n      // Sort by file order\n      const sorted = grp.nodes.sort((a, b) => a.startIndex - b.startIndex)\n      const anchor = sorted[0]\n      const firstChild = anchor.namedChildren[0]\n      const hdrEnd = firstChild ? firstChild.startIndex : anchor.endIndex\n\n      // Preserve indentation + unified header\n      const rawHdr = before.slice(anchor.startIndex, hdrEnd)\n      const indent = rawHdr.match(/^\\s*/)?.[0] ?? ''\n      const mergedHdr = indent + mergeHeaders(\n        sorted.map((n) =>\n          before.slice(n.startIndex, hdrEnd).trim()\n        )\n      )\n\n      // If it's a leaf (e.g. import), keep the single line\n      if (!anchor.namedChildren.length) {\n        const singleLine = before.slice(\n          anchor.startIndex,\n          anchor.endIndex\n        )\n        edits.push({\n          start: anchor.startIndex,\n          end: sorted[sorted.length - 1].endIndex,\n          replacement: singleLine\n        })\n        continue\n      }\n\n      // Merge all unique body fragments in appearance order\n      const seenBodies = new Set<string>()\n      const bodyLines: string[] = []\n\n      for (const dup of sorted) {\n        for (const sub of dup.namedChildren) {\n          const raw = before.slice(sub.startIndex, sub.endIndex)\n          const trimmed = raw.trim()\n          if (!seenBodies.has(trimmed)) {\n            seenBodies.add(trimmed)\n            const subIndent = raw.match(/^\\s*/)?.[0] ?? ''\n            bodyLines.push(subIndent + trimmed)\n          }\n        }\n      }\n\n      // Preserve trailing tokens (braces, semicolons)\n      const lastChild = anchor.namedChildren.at(-1)!\n      const footer = before.slice(\n        lastChild.endIndex,\n        anchor.endIndex\n      )\n\n      const mergedBlock =\n        mergedHdr +\n        '\\n' +\n        bodyLines.join('\\n') +\n        '\\n' +\n        footer\n\n      edits.push({\n        start: anchor.startIndex,\n        end: sorted[sorted.length - 1].endIndex,\n        replacement: mergedBlock\n      })\n    }\n  }\n\n  // Kick-off the recursive merge at the root\n  processNode(tree.rootNode)\n\n  // Apply all edits in **reverse** order to maintain correct offsets\n  edits.sort((a, b) => b.start - a.start)\n  let after = before\n  for (const { start, end, replacement } of edits) {\n    after = after.slice(0, start) + replacement + after.slice(end)\n  }\n\n  return { path, before, after }\n}\n\nimport * as prettierModule from 'prettier'\nimport { Options as PrettierOptions } from 'prettier'\n\nexport class Formatter {\n  private configCache = new Map<string, PrettierOptions>()\n\n  constructor(private readonly prettierInstance: typeof prettierModule | null = prettierModule) { }\n\n  /**\n   * Formats a file using Prettier if available, otherwise falls back to basic whitespace normalization.\n   * Caches resolved Prettier configs per directory. Allows overriding options per invocation.\n   */\n  async formatFile(\n    filePath: string,\n    content: string,\n    lang = 'typescript',\n    overrideOptions: Partial<PrettierOptions> = {}\n  ): Promise<FileEdit> {\n    const fileDir = path.dirname(filePath)\n    const before = content\n    let after = before\n\n    if (!before) {\n      return this.buildEdit(filePath, before, '')\n    }\n\n    if (!this.prettierInstance) {\n      after = this.fallbackFormat(before)\n      return this.buildEdit(filePath, before, after)\n    }\n\n    const parser = this.getParser(lang)\n    const cached = this.configCache.get(fileDir)\n    let baseConfig: PrettierOptions = cached ?? {}\n\n    if (!cached) {\n      try {\nconst resolved = await prettierModule.resolveConfig(filePath, { editorconfig: true })\n        baseConfig = resolved || {}\n      } catch (resolveErr) {\n        console.warn(`Failed to resolve Prettier config for ${filePath}:`, resolveErr instanceof Error ? resolveErr.message : resolveErr)\n      }\n      this.configCache.set(fileDir, baseConfig)\n    }\n\n    const options: PrettierOptions = {\n      parser,\n      ...baseConfig,\n      ...overrideOptions\n    }\n\n    try {\nafter = await this.prettierInstance.format(before, options)\n    } catch (formatErr) {\n      console.warn(\n        `Prettier formatting failed for ${filePath}, falling back to whitespace normalization:`,\n        formatErr instanceof Error ? formatErr.message : formatErr\n      )\n      after = this.fallbackFormat(before)\n    }\n\n    return this.buildEdit(filePath, before, after)\n  }\n\n  private fallbackFormat(content: string): string {\n    return content\n      .replace(/\\r\\n/g, '\\n')\n      .replace(/\\r/g, '\\n')\n      .replace(/[ \\t]+$/gm, '')\n      .replace(/\\n{3,}/g, '\\n\\n')\n      .replace(/\\s+$/, '')\n  }\n\n  private getParser(lang: string): string {\n    const map: Record<string, string> = {\n      typescript: 'typescript',\n      tsx: 'typescript',\n      javascript: 'babel',\n      jsx: 'babel',\n      json: 'json',\n      json5: 'json',\n      graphql: 'graphql',\n      markdown: 'markdown',\n      md: 'markdown',\n      html: 'html',\n      vue: 'vue',\n      css: 'css',\n      scss: 'scss',\n      less: 'less',\n      yaml: 'yaml',\n      yml: 'yaml'\n    }\n    return map[lang.toLowerCase()] || 'babel'\n  }\n\n  private buildEdit(path: string, before: string, after: string): FileEdit {\n    return {\n      path,\n      before,\n      after,\n      applied: before !== after\n    }\n  }\n}\n\n// === CLI Entrypoint ===\nif (require.main === module) {\n  (async () => {\n    const root = process.argv[2] || process.cwd();\n    const editor = new AIEditor(root);\n    console.log(`Initialized AI Editor at ${root}`);\n\n    // Example usage:\n    // const plan = await editor.renameSymbol('path/to/file.ts', 'oldName', 'newName');\n    // const result = await editor.applyPlan(plan);\n  })();\n}\n"]}