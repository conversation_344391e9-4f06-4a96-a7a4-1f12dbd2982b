"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuantumMemorySystem = exports.PineconeVectorStore = exports.ChromaVectorStore = exports.MemoryVectorStore = exports.VectorStoreFactory = exports.RedisDatabase = exports.MongoDBDatabase = exports.MySQLDatabase = exports.PostgresDatabase = exports.SQLiteDatabase = exports.DatabaseFactory = exports.FileChunkingService = exports.CodessaGraphMemory = exports.CodessaMemoryProvider = exports.codessaMemoryProvider = exports.setMemoryEnabled = exports.getMemoryEnabled = exports.getAgentMemory = exports.AgentMemory = exports.vectorMemoryManager = exports.MemoryManager = exports.memoryManager = void 0;
exports.initializeMemorySystem = initializeMemorySystem;
exports.isMemorySystemReady = isMemorySystemReady;
exports.getMemorySystemAnalytics = getMemorySystemAnalytics;
// ============================================================================
// MEMORY SYSTEM EXPORTS - Centralized Export Point
// ============================================================================
// Main Memory Manager (Primary Integration Point)
var memoryManager_1 = require("./memoryManager");
Object.defineProperty(exports, "memoryManager", { enumerable: true, get: function () { return memoryManager_1.memoryManager; } });
Object.defineProperty(exports, "MemoryManager", { enumerable: true, get: function () { return memoryManager_1.MemoryManager; } });
// Vector Memory System
var vectorMemory_1 = require("./vectorMemory");
Object.defineProperty(exports, "vectorMemoryManager", { enumerable: true, get: function () { return vectorMemory_1.vectorMemoryManager; } });
// Agent Memory Integration
var agentMemory_1 = require("./agentMemory");
Object.defineProperty(exports, "AgentMemory", { enumerable: true, get: function () { return agentMemory_1.AgentMemory; } });
Object.defineProperty(exports, "getAgentMemory", { enumerable: true, get: function () { return agentMemory_1.getAgentMemory; } });
// Memory Configuration
var memoryConfig_1 = require("./memoryConfig");
Object.defineProperty(exports, "getMemoryEnabled", { enumerable: true, get: function () { return memoryConfig_1.getMemoryEnabled; } });
Object.defineProperty(exports, "setMemoryEnabled", { enumerable: true, get: function () { return memoryConfig_1.setMemoryEnabled; } });
// Codessa Memory Provider (Advanced Memory System)
var codessaMemory_1 = require("./codessa/codessaMemory");
Object.defineProperty(exports, "codessaMemoryProvider", { enumerable: true, get: function () { return codessaMemory_1.codessaMemoryProvider; } });
Object.defineProperty(exports, "CodessaMemoryProvider", { enumerable: true, get: function () { return codessaMemory_1.CodessaMemoryProvider; } });
// Codessa Graph Memory
var codessaGraphMemory_1 = require("./codessa/codessaGraphMemory");
Object.defineProperty(exports, "CodessaGraphMemory", { enumerable: true, get: function () { return codessaGraphMemory_1.CodessaGraphMemory; } });
// File Chunking Service
var fileChunking_1 = require("./codessa/fileChunking");
Object.defineProperty(exports, "FileChunkingService", { enumerable: true, get: function () { return fileChunking_1.FileChunkingService; } });
// Database Implementations
var databaseFactory_1 = require("./codessa/databases/databaseFactory");
Object.defineProperty(exports, "DatabaseFactory", { enumerable: true, get: function () { return databaseFactory_1.DatabaseFactory; } });
var sqliteDatabase_1 = require("./codessa/databases/sqliteDatabase");
Object.defineProperty(exports, "SQLiteDatabase", { enumerable: true, get: function () { return sqliteDatabase_1.SQLiteDatabase; } });
var postgresDatabase_1 = require("./codessa/databases/postgresDatabase");
Object.defineProperty(exports, "PostgresDatabase", { enumerable: true, get: function () { return postgresDatabase_1.PostgresDatabase; } });
var mysqlDatabase_1 = require("./codessa/databases/mysqlDatabase");
Object.defineProperty(exports, "MySQLDatabase", { enumerable: true, get: function () { return mysqlDatabase_1.MySQLDatabase; } });
var mongodbDatabase_1 = require("./codessa/databases/mongodbDatabase");
Object.defineProperty(exports, "MongoDBDatabase", { enumerable: true, get: function () { return mongodbDatabase_1.MongoDBDatabase; } });
var redisDatabase_1 = require("./codessa/databases/redisDatabase");
Object.defineProperty(exports, "RedisDatabase", { enumerable: true, get: function () { return redisDatabase_1.RedisDatabase; } });
// Vector Store Implementations
var vectorStoreFactory_1 = require("./codessa/vectorStores/vectorStoreFactory");
Object.defineProperty(exports, "VectorStoreFactory", { enumerable: true, get: function () { return vectorStoreFactory_1.VectorStoreFactory; } });
var memoryVectorStore_1 = require("./codessa/vectorStores/memoryVectorStore");
Object.defineProperty(exports, "MemoryVectorStore", { enumerable: true, get: function () { return memoryVectorStore_1.MemoryVectorStore; } });
var chromaVectorStore_1 = require("./codessa/vectorStores/chromaVectorStore");
Object.defineProperty(exports, "ChromaVectorStore", { enumerable: true, get: function () { return chromaVectorStore_1.ChromaVectorStore; } });
var pineconeVectorStore_1 = require("./codessa/vectorStores/pineconeVectorStore");
Object.defineProperty(exports, "PineconeVectorStore", { enumerable: true, get: function () { return pineconeVectorStore_1.PineconeVectorStore; } });
// Quantum Memory System (Advanced)
var quantumMemorySystem_1 = require("./quantum/quantumMemorySystem");
Object.defineProperty(exports, "QuantumMemorySystem", { enumerable: true, get: function () { return quantumMemorySystem_1.QuantumMemorySystem; } });
// ============================================================================
// MEMORY SYSTEM HELPER FUNCTIONS
// ============================================================================
/**
 * Memory System Initialization Helper
 *
 * Provides a convenient way to initialize the entire memory system
 * with proper error handling and fallbacks.
 */
async function initializeMemorySystem(context) {
    try {
        const { memoryManager } = await Promise.resolve().then(() => __importStar(require('./memoryManager')));
        await memoryManager.initialize(context);
        return true;
    }
    catch (error) {
        console.error('Failed to initialize memory system:', error);
        return false;
    }
}
/**
 * Memory System Status Check
 *
 * Provides a way to check if the memory system is properly initialized
 * and ready for use.
 */
async function isMemorySystemReady() {
    try {
        const { memoryManager } = await Promise.resolve().then(() => __importStar(require('./memoryManager')));
        return memoryManager.isInitialized();
    }
    catch (error) {
        console.error('Failed to check memory system status:', error);
        return false;
    }
}
/**
 * Get Memory System Analytics
 *
 * Provides access to memory system analytics and statistics.
 */
async function getMemorySystemAnalytics() {
    try {
        const { memoryManager } = await Promise.resolve().then(() => __importStar(require('./memoryManager')));
        return memoryManager.getMemoryAnalytics();
    }
    catch (error) {
        console.error('Failed to get memory system analytics:', error);
        return null;
    }
}
//# sourceMappingURL=types.js.map