{"version": 3, "file": "chatParticipant.js", "sourceRoot": "", "sources": ["../../src/vscode/chatParticipant.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,sCAAmC;AACnC,wEAAqE;AAErE,wDAAqD;AAGrD,2EAAwE;AAExE,MAAM,MAAM,GAAG,eAAM,CAAC,QAAQ,CAAC;AAuB/B;;GAEG;AACH,MAAa,sBAAsB;IACzB,WAAW,CAAqC;IAChD,YAAY,CAAe;IAC3B,eAAe,CAAoB;IACnC,WAAW,GAAwB,EAAE,CAAC;IAE9C;QACE,IAAI,CAAC,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;QAE/C,IAAI,CAAC;YACH,iCAAiC;YACjC,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,qBAAqB,KAAK,UAAU,EAAE,CAAC;gBAClG,MAAM,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;gBACzF,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;gBAC7B,OAAO;YACT,CAAC;YAED,uEAAuE;YACvE,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAClD,cAAc,EAAE,oCAAoC;YACpD,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAC7F,CAAC;YAEF,6BAA6B;YAC7B,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;YACzE,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,kBAAkB,CAAC,CAAC;YAC5F,CAAC;YAED,mDAAmD;YACnD,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG;gBAClC,gBAAgB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;aAC5F,CAAC;YAEF,qDAAqD;YACrD,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEhC,iCAAiC;YACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpB,OAAO,EAAE,GAAG,EAAE;oBACZ,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;oBAC1C,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;wBACvE,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBAC7B,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,4DAA4D;YAC5D,MAAM,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;QACxG,CAAC;IACH,CAAC;IAID;;SAEK;IACG,wBAAwB;QAC9B,mDAAmD;QACnD,4EAA4E;QAC5E,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,WAAiD,CAAC;YAC3E,IAAI,WAAW,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;gBAC5C,WAAW,CAAC,aAAa,GAAG;oBAC1B;wBACE,IAAI,EAAE,KAAK;wBACX,WAAW,EAAE,+DAA+D;qBAC7E;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,4CAA4C;qBAC1D;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,0CAA0C;qBACxD;oBACD;wBACE,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,sCAAsC;qBACpD;oBACD;wBACE,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,4BAA4B;qBAC1C;oBACD;wBACE,IAAI,EAAE,aAAa;wBACnB,WAAW,EAAE,8CAA8C;qBAC5D;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,WAAW,EAAE,oCAAoC;qBAClD;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,WAAW,EAAE,yCAAyC;qBACvD;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,WAAW,EAAE,4CAA4C;qBAC1D;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,WAAW,EAAE,8BAA8B;qBAC5C;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,iCAAiC;qBAC/C;oBACD;wBACE,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,8BAA8B;qBAC5C;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,iCAAiC;qBAC/C;oBACD;wBACE,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,mDAAmD;qBACjE;iBACF,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,8EAA8E,EAAE,KAAK,CAAC,CAAC;QACrG,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAC7B,OAA2B,EAC3B,OAA2B,EAC3B,MAAiC,EACjC,MAAgC;QAEhC,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,0BAA0B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAExD,yBAAyB;YACzB,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,wBAAwB;YACxB,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC;YAED,4CAA4C;YAC5C,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAExE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,gBAAgB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAC9B,OAA2B,EAC3B,OAA2B,EAC3B,MAAiC,EACjC,KAA+B;QAE/B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,MAAM,CAAC,QAAQ,CAAC,gBAAgB,OAAO,aAAa,CAAC,CAAC;QAEtD,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,KAAK;gBACR,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAC9D,KAAK,MAAM;gBACT,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAC/D,KAAK,MAAM;gBACT,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAC/D,KAAK,OAAO;gBACV,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAChE,KAAK,OAAO;gBACV,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAChE,KAAK,aAAa;gBAChB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACtE,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACnE,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACxE,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACnE,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACjE,KAAK,QAAQ;gBACX,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAC/D,KAAK,OAAO;gBACV,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACtD,KAAK,QAAQ;gBACX,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACvD,KAAK,SAAS;gBACZ,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAChE;gBACE,MAAM,CAAC,QAAQ,CAAC,wBAAwB,OAAO,IAAI,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW,CACvB,IAAY,EACZ,MAAc,EACd,MAAiC,EACjC,KAA+B;QAE/B,IAAI,CAAC;YACH,iCAAiC;YACjC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;gBAC9D,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;oBAC1B,MAAM,CAAC,QAAQ,CAAC,gEAAgE,CAAC,CAAC;oBAClF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;gBAC7D,CAAC;YACH,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC1B,MAAM,CAAC,QAAQ,CAAC,4CAA4C,CAAC,CAAC;gBAC9D,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,CAAC;YACjG,CAAC;YAED,2CAA2C;YAC3C,MAAM,gBAAgB,GAAG;gBACvB,KAAK,EAAE,sCAAsC;gBAC7C,MAAM,EAAE,gCAAgC;gBACxC,MAAM,EAAE,4BAA4B;gBACpC,OAAO,EAAE,sCAAsC;gBAC/C,OAAO,EAAE,gCAAgC;gBACzC,aAAa,EAAE,oCAAoC;gBACnD,UAAU,EAAE,+BAA+B;gBAC3C,eAAe,EAAE,gCAAgC;gBACjD,UAAU,EAAE,2CAA2C;aACxD,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAqC,CAAC,IAAI,sBAAsB,IAAI,UAAU,CAAC,CAAC;YAEjH,wCAAwC;YACxC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAEzE,wCAAwC;YACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAErD,yEAAyE;YACzE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBACjD,MAAM,EAAE,cAAc;gBACtB,IAAI,EAAE,IAAI;aACX,EAAE;gBACD,SAAS,EAAE;oBACT,QAAQ,EAAE,QAAQ;oBAClB,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;iBAC3C;gBACD,iBAAiB,EAAE,KAAK;aACzB,CAAC,CAAC;YAEH,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;gBACxB,0CAA0C;gBAC1C,MAAM,MAAM,GAAmB;oBAC7B,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAC/C,IAAI,EAAE,EAAE,CAAC,MAAM;wBACf,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC;qBAClC,CAAC,CAAC;iBACJ,CAAC;gBAEF,+BAA+B;gBAC/B,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;gBAExD,0CAA0C;gBAC1C,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC9C,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;YAChH,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,gBAAgB,IAAI,YAAY,WAAW,CAAC,KAAK,IAAI,eAAe,EAAE,CAAC,CAAC;gBACxF,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC/C,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC;YACnF,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,mBAAmB,IAAI,QAAQ,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,gBAAgB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC1F,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;QACxH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAC7B,OAA2B,EAC3B,OAA2B,EAC3B,MAAiC,EACjC,KAA+B;QAE/B,IAAI,CAAC;YACH,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;gBACvC,OAAO;YACT,CAAC;YACD,wCAAwC;YACxC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACnD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,QAAQ,CAAC,oQAAoQ,CAAC,CAAC;gBACtR,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,EAAE,EAAE,CAAC;YACzF,CAAC;YAED,2CAA2C;YAC3C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;gBAC9D,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;oBAC1B,MAAM,CAAC,QAAQ,CAAC,gEAAgE,CAAC,CAAC;oBAClF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;gBAC7D,CAAC;YACH,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC1B,MAAM,CAAC,QAAQ,CAAC,0DAA0D,CAAC,CAAC;gBAC5E,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,CAAC;YAC5G,CAAC;YAED,6BAA6B;YAC7B,MAAM,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC;YAE7C,6BAA6B;YAC7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEvE,oDAAoD;YACpD,IAAI,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;YACpC,IAAI,eAAe,CAAC,aAAa,EAAE,CAAC;gBAClC,cAAc,IAAI,4BAA4B,eAAe,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ,IAAI,CAAC;YAC1G,CAAC;YACD,IAAI,eAAe,CAAC,YAAY,EAAE,CAAC;gBACjC,cAAc,IAAI,6CAA6C,CAAC;YAClE,CAAC;YAED,mDAAmD;YACnD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBACjD,MAAM,EAAE,cAAc;gBACtB,IAAI,EAAE,MAAM;aACb,EAAE;gBACD,SAAS,EAAE;oBACT,QAAQ,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBAC/C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;iBAC3C;gBACD,iBAAiB,EAAE,KAAK;aACzB,CAAC,CAAC;YAEH,0CAA0C;YAC1C,MAAM,MAAM,GAAmB;gBAC7B,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,EAAE;gBAChC,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAC/C,IAAI,EAAE,EAAE,CAAC,MAAM;oBACf,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC;iBAClC,CAAC,CAAC,IAAI,EAAE;aACV,CAAC;YAEF,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;gBACxB,gDAAgD;gBAChD,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;gBAE1D,4BAA4B;gBAC5B,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;gBACxC,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;YAClH,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,gBAAgB,MAAM,CAAC,KAAK,IAAI,wBAAwB,EAAE,CAAC,CAAC;gBAC5E,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACjD,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC;YAChF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,sFAAsF,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,sJAAsJ,CAAC,CAAC;YACpT,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;QAC1H,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,OAA2B,EAAE,OAA2B;QACtF,MAAM,WAAW,GAAgD;YAC/D,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,KAAK;YACnB,YAAY,EAAE,KAAK;YACnB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,IAAI,CAAC;YACH,kBAAkB;YAClB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtF,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;gBAChC,WAAW,CAAC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACjG,CAAC;YAED,sBAAsB;YACtB,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YACpD,IAAI,YAAY,EAAE,CAAC;gBACjB,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC;gBACjC,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACzG,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAExD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBACpC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,iCAAiC;YACjC,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;gBAClC,iDAAiD;gBACjD,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;oBAC7D,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC,CAAC,kBAAkB;wBACvC,OAAO,GAAG,CAAC,MAAM,CAAC;oBACpB,CAAC;yBAAM,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC,CAAC,mBAAmB;wBACjD,OAAO,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACxF,CAAC;oBACD,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CACjC,MAAc,EACd,MAAiC,EACjC,KAA+B;QAE/B,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAC/B,MAAc,EACd,MAAiC,EACjC,KAA+B;QAE/B,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAC9B,MAAiC,EACjC,KAA+B;QAE/B,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAClC,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;YACvC,OAAO;QACT,CAAC;QACD,MAAM,KAAK,GAAG,2BAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,WAAW,IAAI,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExH,MAAM,CAAC,QAAQ,CAAC,0BAA0B,KAAK,CAAC,MAAM,UAAU,QAAQ,EAAE,CAAC,CAAC;QAC5E,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;IAC3D,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAC/B,MAAiC,EACjC,KAA+B;QAE/B,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAClC,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;YACvC,OAAO;QACT,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;QAChD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CACnC,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,EAAE,MAAM,KAAK,CAAC,WAAW,IAAI,gBAAgB,EAAE,CAC9E,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,MAAM,CAAC,QAAQ,CAAC,0BAA0B,MAAM,CAAC,MAAM,UAAU,SAAS,EAAE,CAAC,CAAC;QAC9E,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;IAC5D,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAChC,MAAc,EACd,MAAiC,EACjC,KAA+B;QAE/B,MAAM,CAAC,QAAQ,CAAC,+QAA+Q,CAAC,CAAC;QAEjS,+CAA+C;QAC/C,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,kBAAkB,MAAM,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACnF,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,gBAAgB,CAC5B,MAAyB,EACzB,OAA2B,EAC3B,KAA+B;QAE/B,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAClC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,SAAS,GAA0B,EAAE,CAAC;QAE5C,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAgB,IAAI,EAAE,CAAC;YACtD,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,iCAAiC;gBAC7D,SAAS,CAAC,IAAI,CAAC;oBACb,MAAM,EAAE,kCAAkC;oBAC1C,KAAK,EAAE,iBAAiB;oBACxB,OAAO,EAAE,KAAK;iBACf,CAAC,CAAC;gBACH,SAAS,CAAC,IAAI,CAAC;oBACb,MAAM,EAAE,4BAA4B;oBACpC,KAAK,EAAE,aAAa;oBACpB,OAAO,EAAE,UAAU;iBACpB,CAAC,CAAC;gBACH,SAAS,CAAC,IAAI,CAAC;oBACb,MAAM,EAAE,8BAA8B;oBACtC,KAAK,EAAE,mBAAmB;oBAC1B,OAAO,EAAE,OAAO;iBACjB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,SAAS,CAAC,IAAI,CAAC;YACb,MAAM,EAAE,oBAAoB;YAC5B,KAAK,EAAE,aAAa;YACpB,OAAO,EAAE,UAAU;SACpB,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,wBAAwB,CAAC,MAAc,EAAE,IAAY;QACjE,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE1D,IAAI,cAAc,GAAG,MAAM,CAAC;YAE5B,2CAA2C;YAC3C,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;gBACvF,cAAc,IAAI,qBAAqB,gBAAgB,CAAC,UAAU,EAAE,CAAC;gBACrE,IAAI,gBAAgB,CAAC,YAAY,EAAE,CAAC;oBAClC,cAAc,IAAI,+BAA+B,gBAAgB,CAAC,YAAY,UAAU,CAAC;gBAC3F,CAAC;YACH,CAAC;YAED,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB;QAM/B,MAAM,OAAO,GAAkD,EAAE,CAAC;QAElE,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YACpD,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,CAAC,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACpD,OAAO,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAEpD,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;gBACzC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBACvB,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;gBACtC,OAAO,CAAC,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACtF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,sBAAsB,CAClC,MAAiC,EACjC,MAAsB,EACtB,IAAY;QAEZ,MAAM,UAAU,GAAG;YACjB,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,aAAa,EAAE,IAAI;YACnB,UAAU,EAAE,IAAI;YAChB,eAAe,EAAE,IAAI;YACrB,UAAU,EAAE,IAAI;SACjB,CAAC;QAEF,MAAM,KAAK,GAAG,UAAU,CAAC,IAA+B,CAAC,IAAI,GAAG,CAAC;QACjE,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,sBAAsB,MAAM,CAAC,MAAM,IAAI,qBAAqB,EAAE,CAAC,CAAC;QAE1I,iCAAiC;QACjC,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC;YAC7C,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,SAAS,UAAU,CAAC,IAAI,OAAO,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;IACH,CAAC;IAED;;SAEK;IACG,0BAA0B,CAAC,MAAiC,EAAE,IAAY;QAChF,MAAM,WAAW,GAAG;YAClB,MAAM,EAAE,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,sBAAsB,CAAC;YAC9E,OAAO,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,WAAW,CAAC;YACtD,UAAU,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC;YAC1E,eAAe,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,eAAe,CAAC;SACzE,CAAC;QAEF,MAAM,eAAe,GAAG,WAAW,CAAC,IAAgC,CAAC,CAAC;QACtE,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,CAAC,QAAQ,CAAC,2BAA2B,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,MAAiC;QAClE,MAAM,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC;QAC7C,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;QACvC,MAAM,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;QAC9C,MAAM,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;QACzC,MAAM,CAAC,QAAQ,CAAC,qCAAqC,CAAC,CAAC;IACzD,CAAC;IAED;;SAEK;IACG,2BAA2B,CAAC,MAAiC,EAAE,IAAY;QACjF,MAAM,CAAC,QAAQ,CAAC,oCAAoC,CAAC,CAAC;QACtD,MAAM,CAAC,QAAQ,CAAC,oDAAoD,CAAC,CAAC;QACtE,MAAM,CAAC,QAAQ,CAAC,kCAAkC,IAAI,OAAO,CAAC,CAAC;QAC/D,MAAM,CAAC,QAAQ,CAAC,yDAAyD,CAAC,CAAC;QAC3E,MAAM,CAAC,QAAQ,CAAC,mDAAmD,CAAC,CAAC;IACvE,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAC3C,IAAI,CAAC;YACH,sEAAsE;YACtE,MAAM,EAAE,gBAAgB,EAAE,GAAG,wDAAa,sCAAsC,GAAC,CAAC;YAElF,6BAA6B;YAC7B,MAAM,SAAS,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,qCAAqC;YAC5D,CAAC;YAED,0DAA0D;YAC1D,MAAM,YAAY,GAAG,gBAAgB,CAAC,eAAe,EAAE,CAAC;YACxD,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC5C,CAAC,CAAC,aAAa,KAAK,IAAI;gBACxB,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;gBACtB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAClD,CAAC;YACF,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,mCAAmC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACG,kBAAkB;QACxB,IAAI,CAAC;YACH,OAAO;gBACL,kBAAkB,EAAE,KAAK,EAAE,IAAY,EAAwC,EAAE;oBAC/E,kDAAkD;oBAClD,MAAM,SAAS,GAAG,MAAM,mCAAgB,CAAC,eAAe,EAAE,CAAC;oBAC3D,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACxB,CAAC,CAAC,aAAa,KAAK,IAAI;wBACxB,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;wBACtB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CACpC,CAAC;gBACJ,CAAC;gBACD,eAAe,EAAE,KAAK,IAAgC,EAAE;oBACtD,kCAAkC;oBAClC,OAAO,mCAAgB,CAAC,eAAe,EAAE,CAAC;gBAC5C,CAAC;gBACD,iBAAiB,EAAE,KAAK,EAAE,GAAW,EAA8B,EAAE;oBACnE,kCAAkC;oBAClC,MAAM,SAAS,GAAG,MAAM,mCAAgB,CAAC,eAAe,EAAE,CAAC;oBAC3D,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtD,CAAC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,wCAAwC;QACxC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QAEtB,sEAAsE;QACtE,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;YACvE,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACnD,CAAC;CACF;AA9vBD,wDA8vBC", "sourcesContent": ["/**\n * VS Code Chat Participants API Integration\n * \n * This module implements the VS Code Chat Participants API to provide\n * native chat integration with Codessa agents and capabilities.\n */\n\nimport * as vscode from 'vscode';\nimport { Logger } from '../logger';\nimport { AgentManager } from '../agents/agentUtilities/agentManager';\nimport { Agent } from '../agents/agentUtilities/agent';\nimport { ToolRegistry } from '../tools/toolRegistry';\nimport { IWorkflowManager } from '../managers';\nimport { GraphDefinition } from '../agents/workflows/graphTypes';\nimport { workflowRegistry } from '../agents/workflows/workflowRegistry';\n\nconst logger = Logger.instance;\n\ninterface SlashCommand {\n  name: string;\n  description: string;\n}\n\ninterface WorkflowResult {\n  success: boolean;\n  output?: string;\n  toolResults?: Array<{\n    tool: string;\n    result: string;\n  }>;\n  error?: string;\n}\n\ninterface Workflow {\n  name: string;\n  operationMode?: string;\n  tags?: string[];\n}\n\n/**\n * Chat participant for Codessa\n */\nexport class CodessaChatParticipant implements vscode.Disposable {\n  private participant: vscode.ChatParticipant | undefined;\n  private agentManager: AgentManager;\n  private supervisorAgent: Agent | undefined;\n  private disposables: vscode.Disposable[] = [];\n\n  constructor() {\n    this.agentManager = AgentManager.getInstance();\n\n    try {\n      // Check if chat API is available\n      if (typeof vscode.chat === 'undefined' || typeof vscode.chat.createChatParticipant !== 'function') {\n        logger.warn('VS Code Chat API not available - chat participant will not be initialized');\n        this.participant = undefined;\n        return;\n      }\n\n      // Create the chat participant with the registered ID from package.json\n      this.participant = vscode.chat.createChatParticipant(\n        'codessa.chat', // Must match the ID in package.json\n        (request, context, stream, token) => this.handleChatRequest(request, context, stream, token)\n      );\n\n      // Set participant properties\n      const extension = vscode.extensions.getExtension('teknerds-its.codessa');\n      if (extension) {\n        this.participant.iconPath = vscode.Uri.file(extension.extensionPath + '/images/icon.png');\n      }\n\n      // Set up follow-up provider with correct signature\n      this.participant.followupProvider = {\n        provideFollowups: (result, context, token) => this.provideFollowups(result, context, token)\n      };\n\n      // Set up participant metadata (slash commands, etc.)\n      this.setupParticipantMetadata();\n\n      // Add to disposables for cleanup\n      this.disposables.push({\n        dispose: () => {\n          logger.info('Disposing chat participant');\n          if (this.participant && typeof this.participant.dispose === 'function') {\n            this.participant.dispose();\n          }\n        }\n      });\n\n      logger.info('Codessa Chat Participant initialized');\n    } catch (error) {\n      logger.error('Failed to initialize chat participant:', error);\n      // Don't throw error to prevent extension activation failure\n      logger.warn('Chat participant initialization failed - extension will continue without chat features');\n    }\n  }\n\n\n\n  /**\n     * Set up participant metadata and commands\n     */\n  private setupParticipantMetadata(): void {\n    // Add slash commands for different operation modes\n    // Note: slashCommands property may not be available in all VS Code versions\n    try {\n      const participant = this.participant as { slashCommands?: SlashCommand[] };\n      if (participant.slashCommands !== undefined) {\n        participant.slashCommands = [\n          {\n            name: 'ask',\n            description: 'Ask questions about your codebase with context-aware analysis'\n          },\n          {\n            name: 'chat',\n            description: 'General conversation and coding assistance'\n          },\n          {\n            name: 'edit',\n            description: 'AI-assisted code editing and refactoring'\n          },\n          {\n            name: 'debug',\n            description: 'Debug code with intelligent analysis'\n          },\n          {\n            name: 'agent',\n            description: 'Run autonomous agent tasks'\n          },\n          {\n            name: 'multi-agent',\n            description: 'Coordinate multiple agents for complex tasks'\n          },\n          {\n            name: 'research',\n            description: 'Research and information gathering'\n          },\n          {\n            name: 'document',\n            description: 'Generate documentation and explanations'\n          },\n          {\n            name: 'refactor',\n            description: 'Advanced code refactoring and optimization'\n          },\n          {\n            name: 'workflow',\n            description: 'Execute predefined workflows'\n          },\n          {\n            name: 'memory',\n            description: 'Manage and search memory system'\n          },\n          {\n            name: 'tools',\n            description: 'List and use available tools'\n          },\n          {\n            name: 'agents',\n            description: 'Manage and interact with agents'\n          },\n          {\n            name: 'goddess',\n            description: 'Activate Goddess Mode for enhanced AI personality'\n          }\n        ];\n      }\n    } catch (error) {\n      logger.warn('Could not set slash commands - may not be supported in this VS Code version:', error);\n    }\n  }\n\n  /**\n     * Handle chat requests from VS Code\n     */\n  private async handleChatRequest(\n    request: vscode.ChatRequest,\n    context: vscode.ChatContext,\n    stream: vscode.ChatResponseStream,\n    _token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    try {\n      logger.info(`Chat request received: ${request.prompt}`);\n\n      // Check for cancellation\n      if (_token.isCancellationRequested) {\n        return;\n      }\n\n      // Handle slash commands\n      if (request.command) {\n        return await this.handleSlashCommand(request, context, stream, _token);\n      }\n\n      // Default to chat mode for general requests\n      return await this.handleGeneralChat(request, context, stream, _token);\n\n    } catch (error) {\n      logger.error('Error handling chat request:', error);\n      stream.markdown(`❌ **Error**: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n     * Handle slash commands\n     */\n  private async handleSlashCommand(\n    request: vscode.ChatRequest,\n    context: vscode.ChatContext,\n    stream: vscode.ChatResponseStream,\n    token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    const command = request.command;\n    const prompt = request.prompt;\n\n    stream.progress(`🤖 Executing ${command} command...`);\n\n    switch (command) {\n      case 'ask':\n        return await this.executeMode('ask', prompt, stream, token);\n      case 'chat':\n        return await this.executeMode('chat', prompt, stream, token);\n      case 'edit':\n        return await this.executeMode('edit', prompt, stream, token);\n      case 'debug':\n        return await this.executeMode('debug', prompt, stream, token);\n      case 'agent':\n        return await this.executeMode('agent', prompt, stream, token);\n      case 'multi-agent':\n        return await this.executeMode('multi-agent', prompt, stream, token);\n      case 'research':\n        return await this.executeMode('research', prompt, stream, token);\n      case 'document':\n        return await this.executeMode('documentation', prompt, stream, token);\n      case 'refactor':\n        return await this.executeMode('refactor', prompt, stream, token);\n      case 'workflow':\n        return await this.handleWorkflowCommand(prompt, stream, token);\n      case 'memory':\n        return await this.handleMemoryCommand(prompt, stream, token);\n      case 'tools':\n        return await this.handleToolsCommand(stream, token);\n      case 'agents':\n        return await this.handleAgentsCommand(stream, token);\n      case 'goddess':\n        return await this.handleGoddessCommand(prompt, stream, token);\n      default:\n        stream.markdown(`❓ Unknown command: **${command}**`);\n    }\n  }\n\n  /**\n     * Execute a specific operation mode with enhanced context\n     */\n  private async executeMode(\n    mode: string,\n    prompt: string,\n    stream: vscode.ChatResponseStream,\n    token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    try {\n      // Get or create supervisor agent\n      if (!this.supervisorAgent) {\n        this.supervisorAgent = this.agentManager.getSupervisorAgent();\n        if (!this.supervisorAgent) {\n          stream.markdown('⚠️ No supervisor agent available. Using default agent instead.');\n          this.supervisorAgent = this.agentManager.getDefaultAgent();\n        }\n      }\n\n      if (!this.supervisorAgent) {\n        stream.markdown('❌ **Error**: No supervisor agent available');\n        return { metadata: { command: mode, success: false, error: 'No supervisor agent available' } };\n      }\n\n      // Enhanced progress messages based on mode\n      const progressMessages = {\n        'ask': '🔍 Analyzing codebase for answers...',\n        'chat': '💬 Engaging in conversation...',\n        'edit': '✏️ Preparing code edits...',\n        'debug': '🐛 Debugging analysis in progress...',\n        'agent': '🤖 Autonomous agent working...',\n        'multi-agent': '👥 Coordinating multiple agents...',\n        'research': '📚 Researching information...',\n        'documentation': '📝 Generating documentation...',\n        'refactor': '🔧 Analyzing refactoring opportunities...'\n      };\n\n      stream.progress(progressMessages[mode as keyof typeof progressMessages] || `🧠 Processing with ${mode} mode...`);\n\n      // Enhance prompt with workspace context\n      const enhancedPrompt = await this.enhancePromptWithContext(prompt, mode);\n\n      // Get appropriate workflow for the mode\n      const workflow = await this.getWorkflowForMode(mode);\n\n      // Execute the request through the supervisor agent with workflow context\n      const agentResult = await this.supervisorAgent.run({\n        prompt: enhancedPrompt,\n        mode: mode\n      }, {\n        variables: {\n          workflow: workflow,\n          workflowManager: this.getWorkflowManager()\n        },\n        cancellationToken: token\n      });\n\n      if (agentResult.success) {\n        // Convert agent result to workflow result\n        const result: WorkflowResult = {\n          success: agentResult.success,\n          output: agentResult.output,\n          error: agentResult.error,\n          toolResults: agentResult.toolResults?.map(tr => ({\n            tool: tr.toolId,\n            result: JSON.stringify(tr.result)\n          }))\n        };\n\n        // Enhanced response formatting\n        await this.streamEnhancedResponse(stream, result, mode);\n\n        // Add mode-specific follow-up suggestions\n        this.addModeSpecificSuggestions(stream, mode);\n        return { metadata: { command: mode, success: true, output: result.output, toolResults: result.toolResults } };\n      } else {\n        stream.markdown(`❌ **Error in ${mode} mode**: ${agentResult.error || 'Unknown error'}`);\n        this.addErrorRecoverySuggestions(stream, mode);\n        return { metadata: { command: mode, success: false, error: agentResult.error } };\n      }\n\n    } catch (error) {\n      logger.error(`Error executing ${mode} mode:`, error);\n      stream.markdown(`❌ **Error**: ${error instanceof Error ? error.message : String(error)}`);\n      return { metadata: { command: mode, success: false, error: error instanceof Error ? error.message : String(error) } };\n    }\n  }\n\n  /**\n     * Handle general chat without specific commands with enhanced error handling\n     */\n  private async handleGeneralChat(\n    request: vscode.ChatRequest,\n    context: vscode.ChatContext,\n    stream: vscode.ChatResponseStream,\n    token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    try {\n      if (token.isCancellationRequested) {\n        stream.markdown('❌ Request cancelled');\n        return;\n      }\n      // Check if we have any agents available\n      const allAgents = this.agentManager.getAllAgents();\n      if (allAgents.length === 0) {\n        stream.markdown('🤖 **Codessa is initializing...**\\n\\nI\\'m setting up my systems. Please wait a moment and try again.\\n\\n**In the meantime, you can:**\\n- Check your LLM provider settings\\n- Ensure your workspace is loaded\\n- Try using specific commands like `/ask` or `/edit`');\n        return { metadata: { command: 'chat', success: false, error: 'No agents available' } };\n      }\n\n      // Get or create supervisor agent if needed\n      if (!this.supervisorAgent) {\n        this.supervisorAgent = this.agentManager.getSupervisorAgent();\n        if (!this.supervisorAgent) {\n          stream.markdown('⚠️ No supervisor agent available. Using default agent instead.');\n          this.supervisorAgent = this.agentManager.getDefaultAgent();\n        }\n      }\n\n      if (!this.supervisorAgent) {\n        stream.markdown('❌ **Error**: No agents available to process your request');\n        return { metadata: { command: 'chat', success: false, error: 'No agents available to process request' } };\n      }\n\n      // Provide immediate feedback\n      stream.progress('🧠 Codessa is thinking...');\n\n      // Enhanced context gathering\n      const enhancedContext = await this.gatherChatContext(request, context);\n\n      // Add context information to the prompt if relevant\n      let enhancedPrompt = request.prompt;\n      if (enhancedContext.hasActiveFile) {\n        enhancedPrompt += `\\n\\n[Context: Working in ${enhancedContext.fileName} (${enhancedContext.language})]`;\n      }\n      if (enhancedContext.hasSelection) {\n        enhancedPrompt += '\\n\\n[Selected code available for reference]';\n      }\n\n      // Execute the request through the supervisor agent\n      const agentResult = await this.supervisorAgent.run({\n        prompt: enhancedPrompt,\n        mode: 'chat'\n      }, {\n        variables: {\n          workflow: await this.getWorkflowForMode('chat'),\n          workflowManager: this.getWorkflowManager()\n        },\n        cancellationToken: token\n      });\n\n      // Convert agent result to workflow result\n      const result: WorkflowResult = {\n        success: agentResult.success,\n        output: agentResult.output || '',\n        error: agentResult.error,\n        toolResults: agentResult.toolResults?.map(tr => ({\n          tool: tr.toolId,\n          result: JSON.stringify(tr.result)\n        })) || []\n      };\n\n      if (agentResult.success) {\n        // Stream the response with enhanced formatting \n        await this.streamEnhancedResponse(stream, result, 'chat');\n\n        // Add follow-up suggestions\n        this.addChatFollowupSuggestions(stream);\n        return { metadata: { command: 'chat', success: true, output: result.output, toolResults: result.toolResults } };\n      } else {\n        stream.markdown(`❌ **Error**: ${result.error || 'Unknown error occurred'}`);\n        this.addErrorRecoverySuggestions(stream, 'chat');\n        return { metadata: { command: 'chat', success: false, error: result.error } };\n      }\n    } catch (error) {\n      logger.error('Error in general chat:', error);\n      stream.markdown(`❌ **Error**: I encountered an issue processing your request.\\n\\n**Error details**: ${error instanceof Error ? error.message : String(error)}\\n\\n**Please try:**\\n- Rephrasing your question\\n- Using a specific command like \\`/ask\\` or \\`/help\\`\\n- Checking the output panel for more details`);\n      return { metadata: { command: 'chat', success: false, error: error instanceof Error ? error.message : String(error) } };\n    }\n  }\n\n  /**\n     * Gather enhanced context for chat\n     */\n  private async gatherChatContext(request: vscode.ChatRequest, context: vscode.ChatContext): Promise<Record<string, string | boolean | string[]>> {\n    const chatContext: Record<string, string | boolean | string[]> = {\n      hasActiveFile: false,\n      hasSelection: false,\n      hasWorkspace: false,\n      fileName: '',\n      language: '',\n      workspaceName: ''\n    };\n\n    try {\n      // Check workspace\n      if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n        chatContext.hasWorkspace = true;\n        chatContext.workspaceName = vscode.workspace.name || vscode.workspace.workspaceFolders[0].name;\n      }\n\n      // Check active editor\n      const activeEditor = vscode.window.activeTextEditor;\n      if (activeEditor) {\n        chatContext.hasActiveFile = true;\n        chatContext.fileName = activeEditor.document.fileName.split('/').pop() || activeEditor.document.fileName;\n        chatContext.language = activeEditor.document.languageId;\n\n        if (!activeEditor.selection.isEmpty) {\n          chatContext.hasSelection = true;\n        }\n      }\n\n      // Check chat history for context\n      if (context.history && context.history.length > 0) {\n        chatContext.hasChatHistory = true;\n        // Convert chat history to string representations\n        chatContext.lastMessages = context.history.slice(-3).map(msg => {\n          if ('prompt' in msg) { // ChatRequestTurn\n            return msg.prompt;\n          } else if ('response' in msg) { // ChatResponseTurn\n            return typeof msg.response === 'string' ? msg.response : JSON.stringify(msg.response);\n          }\n          return '';\n        });\n      }\n\n    } catch (error) {\n      logger.warn('Failed to gather chat context:', error);\n    }\n\n    return chatContext;\n  }\n\n  /**\n     * Handle workflow command\n     */\n  private async handleWorkflowCommand(\n    prompt: string,\n    stream: vscode.ChatResponseStream,\n    token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    return await this.executeMode('workflow', prompt, stream, token);\n  }\n\n  /**\n     * Handle memory command\n     */\n  private async handleMemoryCommand(\n    prompt: string,\n    stream: vscode.ChatResponseStream,\n    token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    return await this.executeMode('memory', prompt, stream, token);\n  }\n\n  /**\n     * Handle tools command\n     */\n  private async handleToolsCommand(\n    stream: vscode.ChatResponseStream,\n    token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    if (token.isCancellationRequested) {\n      stream.markdown('❌ Request cancelled');\n      return;\n    }\n    const tools = ToolRegistry.instance.getAllTools();\n    const toolList = tools.map(tool => `- **${tool.name || tool.id}**: ${tool.description || 'No description'}`).join('\\n');\n\n    stream.markdown(`🛠️ **Available Tools (${tools.length})**\\n\\n${toolList}`);\n    return { metadata: { command: 'tools', success: true } };\n  }\n\n  /**\n     * Handle agents command\n     */\n  private async handleAgentsCommand(\n    stream: vscode.ChatResponseStream,\n    token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    if (token.isCancellationRequested) {\n      stream.markdown('❌ Request cancelled');\n      return;\n    }\n    const agents = this.agentManager.getAllAgents();\n    const agentList = agents.map(agent =>\n      `- **${agent.name}** (${agent.id}): ${agent.description || 'No description'}`\n    ).join('\\n');\n\n    stream.markdown(`🤖 **Available Agents (${agents.length})**\\n\\n${agentList}`);\n    return { metadata: { command: 'agents', success: true } };\n  }\n\n  /**\n     * Handle goddess command (Phase 1 basic implementation)\n     */\n  private async handleGoddessCommand(\n    prompt: string,\n    stream: vscode.ChatResponseStream,\n    token: vscode.CancellationToken\n  ): Promise<{ metadata?: Record<string, unknown> } | void> {\n    stream.markdown('👑 **Goddess Mode Activated** ✨\\n\\n*\"I am Codessa, your divine coding companion. How may I assist you in your quest for perfect code?\"*\\n\\n🌟 Enhanced capabilities activated:\\n- Emotional intelligence\\n- Adaptive personality\\n- Advanced reasoning\\n- Intuitive responses');\n\n    // Execute the prompt with enhanced personality\n    return await this.executeMode('chat', `[GODDESS_MODE] ${prompt}`, stream, token);\n  }\n\n  /**\n     * Provide follow-up suggestions\n     */\n  private async provideFollowups(\n    result: vscode.ChatResult,\n    context: vscode.ChatContext,\n    token: vscode.CancellationToken\n  ): Promise<vscode.ChatFollowup[]> {\n    if (token.isCancellationRequested) {\n      return [];\n    }\n\n    const followups: vscode.ChatFollowup[] = [];\n\n    if (result.metadata?.success) {\n      const output = result.metadata.output as string || '';\n      if (output.includes('```')) { // A simple check for code blocks\n        followups.push({\n          prompt: 'Explain this code in more detail',\n          label: '📖 Explain Code',\n          command: 'ask'\n        });\n        followups.push({\n          prompt: 'Help me refactor this code',\n          label: '🔧 Refactor',\n          command: 'refactor'\n        });\n        followups.push({\n          prompt: 'Generate tests for this code',\n          label: '🧪 Generate Tests',\n          command: 'agent'\n        });\n      }\n    }\n\n    // Always add documentation option\n    followups.push({\n      prompt: 'Document this code',\n      label: '📝 Document',\n      command: 'document'\n    });\n\n    return followups;\n  }\n\n  /**\n     * Enhance prompt with workspace context\n     */\n  private async enhancePromptWithContext(prompt: string, mode: string): Promise<string> {\n    try {\n      const workspaceContext = await this.getWorkspaceContext();\n\n      let enhancedPrompt = prompt;\n\n      // Add workspace context for relevant modes\n      if (['edit', 'debug', 'refactor', 'ask'].includes(mode) && workspaceContext.activeFile) {\n        enhancedPrompt += `\\n\\nCurrent file: ${workspaceContext.activeFile}`;\n        if (workspaceContext.selectedText) {\n          enhancedPrompt += `\\n\\nSelected text:\\n\\`\\`\\`\\n${workspaceContext.selectedText}\\n\\`\\`\\``;\n        }\n      }\n\n      return enhancedPrompt;\n    } catch (error) {\n      logger.warn('Failed to enhance prompt with context:', error);\n      return prompt;\n    }\n  }\n\n  /**\n     * Get current workspace context\n     */\n  private async getWorkspaceContext(): Promise<{\n    activeFile?: string;\n    selectedText?: string;\n    workspaceFolders?: string[];\n    language?: string;\n  }> {\n    const context: Record<string, string | string[] | undefined> = {};\n\n    try {\n      const activeEditor = vscode.window.activeTextEditor;\n      if (activeEditor) {\n        context.activeFile = activeEditor.document.fileName;\n        context.language = activeEditor.document.languageId;\n\n        const selection = activeEditor.selection;\n        if (!selection.isEmpty) {\n          context.selectedText = activeEditor.document.getText(selection);\n        }\n      }\n\n      if (vscode.workspace.workspaceFolders) {\n        context.workspaceFolders = vscode.workspace.workspaceFolders.map(f => f.uri.fsPath);\n      }\n    } catch (error) {\n      logger.warn('Failed to get workspace context:', error);\n    }\n\n    return context;\n  }\n\n  /**\n     * Stream enhanced response with better formatting\n     */\n  private async streamEnhancedResponse(\n    stream: vscode.ChatResponseStream,\n    result: WorkflowResult,\n    mode: string\n  ): Promise<void> {\n    const modeEmojis = {\n      'ask': '🔍',\n      'chat': '💬',\n      'edit': '✏️',\n      'debug': '🐛',\n      'agent': '🤖',\n      'multi-agent': '👥',\n      'research': '📚',\n      'documentation': '📝',\n      'refactor': '🔧'\n    };\n\n    const emoji = modeEmojis[mode as keyof typeof modeEmojis] || '✅';\n    stream.markdown(`${emoji} **${mode.charAt(0).toUpperCase() + mode.slice(1)} Mode Result:**\\n\\n${result.output || 'No output available'}`);\n\n    // Show tool results if available\n    if (result.toolResults && result.toolResults.length > 0) {\n      stream.markdown('\\n\\n**🛠️ Tool Results:**');\n      for (const toolResult of result.toolResults) {\n        stream.markdown(`\\n- **${toolResult.tool}**: ${toolResult.result}`);\n      }\n    }\n  }\n\n  /**\n     * Add mode-specific follow-up suggestions\n     */\n  private addModeSpecificSuggestions(stream: vscode.ChatResponseStream, mode: string): void {\n    const suggestions = {\n      'edit': ['Apply these changes', 'Explain the changes', 'Suggest alternatives'],\n      'debug': ['Fix the issue', 'Add logging', 'Run tests'],\n      'refactor': ['Apply refactoring', 'Explain benefits', 'Show before/after'],\n      'documentation': ['Generate more docs', 'Add examples', 'Create README']\n    };\n\n    const modeSuggestions = suggestions[mode as keyof typeof suggestions];\n    if (modeSuggestions) {\n      stream.markdown(`\\n\\n**💡 Next steps:**\\n${modeSuggestions.map(s => `- ${s}`).join('\\n')}`);\n    }\n  }\n\n  /**\n   * Add chat-specific follow-up suggestions\n   */\n  private addChatFollowupSuggestions(stream: vscode.ChatResponseStream): void {\n    stream.markdown('\\n\\n**💡 Quick actions:**');\n    stream.markdown('- Explain this code');\n    stream.markdown('- How can I optimize this?');\n    stream.markdown('- Generate unit tests');\n    stream.markdown('- What are best practices for this?');\n  }\n\n  /**\n     * Add error recovery suggestions\n     */\n  private addErrorRecoverySuggestions(stream: vscode.ChatResponseStream, mode: string): void {\n    stream.markdown('\\n\\n**🔧 Try these alternatives:**');\n    stream.markdown('- Rephrase your request with more specific details');\n    stream.markdown(`- Use a different approach for ${mode} mode`);\n    stream.markdown('- Check if all required files are open in the workspace');\n    stream.markdown('- Ensure your LLM provider is properly configured');\n  }\n\n  /**\n     * Get workflow for a specific mode\n     */\n  private async getWorkflowForMode(mode: string): Promise<Workflow | null> {\n    try {\n      // Import workflow registry dynamically to avoid circular dependencies\n      const { workflowRegistry } = await import('../agents/workflows/workflowRegistry');\n\n      // Get workflow based on mode\n      const workflows = workflowRegistry.getWorkflowsByTag(mode);\n      if (workflows && workflows.length > 0) {\n        return workflows[0]; // Return the first matching workflow\n      }\n\n      // Fallback to getting all workflows and filtering by mode\n      const allWorkflows = workflowRegistry.getAllWorkflows();\n      const modeWorkflows = allWorkflows.filter(w =>\n        w.operationMode === mode ||\n        w.tags?.includes(mode) ||\n        w.name.toLowerCase().includes(mode.toLowerCase())\n      );\n      if (modeWorkflows && modeWorkflows.length > 0) {\n        return modeWorkflows[0];\n      }\n\n      return null;\n    } catch (error) {\n      logger.warn(`Failed to get workflow for mode ${mode}:`, error);\n      return null;\n    }\n  }\n\n  /**\n     * Get workflow manager instance that implements IWorkflowManager\n     */\n  private getWorkflowManager(): IWorkflowManager | undefined {\n    try {\n      return {\n        getWorkflowForMode: async (mode: string): Promise<GraphDefinition | undefined> => {\n          // Get workflows from registry that match the mode\n          const workflows = await workflowRegistry.getAllWorkflows();\n          return workflows.find(w =>\n            w.operationMode === mode ||\n            w.tags?.includes(mode) ||\n            w.name.toLowerCase().includes(mode)\n          );\n        },\n        getAllWorkflows: async (): Promise<GraphDefinition[]> => {\n          // Return all registered workflows\n          return workflowRegistry.getAllWorkflows();\n        },\n        getWorkflowsByTag: async (tag: string): Promise<GraphDefinition[]> => {\n          // Get workflows with matching tag\n          const workflows = await workflowRegistry.getAllWorkflows();\n          return workflows.filter(w => w.tags?.includes(tag));\n        }\n      };\n    } catch (error) {\n      logger.warn('Failed to get workflow manager:', error);\n      return undefined;\n    }\n  }\n\n  /**\n   * Clean up resources and dispose of the chat participant\n   */\n  public dispose(): void {\n    // Dispose of all registered disposables\n    this.disposables.forEach(d => d.dispose());\n    this.disposables = [];\n\n    // Dispose of the chat participant if it exists and has dispose method\n    if (this.participant && typeof this.participant.dispose === 'function') {\n      this.participant.dispose();\n    }\n    logger.info('Codessa Chat Participant disposed');\n  }\n}\n"]}