{"version": 3, "file": "phiProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/phiProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAEpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;;;;GAKG;AACH,MAAa,WAAY,SAAQ,iCAAe;IACrC,UAAU,GAAG,KAAK,CAAC;IACnB,WAAW,GAAG,eAAe,CAAC;IAC9B,WAAW,GAAG,mDAAmD,CAAC;IAClE,OAAO,GAAG,uEAAuE,CAAC;IAClF,cAAc,GAAG,IAAI,CAAC,CAAC,gCAAgC;IACvD,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,6CAA6C,CAAC;IAChE,YAAY,GAAG,kCAAkC,CAAC;IAEnD,MAAM,GAAQ,IAAI,CAAC;IACnB,YAAY,GAAkD,aAAa,CAAC;IAEpF,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBAClE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,0BAA0B;QAC1B,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;QACpC,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7E,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC/B,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,IAAI,CAAC,MAAM,EAAE,CAAC;YACnD,eAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAC9D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,MAAM,OAAO,GAA2B;gBACtC,cAAc,EAAE,kBAAkB;aACnC,CAAC;YAEF,iDAAiD;YACjD,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,EAAE,CAAC;oBACxC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,MAAM,EAAE,CAAC;gBAChD,CAAC;qBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE,CAAC;oBACzC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;gBAC9B,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,EAAE,qBAAqB;gBACrC,OAAO;aACR,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,2DAA2D,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAC9F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,uCAAuC;QACvC,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,EAAE,CAAC;YACxC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAC/C,CAAC;QACD,iEAAiE;QACjE,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,MAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;wBAC7C,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBACvG,CAAC;YACH,CAAC;YAED,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,kCAAkC;YAClC,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,EAAE,CAAC;gBACxC,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;YAC9E,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;gBAC1C,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;YACzE,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE,CAAC;gBACzC,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;YACxE,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,IAAI,YAAY,GAAG,yBAAyB,CAAC;YAE7C,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,kBAAkB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAC5G,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB,CACnC,MAAyB,EACzB,OAAe,EACf,eAAiC;QAEjC,qBAAqB;QACrB,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,wCAAwC;QACxC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,eAAe,MAAM,CAAC,YAAY,MAAM,CAAC;YACrD,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,aAAa,OAAO,CAAC,OAAO,MAAM,CAAC;oBAC/C,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,kBAAkB,OAAO,CAAC,OAAO,MAAM,CAAC;oBACpD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,MAAM,IAAI,eAAe,OAAO,CAAC,OAAO,MAAM,CAAC;oBACjD,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,aAAa,MAAM,CAAC,MAAM,MAAM,CAAC;YAC7C,CAAC;YAED,gFAAgF;YAChF,MAAM,IAAI,iBAAiB,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,8BAA8B;YAC9B,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,MAAM,CAAC;YACzC,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,UAAU,OAAO,CAAC,OAAO,MAAM,CAAC;oBAC5C,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,cAAc,OAAO,CAAC,OAAO,MAAM,CAAC;oBAChD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,MAAM,CAAC;oBACrC,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,UAAU,MAAM,CAAC,MAAM,MAAM,CAAC;YAC1C,CAAC;YAED,gFAAgF;YAChF,MAAM,IAAI,aAAa,CAAC;QAC1B,CAAC;QAED,uBAAuB;QACvB,MAAM,WAAW,GAAG;YAClB,MAAM,EAAE,MAAM;YACd,UAAU,EAAE;gBACV,cAAc,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG;gBACvC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,gBAAgB,EAAE,KAAK;gBACvB,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC;aACrD;SACF,CAAC;QAEF,eAAM,CAAC,KAAK,CAAC,gCAAgC,OAAO,mBAAmB,CAAC,CAAC;QAEzE,uBAAuB;QACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE;YAC5D,MAAM,EAAE,eAAe,EAAE,MAAM;SAChC,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;QAE7B,iEAAiE;QACjE,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/C,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;gBAC7B,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;YACrC,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO;YACP,YAAY,EAAE,MAAM;YACpB,KAAK,EAAE;gBACL,YAAY,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;gBAClD,gBAAgB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;aACxD;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAC9B,MAAyB,EACzB,OAAe,EACf,eAAiC;QAEjC,qBAAqB;QACrB,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,wCAAwC;QACxC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,eAAe,MAAM,CAAC,YAAY,MAAM,CAAC;YACrD,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,aAAa,OAAO,CAAC,OAAO,MAAM,CAAC;oBAC/C,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,kBAAkB,OAAO,CAAC,OAAO,MAAM,CAAC;oBACpD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,MAAM,IAAI,eAAe,OAAO,CAAC,OAAO,MAAM,CAAC;oBACjD,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,aAAa,MAAM,CAAC,MAAM,MAAM,CAAC;YAC7C,CAAC;YAED,gFAAgF;YAChF,MAAM,IAAI,iBAAiB,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,8BAA8B;YAC9B,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,MAAM,CAAC;YACzC,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,UAAU,OAAO,CAAC,OAAO,MAAM,CAAC;oBAC5C,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,cAAc,OAAO,CAAC,OAAO,MAAM,CAAC;oBAChD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,MAAM,CAAC;oBACrC,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,UAAU,MAAM,CAAC,MAAM,MAAM,CAAC;YAC1C,CAAC;YAED,gFAAgF;YAChF,MAAM,IAAI,aAAa,CAAC;QAC1B,CAAC;QAED,kCAAkC;QAClC,MAAM,WAAW,GAAG;YAClB,KAAK,EAAE,OAAO;YACd,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBACP,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,WAAW,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG;gBACpC,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC;aACrD;SACF,CAAC;QAEF,eAAM,CAAC,KAAK,CAAC,gCAAgC,OAAO,aAAa,CAAC,CAAC;QAEnE,uBAAuB;QACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE;YACpE,MAAM,EAAE,eAAe,EAAE,MAAM;SAChC,CAAC,CAAC;QAEH,wBAAwB;QACxB,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE;YACrC,YAAY,EAAE,MAAM;YACpB,KAAK,EAAE;gBACL,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB;gBAC7C,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,UAAU;aAC3C;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAC7B,MAAyB,EACzB,OAAe,EACf,eAAiC;QAEjC,wCAAwC;QACxC,MAAM,QAAQ,GAA8E,EAAE,CAAC;QAE/F,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAED,wCAAwC;QACxC,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,QAAQ;YAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;YACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG;YACnC,IAAI,EAAE,MAAM,CAAC,aAAa;SAC3B,CAAC;QAEF,eAAM,CAAC,KAAK,CAAC,gCAAgC,OAAO,mBAAmB,CAAC,CAAC;QAEzE,uBAAuB;QACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,GAAG,OAAO,GAAG,0CAA0C,EAAE,WAAW,EAAE;YAClI,MAAM,EAAE,eAAe,EAAE,MAAM;SAChC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;YACvD,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,IAAI,MAAM;YAC9D,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;SAC3B,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,0BAA0B,CACtC,MAAyB,EACzB,OAAe,EACf,eAAiC;QAEjC,6CAA6C;QAC7C,MAAM,QAAQ,GAA8E,EAAE,CAAC;QAE/F,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAED,iDAAiD;QACjD,MAAM,WAAW,GAAG;YAClB,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,QAAQ;YAClB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;YACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG;YACnC,IAAI,EAAE,MAAM,CAAC,aAAa;SAC3B,CAAC;QAEF,eAAM,CAAC,KAAK,CAAC,gCAAgC,OAAO,sBAAsB,CAAC,CAAC;QAE5E,uBAAuB;QACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE;YACxE,MAAM,EAAE,eAAe,EAAE,MAAM;SAChC,CAAC,CAAC;QAEH,mCAAmC;QACnC,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;YACvD,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,IAAI,MAAM;YAC9D,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;SAC3B,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,gDAAgD;gBAChD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAEpD,wBAAwB;gBACxB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM;qBAChC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CACjB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CACrC;qBACA,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;oBAChB,EAAE,EAAE,CAAC,CAAC,IAAI;oBACV,IAAI,EAAE,CAAC,CAAC,IAAI;oBACZ,WAAW,EAAE,SAAS,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;oBACpD,aAAa,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC;oBACpD,WAAW,EAAE,cAAc;iBAC5B,CAAC,CAAC,CAAC;gBAEN,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,oDAAoD;QACpD,OAAO;YACL;gBACE,EAAE,EAAE,kCAAkC;gBACtC,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,gDAAgD;gBAC7D,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,oCAAoC;gBACxC,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,2CAA2C;gBACxD,aAAa,EAAE,MAAM;gBACrB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,mCAAmC;gBACvC,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,0CAA0C;gBACvD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,oCAAoC;gBACxC,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,2CAA2C;gBACxD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,wCAAwC;gBACrD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,qBAAqB;aACnC;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,wBAAwB,CAAC,OAAe;QAC9C,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,CAAC,UAAU;IACzB,CAAC;IAED;;SAEK;IACG,UAAU,CAAC,KAAa;QAC9B,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;YACjB,OAAO,GAAG,KAAK,IAAI,CAAC;QACtB,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YAC/B,OAAO,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3C,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YACtC,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,8DAA8D;aACxE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,YAAY,KAAK,aAAa,EAAE,CAAC;gBACxC,qDAAqD;gBACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;oBAC/C,MAAM,EAAE,oBAAoB;oBAC5B,UAAU,EAAE;wBACV,cAAc,EAAE,EAAE;wBAClB,gBAAgB,EAAE,KAAK;qBACxB;iBACF,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAClB,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,gEAAgE,OAAO,IAAI;qBACrF,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;gBAC1C,wCAAwC;gBACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACpD,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;gBAE9E,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,UAAU,OAAO,gEAAgE,OAAO,IAAI;qBACtG,CAAC;gBACJ,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,wDAAwD,OAAO,IAAI;iBAC7E,CAAC;YACJ,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE,CAAC;gBACzC,wCAAwC;gBACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,GAAG,OAAO,GAAG,0CAA0C,EAAE;oBACrH,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;oBAC9C,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;oBAC3C,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,gEAAgE,OAAO,IAAI;qBACrF,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,qDAAqD;gBACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBAC3D,KAAK,EAAE,OAAO;oBACd,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;oBAC9C,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;oBAC3C,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,mDAAmD,OAAO,IAAI;qBACxE,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uDAAuD;aACjE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,IAAI,YAAY,GAAG,8BAA8B,CAAC;YAElD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,6BAA6B;gBAC1C,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;aACtD;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,sBAAsB;gBACnC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,+CAA+C;gBAC5D,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,8BAA8B;gBAC3C,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,kCAAkC;oBAClC,oCAAoC;oBACpC,mCAAmC;oBACnC,oCAAoC;oBACpC,iBAAiB;oBACjB,KAAK;iBACN;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAxqBD,kCAwqBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Base<PERSON><PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult, LLMModelInfo } from '../llmProvider';\nimport { logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for Microsoft Phi models (Phi-2, Phi-3)\n * \n * Phi models are small but powerful models from Microsoft Research\n * They can be used via Hugging Face, Ollama, or Azure OpenAI\n */\nexport class PhiProvider extends BaseLLMProvider {\n  readonly providerId = 'phi';\n  readonly displayName = 'Microsoft Phi';\n  readonly description = 'Small but powerful models from Microsoft Research';\n  readonly website = 'https://www.microsoft.com/en-us/research/blog/phi-3-technical-report/';\n  readonly requiresApiKey = true; // Required for Hugging Face API\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api-inference.huggingface.co/models';\n  readonly defaultModel = 'microsoft/phi-3-mini-4k-instruct';\n\n  private client: any = null;\n  private endpointType: 'huggingface' | 'ollama' | 'azure' | 'custom' = 'huggingface';\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        logger.info('Phi configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n        \n    // Determine endpoint type\n    if (baseUrl.includes('huggingface.co')) {\n      this.endpointType = 'huggingface';\n    } else if (baseUrl.includes('localhost:11434') || baseUrl.includes('ollama')) {\n      this.endpointType = 'ollama';\n    } else if (baseUrl.includes('azure.com')) {\n      this.endpointType = 'azure';\n    } else {\n      this.endpointType = 'custom';\n    }\n\n    if (this.endpointType === 'huggingface' && !apiKey) {\n      logger.warn('Hugging Face API key not set for Phi provider.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      const headers: Record<string, string> = {\n        'Content-Type': 'application/json'\n      };\n            \n      // Add API key if provided and using Hugging Face\n      if (apiKey) {\n        if (this.endpointType === 'huggingface') {\n          headers['Authorization'] = `Bearer ${apiKey}`;\n        } else if (this.endpointType === 'azure') {\n          headers['api-key'] = apiKey;\n        }\n      }\n            \n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 60000, // 60 seconds timeout\n        headers\n      });\n            \n      logger.info(`Phi client initialized successfully with endpoint type: ${this.endpointType}`);\n    } catch (error) {\n      logger.error('Failed to initialize Phi client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    // For Hugging Face, we need an API key\n    if (this.endpointType === 'huggingface') {\n      return !!this.client && !!this.config.apiKey;\n    }\n    // For other endpoints, we just need the client to be initialized\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using Phi models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    _tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'Phi provider not configured' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n            \n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            logger.info('Phi request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          logger.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Handle different endpoint types\n      if (this.endpointType === 'huggingface') {\n        return await this.generateWithHuggingFace(params, modelId, abortController);\n      } else if (this.endpointType === 'ollama') {\n        return await this.generateWithOllama(params, modelId, abortController);\n      } else if (this.endpointType === 'azure') {\n        return await this.generateWithAzure(params, modelId, abortController);\n      } else {\n        return await this.generateWithCustomEndpoint(params, modelId, abortController);\n      }\n    } catch (error: any) {\n      logger.error('Phi generate error:', error);\n      let errorMessage = 'Failed to call Phi API.';\n\n      if (error.response) {\n        errorMessage = `Phi API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * Generate with Hugging Face Inference API\n     */\n  private async generateWithHuggingFace(\n    params: LLMGenerateParams,\n    modelId: string,\n    abortController?: AbortController\n  ): Promise<LLMGenerateResult> {\n    // Prepare the prompt\n    let prompt = '';\n        \n    // Phi-3 uses a specific format for chat\n    if (modelId.includes('phi-3')) {\n      if (params.systemPrompt) {\n        prompt += `<|system|>\\n${params.systemPrompt}\\n\\n`;\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            prompt += `<|user|>\\n${message.content}\\n\\n`;\n          } else if (message.role === 'assistant') {\n            prompt += `<|assistant|>\\n${message.content}\\n\\n`;\n          } else if (message.role === 'system') {\n            prompt += `<|system|>\\n${message.content}\\n\\n`;\n          }\n        }\n      } else {\n        // Just add the user prompt\n        prompt += `<|user|>\\n${params.prompt}\\n\\n`;\n      }\n            \n      // Add the assistant prefix to indicate we want the model to generate a response\n      prompt += '<|assistant|>\\n';\n    } else {\n      // Phi-2 uses a simpler format\n      if (params.systemPrompt) {\n        prompt += `${params.systemPrompt}\\n\\n`;\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            prompt += `Human: ${message.content}\\n\\n`;\n          } else if (message.role === 'assistant') {\n            prompt += `Assistant: ${message.content}\\n\\n`;\n          } else if (message.role === 'system') {\n            prompt += `${message.content}\\n\\n`;\n          }\n        }\n      } else {\n        // Just add the user prompt\n        prompt += `Human: ${params.prompt}\\n\\n`;\n      }\n            \n      // Add the assistant prefix to indicate we want the model to generate a response\n      prompt += 'Assistant: ';\n    }\n\n    // Prepare request data\n    const requestData = {\n      inputs: prompt,\n      parameters: {\n        max_new_tokens: params.maxTokens || 512,\n        temperature: params.temperature || 0.7,\n        top_p: 0.95,\n        do_sample: true,\n        return_full_text: false,\n        stop: params.stopSequences || ['Human:', '<|user|>']\n      }\n    };\n\n    logger.debug(`Sending request to Phi model ${modelId} via Hugging Face`);\n\n    // Make the API request\n    const response = await this.client.post(modelId, requestData, {\n      signal: abortController?.signal\n    });\n\n    // Parse the response\n    const result = response.data;\n        \n    // Hugging Face Inference API returns an array of generated texts\n    let content = '';\n    if (Array.isArray(result) && result.length > 0) {\n      if (result[0].generated_text) {\n        content = result[0].generated_text;\n      }\n    }\n\n    return {\n      content,\n      finishReason: 'stop',\n      usage: {\n        promptTokens: prompt.length / 4, // Rough estimate\n        completionTokens: content.length / 4, // Rough estimate\n      }\n    };\n  }\n\n  /**\n     * Generate with Ollama\n     */\n  private async generateWithOllama(\n    params: LLMGenerateParams,\n    modelId: string,\n    abortController?: AbortController\n  ): Promise<LLMGenerateResult> {\n    // Prepare the prompt\n    let prompt = '';\n        \n    // Phi-3 uses a specific format for chat\n    if (modelId.includes('phi-3')) {\n      if (params.systemPrompt) {\n        prompt += `<|system|>\\n${params.systemPrompt}\\n\\n`;\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            prompt += `<|user|>\\n${message.content}\\n\\n`;\n          } else if (message.role === 'assistant') {\n            prompt += `<|assistant|>\\n${message.content}\\n\\n`;\n          } else if (message.role === 'system') {\n            prompt += `<|system|>\\n${message.content}\\n\\n`;\n          }\n        }\n      } else {\n        // Just add the user prompt\n        prompt += `<|user|>\\n${params.prompt}\\n\\n`;\n      }\n            \n      // Add the assistant prefix to indicate we want the model to generate a response\n      prompt += '<|assistant|>\\n';\n    } else {\n      // Phi-2 uses a simpler format\n      if (params.systemPrompt) {\n        prompt += `${params.systemPrompt}\\n\\n`;\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            prompt += `Human: ${message.content}\\n\\n`;\n          } else if (message.role === 'assistant') {\n            prompt += `Assistant: ${message.content}\\n\\n`;\n          } else if (message.role === 'system') {\n            prompt += `${message.content}\\n\\n`;\n          }\n        }\n      } else {\n        // Just add the user prompt\n        prompt += `Human: ${params.prompt}\\n\\n`;\n      }\n            \n      // Add the assistant prefix to indicate we want the model to generate a response\n      prompt += 'Assistant: ';\n    }\n\n    // Prepare request data for Ollama\n    const requestData = {\n      model: modelId,\n      prompt: prompt,\n      stream: false,\n      options: {\n        temperature: params.temperature ?? 0.7,\n        num_predict: params.maxTokens ?? 512,\n        stop: params.stopSequences ?? ['Human:', '<|user|>']\n      }\n    };\n        \n    logger.debug(`Sending request to Phi model ${modelId} via Ollama`);\n        \n    // Make the API request\n    const response = await this.client.post('/api/generate', requestData, {\n      signal: abortController?.signal\n    });\n        \n    // Parse Ollama response\n    return {\n      content: response.data.response || '',\n      finishReason: 'stop',\n      usage: {\n        promptTokens: response.data.prompt_eval_count,\n        completionTokens: response.data.eval_count,\n      }\n    };\n  }\n\n  /**\n     * Generate with Azure OpenAI\n     */\n  private async generateWithAzure(\n    params: LLMGenerateParams,\n    modelId: string,\n    abortController?: AbortController\n  ): Promise<LLMGenerateResult> {\n    // Prepare messages for Azure OpenAI API\n    const messages: { role: string; content: string; name?: string; tool_call_id?: string }[] = [];\n        \n    if (params.systemPrompt) {\n      messages.push({\n        role: 'system',\n        content: params.systemPrompt\n      });\n    }\n        \n    if (params.history && params.history.length > 0) {\n      messages.push(...params.history);\n    } else {\n      messages.push({\n        role: 'user',\n        content: params.prompt\n      });\n    }\n        \n    // Prepare request data for Azure OpenAI\n    const requestData = {\n      messages: messages,\n      temperature: params.temperature ?? 0.7,\n      max_tokens: params.maxTokens ?? 512,\n      stop: params.stopSequences\n    };\n        \n    logger.debug(`Sending request to Phi model ${modelId} via Azure OpenAI`);\n        \n    // Make the API request\n    const response = await this.client.post('/openai/deployments/' + modelId + '/chat/completions?api-version=2023-05-15', requestData, {\n      signal: abortController?.signal\n    });\n        \n    // Parse Azure OpenAI response\n    return {\n      content: response.data.choices[0].message.content || '',\n      finishReason: response.data.choices[0].finish_reason || 'stop',\n      usage: response.data.usage\n    };\n  }\n\n  /**\n     * Generate with custom endpoint (OpenAI-compatible API)\n     */\n  private async generateWithCustomEndpoint(\n    params: LLMGenerateParams,\n    modelId: string,\n    abortController?: AbortController\n  ): Promise<LLMGenerateResult> {\n    // Prepare messages for OpenAI-compatible API\n    const messages: { role: string; content: string; name?: string; tool_call_id?: string }[] = [];\n        \n    if (params.systemPrompt) {\n      messages.push({\n        role: 'system',\n        content: params.systemPrompt\n      });\n    }\n        \n    if (params.history && params.history.length > 0) {\n      messages.push(...params.history);\n    } else {\n      messages.push({\n        role: 'user',\n        content: params.prompt\n      });\n    }\n        \n    // Prepare request data for OpenAI-compatible API\n    const requestData = {\n      model: modelId,\n      messages: messages,\n      temperature: params.temperature ?? 0.7,\n      max_tokens: params.maxTokens ?? 512,\n      stop: params.stopSequences\n    };\n        \n    logger.debug(`Sending request to Phi model ${modelId} via custom endpoint`);\n        \n    // Make the API request\n    const response = await this.client.post('/chat/completions', requestData, {\n      signal: abortController?.signal\n    });\n        \n    // Parse OpenAI-compatible response\n    return {\n      content: response.data.choices[0].message.content || '',\n      finishReason: response.data.choices[0].finish_reason || 'stop',\n      usage: response.data.usage\n    };\n  }\n\n  /**\n     * List available Phi models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    if (this.endpointType === 'ollama') {\n      try {\n        // For Ollama, we can fetch the available models\n        const response = await this.client.get('/api/tags');\n                \n        // Filter for Phi models\n        const models = response.data.models\n          .filter((m: any) => \n            m.name.toLowerCase().includes('phi')\n          )\n          .map((m: any) => ({\n            id: m.name,\n            name: m.name,\n            description: `Size: ${this.formatSize(m.size || 0)}`,\n            contextWindow: this.getContextWindowForModel(m.name),\n            pricingInfo: 'Free (local)'\n          }));\n                \n        return models;\n      } catch (error) {\n        logger.error('Failed to fetch Phi models from Ollama:', error);\n      }\n    }\n        \n    // Return predefined models for other endpoint types\n    return [\n      {\n        id: 'microsoft/phi-3-mini-4k-instruct',\n        name: 'Phi-3 Mini 4K Instruct',\n        description: 'Latest Phi-3 Mini model with 4K context window',\n        contextWindow: 4096,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'microsoft/phi-3-mini-128k-instruct',\n        name: 'Phi-3 Mini 128K Instruct',\n        description: 'Phi-3 Mini model with 128K context window',\n        contextWindow: 131072,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'microsoft/phi-3-small-8k-instruct',\n        name: 'Phi-3 Small 8K Instruct',\n        description: 'Phi-3 Small model with 8K context window',\n        contextWindow: 8192,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'microsoft/phi-3-medium-4k-instruct',\n        name: 'Phi-3 Medium 4K Instruct',\n        description: 'Phi-3 Medium model with 4K context window',\n        contextWindow: 4096,\n        pricingInfo: 'Free (open weights)'\n      },\n      {\n        id: 'microsoft/phi-2',\n        name: 'Phi-2',\n        description: 'Original Phi-2 model (2.7B parameters)',\n        contextWindow: 2048,\n        pricingInfo: 'Free (open weights)'\n      }\n    ];\n  }\n\n  /**\n     * Get the context window size for a specific model\n     */\n  private getContextWindowForModel(modelId: string): number {\n    if (modelId.includes('128k')) {\n      return 131072;\n    } else if (modelId.includes('8k')) {\n      return 8192;\n    } else if (modelId.includes('4k')) {\n      return 4096;\n    } else if (modelId.includes('phi-2')) {\n      return 2048;\n    }\n    return 4096; // Default\n  }\n\n  /**\n     * Format file size in bytes to a human-readable string\n     */\n  private formatSize(bytes: number): string {\n    if (bytes < 1024) {\n      return `${bytes} B`;\n    } else if (bytes < 1024 * 1024) {\n      return `${(bytes / 1024).toFixed(2)} KB`;\n    } else if (bytes < 1024 * 1024 * 1024) {\n      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;\n    } else {\n      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;\n    }\n  }\n\n  /**\n     * Test connection to Phi\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'Phi client not initialized. Please check your configuration.'\n      };\n    }\n\n    try {\n      if (this.endpointType === 'huggingface') {\n        // Simple test request to check if the API is working\n        const response = await this.client.post(modelId, {\n          inputs: 'def hello_world():',\n          parameters: {\n            max_new_tokens: 10,\n            return_full_text: false\n          }\n        });\n\n        if (response.data) {\n          return {\n            success: true,\n            message: `Successfully connected to Hugging Face API and tested model '${modelId}'.`\n          };\n        }\n      } else if (this.endpointType === 'ollama') {\n        // For Ollama, check if the model exists\n        const response = await this.client.get('/api/tags');\n        const modelExists = response.data.models.some((m: any) => m.name === modelId);\n                \n        if (!modelExists) {\n          return {\n            success: false,\n            message: `Model '${modelId}' not found. You may need to pull it first with 'ollama pull ${modelId}'.`\n          };\n        }\n                \n        return {\n          success: true,\n          message: `Successfully connected to Ollama and verified model '${modelId}'.`\n        };\n      } else if (this.endpointType === 'azure') {\n        // For Azure, make a simple test request\n        const response = await this.client.post('/openai/deployments/' + modelId + '/chat/completions?api-version=2023-05-15', {\n          messages: [{ role: 'user', content: 'Hello' }],\n          max_tokens: 10\n        });\n                \n        if (response.data && response.data.choices) {\n          return {\n            success: true,\n            message: `Successfully connected to Azure OpenAI API and tested model '${modelId}'.`\n          };\n        }\n      } else {\n        // For custom endpoints, assume OpenAI-compatible API\n        const response = await this.client.post('/chat/completions', {\n          model: modelId,\n          messages: [{ role: 'user', content: 'Hello' }],\n          max_tokens: 10\n        });\n                \n        if (response.data && response.data.choices) {\n          return {\n            success: true,\n            message: `Successfully connected to API and tested model '${modelId}'.`\n          };\n        }\n      }\n            \n      return {\n        success: false,\n        message: 'Connected to API but received an unexpected response.'\n      };\n    } catch (error: any) {\n      logger.error('Phi connection test failed:', error);\n      let errorMessage = 'Failed to connect to Phi API';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'endpointType',\n        name: 'Endpoint Type',\n        description: 'The type of endpoint to use',\n        required: true,\n        type: 'select',\n        options: ['huggingface', 'ollama', 'azure', 'custom']\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The API endpoint URL',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'API key (required for Hugging Face and Azure)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default Phi model to use',\n        required: false,\n        type: 'select',\n        options: [\n          'microsoft/phi-3-mini-4k-instruct',\n          'microsoft/phi-3-mini-128k-instruct',\n          'microsoft/phi-3-small-8k-instruct',\n          'microsoft/phi-3-medium-4k-instruct',\n          'microsoft/phi-2',\n          'phi'\n        ]\n      }\n    ];\n  }\n}\n"]}