"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowPanel = void 0;
const vscode = __importStar(require("vscode"));
const utils_1 = require("../../utils");
const workflowStorage_1 = require("./workflowStorage");
const logger_1 = require("../../logger");
const managers_1 = require("src/managers");
// Revolutionary helper function to convert workflow definitions to UI workflows
function convertToUIWorkflow(workflow) {
    // Check if it's a GraphDefinition by looking for nodes property
    const isGraphDefinition = 'nodes' in workflow;
    const graphWorkflow = workflow;
    const uiWorkflow = {
        id: workflow.id,
        name: workflow.name,
        description: workflow.description || 'No description provided',
        version: isGraphDefinition ? graphWorkflow.version || '1.0.0' : workflow.version,
        steps: isGraphDefinition ? graphWorkflow.nodes : workflow.steps,
        engine: isGraphDefinition ? 'codessa' : 'original'
    };
    // Add revolutionary features if available
    if (isGraphDefinition && graphWorkflow.metadata?.revolutionaryFeatures) {
        uiWorkflow.revolutionaryFeatures = graphWorkflow.metadata.revolutionaryFeatures;
        uiWorkflow.goddessBlessing = graphWorkflow.metadata.goddessBlessing;
        uiWorkflow.aiCapabilities = graphWorkflow.metadata.aiCapabilities;
        uiWorkflow.enhancementLevel = graphWorkflow.metadata.enhancementLevel;
    }
    return uiWorkflow;
}
/**
 * Workflow Panel
 * Provides a UI for managing workflows
 */
class WorkflowPanel {
    // Persistent workflow storage
    static _loadedWorkflows = [];
    static viewType = 'codessa.workflowPanel';
    static currentPanel;
    _panel;
    _extensionUri;
    _disposables = [];
    constructor(panel, extensionUri) {
        // Load workflows from disk
        workflowStorage_1.WorkflowStorage.loadWorkflows(vscode.extensions.getExtension('TekNerds-ITS.codessa').exports.context)
            .then(workflows => {
            WorkflowPanel._loadedWorkflows = workflows;
            this._update();
        });
        this._panel = panel;
        this._extensionUri = extensionUri;
        // Set the webview's initial html content
        this._update();
        // Listen for when the panel is disposed
        // This happens when the user closes the panel or when the panel is closed programmatically
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
        // Update the content based on view changes
        this._panel.onDidChangeViewState(() => {
            if (this._panel.visible) {
                this._update();
            }
        }, null, this._disposables);
        // Handle messages from the webview
        this._panel.webview.onDidReceiveMessage(async (message) => {
            // Tool picker: respond with all tools & actions
            if (message.command === 'getAvailableToolsWithActions') {
                try {
                    const toolRegistryModule = await Promise.resolve().then(() => __importStar(require('../../tools/toolRegistry')));
                    const { ToolRegistry } = toolRegistryModule;
                    // Recursively serialize tools and actions for the webview
                    // Move function declaration outside of block to fix strict mode error
                    const serializeTool = function (tool) {
                        const out = {
                            id: tool.id,
                            name: tool.name || tool.id,
                            description: tool.description || '',
                            actions: undefined
                        };
                        if (tool.actions) {
                            out.actions = {};
                            for (const [subId, subTool] of Object.entries(tool.actions)) {
                                out.actions[subId] = serializeTool(subTool);
                            }
                        }
                        return out;
                    };
                    const tools = ToolRegistry.instance.getAllTools().map(serializeTool);
                    this._panel.webview.postMessage({ type: 'availableToolsWithActions', tools });
                }
                catch (err) {
                    this._panel.webview.postMessage({ type: 'availableToolsWithActions', tools: [], error: String(err) });
                }
                return;
            }
            if (message.command === 'getWorkflows') {
                this._panel.webview.postMessage({ type: 'workflowsList', workflows: WorkflowPanel._loadedWorkflows });
                return;
            }
            try {
                logger_1.Logger.instance.info(`Received message from webview: ${message.command}`);
                switch (message.command) {
                    case 'createWorkflow':
                        vscode.commands.executeCommand('codessa.createWorkflow');
                        break;
                    case 'createLangGraphWorkflow':
                        vscode.commands.executeCommand('codessa.createCodessaWorkflow');
                        break;
                    case 'createRevolutionaryWorkflow':
                        vscode.commands.executeCommand('codessa.createRevolutionaryWorkflow');
                        break;
                    case 'activateGoddessMode':
                        vscode.commands.executeCommand('codessa.activateGoddessMode');
                        break;
                    case 'enableQuantumAnalysis':
                        vscode.commands.executeCommand('codessa.quantumAnalysis');
                        break;
                    case 'editWorkflow':
                        if (message.engine === 'codessa') {
                            vscode.commands.executeCommand('codessa.editCodessaWorkflow', message.workflowId);
                        }
                        else {
                            vscode.commands.executeCommand('codessa.editWorkflow', message.workflowId);
                        }
                        break;
                    case 'deleteWorkflow':
                        if (message.engine === 'codessa') {
                            vscode.commands.executeCommand('codessa.deleteCodessaWorkflow', message.workflowId);
                        }
                        else {
                            vscode.commands.executeCommand('codessa.deleteWorkflow', message.workflowId);
                        }
                        break;
                    case 'runWorkflow':
                        if (message.engine === 'codessa') {
                            vscode.commands.executeCommand('codessa.runCodessaWorkflow', message.workflowId);
                        }
                        else {
                            vscode.commands.executeCommand('codessa.runWorkflow', message.workflowId);
                        }
                        break;
                    case 'refreshWorkflows':
                        this._update();
                        break;
                    case 'saveWorkflow':
                        await this._handleSaveWorkflow(message.workflow);
                        this._update();
                        break;
                    default:
                        logger_1.Logger.instance.warn(`Unknown command: ${message.command}`);
                }
            }
            catch (error) {
                logger_1.Logger.instance.error('Error handling webview message:', error);
            }
        }, null, this._disposables);
    }
    static createOrShow(extensionUri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;
        // If we already have a panel, show it.
        if (WorkflowPanel.currentPanel) {
            WorkflowPanel.currentPanel._panel.reveal(column);
            return;
        }
        // Otherwise, create a new panel.
        const panel = vscode.window.createWebviewPanel(WorkflowPanel.viewType, 'Codessa Workflows', column || vscode.ViewColumn.One, {
            // Enable javascript in the webview
            enableScripts: true,
            // And restrict the webview to only loading content from our extension's directory
            localResourceRoots: [
                vscode.Uri.joinPath(extensionUri, 'media')
            ]
        });
        WorkflowPanel.currentPanel = new WorkflowPanel(panel, extensionUri);
    }
    /**
       * Handle saving a workflow from the webview
       */
    async _handleSaveWorkflow(workflow) {
        WorkflowPanel._loadedWorkflows.push({
            id: `wf_${Date.now()}`,
            name: workflow.name,
            steps: workflow.steps,
            created: new Date().toISOString(),
        });
        await workflowStorage_1.WorkflowStorage.saveWorkflows(vscode.extensions.getExtension('TekNerds-ITS.codessa').exports.context, WorkflowPanel._loadedWorkflows);
        logger_1.Logger.instance.info(`Saved workflow: ${workflow.name} (${workflow.steps.length} steps)`);
    }
    async _update() {
        const webview = this._panel.webview;
        this._panel.title = 'Codessa Workflows';
        this._panel.webview.html = await this._getHtmlForWebview(webview);
    }
    async _getHtmlForWebview(webview) {
        // Get the local paths to resources
        const stylePath = vscode.Uri.joinPath(this._extensionUri, 'media', 'workflow.css');
        const scriptPath = vscode.Uri.joinPath(this._extensionUri, 'media', 'workflow.js');
        const toolPickerScriptPath = vscode.Uri.joinPath(this._extensionUri, 'media', 'toolPicker.js');
        // Convert to webview URIs
        const styleUri = webview.asWebviewUri(stylePath);
        const scriptUri = webview.asWebviewUri(scriptPath);
        const toolPickerScriptUri = webview.asWebviewUri(toolPickerScriptPath);
        // Use a nonce to only allow specific scripts to be run
        const nonce = (0, utils_1.getNonce)();
        try {
            // Get workflows from original registry
            const workflowsData = await vscode.commands.executeCommand('codessa.getWorkflows') || [];
            // Get workflows from Codessa registry
            const allWorkflows = (0, managers_1.getAllWorkflows)();
            // Create a map of existing workflow IDs for faster lookup
            const existingWorkflowIds = new Set(workflowsData.map(w => w.id));
            // Filter out workflows that are already in workflowsData
            const filteredCodessaWorkflows = allWorkflows.filter(wf => {
                return !existingWorkflowIds.has(wf.id);
            });
            logger_1.Logger.instance.info(`Found ${workflowsData.length} original workflows and ${filteredCodessaWorkflows.length} Codessa workflows`);
            // Example of using getWorkflowById and createWorkflowInstance
            if (filteredCodessaWorkflows.length > 0) {
                const sampleWorkflowId = filteredCodessaWorkflows[0].id;
                const workflowDetails = (0, managers_1.getWorkflowById)(sampleWorkflowId);
                if (workflowDetails) {
                    // Access the name directly from workflowDetails
                    // GraphDefinition doesn't have a workflow property, but has a name property directly
                    const workflowName = workflowDetails.name || 'Unknown';
                    logger_1.Logger.instance.debug(`Sample workflow details: ${workflowName}`);
                    try {
                        // Create an instance but don't execute it
                        const instance = (0, managers_1.createWorkflowInstance)(sampleWorkflowId);
                        // Check instance type and access properties accordingly
                        if (instance && typeof instance.getDefinition === 'function') {
                            // It's a Workflow or Codessa instance with getDefinition
                            const def = instance.getDefinition();
                            logger_1.Logger.instance.debug(`Created workflow instance for: ${def && def.name ? def.name : workflowName}`);
                        }
                        else if (instance) {
                            // It's a different kind of instance
                            logger_1.Logger.instance.debug(`Created workflow instance for workflow: ${workflowName}`);
                        }
                        else {
                            logger_1.Logger.instance.warn(`Could not create workflow instance for workflow: ${workflowName}`);
                        }
                    }
                    catch (error) {
                        logger_1.Logger.instance.error(`Error creating workflow instance: ${error}`);
                    }
                }
            }
            // Create combined workflows array using our revolutionary helper function
            const combinedWorkflows = [
                ...workflowsData,
                ...await Promise.all(filteredCodessaWorkflows.map(async (wf) => {
                    const uiWorkflow = convertToUIWorkflow(wf);
                    // Add metrics from registry if available
                    try {
                        const { workflowRegistry } = await Promise.resolve().then(() => __importStar(require('../../agents/workflows/workflowRegistry')));
                        const metrics = workflowRegistry.getWorkflowMetrics(wf.id);
                        if (metrics) {
                            uiWorkflow.metrics = metrics;
                        }
                    }
                    catch (error) {
                        logger_1.Logger.instance.warn('Could not load workflow metrics:', error);
                    }
                    return uiWorkflow;
                }))
            ];
            // Convert workflows to JSON string for the webview
            const workflowsJson = JSON.stringify(combinedWorkflows);
            return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
    <link href="${styleUri}" rel="stylesheet">
    <title>Codessa Workflows</title>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✨ Revolutionary Workflows</h1>
            <div class="goddess-blessing">
                <p class="blessing-text">🌟 "May your workflows be blessed with divine efficiency and quantum insights" - Codessa</p>
            </div>
            <div class="actions">
                <button id="create-workflow" class="primary-button">Create Workflow</button>
                <button id="create-codessa-workflow" class="primary-button">Create Codessa Workflow</button>
                <button id="create-revolutionary-workflow" class="revolutionary-button">✨ Create Revolutionary Workflow</button>
                <button id="activate-goddess-mode" class="goddess-button">👑 Activate Goddess Mode</button>
                <button id="enable-quantum-analysis" class="quantum-button">🔬 Quantum Analysis</button>
                <button id="refresh-workflows" class="secondary-button">Refresh</button>
            </div>
        </div>

        <div class="revolutionary-features-panel">
            <h3>🚀 Revolutionary Features Available</h3>
            <div class="features-grid">
                <div class="feature-card goddess-mode">
                    <div class="feature-icon">✨</div>
                    <div class="feature-name">Goddess Mode</div>
                    <div class="feature-desc">Emotional intelligence & adaptive personality</div>
                </div>
                <div class="feature-card quantum-analysis">
                    <div class="feature-icon">🔬</div>
                    <div class="feature-name">Quantum Analysis</div>
                    <div class="feature-desc">Quantum-inspired pattern recognition</div>
                </div>
                <div class="feature-card neural-synthesis">
                    <div class="feature-icon">🧠</div>
                    <div class="feature-name">Neural Synthesis</div>
                    <div class="feature-desc">Brain-inspired code generation</div>
                </div>
                <div class="feature-card time-travel">
                    <div class="feature-icon">⏰</div>
                    <div class="feature-name">Time-Travel Debug</div>
                    <div class="feature-desc">Predict future issues</div>
                </div>
            </div>
        </div>

        <div id="workflow-list" class="workflow-list"></div>
        <div id="tool-picker-container" class="tool-picker-container"></div>
    </div>

    <script nonce="${nonce}">
        // Workflows data from extension
        const workflows = ${workflowsJson};
        // Get VS Code API
        const vscode = acquireVsCodeApi();
    </script>
    <script nonce="${nonce}" src="${toolPickerScriptUri}"></script>
    <script nonce="${nonce}" src="${scriptUri}"></script>
</body>
</html>`;
        }
        catch (error) {
            logger_1.Logger.instance.error('Error generating workflow panel HTML:', error);
            return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
    <title>Codessa Workflows - Error</title>
    <style>
        body { padding: 20px; font-family: var(--vscode-font-family); color: var(--vscode-foreground); }
        .error { color: var(--vscode-errorForeground); margin: 20px 0; padding: 10px; border: 1px solid var(--vscode-errorForeground); }
        button { background: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; padding: 8px 12px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Error Loading Workflows</h1>
    <div class="error">
        <p>There was an error loading the workflows. Please try refreshing the panel.</p>
        <p>${error instanceof Error ? error.message : String(error)}</p>
    </div>
    <button id="refresh">Refresh</button>
    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();
        document.getElementById('refresh').addEventListener('click', () => {
            vscode.postMessage({ command: 'refreshWorkflows' });
        });
    </script>
</body>
</html>`;
        }
    }
    dispose() {
        WorkflowPanel.currentPanel = undefined;
        this._panel.dispose();
        this._disposables.forEach(d => d.dispose());
    }
}
exports.WorkflowPanel = WorkflowPanel;
//# sourceMappingURL=workflowPanel.js.map