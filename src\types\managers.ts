/**
 * Type definitions for various manager classes used in the Codessa application
 */

import { MCPContext } from '../mcp/mcpManager';
import { GraphDefinition } from '../agents/workflows/graphTypes';

import { Logger } from '../utils/logger';

/**
 * Interface for the workflow manager
 */
export interface IWorkflowManager {
  /**
   * Get a workflow for a specific mode
   */
  getWorkflowForMode(mode: string): Promise<GraphDefinition | undefined>;

  /**
   * Get all available workflows
   */
  getAllWorkflows(): Promise<GraphDefinition[]>;

  /**
   * Get workflows by tag
   */
  getWorkflowsByTag(tag: string): Promise<GraphDefinition[]>;

  /**
   * Logger instance
   */
  readonly logger: Logger;
}

/**
 * Interface for the MCP manager
 */
export interface IMCPManager {
  /**
   * Get the current context
   */
  getCurrentContext(): MCPContext;

  /**
   * Update the context with new content
   */
  updateContext(content: any): void;

  /**
   * Add code to the context
   */
  addCode(language: string, content: string, path?: string): void;

  /**
   * Add a file to the context
   */
  addFile(path: string, content: string, language?: string): void;
}

/**
 * Interface for the prompt manager
 */
export interface IPromptManager {
  /**
   * Get a system prompt by name
   */
  getSystemPrompt(name: string, variables?: Record<string, unknown>): string;

  /**
   * Get a prompt by ID
   */
  getPrompt(id: string): any;

  /**
   * Render a prompt with variables
   */
  renderPrompt(promptId: string, variables?: Record<string, string>): string;
}

/**
 * Interface for the knowledgebase manager
 */
export interface IKnowledgebaseManager {
  /**
   * Get relevant knowledge for a query
   */
  getRelevantKnowledge(query: string): Promise<string>;

  /**
   * Add knowledge to the knowledgebase
   */
  addKnowledge(content: string, metadata?: Record<string, any>): Promise<void>;

  /**
   * Search the knowledgebase
   */
  searchKnowledge(query: string): Promise<any[]>;
}