{"version": 3, "file": "multiRepoWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/multiRepoWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAaH,0DAmFC;AAKD,8EAwFC;AAzLD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,uBAAuB,CACrC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,EAAE,CAAC,CAAC;IAEnE,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,aAAa,CAAC,CAAC;IAC3G,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,qBAAqB,EAAE,aAAa,CAAC,CAAC;IACxG,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;IACjH,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,yBAAyB,EAAE,aAAa,CAAC,CAAC;IACnH,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,aAAa,CAAC,CAAC;IACpH,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;IACjH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1F,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC9G,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChH,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC/F,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,qCAAqC;QACrC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,kBAAkB,KAAK,EAAE;gBAC/B,MAAM,EAAE,mBAAmB;gBAC3B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,4CAA4C;YAC5C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,iBAAiB;gBACpC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,qBAAqB;gBAC7B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,iBAAiB;YACjB,gBAAgB;YAChB,qBAAqB;YACrB,mBAAmB;YACnB,sBAAsB;YACtB,qBAAqB;YACrB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,UAAU;QACzB,IAAI,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,eAAe,CAAC;KAC1D,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,iCAAiC,CAC/C,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,2DAA2D,IAAI,EAAE,CAAC,CAAC;IAE/E,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,qBAAqB,EAAE,aAAa,CAAC,CAAC;IACxG,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;IAC3G,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;IACxG,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;IAChH,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;IAC3G,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,aAAa,CAAC,CAAC;IAC7H,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC9G,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACxF,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjH,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnH,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC7F,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,oCAAoC;QACpC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,iBAAiB,KAAK,EAAE;gBAC9B,MAAM,EAAE,kBAAkB;gBAC1B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,+CAA+C;YAC/C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,mBAAmB;gBACtC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,wBAAwB;gBAChC,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,gBAAgB;YAChB,mBAAmB;YACnB,kBAAkB;YAClB,oBAAoB;YACpB,mBAAmB;YACnB,yBAAyB;YACzB,oBAAoB;YACpB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,UAAU;QACzB,IAAI,EAAE,CAAC,qBAAqB,EAAE,kBAAkB,EAAE,eAAe,CAAC;KACnE,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Multi-Repository Context Workflow\n *\n * This module provides a workflow for working with multiple repositories:\n * - Analyzing code across repositories\n * - Maintaining context between different codebases\n * - Providing insights across project boundaries\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { Codessa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n\n/**\n * Create a Multi-Repository Context workflow\n */\nexport function createMultiRepoWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  analyzerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Multi-Repository Context workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const repoDiscoveryNode = Codessa.createAgentNode('repo-discovery', 'Repository Discovery', analyzerAgent);\n  const repoAnalysisNode = Codessa.createAgentNode('repo-analysis', 'Repository Analysis', analyzerAgent);\n  const dependencyMappingNode = Codessa.createAgentNode('dependency-mapping', 'Dependency Mapping', analyzerAgent);\n  const crossRepoSearchNode = Codessa.createAgentNode('cross-repo-search', 'Cross-Repository Search', analyzerAgent);\n  const contextIntegrationNode = Codessa.createAgentNode('context-integration', 'Context Integration', analyzerAgent);\n  const insightGenerationNode = Codessa.createAgentNode('insight-generation', 'Insight Generation', analyzerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-discovery', source: 'input', target: 'repo-discovery', type: 'default' },\n    { name: 'discovery-to-analysis', source: 'repo-discovery', target: 'repo-analysis', type: 'default' },\n    { name: 'analysis-to-mapping', source: 'repo-analysis', target: 'dependency-mapping', type: 'default' },\n    { name: 'mapping-to-search', source: 'dependency-mapping', target: 'cross-repo-search', type: 'default' },\n    { name: 'search-to-integration', source: 'cross-repo-search', target: 'context-integration', type: 'default' },\n    { name: 'integration-to-insight', source: 'context-integration', target: 'insight-generation', type: 'default' },\n    { name: 'insight-to-output', source: 'insight-generation', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect cross-repo search to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `search-to-tool-${index}`,\n        source: 'cross-repo-search',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to context integration\n      edges.push({\n        name: `tool-${index}-to-integration`,\n        source: `tool-${index}`,\n        target: 'context-integration',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      repoDiscoveryNode,\n      repoAnalysisNode,\n      dependencyMappingNode,\n      crossRepoSearchNode,\n      contextIntegrationNode,\n      insightGenerationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'research',\n    tags: ['multi-repo', 'cross-repository', 'code-analysis']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized Cross-Repository Dependency Analysis workflow\n */\nexport function createCrossRepoDependencyWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  analyzerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Cross-Repository Dependency Analysis workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const repoScanningNode = Codessa.createAgentNode('repo-scanning', 'Repository Scanning', analyzerAgent);\n  const packageAnalysisNode = Codessa.createAgentNode('package-analysis', 'Package Analysis', analyzerAgent);\n  const importAnalysisNode = Codessa.createAgentNode('import-analysis', 'Import Analysis', analyzerAgent);\n  const apiUsageAnalysisNode = Codessa.createAgentNode('api-usage-analysis', 'API Usage Analysis', analyzerAgent);\n  const dependencyGraphNode = Codessa.createAgentNode('dependency-graph', 'Dependency Graph', analyzerAgent);\n  const vulnerabilityAnalysisNode = Codessa.createAgentNode('vulnerability-analysis', 'Vulnerability Analysis', analyzerAgent);\n  const reportGenerationNode = Codessa.createAgentNode('report-generation', 'Report Generation', analyzerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-scanning', source: 'input', target: 'repo-scanning', type: 'default' },\n    { name: 'scanning-to-package', source: 'repo-scanning', target: 'package-analysis', type: 'default' },\n    { name: 'scanning-to-import', source: 'repo-scanning', target: 'import-analysis', type: 'default' },\n    { name: 'scanning-to-api', source: 'repo-scanning', target: 'api-usage-analysis', type: 'default' },\n    { name: 'package-to-graph', source: 'package-analysis', target: 'dependency-graph', type: 'default' },\n    { name: 'import-to-graph', source: 'import-analysis', target: 'dependency-graph', type: 'default' },\n    { name: 'api-to-graph', source: 'api-usage-analysis', target: 'dependency-graph', type: 'default' },\n    { name: 'graph-to-vulnerability', source: 'dependency-graph', target: 'vulnerability-analysis', type: 'default' },\n    { name: 'vulnerability-to-report', source: 'vulnerability-analysis', target: 'report-generation', type: 'default' },\n    { name: 'report-to-output', source: 'report-generation', target: 'output', type: 'default' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect dependency graph to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `graph-to-tool-${index}`,\n        source: 'dependency-graph',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to vulnerability analysis\n      edges.push({\n        name: `tool-${index}-to-vulnerability`,\n        source: `tool-${index}`,\n        target: 'vulnerability-analysis',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      repoScanningNode,\n      packageAnalysisNode,\n      importAnalysisNode,\n      apiUsageAnalysisNode,\n      dependencyGraphNode,\n      vulnerabilityAnalysisNode,\n      reportGenerationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'research',\n    tags: ['dependency-analysis', 'cross-repository', 'vulnerability']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}