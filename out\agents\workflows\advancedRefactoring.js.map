{"version": 3, "file": "advancedRefactoring.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/advancedRefactoring.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;AAkBH,4EA+SC;AAUD,kEAuUC;AAhpBD,2CAAsE;AAGtE,yCAAsC;AAEtC,sEAAmG;AAGnG;;;;;;;GAOG;AACH,SAAgB,gCAAgC,CAC9C,KAAY,EACZ,UAII,EAAE;IAEN,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,oCAAoC,CAAC;IAClE,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,qDAAqD,CAAC;IAEjG,uBAAuB;IACvB,MAAM,QAAQ,GAAoB;QAChC,EAAE,EAAE,gCAAgC,IAAI,CAAC,GAAG,EAAE,EAAE;QAChD,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,qBAAqB;QACpC,IAAI,EAAE,qBAAqB;QAC3B,KAAK,EAAE;YACL;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,OAAO;aACf;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,QAAQ;aAChB;SACF;QACD,KAAK,EAAE;YACL;gBACE,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,qBAAiC;gBACvC,IAAI,EAAE,cAAc;aACrB;SACF;QACD,WAAW,EAAE,OAAO;KACrB,CAAC;IAEF,sDAAsD;IACtD,MAAM,eAAe,GAAc;QACjC,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,wBAAwB;QAC9B,KAAK,EAAE,wBAAwB;QAC/B,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YAE/E,6DAA6D;YAC7D,IAAI,YAAY,GAAa,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK,EAAE,+BAA+B;oBACtC,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpC,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAsB,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBACnE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,YAAY,CAAC,MAAM,+BAA+B,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YAC1E,CAAC;YAED,iCAAiC;YACjC,OAAO;gBACL,OAAO,EAAE;oBACP,YAAY,EAAE;wBACZ,aAAa,EAAE;4BACb,OAAO,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;4BAC5D,YAAY,EAAE;gCACZ,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,aAAa,EAAE;gCAC7C,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,EAAE,gBAAgB,EAAE;6BAC9C;4BACD,QAAQ,EAAE;gCACR,OAAO,EAAE,CAAC,YAAY,CAAC;gCACvB,SAAS,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;6BAC/C;yBACF;qBACF;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,sBAAW,CACb,cAAc,EACd,EAAE,EACF,IAAI,CAAC,SAAS,CAAC;wBACb,aAAa,EAAE;4BACb,OAAO,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;4BAC5D,YAAY,EAAE;gCACZ,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,aAAa,EAAE;gCAC7C,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,EAAE,gBAAgB,EAAE;6BAC9C;4BACD,QAAQ,EAAE;gCACR,OAAO,EAAE,CAAC,YAAY,CAAC;gCACvB,SAAS,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;6BAC/C;yBACF;qBACF,CAAC,CACH;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,oBAAoB,GAAc;QACtC,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,8BAA8B;QACpC,KAAK,EAAE,8BAA8B;QACrC,KAAK;QACL,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YAE/D,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,EAAE,YAAY,EAAE,aAAa,CAAC;YAEjE,mCAAmC;YACnC,MAAM,sBAAsB,GAAG;gBAC7B;oBACE,OAAO,EAAE,SAAS;oBAClB,MAAM,EAAE,0CAA0C;oBAClD,MAAM,EAAE,MAAM;oBACd,UAAU,EAAE,QAAQ;iBACrB;gBACD;oBACE,OAAO,EAAE,UAAU;oBACnB,MAAM,EAAE,gDAAgD;oBACxD,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,KAAK;iBAClB;aACF,CAAC;YAEF,mDAAmD;YACnD,MAAM,MAAM,GAAG;cACP,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YAEjD,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEjD,OAAO;gBACL,OAAO,EAAE;oBACP,iBAAiB,EAAE;wBACjB,eAAe,EAAE,sBAAsB;wBACvC,WAAW;qBACZ;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,oBAAS,CAAC;;;;;;;;;;EAUtB,WAAW,EAAE,CAAC;iBACP;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,iBAAiB,GAAc;QACnC,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,uBAAuB;QAC7B,KAAK,EAAE,uBAAuB;QAC9B,KAAK;QACL,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAEjD,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,eAAe,CAAC;YAE1E,8DAA8D;YAC9D,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;oBACxC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,SAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC;qBACxC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YAClE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YACnF,CAAC;YAED,2BAA2B;YAC3B,MAAM,MAAM,GAAG;cACP,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YAEnD,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEpD,OAAO;gBACL,OAAO,EAAE;oBACP,cAAc,EAAE;wBACd,cAAc;wBACd,eAAe,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;qBAC5D;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,oBAAS,CAAC;;;EAGtB,cAAc;;;2DAG2C,CAAC;iBACnD;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,gBAAgB,GAAc;QAClC,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,0BAA0B;QAChC,KAAK,EAAE,0BAA0B;QACjC,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAEnD,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG;gBACvB,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE;oBACL,MAAM,EAAE,EAAE;oBACV,MAAM,EAAE,CAAC;oBACT,OAAO,EAAE,CAAC;iBACX;gBACD,WAAW,EAAE;oBACX,MAAM,EAAE;wBACN,aAAa,EAAE,OAAO;wBACtB,WAAW,EAAE,MAAM;qBACpB;oBACD,KAAK,EAAE;wBACL,aAAa,EAAE,OAAO;wBACtB,WAAW,EAAE,MAAM;qBACpB;iBACF;aACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE;oBACP,aAAa,EAAE,gBAAgB;iBAChC;gBACD,QAAQ,EAAE;oBACR,IAAI,sBAAW,CACb,eAAe,EACf,EAAE,EACF,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CACjC;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,wBAAwB;IACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACrC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC1C,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACvC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAEtC,iCAAiC;IACjC,QAAQ,CAAC,KAAK,GAAG;QACf;YACE,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,cAAc;YACtB,IAAI,EAAE,qBAAiC;YACvC,IAAI,EAAE,kBAAkB;SACzB;QACD;YACE,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,mBAAmB;YAC3B,IAAI,EAAE,qBAAiC;YACvC,IAAI,EAAE,2BAA2B;SAClC;QACD;YACE,MAAM,EAAE,mBAAmB;YAC3B,MAAM,EAAE,gBAAgB;YACxB,IAAI,EAAE,qBAAiC;YACvC,IAAI,EAAE,wBAAwB;SAC/B;QACD;YACE,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,eAAe;YACvB,IAAI,EAAE,qBAAiC;YACvC,IAAI,EAAE,oBAAoB;SAC3B;QACD;YACE,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,qBAAiC;YACvC,IAAI,EAAE,WAAW;SAClB;KACF,CAAC;IAEF,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,2BAA2B,CACzC,KAAY,EACZ,UAII,EAAE;IAEN,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,mCAAmC,CAAC;IACjE,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,sDAAsD,CAAC;IAElG,uBAAuB;IACvB,MAAM,QAAQ,GAAoB;QAChC,EAAE,EAAE,sBAAsB,IAAI,CAAC,GAAG,EAAE,EAAE;QACtC,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,gBAAgB;QAC/B,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAE;YACL;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,OAAO;aACf;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,QAAQ;aAChB;SACF;QACD,KAAK,EAAE;YACL;gBACE,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,QAAQ;gBAChB,IAAI,EAAE,gBAA4B;gBAClC,IAAI,EAAE,cAAc;aACrB;SACF;QACD,WAAW,EAAE,OAAO;KACrB,CAAC;IAEF,qDAAqD;IACrD,MAAM,eAAe,GAAc;QACjC,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,wBAAwB;QAC9B,KAAK,EAAE,wBAAwB;QAC/B,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAE9D,oCAAoC;YACpC,OAAO;gBACL,OAAO,EAAE;oBACP,YAAY,EAAE;wBACZ,SAAS,EAAE;4BACT;gCACE,IAAI,EAAE,gBAAgB;gCACtB,QAAQ,EAAE,sBAAsB;gCAChC,QAAQ,EAAE,MAAM;gCAChB,MAAM,EAAE,QAAQ;6BACjB;4BACD;gCACE,IAAI,EAAE,gBAAgB;gCACtB,QAAQ,EAAE,2CAA2C;gCACrD,QAAQ,EAAE,MAAM;gCAChB,MAAM,EAAE,MAAM;6BACf;4BACD;gCACE,IAAI,EAAE,uBAAuB;gCAC7B,QAAQ,EAAE,cAAc;gCACxB,QAAQ,EAAE,QAAQ;gCAClB,MAAM,EAAE,KAAK;6BACd;yBACF;wBACD,OAAO,EAAE;4BACP,UAAU,EAAE,EAAE;4BACd,YAAY,EAAE,KAAK;4BACnB,kBAAkB,EAAE,MAAM;4BAC1B,QAAQ,EAAE,KAAK;yBAChB;qBACF;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,sBAAW,CACb,cAAc,EACd,EAAE,EACF,IAAI,CAAC,SAAS,CAAC;wBACb,SAAS,EAAE;4BACT;gCACE,IAAI,EAAE,gBAAgB;gCACtB,QAAQ,EAAE,sBAAsB;gCAChC,QAAQ,EAAE,MAAM;gCAChB,MAAM,EAAE,QAAQ;6BACjB;4BACD;gCACE,IAAI,EAAE,gBAAgB;gCACtB,QAAQ,EAAE,2CAA2C;gCACrD,QAAQ,EAAE,MAAM;gCAChB,MAAM,EAAE,MAAM;6BACf;4BACD;gCACE,IAAI,EAAE,uBAAuB;gCAC7B,QAAQ,EAAE,cAAc;gCACxB,QAAQ,EAAE,QAAQ;gCAClB,MAAM,EAAE,KAAK;6BACd;yBACF;wBACD,OAAO,EAAE;4BACP,UAAU,EAAE,EAAE;4BACd,YAAY,EAAE,KAAK;4BACnB,kBAAkB,EAAE,MAAM;4BAC1B,QAAQ,EAAE,KAAK;yBAChB;qBACF,CAAC,CACH;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,kBAAkB,GAAc;QACpC,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,2BAA2B;QACjC,KAAK,EAAE,2BAA2B;QAClC,KAAK;QACL,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAE1D,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC;YAEzD,kCAAkC;YAClC,MAAM,MAAM,GAAG;cACP,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YAE7C,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEpD,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG;gBACvB;oBACE,IAAI,EAAE,gBAAgB;oBACtB,QAAQ,EAAE,2CAA2C;oBACrD,QAAQ,EAAE,MAAM;oBAChB,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,gDAAgD;iBACzD;gBACD;oBACE,IAAI,EAAE,gBAAgB;oBACtB,QAAQ,EAAE,sBAAsB;oBAChC,QAAQ,EAAE,MAAM;oBAChB,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,wCAAwC;iBACjD;gBACD;oBACE,IAAI,EAAE,uBAAuB;oBAC7B,QAAQ,EAAE,cAAc;oBACxB,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,KAAK;oBACb,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,kDAAkD;iBAC3D;aACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE;oBACP,eAAe,EAAE;wBACf,gBAAgB;wBAChB,WAAW,EAAE,cAAc;qBAC5B;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,oBAAS,CAAC;;;;;;;;;;;EAWtB,cAAc,EAAE,CAAC;iBACV;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,gBAAgB,GAAc;QAClC,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,eAAe;QACrB,KAAK,EAAE,eAAe;QACtB,KAAK;QACL,OAAO,EAAE,KAAK,EAAE,KAAU,EAAgC,EAAE;YAC1D,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAElE,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,EAAE,eAAe,EAAE,gBAAgB,CAAC;YAC1E,MAAM,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAEpC,qDAAqD;YACrD,MAAM,MAAM,GAAG;cACP,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YAE3C,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEpD,OAAO;gBACL,OAAO,EAAE;oBACP,aAAa,EAAE;wBACb,IAAI,EAAE,OAAO;wBACb,cAAc;wBACd,OAAO,EAAE;4BACP;gCACE,IAAI,EAAE,OAAO,CAAC,QAAQ;gCACtB,IAAI,EAAE,UAAU;gCAChB,WAAW,EAAE,cAAc,OAAO,CAAC,IAAI,EAAE;6BAC1C;yBACF;qBACF;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,oBAAS,CAAC;;;EAGtB,cAAc;;;iHAGiG,CAAC;iBACzG;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,MAAM,wBAAwB,GAAc;QAC1C,EAAE,EAAE,uBAAuB;QAC3B,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,uBAAuB;QAC7B,KAAK,EAAE,uBAAuB;QAC9B,OAAO,EAAE,KAAK,EAAE,MAAkB,EAAgC,EAAE;YAClE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YAE/D,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG;gBACvB,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,UAAU,EAAE,EAAE;wBACd,YAAY,EAAE,KAAK;wBACnB,kBAAkB,EAAE,MAAM;wBAC1B,QAAQ,EAAE,KAAK;qBAChB;oBACD,KAAK,EAAE;wBACL,UAAU,EAAE,EAAE;wBACd,YAAY,EAAE,KAAK;wBACnB,kBAAkB,EAAE,MAAM;wBAC1B,QAAQ,EAAE,KAAK;qBAChB;iBACF;gBACD,YAAY,EAAE;oBACZ,UAAU,EAAE,MAAM;oBAClB,YAAY,EAAE,MAAM;oBACpB,kBAAkB,EAAE,MAAM;oBAC1B,QAAQ,EAAE,OAAO;iBAClB;aACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE;oBACP,qBAAqB,EAAE,gBAAgB;iBACxC;gBACD,QAAQ,EAAE;oBACR,IAAI,sBAAW,CACb,uBAAuB,EACvB,EAAE,EACF,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CACjC;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,wBAAwB;IACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACrC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACxC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACtC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAE9C,iCAAiC;IACjC,QAAQ,CAAC,KAAK,GAAG;QACf;YACE,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,cAAc;YACtB,IAAI,EAAE,gBAA4B;YAClC,IAAI,EAAE,kBAAkB;SACzB;QACD;YACE,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,iBAAiB;YACzB,IAAI,EAAE,gBAA4B;YAClC,IAAI,EAAE,wBAAwB;SAC/B;QACD;YACE,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,eAAe;YACvB,IAAI,EAAE,gBAA4B;YAClC,IAAI,EAAE,qBAAqB;SAC5B;QACD;YACE,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,uBAAuB;YAC/B,IAAI,EAAE,gBAA4B;YAClC,IAAI,EAAE,eAAe;SACtB;QACD;YACE,MAAM,EAAE,uBAAuB;YAC/B,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,gBAA4B;YAClC,IAAI,EAAE,WAAW;SAClB;KACF,CAAC;IAEF,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,uDAAuD", "sourcesContent": ["/**\n * Advanced Code Refactoring Workflows\n *\n * This module provides specialized workflow templates for advanced code refactoring:\n * - Pattern-based refactoring\n * - Architecture refactoring\n * - Performance optimization\n * - Technical debt reduction\n */\n\nimport { AIMessage, ToolMessage, StructuredTool } from 'src/managers';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { GraphDefinition, GraphState, GraphNode, EdgeType } from './types';\nimport { Logger } from '../../logger';\nimport { Agent } from '../agentUtilities/agent';\nimport { codessaMemoryProvider as codessaMemoryManager } from '../../memory/codessa/codessaMemory';\nimport { MemorySource, MemoryType } from '../../memory/types';\n\n/**\n * Creates a pattern-based refactoring workflow\n * This workflow helps with refactoring code based on design patterns:\n * 1. Analyzing the code structure\n * 2. Identifying applicable patterns\n * 3. Applying the patterns\n * 4. Validating the refactored code\n */\nexport function createPatternRefactoringWorkflow(\n  agent: Agent,\n  options: {\n    name?: string;\n    description?: string;\n    tools?: (ITool | StructuredTool)[];\n  } = {}\n): GraphDefinition {\n  const name = options.name || 'Pattern-Based Refactoring Workflow';\n  const description = options.description || 'Workflow for refactoring code using design patterns';\n\n  // Create base workflow\n  const workflow: GraphDefinition = {\n    id: `pattern-refactoring-workflow-${Date.now()}`,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'pattern-refactoring',\n    type: 'pattern-refactoring',\n    nodes: [\n      {\n        id: 'input',\n        type: 'input',\n        name: 'Input',\n        label: 'Input'\n      },\n      {\n        id: 'output',\n        type: 'output',\n        name: 'Output',\n        label: 'Output'\n      }\n    ],\n    edges: [\n      {\n        source: 'input',\n        target: 'output',\n        type: 'pattern-refactoring' as EdgeType,\n        name: 'Default Flow'\n      }\n    ],\n    startNodeId: 'input'\n  };\n\n  // Add specialized nodes for pattern-based refactoring\n  const analyzeCodeNode: GraphNode = {\n    id: 'analyze_code',\n    type: 'tool',\n    name: 'Analyze Code Structure',\n    label: 'Analyze Code Structure',\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Analyzing code structure for pattern-based refactoring');\n\n      // Use memory to retrieve relevant code patterns if available\n      let codePatterns: string[] = [];\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query: 'code patterns design patterns',\n          limit: 5\n        });\n\n        if (memories && memories.length > 0) {\n          codePatterns = memories.map((m: { content: string }) => m.content);\n          Logger.instance.info(`Retrieved ${codePatterns.length} pattern examples from memory`);\n        }\n      } catch (error) {\n        Logger.instance.warn('Failed to retrieve patterns from memory:', error);\n      }\n\n      // Simulated code analysis result\n      return {\n        outputs: {\n          analyze_code: {\n            codeStructure: {\n              classes: ['UserService', 'UserController', 'UserRepository'],\n              dependencies: [\n                { from: 'UserController', to: 'UserService' },\n                { from: 'UserService', to: 'UserRepository' }\n              ],\n              patterns: {\n                current: ['Repository'],\n                potential: ['Factory', 'Strategy', 'Observer']\n              }\n            }\n          }\n        },\n        messages: [\n          new ToolMessage(\n            'analyze_code',\n            {},\n            JSON.stringify({\n              codeStructure: {\n                classes: ['UserService', 'UserController', 'UserRepository'],\n                dependencies: [\n                  { from: 'UserController', to: 'UserService' },\n                  { from: 'UserService', to: 'UserRepository' }\n                ],\n                patterns: {\n                  current: ['Repository'],\n                  potential: ['Factory', 'Strategy', 'Observer']\n                }\n              }\n            })\n          )\n        ]\n      };\n    }\n  };\n\n  const identifyPatternsNode: GraphNode = {\n    id: 'identify_patterns',\n    type: 'agent',\n    name: 'Identify Applicable Patterns',\n    label: 'Identify Applicable Patterns',\n    agent,\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Identifying applicable design patterns');\n\n      const codeStructure = state.outputs?.analyze_code?.codeStructure;\n\n      // Generate pattern recommendations\n      const patternRecommendations = [\n        {\n          pattern: 'Factory',\n          reason: 'Simplify object creation for UserService',\n          impact: 'High',\n          complexity: 'Medium'\n        },\n        {\n          pattern: 'Strategy',\n          reason: 'Allow different user authentication strategies',\n          impact: 'Medium',\n          complexity: 'Low'\n        }\n      ];\n\n      // Use the agent to generate a detailed explanation\n      const prompt = `Analyze the following code structure and recommend design patterns:\n            ${JSON.stringify(codeStructure, null, 2)}`;\n\n      const explanation = await agent.generate(prompt);\n\n      return {\n        outputs: {\n          identify_patterns: {\n            recommendations: patternRecommendations,\n            explanation\n          }\n        },\n        messages: [\n          new AIMessage(`I've identified the following design patterns that could improve the code:\n\n1. **Factory Pattern** - Simplify object creation for UserService\n   - Impact: High\n   - Complexity: Medium\n\n2. **Strategy Pattern** - Allow different user authentication strategies\n   - Impact: Medium\n   - Complexity: Low\n\n${explanation}`)\n        ]\n      };\n    }\n  };\n\n  const applyPatternsNode: GraphNode = {\n    id: 'apply_patterns',\n    type: 'agent',\n    name: 'Apply Design Patterns',\n    label: 'Apply Design Patterns',\n    agent,\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Applying design patterns');\n\n      const recommendations = state.outputs?.identify_patterns?.recommendations;\n\n      // Save pattern recommendations to memory for future reference\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(recommendations),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'pattern' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['design-pattern', 'refactoring']\n          }\n        });\n        Logger.instance.info('Saved pattern recommendations to memory');\n      } catch (error) {\n        Logger.instance.warn('Failed to save pattern recommendations to memory:', error);\n      }\n\n      // Generate refactored code\n      const prompt = `Apply the following design patterns to the code:\n            ${JSON.stringify(recommendations, null, 2)}`;\n\n      const refactoredCode = await agent.generate(prompt);\n\n      return {\n        outputs: {\n          apply_patterns: {\n            refactoredCode,\n            patternsApplied: recommendations.map((r: any) => r.pattern)\n          }\n        },\n        messages: [\n          new AIMessage(`I've applied the recommended design patterns to the code:\n\n\\`\\`\\`typescript\n${refactoredCode}\n\\`\\`\\`\n\nThe code now implements the Factory and Strategy patterns.`)\n        ]\n      };\n    }\n  };\n\n  const validateCodeNode: GraphNode = {\n    id: 'validate_code',\n    type: 'tool',\n    name: 'Validate Refactored Code',\n    label: 'Validate Refactored Code',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Validating refactored code');\n\n      // Simulated validation result\n      const validationResult = {\n        success: true,\n        tests: {\n          passed: 15,\n          failed: 0,\n          skipped: 2\n        },\n        performance: {\n          before: {\n            executionTime: '245ms',\n            memoryUsage: '32MB'\n          },\n          after: {\n            executionTime: '198ms',\n            memoryUsage: '28MB'\n          }\n        }\n      };\n\n      return {\n        outputs: {\n          validate_code: validationResult\n        },\n        messages: [\n          new ToolMessage(\n            'validate_code',\n            {},\n            JSON.stringify(validationResult)\n          )\n        ]\n      };\n    }\n  };\n\n  // Add nodes to workflow\n  workflow.nodes.push(analyzeCodeNode);\n  workflow.nodes.push(identifyPatternsNode);\n  workflow.nodes.push(applyPatternsNode);\n  workflow.nodes.push(validateCodeNode);\n\n  // Add edges to connect the nodes\n  workflow.edges = [\n    {\n      source: 'input',\n      target: 'analyze_code',\n      type: 'pattern-refactoring' as EdgeType,\n      name: 'To Code Analysis'\n    },\n    {\n      source: 'analyze_code',\n      target: 'identify_patterns',\n      type: 'pattern-refactoring' as EdgeType,\n      name: 'To Pattern Identification'\n    },\n    {\n      source: 'identify_patterns',\n      target: 'apply_patterns',\n      type: 'pattern-refactoring' as EdgeType,\n      name: 'To Pattern Application'\n    },\n    {\n      source: 'apply_patterns',\n      target: 'validate_code',\n      type: 'pattern-refactoring' as EdgeType,\n      name: 'To Code Validation'\n    },\n    {\n      source: 'validate_code',\n      target: 'output',\n      type: 'pattern-refactoring' as EdgeType,\n      name: 'To Output'\n    }\n  ];\n\n  return workflow;\n}\n\n/**\n * Creates a technical debt reduction workflow\n * This workflow helps with identifying and reducing technical debt:\n * 1. Analyzing the codebase for technical debt\n * 2. Prioritizing debt items\n * 3. Refactoring to reduce debt\n * 4. Validating improvements\n */\nexport function createTechnicalDebtWorkflow(\n  agent: Agent,\n  options: {\n    name?: string;\n    description?: string;\n    tools?: (ITool | StructuredTool)[];\n  } = {}\n): GraphDefinition {\n  const name = options.name || 'Technical Debt Reduction Workflow';\n  const description = options.description || 'Workflow for identifying and reducing technical debt';\n\n  // Create base workflow\n  const workflow: GraphDefinition = {\n    id: `tech-debt-workflow-${Date.now()}`,\n    name,\n    description,\n    version: '1.0.0',\n    operationMode: 'technical-debt',\n    type: 'technical-debt',\n    nodes: [\n      {\n        id: 'input',\n        type: 'input',\n        name: 'Input',\n        label: 'Input'\n      },\n      {\n        id: 'output',\n        type: 'output',\n        name: 'Output',\n        label: 'Output'\n      }\n    ],\n    edges: [\n      {\n        source: 'input',\n        target: 'output',\n        type: 'technical-debt' as EdgeType,\n        name: 'Default Flow'\n      }\n    ],\n    startNodeId: 'input'\n  };\n\n  // Add specialized nodes for technical debt reduction\n  const analyzeDebtNode: GraphNode = {\n    id: 'analyze_debt',\n    type: 'tool',\n    name: 'Analyze Technical Debt',\n    label: 'Analyze Technical Debt',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Analyzing codebase for technical debt');\n\n      // Simulated technical debt analysis\n      return {\n        outputs: {\n          analyze_debt: {\n            debtItems: [\n              {\n                type: 'Duplicate Code',\n                location: 'src/utils/helpers.ts',\n                severity: 'High',\n                effort: 'Medium'\n              },\n              {\n                type: 'Complex Method',\n                location: 'src/services/dataProcessor.ts:processData',\n                severity: 'High',\n                effort: 'High'\n              },\n              {\n                type: 'Outdated Dependencies',\n                location: 'package.json',\n                severity: 'Medium',\n                effort: 'Low'\n              }\n            ],\n            metrics: {\n              codeSmells: 24,\n              duplications: '15%',\n              technicalDebtRatio: '0.8%',\n              coverage: '65%'\n            }\n          }\n        },\n        messages: [\n          new ToolMessage(\n            'analyze_debt',\n            {},\n            JSON.stringify({\n              debtItems: [\n                {\n                  type: 'Duplicate Code',\n                  location: 'src/utils/helpers.ts',\n                  severity: 'High',\n                  effort: 'Medium'\n                },\n                {\n                  type: 'Complex Method',\n                  location: 'src/services/dataProcessor.ts:processData',\n                  severity: 'High',\n                  effort: 'High'\n                },\n                {\n                  type: 'Outdated Dependencies',\n                  location: 'package.json',\n                  severity: 'Medium',\n                  effort: 'Low'\n                }\n              ],\n              metrics: {\n                codeSmells: 24,\n                duplications: '15%',\n                technicalDebtRatio: '0.8%',\n                coverage: '65%'\n              }\n            })\n          )\n        ]\n      };\n    }\n  };\n\n  const prioritizeDebtNode: GraphNode = {\n    id: 'prioritize_debt',\n    type: 'agent',\n    name: 'Prioritize Technical Debt',\n    label: 'Prioritize Technical Debt',\n    agent,\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Prioritizing technical debt items');\n\n      const debtItems = state.outputs?.analyze_debt?.debtItems;\n\n      // Generate prioritized debt items\n      const prompt = `Prioritize the following technical debt items:\n            ${JSON.stringify(debtItems, null, 2)}`;\n\n      const prioritization = await agent.generate(prompt);\n\n      // Simulated prioritized items\n      const prioritizedItems = [\n        {\n          type: 'Complex Method',\n          location: 'src/services/dataProcessor.ts:processData',\n          severity: 'High',\n          effort: 'High',\n          priority: 1,\n          reason: 'High impact on maintainability and performance'\n        },\n        {\n          type: 'Duplicate Code',\n          location: 'src/utils/helpers.ts',\n          severity: 'High',\n          effort: 'Medium',\n          priority: 2,\n          reason: 'Affects multiple parts of the codebase'\n        },\n        {\n          type: 'Outdated Dependencies',\n          location: 'package.json',\n          severity: 'Medium',\n          effort: 'Low',\n          priority: 3,\n          reason: 'Security implications but relatively easy to fix'\n        }\n      ];\n\n      return {\n        outputs: {\n          prioritize_debt: {\n            prioritizedItems,\n            explanation: prioritization\n          }\n        },\n        messages: [\n          new AIMessage(`I've prioritized the technical debt items:\n\n1. **Complex Method** in src/services/dataProcessor.ts:processData\n   - High impact on maintainability and performance\n\n2. **Duplicate Code** in src/utils/helpers.ts\n   - Affects multiple parts of the codebase\n\n3. **Outdated Dependencies** in package.json\n   - Security implications but relatively easy to fix\n\n${prioritization}`)\n        ]\n      };\n    }\n  };\n\n  const refactorCodeNode: GraphNode = {\n    id: 'refactor_code',\n    type: 'agent',\n    name: 'Refactor Code',\n    label: 'Refactor Code',\n    agent,\n    execute: async (state: any): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Refactoring code to reduce technical debt');\n\n      const prioritizedItems = state.outputs?.prioritize_debt?.prioritizedItems;\n      const topItem = prioritizedItems[0];\n\n      // Generate refactored code for the top priority item\n      const prompt = `Refactor the following code to reduce technical debt:\n            ${JSON.stringify(topItem, null, 2)}`;\n\n      const refactoredCode = await agent.generate(prompt);\n\n      return {\n        outputs: {\n          refactor_code: {\n            item: topItem,\n            refactoredCode,\n            changes: [\n              {\n                file: topItem.location,\n                type: 'Refactor',\n                description: `Refactored ${topItem.type}`\n              }\n            ]\n          }\n        },\n        messages: [\n          new AIMessage(`I've refactored the complex method in src/services/dataProcessor.ts:\n\n\\`\\`\\`typescript\n${refactoredCode}\n\\`\\`\\`\n\nThe refactoring breaks down the complex method into smaller, more focused functions with clear responsibilities.`)\n        ]\n      };\n    }\n  };\n\n  const validateImprovementsNode: GraphNode = {\n    id: 'validate_improvements',\n    type: 'tool',\n    name: 'Validate Improvements',\n    label: 'Validate Improvements',\n    execute: async (_state: GraphState): Promise<Partial<GraphState>> => {\n      Logger.instance.info('Validating technical debt improvements');\n\n      // Simulated validation result\n      const validationResult = {\n        success: true,\n        metrics: {\n          before: {\n            codeSmells: 24,\n            duplications: '15%',\n            technicalDebtRatio: '0.8%',\n            coverage: '65%'\n          },\n          after: {\n            codeSmells: 18,\n            duplications: '12%',\n            technicalDebtRatio: '0.6%',\n            coverage: '68%'\n          }\n        },\n        improvements: {\n          codeSmells: '-25%',\n          duplications: '-20%',\n          technicalDebtRatio: '-25%',\n          coverage: '+4.6%'\n        }\n      };\n\n      return {\n        outputs: {\n          validate_improvements: validationResult\n        },\n        messages: [\n          new ToolMessage(\n            'validate_improvements',\n            {},\n            JSON.stringify(validationResult)\n          )\n        ]\n      };\n    }\n  };\n\n  // Add nodes to workflow\n  workflow.nodes.push(analyzeDebtNode);\n  workflow.nodes.push(prioritizeDebtNode);\n  workflow.nodes.push(refactorCodeNode);\n  workflow.nodes.push(validateImprovementsNode);\n\n  // Add edges to connect the nodes\n  workflow.edges = [\n    {\n      source: 'input',\n      target: 'analyze_debt',\n      type: 'technical-debt' as EdgeType,\n      name: 'To Debt Analysis'\n    },\n    {\n      source: 'analyze_debt',\n      target: 'prioritize_debt',\n      type: 'technical-debt' as EdgeType,\n      name: 'To Debt Prioritization'\n    },\n    {\n      source: 'prioritize_debt',\n      target: 'refactor_code',\n      type: 'technical-debt' as EdgeType,\n      name: 'To Code Refactoring'\n    },\n    {\n      source: 'refactor_code',\n      target: 'validate_improvements',\n      type: 'technical-debt' as EdgeType,\n      name: 'To Validation'\n    },\n    {\n      source: 'validate_improvements',\n      target: 'output',\n      type: 'technical-debt' as EdgeType,\n      name: 'To Output'\n    }\n  ];\n\n  return workflow;\n}\n\n// No need to re-export, they're already exported above\n"]}