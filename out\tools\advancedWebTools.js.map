{"version": 3, "file": "advancedWebTools.js", "sourceRoot": "", "sources": ["../../src/tools/advancedWebTools.ts"], "names": [], "mappings": ";;;;;;AAEA,kDAA0B;AAC1B,6BAAwB;AAExB,MAAa,kBAAkB;IACpB,EAAE,GAAG,gBAAgB,CAAC;IACtB,IAAI,GAAG,kBAAkB,CAAC;IAC1B,WAAW,GAAG,4EAA4E,CAAC;IAC3F,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;KAC5C,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,EAAE;QACvE,QAAQ,EAAE,CAAC,OAAO,CAAC;KACpB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;QACpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC1F,CAAC;QAED,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,aAAa;QACb,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,IAAA,eAAK,EAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,iCAAiC,kBAAkB,CAAC,KAAK,CAAC,sCAAsC,EAAE,CAAC,CAAC;YAClJ,OAAO,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,YAAY,CAAC;QACxH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,YAAY,CAAC,GAAG,UAAU,KAAK,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;QACvE,CAAC;QACD,qBAAqB;QACrB,OAAO,CAAC,MAAM,CAAC,GAAG,0CAA0C,CAAC;QAC7D,uBAAuB;QACvB,OAAO,CAAC,QAAQ,CAAC,GAAG,4CAA4C,CAAC;QACjE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IACzE,CAAC;CACF;AAlCD,gDAkCC;AAED,MAAa,qBAAqB;IACvB,EAAE,GAAG,YAAY,CAAC;IAClB,IAAI,GAAG,qBAAqB,CAAC;IAC7B,WAAW,GAAG,wDAAwD,CAAC;IACvE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;KACzD,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8BAA8B,EAAE,EAAE;QACpF,QAAQ,EAAE,CAAC,KAAK,CAAC;KAClB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,GAAG,GAAG,KAAK,CAAC,GAAa,CAAC;QAChC,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACxF,CAAC;QACD,wFAAwF;QACxF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,2BAA2B,GAAG,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAClG,CAAC;CACF;AAtBD,sDAsBC;AAED,MAAa,eAAe;IACjB,EAAE,GAAG,aAAa,CAAC;IACnB,IAAI,GAAG,mBAAmB,CAAC;IAC3B,WAAW,GAAG,qDAAqD,CAAC;IACpE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;KAC7C,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,kBAAkB,EAAE,EAAE;QACxE,QAAQ,EAAE,CAAC,KAAK,CAAC;KAClB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,GAAG,GAAG,KAAK,CAAC,GAAa,CAAC;QAChC,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACxF,CAAC;QACD,4EAA4E;QAC5E,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,sBAAsB,GAAG,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAC7F,CAAC;CACF;AAtBD,0CAsBC;AAED,MAAa,mBAAmB;IACrB,EAAE,GAAG,cAAc,CAAC;IACpB,IAAI,GAAG,mBAAmB,CAAC;IAC3B,WAAW,GAAG,2CAA2C,CAAC;IAC1D,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CAAC;KAC3D,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,uBAAuB,EAAE,EAAE;QACtF,QAAQ,EAAE,CAAC,cAAc,CAAC;KAC3B,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,YAAY,GAAG,KAAK,CAAC,YAAsB,CAAC;QAClD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QACjG,CAAC;QACD,qEAAqE;QACrE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,0BAA0B,YAAY,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAC1G,CAAC;CACF;AAtBD,kDAsBC", "sourcesContent": ["import { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../types/agent';\nimport axios from 'axios';\nimport { z } from 'zod';\n\nexport class WebMultiSearchTool implements ITool {\n  readonly id = 'webMultiSearch';\n  readonly name = 'Web Multi-Search';\n  readonly description = 'Performs a web search using multiple providers (DuckDuckGo, Bing, Google).';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    query: z.string().describe('Search query.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: { query: { type: 'string', description: 'Search query.' } },\n    required: ['query']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const query = input.query as string;\n    if (!query) {\n      return { success: false, error: '\\'query\\' is required.', toolId: this.id, actionName };\n    }\n\n    const results: Record<string, string> = {};\n    // DuckDuckGo\n    try {\n      const ddg = await axios({ method: 'get', url: `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1` });\n      results['DuckDuckGo'] = ddg.data.AbstractText || ddg.data.Answer || ddg.data.RelatedTopics?.[0]?.Text || 'No answer.';\n    } catch (error: any) {\n      results['DuckDuckGo'] = `Error: ${error.message || 'Unknown error'}`;\n    }\n    // Bing (placeholder)\n    results['Bing'] = 'Bing search integration not implemented.';\n    // Google (placeholder)\n    results['Google'] = 'Google search integration not implemented.';\n    return { success: true, output: results, toolId: this.id, actionName };\n  }\n}\n\nexport class WebContentExtractTool implements ITool {\n  readonly id = 'webExtract';\n  readonly name = 'Web Content Extract';\n  readonly description = 'Extracts main content (article, text) from a web page.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    url: z.string().describe('URL to extract content from.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: { url: { type: 'string', description: 'URL to extract content from.' } },\n    required: ['url']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const url = input.url as string;\n    if (!url) {\n      return { success: false, error: '\\'url\\' is required.', toolId: this.id, actionName };\n    }\n    // Placeholder: In real implementation, use a library like mercury-parser or readability\n    return { success: true, output: `Extracted content from: ${url}`, toolId: this.id, actionName };\n  }\n}\n\nexport class WebSnapshotTool implements ITool {\n  readonly id = 'webSnapshot';\n  readonly name = 'Web Page Snapshot';\n  readonly description = 'Take a snapshot (HTML or screenshot) of a web page.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    url: z.string().describe('URL to snapshot.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: { url: { type: 'string', description: 'URL to snapshot.' } },\n    required: ['url']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const url = input.url as string;\n    if (!url) {\n      return { success: false, error: '\\'url\\' is required.', toolId: this.id, actionName };\n    }\n    // Placeholder: Actual screenshot would require headless browser integration\n    return { success: true, output: `Snapshot data for: ${url}`, toolId: this.id, actionName };\n  }\n}\n\nexport class WebDeployStatusTool implements ITool {\n  readonly id = 'deployStatus';\n  readonly name = 'Web Deploy Status';\n  readonly description = 'Check the deployment status of a web app.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    deploymentId: z.string().describe('Deployment ID or URL.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: { deploymentId: { type: 'string', description: 'Deployment ID or URL.' } },\n    required: ['deploymentId']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const deploymentId = input.deploymentId as string;\n    if (!deploymentId) {\n      return { success: false, error: '\\'deploymentId\\' is required.', toolId: this.id, actionName };\n    }\n    // Placeholder: In real implementation, check deployment provider API\n    return { success: true, output: `Deployment status for: ${deploymentId}`, toolId: this.id, actionName };\n  }\n}\n"]}