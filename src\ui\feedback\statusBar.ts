import * as vscode from 'vscode';
import { Logger } from '../../logger';
import { llmService } from '../../llm/llmService';
import { AgentManager } from '../../agents/agentUtilities/agentManager';
import { shouldSuppressError as _shouldSuppressError } from './notifications';
import { getConfig as _getConfig } from '../../config';

/**
 * Manages status bar items for Codessa
 */
export class StatusBarManager {
  private mainStatusBarItem: vscode.StatusBarItem;
  private modelStatusBarItem: vscode.StatusBarItem;
  private agentStatusBarItem: vscode.StatusBarItem;
  private dashboardStatusBarItem: vscode.StatusBarItem;

  constructor() {
    // Create main status bar item
    this.mainStatusBarItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Right,
      100 // High priority (lower number = higher priority)
    );
    this.mainStatusBarItem.text = '$(hubot) Codessa';
    this.mainStatusBarItem.tooltip = 'Codessa AI Coding Assistant';
    this.mainStatusBarItem.command = 'codessa.showQuickActions';

    // Create dashboard status bar item
    this.dashboardStatusBarItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Right,
      97
    );
    this.dashboardStatusBarItem.text = '$(dashboard) Dashboard';
    this.dashboardStatusBarItem.tooltip = 'Open Codessa Dashboard';
    this.dashboardStatusBarItem.command = 'codessa.openDashboard';

    // Create model status item
    this.modelStatusBarItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Right,
      99
    );

    // Create agent status item
    this.agentStatusBarItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Right,
      98
    );
  }

  /**
     * Initialize and show status bar items
     */
  initialize(): void {
    this.updateModelStatus();
    this.updateAgentStatus();

    // Listen for LLM provider changes
    llmService.onProvidersChanged(() => {
      this.updateModelStatus();
    });

    // Listen for configuration changes
    vscode.workspace.onDidChangeConfiguration(e => {
      if (e.affectsConfiguration('codessa.defaultModel') ||
        e.affectsConfiguration('codessa.providers')) {
        this.updateModelStatus();
      }
    });

    this.mainStatusBarItem.show();
    this.dashboardStatusBarItem.show();
  }

  /**
     * Update model status bar item to show current default model
     */
  async updateModelStatus(): Promise<void> {
    if (!this.modelStatusBarItem) {
      return;
    }

    try {
      const configuredProviders = await llmService.getConfiguredProviders();
      if (configuredProviders.length === 0) {
        this.modelStatusBarItem.text = '$(server) No LLM Configured';
        this.modelStatusBarItem.tooltip = 'Click to configure an LLM provider';
        this.modelStatusBarItem.command = 'codessa.openSettings';
        this.modelStatusBarItem.show();
        return;
      }

      // Get the default provider
      const defaultProvider = await llmService.getDefaultProvider();
      if (!defaultProvider) {
        // Always use a neutral icon and message
        this.modelStatusBarItem.text = '$(server) Default Provider';
        this.modelStatusBarItem.tooltip = 'Click to configure the default LLM provider';
        this.modelStatusBarItem.command = 'codessa.openSettings';
        this.modelStatusBarItem.show();
        return;
      }

      const modelConfig = await llmService.getDefaultModelConfig();
      const temperature = modelConfig.options?.temperature ?? 0.7;

      this.modelStatusBarItem.text = `$(server) ${modelConfig.provider}/${modelConfig.modelId} (${temperature})`;
      this.modelStatusBarItem.tooltip = 'Current default LLM provider and model\nClick to change settings';
      this.modelStatusBarItem.command = 'codessa.openSettings';
      this.modelStatusBarItem.show();
    } catch (error) {
      Logger.instance.error('Error updating model status:', error);

      // Always use a neutral icon and message for errors in the status bar
      // This prevents "screaming" at users with red error icons
      this.modelStatusBarItem.text = '$(server) Default Provider';
      this.modelStatusBarItem.tooltip = 'Click to configure LLM settings';

      // Just log the error without showing it to the user
      const errorMessage = error instanceof Error ? error.message : String(error);
      Logger.instance.debug(`Status bar error (not shown to user): ${errorMessage}`);

      this.modelStatusBarItem.command = 'codessa.openSettings';
      this.modelStatusBarItem.show();
    }
  }

  /**
     * Update agent status bar item to show available agents
     */
  updateAgentStatus(): void {
    if (!this.agentStatusBarItem) {
      return;
    }

    try {
      const agents = AgentManager.getInstance().getAllAgents();

      if (agents.length === 0) {
        this.agentStatusBarItem.text = '$(person-add) Create Agent';
        this.agentStatusBarItem.tooltip = 'No agents configured. Click to create one.';
        this.agentStatusBarItem.command = 'codessa.addAgent';
        this.agentStatusBarItem.show();
        return;
      }

      this.agentStatusBarItem.text = `$(person) ${agents.length} Agent${agents.length > 1 ? 's' : ''}`;
      this.agentStatusBarItem.tooltip = `${agents.length} agent${agents.length > 1 ? 's' : ''} available`;
      this.agentStatusBarItem.command = 'codessa.showAgentList';
      this.agentStatusBarItem.show();
    } catch (error) {
      Logger.instance.error('Error updating agent status:', error);
      this.agentStatusBarItem.hide();
    }
  }

  /**
     * Show activity indicator when an agent is processing
     * @param isActive Whether an agent is actively processing
     */
  setActivityIndicator(isActive: boolean): void {
    if (isActive) {
      this.mainStatusBarItem.text = '$(sync~spin) Codessa';
      this.mainStatusBarItem.tooltip = 'Codessa is processing...';
    } else {
      this.mainStatusBarItem.text = '$(hubot) Codessa';
      this.mainStatusBarItem.tooltip = 'Codessa AI Coding Assistant';
    }
  }

  /**
     * Dispose all status bar items
     */
  dispose(): void {
    this.mainStatusBarItem.dispose();
    this.dashboardStatusBarItem.dispose();
    this.modelStatusBarItem.dispose();
    this.agentStatusBarItem.dispose();
  }
}

/**
 * Status bar manager singleton
 */
export const statusBarManager = new StatusBarManager();