"use strict";
/**
 * Checkpoint Manager for Code Changes
 *
 * This module provides a checkpoint system for tracking and managing code changes
 * during AI-assisted development. It allows:
 * - Creating checkpoints before making changes
 * - Rolling back to previous checkpoints
 * - Comparing changes between checkpoints
 * - Merging changes from different branches
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkpointManager = exports.CheckpointManager = void 0;
const vscode = __importStar(require("vscode"));
const crypto = __importStar(require("crypto"));
const path = __importStar(require("path"));
const logger_1 = require("../logger");
/**
 * Checkpoint manager for tracking code changes
 */
class CheckpointManager {
    checkpoints = [];
    currentCheckpointIndex = -1;
    storageKey = 'codessa.checkpoints';
    constructor() {
        this.loadCheckpoints();
    }
    /**
       * Loads checkpoints from storage
       */
    async loadCheckpoints() {
        try {
            const globalState = await this.getGlobalState();
            if (!globalState)
                return;
            const savedCheckpoints = globalState.get(this.storageKey);
            if (savedCheckpoints && Array.isArray(savedCheckpoints)) {
                // Convert string dates back to Date objects
                this.checkpoints = savedCheckpoints.map(cp => ({
                    ...cp,
                    timestamp: new Date(cp.timestamp)
                }));
                this.currentCheckpointIndex = this.checkpoints.length - 1;
                logger_1.logger.info(`Loaded ${this.checkpoints.length} checkpoints from storage`);
            }
        }
        catch (error) {
            logger_1.logger.error(`Error loading checkpoints: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
       * Saves checkpoints to storage
       */
    async saveCheckpoints() {
        try {
            const globalState = await this.getGlobalState();
            if (!globalState)
                return;
            await globalState.update(this.storageKey, this.checkpoints);
            logger_1.logger.info(`Saved ${this.checkpoints.length} checkpoints to storage`);
        }
        catch (error) {
            logger_1.logger.error(`Error saving checkpoints: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
       * Gets the VS Code extension context global state
       */
    async getGlobalState() {
        // Get the active VS Code extension
        const extension = vscode.extensions.getExtension('codessa.codessa');
        if (!extension) {
            logger_1.logger.error('Codessa extension not found');
            return null;
        }
        // Ensure the extension is activated
        if (!extension.isActive) {
            await extension.activate();
        }
        // Access the extension's global state
        return extension.exports.getGlobalState();
    }
    /**
       * Creates a new checkpoint
       */
    async createCheckpoint(description, files, metadata = {}) {
        const checkpoint = {
            id: `checkpoint-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            timestamp: new Date(),
            description,
            files: await this.captureFiles(files),
            metadata
        };
        this.checkpoints.push(checkpoint);
        this.currentCheckpointIndex = this.checkpoints.length - 1;
        // Save checkpoints to storage
        await this.saveCheckpoints();
        logger_1.logger.info(`Created checkpoint: ${checkpoint.id} - ${description}`);
        return checkpoint;
    }
    /**
       * Captures the content of files for a checkpoint
       */
    async captureFiles(filePaths) {
        const capturedFiles = [];
        for (const filePath of filePaths) {
            try {
                // Get the full path
                const fullPath = this.getFullPath(filePath);
                // Read the file content
                const document = await vscode.workspace.openTextDocument(vscode.Uri.file(fullPath));
                const content = document.getText();
                // Calculate hash
                const hash = this.calculateHash(content);
                capturedFiles.push({
                    path: filePath,
                    content,
                    hash
                });
            }
            catch (error) {
                logger_1.logger.error(`Error capturing file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
                // Add the file with error information
                capturedFiles.push({
                    path: filePath,
                    content: `ERROR: ${error instanceof Error ? error.message : String(error)}`,
                    hash: 'error'
                });
            }
        }
        return capturedFiles;
    }
    /**
       * Calculates a hash for file content
       */
    calculateHash(content) {
        return crypto.createHash('sha256').update(content).digest('hex');
    }
    /**
       * Gets the full path for a file
       */
    getFullPath(filePath) {
        // If the path is already absolute, return it
        if (path.isAbsolute(filePath)) {
            return filePath;
        }
        // Get the workspace folder
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            throw new Error('No workspace folder open');
        }
        // Resolve the path relative to the workspace folder
        return path.join(workspaceFolders[0].uri.fsPath, filePath);
    }
    /**
       * Rolls back to a specific checkpoint
       */
    async rollbackToCheckpoint(checkpointId) {
        const checkpointIndex = this.checkpoints.findIndex(cp => cp.id === checkpointId);
        if (checkpointIndex === -1) {
            logger_1.logger.error(`Checkpoint not found: ${checkpointId}`);
            return false;
        }
        const checkpoint = this.checkpoints[checkpointIndex];
        // Restore file contents
        for (const file of checkpoint.files) {
            try {
                // Get the full path
                const fullPath = this.getFullPath(file.path);
                // Create a TextEdit to replace the entire content
                const document = await vscode.workspace.openTextDocument(vscode.Uri.file(fullPath));
                const edit = new vscode.WorkspaceEdit();
                // Replace the entire content
                const fullRange = new vscode.Range(document.positionAt(0), document.positionAt(document.getText().length));
                edit.replace(document.uri, fullRange, file.content);
                // Apply the edit
                const success = await vscode.workspace.applyEdit(edit);
                if (!success) {
                    logger_1.logger.error(`Failed to restore file: ${file.path}`);
                }
                // Save the document
                await document.save();
            }
            catch (error) {
                logger_1.logger.error(`Error restoring file ${file.path}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        this.currentCheckpointIndex = checkpointIndex;
        // Save the current checkpoint index
        await this.saveCheckpoints();
        logger_1.logger.info(`Rolled back to checkpoint: ${checkpointId}`);
        return true;
    }
    /**
       * Gets all checkpoints
       */
    getCheckpoints() {
        return [...this.checkpoints];
    }
    /**
       * Gets the current checkpoint
       */
    getCurrentCheckpoint() {
        if (this.currentCheckpointIndex === -1) {
            return null;
        }
        return this.checkpoints[this.currentCheckpointIndex];
    }
    /**
       * Compares two checkpoints and returns the differences
       */
    compareCheckpoints(checkpointId1, checkpointId2) {
        const checkpoint1 = this.checkpoints.find(cp => cp.id === checkpointId1);
        const checkpoint2 = this.checkpoints.find(cp => cp.id === checkpointId2);
        if (!checkpoint1 || !checkpoint2) {
            throw new Error('One or both checkpoints not found');
        }
        // Track changes
        let filesChanged = 0;
        let linesAdded = 0;
        let linesRemoved = 0;
        const fileDiffs = {};
        // Get all unique file paths from both checkpoints
        const allPaths = new Set([
            ...checkpoint1.files.map(f => f.path),
            ...checkpoint2.files.map(f => f.path)
        ]);
        // Compare each file
        for (const filePath of allPaths) {
            const file1 = checkpoint1.files.find(f => f.path === filePath);
            const file2 = checkpoint2.files.find(f => f.path === filePath);
            // If file exists in both checkpoints and content is different
            if (file1 && file2 && file1.hash !== file2.hash) {
                filesChanged++;
                // Calculate line differences
                const lines1 = file1.content.split('\n');
                const lines2 = file2.content.split('\n');
                // Simple diff calculation
                if (lines2.length > lines1.length) {
                    linesAdded += lines2.length - lines1.length;
                }
                else {
                    linesRemoved += lines1.length - lines2.length;
                }
                // Store diff details
                fileDiffs[filePath] = {
                    changed: true,
                    linesAdded: Math.max(0, lines2.length - lines1.length),
                    linesRemoved: Math.max(0, lines1.length - lines2.length)
                };
            }
            // File added in checkpoint2
            else if (!file1 && file2) {
                filesChanged++;
                const lineCount = file2.content.split('\n').length;
                linesAdded += lineCount;
                fileDiffs[filePath] = {
                    added: true,
                    linesAdded: lineCount
                };
            }
            // File removed in checkpoint2
            else if (file1 && !file2) {
                filesChanged++;
                const lineCount = file1.content.split('\n').length;
                linesRemoved += lineCount;
                fileDiffs[filePath] = {
                    removed: true,
                    linesRemoved: lineCount
                };
            }
        }
        return {
            filesChanged,
            linesAdded,
            linesRemoved,
            fileDiffs
        };
    }
}
exports.CheckpointManager = CheckpointManager;
// Create a singleton instance
exports.checkpointManager = new CheckpointManager();
//# sourceMappingURL=checkpointManager.js.map