/**
 * AI-Powered CodeLens Provider - Immersive code understanding
 * 
 * Provides intelligent CodeLens insights directly in the editor using existing
 * Codessa agents and tools for deep code analysis.
 */

import * as vscode from 'vscode';
import { SupervisorAgent } from '../../agents/agentTypes/supervisorAgent';
import { ToolRegistry } from '../../tools/toolRegistry';
import { MemoryManager } from '../../memory/memoryManager';
import { logger } from '../../logger';
import { promptManager } from '../../prompts/promptManager';

export interface AICodeLens extends vscode.CodeLens {
  insight: CodeInsight;
  confidence: number;
  actionable: boolean;
}

export interface CodeInsight {
  type: 'complexity' | 'performance' | 'security' | 'documentation' | 'testing' | 'refactoring' | 'explanation';
  title: string;
  description: string;
  severity: 'info' | 'warning' | 'error' | 'suggestion';
  actionCommand?: string;
  actionArgs?: unknown[];
  relatedCode?: vscode.Range[];
}

export interface CodeAnalysisResult {
  insights: CodeInsight[];
  metrics: {
    complexity: number;
    maintainability: number;
    testCoverage: number;
    performance: number;
  };
  suggestions: string[];
}

export class AICodeLensProvider implements vscode.CodeLensProvider {
  private supervisorAgent: SupervisorAgent;
  private toolRegistry: ToolRegistry;
  private memoryManager: MemoryManager;
  private disposables: vscode.Disposable[] = [];

  // Performance optimization
  private analysisCache: Map<string, { result: CodeAnalysisResult; timestamp: number }> = new Map();
  private readonly cacheExpiry = 60000; // 1 minute
  private readonly maxCacheSize = 50;

  // Analysis state
  private isAnalyzing: Map<string, boolean> = new Map();

  constructor(
    supervisorAgent: SupervisorAgent,
    toolRegistry: ToolRegistry,
    memoryManager: MemoryManager
  ) {
    this.supervisorAgent = supervisorAgent;
    this.toolRegistry = toolRegistry;
    this.memoryManager = memoryManager;

    this.registerProvider();
    this.registerCommands();
  }

  private registerProvider(): void {
    // Register for multiple languages
    const languages = ['typescript', 'javascript', 'python', 'java', 'csharp', 'cpp', 'go', 'rust'];

    for (const language of languages) {
      const provider = vscode.languages.registerCodeLensProvider(
        { language, scheme: 'file' },
        this
      );
      this.disposables.push(provider);
    }

    logger.info(`AI CodeLens provider registered for ${languages.length} languages`);
  }

  private registerCommands(): void {
    // Register commands for CodeLens actions
    const explainCommand = vscode.commands.registerCommand('codessa.explainCode', (range: vscode.Range, document: vscode.TextDocument) => {
      this.explainCode(range, document);
    });
    this.disposables.push(explainCommand);

    const optimizeCommand = vscode.commands.registerCommand('codessa.optimizeCode', (range: vscode.Range, document: vscode.TextDocument) => {
      this.optimizeCode(range, document);
    });
    this.disposables.push(optimizeCommand);

    const generateTestsCommand = vscode.commands.registerCommand('codessa.generateTests', (range: vscode.Range, document: vscode.TextDocument) => {
      this.generateTests(range, document);
    });
    this.disposables.push(generateTestsCommand);

    const refactorCommand = vscode.commands.registerCommand('codessa.refactorCode', (range: vscode.Range, document: vscode.TextDocument) => {
      this.refactorCode(range, document);
    });
    this.disposables.push(refactorCommand);

    const documentCommand = vscode.commands.registerCommand('codessa.documentCode', (range: vscode.Range, document: vscode.TextDocument) => {
      this.documentCode(range, document);
    });
    this.disposables.push(documentCommand);

    // Additional commands referenced by CodeLenses
    const showInsightCommand = vscode.commands.registerCommand('codessa.showInsight', async (insight: CodeInsight, range?: vscode.Range, document?: vscode.TextDocument) => {
      // Display a quick detail and offer to run the associated action
      const actionLabel = insight.actionCommand ? 'Run Action' : undefined;
      const selection = await vscode.window.showInformationMessage(`${insight.title}\n\n${insight.description}`, ...(actionLabel ? [actionLabel] : []));
      if (selection === 'Run Action' && insight.actionCommand) {
        await vscode.commands.executeCommand(insight.actionCommand, range, document);
      }
    });
    this.disposables.push(showInsightCommand);

    const showCodeAnalysisCommand = vscode.commands.registerCommand('codessa.showCodeAnalysis', async (analysis: CodeAnalysisResult) => {
      // Open a virtual document with analysis details
      const detailLines: string[] = [];
      detailLines.push('# Code Analysis Summary');
      detailLines.push(`- Insights: ${analysis.insights.length}`);
      detailLines.push(`- Complexity: ${analysis.metrics.complexity}`);
      detailLines.push(`- Maintainability: ${analysis.metrics.maintainability}`);
      detailLines.push(`- Test Coverage: ${analysis.metrics.testCoverage}`);
      detailLines.push(`- Performance: ${analysis.metrics.performance}`);
      detailLines.push('\n## Insights');
      for (const ins of analysis.insights) {
        detailLines.push(`- [${ins.severity}] (${ins.type}) ${ins.title} - ${ins.description}`);
      }
      const doc = await vscode.workspace.openTextDocument({ language: 'markdown', content: detailLines.join('\n') });
      await vscode.window.showTextDocument(doc, { preview: true });
    });
    this.disposables.push(showCodeAnalysisCommand);

    const showSecurityIssuesCommand = vscode.commands.registerCommand('codessa.showSecurityIssues', async (range?: vscode.Range, document?: vscode.TextDocument) => {
      const activeDoc = document ?? vscode.window.activeTextEditor?.document;
      if (!activeDoc) return;
      const securityTool = this.toolRegistry.getTool('securityScan');
      if (!securityTool) {
        vscode.window.showWarningMessage('Security scan tool is not available.');
        return;
      }
      const result = await securityTool.execute('scanFile', { filePath: activeDoc.uri.fsPath });
      const issuesArray = (result.output as { issues?: Array<{ message?: string }> }).issues;
      if (result.success && result.output && Array.isArray(issuesArray)) {
        const issues = issuesArray;
        const lines = ['# Security Issues', ...issues.map((i, idx) => `${idx + 1}. ${i.message}`)];
        const doc = await vscode.workspace.openTextDocument({ language: 'markdown', content: lines.join('\n') });
        await vscode.window.showTextDocument(doc, { preview: true });
      } else {
        vscode.window.showInformationMessage('No security issues found.');
      }
    });
    this.disposables.push(showSecurityIssuesCommand);

    const fixSecurityCommand = vscode.commands.registerCommand('codessa.fixSecurity', async (range?: vscode.Range, document?: vscode.TextDocument) => {
      const activeDoc = document ?? vscode.window.activeTextEditor?.document;
      if (!activeDoc) return;
      const code = range ? activeDoc.getText(range) : activeDoc.getText();
      const result = await this.supervisorAgent.run({
        prompt: `Identify and fix security vulnerabilities in this code. Provide fixed code only.\n\n\`\`\`${activeDoc.languageId}\n${code}\n\`\`\``,
        mode: 'refactor'
      });
      if (result.success && result.output) {
        const edit = new vscode.WorkspaceEdit();
        const targetRange = range ?? new vscode.Range(0, 0, activeDoc.lineCount, 0);
        edit.replace(activeDoc.uri, targetRange, result.output);
        await vscode.workspace.applyEdit(edit);
        vscode.window.showInformationMessage('Applied security fixes.');
      }
    });
    this.disposables.push(fixSecurityCommand);
  }

  /**
     * Main CodeLens provider method
     */
  async provideCodeLenses(
    document: vscode.TextDocument,
    token: vscode.CancellationToken
  ): Promise<vscode.CodeLens[]> {
    try {
      // Check if we should provide CodeLenses for this document
      if (!this.shouldProvideCodeLenses(document)) {
        return [];
      }

      // Get or perform code analysis
      const analysisResult = await this.getCodeAnalysis(document, token);
      if (!analysisResult) {
        return [];
      }

      // Convert insights to CodeLenses
      const codeLenses = this.convertInsightsToCodeLenses(analysisResult, document);

      logger.info(`Provided ${codeLenses.length} CodeLenses for ${document.fileName}`);
      return codeLenses;

    } catch (error) {
      logger.error(`CodeLens provider error: ${error}`);
      return [];
    }
  }

  /**
     * Resolve CodeLens with additional information
     */
  async resolveCodeLens(
    codeLens: vscode.CodeLens,
    token: vscode.CancellationToken
  ): Promise<vscode.CodeLens> {
    if (token.isCancellationRequested) {
      return codeLens;
    }

    const aiCodeLens = codeLens as AICodeLens;

    // Add confidence indicator to title
    const confidenceIndicator = this.getConfidenceIndicator(aiCodeLens.confidence);
    aiCodeLens.command = {
      title: `${confidenceIndicator} ${aiCodeLens.insight.title}`,
      command: aiCodeLens.insight.actionCommand || 'codessa.showInsight',
      arguments: [aiCodeLens.insight, aiCodeLens.range]
    };

    return aiCodeLens;
  }

  /**
     * Determine if we should provide CodeLenses for this document
     */
  private shouldProvideCodeLenses(document: vscode.TextDocument): boolean {
    // Skip for very large files (performance)
    if (document.lineCount > 1000) {
      return false;
    }

    // Skip for certain file types
    const skipExtensions = ['.md', '.txt', '.json', '.xml'];
    const fileExtension = document.fileName.toLowerCase().substring(document.fileName.lastIndexOf('.'));
    if (skipExtensions.includes(fileExtension)) {
      return false;
    }

    return true;
  }

  /**
     * Get or perform code analysis
     */
  private async getCodeAnalysis(
    document: vscode.TextDocument,
    token: vscode.CancellationToken
  ): Promise<CodeAnalysisResult | null> {
    const cacheKey = `${document.uri.fsPath}_${document.version}`;

    // Check cache first
    const cached = this.getCachedAnalysis(cacheKey);
    if (cached) {
      return cached;
    }

    // Check if already analyzing
    if (this.isAnalyzing.get(cacheKey)) {
      return null;
    }

    this.isAnalyzing.set(cacheKey, true);

    try {
      if (token.isCancellationRequested) {
        return null;
      }
      const analysisResult = await this.performCodeAnalysis(document, token);

      // Cache the result
      this.setCachedAnalysis(cacheKey, analysisResult);

      return analysisResult;
    } finally {
      this.isAnalyzing.delete(cacheKey);
    }
  }

  /**
     * Perform comprehensive code analysis using existing tools
     */
  private async performCodeAnalysis(
    document: vscode.TextDocument,
    _token: vscode.CancellationToken
  ): Promise<CodeAnalysisResult> {
    const insights: CodeInsight[] = [];
    const metrics = {
      complexity: 0,
      maintainability: 0,
      testCoverage: 0,
      performance: 0
    };
    const suggestions: string[] = [];

    try {
      // Use existing code analysis tools
      const complexityTool = this.toolRegistry.getTool('codeComplexity');
      if (complexityTool) {
        const complexityResult = await complexityTool.execute('analyzeFile', {
          filePath: document.uri.fsPath,
          content: document.getText()
        });

        if (complexityResult.success && complexityResult.output) {
          const complexity = complexityResult.output as { cyclomaticComplexity?: number; maintainabilityIndex?: number };
          metrics.complexity = complexity.cyclomaticComplexity || 0;
          if (typeof complexity.maintainabilityIndex === 'number') {
            metrics.maintainability = complexity.maintainabilityIndex;
          } else {
            // Heuristic maintainability if not provided: inverse of complexity
            metrics.maintainability = Math.max(0, 100 - metrics.complexity * 5);
          }

          // Add complexity insights
          if (metrics.complexity > 10) {
            insights.push({
              type: 'complexity',
              title: `High Complexity (${metrics.complexity})`,
              description: 'This function has high cyclomatic complexity and may be difficult to maintain',
              severity: 'warning',
              actionCommand: 'codessa.refactorCode'
            });
            suggestions.push('Consider breaking complex functions into smaller, testable units.');
          }
        }
      }

      // Security analysis
      const securityTool = this.toolRegistry.getTool('securityScan');
      if (securityTool) {
        const securityResult = await securityTool.execute('scanFile', {
          filePath: document.uri.fsPath
        });

        if (securityResult.success && securityResult.output) {
          const vulnerabilities = securityResult.output as { issues?: Array<{ severity?: string; message?: string }> };
          if (vulnerabilities.issues && vulnerabilities.issues.length > 0) {
            insights.push({
              type: 'security',
              title: `${vulnerabilities.issues.length} Security Issue(s)`,
              description: 'Potential security vulnerabilities detected',
              severity: 'error',
              actionCommand: 'codessa.showSecurityIssues'
            });
            suggestions.push('Review security issues and apply recommended fixes.');
          }
        }
      }

      // Performance analysis using AI
      if (!(_token && _token.isCancellationRequested)) {
        const performanceInsights = await this.analyzePerformance(document);
        insights.push(...performanceInsights);
        // Simple performance metric: number of performance insights
        metrics.performance = performanceInsights.length;
      }

      // Documentation analysis
      if (!(_token && _token.isCancellationRequested)) {
        const documentationInsights = await this.analyzeDocumentation(document);
        insights.push(...documentationInsights);
      }

      // Testing analysis
      if (!(_token && _token.isCancellationRequested)) {
        const testingInsights = await this.analyzeTestCoverage(document);
        insights.push(...testingInsights);
        // Heuristic test coverage metric
        metrics.testCoverage = testingInsights.length === 0 ? 80 : 40;
      }

    } catch (error) {
      logger.error(`Code analysis failed: ${error}`);
    }

    const result: CodeAnalysisResult = { insights, metrics, suggestions };

    // Persist a concise record to memory for later retrieval/search
    try {
      await this.memoryManager.addMemory({
        content: `Code analysis for ${document.fileName}: ${insights.length} insights. Complexity ${metrics.complexity}, Maintainability ${metrics.maintainability}, Performance ${metrics.performance}, TestCoverage ${metrics.testCoverage}.`,
        metadata: {
          source: 'code',
          type: 'insight',
          tags: ['code', 'analysis', document.languageId]
        }
      });
    } catch (e) {
      logger.debug('Failed to persist analysis memory (non-critical):', e);
    }

    return result;
  }

  /**
     * Analyze performance using AI
     */
  private async analyzePerformance(document: vscode.TextDocument): Promise<CodeInsight[]> {
    const insights: CodeInsight[] = [];

    try {
      const prompt = promptManager.renderPrompt('analysis.performance', {
        codeContent: document.getText(),
        languageId: document.languageId
      });

      const result = await this.supervisorAgent.run({
        prompt,
        mode: 'ask'
      });

      if (result.success && typeof result.output === 'string') {
        const parsed = this.parseAIInsights(result.output, 'performance');
        insights.push(...parsed);
      }
    } catch (error) {
      logger.warn(`Performance analysis failed: ${error}`);
    }

    return insights;
  }

  /**
     * Analyze documentation coverage
     */
  private async analyzeDocumentation(document: vscode.TextDocument): Promise<CodeInsight[]> {
    const insights: CodeInsight[] = [];
    const text = document.getText();

    // Simple heuristic: check for functions without documentation
    const functionRegex = /(?:function|def|class|interface|type)\s+(\w+)/g;
    const docRegex = /\/\*\*[\s\S]*?\*\/|#\s*.*|"""[\s\S]*?"""/g;

    const functions = Array.from(text.matchAll(functionRegex));
    const docs = Array.from(text.matchAll(docRegex));

    if (functions.length > 0 && docs.length < functions.length * 0.5) {
      insights.push({
        type: 'documentation',
        title: 'Missing Documentation',
        description: `${functions.length - docs.length} functions/classes lack documentation`,
        severity: 'suggestion',
        actionCommand: 'codessa.documentCode'
      });
    }

    return insights;
  }

  /**
     * Analyze test coverage
     */
  private async analyzeTestCoverage(document: vscode.TextDocument): Promise<CodeInsight[]> {
    const insights: CodeInsight[] = [];

    // Check if this is a test file
    const isTestFile = document.fileName.includes('.test.') ||
      document.fileName.includes('.spec.') ||
      document.fileName.includes('test/') ||
      document.fileName.includes('tests/');

    if (!isTestFile) {
      // Check if corresponding test file exists
      const testFileExists = await this.checkTestFileExists(document.uri.fsPath);
      if (!testFileExists) {
        insights.push({
          type: 'testing',
          title: 'No Tests Found',
          description: 'No corresponding test file found for this module',
          severity: 'suggestion',
          actionCommand: 'codessa.generateTests'
        });
      }
    }

    return insights;
  }

  /**
     * Convert insights to CodeLenses
     */
  private convertInsightsToCodeLenses(
    analysisResult: CodeAnalysisResult,
    document: vscode.TextDocument
  ): AICodeLens[] {
    const codeLenses: AICodeLens[] = [];

    // Add insights as CodeLenses
    for (const insight of analysisResult.insights) {
      // Determine position for CodeLens
      let range = new vscode.Range(0, 0, 0, 0); // Default to top of file

      // Try to find relevant line for the insight
      if (insight.type === 'complexity' || insight.type === 'performance') {
        // Find function definitions
        const functionMatch = document.getText().match(/(?:function|def|class)\s+\w+/);
        if (functionMatch) {
          const position = document.positionAt(document.getText().indexOf(functionMatch[0]));
          range = new vscode.Range(position, position);
        }
      }

      const codeLens: AICodeLens = {
        range,
        insight,
        confidence: this.calculateInsightConfidence(insight),
        actionable: !!insight.actionCommand,
        isResolved: true,
        command: {
          title: insight.title,
          command: insight.actionCommand || 'codessa.showInsight',
          arguments: [insight, range, document]
        }
      };

      codeLenses.push(codeLens);
    }

    // Add summary CodeLens at the top
    if (analysisResult.insights.length > 0) {
      const summaryCodeLens: AICodeLens = {
        range: new vscode.Range(0, 0, 0, 0),
        insight: {
          type: 'explanation',
          title: `📊 Code Analysis: ${analysisResult.insights.length} insights`,
          description: 'Click to view detailed code analysis',
          severity: 'info',
          actionCommand: 'codessa.showCodeAnalysis'
        },
        confidence: 1.0,
        actionable: true,
        isResolved: true,
        command: {
          title: `📊 Code Analysis: ${analysisResult.insights.length} insights`,
          command: 'codessa.showCodeAnalysis',
          arguments: [analysisResult, document]
        }
      };

      codeLenses.unshift(summaryCodeLens);
    }

    return codeLenses;
  }

  /**
     * Parse AI insights from response
     */
  private parseAIInsights(response: string, type: string): CodeInsight[] {
    const insights: CodeInsight[] = [];

    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (parsed.insights && Array.isArray(parsed.insights)) {
          for (const insight of parsed.insights) {
            insights.push({
              type: type as CodeInsight['type'],
              title: insight.title || 'AI Insight',
              description: insight.description || '',
              severity: insight.severity || 'info',
              actionCommand: this.getActionCommandForType(type)
            });
          }
        }
      }
    } catch (error) {
      logger.warn(`Failed to parse AI insights: ${error}`);
    }

    return insights;
  }

  /**
     * Get action command for insight type
     */
  private getActionCommandForType(type: string): string {
    switch (type) {
      case 'performance': return 'codessa.optimizeCode';
      case 'complexity': return 'codessa.refactorCode';
      case 'documentation': return 'codessa.documentCode';
      case 'testing': return 'codessa.generateTests';
      case 'security': return 'codessa.fixSecurity';
      default: return 'codessa.explainCode';
    }
  }

  /**
     * Calculate confidence for insight
     */
  private calculateInsightConfidence(insight: CodeInsight): number {
    // Base confidence on insight type and severity
    let confidence = 0.5;

    if (insight.severity === 'error') confidence = 0.9;
    else if (insight.severity === 'warning') confidence = 0.7;
    else if (insight.severity === 'suggestion') confidence = 0.6;

    return confidence;
  }

  /**
     * Get confidence indicator emoji
     */
  private getConfidenceIndicator(confidence: number): string {
    if (confidence >= 0.8) return '🎯';
    if (confidence >= 0.6) return '💡';
    return '💭';
  }

  // Command implementations
  private async explainCode(range: vscode.Range, document: vscode.TextDocument): Promise<void> {
    const code = document.getText(range);
    const result = await this.supervisorAgent.run({
      prompt: `Explain this code:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
      mode: 'ask'
    });

    if (result.success && result.output) {
      vscode.window.showInformationMessage(result.output);
    }
  }

  private async optimizeCode(range: vscode.Range, document: vscode.TextDocument): Promise<void> {
    const code = document.getText(range);
    const result = await this.supervisorAgent.run({
      prompt: `Optimize this code for performance:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
      mode: 'edit'
    });

    if (result.success && result.output) {
      const edit = new vscode.WorkspaceEdit();
      edit.replace(document.uri, range, result.output);
      vscode.workspace.applyEdit(edit);
    }
  }

  private async generateTests(range: vscode.Range, document: vscode.TextDocument): Promise<void> {
    const code = document.getText(range);
    const result = await this.supervisorAgent.run({
      prompt: `Generate unit tests for this code:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
      mode: 'agent'
    });

    if (result.success && result.output) {
      // Create new test file or show in new editor
      const testContent = result.output;
      const newDoc = await vscode.workspace.openTextDocument({
        content: testContent,
        language: document.languageId
      });
      vscode.window.showTextDocument(newDoc);
    }
  }

  private async refactorCode(range: vscode.Range, document: vscode.TextDocument): Promise<void> {
    const code = document.getText(range);
    const result = await this.supervisorAgent.run({
      prompt: `Refactor this code to reduce complexity:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
      mode: 'refactor'
    });

    if (result.success && result.output) {
      const edit = new vscode.WorkspaceEdit();
      edit.replace(document.uri, range, result.output);
      vscode.workspace.applyEdit(edit);
    }
  }

  private async documentCode(range: vscode.Range, document: vscode.TextDocument): Promise<void> {
    const code = document.getText(range);
    const result = await this.supervisorAgent.run({
      prompt: `Add comprehensive documentation to this code:\n\`\`\`${document.languageId}\n${code}\n\`\`\``,
      mode: 'documentation'
    });

    if (result.success && result.output) {
      const edit = new vscode.WorkspaceEdit();
      edit.replace(document.uri, range, result.output);
      vscode.workspace.applyEdit(edit);
    }
  }

  // Helper methods
  private getCachedAnalysis(key: string): CodeAnalysisResult | null {
    const cached = this.analysisCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.result;
    }
    if (cached) {
      this.analysisCache.delete(key);
    }
    return null;
  }

  private setCachedAnalysis(key: string, result: CodeAnalysisResult): void {
    if (this.analysisCache.size >= this.maxCacheSize) {
      const firstKey = this.analysisCache.keys().next().value;
      if (firstKey) {
        this.analysisCache.delete(firstKey);
      }
    }
    this.analysisCache.set(key, { result, timestamp: Date.now() });
  }

  private async checkTestFileExists(filePath: string): Promise<boolean> {
    // Simple check for test file existence
    const testPaths = [
      filePath.replace(/\.([^.]+)$/, '.test.$1'),
      filePath.replace(/\.([^.]+)$/, '.spec.$1'),
      filePath.replace(/src\//, 'test/').replace(/\.([^.]+)$/, '.test.$1')
    ];

    for (const testPath of testPaths) {
      try {
        await vscode.workspace.fs.stat(vscode.Uri.file(testPath));
        return true;
      } catch {
        // File doesn't exist, continue
      }
    }
    return false;
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.analysisCache.clear();
    this.isAnalyzing.clear();
  }
}
