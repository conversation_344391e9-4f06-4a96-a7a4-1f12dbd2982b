{"version": 3, "file": "advancedChatInterface.js", "sourceRoot": "", "sources": ["../../../src/ui/chat/advancedChatInterface.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AAKjC,yCAAsC;AAuEtC,MAAa,qBAAqB;IACxB,eAAe,CAAkB;IACjC,aAAa,CAAgB;IAC7B,WAAW,CAAqB;IAChC,gBAAgB,CAAmB;IACnC,WAAW,GAAwB,EAAE,CAAC;IAE9C,wBAAwB;IAChB,cAAc,GAA6B,IAAI,GAAG,EAAE,CAAC;IACrD,gBAAgB,GAAkB,IAAI,CAAC;IACvC,WAAW,GAA+B,IAAI,GAAG,EAAE,CAAC;IAE5D,2BAA2B;IACnB,aAAa,GAAkB;QACrC,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,OAAO;QACjB,KAAK,EAAE,SAAS;QAChB,KAAK,EAAE,GAAG;QACV,KAAK,EAAE,GAAG;KACX,CAAC;IAEF,yBAAyB;IACjB,iBAAiB,GAAG,KAAK,CAAC;IAC1B,kBAAkB,GAAiC,IAAI,GAAG,EAAE,CAAC;IAErE,WAAW;IACH,SAAS,GAA+B,IAAI,CAAC;IAC7C,aAAa,CAAwB;IAE7C,YACE,eAAgC,EAChC,aAA4B,EAC5B,WAA+B,EAC/B,gBAAkC;QAElC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAEzC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC7F,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,qCAAqC,CAAC;QAChE,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,0BAA0B,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,8BAA8B,CAAC;QAC5D,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC5B,CAAC;IAEO,gBAAgB;QACtB,qBAAqB;QACrB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACvF,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEvC,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACvF,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEzC,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC,SAAiB,EAAE,EAAE;YACpG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEzC,uBAAuB;QACvB,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACnF,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEzC,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACrF,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE1C,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACrF,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE1C,yBAAyB;QACzB,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACvF,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE3C,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC,SAAiB,EAAE,EAAE;YACtG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;IAED;;SAEK;IACE,iBAAiB;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC/C,cAAc,EACd,uBAAuB,EACvB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC;gBACvE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC;aACpF;SACF,CACF,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEzD,+BAA+B;QAC/B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;YACnD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/D,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;SAEK;IACE,gBAAgB,CAAC,IAAa;QACnC,MAAM,SAAS,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAgB;YAC3B,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,IAAI,IAAI,gBAAgB,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,EAAE;YAC5D,WAAW,EAAE,qDAAqD;YAClE,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,cAAc,EAAE,MAAM;YACtB,YAAY,EAAE,CAAC;oBACb,EAAE,EAAE,MAAM;oBACV,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC;YACF,eAAe,EAAE,KAAK;YACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,qBAAqB;QACrB,MAAM,UAAU,GAAe;YAC7B,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,mBAAmB;YACzB,WAAW,EAAE,6BAA6B;YAC1C,eAAe,EAAE,EAAE;YACnB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAEzD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,sCAAsC,SAAS,EAAE,CAAC,CAAC;QAC/D,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,OAAkC,MAAM,EAAE,WAA8B;QAChH,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAiB,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,mBAAmB;QACnB,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,EAAE,MAAM;YACZ,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI;YACJ,QAAQ,EAAE,OAAO,CAAC,cAAc;YAChC,QAAQ,EAAE;gBACR,WAAW;aACZ;SACF,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAE/C,uBAAuB;QACvB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAC7E,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAEpD,YAAY;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAED,iBAAiB;QACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,OAAoB,EAAE,WAAwB;QAC7E,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAEnE,sDAAsD;YACtD,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE7D,8CAA8C;YAC9C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBAC5C,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,mBAAmB,CAAC;gBAC5D,IAAI;aACL,EAAE;gBACD,SAAS,EAAE;oBACT,gBAAgB,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAyB;oBAC5D,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;iBACrE;aACF,CAAC,CAAC;YAEH,IAAI,eAAe,GAAG,MAAM,CAAC,MAAM,IAAI,kEAAkE,CAAC;YAE1G,2CAA2C;YAC3C,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,eAAe,EAAE;oBACjE,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;oBACrE,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;gBACH,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC;gBAEnC,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;oBAC3B,eAAe,IAAI,OAAO,QAAQ,CAAC,aAAa,EAAE,CAAC;gBACrD,CAAC;YACH,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;gBACvB,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,eAAe;gBACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,OAAO,CAAC,cAAc;aACjC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YACzD,OAAO;gBACL,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;gBACvB,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,0FAA0F;gBACnG,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,OAAO,CAAC,cAAc;aACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,kBAAkB,CAAC,SAAiB,EAAE,UAAmB;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAiB,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAChD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,QAAQ,GAAG,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACxC,MAAM,MAAM,GAAe;YACzB,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,UAAU,IAAI,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3D,WAAW,EAAE,yBAAyB,SAAS,EAAE;YACjD,eAAe,EAAE,SAAS;YAC1B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,uCAAuC;QACvC,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACpE,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;YACzE,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACxE,GAAG,GAAG;gBACN,QAAQ;aACT,CAAC,CAAC,CAAC;QACN,CAAC;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9B,OAAO,CAAC,cAAc,GAAG,QAAQ,CAAC;QAElC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;QACxD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YACjD,aAAa,EAAE,KAAK;YACpB,SAAS,EAAE,aAAa;YACxB,OAAO,EAAE;gBACP,WAAW,EAAE,CAAC,GAAG,CAAC;aACnB;SACF,CAAC,CAAC;QAEH,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,UAAU,GAAmB;gBACjC,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;gBACxB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,SAAS;gBAC7C,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;gBAC3B,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;gBACrC,IAAI,EAAE,OAAO,CAAC,MAAM;aACrB,CAAC;YAEF,+BAA+B;YAC/B,MAAM,IAAI,CAAC,WAAW,CAAC,yBAAyB,UAAU,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,WAAW;QACtB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YAClD,aAAa,EAAE,KAAK;YACpB,SAAS,EAAE,cAAc;YACzB,OAAO,EAAE;gBACP,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;aACtD;SACF,CAAC,CAAC;QAEH,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5B,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAE9D,MAAM,UAAU,GAAmB;gBACjC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;gBACzB,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,SAAS;gBAC9C,OAAO,EAAE,aAAa;gBACtB,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;gBACtC,IAAI,EAAE,OAAO,CAAC,MAAM;aACrB,CAAC;YAEF,qCAAqC;YACrC,MAAM,IAAI,CAAC,WAAW,CAAC,2BAA2B,UAAU,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;SAEK;IACE,eAAe;QACpB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QAEzD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;gBACjC,OAAO,EAAE,qBAAqB;gBAC9B,QAAQ,EAAE,IAAI,CAAC,aAAa;aAC7B,CAAC,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC;QACnE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,MAAM,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAiB,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QAED,yFAAyF;QACzF,MAAM,SAAS,GAAG,uBAAuB,OAAO,CAAC,EAAE,EAAE,CAAC;QAEtD,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAChD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,mCAAmC,CAAC,CAAC;QAE1E,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;QAC/B,eAAM,CAAC,IAAI,CAAC,wBAAwB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,wBAAwB,CAAC,SAAiB;QACrD,iEAAiE;QACjE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,SAAS,EAAE,CAAC,CAAC;QACpF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,iBAAiB;IACT,mBAAmB,CAAC,OAAoB,EAAE,OAAoB;QACpE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE/B,uBAAuB;QACvB,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,CAAC,CAAC;QACjF,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACjC,CAAC;IAEO,gBAAgB,CAAC,OAAoB,EAAE,OAAe;QAC5D,MAAM,aAAa,GAAgB;YACjC,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,EAAE,QAAQ;YACd,OAAO;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,OAAO,CAAC,cAAc;SACjC,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IACnD,CAAC;IAEO,wBAAwB,CAAC,OAAoB;QACnD,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;QACvE,OAAO,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7E,CAAC;IAEO,aAAa,CAAC,WAAwB,EAAE,OAAe;QAC7D,OAAO;;;;EAIT,OAAO;;;EAGP,WAAW,CAAC,OAAO;;mBAEF,WAAW,CAAC,IAAI;;;EAGjC,WAAW,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM;;;;;SAK1F,CAAC;IACR,CAAC;IAEO,qBAAqB,CAAC,OAAe;QAC3C,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QACrF,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,MAAM,CAAC;QACpF,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,KAAK,CAAC;QACpF,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,OAAO,CAAC;QACzF,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,UAAU,CAAC;QAE7F,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,OAAoB,EAAE,SAAiB;QAClE,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;IACxD,CAAC;IAEO,WAAW,CAAC,QAAgB;QAClC,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAC1D,MAAM,SAAS,GAA8B;YAC3C,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,iBAAiB;YACvB,IAAI,EAAE,iBAAiB;YACvB,MAAM,EAAE,kBAAkB;YAC1B,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,eAAe;SACvB,CAAC;QACF,OAAO,SAAS,CAAC,SAAS,IAAI,EAAE,CAAC,IAAI,0BAA0B,CAAC;IAClE,CAAC;IAEO,iBAAiB;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;QAErD,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,0OAA0O,CAAC;QACpP,CAAC;aAAM,CAAC;YACN,OAAO,yOAAyO,CAAC;QACnP,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACvD,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE;oBACN,IAAI,EAAE,CAAC,eAAe,CAAC;iBACxB;aACF,CAAC,CAAC;YACH,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;gBAC9B,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC/C,IAAI,WAAW,CAAC,EAAE,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;wBAC3C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;oBACvD,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAoB;QACjD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;gBACjC,OAAO,EAAE,0BAA0B,OAAO,CAAC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;gBAC/E,QAAQ,EAAE;oBACR,MAAM,EAAE,cAAqB;oBAC7B,IAAI,EAAE,cAAqB;oBAC3B,IAAI,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC;iBACpD;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,OAAoB;QACvC,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;YACjC,OAAO,EAAE,eAAe;YACxB,OAAO,EAAE;gBACP,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,YAAY,EAAE,OAAO,CAAC,YAAY;aACnC;YACD,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,OAAY;QACvC,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,aAAa;gBAChB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;gBACrE,MAAM;YACR,KAAK,oBAAoB;gBACvB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC/D,MAAM;YACR,KAAK,cAAc;gBACjB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM;YACR,KAAK,aAAa;gBAChB,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,MAAM;YACR,KAAK,aAAa;gBAChB,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM;YACR,KAAK,cAAc;gBACjB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM;QACV,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,QAAgB;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAiB,CAAC,CAAC;QAChE,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,cAAc,GAAG,QAAQ,CAAC;YAClC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,uEAAuE;QACvE,8DAA8D;QAC9D,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA+CF,CAAC;IACR,CAAC;IAED,OAAO;QACL,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;CACF;AArrBD,sDAqrBC", "sourcesContent": ["/**\n * Advanced Chat Interface - Next-generation multi-modal chat\n *\n * Provides advanced chat capabilities including voice, images, diagrams,\n * chat branching, and collaborative features while integrating with existing\n * Codessa infrastructure.\n */\n\nimport * as vscode from 'vscode';\nimport { SupervisorAgent } from '../../agents/agentTypes/supervisorAgent';\nimport { MemoryManager } from '../../memory/memoryManager';\nimport { GoddessModeManager } from '../../goddess/goddessMode';\nimport { ChatViewProvider } from './chatViewProvider';\nimport { logger } from '../../logger';\n\nexport interface ChatMessage {\n  id: string;\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: number;\n  type: 'text' | 'code' | 'image' | 'voice' | 'diagram' | 'file';\n  metadata?: {\n    language?: string;\n    filePath?: string;\n    imageData?: string;\n    voiceData?: string;\n    diagramType?: string;\n    attachments?: ChatAttachment[];\n  };\n  branchId?: string;\n  parentMessageId?: string;\n}\n\nexport interface ChatAttachment {\n  id: string;\n  type: 'file' | 'image' | 'code' | 'diagram';\n  name: string;\n  content: string;\n  mimeType?: string;\n  size?: number;\n}\n\nexport interface ChatBranch {\n  id: string;\n  name: string;\n  description: string;\n  parentMessageId: string;\n  messages: ChatMessage[];\n  isActive: boolean;\n  createdAt: number;\n}\n\nexport interface ChatSession {\n  id: string;\n  name: string;\n  description: string;\n  messages: ChatMessage[];\n  branches: ChatBranch[];\n  activeBranchId: string;\n  participants: ChatParticipant[];\n  isCollaborative: boolean;\n  createdAt: number;\n  updatedAt: number;\n}\n\nexport interface ChatParticipant {\n  id: string;\n  name: string;\n  role: 'owner' | 'collaborator' | 'viewer';\n  isOnline: boolean;\n  cursor?: {\n    messageId: string;\n    position: number;\n  };\n}\n\nexport interface VoiceSettings {\n  enabled: boolean;\n  language: string;\n  voice: string;\n  speed: number;\n  pitch: number;\n}\n\nexport class AdvancedChatInterface {\n  private supervisorAgent: SupervisorAgent;\n  private memoryManager: MemoryManager;\n  private goddessMode: GoddessModeManager;\n  private chatViewProvider: ChatViewProvider;\n  private disposables: vscode.Disposable[] = [];\n\n  // Chat state management\n  private activeSessions: Map<string, ChatSession> = new Map();\n  private currentSessionId: string | null = null;\n  private chatHistory: Map<string, ChatMessage[]> = new Map();\n\n  // Multi-modal capabilities\n  private voiceSettings: VoiceSettings = {\n    enabled: false,\n    language: 'en-US',\n    voice: 'default',\n    speed: 1.0,\n    pitch: 1.0\n  };\n\n  // Collaborative features\n  private collaborativeMode = false;\n  private onlineParticipants: Map<string, ChatParticipant> = new Map();\n\n  // UI state\n  private chatPanel: vscode.WebviewPanel | null = null;\n  private statusBarItem!: vscode.StatusBarItem;\n\n  constructor(\n    supervisorAgent: SupervisorAgent,\n    memoryManager: MemoryManager,\n    goddessMode: GoddessModeManager,\n    chatViewProvider: ChatViewProvider\n  ) {\n    this.supervisorAgent = supervisorAgent;\n    this.memoryManager = memoryManager;\n    this.goddessMode = goddessMode;\n    this.chatViewProvider = chatViewProvider;\n\n    this.setupStatusBar();\n    this.registerCommands();\n    this.loadChatHistory();\n  }\n\n  private setupStatusBar(): void {\n    this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 200);\n    this.statusBarItem.text = '$(comment-discussion) Advanced Chat';\n    this.statusBarItem.command = 'codessa.openAdvancedChat';\n    this.statusBarItem.tooltip = 'Open Advanced Chat Interface';\n    this.statusBarItem.show();\n  }\n\n  private registerCommands(): void {\n    // Main chat commands\n    const openChatCommand = vscode.commands.registerCommand('codessa.openAdvancedChat', () => {\n      this.openChatInterface();\n    });\n    this.disposables.push(openChatCommand);\n\n    const newSessionCommand = vscode.commands.registerCommand('codessa.newChatSession', () => {\n      this.createNewSession();\n    });\n    this.disposables.push(newSessionCommand);\n\n    const branchChatCommand = vscode.commands.registerCommand('codessa.branchChat', (messageId: string) => {\n      this.branchConversation(messageId);\n    });\n    this.disposables.push(branchChatCommand);\n\n    // Multi-modal commands\n    const attachFileCommand = vscode.commands.registerCommand('codessa.attachFile', () => {\n      this.attachFile();\n    });\n    this.disposables.push(attachFileCommand);\n\n    const attachImageCommand = vscode.commands.registerCommand('codessa.attachImage', () => {\n      this.attachImage();\n    });\n    this.disposables.push(attachImageCommand);\n\n    const toggleVoiceCommand = vscode.commands.registerCommand('codessa.toggleVoice', () => {\n      this.toggleVoiceMode();\n    });\n    this.disposables.push(toggleVoiceCommand);\n\n    // Collaborative commands\n    const shareSessionCommand = vscode.commands.registerCommand('codessa.shareSession', () => {\n      this.shareSession();\n    });\n    this.disposables.push(shareSessionCommand);\n\n    const joinSessionCommand = vscode.commands.registerCommand('codessa.joinSession', (sessionId: string) => {\n      this.joinCollaborativeSession(sessionId);\n    });\n    this.disposables.push(joinSessionCommand);\n  }\n\n  /**\n     * Open the advanced chat interface\n     */\n  public openChatInterface(): void {\n    if (this.chatPanel) {\n      this.chatPanel.reveal();\n      return;\n    }\n\n    this.chatPanel = vscode.window.createWebviewPanel(\n      'advancedChat',\n      'Codessa Advanced Chat',\n      vscode.ViewColumn.Beside,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true,\n        localResourceRoots: [\n          vscode.Uri.joinPath(vscode.workspace.workspaceFolders![0].uri, 'media'),\n          vscode.Uri.joinPath(vscode.workspace.workspaceFolders![0].uri, 'src', 'ui', 'chat')\n        ]\n      }\n    );\n\n    this.chatPanel.webview.html = this.getAdvancedChatHTML();\n\n    // Handle messages from webview\n    this.chatPanel.webview.onDidReceiveMessage(message => {\n      this.handleWebviewMessage(message);\n    });\n\n    // Handle panel disposal\n    this.chatPanel.onDidDispose(() => {\n      this.chatPanel = null;\n    });\n\n    // Load current session if exists\n    if (this.currentSessionId) {\n      const session = this.activeSessions.get(this.currentSessionId);\n      if (session) {\n        this.updateChatUI(session);\n      }\n    } else {\n      this.createNewSession();\n    }\n  }\n\n  /**\n     * Create a new chat session\n     */\n  public createNewSession(name?: string): string {\n    const sessionId = `session_${Date.now()}`;\n    const session: ChatSession = {\n      id: sessionId,\n      name: name || `Chat Session ${this.activeSessions.size + 1}`,\n      description: 'Advanced chat session with multi-modal capabilities',\n      messages: [],\n      branches: [],\n      activeBranchId: 'main',\n      participants: [{\n        id: 'user',\n        name: 'You',\n        role: 'owner',\n        isOnline: true\n      }],\n      isCollaborative: false,\n      createdAt: Date.now(),\n      updatedAt: Date.now()\n    };\n\n    // Create main branch\n    const mainBranch: ChatBranch = {\n      id: 'main',\n      name: 'Main Conversation',\n      description: 'Primary conversation thread',\n      parentMessageId: '',\n      messages: [],\n      isActive: true,\n      createdAt: Date.now()\n    };\n\n    session.branches.push(mainBranch);\n    this.activeSessions.set(sessionId, session);\n    this.currentSessionId = sessionId;\n\n    // Add welcome message\n    this.addSystemMessage(session, this.getWelcomeMessage());\n\n    if (this.chatPanel) {\n      this.updateChatUI(session);\n    }\n\n    logger.info(`Created new advanced chat session: ${sessionId}`);\n    return sessionId;\n  }\n\n  /**\n     * Send a message in the current session\n     */\n  public async sendMessage(content: string, type: 'text' | 'code' | 'voice' = 'text', attachments?: ChatAttachment[]): Promise<void> {\n    if (!this.currentSessionId) {\n      this.createNewSession();\n    }\n\n    const session = this.activeSessions.get(this.currentSessionId!);\n    if (!session) {\n      logger.error('No active chat session found');\n      return;\n    }\n\n    // Add user message\n    const userMessage: ChatMessage = {\n      id: `msg_${Date.now()}`,\n      role: 'user',\n      content,\n      timestamp: Date.now(),\n      type,\n      branchId: session.activeBranchId,\n      metadata: {\n        attachments\n      }\n    };\n\n    this.addMessageToSession(session, userMessage);\n\n    // Generate AI response\n    const assistantMessage = await this.generateAIResponse(session, userMessage);\n    this.addMessageToSession(session, assistantMessage);\n\n    // Update UI\n    if (this.chatPanel) {\n      this.updateChatUI(session);\n    }\n\n    // Save to memory\n    await this.saveChatToMemory(session);\n  }\n\n  /**\n     * Generate AI response using existing Codessa infrastructure\n     */\n  private async generateAIResponse(session: ChatSession, userMessage: ChatMessage): Promise<ChatMessage> {\n    try {\n      // Build context from conversation history\n      const conversationContext = this.buildConversationContext(session);\n\n      // Determine appropriate mode based on message content\n      const mode = this.determineResponseMode(userMessage.content);\n\n      // Use SupervisorAgent for response generation\n      const result = await this.supervisorAgent.run({\n        prompt: this.buildAIPrompt(userMessage, conversationContext),\n        mode\n      }, {\n        workspace: {\n          workspaceFolders: vscode.workspace.workspaceFolders as any[],\n          currentFile: vscode.window.activeTextEditor?.document.uri.toString()\n        }\n      });\n\n      let responseContent = result.output || 'I apologize, but I encountered an issue processing your request.';\n\n      // Apply Goddess Mode enhancement if active\n      if (this.goddessMode.isGoddessMode()) {\n        const enhanced = this.goddessMode.enhanceResponse(responseContent, {\n          taskComplexity: session.messages.length > 10 ? 'complex' : 'moderate',\n          userMood: 'engaged'\n        });\n        responseContent = enhanced.content;\n\n        if (enhanced.encouragement) {\n          responseContent += `\\n\\n${enhanced.encouragement}`;\n        }\n      }\n\n      return {\n        id: `msg_${Date.now()}`,\n        role: 'assistant',\n        content: responseContent,\n        timestamp: Date.now(),\n        type: 'text',\n        branchId: session.activeBranchId\n      };\n\n    } catch (error) {\n      logger.error(`Failed to generate AI response: ${error}`);\n      return {\n        id: `msg_${Date.now()}`,\n        role: 'assistant',\n        content: 'I apologize, but I encountered an error while processing your request. Please try again.',\n        timestamp: Date.now(),\n        type: 'text',\n        branchId: session.activeBranchId\n      };\n    }\n  }\n\n  /**\n     * Branch conversation from a specific message\n     */\n  public branchConversation(messageId: string, branchName?: string): string {\n    const session = this.activeSessions.get(this.currentSessionId!);\n    if (!session) {\n      logger.error('No active session for branching');\n      return '';\n    }\n\n    const branchId = `branch_${Date.now()}`;\n    const branch: ChatBranch = {\n      id: branchId,\n      name: branchName || `Branch ${session.branches.length + 1}`,\n      description: `Branched from message ${messageId}`,\n      parentMessageId: messageId,\n      messages: [],\n      isActive: false,\n      createdAt: Date.now()\n    };\n\n    // Copy messages up to the branch point\n    const parentMessage = this.findMessageInSession(session, messageId);\n    if (parentMessage) {\n      const messageIndex = session.messages.findIndex(m => m.id === messageId);\n      branch.messages = session.messages.slice(0, messageIndex + 1).map(msg => ({\n        ...msg,\n        branchId\n      }));\n    }\n\n    session.branches.push(branch);\n    session.activeBranchId = branchId;\n\n    if (this.chatPanel) {\n      this.updateChatUI(session);\n    }\n\n    logger.info(`Created conversation branch: ${branchId}`);\n    return branchId;\n  }\n\n  /**\n     * Attach file to current conversation\n     */\n  public async attachFile(): Promise<void> {\n    const fileUri = await vscode.window.showOpenDialog({\n      canSelectMany: false,\n      openLabel: 'Attach File',\n      filters: {\n        'All Files': ['*']\n      }\n    });\n\n    if (fileUri && fileUri[0]) {\n      const file = fileUri[0];\n      const content = await vscode.workspace.fs.readFile(file);\n      const attachment: ChatAttachment = {\n        id: `file_${Date.now()}`,\n        type: 'file',\n        name: file.path.split('/').pop() || 'unknown',\n        content: content.toString(),\n        mimeType: this.getMimeType(file.path),\n        size: content.length\n      };\n\n      // Send message with attachment\n      await this.sendMessage(`I've attached a file: ${attachment.name}`, 'text', [attachment]);\n    }\n  }\n\n  /**\n     * Attach image to current conversation\n     */\n  public async attachImage(): Promise<void> {\n    const imageUri = await vscode.window.showOpenDialog({\n      canSelectMany: false,\n      openLabel: 'Attach Image',\n      filters: {\n        'Images': ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg']\n      }\n    });\n\n    if (imageUri && imageUri[0]) {\n      const image = imageUri[0];\n      const content = await vscode.workspace.fs.readFile(image);\n      const base64Content = Buffer.from(content).toString('base64');\n\n      const attachment: ChatAttachment = {\n        id: `image_${Date.now()}`,\n        type: 'image',\n        name: image.path.split('/').pop() || 'unknown',\n        content: base64Content,\n        mimeType: this.getMimeType(image.path),\n        size: content.length\n      };\n\n      // Send message with image attachment\n      await this.sendMessage(`I've attached an image: ${attachment.name}`, 'text', [attachment]);\n    }\n  }\n\n  /**\n     * Toggle voice mode\n     */\n  public toggleVoiceMode(): void {\n    this.voiceSettings.enabled = !this.voiceSettings.enabled;\n\n    if (this.chatPanel) {\n      this.chatPanel.webview.postMessage({\n        command: 'updateVoiceSettings',\n        settings: this.voiceSettings\n      });\n    }\n\n    const status = this.voiceSettings.enabled ? 'enabled' : 'disabled';\n    vscode.window.showInformationMessage(`Voice mode ${status}`);\n  }\n\n  /**\n     * Share current session for collaboration\n     */\n  public async shareSession(): Promise<void> {\n    const session = this.activeSessions.get(this.currentSessionId!);\n    if (!session) {\n      vscode.window.showWarningMessage('No active session to share');\n      return;\n    }\n\n    // Generate shareable link (in real implementation, this would involve a backend service)\n    const shareLink = `codessa://chat/join/${session.id}`;\n\n    await vscode.env.clipboard.writeText(shareLink);\n    vscode.window.showInformationMessage('Session link copied to clipboard!');\n\n    session.isCollaborative = true;\n    logger.info(`Shared chat session: ${session.id}`);\n  }\n\n  /**\n     * Join collaborative session\n     */\n  public async joinCollaborativeSession(sessionId: string): Promise<void> {\n    // In real implementation, this would connect to a shared session\n    vscode.window.showInformationMessage(`Joining collaborative session: ${sessionId}`);\n    this.collaborativeMode = true;\n  }\n\n  // Helper methods\n  private addMessageToSession(session: ChatSession, message: ChatMessage): void {\n    session.messages.push(message);\n\n    // Add to active branch\n    const activeBranch = session.branches.find(b => b.id === session.activeBranchId);\n    if (activeBranch) {\n      activeBranch.messages.push(message);\n    }\n\n    session.updatedAt = Date.now();\n  }\n\n  private addSystemMessage(session: ChatSession, content: string): void {\n    const systemMessage: ChatMessage = {\n      id: `sys_${Date.now()}`,\n      role: 'system',\n      content,\n      timestamp: Date.now(),\n      type: 'text',\n      branchId: session.activeBranchId\n    };\n\n    this.addMessageToSession(session, systemMessage);\n  }\n\n  private buildConversationContext(session: ChatSession): string {\n    const recentMessages = session.messages.slice(-10); // Last 10 messages\n    return recentMessages.map(msg => `${msg.role}: ${msg.content}`).join('\\n');\n  }\n\n  private buildAIPrompt(userMessage: ChatMessage, context: string): string {\n    return `\n# Advanced Chat Context\n\n## Conversation History:\n${context}\n\n## Current User Message:\n${userMessage.content}\n\n## Message Type: ${userMessage.type}\n\n## Attachments:\n${userMessage.metadata?.attachments?.map(att => `- ${att.name} (${att.type})`).join('\\n') || 'None'}\n\n## Instructions:\nYou are Codessa, the ultimate AI coding goddess. Respond with intelligence, personality, and helpfulness.\nConsider the conversation context and any attachments when formulating your response.\n        `;\n  }\n\n  private determineResponseMode(content: string): 'task' | 'chat' | 'inline' | 'ask' | 'debug' | 'edit' | 'agent' | 'multi-agent' | 'research' | 'documentation' | 'technical-debt' | 'ui-ux' | 'refactor' {\n    const contentLower = content.toLowerCase();\n\n    if (contentLower.includes('debug') || contentLower.includes('error')) return 'debug';\n    if (contentLower.includes('edit') || contentLower.includes('change')) return 'edit';\n    if (contentLower.includes('explain') || contentLower.includes('what')) return 'ask';\n    if (contentLower.includes('generate') || contentLower.includes('create')) return 'agent';\n    if (contentLower.includes('refactor') || contentLower.includes('improve')) return 'refactor';\n\n    return 'chat';\n  }\n\n  private findMessageInSession(session: ChatSession, messageId: string): ChatMessage | undefined {\n    return session.messages.find(m => m.id === messageId);\n  }\n\n  private getMimeType(filePath: string): string {\n    const extension = filePath.toLowerCase().split('.').pop();\n    const mimeTypes: { [key: string]: string } = {\n      'txt': 'text/plain',\n      'js': 'text/javascript',\n      'ts': 'text/typescript',\n      'json': 'application/json',\n      'png': 'image/png',\n      'jpg': 'image/jpeg',\n      'jpeg': 'image/jpeg',\n      'gif': 'image/gif',\n      'svg': 'image/svg+xml'\n    };\n    return mimeTypes[extension || ''] || 'application/octet-stream';\n  }\n\n  private getWelcomeMessage(): string {\n    const goddessMode = this.goddessMode.isGoddessMode();\n\n    if (goddessMode) {\n      return '✨ Welcome to the Advanced Chat Interface! I\\'m Codessa, your coding goddess. I\\'m here to help you with advanced multi-modal conversations, voice interactions, and collaborative coding. What magical code shall we create together? 🌟';\n    } else {\n      return 'Welcome to the Advanced Chat Interface! I\\'m Codessa, your advanced AI coding assistant. This interface supports multi-modal conversations, file attachments, voice interactions, and collaborative features. How can I help you today?';\n    }\n  }\n\n  private async loadChatHistory(): Promise<void> {\n    try {\n      // Load chat history from memory\n      const memories = await this.memoryManager.searchMemories({\n        query: 'advanced-chat',\n        limit: 10,\n        filter: {\n          tags: ['advanced-chat']\n        }\n      });\n      for (const memory of memories) {\n        try {\n          const sessionData = JSON.parse(memory.content);\n          if (sessionData.id && sessionData.messages) {\n            this.activeSessions.set(sessionData.id, sessionData);\n          }\n        } catch (error) {\n          logger.warn(`Failed to parse chat history: ${error}`);\n        }\n      }\n    } catch (error) {\n      logger.warn(`Failed to load chat history: ${error}`);\n    }\n  }\n\n  private async saveChatToMemory(session: ChatSession): Promise<void> {\n    try {\n      await this.memoryManager.addMemory({\n        content: `Advanced chat session: ${session.name}\\n\\n${JSON.stringify(session)}`,\n        metadata: {\n          source: 'conversation' as any,\n          type: 'conversation' as any,\n          tags: ['advanced-chat', 'conversation', session.id]\n        }\n      });\n    } catch (error) {\n      logger.warn(`Failed to save chat to memory: ${error}`);\n    }\n  }\n\n  private updateChatUI(session: ChatSession): void {\n    if (!this.chatPanel) return;\n\n    this.chatPanel.webview.postMessage({\n      command: 'updateSession',\n      session: {\n        id: session.id,\n        name: session.name,\n        messages: session.messages,\n        branches: session.branches,\n        activeBranchId: session.activeBranchId,\n        isCollaborative: session.isCollaborative,\n        participants: session.participants\n      },\n      voiceSettings: this.voiceSettings\n    });\n  }\n\n  private handleWebviewMessage(message: any): void {\n    switch (message.command) {\n      case 'sendMessage':\n        this.sendMessage(message.content, message.type, message.attachments);\n        break;\n      case 'branchConversation':\n        this.branchConversation(message.messageId, message.branchName);\n        break;\n      case 'switchBranch':\n        this.switchBranch(message.branchId);\n        break;\n      case 'attachFile':\n        this.attachFile();\n        break;\n      case 'attachImage':\n        this.attachImage();\n        break;\n      case 'toggleVoice':\n        this.toggleVoiceMode();\n        break;\n      case 'shareSession':\n        this.shareSession();\n        break;\n      case 'newSession':\n        this.createNewSession(message.name);\n        break;\n    }\n  }\n\n  private switchBranch(branchId: string): void {\n    const session = this.activeSessions.get(this.currentSessionId!);\n    if (session) {\n      session.activeBranchId = branchId;\n      this.updateChatUI(session);\n    }\n  }\n\n  private getAdvancedChatHTML(): string {\n    // This would return comprehensive HTML for the advanced chat interface\n    // For brevity, returning a placeholder that would be expanded\n    return `\n        <!DOCTYPE html>\n        <html>\n        <head>\n            <meta charset=\"UTF-8\">\n            <title>Advanced Chat</title>\n            <style>\n                /* Advanced chat styles would go here */\n                body { font-family: var(--vscode-font-family); }\n                .chat-container { display: flex; flex-direction: column; height: 100vh; }\n                /* More styles... */\n            </style>\n        </head>\n        <body>\n            <div class=\"chat-container\">\n                <div class=\"chat-header\">\n                    <h2>🚀 Advanced Chat Interface</h2>\n                    <div class=\"chat-controls\">\n                        <button onclick=\"newSession()\">New Session</button>\n                        <button onclick=\"attachFile()\">📎 File</button>\n                        <button onclick=\"attachImage()\">🖼️ Image</button>\n                        <button onclick=\"toggleVoice()\">🎤 Voice</button>\n                        <button onclick=\"shareSession()\">🔗 Share</button>\n                    </div>\n                </div>\n                <div class=\"chat-messages\" id=\"chatMessages\"></div>\n                <div class=\"chat-input\">\n                    <input type=\"text\" id=\"messageInput\" placeholder=\"Type your message...\" />\n                    <button onclick=\"sendMessage()\">Send</button>\n                </div>\n            </div>\n            <script>\n                const vscode = acquireVsCodeApi();\n                // Advanced chat JavaScript would go here\n                function sendMessage() {\n                    const input = document.getElementById('messageInput');\n                    vscode.postMessage({\n                        command: 'sendMessage',\n                        content: input.value,\n                        type: 'text'\n                    });\n                    input.value = '';\n                }\n                // More JavaScript functions...\n            </script>\n        </body>\n        </html>\n        `;\n  }\n\n  dispose(): void {\n    this.disposables.forEach(d => d.dispose());\n    this.statusBarItem.dispose();\n    if (this.chatPanel) {\n      this.chatPanel.dispose();\n    }\n    this.activeSessions.clear();\n    this.chatHistory.clear();\n  }\n}"]}