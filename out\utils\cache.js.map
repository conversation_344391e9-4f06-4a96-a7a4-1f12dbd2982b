{"version": 3, "file": "cache.js", "sourceRoot": "", "sources": ["../../src/utils/cache.ts"], "names": [], "mappings": ";;;AA2IA,kCAsCC;AAvKD,MAAa,KAAK;IACR,KAAK,GAA+B,IAAI,GAAG,EAAE,CAAC;IAC9C,OAAO,CAAS;IAChB,UAAU,CAAS,CAAC,kBAAkB;IAE9C,YAAY,UAAkB,IAAI,EAAE,aAAqB,CAAC,GAAG,EAAE,GAAG,IAAI;QACpE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,GAAG,CACR,GAAW,EACX,KAAQ,EACR,MAAc,IAAI,CAAC,UAAU;QAE7B,yDAAyD;QACzD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,yBAAyB;QAC9D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YAClB,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG;YAC3B,QAAQ,EAAE;gBACR,IAAI,EAAE,CAAC;gBACP,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;aACzB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,GAAG,CAAC,GAAW;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QAExB,6BAA6B;QAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,kBAAkB;QAClB,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3C,CAAC;QAED,OAAO,KAAK,CAAC,KAAK,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAW;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,CAAC,CAAC;QAEb,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACzB,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;YAC9B,CAAC;YACD,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,IAAI;SACL,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,KAAa;QAC5B,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;aAC7C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YACtB,GAAG;YACH,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,YAAY,IAAI,CAAC;SAChD,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC;QAEnD,uCAAuC;QACvC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;YACpE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAChC,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACvB,KAAK,EAAE,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA3HD,sBA2HC;AAED,wBAAwB;AACX,QAAA,WAAW,GAAG,IAAI,KAAK,EAAO,CAAC;AAE5C,8BAA8B;AAC9B,SAAgB,WAAW,CACzB,MAAc,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,oBAAoB;AACjD,KAAkC;IAElC,OAAO,UACL,MAAW,EACX,WAAmB,EACnB,UAA8B;QAE9B,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QACxC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAEnC,UAAU,CAAC,KAAK,GAAG,UAAS,GAAG,IAAW;YACxC,MAAM,QAAQ,GAAG,KAAK;gBACpB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;gBACzB,CAAC,CAAC,GAAG,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YAE7C,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACnC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAEhD,uCAAuC;YACvC,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;gBAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;oBACvB,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;oBACzB,OAAO,GAAG,CAAC;gBACb,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC5B,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["interface CacheEntry<T> {\n  value: T;\n  expiresAt: number;\n  metadata?: {\n    hits: number;\n    lastAccessed: number;\n    size?: number;\n  };\n}\n\nexport class Cache<T = any> {\n  private cache: Map<string, CacheEntry<T>> = new Map();\n  private maxSize: number;\n  private defaultTTL: number; // in milliseconds\n\n  constructor(maxSize: number = 1000, defaultTTL: number = 5 * 60 * 1000) {\n    this.maxSize = maxSize;\n    this.defaultTTL = defaultTTL;\n  }\n\n  /**\n   * Set a value in the cache\n   */\n  public set(\n    key: string, \n    value: T, \n    ttl: number = this.defaultTTL\n  ): void {\n    // If cache is full, remove the least recently used items\n    if (this.cache.size >= this.maxSize) {\n      this.evictLRU(this.maxSize * 0.1); // Evict 10% of the cache\n    }\n\n    this.cache.set(key, {\n      value,\n      expiresAt: Date.now() + ttl,\n      metadata: {\n        hits: 0,\n        lastAccessed: Date.now(),\n      },\n    });\n  }\n\n  /**\n   * Get a value from the cache\n   */\n  public get(key: string): T | null {\n    const entry = this.cache.get(key);\n    \n    if (!entry) return null;\n    \n    // Check if entry has expired\n    if (Date.now() > entry.expiresAt) {\n      this.cache.delete(key);\n      return null;\n    }\n    \n    // Update metadata\n    if (entry.metadata) {\n      entry.metadata.hits++;\n      entry.metadata.lastAccessed = Date.now();\n    }\n    \n    return entry.value;\n  }\n\n  /**\n   * Delete a value from the cache\n   */\n  public delete(key: string): boolean {\n    return this.cache.delete(key);\n  }\n\n  /**\n   * Clear the entire cache\n   */\n  public clear(): void {\n    this.cache.clear();\n  }\n\n  /**\n   * Get cache statistics\n   */\n  public getStats() {\n    let hits = 0;\n    let size = 0;\n    \n    this.cache.forEach(entry => {\n      if (entry.metadata) {\n        hits += entry.metadata.hits;\n      }\n      size++;\n    });\n    \n    return {\n      size: this.cache.size,\n      hits,\n    };\n  }\n\n  /**\n   * Evict least recently used items\n   */\n  private evictLRU(count: number): void {\n    const entries = Array.from(this.cache.entries())\n      .map(([key, entry]) => ({\n        key,\n        lastAccessed: entry.metadata?.lastAccessed || 0,\n      }))\n      .sort((a, b) => a.lastAccessed - b.lastAccessed);\n    \n    // Remove the least recently used items\n    entries.slice(0, Math.min(count, entries.length)).forEach(({ key }) => {\n      this.cache.delete(key);\n    });\n  }\n\n  /**\n   * Clean up expired entries\n   */\n  public cleanup(): number {\n    let count = 0;\n    const now = Date.now();\n    \n    this.cache.forEach((value, key) => {\n      if (now > value.expiresAt) {\n        this.cache.delete(key);\n        count++;\n      }\n    });\n    \n    return count;\n  }\n}\n\n// Global cache instance\nexport const globalCache = new Cache<any>();\n\n// Cache decorator for methods\nexport function cacheResult(\n  ttl: number = 5 * 60 * 1000, // 5 minutes default\n  keyFn?: (...args: any[]) => string\n) {\n  return function (\n    target: any,\n    propertyKey: string,\n    descriptor: PropertyDescriptor\n  ) {\n    const originalMethod = descriptor.value;\n    const cache = new Cache(1000, ttl);\n    \n    descriptor.value = function(...args: any[]) {\n      const cacheKey = keyFn \n        ? keyFn.apply(this, args)\n        : `${propertyKey}:${JSON.stringify(args)}`;\n      \n      const cached = cache.get(cacheKey);\n      if (cached !== null) {\n        return cached;\n      }\n      \n      const result = originalMethod.apply(this, args);\n      \n      // Handle both sync and async functions\n      if (result instanceof Promise) {\n        return result.then(res => {\n          cache.set(cacheKey, res);\n          return res;\n        });\n      } else {\n        cache.set(cacheKey, result);\n        return result;\n      }\n    };\n    \n    return descriptor;\n  };\n}\n"]}