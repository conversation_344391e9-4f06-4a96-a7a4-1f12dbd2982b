{"version": 3, "file": "technicalDebtWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/technicalDebtWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;AAaH,kEA2FC;AAKD,sFAmGC;AA5MD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,2BAA2B,CACzC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,gBAAuB,EACvB,YAAmB,EACnB,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,IAAI,EAAE,CAAC,CAAC;IAElE,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAClG,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,aAAa,CAAC,CAAC;IACpH,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACtG,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;IAC9G,MAAM,6BAA6B,GAAG,eAAO,CAAC,eAAe,CAAC,4BAA4B,EAAE,4BAA4B,EAAE,gBAAgB,CAAC,CAAC;IAC5I,MAAM,WAAW,GAAG,eAAO,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAChF,MAAM,uBAAuB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC,CAAC;IAC1H,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACxF,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,kCAAkC,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtH,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,4BAA4B,EAAE,IAAI,EAAE,SAAS,EAAE;QACrH,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,4BAA4B,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QACxG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEtG,iBAAiB;QACjB,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,4BAA4B,EAAE,IAAI,EAAE,UAAU,EAAE;KACjH,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,8CAA8C;QAC9C,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,0BAA0B,KAAK,EAAE;gBACvC,MAAM,EAAE,4BAA4B;gBACpC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,gCAAgC;YAChC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,aAAa;gBAChC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,gBAAgB;YAChB,sBAAsB;YACtB,kBAAkB;YAClB,mBAAmB;YACnB,6BAA6B;YAC7B,WAAW;YACX,uBAAuB;YACvB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,gBAAiC;QAChD,IAAI,EAAE,CAAC,gBAAgB,EAAE,aAAa,EAAE,cAAc,CAAC;KACxD,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,qCAAqC,CACnD,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,gBAAuB,EACvB,YAAmB,EACnB,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gDAAgD,IAAI,EAAE,CAAC,CAAC;IAE7E,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC9G,MAAM,sBAAsB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,aAAa,CAAC,CAAC;IACpH,MAAM,0BAA0B,GAAG,eAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,aAAa,CAAC,CAAC;IAChI,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,YAAY,CAAC,CAAC;IAC3H,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;IACjG,MAAM,0BAA0B,GAAG,eAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,gBAAgB,CAAC,CAAC;IACnI,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;IACjH,MAAM,6BAA6B,GAAG,eAAO,CAAC,eAAe,CAAC,4BAA4B,EAAE,4BAA4B,EAAE,gBAAgB,CAAC,CAAC;IAC5I,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,YAAY,CAAC,CAAC;IAChH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,yBAAyB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrH,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrH,EAAE,IAAI,EAAE,gCAAgC,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACtH,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,yBAAyB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrH,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,+BAA+B,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,EAAE,4BAA4B,EAAE,IAAI,EAAE,SAAS,EAAE;QACnI,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpH,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpH,EAAE,IAAI,EAAE,uCAAuC,EAAE,MAAM,EAAE,4BAA4B,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACtI,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAE9F,iBAAiB;QACjB,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,yBAAyB,EAAE,IAAI,EAAE,UAAU,EAAE;KACtH,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,2CAA2C;QAC3C,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,uBAAuB,KAAK,EAAE;gBACpC,MAAM,EAAE,yBAAyB;gBACjC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,2CAA2C;YAC3C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,aAAa;gBAChC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,oBAAoB;gBAC5B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,oBAAoB;YACpB,sBAAsB;YACtB,0BAA0B;YAC1B,wBAAwB;YACxB,gBAAgB;YAChB,0BAA0B;YAC1B,oBAAoB;YACpB,6BAA6B;YAC7B,qBAAqB;YACrB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,gBAAiC;QAChD,IAAI,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,aAAa,CAAC;KACtD,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa Technical Debt Workflow\n *\n * This module provides workflow templates for technical debt reduction:\n * - Code analysis\n * - Refactoring\n * - Testing\n * - Documentation\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { Logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n\n/**\n * Create a Technical Debt workflow for identifying and reducing technical debt\n */\nexport function createTechnicalDebtWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  analyzerAgent: Agent,\n  refactoringAgent: Agent,\n  testingAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating Technical Debt workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const codeAnalysisNode = Codessa.createAgentNode('code-analysis', 'Code Analysis', analyzerAgent);\n  const debtIdentificationNode = Codessa.createAgentNode('debt-identification', 'Debt Identification', analyzerAgent);\n  const prioritizationNode = Codessa.createAgentNode('prioritization', 'Prioritization', analyzerAgent);\n  const refactoringPlanNode = Codessa.createAgentNode('refactoring-plan', 'Refactoring Plan', refactoringAgent);\n  const refactoringImplementationNode = Codessa.createAgentNode('refactoring-implementation', 'Refactoring Implementation', refactoringAgent);\n  const testingNode = Codessa.createAgentNode('testing', 'Testing', testingAgent);\n  const documentationUpdateNode = Codessa.createAgentNode('documentation-update', 'Documentation Update', refactoringAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'default' },\n    { name: 'analysis-to-identification', source: 'code-analysis', target: 'debt-identification', type: 'default' },\n    { name: 'identification-to-prioritization', source: 'debt-identification', target: 'prioritization', type: 'default' },\n    { name: 'prioritization-to-plan', source: 'prioritization', target: 'refactoring-plan', type: 'default' },\n    { name: 'plan-to-implementation', source: 'refactoring-plan', target: 'refactoring-implementation', type: 'default' },\n    { name: 'implementation-to-testing', source: 'refactoring-implementation', target: 'testing', type: 'default' },\n    { name: 'testing-to-documentation', source: 'testing', target: 'documentation-update', type: 'default' },\n    { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'testing-to-implementation', source: 'testing', target: 'refactoring-implementation', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect refactoring implementation to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `implementation-to-tool-${index}`,\n        source: 'refactoring-implementation',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to testing\n      edges.push({\n        name: `tool-${index}-to-testing`,\n        source: `tool-${index}`,\n        target: 'testing',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      codeAnalysisNode,\n      debtIdentificationNode,\n      prioritizationNode,\n      refactoringPlanNode,\n      refactoringImplementationNode,\n      testingNode,\n      documentationUpdateNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'technical-debt' as OperationMode,\n    tags: ['technical-debt', 'refactoring', 'code-quality']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized Legacy Code Modernization workflow\n */\nexport function createLegacyCodeModernizationWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  analyzerAgent: Agent,\n  refactoringAgent: Agent,\n  testingAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating Legacy Code Modernization workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const codebaseAnalysisNode = Codessa.createAgentNode('codebase-analysis', 'Codebase Analysis', analyzerAgent);\n  const dependencyAnalysisNode = Codessa.createAgentNode('dependency-analysis', 'Dependency Analysis', analyzerAgent);\n  const architectureAssessmentNode = Codessa.createAgentNode('architecture-assessment', 'Architecture Assessment', analyzerAgent);\n  const testCoverageAnalysisNode = Codessa.createAgentNode('test-coverage-analysis', 'Test Coverage Analysis', testingAgent);\n  const testCreationNode = Codessa.createAgentNode('test-creation', 'Test Creation', testingAgent);\n  const incrementalRefactoringNode = Codessa.createAgentNode('incremental-refactoring', 'Incremental Refactoring', refactoringAgent);\n  const dependencyUpdateNode = Codessa.createAgentNode('dependency-update', 'Dependency Update', refactoringAgent);\n  const architectureModernizationNode = Codessa.createAgentNode('architecture-modernization', 'Architecture Modernization', refactoringAgent);\n  const regressionTestingNode = Codessa.createAgentNode('regression-testing', 'Regression Testing', testingAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-codebase-analysis', source: 'input', target: 'codebase-analysis', type: 'default' },\n    { name: 'codebase-to-dependency', source: 'codebase-analysis', target: 'dependency-analysis', type: 'default' },\n    { name: 'codebase-to-architecture', source: 'codebase-analysis', target: 'architecture-assessment', type: 'default' },\n    { name: 'codebase-to-test-coverage', source: 'codebase-analysis', target: 'test-coverage-analysis', type: 'default' },\n    { name: 'test-coverage-to-test-creation', source: 'test-coverage-analysis', target: 'test-creation', type: 'default' },\n    { name: 'test-creation-to-refactoring', source: 'test-creation', target: 'incremental-refactoring', type: 'default' },\n    { name: 'dependency-to-update', source: 'dependency-analysis', target: 'dependency-update', type: 'default' },\n    { name: 'architecture-to-modernization', source: 'architecture-assessment', target: 'architecture-modernization', type: 'default' },\n    { name: 'refactoring-to-testing', source: 'incremental-refactoring', target: 'regression-testing', type: 'default' },\n    { name: 'dependency-update-to-testing', source: 'dependency-update', target: 'regression-testing', type: 'default' },\n    { name: 'architecture-modernization-to-testing', source: 'architecture-modernization', target: 'regression-testing', type: 'default' },\n    { name: 'testing-to-output', source: 'regression-testing', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'testing-to-refactoring', source: 'regression-testing', target: 'incremental-refactoring', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect incremental refactoring to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `refactoring-to-tool-${index}`,\n        source: 'incremental-refactoring',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to regression testing\n      edges.push({\n        name: `tool-${index}-to-testing`,\n        source: `tool-${index}`,\n        target: 'regression-testing',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      codebaseAnalysisNode,\n      dependencyAnalysisNode,\n      architectureAssessmentNode,\n      testCoverageAnalysisNode,\n      testCreationNode,\n      incrementalRefactoringNode,\n      dependencyUpdateNode,\n      architectureModernizationNode,\n      regressionTestingNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'technical-debt' as OperationMode,\n    tags: ['legacy-code', 'modernization', 'refactoring']\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}