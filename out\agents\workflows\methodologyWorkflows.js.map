{"version": 3, "file": "methodologyWorkflows.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/methodologyWorkflows.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;AAaH,kDA0EC;AAKD,kDA4EC;AAKD,4CA0EC;AAKD,0DAkEC;AAKD,oDA0FC;AAzZD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAGtC;;GAEG;AACH,SAAgB,mBAAmB,CACjC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,iBAAwB,EACxB,cAAqB,EACrB,OAAc,EACd,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;IAEhD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,iBAAiB,CAAC,CAAC;IACvH,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;IACrH,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;IACrH,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC9F,MAAM,WAAW,GAAG,eAAO,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC3E,MAAM,UAAU,GAAG,eAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;IAC/E,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;IAC/G,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;IACvG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;QAChG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC5G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7F,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QACnF,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,mCAAmC,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACnH,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAE/F,iBAAiB;QACjB,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE;QAC9F,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,EAAE;KAC9G,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,qBAAqB;YACrB,qBAAqB;YACrB,qBAAqB;YACrB,eAAe;YACf,WAAW;YACX,UAAU;YACV,mBAAmB;YACnB,iBAAiB;YACjB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,WAAW,EAAE,OAAsB;KACpC,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CACjC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,iBAAwB,EACxB,gBAAuB,EACvB,cAAqB,EACrB,OAAc,EACd,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;IAEhD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;IAC5G,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAC3G,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAC/F,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC9F,MAAM,WAAW,GAAG,eAAO,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC3E,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;IACtG,MAAM,uBAAuB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC,CAAC;IAC1H,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;IACrH,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjG,EAAE,IAAI,EAAE,oCAAoC,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrH,EAAE,IAAI,EAAE,gCAAgC,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7F,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACjG,EAAE,IAAI,EAAE,uCAAuC,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3H,EAAE,IAAI,EAAE,4CAA4C,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrI,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEzG,iBAAiB;QACjB,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE;QACtG,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE;QAC9F,EAAE,IAAI,EAAE,uCAAuC,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,UAAU,EAAE;KAC7H,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,WAAW;YACX,gBAAgB;YAChB,uBAAuB;YACvB,qBAAqB;YACrB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,WAAW,EAAE,OAAsB;KACpC,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,cAAqB,EACrB,mBAA0B,EAC1B,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;IAE7C,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;IACzF,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAClG,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,wBAAwB,EAAE,cAAc,CAAC,CAAC;IACtG,MAAM,mBAAmB,GAAG,eAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;IACjH,MAAM,yBAAyB,GAAG,eAAO,CAAC,eAAe,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,cAAc,CAAC,CAAC;IAC9H,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC9F,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACnG,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC9G,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QACvF,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,gCAAgC,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,4CAA4C,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrI,EAAE,IAAI,EAAE,uCAAuC,EAAE,MAAM,EAAE,wBAAwB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3H,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,oCAAoC,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACrH,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEvG,iBAAiB;QACjB,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE;QACpG,EAAE,IAAI,EAAE,iCAAiC,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE;KACjH,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,aAAa;YACb,gBAAgB;YAChB,aAAa;YACb,mBAAmB;YACnB,yBAAyB;YACzB,eAAe;YACf,gBAAgB;YAChB,oBAAoB;YACpB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,WAAW,EAAE,IAAmB;KACjC,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CACrC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,YAAmB,EACnB,aAAoB,EACpB,cAAqB,EACrB,WAAkB,EAClB,gBAAuB,EACvB,QAAoC,EAAE;IAEtC,eAAM,CAAC,IAAI,CAAC,gCAAgC,IAAI,EAAE,CAAC,CAAC;IAEpD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,cAAc,EAAE,uBAAuB,EAAE,YAAY,CAAC,CAAC;IACxG,MAAM,UAAU,GAAG,eAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IACrF,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACvG,MAAM,WAAW,GAAG,eAAO,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IAC/E,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;IAC3F,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAChG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3F,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7F,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QACjG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3F,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;KAC5F,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,gBAAgB;YAChB,UAAU;YACV,kBAAkB;YAClB,WAAW;YACX,cAAc;YACd,eAAe;YACf,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,WAAW,EAAE,WAA0B;KACxC,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,cAAqB,EACrB,WAAkB,EAClB,OAAc,EACd,QAAiB,EAAE;IAEnB,eAAM,CAAC,IAAI,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAC;IAEjD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,YAAY,GAAG,eAAO,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACrF,MAAM,UAAU,GAAG,eAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;IAC/E,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IACzE,MAAM,WAAW,GAAG,eAAO,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC3E,MAAM,WAAW,GAAG,eAAO,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IAC/E,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;IACxF,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;IACrF,MAAM,cAAc,GAAG,eAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;IACxF,MAAM,YAAY,GAAG,eAAO,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC9E,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;QACnF,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QACrF,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/E,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QACjF,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;QACrF,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3F,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/F,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/F,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7F,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAErF,iBAAiB;QACjB,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE;QAC1F,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE;QACpF,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE;KACjG,CAAC;IAEF,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,YAAY;YACZ,UAAU;YACV,SAAS;YACT,WAAW;YACX,WAAW;YACX,cAAc;YACd,aAAa;YACb,cAAc;YACd,YAAY;YACZ,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,SAAS;QACxB,WAAW,EAAE,QAAuB;QACpC,UAAU,EAAE;YACV,YAAY;YACZ,UAAU;YACV,SAAS;YACT,WAAW;YACX,WAAW;YACX,cAAc;YACd,aAAa;YACb,cAAc;YACd,YAAY;SACb;QACD,SAAS,EAAE,SAAS;KACrB,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa Methodology Workflows\n *\n * This module provides workflow templates for different development methodologies:\n * - Agile\n * - Scrum\n * - XP (Extreme Programming)\n * - Waterfall\n * - DevOps\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { Codessa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, Methodology } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\n\n/**\n * Create an Agile workflow for iterative development\n */\nexport function createAgileWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  productOwnerAgent: Agent,\n  developerAgent: Agent,\n  qaAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Agile workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const userStoryCreationNode = Codessa.createAgentNode('user-story-creation', 'User Story Creation', productOwnerAgent);\n  const backlogRefinementNode = Codessa.createAgentNode('backlog-refinement', 'Backlog Refinement', productOwnerAgent);\n  const iterationPlanningNode = Codessa.createAgentNode('iteration-planning', 'Iteration Planning', productOwnerAgent);\n  const developmentNode = Codessa.createAgentNode('development', 'Development', developerAgent);\n  const testingNode = Codessa.createAgentNode('testing', 'Testing', qaAgent);\n  const reviewNode = Codessa.createAgentNode('review', 'Review', developerAgent);\n  const iterationReviewNode = Codessa.createAgentNode('iteration-review', 'Iteration Review', productOwnerAgent);\n  const retrospectiveNode = Codessa.createAgentNode('retrospective', 'Retrospective', productOwnerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-user-story', source: 'input', target: 'user-story-creation', type: 'default' },\n    { name: 'user-story-to-backlog', source: 'user-story-creation', target: 'backlog-refinement', type: 'default' },\n    { name: 'backlog-to-planning', source: 'backlog-refinement', target: 'iteration-planning', type: 'default' },\n    { name: 'planning-to-development', source: 'iteration-planning', target: 'development', type: 'default' },\n    { name: 'development-to-testing', source: 'development', target: 'testing', type: 'default' },\n    { name: 'testing-to-review', source: 'testing', target: 'review', type: 'default' },\n    { name: 'review-to-iteration-review', source: 'review', target: 'iteration-review', type: 'default' },\n    { name: 'iteration-review-to-retrospective', source: 'iteration-review', target: 'retrospective', type: 'default' },\n    { name: 'retrospective-to-output', source: 'retrospective', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'testing-to-development', source: 'testing', target: 'development', type: 'feedback' },\n    { name: 'retrospective-to-backlog', source: 'retrospective', target: 'backlog-refinement', type: 'feedback' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      userStoryCreationNode,\n      backlogRefinementNode,\n      iterationPlanningNode,\n      developmentNode,\n      testingNode,\n      reviewNode,\n      iterationReviewNode,\n      retrospectiveNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    methodology: 'agile' as Methodology\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a Scrum workflow for sprint-based development\n */\nexport function createScrumWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  productOwnerAgent: Agent,\n  scrumMasterAgent: Agent,\n  developerAgent: Agent,\n  qaAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Scrum workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const productBacklogNode = Codessa.createAgentNode('product-backlog', 'Product Backlog', productOwnerAgent);\n  const sprintPlanningNode = Codessa.createAgentNode('sprint-planning', 'Sprint Planning', scrumMasterAgent);\n  const dailyScrumNode = Codessa.createAgentNode('daily-scrum', 'Daily Scrum', scrumMasterAgent);\n  const developmentNode = Codessa.createAgentNode('development', 'Development', developerAgent);\n  const testingNode = Codessa.createAgentNode('testing', 'Testing', qaAgent);\n  const sprintReviewNode = Codessa.createAgentNode('sprint-review', 'Sprint Review', productOwnerAgent);\n  const sprintRetrospectiveNode = Codessa.createAgentNode('sprint-retrospective', 'Sprint Retrospective', scrumMasterAgent);\n  const backlogRefinementNode = Codessa.createAgentNode('backlog-refinement', 'Backlog Refinement', productOwnerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-product-backlog', source: 'input', target: 'product-backlog', type: 'default' },\n    { name: 'product-backlog-to-sprint-planning', source: 'product-backlog', target: 'sprint-planning', type: 'default' },\n    { name: 'sprint-planning-to-daily-scrum', source: 'sprint-planning', target: 'daily-scrum', type: 'default' },\n    { name: 'daily-scrum-to-development', source: 'daily-scrum', target: 'development', type: 'default' },\n    { name: 'development-to-testing', source: 'development', target: 'testing', type: 'default' },\n    { name: 'testing-to-sprint-review', source: 'testing', target: 'sprint-review', type: 'default' },\n    { name: 'sprint-review-to-sprint-retrospective', source: 'sprint-review', target: 'sprint-retrospective', type: 'default' },\n    { name: 'sprint-retrospective-to-backlog-refinement', source: 'sprint-retrospective', target: 'backlog-refinement', type: 'default' },\n    { name: 'backlog-refinement-to-output', source: 'backlog-refinement', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'daily-scrum-to-daily-scrum', source: 'daily-scrum', target: 'daily-scrum', type: 'feedback' },\n    { name: 'testing-to-development', source: 'testing', target: 'development', type: 'feedback' },\n    { name: 'backlog-refinement-to-product-backlog', source: 'backlog-refinement', target: 'product-backlog', type: 'feedback' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      productBacklogNode,\n      sprintPlanningNode,\n      dailyScrumNode,\n      developmentNode,\n      testingNode,\n      sprintReviewNode,\n      sprintRetrospectiveNode,\n      backlogRefinementNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    methodology: 'scrum' as Methodology\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create an XP (Extreme Programming) workflow\n */\nexport function createXPWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  customerAgent: Agent,\n  developerAgent: Agent,\n  pairProgrammerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating XP workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const userStoryNode = Codessa.createAgentNode('user-story', 'User Story', customerAgent);\n  const planningGameNode = Codessa.createAgentNode('planning-game', 'Planning Game', customerAgent);\n  const testFirstNode = Codessa.createAgentNode('test-first', 'Test-First Development', developerAgent);\n  const pairProgrammingNode = Codessa.createAgentNode('pair-programming', 'Pair Programming', pairProgrammerAgent);\n  const continuousIntegrationNode = Codessa.createAgentNode('continuous-integration', 'Continuous Integration', developerAgent);\n  const refactoringNode = Codessa.createAgentNode('refactoring', 'Refactoring', developerAgent);\n  const smallReleaseNode = Codessa.createAgentNode('small-release', 'Small Release', developerAgent);\n  const customerFeedbackNode = Codessa.createAgentNode('customer-feedback', 'Customer Feedback', customerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-user-story', source: 'input', target: 'user-story', type: 'default' },\n    { name: 'user-story-to-planning-game', source: 'user-story', target: 'planning-game', type: 'default' },\n    { name: 'planning-game-to-test-first', source: 'planning-game', target: 'test-first', type: 'default' },\n    { name: 'test-first-to-pair-programming', source: 'test-first', target: 'pair-programming', type: 'default' },\n    { name: 'pair-programming-to-continuous-integration', source: 'pair-programming', target: 'continuous-integration', type: 'default' },\n    { name: 'continuous-integration-to-refactoring', source: 'continuous-integration', target: 'refactoring', type: 'default' },\n    { name: 'refactoring-to-small-release', source: 'refactoring', target: 'small-release', type: 'default' },\n    { name: 'small-release-to-customer-feedback', source: 'small-release', target: 'customer-feedback', type: 'default' },\n    { name: 'customer-feedback-to-output', source: 'customer-feedback', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'refactoring-to-test-first', source: 'refactoring', target: 'test-first', type: 'feedback' },\n    { name: 'customer-feedback-to-user-story', source: 'customer-feedback', target: 'user-story', type: 'feedback' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      userStoryNode,\n      planningGameNode,\n      testFirstNode,\n      pairProgrammingNode,\n      continuousIntegrationNode,\n      refactoringNode,\n      smallReleaseNode,\n      customerFeedbackNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    methodology: 'xp' as Methodology\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a Waterfall workflow for sequential development\n */\nexport function createWaterfallWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  analystAgent: Agent,\n  designerAgent: Agent,\n  developerAgent: Agent,\n  testerAgent: Agent,\n  maintenanceAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  logger.info(`Creating Waterfall workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const requirementsNode = Codessa.createAgentNode('requirements', 'Requirements Analysis', analystAgent);\n  const designNode = Codessa.createAgentNode('design', 'System Design', designerAgent);\n  const implementationNode = Codessa.createAgentNode('implementation', 'Implementation', developerAgent);\n  const testingNode = Codessa.createAgentNode('testing', 'Testing', testerAgent);\n  const deploymentNode = Codessa.createAgentNode('deployment', 'Deployment', developerAgent);\n  const maintenanceNode = Codessa.createAgentNode('maintenance', 'Maintenance', maintenanceAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-requirements', source: 'input', target: 'requirements', type: 'default' },\n    { name: 'requirements-to-design', source: 'requirements', target: 'design', type: 'default' },\n    { name: 'design-to-implementation', source: 'design', target: 'implementation', type: 'default' },\n    { name: 'implementation-to-testing', source: 'implementation', target: 'testing', type: 'default' },\n    { name: 'testing-to-deployment', source: 'testing', target: 'deployment', type: 'default' },\n    { name: 'deployment-to-maintenance', source: 'deployment', target: 'maintenance', type: 'default' },\n    { name: 'maintenance-to-output', source: 'maintenance', target: 'output', type: 'default' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      requirementsNode,\n      designNode,\n      implementationNode,\n      testingNode,\n      deploymentNode,\n      maintenanceNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    methodology: 'waterfall' as Methodology\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a DevOps workflow for continuous integration and delivery\n */\nexport function createDevOpsWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  developerAgent: Agent,\n  devOpsAgent: Agent,\n  qaAgent: Agent,\n  tools: ITool[] = []\n): GraphDefinition {\n  logger.info(`Creating DevOps workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const planningNode = Codessa.createAgentNode('planning', 'Planning', developerAgent);\n  const codingNode = Codessa.createAgentNode('coding', 'Coding', developerAgent);\n  const buildNode = Codessa.createAgentNode('build', 'Build', devOpsAgent);\n  const testingNode = Codessa.createAgentNode('testing', 'Testing', qaAgent);\n  const releaseNode = Codessa.createAgentNode('release', 'Release', devOpsAgent);\n  const deploymentNode = Codessa.createAgentNode('deployment', 'Deployment', devOpsAgent);\n  const operationNode = Codessa.createAgentNode('operation', 'Operation', devOpsAgent);\n  const monitoringNode = Codessa.createAgentNode('monitoring', 'Monitoring', devOpsAgent);\n  const feedbackNode = Codessa.createAgentNode('feedback', 'Feedback', qaAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-planning', source: 'input', target: 'planning', type: 'default' },\n    { name: 'planning-to-coding', source: 'planning', target: 'coding', type: 'default' },\n    { name: 'coding-to-build', source: 'coding', target: 'build', type: 'default' },\n    { name: 'build-to-testing', source: 'build', target: 'testing', type: 'default' },\n    { name: 'testing-to-release', source: 'testing', target: 'release', type: 'default' },\n    { name: 'release-to-deployment', source: 'release', target: 'deployment', type: 'default' },\n    { name: 'deployment-to-operation', source: 'deployment', target: 'operation', type: 'default' },\n    { name: 'operation-to-monitoring', source: 'operation', target: 'monitoring', type: 'default' },\n    { name: 'monitoring-to-feedback', source: 'monitoring', target: 'feedback', type: 'default' },\n    { name: 'feedback-to-output', source: 'feedback', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'feedback-to-planning', source: 'feedback', target: 'planning', type: 'feedback' },\n    { name: 'testing-to-coding', source: 'testing', target: 'coding', type: 'feedback' },\n    { name: 'monitoring-to-operation', source: 'monitoring', target: 'operation', type: 'feedback' }\n  ];\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      planningNode,\n      codingNode,\n      buildNode,\n      testingNode,\n      releaseNode,\n      deploymentNode,\n      operationNode,\n      monitoringNode,\n      feedbackNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'agentic',\n    methodology: 'devops' as Methodology,\n    agentNodes: [\n      planningNode,\n      codingNode,\n      buildNode,\n      testingNode,\n      releaseNode,\n      deploymentNode,\n      operationNode,\n      monitoringNode,\n      feedbackNode\n    ],\n    toolNodes: toolNodes\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}"]}