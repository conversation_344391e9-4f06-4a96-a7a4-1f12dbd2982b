{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/checkpoint/index.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,yDAAuF;AAA9E,sHAAA,iBAAiB,OAAA;AAAc,sHAAA,iBAAiB,OAAA", "sourcesContent": ["/**\n * Checkpoint System for Code Changes\n * \n * This module provides a checkpoint system for tracking and managing code changes\n * during AI-assisted development.\n */\n\nexport { CheckpointManager, Checkpoint, checkpointManager } from './checkpointManager';\n"]}