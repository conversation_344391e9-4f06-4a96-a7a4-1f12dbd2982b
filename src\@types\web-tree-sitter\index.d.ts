// Type definitions for web-tree-sitter
declare module 'web-tree-sitter' {
  export interface Point {
    row: number;
    column: number;
  }

  export interface Range {
    startPosition: Point;
    endPosition: Point;
    startIndex: number;
    endIndex: number;
  }

  export interface Tree {
    rootNode: SyntaxNode;
    edit(delta: Edit): Tree;
    walk(): TreeCursor;
  }

  export interface TreeCursor {
    nodeType: string;
    nodeText: string;
    startPosition: Point;
    endPosition: Point;
    startIndex: number;
    endIndex: number;
    gotoFirstChild(): boolean;
    gotoNextSibling(): boolean;
    gotoParent(): boolean;
    delete(): void;
  }

  export interface SyntaxNode {
    type: string;
    text: string;
    startPosition: Point;
    endPosition: Point;
    startIndex: number;
    endIndex: number;
    parent: SyntaxNode | null;
    children: SyntaxNode[];
    namedChildren: SyntaxNode[];
    childCount: number;
    namedChildCount: number;
    firstChild: SyntaxNode | null;
    lastChild: SyntaxNode | null;
    firstNamedChild: SyntaxNode | null;
    lastNamedChild: SyntaxNode | null;
    nextSibling: SyntaxNode | null;
    previousSibling: SyntaxNode | null;
    nextNamedSibling: SyntaxNode | null;
    previousNamedSibling: SyntaxNode | null;
    hasError: boolean;
    hasChanges: boolean;
    hasMissingFields: boolean;
    
    equals(other: SyntaxNode): boolean;
    child(index: number): SyntaxNode | null;
    namedChild(index: number): SyntaxNode | null;
    childForFieldName(fieldName: string): SyntaxNode | null;
    childForFieldId(fieldId: number): SyntaxNode | null;
    descendantForIndex(index: number): SyntaxNode;
    descendantForIndex(startIndex: number, endIndex: number): SyntaxNode;
    namedDescendantForIndex(index: number): SyntaxNode;
    namedDescendantForIndex(startIndex: number, endIndex: number): SyntaxNode;
    descendantForPosition(position: Point): SyntaxNode;
    descendantForPosition(startPosition: Point, endPosition: Point): SyntaxNode;
    namedDescendantForPosition(position: Point): SyntaxNode;
    namedDescendantForPosition(startPosition: Point, endPosition: Point): SyntaxNode;
    walk(): TreeCursor;
  }

  export interface Edit {
    startIndex: number;
    oldEndIndex: number;
    newEndIndex: number;
    startPosition: Point;
    oldEndPosition: Point;
    newEndPosition: Point;
  }

  export interface Parser {
    parse(input: string | { read(length: number): string }, previousTree?: Tree): Tree;
    parseTextBuffer(textBuffer: any, previousTree?: Tree): Tree;
    getLanguage(): Language;
    setLanguage(language: Language): void;
    getLogger(): Logger;
    setLogger(logger: Logger | boolean): void;
  }

  export interface Language {
    query(source: string): Query;
  }

  export interface Query {
    matches(node: SyntaxNode, startPosition?: Point, endPosition?: Point): QueryMatch[];
    captures(node: SyntaxNode, startPosition?: Point, endPosition?: Point): QueryCapture[];
  }

  export interface QueryMatch {
    pattern: number;
    captures: QueryCapture[];
  }

  export interface QueryCapture {
    name: string;
    node: SyntaxNode;
  }

  export interface Logger {
    // Logger interface can be extended as needed
  }

  export function init(): Promise<void>;
  
  export const Parser: new () => Parser;
}

// This allows TypeScript to understand the global Parser type
declare const Parser: new () => import('web-tree-sitter').Parser;

export {};
