{"version": 3, "file": "crossRepositoryContext.js", "sourceRoot": "", "sources": ["../../src/intelligence/crossRepositoryContext.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AAKzB,sCAAmC;AAsFnC,MAAa,sBAAsB;IACzB,eAAe,CAAkB;IACjC,YAAY,CAAe;IAC3B,aAAa,CAAsB;IACnC,mBAAmB,CAA0B;IAErD,sBAAsB;IACd,YAAY,GAAgC,IAAI,GAAG,EAAE,CAAC;IACtD,aAAa,GAAyC,IAAI,GAAG,EAAE,CAAC;IAChE,cAAc,GAA+B,IAAI,GAAG,EAAE,CAAC;IACvD,kBAAkB,GAAmC,IAAI,GAAG,EAAE,CAAC;IAEvE,iBAAiB;IACT,aAAa,GAAoD,IAAI,GAAG,EAAE,CAAC;IAClE,WAAW,GAAG,OAAO,CAAC,CAAC,aAAa;IAErD,YACE,eAAgC,EAChC,YAA0B,EAC1B,aAAkC,EAClC,mBAA4C;QAE5C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAE/C,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAE/D,wBAAwB;YACxB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAElC,wBAAwB;YACxB,MAAM,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAE5C,mBAAmB;YACnB,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEtC,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,eAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yDAAyD,KAAK,EAAE,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,oBAAoB;QAC/B,MAAM,YAAY,GAAqB,EAAE,CAAC;QAE1C,kCAAkC;QAClC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACtC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;gBACvD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACjE,IAAI,QAAQ,EAAE,CAAC;oBACb,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC9D,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;gBACnC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;oBACvC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC3B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,cAAc,YAAY,CAAC,MAAM,eAAe,CAAC,CAAC;QAC9D,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,8BAA8B;QACzC,MAAM,aAAa,GAA4B,EAAE,CAAC;QAClD,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;QAErD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC1C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5E,IAAI,YAAY,EAAE,CAAC;oBACjB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAEjC,oCAAoC;oBACpC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;wBACzC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC1C,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;wBACzC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC1C,CAAC;oBAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACxD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAE,CAAC,IAAI,CAAC;wBACxC,GAAG,YAAY;wBACf,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;wBACvB,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;qBACxB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,YAAY,aAAa,CAAC,MAAM,2BAA2B,CAAC,CAAC;QACzE,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,wBAAwB,CACnC,WAAmB,EACnB,aAAuB,EACvB,eAAmF;QAEnF,eAAM,CAAC,IAAI,CAAC,oCAAoC,WAAW,EAAE,CAAC,CAAC;QAE/D,MAAM,aAAa,GAAG,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC/C,MAAM,OAAO,GAAoC,EAAE,CAAC;QAEpD,oCAAoC;QACpC,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,CAAC,IAAI;gBAAE,SAAS;YAEpB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;YAC5F,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,UAAU,EAAE,WAAW,CAAC,UAAU;aACnC,CAAC,CAAC;QACL,CAAC;QAED,uCAAuC;QACvC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;QAExE,yBAAyB;QACzB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAE3E,yBAAyB;QACzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE9D,MAAM,WAAW,GAAyB;YACxC,EAAE,EAAE,aAAa;YACjB,WAAW;YACX,aAAa;YACb,OAAO;YACP,YAAY;YACZ,eAAe;YACf,SAAS;YACT,YAAY;SACb,CAAC;QAEF,0BAA0B;QAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAC1C,mCAAmC,WAAW,EAAE,EAChD;YACE,QAAQ,EAAE,wBAAwB;YAClC,cAAc,EAAE,YAAY;SAC7B,EACD,QAAQ,EACR,EAAE,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC,EAAE,CACpD,CAAC;QAEF,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,2BAA2B,CAAC,WAAiC;QACxE,eAAM,CAAC,IAAI,CAAC,qCAAqC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;QAEnE,IAAI,CAAC;YACH,sCAAsC;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC;YAEpG,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;gBACnC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAChD,IAAI,CAAC,IAAI;oBAAE,SAAS;gBAEpB,eAAM,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAE5D,iCAAiC;gBACjC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC3D,IAAI,YAAY,EAAE,CAAC;oBACjB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;wBAC1C,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE;4BAC5D,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,QAAQ,EAAE,SAAS,CAAC,QAAQ;4BAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;4BAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,MAAM,EAAE,SAAS,CAAC,MAAM;yBACzB,CAAC,CAAC;wBAEH,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;4BACpB,eAAM,CAAC,KAAK,CAAC,iCAAiC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;4BAC9D,gCAAgC;4BAChC,OAAO,KAAK,CAAC;wBACf,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,wBAAwB;YACxB,MAAM,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAC1C,oCAAoC,WAAW,CAAC,WAAW,EAAE,EAC7D;gBACE,QAAQ,EAAE,wBAAwB;gBAClC,cAAc,EAAE,YAAY;aAC7B,EACD,QAAQ,EACR,EAAE,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC,EAAE,CACpD,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,wBAAwB;QACnC,MAAM,SAAS,GAAwB,EAAE,CAAC;QAE1C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjE,IAAI,SAAS,EAAE,CAAC;oBACd,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC1B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,YAAY,SAAS,CAAC,MAAM,sBAAsB,CAAC,CAAC;QAChE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,mBAAmB;QAC9B,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,8BAA8B;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEjD,+BAA+B;QAC/B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAExD,yBAAyB;QACzB,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,UAAU,EAAE,CAAC;YAC1C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,OAAO,GAAkB;oBAC7B,EAAE,EAAE,UAAU,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;oBACrC,IAAI,EAAE,sBAAsB,OAAO,EAAE;oBACrC,WAAW,EAAE,qBAAqB,OAAO,gBAAgB,KAAK,CAAC,MAAM,eAAe;oBACpF,KAAK;oBACL,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;oBAC3C,WAAW,EAAE,EAAE;oBACf,eAAe,EAAE,EAAE;oBACnB,sBAAsB,EAAE,EAAE;oBAC1B,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;oBACvB,OAAO,EAAE,OAAO;iBACjB,CAAC;gBAEF,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM,kBAAkB,CAAC,CAAC;QACxD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,oBAAoB;QAC/B,MAAM,QAAQ,GAAG;YACf,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACzC,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM;YACpE,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;YACxC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI;YACvC,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACtD,sBAAsB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YACxD,wBAAwB,EAAE,MAAM,IAAI,CAAC,gCAAgC,EAAE;SACxE,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,yBAAyB;IACjB,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,iCAAiC;YACjC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,6BAA6B;YAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACjD,IAAI,SAAS,GAAG,EAAE,CAAC;YACnB,IAAI,MAAM,GAAG,MAAM,CAAC;YACpB,IAAI,UAAU,GAAG,EAAE,CAAC;YAEpB,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAC/E,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBACzB,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC;gBAClC,CAAC;gBAED,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;gBACnF,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBACzB,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;gBAC/B,CAAC;gBAED,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAChF,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBACzB,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC;gBACnC,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAErF,4BAA4B;YAC5B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAEzD,MAAM,QAAQ,GAAmB;gBAC/B,EAAE,EAAE,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACnD,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC7B,IAAI,EAAE,QAAQ;gBACd,SAAS;gBACT,MAAM;gBACN,UAAU;gBACV,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC;gBACzE,YAAY,EAAE,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;gBACrE,UAAU;gBACV,YAAY,EAAE,EAAE;gBAChB,kBAAkB,EAAE,EAAE;aACvB,CAAC;YAEF,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,IAAoB;QACxD,MAAM,OAAO,GAAqB,EAAE,CAAC;QAErC,4CAA4C;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAE/E,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,IAAI,OAAO,CAAC,WAAW,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;oBACxD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBACvD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;oBAC9D,IAAI,WAAW,EAAE,CAAC;wBAChB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC5B,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,wCAAwC,KAAK,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAqB,EAAE,KAAqB;QAChF,8BAA8B;QAC9B,MAAM,UAAU,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAEtF,kCAAkC;QAClC,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,QAAQ,IAAI,UAAU,CAAC,MAAM,GAAG,GAAG,CAAC;QAEpC,iCAAiC;QACjC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACvE,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,GAAG,CAAC;QAErC,0EAA0E;QAC1E,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1D,QAAQ,IAAI,GAAG,CAAC;QAClB,CAAC;QAED,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,CAAC,qBAAqB;QACpC,CAAC;QAED,8BAA8B;QAC9B,IAAI,gBAAgB,GAA8C,YAAY,CAAC;QAC/E,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACzC,gBAAgB,GAAG,kBAAkB,CAAC;QACxC,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,gBAAgB,GAAG,gBAAgB,CAAC;QACtC,CAAC;QAED,OAAO;YACL,UAAU,EAAE,KAAK,CAAC,EAAE;YACpB,UAAU,EAAE,KAAK,CAAC,EAAE;YACpB,gBAAgB;YAChB,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC;YAC/B,WAAW,EAAE,UAAU,UAAU,CAAC,MAAM,qBAAqB,WAAW,CAAC,MAAM,gBAAgB;YAC/F,WAAW;YACX,kBAAkB,EAAE,UAAU;YAC9B,qBAAqB,EAAE,EAAE;SAC1B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,SAAiB;QAChE,uEAAuE;QACvE,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAEjD,sCAAsC;YACtC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBACrD,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gBAE7D,IAAI,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;oBACvC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAe;QACvC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAE5E,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;gBAEhD,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBACxF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;oBAClD,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;gBAC1B,CAAC;qBAAM,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1B,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,qBAAqB,CAAC,SAAyC;QACrE,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,eAAe,GAAG,SAAS,CAAC;QAEhC,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1D,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC;gBACrB,QAAQ,GAAG,KAAK,CAAC;gBACjB,eAAe,GAAG,QAAQ,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC7C,uCAAuC;QACvC,MAAM,kBAAkB,GAAG;YACzB,YAAY;YACZ,SAAS;YACT,WAAW;YACX,UAAU;YACV,MAAM;YACN,MAAM;SACP,CAAC;QAEF,KAAK,MAAM,SAAS,IAAI,kBAAkB,EAAE,CAAC;YAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACrD,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,oCAAoC;QACpC,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC5D,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;gBACpF,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC;oBAC3B,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,QAAgB;QACrD,IAAI,CAAC;YACH,MAAM,SAAS,GAAsB;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,EAAE;gBACZ,kBAAkB,EAAE,EAAE;gBACtB,WAAW,EAAE,QAAQ;gBACrB,eAAe,EAAE,EAAE;gBACnB,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,sBAAsB;YACtB,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC;gBACrD,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC;YAClC,CAAC;iBAAM,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;gBACzD,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;YAC/B,CAAC;iBAAM,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC;gBAC3D,SAAS,CAAC,WAAW,GAAG,MAAM,CAAC;YACjC,CAAC;YAED,gBAAgB;YAChB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACpD,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/B,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;gBAEpF,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;oBAC9B,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC;wBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;wBACrD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;wBACnE,IAAI,WAAW,EAAE,CAAC;4BAChB,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBACvC,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QAEnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,WAAmB;QACtD,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAE/D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;YAEpF,OAAO;gBACL,IAAI,EAAE,WAAW,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBACpD,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,WAAW,CAAC,OAAO,IAAI,OAAO;gBACvC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC;gBACzD,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,IAAI,EAAE,CAAC;gBAC/D,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,IAAI,EAAE,CAAC;gBACjE,OAAO,EAAE,WAAW,CAAC,OAAO,IAAI,EAAE;gBAClC,SAAS,EAAE,WAAW,CAAC,OAAO,IAAI,KAAK;aACxC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,sCAAsC,WAAW,KAAK,KAAK,EAAE,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,sBAAsB;QAC5B,MAAM,UAAU,GAAG,IAAI,GAAG,EAAoB,CAAC;QAE/C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;oBACzB,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBAC1B,CAAC;gBACD,UAAU,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAoB,CAAC;QAEhD,uDAAuD;QACvD,oCAAoC;QAEpC,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,wBAAwB;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEjD,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;aACpC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;aAC3D,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC;aAChC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;aACzC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAClB,CAAC;IAEO,yBAAyB;QAC/B,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAExE,OAAO,gBAAgB;aACpB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;aACvC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,gCAAgC;QAC5C,MAAM,aAAa,GAAa,EAAE,CAAC;QAEnC,8CAA8C;QAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACjD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,UAAU,EAAE,CAAC;YACtC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,aAAa,CAAC,IAAI,CAAC,8BAA8B,GAAG,YAAY,KAAK,CAAC,MAAM,eAAe,CAAC,CAAC;YAC/F,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,6CAA6C;YAC7C,aAAa,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,wBAAwB,CACpC,IAAoB,EACpB,WAAmB,EACnB,eAAuB;QAEvB,eAAM,CAAC,IAAI,CAAC,oCAAoC,IAAI,CAAC,IAAI,KAAK,WAAW,KAAK,eAAe,GAAG,CAAC,CAAC;QAElG,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,MAAM,UAAU,GAA2B,EAAE,CAAC;QAE9C,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,MAAM,GAAG;6DACwC,IAAI,CAAC,IAAI;uCAC/B,WAAW;gCAClB,eAAe;mCACZ,IAAI,CAAC,QAAQ;4BACpB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;aAoBxD,CAAC;YAER,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBAC5C,MAAM;gBACN,IAAI,EAAE,KAAK;aACZ,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC5D,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5B,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,8BAA8B,CAAC,OAAwC;QACnF,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,2DAA2D;QAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE3B,yDAAyD;gBACzD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAClD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAElD,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC;oBACnB,gCAAgC;oBAChC,MAAM,UAAU,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;oBACtF,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC1B,YAAY,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,OAAO,OAAO,CAAC,IAAI,yBAAyB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACxG,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,qBAAqB,CAAC,OAAwC;QACpE,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjF,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAE3F,IAAI,eAAe,GAA8B,KAAK,CAAC;QACvD,IAAI,SAAS,GAA8B,KAAK,CAAC;QAEjD,IAAI,UAAU,GAAG,EAAE,IAAI,eAAe,GAAG,EAAE,EAAE,CAAC;YAC5C,eAAe,GAAG,MAAM,CAAC;YACzB,SAAS,GAAG,MAAM,CAAC;QACrB,CAAC;aAAM,IAAI,UAAU,GAAG,EAAE,IAAI,eAAe,GAAG,EAAE,EAAE,CAAC;YACnD,eAAe,GAAG,QAAQ,CAAC;YAC3B,SAAS,GAAG,QAAQ,CAAC;QACvB,CAAC;QAED,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAwC;QACzE,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,mDAAmD;QACnD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,CAAC,IAAI,CAAC,wBAAwB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;gBACjD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBAChC,KAAK,CAAC,IAAI,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAC;gBAClD,CAAC;gBACD,KAAK,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAC9D,KAAK,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAE/D,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,yBAAyB,CAAC,OAAwC,EAAE,YAAsB;QAChG,sEAAsE;QACtE,MAAM,MAAM,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;QAE5B,sDAAsD;QACtD,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;YAC/B,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;YAC7D,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;YAE7D,IAAI,WAAW,GAAG,WAAW,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC1E,4BAA4B;gBAC5B,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,wBAAwB,CAAC,QAAgB;QAC/C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,OAAO;oBACL,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;oBACzB,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;iBACpC,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;IACvC,CAAC;IAEO,mBAAmB;QACzB,+BAA+B;QAC/B,MAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,GAAG,EAAE;YAChD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACE,OAAO;QACZ,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACnD,CAAC;CACF;AA30BD,wDA20BC", "sourcesContent": ["/**\n * Cross-Repository Context System - Multi-repo intelligence\n *\n * Provides multi-repo awareness, cross-repo refactoring, dependency tracking,\n * shared context, and monorepo support while integrating with existing\n * Codessa infrastructure.\n */\n\nimport * as vscode from 'vscode';\nimport * as path from 'path';\nimport * as fs from 'fs';\nimport { SupervisorAgent } from '../agents/agentTypes/supervisorAgent';\nimport { ToolRegistry } from '../tools/toolRegistry';\nimport { QuantumMemorySystem } from '../memory/quantum/quantumMemorySystem';\nimport { ProjectWideIntelligence } from './projectWideIntelligence';\nimport { logger } from '../logger';\n\nexport interface RepositoryInfo {\n  id: string;\n  name: string;\n  path: string;\n  remoteUrl?: string;\n  branch: string;\n  lastCommit: string;\n  language: string;\n  framework?: string;\n  dependencies: string[];\n  isMonorepo: boolean;\n  subprojects?: string[];\n  relatedRepos: string[];\n  sharedDependencies: string[];\n}\n\nexport interface CrossRepoRelationship {\n  sourceRepo: string;\n  targetRepo: string;\n  relationshipType: 'dependency' | 'shared-library' | 'microservice' | 'monorepo-package' | 'fork' | 'template';\n  strength: number; // 0-1 scale\n  description: string;\n  sharedFiles: string[];\n  sharedDependencies: string[];\n  communicationPatterns: string[];\n}\n\nexport interface CrossRepoRefactoring {\n  id: string;\n  description: string;\n  affectedRepos: string[];\n  changes: {\n    repo: string;\n    files: string[];\n    operations: RefactoringOperation[];\n  }[];\n  dependencies: string[];\n  estimatedImpact: 'low' | 'medium' | 'high';\n  riskLevel: 'low' | 'medium' | 'high';\n  rollbackPlan: string;\n}\n\nexport interface RefactoringOperation {\n  type: 'rename' | 'move' | 'extract' | 'inline' | 'update-dependency' | 'update-api';\n  file: string;\n  oldValue: string;\n  newValue: string;\n  line?: number;\n  column?: number;\n}\n\nexport interface MonorepoStructure {\n  rootPath: string;\n  packages: MonorepoPackage[];\n  sharedDependencies: string[];\n  buildSystem: 'lerna' | 'nx' | 'rush' | 'yarn-workspaces' | 'npm-workspaces' | 'custom';\n  workspaceConfig: any;\n  dependencyGraph: { [packageName: string]: string[] };\n}\n\nexport interface MonorepoPackage {\n  name: string;\n  path: string;\n  version: string;\n  dependencies: string[];\n  devDependencies: string[];\n  peerDependencies: string[];\n  scripts: { [scriptName: string]: string };\n  isPrivate: boolean;\n}\n\nexport interface SharedContext {\n  id: string;\n  name: string;\n  description: string;\n  repos: string[];\n  sharedFiles: string[];\n  sharedTypes: string[];\n  sharedConstants: string[];\n  communicationProtocols: string[];\n  lastUpdated: number;\n  version: string;\n}\n\nexport class CrossRepositoryContext {\n  private supervisorAgent: SupervisorAgent;\n  private toolRegistry: ToolRegistry;\n  private quantumMemory: QuantumMemorySystem;\n  private projectIntelligence: ProjectWideIntelligence;\n\n  // Repository tracking\n  private repositories: Map<string, RepositoryInfo> = new Map();\n  private relationships: Map<string, CrossRepoRelationship[]> = new Map();\n  private sharedContexts: Map<string, SharedContext> = new Map();\n  private monorepoStructures: Map<string, MonorepoStructure> = new Map();\n\n  // Analysis cache\n  private analysisCache: Map<string, { result: any; timestamp: number }> = new Map();\n  private readonly cacheExpiry = 1800000; // 30 minutes\n\n  constructor(\n    supervisorAgent: SupervisorAgent,\n    toolRegistry: ToolRegistry,\n    quantumMemory: QuantumMemorySystem,\n    projectIntelligence: ProjectWideIntelligence\n  ) {\n    this.supervisorAgent = supervisorAgent;\n    this.toolRegistry = toolRegistry;\n    this.quantumMemory = quantumMemory;\n    this.projectIntelligence = projectIntelligence;\n\n    this.setupEventListeners();\n  }\n\n  /**\n     * Initialize cross-repository context system\n     */\n  public async initialize(): Promise<void> {\n    try {\n      logger.info('Initializing Cross-Repository Context System...');\n\n      // Discover repositories\n      await this.discoverRepositories();\n\n      // Analyze relationships\n      await this.analyzeRepositoryRelationships();\n\n      // Detect monorepos\n      await this.detectMonorepoStructures();\n\n      // Build shared contexts\n      await this.buildSharedContexts();\n\n      logger.info('Cross-Repository Context System initialized successfully');\n    } catch (error) {\n      logger.error(`Failed to initialize Cross-Repository Context System: ${error}`);\n      throw error;\n    }\n  }\n\n  /**\n     * Discover all repositories in workspace and related locations\n     */\n  public async discoverRepositories(): Promise<RepositoryInfo[]> {\n    const repositories: RepositoryInfo[] = [];\n\n    // Check current workspace folders\n    if (vscode.workspace.workspaceFolders) {\n      for (const folder of vscode.workspace.workspaceFolders) {\n        const repoInfo = await this.analyzeRepository(folder.uri.fsPath);\n        if (repoInfo) {\n          repositories.push(repoInfo);\n          this.repositories.set(repoInfo.id, repoInfo);\n        }\n      }\n    }\n\n    // Look for related repositories\n    for (const repo of repositories) {\n      const relatedRepos = await this.findRelatedRepositories(repo);\n      for (const related of relatedRepos) {\n        if (!this.repositories.has(related.id)) {\n          repositories.push(related);\n          this.repositories.set(related.id, related);\n        }\n      }\n    }\n\n    logger.info(`Discovered ${repositories.length} repositories`);\n    return repositories;\n  }\n\n  /**\n     * Analyze relationships between repositories\n     */\n  public async analyzeRepositoryRelationships(): Promise<CrossRepoRelationship[]> {\n    const relationships: CrossRepoRelationship[] = [];\n    const repos = Array.from(this.repositories.values());\n\n    for (let i = 0; i < repos.length; i++) {\n      for (let j = i + 1; j < repos.length; j++) {\n        const relationship = await this.analyzeRepoRelationship(repos[i], repos[j]);\n        if (relationship) {\n          relationships.push(relationship);\n\n          // Store bidirectional relationships\n          if (!this.relationships.has(repos[i].id)) {\n            this.relationships.set(repos[i].id, []);\n          }\n          if (!this.relationships.has(repos[j].id)) {\n            this.relationships.set(repos[j].id, []);\n          }\n\n          this.relationships.get(repos[i].id)!.push(relationship);\n          this.relationships.get(repos[j].id)!.push({\n            ...relationship,\n            sourceRepo: repos[j].id,\n            targetRepo: repos[i].id\n          });\n        }\n      }\n    }\n\n    logger.info(`Analyzed ${relationships.length} repository relationships`);\n    return relationships;\n  }\n\n  /**\n     * Plan cross-repository refactoring\n     */\n  public async planCrossRepoRefactoring(\n    description: string,\n    affectedRepos: string[],\n    refactoringType: 'rename' | 'move' | 'extract' | 'api-change' | 'dependency-update'\n  ): Promise<CrossRepoRefactoring> {\n    logger.info(`Planning cross-repo refactoring: ${description}`);\n\n    const refactoringId = `refactor_${Date.now()}`;\n    const changes: CrossRepoRefactoring['changes'] = [];\n\n    // Analyze impact on each repository\n    for (const repoId of affectedRepos) {\n      const repo = this.repositories.get(repoId);\n      if (!repo) continue;\n\n      const repoChanges = await this.analyzeRefactoringImpact(repo, description, refactoringType);\n      changes.push({\n        repo: repoId,\n        files: repoChanges.files,\n        operations: repoChanges.operations\n      });\n    }\n\n    // Analyze dependencies between changes\n    const dependencies = await this.analyzeRefactoringDependencies(changes);\n\n    // Assess risk and impact\n    const { estimatedImpact, riskLevel } = this.assessRefactoringRisk(changes);\n\n    // Generate rollback plan\n    const rollbackPlan = await this.generateRollbackPlan(changes);\n\n    const refactoring: CrossRepoRefactoring = {\n      id: refactoringId,\n      description,\n      affectedRepos,\n      changes,\n      dependencies,\n      estimatedImpact,\n      riskLevel,\n      rollbackPlan\n    };\n\n    // Store in quantum memory\n    await this.quantumMemory.storeTemporalMemory(\n      `Cross-repo refactoring planned: ${description}`,\n      {\n        filePath: 'cross-repo-refactoring',\n        projectContext: 'multi-repo'\n      },\n      'create',\n      { tags: ['cross-repo', 'refactoring', 'planning'] }\n    );\n\n    return refactoring;\n  }\n\n  /**\n     * Execute cross-repository refactoring\n     */\n  public async executeCrossRepoRefactoring(refactoring: CrossRepoRefactoring): Promise<boolean> {\n    logger.info(`Executing cross-repo refactoring: ${refactoring.id}`);\n\n    try {\n      // Execute changes in dependency order\n      const sortedChanges = this.sortChangesByDependencies(refactoring.changes, refactoring.dependencies);\n\n      for (const change of sortedChanges) {\n        const repo = this.repositories.get(change.repo);\n        if (!repo) continue;\n\n        logger.info(`Applying changes to repository: ${repo.name}`);\n\n        // Use existing refactoring tools\n        const refactorTool = this.toolRegistry.getTool('refactor');\n        if (refactorTool) {\n          for (const operation of change.operations) {\n            const result = await refactorTool.execute('applyRefactoring', {\n              file: operation.file,\n              type: operation.type,\n              oldValue: operation.oldValue,\n              newValue: operation.newValue,\n              line: operation.line,\n              column: operation.column\n            });\n\n            if (!result.success) {\n              logger.error(`Refactoring operation failed: ${result.error}`);\n              // Implement rollback logic here\n              return false;\n            }\n          }\n        }\n      }\n\n      // Update quantum memory\n      await this.quantumMemory.storeTemporalMemory(\n        `Cross-repo refactoring executed: ${refactoring.description}`,\n        {\n          filePath: 'cross-repo-refactoring',\n          projectContext: 'multi-repo'\n        },\n        'update',\n        { tags: ['cross-repo', 'refactoring', 'executed'] }\n      );\n\n      logger.info('Cross-repo refactoring completed successfully');\n      return true;\n\n    } catch (error) {\n      logger.error(`Cross-repo refactoring failed: ${error}`);\n      return false;\n    }\n  }\n\n  /**\n     * Detect and analyze monorepo structures\n     */\n  public async detectMonorepoStructures(): Promise<MonorepoStructure[]> {\n    const monorepos: MonorepoStructure[] = [];\n\n    for (const repo of this.repositories.values()) {\n      if (repo.isMonorepo) {\n        const structure = await this.analyzeMonorepoStructure(repo.path);\n        if (structure) {\n          monorepos.push(structure);\n          this.monorepoStructures.set(repo.id, structure);\n        }\n      }\n    }\n\n    logger.info(`Detected ${monorepos.length} monorepo structures`);\n    return monorepos;\n  }\n\n  /**\n     * Build shared contexts across repositories\n     */\n  public async buildSharedContexts(): Promise<SharedContext[]> {\n    const contexts: SharedContext[] = [];\n\n    // Analyze shared dependencies\n    const sharedDeps = this.findSharedDependencies();\n\n    // Analyze shared file patterns\n    const sharedFiles = await this.findSharedFilePatterns();\n\n    // Create shared contexts\n    for (const [depName, repos] of sharedDeps) {\n      if (repos.length > 1) {\n        const context: SharedContext = {\n          id: `shared_${depName}_${Date.now()}`,\n          name: `Shared Dependency: ${depName}`,\n          description: `Shared dependency ${depName} used across ${repos.length} repositories`,\n          repos,\n          sharedFiles: sharedFiles.get(depName) || [],\n          sharedTypes: [],\n          sharedConstants: [],\n          communicationProtocols: [],\n          lastUpdated: Date.now(),\n          version: '1.0.0'\n        };\n\n        contexts.push(context);\n        this.sharedContexts.set(context.id, context);\n      }\n    }\n\n    logger.info(`Built ${contexts.length} shared contexts`);\n    return contexts;\n  }\n\n  /**\n     * Get cross-repository insights\n     */\n  public async getCrossRepoInsights(): Promise<any> {\n    const insights = {\n      totalRepositories: this.repositories.size,\n      relationships: Array.from(this.relationships.values()).flat().length,\n      sharedContexts: this.sharedContexts.size,\n      monorepos: this.monorepoStructures.size,\n      topSharedDependencies: this.getTopSharedDependencies(),\n      strongestRelationships: this.getStrongestRelationships(),\n      refactoringOpportunities: await this.identifyRefactoringOpportunities()\n    };\n\n    return insights;\n  }\n\n  // Private helper methods\n  private async analyzeRepository(repoPath: string): Promise<RepositoryInfo | null> {\n    try {\n      // Check if it's a git repository\n      const gitPath = path.join(repoPath, '.git');\n      if (!fs.existsSync(gitPath)) {\n        return null;\n      }\n\n      // Get repository information\n      const gitTool = this.toolRegistry.getTool('git');\n      let remoteUrl = '';\n      let branch = 'main';\n      let lastCommit = '';\n\n      if (gitTool) {\n        const remoteResult = await gitTool.execute('getRemoteUrl', { path: repoPath });\n        if (remoteResult.success) {\n          remoteUrl = remoteResult.output;\n        }\n\n        const branchResult = await gitTool.execute('getCurrentBranch', { path: repoPath });\n        if (branchResult.success) {\n          branch = branchResult.output;\n        }\n\n        const commitResult = await gitTool.execute('getLastCommit', { path: repoPath });\n        if (commitResult.success) {\n          lastCommit = commitResult.output;\n        }\n      }\n\n      // Analyze project structure\n      const projectReport = await this.projectIntelligence.generateProjectReport(repoPath);\n\n      // Detect if it's a monorepo\n      const isMonorepo = await this.detectIfMonorepo(repoPath);\n\n      const repoInfo: RepositoryInfo = {\n        id: `repo_${path.basename(repoPath)}_${Date.now()}`,\n        name: path.basename(repoPath),\n        path: repoPath,\n        remoteUrl,\n        branch,\n        lastCommit,\n        language: this.detectPrimaryLanguage(projectReport.codebaseMap.languages),\n        dependencies: projectReport.codebaseMap.dependencies.map(d => d.name),\n        isMonorepo,\n        relatedRepos: [],\n        sharedDependencies: []\n      };\n\n      return repoInfo;\n\n    } catch (error) {\n      logger.warn(`Failed to analyze repository ${repoPath}: ${error}`);\n      return null;\n    }\n  }\n\n  private async findRelatedRepositories(repo: RepositoryInfo): Promise<RepositoryInfo[]> {\n    const related: RepositoryInfo[] = [];\n\n    // Look for repositories in parent directory\n    const parentDir = path.dirname(repo.path);\n    try {\n      const siblings = await fs.promises.readdir(parentDir, { withFileTypes: true });\n\n      for (const sibling of siblings) {\n        if (sibling.isDirectory() && sibling.name !== repo.name) {\n          const siblingPath = path.join(parentDir, sibling.name);\n          const siblingRepo = await this.analyzeRepository(siblingPath);\n          if (siblingRepo) {\n            related.push(siblingRepo);\n          }\n        }\n      }\n    } catch (error) {\n      logger.warn(`Failed to find related repositories: ${error}`);\n    }\n\n    return related;\n  }\n\n  private async analyzeRepoRelationship(repo1: RepositoryInfo, repo2: RepositoryInfo): Promise<CrossRepoRelationship | null> {\n    // Analyze shared dependencies\n    const sharedDeps = repo1.dependencies.filter(dep => repo2.dependencies.includes(dep));\n\n    // Calculate relationship strength\n    let strength = 0;\n    strength += sharedDeps.length * 0.1;\n\n    // Check for shared file patterns\n    const sharedFiles = await this.findSharedFiles(repo1.path, repo2.path);\n    strength += sharedFiles.length * 0.2;\n\n    // Check if they're in the same parent directory (likely related projects)\n    if (path.dirname(repo1.path) === path.dirname(repo2.path)) {\n      strength += 0.3;\n    }\n\n    if (strength < 0.1) {\n      return null; // Not related enough\n    }\n\n    // Determine relationship type\n    let relationshipType: CrossRepoRelationship['relationshipType'] = 'dependency';\n    if (repo1.isMonorepo || repo2.isMonorepo) {\n      relationshipType = 'monorepo-package';\n    } else if (sharedDeps.length > 5) {\n      relationshipType = 'shared-library';\n    }\n\n    return {\n      sourceRepo: repo1.id,\n      targetRepo: repo2.id,\n      relationshipType,\n      strength: Math.min(1, strength),\n      description: `Shared ${sharedDeps.length} dependencies and ${sharedFiles.length} file patterns`,\n      sharedFiles,\n      sharedDependencies: sharedDeps,\n      communicationPatterns: []\n    };\n  }\n\n  private async findSharedFiles(repo1Path: string, repo2Path: string): Promise<string[]> {\n    // Simple implementation - in reality, this would be more sophisticated\n    const sharedFiles: string[] = [];\n\n    try {\n      const files1 = await this.getFileList(repo1Path);\n      const files2 = await this.getFileList(repo2Path);\n\n      // Find files with same relative paths\n      for (const file1 of files1) {\n        const relativePath = path.relative(repo1Path, file1);\n        const correspondingFile = path.join(repo2Path, relativePath);\n\n        if (files2.includes(correspondingFile)) {\n          sharedFiles.push(relativePath);\n        }\n      }\n    } catch (error) {\n      logger.warn(`Failed to find shared files: ${error}`);\n    }\n\n    return sharedFiles;\n  }\n\n  private async getFileList(dirPath: string): Promise<string[]> {\n    const files: string[] = [];\n\n    try {\n      const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });\n\n      for (const entry of entries) {\n        const fullPath = path.join(dirPath, entry.name);\n\n        if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {\n          const subFiles = await this.getFileList(fullPath);\n          files.push(...subFiles);\n        } else if (entry.isFile()) {\n          files.push(fullPath);\n        }\n      }\n    } catch (error) {\n      logger.warn('Error accessing directory:', error);\n    }\n\n    return files;\n  }\n\n  private detectPrimaryLanguage(languages: { [language: string]: number }): string {\n    let maxCount = 0;\n    let primaryLanguage = 'unknown';\n\n    for (const [language, count] of Object.entries(languages)) {\n      if (count > maxCount) {\n        maxCount = count;\n        primaryLanguage = language;\n      }\n    }\n\n    return primaryLanguage;\n  }\n\n  private async detectIfMonorepo(repoPath: string): Promise<boolean> {\n    // Check for common monorepo indicators\n    const monorepoIndicators = [\n      'lerna.json',\n      'nx.json',\n      'rush.json',\n      'packages',\n      'apps',\n      'libs'\n    ];\n\n    for (const indicator of monorepoIndicators) {\n      const indicatorPath = path.join(repoPath, indicator);\n      if (fs.existsSync(indicatorPath)) {\n        return true;\n      }\n    }\n\n    // Check package.json for workspaces\n    const packageJsonPath = path.join(repoPath, 'package.json');\n    if (fs.existsSync(packageJsonPath)) {\n      try {\n        const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf8'));\n        if (packageJson.workspaces) {\n          return true;\n        }\n      } catch (error) {\n        logger.debug('Failed to parse package.json:', error);\n      }\n    }\n\n    return false;\n  }\n\n  private async analyzeMonorepoStructure(repoPath: string): Promise<MonorepoStructure | null> {\n    try {\n      const structure: MonorepoStructure = {\n        rootPath: repoPath,\n        packages: [],\n        sharedDependencies: [],\n        buildSystem: 'custom',\n        workspaceConfig: {},\n        dependencyGraph: {}\n      };\n\n      // Detect build system\n      if (fs.existsSync(path.join(repoPath, 'lerna.json'))) {\n        structure.buildSystem = 'lerna';\n      } else if (fs.existsSync(path.join(repoPath, 'nx.json'))) {\n        structure.buildSystem = 'nx';\n      } else if (fs.existsSync(path.join(repoPath, 'rush.json'))) {\n        structure.buildSystem = 'rush';\n      }\n\n      // Find packages\n      const packagesDir = path.join(repoPath, 'packages');\n      if (fs.existsSync(packagesDir)) {\n        const packageDirs = await fs.promises.readdir(packagesDir, { withFileTypes: true });\n\n        for (const dir of packageDirs) {\n          if (dir.isDirectory()) {\n            const packagePath = path.join(packagesDir, dir.name);\n            const packageInfo = await this.analyzeMonorepoPackage(packagePath);\n            if (packageInfo) {\n              structure.packages.push(packageInfo);\n            }\n          }\n        }\n      }\n\n      return structure;\n\n    } catch (error) {\n      logger.warn(`Failed to analyze monorepo structure: ${error}`);\n      return null;\n    }\n  }\n\n  private async analyzeMonorepoPackage(packagePath: string): Promise<MonorepoPackage | null> {\n    const packageJsonPath = path.join(packagePath, 'package.json');\n\n    if (!fs.existsSync(packageJsonPath)) {\n      return null;\n    }\n\n    try {\n      const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf8'));\n\n      return {\n        name: packageJson.name || path.basename(packagePath),\n        path: packagePath,\n        version: packageJson.version || '0.0.0',\n        dependencies: Object.keys(packageJson.dependencies || {}),\n        devDependencies: Object.keys(packageJson.devDependencies || {}),\n        peerDependencies: Object.keys(packageJson.peerDependencies || {}),\n        scripts: packageJson.scripts || {},\n        isPrivate: packageJson.private || false\n      };\n    } catch (error) {\n      logger.warn(`Failed to analyze monorepo package ${packagePath}: ${error}`);\n      return null;\n    }\n  }\n\n  private findSharedDependencies(): Map<string, string[]> {\n    const sharedDeps = new Map<string, string[]>();\n\n    for (const repo of this.repositories.values()) {\n      for (const dep of repo.dependencies) {\n        if (!sharedDeps.has(dep)) {\n          sharedDeps.set(dep, []);\n        }\n        sharedDeps.get(dep)!.push(repo.id);\n      }\n    }\n\n    return sharedDeps;\n  }\n\n  private async findSharedFilePatterns(): Promise<Map<string, string[]>> {\n    const sharedFiles = new Map<string, string[]>();\n\n    // This would analyze file patterns across repositories\n    // Simplified implementation for now\n\n    return sharedFiles;\n  }\n\n  private getTopSharedDependencies(): Array<{ name: string; repoCount: number }> {\n    const sharedDeps = this.findSharedDependencies();\n\n    return Array.from(sharedDeps.entries())\n      .map(([name, repos]) => ({ name, repoCount: repos.length }))\n      .filter(dep => dep.repoCount > 1)\n      .sort((a, b) => b.repoCount - a.repoCount)\n      .slice(0, 10);\n  }\n\n  private getStrongestRelationships(): CrossRepoRelationship[] {\n    const allRelationships = Array.from(this.relationships.values()).flat();\n\n    return allRelationships\n      .sort((a, b) => b.strength - a.strength)\n      .slice(0, 5);\n  }\n\n  private async identifyRefactoringOpportunities(): Promise<string[]> {\n    const opportunities: string[] = [];\n\n    // Identify duplicate code across repositories\n    const sharedDeps = this.findSharedDependencies();\n    for (const [dep, repos] of sharedDeps) {\n      if (repos.length > 2) {\n        opportunities.push(`Extract shared library for ${dep} used in ${repos.length} repositories`);\n      }\n    }\n\n    // Identify outdated dependencies\n    for (const repo of this.repositories.values()) {\n      // This would check for outdated dependencies\n      opportunities.push(`Update dependencies in ${repo.name}`);\n    }\n\n    return opportunities.slice(0, 5);\n  }\n\n  private async analyzeRefactoringImpact(\n    repo: RepositoryInfo,\n    description: string,\n    refactoringType: string\n  ): Promise<{ files: string[]; operations: RefactoringOperation[] }> {\n    logger.info(`Analyzing refactoring impact for ${repo.name}: ${description} (${refactoringType})`);\n\n    const files: string[] = [];\n    const operations: RefactoringOperation[] = [];\n\n    try {\n      // Use AI to analyze refactoring impact\n      const prompt = `\n            Analyze the refactoring impact for repository: ${repo.name}\n            Refactoring description: ${description}\n            Refactoring type: ${refactoringType}\n            Repository language: ${repo.language}\n            Dependencies: ${repo.dependencies.slice(0, 10).join(', ')}\n\n            Identify:\n            1. Files that need to be modified\n            2. Specific operations required\n            3. Potential breaking changes\n\n            Provide response in JSON format:\n            {\n              \"files\": [\"file1.js\", \"file2.ts\"],\n              \"operations\": [\n                {\n                  \"type\": \"rename|move|extract|inline|update-dependency|update-api\",\n                  \"file\": \"path/to/file\",\n                  \"oldValue\": \"old value\",\n                  \"newValue\": \"new value\",\n                  \"line\": 10\n                }\n              ]\n            }\n            `;\n\n      const result = await this.supervisorAgent.run({\n        prompt,\n        mode: 'ask'\n      });\n\n      if (result.success && result.output) {\n        const parsed = this.parseRefactoringAnalysis(result.output);\n        files.push(...parsed.files);\n        operations.push(...parsed.operations);\n      }\n    } catch (error) {\n      logger.warn(`Failed to analyze refactoring impact: ${error}`);\n    }\n\n    return { files, operations };\n  }\n\n  private async analyzeRefactoringDependencies(changes: CrossRepoRefactoring['changes']): Promise<string[]> {\n    const dependencies: string[] = [];\n\n    // Analyze dependencies between changes across repositories\n    for (let i = 0; i < changes.length; i++) {\n      for (let j = i + 1; j < changes.length; j++) {\n        const change1 = changes[i];\n        const change2 = changes[j];\n\n        // Check if change1 affects files that change2 depends on\n        const repo1 = this.repositories.get(change1.repo);\n        const repo2 = this.repositories.get(change2.repo);\n\n        if (repo1 && repo2) {\n          // Check for shared dependencies\n          const sharedDeps = repo1.dependencies.filter(dep => repo2.dependencies.includes(dep));\n          if (sharedDeps.length > 0) {\n            dependencies.push(`${change1.repo} -> ${change2.repo}: shared dependencies ${sharedDeps.join(', ')}`);\n          }\n        }\n      }\n    }\n\n    return dependencies;\n  }\n\n  private assessRefactoringRisk(changes: CrossRepoRefactoring['changes']): { estimatedImpact: 'low' | 'medium' | 'high'; riskLevel: 'low' | 'medium' | 'high' } {\n    const totalFiles = changes.reduce((sum, change) => sum + change.files.length, 0);\n    const totalOperations = changes.reduce((sum, change) => sum + change.operations.length, 0);\n\n    let estimatedImpact: 'low' | 'medium' | 'high' = 'low';\n    let riskLevel: 'low' | 'medium' | 'high' = 'low';\n\n    if (totalFiles > 20 || totalOperations > 50) {\n      estimatedImpact = 'high';\n      riskLevel = 'high';\n    } else if (totalFiles > 10 || totalOperations > 20) {\n      estimatedImpact = 'medium';\n      riskLevel = 'medium';\n    }\n\n    return { estimatedImpact, riskLevel };\n  }\n\n  private async generateRollbackPlan(changes: CrossRepoRefactoring['changes']): Promise<string> {\n    const steps: string[] = [];\n\n    // Generate detailed rollback plan based on changes\n    for (const change of changes) {\n      const repo = this.repositories.get(change.repo);\n      if (repo) {\n        steps.push(`1. Revert changes in ${repo.name}:`);\n        for (const file of change.files) {\n          steps.push(`   - git checkout HEAD -- ${file}`);\n        }\n        steps.push('   - git reset --hard HEAD~1 (if committed)');\n      }\n    }\n\n    steps.push('2. Verify all repositories are in working state');\n    steps.push('3. Run tests to ensure functionality is restored');\n\n    return steps.join('\\n');\n  }\n\n  private sortChangesByDependencies(changes: CrossRepoRefactoring['changes'], dependencies: string[]): CrossRepoRefactoring['changes'] {\n    // Sort changes based on dependencies to ensure proper execution order\n    const sorted = [...changes];\n\n    // Simple topological sort based on dependency strings\n    for (const dep of dependencies) {\n      const [source, target] = dep.split(' -> ');\n      const sourceIndex = sorted.findIndex(c => c.repo === source);\n      const targetIndex = sorted.findIndex(c => c.repo === target);\n\n      if (sourceIndex > targetIndex && sourceIndex !== -1 && targetIndex !== -1) {\n        // Move source before target\n        const sourceChange = sorted.splice(sourceIndex, 1)[0];\n        sorted.splice(targetIndex, 0, sourceChange);\n      }\n    }\n\n    return sorted;\n  }\n\n  private parseRefactoringAnalysis(response: string): { files: string[]; operations: RefactoringOperation[] } {\n    try {\n      const jsonMatch = response.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const parsed = JSON.parse(jsonMatch[0]);\n        return {\n          files: parsed.files || [],\n          operations: parsed.operations || []\n        };\n      }\n    } catch (error) {\n      logger.warn(`Failed to parse refactoring analysis: ${error}`);\n    }\n\n    return { files: [], operations: [] };\n  }\n\n  private setupEventListeners(): void {\n    // Listen for workspace changes\n    vscode.workspace.onDidChangeWorkspaceFolders(() => {\n      this.discoverRepositories();\n    });\n  }\n\n  /**\n     * Dispose cross-repository context system\n     */\n  public dispose(): void {\n    this.repositories.clear();\n    this.relationships.clear();\n    this.sharedContexts.clear();\n    this.monorepoStructures.clear();\n    this.analysisCache.clear();\n    logger.info('Cross-Repository Context disposed');\n  }\n}"]}