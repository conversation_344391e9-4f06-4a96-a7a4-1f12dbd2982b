{"version": 3, "file": "memoryTools.js", "sourceRoot": "", "sources": ["../../src/tools/memoryTools.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAqNH,8DAEC;AAKD,oDAEC;AAKD,kEAEC;AAnOD,mEAAkE;AAClE,aAAa;AACb,6BAAwB;AACxB,sCAAmC;AACnC,2DAAwD;AACxD,yDAA6D;AAG7D;;GAEG;AACH,MAAa,mBAAoB,SAAQ,6BAAc;IACrD,IAAI,GAAG,kBAAkB,CAAC;IAC1B,WAAW,GAAG,+DAA+D,CAAC;IAC9E,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;QAC5E,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;QAC/E,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;KAChF,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK,CAAC,KAAU;QACpB,IAAI,CAAC;YACH,cAAc;YACd,MAAM,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACpE,MAAM,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,MAAM,SAAS,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;YAEvF,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,wCAAwC,CAAC;YAClD,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,KAAK,aAAa,KAAK,gBAAgB,SAAS,GAAG,CAAC,CAAC;YAE7G,sBAAsB;YACtB,MAAM,aAAa,GAAwB;gBACzC,KAAK;gBACL,KAAK;gBACL,SAAS;aACV,CAAC;YAEF,yCAAyC;YACzC,IAAI,QAAQ,GAAkB,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,IAAI,kCAAmB,EAAE,CAAC;oBACxB,QAAQ,GAAG,MAAM,kCAAmB,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;YAED,gDAAgD;YAChD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC;oBACH,QAAQ,GAAG,MAAM,6BAAa,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;gBAC/D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,4BAA4B,CAAC;YACtC,CAAC;YAED,kBAAkB;YAClB,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC/E,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,CAAC;gBAC9D,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC;gBACpD,OAAO,UAAU,KAAK,GAAG,CAAC,KAAK,SAAS,cAAc,MAAM,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC;YACtF,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAE1C,OAAO,aAAa,QAAQ,CAAC,MAAM,0BAA0B,iBAAiB,EAAE,CAAC;QACnF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAChG,CAAC;IACH,CAAC;CACF;AAjED,kDAiEC;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,6BAAc;IAChD,IAAI,GAAG,aAAa,CAAC;IACrB,WAAW,GAAG,6DAA6D,CAAC;IAC5E,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+BAA+B,CAAC;QACtE,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,EAAE,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;KAClG,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK,CAAC,KAAU;QACpB,IAAI,CAAC;YACH,cAAc;YACd,MAAM,OAAO,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;YACxE,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YAEnF,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,qCAAqC,CAAC;YAC/C,CAAC;YAED,uBAAuB;YACvB,MAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,cAAuB;gBAC/B,IAAI,EAAE,MAAe;gBACrB,GAAG,QAAQ;aACZ,CAAC;YAEF,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAExG,sBAAsB;YACtB,MAAM,WAAW,GAAyB;gBACxC,OAAO;gBACP,QAAQ,EAAE,YAAY;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,yBAAyB;YACzB,IAAI,CAAC;gBACH,MAAM,6BAAa,CAAC,SAAS,CAAC;oBAC5B,OAAO;oBACP,QAAQ,EAAE,YAAY;iBACvB,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACpD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBACrD,MAAM,KAAK,CAAC;YACd,CAAC;YAED,OAAO,2BAA2B,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1F,CAAC;IACH,CAAC;CACF;AApDD,wCAoDC;AAED;;GAEG;AACH,MAAa,qBAAsB,SAAQ,6BAAc;IACvD,IAAI,GAAG,oBAAoB,CAAC;IAC5B,WAAW,GAAG,+CAA+C,CAAC;IAC9D,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;QAChB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,sCAAsC,CAAC;QAC7E,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;QAChF,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;KAC1E,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK,CAAC,KAAU;QACpB,IAAI,CAAC;YACH,cAAc;YACd,MAAM,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACpE,MAAM,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,MAAM,UAAU,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC;YAElG,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,0CAA0C,CAAC;YACpD,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,KAAK,aAAa,KAAK,iBAAiB,UAAU,GAAG,CAAC,CAAC;YAEhH,uBAAuB;YACvB,MAAM,aAAa,GAAwB;gBACzC,KAAK;gBACL,KAAK;gBACL,MAAM,EAAE;oBACN,IAAI,EAAE,UAAmB;oBACzB,UAAU;iBACX;aACF,CAAC;YAEF,yCAAyC;YACzC,IAAI,SAAS,GAAkB,EAAE,CAAC;YAClC,IAAI,CAAC;gBACH,IAAI,kCAAmB,EAAE,CAAC;oBACxB,SAAS,GAAG,MAAM,kCAAmB,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACpE,CAAC;YAED,gDAAgD;YAChD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,SAAS,GAAG,MAAM,6BAAa,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;gBAChE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBAC7D,CAAC;YACH,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,OAAO,6BAA6B,CAAC;YACvC,CAAC;YAED,mBAAmB;YACnB,MAAM,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACtD,MAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC;gBACjD,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,EAAE,KAAK,IAAI,YAAY,KAAK,GAAG,CAAC,EAAE,CAAC;gBAC7D,OAAO,aAAa,KAAK,aAAa,MAAM,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;YAClE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEhB,OAAO,aAAa,SAAS,CAAC,MAAM,2BAA2B,kBAAkB,EAAE,CAAC;QACtF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QACjG,CAAC;IACH,CAAC;CACF;AApED,sDAoEC;AAED;;GAEG;AACH,SAAgB,yBAAyB;IACvC,OAAO,IAAI,mBAAmB,EAAE,CAAC;AACnC,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB;IAClC,OAAO,IAAI,cAAc,EAAE,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,SAAgB,2BAA2B;IACzC,OAAO,IAAI,qBAAqB,EAAE,CAAC;AACrC,CAAC", "sourcesContent": ["/**\n * Memory Tools\n *\n * This module provides tools for interacting with memory systems.\n */\n\nimport { StructuredTool } from '../agents/workflows/corePolyfill';\n// @ts-ignore\nimport { z } from 'zod';\nimport { Logger } from '../logger';\nimport { memoryManager } from '../memory/memoryManager';\nimport { vectorMemoryManager } from '../memory/vectorMemory';\nimport { MemoryEntry, MemorySearchOptions } from '../memory/types';\n\n/**\n * Memory retrieval tool\n */\nexport class MemoryRetrievalTool extends StructuredTool {\n  name = 'memory-retrieval';\n  description = 'Retrieves relevant memories based on the current conversation';\n  schema = z.object({\n    input: z.string().optional().describe('The query to search for in memories'),\n    limit: z.number().optional().describe('Maximum number of memories to retrieve'),\n    threshold: z.number().optional().describe('Similarity threshold for retrieval')\n  });\n\n  async _call(input: any): Promise<string> {\n    try {\n      // Parse input\n      const query = typeof input === 'string' ? input : input.query || '';\n      const limit = typeof input === 'object' && input.limit ? input.limit : 5;\n      const threshold = typeof input === 'object' && input.threshold ? input.threshold : 0.7;\n\n      if (!query) {\n        return 'No query provided for memory retrieval';\n      }\n\n      Logger.instance.info(`Retrieving memories for query: \"${query}\" (limit: ${limit}, threshold: ${threshold})`);\n\n      // Search for memories\n      const searchOptions: MemorySearchOptions = {\n        query,\n        limit,\n        threshold\n      };\n\n      // Try to use vector memory manager first\n      let memories: MemoryEntry[] = [];\n      try {\n        if (vectorMemoryManager) {\n          memories = await vectorMemoryManager.searchSimilarMemories(query, limit);\n        }\n      } catch (error) {\n        Logger.instance.error('Error searching vector memories:', error);\n      }\n\n      // Fall back to regular memory manager if needed\n      if (memories.length === 0) {\n        try {\n          memories = await memoryManager.searchMemories(searchOptions);\n        } catch (error) {\n          Logger.instance.error('Error searching memories:', error);\n        }\n      }\n\n      if (memories.length === 0) {\n        return 'No relevant memories found';\n      }\n\n      // Format memories\n      const formattedMemories = await Promise.all(memories.map(async (memory, index) => {\n        const timestamp = new Date(memory.timestamp).toLocaleString();\n        const source = memory.metadata?.source || 'unknown';\n        return `Memory ${index + 1} [${timestamp}] (Source: ${source}):\\n${memory.content}`;\n      })).then(results => results.join('\\n\\n'));\n\n      return `Retrieved ${memories.length} relevant memories:\\n\\n${formattedMemories}`;\n    } catch (error) {\n      Logger.instance.error('Error in memory retrieval tool:', error);\n      return `Error retrieving memories: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n}\n\n/**\n * Memory save tool\n */\nexport class MemorySaveTool extends StructuredTool {\n  name = 'memory-save';\n  description = 'Saves important information from the conversation to memory';\n  schema = z.object({\n    input: z.string().optional().describe('The content to save to memory'),\n    metadata: z.record(z.string(), z.any()).optional().describe('Additional metadata for the memory')\n  });\n\n  async _call(input: any): Promise<string> {\n    try {\n      // Parse input\n      const content = typeof input === 'string' ? input : input.content || '';\n      const metadata = typeof input === 'object' && input.metadata ? input.metadata : {};\n\n      if (!content) {\n        return 'No content provided for memory save';\n      }\n\n      // Add default metadata\n      const fullMetadata = {\n        source: 'conversation' as const,\n        type: 'text' as const,\n        ...metadata\n      };\n\n      Logger.instance.info(`Saving memory: \"${content.substring(0, 50)}${content.length > 50 ? '...' : ''}\"`);\n\n      // Create memory entry\n      const memoryEntry: Partial<MemoryEntry> = {\n        content,\n        metadata: fullMetadata,\n        timestamp: Date.now()\n      };\n\n      // Save to memory manager\n      try {\n        await memoryManager.addMemory({\n          content,\n          metadata: fullMetadata\n        });\n        Logger.instance.info('Memory saved successfully');\n      } catch (error) {\n        Logger.instance.error('Error saving memory:', error);\n        throw error;\n      }\n\n      return 'Memory saved successfully';\n    } catch (error) {\n      Logger.instance.error('Error in memory save tool:', error);\n      return `Error saving memory: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n}\n\n/**\n * Document retrieval tool\n */\nexport class DocumentRetrievalTool extends StructuredTool {\n  name = 'document-retrieval';\n  description = 'Retrieves relevant documents based on a query';\n  schema = z.object({\n    input: z.string().optional().describe('The query to search for in documents'),\n    limit: z.number().optional().describe('Maximum number of documents to retrieve'),\n    collection: z.string().optional().describe('The collection to search in')\n  });\n\n  async _call(input: any): Promise<string> {\n    try {\n      // Parse input\n      const query = typeof input === 'string' ? input : input.query || '';\n      const limit = typeof input === 'object' && input.limit ? input.limit : 5;\n      const collection = typeof input === 'object' && input.collection ? input.collection : 'documents';\n\n      if (!query) {\n        return 'No query provided for document retrieval';\n      }\n\n      Logger.instance.info(`Retrieving documents for query: \"${query}\" (limit: ${limit}, collection: ${collection})`);\n\n      // Search for documents\n      const searchOptions: MemorySearchOptions = {\n        query,\n        limit,\n        filter: {\n          type: 'document' as const,\n          collection\n        }\n      };\n\n      // Try to use vector memory manager first\n      let documents: MemoryEntry[] = [];\n      try {\n        if (vectorMemoryManager) {\n          documents = await vectorMemoryManager.searchSimilarMemories(query, limit);\n        }\n      } catch (error) {\n        Logger.instance.error('Error searching vector documents:', error);\n      }\n\n      // Fall back to regular memory manager if needed\n      if (documents.length === 0) {\n        try {\n          documents = await memoryManager.searchMemories(searchOptions);\n        } catch (error) {\n          Logger.instance.error('Error searching documents:', error);\n        }\n      }\n\n      if (documents.length === 0) {\n        return 'No relevant documents found';\n      }\n\n      // Format documents\n      const formattedDocuments = documents.map((doc, index) => {\n        const source = doc.metadata?.source || 'unknown';\n        const title = doc.metadata?.title || `Document ${index + 1}`;\n        return `Document: ${title} (Source: ${source})\\n${doc.content}`;\n      }).join('\\n\\n');\n\n      return `Retrieved ${documents.length} relevant documents:\\n\\n${formattedDocuments}`;\n    } catch (error) {\n      Logger.instance.error('Error in document retrieval tool:', error);\n      return `Error retrieving documents: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n}\n\n/**\n * Create a memory retrieval tool\n */\nexport function createMemoryRetrievalTool(): StructuredTool {\n  return new MemoryRetrievalTool();\n}\n\n/**\n * Create a memory save tool\n */\nexport function createMemorySaveTool(): StructuredTool {\n  return new MemorySaveTool();\n}\n\n/**\n * Create a document retrieval tool\n */\nexport function createDocumentRetrievalTool(): StructuredTool {\n  return new DocumentRetrievalTool();\n}\n"]}