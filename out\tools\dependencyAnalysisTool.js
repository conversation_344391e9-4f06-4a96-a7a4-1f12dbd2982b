"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DependencyAnalysisTool = void 0;
const vscode = __importStar(require("vscode"));
const cp = __importStar(require("child_process"));
const zod_1 = require("zod");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const util = __importStar(require("util"));
/**
 * Analyzes code dependencies and generates dependency graphs
 */
class DependencyAnalysisTool {
    id = 'dependencyAnalysis';
    name = 'Dependency Analysis';
    description = 'Analyzes code dependencies and generates dependency graphs.';
    type = 'multi-action';
    actions = {
        'analyze': {
            description: 'Analyze dependencies in a file or project',
            schema: zod_1.z.object({
                target: zod_1.z.string().describe('Path to the file or directory to analyze.'),
                depth: zod_1.z.number().optional().describe('Maximum depth for dependency analysis. Default is 3.'),
                includeNodeModules: zod_1.z.boolean().optional().describe('Whether to include node_modules in the analysis. Default is false.'),
                includeExternalDeps: zod_1.z.boolean().optional().describe('Whether to include external dependencies in the analysis. Default is true.')
            }),
            inputSchema: zod_1.z.object({
                target: zod_1.z.string().describe('Path to the file or directory to analyze.'),
                depth: zod_1.z.number().optional().describe('Maximum depth for dependency analysis. Default is 3.'),
                includeNodeModules: zod_1.z.boolean().optional().describe('Whether to include node_modules in the analysis. Default is false.'),
                includeExternalDeps: zod_1.z.boolean().optional().describe('Whether to include external dependencies in the analysis. Default is true.')
            })
        },
        'findCircular': {
            description: 'Find circular dependencies in a project',
            schema: zod_1.z.object({
                target: zod_1.z.string().describe('Path to the directory to analyze.'),
                maxResults: zod_1.z.number().optional().describe('Maximum number of circular dependencies to return. Default is 10.')
            }),
            inputSchema: zod_1.z.object({
                target: zod_1.z.string().describe('Path to the directory to analyze.'),
                maxResults: zod_1.z.number().optional().describe('Maximum number of circular dependencies to return. Default is 10.')
            })
        },
        'visualize': {
            description: 'Generate a visualization of dependencies',
            schema: zod_1.z.object({
                target: zod_1.z.string().describe('Path to the file or directory to visualize.'),
                format: zod_1.z.enum(['text', 'json', 'dot']).optional().describe('Output format. Default is text.'),
                depth: zod_1.z.number().optional().describe('Maximum depth for dependency visualization. Default is 2.')
            }),
            inputSchema: zod_1.z.object({
                target: zod_1.z.string().describe('Path to the file or directory to visualize.'),
                format: zod_1.z.enum(['text', 'json', 'dot']).optional().describe('Output format. Default is text.'),
                depth: zod_1.z.number().optional().describe('Maximum depth for dependency visualization. Default is 2.')
            })
        },
        'impact': {
            description: 'Analyze the impact of changing a file',
            schema: zod_1.z.object({
                filePath: zod_1.z.string().describe('Path to the file to analyze.'),
                depth: zod_1.z.number().optional().describe('Maximum depth for impact analysis. Default is 3.')
            }),
            inputSchema: zod_1.z.object({
                filePath: zod_1.z.string().describe('Path to the file to analyze.'),
                depth: zod_1.z.number().optional().describe('Maximum depth for impact analysis. Default is 3.')
            })
        }
    };
    execPromise = util.promisify(cp.exec);
    async execute(actionName, input, _context) {
        try {
            if (!actionName || !this.actions[actionName]) {
                return {
                    success: false,
                    error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,
                    toolId: this.id,
                    actionName
                };
            }
            switch (actionName) {
                case 'analyze':
                    return this.analyzeDependencies(input, actionName);
                case 'findCircular':
                    return this.findCircularDependencies(input, actionName);
                case 'visualize':
                    return this.visualizeDependencies(input, actionName);
                case 'impact':
                    return this.analyzeImpact(input, actionName);
                default:
                    return {
                        success: false,
                        error: `Unknown action: ${actionName}`,
                        toolId: this.id,
                        actionName
                    };
            }
        }
        catch (error) {
            return {
                success: false,
                error: `Dependency analysis failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async analyzeDependencies(input, actionName) {
        const target = input.target;
        const depth = input.depth || 3;
        const includeNodeModules = input.includeNodeModules || false;
        const includeExternalDeps = input.includeExternalDeps !== false; // Default to true
        if (!target) {
            return {
                success: false,
                error: '\'target\' is required.',
                toolId: this.id,
                actionName
            };
        }
        try {
            // Validate target exists
            const uri = vscode.Uri.file(target);
            // Replace Promise.then().catch() with try/catch for better error handling
            let exists = false;
            try {
                await vscode.workspace.fs.stat(uri);
                exists = true;
            }
            catch {
                exists = false;
            }
            if (!exists) {
                return {
                    success: false,
                    error: `Target not found: ${target}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Determine if target is a file or directory
            const stats = await vscode.workspace.fs.stat(uri);
            const isDirectory = stats.type === vscode.FileType.Directory;
            // Analyze dependencies
            const dependencies = await this.extractDependencies(target, isDirectory, depth, includeNodeModules, includeExternalDeps);
            // Calculate statistics
            const stats2 = this.calculateDependencyStats(dependencies);
            return {
                success: true,
                output: {
                    dependencies,
                    stats: stats2,
                    summary: this.generateDependencySummary(dependencies, stats2)
                },
                toolId: this.id,
                actionName,
                metadata: {
                    target,
                    isDirectory,
                    depth,
                    includeNodeModules,
                    includeExternalDeps,
                    dependencyCount: Object.keys(dependencies).length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Dependency analysis failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async findCircularDependencies(input, actionName) {
        const target = input.target;
        const maxResults = input.maxResults || 10;
        if (!target) {
            return {
                success: false,
                error: '\'target\' is required.',
                toolId: this.id,
                actionName
            };
        }
        try {
            // Validate target exists and is a directory
            const uri = vscode.Uri.file(target);
            // Replace Promise.then().catch() with try/catch for better error handling
            let exists = false;
            try {
                await vscode.workspace.fs.stat(uri);
                exists = true;
            }
            catch {
                exists = false;
            }
            if (!exists) {
                return {
                    success: false,
                    error: `Target not found: ${target}`,
                    toolId: this.id,
                    actionName
                };
            }
            const stats = await vscode.workspace.fs.stat(uri);
            if (stats.type !== vscode.FileType.Directory) {
                return {
                    success: false,
                    error: `Target must be a directory: ${target}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Extract all dependencies
            const dependencies = await this.extractDependencies(target, true, 10, false, true);
            // Find circular dependencies
            const circularDeps = this.findCircular(dependencies, maxResults);
            return {
                success: true,
                output: {
                    circularDependencies: circularDeps,
                    count: circularDeps.length,
                    summary: this.generateCircularDependencySummary(circularDeps)
                },
                toolId: this.id,
                actionName,
                metadata: {
                    target,
                    maxResults,
                    circularCount: circularDeps.length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Finding circular dependencies failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async visualizeDependencies(input, actionName) {
        const target = input.target;
        const format = input.format || 'text';
        const depth = input.depth || 2;
        if (!target) {
            return {
                success: false,
                error: '\'target\' is required.',
                toolId: this.id,
                actionName
            };
        }
        try {
            // Validate target exists
            const uri = vscode.Uri.file(target);
            // Replace Promise.then().catch() with try/catch for better error handling
            let exists = false;
            try {
                await vscode.workspace.fs.stat(uri);
                exists = true;
            }
            catch {
                exists = false;
            }
            if (!exists) {
                return {
                    success: false,
                    error: `Target not found: ${target}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Determine if target is a file or directory
            const stats = await vscode.workspace.fs.stat(uri);
            const isDirectory = stats.type === vscode.FileType.Directory;
            // Extract dependencies
            const dependencies = await this.extractDependencies(target, isDirectory, depth, false, true);
            // Generate visualization based on format
            let visualization;
            switch (format) {
                case 'text':
                    visualization = this.generateTextVisualization(dependencies, target);
                    break;
                case 'json':
                    visualization = dependencies;
                    break;
                case 'dot':
                    visualization = this.generateDotVisualization(dependencies);
                    break;
                default:
                    visualization = this.generateTextVisualization(dependencies, target);
            }
            return {
                success: true,
                output: {
                    visualization,
                    format,
                    target,
                    depth
                },
                toolId: this.id,
                actionName,
                metadata: {
                    target,
                    format,
                    depth,
                    dependencyCount: Object.keys(dependencies).length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Dependency visualization failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    async analyzeImpact(input, actionName) {
        const filePath = input.filePath;
        const depth = input.depth || 3;
        if (!filePath) {
            return {
                success: false,
                error: '\'filePath\' is required.',
                toolId: this.id,
                actionName
            };
        }
        try {
            // Validate file exists
            const uri = vscode.Uri.file(filePath);
            // Replace Promise.then().catch() with try/catch for better error handling
            let exists = false;
            try {
                await vscode.workspace.fs.stat(uri);
                exists = true;
            }
            catch {
                exists = false;
            }
            if (!exists) {
                return {
                    success: false,
                    error: `File not found: ${filePath}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Get workspace root
            const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);
            if (!workspaceFolder) {
                return {
                    success: false,
                    error: `File is not in a workspace: ${filePath}`,
                    toolId: this.id,
                    actionName
                };
            }
            // Extract all dependencies in the workspace
            const allDependencies = await this.extractDependencies(workspaceFolder.uri.fsPath, true, 10, false, false);
            // Find files that depend on the target file (reverse dependencies)
            const impactedFiles = this.findImpactedFiles(allDependencies, filePath, depth);
            // Calculate impact metrics
            const impactMetrics = this.calculateImpactMetrics(impactedFiles, allDependencies);
            return {
                success: true,
                output: {
                    impactedFiles,
                    metrics: impactMetrics,
                    summary: this.generateImpactSummary(impactedFiles, impactMetrics)
                },
                toolId: this.id,
                actionName,
                metadata: {
                    filePath,
                    depth,
                    impactedCount: impactedFiles.length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Impact analysis failed: ${error.message || error}`,
                toolId: this.id,
                actionName
            };
        }
    }
    /**
       * Extract dependencies from a file or directory
       */
    async extractDependencies(target, isDirectory, depth, includeNodeModules, includeExternalDeps) {
        const dependencies = {};
        if (depth <= 0) {
            return dependencies;
        }
        if (isDirectory) {
            // Process all files in the directory recursively
            await this.processDirectory(target, dependencies, includeNodeModules, includeExternalDeps, depth);
        }
        else {
            // Process a single file
            await this.processFile(target, dependencies, includeExternalDeps);
        }
        return dependencies;
    }
    /**
       * Process a directory recursively to extract dependencies
       */
    async processDirectory(dirPath, dependencies, includeNodeModules, includeExternalDeps, depth) {
        try {
            const entries = await vscode.workspace.fs.readDirectory(vscode.Uri.file(dirPath));
            for (const [name, type] of entries) {
                // Skip node_modules if not included
                if (!includeNodeModules && name === 'node_modules') {
                    continue;
                }
                // Skip hidden directories and files
                if (name.startsWith('.')) {
                    continue;
                }
                const fullPath = path.join(dirPath, name);
                if (type === vscode.FileType.Directory) {
                    // Recursively process subdirectories
                    await this.processDirectory(fullPath, dependencies, includeNodeModules, includeExternalDeps, depth);
                }
                else if (type === vscode.FileType.File) {
                    // Process files with supported extensions
                    const ext = path.extname(name).toLowerCase();
                    if (['.js', '.jsx', '.ts', '.tsx', '.vue', '.py', '.java', '.cs', '.php', '.rb', '.go', '.rs'].includes(ext)) {
                        await this.processFile(fullPath, dependencies, includeExternalDeps);
                    }
                }
            }
        }
        catch (error) {
            console.error(`Error processing directory ${dirPath}:`, error);
        }
    }
    /**
       * Process a single file to extract its dependencies
       */
    async processFile(filePath, dependencies, includeExternalDeps) {
        try {
            const document = await vscode.workspace.openTextDocument(filePath);
            const text = document.getText();
            const ext = path.extname(filePath).toLowerCase();
            // Initialize empty dependency array for this file
            dependencies[filePath] = [];
            // Extract imports based on file type
            switch (ext) {
                case '.js':
                case '.jsx':
                case '.ts':
                case '.tsx':
                    this.extractJavaScriptDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
                case '.py':
                    this.extractPythonDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
                case '.java':
                    this.extractJavaDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
                case '.cs':
                    this.extractCSharpDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
                case '.php':
                    this.extractPHPDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
                case '.rb':
                    this.extractRubyDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
                case '.go':
                    this.extractGoDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
                case '.rs':
                    this.extractRustDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
                case '.vue':
                    this.extractVueDependencies(filePath, text, dependencies, includeExternalDeps);
                    break;
            }
        }
        catch (error) {
            console.error(`Error processing file ${filePath}:`, error);
        }
    }
    /**
       * Extract dependencies from JavaScript/TypeScript files
       */
    extractJavaScriptDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match ES6 imports
        const es6ImportRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)\s+from\s+)?['"]([^'"]+)['"]/g;
        let match;
        while ((match = es6ImportRegex.exec(content)) !== null) {
            const importPath = match[1];
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
        // Match CommonJS requires
        const requireRegex = /(?:const|let|var)\s+(?:\{[^}]*\}|\w+)\s*=\s*require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
        while ((match = requireRegex.exec(content)) !== null) {
            const importPath = match[1];
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
        // Match dynamic imports
        const dynamicImportRegex = /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
        while ((match = dynamicImportRegex.exec(content)) !== null) {
            const importPath = match[1];
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
    }
    /**
       * Extract dependencies from Python files
       */
    extractPythonDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match import statements
        const importRegex = /^\s*import\s+(\w+(?:\s*,\s*\w+)*)/gm;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            const imports = match[1].split(',').map(i => i.trim());
            for (const importName of imports) {
                this.addDependency(filePath, importName, dependencies, includeExternalDeps);
            }
        }
        // Match from ... import statements
        const fromImportRegex = /^\s*from\s+([.\w]+)\s+import\s+(?:\(([^)]+)\)|([^#\n]+))/gm;
        while ((match = fromImportRegex.exec(content)) !== null) {
            const moduleName = match[1];
            this.addDependency(filePath, moduleName, dependencies, includeExternalDeps);
        }
    }
    /**
       * Extract dependencies from Java files
       */
    extractJavaDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match import statements
        const importRegex = /^\s*import\s+([^;]+);/gm;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            const importPath = match[1].trim();
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
    }
    /**
       * Extract dependencies from C# files
       */
    extractCSharpDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match using statements
        const usingRegex = /^\s*using\s+([^;]+);/gm;
        let match;
        while ((match = usingRegex.exec(content)) !== null) {
            const importPath = match[1].trim();
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
    }
    /**
       * Extract dependencies from PHP files
       */
    extractPHPDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match require/include statements
        const requireRegex = /(?:require|include|require_once|include_once)\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
        let match;
        while ((match = requireRegex.exec(content)) !== null) {
            const importPath = match[1];
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
        // Match use statements
        const useRegex = /^\s*use\s+([^;]+);/gm;
        while ((match = useRegex.exec(content)) !== null) {
            const importPath = match[1].trim();
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
    }
    /**
       * Extract dependencies from Ruby files
       */
    extractRubyDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match require statements
        const requireRegex = /^\s*require\s+['"]([^'"]+)['"]/gm;
        let match;
        while ((match = requireRegex.exec(content)) !== null) {
            const importPath = match[1];
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
        // Match require_relative statements
        const requireRelativeRegex = /^\s*require_relative\s+['"]([^'"]+)['"]/gm;
        while ((match = requireRelativeRegex.exec(content)) !== null) {
            const importPath = match[1];
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
    }
    /**
       * Extract dependencies from Go files
       */
    extractGoDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match import statements
        const importRegex = /import\s*\(\s*((?:[\s\S](?!\)))*)\s*\)/g;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            const importBlock = match[1];
            const importLines = importBlock.split('\n');
            for (const line of importLines) {
                const importMatch = line.match(/\s*(?:\w+\s+)?["']([^"']+)["']/);
                if (importMatch) {
                    const importPath = importMatch[1];
                    this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
                }
            }
        }
        // Match single import statements
        const singleImportRegex = /import\s+(?:\w+\s+)?["']([^"']+)["']/g;
        while ((match = singleImportRegex.exec(content)) !== null) {
            const importPath = match[1];
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
    }
    /**
       * Extract dependencies from Rust files
       */
    extractRustDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match use statements
        const useRegex = /^\s*use\s+([^;]+);/gm;
        let match;
        while ((match = useRegex.exec(content)) !== null) {
            const importPath = match[1].trim();
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
        // Match extern crate statements
        const externCrateRegex = /^\s*extern\s+crate\s+([^;]+);/gm;
        while ((match = externCrateRegex.exec(content)) !== null) {
            const importPath = match[1].trim();
            this.addDependency(filePath, importPath, dependencies, includeExternalDeps);
        }
    }
    /**
       * Extract dependencies from Vue files
       */
    extractVueDependencies(filePath, content, dependencies, includeExternalDeps) {
        // Match script section
        const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/);
        if (scriptMatch) {
            const scriptContent = scriptMatch[1];
            this.extractJavaScriptDependencies(filePath, scriptContent, dependencies, includeExternalDeps);
        }
    }
    /**
       * Add a dependency to the dependencies map
       */
    addDependency(filePath, importPath, dependencies, includeExternalDeps) {
        // Skip external dependencies if not included
        if (!includeExternalDeps && this.isExternalDependency(importPath)) {
            return;
        }
        // Resolve relative paths
        let resolvedPath = importPath;
        if (importPath.startsWith('.')) {
            const dir = path.dirname(filePath);
            resolvedPath = path.resolve(dir, importPath);
            // Try to resolve with common extensions
            const extensions = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.py', '.java', '.cs', '.php', '.rb', '.go', '.rs'];
            // If the path doesn't have an extension, try to add one
            if (!path.extname(resolvedPath)) {
                for (const ext of extensions) {
                    const pathWithExt = `${resolvedPath}${ext}`;
                    if (fs.existsSync(pathWithExt)) {
                        resolvedPath = pathWithExt;
                        break;
                    }
                }
            }
        }
        // Add to dependencies
        if (!dependencies[filePath].includes(resolvedPath)) {
            dependencies[filePath].push(resolvedPath);
        }
    }
    /**
       * Check if a dependency is external (not a relative path)
       */
    isExternalDependency(importPath) {
        return !importPath.startsWith('.') && !importPath.startsWith('/');
    }
    /**
       * Calculate statistics for dependencies
       */
    calculateDependencyStats(dependencies) {
        const stats = {
            totalFiles: Object.keys(dependencies).length,
            totalDependencies: 0,
            averageDependenciesPerFile: 0,
            maxDependencies: 0,
            maxDependenciesFile: '',
            filesWithNoDependencies: 0,
            filesWithMostDependencies: []
        };
        // Calculate total dependencies and find max
        for (const [file, deps] of Object.entries(dependencies)) {
            stats.totalDependencies += deps.length;
            if (deps.length === 0) {
                stats.filesWithNoDependencies++;
            }
            if (deps.length > stats.maxDependencies) {
                stats.maxDependencies = deps.length;
                stats.maxDependenciesFile = file;
            }
        }
        // Calculate average
        if (stats.totalFiles > 0) {
            stats.averageDependenciesPerFile = stats.totalDependencies / stats.totalFiles;
        }
        // Find top 5 files with most dependencies
        stats.filesWithMostDependencies = Object.entries(dependencies)
            .map(([file, deps]) => ({ file, count: deps.length }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);
        return stats;
    }
    /**
       * Generate a summary of dependencies
       */
    generateDependencySummary(dependencies, stats) {
        return `
Dependency Analysis Summary:
---------------------------
Total Files: ${stats.totalFiles}
Total Dependencies: ${stats.totalDependencies}
Average Dependencies Per File: ${stats.averageDependenciesPerFile.toFixed(2)}
Files With No Dependencies: ${stats.filesWithNoDependencies}
File With Most Dependencies: ${stats.maxDependenciesFile} (${stats.maxDependencies} dependencies)

Top 5 Files With Most Dependencies:
${stats.filesWithMostDependencies.map((item) => `  ${item.file}: ${item.count} dependencies`).join('\n')}
`;
    }
    /**
       * Find circular dependencies in the dependency graph
       */
    findCircular(dependencies, maxResults) {
        const circularDeps = [];
        const visited = new Set();
        const stack = new Set();
        const dfs = (node, path = []) => {
            if (circularDeps.length >= maxResults) {
                return;
            }
            if (stack.has(node)) {
                // Found a cycle
                const cycleStart = path.indexOf(node);
                const cycle = [...path.slice(cycleStart), node];
                circularDeps.push({
                    cycle,
                    length: cycle.length
                });
                return;
            }
            if (visited.has(node) || !dependencies[node]) {
                return;
            }
            visited.add(node);
            stack.add(node);
            path.push(node);
            for (const dep of dependencies[node]) {
                dfs(dep, [...path]);
            }
            stack.delete(node);
        };
        // Start DFS from each node
        for (const node of Object.keys(dependencies)) {
            if (circularDeps.length >= maxResults) {
                break;
            }
            if (!visited.has(node)) {
                dfs(node);
            }
        }
        return circularDeps;
    }
    /**
       * Generate a summary of circular dependencies
       */
    generateCircularDependencySummary(circularDeps) {
        if (circularDeps.length === 0) {
            return 'No circular dependencies found.';
        }
        return `
Circular Dependencies Summary:
----------------------------
Found ${circularDeps.length} circular dependencies.

${circularDeps.map((item, index) => `
Circular Dependency #${index + 1}:
  Length: ${item.length}
  Path: ${item.cycle.join(' -> ')}
`).join('\n')}
`;
    }
    /**
       * Generate a text visualization of dependencies
       */
    generateTextVisualization(dependencies, rootPath) {
        const lines = [`Dependency Tree for ${rootPath}:`];
        const printDependencies = (file, deps, indent = '', visited = new Set()) => {
            if (visited.has(file)) {
                lines.push(`${indent}${file} (circular reference)`);
                return;
            }
            visited.add(file);
            if (deps.length === 0) {
                lines.push(`${indent}${file} (no dependencies)`);
                return;
            }
            lines.push(`${indent}${file}`);
            for (const dep of deps) {
                if (dependencies[dep]) {
                    printDependencies(dep, dependencies[dep], `${indent}  `, new Set(visited));
                }
                else {
                    lines.push(`${indent}  ${dep}`);
                }
            }
        };
        // Start with the root path if it's in the dependencies
        if (dependencies[rootPath]) {
            printDependencies(rootPath, dependencies[rootPath]);
        }
        else {
            // Otherwise, print all top-level dependencies
            for (const [file, deps] of Object.entries(dependencies)) {
                printDependencies(file, deps);
            }
        }
        return lines.join('\n');
    }
    /**
       * Generate a DOT visualization of dependencies
       */
    generateDotVisualization(dependencies) {
        const lines = ['digraph Dependencies {', '  rankdir=LR;', '  node [shape=box, style=filled, fillcolor=lightblue];'];
        // Add nodes
        for (const file of Object.keys(dependencies)) {
            const nodeName = this.sanitizeForDot(file);
            const label = path.basename(file);
            lines.push(`  "${nodeName}" [label="${label}"];`);
        }
        // Add edges
        for (const [file, deps] of Object.entries(dependencies)) {
            const sourceNode = this.sanitizeForDot(file);
            for (const dep of deps) {
                if (dependencies[dep]) { // Only include internal dependencies
                    const targetNode = this.sanitizeForDot(dep);
                    lines.push(`  "${sourceNode}" -> "${targetNode}";`);
                }
            }
        }
        lines.push('}');
        return lines.join('\n');
    }
    /**
       * Sanitize a string for use in DOT format
       */
    sanitizeForDot(str) {
        return str.replace(/\\/g, '/');
    }
    /**
       * Find files that would be impacted by changing a file
       */
    findImpactedFiles(dependencies, targetFile, depth) {
        const impacted = [];
        const visited = new Set();
        const findDependents = (file, currentDepth = 0) => {
            if (currentDepth > depth || visited.has(file)) {
                return;
            }
            visited.add(file);
            // Find files that depend on this file
            for (const [depFile, deps] of Object.entries(dependencies)) {
                if (deps.includes(file) && !impacted.includes(depFile)) {
                    impacted.push(depFile);
                    findDependents(depFile, currentDepth + 1);
                }
            }
        };
        findDependents(targetFile);
        return impacted;
    }
    /**
       * Calculate impact metrics
       */
    calculateImpactMetrics(impactedFiles, allDependencies) {
        const totalFiles = Object.keys(allDependencies).length;
        const impactPercentage = totalFiles > 0 ? (impactedFiles.length / totalFiles) * 100 : 0;
        // Calculate impact severity
        let impactSeverity = 'Low';
        if (impactPercentage > 50) {
            impactSeverity = 'High';
        }
        else if (impactPercentage > 20) {
            impactSeverity = 'Medium';
        }
        return {
            impactedCount: impactedFiles.length,
            totalFiles,
            impactPercentage,
            impactSeverity
        };
    }
    /**
       * Generate a summary of impact analysis
       */
    generateImpactSummary(impactedFiles, metrics) {
        return `
Impact Analysis Summary:
----------------------
File would impact ${metrics.impactedCount} out of ${metrics.totalFiles} files (${metrics.impactPercentage.toFixed(2)}%).
Impact Severity: ${metrics.impactSeverity}

${impactedFiles.length > 0 ? `Impacted Files:
${impactedFiles.map(file => `  ${file}`).join('\n')}` : 'No files would be impacted.'}
`;
    }
}
exports.DependencyAnalysisTool = DependencyAnalysisTool;
//# sourceMappingURL=dependencyAnalysisTool.js.map