"use strict";
/**
 * Pattern Refactoring Workflow
 *
 * This module provides a workflow for pattern-based code refactoring:
 * - Identifying code patterns
 * - Applying design patterns
 * - Refactoring code to follow best practices
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPatternRefactoringWorkflow = createPatternRefactoringWorkflow;
exports.createDesignPatternImplementationWorkflow = createDesignPatternImplementationWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
/**
 * Create a Pattern Refactoring workflow
 */
function createPatternRefactoringWorkflow(id, name, description, analyzerAgent, refactoringAgent, testingAgent, tools = []) {
    logger_1.Logger.instance.info(`Creating Pattern Refactoring workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const codeAnalysisNode = graph_1.Codessa.createAgentNode('code-analysis', 'Code Analysis', analyzerAgent);
    const patternIdentificationNode = graph_1.Codessa.createAgentNode('pattern-identification', 'Pattern Identification', analyzerAgent);
    const designPatternSelectionNode = graph_1.Codessa.createAgentNode('design-pattern-selection', 'Design Pattern Selection', analyzerAgent);
    const refactoringPlanNode = graph_1.Codessa.createAgentNode('refactoring-plan', 'Refactoring Plan', refactoringAgent);
    const codeTransformationNode = graph_1.Codessa.createAgentNode('code-transformation', 'Code Transformation', refactoringAgent);
    const testingNode = graph_1.Codessa.createAgentNode('testing', 'Testing', testingAgent);
    const documentationUpdateNode = graph_1.Codessa.createAgentNode('documentation-update', 'Documentation Update', refactoringAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-analysis', source: 'input', target: 'code-analysis', type: 'default' },
        { name: 'analysis-to-pattern', source: 'code-analysis', target: 'pattern-identification', type: 'default' },
        { name: 'pattern-to-design', source: 'pattern-identification', target: 'design-pattern-selection', type: 'default' },
        { name: 'design-to-plan', source: 'design-pattern-selection', target: 'refactoring-plan', type: 'default' },
        { name: 'plan-to-transformation', source: 'refactoring-plan', target: 'code-transformation', type: 'default' },
        { name: 'transformation-to-testing', source: 'code-transformation', target: 'testing', type: 'default' },
        { name: 'testing-to-documentation', source: 'testing', target: 'documentation-update', type: 'default' },
        { name: 'documentation-to-output', source: 'documentation-update', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'testing-to-transformation', source: 'testing', target: 'code-transformation', type: 'feedback' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect code transformation to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `transformation-to-tool-${index}`,
                source: 'code-transformation',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to testing
            edges.push({
                name: `tool-${index}-to-testing`,
                source: `tool-${index}`,
                target: 'testing',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            codeAnalysisNode,
            patternIdentificationNode,
            designPatternSelectionNode,
            refactoringPlanNode,
            codeTransformationNode,
            testingNode,
            documentationUpdateNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'pattern-refactoring',
        tags: ['refactoring', 'design-patterns', 'code-quality']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a specialized Design Pattern Implementation workflow
 */
function createDesignPatternImplementationWorkflow(id, name, description, designerAgent, implementerAgent, tools = []) {
    logger_1.Logger.instance.info(`Creating Design Pattern Implementation workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const requirementsAnalysisNode = graph_1.Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', designerAgent);
    const patternSelectionNode = graph_1.Codessa.createAgentNode('pattern-selection', 'Pattern Selection', designerAgent);
    const patternAdaptationNode = graph_1.Codessa.createAgentNode('pattern-adaptation', 'Pattern Adaptation', designerAgent);
    const classDesignNode = graph_1.Codessa.createAgentNode('class-design', 'Class Design', designerAgent);
    const interfaceDesignNode = graph_1.Codessa.createAgentNode('interface-design', 'Interface Design', designerAgent);
    const implementationNode = graph_1.Codessa.createAgentNode('implementation', 'Implementation', implementerAgent);
    const testCaseGenerationNode = graph_1.Codessa.createAgentNode('test-case-generation', 'Test Case Generation', implementerAgent);
    const documentationNode = graph_1.Codessa.createAgentNode('documentation', 'Documentation', implementerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },
        { name: 'requirements-to-pattern', source: 'requirements-analysis', target: 'pattern-selection', type: 'default' },
        { name: 'pattern-to-adaptation', source: 'pattern-selection', target: 'pattern-adaptation', type: 'default' },
        { name: 'adaptation-to-class', source: 'pattern-adaptation', target: 'class-design', type: 'default' },
        { name: 'adaptation-to-interface', source: 'pattern-adaptation', target: 'interface-design', type: 'default' },
        { name: 'class-to-implementation', source: 'class-design', target: 'implementation', type: 'default' },
        { name: 'interface-to-implementation', source: 'interface-design', target: 'implementation', type: 'default' },
        { name: 'implementation-to-test', source: 'implementation', target: 'test-case-generation', type: 'default' },
        { name: 'test-to-documentation', source: 'test-case-generation', target: 'documentation', type: 'default' },
        { name: 'documentation-to-output', source: 'documentation', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'test-to-implementation', source: 'test-case-generation', target: 'implementation', type: 'feedback' }
    ];
    // Add tool edges if tools are provided
    if (toolNodes.length > 0) {
        // Connect implementation to tools
        toolNodes.forEach((_, index) => {
            edges.push({
                name: `implementation-to-tool-${index}`,
                source: 'implementation',
                target: `tool-${index}`,
                type: 'conditional'
            });
            // Connect tools back to test case generation
            edges.push({
                name: `tool-${index}-to-test`,
                source: `tool-${index}`,
                target: 'test-case-generation',
                type: 'default'
            });
        });
    }
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            requirementsAnalysisNode,
            patternSelectionNode,
            patternAdaptationNode,
            classDesignNode,
            interfaceDesignNode,
            implementationNode,
            testCaseGenerationNode,
            documentationNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'pattern-refactoring',
        tags: ['design-patterns', 'implementation', 'architecture']
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=patternRefactoringWorkflow.js.map