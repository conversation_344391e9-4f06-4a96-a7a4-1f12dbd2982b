"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefactorAgent = void 0;
const agent_1 = require("../agentUtilities/agent");
const logger_1 = require("../../logger");
class RefactorAgent extends agent_1.Agent {
    constructor(_config) {
        super({
            ..._config,
            role: 'assistant',
            capabilities: ['refactor'],
            llmProvider: 'openai',
            llmModel: 'gpt-4'
        });
    }
    async run(input, context = {}) {
        logger_1.logger.info(`RefactorAgent processing: "${input.prompt.substring(0, 50)}..."`);
        // --- Gather relevant workflow for refactor mode ---
        let workflow = undefined;
        if (context.variables?.workflowManager) {
            workflow = await context.variables.workflowManager.getWorkflowForMode('refactor');
        }
        // --- Gather all relevant tools ---
        let tools = this.tools;
        if (context.tools) {
            tools = context.tools;
        }
        // --- Gather memory and knowledgebase context ---
        let memoryContext = '';
        if (this.getMemory) {
            const memories = await this.getMemory().getRelevantMemories(input.prompt);
            if (memories?.length) {
                memoryContext = this.getMemory().formatMemoriesForPrompt(memories);
            }
        }
        let kbContext = '';
        if (context.variables?.knowledgebaseManager) {
            kbContext = await context.variables.knowledgebaseManager.getRelevantKnowledge(input.prompt);
        }
        // --- Gather MCP context ---
        let mcpContext = '';
        if (context.variables?.mcpManager) {
            mcpContext = JSON.stringify(context.variables.mcpManager.getCurrentContext() || {});
        }
        // --- Use external prompt if available ---
        let externalPrompt = '';
        if (context.variables?.promptManager) {
            externalPrompt = context.variables.promptManager.getSystemPrompt('refactorAgent', context.variables) || '';
        }
        // --- Compose final prompt ---
        const refactorPrompt = `
# Refactor Mode Processing

${externalPrompt}

## User Request:
${input.prompt}

## Memory Context:
${memoryContext}

## Knowledgebase Context:
${kbContext}

## MCP Context:
${mcpContext}

## Workflow:
${workflow ? JSON.stringify(workflow) : 'None'}

## Code to Refactor:
${context.workspace?.currentFile ? (typeof context.workspace.currentFile === 'string' ? context.workspace.currentFile : context.workspace.currentFile) : 'No code context available'}

## Your Task:
1. Analyze the code for refactoring opportunities
2. Suggest specific refactoring changes
3. Explain the benefits of each change
Use all available workflows, tools, memory, knowledgebase, and context. Stream output if possible.
`;
        input.prompt = refactorPrompt;
        // Pass all gathered context and tools to super.run
        return super.run(input, { ...context, tools });
    }
}
exports.RefactorAgent = RefactorAgent;
//# sourceMappingURL=refactorAgent.js.map