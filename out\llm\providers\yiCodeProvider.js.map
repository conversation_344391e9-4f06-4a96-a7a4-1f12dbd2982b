{"version": 3, "file": "yiCodeProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/yiCodeProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAGpD,yCAAsC;AAGtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;;;;GAKG;AACH,MAAa,cAAe,SAAQ,iCAAe;IACxC,UAAU,GAAG,QAAQ,CAAC;IACtB,WAAW,GAAG,SAAS,CAAC;IACxB,WAAW,GAAG,gCAAgC,CAAC;IAC/C,OAAO,GAAG,gBAAgB,CAAC;IAC3B,cAAc,GAAG,IAAI,CAAC,CAAC,gCAAgC;IACvD,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,6CAA6C,CAAC;IAChE,YAAY,GAAG,mBAAmB,CAAC;IAEpC,MAAM,GAAQ,IAAI,CAAC;IAE3B,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;gBAC/E,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAC9D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,MAAM,EAAE,sCAAsC;gBACvD,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,MAAM,EAAE;oBACnC,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACrE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,QAAQ,CACZ,MAAyB,EACzB,iBAA4C,EAC5C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,oDAAoD,EAAE,CAAC;QACtF,CAAC;QAED,IAAI,CAAC;YACH,wFAAwF;YACxF,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAChF,MAAM,QAAQ,GAAG,GAAG,OAAO,EAAE,CAAC;YAE9B,qBAAqB;YACrB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,0CAA0C;YAC1C,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,IAAI,uBAAuB,MAAM,CAAC,YAAY,gBAAgB,CAAC;YACvE,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,qBAAqB,OAAO,CAAC,OAAO,gBAAgB,CAAC;oBACjE,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBACxC,MAAM,IAAI,0BAA0B,OAAO,CAAC,OAAO,gBAAgB,CAAC;oBACtE,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrC,MAAM,IAAI,uBAAuB,OAAO,CAAC,OAAO,gBAAgB,CAAC;oBACnE,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,IAAI,qBAAqB,MAAM,CAAC,MAAM,gBAAgB,CAAC;YAC/D,CAAC;YAED,gFAAgF;YAChF,MAAM,IAAI,yBAAyB,CAAC;YAEpC,mDAAmD;YACnD,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACpE,CAAC;YAED,uBAAuB;YACvB,MAAM,WAAW,GAAG;gBAClB,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE;oBACV,cAAc,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;oBACxC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,KAAK,EAAE,IAAI;oBACX,SAAS,EAAE,IAAI;oBACf,gBAAgB,EAAE,KAAK;oBACvB,IAAI,EAAE,MAAM,CAAC,aAAa,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC;iBAC7D;aACF,CAAC;YAEF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC;YAErE,kEAAkE;YAClE,IAAI,eAA4C,CAAC;YAEjD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;oBAC3C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;oBACxC,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;wBAC7C,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;wBAC1D,eAAe,EAAE,KAAK,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;gBAChH,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE;gBAC7D,MAAM,EAAE,eAAe,EAAE,MAAM;aAChC,CAAC,CAAC;YAEH,8CAA8C;YAC9C,IAAI,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;YACvE,CAAC;YAED,qBAAqB;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,iEAAiE;YACjE,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/C,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;oBAC7B,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;gBACrC,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,MAAM;gBACpB,KAAK,EAAE;oBACL,YAAY,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;oBAClD,gBAAgB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB;iBACxD;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,YAAY,GAAG,6BAA6B,CAAC;YAEjD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,sBAAsB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAChH,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACvC,YAAY,GAAG,2BAA2B,CAAC;YAC7C,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,UAAU;QACd,2DAA2D;QAC3D,OAAO;YACL;gBACE,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,sCAAsC;gBACnD,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,4BAA4B;aAC1C;YACD;gBACE,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,gDAAgD;gBAC7D,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,4BAA4B;aAC1C;YACD;gBACE,EAAE,EAAE,kBAAkB;gBACtB,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,uCAAuC;gBACpD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,4BAA4B;aAC1C;SACF,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,4DAA4D;aACtE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,qDAAqD;YACrD,MAAM,QAAQ,GAAG,OAAO,CAAC;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChD,MAAM,EAAE,oBAAoB;gBAC5B,UAAU,EAAE;oBACV,cAAc,EAAE,EAAE;oBAClB,gBAAgB,EAAE,KAAK;iBACxB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAClB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,gEAAgE,OAAO,IAAI;iBACrF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uDAAuD;iBACjE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,YAAY,GAAG,uCAAuC,CAAC;YAE3D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxG,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,yEAAyE;gBACtF,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,gGAAgG;gBAC7G,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,kCAAkC;gBAC/C,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,mBAAmB;oBACnB,mBAAmB;oBACnB,kBAAkB;iBACnB;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAzSD,wCAySC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { <PERSON><PERSON><PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { Logger } from '../../logger';\nimport { ITool } from '../../tools/tool.ts.backup';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for Yi-Code models from 01.ai\n * \n * Yi-Code is a powerful code model from 01.ai\n * It can be used via Hugging Face or self-hosted\n */\nexport class YiCodeProvider extends BaseLLMProvider {\n  readonly providerId = 'yicode';\n  readonly displayName = 'Yi-Code';\n  readonly description = 'Powerful code model from 01.ai';\n  readonly website = 'https://01.ai/';\n  readonly requiresApiKey = true; // Required for Hugging Face API\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api-inference.huggingface.co/models';\n  readonly defaultModel = '01-ai/Yi-34B-Code';\n\n  private client: any = null;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        Logger.instance.info('Yi-Code configuration changed, re-initializing client.');\n        this.loadConfig().then(() => this.initializeClient());\n      }\n    });\n  }\n\n  private initializeClient() {\n    const apiKey = this.config.apiKey;\n    const baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n\n    if (!apiKey) {\n      Logger.instance.warn('API key not set for Yi-Code provider.');\n      this.client = null;\n      return;\n    }\n\n    try {\n      // Initialize axios client with proper configuration\n      this.client = axios.create({\n        baseURL: baseUrl,\n        timeout: 120000, // 2 minutes timeout for model loading\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      Logger.instance.info('Yi-Code client initialized successfully.');\n    } catch (error) {\n      Logger.instance.error('Failed to initialize Yi-Code client:', error);\n      this.client = null;\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.client;\n  }\n\n  /**\n     * Generate text using Yi-Code models\n     */\n  async generate(\n    params: LLMGenerateParams,\n    cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'Yi-Code provider not configured (API key missing?)' };\n    }\n\n    try {\n      // Prepare the model endpoint - for Hugging Face we need to specify the model in the URL\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n      const endpoint = `${modelId}`;\n\n      // Prepare the prompt\n      let prompt = '';\n            \n      // Yi-Code uses a specific format for chat\n      if (params.systemPrompt) {\n        prompt += `<|im_start|>system\\n${params.systemPrompt}<|im_end|>\\n\\n`;\n      }\n            \n      // Add history if provided\n      if (params.history && params.history.length > 0) {\n        for (const message of params.history) {\n          if (message.role === 'user') {\n            prompt += `<|im_start|>user\\n${message.content}<|im_end|>\\n\\n`;\n          } else if (message.role === 'assistant') {\n            prompt += `<|im_start|>assistant\\n${message.content}<|im_end|>\\n\\n`;\n          } else if (message.role === 'system') {\n            prompt += `<|im_start|>system\\n${message.content}<|im_end|>\\n\\n`;\n          }\n        }\n      } else {\n        // Just add the user prompt\n        prompt += `<|im_start|>user\\n${params.prompt}<|im_end|>\\n\\n`;\n      }\n            \n      // Add the assistant prefix to indicate we want the model to generate a response\n      prompt += '<|im_start|>assistant\\n';\n\n      // Check for cancellation before making the request\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled before sending' };\n      }\n\n      // Prepare request data\n      const requestData = {\n        inputs: prompt,\n        parameters: {\n          max_new_tokens: params.maxTokens || 1024,\n          temperature: params.temperature || 0.7,\n          top_p: 0.95,\n          do_sample: true,\n          return_full_text: false,\n          stop: params.stopSequences || ['<|im_start|>', '<|im_end|>']\n        }\n      };\n\n      Logger.instance.debug(`Sending request to Yi-Code model ${modelId}`);\n\n      // Create cancellation token source to abort the request if needed\n      let abortController: AbortController | undefined;\n\n      if (cancellationToken) {\n        if (typeof AbortController !== 'undefined') {\n          abortController = new AbortController();\n          cancellationToken.onCancellationRequested(() => {\n            Logger.instance.info('Yi-Code request cancelled by user');\n            abortController?.abort();\n          });\n        } else {\n          Logger.instance.warn('AbortController not available in this environment, cancellation may not work properly');\n        }\n      }\n\n      // Make the API request\n      const response = await this.client.post(endpoint, requestData, {\n        signal: abortController?.signal\n      });\n\n      // Check for cancellation again after API call\n      if (cancellationToken?.isCancellationRequested) {\n        return { content: '', error: 'Request cancelled during processing' };\n      }\n\n      // Parse the response\n      const result = response.data;\n            \n      // Hugging Face Inference API returns an array of generated texts\n      let content = '';\n      if (Array.isArray(result) && result.length > 0) {\n        if (result[0].generated_text) {\n          content = result[0].generated_text;\n        }\n      }\n\n      return {\n        content,\n        finishReason: 'stop',\n        usage: {\n          promptTokens: prompt.length / 4, // Rough estimate\n          completionTokens: content.length / 4, // Rough estimate\n        }\n      };\n    } catch (error: any) {\n      Logger.instance.error('Yi-Code generate error:', error);\n      let errorMessage = 'Failed to call Yi-Code API.';\n\n      if (error.response) {\n        errorMessage = `Yi-Code API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error.name === 'AbortError') {\n        errorMessage = 'Request cancelled by user';\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        content: '',\n        error: errorMessage,\n        finishReason: 'error'\n      };\n    }\n  }\n\n  /**\n     * List available Yi-Code models\n     */\n  async listModels(): Promise<LLMModelInfo[]> {\n    // Yi-Code models are fixed, so we return a predefined list\n    return [\n      {\n        id: '01-ai/Yi-34B-Code',\n        name: 'Yi-34B-Code',\n        description: 'Large Yi-Code model (34B parameters)',\n        contextWindow: 32768,\n        pricingInfo: 'Free with Hugging Face API'\n      },\n      {\n        id: '01-ai/Yi-34B-Chat',\n        name: 'Yi-34B-Chat',\n        description: 'General-purpose Yi chat model (34B parameters)',\n        contextWindow: 32768,\n        pricingInfo: 'Free with Hugging Face API'\n      },\n      {\n        id: '01-ai/Yi-6B-Chat',\n        name: 'Yi-6B-Chat',\n        description: 'Smaller Yi chat model (6B parameters)',\n        contextWindow: 4096,\n        pricingInfo: 'Free with Hugging Face API'\n      }\n    ];\n  }\n\n  /**\n     * Test connection to Yi-Code\n     */\n  public async testConnection(modelId: string): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'Yi-Code client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Simple test request to check if the API is working\n      const endpoint = modelId;\n      const response = await this.client.post(endpoint, {\n        inputs: 'def hello_world():',\n        parameters: {\n          max_new_tokens: 10,\n          return_full_text: false\n        }\n      });\n\n      if (response.data) {\n        return {\n          success: true,\n          message: `Successfully connected to Hugging Face API and tested model '${modelId}'.`\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Connected to API but received an unexpected response.'\n        };\n      }\n    } catch (error: any) {\n      Logger.instance.error('Yi-Code connection test failed:', error);\n      let errorMessage = 'Failed to connect to Hugging Face API';\n\n      if (error.response) {\n        errorMessage = `API Error (${error.response.status}): ${error.response.data?.error || error.message}`;\n      } else if (error instanceof Error) {\n        errorMessage = error.message;\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'Hugging Face API Key',\n        description: 'Your Hugging Face API key (from https://huggingface.co/settings/tokens)',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The Hugging Face Inference API endpoint (default: https://api-inference.huggingface.co/models)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default Yi-Code model to use',\n        required: false,\n        type: 'select',\n        options: [\n          '01-ai/Yi-34B-Code',\n          '01-ai/Yi-34B-Chat',\n          '01-ai/Yi-6B-Chat'\n        ]\n      }\n    ];\n  }\n}\n"]}