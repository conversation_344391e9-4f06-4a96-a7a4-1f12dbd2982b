/**
 * Codessa Research Workflow
 *
 * This module provides workflow templates for research-focused tasks:
 * - Information gathering
 * - Source evaluation
 * - Data analysis
 * - Synthesis and reporting
 */

import { ITool } from '../../tools/tool.ts.backup';
import { Agent } from '../agentUtilities/agent';
import { <PERSON>ssa } from './graph';
import { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';
import { workflowRegistry } from './workflowRegistry';
import { logger } from '../../logger';
import { StructuredTool } from './corePolyfill';
import { codessaMemoryProvider as codessaMemoryManager } from '../../memory/codessa/codessaMemory';
import { MemorySource, MemoryType } from '../../memory/types';

/**
 * Create a Research workflow for in-depth research tasks
 */
export function createResearchWorkflow(
  id: string,
  name: string,
  description: string,
  researchAgent: Agent,
  analysisAgent: Agent,
  summaryAgent: Agent,
  tools: ITool[] = []
): GraphDefinition {
  logger.info(`Creating Research workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const queryAnalysisNode = Codessa.createAgentNode('query-analysis', 'Query Analysis', researchAgent);
  const informationRetrievalNode = Codessa.createAgentNode('information-retrieval', 'Information Retrieval', researchAgent);

  // Create a memory manager for the research workflow
  const researchMemoryManager = {
    async storeResearchQuery(query: string): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content: JSON.stringify(query),
          metadata: {
            source: 'user' as MemorySource,
            type: 'text' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['research', 'query']
          }
        });
        logger.info('Saved research query to memory');
      } catch (error) {
        logger.error('Failed to save research query to memory:', error);
      }
    },

    async retrieveRelevantInformation(query: string): Promise<any[]> {
      try {
        const memories = await codessaMemoryManager.searchMemories({
          query,
          limit: 5
        });

        const information = memories.map(m => {
          try {
            return JSON.parse(m.content);
          } catch (parseError) {
            logger.debug('Failed to parse memory content as JSON:', parseError);
            return m.content;
          }
        });

        logger.info(`Retrieved ${information.length} relevant information items from memory`);
        return information;
      } catch (error) {
        logger.error('Failed to retrieve relevant information from memory:', error);
        return [];
      }
    },

    async storeResearchFindings(findings: any): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content: JSON.stringify(findings),
          metadata: {
            source: 'system' as MemorySource,
            type: 'insight' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['research', 'findings']
          }
        });
        logger.info('Saved research findings to memory');
      } catch (error) {
        logger.error('Failed to save research findings to memory:', error);
      }
    }
  };

  const sourceEvaluationNode = Codessa.createAgentNode('source-evaluation', 'Source Evaluation', analysisAgent);
  const dataAnalysisNode = Codessa.createAgentNode('data-analysis', 'Data Analysis', analysisAgent);
  const synthesisNode = Codessa.createAgentNode('synthesis', 'Synthesis', summaryAgent);


  const conclusionNode = Codessa.createAgentNode('conclusion', 'Conclusion', summaryAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-query', source: 'input', target: 'query-analysis', type: 'default' },
    { name: 'query-to-retrieval', source: 'query-analysis', target: 'information-retrieval', type: 'default' },
    { name: 'retrieval-to-evaluation', source: 'information-retrieval', target: 'source-evaluation', type: 'default' },
    { name: 'evaluation-to-analysis', source: 'source-evaluation', target: 'data-analysis', type: 'default' },
    { name: 'analysis-to-synthesis', source: 'data-analysis', target: 'synthesis', type: 'default' },
    { name: 'synthesis-to-conclusion', source: 'synthesis', target: 'conclusion', type: 'default' },
    { name: 'conclusion-to-output', source: 'conclusion', target: 'output', type: 'default' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect information retrieval to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `retrieval-to-tool-${index}`,
        source: 'information-retrieval',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to data analysis
      edges.push({
        name: `tool-${index}-to-analysis`,
        source: `tool-${index}`,
        target: 'data-analysis',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      queryAnalysisNode,
      informationRetrievalNode,
      sourceEvaluationNode,
      dataAnalysisNode,
      synthesisNode,
      conclusionNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'research' as OperationMode,
    tags: ['research', 'analysis', 'information-retrieval'],
    metadata: {
      memoryManager: researchMemoryManager
    }
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}

/**
 * Create a specialized academic research workflow
 */
export function createAcademicResearchWorkflow(
  id: string,
  name: string,
  description: string,
  researchAgent: Agent,
  analysisAgent: Agent,
  tools: (ITool | StructuredTool)[] = []
): GraphDefinition {
  logger.info(`Creating Academic Research workflow: ${name}`);

  // Create nodes
  const inputNode = Codessa.createInputNode('input', 'Input');
  const literatureReviewNode = Codessa.createAgentNode('literature-review', 'Literature Review', researchAgent);
  const hypothesisFormulationNode = Codessa.createAgentNode('hypothesis-formulation', 'Hypothesis Formulation', researchAgent);
  const methodologyDesignNode = Codessa.createAgentNode('methodology-design', 'Methodology Design', researchAgent);
  const dataCollectionNode = Codessa.createAgentNode('data-collection', 'Data Collection', researchAgent);
  const dataAnalysisNode = Codessa.createAgentNode('data-analysis', 'Data Analysis', analysisAgent);
  const resultsInterpretationNode = Codessa.createAgentNode('results-interpretation', 'Results Interpretation', analysisAgent);
  const conclusionNode = Codessa.createAgentNode('conclusion', 'Conclusion', analysisAgent);
  const outputNode = Codessa.createOutputNode('output', 'Output');

  const academicResearchMemoryManager = {
    async storeLiteratureReview(literature: any): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content: JSON.stringify(literature),
          metadata: {
            source: 'system' as MemorySource,
            type: 'document' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['academic', 'research', 'literature']
          }
        });
        logger.info('Saved literature review to memory');
      } catch (error) {
        logger.error('Failed to save literature review to memory:', error);
      }
    },

    async storeHypothesis(hypothesis: any): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content: JSON.stringify(hypothesis),
          metadata: {
            source: 'system' as MemorySource,
            type: 'insight' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['academic', 'research', 'hypothesis']
          }
        });
        logger.info('Saved hypothesis to memory');
      } catch (error) {
        logger.error('Failed to save hypothesis to memory:', error);
      }
    },

    async storeMethodology(methodology: any): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content: JSON.stringify(methodology),
          metadata: {
            source: 'system' as MemorySource,
            type: 'procedural' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['academic', 'research', 'methodology']
          }
        });
        logger.info('Saved methodology to memory');
      } catch (error) {
        logger.error('Failed to save methodology to memory:', error);
      }
    },

    async storeResults(results: any): Promise<void> {
      try {
        await codessaMemoryManager.addMemory({
          content: JSON.stringify(results),
          metadata: {
            source: 'system' as MemorySource,
            type: 'insight' as MemoryType,
            timestamp: new Date().toISOString(),
            tags: ['academic', 'research', 'results']
          }
        });
        logger.info('Saved research results to memory');
      } catch (error) {
        logger.error('Failed to save research results to memory:', error);
      }
    },

    async retrieveRelatedResearch(query: string): Promise<any[]> {
      try {
        const memories = await codessaMemoryManager.searchMemories({
          query,
          limit: 5
        });

        const research = memories.map(m => {
          try {
            return JSON.parse(m.content);
          } catch (parseError) {
            logger.debug('Failed to parse research memory as JSON:', parseError);
            return m.content;
          }
        });

        logger.info(`Retrieved ${research.length} related research items from memory`);
        return research;
      } catch (error) {
        logger.error('Failed to retrieve related research from memory:', error);
        return [];
      }
    }
  };

  // Add tool nodes if tools are provided
  const toolNodes: GraphNode[] = tools.map((tool, index) =>
    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)
  );

  // Create edges
  const edges: GraphEdge[] = [
    { name: 'input-to-literature', source: 'input', target: 'literature-review', type: 'default' },
    { name: 'literature-to-hypothesis', source: 'literature-review', target: 'hypothesis-formulation', type: 'default' },
    { name: 'hypothesis-to-methodology', source: 'hypothesis-formulation', target: 'methodology-design', type: 'default' },
    { name: 'methodology-to-data-collection', source: 'methodology-design', target: 'data-collection', type: 'default' },
    { name: 'data-collection-to-analysis', source: 'data-collection', target: 'data-analysis', type: 'default' },
    { name: 'analysis-to-interpretation', source: 'data-analysis', target: 'results-interpretation', type: 'default' },
    { name: 'interpretation-to-conclusion', source: 'results-interpretation', target: 'conclusion', type: 'default' },
    { name: 'conclusion-to-output', source: 'conclusion', target: 'output', type: 'default' }
  ];

  // Add tool edges if tools are provided
  if (toolNodes.length > 0) {
    // Connect data collection to tools
    toolNodes.forEach((_, index) => {
      edges.push({
        name: `data-collection-to-tool-${index}`,
        source: 'data-collection',
        target: `tool-${index}`,
        type: 'conditional'
      });

      // Connect tools back to data analysis
      edges.push({
        name: `tool-${index}-to-analysis`,
        source: `tool-${index}`,
        target: 'data-analysis',
        type: 'default'
      });
    });
  }

  // Create workflow definition
  const workflow: GraphDefinition = {
    id,
    name,
    description,
    version: '1.0.0',
    nodes: [
      inputNode,
      literatureReviewNode,
      hypothesisFormulationNode,
      methodologyDesignNode,
      dataCollectionNode,
      dataAnalysisNode,
      resultsInterpretationNode,
      conclusionNode,
      outputNode,
      ...toolNodes
    ],
    edges,
    startNodeId: 'input',
    operationMode: 'research' as OperationMode,
    tags: ['research', 'academic', 'analysis'],
    metadata: {
      memoryManager: academicResearchMemoryManager
    }
  };

  // Register workflow
  workflowRegistry.registerWorkflow(workflow);

  return workflow;
}
