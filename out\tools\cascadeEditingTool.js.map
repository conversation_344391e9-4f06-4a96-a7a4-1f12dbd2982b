{"version": 3, "file": "cascadeEditingTool.js", "sourceRoot": "", "sources": ["../../src/tools/cascadeEditingTool.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAIH,qEAAkE;AAClE,yDAAwD;AACxD,uEAA8D;AAC9D,sCAAmC;AAwDnC,MAAa,kBAAkB;IACpB,EAAE,GAAG,cAAc,CAAC;IACpB,IAAI,GAAG,sBAAsB,CAAC;IAC9B,WAAW,GAAG,iEAAiE,CAAC;IAChF,IAAI,GAAG,cAAuB,CAAC;IAC/B,QAAQ,GAAG,SAAS,CAAC;IAEtB,cAAc,CAAyB;IACvC,gBAAgB,CAAqB;IACrC,iBAAiB,CAAoB;IAE7C,uDAAuD;IAC/C,eAAe,GAAqB,IAAI,GAAG,EAAE,CAAC;IAC9C,SAAS,GAA8B,IAAI,GAAG,EAAE,CAAC;IACxC,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,kCAAkC;IAChE,YAAY,GAAG,EAAE,CAAC;IAEnC;QACE,IAAI,CAAC,cAAc,GAAG,IAAI,+CAAsB,EAAE,CAAC;QACnD,IAAI,CAAC,gBAAgB,GAAG,IAAI,qCAAkB,EAAE,CAAC;QACjD,IAAI,CAAC,iBAAiB,GAAG,IAAI,2CAAiB,EAAE,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,IAAI,CAAC;YACH,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,sBAAsB;oBACzB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACzD,KAAK,oBAAoB;oBACvB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACvD,KAAK,uBAAuB;oBAC1B,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC1D,KAAK,qBAAqB;oBACxB,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACxD,KAAK,mBAAmB;oBACtB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACtD,KAAK,sBAAsB;oBACzB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACzD;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,UAAU,EAAE;wBACtC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU;qBACX,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAChD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC1F,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,KAAgB,EAAE,OAAsB;QACzE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAC1C,MAAM,eAAe,GAAG,KAAK,CAAC,OAAuB,CAAC;QAEtD,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,EAAE,CAAC;YAClC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,6CAA6C;gBACpD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,sBAAsB;aACnC,CAAC;QACJ,CAAC;QAED,gEAAgE;QAChE,MAAM,aAAa,GAAG,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC;QAEpD,eAAM,CAAC,IAAI,CAAC,gCAAgC,QAAQ,gBAAgB,aAAa,IAAI,SAAS,cAAc,WAAW,IAAI,MAAM,GAAG,CAAC,CAAC;QAEtI,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,QAAQ,QAAQ,EAAE,CAAC;YACpC,IAAI,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAEpD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,4CAA4C;gBAC5C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,EAAE;oBAC1E,QAAQ;oBACR,KAAK,EAAE,CAAC,CAAC,uCAAuC;iBACjD,CAAC,CAAC;gBAEH,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;oBAC9B,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,+BAA+B,gBAAgB,CAAC,KAAK,EAAE;wBAC9D,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU,EAAE,sBAAsB;qBACnC,CAAC;gBACJ,CAAC;gBAED,UAAU,GAAG,gBAAgB,CAAC,MAAa,CAAC;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACjD,CAAC;YAED,6CAA6C;YAC7C,MAAM,CAAC,cAAc,EAAE,eAAe,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC1D,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC;gBACjF,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,uCAAuC;aAC1F,CAAC,CAAC;YAEH,yBAAyB;YACzB,MAAM,cAAc,GAAmB;gBACrC,kBAAkB,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC;gBAC7C,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,cAAc,CAAC;gBAChE,eAAe;gBACf,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,aAAa,IAAI,EAAE,CAAC;gBACrE,0BAA0B,EAAE,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,aAAa,IAAI,EAAE,CAAC;aACxF,CAAC;YAEF,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC3B,WAAW,EAAE,QAAQ;gBACrB,cAAc,EAAE,eAAe;gBAC/B,cAAc;gBACd,cAAc;gBACd,iBAAiB,EAAE,EAAE;aACtB,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,WAAW;gBACnB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,sBAAsB;gBAClC,QAAQ,EAAE;oBACR,QAAQ;oBACR,kBAAkB,EAAE,cAAc,CAAC,kBAAkB;oBACrD,SAAS,EAAE,cAAc,CAAC,SAAS;oBACnC,oBAAoB,EAAE,eAAe,CAAC,MAAM;oBAC5C,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,KAAK;iBACnC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,mCAAmC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAClG,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,sBAAsB;aACnC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,KAAgB,EAAE,OAAsB;QACvE,MAAM,WAAW,GAAG,KAAK,CAAC,WAA0B,CAAC;QACrD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAiB,IAAI,KAAK,CAAC;QAEhD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wCAAwC;gBAC/C,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,oBAAoB;aACjC,CAAC;QACJ,CAAC;QAED,sCAAsC;QACtC,MAAM,aAAa,GAAG,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;QAC7E,eAAM,CAAC,IAAI,CAAC,0BAA0B,WAAW,CAAC,EAAE,aAAa,MAAM,gBAAgB,aAAa,GAAG,CAAC,CAAC;QAEzG,MAAM,OAAO,GAAkE,EAAE,CAAC;QAElF,IAAI,CAAC;YACH,uCAAuC;YACvC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,cAAc,CAAC,CAAC;YACxE,KAAK,MAAM,aAAa,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;gBACvD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;YACpE,CAAC;YAED,qCAAqC;YACrC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,8CAA8C;gBAC9C,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;gBACpF,MAAM,cAAc,GAAG,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAE1E,IAAI,CAAC,cAAc,EAAE,CAAC;oBACpB,MAAM,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACtE,OAAO,CAAC,IAAI,CAAC;wBACX,QAAQ,EAAE,WAAW,CAAC,WAAW;wBACjC,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,UAAU,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,KAAK;qBACzF,CAAC,CAAC;oBACH,yCAAyC;oBACzC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,OAAO;wBACf,KAAK,EAAE,6BAA6B,UAAU,EAAE,KAAK,EAAE;wBACvD,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,UAAU,EAAE,oBAAoB;qBACjC,CAAC;gBACJ,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,WAAW,CAAC,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,iCAAiC;YACjC,KAAK,MAAM,aAAa,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;gBACvD,IAAI,aAAa,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;oBACnC,eAAM,CAAC,IAAI,CAAC,oCAAoC,aAAa,CAAC,QAAQ,iBAAiB,aAAa,CAAC,UAAU,GAAG,CAAC,CAAC;oBACpH,SAAS;gBACX,CAAC;gBAED,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,IAAI,mBAAmB,GAAG,IAAI,CAAC;oBAC/B,KAAK,MAAM,MAAM,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;wBAC3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;wBACxD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;4BACxB,OAAO,CAAC,IAAI,CAAC;gCACX,QAAQ,EAAE,aAAa,CAAC,QAAQ;gCAChC,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,UAAU,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK;6BACvF,CAAC,CAAC;4BACH,mBAAmB,GAAG,KAAK,CAAC;4BAC5B,MAAM;wBACR,CAAC;oBACH,CAAC;oBACD,IAAI,mBAAmB,EAAE,CAAC;wBACxB,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;oBACpE,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,0BAA0B;oBAC1B,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;YAED,sCAAsC;YACtC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBACrE,WAAW,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;YACpD,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,WAAW;oBACX,gBAAgB,EAAE,OAAO;oBACzB,OAAO,EAAE;wBACP,UAAU,EAAE,OAAO,CAAC,MAAM;wBAC1B,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;wBACtD,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;wBACnD,MAAM;qBACP;iBACF;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,oBAAoB;gBAChC,QAAQ,EAAE;oBACR,aAAa,EAAE,WAAW,CAAC,EAAE;oBAC7B,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;oBACpD,MAAM;iBACP;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACjG,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,oBAAoB;aACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB,CAAC,KAAgB,EAAE,OAAsB;QAC1E,kDAAkD;QAClD,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,GAAG,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5E,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,KAAgB,EAAE,OAAsB;QACxE,MAAM,WAAW,GAAG,KAAK,CAAC,WAA0B,CAAC;QAErD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wCAAwC;gBAC/C,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,qBAAqB;aAClC,CAAC;QACJ,CAAC;QAED,qCAAqC;QACrC,MAAM,aAAa,GAAG,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;QAC7E,eAAM,CAAC,IAAI,CAAC,2BAA2B,WAAW,CAAC,EAAE,gBAAgB,aAAa,GAAG,CAAC,CAAC;QAEvF,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAErE,MAAM,cAAc,GAAG,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxE,MAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAE7F,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE;gBACN,cAAc;gBACd,iBAAiB;gBACjB,OAAO,EAAE;oBACP,UAAU,EAAE,iBAAiB,CAAC,MAAM;oBACpC,WAAW,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM;oBAC3D,WAAW,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM;oBAC5D,WAAW;iBACZ;aACF;YACD,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,UAAU,EAAE,qBAAqB;YACjC,QAAQ,EAAE;gBACR,aAAa,EAAE,WAAW,CAAC,EAAE;gBAC7B,cAAc;gBACd,WAAW;aACZ;SACF,CAAC;IACJ,CAAC;IAED,iBAAiB;IACT,KAAK,CAAC,uBAAuB,CACnC,WAAmB,EACnB,cAA4B,EAC5B,aAAuB;QAEvB,MAAM,cAAc,GAAoB,EAAE,CAAC;QAE3C,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;YACzC,IAAI,YAAY,KAAK,WAAW;gBAAE,SAAS;YAE3C,yDAAyD;YACzD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;YAEnG,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,cAAc,CAAC,IAAI,CAAC;oBAClB,QAAQ,EAAE,YAAY;oBACtB,OAAO,EAAE,aAAa;oBACtB,MAAM,EAAE,2BAA2B,WAAW,EAAE;oBAChD,UAAU,EAAE,GAAG,EAAE,wCAAwC;oBACzD,YAAY,EAAE,CAAC,WAAW,CAAC;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,WAAmB,EAAE,cAA4B,EAAE,aAAqB;QAC3G,4EAA4E;QAC5E,MAAM,aAAa,GAAiB,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,mDAAmD;YACnD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,EAAE;gBACxE,QAAQ,EAAE,aAAa;aACxB,CAAC,CAAC;YAEH,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,sEAAsE;gBACtE,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;oBAC3C,qEAAqE;oBACrE,MAAM,gBAAgB,GAAG,cAAc,CAAC,MAAM,EAAE,OAAO,IAAI,EAAE,CAAC;oBAC9D,MAAM,eAAe,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;oBAErF,IAAI,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC;wBAC5C,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBAEzC,sEAAsE;wBACtE,IAAI,aAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;4BACpC,6CAA6C;4BAC7C,aAAa,CAAC,IAAI,CAAC;gCACjB,IAAI,EAAE,SAAS;gCACf,QAAQ,EAAE;oCACR,QAAQ,EAAE,aAAa;oCACvB,SAAS,EAAE,CAAC;oCACZ,OAAO,EAAE,CAAC;iCACX;gCACD,OAAO,EAAE,kCAAkC,eAAe,EAAE;gCAC5D,MAAM,EAAE,qCAAqC,WAAW,EAAE;6BAC3D,CAAC,CAAC;wBACL,CAAC;6BAAM,IAAI,aAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;4BAC3C,2CAA2C;4BAC3C,aAAa,CAAC,IAAI,CAAC;gCACjB,IAAI,EAAE,SAAS;gCACf,QAAQ,EAAE;oCACR,QAAQ,EAAE,aAAa;oCACvB,SAAS,EAAE,CAAC;oCACZ,OAAO,EAAE,CAAC;iCACX;gCACD,OAAO,EAAE,8CAA8C,eAAe,EAAE;gCACxE,MAAM,EAAE,yCAAyC,WAAW,EAAE;6BAC/D,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,0CAA0C,aAAa,KAAK,KAAK,EAAE,CAAC,CAAC;QACnF,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,cAA4B,EAAE,cAA+B;QACjG,mEAAmE;QACnE,MAAM,eAAe,GAAqB,EAAE,CAAC;QAE7C,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;YACpC,yDAAyD;YACzD,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,mBAAmB,EAAE;oBAChF,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ;oBAClC,OAAO,EAAE,MAAM,CAAC,OAAO;iBACxB,CAAC,CAAC;gBAEH,IAAI,QAAQ,GAAmC,OAAO,CAAC;gBAEvD,IAAI,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;oBACxD,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAAa,CAAC;oBAClD,yDAAyD;oBACzD,IAAI,UAAU,CAAC,oBAAoB,GAAG,EAAE,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACrE,QAAQ,GAAG,UAAU,CAAC;oBACxB,CAAC;yBAAM,IAAI,UAAU,CAAC,oBAAoB,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC3E,QAAQ,GAAG,OAAO,CAAC;oBACrB,CAAC;gBACH,CAAC;gBAED,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACzD,eAAe,CAAC,IAAI,CAAC;wBACnB,IAAI,EAAE,WAAW;wBACjB,WAAW,EAAE,GAAG,MAAM,CAAC,IAAI,iEAAiE;wBAC5F,aAAa,EAAE,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;wBACxD,QAAQ;qBACT,CAAC,CAAC;gBACL,CAAC;gBAED,8BAA8B;gBAC9B,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC9E,eAAe,CAAC,IAAI,CAAC;wBACnB,IAAI,EAAE,WAAW;wBACjB,WAAW,EAAE,sCAAsC;wBACnD,aAAa,EAAE,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;wBACxD,QAAQ,EAAE,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;qBACpD,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,uDAAuD,KAAK,EAAE,CAAC,CAAC;gBAC5E,8BAA8B;gBAC9B,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACzD,eAAe,CAAC,IAAI,CAAC;wBACnB,IAAI,EAAE,WAAW;wBACjB,WAAW,EAAE,GAAG,MAAM,CAAC,IAAI,qCAAqC;wBAChE,aAAa,EAAE,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;wBACxD,QAAQ,EAAE,OAAO;qBAClB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,eAAe,CAAC,eAAiC,EAAE,cAA+B;QACxF,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,MAAM,CAAC;QAC9C,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,QAAQ,CAAC;QAC/C,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,aAAuB;QAC3C,OAAO,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACjC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CACzG,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,aAAuB;QACpD,OAAO,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACjC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CACxE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAAkB;QAChD,oDAAoD;QACpD,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,cAAc,EAAE;YAC1D,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ;YAClC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS;YACpC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO;YAChC,UAAU,EAAE,MAAM,CAAC,OAAO;SAC3B,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,WAAwB;QACvD,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,wBAAwB;QACxB,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC;QAE/D,2BAA2B;QAC3B,KAAK,MAAM,aAAa,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,0DAA0D;QAC1D,4CAA4C;QAC5C,OAAO;YACL,QAAQ;YACR,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;SAChB,CAAC;IACJ,CAAC;IAED,0CAA0C;IAClC,KAAK,CAAC,iBAAiB,CAAC,KAAgB,EAAE,OAAsB;QACtE,MAAM,aAAa,GAAG,KAAK,CAAC,aAAuB,CAAC;QAEpD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kDAAkD;gBACzD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,mBAAmB;aAChC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,6CAA6C;YAC7C,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAC1D,IAAI,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;oBACrC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,wCAAwC;YACxC,sDAAsD;YACtD,2CAA2C;YAC3C,mCAAmC;YAEnC,eAAM,CAAC,IAAI,CAAC,yBAAyB,aAAa,EAAE,CAAC,CAAC;YAEtD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,gBAAgB,aAAa,wBAAwB;gBAC7D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,mBAAmB;gBAC/B,QAAQ,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE;aACtE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACjG,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,mBAAmB;aAChC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,KAAgB,EAAE,OAAsB;QACzE,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAkB,CAAC;QAE1C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,mDAAmD;gBAC1D,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,sBAAsB;aACnC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,yDAAyD;YACzD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBAChF,QAAQ;aACT,CAAC,CAAC;YAEH,MAAM,aAAa,GAAa,EAAE,CAAC;YAEnC,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,wDAAwD;gBACxD,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE,CAAC;gBAE/D,kCAAkC;gBAClC,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACrE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,aAAa,CAAC,IAAI,CAAC,SAAS,YAAY,CAAC,MAAM,gDAAgD,CAAC,CAAC;gBACnG,CAAC;gBAED,gCAAgC;gBAChC,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC1B,aAAa,CAAC,IAAI,CAAC,SAAS,UAAU,CAAC,MAAM,0CAA0C,CAAC,CAAC;gBAC3F,CAAC;gBAED,mCAAmC;gBACnC,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACvE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,aAAa,CAAC,IAAI,CAAC,SAAS,aAAa,CAAC,MAAM,kDAAkD,CAAC,CAAC;gBACtG,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,QAAQ;oBACR,aAAa;oBACb,OAAO,EAAE,6BAA6B,QAAQ,WAAW,aAAa,CAAC,MAAM,8BAA8B;oBAC3G,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW;iBACzC;gBACD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,sBAAsB;gBAClC,QAAQ,EAAE,EAAE,QAAQ,EAAE,iBAAiB,EAAE,aAAa,CAAC,MAAM,EAAE;aAChE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACnG,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,sBAAsB;aACnC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,mBAAmB,CAAC,GAAW;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEzB,mCAAmC;QACnC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACrD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IAC1C,CAAC;IAEO,mBAAmB,CAAC,GAAW,EAAE,IAAS;QAChD,6BAA6B;QAC7B,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC,mBAAmB;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YAC1D,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5B,IAAI;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,KAAmB;QACjD,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,kDAAkD;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;YACpD,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YAEtE,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;gBACrD,kCAAkC;YACpC,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA/qBD,gDA+qBC", "sourcesContent": ["/**\n * Cascade Editing Tool - Intelligent dependency-aware editing\n * \n * This tool provides intelligent editing capabilities that automatically\n * propagate changes across dependent files and maintain code consistency.\n */\n\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { DependencyAnalysisTool } from './dependencyAnalysisTool';\nimport { CodeComplexityTool } from './codeAnalysisTool';\nimport { PrecisionEditTool } from './advancedCodeEditingTool';\nimport { logger } from '../logger';\n\nexport interface CascadeEdit {\n  id: string;\n  primaryFile: string;\n  primaryChanges: EditChange[];\n  dependentEdits: DependentEdit[];\n  impactAnalysis: ImpactAnalysis;\n  validationResults: ValidationResult[];\n}\n\nexport interface EditChange {\n  type: 'insert' | 'replace' | 'delete' | 'rename';\n  location: EditLocation;\n  content: string;\n  reason: string;\n}\n\nexport interface EditLocation {\n  filePath: string;\n  startLine: number;\n  endLine: number;\n  startColumn?: number;\n  endColumn?: number;\n}\n\nexport interface DependentEdit {\n  filePath: string;\n  changes: EditChange[];\n  reason: string;\n  confidence: number;\n  dependencies: string[];\n}\n\nexport interface ImpactAnalysis {\n  totalFilesAffected: number;\n  riskLevel: 'low' | 'medium' | 'high';\n  breakingChanges: BreakingChange[];\n  testFilesAffected: string[];\n  documentationUpdatesNeeded: string[];\n}\n\nexport interface BreakingChange {\n  type: 'signature' | 'interface' | 'behavior' | 'dependency';\n  description: string;\n  affectedFiles: string[];\n  severity: 'minor' | 'major' | 'critical';\n}\n\nexport interface ValidationResult {\n  filePath: string;\n  passed: boolean;\n  issues: string[];\n  suggestions: string[];\n}\n\nexport class CascadeEditingTool implements ITool {\n  readonly id = 'cascade-edit';\n  readonly name = 'Cascade Editing Tool';\n  readonly description = 'Intelligent dependency-aware editing with automatic propagation';\n  readonly type = 'multi-action' as const;\n  readonly category = 'Editing';\n\n  private dependencyTool: DependencyAnalysisTool;\n  private codeAnalysisTool: CodeComplexityTool;\n  private precisionEditTool: PrecisionEditTool;\n\n  // Performance optimization: Cache and batch processing\n  private dependencyCache: Map<string, any> = new Map();\n  private editQueue: Map<string, EditChange[]> = new Map();\n  private readonly cacheExpiry = 10 * 60 * 1000; // 10 minutes for dependency cache\n  private readonly maxBatchSize = 10;\n\n  constructor() {\n    this.dependencyTool = new DependencyAnalysisTool();\n    this.codeAnalysisTool = new CodeComplexityTool();\n    this.precisionEditTool = new PrecisionEditTool();\n  }\n\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    try {\n      switch (actionName) {\n        case 'analyzeCascadeImpact':\n          return await this.analyzeCascadeImpact(input, context);\n        case 'executeCascadeEdit':\n          return await this.executeCascadeEdit(input, context);\n        case 'previewCascadeChanges':\n          return await this.previewCascadeChanges(input, context);\n        case 'validateCascadeEdit':\n          return await this.validateCascadeEdit(input, context);\n        case 'revertCascadeEdit':\n          return await this.revertCascadeEdit(input, context);\n        case 'optimizeDependencies':\n          return await this.optimizeDependencies(input, context);\n        default:\n          return {\n            success: false,\n            error: `Unknown action: ${actionName}`,\n            toolId: this.id,\n            actionName\n          };\n      }\n    } catch (error) {\n      logger.error(`Cascade editing error: ${error}`);\n      return {\n        success: false,\n        error: `Cascade editing failed: ${error instanceof Error ? error.message : String(error)}`,\n        toolId: this.id,\n        actionName\n      };\n    }\n  }\n\n  /**\n     * Analyze the cascade impact of proposed changes (Optimized)\n     */\n  private async analyzeCascadeImpact(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n    const proposedChanges = input.changes as EditChange[];\n\n    if (!filePath || !proposedChanges) {\n      return {\n        success: false,\n        error: 'File path and proposed changes are required',\n        toolId: this.id,\n        actionName: 'analyzeCascadeImpact'\n      };\n    }\n\n    // Use context for additional workspace information if available\n    const workspaceRoot = context?.workspace?.workspaceFolders?.[0];\n    const currentFile = context?.workspace?.currentFile;\n\n    logger.info(`Analyzing cascade impact for ${filePath} (workspace: ${workspaceRoot || 'unknown'}, current: ${currentFile || 'none'})`);\n\n    try {\n      // Check cache for recent dependency analysis\n      const cacheKey = `deps_${filePath}`;\n      let impactData = this.getCachedDependency(cacheKey);\n\n      if (!impactData) {\n        // Analyze dependencies with optimized depth\n        const dependencyResult = await this.dependencyTool.execute('analyzeImpact', {\n          filePath,\n          depth: 2 // Reduced depth for better performance\n        });\n\n        if (!dependencyResult.success) {\n          return {\n            success: false,\n            error: `Dependency analysis failed: ${dependencyResult.error}`,\n            toolId: this.id,\n            actionName: 'analyzeCascadeImpact'\n          };\n        }\n\n        impactData = dependencyResult.output as any;\n        this.setCachedDependency(cacheKey, impactData);\n      }\n\n      // Parallel processing for better performance\n      const [dependentEdits, breakingChanges] = await Promise.all([\n        this.analyzeDependentChanges(filePath, proposedChanges, impactData.impactedFiles),\n        this.identifyBreakingChanges(proposedChanges, []) // Pass empty array initially for speed\n      ]);\n\n      // Create impact analysis\n      const impactAnalysis: ImpactAnalysis = {\n        totalFilesAffected: dependentEdits.length + 1,\n        riskLevel: this.assessRiskLevel(breakingChanges, dependentEdits),\n        breakingChanges,\n        testFilesAffected: this.findTestFiles(impactData.impactedFiles || []),\n        documentationUpdatesNeeded: this.findDocumentationFiles(impactData.impactedFiles || [])\n      };\n\n      const cascadeEdit: CascadeEdit = {\n        id: `cascade_${Date.now()}`,\n        primaryFile: filePath,\n        primaryChanges: proposedChanges,\n        dependentEdits,\n        impactAnalysis,\n        validationResults: []\n      };\n\n      return {\n        success: true,\n        output: cascadeEdit,\n        toolId: this.id,\n        actionName: 'analyzeCascadeImpact',\n        metadata: {\n          filePath,\n          totalFilesAffected: impactAnalysis.totalFilesAffected,\n          riskLevel: impactAnalysis.riskLevel,\n          breakingChangesCount: breakingChanges.length,\n          cached: impactData.cached || false\n        }\n      };\n    } catch (error) {\n      logger.error(`Cascade impact analysis failed for ${filePath}:`, error);\n      return {\n        success: false,\n        error: `Cascade impact analysis failed: ${error instanceof Error ? error.message : String(error)}`,\n        toolId: this.id,\n        actionName: 'analyzeCascadeImpact'\n      };\n    }\n  }\n\n  /**\n     * Execute cascade edit with dependency propagation\n     */\n  private async executeCascadeEdit(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    const cascadeEdit = input.cascadeEdit as CascadeEdit;\n    const dryRun = input.dryRun as boolean || false;\n\n    if (!cascadeEdit) {\n      return {\n        success: false,\n        error: 'Cascade edit configuration is required',\n        toolId: this.id,\n        actionName: 'executeCascadeEdit'\n      };\n    }\n\n    // Log execution context for debugging\n    const workspaceInfo = context?.workspace?.workspaceFolders?.[0] || 'unknown';\n    logger.info(`Executing cascade edit ${cascadeEdit.id} (dryRun: ${dryRun}, workspace: ${workspaceInfo})`);\n\n    const results: Array<{ filePath: string, success: boolean, error?: string }> = [];\n\n    try {\n      // Queue all edits for batch processing\n      this.editQueue.set(cascadeEdit.primaryFile, cascadeEdit.primaryChanges);\n      for (const dependentEdit of cascadeEdit.dependentEdits) {\n        this.editQueue.set(dependentEdit.filePath, dependentEdit.changes);\n      }\n\n      // Execute primary file changes first\n      if (!dryRun) {\n        // Use batch processing for better performance\n        const primaryEditResults = await this.processBatchEdits(cascadeEdit.primaryChanges);\n        const primarySuccess = primaryEditResults.every(result => result.success);\n\n        if (!primarySuccess) {\n          const failedEdit = primaryEditResults.find(result => !result.success);\n          results.push({\n            filePath: cascadeEdit.primaryFile,\n            success: false,\n            error: failedEdit?.error instanceof Error ? failedEdit.error.message : failedEdit?.error\n          });\n          // Stop execution on primary file failure\n          return {\n            success: false,\n            output: results,\n            error: `Primary file edit failed: ${failedEdit?.error}`,\n            toolId: this.id,\n            actionName: 'executeCascadeEdit'\n          };\n        }\n        results.push({ filePath: cascadeEdit.primaryFile, success: true });\n      }\n\n      // Execute dependent file changes\n      for (const dependentEdit of cascadeEdit.dependentEdits) {\n        if (dependentEdit.confidence < 0.7) {\n          logger.warn(`Skipping low-confidence edit for ${dependentEdit.filePath} (confidence: ${dependentEdit.confidence})`);\n          continue;\n        }\n\n        if (!dryRun) {\n          let allChangesSucceeded = true;\n          for (const change of dependentEdit.changes) {\n            const editResult = await this.executeEditChange(change);\n            if (!editResult.success) {\n              results.push({\n                filePath: dependentEdit.filePath,\n                success: false,\n                error: editResult.error instanceof Error ? editResult.error.message : editResult.error\n              });\n              allChangesSucceeded = false;\n              break;\n            }\n          }\n          if (allChangesSucceeded) {\n            results.push({ filePath: dependentEdit.filePath, success: true });\n          }\n        } else {\n          // Dry run - just validate\n          results.push({ filePath: dependentEdit.filePath, success: true });\n        }\n      }\n\n      // Validate all changes if not dry run\n      if (!dryRun) {\n        const validationResults = await this.validateAllChanges(cascadeEdit);\n        cascadeEdit.validationResults = validationResults;\n      }\n\n      return {\n        success: true,\n        output: {\n          cascadeEdit,\n          executionResults: results,\n          summary: {\n            totalFiles: results.length,\n            successfulFiles: results.filter(r => r.success).length,\n            failedFiles: results.filter(r => !r.success).length,\n            dryRun\n          }\n        },\n        toolId: this.id,\n        actionName: 'executeCascadeEdit',\n        metadata: {\n          cascadeEditId: cascadeEdit.id,\n          filesModified: results.filter(r => r.success).length,\n          dryRun\n        }\n      };\n\n    } catch (error) {\n      return {\n        success: false,\n        error: `Cascade edit execution failed: ${error instanceof Error ? error.message : String(error)}`,\n        output: results,\n        toolId: this.id,\n        actionName: 'executeCascadeEdit'\n      };\n    }\n  }\n\n  /**\n     * Preview cascade changes without executing them\n     */\n  private async previewCascadeChanges(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    // Set dry run to true and call executeCascadeEdit\n    return await this.executeCascadeEdit({ ...input, dryRun: true }, context);\n  }\n\n  /**\n     * Validate cascade edit results\n     */\n  private async validateCascadeEdit(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    const cascadeEdit = input.cascadeEdit as CascadeEdit;\n\n    if (!cascadeEdit) {\n      return {\n        success: false,\n        error: 'Cascade edit configuration is required',\n        toolId: this.id,\n        actionName: 'validateCascadeEdit'\n      };\n    }\n\n    // Use context for validation logging\n    const workspaceInfo = context?.workspace?.workspaceFolders?.[0] || 'unknown';\n    logger.info(`Validating cascade edit ${cascadeEdit.id} (workspace: ${workspaceInfo})`);\n\n    const validationResults = await this.validateAllChanges(cascadeEdit);\n\n    const overallSuccess = validationResults.every(result => result.passed);\n    const totalIssues = validationResults.reduce((sum, result) => sum + result.issues.length, 0);\n\n    return {\n      success: true,\n      output: {\n        overallSuccess,\n        validationResults,\n        summary: {\n          totalFiles: validationResults.length,\n          passedFiles: validationResults.filter(r => r.passed).length,\n          failedFiles: validationResults.filter(r => !r.passed).length,\n          totalIssues\n        }\n      },\n      toolId: this.id,\n      actionName: 'validateCascadeEdit',\n      metadata: {\n        cascadeEditId: cascadeEdit.id,\n        overallSuccess,\n        totalIssues\n      }\n    };\n  }\n\n  // Helper methods\n  private async analyzeDependentChanges(\n    primaryFile: string,\n    primaryChanges: EditChange[],\n    impactedFiles: string[]\n  ): Promise<DependentEdit[]> {\n    const dependentEdits: DependentEdit[] = [];\n\n    for (const impactedFile of impactedFiles) {\n      if (impactedFile === primaryFile) continue;\n\n      // Analyze what changes are needed in this dependent file\n      const neededChanges = await this.determineNeededChanges(primaryFile, primaryChanges, impactedFile);\n\n      if (neededChanges.length > 0) {\n        dependentEdits.push({\n          filePath: impactedFile,\n          changes: neededChanges,\n          reason: `Dependent on changes in ${primaryFile}`,\n          confidence: 0.8, // Would be calculated based on analysis\n          dependencies: [primaryFile]\n        });\n      }\n    }\n\n    return dependentEdits;\n  }\n\n  private async determineNeededChanges(primaryFile: string, primaryChanges: EditChange[], dependentFile: string): Promise<EditChange[]> {\n    // Analyze the dependency relationship and determine what changes are needed\n    const neededChanges: EditChange[] = [];\n\n    try {\n      // Use code analysis to understand the relationship\n      const analysisResult = await this.codeAnalysisTool.execute('analyzeFile', {\n        filePath: dependentFile\n      });\n\n      if (analysisResult.success) {\n        // Analyze each primary change to see if it affects the dependent file\n        for (const primaryChange of primaryChanges) {\n          // Check if the dependent file imports or references the primary file\n          const dependentContent = analysisResult.output?.content || '';\n          const primaryFileName = primaryFile.split('/').pop()?.replace(/\\.[^/.]+$/, '') || '';\n\n          if (dependentContent.includes(primaryFileName) ||\n            dependentContent.includes(primaryFile)) {\n\n            // Determine what type of change is needed based on the primary change\n            if (primaryChange.type === 'rename') {\n              // Need to update import/reference statements\n              neededChanges.push({\n                type: 'replace',\n                location: {\n                  filePath: dependentFile,\n                  startLine: 1,\n                  endLine: 1\n                },\n                content: `// Update reference to renamed ${primaryFileName}`,\n                reason: `Reference update due to rename in ${primaryFile}`\n              });\n            } else if (primaryChange.type === 'delete') {\n              // Need to remove or replace the dependency\n              neededChanges.push({\n                type: 'replace',\n                location: {\n                  filePath: dependentFile,\n                  startLine: 1,\n                  endLine: 1\n                },\n                content: `// Remove or replace dependency on deleted ${primaryFileName}`,\n                reason: `Dependency removal due to deletion in ${primaryFile}`\n              });\n            }\n          }\n        }\n      }\n    } catch (error) {\n      logger.warn(`Failed to determine needed changes for ${dependentFile}: ${error}`);\n    }\n\n    return neededChanges;\n  }\n\n  private async identifyBreakingChanges(primaryChanges: EditChange[], dependentEdits: DependentEdit[]): Promise<BreakingChange[]> {\n    // Analyze changes to identify breaking changes using code analysis\n    const breakingChanges: BreakingChange[] = [];\n\n    for (const change of primaryChanges) {\n      // Use code analysis tool to assess complexity and impact\n      try {\n        const complexityResult = await this.codeAnalysisTool.execute('analyzeComplexity', {\n          filePath: change.location.filePath,\n          content: change.content\n        });\n\n        let severity: 'minor' | 'major' | 'critical' = 'minor';\n\n        if (complexityResult.success && complexityResult.output) {\n          const complexity = complexityResult.output as any;\n          // Determine severity based on complexity and change type\n          if (complexity.cyclomaticComplexity > 10 || change.type === 'delete') {\n            severity = 'critical';\n          } else if (complexity.cyclomaticComplexity > 5 || change.type === 'rename') {\n            severity = 'major';\n          }\n        }\n\n        if (change.type === 'delete' || change.type === 'rename') {\n          breakingChanges.push({\n            type: 'signature',\n            description: `${change.type} operation may break dependent code (complexity-based analysis)`,\n            affectedFiles: dependentEdits.map(edit => edit.filePath),\n            severity\n          });\n        }\n\n        // Check for interface changes\n        if (change.content.includes('interface') || change.content.includes('export')) {\n          breakingChanges.push({\n            type: 'interface',\n            description: 'Interface or export changes detected',\n            affectedFiles: dependentEdits.map(edit => edit.filePath),\n            severity: severity === 'minor' ? 'major' : severity\n          });\n        }\n      } catch (error) {\n        logger.warn(`Code analysis failed for breaking change detection: ${error}`);\n        // Fallback to simple analysis\n        if (change.type === 'delete' || change.type === 'rename') {\n          breakingChanges.push({\n            type: 'signature',\n            description: `${change.type} operation may break dependent code`,\n            affectedFiles: dependentEdits.map(edit => edit.filePath),\n            severity: 'major'\n          });\n        }\n      }\n    }\n\n    return breakingChanges;\n  }\n\n  private assessRiskLevel(breakingChanges: BreakingChange[], dependentEdits: DependentEdit[]): 'low' | 'medium' | 'high' {\n    if (breakingChanges.length > 0) return 'high';\n    if (dependentEdits.length > 5) return 'medium';\n    return 'low';\n  }\n\n  private findTestFiles(impactedFiles: string[]): string[] {\n    return impactedFiles.filter(file =>\n      file.includes('test') || file.includes('spec') || file.endsWith('.test.ts') || file.endsWith('.spec.ts')\n    );\n  }\n\n  private findDocumentationFiles(impactedFiles: string[]): string[] {\n    return impactedFiles.filter(file =>\n      file.endsWith('.md') || file.includes('doc') || file.includes('README')\n    );\n  }\n\n  private async executeEditChange(change: EditChange): Promise<ToolResult> {\n    // Use the precision edit tool to execute the change\n    return await this.precisionEditTool.execute('replaceLines', {\n      filePath: change.location.filePath,\n      startLine: change.location.startLine,\n      endLine: change.location.endLine,\n      newContent: change.content\n    });\n  }\n\n  private async validateAllChanges(cascadeEdit: CascadeEdit): Promise<ValidationResult[]> {\n    const results: ValidationResult[] = [];\n\n    // Validate primary file\n    results.push(await this.validateFile(cascadeEdit.primaryFile));\n\n    // Validate dependent files\n    for (const dependentEdit of cascadeEdit.dependentEdits) {\n      results.push(await this.validateFile(dependentEdit.filePath));\n    }\n\n    return results;\n  }\n\n  private async validateFile(filePath: string): Promise<ValidationResult> {\n    // This would perform comprehensive validation of the file\n    // For now, return a basic validation result\n    return {\n      filePath,\n      passed: true,\n      issues: [],\n      suggestions: []\n    };\n  }\n\n  // Additional methods for other actions...\n  private async revertCascadeEdit(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    const cascadeEditId = input.cascadeEditId as string;\n\n    if (!cascadeEditId) {\n      return {\n        success: false,\n        error: 'Cascade edit ID is required for revert operation',\n        toolId: this.id,\n        actionName: 'revertCascadeEdit'\n      };\n    }\n\n    try {\n      // Clear the edit queue for this cascade edit\n      Array.from(this.editQueue.entries()).forEach(([filePath]) => {\n        if (filePath.includes(cascadeEditId)) {\n          this.editQueue.delete(filePath);\n        }\n      });\n\n      // In a full implementation, this would:\n      // 1. Retrieve the original state from version control\n      // 2. Restore files to their previous state\n      // 3. Validate the revert operation\n\n      logger.info(`Reverted cascade edit ${cascadeEditId}`);\n\n      return {\n        success: true,\n        output: `Cascade edit ${cascadeEditId} reverted successfully`,\n        toolId: this.id,\n        actionName: 'revertCascadeEdit',\n        metadata: { cascadeEditId, context: context?.workspace?.currentFile }\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: `Failed to revert cascade edit: ${error instanceof Error ? error.message : String(error)}`,\n        toolId: this.id,\n        actionName: 'revertCascadeEdit'\n      };\n    }\n  }\n\n  private async optimizeDependencies(input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    const filePath = input.filePath as string;\n\n    if (!filePath) {\n      return {\n        success: false,\n        error: 'File path is required for dependency optimization',\n        toolId: this.id,\n        actionName: 'optimizeDependencies'\n      };\n    }\n\n    try {\n      // Use code analysis tool to analyze current dependencies\n      const analysisResult = await this.codeAnalysisTool.execute('analyzeDependencies', {\n        filePath\n      });\n\n      const optimizations: string[] = [];\n\n      if (analysisResult.success) {\n        // Analyze dependency patterns and suggest optimizations\n        const dependencies = analysisResult.output?.dependencies || [];\n\n        // Check for circular dependencies\n        const circularDeps = dependencies.filter((dep: any) => dep.circular);\n        if (circularDeps.length > 0) {\n          optimizations.push(`Found ${circularDeps.length} circular dependencies that should be resolved`);\n        }\n\n        // Check for unused dependencies\n        const unusedDeps = dependencies.filter((dep: any) => !dep.used);\n        if (unusedDeps.length > 0) {\n          optimizations.push(`Found ${unusedDeps.length} unused dependencies that can be removed`);\n        }\n\n        // Check for duplicate dependencies\n        const duplicateDeps = dependencies.filter((dep: any) => dep.duplicate);\n        if (duplicateDeps.length > 0) {\n          optimizations.push(`Found ${duplicateDeps.length} duplicate dependencies that can be consolidated`);\n        }\n      }\n\n      return {\n        success: true,\n        output: {\n          filePath,\n          optimizations,\n          summary: `Analyzed dependencies for ${filePath}. Found ${optimizations.length} optimization opportunities.`,\n          context: context?.workspace?.currentFile\n        },\n        toolId: this.id,\n        actionName: 'optimizeDependencies',\n        metadata: { filePath, optimizationCount: optimizations.length }\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: `Failed to optimize dependencies: ${error instanceof Error ? error.message : String(error)}`,\n        toolId: this.id,\n        actionName: 'optimizeDependencies'\n      };\n    }\n  }\n\n  /**\n     * Performance optimization: Dependency cache management\n     */\n  private getCachedDependency(key: string): any | null {\n    const cached = this.dependencyCache.get(key);\n    if (!cached) return null;\n\n    // Check if cache entry has expired\n    if (Date.now() - cached.timestamp > this.cacheExpiry) {\n      this.dependencyCache.delete(key);\n      return null;\n    }\n\n    return { ...cached.data, cached: true };\n  }\n\n  private setCachedDependency(key: string, data: any): void {\n    // Implement simple LRU cache\n    if (this.dependencyCache.size >= 20) { // Limit cache size\n      const firstKey = this.dependencyCache.keys().next().value;\n      if (firstKey) {\n        this.dependencyCache.delete(firstKey);\n      }\n    }\n\n    this.dependencyCache.set(key, {\n      data,\n      timestamp: Date.now()\n    });\n  }\n\n  /**\n     * Performance optimization: Batch edit processing\n     */\n  private async processBatchEdits(edits: EditChange[]): Promise<ToolResult[]> {\n    const results: ToolResult[] = [];\n\n    // Process edits in batches for better performance\n    for (let i = 0; i < edits.length; i += this.maxBatchSize) {\n      const batch = edits.slice(i, i + this.maxBatchSize);\n      const batchPromises = batch.map(edit => this.executeEditChange(edit));\n\n      try {\n        const batchResults = await Promise.all(batchPromises);\n        results.push(...batchResults);\n      } catch (error) {\n        logger.error('Batch edit processing failed:', error);\n        // Continue with remaining batches\n      }\n    }\n\n    return results;\n  }\n}\n"]}