:root {
    --container-padding: 20px;
    --input-padding-vertical: 6px;
    --input-padding-horizontal: 12px;
    --input-margin-vertical: 4px;
    --input-margin-horizontal: 0;
}

body {
    padding: 0;
    margin: 0;
    color: var(--vscode-foreground);
    font-size: var(--vscode-font-size);
    font-weight: var(--vscode-font-weight);
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: var(--container-padding);
    box-sizing: border-box;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.header h1 {
    margin: 0;
    font-size: 1.5em;
}

.actions {
    display: flex;
    gap: 8px;
}

button {
    padding: var(--input-padding-vertical) var(--input-padding-horizontal);
    border: none;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    cursor: pointer;
    border-radius: 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

button:active {
    background-color: var(--vscode-button-background);
    opacity: 0.8;
}

.search-container {
    display: flex;
    margin-bottom: 20px;
}

.search-container input {
    flex: 1;
    padding: var(--input-padding-vertical) var(--input-padding-horizontal);
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    margin-right: 8px;
}

.content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.memories-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid var(--vscode-panel-border);
    margin-right: 10px;
}

.memory-detail {
    flex: 2;
    overflow-y: auto;
    border: 1px solid var(--vscode-panel-border);
    padding: 10px;
}

.memory-item {
    padding: 10px;
    border-bottom: 1px solid var(--vscode-panel-border);
    cursor: pointer;
}

.memory-item:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.memory-item.selected {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
}

.memory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.memory-type {
    font-size: 0.8em;
    padding: 2px 6px;
    border-radius: 4px;
    background-color: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
}

.memory-type.conversation {
    background-color: #4caf50;
}

.memory-type.semantic {
    background-color: #2196f3;
}

.memory-type.project {
    background-color: #ff9800;
}

.memory-type.user_preference {
    background-color: #9c27b0;
}

.memory-type.code {
    background-color: #f44336;
}

.memory-type.file {
    background-color: #795548;
}

.memory-source {
    font-size: 0.8em;
    color: var(--vscode-descriptionForeground);
}

.memory-date {
    font-size: 0.8em;
    color: var(--vscode-descriptionForeground);
}

.memory-preview {
    font-size: 0.9em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.memory-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.memory-detail-id {
    font-family: var(--vscode-editor-font-family);
    font-size: 0.8em;
    color: var(--vscode-descriptionForeground);
}

.memory-detail-date {
    font-size: 0.9em;
}

.memory-detail-content {
    margin-bottom: 20px;
    padding: 10px;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;
    overflow-x: auto;
}

.memory-detail-content pre {
    margin: 0;
    white-space: pre-wrap;
    font-family: var(--vscode-editor-font-family);
}

.memory-detail-metadata h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.metadata-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.metadata-item {
    display: flex;
    align-items: flex-start;
}

.metadata-key {
    min-width: 120px;
    font-weight: bold;
    color: var(--vscode-editor-foreground);
}

.metadata-value {
    flex: 1;
}

.metadata-value.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.tag {
    padding: 2px 6px;
    border-radius: 4px;
    background-color: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    font-size: 0.8em;
}

.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: var(--vscode-editor-background);
    margin: 10% auto;
    padding: 20px;
    border: 1px solid var(--vscode-panel-border);
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.3em;
}

.close {
    color: var(--vscode-descriptionForeground);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: var(--vscode-foreground);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid var(--vscode-panel-border);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="password"],
.form-group select {
    width: 100%;
    padding: var(--input-padding-vertical) var(--input-padding-horizontal);
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
}

.form-group input[type="checkbox"] {
    margin-right: 5px;
}

.nested-settings {
    margin-left: 20px;
    padding-left: 10px;
    border-left: 2px solid var(--vscode-panel-border);
    margin-bottom: 20px;
}

.hidden {
    display: none;
}
