{"version": 3, "file": "deepseekProvider.js", "sourceRoot": "", "sources": ["../../../src/llm/providers/deepseekProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uDAAoD;AAIpD,yCAAsC;AAEtC,mDAAmD;AACnD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B;;GAEG;AACH,MAAa,gBAAiB,SAAQ,iCAAe;IAC1C,UAAU,GAAG,UAAU,CAAC;IACxB,WAAW,GAAG,UAAU,CAAC;IACzB,WAAW,GAAG,oDAAoD,CAAC;IACnE,OAAO,GAAG,qBAAqB,CAAC;IAChC,cAAc,GAAG,IAAI,CAAC;IACtB,6BAA6B,GAAG,IAAI,CAAC;IACrC,eAAe,GAAG,6BAA6B,CAAC;IAChD,YAAY,GAAG,gBAAgB,CAAC;IAEjC,MAAM,GAAQ,IAAI,CAAC;IACnB,OAAO,CAAS;IAExB,YAAY,OAAiC;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;QAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAC;oBAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,gBAAgB;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;gBAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAC/C,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,KAAK,CAAC,qBAAqB;aACrC,CAAC,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,8CAA8C,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAE3E,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,YAAY;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC/C,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,kBAA6C,EAC7C,KAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,iCAAiC,EAAE,CAAC;QACnE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAEhF,mBAAmB;YACnB,MAAM,QAAQ,GAAmF,EAAE,CAAC;YAEpG,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACxB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,MAAM,CAAC,YAAY;qBACpB,CAAC,CAAC;gBACd,CAAC;gBACD,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,MAAM;iBACd,CAAC,CAAC;YACd,CAAC;YAED,2BAA2B;YAC3B,IAAI,SAAS,GAA8E,SAAS,CAAC;YACrG,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC5B,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAClD,IAAI,EAAE,IAAI,CAAC,EAAE;oBACb,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,UAAU,EAAG,IAAY,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE;iBAC1F,CAAC,CAAC,CAAC;YACN,CAAC;YAED,IAAI,UAAU,GAAuB,SAAS,CAAC;YAC/C,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,UAAU,GAAG,MAAM,CAAC;YACtB,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAC9D,KAAK,EAAE,OAAO;gBACd,QAAQ;gBACR,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS;gBAC5B,SAAS;gBACT,aAAa,EAAE,UAAU;aAC1B,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;YAEjE,oBAAoB;YACpB,IAAI,eAAe,GAAkC,SAAS,CAAC;YAC/D,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;gBACrD,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;gBACpE,eAAe,GAAG;oBAChB,IAAI,EAAE,YAAY,CAAC,IAAI;oBACvB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC;iBAC9C,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,MAAM;gBAC/D,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI;oBAC5B,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,CAAC;oBACnB,WAAW,EAAE,CAAC;iBACf;gBACD,QAAQ,EAAE,eAAe;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAChG,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;YACpE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAElD,2BAA2B;YAC3B,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC;YACzC,eAAM,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;YAEvE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;gBAC7B,EAAE,EAAE,CAAC,CAAC,EAAE;gBACR,IAAI,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;gBACpB,WAAW,EAAE,CAAC,CAAC,WAAW,IAAI,EAAE;gBAChC,aAAa,EAAE,CAAC,CAAC,cAAc,IAAI,IAAI;gBACvC,eAAe,EAAE,CAAC,CAAC,iBAAiB;gBACpC,iBAAiB,EAAE,CAAC,CAAC,kBAAkB,IAAI,KAAK;gBAChD,cAAc,EAAE,CAAC,CAAC,eAAe,IAAI,KAAK;aAC3C,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAe,EAAE,CAAe,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAExD,2BAA2B;YAC3B,OAAO;gBACL,wBAAwB;gBACxB;oBACE,EAAE,EAAE,gBAAgB;oBACpB,IAAI,EAAE,gBAAgB;oBACtB,WAAW,EAAE,6DAA6D;oBAC1E,aAAa,EAAE,KAAK;oBACpB,WAAW,EAAE,mBAAmB;iBACjC;gBACD;oBACE,EAAE,EAAE,qBAAqB;oBACzB,IAAI,EAAE,qBAAqB;oBAC3B,WAAW,EAAE,gDAAgD;oBAC7D,aAAa,EAAE,KAAK;oBACpB,WAAW,EAAE,qBAAqB;iBACnC;gBACD;oBACE,EAAE,EAAE,oBAAoB;oBACxB,IAAI,EAAE,oBAAoB;oBAC1B,WAAW,EAAE,8CAA8C;oBAC3D,aAAa,EAAE,KAAK;oBACpB,WAAW,EAAE,qBAAqB;iBACnC;gBACD;oBACE,EAAE,EAAE,yBAAyB;oBAC7B,IAAI,EAAE,yBAAyB;oBAC/B,WAAW,EAAE,wCAAwC;oBACrD,aAAa,EAAE,KAAK;oBACpB,WAAW,EAAE,mBAAmB;iBACjC;gBACD,uBAAuB;gBACvB;oBACE,EAAE,EAAE,eAAe;oBACnB,IAAI,EAAE,eAAe;oBACrB,WAAW,EAAE,qCAAqC;oBAClD,aAAa,EAAE,IAAI;oBACnB,WAAW,EAAE,mBAAmB;iBACjC;gBACD;oBACE,EAAE,EAAE,uBAAuB;oBAC3B,IAAI,EAAE,uBAAuB;oBAC7B,WAAW,EAAE,4CAA4C;oBACzD,aAAa,EAAE,IAAI;oBACnB,WAAW,EAAE,mBAAmB;iBACjC;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6DAA6D;aACvE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAEjC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,yCAAyC;aACnD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,sCAAsC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC1G,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,OAAY;QACpC,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;SAEK;IACE,sBAAsB;QAC3B,OAAO;YACL;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,uBAAuB;gBACpC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,kEAAkE;gBAC/E,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,0BAA0B;gBACvC,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE;oBACP,gBAAgB;oBAChB,qBAAqB;oBACrB,oBAAoB;oBACpB,yBAAyB;oBACzB,eAAe;oBACf,uBAAuB;iBACxB;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAnTD,4CAmTC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { BaseLL<PERSON>rovider } from './baseLLMProvider';\nimport { LLMGenerateParams, LLMGenerateResult } from '../types';\nimport { LLMModelInfo } from '../llmProvider';\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { logger } from '../../logger';\n\n// Use require for axios to avoid TypeScript issues\nconst axios = require('axios');\n\n/**\n * Provider for DeepSeek API\n */\nexport class DeepSeekProvider extends BaseLLMProvider {\n  readonly providerId = 'deepseek';\n  readonly displayName = 'DeepSeek';\n  readonly description = 'Access DeepSeek AI models including DeepSeek-Coder';\n  readonly website = 'https://deepseek.ai';\n  readonly requiresApiKey = true;\n  readonly supportsEndpointConfiguration = true;\n  readonly defaultEndpoint = 'https://api.deepseek.com/v1';\n  readonly defaultModel = 'deepseek-coder';\n\n  private client: any = null;\n  private baseUrl: string;\n\n  constructor(context?: vscode.ExtensionContext) {\n    super(context);\n    this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n    this.initializeClient();\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.llm.providers')) {\n        logger.info('DeepSeek configuration changed, re-initializing client.');\n        this.loadConfig().then(() => {\n          this.baseUrl = this.config.apiEndpoint || this.defaultEndpoint;\n          this.initializeClient();\n        });\n      }\n    });\n  }\n\n  /**\n     * Initialize the Axios client for API requests\n     */\n  private initializeClient(): void {\n    try {\n      if (!this.config.apiKey) {\n        logger.warn('DeepSeek API key not configured');\n        this.client = null;\n        return;\n      }\n\n      this.client = axios.create({\n        baseURL: this.baseUrl,\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        timeout: 60000 // 60 seconds timeout\n      });\n\n      logger.debug(`DeepSeek client initialized with endpoint: ${this.baseUrl}`);\n\n      logger.info('DeepSeek client initialized');\n    } catch (error) {\n      logger.error('Failed to initialize DeepSeek client:', error);\n      this.client = null;\n    }\n  }\n\n  /**\n     * Check if the provider is configured\n     */\n  public isConfigured(): boolean {\n    return !!this.client && !!this.config.apiKey;\n  }\n\n  /**\n     * Generate text using DeepSeek\n     */\n  public async generate(\n    params: LLMGenerateParams,\n    _cancellationToken?: vscode.CancellationToken,\n    tools?: Map<string, ITool>\n  ): Promise<LLMGenerateResult> {\n    if (!this.client) {\n      return { content: '', error: 'DeepSeek client not initialized' };\n    }\n\n    try {\n      const modelId = params.modelId || this.config.defaultModel || this.defaultModel;\n\n      // Prepare messages\n      const messages: Array<{ role: string; content: string; name?: string; tool_call_id?: string }> = [];\n\n      if (params.history && params.history.length > 0) {\n        messages.push(...params.history);\n      } else {\n        if (params.systemPrompt) {\n          messages.push({\n            role: 'system',\n            content: params.systemPrompt\n          } as const);\n        }\n        messages.push({\n          role: 'user',\n          content: params.prompt\n        } as const);\n      }\n\n      // Format tools if provided\n      let functions: Array<{ name: string; description: string; parameters: any }> | undefined = undefined;\n      if (tools && tools.size > 0) {\n        functions = Array.from(tools.values()).map(tool => ({\n          name: tool.id,\n          description: tool.description,\n          parameters: (tool as any).singleActionSchema?.shape || { type: 'object', properties: {} }\n        }));\n      }\n\n      let toolChoice: string | undefined = undefined;\n      if (functions && functions.length > 0) {\n        toolChoice = 'auto';\n      }\n\n      // Make the API request\n      const response = await this.client.post('/v1/chat/completions', {\n        model: modelId,\n        messages,\n        temperature: params.temperature ?? 0.7,\n        max_tokens: params.maxTokens,\n        functions,\n        function_call: toolChoice\n      });\n\n      // Extract the response content\n      const content = response.data.choices[0]?.message?.content || '';\n\n      // Handle tool calls\n      let toolCallRequest: LLMGenerateResult['toolCall'] = undefined;\n      if (response.data.choices[0]?.message?.function_call) {\n        const functionCall = response.data.choices[0].message.function_call;\n        toolCallRequest = {\n          name: functionCall.name,\n          arguments: JSON.parse(functionCall.arguments)\n        };\n      }\n\n      return {\n        content,\n        finishReason: response.data.choices[0]?.finish_reason || 'stop',\n        usage: response.data.usage || {\n          promptTokens: 0,\n          completionTokens: 0,\n          totalTokens: 0\n        },\n        toolCall: toolCallRequest\n      };\n    } catch (error) {\n      logger.error('Error generating text with DeepSeek:', error);\n      return {\n        content: '',\n        error: `DeepSeek generation error: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * List available models from DeepSeek\n     */\n  public async listModels(): Promise<LLMModelInfo[]> {\n    if (!this.client) {\n      logger.warn('Cannot fetch DeepSeek models, client not configured.');\n      return [];\n    }\n\n    try {\n      logger.debug('Fetching DeepSeek models list');\n      const response = await this.client.get('/models');\n\n      // DeepSeek response format\n      const models = response.data?.data || [];\n      logger.info(`Provider deepseek has ${models.length} models available`);\n\n      return models.map((m: any) => ({\n        id: m.id,\n        name: m.name || m.id,\n        description: m.description || '',\n        contextWindow: m.context_length || 8192,\n        maxOutputTokens: m.max_output_tokens,\n        supportsFunctions: m.supports_functions || false,\n        supportsVision: m.supports_vision || false\n      })).sort((a: LLMModelInfo, b: LLMModelInfo) => a.id.localeCompare(b.id));\n    } catch (error) {\n      logger.error('Failed to fetch DeepSeek models:', error);\n\n      // Return predefined models\n      return [\n        // DeepSeek Coder models\n        {\n          id: 'deepseek-coder',\n          name: 'DeepSeek Coder',\n          description: 'Latest DeepSeek Coder model optimized for programming tasks',\n          contextWindow: 16384,\n          pricingInfo: 'API usage pricing'\n        },\n        {\n          id: 'deepseek-coder-6.7b',\n          name: 'DeepSeek Coder 6.7B',\n          description: 'Smaller DeepSeek Coder model (6.7B parameters)',\n          contextWindow: 16384,\n          pricingInfo: 'Free (open weights)'\n        },\n        {\n          id: 'deepseek-coder-33b',\n          name: 'DeepSeek Coder 33B',\n          description: 'Larger DeepSeek Coder model (33B parameters)',\n          contextWindow: 16384,\n          pricingInfo: 'Free (open weights)'\n        },\n        {\n          id: 'deepseek-coder-instruct',\n          name: 'DeepSeek Coder Instruct',\n          description: 'Instruction-tuned DeepSeek Coder model',\n          contextWindow: 16384,\n          pricingInfo: 'API usage pricing'\n        },\n        // DeepSeek Chat models\n        {\n          id: 'deepseek-chat',\n          name: 'DeepSeek Chat',\n          description: 'General-purpose DeepSeek Chat model',\n          contextWindow: 8192,\n          pricingInfo: 'API usage pricing'\n        },\n        {\n          id: 'deepseek-llm-67b-chat',\n          name: 'DeepSeek LLM 67B Chat',\n          description: 'Large DeepSeek Chat model (67B parameters)',\n          contextWindow: 8192,\n          pricingInfo: 'API usage pricing'\n        }\n      ];\n    }\n  }\n\n  /**\n     * Test connection to DeepSeek\n     */\n  public async testConnection(): Promise<{success: boolean, message: string}> {\n    if (!this.client) {\n      return {\n        success: false,\n        message: 'DeepSeek client not initialized. Please check your API key.'\n      };\n    }\n\n    try {\n      // Try a simple models request\n      await this.client.get('/models');\n\n      return {\n        success: true,\n        message: 'Successfully connected to DeepSeek API.'\n      };\n    } catch (error) {\n      logger.error('DeepSeek connection test failed:', error);\n      return {\n        success: false,\n        message: `Failed to connect to DeepSeek API: ${error instanceof Error ? error.message : 'Unknown error'}`\n      };\n    }\n  }\n\n  /**\n     * Update the provider configuration\n     */\n  public async updateConfig(_config: any): Promise<void> {\n    await super.updateConfig(_config);\n    this.initializeClient();\n  }\n\n  /**\n     * Get the configuration fields for this provider\n     */\n  public getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select', options?: string[]}> {\n    return [\n      {\n        id: 'apiKey',\n        name: 'API Key',\n        description: 'Your DeepSeek API key',\n        required: true,\n        type: 'string'\n      },\n      {\n        id: 'apiEndpoint',\n        name: 'API Endpoint',\n        description: 'The DeepSeek API endpoint (default: https://api.deepseek.com/v1)',\n        required: false,\n        type: 'string'\n      },\n      {\n        id: 'defaultModel',\n        name: 'Default Model',\n        description: 'The default model to use',\n        required: false,\n        type: 'select',\n        options: [\n          'deepseek-coder',\n          'deepseek-coder-6.7b',\n          'deepseek-coder-33b',\n          'deepseek-coder-instruct',\n          'deepseek-chat',\n          'deepseek-llm-67b-chat'\n        ]\n      }\n    ];\n  }\n}\n\n\n"]}