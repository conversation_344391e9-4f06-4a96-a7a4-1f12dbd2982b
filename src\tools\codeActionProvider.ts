import * as vscode from 'vscode';

/**
 * Comprehensive Code Action and Quick Fix Integration System
 * Supports all VS Code Code Action indicators and provides advanced quick fix capabilities
 */

export interface CodeActionResult {
    action: vscode.CodeAction;
    priority: number;
    category: string;
    confidence: number;
    metadata: {
        source: string;
        trigger: string;
        context: {
            document: vscode.TextDocument;
            range: vscode.Range;
            diagnostic?: vscode.Diagnostic;
        };
    };
}

export interface QuickFixPattern {
    pattern: RegExp;
    action: string;
    priority: number;
    category: string;
    fix: (match: RegExpMatchArray, document: vscode.TextDocument, range: vscode.Range) => vscode.CodeAction;
}

export interface CodeActionContext {
    document: vscode.TextDocument;
    range: vscode.Range;
    diagnostics: readonly vscode.Diagnostic[];
    trigger: 'manual' | 'auto' | 'onSave' | 'onType';
    source: string;
    only?: vscode.CodeActionKind;
}

export interface IconTheme {
    light: vscode.ThemeColor;
    dark: vscode.ThemeColor;
}

export interface QuickFixIcon {
    name: string;
    icon: vscode.ThemeIcon;
    category: string;
    description: string;
    theme?: IconTheme;
}

// EnhancedCodeActionProvider functionality consolidated into TerminalCodeActionProvider
// This class is removed to eliminate duplication

// Terminal Code Action Integration - Consolidated Provider
export class TerminalCodeActionProvider implements vscode.CodeActionProvider {
    // Consolidated properties from EnhancedCodeActionProvider
    protected readonly _quickFixPatterns: QuickFixPattern[] = [];
    protected readonly _iconRegistry: Map<string, QuickFixIcon> = new Map();
    protected readonly _actionHistory: CodeActionResult[] = [];
    protected readonly _activeActions: Map<string, vscode.CodeAction> = new Map();
    private readonly _terminalPatterns: QuickFixPattern[] = [];

    constructor() {
        this._initializeQuickFixPatterns();
        this._initializeIconRegistry();
        this._initializeTerminalPatterns();
    }

    // Comprehensive pattern initialization with ALL functionality from EnhancedCodeActionProvider
    private _initializeQuickFixPatterns(): void {
        // Terminal Error Patterns
        this._addPattern({
            pattern: /bash:\s+(.+?):\s+command not found/i,
            action: 'install-missing-command',
            priority: 100,
            category: 'terminal',
            fix: (match, doc, range) => {
                const command = match[1];
                const action = new vscode.CodeAction(
                    `Install missing command: ${command}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.installCommand',
                    title: `Install ${command}`,
                    arguments: [command, 'terminal']
                };
                action.diagnostics = [{
                    range,
                    message: `Command '${command}' not found`,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'terminal'
                }];
                return action;
            }
        });

        // Import Error Patterns
        this._addPattern({
            pattern: /Cannot find module\s+['"](.+?)['"]/i,
            action: 'fix-import',
            priority: 95,
            category: 'imports',
            fix: (match, doc, range) => {
                const moduleName = match[1];
                const lineText = doc.getText(range);
                const importStatement = lineText.includes('require') ?
                    `const ${moduleName.split('/').pop()} = require('${moduleName}');` :
                    `import * as ${moduleName.split('/').pop()} from '${moduleName}';`;

                const action = new vscode.CodeAction(
                    `Install missing module: ${moduleName}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.edit = new vscode.WorkspaceEdit();
                action.edit.replace(doc.uri, range, importStatement);
                action.command = {
                    command: 'codessa.installModule',
                    title: `Install ${moduleName}`,
                    arguments: [moduleName, 'npm', doc.uri.fsPath]
                };
                return action;
            }
        });

        // TypeScript Error Patterns
        this._addPattern({
            pattern: /Property\s+['"](.+?)['"]\s+does not exist on type/i,
            action: 'add-property',
            priority: 90,
            category: 'typescript',
            fix: (match, doc, range) => {
                const property = match[1];
                const action = new vscode.CodeAction(
                    `Add property '${property}' to interface`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.addProperty',
                    title: `Add property ${property}`,
                    arguments: [property, doc, range]
                };
                return action;
            }
        });

        // ESLint Error Patterns
        this._addPattern({
            pattern: /(.+?)\s+is not defined/i,
            action: 'define-variable',
            priority: 85,
            category: 'eslint',
            fix: (match, doc, range) => {
                const variable = match[1];
                const action = new vscode.CodeAction(
                    `Define variable: ${variable}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.edit = new vscode.WorkspaceEdit();
                action.edit.insert(doc.uri, new vscode.Position(range.start.line, 0),
                    `const ${variable} = ;\n`);
                return action;
            }
        });

        // Security Vulnerability Patterns
        this._addPattern({
            pattern: /Potential security vulnerability:\s+(.+?)/i,
            action: 'fix-security',
            priority: 100,
            category: 'security',
            fix: (match, doc, range) => {
                const issue = match[1];
                const action = new vscode.CodeAction(
                    `Fix security issue: ${issue}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.fixSecurity',
                    title: `Fix ${issue}`,
                    arguments: [issue, doc, range]
                };
                return action;
            }
        });

        // Python Error Patterns
        this._addPattern({
            pattern: /NameError:\s+name\s+['"](.+?)['"]\s+is not defined/i,
            action: 'import-python-module',
            priority: 80,
            category: 'python',
            fix: (match, doc, range) => {
                const name = match[1];
                const action = new vscode.CodeAction(
                    `Import Python module: ${name}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.edit = new vscode.WorkspaceEdit();
                // Insert import at the beginning of the file, but use range for context
                const insertPosition = range.start.line > 0 ? new vscode.Position(0, 0) : range.start;
                action.edit.insert(doc.uri, insertPosition, `import ${name}\n`);
                action.diagnostics = [{
                    range,
                    message: `Name '${name}' is not defined`,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'python'
                }];
                return action;
            }
        });

        // Git Error Patterns
        this._addPattern({
            pattern: /fatal:\s+not\s+a\s+git\s+repository/i,
            action: 'init-git-repo',
            priority: 75,
            category: 'git',
            fix: (match, doc, range) => {
                const errorMessage = match[0]; // Full error message
                const action = new vscode.CodeAction(
                    'Initialize Git repository',
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.initGitRepo',
                    title: 'Initialize Git repository',
                    arguments: [doc.uri.fsPath]
                };
                action.diagnostics = [{
                    range,
                    message: errorMessage,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'git'
                }];
                return action;
            }
        });

        // Docker Error Patterns
        this._addPattern({
            pattern: /docker:\s+(.+?)\s+not\s+found/i,
            action: 'install-docker',
            priority: 70,
            category: 'docker',
            fix: (match, doc, range) => {
                const dockerCommand = match[1]; // The specific docker command that wasn't found
                const action = new vscode.CodeAction(
                    `Install Docker (missing: ${dockerCommand})`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.installDocker',
                    title: 'Install Docker',
                    arguments: [dockerCommand, doc.uri.fsPath]
                };
                action.diagnostics = [{
                    range,
                    message: `Docker command '${dockerCommand}' not found`,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'docker'
                }];
                return action;
            }
        });

        // Network Error Patterns
        this._addPattern({
            pattern: /ENOTFOUND|ECONNREFUSED|ETIMEDOUT/i,
            action: 'check-network',
            priority: 65,
            category: 'network',
            fix: (match, doc, range) => {
                const networkError = match[0]; // The specific network error (ENOTFOUND, ECONNREFUSED, etc.)
                const action = new vscode.CodeAction(
                    `Fix network issue: ${networkError}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.checkNetwork',
                    title: 'Check network connection',
                    arguments: [networkError, doc.uri.fsPath]
                };
                action.diagnostics = [{
                    range,
                    message: `Network error: ${networkError}`,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'network'
                }];
                return action;
            }
        });

        // File Permission Error Patterns
        this._addPattern({
            pattern: /EACCES|permission\s+denied/i,
            action: 'fix-permissions',
            priority: 60,
            category: 'permissions',
            fix: (match, doc, range) => {
                const permissionError = match[0]; // The specific permission error (EACCES, permission denied, etc.)
                const action = new vscode.CodeAction(
                    `Fix file permissions: ${permissionError}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.fixPermissions',
                    title: 'Fix file permissions',
                    arguments: [doc.uri.fsPath, permissionError]
                };
                action.diagnostics = [{
                    range,
                    message: `Permission error: ${permissionError}`,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'permissions'
                }];
                return action;
            }
        });

        // Syntax Error Patterns
        this._addPattern({
            pattern: /SyntaxError|Unexpected\s+token/i,
            action: 'fix-syntax',
            priority: 55,
            category: 'syntax',
            fix: (match, doc, range) => {
                const syntaxError = match[0]; // The specific syntax error (SyntaxError, Unexpected token, etc.)
                const action = new vscode.CodeAction(
                    `Fix syntax error: ${syntaxError}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.fixSyntax',
                    title: 'Fix syntax error',
                    arguments: [doc, range, syntaxError]
                };
                action.diagnostics = [{
                    range,
                    message: `Syntax error: ${syntaxError}`,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'syntax'
                }];
                return action;
            }
        });
    }

    private _initializeIconRegistry(): void {
        // Define common themes
        const themes = {
            error: {
                light: new vscode.ThemeColor('errorForeground'),
                dark: new vscode.ThemeColor('errorForeground')
            },
            warning: {
                light: new vscode.ThemeColor('editorWarning.foreground'),
                dark: new vscode.ThemeColor('editorWarning.foreground')
            },
            info: {
                light: new vscode.ThemeColor('editorInfo.foreground'),
                dark: new vscode.ThemeColor('editorInfo.foreground')
            },
            success: {
                light: new vscode.ThemeColor('testing.iconPassed'),
                dark: new vscode.ThemeColor('testing.iconPassed')
            }
        };

        // Standard VS Code Quick Fix Icons with unified theming
        this._registerIcon({
            name: 'lightbulb',
            icon: new vscode.ThemeIcon('lightbulb', themes.info.light),
            category: 'suggestion',
            description: 'General suggestions and improvements',
            theme: themes.info
        });

        this._registerIcon({
            name: 'error',
            icon: new vscode.ThemeIcon('error', themes.error.light),
            category: 'error',
            description: 'Error conditions requiring immediate attention',
            theme: themes.error
        });

        this._registerIcon({
            name: 'warning',
            icon: new vscode.ThemeIcon('warning', themes.warning.light),
            category: 'warning',
            description: 'Warning conditions that should be addressed',
            theme: themes.warning
        });

        this._registerIcon({
            name: 'info',
            icon: new vscode.ThemeIcon('info', themes.info.light),
            category: 'info',
            description: 'Informational suggestions and hints',
            theme: themes.info
        });

        this._registerIcon({
            name: 'tools',
            icon: new vscode.ThemeIcon('tools', themes.info.light),
            category: 'refactor',
            description: 'Refactoring and code improvement actions',
            theme: themes.info
        });

        this._registerIcon({
            name: 'package',
            icon: new vscode.ThemeIcon('package', themes.info.light),
            category: 'dependency',
            description: 'Package and dependency management',
            theme: themes.info
        });

        this._registerIcon({
            name: 'terminal',
            icon: new vscode.ThemeIcon('terminal', themes.info.light),
            category: 'terminal',
            description: 'Terminal and command-line related actions',
            theme: themes.info
        });

        this._registerIcon({
            name: 'debug',
            icon: new vscode.ThemeIcon('debug', themes.warning.light),
            category: 'debug',
            description: 'Debugging and diagnostic actions',
            theme: themes.warning
        });

        this._registerIcon({
            name: 'security',
            icon: new vscode.ThemeIcon('shield', themes.error.light),
            category: 'security',
            description: 'Security-related fixes and improvements',
            theme: themes.error
        });

        this._registerIcon({
            name: 'performance',
            icon: new vscode.ThemeIcon('dashboard', themes.info.light),
            category: 'performance',
            description: 'Performance optimization suggestions',
            theme: themes.info
        });

        this._registerIcon({
            name: 'accessibility',
            icon: new vscode.ThemeIcon('accessibility', themes.info.light),
            category: 'accessibility',
            description: 'Accessibility improvements and fixes',
            theme: themes.info
        });

        // Language-specific icons
        this._registerIcon({
            name: 'typescript',
            icon: new vscode.ThemeIcon('symbol-class', themes.info.light),
            category: 'language',
            description: 'TypeScript fixes',
            theme: themes.info
        });

        this._registerIcon({
            name: 'python',
            icon: new vscode.ThemeIcon('symbol-method', themes.info.light),
            category: 'language',
            description: 'Python fixes',
            theme: themes.info
        });

        this._registerIcon({
            name: 'git',
            icon: new vscode.ThemeIcon('git-branch', themes.info.light),
            category: 'vcs',
            description: 'Git operations',
            theme: themes.info
        });

        this._registerIcon({
            name: 'docker',
            icon: new vscode.ThemeIcon('server', themes.info.light),
            category: 'container',
            description: 'Docker operations',
            theme: themes.info
        });

        this._registerIcon({
            name: 'network',
            icon: new vscode.ThemeIcon('globe', themes.warning.light),
            category: 'network',
            description: 'Network fixes',
            theme: themes.warning
        });

        this._registerIcon({
            name: 'permissions',
            icon: new vscode.ThemeIcon('shield', themes.error.light),
            category: 'security',
            description: 'Permission fixes',
            theme: themes.error
        });

        this._registerIcon({
            name: 'syntax',
            icon: new vscode.ThemeIcon('symbol-color', themes.error.light),
            category: 'language',
            description: 'Syntax fixes',
            theme: themes.error
        });

        this._registerIcon({
            name: 'imports',
            icon: new vscode.ThemeIcon('package', themes.info.light),
            category: 'dependency',
            description: 'Import fixes',
            theme: themes.info
        });

        this._registerIcon({
            name: 'eslint',
            icon: new vscode.ThemeIcon('warning', themes.warning.light),
            category: 'linting',
            description: 'ESLint fixes',
            theme: themes.warning
        });

        // Terminal-specific icons with unified theming
        this._registerIcon({
            name: 'terminal-error',
            icon: new vscode.ThemeIcon('terminal', themes.error.light),
            category: 'terminal-error',
            description: 'Terminal error indicators',
            theme: themes.error
        });

        this._registerIcon({
            name: 'terminal-warning',
            icon: new vscode.ThemeIcon('terminal', themes.warning.light),
            category: 'terminal-warning',
            description: 'Terminal warning indicators',
            theme: themes.warning
        });

        this._registerIcon({
            name: 'terminal-success',
            icon: new vscode.ThemeIcon('terminal', themes.success.light),
            category: 'terminal-success',
            description: 'Terminal success indicators',
            theme: themes.success
        });

        this._registerIcon({
            name: 'terminal-info',
            icon: new vscode.ThemeIcon('terminal', themes.info.light),
            category: 'terminal-info',
            description: 'Terminal information indicators',
            theme: themes.info
        });
    }

    protected _registerIcon(icon: QuickFixIcon): void {
        this._iconRegistry.set(icon.name, icon);
    }

    protected _addPattern(pattern: QuickFixPattern): void {
        this._quickFixPatterns.push(pattern);
    }

    protected _getIconForCategory(category: string, source: 'terminal' | 'editor' = 'editor'): string {
        // Normalize the category
        const normalizedCategory = category.toLowerCase();

        // Try to get a source-specific icon first
        const sourceSpecificIcon = this._iconRegistry.get(`${source}-${normalizedCategory}`);
        if (sourceSpecificIcon) {
            return `$(${sourceSpecificIcon.icon.id})`;
        }

        // Fallback to general category icon
        const icon = this._iconRegistry.get(normalizedCategory);
        if (icon) {
            return `$(${icon.icon.id})`;
        }

        // Map common categories to appropriate icons
        switch (normalizedCategory) {
            case 'error':
                return source === 'terminal' ? '$(terminal) $(error)' : '$(error)';
            case 'warning':
                return source === 'terminal' ? '$(terminal) $(warning)' : '$(warning)';
            case 'success':
                return source === 'terminal' ? '$(terminal) $(check)' : '$(check)';
            case 'info':
                return source === 'terminal' ? '$(terminal) $(info)' : '$(info)';
            default:
                return source === 'terminal' ? '$(terminal)' : '$(light-bulb)';
        }
    }

    // VS Code CodeActionProvider interface implementation with comprehensive functionality
    public provideCodeActions(
        document: vscode.TextDocument,
        range: vscode.Range | vscode.Selection,
        context: vscode.CodeActionContext,
        token: vscode.CancellationToken
    ): vscode.ProviderResult<(vscode.CodeAction | vscode.Command)[]> {
        if (token.isCancellationRequested) {
            return [];
        }
        const actions: vscode.CodeAction[] = [];
        const codeActionContext: CodeActionContext = {
            document,
            range,
            diagnostics: context.diagnostics,
            trigger: context.triggerKind === vscode.CodeActionTriggerKind.Invoke ? 'manual' : 'auto',
            source: 'provider'
        };

        // Get pattern-based actions
        const patternActions = this._getPatternBasedActions(codeActionContext);
        actions.push(...patternActions);

        // Get diagnostic-based actions
        const diagnosticActions = this._getDiagnosticBasedActions(codeActionContext);
        actions.push(...diagnosticActions);

        // Get context-aware actions
        const contextActions = this._getContextAwareActions(codeActionContext);
        actions.push(...contextActions);

        return actions;
    }

    protected _getPatternBasedActions(context: CodeActionContext): vscode.CodeAction[] {
        const actions: vscode.CodeAction[] = [];
        const text = context.document.getText(context.range);

        for (const pattern of this._quickFixPatterns) {
            const matches = text.match(pattern.pattern);
            if (matches) {
                try {
                    const action = pattern.fix(matches, context.document, context.range);
                    action.title = `${this._getIconForCategory(pattern.category)} ${action.title}`;
                    actions.push(action);
                } catch (error) {
                    console.error('Error creating code action:', error);
                }
            }
        }

        return actions;
    }

    private _getDiagnosticBasedActions(context: CodeActionContext): vscode.CodeAction[] {
        const actions: vscode.CodeAction[] = [];

        for (const diagnostic of context.diagnostics) {
            // Create action based on diagnostic
            const action = new vscode.CodeAction(
                `Fix: ${diagnostic.message}`,
                vscode.CodeActionKind.QuickFix
            );

            action.diagnostics = [diagnostic];
            action.command = {
                command: 'codessa.applyDiagnosticFix',
                title: `Apply fix for: ${diagnostic.message}`,
                arguments: [diagnostic, context.document]
            };

            actions.push(action);
        }

        return actions;
    }

    protected _getContextAwareActions(context: CodeActionContext): vscode.CodeAction[] {
        const actions: vscode.CodeAction[] = [];

        // Context-aware suggestions based on document content
        const lineText = context.document.lineAt(context.range.start.line).text;

        // Import suggestions
        if (lineText.includes('import') || lineText.includes('require')) {
            const importAction = new vscode.CodeAction(
                `${this._getIconForCategory('dependency')} Optimize imports`,
                vscode.CodeActionKind.Refactor
            );
            importAction.command = {
                command: 'codessa.optimizeImports',
                title: 'Optimize imports',
                arguments: [context.document]
            };
            actions.push(importAction);
        }

        // Function/method suggestions
        if (lineText.includes('function') || lineText.includes('=>') || lineText.includes('(')) {
            const refactorAction = new vscode.CodeAction(
                `${this._getIconForCategory('refactor')} Extract method`,
                vscode.CodeActionKind.RefactorExtract
            );
            refactorAction.command = {
                command: 'codessa.extractMethod',
                title: 'Extract method',
                arguments: [context.document, context.range]
            };
            actions.push(refactorAction);
        }

        return actions;
    }

    // Terminal-specific methods
    public analyzeOutput(output: string): vscode.CodeAction[] {
        const actions: vscode.CodeAction[] = [];

        for (const pattern of this._quickFixPatterns) {
            const matches = output.match(pattern.pattern);
            if (matches) {
                try {
                    // Create a mock document and range for terminal output
                    const mockDocument = {
                        uri: vscode.Uri.parse('terminal://output'),
                        getText: () => output
                    } as vscode.TextDocument;

                    const mockRange = new vscode.Range(0, 0, 0, output.length);
                    const action = pattern.fix(matches, mockDocument, mockRange);
                    if (action) {
                        actions.push(action);
                    }
                } catch (error) {
                    console.error(`Error creating terminal action for pattern ${pattern.action}:`, error);
                }
            }
        }

        return actions;
    }

    private _initializeTerminalPatterns(): void {
        // Terminal-specific error patterns
        this._addTerminalPattern({
            pattern: /(npm ERR!|yarn error|pip install error)/i,
            action: 'fix-package-error',
            priority: 100,
            category: 'terminal',
            fix: (match, output) => {
                const packageManager = match[1]; // npm ERR!, yarn error, or pip install error
                const action = new vscode.CodeAction(
                    `${this._getIconForCategory('terminal')} Fix ${packageManager} installation error`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.fixPackageError',
                    title: `Fix ${packageManager} error`,
                    arguments: [output, packageManager]
                };
                return action;
            }
        });

        this._addTerminalPattern({
            pattern: /(Permission denied|access denied)/i,
            action: 'fix-permission-error',
            priority: 95,
            category: 'terminal',
            fix: (match, output) => {
                const permissionType = match[1]; // Permission denied or access denied
                const action = new vscode.CodeAction(
                    `${this._getIconForCategory('security')} Fix ${permissionType.toLowerCase()}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.fixPermissionError',
                    title: `Fix ${permissionType.toLowerCase()}`,
                    arguments: [output, permissionType]
                };
                return action;
            }
        });

        this._addTerminalPattern({
            pattern: /(connection refused|connection timeout)/i,
            action: 'fix-connection-error',
            priority: 90,
            category: 'terminal',
            fix: (match, output) => {
                const connectionError = match[1]; // connection refused or connection timeout
                const action = new vscode.CodeAction(
                    `${this._getIconForCategory('debug')} Fix ${connectionError}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.fixConnectionError',
                    title: `Fix ${connectionError}`,
                    arguments: [output, connectionError]
                };
                return action;
            }
        });
    }

    private _addTerminalPattern(pattern: QuickFixPattern): void {
        this._terminalPatterns.push(pattern);
    }

    public analyzeTerminalOutput(output: string): vscode.CodeAction[] {
        const actions: vscode.CodeAction[] = [];

        for (const pattern of this._terminalPatterns) {
            const matches = output.match(pattern.pattern);
            if (matches) {
                try {
                    const terminalDocument = {
                        uri: vscode.Uri.parse('terminal-output'),
                        fileName: 'terminal-output',
                        isUntitled: true,
                        languageId: 'plaintext',
                        version: 1,
                        isDirty: false,
                        isClosed: false,
                        save: () => Promise.resolve(false),
                        lineCount: output.split('\n').length,
                        lineAt: function(lineOrPosition: number | vscode.Position): vscode.TextLine {
                            const line = typeof lineOrPosition === 'number' ? lineOrPosition : lineOrPosition.line;
                            const text = output.split('\n')[line] || '';
                            return {
                                lineNumber: line,
                                text,
                                range: new vscode.Range(line, 0, line, text.length),
                                rangeIncludingLineBreak: new vscode.Range(line, 0, line + 1, 0),
                                firstNonWhitespaceCharacterIndex: text.search(/\S/),
                                isEmptyOrWhitespace: !/\S/.test(text)
                            };
                        },
                        offsetAt: function(position: vscode.Position): number {
                            const lines = output.split('\n');
                            let offset = 0;
                            for (let i = 0; i < position.line; i++) {
                                offset += (lines[i]?.length || 0) + 1;  // +1 for newline
                            }
                            return offset + Math.min(position.character, lines[position.line]?.length || 0);
                        },
                        positionAt: function(offset: number): vscode.Position {
                            const lines = output.split('\n');
                            let remaining = offset;
                            let line = 0;
                            
                            while (line < lines.length) {
                                const lineLength = (lines[line]?.length || 0) + 1; // +1 for newline
                                if (remaining < lineLength) {
                                    return new vscode.Position(line, remaining);
                                }
                                remaining -= lineLength;
                                line++;
                            }
                            
                            const lastLine = lines.length - 1;
                            const lastChar = lines[lastLine]?.length || 0;
                            return new vscode.Position(lastLine, lastChar);
                        },
                        getText: function(range?: vscode.Range): string {
                            if (!range) {
                                return output;
                            }
                            const lines = output.split('\n');
                            if (range.start.line === range.end.line) {
                                return (lines[range.start.line] || '').substring(range.start.character, range.end.character);
                            }
                            const result: string[] = [];
                            for (let i = range.start.line; i <= range.end.line; i++) {
                                const line = lines[i] || '';
                                if (i === range.start.line) {
                                    result.push(line.substring(range.start.character));
                                } else if (i === range.end.line) {
                                    result.push(line.substring(0, range.end.character));
                                } else {
                                    result.push(line);
                                }
                            }
                            return result.join('\n');
                        },
                        getWordRangeAtPosition: () => new vscode.Range(0, 0, 0, 0),
                        validateRange: (range: vscode.Range) => range,
                        validatePosition: (position: vscode.Position) => position,
                        eol: vscode.EndOfLine.LF
                    };
                    
                    const action = pattern.fix(
                        matches,
                        terminalDocument as unknown as vscode.TextDocument,
                        new vscode.Range(0, 0, terminalDocument.lineCount - 1, output.split('\n')[output.split('\n').length - 1].length)
                    );
                    actions.push(action);
                } catch (error) {
                    console.error('Error creating terminal code action:', error);
                }
            }
        }

        return actions;
    }

    // Additional comprehensive methods from EnhancedCodeActionProvider
    public getQuickFixIcon(name: string): QuickFixIcon | undefined {
        return this._iconRegistry.get(name);
    }

    public getAllIcons(): QuickFixIcon[] {
        return Array.from(this._iconRegistry.values());
    }

    public registerAction(actionId: string, action: vscode.CodeAction): void {
        this._activeActions.set(actionId, action);
    }

    public unregisterAction(actionId: string): void {
        this._activeActions.delete(actionId);
    }

    public getAction(actionId: string): vscode.CodeAction | undefined {
        return this._activeActions.get(actionId);
    }

    public getActionHistory(): CodeActionResult[] {
        return [...this._actionHistory];
    }

    public clearActionHistory(): void {
        this._actionHistory.length = 0;
    }

    public async applyQuickFix(action: vscode.CodeAction, document: vscode.TextDocument): Promise<boolean> {
        // Track the action in history
        this._actionHistory.push({
            action,
            priority: 100,
            category: action.kind?.value || 'unknown',
            confidence: 1.0,
            metadata: {
                source: 'quickfix',
                trigger: 'manual',
                context: {
                    document,
                    range: action.diagnostics?.[0]?.range || new vscode.Range(0, 0, 0, 0),
                    diagnostic: action.diagnostics?.[0]
                }
            }
        });
        try {
            if (action.edit) {
                const success = await vscode.workspace.applyEdit(action.edit);
                return success;
            } else if (action.command) {
                await vscode.commands.executeCommand(action.command.command, ...action.command.arguments || []);
                return true;
            } else {
                return false;
            }
        } catch (error) {
            console.error('Error applying quick fix:', error);
            return false;
        }
    }
}

// Global Code Action Manager
export class CodeActionManager {
    private static _instance: CodeActionManager;
    private readonly _providers: Map<string, TerminalCodeActionProvider> = new Map();
    private readonly _subscriptions: vscode.Disposable[] = [];

    private constructor() {
        this._initialize();
    }

    public static getInstance(): CodeActionManager {
        if (!CodeActionManager._instance) {
            CodeActionManager._instance = new CodeActionManager();
        }
        return CodeActionManager._instance;
    }

    private _initialize(): void {
        // Register the main code action provider
        const provider = new TerminalCodeActionProvider();
        const disposable = vscode.languages.registerCodeActionsProvider(
            { scheme: 'file', language: '*' },
            provider,
            {
                providedCodeActionKinds: [
                    vscode.CodeActionKind.QuickFix,
                    vscode.CodeActionKind.Refactor,
                    vscode.CodeActionKind.Source
                ]
            }
        );

        this._subscriptions.push(disposable);
        this._providers.set('main', provider);

        // Register terminal-specific provider
        const terminalProvider = new TerminalCodeActionProvider();
        this._providers.set('terminal', terminalProvider);
    }

    public getProvider(name: string): TerminalCodeActionProvider | undefined {
        return this._providers.get(name);
    }

    public getAllProviders(): TerminalCodeActionProvider[] {
        return Array.from(this._providers.values());
    }

    public registerProvider(name: string, provider: TerminalCodeActionProvider): void {
        this._providers.set(name, provider);
    }

    public unregisterProvider(name: string): void {
        const provider = this._providers.get(name);
        if (provider) {
            this._providers.delete(name);
        }
    }

    public async analyzeCode(document: vscode.TextDocument, range: vscode.Range): Promise<vscode.CodeAction[]> {
        const allActions: vscode.CodeAction[] = [];
        const token = new vscode.CancellationTokenSource().token;

        for (const provider of Array.from(this._providers.values())) {
            const context: vscode.CodeActionContext = {
                diagnostics: [],
                triggerKind: vscode.CodeActionTriggerKind.Invoke,
                only: undefined
            };

            try {
                const actions = provider.provideCodeActions(document, range, context, token);
                if (actions) {
                    if (actions instanceof Promise) {
                        const providerActions = await actions;
                        if (providerActions && Array.isArray(providerActions)) {
                            allActions.push(...providerActions);
                        }
                    } else if (Array.isArray(actions)) {
                        // Filter out Commands and only keep CodeActions
                        const codeActions = actions.filter((action): action is vscode.CodeAction =>
                            action && typeof action === 'object' && 'title' in action && typeof action.title === 'string'
                        );
                        allActions.push(...codeActions);
                    }
                }
            } catch (error) {
                console.error('Error getting code actions from provider:', error);
            }
        }

        return allActions;
    }

    public dispose(): void {
        this._subscriptions.forEach(sub => sub.dispose());
        this._subscriptions.length = 0;
        this._providers.clear();
    }
}

// Export singleton instance
export const codeActionManager = CodeActionManager.getInstance();
