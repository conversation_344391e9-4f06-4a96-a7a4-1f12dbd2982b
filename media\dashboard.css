/**
 * Codessa Dashboard UI Styles
 * 
 * A modern, data-focused dashboard interface
 */

:root {
    --codessa-primary: #4D7AFF;
    --codessa-primary-light: #7A9BFF;
    --codessa-primary-dark: #2B5CE0;
    --codessa-secondary: #34D399;
    --codessa-accent: #EC4899;
    --codessa-background: #FFFFFF;
    --codessa-foreground: #1F2937;
    --codessa-gray-100: #F3F4F6;
    --codessa-gray-200: #E5E7EB;
    --codessa-gray-300: #D1D5DB;
    --codessa-gray-400: #9CA3AF;
    --codessa-gray-500: #6B7280;
    --codessa-gray-600: #4B5563;
    --codessa-gray-700: #374151;
    --codessa-gray-800: #1F2937;
    --codessa-gray-900: #111827;
    --codessa-error: #F87171;
    --codessa-warning: #FBBF24;
    --codessa-success: #34D399;
    --codessa-info: #60A5FA;

    /* Status colors */
    --status-ok: #10B981;
    --status-warning: #F59E0B;
    --status-error: #EF4444;
}

body.vscode-dark {
    --codessa-background: #111827;
    --codessa-foreground: #F9FAFB;
    --codessa-gray-100: #1F2937;
    --codessa-gray-200: #374151;
    --codessa-gray-300: #4B5563;
    --codessa-gray-400: #6B7280;
    --codessa-gray-500: #9CA3AF;
    --codessa-gray-600: #D1D5DB;
    --codessa-gray-700: #E5E7EB;
    --codessa-gray-800: #F3F4F6;
    --codessa-gray-900: #F9FAFB;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--codessa-background);
    color: var(--codessa-foreground);
    font-size: 14px;
    line-height: 1.5;
}

* {
    box-sizing: border-box;
}

.dashboard-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 100%;
    margin: 0 auto;
}

.dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background-color: var(--codessa-gray-100);
    border-bottom: 1px solid var(--codessa-gray-200);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.logo-container {
    display: flex;
    align-items: center;
}

.dashboard-header .logo {
    width: 36px;
    height: 36px;
    margin-right: 12px;
}

.dashboard-header h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.dashboard-header .actions {
    display: flex;
    gap: 8px;
}

.icon-button {
    background: transparent;
    border: none;
    color: var(--codessa-gray-600);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
}

.icon-button:hover {
    background-color: var(--codessa-gray-200);
    color: var(--codessa-gray-900);
}

.icon-button:active {
    transform: translateY(1px);
}

.dashboard-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.dashboard-row {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.dashboard-card {
    background: var(--codessa-background);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    padding: 16px;
    flex: 1;
    min-width: 300px;
    border: 1px solid var(--codessa-gray-200);
    transition: box-shadow 0.3s;
}

.dashboard-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-card h2 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: var(--codessa-gray-800);
    border-bottom: 1px solid var(--codessa-gray-200);
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
}

/* Status indicators for the status card */
.status-card {
    background: linear-gradient(135deg, var(--codessa-background), var(--codessa-gray-100));
}

.status-indicators {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    background-color: var(--codessa-background);
    border: 1px solid var(--codessa-gray-200);
}

.status-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 12px;
}

.status-item.status-ok .status-icon {
    background-color: var(--status-ok);
    box-shadow: 0 0 5px var(--status-ok);
}

.status-item.status-warning .status-icon {
    background-color: var(--status-warning);
    box-shadow: 0 0 5px var(--status-warning);
}

.status-item.status-error .status-icon {
    background-color: var(--status-error);
    box-shadow: 0 0 5px var(--status-error);
}

.status-label {
    font-weight: 500;
    flex-grow: 1;
}

.status-value {
    font-weight: 600;
}

.last-updated {
    font-size: 12px;
    color: var(--codessa-gray-500);
    text-align: right;
    margin-top: 16px;
    margin-bottom: 0;
}

/* Agent list styles */
.agents-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 8px;
}

.agent-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 6px;
    background-color: var(--codessa-gray-100);
    border: 1px solid var(--codessa-gray-200);
    cursor: pointer;
    transition: all 0.2s;
}

.agent-item:hover {
    background-color: var(--codessa-gray-200);
}

.agent-icon {
    width: 32px;
    height: 32px;
    background-color: var(--codessa-primary);
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 12px;
}

.agent-details {
    flex-grow: 1;
}

.agent-name {
    font-weight: 600;
    margin-bottom: 4px;
}

.agent-description {
    font-size: 12px;
    color: var(--codessa-gray-600);
}

.agent-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
    font-size: 12px;
}

.meta-tag {
    background-color: var(--codessa-gray-200);
    border-radius: 12px;
    padding: 2px 8px;
    color: var(--codessa-gray-700);
    display: flex;
    align-items: center;
    gap: 4px;
}

.agent-actions {
    display: flex;
    gap: 8px;
}

/* Provider and tools lists */
.provider-list, .tools-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.provider-item, .tool-item {
    padding: 8px 12px;
    border-radius: 6px;
    background-color: var(--codessa-gray-100);
    border: 1px solid var(--codessa-gray-200);
}

.provider-name, .tool-name {
    font-weight: 600;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.provider-status {
    font-size: 12px;
    background-color: var(--codessa-gray-200);
    border-radius: 12px;
    padding: 2px 8px;
    color: var(--codessa-gray-700);
}

.provider-status.configured {
    background-color: var(--status-ok);
    color: white;
}

.provider-status.default {
    background-color: var(--codessa-primary);
    color: white;
}

.provider-status.not-configured {
    background-color: var(--status-warning);
    color: white;
}

.tool-description, .provider-description {
    font-size: 12px;
    color: var(--codessa-gray-600);
}

/* Button styles */
.btn {
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn.primary {
    background-color: var(--codessa-primary);
    color: white;
}

.btn.primary:hover {
    background-color: var(--codessa-primary-dark);
}

.btn.secondary {
    background-color: var(--codessa-gray-200);
    color: var(--codessa-gray-800);
}

.btn.secondary:hover {
    background-color: var(--codessa-gray-300);
}

.btn:active {
    transform: translateY(1px);
}

/* Empty and error states */
.empty-message, .error-message {
    text-align: center;
    padding: 24px;
    border-radius: 8px;
    margin: 16px 0;
}

.empty-message {
    background-color: var(--codessa-gray-100);
    color: var(--codessa-gray-600);
    font-style: italic;
}

.error-message {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--status-error);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
    .dashboard-card {
        min-width: 100%;
    }
    
    .dashboard-row {
        flex-direction: column;
    }
} 