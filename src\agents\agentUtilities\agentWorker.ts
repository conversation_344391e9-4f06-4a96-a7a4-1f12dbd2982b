import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { EventEmitter } from 'events';

export interface WorkerTask {
    agentPath: string;
    agentConfig: Record<string, unknown>;
    method: string;
    args: unknown[];
}

export interface WorkerStreamEvent {
    type: 'data' | 'end' | 'error';
    data?: unknown;
    error?: Error | string;
}

export class AgentWorker extends EventEmitter {
  private worker: Worker;
  constructor(task: WorkerTask) {
    super();
    this.worker = new Worker(__filename, {
      workerData: task
    });
    this.worker.on('message', (event: WorkerStreamEvent) => {
      this.emit(event.type, event.data || event.error);
    });
    this.worker.on('error', err => this.emit('error', err));
    this.worker.on('exit', code => {
      if (code !== 0) this.emit('error', new Error(`Worker stopped with exit code ${code}`));
    });
  }
  terminate(): void {
    this.worker.terminate();
  }
}

// Worker thread code
if (isMainThread === false) {
  (async () => {
    try {
      // Dynamic import for the agent class
      const agentModule = await import(workerData.agentPath);
      const AgentClass = agentModule.default || agentModule;
      const agent = new AgentClass(workerData.agentConfig);
      const method = agent[workerData.method].bind(agent);
      const result = await method(...workerData.args, {
        onStream: (data: unknown) => {
          parentPort?.postMessage({ type: 'data', data });
        }
      });
      parentPort?.postMessage({ type: 'end', data: result });
    } catch (err) {
      let errorMsg = '';
      if (err instanceof Error) {
        errorMsg = err.message;
      } else if (typeof err === 'object' && err !== null && 'message' in err) {
        errorMsg = (err as { message: string }).message;
      } else {
        errorMsg = String(err);
      }
      parentPort?.postMessage({ type: 'error', error: errorMsg });
    }
  })();
}
