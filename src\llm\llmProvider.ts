import * as vscode from 'vscode';
import { ITool } from '../tools/tool.ts.backup';
import { LLMConfig, LLMGenerateParams, LLMGenerateResult } from './types';

export interface ToolCallRequest {
    id?: string;
    name?: string;
    toolId?: string;
    arguments?: Record<string, any>;
    args?: Record<string, any>;
}

export interface LLMModelInfo {
    id: string;
    name?: string;
    description?: string;
    contextWindow?: number;
    maxOutputTokens?: number;
    supportsFunctions?: boolean;
    supportsVision?: boolean;
    deprecated?: boolean;
    pricingInfo?: string;
}

export interface LLMProviderConfig {
    apiKey?: string;
    apiEndpoint?: string;
    organizationId?: string;
    defaultModel?: string;
    additionalParams?: Record<string, any>;
    llamaCppPath?: string;
    additionalModelDirectories?: string[];
}

export interface ILLMProvider {
    readonly providerId: string;
    readonly displayName: string;
    readonly description: string;
    readonly website: string;
    readonly defaultEndpoint?: string;
    readonly requiresApiKey: boolean;
    readonly supportsEndpointConfiguration: boolean;
    readonly defaultModel?: string;
    readonly supportsEmbeddings?: boolean;

    /**
     * Generates text based on the provided parameters.
     */
    generate(
        _params: LLMGenerateParams,
        _cancellationToken?: vscode.CancellationToken,
        _tools?: Map<string, ITool>
    ): Promise<LLMGenerateResult>;

    /**
     * Generates text streamingly, yielding chunks as they arrive.
     * Note: Not all providers support streaming.
     */
    streamGenerate?(
        _params: LLMGenerateParams,
        _cancellationToken?: vscode.CancellationToken
    ): AsyncGenerator<string, void, unknown>;

    /**
     * Generates an embedding vector for the given text.
     * Note: Not all providers support embeddings.
     */
    generateEmbedding?(
        _text: string,
        _modelId?: string
    ): Promise<number[]>;

    /**
     * Gets an embeddings interface for this provider.
     * Note: Not all providers support embeddings.
     */
    getEmbeddings?(): Promise<any>;

    /**
     * Fetches the list of available models for this provider.
     * @deprecated Use listModels() instead
     */
    getAvailableModels?(): Promise<string[]>;

    /**
     * Lists available models with their details.
     * This is the preferred method for getting models.
     */
    listModels(): Promise<LLMModelInfo[]>;

    /**
     * Lists available embedding models with their details.
     * Only relevant for providers that support embeddings.
     */
    listEmbeddingModels?(): Promise<LLMModelInfo[]>;

    /**
     * Tests the connection to the provider with the specified model.
     */
    testConnection(_modelId: string): Promise<{success: boolean, message: string}>;

    /**
     * Checks if the provider is configured and ready (e.g., API key set).
     */
    isConfigured(): boolean;

    /**
     * Gets the current configuration for this provider.
     */
    getConfig(): LLMProviderConfig;

    /**
     * Updates the configuration for this provider.
     */
    updateConfig(_config: any): Promise<void>;

    /**
     * Loads the configuration for this provider from settings.
     */
    loadConfig(): Promise<void>;

    /**
     * Gets the required configuration fields for this provider.
     */
    getConfigurationFields(): Array<{id: string, name: string, description: string, required: boolean, type: 'string' | 'boolean' | 'number' | 'select' | 'file' | 'directory', options?: string[]}>;
}

export abstract class LLMProvider {
  protected config: LLMConfig;

  constructor(_config: any) {
    this.config = _config;
  }

    abstract generate(_params: LLMGenerateParams): Promise<LLMGenerateResult>;
    abstract getModels(): Promise<string[]>;
    abstract validateConfig(_config: any): boolean;

    protected getModelId(params: LLMGenerateParams): string {
      return params.modelId || this.config.modelId;
    }

    protected getTemperature(params: LLMGenerateParams): number {
      return params.temperature ?? this.config.temperature ?? 0.7;
    }

    protected getMaxTokens(params: LLMGenerateParams): number {
      return params.maxTokens ?? this.config.maxTokens ?? 2000;
    }
}

export { LLMGenerateParams, LLMGenerateResult };
