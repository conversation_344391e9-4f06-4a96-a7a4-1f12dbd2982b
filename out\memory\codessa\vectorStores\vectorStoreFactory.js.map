{"version": 3, "file": "vectorStoreFactory.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/vectorStores/vectorStoreFactory.ts"], "names": [], "mappings": ";;;AAEA,2DAAwD;AACxD,2DAAwD;AACxD,+DAA4D;AAC5D,4CAAyC;AAEzC;;GAEG;AACH,MAAa,kBAAkB;IAC7B;;;;;SAKK;IACE,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,UAAsB;QACxE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,IAAI,EAAE,CAAC,CAAC;QAE/D,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,QAAQ;gBACX,OAAO,IAAI,qCAAiB,CAAC,UAAU,CAAC,CAAC;YAC3C,KAAK,QAAQ;gBACX,OAAO,IAAI,qCAAiB,CAAC,UAAU,CAAC,CAAC;YAC3C,KAAK,UAAU;gBACb,OAAO,IAAI,yCAAmB,CAAC,UAAU,CAAC,CAAC;YAC7C;gBACE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,8BAA8B,IAAI,uCAAuC,CAAC,CAAC;gBAChG,OAAO,IAAI,qCAAiB,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;CACF;AAtBD,gDAsBC", "sourcesContent": ["import { Embeddings } from '../../../agents/workflows/corePolyfill';\nimport { IVectorStore } from '../../types';\nimport { MemoryVectorStore } from './memoryVectorStore';\nimport { ChromaVectorStore } from './chromaVectorStore';\nimport { PineconeVectorStore } from './pineconeVectorStore';\nimport { Logger } from '../../../logger';\n\n/**\n * Factory for creating vector stores\n */\nexport class VectorStoreFactory {\n  /**\n     * Create a vector store\n     * @param type Vector store type\n     * @param embeddings Embeddings\n     * @returns Vector store instance\n     */\n  public static async createVectorStore(type: string, embeddings: Embeddings): Promise<IVectorStore> {\n    Logger.instance.info(`Creating vector store of type: ${type}`);\n\n    switch (type) {\n      case 'memory':\n        return new MemoryVectorStore(embeddings);\n      case 'chroma':\n        return new ChromaVectorStore(embeddings);\n      case 'pinecone':\n        return new PineconeVectorStore(embeddings);\n      default:\n        Logger.instance.warn(`Unknown vector store type: ${type}, falling back to memory vector store`);\n        return new MemoryVectorStore(embeddings);\n    }\n  }\n}\n"]}