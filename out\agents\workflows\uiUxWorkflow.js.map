{"version": 3, "file": "uiUxWorkflow.js", "sourceRoot": "", "sources": ["../../../src/agents/workflows/uiUxWorkflow.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;AAgBH,gDAsJC;AAKD,8DAkJC;AAzTD,mCAAkC;AAElC,yDAAsD;AACtD,yCAAsC;AAEtC,sEAAmG;AAInG;;GAEG;AACH,SAAgB,kBAAkB,CAChC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,cAAqB,EACrB,iBAAwB,EACxB,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;IAEzD,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,wBAAwB,GAAG,eAAO,CAAC,eAAe,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;IAC9H,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;IACtG,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IAC7F,MAAM,gBAAgB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAClG,MAAM,aAAa,GAAG,eAAO,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;IACvF,MAAM,oBAAoB,GAAG,eAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;IAClH,MAAM,kBAAkB,GAAG,eAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACvG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,iDAAiD;IACjD,MAAM,iBAAiB,GAAG;QACxB,KAAK,CAAC,mBAAmB,CAAC,YAAiB,EAAE,YAAoB;YAC/D,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;oBACrC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC;qBACxC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,YAAY,YAAY,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,YAAY,aAAa,EAAE,KAAK,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,KAAK,CAAC,uBAAuB,CAAC,KAAa;YACzC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE;wBACN,IAAI,EAAE,CAAC,OAAO,CAAC;qBAChB;iBACF,CAAC,CAAC;gBAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,+BAA+B,CAAC,CAAC;gBAClF,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;gBACjF,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,KAAK,CAAC,iBAAiB,CAAC,YAAiB;YACvC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;oBACrC,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAsB;wBAC9B,IAAI,EAAE,SAAuB;wBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC;qBACpC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACxD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;KACF,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,uBAAuB,EAAE,IAAI,EAAE,SAAS,EAAE;QACpG,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QAC/G,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE;QACpG,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACzG,EAAE,IAAI,EAAE,4BAA4B,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;QACrG,EAAE,IAAI,EAAE,sBAAsB,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;QACnG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC7G,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAEjG,iBAAiB;QACjB,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE;QACxG,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,UAAU,EAAE;KAC/G,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,+BAA+B;QAC/B,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,uBAAuB,KAAK,EAAE;gBACpC,MAAM,EAAE,aAAa;gBACrB,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,sCAAsC;YACtC,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,mBAAmB;gBACtC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,eAAe;gBACvB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,wBAAwB;YACxB,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,aAAa;YACb,oBAAoB;YACpB,kBAAkB;YAClB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,OAAwB;QACvC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC;QACzC,QAAQ,EAAE;YACR,aAAa,EAAE,iBAAiB;SACjC;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CACvC,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,aAAoB,EACpB,cAAqB,EACrB,QAAoC,EAAE;IAEtC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,IAAI,EAAE,CAAC,CAAC;IAEhE,eAAe;IACf,MAAM,SAAS,GAAG,eAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,0BAA0B,GAAG,eAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,aAAa,CAAC,CAAC;IAChI,MAAM,qBAAqB,GAAG,eAAO,CAAC,eAAe,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;IACjH,MAAM,uBAAuB,GAAG,eAAO,CAAC,eAAe,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,aAAa,CAAC,CAAC;IACvH,MAAM,2BAA2B,GAAG,eAAO,CAAC,eAAe,CAAC,0BAA0B,EAAE,0BAA0B,EAAE,cAAc,CAAC,CAAC;IACpI,MAAM,eAAe,GAAG,eAAO,CAAC,eAAe,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAChG,MAAM,iBAAiB,GAAG,eAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACpG,MAAM,UAAU,GAAG,eAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEhE,uCAAuC;IACvC,MAAM,SAAS,GAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACvD,eAAO,CAAC,cAAc,CAAC,QAAQ,KAAK,EAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CACpE,CAAC;IAEF,wDAAwD;IACxD,MAAM,wBAAwB,GAAG;QAC/B,KAAK,CAAC,oBAAoB,CAAC,UAAe;YACxC,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBACnC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,MAAoB;wBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC;qBACpC;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAC3D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,KAAa;YAC1C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,qCAAoB,CAAC,cAAc,CAAC;oBACzD,KAAK;oBACL,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE;wBACN,IAAI,EAAE,CAAC,WAAW,CAAC;qBACpB;iBACF,CAAC,CAAC;gBAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,gCAAgC,CAAC,CAAC;gBACnF,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;gBAClF,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,KAAK,CAAC,4BAA4B,CAAC,cAAmB;YACpD,IAAI,CAAC;gBACH,MAAM,qCAAoB,CAAC,SAAS,CAAC;oBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;oBACvC,QAAQ,EAAE;wBACR,MAAM,EAAE,QAAwB;wBAChC,IAAI,EAAE,UAAwB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,eAAe,CAAC;qBAC3C;iBACF,CAAC,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACnE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;YACrF,CAAC;QACH,CAAC;KACF,CAAC;IAEF,eAAe;IACf,MAAM,KAAK,GAAgB;QACzB,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,yBAAyB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvG,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1H,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,SAAS,EAAE;QACvH,EAAE,IAAI,EAAE,iCAAiC,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,0BAA0B,EAAE,IAAI,EAAE,SAAS,EAAE;QAChI,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,0BAA0B,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE;QAClH,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;QACtG,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;QAE/F,iBAAiB;QACjB,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,0BAA0B,EAAE,IAAI,EAAE,UAAU,EAAE;QACnH,EAAE,IAAI,EAAE,iCAAiC,EAAE,MAAM,EAAE,0BAA0B,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,EAAE,UAAU,EAAE;KAClI,CAAC;IAEF,uCAAuC;IACvC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,sCAAsC;QACtC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,uBAAuB,KAAK,EAAE;gBACpC,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;YAEH,6CAA6C;YAC7C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ,KAAK,mBAAmB;gBACtC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBACvB,MAAM,EAAE,sBAAsB;gBAC9B,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6BAA6B;IAC7B,MAAM,QAAQ,GAAoB;QAChC,EAAE;QACF,IAAI;QACJ,WAAW;QACX,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE;YACL,SAAS;YACT,0BAA0B;YAC1B,qBAAqB;YACrB,uBAAuB;YACvB,2BAA2B;YAC3B,eAAe;YACf,iBAAiB;YACjB,UAAU;YACV,GAAG,SAAS;SACb;QACD,KAAK;QACL,WAAW,EAAE,OAAO;QACpB,aAAa,EAAE,OAAwB;QACvC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe,CAAC;QACpD,QAAQ,EAAE;YACR,aAAa,EAAE,wBAAwB;SACxC;KACF,CAAC;IAEF,oBAAoB;IACpB,mCAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE5C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["/**\n * Codessa UI-UX Workflow\n *\n * This module provides workflow templates for UI/UX design tasks:\n * - User research\n * - Wireframing\n * - Prototyping\n * - Usability testing\n * - Implementation\n */\n\nimport { ITool } from '../../tools/tool.ts.backup';\nimport { Agent } from '../agentUtilities/agent';\nimport { <PERSON>ssa } from './graph';\nimport { GraphDefinition, GraphNode, GraphEdge, OperationMode } from './types';\nimport { workflowRegistry } from './workflowRegistry';\nimport { Logger } from '../../logger';\nimport { StructuredTool } from './corePolyfill';\nimport { codessaMemoryProvider as codessaMemoryManager } from '../../memory/codessa/codessaMemory';\nimport { MemorySource, MemoryType } from '../../memory/types';\n\n\n/**\n * Create a UI-UX workflow for design and user experience tasks\n */\nexport function createUIUXWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  designerAgent: Agent,\n  developerAgent: Agent,\n  userResearchAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating UI-UX workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const requirementsAnalysisNode = Codessa.createAgentNode('requirements-analysis', 'Requirements Analysis', userResearchAgent);\n  const userResearchNode = Codessa.createAgentNode('user-research', 'User Research', userResearchAgent);\n  const wireframingNode = Codessa.createAgentNode('wireframing', 'Wireframing', designerAgent);\n  const designSystemNode = Codessa.createAgentNode('design-system', 'Design System', designerAgent);\n  const prototypeNode = Codessa.createAgentNode('prototype', 'Prototype', designerAgent);\n  const usabilityTestingNode = Codessa.createAgentNode('usability-testing', 'Usability Testing', userResearchAgent);\n  const implementationNode = Codessa.createAgentNode('implementation', 'Implementation', developerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create a memory manager for the UI-UX workflow\n  const uiUxMemoryManager = {\n    async storeDesignArtifact(artifactData: any, artifactType: string): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(artifactData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['ui-ux', 'design', artifactType]\n          }\n        });\n        Logger.instance.info(`Saved ${artifactType} to memory`);\n      } catch (error) {\n        Logger.instance.error(`Failed to save ${artifactType} to memory:`, error);\n      }\n    },\n\n    async retrieveDesignArtifacts(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5,\n          filter: {\n            tags: ['ui-ux']\n          }\n        });\n\n        Logger.instance.info(`Retrieved ${memories.length} design artifacts from memory`);\n        return memories;\n      } catch (error) {\n        Logger.instance.error('Failed to retrieve design artifacts from memory:', error);\n        return [];\n      }\n    },\n\n    async storeUserResearch(researchData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(researchData),\n          metadata: {\n            source: 'user' as MemorySource,\n            type: 'insight' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['ui-ux', 'research', 'user']\n          }\n        });\n        Logger.instance.info('Saved user research to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save user research to memory:', error);\n      }\n    }\n  };\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-requirements', source: 'input', target: 'requirements-analysis', type: 'default' },\n    { name: 'requirements-to-research', source: 'requirements-analysis', target: 'user-research', type: 'default' },\n    { name: 'research-to-wireframing', source: 'user-research', target: 'wireframing', type: 'default' },\n    { name: 'wireframing-to-design-system', source: 'wireframing', target: 'design-system', type: 'default' },\n    { name: 'design-system-to-prototype', source: 'design-system', target: 'prototype', type: 'default' },\n    { name: 'prototype-to-testing', source: 'prototype', target: 'usability-testing', type: 'default' },\n    { name: 'testing-to-implementation', source: 'usability-testing', target: 'implementation', type: 'default' },\n    { name: 'implementation-to-output', source: 'implementation', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'testing-to-wireframing', source: 'usability-testing', target: 'wireframing', type: 'feedback' },\n    { name: 'implementation-to-testing', source: 'implementation', target: 'usability-testing', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect wireframing to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `wireframing-to-tool-${index}`,\n        source: 'wireframing',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to design system\n      edges.push({\n        name: `tool-${index}-to-design-system`,\n        source: `tool-${index}`,\n        target: 'design-system',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      requirementsAnalysisNode,\n      userResearchNode,\n      wireframingNode,\n      designSystemNode,\n      prototypeNode,\n      usabilityTestingNode,\n      implementationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'ui-ux' as OperationMode,\n    tags: ['ui', 'ux', 'design', 'usability'],\n    metadata: {\n      memoryManager: uiUxMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n\n/**\n * Create a specialized UI component design workflow\n */\nexport function createUIComponentWorkflow(\n  id: string,\n  name: string,\n  description: string,\n  designerAgent: Agent,\n  developerAgent: Agent,\n  tools: (ITool | StructuredTool)[] = []\n): GraphDefinition {\n  Logger.instance.info(`Creating UI Component workflow: ${name}`);\n\n  // Create nodes\n  const inputNode = Codessa.createInputNode('input', 'Input');\n  const componentSpecificationNode = Codessa.createAgentNode('component-specification', 'Component Specification', designerAgent);\n  const designExplorationNode = Codessa.createAgentNode('design-exploration', 'Design Exploration', designerAgent);\n  const accessibilityReviewNode = Codessa.createAgentNode('accessibility-review', 'Accessibility Review', designerAgent);\n  const componentImplementationNode = Codessa.createAgentNode('component-implementation', 'Component Implementation', developerAgent);\n  const unitTestingNode = Codessa.createAgentNode('unit-testing', 'Unit Testing', developerAgent);\n  const documentationNode = Codessa.createAgentNode('documentation', 'Documentation', developerAgent);\n  const outputNode = Codessa.createOutputNode('output', 'Output');\n\n  // Add tool nodes if tools are provided\n  const toolNodes: GraphNode[] = tools.map((tool, index) =>\n    Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool)\n  );\n\n  // Create a memory manager for the UI Component workflow\n  const uiComponentMemoryManager = {\n    async storeComponentDesign(designData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(designData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'code' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['ui', 'component', 'design']\n          }\n        });\n        Logger.instance.info('Saved component design to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save component design to memory:', error);\n      }\n    },\n\n    async retrieveComponentDesigns(query: string): Promise<any[]> {\n      try {\n        const memories = await codessaMemoryManager.searchMemories({\n          query,\n          limit: 5,\n          filter: {\n            tags: ['component']\n          }\n        });\n\n        Logger.instance.info(`Retrieved ${memories.length} component designs from memory`);\n        return memories;\n      } catch (error) {\n        Logger.instance.error('Failed to retrieve component designs from memory:', error);\n        return [];\n      }\n    },\n\n    async storeAccessibilityGuidelines(guidelinesData: any): Promise<void> {\n      try {\n        await codessaMemoryManager.addMemory({\n          content: JSON.stringify(guidelinesData),\n          metadata: {\n            source: 'system' as MemorySource,\n            type: 'document' as MemoryType,\n            timestamp: new Date().toISOString(),\n            tags: ['ui', 'component', 'accessibility']\n          }\n        });\n        Logger.instance.info('Saved accessibility guidelines to memory');\n      } catch (error) {\n        Logger.instance.error('Failed to save accessibility guidelines to memory:', error);\n      }\n    }\n  };\n\n  // Create edges\n  const edges: GraphEdge[] = [\n    { name: 'input-to-specification', source: 'input', target: 'component-specification', type: 'default' },\n    { name: 'specification-to-exploration', source: 'component-specification', target: 'design-exploration', type: 'default' },\n    { name: 'exploration-to-accessibility', source: 'design-exploration', target: 'accessibility-review', type: 'default' },\n    { name: 'accessibility-to-implementation', source: 'accessibility-review', target: 'component-implementation', type: 'default' },\n    { name: 'implementation-to-testing', source: 'component-implementation', target: 'unit-testing', type: 'default' },\n    { name: 'testing-to-documentation', source: 'unit-testing', target: 'documentation', type: 'default' },\n    { name: 'documentation-to-output', source: 'documentation', target: 'output', type: 'default' },\n\n    // Feedback loops\n    { name: 'testing-to-implementation', source: 'unit-testing', target: 'component-implementation', type: 'feedback' },\n    { name: 'implementation-to-accessibility', source: 'component-implementation', target: 'accessibility-review', type: 'feedback' }\n  ];\n\n  // Add tool edges if tools are provided\n  if (toolNodes.length > 0) {\n    // Connect design exploration to tools\n    toolNodes.forEach((_, index) => {\n      edges.push({\n        name: `exploration-to-tool-${index}`,\n        source: 'design-exploration',\n        target: `tool-${index}`,\n        type: 'conditional'\n      });\n\n      // Connect tools back to accessibility review\n      edges.push({\n        name: `tool-${index}-to-accessibility`,\n        source: `tool-${index}`,\n        target: 'accessibility-review',\n        type: 'default'\n      });\n    });\n  }\n\n  // Create workflow definition\n  const workflow: GraphDefinition = {\n    id,\n    name,\n    description,\n    version: '1.0.0',\n    nodes: [\n      inputNode,\n      componentSpecificationNode,\n      designExplorationNode,\n      accessibilityReviewNode,\n      componentImplementationNode,\n      unitTestingNode,\n      documentationNode,\n      outputNode,\n      ...toolNodes\n    ],\n    edges,\n    startNodeId: 'input',\n    operationMode: 'ui-ux' as OperationMode,\n    tags: ['ui', 'component', 'design', 'accessibility'],\n    metadata: {\n      memoryManager: uiComponentMemoryManager\n    }\n  };\n\n  // Register workflow\n  workflowRegistry.registerWorkflow(workflow);\n\n  return workflow;\n}\n"]}