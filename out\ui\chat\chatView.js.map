{"version": 3, "file": "chatView.js", "sourceRoot": "", "sources": ["../../../src/ui/chat/chatView.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,yCAAsC;AACtC,uCAAuC;AAEvC,2EAAwE;AAExE,8DAA2D;AAC3D,uEAAiE;AACjE,qDAAkD;AAElD,qDAAyD;AA4CzD,SAAS,WAAW,CAAC,QAAgB;IACnC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACjE,QAAQ,SAAS,EAAE,CAAC;QAClB,gBAAgB;QAChB,KAAK,KAAK,CAAC,CAAC,OAAO,WAAW,CAAC;QAC/B,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,MAAM,CAAC,CAAC,OAAO,YAAY,CAAC;QACjC,KAAK,KAAK,CAAC,CAAC,OAAO,WAAW,CAAC;QAC/B,KAAK,MAAM,CAAC,CAAC,OAAO,YAAY,CAAC;QACjC,KAAK,KAAK,CAAC,CAAC,OAAO,WAAW,CAAC;QAC/B,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;QACnC,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,MAAM,CAAC,CAAC,OAAO,YAAY,CAAC;QAEjC,4BAA4B;QAC5B,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,IAAI,CAAC,CAAC,OAAO,eAAe,CAAC;QAClC,KAAK,UAAU,CAAC,CAAC,OAAO,eAAe,CAAC;QACxC,KAAK,KAAK,CAAC,CAAC,OAAO,UAAU,CAAC;QAC9B,KAAK,KAAK,CAAC,CAAC,OAAO,iBAAiB,CAAC;QACrC,KAAK,MAAM,CAAC,CAAC,OAAO,WAAW,CAAC;QAChC,KAAK,KAAK,CAAC,CAAC,OAAO,WAAW,CAAC;QAC/B,KAAK,KAAK,CAAC,CAAC,OAAO,iBAAiB,CAAC;QACrC,KAAK,OAAO,CAAC,CAAC,OAAO,uBAAuB,CAAC;QAC7C,KAAK,KAAK,CAAC,CAAC,OAAO,UAAU,CAAC;QAC9B,KAAK,KAAK,CAAC,CAAC,OAAO,UAAU,CAAC;QAC9B,KAAK,KAAK,CAAC,CAAC,OAAO,2BAA2B,CAAC;QAE/C,eAAe;QACf,KAAK,KAAK,CAAC,CAAC,OAAO,iBAAiB,CAAC;QACrC,KAAK,KAAK,CAAC,CAAC,OAAO,oBAAoB,CAAC;QACxC,KAAK,MAAM,CAAC,CAAC,OAAO,yEAAyE,CAAC;QAC9F,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,MAAM,CAAC,CAAC,OAAO,mEAAmE,CAAC;QACxF,KAAK,KAAK,CAAC,CAAC,OAAO,+BAA+B,CAAC;QACnD,KAAK,MAAM,CAAC,CAAC,OAAO,2EAA2E,CAAC;QAChG,KAAK,KAAK,CAAC,CAAC,OAAO,yCAAyC,CAAC;QAC7D,KAAK,KAAK,CAAC,CAAC,OAAO,gDAAgD,CAAC;QACpE,KAAK,KAAK,CAAC,CAAC,OAAO,iDAAiD,CAAC;QAErE,sBAAsB;QACtB,KAAK,KAAK,CAAC,CAAC,OAAO,iBAAiB,CAAC;QACrC,KAAK,KAAK,CAAC,CAAC,OAAO,mBAAmB,CAAC;QACvC,KAAK,IAAI,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACrC,KAAK,KAAK,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACtC,KAAK,KAAK,CAAC,CAAC,OAAO,qBAAqB,CAAC;QACzC,KAAK,IAAI,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACrC,KAAK,IAAI,CAAC,CAAC,OAAO,6BAA6B,CAAC;QAChD,KAAK,KAAK,CAAC,CAAC,OAAO,qBAAqB,CAAC;QACzC,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,KAAK,CAAC,CAAC,OAAO,uCAAuC,CAAC;QAC3D,KAAK,KAAK,CAAC,CAAC,OAAO,sCAAsC,CAAC;QAC1D,KAAK,KAAK,CAAC,CAAC,OAAO,mBAAmB,CAAC;QACvC,KAAK,KAAK,CAAC,CAAC,OAAO,+CAA+C,CAAC;QACnE,KAAK,KAAK,CAAC,CAAC,OAAO,+BAA+B,CAAC;QACnD,KAAK,KAAK,CAAC,CAAC,OAAO,qCAAqC,CAAC;QACzD,KAAK,KAAK,CAAC,CAAC,OAAO,yCAAyC,CAAC;QAC7D,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,MAAM,CAAC,CAAC,OAAO,gCAAgC,CAAC;QACrD,KAAK,WAAW,CAAC,CAAC,OAAO,0BAA0B,CAAC;QACpD,KAAK,IAAI,CAAC,CAAC,OAAO,yBAAyB,CAAC;QAC5C,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,OAAO,CAAC,CAAC,OAAO,2BAA2B,CAAC;QAEjD,iBAAiB;QACjB,KAAK,IAAI,CAAC,CAAC,OAAO,wBAAwB,CAAC;QAC3C,KAAK,KAAK,CAAC,CAAC,OAAO,wBAAwB,CAAC;QAC5C,KAAK,KAAK,CAAC,CAAC,OAAO,wBAAwB,CAAC;QAC5C,KAAK,IAAI,CAAC,CAAC,OAAO,wBAAwB,CAAC;QAC3C,KAAK,KAAK,CAAC,CAAC,OAAO,UAAU,CAAC;QAC9B,KAAK,KAAK,CAAC,CAAC,OAAO,UAAU,CAAC;QAC9B,KAAK,QAAQ,CAAC,CAAC,OAAO,mBAAmB,CAAC;QAC1C,KAAK,IAAI,CAAC,CAAC,OAAO,eAAe,CAAC;QAClC,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,KAAK,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAC9C,KAAK,MAAM,CAAC,CAAC,OAAO,oBAAoB,CAAC;QACzC,KAAK,OAAO,CAAC,CAAC,OAAO,qBAAqB,CAAC;QAC3C,KAAK,IAAI,CAAC,CAAC,OAAO,eAAe,CAAC;QAClC,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;QACnC,KAAK,OAAO,CAAC,CAAC,OAAO,cAAc,CAAC;QACpC,KAAK,IAAI,CAAC,CAAC,OAAO,eAAe,CAAC;QAClC,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;QACnC,KAAK,IAAI,CAAC,CAAC,OAAO,eAAe,CAAC;QAClC,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;QACnC,KAAK,GAAG,CAAC,CAAC,OAAO,UAAU,CAAC;QAC5B,KAAK,GAAG,CAAC,CAAC,OAAO,UAAU,CAAC;QAC5B,KAAK,IAAI,CAAC,CAAC,OAAO,eAAe,CAAC;QAClC,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;QACnC,KAAK,GAAG,CAAC,CAAC,OAAO,mBAAmB,CAAC;QACrC,KAAK,IAAI,CAAC,CAAC,OAAO,qBAAqB,CAAC;QACxC,KAAK,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;QAC9B,KAAK,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;QAC9B,KAAK,MAAM,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACvC,KAAK,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC;QAChC,KAAK,KAAK,CAAC,CAAC,OAAO,aAAa,CAAC;QACjC,KAAK,KAAK,CAAC,CAAC,OAAO,yBAAyB,CAAC;QAC7C,KAAK,OAAO,CAAC,CAAC,OAAO,yBAAyB,CAAC;QAC/C,KAAK,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC;QAChC,KAAK,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC;QAChC,KAAK,IAAI,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACrC,KAAK,MAAM,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACvC,KAAK,KAAK,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACtC,KAAK,KAAK,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACtC,KAAK,MAAM,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACvC,KAAK,KAAK,CAAC,CAAC,OAAO,mBAAmB,CAAC;QACvC,KAAK,MAAM,CAAC,CAAC,OAAO,mBAAmB,CAAC;QACxC,KAAK,KAAK,CAAC,CAAC,OAAO,iBAAiB,CAAC;QACrC,KAAK,KAAK,CAAC,CAAC,OAAO,iBAAiB,CAAC;QACrC,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,GAAG,CAAC,CAAC,OAAO,YAAY,CAAC;QAC9B,KAAK,IAAI,CAAC,CAAC,OAAO,gBAAgB,CAAC;QACnC,KAAK,KAAK,CAAC,CAAC,OAAO,yBAAyB,CAAC;QAC7C,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;QACnC,KAAK,IAAI,CAAC,CAAC,OAAO,eAAe,CAAC;QAClC,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;QACnC,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,IAAI,CAAC,CAAC,OAAO,cAAc,CAAC;QACjC,KAAK,OAAO,CAAC,CAAC,OAAO,cAAc,CAAC;QACpC,KAAK,QAAQ,CAAC,CAAC,OAAO,eAAe,CAAC;QACtC,KAAK,QAAQ,CAAC,CAAC,OAAO,eAAe,CAAC;QACtC,KAAK,YAAY,CAAC,CAAC,OAAO,YAAY,CAAC;QACvC,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,MAAM,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACvC,KAAK,OAAO,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACxC,KAAK,MAAM,CAAC,CAAC,OAAO,oBAAoB,CAAC;QACzC,KAAK,KAAK,CAAC,CAAC,OAAO,oBAAoB,CAAC;QACxC,KAAK,MAAM,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACvC,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,MAAM,CAAC,CAAC,OAAO,YAAY,CAAC;QACjC,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAChC,KAAK,MAAM,CAAC,CAAC,OAAO,YAAY,CAAC;QAEjC,qBAAqB;QACrB,KAAK,MAAM,CAAC,CAAC,OAAO,sBAAsB,CAAC;QAC3C,KAAK,KAAK,CAAC,CAAC,OAAO,WAAW,CAAC;QAC/B,KAAK,KAAK,CAAC,CAAC,OAAO,4BAA4B,CAAC;QAChD,KAAK,KAAK,CAAC,CAAC,OAAO,iBAAiB,CAAC;QACrC,KAAK,MAAM,CAAC,CAAC,OAAO,WAAW,CAAC;QAChC,KAAK,QAAQ,CAAC,CAAC,OAAO,oBAAoB,CAAC;QAC3C,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC;QAEhC,wBAAwB;QACxB,KAAK,KAAK,CAAC,CAAC,OAAO,iBAAiB,CAAC;QACrC,KAAK,SAAS,CAAC,CAAC,OAAO,qBAAqB,CAAC;QAC7C,KAAK,KAAK,CAAC,CAAC,OAAO,qBAAqB,CAAC;QACzC,KAAK,OAAO,CAAC,CAAC,OAAO,iBAAiB,CAAC;QAEvC,YAAY;QACZ,KAAK,OAAO,CAAC,CAAC,OAAO,0BAA0B,CAAC;QAEhD,gBAAgB;QAChB,KAAK,YAAY,CAAC,CAAC,OAAO,oBAAoB,CAAC;QAC/C,KAAK,eAAe,CAAC,CAAC,OAAO,oBAAoB,CAAC;QAElD,iBAAiB;QACjB,OAAO,CAAC,CAAC,OAAO,0BAA0B,CAAC;IAC7C,CAAC;AACH,CAAC;AA+BD;;GAEG;AACH,MAAa,WAAW;IASO;IARrB,QAAQ,GAAkB,EAAE,CAAC;IAC7B,UAAU,GAAG,KAAK,CAAC;IACnB,aAAa,GAAmB,EAAE,CAAC;IACnC,QAAQ,GAAiB,EAAE,CAAC;IAC5B,cAAc,GAAW,MAAM,CAAC;IAChC,SAAS,GAAW,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAC5C,WAAW,GAAW,cAAc,CAAC;IAE7C,YAA6B,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;IAAI,CAAC;IAE3D,UAAU,CAAC,OAAoB;QACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAEM,WAAW;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa,CAAC,SAAiB;QACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;IACpE,CAAC;IAEM,aAAa,CAAC,cAA2B;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,CAAC,CAAC;QAC3E,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;QACxC,CAAC;IACH,CAAC;IAEM,aAAa;QAClB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEM,aAAa,CAAC,UAAmB;QACtC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAEM,eAAe,CAAC,IAAkB;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAEM,gBAAgB;QACrB,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAEM,kBAAkB;QACvB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,IAAI;QACf,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE;gBACnD,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;gBACxD,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI;QACf,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAQvC,aAAa,CAAC,CAAC;YAElB,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;gBAC/B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;gBACnC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACpD,IAAI;oBACJ,IAAI,EAAE,EAAE;oBACR,OAAO,EAAE,EAAE;oBACX,IAAI,EAAE,EAAE;iBACT,CAAC,CAAC,CAAC;gBACJ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;gBACrC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc,IAAI,MAAM,CAAC;gBACrD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;gBACnD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;gBACzD,eAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,CAAC,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEM,gBAAgB,CAAC,IAAa;QACnC,IAAI,CAAC,SAAS,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACzC,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,cAAc,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,CAAC;gBACf,EAAE,EAAE,MAAM;gBACV,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,kBAAkB,CAAC,SAAiB,EAAE,UAAmB;QAC9D,MAAM,QAAQ,GAAG,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACxC,MAAM,SAAS,GAAe;YAC5B,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,UAAU,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACxD,eAAe,EAAE,SAAS;YAC1B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QAClE,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;YAClB,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAC3F,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,YAAY,CAAC,QAAgB;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;YAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,CAAC,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;CACF;AAnJD,kCAmJC;AAED,MAAa,SAAS;IACZ,MAAM,CAAC,YAAY,CAAwB;IAC3C,MAAM,CAAU,QAAQ,GAAG,aAAa,CAAC;IAChC,MAAM,CAAsB;IACrC,KAAK,GAAiB,IAAI,CAAC;IAC3B,YAAY,GAAwB,IAAI,CAAC;IAChC,KAAK,CAAiB;IACtB,aAAa,CAAa;IAC1B,YAAY,GAAwB,EAAE,CAAC;IAChD,YAAY,CAAc;IAC1B,aAAa,CAAe;IAC5B,gBAAgB,CAAkB;IAClC,YAAY,GAAG,KAAK,CAAC;IACrB,aAAa,GAAG,KAAK,CAAC;IACtB,cAAc,GAAG,KAAK,CAAC;IACvB,kBAAkB,CAA6C;IAC/D,QAAQ,CAA0B;IAE1C,YACE,KAA0B,EAC1B,KAAY,EACZ,YAAwB,EACxB,OAAgC,EAChC,OAAuB,MAAmC;QAE1D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,gBAAgB,GAAG,oCAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,2BAA2B;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE7E,4CAA4C;QAC5C,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,EAAE;YACpC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,CAAC;QACH,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE5B,+BAA+B;QAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE7G,mCAAmC;QACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAe;QAC9C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,eAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,gEAAgE;YAChE,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAElD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,iBAAiB,OAAO,YAAY,CAAC,CAAC;YACxD,CAAC;YAED,4CAA4C;YAC5C,MAAM;YAEN,0BAA0B;YAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YAEnB,4CAA4C;YAC5C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBACpC,OAAO,EAAE,eAAe;gBACxB,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;aACrC,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,sBAAsB,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBACpC,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE,2BAA2B,KAAK,EAAE;aACzC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,OAAuB;QACzD,uBAAuB;QACvB,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,EAAE,CAAC;YAC9C,eAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE,OAAO,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QAED,gDAAgD;QAChD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC5C,eAAM,CAAC,IAAI,CAAC,gDAAgD,EAAE,OAAO,CAAC,CAAC;YACvE,OAAO;QACT,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,+BAA+B,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,OAAO,KAAK,aAAa,EAAE,CAAC;gBAC9B,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;oBAC3D,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACjD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;gBACxE,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,OAAO,KAAK,WAAW,EAAE,CAAC;gBAC5B,+DAA+D;gBAC/D,MAAM,MAAM,GAKP,EAAE,CAAC;gBAER,IAAI,CAAC;oBACH,qCAAqC;oBACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAE,IAAI,CAAC,YAAgD,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;oBAE/G,qBAAqB;oBACrB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;wBAC/B,IAAI,CAAC;4BACH,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;4BACnD,IAAI,KAAK,EAAE,CAAC;gCACV,MAAM,CAAC,IAAI,CAAC;oCACV,EAAE,EAAE,KAAK,CAAC,EAAE;oCACZ,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;oCACnC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;oCACpC,QAAQ,EAAE,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,EAAE,EAAE;iCACtC,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,UAAU,EAAE,CAAC;4BACpB,eAAM,CAAC,IAAI,CAAC,0BAA0B,OAAO,GAAG,EAAE,UAAU,CAAC,CAAC;wBAChE,CAAC;oBACH,CAAC;oBAED,sCAAsC;oBACtC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;wBAC9B,IAAI,EAAE,cAAuB;wBAC7B,MAAM;wBACN,cAAc,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE;qBACrC,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;oBAC7C,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;gBACzD,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,OAAO,KAAK,iBAAiB,EAAE,CAAC;gBAClC,yCAAyC;gBACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,cAAuB;oBAC7B,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE;oBAC7B,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,EAAE;oBACjC,WAAW,EAAE,IAAI,CAAC,KAAK,EAAE,WAAW,IAAI,EAAE;iBAC3C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,oDAAoD;YACpD,IAAI,CAAC;gBACH,QAAQ,OAAO,EAAE,CAAC;oBAChB,KAAK,aAAa;wBAChB,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;4BACtD,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;wBAC7D,CAAC;wBACD,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBACxC,MAAM;oBACR,KAAK,YAAY;wBACf,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;wBACrC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC;wBACjE,MAAM;oBACR,KAAK,oBAAoB;wBACvB,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;4BACjB,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAc,CAAC,CAAC,CAAC;4BACrH,IAAI,OAAO,EAAE,CAAC;gCACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gCAClE,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gCAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;4BAC7E,CAAC;wBACH,CAAC;wBACD,MAAM;oBACR,KAAK,cAAc;wBACjB,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;4BACjB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;4BAC7C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;4BAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uBAAuB,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;wBAC9E,CAAC;wBACD,MAAM;oBACR,KAAK,aAAa;wBAChB,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC;wBACzC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;wBAClG,MAAM;oBACR,KAAK,cAAc;wBACjB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBACjC,MAAM;oBACR,KAAK,aAAa;wBAChB,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;wBAClD,MAAM;oBAER,KAAK,cAAc;wBACjB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBACjC,MAAM;oBAER,KAAK,YAAY;wBACf,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC/B,MAAM;oBAER,KAAK,WAAW;wBACd,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;4BACvC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC7C,CAAC;6BAAM,CAAC;4BACN,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzD,CAAC;wBACD,MAAM;oBAER,KAAK,gBAAgB;wBACnB,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;wBACnC,MAAM;oBAER,KAAK,eAAe;wBAClB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAClC,MAAM;oBAER,KAAK,YAAY;wBACf,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC/B,MAAM;oBAER,KAAK,YAAY;wBACf,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC/B,MAAM;oBAER,KAAK,cAAc;wBACjB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBACjC,MAAM;oBAER,KAAK,WAAW;wBACd,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBAC9B,MAAM;oBAER,KAAK,YAAY;wBACf,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;4BACrD,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAiC,CAAC,CAAC;wBAC1E,CAAC;6BAAM,CAAC;4BACN,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;wBAC5D,CAAC;wBACD,MAAM;oBAER,KAAK,gBAAgB;wBACnB,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;4BAC7D,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;wBACrD,CAAC;6BAAM,CAAC;4BACN,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;wBACxE,CAAC;wBACD,MAAM;oBAER,KAAK,aAAa;wBAChB,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;4BACvD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC/C,CAAC;6BAAM,CAAC;4BACN,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC/D,CAAC;wBACD,MAAM;oBAER,KAAK,QAAQ;wBACX,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;wBAC3B,MAAM;oBAER;wBACE,eAAM,CAAC,IAAI,CAAC,6BAA6B,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B,CAAC;gBAC1F,eAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,IAAI,EAAE,KAAK,CAAC,CAAC;gBAE5D,kCAAkC;gBAClC,MAAM,QAAQ,GAAgB;oBAC5B,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;oBACzB,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,UAAU,YAAY,EAAE;oBACjC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC;gBAEF,6BAA6B;gBAC7B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACvC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBAE/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,YAAqB;oBAC3B,OAAO,EAAE,QAAQ;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC1E,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAChD,8DAA8D;QAC9D,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CACpC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,CAChE,CAAC;QACF,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CACnC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,CAAC,CACjE,CAAC;QACF,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CACrC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,kBAAkB,EAAE,MAAM,EAAE,aAAa,CAAC,CACnG,CAAC;QAEF,oDAAoD;QACpD,MAAM,KAAK,GAAG,IAAA,gBAAQ,GAAE,CAAC;QAEzB,OAAO;;;;8FAImF,OAAO,CAAC,SAAS,qBAAqB,OAAO,CAAC,SAAS,8BAA8B,OAAO,CAAC,SAAS,uBAAuB,KAAK;;0BAEtM,QAAQ;0BACR,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6BAwDP,KAAK,UAAU,SAAS;;gBAErC,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,8CAA8C;YAC9C,MAAM,eAAe,GAKhB,EAAE,CAAC;YAER,mDAAmD;YACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAE,IAAI,CAAC,YAAgD,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAE/G,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACnD,IAAI,KAAK,EAAE,CAAC;oBACV,eAAe,CAAC,IAAI,CAAC;wBACnB,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;wBACpC,QAAQ,EAAE,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,EAAE,EAAE;qBACtC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACrD,CAAC;YAED,sDAAsD;YACtD,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,aAAsB;gBAC5B,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;gBACzC,WAAW,EAAE,IAAI,CAAC,YAAY;gBAC9B,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,SAAS;gBACxC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,WAAW,IAAI,SAAS;gBAC9C,eAAe;gBACf,cAAc,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE;gBACpC,gBAAgB,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,eAAe;gBACrD,uBAAuB,EAAE,IAAI,CAAC,KAAK,EAAE,WAAW,IAAI,EAAE;aACvD,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBAC/C,UAAU,EAAE,eAAe,CAAC,MAAM;gBAClC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE;gBAClC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,MAAM;aACrD,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAE7C,oDAAoD;YACpD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,cAAc;gBACpB,MAAM,EAAE,eAAe;gBACvB,cAAc,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE;aACrC,CAAC,CAAC;YAEH,0BAA0B;YAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE;gBAC7B,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,eAAe;gBAC9C,WAAW,EAAE,IAAI,CAAC,KAAK,EAAE,WAAW,IAAI,EAAE;aAC3C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAE/D,wBAAwB;YACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,qBAAqB;gBAC3B,OAAO,EAAE,sDAAsD;aAChE,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,YAAY,GAAgB;gBAChC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;gBACzB,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,wEAAwE;gBACjF,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC3C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;YAE/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,YAAY;aACtB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,YAAY,CACxB,YAAwB,EACxB,KAAY,EACZ,OAAgC,EAChC,OAAuB,MAAmC,EAC1D,SAA4B,MAAM,CAAC,UAAU,CAAC,MAAM;QAEpD,sCAAsC;QACtC,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;YAC3B,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,gCAAgC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,SAAS,CAAC,QAAQ,EAClB,SAAS,EACT,MAAM,EACN;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;gBAC1C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,CAAC;gBACpD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,cAAc,CAAC;aAClD;SACF,CACF,CAAC;QAEF,SAAS,CAAC,YAAY,GAAG,IAAI,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACpF,CAAC;IAEO,OAAO;QACb,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEM,OAAO;QACZ,qBAAqB;QACrB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;QAEnC,oCAAoC;QACpC,IAAI,SAAS,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YACpC,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,uBAAU,CAAC,eAAe,EAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC9C,EAAE,EAAE,QAAQ,CAAC,UAAU;gBACvB,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,4DAA4D;gBACvF,YAAY,EAAE,QAAQ,CAAC,YAAY,EAAE;aACtC,CAAC,CAAC,CAAC;YAEJ,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,YAAY;iBACxB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAoB;QAClD,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACvF,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;YAE3E,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,IAAI;iBACX,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,iCAAiC,CAAC;YAChG,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oCAAoC,YAAY,EAAE,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBACjC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBAClC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;gBAEpC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBAE/B,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;oBACzB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;wBAC9B,IAAI,EAAE,wBAAwB;wBAC9B,YAAY,EAAE,KAAK;qBACpB,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,IAAY,EAAE,cAA8B,EAAE;QACzE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACtC,MAAM,WAAW,GAAgB;YAC7B,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,WAAW,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;SAChE,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACtC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAE/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;QAC9E,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QAExF,IAAI,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QAC/D,4BAAgB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,aAAa,GAAG,+BAA+B,CAAC;QAEpD,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACrD,CAAC;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;YAC3D,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACpD,CAAC;YACD,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAC/D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAClF,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YACpD,MAAM,cAAc,GAAG,YAAY,EAAE,QAAQ,CAAC;YAC9C,MAAM,SAAS,GAAG,YAAY,EAAE,SAAS,CAAC;YAE1C,MAAM,eAAe,GAAiB;gBAClC,SAAS,EAAE;oBACP,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC7E,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACzD,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;iBACtD;gBACD,SAAS,EAAE;oBACP,WAAW,EAAE,cAAc,EAAE,GAAG,CAAC,MAAM;oBACvC,SAAS,EAAE,SAAS,IAAI,cAAc,CAAC,CAAC,CAAC;wBACrC,IAAI,EAAE,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC;wBACvC,KAAK,EAAE;4BACH,KAAK,EAAE,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC;4BAC/C,GAAG,EAAE,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC;yBAC9C;qBACJ,CAAC,CAAC,CAAC,SAAS;oBACb,gBAAgB,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;iBACpF;gBACD,KAAK,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC9D,gBAAgB,EAAE;oBACd,QAAQ,EAAE,UAAU,SAAS,EAAE;oBAC/B,QAAQ,EAAE,CAAC,KAAyC,EAAE,EAAE;wBACpD,IAAI,KAAK,EAAE,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;4BAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC7E,CAAC;oBACL,CAAC;oBACD,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK;iBACnD;aACJ,CAAC;YAEF,mDAAmD;YACnD,MAAM,cAAc,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC;gBAC3C,MAAM,EAAE,IAAI;gBACZ,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,eAAe;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC1D,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,SAAS,EAAE,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;YAEtF,yFAAyF;YACzF,MAAM,oBAAoB,GAA8B;gBACpD,IAAI,EAAE,uBAAuB;gBAC7B,MAAM,EAAE,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE,EAAE,SAAS,EAAE,aAAa,CAAC,IAAI,EAAE;gBACpE,MAAM,EAAE,EAAE,OAAO,EAAE,eAAe,CAAC,EAAE,EAAE,SAAS,EAAE,eAAe,CAAC,IAAI,EAAE;gBACxE,OAAO,EAAE;oBACL,WAAW,EAAE,YAAY;oBACzB,OAAO,EAAE,cAAc,CAAC,MAAM;iBACjC;gBACD,QAAQ,EAAE;oBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,SAAS;oBACnE,iBAAiB,EAAE,SAAS;iBAC/B;aACJ,CAAC;YAEF,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC9D,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,gBAAgB,CAAC,CAAC;YAE5E,8DAA8D;YAC9D,iDAAiD;YACjD,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAK,IAAI,CAAC,KAAoC,EAAE,EAAE,IAAI,MAAM,CAAC;YACjH,MAAM,gBAAgB,GAAG,MAAM,eAAe,CAAC,GAAG,CAAC;gBAC/C,MAAM,EAAE,gBAAgB;gBACxB,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,eAAe;aAC3B,CAAC,CAAC;YAEH,aAAa,GAAG,gBAAgB,EAAE,MAAM,IAAI,oCAAoC,CAAC;QAErF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,aAAa,GAAG,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QACvF,CAAC;gBAAS,CAAC;YACP,iFAAiF;YACjF,MAAM,gBAAgB,GAAgB;gBAClC,EAAE,EAAE,IAAA,gBAAQ,GAAE;gBACd,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,aAAa;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC/C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;YAE/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YACnF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;YAEzF,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;YACpC,4BAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAES,sBAAsB,CAAC,OAAe;QAC5C,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QACvE,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,MAAM,CAAC;QAClG,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QACxH,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QACtG,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,UAAU,CAAC;QAC/E,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,uBAAwB,IAAI,CAAC,YAAkD,EAAE,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAC7H,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAChD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,SAAiB;QAChD,IAAI,CAAC;YACH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;gBAC3D,OAAO;YACT,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,SAAS,EAAE,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,EAAE,gBAAgB,EAAE,GAAG,wDAAa,8BAA8B,GAAC,CAAC;YAC1E,MAAM,EAAE,eAAe,EAAE,GAAG,wDAAa,2BAA2B,GAAC,CAAC;YACtE,MAAM,EAAE,aAAa,EAAE,GAAG,wDAAa,4BAA4B,GAAC,CAAC;YAErE,MAAM,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;YAE1C,MAAM,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE9C,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,gBAAgB,CAAC,YAAY,CACjC,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,aAAa,EAClB,aAAa,EACb,eAAe,CAChB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;YAC1D,mCAAmC;YACnC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC;YACnF,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,aAAa,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACjD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;gBAC9D,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAgB,EAAE,EAAE,CAChD,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,CACzE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEf,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC7C,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE;gBAChD,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC;aAC9C,CAAC,CAAC;YAEH,IAAI,GAAG,EAAE,CAAC;gBACR,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;gBACvE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAgB;QAC7C,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YACxF,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;YAE7D,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yDAAyD,CAAC,CAAC;QAClG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAC9C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;gBAC/C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;YACtE,CAAC;YACD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAC/B,IAAI,CAAC;YACD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;YAC3D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAE1B,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;gBACnE,OAAO;YACX,CAAC;YAED,2FAA2F;YAC3F,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YACtC,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC;YAChC,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,WAAW,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5H,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;QACxC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC5C,MAAM,EAAE,+CAA+C;gBACvD,WAAW,EAAE,wBAAwB;aACtC,CAAC,CAAC;YAEH,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,cAAc,GAAgB;oBAClC,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC3B,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,aAAa,IAAI,EAAE;oBAC5B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBAC7C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBAC/B,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC9C,cAAc,EAAE,IAAI;gBACpB,gBAAgB,EAAE,KAAK;gBACvB,aAAa,EAAE,IAAI;gBACnB,SAAS,EAAE,cAAc;gBACzB,OAAO,EAAE;oBACP,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAmB,EAAE,CAAC;YACjC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACxD,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC;oBAC3D,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBAC1D,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAEzC,KAAK,CAAC,IAAI,CAAC;wBACT,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,GAAG,CAAC,MAAM;wBAChB,OAAO,EAAE,WAAW;wBACpB,IAAI,EAAE,QAAQ;qBACf,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;oBACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;oBACrE,SAAS;gBACX,CAAC;YACH,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,gCAAgC;gBAChC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC1C,CAAC;gBAED,wDAAwD;gBACxD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnD,MAAM,WAAW,GAAG,oBAAoB,QAAQ,GAAG,CAAC;gBAEpD,gEAAgE;gBAChE,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBAE9C,gBAAgB;gBAChB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBAC/B,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,6BAA6B,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,YAAY,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC9C,cAAc,EAAE,IAAI;gBACpB,gBAAgB,EAAE,KAAK;gBACvB,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE;oBACP,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;iBACvD;gBACD,SAAS,EAAE,yBAAyB;aACrC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,MAAM,MAAM,GAAmB,EAAE,CAAC;YAClC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACxD,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,OAAO,CAAC;oBAC5D,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAEzC,mDAAmD;oBACnD,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAC9D,MAAM,OAAO,GAAG,QAAQ,QAAQ,WAAW,aAAa,EAAE,CAAC;oBAE3D,MAAM,CAAC,IAAI,CAAC;wBACV,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,GAAG,CAAC,MAAM;wBAChB,OAAO,EAAE,OAAO;wBAChB,IAAI,EAAE,QAAQ;qBACf,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,GAAG,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC1D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;oBACtE,SAAS;gBACX,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,iCAAiC;gBACjC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;oBACzB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBACzC,CAAC;gBAED,yDAAyD;gBACzD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzD,MAAM,WAAW,GAAG,qBAAqB,SAAS,GAAG,CAAC;gBAEtD,sEAAsE;gBACtE,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gBAE/C,gBAAgB;gBAChB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBAC/B,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,YAAY,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,kBAAkB,CAAC,SAAqB;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,4BAA4B;YAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,CAAC;YAExE,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBAC9D,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,mBAAmB;gBAErC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;oBACxB,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBACzC,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,sCAAsC,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,wCAAwC;gBAC1D,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,YAAY,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAgB;QAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;YACtD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,eAAe,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC1F,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,OAAO,EAAE,CAAC,CAAC;YAErE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,OAAO;iBACf,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,YAAY,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IACO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,uBAAU,CAAC,eAAe,EAAE,CAAC;YAC/C,MAAM,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9E,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,UAAU,EAAE,CAAC;YAClD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACrC,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE;gBAC5B,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;aACrC,CAAC,CAAC,CAAC;YACJ,MAAM,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAEtC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;gBAEH,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;wBAC9B,IAAI,EAAE,cAAc;wBACpB,KAAK,EAAE,YAAY;qBACpB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,UAAmB;QACrD,IAAI,CAAC;YACH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,uBAAuB,CAAC,CAAC;gBAC1D,OAAO;YACT,CAAC;YAED,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,kBAAkB,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YAChG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;YAE3E,MAAM,SAAS,GAAG,uBAAU,CAAC,eAAe,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC;YAElE,IAAI,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;gBACrC,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACrC,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE;oBAC5B,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;iBACrC,CAAC,CAAC,CAAC;gBAEJ,MAAM,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAEtC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;gBAEH,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;wBAC9B,IAAI,EAAE,cAAc;wBACpB,KAAK,EAAE,YAAY;qBACpB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;;AAtqCH,8BAuqCC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { logger } from '../../logger';\nimport { getNonce } from '../../utils';\nimport { Agent } from '../../agents/agentUtilities/agent';\nimport { AgentManager } from '../../agents/agentUtilities/agentManager';\nimport { IOperationMode } from '../../agents/agentModes/operationMode';\nimport { AudioService } from '../../services/audioService';\nimport { TTSSettingsView } from '../settings/ttsSettingsSection';\nimport { llmService } from '../../llm/llmService';\nimport { AgentContext } from '../../types/agent';\nimport { statusBarManager } from '../feedback/statusBar';\n\n// Message types for webview communication\ninterface WebviewMessage {\n  command?: string;\n  type?: string;\n  text?: string;\n  state?: boolean;\n  mode?: string;\n  provider?: string;\n  model?: string;\n  agentId?: string;\n  agentName?: string;\n  description?: string;\n  timestamp?: number;\n}\n\n/**\n * Defines the structured message format for communication between agents.\n * This ensures a consistent and predictable data flow, especially from the\n * receiver to the supervisor agent.\n */\ninterface AgentCommunicationMessage {\n    type: 'agent-to-agent-stream';\n    source: {\n        agentId: string;\n        agentName: string;\n    };\n    target: {\n        agentId: string;\n        agentName: string;\n    };\n    payload: {\n        contentType: 'text/plain' | 'application/json';\n        content: string; // The actual output from the source agent\n    };\n    metadata: {\n        timestamp: string; // ISO 8601 format\n        conversationId: string;\n        originalMessageId: string; // The ID of the initial user message\n    };\n}\n\n\nfunction getMimeType(filePath: string): string {\n  const extension = filePath.split('.').pop()?.toLowerCase() || '';\n  switch (extension) {\n    // Image formats\n    case 'png': return 'image/png';\n    case 'jpg': return 'image/jpeg';\n    case 'jpeg': return 'image/jpeg';\n    case 'gif': return 'image/gif';\n    case 'webp': return 'image/webp';\n    case 'bmp': return 'image/bmp';\n    case 'svg': return 'image/svg+xml';\n    case 'ico': return 'image/vnd.microsoft.icon';\n    case 'tif': return 'image/tiff';\n    case 'tiff': return 'image/tiff';\n\n    // Text / Markup / Documents\n    case 'txt': return 'text/plain';\n    case 'md': return 'text/markdown';\n    case 'markdown': return 'text/markdown';\n    case 'mdx': return 'text/mdx';\n    case 'rtf': return 'application/rtf';\n    case 'html': return 'text/html';\n    case 'htm': return 'text/html';\n    case 'xml': return 'application/xml';\n    case 'xhtml': return 'application/xhtml+xml';\n    case 'css': return 'text/css';\n    case 'csv': return 'text/csv';\n    case 'tsv': return 'text/tab-separated-values';\n\n    // Office / PDF\n    case 'pdf': return 'application/pdf';\n    case 'doc': return 'application/msword';\n    case 'docx': return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\n    case 'xls': return 'application/vnd.ms-excel';\n    case 'xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\n    case 'ppt': return 'application/vnd.ms-powerpoint';\n    case 'pptx': return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';\n    case 'odt': return 'application/vnd.oasis.opendocument.text';\n    case 'ods': return 'application/vnd.oasis.opendocument.spreadsheet';\n    case 'odp': return 'application/vnd.oasis.opendocument.presentation';\n\n    // Archives / Packages\n    case 'zip': return 'application/zip';\n    case 'tar': return 'application/x-tar';\n    case 'gz': return 'application/gzip';\n    case 'tgz': return 'application/gzip';\n    case 'bz2': return 'application/x-bzip2';\n    case 'xz': return 'application/x-xz';\n    case '7z': return 'application/x-7z-compressed';\n    case 'rar': return 'application/vnd.rar';\n    case 'jar': return 'application/java-archive';\n    case 'war': return 'application/java-archive';\n    case 'ear': return 'application/java-archive';\n    case 'deb': return 'application/vnd.debian.binary-package';\n    case 'rpm': return 'application/x-redhat-package-manager';\n    case 'msi': return 'application/x-msi';\n    case 'exe': return 'application/vnd.microsoft.portable-executable';\n    case 'dmg': return 'application/x-apple-diskimage';\n    case 'pkg': return 'application/vnd.apple.installer+xml';\n    case 'apk': return 'application/vnd.android.package-archive';\n    case 'aar': return 'application/java-archive';\n    case 'appx': return 'application/vnd.microsoft.appx';\n    case 'debuginfo': return 'application/octet-stream';\n    case 'so': return 'application/x-sharedlib';\n    case 'dll': return 'application/x-msdownload';\n    case 'dylib': return 'application/x-mach-binary';\n\n    // Code & Scripts\n    case 'js': return 'application/javascript';\n    case 'mjs': return 'application/javascript';\n    case 'cjs': return 'application/javascript';\n    case 'ts': return 'application/typescript';\n    case 'tsx': return 'text/tsx';\n    case 'jsx': return 'text/jsx';\n    case 'coffee': return 'text/coffeescript';\n    case 'py': return 'text/x-python';\n    case 'pyc': return 'application/octet-stream';\n    case 'pyo': return 'application/octet-stream';\n    case 'pyd': return 'application/octet-stream';\n    case 'java': return 'text/x-java-source';\n    case 'class': return 'application/java-vm';\n    case 'kt': return 'text/x-kotlin';\n    case 'kts': return 'text/x-kotlin';\n    case 'swift': return 'text/x-swift';\n    case 'cs': return 'text/x-csharp';\n    case 'cpp': return 'text/x-c++src';\n    case 'cc': return 'text/x-c++src';\n    case 'cxx': return 'text/x-c++src';\n    case 'c': return 'text/x-c';\n    case 'h': return 'text/x-c';\n    case 'hh': return 'text/x-c++src';\n    case 'hpp': return 'text/x-c++src';\n    case 'm': return 'text/x-objectivec';\n    case 'mm': return 'text/x-objectivec++';\n    case 'go': return 'text/x-go';\n    case 'rs': return 'text/rust';\n    case 'dart': return 'application/dart';\n    case 'rb': return 'text/x-ruby';\n    case 'erb': return 'text/x-ruby';\n    case 'php': return 'application/x-httpd-php';\n    case 'phtml': return 'application/x-httpd-php';\n    case 'pl': return 'text/x-perl';\n    case 'pm': return 'text/x-perl';\n    case 'sh': return 'application/x-sh';\n    case 'bash': return 'application/x-sh';\n    case 'zsh': return 'application/x-sh';\n    case 'ksh': return 'application/x-sh';\n    case 'fish': return 'application/x-sh';\n    case 'ps1': return 'text/x-powershell';\n    case 'psm1': return 'text/x-powershell';\n    case 'cmd': return 'application/cmd';\n    case 'bat': return 'application/bat';\n    case 'asm': return 'text/x-asm';\n    case 's': return 'text/x-asm';\n    case 'hs': return 'text/x-haskell';\n    case 'lhs': return 'text/x-literate-haskell';\n    case 'erl': return 'text/x-erlang';\n    case 'ex': return 'text/x-elixir';\n    case 'exs': return 'text/x-elixir';\n    case 'lua': return 'text/x-lua';\n    case 'jl': return 'text/x-julia';\n    case 'scala': return 'text/x-scala';\n    case 'groovy': return 'text/x-groovy';\n    case 'gradle': return 'text/x-groovy';\n    case 'dockerfile': return 'text/plain';\n    case 'env': return 'text/plain';\n    case 'json': return 'application/json';\n    case 'jsonc': return 'application/json';\n    case 'yaml': return 'application/x-yaml';\n    case 'yml': return 'application/x-yaml';\n    case 'toml': return 'application/toml';\n    case 'ini': return 'text/plain';\n    case 'cfg': return 'text/plain';\n    case 'conf': return 'text/plain';\n    case 'log': return 'text/plain';\n    case 'lock': return 'text/plain';\n\n    // Markup & Templates\n    case 'xaml': return 'application/xaml+xml';\n    case 'ejs': return 'text/html';\n    case 'hbs': return 'text/x-handlebars-template';\n    case 'njk': return 'text/x-nunjucks';\n    case 'twig': return 'text/html';\n    case 'svelte': return 'application/svelte';\n    case 'vue': return 'text/x-vue';\n\n    // Data / Config Formats\n    case 'sql': return 'application/sql';\n    case 'graphql': return 'application/graphql';\n    case 'gql': return 'application/graphql';\n    case 'proto': return 'text/x-protobuf';\n\n    // Notebooks\n    case 'ipynb': return 'application/x-ipynb+json';\n\n    // CI/CD configs\n    case 'travis.yml': return 'application/x-yaml';\n    case 'gitlab-ci.yml': return 'application/x-yaml';\n\n    // Default binary\n    default: return 'application/octet-stream';\n  }\n}\n\nexport interface ChatMessage {\n  id: string;\n  role: 'user' | 'assistant' | 'system' | 'error';\n  content: string;\n  timestamp: number;\n  isThinking?: boolean;\n  attachments?: AttachedFile[];\n}\n\ninterface ChatBranch {\n  id: string;\n  name: string;\n  description?: string;\n  parentMessageId?: string;\n  messages: ChatMessage[];\n  isActive: boolean;\n  createdAt: number;\n}\n\n/**\n * Represents a file attached to the chat\n */\nexport interface AttachedFile {\n  name: string;\n  path: string;\n  content: string;\n  type: string;\n}\n\n/**\n * Manages a chat session's state and messages\n */\nexport class ChatSession {\n  private messages: ChatMessage[] = [];\n  private processing = false;\n  private attachedFiles: AttachedFile[] = [];\n  private branches: ChatBranch[] = [];\n  private activeBranchId: string = 'main';\n  private sessionId: string = `session_${Date.now()}`;\n  private sessionName: string = 'Chat Session';\n\n  constructor(private readonly context: vscode.ExtensionContext) { }\n\n  public addMessage(message: ChatMessage): void {\n    this.messages.push(message);\n  }\n\n  public getMessages(): ChatMessage[] {\n    return [...this.messages];\n  }\n\n  public removeMessage(messageId: string): void {\n    this.messages = this.messages.filter(msg => msg.id !== messageId);\n  }\n\n  public updateMessage(updatedMessage: ChatMessage): void {\n    const index = this.messages.findIndex(msg => msg.id === updatedMessage.id);\n    if (index !== -1) {\n      this.messages[index] = updatedMessage;\n    }\n  }\n\n  public clearMessages(): void {\n    this.messages = [];\n  }\n\n  public getProcessing(): boolean {\n    return this.processing;\n  }\n\n  public setProcessing(processing: boolean): void {\n    this.processing = processing;\n  }\n\n  public addAttachedFile(file: AttachedFile): void {\n    this.attachedFiles.push(file);\n  }\n\n  public getAttachedFiles(): AttachedFile[] {\n    return [...this.attachedFiles];\n  }\n\n  public clearAttachedFiles(): void {\n    this.attachedFiles = [];\n  }\n\n  public async save(): Promise<void> {\n    try {\n      await this.context.globalState.update('chatSession', {\n        messages: this.messages,\n        processing: this.processing,\n        attachedFiles: this.attachedFiles.map(file => file.name),\n        branches: this.branches,\n        activeBranchId: this.activeBranchId,\n        sessionId: this.sessionId,\n        sessionName: this.sessionName\n      });\n      logger.debug('Chat session saved successfully');\n    } catch (error) {\n      logger.error('Failed to save chat session:', error);\n    }\n  }\n\n  public async load(): Promise<void> {\n    try {\n      const saved = this.context.globalState.get<{\n        messages: ChatMessage[];\n        processing: boolean;\n        attachedFiles: string[];\n        branches?: ChatBranch[];\n        activeBranchId?: string;\n        sessionId?: string;\n        sessionName?: string;\n      }>('chatSession');\n\n      if (saved) {\n        this.messages = saved.messages;\n        this.processing = saved.processing;\n        this.attachedFiles = saved.attachedFiles.map(name => ({\n          name,\n          path: '',\n          content: '',\n          type: ''\n        }));\n        this.branches = saved.branches || [];\n        this.activeBranchId = saved.activeBranchId || 'main';\n        this.sessionId = saved.sessionId || this.sessionId;\n        this.sessionName = saved.sessionName || this.sessionName;\n        logger.debug(`Loaded chat session with ${this.messages.length} messages`);\n      }\n    } catch (error) {\n      logger.error('Failed to load chat session:', error);\n    }\n  }\n\n  public createNewSession(name?: string): string {\n    this.sessionId = `session_${Date.now()}`;\n    this.sessionName = name || 'Chat Session';\n    this.messages = [];\n    this.attachedFiles = [];\n    this.branches = [{\n      id: 'main',\n      name: 'Main',\n      messages: [],\n      isActive: true,\n      createdAt: Date.now()\n    }];\n    this.activeBranchId = 'main';\n    return this.sessionId;\n  }\n\n  public branchConversation(messageId: string, branchName?: string): string {\n    const branchId = `branch_${Date.now()}`;\n    const newBranch: ChatBranch = {\n      id: branchId,\n      name: branchName || `Branch ${this.branches.length + 1}`,\n      parentMessageId: messageId,\n      messages: [],\n      isActive: false,\n      createdAt: Date.now()\n    };\n\n    const msgIndex = this.messages.findIndex(m => m.id === messageId);\n    if (msgIndex >= 0) {\n      newBranch.messages = this.messages.slice(0, msgIndex + 1).map(m => ({ ...m }));\n    }\n\n    this.branches.push(newBranch);\n    this.activeBranchId = branchId;\n    this.branches = this.branches.map(b => ({ ...b, isActive: b.id === this.activeBranchId }));\n    return branchId;\n  }\n\n  public switchBranch(branchId: string): void {\n    if (this.branches.find(b => b.id === branchId)) {\n      this.activeBranchId = branchId;\n      this.branches = this.branches.map(b => ({ ...b, isActive: b.id === branchId }));\n    }\n  }\n}\n\nexport class ChatPanel {\n  private static currentPanel: ChatPanel | undefined;\n  private static readonly viewType = 'codessaChat';\n  private readonly _panel: vscode.WebviewPanel;\n  private agent: Agent | null = null;\n  private agentManager: AgentManager | null = null;\n  private readonly _mode: IOperationMode;\n  private readonly _extensionUri: vscode.Uri;\n  private readonly _disposables: vscode.Disposable[] = [];\n  private _chatSession: ChatSession;\n  private _audioService: AudioService;\n  private _ttsSettingsView: TTSSettingsView;\n  private _isRecording = false;\n  private _voiceEnabled = false;\n  private _collaborative = false;\n  private _cancelTokenSource: vscode.CancellationTokenSource | undefined;\n  private _context: vscode.ExtensionContext;\n\n  constructor(\n    panel: vscode.WebviewPanel,\n    agent: Agent,\n    extensionUri: vscode.Uri,\n    context: vscode.ExtensionContext,\n    mode: IOperationMode = 'chat' as unknown as IOperationMode\n  ) {\n    this._panel = panel;\n    this.agent = agent;\n    this.agentManager = AgentManager.getInstance();\n    this._extensionUri = extensionUri;\n    this._context = context;\n    this._mode = mode;\n    this._chatSession = new ChatSession(context);\n    this._audioService = AudioService.getInstance(context);\n    this._ttsSettingsView = TTSSettingsView.getInstance(context);\n    this._isRecording = false;\n\n    // Set initial HTML content\n    this._updateWebview();\n\n    // Listen for panel disposal\n    this._panel.onDidDispose(() => this.dispose(), undefined, this._disposables);\n\n    // Update content when panel becomes visible\n    this._panel.onDidChangeViewState(() => {\n      if (this._panel.visible) {\n        this._updateWebview();\n      }\n    }, null, this._disposables);\n\n    // Handle messages from webview\n    this._panel.webview.onDidReceiveMessage(this._handleWebviewMessage.bind(this), undefined, this._disposables);\n\n    // Send initial data to the webview\n    this._sendInitialData();\n  }\n\n  private async _handleSwitchAgent(agentId: string): Promise<void> {\n    if (!this.agentManager) {\n      logger.error('Agent manager not initialized');\n      return;\n    }\n\n    try {\n      // Get the agent directly since getAgents might not be available\n      const agent = this.agentManager.getAgent(agentId);\n\n      if (!agent) {\n        throw new Error(`Agent with ID ${agentId} not found`);\n      }\n\n      // Store the current agent's state if needed\n      // ...\n\n      // Switch to the new agent\n      this.agent = agent;\n\n      // Update the UI to reflect the agent switch\n      await this._panel.webview.postMessage({\n        command: 'agentSwitched',\n        agentId: agent.id,\n        agentName: agent.name,\n        description: agent.description || ''\n      });\n\n      logger.info(`Switched to agent: ${agent.name} (${agent.id})`);\n    } catch (error) {\n      logger.error(`Failed to switch agent: ${error}`);\n      await this._panel.webview.postMessage({\n        command: 'error',\n        text: `Failed to switch agent: ${error}`\n      });\n    }\n  }\n\n  private async _handleWebviewMessage(message: WebviewMessage): Promise<void> {\n    // Validate the message\n    if (!message || (typeof message !== 'object')) {\n      logger.warn('Received invalid message from webview:', message);\n      return;\n    }\n\n    // Extract command from message with type safety\n    const command = message.command || message.type;\n    if (!command || typeof command !== 'string') {\n      logger.warn('Received message without a valid command/type:', message);\n      return;\n    }\n\n    logger.debug(`Processing webview command: ${command}`, { message });\n\n    try {\n      // Handle agent-related commands\n      if (command === 'switchAgent') {\n        if (message.agentId && typeof message.agentId === 'string') {\n          await this._handleSwitchAgent(message.agentId);\n        } else {\n          throw new Error('Invalid or missing agentId for switchAgent command');\n        }\n        return;\n      }\n\n      if (command === 'getAgents') {\n        // Get all available agents from the manager with proper typing\n        const agents: Array<{\n          id: string;\n          name: string;\n          description: string;\n          isActive: boolean;\n        }> = [];\n\n        try {\n          // Safely get agent IDs with fallback\n          const agentIds = this.agentManager ? (this.agentManager as { getAgentIds: () => string[] }).getAgentIds() : [];\n\n          // Process each agent\n          for (const agentId of agentIds) {\n            try {\n              const agent = this.agentManager?.getAgent(agentId);\n              if (agent) {\n                agents.push({\n                  id: agent.id,\n                  name: agent.name || 'Unnamed Agent',\n                  description: agent.description || '',\n                  isActive: agent.id === this.agent?.id\n                });\n              }\n            } catch (agentError) {\n              logger.warn(`Error processing agent ${agentId}:`, agentError);\n            }\n          }\n\n          // Send the agents list to the webview\n          this._panel.webview.postMessage({\n            type: 'updateAgents' as const,\n            agents,\n            currentAgentId: this.agent?.id || ''\n          });\n        } catch (error) {\n          logger.error('Failed to get agents:', error);\n          throw new Error('Failed to retrieve available agents');\n        }\n        return;\n      }\n\n      if (command === 'getCurrentAgent') {\n        // Send current agent info to the webview\n        this._panel.webview.postMessage({\n          type: 'currentAgent' as const,\n          agentId: this.agent?.id || '',\n          agentName: this.agent?.name || '',\n          description: this.agent?.description || ''\n        });\n        return;\n      }\n\n      // Handle other command types with proper validation\n      try {\n        switch (command) {\n          case 'sendMessage':\n            if (!message.text || typeof message.text !== 'string') {\n              throw new Error('Missing or invalid text for sendMessage');\n            }\n            await this._handleMessage(message.text);\n            break;\n          case 'newSession':\n            this._chatSession.createNewSession();\n            await this._chatSession.save();\n            vscode.window.showInformationMessage('New chat session created');\n            break;\n          case 'branchConversation':\n            if (message.text) {\n              const lastMsg = [...this._chatSession.getMessages()].reverse().find(m => m.content.includes(message.text as string));\n              if (lastMsg) {\n                const branchId = this._chatSession.branchConversation(lastMsg.id);\n                await this._chatSession.save();\n                vscode.window.showInformationMessage(`Branched conversation: ${branchId}`);\n              }\n            }\n            break;\n          case 'switchBranch':\n            if (message.text) {\n              this._chatSession.switchBranch(message.text);\n              await this._chatSession.save();\n              vscode.window.showInformationMessage(`Switched to branch: ${message.text}`);\n            }\n            break;\n          case 'toggleVoice':\n            this._voiceEnabled = !this._voiceEnabled;\n            vscode.window.showInformationMessage(`Voice mode ${this._voiceEnabled ? 'enabled' : 'disabled'}`);\n            break;\n          case 'shareSession':\n            await this._handleShareSession();\n            break;\n          case 'joinSession':\n            await this._handleJoinSession(message.text || '');\n            break;\n\n          case 'openSettings':\n            await this._handleOpenSettings();\n            break;\n\n          case 'exportChat':\n            await this._handleExportChat();\n            break;\n\n          case 'toggleTTS':\n            if (typeof message.state === 'boolean') {\n              await this._handleToggleTTS(message.state);\n            } else {\n              logger.warn('Invalid toggleTTS state:', message.state);\n            }\n            break;\n\n          case 'startRecording':\n            await this._handleStartRecording();\n            break;\n\n          case 'stopRecording':\n            await this._handleStopRecording();\n            break;\n\n          case 'addContext':\n            await this._handleAddContext();\n            break;\n\n          case 'attachFile':\n            await this._handleAttachFile();\n            break;\n\n          case 'getProviders':\n            await this._handleGetProviders();\n            break;\n\n          case 'getModels':\n            await this._handleGetModels();\n            break;\n\n          case 'changeMode':\n            if (message.mode && typeof message.mode === 'string') {\n              await this._handleChangeMode(message.mode as unknown as IOperationMode);\n            } else {\n              logger.warn('Invalid mode for changeMode:', message.mode);\n            }\n            break;\n\n          case 'changeProvider':\n            if (message.provider && typeof message.provider === 'string') {\n              await this._handleChangeProvider(message.provider);\n            } else {\n              logger.warn('Invalid provider for changeProvider:', message.provider);\n            }\n            break;\n\n          case 'changeModel':\n            if (message.model && typeof message.model === 'string') {\n              await this._handleChangeModel(message.model);\n            } else {\n              logger.warn('Invalid model for changeModel:', message.model);\n            }\n            break;\n\n          case 'cancel':\n            await this._handleCancel();\n            break;\n\n          default:\n            logger.warn(`Unknown command received: ${command}`, message);\n        }\n      } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';\n        logger.error(`Error handling command '${command}':`, error);\n\n        // Create error message for the UI\n        const errorMsg: ChatMessage = {\n          id: `error_${Date.now()}`,\n          role: 'error',\n          content: `Error: ${errorMessage}`,\n          timestamp: Date.now()\n        };\n\n        // Update chat session and UI\n        this._chatSession.addMessage(errorMsg);\n        await this._chatSession.save();\n\n        this._panel.webview.postMessage({\n          type: 'addMessage' as const,\n          message: errorMsg\n        });\n      }\n    } catch (error) {\n      logger.error('Error handling webview message:', error);\n    }\n  }\n\n  private _updateWebview() {\n    if (!this._panel) {\n      return;\n    }\n    this._panel.webview.html = this._getHtmlForWebview(this._panel.webview);\n  }\n\n  private _getHtmlForWebview(webview: vscode.Webview): string {\n    // Get the local path to the script and styles for the webview\n    const scriptUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'media', 'chatView.js')\n    );\n    const styleUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'media', 'chatView.css')\n    );\n    const codiconUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this._extensionUri, 'node_modules', '@vscode/codicons', 'dist', 'codicon.css')\n    );\n\n    // Use a nonce to whitelist which scripts can be run\n    const nonce = getNonce();\n\n    return `<!DOCTYPE html>\n        <html lang=\"en\">\n        <head>\n            <meta charset=\"UTF-8\">\n            <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; img-src ${webview.cspSource} data:; style-src ${webview.cspSource} 'unsafe-inline'; font-src ${webview.cspSource}; script-src 'nonce-${nonce}';\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n            <link href=\"${styleUri}\" rel=\"stylesheet\" />\n            <link href=\"${codiconUri}\" rel=\"stylesheet\" />\n            <title>Codessa Chat</title>\n        </head>\n        <body>\n            <div class=\"chat-container\">\n                <div class=\"chat-header\">\n                    <div class=\"header-left\">\n                        <h1>Codessa Chat</h1>\n                        <div class=\"agent-selector\">\n                            <select id=\"agent-selector\" title=\"Select Agent\">\n                                <!-- Agent options will be populated by JavaScript -->\n                            </select>\n                        </div>\n                    </div>\n                    <div class=\"toolbar\">\n                        <button id=\"clear-chat\" title=\"Clear Chat\">\n                            <span class=\"codicon codicon-clear-all\"></span>\n                        </button>\n                        <button id=\"export-chat\" title=\"Export Chat\">\n                            <span class=\"codicon codicon-export\"></span>\n                        </button>\n                        <button id=\"tts-toggle\" title=\"Toggle TTS\">\n                            <span class=\"codicon codicon-unmute\"></span>\n                        </button>\n                        <button id=\"record-audio\" title=\"Record Audio\">\n                            <span class=\"codicon codicon-mic\"></span>\n                        </button>\n                        <button id=\"attach-file\" title=\"Attach File\">\n                            <span class=\"codicon codicon-add\"></span>\n                        </button>\n                        <button id=\"attach-folder\" title=\"Attach Folder\">\n                            <span class=\"codicon codicon-folder-opened\"></span>\n                        </button>\n                        <button id=\"upload-image\" title=\"Upload Image\">\n                            <span class=\"codicon codicon-file-media\"></span>\n                        </button>\n                    </div>\n                </div>\n                \n                <div class=\"chat-messages\" id=\"chat-messages\">\n                    <!-- Messages will be inserted here -->\n                </div>\n                \n                <div class=\"chat-input-container\">\n                    <div class=\"attached-files\" id=\"attached-files\">\n                        <!-- Attached files will be shown here -->\n                    </div>\n                    <div class=\"input-wrapper\">\n                        <textarea id=\"message-input\" placeholder=\"Type your message here...\" rows=\"3\"></textarea>\n                        <button id=\"send-message\" title=\"Send Message\">\n                            <span class=\"codicon codicon-send\"></span>\n                        </button>\n                    </div>\n                </div>\n            </div>\n            \n            <script nonce=\"${nonce}\" src=\"${scriptUri}\"></script>\n        </body>\n        </html>`;\n  }\n\n  private async _sendInitialData() {\n    try {\n      // Get all available agents with proper typing\n      const availableAgents: Array<{\n        id: string;\n        name: string;\n        description: string;\n        isActive: boolean;\n      }> = [];\n\n      // Get agent IDs using type assertion as a fallback\n      const agentIds = this.agentManager ? (this.agentManager as { getAgentIds: () => string[] }).getAgentIds() : [];\n\n      for (const agentId of agentIds) {\n        const agent = this.agentManager?.getAgent(agentId);\n        if (agent) {\n          availableAgents.push({\n            id: agent.id,\n            name: agent.name,\n            description: agent.description || '',\n            isActive: agent.id === this.agent?.id\n          });\n        }\n      }\n\n      if (availableAgents.length === 0) {\n        logger.warn('No agents available in AgentManager');\n      }\n\n      // Send initial data to the webview with proper typing\n      const initialData = {\n        type: 'initialData' as const,\n        messages: this._chatSession.getMessages(),\n        isRecording: this._isRecording,\n        mode: this._mode,\n        model: this.agent?.llmModel || 'default',\n        provider: this.agent?.llmProvider || 'default',\n        availableAgents,\n        currentAgentId: this.agent?.id || '',\n        currentAgentName: this.agent?.name || 'Unknown Agent',\n        currentAgentDescription: this.agent?.description || ''\n      };\n\n      logger.debug('Sending initial data to webview:', {\n        agentCount: availableAgents.length,\n        currentAgent: this.agent?.id || '',\n        messageCount: this._chatSession.getMessages().length\n      });\n\n      this._panel.webview.postMessage(initialData);\n\n      // Also send a separate agent update for consistency\n      this._panel.webview.postMessage({\n        type: 'updateAgents',\n        agents: availableAgents,\n        currentAgentId: this.agent?.id || ''\n      });\n\n      // Send current agent info\n      this._panel.webview.postMessage({\n        type: 'currentAgent',\n        agentId: this.agent?.id || '',\n        agentName: this.agent?.name || 'Unknown Agent',\n        description: this.agent?.description || ''\n      });\n    } catch (error) {\n      logger.error('Failed to send initial data to webview:', error);\n\n      // Send error to webview\n      this._panel.webview.postMessage({\n        type: 'initializationError',\n        message: 'Failed to initialize chat session. Please try again.'\n      });\n\n      // Log the error to the chat\n      const errorMessage: ChatMessage = {\n        id: `error-${Date.now()}`,\n        role: 'error',\n        content: 'Failed to initialize chat session. Some features may not be available.',\n        timestamp: Date.now()\n      };\n\n      this._chatSession.addMessage(errorMessage);\n      await this._chatSession.save();\n\n      this._panel.webview.postMessage({\n        type: 'addMessage',\n        message: errorMessage\n      });\n    }\n  }\n\n  public static createOrShow(\n    extensionUri: vscode.Uri,\n    agent: Agent,\n    context: vscode.ExtensionContext,\n    mode: IOperationMode = 'chat' as unknown as IOperationMode,\n    column: vscode.ViewColumn = vscode.ViewColumn.Beside\n  ) {\n    // If we already have a panel, show it\n    if (ChatPanel.currentPanel) {\n      ChatPanel.currentPanel._panel.reveal(column);\n      return;\n    }\n\n    // Otherwise, create a new panel\n    const panel = vscode.window.createWebviewPanel(\n      ChatPanel.viewType,\n      'Codessa',\n      column,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true,\n        localResourceRoots: [\n          vscode.Uri.joinPath(extensionUri, 'media'),\n          vscode.Uri.joinPath(extensionUri, 'out', 'compiled'),\n          vscode.Uri.joinPath(extensionUri, 'node_modules'),\n        ]\n      }\n    );\n\n    ChatPanel.currentPanel = new ChatPanel(panel, agent, extensionUri, context, mode);\n  }\n\n  private _update() {\n    this._updateWebview();\n  }\n\n  public dispose(): void {\n    // Clean up resources\n    this._disposables.forEach(disposable => disposable.dispose());\n    this._cancelTokenSource?.dispose();\n\n    // Clear the current panel reference\n    if (ChatPanel.currentPanel === this) {\n      ChatPanel.currentPanel = undefined;\n    }\n  }\n\n  private async _handleGetProviders(): Promise<void> {\n    try {\n      const providers = llmService.getAllProviders();\n      const providerList = providers.map(provider => ({\n        id: provider.providerId,\n        name: provider.providerId, // Using providerId as name since providerName doesn't exist\n        isConfigured: provider.isConfigured()\n      }));\n\n      if (this._panel?.webview) {\n        this._panel.webview.postMessage({\n          type: 'providers',\n          providers: providerList\n        });\n      }\n    } catch (error) {\n      logger.error('Failed to get providers:', error);\n      vscode.window.showErrorMessage('Failed to get providers.');\n    }\n  }\n\n  private async _handleChangeMode(mode: IOperationMode): Promise<void> {\n    try {\n      await vscode.workspace.getConfiguration('codessa').update('operationMode', mode, true);\n      vscode.window.showInformationMessage(`Operation mode changed to: ${mode}`);\n\n      if (this._panel?.webview) {\n        this._panel.webview.postMessage({\n          type: 'modeChanged',\n          mode: mode\n        });\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to change operation mode';\n      logger.error('Error changing operation mode:', error);\n      vscode.window.showErrorMessage(`Failed to change operation mode: ${errorMessage}`);\n    }\n  }\n\n  private async _handleCancel(): Promise<void> {\n    try {\n      if (this._cancelTokenSource) {\n        this._cancelTokenSource.cancel();\n        this._cancelTokenSource.dispose();\n        this._cancelTokenSource = undefined;\n\n        this._chatSession.setProcessing(false);\n        await this._chatSession.save();\n\n        if (this._panel?.webview) {\n          this._panel.webview.postMessage({\n            type: 'processingStateChanged',\n            isProcessing: false\n          });\n        }\n\n        vscode.window.showInformationMessage('Operation cancelled');\n      }\n    } catch (error) {\n      logger.error('Error cancelling operation:', error);\n      vscode.window.showErrorMessage('Failed to cancel operation');\n    }\n  }\n\n  private async _handleMessage(text: string, attachments: AttachedFile[] = []): Promise<void> {\n    if (!text.trim() && attachments.length === 0) {\n        return;\n    }\n\n    const messageId = `msg_${Date.now()}`;\n    const userMessage: ChatMessage = {\n        id: messageId,\n        role: 'user',\n        content: text,\n        timestamp: Date.now(),\n        attachments: attachments.length > 0 ? attachments : undefined\n    };\n\n    this._chatSession.addMessage(userMessage);\n    this._chatSession.setProcessing(true);\n    await this._chatSession.save();\n\n    this._panel.webview.postMessage({ type: 'addMessage', message: userMessage });\n    this._panel.webview.postMessage({ type: 'processingStateChanged', isProcessing: true });\n\n    this._cancelTokenSource = new vscode.CancellationTokenSource();\n    statusBarManager.setActivityIndicator(true);\n    let finalResponse = 'An unexpected error occurred.';\n\n    try {\n        if (!this.agentManager) {\n            throw new Error('Agent manager not initialized');\n        }\n        const receiverAgent = this.agentManager.getReceiverAgent();\n        if (!receiverAgent) {\n            throw new Error('Receiver agent not available');\n        }\n        const supervisorAgent = this.agentManager.getSupervisorAgent();\n        if (!supervisorAgent) {\n            throw new Error('Supervisor agent not available. The system cannot proceed.');\n        }\n\n        const activeEditor = vscode.window.activeTextEditor;\n        const activeDocument = activeEditor?.document;\n        const selection = activeEditor?.selection;\n\n        const agentRunContext: AgentContext = {\n            variables: {\n                message: { id: messageId, content: text, attachments, timestamp: Date.now() },\n                chatHistory: this._chatSession.getMessages().slice(0, -1),\n                attachedFiles: this._chatSession.getAttachedFiles()\n            },\n            workspace: {\n                currentFile: activeDocument?.uri.fsPath,\n                selection: selection && activeDocument ? {\n                    text: activeDocument.getText(selection),\n                    range: {\n                        start: activeDocument.offsetAt(selection.start),\n                        end: activeDocument.offsetAt(selection.end)\n                    }\n                } : undefined,\n                workspaceFolders: vscode.workspace.workspaceFolders?.map(f => f.uri.fsPath) || []\n            },\n            tools: new Map(Array.from(this.agent?.tools?.entries() || [])),\n            streamingContext: {\n                streamId: `stream-${messageId}`,\n                onStream: (event: { type: string; content?: string }) => {\n                    if (event?.type === 'data' && event.content) {\n                        this._panel.webview.postMessage({ type: 'token', token: event.content });\n                    }\n                },\n                cancellationToken: this._cancelTokenSource.token\n            },\n        };\n\n        // Step 1: Process input through the receiver agent\n        const receiverResult = await receiverAgent.run({\n            prompt: text,\n            mode: 'receive',\n            context: agentRunContext\n        });\n\n        if (!receiverResult?.output) {\n            throw new Error('Receiver agent produced no output.');\n        }\n\n        logger.debug('Receiver agent processed input:', { processed: receiverResult.output });\n\n        // Step 2: Format the receiver's output into a structured JSON message for the supervisor\n        const communicationMessage: AgentCommunicationMessage = {\n            type: 'agent-to-agent-stream',\n            source: { agentId: receiverAgent.id, agentName: receiverAgent.name },\n            target: { agentId: supervisorAgent.id, agentName: supervisorAgent.name },\n            payload: {\n                contentType: 'text/plain',\n                content: receiverResult.output,\n            },\n            metadata: {\n                timestamp: new Date().toISOString(),\n                conversationId: this._chatSession.getMessages()[0]?.id || messageId,\n                originalMessageId: messageId,\n            }\n        };\n\n        const supervisorPrompt = JSON.stringify(communicationMessage);\n        logger.debug('Sending the following JSON to supervisor:', supervisorPrompt);\n\n        // Step 3: Send the structured message to the supervisor agent\n        // Determine mode dynamically without changing UI\n        const chosenMode = this._determineResponseMode(text) || (this._mode as unknown as { id?: string })?.id || 'chat';\n        const supervisorResult = await supervisorAgent.run({\n            prompt: supervisorPrompt,\n            mode: chosenMode,\n            context: agentRunContext\n        });\n\n        finalResponse = supervisorResult?.output || 'No response from supervisor agent.';\n\n    } catch (error) {\n        logger.error('Error in agent execution pipeline:', error);\n        finalResponse = `Error: ${error instanceof Error ? error.message : String(error)}`;\n    } finally {\n        // This block ensures that a response, even an error, is always sent to the user.\n        const assistantMessage: ChatMessage = {\n            id: getNonce(),\n            role: 'assistant',\n            content: finalResponse,\n            timestamp: Date.now()\n        };\n\n        this._chatSession.addMessage(assistantMessage);\n        this._chatSession.setProcessing(false);\n        await this._chatSession.save();\n\n        this._panel.webview.postMessage({ type: 'addMessage', message: assistantMessage });\n        this._panel.webview.postMessage({ type: 'processingStateChanged', isProcessing: false });\n\n        this._cancelTokenSource?.dispose();\n        this._cancelTokenSource = undefined;\n        statusBarManager.setActivityIndicator(false);\n    }\n}\n\n  private _determineResponseMode(content: string): string {\n    const lower = content.toLowerCase();\n    if (lower.includes('debug') || lower.includes('error')) return 'debug';\n    if (lower.includes('edit') || lower.includes('change') || lower.includes('modify')) return 'edit';\n    if (lower.includes('explain') || lower.includes('what') || lower.includes('why') || lower.includes('how')) return 'ask';\n    if (lower.includes('generate') || lower.includes('create') || lower.includes('build')) return 'agent';\n    if (lower.includes('refactor') || lower.includes('improve')) return 'refactor';\n    return 'chat';\n  }\n\n  private async _handleShareSession(): Promise<void> {\n    try {\n      const shareLink = `codessa://chat/join/${(this._chatSession as unknown as { sessionId?: string })?.sessionId || Date.now()}`;\n      await vscode.env.clipboard.writeText(shareLink);\n      this._collaborative = true;\n      vscode.window.showInformationMessage('Session link copied to clipboard');\n    } catch (error) {\n      logger.error('Error sharing session:', error);\n      vscode.window.showErrorMessage('Failed to share session');\n    }\n  }\n\n  private async _handleJoinSession(sessionId: string): Promise<void> {\n    try {\n      if (!sessionId) {\n        vscode.window.showWarningMessage('No session ID provided');\n        return;\n      }\n      this._collaborative = true;\n      vscode.window.showInformationMessage(`Joining session: ${sessionId}`);\n    } catch (error) {\n      logger.error('Error joining session:', error);\n      vscode.window.showErrorMessage('Failed to join session');\n    }\n  }\n\n  private async _handleOpenSettings(): Promise<void> {\n    try {\n      const { AllSettingsPanel } = await import('../settings/allSettingsPanel');\n      const { ProviderManager } = await import('../../llm/providerManager');\n      const { MemoryManager } = await import('../../memory/memoryManager');\n\n      const providerManager = ProviderManager.getInstance();\n      const memoryManager = new MemoryManager();\n\n      await memoryManager.initialize(this._context);\n\n      if (AllSettingsPanel) {\n        await AllSettingsPanel.createOrShow(\n          this._context,\n          this._extensionUri,\n          memoryManager,\n          providerManager\n        );\n      } else {\n        throw new Error('Failed to load AllSettingsPanel');\n      }\n    } catch (error) {\n      logger.error('Error opening settings:', error);\n      vscode.window.showErrorMessage('Failed to open settings');\n      // Fallback to the default settings\n      try {\n        await vscode.commands.executeCommand('workbench.action.openSettings', 'codessa');\n      } catch (fallbackError) {\n        logger.error('Failed to open fallback settings:', fallbackError);\n      }\n    }\n  }\n\n  private async _handleExportChat(): Promise<void> {\n    try {\n      const messages = this._chatSession.getMessages();\n      if (messages.length === 0) {\n        vscode.window.showInformationMessage('No messages to export');\n        return;\n      }\n\n      const content = messages.map((msg: ChatMessage) =>\n        `[${new Date(msg.timestamp).toISOString()}] ${msg.role}: ${msg.content}`\n      ).join('\\n\\n');\n\n      const uri = await vscode.window.showSaveDialog({\n        filters: { 'Text': ['txt'], 'Markdown': ['md'] },\n        defaultUri: vscode.Uri.file('chat_export.md')\n      });\n\n      if (uri) {\n        await vscode.workspace.fs.writeFile(uri, Buffer.from(content, 'utf8'));\n        vscode.window.showInformationMessage(`Chat exported to ${uri.fsPath}`);\n      }\n    } catch (error) {\n      logger.error('Error exporting chat:', error);\n      vscode.window.showErrorMessage('Failed to export chat');\n    }\n  }\n\n  private async _handleToggleTTS(enabled: boolean): Promise<void> {\n    try {\n      await vscode.workspace.getConfiguration('codessa').update('tts.enabled', enabled, true);\n      if (enabled) {\n        vscode.window.showInformationMessage('Text-to-speech enabled');\n      } else {\n        vscode.window.showInformationMessage('Text-to-speech disabled');\n      }\n    } catch (error) {\n      logger.error('Error toggling TTS:', error);\n      vscode.window.showErrorMessage('Failed to toggle text-to-speech');\n    }\n  }\n\n  private async _handleStartRecording(): Promise<void> {\n    try {\n      this._isRecording = true;\n      this._cancelTokenSource = new vscode.CancellationTokenSource();\n      vscode.window.showInformationMessage('Recording started...');\n\n      await this._audioService.startRecording();\n      vscode.window.showInformationMessage('Audio recording started. Use the stop button when done.');\n    } catch (error) {\n      if (error instanceof vscode.CancellationError) {\n        vscode.window.showInformationMessage('Recording cancelled');\n      } else {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n        logger.error('Error during recording:', error);\n        vscode.window.showErrorMessage(`Recording failed: ${errorMessage}`);\n      }\n      this._isRecording = false;\n    }\n  }\n\n  private async _handleStopRecording(): Promise<void> {\n    if (!this._isRecording) return;\n    try {\n        const audioData = await this._audioService.stopRecording();\n        this._isRecording = false;\n\n        if (!audioData) {\n            vscode.window.showInformationMessage('No audio data was recorded');\n            return;\n        }\n\n        // The audioData is a base64 data URL, so we need to extract the base64 part and decode it.\n        const base64Data = audioData.split(',')[1];\n        const binaryString = atob(base64Data);\n        const len = binaryString.length;\n        const audioBuffer = new Uint8Array(len);\n        for (let i = 0; i < len; i++) {\n            audioBuffer[i] = binaryString.charCodeAt(i);\n        }\n\n        if (audioBuffer && audioBuffer.length > 0) {\n            await this._processAudioInput(audioBuffer);\n        } else {\n            throw new Error('Failed to process audio data: empty or invalid format');\n        }\n    } catch (error) {\n        logger.error('Error processing recording:', error);\n        vscode.window.showErrorMessage(`Error processing recording: ${error instanceof Error ? error.message : String(error)}`);\n    } finally {\n        this._isRecording = false;\n        this._cancelTokenSource?.dispose();\n        this._cancelTokenSource = undefined;\n    }\n  }\n\n  private async _handleAddContext(): Promise<void> {\n    try {\n      const text = await vscode.window.showInputBox({\n        prompt: 'Enter additional context for the conversation',\n        placeHolder: 'Context information...'\n      });\n\n      if (text) {\n        const contextMessage: ChatMessage = {\n          id: `context_${Date.now()}`,\n          role: 'system',\n          content: `[Context] ${text}`,\n          timestamp: Date.now()\n        };\n        this._chatSession.addMessage(contextMessage);\n        await this._chatSession.save();\n        this._updateWebview();\n      }\n    } catch (error) {\n      logger.error('Error adding context:', error);\n      vscode.window.showErrorMessage('Failed to add context');\n    }\n  }\n\n  private async _handleAttachFile(): Promise<void> {\n    try {\n      const uris = await vscode.window.showOpenDialog({\n        canSelectFiles: true,\n        canSelectFolders: false,\n        canSelectMany: true,\n        openLabel: 'Attach Files',\n        filters: {\n          'All Files': ['*']\n        }\n      });\n\n      if (!uris || uris.length === 0) {\n        return;\n      }\n\n      const files: AttachedFile[] = [];\n      for (const uri of uris) {\n        try {\n          const content = await vscode.workspace.fs.readFile(uri);\n          const fileName = uri.fsPath.split(/[\\\\/]/).pop() || 'file';\n          const fileContent = Buffer.from(content).toString('utf8');\n          const fileType = getMimeType(uri.fsPath);\n\n          files.push({\n            name: fileName,\n            path: uri.fsPath,\n            content: fileContent,\n            type: fileType\n          });\n        } catch (error) {\n          logger.error(`Error reading file ${uri.fsPath}:`, error);\n          vscode.window.showErrorMessage(`Failed to read file: ${uri.fsPath}`);\n          continue;\n        }\n      }\n\n      if (files.length > 0) {\n        // Add files to the chat session\n        for (const file of files) {\n          this._chatSession.addAttachedFile(file);\n        }\n\n        // Create a message that represents the file attachments\n        const fileList = files.map(f => f.name).join(', ');\n        const messageText = `[Attached files: ${fileList}]`;\n\n        // Route the message with attachments through the receiver agent\n        await this._handleMessage(messageText, files);\n\n        // Update the UI\n        await this._chatSession.save();\n        this._updateWebview();\n      } else {\n        vscode.window.showWarningMessage('No files could be attached.');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      logger.error('Error attaching files:', error);\n      vscode.window.showErrorMessage(`Failed to attach files: ${errorMessage}`);\n    }\n  }\n\n  private async _handleUploadImage(): Promise<void> {\n    try {\n      const uris = await vscode.window.showOpenDialog({\n        canSelectFiles: true,\n        canSelectFolders: false,\n        canSelectMany: true,\n        filters: {\n          'Images': ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp']\n        },\n        openLabel: 'Select Images to Upload'\n      });\n\n      if (!uris || uris.length === 0) {\n        return;\n      }\n\n      const images: AttachedFile[] = [];\n      for (const uri of uris) {\n        try {\n          const content = await vscode.workspace.fs.readFile(uri);\n          const fileName = uri.fsPath.split(/[\\\\/]/).pop() || 'image';\n          const mimeType = getMimeType(uri.fsPath);\n\n          // For images, we'll store them as base64 data URLs\n          const base64Content = Buffer.from(content).toString('base64');\n          const dataUrl = `data:${mimeType};base64,${base64Content}`;\n\n          images.push({\n            name: fileName,\n            path: uri.fsPath,\n            content: dataUrl,\n            type: mimeType\n          });\n        } catch (error) {\n          logger.error(`Error reading image ${uri.fsPath}:`, error);\n          vscode.window.showErrorMessage(`Failed to read image: ${uri.fsPath}`);\n          continue;\n        }\n      }\n\n      if (images.length > 0) {\n        // Add images to the chat session\n        for (const img of images) {\n          this._chatSession.addAttachedFile(img);\n        }\n\n        // Create a message that represents the image attachments\n        const imageList = images.map(img => img.name).join(', ');\n        const messageText = `[Attached images: ${imageList}]`;\n\n        // Route the message with image attachments through the receiver agent\n        await this._handleMessage(messageText, images);\n\n        // Update the UI\n        await this._chatSession.save();\n        this._updateWebview();\n        vscode.window.showInformationMessage(`Uploaded ${images.length} images`);\n      } else {\n        vscode.window.showWarningMessage('No images could be attached.');\n      }\n    } catch (error) {\n      logger.error('Error uploading images:', error);\n      vscode.window.showErrorMessage('Failed to upload images');\n    }\n  }\n\n\n  private async _processAudioInput(audioData: Uint8Array): Promise<void> {\n    try {\n      if (!this._audioService) {\n        throw new Error('Audio service not available');\n      }\n\n      // Show processing indicator\n      const status = vscode.window.setStatusBarMessage('Processing audio...');\n\n      try {\n        const text = await this._audioService.speechToText(audioData);\n        status.dispose(); // Clear status bar\n\n        if (text && text.trim()) {\n          await this._handleMessage(text.trim());\n        } else {\n          vscode.window.showWarningMessage('No speech was detected in the audio.');\n        }\n      } catch (error) {\n        status.dispose(); // Ensure status bar is cleared on error\n        throw error;\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      logger.error('Error processing audio input:', error);\n      vscode.window.showErrorMessage(`Failed to process audio: ${errorMessage}`);\n    }\n  }\n\n  private async _handleChangeModel(modelId?: string): Promise<void> {\n    if (!modelId) {\n      vscode.window.showWarningMessage('No model selected');\n      return;\n    }\n\n    try {\n      await vscode.workspace.getConfiguration('codessa').update('selectedModel', modelId, true);\n      vscode.window.showInformationMessage(`Model changed to: ${modelId}`);\n\n      if (this._panel) {\n        this._panel.webview.postMessage({\n          type: 'currentModel',\n          model: modelId\n        });\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to change model';\n      logger.error('Error changing model:', error);\n      vscode.window.showErrorMessage(`Failed to change model: ${errorMessage}`);\n    }\n  }\n  private async _handleGetModels(): Promise<void> {\n    try {\n      const providers = llmService.getAllProviders();\n      const currentProvider = providers.find(p => p.isConfigured()) || providers[0];\n      if (!currentProvider) {\n        vscode.window.showWarningMessage('No provider configured.');\n        return;\n      }\n      const models = await currentProvider.listModels();\n      const modelList = models.map(model => ({\n        id: model.id,\n        name: model.name || model.id,\n        description: model.description || ''\n      }));\n      const currentModel = modelList[0]?.id;\n\n      if (this._panel && this._panel.webview) {\n        this._panel.webview.postMessage({\n          type: 'models',\n          models: modelList\n        });\n\n        if (currentModel) {\n          this._panel.webview.postMessage({\n            type: 'currentModel',\n            model: currentModel\n          });\n        }\n      }\n    } catch (error) {\n      logger.error('Failed to get models:', error);\n      vscode.window.showErrorMessage('Failed to get models.');\n    }\n  }\n\n  private async _handleChangeProvider(providerId?: string): Promise<void> {\n    try {\n      if (!providerId) {\n        vscode.window.showWarningMessage('No provider selected.');\n        return;\n      }\n\n      await vscode.workspace.getConfiguration('codessa').update('selectedProvider', providerId, true);\n      vscode.window.showInformationMessage(`Provider changed to: ${providerId}`);\n\n      const providers = llmService.getAllProviders();\n      const provider = providers.find(p => p.providerId === providerId);\n\n      if (provider && this._panel?.webview) {\n        const models = await provider.listModels();\n        const modelList = models.map(model => ({\n          id: model.id,\n          name: model.name || model.id,\n          description: model.description || ''\n        }));\n\n        const currentModel = modelList[0]?.id;\n\n        this._panel.webview.postMessage({\n          type: 'models',\n          models: modelList\n        });\n\n        if (currentModel) {\n          this._panel.webview.postMessage({\n            type: 'currentModel',\n            model: currentModel\n          });\n        }\n      }\n    } catch (error) {\n      logger.error('Failed to change provider:', error);\n      vscode.window.showErrorMessage('Failed to change provider.');\n    }\n  }\n}\n"]}