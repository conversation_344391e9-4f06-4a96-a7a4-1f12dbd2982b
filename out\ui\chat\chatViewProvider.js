"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatViewProvider = void 0;
const vscode = __importStar(require("vscode"));
const utils_1 = require("../../utils");
const workspace_1 = require("../workspace/workspace");
const agentManager_1 = require("../../agents/agentUtilities/agentManager");
const supervisorAgent_1 = require("../../agents/agentTypes/supervisorAgent");
const chatView_1 = require("./chatView");
const logger_1 = require("../../logger");
const llmService_1 = require("../../llm/llmService");
const audioService_1 = require("../../services/audioService");
const ttsSettingsSection_1 = require("../settings/ttsSettingsSection");
const allSettingsPanel_1 = require("../settings/allSettingsPanel");
const memoryManager_1 = require("../../memory/memoryManager");
const providerManager_1 = require("../../llm/providerManager");
const events_1 = require("events");
class ChatViewProvider {
    _extensionUri;
    _context;
    static viewType = 'codessa.chatView';
    _view;
    _disposables = [];
    _chatSession;
    _audioService;
    _ttsSettingsView;
    _supervisorAgent;
    // Enhanced streaming support
    _streamingEmitter = new events_1.EventEmitter();
    _activeStreams = new Map();
    _agentStatusMap = new Map();
    _currentCancelTokenSource;
    constructor(_extensionUri, _context) {
        this._extensionUri = _extensionUri;
        this._context = _context;
        this._chatSession = new chatView_1.ChatSession(_context);
        this._audioService = audioService_1.AudioService.getInstance(_context);
        this._ttsSettingsView = ttsSettingsSection_1.TTSSettingsView.getInstance(_context);
        // Initialize supervisor agent reference (will be set later)
        this._supervisorAgent = undefined;
        this.refreshAgentReferences();
        // Load any saved chat session
        this._chatSession.load().catch((error) => {
            logger_1.Logger.instance.error('Failed to load chat session:', error);
        });
        // Ensure the setting exists and defaults to ["ollama"] if not set
        const config = vscode.workspace.getConfiguration('codessa.llm');
        if (!config.get('enabledProviders')) {
            config.update('enabledProviders', ['ollama'], vscode.ConfigurationTarget.Global);
        }
    }
    /**
     * Refresh agent references from AgentManager
     */
    refreshAgentReferences() {
        const agentManager = agentManager_1.AgentManager.getInstance();
        const maybeSupervisor = agentManager.getSupervisorAgent();
        this._supervisorAgent = maybeSupervisor instanceof supervisorAgent_1.SupervisorAgent ? maybeSupervisor : undefined;
        if (this._supervisorAgent) {
            logger_1.Logger.instance.info('ChatViewProvider: Supervisor agent reference updated');
        }
        else {
            logger_1.Logger.instance.warn('ChatViewProvider: No supervisor agent available');
        }
    }
    resolveWebviewView(webviewView, _context, _token) {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri
            ]
        };
        webviewView.webview.html = this._getWebviewContent(webviewView.webview);
        // Use the provided resolve context and cancellation token to satisfy lints meaningfully
        void _context; // explicit usage to indicate we intentionally ignore additional details here
        _token.onCancellationRequested(() => {
            logger_1.Logger.instance.info('Chat view resolve was cancelled');
        });
        // Initialize providers and models before sending initial state
        this._initializeProvidersAndModels(webviewView).then(() => {
            // Send initial state to webview
            webviewView.webview.postMessage({
                type: 'initialize',
                data: {
                    messages: this._chatSession.getMessages(),
                    isProcessing: this._chatSession.getProcessing()
                }
            });
            // Send provider info immediately after initialization
            this._sendProviderInfo(webviewView);
        }).catch(error => {
            logger_1.Logger.instance.error('Failed to initialize providers and models:', error);
            // Send initial state anyway
            webviewView.webview.postMessage({
                type: 'initialize',
                data: {
                    messages: this._chatSession.getMessages(),
                    isProcessing: this._chatSession.getProcessing()
                }
            });
            // Still send provider info even if initialization fails
            this._sendProviderInfo(webviewView);
        });
        webviewView.webview.onDidReceiveMessage(async (data) => {
            // Support both 'type' and 'command' from frontend messages
            const messageType = data.type || data.command;
            logger_1.Logger.instance.info(`Chat view received message: ${messageType}`, data);
            switch (messageType) {
                case 'providerChanged': {
                    const providerId = data.provider;
                    if (providerId) {
                        logger_1.Logger.instance.info(`Provider changed to: ${providerId}`);
                        // Store the selected provider
                        this._context.globalState.update('selectedProvider', providerId);
                        // Get models for the selected provider
                        const models = await this._getModelsForProvider(providerId);
                        // Send models to webview
                        webviewView.webview.postMessage({
                            type: 'models',
                            models,
                            provider: providerId
                        });
                        // Set default model if available
                        if (models.length > 0) {
                            this._context.globalState.update('selectedModel', models[0].id);
                            webviewView.webview.postMessage({
                                type: 'currentModel',
                                model: models[0].id
                            });
                        }
                    }
                    break;
                }
                case 'modelChanged': {
                    const modelId = data.model;
                    if (modelId) {
                        // Store the selected model in the context
                        this._context.globalState.update('selectedModel', modelId);
                        logger_1.Logger.instance.info(`Model changed to: ${modelId}`);
                    }
                    break;
                }
                case 'sendMessage': {
                    const messageText = data.text || data.message?.text || data.content;
                    if (!messageText) {
                        logger_1.Logger.instance.warn('Received sendMessage with no text content');
                        break;
                    }
                    // Cancel any previous operation
                    if (this._currentCancelTokenSource) {
                        this._currentCancelTokenSource.cancel();
                        this._currentCancelTokenSource.dispose();
                    }
                    this._currentCancelTokenSource = new vscode.CancellationTokenSource();
                    const cancellationToken = this._currentCancelTokenSource.token;
                    // Add user message
                    const userMessage = {
                        id: `user_${Date.now()}`,
                        role: 'user',
                        content: messageText,
                        timestamp: Date.now()
                    };
                    this._chatSession.addMessage(userMessage);
                    this._chatSession.setProcessing(true);
                    this._chatSession.save();
                    // Send processing state to webview
                    webviewView.webview.postMessage({
                        type: 'processingState',
                        isProcessing: true
                    });
                    try {
                        // Get selected provider and model from context
                        const selectedProvider = this._context.globalState.get('selectedProvider') || 'ollama';
                        const selectedModel = this._context.globalState.get('selectedModel') || 'llama3';
                        logger_1.Logger.instance.info(`Using provider: ${selectedProvider}, model: ${selectedModel}`);
                        // Update the default LLM config to use selected provider/model
                        const config = vscode.workspace.getConfiguration('codessa');
                        await config.update('selectedProvider', selectedProvider, vscode.ConfigurationTarget.Global);
                        await config.update('selectedModel', selectedModel, vscode.ConfigurationTarget.Global);
                        // Create streaming context for real-time updates
                        const streamId = `chat_${Date.now()}`;
                        const streamingContext = {
                            streamId,
                            onStream: (event) => {
                                this.handleAgentStreamEvent(event, webviewView);
                            },
                            cancellationToken
                        };
                        this._activeStreams.set(streamId, streamingContext);
                        const agentManager = agentManager_1.AgentManager.getInstance();
                        const receiverAgent = agentManager.getReceiverAgent();
                        const maybeSupervisor = agentManager.getSupervisorAgent();
                        const supervisorAgent = maybeSupervisor instanceof supervisorAgent_1.SupervisorAgent ? maybeSupervisor : undefined;
                        if (!receiverAgent || !supervisorAgent) {
                            const errorResponse = {
                                id: `error_${Date.now()}`,
                                role: 'error',
                                content: 'Required agents (receiver or supervisor) not available.',
                                timestamp: Date.now()
                            };
                            this._chatSession.addMessage(errorResponse);
                            this._chatSession.setProcessing(false);
                            this._chatSession.save();
                            webviewView.webview.postMessage({
                                type: 'addMessage',
                                message: errorResponse
                            });
                            webviewView.webview.postMessage({
                                type: 'processingState',
                                isProcessing: false
                            });
                            return;
                        }
                        // --- Receiver agent thinking ---
                        const receiverThinkingMessage = {
                            id: `receiver_thinking_${Date.now()}`,
                            role: 'assistant',
                            content: 'Receiver agent is processing your input...',
                            timestamp: Date.now(),
                            isThinking: true
                        };
                        this._chatSession.addMessage(receiverThinkingMessage);
                        this._chatSession.save();
                        webviewView.webview.postMessage({
                            type: 'addMessage',
                            message: receiverThinkingMessage
                        });
                        // First, process through receiver agent
                        const receiverResult = await receiverAgent.run({
                            prompt: messageText,
                            mode: 'chat'
                        }, {
                            workspace: workspace_1.WorkspaceHelper.createWorkspaceContext(),
                            variables: {
                                attachedFiles: this._chatSession.getAttachedFiles()
                            },
                            streamingContext,
                            cancellationToken
                        });
                        // Update receiver thinking message with result
                        receiverThinkingMessage.content = '✅ **Receiver Agent** completed analysis:\\n\\n*Enhanced request ready for supervisor*';
                        receiverThinkingMessage.isThinking = false;
                        this._chatSession.updateMessage(receiverThinkingMessage);
                        this._chatSession.save();
                        webviewView.webview.postMessage({
                            type: 'updateMessage',
                            message: receiverThinkingMessage
                        });
                        // Brief pause to show receiver completion
                        await new Promise(resolve => setTimeout(resolve, 800));
                        if (cancellationToken.isCancellationRequested) {
                            const cancelledMsg = {
                                id: `cancelled_${Date.now()}`,
                                role: 'assistant',
                                content: 'Operation cancelled.',
                                timestamp: Date.now()
                            };
                            this._chatSession.addMessage(cancelledMsg);
                            this._chatSession.setProcessing(false);
                            this._chatSession.save();
                            webviewView.webview.postMessage({
                                type: 'addMessage',
                                message: cancelledMsg
                            });
                            webviewView.webview.postMessage({
                                type: 'processingState',
                                isProcessing: false
                            });
                            return;
                        }
                        if (!receiverResult.success) {
                            throw new Error(`Receiver agent error: ${receiverResult.error}`);
                        }
                        // --- Supervisor agent thinking ---
                        const supervisorThinkingMessage = {
                            id: `supervisor_thinking_${Date.now()}`,
                            role: 'assistant',
                            content: '🎯 **Supervisor Agent** analyzing and orchestrating your request...',
                            timestamp: Date.now(),
                            isThinking: true
                        };
                        this._chatSession.addMessage(supervisorThinkingMessage);
                        this._chatSession.save();
                        webviewView.webview.postMessage({
                            type: 'addMessage',
                            message: supervisorThinkingMessage
                        });
                        // Then, process through supervisor agent with streaming
                        const supervisorResult = await supervisorAgent.run({
                            prompt: receiverResult.output || '',
                            mode: 'chat'
                        }, {
                            workspace: workspace_1.WorkspaceHelper.createWorkspaceContext(),
                            variables: {
                                attachedFiles: this._chatSession.getAttachedFiles(),
                                receiverOutput: receiverResult.output
                            },
                            streamingContext,
                            cancellationToken
                        });
                        if (cancellationToken.isCancellationRequested) {
                            this._chatSession.removeMessage(supervisorThinkingMessage.id);
                            this._chatSession.save();
                            webviewView.webview.postMessage({
                                type: 'removeMessage',
                                messageId: supervisorThinkingMessage.id
                            });
                            const cancelledMsg = {
                                id: `cancelled_${Date.now()}`,
                                role: 'assistant',
                                content: 'Operation cancelled.',
                                timestamp: Date.now()
                            };
                            this._chatSession.addMessage(cancelledMsg);
                            this._chatSession.setProcessing(false);
                            this._chatSession.save();
                            webviewView.webview.postMessage({
                                type: 'addMessage',
                                message: cancelledMsg
                            });
                            webviewView.webview.postMessage({
                                type: 'processingState',
                                isProcessing: false
                            });
                            return;
                        }
                        if (!supervisorResult.success) {
                            // Remove supervisor thinking message
                            this._chatSession.removeMessage(supervisorThinkingMessage.id);
                            this._chatSession.save();
                            webviewView.webview.postMessage({
                                type: 'removeMessage',
                                messageId: supervisorThinkingMessage.id
                            });
                            throw new Error(`Supervisor agent error: ${supervisorResult.error}`);
                        }
                        // Update supervisor thinking message to show completion
                        supervisorThinkingMessage.content = '✅ **Supervisor Agent** completed orchestration and coordination';
                        supervisorThinkingMessage.isThinking = false;
                        this._chatSession.updateMessage(supervisorThinkingMessage);
                        this._chatSession.save();
                        webviewView.webview.postMessage({
                            type: 'updateMessage',
                            message: supervisorThinkingMessage
                        });
                        // Brief pause to show supervisor completion
                        await new Promise(resolve => setTimeout(resolve, 800));
                        // --- Delegated agents processing (if any) ---
                        const additionalAgents = agentManager.getAdditionalAgents();
                        let finalOutput = supervisorResult.output;
                        for (const agent of additionalAgents) {
                            const delegatedThinkingMessage = {
                                id: `delegated_thinking_${agent.id}_${Date.now()}`,
                                role: 'assistant',
                                content: `🔄 **${agent.name || agent.id}** processing delegated task...`,
                                timestamp: Date.now(),
                                isThinking: true
                            };
                            this._chatSession.addMessage(delegatedThinkingMessage);
                            this._chatSession.save();
                            webviewView.webview.postMessage({
                                type: 'addMessage',
                                message: delegatedThinkingMessage
                            });
                            const result = await agent.run({
                                prompt: finalOutput || '',
                                mode: 'chat'
                            }, {
                                workspace: workspace_1.WorkspaceHelper.createWorkspaceContext(),
                                variables: {
                                    attachedFiles: this._chatSession.getAttachedFiles(),
                                    receiverOutput: receiverResult.output,
                                    supervisorOutput: supervisorResult.output
                                },
                                streamingContext,
                                cancellationToken
                            });
                            // Update delegated thinking message with result
                            delegatedThinkingMessage.content = `✅ **${agent.name || agent.id}** completed task`;
                            delegatedThinkingMessage.isThinking = false;
                            this._chatSession.updateMessage(delegatedThinkingMessage);
                            this._chatSession.save();
                            webviewView.webview.postMessage({
                                type: 'updateMessage',
                                message: delegatedThinkingMessage
                            });
                            if (cancellationToken.isCancellationRequested) {
                                const cancelledMsg = {
                                    id: `cancelled_${Date.now()}`,
                                    role: 'assistant',
                                    content: 'Operation cancelled.',
                                    timestamp: Date.now()
                                };
                                this._chatSession.addMessage(cancelledMsg);
                                this._chatSession.setProcessing(false);
                                this._chatSession.save();
                                webviewView.webview.postMessage({
                                    type: 'addMessage',
                                    message: cancelledMsg
                                });
                                webviewView.webview.postMessage({
                                    type: 'processingState',
                                    isProcessing: false
                                });
                                return;
                            }
                            if (result.success && result.output) {
                                finalOutput = result.output;
                            }
                            // Brief pause between agents
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                        // Add final assistant message with complete response
                        const assistantMessage = {
                            id: `assistant_${Date.now()}`,
                            role: 'assistant',
                            content: finalOutput || 'I apologize, but I was unable to generate a response to your request.',
                            timestamp: Date.now()
                        };
                        this._chatSession.addMessage(assistantMessage);
                        this._chatSession.setProcessing(false);
                        this._chatSession.save();
                        webviewView.webview.postMessage({
                            type: 'addMessage',
                            message: assistantMessage
                        });
                        webviewView.webview.postMessage({
                            type: 'processingState',
                            isProcessing: false
                        });
                        // Clean up streaming context
                        this._activeStreams.delete(streamId);
                        logger_1.Logger.instance.info('Message processing completed successfully');
                    }
                    catch (error) {
                        logger_1.Logger.instance.error('Error processing message:', error);
                        const errorMessage = {
                            id: `error_${Date.now()}`,
                            role: 'error',
                            content: `Error: ${error instanceof Error ? error.message : String(error)}`,
                            timestamp: Date.now()
                        };
                        this._chatSession.addMessage(errorMessage);
                        this._chatSession.setProcessing(false);
                        this._chatSession.save();
                        webviewView.webview.postMessage({
                            type: 'addMessage',
                            message: errorMessage
                        });
                        webviewView.webview.postMessage({
                            type: 'processingState',
                            isProcessing: false
                        });
                    }
                    break;
                }
                case 'openSettings': {
                    logger_1.Logger.instance.info('Opening settings panel');
                    try {
                        // Get required managers
                        const memoryManager = memoryManager_1.MemoryManager.getInstance();
                        const providerManager = providerManager_1.ProviderManager.getInstance();
                        // Open the AllSettingsPanel
                        await allSettingsPanel_1.AllSettingsPanel.createOrShow(this._context, this._extensionUri, memoryManager, providerManager);
                    }
                    catch (error) {
                        logger_1.Logger.instance.error('Failed to open settings panel:', error);
                        vscode.window.showErrorMessage('Failed to open settings panel');
                    }
                    break;
                }
                case 'toggleTTS': {
                    const state = data.state;
                    logger_1.Logger.instance.info(`TTS toggled: ${state}`);
                    try {
                        // Toggle TTS state
                        this._audioService.setTTSEnabled(state);
                    }
                    catch (error) {
                        logger_1.Logger.instance.error('Failed to toggle TTS:', error);
                    }
                    break;
                }
                case 'clearChat': {
                    logger_1.Logger.instance.info('Clearing chat history');
                    try {
                        this._chatSession.clearMessages();
                        this._chatSession.save();
                        webviewView.webview.postMessage({
                            type: 'clearMessages'
                        });
                    }
                    catch (error) {
                        logger_1.Logger.instance.error('Failed to clear chat:', error);
                    }
                    break;
                }
                case 'exportChat': {
                    logger_1.Logger.instance.info('Exporting chat');
                    try {
                        const messages = this._chatSession.getMessages();
                        const exportData = JSON.stringify(messages, null, 2);
                        // Show save dialog
                        const uri = await vscode.window.showSaveDialog({
                            defaultUri: vscode.Uri.file('codessa-chat-export.json'),
                            filters: {
                                'JSON Files': ['json'],
                                'All Files': ['*']
                            }
                        });
                        if (uri) {
                            await vscode.workspace.fs.writeFile(uri, Buffer.from(exportData, 'utf8'));
                            vscode.window.showInformationMessage('Chat exported successfully');
                        }
                    }
                    catch (error) {
                        logger_1.Logger.instance.error('Failed to export chat:', error);
                        vscode.window.showErrorMessage('Failed to export chat');
                    }
                    break;
                }
                // Other message handlers remain the same...
            }
        });
        webviewView.onDidDispose(() => this.dispose(), null, this._disposables);
    }
    _getWebviewContent(webview) {
        const nonce = (0, utils_1.getNonce)();
        const csp = `default-src 'none'; img-src ${webview.cspSource} https: data:; script-src 'nonce-${nonce}'; style-src 'unsafe-inline' ${webview.cspSource};`;
        // Get resource URIs
        const chatCssUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chat.css'));
        const chatJsUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chat.js'));
        return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="Content-Security-Policy" content="${csp}" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Codessa Chat</title>
  <link rel="stylesheet" href="${chatCssUri}">
</head>
<body>
  <div class="chat-container">
    <!-- Top Toolbar -->
    <div class="top-toolbar">
      <div class="toolbar-left">
        <select id="mode-selector" class="dropdown">
          <option value="chat">Chat</option>
          <option value="ask">Ask</option>
          <option value="debug">Debug</option>
          <option value="edit">Edit</option>
          <option value="agent">Agent</option>
          <option value="multiAgent">Multi-Agent</option>
          <option value="research">Research</option>
          <option value="documentation">Documentation</option>
        </select>
        <select id="provider-selector" class="dropdown">
          <option value="ollama">Ollama</option>
        </select>
        <select id="model-selector" class="dropdown">
          <option value="llama3">Llama 3</option>
        </select>
      </div>
      <div class="toolbar-right">
        <button id="btn-settings" class="btn-icon" title="Settings">⚙️</button>
        <button id="btn-export" class="btn-icon" title="Export Chat">📤</button>
        <button id="btn-clear" class="btn-icon" title="Clear Chat">🗑️</button>
      </div>
    </div>

    <!-- Messages Container -->
    <div id="chat-messages" class="messages-container">
      <div id="empty-chat-message" class="empty-message">
        Start a conversation with Codessa...
      </div>
    </div>

    <!-- Typing Indicator -->
    <div id="typing-indicator" class="typing-indicator" style="display: none;">
      <div class="typing-dots">
        <span></span><span></span><span></span>
      </div>
      <span class="typing-text">Codessa is thinking...</span>
    </div>

    <!-- Input Area -->
    <div class="input-container">
      <div class="input-toolbar">
        <button id="btn-add-context" class="btn-icon" title="Add Context">📎</button>
        <button id="btn-attach-file" class="btn-icon" title="Attach File">📄</button>
        <button id="btn-attach-folder" class="btn-icon" title="Attach Folder">📁</button>
        <button id="btn-upload-image" class="btn-icon" title="Upload Image">🖼️</button>
      </div>

      <div class="input-wrapper">
        <textarea id="message-input" placeholder="Type your message..." rows="1"></textarea>
        <div class="input-actions">
          <button id="btn-record-audio" class="btn-icon" title="Record Audio">🎤</button>
          <button id="btn-toggle-tts" class="btn-icon" title="Toggle TTS">🔊</button>
          <button id="btn-send" class="btn-primary" title="Send">Send</button>
          <button id="btn-cancel" class="btn-secondary" title="Cancel" style="display: none;">Cancel</button>
        </div>
      </div>

      <div class="input-secondary-actions">
        <button id="btn-input-copy" class="btn-icon" title="Copy">📋</button>
        <button id="btn-input-cut" class="btn-icon" title="Cut">✂️</button>
        <button id="btn-input-paste" class="btn-icon" title="Paste">📋</button>
        <button id="btn-input-clear" class="btn-icon" title="Clear Input">🗑️</button>
      </div>
    </div>
  </div>

  <script nonce="${nonce}">
    // Initialize state for chat.js
    const initialState = {
      isProcessing: false,
      messages: [],
      isTTSActive: false,
      currentMode: 'chat',
      currentProvider: 'ollama',
      currentModel: 'llama3',
      availableProviders: [],
      availableModels: []
    };
  </script>
  <script nonce="${nonce}" src="${chatJsUri}"></script>
</body>
</html>`;
    }
    async _getProviderList() {
        try {
            const providers = llmService_1.llmService.getAllProviders();
            return providers.map(p => ({
                id: p.providerId,
                name: p.displayName,
                description: p.description,
                configured: p.isConfigured(),
                isLocal: p.providerId === 'ollama' || p.providerId === 'lmstudio'
            }));
        }
        catch (error) {
            logger_1.Logger.instance.warn('Failed to get provider list:', error);
            return [];
        }
    }
    async _getModelsForProvider(providerId) {
        try {
            const provider = llmService_1.llmService.getProvider(providerId);
            if (provider && provider.isConfigured()) {
                const models = await provider.listModels();
                return models;
            }
            return this._getDefaultModelsForProvider(providerId);
        }
        catch (error) {
            logger_1.Logger.instance.warn(`Failed to list models for provider ${providerId}:`, error);
            return this._getDefaultModelsForProvider(providerId);
        }
    }
    _getDefaultModelsForProvider(providerId) {
        const provider = llmService_1.llmService.getProvider(providerId);
        if (provider?.defaultModel) {
            return [{ id: provider.defaultModel }];
        }
        // Minimal sensible defaults per common providers
        if (providerId === 'ollama') {
            return [{ id: 'llama3' }];
        }
        if (providerId === 'openai') {
            return [{ id: 'gpt-4o' }];
        }
        if (providerId === 'lmstudio') {
            return [{ id: 'default' }];
        }
        return [];
    }
    async _sendProviderInfo(webviewView) {
        try {
            const providers = await this._getProviderList();
            webviewView.webview.postMessage({
                type: 'providers',
                providers
            });
        }
        catch (error) {
            logger_1.Logger.instance.warn('Failed to send provider info:', error);
        }
    }
    async _sendModelsForProvider(webviewView, providerId) {
        try {
            let targetProviderId = providerId || this._context.globalState.get('selectedProvider');
            if (!targetProviderId) {
                const def = await llmService_1.llmService.getDefaultProvider();
                targetProviderId = def?.providerId || 'ollama';
            }
            const models = await this._getModelsForProvider(targetProviderId);
            webviewView.webview.postMessage({
                type: 'models',
                models,
                provider: targetProviderId
            });
            // Determine current model
            const currentModel = this._context.globalState.get('selectedModel') || models[0]?.id;
            if (currentModel) {
                webviewView.webview.postMessage({
                    type: 'currentModel',
                    model: currentModel
                });
            }
        }
        catch (error) {
            logger_1.Logger.instance.warn('Failed to send models for provider:', error);
        }
    }
    async _initializeProvidersAndModels(webviewView) {
        try {
            await this._sendProviderInfo(webviewView);
            // Ensure a selected provider exists
            let selectedProvider = this._context.globalState.get('selectedProvider');
            if (!selectedProvider) {
                const def = await llmService_1.llmService.getDefaultProvider();
                selectedProvider = def?.providerId || 'ollama';
                this._context.globalState.update('selectedProvider', selectedProvider);
            }
            await this._sendModelsForProvider(webviewView, selectedProvider);
        }
        catch (error) {
            logger_1.Logger.instance.warn('Failed to initialize providers/models:', error);
        }
    }
    dispose() {
        this._disposables.forEach(d => d.dispose());
    }
    _getMimeType(filePath) {
        const ext = filePath.toLowerCase().split('.').pop();
        const map = {
            'txt': 'text/plain',
            'md': 'text/markdown',
            'js': 'text/javascript',
            'ts': 'text/typescript',
            'json': 'application/json',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'svg': 'image/svg+xml',
            'wav': 'audio/wav',
            'mp3': 'audio/mpeg'
        };
        return map[ext || ''] || 'application/octet-stream';
    }
    handleAgentStreamEvent(event, webviewView) {
        try {
            // Track agent status map for quick status queries
            if (event.type === 'start') {
                this._agentStatusMap.set(event.agentId, 'running');
            }
            else if (event.type === 'complete') {
                this._agentStatusMap.set(event.agentId, 'complete');
            }
            else if (event.type === 'error') {
                this._agentStatusMap.set(event.agentId, 'error');
            }
            webviewView.webview.postMessage({
                type: 'agentStream',
                event
            });
        }
        catch (error) {
            logger_1.Logger.instance.warn('Failed to forward agent stream event:', error);
        }
    }
    getStreamingStatus() {
        return {
            activeStreams: this._activeStreams.size,
            activeAgents: Array.from(this._agentStatusMap.keys())
        };
    }
    reveal() {
        if (this._view) {
            this._view.show?.(true);
        }
        else {
            vscode.commands.executeCommand('workbench.view.extension.codessa-sidebar');
        }
    }
}
exports.ChatViewProvider = ChatViewProvider;
//# sourceMappingURL=chatViewProvider.js.map