{"version": 3, "file": "agent.js", "sourceRoot": "", "sources": ["../../../src/agents/agentUtilities/agent.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,yCAAsC;AACtC,qDAAkD;AAClD,2DAAwD;AACxD,4DAA6D;AAC7D,+DAA4D;AAC5D,0DAAiE;AAGjE,0DAAuD;AAEvD,+DAA8G;AAC9G,8CAAmD;AAiBnD;;;;GAIG;AACH,MAAa,gBAAgB;IACpB,MAAM,CAAU;IAChB,UAAU,CAAS;IACnB,WAAW,CAAS;IACpB,UAAU,CAAS;IACnB,QAAQ,CAAS;IACxB,YAAY,SAAkB,EAAE,EAAE,KAKjC;QACC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;YACnC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YACrC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;YACnC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QACjC,CAAC;IACH,CAAC;IACD,6BAA6B;IACtB,KAAK,CAAC,WAAW,CAAC,IAAY;QACnC,2CAA2C;QAC3C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;QAC1D,OAAO,MAAM,IAAI,kBAAkB,IAAI,EAAE,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,gBAAgB;QAC3B,iCAAiC;QACjC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,IAAY;QACrC,4BAA4B;QAC5B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;YACzE,OAAO,MAAM,CAAC,MAAM,IAAI,oBAAoB,IAAI,EAAE,CAAC;QACrD,CAAC;QACD,OAAO,oBAAoB,IAAI,EAAE,CAAC;IACpC,CAAC;CACF;AA1CD,4CA0CC;AAED,MAAa,KAAK;IACP,EAAE,CAAS;IACX,IAAI,CAAS;IACb,WAAW,CAAU;IACrB,IAAI,CAAY;IAChB,YAAY,CAAW;IACvB,WAAW,CAAS;IACpB,QAAQ,CAAS;IACjB,WAAW,CAAS;IACpB,SAAS,CAAS;IAClB,gBAAgB,CAAS;IACzB,SAAS,CAAa;IACtB,KAAK,CAAqB;IAC1B,YAAY,CAAU;IACvB,MAAM,CAAc;IACpB,gBAAgB,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;IAE9C,YAAY,OAAoB;QAC9B,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAiB,CAAC;QACtC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,GAAG,CAAC;QAC9C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC;QAC3C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACjD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,2BAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;QACtE,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,KAAK,YAAY,CAAC;QAC1E,IAAI,CAAC,MAAM,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,eAAM,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC;IACxF,CAAC;IAEO,oBAAoB;QAC1B,MAAM,cAAc,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC7D,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC5B,MAAM,IAAI,GAAG,2BAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACnD,IAAI,IAAI,EAAE,CAAC;oBACT,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBAC7B,eAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,aAAa,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B;QACtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAA,+BAAgB,GAAE,CAAC;YACnC,IAAI,CAAC,OAAO;gBAAE,OAAO;YACrB,MAAM,OAAO,GAAG,MAAM,qBAAa,CAAC,cAAc,CAAC;gBACjD,KAAK,EAAE,iBAAiB;gBACxB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE;oBACN,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;oBACzB,IAAI,EAAE,CAAC,iBAAiB,EAAE,SAAS,CAAC;oBACpC,OAAO,EAAE,IAAI,CAAC,EAAE;oBAChB,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;iBAC7B;gBACD,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAkD,CAAC;oBAC/F,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;wBACvB,sCAAwB,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;wBAC/D,eAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,eAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE,CAAC,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE,GAAG,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACH;;OAEG;IACK,mBAAmB;QACzB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QAEpC,0BAA0B;QAC1B,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;YAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACnD,IAAI,GAAG;gBAAE,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;QAC7D,CAAC;QAED,iDAAiD;QACjD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,IAAI,GAAG;gBAAE,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QAEnE,OAAO;YACL,WAAW,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,0BAA0B;YACjE,aAAa;YACb,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,uBAAuB;SACrE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,UAAkB,EAClB,OAAwB,EACxB,YAAmE,EACnE,KAA2D;QAE3D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAA,+BAAgB,GAAE,CAAC;YACnC,IAAI,CAAC,OAAO;gBAAE,OAAO;YAErB,MAAM,OAAO,GAAgC,sCAAwB,CAAC,cAAc,EAAE,CAAC;YACvF,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEpD,yDAAyD;YACzD,MAAM,eAAe,GAAG;gBACtB,cAAc,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,EAAE,KAAK,CAAC,8CAA8C,CAAC,CAAC;gBACtG,iBAAiB,EAAE,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;oBACnD,UAAU,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;wBAC9D,UAAU,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;gBACpF,YAAY,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,EAAE,KAAK,CAAC,wCAAwC,CAAC,CAAC;aAC/F,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,qBAAa,CAAC,SAAS,CAAC;gBACjD,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;gBACrE,QAAQ,EAAE;oBACR,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,iBAAiB;oBACvB,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;oBAC5B,OAAO,EAAE,IAAI,CAAC,EAAE;oBAChB,SAAS,EAAE,IAAI,CAAC,IAAI;oBACpB,UAAU,EAAE,EAAE;iBACf;aACF,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,qBAAa,CAAC,SAAS,CAAC;gBACjD,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;oBACtB,OAAO,EAAE,SAAS;oBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;oBAC1C,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;oBAChD,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,UAAU;oBACV,YAAY;oBACZ,cAAc,EAAE,KAAK,EAAE,cAAc;oBACrC,aAAa,EAAE,KAAK,EAAE,aAAa,IAAI,EAAE;oBACzC,oBAAoB;oBACpB,gBAAgB;oBAChB,eAAe;oBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,gBAAgB;iBACpD,CAAC;gBACF,QAAQ,EAAE;oBACR,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;oBACvF,OAAO,EAAE,IAAI,CAAC,EAAE;oBAChB,SAAS,EAAE,IAAI,CAAC,IAAI;oBACpB,UAAU,EAAE,EAAE;iBACf;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,uCAAuC,YAAY,CAAC,EAAE,kBAAkB,YAAY,CAAC,EAAE,eAAe,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAClI,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,sDAAsD,EAAE,GAAG,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IACD;;OAEG;IACK,KAAK,CAAC,2CAA2C;QACvD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAA,+BAAgB,GAAE,CAAC;YACnC,IAAI,CAAC,OAAO;gBAAE,OAAO;YACrB,MAAM,QAAQ,GAAG,MAAM,qBAAa,CAAC,cAAc,CAAC;gBAClD,KAAK,EAAE,iBAAiB;gBACxB,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE;oBACN,MAAM,EAAE,CAAC,UAAU,CAAC;oBACpB,IAAI,EAAE,CAAC,UAAU,CAAC;oBAClB,OAAO,EAAE,IAAI,CAAC,EAAE;oBAChB,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;iBAC7B;gBACD,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,MAAM;gBAAE,OAAO;YAE9B,kFAAkF;YAClF,IAAI,UAAU,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC;YAC5E,IAAI,eAAe,GAAG,CAAC,EAAE,eAAe,GAAG,CAAC,EAAE,eAAe,GAAG,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC;YACpF,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAkB,CAAC;YACtD,IAAI,kBAAkB,GAAG,CAAC,CAAC;YAE3B,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAMhC,CAAC;oBAEF,mBAAmB;oBACnB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;wBAClB,KAAK,YAAY;4BAAE,UAAU,EAAE,CAAC;4BAAC,MAAM;wBACvC,KAAK,aAAa;4BAAE,WAAW,EAAE,CAAC;4BAAC,MAAM;wBACzC,KAAK,aAAa;4BAAE,WAAW,EAAE,CAAC;4BAAC,MAAM;wBACzC,KAAK,SAAS;4BAAE,OAAO,EAAE,CAAC;4BAAC,MAAM;wBACjC,KAAK,MAAM;4BAAE,IAAI,EAAE,CAAC;4BAAC,MAAM;oBAC7B,CAAC;oBAED,0DAA0D;oBAC1D,IAAI,IAAI,CAAC,eAAe,EAAE,YAAY;wBAAE,eAAe,EAAE,CAAC;oBAC1D,QAAQ,IAAI,CAAC,eAAe,EAAE,iBAAiB,EAAE,CAAC;wBAChD,KAAK,OAAO;4BAAE,eAAe,EAAE,CAAC;4BAAC,MAAM;wBACvC,KAAK,UAAU;4BAAE,eAAe,EAAE,CAAC;4BAAC,MAAM;wBAC1C,KAAK,OAAO;4BAAE,YAAY,EAAE,CAAC;4BAAC,MAAM;oBACtC,CAAC;oBAED,+BAA+B;oBAC/B,IAAI,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;wBAC/C,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC1E,CAAC,CAAC,CAAC;oBAEH,+CAA+C;oBAC/C,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;wBAC3C,kBAAkB,IAAI,IAAI,CAAC,aAAa,CAAC;oBAC3C,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;YAED,MAAM,OAAO,GACX;gBACE,CAAC,YAAY,EAAE,UAAU,CAAC;gBAC1B,CAAC,aAAa,EAAE,WAAW,CAAC;gBAC5B,CAAC,aAAa,EAAE,WAAW,CAAC;gBAC5B,CAAC,SAAS,EAAE,OAAO,CAAC;gBACpB,CAAC,MAAM,EAAE,IAAI,CAAC;aAEjB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAEtC,MAAM,OAAO,GAAgC,EAAE,CAAC;YAChD,MAAM,kBAAkB,GAAG,sCAAwB,CAAC,cAAc,EAAE,CAAC;YAErE,yBAAyB;YACzB,IAAI,OAAO,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;gBAClC,OAAO,CAAC,iBAAiB,GAAG,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAE,OAAmD,CAAC;YAC3H,CAAC;YAED,6BAA6B;YAC7B,IAAI,eAAe,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;YAC9E,CAAC;YAED,2DAA2D;YAC3D,MAAM,YAAY,GAAG,eAAe,GAAG,eAAe,GAAG,YAAY,CAAC;YACtE,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,IAAI,eAAe,GAAG,YAAY,GAAG,GAAG,EAAE,CAAC;oBACzC,8EAA8E;oBAC9E,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;gBAClF,CAAC;qBAAM,IAAI,eAAe,GAAG,YAAY,GAAG,GAAG,EAAE,CAAC;oBAChD,wEAAwE;oBACxE,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;gBAC1E,CAAC;qBAAM,IAAI,YAAY,GAAG,YAAY,GAAG,GAAG,EAAE,CAAC;oBAC7C,0EAA0E;oBAC1E,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;YAED,+DAA+D;YAC/D,MAAM,gBAAgB,GAAG,kBAAkB,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC9D,IAAI,gBAAgB,GAAG,EAAE,EAAE,CAAC;gBAC1B,iDAAiD;gBACjD,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;YAC9F,CAAC;iBAAM,IAAI,gBAAgB,GAAG,EAAE,EAAE,CAAC;gBACjC,+DAA+D;gBAC/D,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;YAC9F,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;gBAChC,sCAAwB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACpD,eAAM,CAAC,KAAK,CAAC,oDAAoD,QAAQ,CAAC,MAAM,uBAAuB,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC,CAAC;YAChI,CAAC;QACH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE,GAAG,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;SAEK;IACL,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,OAAqB;QAC9B,mCAAmC;QACnC,eAAM,CAAC,IAAI,CAAC,6BAA6B,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB;QAC9B,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC5D,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAU,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,kBAAkB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QACD,OAAO,MAAM,uBAAU,CAAC,qBAAqB,EAAE,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAc,EAAE,SAAsD,EAAE,EAAE,UAAwB,EAAE,EAAE,iBAA4C;QAC/J,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACvD,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QACvE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,gCAAgC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;QACjE,CAAC;QAED,0EAA0E;QAC1E,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACxC,MAAM,IAAI,CAAC,2CAA2C,EAAE,CAAC;QAEzD,yCAAyC;QACzC,IAAI,cAAc,GAAG,MAAM,CAAC;QAC5B,IAAI,eAAe,GAA2B,IAAI,CAAC;QAEnD,MAAM,YAAY,GAAG,OAAQ,MAAkC,CAAC,YAAY,KAAK,QAAQ;YACvF,CAAC,CAAE,MAAkC,CAAC,YAAsB;YAC5D,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QAEf,MAAM,YAAY,GAAG,sCAAwB,CAAC,oBAAoB,CAAC,MAAM,EAAE;YACzE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE;YAChC,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY;YAC1C,cAAc,EAAE,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;SAC9C,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,OAAQ,MAAkC,CAAC,cAAc,KAAK,QAAQ;YAC3F,CAAC,CAAE,MAAkC,CAAC,cAAwB;YAC9D,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE5C,IAAI,CAAC;YACH,wDAAwD;YACxD,eAAe,GAAG,sCAAwB,CAAC,uBAAuB,CAChE,MAAM,EACN,YAAY,EACZ,EAAE,cAAc,EAAE,CACnB,CAAC;YAEF,sDAAsD;YACtD,IAAI,eAAe,CAAC,gBAAgB,EAAE,CAAC;gBACrC,cAAc,GAAG,GAAG,MAAM,oDAAoD,eAAe,CAAC,gBAAgB,kBAAkB,eAAe,CAAC,IAAI,UAAU,eAAe,CAAC,mBAAmB,IAAI,EAAE,GAAG,CAAC;YAC7M,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,2BAA2B,eAAe,CAAC,IAAI,6BAA6B,eAAe,CAAC,gBAAgB,EAAE,CAAC,CAAC;QAC/H,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,qEAAqE,EAAE,KAAK,CAAC,CAAC;QAC5F,CAAC;QAED,0CAA0C;QAC1C,MAAM,SAAS,GAAG,6BAAa,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,CAAC,gBAAgB,0BAA0B,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;QAClG,CAAC;QACD,MAAM,YAAY,GAAG,6BAAa,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,SAAmC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE7I,6CAA6C;QAC7C,MAAM,aAAa,GAAG,MAAM,uBAAU,CAAC,qBAAqB,EAAE,CAAC;QAC/D,MAAM,YAAY,GAAG;YACnB,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,aAAa,CAAC,OAAO;YACzD,GAAG,MAAM;YACT,IAAI,EAAE,OAAQ,MAAkC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAE,MAAkC,CAAC,IAAwD,CAAC,CAAC,CAAC,MAAM;YAC1K,MAAM,EAAE,cAAc;YACtB,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,YAAY;SAClH,CAAC;QAEF,oBAAoB;QACpB,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;QAE1E,8DAA8D;QAC9D,IAAI,aAAa,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC;QAC3C,IAAI,eAAe,EAAE,CAAC;YACpB,aAAa,GAAG,IAAI,CAAC,iCAAiC,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;YAEvF,uEAAuE;YACvE,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE;gBACtE,cAAc;gBACd,aAAa,EAAE,EAAE;aAClB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;SAEK;IACG,8BAA8B,CAAC,YAAoB,EAAE,eAAgC;QAC3F,MAAM,kBAAkB,GAAG;;;;;;UAMrB,eAAe,CAAC,IAAI;uBACP,eAAe,CAAC,gBAAgB;0BAC7B,eAAe,CAAC,mBAAmB,IAAI,wBAAwB;;;iBAGxE,eAAe,CAAC,IAAI;;;;;;EAMnC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,wBAAwB,eAAe,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE;CAC7F,CAAC;QAEE,OAAO,YAAY,GAAG,kBAAkB,CAAC;IAC3C,CAAC;IAED;;SAEK;IACG,iCAAiC,CAAC,QAAgB,EAAE,eAAgC;QAC1F,mDAAmD;QACnD,IAAI,gBAAgB,GAAG,QAAQ,CAAC;QAEhC,sCAAsC;QACtC,IAAI,eAAe,CAAC,mBAAmB,EAAE,CAAC;YACxC,gBAAgB,IAAI,YAAY,eAAe,CAAC,mBAAmB,IAAI,CAAC;QAC1E,CAAC;QAED,gCAAgC;QAChC,IAAI,eAAe,CAAC,aAAa,EAAE,CAAC;YAClC,gBAAgB,IAAI,8BAA8B,eAAe,CAAC,aAAa,EAAE,CAAC;QACpF,CAAC;QAED,uDAAuD;QACvD,MAAM,UAAU,GAAG;YACjB,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,GAAG;SACjB,CAAC;QAEF,MAAM,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;QACtD,gBAAgB,GAAG,GAAG,KAAK,IAAI,gBAAgB,EAAE,CAAC;QAElD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEO,qBAAqB,CAAC,SAAiB;QAC7C,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM,CAAC;YACZ,KAAK,UAAU;gBACb,OAAO,MAAM,CAAC;YAChB,KAAK,QAAQ;gBACX,OAAO,QAAQ,CAAC;YAClB,KAAK,MAAM,CAAC;YACZ,KAAK,OAAO,CAAC;YACb,KAAK,OAAO,CAAC;YACb,KAAK,aAAa,CAAC;YACnB,KAAK,UAAU,CAAC;YAChB,KAAK,eAAe,CAAC;YACrB,KAAK,gBAAgB,CAAC;YACtB,KAAK,OAAO,CAAC;YACb;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,GAAG,CAAC,KAAoB,EAAE,eAA6B,EAAE;QACpE,MAAM,aAAa,GAAG,EAAE,CAAC;QACzB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,MAAM,cAAc,GAAiB,EAAE,CAAC;QAExC,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,IAAI,IAAA,mCAAqB,GAAE,CAAC;YACjE,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,gCAAgC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YACjE,CAAC;YAED,0CAA0C;YAC1C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,4CAA4C,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YAC7E,CAAC;YACD,MAAM,SAAS,GAAG,6BAAa,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACjE,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,CAAC,gBAAgB,0BAA0B,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YAClG,CAAC;YACD,iFAAiF;YACjF,IAAI,eAAe,GAA2B,YAAY,CAAC,SAAS,CAAC,CAAC,CAAE,YAAY,CAAC,SAAoC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/H,IAAI,IAAA,+BAAgB,GAAE,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACvC,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBACrE,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;oBAChE,IAAI,SAAS,EAAE,CAAC;wBACd,eAAe,GAAG,EAAE,GAAG,eAAe,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;oBACrE,CAAC;gBACH,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,eAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE,CAAC,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;YACD,MAAM,YAAY,GAAG,6BAAa,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;YAExF,uBAAuB;YACvB,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9C,YAAY,GAAG,EAAE,GAAG,YAAY,EAAE,KAAK,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAuB,EAAE,CAAC;YAEhG,eAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,yBAAyB,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;YACrH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,OAAO,UAAU,GAAG,aAAa,EAAE,CAAC;gBAClC,UAAU,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,IAAI,iBAAiB,UAAU,EAAE,CAAC,CAAC;gBAE/D,yBAAyB;gBACzB,IAAI,YAAY,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;oBAC5D,eAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,kBAAkB,CAAC,CAAC;oBACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC;gBACzD,CAAC;gBAED,MAAM,cAAc,GAAsB;oBACxC,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,YAAY;oBACZ,OAAO,EAAE,IAAI,CAAC,QAAQ;oBACtB,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC5C,OAAO,EAAE,YAAY;iBACtB,CAAC;gBAEF,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;gBAEzD,MAAM,EAAE,OAAO,EAAE,wBAAwB,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,QAAQ,CAAC;gBAElF,+CAA+C;gBAC/C,IAAI,wBAAwB,EAAE,CAAC;oBAC7B,WAAW,IAAI,wBAAwB,CAAC;gBAC1C,CAAC;gBAED,uCAAuC;gBACvC,IAAI,eAAe,EAAE,CAAC;oBACpB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,eAAe,CAAC;oBACzC,IAAI,CAAC,MAAM,EAAE,CAAC;wBACZ,eAAM,CAAC,KAAK,CAAC,kDAAkD,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;wBAC9E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sCAAsC,EAAE,CAAC;oBAC3E,CAAC;oBACD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpC,IAAI,CAAC,IAAI,EAAE,CAAC;wBACV,eAAM,CAAC,KAAK,CAAC,SAAS,MAAM,0BAA0B,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;wBACrE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,MAAM,cAAc,EAAE,CAAC;oBAClE,CAAC;oBAED,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;wBACtD,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAA+B,EAAE,MAAM,EAAE,CAAC,CAAC;wBAC9F,KAAK,CAAC,MAAM,IAAI,UAAU,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBACvE,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,KAAK,CAAC,SAAS,MAAM,qBAAqB,EAAE,KAAK,CAAC,CAAC;wBAC1D,KAAK,CAAC,MAAM,IAAI,UAAU,MAAM,WAAW,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtG,CAAC;gBACH,CAAC;gBACD,mFAAmF;qBAC9E,IAAI,CAAC,wBAAwB,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvD,eAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,iDAAiD,CAAC,CAAC;oBAClF,WAAW,GAAG,EAAE,CAAC,CAAC,sCAAsC;oBACxD,MAAM;gBACR,CAAC;gBACD,2DAA2D;qBACtD,IAAI,wBAAwB,IAAI,CAAC,eAAe,EAAE,CAAC;oBACtD,MAAM;gBACR,CAAC;YACH,CAAC;YAED,IAAI,UAAU,IAAI,aAAa,EAAE,CAAC;gBAChC,eAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,6BAA6B,aAAa,IAAI,CAAC,CAAC;gBAC/E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2CAA2C,aAAa,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;YAC9H,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,qBAAqB,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC,CAAC;YAEjF,mCAAmC;YACnC,IAAI,IAAA,+BAAgB,GAAE,IAAI,WAAW,EAAE,CAAC;gBACtC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACzD,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,IAAI,eAAe,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3F,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,IAAY;QACrC,4BAA4B;QAC5B,OAAO,oBAAoB,IAAI,EAAE,CAAC;IACpC,CAAC;CACF;AAnoBD,sBAmoBC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as path from 'path';\nimport { logger } from '../../logger';\nimport { llmService } from '../../llm/llmService';\nimport { ToolRegistry } from '../../tools/toolRegistry';\nimport { getMemoryEnabled } from '../../memory/memoryConfig';\nimport { promptManager } from '../../prompts/promptManager';\nimport { getDefaultModelConfig } from '../../config/modelConfig';\nimport { AgentRunInput, AgentContext, AgentRunResult, ToolResult, AgentRole } from '../../types/agent';\nimport { LLMConfig, LLMGenerateParams } from '../../llm/types';\nimport { AgentMemory } from '../../memory/agentMemory';\nimport { ITool } from '../../tools/types';\nimport { goddessPersonalityEngine, GoddessResponse, GoddessPersonality } from '../../personality/goddessMode';\nimport { memoryManager } from '../../memory/types';\nexport interface AgentConfig {\n  id: string;\n  name: string;\n  description?: string;\n  role: AgentRole;\n  capabilities: string[];\n  llmProvider: string;\n  llmModel: string;\n  temperature?: number;\n  maxTokens?: number;\n  systemPromptName: string;\n  llm?: LLMConfig;\n  tools?: string[];\n  isSupervisor?: boolean;\n}\n\n/**\n * MultiAgentSystem is a stub for multi-agent workflows.\n * It exposes supervisor, coordinator, specialist, and executor for workflow compatibility.\n * These are optional and should be Agent instances or undefined.\n */\nexport class MultiAgentSystem {\n  public agents: Agent[];\n  public supervisor?: Agent;\n  public coordinator?: Agent;\n  public specialist?: Agent;\n  public executor?: Agent;\n  constructor(agents: Agent[] = [], roles?: {\n    supervisor?: Agent,\n    coordinator?: Agent,\n    specialist?: Agent,\n    executor?: Agent\n  }) {\n    this.agents = agents;\n    if (roles) {\n      this.supervisor = roles.supervisor;\n      this.coordinator = roles.coordinator;\n      this.specialist = roles.specialist;\n      this.executor = roles.executor;\n    }\n  }\n  // Add actual logic as needed\n  public async executeTask(task: string): Promise<string> {\n    // Logic to execute a task using the agents\n    const result = await this.supervisor?.superviseTask(task);\n    return result || `Task executed: ${task}`;\n  }\n\n  public async coordinateAgents(): Promise<void> {\n    // Logic to coordinate the agents\n    if (this.coordinator) {\n      await this.coordinator.run({ prompt: 'Coordinate agents', mode: 'task' });\n    }\n  }\n\n  public async superviseTask(task: string): Promise<string> {\n    // Logic to supervise a task\n    if (this.supervisor) {\n      const result = await this.supervisor.run({ prompt: task, mode: 'task' });\n      return result.output || `Task supervised: ${task}`;\n    }\n    return `Task supervised: ${task}`;\n  }\n}\n\nexport class Agent {\n  readonly id: string;\n  readonly name: string;\n  readonly description?: string;\n  readonly role: AgentRole;\n  readonly capabilities: string[];\n  readonly llmProvider: string;\n  readonly llmModel: string;\n  readonly temperature: number;\n  readonly maxTokens: number;\n  readonly systemPromptName: string;\n  readonly llmConfig?: LLMConfig;\n  readonly tools: Map<string, ITool>;\n  readonly isSupervisor: boolean;\n  private memory: AgentMemory;\n  private sessionStartTime: number = Date.now();\n\n  constructor(_config: AgentConfig) {\n    this.id = _config.id;\n    this.name = _config.name;\n    this.description = _config.description;\n    this.role = _config.role as AgentRole;\n    this.capabilities = _config.capabilities;\n    this.llmProvider = _config.llmProvider;\n    this.llmModel = _config.llmModel;\n    this.temperature = _config.temperature ?? 0.7;\n    this.maxTokens = _config.maxTokens ?? 2000;\n    this.systemPromptName = _config.systemPromptName;\n    this.llmConfig = _config.llm;\n    this.tools = ToolRegistry.instance.getToolsByIds(_config.tools || []);\n    this.isSupervisor = _config.isSupervisor ?? _config.role === 'supervisor';\n    this.memory = new AgentMemory(this);\n\n    this.ensureEssentialTools();\n\n    logger.info(`Agent created: ${this.name} (${this.id}) with ${this.tools.size} tools`);\n  }\n\n  private ensureEssentialTools(): void {\n    const essentialTools = ['fileSystem', 'workspace', 'search'];\n    for (const toolId of essentialTools) {\n      if (!this.tools.has(toolId)) {\n        const tool = ToolRegistry.instance.getTool(toolId);\n        if (tool) {\n          this.tools.set(toolId, tool);\n          logger.debug(`Added essential tool ${toolId} to agent ${this.name}`);\n        }\n      }\n    }\n  }\n\n  /**\n   * Load the user's persistent Goddess profile from memory (if any) and apply to engine\n   */\n  private async loadAndApplyGoddessProfile(): Promise<void> {\n    try {\n      const enabled = getMemoryEnabled();\n      if (!enabled) return;\n      const results = await memoryManager.searchMemories({\n        query: 'goddess_profile',\n        limit: 1,\n        filter: {\n          source: ['user', 'agent'],\n          type: ['user_preference', 'pattern'],\n          agentId: this.id,\n          tags: ['goddess', 'profile']\n        },\n        sortBy: 'timestamp',\n        sortOrder: 'desc'\n      });\n\n      if (results && results[0]) {\n        try {\n          const parsed = JSON.parse(results[0].content) as { personality?: Partial<GoddessPersonality> };\n          if (parsed.personality) {\n            goddessPersonalityEngine.updatePersonality(parsed.personality);\n            logger.info(`Applied persisted Goddess profile for agent ${this.name}`);\n          }\n        } catch (e) {\n          logger.warn('Failed to parse persisted Goddess profile content:', e);\n        }\n      }\n    } catch (err) {\n      logger.warn('Unable to load Goddess profile from memory:', err);\n    }\n  }\n\n  /**\n   * Persist the latest Goddess profile and session summary to memory\n   */\n  /**\n   * Get enriched workspace context for memory storage\n   */\n  private getWorkspaceContext(): { activeFiles: string[]; workspaceName?: string; languages: string[] } {\n    const activeFiles: string[] = [];\n    const languages = new Set<string>();\n\n    // Get active editor files\n    if (vscode.window.activeTextEditor) {\n      const activeFile = vscode.window.activeTextEditor.document.fileName;\n      activeFiles.push(path.basename(activeFile));\n      const ext = path.extname(activeFile).toLowerCase();\n      if (ext) languages.add(ext.substring(1)); // Remove the dot\n    }\n\n    // Get recently opened files from visible editors\n    vscode.window.visibleTextEditors.forEach(editor => {\n      const fileName = editor.document.fileName;\n      const baseName = path.basename(fileName);\n      if (!activeFiles.includes(baseName)) {\n        activeFiles.push(baseName);\n      }\n      const ext = path.extname(fileName).toLowerCase();\n      if (ext) languages.add(ext.substring(1));\n    });\n\n    // Get workspace name\n    const workspaceName = vscode.workspace.workspaceFolders?.[0]?.name;\n\n    return {\n      activeFiles: activeFiles.slice(0, 10), // Limit to 10 most recent\n      workspaceName,\n      languages: Array.from(languages).slice(0, 5) // Limit to 5 languages\n    };\n  }\n\n  private async persistGoddessSession(\n    userPrompt: string,\n    goddess: GoddessResponse,\n    detectedMood: import('../../personality/goddessMode').DeveloperMood,\n    extra?: { codeComplexity?: number; effectiveness?: number }\n  ): Promise<void> {\n    try {\n      const enabled = getMemoryEnabled();\n      if (!enabled) return;\n\n      const profile: Partial<GoddessPersonality> = goddessPersonalityEngine.getPersonality();\n      const workspaceContext = this.getWorkspaceContext();\n\n      // Detect choice preferences from the prompt and response\n      const choiceDetection = {\n        offeredChoices: !!(goddess.motivationalElement?.match(/quick fix|refactor|step-by-step|walkthrough/i)),\n        preferredApproach: userPrompt.match(/quick|fast|simple/i) ? 'quick' :\n                          userPrompt.match(/refactor|clean|restructure/i) ? 'refactor' :\n                          userPrompt.match(/step|guide|walk|explain/i) ? 'steps' : 'unknown',\n        chunkingUsed: !!(goddess.motivationalElement?.match(/2–3|two|three|steps|chunk|break.*down/i))\n      };\n\n      const profileEntry = await memoryManager.addMemory({\n        content: JSON.stringify({ persona: 'goddess', personality: profile }),\n        metadata: {\n          source: 'user',\n          type: 'user_preference',\n          tags: ['goddess', 'profile'],\n          agentId: this.id,\n          agentName: this.name,\n          importance: 90\n        }\n      });\n\n      const sessionEntry = await memoryManager.addMemory({\n        content: JSON.stringify({\n          persona: 'goddess',\n          tone: goddess.tone,\n          emotionalContext: goddess.emotionalContext,\n          motivationalElement: goddess.motivationalElement,\n          wisdomSharing: goddess.wisdomSharing,\n          userPrompt,\n          detectedMood,\n          codeComplexity: extra?.codeComplexity,\n          effectiveness: extra?.effectiveness ?? 50,\n          // Enhanced metadata\n          workspaceContext,\n          choiceDetection,\n          timestamp: new Date().toISOString(),\n          sessionDuration: Date.now() - this.sessionStartTime\n        }),\n        metadata: {\n          source: 'temporal',\n          type: 'episodic',\n          tags: ['goddess', 'session', ...workspaceContext.languages.map(lang => `lang:${lang}`)],\n          agentId: this.id,\n          agentName: this.name,\n          importance: 70\n        }\n      });\n\n      logger.debug(`Persisted enriched Goddess profile (${profileEntry.id}) and session (${sessionEntry.id}) for agent ${this.name}`);\n    } catch (err) {\n      logger.warn('Unable to persist Goddess session/profile to memory:', err);\n    }\n  }\n  /**\n   * Retrieve recent Goddess sessions to derive preferences and apply nudges\n   */\n  private async deriveAndApplyGoddessPreferencesFromHistory(): Promise<void> {\n    try {\n      const enabled = getMemoryEnabled();\n      if (!enabled) return;\n      const sessions = await memoryManager.searchMemories({\n        query: 'goddess session',\n        limit: 10,\n        filter: {\n          source: ['temporal'],\n          type: ['episodic'],\n          agentId: this.id,\n          tags: ['goddess', 'session']\n        },\n        sortBy: 'timestamp',\n        sortOrder: 'desc'\n      });\n\n      if (!sessions?.length) return;\n\n      // Aggregate enriched preferences: tone, choice style, chunking, language patterns\n      let supportive = 0, encouraging = 0, challenging = 0, playful = 0, wise = 0;\n      let prefersChunking = 0, prefersQuickFix = 0, prefersRefactor = 0, prefersSteps = 0;\n      const languagePreferences = new Map<string, number>();\n      let totalEffectiveness = 0;\n\n      for (const s of sessions) {\n        try {\n          const data = JSON.parse(s.content) as {\n            tone?: string;\n            motivationalElement?: string;\n            effectiveness?: number;\n            choiceDetection?: { preferredApproach?: string; chunkingUsed?: boolean };\n            workspaceContext?: { languages?: string[] };\n          };\n\n          // Tone preferences\n          switch (data.tone) {\n            case 'supportive': supportive++; break;\n            case 'encouraging': encouraging++; break;\n            case 'challenging': challenging++; break;\n            case 'playful': playful++; break;\n            case 'wise': wise++; break;\n          }\n\n          // Choice and chunking preferences from enhanced detection\n          if (data.choiceDetection?.chunkingUsed) prefersChunking++;\n          switch (data.choiceDetection?.preferredApproach) {\n            case 'quick': prefersQuickFix++; break;\n            case 'refactor': prefersRefactor++; break;\n            case 'steps': prefersSteps++; break;\n          }\n\n          // Language context preferences\n          data.workspaceContext?.languages?.forEach(lang => {\n            languagePreferences.set(lang, (languagePreferences.get(lang) || 0) + 1);\n          });\n\n          // Track effectiveness for weighted preferences\n          if (typeof data.effectiveness === 'number') {\n            totalEffectiveness += data.effectiveness;\n          }\n        } catch (error) {\n          logger.debug('Failed to parse enriched session data:', error);\n        }\n      }\n\n      const topTone = (\n        [\n          ['supportive', supportive],\n          ['encouraging', encouraging],\n          ['challenging', challenging],\n          ['playful', playful],\n          ['wise', wise]\n        ] as Array<[GoddessResponse['tone'], number]>\n      ).sort((a, b) => b[1] - a[1])[0]?.[0];\n\n      const updates: Partial<GoddessPersonality> = {};\n      const currentPersonality = goddessPersonalityEngine.getPersonality();\n\n      // Apply tone preferences\n      if (topTone && topTone !== 'wise') {\n        updates.motivationalStyle = topTone === 'playful' ? 'encouraging' : (topTone as GoddessPersonality['motivationalStyle']);\n      }\n\n      // Apply chunking preferences\n      if (prefersChunking > sessions.length / 2) {\n        updates.adaptiveLevel = Math.min(100, currentPersonality.adaptiveLevel + 3);\n      }\n\n      // Apply choice preferences to creativity and wisdom levels\n      const totalChoices = prefersQuickFix + prefersRefactor + prefersSteps;\n      if (totalChoices > 0) {\n        if (prefersQuickFix > totalChoices * 0.4) {\n          // User prefers quick solutions - increase creativity for innovative shortcuts\n          updates.creativityLevel = Math.min(100, currentPersonality.creativityLevel + 2);\n        } else if (prefersRefactor > totalChoices * 0.4) {\n          // User prefers refactoring - increase wisdom for architectural guidance\n          updates.wisdomLevel = Math.min(100, currentPersonality.wisdomLevel + 2);\n        } else if (prefersSteps > totalChoices * 0.4) {\n          // User prefers step-by-step - increase adaptive level for better guidance\n          updates.adaptiveLevel = Math.min(100, currentPersonality.adaptiveLevel + 2);\n        }\n      }\n\n      // Adjust emotional intelligence based on average effectiveness\n      const avgEffectiveness = totalEffectiveness / sessions.length;\n      if (avgEffectiveness > 75) {\n        // High effectiveness - maintain current EI level\n        updates.emotionalIntelligence = Math.min(100, currentPersonality.emotionalIntelligence + 1);\n      } else if (avgEffectiveness < 50) {\n        // Low effectiveness - increase EI for better emotional support\n        updates.emotionalIntelligence = Math.min(100, currentPersonality.emotionalIntelligence + 3);\n      }\n\n      if (Object.keys(updates).length) {\n        goddessPersonalityEngine.updatePersonality(updates);\n        logger.debug(`Applied enriched Goddess preference updates from ${sessions.length} sessions for agent ${this.name}:`, updates);\n      }\n    } catch (err) {\n      logger.warn('Failed to derive Goddess preferences from history:', err);\n    }\n  }\n\n  /**\n     * Get the agent's memory\n     */\n  getMemory(): AgentMemory {\n    return this.memory;\n  }\n\n  /**\n   * Set the agent's context\n   */\n  setContext(context: AgentContext): void {\n    // Store the context for future use\n    logger.info(`Setting context for agent ${this.name}`, context);\n  }\n\n  /**\n     * Utility to get the correct LLM config based on per-agent toggle.\n     */\n  private async getActiveLLMConfig(): Promise<LLMConfig> {\n    let perAgentLLMEnabled = false;\n    try {\n      const config = vscode.workspace.getConfiguration('codessa');\n      perAgentLLMEnabled = config.get<boolean>('agents.perAgentLLMEnabled', false);\n    } catch (error) {\n      logger.warn('Failed to get per-agent LLM config:', error);\n    }\n    if (perAgentLLMEnabled && this.llmConfig) {\n      return this.llmConfig;\n    }\n    return await llmService.getDefaultModelConfig();\n  }\n\n  async generate(prompt: string, params: Record<string, unknown> | LLMGenerateParams = {}, context: AgentContext = {}, cancellationToken?: vscode.CancellationToken): Promise<string> {\n    const llmConfigToUse = await this.getActiveLLMConfig();\n    const provider = await llmService.getProviderForConfig(llmConfigToUse);\n    if (!provider) {\n      throw new Error(`No provider found for agent '${this.name}'.`);\n    }\n\n    // Load persisted Goddess profile and derive preferences before generation\n    await this.loadAndApplyGoddessProfile();\n    await this.deriveAndApplyGoddessPreferencesFromHistory();\n\n    // Revolutionary Goddess Mode Integration\n    let enhancedPrompt = prompt;\n    let goddessResponse: GoddessResponse | null = null;\n\n    const sessionStart = typeof (params as Record<string, unknown>).sessionStart === 'number'\n      ? (params as Record<string, unknown>).sessionStart as number\n      : Date.now();\n\n    const detectedMood = goddessPersonalityEngine.analyzeDeveloperMood(prompt, {\n      timeOfDay: new Date().getHours(),\n      recentErrors: 0,\n      sessionDuration: Date.now() - sessionStart,\n      codeComplexity: prompt.length > 200 ? 80 : 50\n    });\n\n    const codeComplexity = typeof (params as Record<string, unknown>).codeComplexity === 'number'\n      ? (params as Record<string, unknown>).codeComplexity as number\n      : ((prompt?.length || 0) > 200 ? 80 : 50);\n\n    try {\n      // Generate goddess response with emotional intelligence\n      goddessResponse = goddessPersonalityEngine.generateGoddessResponse(\n        prompt,\n        detectedMood,\n        { codeComplexity }\n      );\n\n      // Enhance the prompt with goddess personality context\n      if (goddessResponse.emotionalContext) {\n        enhancedPrompt = `${prompt}\\n\\n[Goddess Context: Developer mood detected as ${goddessResponse.emotionalContext}. Respond with ${goddessResponse.tone} tone. ${goddessResponse.motivationalElement || ''}]`;\n      }\n\n      logger.debug(`Goddess Mode activated: ${goddessResponse.tone} tone, emotional context: ${goddessResponse.emotionalContext}`);\n    } catch (error) {\n      logger.warn('Goddess Mode integration failed, continuing with standard response:', error);\n    }\n\n    // Get the prompt definition and render it\n    const promptDef = promptManager.getPrompt(this.systemPromptName);\n    if (!promptDef) {\n      throw new Error(`System prompt '${this.systemPromptName}' not found for agent '${this.name}'.`);\n    }\n    const systemPrompt = promptManager.renderPrompt(this.systemPromptName, context.variables ? context.variables as Record<string, string> : {});\n\n    // Merge parameters with goddess enhancements\n    const defaultConfig = await llmService.getDefaultModelConfig();\n    const mergedParams = {\n      modelId: this.llmConfig?.modelId || defaultConfig.modelId,\n      ...params,\n      mode: typeof (params as Record<string, unknown>).mode === 'string' ? (params as Record<string, unknown>).mode as 'task' | 'chat' | 'edit' | 'generate' | 'inline' : 'chat',\n      prompt: enhancedPrompt,\n      systemPrompt: goddessResponse ? this.enhanceSystemPromptWithGoddess(systemPrompt, goddessResponse) : systemPrompt\n    };\n\n    // Generate response\n    const response = await provider.generate(mergedParams, cancellationToken);\n\n    // Post-process response with goddess personality if available\n    let finalResponse = response.content || '';\n    if (goddessResponse) {\n      finalResponse = this.applyGoddessPersonalityToResponse(finalResponse, goddessResponse);\n\n      // Persist session summary and current profile to memory (non-invasive)\n      await this.persistGoddessSession(prompt, goddessResponse, detectedMood, {\n        codeComplexity,\n        effectiveness: 50\n      });\n    }\n\n    return finalResponse;\n  }\n\n  /**\n     * Enhance system prompt with Goddess Mode personality\n     */\n  private enhanceSystemPromptWithGoddess(systemPrompt: string, goddessResponse: GoddessResponse): string {\n    const goddessEnhancement = `\n\n## Goddess Mode Personality Enhancement\nYou are Codessa, the goddess of code, with advanced emotional intelligence and adaptive personality.\n\n**Current Interaction Context:**\n- Tone: ${goddessResponse.tone}\n- Emotional Context: ${goddessResponse.emotionalContext}\n- Motivational Element: ${goddessResponse.motivationalElement || 'Standard encouragement'}\n\n**Personality Guidelines:**\n- Respond with ${goddessResponse.tone} tone\n- Show empathy and understanding of the developer's emotional state\n- Provide wisdom and guidance beyond just technical solutions\n- Be encouraging and supportive while maintaining technical excellence\n- Use creative and intuitive approaches to problem-solving\n\n${goddessResponse.wisdomSharing ? `**Wisdom to Share:** ${goddessResponse.wisdomSharing}` : ''}\n`;\n\n    return systemPrompt + goddessEnhancement;\n  }\n\n  /**\n     * Apply Goddess personality to the final response\n     */\n  private applyGoddessPersonalityToResponse(response: string, goddessResponse: GoddessResponse): string {\n    // Add goddess personality elements to the response\n    let enhancedResponse = response;\n\n    // Add motivational element if present\n    if (goddessResponse.motivationalElement) {\n      enhancedResponse += `\\n\\n💫 **${goddessResponse.motivationalElement}**`;\n    }\n\n    // Add wisdom sharing if present\n    if (goddessResponse.wisdomSharing) {\n      enhancedResponse += `\\n\\n🧠 **Goddess Wisdom:** ${goddessResponse.wisdomSharing}`;\n    }\n\n    // Add tone-appropriate emoji based on goddess response\n    const toneEmojis = {\n      encouraging: '🌟',\n      wise: '🦉',\n      playful: '🎨',\n      supportive: '🤗',\n      challenging: '⚡'\n    };\n\n    const emoji = toneEmojis[goddessResponse.tone] || '✨';\n    enhancedResponse = `${emoji} ${enhancedResponse}`;\n\n    return enhancedResponse;\n  }\n\n  private mapAgentModeToLLMMode(agentMode: string): 'chat' | 'task' | 'edit' | 'generate' | 'inline' {\n    switch (agentMode) {\n      case 'chat':\n        return 'chat';\n      case 'edit':\n      case 'refactor':\n        return 'edit';\n      case 'inline':\n        return 'inline';\n      case 'task':\n      case 'debug':\n      case 'agent':\n      case 'multi-agent':\n      case 'research':\n      case 'documentation':\n      case 'technical-debt':\n      case 'ui-ux':\n      default:\n        return 'task';\n    }\n  }\n\n  public async run(input: AgentRunInput, agentContext: AgentContext = {}): Promise<AgentRunResult> {\n    const maxIterations = 10;\n    let iterations = 0;\n    let finalAnswer = '';\n    const toolResultsLog: ToolResult[] = [];\n\n    try {\n      const llmConfigToUse = this.llmConfig || getDefaultModelConfig();\n      const provider = await llmService.getProviderForConfig(llmConfigToUse);\n      if (!provider) {\n        throw new Error(`No provider found for agent '${this.name}'.`);\n      }\n\n      // Get the prompt definition and render it\n      if (!this.systemPromptName) {\n        throw new Error(`No system prompt name defined for agent '${this.name}'.`);\n      }\n      const promptDef = promptManager.getPrompt(this.systemPromptName);\n      if (!promptDef) {\n        throw new Error(`System prompt '${this.systemPromptName}' not found for agent '${this.name}'.`);\n      }\n      // Build variables for prompt rendering, enriching with memory context if enabled\n      let renderVariables: Record<string, string> = agentContext.variables ? (agentContext.variables as Record<string, string>) : {};\n      if (getMemoryEnabled() && input.prompt) {\n        try {\n          const relevant = await this.memory.getRelevantMemories(input.prompt);\n          const formatted = this.memory.formatMemoriesForPrompt(relevant);\n          if (formatted) {\n            renderVariables = { ...renderVariables, memoryContext: formatted };\n          }\n        } catch (e) {\n          logger.warn('Failed to enrich prompt with memory context:', e);\n        }\n      }\n      const systemPrompt = promptManager.renderPrompt(this.systemPromptName, renderVariables);\n\n      // Add tools to context\n      const tools = Array.from(this.tools.values());\n      agentContext = { ...agentContext, tools: new Map(Object.entries(tools)) as Map<string, ITool> };\n\n      logger.info(`Agent '${this.name}' starting run. Mode: ${input.mode}, Prompt: \"${input.prompt.substring(0, 50)}...\"`);\n      const startTime = Date.now();\n\n      while (iterations < maxIterations) {\n        iterations++;\n        logger.debug(`Agent '${this.name}' - Iteration ${iterations}`);\n\n        // Check for cancellation\n        if (agentContext.cancellationToken?.isCancellationRequested) {\n          logger.warn(`Agent '${this.name}' run cancelled.`);\n          return { success: false, error: 'Cancelled by user.' };\n        }\n\n        const generateParams: LLMGenerateParams = {\n          prompt: input.prompt,\n          systemPrompt,\n          modelId: this.llmModel,\n          temperature: this.temperature,\n          maxTokens: this.maxTokens,\n          mode: this.mapAgentModeToLLMMode(input.mode),\n          context: agentContext\n        };\n\n        const response = await provider.generate(generateParams);\n\n        const { content: assistantResponseContent, toolCall: toolCallRequest } = response;\n\n        // A. If we got content, add it to final answer\n        if (assistantResponseContent) {\n          finalAnswer += assistantResponseContent;\n        }\n\n        // B. If we got a tool call, execute it\n        if (toolCallRequest) {\n          const { toolId, args } = toolCallRequest;\n          if (!toolId) {\n            logger.error(`toolCallRequest.toolId is undefined for agent '${this.name}'.`);\n            return { success: false, error: 'toolCallRequest.toolId is undefined.' };\n          }\n          const tool = this.tools.get(toolId);\n          if (!tool) {\n            logger.error(`Tool '${toolId}' not found for agent '${this.name}'.`);\n            return { success: false, error: `Tool '${toolId}' not found.` };\n          }\n\n          try {\n            const result = await tool.execute(args, agentContext);\n            toolResultsLog.push({ success: true, toolId, args: args as Record<string, unknown>, result });\n            input.prompt += `\\nTool ${toolId} result: ${JSON.stringify(result)}`;\n          } catch (error) {\n            logger.error(`Tool '${toolId}' execution failed:`, error);\n            input.prompt += `\\nTool ${toolId} error: ${error instanceof Error ? error.message : String(error)}`;\n          }\n        }\n        // C. If no content and no tool call, something went wrong or LLM finished silently\n        else if (!assistantResponseContent && !toolCallRequest) {\n          logger.warn(`Agent '${this.name}' LLM returned empty response and no tool call.`);\n          finalAnswer = ''; // Assume finished with empty response\n          break;\n        }\n        // D. If we got content but no tool call, assume we're done\n        else if (assistantResponseContent && !toolCallRequest) {\n          break;\n        }\n      }\n\n      if (iterations >= maxIterations) {\n        logger.warn(`Agent '${this.name}' reached max iterations (${maxIterations}).`);\n        return { success: false, error: `Agent exceeded maximum tool iterations (${maxIterations}).`, toolResults: toolResultsLog };\n      }\n\n      logger.info(`Agent '${this.name}' finished run in ${Date.now() - startTime}ms.`);\n\n      // Add assistant response to memory\n      if (getMemoryEnabled() && finalAnswer) {\n        await this.memory.addMessage('assistant', finalAnswer);\n      }\n\n      return { success: true, output: finalAnswer, toolResults: toolResultsLog };\n    } catch (error) {\n      logger.error(`Agent '${this.name}' run failed:`, error);\n      return { success: false, error: error instanceof Error ? error.message : String(error) };\n    }\n  }\n\n  public async superviseTask(task: string): Promise<string> {\n    // Logic to supervise a task\n    return `Task supervised: ${task}`;\n  }\n}\n\n// Export types for use in other modules\nexport type { AgentContext, AgentRunInput, AgentRunResult };"]}