{"_type": "UMLClass", "_id": "AAAAAAGH1VectorStoreFactory=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "VectorStoreFactory", "visibility": "public", "operations": [{"_type": "UMLOperation", "_id": "AAAAAAGH1VectorStoreFactoryOp1=", "_parent": {"$ref": "AAAAAAGH1VectorStoreFactory="}, "name": "createVectorStore", "visibility": "public", "isStatic": true, "parameters": [{"_type": "UMLParameter", "_id": "AAAAAAGH1VectorStoreFactoryOp1P1=", "_parent": {"$ref": "AAAAAAGH1VectorStoreFactoryOp1="}, "name": "type", "type": "string"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1VectorStoreFactoryOp1P2=", "_parent": {"$ref": "AAAAAAGH1VectorStoreFactoryOp1="}, "name": "embeddings", "type": "Embeddings"}, {"_type": "UMLParameter", "_id": "AAAAAAGH1VectorStoreFactoryOp1P3=", "_parent": {"$ref": "AAAAAAGH1VectorStoreFactoryOp1="}, "type": "Promise<IVectorStore>", "direction": "return"}]}], "isAbstract": false}