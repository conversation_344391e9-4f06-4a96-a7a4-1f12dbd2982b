{"telemetry.machineId": "4e2bebe2f2f31eee911f69ff8d1066fd390c6accc4e4d03ff2285875bd4e1758", "telemetry.sqmId": "{F10D2CF1-54FA-4CA9-919E-333621B7068B}", "telemetry.devDeviceId": "fea3549e-b221-47e6-992a-7f1d85b87e1f", "backupWorkspaces": {"workspaces": [], "folders": [{"folderUri": "file:///e%3A/_2025_Coding_Projects/AI/Codessa"}], "emptyWindows": []}, "windowControlHeight": 35, "profileAssociations": {"workspaces": {"file:///e%3A/_2025_Coding_Projects/AI/Codessa": "__default__profile__"}, "emptyWindows": {}}, "theme": "vs-dark", "themeBackground": "#1f1f1f", "windowSplash": {"zoomLevel": 0, "baseTheme": "vs-dark", "colorInfo": {"foreground": "#cccccc", "background": "#1f1f1f", "editorBackground": "#1f1f1f", "titleBarBackground": "#181818", "titleBarBorder": "#2b2b2b", "activityBarBackground": "#181818", "activityBarBorder": "#2b2b2b", "sideBarBackground": "#181818", "sideBarBorder": "#2b2b2b", "statusBarBackground": "#181818", "statusBarBorder": "#2b2b2b", "statusBarNoFolderBackground": "#1f1f1f"}, "layoutInfo": {"sideBarSide": "left", "editorPartMinWidth": 220, "titleBarHeight": 35, "activityBarWidth": 48, "sideBarWidth": 300, "auxiliarySideBarWidth": 0, "statusBarHeight": 22, "windowBorder": false}}, "windowsState": {"lastActiveWindow": {"folder": "file:///e%3A/_2025_Coding_Projects/AI/Codessa", "backupPath": "E:\\_2025_Coding_Projects\\AI\\Codessa\\.vscode-test\\user-data\\Backups\\bea3555419ec70562f0145d2f7044985", "uiState": {"mode": 0, "x": 360, "y": 140, "width": 1200, "height": 800}}, "lastPluginDevelopmentHostWindow": {"uiState": {"mode": 1, "x": 360, "y": 140, "width": 1200, "height": 800}}, "openedWindows": []}}