{"version": 3, "file": "codeIntelligenceTool.js", "sourceRoot": "", "sources": ["../../src/tools/codeIntelligenceTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,6BAAwB;AAExB,MAAa,oBAAoB;IACtB,EAAE,GAAG,WAAW,CAAC;IACjB,IAAI,GAAG,mBAAmB,CAAC;IAC3B,WAAW,GAAG,+DAA+D,CAAC;IAC9E,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,2BAA2B,CAAC;QACnH,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CAAC;QACjE,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;YACjB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;YAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;SACnB,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QAC1C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;KAClE,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE;YACzI,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,uBAAuB,EAAE;YAClE,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,kBAAkB,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;YACnI,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;SACnE;QACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;KACrB,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,OAAsB;QACpF,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;QACtC,IAAI,CAAC;YACH,IAAI,MAAM,KAAK,gBAAgB,EAAE,CAAC;gBAChC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;gBACrC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6CAA6C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;gBACzI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC9D,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAChE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAoB,kCAAkC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC5H,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YAC3E,CAAC;iBAAM,IAAI,MAAM,KAAK,gBAAgB,EAAE,CAAC;gBACvC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;gBACrC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6CAA6C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;gBACzI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC9D,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAChE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAoB,iCAAiC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC3H,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YAC3E,CAAC;iBAAM,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;gBACrC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;gBACpC,IAAI,CAAC,KAAK;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0CAA0C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;gBACtH,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAQ,uCAAuC,EAAE,KAAK,CAAC,CAAC;gBAC5G,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YACzE,CAAC;iBAAM,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;gBAC9B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;gBACrC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;oBAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6CAA6C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;gBACzI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC9D,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAChE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAQ,6BAA6B,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBACxG,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YACxE,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qCAAqC,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;YAC/G,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oCAAoC,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC9H,CAAC;IACH,CAAC;CACF;AA9DD,oDA8DC;AAEY,QAAA,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../agents/agentUtilities/agent';\nimport { z } from 'zod';\n\nexport class CodeIntelligenceTool implements ITool {\n  readonly id = 'codeIntel';\n  readonly name = 'Code Intelligence';\n  readonly description = 'Go to definition, find references, symbol search, hover docs.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    action: z.enum(['gotoDefinition', 'findReferences', 'symbolSearch', 'hover']).describe('Code intelligence action.'),\n    filePath: z.string().optional().describe('File path for action.'),\n    position: z.object({\n      line: z.number(),\n      column: z.number()\n    }).optional().describe('Cursor position.'),\n    query: z.string().optional().describe('Query for symbol search.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      action: { type: 'string', enum: ['gotoDefinition', 'findReferences', 'symbolSearch', 'hover'], description: 'Code intelligence action.' },\n      filePath: { type: 'string', description: 'File path for action.' },\n      position: { type: 'object', description: 'Cursor position.', properties: { line: { type: 'number' }, column: { type: 'number' } } },\n      query: { type: 'string', description: 'Query for symbol search.' }\n    },\n    required: ['action']\n  };\n\n  async execute(actionName: string | undefined, input: ToolInput, context?: AgentContext): Promise<ToolResult> {\n    const action = input.action as string;\n    try {\n      if (action === 'gotoDefinition') {\n        const { filePath, position } = input;\n        if (!filePath || !position) return { success: false, error: '\\'filePath\\' and \\'position\\' are required.', toolId: this.id, actionName };\n        const doc = await vscode.workspace.openTextDocument(filePath);\n        const pos = new vscode.Position(position.line, position.column);\n        const locations = await vscode.commands.executeCommand<vscode.Location[]>('vscode.executeDefinitionProvider', doc.uri, pos);\n        return { success: true, output: locations, toolId: this.id, actionName };\n      } else if (action === 'findReferences') {\n        const { filePath, position } = input;\n        if (!filePath || !position) return { success: false, error: '\\'filePath\\' and \\'position\\' are required.', toolId: this.id, actionName };\n        const doc = await vscode.workspace.openTextDocument(filePath);\n        const pos = new vscode.Position(position.line, position.column);\n        const locations = await vscode.commands.executeCommand<vscode.Location[]>('vscode.executeReferenceProvider', doc.uri, pos);\n        return { success: true, output: locations, toolId: this.id, actionName };\n      } else if (action === 'symbolSearch') {\n        const query = input.query as string;\n        if (!query) return { success: false, error: '\\'query\\' is required for symbol search.', toolId: this.id, actionName };\n        const symbols = await vscode.commands.executeCommand<any[]>('vscode.executeWorkspaceSymbolProvider', query);\n        return { success: true, output: symbols, toolId: this.id, actionName };\n      } else if (action === 'hover') {\n        const { filePath, position } = input;\n        if (!filePath || !position) return { success: false, error: '\\'filePath\\' and \\'position\\' are required.', toolId: this.id, actionName };\n        const doc = await vscode.workspace.openTextDocument(filePath);\n        const pos = new vscode.Position(position.line, position.column);\n        const hovers = await vscode.commands.executeCommand<any[]>('vscode.executeHoverProvider', doc.uri, pos);\n        return { success: true, output: hovers, toolId: this.id, actionName };\n      } else {\n        return { success: false, error: `Unknown code intelligence action: ${action}`, toolId: this.id, actionName };\n      }\n    } catch (error: any) {\n      return { success: false, error: `Code intelligence action failed: ${error.message || error}`, toolId: this.id, actionName };\n    }\n  }\n}\n\nexport const codeIntelligenceTool = new CodeIntelligenceTool();\n"]}