"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowManager = exports.WorkflowManager = void 0;
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
/**
 * Workflow Manager for handling workflow operations
 */
class WorkflowManager {
    get logger() {
        return logger_1.logger;
    }
    static instance;
    constructor() { }
    /**
     * Get the singleton instance
     */
    static getInstance() {
        if (!WorkflowManager.instance) {
            WorkflowManager.instance = new WorkflowManager();
        }
        return WorkflowManager.instance;
    }
    /**
     * Get a workflow for a specific mode
     */
    async getWorkflowForMode(mode) {
        logger_1.logger.info(`Getting workflow for mode: ${mode}`);
        // Get workflows with the specified mode tag
        const workflows = workflowRegistry_1.workflowRegistry.getWorkflowsByTag(mode);
        if (workflows.length > 0) {
            // Return the first matching workflow
            return workflows[0];
        }
        // If no specific workflow found, try to get a default workflow
        const defaultWorkflows = workflowRegistry_1.workflowRegistry.getWorkflowsByTag('default');
        if (defaultWorkflows.length > 0) {
            return defaultWorkflows[0];
        }
        return undefined;
    }
    /**
     * Get all available workflows
     */
    async getAllWorkflows() {
        return workflowRegistry_1.workflowRegistry.getAllWorkflows();
    }
    /**
     * Get workflows by tag
     */
    async getWorkflowsByTag(tag) {
        return workflowRegistry_1.workflowRegistry.getWorkflowsByTag(tag);
    }
}
exports.WorkflowManager = WorkflowManager;
// Export singleton instance
exports.workflowManager = WorkflowManager.getInstance();
//# sourceMappingURL=workflowManager.js.map