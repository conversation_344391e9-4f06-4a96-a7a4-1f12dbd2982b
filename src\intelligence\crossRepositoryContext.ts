/**
 * Cross-Repository Context System - Multi-repo intelligence
 *
 * Provides multi-repo awareness, cross-repo refactoring, dependency tracking,
 * shared context, and monorepo support while integrating with existing
 * Codessa infrastructure.
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { SupervisorAgent } from '../agents/agentTypes/supervisorAgent';
import { ToolRegistry } from '../tools/toolRegistry';
import { QuantumMemorySystem } from '../memory/quantum/quantumMemorySystem';
import { ProjectWideIntelligence } from './projectWideIntelligence';
import { logger } from '../logger';

export interface RepositoryInfo {
  id: string;
  name: string;
  path: string;
  remoteUrl?: string;
  branch: string;
  lastCommit: string;
  language: string;
  framework?: string;
  dependencies: string[];
  isMonorepo: boolean;
  subprojects?: string[];
  relatedRepos: string[];
  sharedDependencies: string[];
}

export interface CrossRepoRelationship {
  sourceRepo: string;
  targetRepo: string;
  relationshipType: 'dependency' | 'shared-library' | 'microservice' | 'monorepo-package' | 'fork' | 'template';
  strength: number; // 0-1 scale
  description: string;
  sharedFiles: string[];
  sharedDependencies: string[];
  communicationPatterns: string[];
}

export interface CrossRepoRefactoring {
  id: string;
  description: string;
  affectedRepos: string[];
  changes: {
    repo: string;
    files: string[];
    operations: RefactoringOperation[];
  }[];
  dependencies: string[];
  estimatedImpact: 'low' | 'medium' | 'high';
  riskLevel: 'low' | 'medium' | 'high';
  rollbackPlan: string;
}

export interface RefactoringOperation {
  type: 'rename' | 'move' | 'extract' | 'inline' | 'update-dependency' | 'update-api';
  file: string;
  oldValue: string;
  newValue: string;
  line?: number;
  column?: number;
}

export interface MonorepoStructure {
  rootPath: string;
  packages: MonorepoPackage[];
  sharedDependencies: string[];
  buildSystem: 'lerna' | 'nx' | 'rush' | 'yarn-workspaces' | 'npm-workspaces' | 'custom';
  workspaceConfig: any;
  dependencyGraph: { [packageName: string]: string[] };
}

export interface MonorepoPackage {
  name: string;
  path: string;
  version: string;
  dependencies: string[];
  devDependencies: string[];
  peerDependencies: string[];
  scripts: { [scriptName: string]: string };
  isPrivate: boolean;
}

export interface SharedContext {
  id: string;
  name: string;
  description: string;
  repos: string[];
  sharedFiles: string[];
  sharedTypes: string[];
  sharedConstants: string[];
  communicationProtocols: string[];
  lastUpdated: number;
  version: string;
}

export class CrossRepositoryContext {
  private supervisorAgent: SupervisorAgent;
  private toolRegistry: ToolRegistry;
  private quantumMemory: QuantumMemorySystem;
  private projectIntelligence: ProjectWideIntelligence;

  // Repository tracking
  private repositories: Map<string, RepositoryInfo> = new Map();
  private relationships: Map<string, CrossRepoRelationship[]> = new Map();
  private sharedContexts: Map<string, SharedContext> = new Map();
  private monorepoStructures: Map<string, MonorepoStructure> = new Map();

  // Analysis cache
  private analysisCache: Map<string, { result: any; timestamp: number }> = new Map();
  private readonly cacheExpiry = 1800000; // 30 minutes

  constructor(
    supervisorAgent: SupervisorAgent,
    toolRegistry: ToolRegistry,
    quantumMemory: QuantumMemorySystem,
    projectIntelligence: ProjectWideIntelligence
  ) {
    this.supervisorAgent = supervisorAgent;
    this.toolRegistry = toolRegistry;
    this.quantumMemory = quantumMemory;
    this.projectIntelligence = projectIntelligence;

    this.setupEventListeners();
  }

  /**
     * Initialize cross-repository context system
     */
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing Cross-Repository Context System...');

      // Discover repositories
      await this.discoverRepositories();

      // Analyze relationships
      await this.analyzeRepositoryRelationships();

      // Detect monorepos
      await this.detectMonorepoStructures();

      // Build shared contexts
      await this.buildSharedContexts();

      logger.info('Cross-Repository Context System initialized successfully');
    } catch (error) {
      logger.error(`Failed to initialize Cross-Repository Context System: ${error}`);
      throw error;
    }
  }

  /**
     * Discover all repositories in workspace and related locations
     */
  public async discoverRepositories(): Promise<RepositoryInfo[]> {
    const repositories: RepositoryInfo[] = [];

    // Check current workspace folders
    if (vscode.workspace.workspaceFolders) {
      for (const folder of vscode.workspace.workspaceFolders) {
        const repoInfo = await this.analyzeRepository(folder.uri.fsPath);
        if (repoInfo) {
          repositories.push(repoInfo);
          this.repositories.set(repoInfo.id, repoInfo);
        }
      }
    }

    // Look for related repositories
    for (const repo of repositories) {
      const relatedRepos = await this.findRelatedRepositories(repo);
      for (const related of relatedRepos) {
        if (!this.repositories.has(related.id)) {
          repositories.push(related);
          this.repositories.set(related.id, related);
        }
      }
    }

    logger.info(`Discovered ${repositories.length} repositories`);
    return repositories;
  }

  /**
     * Analyze relationships between repositories
     */
  public async analyzeRepositoryRelationships(): Promise<CrossRepoRelationship[]> {
    const relationships: CrossRepoRelationship[] = [];
    const repos = Array.from(this.repositories.values());

    for (let i = 0; i < repos.length; i++) {
      for (let j = i + 1; j < repos.length; j++) {
        const relationship = await this.analyzeRepoRelationship(repos[i], repos[j]);
        if (relationship) {
          relationships.push(relationship);

          // Store bidirectional relationships
          if (!this.relationships.has(repos[i].id)) {
            this.relationships.set(repos[i].id, []);
          }
          if (!this.relationships.has(repos[j].id)) {
            this.relationships.set(repos[j].id, []);
          }

          this.relationships.get(repos[i].id)!.push(relationship);
          this.relationships.get(repos[j].id)!.push({
            ...relationship,
            sourceRepo: repos[j].id,
            targetRepo: repos[i].id
          });
        }
      }
    }

    logger.info(`Analyzed ${relationships.length} repository relationships`);
    return relationships;
  }

  /**
     * Plan cross-repository refactoring
     */
  public async planCrossRepoRefactoring(
    description: string,
    affectedRepos: string[],
    refactoringType: 'rename' | 'move' | 'extract' | 'api-change' | 'dependency-update'
  ): Promise<CrossRepoRefactoring> {
    logger.info(`Planning cross-repo refactoring: ${description}`);

    const refactoringId = `refactor_${Date.now()}`;
    const changes: CrossRepoRefactoring['changes'] = [];

    // Analyze impact on each repository
    for (const repoId of affectedRepos) {
      const repo = this.repositories.get(repoId);
      if (!repo) continue;

      const repoChanges = await this.analyzeRefactoringImpact(repo, description, refactoringType);
      changes.push({
        repo: repoId,
        files: repoChanges.files,
        operations: repoChanges.operations
      });
    }

    // Analyze dependencies between changes
    const dependencies = await this.analyzeRefactoringDependencies(changes);

    // Assess risk and impact
    const { estimatedImpact, riskLevel } = this.assessRefactoringRisk(changes);

    // Generate rollback plan
    const rollbackPlan = await this.generateRollbackPlan(changes);

    const refactoring: CrossRepoRefactoring = {
      id: refactoringId,
      description,
      affectedRepos,
      changes,
      dependencies,
      estimatedImpact,
      riskLevel,
      rollbackPlan
    };

    // Store in quantum memory
    await this.quantumMemory.storeTemporalMemory(
      `Cross-repo refactoring planned: ${description}`,
      {
        filePath: 'cross-repo-refactoring',
        projectContext: 'multi-repo'
      },
      'create',
      { tags: ['cross-repo', 'refactoring', 'planning'] }
    );

    return refactoring;
  }

  /**
     * Execute cross-repository refactoring
     */
  public async executeCrossRepoRefactoring(refactoring: CrossRepoRefactoring): Promise<boolean> {
    logger.info(`Executing cross-repo refactoring: ${refactoring.id}`);

    try {
      // Execute changes in dependency order
      const sortedChanges = this.sortChangesByDependencies(refactoring.changes, refactoring.dependencies);

      for (const change of sortedChanges) {
        const repo = this.repositories.get(change.repo);
        if (!repo) continue;

        logger.info(`Applying changes to repository: ${repo.name}`);

        // Use existing refactoring tools
        const refactorTool = this.toolRegistry.getTool('refactor');
        if (refactorTool) {
          for (const operation of change.operations) {
            const result = await refactorTool.execute('applyRefactoring', {
              file: operation.file,
              type: operation.type,
              oldValue: operation.oldValue,
              newValue: operation.newValue,
              line: operation.line,
              column: operation.column
            });

            if (!result.success) {
              logger.error(`Refactoring operation failed: ${result.error}`);
              // Implement rollback logic here
              return false;
            }
          }
        }
      }

      // Update quantum memory
      await this.quantumMemory.storeTemporalMemory(
        `Cross-repo refactoring executed: ${refactoring.description}`,
        {
          filePath: 'cross-repo-refactoring',
          projectContext: 'multi-repo'
        },
        'update',
        { tags: ['cross-repo', 'refactoring', 'executed'] }
      );

      logger.info('Cross-repo refactoring completed successfully');
      return true;

    } catch (error) {
      logger.error(`Cross-repo refactoring failed: ${error}`);
      return false;
    }
  }

  /**
     * Detect and analyze monorepo structures
     */
  public async detectMonorepoStructures(): Promise<MonorepoStructure[]> {
    const monorepos: MonorepoStructure[] = [];

    for (const repo of this.repositories.values()) {
      if (repo.isMonorepo) {
        const structure = await this.analyzeMonorepoStructure(repo.path);
        if (structure) {
          monorepos.push(structure);
          this.monorepoStructures.set(repo.id, structure);
        }
      }
    }

    logger.info(`Detected ${monorepos.length} monorepo structures`);
    return monorepos;
  }

  /**
     * Build shared contexts across repositories
     */
  public async buildSharedContexts(): Promise<SharedContext[]> {
    const contexts: SharedContext[] = [];

    // Analyze shared dependencies
    const sharedDeps = this.findSharedDependencies();

    // Analyze shared file patterns
    const sharedFiles = await this.findSharedFilePatterns();

    // Create shared contexts
    for (const [depName, repos] of sharedDeps) {
      if (repos.length > 1) {
        const context: SharedContext = {
          id: `shared_${depName}_${Date.now()}`,
          name: `Shared Dependency: ${depName}`,
          description: `Shared dependency ${depName} used across ${repos.length} repositories`,
          repos,
          sharedFiles: sharedFiles.get(depName) || [],
          sharedTypes: [],
          sharedConstants: [],
          communicationProtocols: [],
          lastUpdated: Date.now(),
          version: '1.0.0'
        };

        contexts.push(context);
        this.sharedContexts.set(context.id, context);
      }
    }

    logger.info(`Built ${contexts.length} shared contexts`);
    return contexts;
  }

  /**
     * Get cross-repository insights
     */
  public async getCrossRepoInsights(): Promise<any> {
    const insights = {
      totalRepositories: this.repositories.size,
      relationships: Array.from(this.relationships.values()).flat().length,
      sharedContexts: this.sharedContexts.size,
      monorepos: this.monorepoStructures.size,
      topSharedDependencies: this.getTopSharedDependencies(),
      strongestRelationships: this.getStrongestRelationships(),
      refactoringOpportunities: await this.identifyRefactoringOpportunities()
    };

    return insights;
  }

  // Private helper methods
  private async analyzeRepository(repoPath: string): Promise<RepositoryInfo | null> {
    try {
      // Check if it's a git repository
      const gitPath = path.join(repoPath, '.git');
      if (!fs.existsSync(gitPath)) {
        return null;
      }

      // Get repository information
      const gitTool = this.toolRegistry.getTool('git');
      let remoteUrl = '';
      let branch = 'main';
      let lastCommit = '';

      if (gitTool) {
        const remoteResult = await gitTool.execute('getRemoteUrl', { path: repoPath });
        if (remoteResult.success) {
          remoteUrl = remoteResult.output;
        }

        const branchResult = await gitTool.execute('getCurrentBranch', { path: repoPath });
        if (branchResult.success) {
          branch = branchResult.output;
        }

        const commitResult = await gitTool.execute('getLastCommit', { path: repoPath });
        if (commitResult.success) {
          lastCommit = commitResult.output;
        }
      }

      // Analyze project structure
      const projectReport = await this.projectIntelligence.generateProjectReport(repoPath);

      // Detect if it's a monorepo
      const isMonorepo = await this.detectIfMonorepo(repoPath);

      const repoInfo: RepositoryInfo = {
        id: `repo_${path.basename(repoPath)}_${Date.now()}`,
        name: path.basename(repoPath),
        path: repoPath,
        remoteUrl,
        branch,
        lastCommit,
        language: this.detectPrimaryLanguage(projectReport.codebaseMap.languages),
        dependencies: projectReport.codebaseMap.dependencies.map(d => d.name),
        isMonorepo,
        relatedRepos: [],
        sharedDependencies: []
      };

      return repoInfo;

    } catch (error) {
      logger.warn(`Failed to analyze repository ${repoPath}: ${error}`);
      return null;
    }
  }

  private async findRelatedRepositories(repo: RepositoryInfo): Promise<RepositoryInfo[]> {
    const related: RepositoryInfo[] = [];

    // Look for repositories in parent directory
    const parentDir = path.dirname(repo.path);
    try {
      const siblings = await fs.promises.readdir(parentDir, { withFileTypes: true });

      for (const sibling of siblings) {
        if (sibling.isDirectory() && sibling.name !== repo.name) {
          const siblingPath = path.join(parentDir, sibling.name);
          const siblingRepo = await this.analyzeRepository(siblingPath);
          if (siblingRepo) {
            related.push(siblingRepo);
          }
        }
      }
    } catch (error) {
      logger.warn(`Failed to find related repositories: ${error}`);
    }

    return related;
  }

  private async analyzeRepoRelationship(repo1: RepositoryInfo, repo2: RepositoryInfo): Promise<CrossRepoRelationship | null> {
    // Analyze shared dependencies
    const sharedDeps = repo1.dependencies.filter(dep => repo2.dependencies.includes(dep));

    // Calculate relationship strength
    let strength = 0;
    strength += sharedDeps.length * 0.1;

    // Check for shared file patterns
    const sharedFiles = await this.findSharedFiles(repo1.path, repo2.path);
    strength += sharedFiles.length * 0.2;

    // Check if they're in the same parent directory (likely related projects)
    if (path.dirname(repo1.path) === path.dirname(repo2.path)) {
      strength += 0.3;
    }

    if (strength < 0.1) {
      return null; // Not related enough
    }

    // Determine relationship type
    let relationshipType: CrossRepoRelationship['relationshipType'] = 'dependency';
    if (repo1.isMonorepo || repo2.isMonorepo) {
      relationshipType = 'monorepo-package';
    } else if (sharedDeps.length > 5) {
      relationshipType = 'shared-library';
    }

    return {
      sourceRepo: repo1.id,
      targetRepo: repo2.id,
      relationshipType,
      strength: Math.min(1, strength),
      description: `Shared ${sharedDeps.length} dependencies and ${sharedFiles.length} file patterns`,
      sharedFiles,
      sharedDependencies: sharedDeps,
      communicationPatterns: []
    };
  }

  private async findSharedFiles(repo1Path: string, repo2Path: string): Promise<string[]> {
    // Simple implementation - in reality, this would be more sophisticated
    const sharedFiles: string[] = [];

    try {
      const files1 = await this.getFileList(repo1Path);
      const files2 = await this.getFileList(repo2Path);

      // Find files with same relative paths
      for (const file1 of files1) {
        const relativePath = path.relative(repo1Path, file1);
        const correspondingFile = path.join(repo2Path, relativePath);

        if (files2.includes(correspondingFile)) {
          sharedFiles.push(relativePath);
        }
      }
    } catch (error) {
      logger.warn(`Failed to find shared files: ${error}`);
    }

    return sharedFiles;
  }

  private async getFileList(dirPath: string): Promise<string[]> {
    const files: string[] = [];

    try {
      const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
          const subFiles = await this.getFileList(fullPath);
          files.push(...subFiles);
        } else if (entry.isFile()) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      logger.warn('Error accessing directory:', error);
    }

    return files;
  }

  private detectPrimaryLanguage(languages: { [language: string]: number }): string {
    let maxCount = 0;
    let primaryLanguage = 'unknown';

    for (const [language, count] of Object.entries(languages)) {
      if (count > maxCount) {
        maxCount = count;
        primaryLanguage = language;
      }
    }

    return primaryLanguage;
  }

  private async detectIfMonorepo(repoPath: string): Promise<boolean> {
    // Check for common monorepo indicators
    const monorepoIndicators = [
      'lerna.json',
      'nx.json',
      'rush.json',
      'packages',
      'apps',
      'libs'
    ];

    for (const indicator of monorepoIndicators) {
      const indicatorPath = path.join(repoPath, indicator);
      if (fs.existsSync(indicatorPath)) {
        return true;
      }
    }

    // Check package.json for workspaces
    const packageJsonPath = path.join(repoPath, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf8'));
        if (packageJson.workspaces) {
          return true;
        }
      } catch (error) {
        logger.debug('Failed to parse package.json:', error);
      }
    }

    return false;
  }

  private async analyzeMonorepoStructure(repoPath: string): Promise<MonorepoStructure | null> {
    try {
      const structure: MonorepoStructure = {
        rootPath: repoPath,
        packages: [],
        sharedDependencies: [],
        buildSystem: 'custom',
        workspaceConfig: {},
        dependencyGraph: {}
      };

      // Detect build system
      if (fs.existsSync(path.join(repoPath, 'lerna.json'))) {
        structure.buildSystem = 'lerna';
      } else if (fs.existsSync(path.join(repoPath, 'nx.json'))) {
        structure.buildSystem = 'nx';
      } else if (fs.existsSync(path.join(repoPath, 'rush.json'))) {
        structure.buildSystem = 'rush';
      }

      // Find packages
      const packagesDir = path.join(repoPath, 'packages');
      if (fs.existsSync(packagesDir)) {
        const packageDirs = await fs.promises.readdir(packagesDir, { withFileTypes: true });

        for (const dir of packageDirs) {
          if (dir.isDirectory()) {
            const packagePath = path.join(packagesDir, dir.name);
            const packageInfo = await this.analyzeMonorepoPackage(packagePath);
            if (packageInfo) {
              structure.packages.push(packageInfo);
            }
          }
        }
      }

      return structure;

    } catch (error) {
      logger.warn(`Failed to analyze monorepo structure: ${error}`);
      return null;
    }
  }

  private async analyzeMonorepoPackage(packagePath: string): Promise<MonorepoPackage | null> {
    const packageJsonPath = path.join(packagePath, 'package.json');

    if (!fs.existsSync(packageJsonPath)) {
      return null;
    }

    try {
      const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf8'));

      return {
        name: packageJson.name || path.basename(packagePath),
        path: packagePath,
        version: packageJson.version || '0.0.0',
        dependencies: Object.keys(packageJson.dependencies || {}),
        devDependencies: Object.keys(packageJson.devDependencies || {}),
        peerDependencies: Object.keys(packageJson.peerDependencies || {}),
        scripts: packageJson.scripts || {},
        isPrivate: packageJson.private || false
      };
    } catch (error) {
      logger.warn(`Failed to analyze monorepo package ${packagePath}: ${error}`);
      return null;
    }
  }

  private findSharedDependencies(): Map<string, string[]> {
    const sharedDeps = new Map<string, string[]>();

    for (const repo of this.repositories.values()) {
      for (const dep of repo.dependencies) {
        if (!sharedDeps.has(dep)) {
          sharedDeps.set(dep, []);
        }
        sharedDeps.get(dep)!.push(repo.id);
      }
    }

    return sharedDeps;
  }

  private async findSharedFilePatterns(): Promise<Map<string, string[]>> {
    const sharedFiles = new Map<string, string[]>();

    // This would analyze file patterns across repositories
    // Simplified implementation for now

    return sharedFiles;
  }

  private getTopSharedDependencies(): Array<{ name: string; repoCount: number }> {
    const sharedDeps = this.findSharedDependencies();

    return Array.from(sharedDeps.entries())
      .map(([name, repos]) => ({ name, repoCount: repos.length }))
      .filter(dep => dep.repoCount > 1)
      .sort((a, b) => b.repoCount - a.repoCount)
      .slice(0, 10);
  }

  private getStrongestRelationships(): CrossRepoRelationship[] {
    const allRelationships = Array.from(this.relationships.values()).flat();

    return allRelationships
      .sort((a, b) => b.strength - a.strength)
      .slice(0, 5);
  }

  private async identifyRefactoringOpportunities(): Promise<string[]> {
    const opportunities: string[] = [];

    // Identify duplicate code across repositories
    const sharedDeps = this.findSharedDependencies();
    for (const [dep, repos] of sharedDeps) {
      if (repos.length > 2) {
        opportunities.push(`Extract shared library for ${dep} used in ${repos.length} repositories`);
      }
    }

    // Identify outdated dependencies
    for (const repo of this.repositories.values()) {
      // This would check for outdated dependencies
      opportunities.push(`Update dependencies in ${repo.name}`);
    }

    return opportunities.slice(0, 5);
  }

  private async analyzeRefactoringImpact(
    repo: RepositoryInfo,
    description: string,
    refactoringType: string
  ): Promise<{ files: string[]; operations: RefactoringOperation[] }> {
    logger.info(`Analyzing refactoring impact for ${repo.name}: ${description} (${refactoringType})`);

    const files: string[] = [];
    const operations: RefactoringOperation[] = [];

    try {
      // Use AI to analyze refactoring impact
      const prompt = `
            Analyze the refactoring impact for repository: ${repo.name}
            Refactoring description: ${description}
            Refactoring type: ${refactoringType}
            Repository language: ${repo.language}
            Dependencies: ${repo.dependencies.slice(0, 10).join(', ')}

            Identify:
            1. Files that need to be modified
            2. Specific operations required
            3. Potential breaking changes

            Provide response in JSON format:
            {
              "files": ["file1.js", "file2.ts"],
              "operations": [
                {
                  "type": "rename|move|extract|inline|update-dependency|update-api",
                  "file": "path/to/file",
                  "oldValue": "old value",
                  "newValue": "new value",
                  "line": 10
                }
              ]
            }
            `;

      const result = await this.supervisorAgent.run({
        prompt,
        mode: 'ask'
      });

      if (result.success && result.output) {
        const parsed = this.parseRefactoringAnalysis(result.output);
        files.push(...parsed.files);
        operations.push(...parsed.operations);
      }
    } catch (error) {
      logger.warn(`Failed to analyze refactoring impact: ${error}`);
    }

    return { files, operations };
  }

  private async analyzeRefactoringDependencies(changes: CrossRepoRefactoring['changes']): Promise<string[]> {
    const dependencies: string[] = [];

    // Analyze dependencies between changes across repositories
    for (let i = 0; i < changes.length; i++) {
      for (let j = i + 1; j < changes.length; j++) {
        const change1 = changes[i];
        const change2 = changes[j];

        // Check if change1 affects files that change2 depends on
        const repo1 = this.repositories.get(change1.repo);
        const repo2 = this.repositories.get(change2.repo);

        if (repo1 && repo2) {
          // Check for shared dependencies
          const sharedDeps = repo1.dependencies.filter(dep => repo2.dependencies.includes(dep));
          if (sharedDeps.length > 0) {
            dependencies.push(`${change1.repo} -> ${change2.repo}: shared dependencies ${sharedDeps.join(', ')}`);
          }
        }
      }
    }

    return dependencies;
  }

  private assessRefactoringRisk(changes: CrossRepoRefactoring['changes']): { estimatedImpact: 'low' | 'medium' | 'high'; riskLevel: 'low' | 'medium' | 'high' } {
    const totalFiles = changes.reduce((sum, change) => sum + change.files.length, 0);
    const totalOperations = changes.reduce((sum, change) => sum + change.operations.length, 0);

    let estimatedImpact: 'low' | 'medium' | 'high' = 'low';
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    if (totalFiles > 20 || totalOperations > 50) {
      estimatedImpact = 'high';
      riskLevel = 'high';
    } else if (totalFiles > 10 || totalOperations > 20) {
      estimatedImpact = 'medium';
      riskLevel = 'medium';
    }

    return { estimatedImpact, riskLevel };
  }

  private async generateRollbackPlan(changes: CrossRepoRefactoring['changes']): Promise<string> {
    const steps: string[] = [];

    // Generate detailed rollback plan based on changes
    for (const change of changes) {
      const repo = this.repositories.get(change.repo);
      if (repo) {
        steps.push(`1. Revert changes in ${repo.name}:`);
        for (const file of change.files) {
          steps.push(`   - git checkout HEAD -- ${file}`);
        }
        steps.push('   - git reset --hard HEAD~1 (if committed)');
      }
    }

    steps.push('2. Verify all repositories are in working state');
    steps.push('3. Run tests to ensure functionality is restored');

    return steps.join('\n');
  }

  private sortChangesByDependencies(changes: CrossRepoRefactoring['changes'], dependencies: string[]): CrossRepoRefactoring['changes'] {
    // Sort changes based on dependencies to ensure proper execution order
    const sorted = [...changes];

    // Simple topological sort based on dependency strings
    for (const dep of dependencies) {
      const [source, target] = dep.split(' -> ');
      const sourceIndex = sorted.findIndex(c => c.repo === source);
      const targetIndex = sorted.findIndex(c => c.repo === target);

      if (sourceIndex > targetIndex && sourceIndex !== -1 && targetIndex !== -1) {
        // Move source before target
        const sourceChange = sorted.splice(sourceIndex, 1)[0];
        sorted.splice(targetIndex, 0, sourceChange);
      }
    }

    return sorted;
  }

  private parseRefactoringAnalysis(response: string): { files: string[]; operations: RefactoringOperation[] } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          files: parsed.files || [],
          operations: parsed.operations || []
        };
      }
    } catch (error) {
      logger.warn(`Failed to parse refactoring analysis: ${error}`);
    }

    return { files: [], operations: [] };
  }

  private setupEventListeners(): void {
    // Listen for workspace changes
    vscode.workspace.onDidChangeWorkspaceFolders(() => {
      this.discoverRepositories();
    });
  }

  /**
     * Dispose cross-repository context system
     */
  public dispose(): void {
    this.repositories.clear();
    this.relationships.clear();
    this.sharedContexts.clear();
    this.monorepoStructures.clear();
    this.analysisCache.clear();
    logger.info('Cross-Repository Context disposed');
  }
}