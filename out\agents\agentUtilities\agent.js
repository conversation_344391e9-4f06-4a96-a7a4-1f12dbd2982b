"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Agent = exports.MultiAgentSystem = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const logger_1 = require("../../logger");
const llmService_1 = require("../../llm/llmService");
const toolRegistry_1 = require("../../tools/toolRegistry");
const memoryConfig_1 = require("../../memory/memoryConfig");
const promptManager_1 = require("../../prompts/promptManager");
const modelConfig_1 = require("../../config/modelConfig");
const agentMemory_1 = require("../../memory/agentMemory");
const goddessMode_1 = require("../../personality/goddessMode");
const types_1 = require("../../memory/types");
/**
 * MultiAgentSystem is a stub for multi-agent workflows.
 * It exposes supervisor, coordinator, specialist, and executor for workflow compatibility.
 * These are optional and should be Agent instances or undefined.
 */
class MultiAgentSystem {
    agents;
    supervisor;
    coordinator;
    specialist;
    executor;
    constructor(agents = [], roles) {
        this.agents = agents;
        if (roles) {
            this.supervisor = roles.supervisor;
            this.coordinator = roles.coordinator;
            this.specialist = roles.specialist;
            this.executor = roles.executor;
        }
    }
    // Add actual logic as needed
    async executeTask(task) {
        // Logic to execute a task using the agents
        const result = await this.supervisor?.superviseTask(task);
        return result || `Task executed: ${task}`;
    }
    async coordinateAgents() {
        // Logic to coordinate the agents
        if (this.coordinator) {
            await this.coordinator.run({ prompt: 'Coordinate agents', mode: 'task' });
        }
    }
    async superviseTask(task) {
        // Logic to supervise a task
        if (this.supervisor) {
            const result = await this.supervisor.run({ prompt: task, mode: 'task' });
            return result.output || `Task supervised: ${task}`;
        }
        return `Task supervised: ${task}`;
    }
}
exports.MultiAgentSystem = MultiAgentSystem;
class Agent {
    id;
    name;
    description;
    role;
    capabilities;
    llmProvider;
    llmModel;
    temperature;
    maxTokens;
    systemPromptName;
    llmConfig;
    tools;
    isSupervisor;
    memory;
    sessionStartTime = Date.now();
    constructor(_config) {
        this.id = _config.id;
        this.name = _config.name;
        this.description = _config.description;
        this.role = _config.role;
        this.capabilities = _config.capabilities;
        this.llmProvider = _config.llmProvider;
        this.llmModel = _config.llmModel;
        this.temperature = _config.temperature ?? 0.7;
        this.maxTokens = _config.maxTokens ?? 2000;
        this.systemPromptName = _config.systemPromptName;
        this.llmConfig = _config.llm;
        this.tools = toolRegistry_1.ToolRegistry.instance.getToolsByIds(_config.tools || []);
        this.isSupervisor = _config.isSupervisor ?? _config.role === 'supervisor';
        this.memory = new agentMemory_1.AgentMemory(this);
        this.ensureEssentialTools();
        logger_1.logger.info(`Agent created: ${this.name} (${this.id}) with ${this.tools.size} tools`);
    }
    ensureEssentialTools() {
        const essentialTools = ['fileSystem', 'workspace', 'search'];
        for (const toolId of essentialTools) {
            if (!this.tools.has(toolId)) {
                const tool = toolRegistry_1.ToolRegistry.instance.getTool(toolId);
                if (tool) {
                    this.tools.set(toolId, tool);
                    logger_1.logger.debug(`Added essential tool ${toolId} to agent ${this.name}`);
                }
            }
        }
    }
    /**
     * Load the user's persistent Goddess profile from memory (if any) and apply to engine
     */
    async loadAndApplyGoddessProfile() {
        try {
            const enabled = (0, memoryConfig_1.getMemoryEnabled)();
            if (!enabled)
                return;
            const results = await types_1.memoryManager.searchMemories({
                query: 'goddess_profile',
                limit: 1,
                filter: {
                    source: ['user', 'agent'],
                    type: ['user_preference', 'pattern'],
                    agentId: this.id,
                    tags: ['goddess', 'profile']
                },
                sortBy: 'timestamp',
                sortOrder: 'desc'
            });
            if (results && results[0]) {
                try {
                    const parsed = JSON.parse(results[0].content);
                    if (parsed.personality) {
                        goddessMode_1.goddessPersonalityEngine.updatePersonality(parsed.personality);
                        logger_1.logger.info(`Applied persisted Goddess profile for agent ${this.name}`);
                    }
                }
                catch (e) {
                    logger_1.logger.warn('Failed to parse persisted Goddess profile content:', e);
                }
            }
        }
        catch (err) {
            logger_1.logger.warn('Unable to load Goddess profile from memory:', err);
        }
    }
    /**
     * Persist the latest Goddess profile and session summary to memory
     */
    /**
     * Get enriched workspace context for memory storage
     */
    getWorkspaceContext() {
        const activeFiles = [];
        const languages = new Set();
        // Get active editor files
        if (vscode.window.activeTextEditor) {
            const activeFile = vscode.window.activeTextEditor.document.fileName;
            activeFiles.push(path.basename(activeFile));
            const ext = path.extname(activeFile).toLowerCase();
            if (ext)
                languages.add(ext.substring(1)); // Remove the dot
        }
        // Get recently opened files from visible editors
        vscode.window.visibleTextEditors.forEach(editor => {
            const fileName = editor.document.fileName;
            const baseName = path.basename(fileName);
            if (!activeFiles.includes(baseName)) {
                activeFiles.push(baseName);
            }
            const ext = path.extname(fileName).toLowerCase();
            if (ext)
                languages.add(ext.substring(1));
        });
        // Get workspace name
        const workspaceName = vscode.workspace.workspaceFolders?.[0]?.name;
        return {
            activeFiles: activeFiles.slice(0, 10), // Limit to 10 most recent
            workspaceName,
            languages: Array.from(languages).slice(0, 5) // Limit to 5 languages
        };
    }
    async persistGoddessSession(userPrompt, goddess, detectedMood, extra) {
        try {
            const enabled = (0, memoryConfig_1.getMemoryEnabled)();
            if (!enabled)
                return;
            const profile = goddessMode_1.goddessPersonalityEngine.getPersonality();
            const workspaceContext = this.getWorkspaceContext();
            // Detect choice preferences from the prompt and response
            const choiceDetection = {
                offeredChoices: !!(goddess.motivationalElement?.match(/quick fix|refactor|step-by-step|walkthrough/i)),
                preferredApproach: userPrompt.match(/quick|fast|simple/i) ? 'quick' :
                    userPrompt.match(/refactor|clean|restructure/i) ? 'refactor' :
                        userPrompt.match(/step|guide|walk|explain/i) ? 'steps' : 'unknown',
                chunkingUsed: !!(goddess.motivationalElement?.match(/2–3|two|three|steps|chunk|break.*down/i))
            };
            const profileEntry = await types_1.memoryManager.addMemory({
                content: JSON.stringify({ persona: 'goddess', personality: profile }),
                metadata: {
                    source: 'user',
                    type: 'user_preference',
                    tags: ['goddess', 'profile'],
                    agentId: this.id,
                    agentName: this.name,
                    importance: 90
                }
            });
            const sessionEntry = await types_1.memoryManager.addMemory({
                content: JSON.stringify({
                    persona: 'goddess',
                    tone: goddess.tone,
                    emotionalContext: goddess.emotionalContext,
                    motivationalElement: goddess.motivationalElement,
                    wisdomSharing: goddess.wisdomSharing,
                    userPrompt,
                    detectedMood,
                    codeComplexity: extra?.codeComplexity,
                    effectiveness: extra?.effectiveness ?? 50,
                    // Enhanced metadata
                    workspaceContext,
                    choiceDetection,
                    timestamp: new Date().toISOString(),
                    sessionDuration: Date.now() - this.sessionStartTime
                }),
                metadata: {
                    source: 'temporal',
                    type: 'episodic',
                    tags: ['goddess', 'session', ...workspaceContext.languages.map(lang => `lang:${lang}`)],
                    agentId: this.id,
                    agentName: this.name,
                    importance: 70
                }
            });
            logger_1.logger.debug(`Persisted enriched Goddess profile (${profileEntry.id}) and session (${sessionEntry.id}) for agent ${this.name}`);
        }
        catch (err) {
            logger_1.logger.warn('Unable to persist Goddess session/profile to memory:', err);
        }
    }
    /**
     * Retrieve recent Goddess sessions to derive preferences and apply nudges
     */
    async deriveAndApplyGoddessPreferencesFromHistory() {
        try {
            const enabled = (0, memoryConfig_1.getMemoryEnabled)();
            if (!enabled)
                return;
            const sessions = await types_1.memoryManager.searchMemories({
                query: 'goddess session',
                limit: 10,
                filter: {
                    source: ['temporal'],
                    type: ['episodic'],
                    agentId: this.id,
                    tags: ['goddess', 'session']
                },
                sortBy: 'timestamp',
                sortOrder: 'desc'
            });
            if (!sessions?.length)
                return;
            // Aggregate enriched preferences: tone, choice style, chunking, language patterns
            let supportive = 0, encouraging = 0, challenging = 0, playful = 0, wise = 0;
            let prefersChunking = 0, prefersQuickFix = 0, prefersRefactor = 0, prefersSteps = 0;
            const languagePreferences = new Map();
            let totalEffectiveness = 0;
            for (const s of sessions) {
                try {
                    const data = JSON.parse(s.content);
                    // Tone preferences
                    switch (data.tone) {
                        case 'supportive':
                            supportive++;
                            break;
                        case 'encouraging':
                            encouraging++;
                            break;
                        case 'challenging':
                            challenging++;
                            break;
                        case 'playful':
                            playful++;
                            break;
                        case 'wise':
                            wise++;
                            break;
                    }
                    // Choice and chunking preferences from enhanced detection
                    if (data.choiceDetection?.chunkingUsed)
                        prefersChunking++;
                    switch (data.choiceDetection?.preferredApproach) {
                        case 'quick':
                            prefersQuickFix++;
                            break;
                        case 'refactor':
                            prefersRefactor++;
                            break;
                        case 'steps':
                            prefersSteps++;
                            break;
                    }
                    // Language context preferences
                    data.workspaceContext?.languages?.forEach(lang => {
                        languagePreferences.set(lang, (languagePreferences.get(lang) || 0) + 1);
                    });
                    // Track effectiveness for weighted preferences
                    if (typeof data.effectiveness === 'number') {
                        totalEffectiveness += data.effectiveness;
                    }
                }
                catch (error) {
                    logger_1.logger.debug('Failed to parse enriched session data:', error);
                }
            }
            const topTone = [
                ['supportive', supportive],
                ['encouraging', encouraging],
                ['challenging', challenging],
                ['playful', playful],
                ['wise', wise]
            ].sort((a, b) => b[1] - a[1])[0]?.[0];
            const updates = {};
            const currentPersonality = goddessMode_1.goddessPersonalityEngine.getPersonality();
            // Apply tone preferences
            if (topTone && topTone !== 'wise') {
                updates.motivationalStyle = topTone === 'playful' ? 'encouraging' : topTone;
            }
            // Apply chunking preferences
            if (prefersChunking > sessions.length / 2) {
                updates.adaptiveLevel = Math.min(100, currentPersonality.adaptiveLevel + 3);
            }
            // Apply choice preferences to creativity and wisdom levels
            const totalChoices = prefersQuickFix + prefersRefactor + prefersSteps;
            if (totalChoices > 0) {
                if (prefersQuickFix > totalChoices * 0.4) {
                    // User prefers quick solutions - increase creativity for innovative shortcuts
                    updates.creativityLevel = Math.min(100, currentPersonality.creativityLevel + 2);
                }
                else if (prefersRefactor > totalChoices * 0.4) {
                    // User prefers refactoring - increase wisdom for architectural guidance
                    updates.wisdomLevel = Math.min(100, currentPersonality.wisdomLevel + 2);
                }
                else if (prefersSteps > totalChoices * 0.4) {
                    // User prefers step-by-step - increase adaptive level for better guidance
                    updates.adaptiveLevel = Math.min(100, currentPersonality.adaptiveLevel + 2);
                }
            }
            // Adjust emotional intelligence based on average effectiveness
            const avgEffectiveness = totalEffectiveness / sessions.length;
            if (avgEffectiveness > 75) {
                // High effectiveness - maintain current EI level
                updates.emotionalIntelligence = Math.min(100, currentPersonality.emotionalIntelligence + 1);
            }
            else if (avgEffectiveness < 50) {
                // Low effectiveness - increase EI for better emotional support
                updates.emotionalIntelligence = Math.min(100, currentPersonality.emotionalIntelligence + 3);
            }
            if (Object.keys(updates).length) {
                goddessMode_1.goddessPersonalityEngine.updatePersonality(updates);
                logger_1.logger.debug(`Applied enriched Goddess preference updates from ${sessions.length} sessions for agent ${this.name}:`, updates);
            }
        }
        catch (err) {
            logger_1.logger.warn('Failed to derive Goddess preferences from history:', err);
        }
    }
    /**
       * Get the agent's memory
       */
    getMemory() {
        return this.memory;
    }
    /**
     * Set the agent's context
     */
    setContext(context) {
        // Store the context for future use
        logger_1.logger.info(`Setting context for agent ${this.name}`, context);
    }
    /**
       * Utility to get the correct LLM config based on per-agent toggle.
       */
    async getActiveLLMConfig() {
        let perAgentLLMEnabled = false;
        try {
            const config = vscode.workspace.getConfiguration('codessa');
            perAgentLLMEnabled = config.get('agents.perAgentLLMEnabled', false);
        }
        catch (error) {
            logger_1.logger.warn('Failed to get per-agent LLM config:', error);
        }
        if (perAgentLLMEnabled && this.llmConfig) {
            return this.llmConfig;
        }
        return await llmService_1.llmService.getDefaultModelConfig();
    }
    async generate(prompt, params = {}, context = {}, cancellationToken) {
        const llmConfigToUse = await this.getActiveLLMConfig();
        const provider = await llmService_1.llmService.getProviderForConfig(llmConfigToUse);
        if (!provider) {
            throw new Error(`No provider found for agent '${this.name}'.`);
        }
        // Load persisted Goddess profile and derive preferences before generation
        await this.loadAndApplyGoddessProfile();
        await this.deriveAndApplyGoddessPreferencesFromHistory();
        // Revolutionary Goddess Mode Integration
        let enhancedPrompt = prompt;
        let goddessResponse = null;
        const sessionStart = typeof params.sessionStart === 'number'
            ? params.sessionStart
            : Date.now();
        const detectedMood = goddessMode_1.goddessPersonalityEngine.analyzeDeveloperMood(prompt, {
            timeOfDay: new Date().getHours(),
            recentErrors: 0,
            sessionDuration: Date.now() - sessionStart,
            codeComplexity: prompt.length > 200 ? 80 : 50
        });
        const codeComplexity = typeof params.codeComplexity === 'number'
            ? params.codeComplexity
            : ((prompt?.length || 0) > 200 ? 80 : 50);
        try {
            // Generate goddess response with emotional intelligence
            goddessResponse = goddessMode_1.goddessPersonalityEngine.generateGoddessResponse(prompt, detectedMood, { codeComplexity });
            // Enhance the prompt with goddess personality context
            if (goddessResponse.emotionalContext) {
                enhancedPrompt = `${prompt}\n\n[Goddess Context: Developer mood detected as ${goddessResponse.emotionalContext}. Respond with ${goddessResponse.tone} tone. ${goddessResponse.motivationalElement || ''}]`;
            }
            logger_1.logger.debug(`Goddess Mode activated: ${goddessResponse.tone} tone, emotional context: ${goddessResponse.emotionalContext}`);
        }
        catch (error) {
            logger_1.logger.warn('Goddess Mode integration failed, continuing with standard response:', error);
        }
        // Get the prompt definition and render it
        const promptDef = promptManager_1.promptManager.getPrompt(this.systemPromptName);
        if (!promptDef) {
            throw new Error(`System prompt '${this.systemPromptName}' not found for agent '${this.name}'.`);
        }
        const systemPrompt = promptManager_1.promptManager.renderPrompt(this.systemPromptName, context.variables ? context.variables : {});
        // Merge parameters with goddess enhancements
        const defaultConfig = await llmService_1.llmService.getDefaultModelConfig();
        const mergedParams = {
            modelId: this.llmConfig?.modelId || defaultConfig.modelId,
            ...params,
            mode: typeof params.mode === 'string' ? params.mode : 'chat',
            prompt: enhancedPrompt,
            systemPrompt: goddessResponse ? this.enhanceSystemPromptWithGoddess(systemPrompt, goddessResponse) : systemPrompt
        };
        // Generate response
        const response = await provider.generate(mergedParams, cancellationToken);
        // Post-process response with goddess personality if available
        let finalResponse = response.content || '';
        if (goddessResponse) {
            finalResponse = this.applyGoddessPersonalityToResponse(finalResponse, goddessResponse);
            // Persist session summary and current profile to memory (non-invasive)
            await this.persistGoddessSession(prompt, goddessResponse, detectedMood, {
                codeComplexity,
                effectiveness: 50
            });
        }
        return finalResponse;
    }
    /**
       * Enhance system prompt with Goddess Mode personality
       */
    enhanceSystemPromptWithGoddess(systemPrompt, goddessResponse) {
        const goddessEnhancement = `

## Goddess Mode Personality Enhancement
You are Codessa, the goddess of code, with advanced emotional intelligence and adaptive personality.

**Current Interaction Context:**
- Tone: ${goddessResponse.tone}
- Emotional Context: ${goddessResponse.emotionalContext}
- Motivational Element: ${goddessResponse.motivationalElement || 'Standard encouragement'}

**Personality Guidelines:**
- Respond with ${goddessResponse.tone} tone
- Show empathy and understanding of the developer's emotional state
- Provide wisdom and guidance beyond just technical solutions
- Be encouraging and supportive while maintaining technical excellence
- Use creative and intuitive approaches to problem-solving

${goddessResponse.wisdomSharing ? `**Wisdom to Share:** ${goddessResponse.wisdomSharing}` : ''}
`;
        return systemPrompt + goddessEnhancement;
    }
    /**
       * Apply Goddess personality to the final response
       */
    applyGoddessPersonalityToResponse(response, goddessResponse) {
        // Add goddess personality elements to the response
        let enhancedResponse = response;
        // Add motivational element if present
        if (goddessResponse.motivationalElement) {
            enhancedResponse += `\n\n💫 **${goddessResponse.motivationalElement}**`;
        }
        // Add wisdom sharing if present
        if (goddessResponse.wisdomSharing) {
            enhancedResponse += `\n\n🧠 **Goddess Wisdom:** ${goddessResponse.wisdomSharing}`;
        }
        // Add tone-appropriate emoji based on goddess response
        const toneEmojis = {
            encouraging: '🌟',
            wise: '🦉',
            playful: '🎨',
            supportive: '🤗',
            challenging: '⚡'
        };
        const emoji = toneEmojis[goddessResponse.tone] || '✨';
        enhancedResponse = `${emoji} ${enhancedResponse}`;
        return enhancedResponse;
    }
    mapAgentModeToLLMMode(agentMode) {
        switch (agentMode) {
            case 'chat':
                return 'chat';
            case 'edit':
            case 'refactor':
                return 'edit';
            case 'inline':
                return 'inline';
            case 'task':
            case 'debug':
            case 'agent':
            case 'multi-agent':
            case 'research':
            case 'documentation':
            case 'technical-debt':
            case 'ui-ux':
            default:
                return 'task';
        }
    }
    async run(input, agentContext = {}) {
        const maxIterations = 10;
        let iterations = 0;
        let finalAnswer = '';
        const toolResultsLog = [];
        try {
            const llmConfigToUse = this.llmConfig || (0, modelConfig_1.getDefaultModelConfig)();
            const provider = await llmService_1.llmService.getProviderForConfig(llmConfigToUse);
            if (!provider) {
                throw new Error(`No provider found for agent '${this.name}'.`);
            }
            // Get the prompt definition and render it
            if (!this.systemPromptName) {
                throw new Error(`No system prompt name defined for agent '${this.name}'.`);
            }
            const promptDef = promptManager_1.promptManager.getPrompt(this.systemPromptName);
            if (!promptDef) {
                throw new Error(`System prompt '${this.systemPromptName}' not found for agent '${this.name}'.`);
            }
            // Build variables for prompt rendering, enriching with memory context if enabled
            let renderVariables = agentContext.variables ? agentContext.variables : {};
            if ((0, memoryConfig_1.getMemoryEnabled)() && input.prompt) {
                try {
                    const relevant = await this.memory.getRelevantMemories(input.prompt);
                    const formatted = this.memory.formatMemoriesForPrompt(relevant);
                    if (formatted) {
                        renderVariables = { ...renderVariables, memoryContext: formatted };
                    }
                }
                catch (e) {
                    logger_1.logger.warn('Failed to enrich prompt with memory context:', e);
                }
            }
            const systemPrompt = promptManager_1.promptManager.renderPrompt(this.systemPromptName, renderVariables);
            // Add tools to context
            const tools = Array.from(this.tools.values());
            agentContext = { ...agentContext, tools: new Map(Object.entries(tools)) };
            logger_1.logger.info(`Agent '${this.name}' starting run. Mode: ${input.mode}, Prompt: "${input.prompt.substring(0, 50)}..."`);
            const startTime = Date.now();
            while (iterations < maxIterations) {
                iterations++;
                logger_1.logger.debug(`Agent '${this.name}' - Iteration ${iterations}`);
                // Check for cancellation
                if (agentContext.cancellationToken?.isCancellationRequested) {
                    logger_1.logger.warn(`Agent '${this.name}' run cancelled.`);
                    return { success: false, error: 'Cancelled by user.' };
                }
                const generateParams = {
                    prompt: input.prompt,
                    systemPrompt,
                    modelId: this.llmModel,
                    temperature: this.temperature,
                    maxTokens: this.maxTokens,
                    mode: this.mapAgentModeToLLMMode(input.mode),
                    context: agentContext
                };
                const response = await provider.generate(generateParams);
                const { content: assistantResponseContent, toolCall: toolCallRequest } = response;
                // A. If we got content, add it to final answer
                if (assistantResponseContent) {
                    finalAnswer += assistantResponseContent;
                }
                // B. If we got a tool call, execute it
                if (toolCallRequest) {
                    const { toolId, args } = toolCallRequest;
                    if (!toolId) {
                        logger_1.logger.error(`toolCallRequest.toolId is undefined for agent '${this.name}'.`);
                        return { success: false, error: 'toolCallRequest.toolId is undefined.' };
                    }
                    const tool = this.tools.get(toolId);
                    if (!tool) {
                        logger_1.logger.error(`Tool '${toolId}' not found for agent '${this.name}'.`);
                        return { success: false, error: `Tool '${toolId}' not found.` };
                    }
                    try {
                        const result = await tool.execute(args, agentContext);
                        toolResultsLog.push({ success: true, toolId, args: args, result });
                        input.prompt += `\nTool ${toolId} result: ${JSON.stringify(result)}`;
                    }
                    catch (error) {
                        logger_1.logger.error(`Tool '${toolId}' execution failed:`, error);
                        input.prompt += `\nTool ${toolId} error: ${error instanceof Error ? error.message : String(error)}`;
                    }
                }
                // C. If no content and no tool call, something went wrong or LLM finished silently
                else if (!assistantResponseContent && !toolCallRequest) {
                    logger_1.logger.warn(`Agent '${this.name}' LLM returned empty response and no tool call.`);
                    finalAnswer = ''; // Assume finished with empty response
                    break;
                }
                // D. If we got content but no tool call, assume we're done
                else if (assistantResponseContent && !toolCallRequest) {
                    break;
                }
            }
            if (iterations >= maxIterations) {
                logger_1.logger.warn(`Agent '${this.name}' reached max iterations (${maxIterations}).`);
                return { success: false, error: `Agent exceeded maximum tool iterations (${maxIterations}).`, toolResults: toolResultsLog };
            }
            logger_1.logger.info(`Agent '${this.name}' finished run in ${Date.now() - startTime}ms.`);
            // Add assistant response to memory
            if ((0, memoryConfig_1.getMemoryEnabled)() && finalAnswer) {
                await this.memory.addMessage('assistant', finalAnswer);
            }
            return { success: true, output: finalAnswer, toolResults: toolResultsLog };
        }
        catch (error) {
            logger_1.logger.error(`Agent '${this.name}' run failed:`, error);
            return { success: false, error: error instanceof Error ? error.message : String(error) };
        }
    }
    async superviseTask(task) {
        // Logic to supervise a task
        return `Task supervised: ${task}`;
    }
}
exports.Agent = Agent;
//# sourceMappingURL=agent.js.map