import * as vscode from 'vscode';
import { operationModeRegistry, ContextType } from '../agents/agentModes/operationMode';
import { AskMode } from '../agents/agentModes/askMode';
import { ChatMode } from '../agents/agentModes/chatMode';
import { DebugMode } from '../agents/agentModes/debugMode';
import { EditMode } from '../agents/agentModes/editMode';
import { AgentMode } from '../agents/agentModes/agentMode';
import { MultiAgentMode } from '../agents/agentModes/multiAgentMode';
import { ResearchMode } from '../agents/agentModes/researchMode';
import { DocumentationMode } from '../agents/agentModes/documentationMode';
import { RefactorMode } from '../agents/agentModes/refactorMode';
import { logger } from '../logger';

/**
 * Register all operation modes
 */
export async function registerModes(context: vscode.ExtensionContext): Promise<void> {
  try {
    logger.info('Registering operation modes...');

    // Register modes
    operationModeRegistry.registerMode(new AskMode(
      'ask',
      'Ask',
      'Ask questions about your codebase',
      '$(question)',
      ContextType.ENTIRE_CODEBASE
    ));
    operationModeRegistry.registerMode(new ChatMode(
      'chat',
      'Chat',
      'General chat with the AI assistant',
      '$(comment)',
      ContextType.NONE
    ));
    operationModeRegistry.registerMode(new DebugMode(
      'debug',
      'Debug',
      'Debug issues with your code',
      '$(bug)',
      ContextType.SELECTED_FILES
    ));
    operationModeRegistry.registerMode(new EditMode(
      'edit',
      'Edit',
      'AI-assisted code editing with human verification',
      '$(edit)',
      ContextType.SELECTED_FILES
    ));
    operationModeRegistry.registerMode(new AgentMode(
      'agent',
      'Agent',
      'Autonomous AI agent that completes tasks with minimal user interaction',
      '$(robot)',
      ContextType.ENTIRE_CODEBASE
    ));
    operationModeRegistry.registerMode(new MultiAgentMode(
      'multi-agent',
      'Multi-Agent',
      'Team of AI agents working together on complex tasks',
      '$(organization)',
      ContextType.ENTIRE_CODEBASE
    ));
    operationModeRegistry.registerMode(new ResearchMode(
      'research',
      'Research',
      'Comprehensive research and analysis of topics',
      '$(search)',
      ContextType.ENTIRE_CODEBASE
    ));
    operationModeRegistry.registerMode(new DocumentationMode(
      'documentation',
      'Documentation',
      'Generate comprehensive documentation for code',
      '$(book)',
      ContextType.SELECTED_FILES
    ));
    operationModeRegistry.registerMode(new RefactorMode(
      'refactor',
      'Refactor',
      'Refactor and optimize code structure',
      '$(tools)',
      ContextType.SELECTED_FILES
    ));

    // Set default mode
    operationModeRegistry.setDefaultMode('chat');

    // Initialize modes
    await operationModeRegistry.initializeModes(context);

    logger.info('Operation modes registered successfully');
  } catch (error) {
    logger.error('Error registering operation modes:', error);
    throw error;
  }
}
