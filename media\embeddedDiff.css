/* Advanced Embedded Diff Styles - Professional change tracking */

/* Header Enhancements */
.header-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-subtitle {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

.header-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-end;
}

.filter-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.control-select {
  padding: 4px 8px;
  border: 1px solid var(--vscode-dropdown-border);
  border-radius: 4px;
  background: var(--vscode-dropdown-background);
  color: var(--vscode-dropdown-foreground);
  font-size: 12px;
  min-width: 120px;
}

.view-mode-toggle {
  display: flex;
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  overflow: hidden;
}

.view-btn {
  padding: 6px 8px;
  border: none;
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.view-btn:hover {
  background: var(--vscode-button-hoverBackground);
}

/* Summary Stats */
.summary-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  border-radius: 6px;
  font-size: 12px;
}

.stat-value {
  font-weight: 600;
  font-size: 14px;
}

.stat-label {
  font-size: 11px;
  opacity: 0.8;
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--vscode-inputValidation-infoBackground);
  border-radius: 4px;
  margin-bottom: 16px;
}

.bulk-label {
  font-size: 12px;
  font-weight: 500;
}

.bulk-btn {
  padding: 4px 8px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 3px;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.bulk-btn:hover {
  background: var(--vscode-button-hoverBackground);
}

/* Advanced Change Item */
.change-item {
  border: 1px solid var(--vscode-panel-border);
  border-radius: 8px;
  margin-bottom: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: var(--vscode-editor-background);
}

.change-item:hover {
  border-color: var(--vscode-focusBorder);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.change-item.priority-critical {
  border-left: 4px solid var(--vscode-charts-red);
}

.change-item.priority-high {
  border-left: 4px solid var(--vscode-charts-orange);
}

.change-item.priority-medium {
  border-left: 4px solid var(--vscode-charts-yellow);
}

.change-item.priority-low {
  border-left: 4px solid var(--vscode-charts-green);
}

.change-item.has-conflicts {
  background: var(--vscode-inputValidation-errorBackground);
  border-color: var(--vscode-inputValidation-errorBorder);
}

.change-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.change-selector {
  display: flex;
  align-items: center;
}

.file-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.change-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.change-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.change-details {
  flex: 1;
}

.file-name-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--vscode-foreground);
}

.change-badges {
  display: flex;
  gap: 4px;
}

.badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.badge.critical {
  background: var(--vscode-charts-red);
  color: white;
}

.badge.conflict {
  background: var(--vscode-charts-orange);
  color: white;
}

.badge.tag {
  background: var(--vscode-charts-blue);
  color: white;
}

.change-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.diff-stats {
  display: flex;
  gap: 4px;
}

.additions {
  color: var(--vscode-charts-green);
  font-weight: 500;
}

.deletions {
  color: var(--vscode-charts-red);
  font-weight: 500;
}

.ai-suggestion {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 6px;
  padding: 6px 8px;
  background: var(--vscode-inputValidation-infoBackground);
  border-radius: 4px;
  font-size: 11px;
}

.suggestion-text {
  flex: 1;
  color: var(--vscode-inputValidation-infoForeground);
}

.apply-suggestion-btn {
  padding: 2px 6px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 3px;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-size: 10px;
  cursor: pointer;
}

.change-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: var(--vscode-foreground);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--vscode-toolbar-hoverBackground);
}

.diff-toggle-btn.expanded {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

/* Advanced Embedded Diff */
.embedded-diff {
  border-top: 1px solid var(--vscode-panel-border);
  background: var(--vscode-editor-background);
}

.diff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--vscode-editorGroupHeader-tabsBackground);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.diff-controls {
  display: flex;
  gap: 6px;
}

.diff-control-btn {
  padding: 4px 8px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 3px;
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  font-size: 11px;
  cursor: pointer;
}

.diff-control-btn:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

.diff-stats {
  display: flex;
  gap: 8px;
  font-size: 11px;
}

.stat-additions {
  color: var(--vscode-charts-green);
  font-weight: 500;
}

.stat-deletions {
  color: var(--vscode-charts-red);
  font-weight: 500;
}

.stat-hunks {
  color: var(--vscode-descriptionForeground);
}

.diff-content {
  max-height: 400px;
  overflow-y: auto;
}

.diff-hunk {
  border-bottom: 1px solid var(--vscode-panel-border);
}

.hunk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 16px;
  background: var(--vscode-diffEditor-unchangedRegionBackground);
  font-family: var(--vscode-editor-font-family);
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.hunk-actions {
  display: flex;
  gap: 4px;
}

.hunk-action-btn {
  padding: 2px 6px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 3px;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-size: 10px;
  cursor: pointer;
}

.hunk-action-btn:hover {
  background: var(--vscode-button-hoverBackground);
}

.diff-line {
  display: flex;
  font-family: var(--vscode-editor-font-family);
  font-size: 13px;
  line-height: 1.4;
  min-height: 18px;
}

.diff-line.addition {
  background: var(--vscode-diffEditor-insertedTextBackground);
}

.diff-line.deletion {
  background: var(--vscode-diffEditor-removedTextBackground);
}

.diff-line.context {
  background: var(--vscode-editor-background);
}

.diff-line[data-highlighted="true"] {
  background: var(--vscode-editor-selectionBackground) !important;
}

.line-numbers {
  display: flex;
  min-width: 80px;
  background: var(--vscode-editorLineNumber-background);
  border-right: 1px solid var(--vscode-panel-border);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.old-line-number,
.new-line-number {
  width: 40px;
  padding: 0 8px;
  text-align: right;
  color: var(--vscode-editorLineNumber-foreground);
  font-size: 11px;
}

.line-content {
  flex: 1;
  display: flex;
  padding: 0 8px;
}

.line-prefix {
  width: 16px;
  font-weight: bold;
}

.line-text {
  flex: 1;
  white-space: pre;
  overflow-x: auto;
}

.ai-annotation {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: var(--vscode-inputValidation-infoBackground);
  color: var(--vscode-inputValidation-infoForeground);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* View Modes */
.changes-list.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
}

.changes-list.timeline .change-item {
  position: relative;
  margin-left: 20px;
}

.changes-list.timeline .change-item::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 20px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--vscode-charts-blue);
}

.changes-list.timeline .change-item::after {
  content: '';
  position: absolute;
  left: -16px;
  top: 28px;
  bottom: -8px;
  width: 1px;
  background: var(--vscode-panel-border);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-controls {
    align-items: stretch;
  }
  
  .filter-controls {
    flex-wrap: wrap;
  }
  
  .summary-stats {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .change-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .change-actions {
    justify-content: center;
  }
  
  .changes-list.grid {
    grid-template-columns: 1fr;
  }
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 400px;
  }
}

.embedded-diff {
  animation: slideDown 0.3s ease-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.diff-loading .codicon-loading {
  animation: pulse 1.5s ease-in-out infinite;
}
