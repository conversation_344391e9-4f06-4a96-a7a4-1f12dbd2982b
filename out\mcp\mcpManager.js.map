{"version": 3, "file": "mcpManager.js", "sourceRoot": "", "sources": ["../../src/mcp/mcpManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;AAEH,sCAAmC;AAiDnC,MAAa,UAAU;IACb,cAAc,CAAa;IAC3B,SAAS,CAAS;IAE1B;QACE,IAAI,CAAC,SAAS,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAC7E,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClD,CAAC;IAED;;SAEK;IACG,kBAAkB;QACxB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE;gBACR,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B;YACD,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,aAAa,CAAC,OAAuC;QACnD,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;YAC5B,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO;YAC9B,GAAG,OAAO;SACX,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAElE,eAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACrC,CAAC;IAED;;SAEK;IACL,OAAO,CAAC,QAAgB,EAAE,OAAe,EAAE,IAAa;QACtD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YACpC,QAAQ;YACR,OAAO;YACP,IAAI;SACL,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAElE,eAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,IAAI,SAAS,EAAE,CAAC,CAAC;IACjE,CAAC;IAED;;SAEK;IACL,OAAO,CAAC,IAAY,EAAE,OAAe,EAAE,QAAiB;QACtD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YACrC,IAAI;YACJ,OAAO;YACP,QAAQ;SACT,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAElE,eAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;SAEK;IACL,OAAO,CAAC,GAAW,EAAE,KAAc,EAAE,WAAoB;QACvD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YACrC,GAAG;YACH,KAAK;YACL,WAAW;SACZ,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAElE,eAAM,CAAC,IAAI,CAAC,8BAA8B,GAAG,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;SAEK;IACL,aAAa,CACX,IAAY,EACZ,WAAmB,EACnB,UAAoC,EACpC,OAAqF;QAErF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC;YAC7B,IAAI;YACJ,WAAW;YACX,UAAU;YACV,OAAO;SACR,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAElE,eAAM,CAAC,IAAI,CAAC,qCAAqC,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED;;SAEK;IACL,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;SAEK;IACL,aAAa;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;SAEK;IACL,aAAa,CAAC,IAAY;QACxB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAe,CAAC;YAE/C,uBAAuB;YACvB,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACtE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAC9B,eAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,eAAM,CAAC,KAAK,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACL,YAAY;QACV,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAChD,eAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACrC,CAAC;CACF;AAxKD,gCAwKC;AAED,8BAA8B;AACjB,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC", "sourcesContent": ["/**\n * Model Context Protocol (MCP) Manager\n * \n * This module provides integration with the Model Context Protocol (MCP),\n * which is a standard for exchanging context between different AI models\n * and tools. It allows:\n * - Sharing context between different models\n * - Standardizing context format\n * - Integrating with external services\n */\n\nimport { logger } from '../logger';\n\n/**\n * MCP Context structure\n */\nexport interface MCPContext {\n    version: string;\n    metadata: {\n        source: string;\n        timestamp: string;\n        sessionId?: string;\n        logLevel?: string;\n    };\n    content: {\n        text?: string;\n        code?: {\n            language: string;\n            content: string;\n            path?: string;\n        }[];\n        files?: {\n            path: string;\n            content: string;\n            language?: string;\n        }[];\n        links?: {\n            url: string;\n            title?: string;\n            description?: string;\n        }[];\n        images?: {\n            url: string;\n            alt?: string;\n            caption?: string;\n        }[];\n    };\n    tools?: Array<{\n        name: string;\n        description: string;\n        parameters?: Record<string, unknown>;\n        results?: Record<string, unknown> | Array<unknown> | string | number | boolean | null;\n    }>;\n}\n\n/**\n * MCP Manager for handling Model Context Protocol integration\n */\nimport { IMCPManager } from '../managers';\n\nexport class MCPManager implements IMCPManager {\n  private currentContext: MCPContext;\n  private sessionId: string;\n    \n  constructor() {\n    this.sessionId = `session-${Date.now()}-${Math.floor(Math.random() * 1000)}`;\n    this.currentContext = this.createEmptyContext();\n  }\n    \n  /**\n     * Creates an empty MCP context\n     */\n  private createEmptyContext(): MCPContext {\n    return {\n      version: '1.0',\n      metadata: {\n        source: 'codessa',\n        timestamp: new Date().toISOString(),\n        sessionId: this.sessionId\n      },\n      content: {}\n    };\n  }\n    \n  /**\n     * Updates the current context with new content\n     */\n  updateContext(content: Partial<MCPContext['content']>): void {\n    this.currentContext.content = {\n      ...this.currentContext.content,\n      ...content\n    };\n    this.currentContext.metadata.timestamp = new Date().toISOString();\n        \n    logger.info('Updated MCP context');\n  }\n    \n  /**\n     * Adds code to the context\n     */\n  addCode(language: string, content: string, path?: string): void {\n    if (!this.currentContext.content.code) {\n      this.currentContext.content.code = [];\n    }\n        \n    this.currentContext.content.code.push({\n      language,\n      content,\n      path\n    });\n        \n    this.currentContext.metadata.timestamp = new Date().toISOString();\n        \n    logger.info(`Added code to MCP context: ${path || 'unnamed'}`);\n  }\n    \n  /**\n     * Adds a file to the context\n     */\n  addFile(path: string, content: string, language?: string): void {\n    if (!this.currentContext.content.files) {\n      this.currentContext.content.files = [];\n    }\n        \n    this.currentContext.content.files.push({\n      path,\n      content,\n      language\n    });\n        \n    this.currentContext.metadata.timestamp = new Date().toISOString();\n        \n    logger.info(`Added file to MCP context: ${path}`);\n  }\n    \n  /**\n     * Adds a link to the context\n     */\n  addLink(url: string, title?: string, description?: string): void {\n    if (!this.currentContext.content.links) {\n      this.currentContext.content.links = [];\n    }\n        \n    this.currentContext.content.links.push({\n      url,\n      title,\n      description\n    });\n        \n    this.currentContext.metadata.timestamp = new Date().toISOString();\n        \n    logger.info(`Added link to MCP context: ${url}`);\n  }\n    \n  /**\n     * Adds a tool result to the context\n     */\n  addToolResult(\n    name: string,\n    description: string,\n    parameters?: Record<string, unknown>,\n    results?: Record<string, unknown> | Array<unknown> | string | number | boolean | null\n  ): void {\n    if (!this.currentContext.tools) {\n      this.currentContext.tools = [];\n    }\n        \n    this.currentContext.tools.push({\n      name,\n      description,\n      parameters,\n      results\n    });\n        \n    this.currentContext.metadata.timestamp = new Date().toISOString();\n        \n    logger.info(`Added tool result to MCP context: ${name}`);\n  }\n    \n  /**\n     * Gets the current context\n     */\n  getContext(): MCPContext {\n    return { ...this.currentContext };\n  }\n  \n  /**\n   * Gets the current context (alias for getContext for IMCPManager interface)\n   */\n  getCurrentContext(): MCPContext {\n    return this.getContext();\n  }\n    \n  /**\n     * Exports the context to a JSON string\n     */\n  exportContext(): string {\n    return JSON.stringify(this.currentContext, null, 2);\n  }\n    \n  /**\n     * Imports a context from a JSON string\n     */\n  importContext(json: string): void {\n    try {\n      const context = JSON.parse(json) as MCPContext;\n            \n      // Validate the context\n      if (!context.version || !context.metadata || !context.metadata.source) {\n        throw new Error('Invalid MCP context format');\n      }\n            \n      this.currentContext = context;\n      logger.info('Imported MCP context');\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      logger.error(`Error importing MCP context: ${errorMessage}`);\n      throw error;\n    }\n  }\n    \n  /**\n     * Clears the current context\n     */\n  clearContext(): void {\n    this.currentContext = this.createEmptyContext();\n    logger.info('Cleared MCP context');\n  }\n}\n\n// Create a singleton instance\nexport const mcpManager = new MCPManager();\n"]}