{"version": 3, "file": "workflowPanel.js", "sourceRoot": "", "sources": ["../../../src/ui/workflows/workflowPanel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAuC;AACvC,uDAAoD;AACpD,yCAAsC;AACtC,2CAAwF;AAmCxF,gFAAgF;AAChF,SAAS,mBAAmB,CAAC,QAA8C;IACzE,gEAAgE;IAChE,MAAM,iBAAiB,GAAG,OAAO,IAAI,QAAQ,CAAC;IAC9C,MAAM,aAAa,GAAG,QAA2B,CAAC;IAElD,MAAM,UAAU,GAAe;QAC7B,EAAE,EAAE,QAAQ,CAAC,EAAE;QACf,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,yBAAyB;QAC9D,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO;QAChF,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAE,QAA+B,CAAC,KAAK;QACvF,MAAM,EAAE,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;KACnD,CAAC;IAEF,0CAA0C;IAC1C,IAAI,iBAAiB,IAAI,aAAa,CAAC,QAAQ,EAAE,qBAAqB,EAAE,CAAC;QACvE,UAAU,CAAC,qBAAqB,GAAG,aAAa,CAAC,QAAQ,CAAC,qBAAqB,CAAC;QAChF,UAAU,CAAC,eAAe,GAAG,aAAa,CAAC,QAAQ,CAAC,eAAe,CAAC;QACpE,UAAU,CAAC,cAAc,GAAG,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC;QAClE,UAAU,CAAC,gBAAgB,GAAG,aAAa,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IACxE,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;GAGG;AACH,MAAa,aAAa;IACxB,8BAA8B;IACtB,MAAM,CAAC,gBAAgB,GAAU,EAAE,CAAC;IAErC,MAAM,CAAU,QAAQ,GAAG,uBAAuB,CAAC;IAClD,MAAM,CAAC,YAAY,CAA4B;IAEtC,MAAM,CAAsB;IAC5B,aAAa,CAAa;IACnC,YAAY,GAAwB,EAAE,CAAC;IAE/C,YAAoB,KAA0B,EAAE,YAAwB;QACtE,2BAA2B;QAC3B,iCAAe,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAE,CAAC,OAAO,CAAC,OAAO,CAAC;aACnG,IAAI,CAAC,SAAS,CAAC,EAAE;YAChB,aAAa,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QACL,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,yCAAyC;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,wCAAwC;QACxC,2FAA2F;QAC3F,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,2CAA2C;QAC3C,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAC9B,GAAG,EAAE;YACH,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CAClB,CAAC;QAEF,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACrC,KAAK,EAAC,OAAO,EAAC,EAAE;YACd,gDAAgD;YAChD,IAAI,OAAO,CAAC,OAAO,KAAK,8BAA8B,EAAE,CAAC;gBACvD,IAAI,CAAC;oBACH,MAAM,kBAAkB,GAAG,wDAAa,0BAA0B,GAAC,CAAC;oBACpE,MAAM,EAAE,YAAY,EAAE,GAAG,kBAAkB,CAAC;oBAC5C,0DAA0D;oBAC1D,sEAAsE;oBACtE,MAAM,aAAa,GAAG,UAAU,IAAS;wBACvC,MAAM,GAAG,GAAQ;4BACf,EAAE,EAAE,IAAI,CAAC,EAAE;4BACX,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE;4BAC1B,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;4BACnC,OAAO,EAAE,SAAS;yBACnB,CAAC;wBACF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;4BACjB,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC;4BACjB,KAAK,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gCAC5D,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;4BAC9C,CAAC;wBACH,CAAC;wBACD,OAAO,GAAG,CAAC;oBACb,CAAC,CAAC;oBACF,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;oBACrE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,KAAK,EAAE,CAAC,CAAC;gBAChF,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACxG,CAAC;gBACD,OAAO;YACT,CAAC;YACD,IAAI,OAAO,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC;gBACtG,OAAO;YACT,CAAC;YACD,IAAI,CAAC;gBACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gBAE1E,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;oBACxB,KAAK,gBAAgB;wBACnB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;wBACzD,MAAM;oBACR,KAAK,yBAAyB;wBAC5B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;wBAChE,MAAM;oBACR,KAAK,6BAA6B;wBAChC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,qCAAqC,CAAC,CAAC;wBACtE,MAAM;oBACR,KAAK,qBAAqB;wBACxB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC;wBAC9D,MAAM;oBACR,KAAK,uBAAuB;wBAC1B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;wBAC1D,MAAM;oBACR,KAAK,cAAc;wBACjB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;4BACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAA6B,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;wBACpF,CAAC;6BAAM,CAAC;4BACN,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;wBAC7E,CAAC;wBACD,MAAM;oBACR,KAAK,gBAAgB;wBACnB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;4BACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;wBACtF,CAAC;6BAAM,CAAC;4BACN,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wBAAwB,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;wBAC/E,CAAC;wBACD,MAAM;oBACR,KAAK,aAAa;wBAChB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;4BACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,4BAA4B,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;wBACnF,CAAC;6BAAM,CAAC;4BACN,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,qBAAqB,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;wBAC5E,CAAC;wBACD,MAAM;oBACR,KAAK,kBAAkB;wBACrB,IAAI,CAAC,OAAO,EAAE,CAAC;wBACf,MAAM;oBACR,KAAK,cAAc;wBACjB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;wBACjD,IAAI,CAAC,OAAO,EAAE,CAAC;wBACf,MAAM;oBACR;wBACE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CAClB,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,YAAwB;QACjD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YAC3C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEd,uCAAuC;QACvC,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;YAC/B,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACjD,OAAO;QACT,CAAC;QAED,iCAAiC;QACjC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,aAAa,CAAC,QAAQ,EACtB,mBAAmB,EACnB,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;YACE,mCAAmC;YACnC,aAAa,EAAE,IAAI;YAEnB,kFAAkF;YAClF,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;aAC3C;SACF,CACF,CAAC;QAEF,aAAa,CAAC,YAAY,GAAG,IAAI,aAAa,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACtE,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB,CAAC,QAAwC;QACxE,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAClC,EAAE,EAAE,MAAM,IAAI,CAAC,GAAG,EAAE,EAAE;YACtB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SAClC,CAAC,CAAC;QACH,MAAM,iCAAe,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAE,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC7I,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,MAAM,SAAS,CAAC,CAAC;IAC5F,CAAC;IAEO,KAAK,CAAC,OAAO;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,mBAAmB,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACpE,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAuB;QACtD,mCAAmC;QACnC,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QACnF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QACnF,MAAM,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;QAE/F,0BAA0B;QAC1B,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,mBAAmB,GAAG,OAAO,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAEvE,uDAAuD;QACvD,MAAM,KAAK,GAAG,IAAA,gBAAQ,GAAE,CAAC;QAEzB,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,aAAa,GAAU,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;YAEhG,sCAAsC;YACtC,MAAM,YAAY,GAAG,IAAA,0BAAe,GAAE,CAAC;YAEvC,0DAA0D;YAC1D,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAElE,yDAAyD;YACzD,MAAM,wBAAwB,GAAG,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;gBACxD,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,aAAa,CAAC,MAAM,2BAA2B,wBAAwB,CAAC,MAAM,oBAAoB,CAAC,CAAC;YAElI,8DAA8D;YAC9D,IAAI,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxD,MAAM,eAAe,GAAG,IAAA,0BAAe,EAAC,gBAAgB,CAAC,CAAC;gBAE1D,IAAI,eAAe,EAAE,CAAC;oBACpB,gDAAgD;oBAChD,qFAAqF;oBACrF,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,IAAI,SAAS,CAAC;oBACvD,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,YAAY,EAAE,CAAC,CAAC;oBAElE,IAAI,CAAC;wBACH,0CAA0C;wBAC1C,MAAM,QAAQ,GAAG,IAAA,iCAAsB,EAAC,gBAAgB,CAAC,CAAC;wBAE1D,wDAAwD;wBACxD,IAAI,QAAQ,IAAI,OAAQ,QAAmD,CAAC,aAAa,KAAK,UAAU,EAAE,CAAC;4BACzG,yDAAyD;4BACzD,MAAM,GAAG,GAAI,QAAoD,CAAC,aAAa,EAAE,CAAC;4BAClF,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC;wBACvG,CAAC;6BAAM,IAAI,QAAQ,EAAE,CAAC;4BACpB,oCAAoC;4BACpC,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2CAA2C,YAAY,EAAE,CAAC,CAAC;wBACnF,CAAC;6BAAM,CAAC;4BACN,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oDAAoD,YAAY,EAAE,CAAC,CAAC;wBAC3F,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;oBACtE,CAAC;gBACH,CAAC;YACH,CAAC;YAED,0EAA0E;YAC1E,MAAM,iBAAiB,GAAG;gBACxB,GAAG,aAAa;gBAChB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,GAAG,CAAC,KAAK,EAAC,EAAE,EAAC,EAAE;oBAC3D,MAAM,UAAU,GAAG,mBAAmB,CAAC,EAAS,CAAC,CAAC;oBAElD,yCAAyC;oBACzC,IAAI,CAAC;wBACH,MAAM,EAAE,gBAAgB,EAAE,GAAG,wDAAa,yCAAyC,GAAC,CAAC;wBACrF,MAAM,OAAO,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAC3D,IAAI,OAAO,EAAE,CAAC;4BACZ,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;wBAC/B,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;oBAClE,CAAC;oBAED,OAAO,UAAU,CAAC;gBACpB,CAAC,CAAC,CAAC;aACJ,CAAC;YAEF,mDAAmD;YACnD,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YAExD,OAAO;;;;;wFAK2E,OAAO,CAAC,SAAS,uBAAuB,KAAK;kBACnH,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qBAkDL,KAAK;;4BAEE,aAAa;;;;qBAIpB,KAAK,UAAU,mBAAmB;qBAClC,KAAK,UAAU,SAAS;;QAErC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YACtE,OAAO;;;;;wFAK2E,OAAO,CAAC,SAAS,uBAAuB,KAAK;;;;;;;;;;;;aAYxH,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;;;qBAG9C,KAAK;;;;;;;QAOlB,CAAC;QACL,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,aAAa,CAAC,YAAY,GAAG,SAAS,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9C,CAAC;;AAvXH,sCAwXC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { getNonce } from '../../utils';\nimport { WorkflowStorage } from './workflowStorage';\nimport { Logger } from '../../logger';\nimport { getAllWorkflows, getWorkflowById, createWorkflowInstance } from 'src/managers';\nimport { WorkflowDefinition } from '../../agents/workflows/workflowEngine';\nimport { GraphDefinition } from '../../agents/workflows/types';\n\n// Revolutionary workflow type for UI\ninterface UIWorkflow {\n  id: string;\n  name: string;\n  description: string;\n  version: string;\n  steps: any[];\n  engine?: string;\n\n  // Revolutionary enhancements\n  revolutionaryFeatures?: {\n    goddessMode?: boolean;\n    quantumAnalysis?: boolean;\n    neuralSynthesis?: boolean;\n    timeTravelDebugging?: boolean;\n    adaptivePersonality?: boolean;\n  };\n\n  metrics?: {\n    executionCount: number;\n    successRate: number;\n    averageDuration: number;\n    userSatisfactionScore: number;\n    goddessRating: number;\n  };\n\n  goddessBlessing?: string;\n  aiCapabilities?: string[];\n  enhancementLevel?: string;\n}\n\n// Revolutionary helper function to convert workflow definitions to UI workflows\nfunction convertToUIWorkflow(workflow: WorkflowDefinition | GraphDefinition): UIWorkflow {\n  // Check if it's a GraphDefinition by looking for nodes property\n  const isGraphDefinition = 'nodes' in workflow;\n  const graphWorkflow = workflow as GraphDefinition;\n\n  const uiWorkflow: UIWorkflow = {\n    id: workflow.id,\n    name: workflow.name,\n    description: workflow.description || 'No description provided',\n    version: isGraphDefinition ? graphWorkflow.version || '1.0.0' : workflow.version,\n    steps: isGraphDefinition ? graphWorkflow.nodes : (workflow as WorkflowDefinition).steps,\n    engine: isGraphDefinition ? 'codessa' : 'original'\n  };\n\n  // Add revolutionary features if available\n  if (isGraphDefinition && graphWorkflow.metadata?.revolutionaryFeatures) {\n    uiWorkflow.revolutionaryFeatures = graphWorkflow.metadata.revolutionaryFeatures;\n    uiWorkflow.goddessBlessing = graphWorkflow.metadata.goddessBlessing;\n    uiWorkflow.aiCapabilities = graphWorkflow.metadata.aiCapabilities;\n    uiWorkflow.enhancementLevel = graphWorkflow.metadata.enhancementLevel;\n  }\n\n  return uiWorkflow;\n}\n\n/**\n * Workflow Panel\n * Provides a UI for managing workflows\n */\nexport class WorkflowPanel {\n  // Persistent workflow storage\n  private static _loadedWorkflows: any[] = [];\n\n  public static readonly viewType = 'codessa.workflowPanel';\n  private static currentPanel: WorkflowPanel | undefined;\n\n  private readonly _panel: vscode.WebviewPanel;\n  private readonly _extensionUri: vscode.Uri;\n  private _disposables: vscode.Disposable[] = [];\n\n  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {\n    // Load workflows from disk\n    WorkflowStorage.loadWorkflows(vscode.extensions.getExtension('TekNerds-ITS.codessa')!.exports.context)\n      .then(workflows => {\n        WorkflowPanel._loadedWorkflows = workflows;\n        this._update();\n      });\n    this._panel = panel;\n    this._extensionUri = extensionUri;\n\n    // Set the webview's initial html content\n    this._update();\n\n    // Listen for when the panel is disposed\n    // This happens when the user closes the panel or when the panel is closed programmatically\n    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);\n\n    // Update the content based on view changes\n    this._panel.onDidChangeViewState(\n      () => {\n        if (this._panel.visible) {\n          this._update();\n        }\n      },\n      null,\n      this._disposables\n    );\n\n    // Handle messages from the webview\n    this._panel.webview.onDidReceiveMessage(\n      async message => {\n        // Tool picker: respond with all tools & actions\n        if (message.command === 'getAvailableToolsWithActions') {\n          try {\n            const toolRegistryModule = await import('../../tools/toolRegistry');\n            const { ToolRegistry } = toolRegistryModule;\n            // Recursively serialize tools and actions for the webview\n            // Move function declaration outside of block to fix strict mode error\n            const serializeTool = function (tool: any) {\n              const out: any = {\n                id: tool.id,\n                name: tool.name || tool.id,\n                description: tool.description || '',\n                actions: undefined\n              };\n              if (tool.actions) {\n                out.actions = {};\n                for (const [subId, subTool] of Object.entries(tool.actions)) {\n                  out.actions[subId] = serializeTool(subTool);\n                }\n              }\n              return out;\n            };\n            const tools = ToolRegistry.instance.getAllTools().map(serializeTool);\n            this._panel.webview.postMessage({ type: 'availableToolsWithActions', tools });\n          } catch (err) {\n            this._panel.webview.postMessage({ type: 'availableToolsWithActions', tools: [], error: String(err) });\n          }\n          return;\n        }\n        if (message.command === 'getWorkflows') {\n          this._panel.webview.postMessage({ type: 'workflowsList', workflows: WorkflowPanel._loadedWorkflows });\n          return;\n        }\n        try {\n          Logger.instance.info(`Received message from webview: ${message.command}`);\n\n          switch (message.command) {\n            case 'createWorkflow':\n              vscode.commands.executeCommand('codessa.createWorkflow');\n              break;\n            case 'createLangGraphWorkflow':\n              vscode.commands.executeCommand('codessa.createCodessaWorkflow');\n              break;\n            case 'createRevolutionaryWorkflow':\n              vscode.commands.executeCommand('codessa.createRevolutionaryWorkflow');\n              break;\n            case 'activateGoddessMode':\n              vscode.commands.executeCommand('codessa.activateGoddessMode');\n              break;\n            case 'enableQuantumAnalysis':\n              vscode.commands.executeCommand('codessa.quantumAnalysis');\n              break;\n            case 'editWorkflow':\n              if (message.engine === 'codessa') {\n                vscode.commands.executeCommand('codessa.editCodessaWorkflow', message.workflowId);\n              } else {\n                vscode.commands.executeCommand('codessa.editWorkflow', message.workflowId);\n              }\n              break;\n            case 'deleteWorkflow':\n              if (message.engine === 'codessa') {\n                vscode.commands.executeCommand('codessa.deleteCodessaWorkflow', message.workflowId);\n              } else {\n                vscode.commands.executeCommand('codessa.deleteWorkflow', message.workflowId);\n              }\n              break;\n            case 'runWorkflow':\n              if (message.engine === 'codessa') {\n                vscode.commands.executeCommand('codessa.runCodessaWorkflow', message.workflowId);\n              } else {\n                vscode.commands.executeCommand('codessa.runWorkflow', message.workflowId);\n              }\n              break;\n            case 'refreshWorkflows':\n              this._update();\n              break;\n            case 'saveWorkflow':\n              await this._handleSaveWorkflow(message.workflow);\n              this._update();\n              break;\n            default:\n              Logger.instance.warn(`Unknown command: ${message.command}`);\n          }\n        } catch (error) {\n          Logger.instance.error('Error handling webview message:', error);\n        }\n      },\n      null,\n      this._disposables\n    );\n  }\n\n  public static createOrShow(extensionUri: vscode.Uri) {\n    const column = vscode.window.activeTextEditor\n      ? vscode.window.activeTextEditor.viewColumn\n      : undefined;\n\n    // If we already have a panel, show it.\n    if (WorkflowPanel.currentPanel) {\n      WorkflowPanel.currentPanel._panel.reveal(column);\n      return;\n    }\n\n    // Otherwise, create a new panel.\n    const panel = vscode.window.createWebviewPanel(\n      WorkflowPanel.viewType,\n      'Codessa Workflows',\n      column || vscode.ViewColumn.One,\n      {\n        // Enable javascript in the webview\n        enableScripts: true,\n\n        // And restrict the webview to only loading content from our extension's directory\n        localResourceRoots: [\n          vscode.Uri.joinPath(extensionUri, 'media')\n        ]\n      }\n    );\n\n    WorkflowPanel.currentPanel = new WorkflowPanel(panel, extensionUri);\n  }\n\n  /**\n     * Handle saving a workflow from the webview\n     */\n  private async _handleSaveWorkflow(workflow: { name: string, steps: any[] }) {\n    WorkflowPanel._loadedWorkflows.push({\n      id: `wf_${Date.now()}`,\n      name: workflow.name,\n      steps: workflow.steps,\n      created: new Date().toISOString(),\n    });\n    await WorkflowStorage.saveWorkflows(vscode.extensions.getExtension('TekNerds-ITS.codessa')!.exports.context, WorkflowPanel._loadedWorkflows);\n    Logger.instance.info(`Saved workflow: ${workflow.name} (${workflow.steps.length} steps)`);\n  }\n\n  private async _update() {\n    const webview = this._panel.webview;\n\n    this._panel.title = 'Codessa Workflows';\n    this._panel.webview.html = await this._getHtmlForWebview(webview);\n  }\n\n  private async _getHtmlForWebview(webview: vscode.Webview) {\n    // Get the local paths to resources\n    const stylePath = vscode.Uri.joinPath(this._extensionUri, 'media', 'workflow.css');\n    const scriptPath = vscode.Uri.joinPath(this._extensionUri, 'media', 'workflow.js');\n    const toolPickerScriptPath = vscode.Uri.joinPath(this._extensionUri, 'media', 'toolPicker.js');\n\n    // Convert to webview URIs\n    const styleUri = webview.asWebviewUri(stylePath);\n    const scriptUri = webview.asWebviewUri(scriptPath);\n    const toolPickerScriptUri = webview.asWebviewUri(toolPickerScriptPath);\n\n    // Use a nonce to only allow specific scripts to be run\n    const nonce = getNonce();\n\n    try {\n      // Get workflows from original registry\n      const workflowsData: any[] = await vscode.commands.executeCommand('codessa.getWorkflows') || [];\n\n      // Get workflows from Codessa registry\n      const allWorkflows = getAllWorkflows();\n\n      // Create a map of existing workflow IDs for faster lookup\n      const existingWorkflowIds = new Set(workflowsData.map(w => w.id));\n\n      // Filter out workflows that are already in workflowsData\n      const filteredCodessaWorkflows = allWorkflows.filter(wf => {\n        return !existingWorkflowIds.has(wf.id);\n      });\n\n      Logger.instance.info(`Found ${workflowsData.length} original workflows and ${filteredCodessaWorkflows.length} Codessa workflows`);\n\n      // Example of using getWorkflowById and createWorkflowInstance\n      if (filteredCodessaWorkflows.length > 0) {\n        const sampleWorkflowId = filteredCodessaWorkflows[0].id;\n        const workflowDetails = getWorkflowById(sampleWorkflowId);\n\n        if (workflowDetails) {\n          // Access the name directly from workflowDetails\n          // GraphDefinition doesn't have a workflow property, but has a name property directly\n          const workflowName = workflowDetails.name || 'Unknown';\n          Logger.instance.debug(`Sample workflow details: ${workflowName}`);\n\n          try {\n            // Create an instance but don't execute it\n            const instance = createWorkflowInstance(sampleWorkflowId);\n\n            // Check instance type and access properties accordingly\n            if (instance && typeof (instance as unknown as { getDefinition?: unknown }).getDefinition === 'function') {\n              // It's a Workflow or Codessa instance with getDefinition\n              const def = (instance as unknown as { getDefinition: () => any }).getDefinition();\n              Logger.instance.debug(`Created workflow instance for: ${def && def.name ? def.name : workflowName}`);\n            } else if (instance) {\n              // It's a different kind of instance\n              Logger.instance.debug(`Created workflow instance for workflow: ${workflowName}`);\n            } else {\n              Logger.instance.warn(`Could not create workflow instance for workflow: ${workflowName}`);\n            }\n          } catch (error) {\n            Logger.instance.error(`Error creating workflow instance: ${error}`);\n          }\n        }\n      }\n\n      // Create combined workflows array using our revolutionary helper function\n      const combinedWorkflows = [\n        ...workflowsData,\n        ...await Promise.all(filteredCodessaWorkflows.map(async wf => {\n          const uiWorkflow = convertToUIWorkflow(wf as any);\n\n          // Add metrics from registry if available\n          try {\n            const { workflowRegistry } = await import('../../agents/workflows/workflowRegistry');\n            const metrics = workflowRegistry.getWorkflowMetrics(wf.id);\n            if (metrics) {\n              uiWorkflow.metrics = metrics;\n            }\n          } catch (error) {\n            Logger.instance.warn('Could not load workflow metrics:', error);\n          }\n\n          return uiWorkflow;\n        }))\n      ];\n\n      // Convert workflows to JSON string for the webview\n      const workflowsJson = JSON.stringify(combinedWorkflows);\n\n      return `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';\">\n    <link href=\"${styleUri}\" rel=\"stylesheet\">\n    <title>Codessa Workflows</title>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>✨ Revolutionary Workflows</h1>\n            <div class=\"goddess-blessing\">\n                <p class=\"blessing-text\">🌟 \"May your workflows be blessed with divine efficiency and quantum insights\" - Codessa</p>\n            </div>\n            <div class=\"actions\">\n                <button id=\"create-workflow\" class=\"primary-button\">Create Workflow</button>\n                <button id=\"create-codessa-workflow\" class=\"primary-button\">Create Codessa Workflow</button>\n                <button id=\"create-revolutionary-workflow\" class=\"revolutionary-button\">✨ Create Revolutionary Workflow</button>\n                <button id=\"activate-goddess-mode\" class=\"goddess-button\">👑 Activate Goddess Mode</button>\n                <button id=\"enable-quantum-analysis\" class=\"quantum-button\">🔬 Quantum Analysis</button>\n                <button id=\"refresh-workflows\" class=\"secondary-button\">Refresh</button>\n            </div>\n        </div>\n\n        <div class=\"revolutionary-features-panel\">\n            <h3>🚀 Revolutionary Features Available</h3>\n            <div class=\"features-grid\">\n                <div class=\"feature-card goddess-mode\">\n                    <div class=\"feature-icon\">✨</div>\n                    <div class=\"feature-name\">Goddess Mode</div>\n                    <div class=\"feature-desc\">Emotional intelligence & adaptive personality</div>\n                </div>\n                <div class=\"feature-card quantum-analysis\">\n                    <div class=\"feature-icon\">🔬</div>\n                    <div class=\"feature-name\">Quantum Analysis</div>\n                    <div class=\"feature-desc\">Quantum-inspired pattern recognition</div>\n                </div>\n                <div class=\"feature-card neural-synthesis\">\n                    <div class=\"feature-icon\">🧠</div>\n                    <div class=\"feature-name\">Neural Synthesis</div>\n                    <div class=\"feature-desc\">Brain-inspired code generation</div>\n                </div>\n                <div class=\"feature-card time-travel\">\n                    <div class=\"feature-icon\">⏰</div>\n                    <div class=\"feature-name\">Time-Travel Debug</div>\n                    <div class=\"feature-desc\">Predict future issues</div>\n                </div>\n            </div>\n        </div>\n\n        <div id=\"workflow-list\" class=\"workflow-list\"></div>\n        <div id=\"tool-picker-container\" class=\"tool-picker-container\"></div>\n    </div>\n\n    <script nonce=\"${nonce}\">\n        // Workflows data from extension\n        const workflows = ${workflowsJson};\n        // Get VS Code API\n        const vscode = acquireVsCodeApi();\n    </script>\n    <script nonce=\"${nonce}\" src=\"${toolPickerScriptUri}\"></script>\n    <script nonce=\"${nonce}\" src=\"${scriptUri}\"></script>\n</body>\n</html>`;\n    } catch (error) {\n      Logger.instance.error('Error generating workflow panel HTML:', error);\n      return `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';\">\n    <title>Codessa Workflows - Error</title>\n    <style>\n        body { padding: 20px; font-family: var(--vscode-font-family); color: var(--vscode-foreground); }\n        .error { color: var(--vscode-errorForeground); margin: 20px 0; padding: 10px; border: 1px solid var(--vscode-errorForeground); }\n        button { background: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; padding: 8px 12px; cursor: pointer; }\n    </style>\n</head>\n<body>\n    <h1>Error Loading Workflows</h1>\n    <div class=\"error\">\n        <p>There was an error loading the workflows. Please try refreshing the panel.</p>\n        <p>${error instanceof Error ? error.message : String(error)}</p>\n    </div>\n    <button id=\"refresh\">Refresh</button>\n    <script nonce=\"${nonce}\">\n        const vscode = acquireVsCodeApi();\n        document.getElementById('refresh').addEventListener('click', () => {\n            vscode.postMessage({ command: 'refreshWorkflows' });\n        });\n    </script>\n</body>\n</html>`;\n    }\n  }\n\n  public dispose() {\n    WorkflowPanel.currentPanel = undefined;\n    this._panel.dispose();\n    this._disposables.forEach(d => d.dispose());\n  }\n}\n"]}