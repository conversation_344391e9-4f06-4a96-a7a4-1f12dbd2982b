"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.terminalCommandTool = exports.TerminalCommandTool = void 0;
exports.getDefaultShell = getDefaultShell;
const zod_to_json_schema_1 = require("zod-to-json-schema");
/**
 * AI Agent Tool to execute terminal commands across different operating systems.
 * Provides robust execution and output capture for non-interactive commands.
 */
const vscode = __importStar(require("vscode"));
const cp = __importStar(require("child_process")); // Node.js child_process module
const util_1 = require("util"); // Node.js util module for promises
const zod_1 = require("zod"); // Zod for schema validation
// Assume these are available paths in the project
const logger_1 = require("../logger"); // Assuming logging utility is available
const config_1 = require("../config"); // Assuming configuration utility is available
const os = __importStar(require("os")); // Node.js os module
const nodePath = __importStar(require("path")); // Node.js path module - Renamed to avoid clash if 'path' variable is used
// Promisify cp.exec for fallback or alternative execution strategy
const execP = (0, util_1.promisify)(cp.exec);
/**
 * Utility to get the default shell based on the operating system and configuration.
 * @returns The path to the default shell executable string.
 */
function getDefaultShell() {
    const configShell = (0, config_1.getConfig)('tools.terminalCommand.defaultShell', undefined);
    if (configShell) {
        // Validate if configured shell exists and is executable? Too complex for polyfill. Assume valid.
        return configShell;
    }
    const platform = os.platform();
    if (platform === 'win32') {
        // On Windows, prefer PowerShell if available, otherwise Cmd.exe
        // Checking for existence is complex asyncly. Just return potential paths.
        // PowerShell path can vary (System32, SysWOW64). Using just "powershell.exe" relies on PATH.
        // Cmd.exe is usually in System32.
        // Let's prioritize PowerShell if it's the OS default, otherwise Cmd.
        // Relying on spawn's shell option interpretation is best practice.
        // 'powershell' or 'cmd' might work directly with spawn.
        // Use 'cmd' as a safer default fallback for Windows if no config.
        return 'cmd.exe';
    }
    else {
        // On Unix-like systems, default to bash
        return process.env.SHELL || '/bin/bash';
    }
}
/**
 * Validates if a command string is allowed based on configured allow/forbidden lists.
 * Note: This is a basic check and not a full security sandbox. Shell commands
 * are inherently powerful and complex.
 * @param command The full command string including args.
 * @param config The tool's configuration with allowed/forbidden lists.
 * @returns True if the command is allowed, false otherwise.
 */
function isCommandAllowed(command, _config) {
    const commandLower = command.toLowerCase();
    // If allow-list exists, command must match at least one pattern
    if (_config.allowedCommands && _config.allowedCommands.length > 0) {
        let allowedByList = false;
        for (const allowedPattern of _config.allowedCommands) {
            try {
                // Test as regex if it looks like one, otherwise as simple substring/prefix
                if (allowedPattern.startsWith('/') && allowedPattern.endsWith('/')) {
                    const regex = new RegExp(allowedPattern.slice(1, -1));
                    if (regex.test(command)) {
                        allowedByList = true;
                        break;
                    }
                }
                else if (command.startsWith(allowedPattern)) { // Simple prefix match
                    allowedByList = true;
                    break;
                }
                else if (command.includes(allowedPattern)) { // Simple includes match (less secure)
                    allowedByList = true;
                    break;
                }
            }
            catch (e) {
                logger_1.Logger.instance.error('Invalid regex in allowedCommands config:', e);
                // Invalid regex in allow list - treat as not allowed for safety
                return false;
            }
        }
        if (!allowedByList) {
            logger_1.Logger.instance.warn('Command blocked: Not matched by any allowed pattern: "' + command + '"');
            return false;
        }
    }
    // If forbidden-list exists, command must *not* match any pattern
    if (_config.forbiddenCommands && _config.forbiddenCommands.length > 0) {
        for (const forbiddenPattern of _config.forbiddenCommands) {
            try {
                // Test as regex if it looks like one, otherwise as simple substring/prefix
                if (forbiddenPattern.startsWith('/') && forbiddenPattern.endsWith('/')) {
                    const regex = new RegExp(forbiddenPattern.slice(1, -1));
                    if (regex.test(command)) {
                        logger_1.Logger.instance.warn('Command blocked by forbidden regex pattern: "' + command + '"');
                        return false;
                    }
                }
                else if (command.startsWith(forbiddenPattern)) { // Simple prefix match
                    logger_1.Logger.instance.warn('Command blocked by forbidden prefix: "' + command + '"');
                    return false;
                }
                else if (command.includes(forbiddenPattern)) { // Simple includes match
                    logger_1.Logger.instance.warn('Command blocked by forbidden substring: "' + command + '"');
                    return false;
                }
            }
            catch (e) {
                logger_1.Logger.instance.error('Invalid regex in forbiddenCommands config:', e);
                // Invalid regex in forbidden list - block command for safety
                return false;
            }
        }
    }
    return true; // Passed checks
}
/**
 * Implements the ITool interface for executing terminal commands.
 * Supports structured input for command, arguments, cwd, env, timeout, shell, stdin, and output limits.
 * Uses child_process.spawn for better control and stream handling, with an option to use exec.
 */
class TerminalCommandTool {
    id = 'terminal_command';
    name = 'terminal_command'; // Use a machine-friendly name
    description = 'Executes a shell command or script in a terminal environment and returns output. Supports specifying working directory, environment variables, timeout, shell, and input.';
    category = 'System';
    type = 'single-action'; // Define as single-action tool
    schema = zod_1.z.object({
        command: zod_1.z.string().min(1).describe('The command string or executable path (e.g., "ls", "dir", "git status", "python script.py").'),
        args: zod_1.z.string().array().optional().describe('Optional arguments to pass to the command.'),
        cwd: zod_1.z.string().optional().describe('The current working directory for the command. Defaults to the VS Code workspace root or the current process directory.'),
        env: zod_1.z.record(zod_1.z.string(), zod_1.z.string().nullable().optional()).optional().describe('Environment variables to set for the command process (e.g., { "NODE_ENV": "development", "PATH": "/usr/local/bin" }). Null/undefined values remove inherited vars.'),
        timeout: zod_1.z.number().int().positive().optional().describe('Maximum duration in milliseconds for the command to run.'),
        shell: zod_1.z.string().optional().describe('The shell executable to use (e.g., "bash", "powershell", "cmd.exe").'),
        stdin: zod_1.z.string().optional().describe('Optional input string to pipe to the command\'s stdin.'),
        outputLimitBytes: zod_1.z.number().int().positive().optional().describe('Maximum bytes to capture from stdout and stderr before truncating.')
    });
    actions = {}; // Required by ITool
    // Define the input schema using Zod for validation and structured input
    singleActionSchema = zod_1.z.object({
        /** The command string or executable path. Required. */
        command: zod_1.z.string().min(1).describe('The command string or executable path (e.g., "ls", "dir", "git status", "python script.py").'),
        /** Optional array of arguments to pass to the command. */
        args: zod_1.z.string().array().optional().describe('Optional arguments to pass to the command.'),
        /** The current working directory for the command. Defaults to the workspace root if available. */
        cwd: zod_1.z.string().optional().describe('The current working directory for the command. Defaults to the VS Code workspace root or the current process directory.'),
        /** Environment variables to set for the command process. Merges with parent process env by default. Use null or undefined values to remove inherited env vars. */
        env: zod_1.z.record(zod_1.z.string(), zod_1.z.string().nullable().optional()).optional().describe('Environment variables to set for the command process (e.g., { "NODE_ENV": "development", "PATH": "/usr/local/bin" }). Null/undefined values remove inherited vars.'),
        /** Maximum duration in milliseconds for the command to run before being terminated. */
        timeout: zod_1.z.number().int().positive().optional().describe('Maximum duration in milliseconds for the command to run.'),
        /** The shell executable to use to run the command (e.g., "bash", "powershell", "cmd.exe"). Defaults to the system default or configured default. */
        shell: zod_1.z.string().optional().describe('The shell executable to use (e.g., "bash", "powershell", "cmd.exe").'),
        /** Optional input string to pipe to the command's stdin. */
        stdin: zod_1.z.string().optional().describe('Optional input string to pipe to the command\'s stdin.'),
        /** Maximum bytes to capture from stdout and stderr. Output exceeding this limit will be truncated. */
        outputLimitBytes: zod_1.z.number().int().positive().optional().describe('Maximum bytes to capture from stdout and stderr before truncating.'),
    }).describe('Input parameters for executing a terminal command.');
    config;
    constructor() {
        // Load configuration during instantiation
        const allowedCommands = (0, config_1.getConfig)('tools.terminalCommand.allowedCommands', undefined) || [];
        const forbiddenCommands = (0, config_1.getConfig)('tools.terminalCommand.forbiddenCommands', undefined) || [];
        this.config = {
            maxOutputBufferBytes: (0, config_1.getConfig)('tools.terminalCommand.maxOutputBufferBytes', 10 * 1024 * 1024), // Default 10MB
            defaultTimeoutMs: (0, config_1.getConfig)('tools.terminalCommand.defaultTimeoutMs', 60000), // Default 60 seconds
            defaultShell: (0, config_1.getConfig)('tools.terminalCommand.defaultShell', undefined) || getDefaultShell(),
            allowedCommands: allowedCommands.length > 0 ? allowedCommands : undefined,
            forbiddenCommands: forbiddenCommands.length > 0 ? forbiddenCommands : undefined,
            useExecForSimpleCommands: (0, config_1.getConfig)('tools.terminalCommand.useExecForSimpleCommands', false),
        };
        logger_1.Logger.instance.info('TerminalCommandTool initialized with config:', {
            ...this.config,
            allowedCommands: this.config.allowedCommands ? this.config.allowedCommands.length + ' patterns' : 'all',
            forbiddenCommands: this.config.forbiddenCommands ? this.config.forbiddenCommands.length + ' patterns' : 'none',
            defaultShell: this.config.defaultShell,
            useExec: this.config.useExecForSimpleCommands
        });
    }
    /**
       * Provides the structured definition for the LLM.
       * Conforms to ITool.getDefinitionForModel.
       * @returns The JSON schema definition suitable for LLM tool calling.
       */
    getDefinitionForModel() {
        // Use zod-to-json-schema to convert Zod schema to JSON Schema
        if (this.type === 'single-action' && this.singleActionSchema) {
            const schemaObject = (0, zod_to_json_schema_1.zodToJsonSchema)(this.singleActionSchema);
            // Attempt to fix potential Zod toJSON issues or enrich
            if (schemaObject &&
                typeof schemaObject === 'object' &&
                'properties' in schemaObject &&
                schemaObject.properties &&
                'env' in schemaObject.properties &&
                schemaObject.properties.env) {
                schemaObject.properties.env = {
                    type: 'object',
                    description: schemaObject.properties.env.description,
                    additionalProperties: { type: ['string', 'null'] }
                };
            }
            return {
                name: this.name,
                description: this.description,
                parameters: schemaObject,
            };
        }
        else {
            // This case should not happen for this tool's definition, but included for safety
            logger_1.Logger.instance.error('TerminalCommandTool getDefinitionForModel: Tool type is not single-action or singleActionSchema is missing.');
            // Return a minimal definition if schema generation fails
            return {
                name: this.name,
                description: this.description,
                parameters: {
                    type: 'object',
                    properties: {
                        command: { type: 'string', description: 'Shell command string to execute.' }
                    },
                    required: ['command']
                },
            };
        }
    }
    /**
       * Executes a terminal command. Uses child_process.spawn by default,
       * with an option for cp.exec for simple cases.
       * @param input - The validated input conforming to singleActionSchema.
       * @param context - Optional agent context.
       * @returns A promise resolving to the tool's result.
       */
    async execute(input, context) {
        // Validate input strictly against the Zod schema here to be safe,
        // although the calling framework *should* have done this.
        const parsedInput = this.singleActionSchema.safeParse(input);
        if (!parsedInput.success) {
            const formattedError = parsedInput.error.format();
            const errorMsg = 'Invalid input for tool \'' + this.name + '\': ' +
                Object.entries(formattedError)
                    .filter(([key]) => key !== '_errors')
                    .map(([key, value]) => `${key}: ${value._errors.join(', ')}`)
                    .join('; ');
            logger_1.Logger.instance.error(errorMsg, parsedInput.error);
            return { success: false, error: errorMsg, toolId: this.id, actionName };
        }
        const validatedInput = parsedInput.data;
        const command = validatedInput.command;
        const args = validatedInput.args || [];
        // Determine CWD: Use provided, then workspace root, then current process CWD
        let cwd = validatedInput.cwd ?? (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0 ? vscode.workspace.workspaceFolders[0].uri.fsPath : process.cwd());
        // Normalize cwd using nodePath to ensure cross-platform compatibility
        cwd = nodePath.resolve(cwd);
        // Prepare environment variables: Merge provided env with current process env. Handle null/undefined to remove.
        const currentEnv = { ...process.env };
        const env = validatedInput.env ? Object.keys(validatedInput.env).reduce((acc, key) => {
            if (validatedInput.env[key] === null || validatedInput.env[key] === undefined) {
                delete acc[key]; // Remove from environment
            }
            else {
                acc[key] = validatedInput.env[key]; // Add or overwrite
            }
            return acc;
        }, currentEnv) : currentEnv;
        const timeoutMs = validatedInput.timeout ?? this.config.defaultTimeoutMs;
        const shell = validatedInput.shell ?? this.config.defaultShell;
        const stdinInput = validatedInput.stdin;
        const outputLimitBytes = validatedInput.outputLimitBytes ?? this.config.maxOutputBufferBytes;
        // Command Security Check on the combined command string
        const fullCommandString = command + (args.length > 0 ? ' ' + args.join(' ') : '');
        if (!isCommandAllowed(fullCommandString, this.config)) {
            const errorMsg = 'Execution of command "' + fullCommandString + '" is blocked by security configuration.';
            logger_1.Logger.instance.error(errorMsg);
            return { success: false, error: errorMsg, toolId: this.id };
        }
        logger_1.Logger.instance.info('Executing command: "' + fullCommandString + '"');
        logger_1.Logger.instance.debug('Options: cwd="' + cwd + '", shell="' + shell + '", timeout=' + timeoutMs + 'ms, outputLimit=' + outputLimitBytes + ' bytes, stdin=' + (stdinInput ? 'present' : 'absent') + ', envKeys=[' + Object.keys(env || {}).join(', ') + ']');
        // Decide between spawn and exec based on config or input complexity (e.g., presence of stdin)
        const useExec = this.config.useExecForSimpleCommands && !stdinInput && args.length === 0;
        if (useExec) {
            logger_1.Logger.instance.debug('Using cp.exec for simple command.');
            try {
                // cp.exec combines stdout/stderr unless options.maxBuffer is exceeded, then it errors.
                // It also has a default timeout option.
                const { stdout, stderr } = await execP(fullCommandString, {
                    cwd,
                    env,
                    shell, // Pass shell option
                    timeout: timeoutMs,
                    maxBuffer: outputLimitBytes, // Limit output buffer
                });
                // cp.exec resolves even on non-zero exit codes, unless stdio exceeds maxBuffer or spawn fails
                // We need to manually check for errors captured in stderr or non-zero exit code if using exec.
                // Promisified exec throws on non-zero exit code by default if stdout is empty.
                // If stderr is non-empty, it's often an error.
                if (stderr) {
                    logger_1.Logger.instance.warn('Command "' + fullCommandString + '" executed with stderr:\n' + stderr);
                }
                return {
                    success: true, // Assume success if execP didn't throw and stderr check is handled
                    output: {
                        stdout: stdout,
                        stderr: stderr,
                        // exec doesn't provide explicit exitCode/signal in successful result
                        exitCode: null,
                        signal: null,
                        truncated: { stdout: false, stderr: false }, // exec throws on maxBuffer exceeded
                    },
                    error: stderr || (stdout ? undefined : 'Command produced no output'), // Treat non-empty stderr as a potential error indicator
                    toolId: this.id,
                    metadata: {
                        command: fullCommandString,
                        cwd, shell, timeoutMs,
                        executionMethod: 'exec',
                    },
                };
            }
            catch (error) {
                // exec errors on spawn failure, timeout, or maxBuffer exceeded
                logger_1.Logger.instance.error('cp.exec failed for command "' + fullCommandString + '":', error);
                let errorMessage = error.message || 'Command execution failed.';
                let stdout = error.stdout || '';
                let stderr = error.stderr || '';
                // Improve error message for timeouts or buffer limits from exec
                if (error.killed && error.signal) {
                    errorMessage = 'Command timed out (' + timeoutMs + 'ms) and was terminated with signal ' + error.signal + '.';
                }
                else if (error.code !== undefined) {
                    errorMessage = ('Command exited with non-zero code ' + error.code + '. ' + (error.message || '')).trim();
                }
                else if (error.message && error.message.includes('maxBuffer exceeded')) {
                    errorMessage = 'Command output exceeded limit (' + outputLimitBytes + ' bytes).';
                    stdout = stdout.substring(0, outputLimitBytes); // Truncate captured output
                    stderr = stderr.substring(0, outputLimitBytes);
                }
                return {
                    success: false,
                    error: errorMessage,
                    toolId: this.id,
                    metadata: {
                        command: fullCommandString,
                        cwd, shell, timeoutMs,
                        executionMethod: 'exec',
                        execErrorCode: error.code,
                        execSignal: error.signal,
                        execKilled: error.killed,
                    },
                    output: { stdout, stderr, exitCode: error.code, signal: error.signal, truncated: { stdout: stdout.length === outputLimitBytes, stderr: stderr.length === outputLimitBytes } }
                };
            }
        }
        else {
            logger_1.Logger.instance.debug('Using cp.spawn for robust command execution.');
            // Use spawn for better control over streams and larger outputs
            const process = cp.spawn(command, args, {
                cwd,
                env,
                shell: shell, // Pass the specified shell executable
                timeout: timeoutMs, // Use spawn's built-in timeout (Node 10+)
                // stdio: 'pipe' is default, handles stdin, stdout, stderr streams
            });
            const stdoutBuffer = Buffer.from('');
            const stderrBuffer = Buffer.from('');
            const stdoutTruncated = false;
            const stderrTruncated = false;
            const collectOutput = (stream, buffer, truncated) => {
                return new Promise((resolve, reject) => {
                    let currentBuffer = Buffer.from(buffer);
                    let currentTruncated = truncated;
                    stream.on('data', (data) => {
                        if (currentBuffer.length + data.length <= outputLimitBytes) {
                            currentBuffer = Buffer.concat([currentBuffer, data]);
                        }
                        else if (!currentTruncated) {
                            // Only capture up to the limit and mark as truncated
                            currentBuffer = Buffer.concat([currentBuffer, data.slice(0, outputLimitBytes - currentBuffer.length)]);
                            currentTruncated = true;
                            logger_1.Logger.instance.warn((stream === process.stdout ? 'Stdout' : 'Stderr') + ' truncated for command "' + command + '" due to limit ' + outputLimitBytes + '.');
                            // Optionally pause/destroy stream to stop receiving data
                            // stream.pause(); // Or stream.destroy();
                        }
                        // If truncated, ignore further data
                    });
                    stream.on('end', () => resolve([currentBuffer, currentTruncated]));
                    stream.on('error', reject); // Handle stream errors
                });
            };
            try {
                // Use Promise.all to capture both stdout and stderr streams
                const stdoutPromise = process.stdout ? collectOutput(process.stdout, stdoutBuffer, stdoutTruncated) : Promise.resolve([Buffer.from(''), false]);
                const stderrPromise = process.stderr ? collectOutput(process.stderr, stderrBuffer, stderrTruncated) : Promise.resolve([Buffer.from(''), false]);
                // Wait for the process to close AND for output streams to end
                const [exitResult, [finalStdoutBuffer, finalStdoutTruncated], [finalStderrBuffer, finalStderrTruncated]] = await Promise.all([
                    new Promise((resolve, reject) => {
                        let error;
                        process.on('error', (err) => {
                            // Handles errors like command not found, permission issues *during spawn*
                            logger_1.Logger.instance.error('Failed to spawn command "' + command + '": ' + err.message);
                            error = err; // Capture spawn error
                            // Reject the main process promise on spawn error
                            reject(err);
                        });
                        process.on('close', (code, signal) => {
                            logger_1.Logger.instance.debug('Command "' + command + '" closed with code ' + code + ' and signal ' + signal + '.');
                            // Resolve the main process promise on close
                            resolve({ code, signal, error }); // Include potential spawn error
                        });
                        // Implement manual timeout for spawn
                        let killedByTimeout = false;
                        const timeoutHandle = setTimeout(() => {
                            if (!process.killed) {
                                logger_1.Logger.instance.warn('Command "' + command + '" exceeded timeout (' + timeoutMs + 'ms) and will be killed.');
                                killedByTimeout = true;
                                process.kill('SIGTERM'); // or 'SIGKILL' if you want to force
                            }
                        }, timeoutMs);
                        process.on('close', () => {
                            clearTimeout(timeoutHandle);
                        });
                    }),
                    stdoutPromise,
                    stderrPromise,
                ]);
                const stdout = finalStdoutBuffer.toString('utf8');
                const stderr = finalStderrBuffer.toString('utf8');
                // Check for spawn errors captured by the 'error' event
                if (exitResult.error) {
                    throw exitResult.error; // Re-throw spawn error
                }
                // Determine success based on exit code
                const success = exitResult.code === 0; // Zero exit code usually indicates success
                // Return the detailed result structure
                return {
                    success: success,
                    output: {
                        stdout: stdout,
                        stderr: stderr,
                        exitCode: exitResult.code,
                        signal: exitResult.signal,
                        truncated: {
                            stdout: finalStdoutTruncated,
                            stderr: finalStderrTruncated,
                        },
                    },
                    error: success ? undefined : 'Command exited with code ' + (exitResult.code || 'null') + (exitResult.signal ? ' (signal ' + exitResult.signal + ')' : '') + (stderr ? '\nStderr: ' + stderr.substring(0, 200) + '...' : ''),
                    toolId: this.id,
                    metadata: {
                        command: fullCommandString,
                        cwd, shell, timeoutMs,
                        executionMethod: 'spawn',
                    },
                };
            }
            catch (error) {
                // This catch block handles errors during spawning or stream errors
                logger_1.Logger.instance.error('Error during cp.spawn or stream for command "' + fullCommandString + '":', error);
                // Capture whatever output was collected before the error
                const partialStdout = stdoutBuffer.toString('utf8');
                const partialStderr = stderrBuffer.toString('utf8');
                return {
                    success: false,
                    error: error.message || 'Failed to execute command (spawn error).',
                    toolId: this.id,
                    metadata: {
                        command: fullCommandString,
                        cwd, shell, timeoutMs,
                        executionMethod: 'spawn',
                        processError: true, // Indicate an error before command completion
                    },
                    output: {
                        stdout: partialStdout,
                        stderr: partialStderr,
                        exitCode: null,
                        signal: null,
                        truncated: {
                            stdout: stdoutTruncated, // Use truncation flags captured
                            stderr: stderrTruncated,
                        },
                    }
                };
            }
        }
    }
}
exports.TerminalCommandTool = TerminalCommandTool;
// Singleton pattern with lazy initialization
class TerminalCommandToolSingleton {
    static instance = null;
    constructor() { }
    static getInstance() {
        if (!TerminalCommandToolSingleton.instance) {
            TerminalCommandToolSingleton.instance = new TerminalCommandTool();
        }
        return TerminalCommandToolSingleton.instance;
    }
}
// Export the singleton instance
exports.terminalCommandTool = TerminalCommandToolSingleton.getInstance();
//# sourceMappingURL=terminalCommandTool.js.map