{"version": 3, "file": "providersModelsSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/providersModelsSettingsSection.ts"], "names": [], "mappings": ";AAAA,iDAAiD;;AA+CjD,oFAyDC;AAtGD,kDAAkD;AAClD,SAAS,aAAa,CAAC,GAA8B;IACnD,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,CAAC;AAWD,gDAAqE;AAErE,IAAI,cAAc,GAAoB,EAAE,CAAC;AACzC,IAAI,uBAAuB,GAAkB,IAAI,CAAC;AAClD,MAAM,YAAY,GAA6B,kCAAoB,CAAC,OAAO,CAAC;AAE5E,iEAAiE;AACjE,MAAM,aAAa,GAAG;IACpB,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;IAChC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;IAChC,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE;IACtC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE;IACrC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE;IACrC,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAE;IACvC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;IAChC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE;IACpC,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE;IACxC,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,EAAE;IAC1C,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE;IACtC,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE;IACtC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;IAChC,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,EAAE;IAC1C,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE;IACtC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;IAC1B,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;IAChC,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE;IACtC,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE;IACxC,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE;IACxC,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE;CACzC,CAAC;AAEF,SAAgB,oCAAoC,CAAC,SAAsB,EAAE,QAAa;IACxF,qBAAqB;IACrB,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;IACvF,0BAA0B,CAAC,SAAS,CAAC,CAAC;IAEtC,8BAA8B;IAC9B,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC3G,IAAI,WAAW,GAAG,uEAAuE,CAAC;IAC1F,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;QACxB,WAAW,IAAI;;mFAEgE,CAAC,CAAC,EAAE,KAAK,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;kBAC1H,CAAC,CAAC,IAAI;qBACH,CAAC;IACpB,CAAC,CAAC,CAAC;IACH,WAAW,IAAI,QAAQ,CAAC;IACxB,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,yBAAyB,CAAgB,CAAC;IAClF,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CAAC,kBAAkB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QACtD,kCAAkC;QAClC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,EAAO,EAAE,EAAE;YAC/D,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAM,EAAE,EAAE;gBACvC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;gBACrD,IAAI,UAAU,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;gBACvC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACrB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC;gBACpD,CAAC;gBAED,kDAAkD;gBAClD,IAAK,MAAc,CAAC,gBAAgB,EAAE,CAAC;oBACrC,MAAM,MAAM,GAAI,MAAc,CAAC,gBAAgB,EAAE,CAAC;oBAClD,MAAM,CAAC,WAAW,CAAC;wBACjB,OAAO,EAAE,eAAe;wBACxB,OAAO,EAAE,KAAK;wBACd,GAAG,EAAE,kBAAkB;wBACvB,KAAK,EAAE,UAAU;qBAClB,CAAC,CAAC;gBACL,CAAC;gBAED,gDAAgD;gBAChD,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;oBAC5C,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,uBAAuB;IACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;IAC9D,IAAI,MAAM;QAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,sBAAsB,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IAC/E,gBAAgB;IAChB,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;IACpE,IAAI,SAAS;QAAE,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAC3E,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;IAChE,IAAI,OAAO;QAAE,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,0BAA0B,CAAC,SAAsB;IACxD,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,yBAAyB,CAAgB,CAAC;IAClF,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnD,OAAO,CAAC,SAAS,GAAG,6DAA6D,CAAC;QAClF,OAAO;IACT,CAAC;IACD,IAAI,IAAI,GAAG;;;0BAGa,YAAY,CAAC,QAAQ;qBAC1B,YAAY,CAAC,WAAW;;;;;;yBAMpB,YAAY,CAAC,MAAM,CAAC,UAAU;oBACnC,YAAY,CAAC,MAAM,CAAC,KAAK;qBACxB,YAAY,CAAC,MAAM,CAAC,MAAM;4BACnB,YAAY,CAAC,MAAM,CAAC,YAAY;;;;aAI/C,CAAC;IACZ,IAAI,IAAI,uCAAuC;QACzC,oFAAoF;QACpF,sBAAsB,CAAC;IAC7B,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;QACjC,IAAI,IAAI;kBACM,EAAE,CAAC,QAAQ,IAAI,EAAE;kBACjB,EAAE,CAAC,OAAO,IAAI,EAAE;kBAChB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;kBAC3B,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;;mDAEQ,GAAG;qDACD,GAAG;;cAE1C,CAAC;IACb,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,kBAAkB,CAAC;IAC3B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IACzB,qCAAqC;IACrC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,sBAAsB,CAAC,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACtE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,mBAAmB,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAC,SAAsB,EAAE,EAA0B,EAAE,GAAkB;IACpG,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;IAC5D,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;IACjE,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAA4B,CAAC;IAClG,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAA4B,CAAC;IAChG,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAA4B,CAAC;IAC9F,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAA4B,CAAC;IAChG,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,IAAI,KAAK;QAAE,KAAK,CAAC,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,qBAAqB,CAAC;IACxF,IAAI,aAAa;QAAE,aAAa,CAAC,KAAK,GAAG,EAAE,EAAE,QAAQ,IAAI,EAAE,CAAC;IAC5D,IAAI,YAAY;QAAE,YAAY,CAAC,KAAK,GAAG,EAAE,EAAE,OAAO,IAAI,EAAE,CAAC;IACzD,IAAI,WAAW;QAAE,WAAW,CAAC,KAAK,GAAG,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC;IACtD,IAAI,YAAY;QAAE,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC;IACvD,uBAAuB,GAAG,GAAG,CAAC;AAChC,CAAC;AAED,SAAS,sBAAsB,CAAC,SAAsB;IACpD,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;IAC5D,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,uCAAuC;IACvC,MAAM,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;IAC/D,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACxC,uBAAuB,GAAG,IAAI,CAAC;AACjC,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAsB,EAAE,QAAa;IAC9D,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAA4B,CAAC;IAClG,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAA4B,CAAC;IAChG,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAA4B,CAAC;IAC9F,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAA4B,CAAC;IAChG,MAAM,EAAE,GAAkB;QACxB,QAAQ,EAAE,aAAa,EAAE,KAAK,IAAI,EAAE;QACpC,OAAO,EAAE,YAAY,EAAE,KAAK,IAAI,EAAE;QAClC,MAAM,EAAE,WAAW,EAAE,KAAK,IAAI,EAAE;QAChC,OAAO,EAAE,YAAY,EAAE,OAAO,IAAI,KAAK;KACxC,CAAC;IACF,IAAI,uBAAuB,IAAI,IAAI,EAAE,CAAC;QACpC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;SAAM,CAAC;QACN,cAAc,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC;IAC/C,CAAC;IACD,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC;IACzC,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAClC,0BAA0B,CAAC,SAAS,CAAC,CAAC;AACxC,CAAC;AAED,+CAAgD;AAEhD,SAAS,mBAAmB,CAAC,SAAsB,EAAE,GAAW;IAC9D,MAAM,EAAE,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC9E,IAAA,iBAAS,EAAC;QACR,KAAK,EAAE,uBAAuB;QAC9B,OAAO,EAAE,uDAAuD,OAAO,2BAA2B;QAClG,SAAS,EAAE,GAAG,EAAE;YACd,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC9B,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC;YACzC,0BAA0B,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;KACF,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Providers & Models section logic and rendering\n\n// Utility to ensure parseInt always gets a string\nfunction safeGetString(val: string | null | undefined): string {\n  return typeof val === 'string' ? val : '';\n}\n\n// Handles CRUD UI and interactions for providers and models\n\nexport type ProviderModel = {\n    provider: string;\n    modelId: string;\n    apiKey: string;\n    enabled: boolean;\n};\n\nimport { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';\n\nlet providerModels: ProviderModel[] = [];\nlet editingProviderModelIdx: number | null = null;\nconst sectionTheme: UIThemeConfig['section'] = defaultUIThemeConfig.section;\n\n// List of all known providers (should match those in llmService)\nconst ALL_PROVIDERS = [\n  { id: 'ollama', name: 'Olla<PERSON>' },\n  { id: 'openai', name: 'OpenA<PERSON>' },\n  { id: 'anthropic', name: 'Anthropic' },\n  { id: 'lmstudio', name: 'LM Studio' },\n  { id: 'googleai', name: 'Google AI' },\n  { id: 'mistral<PERSON>', name: 'Mistral AI' },\n  { id: 'cohere', name: 'Cohere' },\n  { id: 'deepseek', name: 'DeepSeek' },\n  { id: 'openrouter', name: 'OpenRouter' },\n  { id: 'huggingface', name: 'HuggingFace' },\n  { id: 'starcoder', name: 'StarCoder' },\n  { id: 'codellama', name: 'CodeLlama' },\n  { id: 'replit', name: 'Replit' },\n  { id: 'wizardcoder', name: 'WizardCoder' },\n  { id: 'xwincoder', name: 'XwinCoder' },\n  { id: 'phi', name: 'Phi' },\n  { id: 'yicode', name: 'YiCode' },\n  { id: 'codegemma', name: 'CodeGemma' },\n  { id: 'santacoder', name: 'SantaCoder' },\n  { id: 'stablecode', name: 'StableCode' },\n  { id: 'perplexity', name: 'Perplexity' },\n];\n\nexport function renderProvidersModelsSettingsSection(container: HTMLElement, settings: any) {\n  // Sync from settings\n  providerModels = Array.isArray(settings.providerModels) ? settings.providerModels : [];\n  renderProvidersModelsTable(container);\n\n  // --- Provider toggles UI ---\n  const enabledProviders = Array.isArray(settings.enabledProviders) ? settings.enabledProviders : ['ollama'];\n  let togglesHtml = '<div style=\"margin-bottom:1em;\"><h3>Enable/Disable LLM Providers</h3>';\n  ALL_PROVIDERS.forEach(p => {\n    togglesHtml += `\n            <label style=\"display:block;margin-bottom:4px;\">\n                <input type=\"checkbox\" class=\"provider-toggle\" data-provider-id=\"${p.id}\" ${enabledProviders.includes(p.id) ? 'checked' : ''}>\n                ${p.name}\n            </label>`;\n  });\n  togglesHtml += '</div>';\n  const section = container.querySelector('#providersModelsSection') as HTMLElement;\n  if (section) {\n    section.insertAdjacentHTML('afterbegin', togglesHtml);\n    // Add event listeners for toggles\n    section.querySelectorAll('.provider-toggle').forEach((el: any) => {\n      el.addEventListener('change', (e: any) => {\n        const id = e.target.getAttribute('data-provider-id');\n        let newEnabled = [...enabledProviders];\n        if (e.target.checked) {\n          if (!newEnabled.includes(id)) newEnabled.push(id);\n        } else {\n          newEnabled = newEnabled.filter(pid => pid !== id);\n        }\n        \n        // Send updateSetting message to VS Code extension\n        if ((window as any).acquireVsCodeApi) {\n          const vscode = (window as any).acquireVsCodeApi();\n          vscode.postMessage({\n            command: 'updateSetting',\n            section: 'llm',\n            key: 'enabledProviders',\n            value: newEnabled\n          });\n        }\n        \n        // Also call onChange for backward compatibility\n        if (typeof settings.onChange === 'function') {\n          settings.onChange('enabledProviders', newEnabled);\n        }\n      });\n    });\n  }\n\n  // Add button listeners\n  const addBtn = document.getElementById('addProviderModelBtn');\n  if (addBtn) addBtn.onclick = () => showProviderModelModal(container, {}, null);\n  // Modal buttons\n  const cancelBtn = document.getElementById('cancelProviderModelBtn');\n  if (cancelBtn) cancelBtn.onclick = () => hideProviderModelModal(container);\n  const saveBtn = document.getElementById('saveProviderModelBtn');\n  if (saveBtn) saveBtn.onclick = () => saveProviderModel(container, settings);\n}\n\nfunction renderProvidersModelsTable(container: HTMLElement) {\n  const section = container.querySelector('#providersModelsSection') as HTMLElement;\n  if (!providerModels || providerModels.length === 0) {\n    section.innerHTML = '<div style=\"color:#aaa;\">No providers/models defined.</div>';\n    return;\n  }\n  let html = `<style>\n        .crud-table th, .crud-table td { padding: 6px 10px; }\n        .crud-table th {\n            background: ${sectionTheme.headerBg};\n            color: ${sectionTheme.headerColor};\n            font-weight: 600;\n        }\n        .crud-table tbody tr:nth-child(even) { background: #fafbfc; }\n        .crud-table tbody tr:hover { background: #e8f0fe; }\n        .btn-provider-model {\n            background:${sectionTheme.button.background};\n            color:${sectionTheme.button.color};\n            border:${sectionTheme.button.border};\n            border-radius:${sectionTheme.button.borderRadius};\n            padding:3px 10px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s;\n        }\n        .btn-provider-model:hover { background:#1d4ed8; }\n    </style>`;\n  html += '<table class=\"crud-table\"><thead><tr>' +\n        '<th>Provider</th><th>Model ID</th><th>API Key</th><th>Enabled</th><th>Actions</th>' +\n        '</tr></thead><tbody>';\n  providerModels.forEach((pm, idx) => {\n    html += `<tr>\n            <td>${pm.provider || ''}</td>\n            <td>${pm.modelId || ''}</td>\n            <td>${pm.apiKey ? '••••••••' : ''}</td>\n            <td>${pm.enabled ? 'Yes' : 'No'}</td>\n            <td>\n                <button type=\"button\" data-edit=\"${idx}\">Edit</button>\n                <button type=\"button\" data-delete=\"${idx}\">Delete</button>\n            </td>\n        </tr>`;\n  });\n  html += '</tbody></table>';\n  section.innerHTML = html;\n  // Attach edit/delete event listeners\n  section.querySelectorAll('button[data-edit]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-edit');\n      const idx = parseInt(safeGetString(idxAttr));\n      showProviderModelModal(container, providerModels[idx], idx);\n    });\n  });\n  section.querySelectorAll('button[data-delete]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-delete');\n      const idx = parseInt(safeGetString(idxAttr));\n      deleteProviderModel(container, idx);\n    });\n  });\n}\n\nfunction showProviderModelModal(container: HTMLElement, pm: Partial<ProviderModel>, idx: number | null) {\n  const modal = document.getElementById('providerModelModal');\n  const title = document.getElementById('providerModelModalTitle');\n  const providerInput = document.getElementById('providerModelProvider') as HTMLInputElement | null;\n  const modelIdInput = document.getElementById('providerModelModelId') as HTMLInputElement | null;\n  const apiKeyInput = document.getElementById('providerModelApiKey') as HTMLInputElement | null;\n  const enabledInput = document.getElementById('providerModelEnabled') as HTMLInputElement | null;\n  if (modal) modal.style.display = 'flex';\n  if (title) title.innerText = idx == null ? 'Add Provider/Model' : 'Edit Provider/Model';\n  if (providerInput) providerInput.value = pm?.provider || '';\n  if (modelIdInput) modelIdInput.value = pm?.modelId || '';\n  if (apiKeyInput) apiKeyInput.value = pm?.apiKey || '';\n  if (enabledInput) enabledInput.checked = !!pm?.enabled;\n  editingProviderModelIdx = idx;\n}\n\nfunction hideProviderModelModal(container: HTMLElement) {\n  const modal = document.getElementById('providerModelModal');\n  if (modal) modal.style.display = 'none';\n  // Clear validation errors in container\n  const errors = container.querySelectorAll('.validation-error');\n  errors.forEach(error => error.remove());\n  editingProviderModelIdx = null;\n}\n\nfunction saveProviderModel(container: HTMLElement, settings: any) {\n  const providerInput = document.getElementById('providerModelProvider') as HTMLInputElement | null;\n  const modelIdInput = document.getElementById('providerModelModelId') as HTMLInputElement | null;\n  const apiKeyInput = document.getElementById('providerModelApiKey') as HTMLInputElement | null;\n  const enabledInput = document.getElementById('providerModelEnabled') as HTMLInputElement | null;\n  const pm: ProviderModel = {\n    provider: providerInput?.value || '',\n    modelId: modelIdInput?.value || '',\n    apiKey: apiKeyInput?.value || '',\n    enabled: enabledInput?.checked || false,\n  };\n  if (editingProviderModelIdx == null) {\n    providerModels.push(pm);\n  } else {\n    providerModels[editingProviderModelIdx] = pm;\n  }\n  settings.providerModels = providerModels;\n  hideProviderModelModal(container);\n  renderProvidersModelsTable(container);\n}\n\nimport { showModal } from '../components/modal';\n\nfunction deleteProviderModel(container: HTMLElement, idx: number) {\n  const pm = providerModels[idx];\n  const pmLabel = pm ? `${pm.provider} - ${pm.modelId}` : 'this provider/model';\n  showModal({\n    title: 'Delete Provider/Model',\n    content: `Are you sure you want to remove the provider/model \"${pmLabel}\"? This cannot be undone.`,\n    onConfirm: () => {\n      providerModels.splice(idx, 1);\n      const settings = (window as any).settings || {};\n      settings.providerModels = providerModels;\n      renderProvidersModelsTable(container);\n    }\n  });\n}\n"]}