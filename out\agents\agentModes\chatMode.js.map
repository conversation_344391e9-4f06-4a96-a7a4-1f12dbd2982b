{"version": 3, "file": "chatMode.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/chatMode.ts"], "names": [], "mappings": ";;;AACA,mDAA4E;AAG5E,yCAAsC;AAEtC,+DAA4D;AAE5D;;GAEG;AACH,MAAa,QAAS,SAAQ,6BAAa;IAChC,EAAE,GAAG,MAAM,CAAC;IACZ,WAAW,GAAG,MAAM,CAAC;IACrB,WAAW,GAAG,oCAAoC,CAAC;IACnD,IAAI,GAAG,YAAY,CAAC;IACpB,kBAAkB,GAAG,2BAAW,CAAC,IAAI,CAAC;IACtC,yBAAyB,GAAG,KAAK,CAAC;IAClC,sBAAsB,GAAG,KAAK,CAAC;IAExC;;SAEK;IACL,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,KAAY;IACZ,mFAAmF;IACnF,cAA6B,EAC7B,gBAAyF;QAEzF,IAAI,CAAC;YACH,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC;YAEpE,qBAAqB;YACrB,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxE,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,aAAa,GAAG,WAAW,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;wBACtE,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,gBAAgB,CAAC,MAAM,oCAAoC,CAAC,CAAC;oBAC9F,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,EAAE,WAAW,CAAC,CAAC;gBACjF,kCAAkC;YACpC,CAAC;YAED,mDAAmD;YACnD,MAAM,eAAe,GAAG,6BAAa,CAAC,YAAY,CAAC,WAAW,EAAE;gBAC9D,cAAc,EAAE,EAAE;gBAClB,aAAa;gBACb,OAAO;aACR,CAAC,CAAC;YAEH,yCAAyC;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,CACnC,eAAe,EACf,SAAS,EACT,gBAAgB,EAAE,iBAAiC,CACpD,CAAC;YAEF,mCAAmC;YACnC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBAC9C,MAAM,WAAW,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,EAAE,WAAW,CAAC,CAAC;gBAC7E,qCAAqC;YACvC,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uCAAuC;YACvC,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,CAAC;gBACjE,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;gBACvE,OAAO,8BAA8B,CAAC;YACxC,CAAC;YAED,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QACpG,CAAC;IACH,CAAC;IAED;;SAEK;IACL,YAAY;QACV,OAAO;YACL,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;YACf,IAAI,EAAE,MAAM;SACb,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,eAAe;IACnB,mFAAmF;IACnF,MAAa;IACb,mFAAmF;IACnF,cAA6B;QAE7B,OAAO,6BAAa,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;SAEK;IACL,eAAe;QAKb,OAAO;YACL,YAAY,EAAE;;;;;CAKnB;SACI,CAAC;IACJ,CAAC;CACF;AAxHD,4BAwHC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { OperationMode, ContextSource, ContextType } from './operationMode';\nimport { Agent } from '../agentUtilities/agent';\nimport { LLMGenerateParams } from '../../llm/types';\nimport { Logger } from '../../logger';\nimport { AgentContext } from '../../types/agent';\nimport { promptManager } from '../../prompts/promptManager';\n\n/**\n * Chat Mode - General chat with no specific context\n */\nexport class ChatMode extends OperationMode {\n  readonly id = 'chat';\n  readonly displayName = 'Chat';\n  readonly description = 'General chat with the AI assistant';\n  readonly icon = '$(comment)';\n  readonly defaultContextType = ContextType.NONE;\n  readonly requiresHumanVerification = false;\n  readonly supportsMultipleAgents = false;\n\n  /**\n     * Process a user message in Chat mode\n     */\n  async processMessage(\n    message: string,\n    agent: Agent,\n    // @ts-ignore - Parameter required by interface but not used in this implementation\n    _contextSource: ContextSource,\n    additionalParams?: Record<string, any> & { cancellationToken?: vscode.CancellationToken }\n  ): Promise<string> {\n    try {\n      Logger.instance.info(`Processing message in Chat mode: ${message}`);\n\n      // Get memory context\n      let memoryContext = '';\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          const relevantMemories = await agentMemory.getRelevantMemories(message);\n          if (relevantMemories && relevantMemories.length > 0) {\n            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);\n            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to chat context`);\n          }\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to retrieve memory context for chat:', memoryError);\n        // Continue without memory context\n      }\n\n      // Use promptManager to create the enhanced message\n      const enhancedMessage = promptManager.renderPrompt('mode.chat', {\n        contextContent: '',\n        memoryContext,\n        message\n      });\n\n      // Pass the enhanced message to the agent\n      const llmParams = this.getLLMParams();\n      const response = await agent.generate(\n        enhancedMessage,\n        llmParams,\n        additionalParams?.cancellationToken as AgentContext\n      );\n\n      // Store the conversation in memory\n      try {\n        const agentMemory = agent.getMemory();\n        if (agentMemory) {\n          await agentMemory.addMessage('user', message);\n          await agentMemory.addMessage('assistant', response);\n        }\n      } catch (memoryError) {\n        Logger.instance.warn('Failed to store conversation in memory:', memoryError);\n        // Continue without storing in memory\n      }\n\n      return response;\n    } catch (error) {\n      // Check if the operation was cancelled\n      if (additionalParams?.cancellationToken?.isCancellationRequested) {\n        Logger.instance.info('Chat mode message processing cancelled by user');\n        return 'Operation cancelled by user.';\n      }\n\n      Logger.instance.error('Error processing message in Chat mode:', error);\n      return `Error processing your message: ${error instanceof Error ? error.message : String(error)}`;\n    }\n  }\n\n  /**\n     * Get LLM parameters specific to Chat mode\n     */\n  getLLMParams(): LLMGenerateParams {\n    return {\n      prompt: '',\n      modelId: '',\n      temperature: 0.7,\n      maxTokens: 2000,\n      mode: 'chat'\n    };\n  }\n\n  /**\n     * Get the system prompt for Chat mode\n     */\n  async getSystemPrompt(\n    // @ts-ignore - Parameter required by interface but not used in this implementation\n    _agent: Agent,\n    // @ts-ignore - Parameter required by interface but not used in this implementation\n    _contextSource: ContextSource\n  ): Promise<string> {\n    return promptManager.renderPrompt('mode.chat', {});\n  }\n\n  /**\n     * Get UI components specific to Chat mode\n     */\n  getUIComponents(): {\n    controlPanel?: string;\n    contextPanel?: string;\n    messageInput?: string;\n  } {\n    return {\n      messageInput: `\n<div class=\"message-input-container\">\n    <textarea id=\"message-input\" placeholder=\"Chat with the AI assistant...\"></textarea>\n    <button id=\"btn-send\" title=\"Send\"><i class=\"codicon codicon-send\"></i></button>\n</div>\n`\n    };\n  }\n}\n"]}