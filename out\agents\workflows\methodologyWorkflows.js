"use strict";
/**
 * Codessa Methodology Workflows
 *
 * This module provides workflow templates for different development methodologies:
 * - Agile
 * - Scrum
 * - XP (Extreme Programming)
 * - Waterfall
 * - DevOps
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAgileWorkflow = createAgileWorkflow;
exports.createScrumWorkflow = createScrumWorkflow;
exports.createXPWorkflow = createXPWorkflow;
exports.createWaterfallWorkflow = createWaterfallWorkflow;
exports.createDevOpsWorkflow = createDevOpsWorkflow;
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
/**
 * Create an Agile workflow for iterative development
 */
function createAgileWorkflow(id, name, description, productOwnerAgent, developerAgent, qaAgent, tools = []) {
    logger_1.logger.info(`Creating Agile workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const userStoryCreationNode = graph_1.Codessa.createAgentNode('user-story-creation', 'User Story Creation', productOwnerAgent);
    const backlogRefinementNode = graph_1.Codessa.createAgentNode('backlog-refinement', 'Backlog Refinement', productOwnerAgent);
    const iterationPlanningNode = graph_1.Codessa.createAgentNode('iteration-planning', 'Iteration Planning', productOwnerAgent);
    const developmentNode = graph_1.Codessa.createAgentNode('development', 'Development', developerAgent);
    const testingNode = graph_1.Codessa.createAgentNode('testing', 'Testing', qaAgent);
    const reviewNode = graph_1.Codessa.createAgentNode('review', 'Review', developerAgent);
    const iterationReviewNode = graph_1.Codessa.createAgentNode('iteration-review', 'Iteration Review', productOwnerAgent);
    const retrospectiveNode = graph_1.Codessa.createAgentNode('retrospective', 'Retrospective', productOwnerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-user-story', source: 'input', target: 'user-story-creation', type: 'default' },
        { name: 'user-story-to-backlog', source: 'user-story-creation', target: 'backlog-refinement', type: 'default' },
        { name: 'backlog-to-planning', source: 'backlog-refinement', target: 'iteration-planning', type: 'default' },
        { name: 'planning-to-development', source: 'iteration-planning', target: 'development', type: 'default' },
        { name: 'development-to-testing', source: 'development', target: 'testing', type: 'default' },
        { name: 'testing-to-review', source: 'testing', target: 'review', type: 'default' },
        { name: 'review-to-iteration-review', source: 'review', target: 'iteration-review', type: 'default' },
        { name: 'iteration-review-to-retrospective', source: 'iteration-review', target: 'retrospective', type: 'default' },
        { name: 'retrospective-to-output', source: 'retrospective', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'testing-to-development', source: 'testing', target: 'development', type: 'feedback' },
        { name: 'retrospective-to-backlog', source: 'retrospective', target: 'backlog-refinement', type: 'feedback' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            userStoryCreationNode,
            backlogRefinementNode,
            iterationPlanningNode,
            developmentNode,
            testingNode,
            reviewNode,
            iterationReviewNode,
            retrospectiveNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        methodology: 'agile'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a Scrum workflow for sprint-based development
 */
function createScrumWorkflow(id, name, description, productOwnerAgent, scrumMasterAgent, developerAgent, qaAgent, tools = []) {
    logger_1.logger.info(`Creating Scrum workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const productBacklogNode = graph_1.Codessa.createAgentNode('product-backlog', 'Product Backlog', productOwnerAgent);
    const sprintPlanningNode = graph_1.Codessa.createAgentNode('sprint-planning', 'Sprint Planning', scrumMasterAgent);
    const dailyScrumNode = graph_1.Codessa.createAgentNode('daily-scrum', 'Daily Scrum', scrumMasterAgent);
    const developmentNode = graph_1.Codessa.createAgentNode('development', 'Development', developerAgent);
    const testingNode = graph_1.Codessa.createAgentNode('testing', 'Testing', qaAgent);
    const sprintReviewNode = graph_1.Codessa.createAgentNode('sprint-review', 'Sprint Review', productOwnerAgent);
    const sprintRetrospectiveNode = graph_1.Codessa.createAgentNode('sprint-retrospective', 'Sprint Retrospective', scrumMasterAgent);
    const backlogRefinementNode = graph_1.Codessa.createAgentNode('backlog-refinement', 'Backlog Refinement', productOwnerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-product-backlog', source: 'input', target: 'product-backlog', type: 'default' },
        { name: 'product-backlog-to-sprint-planning', source: 'product-backlog', target: 'sprint-planning', type: 'default' },
        { name: 'sprint-planning-to-daily-scrum', source: 'sprint-planning', target: 'daily-scrum', type: 'default' },
        { name: 'daily-scrum-to-development', source: 'daily-scrum', target: 'development', type: 'default' },
        { name: 'development-to-testing', source: 'development', target: 'testing', type: 'default' },
        { name: 'testing-to-sprint-review', source: 'testing', target: 'sprint-review', type: 'default' },
        { name: 'sprint-review-to-sprint-retrospective', source: 'sprint-review', target: 'sprint-retrospective', type: 'default' },
        { name: 'sprint-retrospective-to-backlog-refinement', source: 'sprint-retrospective', target: 'backlog-refinement', type: 'default' },
        { name: 'backlog-refinement-to-output', source: 'backlog-refinement', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'daily-scrum-to-daily-scrum', source: 'daily-scrum', target: 'daily-scrum', type: 'feedback' },
        { name: 'testing-to-development', source: 'testing', target: 'development', type: 'feedback' },
        { name: 'backlog-refinement-to-product-backlog', source: 'backlog-refinement', target: 'product-backlog', type: 'feedback' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            productBacklogNode,
            sprintPlanningNode,
            dailyScrumNode,
            developmentNode,
            testingNode,
            sprintReviewNode,
            sprintRetrospectiveNode,
            backlogRefinementNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        methodology: 'scrum'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create an XP (Extreme Programming) workflow
 */
function createXPWorkflow(id, name, description, customerAgent, developerAgent, pairProgrammerAgent, tools = []) {
    logger_1.logger.info(`Creating XP workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const userStoryNode = graph_1.Codessa.createAgentNode('user-story', 'User Story', customerAgent);
    const planningGameNode = graph_1.Codessa.createAgentNode('planning-game', 'Planning Game', customerAgent);
    const testFirstNode = graph_1.Codessa.createAgentNode('test-first', 'Test-First Development', developerAgent);
    const pairProgrammingNode = graph_1.Codessa.createAgentNode('pair-programming', 'Pair Programming', pairProgrammerAgent);
    const continuousIntegrationNode = graph_1.Codessa.createAgentNode('continuous-integration', 'Continuous Integration', developerAgent);
    const refactoringNode = graph_1.Codessa.createAgentNode('refactoring', 'Refactoring', developerAgent);
    const smallReleaseNode = graph_1.Codessa.createAgentNode('small-release', 'Small Release', developerAgent);
    const customerFeedbackNode = graph_1.Codessa.createAgentNode('customer-feedback', 'Customer Feedback', customerAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-user-story', source: 'input', target: 'user-story', type: 'default' },
        { name: 'user-story-to-planning-game', source: 'user-story', target: 'planning-game', type: 'default' },
        { name: 'planning-game-to-test-first', source: 'planning-game', target: 'test-first', type: 'default' },
        { name: 'test-first-to-pair-programming', source: 'test-first', target: 'pair-programming', type: 'default' },
        { name: 'pair-programming-to-continuous-integration', source: 'pair-programming', target: 'continuous-integration', type: 'default' },
        { name: 'continuous-integration-to-refactoring', source: 'continuous-integration', target: 'refactoring', type: 'default' },
        { name: 'refactoring-to-small-release', source: 'refactoring', target: 'small-release', type: 'default' },
        { name: 'small-release-to-customer-feedback', source: 'small-release', target: 'customer-feedback', type: 'default' },
        { name: 'customer-feedback-to-output', source: 'customer-feedback', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'refactoring-to-test-first', source: 'refactoring', target: 'test-first', type: 'feedback' },
        { name: 'customer-feedback-to-user-story', source: 'customer-feedback', target: 'user-story', type: 'feedback' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            userStoryNode,
            planningGameNode,
            testFirstNode,
            pairProgrammingNode,
            continuousIntegrationNode,
            refactoringNode,
            smallReleaseNode,
            customerFeedbackNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        methodology: 'xp'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a Waterfall workflow for sequential development
 */
function createWaterfallWorkflow(id, name, description, analystAgent, designerAgent, developerAgent, testerAgent, maintenanceAgent, tools = []) {
    logger_1.logger.info(`Creating Waterfall workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const requirementsNode = graph_1.Codessa.createAgentNode('requirements', 'Requirements Analysis', analystAgent);
    const designNode = graph_1.Codessa.createAgentNode('design', 'System Design', designerAgent);
    const implementationNode = graph_1.Codessa.createAgentNode('implementation', 'Implementation', developerAgent);
    const testingNode = graph_1.Codessa.createAgentNode('testing', 'Testing', testerAgent);
    const deploymentNode = graph_1.Codessa.createAgentNode('deployment', 'Deployment', developerAgent);
    const maintenanceNode = graph_1.Codessa.createAgentNode('maintenance', 'Maintenance', maintenanceAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-requirements', source: 'input', target: 'requirements', type: 'default' },
        { name: 'requirements-to-design', source: 'requirements', target: 'design', type: 'default' },
        { name: 'design-to-implementation', source: 'design', target: 'implementation', type: 'default' },
        { name: 'implementation-to-testing', source: 'implementation', target: 'testing', type: 'default' },
        { name: 'testing-to-deployment', source: 'testing', target: 'deployment', type: 'default' },
        { name: 'deployment-to-maintenance', source: 'deployment', target: 'maintenance', type: 'default' },
        { name: 'maintenance-to-output', source: 'maintenance', target: 'output', type: 'default' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            requirementsNode,
            designNode,
            implementationNode,
            testingNode,
            deploymentNode,
            maintenanceNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        methodology: 'waterfall'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a DevOps workflow for continuous integration and delivery
 */
function createDevOpsWorkflow(id, name, description, developerAgent, devOpsAgent, qaAgent, tools = []) {
    logger_1.logger.info(`Creating DevOps workflow: ${name}`);
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const planningNode = graph_1.Codessa.createAgentNode('planning', 'Planning', developerAgent);
    const codingNode = graph_1.Codessa.createAgentNode('coding', 'Coding', developerAgent);
    const buildNode = graph_1.Codessa.createAgentNode('build', 'Build', devOpsAgent);
    const testingNode = graph_1.Codessa.createAgentNode('testing', 'Testing', qaAgent);
    const releaseNode = graph_1.Codessa.createAgentNode('release', 'Release', devOpsAgent);
    const deploymentNode = graph_1.Codessa.createAgentNode('deployment', 'Deployment', devOpsAgent);
    const operationNode = graph_1.Codessa.createAgentNode('operation', 'Operation', devOpsAgent);
    const monitoringNode = graph_1.Codessa.createAgentNode('monitoring', 'Monitoring', devOpsAgent);
    const feedbackNode = graph_1.Codessa.createAgentNode('feedback', 'Feedback', qaAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Add tool nodes if tools are provided
    const toolNodes = tools.map((tool, index) => graph_1.Codessa.createToolNode(`tool-${index}`, `Tool: ${tool.name}`, tool));
    // Create edges
    const edges = [
        { name: 'input-to-planning', source: 'input', target: 'planning', type: 'default' },
        { name: 'planning-to-coding', source: 'planning', target: 'coding', type: 'default' },
        { name: 'coding-to-build', source: 'coding', target: 'build', type: 'default' },
        { name: 'build-to-testing', source: 'build', target: 'testing', type: 'default' },
        { name: 'testing-to-release', source: 'testing', target: 'release', type: 'default' },
        { name: 'release-to-deployment', source: 'release', target: 'deployment', type: 'default' },
        { name: 'deployment-to-operation', source: 'deployment', target: 'operation', type: 'default' },
        { name: 'operation-to-monitoring', source: 'operation', target: 'monitoring', type: 'default' },
        { name: 'monitoring-to-feedback', source: 'monitoring', target: 'feedback', type: 'default' },
        { name: 'feedback-to-output', source: 'feedback', target: 'output', type: 'default' },
        // Feedback loops
        { name: 'feedback-to-planning', source: 'feedback', target: 'planning', type: 'feedback' },
        { name: 'testing-to-coding', source: 'testing', target: 'coding', type: 'feedback' },
        { name: 'monitoring-to-operation', source: 'monitoring', target: 'operation', type: 'feedback' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            planningNode,
            codingNode,
            buildNode,
            testingNode,
            releaseNode,
            deploymentNode,
            operationNode,
            monitoringNode,
            feedbackNode,
            outputNode,
            ...toolNodes
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic',
        methodology: 'devops',
        agentNodes: [
            planningNode,
            codingNode,
            buildNode,
            testingNode,
            releaseNode,
            deploymentNode,
            operationNode,
            monitoringNode,
            feedbackNode
        ],
        toolNodes: toolNodes
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=methodologyWorkflows.js.map