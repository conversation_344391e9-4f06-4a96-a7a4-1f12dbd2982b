import { performance, PerformanceObserver } from 'perf_hooks';

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private enabled: boolean = true;
  private observer: PerformanceObserver | null = null;

  private constructor() {
    this.setupPerformanceObserver();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private setupPerformanceObserver() {
    this.observer = new PerformanceObserver((items) => {
      items.getEntries().forEach((entry) => {
        this.metrics.push({
          name: entry.name,
          duration: entry.duration,
          timestamp: performance.now(),
        });
      });
    });
    
    this.observer.observe({ entryTypes: ['measure'] });
  }

  public measure<T>(name: string, fn: () => T): T {
    if (!this.enabled) return fn();

    const startMark = `${name}-start`;
    const endMark = `${name}-end`;
    
    performance.mark(startMark);
    const result = fn();
    
    if (result instanceof Promise) {
      return result.finally(() => {
        performance.mark(endMark);
        performance.measure(name, startMark, endMark);
        performance.clearMarks(startMark);
        performance.clearMarks(endMark);
      }) as unknown as T;
    } else {
      performance.mark(endMark);
      performance.measure(name, startMark, endMark);
      performance.clearMarks(startMark);
      performance.clearMarks(endMark);
      return result;
    }
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  public clearMetrics(): void {
    this.metrics = [];
  }

  public enable(): void {
    this.enabled = true;
  }

  public disable(): void {
    this.enabled = false;
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();

// Decorator for measuring method execution time
export function measurePerformance(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) {
  const originalMethod = descriptor.value;
  
  descriptor.value = function(...args: any[]) {
    return performanceMonitor.measure(
      `${target.constructor.name}.${propertyKey}`,
      () => originalMethod.apply(this, args)
    );
  };
  
  return descriptor;
}

// Utility function to measure async operations
export async function measureAsync<T>(
  name: string,
  operation: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const start = performance.now();
  
  try {
    const result = await operation();
    const duration = performance.now() - start;
    
    performanceMonitor['metrics'].push({
      name,
      duration,
      timestamp: Date.now(),
      metadata
    });
    
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    
    performanceMonitor['metrics'].push({
      name: `${name}-error`,
      duration,
      timestamp: Date.now(),
      metadata: {
        ...metadata,
        error: error instanceof Error ? error.message : String(error)
      }
    });
    
    throw error;
  }
}
