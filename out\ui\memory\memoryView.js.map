{"version": 3, "file": "memoryView.js", "sourceRoot": "", "sources": ["../../../src/ui/memory/memoryView.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,yCAAsC;AACtC,uCAAuC;AACvC,8DAA2D;AAG3D;;;GAGG;AACH,MAAa,UAAU;IACd,MAAM,CAAU,QAAQ,GAAG,oBAAoB,CAAC;IAC/C,KAAK,CAAkC;IACvC,OAAO,CAA0B;IACjC,WAAW,GAAwB,EAAE,CAAC;IAE9C,YAAY,OAAgC;QAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,IAAI;QACf,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,eAAe;QACf,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC3C,UAAU,CAAC,QAAQ,EACnB,gBAAgB,EAChB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACE,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC;aACxD;SACF,CACF,CAAC;QAEF,mBAAmB;QACnB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE3E,mCAAmC;QACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CACpC,KAAK,EAAE,OAAO,EAAE,EAAE;YAChB,IAAI,CAAC;gBACH,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC1B,KAAK,aAAa;wBAChB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC/B,MAAM;oBACR,KAAK,gBAAgB;wBACnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;wBAChE,MAAM;oBACR,KAAK,cAAc;wBACjB,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;wBAC1C,MAAM;oBACR,KAAK,eAAe;wBAClB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBACjC,MAAM;oBACR,KAAK,mBAAmB;wBACtB,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;wBACrC,MAAM;oBACR,KAAK,sBAAsB;wBACzB,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;wBACxD,MAAM;oBACR,KAAK,WAAW;wBACd,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;wBAC7B,MAAM;oBACR,KAAK,gBAAgB;wBACnB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAClC,MAAM;gBACR,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBACpE,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;oBAC9B,OAAO,EAAE,OAAO;oBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE,CAAC,CAAC;YACL,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW,CACjB,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,KAAK,CAAC,YAAY,CACrB,GAAG,EAAE;YACH,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YAEvB,6BAA6B;YAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;gBAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;gBAC1C,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,OAAO,EAAE,CAAC;gBACvB,CAAC;YACH,CAAC;QACH,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW,CACjB,CAAC;QAEF,kCAAkC;QAClC,6BAAa,CAAC,iBAAiB,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,6BAAa,CAAC,WAAW,EAAE,CAAC;YAEnD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,UAAU;gBACnB,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB,CAAC,KAAa,EAAE,UAAwC,EAAE;QAC1F,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,6BAAa,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAE3E,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,eAAe;gBACxB,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,kBAAkB,CAAC,EAAU;QACzC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,6BAAa,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAErD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,eAAe;gBACxB,EAAE;gBACF,OAAO;aACR,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,6BAAa,CAAC,aAAa,EAAE,CAAC;YAEpC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,iBAAiB;aAC3B,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,6BAAa,CAAC,iBAAiB,EAAE,CAAC;YAEnD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,gBAAgB;gBACzB,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,0BAA0B,CAAC,QAAiC;QACxE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,6BAAa,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAEnE,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;oBAC9B,OAAO,EAAE,uBAAuB;oBAChC,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;gBAEH,mBAAmB;gBACnB,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;oBAC9B,OAAO,EAAE,OAAO;oBAChB,OAAO,EAAE,+EAA+E;iBACzF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,mBAAmB;YACnB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAClD,aAAa,EAAE,KAAK;gBACpB,SAAS,EAAE,YAAY;gBACvB,OAAO,EAAE;oBACP,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,OAAO;YACT,CAAC;YAED,aAAa;YACb,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,6BAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAEzD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,aAAa;gBACtB,QAAQ;gBACR,KAAK,EAAE,QAAQ,CAAC,MAAM;aACvB,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACnD,qGAAqG,EACrG,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,KAAK,EACL,IAAI,CACL,CAAC;YAEF,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YAED,uBAAuB;YACvB,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YAE3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAC9B;gBACE,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,0BAA0B;gBACjC,WAAW,EAAE,IAAI;aAClB,EACD,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACxB,kBAAkB;gBAClB,MAAM,UAAU,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;gBAClD,MAAM,QAAQ,GAAG,MAAM,6BAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAEhE,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;oBAC9B,OAAO,EAAE,kBAAkB;oBAC3B,UAAU;oBACV,KAAK,EAAE,QAAQ,CAAC,MAAM;iBACvB,CAAC,CAAC;gBAEH,mBAAmB;gBACnB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjC,CAAC,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC9B,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,iBAAiB,CAAC,OAAuB;QACrD,oBAAoB;QACpB,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CACpC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,EAAE,eAAe,CAAC,CACzE,CAAC;QAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CACnC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAC1E,CAAC;QAEF,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CACtC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,EAAE,aAAa,CAAC,CACvE,CAAC;QAEF,oDAAoD;QACpD,MAAM,KAAK,GAAG,IAAA,gBAAQ,GAAE,CAAC;QAEzB,OAAO;;;;;gGAKqF,OAAO,CAAC,SAAS,uCAAuC,KAAK;0BACnI,QAAQ;0BACR,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6BA2CR,KAAK,UAAU,SAAS;;gBAErC,CAAC;IACf,CAAC;;AApZH,gCAqZC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from '../../logger';\nimport { getNonce } from '../../utils';\nimport { memoryManager } from '../../memory/memoryManager';\nimport { MemoryEntry, MemorySearchOptions, MemorySettings, MemorySource, MemoryType } from '../../memory/types';\n\n/**\n * Memory View\n * Provides a UI for viewing and managing memories\n */\nexport class MemoryView {\n  public static readonly viewType = 'codessa.memoryView';\n  private panel: vscode.WebviewPanel | undefined;\n  private context: vscode.ExtensionContext;\n  private disposables: vscode.Disposable[] = [];\n\n  constructor(context: vscode.ExtensionContext) {\n    this.context = context;\n  }\n\n  /**\n     * Show the memory view\n     */\n  public async show() {\n    if (this.panel) {\n      this.panel.reveal();\n      return;\n    }\n\n    // Create panel\n    this.panel = vscode.window.createWebviewPanel(\n      MemoryView.viewType,\n      'Codessa Memory',\n      vscode.ViewColumn.Beside,\n      {\n        enableScripts: true,\n        retainContextWhenHidden: true,\n        localResourceRoots: [\n          vscode.Uri.joinPath(this.context.extensionUri, 'media')\n        ]\n      }\n    );\n\n    // Set HTML content\n    this.panel.webview.html = await this.getHtmlForWebview(this.panel.webview);\n\n    // Handle messages from the webview\n    this.panel.webview.onDidReceiveMessage(\n      async (message) => {\n        try {\n          switch (message.command) {\n          case 'getMemories':\n            await this.handleGetMemories();\n            break;\n          case 'searchMemories':\n            await this.handleSearchMemories(message.query, message.options);\n            break;\n          case 'deleteMemory':\n            await this.handleDeleteMemory(message.id);\n            break;\n          case 'clearMemories':\n            await this.handleClearMemories();\n            break;\n          case 'getMemorySettings':\n            await this.handleGetMemorySettings();\n            break;\n          case 'updateMemorySettings':\n            await this.handleUpdateMemorySettings(message.settings);\n            break;\n          case 'chunkFile':\n            await this.handleChunkFile();\n            break;\n          case 'chunkWorkspace':\n            await this.handleChunkWorkspace();\n            break;\n          }\n        } catch (error) {\n          Logger.instance.error('Error handling memory view message:', error);\n          this.panel?.webview.postMessage({\n            command: 'error',\n            message: error instanceof Error ? error.message : String(error)\n          });\n        }\n      },\n      null,\n      this.disposables\n    );\n\n    // Handle panel disposal\n    this.panel.onDidDispose(\n      () => {\n        this.panel = undefined;\n\n        // Dispose of all disposables\n        while (this.disposables.length) {\n          const disposable = this.disposables.pop();\n          if (disposable) {\n            disposable.dispose();\n          }\n        }\n      },\n      null,\n      this.disposables\n    );\n\n    // Register memory change listener\n    memoryManager.onMemoriesChanged(() => {\n      this.handleGetMemories();\n    });\n  }\n\n  /**\n     * Handle get memories request\n     */\n  private async handleGetMemories() {\n    try {\n      const memories = await memoryManager.getMemories();\n\n      this.panel?.webview.postMessage({\n        command: 'memories',\n        memories\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting memories:', error);\n      this.panel?.webview.postMessage({\n        command: 'error',\n        message: error instanceof Error ? error.message : String(error)\n      });\n    }\n  }\n\n  /**\n     * Handle search memories request\n     */\n  private async handleSearchMemories(query: string, options: Partial<MemorySearchOptions> = {}) {\n    try {\n      const memories = await memoryManager.searchSimilarMemories(query, options);\n\n      this.panel?.webview.postMessage({\n        command: 'searchResults',\n        memories\n      });\n    } catch (error) {\n      Logger.instance.error('Error searching memories:', error);\n      this.panel?.webview.postMessage({\n        command: 'error',\n        message: error instanceof Error ? error.message : String(error)\n      });\n    }\n  }\n\n  /**\n     * Handle delete memory request\n     */\n  private async handleDeleteMemory(id: string) {\n    try {\n      const success = await memoryManager.deleteMemory(id);\n\n      this.panel?.webview.postMessage({\n        command: 'memoryDeleted',\n        id,\n        success\n      });\n\n      // Refresh memories\n      await this.handleGetMemories();\n    } catch (error) {\n      Logger.instance.error(`Error deleting memory ${id}:`, error);\n      this.panel?.webview.postMessage({\n        command: 'error',\n        message: error instanceof Error ? error.message : String(error)\n      });\n    }\n  }\n\n  /**\n     * Handle clear memories request\n     */\n  private async handleClearMemories() {\n    try {\n      await memoryManager.clearMemories();\n\n      this.panel?.webview.postMessage({\n        command: 'memoriesCleared'\n      });\n\n      // Refresh memories\n      await this.handleGetMemories();\n    } catch (error) {\n      Logger.instance.error('Error clearing memories:', error);\n      this.panel?.webview.postMessage({\n        command: 'error',\n        message: error instanceof Error ? error.message : String(error)\n      });\n    }\n  }\n\n  /**\n     * Handle get memory settings request\n     */\n  private async handleGetMemorySettings() {\n    try {\n      const settings = memoryManager.getMemorySettings();\n\n      this.panel?.webview.postMessage({\n        command: 'memorySettings',\n        settings\n      });\n    } catch (error) {\n      Logger.instance.error('Error getting memory settings:', error);\n      this.panel?.webview.postMessage({\n        command: 'error',\n        message: error instanceof Error ? error.message : String(error)\n      });\n    }\n  }\n\n  /**\n     * Handle update memory settings request\n     */\n  private async handleUpdateMemorySettings(settings: Partial<MemorySettings>) {\n    try {\n      const success = await memoryManager.updateMemorySettings(settings);\n\n      if (success) {\n        this.panel?.webview.postMessage({\n          command: 'memorySettingsUpdated',\n          success: true\n        });\n\n        // Refresh settings\n        await this.handleGetMemorySettings();\n      } else {\n        this.panel?.webview.postMessage({\n          command: 'error',\n          message: 'Failed to update memory settings. Please check the logs for more information.'\n        });\n      }\n    } catch (error) {\n      Logger.instance.error('Error updating memory settings:', error);\n      this.panel?.webview.postMessage({\n        command: 'error',\n        message: error instanceof Error ? error.message : String(error)\n      });\n    }\n  }\n\n  /**\n     * Handle chunk file request\n     */\n  private async handleChunkFile() {\n    try {\n      // Show file picker\n      const fileUris = await vscode.window.showOpenDialog({\n        canSelectMany: false,\n        openLabel: 'Chunk File',\n        filters: {\n          'All Files': ['*']\n        }\n      });\n\n      if (!fileUris || fileUris.length === 0) {\n        return;\n      }\n\n      // Chunk file\n      const filePath = fileUris[0].fsPath;\n      const memories = await memoryManager.chunkFile(filePath);\n\n      this.panel?.webview.postMessage({\n        command: 'fileChunked',\n        filePath,\n        count: memories.length\n      });\n\n      // Refresh memories\n      await this.handleGetMemories();\n    } catch (error) {\n      Logger.instance.error('Error chunking file:', error);\n      this.panel?.webview.postMessage({\n        command: 'error',\n        message: error instanceof Error ? error.message : String(error)\n      });\n    }\n  }\n\n  /**\n     * Handle chunk workspace request\n     */\n  private async handleChunkWorkspace() {\n    try {\n      // Show confirmation dialog\n      const result = await vscode.window.showWarningMessage(\n        'This will chunk all files in the workspace and add them to memory. This may take a while. Continue?',\n        { modal: true },\n        'Yes',\n        'No'\n      );\n\n      if (result !== 'Yes') {\n        return;\n      }\n\n      // Get workspace folder\n      const workspaceFolders = vscode.workspace.workspaceFolders;\n\n      if (!workspaceFolders || workspaceFolders.length === 0) {\n        throw new Error('No workspace folder open');\n      }\n\n      // Show progress\n      await vscode.window.withProgress(\n        {\n          location: vscode.ProgressLocation.Notification,\n          title: 'Chunking workspace files',\n          cancellable: true\n        },\n        async (progress, token) => {\n          // Chunk workspace\n          const folderPath = workspaceFolders[0].uri.fsPath;\n          const memories = await memoryManager.chunkWorkspace(folderPath);\n\n          this.panel?.webview.postMessage({\n            command: 'workspaceChunked',\n            folderPath,\n            count: memories.length\n          });\n\n          // Refresh memories\n          await this.handleGetMemories();\n        }\n      );\n    } catch (error) {\n      Logger.instance.error('Error chunking workspace:', error);\n      this.panel?.webview.postMessage({\n        command: 'error',\n        message: error instanceof Error ? error.message : String(error)\n      });\n    }\n  }\n\n  /**\n     * Get HTML for webview\n     */\n  private async getHtmlForWebview(webview: vscode.Webview): Promise<string> {\n    // Get resource URIs\n    const scriptUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this.context.extensionUri, 'media', 'memoryView.js')\n    );\n\n    const styleUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this.context.extensionUri, 'media', 'memoryView.css')\n    );\n\n    const codiconsUri = webview.asWebviewUri(\n      vscode.Uri.joinPath(this.context.extensionUri, 'media', 'codicon.css')\n    );\n\n    // Use a nonce to whitelist which scripts can be run\n    const nonce = getNonce();\n\n    return `<!DOCTYPE html>\n        <html lang=\"en\">\n        <head>\n            <meta charset=\"UTF-8\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n            <meta http-equiv=\"Content-Security-Policy\" content=\"default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';\">\n            <link href=\"${styleUri}\" rel=\"stylesheet\" />\n            <link href=\"${codiconsUri}\" rel=\"stylesheet\" />\n            <title>Codessa Memory</title>\n        </head>\n        <body>\n            <div class=\"container\">\n                <div class=\"header\">\n                    <h1>Codessa Memory</h1>\n                    <div class=\"actions\">\n                        <button id=\"refreshBtn\" title=\"Refresh Memories\"><i class=\"codicon codicon-refresh\"></i></button>\n                        <button id=\"settingsBtn\" title=\"Memory Settings\"><i class=\"codicon codicon-gear\"></i></button>\n                        <button id=\"chunkFileBtn\" title=\"Chunk File\"><i class=\"codicon codicon-file-add\"></i></button>\n                        <button id=\"chunkWorkspaceBtn\" title=\"Chunk Workspace\"><i class=\"codicon codicon-folder-add\"></i></button>\n                        <button id=\"clearBtn\" title=\"Clear All Memories\"><i class=\"codicon codicon-trash\"></i></button>\n                    </div>\n                </div>\n\n                <div class=\"search-container\">\n                    <input type=\"text\" id=\"searchInput\" placeholder=\"Search memories...\" />\n                    <button id=\"searchBtn\"><i class=\"codicon codicon-search\"></i></button>\n                </div>\n\n                <div class=\"content\">\n                    <div id=\"memoriesList\" class=\"memories-list\"></div>\n                    <div id=\"memoryDetail\" class=\"memory-detail\"></div>\n                </div>\n\n                <div id=\"settingsModal\" class=\"modal\">\n                    <div class=\"modal-content\">\n                        <div class=\"modal-header\">\n                            <h2>Memory Settings</h2>\n                            <span class=\"close\">&times;</span>\n                        </div>\n                        <div class=\"modal-body\">\n                            <div id=\"settingsForm\"></div>\n                        </div>\n                        <div class=\"modal-footer\">\n                            <button id=\"saveSettingsBtn\">Save</button>\n                            <button id=\"cancelSettingsBtn\">Cancel</button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <script nonce=\"${nonce}\" src=\"${scriptUri}\"></script>\n        </body>\n        </html>`;\n  }\n}\n"]}