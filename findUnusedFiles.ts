import * as ts from 'typescript';
import * as fs from 'fs';
import * as path from 'path';

// Configuration
const SRC_DIR = path.join(__dirname, 'src');
const TSCONFIG_PATH = path.join(__dirname, 'tsconfig.json');

// Get TypeScript program
function getProgram() {
  const configFile = ts.readConfigFile(TSCONFIG_PATH, ts.sys.readFile);
  const compilerOptions = ts.parseJsonConfigFileContent(
    configFile.config,
    ts.sys,
    path.dirname(TSCONFIG_PATH)
  ).options;

  const program = ts.createProgram({
    rootNames: [path.join(SRC_DIR, '**/*.ts')],
    options: {
      ...compilerOptions,
      noEmit: true,
      skipLibCheck: true,
    },
  });

  return program;
}

// Get all source files from the program
function getSourceFiles(program: ts.Program) {
  return program.getSourceFiles()
    .filter(file => !file.fileName.includes('node_modules'));
}

// Find all imports in a source file
function getImportsInFile(sourceFile: ts.SourceFile) {
  const imports: string[] = [];
  
  const visit = (node: ts.Node) => {
    if (ts.isImportDeclaration(node)) {
      const moduleSpecifier = node.moduleSpecifier;
      if (ts.isStringLiteral(moduleSpecifier)) {
        imports.push(moduleSpecifier.text);
      }
    } else if (ts.isCallExpression(node) && 
               node.expression.getText() === 'require' && 
               node.arguments.length > 0 && 
               ts.isStringLiteral(node.arguments[0])) {
      imports.push(node.arguments[0].text);
    }
    
    ts.forEachChild(node, visit);
  };
  
  visit(sourceFile);
  return imports;
}

// Main function
async function findUnusedFiles() {
  console.log('Analyzing codebase for unused files...\n');
  
  const program = getProgram();
  const sourceFiles = getSourceFiles(program);
  
  // Map of file paths to their importers
  const fileImports = new Map<string, Set<string>>();
  
  // First pass: collect all imports
  sourceFiles.forEach(sourceFile => {
    const filePath = path.normalize(sourceFile.fileName);
    const imports = getImportsInFile(sourceFile);
    
    imports.forEach(importPath => {
      if (!importPath.startsWith('.')) return; // Skip node_modules imports
      
      // Resolve the import to a file path
      const resolvedPath = resolveImportPath(importPath, path.dirname(filePath));
      if (!resolvedPath) return;
      
      // Add the importing file to the set of importers for the imported file
      if (!fileImports.has(resolvedPath)) {
        fileImports.set(resolvedPath, new Set());
      }
      fileImports.get(resolvedPath)!.add(filePath);
    });
  });
  
  // Find entry points (files that are not imported by any other files)
  const allFiles = new Set(sourceFiles.map(f => path.normalize(f.fileName)));
  const importedFiles = new Set(fileImports.keys());
  
  // Files that are not imported by any other files are potential entry points
  const entryPoints = new Set([...allFiles].filter(f => !importedFiles.has(f)));
  
  // Mark all files reachable from entry points as used
  const usedFiles = new Set<string>();
  const visit = (filePath: string) => {
    if (usedFiles.has(filePath)) return;
    usedFiles.add(filePath);
    
    // Recursively visit all imported files
    const imported = fileImports.get(filePath) || [];
    imported.forEach(importedPath => visit(importedPath));
  };
  
  entryPoints.forEach(entryPoint => visit(entryPoint));
  
  // Find unused files (files not reachable from any entry point)
  const unusedFiles = [...allFiles].filter(f => !usedFiles.has(f));
  
  // Output results
  console.log(`Found ${unusedFiles.length} potentially unused files (not imported by any entry point):\n`);
  unusedFiles.forEach(file => {
    console.log(`- ${file}`);
  });
  
  // Save results to files
  fs.writeFileSync(
    'unused-files.json',
    JSON.stringify(unusedFiles, null, 2)
  );
  
  const usageStats = Array.from(fileImports.entries())
    .map(([file, importers]) => ({
      file,
      importers: Array.from(importers),
      importCount: importers.size
    }))
    .sort((a, b) => b.importCount - a.importCount);
    
  fs.writeFileSync(
    'file-usage-stats.json',
    JSON.stringify(usageStats, null, 2)
  );
  
  console.log('\nAnalysis complete. Results saved to unused-files.json and file-usage-stats.json');
  
  return {
    unusedFiles,
    usageStats
  };
}

// Resolve import path to actual file
function resolveImportPath(importPath: string, baseDir: string): string | null {
  const extensions = ['.ts', '.tsx', '.js', '.jsx', ''];
  const basePath = path.resolve(baseDir, importPath);
  
  // Check for exact file match
  for (const ext of extensions) {
    const fullPath = basePath + ext;
    if (fs.existsSync(fullPath)) {
      return path.normalize(fullPath);
    }
  }
  
  // Check for directory with index file
  for (const ext of extensions) {
    const fullPath = path.join(basePath, 'index' + ext);
    if (fs.existsSync(fullPath)) {
      return path.normalize(fullPath);
    }
  }
  
  return null;
}

// Run the analysis
findUnusedFiles().catch(console.error);
