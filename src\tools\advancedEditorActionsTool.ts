import * as vscode from 'vscode';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../types/agent';
import { z } from 'zod';

export class MultiCursorTool implements ITool {
  readonly id = 'multiCursor';
  readonly name = 'Multi-Cursor';
  readonly description = 'Add, remove, or move multiple cursors.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    positions: z.array(z.object({
      line: z.number(),
      column: z.number()
    })).describe('Array of positions for cursors.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      positions: { type: 'array', items: { type: 'object', properties: { line: { type: 'number' }, column: { type: 'number' } } }, description: 'Array of positions for cursors.' }
    },
    required: ['positions']
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const positions = input.positions as { line: number; column: number }[];
    const editor = vscode.window.activeTextEditor;
    if (!editor) return { success: false, error: 'No active editor.', toolId: this.id, actionName };
    if (!positions || positions.length === 0) return { success: false, error: 'No positions provided.', toolId: this.id, actionName };
    editor.selections = positions.map(pos => new vscode.Selection(pos.line, pos.column, pos.line, pos.column));
    return { success: true, output: `Set ${positions.length} cursors.`, toolId: this.id, actionName };
  }
}

export class ClipboardTool implements ITool {
  readonly id = 'clipboard';
  readonly name = 'Clipboard';
  readonly description = 'Cut, copy, or paste text.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    action: z.enum(['cut', 'copy', 'paste']).describe('Clipboard action.'),
    text: z.string().optional().describe('Text to copy or paste.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      action: { type: 'string', enum: ['cut', 'copy', 'paste'], description: 'Clipboard action.' },
      text: { type: 'string', description: 'Text to copy or paste.' }
    },
    required: ['action']
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const action = input.action as string;
    const text = input.text as string;
    const editor = vscode.window.activeTextEditor;
    if (!editor) return { success: false, error: 'No active editor.', toolId: this.id, actionName };
    if (action === 'copy') {
      const selected = editor.document.getText(editor.selection);
      await vscode.env.clipboard.writeText(selected);
      return { success: true, output: 'Copied to clipboard.', toolId: this.id, actionName };
    } else if (action === 'cut') {
      const selected = editor.document.getText(editor.selection);
      await vscode.env.clipboard.writeText(selected);
      await editor.edit(editBuilder => editBuilder.delete(editor.selection));
      return { success: true, output: 'Cut to clipboard.', toolId: this.id, actionName };
    } else if (action === 'paste') {
      const pasteText = text || await vscode.env.clipboard.readText();
      await editor.edit(editBuilder => editBuilder.insert(editor.selection.active, pasteText));
      return { success: true, output: 'Pasted from clipboard.', toolId: this.id, actionName };
    } else {
      return { success: false, error: `Unknown clipboard action: ${action}`, toolId: this.id, actionName };
    }
  }
}

export class BatchEditTool implements ITool {
  readonly id = 'batchEdit';
  readonly name = 'Batch Edit';
  readonly description = 'Perform multiple edits across files/locations.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    edits: z.array(z.object({
      filePath: z.string(),
      range: z.object({
        start: z.object({
          line: z.number(),
          column: z.number()
        }),
        end: z.object({
          line: z.number(),
          column: z.number()
        })
      }),
      text: z.string()
    })).describe('Array of edits.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      edits: {
        type: 'array', items: { type: 'object', properties: { filePath: { type: 'string' }, range: { type: 'object', properties: { start: { type: 'object', properties: { line: { type: 'number' }, column: { type: 'number' } } }, end: { type: 'object', properties: { line: { type: 'number' }, column: { type: 'number' } } } }, text: { type: 'string' } } }, description: 'Array of edits.' }
      },
    },
    required: ['edits']
  };

  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const edits = input.edits as { filePath: string; range: { start: { line: number; column: number }, end: { line: number; column: number } }, text: string }[];
    if (!edits || edits.length === 0) return { success: false, error: 'No edits provided.', toolId: this.id, actionName };
    for (const edit of edits) {
      const doc = await vscode.workspace.openTextDocument(edit.filePath);
      const editor = await vscode.window.showTextDocument(doc);
      await editor.edit(editBuilder => {
        const start = new vscode.Position(edit.range.start.line, edit.range.start.column);
        const end = new vscode.Position(edit.range.end.line, edit.range.end.column);
        editBuilder.replace(new vscode.Range(start, end), edit.text);
      });
    }
    return { success: true, output: `Applied ${edits.length} edits.`, toolId: this.id, actionName };
  }
}

export class FindReplaceTool implements ITool {
  readonly id = 'findReplace';
  readonly name = 'Find and Replace';
  readonly description = 'Find and replace text in a file or across files.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    filePath: z.string().describe('File to search/replace.'),
    find: z.string().describe('Text or regex to find.'),
    replace: z.string().describe('Replacement text.'),
    isRegex: z.boolean().optional().describe('Use regex?')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      filePath: { type: 'string', description: 'File to search/replace.' },
      find: { type: 'string', description: 'Text or regex to find.' },
      replace: { type: 'string', description: 'Replacement text.' },
      isRegex: { type: 'boolean', description: 'Use regex?' }
    },
    required: ['filePath', 'find', 'replace']
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const { filePath, find, replace, isRegex } = input;
    if (!filePath || !find || replace === undefined) return { success: false, error: 'Missing required fields.', toolId: this.id, actionName };
    const doc = await vscode.workspace.openTextDocument(filePath);
    const editor = await vscode.window.showTextDocument(doc);
    const text = doc.getText();
    let newText: string;
    if (isRegex) {
      newText = text.replace(new RegExp(find, 'g'), replace);
    } else {
      newText = text.split(find).join(replace);
    }
    const start = new vscode.Position(0, 0);
    const end = new vscode.Position(doc.lineCount - 1, doc.lineAt(doc.lineCount - 1).text.length);
    await editor.edit(editBuilder => {
      editBuilder.replace(new vscode.Range(start, end), newText);
    });
    return { success: true, output: `Replaced '${find}' with '${replace}' in ${filePath}.`, toolId: this.id, actionName };
  }
}
